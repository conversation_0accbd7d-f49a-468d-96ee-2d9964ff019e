# Wind Noise PCM Data

这个目录包含从 `wind_noise_voice` 目录转换而来的PCM格式音频文件。

## 转换信息

- **源目录**: `wind_noise_voice/` (WAV格式)
- **目标目录**: `wind_PCM_data/` (PCM格式)
- **转换时间**: 2025年7月31日
- **转换工具**: `convert_wind_data.py`

## 文件统计

- **总文件数**: 444个PCM文件
- **转换成功率**: 100.0%
- **总大小**: 299.81 MB
- **平均文件大小**: 691.4 KB

## 文件格式规格

- **格式**: 16位PCM (s16le)
- **采样率**: 48,000 Hz
- **声道数**: 1 (单声道)
- **字节序**: 小端序 (Little Endian)

## 文件名模式

| 模式 | 数量 | 百分比 | 说明 |
|------|------|--------|------|
| XXX_XXX (数字编号) | 378 | 85.1% | 如 000_001.pcm, 015_042.pcm |
| const_* | 30 | 6.8% | 如 const_01.pcm, const_30.pcm |
| gusts_* | 30 | 6.8% | 如 gusts_01.pcm, gusts_30.pcm |
| mic* | 4 | 0.9% | 如 mic1_2cm.pcm, mic2_10cm.pcm |
| wind_* | 2 | 0.5% | 如 wind_normal.pcm, wind_strong.pcm |

## 使用方法

这些PCM文件可以直接用于RNNoise训练：

1. **特征提取**: 使用 `prepare_pcm_data.py` 提取训练特征
2. **模型训练**: 配合其他PCM数据进行RNNoise模型训练
3. **音频处理**: 作为风噪声数据集用于降噪算法开发

## 质量验证

✅ **转换完整性**: 所有444个WAV文件都成功转换为PCM  
✅ **文件对应性**: 每个PCM文件都有对应的原始WAV文件  
✅ **格式一致性**: 所有文件都使用相同的PCM格式参数  

## 注意事项

- PCM文件大小不完全一致（有4种不同大小），这是正常的，因为原始WAV文件长度不同
- 最小文件: 281.2 KB，最大文件: 10.6 MB
- 建议保留原始WAV文件作为备份

## 相关文件

- `../convert_wind_data.py` - 转换脚本
- `../wind_conversion_summary.py` - 转换结果分析脚本
- `../wind_noise_voice/` - 原始WAV文件目录
