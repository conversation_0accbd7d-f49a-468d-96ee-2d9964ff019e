cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file D<PERSON><PERSON>AIMER within this package.")
cpp_quote(" */")
cpp_quote("")
cpp_quote("#include <winapifamily.h>")

#ifndef DO_NO_IMPORTS
import "objidl.idl";
#endif

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

interface ICreateTypeInfo;
interface ICreateTypeInfo2;
interface ICreateTypeLib;
interface ICreateTypeLib2;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
interface IDispatch;
interface IEnumVARIANT;
interface ITypeComp;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
interface ITypeInfo;
interface ITypeInfo2;
interface ITypeLib;
interface ITypeLib2;
interface ITypeChangeEvents;
interface IErrorInfo;
interface ICreateErrorInfo;
interface ISupportErrorInfo;
interface ITypeFactory;
interface ITypeMarshal;
interface IRecordInfo;
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[version (1.0), pointer_default (unique)]
interface IOleAutomationTypes {
  typedef CY CURRENCY;
  typedef struct tagSAFEARRAYBOUND {
    ULONG cElements;
    LONG lLbound;
  } SAFEARRAYBOUND,*LPSAFEARRAYBOUND;

cpp_quote("")
  typedef [unique] struct _wireVARIANT *wireVARIANT;
  typedef [unique] struct _wireBRECORD *wireBRECORD;
cpp_quote("")
  typedef struct _wireSAFEARR_BSTR {
    ULONG Size;
    [size_is (Size), ref] wireBSTR *aBstr;
  } SAFEARR_BSTR;

cpp_quote("")
  typedef struct _wireSAFEARR_UNKNOWN {
    ULONG Size;
    [size_is (Size), ref] IUnknown **apUnknown;
  } SAFEARR_UNKNOWN;

cpp_quote("")
  typedef struct _wireSAFEARR_DISPATCH {
    ULONG Size;
    [size_is (Size), ref] IDispatch **apDispatch;
  } SAFEARR_DISPATCH;

cpp_quote("")
  typedef struct _wireSAFEARR_VARIANT {
    ULONG Size;
    [size_is (Size), ref] wireVARIANT *aVariant;
  } SAFEARR_VARIANT;

cpp_quote("")
  typedef struct _wireSAFEARR_BRECORD {
    ULONG Size;
    [size_is (Size), ref] wireBRECORD *aRecord;
  } SAFEARR_BRECORD;

cpp_quote("")
  typedef struct _wireSAFEARR_HAVEIID {
    ULONG Size;
    [size_is (Size), ref] IUnknown **apUnknown;
    IID iid;
  } SAFEARR_HAVEIID;

cpp_quote("")
  typedef [v1_enum] enum tagSF_TYPE {
    SF_ERROR = VT_ERROR,
    SF_I1 = VT_I1,
    SF_I2 = VT_I2,
    SF_I4 = VT_I4,
    SF_I8 = VT_I8,
    SF_BSTR = VT_BSTR,
    SF_UNKNOWN = VT_UNKNOWN,
    SF_DISPATCH = VT_DISPATCH,
    SF_VARIANT = VT_VARIANT,
    SF_RECORD = VT_RECORD,
    SF_HAVEIID = VT_UNKNOWN | VT_RESERVED
  } SF_TYPE;

cpp_quote("")
  typedef union _wireSAFEARRAY_UNION switch (ULONG sfType) u {
    case SF_BSTR: SAFEARR_BSTR BstrStr;
    case SF_UNKNOWN: SAFEARR_UNKNOWN UnknownStr;
    case SF_DISPATCH: SAFEARR_DISPATCH DispatchStr;
    case SF_VARIANT: SAFEARR_VARIANT VariantStr;
    case SF_RECORD: SAFEARR_BRECORD RecordStr;
    case SF_HAVEIID: SAFEARR_HAVEIID HaveIidStr;
    case SF_I1: BYTE_SIZEDARR ByteStr;
    case SF_I2: WORD_SIZEDARR WordStr;
    case SF_I4: DWORD_SIZEDARR LongStr;
    case SF_I8: HYPER_SIZEDARR HyperStr;
  } SAFEARRAYUNION;

cpp_quote("")
  typedef [unique] struct _wireSAFEARRAY {
    USHORT cDims;
    USHORT fFeatures;
    ULONG cbElements;
    ULONG cLocks;
    SAFEARRAYUNION uArrayStructs;
    [size_is (cDims)] SAFEARRAYBOUND rgsabound[];
  } *wireSAFEARRAY;
  typedef [unique] wireSAFEARRAY *wirePSAFEARRAY;

cpp_quote("")
  typedef struct tagSAFEARRAY {
    USHORT cDims;
    USHORT fFeatures;
    ULONG cbElements;
    ULONG cLocks;
    PVOID pvData;
    SAFEARRAYBOUND rgsabound[];
  } SAFEARRAY;
  typedef [wire_marshal (wirePSAFEARRAY)] SAFEARRAY *LPSAFEARRAY;

cpp_quote("")
  const USHORT FADF_AUTO = 0x0001;
  const USHORT FADF_STATIC = 0x0002;
  const USHORT FADF_EMBEDDED = 0x0004;
  const USHORT FADF_FIXEDSIZE = 0x0010;
  const USHORT FADF_RECORD = 0x0020;
  const USHORT FADF_HAVEIID = 0x0040;
  const USHORT FADF_HAVEVARTYPE= 0x0080;
  const USHORT FADF_BSTR = 0x0100;
  const USHORT FADF_UNKNOWN = 0x0200;
  const USHORT FADF_DISPATCH = 0x0400;
  const USHORT FADF_VARIANT = 0x0800;
  const USHORT FADF_RESERVED = 0xf008;

cpp_quote("")
cpp_quote("/* Kludge for 3.0 release to disable new default-behavior.")
cpp_quote(" * For now we define _FORCENAMELESSUNION.  For 4.0 this define")
cpp_quote(" * will be removed. */")
cpp_quote("#ifndef _FORCENAMELESSUNION")
cpp_quote("#define _FORCENAMELESSUNION 1")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if (__STDC__ && !defined(__cplusplus) && !defined(_FORCENAMELESSUNION)) || defined(NONAMELESSUNION) || (defined (_MSC_VER) && !defined(_MSC_EXTENSIONS) && !defined(_FORCENAMELESSUNION))")
cpp_quote("#define __VARIANT_NAME_1 n1")
cpp_quote("#define __VARIANT_NAME_2 n2")
cpp_quote("#define __VARIANT_NAME_3 n3")
cpp_quote("#define __VARIANT_NAME_4 brecVal")
cpp_quote("#else")
cpp_quote("#define __tagVARIANT")
cpp_quote("#define __VARIANT_NAME_1")
cpp_quote("#define __VARIANT_NAME_2")
cpp_quote("#define __VARIANT_NAME_3")
cpp_quote("#define __tagBRECORD")
cpp_quote("#define __VARIANT_NAME_4")
cpp_quote("#endif")

cpp_quote("")
  typedef [wire_marshal (wireVARIANT)] struct tagVARIANT VARIANT;

cpp_quote("")
  struct tagVARIANT {
    union {
      struct __tagVARIANT {
	VARTYPE vt;
	WORD wReserved1;
	WORD wReserved2;
	WORD wReserved3;
	union {
	  LONGLONG llVal;
	  LONG lVal;
	  BYTE bVal;
	  SHORT iVal;
	  FLOAT fltVal;
	  DOUBLE dblVal;
	  VARIANT_BOOL boolVal;
	  /* Removed due conflict with bool and gcc's inablity to express a macro expanding to a comment.
	  _VARIANT_BOOL bool; */
	  SCODE scode;
	  CY cyVal;
	  DATE date;
	  BSTR bstrVal;
	  IUnknown *punkVal;
	  IDispatch *pdispVal;
	  SAFEARRAY *parray;
	  BYTE *pbVal;
	  SHORT *piVal;
	  LONG *plVal;
	  LONGLONG *pllVal;
	  FLOAT *pfltVal;
	  DOUBLE *pdblVal;
	  VARIANT_BOOL *pboolVal;
	  /* Removed due conflict with bool and gcc's inablity to express a macro expanding to a comment.
	  _VARIANT_BOOL *pbool; */
	  SCODE *pscode;
	  CY *pcyVal;
	  DATE *pdate;
	  BSTR *pbstrVal;
	  IUnknown **ppunkVal;
	  IDispatch **ppdispVal;
	  SAFEARRAY **pparray;
	  VARIANT *pvarVal;
	  PVOID byref;
	  CHAR cVal;
	  USHORT uiVal;
	  ULONG ulVal;
	  ULONGLONG ullVal;
	  INT intVal;
	  UINT uintVal;
	  DECIMAL *pdecVal;
	  CHAR *pcVal;
	  USHORT *puiVal;
	  ULONG *pulVal;
	  ULONGLONG *pullVal;
	  INT *pintVal;
	  UINT *puintVal;
	  struct __tagBRECORD {
	    PVOID pvRecord;
	    IRecordInfo *pRecInfo;
	  } __VARIANT_NAME_4;
	} __VARIANT_NAME_3;
      } __VARIANT_NAME_2;
      DECIMAL decVal;
    } __VARIANT_NAME_1;
  };

cpp_quote("")
  typedef VARIANT *LPVARIANT;
  typedef VARIANT VARIANTARG;
  typedef VARIANT *LPVARIANTARG;
  cpp_quote("#if 0")
  typedef const VARIANT *REFVARIANT;
  cpp_quote("#else")
  cpp_quote("")
  cpp_quote("#ifndef _REFVARIANT_DEFINED")
  cpp_quote("#define _REFVARIANT_DEFINED")
  cpp_quote("#ifdef __cplusplus")
  cpp_quote("#define REFVARIANT const VARIANT &")
  cpp_quote("#else")
  cpp_quote("#define REFVARIANT const VARIANT * __MIDL_CONST")
  cpp_quote("#endif")
  cpp_quote("#endif")
  cpp_quote("#endif")

cpp_quote("")
  struct _wireBRECORD {
    ULONG fFlags;
    ULONG clSize;
    IRecordInfo *pRecInfo;
    [size_is (clSize)] byte *pRecord;
  };

cpp_quote("")
  struct _wireVARIANT {
    DWORD clSize;
    DWORD rpcReserved;
    USHORT vt;
    USHORT wReserved1;
    USHORT wReserved2;
    USHORT wReserved3;
    [switch_type (ULONG), switch_is (vt)] union {
      [case (VT_I8)] LONGLONG llVal;
      [case (VT_I4)] LONG lVal;
      [case (VT_UI1)] BYTE bVal;
      [case (VT_I2)] SHORT iVal;
      [case (VT_R4)] FLOAT fltVal;
      [case (VT_R8)] DOUBLE dblVal;
      [case (VT_BOOL)] VARIANT_BOOL boolVal;
      [case (VT_ERROR)] SCODE scode;
      [case (VT_CY)] CY cyVal;
      [case (VT_DATE)] DATE date;
      [case (VT_BSTR)] wireBSTR bstrVal;
      [case (VT_UNKNOWN)] IUnknown *punkVal;
      [case (VT_DISPATCH)] IDispatch *pdispVal;
      [case (VT_ARRAY)] wirePSAFEARRAY parray;
      [case (VT_RECORD, VT_RECORD|VT_BYREF)]
      wireBRECORD brecVal;
      [case (VT_UI1|VT_BYREF)]
      BYTE *pbVal;
      [case (VT_I2|VT_BYREF)]
      SHORT *piVal;
      [case (VT_I4|VT_BYREF)]
      LONG *plVal;
      [case (VT_I8|VT_BYREF)]
      LONGLONG *pllVal;
      [case (VT_R4|VT_BYREF)]
      FLOAT *pfltVal;
      [case (VT_R8|VT_BYREF)]
      DOUBLE *pdblVal;
      [case (VT_BOOL|VT_BYREF)]
      VARIANT_BOOL *pboolVal;
      [case (VT_ERROR|VT_BYREF)]
      SCODE *pscode;
      [case (VT_CY|VT_BYREF)]
      CY *pcyVal;
      [case (VT_DATE|VT_BYREF)]
      DATE *pdate;
      [case (VT_BSTR|VT_BYREF)]
      wireBSTR *pbstrVal;
      [case (VT_UNKNOWN|VT_BYREF)]
      IUnknown **ppunkVal;
      [case (VT_DISPATCH|VT_BYREF)]
      IDispatch **ppdispVal;
      [case (VT_ARRAY|VT_BYREF)]
      wirePSAFEARRAY *pparray;
      [case (VT_VARIANT|VT_BYREF)]
      wireVARIANT *pvarVal;
      [case (VT_I1)] CHAR cVal;
      [case (VT_UI2)] USHORT uiVal;
      [case (VT_UI4)] ULONG ulVal;
      [case (VT_UI8)] ULONGLONG ullVal;
      [case (VT_INT)] INT intVal;
      [case (VT_UINT)] UINT uintVal;
      [case (VT_DECIMAL)] DECIMAL decVal;
      [case (VT_BYREF|VT_DECIMAL)]
      DECIMAL *pdecVal;
      [case (VT_BYREF|VT_I1)]
      CHAR *pcVal;
      [case (VT_BYREF|VT_UI2)]
      USHORT *puiVal;
      [case (VT_BYREF|VT_UI4)]
      ULONG *pulVal;
      [case (VT_BYREF|VT_UI8)]
      ULONGLONG *pullVal;
      [case (VT_BYREF|VT_INT)]
      INT *pintVal;
      [case (VT_BYREF|VT_UINT)]
      UINT *puintVal;
      [case (VT_EMPTY)];
      [case (VT_NULL)];
    };
  };

cpp_quote("")
  typedef LONG DISPID;
  typedef DISPID MEMBERID;
  typedef DWORD HREFTYPE;
  typedef [v1_enum] enum tagTYPEKIND {
    TKIND_ENUM = 0,
    TKIND_RECORD,
    TKIND_MODULE,
    TKIND_INTERFACE,
    TKIND_DISPATCH,
    TKIND_COCLASS,
    TKIND_ALIAS,
    TKIND_UNION,
    TKIND_MAX
  } TYPEKIND;

cpp_quote("")
  typedef struct tagTYPEDESC {
    [switch_type (VARTYPE), switch_is (vt)] union {
      [case (VT_PTR, VT_SAFEARRAY)] struct tagTYPEDESC *lptdesc;
      [case (VT_CARRAY)] struct tagARRAYDESC *lpadesc;
      [case (VT_USERDEFINED)] HREFTYPE hreftype;
      [default];
    };
    VARTYPE vt;
  } TYPEDESC;

cpp_quote("")
  typedef struct tagARRAYDESC {
    TYPEDESC tdescElem;
    USHORT cDims;
    [size_is (cDims)] SAFEARRAYBOUND rgbounds[];
  } ARRAYDESC;

cpp_quote("")
  typedef struct tagPARAMDESCEX {
    ULONG cBytes;
    VARIANTARG varDefaultValue;
  } PARAMDESCEX,*LPPARAMDESCEX;

cpp_quote("")
  typedef struct tagPARAMDESC {
    LPPARAMDESCEX pparamdescex;
    USHORT wParamFlags;
  } PARAMDESC,*LPPARAMDESC;

cpp_quote("")
  const USHORT PARAMFLAG_NONE = 0x00;
  const USHORT PARAMFLAG_FIN = 0x01;
  const USHORT PARAMFLAG_FOUT = 0x02;
  const USHORT PARAMFLAG_FLCID = 0x04;
  const USHORT PARAMFLAG_FRETVAL = 0x08;
  const USHORT PARAMFLAG_FOPT = 0x10;
  const USHORT PARAMFLAG_FHASDEFAULT = 0x20;
  const USHORT PARAMFLAG_FHASCUSTDATA = 0x40;

cpp_quote("")
  typedef struct tagIDLDESC {
    ULONG_PTR dwReserved;
    USHORT wIDLFlags;
  } IDLDESC,*LPIDLDESC;

cpp_quote("")
  const USHORT IDLFLAG_NONE = PARAMFLAG_NONE;
  const USHORT IDLFLAG_FIN = PARAMFLAG_FIN;
  const USHORT IDLFLAG_FOUT = PARAMFLAG_FOUT;
  const USHORT IDLFLAG_FLCID = PARAMFLAG_FLCID;
  const USHORT IDLFLAG_FRETVAL = PARAMFLAG_FRETVAL;
  cpp_quote("")
  cpp_quote("#if 0")
  typedef struct tagELEMDESC {
    TYPEDESC tdesc;
    PARAMDESC paramdesc;
  } ELEMDESC;
  cpp_quote("#else")
  cpp_quote("")
  cpp_quote("  typedef struct tagELEMDESC {")
  cpp_quote("    TYPEDESC tdesc;")
  cpp_quote("    __C89_NAMELESS union {")
  cpp_quote("      IDLDESC idldesc;")
  cpp_quote("      PARAMDESC paramdesc;")
  cpp_quote("    } DUMMYUNIONNAME;")
  cpp_quote("  } ELEMDESC,*LPELEMDESC;")
  cpp_quote("#endif")

cpp_quote("")
  typedef struct tagTYPEATTR {
    GUID guid;
    LCID lcid;
    DWORD dwReserved;
    MEMBERID memidConstructor;
    MEMBERID memidDestructor;
    LPOLESTR lpstrSchema;
    ULONG cbSizeInstance;
    TYPEKIND typekind;
    WORD cFuncs;
    WORD cVars;
    WORD cImplTypes;
    WORD cbSizeVft;
    WORD cbAlignment;
    WORD wTypeFlags;
    WORD wMajorVerNum;
    WORD wMinorVerNum;
    TYPEDESC tdescAlias;
    IDLDESC idldescType;
  } TYPEATTR,*LPTYPEATTR;

cpp_quote("")
  typedef struct tagDISPPARAMS {
    [size_is (cArgs)] VARIANTARG *rgvarg;
    [size_is (cNamedArgs)] DISPID *rgdispidNamedArgs;
    UINT cArgs;
    UINT cNamedArgs;
  } DISPPARAMS;
  cpp_quote("")
  cpp_quote("#if 0")
  typedef struct tagEXCEPINFO {
    WORD wCode;
    WORD wReserved;
    BSTR bstrSource;
    BSTR bstrDescription;
    BSTR bstrHelpFile;
    DWORD dwHelpContext;
    ULONG_PTR pvReserved;
    ULONG_PTR pfnDeferredFillIn;
    SCODE scode;
  } EXCEPINFO;
  cpp_quote("#else")
  cpp_quote("  typedef struct tagEXCEPINFO {")
  cpp_quote("    WORD wCode;")
  cpp_quote("    WORD wReserved;")
  cpp_quote("    BSTR bstrSource;")
  cpp_quote("    BSTR bstrDescription;")
  cpp_quote("    BSTR bstrHelpFile;")
  cpp_quote("    DWORD dwHelpContext;")
  cpp_quote("    PVOID pvReserved;")
  cpp_quote("    HRESULT (__stdcall *pfnDeferredFillIn)(struct tagEXCEPINFO *);")
  cpp_quote("    SCODE scode;")
  cpp_quote("  } EXCEPINFO, *LPEXCEPINFO;")
  cpp_quote("#endif")

cpp_quote("")
  typedef [v1_enum] enum tagCALLCONV {
    CC_FASTCALL = 0,
    CC_CDECL = 1,
    CC_MSCPASCAL,
    CC_PASCAL = CC_MSCPASCAL,
    CC_MACPASCAL,
    CC_STDCALL,
    CC_FPFASTCALL,
    CC_SYSCALL,
    CC_MPWCDECL,
    CC_MPWPASCAL,
    CC_MAX
  } CALLCONV;

cpp_quote("")
  typedef [v1_enum] enum tagFUNCKIND {
    FUNC_VIRTUAL,
    FUNC_PUREVIRTUAL,
    FUNC_NONVIRTUAL,
    FUNC_STATIC,
    FUNC_DISPATCH
  } FUNCKIND;

cpp_quote("")
  typedef [v1_enum] enum tagINVOKEKIND {
    INVOKE_FUNC = 1,
    INVOKE_PROPERTYGET = 2,
    INVOKE_PROPERTYPUT = 4,
    INVOKE_PROPERTYPUTREF = 8
  } INVOKEKIND;

cpp_quote("")
  typedef struct tagFUNCDESC {
    MEMBERID memid;
    [size_is (cScodes)] SCODE *lprgscode;
    [size_is (cParams)] ELEMDESC *lprgelemdescParam;
    FUNCKIND funckind;
    INVOKEKIND invkind;
    CALLCONV callconv;
    SHORT cParams;
    SHORT cParamsOpt;
    SHORT oVft;
    SHORT cScodes;
    ELEMDESC elemdescFunc;
    WORD wFuncFlags;
  } FUNCDESC,*LPFUNCDESC;

cpp_quote("")
  typedef [v1_enum] enum tagVARKIND {
    VAR_PERINSTANCE,
    VAR_STATIC,
    VAR_CONST,
    VAR_DISPATCH
  } VARKIND;

cpp_quote("")
  const USHORT IMPLTYPEFLAG_FDEFAULT = 0x1;
  const USHORT IMPLTYPEFLAG_FSOURCE = 0x2;
  const USHORT IMPLTYPEFLAG_FRESTRICTED = 0x4;
  const USHORT IMPLTYPEFLAG_FDEFAULTVTABLE= 0x8;

cpp_quote("")
  typedef struct tagVARDESC {
    MEMBERID memid;
    LPOLESTR lpstrSchema;
    [switch_type (VARKIND), switch_is (varkind)] union {
      [case (VAR_PERINSTANCE, VAR_DISPATCH, VAR_STATIC)] ULONG oInst;
      [case (VAR_CONST)] VARIANT *lpvarValue;
    };
    ELEMDESC elemdescVar;
    WORD wVarFlags;
    VARKIND varkind;
  } VARDESC,*LPVARDESC;

cpp_quote("")
 typedef enum tagTYPEFLAGS {
    TYPEFLAG_FAPPOBJECT = 0x01,
    TYPEFLAG_FCANCREATE = 0x02,
    TYPEFLAG_FLICENSED = 0x04,
    TYPEFLAG_FPREDECLID = 0x08,
    TYPEFLAG_FHIDDEN = 0x10,
    TYPEFLAG_FCONTROL = 0x20,
    TYPEFLAG_FDUAL = 0x40,
    TYPEFLAG_FNONEXTENSIBLE = 0x80,
    TYPEFLAG_FOLEAUTOMATION = 0x100,
    TYPEFLAG_FRESTRICTED = 0x200,
    TYPEFLAG_FAGGREGATABLE = 0x400,
    TYPEFLAG_FREPLACEABLE = 0x800,
    TYPEFLAG_FDISPATCHABLE = 0x1000,
    TYPEFLAG_FREVERSEBIND = 0x2000,
    TYPEFLAG_FPROXY = 0x4000
  } TYPEFLAGS;

cpp_quote("")
  typedef enum tagFUNCFLAGS {
    FUNCFLAG_FRESTRICTED = 0x1,
    FUNCFLAG_FSOURCE = 0x2,
    FUNCFLAG_FBINDABLE = 0x4,
    FUNCFLAG_FREQUESTEDIT = 0x8,
    FUNCFLAG_FDISPLAYBIND = 0x10,
    FUNCFLAG_FDEFAULTBIND = 0x20,
    FUNCFLAG_FHIDDEN = 0x40,
    FUNCFLAG_FUSESGETLASTERROR = 0x80,
    FUNCFLAG_FDEFAULTCOLLELEM = 0x100,
    FUNCFLAG_FUIDEFAULT = 0x200,
    FUNCFLAG_FNONBROWSABLE = 0x400,
    FUNCFLAG_FREPLACEABLE = 0x800,
    FUNCFLAG_FIMMEDIATEBIND = 0x1000
  } FUNCFLAGS;

cpp_quote("")
  typedef enum tagVARFLAGS {
    VARFLAG_FREADONLY = 0x1,
    VARFLAG_FSOURCE = 0x2,
    VARFLAG_FBINDABLE = 0x4,
    VARFLAG_FREQUESTEDIT = 0x8,
    VARFLAG_FDISPLAYBIND = 0x10,
    VARFLAG_FDEFAULTBIND = 0x20,
    VARFLAG_FHIDDEN = 0x40,
    VARFLAG_FRESTRICTED = 0x80,
    VARFLAG_FDEFAULTCOLLELEM = 0x100,
    VARFLAG_FUIDEFAULT = 0x200,
    VARFLAG_FNONBROWSABLE = 0x400,
    VARFLAG_FREPLACEABLE = 0x800,
    VARFLAG_FIMMEDIATEBIND = 0x1000
  } VARFLAGS;

cpp_quote("")
  typedef [wire_marshal (DWORD)] struct tagCLEANLOCALSTORAGE {
    IUnknown *pInterface;
    PVOID pStorage;
    DWORD flags;
  } CLEANLOCALSTORAGE;

cpp_quote("")
  typedef struct tagCUSTDATAITEM {
    GUID guid;
    VARIANTARG varValue;
  } CUSTDATAITEM,*LPCUSTDATAITEM;

cpp_quote("")
  typedef struct tagCUSTDATA {
    DWORD cCustData;
    [size_is (cCustData)] LPCUSTDATAITEM prgCustData;
  } CUSTDATA,*LPCUSTDATA;
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (00020405-0000-0000-C000-000000000046), pointer_default (unique), local]
interface ICreateTypeInfo: IUnknown {
  typedef [unique] ICreateTypeInfo *LPCREATETYPEINFO;
cpp_quote("")
  HRESULT SetGuid ([in] REFGUID guid);
  HRESULT SetTypeFlags ([in] UINT uTypeFlags);
  HRESULT SetDocString ([in] LPOLESTR pStrDoc);
  HRESULT SetHelpContext ([in] DWORD dwHelpContext);
  HRESULT SetVersion ([in] WORD wMajorVerNum,[in] WORD wMinorVerNum);
  HRESULT AddRefTypeInfo ([in] ITypeInfo *pTInfo,[in] HREFTYPE *phRefType);
  HRESULT AddFuncDesc ([in] UINT index,[in] FUNCDESC *pFuncDesc);
  HRESULT AddImplType ([in] UINT index,[in] HREFTYPE hRefType);
  HRESULT SetImplTypeFlags ([in] UINT index,[in] INT implTypeFlags);
  HRESULT SetAlignment ([in] WORD cbAlignment);
  HRESULT SetSchema ([in] LPOLESTR pStrSchema);
  HRESULT AddVarDesc ([in] UINT index,[in] VARDESC *pVarDesc);
  HRESULT SetFuncAndParamNames ([in] UINT index,[in, size_is ((UINT) cNames)] LPOLESTR *rgszNames,[in] UINT cNames);
  HRESULT SetVarName ([in] UINT index,[in] LPOLESTR szName);
  HRESULT SetTypeDescAlias ([in] TYPEDESC *pTDescAlias);
  HRESULT DefineFuncAsDllEntry ([in] UINT index,[in] LPOLESTR szDllName,[in] LPOLESTR szProcName);
  HRESULT SetFuncDocString ([in] UINT index,[in] LPOLESTR szDocString);
  HRESULT SetVarDocString ([in] UINT index,[in] LPOLESTR szDocString);
  HRESULT SetFuncHelpContext ([in] UINT index,[in] DWORD dwHelpContext);
  HRESULT SetVarHelpContext ([in] UINT index,[in] DWORD dwHelpContext);
  HRESULT SetMops ([in] UINT index,[in] BSTR bstrMops);
  HRESULT SetTypeIdldesc ([in] IDLDESC *pIdlDesc);
  HRESULT LayOut (void);
}

cpp_quote("")
[object, uuid (0002040e-0000-0000-C000-000000000046), pointer_default (unique), local]
interface ICreateTypeInfo2: ICreateTypeInfo {
  typedef [unique] ICreateTypeInfo2 *LPCREATETYPEINFO2;
cpp_quote("")
  HRESULT DeleteFuncDesc ([in] UINT index);
  HRESULT DeleteFuncDescByMemId ([in] MEMBERID memid,[in] INVOKEKIND invKind);
  HRESULT DeleteVarDesc ([in] UINT index);
  HRESULT DeleteVarDescByMemId ([in] MEMBERID memid);
  HRESULT DeleteImplType ([in] UINT index);
  HRESULT SetCustData ([in] REFGUID guid,[in] VARIANT *pVarVal);
  HRESULT SetFuncCustData ([in] UINT index,[in] REFGUID guid,[in] VARIANT *pVarVal);
  HRESULT SetParamCustData ([in] UINT indexFunc,[in] UINT indexParam,[in] REFGUID guid,[in] VARIANT *pVarVal);
  HRESULT SetVarCustData ([in] UINT index,[in] REFGUID guid,[in] VARIANT *pVarVal);
  HRESULT SetImplTypeCustData ([in] UINT index,[in] REFGUID guid,[in] VARIANT *pVarVal);
  HRESULT SetHelpStringContext ([in] ULONG dwHelpStringContext);
  HRESULT SetFuncHelpStringContext ([in] UINT index,[in] ULONG dwHelpStringContext);
  HRESULT SetVarHelpStringContext ([in] UINT index,[in] ULONG dwHelpStringContext);
  HRESULT Invalidate (void);
  HRESULT SetName ([in] LPOLESTR szName);
}
cpp_quote("")
[object, uuid (00020406-0000-0000-C000-000000000046), pointer_default (unique), local]
interface ICreateTypeLib : IUnknown {
  typedef [unique] ICreateTypeLib *LPCREATETYPELIB;
cpp_quote("")
  HRESULT CreateTypeInfo ([in] LPOLESTR szName,[in] TYPEKIND tkind,[out] ICreateTypeInfo **ppCTInfo);
  HRESULT SetName ([in] LPOLESTR szName);
  HRESULT SetVersion ([in] WORD wMajorVerNum,[in] WORD wMinorVerNum);
  HRESULT SetGuid ([in] REFGUID guid);
  HRESULT SetDocString ([in] LPOLESTR szDoc);
  HRESULT SetHelpFileName ([in] LPOLESTR szHelpFileName);
  HRESULT SetHelpContext ([in] DWORD dwHelpContext);
  HRESULT SetLcid ([in] LCID lcid);
  HRESULT SetLibFlags ([in] UINT uLibFlags);
  HRESULT SaveAllChanges (void);
}
cpp_quote("")
[object, uuid (0002040f-0000-0000-C000-000000000046), pointer_default (unique), local]
interface ICreateTypeLib2 : ICreateTypeLib {
  typedef [unique] ICreateTypeLib2 *LPCREATETYPELIB2;
  HRESULT DeleteTypeInfo ([in] LPOLESTR szName);
  HRESULT SetCustData ([in] REFGUID guid,[in] VARIANT *pVarVal);
  HRESULT SetHelpStringContext ([in] ULONG dwHelpStringContext);
  HRESULT SetHelpStringDll ([in] LPOLESTR szFileName);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (00020400-0000-0000-C000-000000000046), pointer_default (unique)]
interface IDispatch : IUnknown {
  typedef [unique] IDispatch *LPDISPATCH;
cpp_quote("")
  HRESULT GetTypeInfoCount ([out] UINT *pctinfo);
  HRESULT GetTypeInfo ([in] UINT iTInfo,[in] LCID lcid,[out] ITypeInfo **ppTInfo);
  HRESULT GetIDsOfNames ([in] REFIID riid,[in, size_is (cNames)] LPOLESTR *rgszNames,[in, range (0, 16384)] UINT cNames,[in] LCID lcid,[out, size_is (cNames)] DISPID *rgDispId);
  [local] HRESULT Invoke ([in] DISPID dispIdMember,[in] REFIID riid,[in] LCID lcid,[in] WORD wFlags,[in, out] DISPPARAMS *pDispParams,[out] VARIANT *pVarResult,[out] EXCEPINFO *pExcepInfo,[out] UINT *puArgErr);
  [call_as (Invoke)] HRESULT RemoteInvoke ([in] DISPID dispIdMember,[in] REFIID riid,[in] LCID lcid,[in] DWORD dwFlags,[in] DISPPARAMS *pDispParams,[out] VARIANT *pVarResult,[out] EXCEPINFO *pExcepInfo,[out] UINT *pArgErr,[in] UINT cVarRef,[in, size_is (cVarRef)] UINT *rgVarRefIdx,[in, out, size_is (cVarRef)] VARIANTARG *rgVarRef);
  cpp_quote("")
  const DISPID DISPID_UNKNOWN = -1;
  const DISPID DISPID_VALUE = 0;
  const DISPID DISPID_PROPERTYPUT = -3;
  const DISPID DISPID_NEWENUM = -4;
  const DISPID DISPID_EVALUATE = -5;
  const DISPID DISPID_CONSTRUCTOR = -6;
  const DISPID DISPID_DESTRUCTOR = -7;
  const DISPID DISPID_COLLECT = -8;
  cpp_quote("")
}

cpp_quote("")
[object, uuid (00020404-0000-0000-C000-000000000046), pointer_default (unique)]
interface IEnumVARIANT : IUnknown {
  typedef [unique] IEnumVARIANT *LPENUMVARIANT;
cpp_quote("")
  [local] HRESULT Next ([in] ULONG celt,[out, size_is (celt), length_is (*pCeltFetched)] VARIANT *rgVar,[out] ULONG *pCeltFetched);
  [call_as (Next)] HRESULT RemoteNext ([in] ULONG celt,[out, size_is (celt), length_is (*pCeltFetched)] VARIANT *rgVar,[out] ULONG *pCeltFetched);
  HRESULT Skip ([in] ULONG celt);
  HRESULT Reset ();
  HRESULT Clone ([out] IEnumVARIANT **ppEnum);
}

cpp_quote("")
[object, uuid (00020403-0000-0000-C000-000000000046), pointer_default (unique)]
interface ITypeComp : IUnknown {
  typedef [unique] ITypeComp *LPTYPECOMP;
cpp_quote("")
  typedef [v1_enum] enum tagDESCKIND {
    DESCKIND_NONE = 0,
    DESCKIND_FUNCDESC,
    DESCKIND_VARDESC,
    DESCKIND_TYPECOMP,
    DESCKIND_IMPLICITAPPOBJ,
    DESCKIND_MAX
  } DESCKIND;
cpp_quote("")
  typedef union tagBINDPTR {
    FUNCDESC *lpfuncdesc;
    VARDESC *lpvardesc;
    ITypeComp *lptcomp;
  } BINDPTR,*LPBINDPTR;
cpp_quote("")
  [local] HRESULT Bind ([in] LPOLESTR szName,[in] ULONG lHashVal,[in] WORD wFlags,[out] ITypeInfo **ppTInfo,[out] DESCKIND *pDescKind,[out] BINDPTR *pBindPtr);
  [call_as (Bind)] HRESULT RemoteBind ([in] LPOLESTR szName,[in] ULONG lHashVal,[in] WORD wFlags,[out] ITypeInfo **ppTInfo,[out] DESCKIND *pDescKind,[out] LPFUNCDESC *ppFuncDesc,[out] LPVARDESC *ppVarDesc,[out] ITypeComp **ppTypeComp,[out] CLEANLOCALSTORAGE *pDummy);
  [local] HRESULT BindType ([in] LPOLESTR szName,[in] ULONG lHashVal,[out] ITypeInfo **ppTInfo,[out] ITypeComp **ppTComp);
  [call_as (BindType)] HRESULT RemoteBindType ([in] LPOLESTR szName,[in] ULONG lHashVal,[out] ITypeInfo **ppTInfo);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (00020401-0000-0000-C000-000000000046), pointer_default (unique)]
interface ITypeInfo : IUnknown {
  typedef [unique] ITypeInfo *LPTYPEINFO;
cpp_quote("")
  [local] HRESULT GetTypeAttr ([out] TYPEATTR **ppTypeAttr);
  [call_as (GetTypeAttr)] HRESULT RemoteGetTypeAttr ([out] LPTYPEATTR *ppTypeAttr,[out] CLEANLOCALSTORAGE *pDummy);
  HRESULT GetTypeComp ([out] ITypeComp **ppTComp);
  [local] HRESULT GetFuncDesc ([in] UINT index,[out] FUNCDESC **ppFuncDesc);
  [call_as (GetFuncDesc)] HRESULT RemoteGetFuncDesc ([in] UINT index,[out] LPFUNCDESC *ppFuncDesc,[out] CLEANLOCALSTORAGE *pDummy);
  [local] HRESULT GetVarDesc ([in] UINT index,[out] VARDESC **ppVarDesc);
  [call_as (GetVarDesc)] HRESULT RemoteGetVarDesc ([in] UINT index,[out] LPVARDESC *ppVarDesc,[out] CLEANLOCALSTORAGE *pDummy);
  [local] HRESULT GetNames ([in] MEMBERID memid,[out, size_is (cMaxNames), length_is (*pcNames)] BSTR *rgBstrNames,[in] UINT cMaxNames,[out] UINT *pcNames);
  [call_as (GetNames)] HRESULT RemoteGetNames ([in] MEMBERID memid,[out, size_is (cMaxNames), length_is (*pcNames)] BSTR *rgBstrNames,[in] UINT cMaxNames,[out] UINT *pcNames);
  HRESULT GetRefTypeOfImplType ([in] UINT index,[out] HREFTYPE *pRefType);
  HRESULT GetImplTypeFlags ([in] UINT index,[out] INT *pImplTypeFlags);
  [local] HRESULT GetIDsOfNames ([in, size_is (cNames)] LPOLESTR *rgszNames,[in] UINT cNames,[out, size_is (cNames)] MEMBERID *pMemId);
  [call_as (GetIDsOfNames)] HRESULT LocalGetIDsOfNames (void);
  [local] HRESULT Invoke ([in] PVOID pvInstance,[in] MEMBERID memid,[in] WORD wFlags,[in, out] DISPPARAMS *pDispParams,[out] VARIANT *pVarResult,[out] EXCEPINFO *pExcepInfo,[out] UINT *puArgErr);
  [call_as (Invoke)] HRESULT LocalInvoke (void);
  [local] HRESULT GetDocumentation ([in] MEMBERID memid,[out] BSTR *pBstrName,[out] BSTR *pBstrDocString,[out] DWORD *pdwHelpContext,[out] BSTR *pBstrHelpFile);
  [call_as (GetDocumentation)] HRESULT RemoteGetDocumentation ([in] MEMBERID memid,[in] DWORD refPtrFlags,[out] BSTR *pBstrName,[out] BSTR *pBstrDocString,[out] DWORD *pdwHelpContext,[out] BSTR *pBstrHelpFile);
  [local]
  HRESULT GetDllEntry ([in] MEMBERID memid,[in] INVOKEKIND invKind,[out] BSTR *pBstrDllName,[out] BSTR *pBstrName,[out] WORD *pwOrdinal);
  [call_as (GetDllEntry)]
  HRESULT RemoteGetDllEntry ([in] MEMBERID memid,[in] INVOKEKIND invKind,[in] DWORD refPtrFlags,[out] BSTR *pBstrDllName,[out] BSTR *pBstrName,[out] WORD *pwOrdinal);
  HRESULT GetRefTypeInfo ([in] HREFTYPE hRefType,[out] ITypeInfo **ppTInfo);
  [local] HRESULT AddressOfMember ([in] MEMBERID memid,[in] INVOKEKIND invKind,[out] PVOID *ppv);
  [call_as (AddressOfMember)] HRESULT LocalAddressOfMember (void);
  [local] HRESULT CreateInstance ([in] IUnknown *pUnkOuter,[in] REFIID riid,[out, iid_is (riid)] PVOID *ppvObj);
  [call_as (CreateInstance)] HRESULT RemoteCreateInstance ([in] REFIID riid,[out, iid_is (riid)] IUnknown **ppvObj);
  HRESULT GetMops ([in] MEMBERID memid,[out] BSTR *pBstrMops);
  [local] HRESULT GetContainingTypeLib ([out] ITypeLib **ppTLib,[out] UINT *pIndex);
  [call_as (GetContainingTypeLib)] HRESULT RemoteGetContainingTypeLib ([out] ITypeLib **ppTLib,[out] UINT *pIndex);
  [local] void ReleaseTypeAttr ([in] TYPEATTR *pTypeAttr);
  [call_as (ReleaseTypeAttr)] HRESULT LocalReleaseTypeAttr (void);
  [local] void ReleaseFuncDesc ([in] FUNCDESC *pFuncDesc);
  [call_as (ReleaseFuncDesc)] HRESULT LocalReleaseFuncDesc (void);
  [local] void ReleaseVarDesc ([in] VARDESC *pVarDesc);
  [call_as (ReleaseVarDesc)] HRESULT LocalReleaseVarDesc (void);
}

cpp_quote("")
[object, uuid (00020412-0000-0000-C000-000000000046), pointer_default (unique)]
interface ITypeInfo2 : ITypeInfo {
  typedef [unique] ITypeInfo2 *LPTYPEINFO2;
cpp_quote("")
  HRESULT GetTypeKind ([out] TYPEKIND *pTypeKind);
  HRESULT GetTypeFlags ([out] ULONG *pTypeFlags);
  HRESULT GetFuncIndexOfMemId ([in] MEMBERID memid,[in] INVOKEKIND invKind,[out] UINT *pFuncIndex);
  HRESULT GetVarIndexOfMemId ([in] MEMBERID memid,[out] UINT *pVarIndex);
  HRESULT GetCustData ([in] REFGUID guid,[out] VARIANT *pVarVal);
  HRESULT GetFuncCustData ([in] UINT index,[in] REFGUID guid,[out] VARIANT *pVarVal);
  HRESULT GetParamCustData ([in] UINT indexFunc,[in] UINT indexParam,[in] REFGUID guid,[out] VARIANT *pVarVal);
  HRESULT GetVarCustData ([in] UINT index,[in] REFGUID guid,[out] VARIANT *pVarVal);
  HRESULT GetImplTypeCustData ([in] UINT index,[in] REFGUID guid,[out] VARIANT *pVarVal);
  [local] HRESULT GetDocumentation2 ([in] MEMBERID memid,[in] LCID lcid,[out] BSTR *pbstrHelpString,[out] DWORD *pdwHelpStringContext,[out] BSTR *pbstrHelpStringDll);
  [call_as (GetDocumentation2)] HRESULT RemoteGetDocumentation2 ([in] MEMBERID memid,[in] LCID lcid,[in] DWORD refPtrFlags,[out] BSTR *pbstrHelpString,[out] DWORD *pdwHelpStringContext,[out] BSTR *pbstrHelpStringDll);
  HRESULT GetAllCustData ([out] CUSTDATA *pCustData);
  HRESULT GetAllFuncCustData ([in] UINT index,[out] CUSTDATA *pCustData);
  HRESULT GetAllParamCustData ([in] UINT indexFunc,[in] UINT indexParam,[out] CUSTDATA *pCustData);
  HRESULT GetAllVarCustData ([in] UINT index,[out] CUSTDATA *pCustData);
  HRESULT GetAllImplTypeCustData ([in] UINT index,[out] CUSTDATA *pCustData);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (00020402-0000-0000-C000-000000000046), pointer_default (unique)]
interface ITypeLib : IUnknown {
  typedef [v1_enum] enum tagSYSKIND {
    SYS_WIN16 = 0,
    SYS_WIN32,
    SYS_MAC,
    SYS_WIN64
  } SYSKIND;
cpp_quote("")
  typedef [v1_enum] enum tagLIBFLAGS {
    LIBFLAG_FRESTRICTED = 0x01,
    LIBFLAG_FCONTROL = 0x02,
    LIBFLAG_FHIDDEN = 0x04,
    LIBFLAG_FHASDISKIMAGE = 0x08
  } LIBFLAGS;
cpp_quote("")
  typedef [unique] ITypeLib *LPTYPELIB;
cpp_quote("")
  typedef struct tagTLIBATTR {
    GUID guid;
    LCID lcid;
    SYSKIND syskind;
    WORD wMajorVerNum;
    WORD wMinorVerNum;
    WORD wLibFlags;
  } TLIBATTR,*LPTLIBATTR;
cpp_quote("")
  [local] UINT GetTypeInfoCount (void);
  [call_as (GetTypeInfoCount)] HRESULT RemoteGetTypeInfoCount ([out] UINT *pcTInfo);
  HRESULT GetTypeInfo ([in] UINT index,[out] ITypeInfo **ppTInfo);
  HRESULT GetTypeInfoType ([in] UINT index,[out] TYPEKIND *pTKind);
  HRESULT GetTypeInfoOfGuid ([in] REFGUID guid,[out] ITypeInfo **ppTinfo);
  [local] HRESULT GetLibAttr ([out] TLIBATTR **ppTLibAttr);
  [call_as (GetLibAttr)] HRESULT RemoteGetLibAttr ([out] LPTLIBATTR *ppTLibAttr,[out] CLEANLOCALSTORAGE *pDummy);
  HRESULT GetTypeComp ([out] ITypeComp **ppTComp);
  [local] HRESULT GetDocumentation ([in] INT index,[out] BSTR *pBstrName,[out] BSTR *pBstrDocString,[out] DWORD *pdwHelpContext,[out] BSTR *pBstrHelpFile);
  [call_as (GetDocumentation)] HRESULT RemoteGetDocumentation ([in] INT index,[in] DWORD refPtrFlags,[out] BSTR *pBstrName,[out] BSTR *pBstrDocString,[out] DWORD *pdwHelpContext,[out] BSTR *pBstrHelpFile);
  [local] HRESULT IsName ([in, out] LPOLESTR szNameBuf,[in] ULONG lHashVal,[out] BOOL *pfName);
  [call_as (IsName)] HRESULT RemoteIsName ([in] LPOLESTR szNameBuf,[in] ULONG lHashVal,[out] BOOL *pfName,[out] BSTR *pBstrLibName);
  [local] HRESULT FindName ([in, out] LPOLESTR szNameBuf,[in] ULONG lHashVal,[out, size_is (*pcFound), length_is (*pcFound)] ITypeInfo **ppTInfo,[out, size_is (*pcFound), length_is (*pcFound)] MEMBERID *rgMemId,[in, out] USHORT *pcFound);
  [call_as (FindName)] HRESULT RemoteFindName ([in] LPOLESTR szNameBuf,[in] ULONG lHashVal,[out, size_is (*pcFound), length_is (*pcFound)] ITypeInfo **ppTInfo,[out, size_is (*pcFound), length_is (*pcFound)] MEMBERID *rgMemId,[in, out] USHORT *pcFound,[out] BSTR *pBstrLibName);
  [local] void ReleaseTLibAttr ([in] TLIBATTR *pTLibAttr);
  [call_as (ReleaseTLibAttr)] HRESULT LocalReleaseTLibAttr (void);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (00020411-0000-0000-C000-000000000046), pointer_default (unique)]
interface ITypeLib2 : ITypeLib {
  typedef [unique] ITypeLib2 *LPTYPELIB2;
cpp_quote("")
  HRESULT GetCustData ([in] REFGUID guid,[out] VARIANT *pVarVal);
  [local] HRESULT GetLibStatistics ([out] ULONG *pcUniqueNames,[out] ULONG *pcchUniqueNames);
  [call_as (GetLibStatistics)] HRESULT RemoteGetLibStatistics ([out] ULONG *pcUniqueNames,[out] ULONG *pcchUniqueNames);
  [local] HRESULT GetDocumentation2 ([in] INT index,[in] LCID lcid,[out] BSTR *pbstrHelpString,[out] DWORD *pdwHelpStringContext,[out] BSTR *pbstrHelpStringDll);
  [call_as (GetDocumentation2)] HRESULT RemoteGetDocumentation2 ([in] INT index,[in] LCID lcid,[in] DWORD refPtrFlags,[out] BSTR *pbstrHelpString,[out] DWORD *pdwHelpStringContext,[out] BSTR *pbstrHelpStringDll);
  HRESULT GetAllCustData ([out] CUSTDATA *pCustData);
}

cpp_quote("")
[object, uuid (00020410-0000-0000-C000-000000000046), pointer_default (unique), local]
interface ITypeChangeEvents: IUnknown {
  typedef [unique] ITypeChangeEvents *LPTYPECHANGEEVENTS;
cpp_quote("")
  typedef enum tagCHANGEKIND {
    CHANGEKIND_ADDMEMBER,
    CHANGEKIND_DELETEMEMBER,
    CHANGEKIND_SETNAMES,
    CHANGEKIND_SETDOCUMENTATION,
    CHANGEKIND_GENERAL,
    CHANGEKIND_INVALIDATE,
    CHANGEKIND_CHANGEFAILED,
    CHANGEKIND_MAX
  } CHANGEKIND;
cpp_quote("")
  HRESULT RequestTypeChange ([in] CHANGEKIND changeKind,[in] ITypeInfo *pTInfoBefore,[in] LPOLESTR pStrName,[out] INT *pfCancel);
  HRESULT AfterTypeChange ([in] CHANGEKIND changeKind,[in] ITypeInfo *pTInfoAfter,[in] LPOLESTR pStrName);
}

cpp_quote("")
[object, uuid (1cf2b120-547d-101b-8e65-08002b2bd119), pointer_default (unique)]
interface IErrorInfo: IUnknown {
  typedef [unique] IErrorInfo *LPERRORINFO;
cpp_quote("")
  HRESULT GetGUID ([out] GUID *pGUID);
  HRESULT GetSource ([out] BSTR *pBstrSource);
  HRESULT GetDescription ([out] BSTR *pBstrDescription);
  HRESULT GetHelpFile ([out] BSTR *pBstrHelpFile);
  HRESULT GetHelpContext ([out] DWORD *pdwHelpContext);
}

cpp_quote("")
[object, uuid (22f03340-547d-101b-8e65-08002b2bd119), pointer_default (unique)]
interface ICreateErrorInfo: IUnknown {
  typedef [unique] ICreateErrorInfo *LPCREATEERRORINFO;
cpp_quote("")
  HRESULT SetGUID ([in] REFGUID rguid);
  HRESULT SetSource ([in] LPOLESTR szSource);
  HRESULT SetDescription ([in] LPOLESTR szDescription);
  HRESULT SetHelpFile ([in] LPOLESTR szHelpFile);
  HRESULT SetHelpContext ([in] DWORD dwHelpContext);
}

cpp_quote("")
[object, uuid (DF0B3D60-548f-101b-8e65-08002b2bd119), pointer_default (unique)]
interface ISupportErrorInfo: IUnknown {
  typedef [unique] ISupportErrorInfo *LPSUPPORTERRORINFO;
cpp_quote("")
  HRESULT InterfaceSupportsErrorInfo ([in] REFIID riid);
}

cpp_quote("")
[object, uuid (0000002e-0000-0000-C000-000000000046)]
interface ITypeFactory : IUnknown {
  HRESULT CreateFromTypeInfo ([in] ITypeInfo *pTypeInfo,[in] REFIID riid,[out, iid_is (riid)] IUnknown **ppv);
}

cpp_quote("")
[local, object, uuid (0000002d-0000-0000-C000-000000000046)]
interface ITypeMarshal : IUnknown {
  HRESULT Size ([in] PVOID pvType,[in] DWORD dwDestContext,[in] PVOID pvDestContext,[out] ULONG *pSize);
  HRESULT Marshal ([in] PVOID pvType,[in] DWORD dwDestContext,[in] PVOID pvDestContext,[in] ULONG cbBufferLength,[out] BYTE *pBuffer,[out] ULONG *pcbWritten);
  HRESULT Unmarshal ([out] PVOID pvType,[in] DWORD dwFlags,[in] ULONG cbBufferLength,[in] BYTE *pBuffer,[out] ULONG *pcbRead);
  HRESULT Free ([in] PVOID pvType);
}

cpp_quote("")
[local, object, uuid (0000002f-0000-0000-C000-000000000046)]
interface IRecordInfo: IUnknown {
  typedef [unique] IRecordInfo *LPRECORDINFO;
cpp_quote("")
  HRESULT RecordInit ([out] PVOID pvNew);
  HRESULT RecordClear ([in] PVOID pvExisting);
  HRESULT RecordCopy ([in] PVOID pvExisting,[out] PVOID pvNew);
  HRESULT GetGuid ([out] GUID *pguid);
  HRESULT GetName ([out] BSTR *pbstrName);
  HRESULT GetSize ([out] ULONG *pcbSize);
  HRESULT GetTypeInfo ([out] ITypeInfo **ppTypeInfo);
  HRESULT GetField ([in] PVOID pvData,[in] LPCOLESTR szFieldName,[out] VARIANT *pvarField);
  HRESULT GetFieldNoCopy ([in] PVOID pvData,[in] LPCOLESTR szFieldName,[out] VARIANT *pvarField,[out] PVOID *ppvDataCArray);
  HRESULT PutField ([in] ULONG wFlags,[in, out] PVOID pvData,[in] LPCOLESTR szFieldName,[in] VARIANT *pvarField);
  HRESULT PutFieldNoCopy ([in] ULONG wFlags,[in, out] PVOID pvData,[in] LPCOLESTR szFieldName,[in] VARIANT *pvarField);
  HRESULT GetFieldNames ([in, out] ULONG *pcNames,[out, size_is (*pcNames), length_is (*pcNames)] BSTR *rgBstrNames);
  BOOL IsMatchingType ([in] IRecordInfo *pRecordInfo);
  PVOID RecordCreate ();
  HRESULT RecordCreateCopy ([in] PVOID pvSource,[out] PVOID *ppvDest);
  HRESULT RecordDestroy ([in] PVOID pvRecord);
}

cpp_quote("")
[object, uuid (3127ca40-446e-11ce-8135-00aa004bb851), pointer_default (unique)]
interface IErrorLog : IUnknown {
  typedef IErrorLog *LPERRORLOG;
cpp_quote("")
  HRESULT AddError ([in] LPCOLESTR pszPropName,[in] EXCEPINFO *pExcepInfo);
}

cpp_quote("")
[object, uuid (55272a00-42cb-11ce-8135-00aa004bb851), pointer_default (unique)]
interface IPropertyBag : IUnknown {
  typedef IPropertyBag *LPPROPERTYBAG;
cpp_quote("")
  [local] HRESULT Read ([in] LPCOLESTR pszPropName,[in, out] VARIANT *pVar,[in, unique] IErrorLog *pErrorLog);
  [call_as (Read)] HRESULT RemoteRead ([in] LPCOLESTR pszPropName,[out] VARIANT *pVar,[in, unique] IErrorLog *pErrorLog,[in] DWORD varType,[in] IUnknown *pUnkObj);
  HRESULT Write ([in] LPCOLESTR pszPropName,[in] VARIANT *pVar);
}
cpp_quote("#endif")
