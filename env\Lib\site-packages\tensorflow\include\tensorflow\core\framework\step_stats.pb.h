// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/step_stats.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/allocation_description.pb.h"
#include "tensorflow/core/framework/tensor_description.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
namespace tensorflow {
class AllocationRecord;
struct AllocationRecordDefaultTypeInternal;
extern AllocationRecordDefaultTypeInternal _AllocationRecord_default_instance_;
class AllocatorMemoryUsed;
struct AllocatorMemoryUsedDefaultTypeInternal;
extern AllocatorMemoryUsedDefaultTypeInternal _AllocatorMemoryUsed_default_instance_;
class DeviceStepStats;
struct DeviceStepStatsDefaultTypeInternal;
extern DeviceStepStatsDefaultTypeInternal _DeviceStepStats_default_instance_;
class DeviceStepStats_ThreadNamesEntry_DoNotUse;
struct DeviceStepStats_ThreadNamesEntry_DoNotUseDefaultTypeInternal;
extern DeviceStepStats_ThreadNamesEntry_DoNotUseDefaultTypeInternal _DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_;
class MemoryStats;
struct MemoryStatsDefaultTypeInternal;
extern MemoryStatsDefaultTypeInternal _MemoryStats_default_instance_;
class NodeExecStats;
struct NodeExecStatsDefaultTypeInternal;
extern NodeExecStatsDefaultTypeInternal _NodeExecStats_default_instance_;
class NodeOutput;
struct NodeOutputDefaultTypeInternal;
extern NodeOutputDefaultTypeInternal _NodeOutput_default_instance_;
class StepStats;
struct StepStatsDefaultTypeInternal;
extern StepStatsDefaultTypeInternal _StepStats_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AllocationRecord* Arena::CreateMaybeMessage<::tensorflow::AllocationRecord>(Arena*);
template<> ::tensorflow::AllocatorMemoryUsed* Arena::CreateMaybeMessage<::tensorflow::AllocatorMemoryUsed>(Arena*);
template<> ::tensorflow::DeviceStepStats* Arena::CreateMaybeMessage<::tensorflow::DeviceStepStats>(Arena*);
template<> ::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DeviceStepStats_ThreadNamesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::MemoryStats* Arena::CreateMaybeMessage<::tensorflow::MemoryStats>(Arena*);
template<> ::tensorflow::NodeExecStats* Arena::CreateMaybeMessage<::tensorflow::NodeExecStats>(Arena*);
template<> ::tensorflow::NodeOutput* Arena::CreateMaybeMessage<::tensorflow::NodeOutput>(Arena*);
template<> ::tensorflow::StepStats* Arena::CreateMaybeMessage<::tensorflow::StepStats>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class AllocationRecord final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AllocationRecord) */ {
 public:
  inline AllocationRecord() : AllocationRecord(nullptr) {}
  ~AllocationRecord() override;
  explicit PROTOBUF_CONSTEXPR AllocationRecord(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AllocationRecord(const AllocationRecord& from);
  AllocationRecord(AllocationRecord&& from) noexcept
    : AllocationRecord() {
    *this = ::std::move(from);
  }

  inline AllocationRecord& operator=(const AllocationRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline AllocationRecord& operator=(AllocationRecord&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AllocationRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const AllocationRecord* internal_default_instance() {
    return reinterpret_cast<const AllocationRecord*>(
               &_AllocationRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AllocationRecord& a, AllocationRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(AllocationRecord* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AllocationRecord* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AllocationRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AllocationRecord>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AllocationRecord& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AllocationRecord& from) {
    AllocationRecord::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AllocationRecord* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AllocationRecord";
  }
  protected:
  explicit AllocationRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllocMicrosFieldNumber = 1,
    kAllocBytesFieldNumber = 2,
  };
  // int64 alloc_micros = 1;
  void clear_alloc_micros();
  int64_t alloc_micros() const;
  void set_alloc_micros(int64_t value);
  private:
  int64_t _internal_alloc_micros() const;
  void _internal_set_alloc_micros(int64_t value);
  public:

  // int64 alloc_bytes = 2;
  void clear_alloc_bytes();
  int64_t alloc_bytes() const;
  void set_alloc_bytes(int64_t value);
  private:
  int64_t _internal_alloc_bytes() const;
  void _internal_set_alloc_bytes(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.AllocationRecord)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t alloc_micros_;
    int64_t alloc_bytes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class AllocatorMemoryUsed final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AllocatorMemoryUsed) */ {
 public:
  inline AllocatorMemoryUsed() : AllocatorMemoryUsed(nullptr) {}
  ~AllocatorMemoryUsed() override;
  explicit PROTOBUF_CONSTEXPR AllocatorMemoryUsed(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AllocatorMemoryUsed(const AllocatorMemoryUsed& from);
  AllocatorMemoryUsed(AllocatorMemoryUsed&& from) noexcept
    : AllocatorMemoryUsed() {
    *this = ::std::move(from);
  }

  inline AllocatorMemoryUsed& operator=(const AllocatorMemoryUsed& from) {
    CopyFrom(from);
    return *this;
  }
  inline AllocatorMemoryUsed& operator=(AllocatorMemoryUsed&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AllocatorMemoryUsed& default_instance() {
    return *internal_default_instance();
  }
  static inline const AllocatorMemoryUsed* internal_default_instance() {
    return reinterpret_cast<const AllocatorMemoryUsed*>(
               &_AllocatorMemoryUsed_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(AllocatorMemoryUsed& a, AllocatorMemoryUsed& b) {
    a.Swap(&b);
  }
  inline void Swap(AllocatorMemoryUsed* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AllocatorMemoryUsed* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AllocatorMemoryUsed* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AllocatorMemoryUsed>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AllocatorMemoryUsed& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AllocatorMemoryUsed& from) {
    AllocatorMemoryUsed::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AllocatorMemoryUsed* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AllocatorMemoryUsed";
  }
  protected:
  explicit AllocatorMemoryUsed(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllocationRecordsFieldNumber = 6,
    kAllocatorNameFieldNumber = 1,
    kTotalBytesFieldNumber = 2,
    kPeakBytesFieldNumber = 3,
    kLiveBytesFieldNumber = 4,
    kAllocatorBytesInUseFieldNumber = 5,
  };
  // repeated .tensorflow.AllocationRecord allocation_records = 6;
  int allocation_records_size() const;
  private:
  int _internal_allocation_records_size() const;
  public:
  void clear_allocation_records();
  ::tensorflow::AllocationRecord* mutable_allocation_records(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >*
      mutable_allocation_records();
  private:
  const ::tensorflow::AllocationRecord& _internal_allocation_records(int index) const;
  ::tensorflow::AllocationRecord* _internal_add_allocation_records();
  public:
  const ::tensorflow::AllocationRecord& allocation_records(int index) const;
  ::tensorflow::AllocationRecord* add_allocation_records();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >&
      allocation_records() const;

  // string allocator_name = 1;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_allocator_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_allocator_name();
  PROTOBUF_NODISCARD std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  private:
  const std::string& _internal_allocator_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_allocator_name(const std::string& value);
  std::string* _internal_mutable_allocator_name();
  public:

  // int64 total_bytes = 2;
  void clear_total_bytes();
  int64_t total_bytes() const;
  void set_total_bytes(int64_t value);
  private:
  int64_t _internal_total_bytes() const;
  void _internal_set_total_bytes(int64_t value);
  public:

  // int64 peak_bytes = 3;
  void clear_peak_bytes();
  int64_t peak_bytes() const;
  void set_peak_bytes(int64_t value);
  private:
  int64_t _internal_peak_bytes() const;
  void _internal_set_peak_bytes(int64_t value);
  public:

  // int64 live_bytes = 4;
  void clear_live_bytes();
  int64_t live_bytes() const;
  void set_live_bytes(int64_t value);
  private:
  int64_t _internal_live_bytes() const;
  void _internal_set_live_bytes(int64_t value);
  public:

  // int64 allocator_bytes_in_use = 5;
  void clear_allocator_bytes_in_use();
  int64_t allocator_bytes_in_use() const;
  void set_allocator_bytes_in_use(int64_t value);
  private:
  int64_t _internal_allocator_bytes_in_use() const;
  void _internal_set_allocator_bytes_in_use(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.AllocatorMemoryUsed)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord > allocation_records_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
    int64_t total_bytes_;
    int64_t peak_bytes_;
    int64_t live_bytes_;
    int64_t allocator_bytes_in_use_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class NodeOutput final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NodeOutput) */ {
 public:
  inline NodeOutput() : NodeOutput(nullptr) {}
  ~NodeOutput() override;
  explicit PROTOBUF_CONSTEXPR NodeOutput(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NodeOutput(const NodeOutput& from);
  NodeOutput(NodeOutput&& from) noexcept
    : NodeOutput() {
    *this = ::std::move(from);
  }

  inline NodeOutput& operator=(const NodeOutput& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeOutput& operator=(NodeOutput&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeOutput& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeOutput* internal_default_instance() {
    return reinterpret_cast<const NodeOutput*>(
               &_NodeOutput_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(NodeOutput& a, NodeOutput& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeOutput* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeOutput* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeOutput* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NodeOutput>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NodeOutput& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NodeOutput& from) {
    NodeOutput::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeOutput* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NodeOutput";
  }
  protected:
  explicit NodeOutput(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorDescriptionFieldNumber = 3,
    kSlotFieldNumber = 1,
  };
  // .tensorflow.TensorDescription tensor_description = 3;
  bool has_tensor_description() const;
  private:
  bool _internal_has_tensor_description() const;
  public:
  void clear_tensor_description();
  const ::tensorflow::TensorDescription& tensor_description() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorDescription* release_tensor_description();
  ::tensorflow::TensorDescription* mutable_tensor_description();
  void set_allocated_tensor_description(::tensorflow::TensorDescription* tensor_description);
  private:
  const ::tensorflow::TensorDescription& _internal_tensor_description() const;
  ::tensorflow::TensorDescription* _internal_mutable_tensor_description();
  public:
  void unsafe_arena_set_allocated_tensor_description(
      ::tensorflow::TensorDescription* tensor_description);
  ::tensorflow::TensorDescription* unsafe_arena_release_tensor_description();

  // int32 slot = 1;
  void clear_slot();
  int32_t slot() const;
  void set_slot(int32_t value);
  private:
  int32_t _internal_slot() const;
  void _internal_set_slot(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.NodeOutput)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorDescription* tensor_description_;
    int32_t slot_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class MemoryStats final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryStats) */ {
 public:
  inline MemoryStats() : MemoryStats(nullptr) {}
  ~MemoryStats() override;
  explicit PROTOBUF_CONSTEXPR MemoryStats(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryStats(const MemoryStats& from);
  MemoryStats(MemoryStats&& from) noexcept
    : MemoryStats() {
    *this = ::std::move(from);
  }

  inline MemoryStats& operator=(const MemoryStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryStats& operator=(MemoryStats&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryStats& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryStats* internal_default_instance() {
    return reinterpret_cast<const MemoryStats*>(
               &_MemoryStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MemoryStats& a, MemoryStats& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryStats* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryStats>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryStats& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryStats& from) {
    MemoryStats::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryStats* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryStats";
  }
  protected:
  explicit MemoryStats(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPersistentTensorAllocIdsFieldNumber = 5,
    kDevicePersistentTensorAllocIdsFieldNumber = 6,
    kTempMemorySizeFieldNumber = 1,
    kDeviceTempMemorySizeFieldNumber = 2,
    kPersistentMemorySizeFieldNumber = 3,
    kDevicePersistentMemorySizeFieldNumber = 4,
  };
  // repeated int64 persistent_tensor_alloc_ids = 5;
  int persistent_tensor_alloc_ids_size() const;
  private:
  int _internal_persistent_tensor_alloc_ids_size() const;
  public:
  void clear_persistent_tensor_alloc_ids();
  private:
  int64_t _internal_persistent_tensor_alloc_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_persistent_tensor_alloc_ids() const;
  void _internal_add_persistent_tensor_alloc_ids(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_persistent_tensor_alloc_ids();
  public:
  int64_t persistent_tensor_alloc_ids(int index) const;
  void set_persistent_tensor_alloc_ids(int index, int64_t value);
  void add_persistent_tensor_alloc_ids(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      persistent_tensor_alloc_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_persistent_tensor_alloc_ids();

  // repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
  PROTOBUF_DEPRECATED int device_persistent_tensor_alloc_ids_size() const;
  private:
  int _internal_device_persistent_tensor_alloc_ids_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_device_persistent_tensor_alloc_ids();
  private:
  int64_t _internal_device_persistent_tensor_alloc_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_device_persistent_tensor_alloc_ids() const;
  void _internal_add_device_persistent_tensor_alloc_ids(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_device_persistent_tensor_alloc_ids();
  public:
  PROTOBUF_DEPRECATED int64_t device_persistent_tensor_alloc_ids(int index) const;
  PROTOBUF_DEPRECATED void set_device_persistent_tensor_alloc_ids(int index, int64_t value);
  PROTOBUF_DEPRECATED void add_device_persistent_tensor_alloc_ids(int64_t value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      device_persistent_tensor_alloc_ids() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_device_persistent_tensor_alloc_ids();

  // int64 temp_memory_size = 1;
  void clear_temp_memory_size();
  int64_t temp_memory_size() const;
  void set_temp_memory_size(int64_t value);
  private:
  int64_t _internal_temp_memory_size() const;
  void _internal_set_temp_memory_size(int64_t value);
  public:

  // int64 device_temp_memory_size = 2 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_temp_memory_size();
  PROTOBUF_DEPRECATED int64_t device_temp_memory_size() const;
  PROTOBUF_DEPRECATED void set_device_temp_memory_size(int64_t value);
  private:
  int64_t _internal_device_temp_memory_size() const;
  void _internal_set_device_temp_memory_size(int64_t value);
  public:

  // int64 persistent_memory_size = 3;
  void clear_persistent_memory_size();
  int64_t persistent_memory_size() const;
  void set_persistent_memory_size(int64_t value);
  private:
  int64_t _internal_persistent_memory_size() const;
  void _internal_set_persistent_memory_size(int64_t value);
  public:

  // int64 device_persistent_memory_size = 4 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_device_persistent_memory_size();
  PROTOBUF_DEPRECATED int64_t device_persistent_memory_size() const;
  PROTOBUF_DEPRECATED void set_device_persistent_memory_size(int64_t value);
  private:
  int64_t _internal_device_persistent_memory_size() const;
  void _internal_set_device_persistent_memory_size(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryStats)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > persistent_tensor_alloc_ids_;
    mutable std::atomic<int> _persistent_tensor_alloc_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > device_persistent_tensor_alloc_ids_;
    mutable std::atomic<int> _device_persistent_tensor_alloc_ids_cached_byte_size_;
    int64_t temp_memory_size_;
    int64_t device_temp_memory_size_;
    int64_t persistent_memory_size_;
    int64_t device_persistent_memory_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class NodeExecStats final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NodeExecStats) */ {
 public:
  inline NodeExecStats() : NodeExecStats(nullptr) {}
  ~NodeExecStats() override;
  explicit PROTOBUF_CONSTEXPR NodeExecStats(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NodeExecStats(const NodeExecStats& from);
  NodeExecStats(NodeExecStats&& from) noexcept
    : NodeExecStats() {
    *this = ::std::move(from);
  }

  inline NodeExecStats& operator=(const NodeExecStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeExecStats& operator=(NodeExecStats&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeExecStats& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeExecStats* internal_default_instance() {
    return reinterpret_cast<const NodeExecStats*>(
               &_NodeExecStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(NodeExecStats& a, NodeExecStats& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeExecStats* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeExecStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeExecStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NodeExecStats>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NodeExecStats& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NodeExecStats& from) {
    NodeExecStats::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeExecStats* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NodeExecStats";
  }
  protected:
  explicit NodeExecStats(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMemoryFieldNumber = 6,
    kOutputFieldNumber = 7,
    kReferencedTensorFieldNumber = 11,
    kNodeNameFieldNumber = 1,
    kTimelineLabelFieldNumber = 8,
    kMemoryStatsFieldNumber = 12,
    kAllStartMicrosFieldNumber = 2,
    kOpStartRelMicrosFieldNumber = 3,
    kOpEndRelMicrosFieldNumber = 4,
    kAllEndRelMicrosFieldNumber = 5,
    kScheduledMicrosFieldNumber = 9,
    kAllStartNanosFieldNumber = 13,
    kOpStartRelNanosFieldNumber = 14,
    kOpEndRelNanosFieldNumber = 15,
    kAllEndRelNanosFieldNumber = 16,
    kScheduledNanosFieldNumber = 17,
    kThreadIdFieldNumber = 10,
  };
  // repeated .tensorflow.AllocatorMemoryUsed memory = 6;
  int memory_size() const;
  private:
  int _internal_memory_size() const;
  public:
  void clear_memory();
  ::tensorflow::AllocatorMemoryUsed* mutable_memory(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >*
      mutable_memory();
  private:
  const ::tensorflow::AllocatorMemoryUsed& _internal_memory(int index) const;
  ::tensorflow::AllocatorMemoryUsed* _internal_add_memory();
  public:
  const ::tensorflow::AllocatorMemoryUsed& memory(int index) const;
  ::tensorflow::AllocatorMemoryUsed* add_memory();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >&
      memory() const;

  // repeated .tensorflow.NodeOutput output = 7;
  int output_size() const;
  private:
  int _internal_output_size() const;
  public:
  void clear_output();
  ::tensorflow::NodeOutput* mutable_output(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput >*
      mutable_output();
  private:
  const ::tensorflow::NodeOutput& _internal_output(int index) const;
  ::tensorflow::NodeOutput* _internal_add_output();
  public:
  const ::tensorflow::NodeOutput& output(int index) const;
  ::tensorflow::NodeOutput* add_output();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput >&
      output() const;

  // repeated .tensorflow.AllocationDescription referenced_tensor = 11;
  int referenced_tensor_size() const;
  private:
  int _internal_referenced_tensor_size() const;
  public:
  void clear_referenced_tensor();
  ::tensorflow::AllocationDescription* mutable_referenced_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription >*
      mutable_referenced_tensor();
  private:
  const ::tensorflow::AllocationDescription& _internal_referenced_tensor(int index) const;
  ::tensorflow::AllocationDescription* _internal_add_referenced_tensor();
  public:
  const ::tensorflow::AllocationDescription& referenced_tensor(int index) const;
  ::tensorflow::AllocationDescription* add_referenced_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription >&
      referenced_tensor() const;

  // string node_name = 1;
  void clear_node_name();
  const std::string& node_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_node_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_node_name();
  PROTOBUF_NODISCARD std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  private:
  const std::string& _internal_node_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_node_name(const std::string& value);
  std::string* _internal_mutable_node_name();
  public:

  // string timeline_label = 8;
  void clear_timeline_label();
  const std::string& timeline_label() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_timeline_label(ArgT0&& arg0, ArgT... args);
  std::string* mutable_timeline_label();
  PROTOBUF_NODISCARD std::string* release_timeline_label();
  void set_allocated_timeline_label(std::string* timeline_label);
  private:
  const std::string& _internal_timeline_label() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_timeline_label(const std::string& value);
  std::string* _internal_mutable_timeline_label();
  public:

  // .tensorflow.MemoryStats memory_stats = 12;
  bool has_memory_stats() const;
  private:
  bool _internal_has_memory_stats() const;
  public:
  void clear_memory_stats();
  const ::tensorflow::MemoryStats& memory_stats() const;
  PROTOBUF_NODISCARD ::tensorflow::MemoryStats* release_memory_stats();
  ::tensorflow::MemoryStats* mutable_memory_stats();
  void set_allocated_memory_stats(::tensorflow::MemoryStats* memory_stats);
  private:
  const ::tensorflow::MemoryStats& _internal_memory_stats() const;
  ::tensorflow::MemoryStats* _internal_mutable_memory_stats();
  public:
  void unsafe_arena_set_allocated_memory_stats(
      ::tensorflow::MemoryStats* memory_stats);
  ::tensorflow::MemoryStats* unsafe_arena_release_memory_stats();

  // int64 all_start_micros = 2;
  void clear_all_start_micros();
  int64_t all_start_micros() const;
  void set_all_start_micros(int64_t value);
  private:
  int64_t _internal_all_start_micros() const;
  void _internal_set_all_start_micros(int64_t value);
  public:

  // int64 op_start_rel_micros = 3;
  void clear_op_start_rel_micros();
  int64_t op_start_rel_micros() const;
  void set_op_start_rel_micros(int64_t value);
  private:
  int64_t _internal_op_start_rel_micros() const;
  void _internal_set_op_start_rel_micros(int64_t value);
  public:

  // int64 op_end_rel_micros = 4;
  void clear_op_end_rel_micros();
  int64_t op_end_rel_micros() const;
  void set_op_end_rel_micros(int64_t value);
  private:
  int64_t _internal_op_end_rel_micros() const;
  void _internal_set_op_end_rel_micros(int64_t value);
  public:

  // int64 all_end_rel_micros = 5;
  void clear_all_end_rel_micros();
  int64_t all_end_rel_micros() const;
  void set_all_end_rel_micros(int64_t value);
  private:
  int64_t _internal_all_end_rel_micros() const;
  void _internal_set_all_end_rel_micros(int64_t value);
  public:

  // int64 scheduled_micros = 9;
  void clear_scheduled_micros();
  int64_t scheduled_micros() const;
  void set_scheduled_micros(int64_t value);
  private:
  int64_t _internal_scheduled_micros() const;
  void _internal_set_scheduled_micros(int64_t value);
  public:

  // int64 all_start_nanos = 13;
  void clear_all_start_nanos();
  int64_t all_start_nanos() const;
  void set_all_start_nanos(int64_t value);
  private:
  int64_t _internal_all_start_nanos() const;
  void _internal_set_all_start_nanos(int64_t value);
  public:

  // int64 op_start_rel_nanos = 14;
  void clear_op_start_rel_nanos();
  int64_t op_start_rel_nanos() const;
  void set_op_start_rel_nanos(int64_t value);
  private:
  int64_t _internal_op_start_rel_nanos() const;
  void _internal_set_op_start_rel_nanos(int64_t value);
  public:

  // int64 op_end_rel_nanos = 15;
  void clear_op_end_rel_nanos();
  int64_t op_end_rel_nanos() const;
  void set_op_end_rel_nanos(int64_t value);
  private:
  int64_t _internal_op_end_rel_nanos() const;
  void _internal_set_op_end_rel_nanos(int64_t value);
  public:

  // int64 all_end_rel_nanos = 16;
  void clear_all_end_rel_nanos();
  int64_t all_end_rel_nanos() const;
  void set_all_end_rel_nanos(int64_t value);
  private:
  int64_t _internal_all_end_rel_nanos() const;
  void _internal_set_all_end_rel_nanos(int64_t value);
  public:

  // int64 scheduled_nanos = 17;
  void clear_scheduled_nanos();
  int64_t scheduled_nanos() const;
  void set_scheduled_nanos(int64_t value);
  private:
  int64_t _internal_scheduled_nanos() const;
  void _internal_set_scheduled_nanos(int64_t value);
  public:

  // uint32 thread_id = 10;
  void clear_thread_id();
  uint32_t thread_id() const;
  void set_thread_id(uint32_t value);
  private:
  uint32_t _internal_thread_id() const;
  void _internal_set_thread_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.NodeExecStats)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed > memory_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput > output_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription > referenced_tensor_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr timeline_label_;
    ::tensorflow::MemoryStats* memory_stats_;
    int64_t all_start_micros_;
    int64_t op_start_rel_micros_;
    int64_t op_end_rel_micros_;
    int64_t all_end_rel_micros_;
    int64_t scheduled_micros_;
    int64_t all_start_nanos_;
    int64_t op_start_rel_nanos_;
    int64_t op_end_rel_nanos_;
    int64_t all_end_rel_nanos_;
    int64_t scheduled_nanos_;
    uint32_t thread_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class DeviceStepStats_ThreadNamesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceStepStats_ThreadNamesEntry_DoNotUse, 
    uint32_t, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceStepStats_ThreadNamesEntry_DoNotUse, 
    uint32_t, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  DeviceStepStats_ThreadNamesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR DeviceStepStats_ThreadNamesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit DeviceStepStats_ThreadNamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DeviceStepStats_ThreadNamesEntry_DoNotUse& other);
  static const DeviceStepStats_ThreadNamesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DeviceStepStats_ThreadNamesEntry_DoNotUse*>(&_DeviceStepStats_ThreadNamesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.DeviceStepStats.ThreadNamesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};

// -------------------------------------------------------------------

class DeviceStepStats final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceStepStats) */ {
 public:
  inline DeviceStepStats() : DeviceStepStats(nullptr) {}
  ~DeviceStepStats() override;
  explicit PROTOBUF_CONSTEXPR DeviceStepStats(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceStepStats(const DeviceStepStats& from);
  DeviceStepStats(DeviceStepStats&& from) noexcept
    : DeviceStepStats() {
    *this = ::std::move(from);
  }

  inline DeviceStepStats& operator=(const DeviceStepStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceStepStats& operator=(DeviceStepStats&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceStepStats& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceStepStats* internal_default_instance() {
    return reinterpret_cast<const DeviceStepStats*>(
               &_DeviceStepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DeviceStepStats& a, DeviceStepStats& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceStepStats* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceStepStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceStepStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceStepStats>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceStepStats& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceStepStats& from) {
    DeviceStepStats::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceStepStats* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceStepStats";
  }
  protected:
  explicit DeviceStepStats(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kNodeStatsFieldNumber = 2,
    kThreadNamesFieldNumber = 3,
    kDeviceFieldNumber = 1,
  };
  // repeated .tensorflow.NodeExecStats node_stats = 2;
  int node_stats_size() const;
  private:
  int _internal_node_stats_size() const;
  public:
  void clear_node_stats();
  ::tensorflow::NodeExecStats* mutable_node_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats >*
      mutable_node_stats();
  private:
  const ::tensorflow::NodeExecStats& _internal_node_stats(int index) const;
  ::tensorflow::NodeExecStats* _internal_add_node_stats();
  public:
  const ::tensorflow::NodeExecStats& node_stats(int index) const;
  ::tensorflow::NodeExecStats* add_node_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats >&
      node_stats() const;

  // map<uint32, string> thread_names = 3;
  int thread_names_size() const;
  private:
  int _internal_thread_names_size() const;
  public:
  void clear_thread_names();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, std::string >&
      _internal_thread_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, std::string >*
      _internal_mutable_thread_names();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, std::string >&
      thread_names() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, std::string >*
      mutable_thread_names();

  // string device = 1;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceStepStats)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats > node_stats_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        DeviceStepStats_ThreadNamesEntry_DoNotUse,
        uint32_t, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_UINT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> thread_names_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// -------------------------------------------------------------------

class StepStats final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StepStats) */ {
 public:
  inline StepStats() : StepStats(nullptr) {}
  ~StepStats() override;
  explicit PROTOBUF_CONSTEXPR StepStats(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StepStats(const StepStats& from);
  StepStats(StepStats&& from) noexcept
    : StepStats() {
    *this = ::std::move(from);
  }

  inline StepStats& operator=(const StepStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline StepStats& operator=(StepStats&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StepStats& default_instance() {
    return *internal_default_instance();
  }
  static inline const StepStats* internal_default_instance() {
    return reinterpret_cast<const StepStats*>(
               &_StepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(StepStats& a, StepStats& b) {
    a.Swap(&b);
  }
  inline void Swap(StepStats* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StepStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StepStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StepStats>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StepStats& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StepStats& from) {
    StepStats::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StepStats* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StepStats";
  }
  protected:
  explicit StepStats(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDevStatsFieldNumber = 1,
  };
  // repeated .tensorflow.DeviceStepStats dev_stats = 1;
  int dev_stats_size() const;
  private:
  int _internal_dev_stats_size() const;
  public:
  void clear_dev_stats();
  ::tensorflow::DeviceStepStats* mutable_dev_stats(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats >*
      mutable_dev_stats();
  private:
  const ::tensorflow::DeviceStepStats& _internal_dev_stats(int index) const;
  ::tensorflow::DeviceStepStats* _internal_add_dev_stats();
  public:
  const ::tensorflow::DeviceStepStats& dev_stats(int index) const;
  ::tensorflow::DeviceStepStats* add_dev_stats();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats >&
      dev_stats() const;

  // @@protoc_insertion_point(class_scope:tensorflow.StepStats)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats > dev_stats_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AllocationRecord

// int64 alloc_micros = 1;
inline void AllocationRecord::clear_alloc_micros() {
  _impl_.alloc_micros_ = int64_t{0};
}
inline int64_t AllocationRecord::_internal_alloc_micros() const {
  return _impl_.alloc_micros_;
}
inline int64_t AllocationRecord::alloc_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationRecord.alloc_micros)
  return _internal_alloc_micros();
}
inline void AllocationRecord::_internal_set_alloc_micros(int64_t value) {
  
  _impl_.alloc_micros_ = value;
}
inline void AllocationRecord::set_alloc_micros(int64_t value) {
  _internal_set_alloc_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocationRecord.alloc_micros)
}

// int64 alloc_bytes = 2;
inline void AllocationRecord::clear_alloc_bytes() {
  _impl_.alloc_bytes_ = int64_t{0};
}
inline int64_t AllocationRecord::_internal_alloc_bytes() const {
  return _impl_.alloc_bytes_;
}
inline int64_t AllocationRecord::alloc_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationRecord.alloc_bytes)
  return _internal_alloc_bytes();
}
inline void AllocationRecord::_internal_set_alloc_bytes(int64_t value) {
  
  _impl_.alloc_bytes_ = value;
}
inline void AllocationRecord::set_alloc_bytes(int64_t value) {
  _internal_set_alloc_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocationRecord.alloc_bytes)
}

// -------------------------------------------------------------------

// AllocatorMemoryUsed

// string allocator_name = 1;
inline void AllocatorMemoryUsed::clear_allocator_name() {
  _impl_.allocator_name_.ClearToEmpty();
}
inline const std::string& AllocatorMemoryUsed::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocator_name)
  return _internal_allocator_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AllocatorMemoryUsed::set_allocator_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.allocator_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.allocator_name)
}
inline std::string* AllocatorMemoryUsed::mutable_allocator_name() {
  std::string* _s = _internal_mutable_allocator_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.AllocatorMemoryUsed.allocator_name)
  return _s;
}
inline const std::string& AllocatorMemoryUsed::_internal_allocator_name() const {
  return _impl_.allocator_name_.Get();
}
inline void AllocatorMemoryUsed::_internal_set_allocator_name(const std::string& value) {
  
  _impl_.allocator_name_.Set(value, GetArenaForAllocation());
}
inline std::string* AllocatorMemoryUsed::_internal_mutable_allocator_name() {
  
  return _impl_.allocator_name_.Mutable(GetArenaForAllocation());
}
inline std::string* AllocatorMemoryUsed::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.AllocatorMemoryUsed.allocator_name)
  return _impl_.allocator_name_.Release();
}
inline void AllocatorMemoryUsed::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  _impl_.allocator_name_.SetAllocated(allocator_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.allocator_name_.IsDefault()) {
    _impl_.allocator_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AllocatorMemoryUsed.allocator_name)
}

// int64 total_bytes = 2;
inline void AllocatorMemoryUsed::clear_total_bytes() {
  _impl_.total_bytes_ = int64_t{0};
}
inline int64_t AllocatorMemoryUsed::_internal_total_bytes() const {
  return _impl_.total_bytes_;
}
inline int64_t AllocatorMemoryUsed::total_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.total_bytes)
  return _internal_total_bytes();
}
inline void AllocatorMemoryUsed::_internal_set_total_bytes(int64_t value) {
  
  _impl_.total_bytes_ = value;
}
inline void AllocatorMemoryUsed::set_total_bytes(int64_t value) {
  _internal_set_total_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.total_bytes)
}

// int64 peak_bytes = 3;
inline void AllocatorMemoryUsed::clear_peak_bytes() {
  _impl_.peak_bytes_ = int64_t{0};
}
inline int64_t AllocatorMemoryUsed::_internal_peak_bytes() const {
  return _impl_.peak_bytes_;
}
inline int64_t AllocatorMemoryUsed::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.peak_bytes)
  return _internal_peak_bytes();
}
inline void AllocatorMemoryUsed::_internal_set_peak_bytes(int64_t value) {
  
  _impl_.peak_bytes_ = value;
}
inline void AllocatorMemoryUsed::set_peak_bytes(int64_t value) {
  _internal_set_peak_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.peak_bytes)
}

// int64 live_bytes = 4;
inline void AllocatorMemoryUsed::clear_live_bytes() {
  _impl_.live_bytes_ = int64_t{0};
}
inline int64_t AllocatorMemoryUsed::_internal_live_bytes() const {
  return _impl_.live_bytes_;
}
inline int64_t AllocatorMemoryUsed::live_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.live_bytes)
  return _internal_live_bytes();
}
inline void AllocatorMemoryUsed::_internal_set_live_bytes(int64_t value) {
  
  _impl_.live_bytes_ = value;
}
inline void AllocatorMemoryUsed::set_live_bytes(int64_t value) {
  _internal_set_live_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.live_bytes)
}

// repeated .tensorflow.AllocationRecord allocation_records = 6;
inline int AllocatorMemoryUsed::_internal_allocation_records_size() const {
  return _impl_.allocation_records_.size();
}
inline int AllocatorMemoryUsed::allocation_records_size() const {
  return _internal_allocation_records_size();
}
inline void AllocatorMemoryUsed::clear_allocation_records() {
  _impl_.allocation_records_.Clear();
}
inline ::tensorflow::AllocationRecord* AllocatorMemoryUsed::mutable_allocation_records(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.AllocatorMemoryUsed.allocation_records)
  return _impl_.allocation_records_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >*
AllocatorMemoryUsed::mutable_allocation_records() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.AllocatorMemoryUsed.allocation_records)
  return &_impl_.allocation_records_;
}
inline const ::tensorflow::AllocationRecord& AllocatorMemoryUsed::_internal_allocation_records(int index) const {
  return _impl_.allocation_records_.Get(index);
}
inline const ::tensorflow::AllocationRecord& AllocatorMemoryUsed::allocation_records(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocation_records)
  return _internal_allocation_records(index);
}
inline ::tensorflow::AllocationRecord* AllocatorMemoryUsed::_internal_add_allocation_records() {
  return _impl_.allocation_records_.Add();
}
inline ::tensorflow::AllocationRecord* AllocatorMemoryUsed::add_allocation_records() {
  ::tensorflow::AllocationRecord* _add = _internal_add_allocation_records();
  // @@protoc_insertion_point(field_add:tensorflow.AllocatorMemoryUsed.allocation_records)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >&
AllocatorMemoryUsed::allocation_records() const {
  // @@protoc_insertion_point(field_list:tensorflow.AllocatorMemoryUsed.allocation_records)
  return _impl_.allocation_records_;
}

// int64 allocator_bytes_in_use = 5;
inline void AllocatorMemoryUsed::clear_allocator_bytes_in_use() {
  _impl_.allocator_bytes_in_use_ = int64_t{0};
}
inline int64_t AllocatorMemoryUsed::_internal_allocator_bytes_in_use() const {
  return _impl_.allocator_bytes_in_use_;
}
inline int64_t AllocatorMemoryUsed::allocator_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocatorMemoryUsed.allocator_bytes_in_use)
  return _internal_allocator_bytes_in_use();
}
inline void AllocatorMemoryUsed::_internal_set_allocator_bytes_in_use(int64_t value) {
  
  _impl_.allocator_bytes_in_use_ = value;
}
inline void AllocatorMemoryUsed::set_allocator_bytes_in_use(int64_t value) {
  _internal_set_allocator_bytes_in_use(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocatorMemoryUsed.allocator_bytes_in_use)
}

// -------------------------------------------------------------------

// NodeOutput

// int32 slot = 1;
inline void NodeOutput::clear_slot() {
  _impl_.slot_ = 0;
}
inline int32_t NodeOutput::_internal_slot() const {
  return _impl_.slot_;
}
inline int32_t NodeOutput::slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeOutput.slot)
  return _internal_slot();
}
inline void NodeOutput::_internal_set_slot(int32_t value) {
  
  _impl_.slot_ = value;
}
inline void NodeOutput::set_slot(int32_t value) {
  _internal_set_slot(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeOutput.slot)
}

// .tensorflow.TensorDescription tensor_description = 3;
inline bool NodeOutput::_internal_has_tensor_description() const {
  return this != internal_default_instance() && _impl_.tensor_description_ != nullptr;
}
inline bool NodeOutput::has_tensor_description() const {
  return _internal_has_tensor_description();
}
inline const ::tensorflow::TensorDescription& NodeOutput::_internal_tensor_description() const {
  const ::tensorflow::TensorDescription* p = _impl_.tensor_description_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorDescription&>(
      ::tensorflow::_TensorDescription_default_instance_);
}
inline const ::tensorflow::TensorDescription& NodeOutput::tensor_description() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeOutput.tensor_description)
  return _internal_tensor_description();
}
inline void NodeOutput::unsafe_arena_set_allocated_tensor_description(
    ::tensorflow::TensorDescription* tensor_description) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_description_);
  }
  _impl_.tensor_description_ = tensor_description;
  if (tensor_description) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeOutput.tensor_description)
}
inline ::tensorflow::TensorDescription* NodeOutput::release_tensor_description() {
  
  ::tensorflow::TensorDescription* temp = _impl_.tensor_description_;
  _impl_.tensor_description_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorDescription* NodeOutput::unsafe_arena_release_tensor_description() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeOutput.tensor_description)
  
  ::tensorflow::TensorDescription* temp = _impl_.tensor_description_;
  _impl_.tensor_description_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorDescription* NodeOutput::_internal_mutable_tensor_description() {
  
  if (_impl_.tensor_description_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorDescription>(GetArenaForAllocation());
    _impl_.tensor_description_ = p;
  }
  return _impl_.tensor_description_;
}
inline ::tensorflow::TensorDescription* NodeOutput::mutable_tensor_description() {
  ::tensorflow::TensorDescription* _msg = _internal_mutable_tensor_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeOutput.tensor_description)
  return _msg;
}
inline void NodeOutput::set_allocated_tensor_description(::tensorflow::TensorDescription* tensor_description) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_description_);
  }
  if (tensor_description) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_description));
    if (message_arena != submessage_arena) {
      tensor_description = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_description, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_description_ = tensor_description;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeOutput.tensor_description)
}

// -------------------------------------------------------------------

// MemoryStats

// int64 temp_memory_size = 1;
inline void MemoryStats::clear_temp_memory_size() {
  _impl_.temp_memory_size_ = int64_t{0};
}
inline int64_t MemoryStats::_internal_temp_memory_size() const {
  return _impl_.temp_memory_size_;
}
inline int64_t MemoryStats::temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.temp_memory_size)
  return _internal_temp_memory_size();
}
inline void MemoryStats::_internal_set_temp_memory_size(int64_t value) {
  
  _impl_.temp_memory_size_ = value;
}
inline void MemoryStats::set_temp_memory_size(int64_t value) {
  _internal_set_temp_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.temp_memory_size)
}

// int64 persistent_memory_size = 3;
inline void MemoryStats::clear_persistent_memory_size() {
  _impl_.persistent_memory_size_ = int64_t{0};
}
inline int64_t MemoryStats::_internal_persistent_memory_size() const {
  return _impl_.persistent_memory_size_;
}
inline int64_t MemoryStats::persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.persistent_memory_size)
  return _internal_persistent_memory_size();
}
inline void MemoryStats::_internal_set_persistent_memory_size(int64_t value) {
  
  _impl_.persistent_memory_size_ = value;
}
inline void MemoryStats::set_persistent_memory_size(int64_t value) {
  _internal_set_persistent_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.persistent_memory_size)
}

// repeated int64 persistent_tensor_alloc_ids = 5;
inline int MemoryStats::_internal_persistent_tensor_alloc_ids_size() const {
  return _impl_.persistent_tensor_alloc_ids_.size();
}
inline int MemoryStats::persistent_tensor_alloc_ids_size() const {
  return _internal_persistent_tensor_alloc_ids_size();
}
inline void MemoryStats::clear_persistent_tensor_alloc_ids() {
  _impl_.persistent_tensor_alloc_ids_.Clear();
}
inline int64_t MemoryStats::_internal_persistent_tensor_alloc_ids(int index) const {
  return _impl_.persistent_tensor_alloc_ids_.Get(index);
}
inline int64_t MemoryStats::persistent_tensor_alloc_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return _internal_persistent_tensor_alloc_ids(index);
}
inline void MemoryStats::set_persistent_tensor_alloc_ids(int index, int64_t value) {
  _impl_.persistent_tensor_alloc_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
}
inline void MemoryStats::_internal_add_persistent_tensor_alloc_ids(int64_t value) {
  _impl_.persistent_tensor_alloc_ids_.Add(value);
}
inline void MemoryStats::add_persistent_tensor_alloc_ids(int64_t value) {
  _internal_add_persistent_tensor_alloc_ids(value);
  // @@protoc_insertion_point(field_add:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MemoryStats::_internal_persistent_tensor_alloc_ids() const {
  return _impl_.persistent_tensor_alloc_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MemoryStats::persistent_tensor_alloc_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return _internal_persistent_tensor_alloc_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MemoryStats::_internal_mutable_persistent_tensor_alloc_ids() {
  return &_impl_.persistent_tensor_alloc_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MemoryStats::mutable_persistent_tensor_alloc_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryStats.persistent_tensor_alloc_ids)
  return _internal_mutable_persistent_tensor_alloc_ids();
}

// int64 device_temp_memory_size = 2 [deprecated = true];
inline void MemoryStats::clear_device_temp_memory_size() {
  _impl_.device_temp_memory_size_ = int64_t{0};
}
inline int64_t MemoryStats::_internal_device_temp_memory_size() const {
  return _impl_.device_temp_memory_size_;
}
inline int64_t MemoryStats::device_temp_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_temp_memory_size)
  return _internal_device_temp_memory_size();
}
inline void MemoryStats::_internal_set_device_temp_memory_size(int64_t value) {
  
  _impl_.device_temp_memory_size_ = value;
}
inline void MemoryStats::set_device_temp_memory_size(int64_t value) {
  _internal_set_device_temp_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_temp_memory_size)
}

// int64 device_persistent_memory_size = 4 [deprecated = true];
inline void MemoryStats::clear_device_persistent_memory_size() {
  _impl_.device_persistent_memory_size_ = int64_t{0};
}
inline int64_t MemoryStats::_internal_device_persistent_memory_size() const {
  return _impl_.device_persistent_memory_size_;
}
inline int64_t MemoryStats::device_persistent_memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_persistent_memory_size)
  return _internal_device_persistent_memory_size();
}
inline void MemoryStats::_internal_set_device_persistent_memory_size(int64_t value) {
  
  _impl_.device_persistent_memory_size_ = value;
}
inline void MemoryStats::set_device_persistent_memory_size(int64_t value) {
  _internal_set_device_persistent_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_persistent_memory_size)
}

// repeated int64 device_persistent_tensor_alloc_ids = 6 [deprecated = true];
inline int MemoryStats::_internal_device_persistent_tensor_alloc_ids_size() const {
  return _impl_.device_persistent_tensor_alloc_ids_.size();
}
inline int MemoryStats::device_persistent_tensor_alloc_ids_size() const {
  return _internal_device_persistent_tensor_alloc_ids_size();
}
inline void MemoryStats::clear_device_persistent_tensor_alloc_ids() {
  _impl_.device_persistent_tensor_alloc_ids_.Clear();
}
inline int64_t MemoryStats::_internal_device_persistent_tensor_alloc_ids(int index) const {
  return _impl_.device_persistent_tensor_alloc_ids_.Get(index);
}
inline int64_t MemoryStats::device_persistent_tensor_alloc_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return _internal_device_persistent_tensor_alloc_ids(index);
}
inline void MemoryStats::set_device_persistent_tensor_alloc_ids(int index, int64_t value) {
  _impl_.device_persistent_tensor_alloc_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
}
inline void MemoryStats::_internal_add_device_persistent_tensor_alloc_ids(int64_t value) {
  _impl_.device_persistent_tensor_alloc_ids_.Add(value);
}
inline void MemoryStats::add_device_persistent_tensor_alloc_ids(int64_t value) {
  _internal_add_device_persistent_tensor_alloc_ids(value);
  // @@protoc_insertion_point(field_add:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MemoryStats::_internal_device_persistent_tensor_alloc_ids() const {
  return _impl_.device_persistent_tensor_alloc_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
MemoryStats::device_persistent_tensor_alloc_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return _internal_device_persistent_tensor_alloc_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MemoryStats::_internal_mutable_device_persistent_tensor_alloc_ids() {
  return &_impl_.device_persistent_tensor_alloc_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
MemoryStats::mutable_device_persistent_tensor_alloc_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryStats.device_persistent_tensor_alloc_ids)
  return _internal_mutable_device_persistent_tensor_alloc_ids();
}

// -------------------------------------------------------------------

// NodeExecStats

// string node_name = 1;
inline void NodeExecStats::clear_node_name() {
  _impl_.node_name_.ClearToEmpty();
}
inline const std::string& NodeExecStats::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.node_name)
  return _internal_node_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeExecStats::set_node_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.node_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.node_name)
}
inline std::string* NodeExecStats::mutable_node_name() {
  std::string* _s = _internal_mutable_node_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.node_name)
  return _s;
}
inline const std::string& NodeExecStats::_internal_node_name() const {
  return _impl_.node_name_.Get();
}
inline void NodeExecStats::_internal_set_node_name(const std::string& value) {
  
  _impl_.node_name_.Set(value, GetArenaForAllocation());
}
inline std::string* NodeExecStats::_internal_mutable_node_name() {
  
  return _impl_.node_name_.Mutable(GetArenaForAllocation());
}
inline std::string* NodeExecStats::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.node_name)
  return _impl_.node_name_.Release();
}
inline void NodeExecStats::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  _impl_.node_name_.SetAllocated(node_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.node_name_.IsDefault()) {
    _impl_.node_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.node_name)
}

// int64 all_start_micros = 2;
inline void NodeExecStats::clear_all_start_micros() {
  _impl_.all_start_micros_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_all_start_micros() const {
  return _impl_.all_start_micros_;
}
inline int64_t NodeExecStats::all_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_start_micros)
  return _internal_all_start_micros();
}
inline void NodeExecStats::_internal_set_all_start_micros(int64_t value) {
  
  _impl_.all_start_micros_ = value;
}
inline void NodeExecStats::set_all_start_micros(int64_t value) {
  _internal_set_all_start_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_start_micros)
}

// int64 op_start_rel_micros = 3;
inline void NodeExecStats::clear_op_start_rel_micros() {
  _impl_.op_start_rel_micros_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_op_start_rel_micros() const {
  return _impl_.op_start_rel_micros_;
}
inline int64_t NodeExecStats::op_start_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_start_rel_micros)
  return _internal_op_start_rel_micros();
}
inline void NodeExecStats::_internal_set_op_start_rel_micros(int64_t value) {
  
  _impl_.op_start_rel_micros_ = value;
}
inline void NodeExecStats::set_op_start_rel_micros(int64_t value) {
  _internal_set_op_start_rel_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_start_rel_micros)
}

// int64 op_end_rel_micros = 4;
inline void NodeExecStats::clear_op_end_rel_micros() {
  _impl_.op_end_rel_micros_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_op_end_rel_micros() const {
  return _impl_.op_end_rel_micros_;
}
inline int64_t NodeExecStats::op_end_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_end_rel_micros)
  return _internal_op_end_rel_micros();
}
inline void NodeExecStats::_internal_set_op_end_rel_micros(int64_t value) {
  
  _impl_.op_end_rel_micros_ = value;
}
inline void NodeExecStats::set_op_end_rel_micros(int64_t value) {
  _internal_set_op_end_rel_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_end_rel_micros)
}

// int64 all_end_rel_micros = 5;
inline void NodeExecStats::clear_all_end_rel_micros() {
  _impl_.all_end_rel_micros_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_all_end_rel_micros() const {
  return _impl_.all_end_rel_micros_;
}
inline int64_t NodeExecStats::all_end_rel_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_end_rel_micros)
  return _internal_all_end_rel_micros();
}
inline void NodeExecStats::_internal_set_all_end_rel_micros(int64_t value) {
  
  _impl_.all_end_rel_micros_ = value;
}
inline void NodeExecStats::set_all_end_rel_micros(int64_t value) {
  _internal_set_all_end_rel_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_end_rel_micros)
}

// repeated .tensorflow.AllocatorMemoryUsed memory = 6;
inline int NodeExecStats::_internal_memory_size() const {
  return _impl_.memory_.size();
}
inline int NodeExecStats::memory_size() const {
  return _internal_memory_size();
}
inline void NodeExecStats::clear_memory() {
  _impl_.memory_.Clear();
}
inline ::tensorflow::AllocatorMemoryUsed* NodeExecStats::mutable_memory(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.memory)
  return _impl_.memory_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >*
NodeExecStats::mutable_memory() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.memory)
  return &_impl_.memory_;
}
inline const ::tensorflow::AllocatorMemoryUsed& NodeExecStats::_internal_memory(int index) const {
  return _impl_.memory_.Get(index);
}
inline const ::tensorflow::AllocatorMemoryUsed& NodeExecStats::memory(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.memory)
  return _internal_memory(index);
}
inline ::tensorflow::AllocatorMemoryUsed* NodeExecStats::_internal_add_memory() {
  return _impl_.memory_.Add();
}
inline ::tensorflow::AllocatorMemoryUsed* NodeExecStats::add_memory() {
  ::tensorflow::AllocatorMemoryUsed* _add = _internal_add_memory();
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.memory)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocatorMemoryUsed >&
NodeExecStats::memory() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.memory)
  return _impl_.memory_;
}

// repeated .tensorflow.NodeOutput output = 7;
inline int NodeExecStats::_internal_output_size() const {
  return _impl_.output_.size();
}
inline int NodeExecStats::output_size() const {
  return _internal_output_size();
}
inline void NodeExecStats::clear_output() {
  _impl_.output_.Clear();
}
inline ::tensorflow::NodeOutput* NodeExecStats::mutable_output(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.output)
  return _impl_.output_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput >*
NodeExecStats::mutable_output() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.output)
  return &_impl_.output_;
}
inline const ::tensorflow::NodeOutput& NodeExecStats::_internal_output(int index) const {
  return _impl_.output_.Get(index);
}
inline const ::tensorflow::NodeOutput& NodeExecStats::output(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.output)
  return _internal_output(index);
}
inline ::tensorflow::NodeOutput* NodeExecStats::_internal_add_output() {
  return _impl_.output_.Add();
}
inline ::tensorflow::NodeOutput* NodeExecStats::add_output() {
  ::tensorflow::NodeOutput* _add = _internal_add_output();
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.output)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeOutput >&
NodeExecStats::output() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.output)
  return _impl_.output_;
}

// string timeline_label = 8;
inline void NodeExecStats::clear_timeline_label() {
  _impl_.timeline_label_.ClearToEmpty();
}
inline const std::string& NodeExecStats::timeline_label() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.timeline_label)
  return _internal_timeline_label();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeExecStats::set_timeline_label(ArgT0&& arg0, ArgT... args) {
 
 _impl_.timeline_label_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.timeline_label)
}
inline std::string* NodeExecStats::mutable_timeline_label() {
  std::string* _s = _internal_mutable_timeline_label();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.timeline_label)
  return _s;
}
inline const std::string& NodeExecStats::_internal_timeline_label() const {
  return _impl_.timeline_label_.Get();
}
inline void NodeExecStats::_internal_set_timeline_label(const std::string& value) {
  
  _impl_.timeline_label_.Set(value, GetArenaForAllocation());
}
inline std::string* NodeExecStats::_internal_mutable_timeline_label() {
  
  return _impl_.timeline_label_.Mutable(GetArenaForAllocation());
}
inline std::string* NodeExecStats::release_timeline_label() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.timeline_label)
  return _impl_.timeline_label_.Release();
}
inline void NodeExecStats::set_allocated_timeline_label(std::string* timeline_label) {
  if (timeline_label != nullptr) {
    
  } else {
    
  }
  _impl_.timeline_label_.SetAllocated(timeline_label, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.timeline_label_.IsDefault()) {
    _impl_.timeline_label_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.timeline_label)
}

// int64 scheduled_micros = 9;
inline void NodeExecStats::clear_scheduled_micros() {
  _impl_.scheduled_micros_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_scheduled_micros() const {
  return _impl_.scheduled_micros_;
}
inline int64_t NodeExecStats::scheduled_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.scheduled_micros)
  return _internal_scheduled_micros();
}
inline void NodeExecStats::_internal_set_scheduled_micros(int64_t value) {
  
  _impl_.scheduled_micros_ = value;
}
inline void NodeExecStats::set_scheduled_micros(int64_t value) {
  _internal_set_scheduled_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.scheduled_micros)
}

// uint32 thread_id = 10;
inline void NodeExecStats::clear_thread_id() {
  _impl_.thread_id_ = 0u;
}
inline uint32_t NodeExecStats::_internal_thread_id() const {
  return _impl_.thread_id_;
}
inline uint32_t NodeExecStats::thread_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.thread_id)
  return _internal_thread_id();
}
inline void NodeExecStats::_internal_set_thread_id(uint32_t value) {
  
  _impl_.thread_id_ = value;
}
inline void NodeExecStats::set_thread_id(uint32_t value) {
  _internal_set_thread_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.thread_id)
}

// repeated .tensorflow.AllocationDescription referenced_tensor = 11;
inline int NodeExecStats::_internal_referenced_tensor_size() const {
  return _impl_.referenced_tensor_.size();
}
inline int NodeExecStats::referenced_tensor_size() const {
  return _internal_referenced_tensor_size();
}
inline ::tensorflow::AllocationDescription* NodeExecStats::mutable_referenced_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.referenced_tensor)
  return _impl_.referenced_tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription >*
NodeExecStats::mutable_referenced_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeExecStats.referenced_tensor)
  return &_impl_.referenced_tensor_;
}
inline const ::tensorflow::AllocationDescription& NodeExecStats::_internal_referenced_tensor(int index) const {
  return _impl_.referenced_tensor_.Get(index);
}
inline const ::tensorflow::AllocationDescription& NodeExecStats::referenced_tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.referenced_tensor)
  return _internal_referenced_tensor(index);
}
inline ::tensorflow::AllocationDescription* NodeExecStats::_internal_add_referenced_tensor() {
  return _impl_.referenced_tensor_.Add();
}
inline ::tensorflow::AllocationDescription* NodeExecStats::add_referenced_tensor() {
  ::tensorflow::AllocationDescription* _add = _internal_add_referenced_tensor();
  // @@protoc_insertion_point(field_add:tensorflow.NodeExecStats.referenced_tensor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationDescription >&
NodeExecStats::referenced_tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeExecStats.referenced_tensor)
  return _impl_.referenced_tensor_;
}

// .tensorflow.MemoryStats memory_stats = 12;
inline bool NodeExecStats::_internal_has_memory_stats() const {
  return this != internal_default_instance() && _impl_.memory_stats_ != nullptr;
}
inline bool NodeExecStats::has_memory_stats() const {
  return _internal_has_memory_stats();
}
inline void NodeExecStats::clear_memory_stats() {
  if (GetArenaForAllocation() == nullptr && _impl_.memory_stats_ != nullptr) {
    delete _impl_.memory_stats_;
  }
  _impl_.memory_stats_ = nullptr;
}
inline const ::tensorflow::MemoryStats& NodeExecStats::_internal_memory_stats() const {
  const ::tensorflow::MemoryStats* p = _impl_.memory_stats_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::MemoryStats&>(
      ::tensorflow::_MemoryStats_default_instance_);
}
inline const ::tensorflow::MemoryStats& NodeExecStats::memory_stats() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.memory_stats)
  return _internal_memory_stats();
}
inline void NodeExecStats::unsafe_arena_set_allocated_memory_stats(
    ::tensorflow::MemoryStats* memory_stats) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.memory_stats_);
  }
  _impl_.memory_stats_ = memory_stats;
  if (memory_stats) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeExecStats.memory_stats)
}
inline ::tensorflow::MemoryStats* NodeExecStats::release_memory_stats() {
  
  ::tensorflow::MemoryStats* temp = _impl_.memory_stats_;
  _impl_.memory_stats_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::MemoryStats* NodeExecStats::unsafe_arena_release_memory_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeExecStats.memory_stats)
  
  ::tensorflow::MemoryStats* temp = _impl_.memory_stats_;
  _impl_.memory_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::MemoryStats* NodeExecStats::_internal_mutable_memory_stats() {
  
  if (_impl_.memory_stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MemoryStats>(GetArenaForAllocation());
    _impl_.memory_stats_ = p;
  }
  return _impl_.memory_stats_;
}
inline ::tensorflow::MemoryStats* NodeExecStats::mutable_memory_stats() {
  ::tensorflow::MemoryStats* _msg = _internal_mutable_memory_stats();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeExecStats.memory_stats)
  return _msg;
}
inline void NodeExecStats::set_allocated_memory_stats(::tensorflow::MemoryStats* memory_stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.memory_stats_;
  }
  if (memory_stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(memory_stats);
    if (message_arena != submessage_arena) {
      memory_stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, memory_stats, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.memory_stats_ = memory_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeExecStats.memory_stats)
}

// int64 all_start_nanos = 13;
inline void NodeExecStats::clear_all_start_nanos() {
  _impl_.all_start_nanos_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_all_start_nanos() const {
  return _impl_.all_start_nanos_;
}
inline int64_t NodeExecStats::all_start_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_start_nanos)
  return _internal_all_start_nanos();
}
inline void NodeExecStats::_internal_set_all_start_nanos(int64_t value) {
  
  _impl_.all_start_nanos_ = value;
}
inline void NodeExecStats::set_all_start_nanos(int64_t value) {
  _internal_set_all_start_nanos(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_start_nanos)
}

// int64 op_start_rel_nanos = 14;
inline void NodeExecStats::clear_op_start_rel_nanos() {
  _impl_.op_start_rel_nanos_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_op_start_rel_nanos() const {
  return _impl_.op_start_rel_nanos_;
}
inline int64_t NodeExecStats::op_start_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_start_rel_nanos)
  return _internal_op_start_rel_nanos();
}
inline void NodeExecStats::_internal_set_op_start_rel_nanos(int64_t value) {
  
  _impl_.op_start_rel_nanos_ = value;
}
inline void NodeExecStats::set_op_start_rel_nanos(int64_t value) {
  _internal_set_op_start_rel_nanos(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_start_rel_nanos)
}

// int64 op_end_rel_nanos = 15;
inline void NodeExecStats::clear_op_end_rel_nanos() {
  _impl_.op_end_rel_nanos_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_op_end_rel_nanos() const {
  return _impl_.op_end_rel_nanos_;
}
inline int64_t NodeExecStats::op_end_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.op_end_rel_nanos)
  return _internal_op_end_rel_nanos();
}
inline void NodeExecStats::_internal_set_op_end_rel_nanos(int64_t value) {
  
  _impl_.op_end_rel_nanos_ = value;
}
inline void NodeExecStats::set_op_end_rel_nanos(int64_t value) {
  _internal_set_op_end_rel_nanos(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.op_end_rel_nanos)
}

// int64 all_end_rel_nanos = 16;
inline void NodeExecStats::clear_all_end_rel_nanos() {
  _impl_.all_end_rel_nanos_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_all_end_rel_nanos() const {
  return _impl_.all_end_rel_nanos_;
}
inline int64_t NodeExecStats::all_end_rel_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.all_end_rel_nanos)
  return _internal_all_end_rel_nanos();
}
inline void NodeExecStats::_internal_set_all_end_rel_nanos(int64_t value) {
  
  _impl_.all_end_rel_nanos_ = value;
}
inline void NodeExecStats::set_all_end_rel_nanos(int64_t value) {
  _internal_set_all_end_rel_nanos(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.all_end_rel_nanos)
}

// int64 scheduled_nanos = 17;
inline void NodeExecStats::clear_scheduled_nanos() {
  _impl_.scheduled_nanos_ = int64_t{0};
}
inline int64_t NodeExecStats::_internal_scheduled_nanos() const {
  return _impl_.scheduled_nanos_;
}
inline int64_t NodeExecStats::scheduled_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeExecStats.scheduled_nanos)
  return _internal_scheduled_nanos();
}
inline void NodeExecStats::_internal_set_scheduled_nanos(int64_t value) {
  
  _impl_.scheduled_nanos_ = value;
}
inline void NodeExecStats::set_scheduled_nanos(int64_t value) {
  _internal_set_scheduled_nanos(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeExecStats.scheduled_nanos)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DeviceStepStats

// string device = 1;
inline void DeviceStepStats::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& DeviceStepStats::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceStepStats.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceStepStats::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceStepStats.device)
}
inline std::string* DeviceStepStats::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceStepStats.device)
  return _s;
}
inline const std::string& DeviceStepStats::_internal_device() const {
  return _impl_.device_.Get();
}
inline void DeviceStepStats::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceStepStats::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceStepStats::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceStepStats.device)
  return _impl_.device_.Release();
}
inline void DeviceStepStats::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceStepStats.device)
}

// repeated .tensorflow.NodeExecStats node_stats = 2;
inline int DeviceStepStats::_internal_node_stats_size() const {
  return _impl_.node_stats_.size();
}
inline int DeviceStepStats::node_stats_size() const {
  return _internal_node_stats_size();
}
inline void DeviceStepStats::clear_node_stats() {
  _impl_.node_stats_.Clear();
}
inline ::tensorflow::NodeExecStats* DeviceStepStats::mutable_node_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceStepStats.node_stats)
  return _impl_.node_stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats >*
DeviceStepStats::mutable_node_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DeviceStepStats.node_stats)
  return &_impl_.node_stats_;
}
inline const ::tensorflow::NodeExecStats& DeviceStepStats::_internal_node_stats(int index) const {
  return _impl_.node_stats_.Get(index);
}
inline const ::tensorflow::NodeExecStats& DeviceStepStats::node_stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceStepStats.node_stats)
  return _internal_node_stats(index);
}
inline ::tensorflow::NodeExecStats* DeviceStepStats::_internal_add_node_stats() {
  return _impl_.node_stats_.Add();
}
inline ::tensorflow::NodeExecStats* DeviceStepStats::add_node_stats() {
  ::tensorflow::NodeExecStats* _add = _internal_add_node_stats();
  // @@protoc_insertion_point(field_add:tensorflow.DeviceStepStats.node_stats)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NodeExecStats >&
DeviceStepStats::node_stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.DeviceStepStats.node_stats)
  return _impl_.node_stats_;
}

// map<uint32, string> thread_names = 3;
inline int DeviceStepStats::_internal_thread_names_size() const {
  return _impl_.thread_names_.size();
}
inline int DeviceStepStats::thread_names_size() const {
  return _internal_thread_names_size();
}
inline void DeviceStepStats::clear_thread_names() {
  _impl_.thread_names_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, std::string >&
DeviceStepStats::_internal_thread_names() const {
  return _impl_.thread_names_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, std::string >&
DeviceStepStats::thread_names() const {
  // @@protoc_insertion_point(field_map:tensorflow.DeviceStepStats.thread_names)
  return _internal_thread_names();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, std::string >*
DeviceStepStats::_internal_mutable_thread_names() {
  return _impl_.thread_names_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint32_t, std::string >*
DeviceStepStats::mutable_thread_names() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DeviceStepStats.thread_names)
  return _internal_mutable_thread_names();
}

// -------------------------------------------------------------------

// StepStats

// repeated .tensorflow.DeviceStepStats dev_stats = 1;
inline int StepStats::_internal_dev_stats_size() const {
  return _impl_.dev_stats_.size();
}
inline int StepStats::dev_stats_size() const {
  return _internal_dev_stats_size();
}
inline void StepStats::clear_dev_stats() {
  _impl_.dev_stats_.Clear();
}
inline ::tensorflow::DeviceStepStats* StepStats::mutable_dev_stats(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.StepStats.dev_stats)
  return _impl_.dev_stats_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats >*
StepStats::mutable_dev_stats() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.StepStats.dev_stats)
  return &_impl_.dev_stats_;
}
inline const ::tensorflow::DeviceStepStats& StepStats::_internal_dev_stats(int index) const {
  return _impl_.dev_stats_.Get(index);
}
inline const ::tensorflow::DeviceStepStats& StepStats::dev_stats(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.StepStats.dev_stats)
  return _internal_dev_stats(index);
}
inline ::tensorflow::DeviceStepStats* StepStats::_internal_add_dev_stats() {
  return _impl_.dev_stats_.Add();
}
inline ::tensorflow::DeviceStepStats* StepStats::add_dev_stats() {
  ::tensorflow::DeviceStepStats* _add = _internal_add_dev_stats();
  // @@protoc_insertion_point(field_add:tensorflow.StepStats.dev_stats)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceStepStats >&
StepStats::dev_stats() const {
  // @@protoc_insertion_point(field_list:tensorflow.StepStats.dev_stats)
  return _impl_.dev_stats_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fstep_5fstats_2eproto
