# RNNoise 训练数据生成指南

## 完成的修改

### 1. 代码修改
- ✅ 修改了 `dump_features.c` 中的输出问题，避免shell乱码
- ✅ 注释掉了 `fprintf(stderr, "%d\r", count);` 行

### 2. 创建的文件
- ✅ `compile_windows.bat` - Windows编译脚本
- ✅ `process_data.bat` - 数据处理脚本

## 使用步骤

### 第一步：安装编译器
由于系统没有检测到gcc编译器，您需要安装一个：

**推荐选项：**
1. **MinGW-w64**: https://www.mingw-w64.org/downloads/
2. **MSYS2**: https://www.msys2.org/
3. **Visual Studio Build Tools**

安装后请将编译器路径添加到系统PATH环境变量中。

### 第二步：编译程序
```bash
cd src
compile_windows.bat
```

### 第三步：处理数据
```bash
process_data.bat
```

## 数据文件说明

程序将使用以下文件：
- **语音文件**: `data/clean_PCM_data/000_001.pcm`
- **噪声文件**: `data/wind_PCM_data/000_001.pcm`
- **输出文件**: `src/output.f32`

## 参数说明

- **处理数量**: 500000 (相比原来的50000000减少了100倍)
- **预计时间**: 约5分钟 (相比原来的83分钟大大缩短)
- **输出格式**: 32位浮点数特征文件

## 故障排除

### 编译问题
如果编译失败，请确保：
1. 已正确安装gcc编译器
2. 编译器路径已添加到PATH环境变量
3. 所有源文件都在src目录中

### 运行问题
如果处理失败，请检查：
1. PCM数据文件是否存在
2. 文件权限是否正确
3. 磁盘空间是否充足

## 输出文件

成功运行后，将在src目录生成：
- `output.f32` - 包含500000x87的特征矩阵
- 文件大小约170MB

这个文件可以用于后续的RNNoise神经网络训练。

## 下一步

生成特征文件后，您可以：
1. 使用 `rnn_train.py` 进行神经网络训练
2. 调整训练参数优化模型性能
3. 测试不同的数据组合效果
