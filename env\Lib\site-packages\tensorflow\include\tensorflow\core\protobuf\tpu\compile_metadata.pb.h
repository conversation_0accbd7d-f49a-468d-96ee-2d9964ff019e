// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/compile_metadata.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "xla/xla.pb.h"
#include "xla/xla_data.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/protobuf/tpu/dynamic_padding.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
namespace tensorflow {
namespace tpu {
class TPUCompileMetadataProto;
struct TPUCompileMetadataProtoDefaultTypeInternal;
extern TPUCompileMetadataProtoDefaultTypeInternal _TPUCompileMetadataProto_default_instance_;
class TPUCompileMetadataProto_Arg;
struct TPUCompileMetadataProto_ArgDefaultTypeInternal;
extern TPUCompileMetadataProto_ArgDefaultTypeInternal _TPUCompileMetadataProto_Arg_default_instance_;
class TPUCompileMetadataProto_Retval;
struct TPUCompileMetadataProto_RetvalDefaultTypeInternal;
extern TPUCompileMetadataProto_RetvalDefaultTypeInternal _TPUCompileMetadataProto_Retval_default_instance_;
class TPUCompileOptions;
struct TPUCompileOptionsDefaultTypeInternal;
extern TPUCompileOptionsDefaultTypeInternal _TPUCompileOptions_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tpu::TPUCompileMetadataProto* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUCompileMetadataProto>(Arena*);
template<> ::tensorflow::tpu::TPUCompileMetadataProto_Arg* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUCompileMetadataProto_Arg>(Arena*);
template<> ::tensorflow::tpu::TPUCompileMetadataProto_Retval* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUCompileMetadataProto_Retval>(Arena*);
template<> ::tensorflow::tpu::TPUCompileOptions* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUCompileOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tpu {

enum TPUCompileMetadataProto_Arg_Kind : int {
  TPUCompileMetadataProto_Arg_Kind_INVALID = 0,
  TPUCompileMetadataProto_Arg_Kind_PARAMETER = 1,
  TPUCompileMetadataProto_Arg_Kind_VARIABLE = 2,
  TPUCompileMetadataProto_Arg_Kind_GUARANTEED_CONSTANT = 3,
  TPUCompileMetadataProto_Arg_Kind_TPUCompileMetadataProto_Arg_Kind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TPUCompileMetadataProto_Arg_Kind_TPUCompileMetadataProto_Arg_Kind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TPUCompileMetadataProto_Arg_Kind_IsValid(int value);
constexpr TPUCompileMetadataProto_Arg_Kind TPUCompileMetadataProto_Arg_Kind_Kind_MIN = TPUCompileMetadataProto_Arg_Kind_INVALID;
constexpr TPUCompileMetadataProto_Arg_Kind TPUCompileMetadataProto_Arg_Kind_Kind_MAX = TPUCompileMetadataProto_Arg_Kind_GUARANTEED_CONSTANT;
constexpr int TPUCompileMetadataProto_Arg_Kind_Kind_ARRAYSIZE = TPUCompileMetadataProto_Arg_Kind_Kind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUCompileMetadataProto_Arg_Kind_descriptor();
template<typename T>
inline const std::string& TPUCompileMetadataProto_Arg_Kind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUCompileMetadataProto_Arg_Kind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUCompileMetadataProto_Arg_Kind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUCompileMetadataProto_Arg_Kind_descriptor(), enum_t_value);
}
inline bool TPUCompileMetadataProto_Arg_Kind_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TPUCompileMetadataProto_Arg_Kind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUCompileMetadataProto_Arg_Kind>(
    TPUCompileMetadataProto_Arg_Kind_descriptor(), name, value);
}
enum TPUCompileMetadataProto_Arg_EnableXlaSharding : int {
  TPUCompileMetadataProto_Arg_EnableXlaSharding_DISALLOWED = 0,
  TPUCompileMetadataProto_Arg_EnableXlaSharding_TENTATIVE = 1,
  TPUCompileMetadataProto_Arg_EnableXlaSharding_ALLOWED = 2,
  TPUCompileMetadataProto_Arg_EnableXlaSharding_TPUCompileMetadataProto_Arg_EnableXlaSharding_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TPUCompileMetadataProto_Arg_EnableXlaSharding_TPUCompileMetadataProto_Arg_EnableXlaSharding_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TPUCompileMetadataProto_Arg_EnableXlaSharding_IsValid(int value);
constexpr TPUCompileMetadataProto_Arg_EnableXlaSharding TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MIN = TPUCompileMetadataProto_Arg_EnableXlaSharding_DISALLOWED;
constexpr TPUCompileMetadataProto_Arg_EnableXlaSharding TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MAX = TPUCompileMetadataProto_Arg_EnableXlaSharding_ALLOWED;
constexpr int TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_ARRAYSIZE = TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor();
template<typename T>
inline const std::string& TPUCompileMetadataProto_Arg_EnableXlaSharding_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUCompileMetadataProto_Arg_EnableXlaSharding>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUCompileMetadataProto_Arg_EnableXlaSharding_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor(), enum_t_value);
}
inline bool TPUCompileMetadataProto_Arg_EnableXlaSharding_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TPUCompileMetadataProto_Arg_EnableXlaSharding* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUCompileMetadataProto_Arg_EnableXlaSharding>(
    TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor(), name, value);
}
enum TPUCompileOptions_Precision : int {
  TPUCompileOptions_Precision_DEFAULT = 0,
  TPUCompileOptions_Precision_BFLOAT16 = 1,
  TPUCompileOptions_Precision_FLOAT32 = 2,
  TPUCompileOptions_Precision_TENSOR_FLOAT32 = 3,
  TPUCompileOptions_Precision_TPUCompileOptions_Precision_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TPUCompileOptions_Precision_TPUCompileOptions_Precision_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TPUCompileOptions_Precision_IsValid(int value);
constexpr TPUCompileOptions_Precision TPUCompileOptions_Precision_Precision_MIN = TPUCompileOptions_Precision_DEFAULT;
constexpr TPUCompileOptions_Precision TPUCompileOptions_Precision_Precision_MAX = TPUCompileOptions_Precision_TENSOR_FLOAT32;
constexpr int TPUCompileOptions_Precision_Precision_ARRAYSIZE = TPUCompileOptions_Precision_Precision_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUCompileOptions_Precision_descriptor();
template<typename T>
inline const std::string& TPUCompileOptions_Precision_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUCompileOptions_Precision>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUCompileOptions_Precision_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUCompileOptions_Precision_descriptor(), enum_t_value);
}
inline bool TPUCompileOptions_Precision_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TPUCompileOptions_Precision* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUCompileOptions_Precision>(
    TPUCompileOptions_Precision_descriptor(), name, value);
}
// ===================================================================

class TPUCompileMetadataProto_Arg final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUCompileMetadataProto.Arg) */ {
 public:
  inline TPUCompileMetadataProto_Arg() : TPUCompileMetadataProto_Arg(nullptr) {}
  ~TPUCompileMetadataProto_Arg() override;
  explicit PROTOBUF_CONSTEXPR TPUCompileMetadataProto_Arg(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUCompileMetadataProto_Arg(const TPUCompileMetadataProto_Arg& from);
  TPUCompileMetadataProto_Arg(TPUCompileMetadataProto_Arg&& from) noexcept
    : TPUCompileMetadataProto_Arg() {
    *this = ::std::move(from);
  }

  inline TPUCompileMetadataProto_Arg& operator=(const TPUCompileMetadataProto_Arg& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUCompileMetadataProto_Arg& operator=(TPUCompileMetadataProto_Arg&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUCompileMetadataProto_Arg& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUCompileMetadataProto_Arg* internal_default_instance() {
    return reinterpret_cast<const TPUCompileMetadataProto_Arg*>(
               &_TPUCompileMetadataProto_Arg_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TPUCompileMetadataProto_Arg& a, TPUCompileMetadataProto_Arg& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUCompileMetadataProto_Arg* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUCompileMetadataProto_Arg* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUCompileMetadataProto_Arg* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUCompileMetadataProto_Arg>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPUCompileMetadataProto_Arg& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TPUCompileMetadataProto_Arg& from) {
    TPUCompileMetadataProto_Arg::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUCompileMetadataProto_Arg* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUCompileMetadataProto.Arg";
  }
  protected:
  explicit TPUCompileMetadataProto_Arg(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TPUCompileMetadataProto_Arg_Kind Kind;
  static constexpr Kind INVALID =
    TPUCompileMetadataProto_Arg_Kind_INVALID;
  static constexpr Kind PARAMETER =
    TPUCompileMetadataProto_Arg_Kind_PARAMETER;
  static constexpr Kind VARIABLE =
    TPUCompileMetadataProto_Arg_Kind_VARIABLE;
  static constexpr Kind GUARANTEED_CONSTANT =
    TPUCompileMetadataProto_Arg_Kind_GUARANTEED_CONSTANT;
  static inline bool Kind_IsValid(int value) {
    return TPUCompileMetadataProto_Arg_Kind_IsValid(value);
  }
  static constexpr Kind Kind_MIN =
    TPUCompileMetadataProto_Arg_Kind_Kind_MIN;
  static constexpr Kind Kind_MAX =
    TPUCompileMetadataProto_Arg_Kind_Kind_MAX;
  static constexpr int Kind_ARRAYSIZE =
    TPUCompileMetadataProto_Arg_Kind_Kind_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Kind_descriptor() {
    return TPUCompileMetadataProto_Arg_Kind_descriptor();
  }
  template<typename T>
  static inline const std::string& Kind_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Kind>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Kind_Name.");
    return TPUCompileMetadataProto_Arg_Kind_Name(enum_t_value);
  }
  static inline bool Kind_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Kind* value) {
    return TPUCompileMetadataProto_Arg_Kind_Parse(name, value);
  }

  typedef TPUCompileMetadataProto_Arg_EnableXlaSharding EnableXlaSharding;
  static constexpr EnableXlaSharding DISALLOWED =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_DISALLOWED;
  static constexpr EnableXlaSharding TENTATIVE =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_TENTATIVE;
  static constexpr EnableXlaSharding ALLOWED =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_ALLOWED;
  static inline bool EnableXlaSharding_IsValid(int value) {
    return TPUCompileMetadataProto_Arg_EnableXlaSharding_IsValid(value);
  }
  static constexpr EnableXlaSharding EnableXlaSharding_MIN =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MIN;
  static constexpr EnableXlaSharding EnableXlaSharding_MAX =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_MAX;
  static constexpr int EnableXlaSharding_ARRAYSIZE =
    TPUCompileMetadataProto_Arg_EnableXlaSharding_EnableXlaSharding_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  EnableXlaSharding_descriptor() {
    return TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor();
  }
  template<typename T>
  static inline const std::string& EnableXlaSharding_Name(T enum_t_value) {
    static_assert(::std::is_same<T, EnableXlaSharding>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function EnableXlaSharding_Name.");
    return TPUCompileMetadataProto_Arg_EnableXlaSharding_Name(enum_t_value);
  }
  static inline bool EnableXlaSharding_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      EnableXlaSharding* value) {
    return TPUCompileMetadataProto_Arg_EnableXlaSharding_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kIsBoundedDynamicDimFieldNumber = 12,
    kNameFieldNumber = 10,
    kShapeFieldNumber = 2,
    kShardingFieldNumber = 4,
    kDtypeFieldNumber = 1,
    kKindFieldNumber = 3,
    kEnableXlaShardingFieldNumber = 6,
    kRetvalIndexForShardingFieldNumber = 8,
    kIsSameDataAcrossReplicasFieldNumber = 5,
    kFastMemFieldNumber = 7,
    kUnrestrictedLayoutFieldNumber = 9,
    kRequiresXlaBroadcastFieldNumber = 11,
  };
  // repeated bool is_bounded_dynamic_dim = 12;
  int is_bounded_dynamic_dim_size() const;
  private:
  int _internal_is_bounded_dynamic_dim_size() const;
  public:
  void clear_is_bounded_dynamic_dim();
  private:
  bool _internal_is_bounded_dynamic_dim(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_is_bounded_dynamic_dim() const;
  void _internal_add_is_bounded_dynamic_dim(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_is_bounded_dynamic_dim();
  public:
  bool is_bounded_dynamic_dim(int index) const;
  void set_is_bounded_dynamic_dim(int index, bool value);
  void add_is_bounded_dynamic_dim(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      is_bounded_dynamic_dim() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_is_bounded_dynamic_dim();

  // string name = 10;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .xla.OpSharding sharding = 4;
  bool has_sharding() const;
  private:
  bool _internal_has_sharding() const;
  public:
  void clear_sharding();
  const ::xla::OpSharding& sharding() const;
  PROTOBUF_NODISCARD ::xla::OpSharding* release_sharding();
  ::xla::OpSharding* mutable_sharding();
  void set_allocated_sharding(::xla::OpSharding* sharding);
  private:
  const ::xla::OpSharding& _internal_sharding() const;
  ::xla::OpSharding* _internal_mutable_sharding();
  public:
  void unsafe_arena_set_allocated_sharding(
      ::xla::OpSharding* sharding);
  ::xla::OpSharding* unsafe_arena_release_sharding();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // .tensorflow.tpu.TPUCompileMetadataProto.Arg.Kind kind = 3;
  void clear_kind();
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind kind() const;
  void set_kind(::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind value);
  private:
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind _internal_kind() const;
  void _internal_set_kind(::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind value);
  public:

  // .tensorflow.tpu.TPUCompileMetadataProto.Arg.EnableXlaSharding enable_xla_sharding = 6;
  void clear_enable_xla_sharding();
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding enable_xla_sharding() const;
  void set_enable_xla_sharding(::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding value);
  private:
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding _internal_enable_xla_sharding() const;
  void _internal_set_enable_xla_sharding(::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding value);
  public:

  // int32 retval_index_for_sharding = 8;
  void clear_retval_index_for_sharding();
  int32_t retval_index_for_sharding() const;
  void set_retval_index_for_sharding(int32_t value);
  private:
  int32_t _internal_retval_index_for_sharding() const;
  void _internal_set_retval_index_for_sharding(int32_t value);
  public:

  // bool is_same_data_across_replicas = 5;
  void clear_is_same_data_across_replicas();
  bool is_same_data_across_replicas() const;
  void set_is_same_data_across_replicas(bool value);
  private:
  bool _internal_is_same_data_across_replicas() const;
  void _internal_set_is_same_data_across_replicas(bool value);
  public:

  // bool fast_mem = 7;
  void clear_fast_mem();
  bool fast_mem() const;
  void set_fast_mem(bool value);
  private:
  bool _internal_fast_mem() const;
  void _internal_set_fast_mem(bool value);
  public:

  // bool unrestricted_layout = 9;
  void clear_unrestricted_layout();
  bool unrestricted_layout() const;
  void set_unrestricted_layout(bool value);
  private:
  bool _internal_unrestricted_layout() const;
  void _internal_set_unrestricted_layout(bool value);
  public:

  // bool requires_xla_broadcast = 11;
  void clear_requires_xla_broadcast();
  bool requires_xla_broadcast() const;
  void set_requires_xla_broadcast(bool value);
  private:
  bool _internal_requires_xla_broadcast() const;
  void _internal_set_requires_xla_broadcast(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUCompileMetadataProto.Arg)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > is_bounded_dynamic_dim_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::TensorShapeProto* shape_;
    ::xla::OpSharding* sharding_;
    int dtype_;
    int kind_;
    int enable_xla_sharding_;
    int32_t retval_index_for_sharding_;
    bool is_same_data_across_replicas_;
    bool fast_mem_;
    bool unrestricted_layout_;
    bool requires_xla_broadcast_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class TPUCompileMetadataProto_Retval final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUCompileMetadataProto.Retval) */ {
 public:
  inline TPUCompileMetadataProto_Retval() : TPUCompileMetadataProto_Retval(nullptr) {}
  ~TPUCompileMetadataProto_Retval() override;
  explicit PROTOBUF_CONSTEXPR TPUCompileMetadataProto_Retval(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUCompileMetadataProto_Retval(const TPUCompileMetadataProto_Retval& from);
  TPUCompileMetadataProto_Retval(TPUCompileMetadataProto_Retval&& from) noexcept
    : TPUCompileMetadataProto_Retval() {
    *this = ::std::move(from);
  }

  inline TPUCompileMetadataProto_Retval& operator=(const TPUCompileMetadataProto_Retval& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUCompileMetadataProto_Retval& operator=(TPUCompileMetadataProto_Retval&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUCompileMetadataProto_Retval& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUCompileMetadataProto_Retval* internal_default_instance() {
    return reinterpret_cast<const TPUCompileMetadataProto_Retval*>(
               &_TPUCompileMetadataProto_Retval_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TPUCompileMetadataProto_Retval& a, TPUCompileMetadataProto_Retval& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUCompileMetadataProto_Retval* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUCompileMetadataProto_Retval* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUCompileMetadataProto_Retval* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUCompileMetadataProto_Retval>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPUCompileMetadataProto_Retval& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TPUCompileMetadataProto_Retval& from) {
    TPUCompileMetadataProto_Retval::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUCompileMetadataProto_Retval* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUCompileMetadataProto.Retval";
  }
  protected:
  explicit TPUCompileMetadataProto_Retval(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShardingFieldNumber = 1,
  };
  // .xla.OpSharding sharding = 1;
  bool has_sharding() const;
  private:
  bool _internal_has_sharding() const;
  public:
  void clear_sharding();
  const ::xla::OpSharding& sharding() const;
  PROTOBUF_NODISCARD ::xla::OpSharding* release_sharding();
  ::xla::OpSharding* mutable_sharding();
  void set_allocated_sharding(::xla::OpSharding* sharding);
  private:
  const ::xla::OpSharding& _internal_sharding() const;
  ::xla::OpSharding* _internal_mutable_sharding();
  public:
  void unsafe_arena_set_allocated_sharding(
      ::xla::OpSharding* sharding);
  ::xla::OpSharding* unsafe_arena_release_sharding();

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUCompileMetadataProto.Retval)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::xla::OpSharding* sharding_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class TPUCompileMetadataProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUCompileMetadataProto) */ {
 public:
  inline TPUCompileMetadataProto() : TPUCompileMetadataProto(nullptr) {}
  ~TPUCompileMetadataProto() override;
  explicit PROTOBUF_CONSTEXPR TPUCompileMetadataProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUCompileMetadataProto(const TPUCompileMetadataProto& from);
  TPUCompileMetadataProto(TPUCompileMetadataProto&& from) noexcept
    : TPUCompileMetadataProto() {
    *this = ::std::move(from);
  }

  inline TPUCompileMetadataProto& operator=(const TPUCompileMetadataProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUCompileMetadataProto& operator=(TPUCompileMetadataProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUCompileMetadataProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUCompileMetadataProto* internal_default_instance() {
    return reinterpret_cast<const TPUCompileMetadataProto*>(
               &_TPUCompileMetadataProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TPUCompileMetadataProto& a, TPUCompileMetadataProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUCompileMetadataProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUCompileMetadataProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUCompileMetadataProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUCompileMetadataProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPUCompileMetadataProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TPUCompileMetadataProto& from) {
    TPUCompileMetadataProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUCompileMetadataProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUCompileMetadataProto";
  }
  protected:
  explicit TPUCompileMetadataProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TPUCompileMetadataProto_Arg Arg;
  typedef TPUCompileMetadataProto_Retval Retval;

  // accessors -------------------------------------------------------

  enum : int {
    kArgsFieldNumber = 1,
    kRetvalsFieldNumber = 2,
    kPaddingMapsFieldNumber = 11,
    kAutoSpmdMeshShapeFieldNumber = 19,
    kAutoSpmdMeshIdsFieldNumber = 20,
    kSessionHandleFieldNumber = 9,
    kGuaranteedConstFingerprintFieldNumber = 10,
    kModuleNameFieldNumber = 22,
    kDeviceAssignmentFieldNumber = 8,
    kCompileOptionsFieldNumber = 21,
    kNumReplicasFieldNumber = 3,
    kNumCoresPerReplicaFieldNumber = 4,
    kFunctionLibraryFingerprintFieldNumber = 6,
    kXlaFusionAutotunerThreshFieldNumber = 13,
    kStepMarkerLocationFieldNumber = 12,
    kEnableAutomaticModelParallelismFieldNumber = 14,
    kUseSpmdForXlaPartitioningFieldNumber = 15,
    kUseAutoSpmdForXlaPartitioningFieldNumber = 18,
    kMlirFingerprintFieldNumber = 17,
  };
  // repeated .tensorflow.tpu.TPUCompileMetadataProto.Arg args = 1;
  int args_size() const;
  private:
  int _internal_args_size() const;
  public:
  void clear_args();
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg* mutable_args(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg >*
      mutable_args();
  private:
  const ::tensorflow::tpu::TPUCompileMetadataProto_Arg& _internal_args(int index) const;
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg* _internal_add_args();
  public:
  const ::tensorflow::tpu::TPUCompileMetadataProto_Arg& args(int index) const;
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg* add_args();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg >&
      args() const;

  // repeated .tensorflow.tpu.TPUCompileMetadataProto.Retval retvals = 2;
  int retvals_size() const;
  private:
  int _internal_retvals_size() const;
  public:
  void clear_retvals();
  ::tensorflow::tpu::TPUCompileMetadataProto_Retval* mutable_retvals(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval >*
      mutable_retvals();
  private:
  const ::tensorflow::tpu::TPUCompileMetadataProto_Retval& _internal_retvals(int index) const;
  ::tensorflow::tpu::TPUCompileMetadataProto_Retval* _internal_add_retvals();
  public:
  const ::tensorflow::tpu::TPUCompileMetadataProto_Retval& retvals(int index) const;
  ::tensorflow::tpu::TPUCompileMetadataProto_Retval* add_retvals();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval >&
      retvals() const;

  // repeated .tensorflow.tpu.PaddingMap padding_maps = 11;
  int padding_maps_size() const;
  private:
  int _internal_padding_maps_size() const;
  public:
  void clear_padding_maps();
  ::tensorflow::tpu::PaddingMap* mutable_padding_maps(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap >*
      mutable_padding_maps();
  private:
  const ::tensorflow::tpu::PaddingMap& _internal_padding_maps(int index) const;
  ::tensorflow::tpu::PaddingMap* _internal_add_padding_maps();
  public:
  const ::tensorflow::tpu::PaddingMap& padding_maps(int index) const;
  ::tensorflow::tpu::PaddingMap* add_padding_maps();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap >&
      padding_maps() const;

  // repeated int64 auto_spmd_mesh_shape = 19;
  int auto_spmd_mesh_shape_size() const;
  private:
  int _internal_auto_spmd_mesh_shape_size() const;
  public:
  void clear_auto_spmd_mesh_shape();
  private:
  int64_t _internal_auto_spmd_mesh_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_auto_spmd_mesh_shape() const;
  void _internal_add_auto_spmd_mesh_shape(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_auto_spmd_mesh_shape();
  public:
  int64_t auto_spmd_mesh_shape(int index) const;
  void set_auto_spmd_mesh_shape(int index, int64_t value);
  void add_auto_spmd_mesh_shape(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      auto_spmd_mesh_shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_auto_spmd_mesh_shape();

  // repeated int64 auto_spmd_mesh_ids = 20;
  int auto_spmd_mesh_ids_size() const;
  private:
  int _internal_auto_spmd_mesh_ids_size() const;
  public:
  void clear_auto_spmd_mesh_ids();
  private:
  int64_t _internal_auto_spmd_mesh_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_auto_spmd_mesh_ids() const;
  void _internal_add_auto_spmd_mesh_ids(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_auto_spmd_mesh_ids();
  public:
  int64_t auto_spmd_mesh_ids(int index) const;
  void set_auto_spmd_mesh_ids(int index, int64_t value);
  void add_auto_spmd_mesh_ids(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      auto_spmd_mesh_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_auto_spmd_mesh_ids();

  // string session_handle = 9;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // string guaranteed_const_fingerprint = 10;
  void clear_guaranteed_const_fingerprint();
  const std::string& guaranteed_const_fingerprint() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_guaranteed_const_fingerprint(ArgT0&& arg0, ArgT... args);
  std::string* mutable_guaranteed_const_fingerprint();
  PROTOBUF_NODISCARD std::string* release_guaranteed_const_fingerprint();
  void set_allocated_guaranteed_const_fingerprint(std::string* guaranteed_const_fingerprint);
  private:
  const std::string& _internal_guaranteed_const_fingerprint() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_guaranteed_const_fingerprint(const std::string& value);
  std::string* _internal_mutable_guaranteed_const_fingerprint();
  public:

  // string module_name = 22;
  void clear_module_name();
  const std::string& module_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_module_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_module_name();
  PROTOBUF_NODISCARD std::string* release_module_name();
  void set_allocated_module_name(std::string* module_name);
  private:
  const std::string& _internal_module_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_module_name(const std::string& value);
  std::string* _internal_mutable_module_name();
  public:

  // .xla.DeviceAssignmentProto device_assignment = 8;
  bool has_device_assignment() const;
  private:
  bool _internal_has_device_assignment() const;
  public:
  void clear_device_assignment();
  const ::xla::DeviceAssignmentProto& device_assignment() const;
  PROTOBUF_NODISCARD ::xla::DeviceAssignmentProto* release_device_assignment();
  ::xla::DeviceAssignmentProto* mutable_device_assignment();
  void set_allocated_device_assignment(::xla::DeviceAssignmentProto* device_assignment);
  private:
  const ::xla::DeviceAssignmentProto& _internal_device_assignment() const;
  ::xla::DeviceAssignmentProto* _internal_mutable_device_assignment();
  public:
  void unsafe_arena_set_allocated_device_assignment(
      ::xla::DeviceAssignmentProto* device_assignment);
  ::xla::DeviceAssignmentProto* unsafe_arena_release_device_assignment();

  // .tensorflow.tpu.TPUCompileOptions compile_options = 21;
  bool has_compile_options() const;
  private:
  bool _internal_has_compile_options() const;
  public:
  void clear_compile_options();
  const ::tensorflow::tpu::TPUCompileOptions& compile_options() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::TPUCompileOptions* release_compile_options();
  ::tensorflow::tpu::TPUCompileOptions* mutable_compile_options();
  void set_allocated_compile_options(::tensorflow::tpu::TPUCompileOptions* compile_options);
  private:
  const ::tensorflow::tpu::TPUCompileOptions& _internal_compile_options() const;
  ::tensorflow::tpu::TPUCompileOptions* _internal_mutable_compile_options();
  public:
  void unsafe_arena_set_allocated_compile_options(
      ::tensorflow::tpu::TPUCompileOptions* compile_options);
  ::tensorflow::tpu::TPUCompileOptions* unsafe_arena_release_compile_options();

  // int32 num_replicas = 3;
  void clear_num_replicas();
  int32_t num_replicas() const;
  void set_num_replicas(int32_t value);
  private:
  int32_t _internal_num_replicas() const;
  void _internal_set_num_replicas(int32_t value);
  public:

  // int32 num_cores_per_replica = 4;
  void clear_num_cores_per_replica();
  int32_t num_cores_per_replica() const;
  void set_num_cores_per_replica(int32_t value);
  private:
  int32_t _internal_num_cores_per_replica() const;
  void _internal_set_num_cores_per_replica(int32_t value);
  public:

  // uint64 function_library_fingerprint = 6;
  void clear_function_library_fingerprint();
  uint64_t function_library_fingerprint() const;
  void set_function_library_fingerprint(uint64_t value);
  private:
  uint64_t _internal_function_library_fingerprint() const;
  void _internal_set_function_library_fingerprint(uint64_t value);
  public:

  // int64 xla_fusion_autotuner_thresh = 13;
  void clear_xla_fusion_autotuner_thresh();
  int64_t xla_fusion_autotuner_thresh() const;
  void set_xla_fusion_autotuner_thresh(int64_t value);
  private:
  int64_t _internal_xla_fusion_autotuner_thresh() const;
  void _internal_set_xla_fusion_autotuner_thresh(int64_t value);
  public:

  // .xla.DebugOptions.StepMarkerLocation step_marker_location = 12;
  void clear_step_marker_location();
  ::xla::DebugOptions_StepMarkerLocation step_marker_location() const;
  void set_step_marker_location(::xla::DebugOptions_StepMarkerLocation value);
  private:
  ::xla::DebugOptions_StepMarkerLocation _internal_step_marker_location() const;
  void _internal_set_step_marker_location(::xla::DebugOptions_StepMarkerLocation value);
  public:

  // bool enable_automatic_model_parallelism = 14;
  void clear_enable_automatic_model_parallelism();
  bool enable_automatic_model_parallelism() const;
  void set_enable_automatic_model_parallelism(bool value);
  private:
  bool _internal_enable_automatic_model_parallelism() const;
  void _internal_set_enable_automatic_model_parallelism(bool value);
  public:

  // bool use_spmd_for_xla_partitioning = 15;
  void clear_use_spmd_for_xla_partitioning();
  bool use_spmd_for_xla_partitioning() const;
  void set_use_spmd_for_xla_partitioning(bool value);
  private:
  bool _internal_use_spmd_for_xla_partitioning() const;
  void _internal_set_use_spmd_for_xla_partitioning(bool value);
  public:

  // bool use_auto_spmd_for_xla_partitioning = 18;
  void clear_use_auto_spmd_for_xla_partitioning();
  bool use_auto_spmd_for_xla_partitioning() const;
  void set_use_auto_spmd_for_xla_partitioning(bool value);
  private:
  bool _internal_use_auto_spmd_for_xla_partitioning() const;
  void _internal_set_use_auto_spmd_for_xla_partitioning(bool value);
  public:

  // uint64 mlir_fingerprint = 17;
  void clear_mlir_fingerprint();
  uint64_t mlir_fingerprint() const;
  void set_mlir_fingerprint(uint64_t value);
  private:
  uint64_t _internal_mlir_fingerprint() const;
  void _internal_set_mlir_fingerprint(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUCompileMetadataProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg > args_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval > retvals_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap > padding_maps_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > auto_spmd_mesh_shape_;
    mutable std::atomic<int> _auto_spmd_mesh_shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > auto_spmd_mesh_ids_;
    mutable std::atomic<int> _auto_spmd_mesh_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr guaranteed_const_fingerprint_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr module_name_;
    ::xla::DeviceAssignmentProto* device_assignment_;
    ::tensorflow::tpu::TPUCompileOptions* compile_options_;
    int32_t num_replicas_;
    int32_t num_cores_per_replica_;
    uint64_t function_library_fingerprint_;
    int64_t xla_fusion_autotuner_thresh_;
    int step_marker_location_;
    bool enable_automatic_model_parallelism_;
    bool use_spmd_for_xla_partitioning_;
    bool use_auto_spmd_for_xla_partitioning_;
    uint64_t mlir_fingerprint_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class TPUCompileOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUCompileOptions) */ {
 public:
  inline TPUCompileOptions() : TPUCompileOptions(nullptr) {}
  ~TPUCompileOptions() override;
  explicit PROTOBUF_CONSTEXPR TPUCompileOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUCompileOptions(const TPUCompileOptions& from);
  TPUCompileOptions(TPUCompileOptions&& from) noexcept
    : TPUCompileOptions() {
    *this = ::std::move(from);
  }

  inline TPUCompileOptions& operator=(const TPUCompileOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUCompileOptions& operator=(TPUCompileOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUCompileOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUCompileOptions* internal_default_instance() {
    return reinterpret_cast<const TPUCompileOptions*>(
               &_TPUCompileOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TPUCompileOptions& a, TPUCompileOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUCompileOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUCompileOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUCompileOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUCompileOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPUCompileOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TPUCompileOptions& from) {
    TPUCompileOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUCompileOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUCompileOptions";
  }
  protected:
  explicit TPUCompileOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TPUCompileOptions_Precision Precision;
  static constexpr Precision DEFAULT =
    TPUCompileOptions_Precision_DEFAULT;
  static constexpr Precision BFLOAT16 =
    TPUCompileOptions_Precision_BFLOAT16;
  static constexpr Precision FLOAT32 =
    TPUCompileOptions_Precision_FLOAT32;
  static constexpr Precision TENSOR_FLOAT32 =
    TPUCompileOptions_Precision_TENSOR_FLOAT32;
  static inline bool Precision_IsValid(int value) {
    return TPUCompileOptions_Precision_IsValid(value);
  }
  static constexpr Precision Precision_MIN =
    TPUCompileOptions_Precision_Precision_MIN;
  static constexpr Precision Precision_MAX =
    TPUCompileOptions_Precision_Precision_MAX;
  static constexpr int Precision_ARRAYSIZE =
    TPUCompileOptions_Precision_Precision_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Precision_descriptor() {
    return TPUCompileOptions_Precision_descriptor();
  }
  template<typename T>
  static inline const std::string& Precision_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Precision>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Precision_Name.");
    return TPUCompileOptions_Precision_Name(enum_t_value);
  }
  static inline bool Precision_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Precision* value) {
    return TPUCompileOptions_Precision_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kMatrixUnitOperandPrecisionFieldNumber = 1,
  };
  // .tensorflow.tpu.TPUCompileOptions.Precision matrix_unit_operand_precision = 1;
  void clear_matrix_unit_operand_precision();
  ::tensorflow::tpu::TPUCompileOptions_Precision matrix_unit_operand_precision() const;
  void set_matrix_unit_operand_precision(::tensorflow::tpu::TPUCompileOptions_Precision value);
  private:
  ::tensorflow::tpu::TPUCompileOptions_Precision _internal_matrix_unit_operand_precision() const;
  void _internal_set_matrix_unit_operand_precision(::tensorflow::tpu::TPUCompileOptions_Precision value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUCompileOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int matrix_unit_operand_precision_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TPUCompileMetadataProto_Arg

// .tensorflow.DataType dtype = 1;
inline void TPUCompileMetadataProto_Arg::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType TPUCompileMetadataProto_Arg::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType TPUCompileMetadataProto_Arg::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.dtype)
  return _internal_dtype();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void TPUCompileMetadataProto_Arg::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool TPUCompileMetadataProto_Arg::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool TPUCompileMetadataProto_Arg::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& TPUCompileMetadataProto_Arg::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& TPUCompileMetadataProto_Arg::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
  return _internal_shape();
}
inline void TPUCompileMetadataProto_Arg::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
}
inline ::tensorflow::TensorShapeProto* TPUCompileMetadataProto_Arg::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* TPUCompileMetadataProto_Arg::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TPUCompileMetadataProto_Arg::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* TPUCompileMetadataProto_Arg::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
  return _msg;
}
inline void TPUCompileMetadataProto_Arg::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.shape)
}

// .tensorflow.tpu.TPUCompileMetadataProto.Arg.Kind kind = 3;
inline void TPUCompileMetadataProto_Arg::clear_kind() {
  _impl_.kind_ = 0;
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind TPUCompileMetadataProto_Arg::_internal_kind() const {
  return static_cast< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind >(_impl_.kind_);
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind TPUCompileMetadataProto_Arg::kind() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.kind)
  return _internal_kind();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_kind(::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind value) {
  
  _impl_.kind_ = value;
}
inline void TPUCompileMetadataProto_Arg::set_kind(::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind value) {
  _internal_set_kind(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.kind)
}

// .xla.OpSharding sharding = 4;
inline bool TPUCompileMetadataProto_Arg::_internal_has_sharding() const {
  return this != internal_default_instance() && _impl_.sharding_ != nullptr;
}
inline bool TPUCompileMetadataProto_Arg::has_sharding() const {
  return _internal_has_sharding();
}
inline const ::xla::OpSharding& TPUCompileMetadataProto_Arg::_internal_sharding() const {
  const ::xla::OpSharding* p = _impl_.sharding_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::OpSharding&>(
      ::xla::_OpSharding_default_instance_);
}
inline const ::xla::OpSharding& TPUCompileMetadataProto_Arg::sharding() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
  return _internal_sharding();
}
inline void TPUCompileMetadataProto_Arg::unsafe_arena_set_allocated_sharding(
    ::xla::OpSharding* sharding) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.sharding_);
  }
  _impl_.sharding_ = sharding;
  if (sharding) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Arg::release_sharding() {
  
  ::xla::OpSharding* temp = _impl_.sharding_;
  _impl_.sharding_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Arg::unsafe_arena_release_sharding() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
  
  ::xla::OpSharding* temp = _impl_.sharding_;
  _impl_.sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Arg::_internal_mutable_sharding() {
  
  if (_impl_.sharding_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::OpSharding>(GetArenaForAllocation());
    _impl_.sharding_ = p;
  }
  return _impl_.sharding_;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Arg::mutable_sharding() {
  ::xla::OpSharding* _msg = _internal_mutable_sharding();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
  return _msg;
}
inline void TPUCompileMetadataProto_Arg::set_allocated_sharding(::xla::OpSharding* sharding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.sharding_);
  }
  if (sharding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(sharding));
    if (message_arena != submessage_arena) {
      sharding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sharding, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.sharding_ = sharding;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.sharding)
}

// bool is_same_data_across_replicas = 5;
inline void TPUCompileMetadataProto_Arg::clear_is_same_data_across_replicas() {
  _impl_.is_same_data_across_replicas_ = false;
}
inline bool TPUCompileMetadataProto_Arg::_internal_is_same_data_across_replicas() const {
  return _impl_.is_same_data_across_replicas_;
}
inline bool TPUCompileMetadataProto_Arg::is_same_data_across_replicas() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_same_data_across_replicas)
  return _internal_is_same_data_across_replicas();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_is_same_data_across_replicas(bool value) {
  
  _impl_.is_same_data_across_replicas_ = value;
}
inline void TPUCompileMetadataProto_Arg::set_is_same_data_across_replicas(bool value) {
  _internal_set_is_same_data_across_replicas(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_same_data_across_replicas)
}

// .tensorflow.tpu.TPUCompileMetadataProto.Arg.EnableXlaSharding enable_xla_sharding = 6;
inline void TPUCompileMetadataProto_Arg::clear_enable_xla_sharding() {
  _impl_.enable_xla_sharding_ = 0;
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding TPUCompileMetadataProto_Arg::_internal_enable_xla_sharding() const {
  return static_cast< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding >(_impl_.enable_xla_sharding_);
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding TPUCompileMetadataProto_Arg::enable_xla_sharding() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.enable_xla_sharding)
  return _internal_enable_xla_sharding();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_enable_xla_sharding(::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding value) {
  
  _impl_.enable_xla_sharding_ = value;
}
inline void TPUCompileMetadataProto_Arg::set_enable_xla_sharding(::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding value) {
  _internal_set_enable_xla_sharding(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.enable_xla_sharding)
}

// int32 retval_index_for_sharding = 8;
inline void TPUCompileMetadataProto_Arg::clear_retval_index_for_sharding() {
  _impl_.retval_index_for_sharding_ = 0;
}
inline int32_t TPUCompileMetadataProto_Arg::_internal_retval_index_for_sharding() const {
  return _impl_.retval_index_for_sharding_;
}
inline int32_t TPUCompileMetadataProto_Arg::retval_index_for_sharding() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.retval_index_for_sharding)
  return _internal_retval_index_for_sharding();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_retval_index_for_sharding(int32_t value) {
  
  _impl_.retval_index_for_sharding_ = value;
}
inline void TPUCompileMetadataProto_Arg::set_retval_index_for_sharding(int32_t value) {
  _internal_set_retval_index_for_sharding(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.retval_index_for_sharding)
}

// bool fast_mem = 7;
inline void TPUCompileMetadataProto_Arg::clear_fast_mem() {
  _impl_.fast_mem_ = false;
}
inline bool TPUCompileMetadataProto_Arg::_internal_fast_mem() const {
  return _impl_.fast_mem_;
}
inline bool TPUCompileMetadataProto_Arg::fast_mem() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.fast_mem)
  return _internal_fast_mem();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_fast_mem(bool value) {
  
  _impl_.fast_mem_ = value;
}
inline void TPUCompileMetadataProto_Arg::set_fast_mem(bool value) {
  _internal_set_fast_mem(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.fast_mem)
}

// bool unrestricted_layout = 9;
inline void TPUCompileMetadataProto_Arg::clear_unrestricted_layout() {
  _impl_.unrestricted_layout_ = false;
}
inline bool TPUCompileMetadataProto_Arg::_internal_unrestricted_layout() const {
  return _impl_.unrestricted_layout_;
}
inline bool TPUCompileMetadataProto_Arg::unrestricted_layout() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.unrestricted_layout)
  return _internal_unrestricted_layout();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_unrestricted_layout(bool value) {
  
  _impl_.unrestricted_layout_ = value;
}
inline void TPUCompileMetadataProto_Arg::set_unrestricted_layout(bool value) {
  _internal_set_unrestricted_layout(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.unrestricted_layout)
}

// string name = 10;
inline void TPUCompileMetadataProto_Arg::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& TPUCompileMetadataProto_Arg::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TPUCompileMetadataProto_Arg::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
}
inline std::string* TPUCompileMetadataProto_Arg::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
  return _s;
}
inline const std::string& TPUCompileMetadataProto_Arg::_internal_name() const {
  return _impl_.name_.Get();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* TPUCompileMetadataProto_Arg::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* TPUCompileMetadataProto_Arg::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
  return _impl_.name_.Release();
}
inline void TPUCompileMetadataProto_Arg::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Arg.name)
}

// bool requires_xla_broadcast = 11;
inline void TPUCompileMetadataProto_Arg::clear_requires_xla_broadcast() {
  _impl_.requires_xla_broadcast_ = false;
}
inline bool TPUCompileMetadataProto_Arg::_internal_requires_xla_broadcast() const {
  return _impl_.requires_xla_broadcast_;
}
inline bool TPUCompileMetadataProto_Arg::requires_xla_broadcast() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.requires_xla_broadcast)
  return _internal_requires_xla_broadcast();
}
inline void TPUCompileMetadataProto_Arg::_internal_set_requires_xla_broadcast(bool value) {
  
  _impl_.requires_xla_broadcast_ = value;
}
inline void TPUCompileMetadataProto_Arg::set_requires_xla_broadcast(bool value) {
  _internal_set_requires_xla_broadcast(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.requires_xla_broadcast)
}

// repeated bool is_bounded_dynamic_dim = 12;
inline int TPUCompileMetadataProto_Arg::_internal_is_bounded_dynamic_dim_size() const {
  return _impl_.is_bounded_dynamic_dim_.size();
}
inline int TPUCompileMetadataProto_Arg::is_bounded_dynamic_dim_size() const {
  return _internal_is_bounded_dynamic_dim_size();
}
inline void TPUCompileMetadataProto_Arg::clear_is_bounded_dynamic_dim() {
  _impl_.is_bounded_dynamic_dim_.Clear();
}
inline bool TPUCompileMetadataProto_Arg::_internal_is_bounded_dynamic_dim(int index) const {
  return _impl_.is_bounded_dynamic_dim_.Get(index);
}
inline bool TPUCompileMetadataProto_Arg::is_bounded_dynamic_dim(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_bounded_dynamic_dim)
  return _internal_is_bounded_dynamic_dim(index);
}
inline void TPUCompileMetadataProto_Arg::set_is_bounded_dynamic_dim(int index, bool value) {
  _impl_.is_bounded_dynamic_dim_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_bounded_dynamic_dim)
}
inline void TPUCompileMetadataProto_Arg::_internal_add_is_bounded_dynamic_dim(bool value) {
  _impl_.is_bounded_dynamic_dim_.Add(value);
}
inline void TPUCompileMetadataProto_Arg::add_is_bounded_dynamic_dim(bool value) {
  _internal_add_is_bounded_dynamic_dim(value);
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_bounded_dynamic_dim)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
TPUCompileMetadataProto_Arg::_internal_is_bounded_dynamic_dim() const {
  return _impl_.is_bounded_dynamic_dim_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
TPUCompileMetadataProto_Arg::is_bounded_dynamic_dim() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_bounded_dynamic_dim)
  return _internal_is_bounded_dynamic_dim();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
TPUCompileMetadataProto_Arg::_internal_mutable_is_bounded_dynamic_dim() {
  return &_impl_.is_bounded_dynamic_dim_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
TPUCompileMetadataProto_Arg::mutable_is_bounded_dynamic_dim() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.Arg.is_bounded_dynamic_dim)
  return _internal_mutable_is_bounded_dynamic_dim();
}

// -------------------------------------------------------------------

// TPUCompileMetadataProto_Retval

// .xla.OpSharding sharding = 1;
inline bool TPUCompileMetadataProto_Retval::_internal_has_sharding() const {
  return this != internal_default_instance() && _impl_.sharding_ != nullptr;
}
inline bool TPUCompileMetadataProto_Retval::has_sharding() const {
  return _internal_has_sharding();
}
inline const ::xla::OpSharding& TPUCompileMetadataProto_Retval::_internal_sharding() const {
  const ::xla::OpSharding* p = _impl_.sharding_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::OpSharding&>(
      ::xla::_OpSharding_default_instance_);
}
inline const ::xla::OpSharding& TPUCompileMetadataProto_Retval::sharding() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
  return _internal_sharding();
}
inline void TPUCompileMetadataProto_Retval::unsafe_arena_set_allocated_sharding(
    ::xla::OpSharding* sharding) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.sharding_);
  }
  _impl_.sharding_ = sharding;
  if (sharding) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Retval::release_sharding() {
  
  ::xla::OpSharding* temp = _impl_.sharding_;
  _impl_.sharding_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Retval::unsafe_arena_release_sharding() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
  
  ::xla::OpSharding* temp = _impl_.sharding_;
  _impl_.sharding_ = nullptr;
  return temp;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Retval::_internal_mutable_sharding() {
  
  if (_impl_.sharding_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::OpSharding>(GetArenaForAllocation());
    _impl_.sharding_ = p;
  }
  return _impl_.sharding_;
}
inline ::xla::OpSharding* TPUCompileMetadataProto_Retval::mutable_sharding() {
  ::xla::OpSharding* _msg = _internal_mutable_sharding();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
  return _msg;
}
inline void TPUCompileMetadataProto_Retval::set_allocated_sharding(::xla::OpSharding* sharding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.sharding_);
  }
  if (sharding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(sharding));
    if (message_arena != submessage_arena) {
      sharding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sharding, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.sharding_ = sharding;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.Retval.sharding)
}

// -------------------------------------------------------------------

// TPUCompileMetadataProto

// repeated .tensorflow.tpu.TPUCompileMetadataProto.Arg args = 1;
inline int TPUCompileMetadataProto::_internal_args_size() const {
  return _impl_.args_.size();
}
inline int TPUCompileMetadataProto::args_size() const {
  return _internal_args_size();
}
inline void TPUCompileMetadataProto::clear_args() {
  _impl_.args_.Clear();
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg* TPUCompileMetadataProto::mutable_args(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.args)
  return _impl_.args_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg >*
TPUCompileMetadataProto::mutable_args() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.args)
  return &_impl_.args_;
}
inline const ::tensorflow::tpu::TPUCompileMetadataProto_Arg& TPUCompileMetadataProto::_internal_args(int index) const {
  return _impl_.args_.Get(index);
}
inline const ::tensorflow::tpu::TPUCompileMetadataProto_Arg& TPUCompileMetadataProto::args(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.args)
  return _internal_args(index);
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg* TPUCompileMetadataProto::_internal_add_args() {
  return _impl_.args_.Add();
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Arg* TPUCompileMetadataProto::add_args() {
  ::tensorflow::tpu::TPUCompileMetadataProto_Arg* _add = _internal_add_args();
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.args)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Arg >&
TPUCompileMetadataProto::args() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.args)
  return _impl_.args_;
}

// repeated .tensorflow.tpu.TPUCompileMetadataProto.Retval retvals = 2;
inline int TPUCompileMetadataProto::_internal_retvals_size() const {
  return _impl_.retvals_.size();
}
inline int TPUCompileMetadataProto::retvals_size() const {
  return _internal_retvals_size();
}
inline void TPUCompileMetadataProto::clear_retvals() {
  _impl_.retvals_.Clear();
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Retval* TPUCompileMetadataProto::mutable_retvals(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return _impl_.retvals_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval >*
TPUCompileMetadataProto::mutable_retvals() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return &_impl_.retvals_;
}
inline const ::tensorflow::tpu::TPUCompileMetadataProto_Retval& TPUCompileMetadataProto::_internal_retvals(int index) const {
  return _impl_.retvals_.Get(index);
}
inline const ::tensorflow::tpu::TPUCompileMetadataProto_Retval& TPUCompileMetadataProto::retvals(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return _internal_retvals(index);
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Retval* TPUCompileMetadataProto::_internal_add_retvals() {
  return _impl_.retvals_.Add();
}
inline ::tensorflow::tpu::TPUCompileMetadataProto_Retval* TPUCompileMetadataProto::add_retvals() {
  ::tensorflow::tpu::TPUCompileMetadataProto_Retval* _add = _internal_add_retvals();
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUCompileMetadataProto_Retval >&
TPUCompileMetadataProto::retvals() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.retvals)
  return _impl_.retvals_;
}

// int32 num_replicas = 3;
inline void TPUCompileMetadataProto::clear_num_replicas() {
  _impl_.num_replicas_ = 0;
}
inline int32_t TPUCompileMetadataProto::_internal_num_replicas() const {
  return _impl_.num_replicas_;
}
inline int32_t TPUCompileMetadataProto::num_replicas() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.num_replicas)
  return _internal_num_replicas();
}
inline void TPUCompileMetadataProto::_internal_set_num_replicas(int32_t value) {
  
  _impl_.num_replicas_ = value;
}
inline void TPUCompileMetadataProto::set_num_replicas(int32_t value) {
  _internal_set_num_replicas(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.num_replicas)
}

// int32 num_cores_per_replica = 4;
inline void TPUCompileMetadataProto::clear_num_cores_per_replica() {
  _impl_.num_cores_per_replica_ = 0;
}
inline int32_t TPUCompileMetadataProto::_internal_num_cores_per_replica() const {
  return _impl_.num_cores_per_replica_;
}
inline int32_t TPUCompileMetadataProto::num_cores_per_replica() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.num_cores_per_replica)
  return _internal_num_cores_per_replica();
}
inline void TPUCompileMetadataProto::_internal_set_num_cores_per_replica(int32_t value) {
  
  _impl_.num_cores_per_replica_ = value;
}
inline void TPUCompileMetadataProto::set_num_cores_per_replica(int32_t value) {
  _internal_set_num_cores_per_replica(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.num_cores_per_replica)
}

// .xla.DeviceAssignmentProto device_assignment = 8;
inline bool TPUCompileMetadataProto::_internal_has_device_assignment() const {
  return this != internal_default_instance() && _impl_.device_assignment_ != nullptr;
}
inline bool TPUCompileMetadataProto::has_device_assignment() const {
  return _internal_has_device_assignment();
}
inline const ::xla::DeviceAssignmentProto& TPUCompileMetadataProto::_internal_device_assignment() const {
  const ::xla::DeviceAssignmentProto* p = _impl_.device_assignment_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::DeviceAssignmentProto&>(
      ::xla::_DeviceAssignmentProto_default_instance_);
}
inline const ::xla::DeviceAssignmentProto& TPUCompileMetadataProto::device_assignment() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
  return _internal_device_assignment();
}
inline void TPUCompileMetadataProto::unsafe_arena_set_allocated_device_assignment(
    ::xla::DeviceAssignmentProto* device_assignment) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_assignment_);
  }
  _impl_.device_assignment_ = device_assignment;
  if (device_assignment) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
}
inline ::xla::DeviceAssignmentProto* TPUCompileMetadataProto::release_device_assignment() {
  
  ::xla::DeviceAssignmentProto* temp = _impl_.device_assignment_;
  _impl_.device_assignment_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::DeviceAssignmentProto* TPUCompileMetadataProto::unsafe_arena_release_device_assignment() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
  
  ::xla::DeviceAssignmentProto* temp = _impl_.device_assignment_;
  _impl_.device_assignment_ = nullptr;
  return temp;
}
inline ::xla::DeviceAssignmentProto* TPUCompileMetadataProto::_internal_mutable_device_assignment() {
  
  if (_impl_.device_assignment_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DeviceAssignmentProto>(GetArenaForAllocation());
    _impl_.device_assignment_ = p;
  }
  return _impl_.device_assignment_;
}
inline ::xla::DeviceAssignmentProto* TPUCompileMetadataProto::mutable_device_assignment() {
  ::xla::DeviceAssignmentProto* _msg = _internal_mutable_device_assignment();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
  return _msg;
}
inline void TPUCompileMetadataProto::set_allocated_device_assignment(::xla::DeviceAssignmentProto* device_assignment) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_assignment_);
  }
  if (device_assignment) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_assignment));
    if (message_arena != submessage_arena) {
      device_assignment = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_assignment, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.device_assignment_ = device_assignment;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.device_assignment)
}

// uint64 function_library_fingerprint = 6;
inline void TPUCompileMetadataProto::clear_function_library_fingerprint() {
  _impl_.function_library_fingerprint_ = uint64_t{0u};
}
inline uint64_t TPUCompileMetadataProto::_internal_function_library_fingerprint() const {
  return _impl_.function_library_fingerprint_;
}
inline uint64_t TPUCompileMetadataProto::function_library_fingerprint() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.function_library_fingerprint)
  return _internal_function_library_fingerprint();
}
inline void TPUCompileMetadataProto::_internal_set_function_library_fingerprint(uint64_t value) {
  
  _impl_.function_library_fingerprint_ = value;
}
inline void TPUCompileMetadataProto::set_function_library_fingerprint(uint64_t value) {
  _internal_set_function_library_fingerprint(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.function_library_fingerprint)
}

// string session_handle = 9;
inline void TPUCompileMetadataProto::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& TPUCompileMetadataProto::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TPUCompileMetadataProto::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
}
inline std::string* TPUCompileMetadataProto::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
  return _s;
}
inline const std::string& TPUCompileMetadataProto::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void TPUCompileMetadataProto::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* TPUCompileMetadataProto::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* TPUCompileMetadataProto::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
  return _impl_.session_handle_.Release();
}
inline void TPUCompileMetadataProto::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.session_handle)
}

// string guaranteed_const_fingerprint = 10;
inline void TPUCompileMetadataProto::clear_guaranteed_const_fingerprint() {
  _impl_.guaranteed_const_fingerprint_.ClearToEmpty();
}
inline const std::string& TPUCompileMetadataProto::guaranteed_const_fingerprint() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
  return _internal_guaranteed_const_fingerprint();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TPUCompileMetadataProto::set_guaranteed_const_fingerprint(ArgT0&& arg0, ArgT... args) {
 
 _impl_.guaranteed_const_fingerprint_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
}
inline std::string* TPUCompileMetadataProto::mutable_guaranteed_const_fingerprint() {
  std::string* _s = _internal_mutable_guaranteed_const_fingerprint();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
  return _s;
}
inline const std::string& TPUCompileMetadataProto::_internal_guaranteed_const_fingerprint() const {
  return _impl_.guaranteed_const_fingerprint_.Get();
}
inline void TPUCompileMetadataProto::_internal_set_guaranteed_const_fingerprint(const std::string& value) {
  
  _impl_.guaranteed_const_fingerprint_.Set(value, GetArenaForAllocation());
}
inline std::string* TPUCompileMetadataProto::_internal_mutable_guaranteed_const_fingerprint() {
  
  return _impl_.guaranteed_const_fingerprint_.Mutable(GetArenaForAllocation());
}
inline std::string* TPUCompileMetadataProto::release_guaranteed_const_fingerprint() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
  return _impl_.guaranteed_const_fingerprint_.Release();
}
inline void TPUCompileMetadataProto::set_allocated_guaranteed_const_fingerprint(std::string* guaranteed_const_fingerprint) {
  if (guaranteed_const_fingerprint != nullptr) {
    
  } else {
    
  }
  _impl_.guaranteed_const_fingerprint_.SetAllocated(guaranteed_const_fingerprint, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.guaranteed_const_fingerprint_.IsDefault()) {
    _impl_.guaranteed_const_fingerprint_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.guaranteed_const_fingerprint)
}

// repeated .tensorflow.tpu.PaddingMap padding_maps = 11;
inline int TPUCompileMetadataProto::_internal_padding_maps_size() const {
  return _impl_.padding_maps_.size();
}
inline int TPUCompileMetadataProto::padding_maps_size() const {
  return _internal_padding_maps_size();
}
inline ::tensorflow::tpu::PaddingMap* TPUCompileMetadataProto::mutable_padding_maps(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return _impl_.padding_maps_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap >*
TPUCompileMetadataProto::mutable_padding_maps() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return &_impl_.padding_maps_;
}
inline const ::tensorflow::tpu::PaddingMap& TPUCompileMetadataProto::_internal_padding_maps(int index) const {
  return _impl_.padding_maps_.Get(index);
}
inline const ::tensorflow::tpu::PaddingMap& TPUCompileMetadataProto::padding_maps(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return _internal_padding_maps(index);
}
inline ::tensorflow::tpu::PaddingMap* TPUCompileMetadataProto::_internal_add_padding_maps() {
  return _impl_.padding_maps_.Add();
}
inline ::tensorflow::tpu::PaddingMap* TPUCompileMetadataProto::add_padding_maps() {
  ::tensorflow::tpu::PaddingMap* _add = _internal_add_padding_maps();
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::PaddingMap >&
TPUCompileMetadataProto::padding_maps() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.padding_maps)
  return _impl_.padding_maps_;
}

// .xla.DebugOptions.StepMarkerLocation step_marker_location = 12;
inline void TPUCompileMetadataProto::clear_step_marker_location() {
  _impl_.step_marker_location_ = 0;
}
inline ::xla::DebugOptions_StepMarkerLocation TPUCompileMetadataProto::_internal_step_marker_location() const {
  return static_cast< ::xla::DebugOptions_StepMarkerLocation >(_impl_.step_marker_location_);
}
inline ::xla::DebugOptions_StepMarkerLocation TPUCompileMetadataProto::step_marker_location() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.step_marker_location)
  return _internal_step_marker_location();
}
inline void TPUCompileMetadataProto::_internal_set_step_marker_location(::xla::DebugOptions_StepMarkerLocation value) {
  
  _impl_.step_marker_location_ = value;
}
inline void TPUCompileMetadataProto::set_step_marker_location(::xla::DebugOptions_StepMarkerLocation value) {
  _internal_set_step_marker_location(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.step_marker_location)
}

// int64 xla_fusion_autotuner_thresh = 13;
inline void TPUCompileMetadataProto::clear_xla_fusion_autotuner_thresh() {
  _impl_.xla_fusion_autotuner_thresh_ = int64_t{0};
}
inline int64_t TPUCompileMetadataProto::_internal_xla_fusion_autotuner_thresh() const {
  return _impl_.xla_fusion_autotuner_thresh_;
}
inline int64_t TPUCompileMetadataProto::xla_fusion_autotuner_thresh() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.xla_fusion_autotuner_thresh)
  return _internal_xla_fusion_autotuner_thresh();
}
inline void TPUCompileMetadataProto::_internal_set_xla_fusion_autotuner_thresh(int64_t value) {
  
  _impl_.xla_fusion_autotuner_thresh_ = value;
}
inline void TPUCompileMetadataProto::set_xla_fusion_autotuner_thresh(int64_t value) {
  _internal_set_xla_fusion_autotuner_thresh(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.xla_fusion_autotuner_thresh)
}

// bool enable_automatic_model_parallelism = 14;
inline void TPUCompileMetadataProto::clear_enable_automatic_model_parallelism() {
  _impl_.enable_automatic_model_parallelism_ = false;
}
inline bool TPUCompileMetadataProto::_internal_enable_automatic_model_parallelism() const {
  return _impl_.enable_automatic_model_parallelism_;
}
inline bool TPUCompileMetadataProto::enable_automatic_model_parallelism() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.enable_automatic_model_parallelism)
  return _internal_enable_automatic_model_parallelism();
}
inline void TPUCompileMetadataProto::_internal_set_enable_automatic_model_parallelism(bool value) {
  
  _impl_.enable_automatic_model_parallelism_ = value;
}
inline void TPUCompileMetadataProto::set_enable_automatic_model_parallelism(bool value) {
  _internal_set_enable_automatic_model_parallelism(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.enable_automatic_model_parallelism)
}

// bool use_spmd_for_xla_partitioning = 15;
inline void TPUCompileMetadataProto::clear_use_spmd_for_xla_partitioning() {
  _impl_.use_spmd_for_xla_partitioning_ = false;
}
inline bool TPUCompileMetadataProto::_internal_use_spmd_for_xla_partitioning() const {
  return _impl_.use_spmd_for_xla_partitioning_;
}
inline bool TPUCompileMetadataProto::use_spmd_for_xla_partitioning() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.use_spmd_for_xla_partitioning)
  return _internal_use_spmd_for_xla_partitioning();
}
inline void TPUCompileMetadataProto::_internal_set_use_spmd_for_xla_partitioning(bool value) {
  
  _impl_.use_spmd_for_xla_partitioning_ = value;
}
inline void TPUCompileMetadataProto::set_use_spmd_for_xla_partitioning(bool value) {
  _internal_set_use_spmd_for_xla_partitioning(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.use_spmd_for_xla_partitioning)
}

// bool use_auto_spmd_for_xla_partitioning = 18;
inline void TPUCompileMetadataProto::clear_use_auto_spmd_for_xla_partitioning() {
  _impl_.use_auto_spmd_for_xla_partitioning_ = false;
}
inline bool TPUCompileMetadataProto::_internal_use_auto_spmd_for_xla_partitioning() const {
  return _impl_.use_auto_spmd_for_xla_partitioning_;
}
inline bool TPUCompileMetadataProto::use_auto_spmd_for_xla_partitioning() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.use_auto_spmd_for_xla_partitioning)
  return _internal_use_auto_spmd_for_xla_partitioning();
}
inline void TPUCompileMetadataProto::_internal_set_use_auto_spmd_for_xla_partitioning(bool value) {
  
  _impl_.use_auto_spmd_for_xla_partitioning_ = value;
}
inline void TPUCompileMetadataProto::set_use_auto_spmd_for_xla_partitioning(bool value) {
  _internal_set_use_auto_spmd_for_xla_partitioning(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.use_auto_spmd_for_xla_partitioning)
}

// repeated int64 auto_spmd_mesh_shape = 19;
inline int TPUCompileMetadataProto::_internal_auto_spmd_mesh_shape_size() const {
  return _impl_.auto_spmd_mesh_shape_.size();
}
inline int TPUCompileMetadataProto::auto_spmd_mesh_shape_size() const {
  return _internal_auto_spmd_mesh_shape_size();
}
inline void TPUCompileMetadataProto::clear_auto_spmd_mesh_shape() {
  _impl_.auto_spmd_mesh_shape_.Clear();
}
inline int64_t TPUCompileMetadataProto::_internal_auto_spmd_mesh_shape(int index) const {
  return _impl_.auto_spmd_mesh_shape_.Get(index);
}
inline int64_t TPUCompileMetadataProto::auto_spmd_mesh_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_shape)
  return _internal_auto_spmd_mesh_shape(index);
}
inline void TPUCompileMetadataProto::set_auto_spmd_mesh_shape(int index, int64_t value) {
  _impl_.auto_spmd_mesh_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_shape)
}
inline void TPUCompileMetadataProto::_internal_add_auto_spmd_mesh_shape(int64_t value) {
  _impl_.auto_spmd_mesh_shape_.Add(value);
}
inline void TPUCompileMetadataProto::add_auto_spmd_mesh_shape(int64_t value) {
  _internal_add_auto_spmd_mesh_shape(value);
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TPUCompileMetadataProto::_internal_auto_spmd_mesh_shape() const {
  return _impl_.auto_spmd_mesh_shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TPUCompileMetadataProto::auto_spmd_mesh_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_shape)
  return _internal_auto_spmd_mesh_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TPUCompileMetadataProto::_internal_mutable_auto_spmd_mesh_shape() {
  return &_impl_.auto_spmd_mesh_shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TPUCompileMetadataProto::mutable_auto_spmd_mesh_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_shape)
  return _internal_mutable_auto_spmd_mesh_shape();
}

// repeated int64 auto_spmd_mesh_ids = 20;
inline int TPUCompileMetadataProto::_internal_auto_spmd_mesh_ids_size() const {
  return _impl_.auto_spmd_mesh_ids_.size();
}
inline int TPUCompileMetadataProto::auto_spmd_mesh_ids_size() const {
  return _internal_auto_spmd_mesh_ids_size();
}
inline void TPUCompileMetadataProto::clear_auto_spmd_mesh_ids() {
  _impl_.auto_spmd_mesh_ids_.Clear();
}
inline int64_t TPUCompileMetadataProto::_internal_auto_spmd_mesh_ids(int index) const {
  return _impl_.auto_spmd_mesh_ids_.Get(index);
}
inline int64_t TPUCompileMetadataProto::auto_spmd_mesh_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_ids)
  return _internal_auto_spmd_mesh_ids(index);
}
inline void TPUCompileMetadataProto::set_auto_spmd_mesh_ids(int index, int64_t value) {
  _impl_.auto_spmd_mesh_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_ids)
}
inline void TPUCompileMetadataProto::_internal_add_auto_spmd_mesh_ids(int64_t value) {
  _impl_.auto_spmd_mesh_ids_.Add(value);
}
inline void TPUCompileMetadataProto::add_auto_spmd_mesh_ids(int64_t value) {
  _internal_add_auto_spmd_mesh_ids(value);
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TPUCompileMetadataProto::_internal_auto_spmd_mesh_ids() const {
  return _impl_.auto_spmd_mesh_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TPUCompileMetadataProto::auto_spmd_mesh_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_ids)
  return _internal_auto_spmd_mesh_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TPUCompileMetadataProto::_internal_mutable_auto_spmd_mesh_ids() {
  return &_impl_.auto_spmd_mesh_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TPUCompileMetadataProto::mutable_auto_spmd_mesh_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUCompileMetadataProto.auto_spmd_mesh_ids)
  return _internal_mutable_auto_spmd_mesh_ids();
}

// uint64 mlir_fingerprint = 17;
inline void TPUCompileMetadataProto::clear_mlir_fingerprint() {
  _impl_.mlir_fingerprint_ = uint64_t{0u};
}
inline uint64_t TPUCompileMetadataProto::_internal_mlir_fingerprint() const {
  return _impl_.mlir_fingerprint_;
}
inline uint64_t TPUCompileMetadataProto::mlir_fingerprint() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.mlir_fingerprint)
  return _internal_mlir_fingerprint();
}
inline void TPUCompileMetadataProto::_internal_set_mlir_fingerprint(uint64_t value) {
  
  _impl_.mlir_fingerprint_ = value;
}
inline void TPUCompileMetadataProto::set_mlir_fingerprint(uint64_t value) {
  _internal_set_mlir_fingerprint(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.mlir_fingerprint)
}

// .tensorflow.tpu.TPUCompileOptions compile_options = 21;
inline bool TPUCompileMetadataProto::_internal_has_compile_options() const {
  return this != internal_default_instance() && _impl_.compile_options_ != nullptr;
}
inline bool TPUCompileMetadataProto::has_compile_options() const {
  return _internal_has_compile_options();
}
inline void TPUCompileMetadataProto::clear_compile_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.compile_options_ != nullptr) {
    delete _impl_.compile_options_;
  }
  _impl_.compile_options_ = nullptr;
}
inline const ::tensorflow::tpu::TPUCompileOptions& TPUCompileMetadataProto::_internal_compile_options() const {
  const ::tensorflow::tpu::TPUCompileOptions* p = _impl_.compile_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::TPUCompileOptions&>(
      ::tensorflow::tpu::_TPUCompileOptions_default_instance_);
}
inline const ::tensorflow::tpu::TPUCompileOptions& TPUCompileMetadataProto::compile_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.compile_options)
  return _internal_compile_options();
}
inline void TPUCompileMetadataProto::unsafe_arena_set_allocated_compile_options(
    ::tensorflow::tpu::TPUCompileOptions* compile_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.compile_options_);
  }
  _impl_.compile_options_ = compile_options;
  if (compile_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.compile_options)
}
inline ::tensorflow::tpu::TPUCompileOptions* TPUCompileMetadataProto::release_compile_options() {
  
  ::tensorflow::tpu::TPUCompileOptions* temp = _impl_.compile_options_;
  _impl_.compile_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::TPUCompileOptions* TPUCompileMetadataProto::unsafe_arena_release_compile_options() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.compile_options)
  
  ::tensorflow::tpu::TPUCompileOptions* temp = _impl_.compile_options_;
  _impl_.compile_options_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::TPUCompileOptions* TPUCompileMetadataProto::_internal_mutable_compile_options() {
  
  if (_impl_.compile_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::TPUCompileOptions>(GetArenaForAllocation());
    _impl_.compile_options_ = p;
  }
  return _impl_.compile_options_;
}
inline ::tensorflow::tpu::TPUCompileOptions* TPUCompileMetadataProto::mutable_compile_options() {
  ::tensorflow::tpu::TPUCompileOptions* _msg = _internal_mutable_compile_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.compile_options)
  return _msg;
}
inline void TPUCompileMetadataProto::set_allocated_compile_options(::tensorflow::tpu::TPUCompileOptions* compile_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.compile_options_;
  }
  if (compile_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(compile_options);
    if (message_arena != submessage_arena) {
      compile_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, compile_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.compile_options_ = compile_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.compile_options)
}

// string module_name = 22;
inline void TPUCompileMetadataProto::clear_module_name() {
  _impl_.module_name_.ClearToEmpty();
}
inline const std::string& TPUCompileMetadataProto::module_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileMetadataProto.module_name)
  return _internal_module_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TPUCompileMetadataProto::set_module_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.module_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileMetadataProto.module_name)
}
inline std::string* TPUCompileMetadataProto::mutable_module_name() {
  std::string* _s = _internal_mutable_module_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUCompileMetadataProto.module_name)
  return _s;
}
inline const std::string& TPUCompileMetadataProto::_internal_module_name() const {
  return _impl_.module_name_.Get();
}
inline void TPUCompileMetadataProto::_internal_set_module_name(const std::string& value) {
  
  _impl_.module_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TPUCompileMetadataProto::_internal_mutable_module_name() {
  
  return _impl_.module_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TPUCompileMetadataProto::release_module_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUCompileMetadataProto.module_name)
  return _impl_.module_name_.Release();
}
inline void TPUCompileMetadataProto::set_allocated_module_name(std::string* module_name) {
  if (module_name != nullptr) {
    
  } else {
    
  }
  _impl_.module_name_.SetAllocated(module_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.module_name_.IsDefault()) {
    _impl_.module_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUCompileMetadataProto.module_name)
}

// -------------------------------------------------------------------

// TPUCompileOptions

// .tensorflow.tpu.TPUCompileOptions.Precision matrix_unit_operand_precision = 1;
inline void TPUCompileOptions::clear_matrix_unit_operand_precision() {
  _impl_.matrix_unit_operand_precision_ = 0;
}
inline ::tensorflow::tpu::TPUCompileOptions_Precision TPUCompileOptions::_internal_matrix_unit_operand_precision() const {
  return static_cast< ::tensorflow::tpu::TPUCompileOptions_Precision >(_impl_.matrix_unit_operand_precision_);
}
inline ::tensorflow::tpu::TPUCompileOptions_Precision TPUCompileOptions::matrix_unit_operand_precision() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUCompileOptions.matrix_unit_operand_precision)
  return _internal_matrix_unit_operand_precision();
}
inline void TPUCompileOptions::_internal_set_matrix_unit_operand_precision(::tensorflow::tpu::TPUCompileOptions_Precision value) {
  
  _impl_.matrix_unit_operand_precision_ = value;
}
inline void TPUCompileOptions::set_matrix_unit_operand_precision(::tensorflow::tpu::TPUCompileOptions_Precision value) {
  _internal_set_matrix_unit_operand_precision(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUCompileOptions.matrix_unit_operand_precision)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind>() {
  return ::tensorflow::tpu::TPUCompileMetadataProto_Arg_Kind_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding>() {
  return ::tensorflow::tpu::TPUCompileMetadataProto_Arg_EnableXlaSharding_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::TPUCompileOptions_Precision> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUCompileOptions_Precision>() {
  return ::tensorflow::tpu::TPUCompileOptions_Precision_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fcompile_5fmetadata_2eproto
