/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifdef __cplusplus
extern "C"{
#endif

#include <rpc.h>
#include <rpcndr.h>

#ifdef _MIDL_USE_GUIDDEF_
#ifndef INITGUID
#define INITGUID
#include <guiddef.h>
#undef INITGUID
#else
#include <guiddef.h>
#endif
#define MIDL_DEFINE_GUID(type,name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) DEFINE_GUID(name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8)
#else
#ifndef __IID_DEFINED__
#define __IID_DEFINED__
  typedef struct _IID {
    unsigned long x;
    unsigned short s1;
    unsigned short s2;
    unsigned char c[8];
  } IID;
#endif

#ifndef CLSID_DEFINED
#define CLSID_DEFINED
  typedef IID CLSID;
#endif
#define MIDL_DEFINE_GUID(type,name,l,w1,w2,b1,b2,b3,b4,b5,b6,b7,b8) const type name = {l,w1,w2,{b1,b2,b3,b4,b5,b6,b7,b8}}
#endif

#if defined __cplusplus && !defined CDO_NO_NAMESPACE
  namespace CDO {
#else
#undef IDataSource
#endif

  MIDL_DEFINE_GUID(IID,IID_IDataSource,0xCD000029,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IMessage,0xCD000020,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IBodyPart,0xCD000021,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IConfiguration,0xCD000022,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IMessages,0xCD000025,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IDropDirectory,0xCD000024,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IBodyParts,0xCD000023,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_ISMTPScriptConnector,0xCD000030,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_INNTPEarlyScriptConnector,0xCD000034,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_INNTPPostScriptConnector,0xCD000031,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_INNTPFinalScriptConnector,0xCD000032,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_ISMTPOnArrival,0xCD000026,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_INNTPOnPostEarly,0xCD000033,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_INNTPOnPost,0xCD000027,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_INNTPOnPostFinal,0xCD000028,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IProxyObject,0xCD000083,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IItem,0xCD000126,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IAppointment,0xCD000120,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_ICalendarMessage,0xCD000122,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IIntegers,0xCD00012E,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IVariants,0xCD00012F,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IRecurrencePattern,0xCD000123,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IException,0xCD000124,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IRecurrencePatterns,0xCD00012C,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IExceptions,0xCD00012D,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_ICalendarPart,0xCD000133,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_ICalendarParts,0xCD000130,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IAttendee,0xCD000135,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IAttendees,0xCD000136,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IMailbox,0xCD000125,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IFolder,0xCD000132,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IContactGroupMembers,0xCD000138,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IPerson,0xCD000127,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IAddressee,0xCD000139,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IAddressees,0xCD000142,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,IID_IGetInterface,0xCD0ff000,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(IID,LIBID_CDO,0xCD000000,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_Message,0xCD000001,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_Configuration,0xCD000002,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_DropDirectory,0xCD000004,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_SMTPConnector,0xCD000008,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_NNTPEarlyConnector,0xCD000011,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_NNTPPostConnector,0xCD000009,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_NNTPFinalConnector,0xCD000010,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_Item,0xCD000112,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_Appointment,0xCD000100,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_CalendarMessage,0xCD000102,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_Folder,0xCD00010E,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_Person,0xCD000107,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_Attendee,0xCD00010D,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);
  MIDL_DEFINE_GUID(CLSID,CLSID_Addressee,0xCD000110,0x8B95,0x11D1,0x82,0xDB,0x00,0xC0,0x4F,0xB1,0x62,0x5D);

#undef MIDL_DEFINE_GUID

#if defined __cplusplus && !defined CDO_NO_NAMESPACE
}
#endif

#ifdef __cplusplus
}
#endif
