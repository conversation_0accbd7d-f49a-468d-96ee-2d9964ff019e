/*** Autogenerated by WIDL 10.8 from include/windows.applicationmodel.background.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_applicationmodel_background_h__
#define __windows_applicationmodel_background_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler ABI::Windows::ApplicationModel::Background::IBackgroundTaskCanceledEventHandler
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskCanceledEventHandler;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler ABI::Windows::ApplicationModel::Background::IBackgroundTaskCompletedEventHandler
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskCompletedEventHandler;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler ABI::Windows::ApplicationModel::Background::IBackgroundTaskProgressEventHandler
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskProgressEventHandler;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs ABI::Windows::ApplicationModel::Background::IBackgroundTaskCompletedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskCompletedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral ABI::Windows::ApplicationModel::Background::IBackgroundTaskDeferral
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskDeferral;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs ABI::Windows::ApplicationModel::Background::IBackgroundTaskProgressEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskProgressEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance ABI::Windows::ApplicationModel::Background::IBackgroundTaskInstance
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskInstance;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistration;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistration2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistration3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistrationGroup;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroupFactory
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistrationGroupFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationStatics
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistrationStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationStatics2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistrationStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger ABI::Windows::ApplicationModel::Background::IBackgroundTrigger
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTrigger;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCompletedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCompletedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                class BackgroundTaskCompletedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCompletedEventArgs __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCompletedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCompletedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskDeferral_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                class BackgroundTaskDeferral;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskDeferral __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskDeferral;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskDeferral_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskProgressEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskProgressEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                class BackgroundTaskProgressEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskProgressEventArgs __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskProgressEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskProgressEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistration_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistration_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                class BackgroundTaskRegistration;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistration __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistration;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistration_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistrationGroup_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistrationGroup_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                class BackgroundTaskRegistrationGroup;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistrationGroup __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistrationGroup;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskRegistrationGroup_FWD_DEFINED__ */

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_FWD_DEFINED__
#define ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_FWD_DEFINED__
typedef interface __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration;
#ifdef __cplusplus
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_FWD_DEFINED__
#define ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_FWD_DEFINED__
typedef interface __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration;
#ifdef __cplusplus
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.applicationmodel.activation.h>
#include <windows.storage.h>
#include <windows.system.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCancellationReason __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCancellationReason;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs ABI::Windows::ApplicationModel::Background::IBackgroundTaskCompletedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskCompletedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral ABI::Windows::ApplicationModel::Background::IBackgroundTaskDeferral
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskDeferral;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs ABI::Windows::ApplicationModel::Background::IBackgroundTaskProgressEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskProgressEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance ABI::Windows::ApplicationModel::Background::IBackgroundTaskInstance
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskInstance;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistration;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistration2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistration3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistrationGroup;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroupFactory
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistrationGroupFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationStatics
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistrationStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationStatics2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTaskRegistrationStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger ABI::Windows::ApplicationModel::Background::IBackgroundTrigger
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                interface IBackgroundTrigger;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_FWD_DEFINED__
#define ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_FWD_DEFINED__
typedef interface __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration;
#ifdef __cplusplus
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_FWD_DEFINED__
#define ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_FWD_DEFINED__
typedef interface __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration;
#ifdef __cplusplus
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                enum BackgroundTaskCancellationReason {
                    BackgroundTaskCancellationReason_Abort = 0,
                    BackgroundTaskCancellationReason_Terminating = 1,
                    BackgroundTaskCancellationReason_LoggingOff = 2,
                    BackgroundTaskCancellationReason_ServicingUpdate = 3,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BackgroundTaskCancellationReason_IdleTask = 4,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BackgroundTaskCancellationReason_Uninstall = 5,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BackgroundTaskCancellationReason_ConditionLoss = 6,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BackgroundTaskCancellationReason_SystemPolicy = 7,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BackgroundTaskCancellationReason_QuietHoursEntered = 8,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BackgroundTaskCancellationReason_ExecutionTimeExceeded = 9,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BackgroundTaskCancellationReason_ResourceRevocation = 10,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BackgroundTaskCancellationReason_EnergySaver = 11
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCancellationReason {
    BackgroundTaskCancellationReason_Abort = 0,
    BackgroundTaskCancellationReason_Terminating = 1,
    BackgroundTaskCancellationReason_LoggingOff = 2,
    BackgroundTaskCancellationReason_ServicingUpdate = 3,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BackgroundTaskCancellationReason_IdleTask = 4,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BackgroundTaskCancellationReason_Uninstall = 5,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BackgroundTaskCancellationReason_ConditionLoss = 6,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BackgroundTaskCancellationReason_SystemPolicy = 7,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BackgroundTaskCancellationReason_QuietHoursEntered = 8,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BackgroundTaskCancellationReason_ExecutionTimeExceeded = 9,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BackgroundTaskCancellationReason_ResourceRevocation = 10,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BackgroundTaskCancellationReason_EnergySaver = 11
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
};
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define BackgroundTaskCancellationReason __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCancellationReason
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IBackgroundTaskCanceledEventHandler interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler, 0xa6c4bac0, 0x51f8, 0x4c57, 0xac,0x3f, 0x15,0x6d,0xd1,0x68,0x0c,0x4f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("a6c4bac0-51f8-4c57-ac3f-156dd1680c4f")
                IBackgroundTaskCanceledEventHandler : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskInstance *sender,
                        ABI::Windows::ApplicationModel::Background::BackgroundTaskCancellationReason reason) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler, 0xa6c4bac0, 0x51f8, 0x4c57, 0xac,0x3f, 0x15,0x6d,0xd1,0x68,0x0c,0x4f)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler *This);

    /*** IBackgroundTaskCanceledEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *sender,
        __x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCancellationReason reason);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandlerVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IBackgroundTaskCanceledEventHandler methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_Invoke(This,sender,reason) (This)->lpVtbl->Invoke(This,sender,reason)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IBackgroundTaskCanceledEventHandler methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_Invoke(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *sender,__x_ABI_CWindows_CApplicationModel_CBackground_CBackgroundTaskCancellationReason reason) {
    return This->lpVtbl->Invoke(This,sender,reason);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskCanceledEventHandler IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler
#define IBackgroundTaskCanceledEventHandlerVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandlerVtbl
#define IBackgroundTaskCanceledEventHandler __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler
#define IBackgroundTaskCanceledEventHandler_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_QueryInterface
#define IBackgroundTaskCanceledEventHandler_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_AddRef
#define IBackgroundTaskCanceledEventHandler_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_Release
#define IBackgroundTaskCanceledEventHandler_Invoke __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_Invoke
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskCompletedEventHandler interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler, 0x5b38e929, 0xa086, 0x46a7, 0xa6,0x78, 0x43,0x91,0x35,0x82,0x2b,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("5b38e929-a086-46a7-a678-439135822bcf")
                IBackgroundTaskCompletedEventHandler : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration *sender,
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskCompletedEventArgs *args) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler, 0x5b38e929, 0xa086, 0x46a7, 0xa6,0x78, 0x43,0x91,0x35,0x82,0x2b,0xcf)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler *This);

    /*** IBackgroundTaskCompletedEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *sender,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *args);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandlerVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IBackgroundTaskCompletedEventHandler methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IBackgroundTaskCompletedEventHandler methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_Invoke(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *sender,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskCompletedEventHandler IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler
#define IBackgroundTaskCompletedEventHandlerVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandlerVtbl
#define IBackgroundTaskCompletedEventHandler __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler
#define IBackgroundTaskCompletedEventHandler_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_QueryInterface
#define IBackgroundTaskCompletedEventHandler_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_AddRef
#define IBackgroundTaskCompletedEventHandler_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_Release
#define IBackgroundTaskCompletedEventHandler_Invoke __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_Invoke
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskProgressEventHandler interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler, 0x46e0683c, 0x8a88, 0x4c99, 0x80,0x4c, 0x76,0x89,0x7f,0x62,0x77,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("46e0683c-8a88-4c99-804c-76897f6277a6")
                IBackgroundTaskProgressEventHandler : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration *sender,
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskProgressEventArgs *args) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler, 0x46e0683c, 0x8a88, 0x4c99, 0x80,0x4c, 0x76,0x89,0x7f,0x62,0x77,0xa6)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler *This);

    /*** IBackgroundTaskProgressEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *sender,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *args);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandlerVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IBackgroundTaskProgressEventHandler methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IBackgroundTaskProgressEventHandler methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_Invoke(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *sender,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskProgressEventHandler IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler
#define IBackgroundTaskProgressEventHandlerVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandlerVtbl
#define IBackgroundTaskProgressEventHandler __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler
#define IBackgroundTaskProgressEventHandler_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_QueryInterface
#define IBackgroundTaskProgressEventHandler_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_AddRef
#define IBackgroundTaskProgressEventHandler_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_Release
#define IBackgroundTaskProgressEventHandler_Invoke __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_Invoke
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskCompletedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs, 0x565d25cf, 0xf209, 0x48f4, 0x99,0x67, 0x2b,0x18,0x4f,0x7b,0xfb,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("565d25cf-f209-48f4-9967-2b184f7bfbf0")
                IBackgroundTaskCompletedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_InstanceId(
                        GUID *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CheckResult(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs, 0x565d25cf, 0xf209, 0x48f4, 0x99,0x67, 0x2b,0x18,0x4f,0x7b,0xfb,0xf0)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskCompletedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_InstanceId)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *CheckResult)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs *This);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskCompletedEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_get_InstanceId(This,value) (This)->lpVtbl->get_InstanceId(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_CheckResult(This) (This)->lpVtbl->CheckResult(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskCompletedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_get_InstanceId(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs* This,GUID *value) {
    return This->lpVtbl->get_InstanceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_CheckResult(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs* This) {
    return This->lpVtbl->CheckResult(This);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskCompletedEventArgs IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs
#define IBackgroundTaskCompletedEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgsVtbl
#define IBackgroundTaskCompletedEventArgs __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs
#define IBackgroundTaskCompletedEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_QueryInterface
#define IBackgroundTaskCompletedEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_AddRef
#define IBackgroundTaskCompletedEventArgs_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_Release
#define IBackgroundTaskCompletedEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetIids
#define IBackgroundTaskCompletedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetRuntimeClassName
#define IBackgroundTaskCompletedEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_GetTrustLevel
#define IBackgroundTaskCompletedEventArgs_get_InstanceId __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_get_InstanceId
#define IBackgroundTaskCompletedEventArgs_CheckResult __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_CheckResult
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskDeferral interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral, 0x93cc156d, 0xaf27, 0x4dd3, 0x84,0x6e, 0x24,0xee,0x40,0xca,0xdd,0x25);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("93cc156d-af27-4dd3-846e-24ee40cadd25")
                IBackgroundTaskDeferral : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Complete(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral, 0x93cc156d, 0xaf27, 0x4dd3, 0x84,0x6e, 0x24,0xee,0x40,0xca,0xdd,0x25)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferralVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskDeferral methods ***/
    HRESULT (STDMETHODCALLTYPE *Complete)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral *This);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferralVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferralVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskDeferral methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_Complete(This) (This)->lpVtbl->Complete(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskDeferral methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_Complete(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral* This) {
    return This->lpVtbl->Complete(This);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskDeferral IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral
#define IBackgroundTaskDeferralVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferralVtbl
#define IBackgroundTaskDeferral __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral
#define IBackgroundTaskDeferral_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_QueryInterface
#define IBackgroundTaskDeferral_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_AddRef
#define IBackgroundTaskDeferral_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_Release
#define IBackgroundTaskDeferral_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetIids
#define IBackgroundTaskDeferral_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetRuntimeClassName
#define IBackgroundTaskDeferral_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_GetTrustLevel
#define IBackgroundTaskDeferral_Complete __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_Complete
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskProgressEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs, 0xfb1468ac, 0x8332, 0x4d0a, 0x95,0x32, 0x03,0xea,0xe6,0x84,0xda,0x31);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("fb1468ac-8332-4d0a-9532-03eae684da31")
                IBackgroundTaskProgressEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_InstanceId(
                        GUID *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Progress(
                        UINT32 *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs, 0xfb1468ac, 0x8332, 0x4d0a, 0x95,0x32, 0x03,0xea,0xe6,0x84,0xda,0x31)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskProgressEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_InstanceId)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *get_Progress)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs *This,
        UINT32 *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskProgressEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_get_InstanceId(This,value) (This)->lpVtbl->get_InstanceId(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_get_Progress(This,value) (This)->lpVtbl->get_Progress(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskProgressEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_get_InstanceId(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs* This,GUID *value) {
    return This->lpVtbl->get_InstanceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_get_Progress(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs* This,UINT32 *value) {
    return This->lpVtbl->get_Progress(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskProgressEventArgs IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs
#define IBackgroundTaskProgressEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgsVtbl
#define IBackgroundTaskProgressEventArgs __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs
#define IBackgroundTaskProgressEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_QueryInterface
#define IBackgroundTaskProgressEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_AddRef
#define IBackgroundTaskProgressEventArgs_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_Release
#define IBackgroundTaskProgressEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetIids
#define IBackgroundTaskProgressEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetRuntimeClassName
#define IBackgroundTaskProgressEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_GetTrustLevel
#define IBackgroundTaskProgressEventArgs_get_InstanceId __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_get_InstanceId
#define IBackgroundTaskProgressEventArgs_get_Progress __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_get_Progress
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskInstance interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance, 0x865bda7a, 0x21d8, 0x4573, 0x8f,0x32, 0x92,0x8a,0x1b,0x06,0x41,0xf6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("865bda7a-21d8-4573-8f32-928a1b0641f6")
                IBackgroundTaskInstance : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_InstanceId(
                        GUID *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Task(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration **task) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Progress(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Progress(
                        UINT32 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_TriggerDetails(
                        IInspectable **details) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Canceled(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskCanceledEventHandler *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Canceled(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SuspendedCount(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDeferral(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskDeferral **deferral) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance, 0x865bda7a, 0x21d8, 0x4573, 0x8f,0x32, 0x92,0x8a,0x1b,0x06,0x41,0xf6)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstanceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskInstance methods ***/
    HRESULT (STDMETHODCALLTYPE *get_InstanceId)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *get_Task)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration **task);

    HRESULT (STDMETHODCALLTYPE *get_Progress)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Progress)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        UINT32 value);

    HRESULT (STDMETHODCALLTYPE *get_TriggerDetails)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        IInspectable **details);

    HRESULT (STDMETHODCALLTYPE *add_Canceled)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_Canceled)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *get_SuspendedCount)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetDeferral)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral **deferral);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstanceVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstanceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskInstance methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_InstanceId(This,value) (This)->lpVtbl->get_InstanceId(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_Task(This,task) (This)->lpVtbl->get_Task(This,task)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_Progress(This,value) (This)->lpVtbl->get_Progress(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_put_Progress(This,value) (This)->lpVtbl->put_Progress(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_TriggerDetails(This,details) (This)->lpVtbl->get_TriggerDetails(This,details)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_add_Canceled(This,handler,cookie) (This)->lpVtbl->add_Canceled(This,handler,cookie)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_remove_Canceled(This,cookie) (This)->lpVtbl->remove_Canceled(This,cookie)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_SuspendedCount(This,value) (This)->lpVtbl->get_SuspendedCount(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetDeferral(This,deferral) (This)->lpVtbl->GetDeferral(This,deferral)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskInstance methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_InstanceId(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,GUID *value) {
    return This->lpVtbl->get_InstanceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_Task(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration **task) {
    return This->lpVtbl->get_Task(This,task);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_Progress(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,UINT32 *value) {
    return This->lpVtbl->get_Progress(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_put_Progress(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,UINT32 value) {
    return This->lpVtbl->put_Progress(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_TriggerDetails(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,IInspectable **details) {
    return This->lpVtbl->get_TriggerDetails(This,details);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_add_Canceled(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCanceledEventHandler *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_Canceled(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_remove_Canceled(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_Canceled(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_SuspendedCount(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,UINT32 *value) {
    return This->lpVtbl->get_SuspendedCount(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetDeferral(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskDeferral **deferral) {
    return This->lpVtbl->GetDeferral(This,deferral);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskInstance IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance
#define IBackgroundTaskInstanceVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstanceVtbl
#define IBackgroundTaskInstance __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance
#define IBackgroundTaskInstance_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_QueryInterface
#define IBackgroundTaskInstance_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_AddRef
#define IBackgroundTaskInstance_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_Release
#define IBackgroundTaskInstance_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetIids
#define IBackgroundTaskInstance_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetRuntimeClassName
#define IBackgroundTaskInstance_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetTrustLevel
#define IBackgroundTaskInstance_get_InstanceId __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_InstanceId
#define IBackgroundTaskInstance_get_Task __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_Task
#define IBackgroundTaskInstance_get_Progress __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_Progress
#define IBackgroundTaskInstance_put_Progress __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_put_Progress
#define IBackgroundTaskInstance_get_TriggerDetails __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_TriggerDetails
#define IBackgroundTaskInstance_add_Canceled __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_add_Canceled
#define IBackgroundTaskInstance_remove_Canceled __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_remove_Canceled
#define IBackgroundTaskInstance_get_SuspendedCount __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_get_SuspendedCount
#define IBackgroundTaskInstance_GetDeferral __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_GetDeferral
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskInstance_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskRegistration interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration, 0x10654cc2, 0xa26e, 0x43bf, 0x8c,0x12, 0x1f,0xb4,0x0d,0xbf,0xbf,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("10654cc2-a26e-43bf-8c12-1fb40dbfbfa0")
                IBackgroundTaskRegistration : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_TaskId(
                        GUID *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Name(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Progress(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskProgressEventHandler *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Progress(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Completed(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskCompletedEventHandler *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Completed(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Unregister(
                        boolean cancel_task) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration, 0x10654cc2, 0xa26e, 0x43bf, 0x8c,0x12, 0x1f,0xb4,0x0d,0xbf,0xbf,0xa0)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskRegistration methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TaskId)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *add_Progress)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_Progress)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_Completed)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_Completed)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *Unregister)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration *This,
        boolean cancel_task);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskRegistration methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_get_TaskId(This,value) (This)->lpVtbl->get_TaskId(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_add_Progress(This,handler,cookie) (This)->lpVtbl->add_Progress(This,handler,cookie)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_remove_Progress(This,cookie) (This)->lpVtbl->remove_Progress(This,cookie)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_add_Completed(This,handler,cookie) (This)->lpVtbl->add_Completed(This,handler,cookie)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_remove_Completed(This,cookie) (This)->lpVtbl->remove_Completed(This,cookie)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_Unregister(This,cancel_task) (This)->lpVtbl->Unregister(This,cancel_task)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskRegistration methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_get_TaskId(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,GUID *value) {
    return This->lpVtbl->get_TaskId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_get_Name(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_add_Progress(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskProgressEventHandler *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_Progress(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_remove_Progress(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_Progress(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_add_Completed(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskCompletedEventHandler *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_Completed(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_remove_Completed(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_Completed(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_Unregister(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration* This,boolean cancel_task) {
    return This->lpVtbl->Unregister(This,cancel_task);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskRegistration IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration
#define IBackgroundTaskRegistrationVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationVtbl
#define IBackgroundTaskRegistration __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration
#define IBackgroundTaskRegistration_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_QueryInterface
#define IBackgroundTaskRegistration_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_AddRef
#define IBackgroundTaskRegistration_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_Release
#define IBackgroundTaskRegistration_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetIids
#define IBackgroundTaskRegistration_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetRuntimeClassName
#define IBackgroundTaskRegistration_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_GetTrustLevel
#define IBackgroundTaskRegistration_get_TaskId __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_get_TaskId
#define IBackgroundTaskRegistration_get_Name __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_get_Name
#define IBackgroundTaskRegistration_add_Progress __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_add_Progress
#define IBackgroundTaskRegistration_remove_Progress __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_remove_Progress
#define IBackgroundTaskRegistration_add_Completed __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_add_Completed
#define IBackgroundTaskRegistration_remove_Completed __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_remove_Completed
#define IBackgroundTaskRegistration_Unregister __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_Unregister
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskRegistration2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2, 0x6138c703, 0xbb86, 0x4112, 0xaf,0xc3, 0x7f,0x93,0x9b,0x16,0x6e,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("6138c703-bb86-4112-afc3-7f939b166e3b")
                IBackgroundTaskRegistration2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Trigger(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTrigger **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2, 0x6138c703, 0xbb86, 0x4112, 0xaf,0xc3, 0x7f,0x93,0x9b,0x16,0x6e,0x3b)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskRegistration2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Trigger)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskRegistration2 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_get_Trigger(This,value) (This)->lpVtbl->get_Trigger(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskRegistration2 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_get_Trigger(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger **value) {
    return This->lpVtbl->get_Trigger(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskRegistration2 IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2
#define IBackgroundTaskRegistration2Vtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2Vtbl
#define IBackgroundTaskRegistration2 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2
#define IBackgroundTaskRegistration2_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_QueryInterface
#define IBackgroundTaskRegistration2_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_AddRef
#define IBackgroundTaskRegistration2_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_Release
#define IBackgroundTaskRegistration2_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetIids
#define IBackgroundTaskRegistration2_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetRuntimeClassName
#define IBackgroundTaskRegistration2_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_GetTrustLevel
#define IBackgroundTaskRegistration2_get_Trigger __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_get_Trigger
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskRegistration3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3, 0xfe338195, 0x9423, 0x4d8b, 0x83,0x0d, 0xb1,0xdd,0x2c,0x7b,0xad,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("fe338195-9423-4d8b-830d-b1dd2c7badd5")
                IBackgroundTaskRegistration3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_TaskGroup(
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3, 0xfe338195, 0x9423, 0x4d8b, 0x83,0x0d, 0xb1,0xdd,0x2c,0x7b,0xad,0xd5)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskRegistration3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TaskGroup)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskRegistration3 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_get_TaskGroup(This,value) (This)->lpVtbl->get_TaskGroup(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskRegistration3 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_get_TaskGroup(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **value) {
    return This->lpVtbl->get_TaskGroup(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskRegistration3 IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3
#define IBackgroundTaskRegistration3Vtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3Vtbl
#define IBackgroundTaskRegistration3 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3
#define IBackgroundTaskRegistration3_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_QueryInterface
#define IBackgroundTaskRegistration3_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_AddRef
#define IBackgroundTaskRegistration3_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_Release
#define IBackgroundTaskRegistration3_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetIids
#define IBackgroundTaskRegistration3_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetRuntimeClassName
#define IBackgroundTaskRegistration3_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_GetTrustLevel
#define IBackgroundTaskRegistration3_get_TaskGroup __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_get_TaskGroup
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBackgroundTaskRegistrationGroup interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup, 0x2ab1919a, 0x871b, 0x4167, 0x8a,0x76, 0x05,0x5c,0xd6,0x7b,0x5b,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("2ab1919a-871b-4167-8a76-055cd67b5b23")
                IBackgroundTaskRegistrationGroup : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Name(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_BackgroundActivated(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_BackgroundActivated(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_AllTasks(
                        ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup, 0x2ab1919a, 0x871b, 0x4167, 0x8a,0x76, 0x05,0x5c,0xd6,0x7b,0x5b,0x23)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskRegistrationGroup methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *add_BackgroundActivated)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_BackgroundActivated)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_AllTasks)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *This,
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskRegistrationGroup methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_add_BackgroundActivated(This,handler,token) (This)->lpVtbl->add_BackgroundActivated(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_remove_BackgroundActivated(This,token) (This)->lpVtbl->remove_BackgroundActivated(This,token)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_AllTasks(This,value) (This)->lpVtbl->get_AllTasks(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskRegistrationGroup methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_Id(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_Name(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_add_BackgroundActivated(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_BackgroundActivated(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_remove_BackgroundActivated(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_BackgroundActivated(This,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_AllTasks(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup* This,__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration **value) {
    return This->lpVtbl->get_AllTasks(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskRegistrationGroup IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup
#define IBackgroundTaskRegistrationGroupVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupVtbl
#define IBackgroundTaskRegistrationGroup __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup
#define IBackgroundTaskRegistrationGroup_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_QueryInterface
#define IBackgroundTaskRegistrationGroup_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_AddRef
#define IBackgroundTaskRegistrationGroup_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_Release
#define IBackgroundTaskRegistrationGroup_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetIids
#define IBackgroundTaskRegistrationGroup_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetRuntimeClassName
#define IBackgroundTaskRegistrationGroup_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_GetTrustLevel
#define IBackgroundTaskRegistrationGroup_get_Id __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_Id
#define IBackgroundTaskRegistrationGroup_get_Name __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_Name
#define IBackgroundTaskRegistrationGroup_add_BackgroundActivated __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_add_BackgroundActivated
#define IBackgroundTaskRegistrationGroup_remove_BackgroundActivated __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_remove_BackgroundActivated
#define IBackgroundTaskRegistrationGroup_get_AllTasks __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_get_AllTasks
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBackgroundTaskRegistrationGroupFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory, 0x83d92b69, 0x44cf, 0x4631, 0x97,0x40, 0x03,0xc7,0xd8,0x74,0x1b,0xc5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("83d92b69-44cf-4631-9740-03c7d8741bc5")
                IBackgroundTaskRegistrationGroupFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Create(
                        HSTRING id,
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup **group) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWithName(
                        HSTRING id,
                        HSTRING name,
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup **group) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory, 0x83d92b69, 0x44cf, 0x4631, 0x97,0x40, 0x03,0xc7,0xd8,0x74,0x1b,0xc5)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskRegistrationGroupFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory *This,
        HSTRING id,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **group);

    HRESULT (STDMETHODCALLTYPE *CreateWithName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory *This,
        HSTRING id,
        HSTRING name,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **group);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactoryVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskRegistrationGroupFactory methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_Create(This,id,group) (This)->lpVtbl->Create(This,id,group)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_CreateWithName(This,id,name,group) (This)->lpVtbl->CreateWithName(This,id,name,group)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskRegistrationGroupFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_Create(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory* This,HSTRING id,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **group) {
    return This->lpVtbl->Create(This,id,group);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_CreateWithName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory* This,HSTRING id,HSTRING name,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **group) {
    return This->lpVtbl->CreateWithName(This,id,name,group);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskRegistrationGroupFactory IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory
#define IBackgroundTaskRegistrationGroupFactoryVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactoryVtbl
#define IBackgroundTaskRegistrationGroupFactory __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory
#define IBackgroundTaskRegistrationGroupFactory_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_QueryInterface
#define IBackgroundTaskRegistrationGroupFactory_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_AddRef
#define IBackgroundTaskRegistrationGroupFactory_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_Release
#define IBackgroundTaskRegistrationGroupFactory_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetIids
#define IBackgroundTaskRegistrationGroupFactory_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetRuntimeClassName
#define IBackgroundTaskRegistrationGroupFactory_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_GetTrustLevel
#define IBackgroundTaskRegistrationGroupFactory_Create __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_Create
#define IBackgroundTaskRegistrationGroupFactory_CreateWithName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_CreateWithName
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroupFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBackgroundTaskRegistrationStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics, 0x4c542f69, 0xb000, 0x42ba, 0xa0,0x93, 0x6a,0x56,0x3c,0x65,0xe3,0xf8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("4c542f69-b000-42ba-a093-6a563c65e3f8")
                IBackgroundTaskRegistrationStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AllTasks(
                        ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* > **tasks) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics, 0x4c542f69, 0xb000, 0x42ba, 0xa0,0x93, 0x6a,0x56,0x3c,0x65,0xe3,0xf8)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskRegistrationStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AllTasks)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics *This,
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration **tasks);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStaticsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskRegistrationStatics methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_get_AllTasks(This,tasks) (This)->lpVtbl->get_AllTasks(This,tasks)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskRegistrationStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_get_AllTasks(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics* This,__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration **tasks) {
    return This->lpVtbl->get_AllTasks(This,tasks);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskRegistrationStatics IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics
#define IBackgroundTaskRegistrationStaticsVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStaticsVtbl
#define IBackgroundTaskRegistrationStatics __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics
#define IBackgroundTaskRegistrationStatics_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_QueryInterface
#define IBackgroundTaskRegistrationStatics_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_AddRef
#define IBackgroundTaskRegistrationStatics_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_Release
#define IBackgroundTaskRegistrationStatics_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetIids
#define IBackgroundTaskRegistrationStatics_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetRuntimeClassName
#define IBackgroundTaskRegistrationStatics_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_GetTrustLevel
#define IBackgroundTaskRegistrationStatics_get_AllTasks __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_get_AllTasks
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBackgroundTaskRegistrationStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2, 0x174b671e, 0xb20d, 0x4fa9, 0xad,0x9a, 0xe9,0x3a,0xd6,0xc7,0x1e,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("174b671e-b20d-4fa9-ad9a-e93ad6c71e01")
                IBackgroundTaskRegistrationStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AllTaskGroups(
                        ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetTaskGroup(
                        HSTRING groupId,
                        ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2, 0x174b671e, 0xb20d, 0x4fa9, 0xad,0x9a, 0xe9,0x3a,0xd6,0xc7,0x1e,0x01)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 *This,
        TrustLevel *trustLevel);

    /*** IBackgroundTaskRegistrationStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AllTaskGroups)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 *This,
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup **value);

    HRESULT (STDMETHODCALLTYPE *GetTaskGroup)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 *This,
        HSTRING groupId,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBackgroundTaskRegistrationStatics2 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_get_AllTaskGroups(This,value) (This)->lpVtbl->get_AllTaskGroups(This,value)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetTaskGroup(This,groupId,value) (This)->lpVtbl->GetTaskGroup(This,groupId,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBackgroundTaskRegistrationStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_get_AllTaskGroups(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2* This,__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup **value) {
    return This->lpVtbl->get_AllTaskGroups(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetTaskGroup(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2* This,HSTRING groupId,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **value) {
    return This->lpVtbl->GetTaskGroup(This,groupId,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTaskRegistrationStatics2 IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2
#define IBackgroundTaskRegistrationStatics2Vtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2Vtbl
#define IBackgroundTaskRegistrationStatics2 __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2
#define IBackgroundTaskRegistrationStatics2_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_QueryInterface
#define IBackgroundTaskRegistrationStatics2_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_AddRef
#define IBackgroundTaskRegistrationStatics2_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_Release
#define IBackgroundTaskRegistrationStatics2_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetIids
#define IBackgroundTaskRegistrationStatics2_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetRuntimeClassName
#define IBackgroundTaskRegistrationStatics2_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetTrustLevel
#define IBackgroundTaskRegistrationStatics2_get_AllTaskGroups __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_get_AllTaskGroups
#define IBackgroundTaskRegistrationStatics2_GetTaskGroup __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_GetTaskGroup
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBackgroundTrigger interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger, 0x84b3a058, 0x6027, 0x4b87, 0x97,0x90, 0xbd,0xf3,0xf7,0x57,0xdb,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Background {
                MIDL_INTERFACE("84b3a058-6027-4b87-9790-bdf3f757dbd7")
                IBackgroundTrigger : public IInspectable
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger, 0x84b3a058, 0x6027, 0x4b87, 0x97,0x90, 0xbd,0xf3,0xf7,0x57,0xdb,0xd7)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTriggerVtbl;

interface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_QueryInterface(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_AddRef(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_Release(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetIids(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Background
#define IID_IBackgroundTrigger IID___x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger
#define IBackgroundTriggerVtbl __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTriggerVtbl
#define IBackgroundTrigger __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger
#define IBackgroundTrigger_QueryInterface __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_QueryInterface
#define IBackgroundTrigger_AddRef __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_AddRef
#define IBackgroundTrigger_Release __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_Release
#define IBackgroundTrigger_GetIids __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetIids
#define IBackgroundTrigger_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetRuntimeClassName
#define IBackgroundTrigger_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_GetTrustLevel
#endif /* WIDL_using_Windows_ApplicationModel_Background */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTrigger_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Background.BackgroundTaskCompletedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskCompletedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskCompletedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskCompletedEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','C','o','m','p','l','e','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskCompletedEventArgs[] = L"Windows.ApplicationModel.Background.BackgroundTaskCompletedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskCompletedEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','C','o','m','p','l','e','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskCompletedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Background.BackgroundTaskDeferral
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskDeferral_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskDeferral_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskDeferral[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','D','e','f','e','r','r','a','l',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskDeferral[] = L"Windows.ApplicationModel.Background.BackgroundTaskDeferral";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskDeferral[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','D','e','f','e','r','r','a','l',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskDeferral_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Background.BackgroundTaskProgressEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskProgressEventArgs_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskProgressEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskProgressEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','P','r','o','g','r','e','s','s','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskProgressEventArgs[] = L"Windows.ApplicationModel.Background.BackgroundTaskProgressEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskProgressEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','P','r','o','g','r','e','s','s','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskProgressEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Background.BackgroundTaskRegistration
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskRegistration_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskRegistration_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskRegistration[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','R','e','g','i','s','t','r','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskRegistration[] = L"Windows.ApplicationModel.Background.BackgroundTaskRegistration";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskRegistration[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','R','e','g','i','s','t','r','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskRegistration_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Background.BackgroundTaskRegistrationGroup
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskRegistrationGroup_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskRegistrationGroup_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskRegistrationGroup[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','R','e','g','i','s','t','r','a','t','i','o','n','G','r','o','u','p',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskRegistrationGroup[] = L"Windows.ApplicationModel.Background.BackgroundTaskRegistrationGroup";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Background_BackgroundTaskRegistrationGroup[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','B','a','c','k','g','r','o','u','n','d','.','B','a','c','k','g','r','o','u','n','d','T','a','s','k','R','e','g','i','s','t','r','a','t','i','o','n','G','r','o','u','p',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Background_BackgroundTaskRegistrationGroup_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0x49a07732, 0xe7b8, 0x5c5b, 0x9d,0xe7, 0x22,0xe3,0x3c,0xb9,0x70,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("49a07732-e7b8-5c5b-9de7-22e33cb97004")
            IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs*, ABI::Windows::ApplicationModel::Activation::IBackgroundActivatedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0x49a07732, 0xe7b8, 0x5c5b, 0x9d,0xe7, 0x22,0xe3,0x3c,0xb9,0x70,0x04)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_BackgroundActivatedEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define IEventHandler_BackgroundActivatedEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl
#define IEventHandler_BackgroundActivatedEventArgs __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define IEventHandler_BackgroundActivatedEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface
#define IEventHandler_BackgroundActivatedEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef
#define IEventHandler_BackgroundActivatedEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release
#define IEventHandler_BackgroundActivatedEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0xd4f89768, 0x688f, 0x59ec, 0xbf,0x24, 0xc2,0xaf,0x6a,0x31,0x0f,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d4f89768-688f-59ec-bf24-c2af6a310fa4")
            ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*, ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs*, ABI::Windows::ApplicationModel::Activation::IBackgroundActivatedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0xd4f89768, 0x688f, 0x59ec, 0xbf,0x24, 0xc2,0xaf,0x6a,0x31,0x0f,0xa4)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *sender,
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*,ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup *sender,__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs IID___FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgsVtbl __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs_AddRef __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs_Release __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release
#define ITypedEventHandler_BackgroundTaskRegistrationGroup_BackgroundActivatedEventArgs_Invoke __FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMapView<GUID,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration* > interface
 */
#ifndef ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_INTERFACE_DEFINED__
#define ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration, 0x2c08602f, 0x40b1, 0x5e97, 0xae,0x21, 0x5c,0x04,0xd7,0xfb,0x82,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("2c08602f-40b1-5e97-ae21-5c04d7fb829c")
                IMapView<GUID,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration* > : IMapView_impl<GUID, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration*, ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration, 0x2c08602f, 0x40b1, 0x5e97, 0xae,0x21, 0x5c,0x04,0xd7,0xfb,0x82,0x9c)
#endif
#else
typedef struct __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This,
        TrustLevel *trustLevel);

    /*** IMapView<GUID,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This,
        GUID key,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This,
        GUID key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration *This,
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration **first,
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration **second);

    END_INTERFACE
} __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationVtbl;

interface __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration {
    CONST_VTBL __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<GUID,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration* > methods ***/
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_QueryInterface(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_AddRef(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Release(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetIids(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetRuntimeClassName(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetTrustLevel(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<GUID,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistration* > methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Lookup(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This,GUID key,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_get_Size(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_HasKey(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This,GUID key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Split(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration* This,__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration **first,__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_GUID_BackgroundTaskRegistration IID___FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration
#define IMapView_GUID_BackgroundTaskRegistrationVtbl __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationVtbl
#define IMapView_GUID_BackgroundTaskRegistration __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration
#define IMapView_GUID_BackgroundTaskRegistration_QueryInterface __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_QueryInterface
#define IMapView_GUID_BackgroundTaskRegistration_AddRef __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_AddRef
#define IMapView_GUID_BackgroundTaskRegistration_Release __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Release
#define IMapView_GUID_BackgroundTaskRegistration_GetIids __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetIids
#define IMapView_GUID_BackgroundTaskRegistration_GetRuntimeClassName __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetRuntimeClassName
#define IMapView_GUID_BackgroundTaskRegistration_GetTrustLevel __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_GetTrustLevel
#define IMapView_GUID_BackgroundTaskRegistration_Lookup __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Lookup
#define IMapView_GUID_BackgroundTaskRegistration_get_Size __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_get_Size
#define IMapView_GUID_BackgroundTaskRegistration_HasKey __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_HasKey
#define IMapView_GUID_BackgroundTaskRegistration_Split __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistration_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMapView<GUID,ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* > interface
 */
#ifndef ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_INTERFACE_DEFINED__
#define ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration, 0x78c880f6, 0xa7dc, 0x5172, 0x89,0xda, 0x77,0x49,0xfc,0x82,0xaa,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("78c880f6-a7dc-5172-89da-7749fc82aa82")
                IMapView<GUID,ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* > : IMapView_impl<GUID, ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration, 0x78c880f6, 0xa7dc, 0x5172, 0x89,0xda, 0x77,0x49,0xfc,0x82,0xaa,0x82)
#endif
#else
typedef struct __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistrationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This,
        TrustLevel *trustLevel);

    /*** IMapView<GUID,ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This,
        GUID key,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This,
        GUID key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration *This,
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration **first,
        __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration **second);

    END_INTERFACE
} __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistrationVtbl;

interface __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration {
    CONST_VTBL __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistrationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<GUID,ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* > methods ***/
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_QueryInterface(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_AddRef(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Release(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetIids(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetRuntimeClassName(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetTrustLevel(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<GUID,ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistration* > methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Lookup(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This,GUID key,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistration **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_get_Size(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_HasKey(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This,GUID key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Split(__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration* This,__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration **first,__FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_GUID_IBackgroundTaskRegistration IID___FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration
#define IMapView_GUID_IBackgroundTaskRegistrationVtbl __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistrationVtbl
#define IMapView_GUID_IBackgroundTaskRegistration __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration
#define IMapView_GUID_IBackgroundTaskRegistration_QueryInterface __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_QueryInterface
#define IMapView_GUID_IBackgroundTaskRegistration_AddRef __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_AddRef
#define IMapView_GUID_IBackgroundTaskRegistration_Release __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Release
#define IMapView_GUID_IBackgroundTaskRegistration_GetIids __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetIids
#define IMapView_GUID_IBackgroundTaskRegistration_GetRuntimeClassName __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetRuntimeClassName
#define IMapView_GUID_IBackgroundTaskRegistration_GetTrustLevel __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_GetTrustLevel
#define IMapView_GUID_IBackgroundTaskRegistration_Lookup __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Lookup
#define IMapView_GUID_IBackgroundTaskRegistration_get_Size __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_get_Size
#define IMapView_GUID_IBackgroundTaskRegistration_HasKey __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_HasKey
#define IMapView_GUID_IBackgroundTaskRegistration_Split __FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_GUID_Windows__CApplicationModel__CBackground__CIBackgroundTaskRegistration_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMapView<HSTRING,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup* > interface
 */
#ifndef ____FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_INTERFACE_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup, 0xf6a9dc12, 0x01f7, 0x54f0, 0xa2,0x57, 0xc4,0x04,0x81,0x5b,0x9c,0x1c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f6a9dc12-01f7-54f0-a257-c404815b9c1c")
                IMapView<HSTRING,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup* > : IMapView_impl<HSTRING, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup*, ABI::Windows::ApplicationModel::Background::IBackgroundTaskRegistrationGroup* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup, 0xf6a9dc12, 0x01f7, 0x54f0, 0xa2,0x57, 0xc4,0x04,0x81,0x5b,0x9c,0x1c)
#endif
#else
typedef struct __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This,
        TrustLevel *trustLevel);

    /*** IMapView<HSTRING,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This,
        HSTRING key,
        __x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This,
        HSTRING key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup *This,
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup **first,
        __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup **second);

    END_INTERFACE
} __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroupVtbl;

interface __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup {
    CONST_VTBL __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<HSTRING,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup* > methods ***/
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_QueryInterface(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_AddRef(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Release(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetIids(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetRuntimeClassName(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetTrustLevel(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<HSTRING,ABI::Windows::ApplicationModel::Background::BackgroundTaskRegistrationGroup* > methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Lookup(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This,HSTRING key,__x_ABI_CWindows_CApplicationModel_CBackground_CIBackgroundTaskRegistrationGroup **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_get_Size(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_HasKey(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This,HSTRING key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Split(__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup* This,__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup **first,__FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_HSTRING_BackgroundTaskRegistrationGroup IID___FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup
#define IMapView_HSTRING_BackgroundTaskRegistrationGroupVtbl __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroupVtbl
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_QueryInterface __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_QueryInterface
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_AddRef __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_AddRef
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_Release __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Release
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_GetIids __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetIids
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_GetRuntimeClassName __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetRuntimeClassName
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_GetTrustLevel __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_GetTrustLevel
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_Lookup __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Lookup
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_get_Size __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_get_Size
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_HasKey __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_HasKey
#define IMapView_HSTRING_BackgroundTaskRegistrationGroup_Split __FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_HSTRING_Windows__CApplicationModel__CBackground__CBackgroundTaskRegistrationGroup_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_applicationmodel_background_h__ */
