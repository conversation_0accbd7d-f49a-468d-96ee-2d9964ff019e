#
# Copyright (C) 2012
#     <PERSON> <<EMAIL>>
#     <PERSON> <<EMAIL>>
#     <PERSON> <<EMAIL>>
#     <PERSON> <<EMAIL>>
#     <PERSON> <<EMAIL>>
#
## Test git-remote-mediawiki

# The default target of this Makefile is...
all:: test

-include ../../../config.mak.autogen
-include ../../../config.mak

T = $(wildcard t[0-9][0-9][0-9][0-9]-*.sh)

.PHONY: help test clean all

help:
	@echo 'Run "$(MAKE) test" to launch test scripts'
	@echo 'Run "$(MAKE) clean" to remove trash folders'

test:
	@for t in $(T); do \
		echo "$$t"; \
		"./$$t" || exit 1; \
	done

clean:
	$(RM) -r 'trash directory'.*
