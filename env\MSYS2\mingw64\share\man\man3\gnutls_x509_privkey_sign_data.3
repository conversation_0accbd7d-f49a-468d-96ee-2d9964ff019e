.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_sign_data" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_sign_data \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_sign_data(gnutls_x509_privkey_t " key ", gnutls_digest_algorithm_t " digest ", unsigned int " flags ", const gnutls_datum_t * " data ", void * " signature ", size_t * " signature_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
a key
.IP "gnutls_digest_algorithm_t digest" 12
should be a digest algorithm
.IP "unsigned int flags" 12
should be 0 for now
.IP "const gnutls_datum_t * data" 12
holds the data to be signed
.IP "void * signature" 12
will contain the signature
.IP "size_t * signature_size" 12
holds the size of signature (and will be replaced
by the new size)
.SH "DESCRIPTION"
This function will sign the given data using a signature algorithm
supported by the private key. Signature algorithms are always used
together with a hash functions.  Different hash functions may be
used for the RSA algorithm, but only SHA\-1 for the DSA keys.

If the buffer provided is not long enough to hold the output, then
* \fIsignature_size\fP is updated and \fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP will
be returned.

Use \fBgnutls_x509_crt_get_preferred_hash_algorithm()\fP to determine
the hash algorithm.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
