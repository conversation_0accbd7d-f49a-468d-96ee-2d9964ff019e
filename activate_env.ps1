Write-Host "正在激活RNNoise Python环境..." -ForegroundColor Green
.\env\Scripts\Activate.ps1
Write-Host ""
Write-Host "环境已激活！" -ForegroundColor Green
Write-Host "可用的训练脚本：" -ForegroundColor Yellow
Write-Host "  - python training/rnn_train.py (Keras版本)" -ForegroundColor Cyan
Write-Host "  - python torch/rnnoise/train_rnnoise.py (PyTorch版本)" -ForegroundColor Cyan
Write-Host ""
Write-Host "要测试环境，运行: python test_environment.py" -ForegroundColor Magenta
