/*** Autogenerated by WIDL 10.8 from include/windows.globalization.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_globalization_h__
#define __windows_globalization_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CGlobalization_CICalendar_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendar_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CICalendar __x_ABI_CWindows_CGlobalization_CICalendar;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CICalendar ABI::Windows::Globalization::ICalendar
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ICalendar;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CICalendarFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendarFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CICalendarFactory __x_ABI_CWindows_CGlobalization_CICalendarFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory ABI::Windows::Globalization::ICalendarFactory
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ICalendarFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CICalendarFactory2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendarFactory2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CICalendarFactory2 __x_ABI_CWindows_CGlobalization_CICalendarFactory2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2 ABI::Windows::Globalization::ICalendarFactory2
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ICalendarFactory2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics ABI::Windows::Globalization::IApplicationLanguagesStatics
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IApplicationLanguagesStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 ABI::Windows::Globalization::IApplicationLanguagesStatics2
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IApplicationLanguagesStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguage __x_ABI_CWindows_CGlobalization_CILanguage;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguage ABI::Windows::Globalization::ILanguage
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguage;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguage2 __x_ABI_CWindows_CGlobalization_CILanguage2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguage2 ABI::Windows::Globalization::ILanguage2
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguage2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage3_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguage3 __x_ABI_CWindows_CGlobalization_CILanguage3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguage3 ABI::Windows::Globalization::ILanguage3
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguage3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags ABI::Windows::Globalization::ILanguageExtensionSubtags
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageExtensionSubtags;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageFactory __x_ABI_CWindows_CGlobalization_CILanguageFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory ABI::Windows::Globalization::ILanguageFactory
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageStatics __x_ABI_CWindows_CGlobalization_CILanguageStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics ABI::Windows::Globalization::ILanguageStatics
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageStatics2 __x_ABI_CWindows_CGlobalization_CILanguageStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2 ABI::Windows::Globalization::ILanguageStatics2
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics3_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageStatics3 __x_ABI_CWindows_CGlobalization_CILanguageStatics3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3 ABI::Windows::Globalization::ILanguageStatics3
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageStatics3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegion_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegion_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIGeographicRegion __x_ABI_CWindows_CGlobalization_CIGeographicRegion;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion ABI::Windows::Globalization::IGeographicRegion
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IGeographicRegion;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory ABI::Windows::Globalization::IGeographicRegionFactory
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IGeographicRegionFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics ABI::Windows::Globalization::IGeographicRegionStatics
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IGeographicRegionStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar ABI::Windows::Globalization::ITimeZoneOnCalendar
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ITimeZoneOnCalendar;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CCalendar_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CCalendar_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Globalization {
            class Calendar;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGlobalization_CCalendar __x_ABI_CWindows_CGlobalization_CCalendar;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGlobalization_CCalendar_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGlobalization_CApplicationLanguages_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CApplicationLanguages_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Globalization {
            class ApplicationLanguages;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGlobalization_CApplicationLanguages __x_ABI_CWindows_CGlobalization_CApplicationLanguages;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGlobalization_CApplicationLanguages_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGlobalization_CLanguage_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CLanguage_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Globalization {
            class Language;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGlobalization_CLanguage __x_ABI_CWindows_CGlobalization_CLanguage;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGlobalization_CLanguage_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGlobalization_CGeographicRegion_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CGeographicRegion_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Globalization {
            class GeographicRegion;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGlobalization_CGeographicRegion __x_ABI_CWindows_CGlobalization_CGeographicRegion;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGlobalization_CGeographicRegion_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGlobalization__CLanguage __FIIterable_1_Windows__CGlobalization__CLanguage;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGlobalization__CLanguage ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Globalization::Language* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGlobalization__CLanguage __FIIterator_1_Windows__CGlobalization__CLanguage;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGlobalization__CLanguage ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Globalization::Language* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGlobalization__CLanguage __FIVectorView_1_Windows__CGlobalization__CLanguage;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGlobalization__CLanguage ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Globalization::Language* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
#define ____FIVector_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGlobalization__CLanguage __FIVector_1_Windows__CGlobalization__CLanguage;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGlobalization__CLanguage ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Globalization::Language* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.system.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGlobalization_CDayOfWeek __x_ABI_CWindows_CGlobalization_CDayOfWeek;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGlobalization_CLanguageLayoutDirection __x_ABI_CWindows_CGlobalization_CLanguageLayoutDirection;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics ABI::Windows::Globalization::IApplicationLanguagesStatics
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IApplicationLanguagesStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 ABI::Windows::Globalization::IApplicationLanguagesStatics2
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IApplicationLanguagesStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CICalendar_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendar_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CICalendar __x_ABI_CWindows_CGlobalization_CICalendar;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CICalendar ABI::Windows::Globalization::ICalendar
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ICalendar;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CICalendarFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendarFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CICalendarFactory __x_ABI_CWindows_CGlobalization_CICalendarFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory ABI::Windows::Globalization::ICalendarFactory
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ICalendarFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CICalendarFactory2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendarFactory2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CICalendarFactory2 __x_ABI_CWindows_CGlobalization_CICalendarFactory2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2 ABI::Windows::Globalization::ICalendarFactory2
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ICalendarFactory2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguage __x_ABI_CWindows_CGlobalization_CILanguage;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguage ABI::Windows::Globalization::ILanguage
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguage;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguage2 __x_ABI_CWindows_CGlobalization_CILanguage2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguage2 ABI::Windows::Globalization::ILanguage2
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguage2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage3_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguage3 __x_ABI_CWindows_CGlobalization_CILanguage3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguage3 ABI::Windows::Globalization::ILanguage3
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguage3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags ABI::Windows::Globalization::ILanguageExtensionSubtags
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageExtensionSubtags;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageFactory __x_ABI_CWindows_CGlobalization_CILanguageFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory ABI::Windows::Globalization::ILanguageFactory
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageStatics __x_ABI_CWindows_CGlobalization_CILanguageStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics ABI::Windows::Globalization::ILanguageStatics
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageStatics2 __x_ABI_CWindows_CGlobalization_CILanguageStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2 ABI::Windows::Globalization::ILanguageStatics2
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics3_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CILanguageStatics3 __x_ABI_CWindows_CGlobalization_CILanguageStatics3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3 ABI::Windows::Globalization::ILanguageStatics3
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ILanguageStatics3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegion_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegion_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIGeographicRegion __x_ABI_CWindows_CGlobalization_CIGeographicRegion;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion ABI::Windows::Globalization::IGeographicRegion
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IGeographicRegion;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory ABI::Windows::Globalization::IGeographicRegionFactory
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IGeographicRegionFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics ABI::Windows::Globalization::IGeographicRegionStatics
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface IGeographicRegionStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_FWD_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar ABI::Windows::Globalization::ITimeZoneOnCalendar
namespace ABI {
    namespace Windows {
        namespace Globalization {
            interface ITimeZoneOnCalendar;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGlobalization__CLanguage __FIIterable_1_Windows__CGlobalization__CLanguage;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGlobalization__CLanguage ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Globalization::Language* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGlobalization__CLanguage __FIIterator_1_Windows__CGlobalization__CLanguage;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGlobalization__CLanguage ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Globalization::Language* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CGlobalization__CLanguage __FIVectorView_1_Windows__CGlobalization__CLanguage;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CGlobalization__CLanguage ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Globalization::Language* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
#define ____FIVector_1_Windows__CGlobalization__CLanguage_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CGlobalization__CLanguage __FIVector_1_Windows__CGlobalization__CLanguage;
#ifdef __cplusplus
#define __FIVector_1_Windows__CGlobalization__CLanguage ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Globalization::Language* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            enum DayOfWeek {
                DayOfWeek_Sunday = 0,
                DayOfWeek_Monday = 1,
                DayOfWeek_Tuesday = 2,
                DayOfWeek_Wednesday = 3,
                DayOfWeek_Thursday = 4,
                DayOfWeek_Friday = 5,
                DayOfWeek_Saturday = 6
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGlobalization_CDayOfWeek {
    DayOfWeek_Sunday = 0,
    DayOfWeek_Monday = 1,
    DayOfWeek_Tuesday = 2,
    DayOfWeek_Wednesday = 3,
    DayOfWeek_Thursday = 4,
    DayOfWeek_Friday = 5,
    DayOfWeek_Saturday = 6
};
#ifdef WIDL_using_Windows_Globalization
#define DayOfWeek __x_ABI_CWindows_CGlobalization_CDayOfWeek
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            enum LanguageLayoutDirection {
                LanguageLayoutDirection_Ltr = 0,
                LanguageLayoutDirection_Rtl = 1,
                LanguageLayoutDirection_TtbLtr = 2,
                LanguageLayoutDirection_TtbRtl = 3
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGlobalization_CLanguageLayoutDirection {
    LanguageLayoutDirection_Ltr = 0,
    LanguageLayoutDirection_Rtl = 1,
    LanguageLayoutDirection_TtbLtr = 2,
    LanguageLayoutDirection_TtbRtl = 3
};
#ifdef WIDL_using_Windows_Globalization
#define LanguageLayoutDirection __x_ABI_CWindows_CGlobalization_CLanguageLayoutDirection
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
/*****************************************************************************
 * ICalendar interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CICalendar_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendar_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CICalendar, 0xca30221d, 0x86d9, 0x40fb, 0xa2,0x6b, 0xd4,0x4e,0xb7,0xcf,0x08,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("ca30221d-86d9-40fb-a26b-d44eb7cf08ea")
            ICalendar : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE Clone(
                    ABI::Windows::Globalization::ICalendar **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE SetToMin(
                    ) = 0;

                virtual HRESULT STDMETHODCALLTYPE SetToMax(
                    ) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Languages(
                    ABI::Windows::Foundation::Collections::IVectorView<HSTRING > **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumeralSystem(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_NumeralSystem(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetCalendarSystem(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE ChangeCalendarSystem(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetClock(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE ChangeClock(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetDateTime(
                    ABI::Windows::Foundation::DateTime *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE SetDateTime(
                    ABI::Windows::Foundation::DateTime value) = 0;

                virtual HRESULT STDMETHODCALLTYPE SetToNow(
                    ) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FirstEra(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LastEra(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumberOfEras(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Era(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Era(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddEras(
                    INT32 eras) = 0;

                virtual HRESULT STDMETHODCALLTYPE EraAsFullString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE EraAsString(
                    INT32 ideal_length,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FirstYearInThisEra(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LastYearInThisEra(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumberOfYearsInThisEra(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Year(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Year(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddYears(
                    INT32 years) = 0;

                virtual HRESULT STDMETHODCALLTYPE YearAsString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE YearAsTruncatedString(
                    INT32 remaining_digits,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE YearAsPaddedString(
                    INT32 min_digits,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FirstMonthInThisYear(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LastMonthInThisYear(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumberOfMonthsInThisYear(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Month(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Month(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddMonths(
                    INT32 months) = 0;

                virtual HRESULT STDMETHODCALLTYPE MonthAsFullString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE MonthAsString(
                    INT32 ideal_length,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE MonthAsFullSoloString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE MonthAsSoloString(
                    INT32 ideal_length,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE MonthAsNumericString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE MonthAsPaddedNumericString(
                    INT32 min_digits,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddWeeks(
                    INT32 weeks) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FirstDayInThisMonth(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LastDayInThisMonth(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumberOfDaysInThisMonth(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Day(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Day(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddDays(
                    INT32 days) = 0;

                virtual HRESULT STDMETHODCALLTYPE DayAsString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE DayAsPaddedString(
                    INT32 min_digits,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DayOfWeek(
                    ABI::Windows::Globalization::DayOfWeek *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE DayOfWeekAsFullString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE DayOfWeekAsString(
                    INT32 ideal_length,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE DayOfWeekAsFullSoloString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE DayOfWeekAsSoloString(
                    INT32 ideal_length,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FirstPeriodInThisDay(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LastPeriodInThisDay(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumberOfPeriodsInThisDay(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Period(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Period(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddPeriods(
                    INT32 periods) = 0;

                virtual HRESULT STDMETHODCALLTYPE PeriodAsFullString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE PeriodAsString(
                    INT32 ideal_length,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FirstHourInThisPeriod(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LastHourInThisPeriod(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumberOfHoursInThisPeriod(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Hour(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Hour(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddHours(
                    INT32 hours) = 0;

                virtual HRESULT STDMETHODCALLTYPE HourAsString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE HourAsPaddedString(
                    INT32 min_digits,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Minute(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Minute(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddMinutes(
                    INT32 minutes) = 0;

                virtual HRESULT STDMETHODCALLTYPE MinuteAsString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE MinuteAsPaddedString(
                    INT32 min_digits,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Second(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Second(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddSeconds(
                    INT32 seconds) = 0;

                virtual HRESULT STDMETHODCALLTYPE SecondAsString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE SecondAsPaddedString(
                    INT32 min_digits,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Nanosecond(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Nanosecond(
                    INT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE AddNanoseconds(
                    INT32 nanoseconds) = 0;

                virtual HRESULT STDMETHODCALLTYPE NanosecondAsString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE NanosecondAsPaddedString(
                    INT32 min_digits,
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE Compare(
                    ABI::Windows::Globalization::ICalendar *other,
                    INT32 *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE CompareDateTime(
                    ABI::Windows::Foundation::DateTime other,
                    INT32 *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE CopyTo(
                    ABI::Windows::Globalization::ICalendar *other) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FirstMinuteInThisHour(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LastMinuteInThisHour(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumberOfMinutesInThisHour(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FirstSecondInThisMinute(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LastSecondInThisMinute(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NumberOfSecondsInThisMinute(
                    INT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_ResolvedLanguage(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsDaylightSavingTime(
                    boolean *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CICalendar, 0xca30221d, 0x86d9, 0x40fb, 0xa2,0x6b, 0xd4,0x4e,0xb7,0xcf,0x08,0xea)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CICalendarVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        TrustLevel *trustLevel);

    /*** ICalendar methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        __x_ABI_CWindows_CGlobalization_CICalendar **value);

    HRESULT (STDMETHODCALLTYPE *SetToMin)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This);

    HRESULT (STDMETHODCALLTYPE *SetToMax)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This);

    HRESULT (STDMETHODCALLTYPE *get_Languages)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        __FIVectorView_1_HSTRING **value);

    HRESULT (STDMETHODCALLTYPE *get_NumeralSystem)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_NumeralSystem)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *GetCalendarSystem)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *ChangeCalendarSystem)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *GetClock)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *ChangeClock)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *GetDateTime)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        __x_ABI_CWindows_CFoundation_CDateTime *result);

    HRESULT (STDMETHODCALLTYPE *SetDateTime)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        __x_ABI_CWindows_CFoundation_CDateTime value);

    HRESULT (STDMETHODCALLTYPE *SetToNow)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This);

    HRESULT (STDMETHODCALLTYPE *get_FirstEra)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastEra)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfEras)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Era)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Era)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddEras)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 eras);

    HRESULT (STDMETHODCALLTYPE *EraAsFullString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *EraAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 ideal_length,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *get_FirstYearInThisEra)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastYearInThisEra)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfYearsInThisEra)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Year)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Year)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddYears)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 years);

    HRESULT (STDMETHODCALLTYPE *YearAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *YearAsTruncatedString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 remaining_digits,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *YearAsPaddedString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 min_digits,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *get_FirstMonthInThisYear)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastMonthInThisYear)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfMonthsInThisYear)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Month)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Month)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddMonths)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 months);

    HRESULT (STDMETHODCALLTYPE *MonthAsFullString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *MonthAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 ideal_length,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *MonthAsFullSoloString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *MonthAsSoloString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 ideal_length,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *MonthAsNumericString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *MonthAsPaddedNumericString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 min_digits,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *AddWeeks)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 weeks);

    HRESULT (STDMETHODCALLTYPE *get_FirstDayInThisMonth)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastDayInThisMonth)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfDaysInThisMonth)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Day)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Day)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddDays)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 days);

    HRESULT (STDMETHODCALLTYPE *DayAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *DayAsPaddedString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 min_digits,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *get_DayOfWeek)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        __x_ABI_CWindows_CGlobalization_CDayOfWeek *value);

    HRESULT (STDMETHODCALLTYPE *DayOfWeekAsFullString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *DayOfWeekAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 ideal_length,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *DayOfWeekAsFullSoloString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *DayOfWeekAsSoloString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 ideal_length,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *get_FirstPeriodInThisDay)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastPeriodInThisDay)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfPeriodsInThisDay)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Period)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Period)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddPeriods)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 periods);

    HRESULT (STDMETHODCALLTYPE *PeriodAsFullString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *PeriodAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 ideal_length,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *get_FirstHourInThisPeriod)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastHourInThisPeriod)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfHoursInThisPeriod)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Hour)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Hour)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddHours)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 hours);

    HRESULT (STDMETHODCALLTYPE *HourAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *HourAsPaddedString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 min_digits,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *get_Minute)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Minute)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddMinutes)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 minutes);

    HRESULT (STDMETHODCALLTYPE *MinuteAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *MinuteAsPaddedString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 min_digits,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *get_Second)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Second)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddSeconds)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 seconds);

    HRESULT (STDMETHODCALLTYPE *SecondAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *SecondAsPaddedString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 min_digits,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *get_Nanosecond)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Nanosecond)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *AddNanoseconds)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 nanoseconds);

    HRESULT (STDMETHODCALLTYPE *NanosecondAsString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *NanosecondAsPaddedString)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 min_digits,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *Compare)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        __x_ABI_CWindows_CGlobalization_CICalendar *other,
        INT32 *result);

    HRESULT (STDMETHODCALLTYPE *CompareDateTime)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        __x_ABI_CWindows_CFoundation_CDateTime other,
        INT32 *result);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        __x_ABI_CWindows_CGlobalization_CICalendar *other);

    HRESULT (STDMETHODCALLTYPE *get_FirstMinuteInThisHour)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastMinuteInThisHour)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfMinutesInThisHour)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_FirstSecondInThisMinute)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastSecondInThisMinute)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfSecondsInThisMinute)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_ResolvedLanguage)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_IsDaylightSavingTime)(
        __x_ABI_CWindows_CGlobalization_CICalendar *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CICalendarVtbl;

interface __x_ABI_CWindows_CGlobalization_CICalendar {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CICalendarVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CICalendar_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendar_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CICalendar_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CICalendar_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICalendar methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendar_Clone(This,value) (This)->lpVtbl->Clone(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_SetToMin(This) (This)->lpVtbl->SetToMin(This)
#define __x_ABI_CWindows_CGlobalization_CICalendar_SetToMax(This) (This)->lpVtbl->SetToMax(This)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Languages(This,value) (This)->lpVtbl->get_Languages(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumeralSystem(This,value) (This)->lpVtbl->get_NumeralSystem(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_NumeralSystem(This,value) (This)->lpVtbl->put_NumeralSystem(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_GetCalendarSystem(This,value) (This)->lpVtbl->GetCalendarSystem(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_ChangeCalendarSystem(This,value) (This)->lpVtbl->ChangeCalendarSystem(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_GetClock(This,value) (This)->lpVtbl->GetClock(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_ChangeClock(This,value) (This)->lpVtbl->ChangeClock(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_GetDateTime(This,result) (This)->lpVtbl->GetDateTime(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_SetDateTime(This,value) (This)->lpVtbl->SetDateTime(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_SetToNow(This) (This)->lpVtbl->SetToNow(This)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstEra(This,value) (This)->lpVtbl->get_FirstEra(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_LastEra(This,value) (This)->lpVtbl->get_LastEra(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfEras(This,value) (This)->lpVtbl->get_NumberOfEras(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Era(This,value) (This)->lpVtbl->get_Era(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Era(This,value) (This)->lpVtbl->put_Era(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddEras(This,eras) (This)->lpVtbl->AddEras(This,eras)
#define __x_ABI_CWindows_CGlobalization_CICalendar_EraAsFullString(This,result) (This)->lpVtbl->EraAsFullString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_EraAsString(This,ideal_length,result) (This)->lpVtbl->EraAsString(This,ideal_length,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstYearInThisEra(This,value) (This)->lpVtbl->get_FirstYearInThisEra(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_LastYearInThisEra(This,value) (This)->lpVtbl->get_LastYearInThisEra(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfYearsInThisEra(This,value) (This)->lpVtbl->get_NumberOfYearsInThisEra(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Year(This,value) (This)->lpVtbl->get_Year(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Year(This,value) (This)->lpVtbl->put_Year(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddYears(This,years) (This)->lpVtbl->AddYears(This,years)
#define __x_ABI_CWindows_CGlobalization_CICalendar_YearAsString(This,result) (This)->lpVtbl->YearAsString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_YearAsTruncatedString(This,remaining_digits,result) (This)->lpVtbl->YearAsTruncatedString(This,remaining_digits,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_YearAsPaddedString(This,min_digits,result) (This)->lpVtbl->YearAsPaddedString(This,min_digits,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstMonthInThisYear(This,value) (This)->lpVtbl->get_FirstMonthInThisYear(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_LastMonthInThisYear(This,value) (This)->lpVtbl->get_LastMonthInThisYear(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfMonthsInThisYear(This,value) (This)->lpVtbl->get_NumberOfMonthsInThisYear(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Month(This,value) (This)->lpVtbl->get_Month(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Month(This,value) (This)->lpVtbl->put_Month(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddMonths(This,months) (This)->lpVtbl->AddMonths(This,months)
#define __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsFullString(This,result) (This)->lpVtbl->MonthAsFullString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsString(This,ideal_length,result) (This)->lpVtbl->MonthAsString(This,ideal_length,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsFullSoloString(This,result) (This)->lpVtbl->MonthAsFullSoloString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsSoloString(This,ideal_length,result) (This)->lpVtbl->MonthAsSoloString(This,ideal_length,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsNumericString(This,result) (This)->lpVtbl->MonthAsNumericString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsPaddedNumericString(This,min_digits,result) (This)->lpVtbl->MonthAsPaddedNumericString(This,min_digits,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddWeeks(This,weeks) (This)->lpVtbl->AddWeeks(This,weeks)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstDayInThisMonth(This,value) (This)->lpVtbl->get_FirstDayInThisMonth(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_LastDayInThisMonth(This,value) (This)->lpVtbl->get_LastDayInThisMonth(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfDaysInThisMonth(This,value) (This)->lpVtbl->get_NumberOfDaysInThisMonth(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Day(This,value) (This)->lpVtbl->get_Day(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Day(This,value) (This)->lpVtbl->put_Day(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddDays(This,days) (This)->lpVtbl->AddDays(This,days)
#define __x_ABI_CWindows_CGlobalization_CICalendar_DayAsString(This,result) (This)->lpVtbl->DayAsString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_DayAsPaddedString(This,min_digits,result) (This)->lpVtbl->DayAsPaddedString(This,min_digits,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_DayOfWeek(This,value) (This)->lpVtbl->get_DayOfWeek(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsFullString(This,result) (This)->lpVtbl->DayOfWeekAsFullString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsString(This,ideal_length,result) (This)->lpVtbl->DayOfWeekAsString(This,ideal_length,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsFullSoloString(This,result) (This)->lpVtbl->DayOfWeekAsFullSoloString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsSoloString(This,ideal_length,result) (This)->lpVtbl->DayOfWeekAsSoloString(This,ideal_length,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstPeriodInThisDay(This,value) (This)->lpVtbl->get_FirstPeriodInThisDay(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_LastPeriodInThisDay(This,value) (This)->lpVtbl->get_LastPeriodInThisDay(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfPeriodsInThisDay(This,value) (This)->lpVtbl->get_NumberOfPeriodsInThisDay(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Period(This,value) (This)->lpVtbl->get_Period(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Period(This,value) (This)->lpVtbl->put_Period(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddPeriods(This,periods) (This)->lpVtbl->AddPeriods(This,periods)
#define __x_ABI_CWindows_CGlobalization_CICalendar_PeriodAsFullString(This,result) (This)->lpVtbl->PeriodAsFullString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_PeriodAsString(This,ideal_length,result) (This)->lpVtbl->PeriodAsString(This,ideal_length,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstHourInThisPeriod(This,value) (This)->lpVtbl->get_FirstHourInThisPeriod(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_LastHourInThisPeriod(This,value) (This)->lpVtbl->get_LastHourInThisPeriod(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfHoursInThisPeriod(This,value) (This)->lpVtbl->get_NumberOfHoursInThisPeriod(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Hour(This,value) (This)->lpVtbl->get_Hour(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Hour(This,value) (This)->lpVtbl->put_Hour(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddHours(This,hours) (This)->lpVtbl->AddHours(This,hours)
#define __x_ABI_CWindows_CGlobalization_CICalendar_HourAsString(This,result) (This)->lpVtbl->HourAsString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_HourAsPaddedString(This,min_digits,result) (This)->lpVtbl->HourAsPaddedString(This,min_digits,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Minute(This,value) (This)->lpVtbl->get_Minute(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Minute(This,value) (This)->lpVtbl->put_Minute(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddMinutes(This,minutes) (This)->lpVtbl->AddMinutes(This,minutes)
#define __x_ABI_CWindows_CGlobalization_CICalendar_MinuteAsString(This,result) (This)->lpVtbl->MinuteAsString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_MinuteAsPaddedString(This,min_digits,result) (This)->lpVtbl->MinuteAsPaddedString(This,min_digits,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Second(This,value) (This)->lpVtbl->get_Second(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Second(This,value) (This)->lpVtbl->put_Second(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddSeconds(This,seconds) (This)->lpVtbl->AddSeconds(This,seconds)
#define __x_ABI_CWindows_CGlobalization_CICalendar_SecondAsString(This,result) (This)->lpVtbl->SecondAsString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_SecondAsPaddedString(This,min_digits,result) (This)->lpVtbl->SecondAsPaddedString(This,min_digits,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_Nanosecond(This,value) (This)->lpVtbl->get_Nanosecond(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_put_Nanosecond(This,value) (This)->lpVtbl->put_Nanosecond(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_AddNanoseconds(This,nanoseconds) (This)->lpVtbl->AddNanoseconds(This,nanoseconds)
#define __x_ABI_CWindows_CGlobalization_CICalendar_NanosecondAsString(This,result) (This)->lpVtbl->NanosecondAsString(This,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_NanosecondAsPaddedString(This,min_digits,result) (This)->lpVtbl->NanosecondAsPaddedString(This,min_digits,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_Compare(This,other,result) (This)->lpVtbl->Compare(This,other,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_CompareDateTime(This,other,result) (This)->lpVtbl->CompareDateTime(This,other,result)
#define __x_ABI_CWindows_CGlobalization_CICalendar_CopyTo(This,other) (This)->lpVtbl->CopyTo(This,other)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstMinuteInThisHour(This,value) (This)->lpVtbl->get_FirstMinuteInThisHour(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_LastMinuteInThisHour(This,value) (This)->lpVtbl->get_LastMinuteInThisHour(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfMinutesInThisHour(This,value) (This)->lpVtbl->get_NumberOfMinutesInThisHour(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstSecondInThisMinute(This,value) (This)->lpVtbl->get_FirstSecondInThisMinute(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_LastSecondInThisMinute(This,value) (This)->lpVtbl->get_LastSecondInThisMinute(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfSecondsInThisMinute(This,value) (This)->lpVtbl->get_NumberOfSecondsInThisMinute(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_ResolvedLanguage(This,value) (This)->lpVtbl->get_ResolvedLanguage(This,value)
#define __x_ABI_CWindows_CGlobalization_CICalendar_get_IsDaylightSavingTime(This,value) (This)->lpVtbl->get_IsDaylightSavingTime(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_QueryInterface(__x_ABI_CWindows_CGlobalization_CICalendar* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CICalendar_AddRef(__x_ABI_CWindows_CGlobalization_CICalendar* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CICalendar_Release(__x_ABI_CWindows_CGlobalization_CICalendar* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_GetIids(__x_ABI_CWindows_CGlobalization_CICalendar* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CICalendar* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICalendar methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_Clone(__x_ABI_CWindows_CGlobalization_CICalendar* This,__x_ABI_CWindows_CGlobalization_CICalendar **value) {
    return This->lpVtbl->Clone(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_SetToMin(__x_ABI_CWindows_CGlobalization_CICalendar* This) {
    return This->lpVtbl->SetToMin(This);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_SetToMax(__x_ABI_CWindows_CGlobalization_CICalendar* This) {
    return This->lpVtbl->SetToMax(This);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Languages(__x_ABI_CWindows_CGlobalization_CICalendar* This,__FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->get_Languages(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumeralSystem(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *value) {
    return This->lpVtbl->get_NumeralSystem(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_NumeralSystem(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING value) {
    return This->lpVtbl->put_NumeralSystem(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_GetCalendarSystem(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *value) {
    return This->lpVtbl->GetCalendarSystem(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_ChangeCalendarSystem(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING value) {
    return This->lpVtbl->ChangeCalendarSystem(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_GetClock(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *value) {
    return This->lpVtbl->GetClock(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_ChangeClock(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING value) {
    return This->lpVtbl->ChangeClock(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_GetDateTime(__x_ABI_CWindows_CGlobalization_CICalendar* This,__x_ABI_CWindows_CFoundation_CDateTime *result) {
    return This->lpVtbl->GetDateTime(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_SetDateTime(__x_ABI_CWindows_CGlobalization_CICalendar* This,__x_ABI_CWindows_CFoundation_CDateTime value) {
    return This->lpVtbl->SetDateTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_SetToNow(__x_ABI_CWindows_CGlobalization_CICalendar* This) {
    return This->lpVtbl->SetToNow(This);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstEra(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_FirstEra(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_LastEra(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_LastEra(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfEras(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_NumberOfEras(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Era(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Era(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Era(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Era(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddEras(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 eras) {
    return This->lpVtbl->AddEras(This,eras);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_EraAsFullString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->EraAsFullString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_EraAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 ideal_length,HSTRING *result) {
    return This->lpVtbl->EraAsString(This,ideal_length,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstYearInThisEra(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_FirstYearInThisEra(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_LastYearInThisEra(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_LastYearInThisEra(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfYearsInThisEra(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_NumberOfYearsInThisEra(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Year(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Year(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Year(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Year(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddYears(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 years) {
    return This->lpVtbl->AddYears(This,years);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_YearAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->YearAsString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_YearAsTruncatedString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 remaining_digits,HSTRING *result) {
    return This->lpVtbl->YearAsTruncatedString(This,remaining_digits,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_YearAsPaddedString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 min_digits,HSTRING *result) {
    return This->lpVtbl->YearAsPaddedString(This,min_digits,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstMonthInThisYear(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_FirstMonthInThisYear(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_LastMonthInThisYear(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_LastMonthInThisYear(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfMonthsInThisYear(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_NumberOfMonthsInThisYear(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Month(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Month(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Month(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Month(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddMonths(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 months) {
    return This->lpVtbl->AddMonths(This,months);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsFullString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->MonthAsFullString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 ideal_length,HSTRING *result) {
    return This->lpVtbl->MonthAsString(This,ideal_length,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsFullSoloString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->MonthAsFullSoloString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsSoloString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 ideal_length,HSTRING *result) {
    return This->lpVtbl->MonthAsSoloString(This,ideal_length,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsNumericString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->MonthAsNumericString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsPaddedNumericString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 min_digits,HSTRING *result) {
    return This->lpVtbl->MonthAsPaddedNumericString(This,min_digits,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddWeeks(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 weeks) {
    return This->lpVtbl->AddWeeks(This,weeks);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstDayInThisMonth(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_FirstDayInThisMonth(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_LastDayInThisMonth(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_LastDayInThisMonth(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfDaysInThisMonth(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_NumberOfDaysInThisMonth(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Day(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Day(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Day(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Day(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddDays(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 days) {
    return This->lpVtbl->AddDays(This,days);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_DayAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->DayAsString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_DayAsPaddedString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 min_digits,HSTRING *result) {
    return This->lpVtbl->DayAsPaddedString(This,min_digits,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_DayOfWeek(__x_ABI_CWindows_CGlobalization_CICalendar* This,__x_ABI_CWindows_CGlobalization_CDayOfWeek *value) {
    return This->lpVtbl->get_DayOfWeek(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsFullString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->DayOfWeekAsFullString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 ideal_length,HSTRING *result) {
    return This->lpVtbl->DayOfWeekAsString(This,ideal_length,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsFullSoloString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->DayOfWeekAsFullSoloString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsSoloString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 ideal_length,HSTRING *result) {
    return This->lpVtbl->DayOfWeekAsSoloString(This,ideal_length,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstPeriodInThisDay(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_FirstPeriodInThisDay(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_LastPeriodInThisDay(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_LastPeriodInThisDay(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfPeriodsInThisDay(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_NumberOfPeriodsInThisDay(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Period(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Period(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Period(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Period(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddPeriods(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 periods) {
    return This->lpVtbl->AddPeriods(This,periods);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_PeriodAsFullString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->PeriodAsFullString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_PeriodAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 ideal_length,HSTRING *result) {
    return This->lpVtbl->PeriodAsString(This,ideal_length,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstHourInThisPeriod(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_FirstHourInThisPeriod(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_LastHourInThisPeriod(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_LastHourInThisPeriod(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfHoursInThisPeriod(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_NumberOfHoursInThisPeriod(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Hour(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Hour(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Hour(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Hour(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddHours(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 hours) {
    return This->lpVtbl->AddHours(This,hours);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_HourAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->HourAsString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_HourAsPaddedString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 min_digits,HSTRING *result) {
    return This->lpVtbl->HourAsPaddedString(This,min_digits,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Minute(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Minute(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Minute(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Minute(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddMinutes(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 minutes) {
    return This->lpVtbl->AddMinutes(This,minutes);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_MinuteAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->MinuteAsString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_MinuteAsPaddedString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 min_digits,HSTRING *result) {
    return This->lpVtbl->MinuteAsPaddedString(This,min_digits,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Second(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Second(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Second(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Second(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddSeconds(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 seconds) {
    return This->lpVtbl->AddSeconds(This,seconds);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_SecondAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->SecondAsString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_SecondAsPaddedString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 min_digits,HSTRING *result) {
    return This->lpVtbl->SecondAsPaddedString(This,min_digits,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_Nanosecond(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_Nanosecond(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_put_Nanosecond(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 value) {
    return This->lpVtbl->put_Nanosecond(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_AddNanoseconds(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 nanoseconds) {
    return This->lpVtbl->AddNanoseconds(This,nanoseconds);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_NanosecondAsString(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *result) {
    return This->lpVtbl->NanosecondAsString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_NanosecondAsPaddedString(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 min_digits,HSTRING *result) {
    return This->lpVtbl->NanosecondAsPaddedString(This,min_digits,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_Compare(__x_ABI_CWindows_CGlobalization_CICalendar* This,__x_ABI_CWindows_CGlobalization_CICalendar *other,INT32 *result) {
    return This->lpVtbl->Compare(This,other,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_CompareDateTime(__x_ABI_CWindows_CGlobalization_CICalendar* This,__x_ABI_CWindows_CFoundation_CDateTime other,INT32 *result) {
    return This->lpVtbl->CompareDateTime(This,other,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_CopyTo(__x_ABI_CWindows_CGlobalization_CICalendar* This,__x_ABI_CWindows_CGlobalization_CICalendar *other) {
    return This->lpVtbl->CopyTo(This,other);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstMinuteInThisHour(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_FirstMinuteInThisHour(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_LastMinuteInThisHour(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_LastMinuteInThisHour(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfMinutesInThisHour(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_NumberOfMinutesInThisHour(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstSecondInThisMinute(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_FirstSecondInThisMinute(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_LastSecondInThisMinute(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_LastSecondInThisMinute(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfSecondsInThisMinute(__x_ABI_CWindows_CGlobalization_CICalendar* This,INT32 *value) {
    return This->lpVtbl->get_NumberOfSecondsInThisMinute(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_ResolvedLanguage(__x_ABI_CWindows_CGlobalization_CICalendar* This,HSTRING *value) {
    return This->lpVtbl->get_ResolvedLanguage(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendar_get_IsDaylightSavingTime(__x_ABI_CWindows_CGlobalization_CICalendar* This,boolean *value) {
    return This->lpVtbl->get_IsDaylightSavingTime(This,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ICalendar IID___x_ABI_CWindows_CGlobalization_CICalendar
#define ICalendarVtbl __x_ABI_CWindows_CGlobalization_CICalendarVtbl
#define ICalendar __x_ABI_CWindows_CGlobalization_CICalendar
#define ICalendar_QueryInterface __x_ABI_CWindows_CGlobalization_CICalendar_QueryInterface
#define ICalendar_AddRef __x_ABI_CWindows_CGlobalization_CICalendar_AddRef
#define ICalendar_Release __x_ABI_CWindows_CGlobalization_CICalendar_Release
#define ICalendar_GetIids __x_ABI_CWindows_CGlobalization_CICalendar_GetIids
#define ICalendar_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CICalendar_GetRuntimeClassName
#define ICalendar_GetTrustLevel __x_ABI_CWindows_CGlobalization_CICalendar_GetTrustLevel
#define ICalendar_Clone __x_ABI_CWindows_CGlobalization_CICalendar_Clone
#define ICalendar_SetToMin __x_ABI_CWindows_CGlobalization_CICalendar_SetToMin
#define ICalendar_SetToMax __x_ABI_CWindows_CGlobalization_CICalendar_SetToMax
#define ICalendar_get_Languages __x_ABI_CWindows_CGlobalization_CICalendar_get_Languages
#define ICalendar_get_NumeralSystem __x_ABI_CWindows_CGlobalization_CICalendar_get_NumeralSystem
#define ICalendar_put_NumeralSystem __x_ABI_CWindows_CGlobalization_CICalendar_put_NumeralSystem
#define ICalendar_GetCalendarSystem __x_ABI_CWindows_CGlobalization_CICalendar_GetCalendarSystem
#define ICalendar_ChangeCalendarSystem __x_ABI_CWindows_CGlobalization_CICalendar_ChangeCalendarSystem
#define ICalendar_GetClock __x_ABI_CWindows_CGlobalization_CICalendar_GetClock
#define ICalendar_ChangeClock __x_ABI_CWindows_CGlobalization_CICalendar_ChangeClock
#define ICalendar_GetDateTime __x_ABI_CWindows_CGlobalization_CICalendar_GetDateTime
#define ICalendar_SetDateTime __x_ABI_CWindows_CGlobalization_CICalendar_SetDateTime
#define ICalendar_SetToNow __x_ABI_CWindows_CGlobalization_CICalendar_SetToNow
#define ICalendar_get_FirstEra __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstEra
#define ICalendar_get_LastEra __x_ABI_CWindows_CGlobalization_CICalendar_get_LastEra
#define ICalendar_get_NumberOfEras __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfEras
#define ICalendar_get_Era __x_ABI_CWindows_CGlobalization_CICalendar_get_Era
#define ICalendar_put_Era __x_ABI_CWindows_CGlobalization_CICalendar_put_Era
#define ICalendar_AddEras __x_ABI_CWindows_CGlobalization_CICalendar_AddEras
#define ICalendar_EraAsFullString __x_ABI_CWindows_CGlobalization_CICalendar_EraAsFullString
#define ICalendar_EraAsString __x_ABI_CWindows_CGlobalization_CICalendar_EraAsString
#define ICalendar_get_FirstYearInThisEra __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstYearInThisEra
#define ICalendar_get_LastYearInThisEra __x_ABI_CWindows_CGlobalization_CICalendar_get_LastYearInThisEra
#define ICalendar_get_NumberOfYearsInThisEra __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfYearsInThisEra
#define ICalendar_get_Year __x_ABI_CWindows_CGlobalization_CICalendar_get_Year
#define ICalendar_put_Year __x_ABI_CWindows_CGlobalization_CICalendar_put_Year
#define ICalendar_AddYears __x_ABI_CWindows_CGlobalization_CICalendar_AddYears
#define ICalendar_YearAsString __x_ABI_CWindows_CGlobalization_CICalendar_YearAsString
#define ICalendar_YearAsTruncatedString __x_ABI_CWindows_CGlobalization_CICalendar_YearAsTruncatedString
#define ICalendar_YearAsPaddedString __x_ABI_CWindows_CGlobalization_CICalendar_YearAsPaddedString
#define ICalendar_get_FirstMonthInThisYear __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstMonthInThisYear
#define ICalendar_get_LastMonthInThisYear __x_ABI_CWindows_CGlobalization_CICalendar_get_LastMonthInThisYear
#define ICalendar_get_NumberOfMonthsInThisYear __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfMonthsInThisYear
#define ICalendar_get_Month __x_ABI_CWindows_CGlobalization_CICalendar_get_Month
#define ICalendar_put_Month __x_ABI_CWindows_CGlobalization_CICalendar_put_Month
#define ICalendar_AddMonths __x_ABI_CWindows_CGlobalization_CICalendar_AddMonths
#define ICalendar_MonthAsFullString __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsFullString
#define ICalendar_MonthAsString __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsString
#define ICalendar_MonthAsFullSoloString __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsFullSoloString
#define ICalendar_MonthAsSoloString __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsSoloString
#define ICalendar_MonthAsNumericString __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsNumericString
#define ICalendar_MonthAsPaddedNumericString __x_ABI_CWindows_CGlobalization_CICalendar_MonthAsPaddedNumericString
#define ICalendar_AddWeeks __x_ABI_CWindows_CGlobalization_CICalendar_AddWeeks
#define ICalendar_get_FirstDayInThisMonth __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstDayInThisMonth
#define ICalendar_get_LastDayInThisMonth __x_ABI_CWindows_CGlobalization_CICalendar_get_LastDayInThisMonth
#define ICalendar_get_NumberOfDaysInThisMonth __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfDaysInThisMonth
#define ICalendar_get_Day __x_ABI_CWindows_CGlobalization_CICalendar_get_Day
#define ICalendar_put_Day __x_ABI_CWindows_CGlobalization_CICalendar_put_Day
#define ICalendar_AddDays __x_ABI_CWindows_CGlobalization_CICalendar_AddDays
#define ICalendar_DayAsString __x_ABI_CWindows_CGlobalization_CICalendar_DayAsString
#define ICalendar_DayAsPaddedString __x_ABI_CWindows_CGlobalization_CICalendar_DayAsPaddedString
#define ICalendar_get_DayOfWeek __x_ABI_CWindows_CGlobalization_CICalendar_get_DayOfWeek
#define ICalendar_DayOfWeekAsFullString __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsFullString
#define ICalendar_DayOfWeekAsString __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsString
#define ICalendar_DayOfWeekAsFullSoloString __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsFullSoloString
#define ICalendar_DayOfWeekAsSoloString __x_ABI_CWindows_CGlobalization_CICalendar_DayOfWeekAsSoloString
#define ICalendar_get_FirstPeriodInThisDay __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstPeriodInThisDay
#define ICalendar_get_LastPeriodInThisDay __x_ABI_CWindows_CGlobalization_CICalendar_get_LastPeriodInThisDay
#define ICalendar_get_NumberOfPeriodsInThisDay __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfPeriodsInThisDay
#define ICalendar_get_Period __x_ABI_CWindows_CGlobalization_CICalendar_get_Period
#define ICalendar_put_Period __x_ABI_CWindows_CGlobalization_CICalendar_put_Period
#define ICalendar_AddPeriods __x_ABI_CWindows_CGlobalization_CICalendar_AddPeriods
#define ICalendar_PeriodAsFullString __x_ABI_CWindows_CGlobalization_CICalendar_PeriodAsFullString
#define ICalendar_PeriodAsString __x_ABI_CWindows_CGlobalization_CICalendar_PeriodAsString
#define ICalendar_get_FirstHourInThisPeriod __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstHourInThisPeriod
#define ICalendar_get_LastHourInThisPeriod __x_ABI_CWindows_CGlobalization_CICalendar_get_LastHourInThisPeriod
#define ICalendar_get_NumberOfHoursInThisPeriod __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfHoursInThisPeriod
#define ICalendar_get_Hour __x_ABI_CWindows_CGlobalization_CICalendar_get_Hour
#define ICalendar_put_Hour __x_ABI_CWindows_CGlobalization_CICalendar_put_Hour
#define ICalendar_AddHours __x_ABI_CWindows_CGlobalization_CICalendar_AddHours
#define ICalendar_HourAsString __x_ABI_CWindows_CGlobalization_CICalendar_HourAsString
#define ICalendar_HourAsPaddedString __x_ABI_CWindows_CGlobalization_CICalendar_HourAsPaddedString
#define ICalendar_get_Minute __x_ABI_CWindows_CGlobalization_CICalendar_get_Minute
#define ICalendar_put_Minute __x_ABI_CWindows_CGlobalization_CICalendar_put_Minute
#define ICalendar_AddMinutes __x_ABI_CWindows_CGlobalization_CICalendar_AddMinutes
#define ICalendar_MinuteAsString __x_ABI_CWindows_CGlobalization_CICalendar_MinuteAsString
#define ICalendar_MinuteAsPaddedString __x_ABI_CWindows_CGlobalization_CICalendar_MinuteAsPaddedString
#define ICalendar_get_Second __x_ABI_CWindows_CGlobalization_CICalendar_get_Second
#define ICalendar_put_Second __x_ABI_CWindows_CGlobalization_CICalendar_put_Second
#define ICalendar_AddSeconds __x_ABI_CWindows_CGlobalization_CICalendar_AddSeconds
#define ICalendar_SecondAsString __x_ABI_CWindows_CGlobalization_CICalendar_SecondAsString
#define ICalendar_SecondAsPaddedString __x_ABI_CWindows_CGlobalization_CICalendar_SecondAsPaddedString
#define ICalendar_get_Nanosecond __x_ABI_CWindows_CGlobalization_CICalendar_get_Nanosecond
#define ICalendar_put_Nanosecond __x_ABI_CWindows_CGlobalization_CICalendar_put_Nanosecond
#define ICalendar_AddNanoseconds __x_ABI_CWindows_CGlobalization_CICalendar_AddNanoseconds
#define ICalendar_NanosecondAsString __x_ABI_CWindows_CGlobalization_CICalendar_NanosecondAsString
#define ICalendar_NanosecondAsPaddedString __x_ABI_CWindows_CGlobalization_CICalendar_NanosecondAsPaddedString
#define ICalendar_Compare __x_ABI_CWindows_CGlobalization_CICalendar_Compare
#define ICalendar_CompareDateTime __x_ABI_CWindows_CGlobalization_CICalendar_CompareDateTime
#define ICalendar_CopyTo __x_ABI_CWindows_CGlobalization_CICalendar_CopyTo
#define ICalendar_get_FirstMinuteInThisHour __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstMinuteInThisHour
#define ICalendar_get_LastMinuteInThisHour __x_ABI_CWindows_CGlobalization_CICalendar_get_LastMinuteInThisHour
#define ICalendar_get_NumberOfMinutesInThisHour __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfMinutesInThisHour
#define ICalendar_get_FirstSecondInThisMinute __x_ABI_CWindows_CGlobalization_CICalendar_get_FirstSecondInThisMinute
#define ICalendar_get_LastSecondInThisMinute __x_ABI_CWindows_CGlobalization_CICalendar_get_LastSecondInThisMinute
#define ICalendar_get_NumberOfSecondsInThisMinute __x_ABI_CWindows_CGlobalization_CICalendar_get_NumberOfSecondsInThisMinute
#define ICalendar_get_ResolvedLanguage __x_ABI_CWindows_CGlobalization_CICalendar_get_ResolvedLanguage
#define ICalendar_get_IsDaylightSavingTime __x_ABI_CWindows_CGlobalization_CICalendar_get_IsDaylightSavingTime
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CICalendar_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICalendarFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CICalendarFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendarFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CICalendarFactory, 0x83f58412, 0xe56b, 0x4c75, 0xa6,0x6e, 0x0f,0x63,0xd5,0x77,0x58,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("83f58412-e56b-4c75-a66e-0f63d57758a6")
            ICalendarFactory : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateCalendarDefaultCalendarAndClock(
                    ABI::Windows::Foundation::Collections::IIterable<HSTRING > *languages,
                    ABI::Windows::Globalization::ICalendar **result) = 0;

                virtual HRESULT STDMETHODCALLTYPE CreateCalendar(
                    ABI::Windows::Foundation::Collections::IIterable<HSTRING > *languages,
                    HSTRING calendar,
                    HSTRING clock,
                    ABI::Windows::Globalization::ICalendar **result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CICalendarFactory, 0x83f58412, 0xe56b, 0x4c75, 0xa6,0x6e, 0x0f,0x63,0xd5,0x77,0x58,0xa6)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CICalendarFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory *This,
        TrustLevel *trustLevel);

    /*** ICalendarFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateCalendarDefaultCalendarAndClock)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory *This,
        __FIIterable_1_HSTRING *languages,
        __x_ABI_CWindows_CGlobalization_CICalendar **result);

    HRESULT (STDMETHODCALLTYPE *CreateCalendar)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory *This,
        __FIIterable_1_HSTRING *languages,
        HSTRING calendar,
        HSTRING clock,
        __x_ABI_CWindows_CGlobalization_CICalendar **result);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CICalendarFactoryVtbl;

interface __x_ABI_CWindows_CGlobalization_CICalendarFactory {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CICalendarFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICalendarFactory methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory_CreateCalendarDefaultCalendarAndClock(This,languages,result) (This)->lpVtbl->CreateCalendarDefaultCalendarAndClock(This,languages,result)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory_CreateCalendar(This,languages,calendar,clock,result) (This)->lpVtbl->CreateCalendar(This,languages,calendar,clock,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory_QueryInterface(__x_ABI_CWindows_CGlobalization_CICalendarFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CICalendarFactory_AddRef(__x_ABI_CWindows_CGlobalization_CICalendarFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CICalendarFactory_Release(__x_ABI_CWindows_CGlobalization_CICalendarFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetIids(__x_ABI_CWindows_CGlobalization_CICalendarFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CICalendarFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CICalendarFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICalendarFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory_CreateCalendarDefaultCalendarAndClock(__x_ABI_CWindows_CGlobalization_CICalendarFactory* This,__FIIterable_1_HSTRING *languages,__x_ABI_CWindows_CGlobalization_CICalendar **result) {
    return This->lpVtbl->CreateCalendarDefaultCalendarAndClock(This,languages,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory_CreateCalendar(__x_ABI_CWindows_CGlobalization_CICalendarFactory* This,__FIIterable_1_HSTRING *languages,HSTRING calendar,HSTRING clock,__x_ABI_CWindows_CGlobalization_CICalendar **result) {
    return This->lpVtbl->CreateCalendar(This,languages,calendar,clock,result);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ICalendarFactory IID___x_ABI_CWindows_CGlobalization_CICalendarFactory
#define ICalendarFactoryVtbl __x_ABI_CWindows_CGlobalization_CICalendarFactoryVtbl
#define ICalendarFactory __x_ABI_CWindows_CGlobalization_CICalendarFactory
#define ICalendarFactory_QueryInterface __x_ABI_CWindows_CGlobalization_CICalendarFactory_QueryInterface
#define ICalendarFactory_AddRef __x_ABI_CWindows_CGlobalization_CICalendarFactory_AddRef
#define ICalendarFactory_Release __x_ABI_CWindows_CGlobalization_CICalendarFactory_Release
#define ICalendarFactory_GetIids __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetIids
#define ICalendarFactory_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetRuntimeClassName
#define ICalendarFactory_GetTrustLevel __x_ABI_CWindows_CGlobalization_CICalendarFactory_GetTrustLevel
#define ICalendarFactory_CreateCalendarDefaultCalendarAndClock __x_ABI_CWindows_CGlobalization_CICalendarFactory_CreateCalendarDefaultCalendarAndClock
#define ICalendarFactory_CreateCalendar __x_ABI_CWindows_CGlobalization_CICalendarFactory_CreateCalendar
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CICalendarFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICalendarFactory2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CICalendarFactory2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CICalendarFactory2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CICalendarFactory2, 0xb44b378c, 0xca7e, 0x4590, 0x9e,0x72, 0xea,0x2b,0xec,0x1a,0x51,0x15);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("b44b378c-ca7e-4590-9e72-ea2bec1a5115")
            ICalendarFactory2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateCalendarWithTimeZone(
                    ABI::Windows::Foundation::Collections::IIterable<HSTRING > *languages,
                    HSTRING calendar,
                    HSTRING clock,
                    HSTRING time_zone_id,
                    ABI::Windows::Globalization::ICalendar **result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CICalendarFactory2, 0xb44b378c, 0xca7e, 0x4590, 0x9e,0x72, 0xea,0x2b,0xec,0x1a,0x51,0x15)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CICalendarFactory2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory2 *This,
        TrustLevel *trustLevel);

    /*** ICalendarFactory2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateCalendarWithTimeZone)(
        __x_ABI_CWindows_CGlobalization_CICalendarFactory2 *This,
        __FIIterable_1_HSTRING *languages,
        HSTRING calendar,
        HSTRING clock,
        HSTRING time_zone_id,
        __x_ABI_CWindows_CGlobalization_CICalendar **result);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CICalendarFactory2Vtbl;

interface __x_ABI_CWindows_CGlobalization_CICalendarFactory2 {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CICalendarFactory2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICalendarFactory2 methods ***/
#define __x_ABI_CWindows_CGlobalization_CICalendarFactory2_CreateCalendarWithTimeZone(This,languages,calendar,clock,time_zone_id,result) (This)->lpVtbl->CreateCalendarWithTimeZone(This,languages,calendar,clock,time_zone_id,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory2_QueryInterface(__x_ABI_CWindows_CGlobalization_CICalendarFactory2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CICalendarFactory2_AddRef(__x_ABI_CWindows_CGlobalization_CICalendarFactory2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CICalendarFactory2_Release(__x_ABI_CWindows_CGlobalization_CICalendarFactory2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetIids(__x_ABI_CWindows_CGlobalization_CICalendarFactory2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CICalendarFactory2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CICalendarFactory2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICalendarFactory2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CICalendarFactory2_CreateCalendarWithTimeZone(__x_ABI_CWindows_CGlobalization_CICalendarFactory2* This,__FIIterable_1_HSTRING *languages,HSTRING calendar,HSTRING clock,HSTRING time_zone_id,__x_ABI_CWindows_CGlobalization_CICalendar **result) {
    return This->lpVtbl->CreateCalendarWithTimeZone(This,languages,calendar,clock,time_zone_id,result);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ICalendarFactory2 IID___x_ABI_CWindows_CGlobalization_CICalendarFactory2
#define ICalendarFactory2Vtbl __x_ABI_CWindows_CGlobalization_CICalendarFactory2Vtbl
#define ICalendarFactory2 __x_ABI_CWindows_CGlobalization_CICalendarFactory2
#define ICalendarFactory2_QueryInterface __x_ABI_CWindows_CGlobalization_CICalendarFactory2_QueryInterface
#define ICalendarFactory2_AddRef __x_ABI_CWindows_CGlobalization_CICalendarFactory2_AddRef
#define ICalendarFactory2_Release __x_ABI_CWindows_CGlobalization_CICalendarFactory2_Release
#define ICalendarFactory2_GetIids __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetIids
#define ICalendarFactory2_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetRuntimeClassName
#define ICalendarFactory2_GetTrustLevel __x_ABI_CWindows_CGlobalization_CICalendarFactory2_GetTrustLevel
#define ICalendarFactory2_CreateCalendarWithTimeZone __x_ABI_CWindows_CGlobalization_CICalendarFactory2_CreateCalendarWithTimeZone
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CICalendarFactory2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IApplicationLanguagesStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics, 0x75b40847, 0x0a4c, 0x4a92, 0x95,0x65, 0xfd,0x63,0xc9,0x5f,0x7a,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("75b40847-0a4c-4a92-9565-fd63c95f7aed")
            IApplicationLanguagesStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_PrimaryLanguageOverride(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_PrimaryLanguageOverride(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Languages(
                    ABI::Windows::Foundation::Collections::IVectorView<HSTRING > **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_ManifestLanguages(
                    ABI::Windows::Foundation::Collections::IVectorView<HSTRING > **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics, 0x75b40847, 0x0a4c, 0x4a92, 0x95,0x65, 0xfd,0x63,0xc9,0x5f,0x7a,0xed)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This,
        TrustLevel *trustLevel);

    /*** IApplicationLanguagesStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PrimaryLanguageOverride)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_PrimaryLanguageOverride)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_Languages)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This,
        __FIVectorView_1_HSTRING **value);

    HRESULT (STDMETHODCALLTYPE *get_ManifestLanguages)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics *This,
        __FIVectorView_1_HSTRING **value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStaticsVtbl;

interface __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IApplicationLanguagesStatics methods ***/
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_PrimaryLanguageOverride(This,value) (This)->lpVtbl->get_PrimaryLanguageOverride(This,value)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_put_PrimaryLanguageOverride(This,value) (This)->lpVtbl->put_PrimaryLanguageOverride(This,value)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_Languages(This,value) (This)->lpVtbl->get_Languages(This,value)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_ManifestLanguages(This,value) (This)->lpVtbl->get_ManifestLanguages(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_QueryInterface(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_AddRef(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_Release(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetIids(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IApplicationLanguagesStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_PrimaryLanguageOverride(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This,HSTRING *value) {
    return This->lpVtbl->get_PrimaryLanguageOverride(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_put_PrimaryLanguageOverride(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This,HSTRING value) {
    return This->lpVtbl->put_PrimaryLanguageOverride(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_Languages(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This,__FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->get_Languages(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_ManifestLanguages(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics* This,__FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->get_ManifestLanguages(This,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_IApplicationLanguagesStatics IID___x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics
#define IApplicationLanguagesStaticsVtbl __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStaticsVtbl
#define IApplicationLanguagesStatics __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics
#define IApplicationLanguagesStatics_QueryInterface __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_QueryInterface
#define IApplicationLanguagesStatics_AddRef __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_AddRef
#define IApplicationLanguagesStatics_Release __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_Release
#define IApplicationLanguagesStatics_GetIids __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetIids
#define IApplicationLanguagesStatics_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetRuntimeClassName
#define IApplicationLanguagesStatics_GetTrustLevel __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_GetTrustLevel
#define IApplicationLanguagesStatics_get_PrimaryLanguageOverride __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_PrimaryLanguageOverride
#define IApplicationLanguagesStatics_put_PrimaryLanguageOverride __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_put_PrimaryLanguageOverride
#define IApplicationLanguagesStatics_get_Languages __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_Languages
#define IApplicationLanguagesStatics_get_ManifestLanguages __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_get_ManifestLanguages
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IApplicationLanguagesStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2, 0x1df0de4f, 0x072b, 0x4d7b, 0x8f,0x06, 0xcb,0x2d,0xb4,0x0f,0x2b,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("1df0de4f-072b-4d7b-8f06-cb2db40f2bb5")
            IApplicationLanguagesStatics2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetLanguagesForUser(
                    ABI::Windows::System::IUser *user,
                    ABI::Windows::Foundation::Collections::IVectorView<HSTRING > **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2, 0x1df0de4f, 0x072b, 0x4d7b, 0x8f,0x06, 0xcb,0x2d,0xb4,0x0f,0x2b,0xb5)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 *This,
        TrustLevel *trustLevel);

    /*** IApplicationLanguagesStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLanguagesForUser)(
        __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        __FIVectorView_1_HSTRING **value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2Vtbl;

interface __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IApplicationLanguagesStatics2 methods ***/
#define __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetLanguagesForUser(This,user,value) (This)->lpVtbl->GetLanguagesForUser(This,user,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_QueryInterface(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_AddRef(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_Release(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetIids(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IApplicationLanguagesStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetLanguagesForUser(__x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2* This,__x_ABI_CWindows_CSystem_CIUser *user,__FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->GetLanguagesForUser(This,user,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_IApplicationLanguagesStatics2 IID___x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2
#define IApplicationLanguagesStatics2Vtbl __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2Vtbl
#define IApplicationLanguagesStatics2 __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2
#define IApplicationLanguagesStatics2_QueryInterface __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_QueryInterface
#define IApplicationLanguagesStatics2_AddRef __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_AddRef
#define IApplicationLanguagesStatics2_Release __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_Release
#define IApplicationLanguagesStatics2_GetIids __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetIids
#define IApplicationLanguagesStatics2_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetRuntimeClassName
#define IApplicationLanguagesStatics2_GetTrustLevel __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetTrustLevel
#define IApplicationLanguagesStatics2_GetLanguagesForUser __x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_GetLanguagesForUser
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CIApplicationLanguagesStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * ILanguage interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CILanguage, 0xea79a752, 0xf7c2, 0x4265, 0xb1,0xbd, 0xc4,0xde,0xc4,0xe4,0xf0,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("ea79a752-f7c2-4265-b1bd-c4dec4e4f080")
            ILanguage : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_LanguageTag(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NativeName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Script(
                    HSTRING *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CILanguage, 0xea79a752, 0xf7c2, 0x4265, 0xb1,0xbd, 0xc4,0xde,0xc4,0xe4,0xf0,0x80)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CILanguageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This,
        TrustLevel *trustLevel);

    /*** ILanguage methods ***/
    HRESULT (STDMETHODCALLTYPE *get_LanguageTag)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_NativeName)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Script)(
        __x_ABI_CWindows_CGlobalization_CILanguage *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CILanguageVtbl;

interface __x_ABI_CWindows_CGlobalization_CILanguage {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CILanguageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CILanguage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CILanguage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CILanguage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CILanguage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanguage methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage_get_LanguageTag(This,value) (This)->lpVtbl->get_LanguageTag(This,value)
#define __x_ABI_CWindows_CGlobalization_CILanguage_get_DisplayName(This,value) (This)->lpVtbl->get_DisplayName(This,value)
#define __x_ABI_CWindows_CGlobalization_CILanguage_get_NativeName(This,value) (This)->lpVtbl->get_NativeName(This,value)
#define __x_ABI_CWindows_CGlobalization_CILanguage_get_Script(This,value) (This)->lpVtbl->get_Script(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage_QueryInterface(__x_ABI_CWindows_CGlobalization_CILanguage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguage_AddRef(__x_ABI_CWindows_CGlobalization_CILanguage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguage_Release(__x_ABI_CWindows_CGlobalization_CILanguage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage_GetIids(__x_ABI_CWindows_CGlobalization_CILanguage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CILanguage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CILanguage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanguage methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage_get_LanguageTag(__x_ABI_CWindows_CGlobalization_CILanguage* This,HSTRING *value) {
    return This->lpVtbl->get_LanguageTag(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage_get_DisplayName(__x_ABI_CWindows_CGlobalization_CILanguage* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage_get_NativeName(__x_ABI_CWindows_CGlobalization_CILanguage* This,HSTRING *value) {
    return This->lpVtbl->get_NativeName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage_get_Script(__x_ABI_CWindows_CGlobalization_CILanguage* This,HSTRING *value) {
    return This->lpVtbl->get_Script(This,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ILanguage IID___x_ABI_CWindows_CGlobalization_CILanguage
#define ILanguageVtbl __x_ABI_CWindows_CGlobalization_CILanguageVtbl
#define ILanguage __x_ABI_CWindows_CGlobalization_CILanguage
#define ILanguage_QueryInterface __x_ABI_CWindows_CGlobalization_CILanguage_QueryInterface
#define ILanguage_AddRef __x_ABI_CWindows_CGlobalization_CILanguage_AddRef
#define ILanguage_Release __x_ABI_CWindows_CGlobalization_CILanguage_Release
#define ILanguage_GetIids __x_ABI_CWindows_CGlobalization_CILanguage_GetIids
#define ILanguage_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CILanguage_GetRuntimeClassName
#define ILanguage_GetTrustLevel __x_ABI_CWindows_CGlobalization_CILanguage_GetTrustLevel
#define ILanguage_get_LanguageTag __x_ABI_CWindows_CGlobalization_CILanguage_get_LanguageTag
#define ILanguage_get_DisplayName __x_ABI_CWindows_CGlobalization_CILanguage_get_DisplayName
#define ILanguage_get_NativeName __x_ABI_CWindows_CGlobalization_CILanguage_get_NativeName
#define ILanguage_get_Script __x_ABI_CWindows_CGlobalization_CILanguage_get_Script
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CILanguage_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ILanguage2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CILanguage2, 0x6a47e5b5, 0xd94d, 0x4886, 0xa4,0x04, 0xa5,0xa5,0xb9,0xd5,0xb4,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("6a47e5b5-d94d-4886-a404-a5a5b9d5b494")
            ILanguage2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_LayoutDirection(
                    ABI::Windows::Globalization::LanguageLayoutDirection *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CILanguage2, 0x6a47e5b5, 0xd94d, 0x4886, 0xa4,0x04, 0xa5,0xa5,0xb9,0xd5,0xb4,0x94)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CILanguage2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CILanguage2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CILanguage2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CILanguage2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CILanguage2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CILanguage2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CILanguage2 *This,
        TrustLevel *trustLevel);

    /*** ILanguage2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_LayoutDirection)(
        __x_ABI_CWindows_CGlobalization_CILanguage2 *This,
        __x_ABI_CWindows_CGlobalization_CLanguageLayoutDirection *value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CILanguage2Vtbl;

interface __x_ABI_CWindows_CGlobalization_CILanguage2 {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CILanguage2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CILanguage2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CILanguage2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CILanguage2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CILanguage2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanguage2 methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage2_get_LayoutDirection(This,value) (This)->lpVtbl->get_LayoutDirection(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage2_QueryInterface(__x_ABI_CWindows_CGlobalization_CILanguage2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguage2_AddRef(__x_ABI_CWindows_CGlobalization_CILanguage2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguage2_Release(__x_ABI_CWindows_CGlobalization_CILanguage2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage2_GetIids(__x_ABI_CWindows_CGlobalization_CILanguage2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage2_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CILanguage2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage2_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CILanguage2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanguage2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage2_get_LayoutDirection(__x_ABI_CWindows_CGlobalization_CILanguage2* This,__x_ABI_CWindows_CGlobalization_CLanguageLayoutDirection *value) {
    return This->lpVtbl->get_LayoutDirection(This,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ILanguage2 IID___x_ABI_CWindows_CGlobalization_CILanguage2
#define ILanguage2Vtbl __x_ABI_CWindows_CGlobalization_CILanguage2Vtbl
#define ILanguage2 __x_ABI_CWindows_CGlobalization_CILanguage2
#define ILanguage2_QueryInterface __x_ABI_CWindows_CGlobalization_CILanguage2_QueryInterface
#define ILanguage2_AddRef __x_ABI_CWindows_CGlobalization_CILanguage2_AddRef
#define ILanguage2_Release __x_ABI_CWindows_CGlobalization_CILanguage2_Release
#define ILanguage2_GetIids __x_ABI_CWindows_CGlobalization_CILanguage2_GetIids
#define ILanguage2_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CILanguage2_GetRuntimeClassName
#define ILanguage2_GetTrustLevel __x_ABI_CWindows_CGlobalization_CILanguage2_GetTrustLevel
#define ILanguage2_get_LayoutDirection __x_ABI_CWindows_CGlobalization_CILanguage2_get_LayoutDirection
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CILanguage2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * ILanguage3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CGlobalization_CILanguage3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguage3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CILanguage3, 0xc6af3d10, 0x641a, 0x5ba4, 0xbb,0x43, 0x5e,0x12,0xae,0xd7,0x59,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("c6af3d10-641a-5ba4-bb43-5e12aed75954")
            ILanguage3 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_AbbreviatedName(
                    HSTRING *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CILanguage3, 0xc6af3d10, 0x641a, 0x5ba4, 0xbb,0x43, 0x5e,0x12,0xae,0xd7,0x59,0x54)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CILanguage3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CILanguage3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CILanguage3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CILanguage3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CILanguage3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CILanguage3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CILanguage3 *This,
        TrustLevel *trustLevel);

    /*** ILanguage3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AbbreviatedName)(
        __x_ABI_CWindows_CGlobalization_CILanguage3 *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CILanguage3Vtbl;

interface __x_ABI_CWindows_CGlobalization_CILanguage3 {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CILanguage3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CILanguage3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CILanguage3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CILanguage3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CILanguage3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanguage3 methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguage3_get_AbbreviatedName(This,value) (This)->lpVtbl->get_AbbreviatedName(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage3_QueryInterface(__x_ABI_CWindows_CGlobalization_CILanguage3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguage3_AddRef(__x_ABI_CWindows_CGlobalization_CILanguage3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguage3_Release(__x_ABI_CWindows_CGlobalization_CILanguage3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage3_GetIids(__x_ABI_CWindows_CGlobalization_CILanguage3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage3_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CILanguage3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage3_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CILanguage3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanguage3 methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguage3_get_AbbreviatedName(__x_ABI_CWindows_CGlobalization_CILanguage3* This,HSTRING *value) {
    return This->lpVtbl->get_AbbreviatedName(This,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ILanguage3 IID___x_ABI_CWindows_CGlobalization_CILanguage3
#define ILanguage3Vtbl __x_ABI_CWindows_CGlobalization_CILanguage3Vtbl
#define ILanguage3 __x_ABI_CWindows_CGlobalization_CILanguage3
#define ILanguage3_QueryInterface __x_ABI_CWindows_CGlobalization_CILanguage3_QueryInterface
#define ILanguage3_AddRef __x_ABI_CWindows_CGlobalization_CILanguage3_AddRef
#define ILanguage3_Release __x_ABI_CWindows_CGlobalization_CILanguage3_Release
#define ILanguage3_GetIids __x_ABI_CWindows_CGlobalization_CILanguage3_GetIids
#define ILanguage3_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CILanguage3_GetRuntimeClassName
#define ILanguage3_GetTrustLevel __x_ABI_CWindows_CGlobalization_CILanguage3_GetTrustLevel
#define ILanguage3_get_AbbreviatedName __x_ABI_CWindows_CGlobalization_CILanguage3_get_AbbreviatedName
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CILanguage3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * ILanguageExtensionSubtags interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags, 0x7d7daf45, 0x368d, 0x4364, 0x85,0x2b, 0xde,0xc9,0x27,0x03,0x7b,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("7d7daf45-368d-4364-852b-dec927037b85")
            ILanguageExtensionSubtags : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetExtensionSubtags(
                    HSTRING tag,
                    ABI::Windows::Foundation::Collections::IVectorView<HSTRING > **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags, 0x7d7daf45, 0x368d, 0x4364, 0x85,0x2b, 0xde,0xc9,0x27,0x03,0x7b,0x85)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtagsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags *This,
        TrustLevel *trustLevel);

    /*** ILanguageExtensionSubtags methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExtensionSubtags)(
        __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags *This,
        HSTRING tag,
        __FIVectorView_1_HSTRING **value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtagsVtbl;

interface __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtagsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanguageExtensionSubtags methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetExtensionSubtags(This,tag,value) (This)->lpVtbl->GetExtensionSubtags(This,tag,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_QueryInterface(__x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_AddRef(__x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_Release(__x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetIids(__x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanguageExtensionSubtags methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetExtensionSubtags(__x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags* This,HSTRING tag,__FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->GetExtensionSubtags(This,tag,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ILanguageExtensionSubtags IID___x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags
#define ILanguageExtensionSubtagsVtbl __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtagsVtbl
#define ILanguageExtensionSubtags __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags
#define ILanguageExtensionSubtags_QueryInterface __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_QueryInterface
#define ILanguageExtensionSubtags_AddRef __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_AddRef
#define ILanguageExtensionSubtags_Release __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_Release
#define ILanguageExtensionSubtags_GetIids __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetIids
#define ILanguageExtensionSubtags_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetRuntimeClassName
#define ILanguageExtensionSubtags_GetTrustLevel __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetTrustLevel
#define ILanguageExtensionSubtags_GetExtensionSubtags __x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_GetExtensionSubtags
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CILanguageExtensionSubtags_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ILanguageFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CILanguageFactory, 0x9b0252ac, 0x0c27, 0x44f8, 0xb7,0x92, 0x97,0x93,0xfb,0x66,0xc6,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("9b0252ac-0c27-44f8-b792-9793fb66c63e")
            ILanguageFactory : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateLanguage(
                    HSTRING tag,
                    ABI::Windows::Globalization::ILanguage **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CILanguageFactory, 0x9b0252ac, 0x0c27, 0x44f8, 0xb7,0x92, 0x97,0x93,0xfb,0x66,0xc6,0x3e)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CILanguageFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CILanguageFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CILanguageFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CILanguageFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CILanguageFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CILanguageFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CILanguageFactory *This,
        TrustLevel *trustLevel);

    /*** ILanguageFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateLanguage)(
        __x_ABI_CWindows_CGlobalization_CILanguageFactory *This,
        HSTRING tag,
        __x_ABI_CWindows_CGlobalization_CILanguage **value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CILanguageFactoryVtbl;

interface __x_ABI_CWindows_CGlobalization_CILanguageFactory {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CILanguageFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanguageFactory methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageFactory_CreateLanguage(This,tag,value) (This)->lpVtbl->CreateLanguage(This,tag,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageFactory_QueryInterface(__x_ABI_CWindows_CGlobalization_CILanguageFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageFactory_AddRef(__x_ABI_CWindows_CGlobalization_CILanguageFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageFactory_Release(__x_ABI_CWindows_CGlobalization_CILanguageFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetIids(__x_ABI_CWindows_CGlobalization_CILanguageFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CILanguageFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CILanguageFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanguageFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageFactory_CreateLanguage(__x_ABI_CWindows_CGlobalization_CILanguageFactory* This,HSTRING tag,__x_ABI_CWindows_CGlobalization_CILanguage **value) {
    return This->lpVtbl->CreateLanguage(This,tag,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ILanguageFactory IID___x_ABI_CWindows_CGlobalization_CILanguageFactory
#define ILanguageFactoryVtbl __x_ABI_CWindows_CGlobalization_CILanguageFactoryVtbl
#define ILanguageFactory __x_ABI_CWindows_CGlobalization_CILanguageFactory
#define ILanguageFactory_QueryInterface __x_ABI_CWindows_CGlobalization_CILanguageFactory_QueryInterface
#define ILanguageFactory_AddRef __x_ABI_CWindows_CGlobalization_CILanguageFactory_AddRef
#define ILanguageFactory_Release __x_ABI_CWindows_CGlobalization_CILanguageFactory_Release
#define ILanguageFactory_GetIids __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetIids
#define ILanguageFactory_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetRuntimeClassName
#define ILanguageFactory_GetTrustLevel __x_ABI_CWindows_CGlobalization_CILanguageFactory_GetTrustLevel
#define ILanguageFactory_CreateLanguage __x_ABI_CWindows_CGlobalization_CILanguageFactory_CreateLanguage
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CILanguageFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ILanguageStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CILanguageStatics, 0xb23cd557, 0x0865, 0x46d4, 0x89,0xb8, 0xd5,0x9b,0xe8,0x99,0x0f,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("b23cd557-0865-46d4-89b8-d59be8990f0d")
            ILanguageStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE IsWellFormed(
                    HSTRING tag,
                    BOOLEAN *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_CurrentInputMethodLanguageTag(
                    HSTRING *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CILanguageStatics, 0xb23cd557, 0x0865, 0x46d4, 0x89,0xb8, 0xd5,0x9b,0xe8,0x99,0x0f,0x0d)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CILanguageStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics *This,
        TrustLevel *trustLevel);

    /*** ILanguageStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *IsWellFormed)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics *This,
        HSTRING tag,
        BOOLEAN *result);

    HRESULT (STDMETHODCALLTYPE *get_CurrentInputMethodLanguageTag)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CILanguageStaticsVtbl;

interface __x_ABI_CWindows_CGlobalization_CILanguageStatics {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CILanguageStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanguageStatics methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics_IsWellFormed(This,tag,result) (This)->lpVtbl->IsWellFormed(This,tag,result)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics_get_CurrentInputMethodLanguageTag(This,value) (This)->lpVtbl->get_CurrentInputMethodLanguageTag(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics_QueryInterface(__x_ABI_CWindows_CGlobalization_CILanguageStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageStatics_AddRef(__x_ABI_CWindows_CGlobalization_CILanguageStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageStatics_Release(__x_ABI_CWindows_CGlobalization_CILanguageStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetIids(__x_ABI_CWindows_CGlobalization_CILanguageStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CILanguageStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CILanguageStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanguageStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics_IsWellFormed(__x_ABI_CWindows_CGlobalization_CILanguageStatics* This,HSTRING tag,BOOLEAN *result) {
    return This->lpVtbl->IsWellFormed(This,tag,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics_get_CurrentInputMethodLanguageTag(__x_ABI_CWindows_CGlobalization_CILanguageStatics* This,HSTRING *value) {
    return This->lpVtbl->get_CurrentInputMethodLanguageTag(This,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ILanguageStatics IID___x_ABI_CWindows_CGlobalization_CILanguageStatics
#define ILanguageStaticsVtbl __x_ABI_CWindows_CGlobalization_CILanguageStaticsVtbl
#define ILanguageStatics __x_ABI_CWindows_CGlobalization_CILanguageStatics
#define ILanguageStatics_QueryInterface __x_ABI_CWindows_CGlobalization_CILanguageStatics_QueryInterface
#define ILanguageStatics_AddRef __x_ABI_CWindows_CGlobalization_CILanguageStatics_AddRef
#define ILanguageStatics_Release __x_ABI_CWindows_CGlobalization_CILanguageStatics_Release
#define ILanguageStatics_GetIids __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetIids
#define ILanguageStatics_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetRuntimeClassName
#define ILanguageStatics_GetTrustLevel __x_ABI_CWindows_CGlobalization_CILanguageStatics_GetTrustLevel
#define ILanguageStatics_IsWellFormed __x_ABI_CWindows_CGlobalization_CILanguageStatics_IsWellFormed
#define ILanguageStatics_get_CurrentInputMethodLanguageTag __x_ABI_CWindows_CGlobalization_CILanguageStatics_get_CurrentInputMethodLanguageTag
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CILanguageStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ILanguageStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CILanguageStatics2, 0x30199f6e, 0x914b, 0x4b2a, 0x9d,0x6e, 0xe3,0xb0,0xe2,0x7d,0xbe,0x4f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("30199f6e-914b-4b2a-9d6e-e3b0e27dbe4f")
            ILanguageStatics2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE TrySetInputMethodLanguageTag(
                    HSTRING tag,
                    BOOLEAN *result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CILanguageStatics2, 0x30199f6e, 0x914b, 0x4b2a, 0x9d,0x6e, 0xe3,0xb0,0xe2,0x7d,0xbe,0x4f)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CILanguageStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics2 *This,
        TrustLevel *trustLevel);

    /*** ILanguageStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *TrySetInputMethodLanguageTag)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics2 *This,
        HSTRING tag,
        BOOLEAN *result);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CILanguageStatics2Vtbl;

interface __x_ABI_CWindows_CGlobalization_CILanguageStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CILanguageStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanguageStatics2 methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics2_TrySetInputMethodLanguageTag(This,tag,result) (This)->lpVtbl->TrySetInputMethodLanguageTag(This,tag,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics2_QueryInterface(__x_ABI_CWindows_CGlobalization_CILanguageStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageStatics2_AddRef(__x_ABI_CWindows_CGlobalization_CILanguageStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageStatics2_Release(__x_ABI_CWindows_CGlobalization_CILanguageStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetIids(__x_ABI_CWindows_CGlobalization_CILanguageStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CILanguageStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CILanguageStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanguageStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics2_TrySetInputMethodLanguageTag(__x_ABI_CWindows_CGlobalization_CILanguageStatics2* This,HSTRING tag,BOOLEAN *result) {
    return This->lpVtbl->TrySetInputMethodLanguageTag(This,tag,result);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ILanguageStatics2 IID___x_ABI_CWindows_CGlobalization_CILanguageStatics2
#define ILanguageStatics2Vtbl __x_ABI_CWindows_CGlobalization_CILanguageStatics2Vtbl
#define ILanguageStatics2 __x_ABI_CWindows_CGlobalization_CILanguageStatics2
#define ILanguageStatics2_QueryInterface __x_ABI_CWindows_CGlobalization_CILanguageStatics2_QueryInterface
#define ILanguageStatics2_AddRef __x_ABI_CWindows_CGlobalization_CILanguageStatics2_AddRef
#define ILanguageStatics2_Release __x_ABI_CWindows_CGlobalization_CILanguageStatics2_Release
#define ILanguageStatics2_GetIids __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetIids
#define ILanguageStatics2_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetRuntimeClassName
#define ILanguageStatics2_GetTrustLevel __x_ABI_CWindows_CGlobalization_CILanguageStatics2_GetTrustLevel
#define ILanguageStatics2_TrySetInputMethodLanguageTag __x_ABI_CWindows_CGlobalization_CILanguageStatics2_TrySetInputMethodLanguageTag
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CILanguageStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ILanguageStatics3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CGlobalization_CILanguageStatics3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CILanguageStatics3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CILanguageStatics3, 0xd15ecb5a, 0x71de, 0x5752, 0x95,0x42, 0xfa,0xc5,0xb4,0xf2,0x72,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("d15ecb5a-71de-5752-9542-fac5b4f27261")
            ILanguageStatics3 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetMuiCompatibleLanguageListFromLanguageTags(
                    ABI::Windows::Foundation::Collections::IIterable<HSTRING > *tags,
                    ABI::Windows::Foundation::Collections::IVector<HSTRING > **result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CILanguageStatics3, 0xd15ecb5a, 0x71de, 0x5752, 0x95,0x42, 0xfa,0xc5,0xb4,0xf2,0x72,0x61)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CILanguageStatics3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics3 *This,
        TrustLevel *trustLevel);

    /*** ILanguageStatics3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMuiCompatibleLanguageListFromLanguageTags)(
        __x_ABI_CWindows_CGlobalization_CILanguageStatics3 *This,
        __FIIterable_1_HSTRING *tags,
        __FIVector_1_HSTRING **result);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CILanguageStatics3Vtbl;

interface __x_ABI_CWindows_CGlobalization_CILanguageStatics3 {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CILanguageStatics3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanguageStatics3 methods ***/
#define __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetMuiCompatibleLanguageListFromLanguageTags(This,tags,result) (This)->lpVtbl->GetMuiCompatibleLanguageListFromLanguageTags(This,tags,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics3_QueryInterface(__x_ABI_CWindows_CGlobalization_CILanguageStatics3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageStatics3_AddRef(__x_ABI_CWindows_CGlobalization_CILanguageStatics3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CILanguageStatics3_Release(__x_ABI_CWindows_CGlobalization_CILanguageStatics3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetIids(__x_ABI_CWindows_CGlobalization_CILanguageStatics3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CILanguageStatics3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CILanguageStatics3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanguageStatics3 methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetMuiCompatibleLanguageListFromLanguageTags(__x_ABI_CWindows_CGlobalization_CILanguageStatics3* This,__FIIterable_1_HSTRING *tags,__FIVector_1_HSTRING **result) {
    return This->lpVtbl->GetMuiCompatibleLanguageListFromLanguageTags(This,tags,result);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ILanguageStatics3 IID___x_ABI_CWindows_CGlobalization_CILanguageStatics3
#define ILanguageStatics3Vtbl __x_ABI_CWindows_CGlobalization_CILanguageStatics3Vtbl
#define ILanguageStatics3 __x_ABI_CWindows_CGlobalization_CILanguageStatics3
#define ILanguageStatics3_QueryInterface __x_ABI_CWindows_CGlobalization_CILanguageStatics3_QueryInterface
#define ILanguageStatics3_AddRef __x_ABI_CWindows_CGlobalization_CILanguageStatics3_AddRef
#define ILanguageStatics3_Release __x_ABI_CWindows_CGlobalization_CILanguageStatics3_Release
#define ILanguageStatics3_GetIids __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetIids
#define ILanguageStatics3_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetRuntimeClassName
#define ILanguageStatics3_GetTrustLevel __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetTrustLevel
#define ILanguageStatics3_GetMuiCompatibleLanguageListFromLanguageTags __x_ABI_CWindows_CGlobalization_CILanguageStatics3_GetMuiCompatibleLanguageListFromLanguageTags
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CILanguageStatics3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * IGeographicRegion interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegion_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegion_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CIGeographicRegion, 0x01e9a621, 0x4a64, 0x4ed9, 0x95,0x4f, 0x9e,0xde,0xb0,0x7b,0xd9,0x03);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("01e9a621-4a64-4ed9-954f-9edeb07bd903")
            IGeographicRegion : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Code(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_CodeTwoLetter(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_CodeThreeLetter(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_CodeThreeDigit(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NativeName(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_CurrenciesInUse(
                    ABI::Windows::Foundation::Collections::IVectorView<HSTRING > **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CIGeographicRegion, 0x01e9a621, 0x4a64, 0x4ed9, 0x95,0x4f, 0x9e,0xde,0xb0,0x7b,0xd9,0x03)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CIGeographicRegionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        TrustLevel *trustLevel);

    /*** IGeographicRegion methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Code)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_CodeTwoLetter)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_CodeThreeLetter)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_CodeThreeDigit)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_NativeName)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_CurrenciesInUse)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion *This,
        __FIVectorView_1_HSTRING **value);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CIGeographicRegionVtbl;

interface __x_ABI_CWindows_CGlobalization_CIGeographicRegion {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CIGeographicRegionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGeographicRegion methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_Code(This,value) (This)->lpVtbl->get_Code(This,value)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeTwoLetter(This,value) (This)->lpVtbl->get_CodeTwoLetter(This,value)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeThreeLetter(This,value) (This)->lpVtbl->get_CodeThreeLetter(This,value)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeThreeDigit(This,value) (This)->lpVtbl->get_CodeThreeDigit(This,value)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_DisplayName(This,value) (This)->lpVtbl->get_DisplayName(This,value)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_NativeName(This,value) (This)->lpVtbl->get_NativeName(This,value)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CurrenciesInUse(This,value) (This)->lpVtbl->get_CurrenciesInUse(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_QueryInterface(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIGeographicRegion_AddRef(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIGeographicRegion_Release(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetIids(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGeographicRegion methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_Code(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,HSTRING *value) {
    return This->lpVtbl->get_Code(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeTwoLetter(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,HSTRING *value) {
    return This->lpVtbl->get_CodeTwoLetter(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeThreeLetter(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,HSTRING *value) {
    return This->lpVtbl->get_CodeThreeLetter(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeThreeDigit(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,HSTRING *value) {
    return This->lpVtbl->get_CodeThreeDigit(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_DisplayName(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_NativeName(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,HSTRING *value) {
    return This->lpVtbl->get_NativeName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CurrenciesInUse(__x_ABI_CWindows_CGlobalization_CIGeographicRegion* This,__FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->get_CurrenciesInUse(This,value);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_IGeographicRegion IID___x_ABI_CWindows_CGlobalization_CIGeographicRegion
#define IGeographicRegionVtbl __x_ABI_CWindows_CGlobalization_CIGeographicRegionVtbl
#define IGeographicRegion __x_ABI_CWindows_CGlobalization_CIGeographicRegion
#define IGeographicRegion_QueryInterface __x_ABI_CWindows_CGlobalization_CIGeographicRegion_QueryInterface
#define IGeographicRegion_AddRef __x_ABI_CWindows_CGlobalization_CIGeographicRegion_AddRef
#define IGeographicRegion_Release __x_ABI_CWindows_CGlobalization_CIGeographicRegion_Release
#define IGeographicRegion_GetIids __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetIids
#define IGeographicRegion_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetRuntimeClassName
#define IGeographicRegion_GetTrustLevel __x_ABI_CWindows_CGlobalization_CIGeographicRegion_GetTrustLevel
#define IGeographicRegion_get_Code __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_Code
#define IGeographicRegion_get_CodeTwoLetter __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeTwoLetter
#define IGeographicRegion_get_CodeThreeLetter __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeThreeLetter
#define IGeographicRegion_get_CodeThreeDigit __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CodeThreeDigit
#define IGeographicRegion_get_DisplayName __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_DisplayName
#define IGeographicRegion_get_NativeName __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_NativeName
#define IGeographicRegion_get_CurrenciesInUse __x_ABI_CWindows_CGlobalization_CIGeographicRegion_get_CurrenciesInUse
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CIGeographicRegion_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IGeographicRegionFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory, 0x53425270, 0x77b4, 0x426b, 0x85,0x9f, 0x81,0xe1,0x9d,0x51,0x25,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("53425270-77b4-426b-859f-81e19d512546")
            IGeographicRegionFactory : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateGeographicRegion(
                    HSTRING region_code,
                    ABI::Windows::Globalization::IGeographicRegion **result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory, 0x53425270, 0x77b4, 0x426b, 0x85,0x9f, 0x81,0xe1,0x9d,0x51,0x25,0x46)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory *This,
        TrustLevel *trustLevel);

    /*** IGeographicRegionFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateGeographicRegion)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory *This,
        HSTRING region_code,
        __x_ABI_CWindows_CGlobalization_CIGeographicRegion **result);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactoryVtbl;

interface __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGeographicRegionFactory methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_CreateGeographicRegion(This,region_code,result) (This)->lpVtbl->CreateGeographicRegion(This,region_code,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_QueryInterface(__x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_AddRef(__x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_Release(__x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetIids(__x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGeographicRegionFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_CreateGeographicRegion(__x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory* This,HSTRING region_code,__x_ABI_CWindows_CGlobalization_CIGeographicRegion **result) {
    return This->lpVtbl->CreateGeographicRegion(This,region_code,result);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_IGeographicRegionFactory IID___x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory
#define IGeographicRegionFactoryVtbl __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactoryVtbl
#define IGeographicRegionFactory __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory
#define IGeographicRegionFactory_QueryInterface __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_QueryInterface
#define IGeographicRegionFactory_AddRef __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_AddRef
#define IGeographicRegionFactory_Release __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_Release
#define IGeographicRegionFactory_GetIids __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetIids
#define IGeographicRegionFactory_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetRuntimeClassName
#define IGeographicRegionFactory_GetTrustLevel __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_GetTrustLevel
#define IGeographicRegionFactory_CreateGeographicRegion __x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_CreateGeographicRegion
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CIGeographicRegionFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IGeographicRegionStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics, 0x29e28974, 0x7ad9, 0x4ef4, 0x87,0x99, 0xb3,0xb4,0x4f,0xad,0xec,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("29e28974-7ad9-4ef4-8799-b3b44fadec08")
            IGeographicRegionStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE IsSupported(
                    HSTRING region_code,
                    boolean *result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics, 0x29e28974, 0x7ad9, 0x4ef4, 0x87,0x99, 0xb3,0xb4,0x4f,0xad,0xec,0x08)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CIGeographicRegionStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics *This,
        TrustLevel *trustLevel);

    /*** IGeographicRegionStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *IsSupported)(
        __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics *This,
        HSTRING region_code,
        boolean *result);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CIGeographicRegionStaticsVtbl;

interface __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CIGeographicRegionStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGeographicRegionStatics methods ***/
#define __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_IsSupported(This,region_code,result) (This)->lpVtbl->IsSupported(This,region_code,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_QueryInterface(__x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_AddRef(__x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_Release(__x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetIids(__x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGeographicRegionStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_IsSupported(__x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics* This,HSTRING region_code,boolean *result) {
    return This->lpVtbl->IsSupported(This,region_code,result);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_IGeographicRegionStatics IID___x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics
#define IGeographicRegionStaticsVtbl __x_ABI_CWindows_CGlobalization_CIGeographicRegionStaticsVtbl
#define IGeographicRegionStatics __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics
#define IGeographicRegionStatics_QueryInterface __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_QueryInterface
#define IGeographicRegionStatics_AddRef __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_AddRef
#define IGeographicRegionStatics_Release __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_Release
#define IGeographicRegionStatics_GetIids __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetIids
#define IGeographicRegionStatics_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetRuntimeClassName
#define IGeographicRegionStatics_GetTrustLevel __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_GetTrustLevel
#define IGeographicRegionStatics_IsSupported __x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_IsSupported
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CIGeographicRegionStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ITimeZoneOnCalendar interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar, 0xbb3c25e5, 0x46cf, 0x4317, 0xa3,0xf5, 0x02,0x62,0x1a,0xd5,0x44,0x78);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Globalization {
            MIDL_INTERFACE("bb3c25e5-46cf-4317-a3f5-02621ad54478")
            ITimeZoneOnCalendar : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetTimeZone(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE ChangeTimeZone(
                    HSTRING time_zone_id) = 0;

                virtual HRESULT STDMETHODCALLTYPE TimeZoneAsFullString(
                    HSTRING *result) = 0;

                virtual HRESULT STDMETHODCALLTYPE TimeZoneAsString(
                    INT32 ideal_length,
                    HSTRING *result) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar, 0xbb3c25e5, 0x46cf, 0x4317, 0xa3,0xf5, 0x02,0x62,0x1a,0xd5,0x44,0x78)
#endif
#else
typedef struct __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendarVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This,
        TrustLevel *trustLevel);

    /*** ITimeZoneOnCalendar methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTimeZone)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *ChangeTimeZone)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This,
        HSTRING time_zone_id);

    HRESULT (STDMETHODCALLTYPE *TimeZoneAsFullString)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *TimeZoneAsString)(
        __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar *This,
        INT32 ideal_length,
        HSTRING *result);

    END_INTERFACE
} __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendarVtbl;

interface __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar {
    CONST_VTBL __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendarVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ITimeZoneOnCalendar methods ***/
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetTimeZone(This,value) (This)->lpVtbl->GetTimeZone(This,value)
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_ChangeTimeZone(This,time_zone_id) (This)->lpVtbl->ChangeTimeZone(This,time_zone_id)
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_TimeZoneAsFullString(This,result) (This)->lpVtbl->TimeZoneAsFullString(This,result)
#define __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_TimeZoneAsString(This,ideal_length,result) (This)->lpVtbl->TimeZoneAsString(This,ideal_length,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_QueryInterface(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_AddRef(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_Release(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetIids(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetRuntimeClassName(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetTrustLevel(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ITimeZoneOnCalendar methods ***/
static inline HRESULT __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetTimeZone(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This,HSTRING *value) {
    return This->lpVtbl->GetTimeZone(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_ChangeTimeZone(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This,HSTRING time_zone_id) {
    return This->lpVtbl->ChangeTimeZone(This,time_zone_id);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_TimeZoneAsFullString(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This,HSTRING *result) {
    return This->lpVtbl->TimeZoneAsFullString(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_TimeZoneAsString(__x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar* This,INT32 ideal_length,HSTRING *result) {
    return This->lpVtbl->TimeZoneAsString(This,ideal_length,result);
}
#endif
#ifdef WIDL_using_Windows_Globalization
#define IID_ITimeZoneOnCalendar IID___x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar
#define ITimeZoneOnCalendarVtbl __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendarVtbl
#define ITimeZoneOnCalendar __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar
#define ITimeZoneOnCalendar_QueryInterface __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_QueryInterface
#define ITimeZoneOnCalendar_AddRef __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_AddRef
#define ITimeZoneOnCalendar_Release __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_Release
#define ITimeZoneOnCalendar_GetIids __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetIids
#define ITimeZoneOnCalendar_GetRuntimeClassName __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetRuntimeClassName
#define ITimeZoneOnCalendar_GetTrustLevel __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetTrustLevel
#define ITimeZoneOnCalendar_GetTimeZone __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_GetTimeZone
#define ITimeZoneOnCalendar_ChangeTimeZone __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_ChangeTimeZone
#define ITimeZoneOnCalendar_TimeZoneAsFullString __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_TimeZoneAsFullString
#define ITimeZoneOnCalendar_TimeZoneAsString __x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_TimeZoneAsString
#endif /* WIDL_using_Windows_Globalization */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGlobalization_CITimeZoneOnCalendar_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Globalization.Calendar
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Globalization_Calendar_DEFINED
#define RUNTIMECLASS_Windows_Globalization_Calendar_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Globalization_Calendar[] = {'W','i','n','d','o','w','s','.','G','l','o','b','a','l','i','z','a','t','i','o','n','.','C','a','l','e','n','d','a','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Globalization_Calendar[] = L"Windows.Globalization.Calendar";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Globalization_Calendar[] = {'W','i','n','d','o','w','s','.','G','l','o','b','a','l','i','z','a','t','i','o','n','.','C','a','l','e','n','d','a','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Globalization_Calendar_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Globalization.ApplicationLanguages
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Globalization_ApplicationLanguages_DEFINED
#define RUNTIMECLASS_Windows_Globalization_ApplicationLanguages_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Globalization_ApplicationLanguages[] = {'W','i','n','d','o','w','s','.','G','l','o','b','a','l','i','z','a','t','i','o','n','.','A','p','p','l','i','c','a','t','i','o','n','L','a','n','g','u','a','g','e','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Globalization_ApplicationLanguages[] = L"Windows.Globalization.ApplicationLanguages";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Globalization_ApplicationLanguages[] = {'W','i','n','d','o','w','s','.','G','l','o','b','a','l','i','z','a','t','i','o','n','.','A','p','p','l','i','c','a','t','i','o','n','L','a','n','g','u','a','g','e','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Globalization_ApplicationLanguages_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Globalization.Language
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Globalization_Language_DEFINED
#define RUNTIMECLASS_Windows_Globalization_Language_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Globalization_Language[] = {'W','i','n','d','o','w','s','.','G','l','o','b','a','l','i','z','a','t','i','o','n','.','L','a','n','g','u','a','g','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Globalization_Language[] = L"Windows.Globalization.Language";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Globalization_Language[] = {'W','i','n','d','o','w','s','.','G','l','o','b','a','l','i','z','a','t','i','o','n','.','L','a','n','g','u','a','g','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Globalization_Language_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Globalization.GeographicRegion
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Globalization_GeographicRegion_DEFINED
#define RUNTIMECLASS_Windows_Globalization_GeographicRegion_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Globalization_GeographicRegion[] = {'W','i','n','d','o','w','s','.','G','l','o','b','a','l','i','z','a','t','i','o','n','.','G','e','o','g','r','a','p','h','i','c','R','e','g','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Globalization_GeographicRegion[] = L"Windows.Globalization.GeographicRegion";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Globalization_GeographicRegion[] = {'W','i','n','d','o','w','s','.','G','l','o','b','a','l','i','z','a','t','i','o','n','.','G','e','o','g','r','a','p','h','i','c','R','e','g','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Globalization_GeographicRegion_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Globalization::Language* > interface
 */
#ifndef ____FIIterable_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CGlobalization__CLanguage, 0x48409a10, 0x61b6, 0x5db1, 0xa6,0x9d, 0x8a,0xbc,0x46,0xac,0x60,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("48409a10-61b6-5db1-a69d-8abc46ac608a")
                IIterable<ABI::Windows::Globalization::Language* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Globalization::Language*, ABI::Windows::Globalization::ILanguage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CGlobalization__CLanguage, 0x48409a10, 0x61b6, 0x5db1, 0xa6,0x9d, 0x8a,0xbc,0x46,0xac,0x60,0x8a)
#endif
#else
typedef struct __FIIterable_1_Windows__CGlobalization__CLanguageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CGlobalization__CLanguage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CGlobalization__CLanguage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CGlobalization__CLanguage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CGlobalization__CLanguage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CGlobalization__CLanguage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CGlobalization__CLanguage *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Globalization::Language* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CGlobalization__CLanguage *This,
        __FIIterator_1_Windows__CGlobalization__CLanguage **value);

    END_INTERFACE
} __FIIterable_1_Windows__CGlobalization__CLanguageVtbl;

interface __FIIterable_1_Windows__CGlobalization__CLanguage {
    CONST_VTBL __FIIterable_1_Windows__CGlobalization__CLanguageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CGlobalization__CLanguage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CGlobalization__CLanguage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CGlobalization__CLanguage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CGlobalization__CLanguage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CGlobalization__CLanguage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Globalization::Language* > methods ***/
#define __FIIterable_1_Windows__CGlobalization__CLanguage_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CGlobalization__CLanguage_QueryInterface(__FIIterable_1_Windows__CGlobalization__CLanguage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CGlobalization__CLanguage_AddRef(__FIIterable_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CGlobalization__CLanguage_Release(__FIIterable_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CGlobalization__CLanguage_GetIids(__FIIterable_1_Windows__CGlobalization__CLanguage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName(__FIIterable_1_Windows__CGlobalization__CLanguage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CGlobalization__CLanguage_GetTrustLevel(__FIIterable_1_Windows__CGlobalization__CLanguage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Globalization::Language* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CGlobalization__CLanguage_First(__FIIterable_1_Windows__CGlobalization__CLanguage* This,__FIIterator_1_Windows__CGlobalization__CLanguage **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_Language IID___FIIterable_1_Windows__CGlobalization__CLanguage
#define IIterable_LanguageVtbl __FIIterable_1_Windows__CGlobalization__CLanguageVtbl
#define IIterable_Language __FIIterable_1_Windows__CGlobalization__CLanguage
#define IIterable_Language_QueryInterface __FIIterable_1_Windows__CGlobalization__CLanguage_QueryInterface
#define IIterable_Language_AddRef __FIIterable_1_Windows__CGlobalization__CLanguage_AddRef
#define IIterable_Language_Release __FIIterable_1_Windows__CGlobalization__CLanguage_Release
#define IIterable_Language_GetIids __FIIterable_1_Windows__CGlobalization__CLanguage_GetIids
#define IIterable_Language_GetRuntimeClassName __FIIterable_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName
#define IIterable_Language_GetTrustLevel __FIIterable_1_Windows__CGlobalization__CLanguage_GetTrustLevel
#define IIterable_Language_First __FIIterable_1_Windows__CGlobalization__CLanguage_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Globalization::Language* > interface
 */
#ifndef ____FIIterator_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CGlobalization__CLanguage, 0x30e99ae6, 0xf414, 0x5243, 0x8d,0xb2, 0xaa,0xb3,0x8e,0xa3,0xf1,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("30e99ae6-f414-5243-8db2-aab38ea3f1f1")
                IIterator<ABI::Windows::Globalization::Language* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Globalization::Language*, ABI::Windows::Globalization::ILanguage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CGlobalization__CLanguage, 0x30e99ae6, 0xf414, 0x5243, 0x8d,0xb2, 0xaa,0xb3,0x8e,0xa3,0xf1,0xf1)
#endif
#else
typedef struct __FIIterator_1_Windows__CGlobalization__CLanguageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Globalization::Language* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This,
        __x_ABI_CWindows_CGlobalization_CILanguage **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CGlobalization__CLanguage *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGlobalization_CILanguage **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CGlobalization__CLanguageVtbl;

interface __FIIterator_1_Windows__CGlobalization__CLanguage {
    CONST_VTBL __FIIterator_1_Windows__CGlobalization__CLanguageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CGlobalization__CLanguage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CGlobalization__CLanguage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CGlobalization__CLanguage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CGlobalization__CLanguage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CGlobalization__CLanguage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Globalization::Language* > methods ***/
#define __FIIterator_1_Windows__CGlobalization__CLanguage_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CGlobalization__CLanguage_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CGlobalization__CLanguage_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CGlobalization__CLanguage_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CGlobalization__CLanguage_QueryInterface(__FIIterator_1_Windows__CGlobalization__CLanguage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CGlobalization__CLanguage_AddRef(__FIIterator_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CGlobalization__CLanguage_Release(__FIIterator_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CGlobalization__CLanguage_GetIids(__FIIterator_1_Windows__CGlobalization__CLanguage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName(__FIIterator_1_Windows__CGlobalization__CLanguage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CGlobalization__CLanguage_GetTrustLevel(__FIIterator_1_Windows__CGlobalization__CLanguage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Globalization::Language* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CGlobalization__CLanguage_get_Current(__FIIterator_1_Windows__CGlobalization__CLanguage* This,__x_ABI_CWindows_CGlobalization_CILanguage **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGlobalization__CLanguage_get_HasCurrent(__FIIterator_1_Windows__CGlobalization__CLanguage* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGlobalization__CLanguage_MoveNext(__FIIterator_1_Windows__CGlobalization__CLanguage* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGlobalization__CLanguage_GetMany(__FIIterator_1_Windows__CGlobalization__CLanguage* This,UINT32 items_size,__x_ABI_CWindows_CGlobalization_CILanguage **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_Language IID___FIIterator_1_Windows__CGlobalization__CLanguage
#define IIterator_LanguageVtbl __FIIterator_1_Windows__CGlobalization__CLanguageVtbl
#define IIterator_Language __FIIterator_1_Windows__CGlobalization__CLanguage
#define IIterator_Language_QueryInterface __FIIterator_1_Windows__CGlobalization__CLanguage_QueryInterface
#define IIterator_Language_AddRef __FIIterator_1_Windows__CGlobalization__CLanguage_AddRef
#define IIterator_Language_Release __FIIterator_1_Windows__CGlobalization__CLanguage_Release
#define IIterator_Language_GetIids __FIIterator_1_Windows__CGlobalization__CLanguage_GetIids
#define IIterator_Language_GetRuntimeClassName __FIIterator_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName
#define IIterator_Language_GetTrustLevel __FIIterator_1_Windows__CGlobalization__CLanguage_GetTrustLevel
#define IIterator_Language_get_Current __FIIterator_1_Windows__CGlobalization__CLanguage_get_Current
#define IIterator_Language_get_HasCurrent __FIIterator_1_Windows__CGlobalization__CLanguage_get_HasCurrent
#define IIterator_Language_MoveNext __FIIterator_1_Windows__CGlobalization__CLanguage_MoveNext
#define IIterator_Language_GetMany __FIIterator_1_Windows__CGlobalization__CLanguage_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Globalization::Language* > interface
 */
#ifndef ____FIVectorView_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CGlobalization__CLanguage, 0x144b0f3d, 0x2d59, 0x5dd2, 0xb0,0x12, 0x90,0x8e,0xc3,0xe0,0x64,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("144b0f3d-2d59-5dd2-b012-908ec3e06435")
                IVectorView<ABI::Windows::Globalization::Language* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Globalization::Language*, ABI::Windows::Globalization::ILanguage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CGlobalization__CLanguage, 0x144b0f3d, 0x2d59, 0x5dd2, 0xb0,0x12, 0x90,0x8e,0xc3,0xe0,0x64,0x35)
#endif
#else
typedef struct __FIVectorView_1_Windows__CGlobalization__CLanguageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Globalization::Language* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This,
        UINT32 index,
        __x_ABI_CWindows_CGlobalization_CILanguage **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This,
        __x_ABI_CWindows_CGlobalization_CILanguage *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CGlobalization__CLanguage *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGlobalization_CILanguage **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CGlobalization__CLanguageVtbl;

interface __FIVectorView_1_Windows__CGlobalization__CLanguage {
    CONST_VTBL __FIVectorView_1_Windows__CGlobalization__CLanguageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Globalization::Language* > methods ***/
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CGlobalization__CLanguage_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGlobalization__CLanguage_QueryInterface(__FIVectorView_1_Windows__CGlobalization__CLanguage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CGlobalization__CLanguage_AddRef(__FIVectorView_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CGlobalization__CLanguage_Release(__FIVectorView_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGlobalization__CLanguage_GetIids(__FIVectorView_1_Windows__CGlobalization__CLanguage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName(__FIVectorView_1_Windows__CGlobalization__CLanguage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CGlobalization__CLanguage_GetTrustLevel(__FIVectorView_1_Windows__CGlobalization__CLanguage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Globalization::Language* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CGlobalization__CLanguage_GetAt(__FIVectorView_1_Windows__CGlobalization__CLanguage* This,UINT32 index,__x_ABI_CWindows_CGlobalization_CILanguage **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGlobalization__CLanguage_get_Size(__FIVectorView_1_Windows__CGlobalization__CLanguage* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGlobalization__CLanguage_IndexOf(__FIVectorView_1_Windows__CGlobalization__CLanguage* This,__x_ABI_CWindows_CGlobalization_CILanguage *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CGlobalization__CLanguage_GetMany(__FIVectorView_1_Windows__CGlobalization__CLanguage* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGlobalization_CILanguage **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_Language IID___FIVectorView_1_Windows__CGlobalization__CLanguage
#define IVectorView_LanguageVtbl __FIVectorView_1_Windows__CGlobalization__CLanguageVtbl
#define IVectorView_Language __FIVectorView_1_Windows__CGlobalization__CLanguage
#define IVectorView_Language_QueryInterface __FIVectorView_1_Windows__CGlobalization__CLanguage_QueryInterface
#define IVectorView_Language_AddRef __FIVectorView_1_Windows__CGlobalization__CLanguage_AddRef
#define IVectorView_Language_Release __FIVectorView_1_Windows__CGlobalization__CLanguage_Release
#define IVectorView_Language_GetIids __FIVectorView_1_Windows__CGlobalization__CLanguage_GetIids
#define IVectorView_Language_GetRuntimeClassName __FIVectorView_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName
#define IVectorView_Language_GetTrustLevel __FIVectorView_1_Windows__CGlobalization__CLanguage_GetTrustLevel
#define IVectorView_Language_GetAt __FIVectorView_1_Windows__CGlobalization__CLanguage_GetAt
#define IVectorView_Language_get_Size __FIVectorView_1_Windows__CGlobalization__CLanguage_get_Size
#define IVectorView_Language_IndexOf __FIVectorView_1_Windows__CGlobalization__CLanguage_IndexOf
#define IVectorView_Language_GetMany __FIVectorView_1_Windows__CGlobalization__CLanguage_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Globalization::Language* > interface
 */
#ifndef ____FIVector_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CGlobalization__CLanguage, 0xdcf2525a, 0x42c0, 0x501d, 0x9f,0xcb, 0x47,0x1f,0xae,0x06,0x03,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("dcf2525a-42c0-501d-9fcb-471fae060396")
                IVector<ABI::Windows::Globalization::Language* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Globalization::Language*, ABI::Windows::Globalization::ILanguage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CGlobalization__CLanguage, 0xdcf2525a, 0x42c0, 0x501d, 0x9f,0xcb, 0x47,0x1f,0xae,0x06,0x03,0x96)
#endif
#else
typedef struct __FIVector_1_Windows__CGlobalization__CLanguageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Globalization::Language* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        UINT32 index,
        __x_ABI_CWindows_CGlobalization_CILanguage **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        __FIVectorView_1_Windows__CGlobalization__CLanguage **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        __x_ABI_CWindows_CGlobalization_CILanguage *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        UINT32 index,
        __x_ABI_CWindows_CGlobalization_CILanguage *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        UINT32 index,
        __x_ABI_CWindows_CGlobalization_CILanguage *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        __x_ABI_CWindows_CGlobalization_CILanguage *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGlobalization_CILanguage **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CGlobalization__CLanguage *This,
        UINT32 count,
        __x_ABI_CWindows_CGlobalization_CILanguage **items);

    END_INTERFACE
} __FIVector_1_Windows__CGlobalization__CLanguageVtbl;

interface __FIVector_1_Windows__CGlobalization__CLanguage {
    CONST_VTBL __FIVector_1_Windows__CGlobalization__CLanguageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CGlobalization__CLanguage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CGlobalization__CLanguage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CGlobalization__CLanguage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CGlobalization__CLanguage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CGlobalization__CLanguage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Globalization::Language* > methods ***/
#define __FIVector_1_Windows__CGlobalization__CLanguage_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CGlobalization__CLanguage_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CGlobalization__CLanguage_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CGlobalization__CLanguage_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CGlobalization__CLanguage_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CGlobalization__CLanguage_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CGlobalization__CLanguage_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CGlobalization__CLanguage_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CGlobalization__CLanguage_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CGlobalization__CLanguage_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CGlobalization__CLanguage_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CGlobalization__CLanguage_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_QueryInterface(__FIVector_1_Windows__CGlobalization__CLanguage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CGlobalization__CLanguage_AddRef(__FIVector_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CGlobalization__CLanguage_Release(__FIVector_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_GetIids(__FIVector_1_Windows__CGlobalization__CLanguage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName(__FIVector_1_Windows__CGlobalization__CLanguage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_GetTrustLevel(__FIVector_1_Windows__CGlobalization__CLanguage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Globalization::Language* > methods ***/
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_GetAt(__FIVector_1_Windows__CGlobalization__CLanguage* This,UINT32 index,__x_ABI_CWindows_CGlobalization_CILanguage **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_get_Size(__FIVector_1_Windows__CGlobalization__CLanguage* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_GetView(__FIVector_1_Windows__CGlobalization__CLanguage* This,__FIVectorView_1_Windows__CGlobalization__CLanguage **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_IndexOf(__FIVector_1_Windows__CGlobalization__CLanguage* This,__x_ABI_CWindows_CGlobalization_CILanguage *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_SetAt(__FIVector_1_Windows__CGlobalization__CLanguage* This,UINT32 index,__x_ABI_CWindows_CGlobalization_CILanguage *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_InsertAt(__FIVector_1_Windows__CGlobalization__CLanguage* This,UINT32 index,__x_ABI_CWindows_CGlobalization_CILanguage *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_RemoveAt(__FIVector_1_Windows__CGlobalization__CLanguage* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_Append(__FIVector_1_Windows__CGlobalization__CLanguage* This,__x_ABI_CWindows_CGlobalization_CILanguage *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_RemoveAtEnd(__FIVector_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_Clear(__FIVector_1_Windows__CGlobalization__CLanguage* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_GetMany(__FIVector_1_Windows__CGlobalization__CLanguage* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGlobalization_CILanguage **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CGlobalization__CLanguage_ReplaceAll(__FIVector_1_Windows__CGlobalization__CLanguage* This,UINT32 count,__x_ABI_CWindows_CGlobalization_CILanguage **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_Language IID___FIVector_1_Windows__CGlobalization__CLanguage
#define IVector_LanguageVtbl __FIVector_1_Windows__CGlobalization__CLanguageVtbl
#define IVector_Language __FIVector_1_Windows__CGlobalization__CLanguage
#define IVector_Language_QueryInterface __FIVector_1_Windows__CGlobalization__CLanguage_QueryInterface
#define IVector_Language_AddRef __FIVector_1_Windows__CGlobalization__CLanguage_AddRef
#define IVector_Language_Release __FIVector_1_Windows__CGlobalization__CLanguage_Release
#define IVector_Language_GetIids __FIVector_1_Windows__CGlobalization__CLanguage_GetIids
#define IVector_Language_GetRuntimeClassName __FIVector_1_Windows__CGlobalization__CLanguage_GetRuntimeClassName
#define IVector_Language_GetTrustLevel __FIVector_1_Windows__CGlobalization__CLanguage_GetTrustLevel
#define IVector_Language_GetAt __FIVector_1_Windows__CGlobalization__CLanguage_GetAt
#define IVector_Language_get_Size __FIVector_1_Windows__CGlobalization__CLanguage_get_Size
#define IVector_Language_GetView __FIVector_1_Windows__CGlobalization__CLanguage_GetView
#define IVector_Language_IndexOf __FIVector_1_Windows__CGlobalization__CLanguage_IndexOf
#define IVector_Language_SetAt __FIVector_1_Windows__CGlobalization__CLanguage_SetAt
#define IVector_Language_InsertAt __FIVector_1_Windows__CGlobalization__CLanguage_InsertAt
#define IVector_Language_RemoveAt __FIVector_1_Windows__CGlobalization__CLanguage_RemoveAt
#define IVector_Language_Append __FIVector_1_Windows__CGlobalization__CLanguage_Append
#define IVector_Language_RemoveAtEnd __FIVector_1_Windows__CGlobalization__CLanguage_RemoveAtEnd
#define IVector_Language_Clear __FIVector_1_Windows__CGlobalization__CLanguage_Clear
#define IVector_Language_GetMany __FIVector_1_Windows__CGlobalization__CLanguage_GetMany
#define IVector_Language_ReplaceAll __FIVector_1_Windows__CGlobalization__CLanguage_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CGlobalization__CLanguage_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_globalization_h__ */
