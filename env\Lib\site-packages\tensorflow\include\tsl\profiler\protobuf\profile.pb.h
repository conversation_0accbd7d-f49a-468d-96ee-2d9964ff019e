// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tsl/profiler/protobuf/profile.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
namespace tensorflow {
namespace tfprof {
namespace pprof {
class Function;
struct FunctionDefaultTypeInternal;
extern FunctionDefaultTypeInternal _Function_default_instance_;
class Label;
struct LabelDefaultTypeInternal;
extern LabelDefaultTypeInternal _Label_default_instance_;
class Line;
struct LineDefaultTypeInternal;
extern LineDefaultTypeInternal _Line_default_instance_;
class Location;
struct LocationDefaultTypeInternal;
extern LocationDefaultTypeInternal _Location_default_instance_;
class Mapping;
struct MappingDefaultTypeInternal;
extern MappingDefaultTypeInternal _Mapping_default_instance_;
class Profile;
struct ProfileDefaultTypeInternal;
extern ProfileDefaultTypeInternal _Profile_default_instance_;
class Sample;
struct SampleDefaultTypeInternal;
extern SampleDefaultTypeInternal _Sample_default_instance_;
class ValueType;
struct ValueTypeDefaultTypeInternal;
extern ValueTypeDefaultTypeInternal _ValueType_default_instance_;
}  // namespace pprof
}  // namespace tfprof
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tfprof::pprof::Function* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Function>(Arena*);
template<> ::tensorflow::tfprof::pprof::Label* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Label>(Arena*);
template<> ::tensorflow::tfprof::pprof::Line* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Line>(Arena*);
template<> ::tensorflow::tfprof::pprof::Location* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Location>(Arena*);
template<> ::tensorflow::tfprof::pprof::Mapping* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Mapping>(Arena*);
template<> ::tensorflow::tfprof::pprof::Profile* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Profile>(Arena*);
template<> ::tensorflow::tfprof::pprof::Sample* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::Sample>(Arena*);
template<> ::tensorflow::tfprof::pprof::ValueType* Arena::CreateMaybeMessage<::tensorflow::tfprof::pprof::ValueType>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tfprof {
namespace pprof {

// ===================================================================

class Profile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Profile) */ {
 public:
  inline Profile() : Profile(nullptr) {}
  ~Profile() override;
  explicit PROTOBUF_CONSTEXPR Profile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Profile(const Profile& from);
  Profile(Profile&& from) noexcept
    : Profile() {
    *this = ::std::move(from);
  }

  inline Profile& operator=(const Profile& from) {
    CopyFrom(from);
    return *this;
  }
  inline Profile& operator=(Profile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Profile& default_instance() {
    return *internal_default_instance();
  }
  static inline const Profile* internal_default_instance() {
    return reinterpret_cast<const Profile*>(
               &_Profile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Profile& a, Profile& b) {
    a.Swap(&b);
  }
  inline void Swap(Profile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Profile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Profile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Profile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Profile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Profile& from) {
    Profile::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Profile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Profile";
  }
  protected:
  explicit Profile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSampleTypeFieldNumber = 1,
    kSampleFieldNumber = 2,
    kMappingFieldNumber = 3,
    kLocationFieldNumber = 4,
    kFunctionFieldNumber = 5,
    kStringTableFieldNumber = 6,
    kCommentFieldNumber = 13,
    kPeriodTypeFieldNumber = 11,
    kDropFramesFieldNumber = 7,
    kKeepFramesFieldNumber = 8,
    kTimeNanosFieldNumber = 9,
    kDurationNanosFieldNumber = 10,
    kPeriodFieldNumber = 12,
    kDefaultSampleTypeFieldNumber = 14,
  };
  // repeated .tensorflow.tfprof.pprof.ValueType sample_type = 1;
  int sample_type_size() const;
  private:
  int _internal_sample_type_size() const;
  public:
  void clear_sample_type();
  ::tensorflow::tfprof::pprof::ValueType* mutable_sample_type(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType >*
      mutable_sample_type();
  private:
  const ::tensorflow::tfprof::pprof::ValueType& _internal_sample_type(int index) const;
  ::tensorflow::tfprof::pprof::ValueType* _internal_add_sample_type();
  public:
  const ::tensorflow::tfprof::pprof::ValueType& sample_type(int index) const;
  ::tensorflow::tfprof::pprof::ValueType* add_sample_type();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType >&
      sample_type() const;

  // repeated .tensorflow.tfprof.pprof.Sample sample = 2;
  int sample_size() const;
  private:
  int _internal_sample_size() const;
  public:
  void clear_sample();
  ::tensorflow::tfprof::pprof::Sample* mutable_sample(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample >*
      mutable_sample();
  private:
  const ::tensorflow::tfprof::pprof::Sample& _internal_sample(int index) const;
  ::tensorflow::tfprof::pprof::Sample* _internal_add_sample();
  public:
  const ::tensorflow::tfprof::pprof::Sample& sample(int index) const;
  ::tensorflow::tfprof::pprof::Sample* add_sample();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample >&
      sample() const;

  // repeated .tensorflow.tfprof.pprof.Mapping mapping = 3;
  int mapping_size() const;
  private:
  int _internal_mapping_size() const;
  public:
  void clear_mapping();
  ::tensorflow::tfprof::pprof::Mapping* mutable_mapping(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping >*
      mutable_mapping();
  private:
  const ::tensorflow::tfprof::pprof::Mapping& _internal_mapping(int index) const;
  ::tensorflow::tfprof::pprof::Mapping* _internal_add_mapping();
  public:
  const ::tensorflow::tfprof::pprof::Mapping& mapping(int index) const;
  ::tensorflow::tfprof::pprof::Mapping* add_mapping();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping >&
      mapping() const;

  // repeated .tensorflow.tfprof.pprof.Location location = 4;
  int location_size() const;
  private:
  int _internal_location_size() const;
  public:
  void clear_location();
  ::tensorflow::tfprof::pprof::Location* mutable_location(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location >*
      mutable_location();
  private:
  const ::tensorflow::tfprof::pprof::Location& _internal_location(int index) const;
  ::tensorflow::tfprof::pprof::Location* _internal_add_location();
  public:
  const ::tensorflow::tfprof::pprof::Location& location(int index) const;
  ::tensorflow::tfprof::pprof::Location* add_location();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location >&
      location() const;

  // repeated .tensorflow.tfprof.pprof.Function function = 5;
  int function_size() const;
  private:
  int _internal_function_size() const;
  public:
  void clear_function();
  ::tensorflow::tfprof::pprof::Function* mutable_function(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function >*
      mutable_function();
  private:
  const ::tensorflow::tfprof::pprof::Function& _internal_function(int index) const;
  ::tensorflow::tfprof::pprof::Function* _internal_add_function();
  public:
  const ::tensorflow::tfprof::pprof::Function& function(int index) const;
  ::tensorflow::tfprof::pprof::Function* add_function();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function >&
      function() const;

  // repeated string string_table = 6;
  int string_table_size() const;
  private:
  int _internal_string_table_size() const;
  public:
  void clear_string_table();
  const std::string& string_table(int index) const;
  std::string* mutable_string_table(int index);
  void set_string_table(int index, const std::string& value);
  void set_string_table(int index, std::string&& value);
  void set_string_table(int index, const char* value);
  void set_string_table(int index, const char* value, size_t size);
  std::string* add_string_table();
  void add_string_table(const std::string& value);
  void add_string_table(std::string&& value);
  void add_string_table(const char* value);
  void add_string_table(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& string_table() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_string_table();
  private:
  const std::string& _internal_string_table(int index) const;
  std::string* _internal_add_string_table();
  public:

  // repeated int64 comment = 13;
  int comment_size() const;
  private:
  int _internal_comment_size() const;
  public:
  void clear_comment();
  private:
  int64_t _internal_comment(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_comment() const;
  void _internal_add_comment(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_comment();
  public:
  int64_t comment(int index) const;
  void set_comment(int index, int64_t value);
  void add_comment(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      comment() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_comment();

  // .tensorflow.tfprof.pprof.ValueType period_type = 11;
  bool has_period_type() const;
  private:
  bool _internal_has_period_type() const;
  public:
  void clear_period_type();
  const ::tensorflow::tfprof::pprof::ValueType& period_type() const;
  PROTOBUF_NODISCARD ::tensorflow::tfprof::pprof::ValueType* release_period_type();
  ::tensorflow::tfprof::pprof::ValueType* mutable_period_type();
  void set_allocated_period_type(::tensorflow::tfprof::pprof::ValueType* period_type);
  private:
  const ::tensorflow::tfprof::pprof::ValueType& _internal_period_type() const;
  ::tensorflow::tfprof::pprof::ValueType* _internal_mutable_period_type();
  public:
  void unsafe_arena_set_allocated_period_type(
      ::tensorflow::tfprof::pprof::ValueType* period_type);
  ::tensorflow::tfprof::pprof::ValueType* unsafe_arena_release_period_type();

  // int64 drop_frames = 7;
  void clear_drop_frames();
  int64_t drop_frames() const;
  void set_drop_frames(int64_t value);
  private:
  int64_t _internal_drop_frames() const;
  void _internal_set_drop_frames(int64_t value);
  public:

  // int64 keep_frames = 8;
  void clear_keep_frames();
  int64_t keep_frames() const;
  void set_keep_frames(int64_t value);
  private:
  int64_t _internal_keep_frames() const;
  void _internal_set_keep_frames(int64_t value);
  public:

  // int64 time_nanos = 9;
  void clear_time_nanos();
  int64_t time_nanos() const;
  void set_time_nanos(int64_t value);
  private:
  int64_t _internal_time_nanos() const;
  void _internal_set_time_nanos(int64_t value);
  public:

  // int64 duration_nanos = 10;
  void clear_duration_nanos();
  int64_t duration_nanos() const;
  void set_duration_nanos(int64_t value);
  private:
  int64_t _internal_duration_nanos() const;
  void _internal_set_duration_nanos(int64_t value);
  public:

  // int64 period = 12;
  void clear_period();
  int64_t period() const;
  void set_period(int64_t value);
  private:
  int64_t _internal_period() const;
  void _internal_set_period(int64_t value);
  public:

  // int64 default_sample_type = 14;
  void clear_default_sample_type();
  int64_t default_sample_type() const;
  void set_default_sample_type(int64_t value);
  private:
  int64_t _internal_default_sample_type() const;
  void _internal_set_default_sample_type(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Profile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType > sample_type_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample > sample_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping > mapping_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location > location_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function > function_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> string_table_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > comment_;
    mutable std::atomic<int> _comment_cached_byte_size_;
    ::tensorflow::tfprof::pprof::ValueType* period_type_;
    int64_t drop_frames_;
    int64_t keep_frames_;
    int64_t time_nanos_;
    int64_t duration_nanos_;
    int64_t period_;
    int64_t default_sample_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class ValueType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.ValueType) */ {
 public:
  inline ValueType() : ValueType(nullptr) {}
  ~ValueType() override;
  explicit PROTOBUF_CONSTEXPR ValueType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ValueType(const ValueType& from);
  ValueType(ValueType&& from) noexcept
    : ValueType() {
    *this = ::std::move(from);
  }

  inline ValueType& operator=(const ValueType& from) {
    CopyFrom(from);
    return *this;
  }
  inline ValueType& operator=(ValueType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ValueType& default_instance() {
    return *internal_default_instance();
  }
  static inline const ValueType* internal_default_instance() {
    return reinterpret_cast<const ValueType*>(
               &_ValueType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ValueType& a, ValueType& b) {
    a.Swap(&b);
  }
  inline void Swap(ValueType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ValueType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ValueType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ValueType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ValueType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ValueType& from) {
    ValueType::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ValueType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.ValueType";
  }
  protected:
  explicit ValueType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 1,
    kUnitFieldNumber = 2,
  };
  // int64 type = 1;
  void clear_type();
  int64_t type() const;
  void set_type(int64_t value);
  private:
  int64_t _internal_type() const;
  void _internal_set_type(int64_t value);
  public:

  // int64 unit = 2;
  void clear_unit();
  int64_t unit() const;
  void set_unit(int64_t value);
  private:
  int64_t _internal_unit() const;
  void _internal_set_unit(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.ValueType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t type_;
    int64_t unit_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Sample final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Sample) */ {
 public:
  inline Sample() : Sample(nullptr) {}
  ~Sample() override;
  explicit PROTOBUF_CONSTEXPR Sample(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Sample(const Sample& from);
  Sample(Sample&& from) noexcept
    : Sample() {
    *this = ::std::move(from);
  }

  inline Sample& operator=(const Sample& from) {
    CopyFrom(from);
    return *this;
  }
  inline Sample& operator=(Sample&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Sample& default_instance() {
    return *internal_default_instance();
  }
  static inline const Sample* internal_default_instance() {
    return reinterpret_cast<const Sample*>(
               &_Sample_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Sample& a, Sample& b) {
    a.Swap(&b);
  }
  inline void Swap(Sample* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Sample* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Sample* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Sample>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Sample& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Sample& from) {
    Sample::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Sample* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Sample";
  }
  protected:
  explicit Sample(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocationIdFieldNumber = 1,
    kValueFieldNumber = 2,
    kLabelFieldNumber = 3,
  };
  // repeated uint64 location_id = 1;
  int location_id_size() const;
  private:
  int _internal_location_id_size() const;
  public:
  void clear_location_id();
  private:
  uint64_t _internal_location_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_location_id() const;
  void _internal_add_location_id(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_location_id();
  public:
  uint64_t location_id(int index) const;
  void set_location_id(int index, uint64_t value);
  void add_location_id(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      location_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_location_id();

  // repeated int64 value = 2;
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  private:
  int64_t _internal_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_value() const;
  void _internal_add_value(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_value();
  public:
  int64_t value(int index) const;
  void set_value(int index, int64_t value);
  void add_value(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_value();

  // repeated .tensorflow.tfprof.pprof.Label label = 3;
  int label_size() const;
  private:
  int _internal_label_size() const;
  public:
  void clear_label();
  ::tensorflow::tfprof::pprof::Label* mutable_label(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label >*
      mutable_label();
  private:
  const ::tensorflow::tfprof::pprof::Label& _internal_label(int index) const;
  ::tensorflow::tfprof::pprof::Label* _internal_add_label();
  public:
  const ::tensorflow::tfprof::pprof::Label& label(int index) const;
  ::tensorflow::tfprof::pprof::Label* add_label();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label >&
      label() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Sample)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > location_id_;
    mutable std::atomic<int> _location_id_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > value_;
    mutable std::atomic<int> _value_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label > label_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Label final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Label) */ {
 public:
  inline Label() : Label(nullptr) {}
  ~Label() override;
  explicit PROTOBUF_CONSTEXPR Label(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Label(const Label& from);
  Label(Label&& from) noexcept
    : Label() {
    *this = ::std::move(from);
  }

  inline Label& operator=(const Label& from) {
    CopyFrom(from);
    return *this;
  }
  inline Label& operator=(Label&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Label& default_instance() {
    return *internal_default_instance();
  }
  static inline const Label* internal_default_instance() {
    return reinterpret_cast<const Label*>(
               &_Label_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Label& a, Label& b) {
    a.Swap(&b);
  }
  inline void Swap(Label* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Label* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Label* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Label>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Label& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Label& from) {
    Label::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Label* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Label";
  }
  protected:
  explicit Label(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kStrFieldNumber = 2,
    kNumFieldNumber = 3,
  };
  // int64 key = 1;
  void clear_key();
  int64_t key() const;
  void set_key(int64_t value);
  private:
  int64_t _internal_key() const;
  void _internal_set_key(int64_t value);
  public:

  // int64 str = 2;
  void clear_str();
  int64_t str() const;
  void set_str(int64_t value);
  private:
  int64_t _internal_str() const;
  void _internal_set_str(int64_t value);
  public:

  // int64 num = 3;
  void clear_num();
  int64_t num() const;
  void set_num(int64_t value);
  private:
  int64_t _internal_num() const;
  void _internal_set_num(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Label)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t key_;
    int64_t str_;
    int64_t num_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Mapping final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Mapping) */ {
 public:
  inline Mapping() : Mapping(nullptr) {}
  ~Mapping() override;
  explicit PROTOBUF_CONSTEXPR Mapping(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Mapping(const Mapping& from);
  Mapping(Mapping&& from) noexcept
    : Mapping() {
    *this = ::std::move(from);
  }

  inline Mapping& operator=(const Mapping& from) {
    CopyFrom(from);
    return *this;
  }
  inline Mapping& operator=(Mapping&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Mapping& default_instance() {
    return *internal_default_instance();
  }
  static inline const Mapping* internal_default_instance() {
    return reinterpret_cast<const Mapping*>(
               &_Mapping_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Mapping& a, Mapping& b) {
    a.Swap(&b);
  }
  inline void Swap(Mapping* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Mapping* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Mapping* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Mapping>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Mapping& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Mapping& from) {
    Mapping::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Mapping* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Mapping";
  }
  protected:
  explicit Mapping(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kMemoryStartFieldNumber = 2,
    kMemoryLimitFieldNumber = 3,
    kFileOffsetFieldNumber = 4,
    kFilenameFieldNumber = 5,
    kBuildIdFieldNumber = 6,
    kHasFunctionsFieldNumber = 7,
    kHasFilenamesFieldNumber = 8,
    kHasLineNumbersFieldNumber = 9,
    kHasInlineFramesFieldNumber = 10,
  };
  // uint64 id = 1;
  void clear_id();
  uint64_t id() const;
  void set_id(uint64_t value);
  private:
  uint64_t _internal_id() const;
  void _internal_set_id(uint64_t value);
  public:

  // uint64 memory_start = 2;
  void clear_memory_start();
  uint64_t memory_start() const;
  void set_memory_start(uint64_t value);
  private:
  uint64_t _internal_memory_start() const;
  void _internal_set_memory_start(uint64_t value);
  public:

  // uint64 memory_limit = 3;
  void clear_memory_limit();
  uint64_t memory_limit() const;
  void set_memory_limit(uint64_t value);
  private:
  uint64_t _internal_memory_limit() const;
  void _internal_set_memory_limit(uint64_t value);
  public:

  // uint64 file_offset = 4;
  void clear_file_offset();
  uint64_t file_offset() const;
  void set_file_offset(uint64_t value);
  private:
  uint64_t _internal_file_offset() const;
  void _internal_set_file_offset(uint64_t value);
  public:

  // int64 filename = 5;
  void clear_filename();
  int64_t filename() const;
  void set_filename(int64_t value);
  private:
  int64_t _internal_filename() const;
  void _internal_set_filename(int64_t value);
  public:

  // int64 build_id = 6;
  void clear_build_id();
  int64_t build_id() const;
  void set_build_id(int64_t value);
  private:
  int64_t _internal_build_id() const;
  void _internal_set_build_id(int64_t value);
  public:

  // bool has_functions = 7;
  void clear_has_functions();
  bool has_functions() const;
  void set_has_functions(bool value);
  private:
  bool _internal_has_functions() const;
  void _internal_set_has_functions(bool value);
  public:

  // bool has_filenames = 8;
  void clear_has_filenames();
  bool has_filenames() const;
  void set_has_filenames(bool value);
  private:
  bool _internal_has_filenames() const;
  void _internal_set_has_filenames(bool value);
  public:

  // bool has_line_numbers = 9;
  void clear_has_line_numbers();
  bool has_line_numbers() const;
  void set_has_line_numbers(bool value);
  private:
  bool _internal_has_line_numbers() const;
  void _internal_set_has_line_numbers(bool value);
  public:

  // bool has_inline_frames = 10;
  void clear_has_inline_frames();
  bool has_inline_frames() const;
  void set_has_inline_frames(bool value);
  private:
  bool _internal_has_inline_frames() const;
  void _internal_set_has_inline_frames(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Mapping)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t id_;
    uint64_t memory_start_;
    uint64_t memory_limit_;
    uint64_t file_offset_;
    int64_t filename_;
    int64_t build_id_;
    bool has_functions_;
    bool has_filenames_;
    bool has_line_numbers_;
    bool has_inline_frames_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Location final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Location) */ {
 public:
  inline Location() : Location(nullptr) {}
  ~Location() override;
  explicit PROTOBUF_CONSTEXPR Location(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Location(const Location& from);
  Location(Location&& from) noexcept
    : Location() {
    *this = ::std::move(from);
  }

  inline Location& operator=(const Location& from) {
    CopyFrom(from);
    return *this;
  }
  inline Location& operator=(Location&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Location& default_instance() {
    return *internal_default_instance();
  }
  static inline const Location* internal_default_instance() {
    return reinterpret_cast<const Location*>(
               &_Location_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Location& a, Location& b) {
    a.Swap(&b);
  }
  inline void Swap(Location* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Location* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Location* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Location>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Location& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Location& from) {
    Location::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Location* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Location";
  }
  protected:
  explicit Location(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLineFieldNumber = 4,
    kIdFieldNumber = 1,
    kMappingIdFieldNumber = 2,
    kAddressFieldNumber = 3,
  };
  // repeated .tensorflow.tfprof.pprof.Line line = 4;
  int line_size() const;
  private:
  int _internal_line_size() const;
  public:
  void clear_line();
  ::tensorflow::tfprof::pprof::Line* mutable_line(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line >*
      mutable_line();
  private:
  const ::tensorflow::tfprof::pprof::Line& _internal_line(int index) const;
  ::tensorflow::tfprof::pprof::Line* _internal_add_line();
  public:
  const ::tensorflow::tfprof::pprof::Line& line(int index) const;
  ::tensorflow::tfprof::pprof::Line* add_line();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line >&
      line() const;

  // uint64 id = 1;
  void clear_id();
  uint64_t id() const;
  void set_id(uint64_t value);
  private:
  uint64_t _internal_id() const;
  void _internal_set_id(uint64_t value);
  public:

  // uint64 mapping_id = 2;
  void clear_mapping_id();
  uint64_t mapping_id() const;
  void set_mapping_id(uint64_t value);
  private:
  uint64_t _internal_mapping_id() const;
  void _internal_set_mapping_id(uint64_t value);
  public:

  // uint64 address = 3;
  void clear_address();
  uint64_t address() const;
  void set_address(uint64_t value);
  private:
  uint64_t _internal_address() const;
  void _internal_set_address(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Location)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line > line_;
    uint64_t id_;
    uint64_t mapping_id_;
    uint64_t address_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Line final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Line) */ {
 public:
  inline Line() : Line(nullptr) {}
  ~Line() override;
  explicit PROTOBUF_CONSTEXPR Line(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Line(const Line& from);
  Line(Line&& from) noexcept
    : Line() {
    *this = ::std::move(from);
  }

  inline Line& operator=(const Line& from) {
    CopyFrom(from);
    return *this;
  }
  inline Line& operator=(Line&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Line& default_instance() {
    return *internal_default_instance();
  }
  static inline const Line* internal_default_instance() {
    return reinterpret_cast<const Line*>(
               &_Line_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Line& a, Line& b) {
    a.Swap(&b);
  }
  inline void Swap(Line* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Line* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Line* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Line>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Line& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Line& from) {
    Line::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Line* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Line";
  }
  protected:
  explicit Line(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionIdFieldNumber = 1,
    kLineFieldNumber = 2,
  };
  // uint64 function_id = 1;
  void clear_function_id();
  uint64_t function_id() const;
  void set_function_id(uint64_t value);
  private:
  uint64_t _internal_function_id() const;
  void _internal_set_function_id(uint64_t value);
  public:

  // int64 line = 2;
  void clear_line();
  int64_t line() const;
  void set_line(int64_t value);
  private:
  int64_t _internal_line() const;
  void _internal_set_line(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Line)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t function_id_;
    int64_t line_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
};
// -------------------------------------------------------------------

class Function final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.pprof.Function) */ {
 public:
  inline Function() : Function(nullptr) {}
  ~Function() override;
  explicit PROTOBUF_CONSTEXPR Function(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Function(const Function& from);
  Function(Function&& from) noexcept
    : Function() {
    *this = ::std::move(from);
  }

  inline Function& operator=(const Function& from) {
    CopyFrom(from);
    return *this;
  }
  inline Function& operator=(Function&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Function& default_instance() {
    return *internal_default_instance();
  }
  static inline const Function* internal_default_instance() {
    return reinterpret_cast<const Function*>(
               &_Function_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(Function& a, Function& b) {
    a.Swap(&b);
  }
  inline void Swap(Function* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Function* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Function* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Function>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Function& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Function& from) {
    Function::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Function* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.pprof.Function";
  }
  protected:
  explicit Function(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kNameFieldNumber = 2,
    kSystemNameFieldNumber = 3,
    kFilenameFieldNumber = 4,
    kStartLineFieldNumber = 5,
  };
  // uint64 id = 1;
  void clear_id();
  uint64_t id() const;
  void set_id(uint64_t value);
  private:
  uint64_t _internal_id() const;
  void _internal_set_id(uint64_t value);
  public:

  // int64 name = 2;
  void clear_name();
  int64_t name() const;
  void set_name(int64_t value);
  private:
  int64_t _internal_name() const;
  void _internal_set_name(int64_t value);
  public:

  // int64 system_name = 3;
  void clear_system_name();
  int64_t system_name() const;
  void set_system_name(int64_t value);
  private:
  int64_t _internal_system_name() const;
  void _internal_set_system_name(int64_t value);
  public:

  // int64 filename = 4;
  void clear_filename();
  int64_t filename() const;
  void set_filename(int64_t value);
  private:
  int64_t _internal_filename() const;
  void _internal_set_filename(int64_t value);
  public:

  // int64 start_line = 5;
  void clear_start_line();
  int64_t start_line() const;
  void set_start_line(int64_t value);
  private:
  int64_t _internal_start_line() const;
  void _internal_set_start_line(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.pprof.Function)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t id_;
    int64_t name_;
    int64_t system_name_;
    int64_t filename_;
    int64_t start_line_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Profile

// repeated .tensorflow.tfprof.pprof.ValueType sample_type = 1;
inline int Profile::_internal_sample_type_size() const {
  return _impl_.sample_type_.size();
}
inline int Profile::sample_type_size() const {
  return _internal_sample_type_size();
}
inline void Profile::clear_sample_type() {
  _impl_.sample_type_.Clear();
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::mutable_sample_type(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.sample_type)
  return _impl_.sample_type_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType >*
Profile::mutable_sample_type() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.sample_type)
  return &_impl_.sample_type_;
}
inline const ::tensorflow::tfprof::pprof::ValueType& Profile::_internal_sample_type(int index) const {
  return _impl_.sample_type_.Get(index);
}
inline const ::tensorflow::tfprof::pprof::ValueType& Profile::sample_type(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.sample_type)
  return _internal_sample_type(index);
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::_internal_add_sample_type() {
  return _impl_.sample_type_.Add();
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::add_sample_type() {
  ::tensorflow::tfprof::pprof::ValueType* _add = _internal_add_sample_type();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.sample_type)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::ValueType >&
Profile::sample_type() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.sample_type)
  return _impl_.sample_type_;
}

// repeated .tensorflow.tfprof.pprof.Sample sample = 2;
inline int Profile::_internal_sample_size() const {
  return _impl_.sample_.size();
}
inline int Profile::sample_size() const {
  return _internal_sample_size();
}
inline void Profile::clear_sample() {
  _impl_.sample_.Clear();
}
inline ::tensorflow::tfprof::pprof::Sample* Profile::mutable_sample(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.sample)
  return _impl_.sample_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample >*
Profile::mutable_sample() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.sample)
  return &_impl_.sample_;
}
inline const ::tensorflow::tfprof::pprof::Sample& Profile::_internal_sample(int index) const {
  return _impl_.sample_.Get(index);
}
inline const ::tensorflow::tfprof::pprof::Sample& Profile::sample(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.sample)
  return _internal_sample(index);
}
inline ::tensorflow::tfprof::pprof::Sample* Profile::_internal_add_sample() {
  return _impl_.sample_.Add();
}
inline ::tensorflow::tfprof::pprof::Sample* Profile::add_sample() {
  ::tensorflow::tfprof::pprof::Sample* _add = _internal_add_sample();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.sample)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Sample >&
Profile::sample() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.sample)
  return _impl_.sample_;
}

// repeated .tensorflow.tfprof.pprof.Mapping mapping = 3;
inline int Profile::_internal_mapping_size() const {
  return _impl_.mapping_.size();
}
inline int Profile::mapping_size() const {
  return _internal_mapping_size();
}
inline void Profile::clear_mapping() {
  _impl_.mapping_.Clear();
}
inline ::tensorflow::tfprof::pprof::Mapping* Profile::mutable_mapping(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.mapping)
  return _impl_.mapping_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping >*
Profile::mutable_mapping() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.mapping)
  return &_impl_.mapping_;
}
inline const ::tensorflow::tfprof::pprof::Mapping& Profile::_internal_mapping(int index) const {
  return _impl_.mapping_.Get(index);
}
inline const ::tensorflow::tfprof::pprof::Mapping& Profile::mapping(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.mapping)
  return _internal_mapping(index);
}
inline ::tensorflow::tfprof::pprof::Mapping* Profile::_internal_add_mapping() {
  return _impl_.mapping_.Add();
}
inline ::tensorflow::tfprof::pprof::Mapping* Profile::add_mapping() {
  ::tensorflow::tfprof::pprof::Mapping* _add = _internal_add_mapping();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.mapping)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Mapping >&
Profile::mapping() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.mapping)
  return _impl_.mapping_;
}

// repeated .tensorflow.tfprof.pprof.Location location = 4;
inline int Profile::_internal_location_size() const {
  return _impl_.location_.size();
}
inline int Profile::location_size() const {
  return _internal_location_size();
}
inline void Profile::clear_location() {
  _impl_.location_.Clear();
}
inline ::tensorflow::tfprof::pprof::Location* Profile::mutable_location(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.location)
  return _impl_.location_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location >*
Profile::mutable_location() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.location)
  return &_impl_.location_;
}
inline const ::tensorflow::tfprof::pprof::Location& Profile::_internal_location(int index) const {
  return _impl_.location_.Get(index);
}
inline const ::tensorflow::tfprof::pprof::Location& Profile::location(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.location)
  return _internal_location(index);
}
inline ::tensorflow::tfprof::pprof::Location* Profile::_internal_add_location() {
  return _impl_.location_.Add();
}
inline ::tensorflow::tfprof::pprof::Location* Profile::add_location() {
  ::tensorflow::tfprof::pprof::Location* _add = _internal_add_location();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.location)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Location >&
Profile::location() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.location)
  return _impl_.location_;
}

// repeated .tensorflow.tfprof.pprof.Function function = 5;
inline int Profile::_internal_function_size() const {
  return _impl_.function_.size();
}
inline int Profile::function_size() const {
  return _internal_function_size();
}
inline void Profile::clear_function() {
  _impl_.function_.Clear();
}
inline ::tensorflow::tfprof::pprof::Function* Profile::mutable_function(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.function)
  return _impl_.function_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function >*
Profile::mutable_function() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.function)
  return &_impl_.function_;
}
inline const ::tensorflow::tfprof::pprof::Function& Profile::_internal_function(int index) const {
  return _impl_.function_.Get(index);
}
inline const ::tensorflow::tfprof::pprof::Function& Profile::function(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.function)
  return _internal_function(index);
}
inline ::tensorflow::tfprof::pprof::Function* Profile::_internal_add_function() {
  return _impl_.function_.Add();
}
inline ::tensorflow::tfprof::pprof::Function* Profile::add_function() {
  ::tensorflow::tfprof::pprof::Function* _add = _internal_add_function();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.function)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Function >&
Profile::function() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.function)
  return _impl_.function_;
}

// repeated string string_table = 6;
inline int Profile::_internal_string_table_size() const {
  return _impl_.string_table_.size();
}
inline int Profile::string_table_size() const {
  return _internal_string_table_size();
}
inline void Profile::clear_string_table() {
  _impl_.string_table_.Clear();
}
inline std::string* Profile::add_string_table() {
  std::string* _s = _internal_add_string_table();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.pprof.Profile.string_table)
  return _s;
}
inline const std::string& Profile::_internal_string_table(int index) const {
  return _impl_.string_table_.Get(index);
}
inline const std::string& Profile::string_table(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.string_table)
  return _internal_string_table(index);
}
inline std::string* Profile::mutable_string_table(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.string_table)
  return _impl_.string_table_.Mutable(index);
}
inline void Profile::set_string_table(int index, const std::string& value) {
  _impl_.string_table_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::set_string_table(int index, std::string&& value) {
  _impl_.string_table_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::set_string_table(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.string_table_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::set_string_table(int index, const char* value, size_t size) {
  _impl_.string_table_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.pprof.Profile.string_table)
}
inline std::string* Profile::_internal_add_string_table() {
  return _impl_.string_table_.Add();
}
inline void Profile::add_string_table(const std::string& value) {
  _impl_.string_table_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::add_string_table(std::string&& value) {
  _impl_.string_table_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::add_string_table(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.string_table_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.pprof.Profile.string_table)
}
inline void Profile::add_string_table(const char* value, size_t size) {
  _impl_.string_table_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.pprof.Profile.string_table)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Profile::string_table() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.string_table)
  return _impl_.string_table_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Profile::mutable_string_table() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.string_table)
  return &_impl_.string_table_;
}

// int64 drop_frames = 7;
inline void Profile::clear_drop_frames() {
  _impl_.drop_frames_ = int64_t{0};
}
inline int64_t Profile::_internal_drop_frames() const {
  return _impl_.drop_frames_;
}
inline int64_t Profile::drop_frames() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.drop_frames)
  return _internal_drop_frames();
}
inline void Profile::_internal_set_drop_frames(int64_t value) {
  
  _impl_.drop_frames_ = value;
}
inline void Profile::set_drop_frames(int64_t value) {
  _internal_set_drop_frames(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.drop_frames)
}

// int64 keep_frames = 8;
inline void Profile::clear_keep_frames() {
  _impl_.keep_frames_ = int64_t{0};
}
inline int64_t Profile::_internal_keep_frames() const {
  return _impl_.keep_frames_;
}
inline int64_t Profile::keep_frames() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.keep_frames)
  return _internal_keep_frames();
}
inline void Profile::_internal_set_keep_frames(int64_t value) {
  
  _impl_.keep_frames_ = value;
}
inline void Profile::set_keep_frames(int64_t value) {
  _internal_set_keep_frames(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.keep_frames)
}

// int64 time_nanos = 9;
inline void Profile::clear_time_nanos() {
  _impl_.time_nanos_ = int64_t{0};
}
inline int64_t Profile::_internal_time_nanos() const {
  return _impl_.time_nanos_;
}
inline int64_t Profile::time_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.time_nanos)
  return _internal_time_nanos();
}
inline void Profile::_internal_set_time_nanos(int64_t value) {
  
  _impl_.time_nanos_ = value;
}
inline void Profile::set_time_nanos(int64_t value) {
  _internal_set_time_nanos(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.time_nanos)
}

// int64 duration_nanos = 10;
inline void Profile::clear_duration_nanos() {
  _impl_.duration_nanos_ = int64_t{0};
}
inline int64_t Profile::_internal_duration_nanos() const {
  return _impl_.duration_nanos_;
}
inline int64_t Profile::duration_nanos() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.duration_nanos)
  return _internal_duration_nanos();
}
inline void Profile::_internal_set_duration_nanos(int64_t value) {
  
  _impl_.duration_nanos_ = value;
}
inline void Profile::set_duration_nanos(int64_t value) {
  _internal_set_duration_nanos(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.duration_nanos)
}

// .tensorflow.tfprof.pprof.ValueType period_type = 11;
inline bool Profile::_internal_has_period_type() const {
  return this != internal_default_instance() && _impl_.period_type_ != nullptr;
}
inline bool Profile::has_period_type() const {
  return _internal_has_period_type();
}
inline void Profile::clear_period_type() {
  if (GetArenaForAllocation() == nullptr && _impl_.period_type_ != nullptr) {
    delete _impl_.period_type_;
  }
  _impl_.period_type_ = nullptr;
}
inline const ::tensorflow::tfprof::pprof::ValueType& Profile::_internal_period_type() const {
  const ::tensorflow::tfprof::pprof::ValueType* p = _impl_.period_type_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tfprof::pprof::ValueType&>(
      ::tensorflow::tfprof::pprof::_ValueType_default_instance_);
}
inline const ::tensorflow::tfprof::pprof::ValueType& Profile::period_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.period_type)
  return _internal_period_type();
}
inline void Profile::unsafe_arena_set_allocated_period_type(
    ::tensorflow::tfprof::pprof::ValueType* period_type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.period_type_);
  }
  _impl_.period_type_ = period_type;
  if (period_type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tfprof.pprof.Profile.period_type)
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::release_period_type() {
  
  ::tensorflow::tfprof::pprof::ValueType* temp = _impl_.period_type_;
  _impl_.period_type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::unsafe_arena_release_period_type() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.pprof.Profile.period_type)
  
  ::tensorflow::tfprof::pprof::ValueType* temp = _impl_.period_type_;
  _impl_.period_type_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::_internal_mutable_period_type() {
  
  if (_impl_.period_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::pprof::ValueType>(GetArenaForAllocation());
    _impl_.period_type_ = p;
  }
  return _impl_.period_type_;
}
inline ::tensorflow::tfprof::pprof::ValueType* Profile::mutable_period_type() {
  ::tensorflow::tfprof::pprof::ValueType* _msg = _internal_mutable_period_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Profile.period_type)
  return _msg;
}
inline void Profile::set_allocated_period_type(::tensorflow::tfprof::pprof::ValueType* period_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.period_type_;
  }
  if (period_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(period_type);
    if (message_arena != submessage_arena) {
      period_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, period_type, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.period_type_ = period_type;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.pprof.Profile.period_type)
}

// int64 period = 12;
inline void Profile::clear_period() {
  _impl_.period_ = int64_t{0};
}
inline int64_t Profile::_internal_period() const {
  return _impl_.period_;
}
inline int64_t Profile::period() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.period)
  return _internal_period();
}
inline void Profile::_internal_set_period(int64_t value) {
  
  _impl_.period_ = value;
}
inline void Profile::set_period(int64_t value) {
  _internal_set_period(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.period)
}

// repeated int64 comment = 13;
inline int Profile::_internal_comment_size() const {
  return _impl_.comment_.size();
}
inline int Profile::comment_size() const {
  return _internal_comment_size();
}
inline void Profile::clear_comment() {
  _impl_.comment_.Clear();
}
inline int64_t Profile::_internal_comment(int index) const {
  return _impl_.comment_.Get(index);
}
inline int64_t Profile::comment(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.comment)
  return _internal_comment(index);
}
inline void Profile::set_comment(int index, int64_t value) {
  _impl_.comment_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.comment)
}
inline void Profile::_internal_add_comment(int64_t value) {
  _impl_.comment_.Add(value);
}
inline void Profile::add_comment(int64_t value) {
  _internal_add_comment(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Profile.comment)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Profile::_internal_comment() const {
  return _impl_.comment_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Profile::comment() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Profile.comment)
  return _internal_comment();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Profile::_internal_mutable_comment() {
  return &_impl_.comment_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Profile::mutable_comment() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Profile.comment)
  return _internal_mutable_comment();
}

// int64 default_sample_type = 14;
inline void Profile::clear_default_sample_type() {
  _impl_.default_sample_type_ = int64_t{0};
}
inline int64_t Profile::_internal_default_sample_type() const {
  return _impl_.default_sample_type_;
}
inline int64_t Profile::default_sample_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Profile.default_sample_type)
  return _internal_default_sample_type();
}
inline void Profile::_internal_set_default_sample_type(int64_t value) {
  
  _impl_.default_sample_type_ = value;
}
inline void Profile::set_default_sample_type(int64_t value) {
  _internal_set_default_sample_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Profile.default_sample_type)
}

// -------------------------------------------------------------------

// ValueType

// int64 type = 1;
inline void ValueType::clear_type() {
  _impl_.type_ = int64_t{0};
}
inline int64_t ValueType::_internal_type() const {
  return _impl_.type_;
}
inline int64_t ValueType::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.ValueType.type)
  return _internal_type();
}
inline void ValueType::_internal_set_type(int64_t value) {
  
  _impl_.type_ = value;
}
inline void ValueType::set_type(int64_t value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.ValueType.type)
}

// int64 unit = 2;
inline void ValueType::clear_unit() {
  _impl_.unit_ = int64_t{0};
}
inline int64_t ValueType::_internal_unit() const {
  return _impl_.unit_;
}
inline int64_t ValueType::unit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.ValueType.unit)
  return _internal_unit();
}
inline void ValueType::_internal_set_unit(int64_t value) {
  
  _impl_.unit_ = value;
}
inline void ValueType::set_unit(int64_t value) {
  _internal_set_unit(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.ValueType.unit)
}

// -------------------------------------------------------------------

// Sample

// repeated uint64 location_id = 1;
inline int Sample::_internal_location_id_size() const {
  return _impl_.location_id_.size();
}
inline int Sample::location_id_size() const {
  return _internal_location_id_size();
}
inline void Sample::clear_location_id() {
  _impl_.location_id_.Clear();
}
inline uint64_t Sample::_internal_location_id(int index) const {
  return _impl_.location_id_.Get(index);
}
inline uint64_t Sample::location_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Sample.location_id)
  return _internal_location_id(index);
}
inline void Sample::set_location_id(int index, uint64_t value) {
  _impl_.location_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Sample.location_id)
}
inline void Sample::_internal_add_location_id(uint64_t value) {
  _impl_.location_id_.Add(value);
}
inline void Sample::add_location_id(uint64_t value) {
  _internal_add_location_id(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Sample.location_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
Sample::_internal_location_id() const {
  return _impl_.location_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
Sample::location_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Sample.location_id)
  return _internal_location_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
Sample::_internal_mutable_location_id() {
  return &_impl_.location_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
Sample::mutable_location_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Sample.location_id)
  return _internal_mutable_location_id();
}

// repeated int64 value = 2;
inline int Sample::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int Sample::value_size() const {
  return _internal_value_size();
}
inline void Sample::clear_value() {
  _impl_.value_.Clear();
}
inline int64_t Sample::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline int64_t Sample::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Sample.value)
  return _internal_value(index);
}
inline void Sample::set_value(int index, int64_t value) {
  _impl_.value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Sample.value)
}
inline void Sample::_internal_add_value(int64_t value) {
  _impl_.value_.Add(value);
}
inline void Sample::add_value(int64_t value) {
  _internal_add_value(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Sample.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Sample::_internal_value() const {
  return _impl_.value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Sample::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Sample.value)
  return _internal_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Sample::_internal_mutable_value() {
  return &_impl_.value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Sample::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Sample.value)
  return _internal_mutable_value();
}

// repeated .tensorflow.tfprof.pprof.Label label = 3;
inline int Sample::_internal_label_size() const {
  return _impl_.label_.size();
}
inline int Sample::label_size() const {
  return _internal_label_size();
}
inline void Sample::clear_label() {
  _impl_.label_.Clear();
}
inline ::tensorflow::tfprof::pprof::Label* Sample::mutable_label(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Sample.label)
  return _impl_.label_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label >*
Sample::mutable_label() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Sample.label)
  return &_impl_.label_;
}
inline const ::tensorflow::tfprof::pprof::Label& Sample::_internal_label(int index) const {
  return _impl_.label_.Get(index);
}
inline const ::tensorflow::tfprof::pprof::Label& Sample::label(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Sample.label)
  return _internal_label(index);
}
inline ::tensorflow::tfprof::pprof::Label* Sample::_internal_add_label() {
  return _impl_.label_.Add();
}
inline ::tensorflow::tfprof::pprof::Label* Sample::add_label() {
  ::tensorflow::tfprof::pprof::Label* _add = _internal_add_label();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Sample.label)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Label >&
Sample::label() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Sample.label)
  return _impl_.label_;
}

// -------------------------------------------------------------------

// Label

// int64 key = 1;
inline void Label::clear_key() {
  _impl_.key_ = int64_t{0};
}
inline int64_t Label::_internal_key() const {
  return _impl_.key_;
}
inline int64_t Label::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Label.key)
  return _internal_key();
}
inline void Label::_internal_set_key(int64_t value) {
  
  _impl_.key_ = value;
}
inline void Label::set_key(int64_t value) {
  _internal_set_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Label.key)
}

// int64 str = 2;
inline void Label::clear_str() {
  _impl_.str_ = int64_t{0};
}
inline int64_t Label::_internal_str() const {
  return _impl_.str_;
}
inline int64_t Label::str() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Label.str)
  return _internal_str();
}
inline void Label::_internal_set_str(int64_t value) {
  
  _impl_.str_ = value;
}
inline void Label::set_str(int64_t value) {
  _internal_set_str(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Label.str)
}

// int64 num = 3;
inline void Label::clear_num() {
  _impl_.num_ = int64_t{0};
}
inline int64_t Label::_internal_num() const {
  return _impl_.num_;
}
inline int64_t Label::num() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Label.num)
  return _internal_num();
}
inline void Label::_internal_set_num(int64_t value) {
  
  _impl_.num_ = value;
}
inline void Label::set_num(int64_t value) {
  _internal_set_num(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Label.num)
}

// -------------------------------------------------------------------

// Mapping

// uint64 id = 1;
inline void Mapping::clear_id() {
  _impl_.id_ = uint64_t{0u};
}
inline uint64_t Mapping::_internal_id() const {
  return _impl_.id_;
}
inline uint64_t Mapping::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.id)
  return _internal_id();
}
inline void Mapping::_internal_set_id(uint64_t value) {
  
  _impl_.id_ = value;
}
inline void Mapping::set_id(uint64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.id)
}

// uint64 memory_start = 2;
inline void Mapping::clear_memory_start() {
  _impl_.memory_start_ = uint64_t{0u};
}
inline uint64_t Mapping::_internal_memory_start() const {
  return _impl_.memory_start_;
}
inline uint64_t Mapping::memory_start() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.memory_start)
  return _internal_memory_start();
}
inline void Mapping::_internal_set_memory_start(uint64_t value) {
  
  _impl_.memory_start_ = value;
}
inline void Mapping::set_memory_start(uint64_t value) {
  _internal_set_memory_start(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.memory_start)
}

// uint64 memory_limit = 3;
inline void Mapping::clear_memory_limit() {
  _impl_.memory_limit_ = uint64_t{0u};
}
inline uint64_t Mapping::_internal_memory_limit() const {
  return _impl_.memory_limit_;
}
inline uint64_t Mapping::memory_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.memory_limit)
  return _internal_memory_limit();
}
inline void Mapping::_internal_set_memory_limit(uint64_t value) {
  
  _impl_.memory_limit_ = value;
}
inline void Mapping::set_memory_limit(uint64_t value) {
  _internal_set_memory_limit(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.memory_limit)
}

// uint64 file_offset = 4;
inline void Mapping::clear_file_offset() {
  _impl_.file_offset_ = uint64_t{0u};
}
inline uint64_t Mapping::_internal_file_offset() const {
  return _impl_.file_offset_;
}
inline uint64_t Mapping::file_offset() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.file_offset)
  return _internal_file_offset();
}
inline void Mapping::_internal_set_file_offset(uint64_t value) {
  
  _impl_.file_offset_ = value;
}
inline void Mapping::set_file_offset(uint64_t value) {
  _internal_set_file_offset(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.file_offset)
}

// int64 filename = 5;
inline void Mapping::clear_filename() {
  _impl_.filename_ = int64_t{0};
}
inline int64_t Mapping::_internal_filename() const {
  return _impl_.filename_;
}
inline int64_t Mapping::filename() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.filename)
  return _internal_filename();
}
inline void Mapping::_internal_set_filename(int64_t value) {
  
  _impl_.filename_ = value;
}
inline void Mapping::set_filename(int64_t value) {
  _internal_set_filename(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.filename)
}

// int64 build_id = 6;
inline void Mapping::clear_build_id() {
  _impl_.build_id_ = int64_t{0};
}
inline int64_t Mapping::_internal_build_id() const {
  return _impl_.build_id_;
}
inline int64_t Mapping::build_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.build_id)
  return _internal_build_id();
}
inline void Mapping::_internal_set_build_id(int64_t value) {
  
  _impl_.build_id_ = value;
}
inline void Mapping::set_build_id(int64_t value) {
  _internal_set_build_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.build_id)
}

// bool has_functions = 7;
inline void Mapping::clear_has_functions() {
  _impl_.has_functions_ = false;
}
inline bool Mapping::_internal_has_functions() const {
  return _impl_.has_functions_;
}
inline bool Mapping::has_functions() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.has_functions)
  return _internal_has_functions();
}
inline void Mapping::_internal_set_has_functions(bool value) {
  
  _impl_.has_functions_ = value;
}
inline void Mapping::set_has_functions(bool value) {
  _internal_set_has_functions(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.has_functions)
}

// bool has_filenames = 8;
inline void Mapping::clear_has_filenames() {
  _impl_.has_filenames_ = false;
}
inline bool Mapping::_internal_has_filenames() const {
  return _impl_.has_filenames_;
}
inline bool Mapping::has_filenames() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.has_filenames)
  return _internal_has_filenames();
}
inline void Mapping::_internal_set_has_filenames(bool value) {
  
  _impl_.has_filenames_ = value;
}
inline void Mapping::set_has_filenames(bool value) {
  _internal_set_has_filenames(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.has_filenames)
}

// bool has_line_numbers = 9;
inline void Mapping::clear_has_line_numbers() {
  _impl_.has_line_numbers_ = false;
}
inline bool Mapping::_internal_has_line_numbers() const {
  return _impl_.has_line_numbers_;
}
inline bool Mapping::has_line_numbers() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.has_line_numbers)
  return _internal_has_line_numbers();
}
inline void Mapping::_internal_set_has_line_numbers(bool value) {
  
  _impl_.has_line_numbers_ = value;
}
inline void Mapping::set_has_line_numbers(bool value) {
  _internal_set_has_line_numbers(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.has_line_numbers)
}

// bool has_inline_frames = 10;
inline void Mapping::clear_has_inline_frames() {
  _impl_.has_inline_frames_ = false;
}
inline bool Mapping::_internal_has_inline_frames() const {
  return _impl_.has_inline_frames_;
}
inline bool Mapping::has_inline_frames() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Mapping.has_inline_frames)
  return _internal_has_inline_frames();
}
inline void Mapping::_internal_set_has_inline_frames(bool value) {
  
  _impl_.has_inline_frames_ = value;
}
inline void Mapping::set_has_inline_frames(bool value) {
  _internal_set_has_inline_frames(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Mapping.has_inline_frames)
}

// -------------------------------------------------------------------

// Location

// uint64 id = 1;
inline void Location::clear_id() {
  _impl_.id_ = uint64_t{0u};
}
inline uint64_t Location::_internal_id() const {
  return _impl_.id_;
}
inline uint64_t Location::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Location.id)
  return _internal_id();
}
inline void Location::_internal_set_id(uint64_t value) {
  
  _impl_.id_ = value;
}
inline void Location::set_id(uint64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Location.id)
}

// uint64 mapping_id = 2;
inline void Location::clear_mapping_id() {
  _impl_.mapping_id_ = uint64_t{0u};
}
inline uint64_t Location::_internal_mapping_id() const {
  return _impl_.mapping_id_;
}
inline uint64_t Location::mapping_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Location.mapping_id)
  return _internal_mapping_id();
}
inline void Location::_internal_set_mapping_id(uint64_t value) {
  
  _impl_.mapping_id_ = value;
}
inline void Location::set_mapping_id(uint64_t value) {
  _internal_set_mapping_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Location.mapping_id)
}

// uint64 address = 3;
inline void Location::clear_address() {
  _impl_.address_ = uint64_t{0u};
}
inline uint64_t Location::_internal_address() const {
  return _impl_.address_;
}
inline uint64_t Location::address() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Location.address)
  return _internal_address();
}
inline void Location::_internal_set_address(uint64_t value) {
  
  _impl_.address_ = value;
}
inline void Location::set_address(uint64_t value) {
  _internal_set_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Location.address)
}

// repeated .tensorflow.tfprof.pprof.Line line = 4;
inline int Location::_internal_line_size() const {
  return _impl_.line_.size();
}
inline int Location::line_size() const {
  return _internal_line_size();
}
inline void Location::clear_line() {
  _impl_.line_.Clear();
}
inline ::tensorflow::tfprof::pprof::Line* Location::mutable_line(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.pprof.Location.line)
  return _impl_.line_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line >*
Location::mutable_line() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.pprof.Location.line)
  return &_impl_.line_;
}
inline const ::tensorflow::tfprof::pprof::Line& Location::_internal_line(int index) const {
  return _impl_.line_.Get(index);
}
inline const ::tensorflow::tfprof::pprof::Line& Location::line(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Location.line)
  return _internal_line(index);
}
inline ::tensorflow::tfprof::pprof::Line* Location::_internal_add_line() {
  return _impl_.line_.Add();
}
inline ::tensorflow::tfprof::pprof::Line* Location::add_line() {
  ::tensorflow::tfprof::pprof::Line* _add = _internal_add_line();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.pprof.Location.line)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::pprof::Line >&
Location::line() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.pprof.Location.line)
  return _impl_.line_;
}

// -------------------------------------------------------------------

// Line

// uint64 function_id = 1;
inline void Line::clear_function_id() {
  _impl_.function_id_ = uint64_t{0u};
}
inline uint64_t Line::_internal_function_id() const {
  return _impl_.function_id_;
}
inline uint64_t Line::function_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Line.function_id)
  return _internal_function_id();
}
inline void Line::_internal_set_function_id(uint64_t value) {
  
  _impl_.function_id_ = value;
}
inline void Line::set_function_id(uint64_t value) {
  _internal_set_function_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Line.function_id)
}

// int64 line = 2;
inline void Line::clear_line() {
  _impl_.line_ = int64_t{0};
}
inline int64_t Line::_internal_line() const {
  return _impl_.line_;
}
inline int64_t Line::line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Line.line)
  return _internal_line();
}
inline void Line::_internal_set_line(int64_t value) {
  
  _impl_.line_ = value;
}
inline void Line::set_line(int64_t value) {
  _internal_set_line(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Line.line)
}

// -------------------------------------------------------------------

// Function

// uint64 id = 1;
inline void Function::clear_id() {
  _impl_.id_ = uint64_t{0u};
}
inline uint64_t Function::_internal_id() const {
  return _impl_.id_;
}
inline uint64_t Function::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.id)
  return _internal_id();
}
inline void Function::_internal_set_id(uint64_t value) {
  
  _impl_.id_ = value;
}
inline void Function::set_id(uint64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.id)
}

// int64 name = 2;
inline void Function::clear_name() {
  _impl_.name_ = int64_t{0};
}
inline int64_t Function::_internal_name() const {
  return _impl_.name_;
}
inline int64_t Function::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.name)
  return _internal_name();
}
inline void Function::_internal_set_name(int64_t value) {
  
  _impl_.name_ = value;
}
inline void Function::set_name(int64_t value) {
  _internal_set_name(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.name)
}

// int64 system_name = 3;
inline void Function::clear_system_name() {
  _impl_.system_name_ = int64_t{0};
}
inline int64_t Function::_internal_system_name() const {
  return _impl_.system_name_;
}
inline int64_t Function::system_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.system_name)
  return _internal_system_name();
}
inline void Function::_internal_set_system_name(int64_t value) {
  
  _impl_.system_name_ = value;
}
inline void Function::set_system_name(int64_t value) {
  _internal_set_system_name(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.system_name)
}

// int64 filename = 4;
inline void Function::clear_filename() {
  _impl_.filename_ = int64_t{0};
}
inline int64_t Function::_internal_filename() const {
  return _impl_.filename_;
}
inline int64_t Function::filename() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.filename)
  return _internal_filename();
}
inline void Function::_internal_set_filename(int64_t value) {
  
  _impl_.filename_ = value;
}
inline void Function::set_filename(int64_t value) {
  _internal_set_filename(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.filename)
}

// int64 start_line = 5;
inline void Function::clear_start_line() {
  _impl_.start_line_ = int64_t{0};
}
inline int64_t Function::_internal_start_line() const {
  return _impl_.start_line_;
}
inline int64_t Function::start_line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.pprof.Function.start_line)
  return _internal_start_line();
}
inline void Function::_internal_set_start_line(int64_t value) {
  
  _impl_.start_line_ = value;
}
inline void Function::set_start_line(int64_t value) {
  _internal_set_start_line(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.pprof.Function.start_line)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace pprof
}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofile_2eproto
