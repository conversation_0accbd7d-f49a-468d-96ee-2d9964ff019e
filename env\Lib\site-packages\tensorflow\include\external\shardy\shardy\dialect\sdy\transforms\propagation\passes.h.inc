/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_AGGRESSIVEPROPAGATIONPASS
#define GEN_PASS_DECL_BASICPROPAGATIONPASS
#define GEN_PASS_DECL_OPPRIORITYPROPAGATIONPASS
#define GEN_PASS_DECL_POPULATEOPSHARDINGRULESPASS
#define GEN_PASS_DECL_USERPRIORITYPROPAGATIONPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// AggressivePropagationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_AGGRESSIVEPROPAGATIONPASS
std::unique_ptr<::mlir::Pass> createAggressivePropagationPass();
#undef GEN_PASS_DECL_AGGRESSIVEPROPAGATIONPASS
#endif // GEN_PASS_DECL_AGGRESSIVEPROPAGATIONPASS
#ifdef GEN_PASS_DEF_AGGRESSIVEPROPAGATIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createAggressivePropagationPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class AggressivePropagationPassBase : public AggressivePropagationPassImpl {
public:
  using Base = AggressivePropagationPassBase;

  AggressivePropagationPassBase() : AggressivePropagationPassImpl(::mlir::TypeID::get<DerivedT>()) {}
  AggressivePropagationPassBase(const AggressivePropagationPassBase &other) : AggressivePropagationPassImpl(other) {}
  AggressivePropagationPassBase& operator=(const AggressivePropagationPassBase &) = delete;
  AggressivePropagationPassBase(AggressivePropagationPassBase &&) = delete;
  AggressivePropagationPassBase& operator=(AggressivePropagationPassBase &&) = delete;
  ~AggressivePropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-aggressive-propagate");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-aggressive-propagate"; }

  ::llvm::StringRef getDescription() const override { return "Runs the aggressive sharding propagation algorithm."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AggressivePropagationPass");
  }
  ::llvm::StringRef getName() const override { return "AggressivePropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AggressivePropagationPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createAggressivePropagationPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createAggressivePropagationPass() {
  return impl::createAggressivePropagationPass();
}
#undef GEN_PASS_DEF_AGGRESSIVEPROPAGATIONPASS
#endif // GEN_PASS_DEF_AGGRESSIVEPROPAGATIONPASS

//===----------------------------------------------------------------------===//
// BasicPropagationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_BASICPROPAGATIONPASS
std::unique_ptr<::mlir::Pass> createBasicPropagationPass();
#undef GEN_PASS_DECL_BASICPROPAGATIONPASS
#endif // GEN_PASS_DECL_BASICPROPAGATIONPASS
#ifdef GEN_PASS_DEF_BASICPROPAGATIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createBasicPropagationPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class BasicPropagationPassBase : public BasicPropagationPassImpl {
public:
  using Base = BasicPropagationPassBase;

  BasicPropagationPassBase() : BasicPropagationPassImpl(::mlir::TypeID::get<DerivedT>()) {}
  BasicPropagationPassBase(const BasicPropagationPassBase &other) : BasicPropagationPassImpl(other) {}
  BasicPropagationPassBase& operator=(const BasicPropagationPassBase &) = delete;
  BasicPropagationPassBase(BasicPropagationPassBase &&) = delete;
  BasicPropagationPassBase& operator=(BasicPropagationPassBase &&) = delete;
  ~BasicPropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-basic-propagate");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-basic-propagate"; }

  ::llvm::StringRef getDescription() const override { return "Runs the basic sharding propagation algorithm."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BasicPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "BasicPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BasicPropagationPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createBasicPropagationPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createBasicPropagationPass() {
  return impl::createBasicPropagationPass();
}
#undef GEN_PASS_DEF_BASICPROPAGATIONPASS
#endif // GEN_PASS_DEF_BASICPROPAGATIONPASS

//===----------------------------------------------------------------------===//
// OpPriorityPropagationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_OPPRIORITYPROPAGATIONPASS
std::unique_ptr<::mlir::Pass> createOpPriorityPropagationPass();
#undef GEN_PASS_DECL_OPPRIORITYPROPAGATIONPASS
#endif // GEN_PASS_DECL_OPPRIORITYPROPAGATIONPASS
#ifdef GEN_PASS_DEF_OPPRIORITYPROPAGATIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createOpPriorityPropagationPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class OpPriorityPropagationPassBase : public OpPriorityPropagationPassImpl {
public:
  using Base = OpPriorityPropagationPassBase;

  OpPriorityPropagationPassBase() : OpPriorityPropagationPassImpl(::mlir::TypeID::get<DerivedT>()) {}
  OpPriorityPropagationPassBase(const OpPriorityPropagationPassBase &other) : OpPriorityPropagationPassImpl(other) {}
  OpPriorityPropagationPassBase& operator=(const OpPriorityPropagationPassBase &) = delete;
  OpPriorityPropagationPassBase(OpPriorityPropagationPassBase &&) = delete;
  OpPriorityPropagationPassBase& operator=(OpPriorityPropagationPassBase &&) = delete;
  ~OpPriorityPropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-op-priority-propagate");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-op-priority-propagate"; }

  ::llvm::StringRef getDescription() const override { return "Runs the op-priority propagation algorithm."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OpPriorityPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "OpPriorityPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OpPriorityPropagationPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createOpPriorityPropagationPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createOpPriorityPropagationPass() {
  return impl::createOpPriorityPropagationPass();
}
#undef GEN_PASS_DEF_OPPRIORITYPROPAGATIONPASS
#endif // GEN_PASS_DEF_OPPRIORITYPROPAGATIONPASS

//===----------------------------------------------------------------------===//
// PopulateOpShardingRulesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_POPULATEOPSHARDINGRULESPASS
struct PopulateOpShardingRulesPassOptions {
  bool conservativePropagation = false;
};
std::unique_ptr<::mlir::Pass> createPopulateOpShardingRulesPass();
std::unique_ptr<::mlir::Pass> createPopulateOpShardingRulesPass(PopulateOpShardingRulesPassOptions options);
#undef GEN_PASS_DECL_POPULATEOPSHARDINGRULESPASS
#endif // GEN_PASS_DECL_POPULATEOPSHARDINGRULESPASS
#ifdef GEN_PASS_DEF_POPULATEOPSHARDINGRULESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createPopulateOpShardingRulesPass();
} // namespace impl

namespace impl {
  std::unique_ptr<::mlir::Pass> createPopulateOpShardingRulesPass(PopulateOpShardingRulesPassOptions options);
} // namespace impl
namespace impl {

template <typename DerivedT>
class PopulateOpShardingRulesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = PopulateOpShardingRulesPassBase;

  PopulateOpShardingRulesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  PopulateOpShardingRulesPassBase(const PopulateOpShardingRulesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  PopulateOpShardingRulesPassBase& operator=(const PopulateOpShardingRulesPassBase &) = delete;
  PopulateOpShardingRulesPassBase(PopulateOpShardingRulesPassBase &&) = delete;
  PopulateOpShardingRulesPassBase& operator=(PopulateOpShardingRulesPassBase &&) = delete;
  ~PopulateOpShardingRulesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-populate-op-sharding-rules");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-populate-op-sharding-rules"; }

  ::llvm::StringRef getDescription() const override { return "Populates all registered ops with an `OpShardingRuleAttr`."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PopulateOpShardingRulesPass");
  }
  ::llvm::StringRef getName() const override { return "PopulateOpShardingRulesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PopulateOpShardingRulesPassBase<DerivedT>)

  PopulateOpShardingRulesPassBase(PopulateOpShardingRulesPassOptions options) : PopulateOpShardingRulesPassBase() {
    conservativePropagation = std::move(options.conservativePropagation);
  }
protected:
  ::mlir::Pass::Option<bool> conservativePropagation{*this, "conservative-propagation", ::llvm::cl::desc("whether to disllow rules that can propagate non-divisible sharding axes"), ::llvm::cl::init(false)};
private:

  friend std::unique_ptr<::mlir::Pass> createPopulateOpShardingRulesPass() {
    return std::make_unique<DerivedT>();
  }

  friend std::unique_ptr<::mlir::Pass> createPopulateOpShardingRulesPass(PopulateOpShardingRulesPassOptions options) {
    return std::make_unique<DerivedT>(std::move(options));
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createPopulateOpShardingRulesPass() {
  return impl::createPopulateOpShardingRulesPass();
}

std::unique_ptr<::mlir::Pass> createPopulateOpShardingRulesPass(PopulateOpShardingRulesPassOptions options) {
  return impl::createPopulateOpShardingRulesPass(std::move(options));
}
#undef GEN_PASS_DEF_POPULATEOPSHARDINGRULESPASS
#endif // GEN_PASS_DEF_POPULATEOPSHARDINGRULESPASS

//===----------------------------------------------------------------------===//
// UserPriorityPropagationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_USERPRIORITYPROPAGATIONPASS
std::unique_ptr<::mlir::Pass> createUserPriorityPropagationPass();
#undef GEN_PASS_DECL_USERPRIORITYPROPAGATIONPASS
#endif // GEN_PASS_DECL_USERPRIORITYPROPAGATIONPASS
#ifdef GEN_PASS_DEF_USERPRIORITYPROPAGATIONPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createUserPriorityPropagationPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class UserPriorityPropagationPassBase : public UserPriorityPropagationPassImpl {
public:
  using Base = UserPriorityPropagationPassBase;

  UserPriorityPropagationPassBase() : UserPriorityPropagationPassImpl(::mlir::TypeID::get<DerivedT>()) {}
  UserPriorityPropagationPassBase(const UserPriorityPropagationPassBase &other) : UserPriorityPropagationPassImpl(other) {}
  UserPriorityPropagationPassBase& operator=(const UserPriorityPropagationPassBase &) = delete;
  UserPriorityPropagationPassBase(UserPriorityPropagationPassBase &&) = delete;
  UserPriorityPropagationPassBase& operator=(UserPriorityPropagationPassBase &&) = delete;
  ~UserPriorityPropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-user-priority-propagate");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-user-priority-propagate"; }

  ::llvm::StringRef getDescription() const override { return "Runs the user-priority propagation algorithm."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UserPriorityPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "UserPriorityPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UserPriorityPropagationPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createUserPriorityPropagationPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createUserPriorityPropagationPass() {
  return impl::createUserPriorityPropagationPass();
}
#undef GEN_PASS_DEF_USERPRIORITYPROPAGATIONPASS
#endif // GEN_PASS_DEF_USERPRIORITYPROPAGATIONPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AggressivePropagationPass Registration
//===----------------------------------------------------------------------===//

inline void registerAggressivePropagationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createAggressivePropagationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAggressivePropagationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createAggressivePropagationPass();
  });
}

//===----------------------------------------------------------------------===//
// BasicPropagationPass Registration
//===----------------------------------------------------------------------===//

inline void registerBasicPropagationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createBasicPropagationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerBasicPropagationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createBasicPropagationPass();
  });
}

//===----------------------------------------------------------------------===//
// OpPriorityPropagationPass Registration
//===----------------------------------------------------------------------===//

inline void registerOpPriorityPropagationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createOpPriorityPropagationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerOpPriorityPropagationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createOpPriorityPropagationPass();
  });
}

//===----------------------------------------------------------------------===//
// PopulateOpShardingRulesPass Registration
//===----------------------------------------------------------------------===//

inline void registerPopulateOpShardingRulesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createPopulateOpShardingRulesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerPopulateOpShardingRulesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createPopulateOpShardingRulesPass();
  });
}

//===----------------------------------------------------------------------===//
// UserPriorityPropagationPass Registration
//===----------------------------------------------------------------------===//

inline void registerUserPriorityPropagationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createUserPriorityPropagationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerUserPriorityPropagationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createUserPriorityPropagationPass();
  });
}

//===----------------------------------------------------------------------===//
// SdyPropagation Registration
//===----------------------------------------------------------------------===//

inline void registerSdyPropagationPasses() {
  registerAggressivePropagationPass();
  registerBasicPropagationPass();
  registerOpPriorityPropagationPass();
  registerPopulateOpShardingRulesPass();
  registerUserPriorityPropagationPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class AggressivePropagationPassBase : public AggressivePropagationPassImpl {
public:
  using Base = AggressivePropagationPassBase;

  AggressivePropagationPassBase() : AggressivePropagationPassImpl(::mlir::TypeID::get<DerivedT>()) {}
  AggressivePropagationPassBase(const AggressivePropagationPassBase &other) : AggressivePropagationPassImpl(other) {}
  AggressivePropagationPassBase& operator=(const AggressivePropagationPassBase &) = delete;
  AggressivePropagationPassBase(AggressivePropagationPassBase &&) = delete;
  AggressivePropagationPassBase& operator=(AggressivePropagationPassBase &&) = delete;
  ~AggressivePropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-aggressive-propagate");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-aggressive-propagate"; }

  ::llvm::StringRef getDescription() const override { return "Runs the aggressive sharding propagation algorithm."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AggressivePropagationPass");
  }
  ::llvm::StringRef getName() const override { return "AggressivePropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AggressivePropagationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class BasicPropagationPassBase : public BasicPropagationPassImpl {
public:
  using Base = BasicPropagationPassBase;

  BasicPropagationPassBase() : BasicPropagationPassImpl(::mlir::TypeID::get<DerivedT>()) {}
  BasicPropagationPassBase(const BasicPropagationPassBase &other) : BasicPropagationPassImpl(other) {}
  BasicPropagationPassBase& operator=(const BasicPropagationPassBase &) = delete;
  BasicPropagationPassBase(BasicPropagationPassBase &&) = delete;
  BasicPropagationPassBase& operator=(BasicPropagationPassBase &&) = delete;
  ~BasicPropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-basic-propagate");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-basic-propagate"; }

  ::llvm::StringRef getDescription() const override { return "Runs the basic sharding propagation algorithm."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("BasicPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "BasicPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(BasicPropagationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class OpPriorityPropagationPassBase : public OpPriorityPropagationPassImpl {
public:
  using Base = OpPriorityPropagationPassBase;

  OpPriorityPropagationPassBase() : OpPriorityPropagationPassImpl(::mlir::TypeID::get<DerivedT>()) {}
  OpPriorityPropagationPassBase(const OpPriorityPropagationPassBase &other) : OpPriorityPropagationPassImpl(other) {}
  OpPriorityPropagationPassBase& operator=(const OpPriorityPropagationPassBase &) = delete;
  OpPriorityPropagationPassBase(OpPriorityPropagationPassBase &&) = delete;
  OpPriorityPropagationPassBase& operator=(OpPriorityPropagationPassBase &&) = delete;
  ~OpPriorityPropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-op-priority-propagate");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-op-priority-propagate"; }

  ::llvm::StringRef getDescription() const override { return "Runs the op-priority propagation algorithm."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("OpPriorityPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "OpPriorityPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(OpPriorityPropagationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class PopulateOpShardingRulesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = PopulateOpShardingRulesPassBase;

  PopulateOpShardingRulesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  PopulateOpShardingRulesPassBase(const PopulateOpShardingRulesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  PopulateOpShardingRulesPassBase& operator=(const PopulateOpShardingRulesPassBase &) = delete;
  PopulateOpShardingRulesPassBase(PopulateOpShardingRulesPassBase &&) = delete;
  PopulateOpShardingRulesPassBase& operator=(PopulateOpShardingRulesPassBase &&) = delete;
  ~PopulateOpShardingRulesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-populate-op-sharding-rules");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-populate-op-sharding-rules"; }

  ::llvm::StringRef getDescription() const override { return "Populates all registered ops with an `OpShardingRuleAttr`."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("PopulateOpShardingRulesPass");
  }
  ::llvm::StringRef getName() const override { return "PopulateOpShardingRulesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(PopulateOpShardingRulesPassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> conservativePropagation{*this, "conservative-propagation", ::llvm::cl::desc("whether to disllow rules that can propagate non-divisible sharding axes"), ::llvm::cl::init(false)};
};

template <typename DerivedT>
class UserPriorityPropagationPassBase : public UserPriorityPropagationPassImpl {
public:
  using Base = UserPriorityPropagationPassBase;

  UserPriorityPropagationPassBase() : UserPriorityPropagationPassImpl(::mlir::TypeID::get<DerivedT>()) {}
  UserPriorityPropagationPassBase(const UserPriorityPropagationPassBase &other) : UserPriorityPropagationPassImpl(other) {}
  UserPriorityPropagationPassBase& operator=(const UserPriorityPropagationPassBase &) = delete;
  UserPriorityPropagationPassBase(UserPriorityPropagationPassBase &&) = delete;
  UserPriorityPropagationPassBase& operator=(UserPriorityPropagationPassBase &&) = delete;
  ~UserPriorityPropagationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-user-priority-propagate");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-user-priority-propagate"; }

  ::llvm::StringRef getDescription() const override { return "Runs the user-priority propagation algorithm."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UserPriorityPropagationPass");
  }
  ::llvm::StringRef getName() const override { return "UserPriorityPropagationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UserPriorityPropagationPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
