// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/pjrt/gpu/gpu_topology.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fgpu_2fgpu_5ftopology_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fgpu_2fgpu_5ftopology_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fpjrt_2fgpu_2fgpu_5ftopology_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fpjrt_2fgpu_2fgpu_5ftopology_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fpjrt_2fgpu_2fgpu_5ftopology_2eproto;
namespace xla {
class GpuTopologyProto;
struct GpuTopologyProtoDefaultTypeInternal;
extern GpuTopologyProtoDefaultTypeInternal _GpuTopologyProto_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::GpuTopologyProto* Arena::CreateMaybeMessage<::xla::GpuTopologyProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

// ===================================================================

class GpuTopologyProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.GpuTopologyProto) */ {
 public:
  inline GpuTopologyProto() : GpuTopologyProto(nullptr) {}
  ~GpuTopologyProto() override;
  explicit PROTOBUF_CONSTEXPR GpuTopologyProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GpuTopologyProto(const GpuTopologyProto& from);
  GpuTopologyProto(GpuTopologyProto&& from) noexcept
    : GpuTopologyProto() {
    *this = ::std::move(from);
  }

  inline GpuTopologyProto& operator=(const GpuTopologyProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline GpuTopologyProto& operator=(GpuTopologyProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GpuTopologyProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const GpuTopologyProto* internal_default_instance() {
    return reinterpret_cast<const GpuTopologyProto*>(
               &_GpuTopologyProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GpuTopologyProto& a, GpuTopologyProto& b) {
    a.Swap(&b);
  }
  inline void Swap(GpuTopologyProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GpuTopologyProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GpuTopologyProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GpuTopologyProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GpuTopologyProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GpuTopologyProto& from) {
    GpuTopologyProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GpuTopologyProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.GpuTopologyProto";
  }
  protected:
  explicit GpuTopologyProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceIdsFieldNumber = 1,
    kPlatformVersionFieldNumber = 3,
    kNumSlicesFieldNumber = 4,
    kNumHostsPerSliceFieldNumber = 5,
    kNumDevicesPerHostFieldNumber = 6,
  };
  // repeated int32 device_ids = 1;
  int device_ids_size() const;
  private:
  int _internal_device_ids_size() const;
  public:
  void clear_device_ids();
  private:
  int32_t _internal_device_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_device_ids() const;
  void _internal_add_device_ids(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_device_ids();
  public:
  int32_t device_ids(int index) const;
  void set_device_ids(int index, int32_t value);
  void add_device_ids(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      device_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_device_ids();

  // string platform_version = 3;
  void clear_platform_version();
  const std::string& platform_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_platform_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_platform_version();
  PROTOBUF_NODISCARD std::string* release_platform_version();
  void set_allocated_platform_version(std::string* platform_version);
  private:
  const std::string& _internal_platform_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_platform_version(const std::string& value);
  std::string* _internal_mutable_platform_version();
  public:

  // int32 num_slices = 4;
  void clear_num_slices();
  int32_t num_slices() const;
  void set_num_slices(int32_t value);
  private:
  int32_t _internal_num_slices() const;
  void _internal_set_num_slices(int32_t value);
  public:

  // int32 num_hosts_per_slice = 5;
  void clear_num_hosts_per_slice();
  int32_t num_hosts_per_slice() const;
  void set_num_hosts_per_slice(int32_t value);
  private:
  int32_t _internal_num_hosts_per_slice() const;
  void _internal_set_num_hosts_per_slice(int32_t value);
  public:

  // int32 num_devices_per_host = 6;
  void clear_num_devices_per_host();
  int32_t num_devices_per_host() const;
  void set_num_devices_per_host(int32_t value);
  private:
  int32_t _internal_num_devices_per_host() const;
  void _internal_set_num_devices_per_host(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.GpuTopologyProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > device_ids_;
    mutable std::atomic<int> _device_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr platform_version_;
    int32_t num_slices_;
    int32_t num_hosts_per_slice_;
    int32_t num_devices_per_host_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fgpu_2fgpu_5ftopology_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GpuTopologyProto

// repeated int32 device_ids = 1;
inline int GpuTopologyProto::_internal_device_ids_size() const {
  return _impl_.device_ids_.size();
}
inline int GpuTopologyProto::device_ids_size() const {
  return _internal_device_ids_size();
}
inline void GpuTopologyProto::clear_device_ids() {
  _impl_.device_ids_.Clear();
}
inline int32_t GpuTopologyProto::_internal_device_ids(int index) const {
  return _impl_.device_ids_.Get(index);
}
inline int32_t GpuTopologyProto::device_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.GpuTopologyProto.device_ids)
  return _internal_device_ids(index);
}
inline void GpuTopologyProto::set_device_ids(int index, int32_t value) {
  _impl_.device_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.GpuTopologyProto.device_ids)
}
inline void GpuTopologyProto::_internal_add_device_ids(int32_t value) {
  _impl_.device_ids_.Add(value);
}
inline void GpuTopologyProto::add_device_ids(int32_t value) {
  _internal_add_device_ids(value);
  // @@protoc_insertion_point(field_add:xla.GpuTopologyProto.device_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GpuTopologyProto::_internal_device_ids() const {
  return _impl_.device_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GpuTopologyProto::device_ids() const {
  // @@protoc_insertion_point(field_list:xla.GpuTopologyProto.device_ids)
  return _internal_device_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GpuTopologyProto::_internal_mutable_device_ids() {
  return &_impl_.device_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GpuTopologyProto::mutable_device_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.GpuTopologyProto.device_ids)
  return _internal_mutable_device_ids();
}

// string platform_version = 3;
inline void GpuTopologyProto::clear_platform_version() {
  _impl_.platform_version_.ClearToEmpty();
}
inline const std::string& GpuTopologyProto::platform_version() const {
  // @@protoc_insertion_point(field_get:xla.GpuTopologyProto.platform_version)
  return _internal_platform_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GpuTopologyProto::set_platform_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.platform_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.GpuTopologyProto.platform_version)
}
inline std::string* GpuTopologyProto::mutable_platform_version() {
  std::string* _s = _internal_mutable_platform_version();
  // @@protoc_insertion_point(field_mutable:xla.GpuTopologyProto.platform_version)
  return _s;
}
inline const std::string& GpuTopologyProto::_internal_platform_version() const {
  return _impl_.platform_version_.Get();
}
inline void GpuTopologyProto::_internal_set_platform_version(const std::string& value) {
  
  _impl_.platform_version_.Set(value, GetArenaForAllocation());
}
inline std::string* GpuTopologyProto::_internal_mutable_platform_version() {
  
  return _impl_.platform_version_.Mutable(GetArenaForAllocation());
}
inline std::string* GpuTopologyProto::release_platform_version() {
  // @@protoc_insertion_point(field_release:xla.GpuTopologyProto.platform_version)
  return _impl_.platform_version_.Release();
}
inline void GpuTopologyProto::set_allocated_platform_version(std::string* platform_version) {
  if (platform_version != nullptr) {
    
  } else {
    
  }
  _impl_.platform_version_.SetAllocated(platform_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.platform_version_.IsDefault()) {
    _impl_.platform_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.GpuTopologyProto.platform_version)
}

// int32 num_slices = 4;
inline void GpuTopologyProto::clear_num_slices() {
  _impl_.num_slices_ = 0;
}
inline int32_t GpuTopologyProto::_internal_num_slices() const {
  return _impl_.num_slices_;
}
inline int32_t GpuTopologyProto::num_slices() const {
  // @@protoc_insertion_point(field_get:xla.GpuTopologyProto.num_slices)
  return _internal_num_slices();
}
inline void GpuTopologyProto::_internal_set_num_slices(int32_t value) {
  
  _impl_.num_slices_ = value;
}
inline void GpuTopologyProto::set_num_slices(int32_t value) {
  _internal_set_num_slices(value);
  // @@protoc_insertion_point(field_set:xla.GpuTopologyProto.num_slices)
}

// int32 num_hosts_per_slice = 5;
inline void GpuTopologyProto::clear_num_hosts_per_slice() {
  _impl_.num_hosts_per_slice_ = 0;
}
inline int32_t GpuTopologyProto::_internal_num_hosts_per_slice() const {
  return _impl_.num_hosts_per_slice_;
}
inline int32_t GpuTopologyProto::num_hosts_per_slice() const {
  // @@protoc_insertion_point(field_get:xla.GpuTopologyProto.num_hosts_per_slice)
  return _internal_num_hosts_per_slice();
}
inline void GpuTopologyProto::_internal_set_num_hosts_per_slice(int32_t value) {
  
  _impl_.num_hosts_per_slice_ = value;
}
inline void GpuTopologyProto::set_num_hosts_per_slice(int32_t value) {
  _internal_set_num_hosts_per_slice(value);
  // @@protoc_insertion_point(field_set:xla.GpuTopologyProto.num_hosts_per_slice)
}

// int32 num_devices_per_host = 6;
inline void GpuTopologyProto::clear_num_devices_per_host() {
  _impl_.num_devices_per_host_ = 0;
}
inline int32_t GpuTopologyProto::_internal_num_devices_per_host() const {
  return _impl_.num_devices_per_host_;
}
inline int32_t GpuTopologyProto::num_devices_per_host() const {
  // @@protoc_insertion_point(field_get:xla.GpuTopologyProto.num_devices_per_host)
  return _internal_num_devices_per_host();
}
inline void GpuTopologyProto::_internal_set_num_devices_per_host(int32_t value) {
  
  _impl_.num_devices_per_host_ = value;
}
inline void GpuTopologyProto::set_num_devices_per_host(int32_t value) {
  _internal_set_num_devices_per_host(value);
  // @@protoc_insertion_point(field_set:xla.GpuTopologyProto.num_devices_per_host)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fgpu_2fgpu_5ftopology_2eproto
