.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ext_import_name_constraints" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ext_import_name_constraints \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ext_import_name_constraints(const gnutls_datum_t * " ext ", gnutls_x509_name_constraints_t " nc ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * ext" 12
a DER encoded extension
.IP "gnutls_x509_name_constraints_t nc" 12
The nameconstraints
.IP "unsigned int flags" 12
zero or \fBGNUTLS_NAME_CONSTRAINTS_FLAG_APPEND\fP
.SH "DESCRIPTION"
This function will return an intermediate type containing
the name constraints of the provided NameConstraints extension. That
can be used in combination with \fBgnutls_x509_name_constraints_check()\fP
to verify whether a server's name is in accordance with the constraints.

When the  \fIflags\fP is set to \fBGNUTLS_NAME_CONSTRAINTS_FLAG_APPEND\fP, then if 
the  \fInc\fP type is empty this function will behave identically as if the flag was not set.
Otherwise if there are elements in the  \fInc\fP structure then the
constraints will be merged with the existing constraints following
RFC5280 p6.1.4 (excluded constraints will be appended, permitted
will be intersected).

Note that  \fInc\fP must be initialized prior to calling this function.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, \fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP
if the extension is not present, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
