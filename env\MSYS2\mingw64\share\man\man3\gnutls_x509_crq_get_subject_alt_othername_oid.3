.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_subject_alt_othername_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_subject_alt_othername_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_subject_alt_othername_oid(gnutls_x509_crq_t " crq ", unsigned int " seq ", void * " ret ", size_t * " ret_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "unsigned int seq" 12
specifies the sequence number of the alt name (0 for the first one, 1 for the second etc.)
.IP "void * ret" 12
is the place where the otherName OID will be copied to
.IP "size_t * ret_size" 12
holds the size of ret.
.SH "DESCRIPTION"
This function will extract the type OID of an otherName Subject
Alternative Name, contained in the given certificate, and return
the type as an enumerated element.

This function is only useful if
\fBgnutls_x509_crq_get_subject_alt_name()\fP returned
\fBGNUTLS_SAN_OTHERNAME\fP.
.SH "RETURNS"
the alternative subject name type on success, one of the
enumerated gnutls_x509_subject_alt_name_t.  For supported OIDs,
it will return one of the virtual (GNUTLS_SAN_OTHERNAME_*) types,
e.g. \fBGNUTLS_SAN_OTHERNAME_XMPP\fP, and \fBGNUTLS_SAN_OTHERNAME\fP for
unknown OIDs.  It will return \fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP if
 \fIret_size\fP is not large enough to hold the value.  In that case
 \fIret_size\fP will be updated with the required size.  If the
certificate does not have an Alternative name with the specified
sequence number and with the otherName type then
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP is returned.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
