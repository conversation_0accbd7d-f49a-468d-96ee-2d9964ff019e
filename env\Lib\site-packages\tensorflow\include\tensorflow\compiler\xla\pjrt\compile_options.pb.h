// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/pjrt/compile_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fcompile_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fcompile_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "xla/stream_executor/device_description.pb.h"
#include "xla/xla.pb.h"
#include "xla/xla_data.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fpjrt_2fcompile_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fpjrt_2fcompile_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fpjrt_2fcompile_5foptions_2eproto;
namespace xla {
class CompileOptionsProto;
struct CompileOptionsProtoDefaultTypeInternal;
extern CompileOptionsProtoDefaultTypeInternal _CompileOptionsProto_default_instance_;
class CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse;
struct CompileOptionsProto_EnvOptionOverridesEntry_DoNotUseDefaultTypeInternal;
extern CompileOptionsProto_EnvOptionOverridesEntry_DoNotUseDefaultTypeInternal _CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse_default_instance_;
class ExecutableAndOptionsProto;
struct ExecutableAndOptionsProtoDefaultTypeInternal;
extern ExecutableAndOptionsProtoDefaultTypeInternal _ExecutableAndOptionsProto_default_instance_;
class ExecutableBuildOptionsProto;
struct ExecutableBuildOptionsProtoDefaultTypeInternal;
extern ExecutableBuildOptionsProtoDefaultTypeInternal _ExecutableBuildOptionsProto_default_instance_;
class OptionOverrideProto;
struct OptionOverrideProtoDefaultTypeInternal;
extern OptionOverrideProtoDefaultTypeInternal _OptionOverrideProto_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::CompileOptionsProto* Arena::CreateMaybeMessage<::xla::CompileOptionsProto>(Arena*);
template<> ::xla::CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse* Arena::CreateMaybeMessage<::xla::CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse>(Arena*);
template<> ::xla::ExecutableAndOptionsProto* Arena::CreateMaybeMessage<::xla::ExecutableAndOptionsProto>(Arena*);
template<> ::xla::ExecutableBuildOptionsProto* Arena::CreateMaybeMessage<::xla::ExecutableBuildOptionsProto>(Arena*);
template<> ::xla::OptionOverrideProto* Arena::CreateMaybeMessage<::xla::OptionOverrideProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

// ===================================================================

class ExecutableBuildOptionsProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecutableBuildOptionsProto) */ {
 public:
  inline ExecutableBuildOptionsProto() : ExecutableBuildOptionsProto(nullptr) {}
  ~ExecutableBuildOptionsProto() override;
  explicit PROTOBUF_CONSTEXPR ExecutableBuildOptionsProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExecutableBuildOptionsProto(const ExecutableBuildOptionsProto& from);
  ExecutableBuildOptionsProto(ExecutableBuildOptionsProto&& from) noexcept
    : ExecutableBuildOptionsProto() {
    *this = ::std::move(from);
  }

  inline ExecutableBuildOptionsProto& operator=(const ExecutableBuildOptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecutableBuildOptionsProto& operator=(ExecutableBuildOptionsProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExecutableBuildOptionsProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExecutableBuildOptionsProto* internal_default_instance() {
    return reinterpret_cast<const ExecutableBuildOptionsProto*>(
               &_ExecutableBuildOptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ExecutableBuildOptionsProto& a, ExecutableBuildOptionsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecutableBuildOptionsProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecutableBuildOptionsProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExecutableBuildOptionsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExecutableBuildOptionsProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExecutableBuildOptionsProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExecutableBuildOptionsProto& from) {
    ExecutableBuildOptionsProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutableBuildOptionsProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecutableBuildOptionsProto";
  }
  protected:
  explicit ExecutableBuildOptionsProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllowSpmdShardingPropagationToOutputFieldNumber = 12,
    kAutoSpmdPartitioningMeshShapeFieldNumber = 16,
    kAutoSpmdPartitioningMeshIdsFieldNumber = 17,
    kAllowSpmdShardingPropagationToParametersFieldNumber = 18,
    kFdoProfileFieldNumber = 14,
    kResultLayoutFieldNumber = 2,
    kDebugOptionsFieldNumber = 3,
    kDeviceAssignmentFieldNumber = 9,
    kCompEnvsFieldNumber = 13,
    kDeviceOrdinalFieldNumber = 1,
    kNumReplicasFieldNumber = 4,
    kNumPartitionsFieldNumber = 5,
    kUseSpmdPartitioningFieldNumber = 6,
    kUseAutoSpmdPartitioningFieldNumber = 7,
    kDeduplicateHloFieldNumber = 8,
    kAliasPassthroughParamsFieldNumber = 10,
    kRunBackendOnlyFieldNumber = 11,
    kUseShardyPartitionerFieldNumber = 19,
    kDeviceMemorySizeFieldNumber = 15,
    kExecTimeOptimizationEffortFieldNumber = 20,
    kMemoryFittingEffortFieldNumber = 21,
    kProcessIndexFieldNumber = 22,
    kProcessCountFieldNumber = 23,
  };
  // repeated bool allow_spmd_sharding_propagation_to_output = 12;
  int allow_spmd_sharding_propagation_to_output_size() const;
  private:
  int _internal_allow_spmd_sharding_propagation_to_output_size() const;
  public:
  void clear_allow_spmd_sharding_propagation_to_output();
  private:
  bool _internal_allow_spmd_sharding_propagation_to_output(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_allow_spmd_sharding_propagation_to_output() const;
  void _internal_add_allow_spmd_sharding_propagation_to_output(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_allow_spmd_sharding_propagation_to_output();
  public:
  bool allow_spmd_sharding_propagation_to_output(int index) const;
  void set_allow_spmd_sharding_propagation_to_output(int index, bool value);
  void add_allow_spmd_sharding_propagation_to_output(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      allow_spmd_sharding_propagation_to_output() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_allow_spmd_sharding_propagation_to_output();

  // repeated int64 auto_spmd_partitioning_mesh_shape = 16;
  int auto_spmd_partitioning_mesh_shape_size() const;
  private:
  int _internal_auto_spmd_partitioning_mesh_shape_size() const;
  public:
  void clear_auto_spmd_partitioning_mesh_shape();
  private:
  int64_t _internal_auto_spmd_partitioning_mesh_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_auto_spmd_partitioning_mesh_shape() const;
  void _internal_add_auto_spmd_partitioning_mesh_shape(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_auto_spmd_partitioning_mesh_shape();
  public:
  int64_t auto_spmd_partitioning_mesh_shape(int index) const;
  void set_auto_spmd_partitioning_mesh_shape(int index, int64_t value);
  void add_auto_spmd_partitioning_mesh_shape(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      auto_spmd_partitioning_mesh_shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_auto_spmd_partitioning_mesh_shape();

  // repeated int64 auto_spmd_partitioning_mesh_ids = 17;
  int auto_spmd_partitioning_mesh_ids_size() const;
  private:
  int _internal_auto_spmd_partitioning_mesh_ids_size() const;
  public:
  void clear_auto_spmd_partitioning_mesh_ids();
  private:
  int64_t _internal_auto_spmd_partitioning_mesh_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_auto_spmd_partitioning_mesh_ids() const;
  void _internal_add_auto_spmd_partitioning_mesh_ids(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_auto_spmd_partitioning_mesh_ids();
  public:
  int64_t auto_spmd_partitioning_mesh_ids(int index) const;
  void set_auto_spmd_partitioning_mesh_ids(int index, int64_t value);
  void add_auto_spmd_partitioning_mesh_ids(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      auto_spmd_partitioning_mesh_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_auto_spmd_partitioning_mesh_ids();

  // repeated bool allow_spmd_sharding_propagation_to_parameters = 18;
  int allow_spmd_sharding_propagation_to_parameters_size() const;
  private:
  int _internal_allow_spmd_sharding_propagation_to_parameters_size() const;
  public:
  void clear_allow_spmd_sharding_propagation_to_parameters();
  private:
  bool _internal_allow_spmd_sharding_propagation_to_parameters(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_allow_spmd_sharding_propagation_to_parameters() const;
  void _internal_add_allow_spmd_sharding_propagation_to_parameters(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_allow_spmd_sharding_propagation_to_parameters();
  public:
  bool allow_spmd_sharding_propagation_to_parameters(int index) const;
  void set_allow_spmd_sharding_propagation_to_parameters(int index, bool value);
  void add_allow_spmd_sharding_propagation_to_parameters(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      allow_spmd_sharding_propagation_to_parameters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_allow_spmd_sharding_propagation_to_parameters();

  // bytes fdo_profile = 14;
  void clear_fdo_profile();
  const std::string& fdo_profile() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_fdo_profile(ArgT0&& arg0, ArgT... args);
  std::string* mutable_fdo_profile();
  PROTOBUF_NODISCARD std::string* release_fdo_profile();
  void set_allocated_fdo_profile(std::string* fdo_profile);
  private:
  const std::string& _internal_fdo_profile() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_fdo_profile(const std::string& value);
  std::string* _internal_mutable_fdo_profile();
  public:

  // .xla.ShapeProto result_layout = 2;
  bool has_result_layout() const;
  private:
  bool _internal_has_result_layout() const;
  public:
  void clear_result_layout();
  const ::xla::ShapeProto& result_layout() const;
  PROTOBUF_NODISCARD ::xla::ShapeProto* release_result_layout();
  ::xla::ShapeProto* mutable_result_layout();
  void set_allocated_result_layout(::xla::ShapeProto* result_layout);
  private:
  const ::xla::ShapeProto& _internal_result_layout() const;
  ::xla::ShapeProto* _internal_mutable_result_layout();
  public:
  void unsafe_arena_set_allocated_result_layout(
      ::xla::ShapeProto* result_layout);
  ::xla::ShapeProto* unsafe_arena_release_result_layout();

  // .xla.DebugOptions debug_options = 3;
  bool has_debug_options() const;
  private:
  bool _internal_has_debug_options() const;
  public:
  void clear_debug_options();
  const ::xla::DebugOptions& debug_options() const;
  PROTOBUF_NODISCARD ::xla::DebugOptions* release_debug_options();
  ::xla::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::xla::DebugOptions* debug_options);
  private:
  const ::xla::DebugOptions& _internal_debug_options() const;
  ::xla::DebugOptions* _internal_mutable_debug_options();
  public:
  void unsafe_arena_set_allocated_debug_options(
      ::xla::DebugOptions* debug_options);
  ::xla::DebugOptions* unsafe_arena_release_debug_options();

  // .xla.DeviceAssignmentProto device_assignment = 9;
  bool has_device_assignment() const;
  private:
  bool _internal_has_device_assignment() const;
  public:
  void clear_device_assignment();
  const ::xla::DeviceAssignmentProto& device_assignment() const;
  PROTOBUF_NODISCARD ::xla::DeviceAssignmentProto* release_device_assignment();
  ::xla::DeviceAssignmentProto* mutable_device_assignment();
  void set_allocated_device_assignment(::xla::DeviceAssignmentProto* device_assignment);
  private:
  const ::xla::DeviceAssignmentProto& _internal_device_assignment() const;
  ::xla::DeviceAssignmentProto* _internal_mutable_device_assignment();
  public:
  void unsafe_arena_set_allocated_device_assignment(
      ::xla::DeviceAssignmentProto* device_assignment);
  ::xla::DeviceAssignmentProto* unsafe_arena_release_device_assignment();

  // .xla.CompilationEnvironmentsProto comp_envs = 13;
  bool has_comp_envs() const;
  private:
  bool _internal_has_comp_envs() const;
  public:
  void clear_comp_envs();
  const ::xla::CompilationEnvironmentsProto& comp_envs() const;
  PROTOBUF_NODISCARD ::xla::CompilationEnvironmentsProto* release_comp_envs();
  ::xla::CompilationEnvironmentsProto* mutable_comp_envs();
  void set_allocated_comp_envs(::xla::CompilationEnvironmentsProto* comp_envs);
  private:
  const ::xla::CompilationEnvironmentsProto& _internal_comp_envs() const;
  ::xla::CompilationEnvironmentsProto* _internal_mutable_comp_envs();
  public:
  void unsafe_arena_set_allocated_comp_envs(
      ::xla::CompilationEnvironmentsProto* comp_envs);
  ::xla::CompilationEnvironmentsProto* unsafe_arena_release_comp_envs();

  // int64 device_ordinal = 1;
  void clear_device_ordinal();
  int64_t device_ordinal() const;
  void set_device_ordinal(int64_t value);
  private:
  int64_t _internal_device_ordinal() const;
  void _internal_set_device_ordinal(int64_t value);
  public:

  // int64 num_replicas = 4;
  void clear_num_replicas();
  int64_t num_replicas() const;
  void set_num_replicas(int64_t value);
  private:
  int64_t _internal_num_replicas() const;
  void _internal_set_num_replicas(int64_t value);
  public:

  // int64 num_partitions = 5;
  void clear_num_partitions();
  int64_t num_partitions() const;
  void set_num_partitions(int64_t value);
  private:
  int64_t _internal_num_partitions() const;
  void _internal_set_num_partitions(int64_t value);
  public:

  // bool use_spmd_partitioning = 6;
  void clear_use_spmd_partitioning();
  bool use_spmd_partitioning() const;
  void set_use_spmd_partitioning(bool value);
  private:
  bool _internal_use_spmd_partitioning() const;
  void _internal_set_use_spmd_partitioning(bool value);
  public:

  // bool use_auto_spmd_partitioning = 7;
  void clear_use_auto_spmd_partitioning();
  bool use_auto_spmd_partitioning() const;
  void set_use_auto_spmd_partitioning(bool value);
  private:
  bool _internal_use_auto_spmd_partitioning() const;
  void _internal_set_use_auto_spmd_partitioning(bool value);
  public:

  // bool deduplicate_hlo = 8;
  void clear_deduplicate_hlo();
  bool deduplicate_hlo() const;
  void set_deduplicate_hlo(bool value);
  private:
  bool _internal_deduplicate_hlo() const;
  void _internal_set_deduplicate_hlo(bool value);
  public:

  // bool alias_passthrough_params = 10;
  void clear_alias_passthrough_params();
  bool alias_passthrough_params() const;
  void set_alias_passthrough_params(bool value);
  private:
  bool _internal_alias_passthrough_params() const;
  void _internal_set_alias_passthrough_params(bool value);
  public:

  // bool run_backend_only = 11;
  void clear_run_backend_only();
  bool run_backend_only() const;
  void set_run_backend_only(bool value);
  private:
  bool _internal_run_backend_only() const;
  void _internal_set_run_backend_only(bool value);
  public:

  // bool use_shardy_partitioner = 19;
  void clear_use_shardy_partitioner();
  bool use_shardy_partitioner() const;
  void set_use_shardy_partitioner(bool value);
  private:
  bool _internal_use_shardy_partitioner() const;
  void _internal_set_use_shardy_partitioner(bool value);
  public:

  // int64 device_memory_size = 15;
  void clear_device_memory_size();
  int64_t device_memory_size() const;
  void set_device_memory_size(int64_t value);
  private:
  int64_t _internal_device_memory_size() const;
  void _internal_set_device_memory_size(int64_t value);
  public:

  // float exec_time_optimization_effort = 20;
  void clear_exec_time_optimization_effort();
  float exec_time_optimization_effort() const;
  void set_exec_time_optimization_effort(float value);
  private:
  float _internal_exec_time_optimization_effort() const;
  void _internal_set_exec_time_optimization_effort(float value);
  public:

  // float memory_fitting_effort = 21;
  void clear_memory_fitting_effort();
  float memory_fitting_effort() const;
  void set_memory_fitting_effort(float value);
  private:
  float _internal_memory_fitting_effort() const;
  void _internal_set_memory_fitting_effort(float value);
  public:

  // int64 process_index = 22;
  void clear_process_index();
  int64_t process_index() const;
  void set_process_index(int64_t value);
  private:
  int64_t _internal_process_index() const;
  void _internal_set_process_index(int64_t value);
  public:

  // int64 process_count = 23;
  void clear_process_count();
  int64_t process_count() const;
  void set_process_count(int64_t value);
  private:
  int64_t _internal_process_count() const;
  void _internal_set_process_count(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.ExecutableBuildOptionsProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > allow_spmd_sharding_propagation_to_output_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > auto_spmd_partitioning_mesh_shape_;
    mutable std::atomic<int> _auto_spmd_partitioning_mesh_shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > auto_spmd_partitioning_mesh_ids_;
    mutable std::atomic<int> _auto_spmd_partitioning_mesh_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > allow_spmd_sharding_propagation_to_parameters_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr fdo_profile_;
    ::xla::ShapeProto* result_layout_;
    ::xla::DebugOptions* debug_options_;
    ::xla::DeviceAssignmentProto* device_assignment_;
    ::xla::CompilationEnvironmentsProto* comp_envs_;
    int64_t device_ordinal_;
    int64_t num_replicas_;
    int64_t num_partitions_;
    bool use_spmd_partitioning_;
    bool use_auto_spmd_partitioning_;
    bool deduplicate_hlo_;
    bool alias_passthrough_params_;
    bool run_backend_only_;
    bool use_shardy_partitioner_;
    int64_t device_memory_size_;
    float exec_time_optimization_effort_;
    float memory_fitting_effort_;
    int64_t process_index_;
    int64_t process_count_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fcompile_5foptions_2eproto;
};
// -------------------------------------------------------------------

class OptionOverrideProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.OptionOverrideProto) */ {
 public:
  inline OptionOverrideProto() : OptionOverrideProto(nullptr) {}
  ~OptionOverrideProto() override;
  explicit PROTOBUF_CONSTEXPR OptionOverrideProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OptionOverrideProto(const OptionOverrideProto& from);
  OptionOverrideProto(OptionOverrideProto&& from) noexcept
    : OptionOverrideProto() {
    *this = ::std::move(from);
  }

  inline OptionOverrideProto& operator=(const OptionOverrideProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptionOverrideProto& operator=(OptionOverrideProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OptionOverrideProto& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kStringField = 1,
    kBoolField = 2,
    kIntField = 3,
    kDoubleField = 4,
    VALUE_NOT_SET = 0,
  };

  static inline const OptionOverrideProto* internal_default_instance() {
    return reinterpret_cast<const OptionOverrideProto*>(
               &_OptionOverrideProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(OptionOverrideProto& a, OptionOverrideProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OptionOverrideProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OptionOverrideProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OptionOverrideProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OptionOverrideProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OptionOverrideProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OptionOverrideProto& from) {
    OptionOverrideProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptionOverrideProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.OptionOverrideProto";
  }
  protected:
  explicit OptionOverrideProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStringFieldFieldNumber = 1,
    kBoolFieldFieldNumber = 2,
    kIntFieldFieldNumber = 3,
    kDoubleFieldFieldNumber = 4,
  };
  // string string_field = 1;
  bool has_string_field() const;
  private:
  bool _internal_has_string_field() const;
  public:
  void clear_string_field();
  const std::string& string_field() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_field(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_field();
  PROTOBUF_NODISCARD std::string* release_string_field();
  void set_allocated_string_field(std::string* string_field);
  private:
  const std::string& _internal_string_field() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_field(const std::string& value);
  std::string* _internal_mutable_string_field();
  public:

  // bool bool_field = 2;
  bool has_bool_field() const;
  private:
  bool _internal_has_bool_field() const;
  public:
  void clear_bool_field();
  bool bool_field() const;
  void set_bool_field(bool value);
  private:
  bool _internal_bool_field() const;
  void _internal_set_bool_field(bool value);
  public:

  // int64 int_field = 3;
  bool has_int_field() const;
  private:
  bool _internal_has_int_field() const;
  public:
  void clear_int_field();
  int64_t int_field() const;
  void set_int_field(int64_t value);
  private:
  int64_t _internal_int_field() const;
  void _internal_set_int_field(int64_t value);
  public:

  // double double_field = 4;
  bool has_double_field() const;
  private:
  bool _internal_has_double_field() const;
  public:
  void clear_double_field();
  double double_field() const;
  void set_double_field(double value);
  private:
  double _internal_double_field() const;
  void _internal_set_double_field(double value);
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:xla.OptionOverrideProto)
 private:
  class _Internal;
  void set_has_string_field();
  void set_has_bool_field();
  void set_has_int_field();
  void set_has_double_field();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union ValueUnion {
      constexpr ValueUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_field_;
      bool bool_field_;
      int64_t int_field_;
      double double_field_;
    } value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fcompile_5foptions_2eproto;
};
// -------------------------------------------------------------------

class CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse, 
    std::string, ::xla::OptionOverrideProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse, 
    std::string, ::xla::OptionOverrideProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse& other);
  static const CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse*>(&_CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.CompileOptionsProto.EnvOptionOverridesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2fpjrt_2fcompile_5foptions_2eproto;
};

// -------------------------------------------------------------------

class CompileOptionsProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CompileOptionsProto) */ {
 public:
  inline CompileOptionsProto() : CompileOptionsProto(nullptr) {}
  ~CompileOptionsProto() override;
  explicit PROTOBUF_CONSTEXPR CompileOptionsProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompileOptionsProto(const CompileOptionsProto& from);
  CompileOptionsProto(CompileOptionsProto&& from) noexcept
    : CompileOptionsProto() {
    *this = ::std::move(from);
  }

  inline CompileOptionsProto& operator=(const CompileOptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompileOptionsProto& operator=(CompileOptionsProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompileOptionsProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompileOptionsProto* internal_default_instance() {
    return reinterpret_cast<const CompileOptionsProto*>(
               &_CompileOptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CompileOptionsProto& a, CompileOptionsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(CompileOptionsProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompileOptionsProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompileOptionsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompileOptionsProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompileOptionsProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompileOptionsProto& from) {
    CompileOptionsProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompileOptionsProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CompileOptionsProto";
  }
  protected:
  explicit CompileOptionsProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kArgumentLayoutsFieldNumber = 1,
    kEnvOptionOverridesFieldNumber = 7,
    kSerializedMultiSliceConfigFieldNumber = 6,
    kExecutableBuildOptionsFieldNumber = 3,
    kTargetConfigFieldNumber = 8,
    kProfileVersionFieldNumber = 5,
    kParameterIsTupledArgumentsFieldNumber = 2,
    kCompilePortableExecutableFieldNumber = 4,
  };
  // repeated .xla.ShapeProto argument_layouts = 1;
  int argument_layouts_size() const;
  private:
  int _internal_argument_layouts_size() const;
  public:
  void clear_argument_layouts();
  ::xla::ShapeProto* mutable_argument_layouts(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
      mutable_argument_layouts();
  private:
  const ::xla::ShapeProto& _internal_argument_layouts(int index) const;
  ::xla::ShapeProto* _internal_add_argument_layouts();
  public:
  const ::xla::ShapeProto& argument_layouts(int index) const;
  ::xla::ShapeProto* add_argument_layouts();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
      argument_layouts() const;

  // map<string, .xla.OptionOverrideProto> env_option_overrides = 7;
  int env_option_overrides_size() const;
  private:
  int _internal_env_option_overrides_size() const;
  public:
  void clear_env_option_overrides();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::OptionOverrideProto >&
      _internal_env_option_overrides() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::OptionOverrideProto >*
      _internal_mutable_env_option_overrides();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::OptionOverrideProto >&
      env_option_overrides() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::OptionOverrideProto >*
      mutable_env_option_overrides();

  // bytes serialized_multi_slice_config = 6;
  void clear_serialized_multi_slice_config();
  const std::string& serialized_multi_slice_config() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serialized_multi_slice_config(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serialized_multi_slice_config();
  PROTOBUF_NODISCARD std::string* release_serialized_multi_slice_config();
  void set_allocated_serialized_multi_slice_config(std::string* serialized_multi_slice_config);
  private:
  const std::string& _internal_serialized_multi_slice_config() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serialized_multi_slice_config(const std::string& value);
  std::string* _internal_mutable_serialized_multi_slice_config();
  public:

  // .xla.ExecutableBuildOptionsProto executable_build_options = 3;
  bool has_executable_build_options() const;
  private:
  bool _internal_has_executable_build_options() const;
  public:
  void clear_executable_build_options();
  const ::xla::ExecutableBuildOptionsProto& executable_build_options() const;
  PROTOBUF_NODISCARD ::xla::ExecutableBuildOptionsProto* release_executable_build_options();
  ::xla::ExecutableBuildOptionsProto* mutable_executable_build_options();
  void set_allocated_executable_build_options(::xla::ExecutableBuildOptionsProto* executable_build_options);
  private:
  const ::xla::ExecutableBuildOptionsProto& _internal_executable_build_options() const;
  ::xla::ExecutableBuildOptionsProto* _internal_mutable_executable_build_options();
  public:
  void unsafe_arena_set_allocated_executable_build_options(
      ::xla::ExecutableBuildOptionsProto* executable_build_options);
  ::xla::ExecutableBuildOptionsProto* unsafe_arena_release_executable_build_options();

  // .stream_executor.GpuTargetConfigProto target_config = 8;
  bool has_target_config() const;
  private:
  bool _internal_has_target_config() const;
  public:
  void clear_target_config();
  const ::stream_executor::GpuTargetConfigProto& target_config() const;
  PROTOBUF_NODISCARD ::stream_executor::GpuTargetConfigProto* release_target_config();
  ::stream_executor::GpuTargetConfigProto* mutable_target_config();
  void set_allocated_target_config(::stream_executor::GpuTargetConfigProto* target_config);
  private:
  const ::stream_executor::GpuTargetConfigProto& _internal_target_config() const;
  ::stream_executor::GpuTargetConfigProto* _internal_mutable_target_config();
  public:
  void unsafe_arena_set_allocated_target_config(
      ::stream_executor::GpuTargetConfigProto* target_config);
  ::stream_executor::GpuTargetConfigProto* unsafe_arena_release_target_config();

  // int64 profile_version = 5;
  void clear_profile_version();
  int64_t profile_version() const;
  void set_profile_version(int64_t value);
  private:
  int64_t _internal_profile_version() const;
  void _internal_set_profile_version(int64_t value);
  public:

  // bool parameter_is_tupled_arguments = 2;
  void clear_parameter_is_tupled_arguments();
  bool parameter_is_tupled_arguments() const;
  void set_parameter_is_tupled_arguments(bool value);
  private:
  bool _internal_parameter_is_tupled_arguments() const;
  void _internal_set_parameter_is_tupled_arguments(bool value);
  public:

  // bool compile_portable_executable = 4;
  void clear_compile_portable_executable();
  bool compile_portable_executable() const;
  void set_compile_portable_executable(bool value);
  private:
  bool _internal_compile_portable_executable() const;
  void _internal_set_compile_portable_executable(bool value);
  public:

  // @@protoc_insertion_point(class_scope:xla.CompileOptionsProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto > argument_layouts_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        CompileOptionsProto_EnvOptionOverridesEntry_DoNotUse,
        std::string, ::xla::OptionOverrideProto,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> env_option_overrides_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serialized_multi_slice_config_;
    ::xla::ExecutableBuildOptionsProto* executable_build_options_;
    ::stream_executor::GpuTargetConfigProto* target_config_;
    int64_t profile_version_;
    bool parameter_is_tupled_arguments_;
    bool compile_portable_executable_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fcompile_5foptions_2eproto;
};
// -------------------------------------------------------------------

class ExecutableAndOptionsProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ExecutableAndOptionsProto) */ {
 public:
  inline ExecutableAndOptionsProto() : ExecutableAndOptionsProto(nullptr) {}
  ~ExecutableAndOptionsProto() override;
  explicit PROTOBUF_CONSTEXPR ExecutableAndOptionsProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExecutableAndOptionsProto(const ExecutableAndOptionsProto& from);
  ExecutableAndOptionsProto(ExecutableAndOptionsProto&& from) noexcept
    : ExecutableAndOptionsProto() {
    *this = ::std::move(from);
  }

  inline ExecutableAndOptionsProto& operator=(const ExecutableAndOptionsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecutableAndOptionsProto& operator=(ExecutableAndOptionsProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExecutableAndOptionsProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExecutableAndOptionsProto* internal_default_instance() {
    return reinterpret_cast<const ExecutableAndOptionsProto*>(
               &_ExecutableAndOptionsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ExecutableAndOptionsProto& a, ExecutableAndOptionsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecutableAndOptionsProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecutableAndOptionsProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExecutableAndOptionsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExecutableAndOptionsProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExecutableAndOptionsProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExecutableAndOptionsProto& from) {
    ExecutableAndOptionsProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutableAndOptionsProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ExecutableAndOptionsProto";
  }
  protected:
  explicit ExecutableAndOptionsProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSerializedExecutableFieldNumber = 1,
    kCompileOptionsFieldNumber = 2,
  };
  // bytes serialized_executable = 1;
  void clear_serialized_executable();
  const std::string& serialized_executable() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serialized_executable(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serialized_executable();
  PROTOBUF_NODISCARD std::string* release_serialized_executable();
  void set_allocated_serialized_executable(std::string* serialized_executable);
  private:
  const std::string& _internal_serialized_executable() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serialized_executable(const std::string& value);
  std::string* _internal_mutable_serialized_executable();
  public:

  // .xla.CompileOptionsProto compile_options = 2;
  bool has_compile_options() const;
  private:
  bool _internal_has_compile_options() const;
  public:
  void clear_compile_options();
  const ::xla::CompileOptionsProto& compile_options() const;
  PROTOBUF_NODISCARD ::xla::CompileOptionsProto* release_compile_options();
  ::xla::CompileOptionsProto* mutable_compile_options();
  void set_allocated_compile_options(::xla::CompileOptionsProto* compile_options);
  private:
  const ::xla::CompileOptionsProto& _internal_compile_options() const;
  ::xla::CompileOptionsProto* _internal_mutable_compile_options();
  public:
  void unsafe_arena_set_allocated_compile_options(
      ::xla::CompileOptionsProto* compile_options);
  ::xla::CompileOptionsProto* unsafe_arena_release_compile_options();

  // @@protoc_insertion_point(class_scope:xla.ExecutableAndOptionsProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serialized_executable_;
    ::xla::CompileOptionsProto* compile_options_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fcompile_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ExecutableBuildOptionsProto

// int64 device_ordinal = 1;
inline void ExecutableBuildOptionsProto::clear_device_ordinal() {
  _impl_.device_ordinal_ = int64_t{0};
}
inline int64_t ExecutableBuildOptionsProto::_internal_device_ordinal() const {
  return _impl_.device_ordinal_;
}
inline int64_t ExecutableBuildOptionsProto::device_ordinal() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.device_ordinal)
  return _internal_device_ordinal();
}
inline void ExecutableBuildOptionsProto::_internal_set_device_ordinal(int64_t value) {
  
  _impl_.device_ordinal_ = value;
}
inline void ExecutableBuildOptionsProto::set_device_ordinal(int64_t value) {
  _internal_set_device_ordinal(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.device_ordinal)
}

// .xla.ShapeProto result_layout = 2;
inline bool ExecutableBuildOptionsProto::_internal_has_result_layout() const {
  return this != internal_default_instance() && _impl_.result_layout_ != nullptr;
}
inline bool ExecutableBuildOptionsProto::has_result_layout() const {
  return _internal_has_result_layout();
}
inline const ::xla::ShapeProto& ExecutableBuildOptionsProto::_internal_result_layout() const {
  const ::xla::ShapeProto* p = _impl_.result_layout_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::ShapeProto&>(
      ::xla::_ShapeProto_default_instance_);
}
inline const ::xla::ShapeProto& ExecutableBuildOptionsProto::result_layout() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.result_layout)
  return _internal_result_layout();
}
inline void ExecutableBuildOptionsProto::unsafe_arena_set_allocated_result_layout(
    ::xla::ShapeProto* result_layout) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.result_layout_);
  }
  _impl_.result_layout_ = result_layout;
  if (result_layout) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.ExecutableBuildOptionsProto.result_layout)
}
inline ::xla::ShapeProto* ExecutableBuildOptionsProto::release_result_layout() {
  
  ::xla::ShapeProto* temp = _impl_.result_layout_;
  _impl_.result_layout_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::ShapeProto* ExecutableBuildOptionsProto::unsafe_arena_release_result_layout() {
  // @@protoc_insertion_point(field_release:xla.ExecutableBuildOptionsProto.result_layout)
  
  ::xla::ShapeProto* temp = _impl_.result_layout_;
  _impl_.result_layout_ = nullptr;
  return temp;
}
inline ::xla::ShapeProto* ExecutableBuildOptionsProto::_internal_mutable_result_layout() {
  
  if (_impl_.result_layout_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ShapeProto>(GetArenaForAllocation());
    _impl_.result_layout_ = p;
  }
  return _impl_.result_layout_;
}
inline ::xla::ShapeProto* ExecutableBuildOptionsProto::mutable_result_layout() {
  ::xla::ShapeProto* _msg = _internal_mutable_result_layout();
  // @@protoc_insertion_point(field_mutable:xla.ExecutableBuildOptionsProto.result_layout)
  return _msg;
}
inline void ExecutableBuildOptionsProto::set_allocated_result_layout(::xla::ShapeProto* result_layout) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.result_layout_);
  }
  if (result_layout) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(result_layout));
    if (message_arena != submessage_arena) {
      result_layout = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, result_layout, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.result_layout_ = result_layout;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutableBuildOptionsProto.result_layout)
}

// .xla.CompilationEnvironmentsProto comp_envs = 13;
inline bool ExecutableBuildOptionsProto::_internal_has_comp_envs() const {
  return this != internal_default_instance() && _impl_.comp_envs_ != nullptr;
}
inline bool ExecutableBuildOptionsProto::has_comp_envs() const {
  return _internal_has_comp_envs();
}
inline const ::xla::CompilationEnvironmentsProto& ExecutableBuildOptionsProto::_internal_comp_envs() const {
  const ::xla::CompilationEnvironmentsProto* p = _impl_.comp_envs_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::CompilationEnvironmentsProto&>(
      ::xla::_CompilationEnvironmentsProto_default_instance_);
}
inline const ::xla::CompilationEnvironmentsProto& ExecutableBuildOptionsProto::comp_envs() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.comp_envs)
  return _internal_comp_envs();
}
inline void ExecutableBuildOptionsProto::unsafe_arena_set_allocated_comp_envs(
    ::xla::CompilationEnvironmentsProto* comp_envs) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.comp_envs_);
  }
  _impl_.comp_envs_ = comp_envs;
  if (comp_envs) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.ExecutableBuildOptionsProto.comp_envs)
}
inline ::xla::CompilationEnvironmentsProto* ExecutableBuildOptionsProto::release_comp_envs() {
  
  ::xla::CompilationEnvironmentsProto* temp = _impl_.comp_envs_;
  _impl_.comp_envs_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::CompilationEnvironmentsProto* ExecutableBuildOptionsProto::unsafe_arena_release_comp_envs() {
  // @@protoc_insertion_point(field_release:xla.ExecutableBuildOptionsProto.comp_envs)
  
  ::xla::CompilationEnvironmentsProto* temp = _impl_.comp_envs_;
  _impl_.comp_envs_ = nullptr;
  return temp;
}
inline ::xla::CompilationEnvironmentsProto* ExecutableBuildOptionsProto::_internal_mutable_comp_envs() {
  
  if (_impl_.comp_envs_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::CompilationEnvironmentsProto>(GetArenaForAllocation());
    _impl_.comp_envs_ = p;
  }
  return _impl_.comp_envs_;
}
inline ::xla::CompilationEnvironmentsProto* ExecutableBuildOptionsProto::mutable_comp_envs() {
  ::xla::CompilationEnvironmentsProto* _msg = _internal_mutable_comp_envs();
  // @@protoc_insertion_point(field_mutable:xla.ExecutableBuildOptionsProto.comp_envs)
  return _msg;
}
inline void ExecutableBuildOptionsProto::set_allocated_comp_envs(::xla::CompilationEnvironmentsProto* comp_envs) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.comp_envs_);
  }
  if (comp_envs) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(comp_envs));
    if (message_arena != submessage_arena) {
      comp_envs = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, comp_envs, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.comp_envs_ = comp_envs;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutableBuildOptionsProto.comp_envs)
}

// .xla.DebugOptions debug_options = 3;
inline bool ExecutableBuildOptionsProto::_internal_has_debug_options() const {
  return this != internal_default_instance() && _impl_.debug_options_ != nullptr;
}
inline bool ExecutableBuildOptionsProto::has_debug_options() const {
  return _internal_has_debug_options();
}
inline const ::xla::DebugOptions& ExecutableBuildOptionsProto::_internal_debug_options() const {
  const ::xla::DebugOptions* p = _impl_.debug_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::DebugOptions&>(
      ::xla::_DebugOptions_default_instance_);
}
inline const ::xla::DebugOptions& ExecutableBuildOptionsProto::debug_options() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.debug_options)
  return _internal_debug_options();
}
inline void ExecutableBuildOptionsProto::unsafe_arena_set_allocated_debug_options(
    ::xla::DebugOptions* debug_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debug_options_);
  }
  _impl_.debug_options_ = debug_options;
  if (debug_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.ExecutableBuildOptionsProto.debug_options)
}
inline ::xla::DebugOptions* ExecutableBuildOptionsProto::release_debug_options() {
  
  ::xla::DebugOptions* temp = _impl_.debug_options_;
  _impl_.debug_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::DebugOptions* ExecutableBuildOptionsProto::unsafe_arena_release_debug_options() {
  // @@protoc_insertion_point(field_release:xla.ExecutableBuildOptionsProto.debug_options)
  
  ::xla::DebugOptions* temp = _impl_.debug_options_;
  _impl_.debug_options_ = nullptr;
  return temp;
}
inline ::xla::DebugOptions* ExecutableBuildOptionsProto::_internal_mutable_debug_options() {
  
  if (_impl_.debug_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DebugOptions>(GetArenaForAllocation());
    _impl_.debug_options_ = p;
  }
  return _impl_.debug_options_;
}
inline ::xla::DebugOptions* ExecutableBuildOptionsProto::mutable_debug_options() {
  ::xla::DebugOptions* _msg = _internal_mutable_debug_options();
  // @@protoc_insertion_point(field_mutable:xla.ExecutableBuildOptionsProto.debug_options)
  return _msg;
}
inline void ExecutableBuildOptionsProto::set_allocated_debug_options(::xla::DebugOptions* debug_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debug_options_);
  }
  if (debug_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(debug_options));
    if (message_arena != submessage_arena) {
      debug_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutableBuildOptionsProto.debug_options)
}

// int64 num_replicas = 4;
inline void ExecutableBuildOptionsProto::clear_num_replicas() {
  _impl_.num_replicas_ = int64_t{0};
}
inline int64_t ExecutableBuildOptionsProto::_internal_num_replicas() const {
  return _impl_.num_replicas_;
}
inline int64_t ExecutableBuildOptionsProto::num_replicas() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.num_replicas)
  return _internal_num_replicas();
}
inline void ExecutableBuildOptionsProto::_internal_set_num_replicas(int64_t value) {
  
  _impl_.num_replicas_ = value;
}
inline void ExecutableBuildOptionsProto::set_num_replicas(int64_t value) {
  _internal_set_num_replicas(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.num_replicas)
}

// int64 num_partitions = 5;
inline void ExecutableBuildOptionsProto::clear_num_partitions() {
  _impl_.num_partitions_ = int64_t{0};
}
inline int64_t ExecutableBuildOptionsProto::_internal_num_partitions() const {
  return _impl_.num_partitions_;
}
inline int64_t ExecutableBuildOptionsProto::num_partitions() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.num_partitions)
  return _internal_num_partitions();
}
inline void ExecutableBuildOptionsProto::_internal_set_num_partitions(int64_t value) {
  
  _impl_.num_partitions_ = value;
}
inline void ExecutableBuildOptionsProto::set_num_partitions(int64_t value) {
  _internal_set_num_partitions(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.num_partitions)
}

// bool use_spmd_partitioning = 6;
inline void ExecutableBuildOptionsProto::clear_use_spmd_partitioning() {
  _impl_.use_spmd_partitioning_ = false;
}
inline bool ExecutableBuildOptionsProto::_internal_use_spmd_partitioning() const {
  return _impl_.use_spmd_partitioning_;
}
inline bool ExecutableBuildOptionsProto::use_spmd_partitioning() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.use_spmd_partitioning)
  return _internal_use_spmd_partitioning();
}
inline void ExecutableBuildOptionsProto::_internal_set_use_spmd_partitioning(bool value) {
  
  _impl_.use_spmd_partitioning_ = value;
}
inline void ExecutableBuildOptionsProto::set_use_spmd_partitioning(bool value) {
  _internal_set_use_spmd_partitioning(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.use_spmd_partitioning)
}

// bool use_auto_spmd_partitioning = 7;
inline void ExecutableBuildOptionsProto::clear_use_auto_spmd_partitioning() {
  _impl_.use_auto_spmd_partitioning_ = false;
}
inline bool ExecutableBuildOptionsProto::_internal_use_auto_spmd_partitioning() const {
  return _impl_.use_auto_spmd_partitioning_;
}
inline bool ExecutableBuildOptionsProto::use_auto_spmd_partitioning() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.use_auto_spmd_partitioning)
  return _internal_use_auto_spmd_partitioning();
}
inline void ExecutableBuildOptionsProto::_internal_set_use_auto_spmd_partitioning(bool value) {
  
  _impl_.use_auto_spmd_partitioning_ = value;
}
inline void ExecutableBuildOptionsProto::set_use_auto_spmd_partitioning(bool value) {
  _internal_set_use_auto_spmd_partitioning(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.use_auto_spmd_partitioning)
}

// float exec_time_optimization_effort = 20;
inline void ExecutableBuildOptionsProto::clear_exec_time_optimization_effort() {
  _impl_.exec_time_optimization_effort_ = 0;
}
inline float ExecutableBuildOptionsProto::_internal_exec_time_optimization_effort() const {
  return _impl_.exec_time_optimization_effort_;
}
inline float ExecutableBuildOptionsProto::exec_time_optimization_effort() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.exec_time_optimization_effort)
  return _internal_exec_time_optimization_effort();
}
inline void ExecutableBuildOptionsProto::_internal_set_exec_time_optimization_effort(float value) {
  
  _impl_.exec_time_optimization_effort_ = value;
}
inline void ExecutableBuildOptionsProto::set_exec_time_optimization_effort(float value) {
  _internal_set_exec_time_optimization_effort(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.exec_time_optimization_effort)
}

// float memory_fitting_effort = 21;
inline void ExecutableBuildOptionsProto::clear_memory_fitting_effort() {
  _impl_.memory_fitting_effort_ = 0;
}
inline float ExecutableBuildOptionsProto::_internal_memory_fitting_effort() const {
  return _impl_.memory_fitting_effort_;
}
inline float ExecutableBuildOptionsProto::memory_fitting_effort() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.memory_fitting_effort)
  return _internal_memory_fitting_effort();
}
inline void ExecutableBuildOptionsProto::_internal_set_memory_fitting_effort(float value) {
  
  _impl_.memory_fitting_effort_ = value;
}
inline void ExecutableBuildOptionsProto::set_memory_fitting_effort(float value) {
  _internal_set_memory_fitting_effort(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.memory_fitting_effort)
}

// bool deduplicate_hlo = 8;
inline void ExecutableBuildOptionsProto::clear_deduplicate_hlo() {
  _impl_.deduplicate_hlo_ = false;
}
inline bool ExecutableBuildOptionsProto::_internal_deduplicate_hlo() const {
  return _impl_.deduplicate_hlo_;
}
inline bool ExecutableBuildOptionsProto::deduplicate_hlo() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.deduplicate_hlo)
  return _internal_deduplicate_hlo();
}
inline void ExecutableBuildOptionsProto::_internal_set_deduplicate_hlo(bool value) {
  
  _impl_.deduplicate_hlo_ = value;
}
inline void ExecutableBuildOptionsProto::set_deduplicate_hlo(bool value) {
  _internal_set_deduplicate_hlo(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.deduplicate_hlo)
}

// .xla.DeviceAssignmentProto device_assignment = 9;
inline bool ExecutableBuildOptionsProto::_internal_has_device_assignment() const {
  return this != internal_default_instance() && _impl_.device_assignment_ != nullptr;
}
inline bool ExecutableBuildOptionsProto::has_device_assignment() const {
  return _internal_has_device_assignment();
}
inline const ::xla::DeviceAssignmentProto& ExecutableBuildOptionsProto::_internal_device_assignment() const {
  const ::xla::DeviceAssignmentProto* p = _impl_.device_assignment_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::DeviceAssignmentProto&>(
      ::xla::_DeviceAssignmentProto_default_instance_);
}
inline const ::xla::DeviceAssignmentProto& ExecutableBuildOptionsProto::device_assignment() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.device_assignment)
  return _internal_device_assignment();
}
inline void ExecutableBuildOptionsProto::unsafe_arena_set_allocated_device_assignment(
    ::xla::DeviceAssignmentProto* device_assignment) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_assignment_);
  }
  _impl_.device_assignment_ = device_assignment;
  if (device_assignment) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.ExecutableBuildOptionsProto.device_assignment)
}
inline ::xla::DeviceAssignmentProto* ExecutableBuildOptionsProto::release_device_assignment() {
  
  ::xla::DeviceAssignmentProto* temp = _impl_.device_assignment_;
  _impl_.device_assignment_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::DeviceAssignmentProto* ExecutableBuildOptionsProto::unsafe_arena_release_device_assignment() {
  // @@protoc_insertion_point(field_release:xla.ExecutableBuildOptionsProto.device_assignment)
  
  ::xla::DeviceAssignmentProto* temp = _impl_.device_assignment_;
  _impl_.device_assignment_ = nullptr;
  return temp;
}
inline ::xla::DeviceAssignmentProto* ExecutableBuildOptionsProto::_internal_mutable_device_assignment() {
  
  if (_impl_.device_assignment_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::DeviceAssignmentProto>(GetArenaForAllocation());
    _impl_.device_assignment_ = p;
  }
  return _impl_.device_assignment_;
}
inline ::xla::DeviceAssignmentProto* ExecutableBuildOptionsProto::mutable_device_assignment() {
  ::xla::DeviceAssignmentProto* _msg = _internal_mutable_device_assignment();
  // @@protoc_insertion_point(field_mutable:xla.ExecutableBuildOptionsProto.device_assignment)
  return _msg;
}
inline void ExecutableBuildOptionsProto::set_allocated_device_assignment(::xla::DeviceAssignmentProto* device_assignment) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_assignment_);
  }
  if (device_assignment) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_assignment));
    if (message_arena != submessage_arena) {
      device_assignment = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_assignment, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.device_assignment_ = device_assignment;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutableBuildOptionsProto.device_assignment)
}

// bool alias_passthrough_params = 10;
inline void ExecutableBuildOptionsProto::clear_alias_passthrough_params() {
  _impl_.alias_passthrough_params_ = false;
}
inline bool ExecutableBuildOptionsProto::_internal_alias_passthrough_params() const {
  return _impl_.alias_passthrough_params_;
}
inline bool ExecutableBuildOptionsProto::alias_passthrough_params() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.alias_passthrough_params)
  return _internal_alias_passthrough_params();
}
inline void ExecutableBuildOptionsProto::_internal_set_alias_passthrough_params(bool value) {
  
  _impl_.alias_passthrough_params_ = value;
}
inline void ExecutableBuildOptionsProto::set_alias_passthrough_params(bool value) {
  _internal_set_alias_passthrough_params(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.alias_passthrough_params)
}

// bool run_backend_only = 11;
inline void ExecutableBuildOptionsProto::clear_run_backend_only() {
  _impl_.run_backend_only_ = false;
}
inline bool ExecutableBuildOptionsProto::_internal_run_backend_only() const {
  return _impl_.run_backend_only_;
}
inline bool ExecutableBuildOptionsProto::run_backend_only() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.run_backend_only)
  return _internal_run_backend_only();
}
inline void ExecutableBuildOptionsProto::_internal_set_run_backend_only(bool value) {
  
  _impl_.run_backend_only_ = value;
}
inline void ExecutableBuildOptionsProto::set_run_backend_only(bool value) {
  _internal_set_run_backend_only(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.run_backend_only)
}

// repeated bool allow_spmd_sharding_propagation_to_parameters = 18;
inline int ExecutableBuildOptionsProto::_internal_allow_spmd_sharding_propagation_to_parameters_size() const {
  return _impl_.allow_spmd_sharding_propagation_to_parameters_.size();
}
inline int ExecutableBuildOptionsProto::allow_spmd_sharding_propagation_to_parameters_size() const {
  return _internal_allow_spmd_sharding_propagation_to_parameters_size();
}
inline void ExecutableBuildOptionsProto::clear_allow_spmd_sharding_propagation_to_parameters() {
  _impl_.allow_spmd_sharding_propagation_to_parameters_.Clear();
}
inline bool ExecutableBuildOptionsProto::_internal_allow_spmd_sharding_propagation_to_parameters(int index) const {
  return _impl_.allow_spmd_sharding_propagation_to_parameters_.Get(index);
}
inline bool ExecutableBuildOptionsProto::allow_spmd_sharding_propagation_to_parameters(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_parameters)
  return _internal_allow_spmd_sharding_propagation_to_parameters(index);
}
inline void ExecutableBuildOptionsProto::set_allow_spmd_sharding_propagation_to_parameters(int index, bool value) {
  _impl_.allow_spmd_sharding_propagation_to_parameters_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_parameters)
}
inline void ExecutableBuildOptionsProto::_internal_add_allow_spmd_sharding_propagation_to_parameters(bool value) {
  _impl_.allow_spmd_sharding_propagation_to_parameters_.Add(value);
}
inline void ExecutableBuildOptionsProto::add_allow_spmd_sharding_propagation_to_parameters(bool value) {
  _internal_add_allow_spmd_sharding_propagation_to_parameters(value);
  // @@protoc_insertion_point(field_add:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_parameters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
ExecutableBuildOptionsProto::_internal_allow_spmd_sharding_propagation_to_parameters() const {
  return _impl_.allow_spmd_sharding_propagation_to_parameters_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
ExecutableBuildOptionsProto::allow_spmd_sharding_propagation_to_parameters() const {
  // @@protoc_insertion_point(field_list:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_parameters)
  return _internal_allow_spmd_sharding_propagation_to_parameters();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
ExecutableBuildOptionsProto::_internal_mutable_allow_spmd_sharding_propagation_to_parameters() {
  return &_impl_.allow_spmd_sharding_propagation_to_parameters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
ExecutableBuildOptionsProto::mutable_allow_spmd_sharding_propagation_to_parameters() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_parameters)
  return _internal_mutable_allow_spmd_sharding_propagation_to_parameters();
}

// repeated bool allow_spmd_sharding_propagation_to_output = 12;
inline int ExecutableBuildOptionsProto::_internal_allow_spmd_sharding_propagation_to_output_size() const {
  return _impl_.allow_spmd_sharding_propagation_to_output_.size();
}
inline int ExecutableBuildOptionsProto::allow_spmd_sharding_propagation_to_output_size() const {
  return _internal_allow_spmd_sharding_propagation_to_output_size();
}
inline void ExecutableBuildOptionsProto::clear_allow_spmd_sharding_propagation_to_output() {
  _impl_.allow_spmd_sharding_propagation_to_output_.Clear();
}
inline bool ExecutableBuildOptionsProto::_internal_allow_spmd_sharding_propagation_to_output(int index) const {
  return _impl_.allow_spmd_sharding_propagation_to_output_.Get(index);
}
inline bool ExecutableBuildOptionsProto::allow_spmd_sharding_propagation_to_output(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_output)
  return _internal_allow_spmd_sharding_propagation_to_output(index);
}
inline void ExecutableBuildOptionsProto::set_allow_spmd_sharding_propagation_to_output(int index, bool value) {
  _impl_.allow_spmd_sharding_propagation_to_output_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_output)
}
inline void ExecutableBuildOptionsProto::_internal_add_allow_spmd_sharding_propagation_to_output(bool value) {
  _impl_.allow_spmd_sharding_propagation_to_output_.Add(value);
}
inline void ExecutableBuildOptionsProto::add_allow_spmd_sharding_propagation_to_output(bool value) {
  _internal_add_allow_spmd_sharding_propagation_to_output(value);
  // @@protoc_insertion_point(field_add:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_output)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
ExecutableBuildOptionsProto::_internal_allow_spmd_sharding_propagation_to_output() const {
  return _impl_.allow_spmd_sharding_propagation_to_output_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
ExecutableBuildOptionsProto::allow_spmd_sharding_propagation_to_output() const {
  // @@protoc_insertion_point(field_list:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_output)
  return _internal_allow_spmd_sharding_propagation_to_output();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
ExecutableBuildOptionsProto::_internal_mutable_allow_spmd_sharding_propagation_to_output() {
  return &_impl_.allow_spmd_sharding_propagation_to_output_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
ExecutableBuildOptionsProto::mutable_allow_spmd_sharding_propagation_to_output() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecutableBuildOptionsProto.allow_spmd_sharding_propagation_to_output)
  return _internal_mutable_allow_spmd_sharding_propagation_to_output();
}

// bytes fdo_profile = 14;
inline void ExecutableBuildOptionsProto::clear_fdo_profile() {
  _impl_.fdo_profile_.ClearToEmpty();
}
inline const std::string& ExecutableBuildOptionsProto::fdo_profile() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.fdo_profile)
  return _internal_fdo_profile();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExecutableBuildOptionsProto::set_fdo_profile(ArgT0&& arg0, ArgT... args) {
 
 _impl_.fdo_profile_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.fdo_profile)
}
inline std::string* ExecutableBuildOptionsProto::mutable_fdo_profile() {
  std::string* _s = _internal_mutable_fdo_profile();
  // @@protoc_insertion_point(field_mutable:xla.ExecutableBuildOptionsProto.fdo_profile)
  return _s;
}
inline const std::string& ExecutableBuildOptionsProto::_internal_fdo_profile() const {
  return _impl_.fdo_profile_.Get();
}
inline void ExecutableBuildOptionsProto::_internal_set_fdo_profile(const std::string& value) {
  
  _impl_.fdo_profile_.Set(value, GetArenaForAllocation());
}
inline std::string* ExecutableBuildOptionsProto::_internal_mutable_fdo_profile() {
  
  return _impl_.fdo_profile_.Mutable(GetArenaForAllocation());
}
inline std::string* ExecutableBuildOptionsProto::release_fdo_profile() {
  // @@protoc_insertion_point(field_release:xla.ExecutableBuildOptionsProto.fdo_profile)
  return _impl_.fdo_profile_.Release();
}
inline void ExecutableBuildOptionsProto::set_allocated_fdo_profile(std::string* fdo_profile) {
  if (fdo_profile != nullptr) {
    
  } else {
    
  }
  _impl_.fdo_profile_.SetAllocated(fdo_profile, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.fdo_profile_.IsDefault()) {
    _impl_.fdo_profile_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutableBuildOptionsProto.fdo_profile)
}

// int64 device_memory_size = 15;
inline void ExecutableBuildOptionsProto::clear_device_memory_size() {
  _impl_.device_memory_size_ = int64_t{0};
}
inline int64_t ExecutableBuildOptionsProto::_internal_device_memory_size() const {
  return _impl_.device_memory_size_;
}
inline int64_t ExecutableBuildOptionsProto::device_memory_size() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.device_memory_size)
  return _internal_device_memory_size();
}
inline void ExecutableBuildOptionsProto::_internal_set_device_memory_size(int64_t value) {
  
  _impl_.device_memory_size_ = value;
}
inline void ExecutableBuildOptionsProto::set_device_memory_size(int64_t value) {
  _internal_set_device_memory_size(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.device_memory_size)
}

// repeated int64 auto_spmd_partitioning_mesh_shape = 16;
inline int ExecutableBuildOptionsProto::_internal_auto_spmd_partitioning_mesh_shape_size() const {
  return _impl_.auto_spmd_partitioning_mesh_shape_.size();
}
inline int ExecutableBuildOptionsProto::auto_spmd_partitioning_mesh_shape_size() const {
  return _internal_auto_spmd_partitioning_mesh_shape_size();
}
inline void ExecutableBuildOptionsProto::clear_auto_spmd_partitioning_mesh_shape() {
  _impl_.auto_spmd_partitioning_mesh_shape_.Clear();
}
inline int64_t ExecutableBuildOptionsProto::_internal_auto_spmd_partitioning_mesh_shape(int index) const {
  return _impl_.auto_spmd_partitioning_mesh_shape_.Get(index);
}
inline int64_t ExecutableBuildOptionsProto::auto_spmd_partitioning_mesh_shape(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_shape)
  return _internal_auto_spmd_partitioning_mesh_shape(index);
}
inline void ExecutableBuildOptionsProto::set_auto_spmd_partitioning_mesh_shape(int index, int64_t value) {
  _impl_.auto_spmd_partitioning_mesh_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_shape)
}
inline void ExecutableBuildOptionsProto::_internal_add_auto_spmd_partitioning_mesh_shape(int64_t value) {
  _impl_.auto_spmd_partitioning_mesh_shape_.Add(value);
}
inline void ExecutableBuildOptionsProto::add_auto_spmd_partitioning_mesh_shape(int64_t value) {
  _internal_add_auto_spmd_partitioning_mesh_shape(value);
  // @@protoc_insertion_point(field_add:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ExecutableBuildOptionsProto::_internal_auto_spmd_partitioning_mesh_shape() const {
  return _impl_.auto_spmd_partitioning_mesh_shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ExecutableBuildOptionsProto::auto_spmd_partitioning_mesh_shape() const {
  // @@protoc_insertion_point(field_list:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_shape)
  return _internal_auto_spmd_partitioning_mesh_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ExecutableBuildOptionsProto::_internal_mutable_auto_spmd_partitioning_mesh_shape() {
  return &_impl_.auto_spmd_partitioning_mesh_shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ExecutableBuildOptionsProto::mutable_auto_spmd_partitioning_mesh_shape() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_shape)
  return _internal_mutable_auto_spmd_partitioning_mesh_shape();
}

// repeated int64 auto_spmd_partitioning_mesh_ids = 17;
inline int ExecutableBuildOptionsProto::_internal_auto_spmd_partitioning_mesh_ids_size() const {
  return _impl_.auto_spmd_partitioning_mesh_ids_.size();
}
inline int ExecutableBuildOptionsProto::auto_spmd_partitioning_mesh_ids_size() const {
  return _internal_auto_spmd_partitioning_mesh_ids_size();
}
inline void ExecutableBuildOptionsProto::clear_auto_spmd_partitioning_mesh_ids() {
  _impl_.auto_spmd_partitioning_mesh_ids_.Clear();
}
inline int64_t ExecutableBuildOptionsProto::_internal_auto_spmd_partitioning_mesh_ids(int index) const {
  return _impl_.auto_spmd_partitioning_mesh_ids_.Get(index);
}
inline int64_t ExecutableBuildOptionsProto::auto_spmd_partitioning_mesh_ids(int index) const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_ids)
  return _internal_auto_spmd_partitioning_mesh_ids(index);
}
inline void ExecutableBuildOptionsProto::set_auto_spmd_partitioning_mesh_ids(int index, int64_t value) {
  _impl_.auto_spmd_partitioning_mesh_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_ids)
}
inline void ExecutableBuildOptionsProto::_internal_add_auto_spmd_partitioning_mesh_ids(int64_t value) {
  _impl_.auto_spmd_partitioning_mesh_ids_.Add(value);
}
inline void ExecutableBuildOptionsProto::add_auto_spmd_partitioning_mesh_ids(int64_t value) {
  _internal_add_auto_spmd_partitioning_mesh_ids(value);
  // @@protoc_insertion_point(field_add:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ExecutableBuildOptionsProto::_internal_auto_spmd_partitioning_mesh_ids() const {
  return _impl_.auto_spmd_partitioning_mesh_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ExecutableBuildOptionsProto::auto_spmd_partitioning_mesh_ids() const {
  // @@protoc_insertion_point(field_list:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_ids)
  return _internal_auto_spmd_partitioning_mesh_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ExecutableBuildOptionsProto::_internal_mutable_auto_spmd_partitioning_mesh_ids() {
  return &_impl_.auto_spmd_partitioning_mesh_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ExecutableBuildOptionsProto::mutable_auto_spmd_partitioning_mesh_ids() {
  // @@protoc_insertion_point(field_mutable_list:xla.ExecutableBuildOptionsProto.auto_spmd_partitioning_mesh_ids)
  return _internal_mutable_auto_spmd_partitioning_mesh_ids();
}

// bool use_shardy_partitioner = 19;
inline void ExecutableBuildOptionsProto::clear_use_shardy_partitioner() {
  _impl_.use_shardy_partitioner_ = false;
}
inline bool ExecutableBuildOptionsProto::_internal_use_shardy_partitioner() const {
  return _impl_.use_shardy_partitioner_;
}
inline bool ExecutableBuildOptionsProto::use_shardy_partitioner() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.use_shardy_partitioner)
  return _internal_use_shardy_partitioner();
}
inline void ExecutableBuildOptionsProto::_internal_set_use_shardy_partitioner(bool value) {
  
  _impl_.use_shardy_partitioner_ = value;
}
inline void ExecutableBuildOptionsProto::set_use_shardy_partitioner(bool value) {
  _internal_set_use_shardy_partitioner(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.use_shardy_partitioner)
}

// int64 process_index = 22;
inline void ExecutableBuildOptionsProto::clear_process_index() {
  _impl_.process_index_ = int64_t{0};
}
inline int64_t ExecutableBuildOptionsProto::_internal_process_index() const {
  return _impl_.process_index_;
}
inline int64_t ExecutableBuildOptionsProto::process_index() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.process_index)
  return _internal_process_index();
}
inline void ExecutableBuildOptionsProto::_internal_set_process_index(int64_t value) {
  
  _impl_.process_index_ = value;
}
inline void ExecutableBuildOptionsProto::set_process_index(int64_t value) {
  _internal_set_process_index(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.process_index)
}

// int64 process_count = 23;
inline void ExecutableBuildOptionsProto::clear_process_count() {
  _impl_.process_count_ = int64_t{0};
}
inline int64_t ExecutableBuildOptionsProto::_internal_process_count() const {
  return _impl_.process_count_;
}
inline int64_t ExecutableBuildOptionsProto::process_count() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableBuildOptionsProto.process_count)
  return _internal_process_count();
}
inline void ExecutableBuildOptionsProto::_internal_set_process_count(int64_t value) {
  
  _impl_.process_count_ = value;
}
inline void ExecutableBuildOptionsProto::set_process_count(int64_t value) {
  _internal_set_process_count(value);
  // @@protoc_insertion_point(field_set:xla.ExecutableBuildOptionsProto.process_count)
}

// -------------------------------------------------------------------

// OptionOverrideProto

// string string_field = 1;
inline bool OptionOverrideProto::_internal_has_string_field() const {
  return value_case() == kStringField;
}
inline bool OptionOverrideProto::has_string_field() const {
  return _internal_has_string_field();
}
inline void OptionOverrideProto::set_has_string_field() {
  _impl_._oneof_case_[0] = kStringField;
}
inline void OptionOverrideProto::clear_string_field() {
  if (_internal_has_string_field()) {
    _impl_.value_.string_field_.Destroy();
    clear_has_value();
  }
}
inline const std::string& OptionOverrideProto::string_field() const {
  // @@protoc_insertion_point(field_get:xla.OptionOverrideProto.string_field)
  return _internal_string_field();
}
template <typename ArgT0, typename... ArgT>
inline void OptionOverrideProto::set_string_field(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_string_field()) {
    clear_value();
    set_has_string_field();
    _impl_.value_.string_field_.InitDefault();
  }
  _impl_.value_.string_field_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.OptionOverrideProto.string_field)
}
inline std::string* OptionOverrideProto::mutable_string_field() {
  std::string* _s = _internal_mutable_string_field();
  // @@protoc_insertion_point(field_mutable:xla.OptionOverrideProto.string_field)
  return _s;
}
inline const std::string& OptionOverrideProto::_internal_string_field() const {
  if (_internal_has_string_field()) {
    return _impl_.value_.string_field_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void OptionOverrideProto::_internal_set_string_field(const std::string& value) {
  if (!_internal_has_string_field()) {
    clear_value();
    set_has_string_field();
    _impl_.value_.string_field_.InitDefault();
  }
  _impl_.value_.string_field_.Set(value, GetArenaForAllocation());
}
inline std::string* OptionOverrideProto::_internal_mutable_string_field() {
  if (!_internal_has_string_field()) {
    clear_value();
    set_has_string_field();
    _impl_.value_.string_field_.InitDefault();
  }
  return _impl_.value_.string_field_.Mutable(      GetArenaForAllocation());
}
inline std::string* OptionOverrideProto::release_string_field() {
  // @@protoc_insertion_point(field_release:xla.OptionOverrideProto.string_field)
  if (_internal_has_string_field()) {
    clear_has_value();
    return _impl_.value_.string_field_.Release();
  } else {
    return nullptr;
  }
}
inline void OptionOverrideProto::set_allocated_string_field(std::string* string_field) {
  if (has_value()) {
    clear_value();
  }
  if (string_field != nullptr) {
    set_has_string_field();
    _impl_.value_.string_field_.InitAllocated(string_field, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:xla.OptionOverrideProto.string_field)
}

// bool bool_field = 2;
inline bool OptionOverrideProto::_internal_has_bool_field() const {
  return value_case() == kBoolField;
}
inline bool OptionOverrideProto::has_bool_field() const {
  return _internal_has_bool_field();
}
inline void OptionOverrideProto::set_has_bool_field() {
  _impl_._oneof_case_[0] = kBoolField;
}
inline void OptionOverrideProto::clear_bool_field() {
  if (_internal_has_bool_field()) {
    _impl_.value_.bool_field_ = false;
    clear_has_value();
  }
}
inline bool OptionOverrideProto::_internal_bool_field() const {
  if (_internal_has_bool_field()) {
    return _impl_.value_.bool_field_;
  }
  return false;
}
inline void OptionOverrideProto::_internal_set_bool_field(bool value) {
  if (!_internal_has_bool_field()) {
    clear_value();
    set_has_bool_field();
  }
  _impl_.value_.bool_field_ = value;
}
inline bool OptionOverrideProto::bool_field() const {
  // @@protoc_insertion_point(field_get:xla.OptionOverrideProto.bool_field)
  return _internal_bool_field();
}
inline void OptionOverrideProto::set_bool_field(bool value) {
  _internal_set_bool_field(value);
  // @@protoc_insertion_point(field_set:xla.OptionOverrideProto.bool_field)
}

// int64 int_field = 3;
inline bool OptionOverrideProto::_internal_has_int_field() const {
  return value_case() == kIntField;
}
inline bool OptionOverrideProto::has_int_field() const {
  return _internal_has_int_field();
}
inline void OptionOverrideProto::set_has_int_field() {
  _impl_._oneof_case_[0] = kIntField;
}
inline void OptionOverrideProto::clear_int_field() {
  if (_internal_has_int_field()) {
    _impl_.value_.int_field_ = int64_t{0};
    clear_has_value();
  }
}
inline int64_t OptionOverrideProto::_internal_int_field() const {
  if (_internal_has_int_field()) {
    return _impl_.value_.int_field_;
  }
  return int64_t{0};
}
inline void OptionOverrideProto::_internal_set_int_field(int64_t value) {
  if (!_internal_has_int_field()) {
    clear_value();
    set_has_int_field();
  }
  _impl_.value_.int_field_ = value;
}
inline int64_t OptionOverrideProto::int_field() const {
  // @@protoc_insertion_point(field_get:xla.OptionOverrideProto.int_field)
  return _internal_int_field();
}
inline void OptionOverrideProto::set_int_field(int64_t value) {
  _internal_set_int_field(value);
  // @@protoc_insertion_point(field_set:xla.OptionOverrideProto.int_field)
}

// double double_field = 4;
inline bool OptionOverrideProto::_internal_has_double_field() const {
  return value_case() == kDoubleField;
}
inline bool OptionOverrideProto::has_double_field() const {
  return _internal_has_double_field();
}
inline void OptionOverrideProto::set_has_double_field() {
  _impl_._oneof_case_[0] = kDoubleField;
}
inline void OptionOverrideProto::clear_double_field() {
  if (_internal_has_double_field()) {
    _impl_.value_.double_field_ = 0;
    clear_has_value();
  }
}
inline double OptionOverrideProto::_internal_double_field() const {
  if (_internal_has_double_field()) {
    return _impl_.value_.double_field_;
  }
  return 0;
}
inline void OptionOverrideProto::_internal_set_double_field(double value) {
  if (!_internal_has_double_field()) {
    clear_value();
    set_has_double_field();
  }
  _impl_.value_.double_field_ = value;
}
inline double OptionOverrideProto::double_field() const {
  // @@protoc_insertion_point(field_get:xla.OptionOverrideProto.double_field)
  return _internal_double_field();
}
inline void OptionOverrideProto::set_double_field(double value) {
  _internal_set_double_field(value);
  // @@protoc_insertion_point(field_set:xla.OptionOverrideProto.double_field)
}

inline bool OptionOverrideProto::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void OptionOverrideProto::clear_has_value() {
  _impl_._oneof_case_[0] = VALUE_NOT_SET;
}
inline OptionOverrideProto::ValueCase OptionOverrideProto::value_case() const {
  return OptionOverrideProto::ValueCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CompileOptionsProto

// repeated .xla.ShapeProto argument_layouts = 1;
inline int CompileOptionsProto::_internal_argument_layouts_size() const {
  return _impl_.argument_layouts_.size();
}
inline int CompileOptionsProto::argument_layouts_size() const {
  return _internal_argument_layouts_size();
}
inline ::xla::ShapeProto* CompileOptionsProto::mutable_argument_layouts(int index) {
  // @@protoc_insertion_point(field_mutable:xla.CompileOptionsProto.argument_layouts)
  return _impl_.argument_layouts_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >*
CompileOptionsProto::mutable_argument_layouts() {
  // @@protoc_insertion_point(field_mutable_list:xla.CompileOptionsProto.argument_layouts)
  return &_impl_.argument_layouts_;
}
inline const ::xla::ShapeProto& CompileOptionsProto::_internal_argument_layouts(int index) const {
  return _impl_.argument_layouts_.Get(index);
}
inline const ::xla::ShapeProto& CompileOptionsProto::argument_layouts(int index) const {
  // @@protoc_insertion_point(field_get:xla.CompileOptionsProto.argument_layouts)
  return _internal_argument_layouts(index);
}
inline ::xla::ShapeProto* CompileOptionsProto::_internal_add_argument_layouts() {
  return _impl_.argument_layouts_.Add();
}
inline ::xla::ShapeProto* CompileOptionsProto::add_argument_layouts() {
  ::xla::ShapeProto* _add = _internal_add_argument_layouts();
  // @@protoc_insertion_point(field_add:xla.CompileOptionsProto.argument_layouts)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::ShapeProto >&
CompileOptionsProto::argument_layouts() const {
  // @@protoc_insertion_point(field_list:xla.CompileOptionsProto.argument_layouts)
  return _impl_.argument_layouts_;
}

// bool parameter_is_tupled_arguments = 2;
inline void CompileOptionsProto::clear_parameter_is_tupled_arguments() {
  _impl_.parameter_is_tupled_arguments_ = false;
}
inline bool CompileOptionsProto::_internal_parameter_is_tupled_arguments() const {
  return _impl_.parameter_is_tupled_arguments_;
}
inline bool CompileOptionsProto::parameter_is_tupled_arguments() const {
  // @@protoc_insertion_point(field_get:xla.CompileOptionsProto.parameter_is_tupled_arguments)
  return _internal_parameter_is_tupled_arguments();
}
inline void CompileOptionsProto::_internal_set_parameter_is_tupled_arguments(bool value) {
  
  _impl_.parameter_is_tupled_arguments_ = value;
}
inline void CompileOptionsProto::set_parameter_is_tupled_arguments(bool value) {
  _internal_set_parameter_is_tupled_arguments(value);
  // @@protoc_insertion_point(field_set:xla.CompileOptionsProto.parameter_is_tupled_arguments)
}

// .xla.ExecutableBuildOptionsProto executable_build_options = 3;
inline bool CompileOptionsProto::_internal_has_executable_build_options() const {
  return this != internal_default_instance() && _impl_.executable_build_options_ != nullptr;
}
inline bool CompileOptionsProto::has_executable_build_options() const {
  return _internal_has_executable_build_options();
}
inline void CompileOptionsProto::clear_executable_build_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.executable_build_options_ != nullptr) {
    delete _impl_.executable_build_options_;
  }
  _impl_.executable_build_options_ = nullptr;
}
inline const ::xla::ExecutableBuildOptionsProto& CompileOptionsProto::_internal_executable_build_options() const {
  const ::xla::ExecutableBuildOptionsProto* p = _impl_.executable_build_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::ExecutableBuildOptionsProto&>(
      ::xla::_ExecutableBuildOptionsProto_default_instance_);
}
inline const ::xla::ExecutableBuildOptionsProto& CompileOptionsProto::executable_build_options() const {
  // @@protoc_insertion_point(field_get:xla.CompileOptionsProto.executable_build_options)
  return _internal_executable_build_options();
}
inline void CompileOptionsProto::unsafe_arena_set_allocated_executable_build_options(
    ::xla::ExecutableBuildOptionsProto* executable_build_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.executable_build_options_);
  }
  _impl_.executable_build_options_ = executable_build_options;
  if (executable_build_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.CompileOptionsProto.executable_build_options)
}
inline ::xla::ExecutableBuildOptionsProto* CompileOptionsProto::release_executable_build_options() {
  
  ::xla::ExecutableBuildOptionsProto* temp = _impl_.executable_build_options_;
  _impl_.executable_build_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::ExecutableBuildOptionsProto* CompileOptionsProto::unsafe_arena_release_executable_build_options() {
  // @@protoc_insertion_point(field_release:xla.CompileOptionsProto.executable_build_options)
  
  ::xla::ExecutableBuildOptionsProto* temp = _impl_.executable_build_options_;
  _impl_.executable_build_options_ = nullptr;
  return temp;
}
inline ::xla::ExecutableBuildOptionsProto* CompileOptionsProto::_internal_mutable_executable_build_options() {
  
  if (_impl_.executable_build_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ExecutableBuildOptionsProto>(GetArenaForAllocation());
    _impl_.executable_build_options_ = p;
  }
  return _impl_.executable_build_options_;
}
inline ::xla::ExecutableBuildOptionsProto* CompileOptionsProto::mutable_executable_build_options() {
  ::xla::ExecutableBuildOptionsProto* _msg = _internal_mutable_executable_build_options();
  // @@protoc_insertion_point(field_mutable:xla.CompileOptionsProto.executable_build_options)
  return _msg;
}
inline void CompileOptionsProto::set_allocated_executable_build_options(::xla::ExecutableBuildOptionsProto* executable_build_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.executable_build_options_;
  }
  if (executable_build_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(executable_build_options);
    if (message_arena != submessage_arena) {
      executable_build_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, executable_build_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.executable_build_options_ = executable_build_options;
  // @@protoc_insertion_point(field_set_allocated:xla.CompileOptionsProto.executable_build_options)
}

// bool compile_portable_executable = 4;
inline void CompileOptionsProto::clear_compile_portable_executable() {
  _impl_.compile_portable_executable_ = false;
}
inline bool CompileOptionsProto::_internal_compile_portable_executable() const {
  return _impl_.compile_portable_executable_;
}
inline bool CompileOptionsProto::compile_portable_executable() const {
  // @@protoc_insertion_point(field_get:xla.CompileOptionsProto.compile_portable_executable)
  return _internal_compile_portable_executable();
}
inline void CompileOptionsProto::_internal_set_compile_portable_executable(bool value) {
  
  _impl_.compile_portable_executable_ = value;
}
inline void CompileOptionsProto::set_compile_portable_executable(bool value) {
  _internal_set_compile_portable_executable(value);
  // @@protoc_insertion_point(field_set:xla.CompileOptionsProto.compile_portable_executable)
}

// int64 profile_version = 5;
inline void CompileOptionsProto::clear_profile_version() {
  _impl_.profile_version_ = int64_t{0};
}
inline int64_t CompileOptionsProto::_internal_profile_version() const {
  return _impl_.profile_version_;
}
inline int64_t CompileOptionsProto::profile_version() const {
  // @@protoc_insertion_point(field_get:xla.CompileOptionsProto.profile_version)
  return _internal_profile_version();
}
inline void CompileOptionsProto::_internal_set_profile_version(int64_t value) {
  
  _impl_.profile_version_ = value;
}
inline void CompileOptionsProto::set_profile_version(int64_t value) {
  _internal_set_profile_version(value);
  // @@protoc_insertion_point(field_set:xla.CompileOptionsProto.profile_version)
}

// bytes serialized_multi_slice_config = 6;
inline void CompileOptionsProto::clear_serialized_multi_slice_config() {
  _impl_.serialized_multi_slice_config_.ClearToEmpty();
}
inline const std::string& CompileOptionsProto::serialized_multi_slice_config() const {
  // @@protoc_insertion_point(field_get:xla.CompileOptionsProto.serialized_multi_slice_config)
  return _internal_serialized_multi_slice_config();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompileOptionsProto::set_serialized_multi_slice_config(ArgT0&& arg0, ArgT... args) {
 
 _impl_.serialized_multi_slice_config_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.CompileOptionsProto.serialized_multi_slice_config)
}
inline std::string* CompileOptionsProto::mutable_serialized_multi_slice_config() {
  std::string* _s = _internal_mutable_serialized_multi_slice_config();
  // @@protoc_insertion_point(field_mutable:xla.CompileOptionsProto.serialized_multi_slice_config)
  return _s;
}
inline const std::string& CompileOptionsProto::_internal_serialized_multi_slice_config() const {
  return _impl_.serialized_multi_slice_config_.Get();
}
inline void CompileOptionsProto::_internal_set_serialized_multi_slice_config(const std::string& value) {
  
  _impl_.serialized_multi_slice_config_.Set(value, GetArenaForAllocation());
}
inline std::string* CompileOptionsProto::_internal_mutable_serialized_multi_slice_config() {
  
  return _impl_.serialized_multi_slice_config_.Mutable(GetArenaForAllocation());
}
inline std::string* CompileOptionsProto::release_serialized_multi_slice_config() {
  // @@protoc_insertion_point(field_release:xla.CompileOptionsProto.serialized_multi_slice_config)
  return _impl_.serialized_multi_slice_config_.Release();
}
inline void CompileOptionsProto::set_allocated_serialized_multi_slice_config(std::string* serialized_multi_slice_config) {
  if (serialized_multi_slice_config != nullptr) {
    
  } else {
    
  }
  _impl_.serialized_multi_slice_config_.SetAllocated(serialized_multi_slice_config, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.serialized_multi_slice_config_.IsDefault()) {
    _impl_.serialized_multi_slice_config_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.CompileOptionsProto.serialized_multi_slice_config)
}

// map<string, .xla.OptionOverrideProto> env_option_overrides = 7;
inline int CompileOptionsProto::_internal_env_option_overrides_size() const {
  return _impl_.env_option_overrides_.size();
}
inline int CompileOptionsProto::env_option_overrides_size() const {
  return _internal_env_option_overrides_size();
}
inline void CompileOptionsProto::clear_env_option_overrides() {
  _impl_.env_option_overrides_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::OptionOverrideProto >&
CompileOptionsProto::_internal_env_option_overrides() const {
  return _impl_.env_option_overrides_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::OptionOverrideProto >&
CompileOptionsProto::env_option_overrides() const {
  // @@protoc_insertion_point(field_map:xla.CompileOptionsProto.env_option_overrides)
  return _internal_env_option_overrides();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::OptionOverrideProto >*
CompileOptionsProto::_internal_mutable_env_option_overrides() {
  return _impl_.env_option_overrides_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::OptionOverrideProto >*
CompileOptionsProto::mutable_env_option_overrides() {
  // @@protoc_insertion_point(field_mutable_map:xla.CompileOptionsProto.env_option_overrides)
  return _internal_mutable_env_option_overrides();
}

// .stream_executor.GpuTargetConfigProto target_config = 8;
inline bool CompileOptionsProto::_internal_has_target_config() const {
  return this != internal_default_instance() && _impl_.target_config_ != nullptr;
}
inline bool CompileOptionsProto::has_target_config() const {
  return _internal_has_target_config();
}
inline const ::stream_executor::GpuTargetConfigProto& CompileOptionsProto::_internal_target_config() const {
  const ::stream_executor::GpuTargetConfigProto* p = _impl_.target_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::stream_executor::GpuTargetConfigProto&>(
      ::stream_executor::_GpuTargetConfigProto_default_instance_);
}
inline const ::stream_executor::GpuTargetConfigProto& CompileOptionsProto::target_config() const {
  // @@protoc_insertion_point(field_get:xla.CompileOptionsProto.target_config)
  return _internal_target_config();
}
inline void CompileOptionsProto::unsafe_arena_set_allocated_target_config(
    ::stream_executor::GpuTargetConfigProto* target_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.target_config_);
  }
  _impl_.target_config_ = target_config;
  if (target_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.CompileOptionsProto.target_config)
}
inline ::stream_executor::GpuTargetConfigProto* CompileOptionsProto::release_target_config() {
  
  ::stream_executor::GpuTargetConfigProto* temp = _impl_.target_config_;
  _impl_.target_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stream_executor::GpuTargetConfigProto* CompileOptionsProto::unsafe_arena_release_target_config() {
  // @@protoc_insertion_point(field_release:xla.CompileOptionsProto.target_config)
  
  ::stream_executor::GpuTargetConfigProto* temp = _impl_.target_config_;
  _impl_.target_config_ = nullptr;
  return temp;
}
inline ::stream_executor::GpuTargetConfigProto* CompileOptionsProto::_internal_mutable_target_config() {
  
  if (_impl_.target_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::stream_executor::GpuTargetConfigProto>(GetArenaForAllocation());
    _impl_.target_config_ = p;
  }
  return _impl_.target_config_;
}
inline ::stream_executor::GpuTargetConfigProto* CompileOptionsProto::mutable_target_config() {
  ::stream_executor::GpuTargetConfigProto* _msg = _internal_mutable_target_config();
  // @@protoc_insertion_point(field_mutable:xla.CompileOptionsProto.target_config)
  return _msg;
}
inline void CompileOptionsProto::set_allocated_target_config(::stream_executor::GpuTargetConfigProto* target_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.target_config_);
  }
  if (target_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(target_config));
    if (message_arena != submessage_arena) {
      target_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.target_config_ = target_config;
  // @@protoc_insertion_point(field_set_allocated:xla.CompileOptionsProto.target_config)
}

// -------------------------------------------------------------------

// ExecutableAndOptionsProto

// bytes serialized_executable = 1;
inline void ExecutableAndOptionsProto::clear_serialized_executable() {
  _impl_.serialized_executable_.ClearToEmpty();
}
inline const std::string& ExecutableAndOptionsProto::serialized_executable() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableAndOptionsProto.serialized_executable)
  return _internal_serialized_executable();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExecutableAndOptionsProto::set_serialized_executable(ArgT0&& arg0, ArgT... args) {
 
 _impl_.serialized_executable_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.ExecutableAndOptionsProto.serialized_executable)
}
inline std::string* ExecutableAndOptionsProto::mutable_serialized_executable() {
  std::string* _s = _internal_mutable_serialized_executable();
  // @@protoc_insertion_point(field_mutable:xla.ExecutableAndOptionsProto.serialized_executable)
  return _s;
}
inline const std::string& ExecutableAndOptionsProto::_internal_serialized_executable() const {
  return _impl_.serialized_executable_.Get();
}
inline void ExecutableAndOptionsProto::_internal_set_serialized_executable(const std::string& value) {
  
  _impl_.serialized_executable_.Set(value, GetArenaForAllocation());
}
inline std::string* ExecutableAndOptionsProto::_internal_mutable_serialized_executable() {
  
  return _impl_.serialized_executable_.Mutable(GetArenaForAllocation());
}
inline std::string* ExecutableAndOptionsProto::release_serialized_executable() {
  // @@protoc_insertion_point(field_release:xla.ExecutableAndOptionsProto.serialized_executable)
  return _impl_.serialized_executable_.Release();
}
inline void ExecutableAndOptionsProto::set_allocated_serialized_executable(std::string* serialized_executable) {
  if (serialized_executable != nullptr) {
    
  } else {
    
  }
  _impl_.serialized_executable_.SetAllocated(serialized_executable, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.serialized_executable_.IsDefault()) {
    _impl_.serialized_executable_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutableAndOptionsProto.serialized_executable)
}

// .xla.CompileOptionsProto compile_options = 2;
inline bool ExecutableAndOptionsProto::_internal_has_compile_options() const {
  return this != internal_default_instance() && _impl_.compile_options_ != nullptr;
}
inline bool ExecutableAndOptionsProto::has_compile_options() const {
  return _internal_has_compile_options();
}
inline void ExecutableAndOptionsProto::clear_compile_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.compile_options_ != nullptr) {
    delete _impl_.compile_options_;
  }
  _impl_.compile_options_ = nullptr;
}
inline const ::xla::CompileOptionsProto& ExecutableAndOptionsProto::_internal_compile_options() const {
  const ::xla::CompileOptionsProto* p = _impl_.compile_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::CompileOptionsProto&>(
      ::xla::_CompileOptionsProto_default_instance_);
}
inline const ::xla::CompileOptionsProto& ExecutableAndOptionsProto::compile_options() const {
  // @@protoc_insertion_point(field_get:xla.ExecutableAndOptionsProto.compile_options)
  return _internal_compile_options();
}
inline void ExecutableAndOptionsProto::unsafe_arena_set_allocated_compile_options(
    ::xla::CompileOptionsProto* compile_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.compile_options_);
  }
  _impl_.compile_options_ = compile_options;
  if (compile_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.ExecutableAndOptionsProto.compile_options)
}
inline ::xla::CompileOptionsProto* ExecutableAndOptionsProto::release_compile_options() {
  
  ::xla::CompileOptionsProto* temp = _impl_.compile_options_;
  _impl_.compile_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::CompileOptionsProto* ExecutableAndOptionsProto::unsafe_arena_release_compile_options() {
  // @@protoc_insertion_point(field_release:xla.ExecutableAndOptionsProto.compile_options)
  
  ::xla::CompileOptionsProto* temp = _impl_.compile_options_;
  _impl_.compile_options_ = nullptr;
  return temp;
}
inline ::xla::CompileOptionsProto* ExecutableAndOptionsProto::_internal_mutable_compile_options() {
  
  if (_impl_.compile_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::CompileOptionsProto>(GetArenaForAllocation());
    _impl_.compile_options_ = p;
  }
  return _impl_.compile_options_;
}
inline ::xla::CompileOptionsProto* ExecutableAndOptionsProto::mutable_compile_options() {
  ::xla::CompileOptionsProto* _msg = _internal_mutable_compile_options();
  // @@protoc_insertion_point(field_mutable:xla.ExecutableAndOptionsProto.compile_options)
  return _msg;
}
inline void ExecutableAndOptionsProto::set_allocated_compile_options(::xla::CompileOptionsProto* compile_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.compile_options_;
  }
  if (compile_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(compile_options);
    if (message_arena != submessage_arena) {
      compile_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, compile_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.compile_options_ = compile_options;
  // @@protoc_insertion_point(field_set_allocated:xla.ExecutableAndOptionsProto.compile_options)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fcompile_5foptions_2eproto
