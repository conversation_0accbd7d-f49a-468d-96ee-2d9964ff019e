.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_challenge_password" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_challenge_password \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_challenge_password(gnutls_x509_crq_t " crq ", char * " pass ", size_t * " pass_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "char * pass" 12
will hold a (0)\-terminated password string
.IP "size_t * pass_size" 12
Initially holds the size of  \fIpass\fP .
.SH "DESCRIPTION"
This function will return the challenge password in the request.
The challenge password is intended to be used for requesting a
revocation of the certificate.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
