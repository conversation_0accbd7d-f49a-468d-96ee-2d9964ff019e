#!/usr/bin/env python3
"""
下载完整的MinGW-w64工具链
"""

import os
import sys
import urllib.request
import zipfile
import shutil
from pathlib import Path

def download_file(url, filename):
    """下载文件并显示进度"""
    print(f"正在下载: {url}")
    print(f"保存到: {filename}")
    
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, downloaded * 100 / total_size)
            print(f"\r下载进度: {percent:.1f}% ({downloaded:,}/{total_size:,} 字节)", end='')
        else:
            print(f"\r已下载: {downloaded:,} 字节", end='')
    
    try:
        urllib.request.urlretrieve(url, filename, progress_hook)
        print("\n✓ 下载完成")
        return True
    except Exception as e:
        print(f"\n✗ 下载失败: {e}")
        return False

def extract_zip(filename, extract_to):
    """解压ZIP文件"""
    try:
        print(f"正在解压: {filename}")
        with zipfile.ZipFile(filename, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print("✓ 解压完成")
        return True
    except Exception as e:
        print(f"✗ 解压失败: {e}")
        return False

def main():
    print("=" * 60)
    print("下载完整的MinGW-w64工具链")
    print("=" * 60)
    
    # 使用WinLibs的MinGW-w64发行版
    mingw_url = "https://github.com/brechtsanders/winlibs_mingw/releases/download/13.2.0-16.0.6-11.0.0-msvcrt-r1/winlibs-x86_64-posix-seh-gcc-13.2.0-mingw-w64msvcrt-11.0.0-r1.zip"
    mingw_filename = "winlibs-mingw64.zip"
    
    # 创建目录
    mingw_dir = Path("env/mingw64_complete")
    mingw_dir.mkdir(parents=True, exist_ok=True)
    
    # 下载MinGW
    if not os.path.exists(mingw_filename):
        print("下载完整的MinGW-w64工具链...")
        if not download_file(mingw_url, mingw_filename):
            print("尝试备用下载地址...")
            # 备用地址
            backup_url = "https://sourceforge.net/projects/mingw-w64/files/Toolchains%20targetting%20Win64/Personal%20Builds/mingw-builds/8.1.0/threads-posix/seh/x86_64-8.1.0-release-posix-seh-rt_v6-rev0.7z/download"
            mingw_filename = "mingw64-backup.7z"
            if not download_file(backup_url, mingw_filename):
                return 1
    
    # 解压
    if mingw_filename.endswith('.zip'):
        if extract_zip(mingw_filename, str(mingw_dir)):
            print(f"✓ MinGW-w64已安装到: {mingw_dir}")
        else:
            return 1
    else:
        print("需要7zip来解压.7z文件，请手动解压")
        return 1
    
    # 查找实际的mingw64目录
    extracted_dirs = list(mingw_dir.glob("mingw64"))
    if not extracted_dirs:
        # 可能解压到了子目录
        for subdir in mingw_dir.iterdir():
            if subdir.is_dir():
                mingw64_path = subdir / "mingw64"
                if mingw64_path.exists():
                    extracted_dirs = [mingw64_path]
                    break
    
    if extracted_dirs:
        actual_mingw_path = extracted_dirs[0]
        print(f"找到MinGW64目录: {actual_mingw_path}")
        
        # 检查关键文件
        gcc_path = actual_mingw_path / "bin" / "gcc.exe"
        include_path = actual_mingw_path / "include"
        
        if gcc_path.exists():
            print("✓ 找到gcc.exe")
        if include_path.exists():
            print("✓ 找到include目录")
            # 检查标准头文件
            if (include_path / "stdio.h").exists():
                print("✓ 找到标准C头文件")
            else:
                print("✗ 缺少标准C头文件")
        
        print(f"\n编译器路径: {gcc_path}")
        print(f"包含目录: {include_path}")
        
        return 0
    else:
        print("✗ 未找到mingw64目录")
        return 1

if __name__ == "__main__":
    sys.exit(main())
