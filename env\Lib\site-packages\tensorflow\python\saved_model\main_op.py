# Copyright 2015 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""SavedModel main op.

Builds a main op that defines the sequence of ops to be run as part of the
SavedModel load/restore operations.
"""

# pylint: disable=unused-import
from tensorflow.python.saved_model.main_op_impl import main_op
from tensorflow.python.saved_model.main_op_impl import main_op_with_restore
# pylint: enable=unused-import
