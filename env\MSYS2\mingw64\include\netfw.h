/*** Autogenerated by WIDL 10.8 from include/netfw.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __netfw_h__
#define __netfw_h__

/* Forward declarations */

#ifndef __INetFwIcmpSettings_FWD_DEFINED__
#define __INetFwIcmpSettings_FWD_DEFINED__
typedef interface INetFwIcmpSettings INetFwIcmpSettings;
#ifdef __cplusplus
interface INetFwIcmpSettings;
#endif /* __cplusplus */
#endif

#ifndef __INetFwOpenPort_FWD_DEFINED__
#define __INetFwOpenPort_FWD_DEFINED__
typedef interface INetFwOpenPort INetFwOpenPort;
#ifdef __cplusplus
interface INetFwOpenPort;
#endif /* __cplusplus */
#endif

#ifndef __INetFwOpenPorts_FWD_DEFINED__
#define __INetFwOpenPorts_FWD_DEFINED__
typedef interface INetFwOpenPorts INetFwOpenPorts;
#ifdef __cplusplus
interface INetFwOpenPorts;
#endif /* __cplusplus */
#endif

#ifndef __INetFwService_FWD_DEFINED__
#define __INetFwService_FWD_DEFINED__
typedef interface INetFwService INetFwService;
#ifdef __cplusplus
interface INetFwService;
#endif /* __cplusplus */
#endif

#ifndef __INetFwServices_FWD_DEFINED__
#define __INetFwServices_FWD_DEFINED__
typedef interface INetFwServices INetFwServices;
#ifdef __cplusplus
interface INetFwServices;
#endif /* __cplusplus */
#endif

#ifndef __INetFwAuthorizedApplication_FWD_DEFINED__
#define __INetFwAuthorizedApplication_FWD_DEFINED__
typedef interface INetFwAuthorizedApplication INetFwAuthorizedApplication;
#ifdef __cplusplus
interface INetFwAuthorizedApplication;
#endif /* __cplusplus */
#endif

#ifndef __INetFwRemoteAdminSettings_FWD_DEFINED__
#define __INetFwRemoteAdminSettings_FWD_DEFINED__
typedef interface INetFwRemoteAdminSettings INetFwRemoteAdminSettings;
#ifdef __cplusplus
interface INetFwRemoteAdminSettings;
#endif /* __cplusplus */
#endif

#ifndef __INetFwAuthorizedApplications_FWD_DEFINED__
#define __INetFwAuthorizedApplications_FWD_DEFINED__
typedef interface INetFwAuthorizedApplications INetFwAuthorizedApplications;
#ifdef __cplusplus
interface INetFwAuthorizedApplications;
#endif /* __cplusplus */
#endif

#ifndef __INetFwRule_FWD_DEFINED__
#define __INetFwRule_FWD_DEFINED__
typedef interface INetFwRule INetFwRule;
#ifdef __cplusplus
interface INetFwRule;
#endif /* __cplusplus */
#endif

#ifndef __INetFwRule2_FWD_DEFINED__
#define __INetFwRule2_FWD_DEFINED__
typedef interface INetFwRule2 INetFwRule2;
#ifdef __cplusplus
interface INetFwRule2;
#endif /* __cplusplus */
#endif

#ifndef __INetFwRule3_FWD_DEFINED__
#define __INetFwRule3_FWD_DEFINED__
typedef interface INetFwRule3 INetFwRule3;
#ifdef __cplusplus
interface INetFwRule3;
#endif /* __cplusplus */
#endif

#ifndef __INetFwRules_FWD_DEFINED__
#define __INetFwRules_FWD_DEFINED__
typedef interface INetFwRules INetFwRules;
#ifdef __cplusplus
interface INetFwRules;
#endif /* __cplusplus */
#endif

#ifndef __INetFwServiceRestriction_FWD_DEFINED__
#define __INetFwServiceRestriction_FWD_DEFINED__
typedef interface INetFwServiceRestriction INetFwServiceRestriction;
#ifdef __cplusplus
interface INetFwServiceRestriction;
#endif /* __cplusplus */
#endif

#ifndef __INetFwProfile_FWD_DEFINED__
#define __INetFwProfile_FWD_DEFINED__
typedef interface INetFwProfile INetFwProfile;
#ifdef __cplusplus
interface INetFwProfile;
#endif /* __cplusplus */
#endif

#ifndef __INetFwPolicy_FWD_DEFINED__
#define __INetFwPolicy_FWD_DEFINED__
typedef interface INetFwPolicy INetFwPolicy;
#ifdef __cplusplus
interface INetFwPolicy;
#endif /* __cplusplus */
#endif

#ifndef __INetFwPolicy2_FWD_DEFINED__
#define __INetFwPolicy2_FWD_DEFINED__
typedef interface INetFwPolicy2 INetFwPolicy2;
#ifdef __cplusplus
interface INetFwPolicy2;
#endif /* __cplusplus */
#endif

#ifndef __INetFwMgr_FWD_DEFINED__
#define __INetFwMgr_FWD_DEFINED__
typedef interface INetFwMgr INetFwMgr;
#ifdef __cplusplus
interface INetFwMgr;
#endif /* __cplusplus */
#endif

#ifndef __INetFwProduct_FWD_DEFINED__
#define __INetFwProduct_FWD_DEFINED__
typedef interface INetFwProduct INetFwProduct;
#ifdef __cplusplus
interface INetFwProduct;
#endif /* __cplusplus */
#endif

#ifndef __INetFwProducts_FWD_DEFINED__
#define __INetFwProducts_FWD_DEFINED__
typedef interface INetFwProducts INetFwProducts;
#ifdef __cplusplus
interface INetFwProducts;
#endif /* __cplusplus */
#endif

#ifndef __NetFwOpenPort_FWD_DEFINED__
#define __NetFwOpenPort_FWD_DEFINED__
#ifdef __cplusplus
typedef class NetFwOpenPort NetFwOpenPort;
#else
typedef struct NetFwOpenPort NetFwOpenPort;
#endif /* defined __cplusplus */
#endif /* defined __NetFwOpenPort_FWD_DEFINED__ */

#ifndef __NetFwAuthorizedApplication_FWD_DEFINED__
#define __NetFwAuthorizedApplication_FWD_DEFINED__
#ifdef __cplusplus
typedef class NetFwAuthorizedApplication NetFwAuthorizedApplication;
#else
typedef struct NetFwAuthorizedApplication NetFwAuthorizedApplication;
#endif /* defined __cplusplus */
#endif /* defined __NetFwAuthorizedApplication_FWD_DEFINED__ */

#ifndef __NetFwMgr_FWD_DEFINED__
#define __NetFwMgr_FWD_DEFINED__
#ifdef __cplusplus
typedef class NetFwMgr NetFwMgr;
#else
typedef struct NetFwMgr NetFwMgr;
#endif /* defined __cplusplus */
#endif /* defined __NetFwMgr_FWD_DEFINED__ */

#ifndef __NetFwPolicy2_FWD_DEFINED__
#define __NetFwPolicy2_FWD_DEFINED__
#ifdef __cplusplus
typedef class NetFwPolicy2 NetFwPolicy2;
#else
typedef struct NetFwPolicy2 NetFwPolicy2;
#endif /* defined __cplusplus */
#endif /* defined __NetFwPolicy2_FWD_DEFINED__ */

#ifndef __NetFwRule_FWD_DEFINED__
#define __NetFwRule_FWD_DEFINED__
#ifdef __cplusplus
typedef class NetFwRule NetFwRule;
#else
typedef struct NetFwRule NetFwRule;
#endif /* defined __cplusplus */
#endif /* defined __NetFwRule_FWD_DEFINED__ */

#ifndef __NetFwProduct_FWD_DEFINED__
#define __NetFwProduct_FWD_DEFINED__
#ifdef __cplusplus
typedef class NetFwProduct NetFwProduct;
#else
typedef struct NetFwProduct NetFwProduct;
#endif /* defined __cplusplus */
#endif /* defined __NetFwProduct_FWD_DEFINED__ */

#ifndef __NetFwProducts_FWD_DEFINED__
#define __NetFwProducts_FWD_DEFINED__
#ifdef __cplusplus
typedef class NetFwProducts NetFwProducts;
#else
typedef struct NetFwProducts NetFwProducts;
#endif /* defined __cplusplus */
#endif /* defined __NetFwProducts_FWD_DEFINED__ */

/* Headers for imported files */

#include <icftypes.h>
#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************************
 * INetFwIcmpSettings interface
 */
#ifndef __INetFwIcmpSettings_INTERFACE_DEFINED__
#define __INetFwIcmpSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwIcmpSettings, 0xa6207b2e, 0x7cdd, 0x426a, 0x95,0x1e, 0x5e,0x1c,0xbc,0x5a,0xfe,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a6207b2e-7cdd-426a-951e-5e1cbc5afead")
INetFwIcmpSettings : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_AllowOutboundDestinationUnreachable(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowOutboundDestinationUnreachable(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowRedirect(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowRedirect(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowInboundEchoRequest(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowInboundEchoRequest(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowOutboundTimeExceeded(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowOutboundTimeExceeded(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowOutboundParameterProblem(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowOutboundParameterProblem(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowOutboundSourceQuench(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowOutboundSourceQuench(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowInboundRouterRequest(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowInboundRouterRequest(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowInboundTimestampRequest(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowInboundTimestampRequest(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowInboundMaskRequest(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowInboundMaskRequest(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowOutboundPacketTooBig(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowOutboundPacketTooBig(
        VARIANT_BOOL allow) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwIcmpSettings, 0xa6207b2e, 0x7cdd, 0x426a, 0x95,0x1e, 0x5e,0x1c,0xbc,0x5a,0xfe,0xad)
#endif
#else
typedef struct INetFwIcmpSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwIcmpSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwIcmpSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwIcmpSettings *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwIcmpSettings *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwIcmpSettings *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwIcmpSettings *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwIcmpSettings *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwIcmpSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AllowOutboundDestinationUnreachable)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowOutboundDestinationUnreachable)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowRedirect)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowRedirect)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowInboundEchoRequest)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowInboundEchoRequest)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowOutboundTimeExceeded)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowOutboundTimeExceeded)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowOutboundParameterProblem)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowOutboundParameterProblem)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowOutboundSourceQuench)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowOutboundSourceQuench)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowInboundRouterRequest)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowInboundRouterRequest)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowInboundTimestampRequest)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowInboundTimestampRequest)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowInboundMaskRequest)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowInboundMaskRequest)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_AllowOutboundPacketTooBig)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowOutboundPacketTooBig)(
        INetFwIcmpSettings *This,
        VARIANT_BOOL allow);

    END_INTERFACE
} INetFwIcmpSettingsVtbl;

interface INetFwIcmpSettings {
    CONST_VTBL INetFwIcmpSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwIcmpSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwIcmpSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwIcmpSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwIcmpSettings_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwIcmpSettings_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwIcmpSettings_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwIcmpSettings_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwIcmpSettings methods ***/
#define INetFwIcmpSettings_get_AllowOutboundDestinationUnreachable(This,allow) (This)->lpVtbl->get_AllowOutboundDestinationUnreachable(This,allow)
#define INetFwIcmpSettings_put_AllowOutboundDestinationUnreachable(This,allow) (This)->lpVtbl->put_AllowOutboundDestinationUnreachable(This,allow)
#define INetFwIcmpSettings_get_AllowRedirect(This,allow) (This)->lpVtbl->get_AllowRedirect(This,allow)
#define INetFwIcmpSettings_put_AllowRedirect(This,allow) (This)->lpVtbl->put_AllowRedirect(This,allow)
#define INetFwIcmpSettings_get_AllowInboundEchoRequest(This,allow) (This)->lpVtbl->get_AllowInboundEchoRequest(This,allow)
#define INetFwIcmpSettings_put_AllowInboundEchoRequest(This,allow) (This)->lpVtbl->put_AllowInboundEchoRequest(This,allow)
#define INetFwIcmpSettings_get_AllowOutboundTimeExceeded(This,allow) (This)->lpVtbl->get_AllowOutboundTimeExceeded(This,allow)
#define INetFwIcmpSettings_put_AllowOutboundTimeExceeded(This,allow) (This)->lpVtbl->put_AllowOutboundTimeExceeded(This,allow)
#define INetFwIcmpSettings_get_AllowOutboundParameterProblem(This,allow) (This)->lpVtbl->get_AllowOutboundParameterProblem(This,allow)
#define INetFwIcmpSettings_put_AllowOutboundParameterProblem(This,allow) (This)->lpVtbl->put_AllowOutboundParameterProblem(This,allow)
#define INetFwIcmpSettings_get_AllowOutboundSourceQuench(This,allow) (This)->lpVtbl->get_AllowOutboundSourceQuench(This,allow)
#define INetFwIcmpSettings_put_AllowOutboundSourceQuench(This,allow) (This)->lpVtbl->put_AllowOutboundSourceQuench(This,allow)
#define INetFwIcmpSettings_get_AllowInboundRouterRequest(This,allow) (This)->lpVtbl->get_AllowInboundRouterRequest(This,allow)
#define INetFwIcmpSettings_put_AllowInboundRouterRequest(This,allow) (This)->lpVtbl->put_AllowInboundRouterRequest(This,allow)
#define INetFwIcmpSettings_get_AllowInboundTimestampRequest(This,allow) (This)->lpVtbl->get_AllowInboundTimestampRequest(This,allow)
#define INetFwIcmpSettings_put_AllowInboundTimestampRequest(This,allow) (This)->lpVtbl->put_AllowInboundTimestampRequest(This,allow)
#define INetFwIcmpSettings_get_AllowInboundMaskRequest(This,allow) (This)->lpVtbl->get_AllowInboundMaskRequest(This,allow)
#define INetFwIcmpSettings_put_AllowInboundMaskRequest(This,allow) (This)->lpVtbl->put_AllowInboundMaskRequest(This,allow)
#define INetFwIcmpSettings_get_AllowOutboundPacketTooBig(This,allow) (This)->lpVtbl->get_AllowOutboundPacketTooBig(This,allow)
#define INetFwIcmpSettings_put_AllowOutboundPacketTooBig(This,allow) (This)->lpVtbl->put_AllowOutboundPacketTooBig(This,allow)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwIcmpSettings_QueryInterface(INetFwIcmpSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwIcmpSettings_AddRef(INetFwIcmpSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwIcmpSettings_Release(INetFwIcmpSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwIcmpSettings_GetTypeInfoCount(INetFwIcmpSettings* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwIcmpSettings_GetTypeInfo(INetFwIcmpSettings* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwIcmpSettings_GetIDsOfNames(INetFwIcmpSettings* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwIcmpSettings_Invoke(INetFwIcmpSettings* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwIcmpSettings methods ***/
static inline HRESULT INetFwIcmpSettings_get_AllowOutboundDestinationUnreachable(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowOutboundDestinationUnreachable(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowOutboundDestinationUnreachable(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowOutboundDestinationUnreachable(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowRedirect(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowRedirect(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowRedirect(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowRedirect(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowInboundEchoRequest(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowInboundEchoRequest(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowInboundEchoRequest(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowInboundEchoRequest(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowOutboundTimeExceeded(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowOutboundTimeExceeded(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowOutboundTimeExceeded(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowOutboundTimeExceeded(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowOutboundParameterProblem(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowOutboundParameterProblem(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowOutboundParameterProblem(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowOutboundParameterProblem(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowOutboundSourceQuench(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowOutboundSourceQuench(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowOutboundSourceQuench(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowOutboundSourceQuench(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowInboundRouterRequest(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowInboundRouterRequest(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowInboundRouterRequest(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowInboundRouterRequest(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowInboundTimestampRequest(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowInboundTimestampRequest(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowInboundTimestampRequest(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowInboundTimestampRequest(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowInboundMaskRequest(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowInboundMaskRequest(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowInboundMaskRequest(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowInboundMaskRequest(This,allow);
}
static inline HRESULT INetFwIcmpSettings_get_AllowOutboundPacketTooBig(INetFwIcmpSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowOutboundPacketTooBig(This,allow);
}
static inline HRESULT INetFwIcmpSettings_put_AllowOutboundPacketTooBig(INetFwIcmpSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowOutboundPacketTooBig(This,allow);
}
#endif
#endif

#endif


#endif  /* __INetFwIcmpSettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwOpenPort interface
 */
#ifndef __INetFwOpenPort_INTERFACE_DEFINED__
#define __INetFwOpenPort_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwOpenPort, 0xe0483ba0, 0x47ff, 0x4d9c, 0xa6,0xd6, 0x77,0x41,0xd0,0xb1,0x95,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e0483ba0-47ff-4d9c-a6d6-7741d0b195f7")
INetFwOpenPort : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IpVersion(
        NET_FW_IP_VERSION *ipVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IpVersion(
        NET_FW_IP_VERSION ipVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Protocol(
        NET_FW_IP_PROTOCOL *ipProtocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Protocol(
        NET_FW_IP_PROTOCOL ipProtocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Port(
        LONG *portNumber) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Port(
        LONG portNumber) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Scope(
        NET_FW_SCOPE *scope) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Scope(
        NET_FW_SCOPE scope) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteAddresses(
        BSTR *remoteAddrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RemoteAddresses(
        BSTR remoteAddrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BuiltIn(
        VARIANT_BOOL *builtIn) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwOpenPort, 0xe0483ba0, 0x47ff, 0x4d9c, 0xa6,0xd6, 0x77,0x41,0xd0,0xb1,0x95,0xf7)
#endif
#else
typedef struct INetFwOpenPortVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwOpenPort *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwOpenPort *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwOpenPort *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwOpenPort *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwOpenPort *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwOpenPort *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwOpenPort *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwOpenPort methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        INetFwOpenPort *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        INetFwOpenPort *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_IpVersion)(
        INetFwOpenPort *This,
        NET_FW_IP_VERSION *ipVersion);

    HRESULT (STDMETHODCALLTYPE *put_IpVersion)(
        INetFwOpenPort *This,
        NET_FW_IP_VERSION ipVersion);

    HRESULT (STDMETHODCALLTYPE *get_Protocol)(
        INetFwOpenPort *This,
        NET_FW_IP_PROTOCOL *ipProtocol);

    HRESULT (STDMETHODCALLTYPE *put_Protocol)(
        INetFwOpenPort *This,
        NET_FW_IP_PROTOCOL ipProtocol);

    HRESULT (STDMETHODCALLTYPE *get_Port)(
        INetFwOpenPort *This,
        LONG *portNumber);

    HRESULT (STDMETHODCALLTYPE *put_Port)(
        INetFwOpenPort *This,
        LONG portNumber);

    HRESULT (STDMETHODCALLTYPE *get_Scope)(
        INetFwOpenPort *This,
        NET_FW_SCOPE *scope);

    HRESULT (STDMETHODCALLTYPE *put_Scope)(
        INetFwOpenPort *This,
        NET_FW_SCOPE scope);

    HRESULT (STDMETHODCALLTYPE *get_RemoteAddresses)(
        INetFwOpenPort *This,
        BSTR *remoteAddrs);

    HRESULT (STDMETHODCALLTYPE *put_RemoteAddresses)(
        INetFwOpenPort *This,
        BSTR remoteAddrs);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        INetFwOpenPort *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        INetFwOpenPort *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_BuiltIn)(
        INetFwOpenPort *This,
        VARIANT_BOOL *builtIn);

    END_INTERFACE
} INetFwOpenPortVtbl;

interface INetFwOpenPort {
    CONST_VTBL INetFwOpenPortVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwOpenPort_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwOpenPort_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwOpenPort_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwOpenPort_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwOpenPort_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwOpenPort_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwOpenPort_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwOpenPort methods ***/
#define INetFwOpenPort_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define INetFwOpenPort_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define INetFwOpenPort_get_IpVersion(This,ipVersion) (This)->lpVtbl->get_IpVersion(This,ipVersion)
#define INetFwOpenPort_put_IpVersion(This,ipVersion) (This)->lpVtbl->put_IpVersion(This,ipVersion)
#define INetFwOpenPort_get_Protocol(This,ipProtocol) (This)->lpVtbl->get_Protocol(This,ipProtocol)
#define INetFwOpenPort_put_Protocol(This,ipProtocol) (This)->lpVtbl->put_Protocol(This,ipProtocol)
#define INetFwOpenPort_get_Port(This,portNumber) (This)->lpVtbl->get_Port(This,portNumber)
#define INetFwOpenPort_put_Port(This,portNumber) (This)->lpVtbl->put_Port(This,portNumber)
#define INetFwOpenPort_get_Scope(This,scope) (This)->lpVtbl->get_Scope(This,scope)
#define INetFwOpenPort_put_Scope(This,scope) (This)->lpVtbl->put_Scope(This,scope)
#define INetFwOpenPort_get_RemoteAddresses(This,remoteAddrs) (This)->lpVtbl->get_RemoteAddresses(This,remoteAddrs)
#define INetFwOpenPort_put_RemoteAddresses(This,remoteAddrs) (This)->lpVtbl->put_RemoteAddresses(This,remoteAddrs)
#define INetFwOpenPort_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define INetFwOpenPort_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#define INetFwOpenPort_get_BuiltIn(This,builtIn) (This)->lpVtbl->get_BuiltIn(This,builtIn)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwOpenPort_QueryInterface(INetFwOpenPort* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwOpenPort_AddRef(INetFwOpenPort* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwOpenPort_Release(INetFwOpenPort* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwOpenPort_GetTypeInfoCount(INetFwOpenPort* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwOpenPort_GetTypeInfo(INetFwOpenPort* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwOpenPort_GetIDsOfNames(INetFwOpenPort* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwOpenPort_Invoke(INetFwOpenPort* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwOpenPort methods ***/
static inline HRESULT INetFwOpenPort_get_Name(INetFwOpenPort* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT INetFwOpenPort_put_Name(INetFwOpenPort* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT INetFwOpenPort_get_IpVersion(INetFwOpenPort* This,NET_FW_IP_VERSION *ipVersion) {
    return This->lpVtbl->get_IpVersion(This,ipVersion);
}
static inline HRESULT INetFwOpenPort_put_IpVersion(INetFwOpenPort* This,NET_FW_IP_VERSION ipVersion) {
    return This->lpVtbl->put_IpVersion(This,ipVersion);
}
static inline HRESULT INetFwOpenPort_get_Protocol(INetFwOpenPort* This,NET_FW_IP_PROTOCOL *ipProtocol) {
    return This->lpVtbl->get_Protocol(This,ipProtocol);
}
static inline HRESULT INetFwOpenPort_put_Protocol(INetFwOpenPort* This,NET_FW_IP_PROTOCOL ipProtocol) {
    return This->lpVtbl->put_Protocol(This,ipProtocol);
}
static inline HRESULT INetFwOpenPort_get_Port(INetFwOpenPort* This,LONG *portNumber) {
    return This->lpVtbl->get_Port(This,portNumber);
}
static inline HRESULT INetFwOpenPort_put_Port(INetFwOpenPort* This,LONG portNumber) {
    return This->lpVtbl->put_Port(This,portNumber);
}
static inline HRESULT INetFwOpenPort_get_Scope(INetFwOpenPort* This,NET_FW_SCOPE *scope) {
    return This->lpVtbl->get_Scope(This,scope);
}
static inline HRESULT INetFwOpenPort_put_Scope(INetFwOpenPort* This,NET_FW_SCOPE scope) {
    return This->lpVtbl->put_Scope(This,scope);
}
static inline HRESULT INetFwOpenPort_get_RemoteAddresses(INetFwOpenPort* This,BSTR *remoteAddrs) {
    return This->lpVtbl->get_RemoteAddresses(This,remoteAddrs);
}
static inline HRESULT INetFwOpenPort_put_RemoteAddresses(INetFwOpenPort* This,BSTR remoteAddrs) {
    return This->lpVtbl->put_RemoteAddresses(This,remoteAddrs);
}
static inline HRESULT INetFwOpenPort_get_Enabled(INetFwOpenPort* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT INetFwOpenPort_put_Enabled(INetFwOpenPort* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
static inline HRESULT INetFwOpenPort_get_BuiltIn(INetFwOpenPort* This,VARIANT_BOOL *builtIn) {
    return This->lpVtbl->get_BuiltIn(This,builtIn);
}
#endif
#endif

#endif


#endif  /* __INetFwOpenPort_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwOpenPorts interface
 */
#ifndef __INetFwOpenPorts_INTERFACE_DEFINED__
#define __INetFwOpenPorts_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwOpenPorts, 0xc0e9d7fa, 0xe07e, 0x430a, 0xb1,0x9a, 0x09,0x0c,0xe8,0x2d,0x92,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e9d7fa-e07e-430a-b19a-090ce82d92e2")
INetFwOpenPorts : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        INetFwOpenPort *port) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        LONG portNumber,
        NET_FW_IP_PROTOCOL ipProtocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG portNumber,
        NET_FW_IP_PROTOCOL ipProtocol,
        INetFwOpenPort **openPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **newEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwOpenPorts, 0xc0e9d7fa, 0xe07e, 0x430a, 0xb1,0x9a, 0x09,0x0c,0xe8,0x2d,0x92,0xe2)
#endif
#else
typedef struct INetFwOpenPortsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwOpenPorts *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwOpenPorts *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwOpenPorts *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwOpenPorts *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwOpenPorts *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwOpenPorts *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwOpenPorts *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwOpenPorts methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        INetFwOpenPorts *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *Add)(
        INetFwOpenPorts *This,
        INetFwOpenPort *port);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        INetFwOpenPorts *This,
        LONG portNumber,
        NET_FW_IP_PROTOCOL ipProtocol);

    HRESULT (STDMETHODCALLTYPE *Item)(
        INetFwOpenPorts *This,
        LONG portNumber,
        NET_FW_IP_PROTOCOL ipProtocol,
        INetFwOpenPort **openPort);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        INetFwOpenPorts *This,
        IUnknown **newEnum);

    END_INTERFACE
} INetFwOpenPortsVtbl;

interface INetFwOpenPorts {
    CONST_VTBL INetFwOpenPortsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwOpenPorts_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwOpenPorts_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwOpenPorts_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwOpenPorts_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwOpenPorts_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwOpenPorts_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwOpenPorts_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwOpenPorts methods ***/
#define INetFwOpenPorts_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define INetFwOpenPorts_Add(This,port) (This)->lpVtbl->Add(This,port)
#define INetFwOpenPorts_Remove(This,portNumber,ipProtocol) (This)->lpVtbl->Remove(This,portNumber,ipProtocol)
#define INetFwOpenPorts_Item(This,portNumber,ipProtocol,openPort) (This)->lpVtbl->Item(This,portNumber,ipProtocol,openPort)
#define INetFwOpenPorts_get__NewEnum(This,newEnum) (This)->lpVtbl->get__NewEnum(This,newEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwOpenPorts_QueryInterface(INetFwOpenPorts* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwOpenPorts_AddRef(INetFwOpenPorts* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwOpenPorts_Release(INetFwOpenPorts* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwOpenPorts_GetTypeInfoCount(INetFwOpenPorts* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwOpenPorts_GetTypeInfo(INetFwOpenPorts* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwOpenPorts_GetIDsOfNames(INetFwOpenPorts* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwOpenPorts_Invoke(INetFwOpenPorts* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwOpenPorts methods ***/
static inline HRESULT INetFwOpenPorts_get_Count(INetFwOpenPorts* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT INetFwOpenPorts_Add(INetFwOpenPorts* This,INetFwOpenPort *port) {
    return This->lpVtbl->Add(This,port);
}
static inline HRESULT INetFwOpenPorts_Remove(INetFwOpenPorts* This,LONG portNumber,NET_FW_IP_PROTOCOL ipProtocol) {
    return This->lpVtbl->Remove(This,portNumber,ipProtocol);
}
static inline HRESULT INetFwOpenPorts_Item(INetFwOpenPorts* This,LONG portNumber,NET_FW_IP_PROTOCOL ipProtocol,INetFwOpenPort **openPort) {
    return This->lpVtbl->Item(This,portNumber,ipProtocol,openPort);
}
static inline HRESULT INetFwOpenPorts_get__NewEnum(INetFwOpenPorts* This,IUnknown **newEnum) {
    return This->lpVtbl->get__NewEnum(This,newEnum);
}
#endif
#endif

#endif


#endif  /* __INetFwOpenPorts_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwService interface
 */
#ifndef __INetFwService_INTERFACE_DEFINED__
#define __INetFwService_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwService, 0x79fd57c8, 0x908e, 0x4a36, 0x98,0x88, 0xd5,0xb3,0xf0,0xa4,0x44,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79fd57c8-908e-4a36-9888-d5b3f0a444cf")
INetFwService : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        NET_FW_SERVICE_TYPE *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Customized(
        VARIANT_BOOL *customized) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IpVersion(
        NET_FW_IP_VERSION *ipVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IpVersion(
        NET_FW_IP_VERSION ipVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Scope(
        NET_FW_SCOPE *scope) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Scope(
        NET_FW_SCOPE scope) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteAddresses(
        BSTR *remoteAddrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RemoteAddresses(
        BSTR remoteAddrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_GloballyOpenPorts(
        INetFwOpenPorts **openPorts) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwService, 0x79fd57c8, 0x908e, 0x4a36, 0x98,0x88, 0xd5,0xb3,0xf0,0xa4,0x44,0xcf)
#endif
#else
typedef struct INetFwServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwService *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwService *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwService *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwService *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwService *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwService methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        INetFwService *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        INetFwService *This,
        NET_FW_SERVICE_TYPE *type);

    HRESULT (STDMETHODCALLTYPE *get_Customized)(
        INetFwService *This,
        VARIANT_BOOL *customized);

    HRESULT (STDMETHODCALLTYPE *get_IpVersion)(
        INetFwService *This,
        NET_FW_IP_VERSION *ipVersion);

    HRESULT (STDMETHODCALLTYPE *put_IpVersion)(
        INetFwService *This,
        NET_FW_IP_VERSION ipVersion);

    HRESULT (STDMETHODCALLTYPE *get_Scope)(
        INetFwService *This,
        NET_FW_SCOPE *scope);

    HRESULT (STDMETHODCALLTYPE *put_Scope)(
        INetFwService *This,
        NET_FW_SCOPE scope);

    HRESULT (STDMETHODCALLTYPE *get_RemoteAddresses)(
        INetFwService *This,
        BSTR *remoteAddrs);

    HRESULT (STDMETHODCALLTYPE *put_RemoteAddresses)(
        INetFwService *This,
        BSTR remoteAddrs);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        INetFwService *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        INetFwService *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_GloballyOpenPorts)(
        INetFwService *This,
        INetFwOpenPorts **openPorts);

    END_INTERFACE
} INetFwServiceVtbl;

interface INetFwService {
    CONST_VTBL INetFwServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwService_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwService_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwService_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwService_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwService_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwService methods ***/
#define INetFwService_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define INetFwService_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define INetFwService_get_Customized(This,customized) (This)->lpVtbl->get_Customized(This,customized)
#define INetFwService_get_IpVersion(This,ipVersion) (This)->lpVtbl->get_IpVersion(This,ipVersion)
#define INetFwService_put_IpVersion(This,ipVersion) (This)->lpVtbl->put_IpVersion(This,ipVersion)
#define INetFwService_get_Scope(This,scope) (This)->lpVtbl->get_Scope(This,scope)
#define INetFwService_put_Scope(This,scope) (This)->lpVtbl->put_Scope(This,scope)
#define INetFwService_get_RemoteAddresses(This,remoteAddrs) (This)->lpVtbl->get_RemoteAddresses(This,remoteAddrs)
#define INetFwService_put_RemoteAddresses(This,remoteAddrs) (This)->lpVtbl->put_RemoteAddresses(This,remoteAddrs)
#define INetFwService_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define INetFwService_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#define INetFwService_get_GloballyOpenPorts(This,openPorts) (This)->lpVtbl->get_GloballyOpenPorts(This,openPorts)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwService_QueryInterface(INetFwService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwService_AddRef(INetFwService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwService_Release(INetFwService* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwService_GetTypeInfoCount(INetFwService* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwService_GetTypeInfo(INetFwService* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwService_GetIDsOfNames(INetFwService* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwService_Invoke(INetFwService* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwService methods ***/
static inline HRESULT INetFwService_get_Name(INetFwService* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT INetFwService_get_Type(INetFwService* This,NET_FW_SERVICE_TYPE *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT INetFwService_get_Customized(INetFwService* This,VARIANT_BOOL *customized) {
    return This->lpVtbl->get_Customized(This,customized);
}
static inline HRESULT INetFwService_get_IpVersion(INetFwService* This,NET_FW_IP_VERSION *ipVersion) {
    return This->lpVtbl->get_IpVersion(This,ipVersion);
}
static inline HRESULT INetFwService_put_IpVersion(INetFwService* This,NET_FW_IP_VERSION ipVersion) {
    return This->lpVtbl->put_IpVersion(This,ipVersion);
}
static inline HRESULT INetFwService_get_Scope(INetFwService* This,NET_FW_SCOPE *scope) {
    return This->lpVtbl->get_Scope(This,scope);
}
static inline HRESULT INetFwService_put_Scope(INetFwService* This,NET_FW_SCOPE scope) {
    return This->lpVtbl->put_Scope(This,scope);
}
static inline HRESULT INetFwService_get_RemoteAddresses(INetFwService* This,BSTR *remoteAddrs) {
    return This->lpVtbl->get_RemoteAddresses(This,remoteAddrs);
}
static inline HRESULT INetFwService_put_RemoteAddresses(INetFwService* This,BSTR remoteAddrs) {
    return This->lpVtbl->put_RemoteAddresses(This,remoteAddrs);
}
static inline HRESULT INetFwService_get_Enabled(INetFwService* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT INetFwService_put_Enabled(INetFwService* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
static inline HRESULT INetFwService_get_GloballyOpenPorts(INetFwService* This,INetFwOpenPorts **openPorts) {
    return This->lpVtbl->get_GloballyOpenPorts(This,openPorts);
}
#endif
#endif

#endif


#endif  /* __INetFwService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwServices interface
 */
#ifndef __INetFwServices_INTERFACE_DEFINED__
#define __INetFwServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwServices, 0x79649bb4, 0x903e, 0x421b, 0x94,0xc9, 0x79,0x84,0x8e,0x79,0xf6,0xee);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79649bb4-903e-421b-94c9-79848e79f6ee")
INetFwServices : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        NET_FW_SERVICE_TYPE svcType,
        INetFwService **service) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **newEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwServices, 0x79649bb4, 0x903e, 0x421b, 0x94,0xc9, 0x79,0x84,0x8e,0x79,0xf6,0xee)
#endif
#else
typedef struct INetFwServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwServices *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwServices *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwServices *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwServices *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwServices *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwServices methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        INetFwServices *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *Item)(
        INetFwServices *This,
        NET_FW_SERVICE_TYPE svcType,
        INetFwService **service);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        INetFwServices *This,
        IUnknown **newEnum);

    END_INTERFACE
} INetFwServicesVtbl;

interface INetFwServices {
    CONST_VTBL INetFwServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwServices_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwServices_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwServices_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwServices_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwServices_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwServices methods ***/
#define INetFwServices_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define INetFwServices_Item(This,svcType,service) (This)->lpVtbl->Item(This,svcType,service)
#define INetFwServices_get__NewEnum(This,newEnum) (This)->lpVtbl->get__NewEnum(This,newEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwServices_QueryInterface(INetFwServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwServices_AddRef(INetFwServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwServices_Release(INetFwServices* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwServices_GetTypeInfoCount(INetFwServices* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwServices_GetTypeInfo(INetFwServices* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwServices_GetIDsOfNames(INetFwServices* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwServices_Invoke(INetFwServices* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwServices methods ***/
static inline HRESULT INetFwServices_get_Count(INetFwServices* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT INetFwServices_Item(INetFwServices* This,NET_FW_SERVICE_TYPE svcType,INetFwService **service) {
    return This->lpVtbl->Item(This,svcType,service);
}
static inline HRESULT INetFwServices_get__NewEnum(INetFwServices* This,IUnknown **newEnum) {
    return This->lpVtbl->get__NewEnum(This,newEnum);
}
#endif
#endif

#endif


#endif  /* __INetFwServices_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwAuthorizedApplication interface
 */
#ifndef __INetFwAuthorizedApplication_INTERFACE_DEFINED__
#define __INetFwAuthorizedApplication_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwAuthorizedApplication, 0xb5e64ffa, 0xc2c5, 0x444e, 0xa3,0x01, 0xfb,0x5e,0x00,0x01,0x80,0x50);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b5e64ffa-c2c5-444e-a301-fb5e00018050")
INetFwAuthorizedApplication : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ProcessImageFileName(
        BSTR *imageFileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ProcessImageFileName(
        BSTR imageFileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IpVersion(
        NET_FW_IP_VERSION *ipVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IpVersion(
        NET_FW_IP_VERSION ipVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Scope(
        NET_FW_SCOPE *scope) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Scope(
        NET_FW_SCOPE scope) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteAddresses(
        BSTR *remoteAddrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RemoteAddresses(
        BSTR remoteAddrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwAuthorizedApplication, 0xb5e64ffa, 0xc2c5, 0x444e, 0xa3,0x01, 0xfb,0x5e,0x00,0x01,0x80,0x50)
#endif
#else
typedef struct INetFwAuthorizedApplicationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwAuthorizedApplication *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwAuthorizedApplication *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwAuthorizedApplication *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwAuthorizedApplication *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwAuthorizedApplication *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwAuthorizedApplication *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwAuthorizedApplication *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwAuthorizedApplication methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        INetFwAuthorizedApplication *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        INetFwAuthorizedApplication *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_ProcessImageFileName)(
        INetFwAuthorizedApplication *This,
        BSTR *imageFileName);

    HRESULT (STDMETHODCALLTYPE *put_ProcessImageFileName)(
        INetFwAuthorizedApplication *This,
        BSTR imageFileName);

    HRESULT (STDMETHODCALLTYPE *get_IpVersion)(
        INetFwAuthorizedApplication *This,
        NET_FW_IP_VERSION *ipVersion);

    HRESULT (STDMETHODCALLTYPE *put_IpVersion)(
        INetFwAuthorizedApplication *This,
        NET_FW_IP_VERSION ipVersion);

    HRESULT (STDMETHODCALLTYPE *get_Scope)(
        INetFwAuthorizedApplication *This,
        NET_FW_SCOPE *scope);

    HRESULT (STDMETHODCALLTYPE *put_Scope)(
        INetFwAuthorizedApplication *This,
        NET_FW_SCOPE scope);

    HRESULT (STDMETHODCALLTYPE *get_RemoteAddresses)(
        INetFwAuthorizedApplication *This,
        BSTR *remoteAddrs);

    HRESULT (STDMETHODCALLTYPE *put_RemoteAddresses)(
        INetFwAuthorizedApplication *This,
        BSTR remoteAddrs);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        INetFwAuthorizedApplication *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        INetFwAuthorizedApplication *This,
        VARIANT_BOOL enabled);

    END_INTERFACE
} INetFwAuthorizedApplicationVtbl;

interface INetFwAuthorizedApplication {
    CONST_VTBL INetFwAuthorizedApplicationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwAuthorizedApplication_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwAuthorizedApplication_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwAuthorizedApplication_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwAuthorizedApplication_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwAuthorizedApplication_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwAuthorizedApplication_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwAuthorizedApplication_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwAuthorizedApplication methods ***/
#define INetFwAuthorizedApplication_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define INetFwAuthorizedApplication_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define INetFwAuthorizedApplication_get_ProcessImageFileName(This,imageFileName) (This)->lpVtbl->get_ProcessImageFileName(This,imageFileName)
#define INetFwAuthorizedApplication_put_ProcessImageFileName(This,imageFileName) (This)->lpVtbl->put_ProcessImageFileName(This,imageFileName)
#define INetFwAuthorizedApplication_get_IpVersion(This,ipVersion) (This)->lpVtbl->get_IpVersion(This,ipVersion)
#define INetFwAuthorizedApplication_put_IpVersion(This,ipVersion) (This)->lpVtbl->put_IpVersion(This,ipVersion)
#define INetFwAuthorizedApplication_get_Scope(This,scope) (This)->lpVtbl->get_Scope(This,scope)
#define INetFwAuthorizedApplication_put_Scope(This,scope) (This)->lpVtbl->put_Scope(This,scope)
#define INetFwAuthorizedApplication_get_RemoteAddresses(This,remoteAddrs) (This)->lpVtbl->get_RemoteAddresses(This,remoteAddrs)
#define INetFwAuthorizedApplication_put_RemoteAddresses(This,remoteAddrs) (This)->lpVtbl->put_RemoteAddresses(This,remoteAddrs)
#define INetFwAuthorizedApplication_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define INetFwAuthorizedApplication_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwAuthorizedApplication_QueryInterface(INetFwAuthorizedApplication* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwAuthorizedApplication_AddRef(INetFwAuthorizedApplication* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwAuthorizedApplication_Release(INetFwAuthorizedApplication* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwAuthorizedApplication_GetTypeInfoCount(INetFwAuthorizedApplication* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwAuthorizedApplication_GetTypeInfo(INetFwAuthorizedApplication* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwAuthorizedApplication_GetIDsOfNames(INetFwAuthorizedApplication* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwAuthorizedApplication_Invoke(INetFwAuthorizedApplication* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwAuthorizedApplication methods ***/
static inline HRESULT INetFwAuthorizedApplication_get_Name(INetFwAuthorizedApplication* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT INetFwAuthorizedApplication_put_Name(INetFwAuthorizedApplication* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT INetFwAuthorizedApplication_get_ProcessImageFileName(INetFwAuthorizedApplication* This,BSTR *imageFileName) {
    return This->lpVtbl->get_ProcessImageFileName(This,imageFileName);
}
static inline HRESULT INetFwAuthorizedApplication_put_ProcessImageFileName(INetFwAuthorizedApplication* This,BSTR imageFileName) {
    return This->lpVtbl->put_ProcessImageFileName(This,imageFileName);
}
static inline HRESULT INetFwAuthorizedApplication_get_IpVersion(INetFwAuthorizedApplication* This,NET_FW_IP_VERSION *ipVersion) {
    return This->lpVtbl->get_IpVersion(This,ipVersion);
}
static inline HRESULT INetFwAuthorizedApplication_put_IpVersion(INetFwAuthorizedApplication* This,NET_FW_IP_VERSION ipVersion) {
    return This->lpVtbl->put_IpVersion(This,ipVersion);
}
static inline HRESULT INetFwAuthorizedApplication_get_Scope(INetFwAuthorizedApplication* This,NET_FW_SCOPE *scope) {
    return This->lpVtbl->get_Scope(This,scope);
}
static inline HRESULT INetFwAuthorizedApplication_put_Scope(INetFwAuthorizedApplication* This,NET_FW_SCOPE scope) {
    return This->lpVtbl->put_Scope(This,scope);
}
static inline HRESULT INetFwAuthorizedApplication_get_RemoteAddresses(INetFwAuthorizedApplication* This,BSTR *remoteAddrs) {
    return This->lpVtbl->get_RemoteAddresses(This,remoteAddrs);
}
static inline HRESULT INetFwAuthorizedApplication_put_RemoteAddresses(INetFwAuthorizedApplication* This,BSTR remoteAddrs) {
    return This->lpVtbl->put_RemoteAddresses(This,remoteAddrs);
}
static inline HRESULT INetFwAuthorizedApplication_get_Enabled(INetFwAuthorizedApplication* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT INetFwAuthorizedApplication_put_Enabled(INetFwAuthorizedApplication* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
#endif
#endif

#endif


#endif  /* __INetFwAuthorizedApplication_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwRemoteAdminSettings interface
 */
#ifndef __INetFwRemoteAdminSettings_INTERFACE_DEFINED__
#define __INetFwRemoteAdminSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwRemoteAdminSettings, 0xd4becddf, 0x6f73, 0x4a83, 0xb8,0x32, 0x9c,0x66,0x87,0x4c,0xd2,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d4becddf-6f73-4a83-b832-9c66874cd20e")
INetFwRemoteAdminSettings : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_IpVersion(
        NET_FW_IP_VERSION *ipVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IpVersion(
        NET_FW_IP_VERSION ipVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Scope(
        NET_FW_SCOPE *scope) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Scope(
        NET_FW_SCOPE scope) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteAddresses(
        BSTR *remoteAddrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RemoteAddresses(
        BSTR remoteAddrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwRemoteAdminSettings, 0xd4becddf, 0x6f73, 0x4a83, 0xb8,0x32, 0x9c,0x66,0x87,0x4c,0xd2,0x0e)
#endif
#else
typedef struct INetFwRemoteAdminSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwRemoteAdminSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwRemoteAdminSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwRemoteAdminSettings *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwRemoteAdminSettings *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwRemoteAdminSettings *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwRemoteAdminSettings *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwRemoteAdminSettings *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwRemoteAdminSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IpVersion)(
        INetFwRemoteAdminSettings *This,
        NET_FW_IP_VERSION *ipVersion);

    HRESULT (STDMETHODCALLTYPE *put_IpVersion)(
        INetFwRemoteAdminSettings *This,
        NET_FW_IP_VERSION ipVersion);

    HRESULT (STDMETHODCALLTYPE *get_Scope)(
        INetFwRemoteAdminSettings *This,
        NET_FW_SCOPE *scope);

    HRESULT (STDMETHODCALLTYPE *put_Scope)(
        INetFwRemoteAdminSettings *This,
        NET_FW_SCOPE scope);

    HRESULT (STDMETHODCALLTYPE *get_RemoteAddresses)(
        INetFwRemoteAdminSettings *This,
        BSTR *remoteAddrs);

    HRESULT (STDMETHODCALLTYPE *put_RemoteAddresses)(
        INetFwRemoteAdminSettings *This,
        BSTR remoteAddrs);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        INetFwRemoteAdminSettings *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        INetFwRemoteAdminSettings *This,
        VARIANT_BOOL enabled);

    END_INTERFACE
} INetFwRemoteAdminSettingsVtbl;

interface INetFwRemoteAdminSettings {
    CONST_VTBL INetFwRemoteAdminSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwRemoteAdminSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwRemoteAdminSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwRemoteAdminSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwRemoteAdminSettings_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwRemoteAdminSettings_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwRemoteAdminSettings_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwRemoteAdminSettings_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwRemoteAdminSettings methods ***/
#define INetFwRemoteAdminSettings_get_IpVersion(This,ipVersion) (This)->lpVtbl->get_IpVersion(This,ipVersion)
#define INetFwRemoteAdminSettings_put_IpVersion(This,ipVersion) (This)->lpVtbl->put_IpVersion(This,ipVersion)
#define INetFwRemoteAdminSettings_get_Scope(This,scope) (This)->lpVtbl->get_Scope(This,scope)
#define INetFwRemoteAdminSettings_put_Scope(This,scope) (This)->lpVtbl->put_Scope(This,scope)
#define INetFwRemoteAdminSettings_get_RemoteAddresses(This,remoteAddrs) (This)->lpVtbl->get_RemoteAddresses(This,remoteAddrs)
#define INetFwRemoteAdminSettings_put_RemoteAddresses(This,remoteAddrs) (This)->lpVtbl->put_RemoteAddresses(This,remoteAddrs)
#define INetFwRemoteAdminSettings_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define INetFwRemoteAdminSettings_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwRemoteAdminSettings_QueryInterface(INetFwRemoteAdminSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwRemoteAdminSettings_AddRef(INetFwRemoteAdminSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwRemoteAdminSettings_Release(INetFwRemoteAdminSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwRemoteAdminSettings_GetTypeInfoCount(INetFwRemoteAdminSettings* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwRemoteAdminSettings_GetTypeInfo(INetFwRemoteAdminSettings* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwRemoteAdminSettings_GetIDsOfNames(INetFwRemoteAdminSettings* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwRemoteAdminSettings_Invoke(INetFwRemoteAdminSettings* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwRemoteAdminSettings methods ***/
static inline HRESULT INetFwRemoteAdminSettings_get_IpVersion(INetFwRemoteAdminSettings* This,NET_FW_IP_VERSION *ipVersion) {
    return This->lpVtbl->get_IpVersion(This,ipVersion);
}
static inline HRESULT INetFwRemoteAdminSettings_put_IpVersion(INetFwRemoteAdminSettings* This,NET_FW_IP_VERSION ipVersion) {
    return This->lpVtbl->put_IpVersion(This,ipVersion);
}
static inline HRESULT INetFwRemoteAdminSettings_get_Scope(INetFwRemoteAdminSettings* This,NET_FW_SCOPE *scope) {
    return This->lpVtbl->get_Scope(This,scope);
}
static inline HRESULT INetFwRemoteAdminSettings_put_Scope(INetFwRemoteAdminSettings* This,NET_FW_SCOPE scope) {
    return This->lpVtbl->put_Scope(This,scope);
}
static inline HRESULT INetFwRemoteAdminSettings_get_RemoteAddresses(INetFwRemoteAdminSettings* This,BSTR *remoteAddrs) {
    return This->lpVtbl->get_RemoteAddresses(This,remoteAddrs);
}
static inline HRESULT INetFwRemoteAdminSettings_put_RemoteAddresses(INetFwRemoteAdminSettings* This,BSTR remoteAddrs) {
    return This->lpVtbl->put_RemoteAddresses(This,remoteAddrs);
}
static inline HRESULT INetFwRemoteAdminSettings_get_Enabled(INetFwRemoteAdminSettings* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT INetFwRemoteAdminSettings_put_Enabled(INetFwRemoteAdminSettings* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
#endif
#endif

#endif


#endif  /* __INetFwRemoteAdminSettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwAuthorizedApplications interface
 */
#ifndef __INetFwAuthorizedApplications_INTERFACE_DEFINED__
#define __INetFwAuthorizedApplications_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwAuthorizedApplications, 0x644efd52, 0xccf9, 0x486c, 0x97,0xa2, 0x39,0xf3,0x52,0x57,0x0b,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("644efd52-ccf9-486c-97a2-39f352570b30")
INetFwAuthorizedApplications : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        INetFwAuthorizedApplication *app) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        BSTR imageFileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        BSTR imageFileName,
        INetFwAuthorizedApplication **app) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **newEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwAuthorizedApplications, 0x644efd52, 0xccf9, 0x486c, 0x97,0xa2, 0x39,0xf3,0x52,0x57,0x0b,0x30)
#endif
#else
typedef struct INetFwAuthorizedApplicationsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwAuthorizedApplications *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwAuthorizedApplications *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwAuthorizedApplications *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwAuthorizedApplications *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwAuthorizedApplications *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwAuthorizedApplications *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwAuthorizedApplications *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwAuthorizedApplications methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        INetFwAuthorizedApplications *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *Add)(
        INetFwAuthorizedApplications *This,
        INetFwAuthorizedApplication *app);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        INetFwAuthorizedApplications *This,
        BSTR imageFileName);

    HRESULT (STDMETHODCALLTYPE *Item)(
        INetFwAuthorizedApplications *This,
        BSTR imageFileName,
        INetFwAuthorizedApplication **app);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        INetFwAuthorizedApplications *This,
        IUnknown **newEnum);

    END_INTERFACE
} INetFwAuthorizedApplicationsVtbl;

interface INetFwAuthorizedApplications {
    CONST_VTBL INetFwAuthorizedApplicationsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwAuthorizedApplications_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwAuthorizedApplications_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwAuthorizedApplications_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwAuthorizedApplications_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwAuthorizedApplications_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwAuthorizedApplications_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwAuthorizedApplications_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwAuthorizedApplications methods ***/
#define INetFwAuthorizedApplications_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define INetFwAuthorizedApplications_Add(This,app) (This)->lpVtbl->Add(This,app)
#define INetFwAuthorizedApplications_Remove(This,imageFileName) (This)->lpVtbl->Remove(This,imageFileName)
#define INetFwAuthorizedApplications_Item(This,imageFileName,app) (This)->lpVtbl->Item(This,imageFileName,app)
#define INetFwAuthorizedApplications_get__NewEnum(This,newEnum) (This)->lpVtbl->get__NewEnum(This,newEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwAuthorizedApplications_QueryInterface(INetFwAuthorizedApplications* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwAuthorizedApplications_AddRef(INetFwAuthorizedApplications* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwAuthorizedApplications_Release(INetFwAuthorizedApplications* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwAuthorizedApplications_GetTypeInfoCount(INetFwAuthorizedApplications* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwAuthorizedApplications_GetTypeInfo(INetFwAuthorizedApplications* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwAuthorizedApplications_GetIDsOfNames(INetFwAuthorizedApplications* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwAuthorizedApplications_Invoke(INetFwAuthorizedApplications* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwAuthorizedApplications methods ***/
static inline HRESULT INetFwAuthorizedApplications_get_Count(INetFwAuthorizedApplications* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT INetFwAuthorizedApplications_Add(INetFwAuthorizedApplications* This,INetFwAuthorizedApplication *app) {
    return This->lpVtbl->Add(This,app);
}
static inline HRESULT INetFwAuthorizedApplications_Remove(INetFwAuthorizedApplications* This,BSTR imageFileName) {
    return This->lpVtbl->Remove(This,imageFileName);
}
static inline HRESULT INetFwAuthorizedApplications_Item(INetFwAuthorizedApplications* This,BSTR imageFileName,INetFwAuthorizedApplication **app) {
    return This->lpVtbl->Item(This,imageFileName,app);
}
static inline HRESULT INetFwAuthorizedApplications_get__NewEnum(INetFwAuthorizedApplications* This,IUnknown **newEnum) {
    return This->lpVtbl->get__NewEnum(This,newEnum);
}
#endif
#endif

#endif


#endif  /* __INetFwAuthorizedApplications_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwRule interface
 */
#ifndef __INetFwRule_INTERFACE_DEFINED__
#define __INetFwRule_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwRule, 0xaf230d27, 0xbaba, 0x4e42, 0xac,0xed, 0xf5,0x24,0xf2,0x2c,0xfc,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("af230d27-baba-4e42-aced-f524f22cfce2")
INetFwRule : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Description(
        BSTR desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ApplicationName(
        BSTR *imagename) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ApplicationName(
        BSTR imagename) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ServiceName(
        BSTR *service) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ServiceName(
        BSTR service) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Protocol(
        LONG *protocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Protocol(
        LONG protocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalPorts(
        BSTR *ports) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_LocalPorts(
        BSTR ports) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemotePorts(
        BSTR *ports) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RemotePorts(
        BSTR ports) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalAddresses(
        BSTR *address) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_LocalAddresses(
        BSTR address) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteAddresses(
        BSTR *address) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RemoteAddresses(
        BSTR address) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IcmpTypesAndCodes(
        BSTR *codes) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IcmpTypesAndCodes(
        BSTR codes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Direction(
        NET_FW_RULE_DIRECTION *dir) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Direction(
        NET_FW_RULE_DIRECTION dir) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Interfaces(
        VARIANT *interfaces) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Interfaces(
        VARIANT interfaces) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InterfaceTypes(
        BSTR *types) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_InterfaceTypes(
        BSTR types) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Grouping(
        BSTR *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Grouping(
        BSTR context) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Profiles(
        LONG *profiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Profiles(
        LONG profiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EdgeTraversal(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EdgeTraversal(
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Action(
        NET_FW_ACTION *action) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Action(
        NET_FW_ACTION action) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwRule, 0xaf230d27, 0xbaba, 0x4e42, 0xac,0xed, 0xf5,0x24,0xf2,0x2c,0xfc,0xe2)
#endif
#else
typedef struct INetFwRuleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwRule *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwRule *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwRule *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwRule *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwRule *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwRule *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwRule *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwRule methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        INetFwRule *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        INetFwRule *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        INetFwRule *This,
        BSTR *desc);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        INetFwRule *This,
        BSTR desc);

    HRESULT (STDMETHODCALLTYPE *get_ApplicationName)(
        INetFwRule *This,
        BSTR *imagename);

    HRESULT (STDMETHODCALLTYPE *put_ApplicationName)(
        INetFwRule *This,
        BSTR imagename);

    HRESULT (STDMETHODCALLTYPE *get_ServiceName)(
        INetFwRule *This,
        BSTR *service);

    HRESULT (STDMETHODCALLTYPE *put_ServiceName)(
        INetFwRule *This,
        BSTR service);

    HRESULT (STDMETHODCALLTYPE *get_Protocol)(
        INetFwRule *This,
        LONG *protocol);

    HRESULT (STDMETHODCALLTYPE *put_Protocol)(
        INetFwRule *This,
        LONG protocol);

    HRESULT (STDMETHODCALLTYPE *get_LocalPorts)(
        INetFwRule *This,
        BSTR *ports);

    HRESULT (STDMETHODCALLTYPE *put_LocalPorts)(
        INetFwRule *This,
        BSTR ports);

    HRESULT (STDMETHODCALLTYPE *get_RemotePorts)(
        INetFwRule *This,
        BSTR *ports);

    HRESULT (STDMETHODCALLTYPE *put_RemotePorts)(
        INetFwRule *This,
        BSTR ports);

    HRESULT (STDMETHODCALLTYPE *get_LocalAddresses)(
        INetFwRule *This,
        BSTR *address);

    HRESULT (STDMETHODCALLTYPE *put_LocalAddresses)(
        INetFwRule *This,
        BSTR address);

    HRESULT (STDMETHODCALLTYPE *get_RemoteAddresses)(
        INetFwRule *This,
        BSTR *address);

    HRESULT (STDMETHODCALLTYPE *put_RemoteAddresses)(
        INetFwRule *This,
        BSTR address);

    HRESULT (STDMETHODCALLTYPE *get_IcmpTypesAndCodes)(
        INetFwRule *This,
        BSTR *codes);

    HRESULT (STDMETHODCALLTYPE *put_IcmpTypesAndCodes)(
        INetFwRule *This,
        BSTR codes);

    HRESULT (STDMETHODCALLTYPE *get_Direction)(
        INetFwRule *This,
        NET_FW_RULE_DIRECTION *dir);

    HRESULT (STDMETHODCALLTYPE *put_Direction)(
        INetFwRule *This,
        NET_FW_RULE_DIRECTION dir);

    HRESULT (STDMETHODCALLTYPE *get_Interfaces)(
        INetFwRule *This,
        VARIANT *interfaces);

    HRESULT (STDMETHODCALLTYPE *put_Interfaces)(
        INetFwRule *This,
        VARIANT interfaces);

    HRESULT (STDMETHODCALLTYPE *get_InterfaceTypes)(
        INetFwRule *This,
        BSTR *types);

    HRESULT (STDMETHODCALLTYPE *put_InterfaceTypes)(
        INetFwRule *This,
        BSTR types);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        INetFwRule *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        INetFwRule *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_Grouping)(
        INetFwRule *This,
        BSTR *context);

    HRESULT (STDMETHODCALLTYPE *put_Grouping)(
        INetFwRule *This,
        BSTR context);

    HRESULT (STDMETHODCALLTYPE *get_Profiles)(
        INetFwRule *This,
        LONG *profiles);

    HRESULT (STDMETHODCALLTYPE *put_Profiles)(
        INetFwRule *This,
        LONG profiles);

    HRESULT (STDMETHODCALLTYPE *get_EdgeTraversal)(
        INetFwRule *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_EdgeTraversal)(
        INetFwRule *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_Action)(
        INetFwRule *This,
        NET_FW_ACTION *action);

    HRESULT (STDMETHODCALLTYPE *put_Action)(
        INetFwRule *This,
        NET_FW_ACTION action);

    END_INTERFACE
} INetFwRuleVtbl;

interface INetFwRule {
    CONST_VTBL INetFwRuleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwRule_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwRule_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwRule_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwRule_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwRule_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwRule_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwRule_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwRule methods ***/
#define INetFwRule_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define INetFwRule_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define INetFwRule_get_Description(This,desc) (This)->lpVtbl->get_Description(This,desc)
#define INetFwRule_put_Description(This,desc) (This)->lpVtbl->put_Description(This,desc)
#define INetFwRule_get_ApplicationName(This,imagename) (This)->lpVtbl->get_ApplicationName(This,imagename)
#define INetFwRule_put_ApplicationName(This,imagename) (This)->lpVtbl->put_ApplicationName(This,imagename)
#define INetFwRule_get_ServiceName(This,service) (This)->lpVtbl->get_ServiceName(This,service)
#define INetFwRule_put_ServiceName(This,service) (This)->lpVtbl->put_ServiceName(This,service)
#define INetFwRule_get_Protocol(This,protocol) (This)->lpVtbl->get_Protocol(This,protocol)
#define INetFwRule_put_Protocol(This,protocol) (This)->lpVtbl->put_Protocol(This,protocol)
#define INetFwRule_get_LocalPorts(This,ports) (This)->lpVtbl->get_LocalPorts(This,ports)
#define INetFwRule_put_LocalPorts(This,ports) (This)->lpVtbl->put_LocalPorts(This,ports)
#define INetFwRule_get_RemotePorts(This,ports) (This)->lpVtbl->get_RemotePorts(This,ports)
#define INetFwRule_put_RemotePorts(This,ports) (This)->lpVtbl->put_RemotePorts(This,ports)
#define INetFwRule_get_LocalAddresses(This,address) (This)->lpVtbl->get_LocalAddresses(This,address)
#define INetFwRule_put_LocalAddresses(This,address) (This)->lpVtbl->put_LocalAddresses(This,address)
#define INetFwRule_get_RemoteAddresses(This,address) (This)->lpVtbl->get_RemoteAddresses(This,address)
#define INetFwRule_put_RemoteAddresses(This,address) (This)->lpVtbl->put_RemoteAddresses(This,address)
#define INetFwRule_get_IcmpTypesAndCodes(This,codes) (This)->lpVtbl->get_IcmpTypesAndCodes(This,codes)
#define INetFwRule_put_IcmpTypesAndCodes(This,codes) (This)->lpVtbl->put_IcmpTypesAndCodes(This,codes)
#define INetFwRule_get_Direction(This,dir) (This)->lpVtbl->get_Direction(This,dir)
#define INetFwRule_put_Direction(This,dir) (This)->lpVtbl->put_Direction(This,dir)
#define INetFwRule_get_Interfaces(This,interfaces) (This)->lpVtbl->get_Interfaces(This,interfaces)
#define INetFwRule_put_Interfaces(This,interfaces) (This)->lpVtbl->put_Interfaces(This,interfaces)
#define INetFwRule_get_InterfaceTypes(This,types) (This)->lpVtbl->get_InterfaceTypes(This,types)
#define INetFwRule_put_InterfaceTypes(This,types) (This)->lpVtbl->put_InterfaceTypes(This,types)
#define INetFwRule_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define INetFwRule_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#define INetFwRule_get_Grouping(This,context) (This)->lpVtbl->get_Grouping(This,context)
#define INetFwRule_put_Grouping(This,context) (This)->lpVtbl->put_Grouping(This,context)
#define INetFwRule_get_Profiles(This,profiles) (This)->lpVtbl->get_Profiles(This,profiles)
#define INetFwRule_put_Profiles(This,profiles) (This)->lpVtbl->put_Profiles(This,profiles)
#define INetFwRule_get_EdgeTraversal(This,enabled) (This)->lpVtbl->get_EdgeTraversal(This,enabled)
#define INetFwRule_put_EdgeTraversal(This,enabled) (This)->lpVtbl->put_EdgeTraversal(This,enabled)
#define INetFwRule_get_Action(This,action) (This)->lpVtbl->get_Action(This,action)
#define INetFwRule_put_Action(This,action) (This)->lpVtbl->put_Action(This,action)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwRule_QueryInterface(INetFwRule* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwRule_AddRef(INetFwRule* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwRule_Release(INetFwRule* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwRule_GetTypeInfoCount(INetFwRule* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwRule_GetTypeInfo(INetFwRule* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwRule_GetIDsOfNames(INetFwRule* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwRule_Invoke(INetFwRule* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwRule methods ***/
static inline HRESULT INetFwRule_get_Name(INetFwRule* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT INetFwRule_put_Name(INetFwRule* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT INetFwRule_get_Description(INetFwRule* This,BSTR *desc) {
    return This->lpVtbl->get_Description(This,desc);
}
static inline HRESULT INetFwRule_put_Description(INetFwRule* This,BSTR desc) {
    return This->lpVtbl->put_Description(This,desc);
}
static inline HRESULT INetFwRule_get_ApplicationName(INetFwRule* This,BSTR *imagename) {
    return This->lpVtbl->get_ApplicationName(This,imagename);
}
static inline HRESULT INetFwRule_put_ApplicationName(INetFwRule* This,BSTR imagename) {
    return This->lpVtbl->put_ApplicationName(This,imagename);
}
static inline HRESULT INetFwRule_get_ServiceName(INetFwRule* This,BSTR *service) {
    return This->lpVtbl->get_ServiceName(This,service);
}
static inline HRESULT INetFwRule_put_ServiceName(INetFwRule* This,BSTR service) {
    return This->lpVtbl->put_ServiceName(This,service);
}
static inline HRESULT INetFwRule_get_Protocol(INetFwRule* This,LONG *protocol) {
    return This->lpVtbl->get_Protocol(This,protocol);
}
static inline HRESULT INetFwRule_put_Protocol(INetFwRule* This,LONG protocol) {
    return This->lpVtbl->put_Protocol(This,protocol);
}
static inline HRESULT INetFwRule_get_LocalPorts(INetFwRule* This,BSTR *ports) {
    return This->lpVtbl->get_LocalPorts(This,ports);
}
static inline HRESULT INetFwRule_put_LocalPorts(INetFwRule* This,BSTR ports) {
    return This->lpVtbl->put_LocalPorts(This,ports);
}
static inline HRESULT INetFwRule_get_RemotePorts(INetFwRule* This,BSTR *ports) {
    return This->lpVtbl->get_RemotePorts(This,ports);
}
static inline HRESULT INetFwRule_put_RemotePorts(INetFwRule* This,BSTR ports) {
    return This->lpVtbl->put_RemotePorts(This,ports);
}
static inline HRESULT INetFwRule_get_LocalAddresses(INetFwRule* This,BSTR *address) {
    return This->lpVtbl->get_LocalAddresses(This,address);
}
static inline HRESULT INetFwRule_put_LocalAddresses(INetFwRule* This,BSTR address) {
    return This->lpVtbl->put_LocalAddresses(This,address);
}
static inline HRESULT INetFwRule_get_RemoteAddresses(INetFwRule* This,BSTR *address) {
    return This->lpVtbl->get_RemoteAddresses(This,address);
}
static inline HRESULT INetFwRule_put_RemoteAddresses(INetFwRule* This,BSTR address) {
    return This->lpVtbl->put_RemoteAddresses(This,address);
}
static inline HRESULT INetFwRule_get_IcmpTypesAndCodes(INetFwRule* This,BSTR *codes) {
    return This->lpVtbl->get_IcmpTypesAndCodes(This,codes);
}
static inline HRESULT INetFwRule_put_IcmpTypesAndCodes(INetFwRule* This,BSTR codes) {
    return This->lpVtbl->put_IcmpTypesAndCodes(This,codes);
}
static inline HRESULT INetFwRule_get_Direction(INetFwRule* This,NET_FW_RULE_DIRECTION *dir) {
    return This->lpVtbl->get_Direction(This,dir);
}
static inline HRESULT INetFwRule_put_Direction(INetFwRule* This,NET_FW_RULE_DIRECTION dir) {
    return This->lpVtbl->put_Direction(This,dir);
}
static inline HRESULT INetFwRule_get_Interfaces(INetFwRule* This,VARIANT *interfaces) {
    return This->lpVtbl->get_Interfaces(This,interfaces);
}
static inline HRESULT INetFwRule_put_Interfaces(INetFwRule* This,VARIANT interfaces) {
    return This->lpVtbl->put_Interfaces(This,interfaces);
}
static inline HRESULT INetFwRule_get_InterfaceTypes(INetFwRule* This,BSTR *types) {
    return This->lpVtbl->get_InterfaceTypes(This,types);
}
static inline HRESULT INetFwRule_put_InterfaceTypes(INetFwRule* This,BSTR types) {
    return This->lpVtbl->put_InterfaceTypes(This,types);
}
static inline HRESULT INetFwRule_get_Enabled(INetFwRule* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT INetFwRule_put_Enabled(INetFwRule* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
static inline HRESULT INetFwRule_get_Grouping(INetFwRule* This,BSTR *context) {
    return This->lpVtbl->get_Grouping(This,context);
}
static inline HRESULT INetFwRule_put_Grouping(INetFwRule* This,BSTR context) {
    return This->lpVtbl->put_Grouping(This,context);
}
static inline HRESULT INetFwRule_get_Profiles(INetFwRule* This,LONG *profiles) {
    return This->lpVtbl->get_Profiles(This,profiles);
}
static inline HRESULT INetFwRule_put_Profiles(INetFwRule* This,LONG profiles) {
    return This->lpVtbl->put_Profiles(This,profiles);
}
static inline HRESULT INetFwRule_get_EdgeTraversal(INetFwRule* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_EdgeTraversal(This,enabled);
}
static inline HRESULT INetFwRule_put_EdgeTraversal(INetFwRule* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_EdgeTraversal(This,enabled);
}
static inline HRESULT INetFwRule_get_Action(INetFwRule* This,NET_FW_ACTION *action) {
    return This->lpVtbl->get_Action(This,action);
}
static inline HRESULT INetFwRule_put_Action(INetFwRule* This,NET_FW_ACTION action) {
    return This->lpVtbl->put_Action(This,action);
}
#endif
#endif

#endif


#endif  /* __INetFwRule_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwRule2 interface
 */
#ifndef __INetFwRule2_INTERFACE_DEFINED__
#define __INetFwRule2_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwRule2, 0x9c27c8da, 0x189b, 0x4dde, 0x89,0xf7, 0x8b,0x39,0xa3,0x16,0x78,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c27c8da-189b-4dde-89f7-8b39a316782c")
INetFwRule2 : public INetFwRule
{
    virtual HRESULT STDMETHODCALLTYPE get_EdgeTraversalOptions(
        LONG *lOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EdgeTraversalOptions(
        LONG lOptions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwRule2, 0x9c27c8da, 0x189b, 0x4dde, 0x89,0xf7, 0x8b,0x39,0xa3,0x16,0x78,0x2c)
#endif
#else
typedef struct INetFwRule2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwRule2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwRule2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwRule2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwRule2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwRule2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwRule2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwRule2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwRule methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        INetFwRule2 *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        INetFwRule2 *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        INetFwRule2 *This,
        BSTR *desc);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        INetFwRule2 *This,
        BSTR desc);

    HRESULT (STDMETHODCALLTYPE *get_ApplicationName)(
        INetFwRule2 *This,
        BSTR *imagename);

    HRESULT (STDMETHODCALLTYPE *put_ApplicationName)(
        INetFwRule2 *This,
        BSTR imagename);

    HRESULT (STDMETHODCALLTYPE *get_ServiceName)(
        INetFwRule2 *This,
        BSTR *service);

    HRESULT (STDMETHODCALLTYPE *put_ServiceName)(
        INetFwRule2 *This,
        BSTR service);

    HRESULT (STDMETHODCALLTYPE *get_Protocol)(
        INetFwRule2 *This,
        LONG *protocol);

    HRESULT (STDMETHODCALLTYPE *put_Protocol)(
        INetFwRule2 *This,
        LONG protocol);

    HRESULT (STDMETHODCALLTYPE *get_LocalPorts)(
        INetFwRule2 *This,
        BSTR *ports);

    HRESULT (STDMETHODCALLTYPE *put_LocalPorts)(
        INetFwRule2 *This,
        BSTR ports);

    HRESULT (STDMETHODCALLTYPE *get_RemotePorts)(
        INetFwRule2 *This,
        BSTR *ports);

    HRESULT (STDMETHODCALLTYPE *put_RemotePorts)(
        INetFwRule2 *This,
        BSTR ports);

    HRESULT (STDMETHODCALLTYPE *get_LocalAddresses)(
        INetFwRule2 *This,
        BSTR *address);

    HRESULT (STDMETHODCALLTYPE *put_LocalAddresses)(
        INetFwRule2 *This,
        BSTR address);

    HRESULT (STDMETHODCALLTYPE *get_RemoteAddresses)(
        INetFwRule2 *This,
        BSTR *address);

    HRESULT (STDMETHODCALLTYPE *put_RemoteAddresses)(
        INetFwRule2 *This,
        BSTR address);

    HRESULT (STDMETHODCALLTYPE *get_IcmpTypesAndCodes)(
        INetFwRule2 *This,
        BSTR *codes);

    HRESULT (STDMETHODCALLTYPE *put_IcmpTypesAndCodes)(
        INetFwRule2 *This,
        BSTR codes);

    HRESULT (STDMETHODCALLTYPE *get_Direction)(
        INetFwRule2 *This,
        NET_FW_RULE_DIRECTION *dir);

    HRESULT (STDMETHODCALLTYPE *put_Direction)(
        INetFwRule2 *This,
        NET_FW_RULE_DIRECTION dir);

    HRESULT (STDMETHODCALLTYPE *get_Interfaces)(
        INetFwRule2 *This,
        VARIANT *interfaces);

    HRESULT (STDMETHODCALLTYPE *put_Interfaces)(
        INetFwRule2 *This,
        VARIANT interfaces);

    HRESULT (STDMETHODCALLTYPE *get_InterfaceTypes)(
        INetFwRule2 *This,
        BSTR *types);

    HRESULT (STDMETHODCALLTYPE *put_InterfaceTypes)(
        INetFwRule2 *This,
        BSTR types);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        INetFwRule2 *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        INetFwRule2 *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_Grouping)(
        INetFwRule2 *This,
        BSTR *context);

    HRESULT (STDMETHODCALLTYPE *put_Grouping)(
        INetFwRule2 *This,
        BSTR context);

    HRESULT (STDMETHODCALLTYPE *get_Profiles)(
        INetFwRule2 *This,
        LONG *profiles);

    HRESULT (STDMETHODCALLTYPE *put_Profiles)(
        INetFwRule2 *This,
        LONG profiles);

    HRESULT (STDMETHODCALLTYPE *get_EdgeTraversal)(
        INetFwRule2 *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_EdgeTraversal)(
        INetFwRule2 *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_Action)(
        INetFwRule2 *This,
        NET_FW_ACTION *action);

    HRESULT (STDMETHODCALLTYPE *put_Action)(
        INetFwRule2 *This,
        NET_FW_ACTION action);

    /*** INetFwRule2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_EdgeTraversalOptions)(
        INetFwRule2 *This,
        LONG *lOptions);

    HRESULT (STDMETHODCALLTYPE *put_EdgeTraversalOptions)(
        INetFwRule2 *This,
        LONG lOptions);

    END_INTERFACE
} INetFwRule2Vtbl;

interface INetFwRule2 {
    CONST_VTBL INetFwRule2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwRule2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwRule2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwRule2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwRule2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwRule2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwRule2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwRule2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwRule methods ***/
#define INetFwRule2_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define INetFwRule2_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define INetFwRule2_get_Description(This,desc) (This)->lpVtbl->get_Description(This,desc)
#define INetFwRule2_put_Description(This,desc) (This)->lpVtbl->put_Description(This,desc)
#define INetFwRule2_get_ApplicationName(This,imagename) (This)->lpVtbl->get_ApplicationName(This,imagename)
#define INetFwRule2_put_ApplicationName(This,imagename) (This)->lpVtbl->put_ApplicationName(This,imagename)
#define INetFwRule2_get_ServiceName(This,service) (This)->lpVtbl->get_ServiceName(This,service)
#define INetFwRule2_put_ServiceName(This,service) (This)->lpVtbl->put_ServiceName(This,service)
#define INetFwRule2_get_Protocol(This,protocol) (This)->lpVtbl->get_Protocol(This,protocol)
#define INetFwRule2_put_Protocol(This,protocol) (This)->lpVtbl->put_Protocol(This,protocol)
#define INetFwRule2_get_LocalPorts(This,ports) (This)->lpVtbl->get_LocalPorts(This,ports)
#define INetFwRule2_put_LocalPorts(This,ports) (This)->lpVtbl->put_LocalPorts(This,ports)
#define INetFwRule2_get_RemotePorts(This,ports) (This)->lpVtbl->get_RemotePorts(This,ports)
#define INetFwRule2_put_RemotePorts(This,ports) (This)->lpVtbl->put_RemotePorts(This,ports)
#define INetFwRule2_get_LocalAddresses(This,address) (This)->lpVtbl->get_LocalAddresses(This,address)
#define INetFwRule2_put_LocalAddresses(This,address) (This)->lpVtbl->put_LocalAddresses(This,address)
#define INetFwRule2_get_RemoteAddresses(This,address) (This)->lpVtbl->get_RemoteAddresses(This,address)
#define INetFwRule2_put_RemoteAddresses(This,address) (This)->lpVtbl->put_RemoteAddresses(This,address)
#define INetFwRule2_get_IcmpTypesAndCodes(This,codes) (This)->lpVtbl->get_IcmpTypesAndCodes(This,codes)
#define INetFwRule2_put_IcmpTypesAndCodes(This,codes) (This)->lpVtbl->put_IcmpTypesAndCodes(This,codes)
#define INetFwRule2_get_Direction(This,dir) (This)->lpVtbl->get_Direction(This,dir)
#define INetFwRule2_put_Direction(This,dir) (This)->lpVtbl->put_Direction(This,dir)
#define INetFwRule2_get_Interfaces(This,interfaces) (This)->lpVtbl->get_Interfaces(This,interfaces)
#define INetFwRule2_put_Interfaces(This,interfaces) (This)->lpVtbl->put_Interfaces(This,interfaces)
#define INetFwRule2_get_InterfaceTypes(This,types) (This)->lpVtbl->get_InterfaceTypes(This,types)
#define INetFwRule2_put_InterfaceTypes(This,types) (This)->lpVtbl->put_InterfaceTypes(This,types)
#define INetFwRule2_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define INetFwRule2_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#define INetFwRule2_get_Grouping(This,context) (This)->lpVtbl->get_Grouping(This,context)
#define INetFwRule2_put_Grouping(This,context) (This)->lpVtbl->put_Grouping(This,context)
#define INetFwRule2_get_Profiles(This,profiles) (This)->lpVtbl->get_Profiles(This,profiles)
#define INetFwRule2_put_Profiles(This,profiles) (This)->lpVtbl->put_Profiles(This,profiles)
#define INetFwRule2_get_EdgeTraversal(This,enabled) (This)->lpVtbl->get_EdgeTraversal(This,enabled)
#define INetFwRule2_put_EdgeTraversal(This,enabled) (This)->lpVtbl->put_EdgeTraversal(This,enabled)
#define INetFwRule2_get_Action(This,action) (This)->lpVtbl->get_Action(This,action)
#define INetFwRule2_put_Action(This,action) (This)->lpVtbl->put_Action(This,action)
/*** INetFwRule2 methods ***/
#define INetFwRule2_get_EdgeTraversalOptions(This,lOptions) (This)->lpVtbl->get_EdgeTraversalOptions(This,lOptions)
#define INetFwRule2_put_EdgeTraversalOptions(This,lOptions) (This)->lpVtbl->put_EdgeTraversalOptions(This,lOptions)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwRule2_QueryInterface(INetFwRule2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwRule2_AddRef(INetFwRule2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwRule2_Release(INetFwRule2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwRule2_GetTypeInfoCount(INetFwRule2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwRule2_GetTypeInfo(INetFwRule2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwRule2_GetIDsOfNames(INetFwRule2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwRule2_Invoke(INetFwRule2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwRule methods ***/
static inline HRESULT INetFwRule2_get_Name(INetFwRule2* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT INetFwRule2_put_Name(INetFwRule2* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT INetFwRule2_get_Description(INetFwRule2* This,BSTR *desc) {
    return This->lpVtbl->get_Description(This,desc);
}
static inline HRESULT INetFwRule2_put_Description(INetFwRule2* This,BSTR desc) {
    return This->lpVtbl->put_Description(This,desc);
}
static inline HRESULT INetFwRule2_get_ApplicationName(INetFwRule2* This,BSTR *imagename) {
    return This->lpVtbl->get_ApplicationName(This,imagename);
}
static inline HRESULT INetFwRule2_put_ApplicationName(INetFwRule2* This,BSTR imagename) {
    return This->lpVtbl->put_ApplicationName(This,imagename);
}
static inline HRESULT INetFwRule2_get_ServiceName(INetFwRule2* This,BSTR *service) {
    return This->lpVtbl->get_ServiceName(This,service);
}
static inline HRESULT INetFwRule2_put_ServiceName(INetFwRule2* This,BSTR service) {
    return This->lpVtbl->put_ServiceName(This,service);
}
static inline HRESULT INetFwRule2_get_Protocol(INetFwRule2* This,LONG *protocol) {
    return This->lpVtbl->get_Protocol(This,protocol);
}
static inline HRESULT INetFwRule2_put_Protocol(INetFwRule2* This,LONG protocol) {
    return This->lpVtbl->put_Protocol(This,protocol);
}
static inline HRESULT INetFwRule2_get_LocalPorts(INetFwRule2* This,BSTR *ports) {
    return This->lpVtbl->get_LocalPorts(This,ports);
}
static inline HRESULT INetFwRule2_put_LocalPorts(INetFwRule2* This,BSTR ports) {
    return This->lpVtbl->put_LocalPorts(This,ports);
}
static inline HRESULT INetFwRule2_get_RemotePorts(INetFwRule2* This,BSTR *ports) {
    return This->lpVtbl->get_RemotePorts(This,ports);
}
static inline HRESULT INetFwRule2_put_RemotePorts(INetFwRule2* This,BSTR ports) {
    return This->lpVtbl->put_RemotePorts(This,ports);
}
static inline HRESULT INetFwRule2_get_LocalAddresses(INetFwRule2* This,BSTR *address) {
    return This->lpVtbl->get_LocalAddresses(This,address);
}
static inline HRESULT INetFwRule2_put_LocalAddresses(INetFwRule2* This,BSTR address) {
    return This->lpVtbl->put_LocalAddresses(This,address);
}
static inline HRESULT INetFwRule2_get_RemoteAddresses(INetFwRule2* This,BSTR *address) {
    return This->lpVtbl->get_RemoteAddresses(This,address);
}
static inline HRESULT INetFwRule2_put_RemoteAddresses(INetFwRule2* This,BSTR address) {
    return This->lpVtbl->put_RemoteAddresses(This,address);
}
static inline HRESULT INetFwRule2_get_IcmpTypesAndCodes(INetFwRule2* This,BSTR *codes) {
    return This->lpVtbl->get_IcmpTypesAndCodes(This,codes);
}
static inline HRESULT INetFwRule2_put_IcmpTypesAndCodes(INetFwRule2* This,BSTR codes) {
    return This->lpVtbl->put_IcmpTypesAndCodes(This,codes);
}
static inline HRESULT INetFwRule2_get_Direction(INetFwRule2* This,NET_FW_RULE_DIRECTION *dir) {
    return This->lpVtbl->get_Direction(This,dir);
}
static inline HRESULT INetFwRule2_put_Direction(INetFwRule2* This,NET_FW_RULE_DIRECTION dir) {
    return This->lpVtbl->put_Direction(This,dir);
}
static inline HRESULT INetFwRule2_get_Interfaces(INetFwRule2* This,VARIANT *interfaces) {
    return This->lpVtbl->get_Interfaces(This,interfaces);
}
static inline HRESULT INetFwRule2_put_Interfaces(INetFwRule2* This,VARIANT interfaces) {
    return This->lpVtbl->put_Interfaces(This,interfaces);
}
static inline HRESULT INetFwRule2_get_InterfaceTypes(INetFwRule2* This,BSTR *types) {
    return This->lpVtbl->get_InterfaceTypes(This,types);
}
static inline HRESULT INetFwRule2_put_InterfaceTypes(INetFwRule2* This,BSTR types) {
    return This->lpVtbl->put_InterfaceTypes(This,types);
}
static inline HRESULT INetFwRule2_get_Enabled(INetFwRule2* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT INetFwRule2_put_Enabled(INetFwRule2* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
static inline HRESULT INetFwRule2_get_Grouping(INetFwRule2* This,BSTR *context) {
    return This->lpVtbl->get_Grouping(This,context);
}
static inline HRESULT INetFwRule2_put_Grouping(INetFwRule2* This,BSTR context) {
    return This->lpVtbl->put_Grouping(This,context);
}
static inline HRESULT INetFwRule2_get_Profiles(INetFwRule2* This,LONG *profiles) {
    return This->lpVtbl->get_Profiles(This,profiles);
}
static inline HRESULT INetFwRule2_put_Profiles(INetFwRule2* This,LONG profiles) {
    return This->lpVtbl->put_Profiles(This,profiles);
}
static inline HRESULT INetFwRule2_get_EdgeTraversal(INetFwRule2* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_EdgeTraversal(This,enabled);
}
static inline HRESULT INetFwRule2_put_EdgeTraversal(INetFwRule2* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_EdgeTraversal(This,enabled);
}
static inline HRESULT INetFwRule2_get_Action(INetFwRule2* This,NET_FW_ACTION *action) {
    return This->lpVtbl->get_Action(This,action);
}
static inline HRESULT INetFwRule2_put_Action(INetFwRule2* This,NET_FW_ACTION action) {
    return This->lpVtbl->put_Action(This,action);
}
/*** INetFwRule2 methods ***/
static inline HRESULT INetFwRule2_get_EdgeTraversalOptions(INetFwRule2* This,LONG *lOptions) {
    return This->lpVtbl->get_EdgeTraversalOptions(This,lOptions);
}
static inline HRESULT INetFwRule2_put_EdgeTraversalOptions(INetFwRule2* This,LONG lOptions) {
    return This->lpVtbl->put_EdgeTraversalOptions(This,lOptions);
}
#endif
#endif

#endif


#endif  /* __INetFwRule2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwRule3 interface
 */
#ifndef __INetFwRule3_INTERFACE_DEFINED__
#define __INetFwRule3_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwRule3, 0xb21563ff, 0xd696, 0x4222, 0xab,0x46, 0x4e,0x89,0xb7,0x3a,0xb3,0x4a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b21563ff-d696-4222-ab46-4e89b73ab34a")
INetFwRule3 : public INetFwRule2
{
    virtual HRESULT STDMETHODCALLTYPE get_LocalAppPackageId(
        BSTR *wszPackageId) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_LocalAppPackageId(
        BSTR wszPackageId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalUserOwner(
        BSTR *wszUserOwner) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_LocalUserOwner(
        BSTR wszUserOwner) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalUserAuthorizedList(
        BSTR *wszUserAuthList) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_LocalUserAuthorizedList(
        BSTR wszUserAuthList) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteUserAuthorizedList(
        BSTR *wszUserAuthList) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RemoteUserAuthorizedList(
        BSTR wszUserAuthList) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteMachineAuthorizedList(
        BSTR *wszUserAuthList) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RemoteMachineAuthorizedList(
        BSTR wszUserAuthList) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SecureFlags(
        LONG *lOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SecureFlags(
        LONG lOptions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwRule3, 0xb21563ff, 0xd696, 0x4222, 0xab,0x46, 0x4e,0x89,0xb7,0x3a,0xb3,0x4a)
#endif
#else
typedef struct INetFwRule3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwRule3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwRule3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwRule3 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwRule3 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwRule3 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwRule3 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwRule3 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwRule methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        INetFwRule3 *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        INetFwRule3 *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        INetFwRule3 *This,
        BSTR *desc);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        INetFwRule3 *This,
        BSTR desc);

    HRESULT (STDMETHODCALLTYPE *get_ApplicationName)(
        INetFwRule3 *This,
        BSTR *imagename);

    HRESULT (STDMETHODCALLTYPE *put_ApplicationName)(
        INetFwRule3 *This,
        BSTR imagename);

    HRESULT (STDMETHODCALLTYPE *get_ServiceName)(
        INetFwRule3 *This,
        BSTR *service);

    HRESULT (STDMETHODCALLTYPE *put_ServiceName)(
        INetFwRule3 *This,
        BSTR service);

    HRESULT (STDMETHODCALLTYPE *get_Protocol)(
        INetFwRule3 *This,
        LONG *protocol);

    HRESULT (STDMETHODCALLTYPE *put_Protocol)(
        INetFwRule3 *This,
        LONG protocol);

    HRESULT (STDMETHODCALLTYPE *get_LocalPorts)(
        INetFwRule3 *This,
        BSTR *ports);

    HRESULT (STDMETHODCALLTYPE *put_LocalPorts)(
        INetFwRule3 *This,
        BSTR ports);

    HRESULT (STDMETHODCALLTYPE *get_RemotePorts)(
        INetFwRule3 *This,
        BSTR *ports);

    HRESULT (STDMETHODCALLTYPE *put_RemotePorts)(
        INetFwRule3 *This,
        BSTR ports);

    HRESULT (STDMETHODCALLTYPE *get_LocalAddresses)(
        INetFwRule3 *This,
        BSTR *address);

    HRESULT (STDMETHODCALLTYPE *put_LocalAddresses)(
        INetFwRule3 *This,
        BSTR address);

    HRESULT (STDMETHODCALLTYPE *get_RemoteAddresses)(
        INetFwRule3 *This,
        BSTR *address);

    HRESULT (STDMETHODCALLTYPE *put_RemoteAddresses)(
        INetFwRule3 *This,
        BSTR address);

    HRESULT (STDMETHODCALLTYPE *get_IcmpTypesAndCodes)(
        INetFwRule3 *This,
        BSTR *codes);

    HRESULT (STDMETHODCALLTYPE *put_IcmpTypesAndCodes)(
        INetFwRule3 *This,
        BSTR codes);

    HRESULT (STDMETHODCALLTYPE *get_Direction)(
        INetFwRule3 *This,
        NET_FW_RULE_DIRECTION *dir);

    HRESULT (STDMETHODCALLTYPE *put_Direction)(
        INetFwRule3 *This,
        NET_FW_RULE_DIRECTION dir);

    HRESULT (STDMETHODCALLTYPE *get_Interfaces)(
        INetFwRule3 *This,
        VARIANT *interfaces);

    HRESULT (STDMETHODCALLTYPE *put_Interfaces)(
        INetFwRule3 *This,
        VARIANT interfaces);

    HRESULT (STDMETHODCALLTYPE *get_InterfaceTypes)(
        INetFwRule3 *This,
        BSTR *types);

    HRESULT (STDMETHODCALLTYPE *put_InterfaceTypes)(
        INetFwRule3 *This,
        BSTR types);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        INetFwRule3 *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        INetFwRule3 *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_Grouping)(
        INetFwRule3 *This,
        BSTR *context);

    HRESULT (STDMETHODCALLTYPE *put_Grouping)(
        INetFwRule3 *This,
        BSTR context);

    HRESULT (STDMETHODCALLTYPE *get_Profiles)(
        INetFwRule3 *This,
        LONG *profiles);

    HRESULT (STDMETHODCALLTYPE *put_Profiles)(
        INetFwRule3 *This,
        LONG profiles);

    HRESULT (STDMETHODCALLTYPE *get_EdgeTraversal)(
        INetFwRule3 *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_EdgeTraversal)(
        INetFwRule3 *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_Action)(
        INetFwRule3 *This,
        NET_FW_ACTION *action);

    HRESULT (STDMETHODCALLTYPE *put_Action)(
        INetFwRule3 *This,
        NET_FW_ACTION action);

    /*** INetFwRule2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_EdgeTraversalOptions)(
        INetFwRule3 *This,
        LONG *lOptions);

    HRESULT (STDMETHODCALLTYPE *put_EdgeTraversalOptions)(
        INetFwRule3 *This,
        LONG lOptions);

    /*** INetFwRule3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_LocalAppPackageId)(
        INetFwRule3 *This,
        BSTR *wszPackageId);

    HRESULT (STDMETHODCALLTYPE *put_LocalAppPackageId)(
        INetFwRule3 *This,
        BSTR wszPackageId);

    HRESULT (STDMETHODCALLTYPE *get_LocalUserOwner)(
        INetFwRule3 *This,
        BSTR *wszUserOwner);

    HRESULT (STDMETHODCALLTYPE *put_LocalUserOwner)(
        INetFwRule3 *This,
        BSTR wszUserOwner);

    HRESULT (STDMETHODCALLTYPE *get_LocalUserAuthorizedList)(
        INetFwRule3 *This,
        BSTR *wszUserAuthList);

    HRESULT (STDMETHODCALLTYPE *put_LocalUserAuthorizedList)(
        INetFwRule3 *This,
        BSTR wszUserAuthList);

    HRESULT (STDMETHODCALLTYPE *get_RemoteUserAuthorizedList)(
        INetFwRule3 *This,
        BSTR *wszUserAuthList);

    HRESULT (STDMETHODCALLTYPE *put_RemoteUserAuthorizedList)(
        INetFwRule3 *This,
        BSTR wszUserAuthList);

    HRESULT (STDMETHODCALLTYPE *get_RemoteMachineAuthorizedList)(
        INetFwRule3 *This,
        BSTR *wszUserAuthList);

    HRESULT (STDMETHODCALLTYPE *put_RemoteMachineAuthorizedList)(
        INetFwRule3 *This,
        BSTR wszUserAuthList);

    HRESULT (STDMETHODCALLTYPE *get_SecureFlags)(
        INetFwRule3 *This,
        LONG *lOptions);

    HRESULT (STDMETHODCALLTYPE *put_SecureFlags)(
        INetFwRule3 *This,
        LONG lOptions);

    END_INTERFACE
} INetFwRule3Vtbl;

interface INetFwRule3 {
    CONST_VTBL INetFwRule3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwRule3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwRule3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwRule3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwRule3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwRule3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwRule3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwRule3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwRule methods ***/
#define INetFwRule3_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define INetFwRule3_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define INetFwRule3_get_Description(This,desc) (This)->lpVtbl->get_Description(This,desc)
#define INetFwRule3_put_Description(This,desc) (This)->lpVtbl->put_Description(This,desc)
#define INetFwRule3_get_ApplicationName(This,imagename) (This)->lpVtbl->get_ApplicationName(This,imagename)
#define INetFwRule3_put_ApplicationName(This,imagename) (This)->lpVtbl->put_ApplicationName(This,imagename)
#define INetFwRule3_get_ServiceName(This,service) (This)->lpVtbl->get_ServiceName(This,service)
#define INetFwRule3_put_ServiceName(This,service) (This)->lpVtbl->put_ServiceName(This,service)
#define INetFwRule3_get_Protocol(This,protocol) (This)->lpVtbl->get_Protocol(This,protocol)
#define INetFwRule3_put_Protocol(This,protocol) (This)->lpVtbl->put_Protocol(This,protocol)
#define INetFwRule3_get_LocalPorts(This,ports) (This)->lpVtbl->get_LocalPorts(This,ports)
#define INetFwRule3_put_LocalPorts(This,ports) (This)->lpVtbl->put_LocalPorts(This,ports)
#define INetFwRule3_get_RemotePorts(This,ports) (This)->lpVtbl->get_RemotePorts(This,ports)
#define INetFwRule3_put_RemotePorts(This,ports) (This)->lpVtbl->put_RemotePorts(This,ports)
#define INetFwRule3_get_LocalAddresses(This,address) (This)->lpVtbl->get_LocalAddresses(This,address)
#define INetFwRule3_put_LocalAddresses(This,address) (This)->lpVtbl->put_LocalAddresses(This,address)
#define INetFwRule3_get_RemoteAddresses(This,address) (This)->lpVtbl->get_RemoteAddresses(This,address)
#define INetFwRule3_put_RemoteAddresses(This,address) (This)->lpVtbl->put_RemoteAddresses(This,address)
#define INetFwRule3_get_IcmpTypesAndCodes(This,codes) (This)->lpVtbl->get_IcmpTypesAndCodes(This,codes)
#define INetFwRule3_put_IcmpTypesAndCodes(This,codes) (This)->lpVtbl->put_IcmpTypesAndCodes(This,codes)
#define INetFwRule3_get_Direction(This,dir) (This)->lpVtbl->get_Direction(This,dir)
#define INetFwRule3_put_Direction(This,dir) (This)->lpVtbl->put_Direction(This,dir)
#define INetFwRule3_get_Interfaces(This,interfaces) (This)->lpVtbl->get_Interfaces(This,interfaces)
#define INetFwRule3_put_Interfaces(This,interfaces) (This)->lpVtbl->put_Interfaces(This,interfaces)
#define INetFwRule3_get_InterfaceTypes(This,types) (This)->lpVtbl->get_InterfaceTypes(This,types)
#define INetFwRule3_put_InterfaceTypes(This,types) (This)->lpVtbl->put_InterfaceTypes(This,types)
#define INetFwRule3_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define INetFwRule3_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#define INetFwRule3_get_Grouping(This,context) (This)->lpVtbl->get_Grouping(This,context)
#define INetFwRule3_put_Grouping(This,context) (This)->lpVtbl->put_Grouping(This,context)
#define INetFwRule3_get_Profiles(This,profiles) (This)->lpVtbl->get_Profiles(This,profiles)
#define INetFwRule3_put_Profiles(This,profiles) (This)->lpVtbl->put_Profiles(This,profiles)
#define INetFwRule3_get_EdgeTraversal(This,enabled) (This)->lpVtbl->get_EdgeTraversal(This,enabled)
#define INetFwRule3_put_EdgeTraversal(This,enabled) (This)->lpVtbl->put_EdgeTraversal(This,enabled)
#define INetFwRule3_get_Action(This,action) (This)->lpVtbl->get_Action(This,action)
#define INetFwRule3_put_Action(This,action) (This)->lpVtbl->put_Action(This,action)
/*** INetFwRule2 methods ***/
#define INetFwRule3_get_EdgeTraversalOptions(This,lOptions) (This)->lpVtbl->get_EdgeTraversalOptions(This,lOptions)
#define INetFwRule3_put_EdgeTraversalOptions(This,lOptions) (This)->lpVtbl->put_EdgeTraversalOptions(This,lOptions)
/*** INetFwRule3 methods ***/
#define INetFwRule3_get_LocalAppPackageId(This,wszPackageId) (This)->lpVtbl->get_LocalAppPackageId(This,wszPackageId)
#define INetFwRule3_put_LocalAppPackageId(This,wszPackageId) (This)->lpVtbl->put_LocalAppPackageId(This,wszPackageId)
#define INetFwRule3_get_LocalUserOwner(This,wszUserOwner) (This)->lpVtbl->get_LocalUserOwner(This,wszUserOwner)
#define INetFwRule3_put_LocalUserOwner(This,wszUserOwner) (This)->lpVtbl->put_LocalUserOwner(This,wszUserOwner)
#define INetFwRule3_get_LocalUserAuthorizedList(This,wszUserAuthList) (This)->lpVtbl->get_LocalUserAuthorizedList(This,wszUserAuthList)
#define INetFwRule3_put_LocalUserAuthorizedList(This,wszUserAuthList) (This)->lpVtbl->put_LocalUserAuthorizedList(This,wszUserAuthList)
#define INetFwRule3_get_RemoteUserAuthorizedList(This,wszUserAuthList) (This)->lpVtbl->get_RemoteUserAuthorizedList(This,wszUserAuthList)
#define INetFwRule3_put_RemoteUserAuthorizedList(This,wszUserAuthList) (This)->lpVtbl->put_RemoteUserAuthorizedList(This,wszUserAuthList)
#define INetFwRule3_get_RemoteMachineAuthorizedList(This,wszUserAuthList) (This)->lpVtbl->get_RemoteMachineAuthorizedList(This,wszUserAuthList)
#define INetFwRule3_put_RemoteMachineAuthorizedList(This,wszUserAuthList) (This)->lpVtbl->put_RemoteMachineAuthorizedList(This,wszUserAuthList)
#define INetFwRule3_get_SecureFlags(This,lOptions) (This)->lpVtbl->get_SecureFlags(This,lOptions)
#define INetFwRule3_put_SecureFlags(This,lOptions) (This)->lpVtbl->put_SecureFlags(This,lOptions)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwRule3_QueryInterface(INetFwRule3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwRule3_AddRef(INetFwRule3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwRule3_Release(INetFwRule3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwRule3_GetTypeInfoCount(INetFwRule3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwRule3_GetTypeInfo(INetFwRule3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwRule3_GetIDsOfNames(INetFwRule3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwRule3_Invoke(INetFwRule3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwRule methods ***/
static inline HRESULT INetFwRule3_get_Name(INetFwRule3* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT INetFwRule3_put_Name(INetFwRule3* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT INetFwRule3_get_Description(INetFwRule3* This,BSTR *desc) {
    return This->lpVtbl->get_Description(This,desc);
}
static inline HRESULT INetFwRule3_put_Description(INetFwRule3* This,BSTR desc) {
    return This->lpVtbl->put_Description(This,desc);
}
static inline HRESULT INetFwRule3_get_ApplicationName(INetFwRule3* This,BSTR *imagename) {
    return This->lpVtbl->get_ApplicationName(This,imagename);
}
static inline HRESULT INetFwRule3_put_ApplicationName(INetFwRule3* This,BSTR imagename) {
    return This->lpVtbl->put_ApplicationName(This,imagename);
}
static inline HRESULT INetFwRule3_get_ServiceName(INetFwRule3* This,BSTR *service) {
    return This->lpVtbl->get_ServiceName(This,service);
}
static inline HRESULT INetFwRule3_put_ServiceName(INetFwRule3* This,BSTR service) {
    return This->lpVtbl->put_ServiceName(This,service);
}
static inline HRESULT INetFwRule3_get_Protocol(INetFwRule3* This,LONG *protocol) {
    return This->lpVtbl->get_Protocol(This,protocol);
}
static inline HRESULT INetFwRule3_put_Protocol(INetFwRule3* This,LONG protocol) {
    return This->lpVtbl->put_Protocol(This,protocol);
}
static inline HRESULT INetFwRule3_get_LocalPorts(INetFwRule3* This,BSTR *ports) {
    return This->lpVtbl->get_LocalPorts(This,ports);
}
static inline HRESULT INetFwRule3_put_LocalPorts(INetFwRule3* This,BSTR ports) {
    return This->lpVtbl->put_LocalPorts(This,ports);
}
static inline HRESULT INetFwRule3_get_RemotePorts(INetFwRule3* This,BSTR *ports) {
    return This->lpVtbl->get_RemotePorts(This,ports);
}
static inline HRESULT INetFwRule3_put_RemotePorts(INetFwRule3* This,BSTR ports) {
    return This->lpVtbl->put_RemotePorts(This,ports);
}
static inline HRESULT INetFwRule3_get_LocalAddresses(INetFwRule3* This,BSTR *address) {
    return This->lpVtbl->get_LocalAddresses(This,address);
}
static inline HRESULT INetFwRule3_put_LocalAddresses(INetFwRule3* This,BSTR address) {
    return This->lpVtbl->put_LocalAddresses(This,address);
}
static inline HRESULT INetFwRule3_get_RemoteAddresses(INetFwRule3* This,BSTR *address) {
    return This->lpVtbl->get_RemoteAddresses(This,address);
}
static inline HRESULT INetFwRule3_put_RemoteAddresses(INetFwRule3* This,BSTR address) {
    return This->lpVtbl->put_RemoteAddresses(This,address);
}
static inline HRESULT INetFwRule3_get_IcmpTypesAndCodes(INetFwRule3* This,BSTR *codes) {
    return This->lpVtbl->get_IcmpTypesAndCodes(This,codes);
}
static inline HRESULT INetFwRule3_put_IcmpTypesAndCodes(INetFwRule3* This,BSTR codes) {
    return This->lpVtbl->put_IcmpTypesAndCodes(This,codes);
}
static inline HRESULT INetFwRule3_get_Direction(INetFwRule3* This,NET_FW_RULE_DIRECTION *dir) {
    return This->lpVtbl->get_Direction(This,dir);
}
static inline HRESULT INetFwRule3_put_Direction(INetFwRule3* This,NET_FW_RULE_DIRECTION dir) {
    return This->lpVtbl->put_Direction(This,dir);
}
static inline HRESULT INetFwRule3_get_Interfaces(INetFwRule3* This,VARIANT *interfaces) {
    return This->lpVtbl->get_Interfaces(This,interfaces);
}
static inline HRESULT INetFwRule3_put_Interfaces(INetFwRule3* This,VARIANT interfaces) {
    return This->lpVtbl->put_Interfaces(This,interfaces);
}
static inline HRESULT INetFwRule3_get_InterfaceTypes(INetFwRule3* This,BSTR *types) {
    return This->lpVtbl->get_InterfaceTypes(This,types);
}
static inline HRESULT INetFwRule3_put_InterfaceTypes(INetFwRule3* This,BSTR types) {
    return This->lpVtbl->put_InterfaceTypes(This,types);
}
static inline HRESULT INetFwRule3_get_Enabled(INetFwRule3* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT INetFwRule3_put_Enabled(INetFwRule3* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
static inline HRESULT INetFwRule3_get_Grouping(INetFwRule3* This,BSTR *context) {
    return This->lpVtbl->get_Grouping(This,context);
}
static inline HRESULT INetFwRule3_put_Grouping(INetFwRule3* This,BSTR context) {
    return This->lpVtbl->put_Grouping(This,context);
}
static inline HRESULT INetFwRule3_get_Profiles(INetFwRule3* This,LONG *profiles) {
    return This->lpVtbl->get_Profiles(This,profiles);
}
static inline HRESULT INetFwRule3_put_Profiles(INetFwRule3* This,LONG profiles) {
    return This->lpVtbl->put_Profiles(This,profiles);
}
static inline HRESULT INetFwRule3_get_EdgeTraversal(INetFwRule3* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_EdgeTraversal(This,enabled);
}
static inline HRESULT INetFwRule3_put_EdgeTraversal(INetFwRule3* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_EdgeTraversal(This,enabled);
}
static inline HRESULT INetFwRule3_get_Action(INetFwRule3* This,NET_FW_ACTION *action) {
    return This->lpVtbl->get_Action(This,action);
}
static inline HRESULT INetFwRule3_put_Action(INetFwRule3* This,NET_FW_ACTION action) {
    return This->lpVtbl->put_Action(This,action);
}
/*** INetFwRule2 methods ***/
static inline HRESULT INetFwRule3_get_EdgeTraversalOptions(INetFwRule3* This,LONG *lOptions) {
    return This->lpVtbl->get_EdgeTraversalOptions(This,lOptions);
}
static inline HRESULT INetFwRule3_put_EdgeTraversalOptions(INetFwRule3* This,LONG lOptions) {
    return This->lpVtbl->put_EdgeTraversalOptions(This,lOptions);
}
/*** INetFwRule3 methods ***/
static inline HRESULT INetFwRule3_get_LocalAppPackageId(INetFwRule3* This,BSTR *wszPackageId) {
    return This->lpVtbl->get_LocalAppPackageId(This,wszPackageId);
}
static inline HRESULT INetFwRule3_put_LocalAppPackageId(INetFwRule3* This,BSTR wszPackageId) {
    return This->lpVtbl->put_LocalAppPackageId(This,wszPackageId);
}
static inline HRESULT INetFwRule3_get_LocalUserOwner(INetFwRule3* This,BSTR *wszUserOwner) {
    return This->lpVtbl->get_LocalUserOwner(This,wszUserOwner);
}
static inline HRESULT INetFwRule3_put_LocalUserOwner(INetFwRule3* This,BSTR wszUserOwner) {
    return This->lpVtbl->put_LocalUserOwner(This,wszUserOwner);
}
static inline HRESULT INetFwRule3_get_LocalUserAuthorizedList(INetFwRule3* This,BSTR *wszUserAuthList) {
    return This->lpVtbl->get_LocalUserAuthorizedList(This,wszUserAuthList);
}
static inline HRESULT INetFwRule3_put_LocalUserAuthorizedList(INetFwRule3* This,BSTR wszUserAuthList) {
    return This->lpVtbl->put_LocalUserAuthorizedList(This,wszUserAuthList);
}
static inline HRESULT INetFwRule3_get_RemoteUserAuthorizedList(INetFwRule3* This,BSTR *wszUserAuthList) {
    return This->lpVtbl->get_RemoteUserAuthorizedList(This,wszUserAuthList);
}
static inline HRESULT INetFwRule3_put_RemoteUserAuthorizedList(INetFwRule3* This,BSTR wszUserAuthList) {
    return This->lpVtbl->put_RemoteUserAuthorizedList(This,wszUserAuthList);
}
static inline HRESULT INetFwRule3_get_RemoteMachineAuthorizedList(INetFwRule3* This,BSTR *wszUserAuthList) {
    return This->lpVtbl->get_RemoteMachineAuthorizedList(This,wszUserAuthList);
}
static inline HRESULT INetFwRule3_put_RemoteMachineAuthorizedList(INetFwRule3* This,BSTR wszUserAuthList) {
    return This->lpVtbl->put_RemoteMachineAuthorizedList(This,wszUserAuthList);
}
static inline HRESULT INetFwRule3_get_SecureFlags(INetFwRule3* This,LONG *lOptions) {
    return This->lpVtbl->get_SecureFlags(This,lOptions);
}
static inline HRESULT INetFwRule3_put_SecureFlags(INetFwRule3* This,LONG lOptions) {
    return This->lpVtbl->put_SecureFlags(This,lOptions);
}
#endif
#endif

#endif


#endif  /* __INetFwRule3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwRules interface
 */
#ifndef __INetFwRules_INTERFACE_DEFINED__
#define __INetFwRules_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwRules, 0x9c4c6277, 0x5027, 0x441e, 0xaf,0xae, 0xca,0x1f,0x54,0x2d,0xa0,0x09);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c4c6277-5027-441e-afae-ca1f542da009")
INetFwRules : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        INetFwRule *rule) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        BSTR name,
        INetFwRule **rule) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **newEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwRules, 0x9c4c6277, 0x5027, 0x441e, 0xaf,0xae, 0xca,0x1f,0x54,0x2d,0xa0,0x09)
#endif
#else
typedef struct INetFwRulesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwRules *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwRules *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwRules *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwRules *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwRules *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwRules *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwRules *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwRules methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        INetFwRules *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *Add)(
        INetFwRules *This,
        INetFwRule *rule);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        INetFwRules *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *Item)(
        INetFwRules *This,
        BSTR name,
        INetFwRule **rule);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        INetFwRules *This,
        IUnknown **newEnum);

    END_INTERFACE
} INetFwRulesVtbl;

interface INetFwRules {
    CONST_VTBL INetFwRulesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwRules_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwRules_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwRules_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwRules_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwRules_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwRules_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwRules_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwRules methods ***/
#define INetFwRules_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define INetFwRules_Add(This,rule) (This)->lpVtbl->Add(This,rule)
#define INetFwRules_Remove(This,name) (This)->lpVtbl->Remove(This,name)
#define INetFwRules_Item(This,name,rule) (This)->lpVtbl->Item(This,name,rule)
#define INetFwRules_get__NewEnum(This,newEnum) (This)->lpVtbl->get__NewEnum(This,newEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwRules_QueryInterface(INetFwRules* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwRules_AddRef(INetFwRules* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwRules_Release(INetFwRules* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwRules_GetTypeInfoCount(INetFwRules* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwRules_GetTypeInfo(INetFwRules* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwRules_GetIDsOfNames(INetFwRules* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwRules_Invoke(INetFwRules* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwRules methods ***/
static inline HRESULT INetFwRules_get_Count(INetFwRules* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT INetFwRules_Add(INetFwRules* This,INetFwRule *rule) {
    return This->lpVtbl->Add(This,rule);
}
static inline HRESULT INetFwRules_Remove(INetFwRules* This,BSTR name) {
    return This->lpVtbl->Remove(This,name);
}
static inline HRESULT INetFwRules_Item(INetFwRules* This,BSTR name,INetFwRule **rule) {
    return This->lpVtbl->Item(This,name,rule);
}
static inline HRESULT INetFwRules_get__NewEnum(INetFwRules* This,IUnknown **newEnum) {
    return This->lpVtbl->get__NewEnum(This,newEnum);
}
#endif
#endif

#endif


#endif  /* __INetFwRules_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwServiceRestriction interface
 */
#ifndef __INetFwServiceRestriction_INTERFACE_DEFINED__
#define __INetFwServiceRestriction_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwServiceRestriction, 0x8267bbe3, 0xf890, 0x491c, 0xb7,0xb6, 0x2d,0xb1,0xef,0x0e,0x5d,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8267bbe3-f890-491c-b7b6-2db1ef0e5d2b")
INetFwServiceRestriction : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE RestrictService(
        BSTR serviceName,
        BSTR appName,
        VARIANT_BOOL restrictService,
        VARIANT_BOOL serviceSidRestricted) = 0;

    virtual HRESULT STDMETHODCALLTYPE ServiceRestricted(
        BSTR serviceName,
        BSTR appName,
        VARIANT_BOOL *serviceRestricted) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Rules(
        INetFwRules **rules) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwServiceRestriction, 0x8267bbe3, 0xf890, 0x491c, 0xb7,0xb6, 0x2d,0xb1,0xef,0x0e,0x5d,0x2b)
#endif
#else
typedef struct INetFwServiceRestrictionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwServiceRestriction *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwServiceRestriction *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwServiceRestriction *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwServiceRestriction *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwServiceRestriction *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwServiceRestriction *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwServiceRestriction *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwServiceRestriction methods ***/
    HRESULT (STDMETHODCALLTYPE *RestrictService)(
        INetFwServiceRestriction *This,
        BSTR serviceName,
        BSTR appName,
        VARIANT_BOOL restrictService,
        VARIANT_BOOL serviceSidRestricted);

    HRESULT (STDMETHODCALLTYPE *ServiceRestricted)(
        INetFwServiceRestriction *This,
        BSTR serviceName,
        BSTR appName,
        VARIANT_BOOL *serviceRestricted);

    HRESULT (STDMETHODCALLTYPE *get_Rules)(
        INetFwServiceRestriction *This,
        INetFwRules **rules);

    END_INTERFACE
} INetFwServiceRestrictionVtbl;

interface INetFwServiceRestriction {
    CONST_VTBL INetFwServiceRestrictionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwServiceRestriction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwServiceRestriction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwServiceRestriction_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwServiceRestriction_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwServiceRestriction_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwServiceRestriction_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwServiceRestriction_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwServiceRestriction methods ***/
#define INetFwServiceRestriction_RestrictService(This,serviceName,appName,restrictService,serviceSidRestricted) (This)->lpVtbl->RestrictService(This,serviceName,appName,restrictService,serviceSidRestricted)
#define INetFwServiceRestriction_ServiceRestricted(This,serviceName,appName,serviceRestricted) (This)->lpVtbl->ServiceRestricted(This,serviceName,appName,serviceRestricted)
#define INetFwServiceRestriction_get_Rules(This,rules) (This)->lpVtbl->get_Rules(This,rules)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwServiceRestriction_QueryInterface(INetFwServiceRestriction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwServiceRestriction_AddRef(INetFwServiceRestriction* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwServiceRestriction_Release(INetFwServiceRestriction* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwServiceRestriction_GetTypeInfoCount(INetFwServiceRestriction* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwServiceRestriction_GetTypeInfo(INetFwServiceRestriction* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwServiceRestriction_GetIDsOfNames(INetFwServiceRestriction* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwServiceRestriction_Invoke(INetFwServiceRestriction* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwServiceRestriction methods ***/
static inline HRESULT INetFwServiceRestriction_RestrictService(INetFwServiceRestriction* This,BSTR serviceName,BSTR appName,VARIANT_BOOL restrictService,VARIANT_BOOL serviceSidRestricted) {
    return This->lpVtbl->RestrictService(This,serviceName,appName,restrictService,serviceSidRestricted);
}
static inline HRESULT INetFwServiceRestriction_ServiceRestricted(INetFwServiceRestriction* This,BSTR serviceName,BSTR appName,VARIANT_BOOL *serviceRestricted) {
    return This->lpVtbl->ServiceRestricted(This,serviceName,appName,serviceRestricted);
}
static inline HRESULT INetFwServiceRestriction_get_Rules(INetFwServiceRestriction* This,INetFwRules **rules) {
    return This->lpVtbl->get_Rules(This,rules);
}
#endif
#endif

#endif


#endif  /* __INetFwServiceRestriction_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwProfile interface
 */
#ifndef __INetFwProfile_INTERFACE_DEFINED__
#define __INetFwProfile_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwProfile, 0x174a0dda, 0xe9f9, 0x449d, 0x99,0x3b, 0x21,0xab,0x66,0x7c,0xa4,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("174a0dda-e9f9-449d-993b-21ab667ca456")
INetFwProfile : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Type(
        NET_FW_PROFILE_TYPE *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FirewallEnabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FirewallEnabled(
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExceptionsNotAllowed(
        VARIANT_BOOL *notAllowed) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ExceptionsNotAllowed(
        VARIANT_BOOL notAllowed) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NotificationsDisabled(
        VARIANT_BOOL *disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_NotificationsDisabled(
        VARIANT_BOOL disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UnicastResponsesToMulticastBroadcastDisabled(
        VARIANT_BOOL *disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UnicastResponsesToMulticastBroadcastDisabled(
        VARIANT_BOOL disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteAdminSettings(
        INetFwRemoteAdminSettings **remoteAdminSettings) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IcmpSettings(
        INetFwIcmpSettings **icmpSettings) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_GloballyOpenPorts(
        INetFwOpenPorts **openPorts) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Services(
        INetFwServices **services) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AuthorizedApplications(
        INetFwAuthorizedApplications **apps) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwProfile, 0x174a0dda, 0xe9f9, 0x449d, 0x99,0x3b, 0x21,0xab,0x66,0x7c,0xa4,0x56)
#endif
#else
typedef struct INetFwProfileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwProfile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwProfile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwProfile *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwProfile *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwProfile *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwProfile *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwProfile *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwProfile methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        INetFwProfile *This,
        NET_FW_PROFILE_TYPE *type);

    HRESULT (STDMETHODCALLTYPE *get_FirewallEnabled)(
        INetFwProfile *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_FirewallEnabled)(
        INetFwProfile *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_ExceptionsNotAllowed)(
        INetFwProfile *This,
        VARIANT_BOOL *notAllowed);

    HRESULT (STDMETHODCALLTYPE *put_ExceptionsNotAllowed)(
        INetFwProfile *This,
        VARIANT_BOOL notAllowed);

    HRESULT (STDMETHODCALLTYPE *get_NotificationsDisabled)(
        INetFwProfile *This,
        VARIANT_BOOL *disabled);

    HRESULT (STDMETHODCALLTYPE *put_NotificationsDisabled)(
        INetFwProfile *This,
        VARIANT_BOOL disabled);

    HRESULT (STDMETHODCALLTYPE *get_UnicastResponsesToMulticastBroadcastDisabled)(
        INetFwProfile *This,
        VARIANT_BOOL *disabled);

    HRESULT (STDMETHODCALLTYPE *put_UnicastResponsesToMulticastBroadcastDisabled)(
        INetFwProfile *This,
        VARIANT_BOOL disabled);

    HRESULT (STDMETHODCALLTYPE *get_RemoteAdminSettings)(
        INetFwProfile *This,
        INetFwRemoteAdminSettings **remoteAdminSettings);

    HRESULT (STDMETHODCALLTYPE *get_IcmpSettings)(
        INetFwProfile *This,
        INetFwIcmpSettings **icmpSettings);

    HRESULT (STDMETHODCALLTYPE *get_GloballyOpenPorts)(
        INetFwProfile *This,
        INetFwOpenPorts **openPorts);

    HRESULT (STDMETHODCALLTYPE *get_Services)(
        INetFwProfile *This,
        INetFwServices **services);

    HRESULT (STDMETHODCALLTYPE *get_AuthorizedApplications)(
        INetFwProfile *This,
        INetFwAuthorizedApplications **apps);

    END_INTERFACE
} INetFwProfileVtbl;

interface INetFwProfile {
    CONST_VTBL INetFwProfileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwProfile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwProfile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwProfile_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwProfile_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwProfile_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwProfile_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwProfile_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwProfile methods ***/
#define INetFwProfile_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define INetFwProfile_get_FirewallEnabled(This,enabled) (This)->lpVtbl->get_FirewallEnabled(This,enabled)
#define INetFwProfile_put_FirewallEnabled(This,enabled) (This)->lpVtbl->put_FirewallEnabled(This,enabled)
#define INetFwProfile_get_ExceptionsNotAllowed(This,notAllowed) (This)->lpVtbl->get_ExceptionsNotAllowed(This,notAllowed)
#define INetFwProfile_put_ExceptionsNotAllowed(This,notAllowed) (This)->lpVtbl->put_ExceptionsNotAllowed(This,notAllowed)
#define INetFwProfile_get_NotificationsDisabled(This,disabled) (This)->lpVtbl->get_NotificationsDisabled(This,disabled)
#define INetFwProfile_put_NotificationsDisabled(This,disabled) (This)->lpVtbl->put_NotificationsDisabled(This,disabled)
#define INetFwProfile_get_UnicastResponsesToMulticastBroadcastDisabled(This,disabled) (This)->lpVtbl->get_UnicastResponsesToMulticastBroadcastDisabled(This,disabled)
#define INetFwProfile_put_UnicastResponsesToMulticastBroadcastDisabled(This,disabled) (This)->lpVtbl->put_UnicastResponsesToMulticastBroadcastDisabled(This,disabled)
#define INetFwProfile_get_RemoteAdminSettings(This,remoteAdminSettings) (This)->lpVtbl->get_RemoteAdminSettings(This,remoteAdminSettings)
#define INetFwProfile_get_IcmpSettings(This,icmpSettings) (This)->lpVtbl->get_IcmpSettings(This,icmpSettings)
#define INetFwProfile_get_GloballyOpenPorts(This,openPorts) (This)->lpVtbl->get_GloballyOpenPorts(This,openPorts)
#define INetFwProfile_get_Services(This,services) (This)->lpVtbl->get_Services(This,services)
#define INetFwProfile_get_AuthorizedApplications(This,apps) (This)->lpVtbl->get_AuthorizedApplications(This,apps)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwProfile_QueryInterface(INetFwProfile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwProfile_AddRef(INetFwProfile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwProfile_Release(INetFwProfile* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwProfile_GetTypeInfoCount(INetFwProfile* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwProfile_GetTypeInfo(INetFwProfile* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwProfile_GetIDsOfNames(INetFwProfile* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwProfile_Invoke(INetFwProfile* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwProfile methods ***/
static inline HRESULT INetFwProfile_get_Type(INetFwProfile* This,NET_FW_PROFILE_TYPE *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT INetFwProfile_get_FirewallEnabled(INetFwProfile* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_FirewallEnabled(This,enabled);
}
static inline HRESULT INetFwProfile_put_FirewallEnabled(INetFwProfile* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_FirewallEnabled(This,enabled);
}
static inline HRESULT INetFwProfile_get_ExceptionsNotAllowed(INetFwProfile* This,VARIANT_BOOL *notAllowed) {
    return This->lpVtbl->get_ExceptionsNotAllowed(This,notAllowed);
}
static inline HRESULT INetFwProfile_put_ExceptionsNotAllowed(INetFwProfile* This,VARIANT_BOOL notAllowed) {
    return This->lpVtbl->put_ExceptionsNotAllowed(This,notAllowed);
}
static inline HRESULT INetFwProfile_get_NotificationsDisabled(INetFwProfile* This,VARIANT_BOOL *disabled) {
    return This->lpVtbl->get_NotificationsDisabled(This,disabled);
}
static inline HRESULT INetFwProfile_put_NotificationsDisabled(INetFwProfile* This,VARIANT_BOOL disabled) {
    return This->lpVtbl->put_NotificationsDisabled(This,disabled);
}
static inline HRESULT INetFwProfile_get_UnicastResponsesToMulticastBroadcastDisabled(INetFwProfile* This,VARIANT_BOOL *disabled) {
    return This->lpVtbl->get_UnicastResponsesToMulticastBroadcastDisabled(This,disabled);
}
static inline HRESULT INetFwProfile_put_UnicastResponsesToMulticastBroadcastDisabled(INetFwProfile* This,VARIANT_BOOL disabled) {
    return This->lpVtbl->put_UnicastResponsesToMulticastBroadcastDisabled(This,disabled);
}
static inline HRESULT INetFwProfile_get_RemoteAdminSettings(INetFwProfile* This,INetFwRemoteAdminSettings **remoteAdminSettings) {
    return This->lpVtbl->get_RemoteAdminSettings(This,remoteAdminSettings);
}
static inline HRESULT INetFwProfile_get_IcmpSettings(INetFwProfile* This,INetFwIcmpSettings **icmpSettings) {
    return This->lpVtbl->get_IcmpSettings(This,icmpSettings);
}
static inline HRESULT INetFwProfile_get_GloballyOpenPorts(INetFwProfile* This,INetFwOpenPorts **openPorts) {
    return This->lpVtbl->get_GloballyOpenPorts(This,openPorts);
}
static inline HRESULT INetFwProfile_get_Services(INetFwProfile* This,INetFwServices **services) {
    return This->lpVtbl->get_Services(This,services);
}
static inline HRESULT INetFwProfile_get_AuthorizedApplications(INetFwProfile* This,INetFwAuthorizedApplications **apps) {
    return This->lpVtbl->get_AuthorizedApplications(This,apps);
}
#endif
#endif

#endif


#endif  /* __INetFwProfile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwPolicy interface
 */
#ifndef __INetFwPolicy_INTERFACE_DEFINED__
#define __INetFwPolicy_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwPolicy, 0xd46d2478, 0x9ac9, 0x4008, 0x9d,0xc7, 0x55,0x63,0xce,0x55,0x36,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d46d2478-9ac9-4008-9dc7-5563ce5536cc")
INetFwPolicy : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_CurrentProfile(
        INetFwProfile **profile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProfileByType(
        NET_FW_PROFILE_TYPE profileType,
        INetFwProfile **profile) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwPolicy, 0xd46d2478, 0x9ac9, 0x4008, 0x9d,0xc7, 0x55,0x63,0xce,0x55,0x36,0xcc)
#endif
#else
typedef struct INetFwPolicyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwPolicy *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwPolicy *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwPolicy *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwPolicy *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwPolicy *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwPolicy *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwPolicy *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwPolicy methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentProfile)(
        INetFwPolicy *This,
        INetFwProfile **profile);

    HRESULT (STDMETHODCALLTYPE *GetProfileByType)(
        INetFwPolicy *This,
        NET_FW_PROFILE_TYPE profileType,
        INetFwProfile **profile);

    END_INTERFACE
} INetFwPolicyVtbl;

interface INetFwPolicy {
    CONST_VTBL INetFwPolicyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwPolicy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwPolicy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwPolicy_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwPolicy_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwPolicy_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwPolicy_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwPolicy_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwPolicy methods ***/
#define INetFwPolicy_get_CurrentProfile(This,profile) (This)->lpVtbl->get_CurrentProfile(This,profile)
#define INetFwPolicy_GetProfileByType(This,profileType,profile) (This)->lpVtbl->GetProfileByType(This,profileType,profile)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwPolicy_QueryInterface(INetFwPolicy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwPolicy_AddRef(INetFwPolicy* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwPolicy_Release(INetFwPolicy* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwPolicy_GetTypeInfoCount(INetFwPolicy* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwPolicy_GetTypeInfo(INetFwPolicy* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwPolicy_GetIDsOfNames(INetFwPolicy* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwPolicy_Invoke(INetFwPolicy* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwPolicy methods ***/
static inline HRESULT INetFwPolicy_get_CurrentProfile(INetFwPolicy* This,INetFwProfile **profile) {
    return This->lpVtbl->get_CurrentProfile(This,profile);
}
static inline HRESULT INetFwPolicy_GetProfileByType(INetFwPolicy* This,NET_FW_PROFILE_TYPE profileType,INetFwProfile **profile) {
    return This->lpVtbl->GetProfileByType(This,profileType,profile);
}
#endif
#endif

#endif


#endif  /* __INetFwPolicy_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwPolicy2 interface
 */
#ifndef __INetFwPolicy2_INTERFACE_DEFINED__
#define __INetFwPolicy2_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwPolicy2, 0x98325047, 0xc671, 0x4174, 0x8d,0x81, 0xde,0xfc,0xd3,0xf0,0x31,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("98325047-c671-4174-8d81-defcd3f03186")
INetFwPolicy2 : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_CurrentProfileTypes(
        LONG *profile) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FirewallEnabled(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FirewallEnabled(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExcludedInterfaces(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT *interfaces) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ExcludedInterfaces(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT interfaces) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BlockAllInboundTraffic(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL *block) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_BlockAllInboundTraffic(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL block) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NotificationsDisabled(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL *disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_NotificationsDisabled(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UnicastResponsesToMulticastBroadcastDisabled(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL *disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UnicastResponsesToMulticastBroadcastDisabled(
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL disabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Rules(
        INetFwRules **rules) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ServiceRestriction(
        INetFwServiceRestriction **ServiceRestriction) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableRuleGroup(
        LONG profileTypesBitmask,
        BSTR group,
        VARIANT_BOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRuleGroupEnabled(
        LONG profileTypesBitmask,
        BSTR group,
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE RestoreLocalFirewallDefaults(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DefaultInboundAction(
        NET_FW_PROFILE_TYPE2 profileType,
        NET_FW_ACTION *action) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DefaultInboundAction(
        NET_FW_PROFILE_TYPE2 profileType,
        NET_FW_ACTION action) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DefaultOutboundAction(
        NET_FW_PROFILE_TYPE2 profileType,
        NET_FW_ACTION *action) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DefaultOutboundAction(
        NET_FW_PROFILE_TYPE2 profileType,
        NET_FW_ACTION action) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsRuleGroupCurrentlyEnabled(
        BSTR group,
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalPolicyModifyState(
        NET_FW_MODIFY_STATE *modifyState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwPolicy2, 0x98325047, 0xc671, 0x4174, 0x8d,0x81, 0xde,0xfc,0xd3,0xf0,0x31,0x86)
#endif
#else
typedef struct INetFwPolicy2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwPolicy2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwPolicy2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwPolicy2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwPolicy2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwPolicy2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwPolicy2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwPolicy2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwPolicy2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentProfileTypes)(
        INetFwPolicy2 *This,
        LONG *profile);

    HRESULT (STDMETHODCALLTYPE *get_FirewallEnabled)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_FirewallEnabled)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_ExcludedInterfaces)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT *interfaces);

    HRESULT (STDMETHODCALLTYPE *put_ExcludedInterfaces)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT interfaces);

    HRESULT (STDMETHODCALLTYPE *get_BlockAllInboundTraffic)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL *block);

    HRESULT (STDMETHODCALLTYPE *put_BlockAllInboundTraffic)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL block);

    HRESULT (STDMETHODCALLTYPE *get_NotificationsDisabled)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL *disabled);

    HRESULT (STDMETHODCALLTYPE *put_NotificationsDisabled)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL disabled);

    HRESULT (STDMETHODCALLTYPE *get_UnicastResponsesToMulticastBroadcastDisabled)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL *disabled);

    HRESULT (STDMETHODCALLTYPE *put_UnicastResponsesToMulticastBroadcastDisabled)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        VARIANT_BOOL disabled);

    HRESULT (STDMETHODCALLTYPE *get_Rules)(
        INetFwPolicy2 *This,
        INetFwRules **rules);

    HRESULT (STDMETHODCALLTYPE *get_ServiceRestriction)(
        INetFwPolicy2 *This,
        INetFwServiceRestriction **ServiceRestriction);

    HRESULT (STDMETHODCALLTYPE *EnableRuleGroup)(
        INetFwPolicy2 *This,
        LONG profileTypesBitmask,
        BSTR group,
        VARIANT_BOOL enable);

    HRESULT (STDMETHODCALLTYPE *IsRuleGroupEnabled)(
        INetFwPolicy2 *This,
        LONG profileTypesBitmask,
        BSTR group,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *RestoreLocalFirewallDefaults)(
        INetFwPolicy2 *This);

    HRESULT (STDMETHODCALLTYPE *get_DefaultInboundAction)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        NET_FW_ACTION *action);

    HRESULT (STDMETHODCALLTYPE *put_DefaultInboundAction)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        NET_FW_ACTION action);

    HRESULT (STDMETHODCALLTYPE *get_DefaultOutboundAction)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        NET_FW_ACTION *action);

    HRESULT (STDMETHODCALLTYPE *put_DefaultOutboundAction)(
        INetFwPolicy2 *This,
        NET_FW_PROFILE_TYPE2 profileType,
        NET_FW_ACTION action);

    HRESULT (STDMETHODCALLTYPE *get_IsRuleGroupCurrentlyEnabled)(
        INetFwPolicy2 *This,
        BSTR group,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *get_LocalPolicyModifyState)(
        INetFwPolicy2 *This,
        NET_FW_MODIFY_STATE *modifyState);

    END_INTERFACE
} INetFwPolicy2Vtbl;

interface INetFwPolicy2 {
    CONST_VTBL INetFwPolicy2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwPolicy2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwPolicy2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwPolicy2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwPolicy2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwPolicy2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwPolicy2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwPolicy2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwPolicy2 methods ***/
#define INetFwPolicy2_get_CurrentProfileTypes(This,profile) (This)->lpVtbl->get_CurrentProfileTypes(This,profile)
#define INetFwPolicy2_get_FirewallEnabled(This,profileType,enabled) (This)->lpVtbl->get_FirewallEnabled(This,profileType,enabled)
#define INetFwPolicy2_put_FirewallEnabled(This,profileType,enabled) (This)->lpVtbl->put_FirewallEnabled(This,profileType,enabled)
#define INetFwPolicy2_get_ExcludedInterfaces(This,profileType,interfaces) (This)->lpVtbl->get_ExcludedInterfaces(This,profileType,interfaces)
#define INetFwPolicy2_put_ExcludedInterfaces(This,profileType,interfaces) (This)->lpVtbl->put_ExcludedInterfaces(This,profileType,interfaces)
#define INetFwPolicy2_get_BlockAllInboundTraffic(This,profileType,block) (This)->lpVtbl->get_BlockAllInboundTraffic(This,profileType,block)
#define INetFwPolicy2_put_BlockAllInboundTraffic(This,profileType,block) (This)->lpVtbl->put_BlockAllInboundTraffic(This,profileType,block)
#define INetFwPolicy2_get_NotificationsDisabled(This,profileType,disabled) (This)->lpVtbl->get_NotificationsDisabled(This,profileType,disabled)
#define INetFwPolicy2_put_NotificationsDisabled(This,profileType,disabled) (This)->lpVtbl->put_NotificationsDisabled(This,profileType,disabled)
#define INetFwPolicy2_get_UnicastResponsesToMulticastBroadcastDisabled(This,profileType,disabled) (This)->lpVtbl->get_UnicastResponsesToMulticastBroadcastDisabled(This,profileType,disabled)
#define INetFwPolicy2_put_UnicastResponsesToMulticastBroadcastDisabled(This,profileType,disabled) (This)->lpVtbl->put_UnicastResponsesToMulticastBroadcastDisabled(This,profileType,disabled)
#define INetFwPolicy2_get_Rules(This,rules) (This)->lpVtbl->get_Rules(This,rules)
#define INetFwPolicy2_get_ServiceRestriction(This,ServiceRestriction) (This)->lpVtbl->get_ServiceRestriction(This,ServiceRestriction)
#define INetFwPolicy2_EnableRuleGroup(This,profileTypesBitmask,group,enable) (This)->lpVtbl->EnableRuleGroup(This,profileTypesBitmask,group,enable)
#define INetFwPolicy2_IsRuleGroupEnabled(This,profileTypesBitmask,group,enabled) (This)->lpVtbl->IsRuleGroupEnabled(This,profileTypesBitmask,group,enabled)
#define INetFwPolicy2_RestoreLocalFirewallDefaults(This) (This)->lpVtbl->RestoreLocalFirewallDefaults(This)
#define INetFwPolicy2_get_DefaultInboundAction(This,profileType,action) (This)->lpVtbl->get_DefaultInboundAction(This,profileType,action)
#define INetFwPolicy2_put_DefaultInboundAction(This,profileType,action) (This)->lpVtbl->put_DefaultInboundAction(This,profileType,action)
#define INetFwPolicy2_get_DefaultOutboundAction(This,profileType,action) (This)->lpVtbl->get_DefaultOutboundAction(This,profileType,action)
#define INetFwPolicy2_put_DefaultOutboundAction(This,profileType,action) (This)->lpVtbl->put_DefaultOutboundAction(This,profileType,action)
#define INetFwPolicy2_get_IsRuleGroupCurrentlyEnabled(This,group,enabled) (This)->lpVtbl->get_IsRuleGroupCurrentlyEnabled(This,group,enabled)
#define INetFwPolicy2_get_LocalPolicyModifyState(This,modifyState) (This)->lpVtbl->get_LocalPolicyModifyState(This,modifyState)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwPolicy2_QueryInterface(INetFwPolicy2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwPolicy2_AddRef(INetFwPolicy2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwPolicy2_Release(INetFwPolicy2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwPolicy2_GetTypeInfoCount(INetFwPolicy2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwPolicy2_GetTypeInfo(INetFwPolicy2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwPolicy2_GetIDsOfNames(INetFwPolicy2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwPolicy2_Invoke(INetFwPolicy2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwPolicy2 methods ***/
static inline HRESULT INetFwPolicy2_get_CurrentProfileTypes(INetFwPolicy2* This,LONG *profile) {
    return This->lpVtbl->get_CurrentProfileTypes(This,profile);
}
static inline HRESULT INetFwPolicy2_get_FirewallEnabled(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_FirewallEnabled(This,profileType,enabled);
}
static inline HRESULT INetFwPolicy2_put_FirewallEnabled(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_FirewallEnabled(This,profileType,enabled);
}
static inline HRESULT INetFwPolicy2_get_ExcludedInterfaces(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT *interfaces) {
    return This->lpVtbl->get_ExcludedInterfaces(This,profileType,interfaces);
}
static inline HRESULT INetFwPolicy2_put_ExcludedInterfaces(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT interfaces) {
    return This->lpVtbl->put_ExcludedInterfaces(This,profileType,interfaces);
}
static inline HRESULT INetFwPolicy2_get_BlockAllInboundTraffic(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT_BOOL *block) {
    return This->lpVtbl->get_BlockAllInboundTraffic(This,profileType,block);
}
static inline HRESULT INetFwPolicy2_put_BlockAllInboundTraffic(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT_BOOL block) {
    return This->lpVtbl->put_BlockAllInboundTraffic(This,profileType,block);
}
static inline HRESULT INetFwPolicy2_get_NotificationsDisabled(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT_BOOL *disabled) {
    return This->lpVtbl->get_NotificationsDisabled(This,profileType,disabled);
}
static inline HRESULT INetFwPolicy2_put_NotificationsDisabled(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT_BOOL disabled) {
    return This->lpVtbl->put_NotificationsDisabled(This,profileType,disabled);
}
static inline HRESULT INetFwPolicy2_get_UnicastResponsesToMulticastBroadcastDisabled(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT_BOOL *disabled) {
    return This->lpVtbl->get_UnicastResponsesToMulticastBroadcastDisabled(This,profileType,disabled);
}
static inline HRESULT INetFwPolicy2_put_UnicastResponsesToMulticastBroadcastDisabled(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,VARIANT_BOOL disabled) {
    return This->lpVtbl->put_UnicastResponsesToMulticastBroadcastDisabled(This,profileType,disabled);
}
static inline HRESULT INetFwPolicy2_get_Rules(INetFwPolicy2* This,INetFwRules **rules) {
    return This->lpVtbl->get_Rules(This,rules);
}
static inline HRESULT INetFwPolicy2_get_ServiceRestriction(INetFwPolicy2* This,INetFwServiceRestriction **ServiceRestriction) {
    return This->lpVtbl->get_ServiceRestriction(This,ServiceRestriction);
}
static inline HRESULT INetFwPolicy2_EnableRuleGroup(INetFwPolicy2* This,LONG profileTypesBitmask,BSTR group,VARIANT_BOOL enable) {
    return This->lpVtbl->EnableRuleGroup(This,profileTypesBitmask,group,enable);
}
static inline HRESULT INetFwPolicy2_IsRuleGroupEnabled(INetFwPolicy2* This,LONG profileTypesBitmask,BSTR group,VARIANT_BOOL *enabled) {
    return This->lpVtbl->IsRuleGroupEnabled(This,profileTypesBitmask,group,enabled);
}
static inline HRESULT INetFwPolicy2_RestoreLocalFirewallDefaults(INetFwPolicy2* This) {
    return This->lpVtbl->RestoreLocalFirewallDefaults(This);
}
static inline HRESULT INetFwPolicy2_get_DefaultInboundAction(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,NET_FW_ACTION *action) {
    return This->lpVtbl->get_DefaultInboundAction(This,profileType,action);
}
static inline HRESULT INetFwPolicy2_put_DefaultInboundAction(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,NET_FW_ACTION action) {
    return This->lpVtbl->put_DefaultInboundAction(This,profileType,action);
}
static inline HRESULT INetFwPolicy2_get_DefaultOutboundAction(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,NET_FW_ACTION *action) {
    return This->lpVtbl->get_DefaultOutboundAction(This,profileType,action);
}
static inline HRESULT INetFwPolicy2_put_DefaultOutboundAction(INetFwPolicy2* This,NET_FW_PROFILE_TYPE2 profileType,NET_FW_ACTION action) {
    return This->lpVtbl->put_DefaultOutboundAction(This,profileType,action);
}
static inline HRESULT INetFwPolicy2_get_IsRuleGroupCurrentlyEnabled(INetFwPolicy2* This,BSTR group,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_IsRuleGroupCurrentlyEnabled(This,group,enabled);
}
static inline HRESULT INetFwPolicy2_get_LocalPolicyModifyState(INetFwPolicy2* This,NET_FW_MODIFY_STATE *modifyState) {
    return This->lpVtbl->get_LocalPolicyModifyState(This,modifyState);
}
#endif
#endif

#endif


#endif  /* __INetFwPolicy2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwMgr interface
 */
#ifndef __INetFwMgr_INTERFACE_DEFINED__
#define __INetFwMgr_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwMgr, 0xf7898af5, 0xcac4, 0x4632, 0xa2,0xec, 0xda,0x06,0xe5,0x11,0x1a,0xf2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f7898af5-cac4-4632-a2ec-da06e5111af2")
INetFwMgr : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_LocalPolicy(
        INetFwPolicy **localPolicy) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentProfileType(
        NET_FW_PROFILE_TYPE *profileType) = 0;

    virtual HRESULT STDMETHODCALLTYPE RestoreDefaults(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPortAllowed(
        BSTR imageFileName,
        NET_FW_IP_VERSION ipVersion,
        LONG portNumber,
        BSTR localAddress,
        NET_FW_IP_PROTOCOL ipProtocol,
        VARIANT *allowed,
        VARIANT *restricted) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsIcmpTypeAllowed(
        NET_FW_IP_VERSION ipVersion,
        BSTR localAddress,
        BYTE type,
        VARIANT *allowed,
        VARIANT *restricted) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwMgr, 0xf7898af5, 0xcac4, 0x4632, 0xa2,0xec, 0xda,0x06,0xe5,0x11,0x1a,0xf2)
#endif
#else
typedef struct INetFwMgrVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwMgr *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwMgr *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwMgr *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwMgr *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwMgr *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwMgr *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwMgr *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwMgr methods ***/
    HRESULT (STDMETHODCALLTYPE *get_LocalPolicy)(
        INetFwMgr *This,
        INetFwPolicy **localPolicy);

    HRESULT (STDMETHODCALLTYPE *get_CurrentProfileType)(
        INetFwMgr *This,
        NET_FW_PROFILE_TYPE *profileType);

    HRESULT (STDMETHODCALLTYPE *RestoreDefaults)(
        INetFwMgr *This);

    HRESULT (STDMETHODCALLTYPE *IsPortAllowed)(
        INetFwMgr *This,
        BSTR imageFileName,
        NET_FW_IP_VERSION ipVersion,
        LONG portNumber,
        BSTR localAddress,
        NET_FW_IP_PROTOCOL ipProtocol,
        VARIANT *allowed,
        VARIANT *restricted);

    HRESULT (STDMETHODCALLTYPE *IsIcmpTypeAllowed)(
        INetFwMgr *This,
        NET_FW_IP_VERSION ipVersion,
        BSTR localAddress,
        BYTE type,
        VARIANT *allowed,
        VARIANT *restricted);

    END_INTERFACE
} INetFwMgrVtbl;

interface INetFwMgr {
    CONST_VTBL INetFwMgrVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwMgr_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwMgr_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwMgr_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwMgr_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwMgr_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwMgr_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwMgr_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwMgr methods ***/
#define INetFwMgr_get_LocalPolicy(This,localPolicy) (This)->lpVtbl->get_LocalPolicy(This,localPolicy)
#define INetFwMgr_get_CurrentProfileType(This,profileType) (This)->lpVtbl->get_CurrentProfileType(This,profileType)
#define INetFwMgr_RestoreDefaults(This) (This)->lpVtbl->RestoreDefaults(This)
#define INetFwMgr_IsPortAllowed(This,imageFileName,ipVersion,portNumber,localAddress,ipProtocol,allowed,restricted) (This)->lpVtbl->IsPortAllowed(This,imageFileName,ipVersion,portNumber,localAddress,ipProtocol,allowed,restricted)
#define INetFwMgr_IsIcmpTypeAllowed(This,ipVersion,localAddress,type,allowed,restricted) (This)->lpVtbl->IsIcmpTypeAllowed(This,ipVersion,localAddress,type,allowed,restricted)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwMgr_QueryInterface(INetFwMgr* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwMgr_AddRef(INetFwMgr* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwMgr_Release(INetFwMgr* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwMgr_GetTypeInfoCount(INetFwMgr* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwMgr_GetTypeInfo(INetFwMgr* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwMgr_GetIDsOfNames(INetFwMgr* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwMgr_Invoke(INetFwMgr* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwMgr methods ***/
static inline HRESULT INetFwMgr_get_LocalPolicy(INetFwMgr* This,INetFwPolicy **localPolicy) {
    return This->lpVtbl->get_LocalPolicy(This,localPolicy);
}
static inline HRESULT INetFwMgr_get_CurrentProfileType(INetFwMgr* This,NET_FW_PROFILE_TYPE *profileType) {
    return This->lpVtbl->get_CurrentProfileType(This,profileType);
}
static inline HRESULT INetFwMgr_RestoreDefaults(INetFwMgr* This) {
    return This->lpVtbl->RestoreDefaults(This);
}
static inline HRESULT INetFwMgr_IsPortAllowed(INetFwMgr* This,BSTR imageFileName,NET_FW_IP_VERSION ipVersion,LONG portNumber,BSTR localAddress,NET_FW_IP_PROTOCOL ipProtocol,VARIANT *allowed,VARIANT *restricted) {
    return This->lpVtbl->IsPortAllowed(This,imageFileName,ipVersion,portNumber,localAddress,ipProtocol,allowed,restricted);
}
static inline HRESULT INetFwMgr_IsIcmpTypeAllowed(INetFwMgr* This,NET_FW_IP_VERSION ipVersion,BSTR localAddress,BYTE type,VARIANT *allowed,VARIANT *restricted) {
    return This->lpVtbl->IsIcmpTypeAllowed(This,ipVersion,localAddress,type,allowed,restricted);
}
#endif
#endif

#endif


#endif  /* __INetFwMgr_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwProduct interface
 */
#ifndef __INetFwProduct_INTERFACE_DEFINED__
#define __INetFwProduct_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwProduct, 0x71881699, 0x18f4, 0x458b, 0xb8,0x92, 0x3f,0xfc,0xe5,0xe0,0x7f,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71881699-18f4-458b-b892-3ffce5e07f75")
INetFwProduct : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_RuleCategories(
        VARIANT *ruleCategories) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RuleCategories(
        VARIANT ruleCategories) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
        BSTR *displayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DisplayName(
        BSTR displayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PathToSignedProductExe(
        BSTR *path) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwProduct, 0x71881699, 0x18f4, 0x458b, 0xb8,0x92, 0x3f,0xfc,0xe5,0xe0,0x7f,0x75)
#endif
#else
typedef struct INetFwProductVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwProduct *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwProduct *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwProduct *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwProduct *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwProduct *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwProduct *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwProduct *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwProduct methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RuleCategories)(
        INetFwProduct *This,
        VARIANT *ruleCategories);

    HRESULT (STDMETHODCALLTYPE *put_RuleCategories)(
        INetFwProduct *This,
        VARIANT ruleCategories);

    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        INetFwProduct *This,
        BSTR *displayName);

    HRESULT (STDMETHODCALLTYPE *put_DisplayName)(
        INetFwProduct *This,
        BSTR displayName);

    HRESULT (STDMETHODCALLTYPE *get_PathToSignedProductExe)(
        INetFwProduct *This,
        BSTR *path);

    END_INTERFACE
} INetFwProductVtbl;

interface INetFwProduct {
    CONST_VTBL INetFwProductVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwProduct_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwProduct_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwProduct_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwProduct_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwProduct_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwProduct_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwProduct_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwProduct methods ***/
#define INetFwProduct_get_RuleCategories(This,ruleCategories) (This)->lpVtbl->get_RuleCategories(This,ruleCategories)
#define INetFwProduct_put_RuleCategories(This,ruleCategories) (This)->lpVtbl->put_RuleCategories(This,ruleCategories)
#define INetFwProduct_get_DisplayName(This,displayName) (This)->lpVtbl->get_DisplayName(This,displayName)
#define INetFwProduct_put_DisplayName(This,displayName) (This)->lpVtbl->put_DisplayName(This,displayName)
#define INetFwProduct_get_PathToSignedProductExe(This,path) (This)->lpVtbl->get_PathToSignedProductExe(This,path)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwProduct_QueryInterface(INetFwProduct* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwProduct_AddRef(INetFwProduct* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwProduct_Release(INetFwProduct* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwProduct_GetTypeInfoCount(INetFwProduct* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwProduct_GetTypeInfo(INetFwProduct* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwProduct_GetIDsOfNames(INetFwProduct* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwProduct_Invoke(INetFwProduct* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwProduct methods ***/
static inline HRESULT INetFwProduct_get_RuleCategories(INetFwProduct* This,VARIANT *ruleCategories) {
    return This->lpVtbl->get_RuleCategories(This,ruleCategories);
}
static inline HRESULT INetFwProduct_put_RuleCategories(INetFwProduct* This,VARIANT ruleCategories) {
    return This->lpVtbl->put_RuleCategories(This,ruleCategories);
}
static inline HRESULT INetFwProduct_get_DisplayName(INetFwProduct* This,BSTR *displayName) {
    return This->lpVtbl->get_DisplayName(This,displayName);
}
static inline HRESULT INetFwProduct_put_DisplayName(INetFwProduct* This,BSTR displayName) {
    return This->lpVtbl->put_DisplayName(This,displayName);
}
static inline HRESULT INetFwProduct_get_PathToSignedProductExe(INetFwProduct* This,BSTR *path) {
    return This->lpVtbl->get_PathToSignedProductExe(This,path);
}
#endif
#endif

#endif


#endif  /* __INetFwProduct_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetFwProducts interface
 */
#ifndef __INetFwProducts_INTERFACE_DEFINED__
#define __INetFwProducts_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetFwProducts, 0x39eb36e0, 0x2097, 0x40bd, 0x8a,0xf2, 0x63,0xa1,0x3b,0x52,0x53,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("39eb36e0-2097-40bd-8af2-63a13b525362")
INetFwProducts : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE Register(
        INetFwProduct *product,
        IUnknown **registration) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG index,
        INetFwProduct **product) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **newEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetFwProducts, 0x39eb36e0, 0x2097, 0x40bd, 0x8a,0xf2, 0x63,0xa1,0x3b,0x52,0x53,0x62)
#endif
#else
typedef struct INetFwProductsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetFwProducts *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetFwProducts *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetFwProducts *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetFwProducts *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetFwProducts *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetFwProducts *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetFwProducts *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetFwProducts methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        INetFwProducts *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *Register)(
        INetFwProducts *This,
        INetFwProduct *product,
        IUnknown **registration);

    HRESULT (STDMETHODCALLTYPE *Item)(
        INetFwProducts *This,
        LONG index,
        INetFwProduct **product);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        INetFwProducts *This,
        IUnknown **newEnum);

    END_INTERFACE
} INetFwProductsVtbl;

interface INetFwProducts {
    CONST_VTBL INetFwProductsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetFwProducts_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetFwProducts_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetFwProducts_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetFwProducts_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetFwProducts_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetFwProducts_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetFwProducts_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetFwProducts methods ***/
#define INetFwProducts_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define INetFwProducts_Register(This,product,registration) (This)->lpVtbl->Register(This,product,registration)
#define INetFwProducts_Item(This,index,product) (This)->lpVtbl->Item(This,index,product)
#define INetFwProducts_get__NewEnum(This,newEnum) (This)->lpVtbl->get__NewEnum(This,newEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT INetFwProducts_QueryInterface(INetFwProducts* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetFwProducts_AddRef(INetFwProducts* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetFwProducts_Release(INetFwProducts* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetFwProducts_GetTypeInfoCount(INetFwProducts* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetFwProducts_GetTypeInfo(INetFwProducts* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetFwProducts_GetIDsOfNames(INetFwProducts* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetFwProducts_Invoke(INetFwProducts* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetFwProducts methods ***/
static inline HRESULT INetFwProducts_get_Count(INetFwProducts* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT INetFwProducts_Register(INetFwProducts* This,INetFwProduct *product,IUnknown **registration) {
    return This->lpVtbl->Register(This,product,registration);
}
static inline HRESULT INetFwProducts_Item(INetFwProducts* This,LONG index,INetFwProduct **product) {
    return This->lpVtbl->Item(This,index,product);
}
static inline HRESULT INetFwProducts_get__NewEnum(INetFwProducts* This,IUnknown **newEnum) {
    return This->lpVtbl->get__NewEnum(This,newEnum);
}
#endif
#endif

#endif


#endif  /* __INetFwProducts_INTERFACE_DEFINED__ */

#ifndef __NetFwPublicTypeLib_LIBRARY_DEFINED__
#define __NetFwPublicTypeLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_NetFwPublicTypeLib, 0xdb4f3345, 0x3ef8, 0x45ed, 0xb9,0x76, 0x25,0xa6,0xd3,0xb8,0x1b,0x71);

#ifndef __INetFwRemoteAdminSettings_FWD_DEFINED__
#define __INetFwRemoteAdminSettings_FWD_DEFINED__
typedef interface INetFwRemoteAdminSettings INetFwRemoteAdminSettings;
#ifdef __cplusplus
interface INetFwRemoteAdminSettings;
#endif /* __cplusplus */
#endif

#ifndef __INetFwIcmpSettings_FWD_DEFINED__
#define __INetFwIcmpSettings_FWD_DEFINED__
typedef interface INetFwIcmpSettings INetFwIcmpSettings;
#ifdef __cplusplus
interface INetFwIcmpSettings;
#endif /* __cplusplus */
#endif

#ifndef __INetFwOpenPort_FWD_DEFINED__
#define __INetFwOpenPort_FWD_DEFINED__
typedef interface INetFwOpenPort INetFwOpenPort;
#ifdef __cplusplus
interface INetFwOpenPort;
#endif /* __cplusplus */
#endif

#ifndef __INetFwOpenPorts_FWD_DEFINED__
#define __INetFwOpenPorts_FWD_DEFINED__
typedef interface INetFwOpenPorts INetFwOpenPorts;
#ifdef __cplusplus
interface INetFwOpenPorts;
#endif /* __cplusplus */
#endif

#ifndef __INetFwService_FWD_DEFINED__
#define __INetFwService_FWD_DEFINED__
typedef interface INetFwService INetFwService;
#ifdef __cplusplus
interface INetFwService;
#endif /* __cplusplus */
#endif

#ifndef __INetFwServices_FWD_DEFINED__
#define __INetFwServices_FWD_DEFINED__
typedef interface INetFwServices INetFwServices;
#ifdef __cplusplus
interface INetFwServices;
#endif /* __cplusplus */
#endif

#ifndef __INetFwAuthorizedApplication_FWD_DEFINED__
#define __INetFwAuthorizedApplication_FWD_DEFINED__
typedef interface INetFwAuthorizedApplication INetFwAuthorizedApplication;
#ifdef __cplusplus
interface INetFwAuthorizedApplication;
#endif /* __cplusplus */
#endif

#ifndef __INetFwAuthorizedApplications_FWD_DEFINED__
#define __INetFwAuthorizedApplications_FWD_DEFINED__
typedef interface INetFwAuthorizedApplications INetFwAuthorizedApplications;
#ifdef __cplusplus
interface INetFwAuthorizedApplications;
#endif /* __cplusplus */
#endif

#ifndef __INetFwServiceRestriction_FWD_DEFINED__
#define __INetFwServiceRestriction_FWD_DEFINED__
typedef interface INetFwServiceRestriction INetFwServiceRestriction;
#ifdef __cplusplus
interface INetFwServiceRestriction;
#endif /* __cplusplus */
#endif

#ifndef __INetFwRule_FWD_DEFINED__
#define __INetFwRule_FWD_DEFINED__
typedef interface INetFwRule INetFwRule;
#ifdef __cplusplus
interface INetFwRule;
#endif /* __cplusplus */
#endif

#ifndef __INetFwRules_FWD_DEFINED__
#define __INetFwRules_FWD_DEFINED__
typedef interface INetFwRules INetFwRules;
#ifdef __cplusplus
interface INetFwRules;
#endif /* __cplusplus */
#endif

#ifndef __INetFwProfile_FWD_DEFINED__
#define __INetFwProfile_FWD_DEFINED__
typedef interface INetFwProfile INetFwProfile;
#ifdef __cplusplus
interface INetFwProfile;
#endif /* __cplusplus */
#endif

#ifndef __INetFwPolicy_FWD_DEFINED__
#define __INetFwPolicy_FWD_DEFINED__
typedef interface INetFwPolicy INetFwPolicy;
#ifdef __cplusplus
interface INetFwPolicy;
#endif /* __cplusplus */
#endif

#ifndef __INetFwPolicy2_FWD_DEFINED__
#define __INetFwPolicy2_FWD_DEFINED__
typedef interface INetFwPolicy2 INetFwPolicy2;
#ifdef __cplusplus
interface INetFwPolicy2;
#endif /* __cplusplus */
#endif

#ifndef __INetFwMgr_FWD_DEFINED__
#define __INetFwMgr_FWD_DEFINED__
typedef interface INetFwMgr INetFwMgr;
#ifdef __cplusplus
interface INetFwMgr;
#endif /* __cplusplus */
#endif

#ifndef __INetFwProduct_FWD_DEFINED__
#define __INetFwProduct_FWD_DEFINED__
typedef interface INetFwProduct INetFwProduct;
#ifdef __cplusplus
interface INetFwProduct;
#endif /* __cplusplus */
#endif

#ifndef __INetFwProducts_FWD_DEFINED__
#define __INetFwProducts_FWD_DEFINED__
typedef interface INetFwProducts INetFwProducts;
#ifdef __cplusplus
interface INetFwProducts;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * NetFwOpenPort coclass
 */

DEFINE_GUID(CLSID_NetFwOpenPort, 0x0ca545c6, 0x37ad, 0x4a6c, 0xbf,0x92, 0x9f,0x76,0x10,0x06,0x7e,0xf5);

#ifdef __cplusplus
class DECLSPEC_UUID("0ca545c6-37ad-4a6c-bf92-9f7610067ef5") NetFwOpenPort;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NetFwOpenPort, 0x0ca545c6, 0x37ad, 0x4a6c, 0xbf,0x92, 0x9f,0x76,0x10,0x06,0x7e,0xf5)
#endif
#endif

/*****************************************************************************
 * NetFwAuthorizedApplication coclass
 */

DEFINE_GUID(CLSID_NetFwAuthorizedApplication, 0xec9846b3, 0x2762, 0x4a6b, 0xa2,0x14, 0x6a,0xcb,0x60,0x34,0x62,0xd2);

#ifdef __cplusplus
class DECLSPEC_UUID("ec9846b3-2762-4a6b-a214-6acb603462d2") NetFwAuthorizedApplication;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NetFwAuthorizedApplication, 0xec9846b3, 0x2762, 0x4a6b, 0xa2,0x14, 0x6a,0xcb,0x60,0x34,0x62,0xd2)
#endif
#endif

/*****************************************************************************
 * NetFwMgr coclass
 */

DEFINE_GUID(CLSID_NetFwMgr, 0x304ce942, 0x6e39, 0x40d8, 0x94,0x3a, 0xb9,0x13,0xc4,0x0c,0x9c,0xd4);

#ifdef __cplusplus
class DECLSPEC_UUID("304ce942-6e39-40d8-943a-b913c40c9cd4") NetFwMgr;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NetFwMgr, 0x304ce942, 0x6e39, 0x40d8, 0x94,0x3a, 0xb9,0x13,0xc4,0x0c,0x9c,0xd4)
#endif
#endif

/*****************************************************************************
 * NetFwPolicy2 coclass
 */

DEFINE_GUID(CLSID_NetFwPolicy2, 0xe2b3c97f, 0x6ae1, 0x41ac, 0x81,0x7a, 0xf6,0xf9,0x21,0x66,0xd7,0xdd);

#ifdef __cplusplus
class DECLSPEC_UUID("e2b3c97f-6ae1-41ac-817a-f6f92166d7dd") NetFwPolicy2;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NetFwPolicy2, 0xe2b3c97f, 0x6ae1, 0x41ac, 0x81,0x7a, 0xf6,0xf9,0x21,0x66,0xd7,0xdd)
#endif
#endif

/*****************************************************************************
 * NetFwRule coclass
 */

DEFINE_GUID(CLSID_NetFwRule, 0x2c5bc43e, 0x3369, 0x4c33, 0xab,0x0c, 0xbe,0x94,0x69,0x67,0x7a,0xf4);

#ifdef __cplusplus
class DECLSPEC_UUID("2c5bc43e-3369-4c33-ab0c-be9469677af4") NetFwRule;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NetFwRule, 0x2c5bc43e, 0x3369, 0x4c33, 0xab,0x0c, 0xbe,0x94,0x69,0x67,0x7a,0xf4)
#endif
#endif

/*****************************************************************************
 * NetFwProduct coclass
 */

DEFINE_GUID(CLSID_NetFwProduct, 0x9d745ed8, 0xc514, 0x4d1d, 0xbf,0x42, 0x75,0x1f,0xed,0x2d,0x5a,0xc7);

#ifdef __cplusplus
class DECLSPEC_UUID("9d745ed8-c514-4d1d-bf42-751fed2d5ac7") NetFwProduct;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NetFwProduct, 0x9d745ed8, 0xc514, 0x4d1d, 0xbf,0x42, 0x75,0x1f,0xed,0x2d,0x5a,0xc7)
#endif
#endif

/*****************************************************************************
 * NetFwProducts coclass
 */

DEFINE_GUID(CLSID_NetFwProducts, 0xcc19079b, 0x8272, 0x4d73, 0xbb,0x70, 0xcd,0xb5,0x33,0x52,0x7b,0x61);

#ifdef __cplusplus
class DECLSPEC_UUID("cc19079b-8272-4d73-bb70-cdb533527b61") NetFwProducts;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(NetFwProducts, 0xcc19079b, 0x8272, 0x4d73, 0xbb,0x70, 0xcd,0xb5,0x33,0x52,0x7b,0x61)
#endif
#endif

#endif /* __NetFwPublicTypeLib_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __netfw_h__ */
