 function-entry:call_stack.py:start:23
 function-entry: call_stack.py:function_1:1
 function-entry:  call_stack.py:function_3:9
function-return:  call_stack.py:function_3:10
function-return: call_stack.py:function_1:2
 function-entry: call_stack.py:function_2:5
 function-entry:  call_stack.py:function_1:1
 function-entry:   call_stack.py:function_3:9
function-return:   call_stack.py:function_3:10
function-return:  call_stack.py:function_1:2
function-return: call_stack.py:function_2:6
 function-entry: call_stack.py:function_3:9
function-return: call_stack.py:function_3:10
 function-entry: call_stack.py:function_4:13
function-return: call_stack.py:function_4:14
 function-entry: call_stack.py:function_5:18
function-return: call_stack.py:function_5:21
function-return:call_stack.py:start:28
