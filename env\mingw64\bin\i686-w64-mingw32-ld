#!/bin/sh
#
# Copyright (c) 2018 <PERSON>
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
# ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
# ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
# OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

get_dir() {
    target="$1"
    while [ -L "$target" ]; do
        cd "$(dirname "$target")"
        target="$(readlink "$(basename "$target")")"
    done
    cd "$(dirname "$target")"
    pwd
}

DIR="$(get_dir "$0")"
export PATH="$DIR":"$PATH"

BASENAME="$(basename "$0")"
TARGET="${BASENAME%-*}"
DEFAULT_TARGET=x86_64-w64-mingw32
if [ "$TARGET" = "$BASENAME" ]; then
    TARGET=$DEFAULT_TARGET
fi
ARCH="${TARGET%%-*}"
TARGET_OS="${TARGET##*-}"
case $ARCH in
i686)    M=i386pe   ;;
x86_64)  M=i386pep  ;;
armv7)   M=thumb2pe ;;
aarch64) M=arm64pe  ;;
esac
FLAGS="-m $M"
case $TARGET_OS in
mingw32uwp)
    FLAGS="$FLAGS -lwindowsapp -lucrtapp"
    ;;
esac
ld.lld $FLAGS "$@"
