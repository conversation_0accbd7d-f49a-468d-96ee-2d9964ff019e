// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/gpu/model/hlo_op_profile.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "xla/service/hlo.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto;
namespace xla {
namespace gpu {
class DeviceHloInstructionProfiles;
struct DeviceHloInstructionProfilesDefaultTypeInternal;
extern DeviceHloInstructionProfilesDefaultTypeInternal _DeviceHloInstructionProfiles_default_instance_;
class DeviceHloInstructionProfiles_EntriesEntry_DoNotUse;
struct DeviceHloInstructionProfiles_EntriesEntry_DoNotUseDefaultTypeInternal;
extern DeviceHloInstructionProfiles_EntriesEntry_DoNotUseDefaultTypeInternal _DeviceHloInstructionProfiles_EntriesEntry_DoNotUse_default_instance_;
class HloInstructionProfile;
struct HloInstructionProfileDefaultTypeInternal;
extern HloInstructionProfileDefaultTypeInternal _HloInstructionProfile_default_instance_;
class HloInstructionProfileList;
struct HloInstructionProfileListDefaultTypeInternal;
extern HloInstructionProfileListDefaultTypeInternal _HloInstructionProfileList_default_instance_;
}  // namespace gpu
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::gpu::DeviceHloInstructionProfiles* Arena::CreateMaybeMessage<::xla::gpu::DeviceHloInstructionProfiles>(Arena*);
template<> ::xla::gpu::DeviceHloInstructionProfiles_EntriesEntry_DoNotUse* Arena::CreateMaybeMessage<::xla::gpu::DeviceHloInstructionProfiles_EntriesEntry_DoNotUse>(Arena*);
template<> ::xla::gpu::HloInstructionProfile* Arena::CreateMaybeMessage<::xla::gpu::HloInstructionProfile>(Arena*);
template<> ::xla::gpu::HloInstructionProfileList* Arena::CreateMaybeMessage<::xla::gpu::HloInstructionProfileList>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {
namespace gpu {

// ===================================================================

class HloInstructionProfile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.gpu.HloInstructionProfile) */ {
 public:
  inline HloInstructionProfile() : HloInstructionProfile(nullptr) {}
  ~HloInstructionProfile() override;
  explicit PROTOBUF_CONSTEXPR HloInstructionProfile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HloInstructionProfile(const HloInstructionProfile& from);
  HloInstructionProfile(HloInstructionProfile&& from) noexcept
    : HloInstructionProfile() {
    *this = ::std::move(from);
  }

  inline HloInstructionProfile& operator=(const HloInstructionProfile& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloInstructionProfile& operator=(HloInstructionProfile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HloInstructionProfile& default_instance() {
    return *internal_default_instance();
  }
  static inline const HloInstructionProfile* internal_default_instance() {
    return reinterpret_cast<const HloInstructionProfile*>(
               &_HloInstructionProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(HloInstructionProfile& a, HloInstructionProfile& b) {
    a.Swap(&b);
  }
  inline void Swap(HloInstructionProfile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloInstructionProfile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HloInstructionProfile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HloInstructionProfile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HloInstructionProfile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HloInstructionProfile& from) {
    HloInstructionProfile::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloInstructionProfile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.gpu.HloInstructionProfile";
  }
  protected:
  explicit HloInstructionProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperandsFieldNumber = 5,
    kFingerprintFieldNumber = 3,
    kInstructionFieldNumber = 1,
    kClockCyclesFieldNumber = 2,
    kFlopsFieldNumber = 4,
  };
  // repeated .xla.HloInstructionProto operands = 5;
  int operands_size() const;
  private:
  int _internal_operands_size() const;
  public:
  void clear_operands();
  ::xla::HloInstructionProto* mutable_operands(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto >*
      mutable_operands();
  private:
  const ::xla::HloInstructionProto& _internal_operands(int index) const;
  ::xla::HloInstructionProto* _internal_add_operands();
  public:
  const ::xla::HloInstructionProto& operands(int index) const;
  ::xla::HloInstructionProto* add_operands();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto >&
      operands() const;

  // string fingerprint = 3;
  void clear_fingerprint();
  const std::string& fingerprint() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_fingerprint(ArgT0&& arg0, ArgT... args);
  std::string* mutable_fingerprint();
  PROTOBUF_NODISCARD std::string* release_fingerprint();
  void set_allocated_fingerprint(std::string* fingerprint);
  private:
  const std::string& _internal_fingerprint() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_fingerprint(const std::string& value);
  std::string* _internal_mutable_fingerprint();
  public:

  // .xla.HloInstructionProto instruction = 1;
  bool has_instruction() const;
  private:
  bool _internal_has_instruction() const;
  public:
  void clear_instruction();
  const ::xla::HloInstructionProto& instruction() const;
  PROTOBUF_NODISCARD ::xla::HloInstructionProto* release_instruction();
  ::xla::HloInstructionProto* mutable_instruction();
  void set_allocated_instruction(::xla::HloInstructionProto* instruction);
  private:
  const ::xla::HloInstructionProto& _internal_instruction() const;
  ::xla::HloInstructionProto* _internal_mutable_instruction();
  public:
  void unsafe_arena_set_allocated_instruction(
      ::xla::HloInstructionProto* instruction);
  ::xla::HloInstructionProto* unsafe_arena_release_instruction();

  // int64 clock_cycles = 2;
  void clear_clock_cycles();
  int64_t clock_cycles() const;
  void set_clock_cycles(int64_t value);
  private:
  int64_t _internal_clock_cycles() const;
  void _internal_set_clock_cycles(int64_t value);
  public:

  // int64 flops = 4;
  void clear_flops();
  int64_t flops() const;
  void set_flops(int64_t value);
  private:
  int64_t _internal_flops() const;
  void _internal_set_flops(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.gpu.HloInstructionProfile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto > operands_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr fingerprint_;
    ::xla::HloInstructionProto* instruction_;
    int64_t clock_cycles_;
    int64_t flops_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto;
};
// -------------------------------------------------------------------

class HloInstructionProfileList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.gpu.HloInstructionProfileList) */ {
 public:
  inline HloInstructionProfileList() : HloInstructionProfileList(nullptr) {}
  ~HloInstructionProfileList() override;
  explicit PROTOBUF_CONSTEXPR HloInstructionProfileList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HloInstructionProfileList(const HloInstructionProfileList& from);
  HloInstructionProfileList(HloInstructionProfileList&& from) noexcept
    : HloInstructionProfileList() {
    *this = ::std::move(from);
  }

  inline HloInstructionProfileList& operator=(const HloInstructionProfileList& from) {
    CopyFrom(from);
    return *this;
  }
  inline HloInstructionProfileList& operator=(HloInstructionProfileList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HloInstructionProfileList& default_instance() {
    return *internal_default_instance();
  }
  static inline const HloInstructionProfileList* internal_default_instance() {
    return reinterpret_cast<const HloInstructionProfileList*>(
               &_HloInstructionProfileList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(HloInstructionProfileList& a, HloInstructionProfileList& b) {
    a.Swap(&b);
  }
  inline void Swap(HloInstructionProfileList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HloInstructionProfileList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HloInstructionProfileList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HloInstructionProfileList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HloInstructionProfileList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HloInstructionProfileList& from) {
    HloInstructionProfileList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HloInstructionProfileList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.gpu.HloInstructionProfileList";
  }
  protected:
  explicit HloInstructionProfileList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEntriesFieldNumber = 1,
  };
  // repeated .xla.gpu.HloInstructionProfile entries = 1;
  int entries_size() const;
  private:
  int _internal_entries_size() const;
  public:
  void clear_entries();
  ::xla::gpu::HloInstructionProfile* mutable_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::HloInstructionProfile >*
      mutable_entries();
  private:
  const ::xla::gpu::HloInstructionProfile& _internal_entries(int index) const;
  ::xla::gpu::HloInstructionProfile* _internal_add_entries();
  public:
  const ::xla::gpu::HloInstructionProfile& entries(int index) const;
  ::xla::gpu::HloInstructionProfile* add_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::HloInstructionProfile >&
      entries() const;

  // @@protoc_insertion_point(class_scope:xla.gpu.HloInstructionProfileList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::HloInstructionProfile > entries_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto;
};
// -------------------------------------------------------------------

class DeviceHloInstructionProfiles_EntriesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceHloInstructionProfiles_EntriesEntry_DoNotUse, 
    std::string, ::xla::gpu::HloInstructionProfileList,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceHloInstructionProfiles_EntriesEntry_DoNotUse, 
    std::string, ::xla::gpu::HloInstructionProfileList,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  DeviceHloInstructionProfiles_EntriesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR DeviceHloInstructionProfiles_EntriesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit DeviceHloInstructionProfiles_EntriesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DeviceHloInstructionProfiles_EntriesEntry_DoNotUse& other);
  static const DeviceHloInstructionProfiles_EntriesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DeviceHloInstructionProfiles_EntriesEntry_DoNotUse*>(&_DeviceHloInstructionProfiles_EntriesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "xla.gpu.DeviceHloInstructionProfiles.EntriesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto;
};

// -------------------------------------------------------------------

class DeviceHloInstructionProfiles final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.gpu.DeviceHloInstructionProfiles) */ {
 public:
  inline DeviceHloInstructionProfiles() : DeviceHloInstructionProfiles(nullptr) {}
  ~DeviceHloInstructionProfiles() override;
  explicit PROTOBUF_CONSTEXPR DeviceHloInstructionProfiles(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceHloInstructionProfiles(const DeviceHloInstructionProfiles& from);
  DeviceHloInstructionProfiles(DeviceHloInstructionProfiles&& from) noexcept
    : DeviceHloInstructionProfiles() {
    *this = ::std::move(from);
  }

  inline DeviceHloInstructionProfiles& operator=(const DeviceHloInstructionProfiles& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceHloInstructionProfiles& operator=(DeviceHloInstructionProfiles&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceHloInstructionProfiles& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceHloInstructionProfiles* internal_default_instance() {
    return reinterpret_cast<const DeviceHloInstructionProfiles*>(
               &_DeviceHloInstructionProfiles_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DeviceHloInstructionProfiles& a, DeviceHloInstructionProfiles& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceHloInstructionProfiles* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceHloInstructionProfiles* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceHloInstructionProfiles* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceHloInstructionProfiles>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceHloInstructionProfiles& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceHloInstructionProfiles& from) {
    DeviceHloInstructionProfiles::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceHloInstructionProfiles* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.gpu.DeviceHloInstructionProfiles";
  }
  protected:
  explicit DeviceHloInstructionProfiles(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kEntriesFieldNumber = 2,
  };
  // map<string, .xla.gpu.HloInstructionProfileList> entries = 2;
  int entries_size() const;
  private:
  int _internal_entries_size() const;
  public:
  void clear_entries();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::gpu::HloInstructionProfileList >&
      _internal_entries() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::gpu::HloInstructionProfileList >*
      _internal_mutable_entries();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::gpu::HloInstructionProfileList >&
      entries() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::gpu::HloInstructionProfileList >*
      mutable_entries();

  // @@protoc_insertion_point(class_scope:xla.gpu.DeviceHloInstructionProfiles)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        DeviceHloInstructionProfiles_EntriesEntry_DoNotUse,
        std::string, ::xla::gpu::HloInstructionProfileList,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> entries_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// HloInstructionProfile

// .xla.HloInstructionProto instruction = 1;
inline bool HloInstructionProfile::_internal_has_instruction() const {
  return this != internal_default_instance() && _impl_.instruction_ != nullptr;
}
inline bool HloInstructionProfile::has_instruction() const {
  return _internal_has_instruction();
}
inline const ::xla::HloInstructionProto& HloInstructionProfile::_internal_instruction() const {
  const ::xla::HloInstructionProto* p = _impl_.instruction_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::HloInstructionProto&>(
      ::xla::_HloInstructionProto_default_instance_);
}
inline const ::xla::HloInstructionProto& HloInstructionProfile::instruction() const {
  // @@protoc_insertion_point(field_get:xla.gpu.HloInstructionProfile.instruction)
  return _internal_instruction();
}
inline void HloInstructionProfile::unsafe_arena_set_allocated_instruction(
    ::xla::HloInstructionProto* instruction) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.instruction_);
  }
  _impl_.instruction_ = instruction;
  if (instruction) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.gpu.HloInstructionProfile.instruction)
}
inline ::xla::HloInstructionProto* HloInstructionProfile::release_instruction() {
  
  ::xla::HloInstructionProto* temp = _impl_.instruction_;
  _impl_.instruction_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::HloInstructionProto* HloInstructionProfile::unsafe_arena_release_instruction() {
  // @@protoc_insertion_point(field_release:xla.gpu.HloInstructionProfile.instruction)
  
  ::xla::HloInstructionProto* temp = _impl_.instruction_;
  _impl_.instruction_ = nullptr;
  return temp;
}
inline ::xla::HloInstructionProto* HloInstructionProfile::_internal_mutable_instruction() {
  
  if (_impl_.instruction_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloInstructionProto>(GetArenaForAllocation());
    _impl_.instruction_ = p;
  }
  return _impl_.instruction_;
}
inline ::xla::HloInstructionProto* HloInstructionProfile::mutable_instruction() {
  ::xla::HloInstructionProto* _msg = _internal_mutable_instruction();
  // @@protoc_insertion_point(field_mutable:xla.gpu.HloInstructionProfile.instruction)
  return _msg;
}
inline void HloInstructionProfile::set_allocated_instruction(::xla::HloInstructionProto* instruction) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.instruction_);
  }
  if (instruction) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(instruction));
    if (message_arena != submessage_arena) {
      instruction = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, instruction, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.instruction_ = instruction;
  // @@protoc_insertion_point(field_set_allocated:xla.gpu.HloInstructionProfile.instruction)
}

// repeated .xla.HloInstructionProto operands = 5;
inline int HloInstructionProfile::_internal_operands_size() const {
  return _impl_.operands_.size();
}
inline int HloInstructionProfile::operands_size() const {
  return _internal_operands_size();
}
inline ::xla::HloInstructionProto* HloInstructionProfile::mutable_operands(int index) {
  // @@protoc_insertion_point(field_mutable:xla.gpu.HloInstructionProfile.operands)
  return _impl_.operands_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto >*
HloInstructionProfile::mutable_operands() {
  // @@protoc_insertion_point(field_mutable_list:xla.gpu.HloInstructionProfile.operands)
  return &_impl_.operands_;
}
inline const ::xla::HloInstructionProto& HloInstructionProfile::_internal_operands(int index) const {
  return _impl_.operands_.Get(index);
}
inline const ::xla::HloInstructionProto& HloInstructionProfile::operands(int index) const {
  // @@protoc_insertion_point(field_get:xla.gpu.HloInstructionProfile.operands)
  return _internal_operands(index);
}
inline ::xla::HloInstructionProto* HloInstructionProfile::_internal_add_operands() {
  return _impl_.operands_.Add();
}
inline ::xla::HloInstructionProto* HloInstructionProfile::add_operands() {
  ::xla::HloInstructionProto* _add = _internal_add_operands();
  // @@protoc_insertion_point(field_add:xla.gpu.HloInstructionProfile.operands)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::HloInstructionProto >&
HloInstructionProfile::operands() const {
  // @@protoc_insertion_point(field_list:xla.gpu.HloInstructionProfile.operands)
  return _impl_.operands_;
}

// int64 clock_cycles = 2;
inline void HloInstructionProfile::clear_clock_cycles() {
  _impl_.clock_cycles_ = int64_t{0};
}
inline int64_t HloInstructionProfile::_internal_clock_cycles() const {
  return _impl_.clock_cycles_;
}
inline int64_t HloInstructionProfile::clock_cycles() const {
  // @@protoc_insertion_point(field_get:xla.gpu.HloInstructionProfile.clock_cycles)
  return _internal_clock_cycles();
}
inline void HloInstructionProfile::_internal_set_clock_cycles(int64_t value) {
  
  _impl_.clock_cycles_ = value;
}
inline void HloInstructionProfile::set_clock_cycles(int64_t value) {
  _internal_set_clock_cycles(value);
  // @@protoc_insertion_point(field_set:xla.gpu.HloInstructionProfile.clock_cycles)
}

// string fingerprint = 3;
inline void HloInstructionProfile::clear_fingerprint() {
  _impl_.fingerprint_.ClearToEmpty();
}
inline const std::string& HloInstructionProfile::fingerprint() const {
  // @@protoc_insertion_point(field_get:xla.gpu.HloInstructionProfile.fingerprint)
  return _internal_fingerprint();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HloInstructionProfile::set_fingerprint(ArgT0&& arg0, ArgT... args) {
 
 _impl_.fingerprint_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.gpu.HloInstructionProfile.fingerprint)
}
inline std::string* HloInstructionProfile::mutable_fingerprint() {
  std::string* _s = _internal_mutable_fingerprint();
  // @@protoc_insertion_point(field_mutable:xla.gpu.HloInstructionProfile.fingerprint)
  return _s;
}
inline const std::string& HloInstructionProfile::_internal_fingerprint() const {
  return _impl_.fingerprint_.Get();
}
inline void HloInstructionProfile::_internal_set_fingerprint(const std::string& value) {
  
  _impl_.fingerprint_.Set(value, GetArenaForAllocation());
}
inline std::string* HloInstructionProfile::_internal_mutable_fingerprint() {
  
  return _impl_.fingerprint_.Mutable(GetArenaForAllocation());
}
inline std::string* HloInstructionProfile::release_fingerprint() {
  // @@protoc_insertion_point(field_release:xla.gpu.HloInstructionProfile.fingerprint)
  return _impl_.fingerprint_.Release();
}
inline void HloInstructionProfile::set_allocated_fingerprint(std::string* fingerprint) {
  if (fingerprint != nullptr) {
    
  } else {
    
  }
  _impl_.fingerprint_.SetAllocated(fingerprint, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.fingerprint_.IsDefault()) {
    _impl_.fingerprint_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.gpu.HloInstructionProfile.fingerprint)
}

// int64 flops = 4;
inline void HloInstructionProfile::clear_flops() {
  _impl_.flops_ = int64_t{0};
}
inline int64_t HloInstructionProfile::_internal_flops() const {
  return _impl_.flops_;
}
inline int64_t HloInstructionProfile::flops() const {
  // @@protoc_insertion_point(field_get:xla.gpu.HloInstructionProfile.flops)
  return _internal_flops();
}
inline void HloInstructionProfile::_internal_set_flops(int64_t value) {
  
  _impl_.flops_ = value;
}
inline void HloInstructionProfile::set_flops(int64_t value) {
  _internal_set_flops(value);
  // @@protoc_insertion_point(field_set:xla.gpu.HloInstructionProfile.flops)
}

// -------------------------------------------------------------------

// HloInstructionProfileList

// repeated .xla.gpu.HloInstructionProfile entries = 1;
inline int HloInstructionProfileList::_internal_entries_size() const {
  return _impl_.entries_.size();
}
inline int HloInstructionProfileList::entries_size() const {
  return _internal_entries_size();
}
inline void HloInstructionProfileList::clear_entries() {
  _impl_.entries_.Clear();
}
inline ::xla::gpu::HloInstructionProfile* HloInstructionProfileList::mutable_entries(int index) {
  // @@protoc_insertion_point(field_mutable:xla.gpu.HloInstructionProfileList.entries)
  return _impl_.entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::HloInstructionProfile >*
HloInstructionProfileList::mutable_entries() {
  // @@protoc_insertion_point(field_mutable_list:xla.gpu.HloInstructionProfileList.entries)
  return &_impl_.entries_;
}
inline const ::xla::gpu::HloInstructionProfile& HloInstructionProfileList::_internal_entries(int index) const {
  return _impl_.entries_.Get(index);
}
inline const ::xla::gpu::HloInstructionProfile& HloInstructionProfileList::entries(int index) const {
  // @@protoc_insertion_point(field_get:xla.gpu.HloInstructionProfileList.entries)
  return _internal_entries(index);
}
inline ::xla::gpu::HloInstructionProfile* HloInstructionProfileList::_internal_add_entries() {
  return _impl_.entries_.Add();
}
inline ::xla::gpu::HloInstructionProfile* HloInstructionProfileList::add_entries() {
  ::xla::gpu::HloInstructionProfile* _add = _internal_add_entries();
  // @@protoc_insertion_point(field_add:xla.gpu.HloInstructionProfileList.entries)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::gpu::HloInstructionProfile >&
HloInstructionProfileList::entries() const {
  // @@protoc_insertion_point(field_list:xla.gpu.HloInstructionProfileList.entries)
  return _impl_.entries_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DeviceHloInstructionProfiles

// map<string, .xla.gpu.HloInstructionProfileList> entries = 2;
inline int DeviceHloInstructionProfiles::_internal_entries_size() const {
  return _impl_.entries_.size();
}
inline int DeviceHloInstructionProfiles::entries_size() const {
  return _internal_entries_size();
}
inline void DeviceHloInstructionProfiles::clear_entries() {
  _impl_.entries_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::gpu::HloInstructionProfileList >&
DeviceHloInstructionProfiles::_internal_entries() const {
  return _impl_.entries_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::gpu::HloInstructionProfileList >&
DeviceHloInstructionProfiles::entries() const {
  // @@protoc_insertion_point(field_map:xla.gpu.DeviceHloInstructionProfiles.entries)
  return _internal_entries();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::gpu::HloInstructionProfileList >*
DeviceHloInstructionProfiles::_internal_mutable_entries() {
  return _impl_.entries_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::xla::gpu::HloInstructionProfileList >*
DeviceHloInstructionProfiles::mutable_entries() {
  // @@protoc_insertion_point(field_mutable_map:xla.gpu.DeviceHloInstructionProfiles.entries)
  return _internal_mutable_entries();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace gpu
}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fgpu_2fmodel_2fhlo_5fop_5fprofile_2eproto
