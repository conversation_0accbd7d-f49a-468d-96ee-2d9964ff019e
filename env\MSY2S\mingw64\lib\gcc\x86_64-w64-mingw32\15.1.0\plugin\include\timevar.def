/* This file contains the definitions for timing variables used to
   measure run-time performance of the compiler.
   Copyright (C) 2000-2025 Free Software Foundation, Inc.
   Contributed by <PERSON> <<EMAIL>>

   This file is part of GCC.

   GCC is free software; you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation; either version 3, or (at your option)
   any later version.

   GCC is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with GCC; see the file COPYING3.  If not see
   <http://www.gnu.org/licenses/>.  */

/* This file contains timing variable definitions, used by timevar.h
   and timevar.cc.

   Syntax:

     DEFTIMEVAR (id, name)

   where ID is the enumeral value used to identify the timing
   variable, and NAME is a character string describing its purpose.  */

/* The total execution time.  */
DEFTIMEVAR (TV_TOTAL                 , "total time")
/* The compiler phases.

   These must be mutually exclusive, and the NAME field must begin
   with "phase".

   Also, their sum must be within a millionth of the total time (see
   validate_phases).  */
DEFTIMEVAR (TV_PHASE_SETUP           , "phase setup")
DEFTIMEVAR (TV_PHASE_PARSING         , "phase parsing")
DEFTIMEVAR (TV_PHASE_DEFERRED        , "phase lang. deferred")
DEFTIMEVAR (TV_PHASE_LATE_PARSING_CLEANUPS, "phase late parsing cleanups")
DEFTIMEVAR (TV_PHASE_OPT_GEN         , "phase opt and generate")
DEFTIMEVAR (TV_PHASE_LATE_ASM        , "phase last asm")
DEFTIMEVAR (TV_PHASE_STREAM_IN       , "phase stream in")
DEFTIMEVAR (TV_PHASE_STREAM_OUT      , "phase stream out")
DEFTIMEVAR (TV_PHASE_FINALIZE        , "phase finalize")

/* Concurrent timers, indicated by "|".  */
DEFTIMEVAR (TV_NAME_LOOKUP           , "|name lookup")
DEFTIMEVAR (TV_OVERLOAD              , "|overload resolution")

/* Time spent garbage-collecting.  */
DEFTIMEVAR (TV_GC                    , "garbage collection")

/* Time spent generating dump files.  */
DEFTIMEVAR (TV_DUMP                  , "dump files")

/* Time spent saving/restoring PCH state.  */
DEFTIMEVAR (TV_PCH_SAVE              , "PCH main state save")
DEFTIMEVAR (TV_PCH_CPP_SAVE          , "PCH preprocessor state save")
DEFTIMEVAR (TV_PCH_PTR_REALLOC       , "PCH pointer reallocation")
DEFTIMEVAR (TV_PCH_PTR_SORT          , "PCH pointer sort")
DEFTIMEVAR (TV_PCH_RESTORE           , "PCH main state restore")
DEFTIMEVAR (TV_PCH_CPP_RESTORE       , "PCH preprocessor state restore")

DEFTIMEVAR (TV_CGRAPH                , "callgraph construction")
DEFTIMEVAR (TV_CGRAPHOPT             , "callgraph optimization")
DEFTIMEVAR (TV_CGRAPH_FUNC_EXPANSION , "callgraph functions expansion")
DEFTIMEVAR (TV_CGRAPH_IPA_PASSES     , "callgraph ipa passes")
DEFTIMEVAR (TV_IPA_ODR		     , "ipa ODR types")
DEFTIMEVAR (TV_IPA_FNSUMMARY         , "ipa function summary")
DEFTIMEVAR (TV_IPA_UNREACHABLE       , "ipa dead code removal")
DEFTIMEVAR (TV_IPA_INHERITANCE       , "ipa inheritance graph")
DEFTIMEVAR (TV_IPA_VIRTUAL_CALL      , "ipa virtual call target")
DEFTIMEVAR (TV_IPA_DEVIRT	     , "ipa devirtualization")
DEFTIMEVAR (TV_IPA_CONSTANT_PROP     , "ipa cp")
DEFTIMEVAR (TV_IPA_INLINING          , "ipa inlining heuristics")
DEFTIMEVAR (TV_IPA_FNSPLIT           , "ipa function splitting")
DEFTIMEVAR (TV_IPA_COMDATS	     , "ipa comdats")
DEFTIMEVAR (TV_IPA_OPT		     , "ipa various optimizations")
DEFTIMEVAR (TV_IPA_LTO_DECOMPRESS    , "lto stream decompression")
DEFTIMEVAR (TV_IPA_LTO_COMPRESS      , "lto stream compression")
DEFTIMEVAR (TV_IPA_LTO_OUTPUT        , "lto stream output")
DEFTIMEVAR (TV_IPA_LTO_GIMPLE_IN     , "ipa lto gimple in")
DEFTIMEVAR (TV_IPA_LTO_GIMPLE_OUT    , "ipa lto gimple out")
DEFTIMEVAR (TV_IPA_LTO_DECL_IN       , "ipa lto decl in")
DEFTIMEVAR (TV_IPA_LTO_DECL_OUT      , "ipa lto decl out")
DEFTIMEVAR (TV_IPA_LTO_CTORS_IN      , "ipa lto constructors in")
DEFTIMEVAR (TV_IPA_LTO_CTORS_OUT     , "ipa lto constructors out")
DEFTIMEVAR (TV_IPA_LTO_CGRAPH_IO     , "ipa lto cgraph I/O")
DEFTIMEVAR (TV_IPA_LTO_DECL_MERGE    , "ipa lto decl merge")
DEFTIMEVAR (TV_IPA_LTO_CGRAPH_MERGE  , "ipa lto cgraph merge")
DEFTIMEVAR (TV_LTO                   , "lto")
DEFTIMEVAR (TV_WHOPR_WPA             , "whopr wpa")
DEFTIMEVAR (TV_WHOPR_WPA_IO          , "whopr wpa I/O")
DEFTIMEVAR (TV_WHOPR_PARTITIONING    , "whopr partitioning")
DEFTIMEVAR (TV_WHOPR_LTRANS          , "whopr ltrans")
DEFTIMEVAR (TV_IPA_REFERENCE         , "ipa reference")
DEFTIMEVAR (TV_IPA_PROFILE           , "ipa profile")
DEFTIMEVAR (TV_IPA_AUTOFDO           , "auto profile")
DEFTIMEVAR (TV_IPA_PURE_CONST        , "ipa pure const")
DEFTIMEVAR (TV_IPA_ICF		     , "ipa icf")
DEFTIMEVAR (TV_IPA_PTA               , "ipa points-to")
DEFTIMEVAR (TV_IPA_SRA               , "ipa SRA")
DEFTIMEVAR (TV_IPA_LC               , "ipa locality clone")
DEFTIMEVAR (TV_IPA_FREE_LANG_DATA    , "ipa free lang data")
DEFTIMEVAR (TV_IPA_FREE_INLINE_SUMMARY, "ipa free inline summary")
DEFTIMEVAR (TV_IPA_MODREF	     , "ipa modref")
/* Time spent by constructing CFG.  */
DEFTIMEVAR (TV_CFG                   , "cfg construction")
/* Time spent by cleaning up CFG.  */
DEFTIMEVAR (TV_CLEANUP_CFG           , "cfg cleanup")
DEFTIMEVAR (TV_CFG_VERIFY            , "CFG verifier")
DEFTIMEVAR (TV_DELETE_TRIVIALLY_DEAD , "trivially dead code")

/* Time spent in dataflow problems.  */
DEFTIMEVAR (TV_DF_SCAN		     , "df scan insns")
DEFTIMEVAR (TV_DF_MD		     , "df multiple defs")
DEFTIMEVAR (TV_DF_RD		     , "df reaching defs")
DEFTIMEVAR (TV_DF_LR		     , "df live regs")
DEFTIMEVAR (TV_DF_LIVE		     , "df live&initialized regs")
DEFTIMEVAR (TV_DF_MIR		     , "df must-initialized regs")
DEFTIMEVAR (TV_DF_CHAIN		     , "df use-def / def-use chains")
DEFTIMEVAR (TV_DF_WORD_LR	     , "df live reg subwords")
DEFTIMEVAR (TV_DF_NOTE		     , "df reg dead/unused notes")
DEFTIMEVAR (TV_REG_STATS	     , "register information")

DEFTIMEVAR (TV_ALIAS_ANALYSIS	     , "alias analysis")
DEFTIMEVAR (TV_ALIAS_STMT_WALK	     , "alias stmt walking")
DEFTIMEVAR (TV_REG_SCAN		     , "register scan")
DEFTIMEVAR (TV_REBUILD_JUMP	     , "rebuild jump labels")
/* Timing in various stages of the compiler.  */
DEFTIMEVAR (TV_CPP		     , "preprocessing")
DEFTIMEVAR (TV_LEX		     , "lexical analysis")
DEFTIMEVAR (TV_PARSE_GLOBAL          , "parser (global)")
DEFTIMEVAR (TV_PARSE_STRUCT          , "parser struct body")
DEFTIMEVAR (TV_PARSE_ENUM            , "parser enumerator list")
DEFTIMEVAR (TV_PARSE_FUNC            , "parser function body")
DEFTIMEVAR (TV_PARSE_INLINE          , "parser inl. func. body")
DEFTIMEVAR (TV_PARSE_INMETH          , "parser inl. meth. body")
DEFTIMEVAR (TV_TEMPLATE_INST         , "template instantiation")
DEFTIMEVAR (TV_CONSTEXPR	     , "constant expression evaluation")
DEFTIMEVAR (TV_CONSTRAINT_NORM	     , "constraint normalization")
DEFTIMEVAR (TV_CONSTRAINT_SAT	     , "constraint satisfaction")
DEFTIMEVAR (TV_CONSTRAINT_SUB	     , "constraint subsumption")
DEFTIMEVAR (TV_MODULE_IMPORT	     , "module import")
DEFTIMEVAR (TV_MODULE_EXPORT	     , "module export")
DEFTIMEVAR (TV_MODULE_MAPPER         , "module mapper")
DEFTIMEVAR (TV_FLATTEN_INLINING      , "flatten inlining")
DEFTIMEVAR (TV_EARLY_INLINING        , "early inlining heuristics")
DEFTIMEVAR (TV_INLINE_PARAMETERS     , "inline parameters")
DEFTIMEVAR (TV_INTEGRATION           , "integration")
DEFTIMEVAR (TV_TREE_GIMPLIFY	     , "tree gimplify")
DEFTIMEVAR (TV_TREE_EH		     , "tree eh")
DEFTIMEVAR (TV_TREE_CFG		     , "tree CFG construction")
DEFTIMEVAR (TV_TREE_CLEANUP_CFG	     , "tree CFG cleanup")
DEFTIMEVAR (TV_TREE_TAIL_MERGE       , "tree tail merge")
DEFTIMEVAR (TV_TREE_VRP              , "tree VRP")
DEFTIMEVAR (TV_TREE_VRP_THREADER     , "tree VRP threader")
DEFTIMEVAR (TV_TREE_EARLY_VRP        , "tree Early VRP")
DEFTIMEVAR (TV_TREE_FAST_VRP         , "tree Fast VRP")
DEFTIMEVAR (TV_TREE_ARRAY_BOUNDS     , "warn array bounds")
DEFTIMEVAR (TV_TREE_COPY_PROP        , "tree copy propagation")
DEFTIMEVAR (TV_FIND_REFERENCED_VARS  , "tree find ref. vars")
DEFTIMEVAR (TV_TREE_PTA		     , "tree PTA")
DEFTIMEVAR (TV_TREE_SSA_OTHER	     , "tree SSA other")
DEFTIMEVAR (TV_TREE_INTO_SSA	     , "tree SSA rewrite")
DEFTIMEVAR (TV_TREE_SSA_INCREMENTAL  , "tree SSA incremental")
DEFTIMEVAR (TV_TREE_OPS	             , "tree operand scan")
DEFTIMEVAR (TV_TREE_SSA_DOMINATOR_OPTS   , "dominator optimization")
DEFTIMEVAR (TV_TREE_SSA_THREAD_JUMPS , "backwards jump threading")
DEFTIMEVAR (TV_TREE_SRA              , "tree SRA")
DEFTIMEVAR (TV_ISOLATE_ERRONEOUS_PATHS    , "isolate eroneous paths")
DEFTIMEVAR (TV_TREE_CCP		     , "tree CCP")
DEFTIMEVAR (TV_TREE_SPLIT_EDGES      , "tree split crit edges")
DEFTIMEVAR (TV_TREE_REASSOC          , "tree reassociation")
DEFTIMEVAR (TV_TREE_PRE		     , "tree PRE")
DEFTIMEVAR (TV_TREE_FRE		     , "tree FRE")
DEFTIMEVAR (TV_TREE_RPO_VN	     , "tree RPO VN")
DEFTIMEVAR (TV_TREE_SINK             , "tree code sinking")
DEFTIMEVAR (TV_TREE_PHIOPT	     , "tree linearize phis")
DEFTIMEVAR (TV_TREE_BACKPROP	     , "tree backward propagate")
DEFTIMEVAR (TV_TREE_FORWPROP	     , "tree forward propagate")
DEFTIMEVAR (TV_TREE_PHIPROP	     , "tree phiprop")
DEFTIMEVAR (TV_TREE_DCE		     , "tree conservative DCE")
DEFTIMEVAR (TV_TREE_CD_DCE	     , "tree aggressive DCE")
DEFTIMEVAR (TV_TREE_CALL_CDCE	     , "tree buildin call DCE")
DEFTIMEVAR (TV_TREE_DSE		     , "tree DSE")
DEFTIMEVAR (TV_TREE_MERGE_PHI	     , "PHI merge")
DEFTIMEVAR (TV_TREE_LOOP	     , "tree loop optimization")
DEFTIMEVAR (TV_TREE_NOLOOP           , "loopless fn")
DEFTIMEVAR (TV_TREE_LOOP_BOUNDS	     , "tree loop bounds")
DEFTIMEVAR (TV_LIM                   , "tree loop invariant motion")
DEFTIMEVAR (TV_LINTERCHANGE          , "tree loop interchange")
DEFTIMEVAR (TV_TREE_LOOP_IVCANON     , "tree canonical iv")
DEFTIMEVAR (TV_SCEV_CONST            , "scev constant prop")
DEFTIMEVAR (TV_TREE_LOOP_UNSWITCH    , "tree loop unswitching")
DEFTIMEVAR (TV_LOOP_SPLIT            , "loop splitting")
DEFTIMEVAR (TV_LOOP_JAM              , "unroll and jam")
DEFTIMEVAR (TV_COMPLETE_UNROLL       , "complete unrolling")
DEFTIMEVAR (TV_SCALAR_CLEANUP        , "scalar cleanup")
DEFTIMEVAR (TV_TREE_PARALLELIZE_LOOPS, "tree parallelize loops")
DEFTIMEVAR (TV_TREE_VECTORIZATION    , "tree vectorization")
DEFTIMEVAR (TV_TREE_SLP_VECTORIZATION, "tree slp vectorization")
DEFTIMEVAR (TV_GRAPHITE              , "Graphite")
DEFTIMEVAR (TV_GRAPHITE_TRANSFORMS   , "Graphite loop transforms")
DEFTIMEVAR (TV_GRAPHITE_DATA_DEPS    , "Graphite data dep analysis")
DEFTIMEVAR (TV_GRAPHITE_CODE_GEN     , "Graphite code generation")
DEFTIMEVAR (TV_TREE_LOOP_DISTRIBUTION, "tree loop distribution")
DEFTIMEVAR (TV_CHECK_DATA_DEPS       , "tree check data dependences")
DEFTIMEVAR (TV_TREE_PREFETCH	     , "tree prefetching")
DEFTIMEVAR (TV_TREE_LOOP_IVOPTS	     , "tree iv optimization")
DEFTIMEVAR (TV_PREDCOM		     , "predictive commoning")
DEFTIMEVAR (TV_TREE_CH		     , "tree copy headers")
DEFTIMEVAR (TV_TREE_SSA_UNCPROP	     , "tree SSA uncprop")
DEFTIMEVAR (TV_TREE_NRV		     , "tree NRV optimization")
DEFTIMEVAR (TV_TREE_COPY_RENAME	     , "tree rename SSA copies")
DEFTIMEVAR (TV_TREE_SSA_VERIFY       , "tree SSA verifier")
DEFTIMEVAR (TV_TREE_STMT_VERIFY      , "tree STMT verifier")
DEFTIMEVAR (TV_TREE_SWITCH_CONVERSION, "tree switch conversion")
DEFTIMEVAR (TV_TREE_SWITCH_LOWERING,   "tree switch lowering")
DEFTIMEVAR (TV_TREE_RECIP            , "gimple CSE reciprocals")
DEFTIMEVAR (TV_TREE_SINCOS           , "gimple CSE sin/cos")
DEFTIMEVAR (TV_TREE_POW              , "gimple expand pow")
DEFTIMEVAR (TV_TREE_WIDEN_MUL        , "gimple widening/fma detection")
DEFTIMEVAR (TV_TRANS_MEM             , "transactional memory")
DEFTIMEVAR (TV_TREE_STRLEN           , "tree strlen optimization")
DEFTIMEVAR (TV_TREE_MODREF	     , "tree modref")
DEFTIMEVAR (TV_TREE_ASSUMPTIONS      , "tree assumptions")
DEFTIMEVAR (TV_CGRAPH_VERIFY         , "callgraph verifier")
DEFTIMEVAR (TV_DOM_FRONTIERS         , "dominance frontiers")
DEFTIMEVAR (TV_DOMINANCE             , "dominance computation")
DEFTIMEVAR (TV_CONTROL_DEPENDENCES   , "control dependences")
DEFTIMEVAR (TV_OUT_OF_SSA	     , "out of ssa")
DEFTIMEVAR (TV_VAR_EXPAND	     , "expand vars")
DEFTIMEVAR (TV_EXPAND		     , "expand")
DEFTIMEVAR (TV_POST_EXPAND	     , "post expand cleanups")
DEFTIMEVAR (TV_VARCONST              , "varconst")
DEFTIMEVAR (TV_LOWER_SUBREG	     , "lower subreg")
DEFTIMEVAR (TV_JUMP                  , "jump")
DEFTIMEVAR (TV_FWPROP                , "forward prop")
DEFTIMEVAR (TV_CSE                   , "CSE")
DEFTIMEVAR (TV_DCE                   , "dead code elimination")
DEFTIMEVAR (TV_DSE1                  , "dead store elim1")
DEFTIMEVAR (TV_DSE2                  , "dead store elim2")
DEFTIMEVAR (TV_LOOP                  , "loop analysis")
DEFTIMEVAR (TV_LOOP_INIT	     , "loop init")
DEFTIMEVAR (TV_LOOP_VERSIONING	     , "loop versioning")
DEFTIMEVAR (TV_LOOP_MOVE_INVARIANTS  , "loop invariant motion")
DEFTIMEVAR (TV_LOOP_UNROLL           , "loop unrolling")
DEFTIMEVAR (TV_LOOP_DOLOOP           , "loop doloop")
DEFTIMEVAR (TV_LOOP_FINI	     , "loop fini")
DEFTIMEVAR (TV_CPROP                 , "CPROP")
DEFTIMEVAR (TV_PRE                   , "PRE")
DEFTIMEVAR (TV_HOIST                 , "code hoisting")
DEFTIMEVAR (TV_LSM                   , "LSM")
DEFTIMEVAR (TV_TRACER                , "tracer")
DEFTIMEVAR (TV_WEB                   , "web")
DEFTIMEVAR (TV_AUTO_INC_DEC          , "auto inc dec")
DEFTIMEVAR (TV_CSE2                  , "CSE 2")
DEFTIMEVAR (TV_BRANCH_PROB           , "branch prediction")
DEFTIMEVAR (TV_COMBINE               , "combiner")
DEFTIMEVAR (TV_LATE_COMBINE          , "late combiner")
DEFTIMEVAR (TV_IFCVT		     , "if-conversion")
DEFTIMEVAR (TV_MODE_SWITCH           , "mode switching")
DEFTIMEVAR (TV_SMS		     , "sms modulo scheduling")
DEFTIMEVAR (TV_LIVE_RANGE_SHRINKAGE  , "live range shrinkage")
DEFTIMEVAR (TV_SCHED                 , "scheduling")
DEFTIMEVAR (TV_AVOID_STORE_FORWARDING, "avoid store forwarding")
DEFTIMEVAR (TV_EARLY_REMAT           , "early rematerialization")
DEFTIMEVAR (TV_IRA		     , "integrated RA")
DEFTIMEVAR (TV_LRA		     , "LRA non-specific")
DEFTIMEVAR (TV_LRA_ELIMINATE	     , "LRA virtuals elimination")
DEFTIMEVAR (TV_LRA_INHERITANCE	     , "LRA reload inheritance")
DEFTIMEVAR (TV_LRA_CREATE_LIVE_RANGES, "LRA create live ranges")
DEFTIMEVAR (TV_LRA_ASSIGN	     , "LRA hard reg assignment")
DEFTIMEVAR (TV_LRA_COALESCE	     , "LRA coalesce pseudo regs")
DEFTIMEVAR (TV_LRA_REMAT	     , "LRA rematerialization")
DEFTIMEVAR (TV_RELOAD		     , "reload")
DEFTIMEVAR (TV_RELOAD_CSE_REGS       , "reload CSE regs")
DEFTIMEVAR (TV_GCSE_AFTER_RELOAD     , "load CSE after reload")
DEFTIMEVAR (TV_REE		     , "ree")
DEFTIMEVAR (TV_THREAD_PROLOGUE_AND_EPILOGUE, "thread pro- & epilogue")
DEFTIMEVAR (TV_IFCVT2		     , "if-conversion 2")
DEFTIMEVAR (TV_SPLIT_PATHS	     , "split paths")
DEFTIMEVAR (TV_COMBINE_STACK_ADJUST  , "combine stack adjustments")
DEFTIMEVAR (TV_PEEPHOLE2             , "peephole 2")
DEFTIMEVAR (TV_RENAME_REGISTERS      , "rename registers")
DEFTIMEVAR (TV_SCHED_FUSION          , "scheduling fusion")
DEFTIMEVAR (TV_CPROP_REGISTERS       , "hard reg cprop")
DEFTIMEVAR (TV_SCHED2                , "scheduling 2")
DEFTIMEVAR (TV_MACH_DEP              , "machine dep reorg")
DEFTIMEVAR (TV_DBR_SCHED             , "delay branch sched")
DEFTIMEVAR (TV_REORDER_BLOCKS        , "reorder blocks")
DEFTIMEVAR (TV_DUP_COMPGOTO          , "duplicate computed gotos")
DEFTIMEVAR (TV_SHORTEN_BRANCH        , "shorten branches")
DEFTIMEVAR (TV_REG_STACK             , "reg stack")
DEFTIMEVAR (TV_FINAL                 , "final")
DEFTIMEVAR (TV_VAROUT                , "variable output")
DEFTIMEVAR (TV_SYMOUT                , "symout")
DEFTIMEVAR (TV_VAR_TRACKING          , "variable tracking")
DEFTIMEVAR (TV_VAR_TRACKING_DATAFLOW , "var-tracking dataflow")
DEFTIMEVAR (TV_VAR_TRACKING_EMIT     , "var-tracking emit")
DEFTIMEVAR (TV_TREE_IFCOMBINE        , "tree if-combine")
DEFTIMEVAR (TV_TREE_IF_TO_SWITCH     , "if to switch conversion")
DEFTIMEVAR (TV_TREE_UNINIT           , "uninit var analysis")
DEFTIMEVAR (TV_PLUGIN_INIT           , "plugin initialization")
DEFTIMEVAR (TV_PLUGIN_RUN            , "plugin execution")
DEFTIMEVAR (TV_GIMPLE_SLSR           , "straight-line strength reduction")
DEFTIMEVAR (TV_GIMPLE_STORE_MERGING  , "store merging")
DEFTIMEVAR (TV_VTABLE_VERIFICATION   , "vtable verification")
DEFTIMEVAR (TV_TREE_UBSAN            , "tree ubsan")
DEFTIMEVAR (TV_INITIALIZE_RTL        , "initialize rtl")
DEFTIMEVAR (TV_GIMPLE_LADDRESS       , "address lowering")
DEFTIMEVAR (TV_TREE_LOOP_IFCVT       , "tree loop if-conversion")
DEFTIMEVAR (TV_WARN_ACCESS           , "access analysis")
DEFTIMEVAR (TV_GIMPLE_CRC_OPTIMIZATION, "crc optimization")
DEFTIMEVAR (TV_EXT_DCE               , "ext dce")
DEFTIMEVAR (TV_FOLD_MEM_OFFSETS      , "fold mem offsets")

/* Everything else in rest_of_compilation not included above.  */
DEFTIMEVAR (TV_EARLY_LOCAL	     , "early local passes")
DEFTIMEVAR (TV_OPTIMIZE		     , "unaccounted optimizations")
DEFTIMEVAR (TV_REST_OF_COMPILATION   , "rest of compilation")
DEFTIMEVAR (TV_POSTRELOAD	     , "unaccounted post reload")
DEFTIMEVAR (TV_LATE_COMPILATION	     , "unaccounted late compilation")
DEFTIMEVAR (TV_REMOVE_UNUSED	     , "remove unused locals")
DEFTIMEVAR (TV_ADDRESS_TAKEN	     , "address taken")
DEFTIMEVAR (TV_TODO		     , "unaccounted todo")
DEFTIMEVAR (TV_VERIFY_LOOP_CLOSED    , "verify loop closed")
DEFTIMEVAR (TV_VERIFY_RTL_SHARING    , "verify RTL sharing")
DEFTIMEVAR (TV_REBUILD_FREQUENCIES   , "rebuild frequencies")
DEFTIMEVAR (TV_REPAIR_LOOPS	     , "repair loop structures")

/* Stuff used by libgccjit.so.  */
DEFTIMEVAR (TV_JIT_REPLAY	     , "replay of JIT client activity")
DEFTIMEVAR (TV_ASSEMBLE	     , "assemble JIT code")
DEFTIMEVAR (TV_LINK		     , "link JIT code")
DEFTIMEVAR (TV_LOAD		     , "load JIT result")
DEFTIMEVAR (TV_JIT_ACQUIRING_MUTEX   , "acquiring JIT mutex")
DEFTIMEVAR (TV_JIT_CLIENT_CODE   , "JIT client code")

/* Analyzer timevars.  */
DEFTIMEVAR (TV_ANALYZER              , "analyzer")
DEFTIMEVAR (TV_ANALYZER_SUPERGRAPH   , "analyzer: supergraph")
DEFTIMEVAR (TV_ANALYZER_STATE_PURGE  , "analyzer: state purge")
DEFTIMEVAR (TV_ANALYZER_PLAN         , "analyzer: planning")
DEFTIMEVAR (TV_ANALYZER_SCC          , "analyzer: scc")
DEFTIMEVAR (TV_ANALYZER_WORKLIST     , "analyzer: processing worklist")
DEFTIMEVAR (TV_ANALYZER_INFINITE_LOOPS, "analyzer: finding infinite loops")
DEFTIMEVAR (TV_ANALYZER_DUMP         , "analyzer: dump")
DEFTIMEVAR (TV_ANALYZER_DIAGNOSTICS  , "analyzer: emitting diagnostics")
DEFTIMEVAR (TV_ANALYZER_SHORTEST_PATHS, "analyzer: shortest paths")
