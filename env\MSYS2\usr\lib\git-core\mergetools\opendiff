diff_cmd () {
	"$merge_tool_path" "$LOCAL" "$REMOTE" | cat
}

diff_cmd_help () {
	echo "Use FileMerge (requires a graphical session)"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" "$LOCAL" "$REMOTE" \
			-ancestor "$BASE" -merge "$MERGED" | cat
	else
		"$merge_tool_path" "$LOCAL" "$REMOTE" \
			-merge "$MERGED" | cat
	fi
}

merge_cmd_help () {
	echo "Use FileMerge (requires a graphical session)"
}
