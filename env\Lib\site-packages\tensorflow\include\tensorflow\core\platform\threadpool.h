/* Copyright 2015 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_PLATFORM_THREADPOOL_H_
#define TENSORFLOW_CORE_PLATFORM_THREADPOOL_H_

#include <functional>
#include <memory>

#include "absl/types/optional.h"
#include "tensorflow/core/platform/env.h"
#include "tensorflow/core/platform/macros.h"
#include "tensorflow/core/platform/threadpool_interface.h"
#include "tensorflow/core/platform/types.h"
#include "tsl/platform/threadpool.h"

namespace tensorflow {
namespace thread {
using tsl::thread::EigenEnvironment;  // NOLINT
using tsl::thread::ThreadPool;        // NOLINT

}  // namespace thread
}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_PLATFORM_THREADPOOL_H_
