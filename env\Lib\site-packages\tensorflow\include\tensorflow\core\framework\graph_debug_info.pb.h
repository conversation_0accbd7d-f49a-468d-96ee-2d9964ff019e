// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/graph_debug_info.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto;
namespace tensorflow {
class GraphDebugInfo;
struct GraphDebugInfoDefaultTypeInternal;
extern GraphDebugInfoDefaultTypeInternal _GraphDebugInfo_default_instance_;
class GraphDebugInfo_FileLineCol;
struct GraphDebugInfo_FileLineColDefaultTypeInternal;
extern GraphDebugInfo_FileLineColDefaultTypeInternal _GraphDebugInfo_FileLineCol_default_instance_;
class GraphDebugInfo_FramesByIdEntry_DoNotUse;
struct GraphDebugInfo_FramesByIdEntry_DoNotUseDefaultTypeInternal;
extern GraphDebugInfo_FramesByIdEntry_DoNotUseDefaultTypeInternal _GraphDebugInfo_FramesByIdEntry_DoNotUse_default_instance_;
class GraphDebugInfo_NameToTraceIdEntry_DoNotUse;
struct GraphDebugInfo_NameToTraceIdEntry_DoNotUseDefaultTypeInternal;
extern GraphDebugInfo_NameToTraceIdEntry_DoNotUseDefaultTypeInternal _GraphDebugInfo_NameToTraceIdEntry_DoNotUse_default_instance_;
class GraphDebugInfo_StackTrace;
struct GraphDebugInfo_StackTraceDefaultTypeInternal;
extern GraphDebugInfo_StackTraceDefaultTypeInternal _GraphDebugInfo_StackTrace_default_instance_;
class GraphDebugInfo_TracesByIdEntry_DoNotUse;
struct GraphDebugInfo_TracesByIdEntry_DoNotUseDefaultTypeInternal;
extern GraphDebugInfo_TracesByIdEntry_DoNotUseDefaultTypeInternal _GraphDebugInfo_TracesByIdEntry_DoNotUse_default_instance_;
class GraphDebugInfo_TracesEntry_DoNotUse;
struct GraphDebugInfo_TracesEntry_DoNotUseDefaultTypeInternal;
extern GraphDebugInfo_TracesEntry_DoNotUseDefaultTypeInternal _GraphDebugInfo_TracesEntry_DoNotUse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::GraphDebugInfo* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo>(Arena*);
template<> ::tensorflow::GraphDebugInfo_FileLineCol* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_FileLineCol>(Arena*);
template<> ::tensorflow::GraphDebugInfo_FramesByIdEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_FramesByIdEntry_DoNotUse>(Arena*);
template<> ::tensorflow::GraphDebugInfo_NameToTraceIdEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_NameToTraceIdEntry_DoNotUse>(Arena*);
template<> ::tensorflow::GraphDebugInfo_StackTrace* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_StackTrace>(Arena*);
template<> ::tensorflow::GraphDebugInfo_TracesByIdEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_TracesByIdEntry_DoNotUse>(Arena*);
template<> ::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::GraphDebugInfo_TracesEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class GraphDebugInfo_FileLineCol final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphDebugInfo.FileLineCol) */ {
 public:
  inline GraphDebugInfo_FileLineCol() : GraphDebugInfo_FileLineCol(nullptr) {}
  ~GraphDebugInfo_FileLineCol() override;
  explicit PROTOBUF_CONSTEXPR GraphDebugInfo_FileLineCol(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphDebugInfo_FileLineCol(const GraphDebugInfo_FileLineCol& from);
  GraphDebugInfo_FileLineCol(GraphDebugInfo_FileLineCol&& from) noexcept
    : GraphDebugInfo_FileLineCol() {
    *this = ::std::move(from);
  }

  inline GraphDebugInfo_FileLineCol& operator=(const GraphDebugInfo_FileLineCol& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphDebugInfo_FileLineCol& operator=(GraphDebugInfo_FileLineCol&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphDebugInfo_FileLineCol& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphDebugInfo_FileLineCol* internal_default_instance() {
    return reinterpret_cast<const GraphDebugInfo_FileLineCol*>(
               &_GraphDebugInfo_FileLineCol_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GraphDebugInfo_FileLineCol& a, GraphDebugInfo_FileLineCol& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphDebugInfo_FileLineCol* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphDebugInfo_FileLineCol* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphDebugInfo_FileLineCol* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphDebugInfo_FileLineCol>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphDebugInfo_FileLineCol& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphDebugInfo_FileLineCol& from) {
    GraphDebugInfo_FileLineCol::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphDebugInfo_FileLineCol* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphDebugInfo.FileLineCol";
  }
  protected:
  explicit GraphDebugInfo_FileLineCol(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFuncFieldNumber = 4,
    kCodeFieldNumber = 5,
    kFileIndexFieldNumber = 1,
    kLineFieldNumber = 2,
    kColFieldNumber = 3,
  };
  // optional string func = 4;
  bool has_func() const;
  private:
  bool _internal_has_func() const;
  public:
  void clear_func();
  const std::string& func() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_func(ArgT0&& arg0, ArgT... args);
  std::string* mutable_func();
  PROTOBUF_NODISCARD std::string* release_func();
  void set_allocated_func(std::string* func);
  private:
  const std::string& _internal_func() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_func(const std::string& value);
  std::string* _internal_mutable_func();
  public:

  // optional string code = 5;
  bool has_code() const;
  private:
  bool _internal_has_code() const;
  public:
  void clear_code();
  const std::string& code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_code();
  PROTOBUF_NODISCARD std::string* release_code();
  void set_allocated_code(std::string* code);
  private:
  const std::string& _internal_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_code(const std::string& value);
  std::string* _internal_mutable_code();
  public:

  // optional int32 file_index = 1;
  bool has_file_index() const;
  private:
  bool _internal_has_file_index() const;
  public:
  void clear_file_index();
  int32_t file_index() const;
  void set_file_index(int32_t value);
  private:
  int32_t _internal_file_index() const;
  void _internal_set_file_index(int32_t value);
  public:

  // optional int32 line = 2;
  bool has_line() const;
  private:
  bool _internal_has_line() const;
  public:
  void clear_line();
  int32_t line() const;
  void set_line(int32_t value);
  private:
  int32_t _internal_line() const;
  void _internal_set_line(int32_t value);
  public:

  // optional int32 col = 3;
  bool has_col() const;
  private:
  bool _internal_has_col() const;
  public:
  void clear_col();
  int32_t col() const;
  void set_col(int32_t value);
  private:
  int32_t _internal_col() const;
  void _internal_set_col(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphDebugInfo.FileLineCol)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr func_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr code_;
    int32_t file_index_;
    int32_t line_;
    int32_t col_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphDebugInfo_StackTrace final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphDebugInfo.StackTrace) */ {
 public:
  inline GraphDebugInfo_StackTrace() : GraphDebugInfo_StackTrace(nullptr) {}
  ~GraphDebugInfo_StackTrace() override;
  explicit PROTOBUF_CONSTEXPR GraphDebugInfo_StackTrace(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphDebugInfo_StackTrace(const GraphDebugInfo_StackTrace& from);
  GraphDebugInfo_StackTrace(GraphDebugInfo_StackTrace&& from) noexcept
    : GraphDebugInfo_StackTrace() {
    *this = ::std::move(from);
  }

  inline GraphDebugInfo_StackTrace& operator=(const GraphDebugInfo_StackTrace& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphDebugInfo_StackTrace& operator=(GraphDebugInfo_StackTrace&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphDebugInfo_StackTrace& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphDebugInfo_StackTrace* internal_default_instance() {
    return reinterpret_cast<const GraphDebugInfo_StackTrace*>(
               &_GraphDebugInfo_StackTrace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GraphDebugInfo_StackTrace& a, GraphDebugInfo_StackTrace& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphDebugInfo_StackTrace* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphDebugInfo_StackTrace* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphDebugInfo_StackTrace* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphDebugInfo_StackTrace>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphDebugInfo_StackTrace& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphDebugInfo_StackTrace& from) {
    GraphDebugInfo_StackTrace::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphDebugInfo_StackTrace* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphDebugInfo.StackTrace";
  }
  protected:
  explicit GraphDebugInfo_StackTrace(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFileLineColsFieldNumber = 1,
    kFrameIdFieldNumber = 2,
  };
  // repeated .tensorflow.GraphDebugInfo.FileLineCol file_line_cols = 1;
  int file_line_cols_size() const;
  private:
  int _internal_file_line_cols_size() const;
  public:
  void clear_file_line_cols();
  ::tensorflow::GraphDebugInfo_FileLineCol* mutable_file_line_cols(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol >*
      mutable_file_line_cols();
  private:
  const ::tensorflow::GraphDebugInfo_FileLineCol& _internal_file_line_cols(int index) const;
  ::tensorflow::GraphDebugInfo_FileLineCol* _internal_add_file_line_cols();
  public:
  const ::tensorflow::GraphDebugInfo_FileLineCol& file_line_cols(int index) const;
  ::tensorflow::GraphDebugInfo_FileLineCol* add_file_line_cols();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol >&
      file_line_cols() const;

  // repeated fixed64 frame_id = 2 [packed = true];
  int frame_id_size() const;
  private:
  int _internal_frame_id_size() const;
  public:
  void clear_frame_id();
  private:
  uint64_t _internal_frame_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_frame_id() const;
  void _internal_add_frame_id(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_frame_id();
  public:
  uint64_t frame_id(int index) const;
  void set_frame_id(int index, uint64_t value);
  void add_frame_id(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      frame_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_frame_id();

  // @@protoc_insertion_point(class_scope:tensorflow.GraphDebugInfo.StackTrace)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol > file_line_cols_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > frame_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphDebugInfo_FramesByIdEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphDebugInfo_FramesByIdEntry_DoNotUse, 
    uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphDebugInfo_FramesByIdEntry_DoNotUse, 
    uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GraphDebugInfo_FramesByIdEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR GraphDebugInfo_FramesByIdEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GraphDebugInfo_FramesByIdEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GraphDebugInfo_FramesByIdEntry_DoNotUse& other);
  static const GraphDebugInfo_FramesByIdEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GraphDebugInfo_FramesByIdEntry_DoNotUse*>(&_GraphDebugInfo_FramesByIdEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto;
};

// -------------------------------------------------------------------

class GraphDebugInfo_TracesByIdEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphDebugInfo_TracesByIdEntry_DoNotUse, 
    uint64_t, ::tensorflow::GraphDebugInfo_StackTrace,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphDebugInfo_TracesByIdEntry_DoNotUse, 
    uint64_t, ::tensorflow::GraphDebugInfo_StackTrace,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GraphDebugInfo_TracesByIdEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR GraphDebugInfo_TracesByIdEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GraphDebugInfo_TracesByIdEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GraphDebugInfo_TracesByIdEntry_DoNotUse& other);
  static const GraphDebugInfo_TracesByIdEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GraphDebugInfo_TracesByIdEntry_DoNotUse*>(&_GraphDebugInfo_TracesByIdEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto;
};

// -------------------------------------------------------------------

class GraphDebugInfo_TracesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphDebugInfo_TracesEntry_DoNotUse, 
    std::string, ::tensorflow::GraphDebugInfo_StackTrace,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphDebugInfo_TracesEntry_DoNotUse, 
    std::string, ::tensorflow::GraphDebugInfo_StackTrace,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GraphDebugInfo_TracesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR GraphDebugInfo_TracesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GraphDebugInfo_TracesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GraphDebugInfo_TracesEntry_DoNotUse& other);
  static const GraphDebugInfo_TracesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GraphDebugInfo_TracesEntry_DoNotUse*>(&_GraphDebugInfo_TracesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
#ifndef NDEBUG
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
       s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.GraphDebugInfo.TracesEntry.key");
#else
    (void) s;
#endif
    return true;
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto;
};

// -------------------------------------------------------------------

class GraphDebugInfo_NameToTraceIdEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphDebugInfo_NameToTraceIdEntry_DoNotUse, 
    std::string, uint64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphDebugInfo_NameToTraceIdEntry_DoNotUse, 
    std::string, uint64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64> SuperType;
  GraphDebugInfo_NameToTraceIdEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR GraphDebugInfo_NameToTraceIdEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GraphDebugInfo_NameToTraceIdEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GraphDebugInfo_NameToTraceIdEntry_DoNotUse& other);
  static const GraphDebugInfo_NameToTraceIdEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GraphDebugInfo_NameToTraceIdEntry_DoNotUse*>(&_GraphDebugInfo_NameToTraceIdEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
#ifndef NDEBUG
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
       s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.GraphDebugInfo.NameToTraceIdEntry.key");
#else
    (void) s;
#endif
    return true;
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto;
};

// -------------------------------------------------------------------

class GraphDebugInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphDebugInfo) */ {
 public:
  inline GraphDebugInfo() : GraphDebugInfo(nullptr) {}
  ~GraphDebugInfo() override;
  explicit PROTOBUF_CONSTEXPR GraphDebugInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphDebugInfo(const GraphDebugInfo& from);
  GraphDebugInfo(GraphDebugInfo&& from) noexcept
    : GraphDebugInfo() {
    *this = ::std::move(from);
  }

  inline GraphDebugInfo& operator=(const GraphDebugInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphDebugInfo& operator=(GraphDebugInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphDebugInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphDebugInfo* internal_default_instance() {
    return reinterpret_cast<const GraphDebugInfo*>(
               &_GraphDebugInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(GraphDebugInfo& a, GraphDebugInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphDebugInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphDebugInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphDebugInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphDebugInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphDebugInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphDebugInfo& from) {
    GraphDebugInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphDebugInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphDebugInfo";
  }
  protected:
  explicit GraphDebugInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef GraphDebugInfo_FileLineCol FileLineCol;
  typedef GraphDebugInfo_StackTrace StackTrace;

  // accessors -------------------------------------------------------

  enum : int {
    kFilesFieldNumber = 1,
    kTracesFieldNumber = 2,
    kFramesByIdFieldNumber = 4,
    kNameToTraceIdFieldNumber = 5,
    kTracesByIdFieldNumber = 6,
  };
  // repeated string files = 1;
  int files_size() const;
  private:
  int _internal_files_size() const;
  public:
  void clear_files();
  const std::string& files(int index) const;
  std::string* mutable_files(int index);
  void set_files(int index, const std::string& value);
  void set_files(int index, std::string&& value);
  void set_files(int index, const char* value);
  void set_files(int index, const char* value, size_t size);
  std::string* add_files();
  void add_files(const std::string& value);
  void add_files(std::string&& value);
  void add_files(const char* value);
  void add_files(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& files() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_files();
  private:
  const std::string& _internal_files(int index) const;
  std::string* _internal_add_files();
  public:

  // map<string, .tensorflow.GraphDebugInfo.StackTrace> traces = 2;
  int traces_size() const;
  private:
  int _internal_traces_size() const;
  public:
  void clear_traces();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::GraphDebugInfo_StackTrace >&
      _internal_traces() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::GraphDebugInfo_StackTrace >*
      _internal_mutable_traces();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::GraphDebugInfo_StackTrace >&
      traces() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::GraphDebugInfo_StackTrace >*
      mutable_traces();

  // map<fixed64, .tensorflow.GraphDebugInfo.FileLineCol> frames_by_id = 4;
  int frames_by_id_size() const;
  private:
  int _internal_frames_by_id_size() const;
  public:
  void clear_frames_by_id();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol >&
      _internal_frames_by_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol >*
      _internal_mutable_frames_by_id();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol >&
      frames_by_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol >*
      mutable_frames_by_id();

  // map<string, fixed64> name_to_trace_id = 5;
  int name_to_trace_id_size() const;
  private:
  int _internal_name_to_trace_id_size() const;
  public:
  void clear_name_to_trace_id();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >&
      _internal_name_to_trace_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >*
      _internal_mutable_name_to_trace_id();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >&
      name_to_trace_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >*
      mutable_name_to_trace_id();

  // map<fixed64, .tensorflow.GraphDebugInfo.StackTrace> traces_by_id = 6;
  int traces_by_id_size() const;
  private:
  int _internal_traces_by_id_size() const;
  public:
  void clear_traces_by_id();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_StackTrace >&
      _internal_traces_by_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_StackTrace >*
      _internal_mutable_traces_by_id();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_StackTrace >&
      traces_by_id() const;
  ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_StackTrace >*
      mutable_traces_by_id();

  // @@protoc_insertion_point(class_scope:tensorflow.GraphDebugInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> files_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        GraphDebugInfo_TracesEntry_DoNotUse,
        std::string, ::tensorflow::GraphDebugInfo_StackTrace,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> traces_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        GraphDebugInfo_FramesByIdEntry_DoNotUse,
        uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> frames_by_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        GraphDebugInfo_NameToTraceIdEntry_DoNotUse,
        std::string, uint64_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64> name_to_trace_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        GraphDebugInfo_TracesByIdEntry_DoNotUse,
        uint64_t, ::tensorflow::GraphDebugInfo_StackTrace,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FIXED64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> traces_by_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GraphDebugInfo_FileLineCol

// optional int32 file_index = 1;
inline bool GraphDebugInfo_FileLineCol::_internal_has_file_index() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool GraphDebugInfo_FileLineCol::has_file_index() const {
  return _internal_has_file_index();
}
inline void GraphDebugInfo_FileLineCol::clear_file_index() {
  _impl_.file_index_ = 0;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline int32_t GraphDebugInfo_FileLineCol::_internal_file_index() const {
  return _impl_.file_index_;
}
inline int32_t GraphDebugInfo_FileLineCol::file_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.file_index)
  return _internal_file_index();
}
inline void GraphDebugInfo_FileLineCol::_internal_set_file_index(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.file_index_ = value;
}
inline void GraphDebugInfo_FileLineCol::set_file_index(int32_t value) {
  _internal_set_file_index(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.file_index)
}

// optional int32 line = 2;
inline bool GraphDebugInfo_FileLineCol::_internal_has_line() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool GraphDebugInfo_FileLineCol::has_line() const {
  return _internal_has_line();
}
inline void GraphDebugInfo_FileLineCol::clear_line() {
  _impl_.line_ = 0;
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline int32_t GraphDebugInfo_FileLineCol::_internal_line() const {
  return _impl_.line_;
}
inline int32_t GraphDebugInfo_FileLineCol::line() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.line)
  return _internal_line();
}
inline void GraphDebugInfo_FileLineCol::_internal_set_line(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.line_ = value;
}
inline void GraphDebugInfo_FileLineCol::set_line(int32_t value) {
  _internal_set_line(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.line)
}

// optional int32 col = 3;
inline bool GraphDebugInfo_FileLineCol::_internal_has_col() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool GraphDebugInfo_FileLineCol::has_col() const {
  return _internal_has_col();
}
inline void GraphDebugInfo_FileLineCol::clear_col() {
  _impl_.col_ = 0;
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline int32_t GraphDebugInfo_FileLineCol::_internal_col() const {
  return _impl_.col_;
}
inline int32_t GraphDebugInfo_FileLineCol::col() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.col)
  return _internal_col();
}
inline void GraphDebugInfo_FileLineCol::_internal_set_col(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.col_ = value;
}
inline void GraphDebugInfo_FileLineCol::set_col(int32_t value) {
  _internal_set_col(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.col)
}

// optional string func = 4;
inline bool GraphDebugInfo_FileLineCol::_internal_has_func() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool GraphDebugInfo_FileLineCol::has_func() const {
  return _internal_has_func();
}
inline void GraphDebugInfo_FileLineCol::clear_func() {
  _impl_.func_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& GraphDebugInfo_FileLineCol::func() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.func)
  return _internal_func();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphDebugInfo_FileLineCol::set_func(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.func_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.func)
}
inline std::string* GraphDebugInfo_FileLineCol::mutable_func() {
  std::string* _s = _internal_mutable_func();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDebugInfo.FileLineCol.func)
  return _s;
}
inline const std::string& GraphDebugInfo_FileLineCol::_internal_func() const {
  return _impl_.func_.Get();
}
inline void GraphDebugInfo_FileLineCol::_internal_set_func(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.func_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphDebugInfo_FileLineCol::_internal_mutable_func() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.func_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphDebugInfo_FileLineCol::release_func() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphDebugInfo.FileLineCol.func)
  if (!_internal_has_func()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.func_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.func_.IsDefault()) {
    _impl_.func_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void GraphDebugInfo_FileLineCol::set_allocated_func(std::string* func) {
  if (func != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.func_.SetAllocated(func, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.func_.IsDefault()) {
    _impl_.func_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphDebugInfo.FileLineCol.func)
}

// optional string code = 5;
inline bool GraphDebugInfo_FileLineCol::_internal_has_code() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool GraphDebugInfo_FileLineCol::has_code() const {
  return _internal_has_code();
}
inline void GraphDebugInfo_FileLineCol::clear_code() {
  _impl_.code_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& GraphDebugInfo_FileLineCol::code() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.FileLineCol.code)
  return _internal_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphDebugInfo_FileLineCol::set_code(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.code_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.FileLineCol.code)
}
inline std::string* GraphDebugInfo_FileLineCol::mutable_code() {
  std::string* _s = _internal_mutable_code();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDebugInfo.FileLineCol.code)
  return _s;
}
inline const std::string& GraphDebugInfo_FileLineCol::_internal_code() const {
  return _impl_.code_.Get();
}
inline void GraphDebugInfo_FileLineCol::_internal_set_code(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.code_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphDebugInfo_FileLineCol::_internal_mutable_code() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.code_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphDebugInfo_FileLineCol::release_code() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphDebugInfo.FileLineCol.code)
  if (!_internal_has_code()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.code_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.code_.IsDefault()) {
    _impl_.code_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void GraphDebugInfo_FileLineCol::set_allocated_code(std::string* code) {
  if (code != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.code_.SetAllocated(code, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.code_.IsDefault()) {
    _impl_.code_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphDebugInfo.FileLineCol.code)
}

// -------------------------------------------------------------------

// GraphDebugInfo_StackTrace

// repeated .tensorflow.GraphDebugInfo.FileLineCol file_line_cols = 1;
inline int GraphDebugInfo_StackTrace::_internal_file_line_cols_size() const {
  return _impl_.file_line_cols_.size();
}
inline int GraphDebugInfo_StackTrace::file_line_cols_size() const {
  return _internal_file_line_cols_size();
}
inline void GraphDebugInfo_StackTrace::clear_file_line_cols() {
  _impl_.file_line_cols_.Clear();
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* GraphDebugInfo_StackTrace::mutable_file_line_cols(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return _impl_.file_line_cols_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol >*
GraphDebugInfo_StackTrace::mutable_file_line_cols() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return &_impl_.file_line_cols_;
}
inline const ::tensorflow::GraphDebugInfo_FileLineCol& GraphDebugInfo_StackTrace::_internal_file_line_cols(int index) const {
  return _impl_.file_line_cols_.Get(index);
}
inline const ::tensorflow::GraphDebugInfo_FileLineCol& GraphDebugInfo_StackTrace::file_line_cols(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return _internal_file_line_cols(index);
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* GraphDebugInfo_StackTrace::_internal_add_file_line_cols() {
  return _impl_.file_line_cols_.Add();
}
inline ::tensorflow::GraphDebugInfo_FileLineCol* GraphDebugInfo_StackTrace::add_file_line_cols() {
  ::tensorflow::GraphDebugInfo_FileLineCol* _add = _internal_add_file_line_cols();
  // @@protoc_insertion_point(field_add:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDebugInfo_FileLineCol >&
GraphDebugInfo_StackTrace::file_line_cols() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphDebugInfo.StackTrace.file_line_cols)
  return _impl_.file_line_cols_;
}

// repeated fixed64 frame_id = 2 [packed = true];
inline int GraphDebugInfo_StackTrace::_internal_frame_id_size() const {
  return _impl_.frame_id_.size();
}
inline int GraphDebugInfo_StackTrace::frame_id_size() const {
  return _internal_frame_id_size();
}
inline void GraphDebugInfo_StackTrace::clear_frame_id() {
  _impl_.frame_id_.Clear();
}
inline uint64_t GraphDebugInfo_StackTrace::_internal_frame_id(int index) const {
  return _impl_.frame_id_.Get(index);
}
inline uint64_t GraphDebugInfo_StackTrace::frame_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.StackTrace.frame_id)
  return _internal_frame_id(index);
}
inline void GraphDebugInfo_StackTrace::set_frame_id(int index, uint64_t value) {
  _impl_.frame_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.StackTrace.frame_id)
}
inline void GraphDebugInfo_StackTrace::_internal_add_frame_id(uint64_t value) {
  _impl_.frame_id_.Add(value);
}
inline void GraphDebugInfo_StackTrace::add_frame_id(uint64_t value) {
  _internal_add_frame_id(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphDebugInfo.StackTrace.frame_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
GraphDebugInfo_StackTrace::_internal_frame_id() const {
  return _impl_.frame_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
GraphDebugInfo_StackTrace::frame_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphDebugInfo.StackTrace.frame_id)
  return _internal_frame_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
GraphDebugInfo_StackTrace::_internal_mutable_frame_id() {
  return &_impl_.frame_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
GraphDebugInfo_StackTrace::mutable_frame_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphDebugInfo.StackTrace.frame_id)
  return _internal_mutable_frame_id();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GraphDebugInfo

// repeated string files = 1;
inline int GraphDebugInfo::_internal_files_size() const {
  return _impl_.files_.size();
}
inline int GraphDebugInfo::files_size() const {
  return _internal_files_size();
}
inline void GraphDebugInfo::clear_files() {
  _impl_.files_.Clear();
}
inline std::string* GraphDebugInfo::add_files() {
  std::string* _s = _internal_add_files();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.GraphDebugInfo.files)
  return _s;
}
inline const std::string& GraphDebugInfo::_internal_files(int index) const {
  return _impl_.files_.Get(index);
}
inline const std::string& GraphDebugInfo::files(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphDebugInfo.files)
  return _internal_files(index);
}
inline std::string* GraphDebugInfo::mutable_files(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphDebugInfo.files)
  return _impl_.files_.Mutable(index);
}
inline void GraphDebugInfo::set_files(int index, const std::string& value) {
  _impl_.files_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.files)
}
inline void GraphDebugInfo::set_files(int index, std::string&& value) {
  _impl_.files_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.GraphDebugInfo.files)
}
inline void GraphDebugInfo::set_files(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.files_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.GraphDebugInfo.files)
}
inline void GraphDebugInfo::set_files(int index, const char* value, size_t size) {
  _impl_.files_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.GraphDebugInfo.files)
}
inline std::string* GraphDebugInfo::_internal_add_files() {
  return _impl_.files_.Add();
}
inline void GraphDebugInfo::add_files(const std::string& value) {
  _impl_.files_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphDebugInfo.files)
}
inline void GraphDebugInfo::add_files(std::string&& value) {
  _impl_.files_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.GraphDebugInfo.files)
}
inline void GraphDebugInfo::add_files(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.files_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.GraphDebugInfo.files)
}
inline void GraphDebugInfo::add_files(const char* value, size_t size) {
  _impl_.files_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.GraphDebugInfo.files)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
GraphDebugInfo::files() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphDebugInfo.files)
  return _impl_.files_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
GraphDebugInfo::mutable_files() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphDebugInfo.files)
  return &_impl_.files_;
}

// map<fixed64, .tensorflow.GraphDebugInfo.FileLineCol> frames_by_id = 4;
inline int GraphDebugInfo::_internal_frames_by_id_size() const {
  return _impl_.frames_by_id_.size();
}
inline int GraphDebugInfo::frames_by_id_size() const {
  return _internal_frames_by_id_size();
}
inline void GraphDebugInfo::clear_frames_by_id() {
  _impl_.frames_by_id_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol >&
GraphDebugInfo::_internal_frames_by_id() const {
  return _impl_.frames_by_id_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol >&
GraphDebugInfo::frames_by_id() const {
  // @@protoc_insertion_point(field_map:tensorflow.GraphDebugInfo.frames_by_id)
  return _internal_frames_by_id();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol >*
GraphDebugInfo::_internal_mutable_frames_by_id() {
  return _impl_.frames_by_id_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_FileLineCol >*
GraphDebugInfo::mutable_frames_by_id() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.GraphDebugInfo.frames_by_id)
  return _internal_mutable_frames_by_id();
}

// map<fixed64, .tensorflow.GraphDebugInfo.StackTrace> traces_by_id = 6;
inline int GraphDebugInfo::_internal_traces_by_id_size() const {
  return _impl_.traces_by_id_.size();
}
inline int GraphDebugInfo::traces_by_id_size() const {
  return _internal_traces_by_id_size();
}
inline void GraphDebugInfo::clear_traces_by_id() {
  _impl_.traces_by_id_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_StackTrace >&
GraphDebugInfo::_internal_traces_by_id() const {
  return _impl_.traces_by_id_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_StackTrace >&
GraphDebugInfo::traces_by_id() const {
  // @@protoc_insertion_point(field_map:tensorflow.GraphDebugInfo.traces_by_id)
  return _internal_traces_by_id();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_StackTrace >*
GraphDebugInfo::_internal_mutable_traces_by_id() {
  return _impl_.traces_by_id_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< uint64_t, ::tensorflow::GraphDebugInfo_StackTrace >*
GraphDebugInfo::mutable_traces_by_id() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.GraphDebugInfo.traces_by_id)
  return _internal_mutable_traces_by_id();
}

// map<string, .tensorflow.GraphDebugInfo.StackTrace> traces = 2;
inline int GraphDebugInfo::_internal_traces_size() const {
  return _impl_.traces_.size();
}
inline int GraphDebugInfo::traces_size() const {
  return _internal_traces_size();
}
inline void GraphDebugInfo::clear_traces() {
  _impl_.traces_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::GraphDebugInfo_StackTrace >&
GraphDebugInfo::_internal_traces() const {
  return _impl_.traces_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::GraphDebugInfo_StackTrace >&
GraphDebugInfo::traces() const {
  // @@protoc_insertion_point(field_map:tensorflow.GraphDebugInfo.traces)
  return _internal_traces();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::GraphDebugInfo_StackTrace >*
GraphDebugInfo::_internal_mutable_traces() {
  return _impl_.traces_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::GraphDebugInfo_StackTrace >*
GraphDebugInfo::mutable_traces() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.GraphDebugInfo.traces)
  return _internal_mutable_traces();
}

// map<string, fixed64> name_to_trace_id = 5;
inline int GraphDebugInfo::_internal_name_to_trace_id_size() const {
  return _impl_.name_to_trace_id_.size();
}
inline int GraphDebugInfo::name_to_trace_id_size() const {
  return _internal_name_to_trace_id_size();
}
inline void GraphDebugInfo::clear_name_to_trace_id() {
  _impl_.name_to_trace_id_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >&
GraphDebugInfo::_internal_name_to_trace_id() const {
  return _impl_.name_to_trace_id_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >&
GraphDebugInfo::name_to_trace_id() const {
  // @@protoc_insertion_point(field_map:tensorflow.GraphDebugInfo.name_to_trace_id)
  return _internal_name_to_trace_id();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >*
GraphDebugInfo::_internal_mutable_name_to_trace_id() {
  return _impl_.name_to_trace_id_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, uint64_t >*
GraphDebugInfo::mutable_name_to_trace_id() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.GraphDebugInfo.name_to_trace_id)
  return _internal_mutable_name_to_trace_id();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5fdebug_5finfo_2eproto
