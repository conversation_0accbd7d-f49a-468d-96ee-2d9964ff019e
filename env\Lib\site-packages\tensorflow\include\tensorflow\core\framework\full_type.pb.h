// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/full_type.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto;
namespace tensorflow {
class FullTypeDef;
struct FullTypeDefDefaultTypeInternal;
extern FullTypeDefDefaultTypeInternal _FullTypeDef_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::FullTypeDef* Arena::CreateMaybeMessage<::tensorflow::FullTypeDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum FullTypeId : int {
  TFT_UNSET = 0,
  TFT_VAR = 1,
  TFT_ANY = 2,
  TFT_PRODUCT = 3,
  TFT_NAMED = 4,
  TFT_FOR_EACH = 20,
  TFT_CALLABLE = 100,
  TFT_TENSOR = 1000,
  TFT_ARRAY = 1001,
  TFT_OPTIONAL = 1002,
  TFT_LITERAL = 1003,
  TFT_ENCODED = 1004,
  TFT_SHAPE_TENSOR = 1005,
  TFT_BOOL = 200,
  TFT_UINT8 = 201,
  TFT_UINT16 = 202,
  TFT_UINT32 = 203,
  TFT_UINT64 = 204,
  TFT_INT8 = 205,
  TFT_INT16 = 206,
  TFT_INT32 = 207,
  TFT_INT64 = 208,
  TFT_HALF = 209,
  TFT_FLOAT = 210,
  TFT_DOUBLE = 211,
  TFT_BFLOAT16 = 215,
  TFT_COMPLEX64 = 212,
  TFT_COMPLEX128 = 213,
  TFT_STRING = 214,
  TFT_DATASET = 10102,
  TFT_RAGGED = 10103,
  TFT_ITERATOR = 10104,
  TFT_MUTEX_LOCK = 10202,
  TFT_LEGACY_VARIANT = 10203,
  FullTypeId_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  FullTypeId_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool FullTypeId_IsValid(int value);
constexpr FullTypeId FullTypeId_MIN = TFT_UNSET;
constexpr FullTypeId FullTypeId_MAX = TFT_LEGACY_VARIANT;
constexpr int FullTypeId_ARRAYSIZE = FullTypeId_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FullTypeId_descriptor();
template<typename T>
inline const std::string& FullTypeId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FullTypeId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FullTypeId_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FullTypeId_descriptor(), enum_t_value);
}
inline bool FullTypeId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, FullTypeId* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FullTypeId>(
    FullTypeId_descriptor(), name, value);
}
// ===================================================================

class FullTypeDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FullTypeDef) */ {
 public:
  inline FullTypeDef() : FullTypeDef(nullptr) {}
  ~FullTypeDef() override;
  explicit PROTOBUF_CONSTEXPR FullTypeDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FullTypeDef(const FullTypeDef& from);
  FullTypeDef(FullTypeDef&& from) noexcept
    : FullTypeDef() {
    *this = ::std::move(from);
  }

  inline FullTypeDef& operator=(const FullTypeDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline FullTypeDef& operator=(FullTypeDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FullTypeDef& default_instance() {
    return *internal_default_instance();
  }
  enum AttrCase {
    kS = 3,
    kI = 4,
    ATTR_NOT_SET = 0,
  };

  static inline const FullTypeDef* internal_default_instance() {
    return reinterpret_cast<const FullTypeDef*>(
               &_FullTypeDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(FullTypeDef& a, FullTypeDef& b) {
    a.Swap(&b);
  }
  inline void Swap(FullTypeDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FullTypeDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FullTypeDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FullTypeDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FullTypeDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FullTypeDef& from) {
    FullTypeDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FullTypeDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FullTypeDef";
  }
  protected:
  explicit FullTypeDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgsFieldNumber = 2,
    kTypeIdFieldNumber = 1,
    kSFieldNumber = 3,
    kIFieldNumber = 4,
  };
  // repeated .tensorflow.FullTypeDef args = 2;
  int args_size() const;
  private:
  int _internal_args_size() const;
  public:
  void clear_args();
  ::tensorflow::FullTypeDef* mutable_args(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef >*
      mutable_args();
  private:
  const ::tensorflow::FullTypeDef& _internal_args(int index) const;
  ::tensorflow::FullTypeDef* _internal_add_args();
  public:
  const ::tensorflow::FullTypeDef& args(int index) const;
  ::tensorflow::FullTypeDef* add_args();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef >&
      args() const;

  // .tensorflow.FullTypeId type_id = 1;
  void clear_type_id();
  ::tensorflow::FullTypeId type_id() const;
  void set_type_id(::tensorflow::FullTypeId value);
  private:
  ::tensorflow::FullTypeId _internal_type_id() const;
  void _internal_set_type_id(::tensorflow::FullTypeId value);
  public:

  // string s = 3;
  bool has_s() const;
  private:
  bool _internal_has_s() const;
  public:
  void clear_s();
  const std::string& s() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_s(ArgT0&& arg0, ArgT... args);
  std::string* mutable_s();
  PROTOBUF_NODISCARD std::string* release_s();
  void set_allocated_s(std::string* s);
  private:
  const std::string& _internal_s() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_s(const std::string& value);
  std::string* _internal_mutable_s();
  public:

  // int64 i = 4;
  bool has_i() const;
  private:
  bool _internal_has_i() const;
  public:
  void clear_i();
  int64_t i() const;
  void set_i(int64_t value);
  private:
  int64_t _internal_i() const;
  void _internal_set_i(int64_t value);
  public:

  void clear_attr();
  AttrCase attr_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.FullTypeDef)
 private:
  class _Internal;
  void set_has_s();
  void set_has_i();

  inline bool has_attr() const;
  inline void clear_has_attr();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef > args_;
    int type_id_;
    union AttrUnion {
      constexpr AttrUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr s_;
      int64_t i_;
    } attr_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// FullTypeDef

// .tensorflow.FullTypeId type_id = 1;
inline void FullTypeDef::clear_type_id() {
  _impl_.type_id_ = 0;
}
inline ::tensorflow::FullTypeId FullTypeDef::_internal_type_id() const {
  return static_cast< ::tensorflow::FullTypeId >(_impl_.type_id_);
}
inline ::tensorflow::FullTypeId FullTypeDef::type_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.FullTypeDef.type_id)
  return _internal_type_id();
}
inline void FullTypeDef::_internal_set_type_id(::tensorflow::FullTypeId value) {
  
  _impl_.type_id_ = value;
}
inline void FullTypeDef::set_type_id(::tensorflow::FullTypeId value) {
  _internal_set_type_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.FullTypeDef.type_id)
}

// repeated .tensorflow.FullTypeDef args = 2;
inline int FullTypeDef::_internal_args_size() const {
  return _impl_.args_.size();
}
inline int FullTypeDef::args_size() const {
  return _internal_args_size();
}
inline void FullTypeDef::clear_args() {
  _impl_.args_.Clear();
}
inline ::tensorflow::FullTypeDef* FullTypeDef::mutable_args(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.FullTypeDef.args)
  return _impl_.args_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef >*
FullTypeDef::mutable_args() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.FullTypeDef.args)
  return &_impl_.args_;
}
inline const ::tensorflow::FullTypeDef& FullTypeDef::_internal_args(int index) const {
  return _impl_.args_.Get(index);
}
inline const ::tensorflow::FullTypeDef& FullTypeDef::args(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.FullTypeDef.args)
  return _internal_args(index);
}
inline ::tensorflow::FullTypeDef* FullTypeDef::_internal_add_args() {
  return _impl_.args_.Add();
}
inline ::tensorflow::FullTypeDef* FullTypeDef::add_args() {
  ::tensorflow::FullTypeDef* _add = _internal_add_args();
  // @@protoc_insertion_point(field_add:tensorflow.FullTypeDef.args)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::FullTypeDef >&
FullTypeDef::args() const {
  // @@protoc_insertion_point(field_list:tensorflow.FullTypeDef.args)
  return _impl_.args_;
}

// string s = 3;
inline bool FullTypeDef::_internal_has_s() const {
  return attr_case() == kS;
}
inline bool FullTypeDef::has_s() const {
  return _internal_has_s();
}
inline void FullTypeDef::set_has_s() {
  _impl_._oneof_case_[0] = kS;
}
inline void FullTypeDef::clear_s() {
  if (_internal_has_s()) {
    _impl_.attr_.s_.Destroy();
    clear_has_attr();
  }
}
inline const std::string& FullTypeDef::s() const {
  // @@protoc_insertion_point(field_get:tensorflow.FullTypeDef.s)
  return _internal_s();
}
template <typename ArgT0, typename... ArgT>
inline void FullTypeDef::set_s(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_s()) {
    clear_attr();
    set_has_s();
    _impl_.attr_.s_.InitDefault();
  }
  _impl_.attr_.s_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.FullTypeDef.s)
}
inline std::string* FullTypeDef::mutable_s() {
  std::string* _s = _internal_mutable_s();
  // @@protoc_insertion_point(field_mutable:tensorflow.FullTypeDef.s)
  return _s;
}
inline const std::string& FullTypeDef::_internal_s() const {
  if (_internal_has_s()) {
    return _impl_.attr_.s_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void FullTypeDef::_internal_set_s(const std::string& value) {
  if (!_internal_has_s()) {
    clear_attr();
    set_has_s();
    _impl_.attr_.s_.InitDefault();
  }
  _impl_.attr_.s_.Set(value, GetArenaForAllocation());
}
inline std::string* FullTypeDef::_internal_mutable_s() {
  if (!_internal_has_s()) {
    clear_attr();
    set_has_s();
    _impl_.attr_.s_.InitDefault();
  }
  return _impl_.attr_.s_.Mutable(      GetArenaForAllocation());
}
inline std::string* FullTypeDef::release_s() {
  // @@protoc_insertion_point(field_release:tensorflow.FullTypeDef.s)
  if (_internal_has_s()) {
    clear_has_attr();
    return _impl_.attr_.s_.Release();
  } else {
    return nullptr;
  }
}
inline void FullTypeDef::set_allocated_s(std::string* s) {
  if (has_attr()) {
    clear_attr();
  }
  if (s != nullptr) {
    set_has_s();
    _impl_.attr_.s_.InitAllocated(s, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FullTypeDef.s)
}

// int64 i = 4;
inline bool FullTypeDef::_internal_has_i() const {
  return attr_case() == kI;
}
inline bool FullTypeDef::has_i() const {
  return _internal_has_i();
}
inline void FullTypeDef::set_has_i() {
  _impl_._oneof_case_[0] = kI;
}
inline void FullTypeDef::clear_i() {
  if (_internal_has_i()) {
    _impl_.attr_.i_ = int64_t{0};
    clear_has_attr();
  }
}
inline int64_t FullTypeDef::_internal_i() const {
  if (_internal_has_i()) {
    return _impl_.attr_.i_;
  }
  return int64_t{0};
}
inline void FullTypeDef::_internal_set_i(int64_t value) {
  if (!_internal_has_i()) {
    clear_attr();
    set_has_i();
  }
  _impl_.attr_.i_ = value;
}
inline int64_t FullTypeDef::i() const {
  // @@protoc_insertion_point(field_get:tensorflow.FullTypeDef.i)
  return _internal_i();
}
inline void FullTypeDef::set_i(int64_t value) {
  _internal_set_i(value);
  // @@protoc_insertion_point(field_set:tensorflow.FullTypeDef.i)
}

inline bool FullTypeDef::has_attr() const {
  return attr_case() != ATTR_NOT_SET;
}
inline void FullTypeDef::clear_has_attr() {
  _impl_._oneof_case_[0] = ATTR_NOT_SET;
}
inline FullTypeDef::AttrCase FullTypeDef::attr_case() const {
  return FullTypeDef::AttrCase(_impl_._oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::FullTypeId> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::FullTypeId>() {
  return ::tensorflow::FullTypeId_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ffull_5ftype_2eproto
