@echo off
echo 正在编译denoise_training...

REM 尝试使用gcc编译
gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnn_data.c -o denoise_training.exe -lm

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！生成了denoise_training.exe
) else (
    echo 编译失败！请确保已安装gcc编译器。
    echo.
    echo 安装建议：
    echo 1. 安装MinGW-w64: https://www.mingw-w64.org/downloads/
    echo 2. 或者安装MSYS2: https://www.msys2.org/
    echo 3. 或者使用Visual Studio Build Tools
    echo.
    echo 安装后请将编译器路径添加到系统PATH环境变量中
)

pause
