+{
   locale_version => 1.31,
   entry => <<'ENTRY', # for DUCET v13.0.0
0065      ; [.1FA3.0020.0002][.FFF1.0000.0000] # LATIN SMALL LETTER E
0045      ; [.1FA3.0020.0008][.FFF1.0000.0000] # LATIN CAPITAL LETTER E
0069      ; [.1FA3.0020.0002][.FFF2.0000.0000] # LATIN SMALL LETTER I
0049      ; [.1FA3.0020.0008][.FFF2.0000.0000] # LATIN CAPITAL LETTER I
006F      ; [.1FA3.0020.0002][.FFF3.0000.0000] # LATIN SMALL LETTER O
004F      ; [.1FA3.0020.0008][.FFF3.0000.0000] # LATIN CAPITAL LETTER O
0075      ; [.1FA3.0020.0002][.FFF4.0000.0000] # LATIN SMALL LETTER U
0055      ; [.1FA3.0020.0008][.FFF4.0000.0000] # LATIN CAPITAL LETTER U
02BB      ; [.225A.0020.0002] # MODIFIER LETTER TURNED COMMA
0113      ; [.1FA3.0020.0002][.FFF1.0000.0000][.0000.0032.0002] # LATIN SMALL LETTER E WITH MACRON
0112      ; [.1FA3.0020.0008][.FFF1.0000.0000][.0000.0032.0002] # LATIN CAPITAL LETTER E WITH MACRON
012B      ; [.1FA3.0020.0002][.FFF2.0000.0000][.0000.0032.0002] # LATIN SMALL LETTER I WITH MACRON
012A      ; [.1FA3.0020.0008][.FFF2.0000.0000][.0000.0032.0002] # LATIN CAPITAL LETTER I WITH MACRON
014D      ; [.1FA3.0020.0002][.FFF3.0000.0000][.0000.0032.0002] # LATIN SMALL LETTER O WITH MACRON
014C      ; [.1FA3.0020.0008][.FFF3.0000.0000][.0000.0032.0002] # LATIN CAPITAL LETTER O WITH MACRON
016B      ; [.1FA3.0020.0002][.FFF4.0000.0000][.0000.0032.0002] # LATIN SMALL LETTER U WITH MACRON
016A      ; [.1FA3.0020.0008][.FFF4.0000.0000][.0000.0032.0002] # LATIN CAPITAL LETTER U WITH MACRON
ENTRY
};
