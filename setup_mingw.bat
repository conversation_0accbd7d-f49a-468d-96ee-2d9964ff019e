@echo off
REM 设置MinGW环境变量并编译denoise_training

echo ========================================
echo 设置MinGW环境并编译RNNoise
echo ========================================

REM 设置MinGW路径
set MINGW_PATH=%~dp0env\mingw64\bin
set PATH=%MINGW_PATH%;%PATH%

echo MinGW路径: %MINGW_PATH%
echo.

REM 验证gcc是否可用
echo 检查GCC编译器...
"%MINGW_PATH%\gcc.exe" --version
if %errorlevel% neq 0 (
    echo 错误: 无法找到GCC编译器
    pause
    exit /b 1
)

echo.
echo ========================================
echo 开始编译denoise_training
echo ========================================

REM 切换到src目录
cd src

echo 编译命令: gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnnoise_data.c -o denoise_training.exe -lm

"%MINGW_PATH%\gcc.exe" -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnnoise_data.c -o denoise_training.exe -lm

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✓ 编译成功！
    echo ========================================
    echo.
    echo 生成的可执行文件: src\denoise_training.exe
    echo 文件大小:
    dir denoise_training.exe | findstr denoise_training.exe
    echo.
    echo 使用方法:
    echo   denoise_training.exe input.pcm output.pcm
    echo.
    echo 测试编译结果:
    echo   denoise_training.exe --help
    .\denoise_training.exe --help 2>nul || echo "程序已生成，可以使用PCM文件进行测试"
) else (
    echo.
    echo ========================================
    echo ✗ 编译失败！
    echo ========================================
    echo.
    echo 请检查上面的错误信息
)

cd ..
echo.
echo 按任意键退出...
pause >nul
