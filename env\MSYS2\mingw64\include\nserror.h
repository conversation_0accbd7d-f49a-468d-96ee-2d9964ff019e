/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _NSERROR_H
#define _NSERROR_H

#if defined (RC_INVOKED) || defined (__WIDL__)
#define _HRESULT_TYPEDEF_(_sc) _sc
#else
#define _HRESULT_TYPEDEF_(_sc) ((HRESULT)_sc)
#endif

#define FACILITY_NS 0xD
#define FACILITY_NS_WIN32 0x7

#define STATUS_SEVERITY_SUCCESS 0x0
#define STATUS_SEVERITY_INFORMATIONAL 0x1
#define STATUS_SEVERITY_WARNING 0x2
#define STATUS_SEVERITY_ERROR 0x3

#define NS_S_CALLPENDING                 _HRESULT_TYPEDEF_(0x000D0000)
#define NS_S_CALLABORTED                 _HRESULT_TYPEDEF_(0x000D0001)
#define NS_S_STREAM_TRUNCATED            _HRESULT_TYPEDEF_(0x000D0002)
#define NS_W_SERVER_BANDWIDTH_LIMIT      _HRESULT_TYPEDEF_(0x800D0003)
#define NS_W_FILE_BANDWIDTH_LIMIT        _HRESULT_TYPEDEF_(0x800D0004)
#define NS_E_NOCONNECTION                _HRESULT_TYPEDEF_(0xC00D0005)
#define NS_E_CANNOTCONNECT               _HRESULT_TYPEDEF_(0xC00D0006)
#define NS_E_CANNOTDESTROYTITLE          _HRESULT_TYPEDEF_(0xC00D0007)
#define NS_E_CANNOTRENAMETITLE           _HRESULT_TYPEDEF_(0xC00D0008)
#define NS_E_CANNOTOFFLINEDISK           _HRESULT_TYPEDEF_(0xC00D0009)
#define NS_E_CANNOTONLINEDISK            _HRESULT_TYPEDEF_(0xC00D000A)
#define NS_E_NOREGISTEREDWALKER          _HRESULT_TYPEDEF_(0xC00D000B)
#define NS_E_NOFUNNEL                    _HRESULT_TYPEDEF_(0xC00D000C)
#define NS_E_NO_LOCALPLAY                _HRESULT_TYPEDEF_(0xC00D000D)
#define NS_E_NETWORK_BUSY                _HRESULT_TYPEDEF_(0xC00D000E)
#define NS_E_TOO_MANY_SESS               _HRESULT_TYPEDEF_(0xC00D000F)
#define NS_E_ALREADY_CONNECTED           _HRESULT_TYPEDEF_(0xC00D0010)
#define NS_E_INVALID_INDEX               _HRESULT_TYPEDEF_(0xC00D0011)
#define NS_E_PROTOCOL_MISMATCH           _HRESULT_TYPEDEF_(0xC00D0012)
#define NS_E_TIMEOUT                     _HRESULT_TYPEDEF_(0xC00D0013)
#define NS_E_NET_WRITE                   _HRESULT_TYPEDEF_(0xC00D0014)
#define NS_E_NET_READ                    _HRESULT_TYPEDEF_(0xC00D0015)
#define NS_E_DISK_WRITE                  _HRESULT_TYPEDEF_(0xC00D0016)
#define NS_E_DISK_READ                   _HRESULT_TYPEDEF_(0xC00D0017)
#define NS_E_FILE_WRITE                  _HRESULT_TYPEDEF_(0xC00D0018)
#define NS_E_FILE_READ                   _HRESULT_TYPEDEF_(0xC00D0019)
#define NS_E_FILE_NOT_FOUND              _HRESULT_TYPEDEF_(0xC00D001A)
#define NS_E_FILE_EXISTS                 _HRESULT_TYPEDEF_(0xC00D001B)
#define NS_E_INVALID_NAME                _HRESULT_TYPEDEF_(0xC00D001C)
#define NS_E_FILE_OPEN_FAILED            _HRESULT_TYPEDEF_(0xC00D001D)
#define NS_E_FILE_ALLOCATION_FAILED      _HRESULT_TYPEDEF_(0xC00D001E)
#define NS_E_FILE_INIT_FAILED            _HRESULT_TYPEDEF_(0xC00D001F)
#define NS_E_FILE_PLAY_FAILED            _HRESULT_TYPEDEF_(0xC00D0020)
#define NS_E_SET_DISK_UID_FAILED         _HRESULT_TYPEDEF_(0xC00D0021)
#define NS_E_INDUCED                     _HRESULT_TYPEDEF_(0xC00D0022)
#define NS_E_CCLINK_DOWN                 _HRESULT_TYPEDEF_(0xC00D0023)
#define NS_E_INTERNAL                    _HRESULT_TYPEDEF_(0xC00D0024)
#define NS_E_BUSY                        _HRESULT_TYPEDEF_(0xC00D0025)
#define NS_E_UNRECOGNIZED_STREAM_TYPE    _HRESULT_TYPEDEF_(0xC00D0026)
#define NS_E_NETWORK_SERVICE_FAILURE     _HRESULT_TYPEDEF_(0xC00D0027)
#define NS_E_NETWORK_RESOURCE_FAILURE    _HRESULT_TYPEDEF_(0xC00D0028)
#define NS_E_CONNECTION_FAILURE          _HRESULT_TYPEDEF_(0xC00D0029)
#define NS_E_SHUTDOWN                    _HRESULT_TYPEDEF_(0xC00D002A)
#define NS_E_INVALID_REQUEST             _HRESULT_TYPEDEF_(0xC00D002B)
#define NS_E_INSUFFICIENT_BANDWIDTH      _HRESULT_TYPEDEF_(0xC00D002C)
#define NS_E_NOT_REBUILDING              _HRESULT_TYPEDEF_(0xC00D002D)
#define NS_E_LATE_OPERATION              _HRESULT_TYPEDEF_(0xC00D002E)
#define NS_E_INVALID_DATA                _HRESULT_TYPEDEF_(0xC00D002F)
#define NS_E_FILE_BANDWIDTH_LIMIT        _HRESULT_TYPEDEF_(0xC00D0030)
#define NS_E_OPEN_FILE_LIMIT             _HRESULT_TYPEDEF_(0xC00D0031)
#define NS_E_BAD_CONTROL_DATA            _HRESULT_TYPEDEF_(0xC00D0032)
#define NS_E_NO_STREAM                   _HRESULT_TYPEDEF_(0xC00D0033)
#define NS_E_STREAM_END                  _HRESULT_TYPEDEF_(0xC00D0034)
#define NS_E_SERVER_NOT_FOUND            _HRESULT_TYPEDEF_(0xC00D0035)
#define NS_E_DUPLICATE_NAME              _HRESULT_TYPEDEF_(0xC00D0036)
#define NS_E_DUPLICATE_ADDRESS           _HRESULT_TYPEDEF_(0xC00D0037)
#define NS_E_BAD_MULTICAST_ADDRESS       _HRESULT_TYPEDEF_(0xC00D0038)
#define NS_E_BAD_ADAPTER_ADDRESS         _HRESULT_TYPEDEF_(0xC00D0039)
#define NS_E_BAD_DELIVERY_MODE           _HRESULT_TYPEDEF_(0xC00D003A)
#define NS_E_INVALID_CHANNEL             _HRESULT_TYPEDEF_(0xC00D003B)
#define NS_E_INVALID_STREAM              _HRESULT_TYPEDEF_(0xC00D003C)
#define NS_E_INVALID_ARCHIVE             _HRESULT_TYPEDEF_(0xC00D003D)
#define NS_E_NOTITLES                    _HRESULT_TYPEDEF_(0xC00D003E)
#define NS_E_INVALID_CLIENT              _HRESULT_TYPEDEF_(0xC00D003F)
#define NS_E_INVALID_BLACKHOLE_ADDRESS   _HRESULT_TYPEDEF_(0xC00D0040)
#define NS_E_INCOMPATIBLE_FORMAT         _HRESULT_TYPEDEF_(0xC00D0041)
#define NS_E_INVALID_KEY                 _HRESULT_TYPEDEF_(0xC00D0042)
#define NS_E_INVALID_PORT                _HRESULT_TYPEDEF_(0xC00D0043)
#define NS_E_INVALID_TTL                 _HRESULT_TYPEDEF_(0xC00D0044)
#define NS_E_STRIDE_REFUSED              _HRESULT_TYPEDEF_(0xC00D0045)
#define NS_E_MMSAUTOSERVER_CANTFINDWALKER _HRESULT_TYPEDEF_(0xC00D0046)
#define NS_E_MAX_BITRATE                 _HRESULT_TYPEDEF_(0xC00D0047)
#define NS_E_LOGFILEPERIOD               _HRESULT_TYPEDEF_(0xC00D0048)
#define NS_E_MAX_CLIENTS                 _HRESULT_TYPEDEF_(0xC00D0049)
#define NS_E_LOG_FILE_SIZE               _HRESULT_TYPEDEF_(0xC00D004A)
#define NS_E_MAX_FILERATE                _HRESULT_TYPEDEF_(0xC00D004B)
#define NS_E_WALKER_UNKNOWN              _HRESULT_TYPEDEF_(0xC00D004C)
#define NS_E_WALKER_SERVER               _HRESULT_TYPEDEF_(0xC00D004D)
#define NS_E_WALKER_USAGE                _HRESULT_TYPEDEF_(0xC00D004E)
#define NS_I_TIGER_START                 _HRESULT_TYPEDEF_(0x400D004F)
#define NS_E_TIGER_FAIL                  _HRESULT_TYPEDEF_(0xC00D0050)
#define NS_I_CUB_START                   _HRESULT_TYPEDEF_(0x400D0051)
#define NS_I_CUB_RUNNING                 _HRESULT_TYPEDEF_(0x400D0052)
#define NS_E_CUB_FAIL                    _HRESULT_TYPEDEF_(0xC00D0053)
#define NS_I_DISK_START                  _HRESULT_TYPEDEF_(0x400D0054)
#define NS_E_DISK_FAIL                   _HRESULT_TYPEDEF_(0xC00D0055)
#define NS_I_DISK_REBUILD_STARTED        _HRESULT_TYPEDEF_(0x400D0056)
#define NS_I_DISK_REBUILD_FINISHED       _HRESULT_TYPEDEF_(0x400D0057)
#define NS_I_DISK_REBUILD_ABORTED        _HRESULT_TYPEDEF_(0x400D0058)
#define NS_I_LIMIT_FUNNELS               _HRESULT_TYPEDEF_(0x400D0059)
#define NS_I_START_DISK                  _HRESULT_TYPEDEF_(0x400D005A)
#define NS_I_STOP_DISK                   _HRESULT_TYPEDEF_(0x400D005B)
#define NS_I_STOP_CUB                    _HRESULT_TYPEDEF_(0x400D005C)
#define NS_I_KILL_USERSESSION            _HRESULT_TYPEDEF_(0x400D005D)
#define NS_I_KILL_CONNECTION             _HRESULT_TYPEDEF_(0x400D005E)
#define NS_I_REBUILD_DISK                _HRESULT_TYPEDEF_(0x400D005F)
#define NS_W_UNKNOWN_EVENT               _HRESULT_TYPEDEF_(0x800D0060)
#define NS_E_MAX_FUNNELS_ALERT           _HRESULT_TYPEDEF_(0xC00D0060)
#define NS_E_ALLOCATE_FILE_FAIL          _HRESULT_TYPEDEF_(0xC00D0061)
#define NS_E_PAGING_ERROR                _HRESULT_TYPEDEF_(0xC00D0062)
#define NS_E_BAD_BLOCK0_VERSION          _HRESULT_TYPEDEF_(0xC00D0063)
#define NS_E_BAD_DISK_UID                _HRESULT_TYPEDEF_(0xC00D0064)
#define NS_E_BAD_FSMAJOR_VERSION         _HRESULT_TYPEDEF_(0xC00D0065)
#define NS_E_BAD_STAMPNUMBER             _HRESULT_TYPEDEF_(0xC00D0066)
#define NS_E_PARTIALLY_REBUILT_DISK      _HRESULT_TYPEDEF_(0xC00D0067)
#define NS_E_ENACTPLAN_GIVEUP            _HRESULT_TYPEDEF_(0xC00D0068)
#define MCMADM_I_NO_EVENTS               _HRESULT_TYPEDEF_(0x400D0069)
#define MCMADM_E_REGKEY_NOT_FOUND        _HRESULT_TYPEDEF_(0xC00D006A)
#define NS_E_NO_FORMATS                  _HRESULT_TYPEDEF_(0xC00D006B)
#define NS_E_NO_REFERENCES               _HRESULT_TYPEDEF_(0xC00D006C)
#define NS_E_WAVE_OPEN                   _HRESULT_TYPEDEF_(0xC00D006D)
#define NS_I_LOGGING_FAILED              _HRESULT_TYPEDEF_(0x400D006E)
#define NS_E_CANNOTCONNECTEVENTS         _HRESULT_TYPEDEF_(0xC00D006F)
#define NS_I_LIMIT_BANDWIDTH             _HRESULT_TYPEDEF_(0x400D0070)
#define NS_E_NO_DEVICE                   _HRESULT_TYPEDEF_(0xC00D0071)
#define NS_E_NO_SPECIFIED_DEVICE         _HRESULT_TYPEDEF_(0xC00D0072)
#define NS_E_NOTHING_TO_DO               _HRESULT_TYPEDEF_(0xC00D07F1)
#define NS_E_NO_MULTICAST                _HRESULT_TYPEDEF_(0xC00D07F2)
#define NS_E_MONITOR_GIVEUP              _HRESULT_TYPEDEF_(0xC00D00C8)
#define NS_E_REMIRRORED_DISK             _HRESULT_TYPEDEF_(0xC00D00C9)
#define NS_E_INSUFFICIENT_DATA           _HRESULT_TYPEDEF_(0xC00D00CA)
#define NS_E_ASSERT                      _HRESULT_TYPEDEF_(0xC00D00CB)
#define NS_E_BAD_ADAPTER_NAME            _HRESULT_TYPEDEF_(0xC00D00CC)
#define NS_E_NOT_LICENSED                _HRESULT_TYPEDEF_(0xC00D00CD)
#define NS_E_NO_SERVER_CONTACT           _HRESULT_TYPEDEF_(0xC00D00CE)
#define NS_E_TOO_MANY_TITLES             _HRESULT_TYPEDEF_(0xC00D00CF)
#define NS_E_TITLE_SIZE_EXCEEDED         _HRESULT_TYPEDEF_(0xC00D00D0)
#define NS_E_UDP_DISABLED                _HRESULT_TYPEDEF_(0xC00D00D1)
#define NS_E_TCP_DISABLED                _HRESULT_TYPEDEF_(0xC00D00D2)
#define NS_E_HTTP_DISABLED               _HRESULT_TYPEDEF_(0xC00D00D3)
#define NS_E_LICENSE_EXPIRED             _HRESULT_TYPEDEF_(0xC00D00D4)
#define NS_E_TITLE_BITRATE               _HRESULT_TYPEDEF_(0xC00D00D5)
#define NS_E_EMPTY_PROGRAM_NAME          _HRESULT_TYPEDEF_(0xC00D00D6)
#define NS_E_MISSING_CHANNEL             _HRESULT_TYPEDEF_(0xC00D00D7)
#define NS_E_NO_CHANNELS                 _HRESULT_TYPEDEF_(0xC00D00D8)
#define NS_E_INVALID_INDEX2              _HRESULT_TYPEDEF_(0xC00D00D9)
#define NS_E_CUB_FAIL_LINK               _HRESULT_TYPEDEF_(0xC00D0190)
#define NS_I_CUB_UNFAIL_LINK             _HRESULT_TYPEDEF_(0x400D0191)
#define NS_E_BAD_CUB_UID                 _HRESULT_TYPEDEF_(0xC00D0192)
#define NS_I_RESTRIPE_START              _HRESULT_TYPEDEF_(0x400D0193)
#define NS_I_RESTRIPE_DONE               _HRESULT_TYPEDEF_(0x400D0194)
#define NS_E_GLITCH_MODE                 _HRESULT_TYPEDEF_(0xC00D0195)
#define NS_I_RESTRIPE_DISK_OUT           _HRESULT_TYPEDEF_(0x400D0196)
#define NS_I_RESTRIPE_CUB_OUT            _HRESULT_TYPEDEF_(0x400D0197)
#define NS_I_DISK_STOP                   _HRESULT_TYPEDEF_(0x400D0198)
#define NS_I_CATATONIC_FAILURE           _HRESULT_TYPEDEF_(0x800D0199)
#define NS_I_CATATONIC_AUTO_UNFAIL       _HRESULT_TYPEDEF_(0x800D019A)
#define NS_E_NO_MEDIA_PROTOCOL           _HRESULT_TYPEDEF_(0xC00D019B)
#define NS_E_INVALID_INPUT_FORMAT        _HRESULT_TYPEDEF_(0xC00D0BB8)
#define NS_E_MSAUDIO_NOT_INSTALLED       _HRESULT_TYPEDEF_(0xC00D0BB9)
#define NS_E_UNEXPECTED_MSAUDIO_ERROR    _HRESULT_TYPEDEF_(0xC00D0BBA)
#define NS_E_INVALID_OUTPUT_FORMAT       _HRESULT_TYPEDEF_(0xC00D0BBB)
#define NS_E_NOT_CONFIGURED              _HRESULT_TYPEDEF_(0xC00D0BBC)
#define NS_E_PROTECTED_CONTENT           _HRESULT_TYPEDEF_(0xC00D0BBD)
#define NS_E_LICENSE_REQUIRED            _HRESULT_TYPEDEF_(0xC00D0BBE)
#define NS_E_TAMPERED_CONTENT            _HRESULT_TYPEDEF_(0xC00D0BBF)
#define NS_E_LICENSE_OUTOFDATE           _HRESULT_TYPEDEF_(0xC00D0BC0)
#define NS_E_LICENSE_INCORRECT_RIGHTS    _HRESULT_TYPEDEF_(0xC00D0BC1)
#define NS_E_AUDIO_CODEC_NOT_INSTALLED   _HRESULT_TYPEDEF_(0xC00D0BC2)
#define NS_E_AUDIO_CODEC_ERROR           _HRESULT_TYPEDEF_(0xC00D0BC3)
#define NS_E_VIDEO_CODEC_NOT_INSTALLED   _HRESULT_TYPEDEF_(0xC00D0BC4)
#define NS_E_VIDEO_CODEC_ERROR           _HRESULT_TYPEDEF_(0xC00D0BC5)
#define NS_E_INVALIDPROFILE              _HRESULT_TYPEDEF_(0xC00D0BC6)
#define NS_E_INCOMPATIBLE_VERSION        _HRESULT_TYPEDEF_(0xC00D0BC7)
#define NS_S_REBUFFERING                 _HRESULT_TYPEDEF_(0x000D0BC8)
#define NS_S_DEGRADING_QUALITY           _HRESULT_TYPEDEF_(0x000D0BC9)
#define NS_E_OFFLINE_MODE                _HRESULT_TYPEDEF_(0xC00D0BCA)
#define NS_E_NOT_CONNECTED               _HRESULT_TYPEDEF_(0xC00D0BCB)
#define NS_E_TOO_MUCH_DATA               _HRESULT_TYPEDEF_(0xC00D0BCC)
#define NS_E_UNSUPPORTED_PROPERTY        _HRESULT_TYPEDEF_(0xC00D0BCD)
#define NS_E_8BIT_WAVE_UNSUPPORTED       _HRESULT_TYPEDEF_(0xC00D0BCE)
#define NS_E_NO_MORE_SAMPLES             _HRESULT_TYPEDEF_(0xC00D0BCF)
#define NS_E_INVALID_SAMPLING_RATE       _HRESULT_TYPEDEF_(0xC00D0BD0)
#define NS_E_MAX_PACKET_SIZE_TOO_SMALL   _HRESULT_TYPEDEF_(0xC00D0BD1)
#define NS_E_LATE_PACKET                 _HRESULT_TYPEDEF_(0xC00D0BD2)
#define NS_E_DUPLICATE_PACKET            _HRESULT_TYPEDEF_(0xC00D0BD3)
#define NS_E_SDK_BUFFERTOOSMALL          _HRESULT_TYPEDEF_(0xC00D0BD4)
#define NS_E_INVALID_NUM_PASSES          _HRESULT_TYPEDEF_(0xC00D0BD5)
#define NS_E_ATTRIBUTE_READ_ONLY         _HRESULT_TYPEDEF_(0xC00D0BD6)
#define NS_E_ATTRIBUTE_NOT_ALLOWED       _HRESULT_TYPEDEF_(0xC00D0BD7)
#define NS_E_INVALID_EDL                 _HRESULT_TYPEDEF_(0xC00D0BD8)
#define NS_E_DATA_UNIT_EXTENSION_TOO_LARGE _HRESULT_TYPEDEF_(0xC00D0BD9)
#define NS_E_CODEC_DMO_ERROR             _HRESULT_TYPEDEF_(0xC00D0BDA)
#define NS_S_TRANSCRYPTOR_EOF            _HRESULT_TYPEDEF_(0x000D0BDB)
#define NS_E_FEATURE_DISABLED_BY_GROUP_POLICY _HRESULT_TYPEDEF_(0xC00D0BDC)
#define NS_E_FEATURE_DISABLED_IN_SKU     _HRESULT_TYPEDEF_(0xC00D0BDD)
#define NS_E_WMDRM_DEPRECATED            _HRESULT_TYPEDEF_(0xC00D0BDE)
#define NS_E_NO_CD                       _HRESULT_TYPEDEF_(0xC00D0FA0)
#define NS_E_CANT_READ_DIGITAL           _HRESULT_TYPEDEF_(0xC00D0FA1)
#define NS_E_DEVICE_DISCONNECTED         _HRESULT_TYPEDEF_(0xC00D0FA2)
#define NS_E_DEVICE_NOT_SUPPORT_FORMAT   _HRESULT_TYPEDEF_(0xC00D0FA3)
#define NS_E_SLOW_READ_DIGITAL           _HRESULT_TYPEDEF_(0xC00D0FA4)
#define NS_E_MIXER_INVALID_LINE          _HRESULT_TYPEDEF_(0xC00D0FA5)
#define NS_E_MIXER_INVALID_CONTROL       _HRESULT_TYPEDEF_(0xC00D0FA6)
#define NS_E_MIXER_INVALID_VALUE         _HRESULT_TYPEDEF_(0xC00D0FA7)
#define NS_E_MIXER_UNKNOWN_MMRESULT      _HRESULT_TYPEDEF_(0xC00D0FA8)
#define NS_E_USER_STOP                   _HRESULT_TYPEDEF_(0xC00D0FA9)
#define NS_E_MP3_FORMAT_NOT_FOUND        _HRESULT_TYPEDEF_(0xC00D0FAA)
#define NS_E_CD_READ_ERROR_NO_CORRECTION _HRESULT_TYPEDEF_(0xC00D0FAB)
#define NS_E_CD_READ_ERROR               _HRESULT_TYPEDEF_(0xC00D0FAC)
#define NS_E_CD_SLOW_COPY                _HRESULT_TYPEDEF_(0xC00D0FAD)
#define NS_E_CD_COPYTO_CD                _HRESULT_TYPEDEF_(0xC00D0FAE)
#define NS_E_MIXER_NODRIVER              _HRESULT_TYPEDEF_(0xC00D0FAF)
#define NS_E_REDBOOK_ENABLED_WHILE_COPYING _HRESULT_TYPEDEF_(0xC00D0FB0)
#define NS_E_CD_REFRESH                  _HRESULT_TYPEDEF_(0xC00D0FB1)
#define NS_E_CD_DRIVER_PROBLEM           _HRESULT_TYPEDEF_(0xC00D0FB2)
#define NS_E_WONT_DO_DIGITAL             _HRESULT_TYPEDEF_(0xC00D0FB3)
#define NS_E_WMPXML_NOERROR              _HRESULT_TYPEDEF_(0xC00D0FB4)
#define NS_E_WMPXML_ENDOFDATA            _HRESULT_TYPEDEF_(0xC00D0FB5)
#define NS_E_WMPXML_PARSEERROR           _HRESULT_TYPEDEF_(0xC00D0FB6)
#define NS_E_WMPXML_ATTRIBUTENOTFOUND    _HRESULT_TYPEDEF_(0xC00D0FB7)
#define NS_E_WMPXML_PINOTFOUND           _HRESULT_TYPEDEF_(0xC00D0FB8)
#define NS_E_WMPXML_EMPTYDOC             _HRESULT_TYPEDEF_(0xC00D0FB9)
#define NS_E_WMP_PATH_ALREADY_IN_LIBRARY _HRESULT_TYPEDEF_(0xC00D0FBA)
#define NS_E_WMP_FILESCANALREADYSTARTED  _HRESULT_TYPEDEF_(0xC00D0FBE)
#define NS_E_WMP_HME_INVALIDOBJECTID     _HRESULT_TYPEDEF_(0xC00D0FBF)
#define NS_E_WMP_MF_CODE_EXPIRED         _HRESULT_TYPEDEF_(0xC00D0FC0)
#define NS_E_WMP_HME_NOTSEARCHABLEFORITEMS _HRESULT_TYPEDEF_(0xC00D0FC1)
#define NS_E_WMP_HME_STALEREQUEST        _HRESULT_TYPEDEF_(0xC00D0FC2)
#define NS_E_WMP_ADDTOLIBRARY_FAILED     _HRESULT_TYPEDEF_(0xC00D0FC7)
#define NS_E_WMP_WINDOWSAPIFAILURE       _HRESULT_TYPEDEF_(0xC00D0FC8)
#define NS_E_WMP_RECORDING_NOT_ALLOWED   _HRESULT_TYPEDEF_(0xC00D0FC9)
#define NS_E_DEVICE_NOT_READY            _HRESULT_TYPEDEF_(0xC00D0FCA)
#define NS_E_DAMAGED_FILE                _HRESULT_TYPEDEF_(0xC00D0FCB)
#define NS_E_MPDB_GENERIC                _HRESULT_TYPEDEF_(0xC00D0FCC)
#define NS_E_FILE_FAILED_CHECKS          _HRESULT_TYPEDEF_(0xC00D0FCD)
#define NS_E_MEDIA_LIBRARY_FAILED        _HRESULT_TYPEDEF_(0xC00D0FCE)
#define NS_E_SHARING_VIOLATION           _HRESULT_TYPEDEF_(0xC00D0FCF)
#define NS_E_NO_ERROR_STRING_FOUND       _HRESULT_TYPEDEF_(0xC00D0FD0)
#define NS_E_WMPOCX_NO_REMOTE_CORE       _HRESULT_TYPEDEF_(0xC00D0FD1)
#define NS_E_WMPOCX_NO_ACTIVE_CORE       _HRESULT_TYPEDEF_(0xC00D0FD2)
#define NS_E_WMPOCX_NOT_RUNNING_REMOTELY _HRESULT_TYPEDEF_(0xC00D0FD3)
#define NS_E_WMPOCX_NO_REMOTE_WINDOW     _HRESULT_TYPEDEF_(0xC00D0FD4)
#define NS_E_WMPOCX_ERRORMANAGERNOTAVAILABLE _HRESULT_TYPEDEF_(0xC00D0FD5)
#define NS_E_PLUGIN_NOTSHUTDOWN          _HRESULT_TYPEDEF_(0xC00D0FD6)
#define NS_E_WMP_CANNOT_FIND_FOLDER      _HRESULT_TYPEDEF_(0xC00D0FD7)
#define NS_E_WMP_STREAMING_RECORDING_NOT_ALLOWED _HRESULT_TYPEDEF_(0xC00D0FD8)
#define NS_E_WMP_PLUGINDLL_NOTFOUND      _HRESULT_TYPEDEF_(0xC00D0FD9)
#define NS_E_NEED_TO_ASK_USER            _HRESULT_TYPEDEF_(0xC00D0FDA)
#define NS_E_WMPOCX_PLAYER_NOT_DOCKED    _HRESULT_TYPEDEF_(0xC00D0FDB)
#define NS_E_WMP_EXTERNAL_NOTREADY       _HRESULT_TYPEDEF_(0xC00D0FDC)
#define NS_E_WMP_MLS_STALE_DATA          _HRESULT_TYPEDEF_(0xC00D0FDD)
#define NS_E_WMP_UI_SUBCONTROLSNOTSUPPORTED _HRESULT_TYPEDEF_(0xC00D0FDE)
#define NS_E_WMP_UI_VERSIONMISMATCH      _HRESULT_TYPEDEF_(0xC00D0FDF)
#define NS_E_WMP_UI_NOTATHEMEFILE        _HRESULT_TYPEDEF_(0xC00D0FE0)
#define NS_E_WMP_UI_SUBELEMENTNOTFOUND   _HRESULT_TYPEDEF_(0xC00D0FE1)
#define NS_E_WMP_UI_VERSIONPARSE         _HRESULT_TYPEDEF_(0xC00D0FE2)
#define NS_E_WMP_UI_VIEWIDNOTFOUND       _HRESULT_TYPEDEF_(0xC00D0FE3)
#define NS_E_WMP_UI_PASSTHROUGH          _HRESULT_TYPEDEF_(0xC00D0FE4)
#define NS_E_WMP_UI_OBJECTNOTFOUND       _HRESULT_TYPEDEF_(0xC00D0FE5)
#define NS_E_WMP_UI_SECONDHANDLER        _HRESULT_TYPEDEF_(0xC00D0FE6)
#define NS_E_WMP_UI_NOSKININZIP          _HRESULT_TYPEDEF_(0xC00D0FE7)
#define NS_S_WMP_UI_VERSIONMISMATCH      _HRESULT_TYPEDEF_(0x000D0FE8)
#define NS_S_WMP_EXCEPTION               _HRESULT_TYPEDEF_(0x000D0FE9)
#define NS_E_WMP_URLDOWNLOADFAILED       _HRESULT_TYPEDEF_(0xC00D0FEA)
#define NS_E_WMPOCX_UNABLE_TO_LOAD_SKIN  _HRESULT_TYPEDEF_(0xC00D0FEB)
#define NS_E_WMP_INVALID_SKIN            _HRESULT_TYPEDEF_(0xC00D0FEC)
#define NS_E_WMP_SENDMAILFAILED          _HRESULT_TYPEDEF_(0xC00D0FED)
#define NS_E_WMP_LOCKEDINSKINMODE        _HRESULT_TYPEDEF_(0xC00D0FEE)
#define NS_E_WMP_FAILED_TO_SAVE_FILE     _HRESULT_TYPEDEF_(0xC00D0FEF)
#define NS_E_WMP_SAVEAS_READONLY         _HRESULT_TYPEDEF_(0xC00D0FF0)
#define NS_E_WMP_FAILED_TO_SAVE_PLAYLIST _HRESULT_TYPEDEF_(0xC00D0FF1)
#define NS_E_WMP_FAILED_TO_OPEN_WMD      _HRESULT_TYPEDEF_(0xC00D0FF2)
#define NS_E_WMP_CANT_PLAY_PROTECTED     _HRESULT_TYPEDEF_(0xC00D0FF3)
#define NS_E_SHARING_STATE_OUT_OF_SYNC   _HRESULT_TYPEDEF_(0xC00D0FF4)
#define NS_E_WMPOCX_REMOTE_PLAYER_ALREADY_RUNNING _HRESULT_TYPEDEF_(0xC00D0FFA)
#define NS_E_WMP_RBC_JPGMAPPINGIMAGE     _HRESULT_TYPEDEF_(0xC00D1004)
#define NS_E_WMP_JPGTRANSPARENCY         _HRESULT_TYPEDEF_(0xC00D1005)
#define NS_E_WMP_INVALID_MAX_VAL         _HRESULT_TYPEDEF_(0xC00D1009)
#define NS_E_WMP_INVALID_MIN_VAL         _HRESULT_TYPEDEF_(0xC00D100A)
#define NS_E_WMP_CS_JPGPOSITIONIMAGE     _HRESULT_TYPEDEF_(0xC00D100E)
#define NS_E_WMP_CS_NOTEVENLYDIVISIBLE   _HRESULT_TYPEDEF_(0xC00D100F)
#define NS_E_WMPZIP_NOTAZIPFILE          _HRESULT_TYPEDEF_(0xC00D1018)
#define NS_E_WMPZIP_CORRUPT              _HRESULT_TYPEDEF_(0xC00D1019)
#define NS_E_WMPZIP_FILENOTFOUND         _HRESULT_TYPEDEF_(0xC00D101A)
#define NS_E_WMP_IMAGE_FILETYPE_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D1022)
#define NS_E_WMP_IMAGE_INVALID_FORMAT    _HRESULT_TYPEDEF_(0xC00D1023)
#define NS_E_WMP_GIF_UNEXPECTED_ENDOFFILE _HRESULT_TYPEDEF_(0xC00D1024)
#define NS_E_WMP_GIF_INVALID_FORMAT      _HRESULT_TYPEDEF_(0xC00D1025)
#define NS_E_WMP_GIF_BAD_VERSION_NUMBER  _HRESULT_TYPEDEF_(0xC00D1026)
#define NS_E_WMP_GIF_NO_IMAGE_IN_FILE    _HRESULT_TYPEDEF_(0xC00D1027)
#define NS_E_WMP_PNG_INVALIDFORMAT       _HRESULT_TYPEDEF_(0xC00D1028)
#define NS_E_WMP_PNG_UNSUPPORTED_BITDEPTH _HRESULT_TYPEDEF_(0xC00D1029)
#define NS_E_WMP_PNG_UNSUPPORTED_COMPRESSION _HRESULT_TYPEDEF_(0xC00D102A)
#define NS_E_WMP_PNG_UNSUPPORTED_FILTER  _HRESULT_TYPEDEF_(0xC00D102B)
#define NS_E_WMP_PNG_UNSUPPORTED_INTERLACE _HRESULT_TYPEDEF_(0xC00D102C)
#define NS_E_WMP_PNG_UNSUPPORTED_BAD_CRC _HRESULT_TYPEDEF_(0xC00D102D)
#define NS_E_WMP_BMP_INVALID_BITMASK     _HRESULT_TYPEDEF_(0xC00D102E)
#define NS_E_WMP_BMP_TOPDOWN_DIB_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D102F)
#define NS_E_WMP_BMP_BITMAP_NOT_CREATED  _HRESULT_TYPEDEF_(0xC00D1030)
#define NS_E_WMP_BMP_COMPRESSION_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D1031)
#define NS_E_WMP_BMP_INVALID_FORMAT      _HRESULT_TYPEDEF_(0xC00D1032)
#define NS_E_WMP_JPG_JERR_ARITHCODING_NOTIMPL _HRESULT_TYPEDEF_(0xC00D1033)
#define NS_E_WMP_JPG_INVALID_FORMAT      _HRESULT_TYPEDEF_(0xC00D1034)
#define NS_E_WMP_JPG_BAD_DCTSIZE         _HRESULT_TYPEDEF_(0xC00D1035)
#define NS_E_WMP_JPG_BAD_VERSION_NUMBER  _HRESULT_TYPEDEF_(0xC00D1036)
#define NS_E_WMP_JPG_BAD_PRECISION       _HRESULT_TYPEDEF_(0xC00D1037)
#define NS_E_WMP_JPG_CCIR601_NOTIMPL     _HRESULT_TYPEDEF_(0xC00D1038)
#define NS_E_WMP_JPG_NO_IMAGE_IN_FILE    _HRESULT_TYPEDEF_(0xC00D1039)
#define NS_E_WMP_JPG_READ_ERROR          _HRESULT_TYPEDEF_(0xC00D103A)
#define NS_E_WMP_JPG_FRACT_SAMPLE_NOTIMPL _HRESULT_TYPEDEF_(0xC00D103B)
#define NS_E_WMP_JPG_IMAGE_TOO_BIG       _HRESULT_TYPEDEF_(0xC00D103C)
#define NS_E_WMP_JPG_UNEXPECTED_ENDOFFILE _HRESULT_TYPEDEF_(0xC00D103D)
#define NS_E_WMP_JPG_SOF_UNSUPPORTED     _HRESULT_TYPEDEF_(0xC00D103E)
#define NS_E_WMP_JPG_UNKNOWN_MARKER      _HRESULT_TYPEDEF_(0xC00D103F)
#define NS_S_WMP_LOADED_GIF_IMAGE        _HRESULT_TYPEDEF_(0x000D1040)
#define NS_S_WMP_LOADED_PNG_IMAGE        _HRESULT_TYPEDEF_(0x000D1041)
#define NS_S_WMP_LOADED_BMP_IMAGE        _HRESULT_TYPEDEF_(0x000D1042)
#define NS_S_WMP_LOADED_JPG_IMAGE        _HRESULT_TYPEDEF_(0x000D1043)
#define NS_E_WMP_FAILED_TO_OPEN_IMAGE    _HRESULT_TYPEDEF_(0xC00D1044)
#define NS_E_WMP_DAI_SONGTOOSHORT        _HRESULT_TYPEDEF_(0xC00D1049)
#define NS_E_WMG_RATEUNAVAILABLE         _HRESULT_TYPEDEF_(0xC00D104A)
#define NS_E_WMG_PLUGINUNAVAILABLE       _HRESULT_TYPEDEF_(0xC00D104B)
#define NS_E_WMG_CANNOTQUEUE             _HRESULT_TYPEDEF_(0xC00D104C)
#define NS_E_WMG_PREROLLLICENSEACQUISITIONNOTALLOWED _HRESULT_TYPEDEF_(0xC00D104D)
#define NS_E_WMG_UNEXPECTEDPREROLLSTATUS _HRESULT_TYPEDEF_(0xC00D104E)
#define NS_S_WMG_FORCE_DROP_FRAME        _HRESULT_TYPEDEF_(0x000D104F)
#define NS_E_WMG_INVALID_COPP_CERTIFICATE _HRESULT_TYPEDEF_(0xC00D1051)
#define NS_E_WMG_COPP_SECURITY_INVALID   _HRESULT_TYPEDEF_(0xC00D1052)
#define NS_E_WMG_COPP_UNSUPPORTED        _HRESULT_TYPEDEF_(0xC00D1053)
#define NS_E_WMG_INVALIDSTATE            _HRESULT_TYPEDEF_(0xC00D1054)
#define NS_E_WMG_SINKALREADYEXISTS       _HRESULT_TYPEDEF_(0xC00D1055)
#define NS_E_WMG_NOSDKINTERFACE          _HRESULT_TYPEDEF_(0xC00D1056)
#define NS_E_WMG_NOTALLOUTPUTSRENDERED   _HRESULT_TYPEDEF_(0xC00D1057)
#define NS_E_WMG_FILETRANSFERNOTALLOWED  _HRESULT_TYPEDEF_(0xC00D1058)
#define NS_E_WMR_UNSUPPORTEDSTREAM       _HRESULT_TYPEDEF_(0xC00D1059)
#define NS_E_WMR_PINNOTFOUND             _HRESULT_TYPEDEF_(0xC00D105A)
#define NS_E_WMR_WAITINGONFORMATSWITCH   _HRESULT_TYPEDEF_(0xC00D105B)
#define NS_E_WMR_NOSOURCEFILTER          _HRESULT_TYPEDEF_(0xC00D105C)
#define NS_E_WMR_PINTYPENOMATCH          _HRESULT_TYPEDEF_(0xC00D105D)
#define NS_E_WMR_NOCALLBACKAVAILABLE     _HRESULT_TYPEDEF_(0xC00D105E)
#define NS_S_WMR_ALREADYRENDERED         _HRESULT_TYPEDEF_(0x000D105F)
#define NS_S_WMR_PINTYPEPARTIALMATCH     _HRESULT_TYPEDEF_(0x000D1060)
#define NS_S_WMR_PINTYPEFULLMATCH        _HRESULT_TYPEDEF_(0x000D1061)
#define NS_E_WMR_SAMPLEPROPERTYNOTSET    _HRESULT_TYPEDEF_(0xC00D1062)
#define NS_E_WMR_CANNOT_RENDER_BINARY_STREAM _HRESULT_TYPEDEF_(0xC00D1063)
#define NS_E_WMG_LICENSE_TAMPERED        _HRESULT_TYPEDEF_(0xC00D1064)
#define NS_E_WMR_WILLNOT_RENDER_BINARY_STREAM _HRESULT_TYPEDEF_(0xC00D1065)
#define NS_S_WMG_ADVISE_DROP_FRAME       _HRESULT_TYPEDEF_(0x000D1066)
#define NS_S_WMG_ADVISE_DROP_TO_KEYFRAME _HRESULT_TYPEDEF_(0x000D1067)
#define NS_E_WMX_UNRECOGNIZED_PLAYLIST_FORMAT _HRESULT_TYPEDEF_(0xC00D1068)
#define NS_E_ASX_INVALIDFORMAT           _HRESULT_TYPEDEF_(0xC00D1069)
#define NS_E_ASX_INVALIDVERSION          _HRESULT_TYPEDEF_(0xC00D106A)
#define NS_E_ASX_INVALID_REPEAT_BLOCK    _HRESULT_TYPEDEF_(0xC00D106B)
#define NS_E_ASX_NOTHING_TO_WRITE        _HRESULT_TYPEDEF_(0xC00D106C)
#define NS_E_URLLIST_INVALIDFORMAT       _HRESULT_TYPEDEF_(0xC00D106D)
#define NS_E_WMX_ATTRIBUTE_DOES_NOT_EXIST _HRESULT_TYPEDEF_(0xC00D106E)
#define NS_E_WMX_ATTRIBUTE_ALREADY_EXISTS _HRESULT_TYPEDEF_(0xC00D106F)
#define NS_E_WMX_ATTRIBUTE_UNRETRIEVABLE _HRESULT_TYPEDEF_(0xC00D1070)
#define NS_E_WMX_ITEM_DOES_NOT_EXIST     _HRESULT_TYPEDEF_(0xC00D1071)
#define NS_E_WMX_ITEM_TYPE_ILLEGAL       _HRESULT_TYPEDEF_(0xC00D1072)
#define NS_E_WMX_ITEM_UNSETTABLE         _HRESULT_TYPEDEF_(0xC00D1073)
#define NS_E_WMX_PLAYLIST_EMPTY          _HRESULT_TYPEDEF_(0xC00D1074)
#define NS_E_MLS_SMARTPLAYLIST_FILTER_NOT_REGISTERED _HRESULT_TYPEDEF_(0xC00D1075)
#define NS_E_WMX_INVALID_FORMAT_OVER_NESTING _HRESULT_TYPEDEF_(0xC00D1076)
#define NS_E_WMPCORE_NOSOURCEURLSTRING   _HRESULT_TYPEDEF_(0xC00D107C)
#define NS_E_WMPCORE_COCREATEFAILEDFORGITOBJECT _HRESULT_TYPEDEF_(0xC00D107D)
#define NS_E_WMPCORE_FAILEDTOGETMARSHALLEDEVENTHANDLERINTERFACE _HRESULT_TYPEDEF_(0xC00D107E)
#define NS_E_WMPCORE_BUFFERTOOSMALL      _HRESULT_TYPEDEF_(0xC00D107F)
#define NS_E_WMPCORE_UNAVAILABLE         _HRESULT_TYPEDEF_(0xC00D1080)
#define NS_E_WMPCORE_INVALIDPLAYLISTMODE _HRESULT_TYPEDEF_(0xC00D1081)
#define NS_E_WMPCORE_ITEMNOTINPLAYLIST   _HRESULT_TYPEDEF_(0xC00D1086)
#define NS_E_WMPCORE_PLAYLISTEMPTY       _HRESULT_TYPEDEF_(0xC00D1087)
#define NS_E_WMPCORE_NOBROWSER           _HRESULT_TYPEDEF_(0xC00D1088)
#define NS_E_WMPCORE_UNRECOGNIZED_MEDIA_URL _HRESULT_TYPEDEF_(0xC00D1089)
#define NS_E_WMPCORE_GRAPH_NOT_IN_LIST   _HRESULT_TYPEDEF_(0xC00D108A)
#define NS_E_WMPCORE_PLAYLIST_EMPTY_OR_SINGLE_MEDIA _HRESULT_TYPEDEF_(0xC00D108B)
#define NS_E_WMPCORE_ERRORSINKNOTREGISTERED _HRESULT_TYPEDEF_(0xC00D108C)
#define NS_E_WMPCORE_ERRORMANAGERNOTAVAILABLE _HRESULT_TYPEDEF_(0xC00D108D)
#define NS_E_WMPCORE_WEBHELPFAILED       _HRESULT_TYPEDEF_(0xC00D108E)
#define NS_E_WMPCORE_MEDIA_ERROR_RESUME_FAILED _HRESULT_TYPEDEF_(0xC00D108F)
#define NS_E_WMPCORE_NO_REF_IN_ENTRY     _HRESULT_TYPEDEF_(0xC00D1090)
#define NS_E_WMPCORE_WMX_LIST_ATTRIBUTE_NAME_EMPTY _HRESULT_TYPEDEF_(0xC00D1091)
#define NS_E_WMPCORE_WMX_LIST_ATTRIBUTE_NAME_ILLEGAL _HRESULT_TYPEDEF_(0xC00D1092)
#define NS_E_WMPCORE_WMX_LIST_ATTRIBUTE_VALUE_EMPTY _HRESULT_TYPEDEF_(0xC00D1093)
#define NS_E_WMPCORE_WMX_LIST_ATTRIBUTE_VALUE_ILLEGAL _HRESULT_TYPEDEF_(0xC00D1094)
#define NS_E_WMPCORE_WMX_LIST_ITEM_ATTRIBUTE_NAME_EMPTY _HRESULT_TYPEDEF_(0xC00D1095)
#define NS_E_WMPCORE_WMX_LIST_ITEM_ATTRIBUTE_NAME_ILLEGAL _HRESULT_TYPEDEF_(0xC00D1096)
#define NS_E_WMPCORE_WMX_LIST_ITEM_ATTRIBUTE_VALUE_EMPTY _HRESULT_TYPEDEF_(0xC00D1097)
#define NS_E_WMPCORE_LIST_ENTRY_NO_REF   _HRESULT_TYPEDEF_(0xC00D1098)
#define NS_E_WMPCORE_MISNAMED_FILE       _HRESULT_TYPEDEF_(0xC00D1099)
#define NS_E_WMPCORE_CODEC_NOT_TRUSTED   _HRESULT_TYPEDEF_(0xC00D109A)
#define NS_E_WMPCORE_CODEC_NOT_FOUND     _HRESULT_TYPEDEF_(0xC00D109B)
#define NS_E_WMPCORE_CODEC_DOWNLOAD_NOT_ALLOWED _HRESULT_TYPEDEF_(0xC00D109C)
#define NS_E_WMPCORE_ERROR_DOWNLOADING_PLAYLIST _HRESULT_TYPEDEF_(0xC00D109D)
#define NS_E_WMPCORE_FAILED_TO_BUILD_PLAYLIST _HRESULT_TYPEDEF_(0xC00D109E)
#define NS_E_WMPCORE_PLAYLIST_ITEM_ALTERNATE_NONE _HRESULT_TYPEDEF_(0xC00D109F)
#define NS_E_WMPCORE_PLAYLIST_ITEM_ALTERNATE_EXHAUSTED _HRESULT_TYPEDEF_(0xC00D10A0)
#define NS_E_WMPCORE_PLAYLIST_ITEM_ALTERNATE_NAME_NOT_FOUND _HRESULT_TYPEDEF_(0xC00D10A1)
#define NS_E_WMPCORE_PLAYLIST_ITEM_ALTERNATE_MORPH_FAILED _HRESULT_TYPEDEF_(0xC00D10A2)
#define NS_E_WMPCORE_PLAYLIST_ITEM_ALTERNATE_INIT_FAILED _HRESULT_TYPEDEF_(0xC00D10A3)
#define NS_E_WMPCORE_MEDIA_ALTERNATE_REF_EMPTY _HRESULT_TYPEDEF_(0xC00D10A4)
#define NS_E_WMPCORE_PLAYLIST_NO_EVENT_NAME _HRESULT_TYPEDEF_(0xC00D10A5)
#define NS_E_WMPCORE_PLAYLIST_EVENT_ATTRIBUTE_ABSENT _HRESULT_TYPEDEF_(0xC00D10A6)
#define NS_E_WMPCORE_PLAYLIST_EVENT_EMPTY _HRESULT_TYPEDEF_(0xC00D10A7)
#define NS_E_WMPCORE_PLAYLIST_STACK_EMPTY _HRESULT_TYPEDEF_(0xC00D10A8)
#define NS_E_WMPCORE_CURRENT_MEDIA_NOT_ACTIVE _HRESULT_TYPEDEF_(0xC00D10A9)
#define NS_E_WMPCORE_USER_CANCEL         _HRESULT_TYPEDEF_(0xC00D10AB)
#define NS_E_WMPCORE_PLAYLIST_REPEAT_EMPTY _HRESULT_TYPEDEF_(0xC00D10AC)
#define NS_E_WMPCORE_PLAYLIST_REPEAT_START_MEDIA_NONE _HRESULT_TYPEDEF_(0xC00D10AD)
#define NS_E_WMPCORE_PLAYLIST_REPEAT_END_MEDIA_NONE _HRESULT_TYPEDEF_(0xC00D10AE)
#define NS_E_WMPCORE_INVALID_PLAYLIST_URL _HRESULT_TYPEDEF_(0xC00D10AF)
#define NS_E_WMPCORE_MISMATCHED_RUNTIME  _HRESULT_TYPEDEF_(0xC00D10B0)
#define NS_E_WMPCORE_PLAYLIST_IMPORT_FAILED_NO_ITEMS _HRESULT_TYPEDEF_(0xC00D10B1)
#define NS_E_WMPCORE_VIDEO_TRANSFORM_FILTER_INSERTION _HRESULT_TYPEDEF_(0xC00D10B2)
#define NS_E_WMPCORE_MEDIA_UNAVAILABLE   _HRESULT_TYPEDEF_(0xC00D10B3)
#define NS_E_WMPCORE_WMX_ENTRYREF_NO_REF _HRESULT_TYPEDEF_(0xC00D10B4)
#define NS_E_WMPCORE_NO_PLAYABLE_MEDIA_IN_PLAYLIST _HRESULT_TYPEDEF_(0xC00D10B5)
#define NS_E_WMPCORE_PLAYLIST_EMPTY_NESTED_PLAYLIST_SKIPPED_ITEMS _HRESULT_TYPEDEF_(0xC00D10B6)
#define NS_E_WMPCORE_BUSY                _HRESULT_TYPEDEF_(0xC00D10B7)
#define NS_E_WMPCORE_MEDIA_CHILD_PLAYLIST_UNAVAILABLE _HRESULT_TYPEDEF_(0xC00D10B8)
#define NS_E_WMPCORE_MEDIA_NO_CHILD_PLAYLIST _HRESULT_TYPEDEF_(0xC00D10B9)
#define NS_E_WMPCORE_FILE_NOT_FOUND      _HRESULT_TYPEDEF_(0xC00D10BA)
#define NS_E_WMPCORE_TEMP_FILE_NOT_FOUND _HRESULT_TYPEDEF_(0xC00D10BB)
#define NS_E_WMDM_REVOKED                _HRESULT_TYPEDEF_(0xC00D10BC)
#define NS_E_DDRAW_GENERIC               _HRESULT_TYPEDEF_(0xC00D10BD)
#define NS_E_DISPLAY_MODE_CHANGE_FAILED  _HRESULT_TYPEDEF_(0xC00D10BE)
#define NS_E_PLAYLIST_CONTAINS_ERRORS    _HRESULT_TYPEDEF_(0xC00D10BF)
#define NS_E_CHANGING_PROXY_NAME         _HRESULT_TYPEDEF_(0xC00D10C0)
#define NS_E_CHANGING_PROXY_PORT         _HRESULT_TYPEDEF_(0xC00D10C1)
#define NS_E_CHANGING_PROXY_EXCEPTIONLIST _HRESULT_TYPEDEF_(0xC00D10C2)
#define NS_E_CHANGING_PROXYBYPASS        _HRESULT_TYPEDEF_(0xC00D10C3)
#define NS_E_CHANGING_PROXY_PROTOCOL_NOT_FOUND _HRESULT_TYPEDEF_(0xC00D10C4)
#define NS_E_GRAPH_NOAUDIOLANGUAGE       _HRESULT_TYPEDEF_(0xC00D10C5)
#define NS_E_GRAPH_NOAUDIOLANGUAGESELECTED _HRESULT_TYPEDEF_(0xC00D10C6)
#define NS_E_CORECD_NOTAMEDIACD          _HRESULT_TYPEDEF_(0xC00D10C7)
#define NS_E_WMPCORE_MEDIA_URL_TOO_LONG  _HRESULT_TYPEDEF_(0xC00D10C8)
#define NS_E_WMPFLASH_CANT_FIND_COM_SERVER _HRESULT_TYPEDEF_(0xC00D10C9)
#define NS_E_WMPFLASH_INCOMPATIBLEVERSION _HRESULT_TYPEDEF_(0xC00D10CA)
#define NS_E_WMPOCXGRAPH_IE_DISALLOWS_ACTIVEX_CONTROLS _HRESULT_TYPEDEF_(0xC00D10CB)
#define NS_E_NEED_CORE_REFERENCE         _HRESULT_TYPEDEF_(0xC00D10CC)
#define NS_E_MEDIACD_READ_ERROR          _HRESULT_TYPEDEF_(0xC00D10CD)
#define NS_E_IE_DISALLOWS_ACTIVEX_CONTROLS _HRESULT_TYPEDEF_(0xC00D10CE)
#define NS_E_FLASH_PLAYBACK_NOT_ALLOWED  _HRESULT_TYPEDEF_(0xC00D10CF)
#define NS_E_UNABLE_TO_CREATE_RIP_LOCATION _HRESULT_TYPEDEF_(0xC00D10D0)
#define NS_E_WMPCORE_SOME_CODECS_MISSING _HRESULT_TYPEDEF_(0xC00D10D1)
#define NS_E_WMP_RIP_FAILED              _HRESULT_TYPEDEF_(0xC00D10D2)
#define NS_E_WMP_FAILED_TO_RIP_TRACK     _HRESULT_TYPEDEF_(0xC00D10D3)
#define NS_E_WMP_ERASE_FAILED            _HRESULT_TYPEDEF_(0xC00D10D4)
#define NS_E_WMP_FORMAT_FAILED           _HRESULT_TYPEDEF_(0xC00D10D5)
#define NS_E_WMP_CANNOT_BURN_NON_LOCAL_FILE _HRESULT_TYPEDEF_(0xC00D10D6)
#define NS_E_WMP_FILE_TYPE_CANNOT_BURN_TO_AUDIO_CD _HRESULT_TYPEDEF_(0xC00D10D7)
#define NS_E_WMP_FILE_DOES_NOT_FIT_ON_CD _HRESULT_TYPEDEF_(0xC00D10D8)
#define NS_E_WMP_FILE_NO_DURATION        _HRESULT_TYPEDEF_(0xC00D10D9)
#define NS_E_PDA_FAILED_TO_BURN          _HRESULT_TYPEDEF_(0xC00D10DA)
#define NS_S_NEED_TO_BUY_BURN_RIGHTS     _HRESULT_TYPEDEF_(0x000D10DB)
#define NS_E_FAILED_DOWNLOAD_ABORT_BURN  _HRESULT_TYPEDEF_(0xC00D10DC)
#define NS_E_WMPCORE_DEVICE_DRIVERS_MISSING _HRESULT_TYPEDEF_(0xC00D10DD)
#define NS_S_WMPCORE_PLAYLISTCLEARABORT  _HRESULT_TYPEDEF_(0x000D10FE)
#define NS_S_WMPCORE_PLAYLISTREMOVEITEMABORT _HRESULT_TYPEDEF_(0x000D10FF)
#define NS_S_WMPCORE_PLAYLIST_CREATION_PENDING _HRESULT_TYPEDEF_(0x000D1102)
#define NS_S_WMPCORE_MEDIA_VALIDATION_PENDING _HRESULT_TYPEDEF_(0x000D1103)
#define NS_S_WMPCORE_PLAYLIST_REPEAT_SECONDARY_SEGMENTS_IGNORED _HRESULT_TYPEDEF_(0x000D1104)
#define NS_S_WMPCORE_COMMAND_NOT_AVAILABLE _HRESULT_TYPEDEF_(0x000D1105)
#define NS_S_WMPCORE_PLAYLIST_NAME_AUTO_GENERATED _HRESULT_TYPEDEF_(0x000D1106)
#define NS_S_WMPCORE_PLAYLIST_IMPORT_MISSING_ITEMS _HRESULT_TYPEDEF_(0x000D1107)
#define NS_S_WMPCORE_PLAYLIST_COLLAPSED_TO_SINGLE_MEDIA _HRESULT_TYPEDEF_(0x000D1108)
#define NS_S_WMPCORE_MEDIA_CHILD_PLAYLIST_OPEN_PENDING _HRESULT_TYPEDEF_(0x000D1109)
#define NS_S_WMPCORE_MORE_NODES_AVAIABLE _HRESULT_TYPEDEF_(0x000D110A)
#define NS_E_WMPIM_USEROFFLINE           _HRESULT_TYPEDEF_(0xC00D1126)
#define NS_E_WMPIM_USERCANCELED          _HRESULT_TYPEDEF_(0xC00D1127)
#define NS_E_WMPIM_DIALUPFAILED          _HRESULT_TYPEDEF_(0xC00D1128)
#define NS_E_WINSOCK_ERROR_STRING        _HRESULT_TYPEDEF_(0xC00D1129)
#define NS_E_WMPBR_NOLISTENER            _HRESULT_TYPEDEF_(0xC00D1130)
#define NS_E_WMPBR_BACKUPCANCEL          _HRESULT_TYPEDEF_(0xC00D1131)
#define NS_E_WMPBR_RESTORECANCEL         _HRESULT_TYPEDEF_(0xC00D1132)
#define NS_E_WMPBR_ERRORWITHURL          _HRESULT_TYPEDEF_(0xC00D1133)
#define NS_E_WMPBR_NAMECOLLISION         _HRESULT_TYPEDEF_(0xC00D1134)
#define NS_S_WMPBR_SUCCESS               _HRESULT_TYPEDEF_(0x000D1135)
#define NS_S_WMPBR_PARTIALSUCCESS        _HRESULT_TYPEDEF_(0x000D1136)
#define NS_E_WMPBR_DRIVE_INVALID         _HRESULT_TYPEDEF_(0xC00D1137)
#define NS_E_WMPBR_BACKUPRESTOREFAILED   _HRESULT_TYPEDEF_(0xC00D1138)
#define NS_S_WMPEFFECT_TRANSPARENT       _HRESULT_TYPEDEF_(0x000D1144)
#define NS_S_WMPEFFECT_OPAQUE            _HRESULT_TYPEDEF_(0x000D1145)
#define NS_S_OPERATION_PENDING           _HRESULT_TYPEDEF_(0x000D114E)
#define NS_E_WMP_CONVERT_FILE_FAILED     _HRESULT_TYPEDEF_(0xC00D1158)
#define NS_E_WMP_CONVERT_NO_RIGHTS_ERRORURL _HRESULT_TYPEDEF_(0xC00D1159)
#define NS_E_WMP_CONVERT_NO_RIGHTS_NOERRORURL _HRESULT_TYPEDEF_(0xC00D115A)
#define NS_E_WMP_CONVERT_FILE_CORRUPT    _HRESULT_TYPEDEF_(0xC00D115B)
#define NS_E_WMP_CONVERT_PLUGIN_UNAVAILABLE_ERRORURL _HRESULT_TYPEDEF_(0xC00D115C)
#define NS_E_WMP_CONVERT_PLUGIN_UNAVAILABLE_NOERRORURL _HRESULT_TYPEDEF_(0xC00D115D)
#define NS_E_WMP_CONVERT_PLUGIN_UNKNOWN_FILE_OWNER _HRESULT_TYPEDEF_(0xC00D115E)
#define NS_E_DVD_DISC_COPY_PROTECT_OUTPUT_NS _HRESULT_TYPEDEF_(0xC00D1160)
#define NS_E_DVD_DISC_COPY_PROTECT_OUTPUT_FAILED _HRESULT_TYPEDEF_(0xC00D1161)
#define NS_E_DVD_NO_SUBPICTURE_STREAM    _HRESULT_TYPEDEF_(0xC00D1162)
#define NS_E_DVD_COPY_PROTECT            _HRESULT_TYPEDEF_(0xC00D1163)
#define NS_E_DVD_AUTHORING_PROBLEM       _HRESULT_TYPEDEF_(0xC00D1164)
#define NS_E_DVD_INVALID_DISC_REGION     _HRESULT_TYPEDEF_(0xC00D1165)
#define NS_E_DVD_COMPATIBLE_VIDEO_CARD   _HRESULT_TYPEDEF_(0xC00D1166)
#define NS_E_DVD_MACROVISION             _HRESULT_TYPEDEF_(0xC00D1167)
#define NS_E_DVD_SYSTEM_DECODER_REGION   _HRESULT_TYPEDEF_(0xC00D1168)
#define NS_E_DVD_DISC_DECODER_REGION     _HRESULT_TYPEDEF_(0xC00D1169)
#define NS_E_DVD_NO_VIDEO_STREAM         _HRESULT_TYPEDEF_(0xC00D116A)
#define NS_E_DVD_NO_AUDIO_STREAM         _HRESULT_TYPEDEF_(0xC00D116B)
#define NS_E_DVD_GRAPH_BUILDING          _HRESULT_TYPEDEF_(0xC00D116C)
#define NS_E_DVD_NO_DECODER              _HRESULT_TYPEDEF_(0xC00D116D)
#define NS_E_DVD_PARENTAL                _HRESULT_TYPEDEF_(0xC00D116E)
#define NS_E_DVD_CANNOT_JUMP             _HRESULT_TYPEDEF_(0xC00D116F)
#define NS_E_DVD_DEVICE_CONTENTION       _HRESULT_TYPEDEF_(0xC00D1170)
#define NS_E_DVD_NO_VIDEO_MEMORY         _HRESULT_TYPEDEF_(0xC00D1171)
#define NS_E_DVD_CANNOT_COPY_PROTECTED   _HRESULT_TYPEDEF_(0xC00D1172)
#define NS_E_DVD_REQUIRED_PROPERTY_NOT_SET _HRESULT_TYPEDEF_(0xC00D1173)
#define NS_E_DVD_INVALID_TITLE_CHAPTER   _HRESULT_TYPEDEF_(0xC00D1174)
#define NS_E_NO_CD_BURNER                _HRESULT_TYPEDEF_(0xC00D1176)
#define NS_E_DEVICE_IS_NOT_READY         _HRESULT_TYPEDEF_(0xC00D1177)
#define NS_E_PDA_UNSUPPORTED_FORMAT      _HRESULT_TYPEDEF_(0xC00D1178)
#define NS_E_NO_PDA                      _HRESULT_TYPEDEF_(0xC00D1179)
#define NS_E_PDA_UNSPECIFIED_ERROR       _HRESULT_TYPEDEF_(0xC00D117A)
#define NS_E_MEMSTORAGE_BAD_DATA         _HRESULT_TYPEDEF_(0xC00D117B)
#define NS_E_PDA_FAIL_SELECT_DEVICE      _HRESULT_TYPEDEF_(0xC00D117C)
#define NS_E_PDA_FAIL_READ_WAVE_FILE     _HRESULT_TYPEDEF_(0xC00D117D)
#define NS_E_IMAPI_LOSSOFSTREAMING       _HRESULT_TYPEDEF_(0xC00D117E)
#define NS_E_PDA_DEVICE_FULL             _HRESULT_TYPEDEF_(0xC00D117F)
#define NS_E_FAIL_LAUNCH_ROXIO_PLUGIN    _HRESULT_TYPEDEF_(0xC00D1180)
#define NS_E_PDA_DEVICE_FULL_IN_SESSION  _HRESULT_TYPEDEF_(0xC00D1181)
#define NS_E_IMAPI_MEDIUM_INVALIDTYPE    _HRESULT_TYPEDEF_(0xC00D1182)
#define NS_E_PDA_MANUALDEVICE            _HRESULT_TYPEDEF_(0xC00D1183)
#define NS_E_PDA_PARTNERSHIPNOTEXIST     _HRESULT_TYPEDEF_(0xC00D1184)
#define NS_E_PDA_CANNOT_CREATE_ADDITIONAL_SYNC_RELATIONSHIP _HRESULT_TYPEDEF_(0xC00D1185)
#define NS_E_PDA_NO_TRANSCODE_OF_DRM     _HRESULT_TYPEDEF_(0xC00D1186)
#define NS_E_PDA_TRANSCODECACHEFULL      _HRESULT_TYPEDEF_(0xC00D1187)
#define NS_E_PDA_TOO_MANY_FILE_COLLISIONS _HRESULT_TYPEDEF_(0xC00D1188)
#define NS_E_PDA_CANNOT_TRANSCODE        _HRESULT_TYPEDEF_(0xC00D1189)
#define NS_E_PDA_TOO_MANY_FILES_IN_DIRECTORY _HRESULT_TYPEDEF_(0xC00D118A)
#define NS_E_PROCESSINGSHOWSYNCWIZARD    _HRESULT_TYPEDEF_(0xC00D118B)
#define NS_E_PDA_TRANSCODE_NOT_PERMITTED _HRESULT_TYPEDEF_(0xC00D118C)
#define NS_E_PDA_INITIALIZINGDEVICES     _HRESULT_TYPEDEF_(0xC00D118D)
#define NS_E_PDA_OBSOLETE_SP             _HRESULT_TYPEDEF_(0xC00D118E)
#define NS_E_PDA_TITLE_COLLISION         _HRESULT_TYPEDEF_(0xC00D118F)
#define NS_E_PDA_DEVICESUPPORTDISABLED   _HRESULT_TYPEDEF_(0xC00D1190)
#define NS_E_PDA_NO_LONGER_AVAILABLE     _HRESULT_TYPEDEF_(0xC00D1191)
#define NS_E_PDA_ENCODER_NOT_RESPONDING  _HRESULT_TYPEDEF_(0xC00D1192)
#define NS_E_PDA_CANNOT_SYNC_FROM_LOCATION _HRESULT_TYPEDEF_(0xC00D1193)
#define NS_E_WMP_PROTOCOL_PROBLEM        _HRESULT_TYPEDEF_(0xC00D1194)
#define NS_E_WMP_NO_DISK_SPACE           _HRESULT_TYPEDEF_(0xC00D1195)
#define NS_E_WMP_LOGON_FAILURE           _HRESULT_TYPEDEF_(0xC00D1196)
#define NS_E_WMP_CANNOT_FIND_FILE        _HRESULT_TYPEDEF_(0xC00D1197)
#define NS_E_WMP_SERVER_INACCESSIBLE     _HRESULT_TYPEDEF_(0xC00D1198)
#define NS_E_WMP_UNSUPPORTED_FORMAT      _HRESULT_TYPEDEF_(0xC00D1199)
#define NS_E_WMP_DSHOW_UNSUPPORTED_FORMAT _HRESULT_TYPEDEF_(0xC00D119A)
#define NS_E_WMP_PLAYLIST_EXISTS         _HRESULT_TYPEDEF_(0xC00D119B)
#define NS_E_WMP_NONMEDIA_FILES          _HRESULT_TYPEDEF_(0xC00D119C)
#define NS_E_WMP_INVALID_ASX             _HRESULT_TYPEDEF_(0xC00D119D)
#define NS_E_WMP_ALREADY_IN_USE          _HRESULT_TYPEDEF_(0xC00D119E)
#define NS_E_WMP_IMAPI_FAILURE           _HRESULT_TYPEDEF_(0xC00D119F)
#define NS_E_WMP_WMDM_FAILURE            _HRESULT_TYPEDEF_(0xC00D11A0)
#define NS_E_WMP_CODEC_NEEDED_WITH_4CC   _HRESULT_TYPEDEF_(0xC00D11A1)
#define NS_E_WMP_CODEC_NEEDED_WITH_FORMATTAG _HRESULT_TYPEDEF_(0xC00D11A2)
#define NS_E_WMP_MSSAP_NOT_AVAILABLE     _HRESULT_TYPEDEF_(0xC00D11A3)
#define NS_E_WMP_WMDM_INTERFACEDEAD      _HRESULT_TYPEDEF_(0xC00D11A4)
#define NS_E_WMP_WMDM_NOTCERTIFIED       _HRESULT_TYPEDEF_(0xC00D11A5)
#define NS_E_WMP_WMDM_LICENSE_NOTEXIST   _HRESULT_TYPEDEF_(0xC00D11A6)
#define NS_E_WMP_WMDM_LICENSE_EXPIRED    _HRESULT_TYPEDEF_(0xC00D11A7)
#define NS_E_WMP_WMDM_BUSY               _HRESULT_TYPEDEF_(0xC00D11A8)
#define NS_E_WMP_WMDM_NORIGHTS           _HRESULT_TYPEDEF_(0xC00D11A9)
#define NS_E_WMP_WMDM_INCORRECT_RIGHTS   _HRESULT_TYPEDEF_(0xC00D11AA)
#define NS_E_WMP_IMAPI_GENERIC           _HRESULT_TYPEDEF_(0xC00D11AB)
#define NS_E_WMP_IMAPI_DEVICE_NOTPRESENT _HRESULT_TYPEDEF_(0xC00D11AD)
#define NS_E_WMP_IMAPI_DEVICE_BUSY       _HRESULT_TYPEDEF_(0xC00D11AE)
#define NS_E_WMP_IMAPI_LOSS_OF_STREAMING _HRESULT_TYPEDEF_(0xC00D11AF)
#define NS_E_WMP_SERVER_UNAVAILABLE      _HRESULT_TYPEDEF_(0xC00D11B0)
#define NS_E_WMP_FILE_OPEN_FAILED        _HRESULT_TYPEDEF_(0xC00D11B1)
#define NS_E_WMP_VERIFY_ONLINE           _HRESULT_TYPEDEF_(0xC00D11B2)
#define NS_E_WMP_SERVER_NOT_RESPONDING   _HRESULT_TYPEDEF_(0xC00D11B3)
#define NS_E_WMP_DRM_CORRUPT_BACKUP      _HRESULT_TYPEDEF_(0xC00D11B4)
#define NS_E_WMP_DRM_LICENSE_SERVER_UNAVAILABLE _HRESULT_TYPEDEF_(0xC00D11B5)
#define NS_E_WMP_NETWORK_FIREWALL        _HRESULT_TYPEDEF_(0xC00D11B6)
#define NS_E_WMP_NO_REMOVABLE_MEDIA      _HRESULT_TYPEDEF_(0xC00D11B7)
#define NS_E_WMP_PROXY_CONNECT_TIMEOUT   _HRESULT_TYPEDEF_(0xC00D11B8)
#define NS_E_WMP_NEED_UPGRADE            _HRESULT_TYPEDEF_(0xC00D11B9)
#define NS_E_WMP_AUDIO_HW_PROBLEM        _HRESULT_TYPEDEF_(0xC00D11BA)
#define NS_E_WMP_INVALID_PROTOCOL        _HRESULT_TYPEDEF_(0xC00D11BB)
#define NS_E_WMP_INVALID_LIBRARY_ADD     _HRESULT_TYPEDEF_(0xC00D11BC)
#define NS_E_WMP_MMS_NOT_SUPPORTED       _HRESULT_TYPEDEF_(0xC00D11BD)
#define NS_E_WMP_NO_PROTOCOLS_SELECTED   _HRESULT_TYPEDEF_(0xC00D11BE)
#define NS_E_WMP_GOFULLSCREEN_FAILED     _HRESULT_TYPEDEF_(0xC00D11BF)
#define NS_E_WMP_NETWORK_ERROR           _HRESULT_TYPEDEF_(0xC00D11C0)
#define NS_E_WMP_CONNECT_TIMEOUT         _HRESULT_TYPEDEF_(0xC00D11C1)
#define NS_E_WMP_MULTICAST_DISABLED      _HRESULT_TYPEDEF_(0xC00D11C2)
#define NS_E_WMP_SERVER_DNS_TIMEOUT      _HRESULT_TYPEDEF_(0xC00D11C3)
#define NS_E_WMP_PROXY_NOT_FOUND         _HRESULT_TYPEDEF_(0xC00D11C4)
#define NS_E_WMP_TAMPERED_CONTENT        _HRESULT_TYPEDEF_(0xC00D11C5)
#define NS_E_WMP_OUTOFMEMORY             _HRESULT_TYPEDEF_(0xC00D11C6)
#define NS_E_WMP_AUDIO_CODEC_NOT_INSTALLED _HRESULT_TYPEDEF_(0xC00D11C7)
#define NS_E_WMP_VIDEO_CODEC_NOT_INSTALLED _HRESULT_TYPEDEF_(0xC00D11C8)
#define NS_E_WMP_IMAPI_DEVICE_INVALIDTYPE _HRESULT_TYPEDEF_(0xC00D11C9)
#define NS_E_WMP_DRM_DRIVER_AUTH_FAILURE _HRESULT_TYPEDEF_(0xC00D11CA)
#define NS_E_WMP_NETWORK_RESOURCE_FAILURE _HRESULT_TYPEDEF_(0xC00D11CB)
#define NS_E_WMP_UPGRADE_APPLICATION     _HRESULT_TYPEDEF_(0xC00D11CC)
#define NS_E_WMP_UNKNOWN_ERROR           _HRESULT_TYPEDEF_(0xC00D11CD)
#define NS_E_WMP_INVALID_KEY             _HRESULT_TYPEDEF_(0xC00D11CE)
#define NS_E_WMP_CD_ANOTHER_USER         _HRESULT_TYPEDEF_(0xC00D11CF)
#define NS_E_WMP_DRM_NEEDS_AUTHORIZATION _HRESULT_TYPEDEF_(0xC00D11D0)
#define NS_E_WMP_BAD_DRIVER              _HRESULT_TYPEDEF_(0xC00D11D1)
#define NS_E_WMP_ACCESS_DENIED           _HRESULT_TYPEDEF_(0xC00D11D2)
#define NS_E_WMP_LICENSE_RESTRICTS       _HRESULT_TYPEDEF_(0xC00D11D3)
#define NS_E_WMP_INVALID_REQUEST         _HRESULT_TYPEDEF_(0xC00D11D4)
#define NS_E_WMP_CD_STASH_NO_SPACE       _HRESULT_TYPEDEF_(0xC00D11D5)
#define NS_E_WMP_DRM_NEW_HARDWARE        _HRESULT_TYPEDEF_(0xC00D11D6)
#define NS_E_WMP_DRM_INVALID_SIG         _HRESULT_TYPEDEF_(0xC00D11D7)
#define NS_E_WMP_DRM_CANNOT_RESTORE      _HRESULT_TYPEDEF_(0xC00D11D8)
#define NS_E_WMP_BURN_DISC_OVERFLOW      _HRESULT_TYPEDEF_(0xC00D11D9)
#define NS_E_WMP_DRM_GENERIC_LICENSE_FAILURE _HRESULT_TYPEDEF_(0xC00D11DA)
#define NS_E_WMP_DRM_NO_SECURE_CLOCK     _HRESULT_TYPEDEF_(0xC00D11DB)
#define NS_E_WMP_DRM_NO_RIGHTS           _HRESULT_TYPEDEF_(0xC00D11DC)
#define NS_E_WMP_DRM_INDIV_FAILED        _HRESULT_TYPEDEF_(0xC00D11DD)
#define NS_E_WMP_SERVER_NONEWCONNECTIONS _HRESULT_TYPEDEF_(0xC00D11DE)
#define NS_E_WMP_MULTIPLE_ERROR_IN_PLAYLIST _HRESULT_TYPEDEF_(0xC00D11DF)
#define NS_E_WMP_IMAPI2_ERASE_FAIL       _HRESULT_TYPEDEF_(0xC00D11E0)
#define NS_E_WMP_IMAPI2_ERASE_DEVICE_BUSY _HRESULT_TYPEDEF_(0xC00D11E1)
#define NS_E_WMP_DRM_COMPONENT_FAILURE   _HRESULT_TYPEDEF_(0xC00D11E2)
#define NS_E_WMP_DRM_NO_DEVICE_CERT      _HRESULT_TYPEDEF_(0xC00D11E3)
#define NS_E_WMP_SERVER_SECURITY_ERROR   _HRESULT_TYPEDEF_(0xC00D11E4)
#define NS_E_WMP_AUDIO_DEVICE_LOST       _HRESULT_TYPEDEF_(0xC00D11E5)
#define NS_E_WMP_IMAPI_MEDIA_INCOMPATIBLE _HRESULT_TYPEDEF_(0xC00D11E6)
#define NS_E_SYNCWIZ_DEVICE_FULL         _HRESULT_TYPEDEF_(0xC00D11EE)
#define NS_E_SYNCWIZ_CANNOT_CHANGE_SETTINGS _HRESULT_TYPEDEF_(0xC00D11EF)
#define NS_E_TRANSCODE_DELETECACHEERROR  _HRESULT_TYPEDEF_(0xC00D11F0)
#define NS_E_CD_NO_BUFFERS_READ          _HRESULT_TYPEDEF_(0xC00D11F8)
#define NS_E_CD_EMPTY_TRACK_QUEUE        _HRESULT_TYPEDEF_(0xC00D11F9)
#define NS_E_CD_NO_READER                _HRESULT_TYPEDEF_(0xC00D11FA)
#define NS_E_CD_ISRC_INVALID             _HRESULT_TYPEDEF_(0xC00D11FB)
#define NS_E_CD_MEDIA_CATALOG_NUMBER_INVALID _HRESULT_TYPEDEF_(0xC00D11FC)
#define NS_E_SLOW_READ_DIGITAL_WITH_ERRORCORRECTION _HRESULT_TYPEDEF_(0xC00D11FD)
#define NS_E_CD_SPEEDDETECT_NOT_ENOUGH_READS _HRESULT_TYPEDEF_(0xC00D11FE)
#define NS_E_CD_QUEUEING_DISABLED        _HRESULT_TYPEDEF_(0xC00D11FF)
#define NS_E_WMP_DRM_ACQUIRING_LICENSE   _HRESULT_TYPEDEF_(0xC00D1202)
#define NS_E_WMP_DRM_LICENSE_EXPIRED     _HRESULT_TYPEDEF_(0xC00D1203)
#define NS_E_WMP_DRM_LICENSE_NOTACQUIRED _HRESULT_TYPEDEF_(0xC00D1204)
#define NS_E_WMP_DRM_LICENSE_NOTENABLED  _HRESULT_TYPEDEF_(0xC00D1205)
#define NS_E_WMP_DRM_LICENSE_UNUSABLE    _HRESULT_TYPEDEF_(0xC00D1206)
#define NS_E_WMP_DRM_LICENSE_CONTENT_REVOKED _HRESULT_TYPEDEF_(0xC00D1207)
#define NS_E_WMP_DRM_LICENSE_NOSAP       _HRESULT_TYPEDEF_(0xC00D1208)
#define NS_E_WMP_DRM_UNABLE_TO_ACQUIRE_LICENSE _HRESULT_TYPEDEF_(0xC00D1209)
#define NS_E_WMP_LICENSE_REQUIRED        _HRESULT_TYPEDEF_(0xC00D120A)
#define NS_E_WMP_PROTECTED_CONTENT       _HRESULT_TYPEDEF_(0xC00D120B)
#define NS_E_WMP_POLICY_VALUE_NOT_CONFIGURED _HRESULT_TYPEDEF_(0xC00D122A)
#define NS_E_PDA_CANNOT_SYNC_FROM_INTERNET _HRESULT_TYPEDEF_(0xC00D1234)
#define NS_E_PDA_CANNOT_SYNC_INVALID_PLAYLIST _HRESULT_TYPEDEF_(0xC00D1235)
#define NS_E_PDA_FAILED_TO_SYNCHRONIZE_FILE _HRESULT_TYPEDEF_(0xC00D1236)
#define NS_E_PDA_SYNC_FAILED             _HRESULT_TYPEDEF_(0xC00D1237)
#define NS_E_PDA_DELETE_FAILED           _HRESULT_TYPEDEF_(0xC00D1238)
#define NS_E_PDA_FAILED_TO_RETRIEVE_FILE _HRESULT_TYPEDEF_(0xC00D1239)
#define NS_E_PDA_DEVICE_NOT_RESPONDING   _HRESULT_TYPEDEF_(0xC00D123A)
#define NS_E_PDA_FAILED_TO_TRANSCODE_PHOTO _HRESULT_TYPEDEF_(0xC00D123B)
#define NS_E_PDA_FAILED_TO_ENCRYPT_TRANSCODED_FILE _HRESULT_TYPEDEF_(0xC00D123C)
#define NS_E_PDA_CANNOT_TRANSCODE_TO_AUDIO _HRESULT_TYPEDEF_(0xC00D123D)
#define NS_E_PDA_CANNOT_TRANSCODE_TO_VIDEO _HRESULT_TYPEDEF_(0xC00D123E)
#define NS_E_PDA_CANNOT_TRANSCODE_TO_IMAGE _HRESULT_TYPEDEF_(0xC00D123F)
#define NS_E_PDA_RETRIEVED_FILE_FILENAME_TOO_LONG _HRESULT_TYPEDEF_(0xC00D1240)
#define NS_E_PDA_CEWMDM_DRM_ERROR        _HRESULT_TYPEDEF_(0xC00D1241)
#define NS_E_INCOMPLETE_PLAYLIST         _HRESULT_TYPEDEF_(0xC00D1242)
#define NS_E_PDA_SYNC_RUNNING            _HRESULT_TYPEDEF_(0xC00D1243)
#define NS_E_PDA_SYNC_LOGIN_ERROR        _HRESULT_TYPEDEF_(0xC00D1244)
#define NS_E_PDA_TRANSCODE_CODEC_NOT_FOUND _HRESULT_TYPEDEF_(0xC00D1245)
#define NS_E_CANNOT_SYNC_DRM_TO_NON_JANUS_DEVICE _HRESULT_TYPEDEF_(0xC00D1246)
#define NS_E_CANNOT_SYNC_PREVIOUS_SYNC_RUNNING _HRESULT_TYPEDEF_(0xC00D1247)
#define NS_E_WMP_HWND_NOTFOUND           _HRESULT_TYPEDEF_(0xC00D125C)
#define NS_E_BKGDOWNLOAD_WRONG_NO_FILES  _HRESULT_TYPEDEF_(0xC00D125D)
#define NS_E_BKGDOWNLOAD_COMPLETECANCELLEDJOB _HRESULT_TYPEDEF_(0xC00D125E)
#define NS_E_BKGDOWNLOAD_CANCELCOMPLETEDJOB _HRESULT_TYPEDEF_(0xC00D125F)
#define NS_E_BKGDOWNLOAD_NOJOBPOINTER    _HRESULT_TYPEDEF_(0xC00D1260)
#define NS_E_BKGDOWNLOAD_INVALIDJOBSIGNATURE _HRESULT_TYPEDEF_(0xC00D1261)
#define NS_E_BKGDOWNLOAD_FAILED_TO_CREATE_TEMPFILE _HRESULT_TYPEDEF_(0xC00D1262)
#define NS_E_BKGDOWNLOAD_PLUGIN_FAILEDINITIALIZE _HRESULT_TYPEDEF_(0xC00D1263)
#define NS_E_BKGDOWNLOAD_PLUGIN_FAILEDTOMOVEFILE _HRESULT_TYPEDEF_(0xC00D1264)
#define NS_E_BKGDOWNLOAD_CALLFUNCFAILED  _HRESULT_TYPEDEF_(0xC00D1265)
#define NS_E_BKGDOWNLOAD_CALLFUNCTIMEOUT _HRESULT_TYPEDEF_(0xC00D1266)
#define NS_E_BKGDOWNLOAD_CALLFUNCENDED   _HRESULT_TYPEDEF_(0xC00D1267)
#define NS_E_BKGDOWNLOAD_WMDUNPACKFAILED _HRESULT_TYPEDEF_(0xC00D1268)
#define NS_E_BKGDOWNLOAD_FAILEDINITIALIZE _HRESULT_TYPEDEF_(0xC00D1269)
#define NS_E_INTERFACE_NOT_REGISTERED_IN_GIT _HRESULT_TYPEDEF_(0xC00D126A)
#define NS_E_BKGDOWNLOAD_INVALID_FILE_NAME _HRESULT_TYPEDEF_(0xC00D126B)
#define NS_E_IMAGE_DOWNLOAD_FAILED       _HRESULT_TYPEDEF_(0xC00D128E)
#define NS_E_WMP_UDRM_NOUSERLIST         _HRESULT_TYPEDEF_(0xC00D12C0)
#define NS_E_WMP_DRM_NOT_ACQUIRING       _HRESULT_TYPEDEF_(0xC00D12C1)
#define NS_E_WMP_BSTR_TOO_LONG           _HRESULT_TYPEDEF_(0xC00D12F2)
#define NS_E_WMP_AUTOPLAY_INVALID_STATE  _HRESULT_TYPEDEF_(0xC00D12FC)
#define NS_E_WMP_COMPONENT_REVOKED       _HRESULT_TYPEDEF_(0xC00D1306)
#define NS_E_CURL_NOTSAFE                _HRESULT_TYPEDEF_(0xC00D1324)
#define NS_E_CURL_INVALIDCHAR            _HRESULT_TYPEDEF_(0xC00D1325)
#define NS_E_CURL_INVALIDHOSTNAME        _HRESULT_TYPEDEF_(0xC00D1326)
#define NS_E_CURL_INVALIDPATH            _HRESULT_TYPEDEF_(0xC00D1327)
#define NS_E_CURL_INVALIDSCHEME          _HRESULT_TYPEDEF_(0xC00D1328)
#define NS_E_CURL_INVALIDURL             _HRESULT_TYPEDEF_(0xC00D1329)
#define NS_E_CURL_CANTWALK               _HRESULT_TYPEDEF_(0xC00D132B)
#define NS_E_CURL_INVALIDPORT            _HRESULT_TYPEDEF_(0xC00D132C)
#define NS_E_CURLHELPER_NOTADIRECTORY    _HRESULT_TYPEDEF_(0xC00D132D)
#define NS_E_CURLHELPER_NOTAFILE         _HRESULT_TYPEDEF_(0xC00D132E)
#define NS_E_CURL_CANTDECODE             _HRESULT_TYPEDEF_(0xC00D132F)
#define NS_E_CURLHELPER_NOTRELATIVE      _HRESULT_TYPEDEF_(0xC00D1330)
#define NS_E_CURL_INVALIDBUFFERSIZE      _HRESULT_TYPEDEF_(0xC00D1331)
#define NS_E_SUBSCRIPTIONSERVICE_PLAYBACK_DISALLOWED _HRESULT_TYPEDEF_(0xC00D1356)
#define NS_E_CANNOT_BUY_OR_DOWNLOAD_FROM_MULTIPLE_SERVICES _HRESULT_TYPEDEF_(0xC00D1357)
#define NS_E_CANNOT_BUY_OR_DOWNLOAD_CONTENT _HRESULT_TYPEDEF_(0xC00D1358)
#define NS_S_TRACK_BUY_REQUIRES_ALBUM_PURCHASE _HRESULT_TYPEDEF_(0x000D1359)
#define NS_E_NOT_CONTENT_PARTNER_TRACK   _HRESULT_TYPEDEF_(0xC00D135A)
#define NS_E_TRACK_DOWNLOAD_REQUIRES_ALBUM_PURCHASE _HRESULT_TYPEDEF_(0xC00D135B)
#define NS_E_TRACK_DOWNLOAD_REQUIRES_PURCHASE _HRESULT_TYPEDEF_(0xC00D135C)
#define NS_E_TRACK_PURCHASE_MAXIMUM_EXCEEDED _HRESULT_TYPEDEF_(0xC00D135D)
#define NS_S_NAVIGATION_COMPLETE_WITH_ERRORS _HRESULT_TYPEDEF_(0x000D135E)
#define NS_E_SUBSCRIPTIONSERVICE_LOGIN_FAILED _HRESULT_TYPEDEF_(0xC00D135F)
#define NS_E_SUBSCRIPTIONSERVICE_DOWNLOAD_TIMEOUT _HRESULT_TYPEDEF_(0xC00D1360)
#define NS_S_TRACK_ALREADY_DOWNLOADED    _HRESULT_TYPEDEF_(0x000D1361)
#define NS_E_CONTENT_PARTNER_STILL_INITIALIZING _HRESULT_TYPEDEF_(0xC00D1362)
#define NS_E_OPEN_CONTAINING_FOLDER_FAILED _HRESULT_TYPEDEF_(0xC00D1363)
#define NS_E_ADVANCEDEDIT_TOO_MANY_PICTURES _HRESULT_TYPEDEF_(0xC00D136A)
#define NS_E_REDIRECT                    _HRESULT_TYPEDEF_(0xC00D1388)
#define NS_E_STALE_PRESENTATION          _HRESULT_TYPEDEF_(0xC00D1389)

#define NS_E_NAMESPACE_WRONG_PERSIST     _HRESULT_TYPEDEF_(0xC00D138A)
#define NS_E_NAMESPACE_WRONG_TYPE        _HRESULT_TYPEDEF_(0xC00D138B)
#define NS_E_NAMESPACE_NODE_CONFLICT     _HRESULT_TYPEDEF_(0xC00D138C)
#define NS_E_NAMESPACE_NODE_NOT_FOUND    _HRESULT_TYPEDEF_(0xC00D138D)
#define NS_E_NAMESPACE_BUFFER_TOO_SMALL  _HRESULT_TYPEDEF_(0xC00D138E)
#define NS_E_NAMESPACE_TOO_MANY_CALLBACKS _HRESULT_TYPEDEF_(0xC00D138F)
#define NS_E_NAMESPACE_DUPLICATE_CALLBACK _HRESULT_TYPEDEF_(0xC00D1390)
#define NS_E_NAMESPACE_CALLBACK_NOT_FOUND _HRESULT_TYPEDEF_(0xC00D1391)
#define NS_E_NAMESPACE_NAME_TOO_LONG     _HRESULT_TYPEDEF_(0xC00D1392)
#define NS_E_NAMESPACE_DUPLICATE_NAME    _HRESULT_TYPEDEF_(0xC00D1393)
#define NS_E_NAMESPACE_EMPTY_NAME        _HRESULT_TYPEDEF_(0xC00D1394)
#define NS_E_NAMESPACE_INDEX_TOO_LARGE   _HRESULT_TYPEDEF_(0xC00D1395)
#define NS_E_NAMESPACE_BAD_NAME          _HRESULT_TYPEDEF_(0xC00D1396)
#define NS_E_NAMESPACE_WRONG_SECURITY    _HRESULT_TYPEDEF_(0xC00D1397)

#define NS_E_CACHE_ARCHIVE_CONFLICT      _HRESULT_TYPEDEF_(0xC00D13EC)
#define NS_E_CACHE_ORIGIN_SERVER_NOT_FOUND _HRESULT_TYPEDEF_(0xC00D13ED)
#define NS_E_CACHE_ORIGIN_SERVER_TIMEOUT _HRESULT_TYPEDEF_(0xC00D13EE)
#define NS_E_CACHE_NOT_BROADCAST         _HRESULT_TYPEDEF_(0xC00D13EF)
#define NS_E_CACHE_CANNOT_BE_CACHED      _HRESULT_TYPEDEF_(0xC00D13F0)
#define NS_E_CACHE_NOT_MODIFIED          _HRESULT_TYPEDEF_(0xC00D13F1)
#define NS_E_CANNOT_REMOVE_PUBLISHING_POINT _HRESULT_TYPEDEF_(0xC00D1450)
#define NS_E_CANNOT_REMOVE_PLUGIN        _HRESULT_TYPEDEF_(0xC00D1451)
#define NS_E_WRONG_PUBLISHING_POINT_TYPE _HRESULT_TYPEDEF_(0xC00D1452)
#define NS_E_UNSUPPORTED_LOAD_TYPE       _HRESULT_TYPEDEF_(0xC00D1453)
#define NS_E_INVALID_PLUGIN_LOAD_TYPE_CONFIGURATION _HRESULT_TYPEDEF_(0xC00D1454)
#define NS_E_INVALID_PUBLISHING_POINT_NAME _HRESULT_TYPEDEF_(0xC00D1455)
#define NS_E_TOO_MANY_MULTICAST_SINKS    _HRESULT_TYPEDEF_(0xC00D1456)
#define NS_E_PUBLISHING_POINT_INVALID_REQUEST_WHILE_STARTED _HRESULT_TYPEDEF_(0xC00D1457)
#define NS_E_MULTICAST_PLUGIN_NOT_ENABLED _HRESULT_TYPEDEF_(0xC00D1458)
#define NS_E_INVALID_OPERATING_SYSTEM_VERSION _HRESULT_TYPEDEF_(0xC00D1459)
#define NS_E_PUBLISHING_POINT_REMOVED    _HRESULT_TYPEDEF_(0xC00D145A)
#define NS_E_INVALID_PUSH_PUBLISHING_POINT_START_REQUEST _HRESULT_TYPEDEF_(0xC00D145B)
#define NS_E_UNSUPPORTED_LANGUAGE        _HRESULT_TYPEDEF_(0xC00D145C)
#define NS_E_WRONG_OS_VERSION            _HRESULT_TYPEDEF_(0xC00D145D)
#define NS_E_PUBLISHING_POINT_STOPPED    _HRESULT_TYPEDEF_(0xC00D145E)
#define NS_E_PLAYLIST_ENTRY_ALREADY_PLAYING _HRESULT_TYPEDEF_(0xC00D14B4)
#define NS_E_EMPTY_PLAYLIST              _HRESULT_TYPEDEF_(0xC00D14B5)
#define NS_E_PLAYLIST_PARSE_FAILURE      _HRESULT_TYPEDEF_(0xC00D14B6)
#define NS_E_PLAYLIST_UNSUPPORTED_ENTRY  _HRESULT_TYPEDEF_(0xC00D14B7)
#define NS_E_PLAYLIST_ENTRY_NOT_IN_PLAYLIST _HRESULT_TYPEDEF_(0xC00D14B8)
#define NS_E_PLAYLIST_ENTRY_SEEK         _HRESULT_TYPEDEF_(0xC00D14B9)
#define NS_E_PLAYLIST_RECURSIVE_PLAYLISTS _HRESULT_TYPEDEF_(0xC00D14BA)
#define NS_E_PLAYLIST_TOO_MANY_NESTED_PLAYLISTS _HRESULT_TYPEDEF_(0xC00D14BB)
#define NS_E_PLAYLIST_SHUTDOWN           _HRESULT_TYPEDEF_(0xC00D14BC)
#define NS_E_PLAYLIST_END_RECEDING       _HRESULT_TYPEDEF_(0xC00D14BD)
#define NS_I_PLAYLIST_CHANGE_RECEDING    _HRESULT_TYPEDEF_(0x400D14BE)
#define NS_E_DATAPATH_NO_SINK            _HRESULT_TYPEDEF_(0xC00D1518)
#define NS_S_PUBLISHING_POINT_STARTED_WITH_FAILED_SINKS _HRESULT_TYPEDEF_(0x000D1519)
#define NS_E_INVALID_PUSH_TEMPLATE       _HRESULT_TYPEDEF_(0xC00D151A)
#define NS_E_INVALID_PUSH_PUBLISHING_POINT _HRESULT_TYPEDEF_(0xC00D151B)
#define NS_E_CRITICAL_ERROR              _HRESULT_TYPEDEF_(0xC00D151C)
#define NS_E_NO_NEW_CONNECTIONS          _HRESULT_TYPEDEF_(0xC00D151D)
#define NS_E_WSX_INVALID_VERSION         _HRESULT_TYPEDEF_(0xC00D151E)
#define NS_E_HEADER_MISMATCH             _HRESULT_TYPEDEF_(0xC00D151F)
#define NS_E_PUSH_DUPLICATE_PUBLISHING_POINT_NAME _HRESULT_TYPEDEF_(0xC00D1520)
#define NS_E_NO_SCRIPT_ENGINE            _HRESULT_TYPEDEF_(0xC00D157C)
#define NS_E_PLUGIN_ERROR_REPORTED       _HRESULT_TYPEDEF_(0xC00D157D)
#define NS_E_SOURCE_PLUGIN_NOT_FOUND     _HRESULT_TYPEDEF_(0xC00D157E)
#define NS_E_PLAYLIST_PLUGIN_NOT_FOUND   _HRESULT_TYPEDEF_(0xC00D157F)
#define NS_E_DATA_SOURCE_ENUMERATION_NOT_SUPPORTED _HRESULT_TYPEDEF_(0xC00D1580)
#define NS_E_MEDIA_PARSER_INVALID_FORMAT _HRESULT_TYPEDEF_(0xC00D1581)
#define NS_E_SCRIPT_DEBUGGER_NOT_INSTALLED _HRESULT_TYPEDEF_(0xC00D1582)
#define NS_E_FEATURE_REQUIRES_ENTERPRISE_SERVER _HRESULT_TYPEDEF_(0xC00D1583)
#define NS_E_WIZARD_RUNNING              _HRESULT_TYPEDEF_(0xC00D1584)
#define NS_E_INVALID_LOG_URL             _HRESULT_TYPEDEF_(0xC00D1585)
#define NS_E_INVALID_MTU_RANGE           _HRESULT_TYPEDEF_(0xC00D1586)
#define NS_E_INVALID_PLAY_STATISTICS     _HRESULT_TYPEDEF_(0xC00D1587)
#define NS_E_LOG_NEED_TO_BE_SKIPPED      _HRESULT_TYPEDEF_(0xC00D1588)
#define NS_E_HTTP_TEXT_DATACONTAINER_SIZE_LIMIT_EXCEEDED _HRESULT_TYPEDEF_(0xC00D1589)
#define NS_E_PORT_IN_USE                 _HRESULT_TYPEDEF_(0xC00D158A)
#define NS_E_PORT_IN_USE_HTTP            _HRESULT_TYPEDEF_(0xC00D158B)
#define NS_E_HTTP_TEXT_DATACONTAINER_INVALID_SERVER_RESPONSE _HRESULT_TYPEDEF_(0xC00D158C)
#define NS_E_ARCHIVE_REACH_QUOTA         _HRESULT_TYPEDEF_(0xC00D158D)
#define NS_E_ARCHIVE_ABORT_DUE_TO_BCAST  _HRESULT_TYPEDEF_(0xC00D158E)
#define NS_E_ARCHIVE_GAP_DETECTED        _HRESULT_TYPEDEF_(0xC00D158F)
#define NS_E_AUTHORIZATION_FILE_NOT_FOUND _HRESULT_TYPEDEF_(0xC00D1590)
#define NS_E_BAD_MARKIN                  _HRESULT_TYPEDEF_(0xC00D1B58)
#define NS_E_BAD_MARKOUT                 _HRESULT_TYPEDEF_(0xC00D1B59)
#define NS_E_NOMATCHING_MEDIASOURCE      _HRESULT_TYPEDEF_(0xC00D1B5A)
#define NS_E_UNSUPPORTED_SOURCETYPE      _HRESULT_TYPEDEF_(0xC00D1B5B)
#define NS_E_TOO_MANY_AUDIO              _HRESULT_TYPEDEF_(0xC00D1B5C)
#define NS_E_TOO_MANY_VIDEO              _HRESULT_TYPEDEF_(0xC00D1B5D)
#define NS_E_NOMATCHING_ELEMENT          _HRESULT_TYPEDEF_(0xC00D1B5E)
#define NS_E_MISMATCHED_MEDIACONTENT     _HRESULT_TYPEDEF_(0xC00D1B5F)
#define NS_E_CANNOT_DELETE_ACTIVE_SOURCEGROUP _HRESULT_TYPEDEF_(0xC00D1B60)
#define NS_E_AUDIODEVICE_BUSY            _HRESULT_TYPEDEF_(0xC00D1B61)
#define NS_E_AUDIODEVICE_UNEXPECTED      _HRESULT_TYPEDEF_(0xC00D1B62)
#define NS_E_AUDIODEVICE_BADFORMAT       _HRESULT_TYPEDEF_(0xC00D1B63)
#define NS_E_VIDEODEVICE_BUSY            _HRESULT_TYPEDEF_(0xC00D1B64)
#define NS_E_VIDEODEVICE_UNEXPECTED      _HRESULT_TYPEDEF_(0xC00D1B65)
#define NS_E_INVALIDCALL_WHILE_ENCODER_RUNNING _HRESULT_TYPEDEF_(0xC00D1B66)
#define NS_E_NO_PROFILE_IN_SOURCEGROUP   _HRESULT_TYPEDEF_(0xC00D1B67)
#define NS_E_VIDEODRIVER_UNSTABLE        _HRESULT_TYPEDEF_(0xC00D1B68)
#define NS_E_VIDCAPSTARTFAILED           _HRESULT_TYPEDEF_(0xC00D1B69)
#define NS_E_VIDSOURCECOMPRESSION        _HRESULT_TYPEDEF_(0xC00D1B6A)
#define NS_E_VIDSOURCESIZE               _HRESULT_TYPEDEF_(0xC00D1B6B)
#define NS_E_ICMQUERYFORMAT              _HRESULT_TYPEDEF_(0xC00D1B6C)
#define NS_E_VIDCAPCREATEWINDOW          _HRESULT_TYPEDEF_(0xC00D1B6D)
#define NS_E_VIDCAPDRVINUSE              _HRESULT_TYPEDEF_(0xC00D1B6E)
#define NS_E_NO_MEDIAFORMAT_IN_SOURCE    _HRESULT_TYPEDEF_(0xC00D1B6F)
#define NS_E_NO_VALID_OUTPUT_STREAM      _HRESULT_TYPEDEF_(0xC00D1B70)
#define NS_E_NO_VALID_SOURCE_PLUGIN      _HRESULT_TYPEDEF_(0xC00D1B71)
#define NS_E_NO_ACTIVE_SOURCEGROUP       _HRESULT_TYPEDEF_(0xC00D1B72)
#define NS_E_NO_SCRIPT_STREAM            _HRESULT_TYPEDEF_(0xC00D1B73)
#define NS_E_INVALIDCALL_WHILE_ARCHIVAL_RUNNING _HRESULT_TYPEDEF_(0xC00D1B74)
#define NS_E_INVALIDPACKETSIZE           _HRESULT_TYPEDEF_(0xC00D1B75)
#define NS_E_PLUGIN_CLSID_INVALID        _HRESULT_TYPEDEF_(0xC00D1B76)
#define NS_E_UNSUPPORTED_ARCHIVETYPE     _HRESULT_TYPEDEF_(0xC00D1B77)
#define NS_E_UNSUPPORTED_ARCHIVEOPERATION _HRESULT_TYPEDEF_(0xC00D1B78)
#define NS_E_ARCHIVE_FILENAME_NOTSET     _HRESULT_TYPEDEF_(0xC00D1B79)
#define NS_E_SOURCEGROUP_NOTPREPARED     _HRESULT_TYPEDEF_(0xC00D1B7A)
#define NS_E_PROFILE_MISMATCH            _HRESULT_TYPEDEF_(0xC00D1B7B)
#define NS_E_INCORRECTCLIPSETTINGS       _HRESULT_TYPEDEF_(0xC00D1B7C)
#define NS_E_NOSTATSAVAILABLE            _HRESULT_TYPEDEF_(0xC00D1B7D)
#define NS_E_NOTARCHIVING                _HRESULT_TYPEDEF_(0xC00D1B7E)
#define NS_E_INVALIDCALL_WHILE_ENCODER_STOPPED _HRESULT_TYPEDEF_(0xC00D1B7F)
#define NS_E_NOSOURCEGROUPS              _HRESULT_TYPEDEF_(0xC00D1B80)
#define NS_E_INVALIDINPUTFPS             _HRESULT_TYPEDEF_(0xC00D1B81)
#define NS_E_NO_DATAVIEW_SUPPORT         _HRESULT_TYPEDEF_(0xC00D1B82)
#define NS_E_CODEC_UNAVAILABLE           _HRESULT_TYPEDEF_(0xC00D1B83)
#define NS_E_ARCHIVE_SAME_AS_INPUT       _HRESULT_TYPEDEF_(0xC00D1B84)
#define NS_E_SOURCE_NOTSPECIFIED         _HRESULT_TYPEDEF_(0xC00D1B85)
#define NS_E_NO_REALTIME_TIMECOMPRESSION _HRESULT_TYPEDEF_(0xC00D1B86)
#define NS_E_UNSUPPORTED_ENCODER_DEVICE  _HRESULT_TYPEDEF_(0xC00D1B87)
#define NS_E_UNEXPECTED_DISPLAY_SETTINGS _HRESULT_TYPEDEF_(0xC00D1B88)
#define NS_E_NO_AUDIODATA                _HRESULT_TYPEDEF_(0xC00D1B89)
#define NS_E_INPUTSOURCE_PROBLEM         _HRESULT_TYPEDEF_(0xC00D1B8A)
#define NS_E_WME_VERSION_MISMATCH        _HRESULT_TYPEDEF_(0xC00D1B8B)
#define NS_E_NO_REALTIME_PREPROCESS      _HRESULT_TYPEDEF_(0xC00D1B8C)
#define NS_E_NO_REPEAT_PREPROCESS        _HRESULT_TYPEDEF_(0xC00D1B8D)
#define NS_E_CANNOT_PAUSE_LIVEBROADCAST  _HRESULT_TYPEDEF_(0xC00D1B8E)
#define NS_E_DRM_PROFILE_NOT_SET         _HRESULT_TYPEDEF_(0xC00D1B8F)
#define NS_E_DUPLICATE_DRMPROFILE        _HRESULT_TYPEDEF_(0xC00D1B90)
#define NS_E_INVALID_DEVICE              _HRESULT_TYPEDEF_(0xC00D1B91)
#define NS_E_SPEECHEDL_ON_NON_MIXEDMODE  _HRESULT_TYPEDEF_(0xC00D1B92)
#define NS_E_DRM_PASSWORD_TOO_LONG       _HRESULT_TYPEDEF_(0xC00D1B93)
#define NS_E_DEVCONTROL_FAILED_SEEK      _HRESULT_TYPEDEF_(0xC00D1B94)
#define NS_E_INTERLACE_REQUIRE_SAMESIZE  _HRESULT_TYPEDEF_(0xC00D1B95)
#define NS_E_TOO_MANY_DEVICECONTROL      _HRESULT_TYPEDEF_(0xC00D1B96)
#define NS_E_NO_MULTIPASS_FOR_LIVEDEVICE _HRESULT_TYPEDEF_(0xC00D1B97)
#define NS_E_MISSING_AUDIENCE            _HRESULT_TYPEDEF_(0xC00D1B98)
#define NS_E_AUDIENCE_CONTENTTYPE_MISMATCH _HRESULT_TYPEDEF_(0xC00D1B99)
#define NS_E_MISSING_SOURCE_INDEX        _HRESULT_TYPEDEF_(0xC00D1B9A)
#define NS_E_NUM_LANGUAGE_MISMATCH       _HRESULT_TYPEDEF_(0xC00D1B9B)
#define NS_E_LANGUAGE_MISMATCH           _HRESULT_TYPEDEF_(0xC00D1B9C)
#define NS_E_VBRMODE_MISMATCH            _HRESULT_TYPEDEF_(0xC00D1B9D)
#define NS_E_INVALID_INPUT_AUDIENCE_INDEX _HRESULT_TYPEDEF_(0xC00D1B9E)
#define NS_E_INVALID_INPUT_LANGUAGE      _HRESULT_TYPEDEF_(0xC00D1B9F)
#define NS_E_INVALID_INPUT_STREAM        _HRESULT_TYPEDEF_(0xC00D1BA0)
#define NS_E_EXPECT_MONO_WAV_INPUT       _HRESULT_TYPEDEF_(0xC00D1BA1)
#define NS_E_INPUT_WAVFORMAT_MISMATCH    _HRESULT_TYPEDEF_(0xC00D1BA2)
#define NS_E_RECORDQ_DISK_FULL           _HRESULT_TYPEDEF_(0xC00D1BA3)
#define NS_E_NO_PAL_INVERSE_TELECINE     _HRESULT_TYPEDEF_(0xC00D1BA4)
#define NS_E_ACTIVE_SG_DEVICE_DISCONNECTED _HRESULT_TYPEDEF_(0xC00D1BA5)
#define NS_E_ACTIVE_SG_DEVICE_CONTROL_DISCONNECTED _HRESULT_TYPEDEF_(0xC00D1BA6)
#define NS_E_NO_FRAMES_SUBMITTED_TO_ANALYZER _HRESULT_TYPEDEF_(0xC00D1BA7)
#define NS_E_INPUT_DOESNOT_SUPPORT_SMPTE _HRESULT_TYPEDEF_(0xC00D1BA8)
#define NS_E_NO_SMPTE_WITH_MULTIPLE_SOURCEGROUPS _HRESULT_TYPEDEF_(0xC00D1BA9)
#define NS_E_BAD_CONTENTEDL              _HRESULT_TYPEDEF_(0xC00D1BAA)
#define NS_E_INTERLACEMODE_MISMATCH      _HRESULT_TYPEDEF_(0xC00D1BAB)
#define NS_E_NONSQUAREPIXELMODE_MISMATCH _HRESULT_TYPEDEF_(0xC00D1BAC)
#define NS_E_SMPTEMODE_MISMATCH          _HRESULT_TYPEDEF_(0xC00D1BAD)
#define NS_E_END_OF_TAPE                 _HRESULT_TYPEDEF_(0xC00D1BAE)
#define NS_E_NO_MEDIA_IN_AUDIENCE        _HRESULT_TYPEDEF_(0xC00D1BAF)
#define NS_E_NO_AUDIENCES                _HRESULT_TYPEDEF_(0xC00D1BB0)
#define NS_E_NO_AUDIO_COMPAT             _HRESULT_TYPEDEF_(0xC00D1BB1)
#define NS_E_INVALID_VBR_COMPAT          _HRESULT_TYPEDEF_(0xC00D1BB2)
#define NS_E_NO_PROFILE_NAME             _HRESULT_TYPEDEF_(0xC00D1BB3)
#define NS_E_INVALID_VBR_WITH_UNCOMP     _HRESULT_TYPEDEF_(0xC00D1BB4)
#define NS_E_MULTIPLE_VBR_AUDIENCES      _HRESULT_TYPEDEF_(0xC00D1BB5)
#define NS_E_UNCOMP_COMP_COMBINATION     _HRESULT_TYPEDEF_(0xC00D1BB6)
#define NS_E_MULTIPLE_AUDIO_CODECS       _HRESULT_TYPEDEF_(0xC00D1BB7)
#define NS_E_MULTIPLE_AUDIO_FORMATS      _HRESULT_TYPEDEF_(0xC00D1BB8)
#define NS_E_AUDIO_BITRATE_STEPDOWN      _HRESULT_TYPEDEF_(0xC00D1BB9)
#define NS_E_INVALID_AUDIO_PEAKRATE      _HRESULT_TYPEDEF_(0xC00D1BBA)
#define NS_E_INVALID_AUDIO_PEAKRATE_2    _HRESULT_TYPEDEF_(0xC00D1BBB)
#define NS_E_INVALID_AUDIO_BUFFERMAX     _HRESULT_TYPEDEF_(0xC00D1BBC)
#define NS_E_MULTIPLE_VIDEO_CODECS       _HRESULT_TYPEDEF_(0xC00D1BBD)
#define NS_E_MULTIPLE_VIDEO_SIZES        _HRESULT_TYPEDEF_(0xC00D1BBE)
#define NS_E_INVALID_VIDEO_BITRATE       _HRESULT_TYPEDEF_(0xC00D1BBF)
#define NS_E_VIDEO_BITRATE_STEPDOWN      _HRESULT_TYPEDEF_(0xC00D1BC0)
#define NS_E_INVALID_VIDEO_PEAKRATE      _HRESULT_TYPEDEF_(0xC00D1BC1)
#define NS_E_INVALID_VIDEO_PEAKRATE_2    _HRESULT_TYPEDEF_(0xC00D1BC2)
#define NS_E_INVALID_VIDEO_WIDTH         _HRESULT_TYPEDEF_(0xC00D1BC3)
#define NS_E_INVALID_VIDEO_HEIGHT        _HRESULT_TYPEDEF_(0xC00D1BC4)
#define NS_E_INVALID_VIDEO_FPS           _HRESULT_TYPEDEF_(0xC00D1BC5)
#define NS_E_INVALID_VIDEO_KEYFRAME      _HRESULT_TYPEDEF_(0xC00D1BC6)
#define NS_E_INVALID_VIDEO_IQUALITY      _HRESULT_TYPEDEF_(0xC00D1BC7)
#define NS_E_INVALID_VIDEO_CQUALITY      _HRESULT_TYPEDEF_(0xC00D1BC8)
#define NS_E_INVALID_VIDEO_BUFFER        _HRESULT_TYPEDEF_(0xC00D1BC9)
#define NS_E_INVALID_VIDEO_BUFFERMAX     _HRESULT_TYPEDEF_(0xC00D1BCA)
#define NS_E_INVALID_VIDEO_BUFFERMAX_2   _HRESULT_TYPEDEF_(0xC00D1BCB)
#define NS_E_INVALID_VIDEO_WIDTH_ALIGN   _HRESULT_TYPEDEF_(0xC00D1BCC)
#define NS_E_INVALID_VIDEO_HEIGHT_ALIGN  _HRESULT_TYPEDEF_(0xC00D1BCD)
#define NS_E_MULTIPLE_SCRIPT_BITRATES    _HRESULT_TYPEDEF_(0xC00D1BCE)
#define NS_E_INVALID_SCRIPT_BITRATE      _HRESULT_TYPEDEF_(0xC00D1BCF)
#define NS_E_MULTIPLE_FILE_BITRATES      _HRESULT_TYPEDEF_(0xC00D1BD0)
#define NS_E_INVALID_FILE_BITRATE        _HRESULT_TYPEDEF_(0xC00D1BD1)
#define NS_E_SAME_AS_INPUT_COMBINATION   _HRESULT_TYPEDEF_(0xC00D1BD2)
#define NS_E_SOURCE_CANNOT_LOOP          _HRESULT_TYPEDEF_(0xC00D1BD3)
#define NS_E_INVALID_FOLDDOWN_COEFFICIENTS _HRESULT_TYPEDEF_(0xC00D1BD4)
#define NS_E_DRMPROFILE_NOTFOUND         _HRESULT_TYPEDEF_(0xC00D1BD5)
#define NS_E_INVALID_TIMECODE            _HRESULT_TYPEDEF_(0xC00D1BD6)
#define NS_E_NO_AUDIO_TIMECOMPRESSION    _HRESULT_TYPEDEF_(0xC00D1BD7)
#define NS_E_NO_TWOPASS_TIMECOMPRESSION  _HRESULT_TYPEDEF_(0xC00D1BD8)
#define NS_E_TIMECODE_REQUIRES_VIDEOSTREAM _HRESULT_TYPEDEF_(0xC00D1BD9)
#define NS_E_NO_MBR_WITH_TIMECODE        _HRESULT_TYPEDEF_(0xC00D1BDA)
#define NS_E_INVALID_INTERLACEMODE       _HRESULT_TYPEDEF_(0xC00D1BDB)
#define NS_E_INVALID_INTERLACE_COMPAT    _HRESULT_TYPEDEF_(0xC00D1BDC)
#define NS_E_INVALID_NONSQUAREPIXEL_COMPAT _HRESULT_TYPEDEF_(0xC00D1BDD)
#define NS_E_INVALID_SOURCE_WITH_DEVICE_CONTROL _HRESULT_TYPEDEF_(0xC00D1BDE)
#define NS_E_CANNOT_GENERATE_BROADCAST_INFO_FOR_QUALITYVBR _HRESULT_TYPEDEF_(0xC00D1BDF)
#define NS_E_EXCEED_MAX_DRM_PROFILE_LIMIT _HRESULT_TYPEDEF_(0xC00D1BE0)
#define NS_E_DEVICECONTROL_UNSTABLE      _HRESULT_TYPEDEF_(0xC00D1BE1)
#define NS_E_INVALID_PIXEL_ASPECT_RATIO  _HRESULT_TYPEDEF_(0xC00D1BE2)
#define NS_E_AUDIENCE__LANGUAGE_CONTENTTYPE_MISMATCH _HRESULT_TYPEDEF_(0xC00D1BE3)
#define NS_E_INVALID_PROFILE_CONTENTTYPE _HRESULT_TYPEDEF_(0xC00D1BE4)
#define NS_E_TRANSFORM_PLUGIN_NOT_FOUND  _HRESULT_TYPEDEF_(0xC00D1BE5)
#define NS_E_TRANSFORM_PLUGIN_INVALID    _HRESULT_TYPEDEF_(0xC00D1BE6)
#define NS_E_EDL_REQUIRED_FOR_DEVICE_MULTIPASS _HRESULT_TYPEDEF_(0xC00D1BE7)
#define NS_E_INVALID_VIDEO_WIDTH_FOR_INTERLACED_ENCODING _HRESULT_TYPEDEF_(0xC00D1BE8)
#define NS_E_MARKIN_UNSUPPORTED          _HRESULT_TYPEDEF_(0xC00D1BE9)
#define NS_E_DRM_INVALID_APPLICATION     _HRESULT_TYPEDEF_(0xC00D2711)
#define NS_E_DRM_LICENSE_STORE_ERROR     _HRESULT_TYPEDEF_(0xC00D2712)
#define NS_E_DRM_SECURE_STORE_ERROR      _HRESULT_TYPEDEF_(0xC00D2713)
#define NS_E_DRM_LICENSE_STORE_SAVE_ERROR _HRESULT_TYPEDEF_(0xC00D2714)
#define NS_E_DRM_SECURE_STORE_UNLOCK_ERROR _HRESULT_TYPEDEF_(0xC00D2715)
#define NS_E_DRM_INVALID_CONTENT         _HRESULT_TYPEDEF_(0xC00D2716)
#define NS_E_DRM_UNABLE_TO_OPEN_LICENSE  _HRESULT_TYPEDEF_(0xC00D2717)
#define NS_E_DRM_INVALID_LICENSE         _HRESULT_TYPEDEF_(0xC00D2718)
#define NS_E_DRM_INVALID_MACHINE         _HRESULT_TYPEDEF_(0xC00D2719)
#define NS_E_DRM_ENUM_LICENSE_FAILED     _HRESULT_TYPEDEF_(0xC00D271B)
#define NS_E_DRM_INVALID_LICENSE_REQUEST _HRESULT_TYPEDEF_(0xC00D271C)
#define NS_E_DRM_UNABLE_TO_INITIALIZE    _HRESULT_TYPEDEF_(0xC00D271D)
#define NS_E_DRM_UNABLE_TO_ACQUIRE_LICENSE _HRESULT_TYPEDEF_(0xC00D271E)
#define NS_E_DRM_INVALID_LICENSE_ACQUIRED _HRESULT_TYPEDEF_(0xC00D271F)
#define NS_E_DRM_NO_RIGHTS               _HRESULT_TYPEDEF_(0xC00D2720)
#define NS_E_DRM_KEY_ERROR               _HRESULT_TYPEDEF_(0xC00D2721)
#define NS_E_DRM_ENCRYPT_ERROR           _HRESULT_TYPEDEF_(0xC00D2722)
#define NS_E_DRM_DECRYPT_ERROR           _HRESULT_TYPEDEF_(0xC00D2723)
#define NS_E_DRM_LICENSE_INVALID_XML     _HRESULT_TYPEDEF_(0xC00D2725)
#define NS_S_DRM_LICENSE_ACQUIRED        _HRESULT_TYPEDEF_(0x000D2726)
#define NS_S_DRM_INDIVIDUALIZED          _HRESULT_TYPEDEF_(0x000D2727)
#define NS_E_DRM_NEEDS_INDIVIDUALIZATION _HRESULT_TYPEDEF_(0xC00D2728)
#define NS_E_DRM_ALREADY_INDIVIDUALIZED  _HRESULT_TYPEDEF_(0xC00D2729)
#define NS_E_DRM_ACTION_NOT_QUERIED      _HRESULT_TYPEDEF_(0xC00D272A)
#define NS_E_DRM_ACQUIRING_LICENSE       _HRESULT_TYPEDEF_(0xC00D272B)
#define NS_E_DRM_INDIVIDUALIZING         _HRESULT_TYPEDEF_(0xC00D272C)
#define NS_E_BACKUP_RESTORE_FAILURE      _HRESULT_TYPEDEF_(0xC00D272D)
#define NS_E_BACKUP_RESTORE_BAD_REQUEST_ID _HRESULT_TYPEDEF_(0xC00D272E)
#define NS_E_DRM_PARAMETERS_MISMATCHED   _HRESULT_TYPEDEF_(0xC00D272F)
#define NS_E_DRM_UNABLE_TO_CREATE_LICENSE_OBJECT _HRESULT_TYPEDEF_(0xC00D2730)
#define NS_E_DRM_UNABLE_TO_CREATE_INDI_OBJECT _HRESULT_TYPEDEF_(0xC00D2731)
#define NS_E_DRM_UNABLE_TO_CREATE_ENCRYPT_OBJECT _HRESULT_TYPEDEF_(0xC00D2732)
#define NS_E_DRM_UNABLE_TO_CREATE_DECRYPT_OBJECT _HRESULT_TYPEDEF_(0xC00D2733)
#define NS_E_DRM_UNABLE_TO_CREATE_PROPERTIES_OBJECT _HRESULT_TYPEDEF_(0xC00D2734)
#define NS_E_DRM_UNABLE_TO_CREATE_BACKUP_OBJECT _HRESULT_TYPEDEF_(0xC00D2735)
#define NS_E_DRM_INDIVIDUALIZE_ERROR     _HRESULT_TYPEDEF_(0xC00D2736)
#define NS_E_DRM_LICENSE_OPEN_ERROR      _HRESULT_TYPEDEF_(0xC00D2737)
#define NS_E_DRM_LICENSE_CLOSE_ERROR     _HRESULT_TYPEDEF_(0xC00D2738)
#define NS_E_DRM_GET_LICENSE_ERROR       _HRESULT_TYPEDEF_(0xC00D2739)
#define NS_E_DRM_QUERY_ERROR             _HRESULT_TYPEDEF_(0xC00D273A)
#define NS_E_DRM_REPORT_ERROR            _HRESULT_TYPEDEF_(0xC00D273B)
#define NS_E_DRM_GET_LICENSESTRING_ERROR _HRESULT_TYPEDEF_(0xC00D273C)
#define NS_E_DRM_GET_CONTENTSTRING_ERROR _HRESULT_TYPEDEF_(0xC00D273D)
#define NS_E_DRM_MONITOR_ERROR           _HRESULT_TYPEDEF_(0xC00D273E)
#define NS_E_DRM_UNABLE_TO_SET_PARAMETER _HRESULT_TYPEDEF_(0xC00D273F)
#define NS_E_DRM_INVALID_APPDATA         _HRESULT_TYPEDEF_(0xC00D2740)
#define NS_E_DRM_INVALID_APPDATA_VERSION _HRESULT_TYPEDEF_(0xC00D2741)
#define NS_E_DRM_BACKUP_EXISTS           _HRESULT_TYPEDEF_(0xC00D2742)
#define NS_E_DRM_BACKUP_CORRUPT          _HRESULT_TYPEDEF_(0xC00D2743)
#define NS_E_DRM_BACKUPRESTORE_BUSY      _HRESULT_TYPEDEF_(0xC00D2744)
#define NS_E_BACKUP_RESTORE_BAD_DATA     _HRESULT_TYPEDEF_(0xC00D2745)
#define NS_S_DRM_MONITOR_CANCELLED       _HRESULT_TYPEDEF_(0x000D2746)
#define NS_S_DRM_ACQUIRE_CANCELLED       _HRESULT_TYPEDEF_(0x000D2747)
#define NS_E_DRM_LICENSE_UNUSABLE        _HRESULT_TYPEDEF_(0xC00D2748)
#define NS_E_DRM_INVALID_PROPERTY        _HRESULT_TYPEDEF_(0xC00D2749)
#define NS_E_DRM_SECURE_STORE_NOT_FOUND  _HRESULT_TYPEDEF_(0xC00D274A)
#define NS_E_DRM_CACHED_CONTENT_ERROR    _HRESULT_TYPEDEF_(0xC00D274B)
#define NS_E_DRM_INDIVIDUALIZATION_INCOMPLETE _HRESULT_TYPEDEF_(0xC00D274C)
#define NS_E_DRM_DRIVER_AUTH_FAILURE     _HRESULT_TYPEDEF_(0xC00D274D)
#define NS_E_DRM_NEED_UPGRADE_MSSAP      _HRESULT_TYPEDEF_(0xC00D274E)
#define NS_E_DRM_REOPEN_CONTENT          _HRESULT_TYPEDEF_(0xC00D274F)
#define NS_E_DRM_DRIVER_DIGIOUT_FAILURE  _HRESULT_TYPEDEF_(0xC00D2750)
#define NS_E_DRM_INVALID_SECURESTORE_PASSWORD _HRESULT_TYPEDEF_(0xC00D2751)
#define NS_E_DRM_APPCERT_REVOKED         _HRESULT_TYPEDEF_(0xC00D2752)
#define NS_E_DRM_RESTORE_FRAUD           _HRESULT_TYPEDEF_(0xC00D2753)
#define NS_E_DRM_HARDWARE_INCONSISTENT   _HRESULT_TYPEDEF_(0xC00D2754)
#define NS_E_DRM_SDMI_TRIGGER            _HRESULT_TYPEDEF_(0xC00D2755)
#define NS_E_DRM_SDMI_NOMORECOPIES       _HRESULT_TYPEDEF_(0xC00D2756)
#define NS_E_DRM_UNABLE_TO_CREATE_HEADER_OBJECT _HRESULT_TYPEDEF_(0xC00D2757)
#define NS_E_DRM_UNABLE_TO_CREATE_KEYS_OBJECT _HRESULT_TYPEDEF_(0xC00D2758)
;
#define NS_E_DRM_LICENSE_NOTACQUIRED     _HRESULT_TYPEDEF_(0xC00D2759)
#define NS_E_DRM_UNABLE_TO_CREATE_CODING_OBJECT _HRESULT_TYPEDEF_(0xC00D275A)
#define NS_E_DRM_UNABLE_TO_CREATE_STATE_DATA_OBJECT _HRESULT_TYPEDEF_(0xC00D275B)
#define NS_E_DRM_BUFFER_TOO_SMALL        _HRESULT_TYPEDEF_(0xC00D275C)
#define NS_E_DRM_UNSUPPORTED_PROPERTY    _HRESULT_TYPEDEF_(0xC00D275D)
#define NS_E_DRM_ERROR_BAD_NET_RESP      _HRESULT_TYPEDEF_(0xC00D275E)
#define NS_E_DRM_STORE_NOTALLSTORED      _HRESULT_TYPEDEF_(0xC00D275F)
#define NS_E_DRM_SECURITY_COMPONENT_SIGNATURE_INVALID _HRESULT_TYPEDEF_(0xC00D2760)
#define NS_E_DRM_INVALID_DATA            _HRESULT_TYPEDEF_(0xC00D2761)
#define NS_E_DRM_POLICY_DISABLE_ONLINE   _HRESULT_TYPEDEF_(0xC00D2762)
#define NS_E_DRM_UNABLE_TO_CREATE_AUTHENTICATION_OBJECT _HRESULT_TYPEDEF_(0xC00D2763)
#define NS_E_DRM_NOT_CONFIGURED          _HRESULT_TYPEDEF_(0xC00D2764)
#define NS_E_DRM_DEVICE_ACTIVATION_CANCELED _HRESULT_TYPEDEF_(0xC00D2765)
#define NS_E_BACKUP_RESTORE_TOO_MANY_RESETS _HRESULT_TYPEDEF_(0xC00D2766)
#define NS_E_DRM_DEBUGGING_NOT_ALLOWED   _HRESULT_TYPEDEF_(0xC00D2767)
#define NS_E_DRM_OPERATION_CANCELED      _HRESULT_TYPEDEF_(0xC00D2768)
#define NS_E_DRM_RESTRICTIONS_NOT_RETRIEVED _HRESULT_TYPEDEF_(0xC00D2769)
#define NS_E_DRM_UNABLE_TO_CREATE_PLAYLIST_OBJECT _HRESULT_TYPEDEF_(0xC00D276A)
#define NS_E_DRM_UNABLE_TO_CREATE_PLAYLIST_BURN_OBJECT _HRESULT_TYPEDEF_(0xC00D276B)
#define NS_E_DRM_UNABLE_TO_CREATE_DEVICE_REGISTRATION_OBJECT _HRESULT_TYPEDEF_(0xC00D276C)
#define NS_E_DRM_UNABLE_TO_CREATE_METERING_OBJECT _HRESULT_TYPEDEF_(0xC00D276D)
#define NS_S_DRM_BURNABLE_TRACK          _HRESULT_TYPEDEF_(0x000D276E)
#define NS_S_DRM_BURNABLE_TRACK_WITH_PLAYLIST_RESTRICTION _HRESULT_TYPEDEF_(0x000D276F)
#define NS_E_DRM_TRACK_EXCEEDED_PLAYLIST_RESTICTION _HRESULT_TYPEDEF_(0xC00D2770)
#define NS_E_DRM_TRACK_EXCEEDED_TRACKBURN_RESTRICTION _HRESULT_TYPEDEF_(0xC00D2771)
#define NS_E_DRM_UNABLE_TO_GET_DEVICE_CERT _HRESULT_TYPEDEF_(0xC00D2772)
#define NS_E_DRM_UNABLE_TO_GET_SECURE_CLOCK _HRESULT_TYPEDEF_(0xC00D2773)
#define NS_E_DRM_UNABLE_TO_SET_SECURE_CLOCK _HRESULT_TYPEDEF_(0xC00D2774)
#define NS_E_DRM_UNABLE_TO_GET_SECURE_CLOCK_FROM_SERVER _HRESULT_TYPEDEF_(0xC00D2775)
#define NS_E_DRM_POLICY_METERING_DISABLED _HRESULT_TYPEDEF_(0xC00D2776)
#define NS_E_DRM_TRANSFER_CHAINED_LICENSES_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D2777)
#define NS_E_DRM_SDK_VERSIONMISMATCH     _HRESULT_TYPEDEF_(0xC00D2778)
#define NS_E_DRM_LIC_NEEDS_DEVICE_CLOCK_SET _HRESULT_TYPEDEF_(0xC00D2779)
#define NS_E_LICENSE_HEADER_MISSING_URL  _HRESULT_TYPEDEF_(0xC00D277A)
#define NS_E_DEVICE_NOT_WMDRM_DEVICE     _HRESULT_TYPEDEF_(0xC00D277B)
#define NS_E_DRM_INVALID_APPCERT         _HRESULT_TYPEDEF_(0xC00D277C)
#define NS_E_DRM_PROTOCOL_FORCEFUL_TERMINATION_ON_PETITION _HRESULT_TYPEDEF_(0xC00D277D)
#define NS_E_DRM_PROTOCOL_FORCEFUL_TERMINATION_ON_CHALLENGE _HRESULT_TYPEDEF_(0xC00D277E)
#define NS_E_DRM_CHECKPOINT_FAILED       _HRESULT_TYPEDEF_(0xC00D277F)
#define NS_E_DRM_BB_UNABLE_TO_INITIALIZE _HRESULT_TYPEDEF_(0xC00D2780)
#define NS_E_DRM_UNABLE_TO_LOAD_HARDWARE_ID _HRESULT_TYPEDEF_(0xC00D2781)
#define NS_E_DRM_UNABLE_TO_OPEN_DATA_STORE _HRESULT_TYPEDEF_(0xC00D2782)
#define NS_E_DRM_DATASTORE_CORRUPT       _HRESULT_TYPEDEF_(0xC00D2783)
#define NS_E_DRM_UNABLE_TO_CREATE_INMEMORYSTORE_OBJECT _HRESULT_TYPEDEF_(0xC00D2784)
#define NS_E_DRM_STUBLIB_REQUIRED        _HRESULT_TYPEDEF_(0xC00D2785)
#define NS_E_DRM_UNABLE_TO_CREATE_CERTIFICATE_OBJECT _HRESULT_TYPEDEF_(0xC00D2786)
#define NS_E_DRM_MIGRATION_TARGET_NOT_ONLINE _HRESULT_TYPEDEF_(0xC00D2787)
#define NS_E_DRM_INVALID_MIGRATION_IMAGE _HRESULT_TYPEDEF_(0xC00D2788)
#define NS_E_DRM_MIGRATION_TARGET_STATES_CORRUPTED _HRESULT_TYPEDEF_(0xC00D2789)
#define NS_E_DRM_MIGRATION_IMPORTER_NOT_AVAILABLE _HRESULT_TYPEDEF_(0xC00D278A)
#define NS_DRM_E_MIGRATION_UPGRADE_WITH_DIFF_SID _HRESULT_TYPEDEF_(0xC00D278B)
#define NS_DRM_E_MIGRATION_SOURCE_MACHINE_IN_USE _HRESULT_TYPEDEF_(0xC00D278C)
#define NS_DRM_E_MIGRATION_TARGET_MACHINE_LESS_THAN_LH _HRESULT_TYPEDEF_(0xC00D278D)
#define NS_DRM_E_MIGRATION_IMAGE_ALREADY_EXISTS _HRESULT_TYPEDEF_(0xC00D278E)
#define NS_E_DRM_HARDWAREID_MISMATCH     _HRESULT_TYPEDEF_(0xC00D278F)
#define NS_E_INVALID_DRMV2CLT_STUBLIB    _HRESULT_TYPEDEF_(0xC00D2790)
#define NS_E_DRM_MIGRATION_INVALID_LEGACYV2_DATA _HRESULT_TYPEDEF_(0xC00D2791)
#define NS_E_DRM_MIGRATION_LICENSE_ALREADY_EXISTS _HRESULT_TYPEDEF_(0xC00D2792)
#define NS_E_DRM_MIGRATION_INVALID_LEGACYV2_SST_PASSWORD _HRESULT_TYPEDEF_(0xC00D2793)
#define NS_E_DRM_MIGRATION_NOT_SUPPORTED _HRESULT_TYPEDEF_(0xC00D2794)
#define NS_E_DRM_UNABLE_TO_CREATE_MIGRATION_IMPORTER_OBJECT _HRESULT_TYPEDEF_(0xC00D2795)
#define NS_E_DRM_CHECKPOINT_MISMATCH     _HRESULT_TYPEDEF_(0xC00D2796)
#define NS_E_DRM_CHECKPOINT_CORRUPT      _HRESULT_TYPEDEF_(0xC00D2797)
#define NS_E_REG_FLUSH_FAILURE           _HRESULT_TYPEDEF_(0xC00D2798)
#define NS_E_HDS_KEY_MISMATCH            _HRESULT_TYPEDEF_(0xC00D2799)
#define NS_E_DRM_MIGRATION_OPERATION_CANCELLED _HRESULT_TYPEDEF_(0xC00D279A)
#define NS_E_DRM_MIGRATION_OBJECT_IN_USE _HRESULT_TYPEDEF_(0xC00D279B)
#define NS_E_DRM_MALFORMED_CONTENT_HEADER _HRESULT_TYPEDEF_(0xC00D279C)
#define NS_E_DRM_LICENSE_EXPIRED         _HRESULT_TYPEDEF_(0xC00D27D8)
#define NS_E_DRM_LICENSE_NOTENABLED      _HRESULT_TYPEDEF_(0xC00D27D9)
#define NS_E_DRM_LICENSE_APPSECLOW       _HRESULT_TYPEDEF_(0xC00D27DA)
#define NS_E_DRM_STORE_NEEDINDI          _HRESULT_TYPEDEF_(0xC00D27DB)
#define NS_E_DRM_STORE_NOTALLOWED        _HRESULT_TYPEDEF_(0xC00D27DC)
#define NS_E_DRM_LICENSE_APP_NOTALLOWED  _HRESULT_TYPEDEF_(0xC00D27DD)
#define NS_S_DRM_NEEDS_INDIVIDUALIZATION _HRESULT_TYPEDEF_(0x000D27DE)
#define NS_E_DRM_LICENSE_CERT_EXPIRED    _HRESULT_TYPEDEF_(0xC00D27DF)
#define NS_E_DRM_LICENSE_SECLOW          _HRESULT_TYPEDEF_(0xC00D27E0)
#define NS_E_DRM_LICENSE_CONTENT_REVOKED _HRESULT_TYPEDEF_(0xC00D27E1)
#define NS_E_DRM_DEVICE_NOT_REGISTERED   _HRESULT_TYPEDEF_(0xC00D27E2)
#define NS_E_DRM_LICENSE_NOSAP           _HRESULT_TYPEDEF_(0xC00D280A)
#define NS_E_DRM_LICENSE_NOSVP           _HRESULT_TYPEDEF_(0xC00D280B)
#define NS_E_DRM_LICENSE_NOWDM           _HRESULT_TYPEDEF_(0xC00D280C)
#define NS_E_DRM_LICENSE_NOTRUSTEDCODEC  _HRESULT_TYPEDEF_(0xC00D280D)
#define NS_E_DRM_SOURCEID_NOT_SUPPORTED  _HRESULT_TYPEDEF_(0xC00D280E)
#define NS_E_DRM_NEEDS_UPGRADE_TEMPFILE  _HRESULT_TYPEDEF_(0xC00D283D)
#define NS_E_DRM_NEED_UPGRADE_PD         _HRESULT_TYPEDEF_(0xC00D283E)
#define NS_E_DRM_SIGNATURE_FAILURE       _HRESULT_TYPEDEF_(0xC00D283F)
#define NS_E_DRM_LICENSE_SERVER_INFO_MISSING _HRESULT_TYPEDEF_(0xC00D2840)
#define NS_E_DRM_BUSY                    _HRESULT_TYPEDEF_(0xC00D2841)
#define NS_E_DRM_PD_TOO_MANY_DEVICES     _HRESULT_TYPEDEF_(0xC00D2842)
#define NS_E_DRM_INDIV_FRAUD             _HRESULT_TYPEDEF_(0xC00D2843)
#define NS_E_DRM_INDIV_NO_CABS           _HRESULT_TYPEDEF_(0xC00D2844)
#define NS_E_DRM_INDIV_SERVICE_UNAVAILABLE _HRESULT_TYPEDEF_(0xC00D2845)
#define NS_E_DRM_RESTORE_SERVICE_UNAVAILABLE _HRESULT_TYPEDEF_(0xC00D2846)
#define NS_E_DRM_CLIENT_CODE_EXPIRED     _HRESULT_TYPEDEF_(0xC00D2847)
#define NS_E_DRM_NO_UPLINK_LICENSE       _HRESULT_TYPEDEF_(0xC00D2848)
#define NS_E_DRM_INVALID_KID             _HRESULT_TYPEDEF_(0xC00D2849)
#define NS_E_DRM_LICENSE_INITIALIZATION_ERROR _HRESULT_TYPEDEF_(0xC00D284A)
#define NS_E_DRM_CHAIN_TOO_LONG          _HRESULT_TYPEDEF_(0xC00D284C)
#define NS_E_DRM_UNSUPPORTED_ALGORITHM   _HRESULT_TYPEDEF_(0xC00D284D)
#define NS_E_DRM_LICENSE_DELETION_ERROR  _HRESULT_TYPEDEF_(0xC00D284E)
#define NS_E_DRM_INVALID_CERTIFICATE     _HRESULT_TYPEDEF_(0xC00D28A0)
#define NS_E_DRM_CERTIFICATE_REVOKED     _HRESULT_TYPEDEF_(0xC00D28A1)
#define NS_E_DRM_LICENSE_UNAVAILABLE     _HRESULT_TYPEDEF_(0xC00D28A2)
#define NS_E_DRM_DEVICE_LIMIT_REACHED    _HRESULT_TYPEDEF_(0xC00D28A3)
#define NS_E_DRM_UNABLE_TO_VERIFY_PROXIMITY _HRESULT_TYPEDEF_(0xC00D28A4)
#define NS_E_DRM_MUST_REGISTER           _HRESULT_TYPEDEF_(0xC00D28A5)
#define NS_E_DRM_MUST_APPROVE            _HRESULT_TYPEDEF_(0xC00D28A6)
#define NS_E_DRM_MUST_REVALIDATE         _HRESULT_TYPEDEF_(0xC00D28A7)
#define NS_E_DRM_INVALID_PROXIMITY_RESPONSE _HRESULT_TYPEDEF_(0xC00D28A8)
#define NS_E_DRM_INVALID_SESSION         _HRESULT_TYPEDEF_(0xC00D28A9)
#define NS_E_DRM_DEVICE_NOT_OPEN         _HRESULT_TYPEDEF_(0xC00D28AA)
#define NS_E_DRM_DEVICE_ALREADY_REGISTERED _HRESULT_TYPEDEF_(0xC00D28AB)
#define NS_E_DRM_UNSUPPORTED_PROTOCOL_VERSION _HRESULT_TYPEDEF_(0xC00D28AC)
#define NS_E_DRM_UNSUPPORTED_ACTION      _HRESULT_TYPEDEF_(0xC00D28AD)
#define NS_E_DRM_CERTIFICATE_SECURITY_LEVEL_INADEQUATE _HRESULT_TYPEDEF_(0xC00D28AE)
#define NS_E_DRM_UNABLE_TO_OPEN_PORT     _HRESULT_TYPEDEF_(0xC00D28AF)
#define NS_E_DRM_BAD_REQUEST             _HRESULT_TYPEDEF_(0xC00D28B0)
#define NS_E_DRM_INVALID_CRL             _HRESULT_TYPEDEF_(0xC00D28B1)
#define NS_E_DRM_ATTRIBUTE_TOO_LONG      _HRESULT_TYPEDEF_(0xC00D28B2)
#define NS_E_DRM_EXPIRED_LICENSEBLOB     _HRESULT_TYPEDEF_(0xC00D28B3)
#define NS_E_DRM_INVALID_LICENSEBLOB     _HRESULT_TYPEDEF_(0xC00D28B4)
#define NS_E_DRM_INCLUSION_LIST_REQUIRED _HRESULT_TYPEDEF_(0xC00D28B5)
#define NS_E_DRM_DRMV2CLT_REVOKED        _HRESULT_TYPEDEF_(0xC00D28B6)
#define NS_E_DRM_RIV_TOO_SMALL           _HRESULT_TYPEDEF_(0xC00D28B7)
#define NS_E_OUTPUT_PROTECTION_LEVEL_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D2904)
#define NS_E_COMPRESSED_DIGITAL_VIDEO_PROTECTION_LEVEL_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D2905)
#define NS_E_UNCOMPRESSED_DIGITAL_VIDEO_PROTECTION_LEVEL_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D2906)
#define NS_E_ANALOG_VIDEO_PROTECTION_LEVEL_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D2907)
#define NS_E_COMPRESSED_DIGITAL_AUDIO_PROTECTION_LEVEL_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D2908)
#define NS_E_UNCOMPRESSED_DIGITAL_AUDIO_PROTECTION_LEVEL_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D2909)
#define NS_E_OUTPUT_PROTECTION_SCHEME_UNSUPPORTED _HRESULT_TYPEDEF_(0xC00D290A)
#define NS_S_REBOOT_RECOMMENDED          _HRESULT_TYPEDEF_(0x000D2AF8)
#define NS_S_REBOOT_REQUIRED             _HRESULT_TYPEDEF_(0x000D2AF9)
#define NS_E_REBOOT_RECOMMENDED          _HRESULT_TYPEDEF_(0xC00D2AFA)
#define NS_E_REBOOT_REQUIRED             _HRESULT_TYPEDEF_(0xC00D2AFB)
#define NS_E_SETUP_INCOMPLETE            _HRESULT_TYPEDEF_(0xC00D2AFC)
#define NS_E_SETUP_DRM_MIGRATION_FAILED  _HRESULT_TYPEDEF_(0xC00D2AFD)
#define NS_E_SETUP_IGNORABLE_FAILURE     _HRESULT_TYPEDEF_(0xC00D2AFE)
#define NS_E_SETUP_DRM_MIGRATION_FAILED_AND_IGNORABLE_FAILURE _HRESULT_TYPEDEF_(0xC00D2AFF)
#define NS_E_SETUP_BLOCKED               _HRESULT_TYPEDEF_(0xC00D2B00)
#define NS_E_UNKNOWN_PROTOCOL            _HRESULT_TYPEDEF_(0xC00D2EE0)
#define NS_E_REDIRECT_TO_PROXY           _HRESULT_TYPEDEF_(0xC00D2EE1)
#define NS_E_INTERNAL_SERVER_ERROR       _HRESULT_TYPEDEF_(0xC00D2EE2)
#define NS_E_BAD_REQUEST                 _HRESULT_TYPEDEF_(0xC00D2EE3)
#define NS_E_ERROR_FROM_PROXY            _HRESULT_TYPEDEF_(0xC00D2EE4)
#define NS_E_PROXY_TIMEOUT               _HRESULT_TYPEDEF_(0xC00D2EE5)
#define NS_E_SERVER_UNAVAILABLE          _HRESULT_TYPEDEF_(0xC00D2EE6)
#define NS_E_REFUSED_BY_SERVER           _HRESULT_TYPEDEF_(0xC00D2EE7)
#define NS_E_INCOMPATIBLE_SERVER         _HRESULT_TYPEDEF_(0xC00D2EE8)
#define NS_E_MULTICAST_DISABLED          _HRESULT_TYPEDEF_(0xC00D2EE9)
#define NS_E_INVALID_REDIRECT            _HRESULT_TYPEDEF_(0xC00D2EEA)
#define NS_E_ALL_PROTOCOLS_DISABLED      _HRESULT_TYPEDEF_(0xC00D2EEB)
#define NS_E_MSBD_NO_LONGER_SUPPORTED    _HRESULT_TYPEDEF_(0xC00D2EEC)
#define NS_E_PROXY_NOT_FOUND             _HRESULT_TYPEDEF_(0xC00D2EED)
#define NS_E_CANNOT_CONNECT_TO_PROXY     _HRESULT_TYPEDEF_(0xC00D2EEE)
#define NS_E_SERVER_DNS_TIMEOUT          _HRESULT_TYPEDEF_(0xC00D2EEF)
#define NS_E_PROXY_DNS_TIMEOUT           _HRESULT_TYPEDEF_(0xC00D2EF0)
#define NS_E_CLOSED_ON_SUSPEND           _HRESULT_TYPEDEF_(0xC00D2EF1)
#define NS_E_CANNOT_READ_PLAYLIST_FROM_MEDIASERVER _HRESULT_TYPEDEF_(0xC00D2EF2)
#define NS_E_SESSION_NOT_FOUND           _HRESULT_TYPEDEF_(0xC00D2EF3)
#define NS_E_REQUIRE_STREAMING_CLIENT    _HRESULT_TYPEDEF_(0xC00D2EF4)
#define NS_E_PLAYLIST_ENTRY_HAS_CHANGED  _HRESULT_TYPEDEF_(0xC00D2EF5)
#define NS_E_PROXY_ACCESSDENIED          _HRESULT_TYPEDEF_(0xC00D2EF6)
#define NS_E_PROXY_SOURCE_ACCESSDENIED   _HRESULT_TYPEDEF_(0xC00D2EF7)
#define NS_E_NETWORK_SINK_WRITE          _HRESULT_TYPEDEF_(0xC00D2EF8)
#define NS_E_FIREWALL                    _HRESULT_TYPEDEF_(0xC00D2EF9)
#define NS_E_MMS_NOT_SUPPORTED           _HRESULT_TYPEDEF_(0xC00D2EFA)
#define NS_E_SERVER_ACCESSDENIED         _HRESULT_TYPEDEF_(0xC00D2EFB)
#define NS_E_RESOURCE_GONE               _HRESULT_TYPEDEF_(0xC00D2EFC)
#define NS_E_NO_EXISTING_PACKETIZER      _HRESULT_TYPEDEF_(0xC00D2EFD)
#define NS_E_BAD_SYNTAX_IN_SERVER_RESPONSE _HRESULT_TYPEDEF_(0xC00D2EFE)
#define NS_I_RECONNECTED                 _HRESULT_TYPEDEF_(0x400D2EFF)
#define NS_E_RESET_SOCKET_CONNECTION     _HRESULT_TYPEDEF_(0xC00D2F00)
#define NS_I_NOLOG_STOP                  _HRESULT_TYPEDEF_(0x400D2F01)
#define NS_E_TOO_MANY_HOPS               _HRESULT_TYPEDEF_(0xC00D2F02)
#define NS_I_EXISTING_PACKETIZER         _HRESULT_TYPEDEF_(0x400D2F03)
#define NS_I_MANUAL_PROXY                _HRESULT_TYPEDEF_(0x400D2F04)
#define NS_E_TOO_MUCH_DATA_FROM_SERVER   _HRESULT_TYPEDEF_(0xC00D2F05)
#define NS_E_CONNECT_TIMEOUT             _HRESULT_TYPEDEF_(0xC00D2F06)
#define NS_E_PROXY_CONNECT_TIMEOUT       _HRESULT_TYPEDEF_(0xC00D2F07)
#define NS_E_SESSION_INVALID             _HRESULT_TYPEDEF_(0xC00D2F08)
#define NS_S_EOSRECEDING                 _HRESULT_TYPEDEF_(0x000D2F09)
#define NS_E_PACKETSINK_UNKNOWN_FEC_STREAM _HRESULT_TYPEDEF_(0xC00D2F0A)
#define NS_E_PUSH_CANNOTCONNECT          _HRESULT_TYPEDEF_(0xC00D2F0B)
#define NS_E_INCOMPATIBLE_PUSH_SERVER    _HRESULT_TYPEDEF_(0xC00D2F0C)
#define NS_S_CHANGENOTICE                _HRESULT_TYPEDEF_(0x000D2F0D)
#define NS_E_END_OF_PLAYLIST             _HRESULT_TYPEDEF_(0xC00D32C8)
#define NS_E_USE_FILE_SOURCE             _HRESULT_TYPEDEF_(0xC00D32C9)
#define NS_E_PROPERTY_NOT_FOUND          _HRESULT_TYPEDEF_(0xC00D32CA)
#define NS_E_PROPERTY_READ_ONLY          _HRESULT_TYPEDEF_(0xC00D32CC)
#define NS_E_TABLE_KEY_NOT_FOUND         _HRESULT_TYPEDEF_(0xC00D32CD)
#define NS_E_INVALID_QUERY_OPERATOR      _HRESULT_TYPEDEF_(0xC00D32CF)
#define NS_E_INVALID_QUERY_PROPERTY      _HRESULT_TYPEDEF_(0xC00D32D0)
#define NS_E_PROPERTY_NOT_SUPPORTED      _HRESULT_TYPEDEF_(0xC00D32D2)
#define NS_E_SCHEMA_CLASSIFY_FAILURE     _HRESULT_TYPEDEF_(0xC00D32D4)
#define NS_E_METADATA_FORMAT_NOT_SUPPORTED _HRESULT_TYPEDEF_(0xC00D32D5)
#define NS_E_METADATA_NO_EDITING_CAPABILITY _HRESULT_TYPEDEF_(0xC00D32D6)
#define NS_E_METADATA_CANNOT_SET_LOCALE  _HRESULT_TYPEDEF_(0xC00D32D7)
#define NS_E_METADATA_LANGUAGE_NOT_SUPORTED _HRESULT_TYPEDEF_(0xC00D32D8)
#define NS_E_METADATA_NO_RFC1766_NAME_FOR_LOCALE _HRESULT_TYPEDEF_(0xC00D32D9)
#define NS_E_METADATA_NOT_AVAILABLE      _HRESULT_TYPEDEF_(0xC00D32DA)
#define NS_E_METADATA_CACHE_DATA_NOT_AVAILABLE _HRESULT_TYPEDEF_(0xC00D32DB)
#define NS_E_METADATA_INVALID_DOCUMENT_TYPE _HRESULT_TYPEDEF_(0xC00D32DC)
#define NS_E_METADATA_IDENTIFIER_NOT_AVAILABLE _HRESULT_TYPEDEF_(0xC00D32DD)
#define NS_E_METADATA_CANNOT_RETRIEVE_FROM_OFFLINE_CACHE _HRESULT_TYPEDEF_(0xC00D32DE)

#endif /* _NSERROR_H */
