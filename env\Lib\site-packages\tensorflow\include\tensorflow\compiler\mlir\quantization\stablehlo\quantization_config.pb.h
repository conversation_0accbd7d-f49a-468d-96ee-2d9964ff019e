// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/quantization/stablehlo/quantization_config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
namespace stablehlo {
namespace quantization {
class CalibrationOptions;
struct CalibrationOptionsDefaultTypeInternal;
extern CalibrationOptionsDefaultTypeInternal _CalibrationOptions_default_instance_;
class CalibrationOptions_CalibrationParameters;
struct CalibrationOptions_CalibrationParametersDefaultTypeInternal;
extern CalibrationOptions_CalibrationParametersDefaultTypeInternal _CalibrationOptions_CalibrationParameters_default_instance_;
class DebuggerConfig;
struct DebuggerConfigDefaultTypeInternal;
extern DebuggerConfigDefaultTypeInternal _DebuggerConfig_default_instance_;
class FunctionNameMatcherSpec;
struct FunctionNameMatcherSpecDefaultTypeInternal;
extern FunctionNameMatcherSpecDefaultTypeInternal _FunctionNameMatcherSpec_default_instance_;
class MatcherSpec;
struct MatcherSpecDefaultTypeInternal;
extern MatcherSpecDefaultTypeInternal _MatcherSpec_default_instance_;
class Method;
struct MethodDefaultTypeInternal;
extern MethodDefaultTypeInternal _Method_default_instance_;
class NoQuantization;
struct NoQuantizationDefaultTypeInternal;
extern NoQuantizationDefaultTypeInternal _NoQuantization_default_instance_;
class PerTensor;
struct PerTensorDefaultTypeInternal;
extern PerTensorDefaultTypeInternal _PerTensor_default_instance_;
class PipelineConfig;
struct PipelineConfigDefaultTypeInternal;
extern PipelineConfigDefaultTypeInternal _PipelineConfig_default_instance_;
class QuantizableUnit;
struct QuantizableUnitDefaultTypeInternal;
extern QuantizableUnitDefaultTypeInternal _QuantizableUnit_default_instance_;
class QuantizationConfig;
struct QuantizationConfigDefaultTypeInternal;
extern QuantizationConfigDefaultTypeInternal _QuantizationConfig_default_instance_;
class QuantizationResult;
struct QuantizationResultDefaultTypeInternal;
extern QuantizationResultDefaultTypeInternal _QuantizationResult_default_instance_;
class QuantizationResults;
struct QuantizationResultsDefaultTypeInternal;
extern QuantizationResultsDefaultTypeInternal _QuantizationResults_default_instance_;
class QuantizationSpec;
struct QuantizationSpecDefaultTypeInternal;
extern QuantizationSpecDefaultTypeInternal _QuantizationSpec_default_instance_;
class QuantizationSpecs;
struct QuantizationSpecsDefaultTypeInternal;
extern QuantizationSpecsDefaultTypeInternal _QuantizationSpecs_default_instance_;
class QuantizedDimension;
struct QuantizedDimensionDefaultTypeInternal;
extern QuantizedDimensionDefaultTypeInternal _QuantizedDimension_default_instance_;
class QuantizedType;
struct QuantizedTypeDefaultTypeInternal;
extern QuantizedTypeDefaultTypeInternal _QuantizedType_default_instance_;
class RepresentativeDatasetConfig;
struct RepresentativeDatasetConfigDefaultTypeInternal;
extern RepresentativeDatasetConfigDefaultTypeInternal _RepresentativeDatasetConfig_default_instance_;
class StaticRangePtq;
struct StaticRangePtqDefaultTypeInternal;
extern StaticRangePtqDefaultTypeInternal _StaticRangePtq_default_instance_;
class StaticRangePtqPreset;
struct StaticRangePtqPresetDefaultTypeInternal;
extern StaticRangePtqPresetDefaultTypeInternal _StaticRangePtqPreset_default_instance_;
class StaticRangePtq_InputQuantizedTypesEntry_DoNotUse;
struct StaticRangePtq_InputQuantizedTypesEntry_DoNotUseDefaultTypeInternal;
extern StaticRangePtq_InputQuantizedTypesEntry_DoNotUseDefaultTypeInternal _StaticRangePtq_InputQuantizedTypesEntry_DoNotUse_default_instance_;
class TfRecordFile;
struct TfRecordFileDefaultTypeInternal;
extern TfRecordFileDefaultTypeInternal _TfRecordFile_default_instance_;
class TfSavedModelConfig;
struct TfSavedModelConfigDefaultTypeInternal;
extern TfSavedModelConfigDefaultTypeInternal _TfSavedModelConfig_default_instance_;
class WeightOnlyPtq;
struct WeightOnlyPtqDefaultTypeInternal;
extern WeightOnlyPtqDefaultTypeInternal _WeightOnlyPtq_default_instance_;
class WeightOnlyPtqPreset;
struct WeightOnlyPtqPresetDefaultTypeInternal;
extern WeightOnlyPtqPresetDefaultTypeInternal _WeightOnlyPtqPreset_default_instance_;
class WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse;
struct WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUseDefaultTypeInternal;
extern WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUseDefaultTypeInternal _WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse_default_instance_;
}  // namespace quantization
}  // namespace stablehlo
PROTOBUF_NAMESPACE_OPEN
template<> ::stablehlo::quantization::CalibrationOptions* Arena::CreateMaybeMessage<::stablehlo::quantization::CalibrationOptions>(Arena*);
template<> ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* Arena::CreateMaybeMessage<::stablehlo::quantization::CalibrationOptions_CalibrationParameters>(Arena*);
template<> ::stablehlo::quantization::DebuggerConfig* Arena::CreateMaybeMessage<::stablehlo::quantization::DebuggerConfig>(Arena*);
template<> ::stablehlo::quantization::FunctionNameMatcherSpec* Arena::CreateMaybeMessage<::stablehlo::quantization::FunctionNameMatcherSpec>(Arena*);
template<> ::stablehlo::quantization::MatcherSpec* Arena::CreateMaybeMessage<::stablehlo::quantization::MatcherSpec>(Arena*);
template<> ::stablehlo::quantization::Method* Arena::CreateMaybeMessage<::stablehlo::quantization::Method>(Arena*);
template<> ::stablehlo::quantization::NoQuantization* Arena::CreateMaybeMessage<::stablehlo::quantization::NoQuantization>(Arena*);
template<> ::stablehlo::quantization::PerTensor* Arena::CreateMaybeMessage<::stablehlo::quantization::PerTensor>(Arena*);
template<> ::stablehlo::quantization::PipelineConfig* Arena::CreateMaybeMessage<::stablehlo::quantization::PipelineConfig>(Arena*);
template<> ::stablehlo::quantization::QuantizableUnit* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizableUnit>(Arena*);
template<> ::stablehlo::quantization::QuantizationConfig* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizationConfig>(Arena*);
template<> ::stablehlo::quantization::QuantizationResult* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizationResult>(Arena*);
template<> ::stablehlo::quantization::QuantizationResults* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizationResults>(Arena*);
template<> ::stablehlo::quantization::QuantizationSpec* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizationSpec>(Arena*);
template<> ::stablehlo::quantization::QuantizationSpecs* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizationSpecs>(Arena*);
template<> ::stablehlo::quantization::QuantizedDimension* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizedDimension>(Arena*);
template<> ::stablehlo::quantization::QuantizedType* Arena::CreateMaybeMessage<::stablehlo::quantization::QuantizedType>(Arena*);
template<> ::stablehlo::quantization::RepresentativeDatasetConfig* Arena::CreateMaybeMessage<::stablehlo::quantization::RepresentativeDatasetConfig>(Arena*);
template<> ::stablehlo::quantization::StaticRangePtq* Arena::CreateMaybeMessage<::stablehlo::quantization::StaticRangePtq>(Arena*);
template<> ::stablehlo::quantization::StaticRangePtqPreset* Arena::CreateMaybeMessage<::stablehlo::quantization::StaticRangePtqPreset>(Arena*);
template<> ::stablehlo::quantization::StaticRangePtq_InputQuantizedTypesEntry_DoNotUse* Arena::CreateMaybeMessage<::stablehlo::quantization::StaticRangePtq_InputQuantizedTypesEntry_DoNotUse>(Arena*);
template<> ::stablehlo::quantization::TfRecordFile* Arena::CreateMaybeMessage<::stablehlo::quantization::TfRecordFile>(Arena*);
template<> ::stablehlo::quantization::TfSavedModelConfig* Arena::CreateMaybeMessage<::stablehlo::quantization::TfSavedModelConfig>(Arena*);
template<> ::stablehlo::quantization::WeightOnlyPtq* Arena::CreateMaybeMessage<::stablehlo::quantization::WeightOnlyPtq>(Arena*);
template<> ::stablehlo::quantization::WeightOnlyPtqPreset* Arena::CreateMaybeMessage<::stablehlo::quantization::WeightOnlyPtqPreset>(Arena*);
template<> ::stablehlo::quantization::WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse* Arena::CreateMaybeMessage<::stablehlo::quantization::WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace stablehlo {
namespace quantization {

enum DebuggerConfig_DebuggerType : int {
  DebuggerConfig_DebuggerType_DEBUGGER_TYPE_UNSPECIFIED = 0,
  DebuggerConfig_DebuggerType_DEBUGGER_TYPE_WHOLE_MODEL = 1,
  DebuggerConfig_DebuggerType_DEBUGGER_TYPE_INT_PER_LAYER = 2,
  DebuggerConfig_DebuggerType_DEBUGGER_TYPE_FLOAT_PER_LAYER = 3,
  DebuggerConfig_DebuggerType_DebuggerConfig_DebuggerType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DebuggerConfig_DebuggerType_DebuggerConfig_DebuggerType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DebuggerConfig_DebuggerType_IsValid(int value);
constexpr DebuggerConfig_DebuggerType DebuggerConfig_DebuggerType_DebuggerType_MIN = DebuggerConfig_DebuggerType_DEBUGGER_TYPE_UNSPECIFIED;
constexpr DebuggerConfig_DebuggerType DebuggerConfig_DebuggerType_DebuggerType_MAX = DebuggerConfig_DebuggerType_DEBUGGER_TYPE_FLOAT_PER_LAYER;
constexpr int DebuggerConfig_DebuggerType_DebuggerType_ARRAYSIZE = DebuggerConfig_DebuggerType_DebuggerType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DebuggerConfig_DebuggerType_descriptor();
template<typename T>
inline const std::string& DebuggerConfig_DebuggerType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DebuggerConfig_DebuggerType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DebuggerConfig_DebuggerType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DebuggerConfig_DebuggerType_descriptor(), enum_t_value);
}
inline bool DebuggerConfig_DebuggerType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DebuggerConfig_DebuggerType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DebuggerConfig_DebuggerType>(
    DebuggerConfig_DebuggerType_descriptor(), name, value);
}
enum CalibrationOptions_CalibrationMethod : int {
  CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_UNSPECIFIED = 0,
  CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_MIN_MAX = 1,
  CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_AVERAGE_MIN_MAX = 2,
  CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_PERCENTILE = 3,
  CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_MSE_BRUTEFORCE = 4,
  CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_MSE_MAX_FREQUENCY = 5,
  CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_MSE_SYMMETRIC = 6,
  CalibrationOptions_CalibrationMethod_CalibrationOptions_CalibrationMethod_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CalibrationOptions_CalibrationMethod_CalibrationOptions_CalibrationMethod_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CalibrationOptions_CalibrationMethod_IsValid(int value);
constexpr CalibrationOptions_CalibrationMethod CalibrationOptions_CalibrationMethod_CalibrationMethod_MIN = CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_UNSPECIFIED;
constexpr CalibrationOptions_CalibrationMethod CalibrationOptions_CalibrationMethod_CalibrationMethod_MAX = CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_MSE_SYMMETRIC;
constexpr int CalibrationOptions_CalibrationMethod_CalibrationMethod_ARRAYSIZE = CalibrationOptions_CalibrationMethod_CalibrationMethod_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CalibrationOptions_CalibrationMethod_descriptor();
template<typename T>
inline const std::string& CalibrationOptions_CalibrationMethod_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CalibrationOptions_CalibrationMethod>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CalibrationOptions_CalibrationMethod_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CalibrationOptions_CalibrationMethod_descriptor(), enum_t_value);
}
inline bool CalibrationOptions_CalibrationMethod_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CalibrationOptions_CalibrationMethod* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CalibrationOptions_CalibrationMethod>(
    CalibrationOptions_CalibrationMethod_descriptor(), name, value);
}
// ===================================================================

class TfRecordFile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.TfRecordFile) */ {
 public:
  inline TfRecordFile() : TfRecordFile(nullptr) {}
  ~TfRecordFile() override;
  explicit PROTOBUF_CONSTEXPR TfRecordFile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TfRecordFile(const TfRecordFile& from);
  TfRecordFile(TfRecordFile&& from) noexcept
    : TfRecordFile() {
    *this = ::std::move(from);
  }

  inline TfRecordFile& operator=(const TfRecordFile& from) {
    CopyFrom(from);
    return *this;
  }
  inline TfRecordFile& operator=(TfRecordFile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TfRecordFile& default_instance() {
    return *internal_default_instance();
  }
  static inline const TfRecordFile* internal_default_instance() {
    return reinterpret_cast<const TfRecordFile*>(
               &_TfRecordFile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TfRecordFile& a, TfRecordFile& b) {
    a.Swap(&b);
  }
  inline void Swap(TfRecordFile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TfRecordFile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TfRecordFile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TfRecordFile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TfRecordFile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TfRecordFile& from) {
    TfRecordFile::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TfRecordFile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.TfRecordFile";
  }
  protected:
  explicit TfRecordFile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPathFieldNumber = 1,
  };
  // string path = 1;
  void clear_path();
  const std::string& path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_path();
  PROTOBUF_NODISCARD std::string* release_path();
  void set_allocated_path(std::string* path);
  private:
  const std::string& _internal_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_path(const std::string& value);
  std::string* _internal_mutable_path();
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.TfRecordFile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr path_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class RepresentativeDatasetConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.RepresentativeDatasetConfig) */ {
 public:
  inline RepresentativeDatasetConfig() : RepresentativeDatasetConfig(nullptr) {}
  ~RepresentativeDatasetConfig() override;
  explicit PROTOBUF_CONSTEXPR RepresentativeDatasetConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RepresentativeDatasetConfig(const RepresentativeDatasetConfig& from);
  RepresentativeDatasetConfig(RepresentativeDatasetConfig&& from) noexcept
    : RepresentativeDatasetConfig() {
    *this = ::std::move(from);
  }

  inline RepresentativeDatasetConfig& operator=(const RepresentativeDatasetConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline RepresentativeDatasetConfig& operator=(RepresentativeDatasetConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RepresentativeDatasetConfig& default_instance() {
    return *internal_default_instance();
  }
  enum FileCase {
    kTfRecord = 1,
    FILE_NOT_SET = 0,
  };

  static inline const RepresentativeDatasetConfig* internal_default_instance() {
    return reinterpret_cast<const RepresentativeDatasetConfig*>(
               &_RepresentativeDatasetConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(RepresentativeDatasetConfig& a, RepresentativeDatasetConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(RepresentativeDatasetConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RepresentativeDatasetConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RepresentativeDatasetConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RepresentativeDatasetConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RepresentativeDatasetConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RepresentativeDatasetConfig& from) {
    RepresentativeDatasetConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RepresentativeDatasetConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.RepresentativeDatasetConfig";
  }
  protected:
  explicit RepresentativeDatasetConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSignatureKeyFieldNumber = 2,
    kTfRecordFieldNumber = 1,
  };
  // optional string signature_key = 2;
  bool has_signature_key() const;
  private:
  bool _internal_has_signature_key() const;
  public:
  void clear_signature_key();
  const std::string& signature_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_signature_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_signature_key();
  PROTOBUF_NODISCARD std::string* release_signature_key();
  void set_allocated_signature_key(std::string* signature_key);
  private:
  const std::string& _internal_signature_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_signature_key(const std::string& value);
  std::string* _internal_mutable_signature_key();
  public:

  // .stablehlo.quantization.TfRecordFile tf_record = 1;
  bool has_tf_record() const;
  private:
  bool _internal_has_tf_record() const;
  public:
  void clear_tf_record();
  const ::stablehlo::quantization::TfRecordFile& tf_record() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::TfRecordFile* release_tf_record();
  ::stablehlo::quantization::TfRecordFile* mutable_tf_record();
  void set_allocated_tf_record(::stablehlo::quantization::TfRecordFile* tf_record);
  private:
  const ::stablehlo::quantization::TfRecordFile& _internal_tf_record() const;
  ::stablehlo::quantization::TfRecordFile* _internal_mutable_tf_record();
  public:
  void unsafe_arena_set_allocated_tf_record(
      ::stablehlo::quantization::TfRecordFile* tf_record);
  ::stablehlo::quantization::TfRecordFile* unsafe_arena_release_tf_record();

  void clear_file();
  FileCase file_case() const;
  // @@protoc_insertion_point(class_scope:stablehlo.quantization.RepresentativeDatasetConfig)
 private:
  class _Internal;
  void set_has_tf_record();

  inline bool has_file() const;
  inline void clear_has_file();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr signature_key_;
    union FileUnion {
      constexpr FileUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::stablehlo::quantization::TfRecordFile* tf_record_;
    } file_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class StaticRangePtqPreset final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.StaticRangePtqPreset) */ {
 public:
  inline StaticRangePtqPreset() : StaticRangePtqPreset(nullptr) {}
  ~StaticRangePtqPreset() override;
  explicit PROTOBUF_CONSTEXPR StaticRangePtqPreset(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StaticRangePtqPreset(const StaticRangePtqPreset& from);
  StaticRangePtqPreset(StaticRangePtqPreset&& from) noexcept
    : StaticRangePtqPreset() {
    *this = ::std::move(from);
  }

  inline StaticRangePtqPreset& operator=(const StaticRangePtqPreset& from) {
    CopyFrom(from);
    return *this;
  }
  inline StaticRangePtqPreset& operator=(StaticRangePtqPreset&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StaticRangePtqPreset& default_instance() {
    return *internal_default_instance();
  }
  static inline const StaticRangePtqPreset* internal_default_instance() {
    return reinterpret_cast<const StaticRangePtqPreset*>(
               &_StaticRangePtqPreset_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(StaticRangePtqPreset& a, StaticRangePtqPreset& b) {
    a.Swap(&b);
  }
  inline void Swap(StaticRangePtqPreset* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StaticRangePtqPreset* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StaticRangePtqPreset* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StaticRangePtqPreset>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StaticRangePtqPreset& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StaticRangePtqPreset& from) {
    StaticRangePtqPreset::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StaticRangePtqPreset* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.StaticRangePtqPreset";
  }
  protected:
  explicit StaticRangePtqPreset(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRepresentativeDatasetsFieldNumber = 1,
    kEnablePerChannelQuantizedWeightFieldNumber = 2,
    kEnableFullIntQuantizationFieldNumber = 3,
  };
  // repeated .stablehlo.quantization.RepresentativeDatasetConfig representative_datasets = 1;
  int representative_datasets_size() const;
  private:
  int _internal_representative_datasets_size() const;
  public:
  void clear_representative_datasets();
  ::stablehlo::quantization::RepresentativeDatasetConfig* mutable_representative_datasets(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig >*
      mutable_representative_datasets();
  private:
  const ::stablehlo::quantization::RepresentativeDatasetConfig& _internal_representative_datasets(int index) const;
  ::stablehlo::quantization::RepresentativeDatasetConfig* _internal_add_representative_datasets();
  public:
  const ::stablehlo::quantization::RepresentativeDatasetConfig& representative_datasets(int index) const;
  ::stablehlo::quantization::RepresentativeDatasetConfig* add_representative_datasets();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig >&
      representative_datasets() const;

  // bool enable_per_channel_quantized_weight = 2 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_enable_per_channel_quantized_weight();
  PROTOBUF_DEPRECATED bool enable_per_channel_quantized_weight() const;
  PROTOBUF_DEPRECATED void set_enable_per_channel_quantized_weight(bool value);
  private:
  bool _internal_enable_per_channel_quantized_weight() const;
  void _internal_set_enable_per_channel_quantized_weight(bool value);
  public:

  // bool enable_full_int_quantization = 3;
  void clear_enable_full_int_quantization();
  bool enable_full_int_quantization() const;
  void set_enable_full_int_quantization(bool value);
  private:
  bool _internal_enable_full_int_quantization() const;
  void _internal_set_enable_full_int_quantization(bool value);
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.StaticRangePtqPreset)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig > representative_datasets_;
    bool enable_per_channel_quantized_weight_;
    bool enable_full_int_quantization_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class WeightOnlyPtqPreset final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:stablehlo.quantization.WeightOnlyPtqPreset) */ {
 public:
  inline WeightOnlyPtqPreset() : WeightOnlyPtqPreset(nullptr) {}
  explicit PROTOBUF_CONSTEXPR WeightOnlyPtqPreset(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WeightOnlyPtqPreset(const WeightOnlyPtqPreset& from);
  WeightOnlyPtqPreset(WeightOnlyPtqPreset&& from) noexcept
    : WeightOnlyPtqPreset() {
    *this = ::std::move(from);
  }

  inline WeightOnlyPtqPreset& operator=(const WeightOnlyPtqPreset& from) {
    CopyFrom(from);
    return *this;
  }
  inline WeightOnlyPtqPreset& operator=(WeightOnlyPtqPreset&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WeightOnlyPtqPreset& default_instance() {
    return *internal_default_instance();
  }
  static inline const WeightOnlyPtqPreset* internal_default_instance() {
    return reinterpret_cast<const WeightOnlyPtqPreset*>(
               &_WeightOnlyPtqPreset_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(WeightOnlyPtqPreset& a, WeightOnlyPtqPreset& b) {
    a.Swap(&b);
  }
  inline void Swap(WeightOnlyPtqPreset* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WeightOnlyPtqPreset* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WeightOnlyPtqPreset* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WeightOnlyPtqPreset>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const WeightOnlyPtqPreset& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const WeightOnlyPtqPreset& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.WeightOnlyPtqPreset";
  }
  protected:
  explicit WeightOnlyPtqPreset(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.WeightOnlyPtqPreset)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class TfSavedModelConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.TfSavedModelConfig) */ {
 public:
  inline TfSavedModelConfig() : TfSavedModelConfig(nullptr) {}
  ~TfSavedModelConfig() override;
  explicit PROTOBUF_CONSTEXPR TfSavedModelConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TfSavedModelConfig(const TfSavedModelConfig& from);
  TfSavedModelConfig(TfSavedModelConfig&& from) noexcept
    : TfSavedModelConfig() {
    *this = ::std::move(from);
  }

  inline TfSavedModelConfig& operator=(const TfSavedModelConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline TfSavedModelConfig& operator=(TfSavedModelConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TfSavedModelConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const TfSavedModelConfig* internal_default_instance() {
    return reinterpret_cast<const TfSavedModelConfig*>(
               &_TfSavedModelConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TfSavedModelConfig& a, TfSavedModelConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(TfSavedModelConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TfSavedModelConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TfSavedModelConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TfSavedModelConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TfSavedModelConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TfSavedModelConfig& from) {
    TfSavedModelConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TfSavedModelConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.TfSavedModelConfig";
  }
  protected:
  explicit TfSavedModelConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTagsFieldNumber = 1,
  };
  // repeated string tags = 1;
  int tags_size() const;
  private:
  int _internal_tags_size() const;
  public:
  void clear_tags();
  const std::string& tags(int index) const;
  std::string* mutable_tags(int index);
  void set_tags(int index, const std::string& value);
  void set_tags(int index, std::string&& value);
  void set_tags(int index, const char* value);
  void set_tags(int index, const char* value, size_t size);
  std::string* add_tags();
  void add_tags(const std::string& value);
  void add_tags(std::string&& value);
  void add_tags(const char* value);
  void add_tags(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& tags() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_tags();
  private:
  const std::string& _internal_tags(int index) const;
  std::string* _internal_add_tags();
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.TfSavedModelConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> tags_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class PipelineConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.PipelineConfig) */ {
 public:
  inline PipelineConfig() : PipelineConfig(nullptr) {}
  ~PipelineConfig() override;
  explicit PROTOBUF_CONSTEXPR PipelineConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PipelineConfig(const PipelineConfig& from);
  PipelineConfig(PipelineConfig&& from) noexcept
    : PipelineConfig() {
    *this = ::std::move(from);
  }

  inline PipelineConfig& operator=(const PipelineConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline PipelineConfig& operator=(PipelineConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PipelineConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const PipelineConfig* internal_default_instance() {
    return reinterpret_cast<const PipelineConfig*>(
               &_PipelineConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(PipelineConfig& a, PipelineConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(PipelineConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PipelineConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PipelineConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PipelineConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PipelineConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PipelineConfig& from) {
    PipelineConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PipelineConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.PipelineConfig";
  }
  protected:
  explicit PipelineConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUnpackQuantizedTypesFieldNumber = 1,
    kMergeFusionWithDequantizeFieldNumber = 2,
  };
  // optional bool unpack_quantized_types = 1;
  bool has_unpack_quantized_types() const;
  private:
  bool _internal_has_unpack_quantized_types() const;
  public:
  void clear_unpack_quantized_types();
  bool unpack_quantized_types() const;
  void set_unpack_quantized_types(bool value);
  private:
  bool _internal_unpack_quantized_types() const;
  void _internal_set_unpack_quantized_types(bool value);
  public:

  // bool merge_fusion_with_dequantize = 2;
  void clear_merge_fusion_with_dequantize();
  bool merge_fusion_with_dequantize() const;
  void set_merge_fusion_with_dequantize(bool value);
  private:
  bool _internal_merge_fusion_with_dequantize() const;
  void _internal_set_merge_fusion_with_dequantize(bool value);
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.PipelineConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    bool unpack_quantized_types_;
    bool merge_fusion_with_dequantize_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class QuantizableUnit final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizableUnit) */ {
 public:
  inline QuantizableUnit() : QuantizableUnit(nullptr) {}
  ~QuantizableUnit() override;
  explicit PROTOBUF_CONSTEXPR QuantizableUnit(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizableUnit(const QuantizableUnit& from);
  QuantizableUnit(QuantizableUnit&& from) noexcept
    : QuantizableUnit() {
    *this = ::std::move(from);
  }

  inline QuantizableUnit& operator=(const QuantizableUnit& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizableUnit& operator=(QuantizableUnit&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizableUnit& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizableUnit* internal_default_instance() {
    return reinterpret_cast<const QuantizableUnit*>(
               &_QuantizableUnit_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(QuantizableUnit& a, QuantizableUnit& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizableUnit* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizableUnit* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizableUnit* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizableUnit>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizableUnit& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizableUnit& from) {
    QuantizableUnit::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizableUnit* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizableUnit";
  }
  protected:
  explicit QuantizableUnit(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizableUnit)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class QuantizationResult final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizationResult) */ {
 public:
  inline QuantizationResult() : QuantizationResult(nullptr) {}
  ~QuantizationResult() override;
  explicit PROTOBUF_CONSTEXPR QuantizationResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationResult(const QuantizationResult& from);
  QuantizationResult(QuantizationResult&& from) noexcept
    : QuantizationResult() {
    *this = ::std::move(from);
  }

  inline QuantizationResult& operator=(const QuantizationResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationResult& operator=(QuantizationResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationResult& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationResult* internal_default_instance() {
    return reinterpret_cast<const QuantizationResult*>(
               &_QuantizationResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(QuantizationResult& a, QuantizationResult& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationResult>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationResult& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationResult& from) {
    QuantizationResult::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizationResult";
  }
  protected:
  explicit QuantizationResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQuantizableUnitFieldNumber = 1,
    kMethodFieldNumber = 2,
  };
  // .stablehlo.quantization.QuantizableUnit quantizable_unit = 1;
  bool has_quantizable_unit() const;
  private:
  bool _internal_has_quantizable_unit() const;
  public:
  void clear_quantizable_unit();
  const ::stablehlo::quantization::QuantizableUnit& quantizable_unit() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::QuantizableUnit* release_quantizable_unit();
  ::stablehlo::quantization::QuantizableUnit* mutable_quantizable_unit();
  void set_allocated_quantizable_unit(::stablehlo::quantization::QuantizableUnit* quantizable_unit);
  private:
  const ::stablehlo::quantization::QuantizableUnit& _internal_quantizable_unit() const;
  ::stablehlo::quantization::QuantizableUnit* _internal_mutable_quantizable_unit();
  public:
  void unsafe_arena_set_allocated_quantizable_unit(
      ::stablehlo::quantization::QuantizableUnit* quantizable_unit);
  ::stablehlo::quantization::QuantizableUnit* unsafe_arena_release_quantizable_unit();

  // .stablehlo.quantization.Method method = 2;
  bool has_method() const;
  private:
  bool _internal_has_method() const;
  public:
  void clear_method();
  const ::stablehlo::quantization::Method& method() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::Method* release_method();
  ::stablehlo::quantization::Method* mutable_method();
  void set_allocated_method(::stablehlo::quantization::Method* method);
  private:
  const ::stablehlo::quantization::Method& _internal_method() const;
  ::stablehlo::quantization::Method* _internal_mutable_method();
  public:
  void unsafe_arena_set_allocated_method(
      ::stablehlo::quantization::Method* method);
  ::stablehlo::quantization::Method* unsafe_arena_release_method();

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizationResult)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::stablehlo::quantization::QuantizableUnit* quantizable_unit_;
    ::stablehlo::quantization::Method* method_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class QuantizationResults final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizationResults) */ {
 public:
  inline QuantizationResults() : QuantizationResults(nullptr) {}
  ~QuantizationResults() override;
  explicit PROTOBUF_CONSTEXPR QuantizationResults(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationResults(const QuantizationResults& from);
  QuantizationResults(QuantizationResults&& from) noexcept
    : QuantizationResults() {
    *this = ::std::move(from);
  }

  inline QuantizationResults& operator=(const QuantizationResults& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationResults& operator=(QuantizationResults&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationResults& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationResults* internal_default_instance() {
    return reinterpret_cast<const QuantizationResults*>(
               &_QuantizationResults_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(QuantizationResults& a, QuantizationResults& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationResults* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationResults* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationResults* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationResults>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationResults& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationResults& from) {
    QuantizationResults::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationResults* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizationResults";
  }
  protected:
  explicit QuantizationResults(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResultsFieldNumber = 1,
  };
  // repeated .stablehlo.quantization.QuantizationResult results = 1;
  int results_size() const;
  private:
  int _internal_results_size() const;
  public:
  void clear_results();
  ::stablehlo::quantization::QuantizationResult* mutable_results(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationResult >*
      mutable_results();
  private:
  const ::stablehlo::quantization::QuantizationResult& _internal_results(int index) const;
  ::stablehlo::quantization::QuantizationResult* _internal_add_results();
  public:
  const ::stablehlo::quantization::QuantizationResult& results(int index) const;
  ::stablehlo::quantization::QuantizationResult* add_results();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationResult >&
      results() const;

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizationResults)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationResult > results_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class QuantizedDimension final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizedDimension) */ {
 public:
  inline QuantizedDimension() : QuantizedDimension(nullptr) {}
  ~QuantizedDimension() override;
  explicit PROTOBUF_CONSTEXPR QuantizedDimension(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizedDimension(const QuantizedDimension& from);
  QuantizedDimension(QuantizedDimension&& from) noexcept
    : QuantizedDimension() {
    *this = ::std::move(from);
  }

  inline QuantizedDimension& operator=(const QuantizedDimension& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizedDimension& operator=(QuantizedDimension&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizedDimension& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizedDimension* internal_default_instance() {
    return reinterpret_cast<const QuantizedDimension*>(
               &_QuantizedDimension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(QuantizedDimension& a, QuantizedDimension& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizedDimension* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizedDimension* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizedDimension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizedDimension>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizedDimension& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizedDimension& from) {
    QuantizedDimension::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizedDimension* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizedDimension";
  }
  protected:
  explicit QuantizedDimension(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionFieldNumber = 1,
  };
  // optional int32 dimension = 1;
  bool has_dimension() const;
  private:
  bool _internal_has_dimension() const;
  public:
  void clear_dimension();
  int32_t dimension() const;
  void set_dimension(int32_t value);
  private:
  int32_t _internal_dimension() const;
  void _internal_set_dimension(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizedDimension)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    int32_t dimension_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class PerTensor final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:stablehlo.quantization.PerTensor) */ {
 public:
  inline PerTensor() : PerTensor(nullptr) {}
  explicit PROTOBUF_CONSTEXPR PerTensor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PerTensor(const PerTensor& from);
  PerTensor(PerTensor&& from) noexcept
    : PerTensor() {
    *this = ::std::move(from);
  }

  inline PerTensor& operator=(const PerTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline PerTensor& operator=(PerTensor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PerTensor& default_instance() {
    return *internal_default_instance();
  }
  static inline const PerTensor* internal_default_instance() {
    return reinterpret_cast<const PerTensor*>(
               &_PerTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(PerTensor& a, PerTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(PerTensor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PerTensor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PerTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PerTensor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const PerTensor& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const PerTensor& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.PerTensor";
  }
  protected:
  explicit PerTensor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.PerTensor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class QuantizedType final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizedType) */ {
 public:
  inline QuantizedType() : QuantizedType(nullptr) {}
  ~QuantizedType() override;
  explicit PROTOBUF_CONSTEXPR QuantizedType(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizedType(const QuantizedType& from);
  QuantizedType(QuantizedType&& from) noexcept
    : QuantizedType() {
    *this = ::std::move(from);
  }

  inline QuantizedType& operator=(const QuantizedType& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizedType& operator=(QuantizedType&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizedType& default_instance() {
    return *internal_default_instance();
  }
  enum TypeCase {
    kDimensionSpecs = 1,
    kPerTensor = 2,
    TYPE_NOT_SET = 0,
  };

  static inline const QuantizedType* internal_default_instance() {
    return reinterpret_cast<const QuantizedType*>(
               &_QuantizedType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(QuantizedType& a, QuantizedType& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizedType* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizedType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizedType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizedType>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizedType& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizedType& from) {
    QuantizedType::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizedType* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizedType";
  }
  protected:
  explicit QuantizedType(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimensionSpecsFieldNumber = 1,
    kPerTensorFieldNumber = 2,
  };
  // .stablehlo.quantization.QuantizedDimension dimension_specs = 1;
  bool has_dimension_specs() const;
  private:
  bool _internal_has_dimension_specs() const;
  public:
  void clear_dimension_specs();
  const ::stablehlo::quantization::QuantizedDimension& dimension_specs() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::QuantizedDimension* release_dimension_specs();
  ::stablehlo::quantization::QuantizedDimension* mutable_dimension_specs();
  void set_allocated_dimension_specs(::stablehlo::quantization::QuantizedDimension* dimension_specs);
  private:
  const ::stablehlo::quantization::QuantizedDimension& _internal_dimension_specs() const;
  ::stablehlo::quantization::QuantizedDimension* _internal_mutable_dimension_specs();
  public:
  void unsafe_arena_set_allocated_dimension_specs(
      ::stablehlo::quantization::QuantizedDimension* dimension_specs);
  ::stablehlo::quantization::QuantizedDimension* unsafe_arena_release_dimension_specs();

  // .stablehlo.quantization.PerTensor per_tensor = 2;
  bool has_per_tensor() const;
  private:
  bool _internal_has_per_tensor() const;
  public:
  void clear_per_tensor();
  const ::stablehlo::quantization::PerTensor& per_tensor() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::PerTensor* release_per_tensor();
  ::stablehlo::quantization::PerTensor* mutable_per_tensor();
  void set_allocated_per_tensor(::stablehlo::quantization::PerTensor* per_tensor);
  private:
  const ::stablehlo::quantization::PerTensor& _internal_per_tensor() const;
  ::stablehlo::quantization::PerTensor* _internal_mutable_per_tensor();
  public:
  void unsafe_arena_set_allocated_per_tensor(
      ::stablehlo::quantization::PerTensor* per_tensor);
  ::stablehlo::quantization::PerTensor* unsafe_arena_release_per_tensor();

  void clear_type();
  TypeCase type_case() const;
  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizedType)
 private:
  class _Internal;
  void set_has_dimension_specs();
  void set_has_per_tensor();

  inline bool has_type() const;
  inline void clear_has_type();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union TypeUnion {
      constexpr TypeUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::stablehlo::quantization::QuantizedDimension* dimension_specs_;
      ::stablehlo::quantization::PerTensor* per_tensor_;
    } type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class NoQuantization final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:stablehlo.quantization.NoQuantization) */ {
 public:
  inline NoQuantization() : NoQuantization(nullptr) {}
  explicit PROTOBUF_CONSTEXPR NoQuantization(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NoQuantization(const NoQuantization& from);
  NoQuantization(NoQuantization&& from) noexcept
    : NoQuantization() {
    *this = ::std::move(from);
  }

  inline NoQuantization& operator=(const NoQuantization& from) {
    CopyFrom(from);
    return *this;
  }
  inline NoQuantization& operator=(NoQuantization&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NoQuantization& default_instance() {
    return *internal_default_instance();
  }
  static inline const NoQuantization* internal_default_instance() {
    return reinterpret_cast<const NoQuantization*>(
               &_NoQuantization_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(NoQuantization& a, NoQuantization& b) {
    a.Swap(&b);
  }
  inline void Swap(NoQuantization* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NoQuantization* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NoQuantization* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NoQuantization>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const NoQuantization& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const NoQuantization& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.NoQuantization";
  }
  protected:
  explicit NoQuantization(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.NoQuantization)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class StaticRangePtq_InputQuantizedTypesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<StaticRangePtq_InputQuantizedTypesEntry_DoNotUse, 
    int32_t, ::stablehlo::quantization::QuantizedType,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<StaticRangePtq_InputQuantizedTypesEntry_DoNotUse, 
    int32_t, ::stablehlo::quantization::QuantizedType,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  StaticRangePtq_InputQuantizedTypesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR StaticRangePtq_InputQuantizedTypesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit StaticRangePtq_InputQuantizedTypesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const StaticRangePtq_InputQuantizedTypesEntry_DoNotUse& other);
  static const StaticRangePtq_InputQuantizedTypesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const StaticRangePtq_InputQuantizedTypesEntry_DoNotUse*>(&_StaticRangePtq_InputQuantizedTypesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};

// -------------------------------------------------------------------

class StaticRangePtq final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.StaticRangePtq) */ {
 public:
  inline StaticRangePtq() : StaticRangePtq(nullptr) {}
  ~StaticRangePtq() override;
  explicit PROTOBUF_CONSTEXPR StaticRangePtq(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StaticRangePtq(const StaticRangePtq& from);
  StaticRangePtq(StaticRangePtq&& from) noexcept
    : StaticRangePtq() {
    *this = ::std::move(from);
  }

  inline StaticRangePtq& operator=(const StaticRangePtq& from) {
    CopyFrom(from);
    return *this;
  }
  inline StaticRangePtq& operator=(StaticRangePtq&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StaticRangePtq& default_instance() {
    return *internal_default_instance();
  }
  static inline const StaticRangePtq* internal_default_instance() {
    return reinterpret_cast<const StaticRangePtq*>(
               &_StaticRangePtq_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(StaticRangePtq& a, StaticRangePtq& b) {
    a.Swap(&b);
  }
  inline void Swap(StaticRangePtq* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StaticRangePtq* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StaticRangePtq* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StaticRangePtq>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StaticRangePtq& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StaticRangePtq& from) {
    StaticRangePtq::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StaticRangePtq* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.StaticRangePtq";
  }
  protected:
  explicit StaticRangePtq(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kInputQuantizedTypesFieldNumber = 1,
  };
  // map<int32, .stablehlo.quantization.QuantizedType> input_quantized_types = 1;
  int input_quantized_types_size() const;
  private:
  int _internal_input_quantized_types_size() const;
  public:
  void clear_input_quantized_types();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >&
      _internal_input_quantized_types() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >*
      _internal_mutable_input_quantized_types();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >&
      input_quantized_types() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >*
      mutable_input_quantized_types();

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.StaticRangePtq)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        StaticRangePtq_InputQuantizedTypesEntry_DoNotUse,
        int32_t, ::stablehlo::quantization::QuantizedType,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> input_quantized_types_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse, 
    int32_t, ::stablehlo::quantization::QuantizedType,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse, 
    int32_t, ::stablehlo::quantization::QuantizedType,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse& other);
  static const WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse*>(&_WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};

// -------------------------------------------------------------------

class WeightOnlyPtq final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.WeightOnlyPtq) */ {
 public:
  inline WeightOnlyPtq() : WeightOnlyPtq(nullptr) {}
  ~WeightOnlyPtq() override;
  explicit PROTOBUF_CONSTEXPR WeightOnlyPtq(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WeightOnlyPtq(const WeightOnlyPtq& from);
  WeightOnlyPtq(WeightOnlyPtq&& from) noexcept
    : WeightOnlyPtq() {
    *this = ::std::move(from);
  }

  inline WeightOnlyPtq& operator=(const WeightOnlyPtq& from) {
    CopyFrom(from);
    return *this;
  }
  inline WeightOnlyPtq& operator=(WeightOnlyPtq&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WeightOnlyPtq& default_instance() {
    return *internal_default_instance();
  }
  static inline const WeightOnlyPtq* internal_default_instance() {
    return reinterpret_cast<const WeightOnlyPtq*>(
               &_WeightOnlyPtq_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(WeightOnlyPtq& a, WeightOnlyPtq& b) {
    a.Swap(&b);
  }
  inline void Swap(WeightOnlyPtq* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WeightOnlyPtq* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WeightOnlyPtq* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WeightOnlyPtq>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WeightOnlyPtq& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WeightOnlyPtq& from) {
    WeightOnlyPtq::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WeightOnlyPtq* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.WeightOnlyPtq";
  }
  protected:
  explicit WeightOnlyPtq(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kInputQuantizedTypesFieldNumber = 1,
  };
  // map<int32, .stablehlo.quantization.QuantizedType> input_quantized_types = 1;
  int input_quantized_types_size() const;
  private:
  int _internal_input_quantized_types_size() const;
  public:
  void clear_input_quantized_types();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >&
      _internal_input_quantized_types() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >*
      _internal_mutable_input_quantized_types();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >&
      input_quantized_types() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >*
      mutable_input_quantized_types();

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.WeightOnlyPtq)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        WeightOnlyPtq_InputQuantizedTypesEntry_DoNotUse,
        int32_t, ::stablehlo::quantization::QuantizedType,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> input_quantized_types_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class FunctionNameMatcherSpec final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.FunctionNameMatcherSpec) */ {
 public:
  inline FunctionNameMatcherSpec() : FunctionNameMatcherSpec(nullptr) {}
  ~FunctionNameMatcherSpec() override;
  explicit PROTOBUF_CONSTEXPR FunctionNameMatcherSpec(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunctionNameMatcherSpec(const FunctionNameMatcherSpec& from);
  FunctionNameMatcherSpec(FunctionNameMatcherSpec&& from) noexcept
    : FunctionNameMatcherSpec() {
    *this = ::std::move(from);
  }

  inline FunctionNameMatcherSpec& operator=(const FunctionNameMatcherSpec& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionNameMatcherSpec& operator=(FunctionNameMatcherSpec&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunctionNameMatcherSpec& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunctionNameMatcherSpec* internal_default_instance() {
    return reinterpret_cast<const FunctionNameMatcherSpec*>(
               &_FunctionNameMatcherSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(FunctionNameMatcherSpec& a, FunctionNameMatcherSpec& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionNameMatcherSpec* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionNameMatcherSpec* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FunctionNameMatcherSpec* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FunctionNameMatcherSpec>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunctionNameMatcherSpec& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FunctionNameMatcherSpec& from) {
    FunctionNameMatcherSpec::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionNameMatcherSpec* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.FunctionNameMatcherSpec";
  }
  protected:
  explicit FunctionNameMatcherSpec(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRegexFieldNumber = 1,
  };
  // string regex = 1;
  void clear_regex();
  const std::string& regex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_regex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_regex();
  PROTOBUF_NODISCARD std::string* release_regex();
  void set_allocated_regex(std::string* regex);
  private:
  const std::string& _internal_regex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_regex(const std::string& value);
  std::string* _internal_mutable_regex();
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.FunctionNameMatcherSpec)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr regex_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class MatcherSpec final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.MatcherSpec) */ {
 public:
  inline MatcherSpec() : MatcherSpec(nullptr) {}
  ~MatcherSpec() override;
  explicit PROTOBUF_CONSTEXPR MatcherSpec(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MatcherSpec(const MatcherSpec& from);
  MatcherSpec(MatcherSpec&& from) noexcept
    : MatcherSpec() {
    *this = ::std::move(from);
  }

  inline MatcherSpec& operator=(const MatcherSpec& from) {
    CopyFrom(from);
    return *this;
  }
  inline MatcherSpec& operator=(MatcherSpec&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MatcherSpec& default_instance() {
    return *internal_default_instance();
  }
  static inline const MatcherSpec* internal_default_instance() {
    return reinterpret_cast<const MatcherSpec*>(
               &_MatcherSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(MatcherSpec& a, MatcherSpec& b) {
    a.Swap(&b);
  }
  inline void Swap(MatcherSpec* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MatcherSpec* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MatcherSpec* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MatcherSpec>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MatcherSpec& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MatcherSpec& from) {
    MatcherSpec::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MatcherSpec* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.MatcherSpec";
  }
  protected:
  explicit MatcherSpec(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionNameFieldNumber = 1,
  };
  // .stablehlo.quantization.FunctionNameMatcherSpec function_name = 1;
  bool has_function_name() const;
  private:
  bool _internal_has_function_name() const;
  public:
  void clear_function_name();
  const ::stablehlo::quantization::FunctionNameMatcherSpec& function_name() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::FunctionNameMatcherSpec* release_function_name();
  ::stablehlo::quantization::FunctionNameMatcherSpec* mutable_function_name();
  void set_allocated_function_name(::stablehlo::quantization::FunctionNameMatcherSpec* function_name);
  private:
  const ::stablehlo::quantization::FunctionNameMatcherSpec& _internal_function_name() const;
  ::stablehlo::quantization::FunctionNameMatcherSpec* _internal_mutable_function_name();
  public:
  void unsafe_arena_set_allocated_function_name(
      ::stablehlo::quantization::FunctionNameMatcherSpec* function_name);
  ::stablehlo::quantization::FunctionNameMatcherSpec* unsafe_arena_release_function_name();

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.MatcherSpec)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::stablehlo::quantization::FunctionNameMatcherSpec* function_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class Method final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.Method) */ {
 public:
  inline Method() : Method(nullptr) {}
  ~Method() override;
  explicit PROTOBUF_CONSTEXPR Method(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Method(const Method& from);
  Method(Method&& from) noexcept
    : Method() {
    *this = ::std::move(from);
  }

  inline Method& operator=(const Method& from) {
    CopyFrom(from);
    return *this;
  }
  inline Method& operator=(Method&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Method& default_instance() {
    return *internal_default_instance();
  }
  enum MethodCase {
    kNoQuantization = 1,
    kStaticRangePtq = 2,
    kWeightOnlyPtq = 3,
    METHOD_NOT_SET = 0,
  };

  static inline const Method* internal_default_instance() {
    return reinterpret_cast<const Method*>(
               &_Method_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(Method& a, Method& b) {
    a.Swap(&b);
  }
  inline void Swap(Method* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Method* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Method* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Method>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Method& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Method& from) {
    Method::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Method* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.Method";
  }
  protected:
  explicit Method(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNoQuantizationFieldNumber = 1,
    kStaticRangePtqFieldNumber = 2,
    kWeightOnlyPtqFieldNumber = 3,
  };
  // .stablehlo.quantization.NoQuantization no_quantization = 1;
  bool has_no_quantization() const;
  private:
  bool _internal_has_no_quantization() const;
  public:
  void clear_no_quantization();
  const ::stablehlo::quantization::NoQuantization& no_quantization() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::NoQuantization* release_no_quantization();
  ::stablehlo::quantization::NoQuantization* mutable_no_quantization();
  void set_allocated_no_quantization(::stablehlo::quantization::NoQuantization* no_quantization);
  private:
  const ::stablehlo::quantization::NoQuantization& _internal_no_quantization() const;
  ::stablehlo::quantization::NoQuantization* _internal_mutable_no_quantization();
  public:
  void unsafe_arena_set_allocated_no_quantization(
      ::stablehlo::quantization::NoQuantization* no_quantization);
  ::stablehlo::quantization::NoQuantization* unsafe_arena_release_no_quantization();

  // .stablehlo.quantization.StaticRangePtq static_range_ptq = 2;
  bool has_static_range_ptq() const;
  private:
  bool _internal_has_static_range_ptq() const;
  public:
  void clear_static_range_ptq();
  const ::stablehlo::quantization::StaticRangePtq& static_range_ptq() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::StaticRangePtq* release_static_range_ptq();
  ::stablehlo::quantization::StaticRangePtq* mutable_static_range_ptq();
  void set_allocated_static_range_ptq(::stablehlo::quantization::StaticRangePtq* static_range_ptq);
  private:
  const ::stablehlo::quantization::StaticRangePtq& _internal_static_range_ptq() const;
  ::stablehlo::quantization::StaticRangePtq* _internal_mutable_static_range_ptq();
  public:
  void unsafe_arena_set_allocated_static_range_ptq(
      ::stablehlo::quantization::StaticRangePtq* static_range_ptq);
  ::stablehlo::quantization::StaticRangePtq* unsafe_arena_release_static_range_ptq();

  // .stablehlo.quantization.WeightOnlyPtq weight_only_ptq = 3;
  bool has_weight_only_ptq() const;
  private:
  bool _internal_has_weight_only_ptq() const;
  public:
  void clear_weight_only_ptq();
  const ::stablehlo::quantization::WeightOnlyPtq& weight_only_ptq() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::WeightOnlyPtq* release_weight_only_ptq();
  ::stablehlo::quantization::WeightOnlyPtq* mutable_weight_only_ptq();
  void set_allocated_weight_only_ptq(::stablehlo::quantization::WeightOnlyPtq* weight_only_ptq);
  private:
  const ::stablehlo::quantization::WeightOnlyPtq& _internal_weight_only_ptq() const;
  ::stablehlo::quantization::WeightOnlyPtq* _internal_mutable_weight_only_ptq();
  public:
  void unsafe_arena_set_allocated_weight_only_ptq(
      ::stablehlo::quantization::WeightOnlyPtq* weight_only_ptq);
  ::stablehlo::quantization::WeightOnlyPtq* unsafe_arena_release_weight_only_ptq();

  void clear_method();
  MethodCase method_case() const;
  // @@protoc_insertion_point(class_scope:stablehlo.quantization.Method)
 private:
  class _Internal;
  void set_has_no_quantization();
  void set_has_static_range_ptq();
  void set_has_weight_only_ptq();

  inline bool has_method() const;
  inline void clear_has_method();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union MethodUnion {
      constexpr MethodUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::stablehlo::quantization::NoQuantization* no_quantization_;
      ::stablehlo::quantization::StaticRangePtq* static_range_ptq_;
      ::stablehlo::quantization::WeightOnlyPtq* weight_only_ptq_;
    } method_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class QuantizationSpec final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizationSpec) */ {
 public:
  inline QuantizationSpec() : QuantizationSpec(nullptr) {}
  ~QuantizationSpec() override;
  explicit PROTOBUF_CONSTEXPR QuantizationSpec(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationSpec(const QuantizationSpec& from);
  QuantizationSpec(QuantizationSpec&& from) noexcept
    : QuantizationSpec() {
    *this = ::std::move(from);
  }

  inline QuantizationSpec& operator=(const QuantizationSpec& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationSpec& operator=(QuantizationSpec&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationSpec& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationSpec* internal_default_instance() {
    return reinterpret_cast<const QuantizationSpec*>(
               &_QuantizationSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(QuantizationSpec& a, QuantizationSpec& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationSpec* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationSpec* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationSpec* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationSpec>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationSpec& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationSpec& from) {
    QuantizationSpec::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationSpec* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizationSpec";
  }
  protected:
  explicit QuantizationSpec(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMatcherFieldNumber = 1,
    kMethodFieldNumber = 2,
  };
  // .stablehlo.quantization.MatcherSpec matcher = 1;
  bool has_matcher() const;
  private:
  bool _internal_has_matcher() const;
  public:
  void clear_matcher();
  const ::stablehlo::quantization::MatcherSpec& matcher() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::MatcherSpec* release_matcher();
  ::stablehlo::quantization::MatcherSpec* mutable_matcher();
  void set_allocated_matcher(::stablehlo::quantization::MatcherSpec* matcher);
  private:
  const ::stablehlo::quantization::MatcherSpec& _internal_matcher() const;
  ::stablehlo::quantization::MatcherSpec* _internal_mutable_matcher();
  public:
  void unsafe_arena_set_allocated_matcher(
      ::stablehlo::quantization::MatcherSpec* matcher);
  ::stablehlo::quantization::MatcherSpec* unsafe_arena_release_matcher();

  // .stablehlo.quantization.Method method = 2;
  bool has_method() const;
  private:
  bool _internal_has_method() const;
  public:
  void clear_method();
  const ::stablehlo::quantization::Method& method() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::Method* release_method();
  ::stablehlo::quantization::Method* mutable_method();
  void set_allocated_method(::stablehlo::quantization::Method* method);
  private:
  const ::stablehlo::quantization::Method& _internal_method() const;
  ::stablehlo::quantization::Method* _internal_mutable_method();
  public:
  void unsafe_arena_set_allocated_method(
      ::stablehlo::quantization::Method* method);
  ::stablehlo::quantization::Method* unsafe_arena_release_method();

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizationSpec)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::stablehlo::quantization::MatcherSpec* matcher_;
    ::stablehlo::quantization::Method* method_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class QuantizationSpecs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizationSpecs) */ {
 public:
  inline QuantizationSpecs() : QuantizationSpecs(nullptr) {}
  ~QuantizationSpecs() override;
  explicit PROTOBUF_CONSTEXPR QuantizationSpecs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationSpecs(const QuantizationSpecs& from);
  QuantizationSpecs(QuantizationSpecs&& from) noexcept
    : QuantizationSpecs() {
    *this = ::std::move(from);
  }

  inline QuantizationSpecs& operator=(const QuantizationSpecs& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationSpecs& operator=(QuantizationSpecs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationSpecs& default_instance() {
    return *internal_default_instance();
  }
  static inline const QuantizationSpecs* internal_default_instance() {
    return reinterpret_cast<const QuantizationSpecs*>(
               &_QuantizationSpecs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(QuantizationSpecs& a, QuantizationSpecs& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationSpecs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationSpecs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationSpecs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationSpecs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationSpecs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationSpecs& from) {
    QuantizationSpecs::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationSpecs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizationSpecs";
  }
  protected:
  explicit QuantizationSpecs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSpecsFieldNumber = 1,
  };
  // repeated .stablehlo.quantization.QuantizationSpec specs = 1;
  int specs_size() const;
  private:
  int _internal_specs_size() const;
  public:
  void clear_specs();
  ::stablehlo::quantization::QuantizationSpec* mutable_specs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationSpec >*
      mutable_specs();
  private:
  const ::stablehlo::quantization::QuantizationSpec& _internal_specs(int index) const;
  ::stablehlo::quantization::QuantizationSpec* _internal_add_specs();
  public:
  const ::stablehlo::quantization::QuantizationSpec& specs(int index) const;
  ::stablehlo::quantization::QuantizationSpec* add_specs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationSpec >&
      specs() const;

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizationSpecs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationSpec > specs_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class DebuggerConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.DebuggerConfig) */ {
 public:
  inline DebuggerConfig() : DebuggerConfig(nullptr) {}
  ~DebuggerConfig() override;
  explicit PROTOBUF_CONSTEXPR DebuggerConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebuggerConfig(const DebuggerConfig& from);
  DebuggerConfig(DebuggerConfig&& from) noexcept
    : DebuggerConfig() {
    *this = ::std::move(from);
  }

  inline DebuggerConfig& operator=(const DebuggerConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggerConfig& operator=(DebuggerConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebuggerConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebuggerConfig* internal_default_instance() {
    return reinterpret_cast<const DebuggerConfig*>(
               &_DebuggerConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(DebuggerConfig& a, DebuggerConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggerConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggerConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebuggerConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebuggerConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebuggerConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebuggerConfig& from) {
    DebuggerConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggerConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.DebuggerConfig";
  }
  protected:
  explicit DebuggerConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef DebuggerConfig_DebuggerType DebuggerType;
  static constexpr DebuggerType DEBUGGER_TYPE_UNSPECIFIED =
    DebuggerConfig_DebuggerType_DEBUGGER_TYPE_UNSPECIFIED;
  static constexpr DebuggerType DEBUGGER_TYPE_WHOLE_MODEL =
    DebuggerConfig_DebuggerType_DEBUGGER_TYPE_WHOLE_MODEL;
  static constexpr DebuggerType DEBUGGER_TYPE_INT_PER_LAYER =
    DebuggerConfig_DebuggerType_DEBUGGER_TYPE_INT_PER_LAYER;
  static constexpr DebuggerType DEBUGGER_TYPE_FLOAT_PER_LAYER =
    DebuggerConfig_DebuggerType_DEBUGGER_TYPE_FLOAT_PER_LAYER;
  static inline bool DebuggerType_IsValid(int value) {
    return DebuggerConfig_DebuggerType_IsValid(value);
  }
  static constexpr DebuggerType DebuggerType_MIN =
    DebuggerConfig_DebuggerType_DebuggerType_MIN;
  static constexpr DebuggerType DebuggerType_MAX =
    DebuggerConfig_DebuggerType_DebuggerType_MAX;
  static constexpr int DebuggerType_ARRAYSIZE =
    DebuggerConfig_DebuggerType_DebuggerType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  DebuggerType_descriptor() {
    return DebuggerConfig_DebuggerType_descriptor();
  }
  template<typename T>
  static inline const std::string& DebuggerType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, DebuggerType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function DebuggerType_Name.");
    return DebuggerConfig_DebuggerType_Name(enum_t_value);
  }
  static inline bool DebuggerType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      DebuggerType* value) {
    return DebuggerConfig_DebuggerType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kUnquantizedDumpModelPathFieldNumber = 2,
    kLogDirPathFieldNumber = 3,
    kDebuggerTypeFieldNumber = 1,
  };
  // string unquantized_dump_model_path = 2;
  void clear_unquantized_dump_model_path();
  const std::string& unquantized_dump_model_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_unquantized_dump_model_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_unquantized_dump_model_path();
  PROTOBUF_NODISCARD std::string* release_unquantized_dump_model_path();
  void set_allocated_unquantized_dump_model_path(std::string* unquantized_dump_model_path);
  private:
  const std::string& _internal_unquantized_dump_model_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_unquantized_dump_model_path(const std::string& value);
  std::string* _internal_mutable_unquantized_dump_model_path();
  public:

  // string log_dir_path = 3;
  void clear_log_dir_path();
  const std::string& log_dir_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_log_dir_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_log_dir_path();
  PROTOBUF_NODISCARD std::string* release_log_dir_path();
  void set_allocated_log_dir_path(std::string* log_dir_path);
  private:
  const std::string& _internal_log_dir_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_log_dir_path(const std::string& value);
  std::string* _internal_mutable_log_dir_path();
  public:

  // .stablehlo.quantization.DebuggerConfig.DebuggerType debugger_type = 1;
  void clear_debugger_type();
  ::stablehlo::quantization::DebuggerConfig_DebuggerType debugger_type() const;
  void set_debugger_type(::stablehlo::quantization::DebuggerConfig_DebuggerType value);
  private:
  ::stablehlo::quantization::DebuggerConfig_DebuggerType _internal_debugger_type() const;
  void _internal_set_debugger_type(::stablehlo::quantization::DebuggerConfig_DebuggerType value);
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.DebuggerConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr unquantized_dump_model_path_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr log_dir_path_;
    int debugger_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class CalibrationOptions_CalibrationParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.CalibrationOptions.CalibrationParameters) */ {
 public:
  inline CalibrationOptions_CalibrationParameters() : CalibrationOptions_CalibrationParameters(nullptr) {}
  ~CalibrationOptions_CalibrationParameters() override;
  explicit PROTOBUF_CONSTEXPR CalibrationOptions_CalibrationParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalibrationOptions_CalibrationParameters(const CalibrationOptions_CalibrationParameters& from);
  CalibrationOptions_CalibrationParameters(CalibrationOptions_CalibrationParameters&& from) noexcept
    : CalibrationOptions_CalibrationParameters() {
    *this = ::std::move(from);
  }

  inline CalibrationOptions_CalibrationParameters& operator=(const CalibrationOptions_CalibrationParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalibrationOptions_CalibrationParameters& operator=(CalibrationOptions_CalibrationParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalibrationOptions_CalibrationParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalibrationOptions_CalibrationParameters* internal_default_instance() {
    return reinterpret_cast<const CalibrationOptions_CalibrationParameters*>(
               &_CalibrationOptions_CalibrationParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(CalibrationOptions_CalibrationParameters& a, CalibrationOptions_CalibrationParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(CalibrationOptions_CalibrationParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalibrationOptions_CalibrationParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalibrationOptions_CalibrationParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalibrationOptions_CalibrationParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalibrationOptions_CalibrationParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CalibrationOptions_CalibrationParameters& from) {
    CalibrationOptions_CalibrationParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalibrationOptions_CalibrationParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.CalibrationOptions.CalibrationParameters";
  }
  protected:
  explicit CalibrationOptions_CalibrationParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNumBinsFieldNumber = 1,
    kMinPercentileFieldNumber = 2,
    kMaxPercentileFieldNumber = 3,
  };
  // int32 num_bins = 1;
  void clear_num_bins();
  int32_t num_bins() const;
  void set_num_bins(int32_t value);
  private:
  int32_t _internal_num_bins() const;
  void _internal_set_num_bins(int32_t value);
  public:

  // float min_percentile = 2;
  void clear_min_percentile();
  float min_percentile() const;
  void set_min_percentile(float value);
  private:
  float _internal_min_percentile() const;
  void _internal_set_min_percentile(float value);
  public:

  // float max_percentile = 3;
  void clear_max_percentile();
  float max_percentile() const;
  void set_max_percentile(float value);
  private:
  float _internal_max_percentile() const;
  void _internal_set_max_percentile(float value);
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.CalibrationOptions.CalibrationParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t num_bins_;
    float min_percentile_;
    float max_percentile_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class CalibrationOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.CalibrationOptions) */ {
 public:
  inline CalibrationOptions() : CalibrationOptions(nullptr) {}
  ~CalibrationOptions() override;
  explicit PROTOBUF_CONSTEXPR CalibrationOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalibrationOptions(const CalibrationOptions& from);
  CalibrationOptions(CalibrationOptions&& from) noexcept
    : CalibrationOptions() {
    *this = ::std::move(from);
  }

  inline CalibrationOptions& operator=(const CalibrationOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalibrationOptions& operator=(CalibrationOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalibrationOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalibrationOptions* internal_default_instance() {
    return reinterpret_cast<const CalibrationOptions*>(
               &_CalibrationOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(CalibrationOptions& a, CalibrationOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(CalibrationOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalibrationOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalibrationOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalibrationOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalibrationOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CalibrationOptions& from) {
    CalibrationOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalibrationOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.CalibrationOptions";
  }
  protected:
  explicit CalibrationOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CalibrationOptions_CalibrationParameters CalibrationParameters;

  typedef CalibrationOptions_CalibrationMethod CalibrationMethod;
  static constexpr CalibrationMethod CALIBRATION_METHOD_UNSPECIFIED =
    CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_UNSPECIFIED;
  static constexpr CalibrationMethod CALIBRATION_METHOD_MIN_MAX =
    CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_MIN_MAX;
  static constexpr CalibrationMethod CALIBRATION_METHOD_AVERAGE_MIN_MAX =
    CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_AVERAGE_MIN_MAX;
  static constexpr CalibrationMethod CALIBRATION_METHOD_HISTOGRAM_PERCENTILE =
    CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_PERCENTILE;
  static constexpr CalibrationMethod CALIBRATION_METHOD_HISTOGRAM_MSE_BRUTEFORCE =
    CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_MSE_BRUTEFORCE;
  static constexpr CalibrationMethod CALIBRATION_METHOD_HISTOGRAM_MSE_MAX_FREQUENCY =
    CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_MSE_MAX_FREQUENCY;
  static constexpr CalibrationMethod CALIBRATION_METHOD_HISTOGRAM_MSE_SYMMETRIC =
    CalibrationOptions_CalibrationMethod_CALIBRATION_METHOD_HISTOGRAM_MSE_SYMMETRIC;
  static inline bool CalibrationMethod_IsValid(int value) {
    return CalibrationOptions_CalibrationMethod_IsValid(value);
  }
  static constexpr CalibrationMethod CalibrationMethod_MIN =
    CalibrationOptions_CalibrationMethod_CalibrationMethod_MIN;
  static constexpr CalibrationMethod CalibrationMethod_MAX =
    CalibrationOptions_CalibrationMethod_CalibrationMethod_MAX;
  static constexpr int CalibrationMethod_ARRAYSIZE =
    CalibrationOptions_CalibrationMethod_CalibrationMethod_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  CalibrationMethod_descriptor() {
    return CalibrationOptions_CalibrationMethod_descriptor();
  }
  template<typename T>
  static inline const std::string& CalibrationMethod_Name(T enum_t_value) {
    static_assert(::std::is_same<T, CalibrationMethod>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function CalibrationMethod_Name.");
    return CalibrationOptions_CalibrationMethod_Name(enum_t_value);
  }
  static inline bool CalibrationMethod_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      CalibrationMethod* value) {
    return CalibrationOptions_CalibrationMethod_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kRepresentativeDatasetsFieldNumber = 3,
    kCalibrationDataDirFieldNumber = 4,
    kCalibrationParametersFieldNumber = 2,
    kCalibrationMethodFieldNumber = 1,
    kForceRegenerateCalibrationDataFieldNumber = 5,
  };
  // repeated .stablehlo.quantization.RepresentativeDatasetConfig representative_datasets = 3;
  int representative_datasets_size() const;
  private:
  int _internal_representative_datasets_size() const;
  public:
  void clear_representative_datasets();
  ::stablehlo::quantization::RepresentativeDatasetConfig* mutable_representative_datasets(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig >*
      mutable_representative_datasets();
  private:
  const ::stablehlo::quantization::RepresentativeDatasetConfig& _internal_representative_datasets(int index) const;
  ::stablehlo::quantization::RepresentativeDatasetConfig* _internal_add_representative_datasets();
  public:
  const ::stablehlo::quantization::RepresentativeDatasetConfig& representative_datasets(int index) const;
  ::stablehlo::quantization::RepresentativeDatasetConfig* add_representative_datasets();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig >&
      representative_datasets() const;

  // string calibration_data_dir = 4;
  void clear_calibration_data_dir();
  const std::string& calibration_data_dir() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_calibration_data_dir(ArgT0&& arg0, ArgT... args);
  std::string* mutable_calibration_data_dir();
  PROTOBUF_NODISCARD std::string* release_calibration_data_dir();
  void set_allocated_calibration_data_dir(std::string* calibration_data_dir);
  private:
  const std::string& _internal_calibration_data_dir() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_calibration_data_dir(const std::string& value);
  std::string* _internal_mutable_calibration_data_dir();
  public:

  // .stablehlo.quantization.CalibrationOptions.CalibrationParameters calibration_parameters = 2;
  bool has_calibration_parameters() const;
  private:
  bool _internal_has_calibration_parameters() const;
  public:
  void clear_calibration_parameters();
  const ::stablehlo::quantization::CalibrationOptions_CalibrationParameters& calibration_parameters() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* release_calibration_parameters();
  ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* mutable_calibration_parameters();
  void set_allocated_calibration_parameters(::stablehlo::quantization::CalibrationOptions_CalibrationParameters* calibration_parameters);
  private:
  const ::stablehlo::quantization::CalibrationOptions_CalibrationParameters& _internal_calibration_parameters() const;
  ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* _internal_mutable_calibration_parameters();
  public:
  void unsafe_arena_set_allocated_calibration_parameters(
      ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* calibration_parameters);
  ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* unsafe_arena_release_calibration_parameters();

  // .stablehlo.quantization.CalibrationOptions.CalibrationMethod calibration_method = 1;
  void clear_calibration_method();
  ::stablehlo::quantization::CalibrationOptions_CalibrationMethod calibration_method() const;
  void set_calibration_method(::stablehlo::quantization::CalibrationOptions_CalibrationMethod value);
  private:
  ::stablehlo::quantization::CalibrationOptions_CalibrationMethod _internal_calibration_method() const;
  void _internal_set_calibration_method(::stablehlo::quantization::CalibrationOptions_CalibrationMethod value);
  public:

  // bool force_regenerate_calibration_data = 5;
  void clear_force_regenerate_calibration_data();
  bool force_regenerate_calibration_data() const;
  void set_force_regenerate_calibration_data(bool value);
  private:
  bool _internal_force_regenerate_calibration_data() const;
  void _internal_set_force_regenerate_calibration_data(bool value);
  public:

  // @@protoc_insertion_point(class_scope:stablehlo.quantization.CalibrationOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig > representative_datasets_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr calibration_data_dir_;
    ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* calibration_parameters_;
    int calibration_method_;
    bool force_regenerate_calibration_data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class QuantizationConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:stablehlo.quantization.QuantizationConfig) */ {
 public:
  inline QuantizationConfig() : QuantizationConfig(nullptr) {}
  ~QuantizationConfig() override;
  explicit PROTOBUF_CONSTEXPR QuantizationConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QuantizationConfig(const QuantizationConfig& from);
  QuantizationConfig(QuantizationConfig&& from) noexcept
    : QuantizationConfig() {
    *this = ::std::move(from);
  }

  inline QuantizationConfig& operator=(const QuantizationConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline QuantizationConfig& operator=(QuantizationConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QuantizationConfig& default_instance() {
    return *internal_default_instance();
  }
  enum PresetCase {
    kStaticRangePtqPreset = 1,
    kWeightOnlyPtqPreset = 7,
    PRESET_NOT_SET = 0,
  };

  static inline const QuantizationConfig* internal_default_instance() {
    return reinterpret_cast<const QuantizationConfig*>(
               &_QuantizationConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(QuantizationConfig& a, QuantizationConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(QuantizationConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QuantizationConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QuantizationConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QuantizationConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QuantizationConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QuantizationConfig& from) {
    QuantizationConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QuantizationConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "stablehlo.quantization.QuantizationConfig";
  }
  protected:
  explicit QuantizationConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReportFilePathFieldNumber = 8,
    kTfSavedModelFieldNumber = 2,
    kPipelineConfigFieldNumber = 3,
    kSpecsFieldNumber = 4,
    kDebuggerConfigFieldNumber = 5,
    kCalibrationOptionsFieldNumber = 6,
    kStaticRangePtqPresetFieldNumber = 1,
    kWeightOnlyPtqPresetFieldNumber = 7,
  };
  // optional string report_file_path = 8;
  bool has_report_file_path() const;
  private:
  bool _internal_has_report_file_path() const;
  public:
  void clear_report_file_path();
  const std::string& report_file_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_report_file_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_report_file_path();
  PROTOBUF_NODISCARD std::string* release_report_file_path();
  void set_allocated_report_file_path(std::string* report_file_path);
  private:
  const std::string& _internal_report_file_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_report_file_path(const std::string& value);
  std::string* _internal_mutable_report_file_path();
  public:

  // .stablehlo.quantization.TfSavedModelConfig tf_saved_model = 2;
  bool has_tf_saved_model() const;
  private:
  bool _internal_has_tf_saved_model() const;
  public:
  void clear_tf_saved_model();
  const ::stablehlo::quantization::TfSavedModelConfig& tf_saved_model() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::TfSavedModelConfig* release_tf_saved_model();
  ::stablehlo::quantization::TfSavedModelConfig* mutable_tf_saved_model();
  void set_allocated_tf_saved_model(::stablehlo::quantization::TfSavedModelConfig* tf_saved_model);
  private:
  const ::stablehlo::quantization::TfSavedModelConfig& _internal_tf_saved_model() const;
  ::stablehlo::quantization::TfSavedModelConfig* _internal_mutable_tf_saved_model();
  public:
  void unsafe_arena_set_allocated_tf_saved_model(
      ::stablehlo::quantization::TfSavedModelConfig* tf_saved_model);
  ::stablehlo::quantization::TfSavedModelConfig* unsafe_arena_release_tf_saved_model();

  // .stablehlo.quantization.PipelineConfig pipeline_config = 3;
  bool has_pipeline_config() const;
  private:
  bool _internal_has_pipeline_config() const;
  public:
  void clear_pipeline_config();
  const ::stablehlo::quantization::PipelineConfig& pipeline_config() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::PipelineConfig* release_pipeline_config();
  ::stablehlo::quantization::PipelineConfig* mutable_pipeline_config();
  void set_allocated_pipeline_config(::stablehlo::quantization::PipelineConfig* pipeline_config);
  private:
  const ::stablehlo::quantization::PipelineConfig& _internal_pipeline_config() const;
  ::stablehlo::quantization::PipelineConfig* _internal_mutable_pipeline_config();
  public:
  void unsafe_arena_set_allocated_pipeline_config(
      ::stablehlo::quantization::PipelineConfig* pipeline_config);
  ::stablehlo::quantization::PipelineConfig* unsafe_arena_release_pipeline_config();

  // .stablehlo.quantization.QuantizationSpecs specs = 4;
  bool has_specs() const;
  private:
  bool _internal_has_specs() const;
  public:
  void clear_specs();
  const ::stablehlo::quantization::QuantizationSpecs& specs() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::QuantizationSpecs* release_specs();
  ::stablehlo::quantization::QuantizationSpecs* mutable_specs();
  void set_allocated_specs(::stablehlo::quantization::QuantizationSpecs* specs);
  private:
  const ::stablehlo::quantization::QuantizationSpecs& _internal_specs() const;
  ::stablehlo::quantization::QuantizationSpecs* _internal_mutable_specs();
  public:
  void unsafe_arena_set_allocated_specs(
      ::stablehlo::quantization::QuantizationSpecs* specs);
  ::stablehlo::quantization::QuantizationSpecs* unsafe_arena_release_specs();

  // .stablehlo.quantization.DebuggerConfig debugger_config = 5;
  bool has_debugger_config() const;
  private:
  bool _internal_has_debugger_config() const;
  public:
  void clear_debugger_config();
  const ::stablehlo::quantization::DebuggerConfig& debugger_config() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::DebuggerConfig* release_debugger_config();
  ::stablehlo::quantization::DebuggerConfig* mutable_debugger_config();
  void set_allocated_debugger_config(::stablehlo::quantization::DebuggerConfig* debugger_config);
  private:
  const ::stablehlo::quantization::DebuggerConfig& _internal_debugger_config() const;
  ::stablehlo::quantization::DebuggerConfig* _internal_mutable_debugger_config();
  public:
  void unsafe_arena_set_allocated_debugger_config(
      ::stablehlo::quantization::DebuggerConfig* debugger_config);
  ::stablehlo::quantization::DebuggerConfig* unsafe_arena_release_debugger_config();

  // .stablehlo.quantization.CalibrationOptions calibration_options = 6;
  bool has_calibration_options() const;
  private:
  bool _internal_has_calibration_options() const;
  public:
  void clear_calibration_options();
  const ::stablehlo::quantization::CalibrationOptions& calibration_options() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::CalibrationOptions* release_calibration_options();
  ::stablehlo::quantization::CalibrationOptions* mutable_calibration_options();
  void set_allocated_calibration_options(::stablehlo::quantization::CalibrationOptions* calibration_options);
  private:
  const ::stablehlo::quantization::CalibrationOptions& _internal_calibration_options() const;
  ::stablehlo::quantization::CalibrationOptions* _internal_mutable_calibration_options();
  public:
  void unsafe_arena_set_allocated_calibration_options(
      ::stablehlo::quantization::CalibrationOptions* calibration_options);
  ::stablehlo::quantization::CalibrationOptions* unsafe_arena_release_calibration_options();

  // .stablehlo.quantization.StaticRangePtqPreset static_range_ptq_preset = 1;
  bool has_static_range_ptq_preset() const;
  private:
  bool _internal_has_static_range_ptq_preset() const;
  public:
  void clear_static_range_ptq_preset();
  const ::stablehlo::quantization::StaticRangePtqPreset& static_range_ptq_preset() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::StaticRangePtqPreset* release_static_range_ptq_preset();
  ::stablehlo::quantization::StaticRangePtqPreset* mutable_static_range_ptq_preset();
  void set_allocated_static_range_ptq_preset(::stablehlo::quantization::StaticRangePtqPreset* static_range_ptq_preset);
  private:
  const ::stablehlo::quantization::StaticRangePtqPreset& _internal_static_range_ptq_preset() const;
  ::stablehlo::quantization::StaticRangePtqPreset* _internal_mutable_static_range_ptq_preset();
  public:
  void unsafe_arena_set_allocated_static_range_ptq_preset(
      ::stablehlo::quantization::StaticRangePtqPreset* static_range_ptq_preset);
  ::stablehlo::quantization::StaticRangePtqPreset* unsafe_arena_release_static_range_ptq_preset();

  // .stablehlo.quantization.WeightOnlyPtqPreset weight_only_ptq_preset = 7;
  bool has_weight_only_ptq_preset() const;
  private:
  bool _internal_has_weight_only_ptq_preset() const;
  public:
  void clear_weight_only_ptq_preset();
  const ::stablehlo::quantization::WeightOnlyPtqPreset& weight_only_ptq_preset() const;
  PROTOBUF_NODISCARD ::stablehlo::quantization::WeightOnlyPtqPreset* release_weight_only_ptq_preset();
  ::stablehlo::quantization::WeightOnlyPtqPreset* mutable_weight_only_ptq_preset();
  void set_allocated_weight_only_ptq_preset(::stablehlo::quantization::WeightOnlyPtqPreset* weight_only_ptq_preset);
  private:
  const ::stablehlo::quantization::WeightOnlyPtqPreset& _internal_weight_only_ptq_preset() const;
  ::stablehlo::quantization::WeightOnlyPtqPreset* _internal_mutable_weight_only_ptq_preset();
  public:
  void unsafe_arena_set_allocated_weight_only_ptq_preset(
      ::stablehlo::quantization::WeightOnlyPtqPreset* weight_only_ptq_preset);
  ::stablehlo::quantization::WeightOnlyPtqPreset* unsafe_arena_release_weight_only_ptq_preset();

  void clear_preset();
  PresetCase preset_case() const;
  // @@protoc_insertion_point(class_scope:stablehlo.quantization.QuantizationConfig)
 private:
  class _Internal;
  void set_has_static_range_ptq_preset();
  void set_has_weight_only_ptq_preset();

  inline bool has_preset() const;
  inline void clear_has_preset();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr report_file_path_;
    ::stablehlo::quantization::TfSavedModelConfig* tf_saved_model_;
    ::stablehlo::quantization::PipelineConfig* pipeline_config_;
    ::stablehlo::quantization::QuantizationSpecs* specs_;
    ::stablehlo::quantization::DebuggerConfig* debugger_config_;
    ::stablehlo::quantization::CalibrationOptions* calibration_options_;
    union PresetUnion {
      constexpr PresetUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::stablehlo::quantization::StaticRangePtqPreset* static_range_ptq_preset_;
      ::stablehlo::quantization::WeightOnlyPtqPreset* weight_only_ptq_preset_;
    } preset_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TfRecordFile

// string path = 1;
inline void TfRecordFile::clear_path() {
  _impl_.path_.ClearToEmpty();
}
inline const std::string& TfRecordFile::path() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.TfRecordFile.path)
  return _internal_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TfRecordFile::set_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stablehlo.quantization.TfRecordFile.path)
}
inline std::string* TfRecordFile::mutable_path() {
  std::string* _s = _internal_mutable_path();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.TfRecordFile.path)
  return _s;
}
inline const std::string& TfRecordFile::_internal_path() const {
  return _impl_.path_.Get();
}
inline void TfRecordFile::_internal_set_path(const std::string& value) {
  
  _impl_.path_.Set(value, GetArenaForAllocation());
}
inline std::string* TfRecordFile::_internal_mutable_path() {
  
  return _impl_.path_.Mutable(GetArenaForAllocation());
}
inline std::string* TfRecordFile::release_path() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.TfRecordFile.path)
  return _impl_.path_.Release();
}
inline void TfRecordFile::set_allocated_path(std::string* path) {
  if (path != nullptr) {
    
  } else {
    
  }
  _impl_.path_.SetAllocated(path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.path_.IsDefault()) {
    _impl_.path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.TfRecordFile.path)
}

// -------------------------------------------------------------------

// RepresentativeDatasetConfig

// .stablehlo.quantization.TfRecordFile tf_record = 1;
inline bool RepresentativeDatasetConfig::_internal_has_tf_record() const {
  return file_case() == kTfRecord;
}
inline bool RepresentativeDatasetConfig::has_tf_record() const {
  return _internal_has_tf_record();
}
inline void RepresentativeDatasetConfig::set_has_tf_record() {
  _impl_._oneof_case_[0] = kTfRecord;
}
inline void RepresentativeDatasetConfig::clear_tf_record() {
  if (_internal_has_tf_record()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.file_.tf_record_;
    }
    clear_has_file();
  }
}
inline ::stablehlo::quantization::TfRecordFile* RepresentativeDatasetConfig::release_tf_record() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.RepresentativeDatasetConfig.tf_record)
  if (_internal_has_tf_record()) {
    clear_has_file();
    ::stablehlo::quantization::TfRecordFile* temp = _impl_.file_.tf_record_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.file_.tf_record_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::TfRecordFile& RepresentativeDatasetConfig::_internal_tf_record() const {
  return _internal_has_tf_record()
      ? *_impl_.file_.tf_record_
      : reinterpret_cast< ::stablehlo::quantization::TfRecordFile&>(::stablehlo::quantization::_TfRecordFile_default_instance_);
}
inline const ::stablehlo::quantization::TfRecordFile& RepresentativeDatasetConfig::tf_record() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.RepresentativeDatasetConfig.tf_record)
  return _internal_tf_record();
}
inline ::stablehlo::quantization::TfRecordFile* RepresentativeDatasetConfig::unsafe_arena_release_tf_record() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.RepresentativeDatasetConfig.tf_record)
  if (_internal_has_tf_record()) {
    clear_has_file();
    ::stablehlo::quantization::TfRecordFile* temp = _impl_.file_.tf_record_;
    _impl_.file_.tf_record_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void RepresentativeDatasetConfig::unsafe_arena_set_allocated_tf_record(::stablehlo::quantization::TfRecordFile* tf_record) {
  clear_file();
  if (tf_record) {
    set_has_tf_record();
    _impl_.file_.tf_record_ = tf_record;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.RepresentativeDatasetConfig.tf_record)
}
inline ::stablehlo::quantization::TfRecordFile* RepresentativeDatasetConfig::_internal_mutable_tf_record() {
  if (!_internal_has_tf_record()) {
    clear_file();
    set_has_tf_record();
    _impl_.file_.tf_record_ = CreateMaybeMessage< ::stablehlo::quantization::TfRecordFile >(GetArenaForAllocation());
  }
  return _impl_.file_.tf_record_;
}
inline ::stablehlo::quantization::TfRecordFile* RepresentativeDatasetConfig::mutable_tf_record() {
  ::stablehlo::quantization::TfRecordFile* _msg = _internal_mutable_tf_record();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.RepresentativeDatasetConfig.tf_record)
  return _msg;
}

// optional string signature_key = 2;
inline bool RepresentativeDatasetConfig::_internal_has_signature_key() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RepresentativeDatasetConfig::has_signature_key() const {
  return _internal_has_signature_key();
}
inline void RepresentativeDatasetConfig::clear_signature_key() {
  _impl_.signature_key_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RepresentativeDatasetConfig::signature_key() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.RepresentativeDatasetConfig.signature_key)
  return _internal_signature_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RepresentativeDatasetConfig::set_signature_key(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.signature_key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stablehlo.quantization.RepresentativeDatasetConfig.signature_key)
}
inline std::string* RepresentativeDatasetConfig::mutable_signature_key() {
  std::string* _s = _internal_mutable_signature_key();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.RepresentativeDatasetConfig.signature_key)
  return _s;
}
inline const std::string& RepresentativeDatasetConfig::_internal_signature_key() const {
  return _impl_.signature_key_.Get();
}
inline void RepresentativeDatasetConfig::_internal_set_signature_key(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.signature_key_.Set(value, GetArenaForAllocation());
}
inline std::string* RepresentativeDatasetConfig::_internal_mutable_signature_key() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.signature_key_.Mutable(GetArenaForAllocation());
}
inline std::string* RepresentativeDatasetConfig::release_signature_key() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.RepresentativeDatasetConfig.signature_key)
  if (!_internal_has_signature_key()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.signature_key_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.signature_key_.IsDefault()) {
    _impl_.signature_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void RepresentativeDatasetConfig::set_allocated_signature_key(std::string* signature_key) {
  if (signature_key != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.signature_key_.SetAllocated(signature_key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.signature_key_.IsDefault()) {
    _impl_.signature_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.RepresentativeDatasetConfig.signature_key)
}

inline bool RepresentativeDatasetConfig::has_file() const {
  return file_case() != FILE_NOT_SET;
}
inline void RepresentativeDatasetConfig::clear_has_file() {
  _impl_._oneof_case_[0] = FILE_NOT_SET;
}
inline RepresentativeDatasetConfig::FileCase RepresentativeDatasetConfig::file_case() const {
  return RepresentativeDatasetConfig::FileCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// StaticRangePtqPreset

// repeated .stablehlo.quantization.RepresentativeDatasetConfig representative_datasets = 1;
inline int StaticRangePtqPreset::_internal_representative_datasets_size() const {
  return _impl_.representative_datasets_.size();
}
inline int StaticRangePtqPreset::representative_datasets_size() const {
  return _internal_representative_datasets_size();
}
inline void StaticRangePtqPreset::clear_representative_datasets() {
  _impl_.representative_datasets_.Clear();
}
inline ::stablehlo::quantization::RepresentativeDatasetConfig* StaticRangePtqPreset::mutable_representative_datasets(int index) {
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.StaticRangePtqPreset.representative_datasets)
  return _impl_.representative_datasets_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig >*
StaticRangePtqPreset::mutable_representative_datasets() {
  // @@protoc_insertion_point(field_mutable_list:stablehlo.quantization.StaticRangePtqPreset.representative_datasets)
  return &_impl_.representative_datasets_;
}
inline const ::stablehlo::quantization::RepresentativeDatasetConfig& StaticRangePtqPreset::_internal_representative_datasets(int index) const {
  return _impl_.representative_datasets_.Get(index);
}
inline const ::stablehlo::quantization::RepresentativeDatasetConfig& StaticRangePtqPreset::representative_datasets(int index) const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.StaticRangePtqPreset.representative_datasets)
  return _internal_representative_datasets(index);
}
inline ::stablehlo::quantization::RepresentativeDatasetConfig* StaticRangePtqPreset::_internal_add_representative_datasets() {
  return _impl_.representative_datasets_.Add();
}
inline ::stablehlo::quantization::RepresentativeDatasetConfig* StaticRangePtqPreset::add_representative_datasets() {
  ::stablehlo::quantization::RepresentativeDatasetConfig* _add = _internal_add_representative_datasets();
  // @@protoc_insertion_point(field_add:stablehlo.quantization.StaticRangePtqPreset.representative_datasets)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig >&
StaticRangePtqPreset::representative_datasets() const {
  // @@protoc_insertion_point(field_list:stablehlo.quantization.StaticRangePtqPreset.representative_datasets)
  return _impl_.representative_datasets_;
}

// bool enable_per_channel_quantized_weight = 2 [deprecated = true];
inline void StaticRangePtqPreset::clear_enable_per_channel_quantized_weight() {
  _impl_.enable_per_channel_quantized_weight_ = false;
}
inline bool StaticRangePtqPreset::_internal_enable_per_channel_quantized_weight() const {
  return _impl_.enable_per_channel_quantized_weight_;
}
inline bool StaticRangePtqPreset::enable_per_channel_quantized_weight() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.StaticRangePtqPreset.enable_per_channel_quantized_weight)
  return _internal_enable_per_channel_quantized_weight();
}
inline void StaticRangePtqPreset::_internal_set_enable_per_channel_quantized_weight(bool value) {
  
  _impl_.enable_per_channel_quantized_weight_ = value;
}
inline void StaticRangePtqPreset::set_enable_per_channel_quantized_weight(bool value) {
  _internal_set_enable_per_channel_quantized_weight(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.StaticRangePtqPreset.enable_per_channel_quantized_weight)
}

// bool enable_full_int_quantization = 3;
inline void StaticRangePtqPreset::clear_enable_full_int_quantization() {
  _impl_.enable_full_int_quantization_ = false;
}
inline bool StaticRangePtqPreset::_internal_enable_full_int_quantization() const {
  return _impl_.enable_full_int_quantization_;
}
inline bool StaticRangePtqPreset::enable_full_int_quantization() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.StaticRangePtqPreset.enable_full_int_quantization)
  return _internal_enable_full_int_quantization();
}
inline void StaticRangePtqPreset::_internal_set_enable_full_int_quantization(bool value) {
  
  _impl_.enable_full_int_quantization_ = value;
}
inline void StaticRangePtqPreset::set_enable_full_int_quantization(bool value) {
  _internal_set_enable_full_int_quantization(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.StaticRangePtqPreset.enable_full_int_quantization)
}

// -------------------------------------------------------------------

// WeightOnlyPtqPreset

// -------------------------------------------------------------------

// TfSavedModelConfig

// repeated string tags = 1;
inline int TfSavedModelConfig::_internal_tags_size() const {
  return _impl_.tags_.size();
}
inline int TfSavedModelConfig::tags_size() const {
  return _internal_tags_size();
}
inline void TfSavedModelConfig::clear_tags() {
  _impl_.tags_.Clear();
}
inline std::string* TfSavedModelConfig::add_tags() {
  std::string* _s = _internal_add_tags();
  // @@protoc_insertion_point(field_add_mutable:stablehlo.quantization.TfSavedModelConfig.tags)
  return _s;
}
inline const std::string& TfSavedModelConfig::_internal_tags(int index) const {
  return _impl_.tags_.Get(index);
}
inline const std::string& TfSavedModelConfig::tags(int index) const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.TfSavedModelConfig.tags)
  return _internal_tags(index);
}
inline std::string* TfSavedModelConfig::mutable_tags(int index) {
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.TfSavedModelConfig.tags)
  return _impl_.tags_.Mutable(index);
}
inline void TfSavedModelConfig::set_tags(int index, const std::string& value) {
  _impl_.tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.TfSavedModelConfig.tags)
}
inline void TfSavedModelConfig::set_tags(int index, std::string&& value) {
  _impl_.tags_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:stablehlo.quantization.TfSavedModelConfig.tags)
}
inline void TfSavedModelConfig::set_tags(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:stablehlo.quantization.TfSavedModelConfig.tags)
}
inline void TfSavedModelConfig::set_tags(int index, const char* value, size_t size) {
  _impl_.tags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:stablehlo.quantization.TfSavedModelConfig.tags)
}
inline std::string* TfSavedModelConfig::_internal_add_tags() {
  return _impl_.tags_.Add();
}
inline void TfSavedModelConfig::add_tags(const std::string& value) {
  _impl_.tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:stablehlo.quantization.TfSavedModelConfig.tags)
}
inline void TfSavedModelConfig::add_tags(std::string&& value) {
  _impl_.tags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:stablehlo.quantization.TfSavedModelConfig.tags)
}
inline void TfSavedModelConfig::add_tags(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:stablehlo.quantization.TfSavedModelConfig.tags)
}
inline void TfSavedModelConfig::add_tags(const char* value, size_t size) {
  _impl_.tags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:stablehlo.quantization.TfSavedModelConfig.tags)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TfSavedModelConfig::tags() const {
  // @@protoc_insertion_point(field_list:stablehlo.quantization.TfSavedModelConfig.tags)
  return _impl_.tags_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TfSavedModelConfig::mutable_tags() {
  // @@protoc_insertion_point(field_mutable_list:stablehlo.quantization.TfSavedModelConfig.tags)
  return &_impl_.tags_;
}

// -------------------------------------------------------------------

// PipelineConfig

// optional bool unpack_quantized_types = 1;
inline bool PipelineConfig::_internal_has_unpack_quantized_types() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool PipelineConfig::has_unpack_quantized_types() const {
  return _internal_has_unpack_quantized_types();
}
inline void PipelineConfig::clear_unpack_quantized_types() {
  _impl_.unpack_quantized_types_ = false;
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline bool PipelineConfig::_internal_unpack_quantized_types() const {
  return _impl_.unpack_quantized_types_;
}
inline bool PipelineConfig::unpack_quantized_types() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.PipelineConfig.unpack_quantized_types)
  return _internal_unpack_quantized_types();
}
inline void PipelineConfig::_internal_set_unpack_quantized_types(bool value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.unpack_quantized_types_ = value;
}
inline void PipelineConfig::set_unpack_quantized_types(bool value) {
  _internal_set_unpack_quantized_types(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.PipelineConfig.unpack_quantized_types)
}

// bool merge_fusion_with_dequantize = 2;
inline void PipelineConfig::clear_merge_fusion_with_dequantize() {
  _impl_.merge_fusion_with_dequantize_ = false;
}
inline bool PipelineConfig::_internal_merge_fusion_with_dequantize() const {
  return _impl_.merge_fusion_with_dequantize_;
}
inline bool PipelineConfig::merge_fusion_with_dequantize() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.PipelineConfig.merge_fusion_with_dequantize)
  return _internal_merge_fusion_with_dequantize();
}
inline void PipelineConfig::_internal_set_merge_fusion_with_dequantize(bool value) {
  
  _impl_.merge_fusion_with_dequantize_ = value;
}
inline void PipelineConfig::set_merge_fusion_with_dequantize(bool value) {
  _internal_set_merge_fusion_with_dequantize(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.PipelineConfig.merge_fusion_with_dequantize)
}

// -------------------------------------------------------------------

// QuantizableUnit

// string name = 1;
inline void QuantizableUnit::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& QuantizableUnit::name() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizableUnit.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void QuantizableUnit::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizableUnit.name)
}
inline std::string* QuantizableUnit::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizableUnit.name)
  return _s;
}
inline const std::string& QuantizableUnit::_internal_name() const {
  return _impl_.name_.Get();
}
inline void QuantizableUnit::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* QuantizableUnit::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* QuantizableUnit::release_name() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizableUnit.name)
  return _impl_.name_.Release();
}
inline void QuantizableUnit::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizableUnit.name)
}

// -------------------------------------------------------------------

// QuantizationResult

// .stablehlo.quantization.QuantizableUnit quantizable_unit = 1;
inline bool QuantizationResult::_internal_has_quantizable_unit() const {
  return this != internal_default_instance() && _impl_.quantizable_unit_ != nullptr;
}
inline bool QuantizationResult::has_quantizable_unit() const {
  return _internal_has_quantizable_unit();
}
inline void QuantizationResult::clear_quantizable_unit() {
  if (GetArenaForAllocation() == nullptr && _impl_.quantizable_unit_ != nullptr) {
    delete _impl_.quantizable_unit_;
  }
  _impl_.quantizable_unit_ = nullptr;
}
inline const ::stablehlo::quantization::QuantizableUnit& QuantizationResult::_internal_quantizable_unit() const {
  const ::stablehlo::quantization::QuantizableUnit* p = _impl_.quantizable_unit_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::QuantizableUnit&>(
      ::stablehlo::quantization::_QuantizableUnit_default_instance_);
}
inline const ::stablehlo::quantization::QuantizableUnit& QuantizationResult::quantizable_unit() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationResult.quantizable_unit)
  return _internal_quantizable_unit();
}
inline void QuantizationResult::unsafe_arena_set_allocated_quantizable_unit(
    ::stablehlo::quantization::QuantizableUnit* quantizable_unit) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.quantizable_unit_);
  }
  _impl_.quantizable_unit_ = quantizable_unit;
  if (quantizable_unit) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationResult.quantizable_unit)
}
inline ::stablehlo::quantization::QuantizableUnit* QuantizationResult::release_quantizable_unit() {
  
  ::stablehlo::quantization::QuantizableUnit* temp = _impl_.quantizable_unit_;
  _impl_.quantizable_unit_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::QuantizableUnit* QuantizationResult::unsafe_arena_release_quantizable_unit() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationResult.quantizable_unit)
  
  ::stablehlo::quantization::QuantizableUnit* temp = _impl_.quantizable_unit_;
  _impl_.quantizable_unit_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::QuantizableUnit* QuantizationResult::_internal_mutable_quantizable_unit() {
  
  if (_impl_.quantizable_unit_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::QuantizableUnit>(GetArenaForAllocation());
    _impl_.quantizable_unit_ = p;
  }
  return _impl_.quantizable_unit_;
}
inline ::stablehlo::quantization::QuantizableUnit* QuantizationResult::mutable_quantizable_unit() {
  ::stablehlo::quantization::QuantizableUnit* _msg = _internal_mutable_quantizable_unit();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationResult.quantizable_unit)
  return _msg;
}
inline void QuantizationResult::set_allocated_quantizable_unit(::stablehlo::quantization::QuantizableUnit* quantizable_unit) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.quantizable_unit_;
  }
  if (quantizable_unit) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(quantizable_unit);
    if (message_arena != submessage_arena) {
      quantizable_unit = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, quantizable_unit, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.quantizable_unit_ = quantizable_unit;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationResult.quantizable_unit)
}

// .stablehlo.quantization.Method method = 2;
inline bool QuantizationResult::_internal_has_method() const {
  return this != internal_default_instance() && _impl_.method_ != nullptr;
}
inline bool QuantizationResult::has_method() const {
  return _internal_has_method();
}
inline void QuantizationResult::clear_method() {
  if (GetArenaForAllocation() == nullptr && _impl_.method_ != nullptr) {
    delete _impl_.method_;
  }
  _impl_.method_ = nullptr;
}
inline const ::stablehlo::quantization::Method& QuantizationResult::_internal_method() const {
  const ::stablehlo::quantization::Method* p = _impl_.method_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::Method&>(
      ::stablehlo::quantization::_Method_default_instance_);
}
inline const ::stablehlo::quantization::Method& QuantizationResult::method() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationResult.method)
  return _internal_method();
}
inline void QuantizationResult::unsafe_arena_set_allocated_method(
    ::stablehlo::quantization::Method* method) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.method_);
  }
  _impl_.method_ = method;
  if (method) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationResult.method)
}
inline ::stablehlo::quantization::Method* QuantizationResult::release_method() {
  
  ::stablehlo::quantization::Method* temp = _impl_.method_;
  _impl_.method_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::Method* QuantizationResult::unsafe_arena_release_method() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationResult.method)
  
  ::stablehlo::quantization::Method* temp = _impl_.method_;
  _impl_.method_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::Method* QuantizationResult::_internal_mutable_method() {
  
  if (_impl_.method_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::Method>(GetArenaForAllocation());
    _impl_.method_ = p;
  }
  return _impl_.method_;
}
inline ::stablehlo::quantization::Method* QuantizationResult::mutable_method() {
  ::stablehlo::quantization::Method* _msg = _internal_mutable_method();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationResult.method)
  return _msg;
}
inline void QuantizationResult::set_allocated_method(::stablehlo::quantization::Method* method) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.method_;
  }
  if (method) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(method);
    if (message_arena != submessage_arena) {
      method = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, method, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.method_ = method;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationResult.method)
}

// -------------------------------------------------------------------

// QuantizationResults

// repeated .stablehlo.quantization.QuantizationResult results = 1;
inline int QuantizationResults::_internal_results_size() const {
  return _impl_.results_.size();
}
inline int QuantizationResults::results_size() const {
  return _internal_results_size();
}
inline void QuantizationResults::clear_results() {
  _impl_.results_.Clear();
}
inline ::stablehlo::quantization::QuantizationResult* QuantizationResults::mutable_results(int index) {
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationResults.results)
  return _impl_.results_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationResult >*
QuantizationResults::mutable_results() {
  // @@protoc_insertion_point(field_mutable_list:stablehlo.quantization.QuantizationResults.results)
  return &_impl_.results_;
}
inline const ::stablehlo::quantization::QuantizationResult& QuantizationResults::_internal_results(int index) const {
  return _impl_.results_.Get(index);
}
inline const ::stablehlo::quantization::QuantizationResult& QuantizationResults::results(int index) const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationResults.results)
  return _internal_results(index);
}
inline ::stablehlo::quantization::QuantizationResult* QuantizationResults::_internal_add_results() {
  return _impl_.results_.Add();
}
inline ::stablehlo::quantization::QuantizationResult* QuantizationResults::add_results() {
  ::stablehlo::quantization::QuantizationResult* _add = _internal_add_results();
  // @@protoc_insertion_point(field_add:stablehlo.quantization.QuantizationResults.results)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationResult >&
QuantizationResults::results() const {
  // @@protoc_insertion_point(field_list:stablehlo.quantization.QuantizationResults.results)
  return _impl_.results_;
}

// -------------------------------------------------------------------

// QuantizedDimension

// optional int32 dimension = 1;
inline bool QuantizedDimension::_internal_has_dimension() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool QuantizedDimension::has_dimension() const {
  return _internal_has_dimension();
}
inline void QuantizedDimension::clear_dimension() {
  _impl_.dimension_ = 0;
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline int32_t QuantizedDimension::_internal_dimension() const {
  return _impl_.dimension_;
}
inline int32_t QuantizedDimension::dimension() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizedDimension.dimension)
  return _internal_dimension();
}
inline void QuantizedDimension::_internal_set_dimension(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.dimension_ = value;
}
inline void QuantizedDimension::set_dimension(int32_t value) {
  _internal_set_dimension(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizedDimension.dimension)
}

// -------------------------------------------------------------------

// PerTensor

// -------------------------------------------------------------------

// QuantizedType

// .stablehlo.quantization.QuantizedDimension dimension_specs = 1;
inline bool QuantizedType::_internal_has_dimension_specs() const {
  return type_case() == kDimensionSpecs;
}
inline bool QuantizedType::has_dimension_specs() const {
  return _internal_has_dimension_specs();
}
inline void QuantizedType::set_has_dimension_specs() {
  _impl_._oneof_case_[0] = kDimensionSpecs;
}
inline void QuantizedType::clear_dimension_specs() {
  if (_internal_has_dimension_specs()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.type_.dimension_specs_;
    }
    clear_has_type();
  }
}
inline ::stablehlo::quantization::QuantizedDimension* QuantizedType::release_dimension_specs() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizedType.dimension_specs)
  if (_internal_has_dimension_specs()) {
    clear_has_type();
    ::stablehlo::quantization::QuantizedDimension* temp = _impl_.type_.dimension_specs_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.type_.dimension_specs_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::QuantizedDimension& QuantizedType::_internal_dimension_specs() const {
  return _internal_has_dimension_specs()
      ? *_impl_.type_.dimension_specs_
      : reinterpret_cast< ::stablehlo::quantization::QuantizedDimension&>(::stablehlo::quantization::_QuantizedDimension_default_instance_);
}
inline const ::stablehlo::quantization::QuantizedDimension& QuantizedType::dimension_specs() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizedType.dimension_specs)
  return _internal_dimension_specs();
}
inline ::stablehlo::quantization::QuantizedDimension* QuantizedType::unsafe_arena_release_dimension_specs() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.QuantizedType.dimension_specs)
  if (_internal_has_dimension_specs()) {
    clear_has_type();
    ::stablehlo::quantization::QuantizedDimension* temp = _impl_.type_.dimension_specs_;
    _impl_.type_.dimension_specs_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QuantizedType::unsafe_arena_set_allocated_dimension_specs(::stablehlo::quantization::QuantizedDimension* dimension_specs) {
  clear_type();
  if (dimension_specs) {
    set_has_dimension_specs();
    _impl_.type_.dimension_specs_ = dimension_specs;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizedType.dimension_specs)
}
inline ::stablehlo::quantization::QuantizedDimension* QuantizedType::_internal_mutable_dimension_specs() {
  if (!_internal_has_dimension_specs()) {
    clear_type();
    set_has_dimension_specs();
    _impl_.type_.dimension_specs_ = CreateMaybeMessage< ::stablehlo::quantization::QuantizedDimension >(GetArenaForAllocation());
  }
  return _impl_.type_.dimension_specs_;
}
inline ::stablehlo::quantization::QuantizedDimension* QuantizedType::mutable_dimension_specs() {
  ::stablehlo::quantization::QuantizedDimension* _msg = _internal_mutable_dimension_specs();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizedType.dimension_specs)
  return _msg;
}

// .stablehlo.quantization.PerTensor per_tensor = 2;
inline bool QuantizedType::_internal_has_per_tensor() const {
  return type_case() == kPerTensor;
}
inline bool QuantizedType::has_per_tensor() const {
  return _internal_has_per_tensor();
}
inline void QuantizedType::set_has_per_tensor() {
  _impl_._oneof_case_[0] = kPerTensor;
}
inline void QuantizedType::clear_per_tensor() {
  if (_internal_has_per_tensor()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.type_.per_tensor_;
    }
    clear_has_type();
  }
}
inline ::stablehlo::quantization::PerTensor* QuantizedType::release_per_tensor() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizedType.per_tensor)
  if (_internal_has_per_tensor()) {
    clear_has_type();
    ::stablehlo::quantization::PerTensor* temp = _impl_.type_.per_tensor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.type_.per_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::PerTensor& QuantizedType::_internal_per_tensor() const {
  return _internal_has_per_tensor()
      ? *_impl_.type_.per_tensor_
      : reinterpret_cast< ::stablehlo::quantization::PerTensor&>(::stablehlo::quantization::_PerTensor_default_instance_);
}
inline const ::stablehlo::quantization::PerTensor& QuantizedType::per_tensor() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizedType.per_tensor)
  return _internal_per_tensor();
}
inline ::stablehlo::quantization::PerTensor* QuantizedType::unsafe_arena_release_per_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.QuantizedType.per_tensor)
  if (_internal_has_per_tensor()) {
    clear_has_type();
    ::stablehlo::quantization::PerTensor* temp = _impl_.type_.per_tensor_;
    _impl_.type_.per_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QuantizedType::unsafe_arena_set_allocated_per_tensor(::stablehlo::quantization::PerTensor* per_tensor) {
  clear_type();
  if (per_tensor) {
    set_has_per_tensor();
    _impl_.type_.per_tensor_ = per_tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizedType.per_tensor)
}
inline ::stablehlo::quantization::PerTensor* QuantizedType::_internal_mutable_per_tensor() {
  if (!_internal_has_per_tensor()) {
    clear_type();
    set_has_per_tensor();
    _impl_.type_.per_tensor_ = CreateMaybeMessage< ::stablehlo::quantization::PerTensor >(GetArenaForAllocation());
  }
  return _impl_.type_.per_tensor_;
}
inline ::stablehlo::quantization::PerTensor* QuantizedType::mutable_per_tensor() {
  ::stablehlo::quantization::PerTensor* _msg = _internal_mutable_per_tensor();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizedType.per_tensor)
  return _msg;
}

inline bool QuantizedType::has_type() const {
  return type_case() != TYPE_NOT_SET;
}
inline void QuantizedType::clear_has_type() {
  _impl_._oneof_case_[0] = TYPE_NOT_SET;
}
inline QuantizedType::TypeCase QuantizedType::type_case() const {
  return QuantizedType::TypeCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// NoQuantization

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// StaticRangePtq

// map<int32, .stablehlo.quantization.QuantizedType> input_quantized_types = 1;
inline int StaticRangePtq::_internal_input_quantized_types_size() const {
  return _impl_.input_quantized_types_.size();
}
inline int StaticRangePtq::input_quantized_types_size() const {
  return _internal_input_quantized_types_size();
}
inline void StaticRangePtq::clear_input_quantized_types() {
  _impl_.input_quantized_types_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >&
StaticRangePtq::_internal_input_quantized_types() const {
  return _impl_.input_quantized_types_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >&
StaticRangePtq::input_quantized_types() const {
  // @@protoc_insertion_point(field_map:stablehlo.quantization.StaticRangePtq.input_quantized_types)
  return _internal_input_quantized_types();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >*
StaticRangePtq::_internal_mutable_input_quantized_types() {
  return _impl_.input_quantized_types_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >*
StaticRangePtq::mutable_input_quantized_types() {
  // @@protoc_insertion_point(field_mutable_map:stablehlo.quantization.StaticRangePtq.input_quantized_types)
  return _internal_mutable_input_quantized_types();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// WeightOnlyPtq

// map<int32, .stablehlo.quantization.QuantizedType> input_quantized_types = 1;
inline int WeightOnlyPtq::_internal_input_quantized_types_size() const {
  return _impl_.input_quantized_types_.size();
}
inline int WeightOnlyPtq::input_quantized_types_size() const {
  return _internal_input_quantized_types_size();
}
inline void WeightOnlyPtq::clear_input_quantized_types() {
  _impl_.input_quantized_types_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >&
WeightOnlyPtq::_internal_input_quantized_types() const {
  return _impl_.input_quantized_types_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >&
WeightOnlyPtq::input_quantized_types() const {
  // @@protoc_insertion_point(field_map:stablehlo.quantization.WeightOnlyPtq.input_quantized_types)
  return _internal_input_quantized_types();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >*
WeightOnlyPtq::_internal_mutable_input_quantized_types() {
  return _impl_.input_quantized_types_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::stablehlo::quantization::QuantizedType >*
WeightOnlyPtq::mutable_input_quantized_types() {
  // @@protoc_insertion_point(field_mutable_map:stablehlo.quantization.WeightOnlyPtq.input_quantized_types)
  return _internal_mutable_input_quantized_types();
}

// -------------------------------------------------------------------

// FunctionNameMatcherSpec

// string regex = 1;
inline void FunctionNameMatcherSpec::clear_regex() {
  _impl_.regex_.ClearToEmpty();
}
inline const std::string& FunctionNameMatcherSpec::regex() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.FunctionNameMatcherSpec.regex)
  return _internal_regex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FunctionNameMatcherSpec::set_regex(ArgT0&& arg0, ArgT... args) {
 
 _impl_.regex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stablehlo.quantization.FunctionNameMatcherSpec.regex)
}
inline std::string* FunctionNameMatcherSpec::mutable_regex() {
  std::string* _s = _internal_mutable_regex();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.FunctionNameMatcherSpec.regex)
  return _s;
}
inline const std::string& FunctionNameMatcherSpec::_internal_regex() const {
  return _impl_.regex_.Get();
}
inline void FunctionNameMatcherSpec::_internal_set_regex(const std::string& value) {
  
  _impl_.regex_.Set(value, GetArenaForAllocation());
}
inline std::string* FunctionNameMatcherSpec::_internal_mutable_regex() {
  
  return _impl_.regex_.Mutable(GetArenaForAllocation());
}
inline std::string* FunctionNameMatcherSpec::release_regex() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.FunctionNameMatcherSpec.regex)
  return _impl_.regex_.Release();
}
inline void FunctionNameMatcherSpec::set_allocated_regex(std::string* regex) {
  if (regex != nullptr) {
    
  } else {
    
  }
  _impl_.regex_.SetAllocated(regex, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.regex_.IsDefault()) {
    _impl_.regex_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.FunctionNameMatcherSpec.regex)
}

// -------------------------------------------------------------------

// MatcherSpec

// .stablehlo.quantization.FunctionNameMatcherSpec function_name = 1;
inline bool MatcherSpec::_internal_has_function_name() const {
  return this != internal_default_instance() && _impl_.function_name_ != nullptr;
}
inline bool MatcherSpec::has_function_name() const {
  return _internal_has_function_name();
}
inline void MatcherSpec::clear_function_name() {
  if (GetArenaForAllocation() == nullptr && _impl_.function_name_ != nullptr) {
    delete _impl_.function_name_;
  }
  _impl_.function_name_ = nullptr;
}
inline const ::stablehlo::quantization::FunctionNameMatcherSpec& MatcherSpec::_internal_function_name() const {
  const ::stablehlo::quantization::FunctionNameMatcherSpec* p = _impl_.function_name_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::FunctionNameMatcherSpec&>(
      ::stablehlo::quantization::_FunctionNameMatcherSpec_default_instance_);
}
inline const ::stablehlo::quantization::FunctionNameMatcherSpec& MatcherSpec::function_name() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.MatcherSpec.function_name)
  return _internal_function_name();
}
inline void MatcherSpec::unsafe_arena_set_allocated_function_name(
    ::stablehlo::quantization::FunctionNameMatcherSpec* function_name) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.function_name_);
  }
  _impl_.function_name_ = function_name;
  if (function_name) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.MatcherSpec.function_name)
}
inline ::stablehlo::quantization::FunctionNameMatcherSpec* MatcherSpec::release_function_name() {
  
  ::stablehlo::quantization::FunctionNameMatcherSpec* temp = _impl_.function_name_;
  _impl_.function_name_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::FunctionNameMatcherSpec* MatcherSpec::unsafe_arena_release_function_name() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.MatcherSpec.function_name)
  
  ::stablehlo::quantization::FunctionNameMatcherSpec* temp = _impl_.function_name_;
  _impl_.function_name_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::FunctionNameMatcherSpec* MatcherSpec::_internal_mutable_function_name() {
  
  if (_impl_.function_name_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::FunctionNameMatcherSpec>(GetArenaForAllocation());
    _impl_.function_name_ = p;
  }
  return _impl_.function_name_;
}
inline ::stablehlo::quantization::FunctionNameMatcherSpec* MatcherSpec::mutable_function_name() {
  ::stablehlo::quantization::FunctionNameMatcherSpec* _msg = _internal_mutable_function_name();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.MatcherSpec.function_name)
  return _msg;
}
inline void MatcherSpec::set_allocated_function_name(::stablehlo::quantization::FunctionNameMatcherSpec* function_name) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.function_name_;
  }
  if (function_name) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(function_name);
    if (message_arena != submessage_arena) {
      function_name = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, function_name, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.function_name_ = function_name;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.MatcherSpec.function_name)
}

// -------------------------------------------------------------------

// Method

// .stablehlo.quantization.NoQuantization no_quantization = 1;
inline bool Method::_internal_has_no_quantization() const {
  return method_case() == kNoQuantization;
}
inline bool Method::has_no_quantization() const {
  return _internal_has_no_quantization();
}
inline void Method::set_has_no_quantization() {
  _impl_._oneof_case_[0] = kNoQuantization;
}
inline void Method::clear_no_quantization() {
  if (_internal_has_no_quantization()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.method_.no_quantization_;
    }
    clear_has_method();
  }
}
inline ::stablehlo::quantization::NoQuantization* Method::release_no_quantization() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.Method.no_quantization)
  if (_internal_has_no_quantization()) {
    clear_has_method();
    ::stablehlo::quantization::NoQuantization* temp = _impl_.method_.no_quantization_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.method_.no_quantization_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::NoQuantization& Method::_internal_no_quantization() const {
  return _internal_has_no_quantization()
      ? *_impl_.method_.no_quantization_
      : reinterpret_cast< ::stablehlo::quantization::NoQuantization&>(::stablehlo::quantization::_NoQuantization_default_instance_);
}
inline const ::stablehlo::quantization::NoQuantization& Method::no_quantization() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.Method.no_quantization)
  return _internal_no_quantization();
}
inline ::stablehlo::quantization::NoQuantization* Method::unsafe_arena_release_no_quantization() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.Method.no_quantization)
  if (_internal_has_no_quantization()) {
    clear_has_method();
    ::stablehlo::quantization::NoQuantization* temp = _impl_.method_.no_quantization_;
    _impl_.method_.no_quantization_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Method::unsafe_arena_set_allocated_no_quantization(::stablehlo::quantization::NoQuantization* no_quantization) {
  clear_method();
  if (no_quantization) {
    set_has_no_quantization();
    _impl_.method_.no_quantization_ = no_quantization;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.Method.no_quantization)
}
inline ::stablehlo::quantization::NoQuantization* Method::_internal_mutable_no_quantization() {
  if (!_internal_has_no_quantization()) {
    clear_method();
    set_has_no_quantization();
    _impl_.method_.no_quantization_ = CreateMaybeMessage< ::stablehlo::quantization::NoQuantization >(GetArenaForAllocation());
  }
  return _impl_.method_.no_quantization_;
}
inline ::stablehlo::quantization::NoQuantization* Method::mutable_no_quantization() {
  ::stablehlo::quantization::NoQuantization* _msg = _internal_mutable_no_quantization();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.Method.no_quantization)
  return _msg;
}

// .stablehlo.quantization.StaticRangePtq static_range_ptq = 2;
inline bool Method::_internal_has_static_range_ptq() const {
  return method_case() == kStaticRangePtq;
}
inline bool Method::has_static_range_ptq() const {
  return _internal_has_static_range_ptq();
}
inline void Method::set_has_static_range_ptq() {
  _impl_._oneof_case_[0] = kStaticRangePtq;
}
inline void Method::clear_static_range_ptq() {
  if (_internal_has_static_range_ptq()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.method_.static_range_ptq_;
    }
    clear_has_method();
  }
}
inline ::stablehlo::quantization::StaticRangePtq* Method::release_static_range_ptq() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.Method.static_range_ptq)
  if (_internal_has_static_range_ptq()) {
    clear_has_method();
    ::stablehlo::quantization::StaticRangePtq* temp = _impl_.method_.static_range_ptq_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.method_.static_range_ptq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::StaticRangePtq& Method::_internal_static_range_ptq() const {
  return _internal_has_static_range_ptq()
      ? *_impl_.method_.static_range_ptq_
      : reinterpret_cast< ::stablehlo::quantization::StaticRangePtq&>(::stablehlo::quantization::_StaticRangePtq_default_instance_);
}
inline const ::stablehlo::quantization::StaticRangePtq& Method::static_range_ptq() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.Method.static_range_ptq)
  return _internal_static_range_ptq();
}
inline ::stablehlo::quantization::StaticRangePtq* Method::unsafe_arena_release_static_range_ptq() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.Method.static_range_ptq)
  if (_internal_has_static_range_ptq()) {
    clear_has_method();
    ::stablehlo::quantization::StaticRangePtq* temp = _impl_.method_.static_range_ptq_;
    _impl_.method_.static_range_ptq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Method::unsafe_arena_set_allocated_static_range_ptq(::stablehlo::quantization::StaticRangePtq* static_range_ptq) {
  clear_method();
  if (static_range_ptq) {
    set_has_static_range_ptq();
    _impl_.method_.static_range_ptq_ = static_range_ptq;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.Method.static_range_ptq)
}
inline ::stablehlo::quantization::StaticRangePtq* Method::_internal_mutable_static_range_ptq() {
  if (!_internal_has_static_range_ptq()) {
    clear_method();
    set_has_static_range_ptq();
    _impl_.method_.static_range_ptq_ = CreateMaybeMessage< ::stablehlo::quantization::StaticRangePtq >(GetArenaForAllocation());
  }
  return _impl_.method_.static_range_ptq_;
}
inline ::stablehlo::quantization::StaticRangePtq* Method::mutable_static_range_ptq() {
  ::stablehlo::quantization::StaticRangePtq* _msg = _internal_mutable_static_range_ptq();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.Method.static_range_ptq)
  return _msg;
}

// .stablehlo.quantization.WeightOnlyPtq weight_only_ptq = 3;
inline bool Method::_internal_has_weight_only_ptq() const {
  return method_case() == kWeightOnlyPtq;
}
inline bool Method::has_weight_only_ptq() const {
  return _internal_has_weight_only_ptq();
}
inline void Method::set_has_weight_only_ptq() {
  _impl_._oneof_case_[0] = kWeightOnlyPtq;
}
inline void Method::clear_weight_only_ptq() {
  if (_internal_has_weight_only_ptq()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.method_.weight_only_ptq_;
    }
    clear_has_method();
  }
}
inline ::stablehlo::quantization::WeightOnlyPtq* Method::release_weight_only_ptq() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.Method.weight_only_ptq)
  if (_internal_has_weight_only_ptq()) {
    clear_has_method();
    ::stablehlo::quantization::WeightOnlyPtq* temp = _impl_.method_.weight_only_ptq_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.method_.weight_only_ptq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::WeightOnlyPtq& Method::_internal_weight_only_ptq() const {
  return _internal_has_weight_only_ptq()
      ? *_impl_.method_.weight_only_ptq_
      : reinterpret_cast< ::stablehlo::quantization::WeightOnlyPtq&>(::stablehlo::quantization::_WeightOnlyPtq_default_instance_);
}
inline const ::stablehlo::quantization::WeightOnlyPtq& Method::weight_only_ptq() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.Method.weight_only_ptq)
  return _internal_weight_only_ptq();
}
inline ::stablehlo::quantization::WeightOnlyPtq* Method::unsafe_arena_release_weight_only_ptq() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.Method.weight_only_ptq)
  if (_internal_has_weight_only_ptq()) {
    clear_has_method();
    ::stablehlo::quantization::WeightOnlyPtq* temp = _impl_.method_.weight_only_ptq_;
    _impl_.method_.weight_only_ptq_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Method::unsafe_arena_set_allocated_weight_only_ptq(::stablehlo::quantization::WeightOnlyPtq* weight_only_ptq) {
  clear_method();
  if (weight_only_ptq) {
    set_has_weight_only_ptq();
    _impl_.method_.weight_only_ptq_ = weight_only_ptq;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.Method.weight_only_ptq)
}
inline ::stablehlo::quantization::WeightOnlyPtq* Method::_internal_mutable_weight_only_ptq() {
  if (!_internal_has_weight_only_ptq()) {
    clear_method();
    set_has_weight_only_ptq();
    _impl_.method_.weight_only_ptq_ = CreateMaybeMessage< ::stablehlo::quantization::WeightOnlyPtq >(GetArenaForAllocation());
  }
  return _impl_.method_.weight_only_ptq_;
}
inline ::stablehlo::quantization::WeightOnlyPtq* Method::mutable_weight_only_ptq() {
  ::stablehlo::quantization::WeightOnlyPtq* _msg = _internal_mutable_weight_only_ptq();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.Method.weight_only_ptq)
  return _msg;
}

inline bool Method::has_method() const {
  return method_case() != METHOD_NOT_SET;
}
inline void Method::clear_has_method() {
  _impl_._oneof_case_[0] = METHOD_NOT_SET;
}
inline Method::MethodCase Method::method_case() const {
  return Method::MethodCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// QuantizationSpec

// .stablehlo.quantization.MatcherSpec matcher = 1;
inline bool QuantizationSpec::_internal_has_matcher() const {
  return this != internal_default_instance() && _impl_.matcher_ != nullptr;
}
inline bool QuantizationSpec::has_matcher() const {
  return _internal_has_matcher();
}
inline void QuantizationSpec::clear_matcher() {
  if (GetArenaForAllocation() == nullptr && _impl_.matcher_ != nullptr) {
    delete _impl_.matcher_;
  }
  _impl_.matcher_ = nullptr;
}
inline const ::stablehlo::quantization::MatcherSpec& QuantizationSpec::_internal_matcher() const {
  const ::stablehlo::quantization::MatcherSpec* p = _impl_.matcher_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::MatcherSpec&>(
      ::stablehlo::quantization::_MatcherSpec_default_instance_);
}
inline const ::stablehlo::quantization::MatcherSpec& QuantizationSpec::matcher() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationSpec.matcher)
  return _internal_matcher();
}
inline void QuantizationSpec::unsafe_arena_set_allocated_matcher(
    ::stablehlo::quantization::MatcherSpec* matcher) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.matcher_);
  }
  _impl_.matcher_ = matcher;
  if (matcher) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationSpec.matcher)
}
inline ::stablehlo::quantization::MatcherSpec* QuantizationSpec::release_matcher() {
  
  ::stablehlo::quantization::MatcherSpec* temp = _impl_.matcher_;
  _impl_.matcher_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::MatcherSpec* QuantizationSpec::unsafe_arena_release_matcher() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationSpec.matcher)
  
  ::stablehlo::quantization::MatcherSpec* temp = _impl_.matcher_;
  _impl_.matcher_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::MatcherSpec* QuantizationSpec::_internal_mutable_matcher() {
  
  if (_impl_.matcher_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::MatcherSpec>(GetArenaForAllocation());
    _impl_.matcher_ = p;
  }
  return _impl_.matcher_;
}
inline ::stablehlo::quantization::MatcherSpec* QuantizationSpec::mutable_matcher() {
  ::stablehlo::quantization::MatcherSpec* _msg = _internal_mutable_matcher();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationSpec.matcher)
  return _msg;
}
inline void QuantizationSpec::set_allocated_matcher(::stablehlo::quantization::MatcherSpec* matcher) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.matcher_;
  }
  if (matcher) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(matcher);
    if (message_arena != submessage_arena) {
      matcher = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, matcher, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.matcher_ = matcher;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationSpec.matcher)
}

// .stablehlo.quantization.Method method = 2;
inline bool QuantizationSpec::_internal_has_method() const {
  return this != internal_default_instance() && _impl_.method_ != nullptr;
}
inline bool QuantizationSpec::has_method() const {
  return _internal_has_method();
}
inline void QuantizationSpec::clear_method() {
  if (GetArenaForAllocation() == nullptr && _impl_.method_ != nullptr) {
    delete _impl_.method_;
  }
  _impl_.method_ = nullptr;
}
inline const ::stablehlo::quantization::Method& QuantizationSpec::_internal_method() const {
  const ::stablehlo::quantization::Method* p = _impl_.method_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::Method&>(
      ::stablehlo::quantization::_Method_default_instance_);
}
inline const ::stablehlo::quantization::Method& QuantizationSpec::method() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationSpec.method)
  return _internal_method();
}
inline void QuantizationSpec::unsafe_arena_set_allocated_method(
    ::stablehlo::quantization::Method* method) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.method_);
  }
  _impl_.method_ = method;
  if (method) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationSpec.method)
}
inline ::stablehlo::quantization::Method* QuantizationSpec::release_method() {
  
  ::stablehlo::quantization::Method* temp = _impl_.method_;
  _impl_.method_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::Method* QuantizationSpec::unsafe_arena_release_method() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationSpec.method)
  
  ::stablehlo::quantization::Method* temp = _impl_.method_;
  _impl_.method_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::Method* QuantizationSpec::_internal_mutable_method() {
  
  if (_impl_.method_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::Method>(GetArenaForAllocation());
    _impl_.method_ = p;
  }
  return _impl_.method_;
}
inline ::stablehlo::quantization::Method* QuantizationSpec::mutable_method() {
  ::stablehlo::quantization::Method* _msg = _internal_mutable_method();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationSpec.method)
  return _msg;
}
inline void QuantizationSpec::set_allocated_method(::stablehlo::quantization::Method* method) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.method_;
  }
  if (method) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(method);
    if (message_arena != submessage_arena) {
      method = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, method, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.method_ = method;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationSpec.method)
}

// -------------------------------------------------------------------

// QuantizationSpecs

// repeated .stablehlo.quantization.QuantizationSpec specs = 1;
inline int QuantizationSpecs::_internal_specs_size() const {
  return _impl_.specs_.size();
}
inline int QuantizationSpecs::specs_size() const {
  return _internal_specs_size();
}
inline void QuantizationSpecs::clear_specs() {
  _impl_.specs_.Clear();
}
inline ::stablehlo::quantization::QuantizationSpec* QuantizationSpecs::mutable_specs(int index) {
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationSpecs.specs)
  return _impl_.specs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationSpec >*
QuantizationSpecs::mutable_specs() {
  // @@protoc_insertion_point(field_mutable_list:stablehlo.quantization.QuantizationSpecs.specs)
  return &_impl_.specs_;
}
inline const ::stablehlo::quantization::QuantizationSpec& QuantizationSpecs::_internal_specs(int index) const {
  return _impl_.specs_.Get(index);
}
inline const ::stablehlo::quantization::QuantizationSpec& QuantizationSpecs::specs(int index) const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationSpecs.specs)
  return _internal_specs(index);
}
inline ::stablehlo::quantization::QuantizationSpec* QuantizationSpecs::_internal_add_specs() {
  return _impl_.specs_.Add();
}
inline ::stablehlo::quantization::QuantizationSpec* QuantizationSpecs::add_specs() {
  ::stablehlo::quantization::QuantizationSpec* _add = _internal_add_specs();
  // @@protoc_insertion_point(field_add:stablehlo.quantization.QuantizationSpecs.specs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::QuantizationSpec >&
QuantizationSpecs::specs() const {
  // @@protoc_insertion_point(field_list:stablehlo.quantization.QuantizationSpecs.specs)
  return _impl_.specs_;
}

// -------------------------------------------------------------------

// DebuggerConfig

// .stablehlo.quantization.DebuggerConfig.DebuggerType debugger_type = 1;
inline void DebuggerConfig::clear_debugger_type() {
  _impl_.debugger_type_ = 0;
}
inline ::stablehlo::quantization::DebuggerConfig_DebuggerType DebuggerConfig::_internal_debugger_type() const {
  return static_cast< ::stablehlo::quantization::DebuggerConfig_DebuggerType >(_impl_.debugger_type_);
}
inline ::stablehlo::quantization::DebuggerConfig_DebuggerType DebuggerConfig::debugger_type() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.DebuggerConfig.debugger_type)
  return _internal_debugger_type();
}
inline void DebuggerConfig::_internal_set_debugger_type(::stablehlo::quantization::DebuggerConfig_DebuggerType value) {
  
  _impl_.debugger_type_ = value;
}
inline void DebuggerConfig::set_debugger_type(::stablehlo::quantization::DebuggerConfig_DebuggerType value) {
  _internal_set_debugger_type(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.DebuggerConfig.debugger_type)
}

// string unquantized_dump_model_path = 2;
inline void DebuggerConfig::clear_unquantized_dump_model_path() {
  _impl_.unquantized_dump_model_path_.ClearToEmpty();
}
inline const std::string& DebuggerConfig::unquantized_dump_model_path() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.DebuggerConfig.unquantized_dump_model_path)
  return _internal_unquantized_dump_model_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggerConfig::set_unquantized_dump_model_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.unquantized_dump_model_path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stablehlo.quantization.DebuggerConfig.unquantized_dump_model_path)
}
inline std::string* DebuggerConfig::mutable_unquantized_dump_model_path() {
  std::string* _s = _internal_mutable_unquantized_dump_model_path();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.DebuggerConfig.unquantized_dump_model_path)
  return _s;
}
inline const std::string& DebuggerConfig::_internal_unquantized_dump_model_path() const {
  return _impl_.unquantized_dump_model_path_.Get();
}
inline void DebuggerConfig::_internal_set_unquantized_dump_model_path(const std::string& value) {
  
  _impl_.unquantized_dump_model_path_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggerConfig::_internal_mutable_unquantized_dump_model_path() {
  
  return _impl_.unquantized_dump_model_path_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggerConfig::release_unquantized_dump_model_path() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.DebuggerConfig.unquantized_dump_model_path)
  return _impl_.unquantized_dump_model_path_.Release();
}
inline void DebuggerConfig::set_allocated_unquantized_dump_model_path(std::string* unquantized_dump_model_path) {
  if (unquantized_dump_model_path != nullptr) {
    
  } else {
    
  }
  _impl_.unquantized_dump_model_path_.SetAllocated(unquantized_dump_model_path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.unquantized_dump_model_path_.IsDefault()) {
    _impl_.unquantized_dump_model_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.DebuggerConfig.unquantized_dump_model_path)
}

// string log_dir_path = 3;
inline void DebuggerConfig::clear_log_dir_path() {
  _impl_.log_dir_path_.ClearToEmpty();
}
inline const std::string& DebuggerConfig::log_dir_path() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.DebuggerConfig.log_dir_path)
  return _internal_log_dir_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggerConfig::set_log_dir_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.log_dir_path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stablehlo.quantization.DebuggerConfig.log_dir_path)
}
inline std::string* DebuggerConfig::mutable_log_dir_path() {
  std::string* _s = _internal_mutable_log_dir_path();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.DebuggerConfig.log_dir_path)
  return _s;
}
inline const std::string& DebuggerConfig::_internal_log_dir_path() const {
  return _impl_.log_dir_path_.Get();
}
inline void DebuggerConfig::_internal_set_log_dir_path(const std::string& value) {
  
  _impl_.log_dir_path_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggerConfig::_internal_mutable_log_dir_path() {
  
  return _impl_.log_dir_path_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggerConfig::release_log_dir_path() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.DebuggerConfig.log_dir_path)
  return _impl_.log_dir_path_.Release();
}
inline void DebuggerConfig::set_allocated_log_dir_path(std::string* log_dir_path) {
  if (log_dir_path != nullptr) {
    
  } else {
    
  }
  _impl_.log_dir_path_.SetAllocated(log_dir_path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.log_dir_path_.IsDefault()) {
    _impl_.log_dir_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.DebuggerConfig.log_dir_path)
}

// -------------------------------------------------------------------

// CalibrationOptions_CalibrationParameters

// int32 num_bins = 1;
inline void CalibrationOptions_CalibrationParameters::clear_num_bins() {
  _impl_.num_bins_ = 0;
}
inline int32_t CalibrationOptions_CalibrationParameters::_internal_num_bins() const {
  return _impl_.num_bins_;
}
inline int32_t CalibrationOptions_CalibrationParameters::num_bins() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CalibrationOptions.CalibrationParameters.num_bins)
  return _internal_num_bins();
}
inline void CalibrationOptions_CalibrationParameters::_internal_set_num_bins(int32_t value) {
  
  _impl_.num_bins_ = value;
}
inline void CalibrationOptions_CalibrationParameters::set_num_bins(int32_t value) {
  _internal_set_num_bins(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.CalibrationOptions.CalibrationParameters.num_bins)
}

// float min_percentile = 2;
inline void CalibrationOptions_CalibrationParameters::clear_min_percentile() {
  _impl_.min_percentile_ = 0;
}
inline float CalibrationOptions_CalibrationParameters::_internal_min_percentile() const {
  return _impl_.min_percentile_;
}
inline float CalibrationOptions_CalibrationParameters::min_percentile() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CalibrationOptions.CalibrationParameters.min_percentile)
  return _internal_min_percentile();
}
inline void CalibrationOptions_CalibrationParameters::_internal_set_min_percentile(float value) {
  
  _impl_.min_percentile_ = value;
}
inline void CalibrationOptions_CalibrationParameters::set_min_percentile(float value) {
  _internal_set_min_percentile(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.CalibrationOptions.CalibrationParameters.min_percentile)
}

// float max_percentile = 3;
inline void CalibrationOptions_CalibrationParameters::clear_max_percentile() {
  _impl_.max_percentile_ = 0;
}
inline float CalibrationOptions_CalibrationParameters::_internal_max_percentile() const {
  return _impl_.max_percentile_;
}
inline float CalibrationOptions_CalibrationParameters::max_percentile() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CalibrationOptions.CalibrationParameters.max_percentile)
  return _internal_max_percentile();
}
inline void CalibrationOptions_CalibrationParameters::_internal_set_max_percentile(float value) {
  
  _impl_.max_percentile_ = value;
}
inline void CalibrationOptions_CalibrationParameters::set_max_percentile(float value) {
  _internal_set_max_percentile(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.CalibrationOptions.CalibrationParameters.max_percentile)
}

// -------------------------------------------------------------------

// CalibrationOptions

// .stablehlo.quantization.CalibrationOptions.CalibrationMethod calibration_method = 1;
inline void CalibrationOptions::clear_calibration_method() {
  _impl_.calibration_method_ = 0;
}
inline ::stablehlo::quantization::CalibrationOptions_CalibrationMethod CalibrationOptions::_internal_calibration_method() const {
  return static_cast< ::stablehlo::quantization::CalibrationOptions_CalibrationMethod >(_impl_.calibration_method_);
}
inline ::stablehlo::quantization::CalibrationOptions_CalibrationMethod CalibrationOptions::calibration_method() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CalibrationOptions.calibration_method)
  return _internal_calibration_method();
}
inline void CalibrationOptions::_internal_set_calibration_method(::stablehlo::quantization::CalibrationOptions_CalibrationMethod value) {
  
  _impl_.calibration_method_ = value;
}
inline void CalibrationOptions::set_calibration_method(::stablehlo::quantization::CalibrationOptions_CalibrationMethod value) {
  _internal_set_calibration_method(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.CalibrationOptions.calibration_method)
}

// .stablehlo.quantization.CalibrationOptions.CalibrationParameters calibration_parameters = 2;
inline bool CalibrationOptions::_internal_has_calibration_parameters() const {
  return this != internal_default_instance() && _impl_.calibration_parameters_ != nullptr;
}
inline bool CalibrationOptions::has_calibration_parameters() const {
  return _internal_has_calibration_parameters();
}
inline void CalibrationOptions::clear_calibration_parameters() {
  if (GetArenaForAllocation() == nullptr && _impl_.calibration_parameters_ != nullptr) {
    delete _impl_.calibration_parameters_;
  }
  _impl_.calibration_parameters_ = nullptr;
}
inline const ::stablehlo::quantization::CalibrationOptions_CalibrationParameters& CalibrationOptions::_internal_calibration_parameters() const {
  const ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* p = _impl_.calibration_parameters_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::CalibrationOptions_CalibrationParameters&>(
      ::stablehlo::quantization::_CalibrationOptions_CalibrationParameters_default_instance_);
}
inline const ::stablehlo::quantization::CalibrationOptions_CalibrationParameters& CalibrationOptions::calibration_parameters() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CalibrationOptions.calibration_parameters)
  return _internal_calibration_parameters();
}
inline void CalibrationOptions::unsafe_arena_set_allocated_calibration_parameters(
    ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* calibration_parameters) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.calibration_parameters_);
  }
  _impl_.calibration_parameters_ = calibration_parameters;
  if (calibration_parameters) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.CalibrationOptions.calibration_parameters)
}
inline ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* CalibrationOptions::release_calibration_parameters() {
  
  ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* temp = _impl_.calibration_parameters_;
  _impl_.calibration_parameters_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* CalibrationOptions::unsafe_arena_release_calibration_parameters() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.CalibrationOptions.calibration_parameters)
  
  ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* temp = _impl_.calibration_parameters_;
  _impl_.calibration_parameters_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* CalibrationOptions::_internal_mutable_calibration_parameters() {
  
  if (_impl_.calibration_parameters_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::CalibrationOptions_CalibrationParameters>(GetArenaForAllocation());
    _impl_.calibration_parameters_ = p;
  }
  return _impl_.calibration_parameters_;
}
inline ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* CalibrationOptions::mutable_calibration_parameters() {
  ::stablehlo::quantization::CalibrationOptions_CalibrationParameters* _msg = _internal_mutable_calibration_parameters();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.CalibrationOptions.calibration_parameters)
  return _msg;
}
inline void CalibrationOptions::set_allocated_calibration_parameters(::stablehlo::quantization::CalibrationOptions_CalibrationParameters* calibration_parameters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.calibration_parameters_;
  }
  if (calibration_parameters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(calibration_parameters);
    if (message_arena != submessage_arena) {
      calibration_parameters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, calibration_parameters, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.calibration_parameters_ = calibration_parameters;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.CalibrationOptions.calibration_parameters)
}

// repeated .stablehlo.quantization.RepresentativeDatasetConfig representative_datasets = 3;
inline int CalibrationOptions::_internal_representative_datasets_size() const {
  return _impl_.representative_datasets_.size();
}
inline int CalibrationOptions::representative_datasets_size() const {
  return _internal_representative_datasets_size();
}
inline void CalibrationOptions::clear_representative_datasets() {
  _impl_.representative_datasets_.Clear();
}
inline ::stablehlo::quantization::RepresentativeDatasetConfig* CalibrationOptions::mutable_representative_datasets(int index) {
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.CalibrationOptions.representative_datasets)
  return _impl_.representative_datasets_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig >*
CalibrationOptions::mutable_representative_datasets() {
  // @@protoc_insertion_point(field_mutable_list:stablehlo.quantization.CalibrationOptions.representative_datasets)
  return &_impl_.representative_datasets_;
}
inline const ::stablehlo::quantization::RepresentativeDatasetConfig& CalibrationOptions::_internal_representative_datasets(int index) const {
  return _impl_.representative_datasets_.Get(index);
}
inline const ::stablehlo::quantization::RepresentativeDatasetConfig& CalibrationOptions::representative_datasets(int index) const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CalibrationOptions.representative_datasets)
  return _internal_representative_datasets(index);
}
inline ::stablehlo::quantization::RepresentativeDatasetConfig* CalibrationOptions::_internal_add_representative_datasets() {
  return _impl_.representative_datasets_.Add();
}
inline ::stablehlo::quantization::RepresentativeDatasetConfig* CalibrationOptions::add_representative_datasets() {
  ::stablehlo::quantization::RepresentativeDatasetConfig* _add = _internal_add_representative_datasets();
  // @@protoc_insertion_point(field_add:stablehlo.quantization.CalibrationOptions.representative_datasets)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::stablehlo::quantization::RepresentativeDatasetConfig >&
CalibrationOptions::representative_datasets() const {
  // @@protoc_insertion_point(field_list:stablehlo.quantization.CalibrationOptions.representative_datasets)
  return _impl_.representative_datasets_;
}

// string calibration_data_dir = 4;
inline void CalibrationOptions::clear_calibration_data_dir() {
  _impl_.calibration_data_dir_.ClearToEmpty();
}
inline const std::string& CalibrationOptions::calibration_data_dir() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CalibrationOptions.calibration_data_dir)
  return _internal_calibration_data_dir();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CalibrationOptions::set_calibration_data_dir(ArgT0&& arg0, ArgT... args) {
 
 _impl_.calibration_data_dir_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stablehlo.quantization.CalibrationOptions.calibration_data_dir)
}
inline std::string* CalibrationOptions::mutable_calibration_data_dir() {
  std::string* _s = _internal_mutable_calibration_data_dir();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.CalibrationOptions.calibration_data_dir)
  return _s;
}
inline const std::string& CalibrationOptions::_internal_calibration_data_dir() const {
  return _impl_.calibration_data_dir_.Get();
}
inline void CalibrationOptions::_internal_set_calibration_data_dir(const std::string& value) {
  
  _impl_.calibration_data_dir_.Set(value, GetArenaForAllocation());
}
inline std::string* CalibrationOptions::_internal_mutable_calibration_data_dir() {
  
  return _impl_.calibration_data_dir_.Mutable(GetArenaForAllocation());
}
inline std::string* CalibrationOptions::release_calibration_data_dir() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.CalibrationOptions.calibration_data_dir)
  return _impl_.calibration_data_dir_.Release();
}
inline void CalibrationOptions::set_allocated_calibration_data_dir(std::string* calibration_data_dir) {
  if (calibration_data_dir != nullptr) {
    
  } else {
    
  }
  _impl_.calibration_data_dir_.SetAllocated(calibration_data_dir, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.calibration_data_dir_.IsDefault()) {
    _impl_.calibration_data_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.CalibrationOptions.calibration_data_dir)
}

// bool force_regenerate_calibration_data = 5;
inline void CalibrationOptions::clear_force_regenerate_calibration_data() {
  _impl_.force_regenerate_calibration_data_ = false;
}
inline bool CalibrationOptions::_internal_force_regenerate_calibration_data() const {
  return _impl_.force_regenerate_calibration_data_;
}
inline bool CalibrationOptions::force_regenerate_calibration_data() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.CalibrationOptions.force_regenerate_calibration_data)
  return _internal_force_regenerate_calibration_data();
}
inline void CalibrationOptions::_internal_set_force_regenerate_calibration_data(bool value) {
  
  _impl_.force_regenerate_calibration_data_ = value;
}
inline void CalibrationOptions::set_force_regenerate_calibration_data(bool value) {
  _internal_set_force_regenerate_calibration_data(value);
  // @@protoc_insertion_point(field_set:stablehlo.quantization.CalibrationOptions.force_regenerate_calibration_data)
}

// -------------------------------------------------------------------

// QuantizationConfig

// .stablehlo.quantization.StaticRangePtqPreset static_range_ptq_preset = 1;
inline bool QuantizationConfig::_internal_has_static_range_ptq_preset() const {
  return preset_case() == kStaticRangePtqPreset;
}
inline bool QuantizationConfig::has_static_range_ptq_preset() const {
  return _internal_has_static_range_ptq_preset();
}
inline void QuantizationConfig::set_has_static_range_ptq_preset() {
  _impl_._oneof_case_[0] = kStaticRangePtqPreset;
}
inline void QuantizationConfig::clear_static_range_ptq_preset() {
  if (_internal_has_static_range_ptq_preset()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.preset_.static_range_ptq_preset_;
    }
    clear_has_preset();
  }
}
inline ::stablehlo::quantization::StaticRangePtqPreset* QuantizationConfig::release_static_range_ptq_preset() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationConfig.static_range_ptq_preset)
  if (_internal_has_static_range_ptq_preset()) {
    clear_has_preset();
    ::stablehlo::quantization::StaticRangePtqPreset* temp = _impl_.preset_.static_range_ptq_preset_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.preset_.static_range_ptq_preset_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::StaticRangePtqPreset& QuantizationConfig::_internal_static_range_ptq_preset() const {
  return _internal_has_static_range_ptq_preset()
      ? *_impl_.preset_.static_range_ptq_preset_
      : reinterpret_cast< ::stablehlo::quantization::StaticRangePtqPreset&>(::stablehlo::quantization::_StaticRangePtqPreset_default_instance_);
}
inline const ::stablehlo::quantization::StaticRangePtqPreset& QuantizationConfig::static_range_ptq_preset() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationConfig.static_range_ptq_preset)
  return _internal_static_range_ptq_preset();
}
inline ::stablehlo::quantization::StaticRangePtqPreset* QuantizationConfig::unsafe_arena_release_static_range_ptq_preset() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.QuantizationConfig.static_range_ptq_preset)
  if (_internal_has_static_range_ptq_preset()) {
    clear_has_preset();
    ::stablehlo::quantization::StaticRangePtqPreset* temp = _impl_.preset_.static_range_ptq_preset_;
    _impl_.preset_.static_range_ptq_preset_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QuantizationConfig::unsafe_arena_set_allocated_static_range_ptq_preset(::stablehlo::quantization::StaticRangePtqPreset* static_range_ptq_preset) {
  clear_preset();
  if (static_range_ptq_preset) {
    set_has_static_range_ptq_preset();
    _impl_.preset_.static_range_ptq_preset_ = static_range_ptq_preset;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationConfig.static_range_ptq_preset)
}
inline ::stablehlo::quantization::StaticRangePtqPreset* QuantizationConfig::_internal_mutable_static_range_ptq_preset() {
  if (!_internal_has_static_range_ptq_preset()) {
    clear_preset();
    set_has_static_range_ptq_preset();
    _impl_.preset_.static_range_ptq_preset_ = CreateMaybeMessage< ::stablehlo::quantization::StaticRangePtqPreset >(GetArenaForAllocation());
  }
  return _impl_.preset_.static_range_ptq_preset_;
}
inline ::stablehlo::quantization::StaticRangePtqPreset* QuantizationConfig::mutable_static_range_ptq_preset() {
  ::stablehlo::quantization::StaticRangePtqPreset* _msg = _internal_mutable_static_range_ptq_preset();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationConfig.static_range_ptq_preset)
  return _msg;
}

// .stablehlo.quantization.WeightOnlyPtqPreset weight_only_ptq_preset = 7;
inline bool QuantizationConfig::_internal_has_weight_only_ptq_preset() const {
  return preset_case() == kWeightOnlyPtqPreset;
}
inline bool QuantizationConfig::has_weight_only_ptq_preset() const {
  return _internal_has_weight_only_ptq_preset();
}
inline void QuantizationConfig::set_has_weight_only_ptq_preset() {
  _impl_._oneof_case_[0] = kWeightOnlyPtqPreset;
}
inline void QuantizationConfig::clear_weight_only_ptq_preset() {
  if (_internal_has_weight_only_ptq_preset()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.preset_.weight_only_ptq_preset_;
    }
    clear_has_preset();
  }
}
inline ::stablehlo::quantization::WeightOnlyPtqPreset* QuantizationConfig::release_weight_only_ptq_preset() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationConfig.weight_only_ptq_preset)
  if (_internal_has_weight_only_ptq_preset()) {
    clear_has_preset();
    ::stablehlo::quantization::WeightOnlyPtqPreset* temp = _impl_.preset_.weight_only_ptq_preset_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.preset_.weight_only_ptq_preset_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stablehlo::quantization::WeightOnlyPtqPreset& QuantizationConfig::_internal_weight_only_ptq_preset() const {
  return _internal_has_weight_only_ptq_preset()
      ? *_impl_.preset_.weight_only_ptq_preset_
      : reinterpret_cast< ::stablehlo::quantization::WeightOnlyPtqPreset&>(::stablehlo::quantization::_WeightOnlyPtqPreset_default_instance_);
}
inline const ::stablehlo::quantization::WeightOnlyPtqPreset& QuantizationConfig::weight_only_ptq_preset() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationConfig.weight_only_ptq_preset)
  return _internal_weight_only_ptq_preset();
}
inline ::stablehlo::quantization::WeightOnlyPtqPreset* QuantizationConfig::unsafe_arena_release_weight_only_ptq_preset() {
  // @@protoc_insertion_point(field_unsafe_arena_release:stablehlo.quantization.QuantizationConfig.weight_only_ptq_preset)
  if (_internal_has_weight_only_ptq_preset()) {
    clear_has_preset();
    ::stablehlo::quantization::WeightOnlyPtqPreset* temp = _impl_.preset_.weight_only_ptq_preset_;
    _impl_.preset_.weight_only_ptq_preset_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QuantizationConfig::unsafe_arena_set_allocated_weight_only_ptq_preset(::stablehlo::quantization::WeightOnlyPtqPreset* weight_only_ptq_preset) {
  clear_preset();
  if (weight_only_ptq_preset) {
    set_has_weight_only_ptq_preset();
    _impl_.preset_.weight_only_ptq_preset_ = weight_only_ptq_preset;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationConfig.weight_only_ptq_preset)
}
inline ::stablehlo::quantization::WeightOnlyPtqPreset* QuantizationConfig::_internal_mutable_weight_only_ptq_preset() {
  if (!_internal_has_weight_only_ptq_preset()) {
    clear_preset();
    set_has_weight_only_ptq_preset();
    _impl_.preset_.weight_only_ptq_preset_ = CreateMaybeMessage< ::stablehlo::quantization::WeightOnlyPtqPreset >(GetArenaForAllocation());
  }
  return _impl_.preset_.weight_only_ptq_preset_;
}
inline ::stablehlo::quantization::WeightOnlyPtqPreset* QuantizationConfig::mutable_weight_only_ptq_preset() {
  ::stablehlo::quantization::WeightOnlyPtqPreset* _msg = _internal_mutable_weight_only_ptq_preset();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationConfig.weight_only_ptq_preset)
  return _msg;
}

// .stablehlo.quantization.TfSavedModelConfig tf_saved_model = 2;
inline bool QuantizationConfig::_internal_has_tf_saved_model() const {
  return this != internal_default_instance() && _impl_.tf_saved_model_ != nullptr;
}
inline bool QuantizationConfig::has_tf_saved_model() const {
  return _internal_has_tf_saved_model();
}
inline void QuantizationConfig::clear_tf_saved_model() {
  if (GetArenaForAllocation() == nullptr && _impl_.tf_saved_model_ != nullptr) {
    delete _impl_.tf_saved_model_;
  }
  _impl_.tf_saved_model_ = nullptr;
}
inline const ::stablehlo::quantization::TfSavedModelConfig& QuantizationConfig::_internal_tf_saved_model() const {
  const ::stablehlo::quantization::TfSavedModelConfig* p = _impl_.tf_saved_model_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::TfSavedModelConfig&>(
      ::stablehlo::quantization::_TfSavedModelConfig_default_instance_);
}
inline const ::stablehlo::quantization::TfSavedModelConfig& QuantizationConfig::tf_saved_model() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationConfig.tf_saved_model)
  return _internal_tf_saved_model();
}
inline void QuantizationConfig::unsafe_arena_set_allocated_tf_saved_model(
    ::stablehlo::quantization::TfSavedModelConfig* tf_saved_model) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_saved_model_);
  }
  _impl_.tf_saved_model_ = tf_saved_model;
  if (tf_saved_model) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationConfig.tf_saved_model)
}
inline ::stablehlo::quantization::TfSavedModelConfig* QuantizationConfig::release_tf_saved_model() {
  
  ::stablehlo::quantization::TfSavedModelConfig* temp = _impl_.tf_saved_model_;
  _impl_.tf_saved_model_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::TfSavedModelConfig* QuantizationConfig::unsafe_arena_release_tf_saved_model() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationConfig.tf_saved_model)
  
  ::stablehlo::quantization::TfSavedModelConfig* temp = _impl_.tf_saved_model_;
  _impl_.tf_saved_model_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::TfSavedModelConfig* QuantizationConfig::_internal_mutable_tf_saved_model() {
  
  if (_impl_.tf_saved_model_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::TfSavedModelConfig>(GetArenaForAllocation());
    _impl_.tf_saved_model_ = p;
  }
  return _impl_.tf_saved_model_;
}
inline ::stablehlo::quantization::TfSavedModelConfig* QuantizationConfig::mutable_tf_saved_model() {
  ::stablehlo::quantization::TfSavedModelConfig* _msg = _internal_mutable_tf_saved_model();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationConfig.tf_saved_model)
  return _msg;
}
inline void QuantizationConfig::set_allocated_tf_saved_model(::stablehlo::quantization::TfSavedModelConfig* tf_saved_model) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.tf_saved_model_;
  }
  if (tf_saved_model) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(tf_saved_model);
    if (message_arena != submessage_arena) {
      tf_saved_model = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tf_saved_model, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tf_saved_model_ = tf_saved_model;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationConfig.tf_saved_model)
}

// .stablehlo.quantization.PipelineConfig pipeline_config = 3;
inline bool QuantizationConfig::_internal_has_pipeline_config() const {
  return this != internal_default_instance() && _impl_.pipeline_config_ != nullptr;
}
inline bool QuantizationConfig::has_pipeline_config() const {
  return _internal_has_pipeline_config();
}
inline void QuantizationConfig::clear_pipeline_config() {
  if (GetArenaForAllocation() == nullptr && _impl_.pipeline_config_ != nullptr) {
    delete _impl_.pipeline_config_;
  }
  _impl_.pipeline_config_ = nullptr;
}
inline const ::stablehlo::quantization::PipelineConfig& QuantizationConfig::_internal_pipeline_config() const {
  const ::stablehlo::quantization::PipelineConfig* p = _impl_.pipeline_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::PipelineConfig&>(
      ::stablehlo::quantization::_PipelineConfig_default_instance_);
}
inline const ::stablehlo::quantization::PipelineConfig& QuantizationConfig::pipeline_config() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationConfig.pipeline_config)
  return _internal_pipeline_config();
}
inline void QuantizationConfig::unsafe_arena_set_allocated_pipeline_config(
    ::stablehlo::quantization::PipelineConfig* pipeline_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.pipeline_config_);
  }
  _impl_.pipeline_config_ = pipeline_config;
  if (pipeline_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationConfig.pipeline_config)
}
inline ::stablehlo::quantization::PipelineConfig* QuantizationConfig::release_pipeline_config() {
  
  ::stablehlo::quantization::PipelineConfig* temp = _impl_.pipeline_config_;
  _impl_.pipeline_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::PipelineConfig* QuantizationConfig::unsafe_arena_release_pipeline_config() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationConfig.pipeline_config)
  
  ::stablehlo::quantization::PipelineConfig* temp = _impl_.pipeline_config_;
  _impl_.pipeline_config_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::PipelineConfig* QuantizationConfig::_internal_mutable_pipeline_config() {
  
  if (_impl_.pipeline_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::PipelineConfig>(GetArenaForAllocation());
    _impl_.pipeline_config_ = p;
  }
  return _impl_.pipeline_config_;
}
inline ::stablehlo::quantization::PipelineConfig* QuantizationConfig::mutable_pipeline_config() {
  ::stablehlo::quantization::PipelineConfig* _msg = _internal_mutable_pipeline_config();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationConfig.pipeline_config)
  return _msg;
}
inline void QuantizationConfig::set_allocated_pipeline_config(::stablehlo::quantization::PipelineConfig* pipeline_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.pipeline_config_;
  }
  if (pipeline_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(pipeline_config);
    if (message_arena != submessage_arena) {
      pipeline_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pipeline_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.pipeline_config_ = pipeline_config;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationConfig.pipeline_config)
}

// .stablehlo.quantization.QuantizationSpecs specs = 4;
inline bool QuantizationConfig::_internal_has_specs() const {
  return this != internal_default_instance() && _impl_.specs_ != nullptr;
}
inline bool QuantizationConfig::has_specs() const {
  return _internal_has_specs();
}
inline void QuantizationConfig::clear_specs() {
  if (GetArenaForAllocation() == nullptr && _impl_.specs_ != nullptr) {
    delete _impl_.specs_;
  }
  _impl_.specs_ = nullptr;
}
inline const ::stablehlo::quantization::QuantizationSpecs& QuantizationConfig::_internal_specs() const {
  const ::stablehlo::quantization::QuantizationSpecs* p = _impl_.specs_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::QuantizationSpecs&>(
      ::stablehlo::quantization::_QuantizationSpecs_default_instance_);
}
inline const ::stablehlo::quantization::QuantizationSpecs& QuantizationConfig::specs() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationConfig.specs)
  return _internal_specs();
}
inline void QuantizationConfig::unsafe_arena_set_allocated_specs(
    ::stablehlo::quantization::QuantizationSpecs* specs) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.specs_);
  }
  _impl_.specs_ = specs;
  if (specs) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationConfig.specs)
}
inline ::stablehlo::quantization::QuantizationSpecs* QuantizationConfig::release_specs() {
  
  ::stablehlo::quantization::QuantizationSpecs* temp = _impl_.specs_;
  _impl_.specs_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::QuantizationSpecs* QuantizationConfig::unsafe_arena_release_specs() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationConfig.specs)
  
  ::stablehlo::quantization::QuantizationSpecs* temp = _impl_.specs_;
  _impl_.specs_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::QuantizationSpecs* QuantizationConfig::_internal_mutable_specs() {
  
  if (_impl_.specs_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::QuantizationSpecs>(GetArenaForAllocation());
    _impl_.specs_ = p;
  }
  return _impl_.specs_;
}
inline ::stablehlo::quantization::QuantizationSpecs* QuantizationConfig::mutable_specs() {
  ::stablehlo::quantization::QuantizationSpecs* _msg = _internal_mutable_specs();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationConfig.specs)
  return _msg;
}
inline void QuantizationConfig::set_allocated_specs(::stablehlo::quantization::QuantizationSpecs* specs) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.specs_;
  }
  if (specs) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(specs);
    if (message_arena != submessage_arena) {
      specs = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, specs, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.specs_ = specs;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationConfig.specs)
}

// .stablehlo.quantization.DebuggerConfig debugger_config = 5;
inline bool QuantizationConfig::_internal_has_debugger_config() const {
  return this != internal_default_instance() && _impl_.debugger_config_ != nullptr;
}
inline bool QuantizationConfig::has_debugger_config() const {
  return _internal_has_debugger_config();
}
inline void QuantizationConfig::clear_debugger_config() {
  if (GetArenaForAllocation() == nullptr && _impl_.debugger_config_ != nullptr) {
    delete _impl_.debugger_config_;
  }
  _impl_.debugger_config_ = nullptr;
}
inline const ::stablehlo::quantization::DebuggerConfig& QuantizationConfig::_internal_debugger_config() const {
  const ::stablehlo::quantization::DebuggerConfig* p = _impl_.debugger_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::DebuggerConfig&>(
      ::stablehlo::quantization::_DebuggerConfig_default_instance_);
}
inline const ::stablehlo::quantization::DebuggerConfig& QuantizationConfig::debugger_config() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationConfig.debugger_config)
  return _internal_debugger_config();
}
inline void QuantizationConfig::unsafe_arena_set_allocated_debugger_config(
    ::stablehlo::quantization::DebuggerConfig* debugger_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debugger_config_);
  }
  _impl_.debugger_config_ = debugger_config;
  if (debugger_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationConfig.debugger_config)
}
inline ::stablehlo::quantization::DebuggerConfig* QuantizationConfig::release_debugger_config() {
  
  ::stablehlo::quantization::DebuggerConfig* temp = _impl_.debugger_config_;
  _impl_.debugger_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::DebuggerConfig* QuantizationConfig::unsafe_arena_release_debugger_config() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationConfig.debugger_config)
  
  ::stablehlo::quantization::DebuggerConfig* temp = _impl_.debugger_config_;
  _impl_.debugger_config_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::DebuggerConfig* QuantizationConfig::_internal_mutable_debugger_config() {
  
  if (_impl_.debugger_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::DebuggerConfig>(GetArenaForAllocation());
    _impl_.debugger_config_ = p;
  }
  return _impl_.debugger_config_;
}
inline ::stablehlo::quantization::DebuggerConfig* QuantizationConfig::mutable_debugger_config() {
  ::stablehlo::quantization::DebuggerConfig* _msg = _internal_mutable_debugger_config();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationConfig.debugger_config)
  return _msg;
}
inline void QuantizationConfig::set_allocated_debugger_config(::stablehlo::quantization::DebuggerConfig* debugger_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.debugger_config_;
  }
  if (debugger_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(debugger_config);
    if (message_arena != submessage_arena) {
      debugger_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debugger_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.debugger_config_ = debugger_config;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationConfig.debugger_config)
}

// .stablehlo.quantization.CalibrationOptions calibration_options = 6;
inline bool QuantizationConfig::_internal_has_calibration_options() const {
  return this != internal_default_instance() && _impl_.calibration_options_ != nullptr;
}
inline bool QuantizationConfig::has_calibration_options() const {
  return _internal_has_calibration_options();
}
inline void QuantizationConfig::clear_calibration_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.calibration_options_ != nullptr) {
    delete _impl_.calibration_options_;
  }
  _impl_.calibration_options_ = nullptr;
}
inline const ::stablehlo::quantization::CalibrationOptions& QuantizationConfig::_internal_calibration_options() const {
  const ::stablehlo::quantization::CalibrationOptions* p = _impl_.calibration_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::stablehlo::quantization::CalibrationOptions&>(
      ::stablehlo::quantization::_CalibrationOptions_default_instance_);
}
inline const ::stablehlo::quantization::CalibrationOptions& QuantizationConfig::calibration_options() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationConfig.calibration_options)
  return _internal_calibration_options();
}
inline void QuantizationConfig::unsafe_arena_set_allocated_calibration_options(
    ::stablehlo::quantization::CalibrationOptions* calibration_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.calibration_options_);
  }
  _impl_.calibration_options_ = calibration_options;
  if (calibration_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:stablehlo.quantization.QuantizationConfig.calibration_options)
}
inline ::stablehlo::quantization::CalibrationOptions* QuantizationConfig::release_calibration_options() {
  
  ::stablehlo::quantization::CalibrationOptions* temp = _impl_.calibration_options_;
  _impl_.calibration_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stablehlo::quantization::CalibrationOptions* QuantizationConfig::unsafe_arena_release_calibration_options() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationConfig.calibration_options)
  
  ::stablehlo::quantization::CalibrationOptions* temp = _impl_.calibration_options_;
  _impl_.calibration_options_ = nullptr;
  return temp;
}
inline ::stablehlo::quantization::CalibrationOptions* QuantizationConfig::_internal_mutable_calibration_options() {
  
  if (_impl_.calibration_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::stablehlo::quantization::CalibrationOptions>(GetArenaForAllocation());
    _impl_.calibration_options_ = p;
  }
  return _impl_.calibration_options_;
}
inline ::stablehlo::quantization::CalibrationOptions* QuantizationConfig::mutable_calibration_options() {
  ::stablehlo::quantization::CalibrationOptions* _msg = _internal_mutable_calibration_options();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationConfig.calibration_options)
  return _msg;
}
inline void QuantizationConfig::set_allocated_calibration_options(::stablehlo::quantization::CalibrationOptions* calibration_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.calibration_options_;
  }
  if (calibration_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(calibration_options);
    if (message_arena != submessage_arena) {
      calibration_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, calibration_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.calibration_options_ = calibration_options;
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationConfig.calibration_options)
}

// optional string report_file_path = 8;
inline bool QuantizationConfig::_internal_has_report_file_path() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool QuantizationConfig::has_report_file_path() const {
  return _internal_has_report_file_path();
}
inline void QuantizationConfig::clear_report_file_path() {
  _impl_.report_file_path_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& QuantizationConfig::report_file_path() const {
  // @@protoc_insertion_point(field_get:stablehlo.quantization.QuantizationConfig.report_file_path)
  return _internal_report_file_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void QuantizationConfig::set_report_file_path(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.report_file_path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:stablehlo.quantization.QuantizationConfig.report_file_path)
}
inline std::string* QuantizationConfig::mutable_report_file_path() {
  std::string* _s = _internal_mutable_report_file_path();
  // @@protoc_insertion_point(field_mutable:stablehlo.quantization.QuantizationConfig.report_file_path)
  return _s;
}
inline const std::string& QuantizationConfig::_internal_report_file_path() const {
  return _impl_.report_file_path_.Get();
}
inline void QuantizationConfig::_internal_set_report_file_path(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.report_file_path_.Set(value, GetArenaForAllocation());
}
inline std::string* QuantizationConfig::_internal_mutable_report_file_path() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.report_file_path_.Mutable(GetArenaForAllocation());
}
inline std::string* QuantizationConfig::release_report_file_path() {
  // @@protoc_insertion_point(field_release:stablehlo.quantization.QuantizationConfig.report_file_path)
  if (!_internal_has_report_file_path()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.report_file_path_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.report_file_path_.IsDefault()) {
    _impl_.report_file_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void QuantizationConfig::set_allocated_report_file_path(std::string* report_file_path) {
  if (report_file_path != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.report_file_path_.SetAllocated(report_file_path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.report_file_path_.IsDefault()) {
    _impl_.report_file_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:stablehlo.quantization.QuantizationConfig.report_file_path)
}

inline bool QuantizationConfig::has_preset() const {
  return preset_case() != PRESET_NOT_SET;
}
inline void QuantizationConfig::clear_has_preset() {
  _impl_._oneof_case_[0] = PRESET_NOT_SET;
}
inline QuantizationConfig::PresetCase QuantizationConfig::preset_case() const {
  return QuantizationConfig::PresetCase(_impl_._oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace quantization
}  // namespace stablehlo

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::stablehlo::quantization::DebuggerConfig_DebuggerType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stablehlo::quantization::DebuggerConfig_DebuggerType>() {
  return ::stablehlo::quantization::DebuggerConfig_DebuggerType_descriptor();
}
template <> struct is_proto_enum< ::stablehlo::quantization::CalibrationOptions_CalibrationMethod> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::stablehlo::quantization::CalibrationOptions_CalibrationMethod>() {
  return ::stablehlo::quantization::CalibrationOptions_CalibrationMethod_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2fstablehlo_2fquantization_5fconfig_2eproto
