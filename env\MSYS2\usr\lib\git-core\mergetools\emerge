diff_cmd () {
	"$merge_tool_path" -f emerge-files-command "$LOCAL" "$REMOTE"
}

diff_cmd_help () {
	echo "Use Emacs' Emerge"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" \
			-f emerge-files-with-ancestor-command \
			"$LOCAL" "$REMOTE" "$BASE" \
			"$(basename "$MERGED")"
	else
		"$merge_tool_path" \
			-f emerge-files-command \
			"$LOCAL" "$REMOTE" \
			"$(basename "$MERGED")"
	fi
}

merge_cmd_help () {
	echo "Use Emacs' Emerge"
}

translate_merge_tool_path() {
	echo emacs
}

exit_code_trustable () {
	true
}
