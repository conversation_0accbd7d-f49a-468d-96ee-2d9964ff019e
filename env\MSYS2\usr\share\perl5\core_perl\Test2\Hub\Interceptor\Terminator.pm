package Test2::Hub::Interceptor::Terminator;
use strict;
use warnings;

our $VERSION = '1.302194';


1;

__END__

=pod

=encoding UTF-8

=head1 NAME

Test2::Hub::Interceptor::Terminator - Exception class used by
Test2::Hub::Interceptor

=head1 SOURCE

The source code repository for Test2 can be found at
F<http://github.com/Test-More/test-more/>.

=head1 MAINTAINERS

=over 4

=item Chad Granum E<lt><EMAIL><gt>

=back

=head1 AUTHORS

=over 4

=item Chad Granum E<lt><EMAIL><gt>

=back

=head1 COPYRIGHT

Copyright 2020 Chad Granum E<lt><EMAIL><gt>.

This program is free software; you can redistribute it and/or
modify it under the same terms as Perl itself.

See F<http://dev.perl.org/licenses/>

=cut
