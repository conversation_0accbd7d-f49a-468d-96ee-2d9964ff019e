/* LTO partitioning logic routines.
   Copyright The GNU Toolchain Authors

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHA<PERSON><PERSON>ILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

#ifndef IPA_LOCALITY_CLONING_H
#define IPA_LOCALITY_CLONING_H

/* Structure describing locality partitions.  */
struct locality_partition_def
{
  int part_id;
  vec<cgraph_node *> nodes;
  int insns;
};

typedef struct locality_partition_def *locality_partition;

extern vec<locality_partition> locality_partitions;

#endif /* IPA_LOCALITY_CLONING_H */
