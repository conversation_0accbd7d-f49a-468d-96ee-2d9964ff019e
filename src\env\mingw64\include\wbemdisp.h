/*** Autogenerated by WIDL 10.8 from include/wbemdisp.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wbemdisp_h__
#define __wbemdisp_h__

/* Forward declarations */

#ifndef __SWbemLocator_FWD_DEFINED__
#define __SWbemLocator_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemLocator SWbemLocator;
#else
typedef struct SWbemLocator SWbemLocator;
#endif /* defined __cplusplus */
#endif /* defined __SWbemLocator_FWD_DEFINED__ */

#ifndef __SWbemNamedValueSet_FWD_DEFINED__
#define __SWbemNamedValueSet_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemNamedValueSet SWbemNamedValueSet;
#else
typedef struct SWbemNamedValueSet SWbemNamedValueSet;
#endif /* defined __cplusplus */
#endif /* defined __SWbemNamedValueSet_FWD_DEFINED__ */

#ifndef __SWbemObjectPath_FWD_DEFINED__
#define __SWbemObjectPath_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemObjectPath SWbemObjectPath;
#else
typedef struct SWbemObjectPath SWbemObjectPath;
#endif /* defined __cplusplus */
#endif /* defined __SWbemObjectPath_FWD_DEFINED__ */

#ifndef __SWbemLastError_FWD_DEFINED__
#define __SWbemLastError_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemLastError SWbemLastError;
#else
typedef struct SWbemLastError SWbemLastError;
#endif /* defined __cplusplus */
#endif /* defined __SWbemLastError_FWD_DEFINED__ */

#ifndef __SWbemSink_FWD_DEFINED__
#define __SWbemSink_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemSink SWbemSink;
#else
typedef struct SWbemSink SWbemSink;
#endif /* defined __cplusplus */
#endif /* defined __SWbemSink_FWD_DEFINED__ */

#ifndef __SWbemDateTime_FWD_DEFINED__
#define __SWbemDateTime_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemDateTime SWbemDateTime;
#else
typedef struct SWbemDateTime SWbemDateTime;
#endif /* defined __cplusplus */
#endif /* defined __SWbemDateTime_FWD_DEFINED__ */

#ifndef __SWbemRefresher_FWD_DEFINED__
#define __SWbemRefresher_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemRefresher SWbemRefresher;
#else
typedef struct SWbemRefresher SWbemRefresher;
#endif /* defined __cplusplus */
#endif /* defined __SWbemRefresher_FWD_DEFINED__ */

#ifndef __SWbemServices_FWD_DEFINED__
#define __SWbemServices_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemServices SWbemServices;
#else
typedef struct SWbemServices SWbemServices;
#endif /* defined __cplusplus */
#endif /* defined __SWbemServices_FWD_DEFINED__ */

#ifndef __SWbemServicesEx_FWD_DEFINED__
#define __SWbemServicesEx_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemServicesEx SWbemServicesEx;
#else
typedef struct SWbemServicesEx SWbemServicesEx;
#endif /* defined __cplusplus */
#endif /* defined __SWbemServicesEx_FWD_DEFINED__ */

#ifndef __SWbemObject_FWD_DEFINED__
#define __SWbemObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemObject SWbemObject;
#else
typedef struct SWbemObject SWbemObject;
#endif /* defined __cplusplus */
#endif /* defined __SWbemObject_FWD_DEFINED__ */

#ifndef __SWbemObjectEx_FWD_DEFINED__
#define __SWbemObjectEx_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemObjectEx SWbemObjectEx;
#else
typedef struct SWbemObjectEx SWbemObjectEx;
#endif /* defined __cplusplus */
#endif /* defined __SWbemObjectEx_FWD_DEFINED__ */

#ifndef __SWbemObjectSet_FWD_DEFINED__
#define __SWbemObjectSet_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemObjectSet SWbemObjectSet;
#else
typedef struct SWbemObjectSet SWbemObjectSet;
#endif /* defined __cplusplus */
#endif /* defined __SWbemObjectSet_FWD_DEFINED__ */

#ifndef __SWbemNamedValue_FWD_DEFINED__
#define __SWbemNamedValue_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemNamedValue SWbemNamedValue;
#else
typedef struct SWbemNamedValue SWbemNamedValue;
#endif /* defined __cplusplus */
#endif /* defined __SWbemNamedValue_FWD_DEFINED__ */

#ifndef __SWbemQualifier_FWD_DEFINED__
#define __SWbemQualifier_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemQualifier SWbemQualifier;
#else
typedef struct SWbemQualifier SWbemQualifier;
#endif /* defined __cplusplus */
#endif /* defined __SWbemQualifier_FWD_DEFINED__ */

#ifndef __SWbemQualifierSet_FWD_DEFINED__
#define __SWbemQualifierSet_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemQualifierSet SWbemQualifierSet;
#else
typedef struct SWbemQualifierSet SWbemQualifierSet;
#endif /* defined __cplusplus */
#endif /* defined __SWbemQualifierSet_FWD_DEFINED__ */

#ifndef __SWbemProperty_FWD_DEFINED__
#define __SWbemProperty_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemProperty SWbemProperty;
#else
typedef struct SWbemProperty SWbemProperty;
#endif /* defined __cplusplus */
#endif /* defined __SWbemProperty_FWD_DEFINED__ */

#ifndef __SWbemPropertySet_FWD_DEFINED__
#define __SWbemPropertySet_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemPropertySet SWbemPropertySet;
#else
typedef struct SWbemPropertySet SWbemPropertySet;
#endif /* defined __cplusplus */
#endif /* defined __SWbemPropertySet_FWD_DEFINED__ */

#ifndef __SWbemMethod_FWD_DEFINED__
#define __SWbemMethod_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemMethod SWbemMethod;
#else
typedef struct SWbemMethod SWbemMethod;
#endif /* defined __cplusplus */
#endif /* defined __SWbemMethod_FWD_DEFINED__ */

#ifndef __SWbemMethodSet_FWD_DEFINED__
#define __SWbemMethodSet_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemMethodSet SWbemMethodSet;
#else
typedef struct SWbemMethodSet SWbemMethodSet;
#endif /* defined __cplusplus */
#endif /* defined __SWbemMethodSet_FWD_DEFINED__ */

#ifndef __SWbemEventSource_FWD_DEFINED__
#define __SWbemEventSource_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemEventSource SWbemEventSource;
#else
typedef struct SWbemEventSource SWbemEventSource;
#endif /* defined __cplusplus */
#endif /* defined __SWbemEventSource_FWD_DEFINED__ */

#ifndef __SWbemSecurity_FWD_DEFINED__
#define __SWbemSecurity_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemSecurity SWbemSecurity;
#else
typedef struct SWbemSecurity SWbemSecurity;
#endif /* defined __cplusplus */
#endif /* defined __SWbemSecurity_FWD_DEFINED__ */

#ifndef __SWbemPrivilege_FWD_DEFINED__
#define __SWbemPrivilege_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemPrivilege SWbemPrivilege;
#else
typedef struct SWbemPrivilege SWbemPrivilege;
#endif /* defined __cplusplus */
#endif /* defined __SWbemPrivilege_FWD_DEFINED__ */

#ifndef __SWbemPrivilegeSet_FWD_DEFINED__
#define __SWbemPrivilegeSet_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemPrivilegeSet SWbemPrivilegeSet;
#else
typedef struct SWbemPrivilegeSet SWbemPrivilegeSet;
#endif /* defined __cplusplus */
#endif /* defined __SWbemPrivilegeSet_FWD_DEFINED__ */

#ifndef __SWbemRefreshableItem_FWD_DEFINED__
#define __SWbemRefreshableItem_FWD_DEFINED__
#ifdef __cplusplus
typedef class SWbemRefreshableItem SWbemRefreshableItem;
#else
typedef struct SWbemRefreshableItem SWbemRefreshableItem;
#endif /* defined __cplusplus */
#endif /* defined __SWbemRefreshableItem_FWD_DEFINED__ */

#ifndef __ISWbemLocator_FWD_DEFINED__
#define __ISWbemLocator_FWD_DEFINED__
typedef interface ISWbemLocator ISWbemLocator;
#ifdef __cplusplus
interface ISWbemLocator;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemServices_FWD_DEFINED__
#define __ISWbemServices_FWD_DEFINED__
typedef interface ISWbemServices ISWbemServices;
#ifdef __cplusplus
interface ISWbemServices;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemServicesEx_FWD_DEFINED__
#define __ISWbemServicesEx_FWD_DEFINED__
typedef interface ISWbemServicesEx ISWbemServicesEx;
#ifdef __cplusplus
interface ISWbemServicesEx;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemObject_FWD_DEFINED__
#define __ISWbemObject_FWD_DEFINED__
typedef interface ISWbemObject ISWbemObject;
#ifdef __cplusplus
interface ISWbemObject;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemObjectEx_FWD_DEFINED__
#define __ISWbemObjectEx_FWD_DEFINED__
typedef interface ISWbemObjectEx ISWbemObjectEx;
#ifdef __cplusplus
interface ISWbemObjectEx;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemLastError_FWD_DEFINED__
#define __ISWbemLastError_FWD_DEFINED__
typedef interface ISWbemLastError ISWbemLastError;
#ifdef __cplusplus
interface ISWbemLastError;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemObjectSet_FWD_DEFINED__
#define __ISWbemObjectSet_FWD_DEFINED__
typedef interface ISWbemObjectSet ISWbemObjectSet;
#ifdef __cplusplus
interface ISWbemObjectSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemNamedValueSet_FWD_DEFINED__
#define __ISWbemNamedValueSet_FWD_DEFINED__
typedef interface ISWbemNamedValueSet ISWbemNamedValueSet;
#ifdef __cplusplus
interface ISWbemNamedValueSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemNamedValue_FWD_DEFINED__
#define __ISWbemNamedValue_FWD_DEFINED__
typedef interface ISWbemNamedValue ISWbemNamedValue;
#ifdef __cplusplus
interface ISWbemNamedValue;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemObjectPath_FWD_DEFINED__
#define __ISWbemObjectPath_FWD_DEFINED__
typedef interface ISWbemObjectPath ISWbemObjectPath;
#ifdef __cplusplus
interface ISWbemObjectPath;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemProperty_FWD_DEFINED__
#define __ISWbemProperty_FWD_DEFINED__
typedef interface ISWbemProperty ISWbemProperty;
#ifdef __cplusplus
interface ISWbemProperty;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemPropertySet_FWD_DEFINED__
#define __ISWbemPropertySet_FWD_DEFINED__
typedef interface ISWbemPropertySet ISWbemPropertySet;
#ifdef __cplusplus
interface ISWbemPropertySet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemQualifier_FWD_DEFINED__
#define __ISWbemQualifier_FWD_DEFINED__
typedef interface ISWbemQualifier ISWbemQualifier;
#ifdef __cplusplus
interface ISWbemQualifier;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemQualifierSet_FWD_DEFINED__
#define __ISWbemQualifierSet_FWD_DEFINED__
typedef interface ISWbemQualifierSet ISWbemQualifierSet;
#ifdef __cplusplus
interface ISWbemQualifierSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemMethod_FWD_DEFINED__
#define __ISWbemMethod_FWD_DEFINED__
typedef interface ISWbemMethod ISWbemMethod;
#ifdef __cplusplus
interface ISWbemMethod;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemMethodSet_FWD_DEFINED__
#define __ISWbemMethodSet_FWD_DEFINED__
typedef interface ISWbemMethodSet ISWbemMethodSet;
#ifdef __cplusplus
interface ISWbemMethodSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemSink_FWD_DEFINED__
#define __ISWbemSink_FWD_DEFINED__
typedef interface ISWbemSink ISWbemSink;
#ifdef __cplusplus
interface ISWbemSink;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemSinkEvents_FWD_DEFINED__
#define __ISWbemSinkEvents_FWD_DEFINED__
typedef interface ISWbemSinkEvents ISWbemSinkEvents;
#ifdef __cplusplus
interface ISWbemSinkEvents;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemEventSource_FWD_DEFINED__
#define __ISWbemEventSource_FWD_DEFINED__
typedef interface ISWbemEventSource ISWbemEventSource;
#ifdef __cplusplus
interface ISWbemEventSource;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemSecurity_FWD_DEFINED__
#define __ISWbemSecurity_FWD_DEFINED__
typedef interface ISWbemSecurity ISWbemSecurity;
#ifdef __cplusplus
interface ISWbemSecurity;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemPrivilege_FWD_DEFINED__
#define __ISWbemPrivilege_FWD_DEFINED__
typedef interface ISWbemPrivilege ISWbemPrivilege;
#ifdef __cplusplus
interface ISWbemPrivilege;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemPrivilegeSet_FWD_DEFINED__
#define __ISWbemPrivilegeSet_FWD_DEFINED__
typedef interface ISWbemPrivilegeSet ISWbemPrivilegeSet;
#ifdef __cplusplus
interface ISWbemPrivilegeSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemDateTime_FWD_DEFINED__
#define __ISWbemDateTime_FWD_DEFINED__
typedef interface ISWbemDateTime ISWbemDateTime;
#ifdef __cplusplus
interface ISWbemDateTime;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemRefreshableItem_FWD_DEFINED__
#define __ISWbemRefreshableItem_FWD_DEFINED__
typedef interface ISWbemRefreshableItem ISWbemRefreshableItem;
#ifdef __cplusplus
interface ISWbemRefreshableItem;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemRefresher_FWD_DEFINED__
#define __ISWbemRefresher_FWD_DEFINED__
typedef interface ISWbemRefresher ISWbemRefresher;
#ifdef __cplusplus
interface ISWbemRefresher;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <dispex.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __WbemScripting_LIBRARY_DEFINED__
#define __WbemScripting_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_WbemScripting, 0x565783c6, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifndef __ISWbemDateTime_FWD_DEFINED__
#define __ISWbemDateTime_FWD_DEFINED__
typedef interface ISWbemDateTime ISWbemDateTime;
#ifdef __cplusplus
interface ISWbemDateTime;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemEventSource_FWD_DEFINED__
#define __ISWbemEventSource_FWD_DEFINED__
typedef interface ISWbemEventSource ISWbemEventSource;
#ifdef __cplusplus
interface ISWbemEventSource;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemLastError_FWD_DEFINED__
#define __ISWbemLastError_FWD_DEFINED__
typedef interface ISWbemLastError ISWbemLastError;
#ifdef __cplusplus
interface ISWbemLastError;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemLocator_FWD_DEFINED__
#define __ISWbemLocator_FWD_DEFINED__
typedef interface ISWbemLocator ISWbemLocator;
#ifdef __cplusplus
interface ISWbemLocator;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemMethod_FWD_DEFINED__
#define __ISWbemMethod_FWD_DEFINED__
typedef interface ISWbemMethod ISWbemMethod;
#ifdef __cplusplus
interface ISWbemMethod;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemMethodSet_FWD_DEFINED__
#define __ISWbemMethodSet_FWD_DEFINED__
typedef interface ISWbemMethodSet ISWbemMethodSet;
#ifdef __cplusplus
interface ISWbemMethodSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemNamedValue_FWD_DEFINED__
#define __ISWbemNamedValue_FWD_DEFINED__
typedef interface ISWbemNamedValue ISWbemNamedValue;
#ifdef __cplusplus
interface ISWbemNamedValue;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemNamedValueSet_FWD_DEFINED__
#define __ISWbemNamedValueSet_FWD_DEFINED__
typedef interface ISWbemNamedValueSet ISWbemNamedValueSet;
#ifdef __cplusplus
interface ISWbemNamedValueSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemObject_FWD_DEFINED__
#define __ISWbemObject_FWD_DEFINED__
typedef interface ISWbemObject ISWbemObject;
#ifdef __cplusplus
interface ISWbemObject;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemObjectEx_FWD_DEFINED__
#define __ISWbemObjectEx_FWD_DEFINED__
typedef interface ISWbemObjectEx ISWbemObjectEx;
#ifdef __cplusplus
interface ISWbemObjectEx;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemObjectPath_FWD_DEFINED__
#define __ISWbemObjectPath_FWD_DEFINED__
typedef interface ISWbemObjectPath ISWbemObjectPath;
#ifdef __cplusplus
interface ISWbemObjectPath;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemObjectSet_FWD_DEFINED__
#define __ISWbemObjectSet_FWD_DEFINED__
typedef interface ISWbemObjectSet ISWbemObjectSet;
#ifdef __cplusplus
interface ISWbemObjectSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemQualifier_FWD_DEFINED__
#define __ISWbemQualifier_FWD_DEFINED__
typedef interface ISWbemQualifier ISWbemQualifier;
#ifdef __cplusplus
interface ISWbemQualifier;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemQualifierSet_FWD_DEFINED__
#define __ISWbemQualifierSet_FWD_DEFINED__
typedef interface ISWbemQualifierSet ISWbemQualifierSet;
#ifdef __cplusplus
interface ISWbemQualifierSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemPrivilege_FWD_DEFINED__
#define __ISWbemPrivilege_FWD_DEFINED__
typedef interface ISWbemPrivilege ISWbemPrivilege;
#ifdef __cplusplus
interface ISWbemPrivilege;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemPrivilegeSet_FWD_DEFINED__
#define __ISWbemPrivilegeSet_FWD_DEFINED__
typedef interface ISWbemPrivilegeSet ISWbemPrivilegeSet;
#ifdef __cplusplus
interface ISWbemPrivilegeSet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemProperty_FWD_DEFINED__
#define __ISWbemProperty_FWD_DEFINED__
typedef interface ISWbemProperty ISWbemProperty;
#ifdef __cplusplus
interface ISWbemProperty;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemPropertySet_FWD_DEFINED__
#define __ISWbemPropertySet_FWD_DEFINED__
typedef interface ISWbemPropertySet ISWbemPropertySet;
#ifdef __cplusplus
interface ISWbemPropertySet;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemRefresher_FWD_DEFINED__
#define __ISWbemRefresher_FWD_DEFINED__
typedef interface ISWbemRefresher ISWbemRefresher;
#ifdef __cplusplus
interface ISWbemRefresher;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemRefreshableItem_FWD_DEFINED__
#define __ISWbemRefreshableItem_FWD_DEFINED__
typedef interface ISWbemRefreshableItem ISWbemRefreshableItem;
#ifdef __cplusplus
interface ISWbemRefreshableItem;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemSecurity_FWD_DEFINED__
#define __ISWbemSecurity_FWD_DEFINED__
typedef interface ISWbemSecurity ISWbemSecurity;
#ifdef __cplusplus
interface ISWbemSecurity;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemServices_FWD_DEFINED__
#define __ISWbemServices_FWD_DEFINED__
typedef interface ISWbemServices ISWbemServices;
#ifdef __cplusplus
interface ISWbemServices;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemServicesEx_FWD_DEFINED__
#define __ISWbemServicesEx_FWD_DEFINED__
typedef interface ISWbemServicesEx ISWbemServicesEx;
#ifdef __cplusplus
interface ISWbemServicesEx;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemSink_FWD_DEFINED__
#define __ISWbemSink_FWD_DEFINED__
typedef interface ISWbemSink ISWbemSink;
#ifdef __cplusplus
interface ISWbemSink;
#endif /* __cplusplus */
#endif

#ifndef __ISWbemSinkEvents_FWD_DEFINED__
#define __ISWbemSinkEvents_FWD_DEFINED__
typedef interface ISWbemSinkEvents ISWbemSinkEvents;
#ifdef __cplusplus
interface ISWbemSinkEvents;
#endif /* __cplusplus */
#endif

typedef enum WbemChangeFlagEnum {
    wbemChangeFlagCreateOrUpdate = 0x0,
    wbemChangeFlagUpdateOnly = 0x1,
    wbemChangeFlagCreateOnly = 0x2,
    wbemChangeFlagUpdateCompatible = 0x0,
    wbemChangeFlagUpdateSafeMode = 0x20,
    wbemChangeFlagUpdateForceMode = 0x40,
    wbemChangeFlagStrongValidation = 0x80,
    wbemChangeFlagAdvisory = 0x10000
} WbemChangeFlagEnum;
typedef enum WbemFlagEnum {
    wbemFlagReturnImmediately = 0x10,
    wbemFlagReturnWhenComplete = 0,
    wbemFlagBidirectional = 0,
    wbemFlagForwardOnly = 0x20,
    wbemFlagNoErrorObject = 0x40,
    wbemFlagReturnErrorObject = 0,
    wbemFlagSendStatus = 0x80,
    wbemFlagDontSendStatus = 0,
    wbemFlagEnsureLocatable = 0x100,
    wbemFlagDirectRead = 0x200,
    wbemFlagSendOnlySelected = 0,
    wbemFlagUseAmendedQualifiers = 0x20000,
    wbemFlagGetDefault = 0x0,
    wbemFlagSpawnInstance = 0x1,
    wbemFlagUseCurrentTime = 0x1
} WbemFlagEnum;
typedef enum WbemQueryFlagEnum {
    wbemQueryFlagDeep = 0,
    wbemQueryFlagShallow = 1,
    wbemQueryFlagPrototype = 2
} WbemQueryFlagEnum;
typedef enum WbemTextFlagEnum {
    wbemTextFlagNoFlavors = 0x1
} WbemTextFlagEnum;
typedef enum WbemTimeout {
    wbemTimeoutInfinite = 0xffffffff
} WbemTimeout;
typedef enum WbemComparisonFlagEnum {
    wbemComparisonFlagIncludeAll = 0,
    wbemComparisonFlagIgnoreQualifiers = 0x1,
    wbemComparisonFlagIgnoreObjectSource = 0x2,
    wbemComparisonFlagIgnoreDefaultValues = 0x4,
    wbemComparisonFlagIgnoreClass = 0x8,
    wbemComparisonFlagIgnoreCase = 0x10,
    wbemComparisonFlagIgnoreFlavor = 0x20
} WbemComparisonFlagEnum;
typedef enum WbemCimtypeEnum {
    wbemCimtypeSint16 = 2,
    wbemCimtypeSint32 = 3,
    wbemCimtypeReal32 = 4,
    wbemCimtypeReal64 = 5,
    wbemCimtypeString = 8,
    wbemCimtypeBoolean = 11,
    wbemCimtypeObject = 13,
    wbemCimtypeSint8 = 16,
    wbemCimtypeUint8 = 17,
    wbemCimtypeUint16 = 18,
    wbemCimtypeUint32 = 19,
    wbemCimtypeSint64 = 20,
    wbemCimtypeUint64 = 21,
    wbemCimtypeDatetime = 101,
    wbemCimtypeReference = 102,
    wbemCimtypeChar16 = 103
} WbemCimtypeEnum;
typedef enum WbemErrorEnum {
    wbemNoErr = 0,
    wbemErrFailed = 0x80041001,
    wbemErrNotFound = 0x80041002,
    wbemErrAccessDenied = 0x80041003,
    wbemErrProviderFailure = 0x80041004,
    wbemErrTypeMismatch = 0x80041005,
    wbemErrOutOfMemory = 0x80041006,
    wbemErrInvalidContext = 0x80041007,
    wbemErrInvalidParameter = 0x80041008,
    wbemErrNotAvailable = 0x80041009,
    wbemErrCriticalError = 0x8004100a,
    wbemErrInvalidStream = 0x8004100b,
    wbemErrNotSupported = 0x8004100c,
    wbemErrInvalidSuperclass = 0x8004100d,
    wbemErrInvalidNamespace = 0x8004100e,
    wbemErrInvalidObject = 0x8004100f,
    wbemErrInvalidClass = 0x80041010,
    wbemErrProviderNotFound = 0x80041011,
    wbemErrInvalidProviderRegistration = 0x80041012,
    wbemErrProviderLoadFailure = 0x80041013,
    wbemErrInitializationFailure = 0x80041014,
    wbemErrTransportFailure = 0x80041015,
    wbemErrInvalidOperation = 0x80041016,
    wbemErrInvalidQuery = 0x80041017,
    wbemErrInvalidQueryType = 0x80041018,
    wbemErrAlreadyExists = 0x80041019,
    wbemErrOverrideNotAllowed = 0x8004101a,
    wbemErrPropagatedQualifier = 0x8004101b,
    wbemErrPropagatedProperty = 0x8004101c,
    wbemErrUnexpected = 0x8004101d,
    wbemErrIllegalOperation = 0x8004101e,
    wbemErrCannotBeKey = 0x8004101f,
    wbemErrIncompleteClass = 0x80041020,
    wbemErrInvalidSyntax = 0x80041021,
    wbemErrNondecoratedObject = 0x80041022,
    wbemErrReadOnly = 0x80041023,
    wbemErrProviderNotCapable = 0x80041024,
    wbemErrClassHasChildren = 0x80041025,
    wbemErrClassHasInstances = 0x80041026,
    wbemErrQueryNotImplemented = 0x80041027,
    wbemErrIllegalNull = 0x80041028,
    wbemErrInvalidQualifierType = 0x80041029,
    wbemErrInvalidPropertyType = 0x8004102a,
    wbemErrValueOutOfRange = 0x8004102b,
    wbemErrCannotBeSingleton = 0x8004102c,
    wbemErrInvalidCimType = 0x8004102d,
    wbemErrInvalidMethod = 0x8004102e,
    wbemErrInvalidMethodParameters = 0x8004102f,
    wbemErrSystemProperty = 0x80041030,
    wbemErrInvalidProperty = 0x80041031,
    wbemErrCallCancelled = 0x80041032,
    wbemErrShuttingDown = 0x80041033,
    wbemErrPropagatedMethod = 0x80041034,
    wbemErrUnsupportedParameter = 0x80041035,
    wbemErrMissingParameter = 0x80041036,
    wbemErrInvalidParameterId = 0x80041037,
    wbemErrNonConsecutiveParameterIds = 0x80041038,
    wbemErrParameterIdOnRetval = 0x80041039,
    wbemErrInvalidObjectPath = 0x8004103a,
    wbemErrOutOfDiskSpace = 0x8004103b,
    wbemErrBufferTooSmall = 0x8004103c,
    wbemErrUnsupportedPutExtension = 0x8004103d,
    wbemErrUnknownObjectType = 0x8004103e,
    wbemErrUnknownPacketType = 0x8004103f,
    wbemErrMarshalVersionMismatch = 0x80041040,
    wbemErrMarshalInvalidSignature = 0x80041041,
    wbemErrInvalidQualifier = 0x80041042,
    wbemErrInvalidDuplicateParameter = 0x80041043,
    wbemErrTooMuchData = 0x80041044,
    wbemErrServerTooBusy = 0x80041045,
    wbemErrInvalidFlavor = 0x80041046,
    wbemErrCircularReference = 0x80041047,
    wbemErrUnsupportedClassUpdate = 0x80041048,
    wbemErrCannotChangeKeyInheritance = 0x80041049,
    wbemErrCannotChangeIndexInheritance = 0x80041050,
    wbemErrTooManyProperties = 0x80041051,
    wbemErrUpdateTypeMismatch = 0x80041052,
    wbemErrUpdateOverrideNotAllowed = 0x80041053,
    wbemErrUpdatePropagatedMethod = 0x80041054,
    wbemErrMethodNotImplemented = 0x80041055,
    wbemErrMethodDisabled = 0x80041056,
    wbemErrRefresherBusy = 0x80041057,
    wbemErrUnparsableQuery = 0x80041058,
    wbemErrNotEventClass = 0x80041059,
    wbemErrMissingGroupWithin = 0x8004105a,
    wbemErrMissingAggregationList = 0x8004105b,
    wbemErrPropertyNotAnObject = 0x8004105c,
    wbemErrAggregatingByObject = 0x8004105d,
    wbemErrUninterpretableProviderQuery = 0x8004105f,
    wbemErrBackupRestoreWinmgmtRunning = 0x80041060,
    wbemErrQueueOverflow = 0x80041061,
    wbemErrPrivilegeNotHeld = 0x80041062,
    wbemErrInvalidOperator = 0x80041063,
    wbemErrLocalCredentials = 0x80041064,
    wbemErrCannotBeAbstract = 0x80041065,
    wbemErrAmendedObject = 0x80041066,
    wbemErrClientTooSlow = 0x80041067,
    wbemErrNullSecurityDescriptor = 0x80041068,
    wbemErrTimeout = 0x80041069,
    wbemErrInvalidAssociation = 0x8004106a,
    wbemErrAmbiguousOperation = 0x8004106b,
    wbemErrQuotaViolation = 0x8004106c,
    wbemErrTransactionConflict = 0x8004106d,
    wbemErrForcedRollback = 0x8004106e,
    wbemErrUnsupportedLocale = 0x8004106f,
    wbemErrHandleOutOfDate = 0x80041070,
    wbemErrConnectionFailed = 0x80041071,
    wbemErrInvalidHandleRequest = 0x80041072,
    wbemErrPropertyNameTooWide = 0x80041073,
    wbemErrClassNameTooWide = 0x80041074,
    wbemErrMethodNameTooWide = 0x80041075,
    wbemErrQualifierNameTooWide = 0x80041076,
    wbemErrRerunCommand = 0x80041077,
    wbemErrDatabaseVerMismatch = 0x80041078,
    wbemErrVetoPut = 0x80041079,
    wbemErrVetoDelete = 0x8004107a,
    wbemErrInvalidLocale = 0x80041080,
    wbemErrProviderSuspended = 0x80041081,
    wbemErrSynchronizationRequired = 0x80041082,
    wbemErrNoSchema = 0x80041083,
    wbemErrProviderAlreadyRegistered = 0x80041084,
    wbemErrProviderNotRegistered = 0x80041085,
    wbemErrFatalTransportError = 0x80041086,
    wbemErrEncryptedConnectionRequired = 0x80041087,
    wbemErrRegistrationTooBroad = 0x80042001,
    wbemErrRegistrationTooPrecise = 0x80042002,
    wbemErrTimedout = 0x80043001,
    wbemErrResetToDefault = 0x80043002
} WbemErrorEnum;
typedef enum WbemAuthenticationLevelEnum {
    wbemAuthenticationLevelDefault = 0,
    wbemAuthenticationLevelNone = 1,
    wbemAuthenticationLevelConnect = 2,
    wbemAuthenticationLevelCall = 3,
    wbemAuthenticationLevelPkt = 4,
    wbemAuthenticationLevelPktIntegrity = 5,
    wbemAuthenticationLevelPktPrivacy = 6
} WbemAuthenticationLevelEnum;
typedef enum WbemImpersonationLevelEnum {
    wbemImpersonationLevelAnonymous = 1,
    wbemImpersonationLevelIdentify = 2,
    wbemImpersonationLevelImpersonate = 3,
    wbemImpersonationLevelDelegate = 4
} WbemImpersonationLevelEnum;
typedef enum WbemPrivilegeEnum {
    wbemPrivilegeCreateToken = 1,
    wbemPrivilegePrimaryToken = 2,
    wbemPrivilegeLockMemory = 3,
    wbemPrivilegeIncreaseQuota = 4,
    wbemPrivilegeMachineAccount = 5,
    wbemPrivilegeTcb = 6,
    wbemPrivilegeSecurity = 7,
    wbemPrivilegeTakeOwnership = 8,
    wbemPrivilegeLoadDriver = 9,
    wbemPrivilegeSystemProfile = 10,
    wbemPrivilegeSystemtime = 11,
    wbemPrivilegeProfileSingleProcess = 12,
    wbemPrivilegeIncreaseBasePriority = 13,
    wbemPrivilegeCreatePagefile = 14,
    wbemPrivilegeCreatePermanent = 15,
    wbemPrivilegeBackup = 16,
    wbemPrivilegeRestore = 17,
    wbemPrivilegeShutdown = 18,
    wbemPrivilegeDebug = 19,
    wbemPrivilegeAudit = 20,
    wbemPrivilegeSystemEnvironment = 21,
    wbemPrivilegeChangeNotify = 22,
    wbemPrivilegeRemoteShutdown = 23,
    wbemPrivilegeUndock = 24,
    wbemPrivilegeSyncAgent = 25,
    wbemPrivilegeEnableDelegation = 26,
    wbemPrivilegeManageVolume = 27
} WbemPrivilegeEnum;
typedef enum WbemObjectTextFormatEnum {
    wbemObjectTextFormatCIMDTD20 = 1,
    wbemObjectTextFormatWMIDTD20 = 2
} WbemObjectTextFormatEnum;
typedef enum WbemConnectOptionsEnum {
    wbemConnectFlagUseMaxWait = 0x80
} WbemConnectOptionsEnum;
#define WBEMS_DISPID_OBJECT_READY (1)

#define WBEMS_DISPID_COMPLETED (2)

#define WBEMS_DISPID_PROGRESS (3)

#define WBEMS_DISPID_OBJECT_PUT (4)

#define WBEMS_DISPID_CONNECTION_READY (5)

#define WBEMS_DISPID_DERIVATION (23)

/*****************************************************************************
 * SWbemLocator coclass
 */

DEFINE_GUID(CLSID_SWbemLocator, 0x76a64158, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("76a64158-cb41-11d1-8b02-00600806d9b6") SWbemLocator;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemLocator, 0x76a64158, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemNamedValueSet coclass
 */

DEFINE_GUID(CLSID_SWbemNamedValueSet, 0x9aed384e, 0xce8b, 0x11d1, 0x8b,0x05, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("9aed384e-ce8b-11d1-8b05-00600806d9b6") SWbemNamedValueSet;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemNamedValueSet, 0x9aed384e, 0xce8b, 0x11d1, 0x8b,0x05, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemObjectPath coclass
 */

DEFINE_GUID(CLSID_SWbemObjectPath, 0x5791bc26, 0xce9c, 0x11d1, 0x97,0xbf, 0x00,0x00,0xf8,0x1e,0x84,0x9c);

#ifdef __cplusplus
class DECLSPEC_UUID("5791bc26-ce9c-11d1-97bf-0000f81e849c") SWbemObjectPath;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemObjectPath, 0x5791bc26, 0xce9c, 0x11d1, 0x97,0xbf, 0x00,0x00,0xf8,0x1e,0x84,0x9c)
#endif
#endif

/*****************************************************************************
 * SWbemLastError coclass
 */

DEFINE_GUID(CLSID_SWbemLastError, 0xc2feeeac, 0xcfcd, 0x11d1, 0x8b,0x05, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("c2feeeac-cfcd-11d1-8b05-00600806d9b6") SWbemLastError;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemLastError, 0xc2feeeac, 0xcfcd, 0x11d1, 0x8b,0x05, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemSink coclass
 */

DEFINE_GUID(CLSID_SWbemSink, 0x75718c9a, 0xf029, 0x11d1, 0xa1,0xac, 0x00,0xc0,0x4f,0xb6,0xc2,0x23);

#ifdef __cplusplus
class DECLSPEC_UUID("75718c9a-f029-11d1-a1ac-00c04fb6c223") SWbemSink;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemSink, 0x75718c9a, 0xf029, 0x11d1, 0xa1,0xac, 0x00,0xc0,0x4f,0xb6,0xc2,0x23)
#endif
#endif

/*****************************************************************************
 * SWbemDateTime coclass
 */

DEFINE_GUID(CLSID_SWbemDateTime, 0x47dfbe54, 0xcf76, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a);

#ifdef __cplusplus
class DECLSPEC_UUID("47dfbe54-cf76-11d3-b38f-00105a1f473a") SWbemDateTime;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemDateTime, 0x47dfbe54, 0xcf76, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a)
#endif
#endif

/*****************************************************************************
 * SWbemRefresher coclass
 */

DEFINE_GUID(CLSID_SWbemRefresher, 0xd269bf5c, 0xd9c1, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a);

#ifdef __cplusplus
class DECLSPEC_UUID("d269bf5c-d9c1-11d3-b38f-00105a1f473a") SWbemRefresher;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemRefresher, 0xd269bf5c, 0xd9c1, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a)
#endif
#endif

/*****************************************************************************
 * SWbemServices coclass
 */

DEFINE_GUID(CLSID_SWbemServices, 0x04b83d63, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d63-21ae-11d2-8b33-00600806d9b6") SWbemServices;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemServices, 0x04b83d63, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemServicesEx coclass
 */

DEFINE_GUID(CLSID_SWbemServicesEx, 0x62e522dc, 0x8cf3, 0x40a8, 0x8b,0x2e, 0x37,0xd5,0x95,0x65,0x1e,0x40);

#ifdef __cplusplus
class DECLSPEC_UUID("62e522dc-8cf3-40a8-8b2e-37d595651e40") SWbemServicesEx;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemServicesEx, 0x62e522dc, 0x8cf3, 0x40a8, 0x8b,0x2e, 0x37,0xd5,0x95,0x65,0x1e,0x40)
#endif
#endif

/*****************************************************************************
 * SWbemObject coclass
 */

DEFINE_GUID(CLSID_SWbemObject, 0x04b83d62, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d62-21ae-11d2-8b33-00600806d9b6") SWbemObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemObject, 0x04b83d62, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemObjectEx coclass
 */

DEFINE_GUID(CLSID_SWbemObjectEx, 0xd6bdafb2, 0x9435, 0x491f, 0xbb,0x87, 0x6a,0xa0,0xf0,0xbc,0x31,0xa2);

#ifdef __cplusplus
class DECLSPEC_UUID("d6bdafb2-9435-491f-bb87-6aa0f0bc31a2") SWbemObjectEx;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemObjectEx, 0xd6bdafb2, 0x9435, 0x491f, 0xbb,0x87, 0x6a,0xa0,0xf0,0xbc,0x31,0xa2)
#endif
#endif

/*****************************************************************************
 * SWbemObjectSet coclass
 */

DEFINE_GUID(CLSID_SWbemObjectSet, 0x04b83d61, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d61-21ae-11d2-8b33-00600806d9b6") SWbemObjectSet;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemObjectSet, 0x04b83d61, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemNamedValue coclass
 */

DEFINE_GUID(CLSID_SWbemNamedValue, 0x04b83d60, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d60-21ae-11d2-8b33-00600806d9b6") SWbemNamedValue;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemNamedValue, 0x04b83d60, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemQualifier coclass
 */

DEFINE_GUID(CLSID_SWbemQualifier, 0x04b83d5f, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d5f-21ae-11d2-8b33-00600806d9b6") SWbemQualifier;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemQualifier, 0x04b83d5f, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemQualifierSet coclass
 */

DEFINE_GUID(CLSID_SWbemQualifierSet, 0x04b83d5e, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d5e-21ae-11d2-8b33-00600806d9b6") SWbemQualifierSet;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemQualifierSet, 0x04b83d5e, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemProperty coclass
 */

DEFINE_GUID(CLSID_SWbemProperty, 0x04b83d5d, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d5d-21ae-11d2-8b33-00600806d9b6") SWbemProperty;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemProperty, 0x04b83d5d, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemPropertySet coclass
 */

DEFINE_GUID(CLSID_SWbemPropertySet, 0x04b83d5c, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d5c-21ae-11d2-8b33-00600806d9b6") SWbemPropertySet;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemPropertySet, 0x04b83d5c, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemMethod coclass
 */

DEFINE_GUID(CLSID_SWbemMethod, 0x04b83d5b, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d5b-21ae-11d2-8b33-00600806d9b6") SWbemMethod;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemMethod, 0x04b83d5b, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemMethodSet coclass
 */

DEFINE_GUID(CLSID_SWbemMethodSet, 0x04b83d5a, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d5a-21ae-11d2-8b33-00600806d9b6") SWbemMethodSet;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemMethodSet, 0x04b83d5a, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemEventSource coclass
 */

DEFINE_GUID(CLSID_SWbemEventSource, 0x04b83d58, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("04b83d58-21ae-11d2-8b33-00600806d9b6") SWbemEventSource;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemEventSource, 0x04b83d58, 0x21ae, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemSecurity coclass
 */

DEFINE_GUID(CLSID_SWbemSecurity, 0xb54d66e9, 0x2287, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("b54d66e9-2287-11d2-8b33-00600806d9b6") SWbemSecurity;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemSecurity, 0xb54d66e9, 0x2287, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemPrivilege coclass
 */

DEFINE_GUID(CLSID_SWbemPrivilege, 0x26ee67bc, 0x5804, 0x11d2, 0x8b,0x4a, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("26ee67bc-5804-11d2-8b4a-00600806d9b6") SWbemPrivilege;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemPrivilege, 0x26ee67bc, 0x5804, 0x11d2, 0x8b,0x4a, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemPrivilegeSet coclass
 */

DEFINE_GUID(CLSID_SWbemPrivilegeSet, 0x26ee67be, 0x5804, 0x11d2, 0x8b,0x4a, 0x00,0x60,0x08,0x06,0xd9,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("26ee67be-5804-11d2-8b4a-00600806d9b6") SWbemPrivilegeSet;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemPrivilegeSet, 0x26ee67be, 0x5804, 0x11d2, 0x8b,0x4a, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#endif

/*****************************************************************************
 * SWbemRefreshableItem coclass
 */

DEFINE_GUID(CLSID_SWbemRefreshableItem, 0x8c6854bc, 0xde4b, 0x11d3, 0xb3,0x90, 0x00,0x10,0x5a,0x1f,0x47,0x3a);

#ifdef __cplusplus
class DECLSPEC_UUID("8c6854bc-de4b-11d3-b390-00105a1f473a") SWbemRefreshableItem;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SWbemRefreshableItem, 0x8c6854bc, 0xde4b, 0x11d3, 0xb3,0x90, 0x00,0x10,0x5a,0x1f,0x47,0x3a)
#endif
#endif

#endif /* __WbemScripting_LIBRARY_DEFINED__ */
/*****************************************************************************
 * ISWbemLocator interface
 */
#ifndef __ISWbemLocator_INTERFACE_DEFINED__
#define __ISWbemLocator_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemLocator, 0x76a6415b, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("76a6415b-cb41-11d1-8b02-00600806d9b6")
ISWbemLocator : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE ConnectServer(
        BSTR strServer = L".",
        BSTR strNamespace = L"",
        BSTR strUser = L"",
        BSTR strPassword = L"",
        BSTR strLocale = L"",
        BSTR strAuthority = L"",
        LONG iSecurityFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemServices **objWbemServices = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Security_(
        ISWbemSecurity **objWbemSecurity) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemLocator, 0x76a6415b, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemLocatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemLocator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemLocator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemLocator *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemLocator *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemLocator *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemLocator *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemLocator *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemLocator methods ***/
    HRESULT (STDMETHODCALLTYPE *ConnectServer)(
        ISWbemLocator *This,
        BSTR strServer,
        BSTR strNamespace,
        BSTR strUser,
        BSTR strPassword,
        BSTR strLocale,
        BSTR strAuthority,
        LONG iSecurityFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemServices **objWbemServices);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemLocator *This,
        ISWbemSecurity **objWbemSecurity);

    END_INTERFACE
} ISWbemLocatorVtbl;

interface ISWbemLocator {
    CONST_VTBL ISWbemLocatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemLocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemLocator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemLocator_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemLocator_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemLocator_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemLocator_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemLocator_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemLocator methods ***/
#define ISWbemLocator_ConnectServer(This,strServer,strNamespace,strUser,strPassword,strLocale,strAuthority,iSecurityFlags,objWbemNamedValueSet,objWbemServices) (This)->lpVtbl->ConnectServer(This,strServer,strNamespace,strUser,strPassword,strLocale,strAuthority,iSecurityFlags,objWbemNamedValueSet,objWbemServices)
#define ISWbemLocator_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemLocator_QueryInterface(ISWbemLocator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemLocator_AddRef(ISWbemLocator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemLocator_Release(ISWbemLocator* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemLocator_GetTypeInfoCount(ISWbemLocator* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemLocator_GetTypeInfo(ISWbemLocator* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemLocator_GetIDsOfNames(ISWbemLocator* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemLocator_Invoke(ISWbemLocator* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemLocator methods ***/
static inline HRESULT ISWbemLocator_ConnectServer(ISWbemLocator* This,BSTR strServer,BSTR strNamespace,BSTR strUser,BSTR strPassword,BSTR strLocale,BSTR strAuthority,LONG iSecurityFlags,IDispatch *objWbemNamedValueSet,ISWbemServices **objWbemServices) {
    return This->lpVtbl->ConnectServer(This,strServer,strNamespace,strUser,strPassword,strLocale,strAuthority,iSecurityFlags,objWbemNamedValueSet,objWbemServices);
}
static inline HRESULT ISWbemLocator_get_Security_(ISWbemLocator* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
#endif
#endif

#endif


#endif  /* __ISWbemLocator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemServices interface
 */
#ifndef __ISWbemServices_INTERFACE_DEFINED__
#define __ISWbemServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemServices, 0x76a6415c, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("76a6415c-cb41-11d1-8b02-00600806d9b6")
ISWbemServices : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Get(
        BSTR strObjectPath = L"",
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObject **objWbemObject = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAsync(
        IDispatch *objWbemSink,
        BSTR strObjectPath = L"",
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        BSTR strObjectPath,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteAsync(
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstancesOf(
        BSTR strClass,
        LONG iFlags = wbemFlagReturnImmediately,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstancesOfAsync(
        IDispatch *objWbemSink,
        BSTR strClass,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SubclassesOf(
        BSTR strSuperclass = L"",
        LONG iFlags = wbemFlagReturnImmediately | wbemQueryFlagDeep,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SubclassesOfAsync(
        IDispatch *objWbemSink,
        BSTR strSuperclass = L"",
        LONG iFlags = wbemQueryFlagDeep,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecQuery(
        BSTR strQuery,
        BSTR strQueryLanguage = L"WQL",
        LONG iFlags = wbemFlagReturnImmediately,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecQueryAsync(
        IDispatch *objWbemSink,
        BSTR strQuery,
        BSTR strQueryLanguage = L"WQL",
        LONG lFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE AssociatorsOf(
        BSTR strObjectPath,
        BSTR strAssocClass = L"",
        BSTR strResultClass = L"",
        BSTR strResultRole = L"",
        BSTR strRole = L"",
        VARIANT_BOOL bClassesOnly = FALSE,
        VARIANT_BOOL bSchemaOnly = FALSE,
        BSTR strRequiredAssocQualifier = L"",
        BSTR strRequiredQualifier = L"",
        LONG iFlags = wbemFlagReturnImmediately,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE AssociatorsOfAsync(
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strAssocClass = L"",
        BSTR strResultClass = L"",
        BSTR strResultRole = L"",
        BSTR strRole = L"",
        VARIANT_BOOL bClassesOnly = FALSE,
        VARIANT_BOOL bSchemaOnly = FALSE,
        BSTR strRequiredAssocQualifier = L"",
        BSTR strRequiredQualifier = L"",
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReferencesTo(
        BSTR strObjectPath,
        BSTR strResultClass = L"",
        BSTR strRole = L"",
        VARIANT_BOOL bClassesOnly = FALSE,
        VARIANT_BOOL bSchemaOnly = FALSE,
        BSTR strRequiredQualifier = L"",
        LONG iFlags = wbemFlagReturnImmediately,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReferencesToAsync(
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strResultClass = L"",
        BSTR strRole = L"",
        VARIANT_BOOL bClassesOnly = FALSE,
        VARIANT_BOOL bSchemaOnly = FALSE,
        BSTR strRequiredQualifier = L"",
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecNotificationQuery(
        BSTR strQuery,
        BSTR strQueryLanguage = L"WQL",
        LONG iFlags = wbemFlagReturnImmediately | wbemFlagForwardOnly,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemEventSource **objWbemEventSource = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecNotificationQueryAsync(
        IDispatch *objWbemSink,
        BSTR strQuery,
        BSTR strQueryLanguage = L"WQL",
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecMethod(
        BSTR strObjectPath,
        BSTR strMethodName,
        IDispatch *objWbemInParameters = 0,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObject **objWbemOutParameters = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecMethodAsync(
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strMethodName,
        IDispatch *objWbemInParameters = 0,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Security_(
        ISWbemSecurity **objWbemSecurity) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemServices, 0x76a6415c, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemServices *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemServices *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemServices *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemServices *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemServices *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemServices methods ***/
    HRESULT (STDMETHODCALLTYPE *Get)(
        ISWbemServices *This,
        BSTR strObjectPath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *GetAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        ISWbemServices *This,
        BSTR strObjectPath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet);

    HRESULT (STDMETHODCALLTYPE *DeleteAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *InstancesOf)(
        ISWbemServices *This,
        BSTR strClass,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *InstancesOfAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strClass,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *SubclassesOf)(
        ISWbemServices *This,
        BSTR strSuperclass,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *SubclassesOfAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strSuperclass,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecQuery)(
        ISWbemServices *This,
        BSTR strQuery,
        BSTR strQueryLanguage,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *ExecQueryAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strQuery,
        BSTR strQueryLanguage,
        LONG lFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *AssociatorsOf)(
        ISWbemServices *This,
        BSTR strObjectPath,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *AssociatorsOfAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ReferencesTo)(
        ISWbemServices *This,
        BSTR strObjectPath,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *ReferencesToAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecNotificationQuery)(
        ISWbemServices *This,
        BSTR strQuery,
        BSTR strQueryLanguage,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemEventSource **objWbemEventSource);

    HRESULT (STDMETHODCALLTYPE *ExecNotificationQueryAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strQuery,
        BSTR strQueryLanguage,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecMethod)(
        ISWbemServices *This,
        BSTR strObjectPath,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObject **objWbemOutParameters);

    HRESULT (STDMETHODCALLTYPE *ExecMethodAsync)(
        ISWbemServices *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemServices *This,
        ISWbemSecurity **objWbemSecurity);

    END_INTERFACE
} ISWbemServicesVtbl;

interface ISWbemServices {
    CONST_VTBL ISWbemServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemServices_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemServices_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemServices_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemServices_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemServices_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemServices methods ***/
#define ISWbemServices_Get(This,strObjectPath,iFlags,objWbemNamedValueSet,objWbemObject) (This)->lpVtbl->Get(This,strObjectPath,iFlags,objWbemNamedValueSet,objWbemObject)
#define ISWbemServices_GetAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->GetAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_Delete(This,strObjectPath,iFlags,objWbemNamedValueSet) (This)->lpVtbl->Delete(This,strObjectPath,iFlags,objWbemNamedValueSet)
#define ISWbemServices_DeleteAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->DeleteAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_InstancesOf(This,strClass,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->InstancesOf(This,strClass,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServices_InstancesOfAsync(This,objWbemSink,strClass,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->InstancesOfAsync(This,objWbemSink,strClass,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_SubclassesOf(This,strSuperclass,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->SubclassesOf(This,strSuperclass,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServices_SubclassesOfAsync(This,objWbemSink,strSuperclass,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->SubclassesOfAsync(This,objWbemSink,strSuperclass,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_ExecQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->ExecQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServices_ExecQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,lFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,lFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_AssociatorsOf(This,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->AssociatorsOf(This,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServices_AssociatorsOfAsync(This,objWbemSink,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->AssociatorsOfAsync(This,objWbemSink,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_ReferencesTo(This,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->ReferencesTo(This,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServices_ReferencesToAsync(This,objWbemSink,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ReferencesToAsync(This,objWbemSink,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_ExecNotificationQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemEventSource) (This)->lpVtbl->ExecNotificationQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemEventSource)
#define ISWbemServices_ExecNotificationQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecNotificationQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_ExecMethod(This,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters) (This)->lpVtbl->ExecMethod(This,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters)
#define ISWbemServices_ExecMethodAsync(This,objWbemSink,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecMethodAsync(This,objWbemSink,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServices_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemServices_QueryInterface(ISWbemServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemServices_AddRef(ISWbemServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemServices_Release(ISWbemServices* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemServices_GetTypeInfoCount(ISWbemServices* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemServices_GetTypeInfo(ISWbemServices* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemServices_GetIDsOfNames(ISWbemServices* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemServices_Invoke(ISWbemServices* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemServices methods ***/
static inline HRESULT ISWbemServices_Get(ISWbemServices* This,BSTR strObjectPath,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObject **objWbemObject) {
    return This->lpVtbl->Get(This,strObjectPath,iFlags,objWbemNamedValueSet,objWbemObject);
}
static inline HRESULT ISWbemServices_GetAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strObjectPath,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->GetAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_Delete(ISWbemServices* This,BSTR strObjectPath,LONG iFlags,IDispatch *objWbemNamedValueSet) {
    return This->lpVtbl->Delete(This,strObjectPath,iFlags,objWbemNamedValueSet);
}
static inline HRESULT ISWbemServices_DeleteAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strObjectPath,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->DeleteAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_InstancesOf(ISWbemServices* This,BSTR strClass,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->InstancesOf(This,strClass,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServices_InstancesOfAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strClass,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->InstancesOfAsync(This,objWbemSink,strClass,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_SubclassesOf(ISWbemServices* This,BSTR strSuperclass,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->SubclassesOf(This,strSuperclass,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServices_SubclassesOfAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strSuperclass,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->SubclassesOfAsync(This,objWbemSink,strSuperclass,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_ExecQuery(ISWbemServices* This,BSTR strQuery,BSTR strQueryLanguage,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->ExecQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServices_ExecQueryAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strQuery,BSTR strQueryLanguage,LONG lFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,lFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_AssociatorsOf(ISWbemServices* This,BSTR strObjectPath,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->AssociatorsOf(This,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServices_AssociatorsOfAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strObjectPath,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->AssociatorsOfAsync(This,objWbemSink,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_ReferencesTo(ISWbemServices* This,BSTR strObjectPath,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->ReferencesTo(This,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServices_ReferencesToAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strObjectPath,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ReferencesToAsync(This,objWbemSink,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_ExecNotificationQuery(ISWbemServices* This,BSTR strQuery,BSTR strQueryLanguage,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemEventSource **objWbemEventSource) {
    return This->lpVtbl->ExecNotificationQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemEventSource);
}
static inline HRESULT ISWbemServices_ExecNotificationQueryAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strQuery,BSTR strQueryLanguage,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecNotificationQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_ExecMethod(ISWbemServices* This,BSTR strObjectPath,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObject **objWbemOutParameters) {
    return This->lpVtbl->ExecMethod(This,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters);
}
static inline HRESULT ISWbemServices_ExecMethodAsync(ISWbemServices* This,IDispatch *objWbemSink,BSTR strObjectPath,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecMethodAsync(This,objWbemSink,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServices_get_Security_(ISWbemServices* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
#endif
#endif

#endif


#endif  /* __ISWbemServices_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemServicesEx interface
 */
#ifndef __ISWbemServicesEx_INTERFACE_DEFINED__
#define __ISWbemServicesEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemServicesEx, 0xd2f68443, 0x85dc, 0x427e, 0x91,0xd8, 0x36,0x65,0x54,0xcc,0x75,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d2f68443-85dc-427e-91d8-366554cc754c")
ISWbemServicesEx : public ISWbemServices
{
    virtual HRESULT STDMETHODCALLTYPE Put(
        ISWbemObjectEx *objWbemObject,
        LONG iFlags = wbemChangeFlagCreateOrUpdate,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectPath **objWbemObjectPath = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutAsync(
        ISWbemSink *objWbemSink,
        ISWbemObjectEx *objWbemObject,
        LONG iFlags = wbemChangeFlagCreateOrUpdate,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemServicesEx, 0xd2f68443, 0x85dc, 0x427e, 0x91,0xd8, 0x36,0x65,0x54,0xcc,0x75,0x4c)
#endif
#else
typedef struct ISWbemServicesExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemServicesEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemServicesEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemServicesEx *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemServicesEx *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemServicesEx *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemServicesEx *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemServicesEx *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemServices methods ***/
    HRESULT (STDMETHODCALLTYPE *Get)(
        ISWbemServicesEx *This,
        BSTR strObjectPath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *GetAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        ISWbemServicesEx *This,
        BSTR strObjectPath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet);

    HRESULT (STDMETHODCALLTYPE *DeleteAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *InstancesOf)(
        ISWbemServicesEx *This,
        BSTR strClass,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *InstancesOfAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strClass,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *SubclassesOf)(
        ISWbemServicesEx *This,
        BSTR strSuperclass,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *SubclassesOfAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strSuperclass,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecQuery)(
        ISWbemServicesEx *This,
        BSTR strQuery,
        BSTR strQueryLanguage,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *ExecQueryAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strQuery,
        BSTR strQueryLanguage,
        LONG lFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *AssociatorsOf)(
        ISWbemServicesEx *This,
        BSTR strObjectPath,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *AssociatorsOfAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ReferencesTo)(
        ISWbemServicesEx *This,
        BSTR strObjectPath,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *ReferencesToAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecNotificationQuery)(
        ISWbemServicesEx *This,
        BSTR strQuery,
        BSTR strQueryLanguage,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemEventSource **objWbemEventSource);

    HRESULT (STDMETHODCALLTYPE *ExecNotificationQueryAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strQuery,
        BSTR strQueryLanguage,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecMethod)(
        ISWbemServicesEx *This,
        BSTR strObjectPath,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObject **objWbemOutParameters);

    HRESULT (STDMETHODCALLTYPE *ExecMethodAsync)(
        ISWbemServicesEx *This,
        IDispatch *objWbemSink,
        BSTR strObjectPath,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemServicesEx *This,
        ISWbemSecurity **objWbemSecurity);

    /*** ISWbemServicesEx methods ***/
    HRESULT (STDMETHODCALLTYPE *Put)(
        ISWbemServicesEx *This,
        ISWbemObjectEx *objWbemObject,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectPath **objWbemObjectPath);

    HRESULT (STDMETHODCALLTYPE *PutAsync)(
        ISWbemServicesEx *This,
        ISWbemSink *objWbemSink,
        ISWbemObjectEx *objWbemObject,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    END_INTERFACE
} ISWbemServicesExVtbl;

interface ISWbemServicesEx {
    CONST_VTBL ISWbemServicesExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemServicesEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemServicesEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemServicesEx_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemServicesEx_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemServicesEx_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemServicesEx_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemServicesEx_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemServices methods ***/
#define ISWbemServicesEx_Get(This,strObjectPath,iFlags,objWbemNamedValueSet,objWbemObject) (This)->lpVtbl->Get(This,strObjectPath,iFlags,objWbemNamedValueSet,objWbemObject)
#define ISWbemServicesEx_GetAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->GetAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_Delete(This,strObjectPath,iFlags,objWbemNamedValueSet) (This)->lpVtbl->Delete(This,strObjectPath,iFlags,objWbemNamedValueSet)
#define ISWbemServicesEx_DeleteAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->DeleteAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_InstancesOf(This,strClass,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->InstancesOf(This,strClass,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServicesEx_InstancesOfAsync(This,objWbemSink,strClass,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->InstancesOfAsync(This,objWbemSink,strClass,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_SubclassesOf(This,strSuperclass,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->SubclassesOf(This,strSuperclass,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServicesEx_SubclassesOfAsync(This,objWbemSink,strSuperclass,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->SubclassesOfAsync(This,objWbemSink,strSuperclass,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_ExecQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->ExecQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServicesEx_ExecQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,lFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,lFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_AssociatorsOf(This,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->AssociatorsOf(This,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServicesEx_AssociatorsOfAsync(This,objWbemSink,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->AssociatorsOfAsync(This,objWbemSink,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_ReferencesTo(This,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->ReferencesTo(This,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemServicesEx_ReferencesToAsync(This,objWbemSink,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ReferencesToAsync(This,objWbemSink,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_ExecNotificationQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemEventSource) (This)->lpVtbl->ExecNotificationQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemEventSource)
#define ISWbemServicesEx_ExecNotificationQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecNotificationQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_ExecMethod(This,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters) (This)->lpVtbl->ExecMethod(This,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters)
#define ISWbemServicesEx_ExecMethodAsync(This,objWbemSink,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecMethodAsync(This,objWbemSink,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemServicesEx_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
/*** ISWbemServicesEx methods ***/
#define ISWbemServicesEx_Put(This,objWbemObject,iFlags,objWbemNamedValueSet,objWbemObjectPath) (This)->lpVtbl->Put(This,objWbemObject,iFlags,objWbemNamedValueSet,objWbemObjectPath)
#define ISWbemServicesEx_PutAsync(This,objWbemSink,objWbemObject,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->PutAsync(This,objWbemSink,objWbemObject,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemServicesEx_QueryInterface(ISWbemServicesEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemServicesEx_AddRef(ISWbemServicesEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemServicesEx_Release(ISWbemServicesEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemServicesEx_GetTypeInfoCount(ISWbemServicesEx* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemServicesEx_GetTypeInfo(ISWbemServicesEx* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemServicesEx_GetIDsOfNames(ISWbemServicesEx* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemServicesEx_Invoke(ISWbemServicesEx* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemServices methods ***/
static inline HRESULT ISWbemServicesEx_Get(ISWbemServicesEx* This,BSTR strObjectPath,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObject **objWbemObject) {
    return This->lpVtbl->Get(This,strObjectPath,iFlags,objWbemNamedValueSet,objWbemObject);
}
static inline HRESULT ISWbemServicesEx_GetAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strObjectPath,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->GetAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_Delete(ISWbemServicesEx* This,BSTR strObjectPath,LONG iFlags,IDispatch *objWbemNamedValueSet) {
    return This->lpVtbl->Delete(This,strObjectPath,iFlags,objWbemNamedValueSet);
}
static inline HRESULT ISWbemServicesEx_DeleteAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strObjectPath,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->DeleteAsync(This,objWbemSink,strObjectPath,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_InstancesOf(ISWbemServicesEx* This,BSTR strClass,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->InstancesOf(This,strClass,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServicesEx_InstancesOfAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strClass,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->InstancesOfAsync(This,objWbemSink,strClass,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_SubclassesOf(ISWbemServicesEx* This,BSTR strSuperclass,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->SubclassesOf(This,strSuperclass,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServicesEx_SubclassesOfAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strSuperclass,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->SubclassesOfAsync(This,objWbemSink,strSuperclass,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_ExecQuery(ISWbemServicesEx* This,BSTR strQuery,BSTR strQueryLanguage,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->ExecQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServicesEx_ExecQueryAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strQuery,BSTR strQueryLanguage,LONG lFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,lFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_AssociatorsOf(ISWbemServicesEx* This,BSTR strObjectPath,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->AssociatorsOf(This,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServicesEx_AssociatorsOfAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strObjectPath,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->AssociatorsOfAsync(This,objWbemSink,strObjectPath,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_ReferencesTo(ISWbemServicesEx* This,BSTR strObjectPath,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->ReferencesTo(This,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemServicesEx_ReferencesToAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strObjectPath,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ReferencesToAsync(This,objWbemSink,strObjectPath,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_ExecNotificationQuery(ISWbemServicesEx* This,BSTR strQuery,BSTR strQueryLanguage,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemEventSource **objWbemEventSource) {
    return This->lpVtbl->ExecNotificationQuery(This,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemEventSource);
}
static inline HRESULT ISWbemServicesEx_ExecNotificationQueryAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strQuery,BSTR strQueryLanguage,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecNotificationQueryAsync(This,objWbemSink,strQuery,strQueryLanguage,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_ExecMethod(ISWbemServicesEx* This,BSTR strObjectPath,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObject **objWbemOutParameters) {
    return This->lpVtbl->ExecMethod(This,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters);
}
static inline HRESULT ISWbemServicesEx_ExecMethodAsync(ISWbemServicesEx* This,IDispatch *objWbemSink,BSTR strObjectPath,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecMethodAsync(This,objWbemSink,strObjectPath,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemServicesEx_get_Security_(ISWbemServicesEx* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
/*** ISWbemServicesEx methods ***/
static inline HRESULT ISWbemServicesEx_Put(ISWbemServicesEx* This,ISWbemObjectEx *objWbemObject,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectPath **objWbemObjectPath) {
    return This->lpVtbl->Put(This,objWbemObject,iFlags,objWbemNamedValueSet,objWbemObjectPath);
}
static inline HRESULT ISWbemServicesEx_PutAsync(ISWbemServicesEx* This,ISWbemSink *objWbemSink,ISWbemObjectEx *objWbemObject,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->PutAsync(This,objWbemSink,objWbemObject,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
#endif
#endif

#endif


#endif  /* __ISWbemServicesEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemObject interface
 */
#ifndef __ISWbemObject_INTERFACE_DEFINED__
#define __ISWbemObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemObject, 0x76a6415a, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("76a6415a-cb41-11d1-8b02-00600806d9b6")
ISWbemObject : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Put_(
        LONG iFlags = wbemChangeFlagCreateOrUpdate,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectPath **objWbemObjectPath = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutAsync_(
        IDispatch *objWbemSink,
        LONG iFlags = wbemChangeFlagCreateOrUpdate,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete_(
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteAsync_(
        IDispatch *objWbemSink,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Instances_(
        LONG iFlags = wbemFlagReturnImmediately,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE InstancesAsync_(
        IDispatch *objWbemSink,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Subclasses_(
        LONG iFlags = wbemFlagReturnImmediately | wbemQueryFlagDeep,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SubclassesAsync_(
        IDispatch *objWbemSink,
        LONG iFlags = wbemQueryFlagDeep,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Associators_(
        BSTR strAssocClass = L"",
        BSTR strResultClass = L"",
        BSTR strResultRole = L"",
        BSTR strRole = L"",
        VARIANT_BOOL bClassesOnly = FALSE,
        VARIANT_BOOL bSchemaOnly = FALSE,
        BSTR strRequiredAssocQualifier = L"",
        BSTR strRequiredQualifier = L"",
        LONG iFlags = wbemFlagReturnImmediately,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE AssociatorsAsync_(
        IDispatch *objWbemSink,
        BSTR strAssocClass = L"",
        BSTR strResultClass = L"",
        BSTR strResultRole = L"",
        BSTR strRole = L"",
        VARIANT_BOOL bClassesOnly = FALSE,
        VARIANT_BOOL bSchemaOnly = FALSE,
        BSTR strRequiredAssocQualifier = L"",
        BSTR strRequiredQualifier = L"",
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE References_(
        BSTR strResultClass = L"",
        BSTR strRole = L"",
        VARIANT_BOOL bClassesOnly = FALSE,
        VARIANT_BOOL bSchemaOnly = FALSE,
        BSTR strRequiredQualifier = L"",
        LONG iFlags = wbemFlagReturnImmediately,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObjectSet **objWbemObjectSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReferencesAsync_(
        IDispatch *objWbemSink,
        BSTR strResultClass = L"",
        BSTR strRole = L"",
        VARIANT_BOOL bClassesOnly = FALSE,
        VARIANT_BOOL bSchemaOnly = FALSE,
        BSTR strRequiredQualifier = L"",
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecMethod_(
        BSTR strMethodName,
        IDispatch *objWbemInParameters = 0,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemObject **objWbemOutParameters = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecMethodAsync_(
        IDispatch *objWbemSink,
        BSTR strMethodName,
        IDispatch *objWbemInParameters = 0,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        IDispatch *objWbemAsyncContext = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone_(
        ISWbemObject **objWbemObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectText_(
        LONG iFlags = 0,
        BSTR *strObjectText = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SpawnDerivedClass_(
        LONG iFlags = 0,
        ISWbemObject **objWbemObject = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SpawnInstance_(
        LONG iFlags = 0,
        ISWbemObject **objWbemObject = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompareTo_(
        IDispatch *objWbemObject,
        LONG iFlags = wbemComparisonFlagIncludeAll,
        VARIANT_BOOL *bResult = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Qualifiers_(
        ISWbemQualifierSet **objWbemQualifierSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Properties_(
        ISWbemPropertySet **objWbemPropertySet) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Methods_(
        ISWbemMethodSet **objWbemMethodSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Derivation_(
        VARIANT *strClassNameArray) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Path_(
        ISWbemObjectPath **objWbemObjectPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Security_(
        ISWbemSecurity **objWbemSecurity) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemObject, 0x76a6415a, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemObject *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemObject *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemObject *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemObject *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemObject *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Put_)(
        ISWbemObject *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectPath **objWbemObjectPath);

    HRESULT (STDMETHODCALLTYPE *PutAsync_)(
        ISWbemObject *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Delete_)(
        ISWbemObject *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet);

    HRESULT (STDMETHODCALLTYPE *DeleteAsync_)(
        ISWbemObject *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Instances_)(
        ISWbemObject *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *InstancesAsync_)(
        ISWbemObject *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Subclasses_)(
        ISWbemObject *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *SubclassesAsync_)(
        ISWbemObject *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Associators_)(
        ISWbemObject *This,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *AssociatorsAsync_)(
        ISWbemObject *This,
        IDispatch *objWbemSink,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *References_)(
        ISWbemObject *This,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *ReferencesAsync_)(
        ISWbemObject *This,
        IDispatch *objWbemSink,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecMethod_)(
        ISWbemObject *This,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObject **objWbemOutParameters);

    HRESULT (STDMETHODCALLTYPE *ExecMethodAsync_)(
        ISWbemObject *This,
        IDispatch *objWbemSink,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Clone_)(
        ISWbemObject *This,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *GetObjectText_)(
        ISWbemObject *This,
        LONG iFlags,
        BSTR *strObjectText);

    HRESULT (STDMETHODCALLTYPE *SpawnDerivedClass_)(
        ISWbemObject *This,
        LONG iFlags,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *SpawnInstance_)(
        ISWbemObject *This,
        LONG iFlags,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *CompareTo_)(
        ISWbemObject *This,
        IDispatch *objWbemObject,
        LONG iFlags,
        VARIANT_BOOL *bResult);

    HRESULT (STDMETHODCALLTYPE *get_Qualifiers_)(
        ISWbemObject *This,
        ISWbemQualifierSet **objWbemQualifierSet);

    HRESULT (STDMETHODCALLTYPE *get_Properties_)(
        ISWbemObject *This,
        ISWbemPropertySet **objWbemPropertySet);

    HRESULT (STDMETHODCALLTYPE *get_Methods_)(
        ISWbemObject *This,
        ISWbemMethodSet **objWbemMethodSet);

    HRESULT (STDMETHODCALLTYPE *get_Derivation_)(
        ISWbemObject *This,
        VARIANT *strClassNameArray);

    HRESULT (STDMETHODCALLTYPE *get_Path_)(
        ISWbemObject *This,
        ISWbemObjectPath **objWbemObjectPath);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemObject *This,
        ISWbemSecurity **objWbemSecurity);

    END_INTERFACE
} ISWbemObjectVtbl;

interface ISWbemObject {
    CONST_VTBL ISWbemObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemObject_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemObject_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemObject_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemObject_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemObject_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemObject methods ***/
#define ISWbemObject_Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath) (This)->lpVtbl->Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath)
#define ISWbemObject_PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObject_Delete_(This,iFlags,objWbemNamedValueSet) (This)->lpVtbl->Delete_(This,iFlags,objWbemNamedValueSet)
#define ISWbemObject_DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObject_Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemObject_InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObject_Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemObject_SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObject_Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemObject_AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObject_References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemObject_ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObject_ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters) (This)->lpVtbl->ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters)
#define ISWbemObject_ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObject_Clone_(This,objWbemObject) (This)->lpVtbl->Clone_(This,objWbemObject)
#define ISWbemObject_GetObjectText_(This,iFlags,strObjectText) (This)->lpVtbl->GetObjectText_(This,iFlags,strObjectText)
#define ISWbemObject_SpawnDerivedClass_(This,iFlags,objWbemObject) (This)->lpVtbl->SpawnDerivedClass_(This,iFlags,objWbemObject)
#define ISWbemObject_SpawnInstance_(This,iFlags,objWbemObject) (This)->lpVtbl->SpawnInstance_(This,iFlags,objWbemObject)
#define ISWbemObject_CompareTo_(This,objWbemObject,iFlags,bResult) (This)->lpVtbl->CompareTo_(This,objWbemObject,iFlags,bResult)
#define ISWbemObject_get_Qualifiers_(This,objWbemQualifierSet) (This)->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet)
#define ISWbemObject_get_Properties_(This,objWbemPropertySet) (This)->lpVtbl->get_Properties_(This,objWbemPropertySet)
#define ISWbemObject_get_Methods_(This,objWbemMethodSet) (This)->lpVtbl->get_Methods_(This,objWbemMethodSet)
#define ISWbemObject_get_Derivation_(This,strClassNameArray) (This)->lpVtbl->get_Derivation_(This,strClassNameArray)
#define ISWbemObject_get_Path_(This,objWbemObjectPath) (This)->lpVtbl->get_Path_(This,objWbemObjectPath)
#define ISWbemObject_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemObject_QueryInterface(ISWbemObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemObject_AddRef(ISWbemObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemObject_Release(ISWbemObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemObject_GetTypeInfoCount(ISWbemObject* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemObject_GetTypeInfo(ISWbemObject* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemObject_GetIDsOfNames(ISWbemObject* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemObject_Invoke(ISWbemObject* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemObject methods ***/
static inline HRESULT ISWbemObject_Put_(ISWbemObject* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectPath **objWbemObjectPath) {
    return This->lpVtbl->Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath);
}
static inline HRESULT ISWbemObject_PutAsync_(ISWbemObject* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObject_Delete_(ISWbemObject* This,LONG iFlags,IDispatch *objWbemNamedValueSet) {
    return This->lpVtbl->Delete_(This,iFlags,objWbemNamedValueSet);
}
static inline HRESULT ISWbemObject_DeleteAsync_(ISWbemObject* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObject_Instances_(ISWbemObject* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemObject_InstancesAsync_(ISWbemObject* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObject_Subclasses_(ISWbemObject* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemObject_SubclassesAsync_(ISWbemObject* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObject_Associators_(ISWbemObject* This,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemObject_AssociatorsAsync_(ISWbemObject* This,IDispatch *objWbemSink,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObject_References_(ISWbemObject* This,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemObject_ReferencesAsync_(ISWbemObject* This,IDispatch *objWbemSink,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObject_ExecMethod_(ISWbemObject* This,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObject **objWbemOutParameters) {
    return This->lpVtbl->ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters);
}
static inline HRESULT ISWbemObject_ExecMethodAsync_(ISWbemObject* This,IDispatch *objWbemSink,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObject_Clone_(ISWbemObject* This,ISWbemObject **objWbemObject) {
    return This->lpVtbl->Clone_(This,objWbemObject);
}
static inline HRESULT ISWbemObject_GetObjectText_(ISWbemObject* This,LONG iFlags,BSTR *strObjectText) {
    return This->lpVtbl->GetObjectText_(This,iFlags,strObjectText);
}
static inline HRESULT ISWbemObject_SpawnDerivedClass_(ISWbemObject* This,LONG iFlags,ISWbemObject **objWbemObject) {
    return This->lpVtbl->SpawnDerivedClass_(This,iFlags,objWbemObject);
}
static inline HRESULT ISWbemObject_SpawnInstance_(ISWbemObject* This,LONG iFlags,ISWbemObject **objWbemObject) {
    return This->lpVtbl->SpawnInstance_(This,iFlags,objWbemObject);
}
static inline HRESULT ISWbemObject_CompareTo_(ISWbemObject* This,IDispatch *objWbemObject,LONG iFlags,VARIANT_BOOL *bResult) {
    return This->lpVtbl->CompareTo_(This,objWbemObject,iFlags,bResult);
}
static inline HRESULT ISWbemObject_get_Qualifiers_(ISWbemObject* This,ISWbemQualifierSet **objWbemQualifierSet) {
    return This->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet);
}
static inline HRESULT ISWbemObject_get_Properties_(ISWbemObject* This,ISWbemPropertySet **objWbemPropertySet) {
    return This->lpVtbl->get_Properties_(This,objWbemPropertySet);
}
static inline HRESULT ISWbemObject_get_Methods_(ISWbemObject* This,ISWbemMethodSet **objWbemMethodSet) {
    return This->lpVtbl->get_Methods_(This,objWbemMethodSet);
}
static inline HRESULT ISWbemObject_get_Derivation_(ISWbemObject* This,VARIANT *strClassNameArray) {
    return This->lpVtbl->get_Derivation_(This,strClassNameArray);
}
static inline HRESULT ISWbemObject_get_Path_(ISWbemObject* This,ISWbemObjectPath **objWbemObjectPath) {
    return This->lpVtbl->get_Path_(This,objWbemObjectPath);
}
static inline HRESULT ISWbemObject_get_Security_(ISWbemObject* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
#endif
#endif

#endif


#endif  /* __ISWbemObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemObjectEx interface
 */
#ifndef __ISWbemObjectEx_INTERFACE_DEFINED__
#define __ISWbemObjectEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemObjectEx, 0x269ad56a, 0x8a67, 0x4129, 0xbc,0x8c, 0x05,0x06,0xdc,0xfe,0x98,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("269ad56a-8a67-4129-bc8c-0506dcfe9880")
ISWbemObjectEx : public ISWbemObject
{
    virtual HRESULT STDMETHODCALLTYPE Refresh_(
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SystemProperties_(
        ISWbemPropertySet **objWbemPropertySet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetText_(
        WbemObjectTextFormatEnum iObjectTextFormat,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        BSTR *bsText = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFromText_(
        BSTR bsText,
        WbemObjectTextFormatEnum iObjectTextFormat,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemObjectEx, 0x269ad56a, 0x8a67, 0x4129, 0xbc,0x8c, 0x05,0x06,0xdc,0xfe,0x98,0x80)
#endif
#else
typedef struct ISWbemObjectExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemObjectEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemObjectEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemObjectEx *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemObjectEx *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemObjectEx *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemObjectEx *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemObjectEx *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Put_)(
        ISWbemObjectEx *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectPath **objWbemObjectPath);

    HRESULT (STDMETHODCALLTYPE *PutAsync_)(
        ISWbemObjectEx *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Delete_)(
        ISWbemObjectEx *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet);

    HRESULT (STDMETHODCALLTYPE *DeleteAsync_)(
        ISWbemObjectEx *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Instances_)(
        ISWbemObjectEx *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *InstancesAsync_)(
        ISWbemObjectEx *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Subclasses_)(
        ISWbemObjectEx *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *SubclassesAsync_)(
        ISWbemObjectEx *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Associators_)(
        ISWbemObjectEx *This,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *AssociatorsAsync_)(
        ISWbemObjectEx *This,
        IDispatch *objWbemSink,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *References_)(
        ISWbemObjectEx *This,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *ReferencesAsync_)(
        ISWbemObjectEx *This,
        IDispatch *objWbemSink,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecMethod_)(
        ISWbemObjectEx *This,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObject **objWbemOutParameters);

    HRESULT (STDMETHODCALLTYPE *ExecMethodAsync_)(
        ISWbemObjectEx *This,
        IDispatch *objWbemSink,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Clone_)(
        ISWbemObjectEx *This,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *GetObjectText_)(
        ISWbemObjectEx *This,
        LONG iFlags,
        BSTR *strObjectText);

    HRESULT (STDMETHODCALLTYPE *SpawnDerivedClass_)(
        ISWbemObjectEx *This,
        LONG iFlags,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *SpawnInstance_)(
        ISWbemObjectEx *This,
        LONG iFlags,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *CompareTo_)(
        ISWbemObjectEx *This,
        IDispatch *objWbemObject,
        LONG iFlags,
        VARIANT_BOOL *bResult);

    HRESULT (STDMETHODCALLTYPE *get_Qualifiers_)(
        ISWbemObjectEx *This,
        ISWbemQualifierSet **objWbemQualifierSet);

    HRESULT (STDMETHODCALLTYPE *get_Properties_)(
        ISWbemObjectEx *This,
        ISWbemPropertySet **objWbemPropertySet);

    HRESULT (STDMETHODCALLTYPE *get_Methods_)(
        ISWbemObjectEx *This,
        ISWbemMethodSet **objWbemMethodSet);

    HRESULT (STDMETHODCALLTYPE *get_Derivation_)(
        ISWbemObjectEx *This,
        VARIANT *strClassNameArray);

    HRESULT (STDMETHODCALLTYPE *get_Path_)(
        ISWbemObjectEx *This,
        ISWbemObjectPath **objWbemObjectPath);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemObjectEx *This,
        ISWbemSecurity **objWbemSecurity);

    /*** ISWbemObjectEx methods ***/
    HRESULT (STDMETHODCALLTYPE *Refresh_)(
        ISWbemObjectEx *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet);

    HRESULT (STDMETHODCALLTYPE *get_SystemProperties_)(
        ISWbemObjectEx *This,
        ISWbemPropertySet **objWbemPropertySet);

    HRESULT (STDMETHODCALLTYPE *GetText_)(
        ISWbemObjectEx *This,
        WbemObjectTextFormatEnum iObjectTextFormat,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        BSTR *bsText);

    HRESULT (STDMETHODCALLTYPE *SetFromText_)(
        ISWbemObjectEx *This,
        BSTR bsText,
        WbemObjectTextFormatEnum iObjectTextFormat,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet);

    END_INTERFACE
} ISWbemObjectExVtbl;

interface ISWbemObjectEx {
    CONST_VTBL ISWbemObjectExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemObjectEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemObjectEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemObjectEx_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemObjectEx_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemObjectEx_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemObjectEx_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemObjectEx_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemObject methods ***/
#define ISWbemObjectEx_Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath) (This)->lpVtbl->Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath)
#define ISWbemObjectEx_PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObjectEx_Delete_(This,iFlags,objWbemNamedValueSet) (This)->lpVtbl->Delete_(This,iFlags,objWbemNamedValueSet)
#define ISWbemObjectEx_DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObjectEx_Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemObjectEx_InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObjectEx_Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemObjectEx_SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObjectEx_Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemObjectEx_AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObjectEx_References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemObjectEx_ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObjectEx_ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters) (This)->lpVtbl->ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters)
#define ISWbemObjectEx_ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemObjectEx_Clone_(This,objWbemObject) (This)->lpVtbl->Clone_(This,objWbemObject)
#define ISWbemObjectEx_GetObjectText_(This,iFlags,strObjectText) (This)->lpVtbl->GetObjectText_(This,iFlags,strObjectText)
#define ISWbemObjectEx_SpawnDerivedClass_(This,iFlags,objWbemObject) (This)->lpVtbl->SpawnDerivedClass_(This,iFlags,objWbemObject)
#define ISWbemObjectEx_SpawnInstance_(This,iFlags,objWbemObject) (This)->lpVtbl->SpawnInstance_(This,iFlags,objWbemObject)
#define ISWbemObjectEx_CompareTo_(This,objWbemObject,iFlags,bResult) (This)->lpVtbl->CompareTo_(This,objWbemObject,iFlags,bResult)
#define ISWbemObjectEx_get_Qualifiers_(This,objWbemQualifierSet) (This)->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet)
#define ISWbemObjectEx_get_Properties_(This,objWbemPropertySet) (This)->lpVtbl->get_Properties_(This,objWbemPropertySet)
#define ISWbemObjectEx_get_Methods_(This,objWbemMethodSet) (This)->lpVtbl->get_Methods_(This,objWbemMethodSet)
#define ISWbemObjectEx_get_Derivation_(This,strClassNameArray) (This)->lpVtbl->get_Derivation_(This,strClassNameArray)
#define ISWbemObjectEx_get_Path_(This,objWbemObjectPath) (This)->lpVtbl->get_Path_(This,objWbemObjectPath)
#define ISWbemObjectEx_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
/*** ISWbemObjectEx methods ***/
#define ISWbemObjectEx_Refresh_(This,iFlags,objWbemNamedValueSet) (This)->lpVtbl->Refresh_(This,iFlags,objWbemNamedValueSet)
#define ISWbemObjectEx_get_SystemProperties_(This,objWbemPropertySet) (This)->lpVtbl->get_SystemProperties_(This,objWbemPropertySet)
#define ISWbemObjectEx_GetText_(This,iObjectTextFormat,iFlags,objWbemNamedValueSet,bsText) (This)->lpVtbl->GetText_(This,iObjectTextFormat,iFlags,objWbemNamedValueSet,bsText)
#define ISWbemObjectEx_SetFromText_(This,bsText,iObjectTextFormat,iFlags,objWbemNamedValueSet) (This)->lpVtbl->SetFromText_(This,bsText,iObjectTextFormat,iFlags,objWbemNamedValueSet)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemObjectEx_QueryInterface(ISWbemObjectEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemObjectEx_AddRef(ISWbemObjectEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemObjectEx_Release(ISWbemObjectEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemObjectEx_GetTypeInfoCount(ISWbemObjectEx* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemObjectEx_GetTypeInfo(ISWbemObjectEx* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemObjectEx_GetIDsOfNames(ISWbemObjectEx* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemObjectEx_Invoke(ISWbemObjectEx* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemObject methods ***/
static inline HRESULT ISWbemObjectEx_Put_(ISWbemObjectEx* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectPath **objWbemObjectPath) {
    return This->lpVtbl->Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath);
}
static inline HRESULT ISWbemObjectEx_PutAsync_(ISWbemObjectEx* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObjectEx_Delete_(ISWbemObjectEx* This,LONG iFlags,IDispatch *objWbemNamedValueSet) {
    return This->lpVtbl->Delete_(This,iFlags,objWbemNamedValueSet);
}
static inline HRESULT ISWbemObjectEx_DeleteAsync_(ISWbemObjectEx* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObjectEx_Instances_(ISWbemObjectEx* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemObjectEx_InstancesAsync_(ISWbemObjectEx* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObjectEx_Subclasses_(ISWbemObjectEx* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemObjectEx_SubclassesAsync_(ISWbemObjectEx* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObjectEx_Associators_(ISWbemObjectEx* This,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemObjectEx_AssociatorsAsync_(ISWbemObjectEx* This,IDispatch *objWbemSink,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObjectEx_References_(ISWbemObjectEx* This,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemObjectEx_ReferencesAsync_(ISWbemObjectEx* This,IDispatch *objWbemSink,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObjectEx_ExecMethod_(ISWbemObjectEx* This,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObject **objWbemOutParameters) {
    return This->lpVtbl->ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters);
}
static inline HRESULT ISWbemObjectEx_ExecMethodAsync_(ISWbemObjectEx* This,IDispatch *objWbemSink,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemObjectEx_Clone_(ISWbemObjectEx* This,ISWbemObject **objWbemObject) {
    return This->lpVtbl->Clone_(This,objWbemObject);
}
static inline HRESULT ISWbemObjectEx_GetObjectText_(ISWbemObjectEx* This,LONG iFlags,BSTR *strObjectText) {
    return This->lpVtbl->GetObjectText_(This,iFlags,strObjectText);
}
static inline HRESULT ISWbemObjectEx_SpawnDerivedClass_(ISWbemObjectEx* This,LONG iFlags,ISWbemObject **objWbemObject) {
    return This->lpVtbl->SpawnDerivedClass_(This,iFlags,objWbemObject);
}
static inline HRESULT ISWbemObjectEx_SpawnInstance_(ISWbemObjectEx* This,LONG iFlags,ISWbemObject **objWbemObject) {
    return This->lpVtbl->SpawnInstance_(This,iFlags,objWbemObject);
}
static inline HRESULT ISWbemObjectEx_CompareTo_(ISWbemObjectEx* This,IDispatch *objWbemObject,LONG iFlags,VARIANT_BOOL *bResult) {
    return This->lpVtbl->CompareTo_(This,objWbemObject,iFlags,bResult);
}
static inline HRESULT ISWbemObjectEx_get_Qualifiers_(ISWbemObjectEx* This,ISWbemQualifierSet **objWbemQualifierSet) {
    return This->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet);
}
static inline HRESULT ISWbemObjectEx_get_Properties_(ISWbemObjectEx* This,ISWbemPropertySet **objWbemPropertySet) {
    return This->lpVtbl->get_Properties_(This,objWbemPropertySet);
}
static inline HRESULT ISWbemObjectEx_get_Methods_(ISWbemObjectEx* This,ISWbemMethodSet **objWbemMethodSet) {
    return This->lpVtbl->get_Methods_(This,objWbemMethodSet);
}
static inline HRESULT ISWbemObjectEx_get_Derivation_(ISWbemObjectEx* This,VARIANT *strClassNameArray) {
    return This->lpVtbl->get_Derivation_(This,strClassNameArray);
}
static inline HRESULT ISWbemObjectEx_get_Path_(ISWbemObjectEx* This,ISWbemObjectPath **objWbemObjectPath) {
    return This->lpVtbl->get_Path_(This,objWbemObjectPath);
}
static inline HRESULT ISWbemObjectEx_get_Security_(ISWbemObjectEx* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
/*** ISWbemObjectEx methods ***/
static inline HRESULT ISWbemObjectEx_Refresh_(ISWbemObjectEx* This,LONG iFlags,IDispatch *objWbemNamedValueSet) {
    return This->lpVtbl->Refresh_(This,iFlags,objWbemNamedValueSet);
}
static inline HRESULT ISWbemObjectEx_get_SystemProperties_(ISWbemObjectEx* This,ISWbemPropertySet **objWbemPropertySet) {
    return This->lpVtbl->get_SystemProperties_(This,objWbemPropertySet);
}
static inline HRESULT ISWbemObjectEx_GetText_(ISWbemObjectEx* This,WbemObjectTextFormatEnum iObjectTextFormat,LONG iFlags,IDispatch *objWbemNamedValueSet,BSTR *bsText) {
    return This->lpVtbl->GetText_(This,iObjectTextFormat,iFlags,objWbemNamedValueSet,bsText);
}
static inline HRESULT ISWbemObjectEx_SetFromText_(ISWbemObjectEx* This,BSTR bsText,WbemObjectTextFormatEnum iObjectTextFormat,LONG iFlags,IDispatch *objWbemNamedValueSet) {
    return This->lpVtbl->SetFromText_(This,bsText,iObjectTextFormat,iFlags,objWbemNamedValueSet);
}
#endif
#endif

#endif


#endif  /* __ISWbemObjectEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemLastError interface
 */
#ifndef __ISWbemLastError_INTERFACE_DEFINED__
#define __ISWbemLastError_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemLastError, 0xd962db84, 0xd4bb, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d962db84-d4bb-11d1-8b09-00600806d9b6")
ISWbemLastError : public ISWbemObject
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemLastError, 0xd962db84, 0xd4bb, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemLastErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemLastError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemLastError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemLastError *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemLastError *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemLastError *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemLastError *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemLastError *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Put_)(
        ISWbemLastError *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectPath **objWbemObjectPath);

    HRESULT (STDMETHODCALLTYPE *PutAsync_)(
        ISWbemLastError *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Delete_)(
        ISWbemLastError *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet);

    HRESULT (STDMETHODCALLTYPE *DeleteAsync_)(
        ISWbemLastError *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Instances_)(
        ISWbemLastError *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *InstancesAsync_)(
        ISWbemLastError *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Subclasses_)(
        ISWbemLastError *This,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *SubclassesAsync_)(
        ISWbemLastError *This,
        IDispatch *objWbemSink,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Associators_)(
        ISWbemLastError *This,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *AssociatorsAsync_)(
        ISWbemLastError *This,
        IDispatch *objWbemSink,
        BSTR strAssocClass,
        BSTR strResultClass,
        BSTR strResultRole,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredAssocQualifier,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *References_)(
        ISWbemLastError *This,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *ReferencesAsync_)(
        ISWbemLastError *This,
        IDispatch *objWbemSink,
        BSTR strResultClass,
        BSTR strRole,
        VARIANT_BOOL bClassesOnly,
        VARIANT_BOOL bSchemaOnly,
        BSTR strRequiredQualifier,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *ExecMethod_)(
        ISWbemLastError *This,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemObject **objWbemOutParameters);

    HRESULT (STDMETHODCALLTYPE *ExecMethodAsync_)(
        ISWbemLastError *This,
        IDispatch *objWbemSink,
        BSTR strMethodName,
        IDispatch *objWbemInParameters,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        IDispatch *objWbemAsyncContext);

    HRESULT (STDMETHODCALLTYPE *Clone_)(
        ISWbemLastError *This,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *GetObjectText_)(
        ISWbemLastError *This,
        LONG iFlags,
        BSTR *strObjectText);

    HRESULT (STDMETHODCALLTYPE *SpawnDerivedClass_)(
        ISWbemLastError *This,
        LONG iFlags,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *SpawnInstance_)(
        ISWbemLastError *This,
        LONG iFlags,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *CompareTo_)(
        ISWbemLastError *This,
        IDispatch *objWbemObject,
        LONG iFlags,
        VARIANT_BOOL *bResult);

    HRESULT (STDMETHODCALLTYPE *get_Qualifiers_)(
        ISWbemLastError *This,
        ISWbemQualifierSet **objWbemQualifierSet);

    HRESULT (STDMETHODCALLTYPE *get_Properties_)(
        ISWbemLastError *This,
        ISWbemPropertySet **objWbemPropertySet);

    HRESULT (STDMETHODCALLTYPE *get_Methods_)(
        ISWbemLastError *This,
        ISWbemMethodSet **objWbemMethodSet);

    HRESULT (STDMETHODCALLTYPE *get_Derivation_)(
        ISWbemLastError *This,
        VARIANT *strClassNameArray);

    HRESULT (STDMETHODCALLTYPE *get_Path_)(
        ISWbemLastError *This,
        ISWbemObjectPath **objWbemObjectPath);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemLastError *This,
        ISWbemSecurity **objWbemSecurity);

    END_INTERFACE
} ISWbemLastErrorVtbl;

interface ISWbemLastError {
    CONST_VTBL ISWbemLastErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemLastError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemLastError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemLastError_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemLastError_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemLastError_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemLastError_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemLastError_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemObject methods ***/
#define ISWbemLastError_Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath) (This)->lpVtbl->Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath)
#define ISWbemLastError_PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemLastError_Delete_(This,iFlags,objWbemNamedValueSet) (This)->lpVtbl->Delete_(This,iFlags,objWbemNamedValueSet)
#define ISWbemLastError_DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemLastError_Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemLastError_InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemLastError_Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemLastError_SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemLastError_Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemLastError_AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemLastError_References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet) (This)->lpVtbl->References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet)
#define ISWbemLastError_ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemLastError_ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters) (This)->lpVtbl->ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters)
#define ISWbemLastError_ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext) (This)->lpVtbl->ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext)
#define ISWbemLastError_Clone_(This,objWbemObject) (This)->lpVtbl->Clone_(This,objWbemObject)
#define ISWbemLastError_GetObjectText_(This,iFlags,strObjectText) (This)->lpVtbl->GetObjectText_(This,iFlags,strObjectText)
#define ISWbemLastError_SpawnDerivedClass_(This,iFlags,objWbemObject) (This)->lpVtbl->SpawnDerivedClass_(This,iFlags,objWbemObject)
#define ISWbemLastError_SpawnInstance_(This,iFlags,objWbemObject) (This)->lpVtbl->SpawnInstance_(This,iFlags,objWbemObject)
#define ISWbemLastError_CompareTo_(This,objWbemObject,iFlags,bResult) (This)->lpVtbl->CompareTo_(This,objWbemObject,iFlags,bResult)
#define ISWbemLastError_get_Qualifiers_(This,objWbemQualifierSet) (This)->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet)
#define ISWbemLastError_get_Properties_(This,objWbemPropertySet) (This)->lpVtbl->get_Properties_(This,objWbemPropertySet)
#define ISWbemLastError_get_Methods_(This,objWbemMethodSet) (This)->lpVtbl->get_Methods_(This,objWbemMethodSet)
#define ISWbemLastError_get_Derivation_(This,strClassNameArray) (This)->lpVtbl->get_Derivation_(This,strClassNameArray)
#define ISWbemLastError_get_Path_(This,objWbemObjectPath) (This)->lpVtbl->get_Path_(This,objWbemObjectPath)
#define ISWbemLastError_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemLastError_QueryInterface(ISWbemLastError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemLastError_AddRef(ISWbemLastError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemLastError_Release(ISWbemLastError* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemLastError_GetTypeInfoCount(ISWbemLastError* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemLastError_GetTypeInfo(ISWbemLastError* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemLastError_GetIDsOfNames(ISWbemLastError* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemLastError_Invoke(ISWbemLastError* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemObject methods ***/
static inline HRESULT ISWbemLastError_Put_(ISWbemLastError* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectPath **objWbemObjectPath) {
    return This->lpVtbl->Put_(This,iFlags,objWbemNamedValueSet,objWbemObjectPath);
}
static inline HRESULT ISWbemLastError_PutAsync_(ISWbemLastError* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->PutAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemLastError_Delete_(ISWbemLastError* This,LONG iFlags,IDispatch *objWbemNamedValueSet) {
    return This->lpVtbl->Delete_(This,iFlags,objWbemNamedValueSet);
}
static inline HRESULT ISWbemLastError_DeleteAsync_(ISWbemLastError* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->DeleteAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemLastError_Instances_(ISWbemLastError* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Instances_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemLastError_InstancesAsync_(ISWbemLastError* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->InstancesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemLastError_Subclasses_(ISWbemLastError* This,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Subclasses_(This,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemLastError_SubclassesAsync_(ISWbemLastError* This,IDispatch *objWbemSink,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->SubclassesAsync_(This,objWbemSink,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemLastError_Associators_(ISWbemLastError* This,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->Associators_(This,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemLastError_AssociatorsAsync_(ISWbemLastError* This,IDispatch *objWbemSink,BSTR strAssocClass,BSTR strResultClass,BSTR strResultRole,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredAssocQualifier,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->AssociatorsAsync_(This,objWbemSink,strAssocClass,strResultClass,strResultRole,strRole,bClassesOnly,bSchemaOnly,strRequiredAssocQualifier,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemLastError_References_(ISWbemLastError* This,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->References_(This,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemObjectSet);
}
static inline HRESULT ISWbemLastError_ReferencesAsync_(ISWbemLastError* This,IDispatch *objWbemSink,BSTR strResultClass,BSTR strRole,VARIANT_BOOL bClassesOnly,VARIANT_BOOL bSchemaOnly,BSTR strRequiredQualifier,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ReferencesAsync_(This,objWbemSink,strResultClass,strRole,bClassesOnly,bSchemaOnly,strRequiredQualifier,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemLastError_ExecMethod_(ISWbemLastError* This,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemObject **objWbemOutParameters) {
    return This->lpVtbl->ExecMethod_(This,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemOutParameters);
}
static inline HRESULT ISWbemLastError_ExecMethodAsync_(ISWbemLastError* This,IDispatch *objWbemSink,BSTR strMethodName,IDispatch *objWbemInParameters,LONG iFlags,IDispatch *objWbemNamedValueSet,IDispatch *objWbemAsyncContext) {
    return This->lpVtbl->ExecMethodAsync_(This,objWbemSink,strMethodName,objWbemInParameters,iFlags,objWbemNamedValueSet,objWbemAsyncContext);
}
static inline HRESULT ISWbemLastError_Clone_(ISWbemLastError* This,ISWbemObject **objWbemObject) {
    return This->lpVtbl->Clone_(This,objWbemObject);
}
static inline HRESULT ISWbemLastError_GetObjectText_(ISWbemLastError* This,LONG iFlags,BSTR *strObjectText) {
    return This->lpVtbl->GetObjectText_(This,iFlags,strObjectText);
}
static inline HRESULT ISWbemLastError_SpawnDerivedClass_(ISWbemLastError* This,LONG iFlags,ISWbemObject **objWbemObject) {
    return This->lpVtbl->SpawnDerivedClass_(This,iFlags,objWbemObject);
}
static inline HRESULT ISWbemLastError_SpawnInstance_(ISWbemLastError* This,LONG iFlags,ISWbemObject **objWbemObject) {
    return This->lpVtbl->SpawnInstance_(This,iFlags,objWbemObject);
}
static inline HRESULT ISWbemLastError_CompareTo_(ISWbemLastError* This,IDispatch *objWbemObject,LONG iFlags,VARIANT_BOOL *bResult) {
    return This->lpVtbl->CompareTo_(This,objWbemObject,iFlags,bResult);
}
static inline HRESULT ISWbemLastError_get_Qualifiers_(ISWbemLastError* This,ISWbemQualifierSet **objWbemQualifierSet) {
    return This->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet);
}
static inline HRESULT ISWbemLastError_get_Properties_(ISWbemLastError* This,ISWbemPropertySet **objWbemPropertySet) {
    return This->lpVtbl->get_Properties_(This,objWbemPropertySet);
}
static inline HRESULT ISWbemLastError_get_Methods_(ISWbemLastError* This,ISWbemMethodSet **objWbemMethodSet) {
    return This->lpVtbl->get_Methods_(This,objWbemMethodSet);
}
static inline HRESULT ISWbemLastError_get_Derivation_(ISWbemLastError* This,VARIANT *strClassNameArray) {
    return This->lpVtbl->get_Derivation_(This,strClassNameArray);
}
static inline HRESULT ISWbemLastError_get_Path_(ISWbemLastError* This,ISWbemObjectPath **objWbemObjectPath) {
    return This->lpVtbl->get_Path_(This,objWbemObjectPath);
}
static inline HRESULT ISWbemLastError_get_Security_(ISWbemLastError* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
#endif
#endif

#endif


#endif  /* __ISWbemLastError_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemObjectSet interface
 */
#ifndef __ISWbemObjectSet_INTERFACE_DEFINED__
#define __ISWbemObjectSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemObjectSet, 0x76a6415f, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("76a6415f-cb41-11d1-8b02-00600806d9b6")
ISWbemObjectSet : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        BSTR strObjectPath,
        LONG iFlags = 0,
        ISWbemObject **objWbemObject = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *iCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Security_(
        ISWbemSecurity **objWbemSecurity) = 0;

    virtual HRESULT STDMETHODCALLTYPE ItemIndex(
        LONG lIndex,
        ISWbemObject **objWbemObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemObjectSet, 0x76a6415f, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemObjectSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemObjectSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemObjectSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemObjectSet *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemObjectSet *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemObjectSet *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemObjectSet *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemObjectSet *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemObjectSet methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ISWbemObjectSet *This,
        IUnknown **pUnk);

    HRESULT (STDMETHODCALLTYPE *Item)(
        ISWbemObjectSet *This,
        BSTR strObjectPath,
        LONG iFlags,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ISWbemObjectSet *This,
        LONG *iCount);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemObjectSet *This,
        ISWbemSecurity **objWbemSecurity);

    HRESULT (STDMETHODCALLTYPE *ItemIndex)(
        ISWbemObjectSet *This,
        LONG lIndex,
        ISWbemObject **objWbemObject);

    END_INTERFACE
} ISWbemObjectSetVtbl;

interface ISWbemObjectSet {
    CONST_VTBL ISWbemObjectSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemObjectSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemObjectSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemObjectSet_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemObjectSet_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemObjectSet_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemObjectSet_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemObjectSet_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemObjectSet methods ***/
#define ISWbemObjectSet_get__NewEnum(This,pUnk) (This)->lpVtbl->get__NewEnum(This,pUnk)
#define ISWbemObjectSet_Item(This,strObjectPath,iFlags,objWbemObject) (This)->lpVtbl->Item(This,strObjectPath,iFlags,objWbemObject)
#define ISWbemObjectSet_get_Count(This,iCount) (This)->lpVtbl->get_Count(This,iCount)
#define ISWbemObjectSet_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
#define ISWbemObjectSet_ItemIndex(This,lIndex,objWbemObject) (This)->lpVtbl->ItemIndex(This,lIndex,objWbemObject)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemObjectSet_QueryInterface(ISWbemObjectSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemObjectSet_AddRef(ISWbemObjectSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemObjectSet_Release(ISWbemObjectSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemObjectSet_GetTypeInfoCount(ISWbemObjectSet* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemObjectSet_GetTypeInfo(ISWbemObjectSet* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemObjectSet_GetIDsOfNames(ISWbemObjectSet* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemObjectSet_Invoke(ISWbemObjectSet* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemObjectSet methods ***/
static inline HRESULT ISWbemObjectSet_get__NewEnum(ISWbemObjectSet* This,IUnknown **pUnk) {
    return This->lpVtbl->get__NewEnum(This,pUnk);
}
static inline HRESULT ISWbemObjectSet_Item(ISWbemObjectSet* This,BSTR strObjectPath,LONG iFlags,ISWbemObject **objWbemObject) {
    return This->lpVtbl->Item(This,strObjectPath,iFlags,objWbemObject);
}
static inline HRESULT ISWbemObjectSet_get_Count(ISWbemObjectSet* This,LONG *iCount) {
    return This->lpVtbl->get_Count(This,iCount);
}
static inline HRESULT ISWbemObjectSet_get_Security_(ISWbemObjectSet* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
static inline HRESULT ISWbemObjectSet_ItemIndex(ISWbemObjectSet* This,LONG lIndex,ISWbemObject **objWbemObject) {
    return This->lpVtbl->ItemIndex(This,lIndex,objWbemObject);
}
#endif
#endif

#endif


#endif  /* __ISWbemObjectSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemNamedValueSet interface
 */
#ifndef __ISWbemNamedValueSet_INTERFACE_DEFINED__
#define __ISWbemNamedValueSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemNamedValueSet, 0xcf2376ea, 0xce8c, 0x11d1, 0x8b,0x05, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cf2376ea-ce8c-11d1-8b05-00600806d9b6")
ISWbemNamedValueSet : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        BSTR strName,
        LONG iFlags = 0,
        ISWbemNamedValue **objWbemNamedValue = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *iCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        BSTR strName,
        VARIANT *varValue,
        LONG iFlags = 0,
        ISWbemNamedValue **objWbemNamedValue = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        BSTR strName,
        LONG iFlags = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        ISWbemNamedValueSet **objWbemNamedValueSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteAll(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemNamedValueSet, 0xcf2376ea, 0xce8c, 0x11d1, 0x8b,0x05, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemNamedValueSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemNamedValueSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemNamedValueSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemNamedValueSet *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemNamedValueSet *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemNamedValueSet *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemNamedValueSet *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemNamedValueSet *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemNamedValueSet methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ISWbemNamedValueSet *This,
        IUnknown **pUnk);

    HRESULT (STDMETHODCALLTYPE *Item)(
        ISWbemNamedValueSet *This,
        BSTR strName,
        LONG iFlags,
        ISWbemNamedValue **objWbemNamedValue);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ISWbemNamedValueSet *This,
        LONG *iCount);

    HRESULT (STDMETHODCALLTYPE *Add)(
        ISWbemNamedValueSet *This,
        BSTR strName,
        VARIANT *varValue,
        LONG iFlags,
        ISWbemNamedValue **objWbemNamedValue);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ISWbemNamedValueSet *This,
        BSTR strName,
        LONG iFlags);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        ISWbemNamedValueSet *This,
        ISWbemNamedValueSet **objWbemNamedValueSet);

    HRESULT (STDMETHODCALLTYPE *DeleteAll)(
        ISWbemNamedValueSet *This);

    END_INTERFACE
} ISWbemNamedValueSetVtbl;

interface ISWbemNamedValueSet {
    CONST_VTBL ISWbemNamedValueSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemNamedValueSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemNamedValueSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemNamedValueSet_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemNamedValueSet_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemNamedValueSet_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemNamedValueSet_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemNamedValueSet_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemNamedValueSet methods ***/
#define ISWbemNamedValueSet_get__NewEnum(This,pUnk) (This)->lpVtbl->get__NewEnum(This,pUnk)
#define ISWbemNamedValueSet_Item(This,strName,iFlags,objWbemNamedValue) (This)->lpVtbl->Item(This,strName,iFlags,objWbemNamedValue)
#define ISWbemNamedValueSet_get_Count(This,iCount) (This)->lpVtbl->get_Count(This,iCount)
#define ISWbemNamedValueSet_Add(This,strName,varValue,iFlags,objWbemNamedValue) (This)->lpVtbl->Add(This,strName,varValue,iFlags,objWbemNamedValue)
#define ISWbemNamedValueSet_Remove(This,strName,iFlags) (This)->lpVtbl->Remove(This,strName,iFlags)
#define ISWbemNamedValueSet_Clone(This,objWbemNamedValueSet) (This)->lpVtbl->Clone(This,objWbemNamedValueSet)
#define ISWbemNamedValueSet_DeleteAll(This) (This)->lpVtbl->DeleteAll(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemNamedValueSet_QueryInterface(ISWbemNamedValueSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemNamedValueSet_AddRef(ISWbemNamedValueSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemNamedValueSet_Release(ISWbemNamedValueSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemNamedValueSet_GetTypeInfoCount(ISWbemNamedValueSet* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemNamedValueSet_GetTypeInfo(ISWbemNamedValueSet* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemNamedValueSet_GetIDsOfNames(ISWbemNamedValueSet* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemNamedValueSet_Invoke(ISWbemNamedValueSet* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemNamedValueSet methods ***/
static inline HRESULT ISWbemNamedValueSet_get__NewEnum(ISWbemNamedValueSet* This,IUnknown **pUnk) {
    return This->lpVtbl->get__NewEnum(This,pUnk);
}
static inline HRESULT ISWbemNamedValueSet_Item(ISWbemNamedValueSet* This,BSTR strName,LONG iFlags,ISWbemNamedValue **objWbemNamedValue) {
    return This->lpVtbl->Item(This,strName,iFlags,objWbemNamedValue);
}
static inline HRESULT ISWbemNamedValueSet_get_Count(ISWbemNamedValueSet* This,LONG *iCount) {
    return This->lpVtbl->get_Count(This,iCount);
}
static inline HRESULT ISWbemNamedValueSet_Add(ISWbemNamedValueSet* This,BSTR strName,VARIANT *varValue,LONG iFlags,ISWbemNamedValue **objWbemNamedValue) {
    return This->lpVtbl->Add(This,strName,varValue,iFlags,objWbemNamedValue);
}
static inline HRESULT ISWbemNamedValueSet_Remove(ISWbemNamedValueSet* This,BSTR strName,LONG iFlags) {
    return This->lpVtbl->Remove(This,strName,iFlags);
}
static inline HRESULT ISWbemNamedValueSet_Clone(ISWbemNamedValueSet* This,ISWbemNamedValueSet **objWbemNamedValueSet) {
    return This->lpVtbl->Clone(This,objWbemNamedValueSet);
}
static inline HRESULT ISWbemNamedValueSet_DeleteAll(ISWbemNamedValueSet* This) {
    return This->lpVtbl->DeleteAll(This);
}
#endif
#endif

#endif


#endif  /* __ISWbemNamedValueSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemNamedValue interface
 */
#ifndef __ISWbemNamedValue_INTERFACE_DEFINED__
#define __ISWbemNamedValue_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemNamedValue, 0x76a64164, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("76a64164-cb41-11d1-8b02-00600806d9b6")
ISWbemNamedValue : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Value(
        VARIANT *varValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Value(
        VARIANT *varValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *strName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemNamedValue, 0x76a64164, 0xcb41, 0x11d1, 0x8b,0x02, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemNamedValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemNamedValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemNamedValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemNamedValue *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemNamedValue *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemNamedValue *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemNamedValue *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemNamedValue *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemNamedValue methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        ISWbemNamedValue *This,
        VARIANT *varValue);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        ISWbemNamedValue *This,
        VARIANT *varValue);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ISWbemNamedValue *This,
        BSTR *strName);

    END_INTERFACE
} ISWbemNamedValueVtbl;

interface ISWbemNamedValue {
    CONST_VTBL ISWbemNamedValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemNamedValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemNamedValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemNamedValue_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemNamedValue_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemNamedValue_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemNamedValue_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemNamedValue_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemNamedValue methods ***/
#define ISWbemNamedValue_get_Value(This,varValue) (This)->lpVtbl->get_Value(This,varValue)
#define ISWbemNamedValue_put_Value(This,varValue) (This)->lpVtbl->put_Value(This,varValue)
#define ISWbemNamedValue_get_Name(This,strName) (This)->lpVtbl->get_Name(This,strName)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemNamedValue_QueryInterface(ISWbemNamedValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemNamedValue_AddRef(ISWbemNamedValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemNamedValue_Release(ISWbemNamedValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemNamedValue_GetTypeInfoCount(ISWbemNamedValue* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemNamedValue_GetTypeInfo(ISWbemNamedValue* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemNamedValue_GetIDsOfNames(ISWbemNamedValue* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemNamedValue_Invoke(ISWbemNamedValue* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemNamedValue methods ***/
static inline HRESULT ISWbemNamedValue_get_Value(ISWbemNamedValue* This,VARIANT *varValue) {
    return This->lpVtbl->get_Value(This,varValue);
}
static inline HRESULT ISWbemNamedValue_put_Value(ISWbemNamedValue* This,VARIANT *varValue) {
    return This->lpVtbl->put_Value(This,varValue);
}
static inline HRESULT ISWbemNamedValue_get_Name(ISWbemNamedValue* This,BSTR *strName) {
    return This->lpVtbl->get_Name(This,strName);
}
#endif
#endif

#endif


#endif  /* __ISWbemNamedValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemObjectPath interface
 */
#ifndef __ISWbemObjectPath_INTERFACE_DEFINED__
#define __ISWbemObjectPath_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemObjectPath, 0x5791bc27, 0xce9c, 0x11d1, 0x97,0xbf, 0x00,0x00,0xf8,0x1e,0x84,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5791bc27-ce9c-11d1-97bf-0000f81e849c")
ISWbemObjectPath : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *strPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Path(
        BSTR strPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RelPath(
        BSTR *strRelPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RelPath(
        BSTR strRelPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Server(
        BSTR *strServer) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Server(
        BSTR strServer) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Namespace(
        BSTR *strNamespace) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Namespace(
        BSTR strNamespace) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ParentNamespace(
        BSTR *strParentNamespace) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
        BSTR *strDisplayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DisplayName(
        BSTR strDisplayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Class(
        BSTR *strClass) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Class(
        BSTR strClass) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsClass(
        VARIANT_BOOL *bIsClass) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAsClass(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsSingleton(
        VARIANT_BOOL *bIsSingleton) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAsSingleton(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Keys(
        ISWbemNamedValueSet **objWbemNamedValueSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Security_(
        ISWbemSecurity **objWbemSecurity) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Locale(
        BSTR *strLocale) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Locale(
        BSTR strLocale) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Authority(
        BSTR *strAuthority) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Authority(
        BSTR strAuthority) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemObjectPath, 0x5791bc27, 0xce9c, 0x11d1, 0x97,0xbf, 0x00,0x00,0xf8,0x1e,0x84,0x9c)
#endif
#else
typedef struct ISWbemObjectPathVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemObjectPath *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemObjectPath *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemObjectPath *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemObjectPath *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemObjectPath *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemObjectPath *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemObjectPath *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemObjectPath methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        ISWbemObjectPath *This,
        BSTR *strPath);

    HRESULT (STDMETHODCALLTYPE *put_Path)(
        ISWbemObjectPath *This,
        BSTR strPath);

    HRESULT (STDMETHODCALLTYPE *get_RelPath)(
        ISWbemObjectPath *This,
        BSTR *strRelPath);

    HRESULT (STDMETHODCALLTYPE *put_RelPath)(
        ISWbemObjectPath *This,
        BSTR strRelPath);

    HRESULT (STDMETHODCALLTYPE *get_Server)(
        ISWbemObjectPath *This,
        BSTR *strServer);

    HRESULT (STDMETHODCALLTYPE *put_Server)(
        ISWbemObjectPath *This,
        BSTR strServer);

    HRESULT (STDMETHODCALLTYPE *get_Namespace)(
        ISWbemObjectPath *This,
        BSTR *strNamespace);

    HRESULT (STDMETHODCALLTYPE *put_Namespace)(
        ISWbemObjectPath *This,
        BSTR strNamespace);

    HRESULT (STDMETHODCALLTYPE *get_ParentNamespace)(
        ISWbemObjectPath *This,
        BSTR *strParentNamespace);

    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        ISWbemObjectPath *This,
        BSTR *strDisplayName);

    HRESULT (STDMETHODCALLTYPE *put_DisplayName)(
        ISWbemObjectPath *This,
        BSTR strDisplayName);

    HRESULT (STDMETHODCALLTYPE *get_Class)(
        ISWbemObjectPath *This,
        BSTR *strClass);

    HRESULT (STDMETHODCALLTYPE *put_Class)(
        ISWbemObjectPath *This,
        BSTR strClass);

    HRESULT (STDMETHODCALLTYPE *get_IsClass)(
        ISWbemObjectPath *This,
        VARIANT_BOOL *bIsClass);

    HRESULT (STDMETHODCALLTYPE *SetAsClass)(
        ISWbemObjectPath *This);

    HRESULT (STDMETHODCALLTYPE *get_IsSingleton)(
        ISWbemObjectPath *This,
        VARIANT_BOOL *bIsSingleton);

    HRESULT (STDMETHODCALLTYPE *SetAsSingleton)(
        ISWbemObjectPath *This);

    HRESULT (STDMETHODCALLTYPE *get_Keys)(
        ISWbemObjectPath *This,
        ISWbemNamedValueSet **objWbemNamedValueSet);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemObjectPath *This,
        ISWbemSecurity **objWbemSecurity);

    HRESULT (STDMETHODCALLTYPE *get_Locale)(
        ISWbemObjectPath *This,
        BSTR *strLocale);

    HRESULT (STDMETHODCALLTYPE *put_Locale)(
        ISWbemObjectPath *This,
        BSTR strLocale);

    HRESULT (STDMETHODCALLTYPE *get_Authority)(
        ISWbemObjectPath *This,
        BSTR *strAuthority);

    HRESULT (STDMETHODCALLTYPE *put_Authority)(
        ISWbemObjectPath *This,
        BSTR strAuthority);

    END_INTERFACE
} ISWbemObjectPathVtbl;

interface ISWbemObjectPath {
    CONST_VTBL ISWbemObjectPathVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemObjectPath_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemObjectPath_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemObjectPath_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemObjectPath_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemObjectPath_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemObjectPath_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemObjectPath_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemObjectPath methods ***/
#define ISWbemObjectPath_get_Path(This,strPath) (This)->lpVtbl->get_Path(This,strPath)
#define ISWbemObjectPath_put_Path(This,strPath) (This)->lpVtbl->put_Path(This,strPath)
#define ISWbemObjectPath_get_RelPath(This,strRelPath) (This)->lpVtbl->get_RelPath(This,strRelPath)
#define ISWbemObjectPath_put_RelPath(This,strRelPath) (This)->lpVtbl->put_RelPath(This,strRelPath)
#define ISWbemObjectPath_get_Server(This,strServer) (This)->lpVtbl->get_Server(This,strServer)
#define ISWbemObjectPath_put_Server(This,strServer) (This)->lpVtbl->put_Server(This,strServer)
#define ISWbemObjectPath_get_Namespace(This,strNamespace) (This)->lpVtbl->get_Namespace(This,strNamespace)
#define ISWbemObjectPath_put_Namespace(This,strNamespace) (This)->lpVtbl->put_Namespace(This,strNamespace)
#define ISWbemObjectPath_get_ParentNamespace(This,strParentNamespace) (This)->lpVtbl->get_ParentNamespace(This,strParentNamespace)
#define ISWbemObjectPath_get_DisplayName(This,strDisplayName) (This)->lpVtbl->get_DisplayName(This,strDisplayName)
#define ISWbemObjectPath_put_DisplayName(This,strDisplayName) (This)->lpVtbl->put_DisplayName(This,strDisplayName)
#define ISWbemObjectPath_get_Class(This,strClass) (This)->lpVtbl->get_Class(This,strClass)
#define ISWbemObjectPath_put_Class(This,strClass) (This)->lpVtbl->put_Class(This,strClass)
#define ISWbemObjectPath_get_IsClass(This,bIsClass) (This)->lpVtbl->get_IsClass(This,bIsClass)
#define ISWbemObjectPath_SetAsClass(This) (This)->lpVtbl->SetAsClass(This)
#define ISWbemObjectPath_get_IsSingleton(This,bIsSingleton) (This)->lpVtbl->get_IsSingleton(This,bIsSingleton)
#define ISWbemObjectPath_SetAsSingleton(This) (This)->lpVtbl->SetAsSingleton(This)
#define ISWbemObjectPath_get_Keys(This,objWbemNamedValueSet) (This)->lpVtbl->get_Keys(This,objWbemNamedValueSet)
#define ISWbemObjectPath_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
#define ISWbemObjectPath_get_Locale(This,strLocale) (This)->lpVtbl->get_Locale(This,strLocale)
#define ISWbemObjectPath_put_Locale(This,strLocale) (This)->lpVtbl->put_Locale(This,strLocale)
#define ISWbemObjectPath_get_Authority(This,strAuthority) (This)->lpVtbl->get_Authority(This,strAuthority)
#define ISWbemObjectPath_put_Authority(This,strAuthority) (This)->lpVtbl->put_Authority(This,strAuthority)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemObjectPath_QueryInterface(ISWbemObjectPath* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemObjectPath_AddRef(ISWbemObjectPath* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemObjectPath_Release(ISWbemObjectPath* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemObjectPath_GetTypeInfoCount(ISWbemObjectPath* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemObjectPath_GetTypeInfo(ISWbemObjectPath* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemObjectPath_GetIDsOfNames(ISWbemObjectPath* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemObjectPath_Invoke(ISWbemObjectPath* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemObjectPath methods ***/
static inline HRESULT ISWbemObjectPath_get_Path(ISWbemObjectPath* This,BSTR *strPath) {
    return This->lpVtbl->get_Path(This,strPath);
}
static inline HRESULT ISWbemObjectPath_put_Path(ISWbemObjectPath* This,BSTR strPath) {
    return This->lpVtbl->put_Path(This,strPath);
}
static inline HRESULT ISWbemObjectPath_get_RelPath(ISWbemObjectPath* This,BSTR *strRelPath) {
    return This->lpVtbl->get_RelPath(This,strRelPath);
}
static inline HRESULT ISWbemObjectPath_put_RelPath(ISWbemObjectPath* This,BSTR strRelPath) {
    return This->lpVtbl->put_RelPath(This,strRelPath);
}
static inline HRESULT ISWbemObjectPath_get_Server(ISWbemObjectPath* This,BSTR *strServer) {
    return This->lpVtbl->get_Server(This,strServer);
}
static inline HRESULT ISWbemObjectPath_put_Server(ISWbemObjectPath* This,BSTR strServer) {
    return This->lpVtbl->put_Server(This,strServer);
}
static inline HRESULT ISWbemObjectPath_get_Namespace(ISWbemObjectPath* This,BSTR *strNamespace) {
    return This->lpVtbl->get_Namespace(This,strNamespace);
}
static inline HRESULT ISWbemObjectPath_put_Namespace(ISWbemObjectPath* This,BSTR strNamespace) {
    return This->lpVtbl->put_Namespace(This,strNamespace);
}
static inline HRESULT ISWbemObjectPath_get_ParentNamespace(ISWbemObjectPath* This,BSTR *strParentNamespace) {
    return This->lpVtbl->get_ParentNamespace(This,strParentNamespace);
}
static inline HRESULT ISWbemObjectPath_get_DisplayName(ISWbemObjectPath* This,BSTR *strDisplayName) {
    return This->lpVtbl->get_DisplayName(This,strDisplayName);
}
static inline HRESULT ISWbemObjectPath_put_DisplayName(ISWbemObjectPath* This,BSTR strDisplayName) {
    return This->lpVtbl->put_DisplayName(This,strDisplayName);
}
static inline HRESULT ISWbemObjectPath_get_Class(ISWbemObjectPath* This,BSTR *strClass) {
    return This->lpVtbl->get_Class(This,strClass);
}
static inline HRESULT ISWbemObjectPath_put_Class(ISWbemObjectPath* This,BSTR strClass) {
    return This->lpVtbl->put_Class(This,strClass);
}
static inline HRESULT ISWbemObjectPath_get_IsClass(ISWbemObjectPath* This,VARIANT_BOOL *bIsClass) {
    return This->lpVtbl->get_IsClass(This,bIsClass);
}
static inline HRESULT ISWbemObjectPath_SetAsClass(ISWbemObjectPath* This) {
    return This->lpVtbl->SetAsClass(This);
}
static inline HRESULT ISWbemObjectPath_get_IsSingleton(ISWbemObjectPath* This,VARIANT_BOOL *bIsSingleton) {
    return This->lpVtbl->get_IsSingleton(This,bIsSingleton);
}
static inline HRESULT ISWbemObjectPath_SetAsSingleton(ISWbemObjectPath* This) {
    return This->lpVtbl->SetAsSingleton(This);
}
static inline HRESULT ISWbemObjectPath_get_Keys(ISWbemObjectPath* This,ISWbemNamedValueSet **objWbemNamedValueSet) {
    return This->lpVtbl->get_Keys(This,objWbemNamedValueSet);
}
static inline HRESULT ISWbemObjectPath_get_Security_(ISWbemObjectPath* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
static inline HRESULT ISWbemObjectPath_get_Locale(ISWbemObjectPath* This,BSTR *strLocale) {
    return This->lpVtbl->get_Locale(This,strLocale);
}
static inline HRESULT ISWbemObjectPath_put_Locale(ISWbemObjectPath* This,BSTR strLocale) {
    return This->lpVtbl->put_Locale(This,strLocale);
}
static inline HRESULT ISWbemObjectPath_get_Authority(ISWbemObjectPath* This,BSTR *strAuthority) {
    return This->lpVtbl->get_Authority(This,strAuthority);
}
static inline HRESULT ISWbemObjectPath_put_Authority(ISWbemObjectPath* This,BSTR strAuthority) {
    return This->lpVtbl->put_Authority(This,strAuthority);
}
#endif
#endif

#endif


#endif  /* __ISWbemObjectPath_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemProperty interface
 */
#ifndef __ISWbemProperty_INTERFACE_DEFINED__
#define __ISWbemProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemProperty, 0x1a388f98, 0xd4ba, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1a388f98-d4ba-11d1-8b09-00600806d9b6")
ISWbemProperty : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Value(
        VARIANT *varValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Value(
        VARIANT *varValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *strName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsLocal(
        VARIANT_BOOL *bIsLocal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Origin(
        BSTR *strOrigin) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CIMType(
        WbemCimtypeEnum *iCimType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Qualifiers_(
        ISWbemQualifierSet **objWbemQualifierSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsArray(
        VARIANT_BOOL *bIsArray) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemProperty, 0x1a388f98, 0xd4ba, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemProperty *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemProperty *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemProperty *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemProperty *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemProperty *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        ISWbemProperty *This,
        VARIANT *varValue);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        ISWbemProperty *This,
        VARIANT *varValue);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ISWbemProperty *This,
        BSTR *strName);

    HRESULT (STDMETHODCALLTYPE *get_IsLocal)(
        ISWbemProperty *This,
        VARIANT_BOOL *bIsLocal);

    HRESULT (STDMETHODCALLTYPE *get_Origin)(
        ISWbemProperty *This,
        BSTR *strOrigin);

    HRESULT (STDMETHODCALLTYPE *get_CIMType)(
        ISWbemProperty *This,
        WbemCimtypeEnum *iCimType);

    HRESULT (STDMETHODCALLTYPE *get_Qualifiers_)(
        ISWbemProperty *This,
        ISWbemQualifierSet **objWbemQualifierSet);

    HRESULT (STDMETHODCALLTYPE *get_IsArray)(
        ISWbemProperty *This,
        VARIANT_BOOL *bIsArray);

    END_INTERFACE
} ISWbemPropertyVtbl;

interface ISWbemProperty {
    CONST_VTBL ISWbemPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemProperty_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemProperty_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemProperty_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemProperty_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemProperty methods ***/
#define ISWbemProperty_get_Value(This,varValue) (This)->lpVtbl->get_Value(This,varValue)
#define ISWbemProperty_put_Value(This,varValue) (This)->lpVtbl->put_Value(This,varValue)
#define ISWbemProperty_get_Name(This,strName) (This)->lpVtbl->get_Name(This,strName)
#define ISWbemProperty_get_IsLocal(This,bIsLocal) (This)->lpVtbl->get_IsLocal(This,bIsLocal)
#define ISWbemProperty_get_Origin(This,strOrigin) (This)->lpVtbl->get_Origin(This,strOrigin)
#define ISWbemProperty_get_CIMType(This,iCimType) (This)->lpVtbl->get_CIMType(This,iCimType)
#define ISWbemProperty_get_Qualifiers_(This,objWbemQualifierSet) (This)->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet)
#define ISWbemProperty_get_IsArray(This,bIsArray) (This)->lpVtbl->get_IsArray(This,bIsArray)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemProperty_QueryInterface(ISWbemProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemProperty_AddRef(ISWbemProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemProperty_Release(ISWbemProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemProperty_GetTypeInfoCount(ISWbemProperty* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemProperty_GetTypeInfo(ISWbemProperty* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemProperty_GetIDsOfNames(ISWbemProperty* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemProperty_Invoke(ISWbemProperty* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemProperty methods ***/
static inline HRESULT ISWbemProperty_get_Value(ISWbemProperty* This,VARIANT *varValue) {
    return This->lpVtbl->get_Value(This,varValue);
}
static inline HRESULT ISWbemProperty_put_Value(ISWbemProperty* This,VARIANT *varValue) {
    return This->lpVtbl->put_Value(This,varValue);
}
static inline HRESULT ISWbemProperty_get_Name(ISWbemProperty* This,BSTR *strName) {
    return This->lpVtbl->get_Name(This,strName);
}
static inline HRESULT ISWbemProperty_get_IsLocal(ISWbemProperty* This,VARIANT_BOOL *bIsLocal) {
    return This->lpVtbl->get_IsLocal(This,bIsLocal);
}
static inline HRESULT ISWbemProperty_get_Origin(ISWbemProperty* This,BSTR *strOrigin) {
    return This->lpVtbl->get_Origin(This,strOrigin);
}
static inline HRESULT ISWbemProperty_get_CIMType(ISWbemProperty* This,WbemCimtypeEnum *iCimType) {
    return This->lpVtbl->get_CIMType(This,iCimType);
}
static inline HRESULT ISWbemProperty_get_Qualifiers_(ISWbemProperty* This,ISWbemQualifierSet **objWbemQualifierSet) {
    return This->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet);
}
static inline HRESULT ISWbemProperty_get_IsArray(ISWbemProperty* This,VARIANT_BOOL *bIsArray) {
    return This->lpVtbl->get_IsArray(This,bIsArray);
}
#endif
#endif

#endif


#endif  /* __ISWbemProperty_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemPropertySet interface
 */
#ifndef __ISWbemPropertySet_INTERFACE_DEFINED__
#define __ISWbemPropertySet_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemPropertySet, 0xdea0a7b2, 0xd4ba, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dea0a7b2-d4ba-11d1-8b09-00600806d9b6")
ISWbemPropertySet : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        BSTR strName,
        LONG iFlags = 0,
        ISWbemProperty **objWbemProperty = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *iCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        BSTR strName,
        WbemCimtypeEnum iCIMType,
        VARIANT_BOOL bIsArray = FALSE,
        LONG iFlags = 0,
        ISWbemProperty **objWbemProperty = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        BSTR strName,
        LONG iFlags = 0) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemPropertySet, 0xdea0a7b2, 0xd4ba, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemPropertySetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemPropertySet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemPropertySet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemPropertySet *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemPropertySet *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemPropertySet *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemPropertySet *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemPropertySet *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemPropertySet methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ISWbemPropertySet *This,
        IUnknown **pUnk);

    HRESULT (STDMETHODCALLTYPE *Item)(
        ISWbemPropertySet *This,
        BSTR strName,
        LONG iFlags,
        ISWbemProperty **objWbemProperty);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ISWbemPropertySet *This,
        LONG *iCount);

    HRESULT (STDMETHODCALLTYPE *Add)(
        ISWbemPropertySet *This,
        BSTR strName,
        WbemCimtypeEnum iCIMType,
        VARIANT_BOOL bIsArray,
        LONG iFlags,
        ISWbemProperty **objWbemProperty);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ISWbemPropertySet *This,
        BSTR strName,
        LONG iFlags);

    END_INTERFACE
} ISWbemPropertySetVtbl;

interface ISWbemPropertySet {
    CONST_VTBL ISWbemPropertySetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemPropertySet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemPropertySet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemPropertySet_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemPropertySet_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemPropertySet_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemPropertySet_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemPropertySet_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemPropertySet methods ***/
#define ISWbemPropertySet_get__NewEnum(This,pUnk) (This)->lpVtbl->get__NewEnum(This,pUnk)
#define ISWbemPropertySet_Item(This,strName,iFlags,objWbemProperty) (This)->lpVtbl->Item(This,strName,iFlags,objWbemProperty)
#define ISWbemPropertySet_get_Count(This,iCount) (This)->lpVtbl->get_Count(This,iCount)
#define ISWbemPropertySet_Add(This,strName,iCIMType,bIsArray,iFlags,objWbemProperty) (This)->lpVtbl->Add(This,strName,iCIMType,bIsArray,iFlags,objWbemProperty)
#define ISWbemPropertySet_Remove(This,strName,iFlags) (This)->lpVtbl->Remove(This,strName,iFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemPropertySet_QueryInterface(ISWbemPropertySet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemPropertySet_AddRef(ISWbemPropertySet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemPropertySet_Release(ISWbemPropertySet* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemPropertySet_GetTypeInfoCount(ISWbemPropertySet* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemPropertySet_GetTypeInfo(ISWbemPropertySet* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemPropertySet_GetIDsOfNames(ISWbemPropertySet* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemPropertySet_Invoke(ISWbemPropertySet* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemPropertySet methods ***/
static inline HRESULT ISWbemPropertySet_get__NewEnum(ISWbemPropertySet* This,IUnknown **pUnk) {
    return This->lpVtbl->get__NewEnum(This,pUnk);
}
static inline HRESULT ISWbemPropertySet_Item(ISWbemPropertySet* This,BSTR strName,LONG iFlags,ISWbemProperty **objWbemProperty) {
    return This->lpVtbl->Item(This,strName,iFlags,objWbemProperty);
}
static inline HRESULT ISWbemPropertySet_get_Count(ISWbemPropertySet* This,LONG *iCount) {
    return This->lpVtbl->get_Count(This,iCount);
}
static inline HRESULT ISWbemPropertySet_Add(ISWbemPropertySet* This,BSTR strName,WbemCimtypeEnum iCIMType,VARIANT_BOOL bIsArray,LONG iFlags,ISWbemProperty **objWbemProperty) {
    return This->lpVtbl->Add(This,strName,iCIMType,bIsArray,iFlags,objWbemProperty);
}
static inline HRESULT ISWbemPropertySet_Remove(ISWbemPropertySet* This,BSTR strName,LONG iFlags) {
    return This->lpVtbl->Remove(This,strName,iFlags);
}
#endif
#endif

#endif


#endif  /* __ISWbemPropertySet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemQualifier interface
 */
#ifndef __ISWbemQualifier_INTERFACE_DEFINED__
#define __ISWbemQualifier_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemQualifier, 0x79b05932, 0xd3b7, 0x11d1, 0x8b,0x06, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79b05932-d3b7-11d1-8b06-00600806d9b6")
ISWbemQualifier : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Value(
        VARIANT *varValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Value(
        VARIANT *varValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *strName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsLocal(
        VARIANT_BOOL *bIsLocal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PropagatesToSubclass(
        VARIANT_BOOL *bPropagatesToSubclass) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PropagatesToSubclass(
        VARIANT_BOOL bPropagatesToSubclass) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PropagatesToInstance(
        VARIANT_BOOL *bPropagatesToInstance) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PropagatesToInstance(
        VARIANT_BOOL bPropagatesToInstance) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsOverridable(
        VARIANT_BOOL *bIsOverridable) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IsOverridable(
        VARIANT_BOOL bIsOverridable) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsAmended(
        VARIANT_BOOL *bIsAmended) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemQualifier, 0x79b05932, 0xd3b7, 0x11d1, 0x8b,0x06, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemQualifierVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemQualifier *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemQualifier *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemQualifier *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemQualifier *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemQualifier *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemQualifier *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemQualifier *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemQualifier methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        ISWbemQualifier *This,
        VARIANT *varValue);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        ISWbemQualifier *This,
        VARIANT *varValue);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ISWbemQualifier *This,
        BSTR *strName);

    HRESULT (STDMETHODCALLTYPE *get_IsLocal)(
        ISWbemQualifier *This,
        VARIANT_BOOL *bIsLocal);

    HRESULT (STDMETHODCALLTYPE *get_PropagatesToSubclass)(
        ISWbemQualifier *This,
        VARIANT_BOOL *bPropagatesToSubclass);

    HRESULT (STDMETHODCALLTYPE *put_PropagatesToSubclass)(
        ISWbemQualifier *This,
        VARIANT_BOOL bPropagatesToSubclass);

    HRESULT (STDMETHODCALLTYPE *get_PropagatesToInstance)(
        ISWbemQualifier *This,
        VARIANT_BOOL *bPropagatesToInstance);

    HRESULT (STDMETHODCALLTYPE *put_PropagatesToInstance)(
        ISWbemQualifier *This,
        VARIANT_BOOL bPropagatesToInstance);

    HRESULT (STDMETHODCALLTYPE *get_IsOverridable)(
        ISWbemQualifier *This,
        VARIANT_BOOL *bIsOverridable);

    HRESULT (STDMETHODCALLTYPE *put_IsOverridable)(
        ISWbemQualifier *This,
        VARIANT_BOOL bIsOverridable);

    HRESULT (STDMETHODCALLTYPE *get_IsAmended)(
        ISWbemQualifier *This,
        VARIANT_BOOL *bIsAmended);

    END_INTERFACE
} ISWbemQualifierVtbl;

interface ISWbemQualifier {
    CONST_VTBL ISWbemQualifierVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemQualifier_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemQualifier_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemQualifier_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemQualifier_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemQualifier_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemQualifier_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemQualifier_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemQualifier methods ***/
#define ISWbemQualifier_get_Value(This,varValue) (This)->lpVtbl->get_Value(This,varValue)
#define ISWbemQualifier_put_Value(This,varValue) (This)->lpVtbl->put_Value(This,varValue)
#define ISWbemQualifier_get_Name(This,strName) (This)->lpVtbl->get_Name(This,strName)
#define ISWbemQualifier_get_IsLocal(This,bIsLocal) (This)->lpVtbl->get_IsLocal(This,bIsLocal)
#define ISWbemQualifier_get_PropagatesToSubclass(This,bPropagatesToSubclass) (This)->lpVtbl->get_PropagatesToSubclass(This,bPropagatesToSubclass)
#define ISWbemQualifier_put_PropagatesToSubclass(This,bPropagatesToSubclass) (This)->lpVtbl->put_PropagatesToSubclass(This,bPropagatesToSubclass)
#define ISWbemQualifier_get_PropagatesToInstance(This,bPropagatesToInstance) (This)->lpVtbl->get_PropagatesToInstance(This,bPropagatesToInstance)
#define ISWbemQualifier_put_PropagatesToInstance(This,bPropagatesToInstance) (This)->lpVtbl->put_PropagatesToInstance(This,bPropagatesToInstance)
#define ISWbemQualifier_get_IsOverridable(This,bIsOverridable) (This)->lpVtbl->get_IsOverridable(This,bIsOverridable)
#define ISWbemQualifier_put_IsOverridable(This,bIsOverridable) (This)->lpVtbl->put_IsOverridable(This,bIsOverridable)
#define ISWbemQualifier_get_IsAmended(This,bIsAmended) (This)->lpVtbl->get_IsAmended(This,bIsAmended)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemQualifier_QueryInterface(ISWbemQualifier* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemQualifier_AddRef(ISWbemQualifier* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemQualifier_Release(ISWbemQualifier* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemQualifier_GetTypeInfoCount(ISWbemQualifier* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemQualifier_GetTypeInfo(ISWbemQualifier* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemQualifier_GetIDsOfNames(ISWbemQualifier* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemQualifier_Invoke(ISWbemQualifier* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemQualifier methods ***/
static inline HRESULT ISWbemQualifier_get_Value(ISWbemQualifier* This,VARIANT *varValue) {
    return This->lpVtbl->get_Value(This,varValue);
}
static inline HRESULT ISWbemQualifier_put_Value(ISWbemQualifier* This,VARIANT *varValue) {
    return This->lpVtbl->put_Value(This,varValue);
}
static inline HRESULT ISWbemQualifier_get_Name(ISWbemQualifier* This,BSTR *strName) {
    return This->lpVtbl->get_Name(This,strName);
}
static inline HRESULT ISWbemQualifier_get_IsLocal(ISWbemQualifier* This,VARIANT_BOOL *bIsLocal) {
    return This->lpVtbl->get_IsLocal(This,bIsLocal);
}
static inline HRESULT ISWbemQualifier_get_PropagatesToSubclass(ISWbemQualifier* This,VARIANT_BOOL *bPropagatesToSubclass) {
    return This->lpVtbl->get_PropagatesToSubclass(This,bPropagatesToSubclass);
}
static inline HRESULT ISWbemQualifier_put_PropagatesToSubclass(ISWbemQualifier* This,VARIANT_BOOL bPropagatesToSubclass) {
    return This->lpVtbl->put_PropagatesToSubclass(This,bPropagatesToSubclass);
}
static inline HRESULT ISWbemQualifier_get_PropagatesToInstance(ISWbemQualifier* This,VARIANT_BOOL *bPropagatesToInstance) {
    return This->lpVtbl->get_PropagatesToInstance(This,bPropagatesToInstance);
}
static inline HRESULT ISWbemQualifier_put_PropagatesToInstance(ISWbemQualifier* This,VARIANT_BOOL bPropagatesToInstance) {
    return This->lpVtbl->put_PropagatesToInstance(This,bPropagatesToInstance);
}
static inline HRESULT ISWbemQualifier_get_IsOverridable(ISWbemQualifier* This,VARIANT_BOOL *bIsOverridable) {
    return This->lpVtbl->get_IsOverridable(This,bIsOverridable);
}
static inline HRESULT ISWbemQualifier_put_IsOverridable(ISWbemQualifier* This,VARIANT_BOOL bIsOverridable) {
    return This->lpVtbl->put_IsOverridable(This,bIsOverridable);
}
static inline HRESULT ISWbemQualifier_get_IsAmended(ISWbemQualifier* This,VARIANT_BOOL *bIsAmended) {
    return This->lpVtbl->get_IsAmended(This,bIsAmended);
}
#endif
#endif

#endif


#endif  /* __ISWbemQualifier_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemQualifierSet interface
 */
#ifndef __ISWbemQualifierSet_INTERFACE_DEFINED__
#define __ISWbemQualifierSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemQualifierSet, 0x9b16ed16, 0xd3df, 0x11d1, 0x8b,0x08, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9b16ed16-d3df-11d1-8b08-00600806d9b6")
ISWbemQualifierSet : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        BSTR name,
        LONG iFlags = 0,
        ISWbemQualifier **objWbemQualifier = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *iCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        BSTR strName,
        VARIANT *varVal,
        VARIANT_BOOL bPropagatesToSubclass = TRUE,
        VARIANT_BOOL bPropagatesToInstance = TRUE,
        VARIANT_BOOL bIsOverridable = TRUE,
        LONG iFlags = 0,
        ISWbemQualifier **objWbemQualifier = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        BSTR strName,
        LONG iFlags = 0) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemQualifierSet, 0x9b16ed16, 0xd3df, 0x11d1, 0x8b,0x08, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemQualifierSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemQualifierSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemQualifierSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemQualifierSet *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemQualifierSet *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemQualifierSet *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemQualifierSet *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemQualifierSet *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemQualifierSet methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ISWbemQualifierSet *This,
        IUnknown **pUnk);

    HRESULT (STDMETHODCALLTYPE *Item)(
        ISWbemQualifierSet *This,
        BSTR name,
        LONG iFlags,
        ISWbemQualifier **objWbemQualifier);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ISWbemQualifierSet *This,
        LONG *iCount);

    HRESULT (STDMETHODCALLTYPE *Add)(
        ISWbemQualifierSet *This,
        BSTR strName,
        VARIANT *varVal,
        VARIANT_BOOL bPropagatesToSubclass,
        VARIANT_BOOL bPropagatesToInstance,
        VARIANT_BOOL bIsOverridable,
        LONG iFlags,
        ISWbemQualifier **objWbemQualifier);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ISWbemQualifierSet *This,
        BSTR strName,
        LONG iFlags);

    END_INTERFACE
} ISWbemQualifierSetVtbl;

interface ISWbemQualifierSet {
    CONST_VTBL ISWbemQualifierSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemQualifierSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemQualifierSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemQualifierSet_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemQualifierSet_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemQualifierSet_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemQualifierSet_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemQualifierSet_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemQualifierSet methods ***/
#define ISWbemQualifierSet_get__NewEnum(This,pUnk) (This)->lpVtbl->get__NewEnum(This,pUnk)
#define ISWbemQualifierSet_Item(This,name,iFlags,objWbemQualifier) (This)->lpVtbl->Item(This,name,iFlags,objWbemQualifier)
#define ISWbemQualifierSet_get_Count(This,iCount) (This)->lpVtbl->get_Count(This,iCount)
#define ISWbemQualifierSet_Add(This,strName,varVal,bPropagatesToSubclass,bPropagatesToInstance,bIsOverridable,iFlags,objWbemQualifier) (This)->lpVtbl->Add(This,strName,varVal,bPropagatesToSubclass,bPropagatesToInstance,bIsOverridable,iFlags,objWbemQualifier)
#define ISWbemQualifierSet_Remove(This,strName,iFlags) (This)->lpVtbl->Remove(This,strName,iFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemQualifierSet_QueryInterface(ISWbemQualifierSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemQualifierSet_AddRef(ISWbemQualifierSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemQualifierSet_Release(ISWbemQualifierSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemQualifierSet_GetTypeInfoCount(ISWbemQualifierSet* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemQualifierSet_GetTypeInfo(ISWbemQualifierSet* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemQualifierSet_GetIDsOfNames(ISWbemQualifierSet* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemQualifierSet_Invoke(ISWbemQualifierSet* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemQualifierSet methods ***/
static inline HRESULT ISWbemQualifierSet_get__NewEnum(ISWbemQualifierSet* This,IUnknown **pUnk) {
    return This->lpVtbl->get__NewEnum(This,pUnk);
}
static inline HRESULT ISWbemQualifierSet_Item(ISWbemQualifierSet* This,BSTR name,LONG iFlags,ISWbemQualifier **objWbemQualifier) {
    return This->lpVtbl->Item(This,name,iFlags,objWbemQualifier);
}
static inline HRESULT ISWbemQualifierSet_get_Count(ISWbemQualifierSet* This,LONG *iCount) {
    return This->lpVtbl->get_Count(This,iCount);
}
static inline HRESULT ISWbemQualifierSet_Add(ISWbemQualifierSet* This,BSTR strName,VARIANT *varVal,VARIANT_BOOL bPropagatesToSubclass,VARIANT_BOOL bPropagatesToInstance,VARIANT_BOOL bIsOverridable,LONG iFlags,ISWbemQualifier **objWbemQualifier) {
    return This->lpVtbl->Add(This,strName,varVal,bPropagatesToSubclass,bPropagatesToInstance,bIsOverridable,iFlags,objWbemQualifier);
}
static inline HRESULT ISWbemQualifierSet_Remove(ISWbemQualifierSet* This,BSTR strName,LONG iFlags) {
    return This->lpVtbl->Remove(This,strName,iFlags);
}
#endif
#endif

#endif


#endif  /* __ISWbemQualifierSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemMethod interface
 */
#ifndef __ISWbemMethod_INTERFACE_DEFINED__
#define __ISWbemMethod_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemMethod, 0x422e8e90, 0xd955, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("422e8e90-d955-11d1-8b09-00600806d9b6")
ISWbemMethod : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *strName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Origin(
        BSTR *strOrigin) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InParameters(
        ISWbemObject **objWbemInParameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_OutParameters(
        ISWbemObject **objWbemOutParameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Qualifiers_(
        ISWbemQualifierSet **objWbemQualifierSet) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemMethod, 0x422e8e90, 0xd955, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemMethodVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemMethod *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemMethod *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemMethod *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemMethod *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemMethod *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemMethod *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemMethod *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemMethod methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ISWbemMethod *This,
        BSTR *strName);

    HRESULT (STDMETHODCALLTYPE *get_Origin)(
        ISWbemMethod *This,
        BSTR *strOrigin);

    HRESULT (STDMETHODCALLTYPE *get_InParameters)(
        ISWbemMethod *This,
        ISWbemObject **objWbemInParameters);

    HRESULT (STDMETHODCALLTYPE *get_OutParameters)(
        ISWbemMethod *This,
        ISWbemObject **objWbemOutParameters);

    HRESULT (STDMETHODCALLTYPE *get_Qualifiers_)(
        ISWbemMethod *This,
        ISWbemQualifierSet **objWbemQualifierSet);

    END_INTERFACE
} ISWbemMethodVtbl;

interface ISWbemMethod {
    CONST_VTBL ISWbemMethodVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemMethod_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemMethod_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemMethod_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemMethod_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemMethod_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemMethod_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemMethod_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemMethod methods ***/
#define ISWbemMethod_get_Name(This,strName) (This)->lpVtbl->get_Name(This,strName)
#define ISWbemMethod_get_Origin(This,strOrigin) (This)->lpVtbl->get_Origin(This,strOrigin)
#define ISWbemMethod_get_InParameters(This,objWbemInParameters) (This)->lpVtbl->get_InParameters(This,objWbemInParameters)
#define ISWbemMethod_get_OutParameters(This,objWbemOutParameters) (This)->lpVtbl->get_OutParameters(This,objWbemOutParameters)
#define ISWbemMethod_get_Qualifiers_(This,objWbemQualifierSet) (This)->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemMethod_QueryInterface(ISWbemMethod* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemMethod_AddRef(ISWbemMethod* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemMethod_Release(ISWbemMethod* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemMethod_GetTypeInfoCount(ISWbemMethod* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemMethod_GetTypeInfo(ISWbemMethod* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemMethod_GetIDsOfNames(ISWbemMethod* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemMethod_Invoke(ISWbemMethod* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemMethod methods ***/
static inline HRESULT ISWbemMethod_get_Name(ISWbemMethod* This,BSTR *strName) {
    return This->lpVtbl->get_Name(This,strName);
}
static inline HRESULT ISWbemMethod_get_Origin(ISWbemMethod* This,BSTR *strOrigin) {
    return This->lpVtbl->get_Origin(This,strOrigin);
}
static inline HRESULT ISWbemMethod_get_InParameters(ISWbemMethod* This,ISWbemObject **objWbemInParameters) {
    return This->lpVtbl->get_InParameters(This,objWbemInParameters);
}
static inline HRESULT ISWbemMethod_get_OutParameters(ISWbemMethod* This,ISWbemObject **objWbemOutParameters) {
    return This->lpVtbl->get_OutParameters(This,objWbemOutParameters);
}
static inline HRESULT ISWbemMethod_get_Qualifiers_(ISWbemMethod* This,ISWbemQualifierSet **objWbemQualifierSet) {
    return This->lpVtbl->get_Qualifiers_(This,objWbemQualifierSet);
}
#endif
#endif

#endif


#endif  /* __ISWbemMethod_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemMethodSet interface
 */
#ifndef __ISWbemMethodSet_INTERFACE_DEFINED__
#define __ISWbemMethodSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemMethodSet, 0xc93ba292, 0xd955, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c93ba292-d955-11d1-8b09-00600806d9b6")
ISWbemMethodSet : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        BSTR strName,
        LONG iFlags = 0,
        ISWbemMethod **objWbemMethod = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *iCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemMethodSet, 0xc93ba292, 0xd955, 0x11d1, 0x8b,0x09, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemMethodSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemMethodSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemMethodSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemMethodSet *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemMethodSet *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemMethodSet *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemMethodSet *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemMethodSet *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemMethodSet methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ISWbemMethodSet *This,
        IUnknown **pUnk);

    HRESULT (STDMETHODCALLTYPE *Item)(
        ISWbemMethodSet *This,
        BSTR strName,
        LONG iFlags,
        ISWbemMethod **objWbemMethod);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ISWbemMethodSet *This,
        LONG *iCount);

    END_INTERFACE
} ISWbemMethodSetVtbl;

interface ISWbemMethodSet {
    CONST_VTBL ISWbemMethodSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemMethodSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemMethodSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemMethodSet_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemMethodSet_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemMethodSet_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemMethodSet_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemMethodSet_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemMethodSet methods ***/
#define ISWbemMethodSet_get__NewEnum(This,pUnk) (This)->lpVtbl->get__NewEnum(This,pUnk)
#define ISWbemMethodSet_Item(This,strName,iFlags,objWbemMethod) (This)->lpVtbl->Item(This,strName,iFlags,objWbemMethod)
#define ISWbemMethodSet_get_Count(This,iCount) (This)->lpVtbl->get_Count(This,iCount)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemMethodSet_QueryInterface(ISWbemMethodSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemMethodSet_AddRef(ISWbemMethodSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemMethodSet_Release(ISWbemMethodSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemMethodSet_GetTypeInfoCount(ISWbemMethodSet* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemMethodSet_GetTypeInfo(ISWbemMethodSet* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemMethodSet_GetIDsOfNames(ISWbemMethodSet* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemMethodSet_Invoke(ISWbemMethodSet* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemMethodSet methods ***/
static inline HRESULT ISWbemMethodSet_get__NewEnum(ISWbemMethodSet* This,IUnknown **pUnk) {
    return This->lpVtbl->get__NewEnum(This,pUnk);
}
static inline HRESULT ISWbemMethodSet_Item(ISWbemMethodSet* This,BSTR strName,LONG iFlags,ISWbemMethod **objWbemMethod) {
    return This->lpVtbl->Item(This,strName,iFlags,objWbemMethod);
}
static inline HRESULT ISWbemMethodSet_get_Count(ISWbemMethodSet* This,LONG *iCount) {
    return This->lpVtbl->get_Count(This,iCount);
}
#endif
#endif

#endif


#endif  /* __ISWbemMethodSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemSink interface
 */
#ifndef __ISWbemSink_INTERFACE_DEFINED__
#define __ISWbemSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemSink, 0x75718c9f, 0xf029, 0x11d1, 0xa1,0xac, 0x00,0xc0,0x4f,0xb6,0xc2,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("75718c9f-f029-11d1-a1ac-00c04fb6c223")
ISWbemSink : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemSink, 0x75718c9f, 0xf029, 0x11d1, 0xa1,0xac, 0x00,0xc0,0x4f,0xb6,0xc2,0x23)
#endif
#else
typedef struct ISWbemSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemSink *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemSink *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemSink *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemSink *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemSink *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemSink methods ***/
    HRESULT (STDMETHODCALLTYPE *Cancel)(
        ISWbemSink *This);

    END_INTERFACE
} ISWbemSinkVtbl;

interface ISWbemSink {
    CONST_VTBL ISWbemSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemSink_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemSink_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemSink_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemSink_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemSink_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemSink methods ***/
#define ISWbemSink_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemSink_QueryInterface(ISWbemSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemSink_AddRef(ISWbemSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemSink_Release(ISWbemSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemSink_GetTypeInfoCount(ISWbemSink* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemSink_GetTypeInfo(ISWbemSink* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemSink_GetIDsOfNames(ISWbemSink* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemSink_Invoke(ISWbemSink* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemSink methods ***/
static inline HRESULT ISWbemSink_Cancel(ISWbemSink* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif


#endif  /* __ISWbemSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemSinkEvents dispinterface
 */
#ifndef __ISWbemSinkEvents_DISPINTERFACE_DEFINED__
#define __ISWbemSinkEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID_ISWbemSinkEvents, 0x75718ca0, 0xf029, 0x11d1, 0xa1,0xac, 0x00,0xc0,0x4f,0xb6,0xc2,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("75718ca0-f029-11d1-a1ac-00c04fb6c223")
ISWbemSinkEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemSinkEvents, 0x75718ca0, 0xf029, 0x11d1, 0xa1,0xac, 0x00,0xc0,0x4f,0xb6,0xc2,0x23)
#endif
#else
typedef struct ISWbemSinkEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemSinkEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemSinkEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemSinkEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemSinkEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemSinkEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemSinkEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemSinkEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} ISWbemSinkEventsVtbl;

interface ISWbemSinkEvents {
    CONST_VTBL ISWbemSinkEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemSinkEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemSinkEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemSinkEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemSinkEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemSinkEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemSinkEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemSinkEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemSinkEvents_QueryInterface(ISWbemSinkEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemSinkEvents_AddRef(ISWbemSinkEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemSinkEvents_Release(ISWbemSinkEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemSinkEvents_GetTypeInfoCount(ISWbemSinkEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemSinkEvents_GetTypeInfo(ISWbemSinkEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemSinkEvents_GetIDsOfNames(ISWbemSinkEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemSinkEvents_Invoke(ISWbemSinkEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* __ISWbemSinkEvents_DISPINTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemEventSource interface
 */
#ifndef __ISWbemEventSource_INTERFACE_DEFINED__
#define __ISWbemEventSource_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemEventSource, 0x27d54d92, 0x0ebe, 0x11d2, 0x8b,0x22, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("27d54d92-0ebe-11d2-8b22-00600806d9b6")
ISWbemEventSource : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE NextEvent(
        LONG iTimeoutMs = wbemTimeoutInfinite,
        ISWbemObject **objWbemObject = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Security_(
        ISWbemSecurity **objWbemSecurity) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemEventSource, 0x27d54d92, 0x0ebe, 0x11d2, 0x8b,0x22, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemEventSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemEventSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemEventSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemEventSource *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemEventSource *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemEventSource *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemEventSource *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemEventSource *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemEventSource methods ***/
    HRESULT (STDMETHODCALLTYPE *NextEvent)(
        ISWbemEventSource *This,
        LONG iTimeoutMs,
        ISWbemObject **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *get_Security_)(
        ISWbemEventSource *This,
        ISWbemSecurity **objWbemSecurity);

    END_INTERFACE
} ISWbemEventSourceVtbl;

interface ISWbemEventSource {
    CONST_VTBL ISWbemEventSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemEventSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemEventSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemEventSource_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemEventSource_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemEventSource_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemEventSource_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemEventSource_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemEventSource methods ***/
#define ISWbemEventSource_NextEvent(This,iTimeoutMs,objWbemObject) (This)->lpVtbl->NextEvent(This,iTimeoutMs,objWbemObject)
#define ISWbemEventSource_get_Security_(This,objWbemSecurity) (This)->lpVtbl->get_Security_(This,objWbemSecurity)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemEventSource_QueryInterface(ISWbemEventSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemEventSource_AddRef(ISWbemEventSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemEventSource_Release(ISWbemEventSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemEventSource_GetTypeInfoCount(ISWbemEventSource* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemEventSource_GetTypeInfo(ISWbemEventSource* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemEventSource_GetIDsOfNames(ISWbemEventSource* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemEventSource_Invoke(ISWbemEventSource* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemEventSource methods ***/
static inline HRESULT ISWbemEventSource_NextEvent(ISWbemEventSource* This,LONG iTimeoutMs,ISWbemObject **objWbemObject) {
    return This->lpVtbl->NextEvent(This,iTimeoutMs,objWbemObject);
}
static inline HRESULT ISWbemEventSource_get_Security_(ISWbemEventSource* This,ISWbemSecurity **objWbemSecurity) {
    return This->lpVtbl->get_Security_(This,objWbemSecurity);
}
#endif
#endif

#endif


#endif  /* __ISWbemEventSource_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemSecurity interface
 */
#ifndef __ISWbemSecurity_INTERFACE_DEFINED__
#define __ISWbemSecurity_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemSecurity, 0xb54d66e6, 0x2287, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b54d66e6-2287-11d2-8b33-00600806d9b6")
ISWbemSecurity : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ImpersonationLevel(
        WbemImpersonationLevelEnum *iImpersonationLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ImpersonationLevel(
        WbemImpersonationLevelEnum iImpersonationLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AuthenticationLevel(
        WbemAuthenticationLevelEnum *iAuthenticationLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AuthenticationLevel(
        WbemAuthenticationLevelEnum iAuthenticationLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Privileges(
        ISWbemPrivilegeSet **objWbemPrivilegeSet) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemSecurity, 0xb54d66e6, 0x2287, 0x11d2, 0x8b,0x33, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemSecurityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemSecurity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemSecurity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemSecurity *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemSecurity *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemSecurity *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemSecurity *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemSecurity *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemSecurity methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ImpersonationLevel)(
        ISWbemSecurity *This,
        WbemImpersonationLevelEnum *iImpersonationLevel);

    HRESULT (STDMETHODCALLTYPE *put_ImpersonationLevel)(
        ISWbemSecurity *This,
        WbemImpersonationLevelEnum iImpersonationLevel);

    HRESULT (STDMETHODCALLTYPE *get_AuthenticationLevel)(
        ISWbemSecurity *This,
        WbemAuthenticationLevelEnum *iAuthenticationLevel);

    HRESULT (STDMETHODCALLTYPE *put_AuthenticationLevel)(
        ISWbemSecurity *This,
        WbemAuthenticationLevelEnum iAuthenticationLevel);

    HRESULT (STDMETHODCALLTYPE *get_Privileges)(
        ISWbemSecurity *This,
        ISWbemPrivilegeSet **objWbemPrivilegeSet);

    END_INTERFACE
} ISWbemSecurityVtbl;

interface ISWbemSecurity {
    CONST_VTBL ISWbemSecurityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemSecurity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemSecurity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemSecurity_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemSecurity_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemSecurity_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemSecurity_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemSecurity_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemSecurity methods ***/
#define ISWbemSecurity_get_ImpersonationLevel(This,iImpersonationLevel) (This)->lpVtbl->get_ImpersonationLevel(This,iImpersonationLevel)
#define ISWbemSecurity_put_ImpersonationLevel(This,iImpersonationLevel) (This)->lpVtbl->put_ImpersonationLevel(This,iImpersonationLevel)
#define ISWbemSecurity_get_AuthenticationLevel(This,iAuthenticationLevel) (This)->lpVtbl->get_AuthenticationLevel(This,iAuthenticationLevel)
#define ISWbemSecurity_put_AuthenticationLevel(This,iAuthenticationLevel) (This)->lpVtbl->put_AuthenticationLevel(This,iAuthenticationLevel)
#define ISWbemSecurity_get_Privileges(This,objWbemPrivilegeSet) (This)->lpVtbl->get_Privileges(This,objWbemPrivilegeSet)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemSecurity_QueryInterface(ISWbemSecurity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemSecurity_AddRef(ISWbemSecurity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemSecurity_Release(ISWbemSecurity* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemSecurity_GetTypeInfoCount(ISWbemSecurity* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemSecurity_GetTypeInfo(ISWbemSecurity* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemSecurity_GetIDsOfNames(ISWbemSecurity* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemSecurity_Invoke(ISWbemSecurity* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemSecurity methods ***/
static inline HRESULT ISWbemSecurity_get_ImpersonationLevel(ISWbemSecurity* This,WbemImpersonationLevelEnum *iImpersonationLevel) {
    return This->lpVtbl->get_ImpersonationLevel(This,iImpersonationLevel);
}
static inline HRESULT ISWbemSecurity_put_ImpersonationLevel(ISWbemSecurity* This,WbemImpersonationLevelEnum iImpersonationLevel) {
    return This->lpVtbl->put_ImpersonationLevel(This,iImpersonationLevel);
}
static inline HRESULT ISWbemSecurity_get_AuthenticationLevel(ISWbemSecurity* This,WbemAuthenticationLevelEnum *iAuthenticationLevel) {
    return This->lpVtbl->get_AuthenticationLevel(This,iAuthenticationLevel);
}
static inline HRESULT ISWbemSecurity_put_AuthenticationLevel(ISWbemSecurity* This,WbemAuthenticationLevelEnum iAuthenticationLevel) {
    return This->lpVtbl->put_AuthenticationLevel(This,iAuthenticationLevel);
}
static inline HRESULT ISWbemSecurity_get_Privileges(ISWbemSecurity* This,ISWbemPrivilegeSet **objWbemPrivilegeSet) {
    return This->lpVtbl->get_Privileges(This,objWbemPrivilegeSet);
}
#endif
#endif

#endif


#endif  /* __ISWbemSecurity_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemPrivilege interface
 */
#ifndef __ISWbemPrivilege_INTERFACE_DEFINED__
#define __ISWbemPrivilege_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemPrivilege, 0x26ee67bd, 0x5804, 0x11d2, 0x8b,0x4a, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("26ee67bd-5804-11d2-8b4a-00600806d9b6")
ISWbemPrivilege : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_IsEnabled(
        VARIANT_BOOL *bIsEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IsEnabled(
        VARIANT_BOOL bIsEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *strDisplayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
        BSTR *strDisplayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Identifier(
        WbemPrivilegeEnum *iPrivilege) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemPrivilege, 0x26ee67bd, 0x5804, 0x11d2, 0x8b,0x4a, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemPrivilegeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemPrivilege *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemPrivilege *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemPrivilege *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemPrivilege *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemPrivilege *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemPrivilege *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemPrivilege *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemPrivilege methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsEnabled)(
        ISWbemPrivilege *This,
        VARIANT_BOOL *bIsEnabled);

    HRESULT (STDMETHODCALLTYPE *put_IsEnabled)(
        ISWbemPrivilege *This,
        VARIANT_BOOL bIsEnabled);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ISWbemPrivilege *This,
        BSTR *strDisplayName);

    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        ISWbemPrivilege *This,
        BSTR *strDisplayName);

    HRESULT (STDMETHODCALLTYPE *get_Identifier)(
        ISWbemPrivilege *This,
        WbemPrivilegeEnum *iPrivilege);

    END_INTERFACE
} ISWbemPrivilegeVtbl;

interface ISWbemPrivilege {
    CONST_VTBL ISWbemPrivilegeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemPrivilege_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemPrivilege_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemPrivilege_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemPrivilege_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemPrivilege_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemPrivilege_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemPrivilege_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemPrivilege methods ***/
#define ISWbemPrivilege_get_IsEnabled(This,bIsEnabled) (This)->lpVtbl->get_IsEnabled(This,bIsEnabled)
#define ISWbemPrivilege_put_IsEnabled(This,bIsEnabled) (This)->lpVtbl->put_IsEnabled(This,bIsEnabled)
#define ISWbemPrivilege_get_Name(This,strDisplayName) (This)->lpVtbl->get_Name(This,strDisplayName)
#define ISWbemPrivilege_get_DisplayName(This,strDisplayName) (This)->lpVtbl->get_DisplayName(This,strDisplayName)
#define ISWbemPrivilege_get_Identifier(This,iPrivilege) (This)->lpVtbl->get_Identifier(This,iPrivilege)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemPrivilege_QueryInterface(ISWbemPrivilege* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemPrivilege_AddRef(ISWbemPrivilege* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemPrivilege_Release(ISWbemPrivilege* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemPrivilege_GetTypeInfoCount(ISWbemPrivilege* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemPrivilege_GetTypeInfo(ISWbemPrivilege* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemPrivilege_GetIDsOfNames(ISWbemPrivilege* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemPrivilege_Invoke(ISWbemPrivilege* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemPrivilege methods ***/
static inline HRESULT ISWbemPrivilege_get_IsEnabled(ISWbemPrivilege* This,VARIANT_BOOL *bIsEnabled) {
    return This->lpVtbl->get_IsEnabled(This,bIsEnabled);
}
static inline HRESULT ISWbemPrivilege_put_IsEnabled(ISWbemPrivilege* This,VARIANT_BOOL bIsEnabled) {
    return This->lpVtbl->put_IsEnabled(This,bIsEnabled);
}
static inline HRESULT ISWbemPrivilege_get_Name(ISWbemPrivilege* This,BSTR *strDisplayName) {
    return This->lpVtbl->get_Name(This,strDisplayName);
}
static inline HRESULT ISWbemPrivilege_get_DisplayName(ISWbemPrivilege* This,BSTR *strDisplayName) {
    return This->lpVtbl->get_DisplayName(This,strDisplayName);
}
static inline HRESULT ISWbemPrivilege_get_Identifier(ISWbemPrivilege* This,WbemPrivilegeEnum *iPrivilege) {
    return This->lpVtbl->get_Identifier(This,iPrivilege);
}
#endif
#endif

#endif


#endif  /* __ISWbemPrivilege_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemPrivilegeSet interface
 */
#ifndef __ISWbemPrivilegeSet_INTERFACE_DEFINED__
#define __ISWbemPrivilegeSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemPrivilegeSet, 0x26ee67bf, 0x5804, 0x11d2, 0x8b,0x4a, 0x00,0x60,0x08,0x06,0xd9,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("26ee67bf-5804-11d2-8b4a-00600806d9b6")
ISWbemPrivilegeSet : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        WbemPrivilegeEnum iPrivilege,
        ISWbemPrivilege **objWbemPrivilege) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *iCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        WbemPrivilegeEnum iPrivilege,
        VARIANT_BOOL bIsEnabled = TRUE,
        ISWbemPrivilege **objWbemPrivilege = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        WbemPrivilegeEnum iPrivilege) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteAll(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddAsString(
        BSTR strPrivilege,
        VARIANT_BOOL bIsEnabled = TRUE,
        ISWbemPrivilege **objWbemPrivilege = 0) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemPrivilegeSet, 0x26ee67bf, 0x5804, 0x11d2, 0x8b,0x4a, 0x00,0x60,0x08,0x06,0xd9,0xb6)
#endif
#else
typedef struct ISWbemPrivilegeSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemPrivilegeSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemPrivilegeSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemPrivilegeSet *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemPrivilegeSet *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemPrivilegeSet *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemPrivilegeSet *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemPrivilegeSet *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemPrivilegeSet methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ISWbemPrivilegeSet *This,
        IUnknown **pUnk);

    HRESULT (STDMETHODCALLTYPE *Item)(
        ISWbemPrivilegeSet *This,
        WbemPrivilegeEnum iPrivilege,
        ISWbemPrivilege **objWbemPrivilege);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ISWbemPrivilegeSet *This,
        LONG *iCount);

    HRESULT (STDMETHODCALLTYPE *Add)(
        ISWbemPrivilegeSet *This,
        WbemPrivilegeEnum iPrivilege,
        VARIANT_BOOL bIsEnabled,
        ISWbemPrivilege **objWbemPrivilege);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ISWbemPrivilegeSet *This,
        WbemPrivilegeEnum iPrivilege);

    HRESULT (STDMETHODCALLTYPE *DeleteAll)(
        ISWbemPrivilegeSet *This);

    HRESULT (STDMETHODCALLTYPE *AddAsString)(
        ISWbemPrivilegeSet *This,
        BSTR strPrivilege,
        VARIANT_BOOL bIsEnabled,
        ISWbemPrivilege **objWbemPrivilege);

    END_INTERFACE
} ISWbemPrivilegeSetVtbl;

interface ISWbemPrivilegeSet {
    CONST_VTBL ISWbemPrivilegeSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemPrivilegeSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemPrivilegeSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemPrivilegeSet_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemPrivilegeSet_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemPrivilegeSet_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemPrivilegeSet_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemPrivilegeSet_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemPrivilegeSet methods ***/
#define ISWbemPrivilegeSet_get__NewEnum(This,pUnk) (This)->lpVtbl->get__NewEnum(This,pUnk)
#define ISWbemPrivilegeSet_Item(This,iPrivilege,objWbemPrivilege) (This)->lpVtbl->Item(This,iPrivilege,objWbemPrivilege)
#define ISWbemPrivilegeSet_get_Count(This,iCount) (This)->lpVtbl->get_Count(This,iCount)
#define ISWbemPrivilegeSet_Add(This,iPrivilege,bIsEnabled,objWbemPrivilege) (This)->lpVtbl->Add(This,iPrivilege,bIsEnabled,objWbemPrivilege)
#define ISWbemPrivilegeSet_Remove(This,iPrivilege) (This)->lpVtbl->Remove(This,iPrivilege)
#define ISWbemPrivilegeSet_DeleteAll(This) (This)->lpVtbl->DeleteAll(This)
#define ISWbemPrivilegeSet_AddAsString(This,strPrivilege,bIsEnabled,objWbemPrivilege) (This)->lpVtbl->AddAsString(This,strPrivilege,bIsEnabled,objWbemPrivilege)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemPrivilegeSet_QueryInterface(ISWbemPrivilegeSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemPrivilegeSet_AddRef(ISWbemPrivilegeSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemPrivilegeSet_Release(ISWbemPrivilegeSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemPrivilegeSet_GetTypeInfoCount(ISWbemPrivilegeSet* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemPrivilegeSet_GetTypeInfo(ISWbemPrivilegeSet* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemPrivilegeSet_GetIDsOfNames(ISWbemPrivilegeSet* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemPrivilegeSet_Invoke(ISWbemPrivilegeSet* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemPrivilegeSet methods ***/
static inline HRESULT ISWbemPrivilegeSet_get__NewEnum(ISWbemPrivilegeSet* This,IUnknown **pUnk) {
    return This->lpVtbl->get__NewEnum(This,pUnk);
}
static inline HRESULT ISWbemPrivilegeSet_Item(ISWbemPrivilegeSet* This,WbemPrivilegeEnum iPrivilege,ISWbemPrivilege **objWbemPrivilege) {
    return This->lpVtbl->Item(This,iPrivilege,objWbemPrivilege);
}
static inline HRESULT ISWbemPrivilegeSet_get_Count(ISWbemPrivilegeSet* This,LONG *iCount) {
    return This->lpVtbl->get_Count(This,iCount);
}
static inline HRESULT ISWbemPrivilegeSet_Add(ISWbemPrivilegeSet* This,WbemPrivilegeEnum iPrivilege,VARIANT_BOOL bIsEnabled,ISWbemPrivilege **objWbemPrivilege) {
    return This->lpVtbl->Add(This,iPrivilege,bIsEnabled,objWbemPrivilege);
}
static inline HRESULT ISWbemPrivilegeSet_Remove(ISWbemPrivilegeSet* This,WbemPrivilegeEnum iPrivilege) {
    return This->lpVtbl->Remove(This,iPrivilege);
}
static inline HRESULT ISWbemPrivilegeSet_DeleteAll(ISWbemPrivilegeSet* This) {
    return This->lpVtbl->DeleteAll(This);
}
static inline HRESULT ISWbemPrivilegeSet_AddAsString(ISWbemPrivilegeSet* This,BSTR strPrivilege,VARIANT_BOOL bIsEnabled,ISWbemPrivilege **objWbemPrivilege) {
    return This->lpVtbl->AddAsString(This,strPrivilege,bIsEnabled,objWbemPrivilege);
}
#endif
#endif

#endif


#endif  /* __ISWbemPrivilegeSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemDateTime interface
 */
#ifndef __ISWbemDateTime_INTERFACE_DEFINED__
#define __ISWbemDateTime_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemDateTime, 0x5e97458a, 0xcf77, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5e97458a-cf77-11d3-b38f-00105a1f473a")
ISWbemDateTime : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Value(
        BSTR *strValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Value(
        BSTR strValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Year(
        LONG *iYear) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Year(
        LONG iYear) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_YearSpecified(
        VARIANT_BOOL *bYearSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_YearSpecified(
        VARIANT_BOOL bYearSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Month(
        LONG *iMonth) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Month(
        LONG iMonth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MonthSpecified(
        VARIANT_BOOL *bMonthSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MonthSpecified(
        VARIANT_BOOL bMonthSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Day(
        LONG *iDay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Day(
        LONG iDay) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DaySpecified(
        VARIANT_BOOL *bDaySpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DaySpecified(
        VARIANT_BOOL bDaySpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Hours(
        LONG *iHours) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Hours(
        LONG iHours) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HoursSpecified(
        VARIANT_BOOL *bHoursSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_HoursSpecified(
        VARIANT_BOOL bHoursSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Minutes(
        LONG *iMinutes) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Minutes(
        LONG iMinutes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MinutesSpecified(
        VARIANT_BOOL *bMinutesSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MinutesSpecified(
        VARIANT_BOOL bMinutesSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Seconds(
        LONG *iSeconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Seconds(
        LONG iSeconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SecondsSpecified(
        VARIANT_BOOL *bSecondsSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SecondsSpecified(
        VARIANT_BOOL bSecondsSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Microseconds(
        LONG *iMicroseconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Microseconds(
        LONG iMicroseconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MicrosecondsSpecified(
        VARIANT_BOOL *bMicrosecondsSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MicrosecondsSpecified(
        VARIANT_BOOL bMicrosecondsSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UTC(
        LONG *iUTC) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UTC(
        LONG iUTC) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UTCSpecified(
        VARIANT_BOOL *bUTCSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UTCSpecified(
        VARIANT_BOOL bUTCSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsInterval(
        VARIANT_BOOL *bIsInterval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IsInterval(
        VARIANT_BOOL bIsInterval) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVarDate(
        VARIANT_BOOL bIsLocal = TRUE,
        DATE *dVarDate = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVarDate(
        DATE dVarDate,
        VARIANT_BOOL bIsLocal = TRUE) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileTime(
        VARIANT_BOOL bIsLocal = TRUE,
        BSTR *strFileTime = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFileTime(
        BSTR strFileTime,
        VARIANT_BOOL bIsLocal = TRUE) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemDateTime, 0x5e97458a, 0xcf77, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a)
#endif
#else
typedef struct ISWbemDateTimeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemDateTime *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemDateTime *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemDateTime *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemDateTime *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemDateTime *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemDateTime *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemDateTime *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemDateTime methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        ISWbemDateTime *This,
        BSTR *strValue);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        ISWbemDateTime *This,
        BSTR strValue);

    HRESULT (STDMETHODCALLTYPE *get_Year)(
        ISWbemDateTime *This,
        LONG *iYear);

    HRESULT (STDMETHODCALLTYPE *put_Year)(
        ISWbemDateTime *This,
        LONG iYear);

    HRESULT (STDMETHODCALLTYPE *get_YearSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bYearSpecified);

    HRESULT (STDMETHODCALLTYPE *put_YearSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL bYearSpecified);

    HRESULT (STDMETHODCALLTYPE *get_Month)(
        ISWbemDateTime *This,
        LONG *iMonth);

    HRESULT (STDMETHODCALLTYPE *put_Month)(
        ISWbemDateTime *This,
        LONG iMonth);

    HRESULT (STDMETHODCALLTYPE *get_MonthSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bMonthSpecified);

    HRESULT (STDMETHODCALLTYPE *put_MonthSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL bMonthSpecified);

    HRESULT (STDMETHODCALLTYPE *get_Day)(
        ISWbemDateTime *This,
        LONG *iDay);

    HRESULT (STDMETHODCALLTYPE *put_Day)(
        ISWbemDateTime *This,
        LONG iDay);

    HRESULT (STDMETHODCALLTYPE *get_DaySpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bDaySpecified);

    HRESULT (STDMETHODCALLTYPE *put_DaySpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL bDaySpecified);

    HRESULT (STDMETHODCALLTYPE *get_Hours)(
        ISWbemDateTime *This,
        LONG *iHours);

    HRESULT (STDMETHODCALLTYPE *put_Hours)(
        ISWbemDateTime *This,
        LONG iHours);

    HRESULT (STDMETHODCALLTYPE *get_HoursSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bHoursSpecified);

    HRESULT (STDMETHODCALLTYPE *put_HoursSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL bHoursSpecified);

    HRESULT (STDMETHODCALLTYPE *get_Minutes)(
        ISWbemDateTime *This,
        LONG *iMinutes);

    HRESULT (STDMETHODCALLTYPE *put_Minutes)(
        ISWbemDateTime *This,
        LONG iMinutes);

    HRESULT (STDMETHODCALLTYPE *get_MinutesSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bMinutesSpecified);

    HRESULT (STDMETHODCALLTYPE *put_MinutesSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL bMinutesSpecified);

    HRESULT (STDMETHODCALLTYPE *get_Seconds)(
        ISWbemDateTime *This,
        LONG *iSeconds);

    HRESULT (STDMETHODCALLTYPE *put_Seconds)(
        ISWbemDateTime *This,
        LONG iSeconds);

    HRESULT (STDMETHODCALLTYPE *get_SecondsSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bSecondsSpecified);

    HRESULT (STDMETHODCALLTYPE *put_SecondsSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL bSecondsSpecified);

    HRESULT (STDMETHODCALLTYPE *get_Microseconds)(
        ISWbemDateTime *This,
        LONG *iMicroseconds);

    HRESULT (STDMETHODCALLTYPE *put_Microseconds)(
        ISWbemDateTime *This,
        LONG iMicroseconds);

    HRESULT (STDMETHODCALLTYPE *get_MicrosecondsSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bMicrosecondsSpecified);

    HRESULT (STDMETHODCALLTYPE *put_MicrosecondsSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL bMicrosecondsSpecified);

    HRESULT (STDMETHODCALLTYPE *get_UTC)(
        ISWbemDateTime *This,
        LONG *iUTC);

    HRESULT (STDMETHODCALLTYPE *put_UTC)(
        ISWbemDateTime *This,
        LONG iUTC);

    HRESULT (STDMETHODCALLTYPE *get_UTCSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bUTCSpecified);

    HRESULT (STDMETHODCALLTYPE *put_UTCSpecified)(
        ISWbemDateTime *This,
        VARIANT_BOOL bUTCSpecified);

    HRESULT (STDMETHODCALLTYPE *get_IsInterval)(
        ISWbemDateTime *This,
        VARIANT_BOOL *bIsInterval);

    HRESULT (STDMETHODCALLTYPE *put_IsInterval)(
        ISWbemDateTime *This,
        VARIANT_BOOL bIsInterval);

    HRESULT (STDMETHODCALLTYPE *GetVarDate)(
        ISWbemDateTime *This,
        VARIANT_BOOL bIsLocal,
        DATE *dVarDate);

    HRESULT (STDMETHODCALLTYPE *SetVarDate)(
        ISWbemDateTime *This,
        DATE dVarDate,
        VARIANT_BOOL bIsLocal);

    HRESULT (STDMETHODCALLTYPE *GetFileTime)(
        ISWbemDateTime *This,
        VARIANT_BOOL bIsLocal,
        BSTR *strFileTime);

    HRESULT (STDMETHODCALLTYPE *SetFileTime)(
        ISWbemDateTime *This,
        BSTR strFileTime,
        VARIANT_BOOL bIsLocal);

    END_INTERFACE
} ISWbemDateTimeVtbl;

interface ISWbemDateTime {
    CONST_VTBL ISWbemDateTimeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemDateTime_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemDateTime_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemDateTime_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemDateTime_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemDateTime_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemDateTime_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemDateTime_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemDateTime methods ***/
#define ISWbemDateTime_get_Value(This,strValue) (This)->lpVtbl->get_Value(This,strValue)
#define ISWbemDateTime_put_Value(This,strValue) (This)->lpVtbl->put_Value(This,strValue)
#define ISWbemDateTime_get_Year(This,iYear) (This)->lpVtbl->get_Year(This,iYear)
#define ISWbemDateTime_put_Year(This,iYear) (This)->lpVtbl->put_Year(This,iYear)
#define ISWbemDateTime_get_YearSpecified(This,bYearSpecified) (This)->lpVtbl->get_YearSpecified(This,bYearSpecified)
#define ISWbemDateTime_put_YearSpecified(This,bYearSpecified) (This)->lpVtbl->put_YearSpecified(This,bYearSpecified)
#define ISWbemDateTime_get_Month(This,iMonth) (This)->lpVtbl->get_Month(This,iMonth)
#define ISWbemDateTime_put_Month(This,iMonth) (This)->lpVtbl->put_Month(This,iMonth)
#define ISWbemDateTime_get_MonthSpecified(This,bMonthSpecified) (This)->lpVtbl->get_MonthSpecified(This,bMonthSpecified)
#define ISWbemDateTime_put_MonthSpecified(This,bMonthSpecified) (This)->lpVtbl->put_MonthSpecified(This,bMonthSpecified)
#define ISWbemDateTime_get_Day(This,iDay) (This)->lpVtbl->get_Day(This,iDay)
#define ISWbemDateTime_put_Day(This,iDay) (This)->lpVtbl->put_Day(This,iDay)
#define ISWbemDateTime_get_DaySpecified(This,bDaySpecified) (This)->lpVtbl->get_DaySpecified(This,bDaySpecified)
#define ISWbemDateTime_put_DaySpecified(This,bDaySpecified) (This)->lpVtbl->put_DaySpecified(This,bDaySpecified)
#define ISWbemDateTime_get_Hours(This,iHours) (This)->lpVtbl->get_Hours(This,iHours)
#define ISWbemDateTime_put_Hours(This,iHours) (This)->lpVtbl->put_Hours(This,iHours)
#define ISWbemDateTime_get_HoursSpecified(This,bHoursSpecified) (This)->lpVtbl->get_HoursSpecified(This,bHoursSpecified)
#define ISWbemDateTime_put_HoursSpecified(This,bHoursSpecified) (This)->lpVtbl->put_HoursSpecified(This,bHoursSpecified)
#define ISWbemDateTime_get_Minutes(This,iMinutes) (This)->lpVtbl->get_Minutes(This,iMinutes)
#define ISWbemDateTime_put_Minutes(This,iMinutes) (This)->lpVtbl->put_Minutes(This,iMinutes)
#define ISWbemDateTime_get_MinutesSpecified(This,bMinutesSpecified) (This)->lpVtbl->get_MinutesSpecified(This,bMinutesSpecified)
#define ISWbemDateTime_put_MinutesSpecified(This,bMinutesSpecified) (This)->lpVtbl->put_MinutesSpecified(This,bMinutesSpecified)
#define ISWbemDateTime_get_Seconds(This,iSeconds) (This)->lpVtbl->get_Seconds(This,iSeconds)
#define ISWbemDateTime_put_Seconds(This,iSeconds) (This)->lpVtbl->put_Seconds(This,iSeconds)
#define ISWbemDateTime_get_SecondsSpecified(This,bSecondsSpecified) (This)->lpVtbl->get_SecondsSpecified(This,bSecondsSpecified)
#define ISWbemDateTime_put_SecondsSpecified(This,bSecondsSpecified) (This)->lpVtbl->put_SecondsSpecified(This,bSecondsSpecified)
#define ISWbemDateTime_get_Microseconds(This,iMicroseconds) (This)->lpVtbl->get_Microseconds(This,iMicroseconds)
#define ISWbemDateTime_put_Microseconds(This,iMicroseconds) (This)->lpVtbl->put_Microseconds(This,iMicroseconds)
#define ISWbemDateTime_get_MicrosecondsSpecified(This,bMicrosecondsSpecified) (This)->lpVtbl->get_MicrosecondsSpecified(This,bMicrosecondsSpecified)
#define ISWbemDateTime_put_MicrosecondsSpecified(This,bMicrosecondsSpecified) (This)->lpVtbl->put_MicrosecondsSpecified(This,bMicrosecondsSpecified)
#define ISWbemDateTime_get_UTC(This,iUTC) (This)->lpVtbl->get_UTC(This,iUTC)
#define ISWbemDateTime_put_UTC(This,iUTC) (This)->lpVtbl->put_UTC(This,iUTC)
#define ISWbemDateTime_get_UTCSpecified(This,bUTCSpecified) (This)->lpVtbl->get_UTCSpecified(This,bUTCSpecified)
#define ISWbemDateTime_put_UTCSpecified(This,bUTCSpecified) (This)->lpVtbl->put_UTCSpecified(This,bUTCSpecified)
#define ISWbemDateTime_get_IsInterval(This,bIsInterval) (This)->lpVtbl->get_IsInterval(This,bIsInterval)
#define ISWbemDateTime_put_IsInterval(This,bIsInterval) (This)->lpVtbl->put_IsInterval(This,bIsInterval)
#define ISWbemDateTime_GetVarDate(This,bIsLocal,dVarDate) (This)->lpVtbl->GetVarDate(This,bIsLocal,dVarDate)
#define ISWbemDateTime_SetVarDate(This,dVarDate,bIsLocal) (This)->lpVtbl->SetVarDate(This,dVarDate,bIsLocal)
#define ISWbemDateTime_GetFileTime(This,bIsLocal,strFileTime) (This)->lpVtbl->GetFileTime(This,bIsLocal,strFileTime)
#define ISWbemDateTime_SetFileTime(This,strFileTime,bIsLocal) (This)->lpVtbl->SetFileTime(This,strFileTime,bIsLocal)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemDateTime_QueryInterface(ISWbemDateTime* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemDateTime_AddRef(ISWbemDateTime* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemDateTime_Release(ISWbemDateTime* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemDateTime_GetTypeInfoCount(ISWbemDateTime* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemDateTime_GetTypeInfo(ISWbemDateTime* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemDateTime_GetIDsOfNames(ISWbemDateTime* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemDateTime_Invoke(ISWbemDateTime* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemDateTime methods ***/
static inline HRESULT ISWbemDateTime_get_Value(ISWbemDateTime* This,BSTR *strValue) {
    return This->lpVtbl->get_Value(This,strValue);
}
static inline HRESULT ISWbemDateTime_put_Value(ISWbemDateTime* This,BSTR strValue) {
    return This->lpVtbl->put_Value(This,strValue);
}
static inline HRESULT ISWbemDateTime_get_Year(ISWbemDateTime* This,LONG *iYear) {
    return This->lpVtbl->get_Year(This,iYear);
}
static inline HRESULT ISWbemDateTime_put_Year(ISWbemDateTime* This,LONG iYear) {
    return This->lpVtbl->put_Year(This,iYear);
}
static inline HRESULT ISWbemDateTime_get_YearSpecified(ISWbemDateTime* This,VARIANT_BOOL *bYearSpecified) {
    return This->lpVtbl->get_YearSpecified(This,bYearSpecified);
}
static inline HRESULT ISWbemDateTime_put_YearSpecified(ISWbemDateTime* This,VARIANT_BOOL bYearSpecified) {
    return This->lpVtbl->put_YearSpecified(This,bYearSpecified);
}
static inline HRESULT ISWbemDateTime_get_Month(ISWbemDateTime* This,LONG *iMonth) {
    return This->lpVtbl->get_Month(This,iMonth);
}
static inline HRESULT ISWbemDateTime_put_Month(ISWbemDateTime* This,LONG iMonth) {
    return This->lpVtbl->put_Month(This,iMonth);
}
static inline HRESULT ISWbemDateTime_get_MonthSpecified(ISWbemDateTime* This,VARIANT_BOOL *bMonthSpecified) {
    return This->lpVtbl->get_MonthSpecified(This,bMonthSpecified);
}
static inline HRESULT ISWbemDateTime_put_MonthSpecified(ISWbemDateTime* This,VARIANT_BOOL bMonthSpecified) {
    return This->lpVtbl->put_MonthSpecified(This,bMonthSpecified);
}
static inline HRESULT ISWbemDateTime_get_Day(ISWbemDateTime* This,LONG *iDay) {
    return This->lpVtbl->get_Day(This,iDay);
}
static inline HRESULT ISWbemDateTime_put_Day(ISWbemDateTime* This,LONG iDay) {
    return This->lpVtbl->put_Day(This,iDay);
}
static inline HRESULT ISWbemDateTime_get_DaySpecified(ISWbemDateTime* This,VARIANT_BOOL *bDaySpecified) {
    return This->lpVtbl->get_DaySpecified(This,bDaySpecified);
}
static inline HRESULT ISWbemDateTime_put_DaySpecified(ISWbemDateTime* This,VARIANT_BOOL bDaySpecified) {
    return This->lpVtbl->put_DaySpecified(This,bDaySpecified);
}
static inline HRESULT ISWbemDateTime_get_Hours(ISWbemDateTime* This,LONG *iHours) {
    return This->lpVtbl->get_Hours(This,iHours);
}
static inline HRESULT ISWbemDateTime_put_Hours(ISWbemDateTime* This,LONG iHours) {
    return This->lpVtbl->put_Hours(This,iHours);
}
static inline HRESULT ISWbemDateTime_get_HoursSpecified(ISWbemDateTime* This,VARIANT_BOOL *bHoursSpecified) {
    return This->lpVtbl->get_HoursSpecified(This,bHoursSpecified);
}
static inline HRESULT ISWbemDateTime_put_HoursSpecified(ISWbemDateTime* This,VARIANT_BOOL bHoursSpecified) {
    return This->lpVtbl->put_HoursSpecified(This,bHoursSpecified);
}
static inline HRESULT ISWbemDateTime_get_Minutes(ISWbemDateTime* This,LONG *iMinutes) {
    return This->lpVtbl->get_Minutes(This,iMinutes);
}
static inline HRESULT ISWbemDateTime_put_Minutes(ISWbemDateTime* This,LONG iMinutes) {
    return This->lpVtbl->put_Minutes(This,iMinutes);
}
static inline HRESULT ISWbemDateTime_get_MinutesSpecified(ISWbemDateTime* This,VARIANT_BOOL *bMinutesSpecified) {
    return This->lpVtbl->get_MinutesSpecified(This,bMinutesSpecified);
}
static inline HRESULT ISWbemDateTime_put_MinutesSpecified(ISWbemDateTime* This,VARIANT_BOOL bMinutesSpecified) {
    return This->lpVtbl->put_MinutesSpecified(This,bMinutesSpecified);
}
static inline HRESULT ISWbemDateTime_get_Seconds(ISWbemDateTime* This,LONG *iSeconds) {
    return This->lpVtbl->get_Seconds(This,iSeconds);
}
static inline HRESULT ISWbemDateTime_put_Seconds(ISWbemDateTime* This,LONG iSeconds) {
    return This->lpVtbl->put_Seconds(This,iSeconds);
}
static inline HRESULT ISWbemDateTime_get_SecondsSpecified(ISWbemDateTime* This,VARIANT_BOOL *bSecondsSpecified) {
    return This->lpVtbl->get_SecondsSpecified(This,bSecondsSpecified);
}
static inline HRESULT ISWbemDateTime_put_SecondsSpecified(ISWbemDateTime* This,VARIANT_BOOL bSecondsSpecified) {
    return This->lpVtbl->put_SecondsSpecified(This,bSecondsSpecified);
}
static inline HRESULT ISWbemDateTime_get_Microseconds(ISWbemDateTime* This,LONG *iMicroseconds) {
    return This->lpVtbl->get_Microseconds(This,iMicroseconds);
}
static inline HRESULT ISWbemDateTime_put_Microseconds(ISWbemDateTime* This,LONG iMicroseconds) {
    return This->lpVtbl->put_Microseconds(This,iMicroseconds);
}
static inline HRESULT ISWbemDateTime_get_MicrosecondsSpecified(ISWbemDateTime* This,VARIANT_BOOL *bMicrosecondsSpecified) {
    return This->lpVtbl->get_MicrosecondsSpecified(This,bMicrosecondsSpecified);
}
static inline HRESULT ISWbemDateTime_put_MicrosecondsSpecified(ISWbemDateTime* This,VARIANT_BOOL bMicrosecondsSpecified) {
    return This->lpVtbl->put_MicrosecondsSpecified(This,bMicrosecondsSpecified);
}
static inline HRESULT ISWbemDateTime_get_UTC(ISWbemDateTime* This,LONG *iUTC) {
    return This->lpVtbl->get_UTC(This,iUTC);
}
static inline HRESULT ISWbemDateTime_put_UTC(ISWbemDateTime* This,LONG iUTC) {
    return This->lpVtbl->put_UTC(This,iUTC);
}
static inline HRESULT ISWbemDateTime_get_UTCSpecified(ISWbemDateTime* This,VARIANT_BOOL *bUTCSpecified) {
    return This->lpVtbl->get_UTCSpecified(This,bUTCSpecified);
}
static inline HRESULT ISWbemDateTime_put_UTCSpecified(ISWbemDateTime* This,VARIANT_BOOL bUTCSpecified) {
    return This->lpVtbl->put_UTCSpecified(This,bUTCSpecified);
}
static inline HRESULT ISWbemDateTime_get_IsInterval(ISWbemDateTime* This,VARIANT_BOOL *bIsInterval) {
    return This->lpVtbl->get_IsInterval(This,bIsInterval);
}
static inline HRESULT ISWbemDateTime_put_IsInterval(ISWbemDateTime* This,VARIANT_BOOL bIsInterval) {
    return This->lpVtbl->put_IsInterval(This,bIsInterval);
}
static inline HRESULT ISWbemDateTime_GetVarDate(ISWbemDateTime* This,VARIANT_BOOL bIsLocal,DATE *dVarDate) {
    return This->lpVtbl->GetVarDate(This,bIsLocal,dVarDate);
}
static inline HRESULT ISWbemDateTime_SetVarDate(ISWbemDateTime* This,DATE dVarDate,VARIANT_BOOL bIsLocal) {
    return This->lpVtbl->SetVarDate(This,dVarDate,bIsLocal);
}
static inline HRESULT ISWbemDateTime_GetFileTime(ISWbemDateTime* This,VARIANT_BOOL bIsLocal,BSTR *strFileTime) {
    return This->lpVtbl->GetFileTime(This,bIsLocal,strFileTime);
}
static inline HRESULT ISWbemDateTime_SetFileTime(ISWbemDateTime* This,BSTR strFileTime,VARIANT_BOOL bIsLocal) {
    return This->lpVtbl->SetFileTime(This,strFileTime,bIsLocal);
}
#endif
#endif

#endif


#endif  /* __ISWbemDateTime_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemRefreshableItem interface
 */
#ifndef __ISWbemRefreshableItem_INTERFACE_DEFINED__
#define __ISWbemRefreshableItem_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemRefreshableItem, 0x5ad4bf92, 0xdaab, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5ad4bf92-daab-11d3-b38f-00105a1f473a")
ISWbemRefreshableItem : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Index(
        LONG *iIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Refresher(
        ISWbemRefresher **objWbemRefresher) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsSet(
        VARIANT_BOOL *bIsSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Object(
        ISWbemObjectEx **objWbemObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ObjectSet(
        ISWbemObjectSet **objWbemObjectSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        LONG iFlags = 0) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemRefreshableItem, 0x5ad4bf92, 0xdaab, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a)
#endif
#else
typedef struct ISWbemRefreshableItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemRefreshableItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemRefreshableItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemRefreshableItem *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemRefreshableItem *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemRefreshableItem *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemRefreshableItem *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemRefreshableItem *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemRefreshableItem methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Index)(
        ISWbemRefreshableItem *This,
        LONG *iIndex);

    HRESULT (STDMETHODCALLTYPE *get_Refresher)(
        ISWbemRefreshableItem *This,
        ISWbemRefresher **objWbemRefresher);

    HRESULT (STDMETHODCALLTYPE *get_IsSet)(
        ISWbemRefreshableItem *This,
        VARIANT_BOOL *bIsSet);

    HRESULT (STDMETHODCALLTYPE *get_Object)(
        ISWbemRefreshableItem *This,
        ISWbemObjectEx **objWbemObject);

    HRESULT (STDMETHODCALLTYPE *get_ObjectSet)(
        ISWbemRefreshableItem *This,
        ISWbemObjectSet **objWbemObjectSet);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ISWbemRefreshableItem *This,
        LONG iFlags);

    END_INTERFACE
} ISWbemRefreshableItemVtbl;

interface ISWbemRefreshableItem {
    CONST_VTBL ISWbemRefreshableItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemRefreshableItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemRefreshableItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemRefreshableItem_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemRefreshableItem_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemRefreshableItem_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemRefreshableItem_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemRefreshableItem_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemRefreshableItem methods ***/
#define ISWbemRefreshableItem_get_Index(This,iIndex) (This)->lpVtbl->get_Index(This,iIndex)
#define ISWbemRefreshableItem_get_Refresher(This,objWbemRefresher) (This)->lpVtbl->get_Refresher(This,objWbemRefresher)
#define ISWbemRefreshableItem_get_IsSet(This,bIsSet) (This)->lpVtbl->get_IsSet(This,bIsSet)
#define ISWbemRefreshableItem_get_Object(This,objWbemObject) (This)->lpVtbl->get_Object(This,objWbemObject)
#define ISWbemRefreshableItem_get_ObjectSet(This,objWbemObjectSet) (This)->lpVtbl->get_ObjectSet(This,objWbemObjectSet)
#define ISWbemRefreshableItem_Remove(This,iFlags) (This)->lpVtbl->Remove(This,iFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemRefreshableItem_QueryInterface(ISWbemRefreshableItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemRefreshableItem_AddRef(ISWbemRefreshableItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemRefreshableItem_Release(ISWbemRefreshableItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemRefreshableItem_GetTypeInfoCount(ISWbemRefreshableItem* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemRefreshableItem_GetTypeInfo(ISWbemRefreshableItem* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemRefreshableItem_GetIDsOfNames(ISWbemRefreshableItem* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemRefreshableItem_Invoke(ISWbemRefreshableItem* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemRefreshableItem methods ***/
static inline HRESULT ISWbemRefreshableItem_get_Index(ISWbemRefreshableItem* This,LONG *iIndex) {
    return This->lpVtbl->get_Index(This,iIndex);
}
static inline HRESULT ISWbemRefreshableItem_get_Refresher(ISWbemRefreshableItem* This,ISWbemRefresher **objWbemRefresher) {
    return This->lpVtbl->get_Refresher(This,objWbemRefresher);
}
static inline HRESULT ISWbemRefreshableItem_get_IsSet(ISWbemRefreshableItem* This,VARIANT_BOOL *bIsSet) {
    return This->lpVtbl->get_IsSet(This,bIsSet);
}
static inline HRESULT ISWbemRefreshableItem_get_Object(ISWbemRefreshableItem* This,ISWbemObjectEx **objWbemObject) {
    return This->lpVtbl->get_Object(This,objWbemObject);
}
static inline HRESULT ISWbemRefreshableItem_get_ObjectSet(ISWbemRefreshableItem* This,ISWbemObjectSet **objWbemObjectSet) {
    return This->lpVtbl->get_ObjectSet(This,objWbemObjectSet);
}
static inline HRESULT ISWbemRefreshableItem_Remove(ISWbemRefreshableItem* This,LONG iFlags) {
    return This->lpVtbl->Remove(This,iFlags);
}
#endif
#endif

#endif


#endif  /* __ISWbemRefreshableItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISWbemRefresher interface
 */
#ifndef __ISWbemRefresher_INTERFACE_DEFINED__
#define __ISWbemRefresher_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISWbemRefresher, 0x14d8250e, 0xd9c2, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("14d8250e-d9c2-11d3-b38f-00105a1f473a")
ISWbemRefresher : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG iIndex,
        ISWbemRefreshableItem **objWbemRefreshableItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *iCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        ISWbemServicesEx *objWbemServices,
        BSTR bsInstancePath,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemRefreshableItem **objWbemRefreshableItem = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddEnum(
        ISWbemServicesEx *objWbemServices,
        BSTR bsClassName,
        LONG iFlags = 0,
        IDispatch *objWbemNamedValueSet = 0,
        ISWbemRefreshableItem **objWbemRefreshableItem = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        LONG iIndex,
        LONG iFlags = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Refresh(
        LONG iFlags = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AutoReconnect(
        VARIANT_BOOL *bCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AutoReconnect(
        VARIANT_BOOL bCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteAll(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISWbemRefresher, 0x14d8250e, 0xd9c2, 0x11d3, 0xb3,0x8f, 0x00,0x10,0x5a,0x1f,0x47,0x3a)
#endif
#else
typedef struct ISWbemRefresherVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISWbemRefresher *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISWbemRefresher *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISWbemRefresher *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISWbemRefresher *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISWbemRefresher *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISWbemRefresher *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISWbemRefresher *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISWbemRefresher methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ISWbemRefresher *This,
        IUnknown **pUnk);

    HRESULT (STDMETHODCALLTYPE *Item)(
        ISWbemRefresher *This,
        LONG iIndex,
        ISWbemRefreshableItem **objWbemRefreshableItem);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ISWbemRefresher *This,
        LONG *iCount);

    HRESULT (STDMETHODCALLTYPE *Add)(
        ISWbemRefresher *This,
        ISWbemServicesEx *objWbemServices,
        BSTR bsInstancePath,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemRefreshableItem **objWbemRefreshableItem);

    HRESULT (STDMETHODCALLTYPE *AddEnum)(
        ISWbemRefresher *This,
        ISWbemServicesEx *objWbemServices,
        BSTR bsClassName,
        LONG iFlags,
        IDispatch *objWbemNamedValueSet,
        ISWbemRefreshableItem **objWbemRefreshableItem);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ISWbemRefresher *This,
        LONG iIndex,
        LONG iFlags);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        ISWbemRefresher *This,
        LONG iFlags);

    HRESULT (STDMETHODCALLTYPE *get_AutoReconnect)(
        ISWbemRefresher *This,
        VARIANT_BOOL *bCount);

    HRESULT (STDMETHODCALLTYPE *put_AutoReconnect)(
        ISWbemRefresher *This,
        VARIANT_BOOL bCount);

    HRESULT (STDMETHODCALLTYPE *DeleteAll)(
        ISWbemRefresher *This);

    END_INTERFACE
} ISWbemRefresherVtbl;

interface ISWbemRefresher {
    CONST_VTBL ISWbemRefresherVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISWbemRefresher_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISWbemRefresher_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISWbemRefresher_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISWbemRefresher_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISWbemRefresher_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISWbemRefresher_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISWbemRefresher_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISWbemRefresher methods ***/
#define ISWbemRefresher_get__NewEnum(This,pUnk) (This)->lpVtbl->get__NewEnum(This,pUnk)
#define ISWbemRefresher_Item(This,iIndex,objWbemRefreshableItem) (This)->lpVtbl->Item(This,iIndex,objWbemRefreshableItem)
#define ISWbemRefresher_get_Count(This,iCount) (This)->lpVtbl->get_Count(This,iCount)
#define ISWbemRefresher_Add(This,objWbemServices,bsInstancePath,iFlags,objWbemNamedValueSet,objWbemRefreshableItem) (This)->lpVtbl->Add(This,objWbemServices,bsInstancePath,iFlags,objWbemNamedValueSet,objWbemRefreshableItem)
#define ISWbemRefresher_AddEnum(This,objWbemServices,bsClassName,iFlags,objWbemNamedValueSet,objWbemRefreshableItem) (This)->lpVtbl->AddEnum(This,objWbemServices,bsClassName,iFlags,objWbemNamedValueSet,objWbemRefreshableItem)
#define ISWbemRefresher_Remove(This,iIndex,iFlags) (This)->lpVtbl->Remove(This,iIndex,iFlags)
#define ISWbemRefresher_Refresh(This,iFlags) (This)->lpVtbl->Refresh(This,iFlags)
#define ISWbemRefresher_get_AutoReconnect(This,bCount) (This)->lpVtbl->get_AutoReconnect(This,bCount)
#define ISWbemRefresher_put_AutoReconnect(This,bCount) (This)->lpVtbl->put_AutoReconnect(This,bCount)
#define ISWbemRefresher_DeleteAll(This) (This)->lpVtbl->DeleteAll(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISWbemRefresher_QueryInterface(ISWbemRefresher* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISWbemRefresher_AddRef(ISWbemRefresher* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISWbemRefresher_Release(ISWbemRefresher* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISWbemRefresher_GetTypeInfoCount(ISWbemRefresher* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISWbemRefresher_GetTypeInfo(ISWbemRefresher* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISWbemRefresher_GetIDsOfNames(ISWbemRefresher* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISWbemRefresher_Invoke(ISWbemRefresher* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISWbemRefresher methods ***/
static inline HRESULT ISWbemRefresher_get__NewEnum(ISWbemRefresher* This,IUnknown **pUnk) {
    return This->lpVtbl->get__NewEnum(This,pUnk);
}
static inline HRESULT ISWbemRefresher_Item(ISWbemRefresher* This,LONG iIndex,ISWbemRefreshableItem **objWbemRefreshableItem) {
    return This->lpVtbl->Item(This,iIndex,objWbemRefreshableItem);
}
static inline HRESULT ISWbemRefresher_get_Count(ISWbemRefresher* This,LONG *iCount) {
    return This->lpVtbl->get_Count(This,iCount);
}
static inline HRESULT ISWbemRefresher_Add(ISWbemRefresher* This,ISWbemServicesEx *objWbemServices,BSTR bsInstancePath,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemRefreshableItem **objWbemRefreshableItem) {
    return This->lpVtbl->Add(This,objWbemServices,bsInstancePath,iFlags,objWbemNamedValueSet,objWbemRefreshableItem);
}
static inline HRESULT ISWbemRefresher_AddEnum(ISWbemRefresher* This,ISWbemServicesEx *objWbemServices,BSTR bsClassName,LONG iFlags,IDispatch *objWbemNamedValueSet,ISWbemRefreshableItem **objWbemRefreshableItem) {
    return This->lpVtbl->AddEnum(This,objWbemServices,bsClassName,iFlags,objWbemNamedValueSet,objWbemRefreshableItem);
}
static inline HRESULT ISWbemRefresher_Remove(ISWbemRefresher* This,LONG iIndex,LONG iFlags) {
    return This->lpVtbl->Remove(This,iIndex,iFlags);
}
static inline HRESULT ISWbemRefresher_Refresh(ISWbemRefresher* This,LONG iFlags) {
    return This->lpVtbl->Refresh(This,iFlags);
}
static inline HRESULT ISWbemRefresher_get_AutoReconnect(ISWbemRefresher* This,VARIANT_BOOL *bCount) {
    return This->lpVtbl->get_AutoReconnect(This,bCount);
}
static inline HRESULT ISWbemRefresher_put_AutoReconnect(ISWbemRefresher* This,VARIANT_BOOL bCount) {
    return This->lpVtbl->put_AutoReconnect(This,bCount);
}
static inline HRESULT ISWbemRefresher_DeleteAll(ISWbemRefresher* This) {
    return This->lpVtbl->DeleteAll(This);
}
#endif
#endif

#endif


#endif  /* __ISWbemRefresher_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wbemdisp_h__ */
