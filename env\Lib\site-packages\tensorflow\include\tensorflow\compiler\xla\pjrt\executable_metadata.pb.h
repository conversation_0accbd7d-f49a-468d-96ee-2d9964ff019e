// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/pjrt/executable_metadata.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fexecutable_5fmetadata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fexecutable_5fmetadata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "xla/service/hlo.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fpjrt_2fexecutable_5fmetadata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fpjrt_2fexecutable_5fmetadata_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fpjrt_2fexecutable_5fmetadata_2eproto;
namespace xla {
class CompiledMemoryStatsProto;
struct CompiledMemoryStatsProtoDefaultTypeInternal;
extern CompiledMemoryStatsProtoDefaultTypeInternal _CompiledMemoryStatsProto_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::CompiledMemoryStatsProto* Arena::CreateMaybeMessage<::xla::CompiledMemoryStatsProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

// ===================================================================

class CompiledMemoryStatsProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CompiledMemoryStatsProto) */ {
 public:
  inline CompiledMemoryStatsProto() : CompiledMemoryStatsProto(nullptr) {}
  ~CompiledMemoryStatsProto() override;
  explicit PROTOBUF_CONSTEXPR CompiledMemoryStatsProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompiledMemoryStatsProto(const CompiledMemoryStatsProto& from);
  CompiledMemoryStatsProto(CompiledMemoryStatsProto&& from) noexcept
    : CompiledMemoryStatsProto() {
    *this = ::std::move(from);
  }

  inline CompiledMemoryStatsProto& operator=(const CompiledMemoryStatsProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompiledMemoryStatsProto& operator=(CompiledMemoryStatsProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompiledMemoryStatsProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompiledMemoryStatsProto* internal_default_instance() {
    return reinterpret_cast<const CompiledMemoryStatsProto*>(
               &_CompiledMemoryStatsProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CompiledMemoryStatsProto& a, CompiledMemoryStatsProto& b) {
    a.Swap(&b);
  }
  inline void Swap(CompiledMemoryStatsProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompiledMemoryStatsProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompiledMemoryStatsProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompiledMemoryStatsProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompiledMemoryStatsProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompiledMemoryStatsProto& from) {
    CompiledMemoryStatsProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompiledMemoryStatsProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CompiledMemoryStatsProto";
  }
  protected:
  explicit CompiledMemoryStatsProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHloProtoFieldNumber = 6,
    kGeneratedCodeSizeInBytesFieldNumber = 1,
    kArgumentSizeInBytesFieldNumber = 2,
    kOutputSizeInBytesFieldNumber = 3,
    kAliasSizeInBytesFieldNumber = 4,
    kTempSizeInBytesFieldNumber = 5,
    kHostGeneratedCodeSizeInBytesFieldNumber = 7,
    kHostArgumentSizeInBytesFieldNumber = 8,
    kHostOutputSizeInBytesFieldNumber = 9,
    kHostAliasSizeInBytesFieldNumber = 10,
    kHostTempSizeInBytesFieldNumber = 11,
  };
  // .xla.HloProto hlo_proto = 6;
  bool has_hlo_proto() const;
  private:
  bool _internal_has_hlo_proto() const;
  public:
  void clear_hlo_proto();
  const ::xla::HloProto& hlo_proto() const;
  PROTOBUF_NODISCARD ::xla::HloProto* release_hlo_proto();
  ::xla::HloProto* mutable_hlo_proto();
  void set_allocated_hlo_proto(::xla::HloProto* hlo_proto);
  private:
  const ::xla::HloProto& _internal_hlo_proto() const;
  ::xla::HloProto* _internal_mutable_hlo_proto();
  public:
  void unsafe_arena_set_allocated_hlo_proto(
      ::xla::HloProto* hlo_proto);
  ::xla::HloProto* unsafe_arena_release_hlo_proto();

  // int64 generated_code_size_in_bytes = 1;
  void clear_generated_code_size_in_bytes();
  int64_t generated_code_size_in_bytes() const;
  void set_generated_code_size_in_bytes(int64_t value);
  private:
  int64_t _internal_generated_code_size_in_bytes() const;
  void _internal_set_generated_code_size_in_bytes(int64_t value);
  public:

  // int64 argument_size_in_bytes = 2;
  void clear_argument_size_in_bytes();
  int64_t argument_size_in_bytes() const;
  void set_argument_size_in_bytes(int64_t value);
  private:
  int64_t _internal_argument_size_in_bytes() const;
  void _internal_set_argument_size_in_bytes(int64_t value);
  public:

  // int64 output_size_in_bytes = 3;
  void clear_output_size_in_bytes();
  int64_t output_size_in_bytes() const;
  void set_output_size_in_bytes(int64_t value);
  private:
  int64_t _internal_output_size_in_bytes() const;
  void _internal_set_output_size_in_bytes(int64_t value);
  public:

  // int64 alias_size_in_bytes = 4;
  void clear_alias_size_in_bytes();
  int64_t alias_size_in_bytes() const;
  void set_alias_size_in_bytes(int64_t value);
  private:
  int64_t _internal_alias_size_in_bytes() const;
  void _internal_set_alias_size_in_bytes(int64_t value);
  public:

  // int64 temp_size_in_bytes = 5;
  void clear_temp_size_in_bytes();
  int64_t temp_size_in_bytes() const;
  void set_temp_size_in_bytes(int64_t value);
  private:
  int64_t _internal_temp_size_in_bytes() const;
  void _internal_set_temp_size_in_bytes(int64_t value);
  public:

  // int64 host_generated_code_size_in_bytes = 7;
  void clear_host_generated_code_size_in_bytes();
  int64_t host_generated_code_size_in_bytes() const;
  void set_host_generated_code_size_in_bytes(int64_t value);
  private:
  int64_t _internal_host_generated_code_size_in_bytes() const;
  void _internal_set_host_generated_code_size_in_bytes(int64_t value);
  public:

  // int64 host_argument_size_in_bytes = 8;
  void clear_host_argument_size_in_bytes();
  int64_t host_argument_size_in_bytes() const;
  void set_host_argument_size_in_bytes(int64_t value);
  private:
  int64_t _internal_host_argument_size_in_bytes() const;
  void _internal_set_host_argument_size_in_bytes(int64_t value);
  public:

  // int64 host_output_size_in_bytes = 9;
  void clear_host_output_size_in_bytes();
  int64_t host_output_size_in_bytes() const;
  void set_host_output_size_in_bytes(int64_t value);
  private:
  int64_t _internal_host_output_size_in_bytes() const;
  void _internal_set_host_output_size_in_bytes(int64_t value);
  public:

  // int64 host_alias_size_in_bytes = 10;
  void clear_host_alias_size_in_bytes();
  int64_t host_alias_size_in_bytes() const;
  void set_host_alias_size_in_bytes(int64_t value);
  private:
  int64_t _internal_host_alias_size_in_bytes() const;
  void _internal_set_host_alias_size_in_bytes(int64_t value);
  public:

  // int64 host_temp_size_in_bytes = 11;
  void clear_host_temp_size_in_bytes();
  int64_t host_temp_size_in_bytes() const;
  void set_host_temp_size_in_bytes(int64_t value);
  private:
  int64_t _internal_host_temp_size_in_bytes() const;
  void _internal_set_host_temp_size_in_bytes(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.CompiledMemoryStatsProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::xla::HloProto* hlo_proto_;
    int64_t generated_code_size_in_bytes_;
    int64_t argument_size_in_bytes_;
    int64_t output_size_in_bytes_;
    int64_t alias_size_in_bytes_;
    int64_t temp_size_in_bytes_;
    int64_t host_generated_code_size_in_bytes_;
    int64_t host_argument_size_in_bytes_;
    int64_t host_output_size_in_bytes_;
    int64_t host_alias_size_in_bytes_;
    int64_t host_temp_size_in_bytes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fpjrt_2fexecutable_5fmetadata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CompiledMemoryStatsProto

// int64 generated_code_size_in_bytes = 1;
inline void CompiledMemoryStatsProto::clear_generated_code_size_in_bytes() {
  _impl_.generated_code_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_generated_code_size_in_bytes() const {
  return _impl_.generated_code_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::generated_code_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.generated_code_size_in_bytes)
  return _internal_generated_code_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_generated_code_size_in_bytes(int64_t value) {
  
  _impl_.generated_code_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_generated_code_size_in_bytes(int64_t value) {
  _internal_set_generated_code_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.generated_code_size_in_bytes)
}

// int64 argument_size_in_bytes = 2;
inline void CompiledMemoryStatsProto::clear_argument_size_in_bytes() {
  _impl_.argument_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_argument_size_in_bytes() const {
  return _impl_.argument_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::argument_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.argument_size_in_bytes)
  return _internal_argument_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_argument_size_in_bytes(int64_t value) {
  
  _impl_.argument_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_argument_size_in_bytes(int64_t value) {
  _internal_set_argument_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.argument_size_in_bytes)
}

// int64 output_size_in_bytes = 3;
inline void CompiledMemoryStatsProto::clear_output_size_in_bytes() {
  _impl_.output_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_output_size_in_bytes() const {
  return _impl_.output_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::output_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.output_size_in_bytes)
  return _internal_output_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_output_size_in_bytes(int64_t value) {
  
  _impl_.output_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_output_size_in_bytes(int64_t value) {
  _internal_set_output_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.output_size_in_bytes)
}

// int64 alias_size_in_bytes = 4;
inline void CompiledMemoryStatsProto::clear_alias_size_in_bytes() {
  _impl_.alias_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_alias_size_in_bytes() const {
  return _impl_.alias_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::alias_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.alias_size_in_bytes)
  return _internal_alias_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_alias_size_in_bytes(int64_t value) {
  
  _impl_.alias_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_alias_size_in_bytes(int64_t value) {
  _internal_set_alias_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.alias_size_in_bytes)
}

// int64 temp_size_in_bytes = 5;
inline void CompiledMemoryStatsProto::clear_temp_size_in_bytes() {
  _impl_.temp_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_temp_size_in_bytes() const {
  return _impl_.temp_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::temp_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.temp_size_in_bytes)
  return _internal_temp_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_temp_size_in_bytes(int64_t value) {
  
  _impl_.temp_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_temp_size_in_bytes(int64_t value) {
  _internal_set_temp_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.temp_size_in_bytes)
}

// .xla.HloProto hlo_proto = 6;
inline bool CompiledMemoryStatsProto::_internal_has_hlo_proto() const {
  return this != internal_default_instance() && _impl_.hlo_proto_ != nullptr;
}
inline bool CompiledMemoryStatsProto::has_hlo_proto() const {
  return _internal_has_hlo_proto();
}
inline const ::xla::HloProto& CompiledMemoryStatsProto::_internal_hlo_proto() const {
  const ::xla::HloProto* p = _impl_.hlo_proto_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::HloProto&>(
      ::xla::_HloProto_default_instance_);
}
inline const ::xla::HloProto& CompiledMemoryStatsProto::hlo_proto() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.hlo_proto)
  return _internal_hlo_proto();
}
inline void CompiledMemoryStatsProto::unsafe_arena_set_allocated_hlo_proto(
    ::xla::HloProto* hlo_proto) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hlo_proto_);
  }
  _impl_.hlo_proto_ = hlo_proto;
  if (hlo_proto) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.CompiledMemoryStatsProto.hlo_proto)
}
inline ::xla::HloProto* CompiledMemoryStatsProto::release_hlo_proto() {
  
  ::xla::HloProto* temp = _impl_.hlo_proto_;
  _impl_.hlo_proto_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::HloProto* CompiledMemoryStatsProto::unsafe_arena_release_hlo_proto() {
  // @@protoc_insertion_point(field_release:xla.CompiledMemoryStatsProto.hlo_proto)
  
  ::xla::HloProto* temp = _impl_.hlo_proto_;
  _impl_.hlo_proto_ = nullptr;
  return temp;
}
inline ::xla::HloProto* CompiledMemoryStatsProto::_internal_mutable_hlo_proto() {
  
  if (_impl_.hlo_proto_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloProto>(GetArenaForAllocation());
    _impl_.hlo_proto_ = p;
  }
  return _impl_.hlo_proto_;
}
inline ::xla::HloProto* CompiledMemoryStatsProto::mutable_hlo_proto() {
  ::xla::HloProto* _msg = _internal_mutable_hlo_proto();
  // @@protoc_insertion_point(field_mutable:xla.CompiledMemoryStatsProto.hlo_proto)
  return _msg;
}
inline void CompiledMemoryStatsProto::set_allocated_hlo_proto(::xla::HloProto* hlo_proto) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hlo_proto_);
  }
  if (hlo_proto) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(hlo_proto));
    if (message_arena != submessage_arena) {
      hlo_proto = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hlo_proto, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.hlo_proto_ = hlo_proto;
  // @@protoc_insertion_point(field_set_allocated:xla.CompiledMemoryStatsProto.hlo_proto)
}

// int64 host_generated_code_size_in_bytes = 7;
inline void CompiledMemoryStatsProto::clear_host_generated_code_size_in_bytes() {
  _impl_.host_generated_code_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_host_generated_code_size_in_bytes() const {
  return _impl_.host_generated_code_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::host_generated_code_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.host_generated_code_size_in_bytes)
  return _internal_host_generated_code_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_host_generated_code_size_in_bytes(int64_t value) {
  
  _impl_.host_generated_code_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_host_generated_code_size_in_bytes(int64_t value) {
  _internal_set_host_generated_code_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.host_generated_code_size_in_bytes)
}

// int64 host_argument_size_in_bytes = 8;
inline void CompiledMemoryStatsProto::clear_host_argument_size_in_bytes() {
  _impl_.host_argument_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_host_argument_size_in_bytes() const {
  return _impl_.host_argument_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::host_argument_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.host_argument_size_in_bytes)
  return _internal_host_argument_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_host_argument_size_in_bytes(int64_t value) {
  
  _impl_.host_argument_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_host_argument_size_in_bytes(int64_t value) {
  _internal_set_host_argument_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.host_argument_size_in_bytes)
}

// int64 host_output_size_in_bytes = 9;
inline void CompiledMemoryStatsProto::clear_host_output_size_in_bytes() {
  _impl_.host_output_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_host_output_size_in_bytes() const {
  return _impl_.host_output_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::host_output_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.host_output_size_in_bytes)
  return _internal_host_output_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_host_output_size_in_bytes(int64_t value) {
  
  _impl_.host_output_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_host_output_size_in_bytes(int64_t value) {
  _internal_set_host_output_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.host_output_size_in_bytes)
}

// int64 host_alias_size_in_bytes = 10;
inline void CompiledMemoryStatsProto::clear_host_alias_size_in_bytes() {
  _impl_.host_alias_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_host_alias_size_in_bytes() const {
  return _impl_.host_alias_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::host_alias_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.host_alias_size_in_bytes)
  return _internal_host_alias_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_host_alias_size_in_bytes(int64_t value) {
  
  _impl_.host_alias_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_host_alias_size_in_bytes(int64_t value) {
  _internal_set_host_alias_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.host_alias_size_in_bytes)
}

// int64 host_temp_size_in_bytes = 11;
inline void CompiledMemoryStatsProto::clear_host_temp_size_in_bytes() {
  _impl_.host_temp_size_in_bytes_ = int64_t{0};
}
inline int64_t CompiledMemoryStatsProto::_internal_host_temp_size_in_bytes() const {
  return _impl_.host_temp_size_in_bytes_;
}
inline int64_t CompiledMemoryStatsProto::host_temp_size_in_bytes() const {
  // @@protoc_insertion_point(field_get:xla.CompiledMemoryStatsProto.host_temp_size_in_bytes)
  return _internal_host_temp_size_in_bytes();
}
inline void CompiledMemoryStatsProto::_internal_set_host_temp_size_in_bytes(int64_t value) {
  
  _impl_.host_temp_size_in_bytes_ = value;
}
inline void CompiledMemoryStatsProto::set_host_temp_size_in_bytes(int64_t value) {
  _internal_set_host_temp_size_in_bytes(value);
  // @@protoc_insertion_point(field_set:xla.CompiledMemoryStatsProto.host_temp_size_in_bytes)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fpjrt_2fexecutable_5fmetadata_2eproto
