.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_add_named_crt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_add_named_crt \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_add_named_crt(gnutls_x509_trust_list_t " list ", gnutls_x509_crt_t " cert ", const void * " name ", size_t " name_size ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "gnutls_x509_crt_t cert" 12
A certificate
.IP "const void * name" 12
An identifier for the certificate
.IP "size_t name_size" 12
The size of the identifier
.IP "unsigned int flags" 12
should be 0.
.SH "DESCRIPTION"
This function will add the given certificate to the trusted
list and associate it with a name. The certificate will not be
be used for verification with \fBgnutls_x509_trust_list_verify_crt()\fP
but with \fBgnutls_x509_trust_list_verify_named_crt()\fP or
\fBgnutls_x509_trust_list_verify_crt2()\fP \- the latter only since
GnuTLS 3.4.0 and if a hostname is provided.

In principle this function can be used to set individual "server"
certificates that are trusted by the user for that specific server
but for no other purposes.

The certificate  \fIcert\fP must not be deinitialized during the lifetime
of the  \fIlist\fP .
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.0.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
