.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_rdn_get2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_rdn_get2 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_rdn_get2(const gnutls_datum_t * " idn ", gnutls_datum_t * " str ", unsigned " flags ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * idn" 12
should contain a DER encoded RDN sequence
.IP "gnutls_datum_t * str" 12
a datum that will hold the name
.IP "unsigned flags" 12
zero of \fBGNUTLS_X509_DN_FLAG_COMPAT\fP
.SH "DESCRIPTION"
This function will return the name of the given RDN sequence.  The
name will be in the form "C=xxxx,O=yyyy,CN=zzzz" as described in
RFC4514.

When the flag \fBGNUTLS_X509_DN_FLAG_COMPAT\fP is specified, the output
format will match the format output by previous to 3.5.6 versions of GnuTLS
which was not not fully RFC4514\-compliant.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, or
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP is returned and * \fIbuf_size\fP is
updated if the provided buffer is not long enough, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
