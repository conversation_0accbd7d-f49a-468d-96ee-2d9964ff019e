// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/autotuning.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fautotuning_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fautotuning_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/duration.pb.h>
#include "xla/tsl/protobuf/dnn.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fautotuning_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fautotuning_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fautotuning_2eproto;
namespace xla {
class AutotuneResult;
struct AutotuneResultDefaultTypeInternal;
extern AutotuneResultDefaultTypeInternal _AutotuneResult_default_instance_;
class AutotuneResult_ConvKey;
struct AutotuneResult_ConvKeyDefaultTypeInternal;
extern AutotuneResult_ConvKeyDefaultTypeInternal _AutotuneResult_ConvKey_default_instance_;
class AutotuneResult_CudaConvPlanKey;
struct AutotuneResult_CudaConvPlanKeyDefaultTypeInternal;
extern AutotuneResult_CudaConvPlanKeyDefaultTypeInternal _AutotuneResult_CudaConvPlanKey_default_instance_;
class AutotuneResult_CustomKernelFusionKey;
struct AutotuneResult_CustomKernelFusionKeyDefaultTypeInternal;
extern AutotuneResult_CustomKernelFusionKeyDefaultTypeInternal _AutotuneResult_CustomKernelFusionKey_default_instance_;
class AutotuneResult_FailureResult;
struct AutotuneResult_FailureResultDefaultTypeInternal;
extern AutotuneResult_FailureResultDefaultTypeInternal _AutotuneResult_FailureResult_default_instance_;
class AutotuneResult_GemmKey;
struct AutotuneResult_GemmKeyDefaultTypeInternal;
extern AutotuneResult_GemmKeyDefaultTypeInternal _AutotuneResult_GemmKey_default_instance_;
class AutotuneResult_TritonGemmKey;
struct AutotuneResult_TritonGemmKeyDefaultTypeInternal;
extern AutotuneResult_TritonGemmKeyDefaultTypeInternal _AutotuneResult_TritonGemmKey_default_instance_;
class AutotuningLog;
struct AutotuningLogDefaultTypeInternal;
extern AutotuningLogDefaultTypeInternal _AutotuningLog_default_instance_;
class ComputeCapability;
struct ComputeCapabilityDefaultTypeInternal;
extern ComputeCapabilityDefaultTypeInternal _ComputeCapability_default_instance_;
class CudnnVersion;
struct CudnnVersionDefaultTypeInternal;
extern CudnnVersionDefaultTypeInternal _CudnnVersion_default_instance_;
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::AutotuneResult* Arena::CreateMaybeMessage<::xla::AutotuneResult>(Arena*);
template<> ::xla::AutotuneResult_ConvKey* Arena::CreateMaybeMessage<::xla::AutotuneResult_ConvKey>(Arena*);
template<> ::xla::AutotuneResult_CudaConvPlanKey* Arena::CreateMaybeMessage<::xla::AutotuneResult_CudaConvPlanKey>(Arena*);
template<> ::xla::AutotuneResult_CustomKernelFusionKey* Arena::CreateMaybeMessage<::xla::AutotuneResult_CustomKernelFusionKey>(Arena*);
template<> ::xla::AutotuneResult_FailureResult* Arena::CreateMaybeMessage<::xla::AutotuneResult_FailureResult>(Arena*);
template<> ::xla::AutotuneResult_GemmKey* Arena::CreateMaybeMessage<::xla::AutotuneResult_GemmKey>(Arena*);
template<> ::xla::AutotuneResult_TritonGemmKey* Arena::CreateMaybeMessage<::xla::AutotuneResult_TritonGemmKey>(Arena*);
template<> ::xla::AutotuningLog* Arena::CreateMaybeMessage<::xla::AutotuningLog>(Arena*);
template<> ::xla::ComputeCapability* Arena::CreateMaybeMessage<::xla::ComputeCapability>(Arena*);
template<> ::xla::CudnnVersion* Arena::CreateMaybeMessage<::xla::CudnnVersion>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {

enum AutotuneResult_FailureKind : int {
  AutotuneResult_FailureKind_UNKNOWN = 0,
  AutotuneResult_FailureKind_REDZONE_MODIFIED = 1,
  AutotuneResult_FailureKind_WRONG_RESULT = 2,
  AutotuneResult_FailureKind_DISQUALIFIED = 3,
  AutotuneResult_FailureKind_AutotuneResult_FailureKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  AutotuneResult_FailureKind_AutotuneResult_FailureKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool AutotuneResult_FailureKind_IsValid(int value);
constexpr AutotuneResult_FailureKind AutotuneResult_FailureKind_FailureKind_MIN = AutotuneResult_FailureKind_UNKNOWN;
constexpr AutotuneResult_FailureKind AutotuneResult_FailureKind_FailureKind_MAX = AutotuneResult_FailureKind_DISQUALIFIED;
constexpr int AutotuneResult_FailureKind_FailureKind_ARRAYSIZE = AutotuneResult_FailureKind_FailureKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AutotuneResult_FailureKind_descriptor();
template<typename T>
inline const std::string& AutotuneResult_FailureKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AutotuneResult_FailureKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AutotuneResult_FailureKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AutotuneResult_FailureKind_descriptor(), enum_t_value);
}
inline bool AutotuneResult_FailureKind_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AutotuneResult_FailureKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AutotuneResult_FailureKind>(
    AutotuneResult_FailureKind_descriptor(), name, value);
}
// ===================================================================

class CudnnVersion final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.CudnnVersion) */ {
 public:
  inline CudnnVersion() : CudnnVersion(nullptr) {}
  ~CudnnVersion() override;
  explicit PROTOBUF_CONSTEXPR CudnnVersion(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CudnnVersion(const CudnnVersion& from);
  CudnnVersion(CudnnVersion&& from) noexcept
    : CudnnVersion() {
    *this = ::std::move(from);
  }

  inline CudnnVersion& operator=(const CudnnVersion& from) {
    CopyFrom(from);
    return *this;
  }
  inline CudnnVersion& operator=(CudnnVersion&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CudnnVersion& default_instance() {
    return *internal_default_instance();
  }
  static inline const CudnnVersion* internal_default_instance() {
    return reinterpret_cast<const CudnnVersion*>(
               &_CudnnVersion_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CudnnVersion& a, CudnnVersion& b) {
    a.Swap(&b);
  }
  inline void Swap(CudnnVersion* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CudnnVersion* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CudnnVersion* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CudnnVersion>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CudnnVersion& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CudnnVersion& from) {
    CudnnVersion::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CudnnVersion* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.CudnnVersion";
  }
  protected:
  explicit CudnnVersion(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMajorFieldNumber = 1,
    kMinorFieldNumber = 2,
    kPatchFieldNumber = 3,
  };
  // int32 major = 1;
  void clear_major();
  int32_t major() const;
  void set_major(int32_t value);
  private:
  int32_t _internal_major() const;
  void _internal_set_major(int32_t value);
  public:

  // int32 minor = 2;
  void clear_minor();
  int32_t minor() const;
  void set_minor(int32_t value);
  private:
  int32_t _internal_minor() const;
  void _internal_set_minor(int32_t value);
  public:

  // int32 patch = 3;
  void clear_patch();
  int32_t patch() const;
  void set_patch(int32_t value);
  private:
  int32_t _internal_patch() const;
  void _internal_set_patch(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.CudnnVersion)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t major_;
    int32_t minor_;
    int32_t patch_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class ComputeCapability final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.ComputeCapability) */ {
 public:
  inline ComputeCapability() : ComputeCapability(nullptr) {}
  ~ComputeCapability() override;
  explicit PROTOBUF_CONSTEXPR ComputeCapability(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ComputeCapability(const ComputeCapability& from);
  ComputeCapability(ComputeCapability&& from) noexcept
    : ComputeCapability() {
    *this = ::std::move(from);
  }

  inline ComputeCapability& operator=(const ComputeCapability& from) {
    CopyFrom(from);
    return *this;
  }
  inline ComputeCapability& operator=(ComputeCapability&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ComputeCapability& default_instance() {
    return *internal_default_instance();
  }
  static inline const ComputeCapability* internal_default_instance() {
    return reinterpret_cast<const ComputeCapability*>(
               &_ComputeCapability_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ComputeCapability& a, ComputeCapability& b) {
    a.Swap(&b);
  }
  inline void Swap(ComputeCapability* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ComputeCapability* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ComputeCapability* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ComputeCapability>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ComputeCapability& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ComputeCapability& from) {
    ComputeCapability::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ComputeCapability* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.ComputeCapability";
  }
  protected:
  explicit ComputeCapability(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMajorFieldNumber = 1,
    kMinorFieldNumber = 2,
  };
  // int32 major = 1;
  void clear_major();
  int32_t major() const;
  void set_major(int32_t value);
  private:
  int32_t _internal_major() const;
  void _internal_set_major(int32_t value);
  public:

  // int32 minor = 2;
  void clear_minor();
  int32_t minor() const;
  void set_minor(int32_t value);
  private:
  int32_t _internal_minor() const;
  void _internal_set_minor(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.ComputeCapability)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t major_;
    int32_t minor_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_FailureResult final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.AutotuneResult.FailureResult) */ {
 public:
  inline AutotuneResult_FailureResult() : AutotuneResult_FailureResult(nullptr) {}
  ~AutotuneResult_FailureResult() override;
  explicit PROTOBUF_CONSTEXPR AutotuneResult_FailureResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuneResult_FailureResult(const AutotuneResult_FailureResult& from);
  AutotuneResult_FailureResult(AutotuneResult_FailureResult&& from) noexcept
    : AutotuneResult_FailureResult() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_FailureResult& operator=(const AutotuneResult_FailureResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_FailureResult& operator=(AutotuneResult_FailureResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuneResult_FailureResult& default_instance() {
    return *internal_default_instance();
  }
  enum KeyCase {
    kReferenceConv = 11,
    kReferenceGemm = 12,
    kReferenceCudaConvPlan = 14,
    kReferenceAlgorithm = 15,
    KEY_NOT_SET = 0,
  };

  static inline const AutotuneResult_FailureResult* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_FailureResult*>(
               &_AutotuneResult_FailureResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(AutotuneResult_FailureResult& a, AutotuneResult_FailureResult& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_FailureResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuneResult_FailureResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuneResult_FailureResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuneResult_FailureResult>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuneResult_FailureResult& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuneResult_FailureResult& from) {
    AutotuneResult_FailureResult::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_FailureResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.AutotuneResult.FailureResult";
  }
  protected:
  explicit AutotuneResult_FailureResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMsgFieldNumber = 2,
    kBufferAddressFieldNumber = 13,
    kKindFieldNumber = 1,
    kReferenceConvFieldNumber = 11,
    kReferenceGemmFieldNumber = 12,
    kReferenceCudaConvPlanFieldNumber = 14,
    kReferenceAlgorithmFieldNumber = 15,
  };
  // string msg = 2;
  void clear_msg();
  const std::string& msg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_msg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_msg();
  PROTOBUF_NODISCARD std::string* release_msg();
  void set_allocated_msg(std::string* msg);
  private:
  const std::string& _internal_msg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_msg(const std::string& value);
  std::string* _internal_mutable_msg();
  public:

  // int64 buffer_address = 13;
  void clear_buffer_address();
  int64_t buffer_address() const;
  void set_buffer_address(int64_t value);
  private:
  int64_t _internal_buffer_address() const;
  void _internal_set_buffer_address(int64_t value);
  public:

  // .xla.AutotuneResult.FailureKind kind = 1;
  void clear_kind();
  ::xla::AutotuneResult_FailureKind kind() const;
  void set_kind(::xla::AutotuneResult_FailureKind value);
  private:
  ::xla::AutotuneResult_FailureKind _internal_kind() const;
  void _internal_set_kind(::xla::AutotuneResult_FailureKind value);
  public:

  // .xla.AutotuneResult.ConvKey reference_conv = 11;
  bool has_reference_conv() const;
  private:
  bool _internal_has_reference_conv() const;
  public:
  void clear_reference_conv();
  const ::xla::AutotuneResult_ConvKey& reference_conv() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_ConvKey* release_reference_conv();
  ::xla::AutotuneResult_ConvKey* mutable_reference_conv();
  void set_allocated_reference_conv(::xla::AutotuneResult_ConvKey* reference_conv);
  private:
  const ::xla::AutotuneResult_ConvKey& _internal_reference_conv() const;
  ::xla::AutotuneResult_ConvKey* _internal_mutable_reference_conv();
  public:
  void unsafe_arena_set_allocated_reference_conv(
      ::xla::AutotuneResult_ConvKey* reference_conv);
  ::xla::AutotuneResult_ConvKey* unsafe_arena_release_reference_conv();

  // .xla.AutotuneResult.GemmKey reference_gemm = 12;
  bool has_reference_gemm() const;
  private:
  bool _internal_has_reference_gemm() const;
  public:
  void clear_reference_gemm();
  const ::xla::AutotuneResult_GemmKey& reference_gemm() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_GemmKey* release_reference_gemm();
  ::xla::AutotuneResult_GemmKey* mutable_reference_gemm();
  void set_allocated_reference_gemm(::xla::AutotuneResult_GemmKey* reference_gemm);
  private:
  const ::xla::AutotuneResult_GemmKey& _internal_reference_gemm() const;
  ::xla::AutotuneResult_GemmKey* _internal_mutable_reference_gemm();
  public:
  void unsafe_arena_set_allocated_reference_gemm(
      ::xla::AutotuneResult_GemmKey* reference_gemm);
  ::xla::AutotuneResult_GemmKey* unsafe_arena_release_reference_gemm();

  // .xla.AutotuneResult.CudaConvPlanKey reference_cuda_conv_plan = 14;
  bool has_reference_cuda_conv_plan() const;
  private:
  bool _internal_has_reference_cuda_conv_plan() const;
  public:
  void clear_reference_cuda_conv_plan();
  const ::xla::AutotuneResult_CudaConvPlanKey& reference_cuda_conv_plan() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_CudaConvPlanKey* release_reference_cuda_conv_plan();
  ::xla::AutotuneResult_CudaConvPlanKey* mutable_reference_cuda_conv_plan();
  void set_allocated_reference_cuda_conv_plan(::xla::AutotuneResult_CudaConvPlanKey* reference_cuda_conv_plan);
  private:
  const ::xla::AutotuneResult_CudaConvPlanKey& _internal_reference_cuda_conv_plan() const;
  ::xla::AutotuneResult_CudaConvPlanKey* _internal_mutable_reference_cuda_conv_plan();
  public:
  void unsafe_arena_set_allocated_reference_cuda_conv_plan(
      ::xla::AutotuneResult_CudaConvPlanKey* reference_cuda_conv_plan);
  ::xla::AutotuneResult_CudaConvPlanKey* unsafe_arena_release_reference_cuda_conv_plan();

  // .stream_executor.dnn.AlgorithmProto reference_algorithm = 15;
  bool has_reference_algorithm() const;
  private:
  bool _internal_has_reference_algorithm() const;
  public:
  void clear_reference_algorithm();
  const ::stream_executor::dnn::AlgorithmProto& reference_algorithm() const;
  PROTOBUF_NODISCARD ::stream_executor::dnn::AlgorithmProto* release_reference_algorithm();
  ::stream_executor::dnn::AlgorithmProto* mutable_reference_algorithm();
  void set_allocated_reference_algorithm(::stream_executor::dnn::AlgorithmProto* reference_algorithm);
  private:
  const ::stream_executor::dnn::AlgorithmProto& _internal_reference_algorithm() const;
  ::stream_executor::dnn::AlgorithmProto* _internal_mutable_reference_algorithm();
  public:
  void unsafe_arena_set_allocated_reference_algorithm(
      ::stream_executor::dnn::AlgorithmProto* reference_algorithm);
  ::stream_executor::dnn::AlgorithmProto* unsafe_arena_release_reference_algorithm();

  void clear_key();
  KeyCase key_case() const;
  // @@protoc_insertion_point(class_scope:xla.AutotuneResult.FailureResult)
 private:
  class _Internal;
  void set_has_reference_conv();
  void set_has_reference_gemm();
  void set_has_reference_cuda_conv_plan();
  void set_has_reference_algorithm();

  inline bool has_key() const;
  inline void clear_has_key();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr msg_;
    int64_t buffer_address_;
    int kind_;
    union KeyUnion {
      constexpr KeyUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::xla::AutotuneResult_ConvKey* reference_conv_;
      ::xla::AutotuneResult_GemmKey* reference_gemm_;
      ::xla::AutotuneResult_CudaConvPlanKey* reference_cuda_conv_plan_;
      ::stream_executor::dnn::AlgorithmProto* reference_algorithm_;
    } key_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_ConvKey final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.AutotuneResult.ConvKey) */ {
 public:
  inline AutotuneResult_ConvKey() : AutotuneResult_ConvKey(nullptr) {}
  ~AutotuneResult_ConvKey() override;
  explicit PROTOBUF_CONSTEXPR AutotuneResult_ConvKey(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuneResult_ConvKey(const AutotuneResult_ConvKey& from);
  AutotuneResult_ConvKey(AutotuneResult_ConvKey&& from) noexcept
    : AutotuneResult_ConvKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_ConvKey& operator=(const AutotuneResult_ConvKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_ConvKey& operator=(AutotuneResult_ConvKey&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuneResult_ConvKey& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutotuneResult_ConvKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_ConvKey*>(
               &_AutotuneResult_ConvKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AutotuneResult_ConvKey& a, AutotuneResult_ConvKey& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_ConvKey* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuneResult_ConvKey* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuneResult_ConvKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuneResult_ConvKey>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuneResult_ConvKey& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuneResult_ConvKey& from) {
    AutotuneResult_ConvKey::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_ConvKey* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.AutotuneResult.ConvKey";
  }
  protected:
  explicit AutotuneResult_ConvKey(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlgorithmFieldNumber = 1,
    kTensorOpsEnabledFieldNumber = 2,
  };
  // int64 algorithm = 1;
  void clear_algorithm();
  int64_t algorithm() const;
  void set_algorithm(int64_t value);
  private:
  int64_t _internal_algorithm() const;
  void _internal_set_algorithm(int64_t value);
  public:

  // bool tensor_ops_enabled = 2;
  void clear_tensor_ops_enabled();
  bool tensor_ops_enabled() const;
  void set_tensor_ops_enabled(bool value);
  private:
  bool _internal_tensor_ops_enabled() const;
  void _internal_set_tensor_ops_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:xla.AutotuneResult.ConvKey)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t algorithm_;
    bool tensor_ops_enabled_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_GemmKey final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.AutotuneResult.GemmKey) */ {
 public:
  inline AutotuneResult_GemmKey() : AutotuneResult_GemmKey(nullptr) {}
  ~AutotuneResult_GemmKey() override;
  explicit PROTOBUF_CONSTEXPR AutotuneResult_GemmKey(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuneResult_GemmKey(const AutotuneResult_GemmKey& from);
  AutotuneResult_GemmKey(AutotuneResult_GemmKey&& from) noexcept
    : AutotuneResult_GemmKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_GemmKey& operator=(const AutotuneResult_GemmKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_GemmKey& operator=(AutotuneResult_GemmKey&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuneResult_GemmKey& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutotuneResult_GemmKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_GemmKey*>(
               &_AutotuneResult_GemmKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AutotuneResult_GemmKey& a, AutotuneResult_GemmKey& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_GemmKey* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuneResult_GemmKey* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuneResult_GemmKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuneResult_GemmKey>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuneResult_GemmKey& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuneResult_GemmKey& from) {
    AutotuneResult_GemmKey::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_GemmKey* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.AutotuneResult.GemmKey";
  }
  protected:
  explicit AutotuneResult_GemmKey(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlgorithmFieldNumber = 1,
  };
  // int64 algorithm = 1;
  void clear_algorithm();
  int64_t algorithm() const;
  void set_algorithm(int64_t value);
  private:
  int64_t _internal_algorithm() const;
  void _internal_set_algorithm(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.AutotuneResult.GemmKey)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t algorithm_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_CudaConvPlanKey final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.AutotuneResult.CudaConvPlanKey) */ {
 public:
  inline AutotuneResult_CudaConvPlanKey() : AutotuneResult_CudaConvPlanKey(nullptr) {}
  ~AutotuneResult_CudaConvPlanKey() override;
  explicit PROTOBUF_CONSTEXPR AutotuneResult_CudaConvPlanKey(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuneResult_CudaConvPlanKey(const AutotuneResult_CudaConvPlanKey& from);
  AutotuneResult_CudaConvPlanKey(AutotuneResult_CudaConvPlanKey&& from) noexcept
    : AutotuneResult_CudaConvPlanKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_CudaConvPlanKey& operator=(const AutotuneResult_CudaConvPlanKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_CudaConvPlanKey& operator=(AutotuneResult_CudaConvPlanKey&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuneResult_CudaConvPlanKey& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutotuneResult_CudaConvPlanKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_CudaConvPlanKey*>(
               &_AutotuneResult_CudaConvPlanKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(AutotuneResult_CudaConvPlanKey& a, AutotuneResult_CudaConvPlanKey& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_CudaConvPlanKey* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuneResult_CudaConvPlanKey* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuneResult_CudaConvPlanKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuneResult_CudaConvPlanKey>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuneResult_CudaConvPlanKey& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuneResult_CudaConvPlanKey& from) {
    AutotuneResult_CudaConvPlanKey::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_CudaConvPlanKey* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.AutotuneResult.CudaConvPlanKey";
  }
  protected:
  explicit AutotuneResult_CudaConvPlanKey(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExecPlanIdFieldNumber = 1,
  };
  // string exec_plan_id = 1;
  void clear_exec_plan_id();
  const std::string& exec_plan_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_exec_plan_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_exec_plan_id();
  PROTOBUF_NODISCARD std::string* release_exec_plan_id();
  void set_allocated_exec_plan_id(std::string* exec_plan_id);
  private:
  const std::string& _internal_exec_plan_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_exec_plan_id(const std::string& value);
  std::string* _internal_mutable_exec_plan_id();
  public:

  // @@protoc_insertion_point(class_scope:xla.AutotuneResult.CudaConvPlanKey)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr exec_plan_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_TritonGemmKey final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.AutotuneResult.TritonGemmKey) */ {
 public:
  inline AutotuneResult_TritonGemmKey() : AutotuneResult_TritonGemmKey(nullptr) {}
  ~AutotuneResult_TritonGemmKey() override;
  explicit PROTOBUF_CONSTEXPR AutotuneResult_TritonGemmKey(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuneResult_TritonGemmKey(const AutotuneResult_TritonGemmKey& from);
  AutotuneResult_TritonGemmKey(AutotuneResult_TritonGemmKey&& from) noexcept
    : AutotuneResult_TritonGemmKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_TritonGemmKey& operator=(const AutotuneResult_TritonGemmKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_TritonGemmKey& operator=(AutotuneResult_TritonGemmKey&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuneResult_TritonGemmKey& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutotuneResult_TritonGemmKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_TritonGemmKey*>(
               &_AutotuneResult_TritonGemmKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(AutotuneResult_TritonGemmKey& a, AutotuneResult_TritonGemmKey& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_TritonGemmKey* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuneResult_TritonGemmKey* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuneResult_TritonGemmKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuneResult_TritonGemmKey>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuneResult_TritonGemmKey& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuneResult_TritonGemmKey& from) {
    AutotuneResult_TritonGemmKey::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_TritonGemmKey* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.AutotuneResult.TritonGemmKey";
  }
  protected:
  explicit AutotuneResult_TritonGemmKey(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBlockMFieldNumber = 1,
    kBlockNFieldNumber = 2,
    kBlockKFieldNumber = 3,
    kSplitKFieldNumber = 4,
    kNumStagesFieldNumber = 5,
    kNumWarpsFieldNumber = 6,
    kNumCtasFieldNumber = 7,
  };
  // int64 block_m = 1;
  void clear_block_m();
  int64_t block_m() const;
  void set_block_m(int64_t value);
  private:
  int64_t _internal_block_m() const;
  void _internal_set_block_m(int64_t value);
  public:

  // int64 block_n = 2;
  void clear_block_n();
  int64_t block_n() const;
  void set_block_n(int64_t value);
  private:
  int64_t _internal_block_n() const;
  void _internal_set_block_n(int64_t value);
  public:

  // int64 block_k = 3;
  void clear_block_k();
  int64_t block_k() const;
  void set_block_k(int64_t value);
  private:
  int64_t _internal_block_k() const;
  void _internal_set_block_k(int64_t value);
  public:

  // int64 split_k = 4;
  void clear_split_k();
  int64_t split_k() const;
  void set_split_k(int64_t value);
  private:
  int64_t _internal_split_k() const;
  void _internal_set_split_k(int64_t value);
  public:

  // int64 num_stages = 5;
  void clear_num_stages();
  int64_t num_stages() const;
  void set_num_stages(int64_t value);
  private:
  int64_t _internal_num_stages() const;
  void _internal_set_num_stages(int64_t value);
  public:

  // int64 num_warps = 6;
  void clear_num_warps();
  int64_t num_warps() const;
  void set_num_warps(int64_t value);
  private:
  int64_t _internal_num_warps() const;
  void _internal_set_num_warps(int64_t value);
  public:

  // int64 num_ctas = 7;
  void clear_num_ctas();
  int64_t num_ctas() const;
  void set_num_ctas(int64_t value);
  private:
  int64_t _internal_num_ctas() const;
  void _internal_set_num_ctas(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.AutotuneResult.TritonGemmKey)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t block_m_;
    int64_t block_n_;
    int64_t block_k_;
    int64_t split_k_;
    int64_t num_stages_;
    int64_t num_warps_;
    int64_t num_ctas_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult_CustomKernelFusionKey final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.AutotuneResult.CustomKernelFusionKey) */ {
 public:
  inline AutotuneResult_CustomKernelFusionKey() : AutotuneResult_CustomKernelFusionKey(nullptr) {}
  ~AutotuneResult_CustomKernelFusionKey() override;
  explicit PROTOBUF_CONSTEXPR AutotuneResult_CustomKernelFusionKey(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuneResult_CustomKernelFusionKey(const AutotuneResult_CustomKernelFusionKey& from);
  AutotuneResult_CustomKernelFusionKey(AutotuneResult_CustomKernelFusionKey&& from) noexcept
    : AutotuneResult_CustomKernelFusionKey() {
    *this = ::std::move(from);
  }

  inline AutotuneResult_CustomKernelFusionKey& operator=(const AutotuneResult_CustomKernelFusionKey& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult_CustomKernelFusionKey& operator=(AutotuneResult_CustomKernelFusionKey&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuneResult_CustomKernelFusionKey& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutotuneResult_CustomKernelFusionKey* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult_CustomKernelFusionKey*>(
               &_AutotuneResult_CustomKernelFusionKey_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(AutotuneResult_CustomKernelFusionKey& a, AutotuneResult_CustomKernelFusionKey& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult_CustomKernelFusionKey* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuneResult_CustomKernelFusionKey* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuneResult_CustomKernelFusionKey* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuneResult_CustomKernelFusionKey>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuneResult_CustomKernelFusionKey& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuneResult_CustomKernelFusionKey& from) {
    AutotuneResult_CustomKernelFusionKey::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult_CustomKernelFusionKey* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.AutotuneResult.CustomKernelFusionKey";
  }
  protected:
  explicit AutotuneResult_CustomKernelFusionKey(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKernelIndexFieldNumber = 1,
  };
  // int64 kernel_index = 1;
  void clear_kernel_index();
  int64_t kernel_index() const;
  void set_kernel_index(int64_t value);
  private:
  int64_t _internal_kernel_index() const;
  void _internal_set_kernel_index(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.AutotuneResult.CustomKernelFusionKey)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t kernel_index_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuneResult final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.AutotuneResult) */ {
 public:
  inline AutotuneResult() : AutotuneResult(nullptr) {}
  ~AutotuneResult() override;
  explicit PROTOBUF_CONSTEXPR AutotuneResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuneResult(const AutotuneResult& from);
  AutotuneResult(AutotuneResult&& from) noexcept
    : AutotuneResult() {
    *this = ::std::move(from);
  }

  inline AutotuneResult& operator=(const AutotuneResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuneResult& operator=(AutotuneResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuneResult& default_instance() {
    return *internal_default_instance();
  }
  enum KeyCase {
    kConv = 5,
    kGemm = 6,
    kTriton = 17,
    kCudaConvPlan = 15,
    kCustomKernelFusion = 18,
    kAlgorithm = 16,
    KEY_NOT_SET = 0,
  };

  static inline const AutotuneResult* internal_default_instance() {
    return reinterpret_cast<const AutotuneResult*>(
               &_AutotuneResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(AutotuneResult& a, AutotuneResult& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuneResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuneResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuneResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuneResult>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuneResult& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuneResult& from) {
    AutotuneResult::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuneResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.AutotuneResult";
  }
  protected:
  explicit AutotuneResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AutotuneResult_FailureResult FailureResult;
  typedef AutotuneResult_ConvKey ConvKey;
  typedef AutotuneResult_GemmKey GemmKey;
  typedef AutotuneResult_CudaConvPlanKey CudaConvPlanKey;
  typedef AutotuneResult_TritonGemmKey TritonGemmKey;
  typedef AutotuneResult_CustomKernelFusionKey CustomKernelFusionKey;

  typedef AutotuneResult_FailureKind FailureKind;
  static constexpr FailureKind UNKNOWN =
    AutotuneResult_FailureKind_UNKNOWN;
  static constexpr FailureKind REDZONE_MODIFIED =
    AutotuneResult_FailureKind_REDZONE_MODIFIED;
  static constexpr FailureKind WRONG_RESULT =
    AutotuneResult_FailureKind_WRONG_RESULT;
  static constexpr FailureKind DISQUALIFIED =
    AutotuneResult_FailureKind_DISQUALIFIED;
  static inline bool FailureKind_IsValid(int value) {
    return AutotuneResult_FailureKind_IsValid(value);
  }
  static constexpr FailureKind FailureKind_MIN =
    AutotuneResult_FailureKind_FailureKind_MIN;
  static constexpr FailureKind FailureKind_MAX =
    AutotuneResult_FailureKind_FailureKind_MAX;
  static constexpr int FailureKind_ARRAYSIZE =
    AutotuneResult_FailureKind_FailureKind_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  FailureKind_descriptor() {
    return AutotuneResult_FailureKind_descriptor();
  }
  template<typename T>
  static inline const std::string& FailureKind_Name(T enum_t_value) {
    static_assert(::std::is_same<T, FailureKind>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function FailureKind_Name.");
    return AutotuneResult_FailureKind_Name(enum_t_value);
  }
  static inline bool FailureKind_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      FailureKind* value) {
    return AutotuneResult_FailureKind_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFailureFieldNumber = 7,
    kRunTimeFieldNumber = 9,
    kScratchBytesFieldNumber = 8,
    kConvFieldNumber = 5,
    kGemmFieldNumber = 6,
    kTritonFieldNumber = 17,
    kCudaConvPlanFieldNumber = 15,
    kCustomKernelFusionFieldNumber = 18,
    kAlgorithmFieldNumber = 16,
  };
  // .xla.AutotuneResult.FailureResult failure = 7;
  bool has_failure() const;
  private:
  bool _internal_has_failure() const;
  public:
  void clear_failure();
  const ::xla::AutotuneResult_FailureResult& failure() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_FailureResult* release_failure();
  ::xla::AutotuneResult_FailureResult* mutable_failure();
  void set_allocated_failure(::xla::AutotuneResult_FailureResult* failure);
  private:
  const ::xla::AutotuneResult_FailureResult& _internal_failure() const;
  ::xla::AutotuneResult_FailureResult* _internal_mutable_failure();
  public:
  void unsafe_arena_set_allocated_failure(
      ::xla::AutotuneResult_FailureResult* failure);
  ::xla::AutotuneResult_FailureResult* unsafe_arena_release_failure();

  // .google.protobuf.Duration run_time = 9;
  bool has_run_time() const;
  private:
  bool _internal_has_run_time() const;
  public:
  void clear_run_time();
  const ::PROTOBUF_NAMESPACE_ID::Duration& run_time() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Duration* release_run_time();
  ::PROTOBUF_NAMESPACE_ID::Duration* mutable_run_time();
  void set_allocated_run_time(::PROTOBUF_NAMESPACE_ID::Duration* run_time);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Duration& _internal_run_time() const;
  ::PROTOBUF_NAMESPACE_ID::Duration* _internal_mutable_run_time();
  public:
  void unsafe_arena_set_allocated_run_time(
      ::PROTOBUF_NAMESPACE_ID::Duration* run_time);
  ::PROTOBUF_NAMESPACE_ID::Duration* unsafe_arena_release_run_time();

  // int64 scratch_bytes = 8;
  void clear_scratch_bytes();
  int64_t scratch_bytes() const;
  void set_scratch_bytes(int64_t value);
  private:
  int64_t _internal_scratch_bytes() const;
  void _internal_set_scratch_bytes(int64_t value);
  public:

  // .xla.AutotuneResult.ConvKey conv = 5;
  bool has_conv() const;
  private:
  bool _internal_has_conv() const;
  public:
  void clear_conv();
  const ::xla::AutotuneResult_ConvKey& conv() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_ConvKey* release_conv();
  ::xla::AutotuneResult_ConvKey* mutable_conv();
  void set_allocated_conv(::xla::AutotuneResult_ConvKey* conv);
  private:
  const ::xla::AutotuneResult_ConvKey& _internal_conv() const;
  ::xla::AutotuneResult_ConvKey* _internal_mutable_conv();
  public:
  void unsafe_arena_set_allocated_conv(
      ::xla::AutotuneResult_ConvKey* conv);
  ::xla::AutotuneResult_ConvKey* unsafe_arena_release_conv();

  // .xla.AutotuneResult.GemmKey gemm = 6;
  bool has_gemm() const;
  private:
  bool _internal_has_gemm() const;
  public:
  void clear_gemm();
  const ::xla::AutotuneResult_GemmKey& gemm() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_GemmKey* release_gemm();
  ::xla::AutotuneResult_GemmKey* mutable_gemm();
  void set_allocated_gemm(::xla::AutotuneResult_GemmKey* gemm);
  private:
  const ::xla::AutotuneResult_GemmKey& _internal_gemm() const;
  ::xla::AutotuneResult_GemmKey* _internal_mutable_gemm();
  public:
  void unsafe_arena_set_allocated_gemm(
      ::xla::AutotuneResult_GemmKey* gemm);
  ::xla::AutotuneResult_GemmKey* unsafe_arena_release_gemm();

  // .xla.AutotuneResult.TritonGemmKey triton = 17;
  bool has_triton() const;
  private:
  bool _internal_has_triton() const;
  public:
  void clear_triton();
  const ::xla::AutotuneResult_TritonGemmKey& triton() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_TritonGemmKey* release_triton();
  ::xla::AutotuneResult_TritonGemmKey* mutable_triton();
  void set_allocated_triton(::xla::AutotuneResult_TritonGemmKey* triton);
  private:
  const ::xla::AutotuneResult_TritonGemmKey& _internal_triton() const;
  ::xla::AutotuneResult_TritonGemmKey* _internal_mutable_triton();
  public:
  void unsafe_arena_set_allocated_triton(
      ::xla::AutotuneResult_TritonGemmKey* triton);
  ::xla::AutotuneResult_TritonGemmKey* unsafe_arena_release_triton();

  // .xla.AutotuneResult.CudaConvPlanKey cuda_conv_plan = 15;
  bool has_cuda_conv_plan() const;
  private:
  bool _internal_has_cuda_conv_plan() const;
  public:
  void clear_cuda_conv_plan();
  const ::xla::AutotuneResult_CudaConvPlanKey& cuda_conv_plan() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_CudaConvPlanKey* release_cuda_conv_plan();
  ::xla::AutotuneResult_CudaConvPlanKey* mutable_cuda_conv_plan();
  void set_allocated_cuda_conv_plan(::xla::AutotuneResult_CudaConvPlanKey* cuda_conv_plan);
  private:
  const ::xla::AutotuneResult_CudaConvPlanKey& _internal_cuda_conv_plan() const;
  ::xla::AutotuneResult_CudaConvPlanKey* _internal_mutable_cuda_conv_plan();
  public:
  void unsafe_arena_set_allocated_cuda_conv_plan(
      ::xla::AutotuneResult_CudaConvPlanKey* cuda_conv_plan);
  ::xla::AutotuneResult_CudaConvPlanKey* unsafe_arena_release_cuda_conv_plan();

  // .xla.AutotuneResult.CustomKernelFusionKey custom_kernel_fusion = 18;
  bool has_custom_kernel_fusion() const;
  private:
  bool _internal_has_custom_kernel_fusion() const;
  public:
  void clear_custom_kernel_fusion();
  const ::xla::AutotuneResult_CustomKernelFusionKey& custom_kernel_fusion() const;
  PROTOBUF_NODISCARD ::xla::AutotuneResult_CustomKernelFusionKey* release_custom_kernel_fusion();
  ::xla::AutotuneResult_CustomKernelFusionKey* mutable_custom_kernel_fusion();
  void set_allocated_custom_kernel_fusion(::xla::AutotuneResult_CustomKernelFusionKey* custom_kernel_fusion);
  private:
  const ::xla::AutotuneResult_CustomKernelFusionKey& _internal_custom_kernel_fusion() const;
  ::xla::AutotuneResult_CustomKernelFusionKey* _internal_mutable_custom_kernel_fusion();
  public:
  void unsafe_arena_set_allocated_custom_kernel_fusion(
      ::xla::AutotuneResult_CustomKernelFusionKey* custom_kernel_fusion);
  ::xla::AutotuneResult_CustomKernelFusionKey* unsafe_arena_release_custom_kernel_fusion();

  // .stream_executor.dnn.AlgorithmProto algorithm = 16;
  bool has_algorithm() const;
  private:
  bool _internal_has_algorithm() const;
  public:
  void clear_algorithm();
  const ::stream_executor::dnn::AlgorithmProto& algorithm() const;
  PROTOBUF_NODISCARD ::stream_executor::dnn::AlgorithmProto* release_algorithm();
  ::stream_executor::dnn::AlgorithmProto* mutable_algorithm();
  void set_allocated_algorithm(::stream_executor::dnn::AlgorithmProto* algorithm);
  private:
  const ::stream_executor::dnn::AlgorithmProto& _internal_algorithm() const;
  ::stream_executor::dnn::AlgorithmProto* _internal_mutable_algorithm();
  public:
  void unsafe_arena_set_allocated_algorithm(
      ::stream_executor::dnn::AlgorithmProto* algorithm);
  ::stream_executor::dnn::AlgorithmProto* unsafe_arena_release_algorithm();

  void clear_key();
  KeyCase key_case() const;
  // @@protoc_insertion_point(class_scope:xla.AutotuneResult)
 private:
  class _Internal;
  void set_has_conv();
  void set_has_gemm();
  void set_has_triton();
  void set_has_cuda_conv_plan();
  void set_has_custom_kernel_fusion();
  void set_has_algorithm();

  inline bool has_key() const;
  inline void clear_has_key();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::xla::AutotuneResult_FailureResult* failure_;
    ::PROTOBUF_NAMESPACE_ID::Duration* run_time_;
    int64_t scratch_bytes_;
    union KeyUnion {
      constexpr KeyUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::xla::AutotuneResult_ConvKey* conv_;
      ::xla::AutotuneResult_GemmKey* gemm_;
      ::xla::AutotuneResult_TritonGemmKey* triton_;
      ::xla::AutotuneResult_CudaConvPlanKey* cuda_conv_plan_;
      ::xla::AutotuneResult_CustomKernelFusionKey* custom_kernel_fusion_;
      ::stream_executor::dnn::AlgorithmProto* algorithm_;
    } key_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// -------------------------------------------------------------------

class AutotuningLog final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.AutotuningLog) */ {
 public:
  inline AutotuningLog() : AutotuningLog(nullptr) {}
  ~AutotuningLog() override;
  explicit PROTOBUF_CONSTEXPR AutotuningLog(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutotuningLog(const AutotuningLog& from);
  AutotuningLog(AutotuningLog&& from) noexcept
    : AutotuningLog() {
    *this = ::std::move(from);
  }

  inline AutotuningLog& operator=(const AutotuningLog& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutotuningLog& operator=(AutotuningLog&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutotuningLog& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutotuningLog* internal_default_instance() {
    return reinterpret_cast<const AutotuningLog*>(
               &_AutotuningLog_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(AutotuningLog& a, AutotuningLog& b) {
    a.Swap(&b);
  }
  inline void Swap(AutotuningLog* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutotuningLog* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutotuningLog* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutotuningLog>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutotuningLog& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutotuningLog& from) {
    AutotuningLog::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutotuningLog* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.AutotuningLog";
  }
  protected:
  explicit AutotuningLog(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResultsFieldNumber = 2,
    kDevicePciBusIdFieldNumber = 5,
    kBlasVersionFieldNumber = 6,
    kFusionNameFieldNumber = 7,
    kInstrFieldNumber = 1,
    kCudnnVersionFieldNumber = 3,
    kComputeCapabilityFieldNumber = 4,
    kFusionCountFieldNumber = 8,
  };
  // repeated .xla.AutotuneResult results = 2;
  int results_size() const;
  private:
  int _internal_results_size() const;
  public:
  void clear_results();
  ::xla::AutotuneResult* mutable_results(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::AutotuneResult >*
      mutable_results();
  private:
  const ::xla::AutotuneResult& _internal_results(int index) const;
  ::xla::AutotuneResult* _internal_add_results();
  public:
  const ::xla::AutotuneResult& results(int index) const;
  ::xla::AutotuneResult* add_results();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::AutotuneResult >&
      results() const;

  // string device_pci_bus_id = 5;
  void clear_device_pci_bus_id();
  const std::string& device_pci_bus_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_pci_bus_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_pci_bus_id();
  PROTOBUF_NODISCARD std::string* release_device_pci_bus_id();
  void set_allocated_device_pci_bus_id(std::string* device_pci_bus_id);
  private:
  const std::string& _internal_device_pci_bus_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_pci_bus_id(const std::string& value);
  std::string* _internal_mutable_device_pci_bus_id();
  public:

  // string blas_version = 6;
  void clear_blas_version();
  const std::string& blas_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_blas_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_blas_version();
  PROTOBUF_NODISCARD std::string* release_blas_version();
  void set_allocated_blas_version(std::string* blas_version);
  private:
  const std::string& _internal_blas_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_blas_version(const std::string& value);
  std::string* _internal_mutable_blas_version();
  public:

  // string fusion_name = 7;
  void clear_fusion_name();
  const std::string& fusion_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_fusion_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_fusion_name();
  PROTOBUF_NODISCARD std::string* release_fusion_name();
  void set_allocated_fusion_name(std::string* fusion_name);
  private:
  const std::string& _internal_fusion_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_fusion_name(const std::string& value);
  std::string* _internal_mutable_fusion_name();
  public:

  // .google.protobuf.Any instr = 1;
  bool has_instr() const;
  private:
  bool _internal_has_instr() const;
  public:
  void clear_instr();
  const ::PROTOBUF_NAMESPACE_ID::Any& instr() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_instr();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_instr();
  void set_allocated_instr(::PROTOBUF_NAMESPACE_ID::Any* instr);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_instr() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_instr();
  public:
  void unsafe_arena_set_allocated_instr(
      ::PROTOBUF_NAMESPACE_ID::Any* instr);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_instr();

  // .xla.CudnnVersion cudnn_version = 3;
  bool has_cudnn_version() const;
  private:
  bool _internal_has_cudnn_version() const;
  public:
  void clear_cudnn_version();
  const ::xla::CudnnVersion& cudnn_version() const;
  PROTOBUF_NODISCARD ::xla::CudnnVersion* release_cudnn_version();
  ::xla::CudnnVersion* mutable_cudnn_version();
  void set_allocated_cudnn_version(::xla::CudnnVersion* cudnn_version);
  private:
  const ::xla::CudnnVersion& _internal_cudnn_version() const;
  ::xla::CudnnVersion* _internal_mutable_cudnn_version();
  public:
  void unsafe_arena_set_allocated_cudnn_version(
      ::xla::CudnnVersion* cudnn_version);
  ::xla::CudnnVersion* unsafe_arena_release_cudnn_version();

  // .xla.ComputeCapability compute_capability = 4;
  bool has_compute_capability() const;
  private:
  bool _internal_has_compute_capability() const;
  public:
  void clear_compute_capability();
  const ::xla::ComputeCapability& compute_capability() const;
  PROTOBUF_NODISCARD ::xla::ComputeCapability* release_compute_capability();
  ::xla::ComputeCapability* mutable_compute_capability();
  void set_allocated_compute_capability(::xla::ComputeCapability* compute_capability);
  private:
  const ::xla::ComputeCapability& _internal_compute_capability() const;
  ::xla::ComputeCapability* _internal_mutable_compute_capability();
  public:
  void unsafe_arena_set_allocated_compute_capability(
      ::xla::ComputeCapability* compute_capability);
  ::xla::ComputeCapability* unsafe_arena_release_compute_capability();

  // int64 fusion_count = 8;
  void clear_fusion_count();
  int64_t fusion_count() const;
  void set_fusion_count(int64_t value);
  private:
  int64_t _internal_fusion_count() const;
  void _internal_set_fusion_count(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:xla.AutotuningLog)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::AutotuneResult > results_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_pci_bus_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr blas_version_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr fusion_name_;
    ::PROTOBUF_NAMESPACE_ID::Any* instr_;
    ::xla::CudnnVersion* cudnn_version_;
    ::xla::ComputeCapability* compute_capability_;
    int64_t fusion_count_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fautotuning_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CudnnVersion

// int32 major = 1;
inline void CudnnVersion::clear_major() {
  _impl_.major_ = 0;
}
inline int32_t CudnnVersion::_internal_major() const {
  return _impl_.major_;
}
inline int32_t CudnnVersion::major() const {
  // @@protoc_insertion_point(field_get:xla.CudnnVersion.major)
  return _internal_major();
}
inline void CudnnVersion::_internal_set_major(int32_t value) {
  
  _impl_.major_ = value;
}
inline void CudnnVersion::set_major(int32_t value) {
  _internal_set_major(value);
  // @@protoc_insertion_point(field_set:xla.CudnnVersion.major)
}

// int32 minor = 2;
inline void CudnnVersion::clear_minor() {
  _impl_.minor_ = 0;
}
inline int32_t CudnnVersion::_internal_minor() const {
  return _impl_.minor_;
}
inline int32_t CudnnVersion::minor() const {
  // @@protoc_insertion_point(field_get:xla.CudnnVersion.minor)
  return _internal_minor();
}
inline void CudnnVersion::_internal_set_minor(int32_t value) {
  
  _impl_.minor_ = value;
}
inline void CudnnVersion::set_minor(int32_t value) {
  _internal_set_minor(value);
  // @@protoc_insertion_point(field_set:xla.CudnnVersion.minor)
}

// int32 patch = 3;
inline void CudnnVersion::clear_patch() {
  _impl_.patch_ = 0;
}
inline int32_t CudnnVersion::_internal_patch() const {
  return _impl_.patch_;
}
inline int32_t CudnnVersion::patch() const {
  // @@protoc_insertion_point(field_get:xla.CudnnVersion.patch)
  return _internal_patch();
}
inline void CudnnVersion::_internal_set_patch(int32_t value) {
  
  _impl_.patch_ = value;
}
inline void CudnnVersion::set_patch(int32_t value) {
  _internal_set_patch(value);
  // @@protoc_insertion_point(field_set:xla.CudnnVersion.patch)
}

// -------------------------------------------------------------------

// ComputeCapability

// int32 major = 1;
inline void ComputeCapability::clear_major() {
  _impl_.major_ = 0;
}
inline int32_t ComputeCapability::_internal_major() const {
  return _impl_.major_;
}
inline int32_t ComputeCapability::major() const {
  // @@protoc_insertion_point(field_get:xla.ComputeCapability.major)
  return _internal_major();
}
inline void ComputeCapability::_internal_set_major(int32_t value) {
  
  _impl_.major_ = value;
}
inline void ComputeCapability::set_major(int32_t value) {
  _internal_set_major(value);
  // @@protoc_insertion_point(field_set:xla.ComputeCapability.major)
}

// int32 minor = 2;
inline void ComputeCapability::clear_minor() {
  _impl_.minor_ = 0;
}
inline int32_t ComputeCapability::_internal_minor() const {
  return _impl_.minor_;
}
inline int32_t ComputeCapability::minor() const {
  // @@protoc_insertion_point(field_get:xla.ComputeCapability.minor)
  return _internal_minor();
}
inline void ComputeCapability::_internal_set_minor(int32_t value) {
  
  _impl_.minor_ = value;
}
inline void ComputeCapability::set_minor(int32_t value) {
  _internal_set_minor(value);
  // @@protoc_insertion_point(field_set:xla.ComputeCapability.minor)
}

// -------------------------------------------------------------------

// AutotuneResult_FailureResult

// .xla.AutotuneResult.FailureKind kind = 1;
inline void AutotuneResult_FailureResult::clear_kind() {
  _impl_.kind_ = 0;
}
inline ::xla::AutotuneResult_FailureKind AutotuneResult_FailureResult::_internal_kind() const {
  return static_cast< ::xla::AutotuneResult_FailureKind >(_impl_.kind_);
}
inline ::xla::AutotuneResult_FailureKind AutotuneResult_FailureResult::kind() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.FailureResult.kind)
  return _internal_kind();
}
inline void AutotuneResult_FailureResult::_internal_set_kind(::xla::AutotuneResult_FailureKind value) {
  
  _impl_.kind_ = value;
}
inline void AutotuneResult_FailureResult::set_kind(::xla::AutotuneResult_FailureKind value) {
  _internal_set_kind(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.FailureResult.kind)
}

// string msg = 2;
inline void AutotuneResult_FailureResult::clear_msg() {
  _impl_.msg_.ClearToEmpty();
}
inline const std::string& AutotuneResult_FailureResult::msg() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.FailureResult.msg)
  return _internal_msg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AutotuneResult_FailureResult::set_msg(ArgT0&& arg0, ArgT... args) {
 
 _impl_.msg_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.FailureResult.msg)
}
inline std::string* AutotuneResult_FailureResult::mutable_msg() {
  std::string* _s = _internal_mutable_msg();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.FailureResult.msg)
  return _s;
}
inline const std::string& AutotuneResult_FailureResult::_internal_msg() const {
  return _impl_.msg_.Get();
}
inline void AutotuneResult_FailureResult::_internal_set_msg(const std::string& value) {
  
  _impl_.msg_.Set(value, GetArenaForAllocation());
}
inline std::string* AutotuneResult_FailureResult::_internal_mutable_msg() {
  
  return _impl_.msg_.Mutable(GetArenaForAllocation());
}
inline std::string* AutotuneResult_FailureResult::release_msg() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.FailureResult.msg)
  return _impl_.msg_.Release();
}
inline void AutotuneResult_FailureResult::set_allocated_msg(std::string* msg) {
  if (msg != nullptr) {
    
  } else {
    
  }
  _impl_.msg_.SetAllocated(msg, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.msg_.IsDefault()) {
    _impl_.msg_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuneResult.FailureResult.msg)
}

// .xla.AutotuneResult.ConvKey reference_conv = 11;
inline bool AutotuneResult_FailureResult::_internal_has_reference_conv() const {
  return key_case() == kReferenceConv;
}
inline bool AutotuneResult_FailureResult::has_reference_conv() const {
  return _internal_has_reference_conv();
}
inline void AutotuneResult_FailureResult::set_has_reference_conv() {
  _impl_._oneof_case_[0] = kReferenceConv;
}
inline void AutotuneResult_FailureResult::clear_reference_conv() {
  if (_internal_has_reference_conv()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.key_.reference_conv_;
    }
    clear_has_key();
  }
}
inline ::xla::AutotuneResult_ConvKey* AutotuneResult_FailureResult::release_reference_conv() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.FailureResult.reference_conv)
  if (_internal_has_reference_conv()) {
    clear_has_key();
    ::xla::AutotuneResult_ConvKey* temp = _impl_.key_.reference_conv_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.reference_conv_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::AutotuneResult_ConvKey& AutotuneResult_FailureResult::_internal_reference_conv() const {
  return _internal_has_reference_conv()
      ? *_impl_.key_.reference_conv_
      : reinterpret_cast< ::xla::AutotuneResult_ConvKey&>(::xla::_AutotuneResult_ConvKey_default_instance_);
}
inline const ::xla::AutotuneResult_ConvKey& AutotuneResult_FailureResult::reference_conv() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.FailureResult.reference_conv)
  return _internal_reference_conv();
}
inline ::xla::AutotuneResult_ConvKey* AutotuneResult_FailureResult::unsafe_arena_release_reference_conv() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.FailureResult.reference_conv)
  if (_internal_has_reference_conv()) {
    clear_has_key();
    ::xla::AutotuneResult_ConvKey* temp = _impl_.key_.reference_conv_;
    _impl_.key_.reference_conv_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult_FailureResult::unsafe_arena_set_allocated_reference_conv(::xla::AutotuneResult_ConvKey* reference_conv) {
  clear_key();
  if (reference_conv) {
    set_has_reference_conv();
    _impl_.key_.reference_conv_ = reference_conv;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.FailureResult.reference_conv)
}
inline ::xla::AutotuneResult_ConvKey* AutotuneResult_FailureResult::_internal_mutable_reference_conv() {
  if (!_internal_has_reference_conv()) {
    clear_key();
    set_has_reference_conv();
    _impl_.key_.reference_conv_ = CreateMaybeMessage< ::xla::AutotuneResult_ConvKey >(GetArenaForAllocation());
  }
  return _impl_.key_.reference_conv_;
}
inline ::xla::AutotuneResult_ConvKey* AutotuneResult_FailureResult::mutable_reference_conv() {
  ::xla::AutotuneResult_ConvKey* _msg = _internal_mutable_reference_conv();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.FailureResult.reference_conv)
  return _msg;
}

// .xla.AutotuneResult.GemmKey reference_gemm = 12;
inline bool AutotuneResult_FailureResult::_internal_has_reference_gemm() const {
  return key_case() == kReferenceGemm;
}
inline bool AutotuneResult_FailureResult::has_reference_gemm() const {
  return _internal_has_reference_gemm();
}
inline void AutotuneResult_FailureResult::set_has_reference_gemm() {
  _impl_._oneof_case_[0] = kReferenceGemm;
}
inline void AutotuneResult_FailureResult::clear_reference_gemm() {
  if (_internal_has_reference_gemm()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.key_.reference_gemm_;
    }
    clear_has_key();
  }
}
inline ::xla::AutotuneResult_GemmKey* AutotuneResult_FailureResult::release_reference_gemm() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.FailureResult.reference_gemm)
  if (_internal_has_reference_gemm()) {
    clear_has_key();
    ::xla::AutotuneResult_GemmKey* temp = _impl_.key_.reference_gemm_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.reference_gemm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::AutotuneResult_GemmKey& AutotuneResult_FailureResult::_internal_reference_gemm() const {
  return _internal_has_reference_gemm()
      ? *_impl_.key_.reference_gemm_
      : reinterpret_cast< ::xla::AutotuneResult_GemmKey&>(::xla::_AutotuneResult_GemmKey_default_instance_);
}
inline const ::xla::AutotuneResult_GemmKey& AutotuneResult_FailureResult::reference_gemm() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.FailureResult.reference_gemm)
  return _internal_reference_gemm();
}
inline ::xla::AutotuneResult_GemmKey* AutotuneResult_FailureResult::unsafe_arena_release_reference_gemm() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.FailureResult.reference_gemm)
  if (_internal_has_reference_gemm()) {
    clear_has_key();
    ::xla::AutotuneResult_GemmKey* temp = _impl_.key_.reference_gemm_;
    _impl_.key_.reference_gemm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult_FailureResult::unsafe_arena_set_allocated_reference_gemm(::xla::AutotuneResult_GemmKey* reference_gemm) {
  clear_key();
  if (reference_gemm) {
    set_has_reference_gemm();
    _impl_.key_.reference_gemm_ = reference_gemm;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.FailureResult.reference_gemm)
}
inline ::xla::AutotuneResult_GemmKey* AutotuneResult_FailureResult::_internal_mutable_reference_gemm() {
  if (!_internal_has_reference_gemm()) {
    clear_key();
    set_has_reference_gemm();
    _impl_.key_.reference_gemm_ = CreateMaybeMessage< ::xla::AutotuneResult_GemmKey >(GetArenaForAllocation());
  }
  return _impl_.key_.reference_gemm_;
}
inline ::xla::AutotuneResult_GemmKey* AutotuneResult_FailureResult::mutable_reference_gemm() {
  ::xla::AutotuneResult_GemmKey* _msg = _internal_mutable_reference_gemm();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.FailureResult.reference_gemm)
  return _msg;
}

// .xla.AutotuneResult.CudaConvPlanKey reference_cuda_conv_plan = 14;
inline bool AutotuneResult_FailureResult::_internal_has_reference_cuda_conv_plan() const {
  return key_case() == kReferenceCudaConvPlan;
}
inline bool AutotuneResult_FailureResult::has_reference_cuda_conv_plan() const {
  return _internal_has_reference_cuda_conv_plan();
}
inline void AutotuneResult_FailureResult::set_has_reference_cuda_conv_plan() {
  _impl_._oneof_case_[0] = kReferenceCudaConvPlan;
}
inline void AutotuneResult_FailureResult::clear_reference_cuda_conv_plan() {
  if (_internal_has_reference_cuda_conv_plan()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.key_.reference_cuda_conv_plan_;
    }
    clear_has_key();
  }
}
inline ::xla::AutotuneResult_CudaConvPlanKey* AutotuneResult_FailureResult::release_reference_cuda_conv_plan() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.FailureResult.reference_cuda_conv_plan)
  if (_internal_has_reference_cuda_conv_plan()) {
    clear_has_key();
    ::xla::AutotuneResult_CudaConvPlanKey* temp = _impl_.key_.reference_cuda_conv_plan_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.reference_cuda_conv_plan_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::AutotuneResult_CudaConvPlanKey& AutotuneResult_FailureResult::_internal_reference_cuda_conv_plan() const {
  return _internal_has_reference_cuda_conv_plan()
      ? *_impl_.key_.reference_cuda_conv_plan_
      : reinterpret_cast< ::xla::AutotuneResult_CudaConvPlanKey&>(::xla::_AutotuneResult_CudaConvPlanKey_default_instance_);
}
inline const ::xla::AutotuneResult_CudaConvPlanKey& AutotuneResult_FailureResult::reference_cuda_conv_plan() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.FailureResult.reference_cuda_conv_plan)
  return _internal_reference_cuda_conv_plan();
}
inline ::xla::AutotuneResult_CudaConvPlanKey* AutotuneResult_FailureResult::unsafe_arena_release_reference_cuda_conv_plan() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.FailureResult.reference_cuda_conv_plan)
  if (_internal_has_reference_cuda_conv_plan()) {
    clear_has_key();
    ::xla::AutotuneResult_CudaConvPlanKey* temp = _impl_.key_.reference_cuda_conv_plan_;
    _impl_.key_.reference_cuda_conv_plan_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult_FailureResult::unsafe_arena_set_allocated_reference_cuda_conv_plan(::xla::AutotuneResult_CudaConvPlanKey* reference_cuda_conv_plan) {
  clear_key();
  if (reference_cuda_conv_plan) {
    set_has_reference_cuda_conv_plan();
    _impl_.key_.reference_cuda_conv_plan_ = reference_cuda_conv_plan;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.FailureResult.reference_cuda_conv_plan)
}
inline ::xla::AutotuneResult_CudaConvPlanKey* AutotuneResult_FailureResult::_internal_mutable_reference_cuda_conv_plan() {
  if (!_internal_has_reference_cuda_conv_plan()) {
    clear_key();
    set_has_reference_cuda_conv_plan();
    _impl_.key_.reference_cuda_conv_plan_ = CreateMaybeMessage< ::xla::AutotuneResult_CudaConvPlanKey >(GetArenaForAllocation());
  }
  return _impl_.key_.reference_cuda_conv_plan_;
}
inline ::xla::AutotuneResult_CudaConvPlanKey* AutotuneResult_FailureResult::mutable_reference_cuda_conv_plan() {
  ::xla::AutotuneResult_CudaConvPlanKey* _msg = _internal_mutable_reference_cuda_conv_plan();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.FailureResult.reference_cuda_conv_plan)
  return _msg;
}

// .stream_executor.dnn.AlgorithmProto reference_algorithm = 15;
inline bool AutotuneResult_FailureResult::_internal_has_reference_algorithm() const {
  return key_case() == kReferenceAlgorithm;
}
inline bool AutotuneResult_FailureResult::has_reference_algorithm() const {
  return _internal_has_reference_algorithm();
}
inline void AutotuneResult_FailureResult::set_has_reference_algorithm() {
  _impl_._oneof_case_[0] = kReferenceAlgorithm;
}
inline ::stream_executor::dnn::AlgorithmProto* AutotuneResult_FailureResult::release_reference_algorithm() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.FailureResult.reference_algorithm)
  if (_internal_has_reference_algorithm()) {
    clear_has_key();
    ::stream_executor::dnn::AlgorithmProto* temp = _impl_.key_.reference_algorithm_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.reference_algorithm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stream_executor::dnn::AlgorithmProto& AutotuneResult_FailureResult::_internal_reference_algorithm() const {
  return _internal_has_reference_algorithm()
      ? *_impl_.key_.reference_algorithm_
      : reinterpret_cast< ::stream_executor::dnn::AlgorithmProto&>(::stream_executor::dnn::_AlgorithmProto_default_instance_);
}
inline const ::stream_executor::dnn::AlgorithmProto& AutotuneResult_FailureResult::reference_algorithm() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.FailureResult.reference_algorithm)
  return _internal_reference_algorithm();
}
inline ::stream_executor::dnn::AlgorithmProto* AutotuneResult_FailureResult::unsafe_arena_release_reference_algorithm() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.FailureResult.reference_algorithm)
  if (_internal_has_reference_algorithm()) {
    clear_has_key();
    ::stream_executor::dnn::AlgorithmProto* temp = _impl_.key_.reference_algorithm_;
    _impl_.key_.reference_algorithm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult_FailureResult::unsafe_arena_set_allocated_reference_algorithm(::stream_executor::dnn::AlgorithmProto* reference_algorithm) {
  clear_key();
  if (reference_algorithm) {
    set_has_reference_algorithm();
    _impl_.key_.reference_algorithm_ = reference_algorithm;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.FailureResult.reference_algorithm)
}
inline ::stream_executor::dnn::AlgorithmProto* AutotuneResult_FailureResult::_internal_mutable_reference_algorithm() {
  if (!_internal_has_reference_algorithm()) {
    clear_key();
    set_has_reference_algorithm();
    _impl_.key_.reference_algorithm_ = CreateMaybeMessage< ::stream_executor::dnn::AlgorithmProto >(GetArenaForAllocation());
  }
  return _impl_.key_.reference_algorithm_;
}
inline ::stream_executor::dnn::AlgorithmProto* AutotuneResult_FailureResult::mutable_reference_algorithm() {
  ::stream_executor::dnn::AlgorithmProto* _msg = _internal_mutable_reference_algorithm();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.FailureResult.reference_algorithm)
  return _msg;
}

// int64 buffer_address = 13;
inline void AutotuneResult_FailureResult::clear_buffer_address() {
  _impl_.buffer_address_ = int64_t{0};
}
inline int64_t AutotuneResult_FailureResult::_internal_buffer_address() const {
  return _impl_.buffer_address_;
}
inline int64_t AutotuneResult_FailureResult::buffer_address() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.FailureResult.buffer_address)
  return _internal_buffer_address();
}
inline void AutotuneResult_FailureResult::_internal_set_buffer_address(int64_t value) {
  
  _impl_.buffer_address_ = value;
}
inline void AutotuneResult_FailureResult::set_buffer_address(int64_t value) {
  _internal_set_buffer_address(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.FailureResult.buffer_address)
}

inline bool AutotuneResult_FailureResult::has_key() const {
  return key_case() != KEY_NOT_SET;
}
inline void AutotuneResult_FailureResult::clear_has_key() {
  _impl_._oneof_case_[0] = KEY_NOT_SET;
}
inline AutotuneResult_FailureResult::KeyCase AutotuneResult_FailureResult::key_case() const {
  return AutotuneResult_FailureResult::KeyCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// AutotuneResult_ConvKey

// int64 algorithm = 1;
inline void AutotuneResult_ConvKey::clear_algorithm() {
  _impl_.algorithm_ = int64_t{0};
}
inline int64_t AutotuneResult_ConvKey::_internal_algorithm() const {
  return _impl_.algorithm_;
}
inline int64_t AutotuneResult_ConvKey::algorithm() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.ConvKey.algorithm)
  return _internal_algorithm();
}
inline void AutotuneResult_ConvKey::_internal_set_algorithm(int64_t value) {
  
  _impl_.algorithm_ = value;
}
inline void AutotuneResult_ConvKey::set_algorithm(int64_t value) {
  _internal_set_algorithm(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.ConvKey.algorithm)
}

// bool tensor_ops_enabled = 2;
inline void AutotuneResult_ConvKey::clear_tensor_ops_enabled() {
  _impl_.tensor_ops_enabled_ = false;
}
inline bool AutotuneResult_ConvKey::_internal_tensor_ops_enabled() const {
  return _impl_.tensor_ops_enabled_;
}
inline bool AutotuneResult_ConvKey::tensor_ops_enabled() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.ConvKey.tensor_ops_enabled)
  return _internal_tensor_ops_enabled();
}
inline void AutotuneResult_ConvKey::_internal_set_tensor_ops_enabled(bool value) {
  
  _impl_.tensor_ops_enabled_ = value;
}
inline void AutotuneResult_ConvKey::set_tensor_ops_enabled(bool value) {
  _internal_set_tensor_ops_enabled(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.ConvKey.tensor_ops_enabled)
}

// -------------------------------------------------------------------

// AutotuneResult_GemmKey

// int64 algorithm = 1;
inline void AutotuneResult_GemmKey::clear_algorithm() {
  _impl_.algorithm_ = int64_t{0};
}
inline int64_t AutotuneResult_GemmKey::_internal_algorithm() const {
  return _impl_.algorithm_;
}
inline int64_t AutotuneResult_GemmKey::algorithm() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.GemmKey.algorithm)
  return _internal_algorithm();
}
inline void AutotuneResult_GemmKey::_internal_set_algorithm(int64_t value) {
  
  _impl_.algorithm_ = value;
}
inline void AutotuneResult_GemmKey::set_algorithm(int64_t value) {
  _internal_set_algorithm(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.GemmKey.algorithm)
}

// -------------------------------------------------------------------

// AutotuneResult_CudaConvPlanKey

// string exec_plan_id = 1;
inline void AutotuneResult_CudaConvPlanKey::clear_exec_plan_id() {
  _impl_.exec_plan_id_.ClearToEmpty();
}
inline const std::string& AutotuneResult_CudaConvPlanKey::exec_plan_id() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.CudaConvPlanKey.exec_plan_id)
  return _internal_exec_plan_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AutotuneResult_CudaConvPlanKey::set_exec_plan_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.exec_plan_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.CudaConvPlanKey.exec_plan_id)
}
inline std::string* AutotuneResult_CudaConvPlanKey::mutable_exec_plan_id() {
  std::string* _s = _internal_mutable_exec_plan_id();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.CudaConvPlanKey.exec_plan_id)
  return _s;
}
inline const std::string& AutotuneResult_CudaConvPlanKey::_internal_exec_plan_id() const {
  return _impl_.exec_plan_id_.Get();
}
inline void AutotuneResult_CudaConvPlanKey::_internal_set_exec_plan_id(const std::string& value) {
  
  _impl_.exec_plan_id_.Set(value, GetArenaForAllocation());
}
inline std::string* AutotuneResult_CudaConvPlanKey::_internal_mutable_exec_plan_id() {
  
  return _impl_.exec_plan_id_.Mutable(GetArenaForAllocation());
}
inline std::string* AutotuneResult_CudaConvPlanKey::release_exec_plan_id() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.CudaConvPlanKey.exec_plan_id)
  return _impl_.exec_plan_id_.Release();
}
inline void AutotuneResult_CudaConvPlanKey::set_allocated_exec_plan_id(std::string* exec_plan_id) {
  if (exec_plan_id != nullptr) {
    
  } else {
    
  }
  _impl_.exec_plan_id_.SetAllocated(exec_plan_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.exec_plan_id_.IsDefault()) {
    _impl_.exec_plan_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuneResult.CudaConvPlanKey.exec_plan_id)
}

// -------------------------------------------------------------------

// AutotuneResult_TritonGemmKey

// int64 block_m = 1;
inline void AutotuneResult_TritonGemmKey::clear_block_m() {
  _impl_.block_m_ = int64_t{0};
}
inline int64_t AutotuneResult_TritonGemmKey::_internal_block_m() const {
  return _impl_.block_m_;
}
inline int64_t AutotuneResult_TritonGemmKey::block_m() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.TritonGemmKey.block_m)
  return _internal_block_m();
}
inline void AutotuneResult_TritonGemmKey::_internal_set_block_m(int64_t value) {
  
  _impl_.block_m_ = value;
}
inline void AutotuneResult_TritonGemmKey::set_block_m(int64_t value) {
  _internal_set_block_m(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.TritonGemmKey.block_m)
}

// int64 block_n = 2;
inline void AutotuneResult_TritonGemmKey::clear_block_n() {
  _impl_.block_n_ = int64_t{0};
}
inline int64_t AutotuneResult_TritonGemmKey::_internal_block_n() const {
  return _impl_.block_n_;
}
inline int64_t AutotuneResult_TritonGemmKey::block_n() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.TritonGemmKey.block_n)
  return _internal_block_n();
}
inline void AutotuneResult_TritonGemmKey::_internal_set_block_n(int64_t value) {
  
  _impl_.block_n_ = value;
}
inline void AutotuneResult_TritonGemmKey::set_block_n(int64_t value) {
  _internal_set_block_n(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.TritonGemmKey.block_n)
}

// int64 block_k = 3;
inline void AutotuneResult_TritonGemmKey::clear_block_k() {
  _impl_.block_k_ = int64_t{0};
}
inline int64_t AutotuneResult_TritonGemmKey::_internal_block_k() const {
  return _impl_.block_k_;
}
inline int64_t AutotuneResult_TritonGemmKey::block_k() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.TritonGemmKey.block_k)
  return _internal_block_k();
}
inline void AutotuneResult_TritonGemmKey::_internal_set_block_k(int64_t value) {
  
  _impl_.block_k_ = value;
}
inline void AutotuneResult_TritonGemmKey::set_block_k(int64_t value) {
  _internal_set_block_k(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.TritonGemmKey.block_k)
}

// int64 split_k = 4;
inline void AutotuneResult_TritonGemmKey::clear_split_k() {
  _impl_.split_k_ = int64_t{0};
}
inline int64_t AutotuneResult_TritonGemmKey::_internal_split_k() const {
  return _impl_.split_k_;
}
inline int64_t AutotuneResult_TritonGemmKey::split_k() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.TritonGemmKey.split_k)
  return _internal_split_k();
}
inline void AutotuneResult_TritonGemmKey::_internal_set_split_k(int64_t value) {
  
  _impl_.split_k_ = value;
}
inline void AutotuneResult_TritonGemmKey::set_split_k(int64_t value) {
  _internal_set_split_k(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.TritonGemmKey.split_k)
}

// int64 num_stages = 5;
inline void AutotuneResult_TritonGemmKey::clear_num_stages() {
  _impl_.num_stages_ = int64_t{0};
}
inline int64_t AutotuneResult_TritonGemmKey::_internal_num_stages() const {
  return _impl_.num_stages_;
}
inline int64_t AutotuneResult_TritonGemmKey::num_stages() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.TritonGemmKey.num_stages)
  return _internal_num_stages();
}
inline void AutotuneResult_TritonGemmKey::_internal_set_num_stages(int64_t value) {
  
  _impl_.num_stages_ = value;
}
inline void AutotuneResult_TritonGemmKey::set_num_stages(int64_t value) {
  _internal_set_num_stages(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.TritonGemmKey.num_stages)
}

// int64 num_warps = 6;
inline void AutotuneResult_TritonGemmKey::clear_num_warps() {
  _impl_.num_warps_ = int64_t{0};
}
inline int64_t AutotuneResult_TritonGemmKey::_internal_num_warps() const {
  return _impl_.num_warps_;
}
inline int64_t AutotuneResult_TritonGemmKey::num_warps() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.TritonGemmKey.num_warps)
  return _internal_num_warps();
}
inline void AutotuneResult_TritonGemmKey::_internal_set_num_warps(int64_t value) {
  
  _impl_.num_warps_ = value;
}
inline void AutotuneResult_TritonGemmKey::set_num_warps(int64_t value) {
  _internal_set_num_warps(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.TritonGemmKey.num_warps)
}

// int64 num_ctas = 7;
inline void AutotuneResult_TritonGemmKey::clear_num_ctas() {
  _impl_.num_ctas_ = int64_t{0};
}
inline int64_t AutotuneResult_TritonGemmKey::_internal_num_ctas() const {
  return _impl_.num_ctas_;
}
inline int64_t AutotuneResult_TritonGemmKey::num_ctas() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.TritonGemmKey.num_ctas)
  return _internal_num_ctas();
}
inline void AutotuneResult_TritonGemmKey::_internal_set_num_ctas(int64_t value) {
  
  _impl_.num_ctas_ = value;
}
inline void AutotuneResult_TritonGemmKey::set_num_ctas(int64_t value) {
  _internal_set_num_ctas(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.TritonGemmKey.num_ctas)
}

// -------------------------------------------------------------------

// AutotuneResult_CustomKernelFusionKey

// int64 kernel_index = 1;
inline void AutotuneResult_CustomKernelFusionKey::clear_kernel_index() {
  _impl_.kernel_index_ = int64_t{0};
}
inline int64_t AutotuneResult_CustomKernelFusionKey::_internal_kernel_index() const {
  return _impl_.kernel_index_;
}
inline int64_t AutotuneResult_CustomKernelFusionKey::kernel_index() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.CustomKernelFusionKey.kernel_index)
  return _internal_kernel_index();
}
inline void AutotuneResult_CustomKernelFusionKey::_internal_set_kernel_index(int64_t value) {
  
  _impl_.kernel_index_ = value;
}
inline void AutotuneResult_CustomKernelFusionKey::set_kernel_index(int64_t value) {
  _internal_set_kernel_index(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.CustomKernelFusionKey.kernel_index)
}

// -------------------------------------------------------------------

// AutotuneResult

// int64 scratch_bytes = 8;
inline void AutotuneResult::clear_scratch_bytes() {
  _impl_.scratch_bytes_ = int64_t{0};
}
inline int64_t AutotuneResult::_internal_scratch_bytes() const {
  return _impl_.scratch_bytes_;
}
inline int64_t AutotuneResult::scratch_bytes() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.scratch_bytes)
  return _internal_scratch_bytes();
}
inline void AutotuneResult::_internal_set_scratch_bytes(int64_t value) {
  
  _impl_.scratch_bytes_ = value;
}
inline void AutotuneResult::set_scratch_bytes(int64_t value) {
  _internal_set_scratch_bytes(value);
  // @@protoc_insertion_point(field_set:xla.AutotuneResult.scratch_bytes)
}

// .google.protobuf.Duration run_time = 9;
inline bool AutotuneResult::_internal_has_run_time() const {
  return this != internal_default_instance() && _impl_.run_time_ != nullptr;
}
inline bool AutotuneResult::has_run_time() const {
  return _internal_has_run_time();
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& AutotuneResult::_internal_run_time() const {
  const ::PROTOBUF_NAMESPACE_ID::Duration* p = _impl_.run_time_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Duration&>(
      ::PROTOBUF_NAMESPACE_ID::_Duration_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Duration& AutotuneResult::run_time() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.run_time)
  return _internal_run_time();
}
inline void AutotuneResult::unsafe_arena_set_allocated_run_time(
    ::PROTOBUF_NAMESPACE_ID::Duration* run_time) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.run_time_);
  }
  _impl_.run_time_ = run_time;
  if (run_time) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.run_time)
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* AutotuneResult::release_run_time() {
  
  ::PROTOBUF_NAMESPACE_ID::Duration* temp = _impl_.run_time_;
  _impl_.run_time_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* AutotuneResult::unsafe_arena_release_run_time() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.run_time)
  
  ::PROTOBUF_NAMESPACE_ID::Duration* temp = _impl_.run_time_;
  _impl_.run_time_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* AutotuneResult::_internal_mutable_run_time() {
  
  if (_impl_.run_time_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Duration>(GetArenaForAllocation());
    _impl_.run_time_ = p;
  }
  return _impl_.run_time_;
}
inline ::PROTOBUF_NAMESPACE_ID::Duration* AutotuneResult::mutable_run_time() {
  ::PROTOBUF_NAMESPACE_ID::Duration* _msg = _internal_mutable_run_time();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.run_time)
  return _msg;
}
inline void AutotuneResult::set_allocated_run_time(::PROTOBUF_NAMESPACE_ID::Duration* run_time) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.run_time_);
  }
  if (run_time) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(run_time));
    if (message_arena != submessage_arena) {
      run_time = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, run_time, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.run_time_ = run_time;
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuneResult.run_time)
}

// .xla.AutotuneResult.FailureResult failure = 7;
inline bool AutotuneResult::_internal_has_failure() const {
  return this != internal_default_instance() && _impl_.failure_ != nullptr;
}
inline bool AutotuneResult::has_failure() const {
  return _internal_has_failure();
}
inline void AutotuneResult::clear_failure() {
  if (GetArenaForAllocation() == nullptr && _impl_.failure_ != nullptr) {
    delete _impl_.failure_;
  }
  _impl_.failure_ = nullptr;
}
inline const ::xla::AutotuneResult_FailureResult& AutotuneResult::_internal_failure() const {
  const ::xla::AutotuneResult_FailureResult* p = _impl_.failure_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::AutotuneResult_FailureResult&>(
      ::xla::_AutotuneResult_FailureResult_default_instance_);
}
inline const ::xla::AutotuneResult_FailureResult& AutotuneResult::failure() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.failure)
  return _internal_failure();
}
inline void AutotuneResult::unsafe_arena_set_allocated_failure(
    ::xla::AutotuneResult_FailureResult* failure) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.failure_);
  }
  _impl_.failure_ = failure;
  if (failure) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.failure)
}
inline ::xla::AutotuneResult_FailureResult* AutotuneResult::release_failure() {
  
  ::xla::AutotuneResult_FailureResult* temp = _impl_.failure_;
  _impl_.failure_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::AutotuneResult_FailureResult* AutotuneResult::unsafe_arena_release_failure() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.failure)
  
  ::xla::AutotuneResult_FailureResult* temp = _impl_.failure_;
  _impl_.failure_ = nullptr;
  return temp;
}
inline ::xla::AutotuneResult_FailureResult* AutotuneResult::_internal_mutable_failure() {
  
  if (_impl_.failure_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::AutotuneResult_FailureResult>(GetArenaForAllocation());
    _impl_.failure_ = p;
  }
  return _impl_.failure_;
}
inline ::xla::AutotuneResult_FailureResult* AutotuneResult::mutable_failure() {
  ::xla::AutotuneResult_FailureResult* _msg = _internal_mutable_failure();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.failure)
  return _msg;
}
inline void AutotuneResult::set_allocated_failure(::xla::AutotuneResult_FailureResult* failure) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.failure_;
  }
  if (failure) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(failure);
    if (message_arena != submessage_arena) {
      failure = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, failure, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.failure_ = failure;
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuneResult.failure)
}

// .xla.AutotuneResult.ConvKey conv = 5;
inline bool AutotuneResult::_internal_has_conv() const {
  return key_case() == kConv;
}
inline bool AutotuneResult::has_conv() const {
  return _internal_has_conv();
}
inline void AutotuneResult::set_has_conv() {
  _impl_._oneof_case_[0] = kConv;
}
inline void AutotuneResult::clear_conv() {
  if (_internal_has_conv()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.key_.conv_;
    }
    clear_has_key();
  }
}
inline ::xla::AutotuneResult_ConvKey* AutotuneResult::release_conv() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.conv)
  if (_internal_has_conv()) {
    clear_has_key();
    ::xla::AutotuneResult_ConvKey* temp = _impl_.key_.conv_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.conv_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::AutotuneResult_ConvKey& AutotuneResult::_internal_conv() const {
  return _internal_has_conv()
      ? *_impl_.key_.conv_
      : reinterpret_cast< ::xla::AutotuneResult_ConvKey&>(::xla::_AutotuneResult_ConvKey_default_instance_);
}
inline const ::xla::AutotuneResult_ConvKey& AutotuneResult::conv() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.conv)
  return _internal_conv();
}
inline ::xla::AutotuneResult_ConvKey* AutotuneResult::unsafe_arena_release_conv() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.conv)
  if (_internal_has_conv()) {
    clear_has_key();
    ::xla::AutotuneResult_ConvKey* temp = _impl_.key_.conv_;
    _impl_.key_.conv_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult::unsafe_arena_set_allocated_conv(::xla::AutotuneResult_ConvKey* conv) {
  clear_key();
  if (conv) {
    set_has_conv();
    _impl_.key_.conv_ = conv;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.conv)
}
inline ::xla::AutotuneResult_ConvKey* AutotuneResult::_internal_mutable_conv() {
  if (!_internal_has_conv()) {
    clear_key();
    set_has_conv();
    _impl_.key_.conv_ = CreateMaybeMessage< ::xla::AutotuneResult_ConvKey >(GetArenaForAllocation());
  }
  return _impl_.key_.conv_;
}
inline ::xla::AutotuneResult_ConvKey* AutotuneResult::mutable_conv() {
  ::xla::AutotuneResult_ConvKey* _msg = _internal_mutable_conv();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.conv)
  return _msg;
}

// .xla.AutotuneResult.GemmKey gemm = 6;
inline bool AutotuneResult::_internal_has_gemm() const {
  return key_case() == kGemm;
}
inline bool AutotuneResult::has_gemm() const {
  return _internal_has_gemm();
}
inline void AutotuneResult::set_has_gemm() {
  _impl_._oneof_case_[0] = kGemm;
}
inline void AutotuneResult::clear_gemm() {
  if (_internal_has_gemm()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.key_.gemm_;
    }
    clear_has_key();
  }
}
inline ::xla::AutotuneResult_GemmKey* AutotuneResult::release_gemm() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.gemm)
  if (_internal_has_gemm()) {
    clear_has_key();
    ::xla::AutotuneResult_GemmKey* temp = _impl_.key_.gemm_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.gemm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::AutotuneResult_GemmKey& AutotuneResult::_internal_gemm() const {
  return _internal_has_gemm()
      ? *_impl_.key_.gemm_
      : reinterpret_cast< ::xla::AutotuneResult_GemmKey&>(::xla::_AutotuneResult_GemmKey_default_instance_);
}
inline const ::xla::AutotuneResult_GemmKey& AutotuneResult::gemm() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.gemm)
  return _internal_gemm();
}
inline ::xla::AutotuneResult_GemmKey* AutotuneResult::unsafe_arena_release_gemm() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.gemm)
  if (_internal_has_gemm()) {
    clear_has_key();
    ::xla::AutotuneResult_GemmKey* temp = _impl_.key_.gemm_;
    _impl_.key_.gemm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult::unsafe_arena_set_allocated_gemm(::xla::AutotuneResult_GemmKey* gemm) {
  clear_key();
  if (gemm) {
    set_has_gemm();
    _impl_.key_.gemm_ = gemm;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.gemm)
}
inline ::xla::AutotuneResult_GemmKey* AutotuneResult::_internal_mutable_gemm() {
  if (!_internal_has_gemm()) {
    clear_key();
    set_has_gemm();
    _impl_.key_.gemm_ = CreateMaybeMessage< ::xla::AutotuneResult_GemmKey >(GetArenaForAllocation());
  }
  return _impl_.key_.gemm_;
}
inline ::xla::AutotuneResult_GemmKey* AutotuneResult::mutable_gemm() {
  ::xla::AutotuneResult_GemmKey* _msg = _internal_mutable_gemm();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.gemm)
  return _msg;
}

// .xla.AutotuneResult.TritonGemmKey triton = 17;
inline bool AutotuneResult::_internal_has_triton() const {
  return key_case() == kTriton;
}
inline bool AutotuneResult::has_triton() const {
  return _internal_has_triton();
}
inline void AutotuneResult::set_has_triton() {
  _impl_._oneof_case_[0] = kTriton;
}
inline void AutotuneResult::clear_triton() {
  if (_internal_has_triton()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.key_.triton_;
    }
    clear_has_key();
  }
}
inline ::xla::AutotuneResult_TritonGemmKey* AutotuneResult::release_triton() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.triton)
  if (_internal_has_triton()) {
    clear_has_key();
    ::xla::AutotuneResult_TritonGemmKey* temp = _impl_.key_.triton_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.triton_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::AutotuneResult_TritonGemmKey& AutotuneResult::_internal_triton() const {
  return _internal_has_triton()
      ? *_impl_.key_.triton_
      : reinterpret_cast< ::xla::AutotuneResult_TritonGemmKey&>(::xla::_AutotuneResult_TritonGemmKey_default_instance_);
}
inline const ::xla::AutotuneResult_TritonGemmKey& AutotuneResult::triton() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.triton)
  return _internal_triton();
}
inline ::xla::AutotuneResult_TritonGemmKey* AutotuneResult::unsafe_arena_release_triton() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.triton)
  if (_internal_has_triton()) {
    clear_has_key();
    ::xla::AutotuneResult_TritonGemmKey* temp = _impl_.key_.triton_;
    _impl_.key_.triton_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult::unsafe_arena_set_allocated_triton(::xla::AutotuneResult_TritonGemmKey* triton) {
  clear_key();
  if (triton) {
    set_has_triton();
    _impl_.key_.triton_ = triton;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.triton)
}
inline ::xla::AutotuneResult_TritonGemmKey* AutotuneResult::_internal_mutable_triton() {
  if (!_internal_has_triton()) {
    clear_key();
    set_has_triton();
    _impl_.key_.triton_ = CreateMaybeMessage< ::xla::AutotuneResult_TritonGemmKey >(GetArenaForAllocation());
  }
  return _impl_.key_.triton_;
}
inline ::xla::AutotuneResult_TritonGemmKey* AutotuneResult::mutable_triton() {
  ::xla::AutotuneResult_TritonGemmKey* _msg = _internal_mutable_triton();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.triton)
  return _msg;
}

// .xla.AutotuneResult.CudaConvPlanKey cuda_conv_plan = 15;
inline bool AutotuneResult::_internal_has_cuda_conv_plan() const {
  return key_case() == kCudaConvPlan;
}
inline bool AutotuneResult::has_cuda_conv_plan() const {
  return _internal_has_cuda_conv_plan();
}
inline void AutotuneResult::set_has_cuda_conv_plan() {
  _impl_._oneof_case_[0] = kCudaConvPlan;
}
inline void AutotuneResult::clear_cuda_conv_plan() {
  if (_internal_has_cuda_conv_plan()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.key_.cuda_conv_plan_;
    }
    clear_has_key();
  }
}
inline ::xla::AutotuneResult_CudaConvPlanKey* AutotuneResult::release_cuda_conv_plan() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.cuda_conv_plan)
  if (_internal_has_cuda_conv_plan()) {
    clear_has_key();
    ::xla::AutotuneResult_CudaConvPlanKey* temp = _impl_.key_.cuda_conv_plan_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.cuda_conv_plan_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::AutotuneResult_CudaConvPlanKey& AutotuneResult::_internal_cuda_conv_plan() const {
  return _internal_has_cuda_conv_plan()
      ? *_impl_.key_.cuda_conv_plan_
      : reinterpret_cast< ::xla::AutotuneResult_CudaConvPlanKey&>(::xla::_AutotuneResult_CudaConvPlanKey_default_instance_);
}
inline const ::xla::AutotuneResult_CudaConvPlanKey& AutotuneResult::cuda_conv_plan() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.cuda_conv_plan)
  return _internal_cuda_conv_plan();
}
inline ::xla::AutotuneResult_CudaConvPlanKey* AutotuneResult::unsafe_arena_release_cuda_conv_plan() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.cuda_conv_plan)
  if (_internal_has_cuda_conv_plan()) {
    clear_has_key();
    ::xla::AutotuneResult_CudaConvPlanKey* temp = _impl_.key_.cuda_conv_plan_;
    _impl_.key_.cuda_conv_plan_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult::unsafe_arena_set_allocated_cuda_conv_plan(::xla::AutotuneResult_CudaConvPlanKey* cuda_conv_plan) {
  clear_key();
  if (cuda_conv_plan) {
    set_has_cuda_conv_plan();
    _impl_.key_.cuda_conv_plan_ = cuda_conv_plan;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.cuda_conv_plan)
}
inline ::xla::AutotuneResult_CudaConvPlanKey* AutotuneResult::_internal_mutable_cuda_conv_plan() {
  if (!_internal_has_cuda_conv_plan()) {
    clear_key();
    set_has_cuda_conv_plan();
    _impl_.key_.cuda_conv_plan_ = CreateMaybeMessage< ::xla::AutotuneResult_CudaConvPlanKey >(GetArenaForAllocation());
  }
  return _impl_.key_.cuda_conv_plan_;
}
inline ::xla::AutotuneResult_CudaConvPlanKey* AutotuneResult::mutable_cuda_conv_plan() {
  ::xla::AutotuneResult_CudaConvPlanKey* _msg = _internal_mutable_cuda_conv_plan();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.cuda_conv_plan)
  return _msg;
}

// .xla.AutotuneResult.CustomKernelFusionKey custom_kernel_fusion = 18;
inline bool AutotuneResult::_internal_has_custom_kernel_fusion() const {
  return key_case() == kCustomKernelFusion;
}
inline bool AutotuneResult::has_custom_kernel_fusion() const {
  return _internal_has_custom_kernel_fusion();
}
inline void AutotuneResult::set_has_custom_kernel_fusion() {
  _impl_._oneof_case_[0] = kCustomKernelFusion;
}
inline void AutotuneResult::clear_custom_kernel_fusion() {
  if (_internal_has_custom_kernel_fusion()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.key_.custom_kernel_fusion_;
    }
    clear_has_key();
  }
}
inline ::xla::AutotuneResult_CustomKernelFusionKey* AutotuneResult::release_custom_kernel_fusion() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.custom_kernel_fusion)
  if (_internal_has_custom_kernel_fusion()) {
    clear_has_key();
    ::xla::AutotuneResult_CustomKernelFusionKey* temp = _impl_.key_.custom_kernel_fusion_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.custom_kernel_fusion_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::xla::AutotuneResult_CustomKernelFusionKey& AutotuneResult::_internal_custom_kernel_fusion() const {
  return _internal_has_custom_kernel_fusion()
      ? *_impl_.key_.custom_kernel_fusion_
      : reinterpret_cast< ::xla::AutotuneResult_CustomKernelFusionKey&>(::xla::_AutotuneResult_CustomKernelFusionKey_default_instance_);
}
inline const ::xla::AutotuneResult_CustomKernelFusionKey& AutotuneResult::custom_kernel_fusion() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.custom_kernel_fusion)
  return _internal_custom_kernel_fusion();
}
inline ::xla::AutotuneResult_CustomKernelFusionKey* AutotuneResult::unsafe_arena_release_custom_kernel_fusion() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.custom_kernel_fusion)
  if (_internal_has_custom_kernel_fusion()) {
    clear_has_key();
    ::xla::AutotuneResult_CustomKernelFusionKey* temp = _impl_.key_.custom_kernel_fusion_;
    _impl_.key_.custom_kernel_fusion_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult::unsafe_arena_set_allocated_custom_kernel_fusion(::xla::AutotuneResult_CustomKernelFusionKey* custom_kernel_fusion) {
  clear_key();
  if (custom_kernel_fusion) {
    set_has_custom_kernel_fusion();
    _impl_.key_.custom_kernel_fusion_ = custom_kernel_fusion;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.custom_kernel_fusion)
}
inline ::xla::AutotuneResult_CustomKernelFusionKey* AutotuneResult::_internal_mutable_custom_kernel_fusion() {
  if (!_internal_has_custom_kernel_fusion()) {
    clear_key();
    set_has_custom_kernel_fusion();
    _impl_.key_.custom_kernel_fusion_ = CreateMaybeMessage< ::xla::AutotuneResult_CustomKernelFusionKey >(GetArenaForAllocation());
  }
  return _impl_.key_.custom_kernel_fusion_;
}
inline ::xla::AutotuneResult_CustomKernelFusionKey* AutotuneResult::mutable_custom_kernel_fusion() {
  ::xla::AutotuneResult_CustomKernelFusionKey* _msg = _internal_mutable_custom_kernel_fusion();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.custom_kernel_fusion)
  return _msg;
}

// .stream_executor.dnn.AlgorithmProto algorithm = 16;
inline bool AutotuneResult::_internal_has_algorithm() const {
  return key_case() == kAlgorithm;
}
inline bool AutotuneResult::has_algorithm() const {
  return _internal_has_algorithm();
}
inline void AutotuneResult::set_has_algorithm() {
  _impl_._oneof_case_[0] = kAlgorithm;
}
inline ::stream_executor::dnn::AlgorithmProto* AutotuneResult::release_algorithm() {
  // @@protoc_insertion_point(field_release:xla.AutotuneResult.algorithm)
  if (_internal_has_algorithm()) {
    clear_has_key();
    ::stream_executor::dnn::AlgorithmProto* temp = _impl_.key_.algorithm_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.key_.algorithm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::stream_executor::dnn::AlgorithmProto& AutotuneResult::_internal_algorithm() const {
  return _internal_has_algorithm()
      ? *_impl_.key_.algorithm_
      : reinterpret_cast< ::stream_executor::dnn::AlgorithmProto&>(::stream_executor::dnn::_AlgorithmProto_default_instance_);
}
inline const ::stream_executor::dnn::AlgorithmProto& AutotuneResult::algorithm() const {
  // @@protoc_insertion_point(field_get:xla.AutotuneResult.algorithm)
  return _internal_algorithm();
}
inline ::stream_executor::dnn::AlgorithmProto* AutotuneResult::unsafe_arena_release_algorithm() {
  // @@protoc_insertion_point(field_unsafe_arena_release:xla.AutotuneResult.algorithm)
  if (_internal_has_algorithm()) {
    clear_has_key();
    ::stream_executor::dnn::AlgorithmProto* temp = _impl_.key_.algorithm_;
    _impl_.key_.algorithm_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void AutotuneResult::unsafe_arena_set_allocated_algorithm(::stream_executor::dnn::AlgorithmProto* algorithm) {
  clear_key();
  if (algorithm) {
    set_has_algorithm();
    _impl_.key_.algorithm_ = algorithm;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuneResult.algorithm)
}
inline ::stream_executor::dnn::AlgorithmProto* AutotuneResult::_internal_mutable_algorithm() {
  if (!_internal_has_algorithm()) {
    clear_key();
    set_has_algorithm();
    _impl_.key_.algorithm_ = CreateMaybeMessage< ::stream_executor::dnn::AlgorithmProto >(GetArenaForAllocation());
  }
  return _impl_.key_.algorithm_;
}
inline ::stream_executor::dnn::AlgorithmProto* AutotuneResult::mutable_algorithm() {
  ::stream_executor::dnn::AlgorithmProto* _msg = _internal_mutable_algorithm();
  // @@protoc_insertion_point(field_mutable:xla.AutotuneResult.algorithm)
  return _msg;
}

inline bool AutotuneResult::has_key() const {
  return key_case() != KEY_NOT_SET;
}
inline void AutotuneResult::clear_has_key() {
  _impl_._oneof_case_[0] = KEY_NOT_SET;
}
inline AutotuneResult::KeyCase AutotuneResult::key_case() const {
  return AutotuneResult::KeyCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// AutotuningLog

// .google.protobuf.Any instr = 1;
inline bool AutotuningLog::_internal_has_instr() const {
  return this != internal_default_instance() && _impl_.instr_ != nullptr;
}
inline bool AutotuningLog::has_instr() const {
  return _internal_has_instr();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& AutotuningLog::_internal_instr() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = _impl_.instr_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& AutotuningLog::instr() const {
  // @@protoc_insertion_point(field_get:xla.AutotuningLog.instr)
  return _internal_instr();
}
inline void AutotuningLog::unsafe_arena_set_allocated_instr(
    ::PROTOBUF_NAMESPACE_ID::Any* instr) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.instr_);
  }
  _impl_.instr_ = instr;
  if (instr) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuningLog.instr)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* AutotuningLog::release_instr() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.instr_;
  _impl_.instr_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* AutotuningLog::unsafe_arena_release_instr() {
  // @@protoc_insertion_point(field_release:xla.AutotuningLog.instr)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.instr_;
  _impl_.instr_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* AutotuningLog::_internal_mutable_instr() {
  
  if (_impl_.instr_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    _impl_.instr_ = p;
  }
  return _impl_.instr_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* AutotuningLog::mutable_instr() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_instr();
  // @@protoc_insertion_point(field_mutable:xla.AutotuningLog.instr)
  return _msg;
}
inline void AutotuningLog::set_allocated_instr(::PROTOBUF_NAMESPACE_ID::Any* instr) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.instr_);
  }
  if (instr) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(instr));
    if (message_arena != submessage_arena) {
      instr = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, instr, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.instr_ = instr;
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuningLog.instr)
}

// repeated .xla.AutotuneResult results = 2;
inline int AutotuningLog::_internal_results_size() const {
  return _impl_.results_.size();
}
inline int AutotuningLog::results_size() const {
  return _internal_results_size();
}
inline void AutotuningLog::clear_results() {
  _impl_.results_.Clear();
}
inline ::xla::AutotuneResult* AutotuningLog::mutable_results(int index) {
  // @@protoc_insertion_point(field_mutable:xla.AutotuningLog.results)
  return _impl_.results_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::AutotuneResult >*
AutotuningLog::mutable_results() {
  // @@protoc_insertion_point(field_mutable_list:xla.AutotuningLog.results)
  return &_impl_.results_;
}
inline const ::xla::AutotuneResult& AutotuningLog::_internal_results(int index) const {
  return _impl_.results_.Get(index);
}
inline const ::xla::AutotuneResult& AutotuningLog::results(int index) const {
  // @@protoc_insertion_point(field_get:xla.AutotuningLog.results)
  return _internal_results(index);
}
inline ::xla::AutotuneResult* AutotuningLog::_internal_add_results() {
  return _impl_.results_.Add();
}
inline ::xla::AutotuneResult* AutotuningLog::add_results() {
  ::xla::AutotuneResult* _add = _internal_add_results();
  // @@protoc_insertion_point(field_add:xla.AutotuningLog.results)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::AutotuneResult >&
AutotuningLog::results() const {
  // @@protoc_insertion_point(field_list:xla.AutotuningLog.results)
  return _impl_.results_;
}

// .xla.CudnnVersion cudnn_version = 3;
inline bool AutotuningLog::_internal_has_cudnn_version() const {
  return this != internal_default_instance() && _impl_.cudnn_version_ != nullptr;
}
inline bool AutotuningLog::has_cudnn_version() const {
  return _internal_has_cudnn_version();
}
inline void AutotuningLog::clear_cudnn_version() {
  if (GetArenaForAllocation() == nullptr && _impl_.cudnn_version_ != nullptr) {
    delete _impl_.cudnn_version_;
  }
  _impl_.cudnn_version_ = nullptr;
}
inline const ::xla::CudnnVersion& AutotuningLog::_internal_cudnn_version() const {
  const ::xla::CudnnVersion* p = _impl_.cudnn_version_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::CudnnVersion&>(
      ::xla::_CudnnVersion_default_instance_);
}
inline const ::xla::CudnnVersion& AutotuningLog::cudnn_version() const {
  // @@protoc_insertion_point(field_get:xla.AutotuningLog.cudnn_version)
  return _internal_cudnn_version();
}
inline void AutotuningLog::unsafe_arena_set_allocated_cudnn_version(
    ::xla::CudnnVersion* cudnn_version) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.cudnn_version_);
  }
  _impl_.cudnn_version_ = cudnn_version;
  if (cudnn_version) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuningLog.cudnn_version)
}
inline ::xla::CudnnVersion* AutotuningLog::release_cudnn_version() {
  
  ::xla::CudnnVersion* temp = _impl_.cudnn_version_;
  _impl_.cudnn_version_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::CudnnVersion* AutotuningLog::unsafe_arena_release_cudnn_version() {
  // @@protoc_insertion_point(field_release:xla.AutotuningLog.cudnn_version)
  
  ::xla::CudnnVersion* temp = _impl_.cudnn_version_;
  _impl_.cudnn_version_ = nullptr;
  return temp;
}
inline ::xla::CudnnVersion* AutotuningLog::_internal_mutable_cudnn_version() {
  
  if (_impl_.cudnn_version_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::CudnnVersion>(GetArenaForAllocation());
    _impl_.cudnn_version_ = p;
  }
  return _impl_.cudnn_version_;
}
inline ::xla::CudnnVersion* AutotuningLog::mutable_cudnn_version() {
  ::xla::CudnnVersion* _msg = _internal_mutable_cudnn_version();
  // @@protoc_insertion_point(field_mutable:xla.AutotuningLog.cudnn_version)
  return _msg;
}
inline void AutotuningLog::set_allocated_cudnn_version(::xla::CudnnVersion* cudnn_version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.cudnn_version_;
  }
  if (cudnn_version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(cudnn_version);
    if (message_arena != submessage_arena) {
      cudnn_version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cudnn_version, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.cudnn_version_ = cudnn_version;
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuningLog.cudnn_version)
}

// .xla.ComputeCapability compute_capability = 4;
inline bool AutotuningLog::_internal_has_compute_capability() const {
  return this != internal_default_instance() && _impl_.compute_capability_ != nullptr;
}
inline bool AutotuningLog::has_compute_capability() const {
  return _internal_has_compute_capability();
}
inline void AutotuningLog::clear_compute_capability() {
  if (GetArenaForAllocation() == nullptr && _impl_.compute_capability_ != nullptr) {
    delete _impl_.compute_capability_;
  }
  _impl_.compute_capability_ = nullptr;
}
inline const ::xla::ComputeCapability& AutotuningLog::_internal_compute_capability() const {
  const ::xla::ComputeCapability* p = _impl_.compute_capability_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::ComputeCapability&>(
      ::xla::_ComputeCapability_default_instance_);
}
inline const ::xla::ComputeCapability& AutotuningLog::compute_capability() const {
  // @@protoc_insertion_point(field_get:xla.AutotuningLog.compute_capability)
  return _internal_compute_capability();
}
inline void AutotuningLog::unsafe_arena_set_allocated_compute_capability(
    ::xla::ComputeCapability* compute_capability) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.compute_capability_);
  }
  _impl_.compute_capability_ = compute_capability;
  if (compute_capability) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.AutotuningLog.compute_capability)
}
inline ::xla::ComputeCapability* AutotuningLog::release_compute_capability() {
  
  ::xla::ComputeCapability* temp = _impl_.compute_capability_;
  _impl_.compute_capability_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::ComputeCapability* AutotuningLog::unsafe_arena_release_compute_capability() {
  // @@protoc_insertion_point(field_release:xla.AutotuningLog.compute_capability)
  
  ::xla::ComputeCapability* temp = _impl_.compute_capability_;
  _impl_.compute_capability_ = nullptr;
  return temp;
}
inline ::xla::ComputeCapability* AutotuningLog::_internal_mutable_compute_capability() {
  
  if (_impl_.compute_capability_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::ComputeCapability>(GetArenaForAllocation());
    _impl_.compute_capability_ = p;
  }
  return _impl_.compute_capability_;
}
inline ::xla::ComputeCapability* AutotuningLog::mutable_compute_capability() {
  ::xla::ComputeCapability* _msg = _internal_mutable_compute_capability();
  // @@protoc_insertion_point(field_mutable:xla.AutotuningLog.compute_capability)
  return _msg;
}
inline void AutotuningLog::set_allocated_compute_capability(::xla::ComputeCapability* compute_capability) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.compute_capability_;
  }
  if (compute_capability) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(compute_capability);
    if (message_arena != submessage_arena) {
      compute_capability = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, compute_capability, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.compute_capability_ = compute_capability;
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuningLog.compute_capability)
}

// string device_pci_bus_id = 5;
inline void AutotuningLog::clear_device_pci_bus_id() {
  _impl_.device_pci_bus_id_.ClearToEmpty();
}
inline const std::string& AutotuningLog::device_pci_bus_id() const {
  // @@protoc_insertion_point(field_get:xla.AutotuningLog.device_pci_bus_id)
  return _internal_device_pci_bus_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AutotuningLog::set_device_pci_bus_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_pci_bus_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.AutotuningLog.device_pci_bus_id)
}
inline std::string* AutotuningLog::mutable_device_pci_bus_id() {
  std::string* _s = _internal_mutable_device_pci_bus_id();
  // @@protoc_insertion_point(field_mutable:xla.AutotuningLog.device_pci_bus_id)
  return _s;
}
inline const std::string& AutotuningLog::_internal_device_pci_bus_id() const {
  return _impl_.device_pci_bus_id_.Get();
}
inline void AutotuningLog::_internal_set_device_pci_bus_id(const std::string& value) {
  
  _impl_.device_pci_bus_id_.Set(value, GetArenaForAllocation());
}
inline std::string* AutotuningLog::_internal_mutable_device_pci_bus_id() {
  
  return _impl_.device_pci_bus_id_.Mutable(GetArenaForAllocation());
}
inline std::string* AutotuningLog::release_device_pci_bus_id() {
  // @@protoc_insertion_point(field_release:xla.AutotuningLog.device_pci_bus_id)
  return _impl_.device_pci_bus_id_.Release();
}
inline void AutotuningLog::set_allocated_device_pci_bus_id(std::string* device_pci_bus_id) {
  if (device_pci_bus_id != nullptr) {
    
  } else {
    
  }
  _impl_.device_pci_bus_id_.SetAllocated(device_pci_bus_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_pci_bus_id_.IsDefault()) {
    _impl_.device_pci_bus_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuningLog.device_pci_bus_id)
}

// string blas_version = 6;
inline void AutotuningLog::clear_blas_version() {
  _impl_.blas_version_.ClearToEmpty();
}
inline const std::string& AutotuningLog::blas_version() const {
  // @@protoc_insertion_point(field_get:xla.AutotuningLog.blas_version)
  return _internal_blas_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AutotuningLog::set_blas_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.blas_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.AutotuningLog.blas_version)
}
inline std::string* AutotuningLog::mutable_blas_version() {
  std::string* _s = _internal_mutable_blas_version();
  // @@protoc_insertion_point(field_mutable:xla.AutotuningLog.blas_version)
  return _s;
}
inline const std::string& AutotuningLog::_internal_blas_version() const {
  return _impl_.blas_version_.Get();
}
inline void AutotuningLog::_internal_set_blas_version(const std::string& value) {
  
  _impl_.blas_version_.Set(value, GetArenaForAllocation());
}
inline std::string* AutotuningLog::_internal_mutable_blas_version() {
  
  return _impl_.blas_version_.Mutable(GetArenaForAllocation());
}
inline std::string* AutotuningLog::release_blas_version() {
  // @@protoc_insertion_point(field_release:xla.AutotuningLog.blas_version)
  return _impl_.blas_version_.Release();
}
inline void AutotuningLog::set_allocated_blas_version(std::string* blas_version) {
  if (blas_version != nullptr) {
    
  } else {
    
  }
  _impl_.blas_version_.SetAllocated(blas_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.blas_version_.IsDefault()) {
    _impl_.blas_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuningLog.blas_version)
}

// string fusion_name = 7;
inline void AutotuningLog::clear_fusion_name() {
  _impl_.fusion_name_.ClearToEmpty();
}
inline const std::string& AutotuningLog::fusion_name() const {
  // @@protoc_insertion_point(field_get:xla.AutotuningLog.fusion_name)
  return _internal_fusion_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AutotuningLog::set_fusion_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.fusion_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.AutotuningLog.fusion_name)
}
inline std::string* AutotuningLog::mutable_fusion_name() {
  std::string* _s = _internal_mutable_fusion_name();
  // @@protoc_insertion_point(field_mutable:xla.AutotuningLog.fusion_name)
  return _s;
}
inline const std::string& AutotuningLog::_internal_fusion_name() const {
  return _impl_.fusion_name_.Get();
}
inline void AutotuningLog::_internal_set_fusion_name(const std::string& value) {
  
  _impl_.fusion_name_.Set(value, GetArenaForAllocation());
}
inline std::string* AutotuningLog::_internal_mutable_fusion_name() {
  
  return _impl_.fusion_name_.Mutable(GetArenaForAllocation());
}
inline std::string* AutotuningLog::release_fusion_name() {
  // @@protoc_insertion_point(field_release:xla.AutotuningLog.fusion_name)
  return _impl_.fusion_name_.Release();
}
inline void AutotuningLog::set_allocated_fusion_name(std::string* fusion_name) {
  if (fusion_name != nullptr) {
    
  } else {
    
  }
  _impl_.fusion_name_.SetAllocated(fusion_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.fusion_name_.IsDefault()) {
    _impl_.fusion_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.AutotuningLog.fusion_name)
}

// int64 fusion_count = 8;
inline void AutotuningLog::clear_fusion_count() {
  _impl_.fusion_count_ = int64_t{0};
}
inline int64_t AutotuningLog::_internal_fusion_count() const {
  return _impl_.fusion_count_;
}
inline int64_t AutotuningLog::fusion_count() const {
  // @@protoc_insertion_point(field_get:xla.AutotuningLog.fusion_count)
  return _internal_fusion_count();
}
inline void AutotuningLog::_internal_set_fusion_count(int64_t value) {
  
  _impl_.fusion_count_ = value;
}
inline void AutotuningLog::set_fusion_count(int64_t value) {
  _internal_set_fusion_count(value);
  // @@protoc_insertion_point(field_set:xla.AutotuningLog.fusion_count)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace xla

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::xla::AutotuneResult_FailureKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::AutotuneResult_FailureKind>() {
  return ::xla::AutotuneResult_FailureKind_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fautotuning_2eproto
