// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/rewriter_config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/protobuf/verifier_config.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
namespace tensorflow {
class AutoParallelOptions;
struct AutoParallelOptionsDefaultTypeInternal;
extern AutoParallelOptionsDefaultTypeInternal _AutoParallelOptions_default_instance_;
class RewriterConfig;
struct RewriterConfigDefaultTypeInternal;
extern RewriterConfigDefaultTypeInternal _RewriterConfig_default_instance_;
class RewriterConfig_CustomGraphOptimizer;
struct RewriterConfig_CustomGraphOptimizerDefaultTypeInternal;
extern RewriterConfig_CustomGraphOptimizerDefaultTypeInternal _RewriterConfig_CustomGraphOptimizer_default_instance_;
class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse;
struct RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUseDefaultTypeInternal;
extern RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUseDefaultTypeInternal _RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_;
class ScopedAllocatorOptions;
struct ScopedAllocatorOptionsDefaultTypeInternal;
extern ScopedAllocatorOptionsDefaultTypeInternal _ScopedAllocatorOptions_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AutoParallelOptions* Arena::CreateMaybeMessage<::tensorflow::AutoParallelOptions>(Arena*);
template<> ::tensorflow::RewriterConfig* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig>(Arena*);
template<> ::tensorflow::RewriterConfig_CustomGraphOptimizer* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig_CustomGraphOptimizer>(Arena*);
template<> ::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ScopedAllocatorOptions* Arena::CreateMaybeMessage<::tensorflow::ScopedAllocatorOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum RewriterConfig_Toggle : int {
  RewriterConfig_Toggle_DEFAULT = 0,
  RewriterConfig_Toggle_ON = 1,
  RewriterConfig_Toggle_OFF = 2,
  RewriterConfig_Toggle_AGGRESSIVE = 3,
  RewriterConfig_Toggle_EXPERIMENTAL_MLIR = 4,
  RewriterConfig_Toggle_EXPERIMENTAL_BOTH = 5,
  RewriterConfig_Toggle_RewriterConfig_Toggle_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  RewriterConfig_Toggle_RewriterConfig_Toggle_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool RewriterConfig_Toggle_IsValid(int value);
constexpr RewriterConfig_Toggle RewriterConfig_Toggle_Toggle_MIN = RewriterConfig_Toggle_DEFAULT;
constexpr RewriterConfig_Toggle RewriterConfig_Toggle_Toggle_MAX = RewriterConfig_Toggle_EXPERIMENTAL_BOTH;
constexpr int RewriterConfig_Toggle_Toggle_ARRAYSIZE = RewriterConfig_Toggle_Toggle_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RewriterConfig_Toggle_descriptor();
template<typename T>
inline const std::string& RewriterConfig_Toggle_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RewriterConfig_Toggle>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RewriterConfig_Toggle_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RewriterConfig_Toggle_descriptor(), enum_t_value);
}
inline bool RewriterConfig_Toggle_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RewriterConfig_Toggle* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RewriterConfig_Toggle>(
    RewriterConfig_Toggle_descriptor(), name, value);
}
enum RewriterConfig_CpuLayout : int {
  RewriterConfig_CpuLayout_NO_CONVERSION_ON_CPU = 0,
  RewriterConfig_CpuLayout_NCHW_TO_NHWC = 1,
  RewriterConfig_CpuLayout_NHWC_TO_NCHW = 2,
  RewriterConfig_CpuLayout_RewriterConfig_CpuLayout_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  RewriterConfig_CpuLayout_RewriterConfig_CpuLayout_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool RewriterConfig_CpuLayout_IsValid(int value);
constexpr RewriterConfig_CpuLayout RewriterConfig_CpuLayout_CpuLayout_MIN = RewriterConfig_CpuLayout_NO_CONVERSION_ON_CPU;
constexpr RewriterConfig_CpuLayout RewriterConfig_CpuLayout_CpuLayout_MAX = RewriterConfig_CpuLayout_NHWC_TO_NCHW;
constexpr int RewriterConfig_CpuLayout_CpuLayout_ARRAYSIZE = RewriterConfig_CpuLayout_CpuLayout_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RewriterConfig_CpuLayout_descriptor();
template<typename T>
inline const std::string& RewriterConfig_CpuLayout_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RewriterConfig_CpuLayout>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RewriterConfig_CpuLayout_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RewriterConfig_CpuLayout_descriptor(), enum_t_value);
}
inline bool RewriterConfig_CpuLayout_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RewriterConfig_CpuLayout* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RewriterConfig_CpuLayout>(
    RewriterConfig_CpuLayout_descriptor(), name, value);
}
enum RewriterConfig_NumIterationsType : int {
  RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS = 0,
  RewriterConfig_NumIterationsType_ONE = 1,
  RewriterConfig_NumIterationsType_TWO = 2,
  RewriterConfig_NumIterationsType_RewriterConfig_NumIterationsType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  RewriterConfig_NumIterationsType_RewriterConfig_NumIterationsType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool RewriterConfig_NumIterationsType_IsValid(int value);
constexpr RewriterConfig_NumIterationsType RewriterConfig_NumIterationsType_NumIterationsType_MIN = RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS;
constexpr RewriterConfig_NumIterationsType RewriterConfig_NumIterationsType_NumIterationsType_MAX = RewriterConfig_NumIterationsType_TWO;
constexpr int RewriterConfig_NumIterationsType_NumIterationsType_ARRAYSIZE = RewriterConfig_NumIterationsType_NumIterationsType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RewriterConfig_NumIterationsType_descriptor();
template<typename T>
inline const std::string& RewriterConfig_NumIterationsType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RewriterConfig_NumIterationsType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RewriterConfig_NumIterationsType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RewriterConfig_NumIterationsType_descriptor(), enum_t_value);
}
inline bool RewriterConfig_NumIterationsType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RewriterConfig_NumIterationsType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RewriterConfig_NumIterationsType>(
    RewriterConfig_NumIterationsType_descriptor(), name, value);
}
enum RewriterConfig_MemOptType : int {
  RewriterConfig_MemOptType_DEFAULT_MEM_OPT = 0,
  RewriterConfig_MemOptType_NO_MEM_OPT = 1,
  RewriterConfig_MemOptType_MANUAL = 2,
  RewriterConfig_MemOptType_SWAPPING_HEURISTICS = 4,
  RewriterConfig_MemOptType_RECOMPUTATION_HEURISTICS = 5,
  RewriterConfig_MemOptType_SCHEDULING_HEURISTICS = 6,
  RewriterConfig_MemOptType_HEURISTICS = 3,
  RewriterConfig_MemOptType_RewriterConfig_MemOptType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  RewriterConfig_MemOptType_RewriterConfig_MemOptType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool RewriterConfig_MemOptType_IsValid(int value);
constexpr RewriterConfig_MemOptType RewriterConfig_MemOptType_MemOptType_MIN = RewriterConfig_MemOptType_DEFAULT_MEM_OPT;
constexpr RewriterConfig_MemOptType RewriterConfig_MemOptType_MemOptType_MAX = RewriterConfig_MemOptType_SCHEDULING_HEURISTICS;
constexpr int RewriterConfig_MemOptType_MemOptType_ARRAYSIZE = RewriterConfig_MemOptType_MemOptType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RewriterConfig_MemOptType_descriptor();
template<typename T>
inline const std::string& RewriterConfig_MemOptType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RewriterConfig_MemOptType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RewriterConfig_MemOptType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RewriterConfig_MemOptType_descriptor(), enum_t_value);
}
inline bool RewriterConfig_MemOptType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RewriterConfig_MemOptType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RewriterConfig_MemOptType>(
    RewriterConfig_MemOptType_descriptor(), name, value);
}
// ===================================================================

class AutoParallelOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AutoParallelOptions) */ {
 public:
  inline AutoParallelOptions() : AutoParallelOptions(nullptr) {}
  ~AutoParallelOptions() override;
  explicit PROTOBUF_CONSTEXPR AutoParallelOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutoParallelOptions(const AutoParallelOptions& from);
  AutoParallelOptions(AutoParallelOptions&& from) noexcept
    : AutoParallelOptions() {
    *this = ::std::move(from);
  }

  inline AutoParallelOptions& operator=(const AutoParallelOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutoParallelOptions& operator=(AutoParallelOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutoParallelOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutoParallelOptions* internal_default_instance() {
    return reinterpret_cast<const AutoParallelOptions*>(
               &_AutoParallelOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AutoParallelOptions& a, AutoParallelOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(AutoParallelOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutoParallelOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutoParallelOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutoParallelOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutoParallelOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AutoParallelOptions& from) {
    AutoParallelOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutoParallelOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AutoParallelOptions";
  }
  protected:
  explicit AutoParallelOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnableFieldNumber = 1,
    kNumReplicasFieldNumber = 2,
  };
  // bool enable = 1;
  void clear_enable();
  bool enable() const;
  void set_enable(bool value);
  private:
  bool _internal_enable() const;
  void _internal_set_enable(bool value);
  public:

  // int32 num_replicas = 2;
  void clear_num_replicas();
  int32_t num_replicas() const;
  void set_num_replicas(int32_t value);
  private:
  int32_t _internal_num_replicas() const;
  void _internal_set_num_replicas(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.AutoParallelOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool enable_;
    int32_t num_replicas_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class ScopedAllocatorOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ScopedAllocatorOptions) */ {
 public:
  inline ScopedAllocatorOptions() : ScopedAllocatorOptions(nullptr) {}
  ~ScopedAllocatorOptions() override;
  explicit PROTOBUF_CONSTEXPR ScopedAllocatorOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ScopedAllocatorOptions(const ScopedAllocatorOptions& from);
  ScopedAllocatorOptions(ScopedAllocatorOptions&& from) noexcept
    : ScopedAllocatorOptions() {
    *this = ::std::move(from);
  }

  inline ScopedAllocatorOptions& operator=(const ScopedAllocatorOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ScopedAllocatorOptions& operator=(ScopedAllocatorOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ScopedAllocatorOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const ScopedAllocatorOptions* internal_default_instance() {
    return reinterpret_cast<const ScopedAllocatorOptions*>(
               &_ScopedAllocatorOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ScopedAllocatorOptions& a, ScopedAllocatorOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ScopedAllocatorOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ScopedAllocatorOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ScopedAllocatorOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ScopedAllocatorOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ScopedAllocatorOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ScopedAllocatorOptions& from) {
    ScopedAllocatorOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ScopedAllocatorOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ScopedAllocatorOptions";
  }
  protected:
  explicit ScopedAllocatorOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnableOpFieldNumber = 1,
  };
  // repeated string enable_op = 1;
  int enable_op_size() const;
  private:
  int _internal_enable_op_size() const;
  public:
  void clear_enable_op();
  const std::string& enable_op(int index) const;
  std::string* mutable_enable_op(int index);
  void set_enable_op(int index, const std::string& value);
  void set_enable_op(int index, std::string&& value);
  void set_enable_op(int index, const char* value);
  void set_enable_op(int index, const char* value, size_t size);
  std::string* add_enable_op();
  void add_enable_op(const std::string& value);
  void add_enable_op(std::string&& value);
  void add_enable_op(const char* value);
  void add_enable_op(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& enable_op() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_enable_op();
  private:
  const std::string& _internal_enable_op(int index) const;
  std::string* _internal_add_enable_op();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ScopedAllocatorOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> enable_op_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse& other);
  static const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse*>(&_RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.RewriterConfig.CustomGraphOptimizer.ParameterMapEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};

// -------------------------------------------------------------------

class RewriterConfig_CustomGraphOptimizer final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RewriterConfig.CustomGraphOptimizer) */ {
 public:
  inline RewriterConfig_CustomGraphOptimizer() : RewriterConfig_CustomGraphOptimizer(nullptr) {}
  ~RewriterConfig_CustomGraphOptimizer() override;
  explicit PROTOBUF_CONSTEXPR RewriterConfig_CustomGraphOptimizer(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RewriterConfig_CustomGraphOptimizer(const RewriterConfig_CustomGraphOptimizer& from);
  RewriterConfig_CustomGraphOptimizer(RewriterConfig_CustomGraphOptimizer&& from) noexcept
    : RewriterConfig_CustomGraphOptimizer() {
    *this = ::std::move(from);
  }

  inline RewriterConfig_CustomGraphOptimizer& operator=(const RewriterConfig_CustomGraphOptimizer& from) {
    CopyFrom(from);
    return *this;
  }
  inline RewriterConfig_CustomGraphOptimizer& operator=(RewriterConfig_CustomGraphOptimizer&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RewriterConfig_CustomGraphOptimizer& default_instance() {
    return *internal_default_instance();
  }
  static inline const RewriterConfig_CustomGraphOptimizer* internal_default_instance() {
    return reinterpret_cast<const RewriterConfig_CustomGraphOptimizer*>(
               &_RewriterConfig_CustomGraphOptimizer_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(RewriterConfig_CustomGraphOptimizer& a, RewriterConfig_CustomGraphOptimizer& b) {
    a.Swap(&b);
  }
  inline void Swap(RewriterConfig_CustomGraphOptimizer* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RewriterConfig_CustomGraphOptimizer* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RewriterConfig_CustomGraphOptimizer* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RewriterConfig_CustomGraphOptimizer>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RewriterConfig_CustomGraphOptimizer& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RewriterConfig_CustomGraphOptimizer& from) {
    RewriterConfig_CustomGraphOptimizer::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RewriterConfig_CustomGraphOptimizer* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RewriterConfig.CustomGraphOptimizer";
  }
  protected:
  explicit RewriterConfig_CustomGraphOptimizer(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kParameterMapFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // map<string, .tensorflow.AttrValue> parameter_map = 2;
  int parameter_map_size() const;
  private:
  int _internal_parameter_map_size() const;
  public:
  void clear_parameter_map();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      _internal_parameter_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      _internal_mutable_parameter_map();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      parameter_map() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_parameter_map();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RewriterConfig.CustomGraphOptimizer)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        RewriterConfig_CustomGraphOptimizer_ParameterMapEntry_DoNotUse,
        std::string, ::tensorflow::AttrValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> parameter_map_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class RewriterConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RewriterConfig) */ {
 public:
  inline RewriterConfig() : RewriterConfig(nullptr) {}
  ~RewriterConfig() override;
  explicit PROTOBUF_CONSTEXPR RewriterConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RewriterConfig(const RewriterConfig& from);
  RewriterConfig(RewriterConfig&& from) noexcept
    : RewriterConfig() {
    *this = ::std::move(from);
  }

  inline RewriterConfig& operator=(const RewriterConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline RewriterConfig& operator=(RewriterConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RewriterConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const RewriterConfig* internal_default_instance() {
    return reinterpret_cast<const RewriterConfig*>(
               &_RewriterConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(RewriterConfig& a, RewriterConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(RewriterConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RewriterConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RewriterConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RewriterConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RewriterConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RewriterConfig& from) {
    RewriterConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RewriterConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RewriterConfig";
  }
  protected:
  explicit RewriterConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef RewriterConfig_CustomGraphOptimizer CustomGraphOptimizer;

  typedef RewriterConfig_Toggle Toggle;
  static constexpr Toggle DEFAULT =
    RewriterConfig_Toggle_DEFAULT;
  static constexpr Toggle ON =
    RewriterConfig_Toggle_ON;
  static constexpr Toggle OFF =
    RewriterConfig_Toggle_OFF;
  static constexpr Toggle AGGRESSIVE =
    RewriterConfig_Toggle_AGGRESSIVE;
  static constexpr Toggle EXPERIMENTAL_MLIR =
    RewriterConfig_Toggle_EXPERIMENTAL_MLIR;
  static constexpr Toggle EXPERIMENTAL_BOTH =
    RewriterConfig_Toggle_EXPERIMENTAL_BOTH;
  static inline bool Toggle_IsValid(int value) {
    return RewriterConfig_Toggle_IsValid(value);
  }
  static constexpr Toggle Toggle_MIN =
    RewriterConfig_Toggle_Toggle_MIN;
  static constexpr Toggle Toggle_MAX =
    RewriterConfig_Toggle_Toggle_MAX;
  static constexpr int Toggle_ARRAYSIZE =
    RewriterConfig_Toggle_Toggle_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Toggle_descriptor() {
    return RewriterConfig_Toggle_descriptor();
  }
  template<typename T>
  static inline const std::string& Toggle_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Toggle>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Toggle_Name.");
    return RewriterConfig_Toggle_Name(enum_t_value);
  }
  static inline bool Toggle_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Toggle* value) {
    return RewriterConfig_Toggle_Parse(name, value);
  }

  typedef RewriterConfig_CpuLayout CpuLayout;
  static constexpr CpuLayout NO_CONVERSION_ON_CPU =
    RewriterConfig_CpuLayout_NO_CONVERSION_ON_CPU;
  static constexpr CpuLayout NCHW_TO_NHWC =
    RewriterConfig_CpuLayout_NCHW_TO_NHWC;
  static constexpr CpuLayout NHWC_TO_NCHW =
    RewriterConfig_CpuLayout_NHWC_TO_NCHW;
  static inline bool CpuLayout_IsValid(int value) {
    return RewriterConfig_CpuLayout_IsValid(value);
  }
  static constexpr CpuLayout CpuLayout_MIN =
    RewriterConfig_CpuLayout_CpuLayout_MIN;
  static constexpr CpuLayout CpuLayout_MAX =
    RewriterConfig_CpuLayout_CpuLayout_MAX;
  static constexpr int CpuLayout_ARRAYSIZE =
    RewriterConfig_CpuLayout_CpuLayout_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  CpuLayout_descriptor() {
    return RewriterConfig_CpuLayout_descriptor();
  }
  template<typename T>
  static inline const std::string& CpuLayout_Name(T enum_t_value) {
    static_assert(::std::is_same<T, CpuLayout>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function CpuLayout_Name.");
    return RewriterConfig_CpuLayout_Name(enum_t_value);
  }
  static inline bool CpuLayout_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      CpuLayout* value) {
    return RewriterConfig_CpuLayout_Parse(name, value);
  }

  typedef RewriterConfig_NumIterationsType NumIterationsType;
  static constexpr NumIterationsType DEFAULT_NUM_ITERS =
    RewriterConfig_NumIterationsType_DEFAULT_NUM_ITERS;
  static constexpr NumIterationsType ONE =
    RewriterConfig_NumIterationsType_ONE;
  static constexpr NumIterationsType TWO =
    RewriterConfig_NumIterationsType_TWO;
  static inline bool NumIterationsType_IsValid(int value) {
    return RewriterConfig_NumIterationsType_IsValid(value);
  }
  static constexpr NumIterationsType NumIterationsType_MIN =
    RewriterConfig_NumIterationsType_NumIterationsType_MIN;
  static constexpr NumIterationsType NumIterationsType_MAX =
    RewriterConfig_NumIterationsType_NumIterationsType_MAX;
  static constexpr int NumIterationsType_ARRAYSIZE =
    RewriterConfig_NumIterationsType_NumIterationsType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  NumIterationsType_descriptor() {
    return RewriterConfig_NumIterationsType_descriptor();
  }
  template<typename T>
  static inline const std::string& NumIterationsType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, NumIterationsType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function NumIterationsType_Name.");
    return RewriterConfig_NumIterationsType_Name(enum_t_value);
  }
  static inline bool NumIterationsType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      NumIterationsType* value) {
    return RewriterConfig_NumIterationsType_Parse(name, value);
  }

  typedef RewriterConfig_MemOptType MemOptType;
  static constexpr MemOptType DEFAULT_MEM_OPT =
    RewriterConfig_MemOptType_DEFAULT_MEM_OPT;
  static constexpr MemOptType NO_MEM_OPT =
    RewriterConfig_MemOptType_NO_MEM_OPT;
  static constexpr MemOptType MANUAL =
    RewriterConfig_MemOptType_MANUAL;
  static constexpr MemOptType SWAPPING_HEURISTICS =
    RewriterConfig_MemOptType_SWAPPING_HEURISTICS;
  static constexpr MemOptType RECOMPUTATION_HEURISTICS =
    RewriterConfig_MemOptType_RECOMPUTATION_HEURISTICS;
  static constexpr MemOptType SCHEDULING_HEURISTICS =
    RewriterConfig_MemOptType_SCHEDULING_HEURISTICS;
  static constexpr MemOptType HEURISTICS =
    RewriterConfig_MemOptType_HEURISTICS;
  static inline bool MemOptType_IsValid(int value) {
    return RewriterConfig_MemOptType_IsValid(value);
  }
  static constexpr MemOptType MemOptType_MIN =
    RewriterConfig_MemOptType_MemOptType_MIN;
  static constexpr MemOptType MemOptType_MAX =
    RewriterConfig_MemOptType_MemOptType_MAX;
  static constexpr int MemOptType_ARRAYSIZE =
    RewriterConfig_MemOptType_MemOptType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MemOptType_descriptor() {
    return RewriterConfig_MemOptType_descriptor();
  }
  template<typename T>
  static inline const std::string& MemOptType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MemOptType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MemOptType_Name.");
    return RewriterConfig_MemOptType_Name(enum_t_value);
  }
  static inline bool MemOptType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      MemOptType* value) {
    return RewriterConfig_MemOptType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kOptimizersFieldNumber = 100,
    kCustomOptimizersFieldNumber = 200,
    kMemoryOptimizerTargetNodeNameScopeFieldNumber = 6,
    kAutoParallelFieldNumber = 5,
    kScopedAllocatorOptsFieldNumber = 16,
    kInterOptimizerVerifierConfigFieldNumber = 300,
    kPostOptimizationVerifierConfigFieldNumber = 301,
    kLayoutOptimizerFieldNumber = 1,
    kConstantFoldingFieldNumber = 3,
    kMemoryOptimizationFieldNumber = 4,
    kArithmeticOptimizationFieldNumber = 7,
    kDependencyOptimizationFieldNumber = 8,
    kLoopOptimizationFieldNumber = 9,
    kFunctionOptimizationFieldNumber = 10,
    kDebugStripperFieldNumber = 11,
    kMetaOptimizerIterationsFieldNumber = 12,
    kShapeOptimizationFieldNumber = 13,
    kRemappingFieldNumber = 14,
    kScopedAllocatorOptimizationFieldNumber = 15,
    kMinGraphNodesFieldNumber = 17,
    kPinToHostOptimizationFieldNumber = 18,
    kMetaOptimizerTimeoutMsFieldNumber = 20,
    kDisableModelPruningFieldNumber = 2,
    kDisableMetaOptimizerFieldNumber = 19,
    kDisableTfgOptimizerFieldNumber = 32,
    kExperimentalDisableCompressedTensorOptimizationFieldNumber = 26,
    kImplementationSelectorFieldNumber = 22,
    kAutoMixedPrecisionFieldNumber = 23,
    kCommonSubgraphEliminationFieldNumber = 24,
    kExperimentalDisableFoldingQuantizationEmulationFieldNumber = 27,
    kFailOnOptimizerErrorsFieldNumber = 21,
    kAutoMixedPrecisionMklFieldNumber = 25,
    kUsePluginOptimizersFieldNumber = 28,
    kAutoMixedPrecisionCpuFieldNumber = 29,
    kExperimentalConditionalCodeMotionFieldNumber = 30,
    kAutoMixedPrecisionOnednnBfloat16FieldNumber = 31,
    kCpuLayoutConversionFieldNumber = 50,
  };
  // repeated string optimizers = 100;
  int optimizers_size() const;
  private:
  int _internal_optimizers_size() const;
  public:
  void clear_optimizers();
  const std::string& optimizers(int index) const;
  std::string* mutable_optimizers(int index);
  void set_optimizers(int index, const std::string& value);
  void set_optimizers(int index, std::string&& value);
  void set_optimizers(int index, const char* value);
  void set_optimizers(int index, const char* value, size_t size);
  std::string* add_optimizers();
  void add_optimizers(const std::string& value);
  void add_optimizers(std::string&& value);
  void add_optimizers(const char* value);
  void add_optimizers(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& optimizers() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_optimizers();
  private:
  const std::string& _internal_optimizers(int index) const;
  std::string* _internal_add_optimizers();
  public:

  // repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
  int custom_optimizers_size() const;
  private:
  int _internal_custom_optimizers_size() const;
  public:
  void clear_custom_optimizers();
  ::tensorflow::RewriterConfig_CustomGraphOptimizer* mutable_custom_optimizers(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >*
      mutable_custom_optimizers();
  private:
  const ::tensorflow::RewriterConfig_CustomGraphOptimizer& _internal_custom_optimizers(int index) const;
  ::tensorflow::RewriterConfig_CustomGraphOptimizer* _internal_add_custom_optimizers();
  public:
  const ::tensorflow::RewriterConfig_CustomGraphOptimizer& custom_optimizers(int index) const;
  ::tensorflow::RewriterConfig_CustomGraphOptimizer* add_custom_optimizers();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >&
      custom_optimizers() const;

  // string memory_optimizer_target_node_name_scope = 6;
  void clear_memory_optimizer_target_node_name_scope();
  const std::string& memory_optimizer_target_node_name_scope() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_memory_optimizer_target_node_name_scope(ArgT0&& arg0, ArgT... args);
  std::string* mutable_memory_optimizer_target_node_name_scope();
  PROTOBUF_NODISCARD std::string* release_memory_optimizer_target_node_name_scope();
  void set_allocated_memory_optimizer_target_node_name_scope(std::string* memory_optimizer_target_node_name_scope);
  private:
  const std::string& _internal_memory_optimizer_target_node_name_scope() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_memory_optimizer_target_node_name_scope(const std::string& value);
  std::string* _internal_mutable_memory_optimizer_target_node_name_scope();
  public:

  // .tensorflow.AutoParallelOptions auto_parallel = 5;
  bool has_auto_parallel() const;
  private:
  bool _internal_has_auto_parallel() const;
  public:
  void clear_auto_parallel();
  const ::tensorflow::AutoParallelOptions& auto_parallel() const;
  PROTOBUF_NODISCARD ::tensorflow::AutoParallelOptions* release_auto_parallel();
  ::tensorflow::AutoParallelOptions* mutable_auto_parallel();
  void set_allocated_auto_parallel(::tensorflow::AutoParallelOptions* auto_parallel);
  private:
  const ::tensorflow::AutoParallelOptions& _internal_auto_parallel() const;
  ::tensorflow::AutoParallelOptions* _internal_mutable_auto_parallel();
  public:
  void unsafe_arena_set_allocated_auto_parallel(
      ::tensorflow::AutoParallelOptions* auto_parallel);
  ::tensorflow::AutoParallelOptions* unsafe_arena_release_auto_parallel();

  // .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
  bool has_scoped_allocator_opts() const;
  private:
  bool _internal_has_scoped_allocator_opts() const;
  public:
  void clear_scoped_allocator_opts();
  const ::tensorflow::ScopedAllocatorOptions& scoped_allocator_opts() const;
  PROTOBUF_NODISCARD ::tensorflow::ScopedAllocatorOptions* release_scoped_allocator_opts();
  ::tensorflow::ScopedAllocatorOptions* mutable_scoped_allocator_opts();
  void set_allocated_scoped_allocator_opts(::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts);
  private:
  const ::tensorflow::ScopedAllocatorOptions& _internal_scoped_allocator_opts() const;
  ::tensorflow::ScopedAllocatorOptions* _internal_mutable_scoped_allocator_opts();
  public:
  void unsafe_arena_set_allocated_scoped_allocator_opts(
      ::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts);
  ::tensorflow::ScopedAllocatorOptions* unsafe_arena_release_scoped_allocator_opts();

  // .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
  bool has_inter_optimizer_verifier_config() const;
  private:
  bool _internal_has_inter_optimizer_verifier_config() const;
  public:
  void clear_inter_optimizer_verifier_config();
  const ::tensorflow::VerifierConfig& inter_optimizer_verifier_config() const;
  PROTOBUF_NODISCARD ::tensorflow::VerifierConfig* release_inter_optimizer_verifier_config();
  ::tensorflow::VerifierConfig* mutable_inter_optimizer_verifier_config();
  void set_allocated_inter_optimizer_verifier_config(::tensorflow::VerifierConfig* inter_optimizer_verifier_config);
  private:
  const ::tensorflow::VerifierConfig& _internal_inter_optimizer_verifier_config() const;
  ::tensorflow::VerifierConfig* _internal_mutable_inter_optimizer_verifier_config();
  public:
  void unsafe_arena_set_allocated_inter_optimizer_verifier_config(
      ::tensorflow::VerifierConfig* inter_optimizer_verifier_config);
  ::tensorflow::VerifierConfig* unsafe_arena_release_inter_optimizer_verifier_config();

  // .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
  bool has_post_optimization_verifier_config() const;
  private:
  bool _internal_has_post_optimization_verifier_config() const;
  public:
  void clear_post_optimization_verifier_config();
  const ::tensorflow::VerifierConfig& post_optimization_verifier_config() const;
  PROTOBUF_NODISCARD ::tensorflow::VerifierConfig* release_post_optimization_verifier_config();
  ::tensorflow::VerifierConfig* mutable_post_optimization_verifier_config();
  void set_allocated_post_optimization_verifier_config(::tensorflow::VerifierConfig* post_optimization_verifier_config);
  private:
  const ::tensorflow::VerifierConfig& _internal_post_optimization_verifier_config() const;
  ::tensorflow::VerifierConfig* _internal_mutable_post_optimization_verifier_config();
  public:
  void unsafe_arena_set_allocated_post_optimization_verifier_config(
      ::tensorflow::VerifierConfig* post_optimization_verifier_config);
  ::tensorflow::VerifierConfig* unsafe_arena_release_post_optimization_verifier_config();

  // .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
  void clear_layout_optimizer();
  ::tensorflow::RewriterConfig_Toggle layout_optimizer() const;
  void set_layout_optimizer(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_layout_optimizer() const;
  void _internal_set_layout_optimizer(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle constant_folding = 3;
  void clear_constant_folding();
  ::tensorflow::RewriterConfig_Toggle constant_folding() const;
  void set_constant_folding(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_constant_folding() const;
  void _internal_set_constant_folding(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
  void clear_memory_optimization();
  ::tensorflow::RewriterConfig_MemOptType memory_optimization() const;
  void set_memory_optimization(::tensorflow::RewriterConfig_MemOptType value);
  private:
  ::tensorflow::RewriterConfig_MemOptType _internal_memory_optimization() const;
  void _internal_set_memory_optimization(::tensorflow::RewriterConfig_MemOptType value);
  public:

  // .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
  void clear_arithmetic_optimization();
  ::tensorflow::RewriterConfig_Toggle arithmetic_optimization() const;
  void set_arithmetic_optimization(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_arithmetic_optimization() const;
  void _internal_set_arithmetic_optimization(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
  void clear_dependency_optimization();
  ::tensorflow::RewriterConfig_Toggle dependency_optimization() const;
  void set_dependency_optimization(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_dependency_optimization() const;
  void _internal_set_dependency_optimization(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
  void clear_loop_optimization();
  ::tensorflow::RewriterConfig_Toggle loop_optimization() const;
  void set_loop_optimization(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_loop_optimization() const;
  void _internal_set_loop_optimization(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle function_optimization = 10;
  void clear_function_optimization();
  ::tensorflow::RewriterConfig_Toggle function_optimization() const;
  void set_function_optimization(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_function_optimization() const;
  void _internal_set_function_optimization(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
  void clear_debug_stripper();
  ::tensorflow::RewriterConfig_Toggle debug_stripper() const;
  void set_debug_stripper(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_debug_stripper() const;
  void _internal_set_debug_stripper(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
  void clear_meta_optimizer_iterations();
  ::tensorflow::RewriterConfig_NumIterationsType meta_optimizer_iterations() const;
  void set_meta_optimizer_iterations(::tensorflow::RewriterConfig_NumIterationsType value);
  private:
  ::tensorflow::RewriterConfig_NumIterationsType _internal_meta_optimizer_iterations() const;
  void _internal_set_meta_optimizer_iterations(::tensorflow::RewriterConfig_NumIterationsType value);
  public:

  // .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
  void clear_shape_optimization();
  ::tensorflow::RewriterConfig_Toggle shape_optimization() const;
  void set_shape_optimization(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_shape_optimization() const;
  void _internal_set_shape_optimization(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle remapping = 14;
  void clear_remapping();
  ::tensorflow::RewriterConfig_Toggle remapping() const;
  void set_remapping(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_remapping() const;
  void _internal_set_remapping(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
  void clear_scoped_allocator_optimization();
  ::tensorflow::RewriterConfig_Toggle scoped_allocator_optimization() const;
  void set_scoped_allocator_optimization(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_scoped_allocator_optimization() const;
  void _internal_set_scoped_allocator_optimization(::tensorflow::RewriterConfig_Toggle value);
  public:

  // int32 min_graph_nodes = 17;
  void clear_min_graph_nodes();
  int32_t min_graph_nodes() const;
  void set_min_graph_nodes(int32_t value);
  private:
  int32_t _internal_min_graph_nodes() const;
  void _internal_set_min_graph_nodes(int32_t value);
  public:

  // .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
  void clear_pin_to_host_optimization();
  ::tensorflow::RewriterConfig_Toggle pin_to_host_optimization() const;
  void set_pin_to_host_optimization(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_pin_to_host_optimization() const;
  void _internal_set_pin_to_host_optimization(::tensorflow::RewriterConfig_Toggle value);
  public:

  // int64 meta_optimizer_timeout_ms = 20;
  void clear_meta_optimizer_timeout_ms();
  int64_t meta_optimizer_timeout_ms() const;
  void set_meta_optimizer_timeout_ms(int64_t value);
  private:
  int64_t _internal_meta_optimizer_timeout_ms() const;
  void _internal_set_meta_optimizer_timeout_ms(int64_t value);
  public:

  // bool disable_model_pruning = 2;
  void clear_disable_model_pruning();
  bool disable_model_pruning() const;
  void set_disable_model_pruning(bool value);
  private:
  bool _internal_disable_model_pruning() const;
  void _internal_set_disable_model_pruning(bool value);
  public:

  // bool disable_meta_optimizer = 19;
  void clear_disable_meta_optimizer();
  bool disable_meta_optimizer() const;
  void set_disable_meta_optimizer(bool value);
  private:
  bool _internal_disable_meta_optimizer() const;
  void _internal_set_disable_meta_optimizer(bool value);
  public:

  // bool disable_tfg_optimizer = 32;
  void clear_disable_tfg_optimizer();
  bool disable_tfg_optimizer() const;
  void set_disable_tfg_optimizer(bool value);
  private:
  bool _internal_disable_tfg_optimizer() const;
  void _internal_set_disable_tfg_optimizer(bool value);
  public:

  // bool experimental_disable_compressed_tensor_optimization = 26;
  void clear_experimental_disable_compressed_tensor_optimization();
  bool experimental_disable_compressed_tensor_optimization() const;
  void set_experimental_disable_compressed_tensor_optimization(bool value);
  private:
  bool _internal_experimental_disable_compressed_tensor_optimization() const;
  void _internal_set_experimental_disable_compressed_tensor_optimization(bool value);
  public:

  // .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
  void clear_implementation_selector();
  ::tensorflow::RewriterConfig_Toggle implementation_selector() const;
  void set_implementation_selector(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_implementation_selector() const;
  void _internal_set_implementation_selector(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle auto_mixed_precision = 23;
  void clear_auto_mixed_precision();
  ::tensorflow::RewriterConfig_Toggle auto_mixed_precision() const;
  void set_auto_mixed_precision(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_auto_mixed_precision() const;
  void _internal_set_auto_mixed_precision(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle common_subgraph_elimination = 24;
  void clear_common_subgraph_elimination();
  ::tensorflow::RewriterConfig_Toggle common_subgraph_elimination() const;
  void set_common_subgraph_elimination(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_common_subgraph_elimination() const;
  void _internal_set_common_subgraph_elimination(::tensorflow::RewriterConfig_Toggle value);
  public:

  // bool experimental_disable_folding_quantization_emulation = 27;
  void clear_experimental_disable_folding_quantization_emulation();
  bool experimental_disable_folding_quantization_emulation() const;
  void set_experimental_disable_folding_quantization_emulation(bool value);
  private:
  bool _internal_experimental_disable_folding_quantization_emulation() const;
  void _internal_set_experimental_disable_folding_quantization_emulation(bool value);
  public:

  // bool fail_on_optimizer_errors = 21;
  void clear_fail_on_optimizer_errors();
  bool fail_on_optimizer_errors() const;
  void set_fail_on_optimizer_errors(bool value);
  private:
  bool _internal_fail_on_optimizer_errors() const;
  void _internal_set_fail_on_optimizer_errors(bool value);
  public:

  // .tensorflow.RewriterConfig.Toggle auto_mixed_precision_mkl = 25;
  void clear_auto_mixed_precision_mkl();
  ::tensorflow::RewriterConfig_Toggle auto_mixed_precision_mkl() const;
  void set_auto_mixed_precision_mkl(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_auto_mixed_precision_mkl() const;
  void _internal_set_auto_mixed_precision_mkl(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle use_plugin_optimizers = 28;
  void clear_use_plugin_optimizers();
  ::tensorflow::RewriterConfig_Toggle use_plugin_optimizers() const;
  void set_use_plugin_optimizers(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_use_plugin_optimizers() const;
  void _internal_set_use_plugin_optimizers(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle auto_mixed_precision_cpu = 29;
  void clear_auto_mixed_precision_cpu();
  ::tensorflow::RewriterConfig_Toggle auto_mixed_precision_cpu() const;
  void set_auto_mixed_precision_cpu(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_auto_mixed_precision_cpu() const;
  void _internal_set_auto_mixed_precision_cpu(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle experimental_conditional_code_motion = 30;
  void clear_experimental_conditional_code_motion();
  ::tensorflow::RewriterConfig_Toggle experimental_conditional_code_motion() const;
  void set_experimental_conditional_code_motion(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_experimental_conditional_code_motion() const;
  void _internal_set_experimental_conditional_code_motion(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.Toggle auto_mixed_precision_onednn_bfloat16 = 31;
  void clear_auto_mixed_precision_onednn_bfloat16();
  ::tensorflow::RewriterConfig_Toggle auto_mixed_precision_onednn_bfloat16() const;
  void set_auto_mixed_precision_onednn_bfloat16(::tensorflow::RewriterConfig_Toggle value);
  private:
  ::tensorflow::RewriterConfig_Toggle _internal_auto_mixed_precision_onednn_bfloat16() const;
  void _internal_set_auto_mixed_precision_onednn_bfloat16(::tensorflow::RewriterConfig_Toggle value);
  public:

  // .tensorflow.RewriterConfig.CpuLayout cpu_layout_conversion = 50;
  void clear_cpu_layout_conversion();
  ::tensorflow::RewriterConfig_CpuLayout cpu_layout_conversion() const;
  void set_cpu_layout_conversion(::tensorflow::RewriterConfig_CpuLayout value);
  private:
  ::tensorflow::RewriterConfig_CpuLayout _internal_cpu_layout_conversion() const;
  void _internal_set_cpu_layout_conversion(::tensorflow::RewriterConfig_CpuLayout value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RewriterConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> optimizers_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer > custom_optimizers_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr memory_optimizer_target_node_name_scope_;
    ::tensorflow::AutoParallelOptions* auto_parallel_;
    ::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts_;
    ::tensorflow::VerifierConfig* inter_optimizer_verifier_config_;
    ::tensorflow::VerifierConfig* post_optimization_verifier_config_;
    int layout_optimizer_;
    int constant_folding_;
    int memory_optimization_;
    int arithmetic_optimization_;
    int dependency_optimization_;
    int loop_optimization_;
    int function_optimization_;
    int debug_stripper_;
    int meta_optimizer_iterations_;
    int shape_optimization_;
    int remapping_;
    int scoped_allocator_optimization_;
    int32_t min_graph_nodes_;
    int pin_to_host_optimization_;
    int64_t meta_optimizer_timeout_ms_;
    bool disable_model_pruning_;
    bool disable_meta_optimizer_;
    bool disable_tfg_optimizer_;
    bool experimental_disable_compressed_tensor_optimization_;
    int implementation_selector_;
    int auto_mixed_precision_;
    int common_subgraph_elimination_;
    bool experimental_disable_folding_quantization_emulation_;
    bool fail_on_optimizer_errors_;
    int auto_mixed_precision_mkl_;
    int use_plugin_optimizers_;
    int auto_mixed_precision_cpu_;
    int experimental_conditional_code_motion_;
    int auto_mixed_precision_onednn_bfloat16_;
    int cpu_layout_conversion_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AutoParallelOptions

// bool enable = 1;
inline void AutoParallelOptions::clear_enable() {
  _impl_.enable_ = false;
}
inline bool AutoParallelOptions::_internal_enable() const {
  return _impl_.enable_;
}
inline bool AutoParallelOptions::enable() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutoParallelOptions.enable)
  return _internal_enable();
}
inline void AutoParallelOptions::_internal_set_enable(bool value) {
  
  _impl_.enable_ = value;
}
inline void AutoParallelOptions::set_enable(bool value) {
  _internal_set_enable(value);
  // @@protoc_insertion_point(field_set:tensorflow.AutoParallelOptions.enable)
}

// int32 num_replicas = 2;
inline void AutoParallelOptions::clear_num_replicas() {
  _impl_.num_replicas_ = 0;
}
inline int32_t AutoParallelOptions::_internal_num_replicas() const {
  return _impl_.num_replicas_;
}
inline int32_t AutoParallelOptions::num_replicas() const {
  // @@protoc_insertion_point(field_get:tensorflow.AutoParallelOptions.num_replicas)
  return _internal_num_replicas();
}
inline void AutoParallelOptions::_internal_set_num_replicas(int32_t value) {
  
  _impl_.num_replicas_ = value;
}
inline void AutoParallelOptions::set_num_replicas(int32_t value) {
  _internal_set_num_replicas(value);
  // @@protoc_insertion_point(field_set:tensorflow.AutoParallelOptions.num_replicas)
}

// -------------------------------------------------------------------

// ScopedAllocatorOptions

// repeated string enable_op = 1;
inline int ScopedAllocatorOptions::_internal_enable_op_size() const {
  return _impl_.enable_op_.size();
}
inline int ScopedAllocatorOptions::enable_op_size() const {
  return _internal_enable_op_size();
}
inline void ScopedAllocatorOptions::clear_enable_op() {
  _impl_.enable_op_.Clear();
}
inline std::string* ScopedAllocatorOptions::add_enable_op() {
  std::string* _s = _internal_add_enable_op();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ScopedAllocatorOptions.enable_op)
  return _s;
}
inline const std::string& ScopedAllocatorOptions::_internal_enable_op(int index) const {
  return _impl_.enable_op_.Get(index);
}
inline const std::string& ScopedAllocatorOptions::enable_op(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ScopedAllocatorOptions.enable_op)
  return _internal_enable_op(index);
}
inline std::string* ScopedAllocatorOptions::mutable_enable_op(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ScopedAllocatorOptions.enable_op)
  return _impl_.enable_op_.Mutable(index);
}
inline void ScopedAllocatorOptions::set_enable_op(int index, const std::string& value) {
  _impl_.enable_op_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::set_enable_op(int index, std::string&& value) {
  _impl_.enable_op_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::set_enable_op(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.enable_op_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::set_enable_op(int index, const char* value, size_t size) {
  _impl_.enable_op_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline std::string* ScopedAllocatorOptions::_internal_add_enable_op() {
  return _impl_.enable_op_.Add();
}
inline void ScopedAllocatorOptions::add_enable_op(const std::string& value) {
  _impl_.enable_op_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::add_enable_op(std::string&& value) {
  _impl_.enable_op_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::add_enable_op(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.enable_op_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline void ScopedAllocatorOptions::add_enable_op(const char* value, size_t size) {
  _impl_.enable_op_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ScopedAllocatorOptions.enable_op)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ScopedAllocatorOptions::enable_op() const {
  // @@protoc_insertion_point(field_list:tensorflow.ScopedAllocatorOptions.enable_op)
  return _impl_.enable_op_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ScopedAllocatorOptions::mutable_enable_op() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ScopedAllocatorOptions.enable_op)
  return &_impl_.enable_op_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// RewriterConfig_CustomGraphOptimizer

// string name = 1;
inline void RewriterConfig_CustomGraphOptimizer::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& RewriterConfig_CustomGraphOptimizer::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RewriterConfig_CustomGraphOptimizer::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}
inline std::string* RewriterConfig_CustomGraphOptimizer::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  return _s;
}
inline const std::string& RewriterConfig_CustomGraphOptimizer::_internal_name() const {
  return _impl_.name_.Get();
}
inline void RewriterConfig_CustomGraphOptimizer::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* RewriterConfig_CustomGraphOptimizer::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* RewriterConfig_CustomGraphOptimizer::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
  return _impl_.name_.Release();
}
inline void RewriterConfig_CustomGraphOptimizer::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.CustomGraphOptimizer.name)
}

// map<string, .tensorflow.AttrValue> parameter_map = 2;
inline int RewriterConfig_CustomGraphOptimizer::_internal_parameter_map_size() const {
  return _impl_.parameter_map_.size();
}
inline int RewriterConfig_CustomGraphOptimizer::parameter_map_size() const {
  return _internal_parameter_map_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
RewriterConfig_CustomGraphOptimizer::_internal_parameter_map() const {
  return _impl_.parameter_map_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
RewriterConfig_CustomGraphOptimizer::parameter_map() const {
  // @@protoc_insertion_point(field_map:tensorflow.RewriterConfig.CustomGraphOptimizer.parameter_map)
  return _internal_parameter_map();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
RewriterConfig_CustomGraphOptimizer::_internal_mutable_parameter_map() {
  return _impl_.parameter_map_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
RewriterConfig_CustomGraphOptimizer::mutable_parameter_map() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.RewriterConfig.CustomGraphOptimizer.parameter_map)
  return _internal_mutable_parameter_map();
}

// -------------------------------------------------------------------

// RewriterConfig

// .tensorflow.RewriterConfig.CpuLayout cpu_layout_conversion = 50;
inline void RewriterConfig::clear_cpu_layout_conversion() {
  _impl_.cpu_layout_conversion_ = 0;
}
inline ::tensorflow::RewriterConfig_CpuLayout RewriterConfig::_internal_cpu_layout_conversion() const {
  return static_cast< ::tensorflow::RewriterConfig_CpuLayout >(_impl_.cpu_layout_conversion_);
}
inline ::tensorflow::RewriterConfig_CpuLayout RewriterConfig::cpu_layout_conversion() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.cpu_layout_conversion)
  return _internal_cpu_layout_conversion();
}
inline void RewriterConfig::_internal_set_cpu_layout_conversion(::tensorflow::RewriterConfig_CpuLayout value) {
  
  _impl_.cpu_layout_conversion_ = value;
}
inline void RewriterConfig::set_cpu_layout_conversion(::tensorflow::RewriterConfig_CpuLayout value) {
  _internal_set_cpu_layout_conversion(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.cpu_layout_conversion)
}

// .tensorflow.RewriterConfig.Toggle layout_optimizer = 1;
inline void RewriterConfig::clear_layout_optimizer() {
  _impl_.layout_optimizer_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_layout_optimizer() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.layout_optimizer_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::layout_optimizer() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.layout_optimizer)
  return _internal_layout_optimizer();
}
inline void RewriterConfig::_internal_set_layout_optimizer(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.layout_optimizer_ = value;
}
inline void RewriterConfig::set_layout_optimizer(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_layout_optimizer(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.layout_optimizer)
}

// .tensorflow.RewriterConfig.Toggle constant_folding = 3;
inline void RewriterConfig::clear_constant_folding() {
  _impl_.constant_folding_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_constant_folding() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.constant_folding_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::constant_folding() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.constant_folding)
  return _internal_constant_folding();
}
inline void RewriterConfig::_internal_set_constant_folding(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.constant_folding_ = value;
}
inline void RewriterConfig::set_constant_folding(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_constant_folding(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.constant_folding)
}

// .tensorflow.RewriterConfig.Toggle shape_optimization = 13;
inline void RewriterConfig::clear_shape_optimization() {
  _impl_.shape_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_shape_optimization() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.shape_optimization_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::shape_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.shape_optimization)
  return _internal_shape_optimization();
}
inline void RewriterConfig::_internal_set_shape_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.shape_optimization_ = value;
}
inline void RewriterConfig::set_shape_optimization(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_shape_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.shape_optimization)
}

// .tensorflow.RewriterConfig.Toggle remapping = 14;
inline void RewriterConfig::clear_remapping() {
  _impl_.remapping_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_remapping() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.remapping_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::remapping() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.remapping)
  return _internal_remapping();
}
inline void RewriterConfig::_internal_set_remapping(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.remapping_ = value;
}
inline void RewriterConfig::set_remapping(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_remapping(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.remapping)
}

// .tensorflow.RewriterConfig.Toggle common_subgraph_elimination = 24;
inline void RewriterConfig::clear_common_subgraph_elimination() {
  _impl_.common_subgraph_elimination_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_common_subgraph_elimination() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.common_subgraph_elimination_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::common_subgraph_elimination() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.common_subgraph_elimination)
  return _internal_common_subgraph_elimination();
}
inline void RewriterConfig::_internal_set_common_subgraph_elimination(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.common_subgraph_elimination_ = value;
}
inline void RewriterConfig::set_common_subgraph_elimination(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_common_subgraph_elimination(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.common_subgraph_elimination)
}

// .tensorflow.RewriterConfig.Toggle arithmetic_optimization = 7;
inline void RewriterConfig::clear_arithmetic_optimization() {
  _impl_.arithmetic_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_arithmetic_optimization() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.arithmetic_optimization_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::arithmetic_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.arithmetic_optimization)
  return _internal_arithmetic_optimization();
}
inline void RewriterConfig::_internal_set_arithmetic_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.arithmetic_optimization_ = value;
}
inline void RewriterConfig::set_arithmetic_optimization(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_arithmetic_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.arithmetic_optimization)
}

// .tensorflow.RewriterConfig.Toggle dependency_optimization = 8;
inline void RewriterConfig::clear_dependency_optimization() {
  _impl_.dependency_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_dependency_optimization() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.dependency_optimization_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::dependency_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.dependency_optimization)
  return _internal_dependency_optimization();
}
inline void RewriterConfig::_internal_set_dependency_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.dependency_optimization_ = value;
}
inline void RewriterConfig::set_dependency_optimization(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_dependency_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.dependency_optimization)
}

// .tensorflow.RewriterConfig.Toggle loop_optimization = 9;
inline void RewriterConfig::clear_loop_optimization() {
  _impl_.loop_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_loop_optimization() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.loop_optimization_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::loop_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.loop_optimization)
  return _internal_loop_optimization();
}
inline void RewriterConfig::_internal_set_loop_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.loop_optimization_ = value;
}
inline void RewriterConfig::set_loop_optimization(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_loop_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.loop_optimization)
}

// .tensorflow.RewriterConfig.Toggle function_optimization = 10;
inline void RewriterConfig::clear_function_optimization() {
  _impl_.function_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_function_optimization() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.function_optimization_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::function_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.function_optimization)
  return _internal_function_optimization();
}
inline void RewriterConfig::_internal_set_function_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.function_optimization_ = value;
}
inline void RewriterConfig::set_function_optimization(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_function_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.function_optimization)
}

// .tensorflow.RewriterConfig.Toggle debug_stripper = 11;
inline void RewriterConfig::clear_debug_stripper() {
  _impl_.debug_stripper_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_debug_stripper() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.debug_stripper_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::debug_stripper() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.debug_stripper)
  return _internal_debug_stripper();
}
inline void RewriterConfig::_internal_set_debug_stripper(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.debug_stripper_ = value;
}
inline void RewriterConfig::set_debug_stripper(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_debug_stripper(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.debug_stripper)
}

// bool disable_model_pruning = 2;
inline void RewriterConfig::clear_disable_model_pruning() {
  _impl_.disable_model_pruning_ = false;
}
inline bool RewriterConfig::_internal_disable_model_pruning() const {
  return _impl_.disable_model_pruning_;
}
inline bool RewriterConfig::disable_model_pruning() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.disable_model_pruning)
  return _internal_disable_model_pruning();
}
inline void RewriterConfig::_internal_set_disable_model_pruning(bool value) {
  
  _impl_.disable_model_pruning_ = value;
}
inline void RewriterConfig::set_disable_model_pruning(bool value) {
  _internal_set_disable_model_pruning(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.disable_model_pruning)
}

// .tensorflow.RewriterConfig.Toggle scoped_allocator_optimization = 15;
inline void RewriterConfig::clear_scoped_allocator_optimization() {
  _impl_.scoped_allocator_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_scoped_allocator_optimization() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.scoped_allocator_optimization_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::scoped_allocator_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.scoped_allocator_optimization)
  return _internal_scoped_allocator_optimization();
}
inline void RewriterConfig::_internal_set_scoped_allocator_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.scoped_allocator_optimization_ = value;
}
inline void RewriterConfig::set_scoped_allocator_optimization(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_scoped_allocator_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.scoped_allocator_optimization)
}

// .tensorflow.RewriterConfig.Toggle pin_to_host_optimization = 18;
inline void RewriterConfig::clear_pin_to_host_optimization() {
  _impl_.pin_to_host_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_pin_to_host_optimization() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.pin_to_host_optimization_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::pin_to_host_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.pin_to_host_optimization)
  return _internal_pin_to_host_optimization();
}
inline void RewriterConfig::_internal_set_pin_to_host_optimization(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.pin_to_host_optimization_ = value;
}
inline void RewriterConfig::set_pin_to_host_optimization(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_pin_to_host_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.pin_to_host_optimization)
}

// .tensorflow.RewriterConfig.Toggle implementation_selector = 22;
inline void RewriterConfig::clear_implementation_selector() {
  _impl_.implementation_selector_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_implementation_selector() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.implementation_selector_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::implementation_selector() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.implementation_selector)
  return _internal_implementation_selector();
}
inline void RewriterConfig::_internal_set_implementation_selector(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.implementation_selector_ = value;
}
inline void RewriterConfig::set_implementation_selector(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_implementation_selector(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.implementation_selector)
}

// .tensorflow.RewriterConfig.Toggle auto_mixed_precision = 23;
inline void RewriterConfig::clear_auto_mixed_precision() {
  _impl_.auto_mixed_precision_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_auto_mixed_precision() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.auto_mixed_precision_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::auto_mixed_precision() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_mixed_precision)
  return _internal_auto_mixed_precision();
}
inline void RewriterConfig::_internal_set_auto_mixed_precision(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.auto_mixed_precision_ = value;
}
inline void RewriterConfig::set_auto_mixed_precision(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_auto_mixed_precision(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.auto_mixed_precision)
}

// .tensorflow.RewriterConfig.Toggle auto_mixed_precision_mkl = 25;
inline void RewriterConfig::clear_auto_mixed_precision_mkl() {
  _impl_.auto_mixed_precision_mkl_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_auto_mixed_precision_mkl() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.auto_mixed_precision_mkl_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::auto_mixed_precision_mkl() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_mixed_precision_mkl)
  return _internal_auto_mixed_precision_mkl();
}
inline void RewriterConfig::_internal_set_auto_mixed_precision_mkl(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.auto_mixed_precision_mkl_ = value;
}
inline void RewriterConfig::set_auto_mixed_precision_mkl(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_auto_mixed_precision_mkl(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.auto_mixed_precision_mkl)
}

// .tensorflow.RewriterConfig.Toggle auto_mixed_precision_onednn_bfloat16 = 31;
inline void RewriterConfig::clear_auto_mixed_precision_onednn_bfloat16() {
  _impl_.auto_mixed_precision_onednn_bfloat16_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_auto_mixed_precision_onednn_bfloat16() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.auto_mixed_precision_onednn_bfloat16_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::auto_mixed_precision_onednn_bfloat16() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_mixed_precision_onednn_bfloat16)
  return _internal_auto_mixed_precision_onednn_bfloat16();
}
inline void RewriterConfig::_internal_set_auto_mixed_precision_onednn_bfloat16(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.auto_mixed_precision_onednn_bfloat16_ = value;
}
inline void RewriterConfig::set_auto_mixed_precision_onednn_bfloat16(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_auto_mixed_precision_onednn_bfloat16(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.auto_mixed_precision_onednn_bfloat16)
}

// .tensorflow.RewriterConfig.Toggle auto_mixed_precision_cpu = 29;
inline void RewriterConfig::clear_auto_mixed_precision_cpu() {
  _impl_.auto_mixed_precision_cpu_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_auto_mixed_precision_cpu() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.auto_mixed_precision_cpu_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::auto_mixed_precision_cpu() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_mixed_precision_cpu)
  return _internal_auto_mixed_precision_cpu();
}
inline void RewriterConfig::_internal_set_auto_mixed_precision_cpu(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.auto_mixed_precision_cpu_ = value;
}
inline void RewriterConfig::set_auto_mixed_precision_cpu(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_auto_mixed_precision_cpu(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.auto_mixed_precision_cpu)
}

// bool disable_meta_optimizer = 19;
inline void RewriterConfig::clear_disable_meta_optimizer() {
  _impl_.disable_meta_optimizer_ = false;
}
inline bool RewriterConfig::_internal_disable_meta_optimizer() const {
  return _impl_.disable_meta_optimizer_;
}
inline bool RewriterConfig::disable_meta_optimizer() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.disable_meta_optimizer)
  return _internal_disable_meta_optimizer();
}
inline void RewriterConfig::_internal_set_disable_meta_optimizer(bool value) {
  
  _impl_.disable_meta_optimizer_ = value;
}
inline void RewriterConfig::set_disable_meta_optimizer(bool value) {
  _internal_set_disable_meta_optimizer(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.disable_meta_optimizer)
}

// bool disable_tfg_optimizer = 32;
inline void RewriterConfig::clear_disable_tfg_optimizer() {
  _impl_.disable_tfg_optimizer_ = false;
}
inline bool RewriterConfig::_internal_disable_tfg_optimizer() const {
  return _impl_.disable_tfg_optimizer_;
}
inline bool RewriterConfig::disable_tfg_optimizer() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.disable_tfg_optimizer)
  return _internal_disable_tfg_optimizer();
}
inline void RewriterConfig::_internal_set_disable_tfg_optimizer(bool value) {
  
  _impl_.disable_tfg_optimizer_ = value;
}
inline void RewriterConfig::set_disable_tfg_optimizer(bool value) {
  _internal_set_disable_tfg_optimizer(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.disable_tfg_optimizer)
}

// .tensorflow.RewriterConfig.Toggle use_plugin_optimizers = 28;
inline void RewriterConfig::clear_use_plugin_optimizers() {
  _impl_.use_plugin_optimizers_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_use_plugin_optimizers() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.use_plugin_optimizers_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::use_plugin_optimizers() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.use_plugin_optimizers)
  return _internal_use_plugin_optimizers();
}
inline void RewriterConfig::_internal_set_use_plugin_optimizers(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.use_plugin_optimizers_ = value;
}
inline void RewriterConfig::set_use_plugin_optimizers(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_use_plugin_optimizers(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.use_plugin_optimizers)
}

// .tensorflow.RewriterConfig.Toggle experimental_conditional_code_motion = 30;
inline void RewriterConfig::clear_experimental_conditional_code_motion() {
  _impl_.experimental_conditional_code_motion_ = 0;
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::_internal_experimental_conditional_code_motion() const {
  return static_cast< ::tensorflow::RewriterConfig_Toggle >(_impl_.experimental_conditional_code_motion_);
}
inline ::tensorflow::RewriterConfig_Toggle RewriterConfig::experimental_conditional_code_motion() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.experimental_conditional_code_motion)
  return _internal_experimental_conditional_code_motion();
}
inline void RewriterConfig::_internal_set_experimental_conditional_code_motion(::tensorflow::RewriterConfig_Toggle value) {
  
  _impl_.experimental_conditional_code_motion_ = value;
}
inline void RewriterConfig::set_experimental_conditional_code_motion(::tensorflow::RewriterConfig_Toggle value) {
  _internal_set_experimental_conditional_code_motion(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.experimental_conditional_code_motion)
}

// .tensorflow.RewriterConfig.NumIterationsType meta_optimizer_iterations = 12;
inline void RewriterConfig::clear_meta_optimizer_iterations() {
  _impl_.meta_optimizer_iterations_ = 0;
}
inline ::tensorflow::RewriterConfig_NumIterationsType RewriterConfig::_internal_meta_optimizer_iterations() const {
  return static_cast< ::tensorflow::RewriterConfig_NumIterationsType >(_impl_.meta_optimizer_iterations_);
}
inline ::tensorflow::RewriterConfig_NumIterationsType RewriterConfig::meta_optimizer_iterations() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.meta_optimizer_iterations)
  return _internal_meta_optimizer_iterations();
}
inline void RewriterConfig::_internal_set_meta_optimizer_iterations(::tensorflow::RewriterConfig_NumIterationsType value) {
  
  _impl_.meta_optimizer_iterations_ = value;
}
inline void RewriterConfig::set_meta_optimizer_iterations(::tensorflow::RewriterConfig_NumIterationsType value) {
  _internal_set_meta_optimizer_iterations(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.meta_optimizer_iterations)
}

// int32 min_graph_nodes = 17;
inline void RewriterConfig::clear_min_graph_nodes() {
  _impl_.min_graph_nodes_ = 0;
}
inline int32_t RewriterConfig::_internal_min_graph_nodes() const {
  return _impl_.min_graph_nodes_;
}
inline int32_t RewriterConfig::min_graph_nodes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.min_graph_nodes)
  return _internal_min_graph_nodes();
}
inline void RewriterConfig::_internal_set_min_graph_nodes(int32_t value) {
  
  _impl_.min_graph_nodes_ = value;
}
inline void RewriterConfig::set_min_graph_nodes(int32_t value) {
  _internal_set_min_graph_nodes(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.min_graph_nodes)
}

// bool experimental_disable_compressed_tensor_optimization = 26;
inline void RewriterConfig::clear_experimental_disable_compressed_tensor_optimization() {
  _impl_.experimental_disable_compressed_tensor_optimization_ = false;
}
inline bool RewriterConfig::_internal_experimental_disable_compressed_tensor_optimization() const {
  return _impl_.experimental_disable_compressed_tensor_optimization_;
}
inline bool RewriterConfig::experimental_disable_compressed_tensor_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.experimental_disable_compressed_tensor_optimization)
  return _internal_experimental_disable_compressed_tensor_optimization();
}
inline void RewriterConfig::_internal_set_experimental_disable_compressed_tensor_optimization(bool value) {
  
  _impl_.experimental_disable_compressed_tensor_optimization_ = value;
}
inline void RewriterConfig::set_experimental_disable_compressed_tensor_optimization(bool value) {
  _internal_set_experimental_disable_compressed_tensor_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.experimental_disable_compressed_tensor_optimization)
}

// bool experimental_disable_folding_quantization_emulation = 27;
inline void RewriterConfig::clear_experimental_disable_folding_quantization_emulation() {
  _impl_.experimental_disable_folding_quantization_emulation_ = false;
}
inline bool RewriterConfig::_internal_experimental_disable_folding_quantization_emulation() const {
  return _impl_.experimental_disable_folding_quantization_emulation_;
}
inline bool RewriterConfig::experimental_disable_folding_quantization_emulation() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.experimental_disable_folding_quantization_emulation)
  return _internal_experimental_disable_folding_quantization_emulation();
}
inline void RewriterConfig::_internal_set_experimental_disable_folding_quantization_emulation(bool value) {
  
  _impl_.experimental_disable_folding_quantization_emulation_ = value;
}
inline void RewriterConfig::set_experimental_disable_folding_quantization_emulation(bool value) {
  _internal_set_experimental_disable_folding_quantization_emulation(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.experimental_disable_folding_quantization_emulation)
}

// .tensorflow.RewriterConfig.MemOptType memory_optimization = 4;
inline void RewriterConfig::clear_memory_optimization() {
  _impl_.memory_optimization_ = 0;
}
inline ::tensorflow::RewriterConfig_MemOptType RewriterConfig::_internal_memory_optimization() const {
  return static_cast< ::tensorflow::RewriterConfig_MemOptType >(_impl_.memory_optimization_);
}
inline ::tensorflow::RewriterConfig_MemOptType RewriterConfig::memory_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.memory_optimization)
  return _internal_memory_optimization();
}
inline void RewriterConfig::_internal_set_memory_optimization(::tensorflow::RewriterConfig_MemOptType value) {
  
  _impl_.memory_optimization_ = value;
}
inline void RewriterConfig::set_memory_optimization(::tensorflow::RewriterConfig_MemOptType value) {
  _internal_set_memory_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.memory_optimization)
}

// string memory_optimizer_target_node_name_scope = 6;
inline void RewriterConfig::clear_memory_optimizer_target_node_name_scope() {
  _impl_.memory_optimizer_target_node_name_scope_.ClearToEmpty();
}
inline const std::string& RewriterConfig::memory_optimizer_target_node_name_scope() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  return _internal_memory_optimizer_target_node_name_scope();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RewriterConfig::set_memory_optimizer_target_node_name_scope(ArgT0&& arg0, ArgT... args) {
 
 _impl_.memory_optimizer_target_node_name_scope_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}
inline std::string* RewriterConfig::mutable_memory_optimizer_target_node_name_scope() {
  std::string* _s = _internal_mutable_memory_optimizer_target_node_name_scope();
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  return _s;
}
inline const std::string& RewriterConfig::_internal_memory_optimizer_target_node_name_scope() const {
  return _impl_.memory_optimizer_target_node_name_scope_.Get();
}
inline void RewriterConfig::_internal_set_memory_optimizer_target_node_name_scope(const std::string& value) {
  
  _impl_.memory_optimizer_target_node_name_scope_.Set(value, GetArenaForAllocation());
}
inline std::string* RewriterConfig::_internal_mutable_memory_optimizer_target_node_name_scope() {
  
  return _impl_.memory_optimizer_target_node_name_scope_.Mutable(GetArenaForAllocation());
}
inline std::string* RewriterConfig::release_memory_optimizer_target_node_name_scope() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
  return _impl_.memory_optimizer_target_node_name_scope_.Release();
}
inline void RewriterConfig::set_allocated_memory_optimizer_target_node_name_scope(std::string* memory_optimizer_target_node_name_scope) {
  if (memory_optimizer_target_node_name_scope != nullptr) {
    
  } else {
    
  }
  _impl_.memory_optimizer_target_node_name_scope_.SetAllocated(memory_optimizer_target_node_name_scope, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.memory_optimizer_target_node_name_scope_.IsDefault()) {
    _impl_.memory_optimizer_target_node_name_scope_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.memory_optimizer_target_node_name_scope)
}

// int64 meta_optimizer_timeout_ms = 20;
inline void RewriterConfig::clear_meta_optimizer_timeout_ms() {
  _impl_.meta_optimizer_timeout_ms_ = int64_t{0};
}
inline int64_t RewriterConfig::_internal_meta_optimizer_timeout_ms() const {
  return _impl_.meta_optimizer_timeout_ms_;
}
inline int64_t RewriterConfig::meta_optimizer_timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.meta_optimizer_timeout_ms)
  return _internal_meta_optimizer_timeout_ms();
}
inline void RewriterConfig::_internal_set_meta_optimizer_timeout_ms(int64_t value) {
  
  _impl_.meta_optimizer_timeout_ms_ = value;
}
inline void RewriterConfig::set_meta_optimizer_timeout_ms(int64_t value) {
  _internal_set_meta_optimizer_timeout_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.meta_optimizer_timeout_ms)
}

// .tensorflow.AutoParallelOptions auto_parallel = 5;
inline bool RewriterConfig::_internal_has_auto_parallel() const {
  return this != internal_default_instance() && _impl_.auto_parallel_ != nullptr;
}
inline bool RewriterConfig::has_auto_parallel() const {
  return _internal_has_auto_parallel();
}
inline void RewriterConfig::clear_auto_parallel() {
  if (GetArenaForAllocation() == nullptr && _impl_.auto_parallel_ != nullptr) {
    delete _impl_.auto_parallel_;
  }
  _impl_.auto_parallel_ = nullptr;
}
inline const ::tensorflow::AutoParallelOptions& RewriterConfig::_internal_auto_parallel() const {
  const ::tensorflow::AutoParallelOptions* p = _impl_.auto_parallel_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::AutoParallelOptions&>(
      ::tensorflow::_AutoParallelOptions_default_instance_);
}
inline const ::tensorflow::AutoParallelOptions& RewriterConfig::auto_parallel() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.auto_parallel)
  return _internal_auto_parallel();
}
inline void RewriterConfig::unsafe_arena_set_allocated_auto_parallel(
    ::tensorflow::AutoParallelOptions* auto_parallel) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.auto_parallel_);
  }
  _impl_.auto_parallel_ = auto_parallel;
  if (auto_parallel) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.auto_parallel)
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::release_auto_parallel() {
  
  ::tensorflow::AutoParallelOptions* temp = _impl_.auto_parallel_;
  _impl_.auto_parallel_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::unsafe_arena_release_auto_parallel() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.auto_parallel)
  
  ::tensorflow::AutoParallelOptions* temp = _impl_.auto_parallel_;
  _impl_.auto_parallel_ = nullptr;
  return temp;
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::_internal_mutable_auto_parallel() {
  
  if (_impl_.auto_parallel_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AutoParallelOptions>(GetArenaForAllocation());
    _impl_.auto_parallel_ = p;
  }
  return _impl_.auto_parallel_;
}
inline ::tensorflow::AutoParallelOptions* RewriterConfig::mutable_auto_parallel() {
  ::tensorflow::AutoParallelOptions* _msg = _internal_mutable_auto_parallel();
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.auto_parallel)
  return _msg;
}
inline void RewriterConfig::set_allocated_auto_parallel(::tensorflow::AutoParallelOptions* auto_parallel) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.auto_parallel_;
  }
  if (auto_parallel) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(auto_parallel);
    if (message_arena != submessage_arena) {
      auto_parallel = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, auto_parallel, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.auto_parallel_ = auto_parallel;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.auto_parallel)
}

// bool fail_on_optimizer_errors = 21;
inline void RewriterConfig::clear_fail_on_optimizer_errors() {
  _impl_.fail_on_optimizer_errors_ = false;
}
inline bool RewriterConfig::_internal_fail_on_optimizer_errors() const {
  return _impl_.fail_on_optimizer_errors_;
}
inline bool RewriterConfig::fail_on_optimizer_errors() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.fail_on_optimizer_errors)
  return _internal_fail_on_optimizer_errors();
}
inline void RewriterConfig::_internal_set_fail_on_optimizer_errors(bool value) {
  
  _impl_.fail_on_optimizer_errors_ = value;
}
inline void RewriterConfig::set_fail_on_optimizer_errors(bool value) {
  _internal_set_fail_on_optimizer_errors(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.fail_on_optimizer_errors)
}

// .tensorflow.ScopedAllocatorOptions scoped_allocator_opts = 16;
inline bool RewriterConfig::_internal_has_scoped_allocator_opts() const {
  return this != internal_default_instance() && _impl_.scoped_allocator_opts_ != nullptr;
}
inline bool RewriterConfig::has_scoped_allocator_opts() const {
  return _internal_has_scoped_allocator_opts();
}
inline void RewriterConfig::clear_scoped_allocator_opts() {
  if (GetArenaForAllocation() == nullptr && _impl_.scoped_allocator_opts_ != nullptr) {
    delete _impl_.scoped_allocator_opts_;
  }
  _impl_.scoped_allocator_opts_ = nullptr;
}
inline const ::tensorflow::ScopedAllocatorOptions& RewriterConfig::_internal_scoped_allocator_opts() const {
  const ::tensorflow::ScopedAllocatorOptions* p = _impl_.scoped_allocator_opts_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ScopedAllocatorOptions&>(
      ::tensorflow::_ScopedAllocatorOptions_default_instance_);
}
inline const ::tensorflow::ScopedAllocatorOptions& RewriterConfig::scoped_allocator_opts() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.scoped_allocator_opts)
  return _internal_scoped_allocator_opts();
}
inline void RewriterConfig::unsafe_arena_set_allocated_scoped_allocator_opts(
    ::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.scoped_allocator_opts_);
  }
  _impl_.scoped_allocator_opts_ = scoped_allocator_opts;
  if (scoped_allocator_opts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.scoped_allocator_opts)
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::release_scoped_allocator_opts() {
  
  ::tensorflow::ScopedAllocatorOptions* temp = _impl_.scoped_allocator_opts_;
  _impl_.scoped_allocator_opts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::unsafe_arena_release_scoped_allocator_opts() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.scoped_allocator_opts)
  
  ::tensorflow::ScopedAllocatorOptions* temp = _impl_.scoped_allocator_opts_;
  _impl_.scoped_allocator_opts_ = nullptr;
  return temp;
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::_internal_mutable_scoped_allocator_opts() {
  
  if (_impl_.scoped_allocator_opts_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ScopedAllocatorOptions>(GetArenaForAllocation());
    _impl_.scoped_allocator_opts_ = p;
  }
  return _impl_.scoped_allocator_opts_;
}
inline ::tensorflow::ScopedAllocatorOptions* RewriterConfig::mutable_scoped_allocator_opts() {
  ::tensorflow::ScopedAllocatorOptions* _msg = _internal_mutable_scoped_allocator_opts();
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.scoped_allocator_opts)
  return _msg;
}
inline void RewriterConfig::set_allocated_scoped_allocator_opts(::tensorflow::ScopedAllocatorOptions* scoped_allocator_opts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.scoped_allocator_opts_;
  }
  if (scoped_allocator_opts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(scoped_allocator_opts);
    if (message_arena != submessage_arena) {
      scoped_allocator_opts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, scoped_allocator_opts, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.scoped_allocator_opts_ = scoped_allocator_opts;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.scoped_allocator_opts)
}

// repeated string optimizers = 100;
inline int RewriterConfig::_internal_optimizers_size() const {
  return _impl_.optimizers_.size();
}
inline int RewriterConfig::optimizers_size() const {
  return _internal_optimizers_size();
}
inline void RewriterConfig::clear_optimizers() {
  _impl_.optimizers_.Clear();
}
inline std::string* RewriterConfig::add_optimizers() {
  std::string* _s = _internal_add_optimizers();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RewriterConfig.optimizers)
  return _s;
}
inline const std::string& RewriterConfig::_internal_optimizers(int index) const {
  return _impl_.optimizers_.Get(index);
}
inline const std::string& RewriterConfig::optimizers(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.optimizers)
  return _internal_optimizers(index);
}
inline std::string* RewriterConfig::mutable_optimizers(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.optimizers)
  return _impl_.optimizers_.Mutable(index);
}
inline void RewriterConfig::set_optimizers(int index, const std::string& value) {
  _impl_.optimizers_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::set_optimizers(int index, std::string&& value) {
  _impl_.optimizers_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::set_optimizers(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.optimizers_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::set_optimizers(int index, const char* value, size_t size) {
  _impl_.optimizers_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RewriterConfig.optimizers)
}
inline std::string* RewriterConfig::_internal_add_optimizers() {
  return _impl_.optimizers_.Add();
}
inline void RewriterConfig::add_optimizers(const std::string& value) {
  _impl_.optimizers_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::add_optimizers(std::string&& value) {
  _impl_.optimizers_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::add_optimizers(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.optimizers_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RewriterConfig.optimizers)
}
inline void RewriterConfig::add_optimizers(const char* value, size_t size) {
  _impl_.optimizers_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RewriterConfig.optimizers)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RewriterConfig::optimizers() const {
  // @@protoc_insertion_point(field_list:tensorflow.RewriterConfig.optimizers)
  return _impl_.optimizers_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RewriterConfig::mutable_optimizers() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RewriterConfig.optimizers)
  return &_impl_.optimizers_;
}

// repeated .tensorflow.RewriterConfig.CustomGraphOptimizer custom_optimizers = 200;
inline int RewriterConfig::_internal_custom_optimizers_size() const {
  return _impl_.custom_optimizers_.size();
}
inline int RewriterConfig::custom_optimizers_size() const {
  return _internal_custom_optimizers_size();
}
inline void RewriterConfig::clear_custom_optimizers() {
  _impl_.custom_optimizers_.Clear();
}
inline ::tensorflow::RewriterConfig_CustomGraphOptimizer* RewriterConfig::mutable_custom_optimizers(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.custom_optimizers)
  return _impl_.custom_optimizers_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >*
RewriterConfig::mutable_custom_optimizers() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RewriterConfig.custom_optimizers)
  return &_impl_.custom_optimizers_;
}
inline const ::tensorflow::RewriterConfig_CustomGraphOptimizer& RewriterConfig::_internal_custom_optimizers(int index) const {
  return _impl_.custom_optimizers_.Get(index);
}
inline const ::tensorflow::RewriterConfig_CustomGraphOptimizer& RewriterConfig::custom_optimizers(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.custom_optimizers)
  return _internal_custom_optimizers(index);
}
inline ::tensorflow::RewriterConfig_CustomGraphOptimizer* RewriterConfig::_internal_add_custom_optimizers() {
  return _impl_.custom_optimizers_.Add();
}
inline ::tensorflow::RewriterConfig_CustomGraphOptimizer* RewriterConfig::add_custom_optimizers() {
  ::tensorflow::RewriterConfig_CustomGraphOptimizer* _add = _internal_add_custom_optimizers();
  // @@protoc_insertion_point(field_add:tensorflow.RewriterConfig.custom_optimizers)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RewriterConfig_CustomGraphOptimizer >&
RewriterConfig::custom_optimizers() const {
  // @@protoc_insertion_point(field_list:tensorflow.RewriterConfig.custom_optimizers)
  return _impl_.custom_optimizers_;
}

// .tensorflow.VerifierConfig inter_optimizer_verifier_config = 300;
inline bool RewriterConfig::_internal_has_inter_optimizer_verifier_config() const {
  return this != internal_default_instance() && _impl_.inter_optimizer_verifier_config_ != nullptr;
}
inline bool RewriterConfig::has_inter_optimizer_verifier_config() const {
  return _internal_has_inter_optimizer_verifier_config();
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::_internal_inter_optimizer_verifier_config() const {
  const ::tensorflow::VerifierConfig* p = _impl_.inter_optimizer_verifier_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::VerifierConfig&>(
      ::tensorflow::_VerifierConfig_default_instance_);
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::inter_optimizer_verifier_config() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  return _internal_inter_optimizer_verifier_config();
}
inline void RewriterConfig::unsafe_arena_set_allocated_inter_optimizer_verifier_config(
    ::tensorflow::VerifierConfig* inter_optimizer_verifier_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.inter_optimizer_verifier_config_);
  }
  _impl_.inter_optimizer_verifier_config_ = inter_optimizer_verifier_config;
  if (inter_optimizer_verifier_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
}
inline ::tensorflow::VerifierConfig* RewriterConfig::release_inter_optimizer_verifier_config() {
  
  ::tensorflow::VerifierConfig* temp = _impl_.inter_optimizer_verifier_config_;
  _impl_.inter_optimizer_verifier_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::unsafe_arena_release_inter_optimizer_verifier_config() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = _impl_.inter_optimizer_verifier_config_;
  _impl_.inter_optimizer_verifier_config_ = nullptr;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::_internal_mutable_inter_optimizer_verifier_config() {
  
  if (_impl_.inter_optimizer_verifier_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VerifierConfig>(GetArenaForAllocation());
    _impl_.inter_optimizer_verifier_config_ = p;
  }
  return _impl_.inter_optimizer_verifier_config_;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::mutable_inter_optimizer_verifier_config() {
  ::tensorflow::VerifierConfig* _msg = _internal_mutable_inter_optimizer_verifier_config();
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
  return _msg;
}
inline void RewriterConfig::set_allocated_inter_optimizer_verifier_config(::tensorflow::VerifierConfig* inter_optimizer_verifier_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.inter_optimizer_verifier_config_);
  }
  if (inter_optimizer_verifier_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(inter_optimizer_verifier_config));
    if (message_arena != submessage_arena) {
      inter_optimizer_verifier_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, inter_optimizer_verifier_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.inter_optimizer_verifier_config_ = inter_optimizer_verifier_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.inter_optimizer_verifier_config)
}

// .tensorflow.VerifierConfig post_optimization_verifier_config = 301;
inline bool RewriterConfig::_internal_has_post_optimization_verifier_config() const {
  return this != internal_default_instance() && _impl_.post_optimization_verifier_config_ != nullptr;
}
inline bool RewriterConfig::has_post_optimization_verifier_config() const {
  return _internal_has_post_optimization_verifier_config();
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::_internal_post_optimization_verifier_config() const {
  const ::tensorflow::VerifierConfig* p = _impl_.post_optimization_verifier_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::VerifierConfig&>(
      ::tensorflow::_VerifierConfig_default_instance_);
}
inline const ::tensorflow::VerifierConfig& RewriterConfig::post_optimization_verifier_config() const {
  // @@protoc_insertion_point(field_get:tensorflow.RewriterConfig.post_optimization_verifier_config)
  return _internal_post_optimization_verifier_config();
}
inline void RewriterConfig::unsafe_arena_set_allocated_post_optimization_verifier_config(
    ::tensorflow::VerifierConfig* post_optimization_verifier_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.post_optimization_verifier_config_);
  }
  _impl_.post_optimization_verifier_config_ = post_optimization_verifier_config;
  if (post_optimization_verifier_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RewriterConfig.post_optimization_verifier_config)
}
inline ::tensorflow::VerifierConfig* RewriterConfig::release_post_optimization_verifier_config() {
  
  ::tensorflow::VerifierConfig* temp = _impl_.post_optimization_verifier_config_;
  _impl_.post_optimization_verifier_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::unsafe_arena_release_post_optimization_verifier_config() {
  // @@protoc_insertion_point(field_release:tensorflow.RewriterConfig.post_optimization_verifier_config)
  
  ::tensorflow::VerifierConfig* temp = _impl_.post_optimization_verifier_config_;
  _impl_.post_optimization_verifier_config_ = nullptr;
  return temp;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::_internal_mutable_post_optimization_verifier_config() {
  
  if (_impl_.post_optimization_verifier_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VerifierConfig>(GetArenaForAllocation());
    _impl_.post_optimization_verifier_config_ = p;
  }
  return _impl_.post_optimization_verifier_config_;
}
inline ::tensorflow::VerifierConfig* RewriterConfig::mutable_post_optimization_verifier_config() {
  ::tensorflow::VerifierConfig* _msg = _internal_mutable_post_optimization_verifier_config();
  // @@protoc_insertion_point(field_mutable:tensorflow.RewriterConfig.post_optimization_verifier_config)
  return _msg;
}
inline void RewriterConfig::set_allocated_post_optimization_verifier_config(::tensorflow::VerifierConfig* post_optimization_verifier_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.post_optimization_verifier_config_);
  }
  if (post_optimization_verifier_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(post_optimization_verifier_config));
    if (message_arena != submessage_arena) {
      post_optimization_verifier_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, post_optimization_verifier_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.post_optimization_verifier_config_ = post_optimization_verifier_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RewriterConfig.post_optimization_verifier_config)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::RewriterConfig_Toggle> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_Toggle>() {
  return ::tensorflow::RewriterConfig_Toggle_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RewriterConfig_CpuLayout> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_CpuLayout>() {
  return ::tensorflow::RewriterConfig_CpuLayout_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RewriterConfig_NumIterationsType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_NumIterationsType>() {
  return ::tensorflow::RewriterConfig_NumIterationsType_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RewriterConfig_MemOptType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RewriterConfig_MemOptType>() {
  return ::tensorflow::RewriterConfig_MemOptType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2frewriter_5fconfig_2eproto
