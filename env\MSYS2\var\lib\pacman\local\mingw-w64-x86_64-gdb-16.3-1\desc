%NAME%
mingw-w64-x86_64-gdb

%VERSION%
16.3-1

%BASE%
mingw-w64-gdb

%DESC%
GNU Debugger (mingw-w64)

%URL%
https://www.gnu.org/software/gdb/

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/e8d10d7e/14628098762)

%SIZE%
15232173

%GROUPS%
mingw-w64-x86_64-toolchain

%LICENSE%
spdx:GPL-3.0-or-later

%VALIDATION%
sha256
pgp

%DEPENDS%
mingw-w64-x86_64-gcc-libs
mingw-w64-x86_64-expat
mingw-w64-x86_64-gettext-runtime
mingw-w64-x86_64-gmp
mingw-w64-x86_64-libiconv
mingw-w64-x86_64-mpfr
mingw-w64-x86_64-ncurses
mingw-w64-x86_64-python
mingw-w64-x86_64-readline
mingw-w64-x86_64-xxhash
mingw-w64-x86_64-xz
mingw-w64-x86_64-zlib
mingw-w64-x86_64-zstd

%OPTDEPENDS%
mingw-w64-x86_64-python-pygments: for syntax highlighting

%XDATA%
pkgtype=split

