// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tensorflow/core/debug/debug_service.proto

#include "tensorflow/core/debug/debug_service.pb.h"
#include "tensorflow/core/debug/debug_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace tensorflow {


namespace grpc {

class MockEventListenerStub : public EventListener::StubInterface {
 public:
  MOCK_METHOD1(SendEventsRaw, ::grpc::ClientReaderWriterInterface< ::tensorflow::Event, ::tensorflow::EventReply>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncSendEventsRaw, ::grpc::ClientAsyncReaderWriterInterface<::tensorflow::Event, ::tensorflow::EventReply>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncSendEventsRaw, ::grpc::ClientAsyncReaderWriterInterface<::tensorflow::Event, ::tensorflow::EventReply>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(SendTracebacks, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::CallTraceback& request, ::tensorflow::EventReply* response));
  MOCK_METHOD3(AsyncSendTracebacksRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EventReply>*(::grpc::ClientContext* context, const ::tensorflow::CallTraceback& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncSendTracebacksRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EventReply>*(::grpc::ClientContext* context, const ::tensorflow::CallTraceback& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(SendSourceFiles, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::DebuggedSourceFiles& request, ::tensorflow::EventReply* response));
  MOCK_METHOD3(AsyncSendSourceFilesRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EventReply>*(::grpc::ClientContext* context, const ::tensorflow::DebuggedSourceFiles& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncSendSourceFilesRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::EventReply>*(::grpc::ClientContext* context, const ::tensorflow::DebuggedSourceFiles& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc

} // namespace tensorflow

