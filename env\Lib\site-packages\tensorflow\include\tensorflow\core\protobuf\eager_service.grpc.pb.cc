// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tensorflow/core/protobuf/eager_service.proto

#include "tensorflow/core/protobuf/eager_service.pb.h"
#include "tensorflow/core/protobuf/eager_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace tensorflow {
namespace eager {

static const char* grpcEagerService_method_names[] = {
  "/tensorflow.eager.EagerService/CreateContext",
  "/tensorflow.eager.EagerService/UpdateContext",
  "/tensorflow.eager.EagerService/Enqueue",
  "/tensorflow.eager.EagerService/StreamingEnqueue",
  "/tensorflow.eager.EagerService/WaitQueueDone",
  "/tensorflow.eager.EagerService/RunComponentFunction",
  "/tensorflow.eager.EagerService/KeepAlive",
  "/tensorflow.eager.EagerService/CloseContext",
};

std::unique_ptr< grpc::EagerService::Stub> grpc::EagerService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< grpc::EagerService::Stub> stub(new grpc::EagerService::Stub(channel));
  return stub;
}

grpc::EagerService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_CreateContext_(grpcEagerService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpdateContext_(grpcEagerService_method_names[1], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Enqueue_(grpcEagerService_method_names[2], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StreamingEnqueue_(grpcEagerService_method_names[3], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_WaitQueueDone_(grpcEagerService_method_names[4], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_RunComponentFunction_(grpcEagerService_method_names[5], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_KeepAlive_(grpcEagerService_method_names[6], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CloseContext_(grpcEagerService_method_names[7], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status grpc::EagerService::Stub::CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::tensorflow::eager::CreateContextResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_CreateContext_, context, request, response);
}

void grpc::EagerService::Stub::experimental_async::CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_CreateContext_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::CreateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CreateContextResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_CreateContext_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_CreateContext_, context, request, response, reactor);
}

void grpc::EagerService::Stub::experimental_async::CreateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_CreateContext_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CreateContextResponse>* grpc::EagerService::Stub::AsyncCreateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::CreateContextResponse>::Create(channel_.get(), cq, rpcmethod_CreateContext_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CreateContextResponse>* grpc::EagerService::Stub::PrepareAsyncCreateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::CreateContextResponse>::Create(channel_.get(), cq, rpcmethod_CreateContext_, context, request, false);
}

::grpc::Status grpc::EagerService::Stub::UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::tensorflow::eager::UpdateContextResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_UpdateContext_, context, request, response);
}

void grpc::EagerService::Stub::experimental_async::UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_UpdateContext_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::UpdateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::UpdateContextResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_UpdateContext_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_UpdateContext_, context, request, response, reactor);
}

void grpc::EagerService::Stub::experimental_async::UpdateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_UpdateContext_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::UpdateContextResponse>* grpc::EagerService::Stub::AsyncUpdateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::UpdateContextResponse>::Create(channel_.get(), cq, rpcmethod_UpdateContext_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::UpdateContextResponse>* grpc::EagerService::Stub::PrepareAsyncUpdateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::UpdateContextResponse>::Create(channel_.get(), cq, rpcmethod_UpdateContext_, context, request, false);
}

::grpc::Status grpc::EagerService::Stub::Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::tensorflow::eager::EnqueueResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Enqueue_, context, request, response);
}

void grpc::EagerService::Stub::experimental_async::Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Enqueue_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::Enqueue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::EnqueueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Enqueue_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Enqueue_, context, request, response, reactor);
}

void grpc::EagerService::Stub::experimental_async::Enqueue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Enqueue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::EnqueueResponse>* grpc::EagerService::Stub::AsyncEnqueueRaw(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::EnqueueResponse>::Create(channel_.get(), cq, rpcmethod_Enqueue_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::EnqueueResponse>* grpc::EagerService::Stub::PrepareAsyncEnqueueRaw(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::EnqueueResponse>::Create(channel_.get(), cq, rpcmethod_Enqueue_, context, request, false);
}

::grpc::ClientReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* grpc::EagerService::Stub::StreamingEnqueueRaw(::grpc::ClientContext* context) {
  return ::grpc_impl::internal::ClientReaderWriterFactory< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>::Create(channel_.get(), rpcmethod_StreamingEnqueue_, context);
}

void grpc::EagerService::Stub::experimental_async::StreamingEnqueue(::grpc::ClientContext* context, ::grpc::experimental::ClientBidiReactor< ::tensorflow::eager::EnqueueRequest,::tensorflow::eager::EnqueueResponse>* reactor) {
  ::grpc_impl::internal::ClientCallbackReaderWriterFactory< ::tensorflow::eager::EnqueueRequest,::tensorflow::eager::EnqueueResponse>::Create(stub_->channel_.get(), stub_->rpcmethod_StreamingEnqueue_, context, reactor);
}

::grpc::ClientAsyncReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* grpc::EagerService::Stub::AsyncStreamingEnqueueRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc_impl::internal::ClientAsyncReaderWriterFactory< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>::Create(channel_.get(), cq, rpcmethod_StreamingEnqueue_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* grpc::EagerService::Stub::PrepareAsyncStreamingEnqueueRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncReaderWriterFactory< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>::Create(channel_.get(), cq, rpcmethod_StreamingEnqueue_, context, false, nullptr);
}

::grpc::Status grpc::EagerService::Stub::WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::tensorflow::eager::WaitQueueDoneResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_WaitQueueDone_, context, request, response);
}

void grpc::EagerService::Stub::experimental_async::WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_WaitQueueDone_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::WaitQueueDone(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::WaitQueueDoneResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_WaitQueueDone_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_WaitQueueDone_, context, request, response, reactor);
}

void grpc::EagerService::Stub::experimental_async::WaitQueueDone(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_WaitQueueDone_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::WaitQueueDoneResponse>* grpc::EagerService::Stub::AsyncWaitQueueDoneRaw(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::WaitQueueDoneResponse>::Create(channel_.get(), cq, rpcmethod_WaitQueueDone_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::WaitQueueDoneResponse>* grpc::EagerService::Stub::PrepareAsyncWaitQueueDoneRaw(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::WaitQueueDoneResponse>::Create(channel_.get(), cq, rpcmethod_WaitQueueDone_, context, request, false);
}

::grpc::Status grpc::EagerService::Stub::RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::tensorflow::eager::RunComponentFunctionResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_RunComponentFunction_, context, request, response);
}

void grpc::EagerService::Stub::experimental_async::RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_RunComponentFunction_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::RunComponentFunction(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::RunComponentFunctionResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_RunComponentFunction_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_RunComponentFunction_, context, request, response, reactor);
}

void grpc::EagerService::Stub::experimental_async::RunComponentFunction(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_RunComponentFunction_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::RunComponentFunctionResponse>* grpc::EagerService::Stub::AsyncRunComponentFunctionRaw(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::RunComponentFunctionResponse>::Create(channel_.get(), cq, rpcmethod_RunComponentFunction_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::RunComponentFunctionResponse>* grpc::EagerService::Stub::PrepareAsyncRunComponentFunctionRaw(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::RunComponentFunctionResponse>::Create(channel_.get(), cq, rpcmethod_RunComponentFunction_, context, request, false);
}

::grpc::Status grpc::EagerService::Stub::KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::tensorflow::eager::KeepAliveResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_KeepAlive_, context, request, response);
}

void grpc::EagerService::Stub::experimental_async::KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_KeepAlive_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::KeepAlive(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::KeepAliveResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_KeepAlive_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_KeepAlive_, context, request, response, reactor);
}

void grpc::EagerService::Stub::experimental_async::KeepAlive(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_KeepAlive_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::KeepAliveResponse>* grpc::EagerService::Stub::AsyncKeepAliveRaw(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::KeepAliveResponse>::Create(channel_.get(), cq, rpcmethod_KeepAlive_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::KeepAliveResponse>* grpc::EagerService::Stub::PrepareAsyncKeepAliveRaw(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::KeepAliveResponse>::Create(channel_.get(), cq, rpcmethod_KeepAlive_, context, request, false);
}

::grpc::Status grpc::EagerService::Stub::CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::tensorflow::eager::CloseContextResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_CloseContext_, context, request, response);
}

void grpc::EagerService::Stub::experimental_async::CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_CloseContext_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::CloseContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CloseContextResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_CloseContext_, context, request, response, std::move(f));
}

void grpc::EagerService::Stub::experimental_async::CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_CloseContext_, context, request, response, reactor);
}

void grpc::EagerService::Stub::experimental_async::CloseContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_CloseContext_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CloseContextResponse>* grpc::EagerService::Stub::AsyncCloseContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::CloseContextResponse>::Create(channel_.get(), cq, rpcmethod_CloseContext_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CloseContextResponse>* grpc::EagerService::Stub::PrepareAsyncCloseContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::eager::CloseContextResponse>::Create(channel_.get(), cq, rpcmethod_CloseContext_, context, request, false);
}

grpc::EagerService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEagerService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EagerService::Service, ::tensorflow::eager::CreateContextRequest, ::tensorflow::eager::CreateContextResponse>(
          std::mem_fn(&grpc::EagerService::Service::CreateContext), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEagerService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EagerService::Service, ::tensorflow::eager::UpdateContextRequest, ::tensorflow::eager::UpdateContextResponse>(
          std::mem_fn(&grpc::EagerService::Service::UpdateContext), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEagerService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EagerService::Service, ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>(
          std::mem_fn(&grpc::EagerService::Service::Enqueue), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEagerService_method_names[3],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< grpc::EagerService::Service, ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>(
          std::mem_fn(&grpc::EagerService::Service::StreamingEnqueue), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEagerService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EagerService::Service, ::tensorflow::eager::WaitQueueDoneRequest, ::tensorflow::eager::WaitQueueDoneResponse>(
          std::mem_fn(&grpc::EagerService::Service::WaitQueueDone), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEagerService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EagerService::Service, ::tensorflow::eager::RunComponentFunctionRequest, ::tensorflow::eager::RunComponentFunctionResponse>(
          std::mem_fn(&grpc::EagerService::Service::RunComponentFunction), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEagerService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EagerService::Service, ::tensorflow::eager::KeepAliveRequest, ::tensorflow::eager::KeepAliveResponse>(
          std::mem_fn(&grpc::EagerService::Service::KeepAlive), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcEagerService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::EagerService::Service, ::tensorflow::eager::CloseContextRequest, ::tensorflow::eager::CloseContextResponse>(
          std::mem_fn(&grpc::EagerService::Service::CloseContext), this)));
}

grpc::EagerService::Service::~Service() {
}

::grpc::Status grpc::EagerService::Service::CreateContext(::grpc::ServerContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EagerService::Service::UpdateContext(::grpc::ServerContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EagerService::Service::Enqueue(::grpc::ServerContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EagerService::Service::StreamingEnqueue(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::tensorflow::eager::EnqueueResponse, ::tensorflow::eager::EnqueueRequest>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EagerService::Service::WaitQueueDone(::grpc::ServerContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EagerService::Service::RunComponentFunction(::grpc::ServerContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EagerService::Service::KeepAlive(::grpc::ServerContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::EagerService::Service::CloseContext(::grpc::ServerContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace tensorflow
}  // namespace eager

