/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

import "oaidl.idl";
import "ocidl.idl";
import "tpcshrd.idl";

// cpp_quote("#import <tpcerror.h>")

typedef enum InkRasterOperation {
    IRO_Black       = 1,
    IRO_NotMergePen = 2,
    IRO_MaskNotPen  = 3,
    IRO_NotCopyPen  = 4,
    IRO_MaskPenNot  = 5,
    IRO_Not         = 6,
    IRO_XOrPen      = 7,
    IRO_NotMaskPen  = 8,
    IRO_MaskPen     = 9,
    IRO_NotXOrPen   = 10,
    IRO_NoOperation = 11,
    IRO_MergeNotPen = 12,
    IRO_CopyPen     = 13,
    IRO_MergePenNot = 14,
    IRO_MergePen    = 15,
    IRO_White       = 16
} InkRasterOperation;

typedef enum InkPenTip {
    IPT_Ball      = 0,
    IPT_Rectangle = 1
} InkPenTip;

typedef enum TabletHardwareCapabilities {
    THWC_Integrated             = 0x1,
    THWC_CursorMustTouch        = 0x2,
    THWC_HardProximity          = 0x4,
    THWC_CursorsHavePhysicalIds = 0x8
} TabletHardwareCapabilities;

typedef enum TabletPropertyMetricUnit {
    TPMU_Default,
    TPMU_Inches,
    TPMU_Centimeters,
    TPMU_Degrees,
    TPMU_Radians,
    TPMU_Seconds,
    TPMU_Pounds,
    TPMU_Grams
} TabletPropertyMetricUnit;

typedef enum InkCursorButtonState {
    ICBS_Unavailable,
    ICBS_Up,
    ICBS_Down
} InkCursorButtonState;

typedef enum InkRecognitionConfidence {
    IRC_Strong       = 0,
    IRC_Intermediate = 1,
    IRC_Poor         = 2
} InkRecognitionConfidence;

typedef enum InkBoundingBoxMode {
    IBBM_Default    = 0,
    IBBM_NoCurveFit = 1,
    IBBM_CurveFit   = 2,
    IBBM_PointsOnly = 3,
    IBBM_Union      = 4
} InkBoundingBoxMode;

typedef enum InkExtractFlags {
    IEF_CopyFromOriginal   = 0,
    IEF_RemoveFromOriginal = 1,
    IEF_Default            = IEF_RemoveFromOriginal
} InkExtractFlags;

typedef enum InkPersistenceFormat {
    IPF_InkSerializedFormat       = 0,
    IPF_Base64InkSerializedFormat = 1,
    IPF_GIF                       = 2,
    IPF_Base64GIF                 = 3
} InkPersistenceFormat;

typedef enum InkPersistenceCompressionMode {
    IPCM_Default            = 0,
    IPCM_MaximumCompression = 1,
    IPCM_NoCompression      = 2
} InkPersistenceCompressionMode;

typedef enum InkClipboardFormats {
    ICF_None                = 0x0000,
    ICF_InkSerializedFormat = 0x0001,
    ICF_SketchInk           = 0x0002,
    ICF_TextInk             = 0x0006,
    ICF_EnhancedMetafile    = 0x0008,
    ICF_Metafile            = 0x0020,
    ICF_Bitmap              = 0x0040,
    ICF_PasteMask           = 0x0007,
    ICF_CopyMask            = 0x007f,
    ICF_Default             = ICF_CopyMask
} InkClipboardFormats;

typedef enum InkClipboardModes {
    ICB_Copy        = 0x00,
    ICB_Cut         = 0x01,
    ICB_ExtractOnly = 0x30,
    ICB_DelayedCopy = 0x20,
    ICB_Default     = ICB_Copy
} InkClipboardModes;

typedef enum InkCollectionMode {
    ICM_InkOnly,
    ICM_GestureOnly,
    ICM_InkAndGesture
} InkCollectionMode;

typedef enum InkMousePointer {
    IMP_Default        = 0,
    IMP_Arrow          = 1,
    IMP_Crosshair      = 2,
    IMP_Ibeam          = 3,
    IMP_SizeNESW       = 4,
    IMP_SizeNS         = 5,
    IMP_SizeNWSE       = 6,
    IMP_SizeWE         = 7,
    IMP_UpArrow        = 8,
    IMP_Hourglass      = 9,
    IMP_NoDrop         = 10,
    IMP_ArrowHourglass = 11,
    IMP_ArrowQuestion  = 12,
    IMP_SizeAll        = 13,
    IMP_Hand           = 14,
    IMP_Custom         = 99
} InkMousePointer;

typedef enum InkApplicationGesture {
    IAG_AllGestures     = 0x0000,
    IAG_NoGesture       = 0xf000,
    IAG_Scratchout      = 0xf001,
    IAG_Triangle        = 0xf002,
    IAG_Square          = 0xf003,
    IAG_Star            = 0xf004,
    IAG_Check           = 0xf005,
    IAG_Curlicue        = 0xf010,
    IAG_DoubleCurlicue  = 0xf011,
    IAG_Circle          = 0xf020,
    IAG_DoubleCircle    = 0xf021,
    IAG_SemiCircleLeft  = 0xf028,
    IAG_SemiCircleRight = 0xf029,
    IAG_ChevronUp       = 0xf030,
    IAG_ChevronDown     = 0xf031,
    IAG_ChevronLeft     = 0xf032,
    IAG_ChevronRight    = 0xf033,
    IAG_ArrowUp         = 0xf038,
    IAG_ArrowDown       = 0xf039,
    IAG_ArrowLeft       = 0xf03a,
    IAG_ArrowRight      = 0xf03b,
    IAG_Up              = 0xf058,
    IAG_Down            = 0xf059,
    IAG_Left            = 0xf05a,
    IAG_Right           = 0xf05b,
    IAG_UpDown          = 0xf060,
    IAG_DownUp          = 0xf061,
    IAG_LeftRight       = 0xf062,
    IAG_RightLeft       = 0xf063,
    IAG_UpLeftLong      = 0xf064,
    IAG_UpRightLong     = 0xf065,
    IAG_DownLeftLong    = 0xf066,
    IAG_DownRightLong   = 0xf067,
    IAG_UpLeft          = 0xf068,
    IAG_UpRight         = 0xf069,
    IAG_DownLeft        = 0xf06a,
    IAG_DownRight       = 0xf06b,
    IAG_LeftUp          = 0xf06c,
    IAG_LeftDown        = 0xf06d,
    IAG_RightUp         = 0xf06e,
    IAG_RightDown       = 0xf06f,
    IAG_Exclamation     = 0xf0a4,
    IAG_Tap             = 0xf0f0,
    IAG_DoubleTap       = 0xf0f1
} InkApplicationGesture;

typedef enum InkCollectorEventInterest {
    ICEI_DefaultEvents    = -1,
    ICEI_CursorDown       = (ICEI_DefaultEvents+1),
    ICEI_Stroke           = (ICEI_CursorDown+1),
    ICEI_NewPackets       = (ICEI_Stroke+1),
    ICEI_NewInAirPackets  = (ICEI_NewPackets+1),
    ICEI_CursorButtonDown = (ICEI_NewInAirPackets+1),
    ICEI_CursorButtonUp   = (ICEI_CursorButtonDown+1),
    ICEI_CursorInRange    = (ICEI_CursorButtonUp+1),
    ICEI_CursorOutOfRange = (ICEI_CursorInRange+1),
    ICEI_SystemGesture    = (ICEI_CursorOutOfRange+1),
    ICEI_TabletAdded      = (ICEI_SystemGesture+1),
    ICEI_TabletRemoved    = (ICEI_TabletAdded+1),
    ICEI_MouseDown        = (ICEI_TabletRemoved+1),
    ICEI_MouseMove        = (ICEI_MouseDown+1),
    ICEI_MouseUp          = (ICEI_MouseMove+1),
    ICEI_MouseWheel       = (ICEI_MouseUp+1),
    ICEI_DblClick         = (ICEI_MouseWheel+1),
    ICEI_AllEvents        = (ICEI_DblClick+1)
} InkCollectorEventInterest;

typedef enum DISPID_InkCollectorEvent {
    DISPID_ICEStroke              = 1,
    DISPID_ICECursorDown          = (DISPID_ICEStroke+1),
    DISPID_ICENewPackets          = (DISPID_ICECursorDown+1),
    DISPID_ICENewInAirPackets     = (DISPID_ICENewPackets+1),
    DISPID_ICECursorButtonDown    = (DISPID_ICENewInAirPackets+1),
    DISPID_ICECursorButtonUp      = (DISPID_ICECursorButtonDown+1),
    DISPID_ICECursorInRange       = (DISPID_ICECursorButtonUp+1),
    DISPID_ICECursorOutOfRange    = (DISPID_ICECursorInRange+1),
    DISPID_ICESystemGesture       = (DISPID_ICECursorOutOfRange+1),
    DISPID_ICEGesture             = (DISPID_ICESystemGesture+1),
    DISPID_ICETabletAdded         = (DISPID_ICEGesture+1),
    DISPID_ICETabletRemoved       = (DISPID_ICETabletAdded+1),
    DISPID_IOEPainting            = (DISPID_ICETabletRemoved+1),
    DISPID_IOEPainted             = (DISPID_IOEPainting+1),
    DISPID_IOESelectionChanging   = (DISPID_IOEPainted+1),
    DISPID_IOESelectionChanged    = (DISPID_IOESelectionChanging+1),
    DISPID_IOESelectionMoving     = (DISPID_IOESelectionChanged+1),
    DISPID_IOESelectionMoved      = (DISPID_IOESelectionMoving+1),
    DISPID_IOESelectionResizing   = (DISPID_IOESelectionMoved+1),
    DISPID_IOESelectionResized    = (DISPID_IOESelectionResizing+1),
    DISPID_IOEStrokesDeleting     = (DISPID_IOESelectionResized+1),
    DISPID_IOEStrokesDeleted      = (DISPID_IOEStrokesDeleting+1),
    DISPID_IPEChangeUICues        = (DISPID_IOEStrokesDeleted+1),
    DISPID_IPEClick               = (DISPID_IPEChangeUICues+1),
    DISPID_IPEDblClick            = (DISPID_IPEClick+1),
    DISPID_IPEInvalidated         = (DISPID_IPEDblClick+1),
    DISPID_IPEMouseDown           = (DISPID_IPEInvalidated+1),
    DISPID_IPEMouseEnter          = (DISPID_IPEMouseDown+1),
    DISPID_IPEMouseHover          = (DISPID_IPEMouseEnter+1),
    DISPID_IPEMouseLeave          = (DISPID_IPEMouseHover+1),
    DISPID_IPEMouseMove           = (DISPID_IPEMouseLeave+1),
    DISPID_IPEMouseUp             = (DISPID_IPEMouseMove+1),
    DISPID_IPEMouseWheel          = (DISPID_IPEMouseUp+1),
    DISPID_IPESizeModeChanged     = (DISPID_IPEMouseWheel+1),
    DISPID_IPEStyleChanged        = (DISPID_IPESizeModeChanged+1),
    DISPID_IPESystemColorsChanged = (DISPID_IPEStyleChanged+1),
    DISPID_IPEKeyDown             = (DISPID_IPESystemColorsChanged+1),
    DISPID_IPEKeyPress            = (DISPID_IPEKeyDown+1),
    DISPID_IPEKeyUp               = (DISPID_IPEKeyPress+1),
    DISPID_IPEResize              = (DISPID_IPEKeyUp+1),
    DISPID_IPESizeChanged         = (DISPID_IPEResize+1)
} DISPID_InkCollectorEvent;


interface IInkDisp;
interface IInkStrokes;
interface IInkRecognitionAlternate;

[
    object,
    uuid(db489209-b7c3-411d-90f6-1548cfff271e),
    dual
]
interface IInkExtendedProperty : IDispatch
{
    [propget] HRESULT Guid([out, retval] BSTR *Guid);
    [propget] HRESULT Data([out, retval] VARIANT *Data);
    [propput] HRESULT Data([in] VARIANT Data);
}

[
    object,
    uuid(89f2a8be-95a9-4530-8b8f-88e971e3e25f),
    dual
]
interface IInkExtendedProperties : IDispatch
{
    [propget] HRESULT Count([out, retval] long *Count);
    [propget] HRESULT _NewEnum([out, retval] IUnknown **_NewEnum);
    HRESULT Item([in] VARIANT Identifier, [out, retval] IInkExtendedProperty **Item);
    HRESULT Add([in] BSTR Guid, [in] VARIANT Data, [out, retval] IInkExtendedProperty **InkExtendedProperty);
    HRESULT Remove([in] VARIANT Identifier);
    HRESULT Clear();
    HRESULT DoesPropertyExist([in] BSTR Guid, [out, retval] VARIANT_BOOL *DoesPropertyExist);
}

[
    object,
    uuid(bf519b75-0a15-4623-adc9-c00d436a8092),
    dual
]
interface IInkDrawingAttributes : IDispatch
{
    [propget] HRESULT Color([out] long *CurrentColor);
    [propput] HRESULT Color([in] long NewColor);
    [propget] HRESULT Width([out, retval] float *CurrentWidth);
    [propput] HRESULT Width([in] float NewWidth);
    [propget] HRESULT Height([out, retval] float *CurrentHeight);
    [propput] HRESULT Height([in] float NewHeight);
    [propget] HRESULT FitToCurve([out, retval] VARIANT_BOOL *Flag);
    [propput] HRESULT FitToCurve([in] VARIANT_BOOL Flag);
    [propget] HRESULT IgnorePressure([out, retval] VARIANT_BOOL *Flag);
    [propput] HRESULT IgnorePressure([in] VARIANT_BOOL Flag);
    [propget] HRESULT AntiAliased([out, retval] VARIANT_BOOL *Flag);
    [propput] HRESULT AntiAliased([in] VARIANT_BOOL Flag);
    [propget] HRESULT Transparency([out, retval] long *CurrentTransparency);
    [propput] HRESULT Transparency([in] long NewTransparency);
    [propget] HRESULT RasterOperation([out, retval] InkRasterOperation *CurrentRasterOperation);
    [propput] HRESULT RasterOperation([in] InkRasterOperation NewRasterOperation);
    [propget] HRESULT PenTip([out, retval] InkPenTip *CurrentPenTip);
    [propput] HRESULT PenTip([in] InkPenTip NewPenTip);
    [propget] HRESULT ExtendedProperties([out, retval] IInkExtendedProperties **Properties);
    HRESULT Clone([out, retval] IInkDrawingAttributes **DrawingAttributes);
}

[
    object,
    uuid(9794ff82-6071-4717-8a8b-6ac7c64a686e),
    dual
]
interface IInkRectangle : IDispatch
{
    [propget] HRESULT Top([out, retval] long *Units);
    [propput] HRESULT Top([in] long Units);
    [propget] HRESULT Left([out, retval] long *Units);
    [propput] HRESULT Left([in] long Units);
    [propget] HRESULT Bottom([out, retval] long *Units);
    [propput] HRESULT Bottom([in] long Units);
    [propget] HRESULT Right([out, retval] long *Units);
    [propput] HRESULT Right([in] long Units);
    [propget] HRESULT Data([out, retval] RECT *Rect);
    [propput] HRESULT Data([in] RECT Rect);
    HRESULT GetRectangle([out] long *Top, [out] long *Left, [out] long *Bottom, [out] long *Right);
    HRESULT SetRectangle([in] long Top, [in] long Left, [in] long Bottom, [in] long Right);
}

[
    object,
    uuid(2de25eaa-6ef8-42d5-aee9-185bc81b912d),
    dual
]
interface IInkTablet : IDispatch
{
    [propget] HRESULT Name([out, retval] BSTR *Name);
    [propget] HRESULT PlugAndPlayId([out, retval] BSTR *Id);
    [propget] HRESULT MaximumInputRectangle([out, retval] IInkRectangle **Rectangle);
    [propget] HRESULT HardwareCapabilities([out, retval] TabletHardwareCapabilities *Capabilities);
    HRESULT IsPacketPropertySupported([in] BSTR packetPropertyName, [out, retval] VARIANT_BOOL *Supported);
    HRESULT GetPropertyMetrics([in] BSTR propertyName, [out] long *Minimum, [out] long *Maximum,
            [out] TabletPropertyMetricUnit *Units, [out] float *Resolution);
}

[
    object,
    uuid(85ef9417-1d59-49b2-a13c-702c85430894),
    dual
]
interface IInkCursorButton : IDispatch
{
    [propget] HRESULT Name([out, retval] BSTR *Name);
    [propget] HRESULT Id([out, retval] BSTR *Id);
    [propget] HRESULT State([out, retval] InkCursorButtonState *CurrentState);
}

[
    object,
    uuid(3671cC40-b624-4671-9fa0-db119d952d54),
    dual
]
interface IInkCursorButtons : IDispatch
{
    [propget] HRESULT Count([out, retval] long *Count);
    [propget] HRESULT _NewEnum([out, retval] IUnknown **_NewEnum);
    HRESULT Item([in] VARIANT Identifier, [out, retval] IInkCursorButton **Button);
}

[
    object,
    uuid(ad30c630-40c5-4350-8405-9c71012fc558),
    dual
]
interface IInkCursor : IDispatch
{
    [propget] HRESULT Name([out, retval] BSTR *Name);
    [propget] HRESULT Id([out, retval] long *Id);
    [propget] HRESULT Inverted([out, retval] VARIANT_BOOL *Status);
    [propget] HRESULT DrawingAttributes([out, retval] IInkDrawingAttributes **Attributes);
    [propputref] HRESULT DrawingAttributes([in] IInkDrawingAttributes *Attributes);
    [propget] HRESULT Tablet([out, retval] IInkTablet **Tablet);
    [propget] HRESULT Buttons([out, retval] IInkCursorButtons **Buttons);
}

[
    object,
    uuid(615f1d43-8703-4565-88e2-8201d2ecd7b7),
    dual
]
interface IInkTransform : IDispatch
{
    HRESULT Reset();
    HRESULT Translate([in] float HorizontalComponent, [in] float VerticalComponent);
    HRESULT Rotate([in] float Degrees, [in, defaultvalue(0)] float x, [in, defaultvalue(0)] float y);
    HRESULT Reflect([in] VARIANT_BOOL Horizontally, [in] VARIANT_BOOL Vertically);
    HRESULT Shear([in] float HorizontalComponent, [in] float VerticalComponent);
    HRESULT ScaleTransform([in] float HorizontalMultiplier, [in] float VerticalMultiplier);
    HRESULT GetTransform([out] float *eM11, [out] float *eM12, [out] float *eM21, [out] float *eM22,
            [out] float *eDx, [out] float *eDy);
    HRESULT SetTransform([in] float eM11, [in] float eM12, [in] float eM21, [in] float eM22, [in] float eDx, [in] float eDy);
    [propget, hidden] HRESULT eM11([out, retval] float *Value);
    [propput, hidden] HRESULT eM11([in] float Value);
    [propget, hidden] HRESULT eM12([out, retval] float *Value);
    [propput, hidden] HRESULT eM12([in] float Value);
    [propget, hidden] HRESULT eM21([out, retval] float *Value);
    [propput, hidden] HRESULT eM21([in] float Value);
    [propget, hidden] HRESULT eM22([out, retval] float *Value);
    [propput, hidden] HRESULT eM22([in] float Value);
    [propget, hidden] HRESULT eDx([out, retval] float *Value);
    [propput, hidden] HRESULT eDx([in] float Value);
    [propget, hidden] HRESULT eDy([out, retval] float *Value);
    [propput, hidden] HRESULT eDy([in] float Value);
    [propget, hidden] HRESULT Data([out, retval] XFORM *XForm);
    [propput, hidden] HRESULT Data([in] XFORM XForm);
}

[
    object,
    uuid(286a167f-9f19-4c61-9d53-4f07be622b84),
    dual
]
interface IInkRecognitionAlternates : IDispatch
{
    [propget] HRESULT Count([out, retval] long *Count);
    [propget, restricted] HRESULT _NewEnum([out, retval] IUnknown **_NewEnum);
    [propget] HRESULT Strokes([out, retval] IInkStrokes **Strokes);
    HRESULT Item([in] long Index, [out, retval] IInkRecognitionAlternate **InkRecoAlternate);
}

[
    object,
    uuid(b7e660ad-77e4-429b-adda-873780d1fc4a),
    dual
]
interface IInkRecognitionAlternate : IDispatch
{
    [propget] HRESULT String([out, retval] BSTR *RecoString);
    [propget] HRESULT Confidence([out, retval] InkRecognitionConfidence *Confidence) ;
    [propget] HRESULT Baseline([out, retval] VARIANT *Baseline);
    [propget] HRESULT Midline([out, retval] VARIANT *Midline);
    [propget] HRESULT Ascender([out, retval] VARIANT *Ascender);
    [propget] HRESULT Descender([out, retval] VARIANT *Descender);
    [propget] HRESULT LineNumber([out, retval] long *LineNumber);
    [propget] HRESULT Strokes([out, retval] IInkStrokes **Strokes);
    [propget] HRESULT LineAlternates([out, retval] IInkRecognitionAlternates **LineAlternates);
    [propget] HRESULT ConfidenceAlternates([out, retval] IInkRecognitionAlternates **ConfidenceAlternates);
    HRESULT GetStrokesFromStrokeRanges([in] IInkStrokes *Strokes, [out, retval] IInkStrokes **GetStrokesFromStrokeRanges);
    HRESULT GetStrokesFromTextRange([in, out] long *selectionStart, [in, out] long *selectionLength,
            [out, retval] IInkStrokes **GetStrokesFromTextRange);
    HRESULT GetTextRangeFromStrokes([in] IInkStrokes *Strokes, [in, out] long *selectionStart, [in, out] long *selectionLength);
    HRESULT AlternatesWithConstantPropertyValues([in] BSTR PropertyType,
            [out, retval] IInkRecognitionAlternates **AlternatesWithConstantPropertyValues);
    HRESULT GetPropertyValue([in] BSTR PropertyType, [out, retval] VARIANT *PropertyValue);
}

[
    object,
    uuid(3bc129a8-86cd-45ad-bde8-e0d32d61c16d),
    dual
]
interface IInkRecognitionResult : IDispatch
{
    [propget] HRESULT TopString([out, retval] BSTR *TopString);
    [propget] HRESULT TopAlternate([out, retval] IInkRecognitionAlternate **TopAlternate);
    [propget] HRESULT TopConfidence([out, retval] InkRecognitionConfidence *TopConfidence);
    [propget] HRESULT Strokes([out, retval] IInkStrokes **Strokes);
    HRESULT AlternatesFromSelection([in] long selectionStart, [in] long selectionLength, [in] long maximumAlternates,
            [out, retval] IInkRecognitionAlternates **AlternatesFromSelection);
    HRESULT ModifyTopAlternate([in] IInkRecognitionAlternate *Alternate);
    HRESULT SetResultOnStrokes();
}

[
    object,
    uuid(43242fea-91d1-4a72-963e-fbb91829cfa2),
    dual
]
interface IInkStrokeDisp : IDispatch
{
    [propget] HRESULT ID([out, retval] long *ID);
    [propget] HRESULT BezierPoints([out, retval] VARIANT *Points);
    [propget] HRESULT DrawingAttributes([out, retval] IInkDrawingAttributes **DrawAttrs);
    [propputref] HRESULT DrawingAttributes([in] IInkDrawingAttributes *DrawAttrs);
    [propget] HRESULT Ink([out, retval] IInkDisp **Ink);
    [propget] HRESULT ExtendedProperties([out, retval] IInkExtendedProperties **Properties);
    [propget] HRESULT PolylineCusps([out, retval] VARIANT *Cusps);
    [propget] HRESULT BezierCusps([out, retval] VARIANT *Cusps);
    [propget] HRESULT SelfIntersections([out, retval] VARIANT *Intersections);
    [propget] HRESULT PacketCount([out, retval] long *plCount);
    [propget] HRESULT PacketSize([out, retval] long *plSize);
    [propget] HRESULT PacketDescription([out, retval] VARIANT *PacketDescription);
    [propget] HRESULT Deleted([out, retval] VARIANT_BOOL *Deleted);
    HRESULT GetBoundingBox([in] InkBoundingBoxMode BoundingBoxMode, [out, retval] IInkRectangle **Rectangle);
    HRESULT FindIntersections([in] IInkStrokes *Strokes, [out, retval] VARIANT *Intersections);
    HRESULT GetRectangleIntersections([in] IInkRectangle *Rectangle, [out, retval] VARIANT *Intersections);
    HRESULT Clip([in] IInkRectangle *Rectangle);
    HRESULT HitTestCircle([in] long X, [in] long Y, [in] float Radius, [out, retval] VARIANT_BOOL *Intersects);
    HRESULT NearestPoint([in] long X, [in] long Y, [in, out] float *Distance, [out, retval] float *Point);
    HRESULT Split([in] float SplitAt, [out, retval] IInkStrokeDisp **NewStroke);
    HRESULT GetPacketDescriptionPropertyMetrics([in] BSTR PropertyName, [out] long *Minimum, [out] long *Maximum,
            [out] TabletPropertyMetricUnit *Units, [out] float *Resolution);
    HRESULT GetPoints([in] long Index, [in] long Count, [out, retval] VARIANT *Points);
    HRESULT SetPoints([in] VARIANT Points, [in] long Index, [in] long Count, [out, retval] long *NumberOfPointsSet);
    HRESULT GetPacketData([in] long Index, [in] long Count, [out, retval] VARIANT *PacketData);
    HRESULT GetPacketValuesByProperty([in] BSTR PropertyName, [in] long Index, [in] long Count,
            [out, retval] VARIANT *PacketValues) ;
    HRESULT SetPacketValuesByProperty([in] BSTR bstrPropertyName, [in] VARIANT PacketValues, [in] long Index,
            [in] long Count, [out, retval] long *NumberOfPacketsSet);
    HRESULT GetFlattenedBezierPoints([in] long FittingError, [out, retval] VARIANT *FlattenedBezierPoints);
    HRESULT Transform([in] IInkTransform *Transform, [in, defaultvalue(0)] VARIANT_BOOL ApplyOnPenWidth);
    HRESULT ScaleToRectangle([in] IInkRectangle *Rectangle);
    HRESULT Move([in] float HorizontalComponent, [in] float VerticalComponent);
    HRESULT Rotate([in] float Degrees, [in, defaultvalue(0)] float x, [in, defaultvalue(0)] float y);
    HRESULT Shear([in] float HorizontalMultiplier, [in] float VerticalMultiplier);
    HRESULT ScaleTransform([in] float HorizontalMultiplier, [in] float VerticalMultiplier);
}

[
    object,
    uuid(7e23a88f-c30e-420f-9bdb-28902543f0c1),
    dual
]
interface IInkCustomStrokes : IDispatch
{
    [propget] HRESULT Count([out, retval] long *Count);
    [propget, restricted] HRESULT _NewEnum([out, retval] IUnknown **_NewEnum);
    HRESULT Item([in] VARIANT Identifier, [out, retval] IInkStrokes **Strokes);
    HRESULT Add([in] BSTR Name, [in] IInkStrokes *Strokes);
    HRESULT Remove([in] VARIANT Identifier);
    HRESULT Clear();
}

[
    object,
    uuid(9d398fa0-c4e2-4fcd-9973-975caaf47ea6),
    dual
]
interface IInkDisp : IDispatch
{
    [propget] HRESULT Strokes([out, retval] IInkStrokes **Strokes);
    [propget] HRESULT ExtendedProperties([out, retval] IInkExtendedProperties **Properties);
    [propget] HRESULT Dirty([out, retval] VARIANT_BOOL *Dirty);
    [propput] HRESULT Dirty([in] VARIANT_BOOL Dirty);
    [propget] HRESULT CustomStrokes([out, retval] IInkCustomStrokes **ppunkInkCustomStrokes);
    HRESULT GetBoundingBox([in] InkBoundingBoxMode BoundingBoxMode, [out, retval] IInkRectangle **Rectangle);
    HRESULT DeleteStrokes([in, unique, defaultvalue(0)] IInkStrokes *Strokes);
    HRESULT DeleteStroke([in] IInkStrokeDisp *Stroke);
    HRESULT ExtractStrokes([in] IInkStrokes *Strokes, InkExtractFlags ExtractFlags, [out, retval] IInkDisp **ExtractedInk);
    HRESULT ExtractWithRectangle([in] IInkRectangle *Rectangle, [in] InkExtractFlags extractFlags,
            [out, retval] IInkDisp **ExtractedInk);
    HRESULT Clip([in] IInkRectangle *Rectangle);
    HRESULT Clone([out, retval] IInkDisp **NewInk);
    HRESULT HitTestCircle([in] long X, [in] long Y, [in] float radius, [out, retval] IInkStrokes **Strokes);
    HRESULT HitTestWithRectangle([in] IInkRectangle *SelectionRectangle, [in] float IntersectPercent,
            [out, retval] IInkStrokes **Strokes);
    HRESULT HitTestWithLasso([in] VARIANT Points, [in] float IntersectPercent, [in, out, unique] VARIANT *LassoPoints,
            [out, retval] IInkStrokes **Strokes);
    HRESULT NearestPoint([in] long X, [in] long Y, [in, out] float *PointOnStroke, [in, out] float *DistanceFromPacket,
            [out, retval] IInkStrokeDisp **Stroke);
    HRESULT CreateStrokes([in] VARIANT StrokeIds, [out, retval] IInkStrokes **Strokes);
    HRESULT AddStrokesAtRectangle([in] IInkStrokes *SourceStrokes, [in] IInkRectangle *TargetRectangle);
    HRESULT Save([in] InkPersistenceFormat PersistenceFormat, [in] InkPersistenceCompressionMode CompressionMode,
            [out, retval] VARIANT *Data);
    HRESULT Load([in] VARIANT Data);
    HRESULT CreateStroke([in] VARIANT PacketData, [in] VARIANT PacketDescription, [out, retval] IInkStrokeDisp **Stroke);
    HRESULT ClipboardCopyWithRectangle([in] IInkRectangle *Rectangle, [in] InkClipboardFormats ClipboardFormats,
            [in] InkClipboardModes ClipboardModes, [out, retval] IDataObject **DataObject);
    HRESULT ClipboardCopy([in] IInkStrokes *strokes, [in] InkClipboardFormats ClipboardFormats,
            [in] InkClipboardModes ClipboardModes, [out, retval] IDataObject **DataObject);
    HRESULT CanPaste([in] IDataObject *DataObject, [out, retval] VARIANT_BOOL *CanPaste);
    HRESULT ClipboardPaste([in] long x, [in] long y, [in, unique] IDataObject *DataObject, [out, retval] IInkStrokes **Strokes);
}

[
    object,
    uuid(f1f4c9d8-590a-4963-b3ae-1935671bb6f3),
    dual
]
interface IInkStrokes : IDispatch
{
    [propget] HRESULT Count([out, retval] long *Count);
    [propget] HRESULT _NewEnum([out, retval] IUnknown **_NewEnum);
    [propget] HRESULT Ink([out, retval] IInkDisp **Ink);
    [propget] HRESULT RecognitionResult([out, retval] IInkRecognitionResult **RecognitionResult);
    HRESULT ToString([out, retval] BSTR *ToString);
    HRESULT Item([in] long Index, [out, retval] IInkStrokeDisp **Stroke);
    HRESULT Add([in] IInkStrokeDisp *InkStroke);
    HRESULT AddStrokes([in] IInkStrokes *InkStrokes);
    HRESULT Remove([in] IInkStrokeDisp *InkStroke);
    HRESULT RemoveStrokes([in] IInkStrokes *InkStrokes);
    HRESULT ModifyDrawingAttributes([in] IInkDrawingAttributes *DrawAttrs);
    HRESULT GetBoundingBox([in] InkBoundingBoxMode BoundingBoxMode, [out, retval] IInkRectangle **BoundingBox);
    HRESULT Transform([in] IInkTransform *Transform, [in, defaultvalue(0)] VARIANT_BOOL ApplyOnPenWidth);
    HRESULT ScaleToRectangle([in] IInkRectangle *Rectangle);
    HRESULT Move([in] float HorizontalComponent, [in] float VerticalComponent);
    HRESULT Rotate([in] float Degrees, [in, defaultvalue(0)] float x, [in, defaultvalue(0)] float y);
    HRESULT Shear([in] float HorizontalMultiplier, [in] float VerticalMultiplier);
    HRESULT ScaleTransform([in] float HorizontalMultiplier, [in] float VerticalMultiplier);
    HRESULT Clip([in] IInkRectangle *Rectangle);
    HRESULT RemoveRecognitionResult();
}

[
    object,
    uuid(e6257a9c-b511-4f4c-a8b0-a7dbc9506b83),
    dual
]
interface IInkRenderer : IDispatch
{
    HRESULT GetViewTransform([in] IInkTransform *ViewTransform) ;
    HRESULT SetViewTransform([in] IInkTransform *ViewTransform);
    HRESULT GetObjectTransform([in] IInkTransform *ObjectTransform);
    HRESULT SetObjectTransform([in] IInkTransform *ObjectTransform);
    HRESULT Draw([in] LONG_PTR hDC, [in] IInkStrokes *Strokes);
    HRESULT DrawStroke([in] LONG_PTR hDC, [in] IInkStrokeDisp *Stroke,
            [in, defaultvalue(0)] IInkDrawingAttributes *DrawingAttributes);
    HRESULT PixelToInkSpace([in] LONG_PTR hDC, [in, out] long *x, [in, out] long *y);
    HRESULT InkSpaceToPixel([in] LONG_PTR hdcDisplay, [in, out] long *x, [in, out] long *y);
    HRESULT PixelToInkSpaceFromPoints([in] LONG_PTR hDC, [in, out] VARIANT *Points);
    HRESULT InkSpaceToPixelFromPoints([in] LONG_PTR hDC, [in, out] VARIANT *Points);
    HRESULT Measure([in] IInkStrokes *Strokes, [out, retval] IInkRectangle **Rectangle);
    HRESULT MeasureStroke([in] IInkStrokeDisp *Stroke, [in] IInkDrawingAttributes *DrawingAttributes,
            [out, retval] IInkRectangle **Rectangle);
    HRESULT Move([in] float HorizontalComponent, [in] float VerticalComponent);
    HRESULT Rotate([in] float Degrees, [in, defaultvalue(0)] float x, [in, defaultvalue(0)] float y);
    HRESULT ScaleTransform([in] float HorizontalMultiplier, [in] float VerticalMultiplier,
            [in, defaultvalue(-1)] VARIANT_BOOL ApplyOnPenWidth);
}

[
    object,
    uuid(a248c1ac-c698-4e06-9e5c-d57f77c7e647),
    dual
]
interface IInkCursors : IDispatch
{
    [propget] HRESULT Count([out, retval] long *Count);
    [propget, restricted] HRESULT _NewEnum([out, retval] IUnknown **_NewEnum);
    HRESULT Item([in] long Index, [out, retval] IInkCursor **Cursor);
}

[
    object,
    uuid(f0f060b5-8b1f-4a7c-89ec-880692588a4f),
    dual
]
interface IInkCollector : IDispatch
{
    [propget] HRESULT hWnd([out, retval] LONG_PTR *CurrentWindow);
    [propput] HRESULT hWnd([in] LONG_PTR NewWindow);
    [propget] HRESULT Enabled([out, retval] VARIANT_BOOL *Collecting);
    [propput] HRESULT Enabled([in] VARIANT_BOOL Collecting);
    [propget] HRESULT DefaultDrawingAttributes([out, retval] IInkDrawingAttributes **CurrentAttributes);
    [propputref] HRESULT DefaultDrawingAttributes([in] IInkDrawingAttributes *NewAttributes);
    [propget] HRESULT Renderer([out, retval] IInkRenderer **CurrentInkRenderer);
    [propputref] HRESULT Renderer([in] IInkRenderer *NewInkRenderer);
    [propget] HRESULT Ink([out, retval] IInkDisp **Ink);
    [propputref] HRESULT Ink([in] IInkDisp *NewInk);
    [propget] HRESULT AutoRedraw([out, retval] VARIANT_BOOL *AutoRedraw);
    [propput] HRESULT AutoRedraw([in] VARIANT_BOOL AutoRedraw);
    [propget] HRESULT CollectingInk([out, retval] VARIANT_BOOL *Collecting);
    [propget] HRESULT CollectionMode([out, retval] InkCollectionMode *Mode);
    [propput] HRESULT CollectionMode([in] InkCollectionMode Mode);
    [propget] HRESULT DynamicRendering([out, retval] VARIANT_BOOL *Enabled);
    [propput] HRESULT DynamicRendering([in] VARIANT_BOOL Enabled);
    [propget] HRESULT DesiredPacketDescription([out, retval] VARIANT *PacketGuids);
    [propput] HRESULT DesiredPacketDescription([in] VARIANT PacketGuids);
    [propget] HRESULT MouseIcon([out, retval] IPictureDisp **MouseIcon);
    [propput] HRESULT MouseIcon([in, unique] IPictureDisp *MouseIcon);
    [propputref] HRESULT MouseIcon([in, unique] IPictureDisp *MouseIcon);
    [propget] HRESULT MousePointer([out, retval] InkMousePointer *MousePointer);
    [propput] HRESULT MousePointer([in] InkMousePointer MousePointer);
    [propget] HRESULT Cursors([out, retval] IInkCursors **Cursors);
    [propget] HRESULT MarginX([out, retval] long *MarginX);
    [propput] HRESULT MarginX([in] long MarginX);
    [propget] HRESULT MarginY([out, retval] long *MarginY);
    [propput] HRESULT MarginY([in] long MarginY);
    [propget] HRESULT Tablet([out, retval] IInkTablet **SingleTablet);
    [propget] HRESULT SupportHighContrastInk([out, retval] VARIANT_BOOL *Support);
    [propput] HRESULT SupportHighContrastInk([in] VARIANT_BOOL Support);
    HRESULT SetGestureStatus([in] InkApplicationGesture Gesture, [in] VARIANT_BOOL Listen);
    HRESULT GetGestureStatus([in] InkApplicationGesture Gesture, [out, retval] VARIANT_BOOL *Listening);
    HRESULT GetWindowInputRectangle([in, out] IInkRectangle **WindowInputRectangle);
    HRESULT SetWindowInputRectangle([in] IInkRectangle *WindowInputRectangle);
    HRESULT SetAllTabletsMode([in, defaultvalue(-1)] VARIANT_BOOL UseMouseForInput);
    HRESULT SetSingleTabletIntegratedMode([in] IInkTablet *Tablet);
    HRESULT GetEventInterest([in] InkCollectorEventInterest EventId, [out, retval] VARIANT_BOOL *Listen);
    HRESULT SetEventInterest([in] InkCollectorEventInterest EventId, [in] VARIANT_BOOL Listen);
}

[
    uuid(11a583f2-712d-4fea-abcf-ab4af38ea06b)
]
dispinterface _IInkCollectorEvents
{
    properties:
    methods:
    /* FIXME */
}

[
    uuid(43fb1553-ad74-4ee8-88e4-3e6daac915db)
]
coclass InkCollector
{
}
