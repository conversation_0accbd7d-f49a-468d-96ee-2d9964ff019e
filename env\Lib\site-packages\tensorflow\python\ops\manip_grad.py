# Copyright 2015 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""Gradients for operators defined in manip_ops.py."""

from tensorflow.python.framework import ops
from tensorflow.python.ops import manip_ops


@ops.RegisterGradient("Roll")
def _RollGrad(op, grad):
  # The gradient is just the roll reversed
  shift = op.inputs[1]
  axis = op.inputs[2]
  roll_grad = manip_ops.roll(grad, -shift, axis)
  return roll_grad, None, None
