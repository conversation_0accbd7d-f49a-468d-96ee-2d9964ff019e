@echo off
REM Windows下编译denoise_training的脚本
REM 首先检查是否有gcc编译器，如果没有则提示安装

echo ========================================
echo RNNoise denoise_training 编译脚本
echo ========================================

REM 检查是否有gcc编译器
where gcc >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到GCC编译器
    echo.
    echo 请选择以下方式之一安装编译器:
    echo 1. 运行 python download_mingw.py 自动下载MinGW
    echo 2. 手动安装MinGW-w64或MSYS2
    echo 3. 安装Visual Studio Build Tools
    echo.
    pause
    exit /b 1
)

echo 找到GCC编译器:
gcc --version | findstr gcc

echo.
echo 切换到src目录...
cd src

echo.
echo 开始编译...
echo 编译命令: gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnnoise_data.c -o denoise_training -lm

gcc -DTRAINING=1 -Wall -W -O3 -g -I../include denoise.c kiss_fft.c pitch.c celt_lpc.c rnn.c rnnoise_data.c -o denoise_training -lm

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✓ 编译成功！
    echo ========================================
    echo.
    echo 生成的可执行文件: src\denoise_training.exe
    echo.
    echo 使用方法:
    echo   denoise_training input.pcm output.pcm
    echo.
    echo 示例:
    echo   denoise_training ..\data\noisy_audio.pcm clean_output.pcm
    echo.
) else (
    echo.
    echo ========================================
    echo ✗ 编译失败！
    echo ========================================
    echo.
    echo 可能的原因:
    echo 1. 缺少必要的头文件
    echo 2. 编译器版本不兼容
    echo 3. 源文件有错误
    echo.
    echo 请检查错误信息并修复问题
)

cd ..
echo.
echo 按任意键退出...
pause >nul
