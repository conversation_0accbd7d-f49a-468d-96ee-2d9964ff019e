// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/tsl/protobuf/status.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fstatus_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fstatus_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "xla/tsl/protobuf/error_codes.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2ftsl_2fprotobuf_2fstatus_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2ftsl_2fprotobuf_2fstatus_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2ftsl_2fprotobuf_2fstatus_2eproto;
namespace tensorflow {
class StatusProto;
struct StatusProtoDefaultTypeInternal;
extern StatusProtoDefaultTypeInternal _StatusProto_default_instance_;
class StatusProto_PayloadEntry_DoNotUse;
struct StatusProto_PayloadEntry_DoNotUseDefaultTypeInternal;
extern StatusProto_PayloadEntry_DoNotUseDefaultTypeInternal _StatusProto_PayloadEntry_DoNotUse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::StatusProto* Arena::CreateMaybeMessage<::tensorflow::StatusProto>(Arena*);
template<> ::tensorflow::StatusProto_PayloadEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::StatusProto_PayloadEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class StatusProto_PayloadEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<StatusProto_PayloadEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BYTES> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<StatusProto_PayloadEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BYTES> SuperType;
  StatusProto_PayloadEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR StatusProto_PayloadEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit StatusProto_PayloadEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const StatusProto_PayloadEntry_DoNotUse& other);
  static const StatusProto_PayloadEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const StatusProto_PayloadEntry_DoNotUse*>(&_StatusProto_PayloadEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.StatusProto.PayloadEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fstatus_2eproto;
};

// -------------------------------------------------------------------

class StatusProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StatusProto) */ {
 public:
  inline StatusProto() : StatusProto(nullptr) {}
  ~StatusProto() override;
  explicit PROTOBUF_CONSTEXPR StatusProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StatusProto(const StatusProto& from);
  StatusProto(StatusProto&& from) noexcept
    : StatusProto() {
    *this = ::std::move(from);
  }

  inline StatusProto& operator=(const StatusProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline StatusProto& operator=(StatusProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StatusProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const StatusProto* internal_default_instance() {
    return reinterpret_cast<const StatusProto*>(
               &_StatusProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(StatusProto& a, StatusProto& b) {
    a.Swap(&b);
  }
  inline void Swap(StatusProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StatusProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StatusProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StatusProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StatusProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StatusProto& from) {
    StatusProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StatusProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StatusProto";
  }
  protected:
  explicit StatusProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kPayloadFieldNumber = 3,
    kMessageFieldNumber = 2,
    kCodeFieldNumber = 1,
  };
  // map<string, bytes> payload = 3;
  int payload_size() const;
  private:
  int _internal_payload_size() const;
  public:
  void clear_payload();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_payload() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_payload();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      payload() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_payload();

  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // .tensorflow.error.Code code = 1;
  void clear_code();
  ::tensorflow::error::Code code() const;
  void set_code(::tensorflow::error::Code value);
  private:
  ::tensorflow::error::Code _internal_code() const;
  void _internal_set_code(::tensorflow::error::Code value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.StatusProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        StatusProto_PayloadEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BYTES> payload_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
    int code_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2fstatus_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// StatusProto

// .tensorflow.error.Code code = 1;
inline void StatusProto::clear_code() {
  _impl_.code_ = 0;
}
inline ::tensorflow::error::Code StatusProto::_internal_code() const {
  return static_cast< ::tensorflow::error::Code >(_impl_.code_);
}
inline ::tensorflow::error::Code StatusProto::code() const {
  // @@protoc_insertion_point(field_get:tensorflow.StatusProto.code)
  return _internal_code();
}
inline void StatusProto::_internal_set_code(::tensorflow::error::Code value) {
  
  _impl_.code_ = value;
}
inline void StatusProto::set_code(::tensorflow::error::Code value) {
  _internal_set_code(value);
  // @@protoc_insertion_point(field_set:tensorflow.StatusProto.code)
}

// string message = 2;
inline void StatusProto::clear_message() {
  _impl_.message_.ClearToEmpty();
}
inline const std::string& StatusProto::message() const {
  // @@protoc_insertion_point(field_get:tensorflow.StatusProto.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StatusProto::set_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.StatusProto.message)
}
inline std::string* StatusProto::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.StatusProto.message)
  return _s;
}
inline const std::string& StatusProto::_internal_message() const {
  return _impl_.message_.Get();
}
inline void StatusProto::_internal_set_message(const std::string& value) {
  
  _impl_.message_.Set(value, GetArenaForAllocation());
}
inline std::string* StatusProto::_internal_mutable_message() {
  
  return _impl_.message_.Mutable(GetArenaForAllocation());
}
inline std::string* StatusProto::release_message() {
  // @@protoc_insertion_point(field_release:tensorflow.StatusProto.message)
  return _impl_.message_.Release();
}
inline void StatusProto::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  _impl_.message_.SetAllocated(message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.message_.IsDefault()) {
    _impl_.message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StatusProto.message)
}

// map<string, bytes> payload = 3;
inline int StatusProto::_internal_payload_size() const {
  return _impl_.payload_.size();
}
inline int StatusProto::payload_size() const {
  return _internal_payload_size();
}
inline void StatusProto::clear_payload() {
  _impl_.payload_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
StatusProto::_internal_payload() const {
  return _impl_.payload_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
StatusProto::payload() const {
  // @@protoc_insertion_point(field_map:tensorflow.StatusProto.payload)
  return _internal_payload();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
StatusProto::_internal_mutable_payload() {
  return _impl_.payload_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
StatusProto::mutable_payload() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.StatusProto.payload)
  return _internal_mutable_payload();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2fstatus_2eproto
