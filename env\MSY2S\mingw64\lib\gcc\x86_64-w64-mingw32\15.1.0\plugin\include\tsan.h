/* ThreadSanitizer, a data race detector.
   Copyright (C) 2011-2025 Free Software Foundation, Inc.
   Contributed by <PERSON> <dvy<PERSON><PERSON>@google.com>

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

#ifndef TREE_TSAN
#define TREE_TSAN

extern void tsan_finish_file (void);

#endif /* TREE_TSAN */
