/* Copyright 2023 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_COMMON_RUNTIME_EAGER_SUMMARY_OPTIMIZER_H_
#define TENSORFLOW_CORE_COMMON_RUNTIME_EAGER_SUMMARY_OPTIMIZER_H_

#include <string>
#include <utility>
#include <vector>

#include "absl/strings/string_view.h"
#include "tensorflow/core/framework/function.h"
#include "tensorflow/core/framework/function.pb.h"

namespace tensorflow::summary_optimizer {
namespace internal {

// Normalizes an edge's name to match the names stored in a NodeDef.
std::string NormalizeEdgeName(absl::string_view name);

}  // namespace internal

// Returns the name of the input_arg and the bool value that determines whether
// or not to disable summaries. If no such arg exists returns an empty string.
std::pair<absl::string_view, bool> GetDisableSummariesInputArg(
    const FunctionDef& fdef);

// Generates new FunctionDef(s) with the summaries stripped out.
// This function will traverse all the nested functions and generate a version
// of the nested functions with summaries stripped out.
std::vector<FunctionDef> StripSummaries(const FunctionDef& fdef,
                                        const FunctionLibraryDefinition& flib);

// Generates a new function name for the stripped function.
std::string StrippedFunctionName(absl::string_view fname);

}  // namespace tensorflow::summary_optimizer

#endif  // TENSORFLOW_CORE_COMMON_RUNTIME_EAGER_SUMMARY_OPTIMIZER_H_
