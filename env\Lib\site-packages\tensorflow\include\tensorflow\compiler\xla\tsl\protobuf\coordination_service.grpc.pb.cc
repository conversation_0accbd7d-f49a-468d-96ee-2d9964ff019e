// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: xla/tsl/protobuf/coordination_service.proto

#include "xla/tsl/protobuf/coordination_service.pb.h"
#include "xla/tsl/protobuf/coordination_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace tensorflow {

static const char* grpcCoordinationService_method_names[] = {
  "/tensorflow.CoordinationService/RegisterTask",
  "/tensorflow.CoordinationService/Heartbeat",
  "/tensorflow.CoordinationService/WaitForAllTasks",
  "/tensorflow.CoordinationService/ShutdownTask",
  "/tensorflow.CoordinationService/ResetTask",
  "/tensorflow.CoordinationService/ReportErrorToTask",
  "/tensorflow.CoordinationService/ReportErrorToService",
  "/tensorflow.CoordinationService/GetTaskState",
  "/tensorflow.CoordinationService/InsertKeyValue",
  "/tensorflow.CoordinationService/GetKeyValue",
  "/tensorflow.CoordinationService/TryGetKeyValue",
  "/tensorflow.CoordinationService/GetKeyValueDir",
  "/tensorflow.CoordinationService/DeleteKeyValue",
  "/tensorflow.CoordinationService/Barrier",
  "/tensorflow.CoordinationService/CancelBarrier",
  "/tensorflow.CoordinationService/GetAliveTasks",
  "/tensorflow.CoordinationService/PollForError",
};

std::unique_ptr< grpc::CoordinationService::Stub> grpc::CoordinationService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< grpc::CoordinationService::Stub> stub(new grpc::CoordinationService::Stub(channel));
  return stub;
}

grpc::CoordinationService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_RegisterTask_(grpcCoordinationService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Heartbeat_(grpcCoordinationService_method_names[1], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_WaitForAllTasks_(grpcCoordinationService_method_names[2], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ShutdownTask_(grpcCoordinationService_method_names[3], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResetTask_(grpcCoordinationService_method_names[4], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReportErrorToTask_(grpcCoordinationService_method_names[5], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReportErrorToService_(grpcCoordinationService_method_names[6], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTaskState_(grpcCoordinationService_method_names[7], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_InsertKeyValue_(grpcCoordinationService_method_names[8], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetKeyValue_(grpcCoordinationService_method_names[9], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_TryGetKeyValue_(grpcCoordinationService_method_names[10], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetKeyValueDir_(grpcCoordinationService_method_names[11], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteKeyValue_(grpcCoordinationService_method_names[12], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Barrier_(grpcCoordinationService_method_names[13], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CancelBarrier_(grpcCoordinationService_method_names[14], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetAliveTasks_(grpcCoordinationService_method_names[15], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_PollForError_(grpcCoordinationService_method_names[16], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status grpc::CoordinationService::Stub::RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::tensorflow::RegisterTaskResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_RegisterTask_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_RegisterTask_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::RegisterTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::RegisterTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_RegisterTask_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::RegisterTask(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_RegisterTask_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::RegisterTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::RegisterTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_RegisterTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::RegisterTaskResponse>* grpc::CoordinationService::Stub::AsyncRegisterTaskRaw(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::RegisterTaskResponse>::Create(channel_.get(), cq, rpcmethod_RegisterTask_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::RegisterTaskResponse>* grpc::CoordinationService::Stub::PrepareAsyncRegisterTaskRaw(::grpc::ClientContext* context, const ::tensorflow::RegisterTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::RegisterTaskResponse>::Create(channel_.get(), cq, rpcmethod_RegisterTask_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::tensorflow::HeartbeatResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Heartbeat_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Heartbeat_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::Heartbeat(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::HeartbeatResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Heartbeat_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::Heartbeat(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Heartbeat_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::Heartbeat(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::HeartbeatResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Heartbeat_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::HeartbeatResponse>* grpc::CoordinationService::Stub::AsyncHeartbeatRaw(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::HeartbeatResponse>::Create(channel_.get(), cq, rpcmethod_Heartbeat_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::HeartbeatResponse>* grpc::CoordinationService::Stub::PrepareAsyncHeartbeatRaw(::grpc::ClientContext* context, const ::tensorflow::HeartbeatRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::HeartbeatResponse>::Create(channel_.get(), cq, rpcmethod_Heartbeat_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::tensorflow::WaitForAllTasksResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_WaitForAllTasks_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_WaitForAllTasks_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::WaitForAllTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::WaitForAllTasksResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_WaitForAllTasks_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::WaitForAllTasks(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_WaitForAllTasks_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::WaitForAllTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::WaitForAllTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_WaitForAllTasks_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::WaitForAllTasksResponse>* grpc::CoordinationService::Stub::AsyncWaitForAllTasksRaw(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::WaitForAllTasksResponse>::Create(channel_.get(), cq, rpcmethod_WaitForAllTasks_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::WaitForAllTasksResponse>* grpc::CoordinationService::Stub::PrepareAsyncWaitForAllTasksRaw(::grpc::ClientContext* context, const ::tensorflow::WaitForAllTasksRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::WaitForAllTasksResponse>::Create(channel_.get(), cq, rpcmethod_WaitForAllTasks_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::tensorflow::ShutdownTaskResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_ShutdownTask_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_ShutdownTask_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::ShutdownTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ShutdownTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_ShutdownTask_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::ShutdownTask(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_ShutdownTask_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::ShutdownTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ShutdownTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_ShutdownTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ShutdownTaskResponse>* grpc::CoordinationService::Stub::AsyncShutdownTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ShutdownTaskResponse>::Create(channel_.get(), cq, rpcmethod_ShutdownTask_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ShutdownTaskResponse>* grpc::CoordinationService::Stub::PrepareAsyncShutdownTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ShutdownTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ShutdownTaskResponse>::Create(channel_.get(), cq, rpcmethod_ShutdownTask_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::tensorflow::ResetTaskResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_ResetTask_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_ResetTask_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::ResetTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ResetTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_ResetTask_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::ResetTask(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_ResetTask_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::ResetTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ResetTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_ResetTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ResetTaskResponse>* grpc::CoordinationService::Stub::AsyncResetTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ResetTaskResponse>::Create(channel_.get(), cq, rpcmethod_ResetTask_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ResetTaskResponse>* grpc::CoordinationService::Stub::PrepareAsyncResetTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ResetTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ResetTaskResponse>::Create(channel_.get(), cq, rpcmethod_ResetTask_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::tensorflow::ReportErrorToTaskResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_ReportErrorToTask_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_ReportErrorToTask_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::ReportErrorToTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToTaskResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_ReportErrorToTask_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::ReportErrorToTask(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_ReportErrorToTask_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::ReportErrorToTask(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToTaskResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_ReportErrorToTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToTaskResponse>* grpc::CoordinationService::Stub::AsyncReportErrorToTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ReportErrorToTaskResponse>::Create(channel_.get(), cq, rpcmethod_ReportErrorToTask_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToTaskResponse>* grpc::CoordinationService::Stub::PrepareAsyncReportErrorToTaskRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ReportErrorToTaskResponse>::Create(channel_.get(), cq, rpcmethod_ReportErrorToTask_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::tensorflow::ReportErrorToServiceResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_ReportErrorToService_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_ReportErrorToService_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::ReportErrorToService(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToServiceResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_ReportErrorToService_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::ReportErrorToService(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_ReportErrorToService_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::ReportErrorToService(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::ReportErrorToServiceResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_ReportErrorToService_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToServiceResponse>* grpc::CoordinationService::Stub::AsyncReportErrorToServiceRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ReportErrorToServiceResponse>::Create(channel_.get(), cq, rpcmethod_ReportErrorToService_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::ReportErrorToServiceResponse>* grpc::CoordinationService::Stub::PrepareAsyncReportErrorToServiceRaw(::grpc::ClientContext* context, const ::tensorflow::ReportErrorToServiceRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::ReportErrorToServiceResponse>::Create(channel_.get(), cq, rpcmethod_ReportErrorToService_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::tensorflow::GetTaskStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_GetTaskState_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_GetTaskState_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::GetTaskState(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetTaskStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_GetTaskState_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::GetTaskState(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_GetTaskState_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::GetTaskState(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetTaskStateResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_GetTaskState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::GetTaskStateResponse>* grpc::CoordinationService::Stub::AsyncGetTaskStateRaw(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::GetTaskStateResponse>::Create(channel_.get(), cq, rpcmethod_GetTaskState_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::GetTaskStateResponse>* grpc::CoordinationService::Stub::PrepareAsyncGetTaskStateRaw(::grpc::ClientContext* context, const ::tensorflow::GetTaskStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::GetTaskStateResponse>::Create(channel_.get(), cq, rpcmethod_GetTaskState_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::tensorflow::InsertKeyValueResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_InsertKeyValue_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_InsertKeyValue_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::InsertKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::InsertKeyValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_InsertKeyValue_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::InsertKeyValue(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_InsertKeyValue_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::InsertKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::InsertKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_InsertKeyValue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::InsertKeyValueResponse>* grpc::CoordinationService::Stub::AsyncInsertKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::InsertKeyValueResponse>::Create(channel_.get(), cq, rpcmethod_InsertKeyValue_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::InsertKeyValueResponse>* grpc::CoordinationService::Stub::PrepareAsyncInsertKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::InsertKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::InsertKeyValueResponse>::Create(channel_.get(), cq, rpcmethod_InsertKeyValue_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::tensorflow::GetKeyValueResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_GetKeyValue_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_GetKeyValue_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::GetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_GetKeyValue_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::GetKeyValue(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_GetKeyValue_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::GetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_GetKeyValue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueResponse>* grpc::CoordinationService::Stub::AsyncGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::GetKeyValueResponse>::Create(channel_.get(), cq, rpcmethod_GetKeyValue_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueResponse>* grpc::CoordinationService::Stub::PrepareAsyncGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::GetKeyValueResponse>::Create(channel_.get(), cq, rpcmethod_GetKeyValue_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::tensorflow::TryGetKeyValueResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_TryGetKeyValue_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_TryGetKeyValue_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::TryGetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TryGetKeyValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_TryGetKeyValue_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::TryGetKeyValue(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_TryGetKeyValue_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::TryGetKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::TryGetKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_TryGetKeyValue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::TryGetKeyValueResponse>* grpc::CoordinationService::Stub::AsyncTryGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::TryGetKeyValueResponse>::Create(channel_.get(), cq, rpcmethod_TryGetKeyValue_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::TryGetKeyValueResponse>* grpc::CoordinationService::Stub::PrepareAsyncTryGetKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::TryGetKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::TryGetKeyValueResponse>::Create(channel_.get(), cq, rpcmethod_TryGetKeyValue_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::tensorflow::GetKeyValueDirResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_GetKeyValueDir_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_GetKeyValueDir_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::GetKeyValueDir(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueDirResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_GetKeyValueDir_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::GetKeyValueDir(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_GetKeyValueDir_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::GetKeyValueDir(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetKeyValueDirResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_GetKeyValueDir_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueDirResponse>* grpc::CoordinationService::Stub::AsyncGetKeyValueDirRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::GetKeyValueDirResponse>::Create(channel_.get(), cq, rpcmethod_GetKeyValueDir_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::GetKeyValueDirResponse>* grpc::CoordinationService::Stub::PrepareAsyncGetKeyValueDirRaw(::grpc::ClientContext* context, const ::tensorflow::GetKeyValueDirRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::GetKeyValueDirResponse>::Create(channel_.get(), cq, rpcmethod_GetKeyValueDir_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::tensorflow::DeleteKeyValueResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_DeleteKeyValue_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_DeleteKeyValue_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::DeleteKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::DeleteKeyValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_DeleteKeyValue_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::DeleteKeyValue(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_DeleteKeyValue_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::DeleteKeyValue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::DeleteKeyValueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_DeleteKeyValue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::DeleteKeyValueResponse>* grpc::CoordinationService::Stub::AsyncDeleteKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::DeleteKeyValueResponse>::Create(channel_.get(), cq, rpcmethod_DeleteKeyValue_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::DeleteKeyValueResponse>* grpc::CoordinationService::Stub::PrepareAsyncDeleteKeyValueRaw(::grpc::ClientContext* context, const ::tensorflow::DeleteKeyValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::DeleteKeyValueResponse>::Create(channel_.get(), cq, rpcmethod_DeleteKeyValue_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::tensorflow::BarrierResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Barrier_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Barrier_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::Barrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::BarrierResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_Barrier_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::Barrier(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Barrier_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::Barrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::BarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_Barrier_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::BarrierResponse>* grpc::CoordinationService::Stub::AsyncBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::BarrierResponse>::Create(channel_.get(), cq, rpcmethod_Barrier_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::BarrierResponse>* grpc::CoordinationService::Stub::PrepareAsyncBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::BarrierRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::BarrierResponse>::Create(channel_.get(), cq, rpcmethod_Barrier_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::tensorflow::CancelBarrierResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_CancelBarrier_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_CancelBarrier_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::CancelBarrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::CancelBarrierResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_CancelBarrier_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::CancelBarrier(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_CancelBarrier_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::CancelBarrier(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::CancelBarrierResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_CancelBarrier_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::CancelBarrierResponse>* grpc::CoordinationService::Stub::AsyncCancelBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::CancelBarrierResponse>::Create(channel_.get(), cq, rpcmethod_CancelBarrier_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::CancelBarrierResponse>* grpc::CoordinationService::Stub::PrepareAsyncCancelBarrierRaw(::grpc::ClientContext* context, const ::tensorflow::CancelBarrierRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::CancelBarrierResponse>::Create(channel_.get(), cq, rpcmethod_CancelBarrier_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::tensorflow::GetAliveTasksResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_GetAliveTasks_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_GetAliveTasks_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::GetAliveTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetAliveTasksResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_GetAliveTasks_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::GetAliveTasks(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_GetAliveTasks_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::GetAliveTasks(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::GetAliveTasksResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_GetAliveTasks_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::GetAliveTasksResponse>* grpc::CoordinationService::Stub::AsyncGetAliveTasksRaw(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::GetAliveTasksResponse>::Create(channel_.get(), cq, rpcmethod_GetAliveTasks_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::GetAliveTasksResponse>* grpc::CoordinationService::Stub::PrepareAsyncGetAliveTasksRaw(::grpc::ClientContext* context, const ::tensorflow::GetAliveTasksRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::GetAliveTasksResponse>::Create(channel_.get(), cq, rpcmethod_GetAliveTasks_, context, request, false);
}

::grpc::Status grpc::CoordinationService::Stub::PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::tensorflow::PollForErrorResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_PollForError_, context, request, response);
}

void grpc::CoordinationService::Stub::experimental_async::PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_PollForError_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::PollForError(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::PollForErrorResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc_impl::internal::CallbackUnaryCall(stub_->channel_.get(), stub_->rpcmethod_PollForError_, context, request, response, std::move(f));
}

void grpc::CoordinationService::Stub::experimental_async::PollForError(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_PollForError_, context, request, response, reactor);
}

void grpc::CoordinationService::Stub::experimental_async::PollForError(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::PollForErrorResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) {
  ::grpc_impl::internal::ClientCallbackUnaryFactory::Create(stub_->channel_.get(), stub_->rpcmethod_PollForError_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::PollForErrorResponse>* grpc::CoordinationService::Stub::AsyncPollForErrorRaw(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::PollForErrorResponse>::Create(channel_.get(), cq, rpcmethod_PollForError_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::tensorflow::PollForErrorResponse>* grpc::CoordinationService::Stub::PrepareAsyncPollForErrorRaw(::grpc::ClientContext* context, const ::tensorflow::PollForErrorRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc_impl::internal::ClientAsyncResponseReaderFactory< ::tensorflow::PollForErrorResponse>::Create(channel_.get(), cq, rpcmethod_PollForError_, context, request, false);
}

grpc::CoordinationService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::RegisterTaskRequest, ::tensorflow::RegisterTaskResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::RegisterTask), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::HeartbeatRequest, ::tensorflow::HeartbeatResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::Heartbeat), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::WaitForAllTasksRequest, ::tensorflow::WaitForAllTasksResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::WaitForAllTasks), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::ShutdownTaskRequest, ::tensorflow::ShutdownTaskResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::ShutdownTask), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::ResetTaskRequest, ::tensorflow::ResetTaskResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::ResetTask), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::ReportErrorToTaskRequest, ::tensorflow::ReportErrorToTaskResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::ReportErrorToTask), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::ReportErrorToServiceRequest, ::tensorflow::ReportErrorToServiceResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::ReportErrorToService), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::GetTaskStateRequest, ::tensorflow::GetTaskStateResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::GetTaskState), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::InsertKeyValueRequest, ::tensorflow::InsertKeyValueResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::InsertKeyValue), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::GetKeyValueRequest, ::tensorflow::GetKeyValueResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::GetKeyValue), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::TryGetKeyValueRequest, ::tensorflow::TryGetKeyValueResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::TryGetKeyValue), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::GetKeyValueDirRequest, ::tensorflow::GetKeyValueDirResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::GetKeyValueDir), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::DeleteKeyValueRequest, ::tensorflow::DeleteKeyValueResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::DeleteKeyValue), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::BarrierRequest, ::tensorflow::BarrierResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::Barrier), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::CancelBarrierRequest, ::tensorflow::CancelBarrierResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::CancelBarrier), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::GetAliveTasksRequest, ::tensorflow::GetAliveTasksResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::GetAliveTasks), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      grpcCoordinationService_method_names[16],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< grpc::CoordinationService::Service, ::tensorflow::PollForErrorRequest, ::tensorflow::PollForErrorResponse>(
          std::mem_fn(&grpc::CoordinationService::Service::PollForError), this)));
}

grpc::CoordinationService::Service::~Service() {
}

::grpc::Status grpc::CoordinationService::Service::RegisterTask(::grpc::ServerContext* context, const ::tensorflow::RegisterTaskRequest* request, ::tensorflow::RegisterTaskResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::Heartbeat(::grpc::ServerContext* context, const ::tensorflow::HeartbeatRequest* request, ::tensorflow::HeartbeatResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::WaitForAllTasks(::grpc::ServerContext* context, const ::tensorflow::WaitForAllTasksRequest* request, ::tensorflow::WaitForAllTasksResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::ShutdownTask(::grpc::ServerContext* context, const ::tensorflow::ShutdownTaskRequest* request, ::tensorflow::ShutdownTaskResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::ResetTask(::grpc::ServerContext* context, const ::tensorflow::ResetTaskRequest* request, ::tensorflow::ResetTaskResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::ReportErrorToTask(::grpc::ServerContext* context, const ::tensorflow::ReportErrorToTaskRequest* request, ::tensorflow::ReportErrorToTaskResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::ReportErrorToService(::grpc::ServerContext* context, const ::tensorflow::ReportErrorToServiceRequest* request, ::tensorflow::ReportErrorToServiceResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::GetTaskState(::grpc::ServerContext* context, const ::tensorflow::GetTaskStateRequest* request, ::tensorflow::GetTaskStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::InsertKeyValue(::grpc::ServerContext* context, const ::tensorflow::InsertKeyValueRequest* request, ::tensorflow::InsertKeyValueResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::GetKeyValue(::grpc::ServerContext* context, const ::tensorflow::GetKeyValueRequest* request, ::tensorflow::GetKeyValueResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::TryGetKeyValue(::grpc::ServerContext* context, const ::tensorflow::TryGetKeyValueRequest* request, ::tensorflow::TryGetKeyValueResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::GetKeyValueDir(::grpc::ServerContext* context, const ::tensorflow::GetKeyValueDirRequest* request, ::tensorflow::GetKeyValueDirResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::DeleteKeyValue(::grpc::ServerContext* context, const ::tensorflow::DeleteKeyValueRequest* request, ::tensorflow::DeleteKeyValueResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::Barrier(::grpc::ServerContext* context, const ::tensorflow::BarrierRequest* request, ::tensorflow::BarrierResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::CancelBarrier(::grpc::ServerContext* context, const ::tensorflow::CancelBarrierRequest* request, ::tensorflow::CancelBarrierResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::GetAliveTasks(::grpc::ServerContext* context, const ::tensorflow::GetAliveTasksRequest* request, ::tensorflow::GetAliveTasksResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status grpc::CoordinationService::Service::PollForError(::grpc::ServerContext* context, const ::tensorflow::PollForErrorRequest* request, ::tensorflow::PollForErrorResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace tensorflow

