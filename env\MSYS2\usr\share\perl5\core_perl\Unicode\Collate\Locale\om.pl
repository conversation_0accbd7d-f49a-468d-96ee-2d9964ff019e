+{
   locale_version => 1.31,
   entry => <<'ENTRY', # for DUCET v13.0.0
0063 0068 ; [.2287.0020.0002] # <LATIN SMALL LETTER C, LATIN SMALL LETTER H>
0043 0068 ; [.2287.0020.0007] # <LATIN CAPITAL LETTER C, LATIN SMALL LETTER H>
0043 0048 ; [.2287.0020.0008] # <LATIN CAPITAL LETTER C, LATIN CAPITAL LETTER H>
0064 0068 ; [.2288.0020.0002] # <LATIN SMALL LETTER D, LATIN SMALL LETTER H>
0044 0068 ; [.2288.0020.0007] # <LATIN CAPITAL LETTER D, LATIN SMALL LETTER H>
0044 0048 ; [.2288.0020.0008] # <LATIN CAPITAL LETTER D, LATIN CAPITAL LETTER H>
006B 0068 ; [.2289.0020.0002] # <LATIN SMALL LETTER K, LATIN SMALL LETTER H>
004B 0068 ; [.2289.0020.0007] # <LATIN CAPITAL LETTER K, LATIN SMALL LETTER H>
004B 0048 ; [.2289.0020.0008] # <LATIN CAPITAL LETTER K, LATIN CAPITAL LETTER H>
006E 0079 ; [.228A.0020.0002] # <LATIN SMALL LETTER N, LATIN SMALL LETTER Y>
004E 0079 ; [.228A.0020.0007] # <LATIN CAPITAL LETTER N, LATIN SMALL LETTER Y>
004E 0059 ; [.228A.0020.0008] # <LATIN CAPITAL LETTER N, LATIN CAPITAL LETTER Y>
0070 0068 ; [.228B.0020.0002] # <LATIN SMALL LETTER P, LATIN SMALL LETTER H>
0050 0068 ; [.228B.0020.0007] # <LATIN CAPITAL LETTER P, LATIN SMALL LETTER H>
0050 0048 ; [.228B.0020.0008] # <LATIN CAPITAL LETTER P, LATIN CAPITAL LETTER H>
0073 0068 ; [.228C.0020.0002] # <LATIN SMALL LETTER S, LATIN SMALL LETTER H>
0053 0068 ; [.228C.0020.0007] # <LATIN CAPITAL LETTER S, LATIN SMALL LETTER H>
ENTRY
};
