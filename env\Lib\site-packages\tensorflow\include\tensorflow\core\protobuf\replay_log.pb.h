// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/replay_log.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/master.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto;
namespace tensorflow {
class NewReplaySession;
struct NewReplaySessionDefaultTypeInternal;
extern NewReplaySessionDefaultTypeInternal _NewReplaySession_default_instance_;
class ReplayOp;
struct ReplayOpDefaultTypeInternal;
extern ReplayOpDefaultTypeInternal _ReplayOp_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::NewReplaySession* Arena::CreateMaybeMessage<::tensorflow::NewReplaySession>(Arena*);
template<> ::tensorflow::ReplayOp* Arena::CreateMaybeMessage<::tensorflow::ReplayOp>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class NewReplaySession final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NewReplaySession) */ {
 public:
  inline NewReplaySession() : NewReplaySession(nullptr) {}
  ~NewReplaySession() override;
  explicit PROTOBUF_CONSTEXPR NewReplaySession(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NewReplaySession(const NewReplaySession& from);
  NewReplaySession(NewReplaySession&& from) noexcept
    : NewReplaySession() {
    *this = ::std::move(from);
  }

  inline NewReplaySession& operator=(const NewReplaySession& from) {
    CopyFrom(from);
    return *this;
  }
  inline NewReplaySession& operator=(NewReplaySession&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NewReplaySession& default_instance() {
    return *internal_default_instance();
  }
  static inline const NewReplaySession* internal_default_instance() {
    return reinterpret_cast<const NewReplaySession*>(
               &_NewReplaySession_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(NewReplaySession& a, NewReplaySession& b) {
    a.Swap(&b);
  }
  inline void Swap(NewReplaySession* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NewReplaySession* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NewReplaySession* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NewReplaySession>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NewReplaySession& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NewReplaySession& from) {
    NewReplaySession::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NewReplaySession* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NewReplaySession";
  }
  protected:
  explicit NewReplaySession(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 2,
    kDevicesFieldNumber = 1,
  };
  // string session_handle = 2;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // .tensorflow.ListDevicesResponse devices = 1;
  bool has_devices() const;
  private:
  bool _internal_has_devices() const;
  public:
  void clear_devices();
  const ::tensorflow::ListDevicesResponse& devices() const;
  PROTOBUF_NODISCARD ::tensorflow::ListDevicesResponse* release_devices();
  ::tensorflow::ListDevicesResponse* mutable_devices();
  void set_allocated_devices(::tensorflow::ListDevicesResponse* devices);
  private:
  const ::tensorflow::ListDevicesResponse& _internal_devices() const;
  ::tensorflow::ListDevicesResponse* _internal_mutable_devices();
  public:
  void unsafe_arena_set_allocated_devices(
      ::tensorflow::ListDevicesResponse* devices);
  ::tensorflow::ListDevicesResponse* unsafe_arena_release_devices();

  // @@protoc_insertion_point(class_scope:tensorflow.NewReplaySession)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    ::tensorflow::ListDevicesResponse* devices_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto;
};
// -------------------------------------------------------------------

class ReplayOp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ReplayOp) */ {
 public:
  inline ReplayOp() : ReplayOp(nullptr) {}
  ~ReplayOp() override;
  explicit PROTOBUF_CONSTEXPR ReplayOp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReplayOp(const ReplayOp& from);
  ReplayOp(ReplayOp&& from) noexcept
    : ReplayOp() {
    *this = ::std::move(from);
  }

  inline ReplayOp& operator=(const ReplayOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReplayOp& operator=(ReplayOp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReplayOp& default_instance() {
    return *internal_default_instance();
  }
  enum OpCase {
    kCreateSession = 1,
    kExtendSession = 2,
    kPartialRunSetup = 3,
    kRunStep = 4,
    kCloseSession = 5,
    kListDevices = 6,
    kResetRequest = 7,
    kMakeCallable = 8,
    kRunCallable = 9,
    kReleaseCallable = 10,
    kNewReplaySession = 11,
    OP_NOT_SET = 0,
  };

  enum ResponseCase {
    kCreateSessionResponse = 21,
    kExtendSessionResponse = 22,
    kPartialRunSetupResponse = 23,
    kRunStepResponse = 24,
    kCloseSessionResponse = 25,
    kListDevicesResponse = 26,
    kResetRequestResponse = 27,
    kMakeCallableResponse = 28,
    kRunCallableResponse = 29,
    kReleaseCallableResponse = 30,
    RESPONSE_NOT_SET = 0,
  };

  static inline const ReplayOp* internal_default_instance() {
    return reinterpret_cast<const ReplayOp*>(
               &_ReplayOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ReplayOp& a, ReplayOp& b) {
    a.Swap(&b);
  }
  inline void Swap(ReplayOp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReplayOp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReplayOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReplayOp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ReplayOp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ReplayOp& from) {
    ReplayOp::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReplayOp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ReplayOp";
  }
  protected:
  explicit ReplayOp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStartTimeUsFieldNumber = 31,
    kEndTimeUsFieldNumber = 32,
    kCreateSessionFieldNumber = 1,
    kExtendSessionFieldNumber = 2,
    kPartialRunSetupFieldNumber = 3,
    kRunStepFieldNumber = 4,
    kCloseSessionFieldNumber = 5,
    kListDevicesFieldNumber = 6,
    kResetRequestFieldNumber = 7,
    kMakeCallableFieldNumber = 8,
    kRunCallableFieldNumber = 9,
    kReleaseCallableFieldNumber = 10,
    kNewReplaySessionFieldNumber = 11,
    kCreateSessionResponseFieldNumber = 21,
    kExtendSessionResponseFieldNumber = 22,
    kPartialRunSetupResponseFieldNumber = 23,
    kRunStepResponseFieldNumber = 24,
    kCloseSessionResponseFieldNumber = 25,
    kListDevicesResponseFieldNumber = 26,
    kResetRequestResponseFieldNumber = 27,
    kMakeCallableResponseFieldNumber = 28,
    kRunCallableResponseFieldNumber = 29,
    kReleaseCallableResponseFieldNumber = 30,
  };
  // double start_time_us = 31;
  void clear_start_time_us();
  double start_time_us() const;
  void set_start_time_us(double value);
  private:
  double _internal_start_time_us() const;
  void _internal_set_start_time_us(double value);
  public:

  // double end_time_us = 32;
  void clear_end_time_us();
  double end_time_us() const;
  void set_end_time_us(double value);
  private:
  double _internal_end_time_us() const;
  void _internal_set_end_time_us(double value);
  public:

  // .tensorflow.CreateSessionRequest create_session = 1;
  bool has_create_session() const;
  private:
  bool _internal_has_create_session() const;
  public:
  void clear_create_session();
  const ::tensorflow::CreateSessionRequest& create_session() const;
  PROTOBUF_NODISCARD ::tensorflow::CreateSessionRequest* release_create_session();
  ::tensorflow::CreateSessionRequest* mutable_create_session();
  void set_allocated_create_session(::tensorflow::CreateSessionRequest* create_session);
  private:
  const ::tensorflow::CreateSessionRequest& _internal_create_session() const;
  ::tensorflow::CreateSessionRequest* _internal_mutable_create_session();
  public:
  void unsafe_arena_set_allocated_create_session(
      ::tensorflow::CreateSessionRequest* create_session);
  ::tensorflow::CreateSessionRequest* unsafe_arena_release_create_session();

  // .tensorflow.ExtendSessionRequest extend_session = 2;
  bool has_extend_session() const;
  private:
  bool _internal_has_extend_session() const;
  public:
  void clear_extend_session();
  const ::tensorflow::ExtendSessionRequest& extend_session() const;
  PROTOBUF_NODISCARD ::tensorflow::ExtendSessionRequest* release_extend_session();
  ::tensorflow::ExtendSessionRequest* mutable_extend_session();
  void set_allocated_extend_session(::tensorflow::ExtendSessionRequest* extend_session);
  private:
  const ::tensorflow::ExtendSessionRequest& _internal_extend_session() const;
  ::tensorflow::ExtendSessionRequest* _internal_mutable_extend_session();
  public:
  void unsafe_arena_set_allocated_extend_session(
      ::tensorflow::ExtendSessionRequest* extend_session);
  ::tensorflow::ExtendSessionRequest* unsafe_arena_release_extend_session();

  // .tensorflow.PartialRunSetupRequest partial_run_setup = 3;
  bool has_partial_run_setup() const;
  private:
  bool _internal_has_partial_run_setup() const;
  public:
  void clear_partial_run_setup();
  const ::tensorflow::PartialRunSetupRequest& partial_run_setup() const;
  PROTOBUF_NODISCARD ::tensorflow::PartialRunSetupRequest* release_partial_run_setup();
  ::tensorflow::PartialRunSetupRequest* mutable_partial_run_setup();
  void set_allocated_partial_run_setup(::tensorflow::PartialRunSetupRequest* partial_run_setup);
  private:
  const ::tensorflow::PartialRunSetupRequest& _internal_partial_run_setup() const;
  ::tensorflow::PartialRunSetupRequest* _internal_mutable_partial_run_setup();
  public:
  void unsafe_arena_set_allocated_partial_run_setup(
      ::tensorflow::PartialRunSetupRequest* partial_run_setup);
  ::tensorflow::PartialRunSetupRequest* unsafe_arena_release_partial_run_setup();

  // .tensorflow.RunStepRequest run_step = 4;
  bool has_run_step() const;
  private:
  bool _internal_has_run_step() const;
  public:
  void clear_run_step();
  const ::tensorflow::RunStepRequest& run_step() const;
  PROTOBUF_NODISCARD ::tensorflow::RunStepRequest* release_run_step();
  ::tensorflow::RunStepRequest* mutable_run_step();
  void set_allocated_run_step(::tensorflow::RunStepRequest* run_step);
  private:
  const ::tensorflow::RunStepRequest& _internal_run_step() const;
  ::tensorflow::RunStepRequest* _internal_mutable_run_step();
  public:
  void unsafe_arena_set_allocated_run_step(
      ::tensorflow::RunStepRequest* run_step);
  ::tensorflow::RunStepRequest* unsafe_arena_release_run_step();

  // .tensorflow.CloseSessionRequest close_session = 5;
  bool has_close_session() const;
  private:
  bool _internal_has_close_session() const;
  public:
  void clear_close_session();
  const ::tensorflow::CloseSessionRequest& close_session() const;
  PROTOBUF_NODISCARD ::tensorflow::CloseSessionRequest* release_close_session();
  ::tensorflow::CloseSessionRequest* mutable_close_session();
  void set_allocated_close_session(::tensorflow::CloseSessionRequest* close_session);
  private:
  const ::tensorflow::CloseSessionRequest& _internal_close_session() const;
  ::tensorflow::CloseSessionRequest* _internal_mutable_close_session();
  public:
  void unsafe_arena_set_allocated_close_session(
      ::tensorflow::CloseSessionRequest* close_session);
  ::tensorflow::CloseSessionRequest* unsafe_arena_release_close_session();

  // .tensorflow.ListDevicesRequest list_devices = 6;
  bool has_list_devices() const;
  private:
  bool _internal_has_list_devices() const;
  public:
  void clear_list_devices();
  const ::tensorflow::ListDevicesRequest& list_devices() const;
  PROTOBUF_NODISCARD ::tensorflow::ListDevicesRequest* release_list_devices();
  ::tensorflow::ListDevicesRequest* mutable_list_devices();
  void set_allocated_list_devices(::tensorflow::ListDevicesRequest* list_devices);
  private:
  const ::tensorflow::ListDevicesRequest& _internal_list_devices() const;
  ::tensorflow::ListDevicesRequest* _internal_mutable_list_devices();
  public:
  void unsafe_arena_set_allocated_list_devices(
      ::tensorflow::ListDevicesRequest* list_devices);
  ::tensorflow::ListDevicesRequest* unsafe_arena_release_list_devices();

  // .tensorflow.ResetRequest reset_request = 7;
  bool has_reset_request() const;
  private:
  bool _internal_has_reset_request() const;
  public:
  void clear_reset_request();
  const ::tensorflow::ResetRequest& reset_request() const;
  PROTOBUF_NODISCARD ::tensorflow::ResetRequest* release_reset_request();
  ::tensorflow::ResetRequest* mutable_reset_request();
  void set_allocated_reset_request(::tensorflow::ResetRequest* reset_request);
  private:
  const ::tensorflow::ResetRequest& _internal_reset_request() const;
  ::tensorflow::ResetRequest* _internal_mutable_reset_request();
  public:
  void unsafe_arena_set_allocated_reset_request(
      ::tensorflow::ResetRequest* reset_request);
  ::tensorflow::ResetRequest* unsafe_arena_release_reset_request();

  // .tensorflow.MakeCallableRequest make_callable = 8;
  bool has_make_callable() const;
  private:
  bool _internal_has_make_callable() const;
  public:
  void clear_make_callable();
  const ::tensorflow::MakeCallableRequest& make_callable() const;
  PROTOBUF_NODISCARD ::tensorflow::MakeCallableRequest* release_make_callable();
  ::tensorflow::MakeCallableRequest* mutable_make_callable();
  void set_allocated_make_callable(::tensorflow::MakeCallableRequest* make_callable);
  private:
  const ::tensorflow::MakeCallableRequest& _internal_make_callable() const;
  ::tensorflow::MakeCallableRequest* _internal_mutable_make_callable();
  public:
  void unsafe_arena_set_allocated_make_callable(
      ::tensorflow::MakeCallableRequest* make_callable);
  ::tensorflow::MakeCallableRequest* unsafe_arena_release_make_callable();

  // .tensorflow.RunCallableRequest run_callable = 9;
  bool has_run_callable() const;
  private:
  bool _internal_has_run_callable() const;
  public:
  void clear_run_callable();
  const ::tensorflow::RunCallableRequest& run_callable() const;
  PROTOBUF_NODISCARD ::tensorflow::RunCallableRequest* release_run_callable();
  ::tensorflow::RunCallableRequest* mutable_run_callable();
  void set_allocated_run_callable(::tensorflow::RunCallableRequest* run_callable);
  private:
  const ::tensorflow::RunCallableRequest& _internal_run_callable() const;
  ::tensorflow::RunCallableRequest* _internal_mutable_run_callable();
  public:
  void unsafe_arena_set_allocated_run_callable(
      ::tensorflow::RunCallableRequest* run_callable);
  ::tensorflow::RunCallableRequest* unsafe_arena_release_run_callable();

  // .tensorflow.ReleaseCallableRequest release_callable = 10;
  bool has_release_callable() const;
  private:
  bool _internal_has_release_callable() const;
  public:
  void clear_release_callable();
  const ::tensorflow::ReleaseCallableRequest& release_callable() const;
  PROTOBUF_NODISCARD ::tensorflow::ReleaseCallableRequest* release_release_callable();
  ::tensorflow::ReleaseCallableRequest* mutable_release_callable();
  void set_allocated_release_callable(::tensorflow::ReleaseCallableRequest* release_callable);
  private:
  const ::tensorflow::ReleaseCallableRequest& _internal_release_callable() const;
  ::tensorflow::ReleaseCallableRequest* _internal_mutable_release_callable();
  public:
  void unsafe_arena_set_allocated_release_callable(
      ::tensorflow::ReleaseCallableRequest* release_callable);
  ::tensorflow::ReleaseCallableRequest* unsafe_arena_release_release_callable();

  // .tensorflow.NewReplaySession new_replay_session = 11;
  bool has_new_replay_session() const;
  private:
  bool _internal_has_new_replay_session() const;
  public:
  void clear_new_replay_session();
  const ::tensorflow::NewReplaySession& new_replay_session() const;
  PROTOBUF_NODISCARD ::tensorflow::NewReplaySession* release_new_replay_session();
  ::tensorflow::NewReplaySession* mutable_new_replay_session();
  void set_allocated_new_replay_session(::tensorflow::NewReplaySession* new_replay_session);
  private:
  const ::tensorflow::NewReplaySession& _internal_new_replay_session() const;
  ::tensorflow::NewReplaySession* _internal_mutable_new_replay_session();
  public:
  void unsafe_arena_set_allocated_new_replay_session(
      ::tensorflow::NewReplaySession* new_replay_session);
  ::tensorflow::NewReplaySession* unsafe_arena_release_new_replay_session();

  // .tensorflow.CreateSessionResponse create_session_response = 21;
  bool has_create_session_response() const;
  private:
  bool _internal_has_create_session_response() const;
  public:
  void clear_create_session_response();
  const ::tensorflow::CreateSessionResponse& create_session_response() const;
  PROTOBUF_NODISCARD ::tensorflow::CreateSessionResponse* release_create_session_response();
  ::tensorflow::CreateSessionResponse* mutable_create_session_response();
  void set_allocated_create_session_response(::tensorflow::CreateSessionResponse* create_session_response);
  private:
  const ::tensorflow::CreateSessionResponse& _internal_create_session_response() const;
  ::tensorflow::CreateSessionResponse* _internal_mutable_create_session_response();
  public:
  void unsafe_arena_set_allocated_create_session_response(
      ::tensorflow::CreateSessionResponse* create_session_response);
  ::tensorflow::CreateSessionResponse* unsafe_arena_release_create_session_response();

  // .tensorflow.ExtendSessionResponse extend_session_response = 22;
  bool has_extend_session_response() const;
  private:
  bool _internal_has_extend_session_response() const;
  public:
  void clear_extend_session_response();
  const ::tensorflow::ExtendSessionResponse& extend_session_response() const;
  PROTOBUF_NODISCARD ::tensorflow::ExtendSessionResponse* release_extend_session_response();
  ::tensorflow::ExtendSessionResponse* mutable_extend_session_response();
  void set_allocated_extend_session_response(::tensorflow::ExtendSessionResponse* extend_session_response);
  private:
  const ::tensorflow::ExtendSessionResponse& _internal_extend_session_response() const;
  ::tensorflow::ExtendSessionResponse* _internal_mutable_extend_session_response();
  public:
  void unsafe_arena_set_allocated_extend_session_response(
      ::tensorflow::ExtendSessionResponse* extend_session_response);
  ::tensorflow::ExtendSessionResponse* unsafe_arena_release_extend_session_response();

  // .tensorflow.PartialRunSetupResponse partial_run_setup_response = 23;
  bool has_partial_run_setup_response() const;
  private:
  bool _internal_has_partial_run_setup_response() const;
  public:
  void clear_partial_run_setup_response();
  const ::tensorflow::PartialRunSetupResponse& partial_run_setup_response() const;
  PROTOBUF_NODISCARD ::tensorflow::PartialRunSetupResponse* release_partial_run_setup_response();
  ::tensorflow::PartialRunSetupResponse* mutable_partial_run_setup_response();
  void set_allocated_partial_run_setup_response(::tensorflow::PartialRunSetupResponse* partial_run_setup_response);
  private:
  const ::tensorflow::PartialRunSetupResponse& _internal_partial_run_setup_response() const;
  ::tensorflow::PartialRunSetupResponse* _internal_mutable_partial_run_setup_response();
  public:
  void unsafe_arena_set_allocated_partial_run_setup_response(
      ::tensorflow::PartialRunSetupResponse* partial_run_setup_response);
  ::tensorflow::PartialRunSetupResponse* unsafe_arena_release_partial_run_setup_response();

  // .tensorflow.RunStepResponse run_step_response = 24;
  bool has_run_step_response() const;
  private:
  bool _internal_has_run_step_response() const;
  public:
  void clear_run_step_response();
  const ::tensorflow::RunStepResponse& run_step_response() const;
  PROTOBUF_NODISCARD ::tensorflow::RunStepResponse* release_run_step_response();
  ::tensorflow::RunStepResponse* mutable_run_step_response();
  void set_allocated_run_step_response(::tensorflow::RunStepResponse* run_step_response);
  private:
  const ::tensorflow::RunStepResponse& _internal_run_step_response() const;
  ::tensorflow::RunStepResponse* _internal_mutable_run_step_response();
  public:
  void unsafe_arena_set_allocated_run_step_response(
      ::tensorflow::RunStepResponse* run_step_response);
  ::tensorflow::RunStepResponse* unsafe_arena_release_run_step_response();

  // .tensorflow.CloseSessionResponse close_session_response = 25;
  bool has_close_session_response() const;
  private:
  bool _internal_has_close_session_response() const;
  public:
  void clear_close_session_response();
  const ::tensorflow::CloseSessionResponse& close_session_response() const;
  PROTOBUF_NODISCARD ::tensorflow::CloseSessionResponse* release_close_session_response();
  ::tensorflow::CloseSessionResponse* mutable_close_session_response();
  void set_allocated_close_session_response(::tensorflow::CloseSessionResponse* close_session_response);
  private:
  const ::tensorflow::CloseSessionResponse& _internal_close_session_response() const;
  ::tensorflow::CloseSessionResponse* _internal_mutable_close_session_response();
  public:
  void unsafe_arena_set_allocated_close_session_response(
      ::tensorflow::CloseSessionResponse* close_session_response);
  ::tensorflow::CloseSessionResponse* unsafe_arena_release_close_session_response();

  // .tensorflow.ListDevicesResponse list_devices_response = 26;
  bool has_list_devices_response() const;
  private:
  bool _internal_has_list_devices_response() const;
  public:
  void clear_list_devices_response();
  const ::tensorflow::ListDevicesResponse& list_devices_response() const;
  PROTOBUF_NODISCARD ::tensorflow::ListDevicesResponse* release_list_devices_response();
  ::tensorflow::ListDevicesResponse* mutable_list_devices_response();
  void set_allocated_list_devices_response(::tensorflow::ListDevicesResponse* list_devices_response);
  private:
  const ::tensorflow::ListDevicesResponse& _internal_list_devices_response() const;
  ::tensorflow::ListDevicesResponse* _internal_mutable_list_devices_response();
  public:
  void unsafe_arena_set_allocated_list_devices_response(
      ::tensorflow::ListDevicesResponse* list_devices_response);
  ::tensorflow::ListDevicesResponse* unsafe_arena_release_list_devices_response();

  // .tensorflow.ResetResponse reset_request_response = 27;
  bool has_reset_request_response() const;
  private:
  bool _internal_has_reset_request_response() const;
  public:
  void clear_reset_request_response();
  const ::tensorflow::ResetResponse& reset_request_response() const;
  PROTOBUF_NODISCARD ::tensorflow::ResetResponse* release_reset_request_response();
  ::tensorflow::ResetResponse* mutable_reset_request_response();
  void set_allocated_reset_request_response(::tensorflow::ResetResponse* reset_request_response);
  private:
  const ::tensorflow::ResetResponse& _internal_reset_request_response() const;
  ::tensorflow::ResetResponse* _internal_mutable_reset_request_response();
  public:
  void unsafe_arena_set_allocated_reset_request_response(
      ::tensorflow::ResetResponse* reset_request_response);
  ::tensorflow::ResetResponse* unsafe_arena_release_reset_request_response();

  // .tensorflow.MakeCallableResponse make_callable_response = 28;
  bool has_make_callable_response() const;
  private:
  bool _internal_has_make_callable_response() const;
  public:
  void clear_make_callable_response();
  const ::tensorflow::MakeCallableResponse& make_callable_response() const;
  PROTOBUF_NODISCARD ::tensorflow::MakeCallableResponse* release_make_callable_response();
  ::tensorflow::MakeCallableResponse* mutable_make_callable_response();
  void set_allocated_make_callable_response(::tensorflow::MakeCallableResponse* make_callable_response);
  private:
  const ::tensorflow::MakeCallableResponse& _internal_make_callable_response() const;
  ::tensorflow::MakeCallableResponse* _internal_mutable_make_callable_response();
  public:
  void unsafe_arena_set_allocated_make_callable_response(
      ::tensorflow::MakeCallableResponse* make_callable_response);
  ::tensorflow::MakeCallableResponse* unsafe_arena_release_make_callable_response();

  // .tensorflow.RunCallableResponse run_callable_response = 29;
  bool has_run_callable_response() const;
  private:
  bool _internal_has_run_callable_response() const;
  public:
  void clear_run_callable_response();
  const ::tensorflow::RunCallableResponse& run_callable_response() const;
  PROTOBUF_NODISCARD ::tensorflow::RunCallableResponse* release_run_callable_response();
  ::tensorflow::RunCallableResponse* mutable_run_callable_response();
  void set_allocated_run_callable_response(::tensorflow::RunCallableResponse* run_callable_response);
  private:
  const ::tensorflow::RunCallableResponse& _internal_run_callable_response() const;
  ::tensorflow::RunCallableResponse* _internal_mutable_run_callable_response();
  public:
  void unsafe_arena_set_allocated_run_callable_response(
      ::tensorflow::RunCallableResponse* run_callable_response);
  ::tensorflow::RunCallableResponse* unsafe_arena_release_run_callable_response();

  // .tensorflow.ReleaseCallableResponse release_callable_response = 30;
  bool has_release_callable_response() const;
  private:
  bool _internal_has_release_callable_response() const;
  public:
  void clear_release_callable_response();
  const ::tensorflow::ReleaseCallableResponse& release_callable_response() const;
  PROTOBUF_NODISCARD ::tensorflow::ReleaseCallableResponse* release_release_callable_response();
  ::tensorflow::ReleaseCallableResponse* mutable_release_callable_response();
  void set_allocated_release_callable_response(::tensorflow::ReleaseCallableResponse* release_callable_response);
  private:
  const ::tensorflow::ReleaseCallableResponse& _internal_release_callable_response() const;
  ::tensorflow::ReleaseCallableResponse* _internal_mutable_release_callable_response();
  public:
  void unsafe_arena_set_allocated_release_callable_response(
      ::tensorflow::ReleaseCallableResponse* release_callable_response);
  ::tensorflow::ReleaseCallableResponse* unsafe_arena_release_release_callable_response();

  void clear_op();
  OpCase op_case() const;
  void clear_response();
  ResponseCase response_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.ReplayOp)
 private:
  class _Internal;
  void set_has_create_session();
  void set_has_extend_session();
  void set_has_partial_run_setup();
  void set_has_run_step();
  void set_has_close_session();
  void set_has_list_devices();
  void set_has_reset_request();
  void set_has_make_callable();
  void set_has_run_callable();
  void set_has_release_callable();
  void set_has_new_replay_session();
  void set_has_create_session_response();
  void set_has_extend_session_response();
  void set_has_partial_run_setup_response();
  void set_has_run_step_response();
  void set_has_close_session_response();
  void set_has_list_devices_response();
  void set_has_reset_request_response();
  void set_has_make_callable_response();
  void set_has_run_callable_response();
  void set_has_release_callable_response();

  inline bool has_op() const;
  inline void clear_has_op();

  inline bool has_response() const;
  inline void clear_has_response();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double start_time_us_;
    double end_time_us_;
    union OpUnion {
      constexpr OpUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::CreateSessionRequest* create_session_;
      ::tensorflow::ExtendSessionRequest* extend_session_;
      ::tensorflow::PartialRunSetupRequest* partial_run_setup_;
      ::tensorflow::RunStepRequest* run_step_;
      ::tensorflow::CloseSessionRequest* close_session_;
      ::tensorflow::ListDevicesRequest* list_devices_;
      ::tensorflow::ResetRequest* reset_request_;
      ::tensorflow::MakeCallableRequest* make_callable_;
      ::tensorflow::RunCallableRequest* run_callable_;
      ::tensorflow::ReleaseCallableRequest* release_callable_;
      ::tensorflow::NewReplaySession* new_replay_session_;
    } op_;
    union ResponseUnion {
      constexpr ResponseUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::CreateSessionResponse* create_session_response_;
      ::tensorflow::ExtendSessionResponse* extend_session_response_;
      ::tensorflow::PartialRunSetupResponse* partial_run_setup_response_;
      ::tensorflow::RunStepResponse* run_step_response_;
      ::tensorflow::CloseSessionResponse* close_session_response_;
      ::tensorflow::ListDevicesResponse* list_devices_response_;
      ::tensorflow::ResetResponse* reset_request_response_;
      ::tensorflow::MakeCallableResponse* make_callable_response_;
      ::tensorflow::RunCallableResponse* run_callable_response_;
      ::tensorflow::ReleaseCallableResponse* release_callable_response_;
    } response_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[2];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// NewReplaySession

// .tensorflow.ListDevicesResponse devices = 1;
inline bool NewReplaySession::_internal_has_devices() const {
  return this != internal_default_instance() && _impl_.devices_ != nullptr;
}
inline bool NewReplaySession::has_devices() const {
  return _internal_has_devices();
}
inline const ::tensorflow::ListDevicesResponse& NewReplaySession::_internal_devices() const {
  const ::tensorflow::ListDevicesResponse* p = _impl_.devices_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ListDevicesResponse&>(
      ::tensorflow::_ListDevicesResponse_default_instance_);
}
inline const ::tensorflow::ListDevicesResponse& NewReplaySession::devices() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewReplaySession.devices)
  return _internal_devices();
}
inline void NewReplaySession::unsafe_arena_set_allocated_devices(
    ::tensorflow::ListDevicesResponse* devices) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.devices_);
  }
  _impl_.devices_ = devices;
  if (devices) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NewReplaySession.devices)
}
inline ::tensorflow::ListDevicesResponse* NewReplaySession::release_devices() {
  
  ::tensorflow::ListDevicesResponse* temp = _impl_.devices_;
  _impl_.devices_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ListDevicesResponse* NewReplaySession::unsafe_arena_release_devices() {
  // @@protoc_insertion_point(field_release:tensorflow.NewReplaySession.devices)
  
  ::tensorflow::ListDevicesResponse* temp = _impl_.devices_;
  _impl_.devices_ = nullptr;
  return temp;
}
inline ::tensorflow::ListDevicesResponse* NewReplaySession::_internal_mutable_devices() {
  
  if (_impl_.devices_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ListDevicesResponse>(GetArenaForAllocation());
    _impl_.devices_ = p;
  }
  return _impl_.devices_;
}
inline ::tensorflow::ListDevicesResponse* NewReplaySession::mutable_devices() {
  ::tensorflow::ListDevicesResponse* _msg = _internal_mutable_devices();
  // @@protoc_insertion_point(field_mutable:tensorflow.NewReplaySession.devices)
  return _msg;
}
inline void NewReplaySession::set_allocated_devices(::tensorflow::ListDevicesResponse* devices) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.devices_);
  }
  if (devices) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(devices));
    if (message_arena != submessage_arena) {
      devices = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, devices, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.devices_ = devices;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewReplaySession.devices)
}

// string session_handle = 2;
inline void NewReplaySession::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& NewReplaySession::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.NewReplaySession.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NewReplaySession::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NewReplaySession.session_handle)
}
inline std::string* NewReplaySession::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.NewReplaySession.session_handle)
  return _s;
}
inline const std::string& NewReplaySession::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void NewReplaySession::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* NewReplaySession::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* NewReplaySession::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.NewReplaySession.session_handle)
  return _impl_.session_handle_.Release();
}
inline void NewReplaySession::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NewReplaySession.session_handle)
}

// -------------------------------------------------------------------

// ReplayOp

// double start_time_us = 31;
inline void ReplayOp::clear_start_time_us() {
  _impl_.start_time_us_ = 0;
}
inline double ReplayOp::_internal_start_time_us() const {
  return _impl_.start_time_us_;
}
inline double ReplayOp::start_time_us() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.start_time_us)
  return _internal_start_time_us();
}
inline void ReplayOp::_internal_set_start_time_us(double value) {
  
  _impl_.start_time_us_ = value;
}
inline void ReplayOp::set_start_time_us(double value) {
  _internal_set_start_time_us(value);
  // @@protoc_insertion_point(field_set:tensorflow.ReplayOp.start_time_us)
}

// double end_time_us = 32;
inline void ReplayOp::clear_end_time_us() {
  _impl_.end_time_us_ = 0;
}
inline double ReplayOp::_internal_end_time_us() const {
  return _impl_.end_time_us_;
}
inline double ReplayOp::end_time_us() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.end_time_us)
  return _internal_end_time_us();
}
inline void ReplayOp::_internal_set_end_time_us(double value) {
  
  _impl_.end_time_us_ = value;
}
inline void ReplayOp::set_end_time_us(double value) {
  _internal_set_end_time_us(value);
  // @@protoc_insertion_point(field_set:tensorflow.ReplayOp.end_time_us)
}

// .tensorflow.CreateSessionRequest create_session = 1;
inline bool ReplayOp::_internal_has_create_session() const {
  return op_case() == kCreateSession;
}
inline bool ReplayOp::has_create_session() const {
  return _internal_has_create_session();
}
inline void ReplayOp::set_has_create_session() {
  _impl_._oneof_case_[0] = kCreateSession;
}
inline ::tensorflow::CreateSessionRequest* ReplayOp::release_create_session() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.create_session)
  if (_internal_has_create_session()) {
    clear_has_op();
    ::tensorflow::CreateSessionRequest* temp = _impl_.op_.create_session_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.create_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CreateSessionRequest& ReplayOp::_internal_create_session() const {
  return _internal_has_create_session()
      ? *_impl_.op_.create_session_
      : reinterpret_cast< ::tensorflow::CreateSessionRequest&>(::tensorflow::_CreateSessionRequest_default_instance_);
}
inline const ::tensorflow::CreateSessionRequest& ReplayOp::create_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.create_session)
  return _internal_create_session();
}
inline ::tensorflow::CreateSessionRequest* ReplayOp::unsafe_arena_release_create_session() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.create_session)
  if (_internal_has_create_session()) {
    clear_has_op();
    ::tensorflow::CreateSessionRequest* temp = _impl_.op_.create_session_;
    _impl_.op_.create_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_create_session(::tensorflow::CreateSessionRequest* create_session) {
  clear_op();
  if (create_session) {
    set_has_create_session();
    _impl_.op_.create_session_ = create_session;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.create_session)
}
inline ::tensorflow::CreateSessionRequest* ReplayOp::_internal_mutable_create_session() {
  if (!_internal_has_create_session()) {
    clear_op();
    set_has_create_session();
    _impl_.op_.create_session_ = CreateMaybeMessage< ::tensorflow::CreateSessionRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.create_session_;
}
inline ::tensorflow::CreateSessionRequest* ReplayOp::mutable_create_session() {
  ::tensorflow::CreateSessionRequest* _msg = _internal_mutable_create_session();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.create_session)
  return _msg;
}

// .tensorflow.ExtendSessionRequest extend_session = 2;
inline bool ReplayOp::_internal_has_extend_session() const {
  return op_case() == kExtendSession;
}
inline bool ReplayOp::has_extend_session() const {
  return _internal_has_extend_session();
}
inline void ReplayOp::set_has_extend_session() {
  _impl_._oneof_case_[0] = kExtendSession;
}
inline ::tensorflow::ExtendSessionRequest* ReplayOp::release_extend_session() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.extend_session)
  if (_internal_has_extend_session()) {
    clear_has_op();
    ::tensorflow::ExtendSessionRequest* temp = _impl_.op_.extend_session_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.extend_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ExtendSessionRequest& ReplayOp::_internal_extend_session() const {
  return _internal_has_extend_session()
      ? *_impl_.op_.extend_session_
      : reinterpret_cast< ::tensorflow::ExtendSessionRequest&>(::tensorflow::_ExtendSessionRequest_default_instance_);
}
inline const ::tensorflow::ExtendSessionRequest& ReplayOp::extend_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.extend_session)
  return _internal_extend_session();
}
inline ::tensorflow::ExtendSessionRequest* ReplayOp::unsafe_arena_release_extend_session() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.extend_session)
  if (_internal_has_extend_session()) {
    clear_has_op();
    ::tensorflow::ExtendSessionRequest* temp = _impl_.op_.extend_session_;
    _impl_.op_.extend_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_extend_session(::tensorflow::ExtendSessionRequest* extend_session) {
  clear_op();
  if (extend_session) {
    set_has_extend_session();
    _impl_.op_.extend_session_ = extend_session;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.extend_session)
}
inline ::tensorflow::ExtendSessionRequest* ReplayOp::_internal_mutable_extend_session() {
  if (!_internal_has_extend_session()) {
    clear_op();
    set_has_extend_session();
    _impl_.op_.extend_session_ = CreateMaybeMessage< ::tensorflow::ExtendSessionRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.extend_session_;
}
inline ::tensorflow::ExtendSessionRequest* ReplayOp::mutable_extend_session() {
  ::tensorflow::ExtendSessionRequest* _msg = _internal_mutable_extend_session();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.extend_session)
  return _msg;
}

// .tensorflow.PartialRunSetupRequest partial_run_setup = 3;
inline bool ReplayOp::_internal_has_partial_run_setup() const {
  return op_case() == kPartialRunSetup;
}
inline bool ReplayOp::has_partial_run_setup() const {
  return _internal_has_partial_run_setup();
}
inline void ReplayOp::set_has_partial_run_setup() {
  _impl_._oneof_case_[0] = kPartialRunSetup;
}
inline ::tensorflow::PartialRunSetupRequest* ReplayOp::release_partial_run_setup() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.partial_run_setup)
  if (_internal_has_partial_run_setup()) {
    clear_has_op();
    ::tensorflow::PartialRunSetupRequest* temp = _impl_.op_.partial_run_setup_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.partial_run_setup_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::PartialRunSetupRequest& ReplayOp::_internal_partial_run_setup() const {
  return _internal_has_partial_run_setup()
      ? *_impl_.op_.partial_run_setup_
      : reinterpret_cast< ::tensorflow::PartialRunSetupRequest&>(::tensorflow::_PartialRunSetupRequest_default_instance_);
}
inline const ::tensorflow::PartialRunSetupRequest& ReplayOp::partial_run_setup() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.partial_run_setup)
  return _internal_partial_run_setup();
}
inline ::tensorflow::PartialRunSetupRequest* ReplayOp::unsafe_arena_release_partial_run_setup() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.partial_run_setup)
  if (_internal_has_partial_run_setup()) {
    clear_has_op();
    ::tensorflow::PartialRunSetupRequest* temp = _impl_.op_.partial_run_setup_;
    _impl_.op_.partial_run_setup_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_partial_run_setup(::tensorflow::PartialRunSetupRequest* partial_run_setup) {
  clear_op();
  if (partial_run_setup) {
    set_has_partial_run_setup();
    _impl_.op_.partial_run_setup_ = partial_run_setup;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.partial_run_setup)
}
inline ::tensorflow::PartialRunSetupRequest* ReplayOp::_internal_mutable_partial_run_setup() {
  if (!_internal_has_partial_run_setup()) {
    clear_op();
    set_has_partial_run_setup();
    _impl_.op_.partial_run_setup_ = CreateMaybeMessage< ::tensorflow::PartialRunSetupRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.partial_run_setup_;
}
inline ::tensorflow::PartialRunSetupRequest* ReplayOp::mutable_partial_run_setup() {
  ::tensorflow::PartialRunSetupRequest* _msg = _internal_mutable_partial_run_setup();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.partial_run_setup)
  return _msg;
}

// .tensorflow.RunStepRequest run_step = 4;
inline bool ReplayOp::_internal_has_run_step() const {
  return op_case() == kRunStep;
}
inline bool ReplayOp::has_run_step() const {
  return _internal_has_run_step();
}
inline void ReplayOp::set_has_run_step() {
  _impl_._oneof_case_[0] = kRunStep;
}
inline ::tensorflow::RunStepRequest* ReplayOp::release_run_step() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.run_step)
  if (_internal_has_run_step()) {
    clear_has_op();
    ::tensorflow::RunStepRequest* temp = _impl_.op_.run_step_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.run_step_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::RunStepRequest& ReplayOp::_internal_run_step() const {
  return _internal_has_run_step()
      ? *_impl_.op_.run_step_
      : reinterpret_cast< ::tensorflow::RunStepRequest&>(::tensorflow::_RunStepRequest_default_instance_);
}
inline const ::tensorflow::RunStepRequest& ReplayOp::run_step() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.run_step)
  return _internal_run_step();
}
inline ::tensorflow::RunStepRequest* ReplayOp::unsafe_arena_release_run_step() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.run_step)
  if (_internal_has_run_step()) {
    clear_has_op();
    ::tensorflow::RunStepRequest* temp = _impl_.op_.run_step_;
    _impl_.op_.run_step_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_run_step(::tensorflow::RunStepRequest* run_step) {
  clear_op();
  if (run_step) {
    set_has_run_step();
    _impl_.op_.run_step_ = run_step;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.run_step)
}
inline ::tensorflow::RunStepRequest* ReplayOp::_internal_mutable_run_step() {
  if (!_internal_has_run_step()) {
    clear_op();
    set_has_run_step();
    _impl_.op_.run_step_ = CreateMaybeMessage< ::tensorflow::RunStepRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.run_step_;
}
inline ::tensorflow::RunStepRequest* ReplayOp::mutable_run_step() {
  ::tensorflow::RunStepRequest* _msg = _internal_mutable_run_step();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.run_step)
  return _msg;
}

// .tensorflow.CloseSessionRequest close_session = 5;
inline bool ReplayOp::_internal_has_close_session() const {
  return op_case() == kCloseSession;
}
inline bool ReplayOp::has_close_session() const {
  return _internal_has_close_session();
}
inline void ReplayOp::set_has_close_session() {
  _impl_._oneof_case_[0] = kCloseSession;
}
inline ::tensorflow::CloseSessionRequest* ReplayOp::release_close_session() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.close_session)
  if (_internal_has_close_session()) {
    clear_has_op();
    ::tensorflow::CloseSessionRequest* temp = _impl_.op_.close_session_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.close_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CloseSessionRequest& ReplayOp::_internal_close_session() const {
  return _internal_has_close_session()
      ? *_impl_.op_.close_session_
      : reinterpret_cast< ::tensorflow::CloseSessionRequest&>(::tensorflow::_CloseSessionRequest_default_instance_);
}
inline const ::tensorflow::CloseSessionRequest& ReplayOp::close_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.close_session)
  return _internal_close_session();
}
inline ::tensorflow::CloseSessionRequest* ReplayOp::unsafe_arena_release_close_session() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.close_session)
  if (_internal_has_close_session()) {
    clear_has_op();
    ::tensorflow::CloseSessionRequest* temp = _impl_.op_.close_session_;
    _impl_.op_.close_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_close_session(::tensorflow::CloseSessionRequest* close_session) {
  clear_op();
  if (close_session) {
    set_has_close_session();
    _impl_.op_.close_session_ = close_session;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.close_session)
}
inline ::tensorflow::CloseSessionRequest* ReplayOp::_internal_mutable_close_session() {
  if (!_internal_has_close_session()) {
    clear_op();
    set_has_close_session();
    _impl_.op_.close_session_ = CreateMaybeMessage< ::tensorflow::CloseSessionRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.close_session_;
}
inline ::tensorflow::CloseSessionRequest* ReplayOp::mutable_close_session() {
  ::tensorflow::CloseSessionRequest* _msg = _internal_mutable_close_session();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.close_session)
  return _msg;
}

// .tensorflow.ListDevicesRequest list_devices = 6;
inline bool ReplayOp::_internal_has_list_devices() const {
  return op_case() == kListDevices;
}
inline bool ReplayOp::has_list_devices() const {
  return _internal_has_list_devices();
}
inline void ReplayOp::set_has_list_devices() {
  _impl_._oneof_case_[0] = kListDevices;
}
inline ::tensorflow::ListDevicesRequest* ReplayOp::release_list_devices() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.list_devices)
  if (_internal_has_list_devices()) {
    clear_has_op();
    ::tensorflow::ListDevicesRequest* temp = _impl_.op_.list_devices_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.list_devices_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ListDevicesRequest& ReplayOp::_internal_list_devices() const {
  return _internal_has_list_devices()
      ? *_impl_.op_.list_devices_
      : reinterpret_cast< ::tensorflow::ListDevicesRequest&>(::tensorflow::_ListDevicesRequest_default_instance_);
}
inline const ::tensorflow::ListDevicesRequest& ReplayOp::list_devices() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.list_devices)
  return _internal_list_devices();
}
inline ::tensorflow::ListDevicesRequest* ReplayOp::unsafe_arena_release_list_devices() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.list_devices)
  if (_internal_has_list_devices()) {
    clear_has_op();
    ::tensorflow::ListDevicesRequest* temp = _impl_.op_.list_devices_;
    _impl_.op_.list_devices_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_list_devices(::tensorflow::ListDevicesRequest* list_devices) {
  clear_op();
  if (list_devices) {
    set_has_list_devices();
    _impl_.op_.list_devices_ = list_devices;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.list_devices)
}
inline ::tensorflow::ListDevicesRequest* ReplayOp::_internal_mutable_list_devices() {
  if (!_internal_has_list_devices()) {
    clear_op();
    set_has_list_devices();
    _impl_.op_.list_devices_ = CreateMaybeMessage< ::tensorflow::ListDevicesRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.list_devices_;
}
inline ::tensorflow::ListDevicesRequest* ReplayOp::mutable_list_devices() {
  ::tensorflow::ListDevicesRequest* _msg = _internal_mutable_list_devices();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.list_devices)
  return _msg;
}

// .tensorflow.ResetRequest reset_request = 7;
inline bool ReplayOp::_internal_has_reset_request() const {
  return op_case() == kResetRequest;
}
inline bool ReplayOp::has_reset_request() const {
  return _internal_has_reset_request();
}
inline void ReplayOp::set_has_reset_request() {
  _impl_._oneof_case_[0] = kResetRequest;
}
inline ::tensorflow::ResetRequest* ReplayOp::release_reset_request() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.reset_request)
  if (_internal_has_reset_request()) {
    clear_has_op();
    ::tensorflow::ResetRequest* temp = _impl_.op_.reset_request_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.reset_request_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ResetRequest& ReplayOp::_internal_reset_request() const {
  return _internal_has_reset_request()
      ? *_impl_.op_.reset_request_
      : reinterpret_cast< ::tensorflow::ResetRequest&>(::tensorflow::_ResetRequest_default_instance_);
}
inline const ::tensorflow::ResetRequest& ReplayOp::reset_request() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.reset_request)
  return _internal_reset_request();
}
inline ::tensorflow::ResetRequest* ReplayOp::unsafe_arena_release_reset_request() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.reset_request)
  if (_internal_has_reset_request()) {
    clear_has_op();
    ::tensorflow::ResetRequest* temp = _impl_.op_.reset_request_;
    _impl_.op_.reset_request_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_reset_request(::tensorflow::ResetRequest* reset_request) {
  clear_op();
  if (reset_request) {
    set_has_reset_request();
    _impl_.op_.reset_request_ = reset_request;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.reset_request)
}
inline ::tensorflow::ResetRequest* ReplayOp::_internal_mutable_reset_request() {
  if (!_internal_has_reset_request()) {
    clear_op();
    set_has_reset_request();
    _impl_.op_.reset_request_ = CreateMaybeMessage< ::tensorflow::ResetRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.reset_request_;
}
inline ::tensorflow::ResetRequest* ReplayOp::mutable_reset_request() {
  ::tensorflow::ResetRequest* _msg = _internal_mutable_reset_request();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.reset_request)
  return _msg;
}

// .tensorflow.MakeCallableRequest make_callable = 8;
inline bool ReplayOp::_internal_has_make_callable() const {
  return op_case() == kMakeCallable;
}
inline bool ReplayOp::has_make_callable() const {
  return _internal_has_make_callable();
}
inline void ReplayOp::set_has_make_callable() {
  _impl_._oneof_case_[0] = kMakeCallable;
}
inline ::tensorflow::MakeCallableRequest* ReplayOp::release_make_callable() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.make_callable)
  if (_internal_has_make_callable()) {
    clear_has_op();
    ::tensorflow::MakeCallableRequest* temp = _impl_.op_.make_callable_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.make_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::MakeCallableRequest& ReplayOp::_internal_make_callable() const {
  return _internal_has_make_callable()
      ? *_impl_.op_.make_callable_
      : reinterpret_cast< ::tensorflow::MakeCallableRequest&>(::tensorflow::_MakeCallableRequest_default_instance_);
}
inline const ::tensorflow::MakeCallableRequest& ReplayOp::make_callable() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.make_callable)
  return _internal_make_callable();
}
inline ::tensorflow::MakeCallableRequest* ReplayOp::unsafe_arena_release_make_callable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.make_callable)
  if (_internal_has_make_callable()) {
    clear_has_op();
    ::tensorflow::MakeCallableRequest* temp = _impl_.op_.make_callable_;
    _impl_.op_.make_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_make_callable(::tensorflow::MakeCallableRequest* make_callable) {
  clear_op();
  if (make_callable) {
    set_has_make_callable();
    _impl_.op_.make_callable_ = make_callable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.make_callable)
}
inline ::tensorflow::MakeCallableRequest* ReplayOp::_internal_mutable_make_callable() {
  if (!_internal_has_make_callable()) {
    clear_op();
    set_has_make_callable();
    _impl_.op_.make_callable_ = CreateMaybeMessage< ::tensorflow::MakeCallableRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.make_callable_;
}
inline ::tensorflow::MakeCallableRequest* ReplayOp::mutable_make_callable() {
  ::tensorflow::MakeCallableRequest* _msg = _internal_mutable_make_callable();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.make_callable)
  return _msg;
}

// .tensorflow.RunCallableRequest run_callable = 9;
inline bool ReplayOp::_internal_has_run_callable() const {
  return op_case() == kRunCallable;
}
inline bool ReplayOp::has_run_callable() const {
  return _internal_has_run_callable();
}
inline void ReplayOp::set_has_run_callable() {
  _impl_._oneof_case_[0] = kRunCallable;
}
inline ::tensorflow::RunCallableRequest* ReplayOp::release_run_callable() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.run_callable)
  if (_internal_has_run_callable()) {
    clear_has_op();
    ::tensorflow::RunCallableRequest* temp = _impl_.op_.run_callable_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.run_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::RunCallableRequest& ReplayOp::_internal_run_callable() const {
  return _internal_has_run_callable()
      ? *_impl_.op_.run_callable_
      : reinterpret_cast< ::tensorflow::RunCallableRequest&>(::tensorflow::_RunCallableRequest_default_instance_);
}
inline const ::tensorflow::RunCallableRequest& ReplayOp::run_callable() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.run_callable)
  return _internal_run_callable();
}
inline ::tensorflow::RunCallableRequest* ReplayOp::unsafe_arena_release_run_callable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.run_callable)
  if (_internal_has_run_callable()) {
    clear_has_op();
    ::tensorflow::RunCallableRequest* temp = _impl_.op_.run_callable_;
    _impl_.op_.run_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_run_callable(::tensorflow::RunCallableRequest* run_callable) {
  clear_op();
  if (run_callable) {
    set_has_run_callable();
    _impl_.op_.run_callable_ = run_callable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.run_callable)
}
inline ::tensorflow::RunCallableRequest* ReplayOp::_internal_mutable_run_callable() {
  if (!_internal_has_run_callable()) {
    clear_op();
    set_has_run_callable();
    _impl_.op_.run_callable_ = CreateMaybeMessage< ::tensorflow::RunCallableRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.run_callable_;
}
inline ::tensorflow::RunCallableRequest* ReplayOp::mutable_run_callable() {
  ::tensorflow::RunCallableRequest* _msg = _internal_mutable_run_callable();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.run_callable)
  return _msg;
}

// .tensorflow.ReleaseCallableRequest release_callable = 10;
inline bool ReplayOp::_internal_has_release_callable() const {
  return op_case() == kReleaseCallable;
}
inline bool ReplayOp::has_release_callable() const {
  return _internal_has_release_callable();
}
inline void ReplayOp::set_has_release_callable() {
  _impl_._oneof_case_[0] = kReleaseCallable;
}
inline ::tensorflow::ReleaseCallableRequest* ReplayOp::release_release_callable() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.release_callable)
  if (_internal_has_release_callable()) {
    clear_has_op();
    ::tensorflow::ReleaseCallableRequest* temp = _impl_.op_.release_callable_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.release_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ReleaseCallableRequest& ReplayOp::_internal_release_callable() const {
  return _internal_has_release_callable()
      ? *_impl_.op_.release_callable_
      : reinterpret_cast< ::tensorflow::ReleaseCallableRequest&>(::tensorflow::_ReleaseCallableRequest_default_instance_);
}
inline const ::tensorflow::ReleaseCallableRequest& ReplayOp::release_callable() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.release_callable)
  return _internal_release_callable();
}
inline ::tensorflow::ReleaseCallableRequest* ReplayOp::unsafe_arena_release_release_callable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.release_callable)
  if (_internal_has_release_callable()) {
    clear_has_op();
    ::tensorflow::ReleaseCallableRequest* temp = _impl_.op_.release_callable_;
    _impl_.op_.release_callable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_release_callable(::tensorflow::ReleaseCallableRequest* release_callable) {
  clear_op();
  if (release_callable) {
    set_has_release_callable();
    _impl_.op_.release_callable_ = release_callable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.release_callable)
}
inline ::tensorflow::ReleaseCallableRequest* ReplayOp::_internal_mutable_release_callable() {
  if (!_internal_has_release_callable()) {
    clear_op();
    set_has_release_callable();
    _impl_.op_.release_callable_ = CreateMaybeMessage< ::tensorflow::ReleaseCallableRequest >(GetArenaForAllocation());
  }
  return _impl_.op_.release_callable_;
}
inline ::tensorflow::ReleaseCallableRequest* ReplayOp::mutable_release_callable() {
  ::tensorflow::ReleaseCallableRequest* _msg = _internal_mutable_release_callable();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.release_callable)
  return _msg;
}

// .tensorflow.NewReplaySession new_replay_session = 11;
inline bool ReplayOp::_internal_has_new_replay_session() const {
  return op_case() == kNewReplaySession;
}
inline bool ReplayOp::has_new_replay_session() const {
  return _internal_has_new_replay_session();
}
inline void ReplayOp::set_has_new_replay_session() {
  _impl_._oneof_case_[0] = kNewReplaySession;
}
inline void ReplayOp::clear_new_replay_session() {
  if (_internal_has_new_replay_session()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.op_.new_replay_session_;
    }
    clear_has_op();
  }
}
inline ::tensorflow::NewReplaySession* ReplayOp::release_new_replay_session() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.new_replay_session)
  if (_internal_has_new_replay_session()) {
    clear_has_op();
    ::tensorflow::NewReplaySession* temp = _impl_.op_.new_replay_session_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.op_.new_replay_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NewReplaySession& ReplayOp::_internal_new_replay_session() const {
  return _internal_has_new_replay_session()
      ? *_impl_.op_.new_replay_session_
      : reinterpret_cast< ::tensorflow::NewReplaySession&>(::tensorflow::_NewReplaySession_default_instance_);
}
inline const ::tensorflow::NewReplaySession& ReplayOp::new_replay_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.new_replay_session)
  return _internal_new_replay_session();
}
inline ::tensorflow::NewReplaySession* ReplayOp::unsafe_arena_release_new_replay_session() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.new_replay_session)
  if (_internal_has_new_replay_session()) {
    clear_has_op();
    ::tensorflow::NewReplaySession* temp = _impl_.op_.new_replay_session_;
    _impl_.op_.new_replay_session_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_new_replay_session(::tensorflow::NewReplaySession* new_replay_session) {
  clear_op();
  if (new_replay_session) {
    set_has_new_replay_session();
    _impl_.op_.new_replay_session_ = new_replay_session;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.new_replay_session)
}
inline ::tensorflow::NewReplaySession* ReplayOp::_internal_mutable_new_replay_session() {
  if (!_internal_has_new_replay_session()) {
    clear_op();
    set_has_new_replay_session();
    _impl_.op_.new_replay_session_ = CreateMaybeMessage< ::tensorflow::NewReplaySession >(GetArenaForAllocation());
  }
  return _impl_.op_.new_replay_session_;
}
inline ::tensorflow::NewReplaySession* ReplayOp::mutable_new_replay_session() {
  ::tensorflow::NewReplaySession* _msg = _internal_mutable_new_replay_session();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.new_replay_session)
  return _msg;
}

// .tensorflow.CreateSessionResponse create_session_response = 21;
inline bool ReplayOp::_internal_has_create_session_response() const {
  return response_case() == kCreateSessionResponse;
}
inline bool ReplayOp::has_create_session_response() const {
  return _internal_has_create_session_response();
}
inline void ReplayOp::set_has_create_session_response() {
  _impl_._oneof_case_[1] = kCreateSessionResponse;
}
inline ::tensorflow::CreateSessionResponse* ReplayOp::release_create_session_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.create_session_response)
  if (_internal_has_create_session_response()) {
    clear_has_response();
    ::tensorflow::CreateSessionResponse* temp = _impl_.response_.create_session_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.create_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CreateSessionResponse& ReplayOp::_internal_create_session_response() const {
  return _internal_has_create_session_response()
      ? *_impl_.response_.create_session_response_
      : reinterpret_cast< ::tensorflow::CreateSessionResponse&>(::tensorflow::_CreateSessionResponse_default_instance_);
}
inline const ::tensorflow::CreateSessionResponse& ReplayOp::create_session_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.create_session_response)
  return _internal_create_session_response();
}
inline ::tensorflow::CreateSessionResponse* ReplayOp::unsafe_arena_release_create_session_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.create_session_response)
  if (_internal_has_create_session_response()) {
    clear_has_response();
    ::tensorflow::CreateSessionResponse* temp = _impl_.response_.create_session_response_;
    _impl_.response_.create_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_create_session_response(::tensorflow::CreateSessionResponse* create_session_response) {
  clear_response();
  if (create_session_response) {
    set_has_create_session_response();
    _impl_.response_.create_session_response_ = create_session_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.create_session_response)
}
inline ::tensorflow::CreateSessionResponse* ReplayOp::_internal_mutable_create_session_response() {
  if (!_internal_has_create_session_response()) {
    clear_response();
    set_has_create_session_response();
    _impl_.response_.create_session_response_ = CreateMaybeMessage< ::tensorflow::CreateSessionResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.create_session_response_;
}
inline ::tensorflow::CreateSessionResponse* ReplayOp::mutable_create_session_response() {
  ::tensorflow::CreateSessionResponse* _msg = _internal_mutable_create_session_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.create_session_response)
  return _msg;
}

// .tensorflow.ExtendSessionResponse extend_session_response = 22;
inline bool ReplayOp::_internal_has_extend_session_response() const {
  return response_case() == kExtendSessionResponse;
}
inline bool ReplayOp::has_extend_session_response() const {
  return _internal_has_extend_session_response();
}
inline void ReplayOp::set_has_extend_session_response() {
  _impl_._oneof_case_[1] = kExtendSessionResponse;
}
inline ::tensorflow::ExtendSessionResponse* ReplayOp::release_extend_session_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.extend_session_response)
  if (_internal_has_extend_session_response()) {
    clear_has_response();
    ::tensorflow::ExtendSessionResponse* temp = _impl_.response_.extend_session_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.extend_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ExtendSessionResponse& ReplayOp::_internal_extend_session_response() const {
  return _internal_has_extend_session_response()
      ? *_impl_.response_.extend_session_response_
      : reinterpret_cast< ::tensorflow::ExtendSessionResponse&>(::tensorflow::_ExtendSessionResponse_default_instance_);
}
inline const ::tensorflow::ExtendSessionResponse& ReplayOp::extend_session_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.extend_session_response)
  return _internal_extend_session_response();
}
inline ::tensorflow::ExtendSessionResponse* ReplayOp::unsafe_arena_release_extend_session_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.extend_session_response)
  if (_internal_has_extend_session_response()) {
    clear_has_response();
    ::tensorflow::ExtendSessionResponse* temp = _impl_.response_.extend_session_response_;
    _impl_.response_.extend_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_extend_session_response(::tensorflow::ExtendSessionResponse* extend_session_response) {
  clear_response();
  if (extend_session_response) {
    set_has_extend_session_response();
    _impl_.response_.extend_session_response_ = extend_session_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.extend_session_response)
}
inline ::tensorflow::ExtendSessionResponse* ReplayOp::_internal_mutable_extend_session_response() {
  if (!_internal_has_extend_session_response()) {
    clear_response();
    set_has_extend_session_response();
    _impl_.response_.extend_session_response_ = CreateMaybeMessage< ::tensorflow::ExtendSessionResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.extend_session_response_;
}
inline ::tensorflow::ExtendSessionResponse* ReplayOp::mutable_extend_session_response() {
  ::tensorflow::ExtendSessionResponse* _msg = _internal_mutable_extend_session_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.extend_session_response)
  return _msg;
}

// .tensorflow.PartialRunSetupResponse partial_run_setup_response = 23;
inline bool ReplayOp::_internal_has_partial_run_setup_response() const {
  return response_case() == kPartialRunSetupResponse;
}
inline bool ReplayOp::has_partial_run_setup_response() const {
  return _internal_has_partial_run_setup_response();
}
inline void ReplayOp::set_has_partial_run_setup_response() {
  _impl_._oneof_case_[1] = kPartialRunSetupResponse;
}
inline ::tensorflow::PartialRunSetupResponse* ReplayOp::release_partial_run_setup_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.partial_run_setup_response)
  if (_internal_has_partial_run_setup_response()) {
    clear_has_response();
    ::tensorflow::PartialRunSetupResponse* temp = _impl_.response_.partial_run_setup_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.partial_run_setup_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::PartialRunSetupResponse& ReplayOp::_internal_partial_run_setup_response() const {
  return _internal_has_partial_run_setup_response()
      ? *_impl_.response_.partial_run_setup_response_
      : reinterpret_cast< ::tensorflow::PartialRunSetupResponse&>(::tensorflow::_PartialRunSetupResponse_default_instance_);
}
inline const ::tensorflow::PartialRunSetupResponse& ReplayOp::partial_run_setup_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.partial_run_setup_response)
  return _internal_partial_run_setup_response();
}
inline ::tensorflow::PartialRunSetupResponse* ReplayOp::unsafe_arena_release_partial_run_setup_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.partial_run_setup_response)
  if (_internal_has_partial_run_setup_response()) {
    clear_has_response();
    ::tensorflow::PartialRunSetupResponse* temp = _impl_.response_.partial_run_setup_response_;
    _impl_.response_.partial_run_setup_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_partial_run_setup_response(::tensorflow::PartialRunSetupResponse* partial_run_setup_response) {
  clear_response();
  if (partial_run_setup_response) {
    set_has_partial_run_setup_response();
    _impl_.response_.partial_run_setup_response_ = partial_run_setup_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.partial_run_setup_response)
}
inline ::tensorflow::PartialRunSetupResponse* ReplayOp::_internal_mutable_partial_run_setup_response() {
  if (!_internal_has_partial_run_setup_response()) {
    clear_response();
    set_has_partial_run_setup_response();
    _impl_.response_.partial_run_setup_response_ = CreateMaybeMessage< ::tensorflow::PartialRunSetupResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.partial_run_setup_response_;
}
inline ::tensorflow::PartialRunSetupResponse* ReplayOp::mutable_partial_run_setup_response() {
  ::tensorflow::PartialRunSetupResponse* _msg = _internal_mutable_partial_run_setup_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.partial_run_setup_response)
  return _msg;
}

// .tensorflow.RunStepResponse run_step_response = 24;
inline bool ReplayOp::_internal_has_run_step_response() const {
  return response_case() == kRunStepResponse;
}
inline bool ReplayOp::has_run_step_response() const {
  return _internal_has_run_step_response();
}
inline void ReplayOp::set_has_run_step_response() {
  _impl_._oneof_case_[1] = kRunStepResponse;
}
inline ::tensorflow::RunStepResponse* ReplayOp::release_run_step_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.run_step_response)
  if (_internal_has_run_step_response()) {
    clear_has_response();
    ::tensorflow::RunStepResponse* temp = _impl_.response_.run_step_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.run_step_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::RunStepResponse& ReplayOp::_internal_run_step_response() const {
  return _internal_has_run_step_response()
      ? *_impl_.response_.run_step_response_
      : reinterpret_cast< ::tensorflow::RunStepResponse&>(::tensorflow::_RunStepResponse_default_instance_);
}
inline const ::tensorflow::RunStepResponse& ReplayOp::run_step_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.run_step_response)
  return _internal_run_step_response();
}
inline ::tensorflow::RunStepResponse* ReplayOp::unsafe_arena_release_run_step_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.run_step_response)
  if (_internal_has_run_step_response()) {
    clear_has_response();
    ::tensorflow::RunStepResponse* temp = _impl_.response_.run_step_response_;
    _impl_.response_.run_step_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_run_step_response(::tensorflow::RunStepResponse* run_step_response) {
  clear_response();
  if (run_step_response) {
    set_has_run_step_response();
    _impl_.response_.run_step_response_ = run_step_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.run_step_response)
}
inline ::tensorflow::RunStepResponse* ReplayOp::_internal_mutable_run_step_response() {
  if (!_internal_has_run_step_response()) {
    clear_response();
    set_has_run_step_response();
    _impl_.response_.run_step_response_ = CreateMaybeMessage< ::tensorflow::RunStepResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.run_step_response_;
}
inline ::tensorflow::RunStepResponse* ReplayOp::mutable_run_step_response() {
  ::tensorflow::RunStepResponse* _msg = _internal_mutable_run_step_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.run_step_response)
  return _msg;
}

// .tensorflow.CloseSessionResponse close_session_response = 25;
inline bool ReplayOp::_internal_has_close_session_response() const {
  return response_case() == kCloseSessionResponse;
}
inline bool ReplayOp::has_close_session_response() const {
  return _internal_has_close_session_response();
}
inline void ReplayOp::set_has_close_session_response() {
  _impl_._oneof_case_[1] = kCloseSessionResponse;
}
inline ::tensorflow::CloseSessionResponse* ReplayOp::release_close_session_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.close_session_response)
  if (_internal_has_close_session_response()) {
    clear_has_response();
    ::tensorflow::CloseSessionResponse* temp = _impl_.response_.close_session_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.close_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CloseSessionResponse& ReplayOp::_internal_close_session_response() const {
  return _internal_has_close_session_response()
      ? *_impl_.response_.close_session_response_
      : reinterpret_cast< ::tensorflow::CloseSessionResponse&>(::tensorflow::_CloseSessionResponse_default_instance_);
}
inline const ::tensorflow::CloseSessionResponse& ReplayOp::close_session_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.close_session_response)
  return _internal_close_session_response();
}
inline ::tensorflow::CloseSessionResponse* ReplayOp::unsafe_arena_release_close_session_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.close_session_response)
  if (_internal_has_close_session_response()) {
    clear_has_response();
    ::tensorflow::CloseSessionResponse* temp = _impl_.response_.close_session_response_;
    _impl_.response_.close_session_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_close_session_response(::tensorflow::CloseSessionResponse* close_session_response) {
  clear_response();
  if (close_session_response) {
    set_has_close_session_response();
    _impl_.response_.close_session_response_ = close_session_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.close_session_response)
}
inline ::tensorflow::CloseSessionResponse* ReplayOp::_internal_mutable_close_session_response() {
  if (!_internal_has_close_session_response()) {
    clear_response();
    set_has_close_session_response();
    _impl_.response_.close_session_response_ = CreateMaybeMessage< ::tensorflow::CloseSessionResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.close_session_response_;
}
inline ::tensorflow::CloseSessionResponse* ReplayOp::mutable_close_session_response() {
  ::tensorflow::CloseSessionResponse* _msg = _internal_mutable_close_session_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.close_session_response)
  return _msg;
}

// .tensorflow.ListDevicesResponse list_devices_response = 26;
inline bool ReplayOp::_internal_has_list_devices_response() const {
  return response_case() == kListDevicesResponse;
}
inline bool ReplayOp::has_list_devices_response() const {
  return _internal_has_list_devices_response();
}
inline void ReplayOp::set_has_list_devices_response() {
  _impl_._oneof_case_[1] = kListDevicesResponse;
}
inline ::tensorflow::ListDevicesResponse* ReplayOp::release_list_devices_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.list_devices_response)
  if (_internal_has_list_devices_response()) {
    clear_has_response();
    ::tensorflow::ListDevicesResponse* temp = _impl_.response_.list_devices_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.list_devices_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ListDevicesResponse& ReplayOp::_internal_list_devices_response() const {
  return _internal_has_list_devices_response()
      ? *_impl_.response_.list_devices_response_
      : reinterpret_cast< ::tensorflow::ListDevicesResponse&>(::tensorflow::_ListDevicesResponse_default_instance_);
}
inline const ::tensorflow::ListDevicesResponse& ReplayOp::list_devices_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.list_devices_response)
  return _internal_list_devices_response();
}
inline ::tensorflow::ListDevicesResponse* ReplayOp::unsafe_arena_release_list_devices_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.list_devices_response)
  if (_internal_has_list_devices_response()) {
    clear_has_response();
    ::tensorflow::ListDevicesResponse* temp = _impl_.response_.list_devices_response_;
    _impl_.response_.list_devices_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_list_devices_response(::tensorflow::ListDevicesResponse* list_devices_response) {
  clear_response();
  if (list_devices_response) {
    set_has_list_devices_response();
    _impl_.response_.list_devices_response_ = list_devices_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.list_devices_response)
}
inline ::tensorflow::ListDevicesResponse* ReplayOp::_internal_mutable_list_devices_response() {
  if (!_internal_has_list_devices_response()) {
    clear_response();
    set_has_list_devices_response();
    _impl_.response_.list_devices_response_ = CreateMaybeMessage< ::tensorflow::ListDevicesResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.list_devices_response_;
}
inline ::tensorflow::ListDevicesResponse* ReplayOp::mutable_list_devices_response() {
  ::tensorflow::ListDevicesResponse* _msg = _internal_mutable_list_devices_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.list_devices_response)
  return _msg;
}

// .tensorflow.ResetResponse reset_request_response = 27;
inline bool ReplayOp::_internal_has_reset_request_response() const {
  return response_case() == kResetRequestResponse;
}
inline bool ReplayOp::has_reset_request_response() const {
  return _internal_has_reset_request_response();
}
inline void ReplayOp::set_has_reset_request_response() {
  _impl_._oneof_case_[1] = kResetRequestResponse;
}
inline ::tensorflow::ResetResponse* ReplayOp::release_reset_request_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.reset_request_response)
  if (_internal_has_reset_request_response()) {
    clear_has_response();
    ::tensorflow::ResetResponse* temp = _impl_.response_.reset_request_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.reset_request_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ResetResponse& ReplayOp::_internal_reset_request_response() const {
  return _internal_has_reset_request_response()
      ? *_impl_.response_.reset_request_response_
      : reinterpret_cast< ::tensorflow::ResetResponse&>(::tensorflow::_ResetResponse_default_instance_);
}
inline const ::tensorflow::ResetResponse& ReplayOp::reset_request_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.reset_request_response)
  return _internal_reset_request_response();
}
inline ::tensorflow::ResetResponse* ReplayOp::unsafe_arena_release_reset_request_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.reset_request_response)
  if (_internal_has_reset_request_response()) {
    clear_has_response();
    ::tensorflow::ResetResponse* temp = _impl_.response_.reset_request_response_;
    _impl_.response_.reset_request_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_reset_request_response(::tensorflow::ResetResponse* reset_request_response) {
  clear_response();
  if (reset_request_response) {
    set_has_reset_request_response();
    _impl_.response_.reset_request_response_ = reset_request_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.reset_request_response)
}
inline ::tensorflow::ResetResponse* ReplayOp::_internal_mutable_reset_request_response() {
  if (!_internal_has_reset_request_response()) {
    clear_response();
    set_has_reset_request_response();
    _impl_.response_.reset_request_response_ = CreateMaybeMessage< ::tensorflow::ResetResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.reset_request_response_;
}
inline ::tensorflow::ResetResponse* ReplayOp::mutable_reset_request_response() {
  ::tensorflow::ResetResponse* _msg = _internal_mutable_reset_request_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.reset_request_response)
  return _msg;
}

// .tensorflow.MakeCallableResponse make_callable_response = 28;
inline bool ReplayOp::_internal_has_make_callable_response() const {
  return response_case() == kMakeCallableResponse;
}
inline bool ReplayOp::has_make_callable_response() const {
  return _internal_has_make_callable_response();
}
inline void ReplayOp::set_has_make_callable_response() {
  _impl_._oneof_case_[1] = kMakeCallableResponse;
}
inline ::tensorflow::MakeCallableResponse* ReplayOp::release_make_callable_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.make_callable_response)
  if (_internal_has_make_callable_response()) {
    clear_has_response();
    ::tensorflow::MakeCallableResponse* temp = _impl_.response_.make_callable_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.make_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::MakeCallableResponse& ReplayOp::_internal_make_callable_response() const {
  return _internal_has_make_callable_response()
      ? *_impl_.response_.make_callable_response_
      : reinterpret_cast< ::tensorflow::MakeCallableResponse&>(::tensorflow::_MakeCallableResponse_default_instance_);
}
inline const ::tensorflow::MakeCallableResponse& ReplayOp::make_callable_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.make_callable_response)
  return _internal_make_callable_response();
}
inline ::tensorflow::MakeCallableResponse* ReplayOp::unsafe_arena_release_make_callable_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.make_callable_response)
  if (_internal_has_make_callable_response()) {
    clear_has_response();
    ::tensorflow::MakeCallableResponse* temp = _impl_.response_.make_callable_response_;
    _impl_.response_.make_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_make_callable_response(::tensorflow::MakeCallableResponse* make_callable_response) {
  clear_response();
  if (make_callable_response) {
    set_has_make_callable_response();
    _impl_.response_.make_callable_response_ = make_callable_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.make_callable_response)
}
inline ::tensorflow::MakeCallableResponse* ReplayOp::_internal_mutable_make_callable_response() {
  if (!_internal_has_make_callable_response()) {
    clear_response();
    set_has_make_callable_response();
    _impl_.response_.make_callable_response_ = CreateMaybeMessage< ::tensorflow::MakeCallableResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.make_callable_response_;
}
inline ::tensorflow::MakeCallableResponse* ReplayOp::mutable_make_callable_response() {
  ::tensorflow::MakeCallableResponse* _msg = _internal_mutable_make_callable_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.make_callable_response)
  return _msg;
}

// .tensorflow.RunCallableResponse run_callable_response = 29;
inline bool ReplayOp::_internal_has_run_callable_response() const {
  return response_case() == kRunCallableResponse;
}
inline bool ReplayOp::has_run_callable_response() const {
  return _internal_has_run_callable_response();
}
inline void ReplayOp::set_has_run_callable_response() {
  _impl_._oneof_case_[1] = kRunCallableResponse;
}
inline ::tensorflow::RunCallableResponse* ReplayOp::release_run_callable_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.run_callable_response)
  if (_internal_has_run_callable_response()) {
    clear_has_response();
    ::tensorflow::RunCallableResponse* temp = _impl_.response_.run_callable_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.run_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::RunCallableResponse& ReplayOp::_internal_run_callable_response() const {
  return _internal_has_run_callable_response()
      ? *_impl_.response_.run_callable_response_
      : reinterpret_cast< ::tensorflow::RunCallableResponse&>(::tensorflow::_RunCallableResponse_default_instance_);
}
inline const ::tensorflow::RunCallableResponse& ReplayOp::run_callable_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.run_callable_response)
  return _internal_run_callable_response();
}
inline ::tensorflow::RunCallableResponse* ReplayOp::unsafe_arena_release_run_callable_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.run_callable_response)
  if (_internal_has_run_callable_response()) {
    clear_has_response();
    ::tensorflow::RunCallableResponse* temp = _impl_.response_.run_callable_response_;
    _impl_.response_.run_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_run_callable_response(::tensorflow::RunCallableResponse* run_callable_response) {
  clear_response();
  if (run_callable_response) {
    set_has_run_callable_response();
    _impl_.response_.run_callable_response_ = run_callable_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.run_callable_response)
}
inline ::tensorflow::RunCallableResponse* ReplayOp::_internal_mutable_run_callable_response() {
  if (!_internal_has_run_callable_response()) {
    clear_response();
    set_has_run_callable_response();
    _impl_.response_.run_callable_response_ = CreateMaybeMessage< ::tensorflow::RunCallableResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.run_callable_response_;
}
inline ::tensorflow::RunCallableResponse* ReplayOp::mutable_run_callable_response() {
  ::tensorflow::RunCallableResponse* _msg = _internal_mutable_run_callable_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.run_callable_response)
  return _msg;
}

// .tensorflow.ReleaseCallableResponse release_callable_response = 30;
inline bool ReplayOp::_internal_has_release_callable_response() const {
  return response_case() == kReleaseCallableResponse;
}
inline bool ReplayOp::has_release_callable_response() const {
  return _internal_has_release_callable_response();
}
inline void ReplayOp::set_has_release_callable_response() {
  _impl_._oneof_case_[1] = kReleaseCallableResponse;
}
inline ::tensorflow::ReleaseCallableResponse* ReplayOp::release_release_callable_response() {
  // @@protoc_insertion_point(field_release:tensorflow.ReplayOp.release_callable_response)
  if (_internal_has_release_callable_response()) {
    clear_has_response();
    ::tensorflow::ReleaseCallableResponse* temp = _impl_.response_.release_callable_response_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.response_.release_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ReleaseCallableResponse& ReplayOp::_internal_release_callable_response() const {
  return _internal_has_release_callable_response()
      ? *_impl_.response_.release_callable_response_
      : reinterpret_cast< ::tensorflow::ReleaseCallableResponse&>(::tensorflow::_ReleaseCallableResponse_default_instance_);
}
inline const ::tensorflow::ReleaseCallableResponse& ReplayOp::release_callable_response() const {
  // @@protoc_insertion_point(field_get:tensorflow.ReplayOp.release_callable_response)
  return _internal_release_callable_response();
}
inline ::tensorflow::ReleaseCallableResponse* ReplayOp::unsafe_arena_release_release_callable_response() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ReplayOp.release_callable_response)
  if (_internal_has_release_callable_response()) {
    clear_has_response();
    ::tensorflow::ReleaseCallableResponse* temp = _impl_.response_.release_callable_response_;
    _impl_.response_.release_callable_response_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ReplayOp::unsafe_arena_set_allocated_release_callable_response(::tensorflow::ReleaseCallableResponse* release_callable_response) {
  clear_response();
  if (release_callable_response) {
    set_has_release_callable_response();
    _impl_.response_.release_callable_response_ = release_callable_response;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ReplayOp.release_callable_response)
}
inline ::tensorflow::ReleaseCallableResponse* ReplayOp::_internal_mutable_release_callable_response() {
  if (!_internal_has_release_callable_response()) {
    clear_response();
    set_has_release_callable_response();
    _impl_.response_.release_callable_response_ = CreateMaybeMessage< ::tensorflow::ReleaseCallableResponse >(GetArenaForAllocation());
  }
  return _impl_.response_.release_callable_response_;
}
inline ::tensorflow::ReleaseCallableResponse* ReplayOp::mutable_release_callable_response() {
  ::tensorflow::ReleaseCallableResponse* _msg = _internal_mutable_release_callable_response();
  // @@protoc_insertion_point(field_mutable:tensorflow.ReplayOp.release_callable_response)
  return _msg;
}

inline bool ReplayOp::has_op() const {
  return op_case() != OP_NOT_SET;
}
inline void ReplayOp::clear_has_op() {
  _impl_._oneof_case_[0] = OP_NOT_SET;
}
inline bool ReplayOp::has_response() const {
  return response_case() != RESPONSE_NOT_SET;
}
inline void ReplayOp::clear_has_response() {
  _impl_._oneof_case_[1] = RESPONSE_NOT_SET;
}
inline ReplayOp::OpCase ReplayOp::op_case() const {
  return ReplayOp::OpCase(_impl_._oneof_case_[0]);
}
inline ReplayOp::ResponseCase ReplayOp::response_case() const {
  return ReplayOp::ResponseCase(_impl_._oneof_case_[1]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2freplay_5flog_2eproto
