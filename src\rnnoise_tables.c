/* The contents of this file was automatically generated by dump_rnnoise_tables.c*/

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif
#include "kiss_fft.h"

static const arch_fft_state arch_fft = {0, NULL};

static const opus_int32 fft_bitrev[960] = {
0, 192, 384, 576, 768, 64, 256, 448, 640, 832, 128, 320, 512, 704, 896,
16, 208, 400, 592, 784, 80, 272, 464, 656, 848, 144, 336, 528, 720, 912,
32, 224, 416, 608, 800, 96, 288, 480, 672, 864, 160, 352, 544, 736, 928,
48, 240, 432, 624, 816, 112, 304, 496, 688, 880, 176, 368, 560, 752, 944,
4, 196, 388, 580, 772, 68, 260, 452, 644, 836, 132, 324, 516, 708, 900,
20, 212, 404, 596, 788, 84, 276, 468, 660, 852, 148, 340, 532, 724, 916,
36, 228, 420, 612, 804, 100, 292, 484, 676, 868, 164, 356, 548, 740, 932,
52, 244, 436, 628, 820, 116, 308, 500, 692, 884, 180, 372, 564, 756, 948,
8, 200, 392, 584, 776, 72, 264, 456, 648, 840, 136, 328, 520, 712, 904,
24, 216, 408, 600, 792, 88, 280, 472, 664, 856, 152, 344, 536, 728, 920,
40, 232, 424, 616, 808, 104, 296, 488, 680, 872, 168, 360, 552, 744, 936,
56, 248, 440, 632, 824, 120, 312, 504, 696, 888, 184, 376, 568, 760, 952,
12, 204, 396, 588, 780, 76, 268, 460, 652, 844, 140, 332, 524, 716, 908,
28, 220, 412, 604, 796, 92, 284, 476, 668, 860, 156, 348, 540, 732, 924,
44, 236, 428, 620, 812, 108, 300, 492, 684, 876, 172, 364, 556, 748, 940,
60, 252, 444, 636, 828, 124, 316, 508, 700, 892, 188, 380, 572, 764, 956,
1, 193, 385, 577, 769, 65, 257, 449, 641, 833, 129, 321, 513, 705, 897,
17, 209, 401, 593, 785, 81, 273, 465, 657, 849, 145, 337, 529, 721, 913,
33, 225, 417, 609, 801, 97, 289, 481, 673, 865, 161, 353, 545, 737, 929,
49, 241, 433, 625, 817, 113, 305, 497, 689, 881, 177, 369, 561, 753, 945,
5, 197, 389, 581, 773, 69, 261, 453, 645, 837, 133, 325, 517, 709, 901,
21, 213, 405, 597, 789, 85, 277, 469, 661, 853, 149, 341, 533, 725, 917,
37, 229, 421, 613, 805, 101, 293, 485, 677, 869, 165, 357, 549, 741, 933,
53, 245, 437, 629, 821, 117, 309, 501, 693, 885, 181, 373, 565, 757, 949,
9, 201, 393, 585, 777, 73, 265, 457, 649, 841, 137, 329, 521, 713, 905,
25, 217, 409, 601, 793, 89, 281, 473, 665, 857, 153, 345, 537, 729, 921,
41, 233, 425, 617, 809, 105, 297, 489, 681, 873, 169, 361, 553, 745, 937,
57, 249, 441, 633, 825, 121, 313, 505, 697, 889, 185, 377, 569, 761, 953,
13, 205, 397, 589, 781, 77, 269, 461, 653, 845, 141, 333, 525, 717, 909,
29, 221, 413, 605, 797, 93, 285, 477, 669, 861, 157, 349, 541, 733, 925,
45, 237, 429, 621, 813, 109, 301, 493, 685, 877, 173, 365, 557, 749, 941,
61, 253, 445, 637, 829, 125, 317, 509, 701, 893, 189, 381, 573, 765, 957,
2, 194, 386, 578, 770, 66, 258, 450, 642, 834, 130, 322, 514, 706, 898,
18, 210, 402, 594, 786, 82, 274, 466, 658, 850, 146, 338, 530, 722, 914,
34, 226, 418, 610, 802, 98, 290, 482, 674, 866, 162, 354, 546, 738, 930,
50, 242, 434, 626, 818, 114, 306, 498, 690, 882, 178, 370, 562, 754, 946,
6, 198, 390, 582, 774, 70, 262, 454, 646, 838, 134, 326, 518, 710, 902,
22, 214, 406, 598, 790, 86, 278, 470, 662, 854, 150, 342, 534, 726, 918,
38, 230, 422, 614, 806, 102, 294, 486, 678, 870, 166, 358, 550, 742, 934,
54, 246, 438, 630, 822, 118, 310, 502, 694, 886, 182, 374, 566, 758, 950,
10, 202, 394, 586, 778, 74, 266, 458, 650, 842, 138, 330, 522, 714, 906,
26, 218, 410, 602, 794, 90, 282, 474, 666, 858, 154, 346, 538, 730, 922,
42, 234, 426, 618, 810, 106, 298, 490, 682, 874, 170, 362, 554, 746, 938,
58, 250, 442, 634, 826, 122, 314, 506, 698, 890, 186, 378, 570, 762, 954,
14, 206, 398, 590, 782, 78, 270, 462, 654, 846, 142, 334, 526, 718, 910,
30, 222, 414, 606, 798, 94, 286, 478, 670, 862, 158, 350, 542, 734, 926,
46, 238, 430, 622, 814, 110, 302, 494, 686, 878, 174, 366, 558, 750, 942,
62, 254, 446, 638, 830, 126, 318, 510, 702, 894, 190, 382, 574, 766, 958,
3, 195, 387, 579, 771, 67, 259, 451, 643, 835, 131, 323, 515, 707, 899,
19, 211, 403, 595, 787, 83, 275, 467, 659, 851, 147, 339, 531, 723, 915,
35, 227, 419, 611, 803, 99, 291, 483, 675, 867, 163, 355, 547, 739, 931,
51, 243, 435, 627, 819, 115, 307, 499, 691, 883, 179, 371, 563, 755, 947,
7, 199, 391, 583, 775, 71, 263, 455, 647, 839, 135, 327, 519, 711, 903,
23, 215, 407, 599, 791, 87, 279, 471, 663, 855, 151, 343, 535, 727, 919,
39, 231, 423, 615, 807, 103, 295, 487, 679, 871, 167, 359, 551, 743, 935,
55, 247, 439, 631, 823, 119, 311, 503, 695, 887, 183, 375, 567, 759, 951,
11, 203, 395, 587, 779, 75, 267, 459, 651, 843, 139, 331, 523, 715, 907,
27, 219, 411, 603, 795, 91, 283, 475, 667, 859, 155, 347, 539, 731, 923,
43, 235, 427, 619, 811, 107, 299, 491, 683, 875, 171, 363, 555, 747, 939,
59, 251, 443, 635, 827, 123, 315, 507, 699, 891, 187, 379, 571, 763, 955,
15, 207, 399, 591, 783, 79, 271, 463, 655, 847, 143, 335, 527, 719, 911,
31, 223, 415, 607, 799, 95, 287, 479, 671, 863, 159, 351, 543, 735, 927,
47, 239, 431, 623, 815, 111, 303, 495, 687, 879, 175, 367, 559, 751, 943,
63, 255, 447, 639, 831, 127, 319, 511, 703, 895, 191, 383, 575, 767, 959,
};

static const kiss_twiddle_cpx fft_twiddles[960] = {
{1.00000000f, -0.00000000f}, {0.999978602f, -0.00654493785f},
{0.999914348f, -0.0130895954f}, {0.999807239f, -0.0196336918f},
{0.999657333f, -0.0261769481f}, {0.999464571f, -0.0327190831f},
{0.999229014f, -0.0392598175f}, {0.998950660f, -0.0457988679f},
{0.998629510f, -0.0523359552f}, {0.998265624f, -0.0588708036f},
{0.997858942f, -0.0654031262f}, {0.997409463f, -0.0719326511f},
{0.996917307f, -0.0784590989f}, {0.996382475f, -0.0849821791f},
{0.995804906f, -0.0915016159f}, {0.995184720f, -0.0980171412f},
{0.994521916f, -0.104528464f}, {0.993816435f, -0.111035310f},
{0.993068457f, -0.117537394f}, {0.992277920f, -0.124034449f},
{0.991444886f, -0.130526185f}, {0.990569353f, -0.137012348f},
{0.989651382f, -0.143492624f}, {0.988691032f, -0.149966761f},
{0.987688363f, -0.156434461f}, {0.986643314f, -0.162895471f},
{0.985556066f, -0.169349506f}, {0.984426558f, -0.175796285f},
{0.983254910f, -0.182235524f}, {0.982041121f, -0.188666970f},
{0.980785251f, -0.195090324f}, {0.979487419f, -0.201505318f},
{0.978147626f, -0.207911685f}, {0.976765871f, -0.214309156f},
{0.975342333f, -0.220697433f}, {0.973876953f, -0.227076262f},
{0.972369909f, -0.233445361f}, {0.970821202f, -0.239804462f},
{0.969230890f, -0.246153295f}, {0.967599094f, -0.252491564f},
{0.965925813f, -0.258819044f}, {0.964211166f, -0.265135437f},
{0.962455213f, -0.271440446f}, {0.960658073f, -0.277733833f},
{0.958819747f, -0.284015357f}, {0.956940353f, -0.290284663f},
{0.955019951f, -0.296541572f}, {0.953058660f, -0.302785784f},
{0.951056540f, -0.309017003f}, {0.949013650f, -0.315234989f},
{0.946930110f, -0.321439475f}, {0.944806039f, -0.327630192f},
{0.942641497f, -0.333806872f}, {0.940436542f, -0.339969248f},
{0.938191354f, -0.346117049f}, {0.935905933f, -0.352250040f},
{0.933580399f, -0.358367950f}, {0.931214929f, -0.364470512f},
{0.928809524f, -0.370557427f}, {0.926364362f, -0.376628488f},
{0.923879504f, -0.382683426f}, {0.921355128f, -0.388721973f},
{0.918791234f, -0.394743860f}, {0.916187942f, -0.400748819f},
{0.913545430f, -0.406736642f}, {0.910863817f, -0.412707031f},
{0.908143163f, -0.418659747f}, {0.905383646f, -0.424594522f},
{0.902585268f, -0.430511087f}, {0.899748266f, -0.436409235f},
{0.896872759f, -0.442288697f}, {0.893958807f, -0.448149204f},
{0.891006529f, -0.453990489f}, {0.888016105f, -0.459812373f},
{0.884987652f, -0.465614527f}, {0.881921291f, -0.471396744f},
{0.878817141f, -0.477158755f}, {0.875675321f, -0.482900351f},
{0.872496009f, -0.488621235f}, {0.869279325f, -0.494321197f},
{0.866025388f, -0.500000000f}, {0.862734377f, -0.505657375f},
{0.859406412f, -0.511293113f}, {0.856041610f, -0.516906917f},
{0.852640152f, -0.522498548f}, {0.849202156f, -0.528067827f},
{0.845727801f, -0.533614516f}, {0.842217207f, -0.539138317f},
{0.838670552f, -0.544639051f}, {0.835087955f, -0.550116420f},
{0.831469595f, -0.555570245f}, {0.827815652f, -0.561000228f},
{0.824126184f, -0.566406250f}, {0.820401430f, -0.571787953f},
{0.816641569f, -0.577145219f}, {0.812846661f, -0.582477689f},
{0.809017003f, -0.587785244f}, {0.805152655f, -0.593067646f},
{0.801253796f, -0.598324597f}, {0.797320664f, -0.603555918f},
{0.793353319f, -0.608761430f}, {0.789352059f, -0.613940835f},
{0.785316944f, -0.619093955f}, {0.781248152f, -0.624220550f},
{0.777145982f, -0.629320383f}, {0.773010433f, -0.634393275f},
{0.768841803f, -0.639438987f}, {0.764640272f, -0.644457340f},
{0.760405958f, -0.649448037f}, {0.756139100f, -0.654410958f},
{0.751839817f, -0.659345806f}, {0.747508347f, -0.664252460f},
{0.743144810f, -0.669130623f}, {0.738749504f, -0.673980117f},
{0.734322488f, -0.678800762f}, {0.729864061f, -0.683592319f},
{0.725374401f, -0.688354552f}, {0.720853567f, -0.693087339f},
{0.716301918f, -0.697790444f}, {0.711719632f, -0.702463686f},
{0.707106769f, -0.707106769f}, {0.702463686f, -0.711719632f},
{0.697790444f, -0.716301918f}, {0.693087339f, -0.720853567f},
{0.688354552f, -0.725374401f}, {0.683592319f, -0.729864061f},
{0.678800762f, -0.734322488f}, {0.673980117f, -0.738749504f},
{0.669130623f, -0.743144810f}, {0.664252460f, -0.747508347f},
{0.659345806f, -0.751839817f}, {0.654410958f, -0.756139100f},
{0.649448037f, -0.760405958f}, {0.644457340f, -0.764640272f},
{0.639438987f, -0.768841803f}, {0.634393275f, -0.773010433f},
{0.629320383f, -0.777145982f}, {0.624220550f, -0.781248152f},
{0.619093955f, -0.785316944f}, {0.613940835f, -0.789352059f},
{0.608761430f, -0.793353319f}, {0.603555918f, -0.797320664f},
{0.598324597f, -0.801253796f}, {0.593067646f, -0.805152655f},
{0.587785244f, -0.809017003f}, {0.582477689f, -0.812846661f},
{0.577145219f, -0.816641569f}, {0.571787953f, -0.820401430f},
{0.566406250f, -0.824126184f}, {0.561000228f, -0.827815652f},
{0.555570245f, -0.831469595f}, {0.550116420f, -0.835087955f},
{0.544639051f, -0.838670552f}, {0.539138317f, -0.842217207f},
{0.533614516f, -0.845727801f}, {0.528067827f, -0.849202156f},
{0.522498548f, -0.852640152f}, {0.516906917f, -0.856041610f},
{0.511293113f, -0.859406412f}, {0.505657375f, -0.862734377f},
{0.500000000f, -0.866025388f}, {0.494321197f, -0.869279325f},
{0.488621235f, -0.872496009f}, {0.482900351f, -0.875675321f},
{0.477158755f, -0.878817141f}, {0.471396744f, -0.881921291f},
{0.465614527f, -0.884987652f}, {0.459812373f, -0.888016105f},
{0.453990489f, -0.891006529f}, {0.448149204f, -0.893958807f},
{0.442288697f, -0.896872759f}, {0.436409235f, -0.899748266f},
{0.430511087f, -0.902585268f}, {0.424594522f, -0.905383646f},
{0.418659747f, -0.908143163f}, {0.412707031f, -0.910863817f},
{0.406736642f, -0.913545430f}, {0.400748819f, -0.916187942f},
{0.394743860f, -0.918791234f}, {0.388721973f, -0.921355128f},
{0.382683426f, -0.923879504f}, {0.376628488f, -0.926364362f},
{0.370557427f, -0.928809524f}, {0.364470512f, -0.931214929f},
{0.358367950f, -0.933580399f}, {0.352250040f, -0.935905933f},
{0.346117049f, -0.938191354f}, {0.339969248f, -0.940436542f},
{0.333806872f, -0.942641497f}, {0.327630192f, -0.944806039f},
{0.321439475f, -0.946930110f}, {0.315234989f, -0.949013650f},
{0.309017003f, -0.951056540f}, {0.302785784f, -0.953058660f},
{0.296541572f, -0.955019951f}, {0.290284663f, -0.956940353f},
{0.284015357f, -0.958819747f}, {0.277733833f, -0.960658073f},
{0.271440446f, -0.962455213f}, {0.265135437f, -0.964211166f},
{0.258819044f, -0.965925813f}, {0.252491564f, -0.967599094f},
{0.246153295f, -0.969230890f}, {0.239804462f, -0.970821202f},
{0.233445361f, -0.972369909f}, {0.227076262f, -0.973876953f},
{0.220697433f, -0.975342333f}, {0.214309156f, -0.976765871f},
{0.207911685f, -0.978147626f}, {0.201505318f, -0.979487419f},
{0.195090324f, -0.980785251f}, {0.188666970f, -0.982041121f},
{0.182235524f, -0.983254910f}, {0.175796285f, -0.984426558f},
{0.169349506f, -0.985556066f}, {0.162895471f, -0.986643314f},
{0.156434461f, -0.987688363f}, {0.149966761f, -0.988691032f},
{0.143492624f, -0.989651382f}, {0.137012348f, -0.990569353f},
{0.130526185f, -0.991444886f}, {0.124034449f, -0.992277920f},
{0.117537394f, -0.993068457f}, {0.111035310f, -0.993816435f},
{0.104528464f, -0.994521916f}, {0.0980171412f, -0.995184720f},
{0.0915016159f, -0.995804906f}, {0.0849821791f, -0.996382475f},
{0.0784590989f, -0.996917307f}, {0.0719326511f, -0.997409463f},
{0.0654031262f, -0.997858942f}, {0.0588708036f, -0.998265624f},
{0.0523359552f, -0.998629510f}, {0.0457988679f, -0.998950660f},
{0.0392598175f, -0.999229014f}, {0.0327190831f, -0.999464571f},
{0.0261769481f, -0.999657333f}, {0.0196336918f, -0.999807239f},
{0.0130895954f, -0.999914348f}, {0.00654493785f, -0.999978602f},
{6.12323426e-17f, -1.00000000f}, {-0.00654493785f, -0.999978602f},
{-0.0130895954f, -0.999914348f}, {-0.0196336918f, -0.999807239f},
{-0.0261769481f, -0.999657333f}, {-0.0327190831f, -0.999464571f},
{-0.0392598175f, -0.999229014f}, {-0.0457988679f, -0.998950660f},
{-0.0523359552f, -0.998629510f}, {-0.0588708036f, -0.998265624f},
{-0.0654031262f, -0.997858942f}, {-0.0719326511f, -0.997409463f},
{-0.0784590989f, -0.996917307f}, {-0.0849821791f, -0.996382475f},
{-0.0915016159f, -0.995804906f}, {-0.0980171412f, -0.995184720f},
{-0.104528464f, -0.994521916f}, {-0.111035310f, -0.993816435f},
{-0.117537394f, -0.993068457f}, {-0.124034449f, -0.992277920f},
{-0.130526185f, -0.991444886f}, {-0.137012348f, -0.990569353f},
{-0.143492624f, -0.989651382f}, {-0.149966761f, -0.988691032f},
{-0.156434461f, -0.987688363f}, {-0.162895471f, -0.986643314f},
{-0.169349506f, -0.985556066f}, {-0.175796285f, -0.984426558f},
{-0.182235524f, -0.983254910f}, {-0.188666970f, -0.982041121f},
{-0.195090324f, -0.980785251f}, {-0.201505318f, -0.979487419f},
{-0.207911685f, -0.978147626f}, {-0.214309156f, -0.976765871f},
{-0.220697433f, -0.975342333f}, {-0.227076262f, -0.973876953f},
{-0.233445361f, -0.972369909f}, {-0.239804462f, -0.970821202f},
{-0.246153295f, -0.969230890f}, {-0.252491564f, -0.967599094f},
{-0.258819044f, -0.965925813f}, {-0.265135437f, -0.964211166f},
{-0.271440446f, -0.962455213f}, {-0.277733833f, -0.960658073f},
{-0.284015357f, -0.958819747f}, {-0.290284663f, -0.956940353f},
{-0.296541572f, -0.955019951f}, {-0.302785784f, -0.953058660f},
{-0.309017003f, -0.951056540f}, {-0.315234989f, -0.949013650f},
{-0.321439475f, -0.946930110f}, {-0.327630192f, -0.944806039f},
{-0.333806872f, -0.942641497f}, {-0.339969248f, -0.940436542f},
{-0.346117049f, -0.938191354f}, {-0.352250040f, -0.935905933f},
{-0.358367950f, -0.933580399f}, {-0.364470512f, -0.931214929f},
{-0.370557427f, -0.928809524f}, {-0.376628488f, -0.926364362f},
{-0.382683426f, -0.923879504f}, {-0.388721973f, -0.921355128f},
{-0.394743860f, -0.918791234f}, {-0.400748819f, -0.916187942f},
{-0.406736642f, -0.913545430f}, {-0.412707031f, -0.910863817f},
{-0.418659747f, -0.908143163f}, {-0.424594522f, -0.905383646f},
{-0.430511087f, -0.902585268f}, {-0.436409235f, -0.899748266f},
{-0.442288697f, -0.896872759f}, {-0.448149204f, -0.893958807f},
{-0.453990489f, -0.891006529f}, {-0.459812373f, -0.888016105f},
{-0.465614527f, -0.884987652f}, {-0.471396744f, -0.881921291f},
{-0.477158755f, -0.878817141f}, {-0.482900351f, -0.875675321f},
{-0.488621235f, -0.872496009f}, {-0.494321197f, -0.869279325f},
{-0.500000000f, -0.866025388f}, {-0.505657375f, -0.862734377f},
{-0.511293113f, -0.859406412f}, {-0.516906917f, -0.856041610f},
{-0.522498548f, -0.852640152f}, {-0.528067827f, -0.849202156f},
{-0.533614516f, -0.845727801f}, {-0.539138317f, -0.842217207f},
{-0.544639051f, -0.838670552f}, {-0.550116420f, -0.835087955f},
{-0.555570245f, -0.831469595f}, {-0.561000228f, -0.827815652f},
{-0.566406250f, -0.824126184f}, {-0.571787953f, -0.820401430f},
{-0.577145219f, -0.816641569f}, {-0.582477689f, -0.812846661f},
{-0.587785244f, -0.809017003f}, {-0.593067646f, -0.805152655f},
{-0.598324597f, -0.801253796f}, {-0.603555918f, -0.797320664f},
{-0.608761430f, -0.793353319f}, {-0.613940835f, -0.789352059f},
{-0.619093955f, -0.785316944f}, {-0.624220550f, -0.781248152f},
{-0.629320383f, -0.777145982f}, {-0.634393275f, -0.773010433f},
{-0.639438987f, -0.768841803f}, {-0.644457340f, -0.764640272f},
{-0.649448037f, -0.760405958f}, {-0.654410958f, -0.756139100f},
{-0.659345806f, -0.751839817f}, {-0.664252460f, -0.747508347f},
{-0.669130623f, -0.743144810f}, {-0.673980117f, -0.738749504f},
{-0.678800762f, -0.734322488f}, {-0.683592319f, -0.729864061f},
{-0.688354552f, -0.725374401f}, {-0.693087339f, -0.720853567f},
{-0.697790444f, -0.716301918f}, {-0.702463686f, -0.711719632f},
{-0.707106769f, -0.707106769f}, {-0.711719632f, -0.702463686f},
{-0.716301918f, -0.697790444f}, {-0.720853567f, -0.693087339f},
{-0.725374401f, -0.688354552f}, {-0.729864061f, -0.683592319f},
{-0.734322488f, -0.678800762f}, {-0.738749504f, -0.673980117f},
{-0.743144810f, -0.669130623f}, {-0.747508347f, -0.664252460f},
{-0.751839817f, -0.659345806f}, {-0.756139100f, -0.654410958f},
{-0.760405958f, -0.649448037f}, {-0.764640272f, -0.644457340f},
{-0.768841803f, -0.639438987f}, {-0.773010433f, -0.634393275f},
{-0.777145982f, -0.629320383f}, {-0.781248152f, -0.624220550f},
{-0.785316944f, -0.619093955f}, {-0.789352059f, -0.613940835f},
{-0.793353319f, -0.608761430f}, {-0.797320664f, -0.603555918f},
{-0.801253796f, -0.598324597f}, {-0.805152655f, -0.593067646f},
{-0.809017003f, -0.587785244f}, {-0.812846661f, -0.582477689f},
{-0.816641569f, -0.577145219f}, {-0.820401430f, -0.571787953f},
{-0.824126184f, -0.566406250f}, {-0.827815652f, -0.561000228f},
{-0.831469595f, -0.555570245f}, {-0.835087955f, -0.550116420f},
{-0.838670552f, -0.544639051f}, {-0.842217207f, -0.539138317f},
{-0.845727801f, -0.533614516f}, {-0.849202156f, -0.528067827f},
{-0.852640152f, -0.522498548f}, {-0.856041610f, -0.516906917f},
{-0.859406412f, -0.511293113f}, {-0.862734377f, -0.505657375f},
{-0.866025388f, -0.500000000f}, {-0.869279325f, -0.494321197f},
{-0.872496009f, -0.488621235f}, {-0.875675321f, -0.482900351f},
{-0.878817141f, -0.477158755f}, {-0.881921291f, -0.471396744f},
{-0.884987652f, -0.465614527f}, {-0.888016105f, -0.459812373f},
{-0.891006529f, -0.453990489f}, {-0.893958807f, -0.448149204f},
{-0.896872759f, -0.442288697f}, {-0.899748266f, -0.436409235f},
{-0.902585268f, -0.430511087f}, {-0.905383646f, -0.424594522f},
{-0.908143163f, -0.418659747f}, {-0.910863817f, -0.412707031f},
{-0.913545430f, -0.406736642f}, {-0.916187942f, -0.400748819f},
{-0.918791234f, -0.394743860f}, {-0.921355128f, -0.388721973f},
{-0.923879504f, -0.382683426f}, {-0.926364362f, -0.376628488f},
{-0.928809524f, -0.370557427f}, {-0.931214929f, -0.364470512f},
{-0.933580399f, -0.358367950f}, {-0.935905933f, -0.352250040f},
{-0.938191354f, -0.346117049f}, {-0.940436542f, -0.339969248f},
{-0.942641497f, -0.333806872f}, {-0.944806039f, -0.327630192f},
{-0.946930110f, -0.321439475f}, {-0.949013650f, -0.315234989f},
{-0.951056540f, -0.309017003f}, {-0.953058660f, -0.302785784f},
{-0.955019951f, -0.296541572f}, {-0.956940353f, -0.290284663f},
{-0.958819747f, -0.284015357f}, {-0.960658073f, -0.277733833f},
{-0.962455213f, -0.271440446f}, {-0.964211166f, -0.265135437f},
{-0.965925813f, -0.258819044f}, {-0.967599094f, -0.252491564f},
{-0.969230890f, -0.246153295f}, {-0.970821202f, -0.239804462f},
{-0.972369909f, -0.233445361f}, {-0.973876953f, -0.227076262f},
{-0.975342333f, -0.220697433f}, {-0.976765871f, -0.214309156f},
{-0.978147626f, -0.207911685f}, {-0.979487419f, -0.201505318f},
{-0.980785251f, -0.195090324f}, {-0.982041121f, -0.188666970f},
{-0.983254910f, -0.182235524f}, {-0.984426558f, -0.175796285f},
{-0.985556066f, -0.169349506f}, {-0.986643314f, -0.162895471f},
{-0.987688363f, -0.156434461f}, {-0.988691032f, -0.149966761f},
{-0.989651382f, -0.143492624f}, {-0.990569353f, -0.137012348f},
{-0.991444886f, -0.130526185f}, {-0.992277920f, -0.124034449f},
{-0.993068457f, -0.117537394f}, {-0.993816435f, -0.111035310f},
{-0.994521916f, -0.104528464f}, {-0.995184720f, -0.0980171412f},
{-0.995804906f, -0.0915016159f}, {-0.996382475f, -0.0849821791f},
{-0.996917307f, -0.0784590989f}, {-0.997409463f, -0.0719326511f},
{-0.997858942f, -0.0654031262f}, {-0.998265624f, -0.0588708036f},
{-0.998629510f, -0.0523359552f}, {-0.998950660f, -0.0457988679f},
{-0.999229014f, -0.0392598175f}, {-0.999464571f, -0.0327190831f},
{-0.999657333f, -0.0261769481f}, {-0.999807239f, -0.0196336918f},
{-0.999914348f, -0.0130895954f}, {-0.999978602f, -0.00654493785f},
{-1.00000000f, -1.22464685e-16f}, {-0.999978602f, 0.00654493785f},
{-0.999914348f, 0.0130895954f}, {-0.999807239f, 0.0196336918f},
{-0.999657333f, 0.0261769481f}, {-0.999464571f, 0.0327190831f},
{-0.999229014f, 0.0392598175f}, {-0.998950660f, 0.0457988679f},
{-0.998629510f, 0.0523359552f}, {-0.998265624f, 0.0588708036f},
{-0.997858942f, 0.0654031262f}, {-0.997409463f, 0.0719326511f},
{-0.996917307f, 0.0784590989f}, {-0.996382475f, 0.0849821791f},
{-0.995804906f, 0.0915016159f}, {-0.995184720f, 0.0980171412f},
{-0.994521916f, 0.104528464f}, {-0.993816435f, 0.111035310f},
{-0.993068457f, 0.117537394f}, {-0.992277920f, 0.124034449f},
{-0.991444886f, 0.130526185f}, {-0.990569353f, 0.137012348f},
{-0.989651382f, 0.143492624f}, {-0.988691032f, 0.149966761f},
{-0.987688363f, 0.156434461f}, {-0.986643314f, 0.162895471f},
{-0.985556066f, 0.169349506f}, {-0.984426558f, 0.175796285f},
{-0.983254910f, 0.182235524f}, {-0.982041121f, 0.188666970f},
{-0.980785251f, 0.195090324f}, {-0.979487419f, 0.201505318f},
{-0.978147626f, 0.207911685f}, {-0.976765871f, 0.214309156f},
{-0.975342333f, 0.220697433f}, {-0.973876953f, 0.227076262f},
{-0.972369909f, 0.233445361f}, {-0.970821202f, 0.239804462f},
{-0.969230890f, 0.246153295f}, {-0.967599094f, 0.252491564f},
{-0.965925813f, 0.258819044f}, {-0.964211166f, 0.265135437f},
{-0.962455213f, 0.271440446f}, {-0.960658073f, 0.277733833f},
{-0.958819747f, 0.284015357f}, {-0.956940353f, 0.290284663f},
{-0.955019951f, 0.296541572f}, {-0.953058660f, 0.302785784f},
{-0.951056540f, 0.309017003f}, {-0.949013650f, 0.315234989f},
{-0.946930110f, 0.321439475f}, {-0.944806039f, 0.327630192f},
{-0.942641497f, 0.333806872f}, {-0.940436542f, 0.339969248f},
{-0.938191354f, 0.346117049f}, {-0.935905933f, 0.352250040f},
{-0.933580399f, 0.358367950f}, {-0.931214929f, 0.364470512f},
{-0.928809524f, 0.370557427f}, {-0.926364362f, 0.376628488f},
{-0.923879504f, 0.382683426f}, {-0.921355128f, 0.388721973f},
{-0.918791234f, 0.394743860f}, {-0.916187942f, 0.400748819f},
{-0.913545430f, 0.406736642f}, {-0.910863817f, 0.412707031f},
{-0.908143163f, 0.418659747f}, {-0.905383646f, 0.424594522f},
{-0.902585268f, 0.430511087f}, {-0.899748266f, 0.436409235f},
{-0.896872759f, 0.442288697f}, {-0.893958807f, 0.448149204f},
{-0.891006529f, 0.453990489f}, {-0.888016105f, 0.459812373f},
{-0.884987652f, 0.465614527f}, {-0.881921291f, 0.471396744f},
{-0.878817141f, 0.477158755f}, {-0.875675321f, 0.482900351f},
{-0.872496009f, 0.488621235f}, {-0.869279325f, 0.494321197f},
{-0.866025388f, 0.500000000f}, {-0.862734377f, 0.505657375f},
{-0.859406412f, 0.511293113f}, {-0.856041610f, 0.516906917f},
{-0.852640152f, 0.522498548f}, {-0.849202156f, 0.528067827f},
{-0.845727801f, 0.533614516f}, {-0.842217207f, 0.539138317f},
{-0.838670552f, 0.544639051f}, {-0.835087955f, 0.550116420f},
{-0.831469595f, 0.555570245f}, {-0.827815652f, 0.561000228f},
{-0.824126184f, 0.566406250f}, {-0.820401430f, 0.571787953f},
{-0.816641569f, 0.577145219f}, {-0.812846661f, 0.582477689f},
{-0.809017003f, 0.587785244f}, {-0.805152655f, 0.593067646f},
{-0.801253796f, 0.598324597f}, {-0.797320664f, 0.603555918f},
{-0.793353319f, 0.608761430f}, {-0.789352059f, 0.613940835f},
{-0.785316944f, 0.619093955f}, {-0.781248152f, 0.624220550f},
{-0.777145982f, 0.629320383f}, {-0.773010433f, 0.634393275f},
{-0.768841803f, 0.639438987f}, {-0.764640272f, 0.644457340f},
{-0.760405958f, 0.649448037f}, {-0.756139100f, 0.654410958f},
{-0.751839817f, 0.659345806f}, {-0.747508347f, 0.664252460f},
{-0.743144810f, 0.669130623f}, {-0.738749504f, 0.673980117f},
{-0.734322488f, 0.678800762f}, {-0.729864061f, 0.683592319f},
{-0.725374401f, 0.688354552f}, {-0.720853567f, 0.693087339f},
{-0.716301918f, 0.697790444f}, {-0.711719632f, 0.702463686f},
{-0.707106769f, 0.707106769f}, {-0.702463686f, 0.711719632f},
{-0.697790444f, 0.716301918f}, {-0.693087339f, 0.720853567f},
{-0.688354552f, 0.725374401f}, {-0.683592319f, 0.729864061f},
{-0.678800762f, 0.734322488f}, {-0.673980117f, 0.738749504f},
{-0.669130623f, 0.743144810f}, {-0.664252460f, 0.747508347f},
{-0.659345806f, 0.751839817f}, {-0.654410958f, 0.756139100f},
{-0.649448037f, 0.760405958f}, {-0.644457340f, 0.764640272f},
{-0.639438987f, 0.768841803f}, {-0.634393275f, 0.773010433f},
{-0.629320383f, 0.777145982f}, {-0.624220550f, 0.781248152f},
{-0.619093955f, 0.785316944f}, {-0.613940835f, 0.789352059f},
{-0.608761430f, 0.793353319f}, {-0.603555918f, 0.797320664f},
{-0.598324597f, 0.801253796f}, {-0.593067646f, 0.805152655f},
{-0.587785244f, 0.809017003f}, {-0.582477689f, 0.812846661f},
{-0.577145219f, 0.816641569f}, {-0.571787953f, 0.820401430f},
{-0.566406250f, 0.824126184f}, {-0.561000228f, 0.827815652f},
{-0.555570245f, 0.831469595f}, {-0.550116420f, 0.835087955f},
{-0.544639051f, 0.838670552f}, {-0.539138317f, 0.842217207f},
{-0.533614516f, 0.845727801f}, {-0.528067827f, 0.849202156f},
{-0.522498548f, 0.852640152f}, {-0.516906917f, 0.856041610f},
{-0.511293113f, 0.859406412f}, {-0.505657375f, 0.862734377f},
{-0.500000000f, 0.866025388f}, {-0.494321197f, 0.869279325f},
{-0.488621235f, 0.872496009f}, {-0.482900351f, 0.875675321f},
{-0.477158755f, 0.878817141f}, {-0.471396744f, 0.881921291f},
{-0.465614527f, 0.884987652f}, {-0.459812373f, 0.888016105f},
{-0.453990489f, 0.891006529f}, {-0.448149204f, 0.893958807f},
{-0.442288697f, 0.896872759f}, {-0.436409235f, 0.899748266f},
{-0.430511087f, 0.902585268f}, {-0.424594522f, 0.905383646f},
{-0.418659747f, 0.908143163f}, {-0.412707031f, 0.910863817f},
{-0.406736642f, 0.913545430f}, {-0.400748819f, 0.916187942f},
{-0.394743860f, 0.918791234f}, {-0.388721973f, 0.921355128f},
{-0.382683426f, 0.923879504f}, {-0.376628488f, 0.926364362f},
{-0.370557427f, 0.928809524f}, {-0.364470512f, 0.931214929f},
{-0.358367950f, 0.933580399f}, {-0.352250040f, 0.935905933f},
{-0.346117049f, 0.938191354f}, {-0.339969248f, 0.940436542f},
{-0.333806872f, 0.942641497f}, {-0.327630192f, 0.944806039f},
{-0.321439475f, 0.946930110f}, {-0.315234989f, 0.949013650f},
{-0.309017003f, 0.951056540f}, {-0.302785784f, 0.953058660f},
{-0.296541572f, 0.955019951f}, {-0.290284663f, 0.956940353f},
{-0.284015357f, 0.958819747f}, {-0.277733833f, 0.960658073f},
{-0.271440446f, 0.962455213f}, {-0.265135437f, 0.964211166f},
{-0.258819044f, 0.965925813f}, {-0.252491564f, 0.967599094f},
{-0.246153295f, 0.969230890f}, {-0.239804462f, 0.970821202f},
{-0.233445361f, 0.972369909f}, {-0.227076262f, 0.973876953f},
{-0.220697433f, 0.975342333f}, {-0.214309156f, 0.976765871f},
{-0.207911685f, 0.978147626f}, {-0.201505318f, 0.979487419f},
{-0.195090324f, 0.980785251f}, {-0.188666970f, 0.982041121f},
{-0.182235524f, 0.983254910f}, {-0.175796285f, 0.984426558f},
{-0.169349506f, 0.985556066f}, {-0.162895471f, 0.986643314f},
{-0.156434461f, 0.987688363f}, {-0.149966761f, 0.988691032f},
{-0.143492624f, 0.989651382f}, {-0.137012348f, 0.990569353f},
{-0.130526185f, 0.991444886f}, {-0.124034449f, 0.992277920f},
{-0.117537394f, 0.993068457f}, {-0.111035310f, 0.993816435f},
{-0.104528464f, 0.994521916f}, {-0.0980171412f, 0.995184720f},
{-0.0915016159f, 0.995804906f}, {-0.0849821791f, 0.996382475f},
{-0.0784590989f, 0.996917307f}, {-0.0719326511f, 0.997409463f},
{-0.0654031262f, 0.997858942f}, {-0.0588708036f, 0.998265624f},
{-0.0523359552f, 0.998629510f}, {-0.0457988679f, 0.998950660f},
{-0.0392598175f, 0.999229014f}, {-0.0327190831f, 0.999464571f},
{-0.0261769481f, 0.999657333f}, {-0.0196336918f, 0.999807239f},
{-0.0130895954f, 0.999914348f}, {-0.00654493785f, 0.999978602f},
{-1.83697015e-16f, 1.00000000f}, {0.00654493785f, 0.999978602f},
{0.0130895954f, 0.999914348f}, {0.0196336918f, 0.999807239f},
{0.0261769481f, 0.999657333f}, {0.0327190831f, 0.999464571f},
{0.0392598175f, 0.999229014f}, {0.0457988679f, 0.998950660f},
{0.0523359552f, 0.998629510f}, {0.0588708036f, 0.998265624f},
{0.0654031262f, 0.997858942f}, {0.0719326511f, 0.997409463f},
{0.0784590989f, 0.996917307f}, {0.0849821791f, 0.996382475f},
{0.0915016159f, 0.995804906f}, {0.0980171412f, 0.995184720f},
{0.104528464f, 0.994521916f}, {0.111035310f, 0.993816435f},
{0.117537394f, 0.993068457f}, {0.124034449f, 0.992277920f},
{0.130526185f, 0.991444886f}, {0.137012348f, 0.990569353f},
{0.143492624f, 0.989651382f}, {0.149966761f, 0.988691032f},
{0.156434461f, 0.987688363f}, {0.162895471f, 0.986643314f},
{0.169349506f, 0.985556066f}, {0.175796285f, 0.984426558f},
{0.182235524f, 0.983254910f}, {0.188666970f, 0.982041121f},
{0.195090324f, 0.980785251f}, {0.201505318f, 0.979487419f},
{0.207911685f, 0.978147626f}, {0.214309156f, 0.976765871f},
{0.220697433f, 0.975342333f}, {0.227076262f, 0.973876953f},
{0.233445361f, 0.972369909f}, {0.239804462f, 0.970821202f},
{0.246153295f, 0.969230890f}, {0.252491564f, 0.967599094f},
{0.258819044f, 0.965925813f}, {0.265135437f, 0.964211166f},
{0.271440446f, 0.962455213f}, {0.277733833f, 0.960658073f},
{0.284015357f, 0.958819747f}, {0.290284663f, 0.956940353f},
{0.296541572f, 0.955019951f}, {0.302785784f, 0.953058660f},
{0.309017003f, 0.951056540f}, {0.315234989f, 0.949013650f},
{0.321439475f, 0.946930110f}, {0.327630192f, 0.944806039f},
{0.333806872f, 0.942641497f}, {0.339969248f, 0.940436542f},
{0.346117049f, 0.938191354f}, {0.352250040f, 0.935905933f},
{0.358367950f, 0.933580399f}, {0.364470512f, 0.931214929f},
{0.370557427f, 0.928809524f}, {0.376628488f, 0.926364362f},
{0.382683426f, 0.923879504f}, {0.388721973f, 0.921355128f},
{0.394743860f, 0.918791234f}, {0.400748819f, 0.916187942f},
{0.406736642f, 0.913545430f}, {0.412707031f, 0.910863817f},
{0.418659747f, 0.908143163f}, {0.424594522f, 0.905383646f},
{0.430511087f, 0.902585268f}, {0.436409235f, 0.899748266f},
{0.442288697f, 0.896872759f}, {0.448149204f, 0.893958807f},
{0.453990489f, 0.891006529f}, {0.459812373f, 0.888016105f},
{0.465614527f, 0.884987652f}, {0.471396744f, 0.881921291f},
{0.477158755f, 0.878817141f}, {0.482900351f, 0.875675321f},
{0.488621235f, 0.872496009f}, {0.494321197f, 0.869279325f},
{0.500000000f, 0.866025388f}, {0.505657375f, 0.862734377f},
{0.511293113f, 0.859406412f}, {0.516906917f, 0.856041610f},
{0.522498548f, 0.852640152f}, {0.528067827f, 0.849202156f},
{0.533614516f, 0.845727801f}, {0.539138317f, 0.842217207f},
{0.544639051f, 0.838670552f}, {0.550116420f, 0.835087955f},
{0.555570245f, 0.831469595f}, {0.561000228f, 0.827815652f},
{0.566406250f, 0.824126184f}, {0.571787953f, 0.820401430f},
{0.577145219f, 0.816641569f}, {0.582477689f, 0.812846661f},
{0.587785244f, 0.809017003f}, {0.593067646f, 0.805152655f},
{0.598324597f, 0.801253796f}, {0.603555918f, 0.797320664f},
{0.608761430f, 0.793353319f}, {0.613940835f, 0.789352059f},
{0.619093955f, 0.785316944f}, {0.624220550f, 0.781248152f},
{0.629320383f, 0.777145982f}, {0.634393275f, 0.773010433f},
{0.639438987f, 0.768841803f}, {0.644457340f, 0.764640272f},
{0.649448037f, 0.760405958f}, {0.654410958f, 0.756139100f},
{0.659345806f, 0.751839817f}, {0.664252460f, 0.747508347f},
{0.669130623f, 0.743144810f}, {0.673980117f, 0.738749504f},
{0.678800762f, 0.734322488f}, {0.683592319f, 0.729864061f},
{0.688354552f, 0.725374401f}, {0.693087339f, 0.720853567f},
{0.697790444f, 0.716301918f}, {0.702463686f, 0.711719632f},
{0.707106769f, 0.707106769f}, {0.711719632f, 0.702463686f},
{0.716301918f, 0.697790444f}, {0.720853567f, 0.693087339f},
{0.725374401f, 0.688354552f}, {0.729864061f, 0.683592319f},
{0.734322488f, 0.678800762f}, {0.738749504f, 0.673980117f},
{0.743144810f, 0.669130623f}, {0.747508347f, 0.664252460f},
{0.751839817f, 0.659345806f}, {0.756139100f, 0.654410958f},
{0.760405958f, 0.649448037f}, {0.764640272f, 0.644457340f},
{0.768841803f, 0.639438987f}, {0.773010433f, 0.634393275f},
{0.777145982f, 0.629320383f}, {0.781248152f, 0.624220550f},
{0.785316944f, 0.619093955f}, {0.789352059f, 0.613940835f},
{0.793353319f, 0.608761430f}, {0.797320664f, 0.603555918f},
{0.801253796f, 0.598324597f}, {0.805152655f, 0.593067646f},
{0.809017003f, 0.587785244f}, {0.812846661f, 0.582477689f},
{0.816641569f, 0.577145219f}, {0.820401430f, 0.571787953f},
{0.824126184f, 0.566406250f}, {0.827815652f, 0.561000228f},
{0.831469595f, 0.555570245f}, {0.835087955f, 0.550116420f},
{0.838670552f, 0.544639051f}, {0.842217207f, 0.539138317f},
{0.845727801f, 0.533614516f}, {0.849202156f, 0.528067827f},
{0.852640152f, 0.522498548f}, {0.856041610f, 0.516906917f},
{0.859406412f, 0.511293113f}, {0.862734377f, 0.505657375f},
{0.866025388f, 0.500000000f}, {0.869279325f, 0.494321197f},
{0.872496009f, 0.488621235f}, {0.875675321f, 0.482900351f},
{0.878817141f, 0.477158755f}, {0.881921291f, 0.471396744f},
{0.884987652f, 0.465614527f}, {0.888016105f, 0.459812373f},
{0.891006529f, 0.453990489f}, {0.893958807f, 0.448149204f},
{0.896872759f, 0.442288697f}, {0.899748266f, 0.436409235f},
{0.902585268f, 0.430511087f}, {0.905383646f, 0.424594522f},
{0.908143163f, 0.418659747f}, {0.910863817f, 0.412707031f},
{0.913545430f, 0.406736642f}, {0.916187942f, 0.400748819f},
{0.918791234f, 0.394743860f}, {0.921355128f, 0.388721973f},
{0.923879504f, 0.382683426f}, {0.926364362f, 0.376628488f},
{0.928809524f, 0.370557427f}, {0.931214929f, 0.364470512f},
{0.933580399f, 0.358367950f}, {0.935905933f, 0.352250040f},
{0.938191354f, 0.346117049f}, {0.940436542f, 0.339969248f},
{0.942641497f, 0.333806872f}, {0.944806039f, 0.327630192f},
{0.946930110f, 0.321439475f}, {0.949013650f, 0.315234989f},
{0.951056540f, 0.309017003f}, {0.953058660f, 0.302785784f},
{0.955019951f, 0.296541572f}, {0.956940353f, 0.290284663f},
{0.958819747f, 0.284015357f}, {0.960658073f, 0.277733833f},
{0.962455213f, 0.271440446f}, {0.964211166f, 0.265135437f},
{0.965925813f, 0.258819044f}, {0.967599094f, 0.252491564f},
{0.969230890f, 0.246153295f}, {0.970821202f, 0.239804462f},
{0.972369909f, 0.233445361f}, {0.973876953f, 0.227076262f},
{0.975342333f, 0.220697433f}, {0.976765871f, 0.214309156f},
{0.978147626f, 0.207911685f}, {0.979487419f, 0.201505318f},
{0.980785251f, 0.195090324f}, {0.982041121f, 0.188666970f},
{0.983254910f, 0.182235524f}, {0.984426558f, 0.175796285f},
{0.985556066f, 0.169349506f}, {0.986643314f, 0.162895471f},
{0.987688363f, 0.156434461f}, {0.988691032f, 0.149966761f},
{0.989651382f, 0.143492624f}, {0.990569353f, 0.137012348f},
{0.991444886f, 0.130526185f}, {0.992277920f, 0.124034449f},
{0.993068457f, 0.117537394f}, {0.993816435f, 0.111035310f},
{0.994521916f, 0.104528464f}, {0.995184720f, 0.0980171412f},
{0.995804906f, 0.0915016159f}, {0.996382475f, 0.0849821791f},
{0.996917307f, 0.0784590989f}, {0.997409463f, 0.0719326511f},
{0.997858942f, 0.0654031262f}, {0.998265624f, 0.0588708036f},
{0.998629510f, 0.0523359552f}, {0.998950660f, 0.0457988679f},
{0.999229014f, 0.0392598175f}, {0.999464571f, 0.0327190831f},
{0.999657333f, 0.0261769481f}, {0.999807239f, 0.0196336918f},
{0.999914348f, 0.0130895954f}, {0.999978602f, 0.00654493785f},
};

const kiss_fft_state rnn_kfft = {
960, /* nfft */
0.0010416667f, /* scale */
-1, /* shift */
{5, 192, 3, 64, 4, 16, 4, 4, 4, 1, 0, 0, 0, 0, 0, 0, }, /* factors */
fft_bitrev, /* bitrev*/
fft_twiddles, /* twiddles*/
(arch_fft_state *)&arch_fft, /* arch_fft*/
};

const float rnn_half_window[] = {
4.20549168e-06f, 3.78491532e-05f, 0.000105135041f, 0.000206060256f, 0.000340620492f,
0.000508809986f, 0.000710621476f, 0.000946046319f, 0.00121507444f, 0.00151769421f,
0.00185389258f, 0.00222365512f, 0.00262696599f, 0.00306380726f, 0.00353416055f,
0.00403800514f, 0.00457531959f, 0.00514607970f, 0.00575026125f, 0.00638783723f,
0.00705878017f, 0.00776306028f, 0.00850064680f, 0.00927150715f, 0.0100756064f,
0.0109129101f, 0.0117833801f, 0.0126869772f, 0.0136236614f, 0.0145933898f,
0.0155961197f, 0.0166318044f, 0.0177003983f, 0.0188018531f, 0.0199361145f,
0.0211031344f, 0.0223028567f, 0.0235352255f, 0.0248001851f, 0.0260976739f,
0.0274276342f, 0.0287899990f, 0.0301847085f, 0.0316116922f, 0.0330708846f,
0.0345622115f, 0.0360856056f, 0.0376409888f, 0.0392282903f, 0.0408474281f,
0.0424983241f, 0.0441808924f, 0.0458950549f, 0.0476407260f, 0.0494178124f,
0.0512262285f, 0.0530658774f, 0.0549366735f, 0.0568385124f, 0.0587713011f,
0.0607349351f, 0.0627293140f, 0.0647543296f, 0.0668098852f, 0.0688958541f,
0.0710121393f, 0.0731586292f, 0.0753351897f, 0.0775417164f, 0.0797780901f,
0.0820441842f, 0.0843398646f, 0.0866650119f, 0.0890194997f, 0.0914031938f,
0.0938159525f, 0.0962576419f, 0.0987281203f, 0.101227246f, 0.103754878f,
0.106310867f, 0.108895063f, 0.111507311f, 0.114147455f, 0.116815343f,
0.119510807f, 0.122233689f, 0.124983832f, 0.127761051f, 0.130565181f,
0.133396059f, 0.136253506f, 0.139137328f, 0.142047361f, 0.144983411f,
0.147945285f, 0.150932819f, 0.153945804f, 0.156984031f, 0.160047337f,
0.163135484f, 0.166248307f, 0.169385567f, 0.172547072f, 0.175732598f,
0.178941950f, 0.182174906f, 0.185431242f, 0.188710734f, 0.192013159f,
0.195338294f, 0.198685899f, 0.202055752f, 0.205447599f, 0.208861232f,
0.212296382f, 0.215752810f, 0.219230279f, 0.222728521f, 0.226247311f,
0.229786381f, 0.233345464f, 0.236924306f, 0.240522653f, 0.244140238f,
0.247776777f, 0.251432031f, 0.255105674f, 0.258797467f, 0.262507141f,
0.266234398f, 0.269978970f, 0.273740560f, 0.277518868f, 0.281313598f,
0.285124481f, 0.288951218f, 0.292793512f, 0.296651065f, 0.300523549f,
0.304410696f, 0.308312178f, 0.312227666f, 0.316156894f, 0.320099503f,
0.324055225f, 0.328023702f, 0.332004637f, 0.335997701f, 0.340002567f,
0.344018906f, 0.348046392f, 0.352084726f, 0.356133521f, 0.360192508f,
0.364261299f, 0.368339598f, 0.372427016f, 0.376523286f, 0.380627990f,
0.384740859f, 0.388861477f, 0.392989576f, 0.397124738f, 0.401266664f,
0.405414969f, 0.409569323f, 0.413729399f, 0.417894781f, 0.422065198f,
0.426240236f, 0.430419534f, 0.434602767f, 0.438789606f, 0.442979604f,
0.447172493f, 0.451367885f, 0.455565393f, 0.459764689f, 0.463965416f,
0.468167186f, 0.472369671f, 0.476572484f, 0.480775267f, 0.484977663f,
0.489179343f, 0.493379891f, 0.497579008f, 0.501776278f, 0.505971372f,
0.510163903f, 0.514353573f, 0.518539906f, 0.522722721f, 0.526901484f,
0.531075954f, 0.535245717f, 0.539410412f, 0.543569744f, 0.547723293f,
0.551870763f, 0.556011736f, 0.560145974f, 0.564273000f, 0.568392515f,
0.572504222f, 0.576607704f, 0.580702662f, 0.584788740f, 0.588865638f,
0.592932940f, 0.596990347f, 0.601037502f, 0.605074167f, 0.609099925f,
0.613114417f, 0.617117405f, 0.621108532f, 0.625087440f, 0.629053831f,
0.633007407f, 0.636947870f, 0.640874863f, 0.644788086f, 0.648687243f,
0.652572036f, 0.656442165f, 0.660297334f, 0.664137185f, 0.667961538f,
0.671769977f, 0.675562322f, 0.679338276f, 0.683097482f, 0.686839759f,
0.690564752f, 0.694272280f, 0.697961986f, 0.701633692f, 0.705287039f,
0.708921850f, 0.712537885f, 0.716134787f, 0.719712436f, 0.723270535f,
0.726808906f, 0.730327189f, 0.733825266f, 0.737302899f, 0.740759790f,
0.744195819f, 0.747610688f, 0.751004279f, 0.754376352f, 0.757726669f,
0.761055112f, 0.764361382f, 0.767645359f, 0.770906866f, 0.774145722f,
0.777361751f, 0.780554771f, 0.783724606f, 0.786871076f, 0.789994121f,
0.793093503f, 0.796169102f, 0.799220800f, 0.802248418f, 0.805251837f,
0.808230937f, 0.811185598f, 0.814115703f, 0.817021132f, 0.819901764f,
0.822757542f, 0.825588286f, 0.828393936f, 0.831174433f, 0.833929658f,
0.836659551f, 0.839363992f, 0.842042983f, 0.844696403f, 0.847324252f,
0.849926353f, 0.852502763f, 0.855053425f, 0.857578218f, 0.860077202f,
0.862550259f, 0.864997447f, 0.867418647f, 0.869813919f, 0.872183204f,
0.874526560f, 0.876843870f, 0.879135191f, 0.881400526f, 0.883639932f,
0.885853291f, 0.888040781f, 0.890202343f, 0.892337978f, 0.894447744f,
0.896531701f, 0.898589849f, 0.900622249f, 0.902628958f, 0.904610038f,
0.906565487f, 0.908495426f, 0.910399914f, 0.912279010f, 0.914132774f,
0.915961266f, 0.917764664f, 0.919542909f, 0.921296239f, 0.923024654f,
0.924728215f, 0.926407158f, 0.928061485f, 0.929691315f, 0.931296766f,
0.932878017f, 0.934435070f, 0.935968161f, 0.937477291f, 0.938962698f,
0.940424502f, 0.941862822f, 0.943277776f, 0.944669485f, 0.946038187f,
0.947383940f, 0.948706925f, 0.950007319f, 0.951285243f, 0.952540874f,
0.953774393f, 0.954985917f, 0.956175685f, 0.957343817f, 0.958490491f,
0.959615886f, 0.960720181f, 0.961803555f, 0.962866247f, 0.963908315f,
0.964930058f, 0.965931594f, 0.966913164f, 0.967874944f, 0.968817174f,
0.969739914f, 0.970643520f, 0.971528113f, 0.972393870f, 0.973241091f,
0.974069893f, 0.974880517f, 0.975673139f, 0.976447999f, 0.977205336f,
0.977945268f, 0.978668094f, 0.979374051f, 0.980063200f, 0.980735898f,
0.981392324f, 0.982032716f, 0.982657254f, 0.983266115f, 0.983859658f,
0.984437943f, 0.985001266f, 0.985549867f, 0.986083925f, 0.986603677f,
0.987109363f, 0.987601161f, 0.988079309f, 0.988544047f, 0.988995552f,
0.989434063f, 0.989859879f, 0.990273118f, 0.990674019f, 0.991062820f,
0.991439700f, 0.991804957f, 0.992158771f, 0.992501318f, 0.992832899f,
0.993153632f, 0.993463814f, 0.993763626f, 0.994053245f, 0.994332969f,
0.994602919f, 0.994863331f, 0.995114446f, 0.995356441f, 0.995589554f,
0.995813966f, 0.996029854f, 0.996237516f, 0.996437073f, 0.996628702f,
0.996812642f, 0.996989131f, 0.997158289f, 0.997320294f, 0.997475445f,
0.997623861f, 0.997765720f, 0.997901261f, 0.998030603f, 0.998153925f,
0.998271465f, 0.998383403f, 0.998489857f, 0.998591006f, 0.998687088f,
0.998778164f, 0.998864532f, 0.998946249f, 0.999023557f, 0.999096513f,
0.999165416f, 0.999230266f, 0.999291301f, 0.999348700f, 0.999402523f,
0.999453008f, 0.999500215f, 0.999544322f, 0.999585509f, 0.999623775f,
0.999659419f, 0.999692440f, 0.999723017f, 0.999751270f, 0.999777317f,
0.999801278f, 0.999823213f, 0.999843359f, 0.999861658f, 0.999878347f,
0.999893486f, 0.999907196f, 0.999919534f, 0.999930561f, 0.999940455f,
0.999949217f, 0.999957025f, 0.999963880f, 0.999969840f, 0.999975085f,
0.999979615f, 0.999983490f, 0.999986768f, 0.999989510f, 0.999991834f,
0.999993742f, 0.999995291f, 0.999996543f, 0.999997556f, 0.999998271f,
0.999998868f, 0.999999285f, 0.999999523f, 0.999999762f, 0.999999881f,
0.999999940f, 1.00000000f, 1.00000000f, 1.00000000f, 1.00000000f,
};

const float rnn_dct_table[] = {
0.707106769f, 0.998795450f, 0.995184720f, 0.989176512f, 0.980785251f,
0.970031261f, 0.956940353f, 0.941544056f, 0.923879504f, 0.903989315f,
0.881921291f, 0.857728601f, 0.831469595f, 0.803207517f, 0.773010433f,
0.740951121f, 0.707106769f, 0.671558976f, 0.634393275f, 0.595699310f,
0.555570245f, 0.514102757f, 0.471396744f, 0.427555084f, 0.382683426f,
0.336889863f, 0.290284663f, 0.242980182f, 0.195090324f, 0.146730468f,
0.0980171412f, 0.0490676761f, 0.707106769f, 0.989176512f, 0.956940353f,
0.903989315f, 0.831469595f, 0.740951121f, 0.634393275f, 0.514102757f,
0.382683426f, 0.242980182f, 0.0980171412f, -0.0490676761f, -0.195090324f,
-0.336889863f, -0.471396744f, -0.595699310f, -0.707106769f, -0.803207517f,
-0.881921291f, -0.941544056f, -0.980785251f, -0.998795450f, -0.995184720f,
-0.970031261f, -0.923879504f, -0.857728601f, -0.773010433f, -0.671558976f,
-0.555570245f, -0.427555084f, -0.290284663f, -0.146730468f, 0.707106769f,
0.970031261f, 0.881921291f, 0.740951121f, 0.555570245f, 0.336889863f,
0.0980171412f, -0.146730468f, -0.382683426f, -0.595699310f, -0.773010433f,
-0.903989315f, -0.980785251f, -0.998795450f, -0.956940353f, -0.857728601f,
-0.707106769f, -0.514102757f, -0.290284663f, -0.0490676761f, 0.195090324f,
0.427555084f, 0.634393275f, 0.803207517f, 0.923879504f, 0.989176512f,
0.995184720f, 0.941544056f, 0.831469595f, 0.671558976f, 0.471396744f,
0.242980182f, 0.707106769f, 0.941544056f, 0.773010433f, 0.514102757f,
0.195090324f, -0.146730468f, -0.471396744f, -0.740951121f, -0.923879504f,
-0.998795450f, -0.956940353f, -0.803207517f, -0.555570245f, -0.242980182f,
0.0980171412f, 0.427555084f, 0.707106769f, 0.903989315f, 0.995184720f,
0.970031261f, 0.831469595f, 0.595699310f, 0.290284663f, -0.0490676761f,
-0.382683426f, -0.671558976f, -0.881921291f, -0.989176512f, -0.980785251f,
-0.857728601f, -0.634393275f, -0.336889863f, 0.707106769f, 0.903989315f,
0.634393275f, 0.242980182f, -0.195090324f, -0.595699310f, -0.881921291f,
-0.998795450f, -0.923879504f, -0.671558976f, -0.290284663f, 0.146730468f,
0.555570245f, 0.857728601f, 0.995184720f, 0.941544056f, 0.707106769f,
0.336889863f, -0.0980171412f, -0.514102757f, -0.831469595f, -0.989176512f,
-0.956940353f, -0.740951121f, -0.382683426f, 0.0490676761f, 0.471396744f,
0.803207517f, 0.980785251f, 0.970031261f, 0.773010433f, 0.427555084f,
0.707106769f, 0.857728601f, 0.471396744f, -0.0490676761f, -0.555570245f,
-0.903989315f, -0.995184720f, -0.803207517f, -0.382683426f, 0.146730468f,
0.634393275f, 0.941544056f, 0.980785251f, 0.740951121f, 0.290284663f,
-0.242980182f, -0.707106769f, -0.970031261f, -0.956940353f, -0.671558976f,
-0.195090324f, 0.336889863f, 0.773010433f, 0.989176512f, 0.923879504f,
0.595699310f, 0.0980171412f, -0.427555084f, -0.831469595f, -0.998795450f,
-0.881921291f, -0.514102757f, 0.707106769f, 0.803207517f, 0.290284663f,
-0.336889863f, -0.831469595f, -0.998795450f, -0.773010433f, -0.242980182f,
0.382683426f, 0.857728601f, 0.995184720f, 0.740951121f, 0.195090324f,
-0.427555084f, -0.881921291f, -0.989176512f, -0.707106769f, -0.146730468f,
0.471396744f, 0.903989315f, 0.980785251f, 0.671558976f, 0.0980171412f,
-0.514102757f, -0.923879504f, -0.970031261f, -0.634393275f, -0.0490676761f,
0.555570245f, 0.941544056f, 0.956940353f, 0.595699310f, 0.707106769f,
0.740951121f, 0.0980171412f, -0.595699310f, -0.980785251f, -0.857728601f,
-0.290284663f, 0.427555084f, 0.923879504f, 0.941544056f, 0.471396744f,
-0.242980182f, -0.831469595f, -0.989176512f, -0.634393275f, 0.0490676761f,
0.707106769f, 0.998795450f, 0.773010433f, 0.146730468f, -0.555570245f,
-0.970031261f, -0.881921291f, -0.336889863f, 0.382683426f, 0.903989315f,
0.956940353f, 0.514102757f, -0.195090324f, -0.803207517f, -0.995184720f,
-0.671558976f, 0.707106769f, 0.671558976f, -0.0980171412f, -0.803207517f,
-0.980785251f, -0.514102757f, 0.290284663f, 0.903989315f, 0.923879504f,
0.336889863f, -0.471396744f, -0.970031261f, -0.831469595f, -0.146730468f,
0.634393275f, 0.998795450f, 0.707106769f, -0.0490676761f, -0.773010433f,
-0.989176512f, -0.555570245f, 0.242980182f, 0.881921291f, 0.941544056f,
0.382683426f, -0.427555084f, -0.956940353f, -0.857728601f, -0.195090324f,
0.595699310f, 0.995184720f, 0.740951121f, 0.707106769f, 0.595699310f,
-0.290284663f, -0.941544056f, -0.831469595f, -0.0490676761f, 0.773010433f,
0.970031261f, 0.382683426f, -0.514102757f, -0.995184720f, -0.671558976f,
0.195090324f, 0.903989315f, 0.881921291f, 0.146730468f, -0.707106769f,
-0.989176512f, -0.471396744f, 0.427555084f, 0.980785251f, 0.740951121f,
-0.0980171412f, -0.857728601f, -0.923879504f, -0.242980182f, 0.634393275f,
0.998795450f, 0.555570245f, -0.336889863f, -0.956940353f, -0.803207517f,
0.707106769f, 0.514102757f, -0.471396744f, -0.998795450f, -0.555570245f,
0.427555084f, 0.995184720f, 0.595699310f, -0.382683426f, -0.989176512f,
-0.634393275f, 0.336889863f, 0.980785251f, 0.671558976f, -0.290284663f,
-0.970031261f, -0.707106769f, 0.242980182f, 0.956940353f, 0.740951121f,
-0.195090324f, -0.941544056f, -0.773010433f, 0.146730468f, 0.923879504f,
0.803207517f, -0.0980171412f, -0.903989315f, -0.831469595f, 0.0490676761f,
0.881921291f, 0.857728601f, 0.707106769f, 0.427555084f, -0.634393275f,
-0.970031261f, -0.195090324f, 0.803207517f, 0.881921291f, -0.0490676761f,
-0.923879504f, -0.740951121f, 0.290284663f, 0.989176512f, 0.555570245f,
-0.514102757f, -0.995184720f, -0.336889863f, 0.707106769f, 0.941544056f,
0.0980171412f, -0.857728601f, -0.831469595f, 0.146730468f, 0.956940353f,
0.671558976f, -0.382683426f, -0.998795450f, -0.471396744f, 0.595699310f,
0.980785251f, 0.242980182f, -0.773010433f, -0.903989315f, 0.707106769f,
0.336889863f, -0.773010433f, -0.857728601f, 0.195090324f, 0.989176512f,
0.471396744f, -0.671558976f, -0.923879504f, 0.0490676761f, 0.956940353f,
0.595699310f, -0.555570245f, -0.970031261f, -0.0980171412f, 0.903989315f,
0.707106769f, -0.427555084f, -0.995184720f, -0.242980182f, 0.831469595f,
0.803207517f, -0.290284663f, -0.998795450f, -0.382683426f, 0.740951121f,
0.881921291f, -0.146730468f, -0.980785251f, -0.514102757f, 0.634393275f,
0.941544056f, 0.707106769f, 0.242980182f, -0.881921291f, -0.671558976f,
0.555570245f, 0.941544056f, -0.0980171412f, -0.989176512f, -0.382683426f,
0.803207517f, 0.773010433f, -0.427555084f, -0.980785251f, -0.0490676761f,
0.956940353f, 0.514102757f, -0.707106769f, -0.857728601f, 0.290284663f,
0.998795450f, 0.195090324f, -0.903989315f, -0.634393275f, 0.595699310f,
0.923879504f, -0.146730468f, -0.995184720f, -0.336889863f, 0.831469595f,
0.740951121f, -0.471396744f, -0.970031261f, 0.707106769f, 0.146730468f,
-0.956940353f, -0.427555084f, 0.831469595f, 0.671558976f, -0.634393275f,
-0.857728601f, 0.382683426f, 0.970031261f, -0.0980171412f, -0.998795450f,
-0.195090324f, 0.941544056f, 0.471396744f, -0.803207517f, -0.707106769f,
0.595699310f, 0.881921291f, -0.336889863f, -0.980785251f, 0.0490676761f,
0.995184720f, 0.242980182f, -0.923879504f, -0.514102757f, 0.773010433f,
0.740951121f, -0.555570245f, -0.903989315f, 0.290284663f, 0.989176512f,
0.707106769f, 0.0490676761f, -0.995184720f, -0.146730468f, 0.980785251f,
0.242980182f, -0.956940353f, -0.336889863f, 0.923879504f, 0.427555084f,
-0.881921291f, -0.514102757f, 0.831469595f, 0.595699310f, -0.773010433f,
-0.671558976f, 0.707106769f, 0.740951121f, -0.634393275f, -0.803207517f,
0.555570245f, 0.857728601f, -0.471396744f, -0.903989315f, 0.382683426f,
0.941544056f, -0.290284663f, -0.970031261f, 0.195090324f, 0.989176512f,
-0.0980171412f, -0.998795450f, 0.707106769f, -0.0490676761f, -0.995184720f,
0.146730468f, 0.980785251f, -0.242980182f, -0.956940353f, 0.336889863f,
0.923879504f, -0.427555084f, -0.881921291f, 0.514102757f, 0.831469595f,
-0.595699310f, -0.773010433f, 0.671558976f, 0.707106769f, -0.740951121f,
-0.634393275f, 0.803207517f, 0.555570245f, -0.857728601f, -0.471396744f,
0.903989315f, 0.382683426f, -0.941544056f, -0.290284663f, 0.970031261f,
0.195090324f, -0.989176512f, -0.0980171412f, 0.998795450f, 0.707106769f,
-0.146730468f, -0.956940353f, 0.427555084f, 0.831469595f, -0.671558976f,
-0.634393275f, 0.857728601f, 0.382683426f, -0.970031261f, -0.0980171412f,
0.998795450f, -0.195090324f, -0.941544056f, 0.471396744f, 0.803207517f,
-0.707106769f, -0.595699310f, 0.881921291f, 0.336889863f, -0.980785251f,
-0.0490676761f, 0.995184720f, -0.242980182f, -0.923879504f, 0.514102757f,
0.773010433f, -0.740951121f, -0.555570245f, 0.903989315f, 0.290284663f,
-0.989176512f, 0.707106769f, -0.242980182f, -0.881921291f, 0.671558976f,
0.555570245f, -0.941544056f, -0.0980171412f, 0.989176512f, -0.382683426f,
-0.803207517f, 0.773010433f, 0.427555084f, -0.980785251f, 0.0490676761f,
0.956940353f, -0.514102757f, -0.707106769f, 0.857728601f, 0.290284663f,
-0.998795450f, 0.195090324f, 0.903989315f, -0.634393275f, -0.595699310f,
0.923879504f, 0.146730468f, -0.995184720f, 0.336889863f, 0.831469595f,
-0.740951121f, -0.471396744f, 0.970031261f, 0.707106769f, -0.336889863f,
-0.773010433f, 0.857728601f, 0.195090324f, -0.989176512f, 0.471396744f,
0.671558976f, -0.923879504f, -0.0490676761f, 0.956940353f, -0.595699310f,
-0.555570245f, 0.970031261f, -0.0980171412f, -0.903989315f, 0.707106769f,
0.427555084f, -0.995184720f, 0.242980182f, 0.831469595f, -0.803207517f,
-0.290284663f, 0.998795450f, -0.382683426f, -0.740951121f, 0.881921291f,
0.146730468f, -0.980785251f, 0.514102757f, 0.634393275f, -0.941544056f,
0.707106769f, -0.427555084f, -0.634393275f, 0.970031261f, -0.195090324f,
-0.803207517f, 0.881921291f, 0.0490676761f, -0.923879504f, 0.740951121f,
0.290284663f, -0.989176512f, 0.555570245f, 0.514102757f, -0.995184720f,
0.336889863f, 0.707106769f, -0.941544056f, 0.0980171412f, 0.857728601f,
-0.831469595f, -0.146730468f, 0.956940353f, -0.671558976f, -0.382683426f,
0.998795450f, -0.471396744f, -0.595699310f, 0.980785251f, -0.242980182f,
-0.773010433f, 0.903989315f, 0.707106769f, -0.514102757f, -0.471396744f,
0.998795450f, -0.555570245f, -0.427555084f, 0.995184720f, -0.595699310f,
-0.382683426f, 0.989176512f, -0.634393275f, -0.336889863f, 0.980785251f,
-0.671558976f, -0.290284663f, 0.970031261f, -0.707106769f, -0.242980182f,
0.956940353f, -0.740951121f, -0.195090324f, 0.941544056f, -0.773010433f,
-0.146730468f, 0.923879504f, -0.803207517f, -0.0980171412f, 0.903989315f,
-0.831469595f, -0.0490676761f, 0.881921291f, -0.857728601f, 0.707106769f,
-0.595699310f, -0.290284663f, 0.941544056f, -0.831469595f, 0.0490676761f,
0.773010433f, -0.970031261f, 0.382683426f, 0.514102757f, -0.995184720f,
0.671558976f, 0.195090324f, -0.903989315f, 0.881921291f, -0.146730468f,
-0.707106769f, 0.989176512f, -0.471396744f, -0.427555084f, 0.980785251f,
-0.740951121f, -0.0980171412f, 0.857728601f, -0.923879504f, 0.242980182f,
0.634393275f, -0.998795450f, 0.555570245f, 0.336889863f, -0.956940353f,
0.803207517f, 0.707106769f, -0.671558976f, -0.0980171412f, 0.803207517f,
-0.980785251f, 0.514102757f, 0.290284663f, -0.903989315f, 0.923879504f,
-0.336889863f, -0.471396744f, 0.970031261f, -0.831469595f, 0.146730468f,
0.634393275f, -0.998795450f, 0.707106769f, 0.0490676761f, -0.773010433f,
0.989176512f, -0.555570245f, -0.242980182f, 0.881921291f, -0.941544056f,
0.382683426f, 0.427555084f, -0.956940353f, 0.857728601f, -0.195090324f,
-0.595699310f, 0.995184720f, -0.740951121f, 0.707106769f, -0.740951121f,
0.0980171412f, 0.595699310f, -0.980785251f, 0.857728601f, -0.290284663f,
-0.427555084f, 0.923879504f, -0.941544056f, 0.471396744f, 0.242980182f,
-0.831469595f, 0.989176512f, -0.634393275f, -0.0490676761f, 0.707106769f,
-0.998795450f, 0.773010433f, -0.146730468f, -0.555570245f, 0.970031261f,
-0.881921291f, 0.336889863f, 0.382683426f, -0.903989315f, 0.956940353f,
-0.514102757f, -0.195090324f, 0.803207517f, -0.995184720f, 0.671558976f,
0.707106769f, -0.803207517f, 0.290284663f, 0.336889863f, -0.831469595f,
0.998795450f, -0.773010433f, 0.242980182f, 0.382683426f, -0.857728601f,
0.995184720f, -0.740951121f, 0.195090324f, 0.427555084f, -0.881921291f,
0.989176512f, -0.707106769f, 0.146730468f, 0.471396744f, -0.903989315f,
0.980785251f, -0.671558976f, 0.0980171412f, 0.514102757f, -0.923879504f,
0.970031261f, -0.634393275f, 0.0490676761f, 0.555570245f, -0.941544056f,
0.956940353f, -0.595699310f, 0.707106769f, -0.857728601f, 0.471396744f,
0.0490676761f, -0.555570245f, 0.903989315f, -0.995184720f, 0.803207517f,
-0.382683426f, -0.146730468f, 0.634393275f, -0.941544056f, 0.980785251f,
-0.740951121f, 0.290284663f, 0.242980182f, -0.707106769f, 0.970031261f,
-0.956940353f, 0.671558976f, -0.195090324f, -0.336889863f, 0.773010433f,
-0.989176512f, 0.923879504f, -0.595699310f, 0.0980171412f, 0.427555084f,
-0.831469595f, 0.998795450f, -0.881921291f, 0.514102757f, 0.707106769f,
-0.903989315f, 0.634393275f, -0.242980182f, -0.195090324f, 0.595699310f,
-0.881921291f, 0.998795450f, -0.923879504f, 0.671558976f, -0.290284663f,
-0.146730468f, 0.555570245f, -0.857728601f, 0.995184720f, -0.941544056f,
0.707106769f, -0.336889863f, -0.0980171412f, 0.514102757f, -0.831469595f,
0.989176512f, -0.956940353f, 0.740951121f, -0.382683426f, -0.0490676761f,
0.471396744f, -0.803207517f, 0.980785251f, -0.970031261f, 0.773010433f,
-0.427555084f, 0.707106769f, -0.941544056f, 0.773010433f, -0.514102757f,
0.195090324f, 0.146730468f, -0.471396744f, 0.740951121f, -0.923879504f,
0.998795450f, -0.956940353f, 0.803207517f, -0.555570245f, 0.242980182f,
0.0980171412f, -0.427555084f, 0.707106769f, -0.903989315f, 0.995184720f,
-0.970031261f, 0.831469595f, -0.595699310f, 0.290284663f, 0.0490676761f,
-0.382683426f, 0.671558976f, -0.881921291f, 0.989176512f, -0.980785251f,
0.857728601f, -0.634393275f, 0.336889863f, 0.707106769f, -0.970031261f,
0.881921291f, -0.740951121f, 0.555570245f, -0.336889863f, 0.0980171412f,
0.146730468f, -0.382683426f, 0.595699310f, -0.773010433f, 0.903989315f,
-0.980785251f, 0.998795450f, -0.956940353f, 0.857728601f, -0.707106769f,
0.514102757f, -0.290284663f, 0.0490676761f, 0.195090324f, -0.427555084f,
0.634393275f, -0.803207517f, 0.923879504f, -0.989176512f, 0.995184720f,
-0.941544056f, 0.831469595f, -0.671558976f, 0.471396744f, -0.242980182f,
0.707106769f, -0.989176512f, 0.956940353f, -0.903989315f, 0.831469595f,
-0.740951121f, 0.634393275f, -0.514102757f, 0.382683426f, -0.242980182f,
0.0980171412f, 0.0490676761f, -0.195090324f, 0.336889863f, -0.471396744f,
0.595699310f, -0.707106769f, 0.803207517f, -0.881921291f, 0.941544056f,
-0.980785251f, 0.998795450f, -0.995184720f, 0.970031261f, -0.923879504f,
0.857728601f, -0.773010433f, 0.671558976f, -0.555570245f, 0.427555084f,
-0.290284663f, 0.146730468f, 0.707106769f, -0.998795450f, 0.995184720f,
-0.989176512f, 0.980785251f, -0.970031261f, 0.956940353f, -0.941544056f,
0.923879504f, -0.903989315f, 0.881921291f, -0.857728601f, 0.831469595f,
-0.803207517f, 0.773010433f, -0.740951121f, 0.707106769f, -0.671558976f,
0.634393275f, -0.595699310f, 0.555570245f, -0.514102757f, 0.471396744f,
-0.427555084f, 0.382683426f, -0.336889863f, 0.290284663f, -0.242980182f,
0.195090324f, -0.146730468f, 0.0980171412f, -0.0490676761f, };
