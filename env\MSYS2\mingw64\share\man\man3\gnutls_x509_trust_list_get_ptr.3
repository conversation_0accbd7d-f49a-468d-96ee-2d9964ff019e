.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_get_ptr" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_get_ptr \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "void * gnutls_x509_trust_list_get_ptr(gnutls_x509_trust_list_t " tlist ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t tlist" 12
is a \fBgnutls_x509_trust_list_t\fP type.
.SH "DESCRIPTION"
Get user pointer for tlist. Useful in callback function
gnutls_x509_trust_list_set_getissuer_function.
This is the pointer set with \fBgnutls_x509_trust_list_set_ptr()\fP.
.SH "RETURNS"
the user given pointer from the tlist structure, or
\fBNULL\fP if it was never set.
.SH "SINCE"
3.7.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
