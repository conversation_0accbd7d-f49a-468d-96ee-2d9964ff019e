/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

import "mfobjects.idl";
import "mftransform.idl";

cpp_quote("#include <windef.h>")

typedef enum MFSESSION_SETTOPOLOGY_FLAGS {
    MFSESSION_SETTOPOLOGY_IMMEDIATE     = 0x1,
    MFSESSION_SETTOPOLOGY_NORESOLUTION  = 0x2,
    MFSESSION_SETTOPOLOGY_CLEAR_CURRENT = 0x4
} MFSESSION_SETTOPOLOGY_FLAGS;

typedef enum MFSESSION_GETFULLTOPOLOGY_FLAGS {
    M<PERSON>ESSION_GETFULLTOPOLOGY_CURRENT = 0x1
} MFSESSION_GETFULLTOPOLOGY_FLAGS;

typedef enum MFPMPSESSION_CREATION_FLAGS {
    MFPMPSESSION_UNPROTECTED_PROCESS = 0x1,
    MFPMPSESSION_IN_PROCESS = 0x2
} M<PERSON>MPSESSION_CREATION_FLAGS;

typedef unsigned __int64 TOPOID;

typedef enum MF_TOPOLOGY_TYPE {
    MF_TOPOLOGY_OUTPUT_NODE,
    MF_TOPOLOGY_SOURCESTREAM_NODE,
    MF_TOPOLOGY_TRANSFORM_NODE,
    MF_TOPOLOGY_TEE_NODE,
    MF_TOPOLOGY_MAX = 0xffffffff
} MF_TOPOLOGY_TYPE;

[
    object,
    uuid(83cf873a-f6da-4bc8-823f-bacfd55dc430),
]
interface IMFTopologyNode : IMFAttributes
{
    HRESULT SetObject([in] IUnknown *pObject);
    HRESULT GetObject([out] IUnknown **ppObject);
    HRESULT GetNodeType([out] MF_TOPOLOGY_TYPE *pType);
    HRESULT GetTopoNodeID([out] TOPOID *pID);
    HRESULT SetTopoNodeID([in] TOPOID ullTopoID);
    HRESULT GetInputCount([out] DWORD *pcInputs);
    HRESULT GetOutputCount([out] DWORD *pcOutputs);

    [local]
    HRESULT ConnectOutput([in] DWORD dwOutputIndex, [in] IMFTopologyNode* pDownstreamNode,
            [in] DWORD dwInputIndexOnDownstreamNode);
    [local] HRESULT DisconnectOutput([in] DWORD dwOutputIndex);
    HRESULT GetInput([in] DWORD dwInputIndex, [out] IMFTopologyNode** ppUpstreamNode,
            [out] DWORD *pdwOutputIndexOnUpstreamNode);
    HRESULT GetOutput([in] DWORD dwOutputIndex, [out] IMFTopologyNode** ppDownstreamNode,
            [out] DWORD* pdwInputIndexOnDownstreamNode);
    [local] HRESULT SetOutputPrefType([in] DWORD dwOutputIndex, [in] IMFMediaType *pType);

    [local] HRESULT GetOutputPrefType([in] DWORD dwOutputIndex, [out] IMFMediaType **ppType);
    [call_as(GetOutputPrefType)] HRESULT RemoteGetOutputPrefType([in] DWORD dwOutputIndex,
                                         [out] DWORD *pcbData, [out, size_is(,*pcbData)] BYTE **ppbData);

    [local] HRESULT SetInputPrefType([in] DWORD dwInputIndex, [in] IMFMediaType *pType);

    [local] HRESULT GetInputPrefType([in] DWORD dwInputIndex, [out] IMFMediaType **ppType);
    [call_as(GetInputPrefType)] HRESULT RemoteGetInputPrefType( [in] DWORD dwInputIndex,
                                        [out] DWORD *pcbData, [out, size_is(,*pcbData)] BYTE **ppbData);

    HRESULT CloneFrom([in] IMFTopologyNode *pNode);
}

[
    object,
    uuid(83cf873a-f6da-4bc8-823f-bacfd55dc433),
]
interface IMFTopology : IMFAttributes
{
    HRESULT GetTopologyID([out] TOPOID *pID);
    [local] HRESULT AddNode([in] IMFTopologyNode *pNode);
    [local] HRESULT RemoveNode([in] IMFTopologyNode *pNode);
    HRESULT GetNodeCount([out] WORD *pwNodes);
    HRESULT GetNode([in] WORD wIndex, [out] IMFTopologyNode **ppNode);
    [local] HRESULT Clear();
    HRESULT CloneFrom([in] IMFTopology *pTopology);
    HRESULT GetNodeByID([in] TOPOID qwTopoNodeID, [out] IMFTopologyNode **ppNode);
    HRESULT GetSourceNodeCollection([out] IMFCollection **ppCollection);
    HRESULT GetOutputNodeCollection([out] IMFCollection **ppCollection);
}

[
    object,
    uuid(fa993888-4383-415a-a930-dd472a8cf6f7)
]
interface IMFGetService : IUnknown
{
    HRESULT GetService([in] REFGUID guidService, [in] REFIID riid, [out, iid_is(riid)] LPVOID *ppvObject);
}

typedef LONGLONG MFTIME;

typedef enum _MF_CLOCK_STATE {
    MFCLOCK_STATE_INVALID,
    MFCLOCK_STATE_RUNNING,
    MFCLOCK_STATE_STOPPED,
    MFCLOCK_STATE_PAUSED
} MF_CLOCK_STATE, MFCLOCK_STATE;

typedef struct _MFCLOCK_PROPERTIES {
    unsigned __int64 qwCorrelationRate;
    GUID             guidClockId;
    DWORD            dwClockFlags;
    unsigned __int64 qwClockFrequency;
    DWORD            dwClockTolerance;
    DWORD            dwClockJitter;
} MFCLOCK_PROPERTIES;

[
    object,
    uuid(2eb1e945-18b8-4139-9b1a-d5d584818530),
]
interface IMFClock : IUnknown
{
    HRESULT GetClockCharacteristics([out] DWORD *pdwCharacteristics);
    HRESULT GetCorrelatedTime([in] DWORD dwReserved, [out] LONGLONG *pllClockTime, [out] MFTIME *phnsSystemTime);
    HRESULT GetContinuityKey([out] DWORD *pdwContinuityKey);
    HRESULT GetState([in] DWORD dwReserved, [out] MFCLOCK_STATE *peClockState);
    HRESULT GetProperties([out] MFCLOCK_PROPERTIES *pClockProperties);
}

cpp_quote("#define SHA_HASH_LEN 20")
cpp_quote("#define STR_HASH_LEN (SHA_HASH_LEN*2+3)")

cpp_quote("typedef struct _MFRR_COMPONENT_HASH_INFO {")
cpp_quote("  DWORD ulReason;")
cpp_quote("  WCHAR rgHeaderHash[STR_HASH_LEN];")
cpp_quote("  WCHAR rgPublicKeyHash[STR_HASH_LEN];")
cpp_quote("  WCHAR wszName[MAX_PATH];")
cpp_quote("} MFRR_COMPONENT_HASH_INFO, *PMFRR_COMPONENT_HASH_INFO;")

cpp_quote("EXTERN_GUID(MF_PD_DURATION, 0x6c990d33,0xbb8e,0x477a,0x85,0x98,0xd,0x5d,0x96,0xfc,0xd8,0x8a);")

typedef enum _MF_CONNECT_METHOD {
    MF_CONNECT_DIRECT                            = 0x00000000,
    MF_CONNECT_ALLOW_CONVERTER                   = 0x00000001,
    MF_CONNECT_ALLOW_DECODER                     = 0x00000003,
    MF_CONNECT_RESOLVE_INDEPENDENT_OUTPUTTYPES   = 0x00000004,
    MF_CONNECT_AS_OPTIONAL                       = 0x00010000,
    MF_CONNECT_AS_OPTIONAL_BRANCH                = 0x00020000
} MF_CONNECT_METHOD;

[
    object,
    uuid(90377834-21d0-4dee-8214-ba2e3e6c1127),
]
interface IMFMediaSession : IMFMediaEventGenerator
{
    HRESULT SetTopology([in] DWORD dwSetTopologyFlags, [in] IMFTopology *pTopology);
    HRESULT ClearTopologies();
    HRESULT Start([in, unique] const GUID *pguidTimeFormat, [in, unique] const PROPVARIANT *pvarStartPosition);
    HRESULT Pause();
    HRESULT Stop();
    HRESULT Close();
    HRESULT Shutdown();
    HRESULT GetClock([out] IMFClock **ppClock);
    HRESULT GetSessionCapabilities([out] DWORD *pdwCaps);
    HRESULT GetFullTopology([in] DWORD dwGetFullTopologyFlags, [in] TOPOID TopoId, [out] IMFTopology **ppFullTopology);
}

[
    object,
    uuid(e93dcf6c-4b07-4e1e-8123-aa16ed6eadf5),
]
interface IMFMediaTypeHandler : IUnknown
{
    [local] HRESULT IsMediaTypeSupported([in] IMFMediaType *pMediaType, [out] IMFMediaType **ppMediaType);
    [local] HRESULT GetMediaTypeCount([out] DWORD *pdwTypeCount);
    [local] HRESULT GetMediaTypeByIndex([in] DWORD dwIndex, [out] IMFMediaType **ppType);
    [local] HRESULT SetCurrentMediaType([in] IMFMediaType *pMediaType);

    [local]
    HRESULT GetCurrentMediaType([out] IMFMediaType **ppMediaType);
    [call_as(GetCurrentMediaType)]
    HRESULT RemoteGetCurrentMediaType([out, size_is(,*pcbData)] BYTE **ppbData, [out] DWORD *pcbData);

    HRESULT GetMajorType([out] GUID *pguidMajorType);
}

[
    object,
    uuid(56c03d9c-9dbb-45f5-ab4b-d80f47c05938),
]
interface IMFStreamDescriptor : IMFAttributes
{
    HRESULT GetStreamIdentifier([out] DWORD *pdwStreamIdentifier);
    HRESULT GetMediaTypeHandler([out] IMFMediaTypeHandler **ppMediaTypeHandler);
}

[
    object,
    uuid(03cb2711-24d7-4db6-a17f-f3a7a479a536),
]
interface IMFPresentationDescriptor : IMFAttributes
{
    HRESULT GetStreamDescriptorCount([out] DWORD *pdwDescriptorCount);
    HRESULT GetStreamDescriptorByIndex([in] DWORD dwIndex, [out] BOOL *pfSelected, [out] IMFStreamDescriptor **ppDescriptor);
    HRESULT SelectStream([in] DWORD dwDescriptorIndex);
    HRESULT DeselectStream([in] DWORD dwDescriptorIndex);
    HRESULT Clone([out] IMFPresentationDescriptor **ppPresentationDescriptor);
}

[
    object,
    uuid(279a808d-aec7-40c8-9c6b-a6b492c78a66),
]
interface IMFMediaSource : IMFMediaEventGenerator
{
    HRESULT GetCharacteristics([out] DWORD *pdwCharacteristics);

    [local]
    HRESULT CreatePresentationDescriptor([out] IMFPresentationDescriptor **ppPresentationDescriptor);
    [call_as(CreatePresentationDescriptor)]
    HRESULT RemoteCreatePresentationDescriptor(
            [out] DWORD *pcbPD,
            [out, size_is(,*pcbPD)] BYTE **pbPD,
            [out] IMFPresentationDescriptor **ppRemotePD);

    HRESULT Start(
            [in] IMFPresentationDescriptor *pPresentationDescriptor,
            [in, unique] const GUID *pguidTimeFormat,
            [in, unique] const PROPVARIANT *pvarStartPosition);

    HRESULT Stop();
    HRESULT Pause();
    HRESULT Shutdown();
}

cpp_quote("#if (WINVER >= _WIN32_WINNT_WIN8)")
[object, uuid(3c9b2eb9-86d5-4514-a394-f56664f9f0d8)]
interface IMFMediaSourceEx : IMFMediaSource
{
  HRESULT GetSourceAttributes([out] IMFAttributes **ppAttributes);
  HRESULT GetStreamAttributes([in] DWORD dwStreamIdentifier, [out] IMFAttributes **ppAttributes);
  HRESULT SetD3DManager([in] IUnknown *pManager);
}
cpp_quote("EXTERN_GUID(MF_SOURCE_STREAM_SUPPORTS_HW_CONNECTION, 0xa38253aa, 0x6314, 0x42fd, 0xa3, 0xce, 0xbb, 0x27, 0xb6, 0x85, 0x99, 0x46);")

cpp_quote("#endif /* (WINVER >= _WIN32_WINNT_WIN8) */")

typedef [public] struct _MF_LEAKY_BUCKET_PAIR {
    DWORD dwBitrate;
    DWORD msBufferWindow;
} MF_LEAKY_BUCKET_PAIR;

typedef [public] struct _MFBYTESTREAM_BUFFERING_PARAMS {
    QWORD                cbTotalFileSize;
    QWORD                cbPlayableDataSize;
    MF_LEAKY_BUCKET_PAIR *prgBuckets;
    DWORD                cBuckets;
    QWORD                qwNetBufferingTime;
    QWORD                qwExtraBufferingTimeDuringSeek;
    QWORD                qwPlayDuration;
    float                dRate;
} MFBYTESTREAM_BUFFERING_PARAMS;

[
    object,
    uuid(6d66d782-1d4f-4db7-8c63-cb8c77f1ef5e),
    local
]
interface IMFByteStreamBuffering : IUnknown
{
    HRESULT SetBufferingParams([in] MFBYTESTREAM_BUFFERING_PARAMS *pParams);
    HRESULT EnableBuffering([in] BOOL fEnable);
    HRESULT StopBuffering();
}

[
    object,
    uuid(F6696E82-74F7-4f3d-A178-8A5E09C3659F),
]
interface IMFClockStateSink : IUnknown
{
    HRESULT OnClockStart([in] MFTIME hnsSystemTime,[in] LONGLONG llClockStartOffset);
    HRESULT OnClockStop([in] MFTIME hnsSystemTime);
    HRESULT OnClockPause([in] MFTIME hnsSystemTime);
    HRESULT OnClockRestart([in] MFTIME hnsSystemTime);
    HRESULT OnClockSetRate([in] MFTIME hnsSystemTime, [in] float flRate);
}

cpp_quote("HRESULT WINAPI MFRequireProtectedEnvironment(IMFPresentationDescriptor *pPresentationDescriptor);")
cpp_quote("HRESULT WINAPI MFSerializePresentationDescriptor(IMFPresentationDescriptor *pPD,DWORD *pcbData,BYTE **ppbData);")

typedef DWORD MFSequencerElementId;

typedef enum _MFCLOCK_CHARACTERISTICS_FLAGS
{
    MFCLOCK_CHARACTERISTICS_FLAG_FREQUENCY_10MHZ   = 0x2,
    MFCLOCK_CHARACTERISTICS_FLAG_ALWAYS_RUNNING    = 0x4,
    MFCLOCK_CHARACTERISTICS_FLAG_IS_SYSTEM_CLOCK   = 0x8
} MFCLOCK_CHARACTERISTICS_FLAGS;

cpp_quote("#if (WINVER >= 0x0601)")

typedef enum _MF_QUALITY_ADVISE_FLAGS
{
    MF_QUALITY_CANNOT_KEEP_UP = 0x1
} MF_QUALITY_ADVISE_FLAGS;

cpp_quote("#endif /*(WINVER >= 0x0601)*/")

typedef enum _MF_QUALITY_DROP_MODE
{
    MF_DROP_MODE_NONE = 0x0,
    MF_DROP_MODE_1    = 0x1,
    MF_DROP_MODE_2    = 0x2,
    MF_DROP_MODE_3    = 0x3,
    MF_DROP_MODE_4    = 0x4,
    MF_DROP_MODE_5    = 0x5,
    MF_NUM_DROP_MODES = 0x6
} MF_QUALITY_DROP_MODE;

typedef enum _MF_QUALITY_LEVEL
{
    MF_QUALITY_NORMAL         = 0x0,
    MF_QUALITY_NORMAL_MINUS_1 = 0x1,
    MF_QUALITY_NORMAL_MINUS_2 = 0x2,
    MF_QUALITY_NORMAL_MINUS_3 = 0x3,
    MF_QUALITY_NORMAL_MINUS_4 = 0x4,
    MF_QUALITY_NORMAL_MINUS_5 = 0x5,
    MF_NUM_QUALITY_LEVELS     = 0x6
} MF_QUALITY_LEVEL;

typedef enum _MF_TOPOLOGY_RESOLUTION_STATUS_FLAGS
{
    MF_TOPOLOGY_RESOLUTION_SUCCEEDED            = 0x00000000,
    MF_OPTIONAL_NODE_REJECTED_MEDIA_TYPE        = 0x00000001,
    MF_OPTIONAL_NODE_REJECTED_PROTECTED_PROCESS = 0x00000002
} MF_TOPOLOGY_RESOLUTION_STATUS_FLAGS;

typedef enum _MF_TOPONODE_DRAIN_MODE
{
    MF_TOPONODE_DRAIN_DEFAULT,
    MF_TOPONODE_DRAIN_ALWAYS,
    MF_TOPONODE_DRAIN_NEVER
} MF_TOPONODE_DRAIN_MODE;

typedef enum _MF_TOPONODE_FLUSH_MODE
{
    MF_TOPONODE_FLUSH_ALWAYS,
    MF_TOPONODE_FLUSH_SEEK,
    MF_TOPONODE_FLUSH_NEVER
} MF_TOPONODE_FLUSH_MODE;

cpp_quote("#if (WINVER >= 0x0601)")

typedef enum _MF_TRANSCODE_TOPOLOGY_MODE_FLAGS
{
  MF_TRANSCODE_TOPOLOGYMODE_SOFTWARE_ONLY    = 0,
  MF_TRANSCODE_TOPOLOGYMODE_HARDWARE_ALLOWED = 1
} MF_TRANSCODE_TOPOLOGYMODE_FLAGS;

cpp_quote("#endif")

cpp_quote("EXTERN_GUID(MF_TRANSCODE_CONTAINERTYPE, 0x150ff23f, 0x4abc, 0x478b, 0xac, 0x4f, 0xe1, 0x91, 0x6f, 0xba, 0x1c, 0xca);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_ASF, 0x430f6f6e, 0xb6bf, 0x4fc1, 0xa0, 0xbd, 0x9e, 0xe4, 0x6e, 0xee, 0x2a, 0xfb);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_MPEG4, 0xdc6cd05d, 0xb9d0, 0x40ef, 0xbd, 0x35, 0xfa, 0x62, 0x2c, 0x1a, 0xb2, 0x8a);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_MP3, 0xe438b912, 0x83f1, 0x4de6, 0x9e, 0x3a, 0x9f, 0xfb, 0xc6, 0xdd, 0x24, 0xd1);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_FLAC, 0x31344aa3, 0x05a9, 0x42b5, 0x90, 0x1b, 0x8e, 0x9d, 0x42, 0x57, 0xf7, 0x5e);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_3GP, 0x34c50167, 0x4472, 0x4f34, 0x9e, 0xa0, 0xc4, 0x9f, 0xba, 0xcf, 0x03, 0x7d);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_AC3, 0x6d8d91c3, 0x8c91, 0x4ed1, 0x87, 0x42, 0x8c, 0x34, 0x7d, 0x5b, 0x44, 0xd0);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_ADTS, 0x132fd27d, 0x0f02, 0x43de, 0xa3, 0x01, 0x38, 0xfb, 0xbb, 0xb3, 0x83, 0x4e);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_MPEG2, 0xbfc2dbf9, 0x7bb4, 0x4f8f, 0xaf, 0xde, 0xe1, 0x12, 0xc4, 0x4b, 0xa8, 0x82);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_WAVE, 0x64c3453c, 0x0f26, 0x4741, 0xbe, 0x63, 0x87, 0xbd, 0xf8, 0xbb, 0x93, 0x5b);")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_AVI, 0x7edfe8af, 0x402f, 0x4d76, 0xa3, 0x3c, 0x61, 0x9f, 0xd1, 0x57, 0xd0, 0xf1);")
cpp_quote("#if (WINVER >= _WIN32_WINNT_WIN8)")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_FMPEG4, 0x9ba876f1, 0x419f, 0x4b77, 0xa1, 0xe0, 0x35, 0x95, 0x9d, 0x9d, 0x40, 0x4);")
cpp_quote("#endif /* (WINVER >= _WIN32_WINNT_WIN8) */")
cpp_quote("EXTERN_GUID(MFTranscodeContainerType_AMR, 0x25d5ad3, 0x621a, 0x475b, 0x96, 0x4d, 0x66, 0xb1, 0xc8, 0x24, 0xf0, 0x79);")

typedef enum
{
    MF_LICENSE_URL_UNTRUSTED,
    MF_LICENSE_URL_TRUSTED,
    MF_LICENSE_URL_TAMPERED
} MF_URL_TRUST_STATUS;

typedef enum _MFCLOCK_RELATIONAL_FLAGS
{
    MFCLOCK_RELATIONAL_FLAG_JITTER_NEVER_AHEAD = 0x1
} MFCLOCK_RELATIONAL_FLAGS;

typedef enum _MFMEDIASOURCE_CHARACTERISTICS
{
    MFMEDIASOURCE_IS_LIVE                    = 0x1,
    MFMEDIASOURCE_CAN_SEEK                   = 0x2,
    MFMEDIASOURCE_CAN_PAUSE                  = 0x4,
    MFMEDIASOURCE_HAS_SLOW_SEEK              = 0x8,
    MFMEDIASOURCE_HAS_MULTIPLE_PRESENTATIONS = 0x10,
    MFMEDIASOURCE_CAN_SKIPFORWARD            = 0x20,
    MFMEDIASOURCE_CAN_SKIPBACKWARD           = 0x40
} MFMEDIASOURCE_CHARACTERISTICS;

typedef enum _MFNET_PROXYSETTINGS
{
    MFNET_PROXYSETTING_NONE    = 0,
    MFNET_PROXYSETTING_MANUAL  = 1,
    MFNET_PROXYSETTING_AUTO    = 2,
    MFNET_PROXYSETTING_BROWSER = 3
} MFNET_PROXYSETTINGS;

typedef enum _MFNetAuthenticationFlags
{
    MFNET_AUTHENTICATION_PROXY          = 0x00000001,
    MFNET_AUTHENTICATION_CLEAR_TEXT     = 0x00000002,
    MFNET_AUTHENTICATION_LOGGED_ON_USER = 0x00000004
} MFNetAuthenticationFlags;

typedef enum _MFNetCredentialOptions
{
    MFNET_CREDENTIAL_SAVE             = 0x00000001,
    MFNET_CREDENTIAL_DONT_CACHE       = 0x00000002,
    MFNET_CREDENTIAL_ALLOW_CLEAR_TEXT = 0x00000004
} MFNetCredentialOptions;

typedef enum _MFNetCredentialRequirements {
    REQUIRE_PROMPT        = 0x00000001,
    REQUIRE_SAVE_SELECTED = 0x00000002
} MFNetCredentialRequirements;

typedef enum _MFNETSOURCE_CACHE_STATE
{
    MFNETSOURCE_CACHE_UNAVAILABLE,
    MFNETSOURCE_CACHE_ACTIVE_WRITING,
    MFNETSOURCE_CACHE_ACTIVE_COMPLETE
} MFNETSOURCE_CACHE_STATE;

typedef enum _MFNETSOURCE_PROTOCOL_TYPE
{
    MFNETSOURCE_UNDEFINED = 0x0,
    MFNETSOURCE_HTTP      = 0x1,
    MFNETSOURCE_RTSP      = 0x2,
    MFNETSOURCE_FILE      = 0x3,
    MFNETSOURCE_MULTICAST = 0x4
} MFNETSOURCE_PROTOCOL_TYPE;

typedef enum _MFNETSOURCE_STATISTICS_IDS
{
    MFNETSOURCE_RECVPACKETS_ID = 0,
    MFNETSOURCE_LOSTPACKETS_ID,
    MFNETSOURCE_RESENDSREQUESTED_ID,
    MFNETSOURCE_RESENDSRECEIVED_ID,
    MFNETSOURCE_RECOVEREDBYECCPACKETS_ID,
    MFNETSOURCE_RECOVEREDBYRTXPACKETS_ID,
    MFNETSOURCE_OUTPACKETS_ID,
    MFNETSOURCE_RECVRATE_ID,
    MFNETSOURCE_AVGBANDWIDTHBPS_ID,
    MFNETSOURCE_BYTESRECEIVED_ID,
    MFNETSOURCE_PROTOCOL_ID,
    MFNETSOURCE_TRANSPORT_ID,
    MFNETSOURCE_CACHE_STATE_ID,
    MFNETSOURCE_LINKBANDWIDTH_ID,
    MFNETSOURCE_CONTENTBITRATE_ID,
    MFNETSOURCE_SPEEDFACTOR_ID,
    MFNETSOURCE_BUFFERSIZE_ID,
    MFNETSOURCE_BUFFERPROGRESS_ID,
    MFNETSOURCE_LASTBWSWITCHTS_ID,
    MFNETSOURCE_SEEKRANGESTART_ID,
    MFNETSOURCE_SEEKRANGEEND_ID,
    MFNETSOURCE_BUFFERINGCOUNT_ID,
    MFNETSOURCE_INCORRECTLYSIGNEDPACKETS_ID,
    MFNETSOURCE_SIGNEDSESSION_ID,
    MFNETSOURCE_MAXBITRATE_ID,
    MFNETSOURCE_RECEPTION_QUALITY_ID,
    MFNETSOURCE_RECOVEREDPACKETS_ID,
    MFNETSOURCE_VBR_ID,
    MFNETSOURCE_DOWNLOADPROGRESS_ID
} MFNETSOURCE_STATISTICS_IDS;

typedef enum _MFNETSOURCE_TRANSPORT_TYPE
{
    MFNETSOURCE_UDP,
    MFNETSOURCE_TCP
} MFNETSOURCE_TRANSPORT_TYPE;

typedef enum MF_OBJECT_TYPE
{
    MF_OBJECT_MEDIASOURCE,
    MF_OBJECT_BYTESTREAM,
    MF_OBJECT_INVALID
} MF_OBJECT_TYPE;

typedef enum _MFPOLICYMANAGER_ACTION
{
    PEACTION_NO        = 0,
    PEACTION_PLAY      = 1,
    PEACTION_COPY      = 2,
    PEACTION_EXPORT    = 3,
    PEACTION_EXTRACT   = 4,
    PEACTION_RESERVED1 = 5,
    PEACTION_RESERVED2 = 6,
    PEACTION_RESERVED3 = 7,
    PEACTION_LAST      = 7
} MFPOLICYMANAGER_ACTION;

typedef enum _MFRATE_DIRECTION
{
    MFRATE_FORWARD,
    MFRATE_REVERSE
} MFRATE_DIRECTION;

typedef enum _MFSequencerTopologyFlags
{
    SequencerTopologyFlags_Last = 0x00000001
} MFSequencerTopologyFlags;

typedef enum _MFSHUTDOWN_STATUS
{
    MFSHUTDOWN_INITIATED,
    MFSHUTDOWN_COMPLETED
} MFSHUTDOWN_STATUS;

typedef enum MFSTREAMSINK_MARKER_TYPE
{
    MFSTREAMSINK_MARKER_DEFAULT,
    MFSTREAMSINK_MARKER_ENDOFSEGMENT,
    MFSTREAMSINK_MARKER_TICK,
    MFSTREAMSINK_MARKER_EVENT
} MFSTREAMSINK_MARKER_TYPE;

typedef enum MFTIMER_FLAGS
{
    MFTIMER_RELATIVE = 0x00000001
} MFTIMER_FLAGS;

cpp_quote("#if (WINVER >= 0x0601)")

typedef enum MFTOPOLOGY_DXVA_MODE
{
    MFTOPOLOGY_DXVA_DEFAULT = 0,
    MFTOPOLOGY_DXVA_NONE    = 1,
    MFTOPOLOGY_DXVA_FULL    = 2
} MFTOPOLOGY_DXVA_MODE;

typedef enum MFTOPOLOGY_HARDWARE_MODE
{
    MFTOPOLOGY_HWMODE_SOFTWARE_ONLY = 0,
    MFTOPOLOGY_HWMODE_USE_HARDWARE  = 1
} MFTOPOLOGY_HARDWARE_MODE;

typedef struct _MFT_REGISTRATION_INFO
{
    CLSID clsid;
    GUID guidCategory;
    UINT32 uiFlags;
    LPCWSTR pszName;
    DWORD cInTypes;
    [size_is(cInTypes)] MFT_REGISTER_TYPE_INFO *pInTypes;
    DWORD cOutTypes;
    [size_is(cOutTypes)] MFT_REGISTER_TYPE_INFO *pOutTypes;
} MFT_REGISTRATION_INFO;

cpp_quote("#endif /*(WINVER >= 0x0601)*/")

cpp_quote("typedef struct _ASFFlatPicture {")
cpp_quote("  BYTE  bPictureType;")
cpp_quote("  DWORD dwDataLen;")
cpp_quote("} ASF_FLAT_PICTURE;")
cpp_quote("")
cpp_quote("typedef struct _ASFFlatSynchronisedLyrics {")
cpp_quote("  BYTE  bTimeStampFormat;")
cpp_quote("  BYTE  bContentType;")
cpp_quote("  DWORD dwLyricsLen;")
cpp_quote("} ASF_FLAT_SYNCHRONISED_LYRICS;")

typedef enum SAMPLE_PROTECTION_VERSION
{
    SAMPLE_PROTECTION_VERSION_NO         = 0,
    SAMPLE_PROTECTION_VERSION_BASIC_LOKI = 1,
    SAMPLE_PROTECTION_VERSION_SCATTER    = 2,
    SAMPLE_PROTECTION_VERSION_RC4        = 3
} SAMPLE_PROTECTION_VERSION;

typedef struct _MFINPUTTRUSTAUTHORITY_ACTION
{
    MFPOLICYMANAGER_ACTION Action;
    BYTE                   *pbTicket;
    DWORD                  cbTicket;
} MFINPUTTRUSTAUTHORITY_ACCESS_ACTION;

typedef struct _MFINPUTTRUSTAUTHORITY_ACCESS_PARAMS
{
    DWORD                               dwSize;
    DWORD                               dwVer;
    DWORD                               cbSignatureOffset;
    DWORD                               cbSignatureSize;
    DWORD                               cbExtensionOffset;
    DWORD                               cbExtensionSize;
    DWORD                               cActions;
    MFINPUTTRUSTAUTHORITY_ACCESS_ACTION rgOutputActions[1];
} MFINPUTTRUSTAUTHORITY_ACCESS_PARAMS;

typedef struct _MFNetCredentialManagerGetParam
{
    HRESULT hrOp;
    WINBOOL fAllowLoggedOnUser;
    WINBOOL fClearTextPackage;
    LPCWSTR pszUrl;
    LPCWSTR pszSite;
    LPCWSTR pszRealm;
    LPCWSTR pszPackage;
    LONG    nRetries;
} MFNetCredentialManagerGetParam;

cpp_quote("#define MEDIASINK_FIXED_STREAMS                 0x00000001")
cpp_quote("#define MEDIASINK_CANNOT_MATCH_CLOCK            0x00000002")
cpp_quote("#define MEDIASINK_RATELESS                      0x00000004")
cpp_quote("#define MEDIASINK_CLOCK_REQUIRED                0x00000008")
cpp_quote("#define MEDIASINK_CAN_PREROLL                   0x00000010")
cpp_quote("#define MEDIASINK_REQUIRE_REFERENCE_MEDIATYPE   0x00000020")

enum
{
    MF_RESOLUTION_MEDIASOURCE                                           = 0x00000001,
    MF_RESOLUTION_BYTESTREAM                                            = 0x00000002,
    MF_RESOLUTION_CONTENT_DOES_NOT_HAVE_TO_MATCH_EXTENSION_OR_MIME_TYPE = 0x00000010,
    MF_RESOLUTION_KEEP_BYTE_STREAM_ALIVE_ON_FAIL                        = 0x00000020,
    MF_RESOLUTION_READ                                                  = 0x00010000,
    MF_RESOLUTION_WRITE                                                 = 0x00020000
};

/* Forward declaration goes here.
 * Just for ease, declare interfaces alphabatically.
 */
interface IMFASFContentInfo;
interface IMFASFIndexer;
interface IMFASFMultiplexer;
interface IMFASFProfile;
interface IMFASFSplitter;
interface IMFPMPServer;
interface IMFPresentationClock;
interface IMFNetProxyLocator;
interface IMFRemoteDesktopPlugin;
interface IMFTransform;
interface IMFSequencerSource;
interface IMFStreamSink;
interface IMFQualityManager;
interface IMFTranscodeProfile;

[object, uuid(76b1bbdb-4ec8-4f36-b106-70a9316df593)]
interface IMFAudioStreamVolume : IUnknown
{
  HRESULT GetChannelCount([out] UINT32 *pdwCount);
  HRESULT SetChannelVolume([in] UINT32 dwIndex, [in] const float fLevel);
  HRESULT GetChannelVolume([in] UINT32 dwIndex, [out] float *pfLevel);
  HRESULT SetAllVolumes([in] UINT32 dwCount, [in, size_is(dwCount)] const float *pfVolumes);
  HRESULT GetAllVolumes([in] UINT32 dwCount, [out, size_is(dwCount)] float *pfVolumes);
}

[object, uuid(6ef2a660-47c0-4666-b13d-cbb717f2fa2c)]
interface IMFMediaSink : IUnknown
{
  HRESULT GetCharacteristics([out] DWORD *pdwCharacteristics);
  HRESULT AddStreamSink([in] DWORD dwStreamSinkIdentifier, [in] IMFMediaType *pMediaType, [out] IMFStreamSink **ppStreamSink);
  HRESULT RemoveStreamSink([in] DWORD dwStreamSinkIdentifier);
  HRESULT GetStreamSinkCount([out] DWORD *pcStreamSinkCount);
  HRESULT GetStreamSinkByIndex([in] DWORD dwIndex, [out] IMFStreamSink **ppStreamSink);
  HRESULT GetStreamSinkById([in] DWORD dwStreamSinkIdentifier, [out] IMFStreamSink **ppStreamSink);
  HRESULT SetPresentationClock([in] IMFPresentationClock *pPresentationClock);
  HRESULT GetPresentationClock([out] IMFPresentationClock **ppPresentationClock);
  HRESULT Shutdown(void);
}

[object, uuid(eaecb74a-9a50-42ce-9541-6a7f57aa4ad7), local]
interface IMFFinalizableMediaSink : IMFMediaSink
{
  HRESULT BeginFinalize([in] IMFAsyncCallback *pCallback, [in] IUnknown *punkState);
  HRESULT EndFinalize([in] IMFAsyncResult *pResult);
}

[object, uuid(5dfd4b2a-7674-4110-a4e6-8a68fd5f3688)]
interface IMFMediaSinkPreroll : IUnknown
{
  HRESULT NotifyPreroll([in] MFTIME hnsUpcomingStartTime);
}

[object, uuid(d182108f-4ec6-443f-aa42-a71106ec825f)]
interface IMFMediaStream : IMFMediaEventGenerator
{
  HRESULT GetMediaSource([out] IMFMediaSource**ppMediaSource);
  HRESULT GetStreamDescriptor([out] IMFStreamDescriptor **ppStreamDescriptor);

  [local]
  HRESULT RequestSample([in] IUnknown *pToken);

  [call_as(RequestSample)]
  HRESULT RemoteRequestSample(void);
}

[object, uuid(f88cfb8c-ef16-4991-b450-cb8c69e51704)]
interface IMFMetadata : IUnknown
{
  HRESULT SetLanguage([in] LPCWSTR pwszRFC1766);
  HRESULT GetLanguage([out] LPWSTR *ppwszRFC1766);
  HRESULT GetAllLanguages([out] PROPVARIANT *ppvLanguages);
  HRESULT SetProperty([in] LPCWSTR pwszName, [in] const PROPVARIANT *ppvValue);
  HRESULT GetProperty([in] LPCWSTR pwszName, [out] PROPVARIANT *ppvValue);
  HRESULT DeleteProperty([in] LPCWSTR pwszName);
  HRESULT GetAllPropertyNames([out] PROPVARIANT *ppvNames);
}

[object, uuid(56181d2d-e221-4adb-b1c8-3cee6a53f76f)]
interface IMFMetadataProvider : IUnknown
{
  HRESULT GetMFMetadata([in] IMFPresentationDescriptor *pPresentationDescriptor, [in] DWORD dwStreamIdentifier, [in] DWORD dwFlags, [out] IMFMetadata **ppMFMetadata);
}

[object, uuid(7ff12cce-f76f-41c2-863b-1666c8e5e139)]
interface IMFPresentationTimeSource : IMFClock
{
  HRESULT GetUnderlyingClock([out] IMFClock **ppClock);
}

[object, uuid(868ce85c-8ea9-4f55-ab82-b009a910a805)]
interface IMFPresentationClock : IMFClock
{
  HRESULT SetTimeSource([in] IMFPresentationTimeSource *pTimeSource);
  HRESULT GetTimeSource([out] IMFPresentationTimeSource **ppTimeSource);
  HRESULT GetTime([out] MFTIME *phnsClockTime);
  HRESULT AddClockStateSink([in] IMFClockStateSink *pStateSink);
  HRESULT RemoveClockStateSink([in] IMFClockStateSink* pStateSink);
  HRESULT Start([in] LONGLONG llClockStartOffset);
  HRESULT Stop(void);
  HRESULT Pause(void);
}

[object, uuid(88ddcd21-03c3-4275-91ed-55ee3929328f), pointer_default(unique)]
interface IMFRateControl : IUnknown
{
  HRESULT SetRate([in] WINBOOL fThin, [in] float flRate);
  HRESULT GetRate([in, out, unique] WINBOOL *pfThin, [in, out, unique] float *pflRate);
}

[object, uuid(0a9ccdbc-d797-4563-9667-94ec5d79292d), pointer_default(unique)]
interface IMFRateSupport : IUnknown
{
  HRESULT GetSlowestRate([in] MFRATE_DIRECTION eDirection, [in] WINBOOL fThin, [out] float *pflRate);
  HRESULT GetFastestRate([in] MFRATE_DIRECTION eDirection, [in] WINBOOL fThin, [out] float *pflRate);
  HRESULT IsRateSupported([in] WINBOOL fThin, [in] float flRate, [in, out, unique] float *pflNearestSupportedRate);
}

[object, uuid(8c7b80bf-ee42-4b59-b1df-55668e1bdca8), local]
interface IMFSampleGrabberSinkCallback : IMFClockStateSink
{
  HRESULT OnSetPresentationClock([in] IMFPresentationClock *pPresentationClock);
  HRESULT OnProcessSample([in] REFGUID guidMajorMediaType, [in] DWORD dwSampleFlags, [in] LONGLONG llSampleTime, [in] LONGLONG llSampleDuration, [in] const BYTE *pSampleBuffer, [in] DWORD dwSampleSize);
  HRESULT OnShutdown(void);
}

[object, uuid(97ec2ea4-0e42-4937-97ac-9d6d328824e1)]
interface IMFShutdown : IUnknown
{
  HRESULT Shutdown(void);
  HRESULT GetShutdownStatus([out] MFSHUTDOWN_STATUS *pStatus);
};

[object, uuid(089edf13-cf71-4338-8d13-9e569dbdc319)]
interface IMFSimpleAudioVolume : IUnknown
{
  HRESULT SetMasterVolume([in] float fLevel);
  HRESULT GetMasterVolume([out] float *pfLevel);
  HRESULT SetMute([in] const WINBOOL bMute);
  HRESULT GetMute([out] WINBOOL *pbMute);
}

[object, uuid(fbe5a32d-a497-4b61-bb85-97b1a848a6e3)]
interface IMFSourceResolver : IUnknown
{
  [local]
  HRESULT CreateObjectFromURL([in] LPCWSTR pwszURL, [in] DWORD dwFlags, [in] IPropertyStore *pProps, [out] MF_OBJECT_TYPE *pObjectType, [out] IUnknown **ppObject);

  [local]
  HRESULT CreateObjectFromByteStream([in] IMFByteStream* pByteStream, [in] LPCWSTR pwszURL, [in] DWORD dwFlags, [in] IPropertyStore *pProps, [out] MF_OBJECT_TYPE *pObjectType, [out] IUnknown **ppObject);

  [local]
  HRESULT BeginCreateObjectFromURL([in] LPCWSTR pwszURL, [in] DWORD dwFlags, [in] IPropertyStore *pProps, [out] IUnknown **ppIUnknownCancelCookie, [in] IMFAsyncCallback *pCallback, [in] IUnknown *punkState);

  [call_as(BeginCreateObjectFromURL)]
  HRESULT RemoteBeginCreateObjectFromURL([in, string] LPCWSTR pwszURL, [in] DWORD dwFlags, [in] IPropertyStore *pProps, [in] IMFRemoteAsyncCallback *pCallback);

  [local]
  HRESULT EndCreateObjectFromURL([in] IMFAsyncResult *pResult, [out] MF_OBJECT_TYPE *pObjectType, [out] IUnknown **ppObject);

  [call_as(EndCreateObjectFromURL)]
  HRESULT RemoteEndCreateObjectFromURL([in] IUnknown *pResult, [out] MF_OBJECT_TYPE *pObjectType, [out] IUnknown **ppObject);

  [local]
  HRESULT BeginCreateObjectFromByteStream([in] IMFByteStream* pByteStream, [in] LPCWSTR pwszURL, [in] DWORD dwFlags, [in] IPropertyStore *pProps, [out] IUnknown **ppIUnknownCancelCookie, [in] IMFAsyncCallback *pCallback, [in] IUnknown *punkState);

  [call_as(BeginCreateObjectFromByteStream)]
  HRESULT RemoteBeginCreateObjectFromByteStream([in] IMFByteStream* pByteStream, [in, unique] LPCWSTR pwszURL, [in] DWORD dwFlags, [in, unique] IPropertyStore *pProps, [in] IMFRemoteAsyncCallback *pCallback);

  [local]
  HRESULT EndCreateObjectFromByteStream([in] IMFAsyncResult *pResult, [out] MF_OBJECT_TYPE *pObjectType, [out] IUnknown **ppObject);

  [call_as(EndCreateObjectFromByteStream)]
  HRESULT RemoteEndCreateObjectFromByteStream([in] IUnknown *pResult, [out] MF_OBJECT_TYPE *pObjectType, [out] IUnknown **ppObject);

  [local]
  HRESULT CancelObjectCreation([in] IUnknown *pIUnknownCancelCookie);
}

[object, uuid(0a97b3cf-8e7c-4a3d-8f8c-0c843dc247fb)]
interface IMFStreamSink : IMFMediaEventGenerator
{
  HRESULT GetMediaSink([out] IMFMediaSink **ppMediaSink);
  HRESULT GetIdentifier([out] DWORD *pdwIdentifier);
  HRESULT GetMediaTypeHandler([out] IMFMediaTypeHandler **ppHandler);
  HRESULT ProcessSample([in] IMFSample *pSample);
  HRESULT PlaceMarker([in] MFSTREAMSINK_MARKER_TYPE eMarkerType, [in] const PROPVARIANT *pvarMarkerValue, [in] const PROPVARIANT *pvarContextValue);
  HRESULT Flush(void);
}

[object, uuid(e56e4cbd-8f70-49d8-a0f8-edb3d6ab9bf2), local]
interface IMFTimer : IUnknown
{
  HRESULT SetTimer([in] DWORD dwFlags, [in] LONGLONG llClockTime, [in] IMFAsyncCallback *pCallback, [in] IUnknown *punkState, [out] IUnknown **ppunkKey);
  HRESULT CancelTimer([in] IUnknown *punkKey);
}

[object, uuid(de9a6157-f660-4643-b56a-df9f7998c7cd), local]
interface IMFTopoLoader : IUnknown
{
  HRESULT Load([in] IMFTopology *pInputTopo, [out] IMFTopology **ppOutputTopo, [in] IMFTopology *pCurrentTopo);
}

[object, uuid(86cbc910-e533-4751-8e3b-f19b5b806a03), local]
interface IMFVideoSampleAllocator : IUnknown
{
  HRESULT SetDirectXManager([in, unique] IUnknown *pManager);
  HRESULT UninitializeSampleAllocator();
  HRESULT InitializeSampleAllocator([in] DWORD cRequestedFrames, [in] IMFMediaType *pMediaType);
  HRESULT AllocateSample([out] IMFSample **ppSample);
}

cpp_quote("#if WINVER >= _WIN32_WINNT_WIN7")

[object, uuid(a792cdbe-c374-4e89-8335-278e7b9956a4), local]
interface IMFVideoSampleAllocatorNotify : IUnknown
{
  HRESULT NotifyRelease();
}

[object, uuid(3978aa1a-6d5b-4b7f-a340-90899189ae34), local]
interface IMFVideoSampleAllocatorNotifyEx : IMFVideoSampleAllocatorNotify
{
  HRESULT NotifyPrune(IMFSample *ppSample);
}

[object, uuid(992388b4-3372-4f67-8b6f-c84c071f4751), local]
interface IMFVideoSampleAllocatorCallback : IUnknown
{
  HRESULT SetCallback([in, unique] IMFVideoSampleAllocatorNotify *pNotify);
  HRESULT GetFreeSampleCount([out] LONG *plSamples);
}

[object, local, uuid(545b3a48-3283-4f62-866f-a62d8f598f9f), pointer_default(unique)]
interface IMFVideoSampleAllocatorEx : IMFVideoSampleAllocator
{
  HRESULT InitializeSampleAllocatorEx([in] DWORD cInitialSamples, [in] DWORD cMaximumSamples, [in, optional] IMFAttributes *pAttributes, [in] IMFMediaType *pMediaType);
}

cpp_quote("#endif /* WINVER >= _WIN32_WINNT_WIN7 */")

cpp_quote("#if WINVER >= _WIN32_WINNT_WIN8")

typedef enum _MF_VIDEO_PROCESSOR_ROTATION {
  ROTATION_NONE = 0,
  ROTATION_NORMAL = 1
} MF_VIDEO_PROCESSOR_ROTATION;

typedef enum _MF_VIDEO_PROCESSOR_MIRROR {
  MIRROR_NONE = 0,
  MIRROR_HORIZONTAL = 1,
  MIRROR_VERTICAL = 2
} MF_VIDEO_PROCESSOR_MIRROR;

[object, local, uuid(a3f675d5-6119-4f7f-a100-1d8b280f0efb), pointer_default(unique)]
interface IMFVideoProcessorControl : IUnknown
{
  HRESULT SetBorderColor([in, optional] MFARGB *pBorderColor);
  HRESULT SetSourceRectangle([in, optional] RECT *pSrcRect);
  HRESULT SetDestinationRectangle([in, optional] RECT *pDstRect);
  HRESULT SetMirror([in] MF_VIDEO_PROCESSOR_MIRROR eMirror);
  HRESULT SetRotation([in] MF_VIDEO_PROCESSOR_ROTATION eRotation);
  HRESULT SetConstrictionSize([in, optional] SIZE *pConstrictionSize);
}

cpp_quote("#if WINVER >= _WIN32_WINNT_WINBLUE")
[object, local, uuid(bde633d3-e1dc-4a7f-a693-bbae399c4a20), pointer_default(unique)]
interface IMFVideoProcessorControl2 : IMFVideoProcessorControl
{
  HRESULT SetRotationOverride([in] UINT uiRotation);
  HRESULT EnableHardwareEffects([in] WINBOOL fEnabled);
  HRESULT GetSupportedHardwareEffects([out, retval] UINT *puiSupport);
}

cpp_quote("#if WINVER >= _WIN32_WINNT_WIN10")

typedef enum _MFVideoSphericalFormat {
  MFVideoSphericalFormat_Unsupported = 0,
  MFVideoSphericalFormat_Equirectangular = 1,
  MFVideoSphericalFormat_CubeMap = 2,
  MFVideoSphericalFormat_3DMesh = 3
} MFVideoSphericalFormat;

cpp_quote("#endif /* WINVER >= _WIN32_WINNT_WIN10 */")

cpp_quote("#if NTDDI_VERSION >= NTDDI_WIN10_RS3")

cpp_quote("EXTERN_GUID(MF_XVP_SAMPLE_LOCK_TIMEOUT, 0xaa4ddb29, 0x5134, 0x4363, 0xac, 0x72, 0x83, 0xec, 0x4b, 0xc1, 0x4, 0x26);")

typedef enum MFVideoSphericalProjectionMode {
  MFVideoSphericalProjectionMode_Spherical = 0,
  MFVideoSphericalProjectionMode_Flat
} MFVideoSphericalProjectionMode;

[object, local, uuid(2424b3f2-eb23-40f1-91aa-74bddeea0883)]
interface IMFVideoProcessorControl3 : IMFVideoProcessorControl2
{
  HRESULT GetNaturalOutputType([out] IMFMediaType **ppType);
  HRESULT EnableSphericalVideoProcessing([in] WINBOOL fEnable, [in] MFVideoSphericalFormat eFormat, [in] MFVideoSphericalProjectionMode eProjectionMode);
  HRESULT SetSphericalVideoProperties([in] float X, [in] float Y, [in] float Z, [in] float W, [in] float fieldOfView);
  HRESULT SetOutputDevice([in] IUnknown *pOutputDevice);
}

cpp_quote("#endif /* NTDDI_VERSION >= NTDDI_WIN10_RS3 */")

cpp_quote("#endif /* WINVER >= _WIN32_WINNT_WINBLUE */")

cpp_quote("#if NTDDI_VERSION >= NTDDI_WIN10_VB")

[object, local, uuid(604d33d7-cf23-41d5-8224-5bbbb1a87475)]
interface IMFVideoRendererEffectControl : IUnknown
{
  HRESULT OnAppServiceConnectionEstablished([in] IUnknown *pAppServiceConnection);
}

cpp_quote("#endif /* WINVER >= NTDDI_WIN10_VB */")

cpp_quote("#endif /* WINVER >= _WIN32_WINNT_WIN8 */")

cpp_quote("HRESULT WINAPI MFCreate3GPMediaSink(IMFByteStream *pIByteStream,IMFMediaType *pVideoMediaType,IMFMediaType *pAudioMediaType,IMFMediaSink **ppIMediaSink);")
cpp_quote("HRESULT WINAPI MFCreateAggregateSource(IMFCollection *pSourceCollection,IMFMediaSource **ppAggSource);")
cpp_quote("")
cpp_quote("HRESULT WINAPI MFCreateAlignedMemoryBuffer(DWORD cbMaxLength,DWORD fAlignmentFlags,IMFMediaBuffer **ppBuffer);")
cpp_quote("HRESULT WINAPI MFCreateASFContentInfo(IMFASFContentInfo **ppIContentInfo);")
cpp_quote("HRESULT WINAPI MFCreateASFIndexer(IMFASFIndexer **ppIIndexer);")
cpp_quote("HRESULT WINAPI MFCreateASFIndexerByteStream(IMFByteStream *pIContentByteStream,QWORD cbIndexStartOffset,IMFByteStream **pIIndexByteStream);")
cpp_quote("HRESULT WINAPI MFCreateASFMediaSink(IMFByteStream *pIByteStream,IMFMediaSink **ppIMediaSink);")
cpp_quote("HRESULT WINAPI MFCreateASFMediaSinkActivate(LPCWSTR pwszFileName,IMFASFContentInfo *pContentInfo,IMFActivate **ppIActivate);")
cpp_quote("HRESULT WINAPI MFCreateASFMultiplexer(IMFASFMultiplexer **ppIMultiplexer);")
cpp_quote("HRESULT WINAPI MFCreateASFProfile(IMFASFProfile **ppIProfile);")
cpp_quote("HRESULT WINAPI MFCreateASFProfileFromPresentationDescriptor(IMFPresentationDescriptor *pIPD,IMFASFProfile **ppIProfile);")
cpp_quote("HRESULT WINAPI MFCreateASFSplitter(IMFASFSplitter **ppISplitter);")
cpp_quote("HRESULT WINAPI MFCreateAudioRenderer(IMFAttributes *pAudioAttributes, IMFMediaSink **ppSink);")
cpp_quote("HRESULT WINAPI MFCreateAudioRendererActivate(IMFActivate **ppActivate);")
cpp_quote("HRESULT WINAPI MFCreateDeviceSource(IMFAttributes *pAttributes,IMFMediaSource **ppSource);")
cpp_quote("HRESULT WINAPI MFCreateMediaSession(IMFAttributes *pConfiguration,IMFMediaSession **ppMS);")
cpp_quote("HRESULT WINAPI MFCreateMP3MediaSink(IMFByteStream *pTargetByteStream,IMFMediaSink **ppMediaSink);")
cpp_quote("HRESULT WINAPI MFCreateMPEG4MediaSink(IMFByteStream *pIByteStream,IMFMediaType *pVideoMediaType,IMFMediaType *pAudioMediaType,IMFMediaSink **ppIMediaSink);")
cpp_quote("HRESULT WINAPI MFCreateNetSchemePlugin(REFIID riid,LPVOID *ppvHandler);")
cpp_quote("HRESULT WINAPI MFCreatePMPServer(DWORD dwCreationFlags,IMFPMPServer **ppPMPServer);")
cpp_quote("HRESULT WINAPI MFCreatePMPMediaSession(DWORD dwCreationFlags,IMFAttributes *pConfiguration,IMFMediaSession **ppMediaSession,IMFActivate **ppEnablerActivate);")
cpp_quote("HRESULT WINAPI MFCreatePathFromURL(LPCWSTR pwszFileURL,LPWSTR *ppwszFilePath);")
cpp_quote("HRESULT WINAPI MFCreatePresentationClock(IMFPresentationClock **ppPresentationClock);")
cpp_quote("HRESULT WINAPI MFCreatePresentationDescriptor(DWORD cStreamDescriptors,IMFStreamDescriptor **apStreamDescriptors,IMFPresentationDescriptor **ppPresentationDescriptor);")
cpp_quote("HRESULT WINAPI MFCreatePresentationDescriptorFromASFProfile(IMFASFProfile *pIProfile,IMFPresentationDescriptor **ppIPD);")
cpp_quote("HRESULT WINAPI MFCreateProxyLocator(LPCWSTR pszProtocol,IPropertyStore *pProxyConfig,IMFNetProxyLocator **ppProxyLocator);")
cpp_quote("HRESULT WINAPI MFCreateRemoteDesktopPlugin(IMFRemoteDesktopPlugin **ppPlugin);")
cpp_quote("HRESULT WINAPI MFCreateSample(IMFSample **ppIMFSample);")
cpp_quote("HRESULT WINAPI MFCreateSampleCopierMFT(IMFTransform **ppCopierMFT);")
cpp_quote("HRESULT WINAPI MFCreateSampleGrabberSinkActivate(IMFMediaType *pIMFMediaType,IMFSampleGrabberSinkCallback *pIMFSampleGrabberSinkCallback,IMFActivate **ppIActivate);")
cpp_quote("HRESULT WINAPI MFCreateSequencerSource(IUnknown *pReserved,IMFSequencerSource **ppSequencerSource);")
cpp_quote("HRESULT WINAPI MFCreateSimpleTypeHandler(IMFMediaTypeHandler **ppHandler);")
cpp_quote("HRESULT WINAPI MFCreateStandardQualityManager(IMFQualityManager **ppQualityManager);")
cpp_quote("HRESULT WINAPI MFCreateStreamDescriptor(DWORD dwStreamIdentifier,DWORD cMediaTypes,IMFMediaType **apMediaTypes,IMFStreamDescriptor **ppDescriptor);")
cpp_quote("HRESULT WINAPI MFCreateSourceResolver(IMFSourceResolver **ppISourceResolver);")
cpp_quote("HRESULT WINAPI MFCreateSystemTimeSource(IMFPresentationTimeSource **ppSystemTimeSource);")
cpp_quote("HRESULT WINAPI MFCreateTopoLoader(IMFTopoLoader **ppObj);")
cpp_quote("HRESULT WINAPI MFCreateTopology(IMFTopology **ppTopo);")
cpp_quote("HRESULT WINAPI MFCreateTopologyNode(MF_TOPOLOGY_TYPE NodeType,IMFTopologyNode **ppNode);")
cpp_quote("HRESULT WINAPI MFCreateVideoRenderer(REFIID riidRenderer,void **ppVideoRenderer);")
cpp_quote("HRESULT WINAPI MFCreateVideoRendererActivate(HWND hwndVideo,IMFActivate **ppActivate);")
cpp_quote("HRESULT WINAPI MFGetService(IUnknown *punkObject,REFGUID guidService,REFIID riid,LPVOID *ppvObject);")
cpp_quote("HRESULT WINAPI MFGetSupportedMimeTypes(PROPVARIANT *pPropVarMimeTypeArray);")
cpp_quote("HRESULT WINAPI MFGetSupportedSchemes(PROPVARIANT *pPropVarSchemeArray);")
cpp_quote("MFTIME WINAPI MFGetSystemTime(void);")
cpp_quote("HRESULT WINAPI MFShutdownObject(IUnknown *pUnk);")
cpp_quote("HRESULT WINAPI CreateNamedPropertyStore(INamedPropertyStore **ppStore);")
cpp_quote("HRESULT WINAPI CreatePropertyStore(IPropertyStore **ppStore);")
cpp_quote("#if (WINVER >= 0x0601)")
cpp_quote("")
cpp_quote("HRESULT WINAPI MFCreateTranscodeProfile(IMFTranscodeProfile **ppTranscodeProfile);")
cpp_quote("HRESULT WINAPI MFCreateTranscodeSinkActivate(IMFActivate **ppActivate);")
cpp_quote("HRESULT WINAPI MFCreateTranscodeTopology(IMFMediaSource *pSrc,LPCWSTR pwszOutputFilePath,IMFTranscodeProfile *pProfile,IMFTopology **ppTranscodeTopo);")
cpp_quote("HRESULT WINAPI MFEnumDeviceSources(IMFAttributes *pAttributes,IMFActivate ***pppSourceActivate,UINT32 *pcSourceActivate);")
cpp_quote("HRESULT WINAPI MFGetTopoNodeCurrentType(IMFTopologyNode *pNode,DWORD dwStreamIndex,WINBOOL fOutput,IMFMediaType **ppType);")
cpp_quote("HRESULT WINAPI MFTranscodeGetAudioOutputAvailableTypes(REFGUID guidSubType,DWORD dwMFTFlags,IMFAttributes *pCodecConfig,IMFCollection **ppAvailableTypes);")
cpp_quote("#endif")

cpp_quote("EXTERN_GUID(MFNETSOURCE_STATISTICS, 0x3cb1f274, 0x0505, 0x4c5d, 0xae, 0x71, 0x0a, 0x55, 0x63, 0x44, 0xef, 0xa1);")
cpp_quote("EXTERN_GUID(MFNETSOURCE_STATISTICS_SERVICE, 0x3cb1f275, 0x0505, 0x4c5d, 0xae, 0x71, 0x0a, 0x55, 0x63, 0x44, 0xef, 0xa1);")

cpp_quote("EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_MIXER_CLSID, 0xba491360, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );")
cpp_quote("EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_MIXER_ACTIVATE, 0xba491361, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );")
cpp_quote("EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_MIXER_FLAGS, 0xba491362, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );")
cpp_quote("EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_PRESENTER_CLSID, 0xba491364, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );")
cpp_quote("EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_PRESENTER_ACTIVATE, 0xba491365, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );")
cpp_quote("EXTERN_GUID(MF_ACTIVATE_CUSTOM_VIDEO_PRESENTER_FLAGS, 0xba491366, 0xbe50, 0x451e, 0x95, 0xab, 0x6d, 0x4a, 0xcc, 0xc7, 0xda, 0xd8 );")

cpp_quote("EXTERN_GUID(MF_AUDIO_RENDERER_ATTRIBUTE_SESSION_ID, 0xede4b5e3, 0xf805, 0x4d6c, 0x99, 0xb3, 0xdb, 0x01, 0xbf, 0x95, 0xdf, 0xab);")
cpp_quote("EXTERN_GUID(MF_AUDIO_RENDERER_ATTRIBUTE_ENDPOINT_ID, 0xb10aaec3, 0xef71, 0x4cc3, 0xb8, 0x73, 0x5, 0xa9, 0xa0, 0x8b, 0x9f, 0x8e);")
cpp_quote("EXTERN_GUID(MF_AUDIO_RENDERER_ATTRIBUTE_ENDPOINT_ROLE, 0x6ba644ff, 0x27c5, 0x4d02, 0x98, 0x87, 0xc2, 0x86, 0x19, 0xfd, 0xb9, 0x1b);")
cpp_quote("EXTERN_GUID(MF_AUDIO_RENDERER_ATTRIBUTE_STREAM_CATEGORY, 0xa9770471, 0x92ec, 0x4df4, 0x94, 0xfe, 0x81, 0xc3, 0x6f, 0xc, 0x3a, 0x7a);")

cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_FRIENDLY_NAME, 0x60d0e559,0x52f8,0x4fa2,0xbb,0xce,0xac,0xdb,0x34,0xa8,0xec,0x1);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_MEDIA_TYPE, 0x56a819ca,0xc78,0x4de4,0xa0,0xa7,0x3d,0xda,0xba,0xf,0x24,0xd4);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_AUDCAP_GUID, 0x14dd9a1c, 0x7cff, 0x41be, 0xb1, 0xb9, 0xba, 0x1a, 0xc6, 0xec, 0xb5, 0x71);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_AUDCAP_ENDPOINT_ID, 0x30da9258,0xfeb9,0x47a7,0xa4,0x53,0x76,0x3a,0x7a,0x8e,0x1c,0x5f);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_AUDCAP_ROLE, 0xbc9d118e,0x8c67,0x4a18,0x85,0xd4,0x12,0xd3,0x0,0x40,0x5,0x52);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_CATEGORY, 0x77f0ae69,0xc3bd,0x4509,0x94,0x1d,0x46,0x7e,0x4d,0x24,0x89,0x9e);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_HW_SOURCE, 0xde7046ba,0x54d6,0x4487,0xa2,0xa4,0xec,0x7c,0xd,0x1b,0xd1,0x63);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_MAX_BUFFERS, 0x7dd9b730,0x4f2d,0x41d5,0x8f,0x95,0xc,0xc9,0xa9,0x12,0xba,0x26);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_SYMBOLIC_LINK, 0x58f0aad8,0x22bf,0x4f8a,0xbb,0x3d,0xd2,0xc4,0x97,0x8c,0x6e,0x2f);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE, 0xc60ac5fe,0x252a,0x478f,0xa0,0xef,0xbc,0x8f,0xa5,0xf7,0xca,0xd3);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_GUID, 0x8ac3587a,0x4ae7,0x42d8,0x99,0xe0,0x0a,0x60,0x13,0xee,0xf9,0x0f);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_AUDCAP_SYMBOLIC_LINK, 0x98d24b5e, 0x5930, 0x4614, 0xb5, 0xa1, 0xf6, 0x0, 0xf9, 0x35, 0x5a, 0x78);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_TYPE_VIDCAP_PROVIDER_DEVICE_ID, 0x36689d42, 0xa06c, 0x40ae, 0x84, 0xcf, 0xf5, 0xa0, 0x34, 0x6, 0x7c, 0xc4);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_XADDRESS, 0xbca0be52, 0xc327, 0x44c7, 0x9b, 0x7d, 0x7f, 0xa8, 0xd9, 0xb5, 0xbc, 0xda);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_STREAM_URL, 0x9d7b40d2, 0x3617, 0x4043, 0x93, 0xe3, 0x8d, 0x6d, 0xa9, 0xbb, 0x34, 0x92);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_USERNAME,0x5d01add, 0x949f, 0x46eb, 0xbc, 0x8e, 0x8b, 0xd, 0x2b, 0x32, 0xd7, 0x9d);")
cpp_quote("EXTERN_GUID(MF_DEVSOURCE_ATTRIBUTE_SOURCE_PASSWORD, 0xa0fd7e16, 0x42d9, 0x49df, 0x84, 0xc0, 0xe8, 0x2c, 0x5e, 0xab, 0x88, 0x74);")

cpp_quote("EXTERN_GUID(MF_METADATA_PROVIDER_SERVICE, 0xdb214084, 0x58a4, 0x4d2e, 0xb8, 0x4f, 0x6f, 0x75, 0x5b, 0x2f, 0x7a, 0xd);")
cpp_quote("EXTERN_GUID(MF_PROPERTY_HANDLER_SERVICE, 0xa3face02, 0x32b8, 0x41dd, 0x90, 0xe7, 0x5f, 0xef, 0x7c, 0x89, 0x91, 0xb5);")
cpp_quote("EXTERN_GUID(MF_RATE_CONTROL_SERVICE, 0x866fa297, 0xb802, 0x4bf8, 0x9d, 0xc9, 0x5e, 0x3b, 0x6a, 0x9f, 0x53, 0xc9);")
cpp_quote("EXTERN_GUID(MF_SAMPLEGRABBERSINK_IGNORE_CLOCK, 0x0efda2c0, 0x2b69, 0x4e2e, 0xab, 0x8d, 0x46, 0xdc, 0xbf, 0xf7, 0xd2, 0x5d);")

cpp_quote("EXTERN_GUID(MF_SD_LANGUAGE, 0xaf2180, 0xbdc2, 0x423c, 0xab, 0xca, 0xf5, 0x3, 0x59, 0x3b, 0xc1, 0x21);")
cpp_quote("EXTERN_GUID(MF_SD_PROTECTED, 0xaf2181, 0xbdc2, 0x423c, 0xab, 0xca, 0xf5, 0x3, 0x59, 0x3b, 0xc1, 0x21);")
cpp_quote("EXTERN_GUID(MF_SD_STREAM_NAME, 0x4f1b099d, 0xd314, 0x41e5, 0xa7, 0x81, 0x7f, 0xef, 0xaa, 0x4c, 0x50, 0x1f);")
cpp_quote("EXTERN_GUID(MF_SD_MUTUALLY_EXCLUSIVE, 0x23ef79c, 0x388d, 0x487f, 0xac, 0x17, 0x69, 0x6c, 0xd6, 0xe3, 0xc6, 0xf5);")

cpp_quote("EXTERN_GUID(MF_TOPONODE_DRAIN, 0x494bbce9, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_D3DAWARE, 0x494bbced, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPOLOGY_RESOLUTION_STATUS, 0x494bbcde, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_ERRORCODE, 0x494bbcee, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_CONNECT_METHOD, 0x494bbcf1, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_LOCKED, 0x494bbcf7, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_WORKQUEUE_ID, 0x494bbcf8, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_WORKQUEUE_MMCSS_CLASS, 0x494bbcf9, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_DECRYPTOR, 0x494bbcfa, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_DISCARDABLE, 0x494bbcfb, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_ERROR_MAJORTYPE, 0x494bbcfd, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_ERROR_SUBTYPE, 0x494bbcfe, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_WORKQUEUE_MMCSS_TASKID, 0x494bbcff, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_WORKQUEUE_MMCSS_PRIORITY, 0x5001f840, 0x2816, 0x48f4, 0x93, 0x64, 0xad, 0x1e, 0xf6, 0x61, 0xa1, 0x23);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_WORKQUEUE_ITEM_PRIORITY, 0xa1ff99be, 0x5e97, 0x4a53, 0xb4, 0x94, 0x56, 0x8c, 0x64, 0x2c, 0x0f, 0xf3);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_MARKIN_HERE, 0x494bbd00, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_MARKOUT_HERE, 0x494bbd01, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_DECODER, 0x494bbd02, 0xb031, 0x4e38, 0x97, 0xc4, 0xd5, 0x42, 0x2d, 0xd6, 0x18, 0xdc);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_MEDIASTART, 0x835c58ea, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_MEDIASTOP, 0x835c58eb, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_SOURCE, 0x835c58ec, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_PRESENTATION_DESCRIPTOR, 0x835c58ed, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_STREAM_DESCRIPTOR, 0x835c58ee, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_SEQUENCE_ELEMENTID, 0x835c58ef, 0xe075, 0x4bc7, 0xbc, 0xba, 0x4d, 0xe0, 0x00, 0xdf, 0x9a, 0xe6);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_TRANSFORM_OBJECTID, 0x88dcc0c9, 0x293e, 0x4e8b, 0x9a, 0xeb, 0xa, 0xd6, 0x4c, 0xc0, 0x16, 0xb0);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_STREAMID, 0x14932f9b, 0x9087, 0x4bb4, 0x84, 0x12, 0x51, 0x67, 0x14, 0x5c, 0xbe, 0x04);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_NOSHUTDOWN_ON_REMOVE, 0x14932f9c, 0x9087, 0x4bb4, 0x84, 0x12, 0x51, 0x67, 0x14, 0x5c, 0xbe, 0x04);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_RATELESS, 0x14932f9d, 0x9087, 0x4bb4, 0x84, 0x12, 0x51, 0x67, 0x14, 0x5c, 0xbe, 0x04);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_DISABLE_PREROLL, 0x14932f9e, 0x9087, 0x4bb4, 0x84, 0x12, 0x51, 0x67, 0x14, 0x5c, 0xbe, 0x04);")
cpp_quote("EXTERN_GUID(MF_TOPONODE_PRIMARYOUTPUT, 0x6304ef99, 0x16b2, 0x4ebe, 0x9d, 0x67, 0xe4, 0xc5, 0x39, 0xb3, 0xa2, 0x59);")

cpp_quote("EXTERN_GUID(MF_TRANSCODE_SKIP_METADATA_TRANSFER, 0x4e4469ef, 0xb571, 0x4959, 0x8f, 0x83, 0x3d, 0xcf, 0xba, 0x33, 0xa3, 0x93);")
cpp_quote("EXTERN_GUID(MF_TRANSCODE_TOPOLOGYMODE, 0x3e3df610, 0x394a, 0x40b2, 0x9d, 0xea, 0x3b, 0xab, 0x65, 0xb, 0xeb, 0xf2);")
cpp_quote("EXTERN_GUID(MF_TRANSCODE_ADJUST_PROFILE, 0x9c37c21b, 0x60f, 0x487c, 0xa6, 0x90, 0x80, 0xd7, 0xf5, 0xd, 0x1c, 0x72);")
cpp_quote("EXTERN_GUID(MF_TRANSCODE_ENCODINGPROFILE, 0x6947787c, 0xf508, 0x4ea9, 0xb1, 0xe9, 0xa1, 0xfe, 0x3a, 0x49, 0xfb, 0xc9);")
cpp_quote("EXTERN_GUID(MF_TRANSCODE_QUALITYVSSPEED, 0x98332df8, 0x03cd, 0x476b, 0x89, 0xfa, 0x3f, 0x9e, 0x44, 0x2d, 0xec, 0x9f);")
cpp_quote("EXTERN_GUID(MF_TRANSCODE_DONOT_INSERT_ENCODER, 0xf45aa7ce, 0xab24, 0x4012, 0xa1, 0x1b, 0xdc, 0x82, 0x20, 0x20, 0x14, 0x10);")

cpp_quote("EXTERN_GUID(MR_AUDIO_POLICY_SERVICE, 0x911fd737, 0x6775, 0x4ab0, 0xa6, 0x14, 0x29, 0x78, 0x62, 0xfd, 0xac, 0x88);")
cpp_quote("EXTERN_GUID(MR_CAPTURE_POLICY_VOLUME_SERVICE, 0x24030acd, 0x107a, 0x4265, 0x97, 0x5c, 0x41, 0x4e, 0x33, 0xe6, 0x5f, 0x2a);")
cpp_quote("EXTERN_GUID(MR_POLICY_VOLUME_SERVICE, 0x1abaa2ac, 0x9d3b, 0x47c6, 0xab, 0x48, 0xc5, 0x95, 0x6, 0xde, 0x78, 0x4d);")
cpp_quote("EXTERN_GUID(MR_STREAM_VOLUME_SERVICE, 0xf8b5fa2f, 0x32ef, 0x46f5, 0xb1, 0x72, 0x13, 0x21, 0x21, 0x2f, 0xb2, 0xc4);")

cpp_quote("EXTERN_GUID(CLSID_VideoProcessorMFT, 0x88753b26, 0x5b24, 0x49bd, 0xb2, 0xe7, 0xc, 0x44, 0x5c, 0x78, 0xc9, 0x82);")
cpp_quote("EXTERN_GUID(CLSID_FrameServerNetworkCameraSource, 0x7a213aa7, 0x866f, 0x414a, 0x8c, 0x1a, 0x27, 0x5c, 0x72, 0x83, 0xa3, 0x95);")
cpp_quote("EXTERN_GUID(CLSID_MSH264DecoderMFT, 0x62ce7e72, 0x4c71, 0x4d20, 0xb1, 0x5d, 0x45, 0x28, 0x31, 0xa8, 0x7d, 0x9d);")
cpp_quote("EXTERN_GUID(CLSID_MSH264EncoderMFT, 0x6ca50344, 0x051a, 0x4ded, 0x97, 0x79, 0xa4, 0x33, 0x05, 0x16, 0x5e, 0x35);")
cpp_quote("EXTERN_GUID(CLSID_MSDDPlusDecMFT, 0x177c0afe, 0x900b, 0x48d4, 0x9e, 0x4c, 0x57, 0xad, 0xd2, 0x50, 0xb3, 0xd4);")
cpp_quote("EXTERN_GUID(CLSID_MP3DecMediaObject, 0xbbeea841, 0x0a63, 0x4f52, 0xa7, 0xab, 0xa9, 0xb3, 0xa8, 0x4e, 0xd3, 0x8a);")
cpp_quote("EXTERN_GUID(CLSID_MSAACDecMFT, 0x32d186a7, 0x218f, 0x4c75, 0x88, 0x76, 0xdd, 0x77, 0x27, 0x3a, 0x89, 0x99);")
cpp_quote("EXTERN_GUID(CLSID_MSH265DecoderMFT, 0x420a51a3, 0xd605, 0x430c, 0xb4, 0xfc, 0x45, 0x27, 0x4f, 0xa6, 0xc5, 0x62);")
cpp_quote("EXTERN_GUID(CLSID_WMVDecoderMFT, 0x82d353df, 0x90bd, 0x4382, 0x8b, 0xc2, 0x3f, 0x61, 0x92, 0xb7, 0x6e, 0x34);")
cpp_quote("EXTERN_GUID(CLSID_WMADecMediaObject, 0x2eeb4adf, 0x4578, 0x4d10, 0xbc, 0xa7, 0xbb, 0x95, 0x5f, 0x56, 0x32, 0x0a);")
cpp_quote("EXTERN_GUID(CLSID_MSMPEGAudDecMFT, 0x70707b39, 0xb2ca, 0x4015, 0xab, 0xea, 0xf8, 0x44, 0x7d, 0x22, 0xd8, 0x8b);")
cpp_quote("EXTERN_GUID(CLSID_MSMPEGDecoderMFT, 0x2d709e52, 0x123f, 0x49b5, 0x9c, 0xbc, 0x9a, 0xf5, 0xcd, 0xe2, 0x8f, 0xb9);")
cpp_quote("EXTERN_GUID(CLSID_AudioResamplerMediaObject, 0xf447b69e, 0x1884, 0x4a7e, 0x80, 0x55, 0x34, 0x6f, 0x74, 0xd6, 0xed, 0xb3);")
cpp_quote("EXTERN_GUID(CLSID_MSVPxDecoder, 0xe3aaf548, 0xc9a4, 0x4c6e, 0x23, 0x4d, 0x5a, 0xda, 0x37, 0x4b, 0x00, 0x00);")
cpp_quote("EXTERN_GUID(CLSID_MSOpusDecoder, 0x63e17c10, 0x2d43, 0x4c42, 0x8f, 0xe3, 0x8d, 0x8b, 0x63, 0xe4, 0x6a, 0x6a);")
