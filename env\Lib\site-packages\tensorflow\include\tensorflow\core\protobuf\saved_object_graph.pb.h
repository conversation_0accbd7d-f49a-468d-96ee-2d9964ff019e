// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/saved_object_graph.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/framework/variable.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
#include "tensorflow/core/protobuf/struct.pb.h"
#include "tensorflow/core/protobuf/trackable_object_graph.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
namespace tensorflow {
class CapturedTensor;
struct CapturedTensorDefaultTypeInternal;
extern CapturedTensorDefaultTypeInternal _CapturedTensor_default_instance_;
class FunctionSpec;
struct FunctionSpecDefaultTypeInternal;
extern FunctionSpecDefaultTypeInternal _FunctionSpec_default_instance_;
class SaveableObject;
struct SaveableObjectDefaultTypeInternal;
extern SaveableObjectDefaultTypeInternal _SaveableObject_default_instance_;
class SavedAsset;
struct SavedAssetDefaultTypeInternal;
extern SavedAssetDefaultTypeInternal _SavedAsset_default_instance_;
class SavedBareConcreteFunction;
struct SavedBareConcreteFunctionDefaultTypeInternal;
extern SavedBareConcreteFunctionDefaultTypeInternal _SavedBareConcreteFunction_default_instance_;
class SavedConcreteFunction;
struct SavedConcreteFunctionDefaultTypeInternal;
extern SavedConcreteFunctionDefaultTypeInternal _SavedConcreteFunction_default_instance_;
class SavedConstant;
struct SavedConstantDefaultTypeInternal;
extern SavedConstantDefaultTypeInternal _SavedConstant_default_instance_;
class SavedFunction;
struct SavedFunctionDefaultTypeInternal;
extern SavedFunctionDefaultTypeInternal _SavedFunction_default_instance_;
class SavedObject;
struct SavedObjectDefaultTypeInternal;
extern SavedObjectDefaultTypeInternal _SavedObject_default_instance_;
class SavedObjectGraph;
struct SavedObjectGraphDefaultTypeInternal;
extern SavedObjectGraphDefaultTypeInternal _SavedObjectGraph_default_instance_;
class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse;
struct SavedObjectGraph_ConcreteFunctionsEntry_DoNotUseDefaultTypeInternal;
extern SavedObjectGraph_ConcreteFunctionsEntry_DoNotUseDefaultTypeInternal _SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_;
class SavedObject_SaveableObjectsEntry_DoNotUse;
struct SavedObject_SaveableObjectsEntry_DoNotUseDefaultTypeInternal;
extern SavedObject_SaveableObjectsEntry_DoNotUseDefaultTypeInternal _SavedObject_SaveableObjectsEntry_DoNotUse_default_instance_;
class SavedResource;
struct SavedResourceDefaultTypeInternal;
extern SavedResourceDefaultTypeInternal _SavedResource_default_instance_;
class SavedUserObject;
struct SavedUserObjectDefaultTypeInternal;
extern SavedUserObjectDefaultTypeInternal _SavedUserObject_default_instance_;
class SavedVariable;
struct SavedVariableDefaultTypeInternal;
extern SavedVariableDefaultTypeInternal _SavedVariable_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CapturedTensor* Arena::CreateMaybeMessage<::tensorflow::CapturedTensor>(Arena*);
template<> ::tensorflow::FunctionSpec* Arena::CreateMaybeMessage<::tensorflow::FunctionSpec>(Arena*);
template<> ::tensorflow::SaveableObject* Arena::CreateMaybeMessage<::tensorflow::SaveableObject>(Arena*);
template<> ::tensorflow::SavedAsset* Arena::CreateMaybeMessage<::tensorflow::SavedAsset>(Arena*);
template<> ::tensorflow::SavedBareConcreteFunction* Arena::CreateMaybeMessage<::tensorflow::SavedBareConcreteFunction>(Arena*);
template<> ::tensorflow::SavedConcreteFunction* Arena::CreateMaybeMessage<::tensorflow::SavedConcreteFunction>(Arena*);
template<> ::tensorflow::SavedConstant* Arena::CreateMaybeMessage<::tensorflow::SavedConstant>(Arena*);
template<> ::tensorflow::SavedFunction* Arena::CreateMaybeMessage<::tensorflow::SavedFunction>(Arena*);
template<> ::tensorflow::SavedObject* Arena::CreateMaybeMessage<::tensorflow::SavedObject>(Arena*);
template<> ::tensorflow::SavedObjectGraph* Arena::CreateMaybeMessage<::tensorflow::SavedObjectGraph>(Arena*);
template<> ::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SavedObject_SaveableObjectsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SavedObject_SaveableObjectsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SavedResource* Arena::CreateMaybeMessage<::tensorflow::SavedResource>(Arena*);
template<> ::tensorflow::SavedUserObject* Arena::CreateMaybeMessage<::tensorflow::SavedUserObject>(Arena*);
template<> ::tensorflow::SavedVariable* Arena::CreateMaybeMessage<::tensorflow::SavedVariable>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum FunctionSpec_JitCompile : int {
  FunctionSpec_JitCompile_DEFAULT = 0,
  FunctionSpec_JitCompile_ON = 1,
  FunctionSpec_JitCompile_OFF = 2,
  FunctionSpec_JitCompile_FunctionSpec_JitCompile_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  FunctionSpec_JitCompile_FunctionSpec_JitCompile_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool FunctionSpec_JitCompile_IsValid(int value);
constexpr FunctionSpec_JitCompile FunctionSpec_JitCompile_JitCompile_MIN = FunctionSpec_JitCompile_DEFAULT;
constexpr FunctionSpec_JitCompile FunctionSpec_JitCompile_JitCompile_MAX = FunctionSpec_JitCompile_OFF;
constexpr int FunctionSpec_JitCompile_JitCompile_ARRAYSIZE = FunctionSpec_JitCompile_JitCompile_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* FunctionSpec_JitCompile_descriptor();
template<typename T>
inline const std::string& FunctionSpec_JitCompile_Name(T enum_t_value) {
  static_assert(::std::is_same<T, FunctionSpec_JitCompile>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function FunctionSpec_JitCompile_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    FunctionSpec_JitCompile_descriptor(), enum_t_value);
}
inline bool FunctionSpec_JitCompile_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, FunctionSpec_JitCompile* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<FunctionSpec_JitCompile>(
    FunctionSpec_JitCompile_descriptor(), name, value);
}
// ===================================================================

class SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, 
    std::string, ::tensorflow::SavedConcreteFunction,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse, 
    std::string, ::tensorflow::SavedConcreteFunction,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse& other);
  static const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse*>(&_SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.SavedObjectGraph.ConcreteFunctionsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};

// -------------------------------------------------------------------

class SavedObjectGraph final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedObjectGraph) */ {
 public:
  inline SavedObjectGraph() : SavedObjectGraph(nullptr) {}
  ~SavedObjectGraph() override;
  explicit PROTOBUF_CONSTEXPR SavedObjectGraph(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedObjectGraph(const SavedObjectGraph& from);
  SavedObjectGraph(SavedObjectGraph&& from) noexcept
    : SavedObjectGraph() {
    *this = ::std::move(from);
  }

  inline SavedObjectGraph& operator=(const SavedObjectGraph& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedObjectGraph& operator=(SavedObjectGraph&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedObjectGraph& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedObjectGraph* internal_default_instance() {
    return reinterpret_cast<const SavedObjectGraph*>(
               &_SavedObjectGraph_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SavedObjectGraph& a, SavedObjectGraph& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedObjectGraph* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedObjectGraph* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedObjectGraph* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedObjectGraph>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedObjectGraph& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedObjectGraph& from) {
    SavedObjectGraph::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedObjectGraph* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedObjectGraph";
  }
  protected:
  explicit SavedObjectGraph(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 1,
    kConcreteFunctionsFieldNumber = 2,
  };
  // repeated .tensorflow.SavedObject nodes = 1;
  int nodes_size() const;
  private:
  int _internal_nodes_size() const;
  public:
  void clear_nodes();
  ::tensorflow::SavedObject* mutable_nodes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject >*
      mutable_nodes();
  private:
  const ::tensorflow::SavedObject& _internal_nodes(int index) const;
  ::tensorflow::SavedObject* _internal_add_nodes();
  public:
  const ::tensorflow::SavedObject& nodes(int index) const;
  ::tensorflow::SavedObject* add_nodes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject >&
      nodes() const;

  // map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
  int concrete_functions_size() const;
  private:
  int _internal_concrete_functions_size() const;
  public:
  void clear_concrete_functions();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >&
      _internal_concrete_functions() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >*
      _internal_mutable_concrete_functions();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >&
      concrete_functions() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >*
      mutable_concrete_functions();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedObjectGraph)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject > nodes_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        SavedObjectGraph_ConcreteFunctionsEntry_DoNotUse,
        std::string, ::tensorflow::SavedConcreteFunction,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> concrete_functions_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedObject_SaveableObjectsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SavedObject_SaveableObjectsEntry_DoNotUse, 
    std::string, ::tensorflow::SaveableObject,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SavedObject_SaveableObjectsEntry_DoNotUse, 
    std::string, ::tensorflow::SaveableObject,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  SavedObject_SaveableObjectsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR SavedObject_SaveableObjectsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit SavedObject_SaveableObjectsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const SavedObject_SaveableObjectsEntry_DoNotUse& other);
  static const SavedObject_SaveableObjectsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SavedObject_SaveableObjectsEntry_DoNotUse*>(&_SavedObject_SaveableObjectsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.SavedObject.SaveableObjectsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};

// -------------------------------------------------------------------

class SavedObject final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedObject) */ {
 public:
  inline SavedObject() : SavedObject(nullptr) {}
  ~SavedObject() override;
  explicit PROTOBUF_CONSTEXPR SavedObject(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedObject(const SavedObject& from);
  SavedObject(SavedObject&& from) noexcept
    : SavedObject() {
    *this = ::std::move(from);
  }

  inline SavedObject& operator=(const SavedObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedObject& operator=(SavedObject&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedObject& default_instance() {
    return *internal_default_instance();
  }
  enum KindCase {
    kUserObject = 4,
    kAsset = 5,
    kFunction = 6,
    kVariable = 7,
    kBareConcreteFunction = 8,
    kConstant = 9,
    kResource = 10,
    kCapturedTensor = 12,
    KIND_NOT_SET = 0,
  };

  static inline const SavedObject* internal_default_instance() {
    return reinterpret_cast<const SavedObject*>(
               &_SavedObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SavedObject& a, SavedObject& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedObject* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedObject>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedObject& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedObject& from) {
    SavedObject::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedObject* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedObject";
  }
  protected:
  explicit SavedObject(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kChildrenFieldNumber = 1,
    kSlotVariablesFieldNumber = 3,
    kSaveableObjectsFieldNumber = 11,
    kDependenciesFieldNumber = 15,
    kRegisteredNameFieldNumber = 13,
    kRegisteredSaverFieldNumber = 16,
    kSerializedUserProtoFieldNumber = 14,
    kUserObjectFieldNumber = 4,
    kAssetFieldNumber = 5,
    kFunctionFieldNumber = 6,
    kVariableFieldNumber = 7,
    kBareConcreteFunctionFieldNumber = 8,
    kConstantFieldNumber = 9,
    kResourceFieldNumber = 10,
    kCapturedTensorFieldNumber = 12,
  };
  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
  int children_size() const;
  private:
  int _internal_children_size() const;
  public:
  void clear_children();
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
      mutable_children();
  private:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& _internal_children(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* _internal_add_children();
  public:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& children(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
      children() const;

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
  int slot_variables_size() const;
  private:
  int _internal_slot_variables_size() const;
  public:
  void clear_slot_variables();
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* mutable_slot_variables(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
      mutable_slot_variables();
  private:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& _internal_slot_variables(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* _internal_add_slot_variables();
  public:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& slot_variables(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* add_slot_variables();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
      slot_variables() const;

  // map<string, .tensorflow.SaveableObject> saveable_objects = 11;
  int saveable_objects_size() const;
  private:
  int _internal_saveable_objects_size() const;
  public:
  void clear_saveable_objects();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >&
      _internal_saveable_objects() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >*
      _internal_mutable_saveable_objects();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >&
      saveable_objects() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >*
      mutable_saveable_objects();

  // repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference dependencies = 15;
  int dependencies_size() const;
  private:
  int _internal_dependencies_size() const;
  public:
  void clear_dependencies();
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* mutable_dependencies(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
      mutable_dependencies();
  private:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& _internal_dependencies(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* _internal_add_dependencies();
  public:
  const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& dependencies(int index) const;
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* add_dependencies();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
      dependencies() const;

  // string registered_name = 13;
  void clear_registered_name();
  const std::string& registered_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_registered_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_registered_name();
  PROTOBUF_NODISCARD std::string* release_registered_name();
  void set_allocated_registered_name(std::string* registered_name);
  private:
  const std::string& _internal_registered_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_registered_name(const std::string& value);
  std::string* _internal_mutable_registered_name();
  public:

  // string registered_saver = 16;
  void clear_registered_saver();
  const std::string& registered_saver() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_registered_saver(ArgT0&& arg0, ArgT... args);
  std::string* mutable_registered_saver();
  PROTOBUF_NODISCARD std::string* release_registered_saver();
  void set_allocated_registered_saver(std::string* registered_saver);
  private:
  const std::string& _internal_registered_saver() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_registered_saver(const std::string& value);
  std::string* _internal_mutable_registered_saver();
  public:

  // .google.protobuf.Any serialized_user_proto = 14;
  bool has_serialized_user_proto() const;
  private:
  bool _internal_has_serialized_user_proto() const;
  public:
  void clear_serialized_user_proto();
  const ::PROTOBUF_NAMESPACE_ID::Any& serialized_user_proto() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_serialized_user_proto();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_serialized_user_proto();
  void set_allocated_serialized_user_proto(::PROTOBUF_NAMESPACE_ID::Any* serialized_user_proto);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_serialized_user_proto() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_serialized_user_proto();
  public:
  void unsafe_arena_set_allocated_serialized_user_proto(
      ::PROTOBUF_NAMESPACE_ID::Any* serialized_user_proto);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_serialized_user_proto();

  // .tensorflow.SavedUserObject user_object = 4;
  bool has_user_object() const;
  private:
  bool _internal_has_user_object() const;
  public:
  void clear_user_object();
  const ::tensorflow::SavedUserObject& user_object() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedUserObject* release_user_object();
  ::tensorflow::SavedUserObject* mutable_user_object();
  void set_allocated_user_object(::tensorflow::SavedUserObject* user_object);
  private:
  const ::tensorflow::SavedUserObject& _internal_user_object() const;
  ::tensorflow::SavedUserObject* _internal_mutable_user_object();
  public:
  void unsafe_arena_set_allocated_user_object(
      ::tensorflow::SavedUserObject* user_object);
  ::tensorflow::SavedUserObject* unsafe_arena_release_user_object();

  // .tensorflow.SavedAsset asset = 5;
  bool has_asset() const;
  private:
  bool _internal_has_asset() const;
  public:
  void clear_asset();
  const ::tensorflow::SavedAsset& asset() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedAsset* release_asset();
  ::tensorflow::SavedAsset* mutable_asset();
  void set_allocated_asset(::tensorflow::SavedAsset* asset);
  private:
  const ::tensorflow::SavedAsset& _internal_asset() const;
  ::tensorflow::SavedAsset* _internal_mutable_asset();
  public:
  void unsafe_arena_set_allocated_asset(
      ::tensorflow::SavedAsset* asset);
  ::tensorflow::SavedAsset* unsafe_arena_release_asset();

  // .tensorflow.SavedFunction function = 6;
  bool has_function() const;
  private:
  bool _internal_has_function() const;
  public:
  void clear_function();
  const ::tensorflow::SavedFunction& function() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedFunction* release_function();
  ::tensorflow::SavedFunction* mutable_function();
  void set_allocated_function(::tensorflow::SavedFunction* function);
  private:
  const ::tensorflow::SavedFunction& _internal_function() const;
  ::tensorflow::SavedFunction* _internal_mutable_function();
  public:
  void unsafe_arena_set_allocated_function(
      ::tensorflow::SavedFunction* function);
  ::tensorflow::SavedFunction* unsafe_arena_release_function();

  // .tensorflow.SavedVariable variable = 7;
  bool has_variable() const;
  private:
  bool _internal_has_variable() const;
  public:
  void clear_variable();
  const ::tensorflow::SavedVariable& variable() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedVariable* release_variable();
  ::tensorflow::SavedVariable* mutable_variable();
  void set_allocated_variable(::tensorflow::SavedVariable* variable);
  private:
  const ::tensorflow::SavedVariable& _internal_variable() const;
  ::tensorflow::SavedVariable* _internal_mutable_variable();
  public:
  void unsafe_arena_set_allocated_variable(
      ::tensorflow::SavedVariable* variable);
  ::tensorflow::SavedVariable* unsafe_arena_release_variable();

  // .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
  bool has_bare_concrete_function() const;
  private:
  bool _internal_has_bare_concrete_function() const;
  public:
  void clear_bare_concrete_function();
  const ::tensorflow::SavedBareConcreteFunction& bare_concrete_function() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedBareConcreteFunction* release_bare_concrete_function();
  ::tensorflow::SavedBareConcreteFunction* mutable_bare_concrete_function();
  void set_allocated_bare_concrete_function(::tensorflow::SavedBareConcreteFunction* bare_concrete_function);
  private:
  const ::tensorflow::SavedBareConcreteFunction& _internal_bare_concrete_function() const;
  ::tensorflow::SavedBareConcreteFunction* _internal_mutable_bare_concrete_function();
  public:
  void unsafe_arena_set_allocated_bare_concrete_function(
      ::tensorflow::SavedBareConcreteFunction* bare_concrete_function);
  ::tensorflow::SavedBareConcreteFunction* unsafe_arena_release_bare_concrete_function();

  // .tensorflow.SavedConstant constant = 9;
  bool has_constant() const;
  private:
  bool _internal_has_constant() const;
  public:
  void clear_constant();
  const ::tensorflow::SavedConstant& constant() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedConstant* release_constant();
  ::tensorflow::SavedConstant* mutable_constant();
  void set_allocated_constant(::tensorflow::SavedConstant* constant);
  private:
  const ::tensorflow::SavedConstant& _internal_constant() const;
  ::tensorflow::SavedConstant* _internal_mutable_constant();
  public:
  void unsafe_arena_set_allocated_constant(
      ::tensorflow::SavedConstant* constant);
  ::tensorflow::SavedConstant* unsafe_arena_release_constant();

  // .tensorflow.SavedResource resource = 10;
  bool has_resource() const;
  private:
  bool _internal_has_resource() const;
  public:
  void clear_resource();
  const ::tensorflow::SavedResource& resource() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedResource* release_resource();
  ::tensorflow::SavedResource* mutable_resource();
  void set_allocated_resource(::tensorflow::SavedResource* resource);
  private:
  const ::tensorflow::SavedResource& _internal_resource() const;
  ::tensorflow::SavedResource* _internal_mutable_resource();
  public:
  void unsafe_arena_set_allocated_resource(
      ::tensorflow::SavedResource* resource);
  ::tensorflow::SavedResource* unsafe_arena_release_resource();

  // .tensorflow.CapturedTensor captured_tensor = 12;
  bool has_captured_tensor() const;
  private:
  bool _internal_has_captured_tensor() const;
  public:
  void clear_captured_tensor();
  const ::tensorflow::CapturedTensor& captured_tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::CapturedTensor* release_captured_tensor();
  ::tensorflow::CapturedTensor* mutable_captured_tensor();
  void set_allocated_captured_tensor(::tensorflow::CapturedTensor* captured_tensor);
  private:
  const ::tensorflow::CapturedTensor& _internal_captured_tensor() const;
  ::tensorflow::CapturedTensor* _internal_mutable_captured_tensor();
  public:
  void unsafe_arena_set_allocated_captured_tensor(
      ::tensorflow::CapturedTensor* captured_tensor);
  ::tensorflow::CapturedTensor* unsafe_arena_release_captured_tensor();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.SavedObject)
 private:
  class _Internal;
  void set_has_user_object();
  void set_has_asset();
  void set_has_function();
  void set_has_variable();
  void set_has_bare_concrete_function();
  void set_has_constant();
  void set_has_resource();
  void set_has_captured_tensor();

  inline bool has_kind() const;
  inline void clear_has_kind();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference > children_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference > slot_variables_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        SavedObject_SaveableObjectsEntry_DoNotUse,
        std::string, ::tensorflow::SaveableObject,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> saveable_objects_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference > dependencies_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr registered_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr registered_saver_;
    ::PROTOBUF_NAMESPACE_ID::Any* serialized_user_proto_;
    union KindUnion {
      constexpr KindUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::SavedUserObject* user_object_;
      ::tensorflow::SavedAsset* asset_;
      ::tensorflow::SavedFunction* function_;
      ::tensorflow::SavedVariable* variable_;
      ::tensorflow::SavedBareConcreteFunction* bare_concrete_function_;
      ::tensorflow::SavedConstant* constant_;
      ::tensorflow::SavedResource* resource_;
      ::tensorflow::CapturedTensor* captured_tensor_;
    } kind_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedUserObject final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedUserObject) */ {
 public:
  inline SavedUserObject() : SavedUserObject(nullptr) {}
  ~SavedUserObject() override;
  explicit PROTOBUF_CONSTEXPR SavedUserObject(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedUserObject(const SavedUserObject& from);
  SavedUserObject(SavedUserObject&& from) noexcept
    : SavedUserObject() {
    *this = ::std::move(from);
  }

  inline SavedUserObject& operator=(const SavedUserObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedUserObject& operator=(SavedUserObject&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedUserObject& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedUserObject* internal_default_instance() {
    return reinterpret_cast<const SavedUserObject*>(
               &_SavedUserObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SavedUserObject& a, SavedUserObject& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedUserObject* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedUserObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedUserObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedUserObject>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedUserObject& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedUserObject& from) {
    SavedUserObject::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedUserObject* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedUserObject";
  }
  protected:
  explicit SavedUserObject(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdentifierFieldNumber = 1,
    kMetadataFieldNumber = 3,
    kVersionFieldNumber = 2,
  };
  // string identifier = 1;
  void clear_identifier();
  const std::string& identifier() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_identifier(ArgT0&& arg0, ArgT... args);
  std::string* mutable_identifier();
  PROTOBUF_NODISCARD std::string* release_identifier();
  void set_allocated_identifier(std::string* identifier);
  private:
  const std::string& _internal_identifier() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_identifier(const std::string& value);
  std::string* _internal_mutable_identifier();
  public:

  // string metadata = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_metadata();
  PROTOBUF_DEPRECATED const std::string& metadata() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_metadata(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_metadata();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_metadata();
  PROTOBUF_DEPRECATED void set_allocated_metadata(std::string* metadata);
  private:
  const std::string& _internal_metadata() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_metadata(const std::string& value);
  std::string* _internal_mutable_metadata();
  public:

  // .tensorflow.VersionDef version = 2;
  bool has_version() const;
  private:
  bool _internal_has_version() const;
  public:
  void clear_version();
  const ::tensorflow::VersionDef& version() const;
  PROTOBUF_NODISCARD ::tensorflow::VersionDef* release_version();
  ::tensorflow::VersionDef* mutable_version();
  void set_allocated_version(::tensorflow::VersionDef* version);
  private:
  const ::tensorflow::VersionDef& _internal_version() const;
  ::tensorflow::VersionDef* _internal_mutable_version();
  public:
  void unsafe_arena_set_allocated_version(
      ::tensorflow::VersionDef* version);
  ::tensorflow::VersionDef* unsafe_arena_release_version();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedUserObject)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr identifier_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr metadata_;
    ::tensorflow::VersionDef* version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedAsset final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedAsset) */ {
 public:
  inline SavedAsset() : SavedAsset(nullptr) {}
  ~SavedAsset() override;
  explicit PROTOBUF_CONSTEXPR SavedAsset(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedAsset(const SavedAsset& from);
  SavedAsset(SavedAsset&& from) noexcept
    : SavedAsset() {
    *this = ::std::move(from);
  }

  inline SavedAsset& operator=(const SavedAsset& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedAsset& operator=(SavedAsset&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedAsset& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedAsset* internal_default_instance() {
    return reinterpret_cast<const SavedAsset*>(
               &_SavedAsset_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(SavedAsset& a, SavedAsset& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedAsset* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedAsset* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedAsset* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedAsset>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedAsset& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedAsset& from) {
    SavedAsset::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedAsset* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedAsset";
  }
  protected:
  explicit SavedAsset(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAssetFileDefIndexFieldNumber = 1,
  };
  // int32 asset_file_def_index = 1;
  void clear_asset_file_def_index();
  int32_t asset_file_def_index() const;
  void set_asset_file_def_index(int32_t value);
  private:
  int32_t _internal_asset_file_def_index() const;
  void _internal_set_asset_file_def_index(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SavedAsset)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t asset_file_def_index_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedFunction final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedFunction) */ {
 public:
  inline SavedFunction() : SavedFunction(nullptr) {}
  ~SavedFunction() override;
  explicit PROTOBUF_CONSTEXPR SavedFunction(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedFunction(const SavedFunction& from);
  SavedFunction(SavedFunction&& from) noexcept
    : SavedFunction() {
    *this = ::std::move(from);
  }

  inline SavedFunction& operator=(const SavedFunction& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedFunction& operator=(SavedFunction&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedFunction& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedFunction* internal_default_instance() {
    return reinterpret_cast<const SavedFunction*>(
               &_SavedFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(SavedFunction& a, SavedFunction& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedFunction* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedFunction* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedFunction* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedFunction>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedFunction& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedFunction& from) {
    SavedFunction::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedFunction* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedFunction";
  }
  protected:
  explicit SavedFunction(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConcreteFunctionsFieldNumber = 1,
    kFunctionSpecFieldNumber = 2,
  };
  // repeated string concrete_functions = 1;
  int concrete_functions_size() const;
  private:
  int _internal_concrete_functions_size() const;
  public:
  void clear_concrete_functions();
  const std::string& concrete_functions(int index) const;
  std::string* mutable_concrete_functions(int index);
  void set_concrete_functions(int index, const std::string& value);
  void set_concrete_functions(int index, std::string&& value);
  void set_concrete_functions(int index, const char* value);
  void set_concrete_functions(int index, const char* value, size_t size);
  std::string* add_concrete_functions();
  void add_concrete_functions(const std::string& value);
  void add_concrete_functions(std::string&& value);
  void add_concrete_functions(const char* value);
  void add_concrete_functions(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& concrete_functions() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_concrete_functions();
  private:
  const std::string& _internal_concrete_functions(int index) const;
  std::string* _internal_add_concrete_functions();
  public:

  // .tensorflow.FunctionSpec function_spec = 2;
  bool has_function_spec() const;
  private:
  bool _internal_has_function_spec() const;
  public:
  void clear_function_spec();
  const ::tensorflow::FunctionSpec& function_spec() const;
  PROTOBUF_NODISCARD ::tensorflow::FunctionSpec* release_function_spec();
  ::tensorflow::FunctionSpec* mutable_function_spec();
  void set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec);
  private:
  const ::tensorflow::FunctionSpec& _internal_function_spec() const;
  ::tensorflow::FunctionSpec* _internal_mutable_function_spec();
  public:
  void unsafe_arena_set_allocated_function_spec(
      ::tensorflow::FunctionSpec* function_spec);
  ::tensorflow::FunctionSpec* unsafe_arena_release_function_spec();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedFunction)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> concrete_functions_;
    ::tensorflow::FunctionSpec* function_spec_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CapturedTensor final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CapturedTensor) */ {
 public:
  inline CapturedTensor() : CapturedTensor(nullptr) {}
  ~CapturedTensor() override;
  explicit PROTOBUF_CONSTEXPR CapturedTensor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CapturedTensor(const CapturedTensor& from);
  CapturedTensor(CapturedTensor&& from) noexcept
    : CapturedTensor() {
    *this = ::std::move(from);
  }

  inline CapturedTensor& operator=(const CapturedTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline CapturedTensor& operator=(CapturedTensor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CapturedTensor& default_instance() {
    return *internal_default_instance();
  }
  static inline const CapturedTensor* internal_default_instance() {
    return reinterpret_cast<const CapturedTensor*>(
               &_CapturedTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(CapturedTensor& a, CapturedTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(CapturedTensor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CapturedTensor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CapturedTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CapturedTensor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CapturedTensor& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CapturedTensor& from) {
    CapturedTensor::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CapturedTensor* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CapturedTensor";
  }
  protected:
  explicit CapturedTensor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kConcreteFunctionFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string concrete_function = 2;
  void clear_concrete_function();
  const std::string& concrete_function() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_concrete_function(ArgT0&& arg0, ArgT... args);
  std::string* mutable_concrete_function();
  PROTOBUF_NODISCARD std::string* release_concrete_function();
  void set_allocated_concrete_function(std::string* concrete_function);
  private:
  const std::string& _internal_concrete_function() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_concrete_function(const std::string& value);
  std::string* _internal_mutable_concrete_function();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CapturedTensor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr concrete_function_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedConcreteFunction final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedConcreteFunction) */ {
 public:
  inline SavedConcreteFunction() : SavedConcreteFunction(nullptr) {}
  ~SavedConcreteFunction() override;
  explicit PROTOBUF_CONSTEXPR SavedConcreteFunction(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedConcreteFunction(const SavedConcreteFunction& from);
  SavedConcreteFunction(SavedConcreteFunction&& from) noexcept
    : SavedConcreteFunction() {
    *this = ::std::move(from);
  }

  inline SavedConcreteFunction& operator=(const SavedConcreteFunction& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedConcreteFunction& operator=(SavedConcreteFunction&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedConcreteFunction& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedConcreteFunction* internal_default_instance() {
    return reinterpret_cast<const SavedConcreteFunction*>(
               &_SavedConcreteFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(SavedConcreteFunction& a, SavedConcreteFunction& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedConcreteFunction* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedConcreteFunction* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedConcreteFunction* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedConcreteFunction>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedConcreteFunction& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedConcreteFunction& from) {
    SavedConcreteFunction::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedConcreteFunction* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedConcreteFunction";
  }
  protected:
  explicit SavedConcreteFunction(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBoundInputsFieldNumber = 2,
    kCanonicalizedInputSignatureFieldNumber = 3,
    kOutputSignatureFieldNumber = 4,
  };
  // repeated int32 bound_inputs = 2;
  int bound_inputs_size() const;
  private:
  int _internal_bound_inputs_size() const;
  public:
  void clear_bound_inputs();
  private:
  int32_t _internal_bound_inputs(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_bound_inputs() const;
  void _internal_add_bound_inputs(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_bound_inputs();
  public:
  int32_t bound_inputs(int index) const;
  void set_bound_inputs(int index, int32_t value);
  void add_bound_inputs(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      bound_inputs() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_bound_inputs();

  // .tensorflow.StructuredValue canonicalized_input_signature = 3;
  bool has_canonicalized_input_signature() const;
  private:
  bool _internal_has_canonicalized_input_signature() const;
  public:
  void clear_canonicalized_input_signature();
  const ::tensorflow::StructuredValue& canonicalized_input_signature() const;
  PROTOBUF_NODISCARD ::tensorflow::StructuredValue* release_canonicalized_input_signature();
  ::tensorflow::StructuredValue* mutable_canonicalized_input_signature();
  void set_allocated_canonicalized_input_signature(::tensorflow::StructuredValue* canonicalized_input_signature);
  private:
  const ::tensorflow::StructuredValue& _internal_canonicalized_input_signature() const;
  ::tensorflow::StructuredValue* _internal_mutable_canonicalized_input_signature();
  public:
  void unsafe_arena_set_allocated_canonicalized_input_signature(
      ::tensorflow::StructuredValue* canonicalized_input_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_canonicalized_input_signature();

  // .tensorflow.StructuredValue output_signature = 4;
  bool has_output_signature() const;
  private:
  bool _internal_has_output_signature() const;
  public:
  void clear_output_signature();
  const ::tensorflow::StructuredValue& output_signature() const;
  PROTOBUF_NODISCARD ::tensorflow::StructuredValue* release_output_signature();
  ::tensorflow::StructuredValue* mutable_output_signature();
  void set_allocated_output_signature(::tensorflow::StructuredValue* output_signature);
  private:
  const ::tensorflow::StructuredValue& _internal_output_signature() const;
  ::tensorflow::StructuredValue* _internal_mutable_output_signature();
  public:
  void unsafe_arena_set_allocated_output_signature(
      ::tensorflow::StructuredValue* output_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_output_signature();

  // @@protoc_insertion_point(class_scope:tensorflow.SavedConcreteFunction)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > bound_inputs_;
    mutable std::atomic<int> _bound_inputs_cached_byte_size_;
    ::tensorflow::StructuredValue* canonicalized_input_signature_;
    ::tensorflow::StructuredValue* output_signature_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedBareConcreteFunction final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedBareConcreteFunction) */ {
 public:
  inline SavedBareConcreteFunction() : SavedBareConcreteFunction(nullptr) {}
  ~SavedBareConcreteFunction() override;
  explicit PROTOBUF_CONSTEXPR SavedBareConcreteFunction(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedBareConcreteFunction(const SavedBareConcreteFunction& from);
  SavedBareConcreteFunction(SavedBareConcreteFunction&& from) noexcept
    : SavedBareConcreteFunction() {
    *this = ::std::move(from);
  }

  inline SavedBareConcreteFunction& operator=(const SavedBareConcreteFunction& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedBareConcreteFunction& operator=(SavedBareConcreteFunction&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedBareConcreteFunction& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedBareConcreteFunction* internal_default_instance() {
    return reinterpret_cast<const SavedBareConcreteFunction*>(
               &_SavedBareConcreteFunction_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(SavedBareConcreteFunction& a, SavedBareConcreteFunction& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedBareConcreteFunction* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedBareConcreteFunction* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedBareConcreteFunction* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedBareConcreteFunction>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedBareConcreteFunction& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedBareConcreteFunction& from) {
    SavedBareConcreteFunction::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedBareConcreteFunction* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedBareConcreteFunction";
  }
  protected:
  explicit SavedBareConcreteFunction(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgumentKeywordsFieldNumber = 2,
    kConcreteFunctionNameFieldNumber = 1,
    kFunctionSpecFieldNumber = 4,
    kAllowedPositionalArgumentsFieldNumber = 3,
  };
  // repeated string argument_keywords = 2;
  int argument_keywords_size() const;
  private:
  int _internal_argument_keywords_size() const;
  public:
  void clear_argument_keywords();
  const std::string& argument_keywords(int index) const;
  std::string* mutable_argument_keywords(int index);
  void set_argument_keywords(int index, const std::string& value);
  void set_argument_keywords(int index, std::string&& value);
  void set_argument_keywords(int index, const char* value);
  void set_argument_keywords(int index, const char* value, size_t size);
  std::string* add_argument_keywords();
  void add_argument_keywords(const std::string& value);
  void add_argument_keywords(std::string&& value);
  void add_argument_keywords(const char* value);
  void add_argument_keywords(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& argument_keywords() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_argument_keywords();
  private:
  const std::string& _internal_argument_keywords(int index) const;
  std::string* _internal_add_argument_keywords();
  public:

  // string concrete_function_name = 1;
  void clear_concrete_function_name();
  const std::string& concrete_function_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_concrete_function_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_concrete_function_name();
  PROTOBUF_NODISCARD std::string* release_concrete_function_name();
  void set_allocated_concrete_function_name(std::string* concrete_function_name);
  private:
  const std::string& _internal_concrete_function_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_concrete_function_name(const std::string& value);
  std::string* _internal_mutable_concrete_function_name();
  public:

  // .tensorflow.FunctionSpec function_spec = 4;
  bool has_function_spec() const;
  private:
  bool _internal_has_function_spec() const;
  public:
  void clear_function_spec();
  const ::tensorflow::FunctionSpec& function_spec() const;
  PROTOBUF_NODISCARD ::tensorflow::FunctionSpec* release_function_spec();
  ::tensorflow::FunctionSpec* mutable_function_spec();
  void set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec);
  private:
  const ::tensorflow::FunctionSpec& _internal_function_spec() const;
  ::tensorflow::FunctionSpec* _internal_mutable_function_spec();
  public:
  void unsafe_arena_set_allocated_function_spec(
      ::tensorflow::FunctionSpec* function_spec);
  ::tensorflow::FunctionSpec* unsafe_arena_release_function_spec();

  // int64 allowed_positional_arguments = 3;
  void clear_allowed_positional_arguments();
  int64_t allowed_positional_arguments() const;
  void set_allowed_positional_arguments(int64_t value);
  private:
  int64_t _internal_allowed_positional_arguments() const;
  void _internal_set_allowed_positional_arguments(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SavedBareConcreteFunction)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> argument_keywords_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr concrete_function_name_;
    ::tensorflow::FunctionSpec* function_spec_;
    int64_t allowed_positional_arguments_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedConstant final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedConstant) */ {
 public:
  inline SavedConstant() : SavedConstant(nullptr) {}
  ~SavedConstant() override;
  explicit PROTOBUF_CONSTEXPR SavedConstant(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedConstant(const SavedConstant& from);
  SavedConstant(SavedConstant&& from) noexcept
    : SavedConstant() {
    *this = ::std::move(from);
  }

  inline SavedConstant& operator=(const SavedConstant& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedConstant& operator=(SavedConstant&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedConstant& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedConstant* internal_default_instance() {
    return reinterpret_cast<const SavedConstant*>(
               &_SavedConstant_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(SavedConstant& a, SavedConstant& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedConstant* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedConstant* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedConstant* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedConstant>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedConstant& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedConstant& from) {
    SavedConstant::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedConstant* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedConstant";
  }
  protected:
  explicit SavedConstant(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOperationFieldNumber = 1,
  };
  // string operation = 1;
  void clear_operation();
  const std::string& operation() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_operation(ArgT0&& arg0, ArgT... args);
  std::string* mutable_operation();
  PROTOBUF_NODISCARD std::string* release_operation();
  void set_allocated_operation(std::string* operation);
  private:
  const std::string& _internal_operation() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_operation(const std::string& value);
  std::string* _internal_mutable_operation();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SavedConstant)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr operation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedVariable final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedVariable) */ {
 public:
  inline SavedVariable() : SavedVariable(nullptr) {}
  ~SavedVariable() override;
  explicit PROTOBUF_CONSTEXPR SavedVariable(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedVariable(const SavedVariable& from);
  SavedVariable(SavedVariable&& from) noexcept
    : SavedVariable() {
    *this = ::std::move(from);
  }

  inline SavedVariable& operator=(const SavedVariable& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedVariable& operator=(SavedVariable&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedVariable& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedVariable* internal_default_instance() {
    return reinterpret_cast<const SavedVariable*>(
               &_SavedVariable_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(SavedVariable& a, SavedVariable& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedVariable* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedVariable* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedVariable* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedVariable>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedVariable& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedVariable& from) {
    SavedVariable::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedVariable* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedVariable";
  }
  protected:
  explicit SavedVariable(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExperimentalDistributedVariableComponentsFieldNumber = 8,
    kNameFieldNumber = 6,
    kDeviceFieldNumber = 7,
    kShapeFieldNumber = 2,
    kDtypeFieldNumber = 1,
    kTrainableFieldNumber = 3,
    kSynchronizationFieldNumber = 4,
    kAggregationFieldNumber = 5,
  };
  // repeated .tensorflow.SavedVariable experimental_distributed_variable_components = 8;
  int experimental_distributed_variable_components_size() const;
  private:
  int _internal_experimental_distributed_variable_components_size() const;
  public:
  void clear_experimental_distributed_variable_components();
  ::tensorflow::SavedVariable* mutable_experimental_distributed_variable_components(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable >*
      mutable_experimental_distributed_variable_components();
  private:
  const ::tensorflow::SavedVariable& _internal_experimental_distributed_variable_components(int index) const;
  ::tensorflow::SavedVariable* _internal_add_experimental_distributed_variable_components();
  public:
  const ::tensorflow::SavedVariable& experimental_distributed_variable_components(int index) const;
  ::tensorflow::SavedVariable* add_experimental_distributed_variable_components();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable >&
      experimental_distributed_variable_components() const;

  // string name = 6;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string device = 7;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // bool trainable = 3;
  void clear_trainable();
  bool trainable() const;
  void set_trainable(bool value);
  private:
  bool _internal_trainable() const;
  void _internal_set_trainable(bool value);
  public:

  // .tensorflow.VariableSynchronization synchronization = 4;
  void clear_synchronization();
  ::tensorflow::VariableSynchronization synchronization() const;
  void set_synchronization(::tensorflow::VariableSynchronization value);
  private:
  ::tensorflow::VariableSynchronization _internal_synchronization() const;
  void _internal_set_synchronization(::tensorflow::VariableSynchronization value);
  public:

  // .tensorflow.VariableAggregation aggregation = 5;
  void clear_aggregation();
  ::tensorflow::VariableAggregation aggregation() const;
  void set_aggregation(::tensorflow::VariableAggregation value);
  private:
  ::tensorflow::VariableAggregation _internal_aggregation() const;
  void _internal_set_aggregation(::tensorflow::VariableAggregation value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SavedVariable)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable > experimental_distributed_variable_components_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    ::tensorflow::TensorShapeProto* shape_;
    int dtype_;
    bool trainable_;
    int synchronization_;
    int aggregation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class FunctionSpec final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.FunctionSpec) */ {
 public:
  inline FunctionSpec() : FunctionSpec(nullptr) {}
  ~FunctionSpec() override;
  explicit PROTOBUF_CONSTEXPR FunctionSpec(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FunctionSpec(const FunctionSpec& from);
  FunctionSpec(FunctionSpec&& from) noexcept
    : FunctionSpec() {
    *this = ::std::move(from);
  }

  inline FunctionSpec& operator=(const FunctionSpec& from) {
    CopyFrom(from);
    return *this;
  }
  inline FunctionSpec& operator=(FunctionSpec&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FunctionSpec& default_instance() {
    return *internal_default_instance();
  }
  static inline const FunctionSpec* internal_default_instance() {
    return reinterpret_cast<const FunctionSpec*>(
               &_FunctionSpec_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(FunctionSpec& a, FunctionSpec& b) {
    a.Swap(&b);
  }
  inline void Swap(FunctionSpec* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FunctionSpec* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FunctionSpec* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FunctionSpec>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FunctionSpec& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FunctionSpec& from) {
    FunctionSpec::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FunctionSpec* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.FunctionSpec";
  }
  protected:
  explicit FunctionSpec(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef FunctionSpec_JitCompile JitCompile;
  static constexpr JitCompile DEFAULT =
    FunctionSpec_JitCompile_DEFAULT;
  static constexpr JitCompile ON =
    FunctionSpec_JitCompile_ON;
  static constexpr JitCompile OFF =
    FunctionSpec_JitCompile_OFF;
  static inline bool JitCompile_IsValid(int value) {
    return FunctionSpec_JitCompile_IsValid(value);
  }
  static constexpr JitCompile JitCompile_MIN =
    FunctionSpec_JitCompile_JitCompile_MIN;
  static constexpr JitCompile JitCompile_MAX =
    FunctionSpec_JitCompile_JitCompile_MAX;
  static constexpr int JitCompile_ARRAYSIZE =
    FunctionSpec_JitCompile_JitCompile_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  JitCompile_descriptor() {
    return FunctionSpec_JitCompile_descriptor();
  }
  template<typename T>
  static inline const std::string& JitCompile_Name(T enum_t_value) {
    static_assert(::std::is_same<T, JitCompile>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function JitCompile_Name.");
    return FunctionSpec_JitCompile_Name(enum_t_value);
  }
  static inline bool JitCompile_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      JitCompile* value) {
    return FunctionSpec_JitCompile_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFullargspecFieldNumber = 1,
    kInputSignatureFieldNumber = 5,
    kIsMethodFieldNumber = 2,
    kJitCompileFieldNumber = 6,
  };
  // .tensorflow.StructuredValue fullargspec = 1;
  bool has_fullargspec() const;
  private:
  bool _internal_has_fullargspec() const;
  public:
  void clear_fullargspec();
  const ::tensorflow::StructuredValue& fullargspec() const;
  PROTOBUF_NODISCARD ::tensorflow::StructuredValue* release_fullargspec();
  ::tensorflow::StructuredValue* mutable_fullargspec();
  void set_allocated_fullargspec(::tensorflow::StructuredValue* fullargspec);
  private:
  const ::tensorflow::StructuredValue& _internal_fullargspec() const;
  ::tensorflow::StructuredValue* _internal_mutable_fullargspec();
  public:
  void unsafe_arena_set_allocated_fullargspec(
      ::tensorflow::StructuredValue* fullargspec);
  ::tensorflow::StructuredValue* unsafe_arena_release_fullargspec();

  // .tensorflow.StructuredValue input_signature = 5;
  bool has_input_signature() const;
  private:
  bool _internal_has_input_signature() const;
  public:
  void clear_input_signature();
  const ::tensorflow::StructuredValue& input_signature() const;
  PROTOBUF_NODISCARD ::tensorflow::StructuredValue* release_input_signature();
  ::tensorflow::StructuredValue* mutable_input_signature();
  void set_allocated_input_signature(::tensorflow::StructuredValue* input_signature);
  private:
  const ::tensorflow::StructuredValue& _internal_input_signature() const;
  ::tensorflow::StructuredValue* _internal_mutable_input_signature();
  public:
  void unsafe_arena_set_allocated_input_signature(
      ::tensorflow::StructuredValue* input_signature);
  ::tensorflow::StructuredValue* unsafe_arena_release_input_signature();

  // bool is_method = 2;
  void clear_is_method();
  bool is_method() const;
  void set_is_method(bool value);
  private:
  bool _internal_is_method() const;
  void _internal_set_is_method(bool value);
  public:

  // .tensorflow.FunctionSpec.JitCompile jit_compile = 6;
  void clear_jit_compile();
  ::tensorflow::FunctionSpec_JitCompile jit_compile() const;
  void set_jit_compile(::tensorflow::FunctionSpec_JitCompile value);
  private:
  ::tensorflow::FunctionSpec_JitCompile _internal_jit_compile() const;
  void _internal_set_jit_compile(::tensorflow::FunctionSpec_JitCompile value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.FunctionSpec)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::StructuredValue* fullargspec_;
    ::tensorflow::StructuredValue* input_signature_;
    bool is_method_;
    int jit_compile_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SavedResource final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SavedResource) */ {
 public:
  inline SavedResource() : SavedResource(nullptr) {}
  ~SavedResource() override;
  explicit PROTOBUF_CONSTEXPR SavedResource(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SavedResource(const SavedResource& from);
  SavedResource(SavedResource&& from) noexcept
    : SavedResource() {
    *this = ::std::move(from);
  }

  inline SavedResource& operator=(const SavedResource& from) {
    CopyFrom(from);
    return *this;
  }
  inline SavedResource& operator=(SavedResource&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SavedResource& default_instance() {
    return *internal_default_instance();
  }
  static inline const SavedResource* internal_default_instance() {
    return reinterpret_cast<const SavedResource*>(
               &_SavedResource_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(SavedResource& a, SavedResource& b) {
    a.Swap(&b);
  }
  inline void Swap(SavedResource* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SavedResource* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SavedResource* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SavedResource>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SavedResource& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SavedResource& from) {
    SavedResource::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SavedResource* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SavedResource";
  }
  protected:
  explicit SavedResource(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFieldNumber = 1,
  };
  // string device = 1;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SavedResource)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SaveableObject final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SaveableObject) */ {
 public:
  inline SaveableObject() : SaveableObject(nullptr) {}
  ~SaveableObject() override;
  explicit PROTOBUF_CONSTEXPR SaveableObject(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveableObject(const SaveableObject& from);
  SaveableObject(SaveableObject&& from) noexcept
    : SaveableObject() {
    *this = ::std::move(from);
  }

  inline SaveableObject& operator=(const SaveableObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveableObject& operator=(SaveableObject&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveableObject& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveableObject* internal_default_instance() {
    return reinterpret_cast<const SaveableObject*>(
               &_SaveableObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(SaveableObject& a, SaveableObject& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveableObject* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveableObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveableObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveableObject>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveableObject& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SaveableObject& from) {
    SaveableObject::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveableObject* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SaveableObject";
  }
  protected:
  explicit SaveableObject(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSaveFunctionFieldNumber = 2,
    kRestoreFunctionFieldNumber = 3,
  };
  // int32 save_function = 2;
  void clear_save_function();
  int32_t save_function() const;
  void set_save_function(int32_t value);
  private:
  int32_t _internal_save_function() const;
  void _internal_set_save_function(int32_t value);
  public:

  // int32 restore_function = 3;
  void clear_restore_function();
  int32_t restore_function() const;
  void set_restore_function(int32_t value);
  private:
  int32_t _internal_restore_function() const;
  void _internal_set_restore_function(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SaveableObject)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t save_function_;
    int32_t restore_function_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// SavedObjectGraph

// repeated .tensorflow.SavedObject nodes = 1;
inline int SavedObjectGraph::_internal_nodes_size() const {
  return _impl_.nodes_.size();
}
inline int SavedObjectGraph::nodes_size() const {
  return _internal_nodes_size();
}
inline void SavedObjectGraph::clear_nodes() {
  _impl_.nodes_.Clear();
}
inline ::tensorflow::SavedObject* SavedObjectGraph::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObjectGraph.nodes)
  return _impl_.nodes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject >*
SavedObjectGraph::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObjectGraph.nodes)
  return &_impl_.nodes_;
}
inline const ::tensorflow::SavedObject& SavedObjectGraph::_internal_nodes(int index) const {
  return _impl_.nodes_.Get(index);
}
inline const ::tensorflow::SavedObject& SavedObjectGraph::nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObjectGraph.nodes)
  return _internal_nodes(index);
}
inline ::tensorflow::SavedObject* SavedObjectGraph::_internal_add_nodes() {
  return _impl_.nodes_.Add();
}
inline ::tensorflow::SavedObject* SavedObjectGraph::add_nodes() {
  ::tensorflow::SavedObject* _add = _internal_add_nodes();
  // @@protoc_insertion_point(field_add:tensorflow.SavedObjectGraph.nodes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedObject >&
SavedObjectGraph::nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObjectGraph.nodes)
  return _impl_.nodes_;
}

// map<string, .tensorflow.SavedConcreteFunction> concrete_functions = 2;
inline int SavedObjectGraph::_internal_concrete_functions_size() const {
  return _impl_.concrete_functions_.size();
}
inline int SavedObjectGraph::concrete_functions_size() const {
  return _internal_concrete_functions_size();
}
inline void SavedObjectGraph::clear_concrete_functions() {
  _impl_.concrete_functions_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >&
SavedObjectGraph::_internal_concrete_functions() const {
  return _impl_.concrete_functions_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >&
SavedObjectGraph::concrete_functions() const {
  // @@protoc_insertion_point(field_map:tensorflow.SavedObjectGraph.concrete_functions)
  return _internal_concrete_functions();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >*
SavedObjectGraph::_internal_mutable_concrete_functions() {
  return _impl_.concrete_functions_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SavedConcreteFunction >*
SavedObjectGraph::mutable_concrete_functions() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SavedObjectGraph.concrete_functions)
  return _internal_mutable_concrete_functions();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// SavedObject

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference children = 1;
inline int SavedObject::_internal_children_size() const {
  return _impl_.children_.size();
}
inline int SavedObject::children_size() const {
  return _internal_children_size();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.children)
  return _impl_.children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
SavedObject::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObject.children)
  return &_impl_.children_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& SavedObject::_internal_children(int index) const {
  return _impl_.children_.Get(index);
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& SavedObject::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.children)
  return _internal_children(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::_internal_add_children() {
  return _impl_.children_.Add();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::add_children() {
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* _add = _internal_add_children();
  // @@protoc_insertion_point(field_add:tensorflow.SavedObject.children)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
SavedObject::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObject.children)
  return _impl_.children_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.ObjectReference dependencies = 15;
inline int SavedObject::_internal_dependencies_size() const {
  return _impl_.dependencies_.size();
}
inline int SavedObject::dependencies_size() const {
  return _internal_dependencies_size();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::mutable_dependencies(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.dependencies)
  return _impl_.dependencies_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >*
SavedObject::mutable_dependencies() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObject.dependencies)
  return &_impl_.dependencies_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& SavedObject::_internal_dependencies(int index) const {
  return _impl_.dependencies_.Get(index);
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference& SavedObject::dependencies(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.dependencies)
  return _internal_dependencies(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::_internal_add_dependencies() {
  return _impl_.dependencies_.Add();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* SavedObject::add_dependencies() {
  ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference* _add = _internal_add_dependencies();
  // @@protoc_insertion_point(field_add:tensorflow.SavedObject.dependencies)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_ObjectReference >&
SavedObject::dependencies() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObject.dependencies)
  return _impl_.dependencies_;
}

// repeated .tensorflow.TrackableObjectGraph.TrackableObject.SlotVariableReference slot_variables = 3;
inline int SavedObject::_internal_slot_variables_size() const {
  return _impl_.slot_variables_.size();
}
inline int SavedObject::slot_variables_size() const {
  return _internal_slot_variables_size();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* SavedObject::mutable_slot_variables(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.slot_variables)
  return _impl_.slot_variables_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >*
SavedObject::mutable_slot_variables() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedObject.slot_variables)
  return &_impl_.slot_variables_;
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& SavedObject::_internal_slot_variables(int index) const {
  return _impl_.slot_variables_.Get(index);
}
inline const ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference& SavedObject::slot_variables(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.slot_variables)
  return _internal_slot_variables(index);
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* SavedObject::_internal_add_slot_variables() {
  return _impl_.slot_variables_.Add();
}
inline ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* SavedObject::add_slot_variables() {
  ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference* _add = _internal_add_slot_variables();
  // @@protoc_insertion_point(field_add:tensorflow.SavedObject.slot_variables)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TrackableObjectGraph_TrackableObject_SlotVariableReference >&
SavedObject::slot_variables() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedObject.slot_variables)
  return _impl_.slot_variables_;
}

// .tensorflow.SavedUserObject user_object = 4;
inline bool SavedObject::_internal_has_user_object() const {
  return kind_case() == kUserObject;
}
inline bool SavedObject::has_user_object() const {
  return _internal_has_user_object();
}
inline void SavedObject::set_has_user_object() {
  _impl_._oneof_case_[0] = kUserObject;
}
inline void SavedObject::clear_user_object() {
  if (_internal_has_user_object()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.user_object_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedUserObject* SavedObject::release_user_object() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.user_object)
  if (_internal_has_user_object()) {
    clear_has_kind();
    ::tensorflow::SavedUserObject* temp = _impl_.kind_.user_object_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.user_object_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedUserObject& SavedObject::_internal_user_object() const {
  return _internal_has_user_object()
      ? *_impl_.kind_.user_object_
      : reinterpret_cast< ::tensorflow::SavedUserObject&>(::tensorflow::_SavedUserObject_default_instance_);
}
inline const ::tensorflow::SavedUserObject& SavedObject::user_object() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.user_object)
  return _internal_user_object();
}
inline ::tensorflow::SavedUserObject* SavedObject::unsafe_arena_release_user_object() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.user_object)
  if (_internal_has_user_object()) {
    clear_has_kind();
    ::tensorflow::SavedUserObject* temp = _impl_.kind_.user_object_;
    _impl_.kind_.user_object_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_user_object(::tensorflow::SavedUserObject* user_object) {
  clear_kind();
  if (user_object) {
    set_has_user_object();
    _impl_.kind_.user_object_ = user_object;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.user_object)
}
inline ::tensorflow::SavedUserObject* SavedObject::_internal_mutable_user_object() {
  if (!_internal_has_user_object()) {
    clear_kind();
    set_has_user_object();
    _impl_.kind_.user_object_ = CreateMaybeMessage< ::tensorflow::SavedUserObject >(GetArenaForAllocation());
  }
  return _impl_.kind_.user_object_;
}
inline ::tensorflow::SavedUserObject* SavedObject::mutable_user_object() {
  ::tensorflow::SavedUserObject* _msg = _internal_mutable_user_object();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.user_object)
  return _msg;
}

// .tensorflow.SavedAsset asset = 5;
inline bool SavedObject::_internal_has_asset() const {
  return kind_case() == kAsset;
}
inline bool SavedObject::has_asset() const {
  return _internal_has_asset();
}
inline void SavedObject::set_has_asset() {
  _impl_._oneof_case_[0] = kAsset;
}
inline void SavedObject::clear_asset() {
  if (_internal_has_asset()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.asset_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedAsset* SavedObject::release_asset() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.asset)
  if (_internal_has_asset()) {
    clear_has_kind();
    ::tensorflow::SavedAsset* temp = _impl_.kind_.asset_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.asset_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedAsset& SavedObject::_internal_asset() const {
  return _internal_has_asset()
      ? *_impl_.kind_.asset_
      : reinterpret_cast< ::tensorflow::SavedAsset&>(::tensorflow::_SavedAsset_default_instance_);
}
inline const ::tensorflow::SavedAsset& SavedObject::asset() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.asset)
  return _internal_asset();
}
inline ::tensorflow::SavedAsset* SavedObject::unsafe_arena_release_asset() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.asset)
  if (_internal_has_asset()) {
    clear_has_kind();
    ::tensorflow::SavedAsset* temp = _impl_.kind_.asset_;
    _impl_.kind_.asset_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_asset(::tensorflow::SavedAsset* asset) {
  clear_kind();
  if (asset) {
    set_has_asset();
    _impl_.kind_.asset_ = asset;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.asset)
}
inline ::tensorflow::SavedAsset* SavedObject::_internal_mutable_asset() {
  if (!_internal_has_asset()) {
    clear_kind();
    set_has_asset();
    _impl_.kind_.asset_ = CreateMaybeMessage< ::tensorflow::SavedAsset >(GetArenaForAllocation());
  }
  return _impl_.kind_.asset_;
}
inline ::tensorflow::SavedAsset* SavedObject::mutable_asset() {
  ::tensorflow::SavedAsset* _msg = _internal_mutable_asset();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.asset)
  return _msg;
}

// .tensorflow.SavedFunction function = 6;
inline bool SavedObject::_internal_has_function() const {
  return kind_case() == kFunction;
}
inline bool SavedObject::has_function() const {
  return _internal_has_function();
}
inline void SavedObject::set_has_function() {
  _impl_._oneof_case_[0] = kFunction;
}
inline void SavedObject::clear_function() {
  if (_internal_has_function()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.function_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedFunction* SavedObject::release_function() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.function)
  if (_internal_has_function()) {
    clear_has_kind();
    ::tensorflow::SavedFunction* temp = _impl_.kind_.function_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedFunction& SavedObject::_internal_function() const {
  return _internal_has_function()
      ? *_impl_.kind_.function_
      : reinterpret_cast< ::tensorflow::SavedFunction&>(::tensorflow::_SavedFunction_default_instance_);
}
inline const ::tensorflow::SavedFunction& SavedObject::function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.function)
  return _internal_function();
}
inline ::tensorflow::SavedFunction* SavedObject::unsafe_arena_release_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.function)
  if (_internal_has_function()) {
    clear_has_kind();
    ::tensorflow::SavedFunction* temp = _impl_.kind_.function_;
    _impl_.kind_.function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_function(::tensorflow::SavedFunction* function) {
  clear_kind();
  if (function) {
    set_has_function();
    _impl_.kind_.function_ = function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.function)
}
inline ::tensorflow::SavedFunction* SavedObject::_internal_mutable_function() {
  if (!_internal_has_function()) {
    clear_kind();
    set_has_function();
    _impl_.kind_.function_ = CreateMaybeMessage< ::tensorflow::SavedFunction >(GetArenaForAllocation());
  }
  return _impl_.kind_.function_;
}
inline ::tensorflow::SavedFunction* SavedObject::mutable_function() {
  ::tensorflow::SavedFunction* _msg = _internal_mutable_function();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.function)
  return _msg;
}

// .tensorflow.SavedVariable variable = 7;
inline bool SavedObject::_internal_has_variable() const {
  return kind_case() == kVariable;
}
inline bool SavedObject::has_variable() const {
  return _internal_has_variable();
}
inline void SavedObject::set_has_variable() {
  _impl_._oneof_case_[0] = kVariable;
}
inline void SavedObject::clear_variable() {
  if (_internal_has_variable()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.variable_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedVariable* SavedObject::release_variable() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.variable)
  if (_internal_has_variable()) {
    clear_has_kind();
    ::tensorflow::SavedVariable* temp = _impl_.kind_.variable_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.variable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedVariable& SavedObject::_internal_variable() const {
  return _internal_has_variable()
      ? *_impl_.kind_.variable_
      : reinterpret_cast< ::tensorflow::SavedVariable&>(::tensorflow::_SavedVariable_default_instance_);
}
inline const ::tensorflow::SavedVariable& SavedObject::variable() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.variable)
  return _internal_variable();
}
inline ::tensorflow::SavedVariable* SavedObject::unsafe_arena_release_variable() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.variable)
  if (_internal_has_variable()) {
    clear_has_kind();
    ::tensorflow::SavedVariable* temp = _impl_.kind_.variable_;
    _impl_.kind_.variable_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_variable(::tensorflow::SavedVariable* variable) {
  clear_kind();
  if (variable) {
    set_has_variable();
    _impl_.kind_.variable_ = variable;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.variable)
}
inline ::tensorflow::SavedVariable* SavedObject::_internal_mutable_variable() {
  if (!_internal_has_variable()) {
    clear_kind();
    set_has_variable();
    _impl_.kind_.variable_ = CreateMaybeMessage< ::tensorflow::SavedVariable >(GetArenaForAllocation());
  }
  return _impl_.kind_.variable_;
}
inline ::tensorflow::SavedVariable* SavedObject::mutable_variable() {
  ::tensorflow::SavedVariable* _msg = _internal_mutable_variable();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.variable)
  return _msg;
}

// .tensorflow.SavedBareConcreteFunction bare_concrete_function = 8;
inline bool SavedObject::_internal_has_bare_concrete_function() const {
  return kind_case() == kBareConcreteFunction;
}
inline bool SavedObject::has_bare_concrete_function() const {
  return _internal_has_bare_concrete_function();
}
inline void SavedObject::set_has_bare_concrete_function() {
  _impl_._oneof_case_[0] = kBareConcreteFunction;
}
inline void SavedObject::clear_bare_concrete_function() {
  if (_internal_has_bare_concrete_function()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.bare_concrete_function_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::release_bare_concrete_function() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.bare_concrete_function)
  if (_internal_has_bare_concrete_function()) {
    clear_has_kind();
    ::tensorflow::SavedBareConcreteFunction* temp = _impl_.kind_.bare_concrete_function_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.bare_concrete_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedBareConcreteFunction& SavedObject::_internal_bare_concrete_function() const {
  return _internal_has_bare_concrete_function()
      ? *_impl_.kind_.bare_concrete_function_
      : reinterpret_cast< ::tensorflow::SavedBareConcreteFunction&>(::tensorflow::_SavedBareConcreteFunction_default_instance_);
}
inline const ::tensorflow::SavedBareConcreteFunction& SavedObject::bare_concrete_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.bare_concrete_function)
  return _internal_bare_concrete_function();
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::unsafe_arena_release_bare_concrete_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.bare_concrete_function)
  if (_internal_has_bare_concrete_function()) {
    clear_has_kind();
    ::tensorflow::SavedBareConcreteFunction* temp = _impl_.kind_.bare_concrete_function_;
    _impl_.kind_.bare_concrete_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_bare_concrete_function(::tensorflow::SavedBareConcreteFunction* bare_concrete_function) {
  clear_kind();
  if (bare_concrete_function) {
    set_has_bare_concrete_function();
    _impl_.kind_.bare_concrete_function_ = bare_concrete_function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.bare_concrete_function)
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::_internal_mutable_bare_concrete_function() {
  if (!_internal_has_bare_concrete_function()) {
    clear_kind();
    set_has_bare_concrete_function();
    _impl_.kind_.bare_concrete_function_ = CreateMaybeMessage< ::tensorflow::SavedBareConcreteFunction >(GetArenaForAllocation());
  }
  return _impl_.kind_.bare_concrete_function_;
}
inline ::tensorflow::SavedBareConcreteFunction* SavedObject::mutable_bare_concrete_function() {
  ::tensorflow::SavedBareConcreteFunction* _msg = _internal_mutable_bare_concrete_function();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.bare_concrete_function)
  return _msg;
}

// .tensorflow.SavedConstant constant = 9;
inline bool SavedObject::_internal_has_constant() const {
  return kind_case() == kConstant;
}
inline bool SavedObject::has_constant() const {
  return _internal_has_constant();
}
inline void SavedObject::set_has_constant() {
  _impl_._oneof_case_[0] = kConstant;
}
inline void SavedObject::clear_constant() {
  if (_internal_has_constant()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.constant_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedConstant* SavedObject::release_constant() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.constant)
  if (_internal_has_constant()) {
    clear_has_kind();
    ::tensorflow::SavedConstant* temp = _impl_.kind_.constant_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.constant_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedConstant& SavedObject::_internal_constant() const {
  return _internal_has_constant()
      ? *_impl_.kind_.constant_
      : reinterpret_cast< ::tensorflow::SavedConstant&>(::tensorflow::_SavedConstant_default_instance_);
}
inline const ::tensorflow::SavedConstant& SavedObject::constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.constant)
  return _internal_constant();
}
inline ::tensorflow::SavedConstant* SavedObject::unsafe_arena_release_constant() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.constant)
  if (_internal_has_constant()) {
    clear_has_kind();
    ::tensorflow::SavedConstant* temp = _impl_.kind_.constant_;
    _impl_.kind_.constant_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_constant(::tensorflow::SavedConstant* constant) {
  clear_kind();
  if (constant) {
    set_has_constant();
    _impl_.kind_.constant_ = constant;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.constant)
}
inline ::tensorflow::SavedConstant* SavedObject::_internal_mutable_constant() {
  if (!_internal_has_constant()) {
    clear_kind();
    set_has_constant();
    _impl_.kind_.constant_ = CreateMaybeMessage< ::tensorflow::SavedConstant >(GetArenaForAllocation());
  }
  return _impl_.kind_.constant_;
}
inline ::tensorflow::SavedConstant* SavedObject::mutable_constant() {
  ::tensorflow::SavedConstant* _msg = _internal_mutable_constant();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.constant)
  return _msg;
}

// .tensorflow.SavedResource resource = 10;
inline bool SavedObject::_internal_has_resource() const {
  return kind_case() == kResource;
}
inline bool SavedObject::has_resource() const {
  return _internal_has_resource();
}
inline void SavedObject::set_has_resource() {
  _impl_._oneof_case_[0] = kResource;
}
inline void SavedObject::clear_resource() {
  if (_internal_has_resource()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.resource_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::SavedResource* SavedObject::release_resource() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.resource)
  if (_internal_has_resource()) {
    clear_has_kind();
    ::tensorflow::SavedResource* temp = _impl_.kind_.resource_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.resource_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SavedResource& SavedObject::_internal_resource() const {
  return _internal_has_resource()
      ? *_impl_.kind_.resource_
      : reinterpret_cast< ::tensorflow::SavedResource&>(::tensorflow::_SavedResource_default_instance_);
}
inline const ::tensorflow::SavedResource& SavedObject::resource() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.resource)
  return _internal_resource();
}
inline ::tensorflow::SavedResource* SavedObject::unsafe_arena_release_resource() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.resource)
  if (_internal_has_resource()) {
    clear_has_kind();
    ::tensorflow::SavedResource* temp = _impl_.kind_.resource_;
    _impl_.kind_.resource_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_resource(::tensorflow::SavedResource* resource) {
  clear_kind();
  if (resource) {
    set_has_resource();
    _impl_.kind_.resource_ = resource;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.resource)
}
inline ::tensorflow::SavedResource* SavedObject::_internal_mutable_resource() {
  if (!_internal_has_resource()) {
    clear_kind();
    set_has_resource();
    _impl_.kind_.resource_ = CreateMaybeMessage< ::tensorflow::SavedResource >(GetArenaForAllocation());
  }
  return _impl_.kind_.resource_;
}
inline ::tensorflow::SavedResource* SavedObject::mutable_resource() {
  ::tensorflow::SavedResource* _msg = _internal_mutable_resource();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.resource)
  return _msg;
}

// .tensorflow.CapturedTensor captured_tensor = 12;
inline bool SavedObject::_internal_has_captured_tensor() const {
  return kind_case() == kCapturedTensor;
}
inline bool SavedObject::has_captured_tensor() const {
  return _internal_has_captured_tensor();
}
inline void SavedObject::set_has_captured_tensor() {
  _impl_._oneof_case_[0] = kCapturedTensor;
}
inline void SavedObject::clear_captured_tensor() {
  if (_internal_has_captured_tensor()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.captured_tensor_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::CapturedTensor* SavedObject::release_captured_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.captured_tensor)
  if (_internal_has_captured_tensor()) {
    clear_has_kind();
    ::tensorflow::CapturedTensor* temp = _impl_.kind_.captured_tensor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.captured_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CapturedTensor& SavedObject::_internal_captured_tensor() const {
  return _internal_has_captured_tensor()
      ? *_impl_.kind_.captured_tensor_
      : reinterpret_cast< ::tensorflow::CapturedTensor&>(::tensorflow::_CapturedTensor_default_instance_);
}
inline const ::tensorflow::CapturedTensor& SavedObject::captured_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.captured_tensor)
  return _internal_captured_tensor();
}
inline ::tensorflow::CapturedTensor* SavedObject::unsafe_arena_release_captured_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.SavedObject.captured_tensor)
  if (_internal_has_captured_tensor()) {
    clear_has_kind();
    ::tensorflow::CapturedTensor* temp = _impl_.kind_.captured_tensor_;
    _impl_.kind_.captured_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SavedObject::unsafe_arena_set_allocated_captured_tensor(::tensorflow::CapturedTensor* captured_tensor) {
  clear_kind();
  if (captured_tensor) {
    set_has_captured_tensor();
    _impl_.kind_.captured_tensor_ = captured_tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.captured_tensor)
}
inline ::tensorflow::CapturedTensor* SavedObject::_internal_mutable_captured_tensor() {
  if (!_internal_has_captured_tensor()) {
    clear_kind();
    set_has_captured_tensor();
    _impl_.kind_.captured_tensor_ = CreateMaybeMessage< ::tensorflow::CapturedTensor >(GetArenaForAllocation());
  }
  return _impl_.kind_.captured_tensor_;
}
inline ::tensorflow::CapturedTensor* SavedObject::mutable_captured_tensor() {
  ::tensorflow::CapturedTensor* _msg = _internal_mutable_captured_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.captured_tensor)
  return _msg;
}

// map<string, .tensorflow.SaveableObject> saveable_objects = 11;
inline int SavedObject::_internal_saveable_objects_size() const {
  return _impl_.saveable_objects_.size();
}
inline int SavedObject::saveable_objects_size() const {
  return _internal_saveable_objects_size();
}
inline void SavedObject::clear_saveable_objects() {
  _impl_.saveable_objects_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >&
SavedObject::_internal_saveable_objects() const {
  return _impl_.saveable_objects_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >&
SavedObject::saveable_objects() const {
  // @@protoc_insertion_point(field_map:tensorflow.SavedObject.saveable_objects)
  return _internal_saveable_objects();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >*
SavedObject::_internal_mutable_saveable_objects() {
  return _impl_.saveable_objects_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SaveableObject >*
SavedObject::mutable_saveable_objects() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SavedObject.saveable_objects)
  return _internal_mutable_saveable_objects();
}

// string registered_name = 13;
inline void SavedObject::clear_registered_name() {
  _impl_.registered_name_.ClearToEmpty();
}
inline const std::string& SavedObject::registered_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.registered_name)
  return _internal_registered_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedObject::set_registered_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.registered_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedObject.registered_name)
}
inline std::string* SavedObject::mutable_registered_name() {
  std::string* _s = _internal_mutable_registered_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.registered_name)
  return _s;
}
inline const std::string& SavedObject::_internal_registered_name() const {
  return _impl_.registered_name_.Get();
}
inline void SavedObject::_internal_set_registered_name(const std::string& value) {
  
  _impl_.registered_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedObject::_internal_mutable_registered_name() {
  
  return _impl_.registered_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedObject::release_registered_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.registered_name)
  return _impl_.registered_name_.Release();
}
inline void SavedObject::set_allocated_registered_name(std::string* registered_name) {
  if (registered_name != nullptr) {
    
  } else {
    
  }
  _impl_.registered_name_.SetAllocated(registered_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.registered_name_.IsDefault()) {
    _impl_.registered_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.registered_name)
}

// .google.protobuf.Any serialized_user_proto = 14;
inline bool SavedObject::_internal_has_serialized_user_proto() const {
  return this != internal_default_instance() && _impl_.serialized_user_proto_ != nullptr;
}
inline bool SavedObject::has_serialized_user_proto() const {
  return _internal_has_serialized_user_proto();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& SavedObject::_internal_serialized_user_proto() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = _impl_.serialized_user_proto_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& SavedObject::serialized_user_proto() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.serialized_user_proto)
  return _internal_serialized_user_proto();
}
inline void SavedObject::unsafe_arena_set_allocated_serialized_user_proto(
    ::PROTOBUF_NAMESPACE_ID::Any* serialized_user_proto) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.serialized_user_proto_);
  }
  _impl_.serialized_user_proto_ = serialized_user_proto;
  if (serialized_user_proto) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedObject.serialized_user_proto)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* SavedObject::release_serialized_user_proto() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.serialized_user_proto_;
  _impl_.serialized_user_proto_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* SavedObject::unsafe_arena_release_serialized_user_proto() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.serialized_user_proto)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.serialized_user_proto_;
  _impl_.serialized_user_proto_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* SavedObject::_internal_mutable_serialized_user_proto() {
  
  if (_impl_.serialized_user_proto_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    _impl_.serialized_user_proto_ = p;
  }
  return _impl_.serialized_user_proto_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* SavedObject::mutable_serialized_user_proto() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_serialized_user_proto();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.serialized_user_proto)
  return _msg;
}
inline void SavedObject::set_allocated_serialized_user_proto(::PROTOBUF_NAMESPACE_ID::Any* serialized_user_proto) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.serialized_user_proto_);
  }
  if (serialized_user_proto) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(serialized_user_proto));
    if (message_arena != submessage_arena) {
      serialized_user_proto = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, serialized_user_proto, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.serialized_user_proto_ = serialized_user_proto;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.serialized_user_proto)
}

// string registered_saver = 16;
inline void SavedObject::clear_registered_saver() {
  _impl_.registered_saver_.ClearToEmpty();
}
inline const std::string& SavedObject::registered_saver() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedObject.registered_saver)
  return _internal_registered_saver();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedObject::set_registered_saver(ArgT0&& arg0, ArgT... args) {
 
 _impl_.registered_saver_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedObject.registered_saver)
}
inline std::string* SavedObject::mutable_registered_saver() {
  std::string* _s = _internal_mutable_registered_saver();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedObject.registered_saver)
  return _s;
}
inline const std::string& SavedObject::_internal_registered_saver() const {
  return _impl_.registered_saver_.Get();
}
inline void SavedObject::_internal_set_registered_saver(const std::string& value) {
  
  _impl_.registered_saver_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedObject::_internal_mutable_registered_saver() {
  
  return _impl_.registered_saver_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedObject::release_registered_saver() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedObject.registered_saver)
  return _impl_.registered_saver_.Release();
}
inline void SavedObject::set_allocated_registered_saver(std::string* registered_saver) {
  if (registered_saver != nullptr) {
    
  } else {
    
  }
  _impl_.registered_saver_.SetAllocated(registered_saver, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.registered_saver_.IsDefault()) {
    _impl_.registered_saver_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedObject.registered_saver)
}

inline bool SavedObject::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void SavedObject::clear_has_kind() {
  _impl_._oneof_case_[0] = KIND_NOT_SET;
}
inline SavedObject::KindCase SavedObject::kind_case() const {
  return SavedObject::KindCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// SavedUserObject

// string identifier = 1;
inline void SavedUserObject::clear_identifier() {
  _impl_.identifier_.ClearToEmpty();
}
inline const std::string& SavedUserObject::identifier() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedUserObject.identifier)
  return _internal_identifier();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedUserObject::set_identifier(ArgT0&& arg0, ArgT... args) {
 
 _impl_.identifier_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedUserObject.identifier)
}
inline std::string* SavedUserObject::mutable_identifier() {
  std::string* _s = _internal_mutable_identifier();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedUserObject.identifier)
  return _s;
}
inline const std::string& SavedUserObject::_internal_identifier() const {
  return _impl_.identifier_.Get();
}
inline void SavedUserObject::_internal_set_identifier(const std::string& value) {
  
  _impl_.identifier_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedUserObject::_internal_mutable_identifier() {
  
  return _impl_.identifier_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedUserObject::release_identifier() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedUserObject.identifier)
  return _impl_.identifier_.Release();
}
inline void SavedUserObject::set_allocated_identifier(std::string* identifier) {
  if (identifier != nullptr) {
    
  } else {
    
  }
  _impl_.identifier_.SetAllocated(identifier, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.identifier_.IsDefault()) {
    _impl_.identifier_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedUserObject.identifier)
}

// .tensorflow.VersionDef version = 2;
inline bool SavedUserObject::_internal_has_version() const {
  return this != internal_default_instance() && _impl_.version_ != nullptr;
}
inline bool SavedUserObject::has_version() const {
  return _internal_has_version();
}
inline const ::tensorflow::VersionDef& SavedUserObject::_internal_version() const {
  const ::tensorflow::VersionDef* p = _impl_.version_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::VersionDef&>(
      ::tensorflow::_VersionDef_default_instance_);
}
inline const ::tensorflow::VersionDef& SavedUserObject::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedUserObject.version)
  return _internal_version();
}
inline void SavedUserObject::unsafe_arena_set_allocated_version(
    ::tensorflow::VersionDef* version) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.version_);
  }
  _impl_.version_ = version;
  if (version) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedUserObject.version)
}
inline ::tensorflow::VersionDef* SavedUserObject::release_version() {
  
  ::tensorflow::VersionDef* temp = _impl_.version_;
  _impl_.version_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::VersionDef* SavedUserObject::unsafe_arena_release_version() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedUserObject.version)
  
  ::tensorflow::VersionDef* temp = _impl_.version_;
  _impl_.version_ = nullptr;
  return temp;
}
inline ::tensorflow::VersionDef* SavedUserObject::_internal_mutable_version() {
  
  if (_impl_.version_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaForAllocation());
    _impl_.version_ = p;
  }
  return _impl_.version_;
}
inline ::tensorflow::VersionDef* SavedUserObject::mutable_version() {
  ::tensorflow::VersionDef* _msg = _internal_mutable_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedUserObject.version)
  return _msg;
}
inline void SavedUserObject::set_allocated_version(::tensorflow::VersionDef* version) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.version_);
  }
  if (version) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(version));
    if (message_arena != submessage_arena) {
      version = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, version, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.version_ = version;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedUserObject.version)
}

// string metadata = 3 [deprecated = true];
inline void SavedUserObject::clear_metadata() {
  _impl_.metadata_.ClearToEmpty();
}
inline const std::string& SavedUserObject::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedUserObject.metadata)
  return _internal_metadata();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedUserObject::set_metadata(ArgT0&& arg0, ArgT... args) {
 
 _impl_.metadata_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedUserObject.metadata)
}
inline std::string* SavedUserObject::mutable_metadata() {
  std::string* _s = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedUserObject.metadata)
  return _s;
}
inline const std::string& SavedUserObject::_internal_metadata() const {
  return _impl_.metadata_.Get();
}
inline void SavedUserObject::_internal_set_metadata(const std::string& value) {
  
  _impl_.metadata_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedUserObject::_internal_mutable_metadata() {
  
  return _impl_.metadata_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedUserObject::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedUserObject.metadata)
  return _impl_.metadata_.Release();
}
inline void SavedUserObject::set_allocated_metadata(std::string* metadata) {
  if (metadata != nullptr) {
    
  } else {
    
  }
  _impl_.metadata_.SetAllocated(metadata, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.metadata_.IsDefault()) {
    _impl_.metadata_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedUserObject.metadata)
}

// -------------------------------------------------------------------

// SavedAsset

// int32 asset_file_def_index = 1;
inline void SavedAsset::clear_asset_file_def_index() {
  _impl_.asset_file_def_index_ = 0;
}
inline int32_t SavedAsset::_internal_asset_file_def_index() const {
  return _impl_.asset_file_def_index_;
}
inline int32_t SavedAsset::asset_file_def_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedAsset.asset_file_def_index)
  return _internal_asset_file_def_index();
}
inline void SavedAsset::_internal_set_asset_file_def_index(int32_t value) {
  
  _impl_.asset_file_def_index_ = value;
}
inline void SavedAsset::set_asset_file_def_index(int32_t value) {
  _internal_set_asset_file_def_index(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedAsset.asset_file_def_index)
}

// -------------------------------------------------------------------

// SavedFunction

// repeated string concrete_functions = 1;
inline int SavedFunction::_internal_concrete_functions_size() const {
  return _impl_.concrete_functions_.size();
}
inline int SavedFunction::concrete_functions_size() const {
  return _internal_concrete_functions_size();
}
inline void SavedFunction::clear_concrete_functions() {
  _impl_.concrete_functions_.Clear();
}
inline std::string* SavedFunction::add_concrete_functions() {
  std::string* _s = _internal_add_concrete_functions();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.SavedFunction.concrete_functions)
  return _s;
}
inline const std::string& SavedFunction::_internal_concrete_functions(int index) const {
  return _impl_.concrete_functions_.Get(index);
}
inline const std::string& SavedFunction::concrete_functions(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedFunction.concrete_functions)
  return _internal_concrete_functions(index);
}
inline std::string* SavedFunction::mutable_concrete_functions(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedFunction.concrete_functions)
  return _impl_.concrete_functions_.Mutable(index);
}
inline void SavedFunction::set_concrete_functions(int index, const std::string& value) {
  _impl_.concrete_functions_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::set_concrete_functions(int index, std::string&& value) {
  _impl_.concrete_functions_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::set_concrete_functions(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.concrete_functions_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::set_concrete_functions(int index, const char* value, size_t size) {
  _impl_.concrete_functions_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedFunction.concrete_functions)
}
inline std::string* SavedFunction::_internal_add_concrete_functions() {
  return _impl_.concrete_functions_.Add();
}
inline void SavedFunction::add_concrete_functions(const std::string& value) {
  _impl_.concrete_functions_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::add_concrete_functions(std::string&& value) {
  _impl_.concrete_functions_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::add_concrete_functions(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.concrete_functions_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.SavedFunction.concrete_functions)
}
inline void SavedFunction::add_concrete_functions(const char* value, size_t size) {
  _impl_.concrete_functions_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.SavedFunction.concrete_functions)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SavedFunction::concrete_functions() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedFunction.concrete_functions)
  return _impl_.concrete_functions_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SavedFunction::mutable_concrete_functions() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedFunction.concrete_functions)
  return &_impl_.concrete_functions_;
}

// .tensorflow.FunctionSpec function_spec = 2;
inline bool SavedFunction::_internal_has_function_spec() const {
  return this != internal_default_instance() && _impl_.function_spec_ != nullptr;
}
inline bool SavedFunction::has_function_spec() const {
  return _internal_has_function_spec();
}
inline void SavedFunction::clear_function_spec() {
  if (GetArenaForAllocation() == nullptr && _impl_.function_spec_ != nullptr) {
    delete _impl_.function_spec_;
  }
  _impl_.function_spec_ = nullptr;
}
inline const ::tensorflow::FunctionSpec& SavedFunction::_internal_function_spec() const {
  const ::tensorflow::FunctionSpec* p = _impl_.function_spec_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::FunctionSpec&>(
      ::tensorflow::_FunctionSpec_default_instance_);
}
inline const ::tensorflow::FunctionSpec& SavedFunction::function_spec() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedFunction.function_spec)
  return _internal_function_spec();
}
inline void SavedFunction::unsafe_arena_set_allocated_function_spec(
    ::tensorflow::FunctionSpec* function_spec) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.function_spec_);
  }
  _impl_.function_spec_ = function_spec;
  if (function_spec) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedFunction.function_spec)
}
inline ::tensorflow::FunctionSpec* SavedFunction::release_function_spec() {
  
  ::tensorflow::FunctionSpec* temp = _impl_.function_spec_;
  _impl_.function_spec_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedFunction::unsafe_arena_release_function_spec() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedFunction.function_spec)
  
  ::tensorflow::FunctionSpec* temp = _impl_.function_spec_;
  _impl_.function_spec_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedFunction::_internal_mutable_function_spec() {
  
  if (_impl_.function_spec_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionSpec>(GetArenaForAllocation());
    _impl_.function_spec_ = p;
  }
  return _impl_.function_spec_;
}
inline ::tensorflow::FunctionSpec* SavedFunction::mutable_function_spec() {
  ::tensorflow::FunctionSpec* _msg = _internal_mutable_function_spec();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedFunction.function_spec)
  return _msg;
}
inline void SavedFunction::set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.function_spec_;
  }
  if (function_spec) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(function_spec);
    if (message_arena != submessage_arena) {
      function_spec = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, function_spec, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.function_spec_ = function_spec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedFunction.function_spec)
}

// -------------------------------------------------------------------

// CapturedTensor

// string name = 1;
inline void CapturedTensor::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& CapturedTensor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CapturedTensor.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CapturedTensor::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CapturedTensor.name)
}
inline std::string* CapturedTensor::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CapturedTensor.name)
  return _s;
}
inline const std::string& CapturedTensor::_internal_name() const {
  return _impl_.name_.Get();
}
inline void CapturedTensor::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* CapturedTensor::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* CapturedTensor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CapturedTensor.name)
  return _impl_.name_.Release();
}
inline void CapturedTensor::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CapturedTensor.name)
}

// string concrete_function = 2;
inline void CapturedTensor::clear_concrete_function() {
  _impl_.concrete_function_.ClearToEmpty();
}
inline const std::string& CapturedTensor::concrete_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.CapturedTensor.concrete_function)
  return _internal_concrete_function();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CapturedTensor::set_concrete_function(ArgT0&& arg0, ArgT... args) {
 
 _impl_.concrete_function_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CapturedTensor.concrete_function)
}
inline std::string* CapturedTensor::mutable_concrete_function() {
  std::string* _s = _internal_mutable_concrete_function();
  // @@protoc_insertion_point(field_mutable:tensorflow.CapturedTensor.concrete_function)
  return _s;
}
inline const std::string& CapturedTensor::_internal_concrete_function() const {
  return _impl_.concrete_function_.Get();
}
inline void CapturedTensor::_internal_set_concrete_function(const std::string& value) {
  
  _impl_.concrete_function_.Set(value, GetArenaForAllocation());
}
inline std::string* CapturedTensor::_internal_mutable_concrete_function() {
  
  return _impl_.concrete_function_.Mutable(GetArenaForAllocation());
}
inline std::string* CapturedTensor::release_concrete_function() {
  // @@protoc_insertion_point(field_release:tensorflow.CapturedTensor.concrete_function)
  return _impl_.concrete_function_.Release();
}
inline void CapturedTensor::set_allocated_concrete_function(std::string* concrete_function) {
  if (concrete_function != nullptr) {
    
  } else {
    
  }
  _impl_.concrete_function_.SetAllocated(concrete_function, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.concrete_function_.IsDefault()) {
    _impl_.concrete_function_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CapturedTensor.concrete_function)
}

// -------------------------------------------------------------------

// SavedConcreteFunction

// repeated int32 bound_inputs = 2;
inline int SavedConcreteFunction::_internal_bound_inputs_size() const {
  return _impl_.bound_inputs_.size();
}
inline int SavedConcreteFunction::bound_inputs_size() const {
  return _internal_bound_inputs_size();
}
inline void SavedConcreteFunction::clear_bound_inputs() {
  _impl_.bound_inputs_.Clear();
}
inline int32_t SavedConcreteFunction::_internal_bound_inputs(int index) const {
  return _impl_.bound_inputs_.Get(index);
}
inline int32_t SavedConcreteFunction::bound_inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.bound_inputs)
  return _internal_bound_inputs(index);
}
inline void SavedConcreteFunction::set_bound_inputs(int index, int32_t value) {
  _impl_.bound_inputs_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedConcreteFunction.bound_inputs)
}
inline void SavedConcreteFunction::_internal_add_bound_inputs(int32_t value) {
  _impl_.bound_inputs_.Add(value);
}
inline void SavedConcreteFunction::add_bound_inputs(int32_t value) {
  _internal_add_bound_inputs(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedConcreteFunction.bound_inputs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
SavedConcreteFunction::_internal_bound_inputs() const {
  return _impl_.bound_inputs_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
SavedConcreteFunction::bound_inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedConcreteFunction.bound_inputs)
  return _internal_bound_inputs();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
SavedConcreteFunction::_internal_mutable_bound_inputs() {
  return &_impl_.bound_inputs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
SavedConcreteFunction::mutable_bound_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedConcreteFunction.bound_inputs)
  return _internal_mutable_bound_inputs();
}

// .tensorflow.StructuredValue canonicalized_input_signature = 3;
inline bool SavedConcreteFunction::_internal_has_canonicalized_input_signature() const {
  return this != internal_default_instance() && _impl_.canonicalized_input_signature_ != nullptr;
}
inline bool SavedConcreteFunction::has_canonicalized_input_signature() const {
  return _internal_has_canonicalized_input_signature();
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::_internal_canonicalized_input_signature() const {
  const ::tensorflow::StructuredValue* p = _impl_.canonicalized_input_signature_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StructuredValue&>(
      ::tensorflow::_StructuredValue_default_instance_);
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::canonicalized_input_signature() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  return _internal_canonicalized_input_signature();
}
inline void SavedConcreteFunction::unsafe_arena_set_allocated_canonicalized_input_signature(
    ::tensorflow::StructuredValue* canonicalized_input_signature) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.canonicalized_input_signature_);
  }
  _impl_.canonicalized_input_signature_ = canonicalized_input_signature;
  if (canonicalized_input_signature) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::release_canonicalized_input_signature() {
  
  ::tensorflow::StructuredValue* temp = _impl_.canonicalized_input_signature_;
  _impl_.canonicalized_input_signature_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::unsafe_arena_release_canonicalized_input_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  
  ::tensorflow::StructuredValue* temp = _impl_.canonicalized_input_signature_;
  _impl_.canonicalized_input_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::_internal_mutable_canonicalized_input_signature() {
  
  if (_impl_.canonicalized_input_signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaForAllocation());
    _impl_.canonicalized_input_signature_ = p;
  }
  return _impl_.canonicalized_input_signature_;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::mutable_canonicalized_input_signature() {
  ::tensorflow::StructuredValue* _msg = _internal_mutable_canonicalized_input_signature();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
  return _msg;
}
inline void SavedConcreteFunction::set_allocated_canonicalized_input_signature(::tensorflow::StructuredValue* canonicalized_input_signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.canonicalized_input_signature_);
  }
  if (canonicalized_input_signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(canonicalized_input_signature));
    if (message_arena != submessage_arena) {
      canonicalized_input_signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, canonicalized_input_signature, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.canonicalized_input_signature_ = canonicalized_input_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConcreteFunction.canonicalized_input_signature)
}

// .tensorflow.StructuredValue output_signature = 4;
inline bool SavedConcreteFunction::_internal_has_output_signature() const {
  return this != internal_default_instance() && _impl_.output_signature_ != nullptr;
}
inline bool SavedConcreteFunction::has_output_signature() const {
  return _internal_has_output_signature();
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::_internal_output_signature() const {
  const ::tensorflow::StructuredValue* p = _impl_.output_signature_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StructuredValue&>(
      ::tensorflow::_StructuredValue_default_instance_);
}
inline const ::tensorflow::StructuredValue& SavedConcreteFunction::output_signature() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedConcreteFunction.output_signature)
  return _internal_output_signature();
}
inline void SavedConcreteFunction::unsafe_arena_set_allocated_output_signature(
    ::tensorflow::StructuredValue* output_signature) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.output_signature_);
  }
  _impl_.output_signature_ = output_signature;
  if (output_signature) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedConcreteFunction.output_signature)
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::release_output_signature() {
  
  ::tensorflow::StructuredValue* temp = _impl_.output_signature_;
  _impl_.output_signature_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::unsafe_arena_release_output_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConcreteFunction.output_signature)
  
  ::tensorflow::StructuredValue* temp = _impl_.output_signature_;
  _impl_.output_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::_internal_mutable_output_signature() {
  
  if (_impl_.output_signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaForAllocation());
    _impl_.output_signature_ = p;
  }
  return _impl_.output_signature_;
}
inline ::tensorflow::StructuredValue* SavedConcreteFunction::mutable_output_signature() {
  ::tensorflow::StructuredValue* _msg = _internal_mutable_output_signature();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConcreteFunction.output_signature)
  return _msg;
}
inline void SavedConcreteFunction::set_allocated_output_signature(::tensorflow::StructuredValue* output_signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.output_signature_);
  }
  if (output_signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(output_signature));
    if (message_arena != submessage_arena) {
      output_signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output_signature, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.output_signature_ = output_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConcreteFunction.output_signature)
}

// -------------------------------------------------------------------

// SavedBareConcreteFunction

// string concrete_function_name = 1;
inline void SavedBareConcreteFunction::clear_concrete_function_name() {
  _impl_.concrete_function_name_.ClearToEmpty();
}
inline const std::string& SavedBareConcreteFunction::concrete_function_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  return _internal_concrete_function_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedBareConcreteFunction::set_concrete_function_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.concrete_function_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}
inline std::string* SavedBareConcreteFunction::mutable_concrete_function_name() {
  std::string* _s = _internal_mutable_concrete_function_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  return _s;
}
inline const std::string& SavedBareConcreteFunction::_internal_concrete_function_name() const {
  return _impl_.concrete_function_name_.Get();
}
inline void SavedBareConcreteFunction::_internal_set_concrete_function_name(const std::string& value) {
  
  _impl_.concrete_function_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedBareConcreteFunction::_internal_mutable_concrete_function_name() {
  
  return _impl_.concrete_function_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedBareConcreteFunction::release_concrete_function_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedBareConcreteFunction.concrete_function_name)
  return _impl_.concrete_function_name_.Release();
}
inline void SavedBareConcreteFunction::set_allocated_concrete_function_name(std::string* concrete_function_name) {
  if (concrete_function_name != nullptr) {
    
  } else {
    
  }
  _impl_.concrete_function_name_.SetAllocated(concrete_function_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.concrete_function_name_.IsDefault()) {
    _impl_.concrete_function_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedBareConcreteFunction.concrete_function_name)
}

// repeated string argument_keywords = 2;
inline int SavedBareConcreteFunction::_internal_argument_keywords_size() const {
  return _impl_.argument_keywords_.size();
}
inline int SavedBareConcreteFunction::argument_keywords_size() const {
  return _internal_argument_keywords_size();
}
inline void SavedBareConcreteFunction::clear_argument_keywords() {
  _impl_.argument_keywords_.Clear();
}
inline std::string* SavedBareConcreteFunction::add_argument_keywords() {
  std::string* _s = _internal_add_argument_keywords();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return _s;
}
inline const std::string& SavedBareConcreteFunction::_internal_argument_keywords(int index) const {
  return _impl_.argument_keywords_.Get(index);
}
inline const std::string& SavedBareConcreteFunction::argument_keywords(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return _internal_argument_keywords(index);
}
inline std::string* SavedBareConcreteFunction::mutable_argument_keywords(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return _impl_.argument_keywords_.Mutable(index);
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const std::string& value) {
  _impl_.argument_keywords_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, std::string&& value) {
  _impl_.argument_keywords_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.argument_keywords_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::set_argument_keywords(int index, const char* value, size_t size) {
  _impl_.argument_keywords_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline std::string* SavedBareConcreteFunction::_internal_add_argument_keywords() {
  return _impl_.argument_keywords_.Add();
}
inline void SavedBareConcreteFunction::add_argument_keywords(const std::string& value) {
  _impl_.argument_keywords_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::add_argument_keywords(std::string&& value) {
  _impl_.argument_keywords_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::add_argument_keywords(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.argument_keywords_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline void SavedBareConcreteFunction::add_argument_keywords(const char* value, size_t size) {
  _impl_.argument_keywords_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.SavedBareConcreteFunction.argument_keywords)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SavedBareConcreteFunction::argument_keywords() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return _impl_.argument_keywords_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SavedBareConcreteFunction::mutable_argument_keywords() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedBareConcreteFunction.argument_keywords)
  return &_impl_.argument_keywords_;
}

// int64 allowed_positional_arguments = 3;
inline void SavedBareConcreteFunction::clear_allowed_positional_arguments() {
  _impl_.allowed_positional_arguments_ = int64_t{0};
}
inline int64_t SavedBareConcreteFunction::_internal_allowed_positional_arguments() const {
  return _impl_.allowed_positional_arguments_;
}
inline int64_t SavedBareConcreteFunction::allowed_positional_arguments() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.allowed_positional_arguments)
  return _internal_allowed_positional_arguments();
}
inline void SavedBareConcreteFunction::_internal_set_allowed_positional_arguments(int64_t value) {
  
  _impl_.allowed_positional_arguments_ = value;
}
inline void SavedBareConcreteFunction::set_allowed_positional_arguments(int64_t value) {
  _internal_set_allowed_positional_arguments(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedBareConcreteFunction.allowed_positional_arguments)
}

// .tensorflow.FunctionSpec function_spec = 4;
inline bool SavedBareConcreteFunction::_internal_has_function_spec() const {
  return this != internal_default_instance() && _impl_.function_spec_ != nullptr;
}
inline bool SavedBareConcreteFunction::has_function_spec() const {
  return _internal_has_function_spec();
}
inline void SavedBareConcreteFunction::clear_function_spec() {
  if (GetArenaForAllocation() == nullptr && _impl_.function_spec_ != nullptr) {
    delete _impl_.function_spec_;
  }
  _impl_.function_spec_ = nullptr;
}
inline const ::tensorflow::FunctionSpec& SavedBareConcreteFunction::_internal_function_spec() const {
  const ::tensorflow::FunctionSpec* p = _impl_.function_spec_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::FunctionSpec&>(
      ::tensorflow::_FunctionSpec_default_instance_);
}
inline const ::tensorflow::FunctionSpec& SavedBareConcreteFunction::function_spec() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedBareConcreteFunction.function_spec)
  return _internal_function_spec();
}
inline void SavedBareConcreteFunction::unsafe_arena_set_allocated_function_spec(
    ::tensorflow::FunctionSpec* function_spec) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.function_spec_);
  }
  _impl_.function_spec_ = function_spec;
  if (function_spec) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedBareConcreteFunction.function_spec)
}
inline ::tensorflow::FunctionSpec* SavedBareConcreteFunction::release_function_spec() {
  
  ::tensorflow::FunctionSpec* temp = _impl_.function_spec_;
  _impl_.function_spec_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedBareConcreteFunction::unsafe_arena_release_function_spec() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedBareConcreteFunction.function_spec)
  
  ::tensorflow::FunctionSpec* temp = _impl_.function_spec_;
  _impl_.function_spec_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionSpec* SavedBareConcreteFunction::_internal_mutable_function_spec() {
  
  if (_impl_.function_spec_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionSpec>(GetArenaForAllocation());
    _impl_.function_spec_ = p;
  }
  return _impl_.function_spec_;
}
inline ::tensorflow::FunctionSpec* SavedBareConcreteFunction::mutable_function_spec() {
  ::tensorflow::FunctionSpec* _msg = _internal_mutable_function_spec();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedBareConcreteFunction.function_spec)
  return _msg;
}
inline void SavedBareConcreteFunction::set_allocated_function_spec(::tensorflow::FunctionSpec* function_spec) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.function_spec_;
  }
  if (function_spec) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(function_spec);
    if (message_arena != submessage_arena) {
      function_spec = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, function_spec, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.function_spec_ = function_spec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedBareConcreteFunction.function_spec)
}

// -------------------------------------------------------------------

// SavedConstant

// string operation = 1;
inline void SavedConstant::clear_operation() {
  _impl_.operation_.ClearToEmpty();
}
inline const std::string& SavedConstant::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedConstant.operation)
  return _internal_operation();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedConstant::set_operation(ArgT0&& arg0, ArgT... args) {
 
 _impl_.operation_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedConstant.operation)
}
inline std::string* SavedConstant::mutable_operation() {
  std::string* _s = _internal_mutable_operation();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedConstant.operation)
  return _s;
}
inline const std::string& SavedConstant::_internal_operation() const {
  return _impl_.operation_.Get();
}
inline void SavedConstant::_internal_set_operation(const std::string& value) {
  
  _impl_.operation_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedConstant::_internal_mutable_operation() {
  
  return _impl_.operation_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedConstant::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedConstant.operation)
  return _impl_.operation_.Release();
}
inline void SavedConstant::set_allocated_operation(std::string* operation) {
  if (operation != nullptr) {
    
  } else {
    
  }
  _impl_.operation_.SetAllocated(operation, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.operation_.IsDefault()) {
    _impl_.operation_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedConstant.operation)
}

// -------------------------------------------------------------------

// SavedVariable

// .tensorflow.DataType dtype = 1;
inline void SavedVariable::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType SavedVariable::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType SavedVariable::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.dtype)
  return _internal_dtype();
}
inline void SavedVariable::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void SavedVariable::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool SavedVariable::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool SavedVariable::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& SavedVariable::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& SavedVariable::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.shape)
  return _internal_shape();
}
inline void SavedVariable::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.SavedVariable.shape)
}
inline ::tensorflow::TensorShapeProto* SavedVariable::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedVariable::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedVariable.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* SavedVariable::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* SavedVariable::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.shape)
  return _msg;
}
inline void SavedVariable::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedVariable.shape)
}

// bool trainable = 3;
inline void SavedVariable::clear_trainable() {
  _impl_.trainable_ = false;
}
inline bool SavedVariable::_internal_trainable() const {
  return _impl_.trainable_;
}
inline bool SavedVariable::trainable() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.trainable)
  return _internal_trainable();
}
inline void SavedVariable::_internal_set_trainable(bool value) {
  
  _impl_.trainable_ = value;
}
inline void SavedVariable::set_trainable(bool value) {
  _internal_set_trainable(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.trainable)
}

// .tensorflow.VariableSynchronization synchronization = 4;
inline void SavedVariable::clear_synchronization() {
  _impl_.synchronization_ = 0;
}
inline ::tensorflow::VariableSynchronization SavedVariable::_internal_synchronization() const {
  return static_cast< ::tensorflow::VariableSynchronization >(_impl_.synchronization_);
}
inline ::tensorflow::VariableSynchronization SavedVariable::synchronization() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.synchronization)
  return _internal_synchronization();
}
inline void SavedVariable::_internal_set_synchronization(::tensorflow::VariableSynchronization value) {
  
  _impl_.synchronization_ = value;
}
inline void SavedVariable::set_synchronization(::tensorflow::VariableSynchronization value) {
  _internal_set_synchronization(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.synchronization)
}

// .tensorflow.VariableAggregation aggregation = 5;
inline void SavedVariable::clear_aggregation() {
  _impl_.aggregation_ = 0;
}
inline ::tensorflow::VariableAggregation SavedVariable::_internal_aggregation() const {
  return static_cast< ::tensorflow::VariableAggregation >(_impl_.aggregation_);
}
inline ::tensorflow::VariableAggregation SavedVariable::aggregation() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.aggregation)
  return _internal_aggregation();
}
inline void SavedVariable::_internal_set_aggregation(::tensorflow::VariableAggregation value) {
  
  _impl_.aggregation_ = value;
}
inline void SavedVariable::set_aggregation(::tensorflow::VariableAggregation value) {
  _internal_set_aggregation(value);
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.aggregation)
}

// string name = 6;
inline void SavedVariable::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& SavedVariable::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedVariable::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.name)
}
inline std::string* SavedVariable::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.name)
  return _s;
}
inline const std::string& SavedVariable::_internal_name() const {
  return _impl_.name_.Get();
}
inline void SavedVariable::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedVariable::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedVariable::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedVariable.name)
  return _impl_.name_.Release();
}
inline void SavedVariable::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedVariable.name)
}

// string device = 7;
inline void SavedVariable::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& SavedVariable::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedVariable::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedVariable.device)
}
inline std::string* SavedVariable::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.device)
  return _s;
}
inline const std::string& SavedVariable::_internal_device() const {
  return _impl_.device_.Get();
}
inline void SavedVariable::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedVariable::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedVariable::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedVariable.device)
  return _impl_.device_.Release();
}
inline void SavedVariable::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedVariable.device)
}

// repeated .tensorflow.SavedVariable experimental_distributed_variable_components = 8;
inline int SavedVariable::_internal_experimental_distributed_variable_components_size() const {
  return _impl_.experimental_distributed_variable_components_.size();
}
inline int SavedVariable::experimental_distributed_variable_components_size() const {
  return _internal_experimental_distributed_variable_components_size();
}
inline void SavedVariable::clear_experimental_distributed_variable_components() {
  _impl_.experimental_distributed_variable_components_.Clear();
}
inline ::tensorflow::SavedVariable* SavedVariable::mutable_experimental_distributed_variable_components(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return _impl_.experimental_distributed_variable_components_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable >*
SavedVariable::mutable_experimental_distributed_variable_components() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return &_impl_.experimental_distributed_variable_components_;
}
inline const ::tensorflow::SavedVariable& SavedVariable::_internal_experimental_distributed_variable_components(int index) const {
  return _impl_.experimental_distributed_variable_components_.Get(index);
}
inline const ::tensorflow::SavedVariable& SavedVariable::experimental_distributed_variable_components(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return _internal_experimental_distributed_variable_components(index);
}
inline ::tensorflow::SavedVariable* SavedVariable::_internal_add_experimental_distributed_variable_components() {
  return _impl_.experimental_distributed_variable_components_.Add();
}
inline ::tensorflow::SavedVariable* SavedVariable::add_experimental_distributed_variable_components() {
  ::tensorflow::SavedVariable* _add = _internal_add_experimental_distributed_variable_components();
  // @@protoc_insertion_point(field_add:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SavedVariable >&
SavedVariable::experimental_distributed_variable_components() const {
  // @@protoc_insertion_point(field_list:tensorflow.SavedVariable.experimental_distributed_variable_components)
  return _impl_.experimental_distributed_variable_components_;
}

// -------------------------------------------------------------------

// FunctionSpec

// .tensorflow.StructuredValue fullargspec = 1;
inline bool FunctionSpec::_internal_has_fullargspec() const {
  return this != internal_default_instance() && _impl_.fullargspec_ != nullptr;
}
inline bool FunctionSpec::has_fullargspec() const {
  return _internal_has_fullargspec();
}
inline const ::tensorflow::StructuredValue& FunctionSpec::_internal_fullargspec() const {
  const ::tensorflow::StructuredValue* p = _impl_.fullargspec_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StructuredValue&>(
      ::tensorflow::_StructuredValue_default_instance_);
}
inline const ::tensorflow::StructuredValue& FunctionSpec::fullargspec() const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.fullargspec)
  return _internal_fullargspec();
}
inline void FunctionSpec::unsafe_arena_set_allocated_fullargspec(
    ::tensorflow::StructuredValue* fullargspec) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.fullargspec_);
  }
  _impl_.fullargspec_ = fullargspec;
  if (fullargspec) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FunctionSpec.fullargspec)
}
inline ::tensorflow::StructuredValue* FunctionSpec::release_fullargspec() {
  
  ::tensorflow::StructuredValue* temp = _impl_.fullargspec_;
  _impl_.fullargspec_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::unsafe_arena_release_fullargspec() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionSpec.fullargspec)
  
  ::tensorflow::StructuredValue* temp = _impl_.fullargspec_;
  _impl_.fullargspec_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::_internal_mutable_fullargspec() {
  
  if (_impl_.fullargspec_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaForAllocation());
    _impl_.fullargspec_ = p;
  }
  return _impl_.fullargspec_;
}
inline ::tensorflow::StructuredValue* FunctionSpec::mutable_fullargspec() {
  ::tensorflow::StructuredValue* _msg = _internal_mutable_fullargspec();
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionSpec.fullargspec)
  return _msg;
}
inline void FunctionSpec::set_allocated_fullargspec(::tensorflow::StructuredValue* fullargspec) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.fullargspec_);
  }
  if (fullargspec) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(fullargspec));
    if (message_arena != submessage_arena) {
      fullargspec = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, fullargspec, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.fullargspec_ = fullargspec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionSpec.fullargspec)
}

// bool is_method = 2;
inline void FunctionSpec::clear_is_method() {
  _impl_.is_method_ = false;
}
inline bool FunctionSpec::_internal_is_method() const {
  return _impl_.is_method_;
}
inline bool FunctionSpec::is_method() const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.is_method)
  return _internal_is_method();
}
inline void FunctionSpec::_internal_set_is_method(bool value) {
  
  _impl_.is_method_ = value;
}
inline void FunctionSpec::set_is_method(bool value) {
  _internal_set_is_method(value);
  // @@protoc_insertion_point(field_set:tensorflow.FunctionSpec.is_method)
}

// .tensorflow.StructuredValue input_signature = 5;
inline bool FunctionSpec::_internal_has_input_signature() const {
  return this != internal_default_instance() && _impl_.input_signature_ != nullptr;
}
inline bool FunctionSpec::has_input_signature() const {
  return _internal_has_input_signature();
}
inline const ::tensorflow::StructuredValue& FunctionSpec::_internal_input_signature() const {
  const ::tensorflow::StructuredValue* p = _impl_.input_signature_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StructuredValue&>(
      ::tensorflow::_StructuredValue_default_instance_);
}
inline const ::tensorflow::StructuredValue& FunctionSpec::input_signature() const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.input_signature)
  return _internal_input_signature();
}
inline void FunctionSpec::unsafe_arena_set_allocated_input_signature(
    ::tensorflow::StructuredValue* input_signature) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.input_signature_);
  }
  _impl_.input_signature_ = input_signature;
  if (input_signature) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.FunctionSpec.input_signature)
}
inline ::tensorflow::StructuredValue* FunctionSpec::release_input_signature() {
  
  ::tensorflow::StructuredValue* temp = _impl_.input_signature_;
  _impl_.input_signature_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::unsafe_arena_release_input_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.FunctionSpec.input_signature)
  
  ::tensorflow::StructuredValue* temp = _impl_.input_signature_;
  _impl_.input_signature_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* FunctionSpec::_internal_mutable_input_signature() {
  
  if (_impl_.input_signature_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaForAllocation());
    _impl_.input_signature_ = p;
  }
  return _impl_.input_signature_;
}
inline ::tensorflow::StructuredValue* FunctionSpec::mutable_input_signature() {
  ::tensorflow::StructuredValue* _msg = _internal_mutable_input_signature();
  // @@protoc_insertion_point(field_mutable:tensorflow.FunctionSpec.input_signature)
  return _msg;
}
inline void FunctionSpec::set_allocated_input_signature(::tensorflow::StructuredValue* input_signature) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.input_signature_);
  }
  if (input_signature) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(input_signature));
    if (message_arena != submessage_arena) {
      input_signature = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, input_signature, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.input_signature_ = input_signature;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.FunctionSpec.input_signature)
}

// .tensorflow.FunctionSpec.JitCompile jit_compile = 6;
inline void FunctionSpec::clear_jit_compile() {
  _impl_.jit_compile_ = 0;
}
inline ::tensorflow::FunctionSpec_JitCompile FunctionSpec::_internal_jit_compile() const {
  return static_cast< ::tensorflow::FunctionSpec_JitCompile >(_impl_.jit_compile_);
}
inline ::tensorflow::FunctionSpec_JitCompile FunctionSpec::jit_compile() const {
  // @@protoc_insertion_point(field_get:tensorflow.FunctionSpec.jit_compile)
  return _internal_jit_compile();
}
inline void FunctionSpec::_internal_set_jit_compile(::tensorflow::FunctionSpec_JitCompile value) {
  
  _impl_.jit_compile_ = value;
}
inline void FunctionSpec::set_jit_compile(::tensorflow::FunctionSpec_JitCompile value) {
  _internal_set_jit_compile(value);
  // @@protoc_insertion_point(field_set:tensorflow.FunctionSpec.jit_compile)
}

// -------------------------------------------------------------------

// SavedResource

// string device = 1;
inline void SavedResource::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& SavedResource::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.SavedResource.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SavedResource::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SavedResource.device)
}
inline std::string* SavedResource::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.SavedResource.device)
  return _s;
}
inline const std::string& SavedResource::_internal_device() const {
  return _impl_.device_.Get();
}
inline void SavedResource::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* SavedResource::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* SavedResource::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.SavedResource.device)
  return _impl_.device_.Release();
}
inline void SavedResource::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SavedResource.device)
}

// -------------------------------------------------------------------

// SaveableObject

// int32 save_function = 2;
inline void SaveableObject::clear_save_function() {
  _impl_.save_function_ = 0;
}
inline int32_t SaveableObject::_internal_save_function() const {
  return _impl_.save_function_;
}
inline int32_t SaveableObject::save_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveableObject.save_function)
  return _internal_save_function();
}
inline void SaveableObject::_internal_set_save_function(int32_t value) {
  
  _impl_.save_function_ = value;
}
inline void SaveableObject::set_save_function(int32_t value) {
  _internal_set_save_function(value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveableObject.save_function)
}

// int32 restore_function = 3;
inline void SaveableObject::clear_restore_function() {
  _impl_.restore_function_ = 0;
}
inline int32_t SaveableObject::_internal_restore_function() const {
  return _impl_.restore_function_;
}
inline int32_t SaveableObject::restore_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaveableObject.restore_function)
  return _internal_restore_function();
}
inline void SaveableObject::_internal_set_restore_function(int32_t value) {
  
  _impl_.restore_function_ = value;
}
inline void SaveableObject::set_restore_function(int32_t value) {
  _internal_set_restore_function(value);
  // @@protoc_insertion_point(field_set:tensorflow.SaveableObject.restore_function)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::FunctionSpec_JitCompile> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::FunctionSpec_JitCompile>() {
  return ::tensorflow::FunctionSpec_JitCompile_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaved_5fobject_5fgraph_2eproto
