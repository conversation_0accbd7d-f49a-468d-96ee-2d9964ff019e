// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/lite/toco/types.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2flite_2ftoco_2ftypes_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2flite_2ftoco_2ftypes_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2flite_2ftoco_2ftypes_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2flite_2ftoco_2ftypes_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2flite_2ftoco_2ftypes_2eproto;
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE
namespace toco {

enum IODataType : int {
  IO_DATA_TYPE_UNKNOWN = 0,
  FLOAT = 1,
  QUANTIZED_UINT8 = 2,
  INT32 = 3,
  INT64 = 4,
  STRING = 5,
  QUANTIZED_INT16 = 6,
  BOOL = 7,
  COMPLEX64 = 8,
  QUANTIZED_INT8 = 9,
  FLOAT16 = 10,
  FLOAT64 = 11,
  COMPLEX128 = 12,
  UINT64 = 13,
  RESOURCE = 14,
  VARIANT = 15,
  UINT32 = 16,
  UINT8 = 17,
  INT8 = 18,
  INT16 = 19,
  UINT16 = 20
};
bool IODataType_IsValid(int value);
constexpr IODataType IODataType_MIN = IO_DATA_TYPE_UNKNOWN;
constexpr IODataType IODataType_MAX = UINT16;
constexpr int IODataType_ARRAYSIZE = IODataType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* IODataType_descriptor();
template<typename T>
inline const std::string& IODataType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, IODataType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function IODataType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    IODataType_descriptor(), enum_t_value);
}
inline bool IODataType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IODataType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<IODataType>(
    IODataType_descriptor(), name, value);
}
// ===================================================================


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace toco

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::toco::IODataType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::toco::IODataType>() {
  return ::toco::IODataType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2flite_2ftoco_2ftypes_2eproto
