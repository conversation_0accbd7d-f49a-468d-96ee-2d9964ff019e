/*
 * Copyright 2022 R<PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.gaming.input.idl";
import "windows.storage.streams.idl";
#endif

namespace Windows.Gaming.Input.Custom {
    typedef enum XusbDeviceSubtype XusbDeviceSubtype;
    typedef enum XusbDeviceType XusbDeviceType;
    typedef struct GameControllerVersionInfo GameControllerVersionInfo;
    interface IGameControllerInputSink;
    interface IGameControllerProvider;
    interface IHidGameControllerInputSink;
    interface IHidGameControllerProvider;
    interface IXusbGameControllerInputSink;
    interface IXusbGameControllerProvider;
    interface ICustomGameControllerFactory;
    interface IGameControllerFactoryManagerStatics;
    interface IGameControllerFactoryManagerStatics2;
    runtimeclass GameControllerFactoryManager;
    runtimeclass HidGameControllerProvider;
    runtimeclass XusbGameControllerProvider;

    [contract(Windows.Foundation.UniversalApiContract, 3.0)]
    enum XusbDeviceSubtype
    {
        Unknown = 0,
        Gamepad = 1,
        ArcadePad = 2,
        ArcadeStick = 3,
        FlightStick = 4,
        Wheel = 5,
        Guitar = 6,
        GuitarAlternate = 7,
        GuitarBass = 8,
        DrumKit = 9,
        DancePad = 10,
    };

    [contract(Windows.Foundation.UniversalApiContract, 3.0)]
    enum XusbDeviceType
    {
        Unknown = 0,
        Gamepad = 1,
    };

    [contract(Windows.Foundation.UniversalApiContract, 3.0)]
    struct GameControllerVersionInfo
    {
        UINT16 Major;
        UINT16 Minor;
        UINT16 Build;
        UINT16 Revision;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        uuid(1ff6f922-c640-4c78-a820-9a715c558bcb)
    ]
    interface IGameControllerInputSink : IInspectable
    {
        HRESULT OnInputResumed([in] UINT64 timestamp);
        HRESULT OnInputSuspended([in] UINT64 timestamp);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        uuid(e6d73982-2996-4559-b16c-3e57d46e58d6)
    ]
    interface IGameControllerProvider : IInspectable
    {
        [propget] HRESULT FirmwareVersionInfo([out, retval] Windows.Gaming.Input.Custom.GameControllerVersionInfo *value);
        [propget] HRESULT HardwareProductId([out, retval] UINT16 *value);
        [propget] HRESULT HardwareVendorId([out, retval] UINT16 *value);
        [propget] HRESULT HardwareVersionInfo([out, retval] Windows.Gaming.Input.Custom.GameControllerVersionInfo *value);
        [propget] HRESULT IsConnected([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        uuid(f754c322-182d-40e4-a126-fcee4ffa1e31)
    ]
    interface IHidGameControllerInputSink : IInspectable
        requires Windows.Gaming.Input.Custom.IGameControllerInputSink
    {
        HRESULT OnInputReportReceived([in] UINT64 timestamp, [in] BYTE id, [in] UINT32 report_len,
                                      [in, size_is(report_len)] BYTE *report_buf);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.Gaming.Input.Custom.HidGameControllerProvider),
        uuid(95ce3af4-abf0-4b68-a081-3b7de73ff0e7)
    ]
    interface IHidGameControllerProvider : IInspectable
        requires Windows.Gaming.Input.Custom.IGameControllerProvider
    {
        [propget] HRESULT UsageId([out, retval] UINT16 *value);
        [propget] HRESULT UsagePage([out, retval] UINT16 *value);
        HRESULT GetFeatureReport([in] BYTE id, [in] UINT32 report_len,
                                 [out, size_is(report_len)] BYTE *report_buf);
        HRESULT SendFeatureReport([in] BYTE id, [in] UINT32 report_len,
                                  [in, size_is(report_len)] BYTE *report_buf);
        HRESULT SendOutputReport([in] BYTE id, [in] UINT32 report_len,
                                 [in, size_is(report_len)] BYTE *report_buf);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        uuid(b2ac1d95-6ecb-42b3-8aab-025401ca4712)
    ]
    interface IXusbGameControllerInputSink : IInspectable
        requires Windows.Gaming.Input.Custom.IGameControllerInputSink
    {
        HRESULT OnInputReceived([in] UINT64 timestamp, [in] BYTE id, [in] UINT32 report_len,
                                [in, size_is(report_len)] BYTE *report_buf);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.Gaming.Input.Custom.XusbGameControllerProvider),
        uuid(6e2971eb-0efb-48b4-808b-837643b2f216)
    ]
    interface IXusbGameControllerProvider : IInspectable
        requires Windows.Gaming.Input.Custom.IGameControllerProvider
    {
        HRESULT SetVibration([in] DOUBLE rumble_intensity, [in] DOUBLE buzz_intensity);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        uuid(69a0ae5e-758e-4cbe-ace6-62155fe9126f)
    ]
    interface ICustomGameControllerFactory : IInspectable
    {
        HRESULT CreateGameController([in] Windows.Gaming.Input.Custom.IGameControllerProvider *provider,
                                     [out, retval] IInspectable **value);
        HRESULT OnGameControllerAdded([in] Windows.Gaming.Input.IGameController *value);
        HRESULT OnGameControllerRemoved([in] Windows.Gaming.Input.IGameController *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.Gaming.Input.Custom.GameControllerFactoryManager),
        uuid(36cb66e3-d0a1-4986-a24c-40b137deba9e)
    ]
    interface IGameControllerFactoryManagerStatics : IInspectable
    {
        HRESULT RegisterCustomFactoryForGipInterface([in] Windows.Gaming.Input.Custom.ICustomGameControllerFactory *factory,
                                                     [in] GUID interfaceId);
        HRESULT RegisterCustomFactoryForHardwareId([in] Windows.Gaming.Input.Custom.ICustomGameControllerFactory *factory,
                                                   [in] UINT16 vendor_id, [in] UINT16 product_id);
        HRESULT RegisterCustomFactoryForXusbType([in] Windows.Gaming.Input.Custom.ICustomGameControllerFactory *factory,
                                                 [in] Windows.Gaming.Input.Custom.XusbDeviceType type,
                                                 [in] Windows.Gaming.Input.Custom.XusbDeviceSubtype subtype);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.Gaming.Input.Custom.GameControllerFactoryManager),
        uuid(eace5644-19df-4115-b32a-2793e2aea3bb)
    ]
    interface IGameControllerFactoryManagerStatics2 : IInspectable
        requires Windows.Gaming.Input.Custom.IGameControllerFactoryManagerStatics
    {
        HRESULT TryGetFactoryControllerFromGameController([in] Windows.Gaming.Input.Custom.ICustomGameControllerFactory *factory,
                                                          [in] Windows.Gaming.Input.IGameController *controller,
                                                          [out, retval] Windows.Gaming.Input.IGameController **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile),
        static(Windows.Gaming.Input.Custom.IGameControllerFactoryManagerStatics, Windows.Foundation.UniversalApiContract, 3.0),
        static(Windows.Gaming.Input.Custom.IGameControllerFactoryManagerStatics2, Windows.Foundation.UniversalApiContract, 4.0),
        threading(both)
    ]
    runtimeclass GameControllerFactoryManager
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass HidGameControllerProvider
    {
        [default] interface Windows.Gaming.Input.Custom.IHidGameControllerProvider;
        interface Windows.Gaming.Input.Custom.IGameControllerProvider;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass XusbGameControllerProvider
    {
        [default] interface Windows.Gaming.Input.Custom.IXusbGameControllerProvider;
        interface Windows.Gaming.Input.Custom.IGameControllerProvider;
    }
}
