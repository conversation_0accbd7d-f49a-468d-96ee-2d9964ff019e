/*** Autogenerated by WIDL 10.8 from include/windows.data.xml.dom.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_data_xml_dom_h__
#define __windows_data_xml_dom_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity ABI::Windows::Data::Xml::Dom::IDtdEntity
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IDtdEntity;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation ABI::Windows::Data::Xml::Dom::IDtdNotation
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IDtdNotation;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute ABI::Windows::Data::Xml::Dom::IXmlAttribute
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlAttribute;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection ABI::Windows::Data::Xml::Dom::IXmlCDataSection
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlCDataSection;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData ABI::Windows::Data::Xml::Dom::IXmlCharacterData
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlCharacterData;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment ABI::Windows::Data::Xml::Dom::IXmlComment
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlComment;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument ABI::Windows::Data::Xml::Dom::IXmlDocument
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocument;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment ABI::Windows::Data::Xml::Dom::IXmlDocumentFragment
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentFragment;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO ABI::Windows::Data::Xml::Dom::IXmlDocumentIO
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentIO;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 ABI::Windows::Data::Xml::Dom::IXmlDocumentIO2
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentIO2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics ABI::Windows::Data::Xml::Dom::IXmlDocumentStatics
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType ABI::Windows::Data::Xml::Dom::IXmlDocumentType
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentType;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation ABI::Windows::Data::Xml::Dom::IXmlDomImplementation
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDomImplementation;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement ABI::Windows::Data::Xml::Dom::IXmlElement
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlElement;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference ABI::Windows::Data::Xml::Dom::IXmlEntityReference
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlEntityReference;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings ABI::Windows::Data::Xml::Dom::IXmlLoadSettings
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlLoadSettings;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap ABI::Windows::Data::Xml::Dom::IXmlNamedNodeMap
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNamedNodeMap;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode ABI::Windows::Data::Xml::Dom::IXmlNode
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNode;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList ABI::Windows::Data::Xml::Dom::IXmlNodeList
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNodeList;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector ABI::Windows::Data::Xml::Dom::IXmlNodeSelector
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNodeSelector;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer ABI::Windows::Data::Xml::Dom::IXmlNodeSerializer
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNodeSerializer;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction ABI::Windows::Data::Xml::Dom::IXmlProcessingInstruction
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlProcessingInstruction;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlText_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlText_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlText __x_ABI_CWindows_CData_CXml_CDom_CIXmlText;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText ABI::Windows::Data::Xml::Dom::IXmlText
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlText;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CDtdEntity_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CDtdEntity_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class DtdEntity;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CDtdEntity __x_ABI_CWindows_CData_CXml_CDom_CDtdEntity;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CDtdEntity_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CDtdNotation_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CDtdNotation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class DtdNotation;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CDtdNotation __x_ABI_CWindows_CData_CXml_CDom_CDtdNotation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CDtdNotation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlAttribute_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlAttribute_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlAttribute;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlAttribute __x_ABI_CWindows_CData_CXml_CDom_CXmlAttribute;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlAttribute_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlCDataSection_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlCDataSection_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlCDataSection;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlCDataSection __x_ABI_CWindows_CData_CXml_CDom_CXmlCDataSection;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlCDataSection_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlComment_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlComment_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlComment;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlComment __x_ABI_CWindows_CData_CXml_CDom_CXmlComment;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlComment_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocument_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocument_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlDocument;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlDocument __x_ABI_CWindows_CData_CXml_CDom_CXmlDocument;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocument_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentFragment_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentFragment_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlDocumentFragment;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentFragment __x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentFragment;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentFragment_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentType_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentType_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlDocumentType;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentType __x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentType;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlDocumentType_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlDomImplementation_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlDomImplementation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlDomImplementation;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlDomImplementation __x_ABI_CWindows_CData_CXml_CDom_CXmlDomImplementation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlDomImplementation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlElement_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlElement_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlElement;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlElement __x_ABI_CWindows_CData_CXml_CDom_CXmlElement;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlElement_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlEntityReference_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlEntityReference_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlEntityReference;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlEntityReference __x_ABI_CWindows_CData_CXml_CDom_CXmlEntityReference;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlEntityReference_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlLoadSettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlLoadSettings_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlLoadSettings;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlLoadSettings __x_ABI_CWindows_CData_CXml_CDom_CXmlLoadSettings;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlLoadSettings_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlNamedNodeMap_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlNamedNodeMap_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlNamedNodeMap;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlNamedNodeMap __x_ABI_CWindows_CData_CXml_CDom_CXmlNamedNodeMap;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlNamedNodeMap_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlNodeList_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlNodeList_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlNodeList;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlNodeList __x_ABI_CWindows_CData_CXml_CDom_CXmlNodeList;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlNodeList_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlProcessingInstruction_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlProcessingInstruction_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlProcessingInstruction;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlProcessingInstruction __x_ABI_CWindows_CData_CXml_CDom_CXmlProcessingInstruction;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlProcessingInstruction_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CXmlText_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CXmlText_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    class XmlText;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CXmlText __x_ABI_CWindows_CData_CXml_CDom_CXmlText;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CXml_CDom_CXmlText_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
#define ____FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Data::Xml::Dom::IXmlNode* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
#define ____FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Data::Xml::Dom::IXmlNode* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Data::Xml::Dom::IXmlNode* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Data::Xml::Dom::XmlDocument* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.storage.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CData_CXml_CDom_CNodeType __x_ABI_CWindows_CData_CXml_CDom_CNodeType;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity ABI::Windows::Data::Xml::Dom::IDtdEntity
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IDtdEntity;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation ABI::Windows::Data::Xml::Dom::IDtdNotation
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IDtdNotation;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute ABI::Windows::Data::Xml::Dom::IXmlAttribute
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlAttribute;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection ABI::Windows::Data::Xml::Dom::IXmlCDataSection
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlCDataSection;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData ABI::Windows::Data::Xml::Dom::IXmlCharacterData
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlCharacterData;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment ABI::Windows::Data::Xml::Dom::IXmlComment
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlComment;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument ABI::Windows::Data::Xml::Dom::IXmlDocument
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocument;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment ABI::Windows::Data::Xml::Dom::IXmlDocumentFragment
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentFragment;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO ABI::Windows::Data::Xml::Dom::IXmlDocumentIO
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentIO;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 ABI::Windows::Data::Xml::Dom::IXmlDocumentIO2
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentIO2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics ABI::Windows::Data::Xml::Dom::IXmlDocumentStatics
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType ABI::Windows::Data::Xml::Dom::IXmlDocumentType
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDocumentType;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation ABI::Windows::Data::Xml::Dom::IXmlDomImplementation
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlDomImplementation;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement ABI::Windows::Data::Xml::Dom::IXmlElement
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlElement;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference ABI::Windows::Data::Xml::Dom::IXmlEntityReference
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlEntityReference;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings ABI::Windows::Data::Xml::Dom::IXmlLoadSettings
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlLoadSettings;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap ABI::Windows::Data::Xml::Dom::IXmlNamedNodeMap
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNamedNodeMap;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode ABI::Windows::Data::Xml::Dom::IXmlNode
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNode;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList ABI::Windows::Data::Xml::Dom::IXmlNodeList
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNodeList;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector ABI::Windows::Data::Xml::Dom::IXmlNodeSelector
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNodeSelector;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer ABI::Windows::Data::Xml::Dom::IXmlNodeSerializer
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlNodeSerializer;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction ABI::Windows::Data::Xml::Dom::IXmlProcessingInstruction
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlProcessingInstruction;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlText_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlText_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlText __x_ABI_CWindows_CData_CXml_CDom_CIXmlText;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText ABI::Windows::Data::Xml::Dom::IXmlText
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    interface IXmlText;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
#define ____FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Data::Xml::Dom::IXmlNode* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
#define ____FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Data::Xml::Dom::IXmlNode* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Data::Xml::Dom::IXmlNode* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    enum NodeType {
                        NodeType_Invalid = 0,
                        NodeType_ElementNode = 1,
                        NodeType_AttributeNode = 2,
                        NodeType_TextNode = 3,
                        NodeType_DataSectionNode = 4,
                        NodeType_EntityReferenceNode = 5,
                        NodeType_EntityNode = 6,
                        NodeType_ProcessingInstructionNode = 7,
                        NodeType_CommentNode = 8,
                        NodeType_DocumentNode = 9,
                        NodeType_DocumentTypeNode = 10,
                        NodeType_DocumentFragmentNode = 11,
                        NodeType_NotationNode = 12
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CData_CXml_CDom_CNodeType {
    NodeType_Invalid = 0,
    NodeType_ElementNode = 1,
    NodeType_AttributeNode = 2,
    NodeType_TextNode = 3,
    NodeType_DataSectionNode = 4,
    NodeType_EntityReferenceNode = 5,
    NodeType_EntityNode = 6,
    NodeType_ProcessingInstructionNode = 7,
    NodeType_CommentNode = 8,
    NodeType_DocumentNode = 9,
    NodeType_DocumentTypeNode = 10,
    NodeType_DocumentFragmentNode = 11,
    NodeType_NotationNode = 12
};
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define NodeType __x_ABI_CWindows_CData_CXml_CDom_CNodeType
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IDtdEntity interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity, 0x6a0b5ffc, 0x63b4, 0x480f, 0x9e,0x6a, 0x8a,0x92,0x81,0x6a,0xad,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("6a0b5ffc-63b4-480f-9e6a-8a92816aade4")
                    IDtdEntity : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_PublicId(
                            IInspectable **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_SystemId(
                            IInspectable **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_NotationName(
                            IInspectable **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity, 0x6a0b5ffc, 0x63b4, 0x480f, 0x9e,0x6a, 0x8a,0x92,0x81,0x6a,0xad,0xe4)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This,
        TrustLevel *trustLevel);

    /*** IDtdEntity methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PublicId)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_SystemId)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_NotationName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity *This,
        IInspectable **value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntityVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDtdEntity methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_PublicId(This,value) (This)->lpVtbl->get_PublicId(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_SystemId(This,value) (This)->lpVtbl->get_SystemId(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_NotationName(This,value) (This)->lpVtbl->get_NotationName(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_Release(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDtdEntity methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_PublicId(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This,IInspectable **value) {
    return This->lpVtbl->get_PublicId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_SystemId(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This,IInspectable **value) {
    return This->lpVtbl->get_SystemId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_NotationName(__x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity* This,IInspectable **value) {
    return This->lpVtbl->get_NotationName(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IDtdEntity IID___x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity
#define IDtdEntityVtbl __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntityVtbl
#define IDtdEntity __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity
#define IDtdEntity_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_QueryInterface
#define IDtdEntity_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_AddRef
#define IDtdEntity_Release __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_Release
#define IDtdEntity_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetIids
#define IDtdEntity_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetRuntimeClassName
#define IDtdEntity_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_GetTrustLevel
#define IDtdEntity_get_PublicId __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_PublicId
#define IDtdEntity_get_SystemId __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_SystemId
#define IDtdEntity_get_NotationName __x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_get_NotationName
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIDtdEntity_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDtdNotation interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation, 0x8cb4e04d, 0x6d46, 0x4edb, 0xab,0x73, 0xdf,0x83,0xc5,0x1a,0xd3,0x97);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("8cb4e04d-6d46-4edb-ab73-df83c51ad397")
                    IDtdNotation : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_PublicId(
                            IInspectable **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_SystemId(
                            IInspectable **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation, 0x8cb4e04d, 0x6d46, 0x4edb, 0xab,0x73, 0xdf,0x83,0xc5,0x1a,0xd3,0x97)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation *This,
        TrustLevel *trustLevel);

    /*** IDtdNotation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PublicId)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_SystemId)(
        __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation *This,
        IInspectable **value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotationVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDtdNotation methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_get_PublicId(This,value) (This)->lpVtbl->get_PublicId(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_get_SystemId(This,value) (This)->lpVtbl->get_SystemId(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_Release(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDtdNotation methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_get_PublicId(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation* This,IInspectable **value) {
    return This->lpVtbl->get_PublicId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_get_SystemId(__x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation* This,IInspectable **value) {
    return This->lpVtbl->get_SystemId(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IDtdNotation IID___x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation
#define IDtdNotationVtbl __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotationVtbl
#define IDtdNotation __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation
#define IDtdNotation_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_QueryInterface
#define IDtdNotation_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_AddRef
#define IDtdNotation_Release __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_Release
#define IDtdNotation_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetIids
#define IDtdNotation_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetRuntimeClassName
#define IDtdNotation_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_GetTrustLevel
#define IDtdNotation_get_PublicId __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_get_PublicId
#define IDtdNotation_get_SystemId __x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_get_SystemId
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIDtdNotation_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlAttribute interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute, 0xac144aa4, 0xb4f1, 0x4db6, 0xb2,0x06, 0x8a,0x22,0xc3,0x08,0xdb,0x0a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("ac144aa4-b4f1-4db6-b206-8a22c308db0a")
                    IXmlAttribute : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Name(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Specified(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Value(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_Value(
                            HSTRING value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute, 0xac144aa4, 0xb4f1, 0x4db6, 0xb2,0x06, 0x8a,0x22,0xc3,0x08,0xdb,0x0a)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttributeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This,
        TrustLevel *trustLevel);

    /*** IXmlAttribute methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Specified)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *This,
        HSTRING value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttributeVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttributeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlAttribute methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Specified(This,value) (This)->lpVtbl->get_Specified(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_put_Value(This,value) (This)->lpVtbl->put_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlAttribute methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Name(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Specified(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This,boolean *value) {
    return This->lpVtbl->get_Specified(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Value(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This,HSTRING *value) {
    return This->lpVtbl->get_Value(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_put_Value(__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute* This,HSTRING value) {
    return This->lpVtbl->put_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlAttribute IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute
#define IXmlAttributeVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttributeVtbl
#define IXmlAttribute __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute
#define IXmlAttribute_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_QueryInterface
#define IXmlAttribute_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_AddRef
#define IXmlAttribute_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_Release
#define IXmlAttribute_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetIids
#define IXmlAttribute_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetRuntimeClassName
#define IXmlAttribute_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_GetTrustLevel
#define IXmlAttribute_get_Name __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Name
#define IXmlAttribute_get_Specified __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Specified
#define IXmlAttribute_get_Value __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_get_Value
#define IXmlAttribute_put_Value __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_put_Value
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlCDataSection interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection, 0x4d04b46f, 0xc8bd, 0x45b4, 0x88,0x99, 0x04,0x00,0xd7,0xc2,0xc6,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("4d04b46f-c8bd-45b4-8899-0400d7c2c60f")
                    IXmlCDataSection : public IInspectable
                    {
                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection, 0x4d04b46f, 0xc8bd, 0x45b4, 0x88,0x99, 0x04,0x00,0xd7,0xc2,0xc6,0x0f)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSectionVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlCDataSection IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection
#define IXmlCDataSectionVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSectionVtbl
#define IXmlCDataSection __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection
#define IXmlCDataSection_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_QueryInterface
#define IXmlCDataSection_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_AddRef
#define IXmlCDataSection_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_Release
#define IXmlCDataSection_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetIids
#define IXmlCDataSection_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetRuntimeClassName
#define IXmlCDataSection_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_GetTrustLevel
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlCharacterData interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData, 0x132e42ab, 0x4e36, 0x4df6, 0xb1,0xc8, 0x0c,0xe6,0x2f,0xd8,0x8b,0x26);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("132e42ab-4e36-4df6-b1c8-0ce62fd88b26")
                    IXmlCharacterData : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Data(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_Data(
                            HSTRING value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Length(
                            UINT32 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SubstringData(
                            UINT32 offset,
                            UINT32 count,
                            HSTRING *data) = 0;

                        virtual HRESULT STDMETHODCALLTYPE AppendData(
                            HSTRING data) = 0;

                        virtual HRESULT STDMETHODCALLTYPE InsertData(
                            UINT32 offset,
                            HSTRING data) = 0;

                        virtual HRESULT STDMETHODCALLTYPE DeleteData(
                            UINT32 offset,
                            UINT32 count) = 0;

                        virtual HRESULT STDMETHODCALLTYPE ReplaceData(
                            UINT32 offset,
                            UINT32 count,
                            HSTRING data) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData, 0x132e42ab, 0x4e36, 0x4df6, 0xb1,0xc8, 0x0c,0xe6,0x2f,0xd8,0x8b,0x26)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterDataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        TrustLevel *trustLevel);

    /*** IXmlCharacterData methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Data)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_Data)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_Length)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *SubstringData)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        UINT32 offset,
        UINT32 count,
        HSTRING *data);

    HRESULT (STDMETHODCALLTYPE *AppendData)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        HSTRING data);

    HRESULT (STDMETHODCALLTYPE *InsertData)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        UINT32 offset,
        HSTRING data);

    HRESULT (STDMETHODCALLTYPE *DeleteData)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        UINT32 offset,
        UINT32 count);

    HRESULT (STDMETHODCALLTYPE *ReplaceData)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData *This,
        UINT32 offset,
        UINT32 count,
        HSTRING data);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterDataVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterDataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlCharacterData methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_get_Data(This,value) (This)->lpVtbl->get_Data(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_put_Data(This,value) (This)->lpVtbl->put_Data(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_get_Length(This,value) (This)->lpVtbl->get_Length(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_SubstringData(This,offset,count,data) (This)->lpVtbl->SubstringData(This,offset,count,data)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_AppendData(This,data) (This)->lpVtbl->AppendData(This,data)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_InsertData(This,offset,data) (This)->lpVtbl->InsertData(This,offset,data)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_DeleteData(This,offset,count) (This)->lpVtbl->DeleteData(This,offset,count)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_ReplaceData(This,offset,count,data) (This)->lpVtbl->ReplaceData(This,offset,count,data)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlCharacterData methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_get_Data(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,HSTRING *value) {
    return This->lpVtbl->get_Data(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_put_Data(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,HSTRING value) {
    return This->lpVtbl->put_Data(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_get_Length(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,UINT32 *value) {
    return This->lpVtbl->get_Length(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_SubstringData(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,UINT32 offset,UINT32 count,HSTRING *data) {
    return This->lpVtbl->SubstringData(This,offset,count,data);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_AppendData(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,HSTRING data) {
    return This->lpVtbl->AppendData(This,data);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_InsertData(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,UINT32 offset,HSTRING data) {
    return This->lpVtbl->InsertData(This,offset,data);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_DeleteData(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,UINT32 offset,UINT32 count) {
    return This->lpVtbl->DeleteData(This,offset,count);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_ReplaceData(__x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData* This,UINT32 offset,UINT32 count,HSTRING data) {
    return This->lpVtbl->ReplaceData(This,offset,count,data);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlCharacterData IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData
#define IXmlCharacterDataVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterDataVtbl
#define IXmlCharacterData __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData
#define IXmlCharacterData_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_QueryInterface
#define IXmlCharacterData_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_AddRef
#define IXmlCharacterData_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_Release
#define IXmlCharacterData_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetIids
#define IXmlCharacterData_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetRuntimeClassName
#define IXmlCharacterData_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_GetTrustLevel
#define IXmlCharacterData_get_Data __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_get_Data
#define IXmlCharacterData_put_Data __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_put_Data
#define IXmlCharacterData_get_Length __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_get_Length
#define IXmlCharacterData_SubstringData __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_SubstringData
#define IXmlCharacterData_AppendData __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_AppendData
#define IXmlCharacterData_InsertData __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_InsertData
#define IXmlCharacterData_DeleteData __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_DeleteData
#define IXmlCharacterData_ReplaceData __x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_ReplaceData
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlCharacterData_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlComment interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlComment, 0xbca474d5, 0xb61f, 0x4611, 0x9c,0xac, 0x2e,0x92,0xe3,0x47,0x6d,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("bca474d5-b61f-4611-9cac-2e92e3476d47")
                    IXmlComment : public IInspectable
                    {
                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlComment, 0xbca474d5, 0xb61f, 0x4611, 0x9c,0xac, 0x2e,0x92,0xe3,0x47,0x6d,0x47)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlCommentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlCommentVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlCommentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlComment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlComment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlComment* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlComment* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlComment* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlComment* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlComment IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlComment
#define IXmlCommentVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlCommentVtbl
#define IXmlComment __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment
#define IXmlComment_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_QueryInterface
#define IXmlComment_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_AddRef
#define IXmlComment_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_Release
#define IXmlComment_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetIids
#define IXmlComment_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetRuntimeClassName
#define IXmlComment_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_GetTrustLevel
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlComment_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlDocument interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument, 0xf7f3a506, 0x1e87, 0x42d6, 0xbc,0xfb, 0xb8,0xc8,0x09,0xfa,0x54,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("f7f3a506-1e87-42d6-bcfb-b8c809fa5494")
                    IXmlDocument : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Doctype(
                            ABI::Windows::Data::Xml::Dom::IXmlDocumentType **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Implementation(
                            ABI::Windows::Data::Xml::Dom::IXmlDomImplementation **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_DocumentElement(
                            ABI::Windows::Data::Xml::Dom::IXmlElement **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateElement(
                            HSTRING tag_name,
                            ABI::Windows::Data::Xml::Dom::IXmlElement **new_element) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateDocumentFragment(
                            ABI::Windows::Data::Xml::Dom::IXmlDocumentFragment **new_document_fragment) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateTextNode(
                            HSTRING data,
                            ABI::Windows::Data::Xml::Dom::IXmlText **new_text_node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateComment(
                            HSTRING data,
                            ABI::Windows::Data::Xml::Dom::IXmlComment **new_comment) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateProcessingInstruction(
                            HSTRING target,
                            HSTRING data,
                            ABI::Windows::Data::Xml::Dom::IXmlProcessingInstruction **new_processing_instruction) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateAttribute(
                            HSTRING name,
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute **new_attribute) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateEntityReference(
                            HSTRING name,
                            ABI::Windows::Data::Xml::Dom::IXmlEntityReference **new_entity_reference) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetElementsByTagName(
                            HSTRING tag_name,
                            ABI::Windows::Data::Xml::Dom::IXmlNodeList **elements) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateCDataSection(
                            HSTRING data,
                            ABI::Windows::Data::Xml::Dom::IXmlCDataSection **new_cdata_section) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_DocumentUri(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateAttributeNS(
                            IInspectable *namespace_uri,
                            HSTRING qualified_name,
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute **new_attribute) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateElementNS(
                            IInspectable *namespace_uri,
                            HSTRING qualified_name,
                            ABI::Windows::Data::Xml::Dom::IXmlElement **new_element) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetElementById(
                            HSTRING element_id,
                            ABI::Windows::Data::Xml::Dom::IXmlElement **element) = 0;

                        virtual HRESULT STDMETHODCALLTYPE ImportNode(
                            ABI::Windows::Data::Xml::Dom::IXmlNode *node,
                            boolean deep,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **new_node) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument, 0xf7f3a506, 0x1e87, 0x42d6, 0xbc,0xfb, 0xb8,0xc8,0x09,0xfa,0x54,0x94)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        TrustLevel *trustLevel);

    /*** IXmlDocument methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Doctype)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType **value);

    HRESULT (STDMETHODCALLTYPE *get_Implementation)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation **value);

    HRESULT (STDMETHODCALLTYPE *get_DocumentElement)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement **value);

    HRESULT (STDMETHODCALLTYPE *CreateElement)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING tag_name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement **new_element);

    HRESULT (STDMETHODCALLTYPE *CreateDocumentFragment)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment **new_document_fragment);

    HRESULT (STDMETHODCALLTYPE *CreateTextNode)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING data,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText **new_text_node);

    HRESULT (STDMETHODCALLTYPE *CreateComment)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING data,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlComment **new_comment);

    HRESULT (STDMETHODCALLTYPE *CreateProcessingInstruction)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING target,
        HSTRING data,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction **new_processing_instruction);

    HRESULT (STDMETHODCALLTYPE *CreateAttribute)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **new_attribute);

    HRESULT (STDMETHODCALLTYPE *CreateEntityReference)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference **new_entity_reference);

    HRESULT (STDMETHODCALLTYPE *GetElementsByTagName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING tag_name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **elements);

    HRESULT (STDMETHODCALLTYPE *CreateCDataSection)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING data,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection **new_cdata_section);

    HRESULT (STDMETHODCALLTYPE *get_DocumentUri)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *CreateAttributeNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        IInspectable *namespace_uri,
        HSTRING qualified_name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **new_attribute);

    HRESULT (STDMETHODCALLTYPE *CreateElementNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        IInspectable *namespace_uri,
        HSTRING qualified_name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement **new_element);

    HRESULT (STDMETHODCALLTYPE *GetElementById)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        HSTRING element_id,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement **element);

    HRESULT (STDMETHODCALLTYPE *ImportNode)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *node,
        boolean deep,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **new_node);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlDocument methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_Doctype(This,value) (This)->lpVtbl->get_Doctype(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_Implementation(This,value) (This)->lpVtbl->get_Implementation(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_DocumentElement(This,value) (This)->lpVtbl->get_DocumentElement(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateElement(This,tag_name,new_element) (This)->lpVtbl->CreateElement(This,tag_name,new_element)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateDocumentFragment(This,new_document_fragment) (This)->lpVtbl->CreateDocumentFragment(This,new_document_fragment)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateTextNode(This,data,new_text_node) (This)->lpVtbl->CreateTextNode(This,data,new_text_node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateComment(This,data,new_comment) (This)->lpVtbl->CreateComment(This,data,new_comment)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateProcessingInstruction(This,target,data,new_processing_instruction) (This)->lpVtbl->CreateProcessingInstruction(This,target,data,new_processing_instruction)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateAttribute(This,name,new_attribute) (This)->lpVtbl->CreateAttribute(This,name,new_attribute)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateEntityReference(This,name,new_entity_reference) (This)->lpVtbl->CreateEntityReference(This,name,new_entity_reference)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetElementsByTagName(This,tag_name,elements) (This)->lpVtbl->GetElementsByTagName(This,tag_name,elements)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateCDataSection(This,data,new_cdata_section) (This)->lpVtbl->CreateCDataSection(This,data,new_cdata_section)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_DocumentUri(This,value) (This)->lpVtbl->get_DocumentUri(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateAttributeNS(This,namespace_uri,qualified_name,new_attribute) (This)->lpVtbl->CreateAttributeNS(This,namespace_uri,qualified_name,new_attribute)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateElementNS(This,namespace_uri,qualified_name,new_element) (This)->lpVtbl->CreateElementNS(This,namespace_uri,qualified_name,new_element)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetElementById(This,element_id,element) (This)->lpVtbl->GetElementById(This,element_id,element)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_ImportNode(This,node,deep,new_node) (This)->lpVtbl->ImportNode(This,node,deep,new_node)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlDocument methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_Doctype(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType **value) {
    return This->lpVtbl->get_Doctype(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_Implementation(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation **value) {
    return This->lpVtbl->get_Implementation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_DocumentElement(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement **value) {
    return This->lpVtbl->get_DocumentElement(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateElement(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING tag_name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement **new_element) {
    return This->lpVtbl->CreateElement(This,tag_name,new_element);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateDocumentFragment(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment **new_document_fragment) {
    return This->lpVtbl->CreateDocumentFragment(This,new_document_fragment);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateTextNode(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING data,__x_ABI_CWindows_CData_CXml_CDom_CIXmlText **new_text_node) {
    return This->lpVtbl->CreateTextNode(This,data,new_text_node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateComment(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING data,__x_ABI_CWindows_CData_CXml_CDom_CIXmlComment **new_comment) {
    return This->lpVtbl->CreateComment(This,data,new_comment);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateProcessingInstruction(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING target,HSTRING data,__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction **new_processing_instruction) {
    return This->lpVtbl->CreateProcessingInstruction(This,target,data,new_processing_instruction);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateAttribute(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **new_attribute) {
    return This->lpVtbl->CreateAttribute(This,name,new_attribute);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateEntityReference(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference **new_entity_reference) {
    return This->lpVtbl->CreateEntityReference(This,name,new_entity_reference);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetElementsByTagName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING tag_name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **elements) {
    return This->lpVtbl->GetElementsByTagName(This,tag_name,elements);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateCDataSection(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING data,__x_ABI_CWindows_CData_CXml_CDom_CIXmlCDataSection **new_cdata_section) {
    return This->lpVtbl->CreateCDataSection(This,data,new_cdata_section);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_DocumentUri(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING *value) {
    return This->lpVtbl->get_DocumentUri(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateAttributeNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,IInspectable *namespace_uri,HSTRING qualified_name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **new_attribute) {
    return This->lpVtbl->CreateAttributeNS(This,namespace_uri,qualified_name,new_attribute);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateElementNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,IInspectable *namespace_uri,HSTRING qualified_name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement **new_element) {
    return This->lpVtbl->CreateElementNS(This,namespace_uri,qualified_name,new_element);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetElementById(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,HSTRING element_id,__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement **element) {
    return This->lpVtbl->GetElementById(This,element_id,element);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_ImportNode(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *node,boolean deep,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **new_node) {
    return This->lpVtbl->ImportNode(This,node,deep,new_node);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlDocument IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument
#define IXmlDocumentVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentVtbl
#define IXmlDocument __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument
#define IXmlDocument_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_QueryInterface
#define IXmlDocument_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_AddRef
#define IXmlDocument_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_Release
#define IXmlDocument_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetIids
#define IXmlDocument_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetRuntimeClassName
#define IXmlDocument_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetTrustLevel
#define IXmlDocument_get_Doctype __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_Doctype
#define IXmlDocument_get_Implementation __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_Implementation
#define IXmlDocument_get_DocumentElement __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_DocumentElement
#define IXmlDocument_CreateElement __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateElement
#define IXmlDocument_CreateDocumentFragment __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateDocumentFragment
#define IXmlDocument_CreateTextNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateTextNode
#define IXmlDocument_CreateComment __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateComment
#define IXmlDocument_CreateProcessingInstruction __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateProcessingInstruction
#define IXmlDocument_CreateAttribute __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateAttribute
#define IXmlDocument_CreateEntityReference __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateEntityReference
#define IXmlDocument_GetElementsByTagName __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetElementsByTagName
#define IXmlDocument_CreateCDataSection __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateCDataSection
#define IXmlDocument_get_DocumentUri __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_get_DocumentUri
#define IXmlDocument_CreateAttributeNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateAttributeNS
#define IXmlDocument_CreateElementNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_CreateElementNS
#define IXmlDocument_GetElementById __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_GetElementById
#define IXmlDocument_ImportNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_ImportNode
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlDocumentFragment interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment, 0xe2ea6a96, 0x0c21, 0x44a5, 0x8b,0xc9, 0x9e,0x4a,0x26,0x27,0x08,0xec);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("e2ea6a96-0c21-44a5-8bc9-9e4a262708ec")
                    IXmlDocumentFragment : public IInspectable
                    {
                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment, 0xe2ea6a96, 0x0c21, 0x44a5, 0x8b,0xc9, 0x9e,0x4a,0x26,0x27,0x08,0xec)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragmentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragmentVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragmentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlDocumentFragment IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment
#define IXmlDocumentFragmentVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragmentVtbl
#define IXmlDocumentFragment __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment
#define IXmlDocumentFragment_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_QueryInterface
#define IXmlDocumentFragment_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_AddRef
#define IXmlDocumentFragment_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_Release
#define IXmlDocumentFragment_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetIids
#define IXmlDocumentFragment_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetRuntimeClassName
#define IXmlDocumentFragment_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_GetTrustLevel
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentFragment_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlDocumentIO interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO, 0x6cd0e74e, 0xee65, 0x4489, 0x9e,0xbf, 0xca,0x43,0xe8,0x7b,0xa6,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("6cd0e74e-ee65-4489-9ebf-ca43e87ba637")
                    IXmlDocumentIO : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE LoadXml(
                            HSTRING xml) = 0;

                        virtual HRESULT STDMETHODCALLTYPE LoadXmlWithSettings(
                            HSTRING xml,
                            ABI::Windows::Data::Xml::Dom::IXmlLoadSettings *load_settings) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SaveToFileAsync(
                            ABI::Windows::Storage::IStorageFile *file,
                            ABI::Windows::Foundation::IAsyncAction **async_info) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO, 0x6cd0e74e, 0xee65, 0x4489, 0x9e,0xbf, 0xca,0x43,0xe8,0x7b,0xa6,0x37)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIOVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This,
        TrustLevel *trustLevel);

    /*** IXmlDocumentIO methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadXml)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This,
        HSTRING xml);

    HRESULT (STDMETHODCALLTYPE *LoadXmlWithSettings)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This,
        HSTRING xml,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *load_settings);

    HRESULT (STDMETHODCALLTYPE *SaveToFileAsync)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO *This,
        __x_ABI_CWindows_CStorage_CIStorageFile *file,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **async_info);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIOVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIOVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlDocumentIO methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_LoadXml(This,xml) (This)->lpVtbl->LoadXml(This,xml)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_LoadXmlWithSettings(This,xml,load_settings) (This)->lpVtbl->LoadXmlWithSettings(This,xml,load_settings)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_SaveToFileAsync(This,file,async_info) (This)->lpVtbl->SaveToFileAsync(This,file,async_info)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlDocumentIO methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_LoadXml(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This,HSTRING xml) {
    return This->lpVtbl->LoadXml(This,xml);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_LoadXmlWithSettings(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This,HSTRING xml,__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *load_settings) {
    return This->lpVtbl->LoadXmlWithSettings(This,xml,load_settings);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_SaveToFileAsync(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO* This,__x_ABI_CWindows_CStorage_CIStorageFile *file,__x_ABI_CWindows_CFoundation_CIAsyncAction **async_info) {
    return This->lpVtbl->SaveToFileAsync(This,file,async_info);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlDocumentIO IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO
#define IXmlDocumentIOVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIOVtbl
#define IXmlDocumentIO __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO
#define IXmlDocumentIO_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_QueryInterface
#define IXmlDocumentIO_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_AddRef
#define IXmlDocumentIO_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_Release
#define IXmlDocumentIO_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetIids
#define IXmlDocumentIO_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetRuntimeClassName
#define IXmlDocumentIO_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_GetTrustLevel
#define IXmlDocumentIO_LoadXml __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_LoadXml
#define IXmlDocumentIO_LoadXmlWithSettings __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_LoadXmlWithSettings
#define IXmlDocumentIO_SaveToFileAsync __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_SaveToFileAsync
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlDocumentIO2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2, 0x5d034661, 0x7bd8, 0x4ad5, 0x9e,0xbf, 0x81,0xe6,0x34,0x72,0x63,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("5d034661-7bd8-4ad5-9ebf-81e6347263b1")
                    IXmlDocumentIO2 : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE LoadXmlFromBuffer(
                            ABI::Windows::Storage::Streams::IBuffer *buffer) = 0;

                        virtual HRESULT STDMETHODCALLTYPE LoadXmlFromBufferWithSettings(
                            ABI::Windows::Storage::Streams::IBuffer *buffer,
                            ABI::Windows::Data::Xml::Dom::IXmlLoadSettings *load_settings) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2, 0x5d034661, 0x7bd8, 0x4ad5, 0x9e,0xbf, 0x81,0xe6,0x34,0x72,0x63,0xb1)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 *This,
        TrustLevel *trustLevel);

    /*** IXmlDocumentIO2 methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadXmlFromBuffer)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer);

    HRESULT (STDMETHODCALLTYPE *LoadXmlFromBufferWithSettings)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *load_settings);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2Vtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2 {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlDocumentIO2 methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_LoadXmlFromBuffer(This,buffer) (This)->lpVtbl->LoadXmlFromBuffer(This,buffer)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_LoadXmlFromBufferWithSettings(This,buffer,load_settings) (This)->lpVtbl->LoadXmlFromBufferWithSettings(This,buffer,load_settings)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlDocumentIO2 methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_LoadXmlFromBuffer(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer) {
    return This->lpVtbl->LoadXmlFromBuffer(This,buffer);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_LoadXmlFromBufferWithSettings(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *load_settings) {
    return This->lpVtbl->LoadXmlFromBufferWithSettings(This,buffer,load_settings);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlDocumentIO2 IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2
#define IXmlDocumentIO2Vtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2Vtbl
#define IXmlDocumentIO2 __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2
#define IXmlDocumentIO2_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_QueryInterface
#define IXmlDocumentIO2_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_AddRef
#define IXmlDocumentIO2_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_Release
#define IXmlDocumentIO2_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetIids
#define IXmlDocumentIO2_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetRuntimeClassName
#define IXmlDocumentIO2_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_GetTrustLevel
#define IXmlDocumentIO2_LoadXmlFromBuffer __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_LoadXmlFromBuffer
#define IXmlDocumentIO2_LoadXmlFromBufferWithSettings __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_LoadXmlFromBufferWithSettings
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentIO2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlDocumentStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics, 0x5543d254, 0xd757, 0x4b79, 0x95,0x39, 0x23,0x2b,0x18,0xf5,0x0b,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("5543d254-d757-4b79-9539-232b18f50bf1")
                    IXmlDocumentStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE LoadFromUriAsync(
                            ABI::Windows::Foundation::IUriRuntimeClass *uri,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > **async_info) = 0;

                        virtual HRESULT STDMETHODCALLTYPE LoadFromUriWithSettingsAsync(
                            ABI::Windows::Foundation::IUriRuntimeClass *uri,
                            ABI::Windows::Data::Xml::Dom::IXmlLoadSettings *load_settings,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > **async_info) = 0;

                        virtual HRESULT STDMETHODCALLTYPE LoadFromFileAsync(
                            ABI::Windows::Storage::IStorageFile *file,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > **async_info) = 0;

                        virtual HRESULT STDMETHODCALLTYPE LoadFromFileWithSettingsAsync(
                            ABI::Windows::Storage::IStorageFile *file,
                            ABI::Windows::Data::Xml::Dom::IXmlLoadSettings *load_settings,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > **async_info) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics, 0x5543d254, 0xd757, 0x4b79, 0x95,0x39, 0x23,0x2b,0x18,0xf5,0x0b,0xf1)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This,
        TrustLevel *trustLevel);

    /*** IXmlDocumentStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadFromUriAsync)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument **async_info);

    HRESULT (STDMETHODCALLTYPE *LoadFromUriWithSettingsAsync)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *load_settings,
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument **async_info);

    HRESULT (STDMETHODCALLTYPE *LoadFromFileAsync)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFile *file,
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument **async_info);

    HRESULT (STDMETHODCALLTYPE *LoadFromFileWithSettingsAsync)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFile *file,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *load_settings,
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument **async_info);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStaticsVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlDocumentStatics methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromUriAsync(This,uri,async_info) (This)->lpVtbl->LoadFromUriAsync(This,uri,async_info)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromUriWithSettingsAsync(This,uri,load_settings,async_info) (This)->lpVtbl->LoadFromUriWithSettingsAsync(This,uri,load_settings,async_info)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromFileAsync(This,file,async_info) (This)->lpVtbl->LoadFromFileAsync(This,file,async_info)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromFileWithSettingsAsync(This,file,load_settings,async_info) (This)->lpVtbl->LoadFromFileWithSettingsAsync(This,file,load_settings,async_info)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlDocumentStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromUriAsync(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument **async_info) {
    return This->lpVtbl->LoadFromUriAsync(This,uri,async_info);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromUriWithSettingsAsync(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *load_settings,__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument **async_info) {
    return This->lpVtbl->LoadFromUriWithSettingsAsync(This,uri,load_settings,async_info);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromFileAsync(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This,__x_ABI_CWindows_CStorage_CIStorageFile *file,__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument **async_info) {
    return This->lpVtbl->LoadFromFileAsync(This,file,async_info);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromFileWithSettingsAsync(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics* This,__x_ABI_CWindows_CStorage_CIStorageFile *file,__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *load_settings,__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument **async_info) {
    return This->lpVtbl->LoadFromFileWithSettingsAsync(This,file,load_settings,async_info);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlDocumentStatics IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics
#define IXmlDocumentStaticsVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStaticsVtbl
#define IXmlDocumentStatics __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics
#define IXmlDocumentStatics_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_QueryInterface
#define IXmlDocumentStatics_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_AddRef
#define IXmlDocumentStatics_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_Release
#define IXmlDocumentStatics_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetIids
#define IXmlDocumentStatics_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetRuntimeClassName
#define IXmlDocumentStatics_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_GetTrustLevel
#define IXmlDocumentStatics_LoadFromUriAsync __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromUriAsync
#define IXmlDocumentStatics_LoadFromUriWithSettingsAsync __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromUriWithSettingsAsync
#define IXmlDocumentStatics_LoadFromFileAsync __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromFileAsync
#define IXmlDocumentStatics_LoadFromFileWithSettingsAsync __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_LoadFromFileWithSettingsAsync
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlDocumentType interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType, 0xf7342425, 0x9781, 0x4964, 0x8e,0x94, 0x9b,0x1c,0x6d,0xfc,0x9b,0xc7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("f7342425-9781-4964-8e94-9b1c6dfc9bc7")
                    IXmlDocumentType : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Name(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Entities(
                            ABI::Windows::Data::Xml::Dom::IXmlNamedNodeMap **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Notations(
                            ABI::Windows::Data::Xml::Dom::IXmlNamedNodeMap **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType, 0xf7342425, 0x9781, 0x4964, 0x8e,0x94, 0x9b,0x1c,0x6d,0xfc,0x9b,0xc7)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This,
        TrustLevel *trustLevel);

    /*** IXmlDocumentType methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Entities)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap **value);

    HRESULT (STDMETHODCALLTYPE *get_Notations)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap **value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentTypeVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlDocumentType methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Entities(This,value) (This)->lpVtbl->get_Entities(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Notations(This,value) (This)->lpVtbl->get_Notations(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlDocumentType methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Name(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Entities(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap **value) {
    return This->lpVtbl->get_Entities(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Notations(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap **value) {
    return This->lpVtbl->get_Notations(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlDocumentType IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType
#define IXmlDocumentTypeVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentTypeVtbl
#define IXmlDocumentType __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType
#define IXmlDocumentType_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_QueryInterface
#define IXmlDocumentType_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_AddRef
#define IXmlDocumentType_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_Release
#define IXmlDocumentType_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetIids
#define IXmlDocumentType_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetRuntimeClassName
#define IXmlDocumentType_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_GetTrustLevel
#define IXmlDocumentType_get_Name __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Name
#define IXmlDocumentType_get_Entities __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Entities
#define IXmlDocumentType_get_Notations __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_get_Notations
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDocumentType_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlDomImplementation interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation, 0x6de58132, 0xf11d, 0x4fbb, 0x8c,0xc6, 0x58,0x3c,0xba,0x93,0x11,0x2f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("6de58132-f11d-4fbb-8cc6-583cba93112f")
                    IXmlDomImplementation : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE HasFeature(
                            HSTRING feature,
                            IInspectable *version,
                            boolean *feature_supported) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation, 0x6de58132, 0xf11d, 0x4fbb, 0x8c,0xc6, 0x58,0x3c,0xba,0x93,0x11,0x2f)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation *This,
        TrustLevel *trustLevel);

    /*** IXmlDomImplementation methods ***/
    HRESULT (STDMETHODCALLTYPE *HasFeature)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation *This,
        HSTRING feature,
        IInspectable *version,
        boolean *feature_supported);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementationVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlDomImplementation methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_HasFeature(This,feature,version,feature_supported) (This)->lpVtbl->HasFeature(This,feature,version,feature_supported)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlDomImplementation methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_HasFeature(__x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation* This,HSTRING feature,IInspectable *version,boolean *feature_supported) {
    return This->lpVtbl->HasFeature(This,feature,version,feature_supported);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlDomImplementation IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation
#define IXmlDomImplementationVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementationVtbl
#define IXmlDomImplementation __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation
#define IXmlDomImplementation_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_QueryInterface
#define IXmlDomImplementation_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_AddRef
#define IXmlDomImplementation_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_Release
#define IXmlDomImplementation_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetIids
#define IXmlDomImplementation_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetRuntimeClassName
#define IXmlDomImplementation_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_GetTrustLevel
#define IXmlDomImplementation_HasFeature __x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_HasFeature
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlDomImplementation_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlElement interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlElement, 0x2dfb8a1f, 0x6b10, 0x4ef8, 0x9f,0x83, 0xef,0xcc,0xe8,0xfa,0xec,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("2dfb8a1f-6b10-4ef8-9f83-efcce8faec37")
                    IXmlElement : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_TagName(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetAttribute(
                            HSTRING attribute_name,
                            HSTRING *attribute_value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetAttribute(
                            HSTRING attribute_name,
                            HSTRING attribute_value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RemoveAttribute(
                            HSTRING attribute_name) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetAttributeNode(
                            HSTRING attribute_name,
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute **attribute_node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetAttributeNode(
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute *new_attribute,
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute **previous_attribute) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RemoveAttributeNode(
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute *attribute_node,
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute **removed_attribute) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetElementsByTagName(
                            HSTRING tag_name,
                            ABI::Windows::Data::Xml::Dom::IXmlNodeList **elements) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetAttributeNS(
                            IInspectable *namespace_uri,
                            HSTRING qualified_name,
                            HSTRING value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetAttributeNS(
                            IInspectable *namespace_uri,
                            HSTRING local_name,
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RemoveAttributeNS(
                            IInspectable *namespace_uri,
                            HSTRING local_name) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetAttributeNodeNS(
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute *new_attribute,
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute **previous_attribute) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetAttributeNodeNS(
                            IInspectable *namespace_uri,
                            HSTRING local_name,
                            ABI::Windows::Data::Xml::Dom::IXmlAttribute **previous_attribute) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement, 0x2dfb8a1f, 0x6b10, 0x4ef8, 0x9f,0x83, 0xef,0xcc,0xe8,0xfa,0xec,0x37)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlElementVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        TrustLevel *trustLevel);

    /*** IXmlElement methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TagName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetAttribute)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        HSTRING attribute_name,
        HSTRING *attribute_value);

    HRESULT (STDMETHODCALLTYPE *SetAttribute)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        HSTRING attribute_name,
        HSTRING attribute_value);

    HRESULT (STDMETHODCALLTYPE *RemoveAttribute)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        HSTRING attribute_name);

    HRESULT (STDMETHODCALLTYPE *GetAttributeNode)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        HSTRING attribute_name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **attribute_node);

    HRESULT (STDMETHODCALLTYPE *SetAttributeNode)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *new_attribute,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **previous_attribute);

    HRESULT (STDMETHODCALLTYPE *RemoveAttributeNode)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *attribute_node,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **removed_attribute);

    HRESULT (STDMETHODCALLTYPE *GetElementsByTagName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        HSTRING tag_name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **elements);

    HRESULT (STDMETHODCALLTYPE *SetAttributeNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        IInspectable *namespace_uri,
        HSTRING qualified_name,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *GetAttributeNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        IInspectable *namespace_uri,
        HSTRING local_name,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAttributeNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        IInspectable *namespace_uri,
        HSTRING local_name);

    HRESULT (STDMETHODCALLTYPE *SetAttributeNodeNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *new_attribute,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **previous_attribute);

    HRESULT (STDMETHODCALLTYPE *GetAttributeNodeNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement *This,
        IInspectable *namespace_uri,
        HSTRING local_name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **previous_attribute);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlElementVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlElementVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlElement methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_get_TagName(This,value) (This)->lpVtbl->get_TagName(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttribute(This,attribute_name,attribute_value) (This)->lpVtbl->GetAttribute(This,attribute_name,attribute_value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttribute(This,attribute_name,attribute_value) (This)->lpVtbl->SetAttribute(This,attribute_name,attribute_value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttribute(This,attribute_name) (This)->lpVtbl->RemoveAttribute(This,attribute_name)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNode(This,attribute_name,attribute_node) (This)->lpVtbl->GetAttributeNode(This,attribute_name,attribute_node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNode(This,new_attribute,previous_attribute) (This)->lpVtbl->SetAttributeNode(This,new_attribute,previous_attribute)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttributeNode(This,attribute_node,removed_attribute) (This)->lpVtbl->RemoveAttributeNode(This,attribute_node,removed_attribute)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetElementsByTagName(This,tag_name,elements) (This)->lpVtbl->GetElementsByTagName(This,tag_name,elements)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNS(This,namespace_uri,qualified_name,value) (This)->lpVtbl->SetAttributeNS(This,namespace_uri,qualified_name,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNS(This,namespace_uri,local_name,value) (This)->lpVtbl->GetAttributeNS(This,namespace_uri,local_name,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttributeNS(This,namespace_uri,local_name) (This)->lpVtbl->RemoveAttributeNS(This,namespace_uri,local_name)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNodeNS(This,new_attribute,previous_attribute) (This)->lpVtbl->SetAttributeNodeNS(This,new_attribute,previous_attribute)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNodeNS(This,namespace_uri,local_name,previous_attribute) (This)->lpVtbl->GetAttributeNodeNS(This,namespace_uri,local_name,previous_attribute)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlElement methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_get_TagName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,HSTRING *value) {
    return This->lpVtbl->get_TagName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttribute(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,HSTRING attribute_name,HSTRING *attribute_value) {
    return This->lpVtbl->GetAttribute(This,attribute_name,attribute_value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttribute(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,HSTRING attribute_name,HSTRING attribute_value) {
    return This->lpVtbl->SetAttribute(This,attribute_name,attribute_value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttribute(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,HSTRING attribute_name) {
    return This->lpVtbl->RemoveAttribute(This,attribute_name);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNode(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,HSTRING attribute_name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **attribute_node) {
    return This->lpVtbl->GetAttributeNode(This,attribute_name,attribute_node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNode(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *new_attribute,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **previous_attribute) {
    return This->lpVtbl->SetAttributeNode(This,new_attribute,previous_attribute);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttributeNode(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *attribute_node,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **removed_attribute) {
    return This->lpVtbl->RemoveAttributeNode(This,attribute_node,removed_attribute);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetElementsByTagName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,HSTRING tag_name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **elements) {
    return This->lpVtbl->GetElementsByTagName(This,tag_name,elements);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,IInspectable *namespace_uri,HSTRING qualified_name,HSTRING value) {
    return This->lpVtbl->SetAttributeNS(This,namespace_uri,qualified_name,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,IInspectable *namespace_uri,HSTRING local_name,HSTRING *value) {
    return This->lpVtbl->GetAttributeNS(This,namespace_uri,local_name,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttributeNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,IInspectable *namespace_uri,HSTRING local_name) {
    return This->lpVtbl->RemoveAttributeNS(This,namespace_uri,local_name);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNodeNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute *new_attribute,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **previous_attribute) {
    return This->lpVtbl->SetAttributeNodeNS(This,new_attribute,previous_attribute);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNodeNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlElement* This,IInspectable *namespace_uri,HSTRING local_name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlAttribute **previous_attribute) {
    return This->lpVtbl->GetAttributeNodeNS(This,namespace_uri,local_name,previous_attribute);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlElement IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlElement
#define IXmlElementVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlElementVtbl
#define IXmlElement __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement
#define IXmlElement_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_QueryInterface
#define IXmlElement_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_AddRef
#define IXmlElement_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_Release
#define IXmlElement_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetIids
#define IXmlElement_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetRuntimeClassName
#define IXmlElement_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetTrustLevel
#define IXmlElement_get_TagName __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_get_TagName
#define IXmlElement_GetAttribute __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttribute
#define IXmlElement_SetAttribute __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttribute
#define IXmlElement_RemoveAttribute __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttribute
#define IXmlElement_GetAttributeNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNode
#define IXmlElement_SetAttributeNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNode
#define IXmlElement_RemoveAttributeNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttributeNode
#define IXmlElement_GetElementsByTagName __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetElementsByTagName
#define IXmlElement_SetAttributeNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNS
#define IXmlElement_GetAttributeNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNS
#define IXmlElement_RemoveAttributeNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_RemoveAttributeNS
#define IXmlElement_SetAttributeNodeNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_SetAttributeNodeNS
#define IXmlElement_GetAttributeNodeNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_GetAttributeNodeNS
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlElement_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlEntityReference interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference, 0x2e2f47bc, 0xc3d0, 0x4ccf, 0xbb,0x86, 0x0a,0xb8,0xc3,0x6a,0x61,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("2e2f47bc-c3d0-4ccf-bb86-0ab8c36a61cf")
                    IXmlEntityReference : public IInspectable
                    {
                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference, 0x2e2f47bc, 0xc3d0, 0x4ccf, 0xbb,0x86, 0x0a,0xb8,0xc3,0x6a,0x61,0xcf)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReferenceVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlEntityReference IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference
#define IXmlEntityReferenceVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReferenceVtbl
#define IXmlEntityReference __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference
#define IXmlEntityReference_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_QueryInterface
#define IXmlEntityReference_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_AddRef
#define IXmlEntityReference_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_Release
#define IXmlEntityReference_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetIids
#define IXmlEntityReference_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetRuntimeClassName
#define IXmlEntityReference_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_GetTrustLevel
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlEntityReference_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlLoadSettings interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings, 0x58aa07a8, 0xfed6, 0x46f7, 0xb4,0xc5, 0xfb,0x1b,0xa7,0x21,0x08,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("58aa07a8-fed6-46f7-b4c5-fb1ba72108d6")
                    IXmlLoadSettings : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_MaxElementDepth(
                            UINT32 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_MaxElementDepth(
                            UINT32 value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ProhibitDtd(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_ProhibitDtd(
                            boolean value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ResolveExternals(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_ResolveExternals(
                            boolean value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ValidateOnParse(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_ValidateOnParse(
                            boolean value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ElementContentWhiteSpace(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_ElementContentWhiteSpace(
                            boolean value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings, 0x58aa07a8, 0xfed6, 0x46f7, 0xb4,0xc5, 0xfb,0x1b,0xa7,0x21,0x08,0xd6)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        TrustLevel *trustLevel);

    /*** IXmlLoadSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MaxElementDepth)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_MaxElementDepth)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        UINT32 value);

    HRESULT (STDMETHODCALLTYPE *get_ProhibitDtd)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_ProhibitDtd)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_ResolveExternals)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_ResolveExternals)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_ValidateOnParse)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_ValidateOnParse)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_ElementContentWhiteSpace)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_ElementContentWhiteSpace)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettingsVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlLoadSettings methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_MaxElementDepth(This,value) (This)->lpVtbl->get_MaxElementDepth(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_MaxElementDepth(This,value) (This)->lpVtbl->put_MaxElementDepth(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ProhibitDtd(This,value) (This)->lpVtbl->get_ProhibitDtd(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ProhibitDtd(This,value) (This)->lpVtbl->put_ProhibitDtd(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ResolveExternals(This,value) (This)->lpVtbl->get_ResolveExternals(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ResolveExternals(This,value) (This)->lpVtbl->put_ResolveExternals(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ValidateOnParse(This,value) (This)->lpVtbl->get_ValidateOnParse(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ValidateOnParse(This,value) (This)->lpVtbl->put_ValidateOnParse(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ElementContentWhiteSpace(This,value) (This)->lpVtbl->get_ElementContentWhiteSpace(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ElementContentWhiteSpace(This,value) (This)->lpVtbl->put_ElementContentWhiteSpace(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlLoadSettings methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_MaxElementDepth(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,UINT32 *value) {
    return This->lpVtbl->get_MaxElementDepth(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_MaxElementDepth(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,UINT32 value) {
    return This->lpVtbl->put_MaxElementDepth(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ProhibitDtd(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,boolean *value) {
    return This->lpVtbl->get_ProhibitDtd(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ProhibitDtd(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,boolean value) {
    return This->lpVtbl->put_ProhibitDtd(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ResolveExternals(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,boolean *value) {
    return This->lpVtbl->get_ResolveExternals(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ResolveExternals(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,boolean value) {
    return This->lpVtbl->put_ResolveExternals(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ValidateOnParse(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,boolean *value) {
    return This->lpVtbl->get_ValidateOnParse(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ValidateOnParse(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,boolean value) {
    return This->lpVtbl->put_ValidateOnParse(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ElementContentWhiteSpace(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,boolean *value) {
    return This->lpVtbl->get_ElementContentWhiteSpace(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ElementContentWhiteSpace(__x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings* This,boolean value) {
    return This->lpVtbl->put_ElementContentWhiteSpace(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlLoadSettings IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings
#define IXmlLoadSettingsVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettingsVtbl
#define IXmlLoadSettings __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings
#define IXmlLoadSettings_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_QueryInterface
#define IXmlLoadSettings_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_AddRef
#define IXmlLoadSettings_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_Release
#define IXmlLoadSettings_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetIids
#define IXmlLoadSettings_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetRuntimeClassName
#define IXmlLoadSettings_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_GetTrustLevel
#define IXmlLoadSettings_get_MaxElementDepth __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_MaxElementDepth
#define IXmlLoadSettings_put_MaxElementDepth __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_MaxElementDepth
#define IXmlLoadSettings_get_ProhibitDtd __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ProhibitDtd
#define IXmlLoadSettings_put_ProhibitDtd __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ProhibitDtd
#define IXmlLoadSettings_get_ResolveExternals __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ResolveExternals
#define IXmlLoadSettings_put_ResolveExternals __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ResolveExternals
#define IXmlLoadSettings_get_ValidateOnParse __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ValidateOnParse
#define IXmlLoadSettings_put_ValidateOnParse __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ValidateOnParse
#define IXmlLoadSettings_get_ElementContentWhiteSpace __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_get_ElementContentWhiteSpace
#define IXmlLoadSettings_put_ElementContentWhiteSpace __x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_put_ElementContentWhiteSpace
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlLoadSettings_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlNamedNodeMap interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap, 0xb3a69eb0, 0xaab0, 0x4b82, 0xa6,0xfa, 0xb1,0x45,0x3f,0x7c,0x02,0x1b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("b3a69eb0-aab0-4b82-a6fa-b1453f7c021b")
                    IXmlNamedNodeMap : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Length(
                            UINT32 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE Item(
                            UINT32 index,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetNamedItem(
                            HSTRING name,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetNamedItem(
                            ABI::Windows::Data::Xml::Dom::IXmlNode *node,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **previous_node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RemoveNamedItem(
                            HSTRING name,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **previous_node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetNamedItemNS(
                            IInspectable *namespace_uri,
                            HSTRING name,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RemoveNamedItemNS(
                            IInspectable *namespace_uri,
                            HSTRING name,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **previous_node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetNamedItemNS(
                            ABI::Windows::Data::Xml::Dom::IXmlNode *node,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **previous_node) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap, 0xb3a69eb0, 0xaab0, 0x4b82, 0xa6,0xfa, 0xb1,0x45,0x3f,0x7c,0x02,0x1b)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        TrustLevel *trustLevel);

    /*** IXmlNamedNodeMap methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Length)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *Item)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node);

    HRESULT (STDMETHODCALLTYPE *GetNamedItem)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        HSTRING name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node);

    HRESULT (STDMETHODCALLTYPE *SetNamedItem)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *node,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_node);

    HRESULT (STDMETHODCALLTYPE *RemoveNamedItem)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        HSTRING name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_node);

    HRESULT (STDMETHODCALLTYPE *GetNamedItemNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        IInspectable *namespace_uri,
        HSTRING name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node);

    HRESULT (STDMETHODCALLTYPE *RemoveNamedItemNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        IInspectable *namespace_uri,
        HSTRING name,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_node);

    HRESULT (STDMETHODCALLTYPE *SetNamedItemNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *node,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_node);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMapVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlNamedNodeMap methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_get_Length(This,value) (This)->lpVtbl->get_Length(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_Item(This,index,node) (This)->lpVtbl->Item(This,index,node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetNamedItem(This,name,node) (This)->lpVtbl->GetNamedItem(This,name,node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_SetNamedItem(This,node,previous_node) (This)->lpVtbl->SetNamedItem(This,node,previous_node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_RemoveNamedItem(This,name,previous_node) (This)->lpVtbl->RemoveNamedItem(This,name,previous_node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetNamedItemNS(This,namespace_uri,name,node) (This)->lpVtbl->GetNamedItemNS(This,namespace_uri,name,node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_RemoveNamedItemNS(This,namespace_uri,name,previous_node) (This)->lpVtbl->RemoveNamedItemNS(This,namespace_uri,name,previous_node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_SetNamedItemNS(This,node,previous_node) (This)->lpVtbl->SetNamedItemNS(This,node,previous_node)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlNamedNodeMap methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_get_Length(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,UINT32 *value) {
    return This->lpVtbl->get_Length(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_Item(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,UINT32 index,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node) {
    return This->lpVtbl->Item(This,index,node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetNamedItem(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,HSTRING name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node) {
    return This->lpVtbl->GetNamedItem(This,name,node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_SetNamedItem(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *node,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_node) {
    return This->lpVtbl->SetNamedItem(This,node,previous_node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_RemoveNamedItem(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,HSTRING name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_node) {
    return This->lpVtbl->RemoveNamedItem(This,name,previous_node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetNamedItemNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,IInspectable *namespace_uri,HSTRING name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node) {
    return This->lpVtbl->GetNamedItemNS(This,namespace_uri,name,node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_RemoveNamedItemNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,IInspectable *namespace_uri,HSTRING name,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_node) {
    return This->lpVtbl->RemoveNamedItemNS(This,namespace_uri,name,previous_node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_SetNamedItemNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *node,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_node) {
    return This->lpVtbl->SetNamedItemNS(This,node,previous_node);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlNamedNodeMap IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap
#define IXmlNamedNodeMapVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMapVtbl
#define IXmlNamedNodeMap __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap
#define IXmlNamedNodeMap_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_QueryInterface
#define IXmlNamedNodeMap_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_AddRef
#define IXmlNamedNodeMap_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_Release
#define IXmlNamedNodeMap_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetIids
#define IXmlNamedNodeMap_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetRuntimeClassName
#define IXmlNamedNodeMap_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetTrustLevel
#define IXmlNamedNodeMap_get_Length __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_get_Length
#define IXmlNamedNodeMap_Item __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_Item
#define IXmlNamedNodeMap_GetNamedItem __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetNamedItem
#define IXmlNamedNodeMap_SetNamedItem __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_SetNamedItem
#define IXmlNamedNodeMap_RemoveNamedItem __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_RemoveNamedItem
#define IXmlNamedNodeMap_GetNamedItemNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_GetNamedItemNS
#define IXmlNamedNodeMap_RemoveNamedItemNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_RemoveNamedItemNS
#define IXmlNamedNodeMap_SetNamedItemNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_SetNamedItemNS
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlNode interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNode, 0x1c741d59, 0x2122, 0x47d5, 0xa8,0x56, 0x83,0xf3,0xd4,0x21,0x48,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("1c741d59-2122-47d5-a856-83f3d4214875")
                    IXmlNode : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_NodeValue(
                            IInspectable **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_NodeValue(
                            IInspectable *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_NodeType(
                            ABI::Windows::Data::Xml::Dom::NodeType *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_NodeName(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ParentNode(
                            ABI::Windows::Data::Xml::Dom::IXmlNode **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ChildNodes(
                            ABI::Windows::Data::Xml::Dom::IXmlNodeList **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_FirstChild(
                            ABI::Windows::Data::Xml::Dom::IXmlNode **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_LastChild(
                            ABI::Windows::Data::Xml::Dom::IXmlNode **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_PreviousSibling(
                            ABI::Windows::Data::Xml::Dom::IXmlNode **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_NextSibling(
                            ABI::Windows::Data::Xml::Dom::IXmlNode **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Attributes(
                            ABI::Windows::Data::Xml::Dom::IXmlNamedNodeMap **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE HasChildNodes(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_OwnerDocument(
                            ABI::Windows::Data::Xml::Dom::IXmlDocument **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE InsertBefore(
                            ABI::Windows::Data::Xml::Dom::IXmlNode *new_child,
                            ABI::Windows::Data::Xml::Dom::IXmlNode *reference_child,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **inserted_child) = 0;

                        virtual HRESULT STDMETHODCALLTYPE ReplaceChild(
                            ABI::Windows::Data::Xml::Dom::IXmlNode *new_child,
                            ABI::Windows::Data::Xml::Dom::IXmlNode *reference_child,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **previous_child) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RemoveChild(
                            ABI::Windows::Data::Xml::Dom::IXmlNode *child_node,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **removed_child) = 0;

                        virtual HRESULT STDMETHODCALLTYPE AppendChild(
                            ABI::Windows::Data::Xml::Dom::IXmlNode *new_child,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **appended_child) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CloneNode(
                            boolean deep,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **new_node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_NamespaceUri(
                            IInspectable **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_LocalName(
                            IInspectable **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Prefix(
                            IInspectable **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE Normalize(
                            ) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_Prefix(
                            IInspectable *value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode, 0x1c741d59, 0x2122, 0x47d5, 0xa8,0x56, 0x83,0xf3,0xd4,0x21,0x48,0x75)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        TrustLevel *trustLevel);

    /*** IXmlNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_NodeValue)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *put_NodeValue)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        IInspectable *value);

    HRESULT (STDMETHODCALLTYPE *get_NodeType)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CNodeType *value);

    HRESULT (STDMETHODCALLTYPE *get_NodeName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_ParentNode)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value);

    HRESULT (STDMETHODCALLTYPE *get_ChildNodes)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **value);

    HRESULT (STDMETHODCALLTYPE *get_FirstChild)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value);

    HRESULT (STDMETHODCALLTYPE *get_LastChild)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value);

    HRESULT (STDMETHODCALLTYPE *get_PreviousSibling)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value);

    HRESULT (STDMETHODCALLTYPE *get_NextSibling)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value);

    HRESULT (STDMETHODCALLTYPE *get_Attributes)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap **value);

    HRESULT (STDMETHODCALLTYPE *HasChildNodes)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_OwnerDocument)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument **value);

    HRESULT (STDMETHODCALLTYPE *InsertBefore)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *new_child,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *reference_child,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **inserted_child);

    HRESULT (STDMETHODCALLTYPE *ReplaceChild)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *new_child,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *reference_child,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_child);

    HRESULT (STDMETHODCALLTYPE *RemoveChild)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *child_node,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **removed_child);

    HRESULT (STDMETHODCALLTYPE *AppendChild)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *new_child,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **appended_child);

    HRESULT (STDMETHODCALLTYPE *CloneNode)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        boolean deep,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **new_node);

    HRESULT (STDMETHODCALLTYPE *get_NamespaceUri)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_LocalName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_Prefix)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *Normalize)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This);

    HRESULT (STDMETHODCALLTYPE *put_Prefix)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *This,
        IInspectable *value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlNode methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeValue(This,value) (This)->lpVtbl->get_NodeValue(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_put_NodeValue(This,value) (This)->lpVtbl->put_NodeValue(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeType(This,value) (This)->lpVtbl->get_NodeType(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeName(This,value) (This)->lpVtbl->get_NodeName(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_ParentNode(This,value) (This)->lpVtbl->get_ParentNode(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_ChildNodes(This,value) (This)->lpVtbl->get_ChildNodes(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_FirstChild(This,value) (This)->lpVtbl->get_FirstChild(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_LastChild(This,value) (This)->lpVtbl->get_LastChild(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_PreviousSibling(This,value) (This)->lpVtbl->get_PreviousSibling(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NextSibling(This,value) (This)->lpVtbl->get_NextSibling(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_Attributes(This,value) (This)->lpVtbl->get_Attributes(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_HasChildNodes(This,value) (This)->lpVtbl->HasChildNodes(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_OwnerDocument(This,value) (This)->lpVtbl->get_OwnerDocument(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_InsertBefore(This,new_child,reference_child,inserted_child) (This)->lpVtbl->InsertBefore(This,new_child,reference_child,inserted_child)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_ReplaceChild(This,new_child,reference_child,previous_child) (This)->lpVtbl->ReplaceChild(This,new_child,reference_child,previous_child)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_RemoveChild(This,child_node,removed_child) (This)->lpVtbl->RemoveChild(This,child_node,removed_child)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_AppendChild(This,new_child,appended_child) (This)->lpVtbl->AppendChild(This,new_child,appended_child)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_CloneNode(This,deep,new_node) (This)->lpVtbl->CloneNode(This,deep,new_node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NamespaceUri(This,value) (This)->lpVtbl->get_NamespaceUri(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_LocalName(This,value) (This)->lpVtbl->get_LocalName(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_Prefix(This,value) (This)->lpVtbl->get_Prefix(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_Normalize(This) (This)->lpVtbl->Normalize(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_put_Prefix(This,value) (This)->lpVtbl->put_Prefix(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlNode methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeValue(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,IInspectable **value) {
    return This->lpVtbl->get_NodeValue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_put_NodeValue(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,IInspectable *value) {
    return This->lpVtbl->put_NodeValue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeType(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CNodeType *value) {
    return This->lpVtbl->get_NodeType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,HSTRING *value) {
    return This->lpVtbl->get_NodeName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_ParentNode(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value) {
    return This->lpVtbl->get_ParentNode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_ChildNodes(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **value) {
    return This->lpVtbl->get_ChildNodes(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_FirstChild(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value) {
    return This->lpVtbl->get_FirstChild(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_LastChild(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value) {
    return This->lpVtbl->get_LastChild(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_PreviousSibling(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value) {
    return This->lpVtbl->get_PreviousSibling(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NextSibling(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value) {
    return This->lpVtbl->get_NextSibling(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_Attributes(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNamedNodeMap **value) {
    return This->lpVtbl->get_Attributes(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_HasChildNodes(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,boolean *value) {
    return This->lpVtbl->HasChildNodes(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_OwnerDocument(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument **value) {
    return This->lpVtbl->get_OwnerDocument(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_InsertBefore(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *new_child,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *reference_child,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **inserted_child) {
    return This->lpVtbl->InsertBefore(This,new_child,reference_child,inserted_child);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_ReplaceChild(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *new_child,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *reference_child,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **previous_child) {
    return This->lpVtbl->ReplaceChild(This,new_child,reference_child,previous_child);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_RemoveChild(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *child_node,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **removed_child) {
    return This->lpVtbl->RemoveChild(This,child_node,removed_child);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_AppendChild(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *new_child,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **appended_child) {
    return This->lpVtbl->AppendChild(This,new_child,appended_child);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_CloneNode(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,boolean deep,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **new_node) {
    return This->lpVtbl->CloneNode(This,deep,new_node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NamespaceUri(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,IInspectable **value) {
    return This->lpVtbl->get_NamespaceUri(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_LocalName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,IInspectable **value) {
    return This->lpVtbl->get_LocalName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_Prefix(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,IInspectable **value) {
    return This->lpVtbl->get_Prefix(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_Normalize(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This) {
    return This->lpVtbl->Normalize(This);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_put_Prefix(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode* This,IInspectable *value) {
    return This->lpVtbl->put_Prefix(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlNode IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNode
#define IXmlNodeVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeVtbl
#define IXmlNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode
#define IXmlNode_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_QueryInterface
#define IXmlNode_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_AddRef
#define IXmlNode_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_Release
#define IXmlNode_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetIids
#define IXmlNode_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetRuntimeClassName
#define IXmlNode_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_GetTrustLevel
#define IXmlNode_get_NodeValue __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeValue
#define IXmlNode_put_NodeValue __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_put_NodeValue
#define IXmlNode_get_NodeType __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeType
#define IXmlNode_get_NodeName __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NodeName
#define IXmlNode_get_ParentNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_ParentNode
#define IXmlNode_get_ChildNodes __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_ChildNodes
#define IXmlNode_get_FirstChild __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_FirstChild
#define IXmlNode_get_LastChild __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_LastChild
#define IXmlNode_get_PreviousSibling __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_PreviousSibling
#define IXmlNode_get_NextSibling __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NextSibling
#define IXmlNode_get_Attributes __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_Attributes
#define IXmlNode_HasChildNodes __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_HasChildNodes
#define IXmlNode_get_OwnerDocument __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_OwnerDocument
#define IXmlNode_InsertBefore __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_InsertBefore
#define IXmlNode_ReplaceChild __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_ReplaceChild
#define IXmlNode_RemoveChild __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_RemoveChild
#define IXmlNode_AppendChild __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_AppendChild
#define IXmlNode_CloneNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_CloneNode
#define IXmlNode_get_NamespaceUri __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_NamespaceUri
#define IXmlNode_get_LocalName __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_LocalName
#define IXmlNode_get_Prefix __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_get_Prefix
#define IXmlNode_Normalize __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_Normalize
#define IXmlNode_put_Prefix __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_put_Prefix
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNode_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlNodeList interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList, 0x8c60ad77, 0x83a4, 0x4ec1, 0x9c,0x54, 0x7b,0xa4,0x29,0xe1,0x3d,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("8c60ad77-83a4-4ec1-9c54-7ba429e13da6")
                    IXmlNodeList : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Length(
                            UINT32 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE Item(
                            UINT32 index,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **node) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList, 0x8c60ad77, 0x83a4, 0x4ec1, 0x9c,0x54, 0x7b,0xa4,0x29,0xe1,0x3d,0xa6)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList *This,
        TrustLevel *trustLevel);

    /*** IXmlNodeList methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Length)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *Item)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeListVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlNodeList methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_get_Length(This,value) (This)->lpVtbl->get_Length(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_Item(This,index,node) (This)->lpVtbl->Item(This,index,node)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlNodeList methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_get_Length(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList* This,UINT32 *value) {
    return This->lpVtbl->get_Length(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_Item(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList* This,UINT32 index,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node) {
    return This->lpVtbl->Item(This,index,node);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlNodeList IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList
#define IXmlNodeListVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeListVtbl
#define IXmlNodeList __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList
#define IXmlNodeList_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_QueryInterface
#define IXmlNodeList_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_AddRef
#define IXmlNodeList_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_Release
#define IXmlNodeList_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetIids
#define IXmlNodeList_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetRuntimeClassName
#define IXmlNodeList_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_GetTrustLevel
#define IXmlNodeList_get_Length __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_get_Length
#define IXmlNodeList_Item __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_Item
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlNodeSelector interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector, 0x63dbba8b, 0xd0db, 0x4fe1, 0xb7,0x45, 0xf9,0x43,0x3a,0xfd,0xc2,0x5b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("63dbba8b-d0db-4fe1-b745-f9433afdc25b")
                    IXmlNodeSelector : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE SelectSingleNode(
                            HSTRING xpath,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SelectNodes(
                            HSTRING xpath,
                            ABI::Windows::Data::Xml::Dom::IXmlNodeList **node_list) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SelectSingleNodeNS(
                            HSTRING xpath,
                            IInspectable *namespaces,
                            ABI::Windows::Data::Xml::Dom::IXmlNode **node) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SelectNodesNS(
                            HSTRING xpath,
                            IInspectable *namespaces,
                            ABI::Windows::Data::Xml::Dom::IXmlNodeList **node_list) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector, 0x63dbba8b, 0xd0db, 0x4fe1, 0xb7,0x45, 0xf9,0x43,0x3a,0xfd,0xc2,0x5b)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This,
        TrustLevel *trustLevel);

    /*** IXmlNodeSelector methods ***/
    HRESULT (STDMETHODCALLTYPE *SelectSingleNode)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This,
        HSTRING xpath,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node);

    HRESULT (STDMETHODCALLTYPE *SelectNodes)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This,
        HSTRING xpath,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **node_list);

    HRESULT (STDMETHODCALLTYPE *SelectSingleNodeNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This,
        HSTRING xpath,
        IInspectable *namespaces,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node);

    HRESULT (STDMETHODCALLTYPE *SelectNodesNS)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector *This,
        HSTRING xpath,
        IInspectable *namespaces,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **node_list);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelectorVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlNodeSelector methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectSingleNode(This,xpath,node) (This)->lpVtbl->SelectSingleNode(This,xpath,node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectNodes(This,xpath,node_list) (This)->lpVtbl->SelectNodes(This,xpath,node_list)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectSingleNodeNS(This,xpath,namespaces,node) (This)->lpVtbl->SelectSingleNodeNS(This,xpath,namespaces,node)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectNodesNS(This,xpath,namespaces,node_list) (This)->lpVtbl->SelectNodesNS(This,xpath,namespaces,node_list)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlNodeSelector methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectSingleNode(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This,HSTRING xpath,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node) {
    return This->lpVtbl->SelectSingleNode(This,xpath,node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectNodes(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This,HSTRING xpath,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **node_list) {
    return This->lpVtbl->SelectNodes(This,xpath,node_list);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectSingleNodeNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This,HSTRING xpath,IInspectable *namespaces,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **node) {
    return This->lpVtbl->SelectSingleNodeNS(This,xpath,namespaces,node);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectNodesNS(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector* This,HSTRING xpath,IInspectable *namespaces,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeList **node_list) {
    return This->lpVtbl->SelectNodesNS(This,xpath,namespaces,node_list);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlNodeSelector IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector
#define IXmlNodeSelectorVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelectorVtbl
#define IXmlNodeSelector __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector
#define IXmlNodeSelector_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_QueryInterface
#define IXmlNodeSelector_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_AddRef
#define IXmlNodeSelector_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_Release
#define IXmlNodeSelector_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetIids
#define IXmlNodeSelector_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetRuntimeClassName
#define IXmlNodeSelector_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_GetTrustLevel
#define IXmlNodeSelector_SelectSingleNode __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectSingleNode
#define IXmlNodeSelector_SelectNodes __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectNodes
#define IXmlNodeSelector_SelectSingleNodeNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectSingleNodeNS
#define IXmlNodeSelector_SelectNodesNS __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_SelectNodesNS
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSelector_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlNodeSerializer interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer, 0x5cc5b382, 0xe6dd, 0x4991, 0xab,0xef, 0x06,0xd8,0xd2,0xe7,0xbd,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("5cc5b382-e6dd-4991-abef-06d8d2e7bd0c")
                    IXmlNodeSerializer : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE GetXml(
                            HSTRING *outer_xml) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_InnerText(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_InnerText(
                            HSTRING value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer, 0x5cc5b382, 0xe6dd, 0x4991, 0xab,0xef, 0x06,0xd8,0xd2,0xe7,0xbd,0x0c)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This,
        TrustLevel *trustLevel);

    /*** IXmlNodeSerializer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetXml)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This,
        HSTRING *outer_xml);

    HRESULT (STDMETHODCALLTYPE *get_InnerText)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_InnerText)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer *This,
        HSTRING value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializerVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlNodeSerializer methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetXml(This,outer_xml) (This)->lpVtbl->GetXml(This,outer_xml)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_get_InnerText(This,value) (This)->lpVtbl->get_InnerText(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_put_InnerText(This,value) (This)->lpVtbl->put_InnerText(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlNodeSerializer methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetXml(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This,HSTRING *outer_xml) {
    return This->lpVtbl->GetXml(This,outer_xml);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_get_InnerText(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This,HSTRING *value) {
    return This->lpVtbl->get_InnerText(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_put_InnerText(__x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer* This,HSTRING value) {
    return This->lpVtbl->put_InnerText(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlNodeSerializer IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer
#define IXmlNodeSerializerVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializerVtbl
#define IXmlNodeSerializer __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer
#define IXmlNodeSerializer_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_QueryInterface
#define IXmlNodeSerializer_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_AddRef
#define IXmlNodeSerializer_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_Release
#define IXmlNodeSerializer_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetIids
#define IXmlNodeSerializer_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetRuntimeClassName
#define IXmlNodeSerializer_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetTrustLevel
#define IXmlNodeSerializer_GetXml __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_GetXml
#define IXmlNodeSerializer_get_InnerText __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_get_InnerText
#define IXmlNodeSerializer_put_InnerText __x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_put_InnerText
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlNodeSerializer_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlProcessingInstruction interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction, 0x2707fd1e, 0x1e92, 0x4ece, 0xb6,0xf4, 0x26,0xf0,0x69,0x07,0x8d,0xdc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("2707fd1e-1e92-4ece-b6f4-26f069078ddc")
                    IXmlProcessingInstruction : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Target(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Data(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_Data(
                            HSTRING value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction, 0x2707fd1e, 0x1e92, 0x4ece, 0xb6,0xf4, 0x26,0xf0,0x69,0x07,0x8d,0xdc)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstructionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This,
        TrustLevel *trustLevel);

    /*** IXmlProcessingInstruction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Target)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Data)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_Data)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction *This,
        HSTRING value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstructionVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstructionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlProcessingInstruction methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_get_Target(This,value) (This)->lpVtbl->get_Target(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_get_Data(This,value) (This)->lpVtbl->get_Data(This,value)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_put_Data(This,value) (This)->lpVtbl->put_Data(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlProcessingInstruction methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_get_Target(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This,HSTRING *value) {
    return This->lpVtbl->get_Target(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_get_Data(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This,HSTRING *value) {
    return This->lpVtbl->get_Data(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_put_Data(__x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction* This,HSTRING value) {
    return This->lpVtbl->put_Data(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlProcessingInstruction IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction
#define IXmlProcessingInstructionVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstructionVtbl
#define IXmlProcessingInstruction __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction
#define IXmlProcessingInstruction_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_QueryInterface
#define IXmlProcessingInstruction_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_AddRef
#define IXmlProcessingInstruction_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_Release
#define IXmlProcessingInstruction_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetIids
#define IXmlProcessingInstruction_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetRuntimeClassName
#define IXmlProcessingInstruction_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_GetTrustLevel
#define IXmlProcessingInstruction_get_Target __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_get_Target
#define IXmlProcessingInstruction_get_Data __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_get_Data
#define IXmlProcessingInstruction_put_Data __x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_put_Data
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlProcessingInstruction_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IXmlText interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CXml_CDom_CIXmlText_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CXml_CDom_CIXmlText_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlText, 0xf931a4cb, 0x308d, 0x4760, 0xa1,0xd5, 0x43,0xb6,0x74,0x50,0xac,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Xml {
                namespace Dom {
                    MIDL_INTERFACE("f931a4cb-308d-4760-a1d5-43b67450ac7e")
                    IXmlText : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE SplitText(
                            UINT32 offset,
                            ABI::Windows::Data::Xml::Dom::IXmlText **second_part) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CXml_CDom_CIXmlText, 0xf931a4cb, 0x308d, 0x4760, 0xa1,0xd5, 0x43,0xb6,0x74,0x50,0xac,0x7e)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CXml_CDom_CIXmlTextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText *This,
        TrustLevel *trustLevel);

    /*** IXmlText methods ***/
    HRESULT (STDMETHODCALLTYPE *SplitText)(
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText *This,
        UINT32 offset,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlText **second_part);

    END_INTERFACE
} __x_ABI_CWindows_CData_CXml_CDom_CIXmlTextVtbl;

interface __x_ABI_CWindows_CData_CXml_CDom_CIXmlText {
    CONST_VTBL __x_ABI_CWindows_CData_CXml_CDom_CIXmlTextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXmlText methods ***/
#define __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_SplitText(This,offset,second_part) (This)->lpVtbl->SplitText(This,offset,second_part)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_QueryInterface(__x_ABI_CWindows_CData_CXml_CDom_CIXmlText* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_AddRef(__x_ABI_CWindows_CData_CXml_CDom_CIXmlText* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_Release(__x_ABI_CWindows_CData_CXml_CDom_CIXmlText* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetIids(__x_ABI_CWindows_CData_CXml_CDom_CIXmlText* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetRuntimeClassName(__x_ABI_CWindows_CData_CXml_CDom_CIXmlText* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetTrustLevel(__x_ABI_CWindows_CData_CXml_CDom_CIXmlText* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXmlText methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_SplitText(__x_ABI_CWindows_CData_CXml_CDom_CIXmlText* This,UINT32 offset,__x_ABI_CWindows_CData_CXml_CDom_CIXmlText **second_part) {
    return This->lpVtbl->SplitText(This,offset,second_part);
}
#endif
#ifdef WIDL_using_Windows_Data_Xml_Dom
#define IID_IXmlText IID___x_ABI_CWindows_CData_CXml_CDom_CIXmlText
#define IXmlTextVtbl __x_ABI_CWindows_CData_CXml_CDom_CIXmlTextVtbl
#define IXmlText __x_ABI_CWindows_CData_CXml_CDom_CIXmlText
#define IXmlText_QueryInterface __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_QueryInterface
#define IXmlText_AddRef __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_AddRef
#define IXmlText_Release __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_Release
#define IXmlText_GetIids __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetIids
#define IXmlText_GetRuntimeClassName __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetRuntimeClassName
#define IXmlText_GetTrustLevel __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_GetTrustLevel
#define IXmlText_SplitText __x_ABI_CWindows_CData_CXml_CDom_CIXmlText_SplitText
#endif /* WIDL_using_Windows_Data_Xml_Dom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CXml_CDom_CIXmlText_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.DtdEntity
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_DtdEntity_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_DtdEntity_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_DtdEntity[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','D','t','d','E','n','t','i','t','y',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_DtdEntity[] = L"Windows.Data.Xml.Dom.DtdEntity";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_DtdEntity[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','D','t','d','E','n','t','i','t','y',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_DtdEntity_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.DtdNotation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_DtdNotation_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_DtdNotation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_DtdNotation[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','D','t','d','N','o','t','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_DtdNotation[] = L"Windows.Data.Xml.Dom.DtdNotation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_DtdNotation[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','D','t','d','N','o','t','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_DtdNotation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlAttribute
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlAttribute_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlAttribute_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlAttribute[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','A','t','t','r','i','b','u','t','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlAttribute[] = L"Windows.Data.Xml.Dom.XmlAttribute";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlAttribute[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','A','t','t','r','i','b','u','t','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlAttribute_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlCDataSection
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlCDataSection_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlCDataSection_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlCDataSection[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','C','D','a','t','a','S','e','c','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlCDataSection[] = L"Windows.Data.Xml.Dom.XmlCDataSection";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlCDataSection[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','C','D','a','t','a','S','e','c','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlCDataSection_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlComment
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlComment_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlComment_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlComment[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','C','o','m','m','e','n','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlComment[] = L"Windows.Data.Xml.Dom.XmlComment";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlComment[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','C','o','m','m','e','n','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlComment_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlDocument
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocument_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocument_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocument[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','D','o','c','u','m','e','n','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocument[] = L"Windows.Data.Xml.Dom.XmlDocument";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocument[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','D','o','c','u','m','e','n','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocument_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlDocumentFragment
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocumentFragment_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocumentFragment_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocumentFragment[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','D','o','c','u','m','e','n','t','F','r','a','g','m','e','n','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocumentFragment[] = L"Windows.Data.Xml.Dom.XmlDocumentFragment";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocumentFragment[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','D','o','c','u','m','e','n','t','F','r','a','g','m','e','n','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocumentFragment_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlDocumentType
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocumentType_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocumentType_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocumentType[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','D','o','c','u','m','e','n','t','T','y','p','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocumentType[] = L"Windows.Data.Xml.Dom.XmlDocumentType";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDocumentType[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','D','o','c','u','m','e','n','t','T','y','p','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDocumentType_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlDomImplementation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDomImplementation_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDomImplementation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDomImplementation[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','D','o','m','I','m','p','l','e','m','e','n','t','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDomImplementation[] = L"Windows.Data.Xml.Dom.XmlDomImplementation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlDomImplementation[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','D','o','m','I','m','p','l','e','m','e','n','t','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlDomImplementation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlElement
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlElement_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlElement_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlElement[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','E','l','e','m','e','n','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlElement[] = L"Windows.Data.Xml.Dom.XmlElement";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlElement[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','E','l','e','m','e','n','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlElement_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlEntityReference
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlEntityReference_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlEntityReference_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlEntityReference[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','E','n','t','i','t','y','R','e','f','e','r','e','n','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlEntityReference[] = L"Windows.Data.Xml.Dom.XmlEntityReference";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlEntityReference[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','E','n','t','i','t','y','R','e','f','e','r','e','n','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlEntityReference_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlLoadSettings
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlLoadSettings_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlLoadSettings_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlLoadSettings[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','L','o','a','d','S','e','t','t','i','n','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlLoadSettings[] = L"Windows.Data.Xml.Dom.XmlLoadSettings";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlLoadSettings[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','L','o','a','d','S','e','t','t','i','n','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlLoadSettings_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlNamedNodeMap
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlNamedNodeMap_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlNamedNodeMap_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlNamedNodeMap[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','N','a','m','e','d','N','o','d','e','M','a','p',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlNamedNodeMap[] = L"Windows.Data.Xml.Dom.XmlNamedNodeMap";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlNamedNodeMap[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','N','a','m','e','d','N','o','d','e','M','a','p',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlNamedNodeMap_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlNodeList
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlNodeList_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlNodeList_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlNodeList[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','N','o','d','e','L','i','s','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlNodeList[] = L"Windows.Data.Xml.Dom.XmlNodeList";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlNodeList[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','N','o','d','e','L','i','s','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlNodeList_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlProcessingInstruction
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlProcessingInstruction_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlProcessingInstruction_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlProcessingInstruction[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','P','r','o','c','e','s','s','i','n','g','I','n','s','t','r','u','c','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlProcessingInstruction[] = L"Windows.Data.Xml.Dom.XmlProcessingInstruction";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlProcessingInstruction[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','P','r','o','c','e','s','s','i','n','g','I','n','s','t','r','u','c','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlProcessingInstruction_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Xml.Dom.XmlText
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Xml_Dom_XmlText_DEFINED
#define RUNTIMECLASS_Windows_Data_Xml_Dom_XmlText_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlText[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','T','e','x','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlText[] = L"Windows.Data.Xml.Dom.XmlText";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Xml_Dom_XmlText[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','X','m','l','.','D','o','m','.','X','m','l','T','e','x','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Xml_Dom_XmlText_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Data::Xml::Dom::IXmlNode* > interface
 */
#ifndef ____FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode, 0xf1146ffc, 0x8c92, 0x56e8, 0x93,0xf1, 0x71,0x1f,0x86,0x72,0x26,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f1146ffc-8c92-56e8-93f1-711f86722633")
                IIterable<ABI::Windows::Data::Xml::Dom::IXmlNode* > : IIterable_impl<ABI::Windows::Data::Xml::Dom::IXmlNode* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode, 0xf1146ffc, 0x8c92, 0x56e8, 0x93,0xf1, 0x71,0x1f,0x86,0x72,0x26,0x33)
#endif
#else
typedef struct __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode **value);

    END_INTERFACE
} __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl;

interface __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode {
    CONST_VTBL __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
#define __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface(__FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef(__FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_Release(__FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids(__FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName(__FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel(__FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_First(__FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode* This,__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IXmlNode IID___FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode
#define IIterable_IXmlNodeVtbl __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl
#define IIterable_IXmlNode __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode
#define IIterable_IXmlNode_QueryInterface __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface
#define IIterable_IXmlNode_AddRef __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef
#define IIterable_IXmlNode_Release __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_Release
#define IIterable_IXmlNode_GetIids __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids
#define IIterable_IXmlNode_GetRuntimeClassName __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName
#define IIterable_IXmlNode_GetTrustLevel __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel
#define IIterable_IXmlNode_First __FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Data::Xml::Dom::IXmlNode* > interface
 */
#ifndef ____FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode, 0x3833a35e, 0x2c61, 0x56bd, 0xb0,0x93, 0x36,0x94,0x16,0x5f,0x88,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("3833a35e-2c61-56bd-b093-3694165f8898")
                IIterator<ABI::Windows::Data::Xml::Dom::IXmlNode* > : IIterator_impl<ABI::Windows::Data::Xml::Dom::IXmlNode* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode, 0x3833a35e, 0x2c61, 0x56bd, 0xb0,0x93, 0x36,0x94,0x16,0x5f,0x88,0x98)
#endif
#else
typedef struct __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        UINT32 items_size,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl;

interface __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode {
    CONST_VTBL __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_Release(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_get_Current(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_get_HasCurrent(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_MoveNext(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetMany(__FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode* This,UINT32 items_size,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IXmlNode IID___FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode
#define IIterator_IXmlNodeVtbl __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl
#define IIterator_IXmlNode __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode
#define IIterator_IXmlNode_QueryInterface __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface
#define IIterator_IXmlNode_AddRef __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef
#define IIterator_IXmlNode_Release __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_Release
#define IIterator_IXmlNode_GetIids __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids
#define IIterator_IXmlNode_GetRuntimeClassName __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName
#define IIterator_IXmlNode_GetTrustLevel __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel
#define IIterator_IXmlNode_get_Current __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_get_Current
#define IIterator_IXmlNode_get_HasCurrent __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_get_HasCurrent
#define IIterator_IXmlNode_MoveNext __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_MoveNext
#define IIterator_IXmlNode_GetMany __FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Data::Xml::Dom::IXmlNode* > interface
 */
#ifndef ____FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode, 0x139d959e, 0xe7b5, 0x5cb6, 0xa5,0x96, 0x4b,0x54,0x44,0x78,0xda,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("139d959e-e7b5-5cb6-a596-4b544478da9b")
                IVectorView<ABI::Windows::Data::Xml::Dom::IXmlNode* > : IVectorView_impl<ABI::Windows::Data::Xml::Dom::IXmlNode* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode, 0x139d959e, 0xe7b5, 0x5cb6, 0xa5,0x96, 0x4b,0x54,0x44,0x78,0xda,0x9b)
#endif
#else
typedef struct __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl;

interface __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode {
    CONST_VTBL __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_Release(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Data::Xml::Dom::IXmlNode* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetAt(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This,UINT32 index,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_get_Size(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_IndexOf(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetMany(__FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CData_CXml_CDom_CIXmlNode **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_IXmlNode IID___FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode
#define IVectorView_IXmlNodeVtbl __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNodeVtbl
#define IVectorView_IXmlNode __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode
#define IVectorView_IXmlNode_QueryInterface __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_QueryInterface
#define IVectorView_IXmlNode_AddRef __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_AddRef
#define IVectorView_IXmlNode_Release __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_Release
#define IVectorView_IXmlNode_GetIids __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetIids
#define IVectorView_IXmlNode_GetRuntimeClassName __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetRuntimeClassName
#define IVectorView_IXmlNode_GetTrustLevel __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetTrustLevel
#define IVectorView_IXmlNode_GetAt __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetAt
#define IVectorView_IXmlNode_get_Size __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_get_Size
#define IVectorView_IXmlNode_IndexOf __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_IndexOf
#define IVectorView_IXmlNode_GetMany __FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CData__CXml__CDom__CIXmlNode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument, 0xf858e239, 0x1896, 0x5982, 0x84,0x95, 0x14,0x31,0x68,0x47,0x8e,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("f858e239-1896-5982-8495-143168478eb8")
            IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Data::Xml::Dom::XmlDocument*, ABI::Windows::Data::Xml::Dom::IXmlDocument* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument, 0xf858e239, 0x1896, 0x5982, 0x84,0x95, 0x14,0x31,0x68,0x47,0x8e,0xb8)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocumentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        __x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocumentVtbl;

interface __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument {
    CONST_VTBL __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocumentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > methods ***/
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_QueryInterface(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_AddRef(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_Release(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetIids(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetTrustLevel(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Data::Xml::Dom::XmlDocument* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_put_Completed(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This,__FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_get_Completed(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This,__FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetResults(__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument* This,__x_ABI_CWindows_CData_CXml_CDom_CIXmlDocument **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_XmlDocument IID___FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument
#define IAsyncOperation_XmlDocumentVtbl __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocumentVtbl
#define IAsyncOperation_XmlDocument __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument
#define IAsyncOperation_XmlDocument_QueryInterface __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_QueryInterface
#define IAsyncOperation_XmlDocument_AddRef __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_AddRef
#define IAsyncOperation_XmlDocument_Release __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_Release
#define IAsyncOperation_XmlDocument_GetIids __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetIids
#define IAsyncOperation_XmlDocument_GetRuntimeClassName __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetRuntimeClassName
#define IAsyncOperation_XmlDocument_GetTrustLevel __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetTrustLevel
#define IAsyncOperation_XmlDocument_put_Completed __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_put_Completed
#define IAsyncOperation_XmlDocument_get_Completed __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_get_Completed
#define IAsyncOperation_XmlDocument_GetResults __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Data::Xml::Dom::XmlDocument* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument, 0x5eef7817, 0x93dd, 0x5c0b, 0x9e,0x5a, 0xeb,0x49,0x04,0x08,0xf3,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("5eef7817-93dd-5c0b-9e5a-eb490408f3a9")
            IAsyncOperationCompletedHandler<ABI::Windows::Data::Xml::Dom::XmlDocument* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Data::Xml::Dom::XmlDocument*, ABI::Windows::Data::Xml::Dom::IXmlDocument* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument, 0x5eef7817, 0x93dd, 0x5c0b, 0x9e,0x5a, 0xeb,0x49,0x04,0x08,0xf3,0xa9)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocumentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Data::Xml::Dom::XmlDocument* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument *This,
        __FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocumentVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocumentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Data::Xml::Dom::XmlDocument* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_Release(__FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Data::Xml::Dom::XmlDocument* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument* This,__FIAsyncOperation_1_Windows__CData__CXml__CDom__CXmlDocument *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_XmlDocument IID___FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument
#define IAsyncOperationCompletedHandler_XmlDocumentVtbl __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocumentVtbl
#define IAsyncOperationCompletedHandler_XmlDocument __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument
#define IAsyncOperationCompletedHandler_XmlDocument_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_QueryInterface
#define IAsyncOperationCompletedHandler_XmlDocument_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_AddRef
#define IAsyncOperationCompletedHandler_XmlDocument_Release __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_Release
#define IAsyncOperationCompletedHandler_XmlDocument_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CData__CXml__CDom__CXmlDocument_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_data_xml_dom_h__ */
