.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_set_attribute_by_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_set_attribute_by_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_set_attribute_by_oid(gnutls_x509_crq_t " crq ", const char * " oid ", void * " buf ", size_t " buf_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "const char * oid" 12
holds an Object Identifier in a null\-terminated string
.IP "void * buf" 12
a pointer to a structure that holds the attribute data
.IP "size_t buf_size" 12
holds the size of  \fIbuf\fP 
.SH "DESCRIPTION"
This function will set the attribute in the certificate request
specified by the given Object ID. The provided attribute must be be DER
encoded.

Attributes in a certificate request is an optional set of data
appended to the request. Their interpretation depends on the CA policy.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
