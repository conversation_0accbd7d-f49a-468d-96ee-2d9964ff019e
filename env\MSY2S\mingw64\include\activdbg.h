/*** Autogenerated by WIDL 10.8 from include/activdbg.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __activdbg_h__
#define __activdbg_h__

/* Forward declarations */

#ifndef __IActiveScriptDebug32_FWD_DEFINED__
#define __IActiveScriptDebug32_FWD_DEFINED__
typedef interface IActiveScriptDebug32 IActiveScriptDebug32;
#ifdef __cplusplus
interface IActiveScriptDebug32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptDebug64_FWD_DEFINED__
#define __IActiveScriptDebug64_FWD_DEFINED__
typedef interface IActiveScriptDebug64 IActiveScriptDebug64;
#ifdef __cplusplus
interface IActiveScriptDebug64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebug32_FWD_DEFINED__
#define __IActiveScriptSiteDebug32_FWD_DEFINED__
typedef interface IActiveScriptSiteDebug32 IActiveScriptSiteDebug32;
#ifdef __cplusplus
interface IActiveScriptSiteDebug32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebug64_FWD_DEFINED__
#define __IActiveScriptSiteDebug64_FWD_DEFINED__
typedef interface IActiveScriptSiteDebug64 IActiveScriptSiteDebug64;
#ifdef __cplusplus
interface IActiveScriptSiteDebug64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebugEx_FWD_DEFINED__
#define __IActiveScriptSiteDebugEx_FWD_DEFINED__
typedef interface IActiveScriptSiteDebugEx IActiveScriptSiteDebugEx;
#ifdef __cplusplus
interface IActiveScriptSiteDebugEx;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptErrorDebug_FWD_DEFINED__
#define __IActiveScriptErrorDebug_FWD_DEFINED__
typedef interface IActiveScriptErrorDebug IActiveScriptErrorDebug;
#ifdef __cplusplus
interface IActiveScriptErrorDebug;
#endif /* __cplusplus */
#endif

#ifndef __IDebugCodeContext_FWD_DEFINED__
#define __IDebugCodeContext_FWD_DEFINED__
typedef interface IDebugCodeContext IDebugCodeContext;
#ifdef __cplusplus
interface IDebugCodeContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpression_FWD_DEFINED__
#define __IDebugExpression_FWD_DEFINED__
typedef interface IDebugExpression IDebugExpression;
#ifdef __cplusplus
interface IDebugExpression;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpressionContext_FWD_DEFINED__
#define __IDebugExpressionContext_FWD_DEFINED__
typedef interface IDebugExpressionContext IDebugExpressionContext;
#ifdef __cplusplus
interface IDebugExpressionContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpressionCallBack_FWD_DEFINED__
#define __IDebugExpressionCallBack_FWD_DEFINED__
typedef interface IDebugExpressionCallBack IDebugExpressionCallBack;
#ifdef __cplusplus
interface IDebugExpressionCallBack;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrame_FWD_DEFINED__
#define __IDebugStackFrame_FWD_DEFINED__
typedef interface IDebugStackFrame IDebugStackFrame;
#ifdef __cplusplus
interface IDebugStackFrame;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrameSniffer_FWD_DEFINED__
#define __IDebugStackFrameSniffer_FWD_DEFINED__
typedef interface IDebugStackFrameSniffer IDebugStackFrameSniffer;
#ifdef __cplusplus
interface IDebugStackFrameSniffer;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrameSnifferEx32_FWD_DEFINED__
#define __IDebugStackFrameSnifferEx32_FWD_DEFINED__
typedef interface IDebugStackFrameSnifferEx32 IDebugStackFrameSnifferEx32;
#ifdef __cplusplus
interface IDebugStackFrameSnifferEx32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrameSnifferEx64_FWD_DEFINED__
#define __IDebugStackFrameSnifferEx64_FWD_DEFINED__
typedef interface IDebugStackFrameSnifferEx64 IDebugStackFrameSnifferEx64;
#ifdef __cplusplus
interface IDebugStackFrameSnifferEx64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugSyncOperation_FWD_DEFINED__
#define __IDebugSyncOperation_FWD_DEFINED__
typedef interface IDebugSyncOperation IDebugSyncOperation;
#ifdef __cplusplus
interface IDebugSyncOperation;
#endif /* __cplusplus */
#endif

#ifndef __IDebugAsyncOperation_FWD_DEFINED__
#define __IDebugAsyncOperation_FWD_DEFINED__
typedef interface IDebugAsyncOperation IDebugAsyncOperation;
#ifdef __cplusplus
interface IDebugAsyncOperation;
#endif /* __cplusplus */
#endif

#ifndef __IDebugAsyncOperationCallBack_FWD_DEFINED__
#define __IDebugAsyncOperationCallBack_FWD_DEFINED__
typedef interface IDebugAsyncOperationCallBack IDebugAsyncOperationCallBack;
#ifdef __cplusplus
interface IDebugAsyncOperationCallBack;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugCodeContexts_FWD_DEFINED__
#define __IEnumDebugCodeContexts_FWD_DEFINED__
typedef interface IEnumDebugCodeContexts IEnumDebugCodeContexts;
#ifdef __cplusplus
interface IEnumDebugCodeContexts;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugStackFrames_FWD_DEFINED__
#define __IEnumDebugStackFrames_FWD_DEFINED__
typedef interface IEnumDebugStackFrames IEnumDebugStackFrames;
#ifdef __cplusplus
interface IEnumDebugStackFrames;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugStackFrames64_FWD_DEFINED__
#define __IEnumDebugStackFrames64_FWD_DEFINED__
typedef interface IEnumDebugStackFrames64 IEnumDebugStackFrames64;
#ifdef __cplusplus
interface IEnumDebugStackFrames64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentInfo_FWD_DEFINED__
#define __IDebugDocumentInfo_FWD_DEFINED__
typedef interface IDebugDocumentInfo IDebugDocumentInfo;
#ifdef __cplusplus
interface IDebugDocumentInfo;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentProvider_FWD_DEFINED__
#define __IDebugDocumentProvider_FWD_DEFINED__
typedef interface IDebugDocumentProvider IDebugDocumentProvider;
#ifdef __cplusplus
interface IDebugDocumentProvider;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocument_FWD_DEFINED__
#define __IDebugDocument_FWD_DEFINED__
typedef interface IDebugDocument IDebugDocument;
#ifdef __cplusplus
interface IDebugDocument;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentText_FWD_DEFINED__
#define __IDebugDocumentText_FWD_DEFINED__
typedef interface IDebugDocumentText IDebugDocumentText;
#ifdef __cplusplus
interface IDebugDocumentText;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentTextEvents_FWD_DEFINED__
#define __IDebugDocumentTextEvents_FWD_DEFINED__
typedef interface IDebugDocumentTextEvents IDebugDocumentTextEvents;
#ifdef __cplusplus
interface IDebugDocumentTextEvents;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentTextAuthor_FWD_DEFINED__
#define __IDebugDocumentTextAuthor_FWD_DEFINED__
typedef interface IDebugDocumentTextAuthor IDebugDocumentTextAuthor;
#ifdef __cplusplus
interface IDebugDocumentTextAuthor;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentTextExternalAuthor_FWD_DEFINED__
#define __IDebugDocumentTextExternalAuthor_FWD_DEFINED__
typedef interface IDebugDocumentTextExternalAuthor IDebugDocumentTextExternalAuthor;
#ifdef __cplusplus
interface IDebugDocumentTextExternalAuthor;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHelper32_FWD_DEFINED__
#define __IDebugDocumentHelper32_FWD_DEFINED__
typedef interface IDebugDocumentHelper32 IDebugDocumentHelper32;
#ifdef __cplusplus
interface IDebugDocumentHelper32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHelper64_FWD_DEFINED__
#define __IDebugDocumentHelper64_FWD_DEFINED__
typedef interface IDebugDocumentHelper64 IDebugDocumentHelper64;
#ifdef __cplusplus
interface IDebugDocumentHelper64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHost_FWD_DEFINED__
#define __IDebugDocumentHost_FWD_DEFINED__
typedef interface IDebugDocumentHost IDebugDocumentHost;
#ifdef __cplusplus
interface IDebugDocumentHost;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentContext_FWD_DEFINED__
#define __IDebugDocumentContext_FWD_DEFINED__
typedef interface IDebugDocumentContext IDebugDocumentContext;
#ifdef __cplusplus
interface IDebugDocumentContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugSessionProvider_FWD_DEFINED__
#define __IDebugSessionProvider_FWD_DEFINED__
typedef interface IDebugSessionProvider IDebugSessionProvider;
#ifdef __cplusplus
interface IDebugSessionProvider;
#endif /* __cplusplus */
#endif

#ifndef __IApplicationDebugger_FWD_DEFINED__
#define __IApplicationDebugger_FWD_DEFINED__
typedef interface IApplicationDebugger IApplicationDebugger;
#ifdef __cplusplus
interface IApplicationDebugger;
#endif /* __cplusplus */
#endif

#ifndef __IApplicationDebuggerUI_FWD_DEFINED__
#define __IApplicationDebuggerUI_FWD_DEFINED__
typedef interface IApplicationDebuggerUI IApplicationDebuggerUI;
#ifdef __cplusplus
interface IApplicationDebuggerUI;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManager_FWD_DEFINED__
#define __IMachineDebugManager_FWD_DEFINED__
typedef interface IMachineDebugManager IMachineDebugManager;
#ifdef __cplusplus
interface IMachineDebugManager;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManagerCookie_FWD_DEFINED__
#define __IMachineDebugManagerCookie_FWD_DEFINED__
typedef interface IMachineDebugManagerCookie IMachineDebugManagerCookie;
#ifdef __cplusplus
interface IMachineDebugManagerCookie;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManagerEvents_FWD_DEFINED__
#define __IMachineDebugManagerEvents_FWD_DEFINED__
typedef interface IMachineDebugManagerEvents IMachineDebugManagerEvents;
#ifdef __cplusplus
interface IMachineDebugManagerEvents;
#endif /* __cplusplus */
#endif

#ifndef __IProcessDebugManager32_FWD_DEFINED__
#define __IProcessDebugManager32_FWD_DEFINED__
typedef interface IProcessDebugManager32 IProcessDebugManager32;
#ifdef __cplusplus
interface IProcessDebugManager32;
#endif /* __cplusplus */
#endif

#ifndef __IProcessDebugManager64_FWD_DEFINED__
#define __IProcessDebugManager64_FWD_DEFINED__
typedef interface IProcessDebugManager64 IProcessDebugManager64;
#ifdef __cplusplus
interface IProcessDebugManager64;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplication_FWD_DEFINED__
#define __IRemoteDebugApplication_FWD_DEFINED__
typedef interface IRemoteDebugApplication IRemoteDebugApplication;
#ifdef __cplusplus
interface IRemoteDebugApplication;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplication32_FWD_DEFINED__
#define __IDebugApplication32_FWD_DEFINED__
typedef interface IDebugApplication32 IDebugApplication32;
#ifdef __cplusplus
interface IDebugApplication32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplication64_FWD_DEFINED__
#define __IDebugApplication64_FWD_DEFINED__
typedef interface IDebugApplication64 IDebugApplication64;
#ifdef __cplusplus
interface IDebugApplication64;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplicationEvents_FWD_DEFINED__
#define __IRemoteDebugApplicationEvents_FWD_DEFINED__
typedef interface IRemoteDebugApplicationEvents IRemoteDebugApplicationEvents;
#ifdef __cplusplus
interface IRemoteDebugApplicationEvents;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationNode_FWD_DEFINED__
#define __IDebugApplicationNode_FWD_DEFINED__
typedef interface IDebugApplicationNode IDebugApplicationNode;
#ifdef __cplusplus
interface IDebugApplicationNode;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationNodeEvents_FWD_DEFINED__
#define __IDebugApplicationNodeEvents_FWD_DEFINED__
typedef interface IDebugApplicationNodeEvents IDebugApplicationNodeEvents;
#ifdef __cplusplus
interface IDebugApplicationNodeEvents;
#endif /* __cplusplus */
#endif

#ifndef __AsyncIDebugApplicationNodeEvents_FWD_DEFINED__
#define __AsyncIDebugApplicationNodeEvents_FWD_DEFINED__
typedef interface AsyncIDebugApplicationNodeEvents AsyncIDebugApplicationNodeEvents;
#ifdef __cplusplus
interface AsyncIDebugApplicationNodeEvents;
#endif /* __cplusplus */
#endif

#ifndef __IDebugThreadCall32_FWD_DEFINED__
#define __IDebugThreadCall32_FWD_DEFINED__
typedef interface IDebugThreadCall32 IDebugThreadCall32;
#ifdef __cplusplus
interface IDebugThreadCall32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugThreadCall64_FWD_DEFINED__
#define __IDebugThreadCall64_FWD_DEFINED__
typedef interface IDebugThreadCall64 IDebugThreadCall64;
#ifdef __cplusplus
interface IDebugThreadCall64;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplicationThread_FWD_DEFINED__
#define __IRemoteDebugApplicationThread_FWD_DEFINED__
typedef interface IRemoteDebugApplicationThread IRemoteDebugApplicationThread;
#ifdef __cplusplus
interface IRemoteDebugApplicationThread;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationThread_FWD_DEFINED__
#define __IDebugApplicationThread_FWD_DEFINED__
typedef interface IDebugApplicationThread IDebugApplicationThread;
#ifdef __cplusplus
interface IDebugApplicationThread;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationThread64_FWD_DEFINED__
#define __IDebugApplicationThread64_FWD_DEFINED__
typedef interface IDebugApplicationThread64 IDebugApplicationThread64;
#ifdef __cplusplus
interface IDebugApplicationThread64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugCookie_FWD_DEFINED__
#define __IDebugCookie_FWD_DEFINED__
typedef interface IDebugCookie IDebugCookie;
#ifdef __cplusplus
interface IDebugCookie;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugApplicationNodes_FWD_DEFINED__
#define __IEnumDebugApplicationNodes_FWD_DEFINED__
typedef interface IEnumDebugApplicationNodes IEnumDebugApplicationNodes;
#ifdef __cplusplus
interface IEnumDebugApplicationNodes;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRemoteDebugApplications_FWD_DEFINED__
#define __IEnumRemoteDebugApplications_FWD_DEFINED__
typedef interface IEnumRemoteDebugApplications IEnumRemoteDebugApplications;
#ifdef __cplusplus
interface IEnumRemoteDebugApplications;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRemoteDebugApplicationThreads_FWD_DEFINED__
#define __IEnumRemoteDebugApplicationThreads_FWD_DEFINED__
typedef interface IEnumRemoteDebugApplicationThreads IEnumRemoteDebugApplicationThreads;
#ifdef __cplusplus
interface IEnumRemoteDebugApplicationThreads;
#endif /* __cplusplus */
#endif

#ifndef __IDebugFormatter_FWD_DEFINED__
#define __IDebugFormatter_FWD_DEFINED__
typedef interface IDebugFormatter IDebugFormatter;
#ifdef __cplusplus
interface IDebugFormatter;
#endif /* __cplusplus */
#endif

#ifndef __ISimpleConnectionPoint_FWD_DEFINED__
#define __ISimpleConnectionPoint_FWD_DEFINED__
typedef interface ISimpleConnectionPoint ISimpleConnectionPoint;
#ifdef __cplusplus
interface ISimpleConnectionPoint;
#endif /* __cplusplus */
#endif

#ifndef __IDebugHelper_FWD_DEFINED__
#define __IDebugHelper_FWD_DEFINED__
typedef interface IDebugHelper IDebugHelper;
#ifdef __cplusplus
interface IDebugHelper;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugExpressionContexts_FWD_DEFINED__
#define __IEnumDebugExpressionContexts_FWD_DEFINED__
typedef interface IEnumDebugExpressionContexts IEnumDebugExpressionContexts;
#ifdef __cplusplus
interface IEnumDebugExpressionContexts;
#endif /* __cplusplus */
#endif

#ifndef __IProvideExpressionContexts_FWD_DEFINED__
#define __IProvideExpressionContexts_FWD_DEFINED__
typedef interface IProvideExpressionContexts IProvideExpressionContexts;
#ifdef __cplusplus
interface IProvideExpressionContexts;
#endif /* __cplusplus */
#endif

#ifndef __ProcessDebugManager_FWD_DEFINED__
#define __ProcessDebugManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class ProcessDebugManager ProcessDebugManager;
#else
typedef struct ProcessDebugManager ProcessDebugManager;
#endif /* defined __cplusplus */
#endif /* defined __ProcessDebugManager_FWD_DEFINED__ */

#ifndef __DebugHelper_FWD_DEFINED__
#define __DebugHelper_FWD_DEFINED__
#ifdef __cplusplus
typedef class DebugHelper DebugHelper;
#else
typedef struct DebugHelper DebugHelper;
#endif /* defined __cplusplus */
#endif /* defined __DebugHelper_FWD_DEFINED__ */

#ifndef __CDebugDocumentHelper_FWD_DEFINED__
#define __CDebugDocumentHelper_FWD_DEFINED__
#ifdef __cplusplus
typedef class CDebugDocumentHelper CDebugDocumentHelper;
#else
typedef struct CDebugDocumentHelper CDebugDocumentHelper;
#endif /* defined __cplusplus */
#endif /* defined __CDebugDocumentHelper_FWD_DEFINED__ */

#ifndef __MachineDebugManager_RETAIL_FWD_DEFINED__
#define __MachineDebugManager_RETAIL_FWD_DEFINED__
#ifdef __cplusplus
typedef class MachineDebugManager_RETAIL MachineDebugManager_RETAIL;
#else
typedef struct MachineDebugManager_RETAIL MachineDebugManager_RETAIL;
#endif /* defined __cplusplus */
#endif /* defined __MachineDebugManager_RETAIL_FWD_DEFINED__ */

#ifndef __MachineDebugManager_DEBUG_FWD_DEFINED__
#define __MachineDebugManager_DEBUG_FWD_DEFINED__
#ifdef __cplusplus
typedef class MachineDebugManager_DEBUG MachineDebugManager_DEBUG;
#else
typedef struct MachineDebugManager_DEBUG MachineDebugManager_DEBUG;
#endif /* defined __cplusplus */
#endif /* defined __MachineDebugManager_DEBUG_FWD_DEFINED__ */

#ifndef __DefaultDebugSessionProvider_FWD_DEFINED__
#define __DefaultDebugSessionProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class DefaultDebugSessionProvider DefaultDebugSessionProvider;
#else
typedef struct DefaultDebugSessionProvider DefaultDebugSessionProvider;
#endif /* defined __cplusplus */
#endif /* defined __DefaultDebugSessionProvider_FWD_DEFINED__ */

/* Headers for imported files */

#include <ocidl.h>
#include <oleidl.h>
#include <oaidl.h>
#include <activscp.h>
#include <dbgprop.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#ifndef __ActivDbg_h
#define __ActivDbg_h
#ifndef __IActiveScriptDebug32_FWD_DEFINED__
#define __IActiveScriptDebug32_FWD_DEFINED__
typedef interface IActiveScriptDebug32 IActiveScriptDebug32;
#ifdef __cplusplus
interface IActiveScriptDebug32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptDebug64_FWD_DEFINED__
#define __IActiveScriptDebug64_FWD_DEFINED__
typedef interface IActiveScriptDebug64 IActiveScriptDebug64;
#ifdef __cplusplus
interface IActiveScriptDebug64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptErrorDebug_FWD_DEFINED__
#define __IActiveScriptErrorDebug_FWD_DEFINED__
typedef interface IActiveScriptErrorDebug IActiveScriptErrorDebug;
#ifdef __cplusplus
interface IActiveScriptErrorDebug;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebug32_FWD_DEFINED__
#define __IActiveScriptSiteDebug32_FWD_DEFINED__
typedef interface IActiveScriptSiteDebug32 IActiveScriptSiteDebug32;
#ifdef __cplusplus
interface IActiveScriptSiteDebug32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebug64_FWD_DEFINED__
#define __IActiveScriptSiteDebug64_FWD_DEFINED__
typedef interface IActiveScriptSiteDebug64 IActiveScriptSiteDebug64;
#ifdef __cplusplus
interface IActiveScriptSiteDebug64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebugEx_FWD_DEFINED__
#define __IActiveScriptSiteDebugEx_FWD_DEFINED__
typedef interface IActiveScriptSiteDebugEx IActiveScriptSiteDebugEx;
#ifdef __cplusplus
interface IActiveScriptSiteDebugEx;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptTextInfo_FWD_DEFINED__
#define __IActiveScriptTextInfo_FWD_DEFINED__
typedef interface IActiveScriptTextInfo IActiveScriptTextInfo;
#ifdef __cplusplus
interface IActiveScriptTextInfo;
#endif /* __cplusplus */
#endif

#ifndef __IApplicationDebugger_FWD_DEFINED__
#define __IApplicationDebugger_FWD_DEFINED__
typedef interface IApplicationDebugger IApplicationDebugger;
#ifdef __cplusplus
interface IApplicationDebugger;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplication32_FWD_DEFINED__
#define __IDebugApplication32_FWD_DEFINED__
typedef interface IDebugApplication32 IDebugApplication32;
#ifdef __cplusplus
interface IDebugApplication32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplication64_FWD_DEFINED__
#define __IDebugApplication64_FWD_DEFINED__
typedef interface IDebugApplication64 IDebugApplication64;
#ifdef __cplusplus
interface IDebugApplication64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationNode_FWD_DEFINED__
#define __IDebugApplicationNode_FWD_DEFINED__
typedef interface IDebugApplicationNode IDebugApplicationNode;
#ifdef __cplusplus
interface IDebugApplicationNode;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationNodeEvents_FWD_DEFINED__
#define __IDebugApplicationNodeEvents_FWD_DEFINED__
typedef interface IDebugApplicationNodeEvents IDebugApplicationNodeEvents;
#ifdef __cplusplus
interface IDebugApplicationNodeEvents;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationThread_FWD_DEFINED__
#define __IDebugApplicationThread_FWD_DEFINED__
typedef interface IDebugApplicationThread IDebugApplicationThread;
#ifdef __cplusplus
interface IDebugApplicationThread;
#endif /* __cplusplus */
#endif

#ifndef __IDebugAsyncOperation_FWD_DEFINED__
#define __IDebugAsyncOperation_FWD_DEFINED__
typedef interface IDebugAsyncOperation IDebugAsyncOperation;
#ifdef __cplusplus
interface IDebugAsyncOperation;
#endif /* __cplusplus */
#endif

#ifndef __IDebugAsyncOperationCallBack_FWD_DEFINED__
#define __IDebugAsyncOperationCallBack_FWD_DEFINED__
typedef interface IDebugAsyncOperationCallBack IDebugAsyncOperationCallBack;
#ifdef __cplusplus
interface IDebugAsyncOperationCallBack;
#endif /* __cplusplus */
#endif

#ifndef __IDebugCodeContext_FWD_DEFINED__
#define __IDebugCodeContext_FWD_DEFINED__
typedef interface IDebugCodeContext IDebugCodeContext;
#ifdef __cplusplus
interface IDebugCodeContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocument_FWD_DEFINED__
#define __IDebugDocument_FWD_DEFINED__
typedef interface IDebugDocument IDebugDocument;
#ifdef __cplusplus
interface IDebugDocument;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentContext_FWD_DEFINED__
#define __IDebugDocumentContext_FWD_DEFINED__
typedef interface IDebugDocumentContext IDebugDocumentContext;
#ifdef __cplusplus
interface IDebugDocumentContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHelper32_FWD_DEFINED__
#define __IDebugDocumentHelper32_FWD_DEFINED__
typedef interface IDebugDocumentHelper32 IDebugDocumentHelper32;
#ifdef __cplusplus
interface IDebugDocumentHelper32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHelper64_FWD_DEFINED__
#define __IDebugDocumentHelper64_FWD_DEFINED__
typedef interface IDebugDocumentHelper64 IDebugDocumentHelper64;
#ifdef __cplusplus
interface IDebugDocumentHelper64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHost_FWD_DEFINED__
#define __IDebugDocumentHost_FWD_DEFINED__
typedef interface IDebugDocumentHost IDebugDocumentHost;
#ifdef __cplusplus
interface IDebugDocumentHost;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentInfo_FWD_DEFINED__
#define __IDebugDocumentInfo_FWD_DEFINED__
typedef interface IDebugDocumentInfo IDebugDocumentInfo;
#ifdef __cplusplus
interface IDebugDocumentInfo;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentProvider_FWD_DEFINED__
#define __IDebugDocumentProvider_FWD_DEFINED__
typedef interface IDebugDocumentProvider IDebugDocumentProvider;
#ifdef __cplusplus
interface IDebugDocumentProvider;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentText_FWD_DEFINED__
#define __IDebugDocumentText_FWD_DEFINED__
typedef interface IDebugDocumentText IDebugDocumentText;
#ifdef __cplusplus
interface IDebugDocumentText;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentTextAuthor_FWD_DEFINED__
#define __IDebugDocumentTextAuthor_FWD_DEFINED__
typedef interface IDebugDocumentTextAuthor IDebugDocumentTextAuthor;
#ifdef __cplusplus
interface IDebugDocumentTextAuthor;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentTextEvents_FWD_DEFINED__
#define __IDebugDocumentTextEvents_FWD_DEFINED__
typedef interface IDebugDocumentTextEvents IDebugDocumentTextEvents;
#ifdef __cplusplus
interface IDebugDocumentTextEvents;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpression_FWD_DEFINED__
#define __IDebugExpression_FWD_DEFINED__
typedef interface IDebugExpression IDebugExpression;
#ifdef __cplusplus
interface IDebugExpression;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpressionCallBack_FWD_DEFINED__
#define __IDebugExpressionCallBack_FWD_DEFINED__
typedef interface IDebugExpressionCallBack IDebugExpressionCallBack;
#ifdef __cplusplus
interface IDebugExpressionCallBack;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpressionContext_FWD_DEFINED__
#define __IDebugExpressionContext_FWD_DEFINED__
typedef interface IDebugExpressionContext IDebugExpressionContext;
#ifdef __cplusplus
interface IDebugExpressionContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugFormatter_FWD_DEFINED__
#define __IDebugFormatter_FWD_DEFINED__
typedef interface IDebugFormatter IDebugFormatter;
#ifdef __cplusplus
interface IDebugFormatter;
#endif /* __cplusplus */
#endif

#ifndef __IDebugSessionProvider_FWD_DEFINED__
#define __IDebugSessionProvider_FWD_DEFINED__
typedef interface IDebugSessionProvider IDebugSessionProvider;
#ifdef __cplusplus
interface IDebugSessionProvider;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrame_FWD_DEFINED__
#define __IDebugStackFrame_FWD_DEFINED__
typedef interface IDebugStackFrame IDebugStackFrame;
#ifdef __cplusplus
interface IDebugStackFrame;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrameSniffer_FWD_DEFINED__
#define __IDebugStackFrameSniffer_FWD_DEFINED__
typedef interface IDebugStackFrameSniffer IDebugStackFrameSniffer;
#ifdef __cplusplus
interface IDebugStackFrameSniffer;
#endif /* __cplusplus */
#endif

#ifndef __IDebugSyncOperation_FWD_DEFINED__
#define __IDebugSyncOperation_FWD_DEFINED__
typedef interface IDebugSyncOperation IDebugSyncOperation;
#ifdef __cplusplus
interface IDebugSyncOperation;
#endif /* __cplusplus */
#endif

#ifndef __IDebugThreadCall32_FWD_DEFINED__
#define __IDebugThreadCall32_FWD_DEFINED__
typedef interface IDebugThreadCall32 IDebugThreadCall32;
#ifdef __cplusplus
interface IDebugThreadCall32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugThreadCall64_FWD_DEFINED__
#define __IDebugThreadCall64_FWD_DEFINED__
typedef interface IDebugThreadCall64 IDebugThreadCall64;
#ifdef __cplusplus
interface IDebugThreadCall64;
#endif /* __cplusplus */
#endif

#ifndef __IEnumActiveScriptDebugs_FWD_DEFINED__
#define __IEnumActiveScriptDebugs_FWD_DEFINED__
typedef interface IEnumActiveScriptDebugs IEnumActiveScriptDebugs;
#ifdef __cplusplus
interface IEnumActiveScriptDebugs;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugApplicationNodes_FWD_DEFINED__
#define __IEnumDebugApplicationNodes_FWD_DEFINED__
typedef interface IEnumDebugApplicationNodes IEnumDebugApplicationNodes;
#ifdef __cplusplus
interface IEnumDebugApplicationNodes;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugCodeContexts_FWD_DEFINED__
#define __IEnumDebugCodeContexts_FWD_DEFINED__
typedef interface IEnumDebugCodeContexts IEnumDebugCodeContexts;
#ifdef __cplusplus
interface IEnumDebugCodeContexts;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugDocumentContexts_FWD_DEFINED__
#define __IEnumDebugDocumentContexts_FWD_DEFINED__
typedef interface IEnumDebugDocumentContexts IEnumDebugDocumentContexts;
#ifdef __cplusplus
interface IEnumDebugDocumentContexts;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugExpressionContexts_FWD_DEFINED__
#define __IEnumDebugExpressionContexts_FWD_DEFINED__
typedef interface IEnumDebugExpressionContexts IEnumDebugExpressionContexts;
#ifdef __cplusplus
interface IEnumDebugExpressionContexts;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugObjectBrowsers_FWD_DEFINED__
#define __IEnumDebugObjectBrowsers_FWD_DEFINED__
typedef interface IEnumDebugObjectBrowsers IEnumDebugObjectBrowsers;
#ifdef __cplusplus
interface IEnumDebugObjectBrowsers;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugStackFrames_FWD_DEFINED__
#define __IEnumDebugStackFrames_FWD_DEFINED__
typedef interface IEnumDebugStackFrames IEnumDebugStackFrames;
#ifdef __cplusplus
interface IEnumDebugStackFrames;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugStackFrames64_FWD_DEFINED__
#define __IEnumDebugStackFrames64_FWD_DEFINED__
typedef interface IEnumDebugStackFrames64 IEnumDebugStackFrames64;
#ifdef __cplusplus
interface IEnumDebugStackFrames64;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRemoteDebugApplications_FWD_DEFINED__
#define __IEnumRemoteDebugApplications_FWD_DEFINED__
typedef interface IEnumRemoteDebugApplications IEnumRemoteDebugApplications;
#ifdef __cplusplus
interface IEnumRemoteDebugApplications;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRemoteDebugApplicationThreads_FWD_DEFINED__
#define __IEnumRemoteDebugApplicationThreads_FWD_DEFINED__
typedef interface IEnumRemoteDebugApplicationThreads IEnumRemoteDebugApplicationThreads;
#ifdef __cplusplus
interface IEnumRemoteDebugApplicationThreads;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManager_FWD_DEFINED__
#define __IMachineDebugManager_FWD_DEFINED__
typedef interface IMachineDebugManager IMachineDebugManager;
#ifdef __cplusplus
interface IMachineDebugManager;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManagerCookie_FWD_DEFINED__
#define __IMachineDebugManagerCookie_FWD_DEFINED__
typedef interface IMachineDebugManagerCookie IMachineDebugManagerCookie;
#ifdef __cplusplus
interface IMachineDebugManagerCookie;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManagerEvents_FWD_DEFINED__
#define __IMachineDebugManagerEvents_FWD_DEFINED__
typedef interface IMachineDebugManagerEvents IMachineDebugManagerEvents;
#ifdef __cplusplus
interface IMachineDebugManagerEvents;
#endif /* __cplusplus */
#endif

#ifndef __IProcessDebugManager_FWD_DEFINED__
#define __IProcessDebugManager_FWD_DEFINED__
typedef interface IProcessDebugManager IProcessDebugManager;
#ifdef __cplusplus
interface IProcessDebugManager;
#endif /* __cplusplus */
#endif

#ifndef __IProvideExpressionContexts_FWD_DEFINED__
#define __IProvideExpressionContexts_FWD_DEFINED__
typedef interface IProvideExpressionContexts IProvideExpressionContexts;
#ifdef __cplusplus
interface IProvideExpressionContexts;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplication_FWD_DEFINED__
#define __IRemoteDebugApplication_FWD_DEFINED__
typedef interface IRemoteDebugApplication IRemoteDebugApplication;
#ifdef __cplusplus
interface IRemoteDebugApplication;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplicationThread_FWD_DEFINED__
#define __IRemoteDebugApplicationThread_FWD_DEFINED__
typedef interface IRemoteDebugApplicationThread IRemoteDebugApplicationThread;
#ifdef __cplusplus
interface IRemoteDebugApplicationThread;
#endif /* __cplusplus */
#endif

typedef DWORD APPBREAKFLAGS;
typedef WORD SOURCE_TEXT_ATTR;
typedef DWORD TEXT_DOC_ATTR;
typedef enum tagBREAKPOINT_STATE {
    BREAKPOINT_DELETED = 0,
    BREAKPOINT_DISABLED = 1,
    BREAKPOINT_ENABLED = 2
} BREAKPOINT_STATE;
typedef enum tagBREAKREASON {
    BREAKREASON_STEP = 0,
    BREAKREASON_BREAKPOINT = 1,
    BREAKREASON_DEBUGGER_BLOCK = 2,
    BREAKREASON_HOST_INITIATED = 3,
    BREAKREASON_LANGUAGE_INITIATED = 4,
    BREAKREASON_DEBUGGER_HALT = 5,
    BREAKREASON_ERROR = 6,
    BREAKREASON_JIT = 7
} BREAKREASON;
typedef enum tagBREAKRESUME_ACTION {
    BREAKRESUMEACTION_ABORT = 0,
    BREAKRESUMEACTION_CONTINUE = 1,
    BREAKRESUMEACTION_STEP_INTO = 2,
    BREAKRESUMEACTION_STEP_OVER = 3,
    BREAKRESUMEACTION_STEP_OUT = 4,
    BREAKRESUMEACTION_IGNORE = 5
} BREAKRESUMEACTION;
typedef enum tagDOCUMENTNAMETYPE {
    DOCUMENTNAMETYPE_APPNODE = 0,
    DOCUMENTNAMETYPE_TITLE = 1,
    DOCUMENTNAMETYPE_FILE_TAIL = 2,
    DOCUMENTNAMETYPE_URL = 3,
    DOCUMENTNAMETYPE_UNIQUE_TITLE = 4
} DOCUMENTNAMETYPE;
typedef enum tagERRORRESUMEACTION {
    ERRORRESUMEACTION_ReexecuteErrorStatement = 0,
    ERRORRESUMEACTION_AbortCallAndReturnErrorToCaller = 1,
    ERRORRESUMEACTION_SkipErrorStatement = 2
} ERRORRESUMEACTION;
typedef struct tagDebugStackFrameDescriptor {
    IDebugStackFrame *pdsf;
    DWORD dwMin;
    DWORD dwLim;
    WINBOOL fFinal;
    IUnknown *punkFinal;
} DebugStackFrameDescriptor;
typedef struct tagDebugStackFrameDescriptor64 {
    IDebugStackFrame *pdsf;
    DWORDLONG dwMin;
    DWORDLONG dwLim;
    WINBOOL fFinal;
    IUnknown *punkFinal;
} DebugStackFrameDescriptor64;
#define APPBREAKFLAG_DEBUGGER_BLOCK (0x1)

#define APPBREAKFLAG_DEBUGGER_HALT (0x2)

#define APPBREAKFLAG_STEP (0x10000)

#define APPBREAKFLAG_NESTED (0x20000)

#define APPBREAKFLAG_STEPTYPE_SOURCE (0x0)

#define APPBREAKFLAG_STEPTYPE_BYTECODE (0x100000)

#define APPBREAKFLAG_STEPTYPE_MACHINE (0x200000)

#define APPBREAKFLAG_STEPTYPE_MASK (0xf00000)

#define APPBREAKFLAG_IN_BREAKPOINT (0x80000000)

#define SOURCETEXT_ATTR_KEYWORD (0x1)

#define SOURCETEXT_ATTR_COMMENT (0x2)

#define SOURCETEXT_ATTR_NONSOURCE (0x4)

#define SOURCETEXT_ATTR_OPERATOR (0x8)

#define SOURCETEXT_ATTR_NUMBER (0x10)

#define SOURCETEXT_ATTR_STRING (0x20)

#define SOURCETEXT_ATTR_FUNCTION_START (0x40)

#define TEXT_DOC_ATTR_READONLY (0x1)

#define TEXT_DOC_ATTR_TYPE_PRIMARY (0x2)

#define TEXT_DOC_ATTR_TYPE_WORKER (0x4)

#define TEXT_DOC_ATTR_TYPE_SCRIPT (0x8)


#define DEBUG_TEXT_ISEXPRESSION (0x1)

#define DEBUG_TEXT_RETURNVALUE (0x2)

#define DEBUG_TEXT_NOSIDEEFFECTS (0x4)

#define DEBUG_TEXT_ALLOWBREAKPOINTS (0x8)

#define DEBUG_TEXT_ALLOWERRORREPORT (0x10)

#define DEBUG_TEXT_EVALUATETOCODECONTEXT (0x20)

#ifndef DISABLE_ACTIVDBG_INTERFACE_WRAPPERS
#ifdef _WIN64
#define IDebugApplication IDebugApplication64
#define IID_IDebugApplication IID_IDebugApplication64
#define IDebugThreadCall IDebugThreadCall64
#define IID_IDebugThreadCall IID_IDebugThreadCall64
#define SynchronousCallIntoThread SynchronousCallIntoThread64
#define IActiveScriptDebug IActiveScriptDebug64
#define IID_IActiveScriptDebug IID_IActiveScriptDebug64
#define IActiveScriptSiteDebug IActiveScriptSiteDebug64
#define IID_IActiveScriptSiteDebug IID_IActiveScriptSiteDebug64
#define IDebugStackFrameSnifferEx IDebugStackFrameSnifferEx64
#define IID_IDebugStackFrameSnifferEx IID_IDebugStackFrameSnifferEx64
#define EnumStackFramesEx EnumStackFramesEx64
#define IDebugDocumentHelper IDebugDocumentHelper64
#define IID_IDebugDocumentHelper IID_IDebugDocumentHelper64
#define IProcessDebugManager IProcessDebugManager64
#define IID_IProcessDebugManager IID_IProcessDebugManager64
#else
#define IDebugApplication IDebugApplication32
#define IID_IDebugApplication IID_IDebugApplication32
#define IDebugThreadCall IDebugThreadCall32
#define IID_IDebugThreadCall IID_IDebugThreadCall32
#define SynchronousCallIntoThread SynchronousCallIntoThread32
#define IActiveScriptDebug IActiveScriptDebug32
#define IID_IActiveScriptDebug IID_IActiveScriptDebug32
#define IActiveScriptSiteDebug IActiveScriptSiteDebug32
#define IID_IActiveScriptSiteDebug IID_IActiveScriptSiteDebug32
#define IDebugStackFrameSnifferEx IDebugStackFrameSnifferEx32
#define IID_IDebugStackFrameSnifferEx IID_IDebugStackFrameSnifferEx32
#define EnumStackFramesEx EnumStackFramesEx32
#define IDebugDocumentHelper IDebugDocumentHelper32
#define IID_IDebugDocumentHelper IID_IDebugDocumentHelper32
#define IProcessDebugManager IProcessDebugManager32
#define IID_IProcessDebugManager IID_IProcessDebugManager32
#endif
#endif
EXTERN_C const CLSID CLSID_DebugHelper;
EXTERN_C const CLSID CLSID_MachineDebugManager;
EXTERN_C const CLSID CLSID_ProcessDebugManager;
/*****************************************************************************
 * IActiveScriptDebug32 interface
 */
#ifndef __IActiveScriptDebug32_INTERFACE_DEFINED__
#define __IActiveScriptDebug32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptDebug32, 0x51973c10, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c10-cb0c-11d0-b5c9-00a0244a0e7a")
IActiveScriptDebug32 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetScriptTextAttributes(
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptletTextAttributes(
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumCodeContextsOfPosition(
        DWORD dwSourceContext,
        ULONG uCharacterOffset,
        ULONG uNumChars,
        IEnumDebugCodeContexts **ppescc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptDebug32, 0x51973c10, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IActiveScriptDebug32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptDebug32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptDebug32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptDebug32 *This);

    /*** IActiveScriptDebug32 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetScriptTextAttributes)(
        IActiveScriptDebug32 *This,
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr);

    HRESULT (STDMETHODCALLTYPE *GetScriptletTextAttributes)(
        IActiveScriptDebug32 *This,
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr);

    HRESULT (STDMETHODCALLTYPE *EnumCodeContextsOfPosition)(
        IActiveScriptDebug32 *This,
        DWORD dwSourceContext,
        ULONG uCharacterOffset,
        ULONG uNumChars,
        IEnumDebugCodeContexts **ppescc);

    END_INTERFACE
} IActiveScriptDebug32Vtbl;

interface IActiveScriptDebug32 {
    CONST_VTBL IActiveScriptDebug32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptDebug32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptDebug32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptDebug32_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptDebug32 methods ***/
#define IActiveScriptDebug32_GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr) (This)->lpVtbl->GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr)
#define IActiveScriptDebug32_GetScriptletTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr) (This)->lpVtbl->GetScriptletTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr)
#define IActiveScriptDebug32_EnumCodeContextsOfPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppescc) (This)->lpVtbl->EnumCodeContextsOfPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppescc)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptDebug32_QueryInterface(IActiveScriptDebug32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptDebug32_AddRef(IActiveScriptDebug32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptDebug32_Release(IActiveScriptDebug32* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptDebug32 methods ***/
static inline HRESULT IActiveScriptDebug32_GetScriptTextAttributes(IActiveScriptDebug32* This,LPCOLESTR pstrCode,ULONG uNumCodeChars,LPCOLESTR pstrDelimiter,DWORD dwFlags,SOURCE_TEXT_ATTR *pattr) {
    return This->lpVtbl->GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr);
}
static inline HRESULT IActiveScriptDebug32_GetScriptletTextAttributes(IActiveScriptDebug32* This,LPCOLESTR pstrCode,ULONG uNumCodeChars,LPCOLESTR pstrDelimiter,DWORD dwFlags,SOURCE_TEXT_ATTR *pattr) {
    return This->lpVtbl->GetScriptletTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr);
}
static inline HRESULT IActiveScriptDebug32_EnumCodeContextsOfPosition(IActiveScriptDebug32* This,DWORD dwSourceContext,ULONG uCharacterOffset,ULONG uNumChars,IEnumDebugCodeContexts **ppescc) {
    return This->lpVtbl->EnumCodeContextsOfPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppescc);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptDebug32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptDebug64 interface
 */
#ifndef __IActiveScriptDebug64_INTERFACE_DEFINED__
#define __IActiveScriptDebug64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptDebug64, 0xbc437e23, 0xf5b8, 0x47f4, 0xbb,0x79, 0x7d,0x1c,0xe5,0x48,0x3b,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bc437e23-f5b8-47f4-bb79-7d1ce5483b86")
IActiveScriptDebug64 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetScriptTextAttributes(
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptletTextAttributes(
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumCodeContextsOfPosition(
        DWORDLONG dwSourceContext,
        ULONG uCharacterOffset,
        ULONG uNumChars,
        IEnumDebugCodeContexts **ppescc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptDebug64, 0xbc437e23, 0xf5b8, 0x47f4, 0xbb,0x79, 0x7d,0x1c,0xe5,0x48,0x3b,0x86)
#endif
#else
typedef struct IActiveScriptDebug64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptDebug64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptDebug64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptDebug64 *This);

    /*** IActiveScriptDebug64 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetScriptTextAttributes)(
        IActiveScriptDebug64 *This,
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr);

    HRESULT (STDMETHODCALLTYPE *GetScriptletTextAttributes)(
        IActiveScriptDebug64 *This,
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr);

    HRESULT (STDMETHODCALLTYPE *EnumCodeContextsOfPosition)(
        IActiveScriptDebug64 *This,
        DWORDLONG dwSourceContext,
        ULONG uCharacterOffset,
        ULONG uNumChars,
        IEnumDebugCodeContexts **ppescc);

    END_INTERFACE
} IActiveScriptDebug64Vtbl;

interface IActiveScriptDebug64 {
    CONST_VTBL IActiveScriptDebug64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptDebug64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptDebug64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptDebug64_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptDebug64 methods ***/
#define IActiveScriptDebug64_GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr) (This)->lpVtbl->GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr)
#define IActiveScriptDebug64_GetScriptletTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr) (This)->lpVtbl->GetScriptletTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr)
#define IActiveScriptDebug64_EnumCodeContextsOfPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppescc) (This)->lpVtbl->EnumCodeContextsOfPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppescc)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptDebug64_QueryInterface(IActiveScriptDebug64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptDebug64_AddRef(IActiveScriptDebug64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptDebug64_Release(IActiveScriptDebug64* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptDebug64 methods ***/
static inline HRESULT IActiveScriptDebug64_GetScriptTextAttributes(IActiveScriptDebug64* This,LPCOLESTR pstrCode,ULONG uNumCodeChars,LPCOLESTR pstrDelimiter,DWORD dwFlags,SOURCE_TEXT_ATTR *pattr) {
    return This->lpVtbl->GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr);
}
static inline HRESULT IActiveScriptDebug64_GetScriptletTextAttributes(IActiveScriptDebug64* This,LPCOLESTR pstrCode,ULONG uNumCodeChars,LPCOLESTR pstrDelimiter,DWORD dwFlags,SOURCE_TEXT_ATTR *pattr) {
    return This->lpVtbl->GetScriptletTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr);
}
static inline HRESULT IActiveScriptDebug64_EnumCodeContextsOfPosition(IActiveScriptDebug64* This,DWORDLONG dwSourceContext,ULONG uCharacterOffset,ULONG uNumChars,IEnumDebugCodeContexts **ppescc) {
    return This->lpVtbl->EnumCodeContextsOfPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppescc);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptDebug64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptSiteDebug32 interface
 */
#ifndef __IActiveScriptSiteDebug32_INTERFACE_DEFINED__
#define __IActiveScriptSiteDebug32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSiteDebug32, 0x51973c11, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c11-cb0c-11d0-b5c9-00a0244a0e7a")
IActiveScriptSiteDebug32 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentContextFromPosition(
        DWORD dwSourceContext,
        ULONG uCharacterOffset,
        ULONG uNumChars,
        IDebugDocumentContext **ppsc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetApplication(
        IDebugApplication32 **ppda) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRootApplicationNode(
        IDebugApplicationNode **ppdanRoot) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnScriptErrorDebug(
        IActiveScriptErrorDebug *pErrorDebug,
        WINBOOL *pfEnterDebugger,
        WINBOOL *pfCallOnScriptErrorWhenContinuing) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSiteDebug32, 0x51973c11, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IActiveScriptSiteDebug32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSiteDebug32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSiteDebug32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSiteDebug32 *This);

    /*** IActiveScriptSiteDebug32 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentContextFromPosition)(
        IActiveScriptSiteDebug32 *This,
        DWORD dwSourceContext,
        ULONG uCharacterOffset,
        ULONG uNumChars,
        IDebugDocumentContext **ppsc);

    HRESULT (STDMETHODCALLTYPE *GetApplication)(
        IActiveScriptSiteDebug32 *This,
        IDebugApplication32 **ppda);

    HRESULT (STDMETHODCALLTYPE *GetRootApplicationNode)(
        IActiveScriptSiteDebug32 *This,
        IDebugApplicationNode **ppdanRoot);

    HRESULT (STDMETHODCALLTYPE *OnScriptErrorDebug)(
        IActiveScriptSiteDebug32 *This,
        IActiveScriptErrorDebug *pErrorDebug,
        WINBOOL *pfEnterDebugger,
        WINBOOL *pfCallOnScriptErrorWhenContinuing);

    END_INTERFACE
} IActiveScriptSiteDebug32Vtbl;

interface IActiveScriptSiteDebug32 {
    CONST_VTBL IActiveScriptSiteDebug32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSiteDebug32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSiteDebug32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSiteDebug32_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSiteDebug32 methods ***/
#define IActiveScriptSiteDebug32_GetDocumentContextFromPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppsc) (This)->lpVtbl->GetDocumentContextFromPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppsc)
#define IActiveScriptSiteDebug32_GetApplication(This,ppda) (This)->lpVtbl->GetApplication(This,ppda)
#define IActiveScriptSiteDebug32_GetRootApplicationNode(This,ppdanRoot) (This)->lpVtbl->GetRootApplicationNode(This,ppdanRoot)
#define IActiveScriptSiteDebug32_OnScriptErrorDebug(This,pErrorDebug,pfEnterDebugger,pfCallOnScriptErrorWhenContinuing) (This)->lpVtbl->OnScriptErrorDebug(This,pErrorDebug,pfEnterDebugger,pfCallOnScriptErrorWhenContinuing)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSiteDebug32_QueryInterface(IActiveScriptSiteDebug32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSiteDebug32_AddRef(IActiveScriptSiteDebug32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSiteDebug32_Release(IActiveScriptSiteDebug32* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSiteDebug32 methods ***/
static inline HRESULT IActiveScriptSiteDebug32_GetDocumentContextFromPosition(IActiveScriptSiteDebug32* This,DWORD dwSourceContext,ULONG uCharacterOffset,ULONG uNumChars,IDebugDocumentContext **ppsc) {
    return This->lpVtbl->GetDocumentContextFromPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppsc);
}
static inline HRESULT IActiveScriptSiteDebug32_GetApplication(IActiveScriptSiteDebug32* This,IDebugApplication32 **ppda) {
    return This->lpVtbl->GetApplication(This,ppda);
}
static inline HRESULT IActiveScriptSiteDebug32_GetRootApplicationNode(IActiveScriptSiteDebug32* This,IDebugApplicationNode **ppdanRoot) {
    return This->lpVtbl->GetRootApplicationNode(This,ppdanRoot);
}
static inline HRESULT IActiveScriptSiteDebug32_OnScriptErrorDebug(IActiveScriptSiteDebug32* This,IActiveScriptErrorDebug *pErrorDebug,WINBOOL *pfEnterDebugger,WINBOOL *pfCallOnScriptErrorWhenContinuing) {
    return This->lpVtbl->OnScriptErrorDebug(This,pErrorDebug,pfEnterDebugger,pfCallOnScriptErrorWhenContinuing);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSiteDebug32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptSiteDebug64 interface
 */
#ifndef __IActiveScriptSiteDebug64_INTERFACE_DEFINED__
#define __IActiveScriptSiteDebug64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSiteDebug64, 0xd6b96b0a, 0x7463, 0x402c, 0x92,0xac, 0x89,0x98,0x42,0x26,0x94,0x2f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d6b96b0a-7463-402c-92ac-89984226942f")
IActiveScriptSiteDebug64 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentContextFromPosition(
        DWORDLONG dwSourceContext,
        ULONG uCharacterOffset,
        ULONG uNumChars,
        IDebugDocumentContext **ppsc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetApplication(
        IDebugApplication64 **ppda) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRootApplicationNode(
        IDebugApplicationNode **ppdanRoot) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnScriptErrorDebug(
        IActiveScriptErrorDebug *pErrorDebug,
        WINBOOL *pfEnterDebugger,
        WINBOOL *pfCallOnScriptErrorWhenContinuing) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSiteDebug64, 0xd6b96b0a, 0x7463, 0x402c, 0x92,0xac, 0x89,0x98,0x42,0x26,0x94,0x2f)
#endif
#else
typedef struct IActiveScriptSiteDebug64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSiteDebug64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSiteDebug64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSiteDebug64 *This);

    /*** IActiveScriptSiteDebug64 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentContextFromPosition)(
        IActiveScriptSiteDebug64 *This,
        DWORDLONG dwSourceContext,
        ULONG uCharacterOffset,
        ULONG uNumChars,
        IDebugDocumentContext **ppsc);

    HRESULT (STDMETHODCALLTYPE *GetApplication)(
        IActiveScriptSiteDebug64 *This,
        IDebugApplication64 **ppda);

    HRESULT (STDMETHODCALLTYPE *GetRootApplicationNode)(
        IActiveScriptSiteDebug64 *This,
        IDebugApplicationNode **ppdanRoot);

    HRESULT (STDMETHODCALLTYPE *OnScriptErrorDebug)(
        IActiveScriptSiteDebug64 *This,
        IActiveScriptErrorDebug *pErrorDebug,
        WINBOOL *pfEnterDebugger,
        WINBOOL *pfCallOnScriptErrorWhenContinuing);

    END_INTERFACE
} IActiveScriptSiteDebug64Vtbl;

interface IActiveScriptSiteDebug64 {
    CONST_VTBL IActiveScriptSiteDebug64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSiteDebug64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSiteDebug64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSiteDebug64_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSiteDebug64 methods ***/
#define IActiveScriptSiteDebug64_GetDocumentContextFromPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppsc) (This)->lpVtbl->GetDocumentContextFromPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppsc)
#define IActiveScriptSiteDebug64_GetApplication(This,ppda) (This)->lpVtbl->GetApplication(This,ppda)
#define IActiveScriptSiteDebug64_GetRootApplicationNode(This,ppdanRoot) (This)->lpVtbl->GetRootApplicationNode(This,ppdanRoot)
#define IActiveScriptSiteDebug64_OnScriptErrorDebug(This,pErrorDebug,pfEnterDebugger,pfCallOnScriptErrorWhenContinuing) (This)->lpVtbl->OnScriptErrorDebug(This,pErrorDebug,pfEnterDebugger,pfCallOnScriptErrorWhenContinuing)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSiteDebug64_QueryInterface(IActiveScriptSiteDebug64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSiteDebug64_AddRef(IActiveScriptSiteDebug64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSiteDebug64_Release(IActiveScriptSiteDebug64* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSiteDebug64 methods ***/
static inline HRESULT IActiveScriptSiteDebug64_GetDocumentContextFromPosition(IActiveScriptSiteDebug64* This,DWORDLONG dwSourceContext,ULONG uCharacterOffset,ULONG uNumChars,IDebugDocumentContext **ppsc) {
    return This->lpVtbl->GetDocumentContextFromPosition(This,dwSourceContext,uCharacterOffset,uNumChars,ppsc);
}
static inline HRESULT IActiveScriptSiteDebug64_GetApplication(IActiveScriptSiteDebug64* This,IDebugApplication64 **ppda) {
    return This->lpVtbl->GetApplication(This,ppda);
}
static inline HRESULT IActiveScriptSiteDebug64_GetRootApplicationNode(IActiveScriptSiteDebug64* This,IDebugApplicationNode **ppdanRoot) {
    return This->lpVtbl->GetRootApplicationNode(This,ppdanRoot);
}
static inline HRESULT IActiveScriptSiteDebug64_OnScriptErrorDebug(IActiveScriptSiteDebug64* This,IActiveScriptErrorDebug *pErrorDebug,WINBOOL *pfEnterDebugger,WINBOOL *pfCallOnScriptErrorWhenContinuing) {
    return This->lpVtbl->OnScriptErrorDebug(This,pErrorDebug,pfEnterDebugger,pfCallOnScriptErrorWhenContinuing);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSiteDebug64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptSiteDebugEx interface
 */
#ifndef __IActiveScriptSiteDebugEx_INTERFACE_DEFINED__
#define __IActiveScriptSiteDebugEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSiteDebugEx, 0xbb722ccb, 0x6ad2, 0x41c6, 0xb7,0x80, 0xaf,0x9c,0x03,0xee,0x69,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb722ccb-6ad2-41c6-b780-af9c03ee69f5")
IActiveScriptSiteDebugEx : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnCanNotJITScriptErrorDebug(
        IActiveScriptErrorDebug *pErrorDebug,
        WINBOOL *pfCallOnScriptErrorWhenContinuing) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSiteDebugEx, 0xbb722ccb, 0x6ad2, 0x41c6, 0xb7,0x80, 0xaf,0x9c,0x03,0xee,0x69,0xf5)
#endif
#else
typedef struct IActiveScriptSiteDebugExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSiteDebugEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSiteDebugEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSiteDebugEx *This);

    /*** IActiveScriptSiteDebugEx methods ***/
    HRESULT (STDMETHODCALLTYPE *OnCanNotJITScriptErrorDebug)(
        IActiveScriptSiteDebugEx *This,
        IActiveScriptErrorDebug *pErrorDebug,
        WINBOOL *pfCallOnScriptErrorWhenContinuing);

    END_INTERFACE
} IActiveScriptSiteDebugExVtbl;

interface IActiveScriptSiteDebugEx {
    CONST_VTBL IActiveScriptSiteDebugExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSiteDebugEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSiteDebugEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSiteDebugEx_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSiteDebugEx methods ***/
#define IActiveScriptSiteDebugEx_OnCanNotJITScriptErrorDebug(This,pErrorDebug,pfCallOnScriptErrorWhenContinuing) (This)->lpVtbl->OnCanNotJITScriptErrorDebug(This,pErrorDebug,pfCallOnScriptErrorWhenContinuing)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSiteDebugEx_QueryInterface(IActiveScriptSiteDebugEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSiteDebugEx_AddRef(IActiveScriptSiteDebugEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSiteDebugEx_Release(IActiveScriptSiteDebugEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSiteDebugEx methods ***/
static inline HRESULT IActiveScriptSiteDebugEx_OnCanNotJITScriptErrorDebug(IActiveScriptSiteDebugEx* This,IActiveScriptErrorDebug *pErrorDebug,WINBOOL *pfCallOnScriptErrorWhenContinuing) {
    return This->lpVtbl->OnCanNotJITScriptErrorDebug(This,pErrorDebug,pfCallOnScriptErrorWhenContinuing);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSiteDebugEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptErrorDebug interface
 */
#ifndef __IActiveScriptErrorDebug_INTERFACE_DEFINED__
#define __IActiveScriptErrorDebug_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptErrorDebug, 0x51973c12, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c12-cb0c-11d0-b5c9-00a0244a0e7a")
IActiveScriptErrorDebug : public IActiveScriptError
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentContext(
        IDebugDocumentContext **ppssc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStackFrame(
        IDebugStackFrame **ppdsf) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptErrorDebug, 0x51973c12, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IActiveScriptErrorDebugVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptErrorDebug *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptErrorDebug *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptErrorDebug *This);

    /*** IActiveScriptError methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExceptionInfo)(
        IActiveScriptErrorDebug *This,
        EXCEPINFO *pexcepinfo);

    HRESULT (STDMETHODCALLTYPE *GetSourcePosition)(
        IActiveScriptErrorDebug *This,
        DWORD *pdwSourceContext,
        ULONG *pulLineNumber,
        LONG *plCharacterPosition);

    HRESULT (STDMETHODCALLTYPE *GetSourceLineText)(
        IActiveScriptErrorDebug *This,
        BSTR *pbstrSourceLine);

    /*** IActiveScriptErrorDebug methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentContext)(
        IActiveScriptErrorDebug *This,
        IDebugDocumentContext **ppssc);

    HRESULT (STDMETHODCALLTYPE *GetStackFrame)(
        IActiveScriptErrorDebug *This,
        IDebugStackFrame **ppdsf);

    END_INTERFACE
} IActiveScriptErrorDebugVtbl;

interface IActiveScriptErrorDebug {
    CONST_VTBL IActiveScriptErrorDebugVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptErrorDebug_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptErrorDebug_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptErrorDebug_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptError methods ***/
#define IActiveScriptErrorDebug_GetExceptionInfo(This,pexcepinfo) (This)->lpVtbl->GetExceptionInfo(This,pexcepinfo)
#define IActiveScriptErrorDebug_GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition) (This)->lpVtbl->GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition)
#define IActiveScriptErrorDebug_GetSourceLineText(This,pbstrSourceLine) (This)->lpVtbl->GetSourceLineText(This,pbstrSourceLine)
/*** IActiveScriptErrorDebug methods ***/
#define IActiveScriptErrorDebug_GetDocumentContext(This,ppssc) (This)->lpVtbl->GetDocumentContext(This,ppssc)
#define IActiveScriptErrorDebug_GetStackFrame(This,ppdsf) (This)->lpVtbl->GetStackFrame(This,ppdsf)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptErrorDebug_QueryInterface(IActiveScriptErrorDebug* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptErrorDebug_AddRef(IActiveScriptErrorDebug* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptErrorDebug_Release(IActiveScriptErrorDebug* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptError methods ***/
static inline HRESULT IActiveScriptErrorDebug_GetExceptionInfo(IActiveScriptErrorDebug* This,EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->GetExceptionInfo(This,pexcepinfo);
}
static inline HRESULT IActiveScriptErrorDebug_GetSourcePosition(IActiveScriptErrorDebug* This,DWORD *pdwSourceContext,ULONG *pulLineNumber,LONG *plCharacterPosition) {
    return This->lpVtbl->GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition);
}
static inline HRESULT IActiveScriptErrorDebug_GetSourceLineText(IActiveScriptErrorDebug* This,BSTR *pbstrSourceLine) {
    return This->lpVtbl->GetSourceLineText(This,pbstrSourceLine);
}
/*** IActiveScriptErrorDebug methods ***/
static inline HRESULT IActiveScriptErrorDebug_GetDocumentContext(IActiveScriptErrorDebug* This,IDebugDocumentContext **ppssc) {
    return This->lpVtbl->GetDocumentContext(This,ppssc);
}
static inline HRESULT IActiveScriptErrorDebug_GetStackFrame(IActiveScriptErrorDebug* This,IDebugStackFrame **ppdsf) {
    return This->lpVtbl->GetStackFrame(This,ppdsf);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptErrorDebug_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugCodeContext interface
 */
#ifndef __IDebugCodeContext_INTERFACE_DEFINED__
#define __IDebugCodeContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugCodeContext, 0x51973c13, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c13-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugCodeContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentContext(
        IDebugDocumentContext **ppsc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBreakPoint(
        BREAKPOINT_STATE bps) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugCodeContext, 0x51973c13, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugCodeContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugCodeContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugCodeContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugCodeContext *This);

    /*** IDebugCodeContext methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentContext)(
        IDebugCodeContext *This,
        IDebugDocumentContext **ppsc);

    HRESULT (STDMETHODCALLTYPE *SetBreakPoint)(
        IDebugCodeContext *This,
        BREAKPOINT_STATE bps);

    END_INTERFACE
} IDebugCodeContextVtbl;

interface IDebugCodeContext {
    CONST_VTBL IDebugCodeContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugCodeContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugCodeContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugCodeContext_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugCodeContext methods ***/
#define IDebugCodeContext_GetDocumentContext(This,ppsc) (This)->lpVtbl->GetDocumentContext(This,ppsc)
#define IDebugCodeContext_SetBreakPoint(This,bps) (This)->lpVtbl->SetBreakPoint(This,bps)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugCodeContext_QueryInterface(IDebugCodeContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugCodeContext_AddRef(IDebugCodeContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugCodeContext_Release(IDebugCodeContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugCodeContext methods ***/
static inline HRESULT IDebugCodeContext_GetDocumentContext(IDebugCodeContext* This,IDebugDocumentContext **ppsc) {
    return This->lpVtbl->GetDocumentContext(This,ppsc);
}
static inline HRESULT IDebugCodeContext_SetBreakPoint(IDebugCodeContext* This,BREAKPOINT_STATE bps) {
    return This->lpVtbl->SetBreakPoint(This,bps);
}
#endif
#endif

#endif


#endif  /* __IDebugCodeContext_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugExpression interface
 */
#ifndef __IDebugExpression_INTERFACE_DEFINED__
#define __IDebugExpression_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugExpression, 0x51973c14, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c14-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugExpression : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Start(
        IDebugExpressionCallBack *pdecb) = 0;

    virtual HRESULT STDMETHODCALLTYPE Abort(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryIsComplete(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResultAsString(
        HRESULT *phrResult,
        BSTR *pbstrResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResultAsDebugProperty(
        HRESULT *phrResult,
        IDebugProperty **ppdp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugExpression, 0x51973c14, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugExpressionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugExpression *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugExpression *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugExpression *This);

    /*** IDebugExpression methods ***/
    HRESULT (STDMETHODCALLTYPE *Start)(
        IDebugExpression *This,
        IDebugExpressionCallBack *pdecb);

    HRESULT (STDMETHODCALLTYPE *Abort)(
        IDebugExpression *This);

    HRESULT (STDMETHODCALLTYPE *QueryIsComplete)(
        IDebugExpression *This);

    HRESULT (STDMETHODCALLTYPE *GetResultAsString)(
        IDebugExpression *This,
        HRESULT *phrResult,
        BSTR *pbstrResult);

    HRESULT (STDMETHODCALLTYPE *GetResultAsDebugProperty)(
        IDebugExpression *This,
        HRESULT *phrResult,
        IDebugProperty **ppdp);

    END_INTERFACE
} IDebugExpressionVtbl;

interface IDebugExpression {
    CONST_VTBL IDebugExpressionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugExpression_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugExpression_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugExpression_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugExpression methods ***/
#define IDebugExpression_Start(This,pdecb) (This)->lpVtbl->Start(This,pdecb)
#define IDebugExpression_Abort(This) (This)->lpVtbl->Abort(This)
#define IDebugExpression_QueryIsComplete(This) (This)->lpVtbl->QueryIsComplete(This)
#define IDebugExpression_GetResultAsString(This,phrResult,pbstrResult) (This)->lpVtbl->GetResultAsString(This,phrResult,pbstrResult)
#define IDebugExpression_GetResultAsDebugProperty(This,phrResult,ppdp) (This)->lpVtbl->GetResultAsDebugProperty(This,phrResult,ppdp)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugExpression_QueryInterface(IDebugExpression* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugExpression_AddRef(IDebugExpression* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugExpression_Release(IDebugExpression* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugExpression methods ***/
static inline HRESULT IDebugExpression_Start(IDebugExpression* This,IDebugExpressionCallBack *pdecb) {
    return This->lpVtbl->Start(This,pdecb);
}
static inline HRESULT IDebugExpression_Abort(IDebugExpression* This) {
    return This->lpVtbl->Abort(This);
}
static inline HRESULT IDebugExpression_QueryIsComplete(IDebugExpression* This) {
    return This->lpVtbl->QueryIsComplete(This);
}
static inline HRESULT IDebugExpression_GetResultAsString(IDebugExpression* This,HRESULT *phrResult,BSTR *pbstrResult) {
    return This->lpVtbl->GetResultAsString(This,phrResult,pbstrResult);
}
static inline HRESULT IDebugExpression_GetResultAsDebugProperty(IDebugExpression* This,HRESULT *phrResult,IDebugProperty **ppdp) {
    return This->lpVtbl->GetResultAsDebugProperty(This,phrResult,ppdp);
}
#endif
#endif

#endif


#endif  /* __IDebugExpression_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugExpressionContext interface
 */
#ifndef __IDebugExpressionContext_INTERFACE_DEFINED__
#define __IDebugExpressionContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugExpressionContext, 0x51973c15, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c15-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugExpressionContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ParseLanguageText(
        LPCOLESTR pstrCode,
        UINT nRadix,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        IDebugExpression **ppe) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguageInfo(
        BSTR *pbstrLanguageName,
        GUID *pLanguageID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugExpressionContext, 0x51973c15, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugExpressionContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugExpressionContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugExpressionContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugExpressionContext *This);

    /*** IDebugExpressionContext methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseLanguageText)(
        IDebugExpressionContext *This,
        LPCOLESTR pstrCode,
        UINT nRadix,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        IDebugExpression **ppe);

    HRESULT (STDMETHODCALLTYPE *GetLanguageInfo)(
        IDebugExpressionContext *This,
        BSTR *pbstrLanguageName,
        GUID *pLanguageID);

    END_INTERFACE
} IDebugExpressionContextVtbl;

interface IDebugExpressionContext {
    CONST_VTBL IDebugExpressionContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugExpressionContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugExpressionContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugExpressionContext_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugExpressionContext methods ***/
#define IDebugExpressionContext_ParseLanguageText(This,pstrCode,nRadix,pstrDelimiter,dwFlags,ppe) (This)->lpVtbl->ParseLanguageText(This,pstrCode,nRadix,pstrDelimiter,dwFlags,ppe)
#define IDebugExpressionContext_GetLanguageInfo(This,pbstrLanguageName,pLanguageID) (This)->lpVtbl->GetLanguageInfo(This,pbstrLanguageName,pLanguageID)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugExpressionContext_QueryInterface(IDebugExpressionContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugExpressionContext_AddRef(IDebugExpressionContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugExpressionContext_Release(IDebugExpressionContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugExpressionContext methods ***/
static inline HRESULT IDebugExpressionContext_ParseLanguageText(IDebugExpressionContext* This,LPCOLESTR pstrCode,UINT nRadix,LPCOLESTR pstrDelimiter,DWORD dwFlags,IDebugExpression **ppe) {
    return This->lpVtbl->ParseLanguageText(This,pstrCode,nRadix,pstrDelimiter,dwFlags,ppe);
}
static inline HRESULT IDebugExpressionContext_GetLanguageInfo(IDebugExpressionContext* This,BSTR *pbstrLanguageName,GUID *pLanguageID) {
    return This->lpVtbl->GetLanguageInfo(This,pbstrLanguageName,pLanguageID);
}
#endif
#endif

#endif


#endif  /* __IDebugExpressionContext_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugExpressionCallBack interface
 */
#ifndef __IDebugExpressionCallBack_INTERFACE_DEFINED__
#define __IDebugExpressionCallBack_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugExpressionCallBack, 0x51973c16, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c16-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugExpressionCallBack : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE onComplete(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugExpressionCallBack, 0x51973c16, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugExpressionCallBackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugExpressionCallBack *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugExpressionCallBack *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugExpressionCallBack *This);

    /*** IDebugExpressionCallBack methods ***/
    HRESULT (STDMETHODCALLTYPE *onComplete)(
        IDebugExpressionCallBack *This);

    END_INTERFACE
} IDebugExpressionCallBackVtbl;

interface IDebugExpressionCallBack {
    CONST_VTBL IDebugExpressionCallBackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugExpressionCallBack_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugExpressionCallBack_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugExpressionCallBack_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugExpressionCallBack methods ***/
#define IDebugExpressionCallBack_onComplete(This) (This)->lpVtbl->onComplete(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugExpressionCallBack_QueryInterface(IDebugExpressionCallBack* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugExpressionCallBack_AddRef(IDebugExpressionCallBack* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugExpressionCallBack_Release(IDebugExpressionCallBack* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugExpressionCallBack methods ***/
static inline HRESULT IDebugExpressionCallBack_onComplete(IDebugExpressionCallBack* This) {
    return This->lpVtbl->onComplete(This);
}
#endif
#endif

#endif


#endif  /* __IDebugExpressionCallBack_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugStackFrame interface
 */
#ifndef __IDebugStackFrame_INTERFACE_DEFINED__
#define __IDebugStackFrame_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugStackFrame, 0x51973c17, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c17-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugStackFrame : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCodeContext(
        IDebugCodeContext **ppcc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescriptionString(
        WINBOOL fLong,
        BSTR *pbstrDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguageString(
        WINBOOL fLong,
        BSTR *pbstrLanguage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetThread(
        IDebugApplicationThread **ppat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDebugProperty(
        IDebugProperty **ppDebugProp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugStackFrame, 0x51973c17, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugStackFrameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugStackFrame *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugStackFrame *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugStackFrame *This);

    /*** IDebugStackFrame methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodeContext)(
        IDebugStackFrame *This,
        IDebugCodeContext **ppcc);

    HRESULT (STDMETHODCALLTYPE *GetDescriptionString)(
        IDebugStackFrame *This,
        WINBOOL fLong,
        BSTR *pbstrDescription);

    HRESULT (STDMETHODCALLTYPE *GetLanguageString)(
        IDebugStackFrame *This,
        WINBOOL fLong,
        BSTR *pbstrLanguage);

    HRESULT (STDMETHODCALLTYPE *GetThread)(
        IDebugStackFrame *This,
        IDebugApplicationThread **ppat);

    HRESULT (STDMETHODCALLTYPE *GetDebugProperty)(
        IDebugStackFrame *This,
        IDebugProperty **ppDebugProp);

    END_INTERFACE
} IDebugStackFrameVtbl;

interface IDebugStackFrame {
    CONST_VTBL IDebugStackFrameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugStackFrame_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugStackFrame_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugStackFrame_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugStackFrame methods ***/
#define IDebugStackFrame_GetCodeContext(This,ppcc) (This)->lpVtbl->GetCodeContext(This,ppcc)
#define IDebugStackFrame_GetDescriptionString(This,fLong,pbstrDescription) (This)->lpVtbl->GetDescriptionString(This,fLong,pbstrDescription)
#define IDebugStackFrame_GetLanguageString(This,fLong,pbstrLanguage) (This)->lpVtbl->GetLanguageString(This,fLong,pbstrLanguage)
#define IDebugStackFrame_GetThread(This,ppat) (This)->lpVtbl->GetThread(This,ppat)
#define IDebugStackFrame_GetDebugProperty(This,ppDebugProp) (This)->lpVtbl->GetDebugProperty(This,ppDebugProp)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugStackFrame_QueryInterface(IDebugStackFrame* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugStackFrame_AddRef(IDebugStackFrame* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugStackFrame_Release(IDebugStackFrame* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugStackFrame methods ***/
static inline HRESULT IDebugStackFrame_GetCodeContext(IDebugStackFrame* This,IDebugCodeContext **ppcc) {
    return This->lpVtbl->GetCodeContext(This,ppcc);
}
static inline HRESULT IDebugStackFrame_GetDescriptionString(IDebugStackFrame* This,WINBOOL fLong,BSTR *pbstrDescription) {
    return This->lpVtbl->GetDescriptionString(This,fLong,pbstrDescription);
}
static inline HRESULT IDebugStackFrame_GetLanguageString(IDebugStackFrame* This,WINBOOL fLong,BSTR *pbstrLanguage) {
    return This->lpVtbl->GetLanguageString(This,fLong,pbstrLanguage);
}
static inline HRESULT IDebugStackFrame_GetThread(IDebugStackFrame* This,IDebugApplicationThread **ppat) {
    return This->lpVtbl->GetThread(This,ppat);
}
static inline HRESULT IDebugStackFrame_GetDebugProperty(IDebugStackFrame* This,IDebugProperty **ppDebugProp) {
    return This->lpVtbl->GetDebugProperty(This,ppDebugProp);
}
#endif
#endif

#endif


#endif  /* __IDebugStackFrame_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugStackFrameSniffer interface
 */
#ifndef __IDebugStackFrameSniffer_INTERFACE_DEFINED__
#define __IDebugStackFrameSniffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugStackFrameSniffer, 0x51973c18, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c18-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugStackFrameSniffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EnumStackFrames(
        IEnumDebugStackFrames **ppedsf) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugStackFrameSniffer, 0x51973c18, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugStackFrameSnifferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugStackFrameSniffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugStackFrameSniffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugStackFrameSniffer *This);

    /*** IDebugStackFrameSniffer methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumStackFrames)(
        IDebugStackFrameSniffer *This,
        IEnumDebugStackFrames **ppedsf);

    END_INTERFACE
} IDebugStackFrameSnifferVtbl;

interface IDebugStackFrameSniffer {
    CONST_VTBL IDebugStackFrameSnifferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugStackFrameSniffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugStackFrameSniffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugStackFrameSniffer_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugStackFrameSniffer methods ***/
#define IDebugStackFrameSniffer_EnumStackFrames(This,ppedsf) (This)->lpVtbl->EnumStackFrames(This,ppedsf)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugStackFrameSniffer_QueryInterface(IDebugStackFrameSniffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugStackFrameSniffer_AddRef(IDebugStackFrameSniffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugStackFrameSniffer_Release(IDebugStackFrameSniffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugStackFrameSniffer methods ***/
static inline HRESULT IDebugStackFrameSniffer_EnumStackFrames(IDebugStackFrameSniffer* This,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->EnumStackFrames(This,ppedsf);
}
#endif
#endif

#endif


#endif  /* __IDebugStackFrameSniffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugStackFrameSnifferEx32 interface
 */
#ifndef __IDebugStackFrameSnifferEx32_INTERFACE_DEFINED__
#define __IDebugStackFrameSnifferEx32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugStackFrameSnifferEx32, 0x51973c19, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c19-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugStackFrameSnifferEx32 : public IDebugStackFrameSniffer
{
    virtual HRESULT STDMETHODCALLTYPE EnumStackFramesEx32(
        DWORD dwSpMin,
        IEnumDebugStackFrames **ppedsf) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugStackFrameSnifferEx32, 0x51973c19, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugStackFrameSnifferEx32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugStackFrameSnifferEx32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugStackFrameSnifferEx32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugStackFrameSnifferEx32 *This);

    /*** IDebugStackFrameSniffer methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumStackFrames)(
        IDebugStackFrameSnifferEx32 *This,
        IEnumDebugStackFrames **ppedsf);

    /*** IDebugStackFrameSnifferEx32 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumStackFramesEx32)(
        IDebugStackFrameSnifferEx32 *This,
        DWORD dwSpMin,
        IEnumDebugStackFrames **ppedsf);

    END_INTERFACE
} IDebugStackFrameSnifferEx32Vtbl;

interface IDebugStackFrameSnifferEx32 {
    CONST_VTBL IDebugStackFrameSnifferEx32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugStackFrameSnifferEx32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugStackFrameSnifferEx32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugStackFrameSnifferEx32_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugStackFrameSniffer methods ***/
#define IDebugStackFrameSnifferEx32_EnumStackFrames(This,ppedsf) (This)->lpVtbl->EnumStackFrames(This,ppedsf)
/*** IDebugStackFrameSnifferEx32 methods ***/
#define IDebugStackFrameSnifferEx32_EnumStackFramesEx32(This,dwSpMin,ppedsf) (This)->lpVtbl->EnumStackFramesEx32(This,dwSpMin,ppedsf)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugStackFrameSnifferEx32_QueryInterface(IDebugStackFrameSnifferEx32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugStackFrameSnifferEx32_AddRef(IDebugStackFrameSnifferEx32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugStackFrameSnifferEx32_Release(IDebugStackFrameSnifferEx32* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugStackFrameSniffer methods ***/
static inline HRESULT IDebugStackFrameSnifferEx32_EnumStackFrames(IDebugStackFrameSnifferEx32* This,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->EnumStackFrames(This,ppedsf);
}
/*** IDebugStackFrameSnifferEx32 methods ***/
static inline HRESULT IDebugStackFrameSnifferEx32_EnumStackFramesEx32(IDebugStackFrameSnifferEx32* This,DWORD dwSpMin,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->EnumStackFramesEx32(This,dwSpMin,ppedsf);
}
#endif
#endif

#endif


#endif  /* __IDebugStackFrameSnifferEx32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugStackFrameSnifferEx64 interface
 */
#ifndef __IDebugStackFrameSnifferEx64_INTERFACE_DEFINED__
#define __IDebugStackFrameSnifferEx64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugStackFrameSnifferEx64, 0x8cd12af4, 0x49c1, 0x4d52, 0x8d,0x8a, 0xc1,0x46,0xf4,0x75,0x81,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8cd12af4-49c1-4d52-8d8a-c146f47581aa")
IDebugStackFrameSnifferEx64 : public IDebugStackFrameSniffer
{
    virtual HRESULT STDMETHODCALLTYPE EnumStackFramesEx64(
        DWORDLONG dwSpMin,
        IEnumDebugStackFrames64 **ppedsf) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugStackFrameSnifferEx64, 0x8cd12af4, 0x49c1, 0x4d52, 0x8d,0x8a, 0xc1,0x46,0xf4,0x75,0x81,0xaa)
#endif
#else
typedef struct IDebugStackFrameSnifferEx64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugStackFrameSnifferEx64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugStackFrameSnifferEx64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugStackFrameSnifferEx64 *This);

    /*** IDebugStackFrameSniffer methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumStackFrames)(
        IDebugStackFrameSnifferEx64 *This,
        IEnumDebugStackFrames **ppedsf);

    /*** IDebugStackFrameSnifferEx64 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumStackFramesEx64)(
        IDebugStackFrameSnifferEx64 *This,
        DWORDLONG dwSpMin,
        IEnumDebugStackFrames64 **ppedsf);

    END_INTERFACE
} IDebugStackFrameSnifferEx64Vtbl;

interface IDebugStackFrameSnifferEx64 {
    CONST_VTBL IDebugStackFrameSnifferEx64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugStackFrameSnifferEx64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugStackFrameSnifferEx64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugStackFrameSnifferEx64_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugStackFrameSniffer methods ***/
#define IDebugStackFrameSnifferEx64_EnumStackFrames(This,ppedsf) (This)->lpVtbl->EnumStackFrames(This,ppedsf)
/*** IDebugStackFrameSnifferEx64 methods ***/
#define IDebugStackFrameSnifferEx64_EnumStackFramesEx64(This,dwSpMin,ppedsf) (This)->lpVtbl->EnumStackFramesEx64(This,dwSpMin,ppedsf)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugStackFrameSnifferEx64_QueryInterface(IDebugStackFrameSnifferEx64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugStackFrameSnifferEx64_AddRef(IDebugStackFrameSnifferEx64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugStackFrameSnifferEx64_Release(IDebugStackFrameSnifferEx64* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugStackFrameSniffer methods ***/
static inline HRESULT IDebugStackFrameSnifferEx64_EnumStackFrames(IDebugStackFrameSnifferEx64* This,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->EnumStackFrames(This,ppedsf);
}
/*** IDebugStackFrameSnifferEx64 methods ***/
static inline HRESULT IDebugStackFrameSnifferEx64_EnumStackFramesEx64(IDebugStackFrameSnifferEx64* This,DWORDLONG dwSpMin,IEnumDebugStackFrames64 **ppedsf) {
    return This->lpVtbl->EnumStackFramesEx64(This,dwSpMin,ppedsf);
}
#endif
#endif

#endif


#endif  /* __IDebugStackFrameSnifferEx64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugSyncOperation interface
 */
#ifndef __IDebugSyncOperation_INTERFACE_DEFINED__
#define __IDebugSyncOperation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugSyncOperation, 0x51973c1a, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c1a-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugSyncOperation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetTargetThread(
        IDebugApplicationThread **ppatTarget) = 0;

    virtual HRESULT STDMETHODCALLTYPE Execute(
        IUnknown **ppunkResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE InProgressAbort(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugSyncOperation, 0x51973c1a, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugSyncOperationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugSyncOperation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugSyncOperation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugSyncOperation *This);

    /*** IDebugSyncOperation methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTargetThread)(
        IDebugSyncOperation *This,
        IDebugApplicationThread **ppatTarget);

    HRESULT (STDMETHODCALLTYPE *Execute)(
        IDebugSyncOperation *This,
        IUnknown **ppunkResult);

    HRESULT (STDMETHODCALLTYPE *InProgressAbort)(
        IDebugSyncOperation *This);

    END_INTERFACE
} IDebugSyncOperationVtbl;

interface IDebugSyncOperation {
    CONST_VTBL IDebugSyncOperationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugSyncOperation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugSyncOperation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugSyncOperation_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugSyncOperation methods ***/
#define IDebugSyncOperation_GetTargetThread(This,ppatTarget) (This)->lpVtbl->GetTargetThread(This,ppatTarget)
#define IDebugSyncOperation_Execute(This,ppunkResult) (This)->lpVtbl->Execute(This,ppunkResult)
#define IDebugSyncOperation_InProgressAbort(This) (This)->lpVtbl->InProgressAbort(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugSyncOperation_QueryInterface(IDebugSyncOperation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugSyncOperation_AddRef(IDebugSyncOperation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugSyncOperation_Release(IDebugSyncOperation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugSyncOperation methods ***/
static inline HRESULT IDebugSyncOperation_GetTargetThread(IDebugSyncOperation* This,IDebugApplicationThread **ppatTarget) {
    return This->lpVtbl->GetTargetThread(This,ppatTarget);
}
static inline HRESULT IDebugSyncOperation_Execute(IDebugSyncOperation* This,IUnknown **ppunkResult) {
    return This->lpVtbl->Execute(This,ppunkResult);
}
static inline HRESULT IDebugSyncOperation_InProgressAbort(IDebugSyncOperation* This) {
    return This->lpVtbl->InProgressAbort(This);
}
#endif
#endif

#endif


#endif  /* __IDebugSyncOperation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugAsyncOperation interface
 */
#ifndef __IDebugAsyncOperation_INTERFACE_DEFINED__
#define __IDebugAsyncOperation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugAsyncOperation, 0x51973c1b, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c1b-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugAsyncOperation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSyncDebugOperation(
        IDebugSyncOperation **ppsdo) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        IDebugAsyncOperationCallBack *padocb) = 0;

    virtual HRESULT STDMETHODCALLTYPE Abort(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryIsComplete(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResult(
        HRESULT *phrResult,
        IUnknown **ppunkResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugAsyncOperation, 0x51973c1b, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugAsyncOperationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugAsyncOperation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugAsyncOperation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugAsyncOperation *This);

    /*** IDebugAsyncOperation methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSyncDebugOperation)(
        IDebugAsyncOperation *This,
        IDebugSyncOperation **ppsdo);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IDebugAsyncOperation *This,
        IDebugAsyncOperationCallBack *padocb);

    HRESULT (STDMETHODCALLTYPE *Abort)(
        IDebugAsyncOperation *This);

    HRESULT (STDMETHODCALLTYPE *QueryIsComplete)(
        IDebugAsyncOperation *This);

    HRESULT (STDMETHODCALLTYPE *GetResult)(
        IDebugAsyncOperation *This,
        HRESULT *phrResult,
        IUnknown **ppunkResult);

    END_INTERFACE
} IDebugAsyncOperationVtbl;

interface IDebugAsyncOperation {
    CONST_VTBL IDebugAsyncOperationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugAsyncOperation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugAsyncOperation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugAsyncOperation_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugAsyncOperation methods ***/
#define IDebugAsyncOperation_GetSyncDebugOperation(This,ppsdo) (This)->lpVtbl->GetSyncDebugOperation(This,ppsdo)
#define IDebugAsyncOperation_Start(This,padocb) (This)->lpVtbl->Start(This,padocb)
#define IDebugAsyncOperation_Abort(This) (This)->lpVtbl->Abort(This)
#define IDebugAsyncOperation_QueryIsComplete(This) (This)->lpVtbl->QueryIsComplete(This)
#define IDebugAsyncOperation_GetResult(This,phrResult,ppunkResult) (This)->lpVtbl->GetResult(This,phrResult,ppunkResult)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugAsyncOperation_QueryInterface(IDebugAsyncOperation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugAsyncOperation_AddRef(IDebugAsyncOperation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugAsyncOperation_Release(IDebugAsyncOperation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugAsyncOperation methods ***/
static inline HRESULT IDebugAsyncOperation_GetSyncDebugOperation(IDebugAsyncOperation* This,IDebugSyncOperation **ppsdo) {
    return This->lpVtbl->GetSyncDebugOperation(This,ppsdo);
}
static inline HRESULT IDebugAsyncOperation_Start(IDebugAsyncOperation* This,IDebugAsyncOperationCallBack *padocb) {
    return This->lpVtbl->Start(This,padocb);
}
static inline HRESULT IDebugAsyncOperation_Abort(IDebugAsyncOperation* This) {
    return This->lpVtbl->Abort(This);
}
static inline HRESULT IDebugAsyncOperation_QueryIsComplete(IDebugAsyncOperation* This) {
    return This->lpVtbl->QueryIsComplete(This);
}
static inline HRESULT IDebugAsyncOperation_GetResult(IDebugAsyncOperation* This,HRESULT *phrResult,IUnknown **ppunkResult) {
    return This->lpVtbl->GetResult(This,phrResult,ppunkResult);
}
#endif
#endif

#endif


#endif  /* __IDebugAsyncOperation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugAsyncOperationCallBack interface
 */
#ifndef __IDebugAsyncOperationCallBack_INTERFACE_DEFINED__
#define __IDebugAsyncOperationCallBack_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugAsyncOperationCallBack, 0x51973c1c, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c1c-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugAsyncOperationCallBack : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE onComplete(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugAsyncOperationCallBack, 0x51973c1c, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugAsyncOperationCallBackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugAsyncOperationCallBack *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugAsyncOperationCallBack *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugAsyncOperationCallBack *This);

    /*** IDebugAsyncOperationCallBack methods ***/
    HRESULT (STDMETHODCALLTYPE *onComplete)(
        IDebugAsyncOperationCallBack *This);

    END_INTERFACE
} IDebugAsyncOperationCallBackVtbl;

interface IDebugAsyncOperationCallBack {
    CONST_VTBL IDebugAsyncOperationCallBackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugAsyncOperationCallBack_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugAsyncOperationCallBack_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugAsyncOperationCallBack_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugAsyncOperationCallBack methods ***/
#define IDebugAsyncOperationCallBack_onComplete(This) (This)->lpVtbl->onComplete(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugAsyncOperationCallBack_QueryInterface(IDebugAsyncOperationCallBack* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugAsyncOperationCallBack_AddRef(IDebugAsyncOperationCallBack* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugAsyncOperationCallBack_Release(IDebugAsyncOperationCallBack* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugAsyncOperationCallBack methods ***/
static inline HRESULT IDebugAsyncOperationCallBack_onComplete(IDebugAsyncOperationCallBack* This) {
    return This->lpVtbl->onComplete(This);
}
#endif
#endif

#endif


#endif  /* __IDebugAsyncOperationCallBack_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumDebugCodeContexts interface
 */
#ifndef __IEnumDebugCodeContexts_INTERFACE_DEFINED__
#define __IEnumDebugCodeContexts_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDebugCodeContexts, 0x51973c1d, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c1d-cb0c-11d0-b5c9-00a0244a0e7a")
IEnumDebugCodeContexts : public IUnknown
{
    virtual HRESULT __stdcall Next(
        ULONG celt,
        IDebugCodeContext **pscc,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDebugCodeContexts **ppescc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDebugCodeContexts, 0x51973c1d, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IEnumDebugCodeContextsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDebugCodeContexts *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDebugCodeContexts *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDebugCodeContexts *This);

    /*** IEnumDebugCodeContexts methods ***/
    HRESULT (__stdcall *Next)(
        IEnumDebugCodeContexts *This,
        ULONG celt,
        IDebugCodeContext **pscc,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDebugCodeContexts *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDebugCodeContexts *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDebugCodeContexts *This,
        IEnumDebugCodeContexts **ppescc);

    END_INTERFACE
} IEnumDebugCodeContextsVtbl;

interface IEnumDebugCodeContexts {
    CONST_VTBL IEnumDebugCodeContextsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDebugCodeContexts_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDebugCodeContexts_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDebugCodeContexts_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDebugCodeContexts methods ***/
#define IEnumDebugCodeContexts_Next(This,celt,pscc,pceltFetched) (This)->lpVtbl->Next(This,celt,pscc,pceltFetched)
#define IEnumDebugCodeContexts_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumDebugCodeContexts_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDebugCodeContexts_Clone(This,ppescc) (This)->lpVtbl->Clone(This,ppescc)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDebugCodeContexts_QueryInterface(IEnumDebugCodeContexts* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDebugCodeContexts_AddRef(IEnumDebugCodeContexts* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDebugCodeContexts_Release(IEnumDebugCodeContexts* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDebugCodeContexts methods ***/
static inline HRESULT IEnumDebugCodeContexts_Next(IEnumDebugCodeContexts* This,ULONG celt,IDebugCodeContext **pscc,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,pscc,pceltFetched);
}
static inline HRESULT IEnumDebugCodeContexts_Skip(IEnumDebugCodeContexts* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumDebugCodeContexts_Reset(IEnumDebugCodeContexts* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDebugCodeContexts_Clone(IEnumDebugCodeContexts* This,IEnumDebugCodeContexts **ppescc) {
    return This->lpVtbl->Clone(This,ppescc);
}
#endif
#endif

#endif

HRESULT __stdcall IEnumDebugCodeContexts_RemoteNext_Proxy(
    IEnumDebugCodeContexts* This,
    ULONG celt,
    IDebugCodeContext **pscc,
    ULONG *pceltFetched);
void __RPC_STUB IEnumDebugCodeContexts_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumDebugCodeContexts_Next_Proxy(
    IEnumDebugCodeContexts* This,
    ULONG celt,
    IDebugCodeContext **pscc,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumDebugCodeContexts_Next_Stub(
    IEnumDebugCodeContexts* This,
    ULONG celt,
    IDebugCodeContext **pscc,
    ULONG *pceltFetched);

#endif  /* __IEnumDebugCodeContexts_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumDebugStackFrames interface
 */
#ifndef __IEnumDebugStackFrames_INTERFACE_DEFINED__
#define __IEnumDebugStackFrames_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDebugStackFrames, 0x51973c1e, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c1e-cb0c-11d0-b5c9-00a0244a0e7a")
IEnumDebugStackFrames : public IUnknown
{
    virtual HRESULT __stdcall Next(
        ULONG celt,
        DebugStackFrameDescriptor *prgdsfd,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDebugStackFrames **ppedsf) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDebugStackFrames, 0x51973c1e, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IEnumDebugStackFramesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDebugStackFrames *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDebugStackFrames *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDebugStackFrames *This);

    /*** IEnumDebugStackFrames methods ***/
    HRESULT (__stdcall *Next)(
        IEnumDebugStackFrames *This,
        ULONG celt,
        DebugStackFrameDescriptor *prgdsfd,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDebugStackFrames *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDebugStackFrames *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDebugStackFrames *This,
        IEnumDebugStackFrames **ppedsf);

    END_INTERFACE
} IEnumDebugStackFramesVtbl;

interface IEnumDebugStackFrames {
    CONST_VTBL IEnumDebugStackFramesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDebugStackFrames_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDebugStackFrames_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDebugStackFrames_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDebugStackFrames methods ***/
#define IEnumDebugStackFrames_Next(This,celt,prgdsfd,pceltFetched) (This)->lpVtbl->Next(This,celt,prgdsfd,pceltFetched)
#define IEnumDebugStackFrames_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumDebugStackFrames_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDebugStackFrames_Clone(This,ppedsf) (This)->lpVtbl->Clone(This,ppedsf)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDebugStackFrames_QueryInterface(IEnumDebugStackFrames* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDebugStackFrames_AddRef(IEnumDebugStackFrames* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDebugStackFrames_Release(IEnumDebugStackFrames* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDebugStackFrames methods ***/
static inline HRESULT IEnumDebugStackFrames_Next(IEnumDebugStackFrames* This,ULONG celt,DebugStackFrameDescriptor *prgdsfd,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,prgdsfd,pceltFetched);
}
static inline HRESULT IEnumDebugStackFrames_Skip(IEnumDebugStackFrames* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumDebugStackFrames_Reset(IEnumDebugStackFrames* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDebugStackFrames_Clone(IEnumDebugStackFrames* This,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->Clone(This,ppedsf);
}
#endif
#endif

#endif

HRESULT __stdcall IEnumDebugStackFrames_RemoteNext_Proxy(
    IEnumDebugStackFrames* This,
    ULONG celt,
    DebugStackFrameDescriptor *prgdsfd,
    ULONG *pceltFetched);
void __RPC_STUB IEnumDebugStackFrames_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumDebugStackFrames_Next_Proxy(
    IEnumDebugStackFrames* This,
    ULONG celt,
    DebugStackFrameDescriptor *prgdsfd,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumDebugStackFrames_Next_Stub(
    IEnumDebugStackFrames* This,
    ULONG celt,
    DebugStackFrameDescriptor *prgdsfd,
    ULONG *pceltFetched);

#endif  /* __IEnumDebugStackFrames_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumDebugStackFrames64 interface
 */
#ifndef __IEnumDebugStackFrames64_INTERFACE_DEFINED__
#define __IEnumDebugStackFrames64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDebugStackFrames64, 0x0dc38853, 0xc1b0, 0x4176, 0xa9,0x84, 0xb2,0x98,0x36,0x10,0x27,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0dc38853-c1b0-4176-a984-b298361027af")
IEnumDebugStackFrames64 : public IEnumDebugStackFrames
{
    virtual HRESULT __stdcall Next64(
        ULONG celt,
        DebugStackFrameDescriptor64 *prgdsfd,
        ULONG *pceltFetched) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDebugStackFrames64, 0x0dc38853, 0xc1b0, 0x4176, 0xa9,0x84, 0xb2,0x98,0x36,0x10,0x27,0xaf)
#endif
#else
typedef struct IEnumDebugStackFrames64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDebugStackFrames64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDebugStackFrames64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDebugStackFrames64 *This);

    /*** IEnumDebugStackFrames methods ***/
    HRESULT (__stdcall *Next)(
        IEnumDebugStackFrames64 *This,
        ULONG celt,
        DebugStackFrameDescriptor *prgdsfd,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDebugStackFrames64 *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDebugStackFrames64 *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDebugStackFrames64 *This,
        IEnumDebugStackFrames **ppedsf);

    /*** IEnumDebugStackFrames64 methods ***/
    HRESULT (__stdcall *Next64)(
        IEnumDebugStackFrames64 *This,
        ULONG celt,
        DebugStackFrameDescriptor64 *prgdsfd,
        ULONG *pceltFetched);

    END_INTERFACE
} IEnumDebugStackFrames64Vtbl;

interface IEnumDebugStackFrames64 {
    CONST_VTBL IEnumDebugStackFrames64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDebugStackFrames64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDebugStackFrames64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDebugStackFrames64_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDebugStackFrames methods ***/
#define IEnumDebugStackFrames64_Next(This,celt,prgdsfd,pceltFetched) (This)->lpVtbl->Next(This,celt,prgdsfd,pceltFetched)
#define IEnumDebugStackFrames64_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumDebugStackFrames64_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDebugStackFrames64_Clone(This,ppedsf) (This)->lpVtbl->Clone(This,ppedsf)
/*** IEnumDebugStackFrames64 methods ***/
#define IEnumDebugStackFrames64_Next64(This,celt,prgdsfd,pceltFetched) (This)->lpVtbl->Next64(This,celt,prgdsfd,pceltFetched)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDebugStackFrames64_QueryInterface(IEnumDebugStackFrames64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDebugStackFrames64_AddRef(IEnumDebugStackFrames64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDebugStackFrames64_Release(IEnumDebugStackFrames64* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDebugStackFrames methods ***/
static inline HRESULT IEnumDebugStackFrames64_Next(IEnumDebugStackFrames64* This,ULONG celt,DebugStackFrameDescriptor *prgdsfd,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,prgdsfd,pceltFetched);
}
static inline HRESULT IEnumDebugStackFrames64_Skip(IEnumDebugStackFrames64* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumDebugStackFrames64_Reset(IEnumDebugStackFrames64* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDebugStackFrames64_Clone(IEnumDebugStackFrames64* This,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->Clone(This,ppedsf);
}
/*** IEnumDebugStackFrames64 methods ***/
static inline HRESULT IEnumDebugStackFrames64_Next64(IEnumDebugStackFrames64* This,ULONG celt,DebugStackFrameDescriptor64 *prgdsfd,ULONG *pceltFetched) {
    return This->lpVtbl->Next64(This,celt,prgdsfd,pceltFetched);
}
#endif
#endif

#endif

HRESULT __stdcall IEnumDebugStackFrames64_RemoteNext64_Proxy(
    IEnumDebugStackFrames64* This,
    ULONG celt,
    DebugStackFrameDescriptor64 *prgdsfd,
    ULONG *pceltFetched);
void __RPC_STUB IEnumDebugStackFrames64_RemoteNext64_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumDebugStackFrames64_Next64_Proxy(
    IEnumDebugStackFrames64* This,
    ULONG celt,
    DebugStackFrameDescriptor64 *prgdsfd,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumDebugStackFrames64_Next64_Stub(
    IEnumDebugStackFrames64* This,
    ULONG celt,
    DebugStackFrameDescriptor64 *prgdsfd,
    ULONG *pceltFetched);

#endif  /* __IEnumDebugStackFrames64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentInfo interface
 */
#ifndef __IDebugDocumentInfo_INTERFACE_DEFINED__
#define __IDebugDocumentInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentInfo, 0x51973c1f, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c1f-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetName(
        DOCUMENTNAMETYPE dnt,
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentClassId(
        CLSID *pclsidDocument) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentInfo, 0x51973c1f, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentInfo *This);

    /*** IDebugDocumentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugDocumentInfo *This,
        DOCUMENTNAMETYPE dnt,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetDocumentClassId)(
        IDebugDocumentInfo *This,
        CLSID *pclsidDocument);

    END_INTERFACE
} IDebugDocumentInfoVtbl;

interface IDebugDocumentInfo {
    CONST_VTBL IDebugDocumentInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentInfo methods ***/
#define IDebugDocumentInfo_GetName(This,dnt,pbstrName) (This)->lpVtbl->GetName(This,dnt,pbstrName)
#define IDebugDocumentInfo_GetDocumentClassId(This,pclsidDocument) (This)->lpVtbl->GetDocumentClassId(This,pclsidDocument)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentInfo_QueryInterface(IDebugDocumentInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentInfo_AddRef(IDebugDocumentInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentInfo_Release(IDebugDocumentInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentInfo methods ***/
static inline HRESULT IDebugDocumentInfo_GetName(IDebugDocumentInfo* This,DOCUMENTNAMETYPE dnt,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,dnt,pbstrName);
}
static inline HRESULT IDebugDocumentInfo_GetDocumentClassId(IDebugDocumentInfo* This,CLSID *pclsidDocument) {
    return This->lpVtbl->GetDocumentClassId(This,pclsidDocument);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentProvider interface
 */
#ifndef __IDebugDocumentProvider_INTERFACE_DEFINED__
#define __IDebugDocumentProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentProvider, 0x51973c20, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c20-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentProvider : public IDebugDocumentInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetDocument(
        IDebugDocument **ppssd) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentProvider, 0x51973c20, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentProvider *This);

    /*** IDebugDocumentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugDocumentProvider *This,
        DOCUMENTNAMETYPE dnt,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetDocumentClassId)(
        IDebugDocumentProvider *This,
        CLSID *pclsidDocument);

    /*** IDebugDocumentProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocument)(
        IDebugDocumentProvider *This,
        IDebugDocument **ppssd);

    END_INTERFACE
} IDebugDocumentProviderVtbl;

interface IDebugDocumentProvider {
    CONST_VTBL IDebugDocumentProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentInfo methods ***/
#define IDebugDocumentProvider_GetName(This,dnt,pbstrName) (This)->lpVtbl->GetName(This,dnt,pbstrName)
#define IDebugDocumentProvider_GetDocumentClassId(This,pclsidDocument) (This)->lpVtbl->GetDocumentClassId(This,pclsidDocument)
/*** IDebugDocumentProvider methods ***/
#define IDebugDocumentProvider_GetDocument(This,ppssd) (This)->lpVtbl->GetDocument(This,ppssd)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentProvider_QueryInterface(IDebugDocumentProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentProvider_AddRef(IDebugDocumentProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentProvider_Release(IDebugDocumentProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentInfo methods ***/
static inline HRESULT IDebugDocumentProvider_GetName(IDebugDocumentProvider* This,DOCUMENTNAMETYPE dnt,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,dnt,pbstrName);
}
static inline HRESULT IDebugDocumentProvider_GetDocumentClassId(IDebugDocumentProvider* This,CLSID *pclsidDocument) {
    return This->lpVtbl->GetDocumentClassId(This,pclsidDocument);
}
/*** IDebugDocumentProvider methods ***/
static inline HRESULT IDebugDocumentProvider_GetDocument(IDebugDocumentProvider* This,IDebugDocument **ppssd) {
    return This->lpVtbl->GetDocument(This,ppssd);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocument interface
 */
#ifndef __IDebugDocument_INTERFACE_DEFINED__
#define __IDebugDocument_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocument, 0x51973c21, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c21-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocument : public IDebugDocumentInfo
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocument, 0x51973c21, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocument *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocument *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocument *This);

    /*** IDebugDocumentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugDocument *This,
        DOCUMENTNAMETYPE dnt,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetDocumentClassId)(
        IDebugDocument *This,
        CLSID *pclsidDocument);

    END_INTERFACE
} IDebugDocumentVtbl;

interface IDebugDocument {
    CONST_VTBL IDebugDocumentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocument_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentInfo methods ***/
#define IDebugDocument_GetName(This,dnt,pbstrName) (This)->lpVtbl->GetName(This,dnt,pbstrName)
#define IDebugDocument_GetDocumentClassId(This,pclsidDocument) (This)->lpVtbl->GetDocumentClassId(This,pclsidDocument)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocument_QueryInterface(IDebugDocument* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocument_AddRef(IDebugDocument* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocument_Release(IDebugDocument* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentInfo methods ***/
static inline HRESULT IDebugDocument_GetName(IDebugDocument* This,DOCUMENTNAMETYPE dnt,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,dnt,pbstrName);
}
static inline HRESULT IDebugDocument_GetDocumentClassId(IDebugDocument* This,CLSID *pclsidDocument) {
    return This->lpVtbl->GetDocumentClassId(This,pclsidDocument);
}
#endif
#endif

#endif


#endif  /* __IDebugDocument_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentText interface
 */
#ifndef __IDebugDocumentText_INTERFACE_DEFINED__
#define __IDebugDocumentText_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentText, 0x51973c22, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c22-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentText : public IDebugDocument
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentAttributes(
        TEXT_DOC_ATTR *ptextdocattr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSize(
        ULONG *pcNumLines,
        ULONG *pcNumChars) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPositionOfLine(
        ULONG cLineNumber,
        ULONG *pcCharacterPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLineOfPosition(
        ULONG cCharacterPosition,
        ULONG *pcLineNumber,
        ULONG *pcCharacterOffsetInLine) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetText(
        ULONG cCharacterPosition,
        WCHAR *pcharText,
        SOURCE_TEXT_ATTR *pstaTextAttr,
        ULONG *pcNumChars,
        ULONG cMaxChars) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPositionOfContext(
        IDebugDocumentContext *psc,
        ULONG *pcCharacterPosition,
        ULONG *cNumChars) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContextOfPosition(
        ULONG cCharacterPosition,
        ULONG cNumChars,
        IDebugDocumentContext **ppsc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentText, 0x51973c22, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentTextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentText *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentText *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentText *This);

    /*** IDebugDocumentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugDocumentText *This,
        DOCUMENTNAMETYPE dnt,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetDocumentClassId)(
        IDebugDocumentText *This,
        CLSID *pclsidDocument);

    /*** IDebugDocumentText methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentAttributes)(
        IDebugDocumentText *This,
        TEXT_DOC_ATTR *ptextdocattr);

    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IDebugDocumentText *This,
        ULONG *pcNumLines,
        ULONG *pcNumChars);

    HRESULT (STDMETHODCALLTYPE *GetPositionOfLine)(
        IDebugDocumentText *This,
        ULONG cLineNumber,
        ULONG *pcCharacterPosition);

    HRESULT (STDMETHODCALLTYPE *GetLineOfPosition)(
        IDebugDocumentText *This,
        ULONG cCharacterPosition,
        ULONG *pcLineNumber,
        ULONG *pcCharacterOffsetInLine);

    HRESULT (STDMETHODCALLTYPE *GetText)(
        IDebugDocumentText *This,
        ULONG cCharacterPosition,
        WCHAR *pcharText,
        SOURCE_TEXT_ATTR *pstaTextAttr,
        ULONG *pcNumChars,
        ULONG cMaxChars);

    HRESULT (STDMETHODCALLTYPE *GetPositionOfContext)(
        IDebugDocumentText *This,
        IDebugDocumentContext *psc,
        ULONG *pcCharacterPosition,
        ULONG *cNumChars);

    HRESULT (STDMETHODCALLTYPE *GetContextOfPosition)(
        IDebugDocumentText *This,
        ULONG cCharacterPosition,
        ULONG cNumChars,
        IDebugDocumentContext **ppsc);

    END_INTERFACE
} IDebugDocumentTextVtbl;

interface IDebugDocumentText {
    CONST_VTBL IDebugDocumentTextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentText_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentText_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentText_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentInfo methods ***/
#define IDebugDocumentText_GetName(This,dnt,pbstrName) (This)->lpVtbl->GetName(This,dnt,pbstrName)
#define IDebugDocumentText_GetDocumentClassId(This,pclsidDocument) (This)->lpVtbl->GetDocumentClassId(This,pclsidDocument)
/*** IDebugDocumentText methods ***/
#define IDebugDocumentText_GetDocumentAttributes(This,ptextdocattr) (This)->lpVtbl->GetDocumentAttributes(This,ptextdocattr)
#define IDebugDocumentText_GetSize(This,pcNumLines,pcNumChars) (This)->lpVtbl->GetSize(This,pcNumLines,pcNumChars)
#define IDebugDocumentText_GetPositionOfLine(This,cLineNumber,pcCharacterPosition) (This)->lpVtbl->GetPositionOfLine(This,cLineNumber,pcCharacterPosition)
#define IDebugDocumentText_GetLineOfPosition(This,cCharacterPosition,pcLineNumber,pcCharacterOffsetInLine) (This)->lpVtbl->GetLineOfPosition(This,cCharacterPosition,pcLineNumber,pcCharacterOffsetInLine)
#define IDebugDocumentText_GetText(This,cCharacterPosition,pcharText,pstaTextAttr,pcNumChars,cMaxChars) (This)->lpVtbl->GetText(This,cCharacterPosition,pcharText,pstaTextAttr,pcNumChars,cMaxChars)
#define IDebugDocumentText_GetPositionOfContext(This,psc,pcCharacterPosition,cNumChars) (This)->lpVtbl->GetPositionOfContext(This,psc,pcCharacterPosition,cNumChars)
#define IDebugDocumentText_GetContextOfPosition(This,cCharacterPosition,cNumChars,ppsc) (This)->lpVtbl->GetContextOfPosition(This,cCharacterPosition,cNumChars,ppsc)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentText_QueryInterface(IDebugDocumentText* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentText_AddRef(IDebugDocumentText* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentText_Release(IDebugDocumentText* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentInfo methods ***/
static inline HRESULT IDebugDocumentText_GetName(IDebugDocumentText* This,DOCUMENTNAMETYPE dnt,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,dnt,pbstrName);
}
static inline HRESULT IDebugDocumentText_GetDocumentClassId(IDebugDocumentText* This,CLSID *pclsidDocument) {
    return This->lpVtbl->GetDocumentClassId(This,pclsidDocument);
}
/*** IDebugDocumentText methods ***/
static inline HRESULT IDebugDocumentText_GetDocumentAttributes(IDebugDocumentText* This,TEXT_DOC_ATTR *ptextdocattr) {
    return This->lpVtbl->GetDocumentAttributes(This,ptextdocattr);
}
static inline HRESULT IDebugDocumentText_GetSize(IDebugDocumentText* This,ULONG *pcNumLines,ULONG *pcNumChars) {
    return This->lpVtbl->GetSize(This,pcNumLines,pcNumChars);
}
static inline HRESULT IDebugDocumentText_GetPositionOfLine(IDebugDocumentText* This,ULONG cLineNumber,ULONG *pcCharacterPosition) {
    return This->lpVtbl->GetPositionOfLine(This,cLineNumber,pcCharacterPosition);
}
static inline HRESULT IDebugDocumentText_GetLineOfPosition(IDebugDocumentText* This,ULONG cCharacterPosition,ULONG *pcLineNumber,ULONG *pcCharacterOffsetInLine) {
    return This->lpVtbl->GetLineOfPosition(This,cCharacterPosition,pcLineNumber,pcCharacterOffsetInLine);
}
static inline HRESULT IDebugDocumentText_GetText(IDebugDocumentText* This,ULONG cCharacterPosition,WCHAR *pcharText,SOURCE_TEXT_ATTR *pstaTextAttr,ULONG *pcNumChars,ULONG cMaxChars) {
    return This->lpVtbl->GetText(This,cCharacterPosition,pcharText,pstaTextAttr,pcNumChars,cMaxChars);
}
static inline HRESULT IDebugDocumentText_GetPositionOfContext(IDebugDocumentText* This,IDebugDocumentContext *psc,ULONG *pcCharacterPosition,ULONG *cNumChars) {
    return This->lpVtbl->GetPositionOfContext(This,psc,pcCharacterPosition,cNumChars);
}
static inline HRESULT IDebugDocumentText_GetContextOfPosition(IDebugDocumentText* This,ULONG cCharacterPosition,ULONG cNumChars,IDebugDocumentContext **ppsc) {
    return This->lpVtbl->GetContextOfPosition(This,cCharacterPosition,cNumChars,ppsc);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentText_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentTextEvents interface
 */
#ifndef __IDebugDocumentTextEvents_INTERFACE_DEFINED__
#define __IDebugDocumentTextEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentTextEvents, 0x51973c23, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c23-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentTextEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE onDestroy(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE onInsertText(
        ULONG cCharacterPosition,
        ULONG cNumToInsert) = 0;

    virtual HRESULT STDMETHODCALLTYPE onRemoveText(
        ULONG cCharacterPosition,
        ULONG cNumToRemove) = 0;

    virtual HRESULT STDMETHODCALLTYPE onReplaceText(
        ULONG cCharacterPosition,
        ULONG cNumToReplace) = 0;

    virtual HRESULT STDMETHODCALLTYPE onUpdateTextAttributes(
        ULONG cCharacterPosition,
        ULONG cNumToUpdate) = 0;

    virtual HRESULT STDMETHODCALLTYPE onUpdateDocumentAttributes(
        TEXT_DOC_ATTR textdocattr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentTextEvents, 0x51973c23, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentTextEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentTextEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentTextEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentTextEvents *This);

    /*** IDebugDocumentTextEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *onDestroy)(
        IDebugDocumentTextEvents *This);

    HRESULT (STDMETHODCALLTYPE *onInsertText)(
        IDebugDocumentTextEvents *This,
        ULONG cCharacterPosition,
        ULONG cNumToInsert);

    HRESULT (STDMETHODCALLTYPE *onRemoveText)(
        IDebugDocumentTextEvents *This,
        ULONG cCharacterPosition,
        ULONG cNumToRemove);

    HRESULT (STDMETHODCALLTYPE *onReplaceText)(
        IDebugDocumentTextEvents *This,
        ULONG cCharacterPosition,
        ULONG cNumToReplace);

    HRESULT (STDMETHODCALLTYPE *onUpdateTextAttributes)(
        IDebugDocumentTextEvents *This,
        ULONG cCharacterPosition,
        ULONG cNumToUpdate);

    HRESULT (STDMETHODCALLTYPE *onUpdateDocumentAttributes)(
        IDebugDocumentTextEvents *This,
        TEXT_DOC_ATTR textdocattr);

    END_INTERFACE
} IDebugDocumentTextEventsVtbl;

interface IDebugDocumentTextEvents {
    CONST_VTBL IDebugDocumentTextEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentTextEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentTextEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentTextEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentTextEvents methods ***/
#define IDebugDocumentTextEvents_onDestroy(This) (This)->lpVtbl->onDestroy(This)
#define IDebugDocumentTextEvents_onInsertText(This,cCharacterPosition,cNumToInsert) (This)->lpVtbl->onInsertText(This,cCharacterPosition,cNumToInsert)
#define IDebugDocumentTextEvents_onRemoveText(This,cCharacterPosition,cNumToRemove) (This)->lpVtbl->onRemoveText(This,cCharacterPosition,cNumToRemove)
#define IDebugDocumentTextEvents_onReplaceText(This,cCharacterPosition,cNumToReplace) (This)->lpVtbl->onReplaceText(This,cCharacterPosition,cNumToReplace)
#define IDebugDocumentTextEvents_onUpdateTextAttributes(This,cCharacterPosition,cNumToUpdate) (This)->lpVtbl->onUpdateTextAttributes(This,cCharacterPosition,cNumToUpdate)
#define IDebugDocumentTextEvents_onUpdateDocumentAttributes(This,textdocattr) (This)->lpVtbl->onUpdateDocumentAttributes(This,textdocattr)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentTextEvents_QueryInterface(IDebugDocumentTextEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentTextEvents_AddRef(IDebugDocumentTextEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentTextEvents_Release(IDebugDocumentTextEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentTextEvents methods ***/
static inline HRESULT IDebugDocumentTextEvents_onDestroy(IDebugDocumentTextEvents* This) {
    return This->lpVtbl->onDestroy(This);
}
static inline HRESULT IDebugDocumentTextEvents_onInsertText(IDebugDocumentTextEvents* This,ULONG cCharacterPosition,ULONG cNumToInsert) {
    return This->lpVtbl->onInsertText(This,cCharacterPosition,cNumToInsert);
}
static inline HRESULT IDebugDocumentTextEvents_onRemoveText(IDebugDocumentTextEvents* This,ULONG cCharacterPosition,ULONG cNumToRemove) {
    return This->lpVtbl->onRemoveText(This,cCharacterPosition,cNumToRemove);
}
static inline HRESULT IDebugDocumentTextEvents_onReplaceText(IDebugDocumentTextEvents* This,ULONG cCharacterPosition,ULONG cNumToReplace) {
    return This->lpVtbl->onReplaceText(This,cCharacterPosition,cNumToReplace);
}
static inline HRESULT IDebugDocumentTextEvents_onUpdateTextAttributes(IDebugDocumentTextEvents* This,ULONG cCharacterPosition,ULONG cNumToUpdate) {
    return This->lpVtbl->onUpdateTextAttributes(This,cCharacterPosition,cNumToUpdate);
}
static inline HRESULT IDebugDocumentTextEvents_onUpdateDocumentAttributes(IDebugDocumentTextEvents* This,TEXT_DOC_ATTR textdocattr) {
    return This->lpVtbl->onUpdateDocumentAttributes(This,textdocattr);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentTextEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentTextAuthor interface
 */
#ifndef __IDebugDocumentTextAuthor_INTERFACE_DEFINED__
#define __IDebugDocumentTextAuthor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentTextAuthor, 0x51973c24, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c24-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentTextAuthor : public IDebugDocumentText
{
    virtual HRESULT STDMETHODCALLTYPE InsertText(
        ULONG cCharacterPosition,
        ULONG cNumToInsert,
        OLECHAR pcharText[]) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveText(
        ULONG cCharacterPosition,
        ULONG cNumToRemove) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReplaceText(
        ULONG cCharacterPosition,
        ULONG cNumToReplace,
        OLECHAR pcharText[]) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentTextAuthor, 0x51973c24, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentTextAuthorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentTextAuthor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentTextAuthor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentTextAuthor *This);

    /*** IDebugDocumentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugDocumentTextAuthor *This,
        DOCUMENTNAMETYPE dnt,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetDocumentClassId)(
        IDebugDocumentTextAuthor *This,
        CLSID *pclsidDocument);

    /*** IDebugDocumentText methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentAttributes)(
        IDebugDocumentTextAuthor *This,
        TEXT_DOC_ATTR *ptextdocattr);

    HRESULT (STDMETHODCALLTYPE *GetSize)(
        IDebugDocumentTextAuthor *This,
        ULONG *pcNumLines,
        ULONG *pcNumChars);

    HRESULT (STDMETHODCALLTYPE *GetPositionOfLine)(
        IDebugDocumentTextAuthor *This,
        ULONG cLineNumber,
        ULONG *pcCharacterPosition);

    HRESULT (STDMETHODCALLTYPE *GetLineOfPosition)(
        IDebugDocumentTextAuthor *This,
        ULONG cCharacterPosition,
        ULONG *pcLineNumber,
        ULONG *pcCharacterOffsetInLine);

    HRESULT (STDMETHODCALLTYPE *GetText)(
        IDebugDocumentTextAuthor *This,
        ULONG cCharacterPosition,
        WCHAR *pcharText,
        SOURCE_TEXT_ATTR *pstaTextAttr,
        ULONG *pcNumChars,
        ULONG cMaxChars);

    HRESULT (STDMETHODCALLTYPE *GetPositionOfContext)(
        IDebugDocumentTextAuthor *This,
        IDebugDocumentContext *psc,
        ULONG *pcCharacterPosition,
        ULONG *cNumChars);

    HRESULT (STDMETHODCALLTYPE *GetContextOfPosition)(
        IDebugDocumentTextAuthor *This,
        ULONG cCharacterPosition,
        ULONG cNumChars,
        IDebugDocumentContext **ppsc);

    /*** IDebugDocumentTextAuthor methods ***/
    HRESULT (STDMETHODCALLTYPE *InsertText)(
        IDebugDocumentTextAuthor *This,
        ULONG cCharacterPosition,
        ULONG cNumToInsert,
        OLECHAR pcharText[]);

    HRESULT (STDMETHODCALLTYPE *RemoveText)(
        IDebugDocumentTextAuthor *This,
        ULONG cCharacterPosition,
        ULONG cNumToRemove);

    HRESULT (STDMETHODCALLTYPE *ReplaceText)(
        IDebugDocumentTextAuthor *This,
        ULONG cCharacterPosition,
        ULONG cNumToReplace,
        OLECHAR pcharText[]);

    END_INTERFACE
} IDebugDocumentTextAuthorVtbl;

interface IDebugDocumentTextAuthor {
    CONST_VTBL IDebugDocumentTextAuthorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentTextAuthor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentTextAuthor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentTextAuthor_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentInfo methods ***/
#define IDebugDocumentTextAuthor_GetName(This,dnt,pbstrName) (This)->lpVtbl->GetName(This,dnt,pbstrName)
#define IDebugDocumentTextAuthor_GetDocumentClassId(This,pclsidDocument) (This)->lpVtbl->GetDocumentClassId(This,pclsidDocument)
/*** IDebugDocumentText methods ***/
#define IDebugDocumentTextAuthor_GetDocumentAttributes(This,ptextdocattr) (This)->lpVtbl->GetDocumentAttributes(This,ptextdocattr)
#define IDebugDocumentTextAuthor_GetSize(This,pcNumLines,pcNumChars) (This)->lpVtbl->GetSize(This,pcNumLines,pcNumChars)
#define IDebugDocumentTextAuthor_GetPositionOfLine(This,cLineNumber,pcCharacterPosition) (This)->lpVtbl->GetPositionOfLine(This,cLineNumber,pcCharacterPosition)
#define IDebugDocumentTextAuthor_GetLineOfPosition(This,cCharacterPosition,pcLineNumber,pcCharacterOffsetInLine) (This)->lpVtbl->GetLineOfPosition(This,cCharacterPosition,pcLineNumber,pcCharacterOffsetInLine)
#define IDebugDocumentTextAuthor_GetText(This,cCharacterPosition,pcharText,pstaTextAttr,pcNumChars,cMaxChars) (This)->lpVtbl->GetText(This,cCharacterPosition,pcharText,pstaTextAttr,pcNumChars,cMaxChars)
#define IDebugDocumentTextAuthor_GetPositionOfContext(This,psc,pcCharacterPosition,cNumChars) (This)->lpVtbl->GetPositionOfContext(This,psc,pcCharacterPosition,cNumChars)
#define IDebugDocumentTextAuthor_GetContextOfPosition(This,cCharacterPosition,cNumChars,ppsc) (This)->lpVtbl->GetContextOfPosition(This,cCharacterPosition,cNumChars,ppsc)
/*** IDebugDocumentTextAuthor methods ***/
#define IDebugDocumentTextAuthor_InsertText(This,cCharacterPosition,cNumToInsert,pcharText) (This)->lpVtbl->InsertText(This,cCharacterPosition,cNumToInsert,pcharText)
#define IDebugDocumentTextAuthor_RemoveText(This,cCharacterPosition,cNumToRemove) (This)->lpVtbl->RemoveText(This,cCharacterPosition,cNumToRemove)
#define IDebugDocumentTextAuthor_ReplaceText(This,cCharacterPosition,cNumToReplace,pcharText) (This)->lpVtbl->ReplaceText(This,cCharacterPosition,cNumToReplace,pcharText)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentTextAuthor_QueryInterface(IDebugDocumentTextAuthor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentTextAuthor_AddRef(IDebugDocumentTextAuthor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentTextAuthor_Release(IDebugDocumentTextAuthor* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentInfo methods ***/
static inline HRESULT IDebugDocumentTextAuthor_GetName(IDebugDocumentTextAuthor* This,DOCUMENTNAMETYPE dnt,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,dnt,pbstrName);
}
static inline HRESULT IDebugDocumentTextAuthor_GetDocumentClassId(IDebugDocumentTextAuthor* This,CLSID *pclsidDocument) {
    return This->lpVtbl->GetDocumentClassId(This,pclsidDocument);
}
/*** IDebugDocumentText methods ***/
static inline HRESULT IDebugDocumentTextAuthor_GetDocumentAttributes(IDebugDocumentTextAuthor* This,TEXT_DOC_ATTR *ptextdocattr) {
    return This->lpVtbl->GetDocumentAttributes(This,ptextdocattr);
}
static inline HRESULT IDebugDocumentTextAuthor_GetSize(IDebugDocumentTextAuthor* This,ULONG *pcNumLines,ULONG *pcNumChars) {
    return This->lpVtbl->GetSize(This,pcNumLines,pcNumChars);
}
static inline HRESULT IDebugDocumentTextAuthor_GetPositionOfLine(IDebugDocumentTextAuthor* This,ULONG cLineNumber,ULONG *pcCharacterPosition) {
    return This->lpVtbl->GetPositionOfLine(This,cLineNumber,pcCharacterPosition);
}
static inline HRESULT IDebugDocumentTextAuthor_GetLineOfPosition(IDebugDocumentTextAuthor* This,ULONG cCharacterPosition,ULONG *pcLineNumber,ULONG *pcCharacterOffsetInLine) {
    return This->lpVtbl->GetLineOfPosition(This,cCharacterPosition,pcLineNumber,pcCharacterOffsetInLine);
}
static inline HRESULT IDebugDocumentTextAuthor_GetText(IDebugDocumentTextAuthor* This,ULONG cCharacterPosition,WCHAR *pcharText,SOURCE_TEXT_ATTR *pstaTextAttr,ULONG *pcNumChars,ULONG cMaxChars) {
    return This->lpVtbl->GetText(This,cCharacterPosition,pcharText,pstaTextAttr,pcNumChars,cMaxChars);
}
static inline HRESULT IDebugDocumentTextAuthor_GetPositionOfContext(IDebugDocumentTextAuthor* This,IDebugDocumentContext *psc,ULONG *pcCharacterPosition,ULONG *cNumChars) {
    return This->lpVtbl->GetPositionOfContext(This,psc,pcCharacterPosition,cNumChars);
}
static inline HRESULT IDebugDocumentTextAuthor_GetContextOfPosition(IDebugDocumentTextAuthor* This,ULONG cCharacterPosition,ULONG cNumChars,IDebugDocumentContext **ppsc) {
    return This->lpVtbl->GetContextOfPosition(This,cCharacterPosition,cNumChars,ppsc);
}
/*** IDebugDocumentTextAuthor methods ***/
static inline HRESULT IDebugDocumentTextAuthor_InsertText(IDebugDocumentTextAuthor* This,ULONG cCharacterPosition,ULONG cNumToInsert,OLECHAR pcharText[]) {
    return This->lpVtbl->InsertText(This,cCharacterPosition,cNumToInsert,pcharText);
}
static inline HRESULT IDebugDocumentTextAuthor_RemoveText(IDebugDocumentTextAuthor* This,ULONG cCharacterPosition,ULONG cNumToRemove) {
    return This->lpVtbl->RemoveText(This,cCharacterPosition,cNumToRemove);
}
static inline HRESULT IDebugDocumentTextAuthor_ReplaceText(IDebugDocumentTextAuthor* This,ULONG cCharacterPosition,ULONG cNumToReplace,OLECHAR pcharText[]) {
    return This->lpVtbl->ReplaceText(This,cCharacterPosition,cNumToReplace,pcharText);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentTextAuthor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentTextExternalAuthor interface
 */
#ifndef __IDebugDocumentTextExternalAuthor_INTERFACE_DEFINED__
#define __IDebugDocumentTextExternalAuthor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentTextExternalAuthor, 0x51973c25, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c25-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentTextExternalAuthor : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPathName(
        BSTR *pbstrLongName,
        WINBOOL *pfIsOriginalFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileName(
        BSTR *pbstrShortName) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyChanged(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentTextExternalAuthor, 0x51973c25, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentTextExternalAuthorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentTextExternalAuthor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentTextExternalAuthor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentTextExternalAuthor *This);

    /*** IDebugDocumentTextExternalAuthor methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPathName)(
        IDebugDocumentTextExternalAuthor *This,
        BSTR *pbstrLongName,
        WINBOOL *pfIsOriginalFile);

    HRESULT (STDMETHODCALLTYPE *GetFileName)(
        IDebugDocumentTextExternalAuthor *This,
        BSTR *pbstrShortName);

    HRESULT (STDMETHODCALLTYPE *NotifyChanged)(
        IDebugDocumentTextExternalAuthor *This);

    END_INTERFACE
} IDebugDocumentTextExternalAuthorVtbl;

interface IDebugDocumentTextExternalAuthor {
    CONST_VTBL IDebugDocumentTextExternalAuthorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentTextExternalAuthor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentTextExternalAuthor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentTextExternalAuthor_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentTextExternalAuthor methods ***/
#define IDebugDocumentTextExternalAuthor_GetPathName(This,pbstrLongName,pfIsOriginalFile) (This)->lpVtbl->GetPathName(This,pbstrLongName,pfIsOriginalFile)
#define IDebugDocumentTextExternalAuthor_GetFileName(This,pbstrShortName) (This)->lpVtbl->GetFileName(This,pbstrShortName)
#define IDebugDocumentTextExternalAuthor_NotifyChanged(This) (This)->lpVtbl->NotifyChanged(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentTextExternalAuthor_QueryInterface(IDebugDocumentTextExternalAuthor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentTextExternalAuthor_AddRef(IDebugDocumentTextExternalAuthor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentTextExternalAuthor_Release(IDebugDocumentTextExternalAuthor* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentTextExternalAuthor methods ***/
static inline HRESULT IDebugDocumentTextExternalAuthor_GetPathName(IDebugDocumentTextExternalAuthor* This,BSTR *pbstrLongName,WINBOOL *pfIsOriginalFile) {
    return This->lpVtbl->GetPathName(This,pbstrLongName,pfIsOriginalFile);
}
static inline HRESULT IDebugDocumentTextExternalAuthor_GetFileName(IDebugDocumentTextExternalAuthor* This,BSTR *pbstrShortName) {
    return This->lpVtbl->GetFileName(This,pbstrShortName);
}
static inline HRESULT IDebugDocumentTextExternalAuthor_NotifyChanged(IDebugDocumentTextExternalAuthor* This) {
    return This->lpVtbl->NotifyChanged(This);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentTextExternalAuthor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentHelper32 interface
 */
#ifndef __IDebugDocumentHelper32_INTERFACE_DEFINED__
#define __IDebugDocumentHelper32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentHelper32, 0x51973c26, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c26-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentHelper32 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Init(
        IDebugApplication32 *pda,
        LPCOLESTR pszShortName,
        LPCOLESTR pszLongName,
        TEXT_DOC_ATTR docAttr) = 0;

    virtual HRESULT STDMETHODCALLTYPE Attach(
        IDebugDocumentHelper32 *pddhParent) = 0;

    virtual HRESULT STDMETHODCALLTYPE Detach(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddUnicodeText(
        LPCOLESTR pszText) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDBCSText(
        LPCSTR pszText) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDebugDocumentHost(
        IDebugDocumentHost *pddh) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDeferredText(
        ULONG cChars,
        DWORD dwTextStartCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE DefineScriptBlock(
        ULONG ulCharOffset,
        ULONG cChars,
        IActiveScript *pas,
        WINBOOL fScriptlet,
        DWORD *pdwSourceContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultTextAttr(
        SOURCE_TEXT_ATTR staTextAttr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTextAttributes(
        ULONG ulCharOffset,
        ULONG cChars,
        SOURCE_TEXT_ATTR *pstaTextAttr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLongName(
        LPCOLESTR pszLongName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetShortName(
        LPCOLESTR pszShortName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDocumentAttr(
        TEXT_DOC_ATTR pszAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDebugApplicationNode(
        IDebugApplicationNode **ppdan) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptBlockInfo(
        DWORD dwSourceContext,
        IActiveScript **ppasd,
        ULONG *piCharPos,
        ULONG *pcChars) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDebugDocumentContext(
        ULONG iCharPos,
        ULONG cChars,
        IDebugDocumentContext **ppddc) = 0;

    virtual HRESULT STDMETHODCALLTYPE BringDocumentToTop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE BringDocumentContextToTop(
        IDebugDocumentContext *pddc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentHelper32, 0x51973c26, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentHelper32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentHelper32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentHelper32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentHelper32 *This);

    /*** IDebugDocumentHelper32 methods ***/
    HRESULT (STDMETHODCALLTYPE *Init)(
        IDebugDocumentHelper32 *This,
        IDebugApplication32 *pda,
        LPCOLESTR pszShortName,
        LPCOLESTR pszLongName,
        TEXT_DOC_ATTR docAttr);

    HRESULT (STDMETHODCALLTYPE *Attach)(
        IDebugDocumentHelper32 *This,
        IDebugDocumentHelper32 *pddhParent);

    HRESULT (STDMETHODCALLTYPE *Detach)(
        IDebugDocumentHelper32 *This);

    HRESULT (STDMETHODCALLTYPE *AddUnicodeText)(
        IDebugDocumentHelper32 *This,
        LPCOLESTR pszText);

    HRESULT (STDMETHODCALLTYPE *AddDBCSText)(
        IDebugDocumentHelper32 *This,
        LPCSTR pszText);

    HRESULT (STDMETHODCALLTYPE *SetDebugDocumentHost)(
        IDebugDocumentHelper32 *This,
        IDebugDocumentHost *pddh);

    HRESULT (STDMETHODCALLTYPE *AddDeferredText)(
        IDebugDocumentHelper32 *This,
        ULONG cChars,
        DWORD dwTextStartCookie);

    HRESULT (STDMETHODCALLTYPE *DefineScriptBlock)(
        IDebugDocumentHelper32 *This,
        ULONG ulCharOffset,
        ULONG cChars,
        IActiveScript *pas,
        WINBOOL fScriptlet,
        DWORD *pdwSourceContext);

    HRESULT (STDMETHODCALLTYPE *SetDefaultTextAttr)(
        IDebugDocumentHelper32 *This,
        SOURCE_TEXT_ATTR staTextAttr);

    HRESULT (STDMETHODCALLTYPE *SetTextAttributes)(
        IDebugDocumentHelper32 *This,
        ULONG ulCharOffset,
        ULONG cChars,
        SOURCE_TEXT_ATTR *pstaTextAttr);

    HRESULT (STDMETHODCALLTYPE *SetLongName)(
        IDebugDocumentHelper32 *This,
        LPCOLESTR pszLongName);

    HRESULT (STDMETHODCALLTYPE *SetShortName)(
        IDebugDocumentHelper32 *This,
        LPCOLESTR pszShortName);

    HRESULT (STDMETHODCALLTYPE *SetDocumentAttr)(
        IDebugDocumentHelper32 *This,
        TEXT_DOC_ATTR pszAttributes);

    HRESULT (STDMETHODCALLTYPE *GetDebugApplicationNode)(
        IDebugDocumentHelper32 *This,
        IDebugApplicationNode **ppdan);

    HRESULT (STDMETHODCALLTYPE *GetScriptBlockInfo)(
        IDebugDocumentHelper32 *This,
        DWORD dwSourceContext,
        IActiveScript **ppasd,
        ULONG *piCharPos,
        ULONG *pcChars);

    HRESULT (STDMETHODCALLTYPE *CreateDebugDocumentContext)(
        IDebugDocumentHelper32 *This,
        ULONG iCharPos,
        ULONG cChars,
        IDebugDocumentContext **ppddc);

    HRESULT (STDMETHODCALLTYPE *BringDocumentToTop)(
        IDebugDocumentHelper32 *This);

    HRESULT (STDMETHODCALLTYPE *BringDocumentContextToTop)(
        IDebugDocumentHelper32 *This,
        IDebugDocumentContext *pddc);

    END_INTERFACE
} IDebugDocumentHelper32Vtbl;

interface IDebugDocumentHelper32 {
    CONST_VTBL IDebugDocumentHelper32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentHelper32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentHelper32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentHelper32_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentHelper32 methods ***/
#define IDebugDocumentHelper32_Init(This,pda,pszShortName,pszLongName,docAttr) (This)->lpVtbl->Init(This,pda,pszShortName,pszLongName,docAttr)
#define IDebugDocumentHelper32_Attach(This,pddhParent) (This)->lpVtbl->Attach(This,pddhParent)
#define IDebugDocumentHelper32_Detach(This) (This)->lpVtbl->Detach(This)
#define IDebugDocumentHelper32_AddUnicodeText(This,pszText) (This)->lpVtbl->AddUnicodeText(This,pszText)
#define IDebugDocumentHelper32_AddDBCSText(This,pszText) (This)->lpVtbl->AddDBCSText(This,pszText)
#define IDebugDocumentHelper32_SetDebugDocumentHost(This,pddh) (This)->lpVtbl->SetDebugDocumentHost(This,pddh)
#define IDebugDocumentHelper32_AddDeferredText(This,cChars,dwTextStartCookie) (This)->lpVtbl->AddDeferredText(This,cChars,dwTextStartCookie)
#define IDebugDocumentHelper32_DefineScriptBlock(This,ulCharOffset,cChars,pas,fScriptlet,pdwSourceContext) (This)->lpVtbl->DefineScriptBlock(This,ulCharOffset,cChars,pas,fScriptlet,pdwSourceContext)
#define IDebugDocumentHelper32_SetDefaultTextAttr(This,staTextAttr) (This)->lpVtbl->SetDefaultTextAttr(This,staTextAttr)
#define IDebugDocumentHelper32_SetTextAttributes(This,ulCharOffset,cChars,pstaTextAttr) (This)->lpVtbl->SetTextAttributes(This,ulCharOffset,cChars,pstaTextAttr)
#define IDebugDocumentHelper32_SetLongName(This,pszLongName) (This)->lpVtbl->SetLongName(This,pszLongName)
#define IDebugDocumentHelper32_SetShortName(This,pszShortName) (This)->lpVtbl->SetShortName(This,pszShortName)
#define IDebugDocumentHelper32_SetDocumentAttr(This,pszAttributes) (This)->lpVtbl->SetDocumentAttr(This,pszAttributes)
#define IDebugDocumentHelper32_GetDebugApplicationNode(This,ppdan) (This)->lpVtbl->GetDebugApplicationNode(This,ppdan)
#define IDebugDocumentHelper32_GetScriptBlockInfo(This,dwSourceContext,ppasd,piCharPos,pcChars) (This)->lpVtbl->GetScriptBlockInfo(This,dwSourceContext,ppasd,piCharPos,pcChars)
#define IDebugDocumentHelper32_CreateDebugDocumentContext(This,iCharPos,cChars,ppddc) (This)->lpVtbl->CreateDebugDocumentContext(This,iCharPos,cChars,ppddc)
#define IDebugDocumentHelper32_BringDocumentToTop(This) (This)->lpVtbl->BringDocumentToTop(This)
#define IDebugDocumentHelper32_BringDocumentContextToTop(This,pddc) (This)->lpVtbl->BringDocumentContextToTop(This,pddc)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentHelper32_QueryInterface(IDebugDocumentHelper32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentHelper32_AddRef(IDebugDocumentHelper32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentHelper32_Release(IDebugDocumentHelper32* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentHelper32 methods ***/
static inline HRESULT IDebugDocumentHelper32_Init(IDebugDocumentHelper32* This,IDebugApplication32 *pda,LPCOLESTR pszShortName,LPCOLESTR pszLongName,TEXT_DOC_ATTR docAttr) {
    return This->lpVtbl->Init(This,pda,pszShortName,pszLongName,docAttr);
}
static inline HRESULT IDebugDocumentHelper32_Attach(IDebugDocumentHelper32* This,IDebugDocumentHelper32 *pddhParent) {
    return This->lpVtbl->Attach(This,pddhParent);
}
static inline HRESULT IDebugDocumentHelper32_Detach(IDebugDocumentHelper32* This) {
    return This->lpVtbl->Detach(This);
}
static inline HRESULT IDebugDocumentHelper32_AddUnicodeText(IDebugDocumentHelper32* This,LPCOLESTR pszText) {
    return This->lpVtbl->AddUnicodeText(This,pszText);
}
static inline HRESULT IDebugDocumentHelper32_AddDBCSText(IDebugDocumentHelper32* This,LPCSTR pszText) {
    return This->lpVtbl->AddDBCSText(This,pszText);
}
static inline HRESULT IDebugDocumentHelper32_SetDebugDocumentHost(IDebugDocumentHelper32* This,IDebugDocumentHost *pddh) {
    return This->lpVtbl->SetDebugDocumentHost(This,pddh);
}
static inline HRESULT IDebugDocumentHelper32_AddDeferredText(IDebugDocumentHelper32* This,ULONG cChars,DWORD dwTextStartCookie) {
    return This->lpVtbl->AddDeferredText(This,cChars,dwTextStartCookie);
}
static inline HRESULT IDebugDocumentHelper32_DefineScriptBlock(IDebugDocumentHelper32* This,ULONG ulCharOffset,ULONG cChars,IActiveScript *pas,WINBOOL fScriptlet,DWORD *pdwSourceContext) {
    return This->lpVtbl->DefineScriptBlock(This,ulCharOffset,cChars,pas,fScriptlet,pdwSourceContext);
}
static inline HRESULT IDebugDocumentHelper32_SetDefaultTextAttr(IDebugDocumentHelper32* This,SOURCE_TEXT_ATTR staTextAttr) {
    return This->lpVtbl->SetDefaultTextAttr(This,staTextAttr);
}
static inline HRESULT IDebugDocumentHelper32_SetTextAttributes(IDebugDocumentHelper32* This,ULONG ulCharOffset,ULONG cChars,SOURCE_TEXT_ATTR *pstaTextAttr) {
    return This->lpVtbl->SetTextAttributes(This,ulCharOffset,cChars,pstaTextAttr);
}
static inline HRESULT IDebugDocumentHelper32_SetLongName(IDebugDocumentHelper32* This,LPCOLESTR pszLongName) {
    return This->lpVtbl->SetLongName(This,pszLongName);
}
static inline HRESULT IDebugDocumentHelper32_SetShortName(IDebugDocumentHelper32* This,LPCOLESTR pszShortName) {
    return This->lpVtbl->SetShortName(This,pszShortName);
}
static inline HRESULT IDebugDocumentHelper32_SetDocumentAttr(IDebugDocumentHelper32* This,TEXT_DOC_ATTR pszAttributes) {
    return This->lpVtbl->SetDocumentAttr(This,pszAttributes);
}
static inline HRESULT IDebugDocumentHelper32_GetDebugApplicationNode(IDebugDocumentHelper32* This,IDebugApplicationNode **ppdan) {
    return This->lpVtbl->GetDebugApplicationNode(This,ppdan);
}
static inline HRESULT IDebugDocumentHelper32_GetScriptBlockInfo(IDebugDocumentHelper32* This,DWORD dwSourceContext,IActiveScript **ppasd,ULONG *piCharPos,ULONG *pcChars) {
    return This->lpVtbl->GetScriptBlockInfo(This,dwSourceContext,ppasd,piCharPos,pcChars);
}
static inline HRESULT IDebugDocumentHelper32_CreateDebugDocumentContext(IDebugDocumentHelper32* This,ULONG iCharPos,ULONG cChars,IDebugDocumentContext **ppddc) {
    return This->lpVtbl->CreateDebugDocumentContext(This,iCharPos,cChars,ppddc);
}
static inline HRESULT IDebugDocumentHelper32_BringDocumentToTop(IDebugDocumentHelper32* This) {
    return This->lpVtbl->BringDocumentToTop(This);
}
static inline HRESULT IDebugDocumentHelper32_BringDocumentContextToTop(IDebugDocumentHelper32* This,IDebugDocumentContext *pddc) {
    return This->lpVtbl->BringDocumentContextToTop(This,pddc);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentHelper32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentHelper64 interface
 */
#ifndef __IDebugDocumentHelper64_INTERFACE_DEFINED__
#define __IDebugDocumentHelper64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentHelper64, 0xc4c7363c, 0x20fd, 0x47f9, 0xbd,0x82, 0x48,0x55,0xe0,0x15,0x08,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c4c7363c-20fd-47f9-bd82-4855e0150871")
IDebugDocumentHelper64 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Init(
        IDebugApplication64 *pda,
        LPCOLESTR pszShortName,
        LPCOLESTR pszLongName,
        TEXT_DOC_ATTR docAttr) = 0;

    virtual HRESULT STDMETHODCALLTYPE Attach(
        IDebugDocumentHelper64 *pddhParent) = 0;

    virtual HRESULT STDMETHODCALLTYPE Detach(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddUnicodeText(
        LPCOLESTR pszText) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDBCSText(
        LPCSTR pszText) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDebugDocumentHost(
        IDebugDocumentHost *pddh) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddDeferredText(
        ULONG cChars,
        DWORD dwTextStartCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE DefineScriptBlock(
        ULONG ulCharOffset,
        ULONG cChars,
        IActiveScript *pas,
        WINBOOL fScriptlet,
        DWORDLONG *pdwSourceContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultTextAttr(
        SOURCE_TEXT_ATTR staTextAttr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTextAttributes(
        ULONG ulCharOffset,
        ULONG cChars,
        SOURCE_TEXT_ATTR *pstaTextAttr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLongName(
        LPCOLESTR pszLongName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetShortName(
        LPCOLESTR pszShortName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDocumentAttr(
        TEXT_DOC_ATTR pszAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDebugApplicationNode(
        IDebugApplicationNode **ppdan) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptBlockInfo(
        DWORDLONG dwSourceContext,
        IActiveScript **ppasd,
        ULONG *piCharPos,
        ULONG *pcChars) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDebugDocumentContext(
        ULONG iCharPos,
        ULONG cChars,
        IDebugDocumentContext **ppddc) = 0;

    virtual HRESULT STDMETHODCALLTYPE BringDocumentToTop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE BringDocumentContextToTop(
        IDebugDocumentContext *pddc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentHelper64, 0xc4c7363c, 0x20fd, 0x47f9, 0xbd,0x82, 0x48,0x55,0xe0,0x15,0x08,0x71)
#endif
#else
typedef struct IDebugDocumentHelper64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentHelper64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentHelper64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentHelper64 *This);

    /*** IDebugDocumentHelper64 methods ***/
    HRESULT (STDMETHODCALLTYPE *Init)(
        IDebugDocumentHelper64 *This,
        IDebugApplication64 *pda,
        LPCOLESTR pszShortName,
        LPCOLESTR pszLongName,
        TEXT_DOC_ATTR docAttr);

    HRESULT (STDMETHODCALLTYPE *Attach)(
        IDebugDocumentHelper64 *This,
        IDebugDocumentHelper64 *pddhParent);

    HRESULT (STDMETHODCALLTYPE *Detach)(
        IDebugDocumentHelper64 *This);

    HRESULT (STDMETHODCALLTYPE *AddUnicodeText)(
        IDebugDocumentHelper64 *This,
        LPCOLESTR pszText);

    HRESULT (STDMETHODCALLTYPE *AddDBCSText)(
        IDebugDocumentHelper64 *This,
        LPCSTR pszText);

    HRESULT (STDMETHODCALLTYPE *SetDebugDocumentHost)(
        IDebugDocumentHelper64 *This,
        IDebugDocumentHost *pddh);

    HRESULT (STDMETHODCALLTYPE *AddDeferredText)(
        IDebugDocumentHelper64 *This,
        ULONG cChars,
        DWORD dwTextStartCookie);

    HRESULT (STDMETHODCALLTYPE *DefineScriptBlock)(
        IDebugDocumentHelper64 *This,
        ULONG ulCharOffset,
        ULONG cChars,
        IActiveScript *pas,
        WINBOOL fScriptlet,
        DWORDLONG *pdwSourceContext);

    HRESULT (STDMETHODCALLTYPE *SetDefaultTextAttr)(
        IDebugDocumentHelper64 *This,
        SOURCE_TEXT_ATTR staTextAttr);

    HRESULT (STDMETHODCALLTYPE *SetTextAttributes)(
        IDebugDocumentHelper64 *This,
        ULONG ulCharOffset,
        ULONG cChars,
        SOURCE_TEXT_ATTR *pstaTextAttr);

    HRESULT (STDMETHODCALLTYPE *SetLongName)(
        IDebugDocumentHelper64 *This,
        LPCOLESTR pszLongName);

    HRESULT (STDMETHODCALLTYPE *SetShortName)(
        IDebugDocumentHelper64 *This,
        LPCOLESTR pszShortName);

    HRESULT (STDMETHODCALLTYPE *SetDocumentAttr)(
        IDebugDocumentHelper64 *This,
        TEXT_DOC_ATTR pszAttributes);

    HRESULT (STDMETHODCALLTYPE *GetDebugApplicationNode)(
        IDebugDocumentHelper64 *This,
        IDebugApplicationNode **ppdan);

    HRESULT (STDMETHODCALLTYPE *GetScriptBlockInfo)(
        IDebugDocumentHelper64 *This,
        DWORDLONG dwSourceContext,
        IActiveScript **ppasd,
        ULONG *piCharPos,
        ULONG *pcChars);

    HRESULT (STDMETHODCALLTYPE *CreateDebugDocumentContext)(
        IDebugDocumentHelper64 *This,
        ULONG iCharPos,
        ULONG cChars,
        IDebugDocumentContext **ppddc);

    HRESULT (STDMETHODCALLTYPE *BringDocumentToTop)(
        IDebugDocumentHelper64 *This);

    HRESULT (STDMETHODCALLTYPE *BringDocumentContextToTop)(
        IDebugDocumentHelper64 *This,
        IDebugDocumentContext *pddc);

    END_INTERFACE
} IDebugDocumentHelper64Vtbl;

interface IDebugDocumentHelper64 {
    CONST_VTBL IDebugDocumentHelper64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentHelper64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentHelper64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentHelper64_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentHelper64 methods ***/
#define IDebugDocumentHelper64_Init(This,pda,pszShortName,pszLongName,docAttr) (This)->lpVtbl->Init(This,pda,pszShortName,pszLongName,docAttr)
#define IDebugDocumentHelper64_Attach(This,pddhParent) (This)->lpVtbl->Attach(This,pddhParent)
#define IDebugDocumentHelper64_Detach(This) (This)->lpVtbl->Detach(This)
#define IDebugDocumentHelper64_AddUnicodeText(This,pszText) (This)->lpVtbl->AddUnicodeText(This,pszText)
#define IDebugDocumentHelper64_AddDBCSText(This,pszText) (This)->lpVtbl->AddDBCSText(This,pszText)
#define IDebugDocumentHelper64_SetDebugDocumentHost(This,pddh) (This)->lpVtbl->SetDebugDocumentHost(This,pddh)
#define IDebugDocumentHelper64_AddDeferredText(This,cChars,dwTextStartCookie) (This)->lpVtbl->AddDeferredText(This,cChars,dwTextStartCookie)
#define IDebugDocumentHelper64_DefineScriptBlock(This,ulCharOffset,cChars,pas,fScriptlet,pdwSourceContext) (This)->lpVtbl->DefineScriptBlock(This,ulCharOffset,cChars,pas,fScriptlet,pdwSourceContext)
#define IDebugDocumentHelper64_SetDefaultTextAttr(This,staTextAttr) (This)->lpVtbl->SetDefaultTextAttr(This,staTextAttr)
#define IDebugDocumentHelper64_SetTextAttributes(This,ulCharOffset,cChars,pstaTextAttr) (This)->lpVtbl->SetTextAttributes(This,ulCharOffset,cChars,pstaTextAttr)
#define IDebugDocumentHelper64_SetLongName(This,pszLongName) (This)->lpVtbl->SetLongName(This,pszLongName)
#define IDebugDocumentHelper64_SetShortName(This,pszShortName) (This)->lpVtbl->SetShortName(This,pszShortName)
#define IDebugDocumentHelper64_SetDocumentAttr(This,pszAttributes) (This)->lpVtbl->SetDocumentAttr(This,pszAttributes)
#define IDebugDocumentHelper64_GetDebugApplicationNode(This,ppdan) (This)->lpVtbl->GetDebugApplicationNode(This,ppdan)
#define IDebugDocumentHelper64_GetScriptBlockInfo(This,dwSourceContext,ppasd,piCharPos,pcChars) (This)->lpVtbl->GetScriptBlockInfo(This,dwSourceContext,ppasd,piCharPos,pcChars)
#define IDebugDocumentHelper64_CreateDebugDocumentContext(This,iCharPos,cChars,ppddc) (This)->lpVtbl->CreateDebugDocumentContext(This,iCharPos,cChars,ppddc)
#define IDebugDocumentHelper64_BringDocumentToTop(This) (This)->lpVtbl->BringDocumentToTop(This)
#define IDebugDocumentHelper64_BringDocumentContextToTop(This,pddc) (This)->lpVtbl->BringDocumentContextToTop(This,pddc)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentHelper64_QueryInterface(IDebugDocumentHelper64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentHelper64_AddRef(IDebugDocumentHelper64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentHelper64_Release(IDebugDocumentHelper64* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentHelper64 methods ***/
static inline HRESULT IDebugDocumentHelper64_Init(IDebugDocumentHelper64* This,IDebugApplication64 *pda,LPCOLESTR pszShortName,LPCOLESTR pszLongName,TEXT_DOC_ATTR docAttr) {
    return This->lpVtbl->Init(This,pda,pszShortName,pszLongName,docAttr);
}
static inline HRESULT IDebugDocumentHelper64_Attach(IDebugDocumentHelper64* This,IDebugDocumentHelper64 *pddhParent) {
    return This->lpVtbl->Attach(This,pddhParent);
}
static inline HRESULT IDebugDocumentHelper64_Detach(IDebugDocumentHelper64* This) {
    return This->lpVtbl->Detach(This);
}
static inline HRESULT IDebugDocumentHelper64_AddUnicodeText(IDebugDocumentHelper64* This,LPCOLESTR pszText) {
    return This->lpVtbl->AddUnicodeText(This,pszText);
}
static inline HRESULT IDebugDocumentHelper64_AddDBCSText(IDebugDocumentHelper64* This,LPCSTR pszText) {
    return This->lpVtbl->AddDBCSText(This,pszText);
}
static inline HRESULT IDebugDocumentHelper64_SetDebugDocumentHost(IDebugDocumentHelper64* This,IDebugDocumentHost *pddh) {
    return This->lpVtbl->SetDebugDocumentHost(This,pddh);
}
static inline HRESULT IDebugDocumentHelper64_AddDeferredText(IDebugDocumentHelper64* This,ULONG cChars,DWORD dwTextStartCookie) {
    return This->lpVtbl->AddDeferredText(This,cChars,dwTextStartCookie);
}
static inline HRESULT IDebugDocumentHelper64_DefineScriptBlock(IDebugDocumentHelper64* This,ULONG ulCharOffset,ULONG cChars,IActiveScript *pas,WINBOOL fScriptlet,DWORDLONG *pdwSourceContext) {
    return This->lpVtbl->DefineScriptBlock(This,ulCharOffset,cChars,pas,fScriptlet,pdwSourceContext);
}
static inline HRESULT IDebugDocumentHelper64_SetDefaultTextAttr(IDebugDocumentHelper64* This,SOURCE_TEXT_ATTR staTextAttr) {
    return This->lpVtbl->SetDefaultTextAttr(This,staTextAttr);
}
static inline HRESULT IDebugDocumentHelper64_SetTextAttributes(IDebugDocumentHelper64* This,ULONG ulCharOffset,ULONG cChars,SOURCE_TEXT_ATTR *pstaTextAttr) {
    return This->lpVtbl->SetTextAttributes(This,ulCharOffset,cChars,pstaTextAttr);
}
static inline HRESULT IDebugDocumentHelper64_SetLongName(IDebugDocumentHelper64* This,LPCOLESTR pszLongName) {
    return This->lpVtbl->SetLongName(This,pszLongName);
}
static inline HRESULT IDebugDocumentHelper64_SetShortName(IDebugDocumentHelper64* This,LPCOLESTR pszShortName) {
    return This->lpVtbl->SetShortName(This,pszShortName);
}
static inline HRESULT IDebugDocumentHelper64_SetDocumentAttr(IDebugDocumentHelper64* This,TEXT_DOC_ATTR pszAttributes) {
    return This->lpVtbl->SetDocumentAttr(This,pszAttributes);
}
static inline HRESULT IDebugDocumentHelper64_GetDebugApplicationNode(IDebugDocumentHelper64* This,IDebugApplicationNode **ppdan) {
    return This->lpVtbl->GetDebugApplicationNode(This,ppdan);
}
static inline HRESULT IDebugDocumentHelper64_GetScriptBlockInfo(IDebugDocumentHelper64* This,DWORDLONG dwSourceContext,IActiveScript **ppasd,ULONG *piCharPos,ULONG *pcChars) {
    return This->lpVtbl->GetScriptBlockInfo(This,dwSourceContext,ppasd,piCharPos,pcChars);
}
static inline HRESULT IDebugDocumentHelper64_CreateDebugDocumentContext(IDebugDocumentHelper64* This,ULONG iCharPos,ULONG cChars,IDebugDocumentContext **ppddc) {
    return This->lpVtbl->CreateDebugDocumentContext(This,iCharPos,cChars,ppddc);
}
static inline HRESULT IDebugDocumentHelper64_BringDocumentToTop(IDebugDocumentHelper64* This) {
    return This->lpVtbl->BringDocumentToTop(This);
}
static inline HRESULT IDebugDocumentHelper64_BringDocumentContextToTop(IDebugDocumentHelper64* This,IDebugDocumentContext *pddc) {
    return This->lpVtbl->BringDocumentContextToTop(This,pddc);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentHelper64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentHost interface
 */
#ifndef __IDebugDocumentHost_INTERFACE_DEFINED__
#define __IDebugDocumentHost_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentHost, 0x51973c27, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c27-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentHost : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDeferredText(
        DWORD dwTextStartCookie,
        WCHAR *pcharText,
        SOURCE_TEXT_ATTR *pstaTextAttr,
        ULONG *pcNumChars,
        ULONG cMaxChars) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptTextAttributes(
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnCreateDocumentContext(
        IUnknown **ppunkOuter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPathName(
        BSTR *pbstrLongName,
        WINBOOL *pfIsOriginalFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileName(
        BSTR *pbstrShortName) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyChanged(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentHost, 0x51973c27, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentHostVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentHost *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentHost *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentHost *This);

    /*** IDebugDocumentHost methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeferredText)(
        IDebugDocumentHost *This,
        DWORD dwTextStartCookie,
        WCHAR *pcharText,
        SOURCE_TEXT_ATTR *pstaTextAttr,
        ULONG *pcNumChars,
        ULONG cMaxChars);

    HRESULT (STDMETHODCALLTYPE *GetScriptTextAttributes)(
        IDebugDocumentHost *This,
        LPCOLESTR pstrCode,
        ULONG uNumCodeChars,
        LPCOLESTR pstrDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr);

    HRESULT (STDMETHODCALLTYPE *OnCreateDocumentContext)(
        IDebugDocumentHost *This,
        IUnknown **ppunkOuter);

    HRESULT (STDMETHODCALLTYPE *GetPathName)(
        IDebugDocumentHost *This,
        BSTR *pbstrLongName,
        WINBOOL *pfIsOriginalFile);

    HRESULT (STDMETHODCALLTYPE *GetFileName)(
        IDebugDocumentHost *This,
        BSTR *pbstrShortName);

    HRESULT (STDMETHODCALLTYPE *NotifyChanged)(
        IDebugDocumentHost *This);

    END_INTERFACE
} IDebugDocumentHostVtbl;

interface IDebugDocumentHost {
    CONST_VTBL IDebugDocumentHostVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentHost_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentHost_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentHost_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentHost methods ***/
#define IDebugDocumentHost_GetDeferredText(This,dwTextStartCookie,pcharText,pstaTextAttr,pcNumChars,cMaxChars) (This)->lpVtbl->GetDeferredText(This,dwTextStartCookie,pcharText,pstaTextAttr,pcNumChars,cMaxChars)
#define IDebugDocumentHost_GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr) (This)->lpVtbl->GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr)
#define IDebugDocumentHost_OnCreateDocumentContext(This,ppunkOuter) (This)->lpVtbl->OnCreateDocumentContext(This,ppunkOuter)
#define IDebugDocumentHost_GetPathName(This,pbstrLongName,pfIsOriginalFile) (This)->lpVtbl->GetPathName(This,pbstrLongName,pfIsOriginalFile)
#define IDebugDocumentHost_GetFileName(This,pbstrShortName) (This)->lpVtbl->GetFileName(This,pbstrShortName)
#define IDebugDocumentHost_NotifyChanged(This) (This)->lpVtbl->NotifyChanged(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentHost_QueryInterface(IDebugDocumentHost* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentHost_AddRef(IDebugDocumentHost* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentHost_Release(IDebugDocumentHost* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentHost methods ***/
static inline HRESULT IDebugDocumentHost_GetDeferredText(IDebugDocumentHost* This,DWORD dwTextStartCookie,WCHAR *pcharText,SOURCE_TEXT_ATTR *pstaTextAttr,ULONG *pcNumChars,ULONG cMaxChars) {
    return This->lpVtbl->GetDeferredText(This,dwTextStartCookie,pcharText,pstaTextAttr,pcNumChars,cMaxChars);
}
static inline HRESULT IDebugDocumentHost_GetScriptTextAttributes(IDebugDocumentHost* This,LPCOLESTR pstrCode,ULONG uNumCodeChars,LPCOLESTR pstrDelimiter,DWORD dwFlags,SOURCE_TEXT_ATTR *pattr) {
    return This->lpVtbl->GetScriptTextAttributes(This,pstrCode,uNumCodeChars,pstrDelimiter,dwFlags,pattr);
}
static inline HRESULT IDebugDocumentHost_OnCreateDocumentContext(IDebugDocumentHost* This,IUnknown **ppunkOuter) {
    return This->lpVtbl->OnCreateDocumentContext(This,ppunkOuter);
}
static inline HRESULT IDebugDocumentHost_GetPathName(IDebugDocumentHost* This,BSTR *pbstrLongName,WINBOOL *pfIsOriginalFile) {
    return This->lpVtbl->GetPathName(This,pbstrLongName,pfIsOriginalFile);
}
static inline HRESULT IDebugDocumentHost_GetFileName(IDebugDocumentHost* This,BSTR *pbstrShortName) {
    return This->lpVtbl->GetFileName(This,pbstrShortName);
}
static inline HRESULT IDebugDocumentHost_NotifyChanged(IDebugDocumentHost* This) {
    return This->lpVtbl->NotifyChanged(This);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentHost_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugDocumentContext interface
 */
#ifndef __IDebugDocumentContext_INTERFACE_DEFINED__
#define __IDebugDocumentContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugDocumentContext, 0x51973c28, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c28-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugDocumentContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDocument(
        IDebugDocument **ppsd) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumCodeContexts(
        IEnumDebugCodeContexts **ppescc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugDocumentContext, 0x51973c28, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugDocumentContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugDocumentContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugDocumentContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugDocumentContext *This);

    /*** IDebugDocumentContext methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocument)(
        IDebugDocumentContext *This,
        IDebugDocument **ppsd);

    HRESULT (STDMETHODCALLTYPE *EnumCodeContexts)(
        IDebugDocumentContext *This,
        IEnumDebugCodeContexts **ppescc);

    END_INTERFACE
} IDebugDocumentContextVtbl;

interface IDebugDocumentContext {
    CONST_VTBL IDebugDocumentContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugDocumentContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugDocumentContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugDocumentContext_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentContext methods ***/
#define IDebugDocumentContext_GetDocument(This,ppsd) (This)->lpVtbl->GetDocument(This,ppsd)
#define IDebugDocumentContext_EnumCodeContexts(This,ppescc) (This)->lpVtbl->EnumCodeContexts(This,ppescc)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugDocumentContext_QueryInterface(IDebugDocumentContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugDocumentContext_AddRef(IDebugDocumentContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugDocumentContext_Release(IDebugDocumentContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentContext methods ***/
static inline HRESULT IDebugDocumentContext_GetDocument(IDebugDocumentContext* This,IDebugDocument **ppsd) {
    return This->lpVtbl->GetDocument(This,ppsd);
}
static inline HRESULT IDebugDocumentContext_EnumCodeContexts(IDebugDocumentContext* This,IEnumDebugCodeContexts **ppescc) {
    return This->lpVtbl->EnumCodeContexts(This,ppescc);
}
#endif
#endif

#endif


#endif  /* __IDebugDocumentContext_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugSessionProvider interface
 */
#ifndef __IDebugSessionProvider_INTERFACE_DEFINED__
#define __IDebugSessionProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugSessionProvider, 0x51973c29, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c29-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugSessionProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StartDebugSession(
        IRemoteDebugApplication *pda) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugSessionProvider, 0x51973c29, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugSessionProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugSessionProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugSessionProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugSessionProvider *This);

    /*** IDebugSessionProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *StartDebugSession)(
        IDebugSessionProvider *This,
        IRemoteDebugApplication *pda);

    END_INTERFACE
} IDebugSessionProviderVtbl;

interface IDebugSessionProvider {
    CONST_VTBL IDebugSessionProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugSessionProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugSessionProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugSessionProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugSessionProvider methods ***/
#define IDebugSessionProvider_StartDebugSession(This,pda) (This)->lpVtbl->StartDebugSession(This,pda)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugSessionProvider_QueryInterface(IDebugSessionProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugSessionProvider_AddRef(IDebugSessionProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugSessionProvider_Release(IDebugSessionProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugSessionProvider methods ***/
static inline HRESULT IDebugSessionProvider_StartDebugSession(IDebugSessionProvider* This,IRemoteDebugApplication *pda) {
    return This->lpVtbl->StartDebugSession(This,pda);
}
#endif
#endif

#endif


#endif  /* __IDebugSessionProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IApplicationDebugger interface
 */
#ifndef __IApplicationDebugger_INTERFACE_DEFINED__
#define __IApplicationDebugger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IApplicationDebugger, 0x51973c2a, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c2a-cb0c-11d0-b5c9-00a0244a0e7a")
IApplicationDebugger : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryAlive(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstanceAtDebugger(
        REFCLSID rclsid,
        IUnknown *pUnkOuter,
        DWORD dwClsContext,
        REFIID riid,
        IUnknown **ppvObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE onDebugOutput(
        LPCOLESTR pstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE onHandleBreakPoint(
        IRemoteDebugApplicationThread *prpt,
        BREAKREASON br,
        IActiveScriptErrorDebug *pError) = 0;

    virtual HRESULT STDMETHODCALLTYPE onClose(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE onDebuggerEvent(
        REFIID riid,
        IUnknown *punk) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IApplicationDebugger, 0x51973c2a, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IApplicationDebuggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IApplicationDebugger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IApplicationDebugger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IApplicationDebugger *This);

    /*** IApplicationDebugger methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryAlive)(
        IApplicationDebugger *This);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceAtDebugger)(
        IApplicationDebugger *This,
        REFCLSID rclsid,
        IUnknown *pUnkOuter,
        DWORD dwClsContext,
        REFIID riid,
        IUnknown **ppvObject);

    HRESULT (STDMETHODCALLTYPE *onDebugOutput)(
        IApplicationDebugger *This,
        LPCOLESTR pstr);

    HRESULT (STDMETHODCALLTYPE *onHandleBreakPoint)(
        IApplicationDebugger *This,
        IRemoteDebugApplicationThread *prpt,
        BREAKREASON br,
        IActiveScriptErrorDebug *pError);

    HRESULT (STDMETHODCALLTYPE *onClose)(
        IApplicationDebugger *This);

    HRESULT (STDMETHODCALLTYPE *onDebuggerEvent)(
        IApplicationDebugger *This,
        REFIID riid,
        IUnknown *punk);

    END_INTERFACE
} IApplicationDebuggerVtbl;

interface IApplicationDebugger {
    CONST_VTBL IApplicationDebuggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IApplicationDebugger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IApplicationDebugger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IApplicationDebugger_Release(This) (This)->lpVtbl->Release(This)
/*** IApplicationDebugger methods ***/
#define IApplicationDebugger_QueryAlive(This) (This)->lpVtbl->QueryAlive(This)
#define IApplicationDebugger_CreateInstanceAtDebugger(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject) (This)->lpVtbl->CreateInstanceAtDebugger(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject)
#define IApplicationDebugger_onDebugOutput(This,pstr) (This)->lpVtbl->onDebugOutput(This,pstr)
#define IApplicationDebugger_onHandleBreakPoint(This,prpt,br,pError) (This)->lpVtbl->onHandleBreakPoint(This,prpt,br,pError)
#define IApplicationDebugger_onClose(This) (This)->lpVtbl->onClose(This)
#define IApplicationDebugger_onDebuggerEvent(This,riid,punk) (This)->lpVtbl->onDebuggerEvent(This,riid,punk)
#else
/*** IUnknown methods ***/
static inline HRESULT IApplicationDebugger_QueryInterface(IApplicationDebugger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IApplicationDebugger_AddRef(IApplicationDebugger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IApplicationDebugger_Release(IApplicationDebugger* This) {
    return This->lpVtbl->Release(This);
}
/*** IApplicationDebugger methods ***/
static inline HRESULT IApplicationDebugger_QueryAlive(IApplicationDebugger* This) {
    return This->lpVtbl->QueryAlive(This);
}
static inline HRESULT IApplicationDebugger_CreateInstanceAtDebugger(IApplicationDebugger* This,REFCLSID rclsid,IUnknown *pUnkOuter,DWORD dwClsContext,REFIID riid,IUnknown **ppvObject) {
    return This->lpVtbl->CreateInstanceAtDebugger(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject);
}
static inline HRESULT IApplicationDebugger_onDebugOutput(IApplicationDebugger* This,LPCOLESTR pstr) {
    return This->lpVtbl->onDebugOutput(This,pstr);
}
static inline HRESULT IApplicationDebugger_onHandleBreakPoint(IApplicationDebugger* This,IRemoteDebugApplicationThread *prpt,BREAKREASON br,IActiveScriptErrorDebug *pError) {
    return This->lpVtbl->onHandleBreakPoint(This,prpt,br,pError);
}
static inline HRESULT IApplicationDebugger_onClose(IApplicationDebugger* This) {
    return This->lpVtbl->onClose(This);
}
static inline HRESULT IApplicationDebugger_onDebuggerEvent(IApplicationDebugger* This,REFIID riid,IUnknown *punk) {
    return This->lpVtbl->onDebuggerEvent(This,riid,punk);
}
#endif
#endif

#endif


#endif  /* __IApplicationDebugger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IApplicationDebuggerUI interface
 */
#ifndef __IApplicationDebuggerUI_INTERFACE_DEFINED__
#define __IApplicationDebuggerUI_INTERFACE_DEFINED__

DEFINE_GUID(IID_IApplicationDebuggerUI, 0x51973c2b, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c2b-cb0c-11d0-b5c9-00a0244a0e7a")
IApplicationDebuggerUI : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE BringDocumentToTop(
        IDebugDocumentText *pddt) = 0;

    virtual HRESULT STDMETHODCALLTYPE BringDocumentContextToTop(
        IDebugDocumentContext *pddc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IApplicationDebuggerUI, 0x51973c2b, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IApplicationDebuggerUIVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IApplicationDebuggerUI *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IApplicationDebuggerUI *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IApplicationDebuggerUI *This);

    /*** IApplicationDebuggerUI methods ***/
    HRESULT (STDMETHODCALLTYPE *BringDocumentToTop)(
        IApplicationDebuggerUI *This,
        IDebugDocumentText *pddt);

    HRESULT (STDMETHODCALLTYPE *BringDocumentContextToTop)(
        IApplicationDebuggerUI *This,
        IDebugDocumentContext *pddc);

    END_INTERFACE
} IApplicationDebuggerUIVtbl;

interface IApplicationDebuggerUI {
    CONST_VTBL IApplicationDebuggerUIVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IApplicationDebuggerUI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IApplicationDebuggerUI_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IApplicationDebuggerUI_Release(This) (This)->lpVtbl->Release(This)
/*** IApplicationDebuggerUI methods ***/
#define IApplicationDebuggerUI_BringDocumentToTop(This,pddt) (This)->lpVtbl->BringDocumentToTop(This,pddt)
#define IApplicationDebuggerUI_BringDocumentContextToTop(This,pddc) (This)->lpVtbl->BringDocumentContextToTop(This,pddc)
#else
/*** IUnknown methods ***/
static inline HRESULT IApplicationDebuggerUI_QueryInterface(IApplicationDebuggerUI* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IApplicationDebuggerUI_AddRef(IApplicationDebuggerUI* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IApplicationDebuggerUI_Release(IApplicationDebuggerUI* This) {
    return This->lpVtbl->Release(This);
}
/*** IApplicationDebuggerUI methods ***/
static inline HRESULT IApplicationDebuggerUI_BringDocumentToTop(IApplicationDebuggerUI* This,IDebugDocumentText *pddt) {
    return This->lpVtbl->BringDocumentToTop(This,pddt);
}
static inline HRESULT IApplicationDebuggerUI_BringDocumentContextToTop(IApplicationDebuggerUI* This,IDebugDocumentContext *pddc) {
    return This->lpVtbl->BringDocumentContextToTop(This,pddc);
}
#endif
#endif

#endif


#endif  /* __IApplicationDebuggerUI_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMachineDebugManager interface
 */
#ifndef __IMachineDebugManager_INTERFACE_DEFINED__
#define __IMachineDebugManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMachineDebugManager, 0x51973c2c, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c2c-cb0c-11d0-b5c9-00a0244a0e7a")
IMachineDebugManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddApplication(
        IRemoteDebugApplication *pda,
        DWORD *pdwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveApplication(
        DWORD dwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumApplications(
        IEnumRemoteDebugApplications **ppeda) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMachineDebugManager, 0x51973c2c, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IMachineDebugManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMachineDebugManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMachineDebugManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMachineDebugManager *This);

    /*** IMachineDebugManager methods ***/
    HRESULT (STDMETHODCALLTYPE *AddApplication)(
        IMachineDebugManager *This,
        IRemoteDebugApplication *pda,
        DWORD *pdwAppCookie);

    HRESULT (STDMETHODCALLTYPE *RemoveApplication)(
        IMachineDebugManager *This,
        DWORD dwAppCookie);

    HRESULT (STDMETHODCALLTYPE *EnumApplications)(
        IMachineDebugManager *This,
        IEnumRemoteDebugApplications **ppeda);

    END_INTERFACE
} IMachineDebugManagerVtbl;

interface IMachineDebugManager {
    CONST_VTBL IMachineDebugManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMachineDebugManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMachineDebugManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMachineDebugManager_Release(This) (This)->lpVtbl->Release(This)
/*** IMachineDebugManager methods ***/
#define IMachineDebugManager_AddApplication(This,pda,pdwAppCookie) (This)->lpVtbl->AddApplication(This,pda,pdwAppCookie)
#define IMachineDebugManager_RemoveApplication(This,dwAppCookie) (This)->lpVtbl->RemoveApplication(This,dwAppCookie)
#define IMachineDebugManager_EnumApplications(This,ppeda) (This)->lpVtbl->EnumApplications(This,ppeda)
#else
/*** IUnknown methods ***/
static inline HRESULT IMachineDebugManager_QueryInterface(IMachineDebugManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMachineDebugManager_AddRef(IMachineDebugManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMachineDebugManager_Release(IMachineDebugManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IMachineDebugManager methods ***/
static inline HRESULT IMachineDebugManager_AddApplication(IMachineDebugManager* This,IRemoteDebugApplication *pda,DWORD *pdwAppCookie) {
    return This->lpVtbl->AddApplication(This,pda,pdwAppCookie);
}
static inline HRESULT IMachineDebugManager_RemoveApplication(IMachineDebugManager* This,DWORD dwAppCookie) {
    return This->lpVtbl->RemoveApplication(This,dwAppCookie);
}
static inline HRESULT IMachineDebugManager_EnumApplications(IMachineDebugManager* This,IEnumRemoteDebugApplications **ppeda) {
    return This->lpVtbl->EnumApplications(This,ppeda);
}
#endif
#endif

#endif


#endif  /* __IMachineDebugManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMachineDebugManagerCookie interface
 */
#ifndef __IMachineDebugManagerCookie_INTERFACE_DEFINED__
#define __IMachineDebugManagerCookie_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMachineDebugManagerCookie, 0x51973c2d, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c2d-cb0c-11d0-b5c9-00a0244a0e7a")
IMachineDebugManagerCookie : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddApplication(
        IRemoteDebugApplication *pda,
        DWORD dwDebugAppCookie,
        DWORD *pdwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveApplication(
        DWORD dwDebugAppCookie,
        DWORD dwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumApplications(
        IEnumRemoteDebugApplications **ppeda) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMachineDebugManagerCookie, 0x51973c2d, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IMachineDebugManagerCookieVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMachineDebugManagerCookie *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMachineDebugManagerCookie *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMachineDebugManagerCookie *This);

    /*** IMachineDebugManagerCookie methods ***/
    HRESULT (STDMETHODCALLTYPE *AddApplication)(
        IMachineDebugManagerCookie *This,
        IRemoteDebugApplication *pda,
        DWORD dwDebugAppCookie,
        DWORD *pdwAppCookie);

    HRESULT (STDMETHODCALLTYPE *RemoveApplication)(
        IMachineDebugManagerCookie *This,
        DWORD dwDebugAppCookie,
        DWORD dwAppCookie);

    HRESULT (STDMETHODCALLTYPE *EnumApplications)(
        IMachineDebugManagerCookie *This,
        IEnumRemoteDebugApplications **ppeda);

    END_INTERFACE
} IMachineDebugManagerCookieVtbl;

interface IMachineDebugManagerCookie {
    CONST_VTBL IMachineDebugManagerCookieVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMachineDebugManagerCookie_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMachineDebugManagerCookie_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMachineDebugManagerCookie_Release(This) (This)->lpVtbl->Release(This)
/*** IMachineDebugManagerCookie methods ***/
#define IMachineDebugManagerCookie_AddApplication(This,pda,dwDebugAppCookie,pdwAppCookie) (This)->lpVtbl->AddApplication(This,pda,dwDebugAppCookie,pdwAppCookie)
#define IMachineDebugManagerCookie_RemoveApplication(This,dwDebugAppCookie,dwAppCookie) (This)->lpVtbl->RemoveApplication(This,dwDebugAppCookie,dwAppCookie)
#define IMachineDebugManagerCookie_EnumApplications(This,ppeda) (This)->lpVtbl->EnumApplications(This,ppeda)
#else
/*** IUnknown methods ***/
static inline HRESULT IMachineDebugManagerCookie_QueryInterface(IMachineDebugManagerCookie* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMachineDebugManagerCookie_AddRef(IMachineDebugManagerCookie* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMachineDebugManagerCookie_Release(IMachineDebugManagerCookie* This) {
    return This->lpVtbl->Release(This);
}
/*** IMachineDebugManagerCookie methods ***/
static inline HRESULT IMachineDebugManagerCookie_AddApplication(IMachineDebugManagerCookie* This,IRemoteDebugApplication *pda,DWORD dwDebugAppCookie,DWORD *pdwAppCookie) {
    return This->lpVtbl->AddApplication(This,pda,dwDebugAppCookie,pdwAppCookie);
}
static inline HRESULT IMachineDebugManagerCookie_RemoveApplication(IMachineDebugManagerCookie* This,DWORD dwDebugAppCookie,DWORD dwAppCookie) {
    return This->lpVtbl->RemoveApplication(This,dwDebugAppCookie,dwAppCookie);
}
static inline HRESULT IMachineDebugManagerCookie_EnumApplications(IMachineDebugManagerCookie* This,IEnumRemoteDebugApplications **ppeda) {
    return This->lpVtbl->EnumApplications(This,ppeda);
}
#endif
#endif

#endif


#endif  /* __IMachineDebugManagerCookie_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMachineDebugManagerEvents interface
 */
#ifndef __IMachineDebugManagerEvents_INTERFACE_DEFINED__
#define __IMachineDebugManagerEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMachineDebugManagerEvents, 0x51973c2e, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c2e-cb0c-11d0-b5c9-00a0244a0e7a")
IMachineDebugManagerEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE onAddApplication(
        IRemoteDebugApplication *pda,
        DWORD dwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE onRemoveApplication(
        IRemoteDebugApplication *pda,
        DWORD dwAppCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMachineDebugManagerEvents, 0x51973c2e, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IMachineDebugManagerEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMachineDebugManagerEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMachineDebugManagerEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMachineDebugManagerEvents *This);

    /*** IMachineDebugManagerEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *onAddApplication)(
        IMachineDebugManagerEvents *This,
        IRemoteDebugApplication *pda,
        DWORD dwAppCookie);

    HRESULT (STDMETHODCALLTYPE *onRemoveApplication)(
        IMachineDebugManagerEvents *This,
        IRemoteDebugApplication *pda,
        DWORD dwAppCookie);

    END_INTERFACE
} IMachineDebugManagerEventsVtbl;

interface IMachineDebugManagerEvents {
    CONST_VTBL IMachineDebugManagerEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMachineDebugManagerEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMachineDebugManagerEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMachineDebugManagerEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IMachineDebugManagerEvents methods ***/
#define IMachineDebugManagerEvents_onAddApplication(This,pda,dwAppCookie) (This)->lpVtbl->onAddApplication(This,pda,dwAppCookie)
#define IMachineDebugManagerEvents_onRemoveApplication(This,pda,dwAppCookie) (This)->lpVtbl->onRemoveApplication(This,pda,dwAppCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IMachineDebugManagerEvents_QueryInterface(IMachineDebugManagerEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMachineDebugManagerEvents_AddRef(IMachineDebugManagerEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMachineDebugManagerEvents_Release(IMachineDebugManagerEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IMachineDebugManagerEvents methods ***/
static inline HRESULT IMachineDebugManagerEvents_onAddApplication(IMachineDebugManagerEvents* This,IRemoteDebugApplication *pda,DWORD dwAppCookie) {
    return This->lpVtbl->onAddApplication(This,pda,dwAppCookie);
}
static inline HRESULT IMachineDebugManagerEvents_onRemoveApplication(IMachineDebugManagerEvents* This,IRemoteDebugApplication *pda,DWORD dwAppCookie) {
    return This->lpVtbl->onRemoveApplication(This,pda,dwAppCookie);
}
#endif
#endif

#endif


#endif  /* __IMachineDebugManagerEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IProcessDebugManager32 interface
 */
#ifndef __IProcessDebugManager32_INTERFACE_DEFINED__
#define __IProcessDebugManager32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProcessDebugManager32, 0x51973c2f, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c2f-cb0c-11d0-b5c9-00a0244a0e7a")
IProcessDebugManager32 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateApplication(
        IDebugApplication32 **ppda) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultApplication(
        IDebugApplication32 **ppda) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddApplication(
        IDebugApplication32 *pda,
        DWORD *pdwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveApplication(
        DWORD dwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDebugDocumentHelper(
        IUnknown *punkOuter,
        IDebugDocumentHelper32 **pddh) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProcessDebugManager32, 0x51973c2f, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IProcessDebugManager32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProcessDebugManager32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProcessDebugManager32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProcessDebugManager32 *This);

    /*** IProcessDebugManager32 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateApplication)(
        IProcessDebugManager32 *This,
        IDebugApplication32 **ppda);

    HRESULT (STDMETHODCALLTYPE *GetDefaultApplication)(
        IProcessDebugManager32 *This,
        IDebugApplication32 **ppda);

    HRESULT (STDMETHODCALLTYPE *AddApplication)(
        IProcessDebugManager32 *This,
        IDebugApplication32 *pda,
        DWORD *pdwAppCookie);

    HRESULT (STDMETHODCALLTYPE *RemoveApplication)(
        IProcessDebugManager32 *This,
        DWORD dwAppCookie);

    HRESULT (STDMETHODCALLTYPE *CreateDebugDocumentHelper)(
        IProcessDebugManager32 *This,
        IUnknown *punkOuter,
        IDebugDocumentHelper32 **pddh);

    END_INTERFACE
} IProcessDebugManager32Vtbl;

interface IProcessDebugManager32 {
    CONST_VTBL IProcessDebugManager32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProcessDebugManager32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProcessDebugManager32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProcessDebugManager32_Release(This) (This)->lpVtbl->Release(This)
/*** IProcessDebugManager32 methods ***/
#define IProcessDebugManager32_CreateApplication(This,ppda) (This)->lpVtbl->CreateApplication(This,ppda)
#define IProcessDebugManager32_GetDefaultApplication(This,ppda) (This)->lpVtbl->GetDefaultApplication(This,ppda)
#define IProcessDebugManager32_AddApplication(This,pda,pdwAppCookie) (This)->lpVtbl->AddApplication(This,pda,pdwAppCookie)
#define IProcessDebugManager32_RemoveApplication(This,dwAppCookie) (This)->lpVtbl->RemoveApplication(This,dwAppCookie)
#define IProcessDebugManager32_CreateDebugDocumentHelper(This,punkOuter,pddh) (This)->lpVtbl->CreateDebugDocumentHelper(This,punkOuter,pddh)
#else
/*** IUnknown methods ***/
static inline HRESULT IProcessDebugManager32_QueryInterface(IProcessDebugManager32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProcessDebugManager32_AddRef(IProcessDebugManager32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProcessDebugManager32_Release(IProcessDebugManager32* This) {
    return This->lpVtbl->Release(This);
}
/*** IProcessDebugManager32 methods ***/
static inline HRESULT IProcessDebugManager32_CreateApplication(IProcessDebugManager32* This,IDebugApplication32 **ppda) {
    return This->lpVtbl->CreateApplication(This,ppda);
}
static inline HRESULT IProcessDebugManager32_GetDefaultApplication(IProcessDebugManager32* This,IDebugApplication32 **ppda) {
    return This->lpVtbl->GetDefaultApplication(This,ppda);
}
static inline HRESULT IProcessDebugManager32_AddApplication(IProcessDebugManager32* This,IDebugApplication32 *pda,DWORD *pdwAppCookie) {
    return This->lpVtbl->AddApplication(This,pda,pdwAppCookie);
}
static inline HRESULT IProcessDebugManager32_RemoveApplication(IProcessDebugManager32* This,DWORD dwAppCookie) {
    return This->lpVtbl->RemoveApplication(This,dwAppCookie);
}
static inline HRESULT IProcessDebugManager32_CreateDebugDocumentHelper(IProcessDebugManager32* This,IUnknown *punkOuter,IDebugDocumentHelper32 **pddh) {
    return This->lpVtbl->CreateDebugDocumentHelper(This,punkOuter,pddh);
}
#endif
#endif

#endif


#endif  /* __IProcessDebugManager32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IProcessDebugManager64 interface
 */
#ifndef __IProcessDebugManager64_INTERFACE_DEFINED__
#define __IProcessDebugManager64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProcessDebugManager64, 0x56b9fc1c, 0x63a9, 0x4cc1, 0xac,0x21, 0x08,0x7d,0x69,0xa1,0x7f,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56b9fc1c-63a9-4cc1-ac21-087d69a17fab")
IProcessDebugManager64 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateApplication(
        IDebugApplication64 **ppda) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultApplication(
        IDebugApplication64 **ppda) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddApplication(
        IDebugApplication64 *pda,
        DWORD *pdwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveApplication(
        DWORD dwAppCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDebugDocumentHelper(
        IUnknown *punkOuter,
        IDebugDocumentHelper64 **pddh) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProcessDebugManager64, 0x56b9fc1c, 0x63a9, 0x4cc1, 0xac,0x21, 0x08,0x7d,0x69,0xa1,0x7f,0xab)
#endif
#else
typedef struct IProcessDebugManager64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProcessDebugManager64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProcessDebugManager64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProcessDebugManager64 *This);

    /*** IProcessDebugManager64 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateApplication)(
        IProcessDebugManager64 *This,
        IDebugApplication64 **ppda);

    HRESULT (STDMETHODCALLTYPE *GetDefaultApplication)(
        IProcessDebugManager64 *This,
        IDebugApplication64 **ppda);

    HRESULT (STDMETHODCALLTYPE *AddApplication)(
        IProcessDebugManager64 *This,
        IDebugApplication64 *pda,
        DWORD *pdwAppCookie);

    HRESULT (STDMETHODCALLTYPE *RemoveApplication)(
        IProcessDebugManager64 *This,
        DWORD dwAppCookie);

    HRESULT (STDMETHODCALLTYPE *CreateDebugDocumentHelper)(
        IProcessDebugManager64 *This,
        IUnknown *punkOuter,
        IDebugDocumentHelper64 **pddh);

    END_INTERFACE
} IProcessDebugManager64Vtbl;

interface IProcessDebugManager64 {
    CONST_VTBL IProcessDebugManager64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProcessDebugManager64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProcessDebugManager64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProcessDebugManager64_Release(This) (This)->lpVtbl->Release(This)
/*** IProcessDebugManager64 methods ***/
#define IProcessDebugManager64_CreateApplication(This,ppda) (This)->lpVtbl->CreateApplication(This,ppda)
#define IProcessDebugManager64_GetDefaultApplication(This,ppda) (This)->lpVtbl->GetDefaultApplication(This,ppda)
#define IProcessDebugManager64_AddApplication(This,pda,pdwAppCookie) (This)->lpVtbl->AddApplication(This,pda,pdwAppCookie)
#define IProcessDebugManager64_RemoveApplication(This,dwAppCookie) (This)->lpVtbl->RemoveApplication(This,dwAppCookie)
#define IProcessDebugManager64_CreateDebugDocumentHelper(This,punkOuter,pddh) (This)->lpVtbl->CreateDebugDocumentHelper(This,punkOuter,pddh)
#else
/*** IUnknown methods ***/
static inline HRESULT IProcessDebugManager64_QueryInterface(IProcessDebugManager64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProcessDebugManager64_AddRef(IProcessDebugManager64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProcessDebugManager64_Release(IProcessDebugManager64* This) {
    return This->lpVtbl->Release(This);
}
/*** IProcessDebugManager64 methods ***/
static inline HRESULT IProcessDebugManager64_CreateApplication(IProcessDebugManager64* This,IDebugApplication64 **ppda) {
    return This->lpVtbl->CreateApplication(This,ppda);
}
static inline HRESULT IProcessDebugManager64_GetDefaultApplication(IProcessDebugManager64* This,IDebugApplication64 **ppda) {
    return This->lpVtbl->GetDefaultApplication(This,ppda);
}
static inline HRESULT IProcessDebugManager64_AddApplication(IProcessDebugManager64* This,IDebugApplication64 *pda,DWORD *pdwAppCookie) {
    return This->lpVtbl->AddApplication(This,pda,pdwAppCookie);
}
static inline HRESULT IProcessDebugManager64_RemoveApplication(IProcessDebugManager64* This,DWORD dwAppCookie) {
    return This->lpVtbl->RemoveApplication(This,dwAppCookie);
}
static inline HRESULT IProcessDebugManager64_CreateDebugDocumentHelper(IProcessDebugManager64* This,IUnknown *punkOuter,IDebugDocumentHelper64 **pddh) {
    return This->lpVtbl->CreateDebugDocumentHelper(This,punkOuter,pddh);
}
#endif
#endif

#endif


#endif  /* __IProcessDebugManager64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRemoteDebugApplication interface
 */
#ifndef __IRemoteDebugApplication_INTERFACE_DEFINED__
#define __IRemoteDebugApplication_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRemoteDebugApplication, 0x51973c30, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c30-cb0c-11d0-b5c9-00a0244a0e7a")
IRemoteDebugApplication : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ResumeFromBreakPoint(
        IRemoteDebugApplicationThread *prptFocus,
        BREAKRESUMEACTION bra,
        ERRORRESUMEACTION era) = 0;

    virtual HRESULT STDMETHODCALLTYPE CauseBreak(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConnectDebugger(
        IApplicationDebugger *pad) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisconnectDebugger(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDebugger(
        IApplicationDebugger **pad) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstanceAtApplication(
        REFCLSID rclsid,
        IUnknown *pUnkOuter,
        DWORD dwClsContext,
        REFIID riid,
        IUnknown **ppvObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryAlive(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumThreads(
        IEnumRemoteDebugApplicationThreads **pperdat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetName(
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRootNode(
        IDebugApplicationNode **ppdanRoot) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumGlobalExpressionContexts(
        IEnumDebugExpressionContexts **ppedec) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRemoteDebugApplication, 0x51973c30, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IRemoteDebugApplicationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRemoteDebugApplication *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRemoteDebugApplication *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRemoteDebugApplication *This);

    /*** IRemoteDebugApplication methods ***/
    HRESULT (STDMETHODCALLTYPE *ResumeFromBreakPoint)(
        IRemoteDebugApplication *This,
        IRemoteDebugApplicationThread *prptFocus,
        BREAKRESUMEACTION bra,
        ERRORRESUMEACTION era);

    HRESULT (STDMETHODCALLTYPE *CauseBreak)(
        IRemoteDebugApplication *This);

    HRESULT (STDMETHODCALLTYPE *ConnectDebugger)(
        IRemoteDebugApplication *This,
        IApplicationDebugger *pad);

    HRESULT (STDMETHODCALLTYPE *DisconnectDebugger)(
        IRemoteDebugApplication *This);

    HRESULT (STDMETHODCALLTYPE *GetDebugger)(
        IRemoteDebugApplication *This,
        IApplicationDebugger **pad);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceAtApplication)(
        IRemoteDebugApplication *This,
        REFCLSID rclsid,
        IUnknown *pUnkOuter,
        DWORD dwClsContext,
        REFIID riid,
        IUnknown **ppvObject);

    HRESULT (STDMETHODCALLTYPE *QueryAlive)(
        IRemoteDebugApplication *This);

    HRESULT (STDMETHODCALLTYPE *EnumThreads)(
        IRemoteDebugApplication *This,
        IEnumRemoteDebugApplicationThreads **pperdat);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IRemoteDebugApplication *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetRootNode)(
        IRemoteDebugApplication *This,
        IDebugApplicationNode **ppdanRoot);

    HRESULT (STDMETHODCALLTYPE *EnumGlobalExpressionContexts)(
        IRemoteDebugApplication *This,
        IEnumDebugExpressionContexts **ppedec);

    END_INTERFACE
} IRemoteDebugApplicationVtbl;

interface IRemoteDebugApplication {
    CONST_VTBL IRemoteDebugApplicationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRemoteDebugApplication_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRemoteDebugApplication_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRemoteDebugApplication_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplication methods ***/
#define IRemoteDebugApplication_ResumeFromBreakPoint(This,prptFocus,bra,era) (This)->lpVtbl->ResumeFromBreakPoint(This,prptFocus,bra,era)
#define IRemoteDebugApplication_CauseBreak(This) (This)->lpVtbl->CauseBreak(This)
#define IRemoteDebugApplication_ConnectDebugger(This,pad) (This)->lpVtbl->ConnectDebugger(This,pad)
#define IRemoteDebugApplication_DisconnectDebugger(This) (This)->lpVtbl->DisconnectDebugger(This)
#define IRemoteDebugApplication_GetDebugger(This,pad) (This)->lpVtbl->GetDebugger(This,pad)
#define IRemoteDebugApplication_CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject) (This)->lpVtbl->CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject)
#define IRemoteDebugApplication_QueryAlive(This) (This)->lpVtbl->QueryAlive(This)
#define IRemoteDebugApplication_EnumThreads(This,pperdat) (This)->lpVtbl->EnumThreads(This,pperdat)
#define IRemoteDebugApplication_GetName(This,pbstrName) (This)->lpVtbl->GetName(This,pbstrName)
#define IRemoteDebugApplication_GetRootNode(This,ppdanRoot) (This)->lpVtbl->GetRootNode(This,ppdanRoot)
#define IRemoteDebugApplication_EnumGlobalExpressionContexts(This,ppedec) (This)->lpVtbl->EnumGlobalExpressionContexts(This,ppedec)
#else
/*** IUnknown methods ***/
static inline HRESULT IRemoteDebugApplication_QueryInterface(IRemoteDebugApplication* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRemoteDebugApplication_AddRef(IRemoteDebugApplication* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRemoteDebugApplication_Release(IRemoteDebugApplication* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplication methods ***/
static inline HRESULT IRemoteDebugApplication_ResumeFromBreakPoint(IRemoteDebugApplication* This,IRemoteDebugApplicationThread *prptFocus,BREAKRESUMEACTION bra,ERRORRESUMEACTION era) {
    return This->lpVtbl->ResumeFromBreakPoint(This,prptFocus,bra,era);
}
static inline HRESULT IRemoteDebugApplication_CauseBreak(IRemoteDebugApplication* This) {
    return This->lpVtbl->CauseBreak(This);
}
static inline HRESULT IRemoteDebugApplication_ConnectDebugger(IRemoteDebugApplication* This,IApplicationDebugger *pad) {
    return This->lpVtbl->ConnectDebugger(This,pad);
}
static inline HRESULT IRemoteDebugApplication_DisconnectDebugger(IRemoteDebugApplication* This) {
    return This->lpVtbl->DisconnectDebugger(This);
}
static inline HRESULT IRemoteDebugApplication_GetDebugger(IRemoteDebugApplication* This,IApplicationDebugger **pad) {
    return This->lpVtbl->GetDebugger(This,pad);
}
static inline HRESULT IRemoteDebugApplication_CreateInstanceAtApplication(IRemoteDebugApplication* This,REFCLSID rclsid,IUnknown *pUnkOuter,DWORD dwClsContext,REFIID riid,IUnknown **ppvObject) {
    return This->lpVtbl->CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject);
}
static inline HRESULT IRemoteDebugApplication_QueryAlive(IRemoteDebugApplication* This) {
    return This->lpVtbl->QueryAlive(This);
}
static inline HRESULT IRemoteDebugApplication_EnumThreads(IRemoteDebugApplication* This,IEnumRemoteDebugApplicationThreads **pperdat) {
    return This->lpVtbl->EnumThreads(This,pperdat);
}
static inline HRESULT IRemoteDebugApplication_GetName(IRemoteDebugApplication* This,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,pbstrName);
}
static inline HRESULT IRemoteDebugApplication_GetRootNode(IRemoteDebugApplication* This,IDebugApplicationNode **ppdanRoot) {
    return This->lpVtbl->GetRootNode(This,ppdanRoot);
}
static inline HRESULT IRemoteDebugApplication_EnumGlobalExpressionContexts(IRemoteDebugApplication* This,IEnumDebugExpressionContexts **ppedec) {
    return This->lpVtbl->EnumGlobalExpressionContexts(This,ppedec);
}
#endif
#endif

#endif


#endif  /* __IRemoteDebugApplication_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplication32 interface
 */
#ifndef __IDebugApplication32_INTERFACE_DEFINED__
#define __IDebugApplication32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplication32, 0x51973c32, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c32-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugApplication32 : public IRemoteDebugApplication
{
    virtual HRESULT STDMETHODCALLTYPE SetName(
        LPCOLESTR pstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE StepOutComplete(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DebugOutput(
        LPCOLESTR pstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartDebugSession(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE HandleBreakPoint(
        BREAKREASON br,
        BREAKRESUMEACTION *pbra) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBreakFlags(
        APPBREAKFLAGS *pabf,
        IRemoteDebugApplicationThread **pprdatSteppingThread) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentThread(
        IDebugApplicationThread **pat) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAsyncDebugOperation(
        IDebugSyncOperation *psdo,
        IDebugAsyncOperation **ppado) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStackFrameSniffer(
        IDebugStackFrameSniffer *pdsfs,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStackFrameSniffer(
        DWORD dwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryCurrentThreadIsDebuggerThread(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SynchronousCallInDebuggerThread(
        IDebugThreadCall32 *pptc,
        DWORD dwParam1,
        DWORD dwParam2,
        DWORD dwParam3) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateApplicationNode(
        IDebugApplicationNode **ppdanNew) = 0;

    virtual HRESULT STDMETHODCALLTYPE FireDebuggerEvent(
        REFGUID riid,
        IUnknown *punk) = 0;

    virtual HRESULT STDMETHODCALLTYPE HandleRuntimeError(
        IActiveScriptErrorDebug *pErrorDebug,
        IActiveScriptSite *pScriptSite,
        BREAKRESUMEACTION *pbra,
        ERRORRESUMEACTION *perra,
        WINBOOL *pfCallOnScriptError) = 0;

    virtual WINBOOL STDMETHODCALLTYPE FCanJitDebug(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE FIsAutoJitDebugEnabled(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddGlobalExpressionContextProvider(
        IProvideExpressionContexts *pdsfs,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveGlobalExpressionContextProvider(
        DWORD dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplication32, 0x51973c32, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugApplication32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplication32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplication32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplication32 *This);

    /*** IRemoteDebugApplication methods ***/
    HRESULT (STDMETHODCALLTYPE *ResumeFromBreakPoint)(
        IDebugApplication32 *This,
        IRemoteDebugApplicationThread *prptFocus,
        BREAKRESUMEACTION bra,
        ERRORRESUMEACTION era);

    HRESULT (STDMETHODCALLTYPE *CauseBreak)(
        IDebugApplication32 *This);

    HRESULT (STDMETHODCALLTYPE *ConnectDebugger)(
        IDebugApplication32 *This,
        IApplicationDebugger *pad);

    HRESULT (STDMETHODCALLTYPE *DisconnectDebugger)(
        IDebugApplication32 *This);

    HRESULT (STDMETHODCALLTYPE *GetDebugger)(
        IDebugApplication32 *This,
        IApplicationDebugger **pad);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceAtApplication)(
        IDebugApplication32 *This,
        REFCLSID rclsid,
        IUnknown *pUnkOuter,
        DWORD dwClsContext,
        REFIID riid,
        IUnknown **ppvObject);

    HRESULT (STDMETHODCALLTYPE *QueryAlive)(
        IDebugApplication32 *This);

    HRESULT (STDMETHODCALLTYPE *EnumThreads)(
        IDebugApplication32 *This,
        IEnumRemoteDebugApplicationThreads **pperdat);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugApplication32 *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetRootNode)(
        IDebugApplication32 *This,
        IDebugApplicationNode **ppdanRoot);

    HRESULT (STDMETHODCALLTYPE *EnumGlobalExpressionContexts)(
        IDebugApplication32 *This,
        IEnumDebugExpressionContexts **ppedec);

    /*** IDebugApplication32 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetName)(
        IDebugApplication32 *This,
        LPCOLESTR pstrName);

    HRESULT (STDMETHODCALLTYPE *StepOutComplete)(
        IDebugApplication32 *This);

    HRESULT (STDMETHODCALLTYPE *DebugOutput)(
        IDebugApplication32 *This,
        LPCOLESTR pstr);

    HRESULT (STDMETHODCALLTYPE *StartDebugSession)(
        IDebugApplication32 *This);

    HRESULT (STDMETHODCALLTYPE *HandleBreakPoint)(
        IDebugApplication32 *This,
        BREAKREASON br,
        BREAKRESUMEACTION *pbra);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IDebugApplication32 *This);

    HRESULT (STDMETHODCALLTYPE *GetBreakFlags)(
        IDebugApplication32 *This,
        APPBREAKFLAGS *pabf,
        IRemoteDebugApplicationThread **pprdatSteppingThread);

    HRESULT (STDMETHODCALLTYPE *GetCurrentThread)(
        IDebugApplication32 *This,
        IDebugApplicationThread **pat);

    HRESULT (STDMETHODCALLTYPE *CreateAsyncDebugOperation)(
        IDebugApplication32 *This,
        IDebugSyncOperation *psdo,
        IDebugAsyncOperation **ppado);

    HRESULT (STDMETHODCALLTYPE *AddStackFrameSniffer)(
        IDebugApplication32 *This,
        IDebugStackFrameSniffer *pdsfs,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RemoveStackFrameSniffer)(
        IDebugApplication32 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *QueryCurrentThreadIsDebuggerThread)(
        IDebugApplication32 *This);

    HRESULT (STDMETHODCALLTYPE *SynchronousCallInDebuggerThread)(
        IDebugApplication32 *This,
        IDebugThreadCall32 *pptc,
        DWORD dwParam1,
        DWORD dwParam2,
        DWORD dwParam3);

    HRESULT (STDMETHODCALLTYPE *CreateApplicationNode)(
        IDebugApplication32 *This,
        IDebugApplicationNode **ppdanNew);

    HRESULT (STDMETHODCALLTYPE *FireDebuggerEvent)(
        IDebugApplication32 *This,
        REFGUID riid,
        IUnknown *punk);

    HRESULT (STDMETHODCALLTYPE *HandleRuntimeError)(
        IDebugApplication32 *This,
        IActiveScriptErrorDebug *pErrorDebug,
        IActiveScriptSite *pScriptSite,
        BREAKRESUMEACTION *pbra,
        ERRORRESUMEACTION *perra,
        WINBOOL *pfCallOnScriptError);

    WINBOOL (STDMETHODCALLTYPE *FCanJitDebug)(
        IDebugApplication32 *This);

    WINBOOL (STDMETHODCALLTYPE *FIsAutoJitDebugEnabled)(
        IDebugApplication32 *This);

    HRESULT (STDMETHODCALLTYPE *AddGlobalExpressionContextProvider)(
        IDebugApplication32 *This,
        IProvideExpressionContexts *pdsfs,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RemoveGlobalExpressionContextProvider)(
        IDebugApplication32 *This,
        DWORD dwCookie);

    END_INTERFACE
} IDebugApplication32Vtbl;

interface IDebugApplication32 {
    CONST_VTBL IDebugApplication32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplication32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplication32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplication32_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplication methods ***/
#define IDebugApplication32_ResumeFromBreakPoint(This,prptFocus,bra,era) (This)->lpVtbl->ResumeFromBreakPoint(This,prptFocus,bra,era)
#define IDebugApplication32_CauseBreak(This) (This)->lpVtbl->CauseBreak(This)
#define IDebugApplication32_ConnectDebugger(This,pad) (This)->lpVtbl->ConnectDebugger(This,pad)
#define IDebugApplication32_DisconnectDebugger(This) (This)->lpVtbl->DisconnectDebugger(This)
#define IDebugApplication32_GetDebugger(This,pad) (This)->lpVtbl->GetDebugger(This,pad)
#define IDebugApplication32_CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject) (This)->lpVtbl->CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject)
#define IDebugApplication32_QueryAlive(This) (This)->lpVtbl->QueryAlive(This)
#define IDebugApplication32_EnumThreads(This,pperdat) (This)->lpVtbl->EnumThreads(This,pperdat)
#define IDebugApplication32_GetName(This,pbstrName) (This)->lpVtbl->GetName(This,pbstrName)
#define IDebugApplication32_GetRootNode(This,ppdanRoot) (This)->lpVtbl->GetRootNode(This,ppdanRoot)
#define IDebugApplication32_EnumGlobalExpressionContexts(This,ppedec) (This)->lpVtbl->EnumGlobalExpressionContexts(This,ppedec)
/*** IDebugApplication32 methods ***/
#define IDebugApplication32_SetName(This,pstrName) (This)->lpVtbl->SetName(This,pstrName)
#define IDebugApplication32_StepOutComplete(This) (This)->lpVtbl->StepOutComplete(This)
#define IDebugApplication32_DebugOutput(This,pstr) (This)->lpVtbl->DebugOutput(This,pstr)
#define IDebugApplication32_StartDebugSession(This) (This)->lpVtbl->StartDebugSession(This)
#define IDebugApplication32_HandleBreakPoint(This,br,pbra) (This)->lpVtbl->HandleBreakPoint(This,br,pbra)
#define IDebugApplication32_Close(This) (This)->lpVtbl->Close(This)
#define IDebugApplication32_GetBreakFlags(This,pabf,pprdatSteppingThread) (This)->lpVtbl->GetBreakFlags(This,pabf,pprdatSteppingThread)
#define IDebugApplication32_GetCurrentThread(This,pat) (This)->lpVtbl->GetCurrentThread(This,pat)
#define IDebugApplication32_CreateAsyncDebugOperation(This,psdo,ppado) (This)->lpVtbl->CreateAsyncDebugOperation(This,psdo,ppado)
#define IDebugApplication32_AddStackFrameSniffer(This,pdsfs,pdwCookie) (This)->lpVtbl->AddStackFrameSniffer(This,pdsfs,pdwCookie)
#define IDebugApplication32_RemoveStackFrameSniffer(This,dwCookie) (This)->lpVtbl->RemoveStackFrameSniffer(This,dwCookie)
#define IDebugApplication32_QueryCurrentThreadIsDebuggerThread(This) (This)->lpVtbl->QueryCurrentThreadIsDebuggerThread(This)
#define IDebugApplication32_SynchronousCallInDebuggerThread(This,pptc,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->SynchronousCallInDebuggerThread(This,pptc,dwParam1,dwParam2,dwParam3)
#define IDebugApplication32_CreateApplicationNode(This,ppdanNew) (This)->lpVtbl->CreateApplicationNode(This,ppdanNew)
#define IDebugApplication32_FireDebuggerEvent(This,riid,punk) (This)->lpVtbl->FireDebuggerEvent(This,riid,punk)
#define IDebugApplication32_HandleRuntimeError(This,pErrorDebug,pScriptSite,pbra,perra,pfCallOnScriptError) (This)->lpVtbl->HandleRuntimeError(This,pErrorDebug,pScriptSite,pbra,perra,pfCallOnScriptError)
#define IDebugApplication32_FCanJitDebug(This) (This)->lpVtbl->FCanJitDebug(This)
#define IDebugApplication32_FIsAutoJitDebugEnabled(This) (This)->lpVtbl->FIsAutoJitDebugEnabled(This)
#define IDebugApplication32_AddGlobalExpressionContextProvider(This,pdsfs,pdwCookie) (This)->lpVtbl->AddGlobalExpressionContextProvider(This,pdsfs,pdwCookie)
#define IDebugApplication32_RemoveGlobalExpressionContextProvider(This,dwCookie) (This)->lpVtbl->RemoveGlobalExpressionContextProvider(This,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugApplication32_QueryInterface(IDebugApplication32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugApplication32_AddRef(IDebugApplication32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugApplication32_Release(IDebugApplication32* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplication methods ***/
static inline HRESULT IDebugApplication32_ResumeFromBreakPoint(IDebugApplication32* This,IRemoteDebugApplicationThread *prptFocus,BREAKRESUMEACTION bra,ERRORRESUMEACTION era) {
    return This->lpVtbl->ResumeFromBreakPoint(This,prptFocus,bra,era);
}
static inline HRESULT IDebugApplication32_CauseBreak(IDebugApplication32* This) {
    return This->lpVtbl->CauseBreak(This);
}
static inline HRESULT IDebugApplication32_ConnectDebugger(IDebugApplication32* This,IApplicationDebugger *pad) {
    return This->lpVtbl->ConnectDebugger(This,pad);
}
static inline HRESULT IDebugApplication32_DisconnectDebugger(IDebugApplication32* This) {
    return This->lpVtbl->DisconnectDebugger(This);
}
static inline HRESULT IDebugApplication32_GetDebugger(IDebugApplication32* This,IApplicationDebugger **pad) {
    return This->lpVtbl->GetDebugger(This,pad);
}
static inline HRESULT IDebugApplication32_CreateInstanceAtApplication(IDebugApplication32* This,REFCLSID rclsid,IUnknown *pUnkOuter,DWORD dwClsContext,REFIID riid,IUnknown **ppvObject) {
    return This->lpVtbl->CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject);
}
static inline HRESULT IDebugApplication32_QueryAlive(IDebugApplication32* This) {
    return This->lpVtbl->QueryAlive(This);
}
static inline HRESULT IDebugApplication32_EnumThreads(IDebugApplication32* This,IEnumRemoteDebugApplicationThreads **pperdat) {
    return This->lpVtbl->EnumThreads(This,pperdat);
}
static inline HRESULT IDebugApplication32_GetName(IDebugApplication32* This,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,pbstrName);
}
static inline HRESULT IDebugApplication32_GetRootNode(IDebugApplication32* This,IDebugApplicationNode **ppdanRoot) {
    return This->lpVtbl->GetRootNode(This,ppdanRoot);
}
static inline HRESULT IDebugApplication32_EnumGlobalExpressionContexts(IDebugApplication32* This,IEnumDebugExpressionContexts **ppedec) {
    return This->lpVtbl->EnumGlobalExpressionContexts(This,ppedec);
}
/*** IDebugApplication32 methods ***/
static inline HRESULT IDebugApplication32_SetName(IDebugApplication32* This,LPCOLESTR pstrName) {
    return This->lpVtbl->SetName(This,pstrName);
}
static inline HRESULT IDebugApplication32_StepOutComplete(IDebugApplication32* This) {
    return This->lpVtbl->StepOutComplete(This);
}
static inline HRESULT IDebugApplication32_DebugOutput(IDebugApplication32* This,LPCOLESTR pstr) {
    return This->lpVtbl->DebugOutput(This,pstr);
}
static inline HRESULT IDebugApplication32_StartDebugSession(IDebugApplication32* This) {
    return This->lpVtbl->StartDebugSession(This);
}
static inline HRESULT IDebugApplication32_HandleBreakPoint(IDebugApplication32* This,BREAKREASON br,BREAKRESUMEACTION *pbra) {
    return This->lpVtbl->HandleBreakPoint(This,br,pbra);
}
static inline HRESULT IDebugApplication32_Close(IDebugApplication32* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IDebugApplication32_GetBreakFlags(IDebugApplication32* This,APPBREAKFLAGS *pabf,IRemoteDebugApplicationThread **pprdatSteppingThread) {
    return This->lpVtbl->GetBreakFlags(This,pabf,pprdatSteppingThread);
}
static inline HRESULT IDebugApplication32_GetCurrentThread(IDebugApplication32* This,IDebugApplicationThread **pat) {
    return This->lpVtbl->GetCurrentThread(This,pat);
}
static inline HRESULT IDebugApplication32_CreateAsyncDebugOperation(IDebugApplication32* This,IDebugSyncOperation *psdo,IDebugAsyncOperation **ppado) {
    return This->lpVtbl->CreateAsyncDebugOperation(This,psdo,ppado);
}
static inline HRESULT IDebugApplication32_AddStackFrameSniffer(IDebugApplication32* This,IDebugStackFrameSniffer *pdsfs,DWORD *pdwCookie) {
    return This->lpVtbl->AddStackFrameSniffer(This,pdsfs,pdwCookie);
}
static inline HRESULT IDebugApplication32_RemoveStackFrameSniffer(IDebugApplication32* This,DWORD dwCookie) {
    return This->lpVtbl->RemoveStackFrameSniffer(This,dwCookie);
}
static inline HRESULT IDebugApplication32_QueryCurrentThreadIsDebuggerThread(IDebugApplication32* This) {
    return This->lpVtbl->QueryCurrentThreadIsDebuggerThread(This);
}
static inline HRESULT IDebugApplication32_SynchronousCallInDebuggerThread(IDebugApplication32* This,IDebugThreadCall32 *pptc,DWORD dwParam1,DWORD dwParam2,DWORD dwParam3) {
    return This->lpVtbl->SynchronousCallInDebuggerThread(This,pptc,dwParam1,dwParam2,dwParam3);
}
static inline HRESULT IDebugApplication32_CreateApplicationNode(IDebugApplication32* This,IDebugApplicationNode **ppdanNew) {
    return This->lpVtbl->CreateApplicationNode(This,ppdanNew);
}
static inline HRESULT IDebugApplication32_FireDebuggerEvent(IDebugApplication32* This,REFGUID riid,IUnknown *punk) {
    return This->lpVtbl->FireDebuggerEvent(This,riid,punk);
}
static inline HRESULT IDebugApplication32_HandleRuntimeError(IDebugApplication32* This,IActiveScriptErrorDebug *pErrorDebug,IActiveScriptSite *pScriptSite,BREAKRESUMEACTION *pbra,ERRORRESUMEACTION *perra,WINBOOL *pfCallOnScriptError) {
    return This->lpVtbl->HandleRuntimeError(This,pErrorDebug,pScriptSite,pbra,perra,pfCallOnScriptError);
}
static inline WINBOOL IDebugApplication32_FCanJitDebug(IDebugApplication32* This) {
    return This->lpVtbl->FCanJitDebug(This);
}
static inline WINBOOL IDebugApplication32_FIsAutoJitDebugEnabled(IDebugApplication32* This) {
    return This->lpVtbl->FIsAutoJitDebugEnabled(This);
}
static inline HRESULT IDebugApplication32_AddGlobalExpressionContextProvider(IDebugApplication32* This,IProvideExpressionContexts *pdsfs,DWORD *pdwCookie) {
    return This->lpVtbl->AddGlobalExpressionContextProvider(This,pdsfs,pdwCookie);
}
static inline HRESULT IDebugApplication32_RemoveGlobalExpressionContextProvider(IDebugApplication32* This,DWORD dwCookie) {
    return This->lpVtbl->RemoveGlobalExpressionContextProvider(This,dwCookie);
}
#endif
#endif

#endif


#endif  /* __IDebugApplication32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplication64 interface
 */
#ifndef __IDebugApplication64_INTERFACE_DEFINED__
#define __IDebugApplication64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplication64, 0x4dedc754, 0x04c7, 0x4f10, 0x9e,0x60, 0x16,0xa3,0x90,0xfe,0x6e,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4dedc754-04c7-4f10-9e60-16a390fe6e62")
IDebugApplication64 : public IRemoteDebugApplication
{
    virtual HRESULT STDMETHODCALLTYPE SetName(
        LPCOLESTR pstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE StepOutComplete(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DebugOutput(
        LPCOLESTR pstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartDebugSession(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE HandleBreakPoint(
        BREAKREASON br,
        BREAKRESUMEACTION *pbra) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBreakFlags(
        APPBREAKFLAGS *pabf,
        IRemoteDebugApplicationThread **pprdatSteppingThread) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentThread(
        IDebugApplicationThread **pat) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAsyncDebugOperation(
        IDebugSyncOperation *psdo,
        IDebugAsyncOperation **ppado) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStackFrameSniffer(
        IDebugStackFrameSniffer *pdsfs,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStackFrameSniffer(
        DWORD dwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryCurrentThreadIsDebuggerThread(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SynchronousCallInDebuggerThread(
        IDebugThreadCall64 *pptc,
        DWORDLONG dwParam1,
        DWORDLONG dwParam2,
        DWORDLONG dwParam3) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateApplicationNode(
        IDebugApplicationNode **ppdanNew) = 0;

    virtual HRESULT STDMETHODCALLTYPE FireDebuggerEvent(
        REFGUID riid,
        IUnknown *punk) = 0;

    virtual HRESULT STDMETHODCALLTYPE HandleRuntimeError(
        IActiveScriptErrorDebug *pErrorDebug,
        IActiveScriptSite *pScriptSite,
        BREAKRESUMEACTION *pbra,
        ERRORRESUMEACTION *perra,
        WINBOOL *pfCallOnScriptError) = 0;

    virtual WINBOOL STDMETHODCALLTYPE FCanJitDebug(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE FIsAutoJitDebugEnabled(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddGlobalExpressionContextProvider(
        IProvideExpressionContexts *pdsfs,
        DWORDLONG *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveGlobalExpressionContextProvider(
        DWORDLONG dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplication64, 0x4dedc754, 0x04c7, 0x4f10, 0x9e,0x60, 0x16,0xa3,0x90,0xfe,0x6e,0x62)
#endif
#else
typedef struct IDebugApplication64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplication64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplication64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplication64 *This);

    /*** IRemoteDebugApplication methods ***/
    HRESULT (STDMETHODCALLTYPE *ResumeFromBreakPoint)(
        IDebugApplication64 *This,
        IRemoteDebugApplicationThread *prptFocus,
        BREAKRESUMEACTION bra,
        ERRORRESUMEACTION era);

    HRESULT (STDMETHODCALLTYPE *CauseBreak)(
        IDebugApplication64 *This);

    HRESULT (STDMETHODCALLTYPE *ConnectDebugger)(
        IDebugApplication64 *This,
        IApplicationDebugger *pad);

    HRESULT (STDMETHODCALLTYPE *DisconnectDebugger)(
        IDebugApplication64 *This);

    HRESULT (STDMETHODCALLTYPE *GetDebugger)(
        IDebugApplication64 *This,
        IApplicationDebugger **pad);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceAtApplication)(
        IDebugApplication64 *This,
        REFCLSID rclsid,
        IUnknown *pUnkOuter,
        DWORD dwClsContext,
        REFIID riid,
        IUnknown **ppvObject);

    HRESULT (STDMETHODCALLTYPE *QueryAlive)(
        IDebugApplication64 *This);

    HRESULT (STDMETHODCALLTYPE *EnumThreads)(
        IDebugApplication64 *This,
        IEnumRemoteDebugApplicationThreads **pperdat);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugApplication64 *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetRootNode)(
        IDebugApplication64 *This,
        IDebugApplicationNode **ppdanRoot);

    HRESULT (STDMETHODCALLTYPE *EnumGlobalExpressionContexts)(
        IDebugApplication64 *This,
        IEnumDebugExpressionContexts **ppedec);

    /*** IDebugApplication64 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetName)(
        IDebugApplication64 *This,
        LPCOLESTR pstrName);

    HRESULT (STDMETHODCALLTYPE *StepOutComplete)(
        IDebugApplication64 *This);

    HRESULT (STDMETHODCALLTYPE *DebugOutput)(
        IDebugApplication64 *This,
        LPCOLESTR pstr);

    HRESULT (STDMETHODCALLTYPE *StartDebugSession)(
        IDebugApplication64 *This);

    HRESULT (STDMETHODCALLTYPE *HandleBreakPoint)(
        IDebugApplication64 *This,
        BREAKREASON br,
        BREAKRESUMEACTION *pbra);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IDebugApplication64 *This);

    HRESULT (STDMETHODCALLTYPE *GetBreakFlags)(
        IDebugApplication64 *This,
        APPBREAKFLAGS *pabf,
        IRemoteDebugApplicationThread **pprdatSteppingThread);

    HRESULT (STDMETHODCALLTYPE *GetCurrentThread)(
        IDebugApplication64 *This,
        IDebugApplicationThread **pat);

    HRESULT (STDMETHODCALLTYPE *CreateAsyncDebugOperation)(
        IDebugApplication64 *This,
        IDebugSyncOperation *psdo,
        IDebugAsyncOperation **ppado);

    HRESULT (STDMETHODCALLTYPE *AddStackFrameSniffer)(
        IDebugApplication64 *This,
        IDebugStackFrameSniffer *pdsfs,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RemoveStackFrameSniffer)(
        IDebugApplication64 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *QueryCurrentThreadIsDebuggerThread)(
        IDebugApplication64 *This);

    HRESULT (STDMETHODCALLTYPE *SynchronousCallInDebuggerThread)(
        IDebugApplication64 *This,
        IDebugThreadCall64 *pptc,
        DWORDLONG dwParam1,
        DWORDLONG dwParam2,
        DWORDLONG dwParam3);

    HRESULT (STDMETHODCALLTYPE *CreateApplicationNode)(
        IDebugApplication64 *This,
        IDebugApplicationNode **ppdanNew);

    HRESULT (STDMETHODCALLTYPE *FireDebuggerEvent)(
        IDebugApplication64 *This,
        REFGUID riid,
        IUnknown *punk);

    HRESULT (STDMETHODCALLTYPE *HandleRuntimeError)(
        IDebugApplication64 *This,
        IActiveScriptErrorDebug *pErrorDebug,
        IActiveScriptSite *pScriptSite,
        BREAKRESUMEACTION *pbra,
        ERRORRESUMEACTION *perra,
        WINBOOL *pfCallOnScriptError);

    WINBOOL (STDMETHODCALLTYPE *FCanJitDebug)(
        IDebugApplication64 *This);

    WINBOOL (STDMETHODCALLTYPE *FIsAutoJitDebugEnabled)(
        IDebugApplication64 *This);

    HRESULT (STDMETHODCALLTYPE *AddGlobalExpressionContextProvider)(
        IDebugApplication64 *This,
        IProvideExpressionContexts *pdsfs,
        DWORDLONG *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RemoveGlobalExpressionContextProvider)(
        IDebugApplication64 *This,
        DWORDLONG dwCookie);

    END_INTERFACE
} IDebugApplication64Vtbl;

interface IDebugApplication64 {
    CONST_VTBL IDebugApplication64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplication64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplication64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplication64_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplication methods ***/
#define IDebugApplication64_ResumeFromBreakPoint(This,prptFocus,bra,era) (This)->lpVtbl->ResumeFromBreakPoint(This,prptFocus,bra,era)
#define IDebugApplication64_CauseBreak(This) (This)->lpVtbl->CauseBreak(This)
#define IDebugApplication64_ConnectDebugger(This,pad) (This)->lpVtbl->ConnectDebugger(This,pad)
#define IDebugApplication64_DisconnectDebugger(This) (This)->lpVtbl->DisconnectDebugger(This)
#define IDebugApplication64_GetDebugger(This,pad) (This)->lpVtbl->GetDebugger(This,pad)
#define IDebugApplication64_CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject) (This)->lpVtbl->CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject)
#define IDebugApplication64_QueryAlive(This) (This)->lpVtbl->QueryAlive(This)
#define IDebugApplication64_EnumThreads(This,pperdat) (This)->lpVtbl->EnumThreads(This,pperdat)
#define IDebugApplication64_GetName(This,pbstrName) (This)->lpVtbl->GetName(This,pbstrName)
#define IDebugApplication64_GetRootNode(This,ppdanRoot) (This)->lpVtbl->GetRootNode(This,ppdanRoot)
#define IDebugApplication64_EnumGlobalExpressionContexts(This,ppedec) (This)->lpVtbl->EnumGlobalExpressionContexts(This,ppedec)
/*** IDebugApplication64 methods ***/
#define IDebugApplication64_SetName(This,pstrName) (This)->lpVtbl->SetName(This,pstrName)
#define IDebugApplication64_StepOutComplete(This) (This)->lpVtbl->StepOutComplete(This)
#define IDebugApplication64_DebugOutput(This,pstr) (This)->lpVtbl->DebugOutput(This,pstr)
#define IDebugApplication64_StartDebugSession(This) (This)->lpVtbl->StartDebugSession(This)
#define IDebugApplication64_HandleBreakPoint(This,br,pbra) (This)->lpVtbl->HandleBreakPoint(This,br,pbra)
#define IDebugApplication64_Close(This) (This)->lpVtbl->Close(This)
#define IDebugApplication64_GetBreakFlags(This,pabf,pprdatSteppingThread) (This)->lpVtbl->GetBreakFlags(This,pabf,pprdatSteppingThread)
#define IDebugApplication64_GetCurrentThread(This,pat) (This)->lpVtbl->GetCurrentThread(This,pat)
#define IDebugApplication64_CreateAsyncDebugOperation(This,psdo,ppado) (This)->lpVtbl->CreateAsyncDebugOperation(This,psdo,ppado)
#define IDebugApplication64_AddStackFrameSniffer(This,pdsfs,pdwCookie) (This)->lpVtbl->AddStackFrameSniffer(This,pdsfs,pdwCookie)
#define IDebugApplication64_RemoveStackFrameSniffer(This,dwCookie) (This)->lpVtbl->RemoveStackFrameSniffer(This,dwCookie)
#define IDebugApplication64_QueryCurrentThreadIsDebuggerThread(This) (This)->lpVtbl->QueryCurrentThreadIsDebuggerThread(This)
#define IDebugApplication64_SynchronousCallInDebuggerThread(This,pptc,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->SynchronousCallInDebuggerThread(This,pptc,dwParam1,dwParam2,dwParam3)
#define IDebugApplication64_CreateApplicationNode(This,ppdanNew) (This)->lpVtbl->CreateApplicationNode(This,ppdanNew)
#define IDebugApplication64_FireDebuggerEvent(This,riid,punk) (This)->lpVtbl->FireDebuggerEvent(This,riid,punk)
#define IDebugApplication64_HandleRuntimeError(This,pErrorDebug,pScriptSite,pbra,perra,pfCallOnScriptError) (This)->lpVtbl->HandleRuntimeError(This,pErrorDebug,pScriptSite,pbra,perra,pfCallOnScriptError)
#define IDebugApplication64_FCanJitDebug(This) (This)->lpVtbl->FCanJitDebug(This)
#define IDebugApplication64_FIsAutoJitDebugEnabled(This) (This)->lpVtbl->FIsAutoJitDebugEnabled(This)
#define IDebugApplication64_AddGlobalExpressionContextProvider(This,pdsfs,pdwCookie) (This)->lpVtbl->AddGlobalExpressionContextProvider(This,pdsfs,pdwCookie)
#define IDebugApplication64_RemoveGlobalExpressionContextProvider(This,dwCookie) (This)->lpVtbl->RemoveGlobalExpressionContextProvider(This,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugApplication64_QueryInterface(IDebugApplication64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugApplication64_AddRef(IDebugApplication64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugApplication64_Release(IDebugApplication64* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplication methods ***/
static inline HRESULT IDebugApplication64_ResumeFromBreakPoint(IDebugApplication64* This,IRemoteDebugApplicationThread *prptFocus,BREAKRESUMEACTION bra,ERRORRESUMEACTION era) {
    return This->lpVtbl->ResumeFromBreakPoint(This,prptFocus,bra,era);
}
static inline HRESULT IDebugApplication64_CauseBreak(IDebugApplication64* This) {
    return This->lpVtbl->CauseBreak(This);
}
static inline HRESULT IDebugApplication64_ConnectDebugger(IDebugApplication64* This,IApplicationDebugger *pad) {
    return This->lpVtbl->ConnectDebugger(This,pad);
}
static inline HRESULT IDebugApplication64_DisconnectDebugger(IDebugApplication64* This) {
    return This->lpVtbl->DisconnectDebugger(This);
}
static inline HRESULT IDebugApplication64_GetDebugger(IDebugApplication64* This,IApplicationDebugger **pad) {
    return This->lpVtbl->GetDebugger(This,pad);
}
static inline HRESULT IDebugApplication64_CreateInstanceAtApplication(IDebugApplication64* This,REFCLSID rclsid,IUnknown *pUnkOuter,DWORD dwClsContext,REFIID riid,IUnknown **ppvObject) {
    return This->lpVtbl->CreateInstanceAtApplication(This,rclsid,pUnkOuter,dwClsContext,riid,ppvObject);
}
static inline HRESULT IDebugApplication64_QueryAlive(IDebugApplication64* This) {
    return This->lpVtbl->QueryAlive(This);
}
static inline HRESULT IDebugApplication64_EnumThreads(IDebugApplication64* This,IEnumRemoteDebugApplicationThreads **pperdat) {
    return This->lpVtbl->EnumThreads(This,pperdat);
}
static inline HRESULT IDebugApplication64_GetName(IDebugApplication64* This,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,pbstrName);
}
static inline HRESULT IDebugApplication64_GetRootNode(IDebugApplication64* This,IDebugApplicationNode **ppdanRoot) {
    return This->lpVtbl->GetRootNode(This,ppdanRoot);
}
static inline HRESULT IDebugApplication64_EnumGlobalExpressionContexts(IDebugApplication64* This,IEnumDebugExpressionContexts **ppedec) {
    return This->lpVtbl->EnumGlobalExpressionContexts(This,ppedec);
}
/*** IDebugApplication64 methods ***/
static inline HRESULT IDebugApplication64_SetName(IDebugApplication64* This,LPCOLESTR pstrName) {
    return This->lpVtbl->SetName(This,pstrName);
}
static inline HRESULT IDebugApplication64_StepOutComplete(IDebugApplication64* This) {
    return This->lpVtbl->StepOutComplete(This);
}
static inline HRESULT IDebugApplication64_DebugOutput(IDebugApplication64* This,LPCOLESTR pstr) {
    return This->lpVtbl->DebugOutput(This,pstr);
}
static inline HRESULT IDebugApplication64_StartDebugSession(IDebugApplication64* This) {
    return This->lpVtbl->StartDebugSession(This);
}
static inline HRESULT IDebugApplication64_HandleBreakPoint(IDebugApplication64* This,BREAKREASON br,BREAKRESUMEACTION *pbra) {
    return This->lpVtbl->HandleBreakPoint(This,br,pbra);
}
static inline HRESULT IDebugApplication64_Close(IDebugApplication64* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IDebugApplication64_GetBreakFlags(IDebugApplication64* This,APPBREAKFLAGS *pabf,IRemoteDebugApplicationThread **pprdatSteppingThread) {
    return This->lpVtbl->GetBreakFlags(This,pabf,pprdatSteppingThread);
}
static inline HRESULT IDebugApplication64_GetCurrentThread(IDebugApplication64* This,IDebugApplicationThread **pat) {
    return This->lpVtbl->GetCurrentThread(This,pat);
}
static inline HRESULT IDebugApplication64_CreateAsyncDebugOperation(IDebugApplication64* This,IDebugSyncOperation *psdo,IDebugAsyncOperation **ppado) {
    return This->lpVtbl->CreateAsyncDebugOperation(This,psdo,ppado);
}
static inline HRESULT IDebugApplication64_AddStackFrameSniffer(IDebugApplication64* This,IDebugStackFrameSniffer *pdsfs,DWORD *pdwCookie) {
    return This->lpVtbl->AddStackFrameSniffer(This,pdsfs,pdwCookie);
}
static inline HRESULT IDebugApplication64_RemoveStackFrameSniffer(IDebugApplication64* This,DWORD dwCookie) {
    return This->lpVtbl->RemoveStackFrameSniffer(This,dwCookie);
}
static inline HRESULT IDebugApplication64_QueryCurrentThreadIsDebuggerThread(IDebugApplication64* This) {
    return This->lpVtbl->QueryCurrentThreadIsDebuggerThread(This);
}
static inline HRESULT IDebugApplication64_SynchronousCallInDebuggerThread(IDebugApplication64* This,IDebugThreadCall64 *pptc,DWORDLONG dwParam1,DWORDLONG dwParam2,DWORDLONG dwParam3) {
    return This->lpVtbl->SynchronousCallInDebuggerThread(This,pptc,dwParam1,dwParam2,dwParam3);
}
static inline HRESULT IDebugApplication64_CreateApplicationNode(IDebugApplication64* This,IDebugApplicationNode **ppdanNew) {
    return This->lpVtbl->CreateApplicationNode(This,ppdanNew);
}
static inline HRESULT IDebugApplication64_FireDebuggerEvent(IDebugApplication64* This,REFGUID riid,IUnknown *punk) {
    return This->lpVtbl->FireDebuggerEvent(This,riid,punk);
}
static inline HRESULT IDebugApplication64_HandleRuntimeError(IDebugApplication64* This,IActiveScriptErrorDebug *pErrorDebug,IActiveScriptSite *pScriptSite,BREAKRESUMEACTION *pbra,ERRORRESUMEACTION *perra,WINBOOL *pfCallOnScriptError) {
    return This->lpVtbl->HandleRuntimeError(This,pErrorDebug,pScriptSite,pbra,perra,pfCallOnScriptError);
}
static inline WINBOOL IDebugApplication64_FCanJitDebug(IDebugApplication64* This) {
    return This->lpVtbl->FCanJitDebug(This);
}
static inline WINBOOL IDebugApplication64_FIsAutoJitDebugEnabled(IDebugApplication64* This) {
    return This->lpVtbl->FIsAutoJitDebugEnabled(This);
}
static inline HRESULT IDebugApplication64_AddGlobalExpressionContextProvider(IDebugApplication64* This,IProvideExpressionContexts *pdsfs,DWORDLONG *pdwCookie) {
    return This->lpVtbl->AddGlobalExpressionContextProvider(This,pdsfs,pdwCookie);
}
static inline HRESULT IDebugApplication64_RemoveGlobalExpressionContextProvider(IDebugApplication64* This,DWORDLONG dwCookie) {
    return This->lpVtbl->RemoveGlobalExpressionContextProvider(This,dwCookie);
}
#endif
#endif

#endif


#endif  /* __IDebugApplication64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRemoteDebugApplicationEvents interface
 */
#ifndef __IRemoteDebugApplicationEvents_INTERFACE_DEFINED__
#define __IRemoteDebugApplicationEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRemoteDebugApplicationEvents, 0x51973c33, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c33-cb0c-11d0-b5c9-00a0244a0e7a")
IRemoteDebugApplicationEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnConnectDebugger(
        IApplicationDebugger *pad) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDisconnectDebugger(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnSetName(
        LPCOLESTR pstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDebugOutput(
        LPCOLESTR pstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnClose(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnEnterBreakPoint(
        IRemoteDebugApplicationThread *prdat) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnLeaveBreakPoint(
        IRemoteDebugApplicationThread *prdat) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnCreateThread(
        IRemoteDebugApplicationThread *prdat) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDestroyThread(
        IRemoteDebugApplicationThread *prdat) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnBreakFlagChange(
        APPBREAKFLAGS abf,
        IRemoteDebugApplicationThread *prdatSteppingThread) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRemoteDebugApplicationEvents, 0x51973c33, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IRemoteDebugApplicationEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRemoteDebugApplicationEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRemoteDebugApplicationEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRemoteDebugApplicationEvents *This);

    /*** IRemoteDebugApplicationEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *OnConnectDebugger)(
        IRemoteDebugApplicationEvents *This,
        IApplicationDebugger *pad);

    HRESULT (STDMETHODCALLTYPE *OnDisconnectDebugger)(
        IRemoteDebugApplicationEvents *This);

    HRESULT (STDMETHODCALLTYPE *OnSetName)(
        IRemoteDebugApplicationEvents *This,
        LPCOLESTR pstrName);

    HRESULT (STDMETHODCALLTYPE *OnDebugOutput)(
        IRemoteDebugApplicationEvents *This,
        LPCOLESTR pstr);

    HRESULT (STDMETHODCALLTYPE *OnClose)(
        IRemoteDebugApplicationEvents *This);

    HRESULT (STDMETHODCALLTYPE *OnEnterBreakPoint)(
        IRemoteDebugApplicationEvents *This,
        IRemoteDebugApplicationThread *prdat);

    HRESULT (STDMETHODCALLTYPE *OnLeaveBreakPoint)(
        IRemoteDebugApplicationEvents *This,
        IRemoteDebugApplicationThread *prdat);

    HRESULT (STDMETHODCALLTYPE *OnCreateThread)(
        IRemoteDebugApplicationEvents *This,
        IRemoteDebugApplicationThread *prdat);

    HRESULT (STDMETHODCALLTYPE *OnDestroyThread)(
        IRemoteDebugApplicationEvents *This,
        IRemoteDebugApplicationThread *prdat);

    HRESULT (STDMETHODCALLTYPE *OnBreakFlagChange)(
        IRemoteDebugApplicationEvents *This,
        APPBREAKFLAGS abf,
        IRemoteDebugApplicationThread *prdatSteppingThread);

    END_INTERFACE
} IRemoteDebugApplicationEventsVtbl;

interface IRemoteDebugApplicationEvents {
    CONST_VTBL IRemoteDebugApplicationEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRemoteDebugApplicationEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRemoteDebugApplicationEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRemoteDebugApplicationEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplicationEvents methods ***/
#define IRemoteDebugApplicationEvents_OnConnectDebugger(This,pad) (This)->lpVtbl->OnConnectDebugger(This,pad)
#define IRemoteDebugApplicationEvents_OnDisconnectDebugger(This) (This)->lpVtbl->OnDisconnectDebugger(This)
#define IRemoteDebugApplicationEvents_OnSetName(This,pstrName) (This)->lpVtbl->OnSetName(This,pstrName)
#define IRemoteDebugApplicationEvents_OnDebugOutput(This,pstr) (This)->lpVtbl->OnDebugOutput(This,pstr)
#define IRemoteDebugApplicationEvents_OnClose(This) (This)->lpVtbl->OnClose(This)
#define IRemoteDebugApplicationEvents_OnEnterBreakPoint(This,prdat) (This)->lpVtbl->OnEnterBreakPoint(This,prdat)
#define IRemoteDebugApplicationEvents_OnLeaveBreakPoint(This,prdat) (This)->lpVtbl->OnLeaveBreakPoint(This,prdat)
#define IRemoteDebugApplicationEvents_OnCreateThread(This,prdat) (This)->lpVtbl->OnCreateThread(This,prdat)
#define IRemoteDebugApplicationEvents_OnDestroyThread(This,prdat) (This)->lpVtbl->OnDestroyThread(This,prdat)
#define IRemoteDebugApplicationEvents_OnBreakFlagChange(This,abf,prdatSteppingThread) (This)->lpVtbl->OnBreakFlagChange(This,abf,prdatSteppingThread)
#else
/*** IUnknown methods ***/
static inline HRESULT IRemoteDebugApplicationEvents_QueryInterface(IRemoteDebugApplicationEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRemoteDebugApplicationEvents_AddRef(IRemoteDebugApplicationEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRemoteDebugApplicationEvents_Release(IRemoteDebugApplicationEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplicationEvents methods ***/
static inline HRESULT IRemoteDebugApplicationEvents_OnConnectDebugger(IRemoteDebugApplicationEvents* This,IApplicationDebugger *pad) {
    return This->lpVtbl->OnConnectDebugger(This,pad);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnDisconnectDebugger(IRemoteDebugApplicationEvents* This) {
    return This->lpVtbl->OnDisconnectDebugger(This);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnSetName(IRemoteDebugApplicationEvents* This,LPCOLESTR pstrName) {
    return This->lpVtbl->OnSetName(This,pstrName);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnDebugOutput(IRemoteDebugApplicationEvents* This,LPCOLESTR pstr) {
    return This->lpVtbl->OnDebugOutput(This,pstr);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnClose(IRemoteDebugApplicationEvents* This) {
    return This->lpVtbl->OnClose(This);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnEnterBreakPoint(IRemoteDebugApplicationEvents* This,IRemoteDebugApplicationThread *prdat) {
    return This->lpVtbl->OnEnterBreakPoint(This,prdat);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnLeaveBreakPoint(IRemoteDebugApplicationEvents* This,IRemoteDebugApplicationThread *prdat) {
    return This->lpVtbl->OnLeaveBreakPoint(This,prdat);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnCreateThread(IRemoteDebugApplicationEvents* This,IRemoteDebugApplicationThread *prdat) {
    return This->lpVtbl->OnCreateThread(This,prdat);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnDestroyThread(IRemoteDebugApplicationEvents* This,IRemoteDebugApplicationThread *prdat) {
    return This->lpVtbl->OnDestroyThread(This,prdat);
}
static inline HRESULT IRemoteDebugApplicationEvents_OnBreakFlagChange(IRemoteDebugApplicationEvents* This,APPBREAKFLAGS abf,IRemoteDebugApplicationThread *prdatSteppingThread) {
    return This->lpVtbl->OnBreakFlagChange(This,abf,prdatSteppingThread);
}
#endif
#endif

#endif


#endif  /* __IRemoteDebugApplicationEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplicationNode interface
 */
#ifndef __IDebugApplicationNode_INTERFACE_DEFINED__
#define __IDebugApplicationNode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplicationNode, 0x51973c34, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c34-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugApplicationNode : public IDebugDocumentProvider
{
    virtual HRESULT STDMETHODCALLTYPE EnumChildren(
        IEnumDebugApplicationNodes **pperddp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParent(
        IDebugApplicationNode **pprddp) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDocumentProvider(
        IDebugDocumentProvider *pddp) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Attach(
        IDebugApplicationNode *pdanParent) = 0;

    virtual HRESULT STDMETHODCALLTYPE Detach(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplicationNode, 0x51973c34, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugApplicationNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplicationNode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplicationNode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplicationNode *This);

    /*** IDebugDocumentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugApplicationNode *This,
        DOCUMENTNAMETYPE dnt,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *GetDocumentClassId)(
        IDebugApplicationNode *This,
        CLSID *pclsidDocument);

    /*** IDebugDocumentProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocument)(
        IDebugApplicationNode *This,
        IDebugDocument **ppssd);

    /*** IDebugApplicationNode methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumChildren)(
        IDebugApplicationNode *This,
        IEnumDebugApplicationNodes **pperddp);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDebugApplicationNode *This,
        IDebugApplicationNode **pprddp);

    HRESULT (STDMETHODCALLTYPE *SetDocumentProvider)(
        IDebugApplicationNode *This,
        IDebugDocumentProvider *pddp);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IDebugApplicationNode *This);

    HRESULT (STDMETHODCALLTYPE *Attach)(
        IDebugApplicationNode *This,
        IDebugApplicationNode *pdanParent);

    HRESULT (STDMETHODCALLTYPE *Detach)(
        IDebugApplicationNode *This);

    END_INTERFACE
} IDebugApplicationNodeVtbl;

interface IDebugApplicationNode {
    CONST_VTBL IDebugApplicationNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplicationNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplicationNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplicationNode_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugDocumentInfo methods ***/
#define IDebugApplicationNode_GetName(This,dnt,pbstrName) (This)->lpVtbl->GetName(This,dnt,pbstrName)
#define IDebugApplicationNode_GetDocumentClassId(This,pclsidDocument) (This)->lpVtbl->GetDocumentClassId(This,pclsidDocument)
/*** IDebugDocumentProvider methods ***/
#define IDebugApplicationNode_GetDocument(This,ppssd) (This)->lpVtbl->GetDocument(This,ppssd)
/*** IDebugApplicationNode methods ***/
#define IDebugApplicationNode_EnumChildren(This,pperddp) (This)->lpVtbl->EnumChildren(This,pperddp)
#define IDebugApplicationNode_GetParent(This,pprddp) (This)->lpVtbl->GetParent(This,pprddp)
#define IDebugApplicationNode_SetDocumentProvider(This,pddp) (This)->lpVtbl->SetDocumentProvider(This,pddp)
#define IDebugApplicationNode_Close(This) (This)->lpVtbl->Close(This)
#define IDebugApplicationNode_Attach(This,pdanParent) (This)->lpVtbl->Attach(This,pdanParent)
#define IDebugApplicationNode_Detach(This) (This)->lpVtbl->Detach(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugApplicationNode_QueryInterface(IDebugApplicationNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugApplicationNode_AddRef(IDebugApplicationNode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugApplicationNode_Release(IDebugApplicationNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugDocumentInfo methods ***/
static inline HRESULT IDebugApplicationNode_GetName(IDebugApplicationNode* This,DOCUMENTNAMETYPE dnt,BSTR *pbstrName) {
    return This->lpVtbl->GetName(This,dnt,pbstrName);
}
static inline HRESULT IDebugApplicationNode_GetDocumentClassId(IDebugApplicationNode* This,CLSID *pclsidDocument) {
    return This->lpVtbl->GetDocumentClassId(This,pclsidDocument);
}
/*** IDebugDocumentProvider methods ***/
static inline HRESULT IDebugApplicationNode_GetDocument(IDebugApplicationNode* This,IDebugDocument **ppssd) {
    return This->lpVtbl->GetDocument(This,ppssd);
}
/*** IDebugApplicationNode methods ***/
static inline HRESULT IDebugApplicationNode_EnumChildren(IDebugApplicationNode* This,IEnumDebugApplicationNodes **pperddp) {
    return This->lpVtbl->EnumChildren(This,pperddp);
}
static inline HRESULT IDebugApplicationNode_GetParent(IDebugApplicationNode* This,IDebugApplicationNode **pprddp) {
    return This->lpVtbl->GetParent(This,pprddp);
}
static inline HRESULT IDebugApplicationNode_SetDocumentProvider(IDebugApplicationNode* This,IDebugDocumentProvider *pddp) {
    return This->lpVtbl->SetDocumentProvider(This,pddp);
}
static inline HRESULT IDebugApplicationNode_Close(IDebugApplicationNode* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IDebugApplicationNode_Attach(IDebugApplicationNode* This,IDebugApplicationNode *pdanParent) {
    return This->lpVtbl->Attach(This,pdanParent);
}
static inline HRESULT IDebugApplicationNode_Detach(IDebugApplicationNode* This) {
    return This->lpVtbl->Detach(This);
}
#endif
#endif

#endif


#endif  /* __IDebugApplicationNode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplicationNodeEvents interface
 */
#ifndef __IDebugApplicationNodeEvents_INTERFACE_DEFINED__
#define __IDebugApplicationNodeEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplicationNodeEvents, 0x51973c35, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c35-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugApplicationNodeEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE onAddChild(
        IDebugApplicationNode *prddpChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE onRemoveChild(
        IDebugApplicationNode *prddpChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE onDetach(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE onAttach(
        IDebugApplicationNode *prddpParent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplicationNodeEvents, 0x51973c35, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugApplicationNodeEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplicationNodeEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplicationNodeEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplicationNodeEvents *This);

    /*** IDebugApplicationNodeEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *onAddChild)(
        IDebugApplicationNodeEvents *This,
        IDebugApplicationNode *prddpChild);

    HRESULT (STDMETHODCALLTYPE *onRemoveChild)(
        IDebugApplicationNodeEvents *This,
        IDebugApplicationNode *prddpChild);

    HRESULT (STDMETHODCALLTYPE *onDetach)(
        IDebugApplicationNodeEvents *This);

    HRESULT (STDMETHODCALLTYPE *onAttach)(
        IDebugApplicationNodeEvents *This,
        IDebugApplicationNode *prddpParent);

    END_INTERFACE
} IDebugApplicationNodeEventsVtbl;

interface IDebugApplicationNodeEvents {
    CONST_VTBL IDebugApplicationNodeEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplicationNodeEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplicationNodeEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplicationNodeEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugApplicationNodeEvents methods ***/
#define IDebugApplicationNodeEvents_onAddChild(This,prddpChild) (This)->lpVtbl->onAddChild(This,prddpChild)
#define IDebugApplicationNodeEvents_onRemoveChild(This,prddpChild) (This)->lpVtbl->onRemoveChild(This,prddpChild)
#define IDebugApplicationNodeEvents_onDetach(This) (This)->lpVtbl->onDetach(This)
#define IDebugApplicationNodeEvents_onAttach(This,prddpParent) (This)->lpVtbl->onAttach(This,prddpParent)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugApplicationNodeEvents_QueryInterface(IDebugApplicationNodeEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugApplicationNodeEvents_AddRef(IDebugApplicationNodeEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugApplicationNodeEvents_Release(IDebugApplicationNodeEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugApplicationNodeEvents methods ***/
static inline HRESULT IDebugApplicationNodeEvents_onAddChild(IDebugApplicationNodeEvents* This,IDebugApplicationNode *prddpChild) {
    return This->lpVtbl->onAddChild(This,prddpChild);
}
static inline HRESULT IDebugApplicationNodeEvents_onRemoveChild(IDebugApplicationNodeEvents* This,IDebugApplicationNode *prddpChild) {
    return This->lpVtbl->onRemoveChild(This,prddpChild);
}
static inline HRESULT IDebugApplicationNodeEvents_onDetach(IDebugApplicationNodeEvents* This) {
    return This->lpVtbl->onDetach(This);
}
static inline HRESULT IDebugApplicationNodeEvents_onAttach(IDebugApplicationNodeEvents* This,IDebugApplicationNode *prddpParent) {
    return This->lpVtbl->onAttach(This,prddpParent);
}
#endif
#endif

#endif


#endif  /* __IDebugApplicationNodeEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * AsyncIDebugApplicationNodeEvents interface
 */
#ifndef __AsyncIDebugApplicationNodeEvents_INTERFACE_DEFINED__
#define __AsyncIDebugApplicationNodeEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_AsyncIDebugApplicationNodeEvents, 0xa2e3aa3b, 0xaa8d, 0x4ebf, 0x84,0xcd, 0x64,0x8b,0x73,0x7b,0x8c,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a2e3aa3b-aa8d-4ebf-84cd-648b737b8c13")
AsyncIDebugApplicationNodeEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Begin_onAddChild(
        IDebugApplicationNode *prddpChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finish_onAddChild(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Begin_onRemoveChild(
        IDebugApplicationNode *prddpChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finish_onRemoveChild(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Begin_onDetach(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finish_onDetach(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Begin_onAttach(
        IDebugApplicationNode *prddpParent) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finish_onAttach(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AsyncIDebugApplicationNodeEvents, 0xa2e3aa3b, 0xaa8d, 0x4ebf, 0x84,0xcd, 0x64,0x8b,0x73,0x7b,0x8c,0x13)
#endif
#else
typedef struct AsyncIDebugApplicationNodeEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        AsyncIDebugApplicationNodeEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        AsyncIDebugApplicationNodeEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        AsyncIDebugApplicationNodeEvents *This);

    /*** AsyncIDebugApplicationNodeEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *Begin_onAddChild)(
        AsyncIDebugApplicationNodeEvents *This,
        IDebugApplicationNode *prddpChild);

    HRESULT (STDMETHODCALLTYPE *Finish_onAddChild)(
        AsyncIDebugApplicationNodeEvents *This);

    HRESULT (STDMETHODCALLTYPE *Begin_onRemoveChild)(
        AsyncIDebugApplicationNodeEvents *This,
        IDebugApplicationNode *prddpChild);

    HRESULT (STDMETHODCALLTYPE *Finish_onRemoveChild)(
        AsyncIDebugApplicationNodeEvents *This);

    HRESULT (STDMETHODCALLTYPE *Begin_onDetach)(
        AsyncIDebugApplicationNodeEvents *This);

    HRESULT (STDMETHODCALLTYPE *Finish_onDetach)(
        AsyncIDebugApplicationNodeEvents *This);

    HRESULT (STDMETHODCALLTYPE *Begin_onAttach)(
        AsyncIDebugApplicationNodeEvents *This,
        IDebugApplicationNode *prddpParent);

    HRESULT (STDMETHODCALLTYPE *Finish_onAttach)(
        AsyncIDebugApplicationNodeEvents *This);

    END_INTERFACE
} AsyncIDebugApplicationNodeEventsVtbl;

interface AsyncIDebugApplicationNodeEvents {
    CONST_VTBL AsyncIDebugApplicationNodeEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define AsyncIDebugApplicationNodeEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define AsyncIDebugApplicationNodeEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define AsyncIDebugApplicationNodeEvents_Release(This) (This)->lpVtbl->Release(This)
/*** AsyncIDebugApplicationNodeEvents methods ***/
#define AsyncIDebugApplicationNodeEvents_Begin_onAddChild(This,prddpChild) (This)->lpVtbl->Begin_onAddChild(This,prddpChild)
#define AsyncIDebugApplicationNodeEvents_Finish_onAddChild(This) (This)->lpVtbl->Finish_onAddChild(This)
#define AsyncIDebugApplicationNodeEvents_Begin_onRemoveChild(This,prddpChild) (This)->lpVtbl->Begin_onRemoveChild(This,prddpChild)
#define AsyncIDebugApplicationNodeEvents_Finish_onRemoveChild(This) (This)->lpVtbl->Finish_onRemoveChild(This)
#define AsyncIDebugApplicationNodeEvents_Begin_onDetach(This) (This)->lpVtbl->Begin_onDetach(This)
#define AsyncIDebugApplicationNodeEvents_Finish_onDetach(This) (This)->lpVtbl->Finish_onDetach(This)
#define AsyncIDebugApplicationNodeEvents_Begin_onAttach(This,prddpParent) (This)->lpVtbl->Begin_onAttach(This,prddpParent)
#define AsyncIDebugApplicationNodeEvents_Finish_onAttach(This) (This)->lpVtbl->Finish_onAttach(This)
#else
/*** IUnknown methods ***/
static inline HRESULT AsyncIDebugApplicationNodeEvents_QueryInterface(AsyncIDebugApplicationNodeEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG AsyncIDebugApplicationNodeEvents_AddRef(AsyncIDebugApplicationNodeEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG AsyncIDebugApplicationNodeEvents_Release(AsyncIDebugApplicationNodeEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** AsyncIDebugApplicationNodeEvents methods ***/
static inline HRESULT AsyncIDebugApplicationNodeEvents_Begin_onAddChild(AsyncIDebugApplicationNodeEvents* This,IDebugApplicationNode *prddpChild) {
    return This->lpVtbl->Begin_onAddChild(This,prddpChild);
}
static inline HRESULT AsyncIDebugApplicationNodeEvents_Finish_onAddChild(AsyncIDebugApplicationNodeEvents* This) {
    return This->lpVtbl->Finish_onAddChild(This);
}
static inline HRESULT AsyncIDebugApplicationNodeEvents_Begin_onRemoveChild(AsyncIDebugApplicationNodeEvents* This,IDebugApplicationNode *prddpChild) {
    return This->lpVtbl->Begin_onRemoveChild(This,prddpChild);
}
static inline HRESULT AsyncIDebugApplicationNodeEvents_Finish_onRemoveChild(AsyncIDebugApplicationNodeEvents* This) {
    return This->lpVtbl->Finish_onRemoveChild(This);
}
static inline HRESULT AsyncIDebugApplicationNodeEvents_Begin_onDetach(AsyncIDebugApplicationNodeEvents* This) {
    return This->lpVtbl->Begin_onDetach(This);
}
static inline HRESULT AsyncIDebugApplicationNodeEvents_Finish_onDetach(AsyncIDebugApplicationNodeEvents* This) {
    return This->lpVtbl->Finish_onDetach(This);
}
static inline HRESULT AsyncIDebugApplicationNodeEvents_Begin_onAttach(AsyncIDebugApplicationNodeEvents* This,IDebugApplicationNode *prddpParent) {
    return This->lpVtbl->Begin_onAttach(This,prddpParent);
}
static inline HRESULT AsyncIDebugApplicationNodeEvents_Finish_onAttach(AsyncIDebugApplicationNodeEvents* This) {
    return This->lpVtbl->Finish_onAttach(This);
}
#endif
#endif

#endif


#endif  /* __AsyncIDebugApplicationNodeEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugThreadCall32 interface
 */
#ifndef __IDebugThreadCall32_INTERFACE_DEFINED__
#define __IDebugThreadCall32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugThreadCall32, 0x51973c36, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c36-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugThreadCall32 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ThreadCallHandler(
        DWORD dwParam1,
        DWORD dwParam2,
        DWORD dwParam3) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugThreadCall32, 0x51973c36, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugThreadCall32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugThreadCall32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugThreadCall32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugThreadCall32 *This);

    /*** IDebugThreadCall32 methods ***/
    HRESULT (STDMETHODCALLTYPE *ThreadCallHandler)(
        IDebugThreadCall32 *This,
        DWORD dwParam1,
        DWORD dwParam2,
        DWORD dwParam3);

    END_INTERFACE
} IDebugThreadCall32Vtbl;

interface IDebugThreadCall32 {
    CONST_VTBL IDebugThreadCall32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugThreadCall32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugThreadCall32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugThreadCall32_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugThreadCall32 methods ***/
#define IDebugThreadCall32_ThreadCallHandler(This,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->ThreadCallHandler(This,dwParam1,dwParam2,dwParam3)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugThreadCall32_QueryInterface(IDebugThreadCall32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugThreadCall32_AddRef(IDebugThreadCall32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugThreadCall32_Release(IDebugThreadCall32* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugThreadCall32 methods ***/
static inline HRESULT IDebugThreadCall32_ThreadCallHandler(IDebugThreadCall32* This,DWORD dwParam1,DWORD dwParam2,DWORD dwParam3) {
    return This->lpVtbl->ThreadCallHandler(This,dwParam1,dwParam2,dwParam3);
}
#endif
#endif

#endif


#endif  /* __IDebugThreadCall32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugThreadCall64 interface
 */
#ifndef __IDebugThreadCall64_INTERFACE_DEFINED__
#define __IDebugThreadCall64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugThreadCall64, 0xcb3fa335, 0xe979, 0x42fd, 0x9f,0xcf, 0xa7,0x54,0x6a,0x0f,0x39,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cb3fa335-e979-42fd-9fcf-a7546a0f3905")
IDebugThreadCall64 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ThreadCallHandler(
        DWORDLONG dwParam1,
        DWORDLONG dwParam2,
        DWORDLONG dwParam3) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugThreadCall64, 0xcb3fa335, 0xe979, 0x42fd, 0x9f,0xcf, 0xa7,0x54,0x6a,0x0f,0x39,0x05)
#endif
#else
typedef struct IDebugThreadCall64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugThreadCall64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugThreadCall64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugThreadCall64 *This);

    /*** IDebugThreadCall64 methods ***/
    HRESULT (STDMETHODCALLTYPE *ThreadCallHandler)(
        IDebugThreadCall64 *This,
        DWORDLONG dwParam1,
        DWORDLONG dwParam2,
        DWORDLONG dwParam3);

    END_INTERFACE
} IDebugThreadCall64Vtbl;

interface IDebugThreadCall64 {
    CONST_VTBL IDebugThreadCall64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugThreadCall64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugThreadCall64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugThreadCall64_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugThreadCall64 methods ***/
#define IDebugThreadCall64_ThreadCallHandler(This,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->ThreadCallHandler(This,dwParam1,dwParam2,dwParam3)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugThreadCall64_QueryInterface(IDebugThreadCall64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugThreadCall64_AddRef(IDebugThreadCall64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugThreadCall64_Release(IDebugThreadCall64* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugThreadCall64 methods ***/
static inline HRESULT IDebugThreadCall64_ThreadCallHandler(IDebugThreadCall64* This,DWORDLONG dwParam1,DWORDLONG dwParam2,DWORDLONG dwParam3) {
    return This->lpVtbl->ThreadCallHandler(This,dwParam1,dwParam2,dwParam3);
}
#endif
#endif

#endif


#endif  /* __IDebugThreadCall64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRemoteDebugApplicationThread interface
 */
#ifndef __IRemoteDebugApplicationThread_INTERFACE_DEFINED__
#define __IRemoteDebugApplicationThread_INTERFACE_DEFINED__

typedef DWORD THREAD_STATE;
#define THREAD_STATE_RUNNING (0x1)

#define THREAD_STATE_SUSPENDED (0x2)

#define THREAD_BLOCKED (0x4)

#define THREAD_OUT_OF_CONTEXT (0x8)

DEFINE_GUID(IID_IRemoteDebugApplicationThread, 0x51973c37, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c37-cb0c-11d0-b5c9-00a0244a0e7a")
IRemoteDebugApplicationThread : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSystemThreadId(
        DWORD *dwThreadId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetApplication(
        IRemoteDebugApplication **pprda) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumStackFrames(
        IEnumDebugStackFrames **ppedsf) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        BSTR *pbstrDescription,
        BSTR *pbstrState) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNextStatement(
        IDebugStackFrame *pStackFrame,
        IDebugCodeContext *pCodeContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        DWORD *pState) = 0;

    virtual HRESULT STDMETHODCALLTYPE Suspend(
        DWORD *pdwCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        DWORD *pdwCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSuspendCount(
        DWORD *pdwCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRemoteDebugApplicationThread, 0x51973c37, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IRemoteDebugApplicationThreadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRemoteDebugApplicationThread *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRemoteDebugApplicationThread *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRemoteDebugApplicationThread *This);

    /*** IRemoteDebugApplicationThread methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSystemThreadId)(
        IRemoteDebugApplicationThread *This,
        DWORD *dwThreadId);

    HRESULT (STDMETHODCALLTYPE *GetApplication)(
        IRemoteDebugApplicationThread *This,
        IRemoteDebugApplication **pprda);

    HRESULT (STDMETHODCALLTYPE *EnumStackFrames)(
        IRemoteDebugApplicationThread *This,
        IEnumDebugStackFrames **ppedsf);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IRemoteDebugApplicationThread *This,
        BSTR *pbstrDescription,
        BSTR *pbstrState);

    HRESULT (STDMETHODCALLTYPE *SetNextStatement)(
        IRemoteDebugApplicationThread *This,
        IDebugStackFrame *pStackFrame,
        IDebugCodeContext *pCodeContext);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IRemoteDebugApplicationThread *This,
        DWORD *pState);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IRemoteDebugApplicationThread *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IRemoteDebugApplicationThread *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *GetSuspendCount)(
        IRemoteDebugApplicationThread *This,
        DWORD *pdwCount);

    END_INTERFACE
} IRemoteDebugApplicationThreadVtbl;

interface IRemoteDebugApplicationThread {
    CONST_VTBL IRemoteDebugApplicationThreadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRemoteDebugApplicationThread_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRemoteDebugApplicationThread_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRemoteDebugApplicationThread_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplicationThread methods ***/
#define IRemoteDebugApplicationThread_GetSystemThreadId(This,dwThreadId) (This)->lpVtbl->GetSystemThreadId(This,dwThreadId)
#define IRemoteDebugApplicationThread_GetApplication(This,pprda) (This)->lpVtbl->GetApplication(This,pprda)
#define IRemoteDebugApplicationThread_EnumStackFrames(This,ppedsf) (This)->lpVtbl->EnumStackFrames(This,ppedsf)
#define IRemoteDebugApplicationThread_GetDescription(This,pbstrDescription,pbstrState) (This)->lpVtbl->GetDescription(This,pbstrDescription,pbstrState)
#define IRemoteDebugApplicationThread_SetNextStatement(This,pStackFrame,pCodeContext) (This)->lpVtbl->SetNextStatement(This,pStackFrame,pCodeContext)
#define IRemoteDebugApplicationThread_GetState(This,pState) (This)->lpVtbl->GetState(This,pState)
#define IRemoteDebugApplicationThread_Suspend(This,pdwCount) (This)->lpVtbl->Suspend(This,pdwCount)
#define IRemoteDebugApplicationThread_Resume(This,pdwCount) (This)->lpVtbl->Resume(This,pdwCount)
#define IRemoteDebugApplicationThread_GetSuspendCount(This,pdwCount) (This)->lpVtbl->GetSuspendCount(This,pdwCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IRemoteDebugApplicationThread_QueryInterface(IRemoteDebugApplicationThread* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRemoteDebugApplicationThread_AddRef(IRemoteDebugApplicationThread* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRemoteDebugApplicationThread_Release(IRemoteDebugApplicationThread* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplicationThread methods ***/
static inline HRESULT IRemoteDebugApplicationThread_GetSystemThreadId(IRemoteDebugApplicationThread* This,DWORD *dwThreadId) {
    return This->lpVtbl->GetSystemThreadId(This,dwThreadId);
}
static inline HRESULT IRemoteDebugApplicationThread_GetApplication(IRemoteDebugApplicationThread* This,IRemoteDebugApplication **pprda) {
    return This->lpVtbl->GetApplication(This,pprda);
}
static inline HRESULT IRemoteDebugApplicationThread_EnumStackFrames(IRemoteDebugApplicationThread* This,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->EnumStackFrames(This,ppedsf);
}
static inline HRESULT IRemoteDebugApplicationThread_GetDescription(IRemoteDebugApplicationThread* This,BSTR *pbstrDescription,BSTR *pbstrState) {
    return This->lpVtbl->GetDescription(This,pbstrDescription,pbstrState);
}
static inline HRESULT IRemoteDebugApplicationThread_SetNextStatement(IRemoteDebugApplicationThread* This,IDebugStackFrame *pStackFrame,IDebugCodeContext *pCodeContext) {
    return This->lpVtbl->SetNextStatement(This,pStackFrame,pCodeContext);
}
static inline HRESULT IRemoteDebugApplicationThread_GetState(IRemoteDebugApplicationThread* This,DWORD *pState) {
    return This->lpVtbl->GetState(This,pState);
}
static inline HRESULT IRemoteDebugApplicationThread_Suspend(IRemoteDebugApplicationThread* This,DWORD *pdwCount) {
    return This->lpVtbl->Suspend(This,pdwCount);
}
static inline HRESULT IRemoteDebugApplicationThread_Resume(IRemoteDebugApplicationThread* This,DWORD *pdwCount) {
    return This->lpVtbl->Resume(This,pdwCount);
}
static inline HRESULT IRemoteDebugApplicationThread_GetSuspendCount(IRemoteDebugApplicationThread* This,DWORD *pdwCount) {
    return This->lpVtbl->GetSuspendCount(This,pdwCount);
}
#endif
#endif

#endif


#endif  /* __IRemoteDebugApplicationThread_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplicationThread interface
 */
#ifndef __IDebugApplicationThread_INTERFACE_DEFINED__
#define __IDebugApplicationThread_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplicationThread, 0x51973c38, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c38-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugApplicationThread : public IRemoteDebugApplicationThread
{
    virtual HRESULT STDMETHODCALLTYPE SynchronousCallIntoThread32(
        IDebugThreadCall32 *pstcb,
        DWORD dwParam1,
        DWORD dwParam2,
        DWORD dwParam3) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryIsCurrentThread(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryIsDebuggerThread(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDescription(
        LPCOLESTR pstrDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStateString(
        LPCOLESTR pstrState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplicationThread, 0x51973c38, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugApplicationThreadVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplicationThread *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplicationThread *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplicationThread *This);

    /*** IRemoteDebugApplicationThread methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSystemThreadId)(
        IDebugApplicationThread *This,
        DWORD *dwThreadId);

    HRESULT (STDMETHODCALLTYPE *GetApplication)(
        IDebugApplicationThread *This,
        IRemoteDebugApplication **pprda);

    HRESULT (STDMETHODCALLTYPE *EnumStackFrames)(
        IDebugApplicationThread *This,
        IEnumDebugStackFrames **ppedsf);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IDebugApplicationThread *This,
        BSTR *pbstrDescription,
        BSTR *pbstrState);

    HRESULT (STDMETHODCALLTYPE *SetNextStatement)(
        IDebugApplicationThread *This,
        IDebugStackFrame *pStackFrame,
        IDebugCodeContext *pCodeContext);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IDebugApplicationThread *This,
        DWORD *pState);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IDebugApplicationThread *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IDebugApplicationThread *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *GetSuspendCount)(
        IDebugApplicationThread *This,
        DWORD *pdwCount);

    /*** IDebugApplicationThread methods ***/
    HRESULT (STDMETHODCALLTYPE *SynchronousCallIntoThread32)(
        IDebugApplicationThread *This,
        IDebugThreadCall32 *pstcb,
        DWORD dwParam1,
        DWORD dwParam2,
        DWORD dwParam3);

    HRESULT (STDMETHODCALLTYPE *QueryIsCurrentThread)(
        IDebugApplicationThread *This);

    HRESULT (STDMETHODCALLTYPE *QueryIsDebuggerThread)(
        IDebugApplicationThread *This);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        IDebugApplicationThread *This,
        LPCOLESTR pstrDescription);

    HRESULT (STDMETHODCALLTYPE *SetStateString)(
        IDebugApplicationThread *This,
        LPCOLESTR pstrState);

    END_INTERFACE
} IDebugApplicationThreadVtbl;

interface IDebugApplicationThread {
    CONST_VTBL IDebugApplicationThreadVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplicationThread_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplicationThread_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplicationThread_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplicationThread methods ***/
#define IDebugApplicationThread_GetSystemThreadId(This,dwThreadId) (This)->lpVtbl->GetSystemThreadId(This,dwThreadId)
#define IDebugApplicationThread_GetApplication(This,pprda) (This)->lpVtbl->GetApplication(This,pprda)
#define IDebugApplicationThread_EnumStackFrames(This,ppedsf) (This)->lpVtbl->EnumStackFrames(This,ppedsf)
#define IDebugApplicationThread_GetDescription(This,pbstrDescription,pbstrState) (This)->lpVtbl->GetDescription(This,pbstrDescription,pbstrState)
#define IDebugApplicationThread_SetNextStatement(This,pStackFrame,pCodeContext) (This)->lpVtbl->SetNextStatement(This,pStackFrame,pCodeContext)
#define IDebugApplicationThread_GetState(This,pState) (This)->lpVtbl->GetState(This,pState)
#define IDebugApplicationThread_Suspend(This,pdwCount) (This)->lpVtbl->Suspend(This,pdwCount)
#define IDebugApplicationThread_Resume(This,pdwCount) (This)->lpVtbl->Resume(This,pdwCount)
#define IDebugApplicationThread_GetSuspendCount(This,pdwCount) (This)->lpVtbl->GetSuspendCount(This,pdwCount)
/*** IDebugApplicationThread methods ***/
#define IDebugApplicationThread_SynchronousCallIntoThread32(This,pstcb,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->SynchronousCallIntoThread32(This,pstcb,dwParam1,dwParam2,dwParam3)
#define IDebugApplicationThread_QueryIsCurrentThread(This) (This)->lpVtbl->QueryIsCurrentThread(This)
#define IDebugApplicationThread_QueryIsDebuggerThread(This) (This)->lpVtbl->QueryIsDebuggerThread(This)
#define IDebugApplicationThread_SetDescription(This,pstrDescription) (This)->lpVtbl->SetDescription(This,pstrDescription)
#define IDebugApplicationThread_SetStateString(This,pstrState) (This)->lpVtbl->SetStateString(This,pstrState)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugApplicationThread_QueryInterface(IDebugApplicationThread* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugApplicationThread_AddRef(IDebugApplicationThread* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugApplicationThread_Release(IDebugApplicationThread* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplicationThread methods ***/
static inline HRESULT IDebugApplicationThread_GetSystemThreadId(IDebugApplicationThread* This,DWORD *dwThreadId) {
    return This->lpVtbl->GetSystemThreadId(This,dwThreadId);
}
static inline HRESULT IDebugApplicationThread_GetApplication(IDebugApplicationThread* This,IRemoteDebugApplication **pprda) {
    return This->lpVtbl->GetApplication(This,pprda);
}
static inline HRESULT IDebugApplicationThread_EnumStackFrames(IDebugApplicationThread* This,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->EnumStackFrames(This,ppedsf);
}
static inline HRESULT IDebugApplicationThread_GetDescription(IDebugApplicationThread* This,BSTR *pbstrDescription,BSTR *pbstrState) {
    return This->lpVtbl->GetDescription(This,pbstrDescription,pbstrState);
}
static inline HRESULT IDebugApplicationThread_SetNextStatement(IDebugApplicationThread* This,IDebugStackFrame *pStackFrame,IDebugCodeContext *pCodeContext) {
    return This->lpVtbl->SetNextStatement(This,pStackFrame,pCodeContext);
}
static inline HRESULT IDebugApplicationThread_GetState(IDebugApplicationThread* This,DWORD *pState) {
    return This->lpVtbl->GetState(This,pState);
}
static inline HRESULT IDebugApplicationThread_Suspend(IDebugApplicationThread* This,DWORD *pdwCount) {
    return This->lpVtbl->Suspend(This,pdwCount);
}
static inline HRESULT IDebugApplicationThread_Resume(IDebugApplicationThread* This,DWORD *pdwCount) {
    return This->lpVtbl->Resume(This,pdwCount);
}
static inline HRESULT IDebugApplicationThread_GetSuspendCount(IDebugApplicationThread* This,DWORD *pdwCount) {
    return This->lpVtbl->GetSuspendCount(This,pdwCount);
}
/*** IDebugApplicationThread methods ***/
static inline HRESULT IDebugApplicationThread_SynchronousCallIntoThread32(IDebugApplicationThread* This,IDebugThreadCall32 *pstcb,DWORD dwParam1,DWORD dwParam2,DWORD dwParam3) {
    return This->lpVtbl->SynchronousCallIntoThread32(This,pstcb,dwParam1,dwParam2,dwParam3);
}
static inline HRESULT IDebugApplicationThread_QueryIsCurrentThread(IDebugApplicationThread* This) {
    return This->lpVtbl->QueryIsCurrentThread(This);
}
static inline HRESULT IDebugApplicationThread_QueryIsDebuggerThread(IDebugApplicationThread* This) {
    return This->lpVtbl->QueryIsDebuggerThread(This);
}
static inline HRESULT IDebugApplicationThread_SetDescription(IDebugApplicationThread* This,LPCOLESTR pstrDescription) {
    return This->lpVtbl->SetDescription(This,pstrDescription);
}
static inline HRESULT IDebugApplicationThread_SetStateString(IDebugApplicationThread* This,LPCOLESTR pstrState) {
    return This->lpVtbl->SetStateString(This,pstrState);
}
#endif
#endif

#endif


#endif  /* __IDebugApplicationThread_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugApplicationThread64 interface
 */
#ifndef __IDebugApplicationThread64_INTERFACE_DEFINED__
#define __IDebugApplicationThread64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugApplicationThread64, 0x9dac5886, 0xdbad, 0x456d, 0x9d,0xee, 0x5d,0xec,0x39,0xab,0x3d,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9dac5886-dbad-456d-9dee-5dec39ab3dda")
IDebugApplicationThread64 : public IDebugApplicationThread
{
    virtual HRESULT STDMETHODCALLTYPE SynchronousCallIntoThread64(
        IDebugThreadCall64 *pstcb,
        DWORDLONG dwParam1,
        DWORDLONG dwParam2,
        DWORDLONG dwParam3) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugApplicationThread64, 0x9dac5886, 0xdbad, 0x456d, 0x9d,0xee, 0x5d,0xec,0x39,0xab,0x3d,0xda)
#endif
#else
typedef struct IDebugApplicationThread64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugApplicationThread64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugApplicationThread64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugApplicationThread64 *This);

    /*** IRemoteDebugApplicationThread methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSystemThreadId)(
        IDebugApplicationThread64 *This,
        DWORD *dwThreadId);

    HRESULT (STDMETHODCALLTYPE *GetApplication)(
        IDebugApplicationThread64 *This,
        IRemoteDebugApplication **pprda);

    HRESULT (STDMETHODCALLTYPE *EnumStackFrames)(
        IDebugApplicationThread64 *This,
        IEnumDebugStackFrames **ppedsf);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IDebugApplicationThread64 *This,
        BSTR *pbstrDescription,
        BSTR *pbstrState);

    HRESULT (STDMETHODCALLTYPE *SetNextStatement)(
        IDebugApplicationThread64 *This,
        IDebugStackFrame *pStackFrame,
        IDebugCodeContext *pCodeContext);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IDebugApplicationThread64 *This,
        DWORD *pState);

    HRESULT (STDMETHODCALLTYPE *Suspend)(
        IDebugApplicationThread64 *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IDebugApplicationThread64 *This,
        DWORD *pdwCount);

    HRESULT (STDMETHODCALLTYPE *GetSuspendCount)(
        IDebugApplicationThread64 *This,
        DWORD *pdwCount);

    /*** IDebugApplicationThread methods ***/
    HRESULT (STDMETHODCALLTYPE *SynchronousCallIntoThread32)(
        IDebugApplicationThread64 *This,
        IDebugThreadCall32 *pstcb,
        DWORD dwParam1,
        DWORD dwParam2,
        DWORD dwParam3);

    HRESULT (STDMETHODCALLTYPE *QueryIsCurrentThread)(
        IDebugApplicationThread64 *This);

    HRESULT (STDMETHODCALLTYPE *QueryIsDebuggerThread)(
        IDebugApplicationThread64 *This);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        IDebugApplicationThread64 *This,
        LPCOLESTR pstrDescription);

    HRESULT (STDMETHODCALLTYPE *SetStateString)(
        IDebugApplicationThread64 *This,
        LPCOLESTR pstrState);

    /*** IDebugApplicationThread64 methods ***/
    HRESULT (STDMETHODCALLTYPE *SynchronousCallIntoThread64)(
        IDebugApplicationThread64 *This,
        IDebugThreadCall64 *pstcb,
        DWORDLONG dwParam1,
        DWORDLONG dwParam2,
        DWORDLONG dwParam3);

    END_INTERFACE
} IDebugApplicationThread64Vtbl;

interface IDebugApplicationThread64 {
    CONST_VTBL IDebugApplicationThread64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugApplicationThread64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugApplicationThread64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugApplicationThread64_Release(This) (This)->lpVtbl->Release(This)
/*** IRemoteDebugApplicationThread methods ***/
#define IDebugApplicationThread64_GetSystemThreadId(This,dwThreadId) (This)->lpVtbl->GetSystemThreadId(This,dwThreadId)
#define IDebugApplicationThread64_GetApplication(This,pprda) (This)->lpVtbl->GetApplication(This,pprda)
#define IDebugApplicationThread64_EnumStackFrames(This,ppedsf) (This)->lpVtbl->EnumStackFrames(This,ppedsf)
#define IDebugApplicationThread64_GetDescription(This,pbstrDescription,pbstrState) (This)->lpVtbl->GetDescription(This,pbstrDescription,pbstrState)
#define IDebugApplicationThread64_SetNextStatement(This,pStackFrame,pCodeContext) (This)->lpVtbl->SetNextStatement(This,pStackFrame,pCodeContext)
#define IDebugApplicationThread64_GetState(This,pState) (This)->lpVtbl->GetState(This,pState)
#define IDebugApplicationThread64_Suspend(This,pdwCount) (This)->lpVtbl->Suspend(This,pdwCount)
#define IDebugApplicationThread64_Resume(This,pdwCount) (This)->lpVtbl->Resume(This,pdwCount)
#define IDebugApplicationThread64_GetSuspendCount(This,pdwCount) (This)->lpVtbl->GetSuspendCount(This,pdwCount)
/*** IDebugApplicationThread methods ***/
#define IDebugApplicationThread64_SynchronousCallIntoThread32(This,pstcb,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->SynchronousCallIntoThread32(This,pstcb,dwParam1,dwParam2,dwParam3)
#define IDebugApplicationThread64_QueryIsCurrentThread(This) (This)->lpVtbl->QueryIsCurrentThread(This)
#define IDebugApplicationThread64_QueryIsDebuggerThread(This) (This)->lpVtbl->QueryIsDebuggerThread(This)
#define IDebugApplicationThread64_SetDescription(This,pstrDescription) (This)->lpVtbl->SetDescription(This,pstrDescription)
#define IDebugApplicationThread64_SetStateString(This,pstrState) (This)->lpVtbl->SetStateString(This,pstrState)
/*** IDebugApplicationThread64 methods ***/
#define IDebugApplicationThread64_SynchronousCallIntoThread64(This,pstcb,dwParam1,dwParam2,dwParam3) (This)->lpVtbl->SynchronousCallIntoThread64(This,pstcb,dwParam1,dwParam2,dwParam3)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugApplicationThread64_QueryInterface(IDebugApplicationThread64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugApplicationThread64_AddRef(IDebugApplicationThread64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugApplicationThread64_Release(IDebugApplicationThread64* This) {
    return This->lpVtbl->Release(This);
}
/*** IRemoteDebugApplicationThread methods ***/
static inline HRESULT IDebugApplicationThread64_GetSystemThreadId(IDebugApplicationThread64* This,DWORD *dwThreadId) {
    return This->lpVtbl->GetSystemThreadId(This,dwThreadId);
}
static inline HRESULT IDebugApplicationThread64_GetApplication(IDebugApplicationThread64* This,IRemoteDebugApplication **pprda) {
    return This->lpVtbl->GetApplication(This,pprda);
}
static inline HRESULT IDebugApplicationThread64_EnumStackFrames(IDebugApplicationThread64* This,IEnumDebugStackFrames **ppedsf) {
    return This->lpVtbl->EnumStackFrames(This,ppedsf);
}
static inline HRESULT IDebugApplicationThread64_GetDescription(IDebugApplicationThread64* This,BSTR *pbstrDescription,BSTR *pbstrState) {
    return This->lpVtbl->GetDescription(This,pbstrDescription,pbstrState);
}
static inline HRESULT IDebugApplicationThread64_SetNextStatement(IDebugApplicationThread64* This,IDebugStackFrame *pStackFrame,IDebugCodeContext *pCodeContext) {
    return This->lpVtbl->SetNextStatement(This,pStackFrame,pCodeContext);
}
static inline HRESULT IDebugApplicationThread64_GetState(IDebugApplicationThread64* This,DWORD *pState) {
    return This->lpVtbl->GetState(This,pState);
}
static inline HRESULT IDebugApplicationThread64_Suspend(IDebugApplicationThread64* This,DWORD *pdwCount) {
    return This->lpVtbl->Suspend(This,pdwCount);
}
static inline HRESULT IDebugApplicationThread64_Resume(IDebugApplicationThread64* This,DWORD *pdwCount) {
    return This->lpVtbl->Resume(This,pdwCount);
}
static inline HRESULT IDebugApplicationThread64_GetSuspendCount(IDebugApplicationThread64* This,DWORD *pdwCount) {
    return This->lpVtbl->GetSuspendCount(This,pdwCount);
}
/*** IDebugApplicationThread methods ***/
static inline HRESULT IDebugApplicationThread64_SynchronousCallIntoThread32(IDebugApplicationThread64* This,IDebugThreadCall32 *pstcb,DWORD dwParam1,DWORD dwParam2,DWORD dwParam3) {
    return This->lpVtbl->SynchronousCallIntoThread32(This,pstcb,dwParam1,dwParam2,dwParam3);
}
static inline HRESULT IDebugApplicationThread64_QueryIsCurrentThread(IDebugApplicationThread64* This) {
    return This->lpVtbl->QueryIsCurrentThread(This);
}
static inline HRESULT IDebugApplicationThread64_QueryIsDebuggerThread(IDebugApplicationThread64* This) {
    return This->lpVtbl->QueryIsDebuggerThread(This);
}
static inline HRESULT IDebugApplicationThread64_SetDescription(IDebugApplicationThread64* This,LPCOLESTR pstrDescription) {
    return This->lpVtbl->SetDescription(This,pstrDescription);
}
static inline HRESULT IDebugApplicationThread64_SetStateString(IDebugApplicationThread64* This,LPCOLESTR pstrState) {
    return This->lpVtbl->SetStateString(This,pstrState);
}
/*** IDebugApplicationThread64 methods ***/
static inline HRESULT IDebugApplicationThread64_SynchronousCallIntoThread64(IDebugApplicationThread64* This,IDebugThreadCall64 *pstcb,DWORDLONG dwParam1,DWORDLONG dwParam2,DWORDLONG dwParam3) {
    return This->lpVtbl->SynchronousCallIntoThread64(This,pstcb,dwParam1,dwParam2,dwParam3);
}
#endif
#endif

#endif


#endif  /* __IDebugApplicationThread64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugCookie interface
 */
#ifndef __IDebugCookie_INTERFACE_DEFINED__
#define __IDebugCookie_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugCookie, 0x51973c39, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c39-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugCookie : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetDebugCookie(
        DWORD dwDebugAppCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugCookie, 0x51973c39, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugCookieVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugCookie *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugCookie *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugCookie *This);

    /*** IDebugCookie methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDebugCookie)(
        IDebugCookie *This,
        DWORD dwDebugAppCookie);

    END_INTERFACE
} IDebugCookieVtbl;

interface IDebugCookie {
    CONST_VTBL IDebugCookieVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugCookie_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugCookie_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugCookie_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugCookie methods ***/
#define IDebugCookie_SetDebugCookie(This,dwDebugAppCookie) (This)->lpVtbl->SetDebugCookie(This,dwDebugAppCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugCookie_QueryInterface(IDebugCookie* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugCookie_AddRef(IDebugCookie* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugCookie_Release(IDebugCookie* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugCookie methods ***/
static inline HRESULT IDebugCookie_SetDebugCookie(IDebugCookie* This,DWORD dwDebugAppCookie) {
    return This->lpVtbl->SetDebugCookie(This,dwDebugAppCookie);
}
#endif
#endif

#endif


#endif  /* __IDebugCookie_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumDebugApplicationNodes interface
 */
#ifndef __IEnumDebugApplicationNodes_INTERFACE_DEFINED__
#define __IEnumDebugApplicationNodes_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDebugApplicationNodes, 0x51973c3a, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c3a-cb0c-11d0-b5c9-00a0244a0e7a")
IEnumDebugApplicationNodes : public IUnknown
{
    virtual HRESULT __stdcall Next(
        ULONG celt,
        IDebugApplicationNode **pprddp,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDebugApplicationNodes **pperddp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDebugApplicationNodes, 0x51973c3a, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IEnumDebugApplicationNodesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDebugApplicationNodes *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDebugApplicationNodes *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDebugApplicationNodes *This);

    /*** IEnumDebugApplicationNodes methods ***/
    HRESULT (__stdcall *Next)(
        IEnumDebugApplicationNodes *This,
        ULONG celt,
        IDebugApplicationNode **pprddp,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDebugApplicationNodes *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDebugApplicationNodes *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDebugApplicationNodes *This,
        IEnumDebugApplicationNodes **pperddp);

    END_INTERFACE
} IEnumDebugApplicationNodesVtbl;

interface IEnumDebugApplicationNodes {
    CONST_VTBL IEnumDebugApplicationNodesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDebugApplicationNodes_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDebugApplicationNodes_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDebugApplicationNodes_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDebugApplicationNodes methods ***/
#define IEnumDebugApplicationNodes_Next(This,celt,pprddp,pceltFetched) (This)->lpVtbl->Next(This,celt,pprddp,pceltFetched)
#define IEnumDebugApplicationNodes_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumDebugApplicationNodes_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDebugApplicationNodes_Clone(This,pperddp) (This)->lpVtbl->Clone(This,pperddp)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDebugApplicationNodes_QueryInterface(IEnumDebugApplicationNodes* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDebugApplicationNodes_AddRef(IEnumDebugApplicationNodes* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDebugApplicationNodes_Release(IEnumDebugApplicationNodes* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDebugApplicationNodes methods ***/
static inline HRESULT IEnumDebugApplicationNodes_Next(IEnumDebugApplicationNodes* This,ULONG celt,IDebugApplicationNode **pprddp,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,pprddp,pceltFetched);
}
static inline HRESULT IEnumDebugApplicationNodes_Skip(IEnumDebugApplicationNodes* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumDebugApplicationNodes_Reset(IEnumDebugApplicationNodes* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDebugApplicationNodes_Clone(IEnumDebugApplicationNodes* This,IEnumDebugApplicationNodes **pperddp) {
    return This->lpVtbl->Clone(This,pperddp);
}
#endif
#endif

#endif

HRESULT __stdcall IEnumDebugApplicationNodes_RemoteNext_Proxy(
    IEnumDebugApplicationNodes* This,
    ULONG celt,
    IDebugApplicationNode **pprddp,
    ULONG *pceltFetched);
void __RPC_STUB IEnumDebugApplicationNodes_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumDebugApplicationNodes_Next_Proxy(
    IEnumDebugApplicationNodes* This,
    ULONG celt,
    IDebugApplicationNode **pprddp,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumDebugApplicationNodes_Next_Stub(
    IEnumDebugApplicationNodes* This,
    ULONG celt,
    IDebugApplicationNode **pprddp,
    ULONG *pceltFetched);

#endif  /* __IEnumDebugApplicationNodes_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumRemoteDebugApplications interface
 */
#ifndef __IEnumRemoteDebugApplications_INTERFACE_DEFINED__
#define __IEnumRemoteDebugApplications_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumRemoteDebugApplications, 0x51973c3b, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c3b-cb0c-11d0-b5c9-00a0244a0e7a")
IEnumRemoteDebugApplications : public IUnknown
{
    virtual HRESULT __stdcall Next(
        ULONG celt,
        IRemoteDebugApplication **ppda,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumRemoteDebugApplications **ppessd) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumRemoteDebugApplications, 0x51973c3b, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IEnumRemoteDebugApplicationsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumRemoteDebugApplications *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumRemoteDebugApplications *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumRemoteDebugApplications *This);

    /*** IEnumRemoteDebugApplications methods ***/
    HRESULT (__stdcall *Next)(
        IEnumRemoteDebugApplications *This,
        ULONG celt,
        IRemoteDebugApplication **ppda,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumRemoteDebugApplications *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumRemoteDebugApplications *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumRemoteDebugApplications *This,
        IEnumRemoteDebugApplications **ppessd);

    END_INTERFACE
} IEnumRemoteDebugApplicationsVtbl;

interface IEnumRemoteDebugApplications {
    CONST_VTBL IEnumRemoteDebugApplicationsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumRemoteDebugApplications_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumRemoteDebugApplications_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumRemoteDebugApplications_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumRemoteDebugApplications methods ***/
#define IEnumRemoteDebugApplications_Next(This,celt,ppda,pceltFetched) (This)->lpVtbl->Next(This,celt,ppda,pceltFetched)
#define IEnumRemoteDebugApplications_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumRemoteDebugApplications_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumRemoteDebugApplications_Clone(This,ppessd) (This)->lpVtbl->Clone(This,ppessd)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumRemoteDebugApplications_QueryInterface(IEnumRemoteDebugApplications* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumRemoteDebugApplications_AddRef(IEnumRemoteDebugApplications* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumRemoteDebugApplications_Release(IEnumRemoteDebugApplications* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumRemoteDebugApplications methods ***/
static inline HRESULT IEnumRemoteDebugApplications_Next(IEnumRemoteDebugApplications* This,ULONG celt,IRemoteDebugApplication **ppda,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,ppda,pceltFetched);
}
static inline HRESULT IEnumRemoteDebugApplications_Skip(IEnumRemoteDebugApplications* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumRemoteDebugApplications_Reset(IEnumRemoteDebugApplications* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumRemoteDebugApplications_Clone(IEnumRemoteDebugApplications* This,IEnumRemoteDebugApplications **ppessd) {
    return This->lpVtbl->Clone(This,ppessd);
}
#endif
#endif

#endif

HRESULT __stdcall IEnumRemoteDebugApplications_RemoteNext_Proxy(
    IEnumRemoteDebugApplications* This,
    ULONG celt,
    IRemoteDebugApplication **ppda,
    ULONG *pceltFetched);
void __RPC_STUB IEnumRemoteDebugApplications_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumRemoteDebugApplications_Next_Proxy(
    IEnumRemoteDebugApplications* This,
    ULONG celt,
    IRemoteDebugApplication **ppda,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumRemoteDebugApplications_Next_Stub(
    IEnumRemoteDebugApplications* This,
    ULONG celt,
    IRemoteDebugApplication **ppda,
    ULONG *pceltFetched);

#endif  /* __IEnumRemoteDebugApplications_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumRemoteDebugApplicationThreads interface
 */
#ifndef __IEnumRemoteDebugApplicationThreads_INTERFACE_DEFINED__
#define __IEnumRemoteDebugApplicationThreads_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumRemoteDebugApplicationThreads, 0x51973c3c, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c3c-cb0c-11d0-b5c9-00a0244a0e7a")
IEnumRemoteDebugApplicationThreads : public IUnknown
{
    virtual HRESULT __stdcall Next(
        ULONG celt,
        IRemoteDebugApplicationThread **pprdat,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumRemoteDebugApplicationThreads **pperdat) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumRemoteDebugApplicationThreads, 0x51973c3c, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IEnumRemoteDebugApplicationThreadsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumRemoteDebugApplicationThreads *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumRemoteDebugApplicationThreads *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumRemoteDebugApplicationThreads *This);

    /*** IEnumRemoteDebugApplicationThreads methods ***/
    HRESULT (__stdcall *Next)(
        IEnumRemoteDebugApplicationThreads *This,
        ULONG celt,
        IRemoteDebugApplicationThread **pprdat,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumRemoteDebugApplicationThreads *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumRemoteDebugApplicationThreads *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumRemoteDebugApplicationThreads *This,
        IEnumRemoteDebugApplicationThreads **pperdat);

    END_INTERFACE
} IEnumRemoteDebugApplicationThreadsVtbl;

interface IEnumRemoteDebugApplicationThreads {
    CONST_VTBL IEnumRemoteDebugApplicationThreadsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumRemoteDebugApplicationThreads_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumRemoteDebugApplicationThreads_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumRemoteDebugApplicationThreads_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumRemoteDebugApplicationThreads methods ***/
#define IEnumRemoteDebugApplicationThreads_Next(This,celt,pprdat,pceltFetched) (This)->lpVtbl->Next(This,celt,pprdat,pceltFetched)
#define IEnumRemoteDebugApplicationThreads_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumRemoteDebugApplicationThreads_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumRemoteDebugApplicationThreads_Clone(This,pperdat) (This)->lpVtbl->Clone(This,pperdat)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumRemoteDebugApplicationThreads_QueryInterface(IEnumRemoteDebugApplicationThreads* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumRemoteDebugApplicationThreads_AddRef(IEnumRemoteDebugApplicationThreads* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumRemoteDebugApplicationThreads_Release(IEnumRemoteDebugApplicationThreads* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumRemoteDebugApplicationThreads methods ***/
static inline HRESULT IEnumRemoteDebugApplicationThreads_Next(IEnumRemoteDebugApplicationThreads* This,ULONG celt,IRemoteDebugApplicationThread **pprdat,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,pprdat,pceltFetched);
}
static inline HRESULT IEnumRemoteDebugApplicationThreads_Skip(IEnumRemoteDebugApplicationThreads* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumRemoteDebugApplicationThreads_Reset(IEnumRemoteDebugApplicationThreads* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumRemoteDebugApplicationThreads_Clone(IEnumRemoteDebugApplicationThreads* This,IEnumRemoteDebugApplicationThreads **pperdat) {
    return This->lpVtbl->Clone(This,pperdat);
}
#endif
#endif

#endif

HRESULT __stdcall IEnumRemoteDebugApplicationThreads_RemoteNext_Proxy(
    IEnumRemoteDebugApplicationThreads* This,
    ULONG celt,
    IRemoteDebugApplicationThread **ppdat,
    ULONG *pceltFetched);
void __RPC_STUB IEnumRemoteDebugApplicationThreads_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumRemoteDebugApplicationThreads_Next_Proxy(
    IEnumRemoteDebugApplicationThreads* This,
    ULONG celt,
    IRemoteDebugApplicationThread **pprdat,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumRemoteDebugApplicationThreads_Next_Stub(
    IEnumRemoteDebugApplicationThreads* This,
    ULONG celt,
    IRemoteDebugApplicationThread **ppdat,
    ULONG *pceltFetched);

#endif  /* __IEnumRemoteDebugApplicationThreads_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugFormatter interface
 */
#ifndef __IDebugFormatter_INTERFACE_DEFINED__
#define __IDebugFormatter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugFormatter, 0x51973c05, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c05-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugFormatter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStringForVariant(
        VARIANT *pvar,
        ULONG nRadix,
        BSTR *pbstrValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVariantForString(
        LPCOLESTR pwstrValue,
        VARIANT *pvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringForVarType(
        VARTYPE vt,
        TYPEDESC *ptdescArrayType,
        BSTR *pbstr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugFormatter, 0x51973c05, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugFormatterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugFormatter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugFormatter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugFormatter *This);

    /*** IDebugFormatter methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStringForVariant)(
        IDebugFormatter *This,
        VARIANT *pvar,
        ULONG nRadix,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *GetVariantForString)(
        IDebugFormatter *This,
        LPCOLESTR pwstrValue,
        VARIANT *pvar);

    HRESULT (STDMETHODCALLTYPE *GetStringForVarType)(
        IDebugFormatter *This,
        VARTYPE vt,
        TYPEDESC *ptdescArrayType,
        BSTR *pbstr);

    END_INTERFACE
} IDebugFormatterVtbl;

interface IDebugFormatter {
    CONST_VTBL IDebugFormatterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugFormatter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugFormatter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugFormatter_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugFormatter methods ***/
#define IDebugFormatter_GetStringForVariant(This,pvar,nRadix,pbstrValue) (This)->lpVtbl->GetStringForVariant(This,pvar,nRadix,pbstrValue)
#define IDebugFormatter_GetVariantForString(This,pwstrValue,pvar) (This)->lpVtbl->GetVariantForString(This,pwstrValue,pvar)
#define IDebugFormatter_GetStringForVarType(This,vt,ptdescArrayType,pbstr) (This)->lpVtbl->GetStringForVarType(This,vt,ptdescArrayType,pbstr)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugFormatter_QueryInterface(IDebugFormatter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugFormatter_AddRef(IDebugFormatter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugFormatter_Release(IDebugFormatter* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugFormatter methods ***/
static inline HRESULT IDebugFormatter_GetStringForVariant(IDebugFormatter* This,VARIANT *pvar,ULONG nRadix,BSTR *pbstrValue) {
    return This->lpVtbl->GetStringForVariant(This,pvar,nRadix,pbstrValue);
}
static inline HRESULT IDebugFormatter_GetVariantForString(IDebugFormatter* This,LPCOLESTR pwstrValue,VARIANT *pvar) {
    return This->lpVtbl->GetVariantForString(This,pwstrValue,pvar);
}
static inline HRESULT IDebugFormatter_GetStringForVarType(IDebugFormatter* This,VARTYPE vt,TYPEDESC *ptdescArrayType,BSTR *pbstr) {
    return This->lpVtbl->GetStringForVarType(This,vt,ptdescArrayType,pbstr);
}
#endif
#endif

#endif


#endif  /* __IDebugFormatter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISimpleConnectionPoint interface
 */
#ifndef __ISimpleConnectionPoint_INTERFACE_DEFINED__
#define __ISimpleConnectionPoint_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISimpleConnectionPoint, 0x51973c3e, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c3e-cb0c-11d0-b5c9-00a0244a0e7a")
ISimpleConnectionPoint : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEventCount(
        ULONG *pulCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE DescribeEvents(
        ULONG iEvent,
        ULONG cEvents,
        DISPID *prgid,
        BSTR *prgbstr,
        ULONG *pcEventsFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Advise(
        IDispatch *pdisp,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unadvise(
        DWORD dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISimpleConnectionPoint, 0x51973c3e, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct ISimpleConnectionPointVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISimpleConnectionPoint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISimpleConnectionPoint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISimpleConnectionPoint *This);

    /*** ISimpleConnectionPoint methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEventCount)(
        ISimpleConnectionPoint *This,
        ULONG *pulCount);

    HRESULT (STDMETHODCALLTYPE *DescribeEvents)(
        ISimpleConnectionPoint *This,
        ULONG iEvent,
        ULONG cEvents,
        DISPID *prgid,
        BSTR *prgbstr,
        ULONG *pcEventsFetched);

    HRESULT (STDMETHODCALLTYPE *Advise)(
        ISimpleConnectionPoint *This,
        IDispatch *pdisp,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *Unadvise)(
        ISimpleConnectionPoint *This,
        DWORD dwCookie);

    END_INTERFACE
} ISimpleConnectionPointVtbl;

interface ISimpleConnectionPoint {
    CONST_VTBL ISimpleConnectionPointVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISimpleConnectionPoint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISimpleConnectionPoint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISimpleConnectionPoint_Release(This) (This)->lpVtbl->Release(This)
/*** ISimpleConnectionPoint methods ***/
#define ISimpleConnectionPoint_GetEventCount(This,pulCount) (This)->lpVtbl->GetEventCount(This,pulCount)
#define ISimpleConnectionPoint_DescribeEvents(This,iEvent,cEvents,prgid,prgbstr,pcEventsFetched) (This)->lpVtbl->DescribeEvents(This,iEvent,cEvents,prgid,prgbstr,pcEventsFetched)
#define ISimpleConnectionPoint_Advise(This,pdisp,pdwCookie) (This)->lpVtbl->Advise(This,pdisp,pdwCookie)
#define ISimpleConnectionPoint_Unadvise(This,dwCookie) (This)->lpVtbl->Unadvise(This,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT ISimpleConnectionPoint_QueryInterface(ISimpleConnectionPoint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISimpleConnectionPoint_AddRef(ISimpleConnectionPoint* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISimpleConnectionPoint_Release(ISimpleConnectionPoint* This) {
    return This->lpVtbl->Release(This);
}
/*** ISimpleConnectionPoint methods ***/
static inline HRESULT ISimpleConnectionPoint_GetEventCount(ISimpleConnectionPoint* This,ULONG *pulCount) {
    return This->lpVtbl->GetEventCount(This,pulCount);
}
static inline HRESULT ISimpleConnectionPoint_DescribeEvents(ISimpleConnectionPoint* This,ULONG iEvent,ULONG cEvents,DISPID *prgid,BSTR *prgbstr,ULONG *pcEventsFetched) {
    return This->lpVtbl->DescribeEvents(This,iEvent,cEvents,prgid,prgbstr,pcEventsFetched);
}
static inline HRESULT ISimpleConnectionPoint_Advise(ISimpleConnectionPoint* This,IDispatch *pdisp,DWORD *pdwCookie) {
    return This->lpVtbl->Advise(This,pdisp,pdwCookie);
}
static inline HRESULT ISimpleConnectionPoint_Unadvise(ISimpleConnectionPoint* This,DWORD dwCookie) {
    return This->lpVtbl->Unadvise(This,dwCookie);
}
#endif
#endif

#endif


#endif  /* __ISimpleConnectionPoint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDebugHelper interface
 */
#ifndef __IDebugHelper_INTERFACE_DEFINED__
#define __IDebugHelper_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugHelper, 0x51973c3f, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c3f-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugHelper : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreatePropertyBrowser(
        VARIANT *pvar,
        LPCOLESTR bstrName,
        IDebugApplicationThread *pdat,
        IDebugProperty **ppdob) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePropertyBrowserEx(
        VARIANT *pvar,
        LPCOLESTR bstrName,
        IDebugApplicationThread *pdat,
        IDebugFormatter *pdf,
        IDebugProperty **ppdob) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSimpleConnectionPoint(
        IDispatch *pdisp,
        ISimpleConnectionPoint **ppscp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugHelper, 0x51973c3f, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugHelperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugHelper *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugHelper *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugHelper *This);

    /*** IDebugHelper methods ***/
    HRESULT (STDMETHODCALLTYPE *CreatePropertyBrowser)(
        IDebugHelper *This,
        VARIANT *pvar,
        LPCOLESTR bstrName,
        IDebugApplicationThread *pdat,
        IDebugProperty **ppdob);

    HRESULT (STDMETHODCALLTYPE *CreatePropertyBrowserEx)(
        IDebugHelper *This,
        VARIANT *pvar,
        LPCOLESTR bstrName,
        IDebugApplicationThread *pdat,
        IDebugFormatter *pdf,
        IDebugProperty **ppdob);

    HRESULT (STDMETHODCALLTYPE *CreateSimpleConnectionPoint)(
        IDebugHelper *This,
        IDispatch *pdisp,
        ISimpleConnectionPoint **ppscp);

    END_INTERFACE
} IDebugHelperVtbl;

interface IDebugHelper {
    CONST_VTBL IDebugHelperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugHelper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugHelper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugHelper_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugHelper methods ***/
#define IDebugHelper_CreatePropertyBrowser(This,pvar,bstrName,pdat,ppdob) (This)->lpVtbl->CreatePropertyBrowser(This,pvar,bstrName,pdat,ppdob)
#define IDebugHelper_CreatePropertyBrowserEx(This,pvar,bstrName,pdat,pdf,ppdob) (This)->lpVtbl->CreatePropertyBrowserEx(This,pvar,bstrName,pdat,pdf,ppdob)
#define IDebugHelper_CreateSimpleConnectionPoint(This,pdisp,ppscp) (This)->lpVtbl->CreateSimpleConnectionPoint(This,pdisp,ppscp)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugHelper_QueryInterface(IDebugHelper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugHelper_AddRef(IDebugHelper* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugHelper_Release(IDebugHelper* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugHelper methods ***/
static inline HRESULT IDebugHelper_CreatePropertyBrowser(IDebugHelper* This,VARIANT *pvar,LPCOLESTR bstrName,IDebugApplicationThread *pdat,IDebugProperty **ppdob) {
    return This->lpVtbl->CreatePropertyBrowser(This,pvar,bstrName,pdat,ppdob);
}
static inline HRESULT IDebugHelper_CreatePropertyBrowserEx(IDebugHelper* This,VARIANT *pvar,LPCOLESTR bstrName,IDebugApplicationThread *pdat,IDebugFormatter *pdf,IDebugProperty **ppdob) {
    return This->lpVtbl->CreatePropertyBrowserEx(This,pvar,bstrName,pdat,pdf,ppdob);
}
static inline HRESULT IDebugHelper_CreateSimpleConnectionPoint(IDebugHelper* This,IDispatch *pdisp,ISimpleConnectionPoint **ppscp) {
    return This->lpVtbl->CreateSimpleConnectionPoint(This,pdisp,ppscp);
}
#endif
#endif

#endif


#endif  /* __IDebugHelper_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumDebugExpressionContexts interface
 */
#ifndef __IEnumDebugExpressionContexts_INTERFACE_DEFINED__
#define __IEnumDebugExpressionContexts_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDebugExpressionContexts, 0x51973c40, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c40-cb0c-11d0-b5c9-00a0244a0e7a")
IEnumDebugExpressionContexts : public IUnknown
{
    virtual HRESULT __stdcall Next(
        ULONG celt,
        IDebugExpressionContext **ppdec,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDebugExpressionContexts **ppedec) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDebugExpressionContexts, 0x51973c40, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IEnumDebugExpressionContextsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDebugExpressionContexts *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDebugExpressionContexts *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDebugExpressionContexts *This);

    /*** IEnumDebugExpressionContexts methods ***/
    HRESULT (__stdcall *Next)(
        IEnumDebugExpressionContexts *This,
        ULONG celt,
        IDebugExpressionContext **ppdec,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDebugExpressionContexts *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDebugExpressionContexts *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDebugExpressionContexts *This,
        IEnumDebugExpressionContexts **ppedec);

    END_INTERFACE
} IEnumDebugExpressionContextsVtbl;

interface IEnumDebugExpressionContexts {
    CONST_VTBL IEnumDebugExpressionContextsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDebugExpressionContexts_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDebugExpressionContexts_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDebugExpressionContexts_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDebugExpressionContexts methods ***/
#define IEnumDebugExpressionContexts_Next(This,celt,ppdec,pceltFetched) (This)->lpVtbl->Next(This,celt,ppdec,pceltFetched)
#define IEnumDebugExpressionContexts_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumDebugExpressionContexts_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDebugExpressionContexts_Clone(This,ppedec) (This)->lpVtbl->Clone(This,ppedec)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDebugExpressionContexts_QueryInterface(IEnumDebugExpressionContexts* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDebugExpressionContexts_AddRef(IEnumDebugExpressionContexts* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDebugExpressionContexts_Release(IEnumDebugExpressionContexts* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDebugExpressionContexts methods ***/
static inline HRESULT IEnumDebugExpressionContexts_Next(IEnumDebugExpressionContexts* This,ULONG celt,IDebugExpressionContext **ppdec,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,ppdec,pceltFetched);
}
static inline HRESULT IEnumDebugExpressionContexts_Skip(IEnumDebugExpressionContexts* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumDebugExpressionContexts_Reset(IEnumDebugExpressionContexts* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDebugExpressionContexts_Clone(IEnumDebugExpressionContexts* This,IEnumDebugExpressionContexts **ppedec) {
    return This->lpVtbl->Clone(This,ppedec);
}
#endif
#endif

#endif

HRESULT __stdcall IEnumDebugExpressionContexts_RemoteNext_Proxy(
    IEnumDebugExpressionContexts* This,
    ULONG celt,
    IDebugExpressionContext **pprgdec,
    ULONG *pceltFetched);
void __RPC_STUB IEnumDebugExpressionContexts_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumDebugExpressionContexts_Next_Proxy(
    IEnumDebugExpressionContexts* This,
    ULONG celt,
    IDebugExpressionContext **ppdec,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumDebugExpressionContexts_Next_Stub(
    IEnumDebugExpressionContexts* This,
    ULONG celt,
    IDebugExpressionContext **pprgdec,
    ULONG *pceltFetched);

#endif  /* __IEnumDebugExpressionContexts_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IProvideExpressionContexts interface
 */
#ifndef __IProvideExpressionContexts_INTERFACE_DEFINED__
#define __IProvideExpressionContexts_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProvideExpressionContexts, 0x51973c41, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c41-cb0c-11d0-b5c9-00a0244a0e7a")
IProvideExpressionContexts : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EnumExpressionContexts(
        IEnumDebugExpressionContexts **ppedec) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProvideExpressionContexts, 0x51973c41, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IProvideExpressionContextsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProvideExpressionContexts *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProvideExpressionContexts *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProvideExpressionContexts *This);

    /*** IProvideExpressionContexts methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumExpressionContexts)(
        IProvideExpressionContexts *This,
        IEnumDebugExpressionContexts **ppedec);

    END_INTERFACE
} IProvideExpressionContextsVtbl;

interface IProvideExpressionContexts {
    CONST_VTBL IProvideExpressionContextsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProvideExpressionContexts_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProvideExpressionContexts_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProvideExpressionContexts_Release(This) (This)->lpVtbl->Release(This)
/*** IProvideExpressionContexts methods ***/
#define IProvideExpressionContexts_EnumExpressionContexts(This,ppedec) (This)->lpVtbl->EnumExpressionContexts(This,ppedec)
#else
/*** IUnknown methods ***/
static inline HRESULT IProvideExpressionContexts_QueryInterface(IProvideExpressionContexts* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProvideExpressionContexts_AddRef(IProvideExpressionContexts* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProvideExpressionContexts_Release(IProvideExpressionContexts* This) {
    return This->lpVtbl->Release(This);
}
/*** IProvideExpressionContexts methods ***/
static inline HRESULT IProvideExpressionContexts_EnumExpressionContexts(IProvideExpressionContexts* This,IEnumDebugExpressionContexts **ppedec) {
    return This->lpVtbl->EnumExpressionContexts(This,ppedec);
}
#endif
#endif

#endif


#endif  /* __IProvideExpressionContexts_INTERFACE_DEFINED__ */

#ifndef __ProcessDebugManagerLib_LIBRARY_DEFINED__
#define __ProcessDebugManagerLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_ProcessDebugManagerLib, 0x78a51821, 0x51f4, 0x11d0, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);

#ifndef __IActiveScriptDebug32_FWD_DEFINED__
#define __IActiveScriptDebug32_FWD_DEFINED__
typedef interface IActiveScriptDebug32 IActiveScriptDebug32;
#ifdef __cplusplus
interface IActiveScriptDebug32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptDebug64_FWD_DEFINED__
#define __IActiveScriptDebug64_FWD_DEFINED__
typedef interface IActiveScriptDebug64 IActiveScriptDebug64;
#ifdef __cplusplus
interface IActiveScriptDebug64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptErrorDebug_FWD_DEFINED__
#define __IActiveScriptErrorDebug_FWD_DEFINED__
typedef interface IActiveScriptErrorDebug IActiveScriptErrorDebug;
#ifdef __cplusplus
interface IActiveScriptErrorDebug;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebug32_FWD_DEFINED__
#define __IActiveScriptSiteDebug32_FWD_DEFINED__
typedef interface IActiveScriptSiteDebug32 IActiveScriptSiteDebug32;
#ifdef __cplusplus
interface IActiveScriptSiteDebug32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebug64_FWD_DEFINED__
#define __IActiveScriptSiteDebug64_FWD_DEFINED__
typedef interface IActiveScriptSiteDebug64 IActiveScriptSiteDebug64;
#ifdef __cplusplus
interface IActiveScriptSiteDebug64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteDebugEx_FWD_DEFINED__
#define __IActiveScriptSiteDebugEx_FWD_DEFINED__
typedef interface IActiveScriptSiteDebugEx IActiveScriptSiteDebugEx;
#ifdef __cplusplus
interface IActiveScriptSiteDebugEx;
#endif /* __cplusplus */
#endif

#ifndef __IApplicationDebugger_FWD_DEFINED__
#define __IApplicationDebugger_FWD_DEFINED__
typedef interface IApplicationDebugger IApplicationDebugger;
#ifdef __cplusplus
interface IApplicationDebugger;
#endif /* __cplusplus */
#endif

#ifndef __IApplicationDebuggerUI_FWD_DEFINED__
#define __IApplicationDebuggerUI_FWD_DEFINED__
typedef interface IApplicationDebuggerUI IApplicationDebuggerUI;
#ifdef __cplusplus
interface IApplicationDebuggerUI;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplication32_FWD_DEFINED__
#define __IDebugApplication32_FWD_DEFINED__
typedef interface IDebugApplication32 IDebugApplication32;
#ifdef __cplusplus
interface IDebugApplication32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplication64_FWD_DEFINED__
#define __IDebugApplication64_FWD_DEFINED__
typedef interface IDebugApplication64 IDebugApplication64;
#ifdef __cplusplus
interface IDebugApplication64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationNode_FWD_DEFINED__
#define __IDebugApplicationNode_FWD_DEFINED__
typedef interface IDebugApplicationNode IDebugApplicationNode;
#ifdef __cplusplus
interface IDebugApplicationNode;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationNodeEvents_FWD_DEFINED__
#define __IDebugApplicationNodeEvents_FWD_DEFINED__
typedef interface IDebugApplicationNodeEvents IDebugApplicationNodeEvents;
#ifdef __cplusplus
interface IDebugApplicationNodeEvents;
#endif /* __cplusplus */
#endif

#ifndef __IDebugApplicationThread_FWD_DEFINED__
#define __IDebugApplicationThread_FWD_DEFINED__
typedef interface IDebugApplicationThread IDebugApplicationThread;
#ifdef __cplusplus
interface IDebugApplicationThread;
#endif /* __cplusplus */
#endif

#ifndef __IDebugAsyncOperation_FWD_DEFINED__
#define __IDebugAsyncOperation_FWD_DEFINED__
typedef interface IDebugAsyncOperation IDebugAsyncOperation;
#ifdef __cplusplus
interface IDebugAsyncOperation;
#endif /* __cplusplus */
#endif

#ifndef __IDebugAsyncOperationCallBack_FWD_DEFINED__
#define __IDebugAsyncOperationCallBack_FWD_DEFINED__
typedef interface IDebugAsyncOperationCallBack IDebugAsyncOperationCallBack;
#ifdef __cplusplus
interface IDebugAsyncOperationCallBack;
#endif /* __cplusplus */
#endif

#ifndef __IDebugCodeContext_FWD_DEFINED__
#define __IDebugCodeContext_FWD_DEFINED__
typedef interface IDebugCodeContext IDebugCodeContext;
#ifdef __cplusplus
interface IDebugCodeContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugCookie_FWD_DEFINED__
#define __IDebugCookie_FWD_DEFINED__
typedef interface IDebugCookie IDebugCookie;
#ifdef __cplusplus
interface IDebugCookie;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocument_FWD_DEFINED__
#define __IDebugDocument_FWD_DEFINED__
typedef interface IDebugDocument IDebugDocument;
#ifdef __cplusplus
interface IDebugDocument;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentContext_FWD_DEFINED__
#define __IDebugDocumentContext_FWD_DEFINED__
typedef interface IDebugDocumentContext IDebugDocumentContext;
#ifdef __cplusplus
interface IDebugDocumentContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHelper32_FWD_DEFINED__
#define __IDebugDocumentHelper32_FWD_DEFINED__
typedef interface IDebugDocumentHelper32 IDebugDocumentHelper32;
#ifdef __cplusplus
interface IDebugDocumentHelper32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHelper64_FWD_DEFINED__
#define __IDebugDocumentHelper64_FWD_DEFINED__
typedef interface IDebugDocumentHelper64 IDebugDocumentHelper64;
#ifdef __cplusplus
interface IDebugDocumentHelper64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentHost_FWD_DEFINED__
#define __IDebugDocumentHost_FWD_DEFINED__
typedef interface IDebugDocumentHost IDebugDocumentHost;
#ifdef __cplusplus
interface IDebugDocumentHost;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentInfo_FWD_DEFINED__
#define __IDebugDocumentInfo_FWD_DEFINED__
typedef interface IDebugDocumentInfo IDebugDocumentInfo;
#ifdef __cplusplus
interface IDebugDocumentInfo;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentProvider_FWD_DEFINED__
#define __IDebugDocumentProvider_FWD_DEFINED__
typedef interface IDebugDocumentProvider IDebugDocumentProvider;
#ifdef __cplusplus
interface IDebugDocumentProvider;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentText_FWD_DEFINED__
#define __IDebugDocumentText_FWD_DEFINED__
typedef interface IDebugDocumentText IDebugDocumentText;
#ifdef __cplusplus
interface IDebugDocumentText;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentTextAuthor_FWD_DEFINED__
#define __IDebugDocumentTextAuthor_FWD_DEFINED__
typedef interface IDebugDocumentTextAuthor IDebugDocumentTextAuthor;
#ifdef __cplusplus
interface IDebugDocumentTextAuthor;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentTextEvents_FWD_DEFINED__
#define __IDebugDocumentTextEvents_FWD_DEFINED__
typedef interface IDebugDocumentTextEvents IDebugDocumentTextEvents;
#ifdef __cplusplus
interface IDebugDocumentTextEvents;
#endif /* __cplusplus */
#endif

#ifndef __IDebugDocumentTextExternalAuthor_FWD_DEFINED__
#define __IDebugDocumentTextExternalAuthor_FWD_DEFINED__
typedef interface IDebugDocumentTextExternalAuthor IDebugDocumentTextExternalAuthor;
#ifdef __cplusplus
interface IDebugDocumentTextExternalAuthor;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpression_FWD_DEFINED__
#define __IDebugExpression_FWD_DEFINED__
typedef interface IDebugExpression IDebugExpression;
#ifdef __cplusplus
interface IDebugExpression;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpressionCallBack_FWD_DEFINED__
#define __IDebugExpressionCallBack_FWD_DEFINED__
typedef interface IDebugExpressionCallBack IDebugExpressionCallBack;
#ifdef __cplusplus
interface IDebugExpressionCallBack;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExpressionContext_FWD_DEFINED__
#define __IDebugExpressionContext_FWD_DEFINED__
typedef interface IDebugExpressionContext IDebugExpressionContext;
#ifdef __cplusplus
interface IDebugExpressionContext;
#endif /* __cplusplus */
#endif

#ifndef __IDebugFormatter_FWD_DEFINED__
#define __IDebugFormatter_FWD_DEFINED__
typedef interface IDebugFormatter IDebugFormatter;
#ifdef __cplusplus
interface IDebugFormatter;
#endif /* __cplusplus */
#endif

#ifndef __IDebugHelper_FWD_DEFINED__
#define __IDebugHelper_FWD_DEFINED__
typedef interface IDebugHelper IDebugHelper;
#ifdef __cplusplus
interface IDebugHelper;
#endif /* __cplusplus */
#endif

#ifndef __IDebugSessionProvider_FWD_DEFINED__
#define __IDebugSessionProvider_FWD_DEFINED__
typedef interface IDebugSessionProvider IDebugSessionProvider;
#ifdef __cplusplus
interface IDebugSessionProvider;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrame_FWD_DEFINED__
#define __IDebugStackFrame_FWD_DEFINED__
typedef interface IDebugStackFrame IDebugStackFrame;
#ifdef __cplusplus
interface IDebugStackFrame;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrameSniffer_FWD_DEFINED__
#define __IDebugStackFrameSniffer_FWD_DEFINED__
typedef interface IDebugStackFrameSniffer IDebugStackFrameSniffer;
#ifdef __cplusplus
interface IDebugStackFrameSniffer;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrameSnifferEx32_FWD_DEFINED__
#define __IDebugStackFrameSnifferEx32_FWD_DEFINED__
typedef interface IDebugStackFrameSnifferEx32 IDebugStackFrameSnifferEx32;
#ifdef __cplusplus
interface IDebugStackFrameSnifferEx32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugStackFrameSnifferEx64_FWD_DEFINED__
#define __IDebugStackFrameSnifferEx64_FWD_DEFINED__
typedef interface IDebugStackFrameSnifferEx64 IDebugStackFrameSnifferEx64;
#ifdef __cplusplus
interface IDebugStackFrameSnifferEx64;
#endif /* __cplusplus */
#endif

#ifndef __IDebugSyncOperation_FWD_DEFINED__
#define __IDebugSyncOperation_FWD_DEFINED__
typedef interface IDebugSyncOperation IDebugSyncOperation;
#ifdef __cplusplus
interface IDebugSyncOperation;
#endif /* __cplusplus */
#endif

#ifndef __IDebugThreadCall32_FWD_DEFINED__
#define __IDebugThreadCall32_FWD_DEFINED__
typedef interface IDebugThreadCall32 IDebugThreadCall32;
#ifdef __cplusplus
interface IDebugThreadCall32;
#endif /* __cplusplus */
#endif

#ifndef __IDebugThreadCall64_FWD_DEFINED__
#define __IDebugThreadCall64_FWD_DEFINED__
typedef interface IDebugThreadCall64 IDebugThreadCall64;
#ifdef __cplusplus
interface IDebugThreadCall64;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugApplicationNodes_FWD_DEFINED__
#define __IEnumDebugApplicationNodes_FWD_DEFINED__
typedef interface IEnumDebugApplicationNodes IEnumDebugApplicationNodes;
#ifdef __cplusplus
interface IEnumDebugApplicationNodes;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugCodeContexts_FWD_DEFINED__
#define __IEnumDebugCodeContexts_FWD_DEFINED__
typedef interface IEnumDebugCodeContexts IEnumDebugCodeContexts;
#ifdef __cplusplus
interface IEnumDebugCodeContexts;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugExpressionContexts_FWD_DEFINED__
#define __IEnumDebugExpressionContexts_FWD_DEFINED__
typedef interface IEnumDebugExpressionContexts IEnumDebugExpressionContexts;
#ifdef __cplusplus
interface IEnumDebugExpressionContexts;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugStackFrames_FWD_DEFINED__
#define __IEnumDebugStackFrames_FWD_DEFINED__
typedef interface IEnumDebugStackFrames IEnumDebugStackFrames;
#ifdef __cplusplus
interface IEnumDebugStackFrames;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugStackFrames64_FWD_DEFINED__
#define __IEnumDebugStackFrames64_FWD_DEFINED__
typedef interface IEnumDebugStackFrames64 IEnumDebugStackFrames64;
#ifdef __cplusplus
interface IEnumDebugStackFrames64;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRemoteDebugApplications_FWD_DEFINED__
#define __IEnumRemoteDebugApplications_FWD_DEFINED__
typedef interface IEnumRemoteDebugApplications IEnumRemoteDebugApplications;
#ifdef __cplusplus
interface IEnumRemoteDebugApplications;
#endif /* __cplusplus */
#endif

#ifndef __IEnumRemoteDebugApplicationThreads_FWD_DEFINED__
#define __IEnumRemoteDebugApplicationThreads_FWD_DEFINED__
typedef interface IEnumRemoteDebugApplicationThreads IEnumRemoteDebugApplicationThreads;
#ifdef __cplusplus
interface IEnumRemoteDebugApplicationThreads;
#endif /* __cplusplus */
#endif

#ifndef __IProcessDebugManager32_FWD_DEFINED__
#define __IProcessDebugManager32_FWD_DEFINED__
typedef interface IProcessDebugManager32 IProcessDebugManager32;
#ifdef __cplusplus
interface IProcessDebugManager32;
#endif /* __cplusplus */
#endif

#ifndef __IProcessDebugManager64_FWD_DEFINED__
#define __IProcessDebugManager64_FWD_DEFINED__
typedef interface IProcessDebugManager64 IProcessDebugManager64;
#ifdef __cplusplus
interface IProcessDebugManager64;
#endif /* __cplusplus */
#endif

#ifndef __IProvideExpressionContexts_FWD_DEFINED__
#define __IProvideExpressionContexts_FWD_DEFINED__
typedef interface IProvideExpressionContexts IProvideExpressionContexts;
#ifdef __cplusplus
interface IProvideExpressionContexts;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManager_FWD_DEFINED__
#define __IMachineDebugManager_FWD_DEFINED__
typedef interface IMachineDebugManager IMachineDebugManager;
#ifdef __cplusplus
interface IMachineDebugManager;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManagerCookie_FWD_DEFINED__
#define __IMachineDebugManagerCookie_FWD_DEFINED__
typedef interface IMachineDebugManagerCookie IMachineDebugManagerCookie;
#ifdef __cplusplus
interface IMachineDebugManagerCookie;
#endif /* __cplusplus */
#endif

#ifndef __IMachineDebugManagerEvents_FWD_DEFINED__
#define __IMachineDebugManagerEvents_FWD_DEFINED__
typedef interface IMachineDebugManagerEvents IMachineDebugManagerEvents;
#ifdef __cplusplus
interface IMachineDebugManagerEvents;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplication_FWD_DEFINED__
#define __IRemoteDebugApplication_FWD_DEFINED__
typedef interface IRemoteDebugApplication IRemoteDebugApplication;
#ifdef __cplusplus
interface IRemoteDebugApplication;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplicationEvents_FWD_DEFINED__
#define __IRemoteDebugApplicationEvents_FWD_DEFINED__
typedef interface IRemoteDebugApplicationEvents IRemoteDebugApplicationEvents;
#ifdef __cplusplus
interface IRemoteDebugApplicationEvents;
#endif /* __cplusplus */
#endif

#ifndef __IRemoteDebugApplicationThread_FWD_DEFINED__
#define __IRemoteDebugApplicationThread_FWD_DEFINED__
typedef interface IRemoteDebugApplicationThread IRemoteDebugApplicationThread;
#ifdef __cplusplus
interface IRemoteDebugApplicationThread;
#endif /* __cplusplus */
#endif

#ifndef __ISimpleConnectionPoint_FWD_DEFINED__
#define __ISimpleConnectionPoint_FWD_DEFINED__
typedef interface ISimpleConnectionPoint ISimpleConnectionPoint;
#ifdef __cplusplus
interface ISimpleConnectionPoint;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ProcessDebugManager coclass
 */

DEFINE_GUID(CLSID_ProcessDebugManager, 0x78a51822, 0x51f4, 0x11d0, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);

#ifdef __cplusplus
class DECLSPEC_UUID("78a51822-51f4-11d0-8f20-00805f2cd064") ProcessDebugManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ProcessDebugManager, 0x78a51822, 0x51f4, 0x11d0, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#endif

/*****************************************************************************
 * DebugHelper coclass
 */

DEFINE_GUID(CLSID_DebugHelper, 0x0bfcc060, 0x8c1d, 0x11d0, 0xac,0xcd, 0x00,0xaa,0x00,0x60,0x27,0x5c);

#ifdef __cplusplus
class DECLSPEC_UUID("0bfcc060-8c1d-11d0-accd-00aa0060275c") DebugHelper;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DebugHelper, 0x0bfcc060, 0x8c1d, 0x11d0, 0xac,0xcd, 0x00,0xaa,0x00,0x60,0x27,0x5c)
#endif
#endif

EXTERN_C const CLSID CLSID_CDebugDocumentHelper;
/*****************************************************************************
 * CDebugDocumentHelper coclass
 */

DEFINE_GUID(CLSID_CDebugDocumentHelper, 0x83b8bca6, 0x687c, 0x11d0, 0xa4,0x05, 0x00,0xaa,0x00,0x60,0x27,0x5c);

#ifdef __cplusplus
class DECLSPEC_UUID("83b8bca6-687c-11d0-a405-00aa0060275c") CDebugDocumentHelper;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CDebugDocumentHelper, 0x83b8bca6, 0x687c, 0x11d0, 0xa4,0x05, 0x00,0xaa,0x00,0x60,0x27,0x5c)
#endif
#endif

#ifdef DEBUG
#define MachineDebugManger MachineDebugManager_DEBUG
#define CLSID_MachineDebugManager CLSID_MachineDebugManager_DEBUG
#else
#define MachineDebugManger MachineDebugManager_RETAIL
#define CLSID_MachineDebugManager CLSID_MachineDebugManager_RETAIL
#endif
/*****************************************************************************
 * MachineDebugManager_RETAIL coclass
 */

DEFINE_GUID(CLSID_MachineDebugManager_RETAIL, 0x0c0a3666, 0x30c9, 0x11d0, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);

#ifdef __cplusplus
class DECLSPEC_UUID("0c0a3666-30c9-11d0-8f20-00805f2cd064") MachineDebugManager_RETAIL;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(MachineDebugManager_RETAIL, 0x0c0a3666, 0x30c9, 0x11d0, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#endif

/*****************************************************************************
 * MachineDebugManager_DEBUG coclass
 */

DEFINE_GUID(CLSID_MachineDebugManager_DEBUG, 0x49769cec, 0x3a55, 0x4bb0, 0xb6,0x97, 0x88,0xfe,0xde,0x77,0xe8,0xea);

#ifdef __cplusplus
class DECLSPEC_UUID("49769cec-3a55-4bb0-b697-88fede77e8ea") MachineDebugManager_DEBUG;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(MachineDebugManager_DEBUG, 0x49769cec, 0x3a55, 0x4bb0, 0xb6,0x97, 0x88,0xfe,0xde,0x77,0xe8,0xea)
#endif
#endif

/*****************************************************************************
 * DefaultDebugSessionProvider coclass
 */

DEFINE_GUID(CLSID_DefaultDebugSessionProvider, 0x834128a2, 0x51f4, 0x11d0, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);

#ifdef __cplusplus
class DECLSPEC_UUID("834128a2-51f4-11d0-8f20-00805f2cd064") DefaultDebugSessionProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DefaultDebugSessionProvider, 0x834128a2, 0x51f4, 0x11d0, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#endif

#endif /* __ProcessDebugManagerLib_LIBRARY_DEFINED__ */
#endif
#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __activdbg_h__ */
