// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/composite_tensor_variant.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/struct.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto;
namespace tensorflow {
class CompositeTensorVariantMetadata;
struct CompositeTensorVariantMetadataDefaultTypeInternal;
extern CompositeTensorVariantMetadataDefaultTypeInternal _CompositeTensorVariantMetadata_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CompositeTensorVariantMetadata* Arena::CreateMaybeMessage<::tensorflow::CompositeTensorVariantMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class CompositeTensorVariantMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompositeTensorVariantMetadata) */ {
 public:
  inline CompositeTensorVariantMetadata() : CompositeTensorVariantMetadata(nullptr) {}
  ~CompositeTensorVariantMetadata() override;
  explicit PROTOBUF_CONSTEXPR CompositeTensorVariantMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompositeTensorVariantMetadata(const CompositeTensorVariantMetadata& from);
  CompositeTensorVariantMetadata(CompositeTensorVariantMetadata&& from) noexcept
    : CompositeTensorVariantMetadata() {
    *this = ::std::move(from);
  }

  inline CompositeTensorVariantMetadata& operator=(const CompositeTensorVariantMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompositeTensorVariantMetadata& operator=(CompositeTensorVariantMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompositeTensorVariantMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompositeTensorVariantMetadata* internal_default_instance() {
    return reinterpret_cast<const CompositeTensorVariantMetadata*>(
               &_CompositeTensorVariantMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CompositeTensorVariantMetadata& a, CompositeTensorVariantMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(CompositeTensorVariantMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompositeTensorVariantMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompositeTensorVariantMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompositeTensorVariantMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompositeTensorVariantMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompositeTensorVariantMetadata& from) {
    CompositeTensorVariantMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompositeTensorVariantMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompositeTensorVariantMetadata";
  }
  protected:
  explicit CompositeTensorVariantMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeSpecProtoFieldNumber = 1,
  };
  // .tensorflow.TypeSpecProto type_spec_proto = 1;
  bool has_type_spec_proto() const;
  private:
  bool _internal_has_type_spec_proto() const;
  public:
  void clear_type_spec_proto();
  const ::tensorflow::TypeSpecProto& type_spec_proto() const;
  PROTOBUF_NODISCARD ::tensorflow::TypeSpecProto* release_type_spec_proto();
  ::tensorflow::TypeSpecProto* mutable_type_spec_proto();
  void set_allocated_type_spec_proto(::tensorflow::TypeSpecProto* type_spec_proto);
  private:
  const ::tensorflow::TypeSpecProto& _internal_type_spec_proto() const;
  ::tensorflow::TypeSpecProto* _internal_mutable_type_spec_proto();
  public:
  void unsafe_arena_set_allocated_type_spec_proto(
      ::tensorflow::TypeSpecProto* type_spec_proto);
  ::tensorflow::TypeSpecProto* unsafe_arena_release_type_spec_proto();

  // @@protoc_insertion_point(class_scope:tensorflow.CompositeTensorVariantMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TypeSpecProto* type_spec_proto_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CompositeTensorVariantMetadata

// .tensorflow.TypeSpecProto type_spec_proto = 1;
inline bool CompositeTensorVariantMetadata::_internal_has_type_spec_proto() const {
  return this != internal_default_instance() && _impl_.type_spec_proto_ != nullptr;
}
inline bool CompositeTensorVariantMetadata::has_type_spec_proto() const {
  return _internal_has_type_spec_proto();
}
inline const ::tensorflow::TypeSpecProto& CompositeTensorVariantMetadata::_internal_type_spec_proto() const {
  const ::tensorflow::TypeSpecProto* p = _impl_.type_spec_proto_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TypeSpecProto&>(
      ::tensorflow::_TypeSpecProto_default_instance_);
}
inline const ::tensorflow::TypeSpecProto& CompositeTensorVariantMetadata::type_spec_proto() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
  return _internal_type_spec_proto();
}
inline void CompositeTensorVariantMetadata::unsafe_arena_set_allocated_type_spec_proto(
    ::tensorflow::TypeSpecProto* type_spec_proto) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.type_spec_proto_);
  }
  _impl_.type_spec_proto_ = type_spec_proto;
  if (type_spec_proto) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
}
inline ::tensorflow::TypeSpecProto* CompositeTensorVariantMetadata::release_type_spec_proto() {
  
  ::tensorflow::TypeSpecProto* temp = _impl_.type_spec_proto_;
  _impl_.type_spec_proto_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TypeSpecProto* CompositeTensorVariantMetadata::unsafe_arena_release_type_spec_proto() {
  // @@protoc_insertion_point(field_release:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
  
  ::tensorflow::TypeSpecProto* temp = _impl_.type_spec_proto_;
  _impl_.type_spec_proto_ = nullptr;
  return temp;
}
inline ::tensorflow::TypeSpecProto* CompositeTensorVariantMetadata::_internal_mutable_type_spec_proto() {
  
  if (_impl_.type_spec_proto_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TypeSpecProto>(GetArenaForAllocation());
    _impl_.type_spec_proto_ = p;
  }
  return _impl_.type_spec_proto_;
}
inline ::tensorflow::TypeSpecProto* CompositeTensorVariantMetadata::mutable_type_spec_proto() {
  ::tensorflow::TypeSpecProto* _msg = _internal_mutable_type_spec_proto();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
  return _msg;
}
inline void CompositeTensorVariantMetadata::set_allocated_type_spec_proto(::tensorflow::TypeSpecProto* type_spec_proto) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.type_spec_proto_);
  }
  if (type_spec_proto) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(type_spec_proto));
    if (message_arena != submessage_arena) {
      type_spec_proto = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type_spec_proto, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.type_spec_proto_ = type_spec_proto;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompositeTensorVariantMetadata.type_spec_proto)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcomposite_5ftensor_5fvariant_2eproto
