// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/device_attributes.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
namespace tensorflow {
class DeviceAttributes;
struct DeviceAttributesDefaultTypeInternal;
extern DeviceAttributesDefaultTypeInternal _DeviceAttributes_default_instance_;
class DeviceLocality;
struct DeviceLocalityDefaultTypeInternal;
extern DeviceLocalityDefaultTypeInternal _DeviceLocality_default_instance_;
class InterconnectLink;
struct InterconnectLinkDefaultTypeInternal;
extern InterconnectLinkDefaultTypeInternal _InterconnectLink_default_instance_;
class LocalLinks;
struct LocalLinksDefaultTypeInternal;
extern LocalLinksDefaultTypeInternal _LocalLinks_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::DeviceAttributes* Arena::CreateMaybeMessage<::tensorflow::DeviceAttributes>(Arena*);
template<> ::tensorflow::DeviceLocality* Arena::CreateMaybeMessage<::tensorflow::DeviceLocality>(Arena*);
template<> ::tensorflow::InterconnectLink* Arena::CreateMaybeMessage<::tensorflow::InterconnectLink>(Arena*);
template<> ::tensorflow::LocalLinks* Arena::CreateMaybeMessage<::tensorflow::LocalLinks>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class InterconnectLink final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.InterconnectLink) */ {
 public:
  inline InterconnectLink() : InterconnectLink(nullptr) {}
  ~InterconnectLink() override;
  explicit PROTOBUF_CONSTEXPR InterconnectLink(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InterconnectLink(const InterconnectLink& from);
  InterconnectLink(InterconnectLink&& from) noexcept
    : InterconnectLink() {
    *this = ::std::move(from);
  }

  inline InterconnectLink& operator=(const InterconnectLink& from) {
    CopyFrom(from);
    return *this;
  }
  inline InterconnectLink& operator=(InterconnectLink&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InterconnectLink& default_instance() {
    return *internal_default_instance();
  }
  static inline const InterconnectLink* internal_default_instance() {
    return reinterpret_cast<const InterconnectLink*>(
               &_InterconnectLink_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(InterconnectLink& a, InterconnectLink& b) {
    a.Swap(&b);
  }
  inline void Swap(InterconnectLink* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InterconnectLink* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InterconnectLink* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InterconnectLink>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const InterconnectLink& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const InterconnectLink& from) {
    InterconnectLink::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InterconnectLink* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.InterconnectLink";
  }
  protected:
  explicit InterconnectLink(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 2,
    kDeviceIdFieldNumber = 1,
    kStrengthFieldNumber = 3,
  };
  // string type = 2;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // int32 device_id = 1;
  void clear_device_id();
  int32_t device_id() const;
  void set_device_id(int32_t value);
  private:
  int32_t _internal_device_id() const;
  void _internal_set_device_id(int32_t value);
  public:

  // int32 strength = 3;
  void clear_strength();
  int32_t strength() const;
  void set_strength(int32_t value);
  private:
  int32_t _internal_strength() const;
  void _internal_set_strength(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.InterconnectLink)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
    int32_t device_id_;
    int32_t strength_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
};
// -------------------------------------------------------------------

class LocalLinks final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LocalLinks) */ {
 public:
  inline LocalLinks() : LocalLinks(nullptr) {}
  ~LocalLinks() override;
  explicit PROTOBUF_CONSTEXPR LocalLinks(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LocalLinks(const LocalLinks& from);
  LocalLinks(LocalLinks&& from) noexcept
    : LocalLinks() {
    *this = ::std::move(from);
  }

  inline LocalLinks& operator=(const LocalLinks& from) {
    CopyFrom(from);
    return *this;
  }
  inline LocalLinks& operator=(LocalLinks&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LocalLinks& default_instance() {
    return *internal_default_instance();
  }
  static inline const LocalLinks* internal_default_instance() {
    return reinterpret_cast<const LocalLinks*>(
               &_LocalLinks_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LocalLinks& a, LocalLinks& b) {
    a.Swap(&b);
  }
  inline void Swap(LocalLinks* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LocalLinks* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LocalLinks* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LocalLinks>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LocalLinks& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LocalLinks& from) {
    LocalLinks::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocalLinks* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LocalLinks";
  }
  protected:
  explicit LocalLinks(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinkFieldNumber = 1,
  };
  // repeated .tensorflow.InterconnectLink link = 1;
  int link_size() const;
  private:
  int _internal_link_size() const;
  public:
  void clear_link();
  ::tensorflow::InterconnectLink* mutable_link(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink >*
      mutable_link();
  private:
  const ::tensorflow::InterconnectLink& _internal_link(int index) const;
  ::tensorflow::InterconnectLink* _internal_add_link();
  public:
  const ::tensorflow::InterconnectLink& link(int index) const;
  ::tensorflow::InterconnectLink* add_link();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink >&
      link() const;

  // @@protoc_insertion_point(class_scope:tensorflow.LocalLinks)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink > link_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
};
// -------------------------------------------------------------------

class DeviceLocality final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceLocality) */ {
 public:
  inline DeviceLocality() : DeviceLocality(nullptr) {}
  ~DeviceLocality() override;
  explicit PROTOBUF_CONSTEXPR DeviceLocality(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceLocality(const DeviceLocality& from);
  DeviceLocality(DeviceLocality&& from) noexcept
    : DeviceLocality() {
    *this = ::std::move(from);
  }

  inline DeviceLocality& operator=(const DeviceLocality& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceLocality& operator=(DeviceLocality&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceLocality& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceLocality* internal_default_instance() {
    return reinterpret_cast<const DeviceLocality*>(
               &_DeviceLocality_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DeviceLocality& a, DeviceLocality& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceLocality* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceLocality* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceLocality* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceLocality>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceLocality& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceLocality& from) {
    DeviceLocality::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceLocality* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceLocality";
  }
  protected:
  explicit DeviceLocality(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinksFieldNumber = 3,
    kBusIdFieldNumber = 1,
    kNumaNodeFieldNumber = 2,
  };
  // .tensorflow.LocalLinks links = 3;
  bool has_links() const;
  private:
  bool _internal_has_links() const;
  public:
  void clear_links();
  const ::tensorflow::LocalLinks& links() const;
  PROTOBUF_NODISCARD ::tensorflow::LocalLinks* release_links();
  ::tensorflow::LocalLinks* mutable_links();
  void set_allocated_links(::tensorflow::LocalLinks* links);
  private:
  const ::tensorflow::LocalLinks& _internal_links() const;
  ::tensorflow::LocalLinks* _internal_mutable_links();
  public:
  void unsafe_arena_set_allocated_links(
      ::tensorflow::LocalLinks* links);
  ::tensorflow::LocalLinks* unsafe_arena_release_links();

  // int32 bus_id = 1;
  void clear_bus_id();
  int32_t bus_id() const;
  void set_bus_id(int32_t value);
  private:
  int32_t _internal_bus_id() const;
  void _internal_set_bus_id(int32_t value);
  public:

  // int32 numa_node = 2;
  void clear_numa_node();
  int32_t numa_node() const;
  void set_numa_node(int32_t value);
  private:
  int32_t _internal_numa_node() const;
  void _internal_set_numa_node(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceLocality)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::LocalLinks* links_;
    int32_t bus_id_;
    int32_t numa_node_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
};
// -------------------------------------------------------------------

class DeviceAttributes final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceAttributes) */ {
 public:
  inline DeviceAttributes() : DeviceAttributes(nullptr) {}
  ~DeviceAttributes() override;
  explicit PROTOBUF_CONSTEXPR DeviceAttributes(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceAttributes(const DeviceAttributes& from);
  DeviceAttributes(DeviceAttributes&& from) noexcept
    : DeviceAttributes() {
    *this = ::std::move(from);
  }

  inline DeviceAttributes& operator=(const DeviceAttributes& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceAttributes& operator=(DeviceAttributes&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceAttributes& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceAttributes* internal_default_instance() {
    return reinterpret_cast<const DeviceAttributes*>(
               &_DeviceAttributes_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DeviceAttributes& a, DeviceAttributes& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceAttributes* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceAttributes* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceAttributes* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceAttributes>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceAttributes& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceAttributes& from) {
    DeviceAttributes::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceAttributes* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceAttributes";
  }
  protected:
  explicit DeviceAttributes(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kDeviceTypeFieldNumber = 2,
    kPhysicalDeviceDescFieldNumber = 7,
    kLocalityFieldNumber = 5,
    kMemoryLimitFieldNumber = 4,
    kIncarnationFieldNumber = 6,
    kXlaGlobalIdFieldNumber = 8,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string device_type = 2;
  void clear_device_type();
  const std::string& device_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_type();
  PROTOBUF_NODISCARD std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  private:
  const std::string& _internal_device_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_type(const std::string& value);
  std::string* _internal_mutable_device_type();
  public:

  // string physical_device_desc = 7;
  void clear_physical_device_desc();
  const std::string& physical_device_desc() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_physical_device_desc(ArgT0&& arg0, ArgT... args);
  std::string* mutable_physical_device_desc();
  PROTOBUF_NODISCARD std::string* release_physical_device_desc();
  void set_allocated_physical_device_desc(std::string* physical_device_desc);
  private:
  const std::string& _internal_physical_device_desc() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_physical_device_desc(const std::string& value);
  std::string* _internal_mutable_physical_device_desc();
  public:

  // .tensorflow.DeviceLocality locality = 5;
  bool has_locality() const;
  private:
  bool _internal_has_locality() const;
  public:
  void clear_locality();
  const ::tensorflow::DeviceLocality& locality() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceLocality* release_locality();
  ::tensorflow::DeviceLocality* mutable_locality();
  void set_allocated_locality(::tensorflow::DeviceLocality* locality);
  private:
  const ::tensorflow::DeviceLocality& _internal_locality() const;
  ::tensorflow::DeviceLocality* _internal_mutable_locality();
  public:
  void unsafe_arena_set_allocated_locality(
      ::tensorflow::DeviceLocality* locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_locality();

  // int64 memory_limit = 4;
  void clear_memory_limit();
  int64_t memory_limit() const;
  void set_memory_limit(int64_t value);
  private:
  int64_t _internal_memory_limit() const;
  void _internal_set_memory_limit(int64_t value);
  public:

  // fixed64 incarnation = 6;
  void clear_incarnation();
  uint64_t incarnation() const;
  void set_incarnation(uint64_t value);
  private:
  uint64_t _internal_incarnation() const;
  void _internal_set_incarnation(uint64_t value);
  public:

  // int64 xla_global_id = 8;
  void clear_xla_global_id();
  int64_t xla_global_id() const;
  void set_xla_global_id(int64_t value);
  private:
  int64_t _internal_xla_global_id() const;
  void _internal_set_xla_global_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceAttributes)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr physical_device_desc_;
    ::tensorflow::DeviceLocality* locality_;
    int64_t memory_limit_;
    uint64_t incarnation_;
    int64_t xla_global_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// InterconnectLink

// int32 device_id = 1;
inline void InterconnectLink::clear_device_id() {
  _impl_.device_id_ = 0;
}
inline int32_t InterconnectLink::_internal_device_id() const {
  return _impl_.device_id_;
}
inline int32_t InterconnectLink::device_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.device_id)
  return _internal_device_id();
}
inline void InterconnectLink::_internal_set_device_id(int32_t value) {
  
  _impl_.device_id_ = value;
}
inline void InterconnectLink::set_device_id(int32_t value) {
  _internal_set_device_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.device_id)
}

// string type = 2;
inline void InterconnectLink::clear_type() {
  _impl_.type_.ClearToEmpty();
}
inline const std::string& InterconnectLink::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void InterconnectLink::set_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.type)
}
inline std::string* InterconnectLink::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.InterconnectLink.type)
  return _s;
}
inline const std::string& InterconnectLink::_internal_type() const {
  return _impl_.type_.Get();
}
inline void InterconnectLink::_internal_set_type(const std::string& value) {
  
  _impl_.type_.Set(value, GetArenaForAllocation());
}
inline std::string* InterconnectLink::_internal_mutable_type() {
  
  return _impl_.type_.Mutable(GetArenaForAllocation());
}
inline std::string* InterconnectLink::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.InterconnectLink.type)
  return _impl_.type_.Release();
}
inline void InterconnectLink::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  _impl_.type_.SetAllocated(type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_.IsDefault()) {
    _impl_.type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.InterconnectLink.type)
}

// int32 strength = 3;
inline void InterconnectLink::clear_strength() {
  _impl_.strength_ = 0;
}
inline int32_t InterconnectLink::_internal_strength() const {
  return _impl_.strength_;
}
inline int32_t InterconnectLink::strength() const {
  // @@protoc_insertion_point(field_get:tensorflow.InterconnectLink.strength)
  return _internal_strength();
}
inline void InterconnectLink::_internal_set_strength(int32_t value) {
  
  _impl_.strength_ = value;
}
inline void InterconnectLink::set_strength(int32_t value) {
  _internal_set_strength(value);
  // @@protoc_insertion_point(field_set:tensorflow.InterconnectLink.strength)
}

// -------------------------------------------------------------------

// LocalLinks

// repeated .tensorflow.InterconnectLink link = 1;
inline int LocalLinks::_internal_link_size() const {
  return _impl_.link_.size();
}
inline int LocalLinks::link_size() const {
  return _internal_link_size();
}
inline void LocalLinks::clear_link() {
  _impl_.link_.Clear();
}
inline ::tensorflow::InterconnectLink* LocalLinks::mutable_link(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.LocalLinks.link)
  return _impl_.link_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink >*
LocalLinks::mutable_link() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LocalLinks.link)
  return &_impl_.link_;
}
inline const ::tensorflow::InterconnectLink& LocalLinks::_internal_link(int index) const {
  return _impl_.link_.Get(index);
}
inline const ::tensorflow::InterconnectLink& LocalLinks::link(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LocalLinks.link)
  return _internal_link(index);
}
inline ::tensorflow::InterconnectLink* LocalLinks::_internal_add_link() {
  return _impl_.link_.Add();
}
inline ::tensorflow::InterconnectLink* LocalLinks::add_link() {
  ::tensorflow::InterconnectLink* _add = _internal_add_link();
  // @@protoc_insertion_point(field_add:tensorflow.LocalLinks.link)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::InterconnectLink >&
LocalLinks::link() const {
  // @@protoc_insertion_point(field_list:tensorflow.LocalLinks.link)
  return _impl_.link_;
}

// -------------------------------------------------------------------

// DeviceLocality

// int32 bus_id = 1;
inline void DeviceLocality::clear_bus_id() {
  _impl_.bus_id_ = 0;
}
inline int32_t DeviceLocality::_internal_bus_id() const {
  return _impl_.bus_id_;
}
inline int32_t DeviceLocality::bus_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.bus_id)
  return _internal_bus_id();
}
inline void DeviceLocality::_internal_set_bus_id(int32_t value) {
  
  _impl_.bus_id_ = value;
}
inline void DeviceLocality::set_bus_id(int32_t value) {
  _internal_set_bus_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceLocality.bus_id)
}

// int32 numa_node = 2;
inline void DeviceLocality::clear_numa_node() {
  _impl_.numa_node_ = 0;
}
inline int32_t DeviceLocality::_internal_numa_node() const {
  return _impl_.numa_node_;
}
inline int32_t DeviceLocality::numa_node() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.numa_node)
  return _internal_numa_node();
}
inline void DeviceLocality::_internal_set_numa_node(int32_t value) {
  
  _impl_.numa_node_ = value;
}
inline void DeviceLocality::set_numa_node(int32_t value) {
  _internal_set_numa_node(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceLocality.numa_node)
}

// .tensorflow.LocalLinks links = 3;
inline bool DeviceLocality::_internal_has_links() const {
  return this != internal_default_instance() && _impl_.links_ != nullptr;
}
inline bool DeviceLocality::has_links() const {
  return _internal_has_links();
}
inline void DeviceLocality::clear_links() {
  if (GetArenaForAllocation() == nullptr && _impl_.links_ != nullptr) {
    delete _impl_.links_;
  }
  _impl_.links_ = nullptr;
}
inline const ::tensorflow::LocalLinks& DeviceLocality::_internal_links() const {
  const ::tensorflow::LocalLinks* p = _impl_.links_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::LocalLinks&>(
      ::tensorflow::_LocalLinks_default_instance_);
}
inline const ::tensorflow::LocalLinks& DeviceLocality::links() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceLocality.links)
  return _internal_links();
}
inline void DeviceLocality::unsafe_arena_set_allocated_links(
    ::tensorflow::LocalLinks* links) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.links_);
  }
  _impl_.links_ = links;
  if (links) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceLocality.links)
}
inline ::tensorflow::LocalLinks* DeviceLocality::release_links() {
  
  ::tensorflow::LocalLinks* temp = _impl_.links_;
  _impl_.links_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::LocalLinks* DeviceLocality::unsafe_arena_release_links() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceLocality.links)
  
  ::tensorflow::LocalLinks* temp = _impl_.links_;
  _impl_.links_ = nullptr;
  return temp;
}
inline ::tensorflow::LocalLinks* DeviceLocality::_internal_mutable_links() {
  
  if (_impl_.links_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::LocalLinks>(GetArenaForAllocation());
    _impl_.links_ = p;
  }
  return _impl_.links_;
}
inline ::tensorflow::LocalLinks* DeviceLocality::mutable_links() {
  ::tensorflow::LocalLinks* _msg = _internal_mutable_links();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceLocality.links)
  return _msg;
}
inline void DeviceLocality::set_allocated_links(::tensorflow::LocalLinks* links) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.links_;
  }
  if (links) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(links);
    if (message_arena != submessage_arena) {
      links = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, links, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.links_ = links;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceLocality.links)
}

// -------------------------------------------------------------------

// DeviceAttributes

// string name = 1;
inline void DeviceAttributes::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& DeviceAttributes::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceAttributes::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.name)
}
inline std::string* DeviceAttributes::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.name)
  return _s;
}
inline const std::string& DeviceAttributes::_internal_name() const {
  return _impl_.name_.Get();
}
inline void DeviceAttributes::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceAttributes::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceAttributes::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.name)
  return _impl_.name_.Release();
}
inline void DeviceAttributes::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.name)
}

// string device_type = 2;
inline void DeviceAttributes::clear_device_type() {
  _impl_.device_type_.ClearToEmpty();
}
inline const std::string& DeviceAttributes::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.device_type)
  return _internal_device_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceAttributes::set_device_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.device_type)
}
inline std::string* DeviceAttributes::mutable_device_type() {
  std::string* _s = _internal_mutable_device_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.device_type)
  return _s;
}
inline const std::string& DeviceAttributes::_internal_device_type() const {
  return _impl_.device_type_.Get();
}
inline void DeviceAttributes::_internal_set_device_type(const std::string& value) {
  
  _impl_.device_type_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceAttributes::_internal_mutable_device_type() {
  
  return _impl_.device_type_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceAttributes::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.device_type)
  return _impl_.device_type_.Release();
}
inline void DeviceAttributes::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  _impl_.device_type_.SetAllocated(device_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_type_.IsDefault()) {
    _impl_.device_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.device_type)
}

// int64 memory_limit = 4;
inline void DeviceAttributes::clear_memory_limit() {
  _impl_.memory_limit_ = int64_t{0};
}
inline int64_t DeviceAttributes::_internal_memory_limit() const {
  return _impl_.memory_limit_;
}
inline int64_t DeviceAttributes::memory_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.memory_limit)
  return _internal_memory_limit();
}
inline void DeviceAttributes::_internal_set_memory_limit(int64_t value) {
  
  _impl_.memory_limit_ = value;
}
inline void DeviceAttributes::set_memory_limit(int64_t value) {
  _internal_set_memory_limit(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.memory_limit)
}

// .tensorflow.DeviceLocality locality = 5;
inline bool DeviceAttributes::_internal_has_locality() const {
  return this != internal_default_instance() && _impl_.locality_ != nullptr;
}
inline bool DeviceAttributes::has_locality() const {
  return _internal_has_locality();
}
inline void DeviceAttributes::clear_locality() {
  if (GetArenaForAllocation() == nullptr && _impl_.locality_ != nullptr) {
    delete _impl_.locality_;
  }
  _impl_.locality_ = nullptr;
}
inline const ::tensorflow::DeviceLocality& DeviceAttributes::_internal_locality() const {
  const ::tensorflow::DeviceLocality* p = _impl_.locality_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceLocality&>(
      ::tensorflow::_DeviceLocality_default_instance_);
}
inline const ::tensorflow::DeviceLocality& DeviceAttributes::locality() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.locality)
  return _internal_locality();
}
inline void DeviceAttributes::unsafe_arena_set_allocated_locality(
    ::tensorflow::DeviceLocality* locality) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.locality_);
  }
  _impl_.locality_ = locality;
  if (locality) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.DeviceAttributes.locality)
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::release_locality() {
  
  ::tensorflow::DeviceLocality* temp = _impl_.locality_;
  _impl_.locality_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::unsafe_arena_release_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.locality)
  
  ::tensorflow::DeviceLocality* temp = _impl_.locality_;
  _impl_.locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::_internal_mutable_locality() {
  
  if (_impl_.locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaForAllocation());
    _impl_.locality_ = p;
  }
  return _impl_.locality_;
}
inline ::tensorflow::DeviceLocality* DeviceAttributes::mutable_locality() {
  ::tensorflow::DeviceLocality* _msg = _internal_mutable_locality();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.locality)
  return _msg;
}
inline void DeviceAttributes::set_allocated_locality(::tensorflow::DeviceLocality* locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.locality_;
  }
  if (locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(locality);
    if (message_arena != submessage_arena) {
      locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, locality, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.locality_ = locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.locality)
}

// fixed64 incarnation = 6;
inline void DeviceAttributes::clear_incarnation() {
  _impl_.incarnation_ = uint64_t{0u};
}
inline uint64_t DeviceAttributes::_internal_incarnation() const {
  return _impl_.incarnation_;
}
inline uint64_t DeviceAttributes::incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.incarnation)
  return _internal_incarnation();
}
inline void DeviceAttributes::_internal_set_incarnation(uint64_t value) {
  
  _impl_.incarnation_ = value;
}
inline void DeviceAttributes::set_incarnation(uint64_t value) {
  _internal_set_incarnation(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.incarnation)
}

// string physical_device_desc = 7;
inline void DeviceAttributes::clear_physical_device_desc() {
  _impl_.physical_device_desc_.ClearToEmpty();
}
inline const std::string& DeviceAttributes::physical_device_desc() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.physical_device_desc)
  return _internal_physical_device_desc();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceAttributes::set_physical_device_desc(ArgT0&& arg0, ArgT... args) {
 
 _impl_.physical_device_desc_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.physical_device_desc)
}
inline std::string* DeviceAttributes::mutable_physical_device_desc() {
  std::string* _s = _internal_mutable_physical_device_desc();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceAttributes.physical_device_desc)
  return _s;
}
inline const std::string& DeviceAttributes::_internal_physical_device_desc() const {
  return _impl_.physical_device_desc_.Get();
}
inline void DeviceAttributes::_internal_set_physical_device_desc(const std::string& value) {
  
  _impl_.physical_device_desc_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceAttributes::_internal_mutable_physical_device_desc() {
  
  return _impl_.physical_device_desc_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceAttributes::release_physical_device_desc() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceAttributes.physical_device_desc)
  return _impl_.physical_device_desc_.Release();
}
inline void DeviceAttributes::set_allocated_physical_device_desc(std::string* physical_device_desc) {
  if (physical_device_desc != nullptr) {
    
  } else {
    
  }
  _impl_.physical_device_desc_.SetAllocated(physical_device_desc, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.physical_device_desc_.IsDefault()) {
    _impl_.physical_device_desc_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceAttributes.physical_device_desc)
}

// int64 xla_global_id = 8;
inline void DeviceAttributes::clear_xla_global_id() {
  _impl_.xla_global_id_ = int64_t{0};
}
inline int64_t DeviceAttributes::_internal_xla_global_id() const {
  return _impl_.xla_global_id_;
}
inline int64_t DeviceAttributes::xla_global_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceAttributes.xla_global_id)
  return _internal_xla_global_id();
}
inline void DeviceAttributes::_internal_set_xla_global_id(int64_t value) {
  
  _impl_.xla_global_id_ = value;
}
inline void DeviceAttributes::set_xla_global_id(int64_t value) {
  _internal_set_xla_global_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceAttributes.xla_global_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fdevice_5fattributes_2eproto
