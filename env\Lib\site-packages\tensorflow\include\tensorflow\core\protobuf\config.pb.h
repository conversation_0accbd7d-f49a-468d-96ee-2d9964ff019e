// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "xla/tsl/protobuf/coordination_config.pb.h"
#include "tensorflow/core/framework/cost_graph.pb.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/step_stats.pb.h"
#include "tensorflow/core/protobuf/cluster.pb.h"
#include "tensorflow/core/protobuf/debug.pb.h"
#include "tensorflow/core/protobuf/rewriter_config.pb.h"
#include "tensorflow/core/protobuf/rpc_options.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
namespace tensorflow {
class BatchingOptions;
struct BatchingOptionsDefaultTypeInternal;
extern BatchingOptionsDefaultTypeInternal _BatchingOptions_default_instance_;
class CallableOptions;
struct CallableOptionsDefaultTypeInternal;
extern CallableOptionsDefaultTypeInternal _CallableOptions_default_instance_;
class CallableOptions_FeedDevicesEntry_DoNotUse;
struct CallableOptions_FeedDevicesEntry_DoNotUseDefaultTypeInternal;
extern CallableOptions_FeedDevicesEntry_DoNotUseDefaultTypeInternal _CallableOptions_FeedDevicesEntry_DoNotUse_default_instance_;
class CallableOptions_FetchDevicesEntry_DoNotUse;
struct CallableOptions_FetchDevicesEntry_DoNotUseDefaultTypeInternal;
extern CallableOptions_FetchDevicesEntry_DoNotUseDefaultTypeInternal _CallableOptions_FetchDevicesEntry_DoNotUse_default_instance_;
class ConfigProto;
struct ConfigProtoDefaultTypeInternal;
extern ConfigProtoDefaultTypeInternal _ConfigProto_default_instance_;
class ConfigProto_DeviceCountEntry_DoNotUse;
struct ConfigProto_DeviceCountEntry_DoNotUseDefaultTypeInternal;
extern ConfigProto_DeviceCountEntry_DoNotUseDefaultTypeInternal _ConfigProto_DeviceCountEntry_DoNotUse_default_instance_;
class ConfigProto_Experimental;
struct ConfigProto_ExperimentalDefaultTypeInternal;
extern ConfigProto_ExperimentalDefaultTypeInternal _ConfigProto_Experimental_default_instance_;
class GPUOptions;
struct GPUOptionsDefaultTypeInternal;
extern GPUOptionsDefaultTypeInternal _GPUOptions_default_instance_;
class GPUOptions_Experimental;
struct GPUOptions_ExperimentalDefaultTypeInternal;
extern GPUOptions_ExperimentalDefaultTypeInternal _GPUOptions_Experimental_default_instance_;
class GPUOptions_Experimental_StreamMergeOptions;
struct GPUOptions_Experimental_StreamMergeOptionsDefaultTypeInternal;
extern GPUOptions_Experimental_StreamMergeOptionsDefaultTypeInternal _GPUOptions_Experimental_StreamMergeOptions_default_instance_;
class GPUOptions_Experimental_VirtualDevices;
struct GPUOptions_Experimental_VirtualDevicesDefaultTypeInternal;
extern GPUOptions_Experimental_VirtualDevicesDefaultTypeInternal _GPUOptions_Experimental_VirtualDevices_default_instance_;
class GraphOptions;
struct GraphOptionsDefaultTypeInternal;
extern GraphOptionsDefaultTypeInternal _GraphOptions_default_instance_;
class OptimizerOptions;
struct OptimizerOptionsDefaultTypeInternal;
extern OptimizerOptionsDefaultTypeInternal _OptimizerOptions_default_instance_;
class RunMetadata;
struct RunMetadataDefaultTypeInternal;
extern RunMetadataDefaultTypeInternal _RunMetadata_default_instance_;
class RunMetadata_FunctionGraphs;
struct RunMetadata_FunctionGraphsDefaultTypeInternal;
extern RunMetadata_FunctionGraphsDefaultTypeInternal _RunMetadata_FunctionGraphs_default_instance_;
class RunOptions;
struct RunOptionsDefaultTypeInternal;
extern RunOptionsDefaultTypeInternal _RunOptions_default_instance_;
class RunOptions_Experimental;
struct RunOptions_ExperimentalDefaultTypeInternal;
extern RunOptions_ExperimentalDefaultTypeInternal _RunOptions_Experimental_default_instance_;
class RunOptions_Experimental_RunHandlerPoolOptions;
struct RunOptions_Experimental_RunHandlerPoolOptionsDefaultTypeInternal;
extern RunOptions_Experimental_RunHandlerPoolOptionsDefaultTypeInternal _RunOptions_Experimental_RunHandlerPoolOptions_default_instance_;
class SessionMetadata;
struct SessionMetadataDefaultTypeInternal;
extern SessionMetadataDefaultTypeInternal _SessionMetadata_default_instance_;
class TensorConnection;
struct TensorConnectionDefaultTypeInternal;
extern TensorConnectionDefaultTypeInternal _TensorConnection_default_instance_;
class ThreadPoolOptionProto;
struct ThreadPoolOptionProtoDefaultTypeInternal;
extern ThreadPoolOptionProtoDefaultTypeInternal _ThreadPoolOptionProto_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::BatchingOptions* Arena::CreateMaybeMessage<::tensorflow::BatchingOptions>(Arena*);
template<> ::tensorflow::CallableOptions* Arena::CreateMaybeMessage<::tensorflow::CallableOptions>(Arena*);
template<> ::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CallableOptions_FeedDevicesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CallableOptions_FetchDevicesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ConfigProto* Arena::CreateMaybeMessage<::tensorflow::ConfigProto>(Arena*);
template<> ::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ConfigProto_DeviceCountEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ConfigProto_Experimental* Arena::CreateMaybeMessage<::tensorflow::ConfigProto_Experimental>(Arena*);
template<> ::tensorflow::GPUOptions* Arena::CreateMaybeMessage<::tensorflow::GPUOptions>(Arena*);
template<> ::tensorflow::GPUOptions_Experimental* Arena::CreateMaybeMessage<::tensorflow::GPUOptions_Experimental>(Arena*);
template<> ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* Arena::CreateMaybeMessage<::tensorflow::GPUOptions_Experimental_StreamMergeOptions>(Arena*);
template<> ::tensorflow::GPUOptions_Experimental_VirtualDevices* Arena::CreateMaybeMessage<::tensorflow::GPUOptions_Experimental_VirtualDevices>(Arena*);
template<> ::tensorflow::GraphOptions* Arena::CreateMaybeMessage<::tensorflow::GraphOptions>(Arena*);
template<> ::tensorflow::OptimizerOptions* Arena::CreateMaybeMessage<::tensorflow::OptimizerOptions>(Arena*);
template<> ::tensorflow::RunMetadata* Arena::CreateMaybeMessage<::tensorflow::RunMetadata>(Arena*);
template<> ::tensorflow::RunMetadata_FunctionGraphs* Arena::CreateMaybeMessage<::tensorflow::RunMetadata_FunctionGraphs>(Arena*);
template<> ::tensorflow::RunOptions* Arena::CreateMaybeMessage<::tensorflow::RunOptions>(Arena*);
template<> ::tensorflow::RunOptions_Experimental* Arena::CreateMaybeMessage<::tensorflow::RunOptions_Experimental>(Arena*);
template<> ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* Arena::CreateMaybeMessage<::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions>(Arena*);
template<> ::tensorflow::SessionMetadata* Arena::CreateMaybeMessage<::tensorflow::SessionMetadata>(Arena*);
template<> ::tensorflow::TensorConnection* Arena::CreateMaybeMessage<::tensorflow::TensorConnection>(Arena*);
template<> ::tensorflow::ThreadPoolOptionProto* Arena::CreateMaybeMessage<::tensorflow::ThreadPoolOptionProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum OptimizerOptions_Level : int {
  OptimizerOptions_Level_L1 = 0,
  OptimizerOptions_Level_L0 = -1,
  OptimizerOptions_Level_OptimizerOptions_Level_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  OptimizerOptions_Level_OptimizerOptions_Level_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool OptimizerOptions_Level_IsValid(int value);
constexpr OptimizerOptions_Level OptimizerOptions_Level_Level_MIN = OptimizerOptions_Level_L0;
constexpr OptimizerOptions_Level OptimizerOptions_Level_Level_MAX = OptimizerOptions_Level_L1;
constexpr int OptimizerOptions_Level_Level_ARRAYSIZE = OptimizerOptions_Level_Level_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OptimizerOptions_Level_descriptor();
template<typename T>
inline const std::string& OptimizerOptions_Level_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OptimizerOptions_Level>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OptimizerOptions_Level_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OptimizerOptions_Level_descriptor(), enum_t_value);
}
inline bool OptimizerOptions_Level_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OptimizerOptions_Level* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OptimizerOptions_Level>(
    OptimizerOptions_Level_descriptor(), name, value);
}
enum OptimizerOptions_GlobalJitLevel : int {
  OptimizerOptions_GlobalJitLevel_DEFAULT = 0,
  OptimizerOptions_GlobalJitLevel_OFF = -1,
  OptimizerOptions_GlobalJitLevel_ON_1 = 1,
  OptimizerOptions_GlobalJitLevel_ON_2 = 2,
  OptimizerOptions_GlobalJitLevel_OptimizerOptions_GlobalJitLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  OptimizerOptions_GlobalJitLevel_OptimizerOptions_GlobalJitLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool OptimizerOptions_GlobalJitLevel_IsValid(int value);
constexpr OptimizerOptions_GlobalJitLevel OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MIN = OptimizerOptions_GlobalJitLevel_OFF;
constexpr OptimizerOptions_GlobalJitLevel OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MAX = OptimizerOptions_GlobalJitLevel_ON_2;
constexpr int OptimizerOptions_GlobalJitLevel_GlobalJitLevel_ARRAYSIZE = OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OptimizerOptions_GlobalJitLevel_descriptor();
template<typename T>
inline const std::string& OptimizerOptions_GlobalJitLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OptimizerOptions_GlobalJitLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OptimizerOptions_GlobalJitLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OptimizerOptions_GlobalJitLevel_descriptor(), enum_t_value);
}
inline bool OptimizerOptions_GlobalJitLevel_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OptimizerOptions_GlobalJitLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OptimizerOptions_GlobalJitLevel>(
    OptimizerOptions_GlobalJitLevel_descriptor(), name, value);
}
enum ConfigProto_Experimental_MlirBridgeRollout : int {
  ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_UNSPECIFIED = 0,
  ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_ENABLED = 1,
  ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_DISABLED = 2,
  ConfigProto_Experimental_MlirBridgeRollout_ConfigProto_Experimental_MlirBridgeRollout_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ConfigProto_Experimental_MlirBridgeRollout_ConfigProto_Experimental_MlirBridgeRollout_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ConfigProto_Experimental_MlirBridgeRollout_IsValid(int value);
constexpr ConfigProto_Experimental_MlirBridgeRollout ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MIN = ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_UNSPECIFIED;
constexpr ConfigProto_Experimental_MlirBridgeRollout ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MAX = ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_DISABLED;
constexpr int ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_ARRAYSIZE = ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConfigProto_Experimental_MlirBridgeRollout_descriptor();
template<typename T>
inline const std::string& ConfigProto_Experimental_MlirBridgeRollout_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConfigProto_Experimental_MlirBridgeRollout>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConfigProto_Experimental_MlirBridgeRollout_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConfigProto_Experimental_MlirBridgeRollout_descriptor(), enum_t_value);
}
inline bool ConfigProto_Experimental_MlirBridgeRollout_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ConfigProto_Experimental_MlirBridgeRollout* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConfigProto_Experimental_MlirBridgeRollout>(
    ConfigProto_Experimental_MlirBridgeRollout_descriptor(), name, value);
}
enum RunOptions_TraceLevel : int {
  RunOptions_TraceLevel_NO_TRACE = 0,
  RunOptions_TraceLevel_SOFTWARE_TRACE = 1,
  RunOptions_TraceLevel_HARDWARE_TRACE = 2,
  RunOptions_TraceLevel_FULL_TRACE = 3,
  RunOptions_TraceLevel_RunOptions_TraceLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  RunOptions_TraceLevel_RunOptions_TraceLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool RunOptions_TraceLevel_IsValid(int value);
constexpr RunOptions_TraceLevel RunOptions_TraceLevel_TraceLevel_MIN = RunOptions_TraceLevel_NO_TRACE;
constexpr RunOptions_TraceLevel RunOptions_TraceLevel_TraceLevel_MAX = RunOptions_TraceLevel_FULL_TRACE;
constexpr int RunOptions_TraceLevel_TraceLevel_ARRAYSIZE = RunOptions_TraceLevel_TraceLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* RunOptions_TraceLevel_descriptor();
template<typename T>
inline const std::string& RunOptions_TraceLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, RunOptions_TraceLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function RunOptions_TraceLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    RunOptions_TraceLevel_descriptor(), enum_t_value);
}
inline bool RunOptions_TraceLevel_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, RunOptions_TraceLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<RunOptions_TraceLevel>(
    RunOptions_TraceLevel_descriptor(), name, value);
}
// ===================================================================

class GPUOptions_Experimental_VirtualDevices final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUOptions.Experimental.VirtualDevices) */ {
 public:
  inline GPUOptions_Experimental_VirtualDevices() : GPUOptions_Experimental_VirtualDevices(nullptr) {}
  ~GPUOptions_Experimental_VirtualDevices() override;
  explicit PROTOBUF_CONSTEXPR GPUOptions_Experimental_VirtualDevices(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GPUOptions_Experimental_VirtualDevices(const GPUOptions_Experimental_VirtualDevices& from);
  GPUOptions_Experimental_VirtualDevices(GPUOptions_Experimental_VirtualDevices&& from) noexcept
    : GPUOptions_Experimental_VirtualDevices() {
    *this = ::std::move(from);
  }

  inline GPUOptions_Experimental_VirtualDevices& operator=(const GPUOptions_Experimental_VirtualDevices& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUOptions_Experimental_VirtualDevices& operator=(GPUOptions_Experimental_VirtualDevices&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GPUOptions_Experimental_VirtualDevices& default_instance() {
    return *internal_default_instance();
  }
  static inline const GPUOptions_Experimental_VirtualDevices* internal_default_instance() {
    return reinterpret_cast<const GPUOptions_Experimental_VirtualDevices*>(
               &_GPUOptions_Experimental_VirtualDevices_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GPUOptions_Experimental_VirtualDevices& a, GPUOptions_Experimental_VirtualDevices& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUOptions_Experimental_VirtualDevices* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUOptions_Experimental_VirtualDevices* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GPUOptions_Experimental_VirtualDevices* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GPUOptions_Experimental_VirtualDevices>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GPUOptions_Experimental_VirtualDevices& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GPUOptions_Experimental_VirtualDevices& from) {
    GPUOptions_Experimental_VirtualDevices::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUOptions_Experimental_VirtualDevices* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUOptions.Experimental.VirtualDevices";
  }
  protected:
  explicit GPUOptions_Experimental_VirtualDevices(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMemoryLimitMbFieldNumber = 1,
    kPriorityFieldNumber = 2,
    kDeviceOrdinalFieldNumber = 3,
  };
  // repeated float memory_limit_mb = 1;
  int memory_limit_mb_size() const;
  private:
  int _internal_memory_limit_mb_size() const;
  public:
  void clear_memory_limit_mb();
  private:
  float _internal_memory_limit_mb(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_memory_limit_mb() const;
  void _internal_add_memory_limit_mb(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_memory_limit_mb();
  public:
  float memory_limit_mb(int index) const;
  void set_memory_limit_mb(int index, float value);
  void add_memory_limit_mb(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      memory_limit_mb() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_memory_limit_mb();

  // repeated int32 priority = 2;
  int priority_size() const;
  private:
  int _internal_priority_size() const;
  public:
  void clear_priority();
  private:
  int32_t _internal_priority(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_priority() const;
  void _internal_add_priority(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_priority();
  public:
  int32_t priority(int index) const;
  void set_priority(int index, int32_t value);
  void add_priority(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      priority() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_priority();

  // repeated int32 device_ordinal = 3;
  int device_ordinal_size() const;
  private:
  int _internal_device_ordinal_size() const;
  public:
  void clear_device_ordinal();
  private:
  int32_t _internal_device_ordinal(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_device_ordinal() const;
  void _internal_add_device_ordinal(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_device_ordinal();
  public:
  int32_t device_ordinal(int index) const;
  void set_device_ordinal(int index, int32_t value);
  void add_device_ordinal(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      device_ordinal() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_device_ordinal();

  // @@protoc_insertion_point(class_scope:tensorflow.GPUOptions.Experimental.VirtualDevices)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > memory_limit_mb_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > priority_;
    mutable std::atomic<int> _priority_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > device_ordinal_;
    mutable std::atomic<int> _device_ordinal_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class GPUOptions_Experimental_StreamMergeOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUOptions.Experimental.StreamMergeOptions) */ {
 public:
  inline GPUOptions_Experimental_StreamMergeOptions() : GPUOptions_Experimental_StreamMergeOptions(nullptr) {}
  ~GPUOptions_Experimental_StreamMergeOptions() override;
  explicit PROTOBUF_CONSTEXPR GPUOptions_Experimental_StreamMergeOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GPUOptions_Experimental_StreamMergeOptions(const GPUOptions_Experimental_StreamMergeOptions& from);
  GPUOptions_Experimental_StreamMergeOptions(GPUOptions_Experimental_StreamMergeOptions&& from) noexcept
    : GPUOptions_Experimental_StreamMergeOptions() {
    *this = ::std::move(from);
  }

  inline GPUOptions_Experimental_StreamMergeOptions& operator=(const GPUOptions_Experimental_StreamMergeOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUOptions_Experimental_StreamMergeOptions& operator=(GPUOptions_Experimental_StreamMergeOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GPUOptions_Experimental_StreamMergeOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const GPUOptions_Experimental_StreamMergeOptions* internal_default_instance() {
    return reinterpret_cast<const GPUOptions_Experimental_StreamMergeOptions*>(
               &_GPUOptions_Experimental_StreamMergeOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GPUOptions_Experimental_StreamMergeOptions& a, GPUOptions_Experimental_StreamMergeOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUOptions_Experimental_StreamMergeOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUOptions_Experimental_StreamMergeOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GPUOptions_Experimental_StreamMergeOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GPUOptions_Experimental_StreamMergeOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GPUOptions_Experimental_StreamMergeOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GPUOptions_Experimental_StreamMergeOptions& from) {
    GPUOptions_Experimental_StreamMergeOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUOptions_Experimental_StreamMergeOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUOptions.Experimental.StreamMergeOptions";
  }
  protected:
  explicit GPUOptions_Experimental_StreamMergeOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMergeHostToDeviceStreamFieldNumber = 1,
    kMergeDeviceToHostStreamFieldNumber = 2,
    kMergeDeviceToDeviceStreamFieldNumber = 3,
  };
  // bool merge_host_to_device_stream = 1;
  void clear_merge_host_to_device_stream();
  bool merge_host_to_device_stream() const;
  void set_merge_host_to_device_stream(bool value);
  private:
  bool _internal_merge_host_to_device_stream() const;
  void _internal_set_merge_host_to_device_stream(bool value);
  public:

  // bool merge_device_to_host_stream = 2;
  void clear_merge_device_to_host_stream();
  bool merge_device_to_host_stream() const;
  void set_merge_device_to_host_stream(bool value);
  private:
  bool _internal_merge_device_to_host_stream() const;
  void _internal_set_merge_device_to_host_stream(bool value);
  public:

  // bool merge_device_to_device_stream = 3;
  void clear_merge_device_to_device_stream();
  bool merge_device_to_device_stream() const;
  void set_merge_device_to_device_stream(bool value);
  private:
  bool _internal_merge_device_to_device_stream() const;
  void _internal_set_merge_device_to_device_stream(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GPUOptions.Experimental.StreamMergeOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool merge_host_to_device_stream_;
    bool merge_device_to_host_stream_;
    bool merge_device_to_device_stream_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class GPUOptions_Experimental final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUOptions.Experimental) */ {
 public:
  inline GPUOptions_Experimental() : GPUOptions_Experimental(nullptr) {}
  ~GPUOptions_Experimental() override;
  explicit PROTOBUF_CONSTEXPR GPUOptions_Experimental(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GPUOptions_Experimental(const GPUOptions_Experimental& from);
  GPUOptions_Experimental(GPUOptions_Experimental&& from) noexcept
    : GPUOptions_Experimental() {
    *this = ::std::move(from);
  }

  inline GPUOptions_Experimental& operator=(const GPUOptions_Experimental& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUOptions_Experimental& operator=(GPUOptions_Experimental&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GPUOptions_Experimental& default_instance() {
    return *internal_default_instance();
  }
  static inline const GPUOptions_Experimental* internal_default_instance() {
    return reinterpret_cast<const GPUOptions_Experimental*>(
               &_GPUOptions_Experimental_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GPUOptions_Experimental& a, GPUOptions_Experimental& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUOptions_Experimental* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUOptions_Experimental* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GPUOptions_Experimental* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GPUOptions_Experimental>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GPUOptions_Experimental& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GPUOptions_Experimental& from) {
    GPUOptions_Experimental::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUOptions_Experimental* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUOptions.Experimental";
  }
  protected:
  explicit GPUOptions_Experimental(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef GPUOptions_Experimental_VirtualDevices VirtualDevices;
  typedef GPUOptions_Experimental_StreamMergeOptions StreamMergeOptions;

  // accessors -------------------------------------------------------

  enum : int {
    kVirtualDevicesFieldNumber = 1,
    kCollectiveRingOrderFieldNumber = 4,
    kStreamMergeOptionsFieldNumber = 19,
    kNumDevToDevCopyStreamsFieldNumber = 3,
    kKernelTrackerMaxIntervalFieldNumber = 7,
    kUseUnifiedMemoryFieldNumber = 2,
    kTimestampedAllocatorFieldNumber = 5,
    kUseCudaMallocAsyncFieldNumber = 11,
    kDisallowRetryOnAllocationFailureFieldNumber = 12,
    kKernelTrackerMaxBytesFieldNumber = 8,
    kInternalFragmentationFractionFieldNumber = 10,
    kKernelTrackerMaxPendingFieldNumber = 9,
    kGpuHostMemLimitInMbFieldNumber = 13,
    kNumVirtualDevicesPerGpuFieldNumber = 15,
    kGpuHostMemDisallowGrowthFieldNumber = 14,
    kPopulatePjrtGpuClientCreationInfoFieldNumber = 17,
    kGpuSystemMemorySizeInMbFieldNumber = 16,
    kNodeIdFieldNumber = 18,
  };
  // repeated .tensorflow.GPUOptions.Experimental.VirtualDevices virtual_devices = 1;
  int virtual_devices_size() const;
  private:
  int _internal_virtual_devices_size() const;
  public:
  void clear_virtual_devices();
  ::tensorflow::GPUOptions_Experimental_VirtualDevices* mutable_virtual_devices(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices >*
      mutable_virtual_devices();
  private:
  const ::tensorflow::GPUOptions_Experimental_VirtualDevices& _internal_virtual_devices(int index) const;
  ::tensorflow::GPUOptions_Experimental_VirtualDevices* _internal_add_virtual_devices();
  public:
  const ::tensorflow::GPUOptions_Experimental_VirtualDevices& virtual_devices(int index) const;
  ::tensorflow::GPUOptions_Experimental_VirtualDevices* add_virtual_devices();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices >&
      virtual_devices() const;

  // string collective_ring_order = 4;
  void clear_collective_ring_order();
  const std::string& collective_ring_order() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_collective_ring_order(ArgT0&& arg0, ArgT... args);
  std::string* mutable_collective_ring_order();
  PROTOBUF_NODISCARD std::string* release_collective_ring_order();
  void set_allocated_collective_ring_order(std::string* collective_ring_order);
  private:
  const std::string& _internal_collective_ring_order() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_collective_ring_order(const std::string& value);
  std::string* _internal_mutable_collective_ring_order();
  public:

  // .tensorflow.GPUOptions.Experimental.StreamMergeOptions stream_merge_options = 19;
  bool has_stream_merge_options() const;
  private:
  bool _internal_has_stream_merge_options() const;
  public:
  void clear_stream_merge_options();
  const ::tensorflow::GPUOptions_Experimental_StreamMergeOptions& stream_merge_options() const;
  PROTOBUF_NODISCARD ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* release_stream_merge_options();
  ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* mutable_stream_merge_options();
  void set_allocated_stream_merge_options(::tensorflow::GPUOptions_Experimental_StreamMergeOptions* stream_merge_options);
  private:
  const ::tensorflow::GPUOptions_Experimental_StreamMergeOptions& _internal_stream_merge_options() const;
  ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* _internal_mutable_stream_merge_options();
  public:
  void unsafe_arena_set_allocated_stream_merge_options(
      ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* stream_merge_options);
  ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* unsafe_arena_release_stream_merge_options();

  // int32 num_dev_to_dev_copy_streams = 3;
  void clear_num_dev_to_dev_copy_streams();
  int32_t num_dev_to_dev_copy_streams() const;
  void set_num_dev_to_dev_copy_streams(int32_t value);
  private:
  int32_t _internal_num_dev_to_dev_copy_streams() const;
  void _internal_set_num_dev_to_dev_copy_streams(int32_t value);
  public:

  // int32 kernel_tracker_max_interval = 7;
  void clear_kernel_tracker_max_interval();
  int32_t kernel_tracker_max_interval() const;
  void set_kernel_tracker_max_interval(int32_t value);
  private:
  int32_t _internal_kernel_tracker_max_interval() const;
  void _internal_set_kernel_tracker_max_interval(int32_t value);
  public:

  // bool use_unified_memory = 2;
  void clear_use_unified_memory();
  bool use_unified_memory() const;
  void set_use_unified_memory(bool value);
  private:
  bool _internal_use_unified_memory() const;
  void _internal_set_use_unified_memory(bool value);
  public:

  // bool timestamped_allocator = 5;
  void clear_timestamped_allocator();
  bool timestamped_allocator() const;
  void set_timestamped_allocator(bool value);
  private:
  bool _internal_timestamped_allocator() const;
  void _internal_set_timestamped_allocator(bool value);
  public:

  // bool use_cuda_malloc_async = 11;
  void clear_use_cuda_malloc_async();
  bool use_cuda_malloc_async() const;
  void set_use_cuda_malloc_async(bool value);
  private:
  bool _internal_use_cuda_malloc_async() const;
  void _internal_set_use_cuda_malloc_async(bool value);
  public:

  // bool disallow_retry_on_allocation_failure = 12;
  void clear_disallow_retry_on_allocation_failure();
  bool disallow_retry_on_allocation_failure() const;
  void set_disallow_retry_on_allocation_failure(bool value);
  private:
  bool _internal_disallow_retry_on_allocation_failure() const;
  void _internal_set_disallow_retry_on_allocation_failure(bool value);
  public:

  // int32 kernel_tracker_max_bytes = 8;
  void clear_kernel_tracker_max_bytes();
  int32_t kernel_tracker_max_bytes() const;
  void set_kernel_tracker_max_bytes(int32_t value);
  private:
  int32_t _internal_kernel_tracker_max_bytes() const;
  void _internal_set_kernel_tracker_max_bytes(int32_t value);
  public:

  // double internal_fragmentation_fraction = 10;
  void clear_internal_fragmentation_fraction();
  double internal_fragmentation_fraction() const;
  void set_internal_fragmentation_fraction(double value);
  private:
  double _internal_internal_fragmentation_fraction() const;
  void _internal_set_internal_fragmentation_fraction(double value);
  public:

  // int32 kernel_tracker_max_pending = 9;
  void clear_kernel_tracker_max_pending();
  int32_t kernel_tracker_max_pending() const;
  void set_kernel_tracker_max_pending(int32_t value);
  private:
  int32_t _internal_kernel_tracker_max_pending() const;
  void _internal_set_kernel_tracker_max_pending(int32_t value);
  public:

  // float gpu_host_mem_limit_in_mb = 13;
  void clear_gpu_host_mem_limit_in_mb();
  float gpu_host_mem_limit_in_mb() const;
  void set_gpu_host_mem_limit_in_mb(float value);
  private:
  float _internal_gpu_host_mem_limit_in_mb() const;
  void _internal_set_gpu_host_mem_limit_in_mb(float value);
  public:

  // int32 num_virtual_devices_per_gpu = 15;
  void clear_num_virtual_devices_per_gpu();
  int32_t num_virtual_devices_per_gpu() const;
  void set_num_virtual_devices_per_gpu(int32_t value);
  private:
  int32_t _internal_num_virtual_devices_per_gpu() const;
  void _internal_set_num_virtual_devices_per_gpu(int32_t value);
  public:

  // bool gpu_host_mem_disallow_growth = 14;
  void clear_gpu_host_mem_disallow_growth();
  bool gpu_host_mem_disallow_growth() const;
  void set_gpu_host_mem_disallow_growth(bool value);
  private:
  bool _internal_gpu_host_mem_disallow_growth() const;
  void _internal_set_gpu_host_mem_disallow_growth(bool value);
  public:

  // bool populate_pjrt_gpu_client_creation_info = 17;
  void clear_populate_pjrt_gpu_client_creation_info();
  bool populate_pjrt_gpu_client_creation_info() const;
  void set_populate_pjrt_gpu_client_creation_info(bool value);
  private:
  bool _internal_populate_pjrt_gpu_client_creation_info() const;
  void _internal_set_populate_pjrt_gpu_client_creation_info(bool value);
  public:

  // int32 gpu_system_memory_size_in_mb = 16;
  void clear_gpu_system_memory_size_in_mb();
  int32_t gpu_system_memory_size_in_mb() const;
  void set_gpu_system_memory_size_in_mb(int32_t value);
  private:
  int32_t _internal_gpu_system_memory_size_in_mb() const;
  void _internal_set_gpu_system_memory_size_in_mb(int32_t value);
  public:

  // int32 node_id = 18;
  void clear_node_id();
  int32_t node_id() const;
  void set_node_id(int32_t value);
  private:
  int32_t _internal_node_id() const;
  void _internal_set_node_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GPUOptions.Experimental)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices > virtual_devices_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr collective_ring_order_;
    ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* stream_merge_options_;
    int32_t num_dev_to_dev_copy_streams_;
    int32_t kernel_tracker_max_interval_;
    bool use_unified_memory_;
    bool timestamped_allocator_;
    bool use_cuda_malloc_async_;
    bool disallow_retry_on_allocation_failure_;
    int32_t kernel_tracker_max_bytes_;
    double internal_fragmentation_fraction_;
    int32_t kernel_tracker_max_pending_;
    float gpu_host_mem_limit_in_mb_;
    int32_t num_virtual_devices_per_gpu_;
    bool gpu_host_mem_disallow_growth_;
    bool populate_pjrt_gpu_client_creation_info_;
    int32_t gpu_system_memory_size_in_mb_;
    int32_t node_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class GPUOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUOptions) */ {
 public:
  inline GPUOptions() : GPUOptions(nullptr) {}
  ~GPUOptions() override;
  explicit PROTOBUF_CONSTEXPR GPUOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GPUOptions(const GPUOptions& from);
  GPUOptions(GPUOptions&& from) noexcept
    : GPUOptions() {
    *this = ::std::move(from);
  }

  inline GPUOptions& operator=(const GPUOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUOptions& operator=(GPUOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GPUOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const GPUOptions* internal_default_instance() {
    return reinterpret_cast<const GPUOptions*>(
               &_GPUOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GPUOptions& a, GPUOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GPUOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GPUOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GPUOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GPUOptions& from) {
    GPUOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUOptions";
  }
  protected:
  explicit GPUOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef GPUOptions_Experimental Experimental;

  // accessors -------------------------------------------------------

  enum : int {
    kAllocatorTypeFieldNumber = 2,
    kVisibleDeviceListFieldNumber = 5,
    kExperimentalFieldNumber = 9,
    kPerProcessGpuMemoryFractionFieldNumber = 1,
    kDeferredDeletionBytesFieldNumber = 3,
    kPollingActiveDelayUsecsFieldNumber = 6,
    kAllowGrowthFieldNumber = 4,
    kForceGpuCompatibleFieldNumber = 8,
    kPollingInactiveDelayMsecsFieldNumber = 7,
  };
  // string allocator_type = 2;
  void clear_allocator_type();
  const std::string& allocator_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_allocator_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_allocator_type();
  PROTOBUF_NODISCARD std::string* release_allocator_type();
  void set_allocated_allocator_type(std::string* allocator_type);
  private:
  const std::string& _internal_allocator_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_allocator_type(const std::string& value);
  std::string* _internal_mutable_allocator_type();
  public:

  // string visible_device_list = 5;
  void clear_visible_device_list();
  const std::string& visible_device_list() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_visible_device_list(ArgT0&& arg0, ArgT... args);
  std::string* mutable_visible_device_list();
  PROTOBUF_NODISCARD std::string* release_visible_device_list();
  void set_allocated_visible_device_list(std::string* visible_device_list);
  private:
  const std::string& _internal_visible_device_list() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_visible_device_list(const std::string& value);
  std::string* _internal_mutable_visible_device_list();
  public:

  // .tensorflow.GPUOptions.Experimental experimental = 9;
  bool has_experimental() const;
  private:
  bool _internal_has_experimental() const;
  public:
  void clear_experimental();
  const ::tensorflow::GPUOptions_Experimental& experimental() const;
  PROTOBUF_NODISCARD ::tensorflow::GPUOptions_Experimental* release_experimental();
  ::tensorflow::GPUOptions_Experimental* mutable_experimental();
  void set_allocated_experimental(::tensorflow::GPUOptions_Experimental* experimental);
  private:
  const ::tensorflow::GPUOptions_Experimental& _internal_experimental() const;
  ::tensorflow::GPUOptions_Experimental* _internal_mutable_experimental();
  public:
  void unsafe_arena_set_allocated_experimental(
      ::tensorflow::GPUOptions_Experimental* experimental);
  ::tensorflow::GPUOptions_Experimental* unsafe_arena_release_experimental();

  // double per_process_gpu_memory_fraction = 1;
  void clear_per_process_gpu_memory_fraction();
  double per_process_gpu_memory_fraction() const;
  void set_per_process_gpu_memory_fraction(double value);
  private:
  double _internal_per_process_gpu_memory_fraction() const;
  void _internal_set_per_process_gpu_memory_fraction(double value);
  public:

  // int64 deferred_deletion_bytes = 3;
  void clear_deferred_deletion_bytes();
  int64_t deferred_deletion_bytes() const;
  void set_deferred_deletion_bytes(int64_t value);
  private:
  int64_t _internal_deferred_deletion_bytes() const;
  void _internal_set_deferred_deletion_bytes(int64_t value);
  public:

  // int32 polling_active_delay_usecs = 6;
  void clear_polling_active_delay_usecs();
  int32_t polling_active_delay_usecs() const;
  void set_polling_active_delay_usecs(int32_t value);
  private:
  int32_t _internal_polling_active_delay_usecs() const;
  void _internal_set_polling_active_delay_usecs(int32_t value);
  public:

  // bool allow_growth = 4;
  void clear_allow_growth();
  bool allow_growth() const;
  void set_allow_growth(bool value);
  private:
  bool _internal_allow_growth() const;
  void _internal_set_allow_growth(bool value);
  public:

  // bool force_gpu_compatible = 8;
  void clear_force_gpu_compatible();
  bool force_gpu_compatible() const;
  void set_force_gpu_compatible(bool value);
  private:
  bool _internal_force_gpu_compatible() const;
  void _internal_set_force_gpu_compatible(bool value);
  public:

  // int32 polling_inactive_delay_msecs = 7;
  void clear_polling_inactive_delay_msecs();
  int32_t polling_inactive_delay_msecs() const;
  void set_polling_inactive_delay_msecs(int32_t value);
  private:
  int32_t _internal_polling_inactive_delay_msecs() const;
  void _internal_set_polling_inactive_delay_msecs(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GPUOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr visible_device_list_;
    ::tensorflow::GPUOptions_Experimental* experimental_;
    double per_process_gpu_memory_fraction_;
    int64_t deferred_deletion_bytes_;
    int32_t polling_active_delay_usecs_;
    bool allow_growth_;
    bool force_gpu_compatible_;
    int32_t polling_inactive_delay_msecs_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class OptimizerOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OptimizerOptions) */ {
 public:
  inline OptimizerOptions() : OptimizerOptions(nullptr) {}
  ~OptimizerOptions() override;
  explicit PROTOBUF_CONSTEXPR OptimizerOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OptimizerOptions(const OptimizerOptions& from);
  OptimizerOptions(OptimizerOptions&& from) noexcept
    : OptimizerOptions() {
    *this = ::std::move(from);
  }

  inline OptimizerOptions& operator=(const OptimizerOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptimizerOptions& operator=(OptimizerOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OptimizerOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const OptimizerOptions* internal_default_instance() {
    return reinterpret_cast<const OptimizerOptions*>(
               &_OptimizerOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(OptimizerOptions& a, OptimizerOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(OptimizerOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OptimizerOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OptimizerOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OptimizerOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OptimizerOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OptimizerOptions& from) {
    OptimizerOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptimizerOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OptimizerOptions";
  }
  protected:
  explicit OptimizerOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OptimizerOptions_Level Level;
  static constexpr Level L1 =
    OptimizerOptions_Level_L1;
  static constexpr Level L0 =
    OptimizerOptions_Level_L0;
  static inline bool Level_IsValid(int value) {
    return OptimizerOptions_Level_IsValid(value);
  }
  static constexpr Level Level_MIN =
    OptimizerOptions_Level_Level_MIN;
  static constexpr Level Level_MAX =
    OptimizerOptions_Level_Level_MAX;
  static constexpr int Level_ARRAYSIZE =
    OptimizerOptions_Level_Level_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Level_descriptor() {
    return OptimizerOptions_Level_descriptor();
  }
  template<typename T>
  static inline const std::string& Level_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Level>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Level_Name.");
    return OptimizerOptions_Level_Name(enum_t_value);
  }
  static inline bool Level_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Level* value) {
    return OptimizerOptions_Level_Parse(name, value);
  }

  typedef OptimizerOptions_GlobalJitLevel GlobalJitLevel;
  static constexpr GlobalJitLevel DEFAULT =
    OptimizerOptions_GlobalJitLevel_DEFAULT;
  static constexpr GlobalJitLevel OFF =
    OptimizerOptions_GlobalJitLevel_OFF;
  static constexpr GlobalJitLevel ON_1 =
    OptimizerOptions_GlobalJitLevel_ON_1;
  static constexpr GlobalJitLevel ON_2 =
    OptimizerOptions_GlobalJitLevel_ON_2;
  static inline bool GlobalJitLevel_IsValid(int value) {
    return OptimizerOptions_GlobalJitLevel_IsValid(value);
  }
  static constexpr GlobalJitLevel GlobalJitLevel_MIN =
    OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MIN;
  static constexpr GlobalJitLevel GlobalJitLevel_MAX =
    OptimizerOptions_GlobalJitLevel_GlobalJitLevel_MAX;
  static constexpr int GlobalJitLevel_ARRAYSIZE =
    OptimizerOptions_GlobalJitLevel_GlobalJitLevel_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  GlobalJitLevel_descriptor() {
    return OptimizerOptions_GlobalJitLevel_descriptor();
  }
  template<typename T>
  static inline const std::string& GlobalJitLevel_Name(T enum_t_value) {
    static_assert(::std::is_same<T, GlobalJitLevel>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function GlobalJitLevel_Name.");
    return OptimizerOptions_GlobalJitLevel_Name(enum_t_value);
  }
  static inline bool GlobalJitLevel_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      GlobalJitLevel* value) {
    return OptimizerOptions_GlobalJitLevel_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kOptLevelFieldNumber = 3,
    kDoCommonSubexpressionEliminationFieldNumber = 1,
    kDoConstantFoldingFieldNumber = 2,
    kDoFunctionInliningFieldNumber = 4,
    kCpuGlobalJitFieldNumber = 7,
    kMaxFoldedConstantInBytesFieldNumber = 6,
    kGlobalJitLevelFieldNumber = 5,
  };
  // .tensorflow.OptimizerOptions.Level opt_level = 3;
  void clear_opt_level();
  ::tensorflow::OptimizerOptions_Level opt_level() const;
  void set_opt_level(::tensorflow::OptimizerOptions_Level value);
  private:
  ::tensorflow::OptimizerOptions_Level _internal_opt_level() const;
  void _internal_set_opt_level(::tensorflow::OptimizerOptions_Level value);
  public:

  // bool do_common_subexpression_elimination = 1;
  void clear_do_common_subexpression_elimination();
  bool do_common_subexpression_elimination() const;
  void set_do_common_subexpression_elimination(bool value);
  private:
  bool _internal_do_common_subexpression_elimination() const;
  void _internal_set_do_common_subexpression_elimination(bool value);
  public:

  // bool do_constant_folding = 2;
  void clear_do_constant_folding();
  bool do_constant_folding() const;
  void set_do_constant_folding(bool value);
  private:
  bool _internal_do_constant_folding() const;
  void _internal_set_do_constant_folding(bool value);
  public:

  // bool do_function_inlining = 4;
  void clear_do_function_inlining();
  bool do_function_inlining() const;
  void set_do_function_inlining(bool value);
  private:
  bool _internal_do_function_inlining() const;
  void _internal_set_do_function_inlining(bool value);
  public:

  // bool cpu_global_jit = 7;
  void clear_cpu_global_jit();
  bool cpu_global_jit() const;
  void set_cpu_global_jit(bool value);
  private:
  bool _internal_cpu_global_jit() const;
  void _internal_set_cpu_global_jit(bool value);
  public:

  // int64 max_folded_constant_in_bytes = 6;
  void clear_max_folded_constant_in_bytes();
  int64_t max_folded_constant_in_bytes() const;
  void set_max_folded_constant_in_bytes(int64_t value);
  private:
  int64_t _internal_max_folded_constant_in_bytes() const;
  void _internal_set_max_folded_constant_in_bytes(int64_t value);
  public:

  // .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 5;
  void clear_global_jit_level();
  ::tensorflow::OptimizerOptions_GlobalJitLevel global_jit_level() const;
  void set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value);
  private:
  ::tensorflow::OptimizerOptions_GlobalJitLevel _internal_global_jit_level() const;
  void _internal_set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.OptimizerOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int opt_level_;
    bool do_common_subexpression_elimination_;
    bool do_constant_folding_;
    bool do_function_inlining_;
    bool cpu_global_jit_;
    int64_t max_folded_constant_in_bytes_;
    int global_jit_level_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class GraphOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphOptions) */ {
 public:
  inline GraphOptions() : GraphOptions(nullptr) {}
  ~GraphOptions() override;
  explicit PROTOBUF_CONSTEXPR GraphOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphOptions(const GraphOptions& from);
  GraphOptions(GraphOptions&& from) noexcept
    : GraphOptions() {
    *this = ::std::move(from);
  }

  inline GraphOptions& operator=(const GraphOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphOptions& operator=(GraphOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphOptions* internal_default_instance() {
    return reinterpret_cast<const GraphOptions*>(
               &_GraphOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GraphOptions& a, GraphOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphOptions& from) {
    GraphOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphOptions";
  }
  protected:
  explicit GraphOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOptimizerOptionsFieldNumber = 3,
    kRewriteOptionsFieldNumber = 10,
    kBuildCostModelFieldNumber = 4,
    kEnableRecvSchedulingFieldNumber = 2,
    kInferShapesFieldNumber = 5,
    kPlacePrunedGraphFieldNumber = 6,
    kEnableBfloat16SendrecvFieldNumber = 7,
    kTimelineStepFieldNumber = 8,
    kBuildCostModelAfterFieldNumber = 9,
  };
  // .tensorflow.OptimizerOptions optimizer_options = 3;
  bool has_optimizer_options() const;
  private:
  bool _internal_has_optimizer_options() const;
  public:
  void clear_optimizer_options();
  const ::tensorflow::OptimizerOptions& optimizer_options() const;
  PROTOBUF_NODISCARD ::tensorflow::OptimizerOptions* release_optimizer_options();
  ::tensorflow::OptimizerOptions* mutable_optimizer_options();
  void set_allocated_optimizer_options(::tensorflow::OptimizerOptions* optimizer_options);
  private:
  const ::tensorflow::OptimizerOptions& _internal_optimizer_options() const;
  ::tensorflow::OptimizerOptions* _internal_mutable_optimizer_options();
  public:
  void unsafe_arena_set_allocated_optimizer_options(
      ::tensorflow::OptimizerOptions* optimizer_options);
  ::tensorflow::OptimizerOptions* unsafe_arena_release_optimizer_options();

  // .tensorflow.RewriterConfig rewrite_options = 10;
  bool has_rewrite_options() const;
  private:
  bool _internal_has_rewrite_options() const;
  public:
  void clear_rewrite_options();
  const ::tensorflow::RewriterConfig& rewrite_options() const;
  PROTOBUF_NODISCARD ::tensorflow::RewriterConfig* release_rewrite_options();
  ::tensorflow::RewriterConfig* mutable_rewrite_options();
  void set_allocated_rewrite_options(::tensorflow::RewriterConfig* rewrite_options);
  private:
  const ::tensorflow::RewriterConfig& _internal_rewrite_options() const;
  ::tensorflow::RewriterConfig* _internal_mutable_rewrite_options();
  public:
  void unsafe_arena_set_allocated_rewrite_options(
      ::tensorflow::RewriterConfig* rewrite_options);
  ::tensorflow::RewriterConfig* unsafe_arena_release_rewrite_options();

  // int64 build_cost_model = 4;
  void clear_build_cost_model();
  int64_t build_cost_model() const;
  void set_build_cost_model(int64_t value);
  private:
  int64_t _internal_build_cost_model() const;
  void _internal_set_build_cost_model(int64_t value);
  public:

  // bool enable_recv_scheduling = 2;
  void clear_enable_recv_scheduling();
  bool enable_recv_scheduling() const;
  void set_enable_recv_scheduling(bool value);
  private:
  bool _internal_enable_recv_scheduling() const;
  void _internal_set_enable_recv_scheduling(bool value);
  public:

  // bool infer_shapes = 5;
  void clear_infer_shapes();
  bool infer_shapes() const;
  void set_infer_shapes(bool value);
  private:
  bool _internal_infer_shapes() const;
  void _internal_set_infer_shapes(bool value);
  public:

  // bool place_pruned_graph = 6;
  void clear_place_pruned_graph();
  bool place_pruned_graph() const;
  void set_place_pruned_graph(bool value);
  private:
  bool _internal_place_pruned_graph() const;
  void _internal_set_place_pruned_graph(bool value);
  public:

  // bool enable_bfloat16_sendrecv = 7;
  void clear_enable_bfloat16_sendrecv();
  bool enable_bfloat16_sendrecv() const;
  void set_enable_bfloat16_sendrecv(bool value);
  private:
  bool _internal_enable_bfloat16_sendrecv() const;
  void _internal_set_enable_bfloat16_sendrecv(bool value);
  public:

  // int32 timeline_step = 8;
  void clear_timeline_step();
  int32_t timeline_step() const;
  void set_timeline_step(int32_t value);
  private:
  int32_t _internal_timeline_step() const;
  void _internal_set_timeline_step(int32_t value);
  public:

  // int64 build_cost_model_after = 9;
  void clear_build_cost_model_after();
  int64_t build_cost_model_after() const;
  void set_build_cost_model_after(int64_t value);
  private:
  int64_t _internal_build_cost_model_after() const;
  void _internal_set_build_cost_model_after(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::OptimizerOptions* optimizer_options_;
    ::tensorflow::RewriterConfig* rewrite_options_;
    int64_t build_cost_model_;
    bool enable_recv_scheduling_;
    bool infer_shapes_;
    bool place_pruned_graph_;
    bool enable_bfloat16_sendrecv_;
    int32_t timeline_step_;
    int64_t build_cost_model_after_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class ThreadPoolOptionProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ThreadPoolOptionProto) */ {
 public:
  inline ThreadPoolOptionProto() : ThreadPoolOptionProto(nullptr) {}
  ~ThreadPoolOptionProto() override;
  explicit PROTOBUF_CONSTEXPR ThreadPoolOptionProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ThreadPoolOptionProto(const ThreadPoolOptionProto& from);
  ThreadPoolOptionProto(ThreadPoolOptionProto&& from) noexcept
    : ThreadPoolOptionProto() {
    *this = ::std::move(from);
  }

  inline ThreadPoolOptionProto& operator=(const ThreadPoolOptionProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ThreadPoolOptionProto& operator=(ThreadPoolOptionProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ThreadPoolOptionProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ThreadPoolOptionProto* internal_default_instance() {
    return reinterpret_cast<const ThreadPoolOptionProto*>(
               &_ThreadPoolOptionProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ThreadPoolOptionProto& a, ThreadPoolOptionProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ThreadPoolOptionProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ThreadPoolOptionProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ThreadPoolOptionProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ThreadPoolOptionProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ThreadPoolOptionProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ThreadPoolOptionProto& from) {
    ThreadPoolOptionProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ThreadPoolOptionProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ThreadPoolOptionProto";
  }
  protected:
  explicit ThreadPoolOptionProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGlobalNameFieldNumber = 2,
    kNumThreadsFieldNumber = 1,
  };
  // string global_name = 2;
  void clear_global_name();
  const std::string& global_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_global_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_global_name();
  PROTOBUF_NODISCARD std::string* release_global_name();
  void set_allocated_global_name(std::string* global_name);
  private:
  const std::string& _internal_global_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_global_name(const std::string& value);
  std::string* _internal_mutable_global_name();
  public:

  // int32 num_threads = 1;
  void clear_num_threads();
  int32_t num_threads() const;
  void set_num_threads(int32_t value);
  private:
  int32_t _internal_num_threads() const;
  void _internal_set_num_threads(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ThreadPoolOptionProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr global_name_;
    int32_t num_threads_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class SessionMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SessionMetadata) */ {
 public:
  inline SessionMetadata() : SessionMetadata(nullptr) {}
  ~SessionMetadata() override;
  explicit PROTOBUF_CONSTEXPR SessionMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SessionMetadata(const SessionMetadata& from);
  SessionMetadata(SessionMetadata&& from) noexcept
    : SessionMetadata() {
    *this = ::std::move(from);
  }

  inline SessionMetadata& operator=(const SessionMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline SessionMetadata& operator=(SessionMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SessionMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const SessionMetadata* internal_default_instance() {
    return reinterpret_cast<const SessionMetadata*>(
               &_SessionMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(SessionMetadata& a, SessionMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(SessionMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SessionMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SessionMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SessionMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SessionMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SessionMetadata& from) {
    SessionMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SessionMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SessionMetadata";
  }
  protected:
  explicit SessionMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kVersionFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 version = 2;
  void clear_version();
  int64_t version() const;
  void set_version(int64_t value);
  private:
  int64_t _internal_version() const;
  void _internal_set_version(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SessionMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int64_t version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class ConfigProto_DeviceCountEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ConfigProto_DeviceCountEntry_DoNotUse, 
    std::string, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ConfigProto_DeviceCountEntry_DoNotUse, 
    std::string, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  ConfigProto_DeviceCountEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ConfigProto_DeviceCountEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ConfigProto_DeviceCountEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ConfigProto_DeviceCountEntry_DoNotUse& other);
  static const ConfigProto_DeviceCountEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ConfigProto_DeviceCountEntry_DoNotUse*>(&_ConfigProto_DeviceCountEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ConfigProto.DeviceCountEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};

// -------------------------------------------------------------------

class ConfigProto_Experimental final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ConfigProto.Experimental) */ {
 public:
  inline ConfigProto_Experimental() : ConfigProto_Experimental(nullptr) {}
  ~ConfigProto_Experimental() override;
  explicit PROTOBUF_CONSTEXPR ConfigProto_Experimental(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigProto_Experimental(const ConfigProto_Experimental& from);
  ConfigProto_Experimental(ConfigProto_Experimental&& from) noexcept
    : ConfigProto_Experimental() {
    *this = ::std::move(from);
  }

  inline ConfigProto_Experimental& operator=(const ConfigProto_Experimental& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigProto_Experimental& operator=(ConfigProto_Experimental&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigProto_Experimental& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigProto_Experimental* internal_default_instance() {
    return reinterpret_cast<const ConfigProto_Experimental*>(
               &_ConfigProto_Experimental_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(ConfigProto_Experimental& a, ConfigProto_Experimental& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigProto_Experimental* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigProto_Experimental* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigProto_Experimental* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigProto_Experimental>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigProto_Experimental& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ConfigProto_Experimental& from) {
    ConfigProto_Experimental::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigProto_Experimental* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ConfigProto.Experimental";
  }
  protected:
  explicit ConfigProto_Experimental(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ConfigProto_Experimental_MlirBridgeRollout MlirBridgeRollout;
  static constexpr MlirBridgeRollout MLIR_BRIDGE_ROLLOUT_UNSPECIFIED =
    ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_UNSPECIFIED;
  static constexpr MlirBridgeRollout MLIR_BRIDGE_ROLLOUT_ENABLED =
    ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_ENABLED;
  static constexpr MlirBridgeRollout MLIR_BRIDGE_ROLLOUT_DISABLED =
    ConfigProto_Experimental_MlirBridgeRollout_MLIR_BRIDGE_ROLLOUT_DISABLED;
  static inline bool MlirBridgeRollout_IsValid(int value) {
    return ConfigProto_Experimental_MlirBridgeRollout_IsValid(value);
  }
  static constexpr MlirBridgeRollout MlirBridgeRollout_MIN =
    ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MIN;
  static constexpr MlirBridgeRollout MlirBridgeRollout_MAX =
    ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_MAX;
  static constexpr int MlirBridgeRollout_ARRAYSIZE =
    ConfigProto_Experimental_MlirBridgeRollout_MlirBridgeRollout_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MlirBridgeRollout_descriptor() {
    return ConfigProto_Experimental_MlirBridgeRollout_descriptor();
  }
  template<typename T>
  static inline const std::string& MlirBridgeRollout_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MlirBridgeRollout>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MlirBridgeRollout_Name.");
    return ConfigProto_Experimental_MlirBridgeRollout_Name(enum_t_value);
  }
  static inline bool MlirBridgeRollout_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      MlirBridgeRollout* value) {
    return ConfigProto_Experimental_MlirBridgeRollout_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kCollectiveGroupLeaderFieldNumber = 1,
    kExecutorTypeFieldNumber = 3,
    kSessionMetadataFieldNumber = 11,
    kCoordinationConfigFieldNumber = 23,
    kRecvBufMaxChunkFieldNumber = 4,
    kUseNumaAffinityFieldNumber = 5,
    kCollectiveDeterministicSequentialExecutionFieldNumber = 6,
    kCollectiveNcclFieldNumber = 7,
    kShareSessionStateInClusterspecPropagationFieldNumber = 8,
    kDisableThreadSpinningFieldNumber = 9,
    kShareClusterDevicesInSessionFieldNumber = 10,
    kOptimizeForStaticGraphFieldNumber = 12,
    kEnableMlirBridgeFieldNumber = 13,
    kMlirBridgeRolloutFieldNumber = 17,
    kXlaFusionAutotunerThreshFieldNumber = 15,
    kEnableMlirGraphOptimizationFieldNumber = 16,
    kDisableOutputPartitionGraphsFieldNumber = 14,
    kUseTfrtFieldNumber = 18,
    kEnableMultiHostFieldNumber = 27,
    kXlaPreferSingleGraphClusterFieldNumber = 22,
    kDisableOptimizeForStaticGraphFieldNumber = 24,
    kDisableEagerExecutorStreamingEnqueueFieldNumber = 26,
    kBackendServerPortFieldNumber = 28,
    kTfrtUseIfrtFieldNumber = 32,
    kTargetTpuFieldNumber = 29,
    kTargetGpuFieldNumber = 30,
    kDisableFunctionalOpsLoweringFieldNumber = 21,
    kStreamMergeThresholdFieldNumber = 31,
  };
  // string collective_group_leader = 1;
  void clear_collective_group_leader();
  const std::string& collective_group_leader() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_collective_group_leader(ArgT0&& arg0, ArgT... args);
  std::string* mutable_collective_group_leader();
  PROTOBUF_NODISCARD std::string* release_collective_group_leader();
  void set_allocated_collective_group_leader(std::string* collective_group_leader);
  private:
  const std::string& _internal_collective_group_leader() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_collective_group_leader(const std::string& value);
  std::string* _internal_mutable_collective_group_leader();
  public:

  // string executor_type = 3;
  void clear_executor_type();
  const std::string& executor_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_executor_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_executor_type();
  PROTOBUF_NODISCARD std::string* release_executor_type();
  void set_allocated_executor_type(std::string* executor_type);
  private:
  const std::string& _internal_executor_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_executor_type(const std::string& value);
  std::string* _internal_mutable_executor_type();
  public:

  // .tensorflow.SessionMetadata session_metadata = 11;
  bool has_session_metadata() const;
  private:
  bool _internal_has_session_metadata() const;
  public:
  void clear_session_metadata();
  const ::tensorflow::SessionMetadata& session_metadata() const;
  PROTOBUF_NODISCARD ::tensorflow::SessionMetadata* release_session_metadata();
  ::tensorflow::SessionMetadata* mutable_session_metadata();
  void set_allocated_session_metadata(::tensorflow::SessionMetadata* session_metadata);
  private:
  const ::tensorflow::SessionMetadata& _internal_session_metadata() const;
  ::tensorflow::SessionMetadata* _internal_mutable_session_metadata();
  public:
  void unsafe_arena_set_allocated_session_metadata(
      ::tensorflow::SessionMetadata* session_metadata);
  ::tensorflow::SessionMetadata* unsafe_arena_release_session_metadata();

  // .tensorflow.CoordinationServiceConfig coordination_config = 23;
  bool has_coordination_config() const;
  private:
  bool _internal_has_coordination_config() const;
  public:
  void clear_coordination_config();
  const ::tensorflow::CoordinationServiceConfig& coordination_config() const;
  PROTOBUF_NODISCARD ::tensorflow::CoordinationServiceConfig* release_coordination_config();
  ::tensorflow::CoordinationServiceConfig* mutable_coordination_config();
  void set_allocated_coordination_config(::tensorflow::CoordinationServiceConfig* coordination_config);
  private:
  const ::tensorflow::CoordinationServiceConfig& _internal_coordination_config() const;
  ::tensorflow::CoordinationServiceConfig* _internal_mutable_coordination_config();
  public:
  void unsafe_arena_set_allocated_coordination_config(
      ::tensorflow::CoordinationServiceConfig* coordination_config);
  ::tensorflow::CoordinationServiceConfig* unsafe_arena_release_coordination_config();

  // int32 recv_buf_max_chunk = 4;
  void clear_recv_buf_max_chunk();
  int32_t recv_buf_max_chunk() const;
  void set_recv_buf_max_chunk(int32_t value);
  private:
  int32_t _internal_recv_buf_max_chunk() const;
  void _internal_set_recv_buf_max_chunk(int32_t value);
  public:

  // bool use_numa_affinity = 5;
  void clear_use_numa_affinity();
  bool use_numa_affinity() const;
  void set_use_numa_affinity(bool value);
  private:
  bool _internal_use_numa_affinity() const;
  void _internal_set_use_numa_affinity(bool value);
  public:

  // bool collective_deterministic_sequential_execution = 6;
  void clear_collective_deterministic_sequential_execution();
  bool collective_deterministic_sequential_execution() const;
  void set_collective_deterministic_sequential_execution(bool value);
  private:
  bool _internal_collective_deterministic_sequential_execution() const;
  void _internal_set_collective_deterministic_sequential_execution(bool value);
  public:

  // bool collective_nccl = 7;
  void clear_collective_nccl();
  bool collective_nccl() const;
  void set_collective_nccl(bool value);
  private:
  bool _internal_collective_nccl() const;
  void _internal_set_collective_nccl(bool value);
  public:

  // bool share_session_state_in_clusterspec_propagation = 8;
  void clear_share_session_state_in_clusterspec_propagation();
  bool share_session_state_in_clusterspec_propagation() const;
  void set_share_session_state_in_clusterspec_propagation(bool value);
  private:
  bool _internal_share_session_state_in_clusterspec_propagation() const;
  void _internal_set_share_session_state_in_clusterspec_propagation(bool value);
  public:

  // bool disable_thread_spinning = 9;
  void clear_disable_thread_spinning();
  bool disable_thread_spinning() const;
  void set_disable_thread_spinning(bool value);
  private:
  bool _internal_disable_thread_spinning() const;
  void _internal_set_disable_thread_spinning(bool value);
  public:

  // bool share_cluster_devices_in_session = 10;
  void clear_share_cluster_devices_in_session();
  bool share_cluster_devices_in_session() const;
  void set_share_cluster_devices_in_session(bool value);
  private:
  bool _internal_share_cluster_devices_in_session() const;
  void _internal_set_share_cluster_devices_in_session(bool value);
  public:

  // bool optimize_for_static_graph = 12;
  void clear_optimize_for_static_graph();
  bool optimize_for_static_graph() const;
  void set_optimize_for_static_graph(bool value);
  private:
  bool _internal_optimize_for_static_graph() const;
  void _internal_set_optimize_for_static_graph(bool value);
  public:

  // bool enable_mlir_bridge = 13;
  void clear_enable_mlir_bridge();
  bool enable_mlir_bridge() const;
  void set_enable_mlir_bridge(bool value);
  private:
  bool _internal_enable_mlir_bridge() const;
  void _internal_set_enable_mlir_bridge(bool value);
  public:

  // .tensorflow.ConfigProto.Experimental.MlirBridgeRollout mlir_bridge_rollout = 17;
  void clear_mlir_bridge_rollout();
  ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout mlir_bridge_rollout() const;
  void set_mlir_bridge_rollout(::tensorflow::ConfigProto_Experimental_MlirBridgeRollout value);
  private:
  ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout _internal_mlir_bridge_rollout() const;
  void _internal_set_mlir_bridge_rollout(::tensorflow::ConfigProto_Experimental_MlirBridgeRollout value);
  public:

  // int64 xla_fusion_autotuner_thresh = 15;
  void clear_xla_fusion_autotuner_thresh();
  int64_t xla_fusion_autotuner_thresh() const;
  void set_xla_fusion_autotuner_thresh(int64_t value);
  private:
  int64_t _internal_xla_fusion_autotuner_thresh() const;
  void _internal_set_xla_fusion_autotuner_thresh(int64_t value);
  public:

  // bool enable_mlir_graph_optimization = 16;
  void clear_enable_mlir_graph_optimization();
  bool enable_mlir_graph_optimization() const;
  void set_enable_mlir_graph_optimization(bool value);
  private:
  bool _internal_enable_mlir_graph_optimization() const;
  void _internal_set_enable_mlir_graph_optimization(bool value);
  public:

  // bool disable_output_partition_graphs = 14;
  void clear_disable_output_partition_graphs();
  bool disable_output_partition_graphs() const;
  void set_disable_output_partition_graphs(bool value);
  private:
  bool _internal_disable_output_partition_graphs() const;
  void _internal_set_disable_output_partition_graphs(bool value);
  public:

  // bool use_tfrt = 18;
  void clear_use_tfrt();
  bool use_tfrt() const;
  void set_use_tfrt(bool value);
  private:
  bool _internal_use_tfrt() const;
  void _internal_set_use_tfrt(bool value);
  public:

  // bool enable_multi_host = 27;
  void clear_enable_multi_host();
  bool enable_multi_host() const;
  void set_enable_multi_host(bool value);
  private:
  bool _internal_enable_multi_host() const;
  void _internal_set_enable_multi_host(bool value);
  public:

  // bool xla_prefer_single_graph_cluster = 22;
  void clear_xla_prefer_single_graph_cluster();
  bool xla_prefer_single_graph_cluster() const;
  void set_xla_prefer_single_graph_cluster(bool value);
  private:
  bool _internal_xla_prefer_single_graph_cluster() const;
  void _internal_set_xla_prefer_single_graph_cluster(bool value);
  public:

  // bool disable_optimize_for_static_graph = 24;
  void clear_disable_optimize_for_static_graph();
  bool disable_optimize_for_static_graph() const;
  void set_disable_optimize_for_static_graph(bool value);
  private:
  bool _internal_disable_optimize_for_static_graph() const;
  void _internal_set_disable_optimize_for_static_graph(bool value);
  public:

  // bool disable_eager_executor_streaming_enqueue = 26;
  void clear_disable_eager_executor_streaming_enqueue();
  bool disable_eager_executor_streaming_enqueue() const;
  void set_disable_eager_executor_streaming_enqueue(bool value);
  private:
  bool _internal_disable_eager_executor_streaming_enqueue() const;
  void _internal_set_disable_eager_executor_streaming_enqueue(bool value);
  public:

  // int32 backend_server_port = 28;
  void clear_backend_server_port();
  int32_t backend_server_port() const;
  void set_backend_server_port(int32_t value);
  private:
  int32_t _internal_backend_server_port() const;
  void _internal_set_backend_server_port(int32_t value);
  public:

  // bool tfrt_use_ifrt = 32;
  void clear_tfrt_use_ifrt();
  bool tfrt_use_ifrt() const;
  void set_tfrt_use_ifrt(bool value);
  private:
  bool _internal_tfrt_use_ifrt() const;
  void _internal_set_tfrt_use_ifrt(bool value);
  public:

  // bool target_tpu = 29;
  void clear_target_tpu();
  bool target_tpu() const;
  void set_target_tpu(bool value);
  private:
  bool _internal_target_tpu() const;
  void _internal_set_target_tpu(bool value);
  public:

  // bool target_gpu = 30;
  void clear_target_gpu();
  bool target_gpu() const;
  void set_target_gpu(bool value);
  private:
  bool _internal_target_gpu() const;
  void _internal_set_target_gpu(bool value);
  public:

  // bool disable_functional_ops_lowering = 21;
  void clear_disable_functional_ops_lowering();
  bool disable_functional_ops_lowering() const;
  void set_disable_functional_ops_lowering(bool value);
  private:
  bool _internal_disable_functional_ops_lowering() const;
  void _internal_set_disable_functional_ops_lowering(bool value);
  public:

  // int32 stream_merge_threshold = 31;
  void clear_stream_merge_threshold();
  int32_t stream_merge_threshold() const;
  void set_stream_merge_threshold(int32_t value);
  private:
  int32_t _internal_stream_merge_threshold() const;
  void _internal_set_stream_merge_threshold(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ConfigProto.Experimental)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr collective_group_leader_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr executor_type_;
    ::tensorflow::SessionMetadata* session_metadata_;
    ::tensorflow::CoordinationServiceConfig* coordination_config_;
    int32_t recv_buf_max_chunk_;
    bool use_numa_affinity_;
    bool collective_deterministic_sequential_execution_;
    bool collective_nccl_;
    bool share_session_state_in_clusterspec_propagation_;
    bool disable_thread_spinning_;
    bool share_cluster_devices_in_session_;
    bool optimize_for_static_graph_;
    bool enable_mlir_bridge_;
    int mlir_bridge_rollout_;
    int64_t xla_fusion_autotuner_thresh_;
    bool enable_mlir_graph_optimization_;
    bool disable_output_partition_graphs_;
    bool use_tfrt_;
    bool enable_multi_host_;
    bool xla_prefer_single_graph_cluster_;
    bool disable_optimize_for_static_graph_;
    bool disable_eager_executor_streaming_enqueue_;
    int32_t backend_server_port_;
    bool tfrt_use_ifrt_;
    bool target_tpu_;
    bool target_gpu_;
    bool disable_functional_ops_lowering_;
    int32_t stream_merge_threshold_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class ConfigProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ConfigProto) */ {
 public:
  inline ConfigProto() : ConfigProto(nullptr) {}
  ~ConfigProto() override;
  explicit PROTOBUF_CONSTEXPR ConfigProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigProto(const ConfigProto& from);
  ConfigProto(ConfigProto&& from) noexcept
    : ConfigProto() {
    *this = ::std::move(from);
  }

  inline ConfigProto& operator=(const ConfigProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigProto& operator=(ConfigProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigProto* internal_default_instance() {
    return reinterpret_cast<const ConfigProto*>(
               &_ConfigProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(ConfigProto& a, ConfigProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ConfigProto& from) {
    ConfigProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ConfigProto";
  }
  protected:
  explicit ConfigProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ConfigProto_Experimental Experimental;

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceCountFieldNumber = 1,
    kDeviceFiltersFieldNumber = 4,
    kSessionInterOpThreadPoolFieldNumber = 12,
    kGpuOptionsFieldNumber = 6,
    kGraphOptionsFieldNumber = 10,
    kRpcOptionsFieldNumber = 13,
    kClusterDefFieldNumber = 14,
    kExperimentalFieldNumber = 16,
    kPluggableDeviceOptionsFieldNumber = 18,
    kIntraOpParallelismThreadsFieldNumber = 2,
    kPlacementPeriodFieldNumber = 3,
    kInterOpParallelismThreadsFieldNumber = 5,
    kUsePerSessionThreadsFieldNumber = 9,
    kAllowSoftPlacementFieldNumber = 7,
    kLogDevicePlacementFieldNumber = 8,
    kIsolateSessionStateFieldNumber = 15,
    kOperationTimeoutInMsFieldNumber = 11,
    kShareClusterDevicesInSessionFieldNumber = 17,
  };
  // map<string, int32> device_count = 1;
  int device_count_size() const;
  private:
  int _internal_device_count_size() const;
  public:
  void clear_device_count();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
      _internal_device_count() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
      _internal_mutable_device_count();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
      device_count() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
      mutable_device_count();

  // repeated string device_filters = 4;
  int device_filters_size() const;
  private:
  int _internal_device_filters_size() const;
  public:
  void clear_device_filters();
  const std::string& device_filters(int index) const;
  std::string* mutable_device_filters(int index);
  void set_device_filters(int index, const std::string& value);
  void set_device_filters(int index, std::string&& value);
  void set_device_filters(int index, const char* value);
  void set_device_filters(int index, const char* value, size_t size);
  std::string* add_device_filters();
  void add_device_filters(const std::string& value);
  void add_device_filters(std::string&& value);
  void add_device_filters(const char* value);
  void add_device_filters(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& device_filters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_device_filters();
  private:
  const std::string& _internal_device_filters(int index) const;
  std::string* _internal_add_device_filters();
  public:

  // repeated .tensorflow.ThreadPoolOptionProto session_inter_op_thread_pool = 12;
  int session_inter_op_thread_pool_size() const;
  private:
  int _internal_session_inter_op_thread_pool_size() const;
  public:
  void clear_session_inter_op_thread_pool();
  ::tensorflow::ThreadPoolOptionProto* mutable_session_inter_op_thread_pool(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto >*
      mutable_session_inter_op_thread_pool();
  private:
  const ::tensorflow::ThreadPoolOptionProto& _internal_session_inter_op_thread_pool(int index) const;
  ::tensorflow::ThreadPoolOptionProto* _internal_add_session_inter_op_thread_pool();
  public:
  const ::tensorflow::ThreadPoolOptionProto& session_inter_op_thread_pool(int index) const;
  ::tensorflow::ThreadPoolOptionProto* add_session_inter_op_thread_pool();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto >&
      session_inter_op_thread_pool() const;

  // .tensorflow.GPUOptions gpu_options = 6;
  bool has_gpu_options() const;
  private:
  bool _internal_has_gpu_options() const;
  public:
  void clear_gpu_options();
  const ::tensorflow::GPUOptions& gpu_options() const;
  PROTOBUF_NODISCARD ::tensorflow::GPUOptions* release_gpu_options();
  ::tensorflow::GPUOptions* mutable_gpu_options();
  void set_allocated_gpu_options(::tensorflow::GPUOptions* gpu_options);
  private:
  const ::tensorflow::GPUOptions& _internal_gpu_options() const;
  ::tensorflow::GPUOptions* _internal_mutable_gpu_options();
  public:
  void unsafe_arena_set_allocated_gpu_options(
      ::tensorflow::GPUOptions* gpu_options);
  ::tensorflow::GPUOptions* unsafe_arena_release_gpu_options();

  // .tensorflow.GraphOptions graph_options = 10;
  bool has_graph_options() const;
  private:
  bool _internal_has_graph_options() const;
  public:
  void clear_graph_options();
  const ::tensorflow::GraphOptions& graph_options() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphOptions* release_graph_options();
  ::tensorflow::GraphOptions* mutable_graph_options();
  void set_allocated_graph_options(::tensorflow::GraphOptions* graph_options);
  private:
  const ::tensorflow::GraphOptions& _internal_graph_options() const;
  ::tensorflow::GraphOptions* _internal_mutable_graph_options();
  public:
  void unsafe_arena_set_allocated_graph_options(
      ::tensorflow::GraphOptions* graph_options);
  ::tensorflow::GraphOptions* unsafe_arena_release_graph_options();

  // .tensorflow.RPCOptions rpc_options = 13;
  bool has_rpc_options() const;
  private:
  bool _internal_has_rpc_options() const;
  public:
  void clear_rpc_options();
  const ::tensorflow::RPCOptions& rpc_options() const;
  PROTOBUF_NODISCARD ::tensorflow::RPCOptions* release_rpc_options();
  ::tensorflow::RPCOptions* mutable_rpc_options();
  void set_allocated_rpc_options(::tensorflow::RPCOptions* rpc_options);
  private:
  const ::tensorflow::RPCOptions& _internal_rpc_options() const;
  ::tensorflow::RPCOptions* _internal_mutable_rpc_options();
  public:
  void unsafe_arena_set_allocated_rpc_options(
      ::tensorflow::RPCOptions* rpc_options);
  ::tensorflow::RPCOptions* unsafe_arena_release_rpc_options();

  // .tensorflow.ClusterDef cluster_def = 14;
  bool has_cluster_def() const;
  private:
  bool _internal_has_cluster_def() const;
  public:
  void clear_cluster_def();
  const ::tensorflow::ClusterDef& cluster_def() const;
  PROTOBUF_NODISCARD ::tensorflow::ClusterDef* release_cluster_def();
  ::tensorflow::ClusterDef* mutable_cluster_def();
  void set_allocated_cluster_def(::tensorflow::ClusterDef* cluster_def);
  private:
  const ::tensorflow::ClusterDef& _internal_cluster_def() const;
  ::tensorflow::ClusterDef* _internal_mutable_cluster_def();
  public:
  void unsafe_arena_set_allocated_cluster_def(
      ::tensorflow::ClusterDef* cluster_def);
  ::tensorflow::ClusterDef* unsafe_arena_release_cluster_def();

  // .tensorflow.ConfigProto.Experimental experimental = 16;
  bool has_experimental() const;
  private:
  bool _internal_has_experimental() const;
  public:
  void clear_experimental();
  const ::tensorflow::ConfigProto_Experimental& experimental() const;
  PROTOBUF_NODISCARD ::tensorflow::ConfigProto_Experimental* release_experimental();
  ::tensorflow::ConfigProto_Experimental* mutable_experimental();
  void set_allocated_experimental(::tensorflow::ConfigProto_Experimental* experimental);
  private:
  const ::tensorflow::ConfigProto_Experimental& _internal_experimental() const;
  ::tensorflow::ConfigProto_Experimental* _internal_mutable_experimental();
  public:
  void unsafe_arena_set_allocated_experimental(
      ::tensorflow::ConfigProto_Experimental* experimental);
  ::tensorflow::ConfigProto_Experimental* unsafe_arena_release_experimental();

  // .tensorflow.GPUOptions pluggable_device_options = 18;
  bool has_pluggable_device_options() const;
  private:
  bool _internal_has_pluggable_device_options() const;
  public:
  void clear_pluggable_device_options();
  const ::tensorflow::GPUOptions& pluggable_device_options() const;
  PROTOBUF_NODISCARD ::tensorflow::GPUOptions* release_pluggable_device_options();
  ::tensorflow::GPUOptions* mutable_pluggable_device_options();
  void set_allocated_pluggable_device_options(::tensorflow::GPUOptions* pluggable_device_options);
  private:
  const ::tensorflow::GPUOptions& _internal_pluggable_device_options() const;
  ::tensorflow::GPUOptions* _internal_mutable_pluggable_device_options();
  public:
  void unsafe_arena_set_allocated_pluggable_device_options(
      ::tensorflow::GPUOptions* pluggable_device_options);
  ::tensorflow::GPUOptions* unsafe_arena_release_pluggable_device_options();

  // int32 intra_op_parallelism_threads = 2;
  void clear_intra_op_parallelism_threads();
  int32_t intra_op_parallelism_threads() const;
  void set_intra_op_parallelism_threads(int32_t value);
  private:
  int32_t _internal_intra_op_parallelism_threads() const;
  void _internal_set_intra_op_parallelism_threads(int32_t value);
  public:

  // int32 placement_period = 3;
  void clear_placement_period();
  int32_t placement_period() const;
  void set_placement_period(int32_t value);
  private:
  int32_t _internal_placement_period() const;
  void _internal_set_placement_period(int32_t value);
  public:

  // int32 inter_op_parallelism_threads = 5;
  void clear_inter_op_parallelism_threads();
  int32_t inter_op_parallelism_threads() const;
  void set_inter_op_parallelism_threads(int32_t value);
  private:
  int32_t _internal_inter_op_parallelism_threads() const;
  void _internal_set_inter_op_parallelism_threads(int32_t value);
  public:

  // bool use_per_session_threads = 9;
  void clear_use_per_session_threads();
  bool use_per_session_threads() const;
  void set_use_per_session_threads(bool value);
  private:
  bool _internal_use_per_session_threads() const;
  void _internal_set_use_per_session_threads(bool value);
  public:

  // bool allow_soft_placement = 7;
  void clear_allow_soft_placement();
  bool allow_soft_placement() const;
  void set_allow_soft_placement(bool value);
  private:
  bool _internal_allow_soft_placement() const;
  void _internal_set_allow_soft_placement(bool value);
  public:

  // bool log_device_placement = 8;
  void clear_log_device_placement();
  bool log_device_placement() const;
  void set_log_device_placement(bool value);
  private:
  bool _internal_log_device_placement() const;
  void _internal_set_log_device_placement(bool value);
  public:

  // bool isolate_session_state = 15;
  void clear_isolate_session_state();
  bool isolate_session_state() const;
  void set_isolate_session_state(bool value);
  private:
  bool _internal_isolate_session_state() const;
  void _internal_set_isolate_session_state(bool value);
  public:

  // int64 operation_timeout_in_ms = 11;
  void clear_operation_timeout_in_ms();
  int64_t operation_timeout_in_ms() const;
  void set_operation_timeout_in_ms(int64_t value);
  private:
  int64_t _internal_operation_timeout_in_ms() const;
  void _internal_set_operation_timeout_in_ms(int64_t value);
  public:

  // bool share_cluster_devices_in_session = 17;
  void clear_share_cluster_devices_in_session();
  bool share_cluster_devices_in_session() const;
  void set_share_cluster_devices_in_session(bool value);
  private:
  bool _internal_share_cluster_devices_in_session() const;
  void _internal_set_share_cluster_devices_in_session(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ConfigProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ConfigProto_DeviceCountEntry_DoNotUse,
        std::string, int32_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> device_count_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> device_filters_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto > session_inter_op_thread_pool_;
    ::tensorflow::GPUOptions* gpu_options_;
    ::tensorflow::GraphOptions* graph_options_;
    ::tensorflow::RPCOptions* rpc_options_;
    ::tensorflow::ClusterDef* cluster_def_;
    ::tensorflow::ConfigProto_Experimental* experimental_;
    ::tensorflow::GPUOptions* pluggable_device_options_;
    int32_t intra_op_parallelism_threads_;
    int32_t placement_period_;
    int32_t inter_op_parallelism_threads_;
    bool use_per_session_threads_;
    bool allow_soft_placement_;
    bool log_device_placement_;
    bool isolate_session_state_;
    int64_t operation_timeout_in_ms_;
    bool share_cluster_devices_in_session_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunOptions_Experimental_RunHandlerPoolOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions) */ {
 public:
  inline RunOptions_Experimental_RunHandlerPoolOptions() : RunOptions_Experimental_RunHandlerPoolOptions(nullptr) {}
  ~RunOptions_Experimental_RunHandlerPoolOptions() override;
  explicit PROTOBUF_CONSTEXPR RunOptions_Experimental_RunHandlerPoolOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunOptions_Experimental_RunHandlerPoolOptions(const RunOptions_Experimental_RunHandlerPoolOptions& from);
  RunOptions_Experimental_RunHandlerPoolOptions(RunOptions_Experimental_RunHandlerPoolOptions&& from) noexcept
    : RunOptions_Experimental_RunHandlerPoolOptions() {
    *this = ::std::move(from);
  }

  inline RunOptions_Experimental_RunHandlerPoolOptions& operator=(const RunOptions_Experimental_RunHandlerPoolOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunOptions_Experimental_RunHandlerPoolOptions& operator=(RunOptions_Experimental_RunHandlerPoolOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunOptions_Experimental_RunHandlerPoolOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunOptions_Experimental_RunHandlerPoolOptions* internal_default_instance() {
    return reinterpret_cast<const RunOptions_Experimental_RunHandlerPoolOptions*>(
               &_RunOptions_Experimental_RunHandlerPoolOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(RunOptions_Experimental_RunHandlerPoolOptions& a, RunOptions_Experimental_RunHandlerPoolOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(RunOptions_Experimental_RunHandlerPoolOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunOptions_Experimental_RunHandlerPoolOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunOptions_Experimental_RunHandlerPoolOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunOptions_Experimental_RunHandlerPoolOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunOptions_Experimental_RunHandlerPoolOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunOptions_Experimental_RunHandlerPoolOptions& from) {
    RunOptions_Experimental_RunHandlerPoolOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunOptions_Experimental_RunHandlerPoolOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunOptions.Experimental.RunHandlerPoolOptions";
  }
  protected:
  explicit RunOptions_Experimental_RunHandlerPoolOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPriorityFieldNumber = 1,
  };
  // int64 priority = 1;
  void clear_priority();
  int64_t priority() const;
  void set_priority(int64_t value);
  private:
  int64_t _internal_priority() const;
  void _internal_set_priority(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t priority_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunOptions_Experimental final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunOptions.Experimental) */ {
 public:
  inline RunOptions_Experimental() : RunOptions_Experimental(nullptr) {}
  ~RunOptions_Experimental() override;
  explicit PROTOBUF_CONSTEXPR RunOptions_Experimental(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunOptions_Experimental(const RunOptions_Experimental& from);
  RunOptions_Experimental(RunOptions_Experimental&& from) noexcept
    : RunOptions_Experimental() {
    *this = ::std::move(from);
  }

  inline RunOptions_Experimental& operator=(const RunOptions_Experimental& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunOptions_Experimental& operator=(RunOptions_Experimental&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunOptions_Experimental& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunOptions_Experimental* internal_default_instance() {
    return reinterpret_cast<const RunOptions_Experimental*>(
               &_RunOptions_Experimental_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(RunOptions_Experimental& a, RunOptions_Experimental& b) {
    a.Swap(&b);
  }
  inline void Swap(RunOptions_Experimental* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunOptions_Experimental* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunOptions_Experimental* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunOptions_Experimental>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunOptions_Experimental& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunOptions_Experimental& from) {
    RunOptions_Experimental::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunOptions_Experimental* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunOptions.Experimental";
  }
  protected:
  explicit RunOptions_Experimental(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef RunOptions_Experimental_RunHandlerPoolOptions RunHandlerPoolOptions;

  // accessors -------------------------------------------------------

  enum : int {
    kRunHandlerPoolOptionsFieldNumber = 3,
    kCollectiveGraphKeyFieldNumber = 1,
    kUseRunHandlerPoolFieldNumber = 2,
  };
  // .tensorflow.RunOptions.Experimental.RunHandlerPoolOptions run_handler_pool_options = 3;
  bool has_run_handler_pool_options() const;
  private:
  bool _internal_has_run_handler_pool_options() const;
  public:
  void clear_run_handler_pool_options();
  const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions& run_handler_pool_options() const;
  PROTOBUF_NODISCARD ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* release_run_handler_pool_options();
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* mutable_run_handler_pool_options();
  void set_allocated_run_handler_pool_options(::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options);
  private:
  const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions& _internal_run_handler_pool_options() const;
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* _internal_mutable_run_handler_pool_options();
  public:
  void unsafe_arena_set_allocated_run_handler_pool_options(
      ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options);
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* unsafe_arena_release_run_handler_pool_options();

  // int64 collective_graph_key = 1;
  void clear_collective_graph_key();
  int64_t collective_graph_key() const;
  void set_collective_graph_key(int64_t value);
  private:
  int64_t _internal_collective_graph_key() const;
  void _internal_set_collective_graph_key(int64_t value);
  public:

  // bool use_run_handler_pool = 2;
  void clear_use_run_handler_pool();
  bool use_run_handler_pool() const;
  void set_use_run_handler_pool(bool value);
  private:
  bool _internal_use_run_handler_pool() const;
  void _internal_set_use_run_handler_pool(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RunOptions.Experimental)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options_;
    int64_t collective_graph_key_;
    bool use_run_handler_pool_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunOptions) */ {
 public:
  inline RunOptions() : RunOptions(nullptr) {}
  ~RunOptions() override;
  explicit PROTOBUF_CONSTEXPR RunOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunOptions(const RunOptions& from);
  RunOptions(RunOptions&& from) noexcept
    : RunOptions() {
    *this = ::std::move(from);
  }

  inline RunOptions& operator=(const RunOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunOptions& operator=(RunOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunOptions* internal_default_instance() {
    return reinterpret_cast<const RunOptions*>(
               &_RunOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RunOptions& a, RunOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(RunOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunOptions& from) {
    RunOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunOptions";
  }
  protected:
  explicit RunOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef RunOptions_Experimental Experimental;

  typedef RunOptions_TraceLevel TraceLevel;
  static constexpr TraceLevel NO_TRACE =
    RunOptions_TraceLevel_NO_TRACE;
  static constexpr TraceLevel SOFTWARE_TRACE =
    RunOptions_TraceLevel_SOFTWARE_TRACE;
  static constexpr TraceLevel HARDWARE_TRACE =
    RunOptions_TraceLevel_HARDWARE_TRACE;
  static constexpr TraceLevel FULL_TRACE =
    RunOptions_TraceLevel_FULL_TRACE;
  static inline bool TraceLevel_IsValid(int value) {
    return RunOptions_TraceLevel_IsValid(value);
  }
  static constexpr TraceLevel TraceLevel_MIN =
    RunOptions_TraceLevel_TraceLevel_MIN;
  static constexpr TraceLevel TraceLevel_MAX =
    RunOptions_TraceLevel_TraceLevel_MAX;
  static constexpr int TraceLevel_ARRAYSIZE =
    RunOptions_TraceLevel_TraceLevel_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  TraceLevel_descriptor() {
    return RunOptions_TraceLevel_descriptor();
  }
  template<typename T>
  static inline const std::string& TraceLevel_Name(T enum_t_value) {
    static_assert(::std::is_same<T, TraceLevel>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function TraceLevel_Name.");
    return RunOptions_TraceLevel_Name(enum_t_value);
  }
  static inline bool TraceLevel_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      TraceLevel* value) {
    return RunOptions_TraceLevel_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kDebugOptionsFieldNumber = 6,
    kExperimentalFieldNumber = 8,
    kTimeoutInMsFieldNumber = 2,
    kTraceLevelFieldNumber = 1,
    kInterOpThreadPoolFieldNumber = 3,
    kOutputPartitionGraphsFieldNumber = 5,
    kReportTensorAllocationsUponOomFieldNumber = 7,
  };
  // .tensorflow.DebugOptions debug_options = 6;
  bool has_debug_options() const;
  private:
  bool _internal_has_debug_options() const;
  public:
  void clear_debug_options();
  const ::tensorflow::DebugOptions& debug_options() const;
  PROTOBUF_NODISCARD ::tensorflow::DebugOptions* release_debug_options();
  ::tensorflow::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::tensorflow::DebugOptions* debug_options);
  private:
  const ::tensorflow::DebugOptions& _internal_debug_options() const;
  ::tensorflow::DebugOptions* _internal_mutable_debug_options();
  public:
  void unsafe_arena_set_allocated_debug_options(
      ::tensorflow::DebugOptions* debug_options);
  ::tensorflow::DebugOptions* unsafe_arena_release_debug_options();

  // .tensorflow.RunOptions.Experimental experimental = 8;
  bool has_experimental() const;
  private:
  bool _internal_has_experimental() const;
  public:
  void clear_experimental();
  const ::tensorflow::RunOptions_Experimental& experimental() const;
  PROTOBUF_NODISCARD ::tensorflow::RunOptions_Experimental* release_experimental();
  ::tensorflow::RunOptions_Experimental* mutable_experimental();
  void set_allocated_experimental(::tensorflow::RunOptions_Experimental* experimental);
  private:
  const ::tensorflow::RunOptions_Experimental& _internal_experimental() const;
  ::tensorflow::RunOptions_Experimental* _internal_mutable_experimental();
  public:
  void unsafe_arena_set_allocated_experimental(
      ::tensorflow::RunOptions_Experimental* experimental);
  ::tensorflow::RunOptions_Experimental* unsafe_arena_release_experimental();

  // int64 timeout_in_ms = 2;
  void clear_timeout_in_ms();
  int64_t timeout_in_ms() const;
  void set_timeout_in_ms(int64_t value);
  private:
  int64_t _internal_timeout_in_ms() const;
  void _internal_set_timeout_in_ms(int64_t value);
  public:

  // .tensorflow.RunOptions.TraceLevel trace_level = 1;
  void clear_trace_level();
  ::tensorflow::RunOptions_TraceLevel trace_level() const;
  void set_trace_level(::tensorflow::RunOptions_TraceLevel value);
  private:
  ::tensorflow::RunOptions_TraceLevel _internal_trace_level() const;
  void _internal_set_trace_level(::tensorflow::RunOptions_TraceLevel value);
  public:

  // int32 inter_op_thread_pool = 3;
  void clear_inter_op_thread_pool();
  int32_t inter_op_thread_pool() const;
  void set_inter_op_thread_pool(int32_t value);
  private:
  int32_t _internal_inter_op_thread_pool() const;
  void _internal_set_inter_op_thread_pool(int32_t value);
  public:

  // bool output_partition_graphs = 5;
  void clear_output_partition_graphs();
  bool output_partition_graphs() const;
  void set_output_partition_graphs(bool value);
  private:
  bool _internal_output_partition_graphs() const;
  void _internal_set_output_partition_graphs(bool value);
  public:

  // bool report_tensor_allocations_upon_oom = 7;
  void clear_report_tensor_allocations_upon_oom();
  bool report_tensor_allocations_upon_oom() const;
  void set_report_tensor_allocations_upon_oom(bool value);
  private:
  bool _internal_report_tensor_allocations_upon_oom() const;
  void _internal_set_report_tensor_allocations_upon_oom(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RunOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::DebugOptions* debug_options_;
    ::tensorflow::RunOptions_Experimental* experimental_;
    int64_t timeout_in_ms_;
    int trace_level_;
    int32_t inter_op_thread_pool_;
    bool output_partition_graphs_;
    bool report_tensor_allocations_upon_oom_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunMetadata_FunctionGraphs final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunMetadata.FunctionGraphs) */ {
 public:
  inline RunMetadata_FunctionGraphs() : RunMetadata_FunctionGraphs(nullptr) {}
  ~RunMetadata_FunctionGraphs() override;
  explicit PROTOBUF_CONSTEXPR RunMetadata_FunctionGraphs(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunMetadata_FunctionGraphs(const RunMetadata_FunctionGraphs& from);
  RunMetadata_FunctionGraphs(RunMetadata_FunctionGraphs&& from) noexcept
    : RunMetadata_FunctionGraphs() {
    *this = ::std::move(from);
  }

  inline RunMetadata_FunctionGraphs& operator=(const RunMetadata_FunctionGraphs& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunMetadata_FunctionGraphs& operator=(RunMetadata_FunctionGraphs&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunMetadata_FunctionGraphs& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunMetadata_FunctionGraphs* internal_default_instance() {
    return reinterpret_cast<const RunMetadata_FunctionGraphs*>(
               &_RunMetadata_FunctionGraphs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RunMetadata_FunctionGraphs& a, RunMetadata_FunctionGraphs& b) {
    a.Swap(&b);
  }
  inline void Swap(RunMetadata_FunctionGraphs* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunMetadata_FunctionGraphs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunMetadata_FunctionGraphs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunMetadata_FunctionGraphs>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunMetadata_FunctionGraphs& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunMetadata_FunctionGraphs& from) {
    RunMetadata_FunctionGraphs::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunMetadata_FunctionGraphs* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunMetadata.FunctionGraphs";
  }
  protected:
  explicit RunMetadata_FunctionGraphs(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPartitionGraphsFieldNumber = 1,
    kPreOptimizationGraphFieldNumber = 2,
    kPostOptimizationGraphFieldNumber = 3,
  };
  // repeated .tensorflow.GraphDef partition_graphs = 1;
  int partition_graphs_size() const;
  private:
  int _internal_partition_graphs_size() const;
  public:
  void clear_partition_graphs();
  ::tensorflow::GraphDef* mutable_partition_graphs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
      mutable_partition_graphs();
  private:
  const ::tensorflow::GraphDef& _internal_partition_graphs(int index) const;
  ::tensorflow::GraphDef* _internal_add_partition_graphs();
  public:
  const ::tensorflow::GraphDef& partition_graphs(int index) const;
  ::tensorflow::GraphDef* add_partition_graphs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
      partition_graphs() const;

  // .tensorflow.GraphDef pre_optimization_graph = 2;
  bool has_pre_optimization_graph() const;
  private:
  bool _internal_has_pre_optimization_graph() const;
  public:
  void clear_pre_optimization_graph();
  const ::tensorflow::GraphDef& pre_optimization_graph() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphDef* release_pre_optimization_graph();
  ::tensorflow::GraphDef* mutable_pre_optimization_graph();
  void set_allocated_pre_optimization_graph(::tensorflow::GraphDef* pre_optimization_graph);
  private:
  const ::tensorflow::GraphDef& _internal_pre_optimization_graph() const;
  ::tensorflow::GraphDef* _internal_mutable_pre_optimization_graph();
  public:
  void unsafe_arena_set_allocated_pre_optimization_graph(
      ::tensorflow::GraphDef* pre_optimization_graph);
  ::tensorflow::GraphDef* unsafe_arena_release_pre_optimization_graph();

  // .tensorflow.GraphDef post_optimization_graph = 3;
  bool has_post_optimization_graph() const;
  private:
  bool _internal_has_post_optimization_graph() const;
  public:
  void clear_post_optimization_graph();
  const ::tensorflow::GraphDef& post_optimization_graph() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphDef* release_post_optimization_graph();
  ::tensorflow::GraphDef* mutable_post_optimization_graph();
  void set_allocated_post_optimization_graph(::tensorflow::GraphDef* post_optimization_graph);
  private:
  const ::tensorflow::GraphDef& _internal_post_optimization_graph() const;
  ::tensorflow::GraphDef* _internal_mutable_post_optimization_graph();
  public:
  void unsafe_arena_set_allocated_post_optimization_graph(
      ::tensorflow::GraphDef* post_optimization_graph);
  ::tensorflow::GraphDef* unsafe_arena_release_post_optimization_graph();

  // @@protoc_insertion_point(class_scope:tensorflow.RunMetadata.FunctionGraphs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef > partition_graphs_;
    ::tensorflow::GraphDef* pre_optimization_graph_;
    ::tensorflow::GraphDef* post_optimization_graph_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class RunMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunMetadata) */ {
 public:
  inline RunMetadata() : RunMetadata(nullptr) {}
  ~RunMetadata() override;
  explicit PROTOBUF_CONSTEXPR RunMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunMetadata(const RunMetadata& from);
  RunMetadata(RunMetadata&& from) noexcept
    : RunMetadata() {
    *this = ::std::move(from);
  }

  inline RunMetadata& operator=(const RunMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunMetadata& operator=(RunMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunMetadata* internal_default_instance() {
    return reinterpret_cast<const RunMetadata*>(
               &_RunMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(RunMetadata& a, RunMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(RunMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunMetadata& from) {
    RunMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunMetadata";
  }
  protected:
  explicit RunMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef RunMetadata_FunctionGraphs FunctionGraphs;

  // accessors -------------------------------------------------------

  enum : int {
    kPartitionGraphsFieldNumber = 3,
    kFunctionGraphsFieldNumber = 4,
    kStepStatsFieldNumber = 1,
    kCostGraphFieldNumber = 2,
    kSessionMetadataFieldNumber = 5,
  };
  // repeated .tensorflow.GraphDef partition_graphs = 3;
  int partition_graphs_size() const;
  private:
  int _internal_partition_graphs_size() const;
  public:
  void clear_partition_graphs();
  ::tensorflow::GraphDef* mutable_partition_graphs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
      mutable_partition_graphs();
  private:
  const ::tensorflow::GraphDef& _internal_partition_graphs(int index) const;
  ::tensorflow::GraphDef* _internal_add_partition_graphs();
  public:
  const ::tensorflow::GraphDef& partition_graphs(int index) const;
  ::tensorflow::GraphDef* add_partition_graphs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
      partition_graphs() const;

  // repeated .tensorflow.RunMetadata.FunctionGraphs function_graphs = 4;
  int function_graphs_size() const;
  private:
  int _internal_function_graphs_size() const;
  public:
  void clear_function_graphs();
  ::tensorflow::RunMetadata_FunctionGraphs* mutable_function_graphs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs >*
      mutable_function_graphs();
  private:
  const ::tensorflow::RunMetadata_FunctionGraphs& _internal_function_graphs(int index) const;
  ::tensorflow::RunMetadata_FunctionGraphs* _internal_add_function_graphs();
  public:
  const ::tensorflow::RunMetadata_FunctionGraphs& function_graphs(int index) const;
  ::tensorflow::RunMetadata_FunctionGraphs* add_function_graphs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs >&
      function_graphs() const;

  // .tensorflow.StepStats step_stats = 1;
  bool has_step_stats() const;
  private:
  bool _internal_has_step_stats() const;
  public:
  void clear_step_stats();
  const ::tensorflow::StepStats& step_stats() const;
  PROTOBUF_NODISCARD ::tensorflow::StepStats* release_step_stats();
  ::tensorflow::StepStats* mutable_step_stats();
  void set_allocated_step_stats(::tensorflow::StepStats* step_stats);
  private:
  const ::tensorflow::StepStats& _internal_step_stats() const;
  ::tensorflow::StepStats* _internal_mutable_step_stats();
  public:
  void unsafe_arena_set_allocated_step_stats(
      ::tensorflow::StepStats* step_stats);
  ::tensorflow::StepStats* unsafe_arena_release_step_stats();

  // .tensorflow.CostGraphDef cost_graph = 2;
  bool has_cost_graph() const;
  private:
  bool _internal_has_cost_graph() const;
  public:
  void clear_cost_graph();
  const ::tensorflow::CostGraphDef& cost_graph() const;
  PROTOBUF_NODISCARD ::tensorflow::CostGraphDef* release_cost_graph();
  ::tensorflow::CostGraphDef* mutable_cost_graph();
  void set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph);
  private:
  const ::tensorflow::CostGraphDef& _internal_cost_graph() const;
  ::tensorflow::CostGraphDef* _internal_mutable_cost_graph();
  public:
  void unsafe_arena_set_allocated_cost_graph(
      ::tensorflow::CostGraphDef* cost_graph);
  ::tensorflow::CostGraphDef* unsafe_arena_release_cost_graph();

  // .tensorflow.SessionMetadata session_metadata = 5;
  bool has_session_metadata() const;
  private:
  bool _internal_has_session_metadata() const;
  public:
  void clear_session_metadata();
  const ::tensorflow::SessionMetadata& session_metadata() const;
  PROTOBUF_NODISCARD ::tensorflow::SessionMetadata* release_session_metadata();
  ::tensorflow::SessionMetadata* mutable_session_metadata();
  void set_allocated_session_metadata(::tensorflow::SessionMetadata* session_metadata);
  private:
  const ::tensorflow::SessionMetadata& _internal_session_metadata() const;
  ::tensorflow::SessionMetadata* _internal_mutable_session_metadata();
  public:
  void unsafe_arena_set_allocated_session_metadata(
      ::tensorflow::SessionMetadata* session_metadata);
  ::tensorflow::SessionMetadata* unsafe_arena_release_session_metadata();

  // @@protoc_insertion_point(class_scope:tensorflow.RunMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef > partition_graphs_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs > function_graphs_;
    ::tensorflow::StepStats* step_stats_;
    ::tensorflow::CostGraphDef* cost_graph_;
    ::tensorflow::SessionMetadata* session_metadata_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class TensorConnection final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorConnection) */ {
 public:
  inline TensorConnection() : TensorConnection(nullptr) {}
  ~TensorConnection() override;
  explicit PROTOBUF_CONSTEXPR TensorConnection(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorConnection(const TensorConnection& from);
  TensorConnection(TensorConnection&& from) noexcept
    : TensorConnection() {
    *this = ::std::move(from);
  }

  inline TensorConnection& operator=(const TensorConnection& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorConnection& operator=(TensorConnection&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorConnection& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorConnection* internal_default_instance() {
    return reinterpret_cast<const TensorConnection*>(
               &_TensorConnection_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(TensorConnection& a, TensorConnection& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorConnection* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorConnection* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorConnection* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorConnection>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorConnection& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorConnection& from) {
    TensorConnection::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorConnection* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorConnection";
  }
  protected:
  explicit TensorConnection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFromTensorFieldNumber = 1,
    kToTensorFieldNumber = 2,
  };
  // string from_tensor = 1;
  void clear_from_tensor();
  const std::string& from_tensor() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_from_tensor(ArgT0&& arg0, ArgT... args);
  std::string* mutable_from_tensor();
  PROTOBUF_NODISCARD std::string* release_from_tensor();
  void set_allocated_from_tensor(std::string* from_tensor);
  private:
  const std::string& _internal_from_tensor() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_from_tensor(const std::string& value);
  std::string* _internal_mutable_from_tensor();
  public:

  // string to_tensor = 2;
  void clear_to_tensor();
  const std::string& to_tensor() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_to_tensor(ArgT0&& arg0, ArgT... args);
  std::string* mutable_to_tensor();
  PROTOBUF_NODISCARD std::string* release_to_tensor();
  void set_allocated_to_tensor(std::string* to_tensor);
  private:
  const std::string& _internal_to_tensor() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_to_tensor(const std::string& value);
  std::string* _internal_mutable_to_tensor();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TensorConnection)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr from_tensor_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr to_tensor_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class CallableOptions_FeedDevicesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallableOptions_FeedDevicesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallableOptions_FeedDevicesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  CallableOptions_FeedDevicesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR CallableOptions_FeedDevicesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit CallableOptions_FeedDevicesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CallableOptions_FeedDevicesEntry_DoNotUse& other);
  static const CallableOptions_FeedDevicesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CallableOptions_FeedDevicesEntry_DoNotUse*>(&_CallableOptions_FeedDevicesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallableOptions.FeedDevicesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallableOptions.FeedDevicesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};

// -------------------------------------------------------------------

class CallableOptions_FetchDevicesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallableOptions_FetchDevicesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CallableOptions_FetchDevicesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  CallableOptions_FetchDevicesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR CallableOptions_FetchDevicesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit CallableOptions_FetchDevicesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CallableOptions_FetchDevicesEntry_DoNotUse& other);
  static const CallableOptions_FetchDevicesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CallableOptions_FetchDevicesEntry_DoNotUse*>(&_CallableOptions_FetchDevicesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallableOptions.FetchDevicesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CallableOptions.FetchDevicesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};

// -------------------------------------------------------------------

class CallableOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CallableOptions) */ {
 public:
  inline CallableOptions() : CallableOptions(nullptr) {}
  ~CallableOptions() override;
  explicit PROTOBUF_CONSTEXPR CallableOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CallableOptions(const CallableOptions& from);
  CallableOptions(CallableOptions&& from) noexcept
    : CallableOptions() {
    *this = ::std::move(from);
  }

  inline CallableOptions& operator=(const CallableOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline CallableOptions& operator=(CallableOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CallableOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const CallableOptions* internal_default_instance() {
    return reinterpret_cast<const CallableOptions*>(
               &_CallableOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(CallableOptions& a, CallableOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(CallableOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CallableOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CallableOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CallableOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CallableOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CallableOptions& from) {
    CallableOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CallableOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CallableOptions";
  }
  protected:
  explicit CallableOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFeedFieldNumber = 1,
    kFetchFieldNumber = 2,
    kTargetFieldNumber = 3,
    kTensorConnectionFieldNumber = 5,
    kFeedDevicesFieldNumber = 6,
    kFetchDevicesFieldNumber = 7,
    kRunOptionsFieldNumber = 4,
    kFetchSkipSyncFieldNumber = 8,
  };
  // repeated string feed = 1;
  int feed_size() const;
  private:
  int _internal_feed_size() const;
  public:
  void clear_feed();
  const std::string& feed(int index) const;
  std::string* mutable_feed(int index);
  void set_feed(int index, const std::string& value);
  void set_feed(int index, std::string&& value);
  void set_feed(int index, const char* value);
  void set_feed(int index, const char* value, size_t size);
  std::string* add_feed();
  void add_feed(const std::string& value);
  void add_feed(std::string&& value);
  void add_feed(const char* value);
  void add_feed(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& feed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_feed();
  private:
  const std::string& _internal_feed(int index) const;
  std::string* _internal_add_feed();
  public:

  // repeated string fetch = 2;
  int fetch_size() const;
  private:
  int _internal_fetch_size() const;
  public:
  void clear_fetch();
  const std::string& fetch(int index) const;
  std::string* mutable_fetch(int index);
  void set_fetch(int index, const std::string& value);
  void set_fetch(int index, std::string&& value);
  void set_fetch(int index, const char* value);
  void set_fetch(int index, const char* value, size_t size);
  std::string* add_fetch();
  void add_fetch(const std::string& value);
  void add_fetch(std::string&& value);
  void add_fetch(const char* value);
  void add_fetch(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& fetch() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_fetch();
  private:
  const std::string& _internal_fetch(int index) const;
  std::string* _internal_add_fetch();
  public:

  // repeated string target = 3;
  int target_size() const;
  private:
  int _internal_target_size() const;
  public:
  void clear_target();
  const std::string& target(int index) const;
  std::string* mutable_target(int index);
  void set_target(int index, const std::string& value);
  void set_target(int index, std::string&& value);
  void set_target(int index, const char* value);
  void set_target(int index, const char* value, size_t size);
  std::string* add_target();
  void add_target(const std::string& value);
  void add_target(std::string&& value);
  void add_target(const char* value);
  void add_target(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& target() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_target();
  private:
  const std::string& _internal_target(int index) const;
  std::string* _internal_add_target();
  public:

  // repeated .tensorflow.TensorConnection tensor_connection = 5;
  int tensor_connection_size() const;
  private:
  int _internal_tensor_connection_size() const;
  public:
  void clear_tensor_connection();
  ::tensorflow::TensorConnection* mutable_tensor_connection(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection >*
      mutable_tensor_connection();
  private:
  const ::tensorflow::TensorConnection& _internal_tensor_connection(int index) const;
  ::tensorflow::TensorConnection* _internal_add_tensor_connection();
  public:
  const ::tensorflow::TensorConnection& tensor_connection(int index) const;
  ::tensorflow::TensorConnection* add_tensor_connection();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection >&
      tensor_connection() const;

  // map<string, string> feed_devices = 6;
  int feed_devices_size() const;
  private:
  int _internal_feed_devices_size() const;
  public:
  void clear_feed_devices();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_feed_devices() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_feed_devices();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      feed_devices() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_feed_devices();

  // map<string, string> fetch_devices = 7;
  int fetch_devices_size() const;
  private:
  int _internal_fetch_devices_size() const;
  public:
  void clear_fetch_devices();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_fetch_devices() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_fetch_devices();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      fetch_devices() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_fetch_devices();

  // .tensorflow.RunOptions run_options = 4;
  bool has_run_options() const;
  private:
  bool _internal_has_run_options() const;
  public:
  void clear_run_options();
  const ::tensorflow::RunOptions& run_options() const;
  PROTOBUF_NODISCARD ::tensorflow::RunOptions* release_run_options();
  ::tensorflow::RunOptions* mutable_run_options();
  void set_allocated_run_options(::tensorflow::RunOptions* run_options);
  private:
  const ::tensorflow::RunOptions& _internal_run_options() const;
  ::tensorflow::RunOptions* _internal_mutable_run_options();
  public:
  void unsafe_arena_set_allocated_run_options(
      ::tensorflow::RunOptions* run_options);
  ::tensorflow::RunOptions* unsafe_arena_release_run_options();

  // bool fetch_skip_sync = 8;
  void clear_fetch_skip_sync();
  bool fetch_skip_sync() const;
  void set_fetch_skip_sync(bool value);
  private:
  bool _internal_fetch_skip_sync() const;
  void _internal_set_fetch_skip_sync(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CallableOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> feed_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> fetch_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> target_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection > tensor_connection_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        CallableOptions_FeedDevicesEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> feed_devices_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        CallableOptions_FetchDevicesEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> fetch_devices_;
    ::tensorflow::RunOptions* run_options_;
    bool fetch_skip_sync_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// -------------------------------------------------------------------

class BatchingOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BatchingOptions) */ {
 public:
  inline BatchingOptions() : BatchingOptions(nullptr) {}
  ~BatchingOptions() override;
  explicit PROTOBUF_CONSTEXPR BatchingOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BatchingOptions(const BatchingOptions& from);
  BatchingOptions(BatchingOptions&& from) noexcept
    : BatchingOptions() {
    *this = ::std::move(from);
  }

  inline BatchingOptions& operator=(const BatchingOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline BatchingOptions& operator=(BatchingOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BatchingOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const BatchingOptions* internal_default_instance() {
    return reinterpret_cast<const BatchingOptions*>(
               &_BatchingOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(BatchingOptions& a, BatchingOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(BatchingOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BatchingOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BatchingOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BatchingOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BatchingOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BatchingOptions& from) {
    BatchingOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BatchingOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BatchingOptions";
  }
  protected:
  explicit BatchingOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllowedBatchSizesFieldNumber = 4,
    kNumBatchThreadsFieldNumber = 1,
    kMaxBatchSizeFieldNumber = 2,
    kBatchTimeoutMicrosFieldNumber = 3,
    kMaxEnqueuedBatchesFieldNumber = 5,
  };
  // repeated int32 allowed_batch_sizes = 4;
  int allowed_batch_sizes_size() const;
  private:
  int _internal_allowed_batch_sizes_size() const;
  public:
  void clear_allowed_batch_sizes();
  private:
  int32_t _internal_allowed_batch_sizes(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_allowed_batch_sizes() const;
  void _internal_add_allowed_batch_sizes(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_allowed_batch_sizes();
  public:
  int32_t allowed_batch_sizes(int index) const;
  void set_allowed_batch_sizes(int index, int32_t value);
  void add_allowed_batch_sizes(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      allowed_batch_sizes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_allowed_batch_sizes();

  // int32 num_batch_threads = 1;
  void clear_num_batch_threads();
  int32_t num_batch_threads() const;
  void set_num_batch_threads(int32_t value);
  private:
  int32_t _internal_num_batch_threads() const;
  void _internal_set_num_batch_threads(int32_t value);
  public:

  // int32 max_batch_size = 2;
  void clear_max_batch_size();
  int32_t max_batch_size() const;
  void set_max_batch_size(int32_t value);
  private:
  int32_t _internal_max_batch_size() const;
  void _internal_set_max_batch_size(int32_t value);
  public:

  // int32 batch_timeout_micros = 3;
  void clear_batch_timeout_micros();
  int32_t batch_timeout_micros() const;
  void set_batch_timeout_micros(int32_t value);
  private:
  int32_t _internal_batch_timeout_micros() const;
  void _internal_set_batch_timeout_micros(int32_t value);
  public:

  // int32 max_enqueued_batches = 5;
  void clear_max_enqueued_batches();
  int32_t max_enqueued_batches() const;
  void set_max_enqueued_batches(int32_t value);
  private:
  int32_t _internal_max_enqueued_batches() const;
  void _internal_set_max_enqueued_batches(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BatchingOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > allowed_batch_sizes_;
    mutable std::atomic<int> _allowed_batch_sizes_cached_byte_size_;
    int32_t num_batch_threads_;
    int32_t max_batch_size_;
    int32_t batch_timeout_micros_;
    int32_t max_enqueued_batches_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GPUOptions_Experimental_VirtualDevices

// repeated float memory_limit_mb = 1;
inline int GPUOptions_Experimental_VirtualDevices::_internal_memory_limit_mb_size() const {
  return _impl_.memory_limit_mb_.size();
}
inline int GPUOptions_Experimental_VirtualDevices::memory_limit_mb_size() const {
  return _internal_memory_limit_mb_size();
}
inline void GPUOptions_Experimental_VirtualDevices::clear_memory_limit_mb() {
  _impl_.memory_limit_mb_.Clear();
}
inline float GPUOptions_Experimental_VirtualDevices::_internal_memory_limit_mb(int index) const {
  return _impl_.memory_limit_mb_.Get(index);
}
inline float GPUOptions_Experimental_VirtualDevices::memory_limit_mb(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
  return _internal_memory_limit_mb(index);
}
inline void GPUOptions_Experimental_VirtualDevices::set_memory_limit_mb(int index, float value) {
  _impl_.memory_limit_mb_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
}
inline void GPUOptions_Experimental_VirtualDevices::_internal_add_memory_limit_mb(float value) {
  _impl_.memory_limit_mb_.Add(value);
}
inline void GPUOptions_Experimental_VirtualDevices::add_memory_limit_mb(float value) {
  _internal_add_memory_limit_mb(value);
  // @@protoc_insertion_point(field_add:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
GPUOptions_Experimental_VirtualDevices::_internal_memory_limit_mb() const {
  return _impl_.memory_limit_mb_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
GPUOptions_Experimental_VirtualDevices::memory_limit_mb() const {
  // @@protoc_insertion_point(field_list:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
  return _internal_memory_limit_mb();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
GPUOptions_Experimental_VirtualDevices::_internal_mutable_memory_limit_mb() {
  return &_impl_.memory_limit_mb_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
GPUOptions_Experimental_VirtualDevices::mutable_memory_limit_mb() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GPUOptions.Experimental.VirtualDevices.memory_limit_mb)
  return _internal_mutable_memory_limit_mb();
}

// repeated int32 priority = 2;
inline int GPUOptions_Experimental_VirtualDevices::_internal_priority_size() const {
  return _impl_.priority_.size();
}
inline int GPUOptions_Experimental_VirtualDevices::priority_size() const {
  return _internal_priority_size();
}
inline void GPUOptions_Experimental_VirtualDevices::clear_priority() {
  _impl_.priority_.Clear();
}
inline int32_t GPUOptions_Experimental_VirtualDevices::_internal_priority(int index) const {
  return _impl_.priority_.Get(index);
}
inline int32_t GPUOptions_Experimental_VirtualDevices::priority(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
  return _internal_priority(index);
}
inline void GPUOptions_Experimental_VirtualDevices::set_priority(int index, int32_t value) {
  _impl_.priority_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
}
inline void GPUOptions_Experimental_VirtualDevices::_internal_add_priority(int32_t value) {
  _impl_.priority_.Add(value);
}
inline void GPUOptions_Experimental_VirtualDevices::add_priority(int32_t value) {
  _internal_add_priority(value);
  // @@protoc_insertion_point(field_add:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GPUOptions_Experimental_VirtualDevices::_internal_priority() const {
  return _impl_.priority_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GPUOptions_Experimental_VirtualDevices::priority() const {
  // @@protoc_insertion_point(field_list:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
  return _internal_priority();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GPUOptions_Experimental_VirtualDevices::_internal_mutable_priority() {
  return &_impl_.priority_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GPUOptions_Experimental_VirtualDevices::mutable_priority() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GPUOptions.Experimental.VirtualDevices.priority)
  return _internal_mutable_priority();
}

// repeated int32 device_ordinal = 3;
inline int GPUOptions_Experimental_VirtualDevices::_internal_device_ordinal_size() const {
  return _impl_.device_ordinal_.size();
}
inline int GPUOptions_Experimental_VirtualDevices::device_ordinal_size() const {
  return _internal_device_ordinal_size();
}
inline void GPUOptions_Experimental_VirtualDevices::clear_device_ordinal() {
  _impl_.device_ordinal_.Clear();
}
inline int32_t GPUOptions_Experimental_VirtualDevices::_internal_device_ordinal(int index) const {
  return _impl_.device_ordinal_.Get(index);
}
inline int32_t GPUOptions_Experimental_VirtualDevices::device_ordinal(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.VirtualDevices.device_ordinal)
  return _internal_device_ordinal(index);
}
inline void GPUOptions_Experimental_VirtualDevices::set_device_ordinal(int index, int32_t value) {
  _impl_.device_ordinal_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.VirtualDevices.device_ordinal)
}
inline void GPUOptions_Experimental_VirtualDevices::_internal_add_device_ordinal(int32_t value) {
  _impl_.device_ordinal_.Add(value);
}
inline void GPUOptions_Experimental_VirtualDevices::add_device_ordinal(int32_t value) {
  _internal_add_device_ordinal(value);
  // @@protoc_insertion_point(field_add:tensorflow.GPUOptions.Experimental.VirtualDevices.device_ordinal)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GPUOptions_Experimental_VirtualDevices::_internal_device_ordinal() const {
  return _impl_.device_ordinal_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GPUOptions_Experimental_VirtualDevices::device_ordinal() const {
  // @@protoc_insertion_point(field_list:tensorflow.GPUOptions.Experimental.VirtualDevices.device_ordinal)
  return _internal_device_ordinal();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GPUOptions_Experimental_VirtualDevices::_internal_mutable_device_ordinal() {
  return &_impl_.device_ordinal_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GPUOptions_Experimental_VirtualDevices::mutable_device_ordinal() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GPUOptions.Experimental.VirtualDevices.device_ordinal)
  return _internal_mutable_device_ordinal();
}

// -------------------------------------------------------------------

// GPUOptions_Experimental_StreamMergeOptions

// bool merge_host_to_device_stream = 1;
inline void GPUOptions_Experimental_StreamMergeOptions::clear_merge_host_to_device_stream() {
  _impl_.merge_host_to_device_stream_ = false;
}
inline bool GPUOptions_Experimental_StreamMergeOptions::_internal_merge_host_to_device_stream() const {
  return _impl_.merge_host_to_device_stream_;
}
inline bool GPUOptions_Experimental_StreamMergeOptions::merge_host_to_device_stream() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.StreamMergeOptions.merge_host_to_device_stream)
  return _internal_merge_host_to_device_stream();
}
inline void GPUOptions_Experimental_StreamMergeOptions::_internal_set_merge_host_to_device_stream(bool value) {
  
  _impl_.merge_host_to_device_stream_ = value;
}
inline void GPUOptions_Experimental_StreamMergeOptions::set_merge_host_to_device_stream(bool value) {
  _internal_set_merge_host_to_device_stream(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.StreamMergeOptions.merge_host_to_device_stream)
}

// bool merge_device_to_host_stream = 2;
inline void GPUOptions_Experimental_StreamMergeOptions::clear_merge_device_to_host_stream() {
  _impl_.merge_device_to_host_stream_ = false;
}
inline bool GPUOptions_Experimental_StreamMergeOptions::_internal_merge_device_to_host_stream() const {
  return _impl_.merge_device_to_host_stream_;
}
inline bool GPUOptions_Experimental_StreamMergeOptions::merge_device_to_host_stream() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.StreamMergeOptions.merge_device_to_host_stream)
  return _internal_merge_device_to_host_stream();
}
inline void GPUOptions_Experimental_StreamMergeOptions::_internal_set_merge_device_to_host_stream(bool value) {
  
  _impl_.merge_device_to_host_stream_ = value;
}
inline void GPUOptions_Experimental_StreamMergeOptions::set_merge_device_to_host_stream(bool value) {
  _internal_set_merge_device_to_host_stream(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.StreamMergeOptions.merge_device_to_host_stream)
}

// bool merge_device_to_device_stream = 3;
inline void GPUOptions_Experimental_StreamMergeOptions::clear_merge_device_to_device_stream() {
  _impl_.merge_device_to_device_stream_ = false;
}
inline bool GPUOptions_Experimental_StreamMergeOptions::_internal_merge_device_to_device_stream() const {
  return _impl_.merge_device_to_device_stream_;
}
inline bool GPUOptions_Experimental_StreamMergeOptions::merge_device_to_device_stream() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.StreamMergeOptions.merge_device_to_device_stream)
  return _internal_merge_device_to_device_stream();
}
inline void GPUOptions_Experimental_StreamMergeOptions::_internal_set_merge_device_to_device_stream(bool value) {
  
  _impl_.merge_device_to_device_stream_ = value;
}
inline void GPUOptions_Experimental_StreamMergeOptions::set_merge_device_to_device_stream(bool value) {
  _internal_set_merge_device_to_device_stream(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.StreamMergeOptions.merge_device_to_device_stream)
}

// -------------------------------------------------------------------

// GPUOptions_Experimental

// repeated .tensorflow.GPUOptions.Experimental.VirtualDevices virtual_devices = 1;
inline int GPUOptions_Experimental::_internal_virtual_devices_size() const {
  return _impl_.virtual_devices_.size();
}
inline int GPUOptions_Experimental::virtual_devices_size() const {
  return _internal_virtual_devices_size();
}
inline void GPUOptions_Experimental::clear_virtual_devices() {
  _impl_.virtual_devices_.Clear();
}
inline ::tensorflow::GPUOptions_Experimental_VirtualDevices* GPUOptions_Experimental::mutable_virtual_devices(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.Experimental.virtual_devices)
  return _impl_.virtual_devices_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices >*
GPUOptions_Experimental::mutable_virtual_devices() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GPUOptions.Experimental.virtual_devices)
  return &_impl_.virtual_devices_;
}
inline const ::tensorflow::GPUOptions_Experimental_VirtualDevices& GPUOptions_Experimental::_internal_virtual_devices(int index) const {
  return _impl_.virtual_devices_.Get(index);
}
inline const ::tensorflow::GPUOptions_Experimental_VirtualDevices& GPUOptions_Experimental::virtual_devices(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.virtual_devices)
  return _internal_virtual_devices(index);
}
inline ::tensorflow::GPUOptions_Experimental_VirtualDevices* GPUOptions_Experimental::_internal_add_virtual_devices() {
  return _impl_.virtual_devices_.Add();
}
inline ::tensorflow::GPUOptions_Experimental_VirtualDevices* GPUOptions_Experimental::add_virtual_devices() {
  ::tensorflow::GPUOptions_Experimental_VirtualDevices* _add = _internal_add_virtual_devices();
  // @@protoc_insertion_point(field_add:tensorflow.GPUOptions.Experimental.virtual_devices)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GPUOptions_Experimental_VirtualDevices >&
GPUOptions_Experimental::virtual_devices() const {
  // @@protoc_insertion_point(field_list:tensorflow.GPUOptions.Experimental.virtual_devices)
  return _impl_.virtual_devices_;
}

// int32 num_virtual_devices_per_gpu = 15;
inline void GPUOptions_Experimental::clear_num_virtual_devices_per_gpu() {
  _impl_.num_virtual_devices_per_gpu_ = 0;
}
inline int32_t GPUOptions_Experimental::_internal_num_virtual_devices_per_gpu() const {
  return _impl_.num_virtual_devices_per_gpu_;
}
inline int32_t GPUOptions_Experimental::num_virtual_devices_per_gpu() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.num_virtual_devices_per_gpu)
  return _internal_num_virtual_devices_per_gpu();
}
inline void GPUOptions_Experimental::_internal_set_num_virtual_devices_per_gpu(int32_t value) {
  
  _impl_.num_virtual_devices_per_gpu_ = value;
}
inline void GPUOptions_Experimental::set_num_virtual_devices_per_gpu(int32_t value) {
  _internal_set_num_virtual_devices_per_gpu(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.num_virtual_devices_per_gpu)
}

// bool use_unified_memory = 2;
inline void GPUOptions_Experimental::clear_use_unified_memory() {
  _impl_.use_unified_memory_ = false;
}
inline bool GPUOptions_Experimental::_internal_use_unified_memory() const {
  return _impl_.use_unified_memory_;
}
inline bool GPUOptions_Experimental::use_unified_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.use_unified_memory)
  return _internal_use_unified_memory();
}
inline void GPUOptions_Experimental::_internal_set_use_unified_memory(bool value) {
  
  _impl_.use_unified_memory_ = value;
}
inline void GPUOptions_Experimental::set_use_unified_memory(bool value) {
  _internal_set_use_unified_memory(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.use_unified_memory)
}

// int32 num_dev_to_dev_copy_streams = 3;
inline void GPUOptions_Experimental::clear_num_dev_to_dev_copy_streams() {
  _impl_.num_dev_to_dev_copy_streams_ = 0;
}
inline int32_t GPUOptions_Experimental::_internal_num_dev_to_dev_copy_streams() const {
  return _impl_.num_dev_to_dev_copy_streams_;
}
inline int32_t GPUOptions_Experimental::num_dev_to_dev_copy_streams() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.num_dev_to_dev_copy_streams)
  return _internal_num_dev_to_dev_copy_streams();
}
inline void GPUOptions_Experimental::_internal_set_num_dev_to_dev_copy_streams(int32_t value) {
  
  _impl_.num_dev_to_dev_copy_streams_ = value;
}
inline void GPUOptions_Experimental::set_num_dev_to_dev_copy_streams(int32_t value) {
  _internal_set_num_dev_to_dev_copy_streams(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.num_dev_to_dev_copy_streams)
}

// string collective_ring_order = 4;
inline void GPUOptions_Experimental::clear_collective_ring_order() {
  _impl_.collective_ring_order_.ClearToEmpty();
}
inline const std::string& GPUOptions_Experimental::collective_ring_order() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.collective_ring_order)
  return _internal_collective_ring_order();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GPUOptions_Experimental::set_collective_ring_order(ArgT0&& arg0, ArgT... args) {
 
 _impl_.collective_ring_order_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.collective_ring_order)
}
inline std::string* GPUOptions_Experimental::mutable_collective_ring_order() {
  std::string* _s = _internal_mutable_collective_ring_order();
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.Experimental.collective_ring_order)
  return _s;
}
inline const std::string& GPUOptions_Experimental::_internal_collective_ring_order() const {
  return _impl_.collective_ring_order_.Get();
}
inline void GPUOptions_Experimental::_internal_set_collective_ring_order(const std::string& value) {
  
  _impl_.collective_ring_order_.Set(value, GetArenaForAllocation());
}
inline std::string* GPUOptions_Experimental::_internal_mutable_collective_ring_order() {
  
  return _impl_.collective_ring_order_.Mutable(GetArenaForAllocation());
}
inline std::string* GPUOptions_Experimental::release_collective_ring_order() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.Experimental.collective_ring_order)
  return _impl_.collective_ring_order_.Release();
}
inline void GPUOptions_Experimental::set_allocated_collective_ring_order(std::string* collective_ring_order) {
  if (collective_ring_order != nullptr) {
    
  } else {
    
  }
  _impl_.collective_ring_order_.SetAllocated(collective_ring_order, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.collective_ring_order_.IsDefault()) {
    _impl_.collective_ring_order_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.Experimental.collective_ring_order)
}

// bool timestamped_allocator = 5;
inline void GPUOptions_Experimental::clear_timestamped_allocator() {
  _impl_.timestamped_allocator_ = false;
}
inline bool GPUOptions_Experimental::_internal_timestamped_allocator() const {
  return _impl_.timestamped_allocator_;
}
inline bool GPUOptions_Experimental::timestamped_allocator() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.timestamped_allocator)
  return _internal_timestamped_allocator();
}
inline void GPUOptions_Experimental::_internal_set_timestamped_allocator(bool value) {
  
  _impl_.timestamped_allocator_ = value;
}
inline void GPUOptions_Experimental::set_timestamped_allocator(bool value) {
  _internal_set_timestamped_allocator(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.timestamped_allocator)
}

// int32 kernel_tracker_max_interval = 7;
inline void GPUOptions_Experimental::clear_kernel_tracker_max_interval() {
  _impl_.kernel_tracker_max_interval_ = 0;
}
inline int32_t GPUOptions_Experimental::_internal_kernel_tracker_max_interval() const {
  return _impl_.kernel_tracker_max_interval_;
}
inline int32_t GPUOptions_Experimental::kernel_tracker_max_interval() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.kernel_tracker_max_interval)
  return _internal_kernel_tracker_max_interval();
}
inline void GPUOptions_Experimental::_internal_set_kernel_tracker_max_interval(int32_t value) {
  
  _impl_.kernel_tracker_max_interval_ = value;
}
inline void GPUOptions_Experimental::set_kernel_tracker_max_interval(int32_t value) {
  _internal_set_kernel_tracker_max_interval(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.kernel_tracker_max_interval)
}

// int32 kernel_tracker_max_bytes = 8;
inline void GPUOptions_Experimental::clear_kernel_tracker_max_bytes() {
  _impl_.kernel_tracker_max_bytes_ = 0;
}
inline int32_t GPUOptions_Experimental::_internal_kernel_tracker_max_bytes() const {
  return _impl_.kernel_tracker_max_bytes_;
}
inline int32_t GPUOptions_Experimental::kernel_tracker_max_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.kernel_tracker_max_bytes)
  return _internal_kernel_tracker_max_bytes();
}
inline void GPUOptions_Experimental::_internal_set_kernel_tracker_max_bytes(int32_t value) {
  
  _impl_.kernel_tracker_max_bytes_ = value;
}
inline void GPUOptions_Experimental::set_kernel_tracker_max_bytes(int32_t value) {
  _internal_set_kernel_tracker_max_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.kernel_tracker_max_bytes)
}

// int32 kernel_tracker_max_pending = 9;
inline void GPUOptions_Experimental::clear_kernel_tracker_max_pending() {
  _impl_.kernel_tracker_max_pending_ = 0;
}
inline int32_t GPUOptions_Experimental::_internal_kernel_tracker_max_pending() const {
  return _impl_.kernel_tracker_max_pending_;
}
inline int32_t GPUOptions_Experimental::kernel_tracker_max_pending() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.kernel_tracker_max_pending)
  return _internal_kernel_tracker_max_pending();
}
inline void GPUOptions_Experimental::_internal_set_kernel_tracker_max_pending(int32_t value) {
  
  _impl_.kernel_tracker_max_pending_ = value;
}
inline void GPUOptions_Experimental::set_kernel_tracker_max_pending(int32_t value) {
  _internal_set_kernel_tracker_max_pending(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.kernel_tracker_max_pending)
}

// double internal_fragmentation_fraction = 10;
inline void GPUOptions_Experimental::clear_internal_fragmentation_fraction() {
  _impl_.internal_fragmentation_fraction_ = 0;
}
inline double GPUOptions_Experimental::_internal_internal_fragmentation_fraction() const {
  return _impl_.internal_fragmentation_fraction_;
}
inline double GPUOptions_Experimental::internal_fragmentation_fraction() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.internal_fragmentation_fraction)
  return _internal_internal_fragmentation_fraction();
}
inline void GPUOptions_Experimental::_internal_set_internal_fragmentation_fraction(double value) {
  
  _impl_.internal_fragmentation_fraction_ = value;
}
inline void GPUOptions_Experimental::set_internal_fragmentation_fraction(double value) {
  _internal_set_internal_fragmentation_fraction(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.internal_fragmentation_fraction)
}

// bool use_cuda_malloc_async = 11;
inline void GPUOptions_Experimental::clear_use_cuda_malloc_async() {
  _impl_.use_cuda_malloc_async_ = false;
}
inline bool GPUOptions_Experimental::_internal_use_cuda_malloc_async() const {
  return _impl_.use_cuda_malloc_async_;
}
inline bool GPUOptions_Experimental::use_cuda_malloc_async() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.use_cuda_malloc_async)
  return _internal_use_cuda_malloc_async();
}
inline void GPUOptions_Experimental::_internal_set_use_cuda_malloc_async(bool value) {
  
  _impl_.use_cuda_malloc_async_ = value;
}
inline void GPUOptions_Experimental::set_use_cuda_malloc_async(bool value) {
  _internal_set_use_cuda_malloc_async(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.use_cuda_malloc_async)
}

// bool disallow_retry_on_allocation_failure = 12;
inline void GPUOptions_Experimental::clear_disallow_retry_on_allocation_failure() {
  _impl_.disallow_retry_on_allocation_failure_ = false;
}
inline bool GPUOptions_Experimental::_internal_disallow_retry_on_allocation_failure() const {
  return _impl_.disallow_retry_on_allocation_failure_;
}
inline bool GPUOptions_Experimental::disallow_retry_on_allocation_failure() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.disallow_retry_on_allocation_failure)
  return _internal_disallow_retry_on_allocation_failure();
}
inline void GPUOptions_Experimental::_internal_set_disallow_retry_on_allocation_failure(bool value) {
  
  _impl_.disallow_retry_on_allocation_failure_ = value;
}
inline void GPUOptions_Experimental::set_disallow_retry_on_allocation_failure(bool value) {
  _internal_set_disallow_retry_on_allocation_failure(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.disallow_retry_on_allocation_failure)
}

// float gpu_host_mem_limit_in_mb = 13;
inline void GPUOptions_Experimental::clear_gpu_host_mem_limit_in_mb() {
  _impl_.gpu_host_mem_limit_in_mb_ = 0;
}
inline float GPUOptions_Experimental::_internal_gpu_host_mem_limit_in_mb() const {
  return _impl_.gpu_host_mem_limit_in_mb_;
}
inline float GPUOptions_Experimental::gpu_host_mem_limit_in_mb() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.gpu_host_mem_limit_in_mb)
  return _internal_gpu_host_mem_limit_in_mb();
}
inline void GPUOptions_Experimental::_internal_set_gpu_host_mem_limit_in_mb(float value) {
  
  _impl_.gpu_host_mem_limit_in_mb_ = value;
}
inline void GPUOptions_Experimental::set_gpu_host_mem_limit_in_mb(float value) {
  _internal_set_gpu_host_mem_limit_in_mb(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.gpu_host_mem_limit_in_mb)
}

// bool gpu_host_mem_disallow_growth = 14;
inline void GPUOptions_Experimental::clear_gpu_host_mem_disallow_growth() {
  _impl_.gpu_host_mem_disallow_growth_ = false;
}
inline bool GPUOptions_Experimental::_internal_gpu_host_mem_disallow_growth() const {
  return _impl_.gpu_host_mem_disallow_growth_;
}
inline bool GPUOptions_Experimental::gpu_host_mem_disallow_growth() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.gpu_host_mem_disallow_growth)
  return _internal_gpu_host_mem_disallow_growth();
}
inline void GPUOptions_Experimental::_internal_set_gpu_host_mem_disallow_growth(bool value) {
  
  _impl_.gpu_host_mem_disallow_growth_ = value;
}
inline void GPUOptions_Experimental::set_gpu_host_mem_disallow_growth(bool value) {
  _internal_set_gpu_host_mem_disallow_growth(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.gpu_host_mem_disallow_growth)
}

// int32 gpu_system_memory_size_in_mb = 16;
inline void GPUOptions_Experimental::clear_gpu_system_memory_size_in_mb() {
  _impl_.gpu_system_memory_size_in_mb_ = 0;
}
inline int32_t GPUOptions_Experimental::_internal_gpu_system_memory_size_in_mb() const {
  return _impl_.gpu_system_memory_size_in_mb_;
}
inline int32_t GPUOptions_Experimental::gpu_system_memory_size_in_mb() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.gpu_system_memory_size_in_mb)
  return _internal_gpu_system_memory_size_in_mb();
}
inline void GPUOptions_Experimental::_internal_set_gpu_system_memory_size_in_mb(int32_t value) {
  
  _impl_.gpu_system_memory_size_in_mb_ = value;
}
inline void GPUOptions_Experimental::set_gpu_system_memory_size_in_mb(int32_t value) {
  _internal_set_gpu_system_memory_size_in_mb(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.gpu_system_memory_size_in_mb)
}

// bool populate_pjrt_gpu_client_creation_info = 17;
inline void GPUOptions_Experimental::clear_populate_pjrt_gpu_client_creation_info() {
  _impl_.populate_pjrt_gpu_client_creation_info_ = false;
}
inline bool GPUOptions_Experimental::_internal_populate_pjrt_gpu_client_creation_info() const {
  return _impl_.populate_pjrt_gpu_client_creation_info_;
}
inline bool GPUOptions_Experimental::populate_pjrt_gpu_client_creation_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.populate_pjrt_gpu_client_creation_info)
  return _internal_populate_pjrt_gpu_client_creation_info();
}
inline void GPUOptions_Experimental::_internal_set_populate_pjrt_gpu_client_creation_info(bool value) {
  
  _impl_.populate_pjrt_gpu_client_creation_info_ = value;
}
inline void GPUOptions_Experimental::set_populate_pjrt_gpu_client_creation_info(bool value) {
  _internal_set_populate_pjrt_gpu_client_creation_info(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.populate_pjrt_gpu_client_creation_info)
}

// int32 node_id = 18;
inline void GPUOptions_Experimental::clear_node_id() {
  _impl_.node_id_ = 0;
}
inline int32_t GPUOptions_Experimental::_internal_node_id() const {
  return _impl_.node_id_;
}
inline int32_t GPUOptions_Experimental::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.node_id)
  return _internal_node_id();
}
inline void GPUOptions_Experimental::_internal_set_node_id(int32_t value) {
  
  _impl_.node_id_ = value;
}
inline void GPUOptions_Experimental::set_node_id(int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.Experimental.node_id)
}

// .tensorflow.GPUOptions.Experimental.StreamMergeOptions stream_merge_options = 19;
inline bool GPUOptions_Experimental::_internal_has_stream_merge_options() const {
  return this != internal_default_instance() && _impl_.stream_merge_options_ != nullptr;
}
inline bool GPUOptions_Experimental::has_stream_merge_options() const {
  return _internal_has_stream_merge_options();
}
inline void GPUOptions_Experimental::clear_stream_merge_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.stream_merge_options_ != nullptr) {
    delete _impl_.stream_merge_options_;
  }
  _impl_.stream_merge_options_ = nullptr;
}
inline const ::tensorflow::GPUOptions_Experimental_StreamMergeOptions& GPUOptions_Experimental::_internal_stream_merge_options() const {
  const ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* p = _impl_.stream_merge_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GPUOptions_Experimental_StreamMergeOptions&>(
      ::tensorflow::_GPUOptions_Experimental_StreamMergeOptions_default_instance_);
}
inline const ::tensorflow::GPUOptions_Experimental_StreamMergeOptions& GPUOptions_Experimental::stream_merge_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.Experimental.stream_merge_options)
  return _internal_stream_merge_options();
}
inline void GPUOptions_Experimental::unsafe_arena_set_allocated_stream_merge_options(
    ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* stream_merge_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.stream_merge_options_);
  }
  _impl_.stream_merge_options_ = stream_merge_options;
  if (stream_merge_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUOptions.Experimental.stream_merge_options)
}
inline ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* GPUOptions_Experimental::release_stream_merge_options() {
  
  ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* temp = _impl_.stream_merge_options_;
  _impl_.stream_merge_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* GPUOptions_Experimental::unsafe_arena_release_stream_merge_options() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.Experimental.stream_merge_options)
  
  ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* temp = _impl_.stream_merge_options_;
  _impl_.stream_merge_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* GPUOptions_Experimental::_internal_mutable_stream_merge_options() {
  
  if (_impl_.stream_merge_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GPUOptions_Experimental_StreamMergeOptions>(GetArenaForAllocation());
    _impl_.stream_merge_options_ = p;
  }
  return _impl_.stream_merge_options_;
}
inline ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* GPUOptions_Experimental::mutable_stream_merge_options() {
  ::tensorflow::GPUOptions_Experimental_StreamMergeOptions* _msg = _internal_mutable_stream_merge_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.Experimental.stream_merge_options)
  return _msg;
}
inline void GPUOptions_Experimental::set_allocated_stream_merge_options(::tensorflow::GPUOptions_Experimental_StreamMergeOptions* stream_merge_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.stream_merge_options_;
  }
  if (stream_merge_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(stream_merge_options);
    if (message_arena != submessage_arena) {
      stream_merge_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, stream_merge_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.stream_merge_options_ = stream_merge_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.Experimental.stream_merge_options)
}

// -------------------------------------------------------------------

// GPUOptions

// double per_process_gpu_memory_fraction = 1;
inline void GPUOptions::clear_per_process_gpu_memory_fraction() {
  _impl_.per_process_gpu_memory_fraction_ = 0;
}
inline double GPUOptions::_internal_per_process_gpu_memory_fraction() const {
  return _impl_.per_process_gpu_memory_fraction_;
}
inline double GPUOptions::per_process_gpu_memory_fraction() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.per_process_gpu_memory_fraction)
  return _internal_per_process_gpu_memory_fraction();
}
inline void GPUOptions::_internal_set_per_process_gpu_memory_fraction(double value) {
  
  _impl_.per_process_gpu_memory_fraction_ = value;
}
inline void GPUOptions::set_per_process_gpu_memory_fraction(double value) {
  _internal_set_per_process_gpu_memory_fraction(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.per_process_gpu_memory_fraction)
}

// bool allow_growth = 4;
inline void GPUOptions::clear_allow_growth() {
  _impl_.allow_growth_ = false;
}
inline bool GPUOptions::_internal_allow_growth() const {
  return _impl_.allow_growth_;
}
inline bool GPUOptions::allow_growth() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.allow_growth)
  return _internal_allow_growth();
}
inline void GPUOptions::_internal_set_allow_growth(bool value) {
  
  _impl_.allow_growth_ = value;
}
inline void GPUOptions::set_allow_growth(bool value) {
  _internal_set_allow_growth(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.allow_growth)
}

// string allocator_type = 2;
inline void GPUOptions::clear_allocator_type() {
  _impl_.allocator_type_.ClearToEmpty();
}
inline const std::string& GPUOptions::allocator_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.allocator_type)
  return _internal_allocator_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GPUOptions::set_allocator_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.allocator_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.allocator_type)
}
inline std::string* GPUOptions::mutable_allocator_type() {
  std::string* _s = _internal_mutable_allocator_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.allocator_type)
  return _s;
}
inline const std::string& GPUOptions::_internal_allocator_type() const {
  return _impl_.allocator_type_.Get();
}
inline void GPUOptions::_internal_set_allocator_type(const std::string& value) {
  
  _impl_.allocator_type_.Set(value, GetArenaForAllocation());
}
inline std::string* GPUOptions::_internal_mutable_allocator_type() {
  
  return _impl_.allocator_type_.Mutable(GetArenaForAllocation());
}
inline std::string* GPUOptions::release_allocator_type() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.allocator_type)
  return _impl_.allocator_type_.Release();
}
inline void GPUOptions::set_allocated_allocator_type(std::string* allocator_type) {
  if (allocator_type != nullptr) {
    
  } else {
    
  }
  _impl_.allocator_type_.SetAllocated(allocator_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.allocator_type_.IsDefault()) {
    _impl_.allocator_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.allocator_type)
}

// int64 deferred_deletion_bytes = 3;
inline void GPUOptions::clear_deferred_deletion_bytes() {
  _impl_.deferred_deletion_bytes_ = int64_t{0};
}
inline int64_t GPUOptions::_internal_deferred_deletion_bytes() const {
  return _impl_.deferred_deletion_bytes_;
}
inline int64_t GPUOptions::deferred_deletion_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.deferred_deletion_bytes)
  return _internal_deferred_deletion_bytes();
}
inline void GPUOptions::_internal_set_deferred_deletion_bytes(int64_t value) {
  
  _impl_.deferred_deletion_bytes_ = value;
}
inline void GPUOptions::set_deferred_deletion_bytes(int64_t value) {
  _internal_set_deferred_deletion_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.deferred_deletion_bytes)
}

// string visible_device_list = 5;
inline void GPUOptions::clear_visible_device_list() {
  _impl_.visible_device_list_.ClearToEmpty();
}
inline const std::string& GPUOptions::visible_device_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.visible_device_list)
  return _internal_visible_device_list();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GPUOptions::set_visible_device_list(ArgT0&& arg0, ArgT... args) {
 
 _impl_.visible_device_list_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.visible_device_list)
}
inline std::string* GPUOptions::mutable_visible_device_list() {
  std::string* _s = _internal_mutable_visible_device_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.visible_device_list)
  return _s;
}
inline const std::string& GPUOptions::_internal_visible_device_list() const {
  return _impl_.visible_device_list_.Get();
}
inline void GPUOptions::_internal_set_visible_device_list(const std::string& value) {
  
  _impl_.visible_device_list_.Set(value, GetArenaForAllocation());
}
inline std::string* GPUOptions::_internal_mutable_visible_device_list() {
  
  return _impl_.visible_device_list_.Mutable(GetArenaForAllocation());
}
inline std::string* GPUOptions::release_visible_device_list() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.visible_device_list)
  return _impl_.visible_device_list_.Release();
}
inline void GPUOptions::set_allocated_visible_device_list(std::string* visible_device_list) {
  if (visible_device_list != nullptr) {
    
  } else {
    
  }
  _impl_.visible_device_list_.SetAllocated(visible_device_list, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.visible_device_list_.IsDefault()) {
    _impl_.visible_device_list_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.visible_device_list)
}

// int32 polling_active_delay_usecs = 6;
inline void GPUOptions::clear_polling_active_delay_usecs() {
  _impl_.polling_active_delay_usecs_ = 0;
}
inline int32_t GPUOptions::_internal_polling_active_delay_usecs() const {
  return _impl_.polling_active_delay_usecs_;
}
inline int32_t GPUOptions::polling_active_delay_usecs() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.polling_active_delay_usecs)
  return _internal_polling_active_delay_usecs();
}
inline void GPUOptions::_internal_set_polling_active_delay_usecs(int32_t value) {
  
  _impl_.polling_active_delay_usecs_ = value;
}
inline void GPUOptions::set_polling_active_delay_usecs(int32_t value) {
  _internal_set_polling_active_delay_usecs(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.polling_active_delay_usecs)
}

// int32 polling_inactive_delay_msecs = 7;
inline void GPUOptions::clear_polling_inactive_delay_msecs() {
  _impl_.polling_inactive_delay_msecs_ = 0;
}
inline int32_t GPUOptions::_internal_polling_inactive_delay_msecs() const {
  return _impl_.polling_inactive_delay_msecs_;
}
inline int32_t GPUOptions::polling_inactive_delay_msecs() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.polling_inactive_delay_msecs)
  return _internal_polling_inactive_delay_msecs();
}
inline void GPUOptions::_internal_set_polling_inactive_delay_msecs(int32_t value) {
  
  _impl_.polling_inactive_delay_msecs_ = value;
}
inline void GPUOptions::set_polling_inactive_delay_msecs(int32_t value) {
  _internal_set_polling_inactive_delay_msecs(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.polling_inactive_delay_msecs)
}

// bool force_gpu_compatible = 8;
inline void GPUOptions::clear_force_gpu_compatible() {
  _impl_.force_gpu_compatible_ = false;
}
inline bool GPUOptions::_internal_force_gpu_compatible() const {
  return _impl_.force_gpu_compatible_;
}
inline bool GPUOptions::force_gpu_compatible() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.force_gpu_compatible)
  return _internal_force_gpu_compatible();
}
inline void GPUOptions::_internal_set_force_gpu_compatible(bool value) {
  
  _impl_.force_gpu_compatible_ = value;
}
inline void GPUOptions::set_force_gpu_compatible(bool value) {
  _internal_set_force_gpu_compatible(value);
  // @@protoc_insertion_point(field_set:tensorflow.GPUOptions.force_gpu_compatible)
}

// .tensorflow.GPUOptions.Experimental experimental = 9;
inline bool GPUOptions::_internal_has_experimental() const {
  return this != internal_default_instance() && _impl_.experimental_ != nullptr;
}
inline bool GPUOptions::has_experimental() const {
  return _internal_has_experimental();
}
inline void GPUOptions::clear_experimental() {
  if (GetArenaForAllocation() == nullptr && _impl_.experimental_ != nullptr) {
    delete _impl_.experimental_;
  }
  _impl_.experimental_ = nullptr;
}
inline const ::tensorflow::GPUOptions_Experimental& GPUOptions::_internal_experimental() const {
  const ::tensorflow::GPUOptions_Experimental* p = _impl_.experimental_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GPUOptions_Experimental&>(
      ::tensorflow::_GPUOptions_Experimental_default_instance_);
}
inline const ::tensorflow::GPUOptions_Experimental& GPUOptions::experimental() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUOptions.experimental)
  return _internal_experimental();
}
inline void GPUOptions::unsafe_arena_set_allocated_experimental(
    ::tensorflow::GPUOptions_Experimental* experimental) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.experimental_);
  }
  _impl_.experimental_ = experimental;
  if (experimental) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GPUOptions.experimental)
}
inline ::tensorflow::GPUOptions_Experimental* GPUOptions::release_experimental() {
  
  ::tensorflow::GPUOptions_Experimental* temp = _impl_.experimental_;
  _impl_.experimental_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GPUOptions_Experimental* GPUOptions::unsafe_arena_release_experimental() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUOptions.experimental)
  
  ::tensorflow::GPUOptions_Experimental* temp = _impl_.experimental_;
  _impl_.experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::GPUOptions_Experimental* GPUOptions::_internal_mutable_experimental() {
  
  if (_impl_.experimental_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GPUOptions_Experimental>(GetArenaForAllocation());
    _impl_.experimental_ = p;
  }
  return _impl_.experimental_;
}
inline ::tensorflow::GPUOptions_Experimental* GPUOptions::mutable_experimental() {
  ::tensorflow::GPUOptions_Experimental* _msg = _internal_mutable_experimental();
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUOptions.experimental)
  return _msg;
}
inline void GPUOptions::set_allocated_experimental(::tensorflow::GPUOptions_Experimental* experimental) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.experimental_;
  }
  if (experimental) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(experimental);
    if (message_arena != submessage_arena) {
      experimental = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.experimental_ = experimental;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUOptions.experimental)
}

// -------------------------------------------------------------------

// OptimizerOptions

// bool do_common_subexpression_elimination = 1;
inline void OptimizerOptions::clear_do_common_subexpression_elimination() {
  _impl_.do_common_subexpression_elimination_ = false;
}
inline bool OptimizerOptions::_internal_do_common_subexpression_elimination() const {
  return _impl_.do_common_subexpression_elimination_;
}
inline bool OptimizerOptions::do_common_subexpression_elimination() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.do_common_subexpression_elimination)
  return _internal_do_common_subexpression_elimination();
}
inline void OptimizerOptions::_internal_set_do_common_subexpression_elimination(bool value) {
  
  _impl_.do_common_subexpression_elimination_ = value;
}
inline void OptimizerOptions::set_do_common_subexpression_elimination(bool value) {
  _internal_set_do_common_subexpression_elimination(value);
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.do_common_subexpression_elimination)
}

// bool do_constant_folding = 2;
inline void OptimizerOptions::clear_do_constant_folding() {
  _impl_.do_constant_folding_ = false;
}
inline bool OptimizerOptions::_internal_do_constant_folding() const {
  return _impl_.do_constant_folding_;
}
inline bool OptimizerOptions::do_constant_folding() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.do_constant_folding)
  return _internal_do_constant_folding();
}
inline void OptimizerOptions::_internal_set_do_constant_folding(bool value) {
  
  _impl_.do_constant_folding_ = value;
}
inline void OptimizerOptions::set_do_constant_folding(bool value) {
  _internal_set_do_constant_folding(value);
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.do_constant_folding)
}

// int64 max_folded_constant_in_bytes = 6;
inline void OptimizerOptions::clear_max_folded_constant_in_bytes() {
  _impl_.max_folded_constant_in_bytes_ = int64_t{0};
}
inline int64_t OptimizerOptions::_internal_max_folded_constant_in_bytes() const {
  return _impl_.max_folded_constant_in_bytes_;
}
inline int64_t OptimizerOptions::max_folded_constant_in_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.max_folded_constant_in_bytes)
  return _internal_max_folded_constant_in_bytes();
}
inline void OptimizerOptions::_internal_set_max_folded_constant_in_bytes(int64_t value) {
  
  _impl_.max_folded_constant_in_bytes_ = value;
}
inline void OptimizerOptions::set_max_folded_constant_in_bytes(int64_t value) {
  _internal_set_max_folded_constant_in_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.max_folded_constant_in_bytes)
}

// bool do_function_inlining = 4;
inline void OptimizerOptions::clear_do_function_inlining() {
  _impl_.do_function_inlining_ = false;
}
inline bool OptimizerOptions::_internal_do_function_inlining() const {
  return _impl_.do_function_inlining_;
}
inline bool OptimizerOptions::do_function_inlining() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.do_function_inlining)
  return _internal_do_function_inlining();
}
inline void OptimizerOptions::_internal_set_do_function_inlining(bool value) {
  
  _impl_.do_function_inlining_ = value;
}
inline void OptimizerOptions::set_do_function_inlining(bool value) {
  _internal_set_do_function_inlining(value);
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.do_function_inlining)
}

// .tensorflow.OptimizerOptions.Level opt_level = 3;
inline void OptimizerOptions::clear_opt_level() {
  _impl_.opt_level_ = 0;
}
inline ::tensorflow::OptimizerOptions_Level OptimizerOptions::_internal_opt_level() const {
  return static_cast< ::tensorflow::OptimizerOptions_Level >(_impl_.opt_level_);
}
inline ::tensorflow::OptimizerOptions_Level OptimizerOptions::opt_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.opt_level)
  return _internal_opt_level();
}
inline void OptimizerOptions::_internal_set_opt_level(::tensorflow::OptimizerOptions_Level value) {
  
  _impl_.opt_level_ = value;
}
inline void OptimizerOptions::set_opt_level(::tensorflow::OptimizerOptions_Level value) {
  _internal_set_opt_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.opt_level)
}

// .tensorflow.OptimizerOptions.GlobalJitLevel global_jit_level = 5;
inline void OptimizerOptions::clear_global_jit_level() {
  _impl_.global_jit_level_ = 0;
}
inline ::tensorflow::OptimizerOptions_GlobalJitLevel OptimizerOptions::_internal_global_jit_level() const {
  return static_cast< ::tensorflow::OptimizerOptions_GlobalJitLevel >(_impl_.global_jit_level_);
}
inline ::tensorflow::OptimizerOptions_GlobalJitLevel OptimizerOptions::global_jit_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.global_jit_level)
  return _internal_global_jit_level();
}
inline void OptimizerOptions::_internal_set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value) {
  
  _impl_.global_jit_level_ = value;
}
inline void OptimizerOptions::set_global_jit_level(::tensorflow::OptimizerOptions_GlobalJitLevel value) {
  _internal_set_global_jit_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.global_jit_level)
}

// bool cpu_global_jit = 7;
inline void OptimizerOptions::clear_cpu_global_jit() {
  _impl_.cpu_global_jit_ = false;
}
inline bool OptimizerOptions::_internal_cpu_global_jit() const {
  return _impl_.cpu_global_jit_;
}
inline bool OptimizerOptions::cpu_global_jit() const {
  // @@protoc_insertion_point(field_get:tensorflow.OptimizerOptions.cpu_global_jit)
  return _internal_cpu_global_jit();
}
inline void OptimizerOptions::_internal_set_cpu_global_jit(bool value) {
  
  _impl_.cpu_global_jit_ = value;
}
inline void OptimizerOptions::set_cpu_global_jit(bool value) {
  _internal_set_cpu_global_jit(value);
  // @@protoc_insertion_point(field_set:tensorflow.OptimizerOptions.cpu_global_jit)
}

// -------------------------------------------------------------------

// GraphOptions

// bool enable_recv_scheduling = 2;
inline void GraphOptions::clear_enable_recv_scheduling() {
  _impl_.enable_recv_scheduling_ = false;
}
inline bool GraphOptions::_internal_enable_recv_scheduling() const {
  return _impl_.enable_recv_scheduling_;
}
inline bool GraphOptions::enable_recv_scheduling() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.enable_recv_scheduling)
  return _internal_enable_recv_scheduling();
}
inline void GraphOptions::_internal_set_enable_recv_scheduling(bool value) {
  
  _impl_.enable_recv_scheduling_ = value;
}
inline void GraphOptions::set_enable_recv_scheduling(bool value) {
  _internal_set_enable_recv_scheduling(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.enable_recv_scheduling)
}

// .tensorflow.OptimizerOptions optimizer_options = 3;
inline bool GraphOptions::_internal_has_optimizer_options() const {
  return this != internal_default_instance() && _impl_.optimizer_options_ != nullptr;
}
inline bool GraphOptions::has_optimizer_options() const {
  return _internal_has_optimizer_options();
}
inline void GraphOptions::clear_optimizer_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.optimizer_options_ != nullptr) {
    delete _impl_.optimizer_options_;
  }
  _impl_.optimizer_options_ = nullptr;
}
inline const ::tensorflow::OptimizerOptions& GraphOptions::_internal_optimizer_options() const {
  const ::tensorflow::OptimizerOptions* p = _impl_.optimizer_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::OptimizerOptions&>(
      ::tensorflow::_OptimizerOptions_default_instance_);
}
inline const ::tensorflow::OptimizerOptions& GraphOptions::optimizer_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.optimizer_options)
  return _internal_optimizer_options();
}
inline void GraphOptions::unsafe_arena_set_allocated_optimizer_options(
    ::tensorflow::OptimizerOptions* optimizer_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.optimizer_options_);
  }
  _impl_.optimizer_options_ = optimizer_options;
  if (optimizer_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOptions.optimizer_options)
}
inline ::tensorflow::OptimizerOptions* GraphOptions::release_optimizer_options() {
  
  ::tensorflow::OptimizerOptions* temp = _impl_.optimizer_options_;
  _impl_.optimizer_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::OptimizerOptions* GraphOptions::unsafe_arena_release_optimizer_options() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOptions.optimizer_options)
  
  ::tensorflow::OptimizerOptions* temp = _impl_.optimizer_options_;
  _impl_.optimizer_options_ = nullptr;
  return temp;
}
inline ::tensorflow::OptimizerOptions* GraphOptions::_internal_mutable_optimizer_options() {
  
  if (_impl_.optimizer_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OptimizerOptions>(GetArenaForAllocation());
    _impl_.optimizer_options_ = p;
  }
  return _impl_.optimizer_options_;
}
inline ::tensorflow::OptimizerOptions* GraphOptions::mutable_optimizer_options() {
  ::tensorflow::OptimizerOptions* _msg = _internal_mutable_optimizer_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOptions.optimizer_options)
  return _msg;
}
inline void GraphOptions::set_allocated_optimizer_options(::tensorflow::OptimizerOptions* optimizer_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.optimizer_options_;
  }
  if (optimizer_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(optimizer_options);
    if (message_arena != submessage_arena) {
      optimizer_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimizer_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.optimizer_options_ = optimizer_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOptions.optimizer_options)
}

// int64 build_cost_model = 4;
inline void GraphOptions::clear_build_cost_model() {
  _impl_.build_cost_model_ = int64_t{0};
}
inline int64_t GraphOptions::_internal_build_cost_model() const {
  return _impl_.build_cost_model_;
}
inline int64_t GraphOptions::build_cost_model() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.build_cost_model)
  return _internal_build_cost_model();
}
inline void GraphOptions::_internal_set_build_cost_model(int64_t value) {
  
  _impl_.build_cost_model_ = value;
}
inline void GraphOptions::set_build_cost_model(int64_t value) {
  _internal_set_build_cost_model(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.build_cost_model)
}

// int64 build_cost_model_after = 9;
inline void GraphOptions::clear_build_cost_model_after() {
  _impl_.build_cost_model_after_ = int64_t{0};
}
inline int64_t GraphOptions::_internal_build_cost_model_after() const {
  return _impl_.build_cost_model_after_;
}
inline int64_t GraphOptions::build_cost_model_after() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.build_cost_model_after)
  return _internal_build_cost_model_after();
}
inline void GraphOptions::_internal_set_build_cost_model_after(int64_t value) {
  
  _impl_.build_cost_model_after_ = value;
}
inline void GraphOptions::set_build_cost_model_after(int64_t value) {
  _internal_set_build_cost_model_after(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.build_cost_model_after)
}

// bool infer_shapes = 5;
inline void GraphOptions::clear_infer_shapes() {
  _impl_.infer_shapes_ = false;
}
inline bool GraphOptions::_internal_infer_shapes() const {
  return _impl_.infer_shapes_;
}
inline bool GraphOptions::infer_shapes() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.infer_shapes)
  return _internal_infer_shapes();
}
inline void GraphOptions::_internal_set_infer_shapes(bool value) {
  
  _impl_.infer_shapes_ = value;
}
inline void GraphOptions::set_infer_shapes(bool value) {
  _internal_set_infer_shapes(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.infer_shapes)
}

// bool place_pruned_graph = 6;
inline void GraphOptions::clear_place_pruned_graph() {
  _impl_.place_pruned_graph_ = false;
}
inline bool GraphOptions::_internal_place_pruned_graph() const {
  return _impl_.place_pruned_graph_;
}
inline bool GraphOptions::place_pruned_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.place_pruned_graph)
  return _internal_place_pruned_graph();
}
inline void GraphOptions::_internal_set_place_pruned_graph(bool value) {
  
  _impl_.place_pruned_graph_ = value;
}
inline void GraphOptions::set_place_pruned_graph(bool value) {
  _internal_set_place_pruned_graph(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.place_pruned_graph)
}

// bool enable_bfloat16_sendrecv = 7;
inline void GraphOptions::clear_enable_bfloat16_sendrecv() {
  _impl_.enable_bfloat16_sendrecv_ = false;
}
inline bool GraphOptions::_internal_enable_bfloat16_sendrecv() const {
  return _impl_.enable_bfloat16_sendrecv_;
}
inline bool GraphOptions::enable_bfloat16_sendrecv() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.enable_bfloat16_sendrecv)
  return _internal_enable_bfloat16_sendrecv();
}
inline void GraphOptions::_internal_set_enable_bfloat16_sendrecv(bool value) {
  
  _impl_.enable_bfloat16_sendrecv_ = value;
}
inline void GraphOptions::set_enable_bfloat16_sendrecv(bool value) {
  _internal_set_enable_bfloat16_sendrecv(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.enable_bfloat16_sendrecv)
}

// int32 timeline_step = 8;
inline void GraphOptions::clear_timeline_step() {
  _impl_.timeline_step_ = 0;
}
inline int32_t GraphOptions::_internal_timeline_step() const {
  return _impl_.timeline_step_;
}
inline int32_t GraphOptions::timeline_step() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.timeline_step)
  return _internal_timeline_step();
}
inline void GraphOptions::_internal_set_timeline_step(int32_t value) {
  
  _impl_.timeline_step_ = value;
}
inline void GraphOptions::set_timeline_step(int32_t value) {
  _internal_set_timeline_step(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphOptions.timeline_step)
}

// .tensorflow.RewriterConfig rewrite_options = 10;
inline bool GraphOptions::_internal_has_rewrite_options() const {
  return this != internal_default_instance() && _impl_.rewrite_options_ != nullptr;
}
inline bool GraphOptions::has_rewrite_options() const {
  return _internal_has_rewrite_options();
}
inline const ::tensorflow::RewriterConfig& GraphOptions::_internal_rewrite_options() const {
  const ::tensorflow::RewriterConfig* p = _impl_.rewrite_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RewriterConfig&>(
      ::tensorflow::_RewriterConfig_default_instance_);
}
inline const ::tensorflow::RewriterConfig& GraphOptions::rewrite_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphOptions.rewrite_options)
  return _internal_rewrite_options();
}
inline void GraphOptions::unsafe_arena_set_allocated_rewrite_options(
    ::tensorflow::RewriterConfig* rewrite_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.rewrite_options_);
  }
  _impl_.rewrite_options_ = rewrite_options;
  if (rewrite_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.GraphOptions.rewrite_options)
}
inline ::tensorflow::RewriterConfig* GraphOptions::release_rewrite_options() {
  
  ::tensorflow::RewriterConfig* temp = _impl_.rewrite_options_;
  _impl_.rewrite_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RewriterConfig* GraphOptions::unsafe_arena_release_rewrite_options() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphOptions.rewrite_options)
  
  ::tensorflow::RewriterConfig* temp = _impl_.rewrite_options_;
  _impl_.rewrite_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RewriterConfig* GraphOptions::_internal_mutable_rewrite_options() {
  
  if (_impl_.rewrite_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RewriterConfig>(GetArenaForAllocation());
    _impl_.rewrite_options_ = p;
  }
  return _impl_.rewrite_options_;
}
inline ::tensorflow::RewriterConfig* GraphOptions::mutable_rewrite_options() {
  ::tensorflow::RewriterConfig* _msg = _internal_mutable_rewrite_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphOptions.rewrite_options)
  return _msg;
}
inline void GraphOptions::set_allocated_rewrite_options(::tensorflow::RewriterConfig* rewrite_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.rewrite_options_);
  }
  if (rewrite_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rewrite_options));
    if (message_arena != submessage_arena) {
      rewrite_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rewrite_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.rewrite_options_ = rewrite_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphOptions.rewrite_options)
}

// -------------------------------------------------------------------

// ThreadPoolOptionProto

// int32 num_threads = 1;
inline void ThreadPoolOptionProto::clear_num_threads() {
  _impl_.num_threads_ = 0;
}
inline int32_t ThreadPoolOptionProto::_internal_num_threads() const {
  return _impl_.num_threads_;
}
inline int32_t ThreadPoolOptionProto::num_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.ThreadPoolOptionProto.num_threads)
  return _internal_num_threads();
}
inline void ThreadPoolOptionProto::_internal_set_num_threads(int32_t value) {
  
  _impl_.num_threads_ = value;
}
inline void ThreadPoolOptionProto::set_num_threads(int32_t value) {
  _internal_set_num_threads(value);
  // @@protoc_insertion_point(field_set:tensorflow.ThreadPoolOptionProto.num_threads)
}

// string global_name = 2;
inline void ThreadPoolOptionProto::clear_global_name() {
  _impl_.global_name_.ClearToEmpty();
}
inline const std::string& ThreadPoolOptionProto::global_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ThreadPoolOptionProto.global_name)
  return _internal_global_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ThreadPoolOptionProto::set_global_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.global_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ThreadPoolOptionProto.global_name)
}
inline std::string* ThreadPoolOptionProto::mutable_global_name() {
  std::string* _s = _internal_mutable_global_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ThreadPoolOptionProto.global_name)
  return _s;
}
inline const std::string& ThreadPoolOptionProto::_internal_global_name() const {
  return _impl_.global_name_.Get();
}
inline void ThreadPoolOptionProto::_internal_set_global_name(const std::string& value) {
  
  _impl_.global_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ThreadPoolOptionProto::_internal_mutable_global_name() {
  
  return _impl_.global_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ThreadPoolOptionProto::release_global_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ThreadPoolOptionProto.global_name)
  return _impl_.global_name_.Release();
}
inline void ThreadPoolOptionProto::set_allocated_global_name(std::string* global_name) {
  if (global_name != nullptr) {
    
  } else {
    
  }
  _impl_.global_name_.SetAllocated(global_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.global_name_.IsDefault()) {
    _impl_.global_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ThreadPoolOptionProto.global_name)
}

// -------------------------------------------------------------------

// SessionMetadata

// string name = 1;
inline void SessionMetadata::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& SessionMetadata::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionMetadata.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SessionMetadata::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SessionMetadata.name)
}
inline std::string* SessionMetadata::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SessionMetadata.name)
  return _s;
}
inline const std::string& SessionMetadata::_internal_name() const {
  return _impl_.name_.Get();
}
inline void SessionMetadata::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* SessionMetadata::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* SessionMetadata::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SessionMetadata.name)
  return _impl_.name_.Release();
}
inline void SessionMetadata::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SessionMetadata.name)
}

// int64 version = 2;
inline void SessionMetadata::clear_version() {
  _impl_.version_ = int64_t{0};
}
inline int64_t SessionMetadata::_internal_version() const {
  return _impl_.version_;
}
inline int64_t SessionMetadata::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionMetadata.version)
  return _internal_version();
}
inline void SessionMetadata::_internal_set_version(int64_t value) {
  
  _impl_.version_ = value;
}
inline void SessionMetadata::set_version(int64_t value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.SessionMetadata.version)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ConfigProto_Experimental

// string collective_group_leader = 1;
inline void ConfigProto_Experimental::clear_collective_group_leader() {
  _impl_.collective_group_leader_.ClearToEmpty();
}
inline const std::string& ConfigProto_Experimental::collective_group_leader() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.collective_group_leader)
  return _internal_collective_group_leader();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigProto_Experimental::set_collective_group_leader(ArgT0&& arg0, ArgT... args) {
 
 _impl_.collective_group_leader_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.collective_group_leader)
}
inline std::string* ConfigProto_Experimental::mutable_collective_group_leader() {
  std::string* _s = _internal_mutable_collective_group_leader();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.Experimental.collective_group_leader)
  return _s;
}
inline const std::string& ConfigProto_Experimental::_internal_collective_group_leader() const {
  return _impl_.collective_group_leader_.Get();
}
inline void ConfigProto_Experimental::_internal_set_collective_group_leader(const std::string& value) {
  
  _impl_.collective_group_leader_.Set(value, GetArenaForAllocation());
}
inline std::string* ConfigProto_Experimental::_internal_mutable_collective_group_leader() {
  
  return _impl_.collective_group_leader_.Mutable(GetArenaForAllocation());
}
inline std::string* ConfigProto_Experimental::release_collective_group_leader() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.Experimental.collective_group_leader)
  return _impl_.collective_group_leader_.Release();
}
inline void ConfigProto_Experimental::set_allocated_collective_group_leader(std::string* collective_group_leader) {
  if (collective_group_leader != nullptr) {
    
  } else {
    
  }
  _impl_.collective_group_leader_.SetAllocated(collective_group_leader, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.collective_group_leader_.IsDefault()) {
    _impl_.collective_group_leader_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.Experimental.collective_group_leader)
}

// string executor_type = 3;
inline void ConfigProto_Experimental::clear_executor_type() {
  _impl_.executor_type_.ClearToEmpty();
}
inline const std::string& ConfigProto_Experimental::executor_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.executor_type)
  return _internal_executor_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigProto_Experimental::set_executor_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.executor_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.executor_type)
}
inline std::string* ConfigProto_Experimental::mutable_executor_type() {
  std::string* _s = _internal_mutable_executor_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.Experimental.executor_type)
  return _s;
}
inline const std::string& ConfigProto_Experimental::_internal_executor_type() const {
  return _impl_.executor_type_.Get();
}
inline void ConfigProto_Experimental::_internal_set_executor_type(const std::string& value) {
  
  _impl_.executor_type_.Set(value, GetArenaForAllocation());
}
inline std::string* ConfigProto_Experimental::_internal_mutable_executor_type() {
  
  return _impl_.executor_type_.Mutable(GetArenaForAllocation());
}
inline std::string* ConfigProto_Experimental::release_executor_type() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.Experimental.executor_type)
  return _impl_.executor_type_.Release();
}
inline void ConfigProto_Experimental::set_allocated_executor_type(std::string* executor_type) {
  if (executor_type != nullptr) {
    
  } else {
    
  }
  _impl_.executor_type_.SetAllocated(executor_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.executor_type_.IsDefault()) {
    _impl_.executor_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.Experimental.executor_type)
}

// int32 recv_buf_max_chunk = 4;
inline void ConfigProto_Experimental::clear_recv_buf_max_chunk() {
  _impl_.recv_buf_max_chunk_ = 0;
}
inline int32_t ConfigProto_Experimental::_internal_recv_buf_max_chunk() const {
  return _impl_.recv_buf_max_chunk_;
}
inline int32_t ConfigProto_Experimental::recv_buf_max_chunk() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.recv_buf_max_chunk)
  return _internal_recv_buf_max_chunk();
}
inline void ConfigProto_Experimental::_internal_set_recv_buf_max_chunk(int32_t value) {
  
  _impl_.recv_buf_max_chunk_ = value;
}
inline void ConfigProto_Experimental::set_recv_buf_max_chunk(int32_t value) {
  _internal_set_recv_buf_max_chunk(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.recv_buf_max_chunk)
}

// bool use_numa_affinity = 5;
inline void ConfigProto_Experimental::clear_use_numa_affinity() {
  _impl_.use_numa_affinity_ = false;
}
inline bool ConfigProto_Experimental::_internal_use_numa_affinity() const {
  return _impl_.use_numa_affinity_;
}
inline bool ConfigProto_Experimental::use_numa_affinity() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.use_numa_affinity)
  return _internal_use_numa_affinity();
}
inline void ConfigProto_Experimental::_internal_set_use_numa_affinity(bool value) {
  
  _impl_.use_numa_affinity_ = value;
}
inline void ConfigProto_Experimental::set_use_numa_affinity(bool value) {
  _internal_set_use_numa_affinity(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.use_numa_affinity)
}

// bool collective_deterministic_sequential_execution = 6;
inline void ConfigProto_Experimental::clear_collective_deterministic_sequential_execution() {
  _impl_.collective_deterministic_sequential_execution_ = false;
}
inline bool ConfigProto_Experimental::_internal_collective_deterministic_sequential_execution() const {
  return _impl_.collective_deterministic_sequential_execution_;
}
inline bool ConfigProto_Experimental::collective_deterministic_sequential_execution() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.collective_deterministic_sequential_execution)
  return _internal_collective_deterministic_sequential_execution();
}
inline void ConfigProto_Experimental::_internal_set_collective_deterministic_sequential_execution(bool value) {
  
  _impl_.collective_deterministic_sequential_execution_ = value;
}
inline void ConfigProto_Experimental::set_collective_deterministic_sequential_execution(bool value) {
  _internal_set_collective_deterministic_sequential_execution(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.collective_deterministic_sequential_execution)
}

// bool collective_nccl = 7;
inline void ConfigProto_Experimental::clear_collective_nccl() {
  _impl_.collective_nccl_ = false;
}
inline bool ConfigProto_Experimental::_internal_collective_nccl() const {
  return _impl_.collective_nccl_;
}
inline bool ConfigProto_Experimental::collective_nccl() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.collective_nccl)
  return _internal_collective_nccl();
}
inline void ConfigProto_Experimental::_internal_set_collective_nccl(bool value) {
  
  _impl_.collective_nccl_ = value;
}
inline void ConfigProto_Experimental::set_collective_nccl(bool value) {
  _internal_set_collective_nccl(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.collective_nccl)
}

// bool share_session_state_in_clusterspec_propagation = 8;
inline void ConfigProto_Experimental::clear_share_session_state_in_clusterspec_propagation() {
  _impl_.share_session_state_in_clusterspec_propagation_ = false;
}
inline bool ConfigProto_Experimental::_internal_share_session_state_in_clusterspec_propagation() const {
  return _impl_.share_session_state_in_clusterspec_propagation_;
}
inline bool ConfigProto_Experimental::share_session_state_in_clusterspec_propagation() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.share_session_state_in_clusterspec_propagation)
  return _internal_share_session_state_in_clusterspec_propagation();
}
inline void ConfigProto_Experimental::_internal_set_share_session_state_in_clusterspec_propagation(bool value) {
  
  _impl_.share_session_state_in_clusterspec_propagation_ = value;
}
inline void ConfigProto_Experimental::set_share_session_state_in_clusterspec_propagation(bool value) {
  _internal_set_share_session_state_in_clusterspec_propagation(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.share_session_state_in_clusterspec_propagation)
}

// bool disable_thread_spinning = 9;
inline void ConfigProto_Experimental::clear_disable_thread_spinning() {
  _impl_.disable_thread_spinning_ = false;
}
inline bool ConfigProto_Experimental::_internal_disable_thread_spinning() const {
  return _impl_.disable_thread_spinning_;
}
inline bool ConfigProto_Experimental::disable_thread_spinning() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.disable_thread_spinning)
  return _internal_disable_thread_spinning();
}
inline void ConfigProto_Experimental::_internal_set_disable_thread_spinning(bool value) {
  
  _impl_.disable_thread_spinning_ = value;
}
inline void ConfigProto_Experimental::set_disable_thread_spinning(bool value) {
  _internal_set_disable_thread_spinning(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.disable_thread_spinning)
}

// bool share_cluster_devices_in_session = 10;
inline void ConfigProto_Experimental::clear_share_cluster_devices_in_session() {
  _impl_.share_cluster_devices_in_session_ = false;
}
inline bool ConfigProto_Experimental::_internal_share_cluster_devices_in_session() const {
  return _impl_.share_cluster_devices_in_session_;
}
inline bool ConfigProto_Experimental::share_cluster_devices_in_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.share_cluster_devices_in_session)
  return _internal_share_cluster_devices_in_session();
}
inline void ConfigProto_Experimental::_internal_set_share_cluster_devices_in_session(bool value) {
  
  _impl_.share_cluster_devices_in_session_ = value;
}
inline void ConfigProto_Experimental::set_share_cluster_devices_in_session(bool value) {
  _internal_set_share_cluster_devices_in_session(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.share_cluster_devices_in_session)
}

// .tensorflow.SessionMetadata session_metadata = 11;
inline bool ConfigProto_Experimental::_internal_has_session_metadata() const {
  return this != internal_default_instance() && _impl_.session_metadata_ != nullptr;
}
inline bool ConfigProto_Experimental::has_session_metadata() const {
  return _internal_has_session_metadata();
}
inline void ConfigProto_Experimental::clear_session_metadata() {
  if (GetArenaForAllocation() == nullptr && _impl_.session_metadata_ != nullptr) {
    delete _impl_.session_metadata_;
  }
  _impl_.session_metadata_ = nullptr;
}
inline const ::tensorflow::SessionMetadata& ConfigProto_Experimental::_internal_session_metadata() const {
  const ::tensorflow::SessionMetadata* p = _impl_.session_metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SessionMetadata&>(
      ::tensorflow::_SessionMetadata_default_instance_);
}
inline const ::tensorflow::SessionMetadata& ConfigProto_Experimental::session_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.session_metadata)
  return _internal_session_metadata();
}
inline void ConfigProto_Experimental::unsafe_arena_set_allocated_session_metadata(
    ::tensorflow::SessionMetadata* session_metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.session_metadata_);
  }
  _impl_.session_metadata_ = session_metadata;
  if (session_metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.Experimental.session_metadata)
}
inline ::tensorflow::SessionMetadata* ConfigProto_Experimental::release_session_metadata() {
  
  ::tensorflow::SessionMetadata* temp = _impl_.session_metadata_;
  _impl_.session_metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SessionMetadata* ConfigProto_Experimental::unsafe_arena_release_session_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.Experimental.session_metadata)
  
  ::tensorflow::SessionMetadata* temp = _impl_.session_metadata_;
  _impl_.session_metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionMetadata* ConfigProto_Experimental::_internal_mutable_session_metadata() {
  
  if (_impl_.session_metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionMetadata>(GetArenaForAllocation());
    _impl_.session_metadata_ = p;
  }
  return _impl_.session_metadata_;
}
inline ::tensorflow::SessionMetadata* ConfigProto_Experimental::mutable_session_metadata() {
  ::tensorflow::SessionMetadata* _msg = _internal_mutable_session_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.Experimental.session_metadata)
  return _msg;
}
inline void ConfigProto_Experimental::set_allocated_session_metadata(::tensorflow::SessionMetadata* session_metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.session_metadata_;
  }
  if (session_metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(session_metadata);
    if (message_arena != submessage_arena) {
      session_metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, session_metadata, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.session_metadata_ = session_metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.Experimental.session_metadata)
}

// bool optimize_for_static_graph = 12;
inline void ConfigProto_Experimental::clear_optimize_for_static_graph() {
  _impl_.optimize_for_static_graph_ = false;
}
inline bool ConfigProto_Experimental::_internal_optimize_for_static_graph() const {
  return _impl_.optimize_for_static_graph_;
}
inline bool ConfigProto_Experimental::optimize_for_static_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.optimize_for_static_graph)
  return _internal_optimize_for_static_graph();
}
inline void ConfigProto_Experimental::_internal_set_optimize_for_static_graph(bool value) {
  
  _impl_.optimize_for_static_graph_ = value;
}
inline void ConfigProto_Experimental::set_optimize_for_static_graph(bool value) {
  _internal_set_optimize_for_static_graph(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.optimize_for_static_graph)
}

// bool enable_mlir_bridge = 13;
inline void ConfigProto_Experimental::clear_enable_mlir_bridge() {
  _impl_.enable_mlir_bridge_ = false;
}
inline bool ConfigProto_Experimental::_internal_enable_mlir_bridge() const {
  return _impl_.enable_mlir_bridge_;
}
inline bool ConfigProto_Experimental::enable_mlir_bridge() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.enable_mlir_bridge)
  return _internal_enable_mlir_bridge();
}
inline void ConfigProto_Experimental::_internal_set_enable_mlir_bridge(bool value) {
  
  _impl_.enable_mlir_bridge_ = value;
}
inline void ConfigProto_Experimental::set_enable_mlir_bridge(bool value) {
  _internal_set_enable_mlir_bridge(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.enable_mlir_bridge)
}

// .tensorflow.ConfigProto.Experimental.MlirBridgeRollout mlir_bridge_rollout = 17;
inline void ConfigProto_Experimental::clear_mlir_bridge_rollout() {
  _impl_.mlir_bridge_rollout_ = 0;
}
inline ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout ConfigProto_Experimental::_internal_mlir_bridge_rollout() const {
  return static_cast< ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout >(_impl_.mlir_bridge_rollout_);
}
inline ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout ConfigProto_Experimental::mlir_bridge_rollout() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.mlir_bridge_rollout)
  return _internal_mlir_bridge_rollout();
}
inline void ConfigProto_Experimental::_internal_set_mlir_bridge_rollout(::tensorflow::ConfigProto_Experimental_MlirBridgeRollout value) {
  
  _impl_.mlir_bridge_rollout_ = value;
}
inline void ConfigProto_Experimental::set_mlir_bridge_rollout(::tensorflow::ConfigProto_Experimental_MlirBridgeRollout value) {
  _internal_set_mlir_bridge_rollout(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.mlir_bridge_rollout)
}

// bool enable_mlir_graph_optimization = 16;
inline void ConfigProto_Experimental::clear_enable_mlir_graph_optimization() {
  _impl_.enable_mlir_graph_optimization_ = false;
}
inline bool ConfigProto_Experimental::_internal_enable_mlir_graph_optimization() const {
  return _impl_.enable_mlir_graph_optimization_;
}
inline bool ConfigProto_Experimental::enable_mlir_graph_optimization() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.enable_mlir_graph_optimization)
  return _internal_enable_mlir_graph_optimization();
}
inline void ConfigProto_Experimental::_internal_set_enable_mlir_graph_optimization(bool value) {
  
  _impl_.enable_mlir_graph_optimization_ = value;
}
inline void ConfigProto_Experimental::set_enable_mlir_graph_optimization(bool value) {
  _internal_set_enable_mlir_graph_optimization(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.enable_mlir_graph_optimization)
}

// bool disable_output_partition_graphs = 14;
inline void ConfigProto_Experimental::clear_disable_output_partition_graphs() {
  _impl_.disable_output_partition_graphs_ = false;
}
inline bool ConfigProto_Experimental::_internal_disable_output_partition_graphs() const {
  return _impl_.disable_output_partition_graphs_;
}
inline bool ConfigProto_Experimental::disable_output_partition_graphs() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.disable_output_partition_graphs)
  return _internal_disable_output_partition_graphs();
}
inline void ConfigProto_Experimental::_internal_set_disable_output_partition_graphs(bool value) {
  
  _impl_.disable_output_partition_graphs_ = value;
}
inline void ConfigProto_Experimental::set_disable_output_partition_graphs(bool value) {
  _internal_set_disable_output_partition_graphs(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.disable_output_partition_graphs)
}

// int64 xla_fusion_autotuner_thresh = 15;
inline void ConfigProto_Experimental::clear_xla_fusion_autotuner_thresh() {
  _impl_.xla_fusion_autotuner_thresh_ = int64_t{0};
}
inline int64_t ConfigProto_Experimental::_internal_xla_fusion_autotuner_thresh() const {
  return _impl_.xla_fusion_autotuner_thresh_;
}
inline int64_t ConfigProto_Experimental::xla_fusion_autotuner_thresh() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.xla_fusion_autotuner_thresh)
  return _internal_xla_fusion_autotuner_thresh();
}
inline void ConfigProto_Experimental::_internal_set_xla_fusion_autotuner_thresh(int64_t value) {
  
  _impl_.xla_fusion_autotuner_thresh_ = value;
}
inline void ConfigProto_Experimental::set_xla_fusion_autotuner_thresh(int64_t value) {
  _internal_set_xla_fusion_autotuner_thresh(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.xla_fusion_autotuner_thresh)
}

// bool use_tfrt = 18;
inline void ConfigProto_Experimental::clear_use_tfrt() {
  _impl_.use_tfrt_ = false;
}
inline bool ConfigProto_Experimental::_internal_use_tfrt() const {
  return _impl_.use_tfrt_;
}
inline bool ConfigProto_Experimental::use_tfrt() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.use_tfrt)
  return _internal_use_tfrt();
}
inline void ConfigProto_Experimental::_internal_set_use_tfrt(bool value) {
  
  _impl_.use_tfrt_ = value;
}
inline void ConfigProto_Experimental::set_use_tfrt(bool value) {
  _internal_set_use_tfrt(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.use_tfrt)
}

// bool enable_multi_host = 27;
inline void ConfigProto_Experimental::clear_enable_multi_host() {
  _impl_.enable_multi_host_ = false;
}
inline bool ConfigProto_Experimental::_internal_enable_multi_host() const {
  return _impl_.enable_multi_host_;
}
inline bool ConfigProto_Experimental::enable_multi_host() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.enable_multi_host)
  return _internal_enable_multi_host();
}
inline void ConfigProto_Experimental::_internal_set_enable_multi_host(bool value) {
  
  _impl_.enable_multi_host_ = value;
}
inline void ConfigProto_Experimental::set_enable_multi_host(bool value) {
  _internal_set_enable_multi_host(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.enable_multi_host)
}

// bool tfrt_use_ifrt = 32;
inline void ConfigProto_Experimental::clear_tfrt_use_ifrt() {
  _impl_.tfrt_use_ifrt_ = false;
}
inline bool ConfigProto_Experimental::_internal_tfrt_use_ifrt() const {
  return _impl_.tfrt_use_ifrt_;
}
inline bool ConfigProto_Experimental::tfrt_use_ifrt() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.tfrt_use_ifrt)
  return _internal_tfrt_use_ifrt();
}
inline void ConfigProto_Experimental::_internal_set_tfrt_use_ifrt(bool value) {
  
  _impl_.tfrt_use_ifrt_ = value;
}
inline void ConfigProto_Experimental::set_tfrt_use_ifrt(bool value) {
  _internal_set_tfrt_use_ifrt(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.tfrt_use_ifrt)
}

// int32 backend_server_port = 28;
inline void ConfigProto_Experimental::clear_backend_server_port() {
  _impl_.backend_server_port_ = 0;
}
inline int32_t ConfigProto_Experimental::_internal_backend_server_port() const {
  return _impl_.backend_server_port_;
}
inline int32_t ConfigProto_Experimental::backend_server_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.backend_server_port)
  return _internal_backend_server_port();
}
inline void ConfigProto_Experimental::_internal_set_backend_server_port(int32_t value) {
  
  _impl_.backend_server_port_ = value;
}
inline void ConfigProto_Experimental::set_backend_server_port(int32_t value) {
  _internal_set_backend_server_port(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.backend_server_port)
}

// bool target_tpu = 29;
inline void ConfigProto_Experimental::clear_target_tpu() {
  _impl_.target_tpu_ = false;
}
inline bool ConfigProto_Experimental::_internal_target_tpu() const {
  return _impl_.target_tpu_;
}
inline bool ConfigProto_Experimental::target_tpu() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.target_tpu)
  return _internal_target_tpu();
}
inline void ConfigProto_Experimental::_internal_set_target_tpu(bool value) {
  
  _impl_.target_tpu_ = value;
}
inline void ConfigProto_Experimental::set_target_tpu(bool value) {
  _internal_set_target_tpu(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.target_tpu)
}

// bool target_gpu = 30;
inline void ConfigProto_Experimental::clear_target_gpu() {
  _impl_.target_gpu_ = false;
}
inline bool ConfigProto_Experimental::_internal_target_gpu() const {
  return _impl_.target_gpu_;
}
inline bool ConfigProto_Experimental::target_gpu() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.target_gpu)
  return _internal_target_gpu();
}
inline void ConfigProto_Experimental::_internal_set_target_gpu(bool value) {
  
  _impl_.target_gpu_ = value;
}
inline void ConfigProto_Experimental::set_target_gpu(bool value) {
  _internal_set_target_gpu(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.target_gpu)
}

// int32 stream_merge_threshold = 31;
inline void ConfigProto_Experimental::clear_stream_merge_threshold() {
  _impl_.stream_merge_threshold_ = 0;
}
inline int32_t ConfigProto_Experimental::_internal_stream_merge_threshold() const {
  return _impl_.stream_merge_threshold_;
}
inline int32_t ConfigProto_Experimental::stream_merge_threshold() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.stream_merge_threshold)
  return _internal_stream_merge_threshold();
}
inline void ConfigProto_Experimental::_internal_set_stream_merge_threshold(int32_t value) {
  
  _impl_.stream_merge_threshold_ = value;
}
inline void ConfigProto_Experimental::set_stream_merge_threshold(int32_t value) {
  _internal_set_stream_merge_threshold(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.stream_merge_threshold)
}

// bool disable_functional_ops_lowering = 21;
inline void ConfigProto_Experimental::clear_disable_functional_ops_lowering() {
  _impl_.disable_functional_ops_lowering_ = false;
}
inline bool ConfigProto_Experimental::_internal_disable_functional_ops_lowering() const {
  return _impl_.disable_functional_ops_lowering_;
}
inline bool ConfigProto_Experimental::disable_functional_ops_lowering() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.disable_functional_ops_lowering)
  return _internal_disable_functional_ops_lowering();
}
inline void ConfigProto_Experimental::_internal_set_disable_functional_ops_lowering(bool value) {
  
  _impl_.disable_functional_ops_lowering_ = value;
}
inline void ConfigProto_Experimental::set_disable_functional_ops_lowering(bool value) {
  _internal_set_disable_functional_ops_lowering(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.disable_functional_ops_lowering)
}

// bool xla_prefer_single_graph_cluster = 22;
inline void ConfigProto_Experimental::clear_xla_prefer_single_graph_cluster() {
  _impl_.xla_prefer_single_graph_cluster_ = false;
}
inline bool ConfigProto_Experimental::_internal_xla_prefer_single_graph_cluster() const {
  return _impl_.xla_prefer_single_graph_cluster_;
}
inline bool ConfigProto_Experimental::xla_prefer_single_graph_cluster() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.xla_prefer_single_graph_cluster)
  return _internal_xla_prefer_single_graph_cluster();
}
inline void ConfigProto_Experimental::_internal_set_xla_prefer_single_graph_cluster(bool value) {
  
  _impl_.xla_prefer_single_graph_cluster_ = value;
}
inline void ConfigProto_Experimental::set_xla_prefer_single_graph_cluster(bool value) {
  _internal_set_xla_prefer_single_graph_cluster(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.xla_prefer_single_graph_cluster)
}

// .tensorflow.CoordinationServiceConfig coordination_config = 23;
inline bool ConfigProto_Experimental::_internal_has_coordination_config() const {
  return this != internal_default_instance() && _impl_.coordination_config_ != nullptr;
}
inline bool ConfigProto_Experimental::has_coordination_config() const {
  return _internal_has_coordination_config();
}
inline const ::tensorflow::CoordinationServiceConfig& ConfigProto_Experimental::_internal_coordination_config() const {
  const ::tensorflow::CoordinationServiceConfig* p = _impl_.coordination_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CoordinationServiceConfig&>(
      ::tensorflow::_CoordinationServiceConfig_default_instance_);
}
inline const ::tensorflow::CoordinationServiceConfig& ConfigProto_Experimental::coordination_config() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.coordination_config)
  return _internal_coordination_config();
}
inline void ConfigProto_Experimental::unsafe_arena_set_allocated_coordination_config(
    ::tensorflow::CoordinationServiceConfig* coordination_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.coordination_config_);
  }
  _impl_.coordination_config_ = coordination_config;
  if (coordination_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.Experimental.coordination_config)
}
inline ::tensorflow::CoordinationServiceConfig* ConfigProto_Experimental::release_coordination_config() {
  
  ::tensorflow::CoordinationServiceConfig* temp = _impl_.coordination_config_;
  _impl_.coordination_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CoordinationServiceConfig* ConfigProto_Experimental::unsafe_arena_release_coordination_config() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.Experimental.coordination_config)
  
  ::tensorflow::CoordinationServiceConfig* temp = _impl_.coordination_config_;
  _impl_.coordination_config_ = nullptr;
  return temp;
}
inline ::tensorflow::CoordinationServiceConfig* ConfigProto_Experimental::_internal_mutable_coordination_config() {
  
  if (_impl_.coordination_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CoordinationServiceConfig>(GetArenaForAllocation());
    _impl_.coordination_config_ = p;
  }
  return _impl_.coordination_config_;
}
inline ::tensorflow::CoordinationServiceConfig* ConfigProto_Experimental::mutable_coordination_config() {
  ::tensorflow::CoordinationServiceConfig* _msg = _internal_mutable_coordination_config();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.Experimental.coordination_config)
  return _msg;
}
inline void ConfigProto_Experimental::set_allocated_coordination_config(::tensorflow::CoordinationServiceConfig* coordination_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.coordination_config_);
  }
  if (coordination_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(coordination_config));
    if (message_arena != submessage_arena) {
      coordination_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, coordination_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.coordination_config_ = coordination_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.Experimental.coordination_config)
}

// bool disable_optimize_for_static_graph = 24;
inline void ConfigProto_Experimental::clear_disable_optimize_for_static_graph() {
  _impl_.disable_optimize_for_static_graph_ = false;
}
inline bool ConfigProto_Experimental::_internal_disable_optimize_for_static_graph() const {
  return _impl_.disable_optimize_for_static_graph_;
}
inline bool ConfigProto_Experimental::disable_optimize_for_static_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.disable_optimize_for_static_graph)
  return _internal_disable_optimize_for_static_graph();
}
inline void ConfigProto_Experimental::_internal_set_disable_optimize_for_static_graph(bool value) {
  
  _impl_.disable_optimize_for_static_graph_ = value;
}
inline void ConfigProto_Experimental::set_disable_optimize_for_static_graph(bool value) {
  _internal_set_disable_optimize_for_static_graph(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.disable_optimize_for_static_graph)
}

// bool disable_eager_executor_streaming_enqueue = 26;
inline void ConfigProto_Experimental::clear_disable_eager_executor_streaming_enqueue() {
  _impl_.disable_eager_executor_streaming_enqueue_ = false;
}
inline bool ConfigProto_Experimental::_internal_disable_eager_executor_streaming_enqueue() const {
  return _impl_.disable_eager_executor_streaming_enqueue_;
}
inline bool ConfigProto_Experimental::disable_eager_executor_streaming_enqueue() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.Experimental.disable_eager_executor_streaming_enqueue)
  return _internal_disable_eager_executor_streaming_enqueue();
}
inline void ConfigProto_Experimental::_internal_set_disable_eager_executor_streaming_enqueue(bool value) {
  
  _impl_.disable_eager_executor_streaming_enqueue_ = value;
}
inline void ConfigProto_Experimental::set_disable_eager_executor_streaming_enqueue(bool value) {
  _internal_set_disable_eager_executor_streaming_enqueue(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.Experimental.disable_eager_executor_streaming_enqueue)
}

// -------------------------------------------------------------------

// ConfigProto

// map<string, int32> device_count = 1;
inline int ConfigProto::_internal_device_count_size() const {
  return _impl_.device_count_.size();
}
inline int ConfigProto::device_count_size() const {
  return _internal_device_count_size();
}
inline void ConfigProto::clear_device_count() {
  _impl_.device_count_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
ConfigProto::_internal_device_count() const {
  return _impl_.device_count_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >&
ConfigProto::device_count() const {
  // @@protoc_insertion_point(field_map:tensorflow.ConfigProto.device_count)
  return _internal_device_count();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
ConfigProto::_internal_mutable_device_count() {
  return _impl_.device_count_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int32_t >*
ConfigProto::mutable_device_count() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ConfigProto.device_count)
  return _internal_mutable_device_count();
}

// int32 intra_op_parallelism_threads = 2;
inline void ConfigProto::clear_intra_op_parallelism_threads() {
  _impl_.intra_op_parallelism_threads_ = 0;
}
inline int32_t ConfigProto::_internal_intra_op_parallelism_threads() const {
  return _impl_.intra_op_parallelism_threads_;
}
inline int32_t ConfigProto::intra_op_parallelism_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.intra_op_parallelism_threads)
  return _internal_intra_op_parallelism_threads();
}
inline void ConfigProto::_internal_set_intra_op_parallelism_threads(int32_t value) {
  
  _impl_.intra_op_parallelism_threads_ = value;
}
inline void ConfigProto::set_intra_op_parallelism_threads(int32_t value) {
  _internal_set_intra_op_parallelism_threads(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.intra_op_parallelism_threads)
}

// int32 inter_op_parallelism_threads = 5;
inline void ConfigProto::clear_inter_op_parallelism_threads() {
  _impl_.inter_op_parallelism_threads_ = 0;
}
inline int32_t ConfigProto::_internal_inter_op_parallelism_threads() const {
  return _impl_.inter_op_parallelism_threads_;
}
inline int32_t ConfigProto::inter_op_parallelism_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.inter_op_parallelism_threads)
  return _internal_inter_op_parallelism_threads();
}
inline void ConfigProto::_internal_set_inter_op_parallelism_threads(int32_t value) {
  
  _impl_.inter_op_parallelism_threads_ = value;
}
inline void ConfigProto::set_inter_op_parallelism_threads(int32_t value) {
  _internal_set_inter_op_parallelism_threads(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.inter_op_parallelism_threads)
}

// bool use_per_session_threads = 9;
inline void ConfigProto::clear_use_per_session_threads() {
  _impl_.use_per_session_threads_ = false;
}
inline bool ConfigProto::_internal_use_per_session_threads() const {
  return _impl_.use_per_session_threads_;
}
inline bool ConfigProto::use_per_session_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.use_per_session_threads)
  return _internal_use_per_session_threads();
}
inline void ConfigProto::_internal_set_use_per_session_threads(bool value) {
  
  _impl_.use_per_session_threads_ = value;
}
inline void ConfigProto::set_use_per_session_threads(bool value) {
  _internal_set_use_per_session_threads(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.use_per_session_threads)
}

// repeated .tensorflow.ThreadPoolOptionProto session_inter_op_thread_pool = 12;
inline int ConfigProto::_internal_session_inter_op_thread_pool_size() const {
  return _impl_.session_inter_op_thread_pool_.size();
}
inline int ConfigProto::session_inter_op_thread_pool_size() const {
  return _internal_session_inter_op_thread_pool_size();
}
inline void ConfigProto::clear_session_inter_op_thread_pool() {
  _impl_.session_inter_op_thread_pool_.Clear();
}
inline ::tensorflow::ThreadPoolOptionProto* ConfigProto::mutable_session_inter_op_thread_pool(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return _impl_.session_inter_op_thread_pool_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto >*
ConfigProto::mutable_session_inter_op_thread_pool() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return &_impl_.session_inter_op_thread_pool_;
}
inline const ::tensorflow::ThreadPoolOptionProto& ConfigProto::_internal_session_inter_op_thread_pool(int index) const {
  return _impl_.session_inter_op_thread_pool_.Get(index);
}
inline const ::tensorflow::ThreadPoolOptionProto& ConfigProto::session_inter_op_thread_pool(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return _internal_session_inter_op_thread_pool(index);
}
inline ::tensorflow::ThreadPoolOptionProto* ConfigProto::_internal_add_session_inter_op_thread_pool() {
  return _impl_.session_inter_op_thread_pool_.Add();
}
inline ::tensorflow::ThreadPoolOptionProto* ConfigProto::add_session_inter_op_thread_pool() {
  ::tensorflow::ThreadPoolOptionProto* _add = _internal_add_session_inter_op_thread_pool();
  // @@protoc_insertion_point(field_add:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ThreadPoolOptionProto >&
ConfigProto::session_inter_op_thread_pool() const {
  // @@protoc_insertion_point(field_list:tensorflow.ConfigProto.session_inter_op_thread_pool)
  return _impl_.session_inter_op_thread_pool_;
}

// int32 placement_period = 3;
inline void ConfigProto::clear_placement_period() {
  _impl_.placement_period_ = 0;
}
inline int32_t ConfigProto::_internal_placement_period() const {
  return _impl_.placement_period_;
}
inline int32_t ConfigProto::placement_period() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.placement_period)
  return _internal_placement_period();
}
inline void ConfigProto::_internal_set_placement_period(int32_t value) {
  
  _impl_.placement_period_ = value;
}
inline void ConfigProto::set_placement_period(int32_t value) {
  _internal_set_placement_period(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.placement_period)
}

// repeated string device_filters = 4;
inline int ConfigProto::_internal_device_filters_size() const {
  return _impl_.device_filters_.size();
}
inline int ConfigProto::device_filters_size() const {
  return _internal_device_filters_size();
}
inline void ConfigProto::clear_device_filters() {
  _impl_.device_filters_.Clear();
}
inline std::string* ConfigProto::add_device_filters() {
  std::string* _s = _internal_add_device_filters();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ConfigProto.device_filters)
  return _s;
}
inline const std::string& ConfigProto::_internal_device_filters(int index) const {
  return _impl_.device_filters_.Get(index);
}
inline const std::string& ConfigProto::device_filters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.device_filters)
  return _internal_device_filters(index);
}
inline std::string* ConfigProto::mutable_device_filters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.device_filters)
  return _impl_.device_filters_.Mutable(index);
}
inline void ConfigProto::set_device_filters(int index, const std::string& value) {
  _impl_.device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::set_device_filters(int index, std::string&& value) {
  _impl_.device_filters_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::set_device_filters(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::set_device_filters(int index, const char* value, size_t size) {
  _impl_.device_filters_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ConfigProto.device_filters)
}
inline std::string* ConfigProto::_internal_add_device_filters() {
  return _impl_.device_filters_.Add();
}
inline void ConfigProto::add_device_filters(const std::string& value) {
  _impl_.device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::add_device_filters(std::string&& value) {
  _impl_.device_filters_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::add_device_filters(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ConfigProto.device_filters)
}
inline void ConfigProto::add_device_filters(const char* value, size_t size) {
  _impl_.device_filters_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ConfigProto.device_filters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ConfigProto::device_filters() const {
  // @@protoc_insertion_point(field_list:tensorflow.ConfigProto.device_filters)
  return _impl_.device_filters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ConfigProto::mutable_device_filters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ConfigProto.device_filters)
  return &_impl_.device_filters_;
}

// .tensorflow.GPUOptions gpu_options = 6;
inline bool ConfigProto::_internal_has_gpu_options() const {
  return this != internal_default_instance() && _impl_.gpu_options_ != nullptr;
}
inline bool ConfigProto::has_gpu_options() const {
  return _internal_has_gpu_options();
}
inline void ConfigProto::clear_gpu_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.gpu_options_ != nullptr) {
    delete _impl_.gpu_options_;
  }
  _impl_.gpu_options_ = nullptr;
}
inline const ::tensorflow::GPUOptions& ConfigProto::_internal_gpu_options() const {
  const ::tensorflow::GPUOptions* p = _impl_.gpu_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GPUOptions&>(
      ::tensorflow::_GPUOptions_default_instance_);
}
inline const ::tensorflow::GPUOptions& ConfigProto::gpu_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.gpu_options)
  return _internal_gpu_options();
}
inline void ConfigProto::unsafe_arena_set_allocated_gpu_options(
    ::tensorflow::GPUOptions* gpu_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.gpu_options_);
  }
  _impl_.gpu_options_ = gpu_options;
  if (gpu_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.gpu_options)
}
inline ::tensorflow::GPUOptions* ConfigProto::release_gpu_options() {
  
  ::tensorflow::GPUOptions* temp = _impl_.gpu_options_;
  _impl_.gpu_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GPUOptions* ConfigProto::unsafe_arena_release_gpu_options() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.gpu_options)
  
  ::tensorflow::GPUOptions* temp = _impl_.gpu_options_;
  _impl_.gpu_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GPUOptions* ConfigProto::_internal_mutable_gpu_options() {
  
  if (_impl_.gpu_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GPUOptions>(GetArenaForAllocation());
    _impl_.gpu_options_ = p;
  }
  return _impl_.gpu_options_;
}
inline ::tensorflow::GPUOptions* ConfigProto::mutable_gpu_options() {
  ::tensorflow::GPUOptions* _msg = _internal_mutable_gpu_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.gpu_options)
  return _msg;
}
inline void ConfigProto::set_allocated_gpu_options(::tensorflow::GPUOptions* gpu_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.gpu_options_;
  }
  if (gpu_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(gpu_options);
    if (message_arena != submessage_arena) {
      gpu_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, gpu_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.gpu_options_ = gpu_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.gpu_options)
}

// .tensorflow.GPUOptions pluggable_device_options = 18;
inline bool ConfigProto::_internal_has_pluggable_device_options() const {
  return this != internal_default_instance() && _impl_.pluggable_device_options_ != nullptr;
}
inline bool ConfigProto::has_pluggable_device_options() const {
  return _internal_has_pluggable_device_options();
}
inline void ConfigProto::clear_pluggable_device_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.pluggable_device_options_ != nullptr) {
    delete _impl_.pluggable_device_options_;
  }
  _impl_.pluggable_device_options_ = nullptr;
}
inline const ::tensorflow::GPUOptions& ConfigProto::_internal_pluggable_device_options() const {
  const ::tensorflow::GPUOptions* p = _impl_.pluggable_device_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GPUOptions&>(
      ::tensorflow::_GPUOptions_default_instance_);
}
inline const ::tensorflow::GPUOptions& ConfigProto::pluggable_device_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.pluggable_device_options)
  return _internal_pluggable_device_options();
}
inline void ConfigProto::unsafe_arena_set_allocated_pluggable_device_options(
    ::tensorflow::GPUOptions* pluggable_device_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.pluggable_device_options_);
  }
  _impl_.pluggable_device_options_ = pluggable_device_options;
  if (pluggable_device_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.pluggable_device_options)
}
inline ::tensorflow::GPUOptions* ConfigProto::release_pluggable_device_options() {
  
  ::tensorflow::GPUOptions* temp = _impl_.pluggable_device_options_;
  _impl_.pluggable_device_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GPUOptions* ConfigProto::unsafe_arena_release_pluggable_device_options() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.pluggable_device_options)
  
  ::tensorflow::GPUOptions* temp = _impl_.pluggable_device_options_;
  _impl_.pluggable_device_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GPUOptions* ConfigProto::_internal_mutable_pluggable_device_options() {
  
  if (_impl_.pluggable_device_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GPUOptions>(GetArenaForAllocation());
    _impl_.pluggable_device_options_ = p;
  }
  return _impl_.pluggable_device_options_;
}
inline ::tensorflow::GPUOptions* ConfigProto::mutable_pluggable_device_options() {
  ::tensorflow::GPUOptions* _msg = _internal_mutable_pluggable_device_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.pluggable_device_options)
  return _msg;
}
inline void ConfigProto::set_allocated_pluggable_device_options(::tensorflow::GPUOptions* pluggable_device_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.pluggable_device_options_;
  }
  if (pluggable_device_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(pluggable_device_options);
    if (message_arena != submessage_arena) {
      pluggable_device_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pluggable_device_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.pluggable_device_options_ = pluggable_device_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.pluggable_device_options)
}

// bool allow_soft_placement = 7;
inline void ConfigProto::clear_allow_soft_placement() {
  _impl_.allow_soft_placement_ = false;
}
inline bool ConfigProto::_internal_allow_soft_placement() const {
  return _impl_.allow_soft_placement_;
}
inline bool ConfigProto::allow_soft_placement() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.allow_soft_placement)
  return _internal_allow_soft_placement();
}
inline void ConfigProto::_internal_set_allow_soft_placement(bool value) {
  
  _impl_.allow_soft_placement_ = value;
}
inline void ConfigProto::set_allow_soft_placement(bool value) {
  _internal_set_allow_soft_placement(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.allow_soft_placement)
}

// bool log_device_placement = 8;
inline void ConfigProto::clear_log_device_placement() {
  _impl_.log_device_placement_ = false;
}
inline bool ConfigProto::_internal_log_device_placement() const {
  return _impl_.log_device_placement_;
}
inline bool ConfigProto::log_device_placement() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.log_device_placement)
  return _internal_log_device_placement();
}
inline void ConfigProto::_internal_set_log_device_placement(bool value) {
  
  _impl_.log_device_placement_ = value;
}
inline void ConfigProto::set_log_device_placement(bool value) {
  _internal_set_log_device_placement(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.log_device_placement)
}

// .tensorflow.GraphOptions graph_options = 10;
inline bool ConfigProto::_internal_has_graph_options() const {
  return this != internal_default_instance() && _impl_.graph_options_ != nullptr;
}
inline bool ConfigProto::has_graph_options() const {
  return _internal_has_graph_options();
}
inline void ConfigProto::clear_graph_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.graph_options_ != nullptr) {
    delete _impl_.graph_options_;
  }
  _impl_.graph_options_ = nullptr;
}
inline const ::tensorflow::GraphOptions& ConfigProto::_internal_graph_options() const {
  const ::tensorflow::GraphOptions* p = _impl_.graph_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphOptions&>(
      ::tensorflow::_GraphOptions_default_instance_);
}
inline const ::tensorflow::GraphOptions& ConfigProto::graph_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.graph_options)
  return _internal_graph_options();
}
inline void ConfigProto::unsafe_arena_set_allocated_graph_options(
    ::tensorflow::GraphOptions* graph_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_options_);
  }
  _impl_.graph_options_ = graph_options;
  if (graph_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.graph_options)
}
inline ::tensorflow::GraphOptions* ConfigProto::release_graph_options() {
  
  ::tensorflow::GraphOptions* temp = _impl_.graph_options_;
  _impl_.graph_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphOptions* ConfigProto::unsafe_arena_release_graph_options() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.graph_options)
  
  ::tensorflow::GraphOptions* temp = _impl_.graph_options_;
  _impl_.graph_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphOptions* ConfigProto::_internal_mutable_graph_options() {
  
  if (_impl_.graph_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphOptions>(GetArenaForAllocation());
    _impl_.graph_options_ = p;
  }
  return _impl_.graph_options_;
}
inline ::tensorflow::GraphOptions* ConfigProto::mutable_graph_options() {
  ::tensorflow::GraphOptions* _msg = _internal_mutable_graph_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.graph_options)
  return _msg;
}
inline void ConfigProto::set_allocated_graph_options(::tensorflow::GraphOptions* graph_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.graph_options_;
  }
  if (graph_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(graph_options);
    if (message_arena != submessage_arena) {
      graph_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.graph_options_ = graph_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.graph_options)
}

// int64 operation_timeout_in_ms = 11;
inline void ConfigProto::clear_operation_timeout_in_ms() {
  _impl_.operation_timeout_in_ms_ = int64_t{0};
}
inline int64_t ConfigProto::_internal_operation_timeout_in_ms() const {
  return _impl_.operation_timeout_in_ms_;
}
inline int64_t ConfigProto::operation_timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.operation_timeout_in_ms)
  return _internal_operation_timeout_in_ms();
}
inline void ConfigProto::_internal_set_operation_timeout_in_ms(int64_t value) {
  
  _impl_.operation_timeout_in_ms_ = value;
}
inline void ConfigProto::set_operation_timeout_in_ms(int64_t value) {
  _internal_set_operation_timeout_in_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.operation_timeout_in_ms)
}

// .tensorflow.RPCOptions rpc_options = 13;
inline bool ConfigProto::_internal_has_rpc_options() const {
  return this != internal_default_instance() && _impl_.rpc_options_ != nullptr;
}
inline bool ConfigProto::has_rpc_options() const {
  return _internal_has_rpc_options();
}
inline const ::tensorflow::RPCOptions& ConfigProto::_internal_rpc_options() const {
  const ::tensorflow::RPCOptions* p = _impl_.rpc_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RPCOptions&>(
      ::tensorflow::_RPCOptions_default_instance_);
}
inline const ::tensorflow::RPCOptions& ConfigProto::rpc_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.rpc_options)
  return _internal_rpc_options();
}
inline void ConfigProto::unsafe_arena_set_allocated_rpc_options(
    ::tensorflow::RPCOptions* rpc_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.rpc_options_);
  }
  _impl_.rpc_options_ = rpc_options;
  if (rpc_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.rpc_options)
}
inline ::tensorflow::RPCOptions* ConfigProto::release_rpc_options() {
  
  ::tensorflow::RPCOptions* temp = _impl_.rpc_options_;
  _impl_.rpc_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RPCOptions* ConfigProto::unsafe_arena_release_rpc_options() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.rpc_options)
  
  ::tensorflow::RPCOptions* temp = _impl_.rpc_options_;
  _impl_.rpc_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RPCOptions* ConfigProto::_internal_mutable_rpc_options() {
  
  if (_impl_.rpc_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RPCOptions>(GetArenaForAllocation());
    _impl_.rpc_options_ = p;
  }
  return _impl_.rpc_options_;
}
inline ::tensorflow::RPCOptions* ConfigProto::mutable_rpc_options() {
  ::tensorflow::RPCOptions* _msg = _internal_mutable_rpc_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.rpc_options)
  return _msg;
}
inline void ConfigProto::set_allocated_rpc_options(::tensorflow::RPCOptions* rpc_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.rpc_options_);
  }
  if (rpc_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rpc_options));
    if (message_arena != submessage_arena) {
      rpc_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rpc_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.rpc_options_ = rpc_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.rpc_options)
}

// .tensorflow.ClusterDef cluster_def = 14;
inline bool ConfigProto::_internal_has_cluster_def() const {
  return this != internal_default_instance() && _impl_.cluster_def_ != nullptr;
}
inline bool ConfigProto::has_cluster_def() const {
  return _internal_has_cluster_def();
}
inline const ::tensorflow::ClusterDef& ConfigProto::_internal_cluster_def() const {
  const ::tensorflow::ClusterDef* p = _impl_.cluster_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ClusterDef&>(
      ::tensorflow::_ClusterDef_default_instance_);
}
inline const ::tensorflow::ClusterDef& ConfigProto::cluster_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.cluster_def)
  return _internal_cluster_def();
}
inline void ConfigProto::unsafe_arena_set_allocated_cluster_def(
    ::tensorflow::ClusterDef* cluster_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.cluster_def_);
  }
  _impl_.cluster_def_ = cluster_def;
  if (cluster_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.cluster_def)
}
inline ::tensorflow::ClusterDef* ConfigProto::release_cluster_def() {
  
  ::tensorflow::ClusterDef* temp = _impl_.cluster_def_;
  _impl_.cluster_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ClusterDef* ConfigProto::unsafe_arena_release_cluster_def() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.cluster_def)
  
  ::tensorflow::ClusterDef* temp = _impl_.cluster_def_;
  _impl_.cluster_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ClusterDef* ConfigProto::_internal_mutable_cluster_def() {
  
  if (_impl_.cluster_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ClusterDef>(GetArenaForAllocation());
    _impl_.cluster_def_ = p;
  }
  return _impl_.cluster_def_;
}
inline ::tensorflow::ClusterDef* ConfigProto::mutable_cluster_def() {
  ::tensorflow::ClusterDef* _msg = _internal_mutable_cluster_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.cluster_def)
  return _msg;
}
inline void ConfigProto::set_allocated_cluster_def(::tensorflow::ClusterDef* cluster_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.cluster_def_);
  }
  if (cluster_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cluster_def));
    if (message_arena != submessage_arena) {
      cluster_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cluster_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.cluster_def_ = cluster_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.cluster_def)
}

// bool isolate_session_state = 15;
inline void ConfigProto::clear_isolate_session_state() {
  _impl_.isolate_session_state_ = false;
}
inline bool ConfigProto::_internal_isolate_session_state() const {
  return _impl_.isolate_session_state_;
}
inline bool ConfigProto::isolate_session_state() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.isolate_session_state)
  return _internal_isolate_session_state();
}
inline void ConfigProto::_internal_set_isolate_session_state(bool value) {
  
  _impl_.isolate_session_state_ = value;
}
inline void ConfigProto::set_isolate_session_state(bool value) {
  _internal_set_isolate_session_state(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.isolate_session_state)
}

// bool share_cluster_devices_in_session = 17;
inline void ConfigProto::clear_share_cluster_devices_in_session() {
  _impl_.share_cluster_devices_in_session_ = false;
}
inline bool ConfigProto::_internal_share_cluster_devices_in_session() const {
  return _impl_.share_cluster_devices_in_session_;
}
inline bool ConfigProto::share_cluster_devices_in_session() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.share_cluster_devices_in_session)
  return _internal_share_cluster_devices_in_session();
}
inline void ConfigProto::_internal_set_share_cluster_devices_in_session(bool value) {
  
  _impl_.share_cluster_devices_in_session_ = value;
}
inline void ConfigProto::set_share_cluster_devices_in_session(bool value) {
  _internal_set_share_cluster_devices_in_session(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConfigProto.share_cluster_devices_in_session)
}

// .tensorflow.ConfigProto.Experimental experimental = 16;
inline bool ConfigProto::_internal_has_experimental() const {
  return this != internal_default_instance() && _impl_.experimental_ != nullptr;
}
inline bool ConfigProto::has_experimental() const {
  return _internal_has_experimental();
}
inline void ConfigProto::clear_experimental() {
  if (GetArenaForAllocation() == nullptr && _impl_.experimental_ != nullptr) {
    delete _impl_.experimental_;
  }
  _impl_.experimental_ = nullptr;
}
inline const ::tensorflow::ConfigProto_Experimental& ConfigProto::_internal_experimental() const {
  const ::tensorflow::ConfigProto_Experimental* p = _impl_.experimental_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ConfigProto_Experimental&>(
      ::tensorflow::_ConfigProto_Experimental_default_instance_);
}
inline const ::tensorflow::ConfigProto_Experimental& ConfigProto::experimental() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConfigProto.experimental)
  return _internal_experimental();
}
inline void ConfigProto::unsafe_arena_set_allocated_experimental(
    ::tensorflow::ConfigProto_Experimental* experimental) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.experimental_);
  }
  _impl_.experimental_ = experimental;
  if (experimental) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConfigProto.experimental)
}
inline ::tensorflow::ConfigProto_Experimental* ConfigProto::release_experimental() {
  
  ::tensorflow::ConfigProto_Experimental* temp = _impl_.experimental_;
  _impl_.experimental_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ConfigProto_Experimental* ConfigProto::unsafe_arena_release_experimental() {
  // @@protoc_insertion_point(field_release:tensorflow.ConfigProto.experimental)
  
  ::tensorflow::ConfigProto_Experimental* temp = _impl_.experimental_;
  _impl_.experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto_Experimental* ConfigProto::_internal_mutable_experimental() {
  
  if (_impl_.experimental_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ConfigProto_Experimental>(GetArenaForAllocation());
    _impl_.experimental_ = p;
  }
  return _impl_.experimental_;
}
inline ::tensorflow::ConfigProto_Experimental* ConfigProto::mutable_experimental() {
  ::tensorflow::ConfigProto_Experimental* _msg = _internal_mutable_experimental();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConfigProto.experimental)
  return _msg;
}
inline void ConfigProto::set_allocated_experimental(::tensorflow::ConfigProto_Experimental* experimental) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.experimental_;
  }
  if (experimental) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(experimental);
    if (message_arena != submessage_arena) {
      experimental = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.experimental_ = experimental;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConfigProto.experimental)
}

// -------------------------------------------------------------------

// RunOptions_Experimental_RunHandlerPoolOptions

// int64 priority = 1;
inline void RunOptions_Experimental_RunHandlerPoolOptions::clear_priority() {
  _impl_.priority_ = int64_t{0};
}
inline int64_t RunOptions_Experimental_RunHandlerPoolOptions::_internal_priority() const {
  return _impl_.priority_;
}
inline int64_t RunOptions_Experimental_RunHandlerPoolOptions::priority() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions.priority)
  return _internal_priority();
}
inline void RunOptions_Experimental_RunHandlerPoolOptions::_internal_set_priority(int64_t value) {
  
  _impl_.priority_ = value;
}
inline void RunOptions_Experimental_RunHandlerPoolOptions::set_priority(int64_t value) {
  _internal_set_priority(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.Experimental.RunHandlerPoolOptions.priority)
}

// -------------------------------------------------------------------

// RunOptions_Experimental

// int64 collective_graph_key = 1;
inline void RunOptions_Experimental::clear_collective_graph_key() {
  _impl_.collective_graph_key_ = int64_t{0};
}
inline int64_t RunOptions_Experimental::_internal_collective_graph_key() const {
  return _impl_.collective_graph_key_;
}
inline int64_t RunOptions_Experimental::collective_graph_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.Experimental.collective_graph_key)
  return _internal_collective_graph_key();
}
inline void RunOptions_Experimental::_internal_set_collective_graph_key(int64_t value) {
  
  _impl_.collective_graph_key_ = value;
}
inline void RunOptions_Experimental::set_collective_graph_key(int64_t value) {
  _internal_set_collective_graph_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.Experimental.collective_graph_key)
}

// bool use_run_handler_pool = 2;
inline void RunOptions_Experimental::clear_use_run_handler_pool() {
  _impl_.use_run_handler_pool_ = false;
}
inline bool RunOptions_Experimental::_internal_use_run_handler_pool() const {
  return _impl_.use_run_handler_pool_;
}
inline bool RunOptions_Experimental::use_run_handler_pool() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.Experimental.use_run_handler_pool)
  return _internal_use_run_handler_pool();
}
inline void RunOptions_Experimental::_internal_set_use_run_handler_pool(bool value) {
  
  _impl_.use_run_handler_pool_ = value;
}
inline void RunOptions_Experimental::set_use_run_handler_pool(bool value) {
  _internal_set_use_run_handler_pool(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.Experimental.use_run_handler_pool)
}

// .tensorflow.RunOptions.Experimental.RunHandlerPoolOptions run_handler_pool_options = 3;
inline bool RunOptions_Experimental::_internal_has_run_handler_pool_options() const {
  return this != internal_default_instance() && _impl_.run_handler_pool_options_ != nullptr;
}
inline bool RunOptions_Experimental::has_run_handler_pool_options() const {
  return _internal_has_run_handler_pool_options();
}
inline void RunOptions_Experimental::clear_run_handler_pool_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.run_handler_pool_options_ != nullptr) {
    delete _impl_.run_handler_pool_options_;
  }
  _impl_.run_handler_pool_options_ = nullptr;
}
inline const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions& RunOptions_Experimental::_internal_run_handler_pool_options() const {
  const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* p = _impl_.run_handler_pool_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions&>(
      ::tensorflow::_RunOptions_Experimental_RunHandlerPoolOptions_default_instance_);
}
inline const ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions& RunOptions_Experimental::run_handler_pool_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.Experimental.run_handler_pool_options)
  return _internal_run_handler_pool_options();
}
inline void RunOptions_Experimental::unsafe_arena_set_allocated_run_handler_pool_options(
    ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.run_handler_pool_options_);
  }
  _impl_.run_handler_pool_options_ = run_handler_pool_options;
  if (run_handler_pool_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunOptions.Experimental.run_handler_pool_options)
}
inline ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* RunOptions_Experimental::release_run_handler_pool_options() {
  
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* temp = _impl_.run_handler_pool_options_;
  _impl_.run_handler_pool_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* RunOptions_Experimental::unsafe_arena_release_run_handler_pool_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RunOptions.Experimental.run_handler_pool_options)
  
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* temp = _impl_.run_handler_pool_options_;
  _impl_.run_handler_pool_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* RunOptions_Experimental::_internal_mutable_run_handler_pool_options() {
  
  if (_impl_.run_handler_pool_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions>(GetArenaForAllocation());
    _impl_.run_handler_pool_options_ = p;
  }
  return _impl_.run_handler_pool_options_;
}
inline ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* RunOptions_Experimental::mutable_run_handler_pool_options() {
  ::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* _msg = _internal_mutable_run_handler_pool_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunOptions.Experimental.run_handler_pool_options)
  return _msg;
}
inline void RunOptions_Experimental::set_allocated_run_handler_pool_options(::tensorflow::RunOptions_Experimental_RunHandlerPoolOptions* run_handler_pool_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.run_handler_pool_options_;
  }
  if (run_handler_pool_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(run_handler_pool_options);
    if (message_arena != submessage_arena) {
      run_handler_pool_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, run_handler_pool_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.run_handler_pool_options_ = run_handler_pool_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunOptions.Experimental.run_handler_pool_options)
}

// -------------------------------------------------------------------

// RunOptions

// .tensorflow.RunOptions.TraceLevel trace_level = 1;
inline void RunOptions::clear_trace_level() {
  _impl_.trace_level_ = 0;
}
inline ::tensorflow::RunOptions_TraceLevel RunOptions::_internal_trace_level() const {
  return static_cast< ::tensorflow::RunOptions_TraceLevel >(_impl_.trace_level_);
}
inline ::tensorflow::RunOptions_TraceLevel RunOptions::trace_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.trace_level)
  return _internal_trace_level();
}
inline void RunOptions::_internal_set_trace_level(::tensorflow::RunOptions_TraceLevel value) {
  
  _impl_.trace_level_ = value;
}
inline void RunOptions::set_trace_level(::tensorflow::RunOptions_TraceLevel value) {
  _internal_set_trace_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.trace_level)
}

// int64 timeout_in_ms = 2;
inline void RunOptions::clear_timeout_in_ms() {
  _impl_.timeout_in_ms_ = int64_t{0};
}
inline int64_t RunOptions::_internal_timeout_in_ms() const {
  return _impl_.timeout_in_ms_;
}
inline int64_t RunOptions::timeout_in_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.timeout_in_ms)
  return _internal_timeout_in_ms();
}
inline void RunOptions::_internal_set_timeout_in_ms(int64_t value) {
  
  _impl_.timeout_in_ms_ = value;
}
inline void RunOptions::set_timeout_in_ms(int64_t value) {
  _internal_set_timeout_in_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.timeout_in_ms)
}

// int32 inter_op_thread_pool = 3;
inline void RunOptions::clear_inter_op_thread_pool() {
  _impl_.inter_op_thread_pool_ = 0;
}
inline int32_t RunOptions::_internal_inter_op_thread_pool() const {
  return _impl_.inter_op_thread_pool_;
}
inline int32_t RunOptions::inter_op_thread_pool() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.inter_op_thread_pool)
  return _internal_inter_op_thread_pool();
}
inline void RunOptions::_internal_set_inter_op_thread_pool(int32_t value) {
  
  _impl_.inter_op_thread_pool_ = value;
}
inline void RunOptions::set_inter_op_thread_pool(int32_t value) {
  _internal_set_inter_op_thread_pool(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.inter_op_thread_pool)
}

// bool output_partition_graphs = 5;
inline void RunOptions::clear_output_partition_graphs() {
  _impl_.output_partition_graphs_ = false;
}
inline bool RunOptions::_internal_output_partition_graphs() const {
  return _impl_.output_partition_graphs_;
}
inline bool RunOptions::output_partition_graphs() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.output_partition_graphs)
  return _internal_output_partition_graphs();
}
inline void RunOptions::_internal_set_output_partition_graphs(bool value) {
  
  _impl_.output_partition_graphs_ = value;
}
inline void RunOptions::set_output_partition_graphs(bool value) {
  _internal_set_output_partition_graphs(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.output_partition_graphs)
}

// .tensorflow.DebugOptions debug_options = 6;
inline bool RunOptions::_internal_has_debug_options() const {
  return this != internal_default_instance() && _impl_.debug_options_ != nullptr;
}
inline bool RunOptions::has_debug_options() const {
  return _internal_has_debug_options();
}
inline const ::tensorflow::DebugOptions& RunOptions::_internal_debug_options() const {
  const ::tensorflow::DebugOptions* p = _impl_.debug_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DebugOptions&>(
      ::tensorflow::_DebugOptions_default_instance_);
}
inline const ::tensorflow::DebugOptions& RunOptions::debug_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.debug_options)
  return _internal_debug_options();
}
inline void RunOptions::unsafe_arena_set_allocated_debug_options(
    ::tensorflow::DebugOptions* debug_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debug_options_);
  }
  _impl_.debug_options_ = debug_options;
  if (debug_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunOptions.debug_options)
}
inline ::tensorflow::DebugOptions* RunOptions::release_debug_options() {
  
  ::tensorflow::DebugOptions* temp = _impl_.debug_options_;
  _impl_.debug_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DebugOptions* RunOptions::unsafe_arena_release_debug_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RunOptions.debug_options)
  
  ::tensorflow::DebugOptions* temp = _impl_.debug_options_;
  _impl_.debug_options_ = nullptr;
  return temp;
}
inline ::tensorflow::DebugOptions* RunOptions::_internal_mutable_debug_options() {
  
  if (_impl_.debug_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DebugOptions>(GetArenaForAllocation());
    _impl_.debug_options_ = p;
  }
  return _impl_.debug_options_;
}
inline ::tensorflow::DebugOptions* RunOptions::mutable_debug_options() {
  ::tensorflow::DebugOptions* _msg = _internal_mutable_debug_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunOptions.debug_options)
  return _msg;
}
inline void RunOptions::set_allocated_debug_options(::tensorflow::DebugOptions* debug_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debug_options_);
  }
  if (debug_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(debug_options));
    if (message_arena != submessage_arena) {
      debug_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunOptions.debug_options)
}

// bool report_tensor_allocations_upon_oom = 7;
inline void RunOptions::clear_report_tensor_allocations_upon_oom() {
  _impl_.report_tensor_allocations_upon_oom_ = false;
}
inline bool RunOptions::_internal_report_tensor_allocations_upon_oom() const {
  return _impl_.report_tensor_allocations_upon_oom_;
}
inline bool RunOptions::report_tensor_allocations_upon_oom() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.report_tensor_allocations_upon_oom)
  return _internal_report_tensor_allocations_upon_oom();
}
inline void RunOptions::_internal_set_report_tensor_allocations_upon_oom(bool value) {
  
  _impl_.report_tensor_allocations_upon_oom_ = value;
}
inline void RunOptions::set_report_tensor_allocations_upon_oom(bool value) {
  _internal_set_report_tensor_allocations_upon_oom(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunOptions.report_tensor_allocations_upon_oom)
}

// .tensorflow.RunOptions.Experimental experimental = 8;
inline bool RunOptions::_internal_has_experimental() const {
  return this != internal_default_instance() && _impl_.experimental_ != nullptr;
}
inline bool RunOptions::has_experimental() const {
  return _internal_has_experimental();
}
inline void RunOptions::clear_experimental() {
  if (GetArenaForAllocation() == nullptr && _impl_.experimental_ != nullptr) {
    delete _impl_.experimental_;
  }
  _impl_.experimental_ = nullptr;
}
inline const ::tensorflow::RunOptions_Experimental& RunOptions::_internal_experimental() const {
  const ::tensorflow::RunOptions_Experimental* p = _impl_.experimental_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RunOptions_Experimental&>(
      ::tensorflow::_RunOptions_Experimental_default_instance_);
}
inline const ::tensorflow::RunOptions_Experimental& RunOptions::experimental() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunOptions.experimental)
  return _internal_experimental();
}
inline void RunOptions::unsafe_arena_set_allocated_experimental(
    ::tensorflow::RunOptions_Experimental* experimental) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.experimental_);
  }
  _impl_.experimental_ = experimental;
  if (experimental) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunOptions.experimental)
}
inline ::tensorflow::RunOptions_Experimental* RunOptions::release_experimental() {
  
  ::tensorflow::RunOptions_Experimental* temp = _impl_.experimental_;
  _impl_.experimental_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RunOptions_Experimental* RunOptions::unsafe_arena_release_experimental() {
  // @@protoc_insertion_point(field_release:tensorflow.RunOptions.experimental)
  
  ::tensorflow::RunOptions_Experimental* temp = _impl_.experimental_;
  _impl_.experimental_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions_Experimental* RunOptions::_internal_mutable_experimental() {
  
  if (_impl_.experimental_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions_Experimental>(GetArenaForAllocation());
    _impl_.experimental_ = p;
  }
  return _impl_.experimental_;
}
inline ::tensorflow::RunOptions_Experimental* RunOptions::mutable_experimental() {
  ::tensorflow::RunOptions_Experimental* _msg = _internal_mutable_experimental();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunOptions.experimental)
  return _msg;
}
inline void RunOptions::set_allocated_experimental(::tensorflow::RunOptions_Experimental* experimental) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.experimental_;
  }
  if (experimental) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(experimental);
    if (message_arena != submessage_arena) {
      experimental = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.experimental_ = experimental;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunOptions.experimental)
}

// -------------------------------------------------------------------

// RunMetadata_FunctionGraphs

// repeated .tensorflow.GraphDef partition_graphs = 1;
inline int RunMetadata_FunctionGraphs::_internal_partition_graphs_size() const {
  return _impl_.partition_graphs_.size();
}
inline int RunMetadata_FunctionGraphs::partition_graphs_size() const {
  return _internal_partition_graphs_size();
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::mutable_partition_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return _impl_.partition_graphs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
RunMetadata_FunctionGraphs::mutable_partition_graphs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return &_impl_.partition_graphs_;
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::_internal_partition_graphs(int index) const {
  return _impl_.partition_graphs_.Get(index);
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::partition_graphs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return _internal_partition_graphs(index);
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::_internal_add_partition_graphs() {
  return _impl_.partition_graphs_.Add();
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::add_partition_graphs() {
  ::tensorflow::GraphDef* _add = _internal_add_partition_graphs();
  // @@protoc_insertion_point(field_add:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
RunMetadata_FunctionGraphs::partition_graphs() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunMetadata.FunctionGraphs.partition_graphs)
  return _impl_.partition_graphs_;
}

// .tensorflow.GraphDef pre_optimization_graph = 2;
inline bool RunMetadata_FunctionGraphs::_internal_has_pre_optimization_graph() const {
  return this != internal_default_instance() && _impl_.pre_optimization_graph_ != nullptr;
}
inline bool RunMetadata_FunctionGraphs::has_pre_optimization_graph() const {
  return _internal_has_pre_optimization_graph();
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::_internal_pre_optimization_graph() const {
  const ::tensorflow::GraphDef* p = _impl_.pre_optimization_graph_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphDef&>(
      ::tensorflow::_GraphDef_default_instance_);
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::pre_optimization_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
  return _internal_pre_optimization_graph();
}
inline void RunMetadata_FunctionGraphs::unsafe_arena_set_allocated_pre_optimization_graph(
    ::tensorflow::GraphDef* pre_optimization_graph) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.pre_optimization_graph_);
  }
  _impl_.pre_optimization_graph_ = pre_optimization_graph;
  if (pre_optimization_graph) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::release_pre_optimization_graph() {
  
  ::tensorflow::GraphDef* temp = _impl_.pre_optimization_graph_;
  _impl_.pre_optimization_graph_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::unsafe_arena_release_pre_optimization_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
  
  ::tensorflow::GraphDef* temp = _impl_.pre_optimization_graph_;
  _impl_.pre_optimization_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::_internal_mutable_pre_optimization_graph() {
  
  if (_impl_.pre_optimization_graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaForAllocation());
    _impl_.pre_optimization_graph_ = p;
  }
  return _impl_.pre_optimization_graph_;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::mutable_pre_optimization_graph() {
  ::tensorflow::GraphDef* _msg = _internal_mutable_pre_optimization_graph();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
  return _msg;
}
inline void RunMetadata_FunctionGraphs::set_allocated_pre_optimization_graph(::tensorflow::GraphDef* pre_optimization_graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.pre_optimization_graph_);
  }
  if (pre_optimization_graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(pre_optimization_graph));
    if (message_arena != submessage_arena) {
      pre_optimization_graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pre_optimization_graph, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.pre_optimization_graph_ = pre_optimization_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.FunctionGraphs.pre_optimization_graph)
}

// .tensorflow.GraphDef post_optimization_graph = 3;
inline bool RunMetadata_FunctionGraphs::_internal_has_post_optimization_graph() const {
  return this != internal_default_instance() && _impl_.post_optimization_graph_ != nullptr;
}
inline bool RunMetadata_FunctionGraphs::has_post_optimization_graph() const {
  return _internal_has_post_optimization_graph();
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::_internal_post_optimization_graph() const {
  const ::tensorflow::GraphDef* p = _impl_.post_optimization_graph_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphDef&>(
      ::tensorflow::_GraphDef_default_instance_);
}
inline const ::tensorflow::GraphDef& RunMetadata_FunctionGraphs::post_optimization_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
  return _internal_post_optimization_graph();
}
inline void RunMetadata_FunctionGraphs::unsafe_arena_set_allocated_post_optimization_graph(
    ::tensorflow::GraphDef* post_optimization_graph) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.post_optimization_graph_);
  }
  _impl_.post_optimization_graph_ = post_optimization_graph;
  if (post_optimization_graph) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::release_post_optimization_graph() {
  
  ::tensorflow::GraphDef* temp = _impl_.post_optimization_graph_;
  _impl_.post_optimization_graph_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::unsafe_arena_release_post_optimization_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
  
  ::tensorflow::GraphDef* temp = _impl_.post_optimization_graph_;
  _impl_.post_optimization_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::_internal_mutable_post_optimization_graph() {
  
  if (_impl_.post_optimization_graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaForAllocation());
    _impl_.post_optimization_graph_ = p;
  }
  return _impl_.post_optimization_graph_;
}
inline ::tensorflow::GraphDef* RunMetadata_FunctionGraphs::mutable_post_optimization_graph() {
  ::tensorflow::GraphDef* _msg = _internal_mutable_post_optimization_graph();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
  return _msg;
}
inline void RunMetadata_FunctionGraphs::set_allocated_post_optimization_graph(::tensorflow::GraphDef* post_optimization_graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.post_optimization_graph_);
  }
  if (post_optimization_graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(post_optimization_graph));
    if (message_arena != submessage_arena) {
      post_optimization_graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, post_optimization_graph, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.post_optimization_graph_ = post_optimization_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.FunctionGraphs.post_optimization_graph)
}

// -------------------------------------------------------------------

// RunMetadata

// .tensorflow.StepStats step_stats = 1;
inline bool RunMetadata::_internal_has_step_stats() const {
  return this != internal_default_instance() && _impl_.step_stats_ != nullptr;
}
inline bool RunMetadata::has_step_stats() const {
  return _internal_has_step_stats();
}
inline const ::tensorflow::StepStats& RunMetadata::_internal_step_stats() const {
  const ::tensorflow::StepStats* p = _impl_.step_stats_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StepStats&>(
      ::tensorflow::_StepStats_default_instance_);
}
inline const ::tensorflow::StepStats& RunMetadata::step_stats() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.step_stats)
  return _internal_step_stats();
}
inline void RunMetadata::unsafe_arena_set_allocated_step_stats(
    ::tensorflow::StepStats* step_stats) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.step_stats_);
  }
  _impl_.step_stats_ = step_stats;
  if (step_stats) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.step_stats)
}
inline ::tensorflow::StepStats* RunMetadata::release_step_stats() {
  
  ::tensorflow::StepStats* temp = _impl_.step_stats_;
  _impl_.step_stats_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StepStats* RunMetadata::unsafe_arena_release_step_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.step_stats)
  
  ::tensorflow::StepStats* temp = _impl_.step_stats_;
  _impl_.step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* RunMetadata::_internal_mutable_step_stats() {
  
  if (_impl_.step_stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StepStats>(GetArenaForAllocation());
    _impl_.step_stats_ = p;
  }
  return _impl_.step_stats_;
}
inline ::tensorflow::StepStats* RunMetadata::mutable_step_stats() {
  ::tensorflow::StepStats* _msg = _internal_mutable_step_stats();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.step_stats)
  return _msg;
}
inline void RunMetadata::set_allocated_step_stats(::tensorflow::StepStats* step_stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.step_stats_);
  }
  if (step_stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats));
    if (message_arena != submessage_arena) {
      step_stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, step_stats, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.step_stats_ = step_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.step_stats)
}

// .tensorflow.CostGraphDef cost_graph = 2;
inline bool RunMetadata::_internal_has_cost_graph() const {
  return this != internal_default_instance() && _impl_.cost_graph_ != nullptr;
}
inline bool RunMetadata::has_cost_graph() const {
  return _internal_has_cost_graph();
}
inline const ::tensorflow::CostGraphDef& RunMetadata::_internal_cost_graph() const {
  const ::tensorflow::CostGraphDef* p = _impl_.cost_graph_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CostGraphDef&>(
      ::tensorflow::_CostGraphDef_default_instance_);
}
inline const ::tensorflow::CostGraphDef& RunMetadata::cost_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.cost_graph)
  return _internal_cost_graph();
}
inline void RunMetadata::unsafe_arena_set_allocated_cost_graph(
    ::tensorflow::CostGraphDef* cost_graph) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.cost_graph_);
  }
  _impl_.cost_graph_ = cost_graph;
  if (cost_graph) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.cost_graph)
}
inline ::tensorflow::CostGraphDef* RunMetadata::release_cost_graph() {
  
  ::tensorflow::CostGraphDef* temp = _impl_.cost_graph_;
  _impl_.cost_graph_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CostGraphDef* RunMetadata::unsafe_arena_release_cost_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.cost_graph)
  
  ::tensorflow::CostGraphDef* temp = _impl_.cost_graph_;
  _impl_.cost_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::CostGraphDef* RunMetadata::_internal_mutable_cost_graph() {
  
  if (_impl_.cost_graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CostGraphDef>(GetArenaForAllocation());
    _impl_.cost_graph_ = p;
  }
  return _impl_.cost_graph_;
}
inline ::tensorflow::CostGraphDef* RunMetadata::mutable_cost_graph() {
  ::tensorflow::CostGraphDef* _msg = _internal_mutable_cost_graph();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.cost_graph)
  return _msg;
}
inline void RunMetadata::set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.cost_graph_);
  }
  if (cost_graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cost_graph));
    if (message_arena != submessage_arena) {
      cost_graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cost_graph, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.cost_graph_ = cost_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.cost_graph)
}

// repeated .tensorflow.GraphDef partition_graphs = 3;
inline int RunMetadata::_internal_partition_graphs_size() const {
  return _impl_.partition_graphs_.size();
}
inline int RunMetadata::partition_graphs_size() const {
  return _internal_partition_graphs_size();
}
inline ::tensorflow::GraphDef* RunMetadata::mutable_partition_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.partition_graphs)
  return _impl_.partition_graphs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
RunMetadata::mutable_partition_graphs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunMetadata.partition_graphs)
  return &_impl_.partition_graphs_;
}
inline const ::tensorflow::GraphDef& RunMetadata::_internal_partition_graphs(int index) const {
  return _impl_.partition_graphs_.Get(index);
}
inline const ::tensorflow::GraphDef& RunMetadata::partition_graphs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.partition_graphs)
  return _internal_partition_graphs(index);
}
inline ::tensorflow::GraphDef* RunMetadata::_internal_add_partition_graphs() {
  return _impl_.partition_graphs_.Add();
}
inline ::tensorflow::GraphDef* RunMetadata::add_partition_graphs() {
  ::tensorflow::GraphDef* _add = _internal_add_partition_graphs();
  // @@protoc_insertion_point(field_add:tensorflow.RunMetadata.partition_graphs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
RunMetadata::partition_graphs() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunMetadata.partition_graphs)
  return _impl_.partition_graphs_;
}

// repeated .tensorflow.RunMetadata.FunctionGraphs function_graphs = 4;
inline int RunMetadata::_internal_function_graphs_size() const {
  return _impl_.function_graphs_.size();
}
inline int RunMetadata::function_graphs_size() const {
  return _internal_function_graphs_size();
}
inline void RunMetadata::clear_function_graphs() {
  _impl_.function_graphs_.Clear();
}
inline ::tensorflow::RunMetadata_FunctionGraphs* RunMetadata::mutable_function_graphs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.function_graphs)
  return _impl_.function_graphs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs >*
RunMetadata::mutable_function_graphs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunMetadata.function_graphs)
  return &_impl_.function_graphs_;
}
inline const ::tensorflow::RunMetadata_FunctionGraphs& RunMetadata::_internal_function_graphs(int index) const {
  return _impl_.function_graphs_.Get(index);
}
inline const ::tensorflow::RunMetadata_FunctionGraphs& RunMetadata::function_graphs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.function_graphs)
  return _internal_function_graphs(index);
}
inline ::tensorflow::RunMetadata_FunctionGraphs* RunMetadata::_internal_add_function_graphs() {
  return _impl_.function_graphs_.Add();
}
inline ::tensorflow::RunMetadata_FunctionGraphs* RunMetadata::add_function_graphs() {
  ::tensorflow::RunMetadata_FunctionGraphs* _add = _internal_add_function_graphs();
  // @@protoc_insertion_point(field_add:tensorflow.RunMetadata.function_graphs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::RunMetadata_FunctionGraphs >&
RunMetadata::function_graphs() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunMetadata.function_graphs)
  return _impl_.function_graphs_;
}

// .tensorflow.SessionMetadata session_metadata = 5;
inline bool RunMetadata::_internal_has_session_metadata() const {
  return this != internal_default_instance() && _impl_.session_metadata_ != nullptr;
}
inline bool RunMetadata::has_session_metadata() const {
  return _internal_has_session_metadata();
}
inline void RunMetadata::clear_session_metadata() {
  if (GetArenaForAllocation() == nullptr && _impl_.session_metadata_ != nullptr) {
    delete _impl_.session_metadata_;
  }
  _impl_.session_metadata_ = nullptr;
}
inline const ::tensorflow::SessionMetadata& RunMetadata::_internal_session_metadata() const {
  const ::tensorflow::SessionMetadata* p = _impl_.session_metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SessionMetadata&>(
      ::tensorflow::_SessionMetadata_default_instance_);
}
inline const ::tensorflow::SessionMetadata& RunMetadata::session_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunMetadata.session_metadata)
  return _internal_session_metadata();
}
inline void RunMetadata::unsafe_arena_set_allocated_session_metadata(
    ::tensorflow::SessionMetadata* session_metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.session_metadata_);
  }
  _impl_.session_metadata_ = session_metadata;
  if (session_metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunMetadata.session_metadata)
}
inline ::tensorflow::SessionMetadata* RunMetadata::release_session_metadata() {
  
  ::tensorflow::SessionMetadata* temp = _impl_.session_metadata_;
  _impl_.session_metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SessionMetadata* RunMetadata::unsafe_arena_release_session_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.RunMetadata.session_metadata)
  
  ::tensorflow::SessionMetadata* temp = _impl_.session_metadata_;
  _impl_.session_metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::SessionMetadata* RunMetadata::_internal_mutable_session_metadata() {
  
  if (_impl_.session_metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SessionMetadata>(GetArenaForAllocation());
    _impl_.session_metadata_ = p;
  }
  return _impl_.session_metadata_;
}
inline ::tensorflow::SessionMetadata* RunMetadata::mutable_session_metadata() {
  ::tensorflow::SessionMetadata* _msg = _internal_mutable_session_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunMetadata.session_metadata)
  return _msg;
}
inline void RunMetadata::set_allocated_session_metadata(::tensorflow::SessionMetadata* session_metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.session_metadata_;
  }
  if (session_metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(session_metadata);
    if (message_arena != submessage_arena) {
      session_metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, session_metadata, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.session_metadata_ = session_metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunMetadata.session_metadata)
}

// -------------------------------------------------------------------

// TensorConnection

// string from_tensor = 1;
inline void TensorConnection::clear_from_tensor() {
  _impl_.from_tensor_.ClearToEmpty();
}
inline const std::string& TensorConnection::from_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorConnection.from_tensor)
  return _internal_from_tensor();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorConnection::set_from_tensor(ArgT0&& arg0, ArgT... args) {
 
 _impl_.from_tensor_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorConnection.from_tensor)
}
inline std::string* TensorConnection::mutable_from_tensor() {
  std::string* _s = _internal_mutable_from_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorConnection.from_tensor)
  return _s;
}
inline const std::string& TensorConnection::_internal_from_tensor() const {
  return _impl_.from_tensor_.Get();
}
inline void TensorConnection::_internal_set_from_tensor(const std::string& value) {
  
  _impl_.from_tensor_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorConnection::_internal_mutable_from_tensor() {
  
  return _impl_.from_tensor_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorConnection::release_from_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorConnection.from_tensor)
  return _impl_.from_tensor_.Release();
}
inline void TensorConnection::set_allocated_from_tensor(std::string* from_tensor) {
  if (from_tensor != nullptr) {
    
  } else {
    
  }
  _impl_.from_tensor_.SetAllocated(from_tensor, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.from_tensor_.IsDefault()) {
    _impl_.from_tensor_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorConnection.from_tensor)
}

// string to_tensor = 2;
inline void TensorConnection::clear_to_tensor() {
  _impl_.to_tensor_.ClearToEmpty();
}
inline const std::string& TensorConnection::to_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorConnection.to_tensor)
  return _internal_to_tensor();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorConnection::set_to_tensor(ArgT0&& arg0, ArgT... args) {
 
 _impl_.to_tensor_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorConnection.to_tensor)
}
inline std::string* TensorConnection::mutable_to_tensor() {
  std::string* _s = _internal_mutable_to_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorConnection.to_tensor)
  return _s;
}
inline const std::string& TensorConnection::_internal_to_tensor() const {
  return _impl_.to_tensor_.Get();
}
inline void TensorConnection::_internal_set_to_tensor(const std::string& value) {
  
  _impl_.to_tensor_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorConnection::_internal_mutable_to_tensor() {
  
  return _impl_.to_tensor_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorConnection::release_to_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorConnection.to_tensor)
  return _impl_.to_tensor_.Release();
}
inline void TensorConnection::set_allocated_to_tensor(std::string* to_tensor) {
  if (to_tensor != nullptr) {
    
  } else {
    
  }
  _impl_.to_tensor_.SetAllocated(to_tensor, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.to_tensor_.IsDefault()) {
    _impl_.to_tensor_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorConnection.to_tensor)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CallableOptions

// repeated string feed = 1;
inline int CallableOptions::_internal_feed_size() const {
  return _impl_.feed_.size();
}
inline int CallableOptions::feed_size() const {
  return _internal_feed_size();
}
inline void CallableOptions::clear_feed() {
  _impl_.feed_.Clear();
}
inline std::string* CallableOptions::add_feed() {
  std::string* _s = _internal_add_feed();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CallableOptions.feed)
  return _s;
}
inline const std::string& CallableOptions::_internal_feed(int index) const {
  return _impl_.feed_.Get(index);
}
inline const std::string& CallableOptions::feed(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.feed)
  return _internal_feed(index);
}
inline std::string* CallableOptions::mutable_feed(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.feed)
  return _impl_.feed_.Mutable(index);
}
inline void CallableOptions::set_feed(int index, const std::string& value) {
  _impl_.feed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::set_feed(int index, std::string&& value) {
  _impl_.feed_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::set_feed(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.feed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::set_feed(int index, const char* value, size_t size) {
  _impl_.feed_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CallableOptions.feed)
}
inline std::string* CallableOptions::_internal_add_feed() {
  return _impl_.feed_.Add();
}
inline void CallableOptions::add_feed(const std::string& value) {
  _impl_.feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::add_feed(std::string&& value) {
  _impl_.feed_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::add_feed(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.feed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CallableOptions.feed)
}
inline void CallableOptions::add_feed(const char* value, size_t size) {
  _impl_.feed_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CallableOptions.feed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CallableOptions::feed() const {
  // @@protoc_insertion_point(field_list:tensorflow.CallableOptions.feed)
  return _impl_.feed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CallableOptions::mutable_feed() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CallableOptions.feed)
  return &_impl_.feed_;
}

// repeated string fetch = 2;
inline int CallableOptions::_internal_fetch_size() const {
  return _impl_.fetch_.size();
}
inline int CallableOptions::fetch_size() const {
  return _internal_fetch_size();
}
inline void CallableOptions::clear_fetch() {
  _impl_.fetch_.Clear();
}
inline std::string* CallableOptions::add_fetch() {
  std::string* _s = _internal_add_fetch();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CallableOptions.fetch)
  return _s;
}
inline const std::string& CallableOptions::_internal_fetch(int index) const {
  return _impl_.fetch_.Get(index);
}
inline const std::string& CallableOptions::fetch(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.fetch)
  return _internal_fetch(index);
}
inline std::string* CallableOptions::mutable_fetch(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.fetch)
  return _impl_.fetch_.Mutable(index);
}
inline void CallableOptions::set_fetch(int index, const std::string& value) {
  _impl_.fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::set_fetch(int index, std::string&& value) {
  _impl_.fetch_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::set_fetch(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.fetch_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::set_fetch(int index, const char* value, size_t size) {
  _impl_.fetch_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CallableOptions.fetch)
}
inline std::string* CallableOptions::_internal_add_fetch() {
  return _impl_.fetch_.Add();
}
inline void CallableOptions::add_fetch(const std::string& value) {
  _impl_.fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::add_fetch(std::string&& value) {
  _impl_.fetch_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::add_fetch(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.fetch_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CallableOptions.fetch)
}
inline void CallableOptions::add_fetch(const char* value, size_t size) {
  _impl_.fetch_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CallableOptions.fetch)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CallableOptions::fetch() const {
  // @@protoc_insertion_point(field_list:tensorflow.CallableOptions.fetch)
  return _impl_.fetch_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CallableOptions::mutable_fetch() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CallableOptions.fetch)
  return &_impl_.fetch_;
}

// repeated string target = 3;
inline int CallableOptions::_internal_target_size() const {
  return _impl_.target_.size();
}
inline int CallableOptions::target_size() const {
  return _internal_target_size();
}
inline void CallableOptions::clear_target() {
  _impl_.target_.Clear();
}
inline std::string* CallableOptions::add_target() {
  std::string* _s = _internal_add_target();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CallableOptions.target)
  return _s;
}
inline const std::string& CallableOptions::_internal_target(int index) const {
  return _impl_.target_.Get(index);
}
inline const std::string& CallableOptions::target(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.target)
  return _internal_target(index);
}
inline std::string* CallableOptions::mutable_target(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.target)
  return _impl_.target_.Mutable(index);
}
inline void CallableOptions::set_target(int index, const std::string& value) {
  _impl_.target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.target)
}
inline void CallableOptions::set_target(int index, std::string&& value) {
  _impl_.target_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.target)
}
inline void CallableOptions::set_target(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.target_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CallableOptions.target)
}
inline void CallableOptions::set_target(int index, const char* value, size_t size) {
  _impl_.target_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CallableOptions.target)
}
inline std::string* CallableOptions::_internal_add_target() {
  return _impl_.target_.Add();
}
inline void CallableOptions::add_target(const std::string& value) {
  _impl_.target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.target)
}
inline void CallableOptions::add_target(std::string&& value) {
  _impl_.target_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.target)
}
inline void CallableOptions::add_target(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.target_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CallableOptions.target)
}
inline void CallableOptions::add_target(const char* value, size_t size) {
  _impl_.target_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CallableOptions.target)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CallableOptions::target() const {
  // @@protoc_insertion_point(field_list:tensorflow.CallableOptions.target)
  return _impl_.target_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CallableOptions::mutable_target() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CallableOptions.target)
  return &_impl_.target_;
}

// .tensorflow.RunOptions run_options = 4;
inline bool CallableOptions::_internal_has_run_options() const {
  return this != internal_default_instance() && _impl_.run_options_ != nullptr;
}
inline bool CallableOptions::has_run_options() const {
  return _internal_has_run_options();
}
inline void CallableOptions::clear_run_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.run_options_ != nullptr) {
    delete _impl_.run_options_;
  }
  _impl_.run_options_ = nullptr;
}
inline const ::tensorflow::RunOptions& CallableOptions::_internal_run_options() const {
  const ::tensorflow::RunOptions* p = _impl_.run_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RunOptions&>(
      ::tensorflow::_RunOptions_default_instance_);
}
inline const ::tensorflow::RunOptions& CallableOptions::run_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.run_options)
  return _internal_run_options();
}
inline void CallableOptions::unsafe_arena_set_allocated_run_options(
    ::tensorflow::RunOptions* run_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.run_options_);
  }
  _impl_.run_options_ = run_options;
  if (run_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CallableOptions.run_options)
}
inline ::tensorflow::RunOptions* CallableOptions::release_run_options() {
  
  ::tensorflow::RunOptions* temp = _impl_.run_options_;
  _impl_.run_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RunOptions* CallableOptions::unsafe_arena_release_run_options() {
  // @@protoc_insertion_point(field_release:tensorflow.CallableOptions.run_options)
  
  ::tensorflow::RunOptions* temp = _impl_.run_options_;
  _impl_.run_options_ = nullptr;
  return temp;
}
inline ::tensorflow::RunOptions* CallableOptions::_internal_mutable_run_options() {
  
  if (_impl_.run_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunOptions>(GetArenaForAllocation());
    _impl_.run_options_ = p;
  }
  return _impl_.run_options_;
}
inline ::tensorflow::RunOptions* CallableOptions::mutable_run_options() {
  ::tensorflow::RunOptions* _msg = _internal_mutable_run_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.run_options)
  return _msg;
}
inline void CallableOptions::set_allocated_run_options(::tensorflow::RunOptions* run_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.run_options_;
  }
  if (run_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(run_options);
    if (message_arena != submessage_arena) {
      run_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, run_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.run_options_ = run_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CallableOptions.run_options)
}

// repeated .tensorflow.TensorConnection tensor_connection = 5;
inline int CallableOptions::_internal_tensor_connection_size() const {
  return _impl_.tensor_connection_.size();
}
inline int CallableOptions::tensor_connection_size() const {
  return _internal_tensor_connection_size();
}
inline void CallableOptions::clear_tensor_connection() {
  _impl_.tensor_connection_.Clear();
}
inline ::tensorflow::TensorConnection* CallableOptions::mutable_tensor_connection(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CallableOptions.tensor_connection)
  return _impl_.tensor_connection_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection >*
CallableOptions::mutable_tensor_connection() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CallableOptions.tensor_connection)
  return &_impl_.tensor_connection_;
}
inline const ::tensorflow::TensorConnection& CallableOptions::_internal_tensor_connection(int index) const {
  return _impl_.tensor_connection_.Get(index);
}
inline const ::tensorflow::TensorConnection& CallableOptions::tensor_connection(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.tensor_connection)
  return _internal_tensor_connection(index);
}
inline ::tensorflow::TensorConnection* CallableOptions::_internal_add_tensor_connection() {
  return _impl_.tensor_connection_.Add();
}
inline ::tensorflow::TensorConnection* CallableOptions::add_tensor_connection() {
  ::tensorflow::TensorConnection* _add = _internal_add_tensor_connection();
  // @@protoc_insertion_point(field_add:tensorflow.CallableOptions.tensor_connection)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorConnection >&
CallableOptions::tensor_connection() const {
  // @@protoc_insertion_point(field_list:tensorflow.CallableOptions.tensor_connection)
  return _impl_.tensor_connection_;
}

// map<string, string> feed_devices = 6;
inline int CallableOptions::_internal_feed_devices_size() const {
  return _impl_.feed_devices_.size();
}
inline int CallableOptions::feed_devices_size() const {
  return _internal_feed_devices_size();
}
inline void CallableOptions::clear_feed_devices() {
  _impl_.feed_devices_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
CallableOptions::_internal_feed_devices() const {
  return _impl_.feed_devices_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
CallableOptions::feed_devices() const {
  // @@protoc_insertion_point(field_map:tensorflow.CallableOptions.feed_devices)
  return _internal_feed_devices();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
CallableOptions::_internal_mutable_feed_devices() {
  return _impl_.feed_devices_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
CallableOptions::mutable_feed_devices() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CallableOptions.feed_devices)
  return _internal_mutable_feed_devices();
}

// map<string, string> fetch_devices = 7;
inline int CallableOptions::_internal_fetch_devices_size() const {
  return _impl_.fetch_devices_.size();
}
inline int CallableOptions::fetch_devices_size() const {
  return _internal_fetch_devices_size();
}
inline void CallableOptions::clear_fetch_devices() {
  _impl_.fetch_devices_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
CallableOptions::_internal_fetch_devices() const {
  return _impl_.fetch_devices_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
CallableOptions::fetch_devices() const {
  // @@protoc_insertion_point(field_map:tensorflow.CallableOptions.fetch_devices)
  return _internal_fetch_devices();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
CallableOptions::_internal_mutable_fetch_devices() {
  return _impl_.fetch_devices_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
CallableOptions::mutable_fetch_devices() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CallableOptions.fetch_devices)
  return _internal_mutable_fetch_devices();
}

// bool fetch_skip_sync = 8;
inline void CallableOptions::clear_fetch_skip_sync() {
  _impl_.fetch_skip_sync_ = false;
}
inline bool CallableOptions::_internal_fetch_skip_sync() const {
  return _impl_.fetch_skip_sync_;
}
inline bool CallableOptions::fetch_skip_sync() const {
  // @@protoc_insertion_point(field_get:tensorflow.CallableOptions.fetch_skip_sync)
  return _internal_fetch_skip_sync();
}
inline void CallableOptions::_internal_set_fetch_skip_sync(bool value) {
  
  _impl_.fetch_skip_sync_ = value;
}
inline void CallableOptions::set_fetch_skip_sync(bool value) {
  _internal_set_fetch_skip_sync(value);
  // @@protoc_insertion_point(field_set:tensorflow.CallableOptions.fetch_skip_sync)
}

// -------------------------------------------------------------------

// BatchingOptions

// int32 num_batch_threads = 1;
inline void BatchingOptions::clear_num_batch_threads() {
  _impl_.num_batch_threads_ = 0;
}
inline int32_t BatchingOptions::_internal_num_batch_threads() const {
  return _impl_.num_batch_threads_;
}
inline int32_t BatchingOptions::num_batch_threads() const {
  // @@protoc_insertion_point(field_get:tensorflow.BatchingOptions.num_batch_threads)
  return _internal_num_batch_threads();
}
inline void BatchingOptions::_internal_set_num_batch_threads(int32_t value) {
  
  _impl_.num_batch_threads_ = value;
}
inline void BatchingOptions::set_num_batch_threads(int32_t value) {
  _internal_set_num_batch_threads(value);
  // @@protoc_insertion_point(field_set:tensorflow.BatchingOptions.num_batch_threads)
}

// int32 max_batch_size = 2;
inline void BatchingOptions::clear_max_batch_size() {
  _impl_.max_batch_size_ = 0;
}
inline int32_t BatchingOptions::_internal_max_batch_size() const {
  return _impl_.max_batch_size_;
}
inline int32_t BatchingOptions::max_batch_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.BatchingOptions.max_batch_size)
  return _internal_max_batch_size();
}
inline void BatchingOptions::_internal_set_max_batch_size(int32_t value) {
  
  _impl_.max_batch_size_ = value;
}
inline void BatchingOptions::set_max_batch_size(int32_t value) {
  _internal_set_max_batch_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.BatchingOptions.max_batch_size)
}

// int32 batch_timeout_micros = 3;
inline void BatchingOptions::clear_batch_timeout_micros() {
  _impl_.batch_timeout_micros_ = 0;
}
inline int32_t BatchingOptions::_internal_batch_timeout_micros() const {
  return _impl_.batch_timeout_micros_;
}
inline int32_t BatchingOptions::batch_timeout_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.BatchingOptions.batch_timeout_micros)
  return _internal_batch_timeout_micros();
}
inline void BatchingOptions::_internal_set_batch_timeout_micros(int32_t value) {
  
  _impl_.batch_timeout_micros_ = value;
}
inline void BatchingOptions::set_batch_timeout_micros(int32_t value) {
  _internal_set_batch_timeout_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.BatchingOptions.batch_timeout_micros)
}

// repeated int32 allowed_batch_sizes = 4;
inline int BatchingOptions::_internal_allowed_batch_sizes_size() const {
  return _impl_.allowed_batch_sizes_.size();
}
inline int BatchingOptions::allowed_batch_sizes_size() const {
  return _internal_allowed_batch_sizes_size();
}
inline void BatchingOptions::clear_allowed_batch_sizes() {
  _impl_.allowed_batch_sizes_.Clear();
}
inline int32_t BatchingOptions::_internal_allowed_batch_sizes(int index) const {
  return _impl_.allowed_batch_sizes_.Get(index);
}
inline int32_t BatchingOptions::allowed_batch_sizes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BatchingOptions.allowed_batch_sizes)
  return _internal_allowed_batch_sizes(index);
}
inline void BatchingOptions::set_allowed_batch_sizes(int index, int32_t value) {
  _impl_.allowed_batch_sizes_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.BatchingOptions.allowed_batch_sizes)
}
inline void BatchingOptions::_internal_add_allowed_batch_sizes(int32_t value) {
  _impl_.allowed_batch_sizes_.Add(value);
}
inline void BatchingOptions::add_allowed_batch_sizes(int32_t value) {
  _internal_add_allowed_batch_sizes(value);
  // @@protoc_insertion_point(field_add:tensorflow.BatchingOptions.allowed_batch_sizes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
BatchingOptions::_internal_allowed_batch_sizes() const {
  return _impl_.allowed_batch_sizes_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
BatchingOptions::allowed_batch_sizes() const {
  // @@protoc_insertion_point(field_list:tensorflow.BatchingOptions.allowed_batch_sizes)
  return _internal_allowed_batch_sizes();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
BatchingOptions::_internal_mutable_allowed_batch_sizes() {
  return &_impl_.allowed_batch_sizes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
BatchingOptions::mutable_allowed_batch_sizes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BatchingOptions.allowed_batch_sizes)
  return _internal_mutable_allowed_batch_sizes();
}

// int32 max_enqueued_batches = 5;
inline void BatchingOptions::clear_max_enqueued_batches() {
  _impl_.max_enqueued_batches_ = 0;
}
inline int32_t BatchingOptions::_internal_max_enqueued_batches() const {
  return _impl_.max_enqueued_batches_;
}
inline int32_t BatchingOptions::max_enqueued_batches() const {
  // @@protoc_insertion_point(field_get:tensorflow.BatchingOptions.max_enqueued_batches)
  return _internal_max_enqueued_batches();
}
inline void BatchingOptions::_internal_set_max_enqueued_batches(int32_t value) {
  
  _impl_.max_enqueued_batches_ = value;
}
inline void BatchingOptions::set_max_enqueued_batches(int32_t value) {
  _internal_set_max_enqueued_batches(value);
  // @@protoc_insertion_point(field_set:tensorflow.BatchingOptions.max_enqueued_batches)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::OptimizerOptions_Level> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::OptimizerOptions_Level>() {
  return ::tensorflow::OptimizerOptions_Level_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::OptimizerOptions_GlobalJitLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::OptimizerOptions_GlobalJitLevel>() {
  return ::tensorflow::OptimizerOptions_GlobalJitLevel_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout>() {
  return ::tensorflow::ConfigProto_Experimental_MlirBridgeRollout_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::RunOptions_TraceLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::RunOptions_TraceLevel>() {
  return ::tensorflow::RunOptions_TraceLevel_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconfig_2eproto
