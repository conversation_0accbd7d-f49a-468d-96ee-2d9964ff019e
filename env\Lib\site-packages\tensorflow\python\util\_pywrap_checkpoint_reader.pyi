# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

class CheckpointReader:
    def __init__(self, arg0: str) -> None: ...
    @staticmethod
    def CheckpointReader_GetTensor(arg0: CheckpointReader, arg1: str) -> object: ...
    def debug_string(self) -> bytes: ...
    def get_variable_to_shape_map(self, *args, **kwargs): ...
