/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

cpp_quote("#include <winapifamily.h>")

import "mfobjects.idl";
import "mfidl.idl";

cpp_quote("#if WINVER >= _WIN32_WINNT_WIN7")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

cpp_quote("#ifndef _MFVideoNormalizedRect_")
cpp_quote("#define _MFVideoNormalizedRect_")
typedef struct MFVideoNormalizedRect {
  float left;
  float top;
  float right;
  float bottom;
} MFVideoNormalizedRect;
cpp_quote("#endif")

typedef enum MF_CAPTURE_ENGINE_DEVICE_TYPE {
  MF_CAPTURE_ENGINE_DEVICE_TYPE_AUDIO = 0x00000000,
  MF_CAPTURE_ENGINE_DEVICE_TYPE_VIDEO = 0x00000001,
} MF_CAPTURE_ENGINE_DEVICE_TYPE;

typedef enum MF_CAPTURE_ENGINE_SINK_TYPE {
  MF_CAPTURE_ENGINE_SINK_TYPE_RECORD = 0x00000000,
  MF_CAPTURE_ENGINE_SINK_TYPE_PREVIEW = 0x00000001,
  MF_CAPTURE_ENGINE_SINK_TYPE_PHOTO = 0x00000002,
} MF_CAPTURE_ENGINE_SINK_TYPE;

enum {
  MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_VIDEO_PREVIEW = 0xfffffffa,
  MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_VIDEO_RECORD = 0xfffffff9,
  MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_PHOTO = 0xfffffff8,
  MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_AUDIO = 0xfffffff7,
  MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_METADATA = 0xfffffff6,
  MF_CAPTURE_ENGINE_MEDIASOURCE = 0xffffffff,
};

typedef enum MF_CAPTURE_ENGINE_STREAM_CATEGORY {
  MF_CAPTURE_ENGINE_STREAM_CATEGORY_VIDEO_PREVIEW = 0x00000000,
  MF_CAPTURE_ENGINE_STREAM_CATEGORY_VIDEO_CAPTURE = 0x00000001,
  MF_CAPTURE_ENGINE_STREAM_CATEGORY_PHOTO_INDEPENDENT = 0x00000002,
  MF_CAPTURE_ENGINE_STREAM_CATEGORY_PHOTO_DEPENDENT = 0x00000003,
  MF_CAPTURE_ENGINE_STREAM_CATEGORY_AUDIO = 0x00000004,
  MF_CAPTURE_ENGINE_STREAM_CATEGORY_UNSUPPORTED = 0x00000005,
  MF_CAPTURE_ENGINE_STREAM_CATEGORY_METADATA = 0x00000006,
} MF_CAPTURE_ENGINE_STREAM_CATEGORY;

typedef enum MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE {
  MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_OTHER = 0,
  MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_COMMUNICATIONS = 1,
  MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_MEDIA = 2,
  MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_GAMECHAT = 3,
  MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_SPEECH = 4,
  MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_FARFIELDSPEECH = 5,
  MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_UNIFORMSPEECH = 6,
  MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_VOICETYPING = 7,
} MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE;

typedef enum MF_CAPTURE_ENGINE_AUDIO_PROCESSING_MODE {
  MF_CAPTURE_ENGINE_AUDIO_PROCESSING_DEFAULT = 0,
  MF_CAPTURE_ENGINE_AUDIO_PROCESSING_RAW = 1,
} MF_CAPTURE_ENGINE_AUDIO_PROCESSING_MODE;

cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_INITIALIZED, 0x219992bc, 0xcf92, 0x4531, 0xa1, 0xae, 0x96, 0xe1, 0xe8, 0x86, 0xc8, 0xf1);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_PREVIEW_STARTED, 0xa416df21, 0xf9d3, 0x4a74, 0x99, 0x1b, 0xb8, 0x17, 0x29, 0x89, 0x52, 0xc4);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_PREVIEW_STOPPED, 0x13d5143c, 0x1edd, 0x4e50,0xa2, 0xef, 0x35, 0x0a, 0x47, 0x67, 0x80, 0x60);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_STARTED, 0xac2b027b, 0xddf9, 0x48a0,0x89, 0xbe, 0x38, 0xab, 0x35, 0xef, 0x45, 0xc0);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_STOPPED, 0x55e5200a, 0xf98f, 0x4c0d, 0xa9, 0xec, 0x9e, 0xb2, 0x5e, 0xd3, 0xd7, 0x73);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_PHOTO_TAKEN, 0x3c50c445, 0x7304, 0x48eb,0x86, 0x5d, 0xbb, 0xa1, 0x9b, 0xa3, 0xaf, 0x5c);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_SOURCE_CURRENT_DEVICE_MEDIA_TYPE_SET, 0xe7e75e4c, 0x039c, 0x4410, 0x81, 0x5b, 0x87, 0x41, 0x30, 0x7b, 0x63, 0xaa);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_ERROR, 0x46b89fc6, 0x33cc, 0x4399,0x9d, 0xad, 0x78, 0x4d, 0xe7, 0x7d, 0x58, 0x7c);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_EFFECT_ADDED, 0xaa8dc7b5, 0xa048, 0x4e13, 0x8e, 0xbe, 0xf2, 0x3c, 0x46, 0xc8, 0x30, 0xc1);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_EFFECT_REMOVED, 0xc6e8db07, 0xfb09, 0x4a48, 0x89, 0xc6, 0xbf, 0x92, 0xa0, 0x42, 0x22, 0xc9);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_ALL_EFFECTS_REMOVED, 0xfded7521, 0x8ed8, 0x431a, 0xa9, 0x6b, 0xf3, 0xe2, 0x56, 0x5e, 0x98, 0x1c);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_SINK_PREPARED, 0x7bfce257, 0x12b1, 0x4409, 0x8c, 0x34, 0xd4, 0x45, 0xda, 0xab, 0x75, 0x78);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_OUTPUT_MEDIA_TYPE_SET, 0xcaaad994, 0x83ec, 0x45e9,0xa3, 0x0a, 0x1f, 0x20, 0xaa, 0xdb, 0x98, 0x31);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_CAMERA_STREAM_BLOCKED, 0xa4209417, 0x8d39, 0x46f3, 0xb7, 0x59, 0x59, 0x12, 0x52, 0x8f, 0x42, 0x07);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_CAMERA_STREAM_UNBLOCKED, 0x9be9eef0, 0xcdaf, 0x4717, 0x85, 0x64, 0x83, 0x4a, 0xae, 0x66, 0x41, 0x5c);")

cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_D3D_MANAGER, 0x76e25e7b, 0xd595, 0x4283, 0x96, 0x2c, 0xc5, 0x94, 0xaf, 0xd7, 0x8d, 0xdf);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_SINK_VIDEO_MAX_UNPROCESSED_SAMPLES, 0xb467f705, 0x7913, 0x4894, 0x9d, 0x42, 0xa2, 0x15, 0xfe, 0xa2, 0x3d, 0xa9);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_SINK_AUDIO_MAX_UNPROCESSED_SAMPLES, 0x1cddb141, 0xa7f4, 0x4d58, 0x98, 0x96, 0x4d, 0x15, 0xa5, 0x3c, 0x4e, 0xfe);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_SINK_VIDEO_MAX_PROCESSED_SAMPLES, 0xe7b4a49e, 0x382c, 0x4aef, 0xa9, 0x46, 0xae, 0xd5, 0x49, 0xb, 0x71, 0x11);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_SINK_AUDIO_MAX_PROCESSED_SAMPLES, 0x9896e12a, 0xf707, 0x4500, 0xb6, 0xbd, 0xdb, 0x8e, 0xb8, 0x10, 0xb5, 0xf);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_USE_AUDIO_DEVICE_ONLY, 0x1c8077da, 0x8466, 0x4dc4, 0x8b, 0x8e, 0x27, 0x6b, 0x3f, 0x85, 0x92, 0x3b);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_USE_VIDEO_DEVICE_ONLY, 0x7e025171, 0xcf32, 0x4f2e, 0x8f, 0x19, 0x41, 0x5, 0x77, 0xb7, 0x3a, 0x66);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_DISABLE_HARDWARE_TRANSFORMS, 0xb7c42a6b, 0x3207,  0x4495, 0xb4, 0xe7, 0x81, 0xf9, 0xc3, 0x5d, 0x59, 0x91);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_DISABLE_DXVA, 0xf9818862, 0x179d, 0x433f, 0xa3, 0x2f, 0x74, 0xcb, 0xcf, 0x74, 0x46, 0x6d);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_MEDIASOURCE_CONFIG, 0xbc6989d2, 0x0fc1, 0x46e1, 0xa7, 0x4f, 0xef, 0xd3, 0x6b, 0xc7, 0x88, 0xde);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_DECODER_MFT_FIELDOFUSE_UNLOCK_Attribute, 0x2b8ad2e8, 0x7acb, 0x4321, 0xa6, 0x06, 0x32, 0x5c, 0x42, 0x49, 0xf4, 0xfc);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_ENCODER_MFT_FIELDOFUSE_UNLOCK_Attribute, 0x54c63a00, 0x78d5, 0x422f, 0xaa, 0x3e, 0x5e, 0x99, 0xac, 0x64, 0x92, 0x69);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_ENABLE_CAMERA_STREAMSTATE_NOTIFICATION, 0x4c808e9d, 0xaaed, 0x4713, 0x90, 0xfb, 0xcb, 0x24, 0x06, 0x4a, 0xb8, 0xda);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_MEDIA_CATEGORY, 0x8e3f5bd5, 0xdbbf, 0x42f0, 0x85, 0x42, 0xd0, 0x7a, 0x39, 0x71, 0x76, 0x2a);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_AUDIO_PROCESSING, 0x10f1be5e, 0x7e11, 0x410b, 0x97, 0x3d, 0xf4, 0xb6, 0x10, 0x90, 0x0, 0xfe);")

cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_EVENT_GENERATOR_GUID, 0xabfa8ad5, 0xfc6d, 0x4911, 0x87, 0xe0, 0x96, 0x19, 0x45, 0xf8, 0xf7, 0xce);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_EVENT_STREAM_INDEX, 0x82697f44, 0xb1cf, 0x42eb, 0x97, 0x53, 0xf8, 0x6d, 0x64, 0x9c, 0x88, 0x65);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_SELECTEDCAMERAPROFILE, 0x03160b7e, 0x1c6f, 0x4db2, 0xad, 0x56, 0xa7, 0xc4, 0x30, 0xf8, 0x23, 0x92);")
cpp_quote("EXTERN_GUID(MF_CAPTURE_ENGINE_SELECTEDCAMERAPROFILE_INDEX, 0x3ce88613, 0x2214, 0x46c3, 0xb4, 0x17, 0x82, 0xf8, 0xa3, 0x13, 0xc9, 0xc3);")

[object, uuid(aeda51c0-**************-de597b88b089), local]
interface IMFCaptureEngineOnEventCallback : IUnknown
{
  HRESULT OnEvent([in] IMFMediaEvent *pEvent);
}

[object, uuid(52150b82-ab39-4467-980f-e48bf0822ecd), local]
interface IMFCaptureEngineOnSampleCallback : IUnknown
{
  HRESULT OnSample([in] IMFSample *pSample);
}

[object, uuid(72d6135b-35e9-412c-b926-fd5265f2a885), local]
interface IMFCaptureSink : IUnknown
{
  HRESULT GetOutputMediaType([in] DWORD dwSinkStreamIndex, [out] IMFMediaType **ppMediaType);
  HRESULT GetService([in] DWORD dwSinkStreamIndex, [in] REFGUID rguidService, [in] REFIID riid, [out] IUnknown **ppUnknown);
  HRESULT AddStream([in] DWORD dwSourceStreamIndex, [in] IMFMediaType *pMediaType, [in] IMFAttributes *pAttributes, [out] DWORD *pdwSinkStreamIndex);
  HRESULT Prepare();
  HRESULT RemoveAllStreams();
}

[object, uuid(3323b55a-f92a-4fe2-8edc-e9bfc0634d77), local]
interface IMFCaptureRecordSink : IMFCaptureSink
{
  HRESULT SetOutputByteStream([in] IMFByteStream *pByteStream, [in] REFGUID guidContainerType);
  HRESULT SetOutputFileName([in] LPCWSTR fileName);
  HRESULT SetSampleCallback([in] DWORD dwStreamSinkIndex, [in] IMFCaptureEngineOnSampleCallback *pCallback);
  HRESULT SetCustomSink([in] IMFMediaSink *pMediaSink);
  HRESULT GetRotation([in] DWORD dwStreamIndex, [out] DWORD *pdwRotationValue);
  HRESULT SetRotation([in] DWORD dwStreamIndex, [in] DWORD dwRotationValue);
}

[object, uuid(77346cfd-5b49-4d73-ace0-5b52a859f2e0), local]
interface IMFCapturePreviewSink : IMFCaptureSink
{
  HRESULT SetRenderHandle([in] HANDLE handle);
  HRESULT SetRenderSurface([in] IUnknown *pSurface);
  HRESULT UpdateVideo([in] const MFVideoNormalizedRect *pSrc, [in] const RECT *pDst, [in] const COLORREF *pBorderClr);
  HRESULT SetSampleCallback([in] DWORD dwStreamSinkIndex, [in] IMFCaptureEngineOnSampleCallback *pCallback);
  HRESULT GetMirrorState([out] WINBOOL *pfMirrorState);
  HRESULT SetMirrorState([in] WINBOOL fMirrorState);
  HRESULT GetRotation([in] DWORD dwStreamIndex, [out] DWORD *pdwRotationValue);
  HRESULT SetRotation([in] DWORD dwStreamIndex, [in] DWORD dwRotationValue);
  HRESULT SetCustomSink([in] IMFMediaSink *pMediaSink);
}

[object, uuid(d2d43cc8-48bb-4aa7-95db-10c06977e777), local]
interface IMFCapturePhotoSink : IMFCaptureSink
{
  HRESULT SetOutputFileName([in] LPCWSTR fileName);
  HRESULT SetSampleCallback([in] IMFCaptureEngineOnSampleCallback *pCallback);
  HRESULT SetOutputByteStream([in] IMFByteStream *pByteStream);
}

[object, uuid(439a42a8-0d2c-4505-be83-f79b2a05d5c4), local]
interface IMFCaptureSource : IUnknown
{
  HRESULT GetCaptureDeviceSource([in] MF_CAPTURE_ENGINE_DEVICE_TYPE mfCaptureEngineDeviceType, [out] IMFMediaSource **ppMediaSource);
  HRESULT GetCaptureDeviceActivate([in] MF_CAPTURE_ENGINE_DEVICE_TYPE mfCaptureEngineDeviceType, [out] IMFActivate **ppActivate);
  HRESULT GetService([in] REFIID rguidService, [in] REFIID riid, [out] IUnknown **ppUnknown);
  HRESULT AddEffect([in] DWORD dwSourceStreamIndex, [in] IUnknown *pUnknown);
  HRESULT RemoveEffect([in] DWORD dwSourceStreamIndex, [in]IUnknown *pUnknown);
  HRESULT RemoveAllEffects([in] DWORD dwSourceStreamIndex);
  HRESULT GetAvailableDeviceMediaType([in] DWORD dwSourceStreamIndex, [in] DWORD dwMediaTypeIndex, [out] IMFMediaType **ppMediaType);
  HRESULT SetCurrentDeviceMediaType([in] DWORD dwSourceStreamIndex, [in] IMFMediaType *pMediaType);
  HRESULT GetCurrentDeviceMediaType([in] DWORD dwSourceStreamIndex, [out] IMFMediaType **ppMediaType);
  HRESULT GetDeviceStreamCount([out] DWORD *pdwStreamCount);
  HRESULT GetDeviceStreamCategory([in] DWORD dwSourceStreamIndex, [out] MF_CAPTURE_ENGINE_STREAM_CATEGORY *pStreamCategory);
  HRESULT GetMirrorState([in] DWORD dwStreamIndex, [out] WINBOOL *pfMirrorState);
  HRESULT SetMirrorState([in] DWORD dwStreamIndex, [in] WINBOOL fMirrorState);
  HRESULT GetStreamIndexFromFriendlyName([in] UINT32 uifriendlyName, [out] DWORD *pdwActualStreamIndex);
}

cpp_quote("EXTERN_GUID(CLSID_MFCaptureEngine, 0xefce38d3, 0x8914, 0x4674,0xa7, 0xdf, 0xae, 0x1b, 0x3d, 0x65, 0x4b, 0x8a);")

[object, uuid(a6bba433-176b-48b2-b375-53aa03473207), local]
interface IMFCaptureEngine : IUnknown
{
  HRESULT Initialize([in] IMFCaptureEngineOnEventCallback *pEventCallback, [in] IMFAttributes *pAttributes, [in] IUnknown *pAudioSource, [in] IUnknown *pVideoSource);
  HRESULT StartPreview();
  HRESULT StopPreview();
  HRESULT StartRecord();
  HRESULT StopRecord([in] WINBOOL bFinalize, [in] WINBOOL bFlushUnprocessedSamples);
  HRESULT TakePhoto();
  HRESULT GetSink([in] MF_CAPTURE_ENGINE_SINK_TYPE mfCaptureEngineSinkType, [out] IMFCaptureSink **ppSink);
  HRESULT GetSource([out] IMFCaptureSource **ppSource);
}

cpp_quote("EXTERN_GUID(CLSID_MFCaptureEngineClassFactory, 0xefce38d3, 0x8914, 0x4674,0xa7, 0xdf, 0xae, 0x1b, 0x3d, 0x65, 0x4b, 0x8a);")

[object, uuid(8f02d140-56fc-4302-a705-3a97c78be779), local]
interface IMFCaptureEngineClassFactory : IUnknown
{
  HRESULT CreateInstance([in] REFCLSID clsid, [in] REFIID riid, [out, iid_is(riid)] LPVOID *ppvObject );
}

cpp_quote("EXTERN_GUID(MFSampleExtension_DeviceReferenceSystemTime, 0x6523775a, 0xba2d, 0x405f,0xb2, 0xc5, 0x01, 0xff, 0x88, 0xe2, 0xe8, 0xf6);")

[object, uuid(e37ceed7-340f-4514-9f4d-9c2ae026100b), local]
interface IMFCaptureEngineOnSampleCallback2 : IMFCaptureEngineOnSampleCallback
{
  HRESULT OnSynchronizedEvent([in] IMFMediaEvent *pEvent);
}

[object, uuid(f9e4219e-6197-4b5e-b888-bee310ab2c59), local]
interface IMFCaptureSink2 : IMFCaptureSink
{
  HRESULT SetOutputMediaType([in] DWORD dwStreamIndex, [in] IMFMediaType *pMediaType, [in] IMFAttributes *pEncodingAttributes);
}

cpp_quote("#endif /* WINAPI_PARTITION_DESKTOP */")
cpp_quote("#endif /* WINVER >= _WIN32_WINNT_WIN7 */")
