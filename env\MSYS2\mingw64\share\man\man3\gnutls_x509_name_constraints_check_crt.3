.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_name_constraints_check_crt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_name_constraints_check_crt \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_name_constraints_check_crt(gnutls_x509_name_constraints_t " nc ", gnutls_x509_subject_alt_name_t " type ", gnutls_x509_crt_t " cert ");"
.SH ARGUMENTS
.IP "gnutls_x509_name_constraints_t nc" 12
the extracted name constraints
.IP "gnutls_x509_subject_alt_name_t type" 12
the type of the constraint to check (of type gnutls_x509_subject_alt_name_t)
.IP "gnutls_x509_crt_t cert" 12
the certificate to be checked
.SH "DESCRIPTION"
This function will check the provided certificate names against the constraints in
 \fInc\fP using the RFC5280 rules. It will traverse all the certificate's names and
alternative names.

Currently this function is limited to DNS
names and emails (of type \fBGNUTLS_SAN_DNSNAME\fP and \fBGNUTLS_SAN_RFC822NAME\fP).
.SH "RETURNS"
zero if the provided name is not acceptable, and non\-zero otherwise.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
