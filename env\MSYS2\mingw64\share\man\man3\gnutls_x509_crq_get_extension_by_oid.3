.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crq_get_extension_by_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crq_get_extension_by_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crq_get_extension_by_oid(gnutls_x509_crq_t " crq ", const char * " oid ", unsigned " indx ", void * " buf ", size_t * " buf_size ", unsigned int * " critical ");"
.SH ARGUMENTS
.IP "gnutls_x509_crq_t crq" 12
should contain a \fBgnutls_x509_crq_t\fP type
.IP "const char * oid" 12
holds an Object Identifier in a null terminated string
.IP "unsigned indx" 12
In case multiple same OIDs exist in the extensions, this
specifies which to get. Use (0) to get the first one.
.IP "void * buf" 12
a pointer to a structure to hold the name (may be null)
.IP "size_t * buf_size" 12
initially holds the size of  \fIbuf\fP 
.IP "unsigned int * critical" 12
will be non\-zero if the extension is marked as critical
.SH "DESCRIPTION"
This function will return the extension specified by the OID in
the certificate.  The extensions will be returned as binary data
DER encoded, in the provided buffer.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error code in case of an error.  If the certificate does not
contain the specified extension
\fBGNUTLS_E_REQUESTED_DATA_NOT_AVAILABLE\fP will be returned.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
