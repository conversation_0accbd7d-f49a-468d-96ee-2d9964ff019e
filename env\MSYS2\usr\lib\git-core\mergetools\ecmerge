diff_cmd () {
	"$merge_tool_path" --default --mode=diff2 "$LOCAL" "$REMOTE"
}

diff_cmd_help () {
	echo "Use ECMerge (requires a graphical session)"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" "$BASE" "$LOCAL" "$REMOTE" \
			--default --mode=merge3 --to="$MERGED"
	else
		"$merge_tool_path" "$LOCAL" "$REMOTE" \
			--default --mode=merge2 --to="$MERGED"
	fi
}

merge_cmd_help () {
	echo "Use ECMerge (requires a graphical session)"
}
