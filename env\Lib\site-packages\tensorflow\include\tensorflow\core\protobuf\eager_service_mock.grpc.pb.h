// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tensorflow/core/protobuf/eager_service.proto

#include "tensorflow/core/protobuf/eager_service.pb.h"
#include "tensorflow/core/protobuf/eager_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace tensorflow {
namespace eager {


namespace grpc {

class MockEagerServiceStub : public EagerService::StubInterface {
 public:
  MOCK_METHOD3(CreateContext, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::tensorflow::eager::CreateContextResponse* response));
  MOCK_METHOD3(AsyncCreateContextRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CreateContextResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncCreateContextRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CreateContextResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(UpdateContext, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::tensorflow::eager::UpdateContextResponse* response));
  MOCK_METHOD3(AsyncUpdateContextRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::UpdateContextResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUpdateContextRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::UpdateContextResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(Enqueue, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::tensorflow::eager::EnqueueResponse* response));
  MOCK_METHOD3(AsyncEnqueueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::EnqueueResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncEnqueueRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::EnqueueResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD1(StreamingEnqueueRaw, ::grpc::ClientReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncStreamingEnqueueRaw, ::grpc::ClientAsyncReaderWriterInterface<::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncStreamingEnqueueRaw, ::grpc::ClientAsyncReaderWriterInterface<::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(WaitQueueDone, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::tensorflow::eager::WaitQueueDoneResponse* response));
  MOCK_METHOD3(AsyncWaitQueueDoneRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::WaitQueueDoneResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncWaitQueueDoneRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::WaitQueueDoneResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(RunComponentFunction, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::tensorflow::eager::RunComponentFunctionResponse* response));
  MOCK_METHOD3(AsyncRunComponentFunctionRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::RunComponentFunctionResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncRunComponentFunctionRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::RunComponentFunctionResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(KeepAlive, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::tensorflow::eager::KeepAliveResponse* response));
  MOCK_METHOD3(AsyncKeepAliveRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::KeepAliveResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncKeepAliveRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::KeepAliveResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(CloseContext, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::tensorflow::eager::CloseContextResponse* response));
  MOCK_METHOD3(AsyncCloseContextRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CloseContextResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncCloseContextRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CloseContextResponse>*(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc

} // namespace tensorflow
} // namespace eager

