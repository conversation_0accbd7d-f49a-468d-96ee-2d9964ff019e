This is a revocation certificate for the OpenPGP key:

pub   rsa4096 2025-07-31 [S]
      87C2C116810087E7E19454A14C071569795CE2A4
uid          Pacman Keyring Master Key <pacman@localhost>

A revocation certificate is a kind of "kill switch" to publicly
declare that a key shall not anymore be used.  It is not possible
to retract such a revocation certificate once it has been published.

Use it to revoke this key in case of a compromise or loss of
the secret key.  However, if the secret key is still accessible,
it is better to generate a new revocation certificate and give
a reason for the revocation.  For details see the description of
of the gpg command "--generate-revocation" in the GnuPG manual.

To avoid an accidental use of this file, a colon has been inserted
before the 5 dashes below.  Remove this colon with a text editor
before importing and publishing this revocation certificate.

:-----BEGIN PGP PUBLIC KEY BLOCK-----
Comment: This is a revocation certificate

iQI2BCABCAAgFiEEh8LBFoEAh+fhlFShTAcVaXlc4qQFAmiK+AgCHQAACgkQTAcV
aXlc4qQkdA/+PwEBGEwigdSVrpu20eeCdruVIVamcmjY6jiu4vEu7BTEWN99bpuy
aOEBA1dFq89syN4fWHyhQr6u8XKnkBPc+97as1P2gc8XxJ6D/FtOYDZkeTEMjGGs
R3mGl3evDj9m752leLKt7SpdT5iptl6BZJPrMLBkl+E+l2Dys5GLtVAT1ZbSHdTn
4GifRayNH7mWvs61Bdgp1s4GdItDRla7a6xJ4Hn4r88aEQRBufj81g7kyjxCYvQu
oHbk9yopGvbWGwBMyL3l7qK1NSR+d4i3o/HBh+ygiIh/qT4xoXJxGKrGKYKiMLlU
wZYqM56rq4vihKhzTMQBeX+G1v2jgoB6iYy4+1oW6mbgpB53n+I6Ev+5y802IM6M
khoJ7jJRdQuk6TDAIztEKpi8/IzYT0Jm85WxHgDSc1z/j22pGOYFy0pFA2QsTAvQ
GLlezYd2/BTysZqwvAxBWErhyn12kMhFx1Nr4oc5lVfnSh4cjew8M6WjwFwLcMS3
GgIQ3M+/LFE5qGbwz5VLXAbw+xAu5FWm6reb0aSZQh3oIVK7vhXQgZExLrm6Dtcc
Oyo3app4enOau/4T6hrH28emajbJC5LgFh4FC8nGauHdVASjnwwm+IPissuQiA6N
vlfFEm1PXBIAHK4w6fKtcLqy18Y4JEqR0HuS6DjxqwqIPejUaidLoBw=
=5PBX
-----END PGP PUBLIC KEY BLOCK-----
