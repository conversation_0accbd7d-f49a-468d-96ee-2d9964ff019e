.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_get_spki" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_get_spki \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_get_spki(gnutls_x509_privkey_t " key ", gnutls_x509_spki_t " spki ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
should contain a \fBgnutls_x509_privkey_t\fP type
.IP "gnutls_x509_spki_t spki" 12
a SubjectPublicKeyInfo structure of type \fBgnutls_x509_spki_t\fP
.IP "unsigned int flags" 12
must be zero
.SH "DESCRIPTION"
This function will return the public key information of a private
key. The provided  \fIspki\fP must be initialized.
.SH "RETURNS"
Zero on success, or a negative error code on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
