/* Copyright 2022 The OpenXLA Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef XLA_SERVICE_CPU_CPU_EVENT_H_
#define XLA_SERVICE_CPU_CPU_EVENT_H_

namespace xla {

// Typical use case: AsyncValueRef<CpuEvent> is used to indicate the
// completion of a CPU operation, e.g., data transfer or running a program.
struct CpuEvent {
  CpuEvent() = default;
};

}  // namespace xla

#endif  // XLA_SERVICE_CPU_CPU_EVENT_H_
