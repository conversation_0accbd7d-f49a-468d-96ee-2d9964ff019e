/* This file contains the definitions and documentation for the
   synchronization builtins used in the GNU compiler.
   Copyright (C) 2005-2025 Free Software Foundation, Inc.

This file is part of GCC.

GCC is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free
Software Foundation; either version 3, or (at your option) any later
version.

GCC is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.

You should have received a copy of the GNU General Public License
along with GCC; see the file COPYING3.  If not see
<http://www.gnu.org/licenses/>.  */

/* Before including this file, you should define a macro:

     DEF_SYNC_BUILTIN (ENUM, NAME, TYPE, ATTRS)

   See builtins.def for details.  */

/* Synchronization Primitives.  The "_N" version is the one that the user
   is supposed to be using.  It's overloaded, and is resolved to one of the
   "_1" through "_16" versions, plus some extra casts.  */

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_ADD_N, "__sync_fetch_and_add",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_ADD_1, "__sync_fetch_and_add_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_ADD_2, "__sync_fetch_and_add_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_ADD_4, "__sync_fetch_and_add_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_ADD_8, "__sync_fetch_and_add_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_ADD_16, "__sync_fetch_and_add_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_SUB_N, "__sync_fetch_and_sub",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_SUB_1, "__sync_fetch_and_sub_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_SUB_2, "__sync_fetch_and_sub_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_SUB_4, "__sync_fetch_and_sub_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_SUB_8, "__sync_fetch_and_sub_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_SUB_16, "__sync_fetch_and_sub_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_OR_N, "__sync_fetch_and_or",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_OR_1, "__sync_fetch_and_or_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_OR_2, "__sync_fetch_and_or_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_OR_4, "__sync_fetch_and_or_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_OR_8, "__sync_fetch_and_or_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_OR_16, "__sync_fetch_and_or_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_AND_N, "__sync_fetch_and_and",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_AND_1, "__sync_fetch_and_and_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_AND_2, "__sync_fetch_and_and_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_AND_4, "__sync_fetch_and_and_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_AND_8, "__sync_fetch_and_and_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_AND_16, "__sync_fetch_and_and_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_XOR_N, "__sync_fetch_and_xor",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_XOR_1, "__sync_fetch_and_xor_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_XOR_2, "__sync_fetch_and_xor_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_XOR_4, "__sync_fetch_and_xor_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_XOR_8, "__sync_fetch_and_xor_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_XOR_16, "__sync_fetch_and_xor_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_NAND_N, "__sync_fetch_and_nand",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_NAND_1, "__sync_fetch_and_nand_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_NAND_2, "__sync_fetch_and_nand_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_NAND_4, "__sync_fetch_and_nand_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_NAND_8, "__sync_fetch_and_nand_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_FETCH_AND_NAND_16, "__sync_fetch_and_nand_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_ADD_AND_FETCH_N, "__sync_add_and_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_ADD_AND_FETCH_1, "__sync_add_and_fetch_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_ADD_AND_FETCH_2, "__sync_add_and_fetch_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_ADD_AND_FETCH_4, "__sync_add_and_fetch_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_ADD_AND_FETCH_8, "__sync_add_and_fetch_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_ADD_AND_FETCH_16, "__sync_add_and_fetch_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_SUB_AND_FETCH_N, "__sync_sub_and_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_SUB_AND_FETCH_1, "__sync_sub_and_fetch_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_SUB_AND_FETCH_2, "__sync_sub_and_fetch_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_SUB_AND_FETCH_4, "__sync_sub_and_fetch_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_SUB_AND_FETCH_8, "__sync_sub_and_fetch_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_SUB_AND_FETCH_16, "__sync_sub_and_fetch_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_OR_AND_FETCH_N, "__sync_or_and_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_OR_AND_FETCH_1, "__sync_or_and_fetch_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_OR_AND_FETCH_2, "__sync_or_and_fetch_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_OR_AND_FETCH_4, "__sync_or_and_fetch_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_OR_AND_FETCH_8, "__sync_or_and_fetch_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_OR_AND_FETCH_16, "__sync_or_and_fetch_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_AND_AND_FETCH_N, "__sync_and_and_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_AND_AND_FETCH_1, "__sync_and_and_fetch_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_AND_AND_FETCH_2, "__sync_and_and_fetch_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_AND_AND_FETCH_4, "__sync_and_and_fetch_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_AND_AND_FETCH_8, "__sync_and_and_fetch_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_AND_AND_FETCH_16, "__sync_and_and_fetch_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_XOR_AND_FETCH_N, "__sync_xor_and_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_XOR_AND_FETCH_1, "__sync_xor_and_fetch_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_XOR_AND_FETCH_2, "__sync_xor_and_fetch_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_XOR_AND_FETCH_4, "__sync_xor_and_fetch_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_XOR_AND_FETCH_8, "__sync_xor_and_fetch_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_XOR_AND_FETCH_16, "__sync_xor_and_fetch_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_NAND_AND_FETCH_N, "__sync_nand_and_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_NAND_AND_FETCH_1, "__sync_nand_and_fetch_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_NAND_AND_FETCH_2, "__sync_nand_and_fetch_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_NAND_AND_FETCH_4, "__sync_nand_and_fetch_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_NAND_AND_FETCH_8, "__sync_nand_and_fetch_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_NAND_AND_FETCH_16, "__sync_nand_and_fetch_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_BOOL_COMPARE_AND_SWAP_N,
		  "__sync_bool_compare_and_swap",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_BOOL_COMPARE_AND_SWAP_1,
		  "__sync_bool_compare_and_swap_1",
		  BT_FN_BOOL_VPTR_I1_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_BOOL_COMPARE_AND_SWAP_2,
		  "__sync_bool_compare_and_swap_2",
		  BT_FN_BOOL_VPTR_I2_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_BOOL_COMPARE_AND_SWAP_4,
		  "__sync_bool_compare_and_swap_4",
		  BT_FN_BOOL_VPTR_I4_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_BOOL_COMPARE_AND_SWAP_8,
		  "__sync_bool_compare_and_swap_8",
		  BT_FN_BOOL_VPTR_I8_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_BOOL_COMPARE_AND_SWAP_16,
		  "__sync_bool_compare_and_swap_16",
		  BT_FN_BOOL_VPTR_I16_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_VAL_COMPARE_AND_SWAP_N,
		  "__sync_val_compare_and_swap",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_VAL_COMPARE_AND_SWAP_1,
		  "__sync_val_compare_and_swap_1",
		  BT_FN_I1_VPTR_I1_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_VAL_COMPARE_AND_SWAP_2,
		  "__sync_val_compare_and_swap_2",
		  BT_FN_I2_VPTR_I2_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_VAL_COMPARE_AND_SWAP_4,
		  "__sync_val_compare_and_swap_4",
		  BT_FN_I4_VPTR_I4_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_VAL_COMPARE_AND_SWAP_8,
		  "__sync_val_compare_and_swap_8",
		  BT_FN_I8_VPTR_I8_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_VAL_COMPARE_AND_SWAP_16,
		  "__sync_val_compare_and_swap_16",
		  BT_FN_I16_VPTR_I16_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_TEST_AND_SET_N,
		  "__sync_lock_test_and_set",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_TEST_AND_SET_1,
		  "__sync_lock_test_and_set_1",
		  BT_FN_I1_VPTR_I1, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_TEST_AND_SET_2,
		  "__sync_lock_test_and_set_2",
		  BT_FN_I2_VPTR_I2, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_TEST_AND_SET_4,
		  "__sync_lock_test_and_set_4",
		  BT_FN_I4_VPTR_I4, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_TEST_AND_SET_8,
		  "__sync_lock_test_and_set_8",
		  BT_FN_I8_VPTR_I8, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_TEST_AND_SET_16,
		  "__sync_lock_test_and_set_16",
		  BT_FN_I16_VPTR_I16, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_RELEASE_N, "__sync_lock_release",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_RELEASE_1, "__sync_lock_release_1",
		  BT_FN_VOID_VPTR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_RELEASE_2, "__sync_lock_release_2",
		  BT_FN_VOID_VPTR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_RELEASE_4, "__sync_lock_release_4",
		  BT_FN_VOID_VPTR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_RELEASE_8, "__sync_lock_release_8",
		  BT_FN_VOID_VPTR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_SYNC_LOCK_RELEASE_16, "__sync_lock_release_16",
		  BT_FN_VOID_VPTR, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_SYNC_SYNCHRONIZE, "__sync_synchronize",
		  BT_FN_VOID, ATTR_NOTHROWCALL_LEAF_LIST)

/* __sync* builtins for the C++ memory model.  */

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_TEST_AND_SET, "__atomic_test_and_set",
		  BT_FN_BOOL_VPTR_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_CLEAR, "__atomic_clear", BT_FN_VOID_VPTR_INT,
		  ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_EXCHANGE,
		  "__atomic_exchange",
		  BT_FN_VOID_SIZE_VPTR_PTR_PTR_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_EXCHANGE_N,
		  "__atomic_exchange_n",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_EXCHANGE_1,
		  "__atomic_exchange_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_EXCHANGE_2,
		  "__atomic_exchange_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_EXCHANGE_4,
		  "__atomic_exchange_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_EXCHANGE_8,
		  "__atomic_exchange_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_EXCHANGE_16,
		  "__atomic_exchange_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_LOAD,
		  "__atomic_load",
		  BT_FN_VOID_SIZE_CONST_VPTR_PTR_INT,
		  ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_LOAD_N,
		  "__atomic_load_n",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_LOAD_1,
		  "__atomic_load_1",
		  BT_FN_I1_CONST_VPTR_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_LOAD_2,
		  "__atomic_load_2",
		  BT_FN_I2_CONST_VPTR_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_LOAD_4,
		  "__atomic_load_4",
		  BT_FN_I4_CONST_VPTR_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_LOAD_8,
		  "__atomic_load_8",
		  BT_FN_I8_CONST_VPTR_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_LOAD_16,
		  "__atomic_load_16",
		  BT_FN_I16_CONST_VPTR_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_COMPARE_EXCHANGE,
		  "__atomic_compare_exchange",
		  BT_FN_BOOL_SIZE_VPTR_PTR_PTR_INT_INT,
		  ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_COMPARE_EXCHANGE_N,
		  "__atomic_compare_exchange_n",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_COMPARE_EXCHANGE_1,
		  "__atomic_compare_exchange_1",
		  BT_FN_BOOL_VPTR_PTR_I1_BOOL_INT_INT,
		  ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_COMPARE_EXCHANGE_2,
		  "__atomic_compare_exchange_2",
		  BT_FN_BOOL_VPTR_PTR_I2_BOOL_INT_INT,
		  ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_COMPARE_EXCHANGE_4,
		  "__atomic_compare_exchange_4",
		  BT_FN_BOOL_VPTR_PTR_I4_BOOL_INT_INT,
		  ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_COMPARE_EXCHANGE_8,
		  "__atomic_compare_exchange_8",
		  BT_FN_BOOL_VPTR_PTR_I8_BOOL_INT_INT,
		  ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_COMPARE_EXCHANGE_16,
		  "__atomic_compare_exchange_16",
		  BT_FN_BOOL_VPTR_PTR_I16_BOOL_INT_INT,
		  ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_STORE,
		  "__atomic_store",
		  BT_FN_VOID_SIZE_VPTR_PTR_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_STORE_N,
		  "__atomic_store_n",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_STORE_1,
		  "__atomic_store_1",
		  BT_FN_VOID_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_STORE_2,
		  "__atomic_store_2",
		  BT_FN_VOID_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_STORE_4,
		  "__atomic_store_4",
		  BT_FN_VOID_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_STORE_8,
		  "__atomic_store_8",
		  BT_FN_VOID_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_STORE_16,
		  "__atomic_store_16",
		  BT_FN_VOID_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_ADD_FETCH_N,
		  "__atomic_add_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_ADD_FETCH_1,
		  "__atomic_add_fetch_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_ADD_FETCH_2,
		  "__atomic_add_fetch_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_ADD_FETCH_4,
		  "__atomic_add_fetch_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_ADD_FETCH_8,
		  "__atomic_add_fetch_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_ADD_FETCH_16,
		  "__atomic_add_fetch_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_SUB_FETCH_N,
		  "__atomic_sub_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_SUB_FETCH_1,
		  "__atomic_sub_fetch_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_SUB_FETCH_2,
		  "__atomic_sub_fetch_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_SUB_FETCH_4,
		  "__atomic_sub_fetch_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_SUB_FETCH_8,
		  "__atomic_sub_fetch_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_SUB_FETCH_16,
		  "__atomic_sub_fetch_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_AND_FETCH_N,
		  "__atomic_and_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_AND_FETCH_1,
		  "__atomic_and_fetch_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_AND_FETCH_2,
		  "__atomic_and_fetch_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_AND_FETCH_4,
		  "__atomic_and_fetch_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_AND_FETCH_8,
		  "__atomic_and_fetch_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_AND_FETCH_16,
		  "__atomic_and_fetch_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_NAND_FETCH_N,
		  "__atomic_nand_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_NAND_FETCH_1,
		  "__atomic_nand_fetch_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_NAND_FETCH_2,
		  "__atomic_nand_fetch_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_NAND_FETCH_4,
		  "__atomic_nand_fetch_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_NAND_FETCH_8,
		  "__atomic_nand_fetch_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_NAND_FETCH_16,
		  "__atomic_nand_fetch_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_XOR_FETCH_N,
		  "__atomic_xor_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_XOR_FETCH_1,
		  "__atomic_xor_fetch_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_XOR_FETCH_2,
		  "__atomic_xor_fetch_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_XOR_FETCH_4,
		  "__atomic_xor_fetch_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_XOR_FETCH_8,
		  "__atomic_xor_fetch_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_XOR_FETCH_16,
		  "__atomic_xor_fetch_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_OR_FETCH_N,
		  "__atomic_or_fetch",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_OR_FETCH_1,
		  "__atomic_or_fetch_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_OR_FETCH_2,
		  "__atomic_or_fetch_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_OR_FETCH_4,
		  "__atomic_or_fetch_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_OR_FETCH_8,
		  "__atomic_or_fetch_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_OR_FETCH_16,
		  "__atomic_or_fetch_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_ADD_N,
		  "__atomic_fetch_add",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_ADD_1,
		  "__atomic_fetch_add_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_ADD_2,
		  "__atomic_fetch_add_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_ADD_4,
		  "__atomic_fetch_add_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_ADD_8,
		  "__atomic_fetch_add_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_ADD_16,
		  "__atomic_fetch_add_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_SUB_N,
		  "__atomic_fetch_sub",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_SUB_1,
		  "__atomic_fetch_sub_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_SUB_2,
		  "__atomic_fetch_sub_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_SUB_4,
		  "__atomic_fetch_sub_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_SUB_8,
		  "__atomic_fetch_sub_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_SUB_16,
		  "__atomic_fetch_sub_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_AND_N,
		  "__atomic_fetch_and",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_AND_1,
		  "__atomic_fetch_and_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_AND_2,
		  "__atomic_fetch_and_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_AND_4,
		  "__atomic_fetch_and_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_AND_8,
		  "__atomic_fetch_and_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_AND_16,
		  "__atomic_fetch_and_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_NAND_N,
		  "__atomic_fetch_nand",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_NAND_1,
		  "__atomic_fetch_nand_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_NAND_2,
		  "__atomic_fetch_nand_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_NAND_4,
		  "__atomic_fetch_nand_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_NAND_8,
		  "__atomic_fetch_nand_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_NAND_16,
		  "__atomic_fetch_nand_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_XOR_N,
		  "__atomic_fetch_xor",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_XOR_1,
		  "__atomic_fetch_xor_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_XOR_2,
		  "__atomic_fetch_xor_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_XOR_4,
		  "__atomic_fetch_xor_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_XOR_8,
		  "__atomic_fetch_xor_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_XOR_16,
		  "__atomic_fetch_xor_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)


DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_OR_N,
		  "__atomic_fetch_or",
		  BT_FN_VOID_VAR, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_OR_1,
		  "__atomic_fetch_or_1",
		  BT_FN_I1_VPTR_I1_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_OR_2,
		  "__atomic_fetch_or_2",
		  BT_FN_I2_VPTR_I2_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_OR_4,
		  "__atomic_fetch_or_4",
		  BT_FN_I4_VPTR_I4_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_OR_8,
		  "__atomic_fetch_or_8",
		  BT_FN_I8_VPTR_I8_INT, ATTR_NOTHROWCALL_LEAF_LIST)
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FETCH_OR_16,
		  "__atomic_fetch_or_16",
		  BT_FN_I16_VPTR_I16_INT, ATTR_NOTHROWCALL_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_ALWAYS_LOCK_FREE,
		  "__atomic_always_lock_free",
		  BT_FN_BOOL_SIZE_CONST_VPTR, ATTR_CONST_NOTHROW_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_IS_LOCK_FREE,
		  "__atomic_is_lock_free",
		  BT_FN_BOOL_SIZE_CONST_VPTR, ATTR_CONST_NOTHROW_LEAF_LIST)


DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_THREAD_FENCE,
		  "__atomic_thread_fence",
		  BT_FN_VOID_INT, ATTR_NOTHROW_LEAF_LIST)

DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_SIGNAL_FENCE,
		  "__atomic_signal_fence",
		  BT_FN_VOID_INT, ATTR_NOTHROW_LEAF_LIST)

/* This one is actually a function in libatomic and not expected to be
   inlined, declared here for convenience of targets generating calls
   to it.  */
DEF_SYNC_BUILTIN (BUILT_IN_ATOMIC_FERAISEEXCEPT,
		  "__atomic_feraiseexcept",
		  BT_FN_VOID_INT, ATTR_LEAF_LIST)
