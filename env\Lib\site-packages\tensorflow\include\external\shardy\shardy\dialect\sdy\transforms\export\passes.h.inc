/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_CLOSESHARDINGSPASS
#define GEN_PASS_DECL_DROPSHARDINGRULESPASS
#define GEN_PASS_DECL_INSERTEXPLICITRESHARDSPASS
#define GEN_PASS_DECL_REMOVESHARDINGGROUPSPASS
#define GEN_PASS_DECL_RESHARDTOCOLLECTIVESPASS
#define GEN_PASS_DECL_SHARDINGCONSTRAINTTORESHARDPASS
#define GEN_PASS_DECL_SINKDATAFLOWEDGESPASS
#define GEN_PASS_DECL_UPDATENONDIVISIBLEINPUTOUTPUTSHARDINGSPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// CloseShardingsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_CLOSESHARDINGSPASS
std::unique_ptr<::mlir::Pass> createCloseShardingsPass();
#undef GEN_PASS_DECL_CLOSESHARDINGSPASS
#endif // GEN_PASS_DECL_CLOSESHARDINGSPASS
#ifdef GEN_PASS_DEF_CLOSESHARDINGSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createCloseShardingsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class CloseShardingsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = CloseShardingsPassBase;

  CloseShardingsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  CloseShardingsPassBase(const CloseShardingsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  CloseShardingsPassBase& operator=(const CloseShardingsPassBase &) = delete;
  CloseShardingsPassBase(CloseShardingsPassBase &&) = delete;
  CloseShardingsPassBase& operator=(CloseShardingsPassBase &&) = delete;
  ~CloseShardingsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-close-shardings");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-close-shardings"; }

  ::llvm::StringRef getDescription() const override { return "Closes tensor shardings and drops replicated axes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CloseShardingsPass");
  }
  ::llvm::StringRef getName() const override { return "CloseShardingsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CloseShardingsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createCloseShardingsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createCloseShardingsPass() {
  return impl::createCloseShardingsPass();
}
#undef GEN_PASS_DEF_CLOSESHARDINGSPASS
#endif // GEN_PASS_DEF_CLOSESHARDINGSPASS

//===----------------------------------------------------------------------===//
// DropShardingRulesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DROPSHARDINGRULESPASS
std::unique_ptr<::mlir::Pass> createDropShardingRulesPass();
#undef GEN_PASS_DECL_DROPSHARDINGRULESPASS
#endif // GEN_PASS_DECL_DROPSHARDINGRULESPASS
#ifdef GEN_PASS_DEF_DROPSHARDINGRULESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createDropShardingRulesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class DropShardingRulesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = DropShardingRulesPassBase;

  DropShardingRulesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DropShardingRulesPassBase(const DropShardingRulesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  DropShardingRulesPassBase& operator=(const DropShardingRulesPassBase &) = delete;
  DropShardingRulesPassBase(DropShardingRulesPassBase &&) = delete;
  DropShardingRulesPassBase& operator=(DropShardingRulesPassBase &&) = delete;
  ~DropShardingRulesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-drop-sharding-rules");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-drop-sharding-rules"; }

  ::llvm::StringRef getDescription() const override { return "Drops `OpShardingRuleAttr` from all registered ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DropShardingRulesPass");
  }
  ::llvm::StringRef getName() const override { return "DropShardingRulesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DropShardingRulesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createDropShardingRulesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createDropShardingRulesPass() {
  return impl::createDropShardingRulesPass();
}
#undef GEN_PASS_DEF_DROPSHARDINGRULESPASS
#endif // GEN_PASS_DEF_DROPSHARDINGRULESPASS

//===----------------------------------------------------------------------===//
// InsertExplicitReshardsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_INSERTEXPLICITRESHARDSPASS
std::unique_ptr<::mlir::Pass> createInsertExplicitReshardsPass();
#undef GEN_PASS_DECL_INSERTEXPLICITRESHARDSPASS
#endif // GEN_PASS_DECL_INSERTEXPLICITRESHARDSPASS
#ifdef GEN_PASS_DEF_INSERTEXPLICITRESHARDSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createInsertExplicitReshardsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class InsertExplicitReshardsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = InsertExplicitReshardsPassBase;

  InsertExplicitReshardsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  InsertExplicitReshardsPassBase(const InsertExplicitReshardsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  InsertExplicitReshardsPassBase& operator=(const InsertExplicitReshardsPassBase &) = delete;
  InsertExplicitReshardsPassBase(InsertExplicitReshardsPassBase &&) = delete;
  InsertExplicitReshardsPassBase& operator=(InsertExplicitReshardsPassBase &&) = delete;
  ~InsertExplicitReshardsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-insert-explicit-reshards");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-insert-explicit-reshards"; }

  ::llvm::StringRef getDescription() const override { return "Inserts explicit reshards to make all operations have compatible shardings."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InsertExplicitReshardsPass");
  }
  ::llvm::StringRef getName() const override { return "InsertExplicitReshardsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InsertExplicitReshardsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createInsertExplicitReshardsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createInsertExplicitReshardsPass() {
  return impl::createInsertExplicitReshardsPass();
}
#undef GEN_PASS_DEF_INSERTEXPLICITRESHARDSPASS
#endif // GEN_PASS_DEF_INSERTEXPLICITRESHARDSPASS

//===----------------------------------------------------------------------===//
// RemoveShardingGroupsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_REMOVESHARDINGGROUPSPASS
std::unique_ptr<::mlir::Pass> createRemoveShardingGroupsPass();
#undef GEN_PASS_DECL_REMOVESHARDINGGROUPSPASS
#endif // GEN_PASS_DECL_REMOVESHARDINGGROUPSPASS
#ifdef GEN_PASS_DEF_REMOVESHARDINGGROUPSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createRemoveShardingGroupsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class RemoveShardingGroupsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = RemoveShardingGroupsPassBase;

  RemoveShardingGroupsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  RemoveShardingGroupsPassBase(const RemoveShardingGroupsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  RemoveShardingGroupsPassBase& operator=(const RemoveShardingGroupsPassBase &) = delete;
  RemoveShardingGroupsPassBase(RemoveShardingGroupsPassBase &&) = delete;
  RemoveShardingGroupsPassBase& operator=(RemoveShardingGroupsPassBase &&) = delete;
  ~RemoveShardingGroupsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-remove-sharding-groups");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-remove-sharding-groups"; }

  ::llvm::StringRef getDescription() const override { return "Removes ShardingGroupOps after propagation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveShardingGroupsPass");
  }
  ::llvm::StringRef getName() const override { return "RemoveShardingGroupsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemoveShardingGroupsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createRemoveShardingGroupsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createRemoveShardingGroupsPass() {
  return impl::createRemoveShardingGroupsPass();
}
#undef GEN_PASS_DEF_REMOVESHARDINGGROUPSPASS
#endif // GEN_PASS_DEF_REMOVESHARDINGGROUPSPASS

//===----------------------------------------------------------------------===//
// ReshardToCollectivesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_RESHARDTOCOLLECTIVESPASS
std::unique_ptr<::mlir::Pass> createReshardToCollectivesPass();
#undef GEN_PASS_DECL_RESHARDTOCOLLECTIVESPASS
#endif // GEN_PASS_DECL_RESHARDTOCOLLECTIVESPASS
#ifdef GEN_PASS_DEF_RESHARDTOCOLLECTIVESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createReshardToCollectivesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ReshardToCollectivesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ReshardToCollectivesPassBase;

  ReshardToCollectivesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ReshardToCollectivesPassBase(const ReshardToCollectivesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ReshardToCollectivesPassBase& operator=(const ReshardToCollectivesPassBase &) = delete;
  ReshardToCollectivesPassBase(ReshardToCollectivesPassBase &&) = delete;
  ReshardToCollectivesPassBase& operator=(ReshardToCollectivesPassBase &&) = delete;
  ~ReshardToCollectivesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-reshard-to-collectives");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-reshard-to-collectives"; }

  ::llvm::StringRef getDescription() const override { return "Converts ReshardOp into various Shardy collective ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ReshardToCollectivesPass");
  }
  ::llvm::StringRef getName() const override { return "ReshardToCollectivesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ReshardToCollectivesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createReshardToCollectivesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createReshardToCollectivesPass() {
  return impl::createReshardToCollectivesPass();
}
#undef GEN_PASS_DEF_RESHARDTOCOLLECTIVESPASS
#endif // GEN_PASS_DEF_RESHARDTOCOLLECTIVESPASS

//===----------------------------------------------------------------------===//
// ShardingConstraintToReshardPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SHARDINGCONSTRAINTTORESHARDPASS
std::unique_ptr<::mlir::Pass> createShardingConstraintToReshardPass();
#undef GEN_PASS_DECL_SHARDINGCONSTRAINTTORESHARDPASS
#endif // GEN_PASS_DECL_SHARDINGCONSTRAINTTORESHARDPASS
#ifdef GEN_PASS_DEF_SHARDINGCONSTRAINTTORESHARDPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createShardingConstraintToReshardPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class ShardingConstraintToReshardPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShardingConstraintToReshardPassBase;

  ShardingConstraintToReshardPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShardingConstraintToReshardPassBase(const ShardingConstraintToReshardPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ShardingConstraintToReshardPassBase& operator=(const ShardingConstraintToReshardPassBase &) = delete;
  ShardingConstraintToReshardPassBase(ShardingConstraintToReshardPassBase &&) = delete;
  ShardingConstraintToReshardPassBase& operator=(ShardingConstraintToReshardPassBase &&) = delete;
  ~ShardingConstraintToReshardPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-sharding-constraint-to-reshard");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-sharding-constraint-to-reshard"; }

  ::llvm::StringRef getDescription() const override { return "Converts ShardingConstraintOp into ReshardOp."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShardingConstraintToReshardPass");
  }
  ::llvm::StringRef getName() const override { return "ShardingConstraintToReshardPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShardingConstraintToReshardPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createShardingConstraintToReshardPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createShardingConstraintToReshardPass() {
  return impl::createShardingConstraintToReshardPass();
}
#undef GEN_PASS_DEF_SHARDINGCONSTRAINTTORESHARDPASS
#endif // GEN_PASS_DEF_SHARDINGCONSTRAINTTORESHARDPASS

//===----------------------------------------------------------------------===//
// SinkDataFlowEdgesPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_SINKDATAFLOWEDGESPASS
std::unique_ptr<::mlir::Pass> createSinkDataFlowEdgesPass();
#undef GEN_PASS_DECL_SINKDATAFLOWEDGESPASS
#endif // GEN_PASS_DECL_SINKDATAFLOWEDGESPASS
#ifdef GEN_PASS_DEF_SINKDATAFLOWEDGESPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createSinkDataFlowEdgesPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class SinkDataFlowEdgesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = SinkDataFlowEdgesPassBase;

  SinkDataFlowEdgesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SinkDataFlowEdgesPassBase(const SinkDataFlowEdgesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  SinkDataFlowEdgesPassBase& operator=(const SinkDataFlowEdgesPassBase &) = delete;
  SinkDataFlowEdgesPassBase(SinkDataFlowEdgesPassBase &&) = delete;
  SinkDataFlowEdgesPassBase& operator=(SinkDataFlowEdgesPassBase &&) = delete;
  ~SinkDataFlowEdgesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-sink-data-flow-edges");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-sink-data-flow-edges"; }

  ::llvm::StringRef getDescription() const override { return "Sinks all `DataFlowEdgeOp` into their input."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SinkDataFlowEdgesPass");
  }
  ::llvm::StringRef getName() const override { return "SinkDataFlowEdgesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SinkDataFlowEdgesPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createSinkDataFlowEdgesPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createSinkDataFlowEdgesPass() {
  return impl::createSinkDataFlowEdgesPass();
}
#undef GEN_PASS_DEF_SINKDATAFLOWEDGESPASS
#endif // GEN_PASS_DEF_SINKDATAFLOWEDGESPASS

//===----------------------------------------------------------------------===//
// UpdateNonDivisibleInputOutputShardingsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_UPDATENONDIVISIBLEINPUTOUTPUTSHARDINGSPASS
std::unique_ptr<::mlir::Pass> createUpdateNonDivisibleInputOutputShardingsPass();
#undef GEN_PASS_DECL_UPDATENONDIVISIBLEINPUTOUTPUTSHARDINGSPASS
#endif // GEN_PASS_DECL_UPDATENONDIVISIBLEINPUTOUTPUTSHARDINGSPASS
#ifdef GEN_PASS_DEF_UPDATENONDIVISIBLEINPUTOUTPUTSHARDINGSPASS

namespace impl {
  std::unique_ptr<::mlir::Pass> createUpdateNonDivisibleInputOutputShardingsPass();
} // namespace impl
namespace impl {

template <typename DerivedT>
class UpdateNonDivisibleInputOutputShardingsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = UpdateNonDivisibleInputOutputShardingsPassBase;

  UpdateNonDivisibleInputOutputShardingsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  UpdateNonDivisibleInputOutputShardingsPassBase(const UpdateNonDivisibleInputOutputShardingsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  UpdateNonDivisibleInputOutputShardingsPassBase& operator=(const UpdateNonDivisibleInputOutputShardingsPassBase &) = delete;
  UpdateNonDivisibleInputOutputShardingsPassBase(UpdateNonDivisibleInputOutputShardingsPassBase &&) = delete;
  UpdateNonDivisibleInputOutputShardingsPassBase& operator=(UpdateNonDivisibleInputOutputShardingsPassBase &&) = delete;
  ~UpdateNonDivisibleInputOutputShardingsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-update-non-divisible-input-output-shardings");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-update-non-divisible-input-output-shardings"; }

  ::llvm::StringRef getDescription() const override { return "Makes FuncOp inputs/outputs evenly sharded, removing any need for padding due to non-divisible shardings."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UpdateNonDivisibleInputOutputShardingsPass");
  }
  ::llvm::StringRef getName() const override { return "UpdateNonDivisibleInputOutputShardingsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UpdateNonDivisibleInputOutputShardingsPassBase<DerivedT>)

protected:
private:

  friend std::unique_ptr<::mlir::Pass> createUpdateNonDivisibleInputOutputShardingsPass() {
    return std::make_unique<DerivedT>();
  }
};
} // namespace impl

std::unique_ptr<::mlir::Pass> createUpdateNonDivisibleInputOutputShardingsPass() {
  return impl::createUpdateNonDivisibleInputOutputShardingsPass();
}
#undef GEN_PASS_DEF_UPDATENONDIVISIBLEINPUTOUTPUTSHARDINGSPASS
#endif // GEN_PASS_DEF_UPDATENONDIVISIBLEINPUTOUTPUTSHARDINGSPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// CloseShardingsPass Registration
//===----------------------------------------------------------------------===//

inline void registerCloseShardingsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCloseShardingsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerCloseShardingsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createCloseShardingsPass();
  });
}

//===----------------------------------------------------------------------===//
// DropShardingRulesPass Registration
//===----------------------------------------------------------------------===//

inline void registerDropShardingRulesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createDropShardingRulesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDropShardingRulesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createDropShardingRulesPass();
  });
}

//===----------------------------------------------------------------------===//
// InsertExplicitReshardsPass Registration
//===----------------------------------------------------------------------===//

inline void registerInsertExplicitReshardsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createInsertExplicitReshardsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerInsertExplicitReshardsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createInsertExplicitReshardsPass();
  });
}

//===----------------------------------------------------------------------===//
// RemoveShardingGroupsPass Registration
//===----------------------------------------------------------------------===//

inline void registerRemoveShardingGroupsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createRemoveShardingGroupsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerRemoveShardingGroupsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createRemoveShardingGroupsPass();
  });
}

//===----------------------------------------------------------------------===//
// ReshardToCollectivesPass Registration
//===----------------------------------------------------------------------===//

inline void registerReshardToCollectivesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createReshardToCollectivesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerReshardToCollectivesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createReshardToCollectivesPass();
  });
}

//===----------------------------------------------------------------------===//
// ShardingConstraintToReshardPass Registration
//===----------------------------------------------------------------------===//

inline void registerShardingConstraintToReshardPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShardingConstraintToReshardPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerShardingConstraintToReshardPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createShardingConstraintToReshardPass();
  });
}

//===----------------------------------------------------------------------===//
// SinkDataFlowEdgesPass Registration
//===----------------------------------------------------------------------===//

inline void registerSinkDataFlowEdgesPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSinkDataFlowEdgesPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerSinkDataFlowEdgesPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createSinkDataFlowEdgesPass();
  });
}

//===----------------------------------------------------------------------===//
// UpdateNonDivisibleInputOutputShardingsPass Registration
//===----------------------------------------------------------------------===//

inline void registerUpdateNonDivisibleInputOutputShardingsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createUpdateNonDivisibleInputOutputShardingsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerUpdateNonDivisibleInputOutputShardingsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return createUpdateNonDivisibleInputOutputShardingsPass();
  });
}

//===----------------------------------------------------------------------===//
// SdyExport Registration
//===----------------------------------------------------------------------===//

inline void registerSdyExportPasses() {
  registerCloseShardingsPass();
  registerDropShardingRulesPass();
  registerInsertExplicitReshardsPass();
  registerRemoveShardingGroupsPass();
  registerReshardToCollectivesPass();
  registerShardingConstraintToReshardPass();
  registerSinkDataFlowEdgesPass();
  registerUpdateNonDivisibleInputOutputShardingsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class CloseShardingsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = CloseShardingsPassBase;

  CloseShardingsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  CloseShardingsPassBase(const CloseShardingsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  CloseShardingsPassBase& operator=(const CloseShardingsPassBase &) = delete;
  CloseShardingsPassBase(CloseShardingsPassBase &&) = delete;
  CloseShardingsPassBase& operator=(CloseShardingsPassBase &&) = delete;
  ~CloseShardingsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-close-shardings");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-close-shardings"; }

  ::llvm::StringRef getDescription() const override { return "Closes tensor shardings and drops replicated axes."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("CloseShardingsPass");
  }
  ::llvm::StringRef getName() const override { return "CloseShardingsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(CloseShardingsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class DropShardingRulesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = DropShardingRulesPassBase;

  DropShardingRulesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DropShardingRulesPassBase(const DropShardingRulesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  DropShardingRulesPassBase& operator=(const DropShardingRulesPassBase &) = delete;
  DropShardingRulesPassBase(DropShardingRulesPassBase &&) = delete;
  DropShardingRulesPassBase& operator=(DropShardingRulesPassBase &&) = delete;
  ~DropShardingRulesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-drop-sharding-rules");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-drop-sharding-rules"; }

  ::llvm::StringRef getDescription() const override { return "Drops `OpShardingRuleAttr` from all registered ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DropShardingRulesPass");
  }
  ::llvm::StringRef getName() const override { return "DropShardingRulesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DropShardingRulesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class InsertExplicitReshardsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = InsertExplicitReshardsPassBase;

  InsertExplicitReshardsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  InsertExplicitReshardsPassBase(const InsertExplicitReshardsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  InsertExplicitReshardsPassBase& operator=(const InsertExplicitReshardsPassBase &) = delete;
  InsertExplicitReshardsPassBase(InsertExplicitReshardsPassBase &&) = delete;
  InsertExplicitReshardsPassBase& operator=(InsertExplicitReshardsPassBase &&) = delete;
  ~InsertExplicitReshardsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-insert-explicit-reshards");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-insert-explicit-reshards"; }

  ::llvm::StringRef getDescription() const override { return "Inserts explicit reshards to make all operations have compatible shardings."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("InsertExplicitReshardsPass");
  }
  ::llvm::StringRef getName() const override { return "InsertExplicitReshardsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(InsertExplicitReshardsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class RemoveShardingGroupsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = RemoveShardingGroupsPassBase;

  RemoveShardingGroupsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  RemoveShardingGroupsPassBase(const RemoveShardingGroupsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  RemoveShardingGroupsPassBase& operator=(const RemoveShardingGroupsPassBase &) = delete;
  RemoveShardingGroupsPassBase(RemoveShardingGroupsPassBase &&) = delete;
  RemoveShardingGroupsPassBase& operator=(RemoveShardingGroupsPassBase &&) = delete;
  ~RemoveShardingGroupsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-remove-sharding-groups");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-remove-sharding-groups"; }

  ::llvm::StringRef getDescription() const override { return "Removes ShardingGroupOps after propagation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("RemoveShardingGroupsPass");
  }
  ::llvm::StringRef getName() const override { return "RemoveShardingGroupsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(RemoveShardingGroupsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ReshardToCollectivesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ReshardToCollectivesPassBase;

  ReshardToCollectivesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ReshardToCollectivesPassBase(const ReshardToCollectivesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ReshardToCollectivesPassBase& operator=(const ReshardToCollectivesPassBase &) = delete;
  ReshardToCollectivesPassBase(ReshardToCollectivesPassBase &&) = delete;
  ReshardToCollectivesPassBase& operator=(ReshardToCollectivesPassBase &&) = delete;
  ~ReshardToCollectivesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-reshard-to-collectives");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-reshard-to-collectives"; }

  ::llvm::StringRef getDescription() const override { return "Converts ReshardOp into various Shardy collective ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ReshardToCollectivesPass");
  }
  ::llvm::StringRef getName() const override { return "ReshardToCollectivesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ReshardToCollectivesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ShardingConstraintToReshardPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = ShardingConstraintToReshardPassBase;

  ShardingConstraintToReshardPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  ShardingConstraintToReshardPassBase(const ShardingConstraintToReshardPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  ShardingConstraintToReshardPassBase& operator=(const ShardingConstraintToReshardPassBase &) = delete;
  ShardingConstraintToReshardPassBase(ShardingConstraintToReshardPassBase &&) = delete;
  ShardingConstraintToReshardPassBase& operator=(ShardingConstraintToReshardPassBase &&) = delete;
  ~ShardingConstraintToReshardPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-sharding-constraint-to-reshard");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-sharding-constraint-to-reshard"; }

  ::llvm::StringRef getDescription() const override { return "Converts ShardingConstraintOp into ReshardOp."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ShardingConstraintToReshardPass");
  }
  ::llvm::StringRef getName() const override { return "ShardingConstraintToReshardPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ShardingConstraintToReshardPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class SinkDataFlowEdgesPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = SinkDataFlowEdgesPassBase;

  SinkDataFlowEdgesPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  SinkDataFlowEdgesPassBase(const SinkDataFlowEdgesPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  SinkDataFlowEdgesPassBase& operator=(const SinkDataFlowEdgesPassBase &) = delete;
  SinkDataFlowEdgesPassBase(SinkDataFlowEdgesPassBase &&) = delete;
  SinkDataFlowEdgesPassBase& operator=(SinkDataFlowEdgesPassBase &&) = delete;
  ~SinkDataFlowEdgesPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-sink-data-flow-edges");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-sink-data-flow-edges"; }

  ::llvm::StringRef getDescription() const override { return "Sinks all `DataFlowEdgeOp` into their input."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("SinkDataFlowEdgesPass");
  }
  ::llvm::StringRef getName() const override { return "SinkDataFlowEdgesPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(SinkDataFlowEdgesPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class UpdateNonDivisibleInputOutputShardingsPassBase : public ::mlir::OperationPass<func::FuncOp> {
public:
  using Base = UpdateNonDivisibleInputOutputShardingsPassBase;

  UpdateNonDivisibleInputOutputShardingsPassBase() : ::mlir::OperationPass<func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  UpdateNonDivisibleInputOutputShardingsPassBase(const UpdateNonDivisibleInputOutputShardingsPassBase &other) : ::mlir::OperationPass<func::FuncOp>(other) {}
  UpdateNonDivisibleInputOutputShardingsPassBase& operator=(const UpdateNonDivisibleInputOutputShardingsPassBase &) = delete;
  UpdateNonDivisibleInputOutputShardingsPassBase(UpdateNonDivisibleInputOutputShardingsPassBase &&) = delete;
  UpdateNonDivisibleInputOutputShardingsPassBase& operator=(UpdateNonDivisibleInputOutputShardingsPassBase &&) = delete;
  ~UpdateNonDivisibleInputOutputShardingsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("sdy-update-non-divisible-input-output-shardings");
  }
  ::llvm::StringRef getArgument() const override { return "sdy-update-non-divisible-input-output-shardings"; }

  ::llvm::StringRef getDescription() const override { return "Makes FuncOp inputs/outputs evenly sharded, removing any need for padding due to non-divisible shardings."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("UpdateNonDivisibleInputOutputShardingsPass");
  }
  ::llvm::StringRef getName() const override { return "UpdateNonDivisibleInputOutputShardingsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<mlir::sdy::SdyDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(UpdateNonDivisibleInputOutputShardingsPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
