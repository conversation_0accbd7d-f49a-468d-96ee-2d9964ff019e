/*** Autogenerated by WIDL 10.8 from include/windows.graphics.imaging.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_graphics_imaging_h__
#define __windows_graphics_imaging_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer ABI::Windows::Graphics::Imaging::IBitmapBuffer
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                interface IBitmapBuffer;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap ABI::Windows::Graphics::Imaging::ISoftwareBitmap
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                interface ISoftwareBitmap;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory ABI::Windows::Graphics::Imaging::ISoftwareBitmapFactory
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                interface ISoftwareBitmapFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics ABI::Windows::Graphics::Imaging::ISoftwareBitmapStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                interface ISoftwareBitmapStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CBitmapBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CBitmapBuffer_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                class BitmapBuffer;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CBitmapBuffer __x_ABI_CWindows_CGraphics_CImaging_CBitmapBuffer;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CImaging_CBitmapBuffer_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CSoftwareBitmap_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CSoftwareBitmap_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                class SoftwareBitmap;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CSoftwareBitmap __x_ABI_CWindows_CGraphics_CImaging_CSoftwareBitmap;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CImaging_CSoftwareBitmap_FWD_DEFINED__ */

#ifndef ____FIIterable_1_BitmapPixelFormat_FWD_DEFINED__
#define ____FIIterable_1_BitmapPixelFormat_FWD_DEFINED__
typedef interface __FIIterable_1_BitmapPixelFormat __FIIterable_1_BitmapPixelFormat;
#ifdef __cplusplus
#define __FIIterable_1_BitmapPixelFormat ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Graphics::Imaging::SoftwareBitmap* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_BitmapPixelFormat_FWD_DEFINED__
#define ____FIIterator_1_BitmapPixelFormat_FWD_DEFINED__
typedef interface __FIIterator_1_BitmapPixelFormat __FIIterator_1_BitmapPixelFormat;
#ifdef __cplusplus
#define __FIIterator_1_BitmapPixelFormat ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Graphics::Imaging::SoftwareBitmap* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_BitmapPixelFormat_FWD_DEFINED__
#define ____FIVectorView_1_BitmapPixelFormat_FWD_DEFINED__
typedef interface __FIVectorView_1_BitmapPixelFormat __FIVectorView_1_BitmapPixelFormat;
#ifdef __cplusplus
#define __FIVectorView_1_BitmapPixelFormat ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Imaging::SoftwareBitmap* >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_BitmapBounds_FWD_DEFINED__
#define ____FIReference_1_BitmapBounds_FWD_DEFINED__
typedef interface __FIReference_1_BitmapBounds __FIReference_1_BitmapBounds;
#ifdef __cplusplus
#define __FIReference_1_BitmapBounds ABI::Windows::Foundation::IReference<ABI::Windows::Graphics::Imaging::BitmapBounds >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.graphics.directx.direct3d11.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGraphics_CImaging_CBitmapBufferAccessMode __x_ABI_CWindows_CGraphics_CImaging_CBitmapBufferAccessMode;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds __x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                typedef struct BitmapBounds BitmapBounds;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CBitmapPlaneDescription __x_ABI_CWindows_CGraphics_CImaging_CBitmapPlaneDescription;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                typedef struct BitmapPlaneDescription BitmapPlaneDescription;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CBitmapSize __x_ABI_CWindows_CGraphics_CImaging_CBitmapSize;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                typedef struct BitmapSize BitmapSize;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer ABI::Windows::Graphics::Imaging::IBitmapBuffer
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                interface IBitmapBuffer;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap ABI::Windows::Graphics::Imaging::ISoftwareBitmap
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                interface ISoftwareBitmap;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory ABI::Windows::Graphics::Imaging::ISoftwareBitmapFactory
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                interface ISoftwareBitmapFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics ABI::Windows::Graphics::Imaging::ISoftwareBitmapStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                interface ISoftwareBitmapStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_BitmapPixelFormat_FWD_DEFINED__
#define ____FIIterable_1_BitmapPixelFormat_FWD_DEFINED__
typedef interface __FIIterable_1_BitmapPixelFormat __FIIterable_1_BitmapPixelFormat;
#ifdef __cplusplus
#define __FIIterable_1_BitmapPixelFormat ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
#define ____FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Graphics::Imaging::SoftwareBitmap* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_BitmapPixelFormat_FWD_DEFINED__
#define ____FIIterator_1_BitmapPixelFormat_FWD_DEFINED__
typedef interface __FIIterator_1_BitmapPixelFormat __FIIterator_1_BitmapPixelFormat;
#ifdef __cplusplus
#define __FIIterator_1_BitmapPixelFormat ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
#define ____FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Graphics::Imaging::SoftwareBitmap* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_BitmapPixelFormat_FWD_DEFINED__
#define ____FIVectorView_1_BitmapPixelFormat_FWD_DEFINED__
typedef interface __FIVectorView_1_BitmapPixelFormat __FIVectorView_1_BitmapPixelFormat;
#ifdef __cplusplus
#define __FIVectorView_1_BitmapPixelFormat ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_BitmapBounds_FWD_DEFINED__
#define ____FIReference_1_BitmapBounds_FWD_DEFINED__
typedef interface __FIReference_1_BitmapBounds __FIReference_1_BitmapBounds;
#ifdef __cplusplus
#define __FIReference_1_BitmapBounds ABI::Windows::Foundation::IReference<ABI::Windows::Graphics::Imaging::BitmapBounds >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                enum BitmapAlphaMode {
                    BitmapAlphaMode_Premultiplied = 0,
                    BitmapAlphaMode_Straight = 1,
                    BitmapAlphaMode_Ignore = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode {
    BitmapAlphaMode_Premultiplied = 0,
    BitmapAlphaMode_Straight = 1,
    BitmapAlphaMode_Ignore = 2
};
#ifdef WIDL_using_Windows_Graphics_Imaging
#define BitmapAlphaMode __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                enum BitmapBufferAccessMode {
                    BitmapBufferAccessMode_Read = 0,
                    BitmapBufferAccessMode_ReadWrite = 1,
                    BitmapBufferAccessMode_Write = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGraphics_CImaging_CBitmapBufferAccessMode {
    BitmapBufferAccessMode_Read = 0,
    BitmapBufferAccessMode_ReadWrite = 1,
    BitmapBufferAccessMode_Write = 2
};
#ifdef WIDL_using_Windows_Graphics_Imaging
#define BitmapBufferAccessMode __x_ABI_CWindows_CGraphics_CImaging_CBitmapBufferAccessMode
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                enum BitmapPixelFormat {
                    BitmapPixelFormat_Unknown = 0,
                    BitmapPixelFormat_Rgba16 = 12,
                    BitmapPixelFormat_Rgba8 = 30,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BitmapPixelFormat_Gray16 = 57,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BitmapPixelFormat_Gray8 = 62,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                    BitmapPixelFormat_Bgra8 = 87,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BitmapPixelFormat_Nv12 = 103,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
                    BitmapPixelFormat_P010 = 104,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    BitmapPixelFormat_Yuy2 = 107
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat {
    BitmapPixelFormat_Unknown = 0,
    BitmapPixelFormat_Rgba16 = 12,
    BitmapPixelFormat_Rgba8 = 30,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BitmapPixelFormat_Gray16 = 57,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BitmapPixelFormat_Gray8 = 62,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
    BitmapPixelFormat_Bgra8 = 87,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BitmapPixelFormat_Nv12 = 103,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
    BitmapPixelFormat_P010 = 104,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    BitmapPixelFormat_Yuy2 = 107
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
};
#ifdef WIDL_using_Windows_Graphics_Imaging
#define BitmapPixelFormat __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                struct BitmapBounds {
                    UINT32 X;
                    UINT32 Y;
                    UINT32 Width;
                    UINT32 Height;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds {
    UINT32 X;
    UINT32 Y;
    UINT32 Width;
    UINT32 Height;
};
#ifdef WIDL_using_Windows_Graphics_Imaging
#define BitmapBounds __x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                struct BitmapPlaneDescription {
                    INT32 StartIndex;
                    INT32 Width;
                    INT32 Height;
                    INT32 Stride;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CImaging_CBitmapPlaneDescription {
    INT32 StartIndex;
    INT32 Width;
    INT32 Height;
    INT32 Stride;
};
#ifdef WIDL_using_Windows_Graphics_Imaging
#define BitmapPlaneDescription __x_ABI_CWindows_CGraphics_CImaging_CBitmapPlaneDescription
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                struct BitmapSize {
                    UINT32 Width;
                    UINT32 Height;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CImaging_CBitmapSize {
    UINT32 Width;
    UINT32 Height;
};
#ifdef WIDL_using_Windows_Graphics_Imaging
#define BitmapSize __x_ABI_CWindows_CGraphics_CImaging_CBitmapSize
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IBitmapBuffer interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer, 0xa53e04c4, 0x399c, 0x438c, 0xb2,0x8f, 0xa6,0x3a,0x6b,0x83,0xd1,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                MIDL_INTERFACE("a53e04c4-399c-438c-b28f-a63a6b83d1a1")
                IBitmapBuffer : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetPlaneCount(
                        INT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetPlaneDescription(
                        INT32 index,
                        ABI::Windows::Graphics::Imaging::BitmapPlaneDescription *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer, 0xa53e04c4, 0x399c, 0x438c, 0xb2,0x8f, 0xa6,0x3a,0x6b,0x83,0xd1,0xa1)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer *This,
        TrustLevel *trustLevel);

    /*** IBitmapBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPlaneCount)(
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetPlaneDescription)(
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer *This,
        INT32 index,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPlaneDescription *value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBufferVtbl;

interface __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBitmapBuffer methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetPlaneCount(This,value) (This)->lpVtbl->GetPlaneCount(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetPlaneDescription(This,index,value) (This)->lpVtbl->GetPlaneDescription(This,index,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_QueryInterface(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_AddRef(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_Release(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetIids(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetTrustLevel(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBitmapBuffer methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetPlaneCount(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer* This,INT32 *value) {
    return This->lpVtbl->GetPlaneCount(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetPlaneDescription(__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer* This,INT32 index,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPlaneDescription *value) {
    return This->lpVtbl->GetPlaneDescription(This,index,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Imaging
#define IID_IBitmapBuffer IID___x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer
#define IBitmapBufferVtbl __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBufferVtbl
#define IBitmapBuffer __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer
#define IBitmapBuffer_QueryInterface __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_QueryInterface
#define IBitmapBuffer_AddRef __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_AddRef
#define IBitmapBuffer_Release __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_Release
#define IBitmapBuffer_GetIids __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetIids
#define IBitmapBuffer_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetRuntimeClassName
#define IBitmapBuffer_GetTrustLevel __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetTrustLevel
#define IBitmapBuffer_GetPlaneCount __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetPlaneCount
#define IBitmapBuffer_GetPlaneDescription __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_GetPlaneDescription
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISoftwareBitmap interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap, 0x689e0708, 0x7eef, 0x483f, 0x96,0x3f, 0xda,0x93,0x88,0x18,0xe0,0x73);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                MIDL_INTERFACE("689e0708-7eef-483f-963f-da938818e073")
                ISoftwareBitmap : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_BitmapPixelFormat(
                        ABI::Windows::Graphics::Imaging::BitmapPixelFormat *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BitmapAlphaMode(
                        ABI::Windows::Graphics::Imaging::BitmapAlphaMode *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PixelWidth(
                        INT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PixelHeight(
                        INT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsReadOnly(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_DpiX(
                        DOUBLE value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DpiX(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_DpiY(
                        DOUBLE value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DpiY(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE LockBuffer(
                        ABI::Windows::Graphics::Imaging::BitmapBufferAccessMode mode,
                        ABI::Windows::Graphics::Imaging::IBitmapBuffer **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CopyTo(
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap *bitmap) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CopyFromBuffer(
                        ABI::Windows::Storage::Streams::IBuffer *buffer) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CopyToBuffer(
                        ABI::Windows::Storage::Streams::IBuffer *buffer) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetReadOnlyView(
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap, 0x689e0708, 0x7eef, 0x483f, 0x96,0x3f, 0xda,0x93,0x88,0x18,0xe0,0x73)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        TrustLevel *trustLevel);

    /*** ISoftwareBitmap methods ***/
    HRESULT (STDMETHODCALLTYPE *get_BitmapPixelFormat)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *value);

    HRESULT (STDMETHODCALLTYPE *get_BitmapAlphaMode)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode *value);

    HRESULT (STDMETHODCALLTYPE *get_PixelWidth)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_PixelHeight)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        INT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_IsReadOnly)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_DpiX)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *get_DpiX)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *put_DpiY)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *get_DpiY)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *LockBuffer)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapBufferAccessMode mode,
        __x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer **value);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *bitmap);

    HRESULT (STDMETHODCALLTYPE *CopyFromBuffer)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer);

    HRESULT (STDMETHODCALLTYPE *CopyToBuffer)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer);

    HRESULT (STDMETHODCALLTYPE *GetReadOnlyView)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapVtbl;

interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISoftwareBitmap methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_BitmapPixelFormat(This,value) (This)->lpVtbl->get_BitmapPixelFormat(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_BitmapAlphaMode(This,value) (This)->lpVtbl->get_BitmapAlphaMode(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_PixelWidth(This,value) (This)->lpVtbl->get_PixelWidth(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_PixelHeight(This,value) (This)->lpVtbl->get_PixelHeight(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_IsReadOnly(This,value) (This)->lpVtbl->get_IsReadOnly(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_put_DpiX(This,value) (This)->lpVtbl->put_DpiX(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_DpiX(This,value) (This)->lpVtbl->get_DpiX(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_put_DpiY(This,value) (This)->lpVtbl->put_DpiY(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_DpiY(This,value) (This)->lpVtbl->get_DpiY(This,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_LockBuffer(This,mode,value) (This)->lpVtbl->LockBuffer(This,mode,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyTo(This,bitmap) (This)->lpVtbl->CopyTo(This,bitmap)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyFromBuffer(This,buffer) (This)->lpVtbl->CopyFromBuffer(This,buffer)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyToBuffer(This,buffer) (This)->lpVtbl->CopyToBuffer(This,buffer)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetReadOnlyView(This,value) (This)->lpVtbl->GetReadOnlyView(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_QueryInterface(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_AddRef(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_Release(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetIids(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetTrustLevel(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISoftwareBitmap methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_BitmapPixelFormat(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *value) {
    return This->lpVtbl->get_BitmapPixelFormat(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_BitmapAlphaMode(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode *value) {
    return This->lpVtbl->get_BitmapAlphaMode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_PixelWidth(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,INT32 *value) {
    return This->lpVtbl->get_PixelWidth(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_PixelHeight(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,INT32 *value) {
    return This->lpVtbl->get_PixelHeight(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_IsReadOnly(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,boolean *value) {
    return This->lpVtbl->get_IsReadOnly(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_put_DpiX(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,DOUBLE value) {
    return This->lpVtbl->put_DpiX(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_DpiX(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,DOUBLE *value) {
    return This->lpVtbl->get_DpiX(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_put_DpiY(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,DOUBLE value) {
    return This->lpVtbl->put_DpiY(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_DpiY(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,DOUBLE *value) {
    return This->lpVtbl->get_DpiY(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_LockBuffer(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapBufferAccessMode mode,__x_ABI_CWindows_CGraphics_CImaging_CIBitmapBuffer **value) {
    return This->lpVtbl->LockBuffer(This,mode,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyTo(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *bitmap) {
    return This->lpVtbl->CopyTo(This,bitmap);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyFromBuffer(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer) {
    return This->lpVtbl->CopyFromBuffer(This,buffer);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyToBuffer(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer) {
    return This->lpVtbl->CopyToBuffer(This,buffer);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetReadOnlyView(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->GetReadOnlyView(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Imaging
#define IID_ISoftwareBitmap IID___x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap
#define ISoftwareBitmapVtbl __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapVtbl
#define ISoftwareBitmap __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap
#define ISoftwareBitmap_QueryInterface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_QueryInterface
#define ISoftwareBitmap_AddRef __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_AddRef
#define ISoftwareBitmap_Release __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_Release
#define ISoftwareBitmap_GetIids __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetIids
#define ISoftwareBitmap_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetRuntimeClassName
#define ISoftwareBitmap_GetTrustLevel __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetTrustLevel
#define ISoftwareBitmap_get_BitmapPixelFormat __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_BitmapPixelFormat
#define ISoftwareBitmap_get_BitmapAlphaMode __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_BitmapAlphaMode
#define ISoftwareBitmap_get_PixelWidth __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_PixelWidth
#define ISoftwareBitmap_get_PixelHeight __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_PixelHeight
#define ISoftwareBitmap_get_IsReadOnly __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_IsReadOnly
#define ISoftwareBitmap_put_DpiX __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_put_DpiX
#define ISoftwareBitmap_get_DpiX __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_DpiX
#define ISoftwareBitmap_put_DpiY __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_put_DpiY
#define ISoftwareBitmap_get_DpiY __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_get_DpiY
#define ISoftwareBitmap_LockBuffer __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_LockBuffer
#define ISoftwareBitmap_CopyTo __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyTo
#define ISoftwareBitmap_CopyFromBuffer __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyFromBuffer
#define ISoftwareBitmap_CopyToBuffer __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_CopyToBuffer
#define ISoftwareBitmap_GetReadOnlyView __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_GetReadOnlyView
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISoftwareBitmapFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory, 0xc99feb69, 0x2d62, 0x4d47, 0xa6,0xb3, 0x4f,0xdb,0x6a,0x07,0xfd,0xf8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                MIDL_INTERFACE("c99feb69-2d62-4d47-a6b3-4fdb6a07fdf8")
                ISoftwareBitmapFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Create(
                        ABI::Windows::Graphics::Imaging::BitmapPixelFormat format,
                        INT32 width,
                        INT32 height,
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWithAlpha(
                        ABI::Windows::Graphics::Imaging::BitmapPixelFormat format,
                        INT32 width,
                        INT32 height,
                        ABI::Windows::Graphics::Imaging::BitmapAlphaMode alpha,
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory, 0xc99feb69, 0x2d62, 0x4d47, 0xa6,0xb3, 0x4f,0xdb,0x6a,0x07,0xfd,0xf8)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory *This,
        TrustLevel *trustLevel);

    /*** ISoftwareBitmapFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,
        INT32 width,
        INT32 height,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    HRESULT (STDMETHODCALLTYPE *CreateWithAlpha)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,
        INT32 width,
        INT32 height,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode alpha,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactoryVtbl;

interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISoftwareBitmapFactory methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_Create(This,format,width,height,value) (This)->lpVtbl->Create(This,format,width,height,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_CreateWithAlpha(This,format,width,height,alpha,value) (This)->lpVtbl->CreateWithAlpha(This,format,width,height,alpha,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_QueryInterface(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_AddRef(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_Release(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetIids(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetTrustLevel(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISoftwareBitmapFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_Create(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,INT32 width,INT32 height,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->Create(This,format,width,height,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_CreateWithAlpha(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,INT32 width,INT32 height,__x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode alpha,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->CreateWithAlpha(This,format,width,height,alpha,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Imaging
#define IID_ISoftwareBitmapFactory IID___x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory
#define ISoftwareBitmapFactoryVtbl __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactoryVtbl
#define ISoftwareBitmapFactory __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory
#define ISoftwareBitmapFactory_QueryInterface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_QueryInterface
#define ISoftwareBitmapFactory_AddRef __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_AddRef
#define ISoftwareBitmapFactory_Release __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_Release
#define ISoftwareBitmapFactory_GetIids __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetIids
#define ISoftwareBitmapFactory_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetRuntimeClassName
#define ISoftwareBitmapFactory_GetTrustLevel __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_GetTrustLevel
#define ISoftwareBitmapFactory_Create __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_Create
#define ISoftwareBitmapFactory_CreateWithAlpha __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_CreateWithAlpha
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISoftwareBitmapStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics, 0xdf0385db, 0x672f, 0x4a9d, 0x80,0x6e, 0xc2,0x44,0x2f,0x34,0x3e,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Imaging {
                MIDL_INTERFACE("df0385db-672f-4a9d-806e-c2442f343e86")
                ISoftwareBitmapStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Copy(
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap *source,
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Convert(
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap *source,
                        ABI::Windows::Graphics::Imaging::BitmapPixelFormat format,
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE ConvertWithAlpha(
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap *source,
                        ABI::Windows::Graphics::Imaging::BitmapPixelFormat format,
                        ABI::Windows::Graphics::Imaging::BitmapAlphaMode alpha,
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateCopyFromBuffer(
                        ABI::Windows::Storage::Streams::IBuffer *source,
                        ABI::Windows::Graphics::Imaging::BitmapPixelFormat format,
                        INT32 width,
                        INT32 height,
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateCopyWithAlphaFromBuffer(
                        ABI::Windows::Storage::Streams::IBuffer *source,
                        ABI::Windows::Graphics::Imaging::BitmapPixelFormat format,
                        INT32 width,
                        INT32 height,
                        ABI::Windows::Graphics::Imaging::BitmapAlphaMode alpha,
                        ABI::Windows::Graphics::Imaging::ISoftwareBitmap **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateCopyFromSurfaceAsync(
                        ABI::Windows::Graphics::DirectX::Direct3D11::IDirect3DSurface *surface,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateCopyWithAlphaFromSurfaceAsync(
                        ABI::Windows::Graphics::DirectX::Direct3D11::IDirect3DSurface *surface,
                        ABI::Windows::Graphics::Imaging::BitmapAlphaMode alpha,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics, 0xdf0385db, 0x672f, 0x4a9d, 0x80,0x6e, 0xc2,0x44,0x2f,0x34,0x3e,0x86)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        TrustLevel *trustLevel);

    /*** ISoftwareBitmapStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *Copy)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *source,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    HRESULT (STDMETHODCALLTYPE *Convert)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *source,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    HRESULT (STDMETHODCALLTYPE *ConvertWithAlpha)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *source,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode alpha,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    HRESULT (STDMETHODCALLTYPE *CreateCopyFromBuffer)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *source,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,
        INT32 width,
        INT32 height,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    HRESULT (STDMETHODCALLTYPE *CreateCopyWithAlphaFromBuffer)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *source,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,
        INT32 width,
        INT32 height,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode alpha,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    HRESULT (STDMETHODCALLTYPE *CreateCopyFromSurfaceAsync)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DSurface *surface,
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap **value);

    HRESULT (STDMETHODCALLTYPE *CreateCopyWithAlphaFromSurfaceAsync)(
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DSurface *surface,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode alpha,
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap **value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStaticsVtbl;

interface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISoftwareBitmapStatics methods ***/
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Copy(This,source,value) (This)->lpVtbl->Copy(This,source,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Convert(This,source,format,value) (This)->lpVtbl->Convert(This,source,format,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_ConvertWithAlpha(This,source,format,alpha,value) (This)->lpVtbl->ConvertWithAlpha(This,source,format,alpha,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyFromBuffer(This,source,format,width,height,value) (This)->lpVtbl->CreateCopyFromBuffer(This,source,format,width,height,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyWithAlphaFromBuffer(This,source,format,width,height,alpha,value) (This)->lpVtbl->CreateCopyWithAlphaFromBuffer(This,source,format,width,height,alpha,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyFromSurfaceAsync(This,surface,value) (This)->lpVtbl->CreateCopyFromSurfaceAsync(This,surface,value)
#define __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyWithAlphaFromSurfaceAsync(This,surface,alpha,value) (This)->lpVtbl->CreateCopyWithAlphaFromSurfaceAsync(This,surface,alpha,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_QueryInterface(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_AddRef(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Release(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetIids(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetTrustLevel(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISoftwareBitmapStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Copy(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *source,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->Copy(This,source,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Convert(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *source,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->Convert(This,source,format,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_ConvertWithAlpha(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap *source,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,__x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode alpha,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->ConvertWithAlpha(This,source,format,alpha,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyFromBuffer(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *source,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,INT32 width,INT32 height,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->CreateCopyFromBuffer(This,source,format,width,height,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyWithAlphaFromBuffer(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *source,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat format,INT32 width,INT32 height,__x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode alpha,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->CreateCopyWithAlphaFromBuffer(This,source,format,width,height,alpha,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyFromSurfaceAsync(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DSurface *surface,__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap **value) {
    return This->lpVtbl->CreateCopyFromSurfaceAsync(This,surface,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyWithAlphaFromSurfaceAsync(__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DSurface *surface,__x_ABI_CWindows_CGraphics_CImaging_CBitmapAlphaMode alpha,__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap **value) {
    return This->lpVtbl->CreateCopyWithAlphaFromSurfaceAsync(This,surface,alpha,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Imaging
#define IID_ISoftwareBitmapStatics IID___x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics
#define ISoftwareBitmapStaticsVtbl __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStaticsVtbl
#define ISoftwareBitmapStatics __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics
#define ISoftwareBitmapStatics_QueryInterface __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_QueryInterface
#define ISoftwareBitmapStatics_AddRef __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_AddRef
#define ISoftwareBitmapStatics_Release __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Release
#define ISoftwareBitmapStatics_GetIids __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetIids
#define ISoftwareBitmapStatics_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetRuntimeClassName
#define ISoftwareBitmapStatics_GetTrustLevel __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_GetTrustLevel
#define ISoftwareBitmapStatics_Copy __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Copy
#define ISoftwareBitmapStatics_Convert __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_Convert
#define ISoftwareBitmapStatics_ConvertWithAlpha __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_ConvertWithAlpha
#define ISoftwareBitmapStatics_CreateCopyFromBuffer __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyFromBuffer
#define ISoftwareBitmapStatics_CreateCopyWithAlphaFromBuffer __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyWithAlphaFromBuffer
#define ISoftwareBitmapStatics_CreateCopyFromSurfaceAsync __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyFromSurfaceAsync
#define ISoftwareBitmapStatics_CreateCopyWithAlphaFromSurfaceAsync __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_CreateCopyWithAlphaFromSurfaceAsync
#endif /* WIDL_using_Windows_Graphics_Imaging */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmapStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Graphics.Imaging.BitmapBuffer
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Graphics_Imaging_BitmapBuffer_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Imaging_BitmapBuffer_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Imaging_BitmapBuffer[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','I','m','a','g','i','n','g','.','B','i','t','m','a','p','B','u','f','f','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Imaging_BitmapBuffer[] = L"Windows.Graphics.Imaging.BitmapBuffer";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Imaging_BitmapBuffer[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','I','m','a','g','i','n','g','.','B','i','t','m','a','p','B','u','f','f','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Imaging_BitmapBuffer_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Graphics.Imaging.SoftwareBitmap
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Graphics_Imaging_SoftwareBitmap_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Imaging_SoftwareBitmap_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Imaging_SoftwareBitmap[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','I','m','a','g','i','n','g','.','S','o','f','t','w','a','r','e','B','i','t','m','a','p',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Imaging_SoftwareBitmap[] = L"Windows.Graphics.Imaging.SoftwareBitmap";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Imaging_SoftwareBitmap[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','I','m','a','g','i','n','g','.','S','o','f','t','w','a','r','e','B','i','t','m','a','p',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Imaging_SoftwareBitmap_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > interface
 */
#ifndef ____FIIterable_1_BitmapPixelFormat_INTERFACE_DEFINED__
#define ____FIIterable_1_BitmapPixelFormat_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_BitmapPixelFormat, 0xe924d9ed, 0xa13e, 0x5bdb, 0x9e,0xd8, 0x65,0xa1,0x47,0x4d,0xc2,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("e924d9ed-a13e-5bdb-9ed8-65a1474dc274")
                IIterable<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > : IIterable_impl<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_BitmapPixelFormat, 0xe924d9ed, 0xa13e, 0x5bdb, 0x9e,0xd8, 0x65,0xa1,0x47,0x4d,0xc2,0x74)
#endif
#else
typedef struct __FIIterable_1_BitmapPixelFormatVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_BitmapPixelFormat *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_BitmapPixelFormat *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_BitmapPixelFormat *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_BitmapPixelFormat *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_BitmapPixelFormat *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_BitmapPixelFormat *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_BitmapPixelFormat *This,
        __FIIterator_1_BitmapPixelFormat **value);

    END_INTERFACE
} __FIIterable_1_BitmapPixelFormatVtbl;

interface __FIIterable_1_BitmapPixelFormat {
    CONST_VTBL __FIIterable_1_BitmapPixelFormatVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_BitmapPixelFormat_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_BitmapPixelFormat_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_BitmapPixelFormat_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_BitmapPixelFormat_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_BitmapPixelFormat_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_BitmapPixelFormat_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
#define __FIIterable_1_BitmapPixelFormat_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_BitmapPixelFormat_QueryInterface(__FIIterable_1_BitmapPixelFormat* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_BitmapPixelFormat_AddRef(__FIIterable_1_BitmapPixelFormat* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_BitmapPixelFormat_Release(__FIIterable_1_BitmapPixelFormat* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_BitmapPixelFormat_GetIids(__FIIterable_1_BitmapPixelFormat* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_BitmapPixelFormat_GetRuntimeClassName(__FIIterable_1_BitmapPixelFormat* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_BitmapPixelFormat_GetTrustLevel(__FIIterable_1_BitmapPixelFormat* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
static inline HRESULT __FIIterable_1_BitmapPixelFormat_First(__FIIterable_1_BitmapPixelFormat* This,__FIIterator_1_BitmapPixelFormat **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_BitmapPixelFormat IID___FIIterable_1_BitmapPixelFormat
#define IIterable_BitmapPixelFormatVtbl __FIIterable_1_BitmapPixelFormatVtbl
#define IIterable_BitmapPixelFormat __FIIterable_1_BitmapPixelFormat
#define IIterable_BitmapPixelFormat_QueryInterface __FIIterable_1_BitmapPixelFormat_QueryInterface
#define IIterable_BitmapPixelFormat_AddRef __FIIterable_1_BitmapPixelFormat_AddRef
#define IIterable_BitmapPixelFormat_Release __FIIterable_1_BitmapPixelFormat_Release
#define IIterable_BitmapPixelFormat_GetIids __FIIterable_1_BitmapPixelFormat_GetIids
#define IIterable_BitmapPixelFormat_GetRuntimeClassName __FIIterable_1_BitmapPixelFormat_GetRuntimeClassName
#define IIterable_BitmapPixelFormat_GetTrustLevel __FIIterable_1_BitmapPixelFormat_GetTrustLevel
#define IIterable_BitmapPixelFormat_First __FIIterable_1_BitmapPixelFormat_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_BitmapPixelFormat_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > interface
 */
#ifndef ____FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap, 0x22d3a30f, 0x0898, 0x5e94, 0x99,0xa3, 0xaf,0xa5,0x95,0x1d,0xfc,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("22d3a30f-0898-5e94-99a3-afa5951dfcd4")
                IIterable<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Imaging::SoftwareBitmap*, ABI::Windows::Graphics::Imaging::ISoftwareBitmap* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap, 0x22d3a30f, 0x0898, 0x5e94, 0x99,0xa3, 0xaf,0xa5,0x95,0x1d,0xfc,0xd4)
#endif
#else
typedef struct __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap **value);

    END_INTERFACE
} __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl;

interface __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap {
    CONST_VTBL __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
#define __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface(__FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef(__FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release(__FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids(__FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName(__FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel(__FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_First(__FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_SoftwareBitmap IID___FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap
#define IIterable_SoftwareBitmapVtbl __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl
#define IIterable_SoftwareBitmap __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap
#define IIterable_SoftwareBitmap_QueryInterface __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface
#define IIterable_SoftwareBitmap_AddRef __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef
#define IIterable_SoftwareBitmap_Release __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release
#define IIterable_SoftwareBitmap_GetIids __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids
#define IIterable_SoftwareBitmap_GetRuntimeClassName __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName
#define IIterable_SoftwareBitmap_GetTrustLevel __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel
#define IIterable_SoftwareBitmap_First __FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > interface
 */
#ifndef ____FIIterator_1_BitmapPixelFormat_INTERFACE_DEFINED__
#define ____FIIterator_1_BitmapPixelFormat_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_BitmapPixelFormat, 0x7fc2e293, 0x1084, 0x5d45, 0xb8,0xb8, 0x93,0xe1,0x06,0x92,0xbc,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("7fc2e293-1084-5d45-b8b8-93e10692bcc8")
                IIterator<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > : IIterator_impl<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_BitmapPixelFormat, 0x7fc2e293, 0x1084, 0x5d45, 0xb8,0xb8, 0x93,0xe1,0x06,0x92,0xbc,0xc8)
#endif
#else
typedef struct __FIIterator_1_BitmapPixelFormatVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_BitmapPixelFormat *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_BitmapPixelFormat *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_BitmapPixelFormat *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_BitmapPixelFormat *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_BitmapPixelFormat *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_BitmapPixelFormat *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_BitmapPixelFormat *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_BitmapPixelFormat *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_BitmapPixelFormat *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_BitmapPixelFormat *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_BitmapPixelFormatVtbl;

interface __FIIterator_1_BitmapPixelFormat {
    CONST_VTBL __FIIterator_1_BitmapPixelFormatVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_BitmapPixelFormat_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_BitmapPixelFormat_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_BitmapPixelFormat_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_BitmapPixelFormat_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_BitmapPixelFormat_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_BitmapPixelFormat_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
#define __FIIterator_1_BitmapPixelFormat_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_BitmapPixelFormat_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_BitmapPixelFormat_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_BitmapPixelFormat_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_BitmapPixelFormat_QueryInterface(__FIIterator_1_BitmapPixelFormat* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_BitmapPixelFormat_AddRef(__FIIterator_1_BitmapPixelFormat* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_BitmapPixelFormat_Release(__FIIterator_1_BitmapPixelFormat* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_BitmapPixelFormat_GetIids(__FIIterator_1_BitmapPixelFormat* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_BitmapPixelFormat_GetRuntimeClassName(__FIIterator_1_BitmapPixelFormat* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_BitmapPixelFormat_GetTrustLevel(__FIIterator_1_BitmapPixelFormat* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
static inline HRESULT __FIIterator_1_BitmapPixelFormat_get_Current(__FIIterator_1_BitmapPixelFormat* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_BitmapPixelFormat_get_HasCurrent(__FIIterator_1_BitmapPixelFormat* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_BitmapPixelFormat_MoveNext(__FIIterator_1_BitmapPixelFormat* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_BitmapPixelFormat_GetMany(__FIIterator_1_BitmapPixelFormat* This,UINT32 items_size,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_BitmapPixelFormat IID___FIIterator_1_BitmapPixelFormat
#define IIterator_BitmapPixelFormatVtbl __FIIterator_1_BitmapPixelFormatVtbl
#define IIterator_BitmapPixelFormat __FIIterator_1_BitmapPixelFormat
#define IIterator_BitmapPixelFormat_QueryInterface __FIIterator_1_BitmapPixelFormat_QueryInterface
#define IIterator_BitmapPixelFormat_AddRef __FIIterator_1_BitmapPixelFormat_AddRef
#define IIterator_BitmapPixelFormat_Release __FIIterator_1_BitmapPixelFormat_Release
#define IIterator_BitmapPixelFormat_GetIids __FIIterator_1_BitmapPixelFormat_GetIids
#define IIterator_BitmapPixelFormat_GetRuntimeClassName __FIIterator_1_BitmapPixelFormat_GetRuntimeClassName
#define IIterator_BitmapPixelFormat_GetTrustLevel __FIIterator_1_BitmapPixelFormat_GetTrustLevel
#define IIterator_BitmapPixelFormat_get_Current __FIIterator_1_BitmapPixelFormat_get_Current
#define IIterator_BitmapPixelFormat_get_HasCurrent __FIIterator_1_BitmapPixelFormat_get_HasCurrent
#define IIterator_BitmapPixelFormat_MoveNext __FIIterator_1_BitmapPixelFormat_MoveNext
#define IIterator_BitmapPixelFormat_GetMany __FIIterator_1_BitmapPixelFormat_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_BitmapPixelFormat_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > interface
 */
#ifndef ____FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap, 0xcd12e4c3, 0x8ca8, 0x5be6, 0xb6,0x4b, 0x20,0x4a,0x01,0x4f,0xc6,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("cd12e4c3-8ca8-5be6-b64b-204a014fc620")
                IIterator<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Imaging::SoftwareBitmap*, ABI::Windows::Graphics::Imaging::ISoftwareBitmap* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap, 0xcd12e4c3, 0x8ca8, 0x5be6, 0xb6,0x4b, 0x20,0x4a,0x01,0x4f,0xc6,0x20)
#endif
#else
typedef struct __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl;

interface __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap {
    CONST_VTBL __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_Current(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_HasCurrent(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_MoveNext(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetMany(__FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,UINT32 items_size,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_SoftwareBitmap IID___FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap
#define IIterator_SoftwareBitmapVtbl __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl
#define IIterator_SoftwareBitmap __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap
#define IIterator_SoftwareBitmap_QueryInterface __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface
#define IIterator_SoftwareBitmap_AddRef __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef
#define IIterator_SoftwareBitmap_Release __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release
#define IIterator_SoftwareBitmap_GetIids __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids
#define IIterator_SoftwareBitmap_GetRuntimeClassName __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName
#define IIterator_SoftwareBitmap_GetTrustLevel __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel
#define IIterator_SoftwareBitmap_get_Current __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_Current
#define IIterator_SoftwareBitmap_get_HasCurrent __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_HasCurrent
#define IIterator_SoftwareBitmap_MoveNext __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_MoveNext
#define IIterator_SoftwareBitmap_GetMany __FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > interface
 */
#ifndef ____FIVectorView_1_BitmapPixelFormat_INTERFACE_DEFINED__
#define ____FIVectorView_1_BitmapPixelFormat_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_BitmapPixelFormat, 0x76ac4bc2, 0xc19c, 0x559c, 0xb2,0x87, 0x16,0x94,0xc0,0xdc,0x3a,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("76ac4bc2-c19c-559c-b287-1694c0dc3a0d")
                IVectorView<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > : IVectorView_impl<ABI::Windows::Graphics::Imaging::BitmapPixelFormat >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_BitmapPixelFormat, 0x76ac4bc2, 0xc19c, 0x559c, 0xb2,0x87, 0x16,0x94,0xc0,0xdc,0x3a,0x0d)
#endif
#else
typedef struct __FIVectorView_1_BitmapPixelFormatVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_BitmapPixelFormat *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_BitmapPixelFormat *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_BitmapPixelFormat *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_BitmapPixelFormat *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_BitmapPixelFormat *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_BitmapPixelFormat *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_BitmapPixelFormat *This,
        UINT32 index,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_BitmapPixelFormat *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_BitmapPixelFormat *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_BitmapPixelFormat *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_BitmapPixelFormatVtbl;

interface __FIVectorView_1_BitmapPixelFormat {
    CONST_VTBL __FIVectorView_1_BitmapPixelFormatVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_BitmapPixelFormat_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_BitmapPixelFormat_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_BitmapPixelFormat_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_BitmapPixelFormat_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_BitmapPixelFormat_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_BitmapPixelFormat_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
#define __FIVectorView_1_BitmapPixelFormat_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_BitmapPixelFormat_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_BitmapPixelFormat_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_BitmapPixelFormat_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_BitmapPixelFormat_QueryInterface(__FIVectorView_1_BitmapPixelFormat* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_BitmapPixelFormat_AddRef(__FIVectorView_1_BitmapPixelFormat* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_BitmapPixelFormat_Release(__FIVectorView_1_BitmapPixelFormat* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_BitmapPixelFormat_GetIids(__FIVectorView_1_BitmapPixelFormat* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_BitmapPixelFormat_GetRuntimeClassName(__FIVectorView_1_BitmapPixelFormat* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_BitmapPixelFormat_GetTrustLevel(__FIVectorView_1_BitmapPixelFormat* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Graphics::Imaging::BitmapPixelFormat > methods ***/
static inline HRESULT __FIVectorView_1_BitmapPixelFormat_GetAt(__FIVectorView_1_BitmapPixelFormat* This,UINT32 index,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_BitmapPixelFormat_get_Size(__FIVectorView_1_BitmapPixelFormat* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_BitmapPixelFormat_IndexOf(__FIVectorView_1_BitmapPixelFormat* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_BitmapPixelFormat_GetMany(__FIVectorView_1_BitmapPixelFormat* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGraphics_CImaging_CBitmapPixelFormat *items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_BitmapPixelFormat IID___FIVectorView_1_BitmapPixelFormat
#define IVectorView_BitmapPixelFormatVtbl __FIVectorView_1_BitmapPixelFormatVtbl
#define IVectorView_BitmapPixelFormat __FIVectorView_1_BitmapPixelFormat
#define IVectorView_BitmapPixelFormat_QueryInterface __FIVectorView_1_BitmapPixelFormat_QueryInterface
#define IVectorView_BitmapPixelFormat_AddRef __FIVectorView_1_BitmapPixelFormat_AddRef
#define IVectorView_BitmapPixelFormat_Release __FIVectorView_1_BitmapPixelFormat_Release
#define IVectorView_BitmapPixelFormat_GetIids __FIVectorView_1_BitmapPixelFormat_GetIids
#define IVectorView_BitmapPixelFormat_GetRuntimeClassName __FIVectorView_1_BitmapPixelFormat_GetRuntimeClassName
#define IVectorView_BitmapPixelFormat_GetTrustLevel __FIVectorView_1_BitmapPixelFormat_GetTrustLevel
#define IVectorView_BitmapPixelFormat_GetAt __FIVectorView_1_BitmapPixelFormat_GetAt
#define IVectorView_BitmapPixelFormat_get_Size __FIVectorView_1_BitmapPixelFormat_get_Size
#define IVectorView_BitmapPixelFormat_IndexOf __FIVectorView_1_BitmapPixelFormat_IndexOf
#define IVectorView_BitmapPixelFormat_GetMany __FIVectorView_1_BitmapPixelFormat_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_BitmapPixelFormat_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap, 0xc4a10980, 0x714b, 0x5501, 0x8d,0xa2, 0xdb,0xda,0xcc,0xe7,0x0f,0x73);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("c4a10980-714b-5501-8da2-dbdacce70f73")
            IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Imaging::SoftwareBitmap*, ABI::Windows::Graphics::Imaging::ISoftwareBitmap* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap, 0xc4a10980, 0x714b, 0x5501, 0x8d,0xa2, 0xdb,0xda,0xcc,0xe7,0x0f,0x73)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        __x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl;

interface __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap {
    CONST_VTBL __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_put_Completed(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_Completed(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetResults(__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,__x_ABI_CWindows_CGraphics_CImaging_CISoftwareBitmap **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_SoftwareBitmap IID___FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap
#define IAsyncOperation_SoftwareBitmapVtbl __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl
#define IAsyncOperation_SoftwareBitmap __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap
#define IAsyncOperation_SoftwareBitmap_QueryInterface __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface
#define IAsyncOperation_SoftwareBitmap_AddRef __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef
#define IAsyncOperation_SoftwareBitmap_Release __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release
#define IAsyncOperation_SoftwareBitmap_GetIids __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetIids
#define IAsyncOperation_SoftwareBitmap_GetRuntimeClassName __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetRuntimeClassName
#define IAsyncOperation_SoftwareBitmap_GetTrustLevel __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetTrustLevel
#define IAsyncOperation_SoftwareBitmap_put_Completed __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_put_Completed
#define IAsyncOperation_SoftwareBitmap_get_Completed __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_get_Completed
#define IAsyncOperation_SoftwareBitmap_GetResults __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap, 0xb699b653, 0x33ed, 0x5e2d, 0xa7,0x5f, 0x02,0xbf,0x90,0xe3,0x26,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b699b653-33ed-5e2d-a75f-02bf90e32619")
            IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Imaging::SoftwareBitmap*, ABI::Windows::Graphics::Imaging::ISoftwareBitmap* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap, 0xb699b653, 0x33ed, 0x5e2d, 0xa7,0x5f, 0x02,0xbf,0x90,0xe3,0x26,0x19)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap *This,
        __FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Imaging::SoftwareBitmap* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap* This,__FIAsyncOperation_1_Windows__CGraphics__CImaging__CSoftwareBitmap *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_SoftwareBitmap IID___FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap
#define IAsyncOperationCompletedHandler_SoftwareBitmapVtbl __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmapVtbl
#define IAsyncOperationCompletedHandler_SoftwareBitmap __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap
#define IAsyncOperationCompletedHandler_SoftwareBitmap_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_QueryInterface
#define IAsyncOperationCompletedHandler_SoftwareBitmap_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_AddRef
#define IAsyncOperationCompletedHandler_SoftwareBitmap_Release __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Release
#define IAsyncOperationCompletedHandler_SoftwareBitmap_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CImaging__CSoftwareBitmap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IReference<ABI::Windows::Graphics::Imaging::BitmapBounds > interface
 */
#ifndef ____FIReference_1_BitmapBounds_INTERFACE_DEFINED__
#define ____FIReference_1_BitmapBounds_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIReference_1_BitmapBounds, 0xaa19da70, 0xdee6, 0x5b42, 0xb5,0x62, 0x2f,0xcd,0x21,0x8c,0x34,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("aa19da70-dee6-5b42-b562-2fcd218c34ca")
            IReference<ABI::Windows::Graphics::Imaging::BitmapBounds > : IReference_impl<ABI::Windows::Graphics::Imaging::BitmapBounds >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIReference_1_BitmapBounds, 0xaa19da70, 0xdee6, 0x5b42, 0xb5,0x62, 0x2f,0xcd,0x21,0x8c,0x34,0xca)
#endif
#else
typedef struct __FIReference_1_BitmapBoundsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIReference_1_BitmapBounds *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIReference_1_BitmapBounds *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIReference_1_BitmapBounds *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIReference_1_BitmapBounds *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIReference_1_BitmapBounds *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIReference_1_BitmapBounds *This,
        TrustLevel *trustLevel);

    /*** IReference<ABI::Windows::Graphics::Imaging::BitmapBounds > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIReference_1_BitmapBounds *This,
        __x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds *value);

    END_INTERFACE
} __FIReference_1_BitmapBoundsVtbl;

interface __FIReference_1_BitmapBounds {
    CONST_VTBL __FIReference_1_BitmapBoundsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIReference_1_BitmapBounds_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIReference_1_BitmapBounds_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIReference_1_BitmapBounds_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIReference_1_BitmapBounds_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIReference_1_BitmapBounds_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIReference_1_BitmapBounds_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IReference<ABI::Windows::Graphics::Imaging::BitmapBounds > methods ***/
#define __FIReference_1_BitmapBounds_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIReference_1_BitmapBounds_QueryInterface(__FIReference_1_BitmapBounds* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIReference_1_BitmapBounds_AddRef(__FIReference_1_BitmapBounds* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIReference_1_BitmapBounds_Release(__FIReference_1_BitmapBounds* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIReference_1_BitmapBounds_GetIids(__FIReference_1_BitmapBounds* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIReference_1_BitmapBounds_GetRuntimeClassName(__FIReference_1_BitmapBounds* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIReference_1_BitmapBounds_GetTrustLevel(__FIReference_1_BitmapBounds* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IReference<ABI::Windows::Graphics::Imaging::BitmapBounds > methods ***/
static inline HRESULT __FIReference_1_BitmapBounds_get_Value(__FIReference_1_BitmapBounds* This,__x_ABI_CWindows_CGraphics_CImaging_CBitmapBounds *value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IReference_BitmapBounds IID___FIReference_1_BitmapBounds
#define IReference_BitmapBoundsVtbl __FIReference_1_BitmapBoundsVtbl
#define IReference_BitmapBounds __FIReference_1_BitmapBounds
#define IReference_BitmapBounds_QueryInterface __FIReference_1_BitmapBounds_QueryInterface
#define IReference_BitmapBounds_AddRef __FIReference_1_BitmapBounds_AddRef
#define IReference_BitmapBounds_Release __FIReference_1_BitmapBounds_Release
#define IReference_BitmapBounds_GetIids __FIReference_1_BitmapBounds_GetIids
#define IReference_BitmapBounds_GetRuntimeClassName __FIReference_1_BitmapBounds_GetRuntimeClassName
#define IReference_BitmapBounds_GetTrustLevel __FIReference_1_BitmapBounds_GetTrustLevel
#define IReference_BitmapBounds_get_Value __FIReference_1_BitmapBounds_get_Value
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIReference_1_BitmapBounds_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_graphics_imaging_h__ */
