/* Copyright 2018 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/
// Generated from platform_strings.h.

#ifndef TENSORFLOW_TSL_PLATFORM_PLATFORM_STRINGS_COMPUTED_H_
#define TENSORFLOW_TSL_PLATFORM_PLATFORM_STRINGS_COMPUTED_H_

#if defined(_M_IX86_FP)
#define TF_PLAT_STR__M_IX86_FP TF_PLAT_STR_(_M_IX86_FP)
#else
#define TF_PLAT_STR__M_IX86_FP
#endif
#if defined(_NO_PREFETCHW)
#define TF_PLAT_STR__NO_PREFETCHW TF_PLAT_STR_(_NO_PREFETCHW)
#else
#define TF_PLAT_STR__NO_PREFETCHW
#endif
#if defined(__3dNOW_A__)
#define TF_PLAT_STR___3dNOW_A__ TF_PLAT_STR_(__3dNOW_A__)
#else
#define TF_PLAT_STR___3dNOW_A__
#endif
#if defined(__3dNOW__)
#define TF_PLAT_STR___3dNOW__ TF_PLAT_STR_(__3dNOW__)
#else
#define TF_PLAT_STR___3dNOW__
#endif
#if defined(__ABM__)
#define TF_PLAT_STR___ABM__ TF_PLAT_STR_(__ABM__)
#else
#define TF_PLAT_STR___ABM__
#endif
#if defined(__ADX__)
#define TF_PLAT_STR___ADX__ TF_PLAT_STR_(__ADX__)
#else
#define TF_PLAT_STR___ADX__
#endif
#if defined(__AES__)
#define TF_PLAT_STR___AES__ TF_PLAT_STR_(__AES__)
#else
#define TF_PLAT_STR___AES__
#endif
#if defined(__AVX2__)
#define TF_PLAT_STR___AVX2__ TF_PLAT_STR_(__AVX2__)
#else
#define TF_PLAT_STR___AVX2__
#endif
#if defined(__AVX512BW__)
#define TF_PLAT_STR___AVX512BW__ TF_PLAT_STR_(__AVX512BW__)
#else
#define TF_PLAT_STR___AVX512BW__
#endif
#if defined(__AVX512CD__)
#define TF_PLAT_STR___AVX512CD__ TF_PLAT_STR_(__AVX512CD__)
#else
#define TF_PLAT_STR___AVX512CD__
#endif
#if defined(__AVX512DQ__)
#define TF_PLAT_STR___AVX512DQ__ TF_PLAT_STR_(__AVX512DQ__)
#else
#define TF_PLAT_STR___AVX512DQ__
#endif
#if defined(__AVX512ER__)
#define TF_PLAT_STR___AVX512ER__ TF_PLAT_STR_(__AVX512ER__)
#else
#define TF_PLAT_STR___AVX512ER__
#endif
#if defined(__AVX512F__)
#define TF_PLAT_STR___AVX512F__ TF_PLAT_STR_(__AVX512F__)
#else
#define TF_PLAT_STR___AVX512F__
#endif
#if defined(__AVX512IFMA__)
#define TF_PLAT_STR___AVX512IFMA__ TF_PLAT_STR_(__AVX512IFMA__)
#else
#define TF_PLAT_STR___AVX512IFMA__
#endif
#if defined(__AVX512PF__)
#define TF_PLAT_STR___AVX512PF__ TF_PLAT_STR_(__AVX512PF__)
#else
#define TF_PLAT_STR___AVX512PF__
#endif
#if defined(__AVX512VBMI__)
#define TF_PLAT_STR___AVX512VBMI__ TF_PLAT_STR_(__AVX512VBMI__)
#else
#define TF_PLAT_STR___AVX512VBMI__
#endif
#if defined(__AVX512VL__)
#define TF_PLAT_STR___AVX512VL__ TF_PLAT_STR_(__AVX512VL__)
#else
#define TF_PLAT_STR___AVX512VL__
#endif
#if defined(__AVX__)
#define TF_PLAT_STR___AVX__ TF_PLAT_STR_(__AVX__)
#else
#define TF_PLAT_STR___AVX__
#endif
#if defined(__BMI2__)
#define TF_PLAT_STR___BMI2__ TF_PLAT_STR_(__BMI2__)
#else
#define TF_PLAT_STR___BMI2__
#endif
#if defined(__BMI__)
#define TF_PLAT_STR___BMI__ TF_PLAT_STR_(__BMI__)
#else
#define TF_PLAT_STR___BMI__
#endif
#if defined(__CLFLUSHOPT__)
#define TF_PLAT_STR___CLFLUSHOPT__ TF_PLAT_STR_(__CLFLUSHOPT__)
#else
#define TF_PLAT_STR___CLFLUSHOPT__
#endif
#if defined(__CLZERO__)
#define TF_PLAT_STR___CLZERO__ TF_PLAT_STR_(__CLZERO__)
#else
#define TF_PLAT_STR___CLZERO__
#endif
#if defined(__F16C__)
#define TF_PLAT_STR___F16C__ TF_PLAT_STR_(__F16C__)
#else
#define TF_PLAT_STR___F16C__
#endif
#if defined(__FMA4__)
#define TF_PLAT_STR___FMA4__ TF_PLAT_STR_(__FMA4__)
#else
#define TF_PLAT_STR___FMA4__
#endif
#if defined(__FMA__)
#define TF_PLAT_STR___FMA__ TF_PLAT_STR_(__FMA__)
#else
#define TF_PLAT_STR___FMA__
#endif
#if defined(__FP_FAST_FMA)
#define TF_PLAT_STR___FP_FAST_FMA TF_PLAT_STR_(__FP_FAST_FMA)
#else
#define TF_PLAT_STR___FP_FAST_FMA
#endif
#if defined(__FP_FAST_FMAF)
#define TF_PLAT_STR___FP_FAST_FMAF TF_PLAT_STR_(__FP_FAST_FMAF)
#else
#define TF_PLAT_STR___FP_FAST_FMAF
#endif
#if defined(__FSGSBASE__)
#define TF_PLAT_STR___FSGSBASE__ TF_PLAT_STR_(__FSGSBASE__)
#else
#define TF_PLAT_STR___FSGSBASE__
#endif
#if defined(__FXSR__)
#define TF_PLAT_STR___FXSR__ TF_PLAT_STR_(__FXSR__)
#else
#define TF_PLAT_STR___FXSR__
#endif
#if defined(__LWP__)
#define TF_PLAT_STR___LWP__ TF_PLAT_STR_(__LWP__)
#else
#define TF_PLAT_STR___LWP__
#endif
#if defined(__LZCNT__)
#define TF_PLAT_STR___LZCNT__ TF_PLAT_STR_(__LZCNT__)
#else
#define TF_PLAT_STR___LZCNT__
#endif
#if defined(__MMX__)
#define TF_PLAT_STR___MMX__ TF_PLAT_STR_(__MMX__)
#else
#define TF_PLAT_STR___MMX__
#endif
#if defined(__MWAITX__)
#define TF_PLAT_STR___MWAITX__ TF_PLAT_STR_(__MWAITX__)
#else
#define TF_PLAT_STR___MWAITX__
#endif
#if defined(__PCLMUL__)
#define TF_PLAT_STR___PCLMUL__ TF_PLAT_STR_(__PCLMUL__)
#else
#define TF_PLAT_STR___PCLMUL__
#endif
#if defined(__PKU__)
#define TF_PLAT_STR___PKU__ TF_PLAT_STR_(__PKU__)
#else
#define TF_PLAT_STR___PKU__
#endif
#if defined(__POPCNT__)
#define TF_PLAT_STR___POPCNT__ TF_PLAT_STR_(__POPCNT__)
#else
#define TF_PLAT_STR___POPCNT__
#endif
#if defined(__PRFCHW__)
#define TF_PLAT_STR___PRFCHW__ TF_PLAT_STR_(__PRFCHW__)
#else
#define TF_PLAT_STR___PRFCHW__
#endif
#if defined(__RDRND__)
#define TF_PLAT_STR___RDRND__ TF_PLAT_STR_(__RDRND__)
#else
#define TF_PLAT_STR___RDRND__
#endif
#if defined(__RDSEED__)
#define TF_PLAT_STR___RDSEED__ TF_PLAT_STR_(__RDSEED__)
#else
#define TF_PLAT_STR___RDSEED__
#endif
#if defined(__RTM__)
#define TF_PLAT_STR___RTM__ TF_PLAT_STR_(__RTM__)
#else
#define TF_PLAT_STR___RTM__
#endif
#if defined(__SHA__)
#define TF_PLAT_STR___SHA__ TF_PLAT_STR_(__SHA__)
#else
#define TF_PLAT_STR___SHA__
#endif
#if defined(__SSE2_MATH__)
#define TF_PLAT_STR___SSE2_MATH__ TF_PLAT_STR_(__SSE2_MATH__)
#else
#define TF_PLAT_STR___SSE2_MATH__
#endif
#if defined(__SSE2__)
#define TF_PLAT_STR___SSE2__ TF_PLAT_STR_(__SSE2__)
#else
#define TF_PLAT_STR___SSE2__
#endif
#if defined(__SSE_MATH__)
#define TF_PLAT_STR___SSE_MATH__ TF_PLAT_STR_(__SSE_MATH__)
#else
#define TF_PLAT_STR___SSE_MATH__
#endif
#if defined(__SSE__)
#define TF_PLAT_STR___SSE__ TF_PLAT_STR_(__SSE__)
#else
#define TF_PLAT_STR___SSE__
#endif
#if defined(__SSE3__)
#define TF_PLAT_STR___SSE3__ TF_PLAT_STR_(__SSE3__)
#else
#define TF_PLAT_STR___SSE3__
#endif
#if defined(__SSE4A__)
#define TF_PLAT_STR___SSE4A__ TF_PLAT_STR_(__SSE4A__)
#else
#define TF_PLAT_STR___SSE4A__
#endif
#if defined(__SSE4_1__)
#define TF_PLAT_STR___SSE4_1__ TF_PLAT_STR_(__SSE4_1__)
#else
#define TF_PLAT_STR___SSE4_1__
#endif
#if defined(__SSE4_2__)
#define TF_PLAT_STR___SSE4_2__ TF_PLAT_STR_(__SSE4_2__)
#else
#define TF_PLAT_STR___SSE4_2__
#endif
#if defined(__SSSE3__)
#define TF_PLAT_STR___SSSE3__ TF_PLAT_STR_(__SSSE3__)
#else
#define TF_PLAT_STR___SSSE3__
#endif
#if defined(__TBM__)
#define TF_PLAT_STR___TBM__ TF_PLAT_STR_(__TBM__)
#else
#define TF_PLAT_STR___TBM__
#endif
#if defined(__XOP__)
#define TF_PLAT_STR___XOP__ TF_PLAT_STR_(__XOP__)
#else
#define TF_PLAT_STR___XOP__
#endif
#if defined(__XSAVEC__)
#define TF_PLAT_STR___XSAVEC__ TF_PLAT_STR_(__XSAVEC__)
#else
#define TF_PLAT_STR___XSAVEC__
#endif
#if defined(__XSAVEOPT__)
#define TF_PLAT_STR___XSAVEOPT__ TF_PLAT_STR_(__XSAVEOPT__)
#else
#define TF_PLAT_STR___XSAVEOPT__
#endif
#if defined(__XSAVES__)
#define TF_PLAT_STR___XSAVES__ TF_PLAT_STR_(__XSAVES__)
#else
#define TF_PLAT_STR___XSAVES__
#endif
#if defined(__XSAVE__)
#define TF_PLAT_STR___XSAVE__ TF_PLAT_STR_(__XSAVE__)
#else
#define TF_PLAT_STR___XSAVE__
#endif
#if defined(_SOFT_DOUBLE)
#define TF_PLAT_STR__SOFT_DOUBLE TF_PLAT_STR_(_SOFT_DOUBLE)
#else
#define TF_PLAT_STR__SOFT_DOUBLE
#endif
#if defined(_SOFT_FLOAT)
#define TF_PLAT_STR__SOFT_FLOAT TF_PLAT_STR_(_SOFT_FLOAT)
#else
#define TF_PLAT_STR__SOFT_FLOAT
#endif
#if defined(__ALTIVEC__)
#define TF_PLAT_STR___ALTIVEC__ TF_PLAT_STR_(__ALTIVEC__)
#else
#define TF_PLAT_STR___ALTIVEC__
#endif
#if defined(__APPLE_ALTIVEC__)
#define TF_PLAT_STR___APPLE_ALTIVEC__ TF_PLAT_STR_(__APPLE_ALTIVEC__)
#else
#define TF_PLAT_STR___APPLE_ALTIVEC__
#endif
#if defined(__CRYPTO__)
#define TF_PLAT_STR___CRYPTO__ TF_PLAT_STR_(__CRYPTO__)
#else
#define TF_PLAT_STR___CRYPTO__
#endif
#if defined(__FLOAT128_HARDWARE__)
#define TF_PLAT_STR___FLOAT128_HARDWARE__ TF_PLAT_STR_(__FLOAT128_HARDWARE__)
#else
#define TF_PLAT_STR___FLOAT128_HARDWARE__
#endif
#if defined(__FLOAT128_TYPE__)
#define TF_PLAT_STR___FLOAT128_TYPE__ TF_PLAT_STR_(__FLOAT128_TYPE__)
#else
#define TF_PLAT_STR___FLOAT128_TYPE__
#endif
#if defined(__FP_FAST_FMA)
#define TF_PLAT_STR___FP_FAST_FMA TF_PLAT_STR_(__FP_FAST_FMA)
#else
#define TF_PLAT_STR___FP_FAST_FMA
#endif
#if defined(__FP_FAST_FMAF)
#define TF_PLAT_STR___FP_FAST_FMAF TF_PLAT_STR_(__FP_FAST_FMAF)
#else
#define TF_PLAT_STR___FP_FAST_FMAF
#endif
#if defined(__HTM__)
#define TF_PLAT_STR___HTM__ TF_PLAT_STR_(__HTM__)
#else
#define TF_PLAT_STR___HTM__
#endif
#if defined(__NO_FPRS__)
#define TF_PLAT_STR___NO_FPRS__ TF_PLAT_STR_(__NO_FPRS__)
#else
#define TF_PLAT_STR___NO_FPRS__
#endif
#if defined(__NO_LWSYNC__)
#define TF_PLAT_STR___NO_LWSYNC__ TF_PLAT_STR_(__NO_LWSYNC__)
#else
#define TF_PLAT_STR___NO_LWSYNC__
#endif
#if defined(__POWER8_VECTOR__)
#define TF_PLAT_STR___POWER8_VECTOR__ TF_PLAT_STR_(__POWER8_VECTOR__)
#else
#define TF_PLAT_STR___POWER8_VECTOR__
#endif
#if defined(__POWER9_VECTOR__)
#define TF_PLAT_STR___POWER9_VECTOR__ TF_PLAT_STR_(__POWER9_VECTOR__)
#else
#define TF_PLAT_STR___POWER9_VECTOR__
#endif
#if defined(__PPC405__)
#define TF_PLAT_STR___PPC405__ TF_PLAT_STR_(__PPC405__)
#else
#define TF_PLAT_STR___PPC405__
#endif
#if defined(__QUAD_MEMORY_ATOMIC__)
#define TF_PLAT_STR___QUAD_MEMORY_ATOMIC__ TF_PLAT_STR_(__QUAD_MEMORY_ATOMIC__)
#else
#define TF_PLAT_STR___QUAD_MEMORY_ATOMIC__
#endif
#if defined(__RECIPF__)
#define TF_PLAT_STR___RECIPF__ TF_PLAT_STR_(__RECIPF__)
#else
#define TF_PLAT_STR___RECIPF__
#endif
#if defined(__RECIP_PRECISION__)
#define TF_PLAT_STR___RECIP_PRECISION__ TF_PLAT_STR_(__RECIP_PRECISION__)
#else
#define TF_PLAT_STR___RECIP_PRECISION__
#endif
#if defined(__RECIP__)
#define TF_PLAT_STR___RECIP__ TF_PLAT_STR_(__RECIP__)
#else
#define TF_PLAT_STR___RECIP__
#endif
#if defined(__RSQRTEF__)
#define TF_PLAT_STR___RSQRTEF__ TF_PLAT_STR_(__RSQRTEF__)
#else
#define TF_PLAT_STR___RSQRTEF__
#endif
#if defined(__RSQRTE__)
#define TF_PLAT_STR___RSQRTE__ TF_PLAT_STR_(__RSQRTE__)
#else
#define TF_PLAT_STR___RSQRTE__
#endif
#if defined(__TM_FENCE__)
#define TF_PLAT_STR___TM_FENCE__ TF_PLAT_STR_(__TM_FENCE__)
#else
#define TF_PLAT_STR___TM_FENCE__
#endif
#if defined(__UPPER_REGS_DF__)
#define TF_PLAT_STR___UPPER_REGS_DF__ TF_PLAT_STR_(__UPPER_REGS_DF__)
#else
#define TF_PLAT_STR___UPPER_REGS_DF__
#endif
#if defined(__UPPER_REGS_SF__)
#define TF_PLAT_STR___UPPER_REGS_SF__ TF_PLAT_STR_(__UPPER_REGS_SF__)
#else
#define TF_PLAT_STR___UPPER_REGS_SF__
#endif
#if defined(__VEC__)
#define TF_PLAT_STR___VEC__ TF_PLAT_STR_(__VEC__)
#else
#define TF_PLAT_STR___VEC__
#endif
#if defined(__VSX__)
#define TF_PLAT_STR___VSX__ TF_PLAT_STR_(__VSX__)
#else
#define TF_PLAT_STR___VSX__
#endif
#if defined(__ARM_ARCH)
#define TF_PLAT_STR___ARM_ARCH TF_PLAT_STR_(__ARM_ARCH)
#else
#define TF_PLAT_STR___ARM_ARCH
#endif
#if defined(__ARM_FEATURE_CLZ)
#define TF_PLAT_STR___ARM_FEATURE_CLZ TF_PLAT_STR_(__ARM_FEATURE_CLZ)
#else
#define TF_PLAT_STR___ARM_FEATURE_CLZ
#endif
#if defined(__ARM_FEATURE_CRC32)
#define TF_PLAT_STR___ARM_FEATURE_CRC32 TF_PLAT_STR_(__ARM_FEATURE_CRC32)
#else
#define TF_PLAT_STR___ARM_FEATURE_CRC32
#endif
#if defined(__ARM_FEATURE_CRC32)
#define TF_PLAT_STR___ARM_FEATURE_CRC32 TF_PLAT_STR_(__ARM_FEATURE_CRC32)
#else
#define TF_PLAT_STR___ARM_FEATURE_CRC32
#endif
#if defined(__ARM_FEATURE_CRYPTO)
#define TF_PLAT_STR___ARM_FEATURE_CRYPTO TF_PLAT_STR_(__ARM_FEATURE_CRYPTO)
#else
#define TF_PLAT_STR___ARM_FEATURE_CRYPTO
#endif
#if defined(__ARM_FEATURE_DIRECTED_ROUNDING)
#define TF_PLAT_STR___ARM_FEATURE_DIRECTED_ROUNDING \
  TF_PLAT_STR_(__ARM_FEATURE_DIRECTED_ROUNDING)
#else
#define TF_PLAT_STR___ARM_FEATURE_DIRECTED_ROUNDING
#endif
#if defined(__ARM_FEATURE_DSP)
#define TF_PLAT_STR___ARM_FEATURE_DSP TF_PLAT_STR_(__ARM_FEATURE_DSP)
#else
#define TF_PLAT_STR___ARM_FEATURE_DSP
#endif
#if defined(__ARM_FEATURE_FMA)
#define TF_PLAT_STR___ARM_FEATURE_FMA TF_PLAT_STR_(__ARM_FEATURE_FMA)
#else
#define TF_PLAT_STR___ARM_FEATURE_FMA
#endif
#if defined(__ARM_FEATURE_IDIV)
#define TF_PLAT_STR___ARM_FEATURE_IDIV TF_PLAT_STR_(__ARM_FEATURE_IDIV)
#else
#define TF_PLAT_STR___ARM_FEATURE_IDIV
#endif
#if defined(__ARM_FEATURE_LDREX)
#define TF_PLAT_STR___ARM_FEATURE_LDREX TF_PLAT_STR_(__ARM_FEATURE_LDREX)
#else
#define TF_PLAT_STR___ARM_FEATURE_LDREX
#endif
#if defined(__ARM_FEATURE_NUMERIC_MAXMIN)
#define TF_PLAT_STR___ARM_FEATURE_NUMERIC_MAXMIN \
  TF_PLAT_STR_(__ARM_FEATURE_NUMERIC_MAXMIN)
#else
#define TF_PLAT_STR___ARM_FEATURE_NUMERIC_MAXMIN
#endif
#if defined(__ARM_FEATURE_QBIT)
#define TF_PLAT_STR___ARM_FEATURE_QBIT TF_PLAT_STR_(__ARM_FEATURE_QBIT)
#else
#define TF_PLAT_STR___ARM_FEATURE_QBIT
#endif
#if defined(__ARM_FEATURE_QRDMX)
#define TF_PLAT_STR___ARM_FEATURE_QRDMX TF_PLAT_STR_(__ARM_FEATURE_QRDMX)
#else
#define TF_PLAT_STR___ARM_FEATURE_QRDMX
#endif
#if defined(__ARM_FEATURE_SAT)
#define TF_PLAT_STR___ARM_FEATURE_SAT TF_PLAT_STR_(__ARM_FEATURE_SAT)
#else
#define TF_PLAT_STR___ARM_FEATURE_SAT
#endif
#if defined(__ARM_FEATURE_SIMD32)
#define TF_PLAT_STR___ARM_FEATURE_SIMD32 TF_PLAT_STR_(__ARM_FEATURE_SIMD32)
#else
#define TF_PLAT_STR___ARM_FEATURE_SIMD32
#endif
#if defined(__ARM_FEATURE_UNALIGNED)
#define TF_PLAT_STR___ARM_FEATURE_UNALIGNED \
  TF_PLAT_STR_(__ARM_FEATURE_UNALIGNED)
#else
#define TF_PLAT_STR___ARM_FEATURE_UNALIGNED
#endif
#if defined(__ARM_FP)
#define TF_PLAT_STR___ARM_FP TF_PLAT_STR_(__ARM_FP)
#else
#define TF_PLAT_STR___ARM_FP
#endif
#if defined(__ARM_NEON_FP)
#define TF_PLAT_STR___ARM_NEON_FP TF_PLAT_STR_(__ARM_NEON_FP)
#else
#define TF_PLAT_STR___ARM_NEON_FP
#endif
#if defined(__ARM_NEON__)
#define TF_PLAT_STR___ARM_NEON__ TF_PLAT_STR_(__ARM_NEON__)
#else
#define TF_PLAT_STR___ARM_NEON__
#endif
#if defined(__ARM_WMMX)
#define TF_PLAT_STR___ARM_WMMX TF_PLAT_STR_(__ARM_WMMX)
#else
#define TF_PLAT_STR___ARM_WMMX
#endif
#if defined(__IWMMXT2__)
#define TF_PLAT_STR___IWMMXT2__ TF_PLAT_STR_(__IWMMXT2__)
#else
#define TF_PLAT_STR___IWMMXT2__
#endif
#if defined(__IWMMXT__)
#define TF_PLAT_STR___IWMMXT__ TF_PLAT_STR_(__IWMMXT__)
#else
#define TF_PLAT_STR___IWMMXT__
#endif
#if defined(__VFP_FP__)
#define TF_PLAT_STR___VFP_FP__ TF_PLAT_STR_(__VFP_FP__)
#else
#define TF_PLAT_STR___VFP_FP__
#endif
#if defined(TARGET_IPHONE_SIMULATOR)
#define TF_PLAT_STR_TARGET_IPHONE_SIMULATOR \
  TF_PLAT_STR_(TARGET_IPHONE_SIMULATOR)
#else
#define TF_PLAT_STR_TARGET_IPHONE_SIMULATOR
#endif
#if defined(TARGET_OS_IOS)
#define TF_PLAT_STR_TARGET_OS_IOS TF_PLAT_STR_(TARGET_OS_IOS)
#else
#define TF_PLAT_STR_TARGET_OS_IOS
#endif
#if defined(TARGET_OS_IPHONE)
#define TF_PLAT_STR_TARGET_OS_IPHONE TF_PLAT_STR_(TARGET_OS_IPHONE)
#else
#define TF_PLAT_STR_TARGET_OS_IPHONE
#endif
#if defined(_MSC_VER)
#define TF_PLAT_STR__MSC_VER TF_PLAT_STR_(_MSC_VER)
#else
#define TF_PLAT_STR__MSC_VER
#endif
#if defined(_M_ARM)
#define TF_PLAT_STR__M_ARM TF_PLAT_STR_(_M_ARM)
#else
#define TF_PLAT_STR__M_ARM
#endif
#if defined(_M_ARM64)
#define TF_PLAT_STR__M_ARM64 TF_PLAT_STR_(_M_ARM64)
#else
#define TF_PLAT_STR__M_ARM64
#endif
#if defined(_M_ARM_ARMV7VE)
#define TF_PLAT_STR__M_ARM_ARMV7VE TF_PLAT_STR_(_M_ARM_ARMV7VE)
#else
#define TF_PLAT_STR__M_ARM_ARMV7VE
#endif
#if defined(_M_ARM_FP)
#define TF_PLAT_STR__M_ARM_FP TF_PLAT_STR_(_M_ARM_FP)
#else
#define TF_PLAT_STR__M_ARM_FP
#endif
#if defined(_M_IX86)
#define TF_PLAT_STR__M_IX86 TF_PLAT_STR_(_M_IX86)
#else
#define TF_PLAT_STR__M_IX86
#endif
#if defined(_M_X64)
#define TF_PLAT_STR__M_X64 TF_PLAT_STR_(_M_X64)
#else
#define TF_PLAT_STR__M_X64
#endif
#if defined(_WIN32)
#define TF_PLAT_STR__WIN32 TF_PLAT_STR_(_WIN32)
#else
#define TF_PLAT_STR__WIN32
#endif
#if defined(_WIN64)
#define TF_PLAT_STR__WIN64 TF_PLAT_STR_(_WIN64)
#else
#define TF_PLAT_STR__WIN64
#endif
#if defined(__ANDROID__)
#define TF_PLAT_STR___ANDROID__ TF_PLAT_STR_(__ANDROID__)
#else
#define TF_PLAT_STR___ANDROID__
#endif
#if defined(__APPLE__)
#define TF_PLAT_STR___APPLE__ TF_PLAT_STR_(__APPLE__)
#else
#define TF_PLAT_STR___APPLE__
#endif
#if defined(__BYTE_ORDER__)
#define TF_PLAT_STR___BYTE_ORDER__ TF_PLAT_STR_(__BYTE_ORDER__)
#else
#define TF_PLAT_STR___BYTE_ORDER__
#endif
#if defined(__CYGWIN__)
#define TF_PLAT_STR___CYGWIN__ TF_PLAT_STR_(__CYGWIN__)
#else
#define TF_PLAT_STR___CYGWIN__
#endif
#if defined(__FreeBSD__)
#define TF_PLAT_STR___FreeBSD__ TF_PLAT_STR_(__FreeBSD__)
#else
#define TF_PLAT_STR___FreeBSD__
#endif
#if defined(__LITTLE_ENDIAN__)
#define TF_PLAT_STR___LITTLE_ENDIAN__ TF_PLAT_STR_(__LITTLE_ENDIAN__)
#else
#define TF_PLAT_STR___LITTLE_ENDIAN__
#endif
#if defined(__NetBSD__)
#define TF_PLAT_STR___NetBSD__ TF_PLAT_STR_(__NetBSD__)
#else
#define TF_PLAT_STR___NetBSD__
#endif
#if defined(__OpenBSD__)
#define TF_PLAT_STR___OpenBSD__ TF_PLAT_STR_(__OpenBSD__)
#else
#define TF_PLAT_STR___OpenBSD__
#endif
#if defined(____MSYS__)
#define TF_PLAT_STR_____MSYS__ TF_PLAT_STR_(____MSYS__)
#else
#define TF_PLAT_STR_____MSYS__
#endif
#if defined(__aarch64__)
#define TF_PLAT_STR___aarch64__ TF_PLAT_STR_(__aarch64__)
#else
#define TF_PLAT_STR___aarch64__
#endif
#if defined(__alpha__)
#define TF_PLAT_STR___alpha__ TF_PLAT_STR_(__alpha__)
#else
#define TF_PLAT_STR___alpha__
#endif
#if defined(__arm__)
#define TF_PLAT_STR___arm__ TF_PLAT_STR_(__arm__)
#else
#define TF_PLAT_STR___arm__
#endif
#if defined(__i386__)
#define TF_PLAT_STR___i386__ TF_PLAT_STR_(__i386__)
#else
#define TF_PLAT_STR___i386__
#endif
#if defined(__i686__)
#define TF_PLAT_STR___i686__ TF_PLAT_STR_(__i686__)
#else
#define TF_PLAT_STR___i686__
#endif
#if defined(__ia64__)
#define TF_PLAT_STR___ia64__ TF_PLAT_STR_(__ia64__)
#else
#define TF_PLAT_STR___ia64__
#endif
#if defined(__linux__)
#define TF_PLAT_STR___linux__ TF_PLAT_STR_(__linux__)
#else
#define TF_PLAT_STR___linux__
#endif
#if defined(__mips32__)
#define TF_PLAT_STR___mips32__ TF_PLAT_STR_(__mips32__)
#else
#define TF_PLAT_STR___mips32__
#endif
#if defined(__mips64__)
#define TF_PLAT_STR___mips64__ TF_PLAT_STR_(__mips64__)
#else
#define TF_PLAT_STR___mips64__
#endif
#if defined(__powerpc64__)
#define TF_PLAT_STR___powerpc64__ TF_PLAT_STR_(__powerpc64__)
#else
#define TF_PLAT_STR___powerpc64__
#endif
#if defined(__powerpc__)
#define TF_PLAT_STR___powerpc__ TF_PLAT_STR_(__powerpc__)
#else
#define TF_PLAT_STR___powerpc__
#endif
#if defined(__riscv___)
#define TF_PLAT_STR___riscv___ TF_PLAT_STR_(__riscv___)
#else
#define TF_PLAT_STR___riscv___
#endif
#if defined(__s390x__)
#define TF_PLAT_STR___s390x__ TF_PLAT_STR_(__s390x__)
#else
#define TF_PLAT_STR___s390x__
#endif
#if defined(__sparc64__)
#define TF_PLAT_STR___sparc64__ TF_PLAT_STR_(__sparc64__)
#else
#define TF_PLAT_STR___sparc64__
#endif
#if defined(__sparc__)
#define TF_PLAT_STR___sparc__ TF_PLAT_STR_(__sparc__)
#else
#define TF_PLAT_STR___sparc__
#endif
#if defined(__x86_64__)
#define TF_PLAT_STR___x86_64__ TF_PLAT_STR_(__x86_64__)
#else
#define TF_PLAT_STR___x86_64__
#endif

#endif  // TENSORFLOW_TSL_PLATFORM_PLATFORM_STRINGS_COMPUTED_H_
