/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _NTDDMODM_
#define _NTDDMODM_

#ifdef __cplusplus
extern "C" {
#endif

#ifdef DEFINE_GUID
DEFINE_GUID(GUID_DEVINTERFACE_MODEM,0x2c7089aa,0x2e0e,0x11d1,0xb1,0x14,0x00,0xc0,0x4f,0xc2,0xaa,0xe4);
#define GUID_CLASS_MODEM GUID_DEVINTERFACE_MODEM /* GUID_CLASS_MODEM obsolete starting with Win2000. */
#endif

#define IOCTL_MODEM_GET_PASSTHROUGH	CTL_CODE(FILE_DEVICE_MODEM, 1,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_SET_PASSTHROUGH	CTL_CODE(FILE_DEVICE_MODEM, 2,<PERSON><PERSON><PERSON>_BUFFERED,FILE_ANY_ACCESS)
#define <PERSON><PERSON><PERSON>_MODEM_SET_DLE_MONITORING	CTL_CODE(FILE_DEVICE_MODEM, 3,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_GET_DLE		CTL_CODE(FILE_DEVICE_MODEM, 4,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_SET_DLE_SHIELDING	CTL_CODE(FILE_DEVICE_MODEM, 5,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_STOP_WAVE_RECEIVE	CTL_CODE(FILE_DEVICE_MODEM, 6,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_SEND_MESSAGE	CTL_CODE(FILE_DEVICE_MODEM, 7,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_GET_MESSAGE		CTL_CODE(FILE_DEVICE_MODEM, 8,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_SEND_GET_MESSAGE	CTL_CODE(FILE_DEVICE_MODEM, 9,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_SEND_LOOPBACK_MESSAGE \
					CTL_CODE(FILE_DEVICE_MODEM,10,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_CHECK_FOR_MODEM	CTL_CODE(FILE_DEVICE_MODEM,11,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_SET_MIN_POWER	CTL_CODE(FILE_DEVICE_MODEM,12,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_MODEM_WATCH_FOR_RESUME	CTL_CODE(FILE_DEVICE_MODEM,13,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_CANCEL_GET_SEND_MESSAGE	CTL_CODE(FILE_DEVICE_MODEM,14,METHOD_BUFFERED,FILE_ANY_ACCESS)
#define IOCTL_SET_SERVER_STATE		CTL_CODE(FILE_DEVICE_MODEM,15,METHOD_BUFFERED,FILE_ANY_ACCESS)

#ifdef __cplusplus
}
#endif

#endif /* _NTDDMODM_ */

