/*** Autogenerated by WIDL 10.8 from include/rdpencomapi.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __rdpencomapi_h__
#define __rdpencomapi_h__

/* Forward declarations */

#ifndef __IRDPSRAPIDebug_FWD_DEFINED__
#define __IRDPSRAPIDebug_FWD_DEFINED__
typedef interface IRDPSRAPIDebug IRDPSRAPIDebug;
#ifdef __cplusplus
interface IRDPSRAPIDebug;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIPerfCounterLogger_FWD_DEFINED__
#define __IRDPSRAPIPerfCounterLogger_FWD_DEFINED__
typedef interface IRDPSRAPIPerfCounterLogger IRDPSRAPIPerfCounterLogger;
#ifdef __cplusplus
interface IRDPSRAPIPerfCounterLogger;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIPerfCounterLoggingManager_FWD_DEFINED__
#define __IRDPSRAPIPerfCounterLoggingManager_FWD_DEFINED__
typedef interface IRDPSRAPIPerfCounterLoggingManager IRDPSRAPIPerfCounterLoggingManager;
#ifdef __cplusplus
interface IRDPSRAPIPerfCounterLoggingManager;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIAudioStream_FWD_DEFINED__
#define __IRDPSRAPIAudioStream_FWD_DEFINED__
typedef interface IRDPSRAPIAudioStream IRDPSRAPIAudioStream;
#ifdef __cplusplus
interface IRDPSRAPIAudioStream;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIClipboardUseEvents_FWD_DEFINED__
#define __IRDPSRAPIClipboardUseEvents_FWD_DEFINED__
typedef interface IRDPSRAPIClipboardUseEvents IRDPSRAPIClipboardUseEvents;
#ifdef __cplusplus
interface IRDPSRAPIClipboardUseEvents;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIWindow_FWD_DEFINED__
#define __IRDPSRAPIWindow_FWD_DEFINED__
typedef interface IRDPSRAPIWindow IRDPSRAPIWindow;
#ifdef __cplusplus
interface IRDPSRAPIWindow;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIWindowList_FWD_DEFINED__
#define __IRDPSRAPIWindowList_FWD_DEFINED__
typedef interface IRDPSRAPIWindowList IRDPSRAPIWindowList;
#ifdef __cplusplus
interface IRDPSRAPIWindowList;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIApplication_FWD_DEFINED__
#define __IRDPSRAPIApplication_FWD_DEFINED__
typedef interface IRDPSRAPIApplication IRDPSRAPIApplication;
#ifdef __cplusplus
interface IRDPSRAPIApplication;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIApplicationList_FWD_DEFINED__
#define __IRDPSRAPIApplicationList_FWD_DEFINED__
typedef interface IRDPSRAPIApplicationList IRDPSRAPIApplicationList;
#ifdef __cplusplus
interface IRDPSRAPIApplicationList;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIApplicationFilter_FWD_DEFINED__
#define __IRDPSRAPIApplicationFilter_FWD_DEFINED__
typedef interface IRDPSRAPIApplicationFilter IRDPSRAPIApplicationFilter;
#ifdef __cplusplus
interface IRDPSRAPIApplicationFilter;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPISessionProperties_FWD_DEFINED__
#define __IRDPSRAPISessionProperties_FWD_DEFINED__
typedef interface IRDPSRAPISessionProperties IRDPSRAPISessionProperties;
#ifdef __cplusplus
interface IRDPSRAPISessionProperties;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIInvitation_FWD_DEFINED__
#define __IRDPSRAPIInvitation_FWD_DEFINED__
typedef interface IRDPSRAPIInvitation IRDPSRAPIInvitation;
#ifdef __cplusplus
interface IRDPSRAPIInvitation;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIInvitationManager_FWD_DEFINED__
#define __IRDPSRAPIInvitationManager_FWD_DEFINED__
typedef interface IRDPSRAPIInvitationManager IRDPSRAPIInvitationManager;
#ifdef __cplusplus
interface IRDPSRAPIInvitationManager;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPITcpConnectionInfo_FWD_DEFINED__
#define __IRDPSRAPITcpConnectionInfo_FWD_DEFINED__
typedef interface IRDPSRAPITcpConnectionInfo IRDPSRAPITcpConnectionInfo;
#ifdef __cplusplus
interface IRDPSRAPITcpConnectionInfo;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIAttendee_FWD_DEFINED__
#define __IRDPSRAPIAttendee_FWD_DEFINED__
typedef interface IRDPSRAPIAttendee IRDPSRAPIAttendee;
#ifdef __cplusplus
interface IRDPSRAPIAttendee;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIAttendeeManager_FWD_DEFINED__
#define __IRDPSRAPIAttendeeManager_FWD_DEFINED__
typedef interface IRDPSRAPIAttendeeManager IRDPSRAPIAttendeeManager;
#ifdef __cplusplus
interface IRDPSRAPIAttendeeManager;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIAttendeeDisconnectInfo_FWD_DEFINED__
#define __IRDPSRAPIAttendeeDisconnectInfo_FWD_DEFINED__
typedef interface IRDPSRAPIAttendeeDisconnectInfo IRDPSRAPIAttendeeDisconnectInfo;
#ifdef __cplusplus
interface IRDPSRAPIAttendeeDisconnectInfo;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIVirtualChannel_FWD_DEFINED__
#define __IRDPSRAPIVirtualChannel_FWD_DEFINED__
typedef interface IRDPSRAPIVirtualChannel IRDPSRAPIVirtualChannel;
#ifdef __cplusplus
interface IRDPSRAPIVirtualChannel;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIVirtualChannelManager_FWD_DEFINED__
#define __IRDPSRAPIVirtualChannelManager_FWD_DEFINED__
typedef interface IRDPSRAPIVirtualChannelManager IRDPSRAPIVirtualChannelManager;
#ifdef __cplusplus
interface IRDPSRAPIVirtualChannelManager;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIViewer_FWD_DEFINED__
#define __IRDPSRAPIViewer_FWD_DEFINED__
typedef interface IRDPSRAPIViewer IRDPSRAPIViewer;
#ifdef __cplusplus
interface IRDPSRAPIViewer;
#endif /* __cplusplus */
#endif

#ifndef __IRDPViewerRenderingSurface_FWD_DEFINED__
#define __IRDPViewerRenderingSurface_FWD_DEFINED__
typedef interface IRDPViewerRenderingSurface IRDPViewerRenderingSurface;
#ifdef __cplusplus
interface IRDPViewerRenderingSurface;
#endif /* __cplusplus */
#endif

#ifndef __IRDPViewerInputSink_FWD_DEFINED__
#define __IRDPViewerInputSink_FWD_DEFINED__
typedef interface IRDPViewerInputSink IRDPViewerInputSink;
#ifdef __cplusplus
interface IRDPViewerInputSink;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIFrameBuffer_FWD_DEFINED__
#define __IRDPSRAPIFrameBuffer_FWD_DEFINED__
typedef interface IRDPSRAPIFrameBuffer IRDPSRAPIFrameBuffer;
#ifdef __cplusplus
interface IRDPSRAPIFrameBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPITransportStreamBuffer_FWD_DEFINED__
#define __IRDPSRAPITransportStreamBuffer_FWD_DEFINED__
typedef interface IRDPSRAPITransportStreamBuffer IRDPSRAPITransportStreamBuffer;
#ifdef __cplusplus
interface IRDPSRAPITransportStreamBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPITransportStreamEvents_FWD_DEFINED__
#define __IRDPSRAPITransportStreamEvents_FWD_DEFINED__
typedef interface IRDPSRAPITransportStreamEvents IRDPSRAPITransportStreamEvents;
#ifdef __cplusplus
interface IRDPSRAPITransportStreamEvents;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPITransportStream_FWD_DEFINED__
#define __IRDPSRAPITransportStream_FWD_DEFINED__
typedef interface IRDPSRAPITransportStream IRDPSRAPITransportStream;
#ifdef __cplusplus
interface IRDPSRAPITransportStream;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPISharingSession_FWD_DEFINED__
#define __IRDPSRAPISharingSession_FWD_DEFINED__
typedef interface IRDPSRAPISharingSession IRDPSRAPISharingSession;
#ifdef __cplusplus
interface IRDPSRAPISharingSession;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPISharingSession2_FWD_DEFINED__
#define __IRDPSRAPISharingSession2_FWD_DEFINED__
typedef interface IRDPSRAPISharingSession2 IRDPSRAPISharingSession2;
#ifdef __cplusplus
interface IRDPSRAPISharingSession2;
#endif /* __cplusplus */
#endif

#ifndef ___IRDPSessionEvents_FWD_DEFINED__
#define ___IRDPSessionEvents_FWD_DEFINED__
typedef interface _IRDPSessionEvents _IRDPSessionEvents;
#ifdef __cplusplus
interface _IRDPSessionEvents;
#endif /* __cplusplus */
#endif

#ifndef __RDPViewer_FWD_DEFINED__
#define __RDPViewer_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPViewer RDPViewer;
#else
typedef struct RDPViewer RDPViewer;
#endif /* defined __cplusplus */
#endif /* defined __RDPViewer_FWD_DEFINED__ */

#ifndef __RDPSRAPISessionProperties_FWD_DEFINED__
#define __RDPSRAPISessionProperties_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPISessionProperties RDPSRAPISessionProperties;
#else
typedef struct RDPSRAPISessionProperties RDPSRAPISessionProperties;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPISessionProperties_FWD_DEFINED__ */

#ifndef __RDPSRAPIInvitationManager_FWD_DEFINED__
#define __RDPSRAPIInvitationManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIInvitationManager RDPSRAPIInvitationManager;
#else
typedef struct RDPSRAPIInvitationManager RDPSRAPIInvitationManager;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIInvitationManager_FWD_DEFINED__ */

#ifndef __RDPSRAPIInvitation_FWD_DEFINED__
#define __RDPSRAPIInvitation_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIInvitation RDPSRAPIInvitation;
#else
typedef struct RDPSRAPIInvitation RDPSRAPIInvitation;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIInvitation_FWD_DEFINED__ */

#ifndef __RDPSRAPIAttendeeManager_FWD_DEFINED__
#define __RDPSRAPIAttendeeManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIAttendeeManager RDPSRAPIAttendeeManager;
#else
typedef struct RDPSRAPIAttendeeManager RDPSRAPIAttendeeManager;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIAttendeeManager_FWD_DEFINED__ */

#ifndef __RDPSRAPIAttendee_FWD_DEFINED__
#define __RDPSRAPIAttendee_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIAttendee RDPSRAPIAttendee;
#else
typedef struct RDPSRAPIAttendee RDPSRAPIAttendee;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIAttendee_FWD_DEFINED__ */

#ifndef __RDPSRAPIAttendeeDisconnectInfo_FWD_DEFINED__
#define __RDPSRAPIAttendeeDisconnectInfo_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIAttendeeDisconnectInfo RDPSRAPIAttendeeDisconnectInfo;
#else
typedef struct RDPSRAPIAttendeeDisconnectInfo RDPSRAPIAttendeeDisconnectInfo;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIAttendeeDisconnectInfo_FWD_DEFINED__ */

#ifndef __RDPSRAPIApplicationFilter_FWD_DEFINED__
#define __RDPSRAPIApplicationFilter_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIApplicationFilter RDPSRAPIApplicationFilter;
#else
typedef struct RDPSRAPIApplicationFilter RDPSRAPIApplicationFilter;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIApplicationFilter_FWD_DEFINED__ */

#ifndef __RDPSRAPIApplicationList_FWD_DEFINED__
#define __RDPSRAPIApplicationList_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIApplicationList RDPSRAPIApplicationList;
#else
typedef struct RDPSRAPIApplicationList RDPSRAPIApplicationList;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIApplicationList_FWD_DEFINED__ */

#ifndef __RDPSRAPIApplication_FWD_DEFINED__
#define __RDPSRAPIApplication_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIApplication RDPSRAPIApplication;
#else
typedef struct RDPSRAPIApplication RDPSRAPIApplication;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIApplication_FWD_DEFINED__ */

#ifndef __RDPSRAPIWindowList_FWD_DEFINED__
#define __RDPSRAPIWindowList_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIWindowList RDPSRAPIWindowList;
#else
typedef struct RDPSRAPIWindowList RDPSRAPIWindowList;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIWindowList_FWD_DEFINED__ */

#ifndef __RDPSRAPIWindow_FWD_DEFINED__
#define __RDPSRAPIWindow_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIWindow RDPSRAPIWindow;
#else
typedef struct RDPSRAPIWindow RDPSRAPIWindow;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIWindow_FWD_DEFINED__ */

#ifndef __RDPSRAPITcpConnectionInfo_FWD_DEFINED__
#define __RDPSRAPITcpConnectionInfo_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPITcpConnectionInfo RDPSRAPITcpConnectionInfo;
#else
typedef struct RDPSRAPITcpConnectionInfo RDPSRAPITcpConnectionInfo;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPITcpConnectionInfo_FWD_DEFINED__ */

#ifndef __RDPSession_FWD_DEFINED__
#define __RDPSession_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSession RDPSession;
#else
typedef struct RDPSession RDPSession;
#endif /* defined __cplusplus */
#endif /* defined __RDPSession_FWD_DEFINED__ */

#ifndef __RDPSRAPIFrameBuffer_FWD_DEFINED__
#define __RDPSRAPIFrameBuffer_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPSRAPIFrameBuffer RDPSRAPIFrameBuffer;
#else
typedef struct RDPSRAPIFrameBuffer RDPSRAPIFrameBuffer;
#endif /* defined __cplusplus */
#endif /* defined __RDPSRAPIFrameBuffer_FWD_DEFINED__ */

#ifndef __RDPTransportStreamBuffer_FWD_DEFINED__
#define __RDPTransportStreamBuffer_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPTransportStreamBuffer RDPTransportStreamBuffer;
#else
typedef struct RDPTransportStreamBuffer RDPTransportStreamBuffer;
#endif /* defined __cplusplus */
#endif /* defined __RDPTransportStreamBuffer_FWD_DEFINED__ */

#ifndef __RDPTransportStreamEvents_FWD_DEFINED__
#define __RDPTransportStreamEvents_FWD_DEFINED__
#ifdef __cplusplus
typedef class RDPTransportStreamEvents RDPTransportStreamEvents;
#else
typedef struct RDPTransportStreamEvents RDPTransportStreamEvents;
#endif /* defined __cplusplus */
#endif /* defined __RDPTransportStreamEvents_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#define DISPID_RDPSRAPI_METHOD_OPEN (100)

#define DISPID_RDPSRAPI_METHOD_CLOSE (101)

#define DISPID_RDPSRAPI_METHOD_SETSHAREDRECT (102)

#define DISPID_RDPSRAPI_METHOD_GETSHAREDRECT (103)

#define DISPID_RDPSRAPI_METHOD_VIEWERCONNECT (104)

#define DISPID_RDPSRAPI_METHOD_VIEWERDISCONNECT (105)

#define DISPID_RDPSRAPI_METHOD_TERMINATE_CONNECTION (106)

#define DISPID_RDPSRAPI_METHOD_CREATE_INVITATION (107)

#define DISPID_RDPSRAPI_METHOD_REQUEST_CONTROL (108)

#define DISPID_RDPSRAPI_METHOD_VIRTUAL_CHANNEL_CREATE (109)

#define DISPID_RDPSRAPI_METHOD_VIRTUAL_CHANNEL_SEND_DATA (110)

#define DISPID_RDPSRAPI_METHOD_VIRTUAL_CHANNEL_SET_ACCESS (111)

#define DISPID_RDPSRAPI_METHOD_PAUSE (112)

#define DISPID_RDPSRAPI_METHOD_RESUME (113)

#define DISPID_RDPSRAPI_METHOD_SHOW_WINDOW (114)

#define DISPID_RDPSRAPI_METHOD_REQUEST_COLOR_DEPTH_CHANGE (115)

#define DISPID_RDPSRAPI_METHOD_STARTREVCONNECTLISTENER (116)

#define DISPID_RDPSRAPI_METHOD_CONNECTTOCLIENT (117)

#define DISPID_RDPSRAPI_METHOD_SET_RENDERING_SURFACE (118)

#define DISPID_RDPSRAPI_METHOD_SEND_MOUSE_BUTTON_EVENT (119)

#define DISPID_RDPSRAPI_METHOD_SEND_MOUSE_MOVE_EVENT (120)

#define DISPID_RDPSRAPI_METHOD_SEND_MOUSE_WHEEL_EVENT (121)

#define DISPID_RDPSRAPI_METHOD_SEND_KEYBOARD_EVENT (122)

#define DISPID_RDPSRAPI_METHOD_SEND_SYNC_EVENT (123)

#define DISPID_RDPSRAPI_METHOD_BEGIN_TOUCH_FRAME (124)

#define DISPID_RDPSRAPI_METHOD_ADD_TOUCH_INPUT (125)

#define DISPID_RDPSRAPI_METHOD_END_TOUCH_FRAME (126)

#define DISPID_RDPSRAPI_METHOD_CONNECTUSINGTRANSPORTSTREAM (127)

#define DISPID_RDPSRAPI_METHOD_SENDCONTROLLEVELCHANGERESPONSE (148)

#define DISPID_RDPSRAPI_METHOD_GETFRAMEBUFFERBITS (149)

#define DISPID_RDPSRAPI_PROP_DISPIDVALUE (200)

#define DISPID_RDPSRAPI_PROP_ID (201)

#define DISPID_RDPSRAPI_PROP_SESSION_PROPERTIES (202)

#define DISPID_RDPSRAPI_PROP_ATTENDEES (203)

#define DISPID_RDPSRAPI_PROP_INVITATIONS (204)

#define DISPID_RDPSRAPI_PROP_INVITATION (205)

#define DISPID_RDPSRAPI_PROP_CHANNELMANAGER (206)

#define DISPID_RDPSRAPI_PROP_VIRTUAL_CHANNEL_GETNAME (207)

#define DISPID_RDPSRAPI_PROP_VIRTUAL_CHANNEL_GETFLAGS (208)

#define DISPID_RDPSRAPI_PROP_VIRTUAL_CHANNEL_GETPRIORITY (209)

#define DISPID_RDPSRAPI_PROP_WINDOWID (210)

#define DISPID_RDPSRAPI_PROP_APPLICATION (211)

#define DISPID_RDPSRAPI_PROP_WINDOWSHARED (212)

#define DISPID_RDPSRAPI_PROP_WINDOWNAME (213)

#define DISPID_RDPSRAPI_PROP_APPNAME (214)

#define DISPID_RDPSRAPI_PROP_APPLICATION_FILTER (215)

#define DISPID_RDPSRAPI_PROP_WINDOW_LIST (216)

#define DISPID_RDPSRAPI_PROP_APPLICATION_LIST (217)

#define DISPID_RDPSRAPI_PROP_APPFILTER_ENABLED (218)

#define DISPID_RDPSRAPI_PROP_APPFILTERENABLED (219)

#define DISPID_RDPSRAPI_PROP_SHARED (220)

#define DISPID_RDPSRAPI_PROP_INVITATIONITEM (221)

#define DISPID_RDPSRAPI_PROP_DBG_CLX_CMDLINE (222)

#define DISPID_RDPSRAPI_PROP_APPFLAGS (223)

#define DISPID_RDPSRAPI_PROP_WNDFLAGS (224)

#define DISPID_RDPSRAPI_PROP_PROTOCOL_TYPE (225)

#define DISPID_RDPSRAPI_PROP_LOCAL_PORT (226)

#define DISPID_RDPSRAPI_PROP_LOCAL_IP (227)

#define DISPID_RDPSRAPI_PROP_PEER_PORT (228)

#define DISPID_RDPSRAPI_PROP_PEER_IP (229)

#define DISPID_RDPSRAPI_PROP_ATTENDEE_FLAGS (230)

#define DISPID_RDPSRAPI_PROP_CONINFO (231)

#define DISPID_RDPSRAPI_PROP_CONNECTION_STRING (232)

#define DISPID_RDPSRAPI_PROP_GROUP_NAME (233)

#define DISPID_RDPSRAPI_PROP_PASSWORD (234)

#define DISPID_RDPSRAPI_PROP_ATTENDEELIMIT (235)

#define DISPID_RDPSRAPI_PROP_REVOKED (236)

#define DISPID_RDPSRAPI_PROP_DISCONNECTED_STRING (237)

#define DISPID_RDPSRAPI_PROP_USESMARTSIZING (238)

#define DISPID_RDPSRAPI_PROP_SESSION_COLORDEPTH (239)

#define DISPID_RDPSRAPI_PROP_REASON (240)

#define DISPID_RDPSRAPI_PROP_CODE (241)

#define DISPID_RDPSRAPI_PROP_CTRL_LEVEL (242)

#define DISPID_RDPSRAPI_PROP_REMOTENAME (243)

#define DISPID_RDPSRAPI_PROP_COUNT (244)

#define DISPID_RDPSRAPI_PROP_FRAMEBUFFER_HEIGHT (251)

#define DISPID_RDPSRAPI_PROP_FRAMEBUFFER_WIDTH (252)

#define DISPID_RDPSRAPI_PROP_FRAMEBUFFER_BPP (253)

#define DISPID_RDPSRAPI_PROP_FRAMEBUFFER (254)

#define DISPID_RDPSRAPI_EVENT_ON_ATTENDEE_CONNECTED (301)

#define DISPID_RDPSRAPI_EVENT_ON_ATTENDEE_DISCONNECTED (302)

#define DISPID_RDPSRAPI_EVENT_ON_ATTENDEE_UPDATE (303)

#define DISPID_RDPSRAPI_EVENT_ON_ERROR (304)

#define DISPID_RDPSRAPI_EVENT_ON_VIEWER_CONNECTED (305)

#define DISPID_RDPSRAPI_EVENT_ON_VIEWER_DISCONNECTED (306)

#define DISPID_RDPSRAPI_EVENT_ON_VIEWER_AUTHENTICATED (307)

#define DISPID_RDPSRAPI_EVENT_ON_VIEWER_CONNECTFAILED (308)

#define DISPID_RDPSRAPI_EVENT_ON_CTRLLEVEL_CHANGE_REQUEST (309)

#define DISPID_RDPSRAPI_EVENT_ON_GRAPHICS_STREAM_PAUSED (310)

#define DISPID_RDPSRAPI_EVENT_ON_GRAPHICS_STREAM_RESUMED (311)

#define DISPID_RDPSRAPI_EVENT_ON_VIRTUAL_CHANNEL_JOIN (312)

#define DISPID_RDPSRAPI_EVENT_ON_VIRTUAL_CHANNEL_LEAVE (313)

#define DISPID_RDPSRAPI_EVENT_ON_VIRTUAL_CHANNEL_DATARECEIVED (314)

#define DISPID_RDPSRAPI_EVENT_ON_VIRTUAL_CHANNEL_SENDCOMPLETED (315)

#define DISPID_RDPSRAPI_EVENT_ON_APPLICATION_OPEN (316)

#define DISPID_RDPSRAPI_EVENT_ON_APPLICATION_CLOSE (317)

#define DISPID_RDPSRAPI_EVENT_ON_APPLICATION_UPDATE (318)

#define DISPID_RDPSRAPI_EVENT_ON_WINDOW_OPEN (319)

#define DISPID_RDPSRAPI_EVENT_ON_WINDOW_CLOSE (320)

#define DISPID_RDPSRAPI_EVENT_ON_WINDOW_UPDATE (321)

#define DISPID_RDPSRAPI_EVENT_ON_APPFILTER_UPDATE (322)

#define DISPID_RDPSRAPI_EVENT_ON_SHARED_RECT_CHANGED (323)

#define DISPID_RDPSRAPI_EVENT_ON_FOCUSRELEASED (324)

#define DISPID_RDPSRAPI_EVENT_ON_SHARED_DESKTOP_SETTINGS_CHANGED (325)

#define DISPID_RDPSRAPI_EVENT_ON_CTRLLEVEL_CHANGE_RESPONSE (338)

#define DISPID_RDPAPI_EVENT_ON_BOUNDING_RECT_CHANGED (340)

#define DISPID_RDPSRAPI_METHOD_STREAM_ALLOCBUFFER (421)

#define DISPID_RDPSRAPI_METHOD_STREAM_FREEBUFFER (422)

#define DISPID_RDPSRAPI_METHOD_STREAMSENDDATA (423)

#define DISPID_RDPSRAPI_METHOD_STREAMREADDATA (424)

#define DISPID_RDPSRAPI_METHOD_STREAMOPEN (425)

#define DISPID_RDPSRAPI_METHOD_STREAMCLOSE (426)

#define DISPID_RDPSRAPI_PROP_STREAMBUFFER_STORAGE (555)

#define DISPID_RDPSRAPI_PROP_STREAMBUFFER_PAYLOADSIZE (558)

#define DISPID_RDPSRAPI_PROP_STREAMBUFFER_PAYLOADOFFSET (559)

#define DISPID_RDPSRAPI_PROP_STREAMBUFFER_CONTEXT (560)

#define DISPID_RDPSRAPI_PROP_STREAMBUFFER_FLAGS (561)

#define DISPID_RDPSRAPI_PROP_STREAMBUFFER_STORESIZE (562)

#define DISPID_RDPSRAPI_EVENT_ON_STREAM_SENDCOMPLETED (632)

#define DISPID_RDPSRAPI_EVENT_ON_STREAM_DATARECEIVED (633)

#define DISPID_RDPSRAPI_EVENT_ON_STREAM_CLOSED (634)

#define DISPID_RDPSRAPI_EVENT_VIEW_MOUSE_BUTTON_RECEIVED (700)

#define DISPID_RDPSRAPI_EVENT_VIEW_MOUSE_MOVE_RECEIVED (701)

#define DISPID_RDPSRAPI_EVENT_VIEW_MOUSE_WHEEL_RECEIVED (702)

typedef enum __WIDL_rdpencomapi_generated_name_00000020 {
    CTRL_LEVEL_MIN = 0,
    CTRL_LEVEL_INVALID = 0,
    CTRL_LEVEL_NONE = 1,
    CTRL_LEVEL_VIEW = 2,
    CTRL_LEVEL_INTERACTIVE = 3,
    CTRL_LEVEL_REQCTRL_VIEW = 4,
    CTRL_LEVEL_REQCTRL_INTERACTIVE = 5,
    CTRL_LEVEL_MAX = 5
} CTRL_LEVEL;
typedef enum __WIDL_rdpencomapi_generated_name_00000021 {
    ATTENDEE_DISCONNECT_REASON_MIN = 0,
    ATTENDEE_DISCONNECT_REASON_APP = 0,
    ATTENDEE_DISCONNECT_REASON_ERR = 1,
    ATTENDEE_DISCONNECT_REASON_CLI = 2,
    ATTENDEE_DISCONNECT_REASON_MAX = 2
} ATTENDEE_DISCONNECT_REASON;
typedef enum __WIDL_rdpencomapi_generated_name_00000022 {
    CHANNEL_PRIORITY_LO = 0,
    CHANNEL_PRIORITY_MED = 1,
    CHANNEL_PRIORITY_HI = 2
} CHANNEL_PRIORITY;
typedef enum __WIDL_rdpencomapi_generated_name_00000023 {
    CHANNEL_FLAGS_LEGACY = 0x1,
    CHANNEL_FLAGS_UNCOMPRESSED = 0x2,
    CHANNEL_FLAGS_DYNAMIC = 0x4
} CHANNEL_FLAGS;
typedef enum __WIDL_rdpencomapi_generated_name_00000024 {
    CHANNEL_ACCESS_ENUM_NONE = 0,
    CHANNEL_ACCESS_ENUM_SENDRECEIVE = 1
} CHANNEL_ACCESS_ENUM;
typedef enum __WIDL_rdpencomapi_generated_name_00000025 {
    ATTENDEE_FLAGS_LOCAL = 1
} RDPENCOMAPI_ATTENDEE_FLAGS;
typedef enum __WIDL_rdpencomapi_generated_name_00000026 {
    WND_FLAG_PRIVILEGED = 1
} RDPSRAPI_WND_FLAGS;
typedef enum __WIDL_rdpencomapi_generated_name_00000027 {
    APP_FLAG_PRIVILEGED = 1
} RDPSRAPI_APP_FLAGS;
typedef enum __WIDL_rdpencomapi_generated_name_00000028 {
    RDPSRAPI_MOUSE_BUTTON_BUTTON1 = 0,
    RDPSRAPI_MOUSE_BUTTON_BUTTON2 = 1,
    RDPSRAPI_MOUSE_BUTTON_BUTTON3 = 2,
    RDPSRAPI_MOUSE_BUTTON_XBUTTON1 = 3,
    RDPSRAPI_MOUSE_BUTTON_XBUTTON2 = 4,
    RDPSRAPI_MOUSE_BUTTON_XBUTTON3 = 5
} RDPSRAPI_MOUSE_BUTTON_TYPE;
typedef enum __WIDL_rdpencomapi_generated_name_00000029 {
    RDPSRAPI_KBD_CODE_SCANCODE = 0,
    RDPSRAPI_KBD_CODE_UNICODE = 1
} RDPSRAPI_KBD_CODE_TYPE;
typedef enum __WIDL_rdpencomapi_generated_name_0000002A {
    RDPSRAPI_KBD_SYNC_FLAG_SCROLL_LOCK = 1,
    RDPSRAPI_KBD_SYNC_FLAG_NUM_LOCK = 2,
    RDPSRAPI_KBD_SYNC_FLAG_CAPS_LOCK = 4,
    RDPSRAPI_KBD_SYNC_FLAG_KANA_LOCK = 8
} RDPSRAPI_KBD_SYNC_FLAG;
/*****************************************************************************
 * IRDPSRAPIDebug interface
 */
#ifndef __IRDPSRAPIDebug_INTERFACE_DEFINED__
#define __IRDPSRAPIDebug_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIDebug, 0xaa1e42b5, 0x496d, 0x4ca4, 0xa6,0x90, 0x34,0x8d,0xcb,0x2e,0xc4,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa1e42b5-496d-4ca4-a690-348dcb2ec4ad")
IRDPSRAPIDebug : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE put_CLXCmdLine(
        BSTR CLXCmdLine) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CLXCmdLine(
        BSTR *pCLXCmdLine) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIDebug, 0xaa1e42b5, 0x496d, 0x4ca4, 0xa6,0x90, 0x34,0x8d,0xcb,0x2e,0xc4,0xad)
#endif
#else
typedef struct IRDPSRAPIDebugVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIDebug *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIDebug *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIDebug *This);

    /*** IRDPSRAPIDebug methods ***/
    HRESULT (STDMETHODCALLTYPE *put_CLXCmdLine)(
        IRDPSRAPIDebug *This,
        BSTR CLXCmdLine);

    HRESULT (STDMETHODCALLTYPE *get_CLXCmdLine)(
        IRDPSRAPIDebug *This,
        BSTR *pCLXCmdLine);

    END_INTERFACE
} IRDPSRAPIDebugVtbl;

interface IRDPSRAPIDebug {
    CONST_VTBL IRDPSRAPIDebugVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIDebug_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIDebug_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIDebug_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPSRAPIDebug methods ***/
#define IRDPSRAPIDebug_put_CLXCmdLine(This,CLXCmdLine) (This)->lpVtbl->put_CLXCmdLine(This,CLXCmdLine)
#define IRDPSRAPIDebug_get_CLXCmdLine(This,pCLXCmdLine) (This)->lpVtbl->get_CLXCmdLine(This,pCLXCmdLine)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIDebug_QueryInterface(IRDPSRAPIDebug* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIDebug_AddRef(IRDPSRAPIDebug* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIDebug_Release(IRDPSRAPIDebug* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPSRAPIDebug methods ***/
static inline HRESULT IRDPSRAPIDebug_put_CLXCmdLine(IRDPSRAPIDebug* This,BSTR CLXCmdLine) {
    return This->lpVtbl->put_CLXCmdLine(This,CLXCmdLine);
}
static inline HRESULT IRDPSRAPIDebug_get_CLXCmdLine(IRDPSRAPIDebug* This,BSTR *pCLXCmdLine) {
    return This->lpVtbl->get_CLXCmdLine(This,pCLXCmdLine);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIDebug_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIPerfCounterLogger interface
 */
#ifndef __IRDPSRAPIPerfCounterLogger_INTERFACE_DEFINED__
#define __IRDPSRAPIPerfCounterLogger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIPerfCounterLogger, 0x071c2533, 0x0fa4, 0x4e8f, 0xae,0x83, 0x9c,0x10,0xb4,0x30,0x5a,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("071c2533-0fa4-4e8f-ae83-9c10b4305ab5")
IRDPSRAPIPerfCounterLogger : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE LogValue(
        INT64 lValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIPerfCounterLogger, 0x071c2533, 0x0fa4, 0x4e8f, 0xae,0x83, 0x9c,0x10,0xb4,0x30,0x5a,0xb5)
#endif
#else
typedef struct IRDPSRAPIPerfCounterLoggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIPerfCounterLogger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIPerfCounterLogger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIPerfCounterLogger *This);

    /*** IRDPSRAPIPerfCounterLogger methods ***/
    HRESULT (STDMETHODCALLTYPE *LogValue)(
        IRDPSRAPIPerfCounterLogger *This,
        INT64 lValue);

    END_INTERFACE
} IRDPSRAPIPerfCounterLoggerVtbl;

interface IRDPSRAPIPerfCounterLogger {
    CONST_VTBL IRDPSRAPIPerfCounterLoggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIPerfCounterLogger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIPerfCounterLogger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIPerfCounterLogger_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPSRAPIPerfCounterLogger methods ***/
#define IRDPSRAPIPerfCounterLogger_LogValue(This,lValue) (This)->lpVtbl->LogValue(This,lValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIPerfCounterLogger_QueryInterface(IRDPSRAPIPerfCounterLogger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIPerfCounterLogger_AddRef(IRDPSRAPIPerfCounterLogger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIPerfCounterLogger_Release(IRDPSRAPIPerfCounterLogger* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPSRAPIPerfCounterLogger methods ***/
static inline HRESULT IRDPSRAPIPerfCounterLogger_LogValue(IRDPSRAPIPerfCounterLogger* This,INT64 lValue) {
    return This->lpVtbl->LogValue(This,lValue);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIPerfCounterLogger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIPerfCounterLoggingManager interface
 */
#ifndef __IRDPSRAPIPerfCounterLoggingManager_INTERFACE_DEFINED__
#define __IRDPSRAPIPerfCounterLoggingManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIPerfCounterLoggingManager, 0x9a512c86, 0xac6e, 0x4a8e, 0xb1,0xa4, 0xfc,0xef,0x36,0x3f,0x6e,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9a512c86-ac6e-4a8e-b1a4-fcef363f6e64")
IRDPSRAPIPerfCounterLoggingManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateLogger(
        BSTR bstrCounterName,
        IRDPSRAPIPerfCounterLogger **ppLogger) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIPerfCounterLoggingManager, 0x9a512c86, 0xac6e, 0x4a8e, 0xb1,0xa4, 0xfc,0xef,0x36,0x3f,0x6e,0x64)
#endif
#else
typedef struct IRDPSRAPIPerfCounterLoggingManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIPerfCounterLoggingManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIPerfCounterLoggingManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIPerfCounterLoggingManager *This);

    /*** IRDPSRAPIPerfCounterLoggingManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateLogger)(
        IRDPSRAPIPerfCounterLoggingManager *This,
        BSTR bstrCounterName,
        IRDPSRAPIPerfCounterLogger **ppLogger);

    END_INTERFACE
} IRDPSRAPIPerfCounterLoggingManagerVtbl;

interface IRDPSRAPIPerfCounterLoggingManager {
    CONST_VTBL IRDPSRAPIPerfCounterLoggingManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIPerfCounterLoggingManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIPerfCounterLoggingManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIPerfCounterLoggingManager_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPSRAPIPerfCounterLoggingManager methods ***/
#define IRDPSRAPIPerfCounterLoggingManager_CreateLogger(This,bstrCounterName,ppLogger) (This)->lpVtbl->CreateLogger(This,bstrCounterName,ppLogger)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIPerfCounterLoggingManager_QueryInterface(IRDPSRAPIPerfCounterLoggingManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIPerfCounterLoggingManager_AddRef(IRDPSRAPIPerfCounterLoggingManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIPerfCounterLoggingManager_Release(IRDPSRAPIPerfCounterLoggingManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPSRAPIPerfCounterLoggingManager methods ***/
static inline HRESULT IRDPSRAPIPerfCounterLoggingManager_CreateLogger(IRDPSRAPIPerfCounterLoggingManager* This,BSTR bstrCounterName,IRDPSRAPIPerfCounterLogger **ppLogger) {
    return This->lpVtbl->CreateLogger(This,bstrCounterName,ppLogger);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIPerfCounterLoggingManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIAudioStream interface
 */
#ifndef __IRDPSRAPIAudioStream_INTERFACE_DEFINED__
#define __IRDPSRAPIAudioStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIAudioStream, 0xe3e30ef9, 0x89c6, 0x4541, 0xba,0x3b, 0x19,0x33,0x6a,0xc6,0xd3,0x1c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e3e30ef9-89c6-4541-ba3b-19336ac6d31c")
IRDPSRAPIAudioStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        INT64 *pnPeriodInHundredNsIntervals) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBuffer(
        BYTE **ppbData,
        UINT32 *pcbData,
        UINT64 *pTimestamp) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeBuffer(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIAudioStream, 0xe3e30ef9, 0x89c6, 0x4541, 0xba,0x3b, 0x19,0x33,0x6a,0xc6,0xd3,0x1c)
#endif
#else
typedef struct IRDPSRAPIAudioStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIAudioStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIAudioStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIAudioStream *This);

    /*** IRDPSRAPIAudioStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IRDPSRAPIAudioStream *This,
        INT64 *pnPeriodInHundredNsIntervals);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IRDPSRAPIAudioStream *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IRDPSRAPIAudioStream *This);

    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IRDPSRAPIAudioStream *This,
        BYTE **ppbData,
        UINT32 *pcbData,
        UINT64 *pTimestamp);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IRDPSRAPIAudioStream *This);

    END_INTERFACE
} IRDPSRAPIAudioStreamVtbl;

interface IRDPSRAPIAudioStream {
    CONST_VTBL IRDPSRAPIAudioStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIAudioStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIAudioStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIAudioStream_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPSRAPIAudioStream methods ***/
#define IRDPSRAPIAudioStream_Initialize(This,pnPeriodInHundredNsIntervals) (This)->lpVtbl->Initialize(This,pnPeriodInHundredNsIntervals)
#define IRDPSRAPIAudioStream_Start(This) (This)->lpVtbl->Start(This)
#define IRDPSRAPIAudioStream_Stop(This) (This)->lpVtbl->Stop(This)
#define IRDPSRAPIAudioStream_GetBuffer(This,ppbData,pcbData,pTimestamp) (This)->lpVtbl->GetBuffer(This,ppbData,pcbData,pTimestamp)
#define IRDPSRAPIAudioStream_FreeBuffer(This) (This)->lpVtbl->FreeBuffer(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIAudioStream_QueryInterface(IRDPSRAPIAudioStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIAudioStream_AddRef(IRDPSRAPIAudioStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIAudioStream_Release(IRDPSRAPIAudioStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPSRAPIAudioStream methods ***/
static inline HRESULT IRDPSRAPIAudioStream_Initialize(IRDPSRAPIAudioStream* This,INT64 *pnPeriodInHundredNsIntervals) {
    return This->lpVtbl->Initialize(This,pnPeriodInHundredNsIntervals);
}
static inline HRESULT IRDPSRAPIAudioStream_Start(IRDPSRAPIAudioStream* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT IRDPSRAPIAudioStream_Stop(IRDPSRAPIAudioStream* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IRDPSRAPIAudioStream_GetBuffer(IRDPSRAPIAudioStream* This,BYTE **ppbData,UINT32 *pcbData,UINT64 *pTimestamp) {
    return This->lpVtbl->GetBuffer(This,ppbData,pcbData,pTimestamp);
}
static inline HRESULT IRDPSRAPIAudioStream_FreeBuffer(IRDPSRAPIAudioStream* This) {
    return This->lpVtbl->FreeBuffer(This);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIAudioStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIClipboardUseEvents interface
 */
#ifndef __IRDPSRAPIClipboardUseEvents_INTERFACE_DEFINED__
#define __IRDPSRAPIClipboardUseEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIClipboardUseEvents, 0xd559f59a, 0x7a27, 0x4138, 0x87,0x63, 0x24,0x7c,0xe5,0xf6,0x59,0xa8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d559f59a-7a27-4138-8763-247ce5f659a8")
IRDPSRAPIClipboardUseEvents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnPasteFromClipboard(
        UINT clipboardFormat,
        IDispatch *pAttendee,
        VARIANT_BOOL *pRetVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIClipboardUseEvents, 0xd559f59a, 0x7a27, 0x4138, 0x87,0x63, 0x24,0x7c,0xe5,0xf6,0x59,0xa8)
#endif
#else
typedef struct IRDPSRAPIClipboardUseEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIClipboardUseEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIClipboardUseEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIClipboardUseEvents *This);

    /*** IRDPSRAPIClipboardUseEvents methods ***/
    HRESULT (STDMETHODCALLTYPE *OnPasteFromClipboard)(
        IRDPSRAPIClipboardUseEvents *This,
        UINT clipboardFormat,
        IDispatch *pAttendee,
        VARIANT_BOOL *pRetVal);

    END_INTERFACE
} IRDPSRAPIClipboardUseEventsVtbl;

interface IRDPSRAPIClipboardUseEvents {
    CONST_VTBL IRDPSRAPIClipboardUseEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIClipboardUseEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIClipboardUseEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIClipboardUseEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPSRAPIClipboardUseEvents methods ***/
#define IRDPSRAPIClipboardUseEvents_OnPasteFromClipboard(This,clipboardFormat,pAttendee,pRetVal) (This)->lpVtbl->OnPasteFromClipboard(This,clipboardFormat,pAttendee,pRetVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIClipboardUseEvents_QueryInterface(IRDPSRAPIClipboardUseEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIClipboardUseEvents_AddRef(IRDPSRAPIClipboardUseEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIClipboardUseEvents_Release(IRDPSRAPIClipboardUseEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPSRAPIClipboardUseEvents methods ***/
static inline HRESULT IRDPSRAPIClipboardUseEvents_OnPasteFromClipboard(IRDPSRAPIClipboardUseEvents* This,UINT clipboardFormat,IDispatch *pAttendee,VARIANT_BOOL *pRetVal) {
    return This->lpVtbl->OnPasteFromClipboard(This,clipboardFormat,pAttendee,pRetVal);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIClipboardUseEvents_INTERFACE_DEFINED__ */

#ifndef __IRDPSRAPIApplication_FWD_DEFINED__
#define __IRDPSRAPIApplication_FWD_DEFINED__
typedef interface IRDPSRAPIApplication IRDPSRAPIApplication;
#ifdef __cplusplus
interface IRDPSRAPIApplication;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IRDPSRAPIWindow interface
 */
#ifndef __IRDPSRAPIWindow_INTERFACE_DEFINED__
#define __IRDPSRAPIWindow_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIWindow, 0xbeafe0f9, 0xc77b, 0x4933, 0xba,0x9f, 0xa2,0x4c,0xdd,0xcc,0x27,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("beafe0f9-c77b-4933-ba9f-a24cddcc27cf")
IRDPSRAPIWindow : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Id(
        LONG *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Application(
        IRDPSRAPIApplication **pApplication) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Shared(
        VARIANT_BOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Shared(
        VARIANT_BOOL NewVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE Show(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Flags(
        ULONG *pdwFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIWindow, 0xbeafe0f9, 0xc77b, 0x4933, 0xba,0x9f, 0xa2,0x4c,0xdd,0xcc,0x27,0xcf)
#endif
#else
typedef struct IRDPSRAPIWindowVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIWindow *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIWindow *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIWindow *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIWindow *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIWindow *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIWindow *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIWindow *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IRDPSRAPIWindow *This,
        LONG *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Application)(
        IRDPSRAPIWindow *This,
        IRDPSRAPIApplication **pApplication);

    HRESULT (STDMETHODCALLTYPE *get_Shared)(
        IRDPSRAPIWindow *This,
        VARIANT_BOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *put_Shared)(
        IRDPSRAPIWindow *This,
        VARIANT_BOOL NewVal);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IRDPSRAPIWindow *This,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *Show)(
        IRDPSRAPIWindow *This);

    HRESULT (STDMETHODCALLTYPE *get_Flags)(
        IRDPSRAPIWindow *This,
        ULONG *pdwFlags);

    END_INTERFACE
} IRDPSRAPIWindowVtbl;

interface IRDPSRAPIWindow {
    CONST_VTBL IRDPSRAPIWindowVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIWindow_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIWindow_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIWindow_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIWindow_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIWindow_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIWindow_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIWindow_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIWindow methods ***/
#define IRDPSRAPIWindow_get_Id(This,pRetVal) (This)->lpVtbl->get_Id(This,pRetVal)
#define IRDPSRAPIWindow_get_Application(This,pApplication) (This)->lpVtbl->get_Application(This,pApplication)
#define IRDPSRAPIWindow_get_Shared(This,pRetVal) (This)->lpVtbl->get_Shared(This,pRetVal)
#define IRDPSRAPIWindow_put_Shared(This,NewVal) (This)->lpVtbl->put_Shared(This,NewVal)
#define IRDPSRAPIWindow_get_Name(This,pRetVal) (This)->lpVtbl->get_Name(This,pRetVal)
#define IRDPSRAPIWindow_Show(This) (This)->lpVtbl->Show(This)
#define IRDPSRAPIWindow_get_Flags(This,pdwFlags) (This)->lpVtbl->get_Flags(This,pdwFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIWindow_QueryInterface(IRDPSRAPIWindow* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIWindow_AddRef(IRDPSRAPIWindow* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIWindow_Release(IRDPSRAPIWindow* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIWindow_GetTypeInfoCount(IRDPSRAPIWindow* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIWindow_GetTypeInfo(IRDPSRAPIWindow* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIWindow_GetIDsOfNames(IRDPSRAPIWindow* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIWindow_Invoke(IRDPSRAPIWindow* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIWindow methods ***/
static inline HRESULT IRDPSRAPIWindow_get_Id(IRDPSRAPIWindow* This,LONG *pRetVal) {
    return This->lpVtbl->get_Id(This,pRetVal);
}
static inline HRESULT IRDPSRAPIWindow_get_Application(IRDPSRAPIWindow* This,IRDPSRAPIApplication **pApplication) {
    return This->lpVtbl->get_Application(This,pApplication);
}
static inline HRESULT IRDPSRAPIWindow_get_Shared(IRDPSRAPIWindow* This,VARIANT_BOOL *pRetVal) {
    return This->lpVtbl->get_Shared(This,pRetVal);
}
static inline HRESULT IRDPSRAPIWindow_put_Shared(IRDPSRAPIWindow* This,VARIANT_BOOL NewVal) {
    return This->lpVtbl->put_Shared(This,NewVal);
}
static inline HRESULT IRDPSRAPIWindow_get_Name(IRDPSRAPIWindow* This,BSTR *pRetVal) {
    return This->lpVtbl->get_Name(This,pRetVal);
}
static inline HRESULT IRDPSRAPIWindow_Show(IRDPSRAPIWindow* This) {
    return This->lpVtbl->Show(This);
}
static inline HRESULT IRDPSRAPIWindow_get_Flags(IRDPSRAPIWindow* This,ULONG *pdwFlags) {
    return This->lpVtbl->get_Flags(This,pdwFlags);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIWindow_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIWindowList interface
 */
#ifndef __IRDPSRAPIWindowList_INTERFACE_DEFINED__
#define __IRDPSRAPIWindowList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIWindowList, 0x8a05ce44, 0x715a, 0x4116, 0xa1,0x89, 0xa1,0x18,0xf3,0x0a,0x07,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8a05ce44-715a-4116-a189-a118f30a07bd")
IRDPSRAPIWindowList : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG item,
        IRDPSRAPIWindow **pWindow) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIWindowList, 0x8a05ce44, 0x715a, 0x4116, 0xa1,0x89, 0xa1,0x18,0xf3,0x0a,0x07,0xbd)
#endif
#else
typedef struct IRDPSRAPIWindowListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIWindowList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIWindowList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIWindowList *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIWindowList *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIWindowList *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIWindowList *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIWindowList *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIWindowList methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IRDPSRAPIWindowList *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IRDPSRAPIWindowList *This,
        LONG item,
        IRDPSRAPIWindow **pWindow);

    END_INTERFACE
} IRDPSRAPIWindowListVtbl;

interface IRDPSRAPIWindowList {
    CONST_VTBL IRDPSRAPIWindowListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIWindowList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIWindowList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIWindowList_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIWindowList_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIWindowList_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIWindowList_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIWindowList_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIWindowList methods ***/
#define IRDPSRAPIWindowList_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IRDPSRAPIWindowList_get_Item(This,item,pWindow) (This)->lpVtbl->get_Item(This,item,pWindow)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIWindowList_QueryInterface(IRDPSRAPIWindowList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIWindowList_AddRef(IRDPSRAPIWindowList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIWindowList_Release(IRDPSRAPIWindowList* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIWindowList_GetTypeInfoCount(IRDPSRAPIWindowList* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIWindowList_GetTypeInfo(IRDPSRAPIWindowList* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIWindowList_GetIDsOfNames(IRDPSRAPIWindowList* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIWindowList_Invoke(IRDPSRAPIWindowList* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIWindowList methods ***/
static inline HRESULT IRDPSRAPIWindowList_get__NewEnum(IRDPSRAPIWindowList* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IRDPSRAPIWindowList_get_Item(IRDPSRAPIWindowList* This,LONG item,IRDPSRAPIWindow **pWindow) {
    return This->lpVtbl->get_Item(This,item,pWindow);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIWindowList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIApplication interface
 */
#ifndef __IRDPSRAPIApplication_INTERFACE_DEFINED__
#define __IRDPSRAPIApplication_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIApplication, 0x41e7a09d, 0xeb7a, 0x436e, 0x93,0x5d, 0x78,0x0c,0xa2,0x62,0x83,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("41e7a09d-eb7a-436e-935d-780ca2628324")
IRDPSRAPIApplication : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Windows(
        IRDPSRAPIWindowList **pWindowList) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        LONG *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Shared(
        VARIANT_BOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Shared(
        VARIANT_BOOL NewVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Flags(
        ULONG *pdwFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIApplication, 0x41e7a09d, 0xeb7a, 0x436e, 0x93,0x5d, 0x78,0x0c,0xa2,0x62,0x83,0x24)
#endif
#else
typedef struct IRDPSRAPIApplicationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIApplication *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIApplication *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIApplication *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIApplication *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIApplication *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIApplication *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIApplication *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIApplication methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Windows)(
        IRDPSRAPIApplication *This,
        IRDPSRAPIWindowList **pWindowList);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IRDPSRAPIApplication *This,
        LONG *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Shared)(
        IRDPSRAPIApplication *This,
        VARIANT_BOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *put_Shared)(
        IRDPSRAPIApplication *This,
        VARIANT_BOOL NewVal);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IRDPSRAPIApplication *This,
        BSTR *pRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Flags)(
        IRDPSRAPIApplication *This,
        ULONG *pdwFlags);

    END_INTERFACE
} IRDPSRAPIApplicationVtbl;

interface IRDPSRAPIApplication {
    CONST_VTBL IRDPSRAPIApplicationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIApplication_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIApplication_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIApplication_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIApplication_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIApplication_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIApplication_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIApplication_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIApplication methods ***/
#define IRDPSRAPIApplication_get_Windows(This,pWindowList) (This)->lpVtbl->get_Windows(This,pWindowList)
#define IRDPSRAPIApplication_get_Id(This,pRetVal) (This)->lpVtbl->get_Id(This,pRetVal)
#define IRDPSRAPIApplication_get_Shared(This,pRetVal) (This)->lpVtbl->get_Shared(This,pRetVal)
#define IRDPSRAPIApplication_put_Shared(This,NewVal) (This)->lpVtbl->put_Shared(This,NewVal)
#define IRDPSRAPIApplication_get_Name(This,pRetVal) (This)->lpVtbl->get_Name(This,pRetVal)
#define IRDPSRAPIApplication_get_Flags(This,pdwFlags) (This)->lpVtbl->get_Flags(This,pdwFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIApplication_QueryInterface(IRDPSRAPIApplication* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIApplication_AddRef(IRDPSRAPIApplication* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIApplication_Release(IRDPSRAPIApplication* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIApplication_GetTypeInfoCount(IRDPSRAPIApplication* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIApplication_GetTypeInfo(IRDPSRAPIApplication* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIApplication_GetIDsOfNames(IRDPSRAPIApplication* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIApplication_Invoke(IRDPSRAPIApplication* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIApplication methods ***/
static inline HRESULT IRDPSRAPIApplication_get_Windows(IRDPSRAPIApplication* This,IRDPSRAPIWindowList **pWindowList) {
    return This->lpVtbl->get_Windows(This,pWindowList);
}
static inline HRESULT IRDPSRAPIApplication_get_Id(IRDPSRAPIApplication* This,LONG *pRetVal) {
    return This->lpVtbl->get_Id(This,pRetVal);
}
static inline HRESULT IRDPSRAPIApplication_get_Shared(IRDPSRAPIApplication* This,VARIANT_BOOL *pRetVal) {
    return This->lpVtbl->get_Shared(This,pRetVal);
}
static inline HRESULT IRDPSRAPIApplication_put_Shared(IRDPSRAPIApplication* This,VARIANT_BOOL NewVal) {
    return This->lpVtbl->put_Shared(This,NewVal);
}
static inline HRESULT IRDPSRAPIApplication_get_Name(IRDPSRAPIApplication* This,BSTR *pRetVal) {
    return This->lpVtbl->get_Name(This,pRetVal);
}
static inline HRESULT IRDPSRAPIApplication_get_Flags(IRDPSRAPIApplication* This,ULONG *pdwFlags) {
    return This->lpVtbl->get_Flags(This,pdwFlags);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIApplication_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIApplicationList interface
 */
#ifndef __IRDPSRAPIApplicationList_INTERFACE_DEFINED__
#define __IRDPSRAPIApplicationList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIApplicationList, 0xd4b4aeb3, 0x22dc, 0x4837, 0xb3,0xb6, 0x42,0xea,0x25,0x17,0x84,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d4b4aeb3-22dc-4837-b3b6-42ea2517849a")
IRDPSRAPIApplicationList : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG item,
        IRDPSRAPIApplication **pApplication) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIApplicationList, 0xd4b4aeb3, 0x22dc, 0x4837, 0xb3,0xb6, 0x42,0xea,0x25,0x17,0x84,0x9a)
#endif
#else
typedef struct IRDPSRAPIApplicationListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIApplicationList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIApplicationList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIApplicationList *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIApplicationList *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIApplicationList *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIApplicationList *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIApplicationList *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIApplicationList methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IRDPSRAPIApplicationList *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IRDPSRAPIApplicationList *This,
        LONG item,
        IRDPSRAPIApplication **pApplication);

    END_INTERFACE
} IRDPSRAPIApplicationListVtbl;

interface IRDPSRAPIApplicationList {
    CONST_VTBL IRDPSRAPIApplicationListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIApplicationList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIApplicationList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIApplicationList_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIApplicationList_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIApplicationList_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIApplicationList_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIApplicationList_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIApplicationList methods ***/
#define IRDPSRAPIApplicationList_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IRDPSRAPIApplicationList_get_Item(This,item,pApplication) (This)->lpVtbl->get_Item(This,item,pApplication)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIApplicationList_QueryInterface(IRDPSRAPIApplicationList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIApplicationList_AddRef(IRDPSRAPIApplicationList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIApplicationList_Release(IRDPSRAPIApplicationList* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIApplicationList_GetTypeInfoCount(IRDPSRAPIApplicationList* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIApplicationList_GetTypeInfo(IRDPSRAPIApplicationList* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIApplicationList_GetIDsOfNames(IRDPSRAPIApplicationList* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIApplicationList_Invoke(IRDPSRAPIApplicationList* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIApplicationList methods ***/
static inline HRESULT IRDPSRAPIApplicationList_get__NewEnum(IRDPSRAPIApplicationList* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IRDPSRAPIApplicationList_get_Item(IRDPSRAPIApplicationList* This,LONG item,IRDPSRAPIApplication **pApplication) {
    return This->lpVtbl->get_Item(This,item,pApplication);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIApplicationList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIApplicationFilter interface
 */
#ifndef __IRDPSRAPIApplicationFilter_INTERFACE_DEFINED__
#define __IRDPSRAPIApplicationFilter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIApplicationFilter, 0xd20f10ca, 0x6637, 0x4f06, 0xb1,0xd5, 0x27,0x7e,0xa7,0xe5,0x16,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d20f10ca-6637-4f06-b1d5-277ea7e5160d")
IRDPSRAPIApplicationFilter : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Applications(
        IRDPSRAPIApplicationList **pApplications) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Windows(
        IRDPSRAPIWindowList **pWindows) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL NewVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIApplicationFilter, 0xd20f10ca, 0x6637, 0x4f06, 0xb1,0xd5, 0x27,0x7e,0xa7,0xe5,0x16,0x0d)
#endif
#else
typedef struct IRDPSRAPIApplicationFilterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIApplicationFilter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIApplicationFilter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIApplicationFilter *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIApplicationFilter *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIApplicationFilter *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIApplicationFilter *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIApplicationFilter *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIApplicationFilter methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Applications)(
        IRDPSRAPIApplicationFilter *This,
        IRDPSRAPIApplicationList **pApplications);

    HRESULT (STDMETHODCALLTYPE *get_Windows)(
        IRDPSRAPIApplicationFilter *This,
        IRDPSRAPIWindowList **pWindows);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IRDPSRAPIApplicationFilter *This,
        VARIANT_BOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IRDPSRAPIApplicationFilter *This,
        VARIANT_BOOL NewVal);

    END_INTERFACE
} IRDPSRAPIApplicationFilterVtbl;

interface IRDPSRAPIApplicationFilter {
    CONST_VTBL IRDPSRAPIApplicationFilterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIApplicationFilter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIApplicationFilter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIApplicationFilter_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIApplicationFilter_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIApplicationFilter_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIApplicationFilter_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIApplicationFilter_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIApplicationFilter methods ***/
#define IRDPSRAPIApplicationFilter_get_Applications(This,pApplications) (This)->lpVtbl->get_Applications(This,pApplications)
#define IRDPSRAPIApplicationFilter_get_Windows(This,pWindows) (This)->lpVtbl->get_Windows(This,pWindows)
#define IRDPSRAPIApplicationFilter_get_Enabled(This,pRetVal) (This)->lpVtbl->get_Enabled(This,pRetVal)
#define IRDPSRAPIApplicationFilter_put_Enabled(This,NewVal) (This)->lpVtbl->put_Enabled(This,NewVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIApplicationFilter_QueryInterface(IRDPSRAPIApplicationFilter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIApplicationFilter_AddRef(IRDPSRAPIApplicationFilter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIApplicationFilter_Release(IRDPSRAPIApplicationFilter* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIApplicationFilter_GetTypeInfoCount(IRDPSRAPIApplicationFilter* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIApplicationFilter_GetTypeInfo(IRDPSRAPIApplicationFilter* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIApplicationFilter_GetIDsOfNames(IRDPSRAPIApplicationFilter* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIApplicationFilter_Invoke(IRDPSRAPIApplicationFilter* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIApplicationFilter methods ***/
static inline HRESULT IRDPSRAPIApplicationFilter_get_Applications(IRDPSRAPIApplicationFilter* This,IRDPSRAPIApplicationList **pApplications) {
    return This->lpVtbl->get_Applications(This,pApplications);
}
static inline HRESULT IRDPSRAPIApplicationFilter_get_Windows(IRDPSRAPIApplicationFilter* This,IRDPSRAPIWindowList **pWindows) {
    return This->lpVtbl->get_Windows(This,pWindows);
}
static inline HRESULT IRDPSRAPIApplicationFilter_get_Enabled(IRDPSRAPIApplicationFilter* This,VARIANT_BOOL *pRetVal) {
    return This->lpVtbl->get_Enabled(This,pRetVal);
}
static inline HRESULT IRDPSRAPIApplicationFilter_put_Enabled(IRDPSRAPIApplicationFilter* This,VARIANT_BOOL NewVal) {
    return This->lpVtbl->put_Enabled(This,NewVal);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIApplicationFilter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPISessionProperties interface
 */
#ifndef __IRDPSRAPISessionProperties_INTERFACE_DEFINED__
#define __IRDPSRAPISessionProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPISessionProperties, 0x339b24f2, 0x9bc0, 0x4f16, 0x9a,0xac, 0xf1,0x65,0x43,0x3d,0x13,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("339b24f2-9bc0-4f16-9aac-f165433d13d4")
IRDPSRAPISessionProperties : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Property(
        BSTR PropertyName,
        VARIANT *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Property(
        BSTR PropertyName,
        VARIANT newVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPISessionProperties, 0x339b24f2, 0x9bc0, 0x4f16, 0x9a,0xac, 0xf1,0x65,0x43,0x3d,0x13,0xd4)
#endif
#else
typedef struct IRDPSRAPISessionPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPISessionProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPISessionProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPISessionProperties *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPISessionProperties *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPISessionProperties *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPISessionProperties *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPISessionProperties *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPISessionProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Property)(
        IRDPSRAPISessionProperties *This,
        BSTR PropertyName,
        VARIANT *pVal);

    HRESULT (STDMETHODCALLTYPE *put_Property)(
        IRDPSRAPISessionProperties *This,
        BSTR PropertyName,
        VARIANT newVal);

    END_INTERFACE
} IRDPSRAPISessionPropertiesVtbl;

interface IRDPSRAPISessionProperties {
    CONST_VTBL IRDPSRAPISessionPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPISessionProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPISessionProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPISessionProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPISessionProperties_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPISessionProperties_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPISessionProperties_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPISessionProperties_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPISessionProperties methods ***/
#define IRDPSRAPISessionProperties_get_Property(This,PropertyName,pVal) (This)->lpVtbl->get_Property(This,PropertyName,pVal)
#define IRDPSRAPISessionProperties_put_Property(This,PropertyName,newVal) (This)->lpVtbl->put_Property(This,PropertyName,newVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPISessionProperties_QueryInterface(IRDPSRAPISessionProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPISessionProperties_AddRef(IRDPSRAPISessionProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPISessionProperties_Release(IRDPSRAPISessionProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPISessionProperties_GetTypeInfoCount(IRDPSRAPISessionProperties* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPISessionProperties_GetTypeInfo(IRDPSRAPISessionProperties* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPISessionProperties_GetIDsOfNames(IRDPSRAPISessionProperties* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPISessionProperties_Invoke(IRDPSRAPISessionProperties* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPISessionProperties methods ***/
static inline HRESULT IRDPSRAPISessionProperties_get_Property(IRDPSRAPISessionProperties* This,BSTR PropertyName,VARIANT *pVal) {
    return This->lpVtbl->get_Property(This,PropertyName,pVal);
}
static inline HRESULT IRDPSRAPISessionProperties_put_Property(IRDPSRAPISessionProperties* This,BSTR PropertyName,VARIANT newVal) {
    return This->lpVtbl->put_Property(This,PropertyName,newVal);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPISessionProperties_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIInvitation interface
 */
#ifndef __IRDPSRAPIInvitation_INTERFACE_DEFINED__
#define __IRDPSRAPIInvitation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIInvitation, 0x4fac1d43, 0xfc51, 0x45bb, 0xb1,0xb4, 0x2b,0x53,0xaa,0x56,0x2f,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4fac1d43-fc51-45bb-b1b4-2b53aa562fa3")
IRDPSRAPIInvitation : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ConnectionString(
        BSTR *pbstrVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_GroupName(
        BSTR *pbstrVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Password(
        BSTR *pbstrVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AttendeeLimit(
        LONG *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AttendeeLimit(
        LONG NewVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Revoked(
        VARIANT_BOOL *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Revoked(
        VARIANT_BOOL NewVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIInvitation, 0x4fac1d43, 0xfc51, 0x45bb, 0xb1,0xb4, 0x2b,0x53,0xaa,0x56,0x2f,0xa3)
#endif
#else
typedef struct IRDPSRAPIInvitationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIInvitation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIInvitation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIInvitation *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIInvitation *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIInvitation *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIInvitation *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIInvitation *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIInvitation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ConnectionString)(
        IRDPSRAPIInvitation *This,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *get_GroupName)(
        IRDPSRAPIInvitation *This,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *get_Password)(
        IRDPSRAPIInvitation *This,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *get_AttendeeLimit)(
        IRDPSRAPIInvitation *This,
        LONG *pRetVal);

    HRESULT (STDMETHODCALLTYPE *put_AttendeeLimit)(
        IRDPSRAPIInvitation *This,
        LONG NewVal);

    HRESULT (STDMETHODCALLTYPE *get_Revoked)(
        IRDPSRAPIInvitation *This,
        VARIANT_BOOL *pRetVal);

    HRESULT (STDMETHODCALLTYPE *put_Revoked)(
        IRDPSRAPIInvitation *This,
        VARIANT_BOOL NewVal);

    END_INTERFACE
} IRDPSRAPIInvitationVtbl;

interface IRDPSRAPIInvitation {
    CONST_VTBL IRDPSRAPIInvitationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIInvitation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIInvitation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIInvitation_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIInvitation_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIInvitation_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIInvitation_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIInvitation_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIInvitation methods ***/
#define IRDPSRAPIInvitation_get_ConnectionString(This,pbstrVal) (This)->lpVtbl->get_ConnectionString(This,pbstrVal)
#define IRDPSRAPIInvitation_get_GroupName(This,pbstrVal) (This)->lpVtbl->get_GroupName(This,pbstrVal)
#define IRDPSRAPIInvitation_get_Password(This,pbstrVal) (This)->lpVtbl->get_Password(This,pbstrVal)
#define IRDPSRAPIInvitation_get_AttendeeLimit(This,pRetVal) (This)->lpVtbl->get_AttendeeLimit(This,pRetVal)
#define IRDPSRAPIInvitation_put_AttendeeLimit(This,NewVal) (This)->lpVtbl->put_AttendeeLimit(This,NewVal)
#define IRDPSRAPIInvitation_get_Revoked(This,pRetVal) (This)->lpVtbl->get_Revoked(This,pRetVal)
#define IRDPSRAPIInvitation_put_Revoked(This,NewVal) (This)->lpVtbl->put_Revoked(This,NewVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIInvitation_QueryInterface(IRDPSRAPIInvitation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIInvitation_AddRef(IRDPSRAPIInvitation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIInvitation_Release(IRDPSRAPIInvitation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIInvitation_GetTypeInfoCount(IRDPSRAPIInvitation* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIInvitation_GetTypeInfo(IRDPSRAPIInvitation* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIInvitation_GetIDsOfNames(IRDPSRAPIInvitation* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIInvitation_Invoke(IRDPSRAPIInvitation* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIInvitation methods ***/
static inline HRESULT IRDPSRAPIInvitation_get_ConnectionString(IRDPSRAPIInvitation* This,BSTR *pbstrVal) {
    return This->lpVtbl->get_ConnectionString(This,pbstrVal);
}
static inline HRESULT IRDPSRAPIInvitation_get_GroupName(IRDPSRAPIInvitation* This,BSTR *pbstrVal) {
    return This->lpVtbl->get_GroupName(This,pbstrVal);
}
static inline HRESULT IRDPSRAPIInvitation_get_Password(IRDPSRAPIInvitation* This,BSTR *pbstrVal) {
    return This->lpVtbl->get_Password(This,pbstrVal);
}
static inline HRESULT IRDPSRAPIInvitation_get_AttendeeLimit(IRDPSRAPIInvitation* This,LONG *pRetVal) {
    return This->lpVtbl->get_AttendeeLimit(This,pRetVal);
}
static inline HRESULT IRDPSRAPIInvitation_put_AttendeeLimit(IRDPSRAPIInvitation* This,LONG NewVal) {
    return This->lpVtbl->put_AttendeeLimit(This,NewVal);
}
static inline HRESULT IRDPSRAPIInvitation_get_Revoked(IRDPSRAPIInvitation* This,VARIANT_BOOL *pRetVal) {
    return This->lpVtbl->get_Revoked(This,pRetVal);
}
static inline HRESULT IRDPSRAPIInvitation_put_Revoked(IRDPSRAPIInvitation* This,VARIANT_BOOL NewVal) {
    return This->lpVtbl->put_Revoked(This,NewVal);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIInvitation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIInvitationManager interface
 */
#ifndef __IRDPSRAPIInvitationManager_INTERFACE_DEFINED__
#define __IRDPSRAPIInvitationManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIInvitationManager, 0x4722b049, 0x92c3, 0x4c2d, 0x8a,0x65, 0xf7,0x34,0x8f,0x64,0x4d,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4722b049-92c3-4c2d-8a65-f7348f644dcf")
IRDPSRAPIInvitationManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        VARIANT item,
        IRDPSRAPIInvitation **ppInvitation) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *pRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInvitation(
        BSTR bstrAuthString,
        BSTR bstrGroupName,
        BSTR bstrPassword,
        LONG AttendeeLimit,
        IRDPSRAPIInvitation **ppInvitation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIInvitationManager, 0x4722b049, 0x92c3, 0x4c2d, 0x8a,0x65, 0xf7,0x34,0x8f,0x64,0x4d,0xcf)
#endif
#else
typedef struct IRDPSRAPIInvitationManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIInvitationManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIInvitationManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIInvitationManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIInvitationManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIInvitationManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIInvitationManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIInvitationManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIInvitationManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IRDPSRAPIInvitationManager *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IRDPSRAPIInvitationManager *This,
        VARIANT item,
        IRDPSRAPIInvitation **ppInvitation);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IRDPSRAPIInvitationManager *This,
        LONG *pRetVal);

    HRESULT (STDMETHODCALLTYPE *CreateInvitation)(
        IRDPSRAPIInvitationManager *This,
        BSTR bstrAuthString,
        BSTR bstrGroupName,
        BSTR bstrPassword,
        LONG AttendeeLimit,
        IRDPSRAPIInvitation **ppInvitation);

    END_INTERFACE
} IRDPSRAPIInvitationManagerVtbl;

interface IRDPSRAPIInvitationManager {
    CONST_VTBL IRDPSRAPIInvitationManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIInvitationManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIInvitationManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIInvitationManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIInvitationManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIInvitationManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIInvitationManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIInvitationManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIInvitationManager methods ***/
#define IRDPSRAPIInvitationManager_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IRDPSRAPIInvitationManager_get_Item(This,item,ppInvitation) (This)->lpVtbl->get_Item(This,item,ppInvitation)
#define IRDPSRAPIInvitationManager_get_Count(This,pRetVal) (This)->lpVtbl->get_Count(This,pRetVal)
#define IRDPSRAPIInvitationManager_CreateInvitation(This,bstrAuthString,bstrGroupName,bstrPassword,AttendeeLimit,ppInvitation) (This)->lpVtbl->CreateInvitation(This,bstrAuthString,bstrGroupName,bstrPassword,AttendeeLimit,ppInvitation)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIInvitationManager_QueryInterface(IRDPSRAPIInvitationManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIInvitationManager_AddRef(IRDPSRAPIInvitationManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIInvitationManager_Release(IRDPSRAPIInvitationManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIInvitationManager_GetTypeInfoCount(IRDPSRAPIInvitationManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIInvitationManager_GetTypeInfo(IRDPSRAPIInvitationManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIInvitationManager_GetIDsOfNames(IRDPSRAPIInvitationManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIInvitationManager_Invoke(IRDPSRAPIInvitationManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIInvitationManager methods ***/
static inline HRESULT IRDPSRAPIInvitationManager_get__NewEnum(IRDPSRAPIInvitationManager* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IRDPSRAPIInvitationManager_get_Item(IRDPSRAPIInvitationManager* This,VARIANT item,IRDPSRAPIInvitation **ppInvitation) {
    return This->lpVtbl->get_Item(This,item,ppInvitation);
}
static inline HRESULT IRDPSRAPIInvitationManager_get_Count(IRDPSRAPIInvitationManager* This,LONG *pRetVal) {
    return This->lpVtbl->get_Count(This,pRetVal);
}
static inline HRESULT IRDPSRAPIInvitationManager_CreateInvitation(IRDPSRAPIInvitationManager* This,BSTR bstrAuthString,BSTR bstrGroupName,BSTR bstrPassword,LONG AttendeeLimit,IRDPSRAPIInvitation **ppInvitation) {
    return This->lpVtbl->CreateInvitation(This,bstrAuthString,bstrGroupName,bstrPassword,AttendeeLimit,ppInvitation);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIInvitationManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPITcpConnectionInfo interface
 */
#ifndef __IRDPSRAPITcpConnectionInfo_INTERFACE_DEFINED__
#define __IRDPSRAPITcpConnectionInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPITcpConnectionInfo, 0xf74049a4, 0x3d06, 0x4028, 0x81,0x93, 0x0a,0x8c,0x29,0xbc,0x24,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f74049a4-3d06-4028-8193-0a8c29bc2452")
IRDPSRAPITcpConnectionInfo : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Protocol(
        LONG *plProtocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalPort(
        LONG *plPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalIP(
        BSTR *pbsrLocalIP) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PeerPort(
        LONG *plPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PeerIP(
        BSTR *pbstrIP) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPITcpConnectionInfo, 0xf74049a4, 0x3d06, 0x4028, 0x81,0x93, 0x0a,0x8c,0x29,0xbc,0x24,0x52)
#endif
#else
typedef struct IRDPSRAPITcpConnectionInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPITcpConnectionInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPITcpConnectionInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPITcpConnectionInfo *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPITcpConnectionInfo *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPITcpConnectionInfo *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPITcpConnectionInfo *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPITcpConnectionInfo *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPITcpConnectionInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Protocol)(
        IRDPSRAPITcpConnectionInfo *This,
        LONG *plProtocol);

    HRESULT (STDMETHODCALLTYPE *get_LocalPort)(
        IRDPSRAPITcpConnectionInfo *This,
        LONG *plPort);

    HRESULT (STDMETHODCALLTYPE *get_LocalIP)(
        IRDPSRAPITcpConnectionInfo *This,
        BSTR *pbsrLocalIP);

    HRESULT (STDMETHODCALLTYPE *get_PeerPort)(
        IRDPSRAPITcpConnectionInfo *This,
        LONG *plPort);

    HRESULT (STDMETHODCALLTYPE *get_PeerIP)(
        IRDPSRAPITcpConnectionInfo *This,
        BSTR *pbstrIP);

    END_INTERFACE
} IRDPSRAPITcpConnectionInfoVtbl;

interface IRDPSRAPITcpConnectionInfo {
    CONST_VTBL IRDPSRAPITcpConnectionInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPITcpConnectionInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPITcpConnectionInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPITcpConnectionInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPITcpConnectionInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPITcpConnectionInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPITcpConnectionInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPITcpConnectionInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPITcpConnectionInfo methods ***/
#define IRDPSRAPITcpConnectionInfo_get_Protocol(This,plProtocol) (This)->lpVtbl->get_Protocol(This,plProtocol)
#define IRDPSRAPITcpConnectionInfo_get_LocalPort(This,plPort) (This)->lpVtbl->get_LocalPort(This,plPort)
#define IRDPSRAPITcpConnectionInfo_get_LocalIP(This,pbsrLocalIP) (This)->lpVtbl->get_LocalIP(This,pbsrLocalIP)
#define IRDPSRAPITcpConnectionInfo_get_PeerPort(This,plPort) (This)->lpVtbl->get_PeerPort(This,plPort)
#define IRDPSRAPITcpConnectionInfo_get_PeerIP(This,pbstrIP) (This)->lpVtbl->get_PeerIP(This,pbstrIP)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPITcpConnectionInfo_QueryInterface(IRDPSRAPITcpConnectionInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPITcpConnectionInfo_AddRef(IRDPSRAPITcpConnectionInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPITcpConnectionInfo_Release(IRDPSRAPITcpConnectionInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPITcpConnectionInfo_GetTypeInfoCount(IRDPSRAPITcpConnectionInfo* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPITcpConnectionInfo_GetTypeInfo(IRDPSRAPITcpConnectionInfo* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPITcpConnectionInfo_GetIDsOfNames(IRDPSRAPITcpConnectionInfo* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPITcpConnectionInfo_Invoke(IRDPSRAPITcpConnectionInfo* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPITcpConnectionInfo methods ***/
static inline HRESULT IRDPSRAPITcpConnectionInfo_get_Protocol(IRDPSRAPITcpConnectionInfo* This,LONG *plProtocol) {
    return This->lpVtbl->get_Protocol(This,plProtocol);
}
static inline HRESULT IRDPSRAPITcpConnectionInfo_get_LocalPort(IRDPSRAPITcpConnectionInfo* This,LONG *plPort) {
    return This->lpVtbl->get_LocalPort(This,plPort);
}
static inline HRESULT IRDPSRAPITcpConnectionInfo_get_LocalIP(IRDPSRAPITcpConnectionInfo* This,BSTR *pbsrLocalIP) {
    return This->lpVtbl->get_LocalIP(This,pbsrLocalIP);
}
static inline HRESULT IRDPSRAPITcpConnectionInfo_get_PeerPort(IRDPSRAPITcpConnectionInfo* This,LONG *plPort) {
    return This->lpVtbl->get_PeerPort(This,plPort);
}
static inline HRESULT IRDPSRAPITcpConnectionInfo_get_PeerIP(IRDPSRAPITcpConnectionInfo* This,BSTR *pbstrIP) {
    return This->lpVtbl->get_PeerIP(This,pbstrIP);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPITcpConnectionInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIAttendee interface
 */
#ifndef __IRDPSRAPIAttendee_INTERFACE_DEFINED__
#define __IRDPSRAPIAttendee_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIAttendee, 0xec0671b3, 0x1b78, 0x4b80, 0xa4,0x64, 0x91,0x32,0x24,0x75,0x43,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ec0671b3-1b78-4b80-a464-9132247543e3")
IRDPSRAPIAttendee : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Id(
        LONG *pId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemoteName(
        BSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ControlLevel(
        CTRL_LEVEL *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ControlLevel(
        CTRL_LEVEL pNewVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Invitation(
        IRDPSRAPIInvitation **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE TerminateConnection(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Flags(
        LONG *plFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ConnectivityInfo(
        IUnknown **ppVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIAttendee, 0xec0671b3, 0x1b78, 0x4b80, 0xa4,0x64, 0x91,0x32,0x24,0x75,0x43,0xe3)
#endif
#else
typedef struct IRDPSRAPIAttendeeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIAttendee *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIAttendee *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIAttendee *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIAttendee *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIAttendee *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIAttendee *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIAttendee *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIAttendee methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IRDPSRAPIAttendee *This,
        LONG *pId);

    HRESULT (STDMETHODCALLTYPE *get_RemoteName)(
        IRDPSRAPIAttendee *This,
        BSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *get_ControlLevel)(
        IRDPSRAPIAttendee *This,
        CTRL_LEVEL *pVal);

    HRESULT (STDMETHODCALLTYPE *put_ControlLevel)(
        IRDPSRAPIAttendee *This,
        CTRL_LEVEL pNewVal);

    HRESULT (STDMETHODCALLTYPE *get_Invitation)(
        IRDPSRAPIAttendee *This,
        IRDPSRAPIInvitation **ppVal);

    HRESULT (STDMETHODCALLTYPE *TerminateConnection)(
        IRDPSRAPIAttendee *This);

    HRESULT (STDMETHODCALLTYPE *get_Flags)(
        IRDPSRAPIAttendee *This,
        LONG *plFlags);

    HRESULT (STDMETHODCALLTYPE *get_ConnectivityInfo)(
        IRDPSRAPIAttendee *This,
        IUnknown **ppVal);

    END_INTERFACE
} IRDPSRAPIAttendeeVtbl;

interface IRDPSRAPIAttendee {
    CONST_VTBL IRDPSRAPIAttendeeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIAttendee_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIAttendee_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIAttendee_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIAttendee_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIAttendee_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIAttendee_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIAttendee_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIAttendee methods ***/
#define IRDPSRAPIAttendee_get_Id(This,pId) (This)->lpVtbl->get_Id(This,pId)
#define IRDPSRAPIAttendee_get_RemoteName(This,pVal) (This)->lpVtbl->get_RemoteName(This,pVal)
#define IRDPSRAPIAttendee_get_ControlLevel(This,pVal) (This)->lpVtbl->get_ControlLevel(This,pVal)
#define IRDPSRAPIAttendee_put_ControlLevel(This,pNewVal) (This)->lpVtbl->put_ControlLevel(This,pNewVal)
#define IRDPSRAPIAttendee_get_Invitation(This,ppVal) (This)->lpVtbl->get_Invitation(This,ppVal)
#define IRDPSRAPIAttendee_TerminateConnection(This) (This)->lpVtbl->TerminateConnection(This)
#define IRDPSRAPIAttendee_get_Flags(This,plFlags) (This)->lpVtbl->get_Flags(This,plFlags)
#define IRDPSRAPIAttendee_get_ConnectivityInfo(This,ppVal) (This)->lpVtbl->get_ConnectivityInfo(This,ppVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIAttendee_QueryInterface(IRDPSRAPIAttendee* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIAttendee_AddRef(IRDPSRAPIAttendee* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIAttendee_Release(IRDPSRAPIAttendee* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIAttendee_GetTypeInfoCount(IRDPSRAPIAttendee* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIAttendee_GetTypeInfo(IRDPSRAPIAttendee* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIAttendee_GetIDsOfNames(IRDPSRAPIAttendee* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIAttendee_Invoke(IRDPSRAPIAttendee* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIAttendee methods ***/
static inline HRESULT IRDPSRAPIAttendee_get_Id(IRDPSRAPIAttendee* This,LONG *pId) {
    return This->lpVtbl->get_Id(This,pId);
}
static inline HRESULT IRDPSRAPIAttendee_get_RemoteName(IRDPSRAPIAttendee* This,BSTR *pVal) {
    return This->lpVtbl->get_RemoteName(This,pVal);
}
static inline HRESULT IRDPSRAPIAttendee_get_ControlLevel(IRDPSRAPIAttendee* This,CTRL_LEVEL *pVal) {
    return This->lpVtbl->get_ControlLevel(This,pVal);
}
static inline HRESULT IRDPSRAPIAttendee_put_ControlLevel(IRDPSRAPIAttendee* This,CTRL_LEVEL pNewVal) {
    return This->lpVtbl->put_ControlLevel(This,pNewVal);
}
static inline HRESULT IRDPSRAPIAttendee_get_Invitation(IRDPSRAPIAttendee* This,IRDPSRAPIInvitation **ppVal) {
    return This->lpVtbl->get_Invitation(This,ppVal);
}
static inline HRESULT IRDPSRAPIAttendee_TerminateConnection(IRDPSRAPIAttendee* This) {
    return This->lpVtbl->TerminateConnection(This);
}
static inline HRESULT IRDPSRAPIAttendee_get_Flags(IRDPSRAPIAttendee* This,LONG *plFlags) {
    return This->lpVtbl->get_Flags(This,plFlags);
}
static inline HRESULT IRDPSRAPIAttendee_get_ConnectivityInfo(IRDPSRAPIAttendee* This,IUnknown **ppVal) {
    return This->lpVtbl->get_ConnectivityInfo(This,ppVal);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIAttendee_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIAttendeeManager interface
 */
#ifndef __IRDPSRAPIAttendeeManager_INTERFACE_DEFINED__
#define __IRDPSRAPIAttendeeManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIAttendeeManager, 0xba3a37e8, 0x33da, 0x4749, 0x8d,0xa0, 0x07,0xfa,0x34,0xda,0x79,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ba3a37e8-33da-4749-8da0-07fa34da7944")
IRDPSRAPIAttendeeManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG id,
        IRDPSRAPIAttendee **ppItem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIAttendeeManager, 0xba3a37e8, 0x33da, 0x4749, 0x8d,0xa0, 0x07,0xfa,0x34,0xda,0x79,0x44)
#endif
#else
typedef struct IRDPSRAPIAttendeeManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIAttendeeManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIAttendeeManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIAttendeeManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIAttendeeManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIAttendeeManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIAttendeeManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIAttendeeManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIAttendeeManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IRDPSRAPIAttendeeManager *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IRDPSRAPIAttendeeManager *This,
        LONG id,
        IRDPSRAPIAttendee **ppItem);

    END_INTERFACE
} IRDPSRAPIAttendeeManagerVtbl;

interface IRDPSRAPIAttendeeManager {
    CONST_VTBL IRDPSRAPIAttendeeManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIAttendeeManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIAttendeeManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIAttendeeManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIAttendeeManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIAttendeeManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIAttendeeManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIAttendeeManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIAttendeeManager methods ***/
#define IRDPSRAPIAttendeeManager_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IRDPSRAPIAttendeeManager_get_Item(This,id,ppItem) (This)->lpVtbl->get_Item(This,id,ppItem)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIAttendeeManager_QueryInterface(IRDPSRAPIAttendeeManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIAttendeeManager_AddRef(IRDPSRAPIAttendeeManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIAttendeeManager_Release(IRDPSRAPIAttendeeManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIAttendeeManager_GetTypeInfoCount(IRDPSRAPIAttendeeManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIAttendeeManager_GetTypeInfo(IRDPSRAPIAttendeeManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIAttendeeManager_GetIDsOfNames(IRDPSRAPIAttendeeManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIAttendeeManager_Invoke(IRDPSRAPIAttendeeManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIAttendeeManager methods ***/
static inline HRESULT IRDPSRAPIAttendeeManager_get__NewEnum(IRDPSRAPIAttendeeManager* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IRDPSRAPIAttendeeManager_get_Item(IRDPSRAPIAttendeeManager* This,LONG id,IRDPSRAPIAttendee **ppItem) {
    return This->lpVtbl->get_Item(This,id,ppItem);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIAttendeeManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIAttendeeDisconnectInfo interface
 */
#ifndef __IRDPSRAPIAttendeeDisconnectInfo_INTERFACE_DEFINED__
#define __IRDPSRAPIAttendeeDisconnectInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIAttendeeDisconnectInfo, 0xc187689f, 0x447c, 0x44a1, 0x9c,0x14, 0xff,0xfb,0xb3,0xb7,0xec,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c187689f-447c-44a1-9c14-fffbb3b7ec17")
IRDPSRAPIAttendeeDisconnectInfo : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Attendee(
        IRDPSRAPIAttendee **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Reason(
        ATTENDEE_DISCONNECT_REASON *pReason) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Code(
        LONG *pVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIAttendeeDisconnectInfo, 0xc187689f, 0x447c, 0x44a1, 0x9c,0x14, 0xff,0xfb,0xb3,0xb7,0xec,0x17)
#endif
#else
typedef struct IRDPSRAPIAttendeeDisconnectInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIAttendeeDisconnectInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIAttendeeDisconnectInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIAttendeeDisconnectInfo *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIAttendeeDisconnectInfo *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIAttendeeDisconnectInfo *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIAttendeeDisconnectInfo *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIAttendeeDisconnectInfo *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIAttendeeDisconnectInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Attendee)(
        IRDPSRAPIAttendeeDisconnectInfo *This,
        IRDPSRAPIAttendee **retval);

    HRESULT (STDMETHODCALLTYPE *get_Reason)(
        IRDPSRAPIAttendeeDisconnectInfo *This,
        ATTENDEE_DISCONNECT_REASON *pReason);

    HRESULT (STDMETHODCALLTYPE *get_Code)(
        IRDPSRAPIAttendeeDisconnectInfo *This,
        LONG *pVal);

    END_INTERFACE
} IRDPSRAPIAttendeeDisconnectInfoVtbl;

interface IRDPSRAPIAttendeeDisconnectInfo {
    CONST_VTBL IRDPSRAPIAttendeeDisconnectInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIAttendeeDisconnectInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIAttendeeDisconnectInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIAttendeeDisconnectInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIAttendeeDisconnectInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIAttendeeDisconnectInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIAttendeeDisconnectInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIAttendeeDisconnectInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIAttendeeDisconnectInfo methods ***/
#define IRDPSRAPIAttendeeDisconnectInfo_get_Attendee(This,retval) (This)->lpVtbl->get_Attendee(This,retval)
#define IRDPSRAPIAttendeeDisconnectInfo_get_Reason(This,pReason) (This)->lpVtbl->get_Reason(This,pReason)
#define IRDPSRAPIAttendeeDisconnectInfo_get_Code(This,pVal) (This)->lpVtbl->get_Code(This,pVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIAttendeeDisconnectInfo_QueryInterface(IRDPSRAPIAttendeeDisconnectInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIAttendeeDisconnectInfo_AddRef(IRDPSRAPIAttendeeDisconnectInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIAttendeeDisconnectInfo_Release(IRDPSRAPIAttendeeDisconnectInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIAttendeeDisconnectInfo_GetTypeInfoCount(IRDPSRAPIAttendeeDisconnectInfo* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIAttendeeDisconnectInfo_GetTypeInfo(IRDPSRAPIAttendeeDisconnectInfo* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIAttendeeDisconnectInfo_GetIDsOfNames(IRDPSRAPIAttendeeDisconnectInfo* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIAttendeeDisconnectInfo_Invoke(IRDPSRAPIAttendeeDisconnectInfo* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIAttendeeDisconnectInfo methods ***/
static inline HRESULT IRDPSRAPIAttendeeDisconnectInfo_get_Attendee(IRDPSRAPIAttendeeDisconnectInfo* This,IRDPSRAPIAttendee **retval) {
    return This->lpVtbl->get_Attendee(This,retval);
}
static inline HRESULT IRDPSRAPIAttendeeDisconnectInfo_get_Reason(IRDPSRAPIAttendeeDisconnectInfo* This,ATTENDEE_DISCONNECT_REASON *pReason) {
    return This->lpVtbl->get_Reason(This,pReason);
}
static inline HRESULT IRDPSRAPIAttendeeDisconnectInfo_get_Code(IRDPSRAPIAttendeeDisconnectInfo* This,LONG *pVal) {
    return This->lpVtbl->get_Code(This,pVal);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIAttendeeDisconnectInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIVirtualChannel interface
 */
#ifndef __IRDPSRAPIVirtualChannel_INTERFACE_DEFINED__
#define __IRDPSRAPIVirtualChannel_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIVirtualChannel, 0x05e12f95, 0x28b3, 0x4c9a, 0x87,0x80, 0xd0,0x24,0x85,0x74,0xa1,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("05e12f95-28b3-4c9a-8780-d0248574a1e0")
IRDPSRAPIVirtualChannel : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE SendData(
        BSTR bstrData,
        LONG lAttendeeId,
        ULONG ChannelSendFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAccess(
        LONG lAttendeeId,
        CHANNEL_ACCESS_ENUM AccessType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Flags(
        LONG *plFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Priority(
        CHANNEL_PRIORITY *pPriority) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIVirtualChannel, 0x05e12f95, 0x28b3, 0x4c9a, 0x87,0x80, 0xd0,0x24,0x85,0x74,0xa1,0xe0)
#endif
#else
typedef struct IRDPSRAPIVirtualChannelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIVirtualChannel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIVirtualChannel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIVirtualChannel *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIVirtualChannel *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIVirtualChannel *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIVirtualChannel *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIVirtualChannel *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIVirtualChannel methods ***/
    HRESULT (STDMETHODCALLTYPE *SendData)(
        IRDPSRAPIVirtualChannel *This,
        BSTR bstrData,
        LONG lAttendeeId,
        ULONG ChannelSendFlags);

    HRESULT (STDMETHODCALLTYPE *SetAccess)(
        IRDPSRAPIVirtualChannel *This,
        LONG lAttendeeId,
        CHANNEL_ACCESS_ENUM AccessType);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IRDPSRAPIVirtualChannel *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *get_Flags)(
        IRDPSRAPIVirtualChannel *This,
        LONG *plFlags);

    HRESULT (STDMETHODCALLTYPE *get_Priority)(
        IRDPSRAPIVirtualChannel *This,
        CHANNEL_PRIORITY *pPriority);

    END_INTERFACE
} IRDPSRAPIVirtualChannelVtbl;

interface IRDPSRAPIVirtualChannel {
    CONST_VTBL IRDPSRAPIVirtualChannelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIVirtualChannel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIVirtualChannel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIVirtualChannel_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIVirtualChannel_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIVirtualChannel_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIVirtualChannel_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIVirtualChannel_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIVirtualChannel methods ***/
#define IRDPSRAPIVirtualChannel_SendData(This,bstrData,lAttendeeId,ChannelSendFlags) (This)->lpVtbl->SendData(This,bstrData,lAttendeeId,ChannelSendFlags)
#define IRDPSRAPIVirtualChannel_SetAccess(This,lAttendeeId,AccessType) (This)->lpVtbl->SetAccess(This,lAttendeeId,AccessType)
#define IRDPSRAPIVirtualChannel_get_Name(This,pbstrName) (This)->lpVtbl->get_Name(This,pbstrName)
#define IRDPSRAPIVirtualChannel_get_Flags(This,plFlags) (This)->lpVtbl->get_Flags(This,plFlags)
#define IRDPSRAPIVirtualChannel_get_Priority(This,pPriority) (This)->lpVtbl->get_Priority(This,pPriority)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIVirtualChannel_QueryInterface(IRDPSRAPIVirtualChannel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIVirtualChannel_AddRef(IRDPSRAPIVirtualChannel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIVirtualChannel_Release(IRDPSRAPIVirtualChannel* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIVirtualChannel_GetTypeInfoCount(IRDPSRAPIVirtualChannel* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIVirtualChannel_GetTypeInfo(IRDPSRAPIVirtualChannel* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIVirtualChannel_GetIDsOfNames(IRDPSRAPIVirtualChannel* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIVirtualChannel_Invoke(IRDPSRAPIVirtualChannel* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIVirtualChannel methods ***/
static inline HRESULT IRDPSRAPIVirtualChannel_SendData(IRDPSRAPIVirtualChannel* This,BSTR bstrData,LONG lAttendeeId,ULONG ChannelSendFlags) {
    return This->lpVtbl->SendData(This,bstrData,lAttendeeId,ChannelSendFlags);
}
static inline HRESULT IRDPSRAPIVirtualChannel_SetAccess(IRDPSRAPIVirtualChannel* This,LONG lAttendeeId,CHANNEL_ACCESS_ENUM AccessType) {
    return This->lpVtbl->SetAccess(This,lAttendeeId,AccessType);
}
static inline HRESULT IRDPSRAPIVirtualChannel_get_Name(IRDPSRAPIVirtualChannel* This,BSTR *pbstrName) {
    return This->lpVtbl->get_Name(This,pbstrName);
}
static inline HRESULT IRDPSRAPIVirtualChannel_get_Flags(IRDPSRAPIVirtualChannel* This,LONG *plFlags) {
    return This->lpVtbl->get_Flags(This,plFlags);
}
static inline HRESULT IRDPSRAPIVirtualChannel_get_Priority(IRDPSRAPIVirtualChannel* This,CHANNEL_PRIORITY *pPriority) {
    return This->lpVtbl->get_Priority(This,pPriority);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIVirtualChannel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIVirtualChannelManager interface
 */
#ifndef __IRDPSRAPIVirtualChannelManager_INTERFACE_DEFINED__
#define __IRDPSRAPIVirtualChannelManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIVirtualChannelManager, 0x0d11c661, 0x5d0d, 0x4ee4, 0x89,0xdf, 0x21,0x66,0xae,0x1f,0xdf,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0d11c661-5d0d-4ee4-89df-2166ae1fdfed")
IRDPSRAPIVirtualChannelManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        VARIANT item,
        IRDPSRAPIVirtualChannel **pChannel) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVirtualChannel(
        BSTR bstrChannelName,
        CHANNEL_PRIORITY Priority,
        ULONG ChannelFlags,
        IRDPSRAPIVirtualChannel **ppChannel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIVirtualChannelManager, 0x0d11c661, 0x5d0d, 0x4ee4, 0x89,0xdf, 0x21,0x66,0xae,0x1f,0xdf,0xed)
#endif
#else
typedef struct IRDPSRAPIVirtualChannelManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIVirtualChannelManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIVirtualChannelManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIVirtualChannelManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIVirtualChannelManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIVirtualChannelManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIVirtualChannelManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIVirtualChannelManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIVirtualChannelManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IRDPSRAPIVirtualChannelManager *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IRDPSRAPIVirtualChannelManager *This,
        VARIANT item,
        IRDPSRAPIVirtualChannel **pChannel);

    HRESULT (STDMETHODCALLTYPE *CreateVirtualChannel)(
        IRDPSRAPIVirtualChannelManager *This,
        BSTR bstrChannelName,
        CHANNEL_PRIORITY Priority,
        ULONG ChannelFlags,
        IRDPSRAPIVirtualChannel **ppChannel);

    END_INTERFACE
} IRDPSRAPIVirtualChannelManagerVtbl;

interface IRDPSRAPIVirtualChannelManager {
    CONST_VTBL IRDPSRAPIVirtualChannelManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIVirtualChannelManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIVirtualChannelManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIVirtualChannelManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIVirtualChannelManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIVirtualChannelManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIVirtualChannelManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIVirtualChannelManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIVirtualChannelManager methods ***/
#define IRDPSRAPIVirtualChannelManager_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IRDPSRAPIVirtualChannelManager_get_Item(This,item,pChannel) (This)->lpVtbl->get_Item(This,item,pChannel)
#define IRDPSRAPIVirtualChannelManager_CreateVirtualChannel(This,bstrChannelName,Priority,ChannelFlags,ppChannel) (This)->lpVtbl->CreateVirtualChannel(This,bstrChannelName,Priority,ChannelFlags,ppChannel)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIVirtualChannelManager_QueryInterface(IRDPSRAPIVirtualChannelManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIVirtualChannelManager_AddRef(IRDPSRAPIVirtualChannelManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIVirtualChannelManager_Release(IRDPSRAPIVirtualChannelManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIVirtualChannelManager_GetTypeInfoCount(IRDPSRAPIVirtualChannelManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIVirtualChannelManager_GetTypeInfo(IRDPSRAPIVirtualChannelManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIVirtualChannelManager_GetIDsOfNames(IRDPSRAPIVirtualChannelManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIVirtualChannelManager_Invoke(IRDPSRAPIVirtualChannelManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIVirtualChannelManager methods ***/
static inline HRESULT IRDPSRAPIVirtualChannelManager_get__NewEnum(IRDPSRAPIVirtualChannelManager* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IRDPSRAPIVirtualChannelManager_get_Item(IRDPSRAPIVirtualChannelManager* This,VARIANT item,IRDPSRAPIVirtualChannel **pChannel) {
    return This->lpVtbl->get_Item(This,item,pChannel);
}
static inline HRESULT IRDPSRAPIVirtualChannelManager_CreateVirtualChannel(IRDPSRAPIVirtualChannelManager* This,BSTR bstrChannelName,CHANNEL_PRIORITY Priority,ULONG ChannelFlags,IRDPSRAPIVirtualChannel **ppChannel) {
    return This->lpVtbl->CreateVirtualChannel(This,bstrChannelName,Priority,ChannelFlags,ppChannel);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIVirtualChannelManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIViewer interface
 */
#ifndef __IRDPSRAPIViewer_INTERFACE_DEFINED__
#define __IRDPSRAPIViewer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIViewer, 0xc6bfcd38, 0x8ce9, 0x404d, 0x8a,0xe8, 0xf3,0x1d,0x00,0xc6,0x5c,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c6bfcd38-8ce9-404d-8ae8-f31d00c65cb5")
IRDPSRAPIViewer : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Connect(
        BSTR bstrConnectionString,
        BSTR bstrName,
        BSTR bstrPassword) = 0;

    virtual HRESULT STDMETHODCALLTYPE Disconnect(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Attendees(
        IRDPSRAPIAttendeeManager **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Invitations(
        IRDPSRAPIInvitationManager **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ApplicationFilter(
        IRDPSRAPIApplicationFilter **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_VirtualChannelManager(
        IRDPSRAPIVirtualChannelManager **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SmartSizing(
        VARIANT_BOOL vbSmartSizing) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SmartSizing(
        VARIANT_BOOL *pvbSmartSizing) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestControl(
        CTRL_LEVEL CtrlLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DisconnectedText(
        BSTR bstrDisconnectedText) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DisconnectedText(
        BSTR *pbstrDisconnectedText) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestColorDepthChange(
        LONG Bpp) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Properties(
        IRDPSRAPISessionProperties **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartReverseConnectListener(
        BSTR bstrConnectionString,
        BSTR bstrUserName,
        BSTR bstrPassword,
        BSTR *pbstrReverseConnectString) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIViewer, 0xc6bfcd38, 0x8ce9, 0x404d, 0x8a,0xe8, 0xf3,0x1d,0x00,0xc6,0x5c,0xb5)
#endif
#else
typedef struct IRDPSRAPIViewerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIViewer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIViewer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIViewer *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIViewer *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIViewer *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIViewer *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIViewer *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIViewer methods ***/
    HRESULT (STDMETHODCALLTYPE *Connect)(
        IRDPSRAPIViewer *This,
        BSTR bstrConnectionString,
        BSTR bstrName,
        BSTR bstrPassword);

    HRESULT (STDMETHODCALLTYPE *Disconnect)(
        IRDPSRAPIViewer *This);

    HRESULT (STDMETHODCALLTYPE *get_Attendees)(
        IRDPSRAPIViewer *This,
        IRDPSRAPIAttendeeManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_Invitations)(
        IRDPSRAPIViewer *This,
        IRDPSRAPIInvitationManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_ApplicationFilter)(
        IRDPSRAPIViewer *This,
        IRDPSRAPIApplicationFilter **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_VirtualChannelManager)(
        IRDPSRAPIViewer *This,
        IRDPSRAPIVirtualChannelManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *put_SmartSizing)(
        IRDPSRAPIViewer *This,
        VARIANT_BOOL vbSmartSizing);

    HRESULT (STDMETHODCALLTYPE *get_SmartSizing)(
        IRDPSRAPIViewer *This,
        VARIANT_BOOL *pvbSmartSizing);

    HRESULT (STDMETHODCALLTYPE *RequestControl)(
        IRDPSRAPIViewer *This,
        CTRL_LEVEL CtrlLevel);

    HRESULT (STDMETHODCALLTYPE *put_DisconnectedText)(
        IRDPSRAPIViewer *This,
        BSTR bstrDisconnectedText);

    HRESULT (STDMETHODCALLTYPE *get_DisconnectedText)(
        IRDPSRAPIViewer *This,
        BSTR *pbstrDisconnectedText);

    HRESULT (STDMETHODCALLTYPE *RequestColorDepthChange)(
        IRDPSRAPIViewer *This,
        LONG Bpp);

    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        IRDPSRAPIViewer *This,
        IRDPSRAPISessionProperties **ppVal);

    HRESULT (STDMETHODCALLTYPE *StartReverseConnectListener)(
        IRDPSRAPIViewer *This,
        BSTR bstrConnectionString,
        BSTR bstrUserName,
        BSTR bstrPassword,
        BSTR *pbstrReverseConnectString);

    END_INTERFACE
} IRDPSRAPIViewerVtbl;

interface IRDPSRAPIViewer {
    CONST_VTBL IRDPSRAPIViewerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIViewer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIViewer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIViewer_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIViewer_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIViewer_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIViewer_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIViewer_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIViewer methods ***/
#define IRDPSRAPIViewer_Connect(This,bstrConnectionString,bstrName,bstrPassword) (This)->lpVtbl->Connect(This,bstrConnectionString,bstrName,bstrPassword)
#define IRDPSRAPIViewer_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#define IRDPSRAPIViewer_get_Attendees(This,ppVal) (This)->lpVtbl->get_Attendees(This,ppVal)
#define IRDPSRAPIViewer_get_Invitations(This,ppVal) (This)->lpVtbl->get_Invitations(This,ppVal)
#define IRDPSRAPIViewer_get_ApplicationFilter(This,ppVal) (This)->lpVtbl->get_ApplicationFilter(This,ppVal)
#define IRDPSRAPIViewer_get_VirtualChannelManager(This,ppVal) (This)->lpVtbl->get_VirtualChannelManager(This,ppVal)
#define IRDPSRAPIViewer_put_SmartSizing(This,vbSmartSizing) (This)->lpVtbl->put_SmartSizing(This,vbSmartSizing)
#define IRDPSRAPIViewer_get_SmartSizing(This,pvbSmartSizing) (This)->lpVtbl->get_SmartSizing(This,pvbSmartSizing)
#define IRDPSRAPIViewer_RequestControl(This,CtrlLevel) (This)->lpVtbl->RequestControl(This,CtrlLevel)
#define IRDPSRAPIViewer_put_DisconnectedText(This,bstrDisconnectedText) (This)->lpVtbl->put_DisconnectedText(This,bstrDisconnectedText)
#define IRDPSRAPIViewer_get_DisconnectedText(This,pbstrDisconnectedText) (This)->lpVtbl->get_DisconnectedText(This,pbstrDisconnectedText)
#define IRDPSRAPIViewer_RequestColorDepthChange(This,Bpp) (This)->lpVtbl->RequestColorDepthChange(This,Bpp)
#define IRDPSRAPIViewer_get_Properties(This,ppVal) (This)->lpVtbl->get_Properties(This,ppVal)
#define IRDPSRAPIViewer_StartReverseConnectListener(This,bstrConnectionString,bstrUserName,bstrPassword,pbstrReverseConnectString) (This)->lpVtbl->StartReverseConnectListener(This,bstrConnectionString,bstrUserName,bstrPassword,pbstrReverseConnectString)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIViewer_QueryInterface(IRDPSRAPIViewer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIViewer_AddRef(IRDPSRAPIViewer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIViewer_Release(IRDPSRAPIViewer* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIViewer_GetTypeInfoCount(IRDPSRAPIViewer* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIViewer_GetTypeInfo(IRDPSRAPIViewer* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIViewer_GetIDsOfNames(IRDPSRAPIViewer* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIViewer_Invoke(IRDPSRAPIViewer* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIViewer methods ***/
static inline HRESULT IRDPSRAPIViewer_Connect(IRDPSRAPIViewer* This,BSTR bstrConnectionString,BSTR bstrName,BSTR bstrPassword) {
    return This->lpVtbl->Connect(This,bstrConnectionString,bstrName,bstrPassword);
}
static inline HRESULT IRDPSRAPIViewer_Disconnect(IRDPSRAPIViewer* This) {
    return This->lpVtbl->Disconnect(This);
}
static inline HRESULT IRDPSRAPIViewer_get_Attendees(IRDPSRAPIViewer* This,IRDPSRAPIAttendeeManager **ppVal) {
    return This->lpVtbl->get_Attendees(This,ppVal);
}
static inline HRESULT IRDPSRAPIViewer_get_Invitations(IRDPSRAPIViewer* This,IRDPSRAPIInvitationManager **ppVal) {
    return This->lpVtbl->get_Invitations(This,ppVal);
}
static inline HRESULT IRDPSRAPIViewer_get_ApplicationFilter(IRDPSRAPIViewer* This,IRDPSRAPIApplicationFilter **ppVal) {
    return This->lpVtbl->get_ApplicationFilter(This,ppVal);
}
static inline HRESULT IRDPSRAPIViewer_get_VirtualChannelManager(IRDPSRAPIViewer* This,IRDPSRAPIVirtualChannelManager **ppVal) {
    return This->lpVtbl->get_VirtualChannelManager(This,ppVal);
}
static inline HRESULT IRDPSRAPIViewer_put_SmartSizing(IRDPSRAPIViewer* This,VARIANT_BOOL vbSmartSizing) {
    return This->lpVtbl->put_SmartSizing(This,vbSmartSizing);
}
static inline HRESULT IRDPSRAPIViewer_get_SmartSizing(IRDPSRAPIViewer* This,VARIANT_BOOL *pvbSmartSizing) {
    return This->lpVtbl->get_SmartSizing(This,pvbSmartSizing);
}
static inline HRESULT IRDPSRAPIViewer_RequestControl(IRDPSRAPIViewer* This,CTRL_LEVEL CtrlLevel) {
    return This->lpVtbl->RequestControl(This,CtrlLevel);
}
static inline HRESULT IRDPSRAPIViewer_put_DisconnectedText(IRDPSRAPIViewer* This,BSTR bstrDisconnectedText) {
    return This->lpVtbl->put_DisconnectedText(This,bstrDisconnectedText);
}
static inline HRESULT IRDPSRAPIViewer_get_DisconnectedText(IRDPSRAPIViewer* This,BSTR *pbstrDisconnectedText) {
    return This->lpVtbl->get_DisconnectedText(This,pbstrDisconnectedText);
}
static inline HRESULT IRDPSRAPIViewer_RequestColorDepthChange(IRDPSRAPIViewer* This,LONG Bpp) {
    return This->lpVtbl->RequestColorDepthChange(This,Bpp);
}
static inline HRESULT IRDPSRAPIViewer_get_Properties(IRDPSRAPIViewer* This,IRDPSRAPISessionProperties **ppVal) {
    return This->lpVtbl->get_Properties(This,ppVal);
}
static inline HRESULT IRDPSRAPIViewer_StartReverseConnectListener(IRDPSRAPIViewer* This,BSTR bstrConnectionString,BSTR bstrUserName,BSTR bstrPassword,BSTR *pbstrReverseConnectString) {
    return This->lpVtbl->StartReverseConnectListener(This,bstrConnectionString,bstrUserName,bstrPassword,pbstrReverseConnectString);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIViewer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPViewerRenderingSurface interface
 */
#ifndef __IRDPViewerRenderingSurface_INTERFACE_DEFINED__
#define __IRDPViewerRenderingSurface_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPViewerRenderingSurface, 0x56bfce32, 0x83e9, 0x414d, 0x82,0xe8, 0xf3,0x1d,0x01,0xc6,0x2c,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56bfce32-83e9-414d-82e8-f31d01c62cb5")
IRDPViewerRenderingSurface : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetRenderingSurface(
        IUnknown *pRenderingSurface,
        LONG surfaceWidth,
        LONG surfaceHeight) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPViewerRenderingSurface, 0x56bfce32, 0x83e9, 0x414d, 0x82,0xe8, 0xf3,0x1d,0x01,0xc6,0x2c,0xb5)
#endif
#else
typedef struct IRDPViewerRenderingSurfaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPViewerRenderingSurface *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPViewerRenderingSurface *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPViewerRenderingSurface *This);

    /*** IRDPViewerRenderingSurface methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRenderingSurface)(
        IRDPViewerRenderingSurface *This,
        IUnknown *pRenderingSurface,
        LONG surfaceWidth,
        LONG surfaceHeight);

    END_INTERFACE
} IRDPViewerRenderingSurfaceVtbl;

interface IRDPViewerRenderingSurface {
    CONST_VTBL IRDPViewerRenderingSurfaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPViewerRenderingSurface_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPViewerRenderingSurface_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPViewerRenderingSurface_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPViewerRenderingSurface methods ***/
#define IRDPViewerRenderingSurface_SetRenderingSurface(This,pRenderingSurface,surfaceWidth,surfaceHeight) (This)->lpVtbl->SetRenderingSurface(This,pRenderingSurface,surfaceWidth,surfaceHeight)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPViewerRenderingSurface_QueryInterface(IRDPViewerRenderingSurface* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPViewerRenderingSurface_AddRef(IRDPViewerRenderingSurface* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPViewerRenderingSurface_Release(IRDPViewerRenderingSurface* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPViewerRenderingSurface methods ***/
static inline HRESULT IRDPViewerRenderingSurface_SetRenderingSurface(IRDPViewerRenderingSurface* This,IUnknown *pRenderingSurface,LONG surfaceWidth,LONG surfaceHeight) {
    return This->lpVtbl->SetRenderingSurface(This,pRenderingSurface,surfaceWidth,surfaceHeight);
}
#endif
#endif

#endif


#endif  /* __IRDPViewerRenderingSurface_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPViewerInputSink interface
 */
#ifndef __IRDPViewerInputSink_INTERFACE_DEFINED__
#define __IRDPViewerInputSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPViewerInputSink, 0xbb590853, 0xa6c5, 0x4a7b, 0x8d,0xd4, 0x76,0xb6,0x9e,0xea,0x12,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb590853-a6c5-4a7b-8dd4-76b69eea12d5")
IRDPViewerInputSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SendMouseButtonEvent(
        RDPSRAPI_MOUSE_BUTTON_TYPE buttonType,
        VARIANT_BOOL vbButtonDown,
        ULONG xPos,
        ULONG yPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendMouseMoveEvent(
        ULONG xPos,
        ULONG yPos) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendMouseWheelEvent(
        UINT16 wheelRotation) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendKeyboardEvent(
        RDPSRAPI_KBD_CODE_TYPE codeType,
        UINT16 keycode,
        VARIANT_BOOL vbKeyUp,
        VARIANT_BOOL vbRepeat,
        VARIANT_BOOL vbExtended) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendSyncEvent(
        ULONG syncFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginTouchFrame(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTouchInput(
        UINT32 contactId,
        UINT32 evnt,
        INT32 x,
        INT32 y) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndTouchFrame(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPViewerInputSink, 0xbb590853, 0xa6c5, 0x4a7b, 0x8d,0xd4, 0x76,0xb6,0x9e,0xea,0x12,0xd5)
#endif
#else
typedef struct IRDPViewerInputSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPViewerInputSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPViewerInputSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPViewerInputSink *This);

    /*** IRDPViewerInputSink methods ***/
    HRESULT (STDMETHODCALLTYPE *SendMouseButtonEvent)(
        IRDPViewerInputSink *This,
        RDPSRAPI_MOUSE_BUTTON_TYPE buttonType,
        VARIANT_BOOL vbButtonDown,
        ULONG xPos,
        ULONG yPos);

    HRESULT (STDMETHODCALLTYPE *SendMouseMoveEvent)(
        IRDPViewerInputSink *This,
        ULONG xPos,
        ULONG yPos);

    HRESULT (STDMETHODCALLTYPE *SendMouseWheelEvent)(
        IRDPViewerInputSink *This,
        UINT16 wheelRotation);

    HRESULT (STDMETHODCALLTYPE *SendKeyboardEvent)(
        IRDPViewerInputSink *This,
        RDPSRAPI_KBD_CODE_TYPE codeType,
        UINT16 keycode,
        VARIANT_BOOL vbKeyUp,
        VARIANT_BOOL vbRepeat,
        VARIANT_BOOL vbExtended);

    HRESULT (STDMETHODCALLTYPE *SendSyncEvent)(
        IRDPViewerInputSink *This,
        ULONG syncFlags);

    HRESULT (STDMETHODCALLTYPE *BeginTouchFrame)(
        IRDPViewerInputSink *This);

    HRESULT (STDMETHODCALLTYPE *AddTouchInput)(
        IRDPViewerInputSink *This,
        UINT32 contactId,
        UINT32 evnt,
        INT32 x,
        INT32 y);

    HRESULT (STDMETHODCALLTYPE *EndTouchFrame)(
        IRDPViewerInputSink *This);

    END_INTERFACE
} IRDPViewerInputSinkVtbl;

interface IRDPViewerInputSink {
    CONST_VTBL IRDPViewerInputSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPViewerInputSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPViewerInputSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPViewerInputSink_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPViewerInputSink methods ***/
#define IRDPViewerInputSink_SendMouseButtonEvent(This,buttonType,vbButtonDown,xPos,yPos) (This)->lpVtbl->SendMouseButtonEvent(This,buttonType,vbButtonDown,xPos,yPos)
#define IRDPViewerInputSink_SendMouseMoveEvent(This,xPos,yPos) (This)->lpVtbl->SendMouseMoveEvent(This,xPos,yPos)
#define IRDPViewerInputSink_SendMouseWheelEvent(This,wheelRotation) (This)->lpVtbl->SendMouseWheelEvent(This,wheelRotation)
#define IRDPViewerInputSink_SendKeyboardEvent(This,codeType,keycode,vbKeyUp,vbRepeat,vbExtended) (This)->lpVtbl->SendKeyboardEvent(This,codeType,keycode,vbKeyUp,vbRepeat,vbExtended)
#define IRDPViewerInputSink_SendSyncEvent(This,syncFlags) (This)->lpVtbl->SendSyncEvent(This,syncFlags)
#define IRDPViewerInputSink_BeginTouchFrame(This) (This)->lpVtbl->BeginTouchFrame(This)
#define IRDPViewerInputSink_AddTouchInput(This,contactId,evnt,x,y) (This)->lpVtbl->AddTouchInput(This,contactId,evnt,x,y)
#define IRDPViewerInputSink_EndTouchFrame(This) (This)->lpVtbl->EndTouchFrame(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPViewerInputSink_QueryInterface(IRDPViewerInputSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPViewerInputSink_AddRef(IRDPViewerInputSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPViewerInputSink_Release(IRDPViewerInputSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPViewerInputSink methods ***/
static inline HRESULT IRDPViewerInputSink_SendMouseButtonEvent(IRDPViewerInputSink* This,RDPSRAPI_MOUSE_BUTTON_TYPE buttonType,VARIANT_BOOL vbButtonDown,ULONG xPos,ULONG yPos) {
    return This->lpVtbl->SendMouseButtonEvent(This,buttonType,vbButtonDown,xPos,yPos);
}
static inline HRESULT IRDPViewerInputSink_SendMouseMoveEvent(IRDPViewerInputSink* This,ULONG xPos,ULONG yPos) {
    return This->lpVtbl->SendMouseMoveEvent(This,xPos,yPos);
}
static inline HRESULT IRDPViewerInputSink_SendMouseWheelEvent(IRDPViewerInputSink* This,UINT16 wheelRotation) {
    return This->lpVtbl->SendMouseWheelEvent(This,wheelRotation);
}
static inline HRESULT IRDPViewerInputSink_SendKeyboardEvent(IRDPViewerInputSink* This,RDPSRAPI_KBD_CODE_TYPE codeType,UINT16 keycode,VARIANT_BOOL vbKeyUp,VARIANT_BOOL vbRepeat,VARIANT_BOOL vbExtended) {
    return This->lpVtbl->SendKeyboardEvent(This,codeType,keycode,vbKeyUp,vbRepeat,vbExtended);
}
static inline HRESULT IRDPViewerInputSink_SendSyncEvent(IRDPViewerInputSink* This,ULONG syncFlags) {
    return This->lpVtbl->SendSyncEvent(This,syncFlags);
}
static inline HRESULT IRDPViewerInputSink_BeginTouchFrame(IRDPViewerInputSink* This) {
    return This->lpVtbl->BeginTouchFrame(This);
}
static inline HRESULT IRDPViewerInputSink_AddTouchInput(IRDPViewerInputSink* This,UINT32 contactId,UINT32 evnt,INT32 x,INT32 y) {
    return This->lpVtbl->AddTouchInput(This,contactId,evnt,x,y);
}
static inline HRESULT IRDPViewerInputSink_EndTouchFrame(IRDPViewerInputSink* This) {
    return This->lpVtbl->EndTouchFrame(This);
}
#endif
#endif

#endif


#endif  /* __IRDPViewerInputSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPIFrameBuffer interface
 */
#ifndef __IRDPSRAPIFrameBuffer_INTERFACE_DEFINED__
#define __IRDPSRAPIFrameBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPIFrameBuffer, 0x3d67e7d2, 0xb27b, 0x448e, 0x81,0xb3, 0xc6,0x11,0x0e,0xd8,0xb4,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3d67e7d2-b27b-448e-81b3-c6110ed8b4be")
IRDPSRAPIFrameBuffer : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Width(
        LONG *plWidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Height(
        LONG *plHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Bpp(
        LONG *plBpp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFrameBufferBits(
        LONG x,
        LONG y,
        LONG Width,
        LONG Heigth,
        SAFEARRAY **ppBits) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPIFrameBuffer, 0x3d67e7d2, 0xb27b, 0x448e, 0x81,0xb3, 0xc6,0x11,0x0e,0xd8,0xb4,0xbe)
#endif
#else
typedef struct IRDPSRAPIFrameBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPIFrameBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPIFrameBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPIFrameBuffer *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPIFrameBuffer *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPIFrameBuffer *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPIFrameBuffer *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPIFrameBuffer *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPIFrameBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Width)(
        IRDPSRAPIFrameBuffer *This,
        LONG *plWidth);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        IRDPSRAPIFrameBuffer *This,
        LONG *plHeight);

    HRESULT (STDMETHODCALLTYPE *get_Bpp)(
        IRDPSRAPIFrameBuffer *This,
        LONG *plBpp);

    HRESULT (STDMETHODCALLTYPE *GetFrameBufferBits)(
        IRDPSRAPIFrameBuffer *This,
        LONG x,
        LONG y,
        LONG Width,
        LONG Heigth,
        SAFEARRAY **ppBits);

    END_INTERFACE
} IRDPSRAPIFrameBufferVtbl;

interface IRDPSRAPIFrameBuffer {
    CONST_VTBL IRDPSRAPIFrameBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPIFrameBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPIFrameBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPIFrameBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPIFrameBuffer_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPIFrameBuffer_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPIFrameBuffer_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPIFrameBuffer_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPIFrameBuffer methods ***/
#define IRDPSRAPIFrameBuffer_get_Width(This,plWidth) (This)->lpVtbl->get_Width(This,plWidth)
#define IRDPSRAPIFrameBuffer_get_Height(This,plHeight) (This)->lpVtbl->get_Height(This,plHeight)
#define IRDPSRAPIFrameBuffer_get_Bpp(This,plBpp) (This)->lpVtbl->get_Bpp(This,plBpp)
#define IRDPSRAPIFrameBuffer_GetFrameBufferBits(This,x,y,Width,Heigth,ppBits) (This)->lpVtbl->GetFrameBufferBits(This,x,y,Width,Heigth,ppBits)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPIFrameBuffer_QueryInterface(IRDPSRAPIFrameBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPIFrameBuffer_AddRef(IRDPSRAPIFrameBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPIFrameBuffer_Release(IRDPSRAPIFrameBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPIFrameBuffer_GetTypeInfoCount(IRDPSRAPIFrameBuffer* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPIFrameBuffer_GetTypeInfo(IRDPSRAPIFrameBuffer* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPIFrameBuffer_GetIDsOfNames(IRDPSRAPIFrameBuffer* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPIFrameBuffer_Invoke(IRDPSRAPIFrameBuffer* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPIFrameBuffer methods ***/
static inline HRESULT IRDPSRAPIFrameBuffer_get_Width(IRDPSRAPIFrameBuffer* This,LONG *plWidth) {
    return This->lpVtbl->get_Width(This,plWidth);
}
static inline HRESULT IRDPSRAPIFrameBuffer_get_Height(IRDPSRAPIFrameBuffer* This,LONG *plHeight) {
    return This->lpVtbl->get_Height(This,plHeight);
}
static inline HRESULT IRDPSRAPIFrameBuffer_get_Bpp(IRDPSRAPIFrameBuffer* This,LONG *plBpp) {
    return This->lpVtbl->get_Bpp(This,plBpp);
}
static inline HRESULT IRDPSRAPIFrameBuffer_GetFrameBufferBits(IRDPSRAPIFrameBuffer* This,LONG x,LONG y,LONG Width,LONG Heigth,SAFEARRAY **ppBits) {
    return This->lpVtbl->GetFrameBufferBits(This,x,y,Width,Heigth,ppBits);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPIFrameBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPITransportStreamBuffer interface
 */
#ifndef __IRDPSRAPITransportStreamBuffer_INTERFACE_DEFINED__
#define __IRDPSRAPITransportStreamBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPITransportStreamBuffer, 0x81c80290, 0x5085, 0x44b0, 0xb4,0x60, 0xf8,0x65,0xc3,0x9c,0xb4,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("81c80290-5085-44b0-b460-f865c39cb4a9")
IRDPSRAPITransportStreamBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Storage(
        BYTE **ppbStorage) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StorageSize(
        LONG *plMaxStore) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PayloadSize(
        LONG *plRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PayloadSize(
        LONG lVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PayloadOffset(
        LONG *plRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PayloadOffset(
        LONG lRetVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Flags(
        LONG *plFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Flags(
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Context(
        IUnknown **ppContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Context(
        IUnknown *pContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPITransportStreamBuffer, 0x81c80290, 0x5085, 0x44b0, 0xb4,0x60, 0xf8,0x65,0xc3,0x9c,0xb4,0xa9)
#endif
#else
typedef struct IRDPSRAPITransportStreamBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPITransportStreamBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPITransportStreamBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPITransportStreamBuffer *This);

    /*** IRDPSRAPITransportStreamBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Storage)(
        IRDPSRAPITransportStreamBuffer *This,
        BYTE **ppbStorage);

    HRESULT (STDMETHODCALLTYPE *get_StorageSize)(
        IRDPSRAPITransportStreamBuffer *This,
        LONG *plMaxStore);

    HRESULT (STDMETHODCALLTYPE *get_PayloadSize)(
        IRDPSRAPITransportStreamBuffer *This,
        LONG *plRetVal);

    HRESULT (STDMETHODCALLTYPE *put_PayloadSize)(
        IRDPSRAPITransportStreamBuffer *This,
        LONG lVal);

    HRESULT (STDMETHODCALLTYPE *get_PayloadOffset)(
        IRDPSRAPITransportStreamBuffer *This,
        LONG *plRetVal);

    HRESULT (STDMETHODCALLTYPE *put_PayloadOffset)(
        IRDPSRAPITransportStreamBuffer *This,
        LONG lRetVal);

    HRESULT (STDMETHODCALLTYPE *get_Flags)(
        IRDPSRAPITransportStreamBuffer *This,
        LONG *plFlags);

    HRESULT (STDMETHODCALLTYPE *put_Flags)(
        IRDPSRAPITransportStreamBuffer *This,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *get_Context)(
        IRDPSRAPITransportStreamBuffer *This,
        IUnknown **ppContext);

    HRESULT (STDMETHODCALLTYPE *put_Context)(
        IRDPSRAPITransportStreamBuffer *This,
        IUnknown *pContext);

    END_INTERFACE
} IRDPSRAPITransportStreamBufferVtbl;

interface IRDPSRAPITransportStreamBuffer {
    CONST_VTBL IRDPSRAPITransportStreamBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPITransportStreamBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPITransportStreamBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPITransportStreamBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPSRAPITransportStreamBuffer methods ***/
#define IRDPSRAPITransportStreamBuffer_get_Storage(This,ppbStorage) (This)->lpVtbl->get_Storage(This,ppbStorage)
#define IRDPSRAPITransportStreamBuffer_get_StorageSize(This,plMaxStore) (This)->lpVtbl->get_StorageSize(This,plMaxStore)
#define IRDPSRAPITransportStreamBuffer_get_PayloadSize(This,plRetVal) (This)->lpVtbl->get_PayloadSize(This,plRetVal)
#define IRDPSRAPITransportStreamBuffer_put_PayloadSize(This,lVal) (This)->lpVtbl->put_PayloadSize(This,lVal)
#define IRDPSRAPITransportStreamBuffer_get_PayloadOffset(This,plRetVal) (This)->lpVtbl->get_PayloadOffset(This,plRetVal)
#define IRDPSRAPITransportStreamBuffer_put_PayloadOffset(This,lRetVal) (This)->lpVtbl->put_PayloadOffset(This,lRetVal)
#define IRDPSRAPITransportStreamBuffer_get_Flags(This,plFlags) (This)->lpVtbl->get_Flags(This,plFlags)
#define IRDPSRAPITransportStreamBuffer_put_Flags(This,lFlags) (This)->lpVtbl->put_Flags(This,lFlags)
#define IRDPSRAPITransportStreamBuffer_get_Context(This,ppContext) (This)->lpVtbl->get_Context(This,ppContext)
#define IRDPSRAPITransportStreamBuffer_put_Context(This,pContext) (This)->lpVtbl->put_Context(This,pContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPITransportStreamBuffer_QueryInterface(IRDPSRAPITransportStreamBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPITransportStreamBuffer_AddRef(IRDPSRAPITransportStreamBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPITransportStreamBuffer_Release(IRDPSRAPITransportStreamBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPSRAPITransportStreamBuffer methods ***/
static inline HRESULT IRDPSRAPITransportStreamBuffer_get_Storage(IRDPSRAPITransportStreamBuffer* This,BYTE **ppbStorage) {
    return This->lpVtbl->get_Storage(This,ppbStorage);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_get_StorageSize(IRDPSRAPITransportStreamBuffer* This,LONG *plMaxStore) {
    return This->lpVtbl->get_StorageSize(This,plMaxStore);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_get_PayloadSize(IRDPSRAPITransportStreamBuffer* This,LONG *plRetVal) {
    return This->lpVtbl->get_PayloadSize(This,plRetVal);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_put_PayloadSize(IRDPSRAPITransportStreamBuffer* This,LONG lVal) {
    return This->lpVtbl->put_PayloadSize(This,lVal);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_get_PayloadOffset(IRDPSRAPITransportStreamBuffer* This,LONG *plRetVal) {
    return This->lpVtbl->get_PayloadOffset(This,plRetVal);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_put_PayloadOffset(IRDPSRAPITransportStreamBuffer* This,LONG lRetVal) {
    return This->lpVtbl->put_PayloadOffset(This,lRetVal);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_get_Flags(IRDPSRAPITransportStreamBuffer* This,LONG *plFlags) {
    return This->lpVtbl->get_Flags(This,plFlags);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_put_Flags(IRDPSRAPITransportStreamBuffer* This,LONG lFlags) {
    return This->lpVtbl->put_Flags(This,lFlags);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_get_Context(IRDPSRAPITransportStreamBuffer* This,IUnknown **ppContext) {
    return This->lpVtbl->get_Context(This,ppContext);
}
static inline HRESULT IRDPSRAPITransportStreamBuffer_put_Context(IRDPSRAPITransportStreamBuffer* This,IUnknown *pContext) {
    return This->lpVtbl->put_Context(This,pContext);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPITransportStreamBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPITransportStreamEvents interface
 */
#ifndef __IRDPSRAPITransportStreamEvents_INTERFACE_DEFINED__
#define __IRDPSRAPITransportStreamEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPITransportStreamEvents, 0xea81c254, 0xf5af, 0x4e40, 0x98,0x2e, 0x3e,0x63,0xbb,0x59,0x52,0x76);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ea81c254-f5af-4e40-982e-3e63bb595276")
IRDPSRAPITransportStreamEvents : public IUnknown
{
    virtual void STDMETHODCALLTYPE OnWriteCompleted(
        IRDPSRAPITransportStreamBuffer *pBuffer) = 0;

    virtual void STDMETHODCALLTYPE OnReadCompleted(
        IRDPSRAPITransportStreamBuffer *pBuffer) = 0;

    virtual void STDMETHODCALLTYPE OnStreamClosed(
        HRESULT hrReason) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPITransportStreamEvents, 0xea81c254, 0xf5af, 0x4e40, 0x98,0x2e, 0x3e,0x63,0xbb,0x59,0x52,0x76)
#endif
#else
typedef struct IRDPSRAPITransportStreamEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPITransportStreamEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPITransportStreamEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPITransportStreamEvents *This);

    /*** IRDPSRAPITransportStreamEvents methods ***/
    void (STDMETHODCALLTYPE *OnWriteCompleted)(
        IRDPSRAPITransportStreamEvents *This,
        IRDPSRAPITransportStreamBuffer *pBuffer);

    void (STDMETHODCALLTYPE *OnReadCompleted)(
        IRDPSRAPITransportStreamEvents *This,
        IRDPSRAPITransportStreamBuffer *pBuffer);

    void (STDMETHODCALLTYPE *OnStreamClosed)(
        IRDPSRAPITransportStreamEvents *This,
        HRESULT hrReason);

    END_INTERFACE
} IRDPSRAPITransportStreamEventsVtbl;

interface IRDPSRAPITransportStreamEvents {
    CONST_VTBL IRDPSRAPITransportStreamEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPITransportStreamEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPITransportStreamEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPITransportStreamEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPSRAPITransportStreamEvents methods ***/
#define IRDPSRAPITransportStreamEvents_OnWriteCompleted(This,pBuffer) (This)->lpVtbl->OnWriteCompleted(This,pBuffer)
#define IRDPSRAPITransportStreamEvents_OnReadCompleted(This,pBuffer) (This)->lpVtbl->OnReadCompleted(This,pBuffer)
#define IRDPSRAPITransportStreamEvents_OnStreamClosed(This,hrReason) (This)->lpVtbl->OnStreamClosed(This,hrReason)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPITransportStreamEvents_QueryInterface(IRDPSRAPITransportStreamEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPITransportStreamEvents_AddRef(IRDPSRAPITransportStreamEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPITransportStreamEvents_Release(IRDPSRAPITransportStreamEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPSRAPITransportStreamEvents methods ***/
static inline void IRDPSRAPITransportStreamEvents_OnWriteCompleted(IRDPSRAPITransportStreamEvents* This,IRDPSRAPITransportStreamBuffer *pBuffer) {
    This->lpVtbl->OnWriteCompleted(This,pBuffer);
}
static inline void IRDPSRAPITransportStreamEvents_OnReadCompleted(IRDPSRAPITransportStreamEvents* This,IRDPSRAPITransportStreamBuffer *pBuffer) {
    This->lpVtbl->OnReadCompleted(This,pBuffer);
}
static inline void IRDPSRAPITransportStreamEvents_OnStreamClosed(IRDPSRAPITransportStreamEvents* This,HRESULT hrReason) {
    This->lpVtbl->OnStreamClosed(This,hrReason);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPITransportStreamEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPITransportStream interface
 */
#ifndef __IRDPSRAPITransportStream_INTERFACE_DEFINED__
#define __IRDPSRAPITransportStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPITransportStream, 0x36cfa065, 0x43bb, 0x4ef7, 0xae,0xd7, 0x9b,0x88,0xa5,0x05,0x30,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("36cfa065-43bb-4ef7-aed7-9b88a5053036")
IRDPSRAPITransportStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AllocBuffer(
        LONG maxPayload,
        IRDPSRAPITransportStreamBuffer **ppBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeBuffer(
        IRDPSRAPITransportStreamBuffer *pBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteBuffer(
        IRDPSRAPITransportStreamBuffer *pBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReadBuffer(
        IRDPSRAPITransportStreamBuffer *pBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE Open(
        IRDPSRAPITransportStreamEvents *pCallbacks) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPITransportStream, 0x36cfa065, 0x43bb, 0x4ef7, 0xae,0xd7, 0x9b,0x88,0xa5,0x05,0x30,0x36)
#endif
#else
typedef struct IRDPSRAPITransportStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPITransportStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPITransportStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPITransportStream *This);

    /*** IRDPSRAPITransportStream methods ***/
    HRESULT (STDMETHODCALLTYPE *AllocBuffer)(
        IRDPSRAPITransportStream *This,
        LONG maxPayload,
        IRDPSRAPITransportStreamBuffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IRDPSRAPITransportStream *This,
        IRDPSRAPITransportStreamBuffer *pBuffer);

    HRESULT (STDMETHODCALLTYPE *WriteBuffer)(
        IRDPSRAPITransportStream *This,
        IRDPSRAPITransportStreamBuffer *pBuffer);

    HRESULT (STDMETHODCALLTYPE *ReadBuffer)(
        IRDPSRAPITransportStream *This,
        IRDPSRAPITransportStreamBuffer *pBuffer);

    HRESULT (STDMETHODCALLTYPE *Open)(
        IRDPSRAPITransportStream *This,
        IRDPSRAPITransportStreamEvents *pCallbacks);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IRDPSRAPITransportStream *This);

    END_INTERFACE
} IRDPSRAPITransportStreamVtbl;

interface IRDPSRAPITransportStream {
    CONST_VTBL IRDPSRAPITransportStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPITransportStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPITransportStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPITransportStream_Release(This) (This)->lpVtbl->Release(This)
/*** IRDPSRAPITransportStream methods ***/
#define IRDPSRAPITransportStream_AllocBuffer(This,maxPayload,ppBuffer) (This)->lpVtbl->AllocBuffer(This,maxPayload,ppBuffer)
#define IRDPSRAPITransportStream_FreeBuffer(This,pBuffer) (This)->lpVtbl->FreeBuffer(This,pBuffer)
#define IRDPSRAPITransportStream_WriteBuffer(This,pBuffer) (This)->lpVtbl->WriteBuffer(This,pBuffer)
#define IRDPSRAPITransportStream_ReadBuffer(This,pBuffer) (This)->lpVtbl->ReadBuffer(This,pBuffer)
#define IRDPSRAPITransportStream_Open(This,pCallbacks) (This)->lpVtbl->Open(This,pCallbacks)
#define IRDPSRAPITransportStream_Close(This) (This)->lpVtbl->Close(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPITransportStream_QueryInterface(IRDPSRAPITransportStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPITransportStream_AddRef(IRDPSRAPITransportStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPITransportStream_Release(IRDPSRAPITransportStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IRDPSRAPITransportStream methods ***/
static inline HRESULT IRDPSRAPITransportStream_AllocBuffer(IRDPSRAPITransportStream* This,LONG maxPayload,IRDPSRAPITransportStreamBuffer **ppBuffer) {
    return This->lpVtbl->AllocBuffer(This,maxPayload,ppBuffer);
}
static inline HRESULT IRDPSRAPITransportStream_FreeBuffer(IRDPSRAPITransportStream* This,IRDPSRAPITransportStreamBuffer *pBuffer) {
    return This->lpVtbl->FreeBuffer(This,pBuffer);
}
static inline HRESULT IRDPSRAPITransportStream_WriteBuffer(IRDPSRAPITransportStream* This,IRDPSRAPITransportStreamBuffer *pBuffer) {
    return This->lpVtbl->WriteBuffer(This,pBuffer);
}
static inline HRESULT IRDPSRAPITransportStream_ReadBuffer(IRDPSRAPITransportStream* This,IRDPSRAPITransportStreamBuffer *pBuffer) {
    return This->lpVtbl->ReadBuffer(This,pBuffer);
}
static inline HRESULT IRDPSRAPITransportStream_Open(IRDPSRAPITransportStream* This,IRDPSRAPITransportStreamEvents *pCallbacks) {
    return This->lpVtbl->Open(This,pCallbacks);
}
static inline HRESULT IRDPSRAPITransportStream_Close(IRDPSRAPITransportStream* This) {
    return This->lpVtbl->Close(This);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPITransportStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPISharingSession interface
 */
#ifndef __IRDPSRAPISharingSession_INTERFACE_DEFINED__
#define __IRDPSRAPISharingSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPISharingSession, 0xeeb20886, 0xe470, 0x4cf6, 0x84,0x2b, 0x27,0x39,0xc0,0xec,0x5c,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eeb20886-e470-4cf6-842b-2739c0ec5cfb")
IRDPSRAPISharingSession : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Open(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ColorDepth(
        LONG colorDepth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ColorDepth(
        LONG *pColorDepth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Properties(
        IRDPSRAPISessionProperties **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Attendees(
        IRDPSRAPIAttendeeManager **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Invitations(
        IRDPSRAPIInvitationManager **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ApplicationFilter(
        IRDPSRAPIApplicationFilter **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_VirtualChannelManager(
        IRDPSRAPIVirtualChannelManager **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConnectToClient(
        BSTR bstrConnectionString) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDesktopSharedRect(
        LONG left,
        LONG top,
        LONG right,
        LONG bottom) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesktopSharedRect(
        LONG *pleft,
        LONG *ptop,
        LONG *pright,
        LONG *pbottom) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPISharingSession, 0xeeb20886, 0xe470, 0x4cf6, 0x84,0x2b, 0x27,0x39,0xc0,0xec,0x5c,0xfb)
#endif
#else
typedef struct IRDPSRAPISharingSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPISharingSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPISharingSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPISharingSession *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPISharingSession *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPISharingSession *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPISharingSession *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPISharingSession *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPISharingSession methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IRDPSRAPISharingSession *This);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IRDPSRAPISharingSession *This);

    HRESULT (STDMETHODCALLTYPE *put_ColorDepth)(
        IRDPSRAPISharingSession *This,
        LONG colorDepth);

    HRESULT (STDMETHODCALLTYPE *get_ColorDepth)(
        IRDPSRAPISharingSession *This,
        LONG *pColorDepth);

    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        IRDPSRAPISharingSession *This,
        IRDPSRAPISessionProperties **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_Attendees)(
        IRDPSRAPISharingSession *This,
        IRDPSRAPIAttendeeManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_Invitations)(
        IRDPSRAPISharingSession *This,
        IRDPSRAPIInvitationManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_ApplicationFilter)(
        IRDPSRAPISharingSession *This,
        IRDPSRAPIApplicationFilter **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_VirtualChannelManager)(
        IRDPSRAPISharingSession *This,
        IRDPSRAPIVirtualChannelManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IRDPSRAPISharingSession *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IRDPSRAPISharingSession *This);

    HRESULT (STDMETHODCALLTYPE *ConnectToClient)(
        IRDPSRAPISharingSession *This,
        BSTR bstrConnectionString);

    HRESULT (STDMETHODCALLTYPE *SetDesktopSharedRect)(
        IRDPSRAPISharingSession *This,
        LONG left,
        LONG top,
        LONG right,
        LONG bottom);

    HRESULT (STDMETHODCALLTYPE *GetDesktopSharedRect)(
        IRDPSRAPISharingSession *This,
        LONG *pleft,
        LONG *ptop,
        LONG *pright,
        LONG *pbottom);

    END_INTERFACE
} IRDPSRAPISharingSessionVtbl;

interface IRDPSRAPISharingSession {
    CONST_VTBL IRDPSRAPISharingSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPISharingSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPISharingSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPISharingSession_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPISharingSession_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPISharingSession_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPISharingSession_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPISharingSession_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPISharingSession methods ***/
#define IRDPSRAPISharingSession_Open(This) (This)->lpVtbl->Open(This)
#define IRDPSRAPISharingSession_Close(This) (This)->lpVtbl->Close(This)
#define IRDPSRAPISharingSession_put_ColorDepth(This,colorDepth) (This)->lpVtbl->put_ColorDepth(This,colorDepth)
#define IRDPSRAPISharingSession_get_ColorDepth(This,pColorDepth) (This)->lpVtbl->get_ColorDepth(This,pColorDepth)
#define IRDPSRAPISharingSession_get_Properties(This,ppVal) (This)->lpVtbl->get_Properties(This,ppVal)
#define IRDPSRAPISharingSession_get_Attendees(This,ppVal) (This)->lpVtbl->get_Attendees(This,ppVal)
#define IRDPSRAPISharingSession_get_Invitations(This,ppVal) (This)->lpVtbl->get_Invitations(This,ppVal)
#define IRDPSRAPISharingSession_get_ApplicationFilter(This,ppVal) (This)->lpVtbl->get_ApplicationFilter(This,ppVal)
#define IRDPSRAPISharingSession_get_VirtualChannelManager(This,ppVal) (This)->lpVtbl->get_VirtualChannelManager(This,ppVal)
#define IRDPSRAPISharingSession_Pause(This) (This)->lpVtbl->Pause(This)
#define IRDPSRAPISharingSession_Resume(This) (This)->lpVtbl->Resume(This)
#define IRDPSRAPISharingSession_ConnectToClient(This,bstrConnectionString) (This)->lpVtbl->ConnectToClient(This,bstrConnectionString)
#define IRDPSRAPISharingSession_SetDesktopSharedRect(This,left,top,right,bottom) (This)->lpVtbl->SetDesktopSharedRect(This,left,top,right,bottom)
#define IRDPSRAPISharingSession_GetDesktopSharedRect(This,pleft,ptop,pright,pbottom) (This)->lpVtbl->GetDesktopSharedRect(This,pleft,ptop,pright,pbottom)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPISharingSession_QueryInterface(IRDPSRAPISharingSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPISharingSession_AddRef(IRDPSRAPISharingSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPISharingSession_Release(IRDPSRAPISharingSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPISharingSession_GetTypeInfoCount(IRDPSRAPISharingSession* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPISharingSession_GetTypeInfo(IRDPSRAPISharingSession* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPISharingSession_GetIDsOfNames(IRDPSRAPISharingSession* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPISharingSession_Invoke(IRDPSRAPISharingSession* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPISharingSession methods ***/
static inline HRESULT IRDPSRAPISharingSession_Open(IRDPSRAPISharingSession* This) {
    return This->lpVtbl->Open(This);
}
static inline HRESULT IRDPSRAPISharingSession_Close(IRDPSRAPISharingSession* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IRDPSRAPISharingSession_put_ColorDepth(IRDPSRAPISharingSession* This,LONG colorDepth) {
    return This->lpVtbl->put_ColorDepth(This,colorDepth);
}
static inline HRESULT IRDPSRAPISharingSession_get_ColorDepth(IRDPSRAPISharingSession* This,LONG *pColorDepth) {
    return This->lpVtbl->get_ColorDepth(This,pColorDepth);
}
static inline HRESULT IRDPSRAPISharingSession_get_Properties(IRDPSRAPISharingSession* This,IRDPSRAPISessionProperties **ppVal) {
    return This->lpVtbl->get_Properties(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession_get_Attendees(IRDPSRAPISharingSession* This,IRDPSRAPIAttendeeManager **ppVal) {
    return This->lpVtbl->get_Attendees(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession_get_Invitations(IRDPSRAPISharingSession* This,IRDPSRAPIInvitationManager **ppVal) {
    return This->lpVtbl->get_Invitations(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession_get_ApplicationFilter(IRDPSRAPISharingSession* This,IRDPSRAPIApplicationFilter **ppVal) {
    return This->lpVtbl->get_ApplicationFilter(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession_get_VirtualChannelManager(IRDPSRAPISharingSession* This,IRDPSRAPIVirtualChannelManager **ppVal) {
    return This->lpVtbl->get_VirtualChannelManager(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession_Pause(IRDPSRAPISharingSession* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IRDPSRAPISharingSession_Resume(IRDPSRAPISharingSession* This) {
    return This->lpVtbl->Resume(This);
}
static inline HRESULT IRDPSRAPISharingSession_ConnectToClient(IRDPSRAPISharingSession* This,BSTR bstrConnectionString) {
    return This->lpVtbl->ConnectToClient(This,bstrConnectionString);
}
static inline HRESULT IRDPSRAPISharingSession_SetDesktopSharedRect(IRDPSRAPISharingSession* This,LONG left,LONG top,LONG right,LONG bottom) {
    return This->lpVtbl->SetDesktopSharedRect(This,left,top,right,bottom);
}
static inline HRESULT IRDPSRAPISharingSession_GetDesktopSharedRect(IRDPSRAPISharingSession* This,LONG *pleft,LONG *ptop,LONG *pright,LONG *pbottom) {
    return This->lpVtbl->GetDesktopSharedRect(This,pleft,ptop,pright,pbottom);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPISharingSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRDPSRAPISharingSession2 interface
 */
#ifndef __IRDPSRAPISharingSession2_INTERFACE_DEFINED__
#define __IRDPSRAPISharingSession2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRDPSRAPISharingSession2, 0xfee4ee57, 0xe3e8, 0x4205, 0x8f,0xb0, 0x8f,0xd1,0xd0,0x67,0x5c,0x21);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fee4ee57-e3e8-4205-8fb0-8fd1d0675c21")
IRDPSRAPISharingSession2 : public IRDPSRAPISharingSession
{
    virtual HRESULT STDMETHODCALLTYPE ConnectUsingTransportStream(
        IRDPSRAPITransportStream *pStream,
        BSTR bstrGroup,
        BSTR bstrAuthenticatedAttendeeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FrameBuffer(
        IRDPSRAPIFrameBuffer **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendControlLevelChangeResponse(
        IRDPSRAPIAttendee *pAttendee,
        CTRL_LEVEL RequestedLevel,
        LONG ReasonCode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRDPSRAPISharingSession2, 0xfee4ee57, 0xe3e8, 0x4205, 0x8f,0xb0, 0x8f,0xd1,0xd0,0x67,0x5c,0x21)
#endif
#else
typedef struct IRDPSRAPISharingSession2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRDPSRAPISharingSession2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRDPSRAPISharingSession2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRDPSRAPISharingSession2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRDPSRAPISharingSession2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRDPSRAPISharingSession2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRDPSRAPISharingSession2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRDPSRAPISharingSession2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRDPSRAPISharingSession methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IRDPSRAPISharingSession2 *This);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IRDPSRAPISharingSession2 *This);

    HRESULT (STDMETHODCALLTYPE *put_ColorDepth)(
        IRDPSRAPISharingSession2 *This,
        LONG colorDepth);

    HRESULT (STDMETHODCALLTYPE *get_ColorDepth)(
        IRDPSRAPISharingSession2 *This,
        LONG *pColorDepth);

    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        IRDPSRAPISharingSession2 *This,
        IRDPSRAPISessionProperties **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_Attendees)(
        IRDPSRAPISharingSession2 *This,
        IRDPSRAPIAttendeeManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_Invitations)(
        IRDPSRAPISharingSession2 *This,
        IRDPSRAPIInvitationManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_ApplicationFilter)(
        IRDPSRAPISharingSession2 *This,
        IRDPSRAPIApplicationFilter **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_VirtualChannelManager)(
        IRDPSRAPISharingSession2 *This,
        IRDPSRAPIVirtualChannelManager **ppVal);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IRDPSRAPISharingSession2 *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IRDPSRAPISharingSession2 *This);

    HRESULT (STDMETHODCALLTYPE *ConnectToClient)(
        IRDPSRAPISharingSession2 *This,
        BSTR bstrConnectionString);

    HRESULT (STDMETHODCALLTYPE *SetDesktopSharedRect)(
        IRDPSRAPISharingSession2 *This,
        LONG left,
        LONG top,
        LONG right,
        LONG bottom);

    HRESULT (STDMETHODCALLTYPE *GetDesktopSharedRect)(
        IRDPSRAPISharingSession2 *This,
        LONG *pleft,
        LONG *ptop,
        LONG *pright,
        LONG *pbottom);

    /*** IRDPSRAPISharingSession2 methods ***/
    HRESULT (STDMETHODCALLTYPE *ConnectUsingTransportStream)(
        IRDPSRAPISharingSession2 *This,
        IRDPSRAPITransportStream *pStream,
        BSTR bstrGroup,
        BSTR bstrAuthenticatedAttendeeName);

    HRESULT (STDMETHODCALLTYPE *get_FrameBuffer)(
        IRDPSRAPISharingSession2 *This,
        IRDPSRAPIFrameBuffer **ppVal);

    HRESULT (STDMETHODCALLTYPE *SendControlLevelChangeResponse)(
        IRDPSRAPISharingSession2 *This,
        IRDPSRAPIAttendee *pAttendee,
        CTRL_LEVEL RequestedLevel,
        LONG ReasonCode);

    END_INTERFACE
} IRDPSRAPISharingSession2Vtbl;

interface IRDPSRAPISharingSession2 {
    CONST_VTBL IRDPSRAPISharingSession2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRDPSRAPISharingSession2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRDPSRAPISharingSession2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRDPSRAPISharingSession2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRDPSRAPISharingSession2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRDPSRAPISharingSession2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRDPSRAPISharingSession2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRDPSRAPISharingSession2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRDPSRAPISharingSession methods ***/
#define IRDPSRAPISharingSession2_Open(This) (This)->lpVtbl->Open(This)
#define IRDPSRAPISharingSession2_Close(This) (This)->lpVtbl->Close(This)
#define IRDPSRAPISharingSession2_put_ColorDepth(This,colorDepth) (This)->lpVtbl->put_ColorDepth(This,colorDepth)
#define IRDPSRAPISharingSession2_get_ColorDepth(This,pColorDepth) (This)->lpVtbl->get_ColorDepth(This,pColorDepth)
#define IRDPSRAPISharingSession2_get_Properties(This,ppVal) (This)->lpVtbl->get_Properties(This,ppVal)
#define IRDPSRAPISharingSession2_get_Attendees(This,ppVal) (This)->lpVtbl->get_Attendees(This,ppVal)
#define IRDPSRAPISharingSession2_get_Invitations(This,ppVal) (This)->lpVtbl->get_Invitations(This,ppVal)
#define IRDPSRAPISharingSession2_get_ApplicationFilter(This,ppVal) (This)->lpVtbl->get_ApplicationFilter(This,ppVal)
#define IRDPSRAPISharingSession2_get_VirtualChannelManager(This,ppVal) (This)->lpVtbl->get_VirtualChannelManager(This,ppVal)
#define IRDPSRAPISharingSession2_Pause(This) (This)->lpVtbl->Pause(This)
#define IRDPSRAPISharingSession2_Resume(This) (This)->lpVtbl->Resume(This)
#define IRDPSRAPISharingSession2_ConnectToClient(This,bstrConnectionString) (This)->lpVtbl->ConnectToClient(This,bstrConnectionString)
#define IRDPSRAPISharingSession2_SetDesktopSharedRect(This,left,top,right,bottom) (This)->lpVtbl->SetDesktopSharedRect(This,left,top,right,bottom)
#define IRDPSRAPISharingSession2_GetDesktopSharedRect(This,pleft,ptop,pright,pbottom) (This)->lpVtbl->GetDesktopSharedRect(This,pleft,ptop,pright,pbottom)
/*** IRDPSRAPISharingSession2 methods ***/
#define IRDPSRAPISharingSession2_ConnectUsingTransportStream(This,pStream,bstrGroup,bstrAuthenticatedAttendeeName) (This)->lpVtbl->ConnectUsingTransportStream(This,pStream,bstrGroup,bstrAuthenticatedAttendeeName)
#define IRDPSRAPISharingSession2_get_FrameBuffer(This,ppVal) (This)->lpVtbl->get_FrameBuffer(This,ppVal)
#define IRDPSRAPISharingSession2_SendControlLevelChangeResponse(This,pAttendee,RequestedLevel,ReasonCode) (This)->lpVtbl->SendControlLevelChangeResponse(This,pAttendee,RequestedLevel,ReasonCode)
#else
/*** IUnknown methods ***/
static inline HRESULT IRDPSRAPISharingSession2_QueryInterface(IRDPSRAPISharingSession2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRDPSRAPISharingSession2_AddRef(IRDPSRAPISharingSession2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRDPSRAPISharingSession2_Release(IRDPSRAPISharingSession2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRDPSRAPISharingSession2_GetTypeInfoCount(IRDPSRAPISharingSession2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRDPSRAPISharingSession2_GetTypeInfo(IRDPSRAPISharingSession2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRDPSRAPISharingSession2_GetIDsOfNames(IRDPSRAPISharingSession2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRDPSRAPISharingSession2_Invoke(IRDPSRAPISharingSession2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRDPSRAPISharingSession methods ***/
static inline HRESULT IRDPSRAPISharingSession2_Open(IRDPSRAPISharingSession2* This) {
    return This->lpVtbl->Open(This);
}
static inline HRESULT IRDPSRAPISharingSession2_Close(IRDPSRAPISharingSession2* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IRDPSRAPISharingSession2_put_ColorDepth(IRDPSRAPISharingSession2* This,LONG colorDepth) {
    return This->lpVtbl->put_ColorDepth(This,colorDepth);
}
static inline HRESULT IRDPSRAPISharingSession2_get_ColorDepth(IRDPSRAPISharingSession2* This,LONG *pColorDepth) {
    return This->lpVtbl->get_ColorDepth(This,pColorDepth);
}
static inline HRESULT IRDPSRAPISharingSession2_get_Properties(IRDPSRAPISharingSession2* This,IRDPSRAPISessionProperties **ppVal) {
    return This->lpVtbl->get_Properties(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession2_get_Attendees(IRDPSRAPISharingSession2* This,IRDPSRAPIAttendeeManager **ppVal) {
    return This->lpVtbl->get_Attendees(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession2_get_Invitations(IRDPSRAPISharingSession2* This,IRDPSRAPIInvitationManager **ppVal) {
    return This->lpVtbl->get_Invitations(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession2_get_ApplicationFilter(IRDPSRAPISharingSession2* This,IRDPSRAPIApplicationFilter **ppVal) {
    return This->lpVtbl->get_ApplicationFilter(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession2_get_VirtualChannelManager(IRDPSRAPISharingSession2* This,IRDPSRAPIVirtualChannelManager **ppVal) {
    return This->lpVtbl->get_VirtualChannelManager(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession2_Pause(IRDPSRAPISharingSession2* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IRDPSRAPISharingSession2_Resume(IRDPSRAPISharingSession2* This) {
    return This->lpVtbl->Resume(This);
}
static inline HRESULT IRDPSRAPISharingSession2_ConnectToClient(IRDPSRAPISharingSession2* This,BSTR bstrConnectionString) {
    return This->lpVtbl->ConnectToClient(This,bstrConnectionString);
}
static inline HRESULT IRDPSRAPISharingSession2_SetDesktopSharedRect(IRDPSRAPISharingSession2* This,LONG left,LONG top,LONG right,LONG bottom) {
    return This->lpVtbl->SetDesktopSharedRect(This,left,top,right,bottom);
}
static inline HRESULT IRDPSRAPISharingSession2_GetDesktopSharedRect(IRDPSRAPISharingSession2* This,LONG *pleft,LONG *ptop,LONG *pright,LONG *pbottom) {
    return This->lpVtbl->GetDesktopSharedRect(This,pleft,ptop,pright,pbottom);
}
/*** IRDPSRAPISharingSession2 methods ***/
static inline HRESULT IRDPSRAPISharingSession2_ConnectUsingTransportStream(IRDPSRAPISharingSession2* This,IRDPSRAPITransportStream *pStream,BSTR bstrGroup,BSTR bstrAuthenticatedAttendeeName) {
    return This->lpVtbl->ConnectUsingTransportStream(This,pStream,bstrGroup,bstrAuthenticatedAttendeeName);
}
static inline HRESULT IRDPSRAPISharingSession2_get_FrameBuffer(IRDPSRAPISharingSession2* This,IRDPSRAPIFrameBuffer **ppVal) {
    return This->lpVtbl->get_FrameBuffer(This,ppVal);
}
static inline HRESULT IRDPSRAPISharingSession2_SendControlLevelChangeResponse(IRDPSRAPISharingSession2* This,IRDPSRAPIAttendee *pAttendee,CTRL_LEVEL RequestedLevel,LONG ReasonCode) {
    return This->lpVtbl->SendControlLevelChangeResponse(This,pAttendee,RequestedLevel,ReasonCode);
}
#endif
#endif

#endif


#endif  /* __IRDPSRAPISharingSession2_INTERFACE_DEFINED__ */

#ifndef __RDPCOMAPILib_LIBRARY_DEFINED__
#define __RDPCOMAPILib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_RDPCOMAPILib, 0xcc802d05, 0xae07, 0x4c15, 0xb4,0x96, 0xdb,0x9d,0x22,0xaa,0x0a,0x84);

typedef enum __WIDL_rdpencomapi_generated_name_0000002B {
    CONST_MAX_CHANNEL_MESSAGE_SIZE = 1024,
    CONST_MAX_CHANNEL_NAME_LEN = 8,
    CONST_MAX_LEGACY_CHANNEL_MESSAGE_SIZE = 409600,
    CONST_ATTENDEE_ID_EVERYONE = -1,
    CONST_ATTENDEE_ID_HOST = 0,
    CONST_CONN_INTERVAL = 50,
    CONST_ATTENDEE_ID_DEFAULT = 0xffffffff
} RDPENCOMAPI_CONSTANTS;
/*****************************************************************************
 * _IRDPSessionEvents dispinterface
 */
#ifndef ___IRDPSessionEvents_DISPINTERFACE_DEFINED__
#define ___IRDPSessionEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID__IRDPSessionEvents, 0x98a97042, 0x6698, 0x40e9, 0x8e,0xfd, 0xb3,0x20,0x09,0x90,0x00,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("98a97042-6698-40e9-8efd-b3200990004b")
_IRDPSessionEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(_IRDPSessionEvents, 0x98a97042, 0x6698, 0x40e9, 0x8e,0xfd, 0xb3,0x20,0x09,0x90,0x00,0x4b)
#endif
#else
typedef struct _IRDPSessionEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        _IRDPSessionEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        _IRDPSessionEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        _IRDPSessionEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        _IRDPSessionEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        _IRDPSessionEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        _IRDPSessionEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        _IRDPSessionEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} _IRDPSessionEventsVtbl;

interface _IRDPSessionEvents {
    CONST_VTBL _IRDPSessionEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define _IRDPSessionEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define _IRDPSessionEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define _IRDPSessionEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define _IRDPSessionEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define _IRDPSessionEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define _IRDPSessionEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define _IRDPSessionEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT _IRDPSessionEvents_QueryInterface(_IRDPSessionEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG _IRDPSessionEvents_AddRef(_IRDPSessionEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG _IRDPSessionEvents_Release(_IRDPSessionEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT _IRDPSessionEvents_GetTypeInfoCount(_IRDPSessionEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT _IRDPSessionEvents_GetTypeInfo(_IRDPSessionEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT _IRDPSessionEvents_GetIDsOfNames(_IRDPSessionEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT _IRDPSessionEvents_Invoke(_IRDPSessionEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* ___IRDPSessionEvents_DISPINTERFACE_DEFINED__ */

typedef struct __ReferenceRemainingTypes__ {
    CTRL_LEVEL __ctrlLevel__;
    ATTENDEE_DISCONNECT_REASON __attendeeDisconnectReason__;
    CHANNEL_PRIORITY __channelPriority__;
    CHANNEL_FLAGS __channelFlags__;
    CHANNEL_ACCESS_ENUM __channelAccessEnum__;
    RDPENCOMAPI_ATTENDEE_FLAGS __rdpencomapiAttendeeFlags__;
    RDPSRAPI_WND_FLAGS __rdpsrapiWndFlags__;
    RDPSRAPI_APP_FLAGS __rdpsrapiAppFlags__;
} __ReferenceRemainingTypes__;
#ifndef __IRDPViewerRenderingSurface_FWD_DEFINED__
#define __IRDPViewerRenderingSurface_FWD_DEFINED__
typedef interface IRDPViewerRenderingSurface IRDPViewerRenderingSurface;
#ifdef __cplusplus
interface IRDPViewerRenderingSurface;
#endif /* __cplusplus */
#endif

#ifndef __IRDPViewerInputSink_FWD_DEFINED__
#define __IRDPViewerInputSink_FWD_DEFINED__
typedef interface IRDPViewerInputSink IRDPViewerInputSink;
#ifdef __cplusplus
interface IRDPViewerInputSink;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIAudioStream_FWD_DEFINED__
#define __IRDPSRAPIAudioStream_FWD_DEFINED__
typedef interface IRDPSRAPIAudioStream IRDPSRAPIAudioStream;
#ifdef __cplusplus
interface IRDPSRAPIAudioStream;
#endif /* __cplusplus */
#endif

#ifndef __IRDPSRAPIPerfCounterLoggingManager_FWD_DEFINED__
#define __IRDPSRAPIPerfCounterLoggingManager_FWD_DEFINED__
typedef interface IRDPSRAPIPerfCounterLoggingManager IRDPSRAPIPerfCounterLoggingManager;
#ifdef __cplusplus
interface IRDPSRAPIPerfCounterLoggingManager;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * RDPViewer coclass
 */

DEFINE_GUID(CLSID_RDPViewer, 0x32be5ed2, 0x5c86, 0x480f, 0xa9,0x14, 0x0f,0xf8,0x88,0x5a,0x1b,0x3f);

#ifdef __cplusplus
class DECLSPEC_UUID("32be5ed2-5c86-480f-a914-0ff8885a1b3f") RDPViewer;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPViewer, 0x32be5ed2, 0x5c86, 0x480f, 0xa9,0x14, 0x0f,0xf8,0x88,0x5a,0x1b,0x3f)
#endif
#endif

/*****************************************************************************
 * RDPSRAPISessionProperties coclass
 */

DEFINE_GUID(CLSID_RDPSRAPISessionProperties, 0xdd7594ff, 0xea2a, 0x4c06, 0x8f,0xdf, 0x13,0x2d,0xe4,0x8b,0x65,0x10);

#ifdef __cplusplus
class DECLSPEC_UUID("dd7594ff-ea2a-4c06-8fdf-132de48b6510") RDPSRAPISessionProperties;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPISessionProperties, 0xdd7594ff, 0xea2a, 0x4c06, 0x8f,0xdf, 0x13,0x2d,0xe4,0x8b,0x65,0x10)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIInvitationManager coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIInvitationManager, 0x53d9c9db, 0x75ab, 0x4271, 0x94,0x8a, 0x4c,0x4e,0xb3,0x6a,0x8f,0x2b);

#ifdef __cplusplus
class DECLSPEC_UUID("53d9c9db-75ab-4271-948a-4c4eb36a8f2b") RDPSRAPIInvitationManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIInvitationManager, 0x53d9c9db, 0x75ab, 0x4271, 0x94,0x8a, 0x4c,0x4e,0xb3,0x6a,0x8f,0x2b)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIInvitation coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIInvitation, 0x49174dc6, 0x0731, 0x4b5e, 0x8e,0xe1, 0x83,0xa6,0x3d,0x38,0x68,0xfa);

#ifdef __cplusplus
class DECLSPEC_UUID("49174dc6-0731-4b5e-8ee1-83a63d3868fa") RDPSRAPIInvitation;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIInvitation, 0x49174dc6, 0x0731, 0x4b5e, 0x8e,0xe1, 0x83,0xa6,0x3d,0x38,0x68,0xfa)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIAttendeeManager coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIAttendeeManager, 0xd7b13a01, 0xf7d4, 0x42a6, 0x85,0x95, 0x12,0xfc,0x8c,0x24,0xe8,0x51);

#ifdef __cplusplus
class DECLSPEC_UUID("d7b13a01-f7d4-42a6-8595-12fc8c24e851") RDPSRAPIAttendeeManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIAttendeeManager, 0xd7b13a01, 0xf7d4, 0x42a6, 0x85,0x95, 0x12,0xfc,0x8c,0x24,0xe8,0x51)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIAttendee coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIAttendee, 0x74f93bb5, 0x755f, 0x488e, 0x8a,0x29, 0x23,0x90,0x10,0x8a,0xef,0x55);

#ifdef __cplusplus
class DECLSPEC_UUID("74f93bb5-755f-488e-8a29-2390108aef55") RDPSRAPIAttendee;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIAttendee, 0x74f93bb5, 0x755f, 0x488e, 0x8a,0x29, 0x23,0x90,0x10,0x8a,0xef,0x55)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIAttendeeDisconnectInfo coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIAttendeeDisconnectInfo, 0xb47d7250, 0x5bdb, 0x405d, 0xb4,0x87, 0xca,0xad,0x9c,0x56,0xf4,0xf8);

#ifdef __cplusplus
class DECLSPEC_UUID("b47d7250-5bdb-405d-b487-caad9c56f4f8") RDPSRAPIAttendeeDisconnectInfo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIAttendeeDisconnectInfo, 0xb47d7250, 0x5bdb, 0x405d, 0xb4,0x87, 0xca,0xad,0x9c,0x56,0xf4,0xf8)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIApplicationFilter coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIApplicationFilter, 0xe35ace89, 0xc7e8, 0x427e, 0xa4,0xf9, 0xb9,0xda,0x07,0x28,0x26,0xbd);

#ifdef __cplusplus
class DECLSPEC_UUID("e35ace89-c7e8-427e-a4f9-b9da072826bd") RDPSRAPIApplicationFilter;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIApplicationFilter, 0xe35ace89, 0xc7e8, 0x427e, 0xa4,0xf9, 0xb9,0xda,0x07,0x28,0x26,0xbd)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIApplicationList coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIApplicationList, 0x9e31c815, 0x7433, 0x4876, 0x97,0xfb, 0xed,0x59,0xfe,0x2b,0xaa,0x22);

#ifdef __cplusplus
class DECLSPEC_UUID("9e31c815-7433-4876-97fb-ed59fe2baa22") RDPSRAPIApplicationList;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIApplicationList, 0x9e31c815, 0x7433, 0x4876, 0x97,0xfb, 0xed,0x59,0xfe,0x2b,0xaa,0x22)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIApplication coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIApplication, 0xc116a484, 0x4b25, 0x4b9f, 0x8a,0x54, 0xb9,0x34,0xb0,0x6e,0x57,0xfa);

#ifdef __cplusplus
class DECLSPEC_UUID("c116a484-4b25-4b9f-8a54-b934b06e57fa") RDPSRAPIApplication;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIApplication, 0xc116a484, 0x4b25, 0x4b9f, 0x8a,0x54, 0xb9,0x34,0xb0,0x6e,0x57,0xfa)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIWindowList coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIWindowList, 0x9c21e2b8, 0x5dd4, 0x42cc, 0x81,0xba, 0x1c,0x09,0x98,0x52,0xe6,0xfa);

#ifdef __cplusplus
class DECLSPEC_UUID("9c21e2b8-5dd4-42cc-81ba-1c099852e6fa") RDPSRAPIWindowList;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIWindowList, 0x9c21e2b8, 0x5dd4, 0x42cc, 0x81,0xba, 0x1c,0x09,0x98,0x52,0xe6,0xfa)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIWindow coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIWindow, 0x03cf46db, 0xce45, 0x4d36, 0x86,0xed, 0xed,0x28,0xb7,0x43,0x98,0xbf);

#ifdef __cplusplus
class DECLSPEC_UUID("03cf46db-ce45-4d36-86ed-ed28b74398bf") RDPSRAPIWindow;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIWindow, 0x03cf46db, 0xce45, 0x4d36, 0x86,0xed, 0xed,0x28,0xb7,0x43,0x98,0xbf)
#endif
#endif

/*****************************************************************************
 * RDPSRAPITcpConnectionInfo coclass
 */

DEFINE_GUID(CLSID_RDPSRAPITcpConnectionInfo, 0xbe49db3f, 0xebb6, 0x4278, 0x8c,0xe0, 0xd5,0x45,0x58,0x33,0xea,0xee);

#ifdef __cplusplus
class DECLSPEC_UUID("be49db3f-ebb6-4278-8ce0-d5455833eaee") RDPSRAPITcpConnectionInfo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPITcpConnectionInfo, 0xbe49db3f, 0xebb6, 0x4278, 0x8c,0xe0, 0xd5,0x45,0x58,0x33,0xea,0xee)
#endif
#endif

/*****************************************************************************
 * RDPSession coclass
 */

DEFINE_GUID(CLSID_RDPSession, 0x9b78f0e6, 0x3e05, 0x4a5b, 0xb2,0xe8, 0xe7,0x43,0xa8,0x95,0x6b,0x65);

#ifdef __cplusplus
class DECLSPEC_UUID("9b78f0e6-3e05-4a5b-b2e8-e743a8956b65") RDPSession;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSession, 0x9b78f0e6, 0x3e05, 0x4a5b, 0xb2,0xe8, 0xe7,0x43,0xa8,0x95,0x6b,0x65)
#endif
#endif

/*****************************************************************************
 * RDPSRAPIFrameBuffer coclass
 */

DEFINE_GUID(CLSID_RDPSRAPIFrameBuffer, 0xa4f66bcc, 0x538e, 0x4101, 0x95,0x1d, 0x30,0x84,0x7a,0xdb,0x51,0x01);

#ifdef __cplusplus
class DECLSPEC_UUID("a4f66bcc-538e-4101-951d-30847adb5101") RDPSRAPIFrameBuffer;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPSRAPIFrameBuffer, 0xa4f66bcc, 0x538e, 0x4101, 0x95,0x1d, 0x30,0x84,0x7a,0xdb,0x51,0x01)
#endif
#endif

/*****************************************************************************
 * RDPTransportStreamBuffer coclass
 */

DEFINE_GUID(CLSID_RDPTransportStreamBuffer, 0x8d4a1c69, 0xf17f, 0x4549, 0xa6,0x99, 0x76,0x1c,0x6e,0x6b,0x5c,0x0a);

#ifdef __cplusplus
class DECLSPEC_UUID("8d4a1c69-f17f-4549-a699-761c6e6b5c0a") RDPTransportStreamBuffer;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPTransportStreamBuffer, 0x8d4a1c69, 0xf17f, 0x4549, 0xa6,0x99, 0x76,0x1c,0x6e,0x6b,0x5c,0x0a)
#endif
#endif

/*****************************************************************************
 * RDPTransportStreamEvents coclass
 */

DEFINE_GUID(CLSID_RDPTransportStreamEvents, 0x31e3ab20, 0x5350, 0x483f, 0x9d,0xc6, 0x67,0x48,0x66,0x5e,0xfd,0xeb);

#ifdef __cplusplus
class DECLSPEC_UUID("31e3ab20-5350-483f-9dc6-6748665efdeb") RDPTransportStreamEvents;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(RDPTransportStreamEvents, 0x31e3ab20, 0x5350, 0x483f, 0x9d,0xc6, 0x67,0x48,0x66,0x5e,0xfd,0xeb)
#endif
#endif

#endif /* __RDPCOMAPILib_LIBRARY_DEFINED__ */
#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP) */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __rdpencomapi_h__ */
