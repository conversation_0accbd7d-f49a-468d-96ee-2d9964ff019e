/*******************************************************************************
* Copyright 2019-2020 Intel Corporation
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*******************************************************************************/

#ifndef CPU_MATMUL_CPU_MATMUL_PD_HPP
#define CPU_MATMUL_CPU_MATMUL_PD_HPP

#include "common/matmul_pd.hpp"

#include "cpu/cpu_engine.hpp"

namespace dnnl {
namespace impl {
namespace cpu {
namespace matmul {

struct cpu_matmul_pd_t : public matmul_pd_t {
    using matmul_pd_t::matmul_pd_t;
};

} // namespace matmul
} // namespace cpu
} // namespace impl
} // namespace dnnl

#endif
