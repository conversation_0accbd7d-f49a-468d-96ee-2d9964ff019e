cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER within this package.")
cpp_quote(" */")
cpp_quote("")
cpp_quote("#include <winapifamily.h>")
cpp_quote("")

#ifndef DO_NO_IMPORTS
import "oleidl.idl";
import "oaidl.idl";
import "oleidl.idl";
import "servprov.idl";
import "urlmon.idl";
#endif

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
interface IEnumConnections;
interface IEnumConnectionPoints;
interface IConnectionPoint;
interface IConnectionPointContainer;
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
interface IBindHost;
interface IClassFactory2;
interface IDropTarget;
interface IProvideClassInfo;
interface IProvideClassInfo2;
interface IProvideMultipleClassInfo;
interface IOleClientSite;
interface IOleControl;
interface IOleControlSite;
interface IPropertyPage;
interface IPropertyPage2;
interface IPropertyPageSite;
interface IPropertyNotifySink;
interface ISpecifyPropertyPages;
interface IPersistMemory;
interface IPersistStreamInit;
interface IPersistPropertyBag;
interface ISimpleFrameSite;
interface IFont;
interface IPicture;
interface IFontEventsDisp;
interface IFontDisp;
interface IPictureDisp;
interface IAdviseSinkEx;
interface IOleInPlaceObjectWindowless;
interface IOleInPlaceSite;
interface IOleInPlaceSiteEx;
interface IOleInPlaceSiteWindowless;
interface IViewObject2;
interface IViewObjectEx;
interface IOleUndoUnit;
interface IOleParentUndoUnit;
interface IEnumOleUndoUnits;
interface IOleUndoManager;
interface IPointerInactive;
interface IObjectWithSite;
interface IErrorLog;
interface IPropertyBag;
interface IPerPropertyBrowsing;
interface IPropertyBag2;
interface IPersistPropertyBag2;
interface IQuickActivate;
cpp_quote("#endif")

cpp_quote("")
#if !defined (_DCOM_OA_REMOTING_) && !defined (_DCOM_OC_REMOTING_)
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[version (1.0), pointer_default (unique)]
interface IOleControlTypes {
  typedef [v1_enum] enum tagUASFLAGS {
    UAS_NORMAL = 0x00,
    UAS_BLOCKED = 0x01,
    UAS_NOPARENTENABLE = 0x02,
    UAS_MASK = 0x03
  } UASFLAGS;
  cpp_quote("")
  typedef [v1_enum] enum tagREADYSTATE {
    READYSTATE_UNINITIALIZED= 0,
    READYSTATE_LOADING = 1,
    READYSTATE_LOADED = 2,
    READYSTATE_INTERACTIVE = 3,
    READYSTATE_COMPLETE = 4
  } READYSTATE;
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (B196B287-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IEnumConnections : IUnknown {
  typedef IEnumConnections *PENUMCONNECTIONS;
  typedef IEnumConnections *LPENUMCONNECTIONS;
  typedef struct tagCONNECTDATA {
    IUnknown *pUnk;
    DWORD dwCookie;
  } CONNECTDATA;

cpp_quote("")
  typedef struct tagCONNECTDATA *PCONNECTDATA;
  typedef struct tagCONNECTDATA *LPCONNECTDATA;
cpp_quote("")
  [local] HRESULT Next ([in] ULONG cConnections,[out, size_is (cConnections), length_is (*pcFetched)]LPCONNECTDATA rgcd,[out] ULONG *pcFetched);
  [call_as (Next)] HRESULT RemoteNext ([in] ULONG cConnections,[out, size_is (cConnections), length_is (*pcFetched)]LPCONNECTDATA rgcd,[out] ULONG *pcFetched);
  HRESULT Skip ([in] ULONG cConnections);
  HRESULT Reset (void);
  HRESULT Clone ([out] IEnumConnections **ppEnum);
}

cpp_quote("")
[object, uuid (B196B286-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IConnectionPoint : IUnknown {
  typedef IConnectionPoint *PCONNECTIONPOINT;
  typedef IConnectionPoint *LPCONNECTIONPOINT;
cpp_quote("")
  HRESULT GetConnectionInterface ([out] IID *pIID);
  HRESULT GetConnectionPointContainer ([out] IConnectionPointContainer **ppCPC);
  HRESULT Advise ([in] IUnknown *pUnkSink,[out] DWORD *pdwCookie);
  HRESULT Unadvise ([in] DWORD dwCookie);
  HRESULT EnumConnections ([out] IEnumConnections **ppEnum);
}

cpp_quote("")
[object, uuid (B196B285-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IEnumConnectionPoints : IUnknown {
  typedef IEnumConnectionPoints *PENUMCONNECTIONPOINTS;
  typedef IEnumConnectionPoints *LPENUMCONNECTIONPOINTS;
cpp_quote("")
  [local] HRESULT Next ([in] ULONG cConnections,[out, size_is (cConnections), length_is (*pcFetched)]LPCONNECTIONPOINT *ppCP,[out] ULONG *pcFetched);
  [call_as (Next)] HRESULT RemoteNext ([in] ULONG cConnections,[out, size_is (cConnections), length_is (*pcFetched)]LPCONNECTIONPOINT *ppCP,[out] ULONG *pcFetched);
  HRESULT Skip ([in] ULONG cConnections);
  HRESULT Reset (void);
  HRESULT Clone ([out] IEnumConnectionPoints **ppEnum);
}

cpp_quote("")
[object, uuid (B196B284-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IConnectionPointContainer : IUnknown {
  typedef IConnectionPointContainer *PCONNECTIONPOINTCONTAINER;
  typedef IConnectionPointContainer *LPCONNECTIONPOINTCONTAINER;
cpp_quote("")
  HRESULT EnumConnectionPoints ([out] IEnumConnectionPoints **ppEnum);
  HRESULT FindConnectionPoint ([in] REFIID riid,[out] IConnectionPoint **ppCP);
}
cpp_quote("#endif")

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (B196B28F-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IClassFactory2 : IClassFactory {
  typedef IClassFactory2 *LPCLASSFACTORY2;
cpp_quote("")
  typedef struct tagLICINFO {
    LONG cbLicInfo;
    BOOL fRuntimeKeyAvail;
    BOOL fLicVerified;
  } LICINFO;

cpp_quote("")
  typedef struct tagLICINFO *LPLICINFO;
cpp_quote("")
  HRESULT GetLicInfo ([in, out] LICINFO *pLicInfo);
  HRESULT RequestLicKey ([in] DWORD dwReserved,[out] BSTR *pBstrKey);
  [local] HRESULT CreateInstanceLic ([in] IUnknown *pUnkOuter,[in] IUnknown *pUnkReserved,[in] REFIID riid,[in] BSTR bstrKey,[out, iid_is (riid)] PVOID *ppvObj);
  [call_as (CreateInstanceLic)] HRESULT RemoteCreateInstanceLic ([in] REFIID riid,[in] BSTR bstrKey,[out, iid_is (riid)] IUnknown **ppvObj);
}

cpp_quote("")
[object, uuid (B196B283-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IProvideClassInfo : IUnknown {
  typedef IProvideClassInfo *LPPROVIDECLASSINFO;
cpp_quote("")
  HRESULT GetClassInfo ([out] ITypeInfo **ppTI);
}

cpp_quote("")
[object, uuid (A6BC3AC0-DBAA-11ce-9de3-00aa004bb851), pointer_default (unique)]
interface IProvideClassInfo2 : IProvideClassInfo {
  typedef IProvideClassInfo2 *LPPROVIDECLASSINFO2;
cpp_quote("")
  typedef enum tagGUIDKIND {
    GUIDKIND_DEFAULT_SOURCE_DISP_IID = 1
  } GUIDKIND;
cpp_quote("")
  HRESULT GetGUID ([in] DWORD dwGuidKind,[out] GUID *pGUID);
}

cpp_quote("")
[object, uuid (A7ABA9C1-8983-11cf-8f20-00805f2cd064), pointer_default (unique)]
interface IProvideMultipleClassInfo : IProvideClassInfo2 {
  cpp_quote("#define MULTICLASSINFO_GETTYPEINFO 0x1")
  cpp_quote("#define MULTICLASSINFO_GETNUMRESERVEDDISPIDS 0x2")
  cpp_quote("#define MULTICLASSINFO_GETIIDPRIMARY 0x4")
  cpp_quote("#define MULTICLASSINFO_GETIIDSOURCE 0x8")
  cpp_quote("")
cpp_quote("#define TIFLAGS_EXTENDDISPATCHONLY 0x1")

cpp_quote("")
  typedef IProvideMultipleClassInfo *LPPROVIDEMULTIPLECLASSINFO;
cpp_quote("")
  HRESULT GetMultiTypeInfoCount ([out] ULONG *pcti);
  HRESULT GetInfoOfIndex ([in] ULONG iti,[in] DWORD dwFlags,[out] ITypeInfo **pptiCoClass,[out] DWORD *pdwTIFlags,[out] ULONG *pcdispidReserved,[out] IID *piidPrimary,[out] IID *piidSource);
}

cpp_quote("")
[object, uuid (B196B288-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IOleControl : IUnknown {
  typedef IOleControl *LPOLECONTROL;
cpp_quote("")
  typedef struct tagCONTROLINFO {
    ULONG cb;
    HACCEL hAccel;
    USHORT cAccel;
    DWORD dwFlags;
  } CONTROLINFO;
cpp_quote("")
  typedef struct tagCONTROLINFO *LPCONTROLINFO;
cpp_quote("")
  typedef enum tagCTRLINFO {
    CTRLINFO_EATS_RETURN = 1,
    CTRLINFO_EATS_ESCAPE = 2
  } CTRLINFO;
cpp_quote("")
  HRESULT GetControlInfo ([in, out] CONTROLINFO *pCI);
  HRESULT OnMnemonic ([in] MSG *pMsg);
  HRESULT OnAmbientPropertyChange ([in] DISPID dispID);
  HRESULT FreezeEvents ([in] BOOL bFreeze);
}

cpp_quote("")
[object, uuid (B196B289-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IOleControlSite : IUnknown {
  typedef IOleControlSite *LPOLECONTROLSITE;
cpp_quote("")
  typedef struct tagPOINTF {
    FLOAT x;
    FLOAT y;
  } POINTF;
cpp_quote("")
  typedef struct tagPOINTF *LPPOINTF;
cpp_quote("")
  typedef enum tagXFORMCOORDS {
    XFORMCOORDS_POSITION = 0x1,
    XFORMCOORDS_SIZE = 0x2,
    XFORMCOORDS_HIMETRICTOCONTAINER = 0x4,
    XFORMCOORDS_CONTAINERTOHIMETRIC = 0x8,
    XFORMCOORDS_EVENTCOMPAT = 0x10
  } XFORMCOORDS;
cpp_quote("")
  HRESULT OnControlInfoChanged (void);
  HRESULT LockInPlaceActive ([in] BOOL fLock);
  HRESULT GetExtendedControl ([out] IDispatch **ppDisp);
  HRESULT TransformCoords ([in, out] POINTL *pPtlHimetric,[in, out] POINTF *pPtfContainer,[in] DWORD dwFlags);
  HRESULT TranslateAccelerator ([in] MSG *pMsg,[in] DWORD grfModifiers);
  HRESULT OnFocus ([in] BOOL fGotFocus);
  HRESULT ShowPropertyFrame (void);
}

cpp_quote("")
[object, uuid (B196B28D-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IPropertyPage : IUnknown {
  typedef IPropertyPage *LPPROPERTYPAGE;
cpp_quote("")
  typedef struct tagPROPPAGEINFO {
    ULONG cb;
    LPOLESTR pszTitle;
    SIZE size;
    LPOLESTR pszDocString;
    LPOLESTR pszHelpFile;
    DWORD dwHelpContext;
  } PROPPAGEINFO;
cpp_quote("")
  typedef struct tagPROPPAGEINFO *LPPROPPAGEINFO;
cpp_quote("")
  HRESULT SetPageSite ([in] IPropertyPageSite *pPageSite);
  HRESULT Activate ([in] HWND hWndParent,[in] LPCRECT pRect,[in] BOOL bModal);
  HRESULT Deactivate (void);
  HRESULT GetPageInfo ([out] PROPPAGEINFO *pPageInfo);
  HRESULT SetObjects ([in] ULONG cObjects,[in, size_is (cObjects)] IUnknown **ppUnk);
  HRESULT Show ([in] UINT nCmdShow);
  HRESULT Move ([in] LPCRECT pRect);
  HRESULT IsPageDirty (void);
  HRESULT Apply (void);
  HRESULT Help ([in] LPCOLESTR pszHelpDir);
  HRESULT TranslateAccelerator ([in] MSG *pMsg);
}

cpp_quote("")
[object, uuid (01e44665-24ac-101b-84ed-08002b2ec713), pointer_default (unique)]
interface IPropertyPage2 : IPropertyPage {
  typedef IPropertyPage2 *LPPROPERTYPAGE2;
cpp_quote("")
  HRESULT EditProperty ([in] DISPID dispID);
}

cpp_quote("")
[object, uuid (B196B28C-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface IPropertyPageSite : IUnknown {
  typedef IPropertyPageSite *LPPROPERTYPAGESITE;
cpp_quote("")
  typedef enum tagPROPPAGESTATUS {
    PROPPAGESTATUS_DIRTY = 0x01,
    PROPPAGESTATUS_VALIDATE = 0x02,
    PROPPAGESTATUS_CLEAN = 0x04
  } PROPPAGESTATUS;
cpp_quote("")
  HRESULT OnStatusChange ([in] DWORD dwFlags);
  HRESULT GetLocaleID ([out] LCID *pLocaleID);
  HRESULT GetPageContainer ([out] IUnknown **ppUnk);
  HRESULT TranslateAccelerator ([in] MSG *pMsg);
}

cpp_quote("")
[object, uuid (9bfbbc02-EFF1-101a-84ed-00aa00341d07), pointer_default (unique)]
interface IPropertyNotifySink : IUnknown {
  typedef IPropertyNotifySink *LPPROPERTYNOTIFYSINK;
cpp_quote("")
  HRESULT OnChanged ([in] DISPID dispID);
  HRESULT OnRequestEdit ([in] DISPID dispID);
}

cpp_quote("")
[object, uuid (B196B28B-BAB4-101a-B69C-00aa00341d07), pointer_default (unique)]
interface ISpecifyPropertyPages : IUnknown {
  typedef ISpecifyPropertyPages *LPSPECIFYPROPERTYPAGES;
cpp_quote("")
  typedef struct tagCAUUID {
    ULONG cElems;
    [size_is (cElems)] GUID *pElems;
  } CAUUID;
cpp_quote("")
  typedef struct tagCAUUID *LPCAUUID;
cpp_quote("")
  HRESULT GetPages ([out] CAUUID *pPages);
}

cpp_quote("")
[object, uuid (BD1AE5E0-A6AE-11ce-BD37-504200c10000), pointer_default (unique)]
interface IPersistMemory : IPersist {
  typedef IPersistMemory *LPPERSISTMEMORY;
cpp_quote("")
  HRESULT IsDirty (void);
  [local] HRESULT Load ([in, size_is (cbSize)] LPVOID pMem,[in] ULONG cbSize);
  [call_as (Load)] HRESULT RemoteLoad ([in, size_is (cbSize)] BYTE *pMem,[in] ULONG cbSize);
  [local] HRESULT Save ([out, size_is (cbSize)] LPVOID pMem,[in] BOOL fClearDirty,[in] ULONG cbSize);
  [call_as (Save)] HRESULT RemoteSave ([out, size_is (cbSize)] BYTE *pMem,[in] BOOL fClearDirty,[in] ULONG cbSize);
  HRESULT GetSizeMax ([out] ULONG *pCbSize);
  HRESULT InitNew (void);
}

cpp_quote("")
[object, uuid (7fd52380-4e07-101b-AE2D-08002b2ec713), pointer_default (unique)]
interface IPersistStreamInit : IPersist {
  typedef IPersistStreamInit *LPPERSISTSTREAMINIT;
cpp_quote("")
  HRESULT IsDirty (void);
  HRESULT Load ([in] LPSTREAM pStm);
  HRESULT Save ([in] LPSTREAM pStm,[in] BOOL fClearDirty);
  HRESULT GetSizeMax ([out] ULARGE_INTEGER *pCbSize);
  HRESULT InitNew (void);
}

cpp_quote("")
[object, uuid (37d84f60-42cb-11ce-8135-00aa004bb851), pointer_default (unique)]
interface IPersistPropertyBag : IPersist {
  typedef IPersistPropertyBag *LPPERSISTPROPERTYBAG;
cpp_quote("")
  HRESULT InitNew (void);
  HRESULT Load ([in] IPropertyBag *pPropBag,[in, unique] IErrorLog *pErrorLog);
  HRESULT Save ([in] IPropertyBag *pPropBag,[in] BOOL fClearDirty,[in] BOOL fSaveAllProperties);
}

cpp_quote("")
[object, uuid (742b0e01-14e6-101b-914e-00aa00300cab), pointer_default (unique)]
interface ISimpleFrameSite : IUnknown {
  typedef ISimpleFrameSite *LPSIMPLEFRAMESITE;
cpp_quote("")
  HRESULT PreMessageFilter ([in] HWND hWnd,[in] UINT msg,[in] WPARAM wp,[in] LPARAM lp,[out] LRESULT *plResult,[out] DWORD *pdwCookie);
  HRESULT PostMessageFilter ([in] HWND hWnd,[in] UINT msg,[in] WPARAM wp,[in] LPARAM lp,[out] LRESULT *plResult,[in] DWORD dwCookie);
}

cpp_quote("")
[object, uuid (BEF6E002-A874-101a-8bba-00aa00300cab), pointer_default (unique)]
interface IFont : IUnknown {
  typedef IFont *LPFONT;
cpp_quote("")
  cpp_quote("#ifndef OLE2ANSI")
  typedef TEXTMETRICW TEXTMETRICOLE;
  cpp_quote("#else")
  cpp_quote("typedef TEXTMETRIC TEXTMETRICOLE;")
  cpp_quote("#endif")

cpp_quote("")
  typedef TEXTMETRICOLE *LPTEXTMETRICOLE;
cpp_quote("")
  HRESULT get_Name ([out] BSTR *pName);
  HRESULT put_Name ([in] BSTR name);
  HRESULT get_Size ([out] CY *pSize);
  HRESULT put_Size ([in] CY size);
  HRESULT get_Bold ([out] BOOL *pBold);
  HRESULT put_Bold ([in] BOOL bold);
  HRESULT get_Italic ([out] BOOL *pItalic);
  HRESULT put_Italic ([in] BOOL italic);
  HRESULT get_Underline ([out] BOOL *pUnderline);
  HRESULT put_Underline ([in] BOOL underline);
  HRESULT get_Strikethrough ([out] BOOL *pStrikethrough);
  HRESULT put_Strikethrough ([in] BOOL strikethrough);
  HRESULT get_Weight ([out] SHORT *pWeight);
  HRESULT put_Weight ([in] SHORT weight);
  HRESULT get_Charset ([out] SHORT *pCharset);
  HRESULT put_Charset ([in] SHORT charset);
  HRESULT get_hFont ([out] HFONT *phFont);
  HRESULT Clone ([out] IFont **ppFont);
  HRESULT IsEqual ([in] IFont *pFontOther);
  HRESULT SetRatio ([in] LONG cyLogical,[in] LONG cyHimetric);
  HRESULT QueryTextMetrics ([out] TEXTMETRICOLE *pTM);
  HRESULT AddRefHfont ([in] HFONT hFont);
  HRESULT ReleaseHfont ([in] HFONT hFont);
  HRESULT SetHdc ([in] HDC hDC);
}

cpp_quote("")
[object, uuid (7bf80980-BF32-101a-8bbb-00aa00300cab), pointer_default (unique)]
interface IPicture : IUnknown {
  typedef IPicture *LPPICTURE;
cpp_quote("")
  typedef enum tagPictureAttributes {
    PICTURE_SCALABLE = 0x1,
    PICTURE_TRANSPARENT = 0x2
  } PICTUREATTRIBUTES;
cpp_quote("")
  typedef [uuid (66504313-BE0F-101a-8bbb-00aa00300cab), public] UINT OLE_HANDLE;
  typedef [uuid (66504306-BE0F-101a-8bbb-00aa00300cab), hidden] LONG OLE_XPOS_HIMETRIC;
  typedef [uuid (66504307-BE0F-101a-8bbb-00aa00300cab), hidden] LONG OLE_YPOS_HIMETRIC;
  typedef [uuid (66504308-BE0F-101a-8bbb-00aa00300cab), hidden] LONG OLE_XSIZE_HIMETRIC;
  typedef [uuid (66504309-BE0F-101a-8bbb-00aa00300cab), hidden] LONG OLE_YSIZE_HIMETRIC;
cpp_quote("")
  HRESULT get_Handle ([out] OLE_HANDLE *pHandle);
  HRESULT get_hPal ([out] OLE_HANDLE *phPal);
  HRESULT get_Type ([out] SHORT *pType);
  HRESULT get_Width ([out] OLE_XSIZE_HIMETRIC *pWidth);
  HRESULT get_Height ([out] OLE_YSIZE_HIMETRIC *pHeight);
  HRESULT Render ([in] HDC hDC,[in] LONG x,[in] LONG y,[in] LONG cx,[in] LONG cy,[in] OLE_XPOS_HIMETRIC xSrc,[in] OLE_YPOS_HIMETRIC ySrc,[in] OLE_XSIZE_HIMETRIC cxSrc,[in] OLE_YSIZE_HIMETRIC cySrc,[in] LPCRECT pRcWBounds);
  HRESULT set_hPal ([in] OLE_HANDLE hPal);
  HRESULT get_CurDC ([out] HDC *phDC);
  HRESULT SelectPicture ([in] HDC hDCIn,[out] HDC *phDCOut,[out] OLE_HANDLE *phBmpOut);
  HRESULT get_KeepOriginalFormat ([out] BOOL *pKeep);
  HRESULT put_KeepOriginalFormat ([in] BOOL keep);
  HRESULT PictureChanged (void);
  HRESULT SaveAsFile ([in] LPSTREAM pStream,[in] BOOL fSaveMemCopy,[out] LONG *pCbSize);
  HRESULT get_Attributes ([out] DWORD *pDwAttr);
}

cpp_quote("")
[object, uuid (F5185DD8-2012-4b0b-AAD9-F052C6BD482B), pointer_default (unique)]
interface IPicture2 : IUnknown {
  typedef IPicture2 *LPPICTURE2;
  typedef UINT_PTR HHANDLE;
cpp_quote("")
  HRESULT get_Handle ([out] HHANDLE *pHandle);
  HRESULT get_hPal ([out] HHANDLE *phPal);
  HRESULT get_Type ([out] SHORT *pType);
  HRESULT get_Width ([out] OLE_XSIZE_HIMETRIC *pWidth);
  HRESULT get_Height ([out] OLE_YSIZE_HIMETRIC *pHeight);
  HRESULT Render ([in] HDC hDC,[in] LONG x,[in] LONG y,[in] LONG cx,[in] LONG cy,[in] OLE_XPOS_HIMETRIC xSrc,[in] OLE_YPOS_HIMETRIC ySrc,[in] OLE_XSIZE_HIMETRIC cxSrc,[in] OLE_YSIZE_HIMETRIC cySrc,[in] LPCRECT pRcWBounds);
  HRESULT set_hPal ([in] HHANDLE hPal);
  HRESULT get_CurDC ([out] HDC *phDC);
  HRESULT SelectPicture ([in] HDC hDCIn,[out] HDC *phDCOut,[out] HHANDLE *phBmpOut);
  HRESULT get_KeepOriginalFormat ([out] BOOL *pKeep);
  HRESULT put_KeepOriginalFormat ([in] BOOL keep);
  HRESULT PictureChanged (void);
  HRESULT SaveAsFile ([in] LPSTREAM pStream,[in] BOOL fSaveMemCopy,[out] LONG *pCbSize);
  HRESULT get_Attributes ([out] DWORD *pDwAttr);
}

cpp_quote("")
[object, uuid (4ef6100a-AF88-11d0-9846-00c04fc29993), pointer_default (unique)]
interface IFontEventsDisp : IDispatch {
  typedef IFontEventsDisp *LPFONTEVENTS;
}

cpp_quote("")
[object, uuid (BEF6E003-A874-101a-8bba-00aa00300cab), pointer_default (unique)]
interface IFontDisp : IDispatch {
  typedef IFontDisp *LPFONTDISP;
}

cpp_quote("")
[object, uuid (7bf80981-BF32-101a-8bbb-00aa00300cab), pointer_default (unique)]
interface IPictureDisp : IDispatch {
  typedef IPictureDisp *LPPICTUREDISP;
}

cpp_quote("")
[object, pointer_default (unique), uuid (1c2056cc-5ef4-101b-8bc8-00aa003e3b29)]
interface IOleInPlaceObjectWindowless : IOleInPlaceObject {
  typedef IOleInPlaceObjectWindowless *LPOLEINPLACEOBJECTWINDOWLESS;
cpp_quote("")
  HRESULT OnWindowMessage ([in] UINT msg,[in] WPARAM wParam,[in] LPARAM lParam,[out] LRESULT *plResult);
  HRESULT GetDropTarget ([out] IDropTarget **ppDropTarget);
}

cpp_quote("")
[object, pointer_default (unique), uuid (9c2cad80-3424-11cf-B670-00aa004cd6d8)]
interface IOleInPlaceSiteEx : IOleInPlaceSite {
  typedef IOleInPlaceSiteEx *LPOLEINPLACESITEEX;
cpp_quote("")
  typedef [v1_enum] enum tagACTIVATEFLAGS {
    ACTIVATE_WINDOWLESS = 1,
  } ACTIVATEFLAGS;
cpp_quote("")
  HRESULT OnInPlaceActivateEx ([out] BOOL *pfNoRedraw,[in] DWORD dwFlags);
  HRESULT OnInPlaceDeactivateEx ([in] BOOL fNoRedraw);
  HRESULT RequestUIActivate (void);
}

cpp_quote("")
[object, pointer_default (unique), uuid (922eada0-3424-11cf-B670-00aa004cd6d8)]
interface IOleInPlaceSiteWindowless : IOleInPlaceSiteEx {
  typedef IOleInPlaceSiteWindowless *LPOLEINPLACESITEWINDOWLESS;
cpp_quote("")
  typedef [v1_enum] enum tagOLEDCFLAGS {
    OLEDC_NODRAW = 0x01,
    OLEDC_PAINTBKGND = 0x02,
    OLEDC_OFFSCREEN = 0x04
  } OLEDCFLAGS;
cpp_quote("")
  HRESULT CanWindowlessActivate (void);
  HRESULT GetCapture (void);
  HRESULT SetCapture ([in] BOOL fCapture);
  HRESULT GetFocus (void);
  HRESULT SetFocus ([in] BOOL fFocus);
  HRESULT GetDC ([in, unique] LPCRECT pRect,[in] DWORD grfFlags,[out] HDC *phDC);
  HRESULT ReleaseDC ([in] HDC hDC);
  HRESULT InvalidateRect ([in, unique] LPCRECT pRect,[in] BOOL fErase);
  HRESULT InvalidateRgn ([in] HRGN hRGN,[in] BOOL fErase);
  HRESULT ScrollRect ([in] INT dx,[in] INT dy,[in] LPCRECT pRectScroll,[in] LPCRECT pRectClip);
  HRESULT AdjustRect ([in, out] LPRECT prc);
  HRESULT OnDefWindowMessage ([in] UINT msg,[in] WPARAM wParam,[in] LPARAM lParam,[out] LRESULT *plResult);
}

cpp_quote("")
[object, pointer_default (unique), uuid (3af24292-0c96-11ce-A0CF-00aa00600ab8)]
interface IViewObjectEx : IViewObject2 {
  typedef IViewObjectEx *LPVIEWOBJECTEX;
cpp_quote("")
  typedef [v1_enum] enum tagVIEWSTATUS {
    VIEWSTATUS_OPAQUE = 1,
    VIEWSTATUS_SOLIDBKGND = 2,
    VIEWSTATUS_DVASPECTOPAQUE = 4,
    VIEWSTATUS_DVASPECTTRANSPARENT = 8,
    VIEWSTATUS_SURFACE = 16,
    VIEWSTATUS_3DSURFACE = 32
  } VIEWSTATUS;
cpp_quote("")
  typedef [v1_enum] enum tagHITRESULT {
    HITRESULT_OUTSIDE = 0,
    HITRESULT_TRANSPARENT = 1,
    HITRESULT_CLOSE = 2,
    HITRESULT_HIT = 3
  } HITRESULT;
cpp_quote("")
  typedef [v1_enum] enum tagDVASPECT2 {
    DVASPECT_OPAQUE = 16,
    DVASPECT_TRANSPARENT = 32,
  } DVASPECT2;
cpp_quote("")
  typedef struct tagExtentInfo {
    ULONG cb;
    DWORD dwExtentMode;
    SIZEL sizelProposed;
  } DVEXTENTINFO;
cpp_quote("")
  typedef [v1_enum] enum tagExtentMode {
    DVEXTENT_CONTENT,
    DVEXTENT_INTEGRAL
  } DVEXTENTMODE;
cpp_quote("")
  typedef [v1_enum] enum tagAspectInfoFlag {
    DVASPECTINFOFLAG_CANOPTIMIZE = 1
  } DVASPECTINFOFLAG;
cpp_quote("")
  typedef struct tagAspectInfo {
    ULONG cb;
    DWORD dwFlags;
  } DVASPECTINFO;
cpp_quote("")
  HRESULT GetRect ([in] DWORD dwAspect,[out] LPRECTL pRect);
  HRESULT GetViewStatus ([out] DWORD *pdwStatus);
  HRESULT QueryHitPoint ([in] DWORD dwAspect,[in] LPCRECT pRectBounds,[in] POINT ptlLoc,[in] LONG lCloseHint,[out] DWORD *pHitResult);
  HRESULT QueryHitRect ([in] DWORD dwAspect,[in] LPCRECT pRectBounds,[in] LPCRECT pRectLoc,[in] LONG lCloseHint,[out] DWORD *pHitResult);
  HRESULT GetNaturalExtent ([in] DWORD dwAspect,[in] LONG lindex,[in] DVTARGETDEVICE *ptd,[in] HDC hicTargetDev,[in] DVEXTENTINFO *pExtentInfo,[out] LPSIZEL pSizel);
}

cpp_quote("")
[object, pointer_default (unique), uuid (894ad3b0-EF97-11ce-9bc9-00aa00608e01)]
interface IOleUndoUnit : IUnknown {
  typedef IOleUndoUnit *LPOLEUNDOUNIT;
cpp_quote("")
  HRESULT Do ([in] IOleUndoManager *pUndoManager);
  HRESULT GetDescription ([out] BSTR *pBstr);
  HRESULT GetUnitType ([out] CLSID *pClsid,[out] LONG *plID);
  HRESULT OnNextAdd (void);
}

cpp_quote("")
[object, pointer_default (unique), uuid (A1FAF330-EF97-11ce-9bc9-00aa00608e01)]
interface IOleParentUndoUnit : IOleUndoUnit {
  typedef IOleParentUndoUnit *LPOLEPARENTUNDOUNIT;
cpp_quote("")
  HRESULT Open ([in] IOleParentUndoUnit *pPUU);
  HRESULT Close ([in] IOleParentUndoUnit *pPUU,[in] BOOL fCommit);
  HRESULT Add ([in] IOleUndoUnit *pUU);
  HRESULT FindUnit ([in] IOleUndoUnit *pUU);
  HRESULT GetParentState ([out] DWORD *pdwState);
}

cpp_quote("")
[object, pointer_default (unique), uuid (B3E7C340-EF97-11ce-9bc9-00aa00608e01)]
interface IEnumOleUndoUnits : IUnknown {
  typedef IEnumOleUndoUnits *LPENUMOLEUNDOUNITS;
cpp_quote("")
  [local] HRESULT Next ([in] ULONG cElt,[out, size_is (cElt), length_is (*pcEltFetched)]IOleUndoUnit **rgElt,[out] ULONG *pcEltFetched);
  [call_as (Next)] HRESULT RemoteNext ([in] ULONG cElt,[out, size_is (cElt), length_is (*pcEltFetched)]IOleUndoUnit **rgElt,[out] ULONG *pcEltFetched);
  HRESULT Skip ([in] ULONG cElt);
  HRESULT Reset (void);
  HRESULT Clone ([out] IEnumOleUndoUnits **ppEnum);
}

cpp_quote("")
[object, pointer_default (unique), uuid (D001F200-EF97-11ce-9bc9-00aa00608e01)]
interface IOleUndoManager : IUnknown {
cpp_quote("#define SID_SOleUndoManager IID_IOleUndoManager")
cpp_quote("")
  typedef IOleUndoManager *LPOLEUNDOMANAGER;
cpp_quote("")
  HRESULT Open ([in] IOleParentUndoUnit *pPUU);
  HRESULT Close ([in] IOleParentUndoUnit *pPUU,[in] BOOL fCommit);
  HRESULT Add ([in] IOleUndoUnit *pUU);
  HRESULT GetOpenParentState ([out] DWORD *pdwState);
  HRESULT DiscardFrom ([in] IOleUndoUnit *pUU);
  HRESULT UndoTo ([in] IOleUndoUnit *pUU);
  HRESULT RedoTo ([in] IOleUndoUnit *pUU);
  HRESULT EnumUndoable ([out] IEnumOleUndoUnits **ppEnum);
  HRESULT EnumRedoable ([out] IEnumOleUndoUnits **ppEnum);
  HRESULT GetLastUndoDescription ([out] BSTR *pBstr);
  HRESULT GetLastRedoDescription ([out] BSTR *pBstr);
  HRESULT Enable ([in] BOOL fEnable);
}

cpp_quote("")
[object, pointer_default (unique), uuid (55980ba0-35aa-11cf-B671-00aa004cd6d8)]
interface IPointerInactive : IUnknown {
  typedef IPointerInactive *LPPOINTERINACTIVE;
cpp_quote("")
  typedef [v1_enum] enum tagPOINTERINACTIVE {
    POINTERINACTIVE_ACTIVATEONENTRY = 1,
    POINTERINACTIVE_DEACTIVATEONLEAVE = 2,
    POINTERINACTIVE_ACTIVATEONDRAG = 4
  } POINTERINACTIVE;
cpp_quote("")
  HRESULT GetActivationPolicy ([out] DWORD *pdwPolicy);
  HRESULT OnInactiveMouseMove ([in] LPCRECT pRectBounds,[in] LONG x,[in] LONG y,[in] DWORD grfKeyState);
  HRESULT OnInactiveSetCursor ([in] LPCRECT pRectBounds,[in] LONG x,[in] LONG y,[in] DWORD dwMouseMsg,[in] BOOL fSetAlways);
}

cpp_quote("")
[object, uuid (FC4801A3-2ba9-11cf-A229-00aa003d7352), pointer_default (unique)]
interface IObjectWithSite : IUnknown {
  typedef IObjectWithSite *LPOBJECTWITHSITE;
cpp_quote("")
  HRESULT SetSite ([in] IUnknown *pUnkSite);
  HRESULT GetSite ([in] REFIID riid,[out, iid_is (riid)] void **ppvSite);
}
cpp_quote("#endif")
#endif

#if !defined (_DCOM_OC_REMOTING_)
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (376bd3aa-3845-101b-84ed-08002b2ec713), pointer_default (unique)]
interface IPerPropertyBrowsing : IUnknown {
  typedef IPerPropertyBrowsing *LPPERPROPERTYBROWSING;
cpp_quote("")
  typedef struct tagCALPOLESTR {
    ULONG cElems;
    [size_is (cElems)] LPOLESTR *pElems;
  } CALPOLESTR;
cpp_quote("")
  typedef struct tagCALPOLESTR *LPCALPOLESTR;
cpp_quote("")
  typedef struct tagCADWORD {
    ULONG cElems;
    [size_is (cElems)] DWORD *pElems;
  } CADWORD;
cpp_quote("")
  typedef struct tagCADWORD *LPCADWORD;
cpp_quote("")
  HRESULT GetDisplayString ([in] DISPID dispID,[out] BSTR *pBstr);
  HRESULT MapPropertyToPage ([in] DISPID dispID,[out] CLSID *pClsid);
  HRESULT GetPredefinedStrings ([in] DISPID dispID,[out] CALPOLESTR *pCaStringsOut,[out] CADWORD *pCaCookiesOut);
  HRESULT GetPredefinedValue ([in] DISPID dispID,[in] DWORD dwCookie,[out] VARIANT *pVarOut);
}
cpp_quote("#endif")
#endif

#if !defined (_NON_DCOM_REMOTING_) && !defined (_DCOM_OC_REMOTING_)
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, uuid (22f55882-280b-11d0-A8A9-00a0c90c2004), pointer_default (unique)]
interface IPropertyBag2 : IUnknown {
  typedef IPropertyBag2 *LPPROPERTYBAG2;
cpp_quote("")
  typedef [v1_enum] enum tagPROPBAG2_TYPE {
    PROPBAG2_TYPE_UNDEFINED = 0,
    PROPBAG2_TYPE_DATA = 1,
    PROPBAG2_TYPE_URL = 2,
    PROPBAG2_TYPE_OBJECT = 3,
    PROPBAG2_TYPE_STREAM = 4,
    PROPBAG2_TYPE_STORAGE = 5,
    PROPBAG2_TYPE_MONIKER = 6
  } PROPBAG2_TYPE;
cpp_quote("")
  typedef struct tagPROPBAG2 {
    DWORD dwType;
    VARTYPE vt;
    CLIPFORMAT cfType;
    DWORD dwHint;
    LPOLESTR pstrName;
    CLSID clsid;
  } PROPBAG2;
cpp_quote("")
  HRESULT Read ([in] ULONG cProperties,[in, size_is (cProperties)] PROPBAG2 *pPropBag,[in, unique] IErrorLog *pErrLog,[out, size_is (cProperties)] VARIANT *pvarValue,[in, out, unique, size_is (cProperties)] HRESULT *phrError);
  HRESULT Write ([in] ULONG cProperties,[in, size_is (cProperties)] PROPBAG2 *pPropBag,[in, size_is (cProperties)] VARIANT *pvarValue);
  HRESULT CountProperties ([out] ULONG *pcProperties);
  HRESULT GetPropertyInfo ([in] ULONG iProperty,[in] ULONG cProperties,[out, size_is (cProperties)] PROPBAG2 *pPropBag,[out] ULONG *pcProperties);
  HRESULT LoadObject ([in] LPCOLESTR pstrName,[in] DWORD dwHint,[in] IUnknown *pUnkObject,[in, unique] IErrorLog *pErrLog);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, uuid (22f55881-280b-11d0-A8A9-00a0c90c2004), pointer_default (unique)]
interface IPersistPropertyBag2 : IPersist {
  typedef IPersistPropertyBag2 *LPPERSISTPROPERTYBAG2;
cpp_quote("")
  HRESULT InitNew (void);
  HRESULT Load ([in] IPropertyBag2 *pPropBag,[in, unique] IErrorLog *pErrLog);
  HRESULT Save ([in] IPropertyBag2 *pPropBag,[in] BOOL fClearDirty,[in] BOOL fSaveAllProperties);
  HRESULT IsDirty (void);
}
cpp_quote("#endif")
#endif

#if !defined (_NON_DCOM_REMOTING_) && !defined (_DCOM_OA_REMOTING_)
#if !defined (_DCOM_OC_REMOTING_)
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
[object, pointer_default (unique), uuid (3af24290-0c96-11ce-A0CF-00aa00600ab8)]
interface IAdviseSinkEx : IAdviseSink {
  typedef IAdviseSinkEx *LPADVISESINKEX;
cpp_quote("")
  [local] void OnViewStatusChange ([in] DWORD dwViewStatus);
  [call_as (OnViewStatusChange)] HRESULT RemoteOnViewStatusChange ([in] DWORD dwViewStatus);
}
cpp_quote("#endif")
#endif

cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)")
[object, pointer_default (unique), uuid (CF51ED10-62fe-11cf-BF86-00a0c9034836)]
interface IQuickActivate : IUnknown {
  typedef IQuickActivate *LPQUICKACTIVATE;
cpp_quote("")
  typedef [v1_enum] enum tagQACONTAINERFLAGS {
    QACONTAINER_SHOWHATCHING = 0x0001,
    QACONTAINER_SHOWGRABHANDLES = 0x0002,
    QACONTAINER_USERMODE = 0x0004,
    QACONTAINER_DISPLAYASDEFAULT = 0x0008,
    QACONTAINER_UIDEAD = 0x0010,
    QACONTAINER_AUTOCLIP = 0x0020,
    QACONTAINER_MESSAGEREFLECT = 0x0040,
    QACONTAINER_SUPPORTSMNEMONICS = 0x0080
  } QACONTAINERFLAGS;
cpp_quote("")
  typedef [uuid (66504301-BE0F-101a-8bbb-00aa00300cab), public] DWORD OLE_COLOR;
cpp_quote("")
  typedef struct tagQACONTAINER {
    ULONG cbSize;
    IOleClientSite *pClientSite;
    IAdviseSinkEx *pAdviseSink;
    IPropertyNotifySink *pPropertyNotifySink;
    IUnknown *pUnkEventSink;
    DWORD dwAmbientFlags;
    OLE_COLOR colorFore;
    OLE_COLOR colorBack;
    IFont *pFont;
    IOleUndoManager *pUndoMgr;
    DWORD dwAppearance;
    LONG lcid;
    HPALETTE hpal;
    IBindHost *pBindHost;
    IOleControlSite *pOleControlSite;
    IServiceProvider *pServiceProvider;
  } QACONTAINER;
cpp_quote("")
  typedef struct tagQACONTROL {
    ULONG cbSize;
    DWORD dwMiscStatus;
    DWORD dwViewStatus;
    DWORD dwEventCookie;
    DWORD dwPropNotifyCookie;
    DWORD dwPointerActivationPolicy;
  } QACONTROL;
cpp_quote("")
  [local] HRESULT QuickActivate ([in] QACONTAINER *pQaContainer,[in, out] QACONTROL *pQaControl);
  [call_as (QuickActivate)] HRESULT RemoteQuickActivate ([in] QACONTAINER *pQaContainer,[out] QACONTROL *pQaControl);
  HRESULT SetContentExtent ([in] LPSIZEL pSizel);
  HRESULT GetContentExtent ([out] LPSIZEL pSizel);
}
cpp_quote("#endif")
#endif
