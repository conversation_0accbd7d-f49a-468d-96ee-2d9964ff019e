/*
 * Background Intelligent Transfer Service (BITS) 1.5 interface
 *
 * Copyright 2008 Google (<PERSON>)
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 *
 */

#ifndef DO_NO_IMPORTS
import "bits.idl";
#endif

[
    uuid(54b50739-686f-45eb-9dff-d6a9a0faa9af),
    odl
]
interface IBackgroundCopyJob2 : IBackgroundCopyJob
{
    HRESULT SetNotifyCmdLine([unique] LPCWSTR prog, [unique] <PERSON><PERSON><PERSON><PERSON> params);
    HRESULT GetNotifyCmdLine([out] LPWSTR *prog, [out] LPWSTR *params);

    typedef struct _BG_JOB_REPLY_PROGRESS
    {
        UINT64 BytesTotal;
        UINT64 BytesTransferred;
    } BG_JOB_REPLY_PROGRESS;

    HRESULT GetReplyProgress([in, out] BG_JOB_REPLY_PROGRESS *progress);
    HRESULT GetReplyData([out, size_is( , (unsigned long) *pLength)] byte **pBuffer,
                         [in, out, unique] UINT64 *pLength);
    HRESULT SetReplyFileName([unique] LPCWSTR filename);
    HRESULT GetReplyFileName([out] LPWSTR *pFilename);

    typedef enum
    {
        BG_AUTH_TARGET_SERVER = 1,
        BG_AUTH_TARGET_PROXY
    } BG_AUTH_TARGET;

    typedef enum
    {
        BG_AUTH_SCHEME_BASIC = 1,
        BG_AUTH_SCHEME_DIGEST,
        BG_AUTH_SCHEME_NTLM,
        BG_AUTH_SCHEME_NEGOTIATE,
        BG_AUTH_SCHEME_PASSPORT
    } BG_AUTH_SCHEME;

    typedef struct
    {
        LPWSTR UserName;
        LPWSTR Password;
    } BG_BASIC_CREDENTIALS;
    typedef BG_BASIC_CREDENTIALS *PBG_BASIC_CREDENTIALS;

    typedef [switch_type(BG_AUTH_SCHEME)] union
    {
        [case(BG_AUTH_SCHEME_BASIC, BG_AUTH_SCHEME_DIGEST, BG_AUTH_SCHEME_NTLM,
              BG_AUTH_SCHEME_NEGOTIATE, BG_AUTH_SCHEME_PASSPORT)]
        BG_BASIC_CREDENTIALS Basic;
        [default]
        ;
    } BG_AUTH_CREDENTIALS_UNION;

    typedef struct
    {
        BG_AUTH_TARGET Target;
        BG_AUTH_SCHEME Scheme;
        [switch_is(Scheme)] BG_AUTH_CREDENTIALS_UNION Credentials;
    } BG_AUTH_CREDENTIALS;
    typedef BG_AUTH_CREDENTIALS *PBG_AUTH_CREDENTIALS;

    HRESULT SetCredentials(BG_AUTH_CREDENTIALS *cred);
    HRESULT RemoveCredentials(BG_AUTH_TARGET target, BG_AUTH_SCHEME scheme);
}

[
    uuid(ea9319ea-c628-480f-8331-768fac397e4e),
    lcid(0x0000),
    version(1.0)
]
library BackgroundCopyManager1_5
{
    [
        uuid(f087771f-d74f-4c1a-bb8a-e16aca9124ea)
    ]
    coclass BackgroundCopyManager1_5
    {
        [default] interface IBackgroundCopyManager;
    };

    interface IBackgroundCopyCallback;
    interface IBackgroundCopyJob2;
}

cpp_quote("#include \"bits2_0.h\"")
