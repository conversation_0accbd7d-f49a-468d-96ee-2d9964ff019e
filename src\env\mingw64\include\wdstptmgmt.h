/*** Autogenerated by WIDL 10.8 from include/wdstptmgmt.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wdstptmgmt_h__
#define __wdstptmgmt_h__

/* Forward declarations */

#ifndef __IWdsTransportCacheable_FWD_DEFINED__
#define __IWdsTransportCacheable_FWD_DEFINED__
typedef interface IWdsTransportCacheable IWdsTransportCacheable;
#ifdef __cplusplus
interface IWdsTransportCacheable;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportCollection_FWD_DEFINED__
#define __IWdsTransportCollection_FWD_DEFINED__
typedef interface IWdsTransportCollection IWdsTransportCollection;
#ifdef __cplusplus
interface IWdsTransportCollection;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportConfigurationManager_FWD_DEFINED__
#define __IWdsTransportConfigurationManager_FWD_DEFINED__
typedef interface IWdsTransportConfigurationManager IWdsTransportConfigurationManager;
#ifdef __cplusplus
interface IWdsTransportConfigurationManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportDiagnosticsPolicy_FWD_DEFINED__
#define __IWdsTransportDiagnosticsPolicy_FWD_DEFINED__
typedef interface IWdsTransportDiagnosticsPolicy IWdsTransportDiagnosticsPolicy;
#ifdef __cplusplus
interface IWdsTransportDiagnosticsPolicy;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportConfigurationManager2_FWD_DEFINED__
#define __IWdsTransportConfigurationManager2_FWD_DEFINED__
typedef interface IWdsTransportConfigurationManager2 IWdsTransportConfigurationManager2;
#ifdef __cplusplus
interface IWdsTransportConfigurationManager2;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportManager_FWD_DEFINED__
#define __IWdsTransportManager_FWD_DEFINED__
typedef interface IWdsTransportManager IWdsTransportManager;
#ifdef __cplusplus
interface IWdsTransportManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespace_FWD_DEFINED__
#define __IWdsTransportNamespace_FWD_DEFINED__
typedef interface IWdsTransportNamespace IWdsTransportNamespace;
#ifdef __cplusplus
interface IWdsTransportNamespace;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceAutoCast_FWD_DEFINED__
#define __IWdsTransportNamespaceAutoCast_FWD_DEFINED__
typedef interface IWdsTransportNamespaceAutoCast IWdsTransportNamespaceAutoCast;
#ifdef __cplusplus
interface IWdsTransportNamespaceAutoCast;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceScheduledCast_FWD_DEFINED__
#define __IWdsTransportNamespaceScheduledCast_FWD_DEFINED__
typedef interface IWdsTransportNamespaceScheduledCast IWdsTransportNamespaceScheduledCast;
#ifdef __cplusplus
interface IWdsTransportNamespaceScheduledCast;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceScheduledCastManualStart_FWD_DEFINED__
#define __IWdsTransportNamespaceScheduledCastManualStart_FWD_DEFINED__
typedef interface IWdsTransportNamespaceScheduledCastManualStart IWdsTransportNamespaceScheduledCastManualStart;
#ifdef __cplusplus
interface IWdsTransportNamespaceScheduledCastManualStart;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceScheduledCastAutoStart_FWD_DEFINED__
#define __IWdsTransportNamespaceScheduledCastAutoStart_FWD_DEFINED__
typedef interface IWdsTransportNamespaceScheduledCastAutoStart IWdsTransportNamespaceScheduledCastAutoStart;
#ifdef __cplusplus
interface IWdsTransportNamespaceScheduledCastAutoStart;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceManager_FWD_DEFINED__
#define __IWdsTransportNamespaceManager_FWD_DEFINED__
typedef interface IWdsTransportNamespaceManager IWdsTransportNamespaceManager;
#ifdef __cplusplus
interface IWdsTransportNamespaceManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportServer_FWD_DEFINED__
#define __IWdsTransportServer_FWD_DEFINED__
typedef interface IWdsTransportServer IWdsTransportServer;
#ifdef __cplusplus
interface IWdsTransportServer;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportServer2_FWD_DEFINED__
#define __IWdsTransportServer2_FWD_DEFINED__
typedef interface IWdsTransportServer2 IWdsTransportServer2;
#ifdef __cplusplus
interface IWdsTransportServer2;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportServicePolicy_FWD_DEFINED__
#define __IWdsTransportServicePolicy_FWD_DEFINED__
typedef interface IWdsTransportServicePolicy IWdsTransportServicePolicy;
#ifdef __cplusplus
interface IWdsTransportServicePolicy;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportServicePolicy2_FWD_DEFINED__
#define __IWdsTransportServicePolicy2_FWD_DEFINED__
typedef interface IWdsTransportServicePolicy2 IWdsTransportServicePolicy2;
#ifdef __cplusplus
interface IWdsTransportServicePolicy2;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportSetupManager_FWD_DEFINED__
#define __IWdsTransportSetupManager_FWD_DEFINED__
typedef interface IWdsTransportSetupManager IWdsTransportSetupManager;
#ifdef __cplusplus
interface IWdsTransportSetupManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportSetupManager2_FWD_DEFINED__
#define __IWdsTransportSetupManager2_FWD_DEFINED__
typedef interface IWdsTransportSetupManager2 IWdsTransportSetupManager2;
#ifdef __cplusplus
interface IWdsTransportSetupManager2;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportTftpManager_FWD_DEFINED__
#define __IWdsTransportTftpManager_FWD_DEFINED__
typedef interface IWdsTransportTftpManager IWdsTransportTftpManager;
#ifdef __cplusplus
interface IWdsTransportTftpManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportMulticastSessionPolicy_FWD_DEFINED__
#define __IWdsTransportMulticastSessionPolicy_FWD_DEFINED__
typedef interface IWdsTransportMulticastSessionPolicy IWdsTransportMulticastSessionPolicy;
#ifdef __cplusplus
interface IWdsTransportMulticastSessionPolicy;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportContent_FWD_DEFINED__
#define __IWdsTransportContent_FWD_DEFINED__
typedef interface IWdsTransportContent IWdsTransportContent;
#ifdef __cplusplus
interface IWdsTransportContent;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportSession_FWD_DEFINED__
#define __IWdsTransportSession_FWD_DEFINED__
typedef interface IWdsTransportSession IWdsTransportSession;
#ifdef __cplusplus
interface IWdsTransportSession;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportClient_FWD_DEFINED__
#define __IWdsTransportClient_FWD_DEFINED__
typedef interface IWdsTransportClient IWdsTransportClient;
#ifdef __cplusplus
interface IWdsTransportClient;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportTftpClient_FWD_DEFINED__
#define __IWdsTransportTftpClient_FWD_DEFINED__
typedef interface IWdsTransportTftpClient IWdsTransportTftpClient;
#ifdef __cplusplus
interface IWdsTransportTftpClient;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportContentProvider_FWD_DEFINED__
#define __IWdsTransportContentProvider_FWD_DEFINED__
typedef interface IWdsTransportContentProvider IWdsTransportContentProvider;
#ifdef __cplusplus
interface IWdsTransportContentProvider;
#endif /* __cplusplus */
#endif

#ifndef __WdsTransportCacheable_FWD_DEFINED__
#define __WdsTransportCacheable_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportCacheable WdsTransportCacheable;
#else
typedef struct WdsTransportCacheable WdsTransportCacheable;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportCacheable_FWD_DEFINED__ */

#ifndef __WdsTransportCollection_FWD_DEFINED__
#define __WdsTransportCollection_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportCollection WdsTransportCollection;
#else
typedef struct WdsTransportCollection WdsTransportCollection;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportCollection_FWD_DEFINED__ */

#ifndef __WdsTransportManager_FWD_DEFINED__
#define __WdsTransportManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportManager WdsTransportManager;
#else
typedef struct WdsTransportManager WdsTransportManager;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportManager_FWD_DEFINED__ */

#ifndef __WdsTransportServer_FWD_DEFINED__
#define __WdsTransportServer_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportServer WdsTransportServer;
#else
typedef struct WdsTransportServer WdsTransportServer;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportServer_FWD_DEFINED__ */

#ifndef __WdsTransportSetupManager_FWD_DEFINED__
#define __WdsTransportSetupManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportSetupManager WdsTransportSetupManager;
#else
typedef struct WdsTransportSetupManager WdsTransportSetupManager;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportSetupManager_FWD_DEFINED__ */

#ifndef __WdsTransportConfigurationManager_FWD_DEFINED__
#define __WdsTransportConfigurationManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportConfigurationManager WdsTransportConfigurationManager;
#else
typedef struct WdsTransportConfigurationManager WdsTransportConfigurationManager;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportConfigurationManager_FWD_DEFINED__ */

#ifndef __WdsTransportNamespaceManager_FWD_DEFINED__
#define __WdsTransportNamespaceManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportNamespaceManager WdsTransportNamespaceManager;
#else
typedef struct WdsTransportNamespaceManager WdsTransportNamespaceManager;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportNamespaceManager_FWD_DEFINED__ */

#ifndef __WdsTransportServicePolicy_FWD_DEFINED__
#define __WdsTransportServicePolicy_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportServicePolicy WdsTransportServicePolicy;
#else
typedef struct WdsTransportServicePolicy WdsTransportServicePolicy;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportServicePolicy_FWD_DEFINED__ */

#ifndef __WdsTransportDiagnosticsPolicy_FWD_DEFINED__
#define __WdsTransportDiagnosticsPolicy_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportDiagnosticsPolicy WdsTransportDiagnosticsPolicy;
#else
typedef struct WdsTransportDiagnosticsPolicy WdsTransportDiagnosticsPolicy;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportDiagnosticsPolicy_FWD_DEFINED__ */

#ifndef __WdsTransportMulticastSessionPolicy_FWD_DEFINED__
#define __WdsTransportMulticastSessionPolicy_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportMulticastSessionPolicy WdsTransportMulticastSessionPolicy;
#else
typedef struct WdsTransportMulticastSessionPolicy WdsTransportMulticastSessionPolicy;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportMulticastSessionPolicy_FWD_DEFINED__ */

#ifndef __WdsTransportNamespace_FWD_DEFINED__
#define __WdsTransportNamespace_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportNamespace WdsTransportNamespace;
#else
typedef struct WdsTransportNamespace WdsTransportNamespace;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportNamespace_FWD_DEFINED__ */

#ifndef __WdsTransportNamespaceAutoCast_FWD_DEFINED__
#define __WdsTransportNamespaceAutoCast_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportNamespaceAutoCast WdsTransportNamespaceAutoCast;
#else
typedef struct WdsTransportNamespaceAutoCast WdsTransportNamespaceAutoCast;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportNamespaceAutoCast_FWD_DEFINED__ */

#ifndef __WdsTransportNamespaceScheduledCast_FWD_DEFINED__
#define __WdsTransportNamespaceScheduledCast_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportNamespaceScheduledCast WdsTransportNamespaceScheduledCast;
#else
typedef struct WdsTransportNamespaceScheduledCast WdsTransportNamespaceScheduledCast;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportNamespaceScheduledCast_FWD_DEFINED__ */

#ifndef __WdsTransportNamespaceScheduledCastManualStart_FWD_DEFINED__
#define __WdsTransportNamespaceScheduledCastManualStart_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportNamespaceScheduledCastManualStart WdsTransportNamespaceScheduledCastManualStart;
#else
typedef struct WdsTransportNamespaceScheduledCastManualStart WdsTransportNamespaceScheduledCastManualStart;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportNamespaceScheduledCastManualStart_FWD_DEFINED__ */

#ifndef __WdsTransportNamespaceScheduledCastAutoStart_FWD_DEFINED__
#define __WdsTransportNamespaceScheduledCastAutoStart_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportNamespaceScheduledCastAutoStart WdsTransportNamespaceScheduledCastAutoStart;
#else
typedef struct WdsTransportNamespaceScheduledCastAutoStart WdsTransportNamespaceScheduledCastAutoStart;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportNamespaceScheduledCastAutoStart_FWD_DEFINED__ */

#ifndef __WdsTransportContent_FWD_DEFINED__
#define __WdsTransportContent_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportContent WdsTransportContent;
#else
typedef struct WdsTransportContent WdsTransportContent;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportContent_FWD_DEFINED__ */

#ifndef __WdsTransportSession_FWD_DEFINED__
#define __WdsTransportSession_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportSession WdsTransportSession;
#else
typedef struct WdsTransportSession WdsTransportSession;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportSession_FWD_DEFINED__ */

#ifndef __WdsTransportClient_FWD_DEFINED__
#define __WdsTransportClient_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportClient WdsTransportClient;
#else
typedef struct WdsTransportClient WdsTransportClient;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportClient_FWD_DEFINED__ */

#ifndef __WdsTransportTftpClient_FWD_DEFINED__
#define __WdsTransportTftpClient_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportTftpClient WdsTransportTftpClient;
#else
typedef struct WdsTransportTftpClient WdsTransportTftpClient;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportTftpClient_FWD_DEFINED__ */

#ifndef __WdsTransportTftpManager_FWD_DEFINED__
#define __WdsTransportTftpManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportTftpManager WdsTransportTftpManager;
#else
typedef struct WdsTransportTftpManager WdsTransportTftpManager;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportTftpManager_FWD_DEFINED__ */

#ifndef __WdsTransportContentProvider_FWD_DEFINED__
#define __WdsTransportContentProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class WdsTransportContentProvider WdsTransportContentProvider;
#else
typedef struct WdsTransportContentProvider WdsTransportContentProvider;
#endif /* defined __cplusplus */
#endif /* defined __WdsTransportContentProvider_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)


#ifndef __IWdsTransportCacheable_FWD_DEFINED__
#define __IWdsTransportCacheable_FWD_DEFINED__
typedef interface IWdsTransportCacheable IWdsTransportCacheable;
#ifdef __cplusplus
interface IWdsTransportCacheable;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportClient_FWD_DEFINED__
#define __IWdsTransportClient_FWD_DEFINED__
typedef interface IWdsTransportClient IWdsTransportClient;
#ifdef __cplusplus
interface IWdsTransportClient;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportCollection_FWD_DEFINED__
#define __IWdsTransportCollection_FWD_DEFINED__
typedef interface IWdsTransportCollection IWdsTransportCollection;
#ifdef __cplusplus
interface IWdsTransportCollection;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportConfigurationManager_FWD_DEFINED__
#define __IWdsTransportConfigurationManager_FWD_DEFINED__
typedef interface IWdsTransportConfigurationManager IWdsTransportConfigurationManager;
#ifdef __cplusplus
interface IWdsTransportConfigurationManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportConfigurationManager2_FWD_DEFINED__
#define __IWdsTransportConfigurationManager2_FWD_DEFINED__
typedef interface IWdsTransportConfigurationManager2 IWdsTransportConfigurationManager2;
#ifdef __cplusplus
interface IWdsTransportConfigurationManager2;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportContent_FWD_DEFINED__
#define __IWdsTransportContent_FWD_DEFINED__
typedef interface IWdsTransportContent IWdsTransportContent;
#ifdef __cplusplus
interface IWdsTransportContent;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportContentProvider_FWD_DEFINED__
#define __IWdsTransportContentProvider_FWD_DEFINED__
typedef interface IWdsTransportContentProvider IWdsTransportContentProvider;
#ifdef __cplusplus
interface IWdsTransportContentProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportDiagnosticsPolicy_FWD_DEFINED__
#define __IWdsTransportDiagnosticsPolicy_FWD_DEFINED__
typedef interface IWdsTransportDiagnosticsPolicy IWdsTransportDiagnosticsPolicy;
#ifdef __cplusplus
interface IWdsTransportDiagnosticsPolicy;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportManager_FWD_DEFINED__
#define __IWdsTransportManager_FWD_DEFINED__
typedef interface IWdsTransportManager IWdsTransportManager;
#ifdef __cplusplus
interface IWdsTransportManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespace_FWD_DEFINED__
#define __IWdsTransportNamespace_FWD_DEFINED__
typedef interface IWdsTransportNamespace IWdsTransportNamespace;
#ifdef __cplusplus
interface IWdsTransportNamespace;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceAutoCast_FWD_DEFINED__
#define __IWdsTransportNamespaceAutoCast_FWD_DEFINED__
typedef interface IWdsTransportNamespaceAutoCast IWdsTransportNamespaceAutoCast;
#ifdef __cplusplus
interface IWdsTransportNamespaceAutoCast;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceManager_FWD_DEFINED__
#define __IWdsTransportNamespaceManager_FWD_DEFINED__
typedef interface IWdsTransportNamespaceManager IWdsTransportNamespaceManager;
#ifdef __cplusplus
interface IWdsTransportNamespaceManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceScheduledCast_FWD_DEFINED__
#define __IWdsTransportNamespaceScheduledCast_FWD_DEFINED__
typedef interface IWdsTransportNamespaceScheduledCast IWdsTransportNamespaceScheduledCast;
#ifdef __cplusplus
interface IWdsTransportNamespaceScheduledCast;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceScheduledCastAutoStart_FWD_DEFINED__
#define __IWdsTransportNamespaceScheduledCastAutoStart_FWD_DEFINED__
typedef interface IWdsTransportNamespaceScheduledCastAutoStart IWdsTransportNamespaceScheduledCastAutoStart;
#ifdef __cplusplus
interface IWdsTransportNamespaceScheduledCastAutoStart;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportNamespaceScheduledCastManualStart_FWD_DEFINED__
#define __IWdsTransportNamespaceScheduledCastManualStart_FWD_DEFINED__
typedef interface IWdsTransportNamespaceScheduledCastManualStart IWdsTransportNamespaceScheduledCastManualStart;
#ifdef __cplusplus
interface IWdsTransportNamespaceScheduledCastManualStart;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportServer_FWD_DEFINED__
#define __IWdsTransportServer_FWD_DEFINED__
typedef interface IWdsTransportServer IWdsTransportServer;
#ifdef __cplusplus
interface IWdsTransportServer;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportServer2_FWD_DEFINED__
#define __IWdsTransportServer2_FWD_DEFINED__
typedef interface IWdsTransportServer2 IWdsTransportServer2;
#ifdef __cplusplus
interface IWdsTransportServer2;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportServicePolicy_FWD_DEFINED__
#define __IWdsTransportServicePolicy_FWD_DEFINED__
typedef interface IWdsTransportServicePolicy IWdsTransportServicePolicy;
#ifdef __cplusplus
interface IWdsTransportServicePolicy;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportServicePolicy2_FWD_DEFINED__
#define __IWdsTransportServicePolicy2_FWD_DEFINED__
typedef interface IWdsTransportServicePolicy2 IWdsTransportServicePolicy2;
#ifdef __cplusplus
interface IWdsTransportServicePolicy2;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportSetupManager_FWD_DEFINED__
#define __IWdsTransportSetupManager_FWD_DEFINED__
typedef interface IWdsTransportSetupManager IWdsTransportSetupManager;
#ifdef __cplusplus
interface IWdsTransportSetupManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportSetupManager2_FWD_DEFINED__
#define __IWdsTransportSetupManager2_FWD_DEFINED__
typedef interface IWdsTransportSetupManager2 IWdsTransportSetupManager2;
#ifdef __cplusplus
interface IWdsTransportSetupManager2;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportTftpManager_FWD_DEFINED__
#define __IWdsTransportTftpManager_FWD_DEFINED__
typedef interface IWdsTransportTftpManager IWdsTransportTftpManager;
#ifdef __cplusplus
interface IWdsTransportTftpManager;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportMulticastSessionPolicy_FWD_DEFINED__
#define __IWdsTransportMulticastSessionPolicy_FWD_DEFINED__
typedef interface IWdsTransportMulticastSessionPolicy IWdsTransportMulticastSessionPolicy;
#ifdef __cplusplus
interface IWdsTransportMulticastSessionPolicy;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportSession_FWD_DEFINED__
#define __IWdsTransportSession_FWD_DEFINED__
typedef interface IWdsTransportSession IWdsTransportSession;
#ifdef __cplusplus
interface IWdsTransportSession;
#endif /* __cplusplus */
#endif

#ifndef __IWdsTransportTftpClient_FWD_DEFINED__
#define __IWdsTransportTftpClient_FWD_DEFINED__
typedef interface IWdsTransportTftpClient IWdsTransportTftpClient;
#ifdef __cplusplus
interface IWdsTransportTftpClient;
#endif /* __cplusplus */
#endif


typedef enum __WIDL_wdstptmgmt_generated_name_00000020 {
    WdsTptProtocolUnicast = 0x1,
    WdsTptProtocolMulticast = 0x2
} WDSTRANSPORT_PROTOCOL_FLAGS;

typedef enum __WIDL_wdstptmgmt_generated_name_00000021 {
    WdsTptNamespaceTypeUnknown = 0,
    WdsTptNamespaceTypeAutoCast = 1,
    WdsTptNamespaceTypeScheduledCastManualStart = 2,
    WdsTptNamespaceTypeScheduledCastAutoStart = 3
} WDSTRANSPORT_NAMESPACE_TYPE;

typedef enum __WIDL_wdstptmgmt_generated_name_00000022 {
    WdsTptDisconnectUnknown = 0,
    WdsTptDisconnectFallback = 1,
    WdsTptDisconnectAbort = 2
} WDSTRANSPORT_DISCONNECT_TYPE;

typedef enum __WIDL_wdstptmgmt_generated_name_00000023 {
    WdsTptSlowClientHandlingUnknown = 0,
    WdsTptSlowClientHandlingNone = 1,
    WdsTptSlowClientHandlingAutoDisconnect = 2,
    WdsTptSlowClientHandlingMultistream = 3
} WDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE;

typedef enum __WIDL_wdstptmgmt_generated_name_00000024 {
    WdsTptUdpPortPolicyDynamic = 0,
    WdsTptUdpPortPolicyFixed = 1
} WDSTRANSPORT_UDP_PORT_POLICY;

typedef enum __WIDL_wdstptmgmt_generated_name_00000025 {
    WdsTptTftpCapMaximumBlockSize = 0x1,
    WdsTptTftpCapVariableWindow = 0x2
} WDSTRANSPORT_TFTP_CAPABILITY;

typedef enum __WIDL_wdstptmgmt_generated_name_00000026 {
    WdsTptServiceNotifyUnknown = 0,
    WdsTptServiceNotifyReadSettings = 1
} WDSTRANSPORT_SERVICE_NOTIFICATION;

typedef enum __WIDL_wdstptmgmt_generated_name_00000027 {
    WdsTptIpAddressUnknown = 0,
    WdsTptIpAddressIpv4 = 1,
    WdsTptIpAddressIpv6 = 2
} WDSTRANSPORT_IP_ADDRESS_TYPE;

typedef enum __WIDL_wdstptmgmt_generated_name_00000028 {
    WdsTptIpAddressSourceUnknown = 0,
    WdsTptIpAddressSourceDhcp = 1,
    WdsTptIpAddressSourceRange = 2
} WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE;

typedef enum __WIDL_wdstptmgmt_generated_name_00000029 {
    WdsTptNetworkProfileUnknown = 0,
    WdsTptNetworkProfileCustom = 1,
    WdsTptNetworkProfile10Mbps = 2,
    WdsTptNetworkProfile100Mbps = 3,
    WdsTptNetworkProfile1Gbps = 4
} WDSTRANSPORT_NETWORK_PROFILE_TYPE;

typedef enum __WIDL_wdstptmgmt_generated_name_0000002A {
    WdsTptFeatureAdminPack = 0x1,
    WdsTptFeatureTransportServer = 0x2,
    WdsTptFeatureDeploymentServer = 0x4
} WDSTRANSPORT_FEATURE_FLAGS;

#define WDSTRANSPORT_FEATURE_FLAGS_ALL (WdsTptFeatureAdminPack | WdsTptFeatureTransportServer | WdsTptFeatureDeploymentServer)

typedef enum __WIDL_wdstptmgmt_generated_name_0000002B {
    WdsTptDiagnosticsComponentPxe = 0x1,
    WdsTptDiagnosticsComponentTftp = 0x2,
    WdsTptDiagnosticsComponentImageServer = 0x4,
    WdsTptDiagnosticsComponentMulticast = 0x8
} WDSTRANSPORT_DIAGNOSTICS_COMPONENT_FLAGS;

#define WDSTRANSPORT_DIAGNOSTICS_COMPONENT_FLAGS_ALL (WdsTptDiagnosticsComponentPxe | WdsTptDiagnosticsComponentTftp | WdsTptDiagnosticsComponentImageServer | WdsTptDiagnosticsComponentMulticast)
#define WDSTRANSPORT_RESOURCE_UTILIZATION_UNKNOWN  0xff

typedef WDSTRANSPORT_PROTOCOL_FLAGS *PWDSTRANSPORT_PROTOCOL_FLAGS;
typedef WDSTRANSPORT_NAMESPACE_TYPE *PWDSTRANSPORT_NAMESPACE_TYPE;
typedef WDSTRANSPORT_DISCONNECT_TYPE *PWDSTRANSPORT_DISCONNECT_TYPE;
typedef WDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE *PWDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE;
typedef WDSTRANSPORT_UDP_PORT_POLICY *PWDSTRANSPORT_UDP_PORT_POLICY;
typedef WDSTRANSPORT_TFTP_CAPABILITY *PWDSTRANSPORT_TFTP_CAPABILITY;
typedef WDSTRANSPORT_SERVICE_NOTIFICATION *PWDSTRANSPORT_SERVICE_NOTIFICATION;
typedef WDSTRANSPORT_IP_ADDRESS_TYPE *PWDSTRANSPORT_IP_ADDRESS_TYPE;
typedef WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE *PWDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE;
typedef WDSTRANSPORT_NETWORK_PROFILE_TYPE *PWDSTRANSPORT_NETWORK_PROFILE_TYPE;
typedef WDSTRANSPORT_FEATURE_FLAGS *PWDSTRANSPORT_FEATURE_FLAGS;
typedef WDSTRANSPORT_DIAGNOSTICS_COMPONENT_FLAGS *PWDSTRANSPORT_DIAGNOSTICS_COMPONENT_FLAGS;

/*****************************************************************************
 * IWdsTransportCacheable interface
 */
#ifndef __IWdsTransportCacheable_INTERFACE_DEFINED__
#define __IWdsTransportCacheable_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportCacheable, 0x46ad894b, 0x0bab, 0x47dc, 0x84,0xb2, 0x7b,0x55,0x3f,0x1d,0x8f,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("46ad894b-0bab-47dc-84b2-7b553f1d8f80")
IWdsTransportCacheable : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Dirty(
        VARIANT_BOOL *pbDirty) = 0;

    virtual HRESULT STDMETHODCALLTYPE Discard(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Refresh(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Commit(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportCacheable, 0x46ad894b, 0x0bab, 0x47dc, 0x84,0xb2, 0x7b,0x55,0x3f,0x1d,0x8f,0x80)
#endif
#else
typedef struct IWdsTransportCacheableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportCacheable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportCacheable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportCacheable *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportCacheable *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportCacheable *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportCacheable *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportCacheable *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportCacheable methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Dirty)(
        IWdsTransportCacheable *This,
        VARIANT_BOOL *pbDirty);

    HRESULT (STDMETHODCALLTYPE *Discard)(
        IWdsTransportCacheable *This);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportCacheable *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWdsTransportCacheable *This);

    END_INTERFACE
} IWdsTransportCacheableVtbl;

interface IWdsTransportCacheable {
    CONST_VTBL IWdsTransportCacheableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportCacheable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportCacheable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportCacheable_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportCacheable_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportCacheable_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportCacheable_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportCacheable_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportCacheable methods ***/
#define IWdsTransportCacheable_get_Dirty(This,pbDirty) (This)->lpVtbl->get_Dirty(This,pbDirty)
#define IWdsTransportCacheable_Discard(This) (This)->lpVtbl->Discard(This)
#define IWdsTransportCacheable_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportCacheable_Commit(This) (This)->lpVtbl->Commit(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportCacheable_QueryInterface(IWdsTransportCacheable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportCacheable_AddRef(IWdsTransportCacheable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportCacheable_Release(IWdsTransportCacheable* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportCacheable_GetTypeInfoCount(IWdsTransportCacheable* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportCacheable_GetTypeInfo(IWdsTransportCacheable* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportCacheable_GetIDsOfNames(IWdsTransportCacheable* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportCacheable_Invoke(IWdsTransportCacheable* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportCacheable methods ***/
static inline HRESULT IWdsTransportCacheable_get_Dirty(IWdsTransportCacheable* This,VARIANT_BOOL *pbDirty) {
    return This->lpVtbl->get_Dirty(This,pbDirty);
}
static inline HRESULT IWdsTransportCacheable_Discard(IWdsTransportCacheable* This) {
    return This->lpVtbl->Discard(This);
}
static inline HRESULT IWdsTransportCacheable_Refresh(IWdsTransportCacheable* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportCacheable_Commit(IWdsTransportCacheable* This) {
    return This->lpVtbl->Commit(This);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportCacheable_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportCollection interface
 */
#ifndef __IWdsTransportCollection_INTERFACE_DEFINED__
#define __IWdsTransportCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportCollection, 0xb8ba4b1a, 0x2ff4, 0x43ab, 0x99,0x6c, 0xb2,0xb1,0x0a,0x91,0xa6,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b8ba4b1a-2ff4-43ab-996c-b2b10a91a6eb")
IWdsTransportCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        ULONG *pulCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        ULONG ulIndex,
        IDispatch **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **ppVal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportCollection, 0xb8ba4b1a, 0x2ff4, 0x43ab, 0x99,0x6c, 0xb2,0xb1,0x0a,0x91,0xa6,0xeb)
#endif
#else
typedef struct IWdsTransportCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IWdsTransportCollection *This,
        ULONG *pulCount);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IWdsTransportCollection *This,
        ULONG ulIndex,
        IDispatch **ppVal);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IWdsTransportCollection *This,
        IUnknown **ppVal);

    END_INTERFACE
} IWdsTransportCollectionVtbl;

interface IWdsTransportCollection {
    CONST_VTBL IWdsTransportCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportCollection methods ***/
#define IWdsTransportCollection_get_Count(This,pulCount) (This)->lpVtbl->get_Count(This,pulCount)
#define IWdsTransportCollection_get_Item(This,ulIndex,ppVal) (This)->lpVtbl->get_Item(This,ulIndex,ppVal)
#define IWdsTransportCollection_get__NewEnum(This,ppVal) (This)->lpVtbl->get__NewEnum(This,ppVal)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportCollection_QueryInterface(IWdsTransportCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportCollection_AddRef(IWdsTransportCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportCollection_Release(IWdsTransportCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportCollection_GetTypeInfoCount(IWdsTransportCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportCollection_GetTypeInfo(IWdsTransportCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportCollection_GetIDsOfNames(IWdsTransportCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportCollection_Invoke(IWdsTransportCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportCollection methods ***/
static inline HRESULT IWdsTransportCollection_get_Count(IWdsTransportCollection* This,ULONG *pulCount) {
    return This->lpVtbl->get_Count(This,pulCount);
}
static inline HRESULT IWdsTransportCollection_get_Item(IWdsTransportCollection* This,ULONG ulIndex,IDispatch **ppVal) {
    return This->lpVtbl->get_Item(This,ulIndex,ppVal);
}
static inline HRESULT IWdsTransportCollection_get__NewEnum(IWdsTransportCollection* This,IUnknown **ppVal) {
    return This->lpVtbl->get__NewEnum(This,ppVal);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportCollection_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportConfigurationManager interface
 */
#ifndef __IWdsTransportConfigurationManager_INTERFACE_DEFINED__
#define __IWdsTransportConfigurationManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportConfigurationManager, 0x84cc4779, 0x42dd, 0x4792, 0x89,0x1e, 0x13,0x21,0xd6,0xd7,0x4b,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("84cc4779-42dd-4792-891e-1321d6d74b44")
IWdsTransportConfigurationManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ServicePolicy(
        IWdsTransportServicePolicy **ppWdsTransportServicePolicy) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DiagnosticsPolicy(
        IWdsTransportDiagnosticsPolicy **ppWdsTransportDiagnosticsPolicy) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WdsTransportServicesRunning(
        VARIANT_BOOL bRealtimeStatus,
        VARIANT_BOOL *pbServicesRunning) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableWdsTransportServices(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableWdsTransportServices(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartWdsTransportServices(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopWdsTransportServices(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RestartWdsTransportServices(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyWdsTransportServices(
        WDSTRANSPORT_SERVICE_NOTIFICATION ServiceNotification) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportConfigurationManager, 0x84cc4779, 0x42dd, 0x4792, 0x89,0x1e, 0x13,0x21,0xd6,0xd7,0x4b,0x44)
#endif
#else
typedef struct IWdsTransportConfigurationManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportConfigurationManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportConfigurationManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportConfigurationManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportConfigurationManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportConfigurationManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportConfigurationManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportConfigurationManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportConfigurationManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ServicePolicy)(
        IWdsTransportConfigurationManager *This,
        IWdsTransportServicePolicy **ppWdsTransportServicePolicy);

    HRESULT (STDMETHODCALLTYPE *get_DiagnosticsPolicy)(
        IWdsTransportConfigurationManager *This,
        IWdsTransportDiagnosticsPolicy **ppWdsTransportDiagnosticsPolicy);

    HRESULT (STDMETHODCALLTYPE *get_WdsTransportServicesRunning)(
        IWdsTransportConfigurationManager *This,
        VARIANT_BOOL bRealtimeStatus,
        VARIANT_BOOL *pbServicesRunning);

    HRESULT (STDMETHODCALLTYPE *EnableWdsTransportServices)(
        IWdsTransportConfigurationManager *This);

    HRESULT (STDMETHODCALLTYPE *DisableWdsTransportServices)(
        IWdsTransportConfigurationManager *This);

    HRESULT (STDMETHODCALLTYPE *StartWdsTransportServices)(
        IWdsTransportConfigurationManager *This);

    HRESULT (STDMETHODCALLTYPE *StopWdsTransportServices)(
        IWdsTransportConfigurationManager *This);

    HRESULT (STDMETHODCALLTYPE *RestartWdsTransportServices)(
        IWdsTransportConfigurationManager *This);

    HRESULT (STDMETHODCALLTYPE *NotifyWdsTransportServices)(
        IWdsTransportConfigurationManager *This,
        WDSTRANSPORT_SERVICE_NOTIFICATION ServiceNotification);

    END_INTERFACE
} IWdsTransportConfigurationManagerVtbl;

interface IWdsTransportConfigurationManager {
    CONST_VTBL IWdsTransportConfigurationManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportConfigurationManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportConfigurationManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportConfigurationManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportConfigurationManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportConfigurationManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportConfigurationManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportConfigurationManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportConfigurationManager methods ***/
#define IWdsTransportConfigurationManager_get_ServicePolicy(This,ppWdsTransportServicePolicy) (This)->lpVtbl->get_ServicePolicy(This,ppWdsTransportServicePolicy)
#define IWdsTransportConfigurationManager_get_DiagnosticsPolicy(This,ppWdsTransportDiagnosticsPolicy) (This)->lpVtbl->get_DiagnosticsPolicy(This,ppWdsTransportDiagnosticsPolicy)
#define IWdsTransportConfigurationManager_get_WdsTransportServicesRunning(This,bRealtimeStatus,pbServicesRunning) (This)->lpVtbl->get_WdsTransportServicesRunning(This,bRealtimeStatus,pbServicesRunning)
#define IWdsTransportConfigurationManager_EnableWdsTransportServices(This) (This)->lpVtbl->EnableWdsTransportServices(This)
#define IWdsTransportConfigurationManager_DisableWdsTransportServices(This) (This)->lpVtbl->DisableWdsTransportServices(This)
#define IWdsTransportConfigurationManager_StartWdsTransportServices(This) (This)->lpVtbl->StartWdsTransportServices(This)
#define IWdsTransportConfigurationManager_StopWdsTransportServices(This) (This)->lpVtbl->StopWdsTransportServices(This)
#define IWdsTransportConfigurationManager_RestartWdsTransportServices(This) (This)->lpVtbl->RestartWdsTransportServices(This)
#define IWdsTransportConfigurationManager_NotifyWdsTransportServices(This,ServiceNotification) (This)->lpVtbl->NotifyWdsTransportServices(This,ServiceNotification)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportConfigurationManager_QueryInterface(IWdsTransportConfigurationManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportConfigurationManager_AddRef(IWdsTransportConfigurationManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportConfigurationManager_Release(IWdsTransportConfigurationManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportConfigurationManager_GetTypeInfoCount(IWdsTransportConfigurationManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportConfigurationManager_GetTypeInfo(IWdsTransportConfigurationManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportConfigurationManager_GetIDsOfNames(IWdsTransportConfigurationManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportConfigurationManager_Invoke(IWdsTransportConfigurationManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportConfigurationManager methods ***/
static inline HRESULT IWdsTransportConfigurationManager_get_ServicePolicy(IWdsTransportConfigurationManager* This,IWdsTransportServicePolicy **ppWdsTransportServicePolicy) {
    return This->lpVtbl->get_ServicePolicy(This,ppWdsTransportServicePolicy);
}
static inline HRESULT IWdsTransportConfigurationManager_get_DiagnosticsPolicy(IWdsTransportConfigurationManager* This,IWdsTransportDiagnosticsPolicy **ppWdsTransportDiagnosticsPolicy) {
    return This->lpVtbl->get_DiagnosticsPolicy(This,ppWdsTransportDiagnosticsPolicy);
}
static inline HRESULT IWdsTransportConfigurationManager_get_WdsTransportServicesRunning(IWdsTransportConfigurationManager* This,VARIANT_BOOL bRealtimeStatus,VARIANT_BOOL *pbServicesRunning) {
    return This->lpVtbl->get_WdsTransportServicesRunning(This,bRealtimeStatus,pbServicesRunning);
}
static inline HRESULT IWdsTransportConfigurationManager_EnableWdsTransportServices(IWdsTransportConfigurationManager* This) {
    return This->lpVtbl->EnableWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager_DisableWdsTransportServices(IWdsTransportConfigurationManager* This) {
    return This->lpVtbl->DisableWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager_StartWdsTransportServices(IWdsTransportConfigurationManager* This) {
    return This->lpVtbl->StartWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager_StopWdsTransportServices(IWdsTransportConfigurationManager* This) {
    return This->lpVtbl->StopWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager_RestartWdsTransportServices(IWdsTransportConfigurationManager* This) {
    return This->lpVtbl->RestartWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager_NotifyWdsTransportServices(IWdsTransportConfigurationManager* This,WDSTRANSPORT_SERVICE_NOTIFICATION ServiceNotification) {
    return This->lpVtbl->NotifyWdsTransportServices(This,ServiceNotification);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportConfigurationManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportDiagnosticsPolicy interface
 */
#ifndef __IWdsTransportDiagnosticsPolicy_INTERFACE_DEFINED__
#define __IWdsTransportDiagnosticsPolicy_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportDiagnosticsPolicy, 0x13b33efc, 0x7856, 0x4f61, 0x9a,0x59, 0x8d,0xe6,0x7b,0x6b,0x87,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("13b33efc-7856-4f61-9a59-8de67b6b87b6")
IWdsTransportDiagnosticsPolicy : public IWdsTransportCacheable
{
    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL bEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Components(
        ULONG *pulComponents) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Components(
        ULONG ulComponents) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportDiagnosticsPolicy, 0x13b33efc, 0x7856, 0x4f61, 0x9a,0x59, 0x8d,0xe6,0x7b,0x6b,0x87,0xb6)
#endif
#else
typedef struct IWdsTransportDiagnosticsPolicyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportDiagnosticsPolicy *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportDiagnosticsPolicy *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportDiagnosticsPolicy *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportDiagnosticsPolicy *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportDiagnosticsPolicy *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportDiagnosticsPolicy *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportDiagnosticsPolicy *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportCacheable methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Dirty)(
        IWdsTransportDiagnosticsPolicy *This,
        VARIANT_BOOL *pbDirty);

    HRESULT (STDMETHODCALLTYPE *Discard)(
        IWdsTransportDiagnosticsPolicy *This);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportDiagnosticsPolicy *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWdsTransportDiagnosticsPolicy *This);

    /*** IWdsTransportDiagnosticsPolicy methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IWdsTransportDiagnosticsPolicy *This,
        VARIANT_BOOL *pbEnabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IWdsTransportDiagnosticsPolicy *This,
        VARIANT_BOOL bEnabled);

    HRESULT (STDMETHODCALLTYPE *get_Components)(
        IWdsTransportDiagnosticsPolicy *This,
        ULONG *pulComponents);

    HRESULT (STDMETHODCALLTYPE *put_Components)(
        IWdsTransportDiagnosticsPolicy *This,
        ULONG ulComponents);

    END_INTERFACE
} IWdsTransportDiagnosticsPolicyVtbl;

interface IWdsTransportDiagnosticsPolicy {
    CONST_VTBL IWdsTransportDiagnosticsPolicyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportDiagnosticsPolicy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportDiagnosticsPolicy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportDiagnosticsPolicy_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportDiagnosticsPolicy_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportDiagnosticsPolicy_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportDiagnosticsPolicy_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportDiagnosticsPolicy_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportCacheable methods ***/
#define IWdsTransportDiagnosticsPolicy_get_Dirty(This,pbDirty) (This)->lpVtbl->get_Dirty(This,pbDirty)
#define IWdsTransportDiagnosticsPolicy_Discard(This) (This)->lpVtbl->Discard(This)
#define IWdsTransportDiagnosticsPolicy_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportDiagnosticsPolicy_Commit(This) (This)->lpVtbl->Commit(This)
/*** IWdsTransportDiagnosticsPolicy methods ***/
#define IWdsTransportDiagnosticsPolicy_get_Enabled(This,pbEnabled) (This)->lpVtbl->get_Enabled(This,pbEnabled)
#define IWdsTransportDiagnosticsPolicy_put_Enabled(This,bEnabled) (This)->lpVtbl->put_Enabled(This,bEnabled)
#define IWdsTransportDiagnosticsPolicy_get_Components(This,pulComponents) (This)->lpVtbl->get_Components(This,pulComponents)
#define IWdsTransportDiagnosticsPolicy_put_Components(This,ulComponents) (This)->lpVtbl->put_Components(This,ulComponents)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportDiagnosticsPolicy_QueryInterface(IWdsTransportDiagnosticsPolicy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportDiagnosticsPolicy_AddRef(IWdsTransportDiagnosticsPolicy* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportDiagnosticsPolicy_Release(IWdsTransportDiagnosticsPolicy* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportDiagnosticsPolicy_GetTypeInfoCount(IWdsTransportDiagnosticsPolicy* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_GetTypeInfo(IWdsTransportDiagnosticsPolicy* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_GetIDsOfNames(IWdsTransportDiagnosticsPolicy* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_Invoke(IWdsTransportDiagnosticsPolicy* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportCacheable methods ***/
static inline HRESULT IWdsTransportDiagnosticsPolicy_get_Dirty(IWdsTransportDiagnosticsPolicy* This,VARIANT_BOOL *pbDirty) {
    return This->lpVtbl->get_Dirty(This,pbDirty);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_Discard(IWdsTransportDiagnosticsPolicy* This) {
    return This->lpVtbl->Discard(This);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_Refresh(IWdsTransportDiagnosticsPolicy* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_Commit(IWdsTransportDiagnosticsPolicy* This) {
    return This->lpVtbl->Commit(This);
}
/*** IWdsTransportDiagnosticsPolicy methods ***/
static inline HRESULT IWdsTransportDiagnosticsPolicy_get_Enabled(IWdsTransportDiagnosticsPolicy* This,VARIANT_BOOL *pbEnabled) {
    return This->lpVtbl->get_Enabled(This,pbEnabled);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_put_Enabled(IWdsTransportDiagnosticsPolicy* This,VARIANT_BOOL bEnabled) {
    return This->lpVtbl->put_Enabled(This,bEnabled);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_get_Components(IWdsTransportDiagnosticsPolicy* This,ULONG *pulComponents) {
    return This->lpVtbl->get_Components(This,pulComponents);
}
static inline HRESULT IWdsTransportDiagnosticsPolicy_put_Components(IWdsTransportDiagnosticsPolicy* This,ULONG ulComponents) {
    return This->lpVtbl->put_Components(This,ulComponents);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportDiagnosticsPolicy_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportConfigurationManager2 interface
 */
#ifndef __IWdsTransportConfigurationManager2_INTERFACE_DEFINED__
#define __IWdsTransportConfigurationManager2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportConfigurationManager2, 0xd0d85caf, 0xa153, 0x4f1d, 0xa9,0xdd, 0x96,0xf4,0x31,0xc5,0x07,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d0d85caf-a153-4f1d-a9dd-96f431c50717")
IWdsTransportConfigurationManager2 : public IWdsTransportConfigurationManager
{
    virtual HRESULT STDMETHODCALLTYPE get_MulticastSessionPolicy(
        IWdsTransportMulticastSessionPolicy **ppWdsTransportMulticastSessionPolicy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportConfigurationManager2, 0xd0d85caf, 0xa153, 0x4f1d, 0xa9,0xdd, 0x96,0xf4,0x31,0xc5,0x07,0x17)
#endif
#else
typedef struct IWdsTransportConfigurationManager2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportConfigurationManager2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportConfigurationManager2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportConfigurationManager2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportConfigurationManager2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportConfigurationManager2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportConfigurationManager2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportConfigurationManager2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportConfigurationManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ServicePolicy)(
        IWdsTransportConfigurationManager2 *This,
        IWdsTransportServicePolicy **ppWdsTransportServicePolicy);

    HRESULT (STDMETHODCALLTYPE *get_DiagnosticsPolicy)(
        IWdsTransportConfigurationManager2 *This,
        IWdsTransportDiagnosticsPolicy **ppWdsTransportDiagnosticsPolicy);

    HRESULT (STDMETHODCALLTYPE *get_WdsTransportServicesRunning)(
        IWdsTransportConfigurationManager2 *This,
        VARIANT_BOOL bRealtimeStatus,
        VARIANT_BOOL *pbServicesRunning);

    HRESULT (STDMETHODCALLTYPE *EnableWdsTransportServices)(
        IWdsTransportConfigurationManager2 *This);

    HRESULT (STDMETHODCALLTYPE *DisableWdsTransportServices)(
        IWdsTransportConfigurationManager2 *This);

    HRESULT (STDMETHODCALLTYPE *StartWdsTransportServices)(
        IWdsTransportConfigurationManager2 *This);

    HRESULT (STDMETHODCALLTYPE *StopWdsTransportServices)(
        IWdsTransportConfigurationManager2 *This);

    HRESULT (STDMETHODCALLTYPE *RestartWdsTransportServices)(
        IWdsTransportConfigurationManager2 *This);

    HRESULT (STDMETHODCALLTYPE *NotifyWdsTransportServices)(
        IWdsTransportConfigurationManager2 *This,
        WDSTRANSPORT_SERVICE_NOTIFICATION ServiceNotification);

    /*** IWdsTransportConfigurationManager2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MulticastSessionPolicy)(
        IWdsTransportConfigurationManager2 *This,
        IWdsTransportMulticastSessionPolicy **ppWdsTransportMulticastSessionPolicy);

    END_INTERFACE
} IWdsTransportConfigurationManager2Vtbl;

interface IWdsTransportConfigurationManager2 {
    CONST_VTBL IWdsTransportConfigurationManager2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportConfigurationManager2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportConfigurationManager2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportConfigurationManager2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportConfigurationManager2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportConfigurationManager2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportConfigurationManager2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportConfigurationManager2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportConfigurationManager methods ***/
#define IWdsTransportConfigurationManager2_get_ServicePolicy(This,ppWdsTransportServicePolicy) (This)->lpVtbl->get_ServicePolicy(This,ppWdsTransportServicePolicy)
#define IWdsTransportConfigurationManager2_get_DiagnosticsPolicy(This,ppWdsTransportDiagnosticsPolicy) (This)->lpVtbl->get_DiagnosticsPolicy(This,ppWdsTransportDiagnosticsPolicy)
#define IWdsTransportConfigurationManager2_get_WdsTransportServicesRunning(This,bRealtimeStatus,pbServicesRunning) (This)->lpVtbl->get_WdsTransportServicesRunning(This,bRealtimeStatus,pbServicesRunning)
#define IWdsTransportConfigurationManager2_EnableWdsTransportServices(This) (This)->lpVtbl->EnableWdsTransportServices(This)
#define IWdsTransportConfigurationManager2_DisableWdsTransportServices(This) (This)->lpVtbl->DisableWdsTransportServices(This)
#define IWdsTransportConfigurationManager2_StartWdsTransportServices(This) (This)->lpVtbl->StartWdsTransportServices(This)
#define IWdsTransportConfigurationManager2_StopWdsTransportServices(This) (This)->lpVtbl->StopWdsTransportServices(This)
#define IWdsTransportConfigurationManager2_RestartWdsTransportServices(This) (This)->lpVtbl->RestartWdsTransportServices(This)
#define IWdsTransportConfigurationManager2_NotifyWdsTransportServices(This,ServiceNotification) (This)->lpVtbl->NotifyWdsTransportServices(This,ServiceNotification)
/*** IWdsTransportConfigurationManager2 methods ***/
#define IWdsTransportConfigurationManager2_get_MulticastSessionPolicy(This,ppWdsTransportMulticastSessionPolicy) (This)->lpVtbl->get_MulticastSessionPolicy(This,ppWdsTransportMulticastSessionPolicy)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportConfigurationManager2_QueryInterface(IWdsTransportConfigurationManager2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportConfigurationManager2_AddRef(IWdsTransportConfigurationManager2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportConfigurationManager2_Release(IWdsTransportConfigurationManager2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportConfigurationManager2_GetTypeInfoCount(IWdsTransportConfigurationManager2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportConfigurationManager2_GetTypeInfo(IWdsTransportConfigurationManager2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportConfigurationManager2_GetIDsOfNames(IWdsTransportConfigurationManager2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportConfigurationManager2_Invoke(IWdsTransportConfigurationManager2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportConfigurationManager methods ***/
static inline HRESULT IWdsTransportConfigurationManager2_get_ServicePolicy(IWdsTransportConfigurationManager2* This,IWdsTransportServicePolicy **ppWdsTransportServicePolicy) {
    return This->lpVtbl->get_ServicePolicy(This,ppWdsTransportServicePolicy);
}
static inline HRESULT IWdsTransportConfigurationManager2_get_DiagnosticsPolicy(IWdsTransportConfigurationManager2* This,IWdsTransportDiagnosticsPolicy **ppWdsTransportDiagnosticsPolicy) {
    return This->lpVtbl->get_DiagnosticsPolicy(This,ppWdsTransportDiagnosticsPolicy);
}
static inline HRESULT IWdsTransportConfigurationManager2_get_WdsTransportServicesRunning(IWdsTransportConfigurationManager2* This,VARIANT_BOOL bRealtimeStatus,VARIANT_BOOL *pbServicesRunning) {
    return This->lpVtbl->get_WdsTransportServicesRunning(This,bRealtimeStatus,pbServicesRunning);
}
static inline HRESULT IWdsTransportConfigurationManager2_EnableWdsTransportServices(IWdsTransportConfigurationManager2* This) {
    return This->lpVtbl->EnableWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager2_DisableWdsTransportServices(IWdsTransportConfigurationManager2* This) {
    return This->lpVtbl->DisableWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager2_StartWdsTransportServices(IWdsTransportConfigurationManager2* This) {
    return This->lpVtbl->StartWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager2_StopWdsTransportServices(IWdsTransportConfigurationManager2* This) {
    return This->lpVtbl->StopWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager2_RestartWdsTransportServices(IWdsTransportConfigurationManager2* This) {
    return This->lpVtbl->RestartWdsTransportServices(This);
}
static inline HRESULT IWdsTransportConfigurationManager2_NotifyWdsTransportServices(IWdsTransportConfigurationManager2* This,WDSTRANSPORT_SERVICE_NOTIFICATION ServiceNotification) {
    return This->lpVtbl->NotifyWdsTransportServices(This,ServiceNotification);
}
/*** IWdsTransportConfigurationManager2 methods ***/
static inline HRESULT IWdsTransportConfigurationManager2_get_MulticastSessionPolicy(IWdsTransportConfigurationManager2* This,IWdsTransportMulticastSessionPolicy **ppWdsTransportMulticastSessionPolicy) {
    return This->lpVtbl->get_MulticastSessionPolicy(This,ppWdsTransportMulticastSessionPolicy);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportConfigurationManager2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportManager interface
 */
#ifndef __IWdsTransportManager_INTERFACE_DEFINED__
#define __IWdsTransportManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportManager, 0x5b0d35f5, 0x1b13, 0x4afd, 0xb8,0x78, 0x65,0x26,0xdc,0x34,0x0b,0x5d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5b0d35f5-1b13-4afd-b878-6526dc340b5d")
IWdsTransportManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetWdsTransportServer(
        BSTR bszServerName,
        IWdsTransportServer **ppWdsTransportServer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportManager, 0x5b0d35f5, 0x1b13, 0x4afd, 0xb8,0x78, 0x65,0x26,0xdc,0x34,0x0b,0x5d)
#endif
#else
typedef struct IWdsTransportManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWdsTransportServer)(
        IWdsTransportManager *This,
        BSTR bszServerName,
        IWdsTransportServer **ppWdsTransportServer);

    END_INTERFACE
} IWdsTransportManagerVtbl;

interface IWdsTransportManager {
    CONST_VTBL IWdsTransportManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportManager methods ***/
#define IWdsTransportManager_GetWdsTransportServer(This,bszServerName,ppWdsTransportServer) (This)->lpVtbl->GetWdsTransportServer(This,bszServerName,ppWdsTransportServer)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportManager_QueryInterface(IWdsTransportManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportManager_AddRef(IWdsTransportManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportManager_Release(IWdsTransportManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportManager_GetTypeInfoCount(IWdsTransportManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportManager_GetTypeInfo(IWdsTransportManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportManager_GetIDsOfNames(IWdsTransportManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportManager_Invoke(IWdsTransportManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportManager methods ***/
static inline HRESULT IWdsTransportManager_GetWdsTransportServer(IWdsTransportManager* This,BSTR bszServerName,IWdsTransportServer **ppWdsTransportServer) {
    return This->lpVtbl->GetWdsTransportServer(This,bszServerName,ppWdsTransportServer);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportNamespace interface
 */
#ifndef __IWdsTransportNamespace_INTERFACE_DEFINED__
#define __IWdsTransportNamespace_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportNamespace, 0xfa561f57, 0xfbef, 0x4ed3, 0xb0,0x56, 0x12,0x7c,0xb1,0xb3,0x3b,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fa561f57-fbef-4ed3-b056-127cb1b33b84")
IWdsTransportNamespace : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Type(
        WDSTRANSPORT_NAMESPACE_TYPE *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        ULONG *pulId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR bszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FriendlyName(
        BSTR *pbszFriendlyName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FriendlyName(
        BSTR bszFriendlyName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *pbszDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Description(
        BSTR bszDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ContentProvider(
        BSTR *pbszContentProvider) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ContentProvider(
        BSTR bszContentProvider) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Configuration(
        BSTR *pbszConfiguration) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Configuration(
        BSTR bszConfiguration) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Registered(
        VARIANT_BOOL *pbRegistered) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Tombstoned(
        VARIANT_BOOL *pbTombstoned) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TombstoneTime(
        DATE *pTombstoneTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TransmissionStarted(
        VARIANT_BOOL *pbTransmissionStarted) = 0;

    virtual HRESULT STDMETHODCALLTYPE Register(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Deregister(
        VARIANT_BOOL bTerminateSessions) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IWdsTransportNamespace **ppWdsTransportNamespaceClone) = 0;

    virtual HRESULT STDMETHODCALLTYPE Refresh(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RetrieveContents(
        IWdsTransportCollection **ppWdsTransportContents) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportNamespace, 0xfa561f57, 0xfbef, 0x4ed3, 0xb0,0x56, 0x12,0x7c,0xb1,0xb3,0x3b,0x84)
#endif
#else
typedef struct IWdsTransportNamespaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportNamespace *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportNamespace *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportNamespace *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportNamespace *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportNamespace *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportNamespace *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportNamespace *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportNamespace methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWdsTransportNamespace *This,
        WDSTRANSPORT_NAMESPACE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWdsTransportNamespace *This,
        ULONG *pulId);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportNamespace *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IWdsTransportNamespace *This,
        BSTR bszName);

    HRESULT (STDMETHODCALLTYPE *get_FriendlyName)(
        IWdsTransportNamespace *This,
        BSTR *pbszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *put_FriendlyName)(
        IWdsTransportNamespace *This,
        BSTR bszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IWdsTransportNamespace *This,
        BSTR *pbszDescription);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IWdsTransportNamespace *This,
        BSTR bszDescription);

    HRESULT (STDMETHODCALLTYPE *get_ContentProvider)(
        IWdsTransportNamespace *This,
        BSTR *pbszContentProvider);

    HRESULT (STDMETHODCALLTYPE *put_ContentProvider)(
        IWdsTransportNamespace *This,
        BSTR bszContentProvider);

    HRESULT (STDMETHODCALLTYPE *get_Configuration)(
        IWdsTransportNamespace *This,
        BSTR *pbszConfiguration);

    HRESULT (STDMETHODCALLTYPE *put_Configuration)(
        IWdsTransportNamespace *This,
        BSTR bszConfiguration);

    HRESULT (STDMETHODCALLTYPE *get_Registered)(
        IWdsTransportNamespace *This,
        VARIANT_BOOL *pbRegistered);

    HRESULT (STDMETHODCALLTYPE *get_Tombstoned)(
        IWdsTransportNamespace *This,
        VARIANT_BOOL *pbTombstoned);

    HRESULT (STDMETHODCALLTYPE *get_TombstoneTime)(
        IWdsTransportNamespace *This,
        DATE *pTombstoneTime);

    HRESULT (STDMETHODCALLTYPE *get_TransmissionStarted)(
        IWdsTransportNamespace *This,
        VARIANT_BOOL *pbTransmissionStarted);

    HRESULT (STDMETHODCALLTYPE *Register)(
        IWdsTransportNamespace *This);

    HRESULT (STDMETHODCALLTYPE *Deregister)(
        IWdsTransportNamespace *This,
        VARIANT_BOOL bTerminateSessions);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWdsTransportNamespace *This,
        IWdsTransportNamespace **ppWdsTransportNamespaceClone);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportNamespace *This);

    HRESULT (STDMETHODCALLTYPE *RetrieveContents)(
        IWdsTransportNamespace *This,
        IWdsTransportCollection **ppWdsTransportContents);

    END_INTERFACE
} IWdsTransportNamespaceVtbl;

interface IWdsTransportNamespace {
    CONST_VTBL IWdsTransportNamespaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportNamespace_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportNamespace_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportNamespace_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportNamespace_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportNamespace_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportNamespace_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportNamespace_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportNamespace methods ***/
#define IWdsTransportNamespace_get_Type(This,pType) (This)->lpVtbl->get_Type(This,pType)
#define IWdsTransportNamespace_get_Id(This,pulId) (This)->lpVtbl->get_Id(This,pulId)
#define IWdsTransportNamespace_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportNamespace_put_Name(This,bszName) (This)->lpVtbl->put_Name(This,bszName)
#define IWdsTransportNamespace_get_FriendlyName(This,pbszFriendlyName) (This)->lpVtbl->get_FriendlyName(This,pbszFriendlyName)
#define IWdsTransportNamespace_put_FriendlyName(This,bszFriendlyName) (This)->lpVtbl->put_FriendlyName(This,bszFriendlyName)
#define IWdsTransportNamespace_get_Description(This,pbszDescription) (This)->lpVtbl->get_Description(This,pbszDescription)
#define IWdsTransportNamespace_put_Description(This,bszDescription) (This)->lpVtbl->put_Description(This,bszDescription)
#define IWdsTransportNamespace_get_ContentProvider(This,pbszContentProvider) (This)->lpVtbl->get_ContentProvider(This,pbszContentProvider)
#define IWdsTransportNamespace_put_ContentProvider(This,bszContentProvider) (This)->lpVtbl->put_ContentProvider(This,bszContentProvider)
#define IWdsTransportNamespace_get_Configuration(This,pbszConfiguration) (This)->lpVtbl->get_Configuration(This,pbszConfiguration)
#define IWdsTransportNamespace_put_Configuration(This,bszConfiguration) (This)->lpVtbl->put_Configuration(This,bszConfiguration)
#define IWdsTransportNamespace_get_Registered(This,pbRegistered) (This)->lpVtbl->get_Registered(This,pbRegistered)
#define IWdsTransportNamespace_get_Tombstoned(This,pbTombstoned) (This)->lpVtbl->get_Tombstoned(This,pbTombstoned)
#define IWdsTransportNamespace_get_TombstoneTime(This,pTombstoneTime) (This)->lpVtbl->get_TombstoneTime(This,pTombstoneTime)
#define IWdsTransportNamespace_get_TransmissionStarted(This,pbTransmissionStarted) (This)->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted)
#define IWdsTransportNamespace_Register(This) (This)->lpVtbl->Register(This)
#define IWdsTransportNamespace_Deregister(This,bTerminateSessions) (This)->lpVtbl->Deregister(This,bTerminateSessions)
#define IWdsTransportNamespace_Clone(This,ppWdsTransportNamespaceClone) (This)->lpVtbl->Clone(This,ppWdsTransportNamespaceClone)
#define IWdsTransportNamespace_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportNamespace_RetrieveContents(This,ppWdsTransportContents) (This)->lpVtbl->RetrieveContents(This,ppWdsTransportContents)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportNamespace_QueryInterface(IWdsTransportNamespace* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportNamespace_AddRef(IWdsTransportNamespace* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportNamespace_Release(IWdsTransportNamespace* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportNamespace_GetTypeInfoCount(IWdsTransportNamespace* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportNamespace_GetTypeInfo(IWdsTransportNamespace* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportNamespace_GetIDsOfNames(IWdsTransportNamespace* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportNamespace_Invoke(IWdsTransportNamespace* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportNamespace methods ***/
static inline HRESULT IWdsTransportNamespace_get_Type(IWdsTransportNamespace* This,WDSTRANSPORT_NAMESPACE_TYPE *pType) {
    return This->lpVtbl->get_Type(This,pType);
}
static inline HRESULT IWdsTransportNamespace_get_Id(IWdsTransportNamespace* This,ULONG *pulId) {
    return This->lpVtbl->get_Id(This,pulId);
}
static inline HRESULT IWdsTransportNamespace_get_Name(IWdsTransportNamespace* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportNamespace_put_Name(IWdsTransportNamespace* This,BSTR bszName) {
    return This->lpVtbl->put_Name(This,bszName);
}
static inline HRESULT IWdsTransportNamespace_get_FriendlyName(IWdsTransportNamespace* This,BSTR *pbszFriendlyName) {
    return This->lpVtbl->get_FriendlyName(This,pbszFriendlyName);
}
static inline HRESULT IWdsTransportNamespace_put_FriendlyName(IWdsTransportNamespace* This,BSTR bszFriendlyName) {
    return This->lpVtbl->put_FriendlyName(This,bszFriendlyName);
}
static inline HRESULT IWdsTransportNamespace_get_Description(IWdsTransportNamespace* This,BSTR *pbszDescription) {
    return This->lpVtbl->get_Description(This,pbszDescription);
}
static inline HRESULT IWdsTransportNamespace_put_Description(IWdsTransportNamespace* This,BSTR bszDescription) {
    return This->lpVtbl->put_Description(This,bszDescription);
}
static inline HRESULT IWdsTransportNamespace_get_ContentProvider(IWdsTransportNamespace* This,BSTR *pbszContentProvider) {
    return This->lpVtbl->get_ContentProvider(This,pbszContentProvider);
}
static inline HRESULT IWdsTransportNamespace_put_ContentProvider(IWdsTransportNamespace* This,BSTR bszContentProvider) {
    return This->lpVtbl->put_ContentProvider(This,bszContentProvider);
}
static inline HRESULT IWdsTransportNamespace_get_Configuration(IWdsTransportNamespace* This,BSTR *pbszConfiguration) {
    return This->lpVtbl->get_Configuration(This,pbszConfiguration);
}
static inline HRESULT IWdsTransportNamespace_put_Configuration(IWdsTransportNamespace* This,BSTR bszConfiguration) {
    return This->lpVtbl->put_Configuration(This,bszConfiguration);
}
static inline HRESULT IWdsTransportNamespace_get_Registered(IWdsTransportNamespace* This,VARIANT_BOOL *pbRegistered) {
    return This->lpVtbl->get_Registered(This,pbRegistered);
}
static inline HRESULT IWdsTransportNamespace_get_Tombstoned(IWdsTransportNamespace* This,VARIANT_BOOL *pbTombstoned) {
    return This->lpVtbl->get_Tombstoned(This,pbTombstoned);
}
static inline HRESULT IWdsTransportNamespace_get_TombstoneTime(IWdsTransportNamespace* This,DATE *pTombstoneTime) {
    return This->lpVtbl->get_TombstoneTime(This,pTombstoneTime);
}
static inline HRESULT IWdsTransportNamespace_get_TransmissionStarted(IWdsTransportNamespace* This,VARIANT_BOOL *pbTransmissionStarted) {
    return This->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted);
}
static inline HRESULT IWdsTransportNamespace_Register(IWdsTransportNamespace* This) {
    return This->lpVtbl->Register(This);
}
static inline HRESULT IWdsTransportNamespace_Deregister(IWdsTransportNamespace* This,VARIANT_BOOL bTerminateSessions) {
    return This->lpVtbl->Deregister(This,bTerminateSessions);
}
static inline HRESULT IWdsTransportNamespace_Clone(IWdsTransportNamespace* This,IWdsTransportNamespace **ppWdsTransportNamespaceClone) {
    return This->lpVtbl->Clone(This,ppWdsTransportNamespaceClone);
}
static inline HRESULT IWdsTransportNamespace_Refresh(IWdsTransportNamespace* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportNamespace_RetrieveContents(IWdsTransportNamespace* This,IWdsTransportCollection **ppWdsTransportContents) {
    return This->lpVtbl->RetrieveContents(This,ppWdsTransportContents);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportNamespace_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportNamespaceAutoCast interface
 */
#ifndef __IWdsTransportNamespaceAutoCast_INTERFACE_DEFINED__
#define __IWdsTransportNamespaceAutoCast_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportNamespaceAutoCast, 0xad931a72, 0xc4bd, 0x4c41, 0x8f,0xbc, 0x59,0xc9,0xc7,0x48,0xdf,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ad931a72-c4bd-4c41-8fbc-59c9c748df9e")
IWdsTransportNamespaceAutoCast : public IWdsTransportNamespace
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportNamespaceAutoCast, 0xad931a72, 0xc4bd, 0x4c41, 0x8f,0xbc, 0x59,0xc9,0xc7,0x48,0xdf,0x9e)
#endif
#else
typedef struct IWdsTransportNamespaceAutoCastVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportNamespaceAutoCast *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportNamespaceAutoCast *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportNamespaceAutoCast *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportNamespaceAutoCast *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportNamespaceAutoCast *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportNamespaceAutoCast *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportNamespaceAutoCast *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportNamespace methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWdsTransportNamespaceAutoCast *This,
        WDSTRANSPORT_NAMESPACE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWdsTransportNamespaceAutoCast *This,
        ULONG *pulId);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR bszName);

    HRESULT (STDMETHODCALLTYPE *get_FriendlyName)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR *pbszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *put_FriendlyName)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR bszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR *pbszDescription);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR bszDescription);

    HRESULT (STDMETHODCALLTYPE *get_ContentProvider)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR *pbszContentProvider);

    HRESULT (STDMETHODCALLTYPE *put_ContentProvider)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR bszContentProvider);

    HRESULT (STDMETHODCALLTYPE *get_Configuration)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR *pbszConfiguration);

    HRESULT (STDMETHODCALLTYPE *put_Configuration)(
        IWdsTransportNamespaceAutoCast *This,
        BSTR bszConfiguration);

    HRESULT (STDMETHODCALLTYPE *get_Registered)(
        IWdsTransportNamespaceAutoCast *This,
        VARIANT_BOOL *pbRegistered);

    HRESULT (STDMETHODCALLTYPE *get_Tombstoned)(
        IWdsTransportNamespaceAutoCast *This,
        VARIANT_BOOL *pbTombstoned);

    HRESULT (STDMETHODCALLTYPE *get_TombstoneTime)(
        IWdsTransportNamespaceAutoCast *This,
        DATE *pTombstoneTime);

    HRESULT (STDMETHODCALLTYPE *get_TransmissionStarted)(
        IWdsTransportNamespaceAutoCast *This,
        VARIANT_BOOL *pbTransmissionStarted);

    HRESULT (STDMETHODCALLTYPE *Register)(
        IWdsTransportNamespaceAutoCast *This);

    HRESULT (STDMETHODCALLTYPE *Deregister)(
        IWdsTransportNamespaceAutoCast *This,
        VARIANT_BOOL bTerminateSessions);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWdsTransportNamespaceAutoCast *This,
        IWdsTransportNamespace **ppWdsTransportNamespaceClone);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportNamespaceAutoCast *This);

    HRESULT (STDMETHODCALLTYPE *RetrieveContents)(
        IWdsTransportNamespaceAutoCast *This,
        IWdsTransportCollection **ppWdsTransportContents);

    END_INTERFACE
} IWdsTransportNamespaceAutoCastVtbl;

interface IWdsTransportNamespaceAutoCast {
    CONST_VTBL IWdsTransportNamespaceAutoCastVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportNamespaceAutoCast_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportNamespaceAutoCast_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportNamespaceAutoCast_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportNamespaceAutoCast_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportNamespaceAutoCast_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportNamespaceAutoCast_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportNamespaceAutoCast_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportNamespace methods ***/
#define IWdsTransportNamespaceAutoCast_get_Type(This,pType) (This)->lpVtbl->get_Type(This,pType)
#define IWdsTransportNamespaceAutoCast_get_Id(This,pulId) (This)->lpVtbl->get_Id(This,pulId)
#define IWdsTransportNamespaceAutoCast_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportNamespaceAutoCast_put_Name(This,bszName) (This)->lpVtbl->put_Name(This,bszName)
#define IWdsTransportNamespaceAutoCast_get_FriendlyName(This,pbszFriendlyName) (This)->lpVtbl->get_FriendlyName(This,pbszFriendlyName)
#define IWdsTransportNamespaceAutoCast_put_FriendlyName(This,bszFriendlyName) (This)->lpVtbl->put_FriendlyName(This,bszFriendlyName)
#define IWdsTransportNamespaceAutoCast_get_Description(This,pbszDescription) (This)->lpVtbl->get_Description(This,pbszDescription)
#define IWdsTransportNamespaceAutoCast_put_Description(This,bszDescription) (This)->lpVtbl->put_Description(This,bszDescription)
#define IWdsTransportNamespaceAutoCast_get_ContentProvider(This,pbszContentProvider) (This)->lpVtbl->get_ContentProvider(This,pbszContentProvider)
#define IWdsTransportNamespaceAutoCast_put_ContentProvider(This,bszContentProvider) (This)->lpVtbl->put_ContentProvider(This,bszContentProvider)
#define IWdsTransportNamespaceAutoCast_get_Configuration(This,pbszConfiguration) (This)->lpVtbl->get_Configuration(This,pbszConfiguration)
#define IWdsTransportNamespaceAutoCast_put_Configuration(This,bszConfiguration) (This)->lpVtbl->put_Configuration(This,bszConfiguration)
#define IWdsTransportNamespaceAutoCast_get_Registered(This,pbRegistered) (This)->lpVtbl->get_Registered(This,pbRegistered)
#define IWdsTransportNamespaceAutoCast_get_Tombstoned(This,pbTombstoned) (This)->lpVtbl->get_Tombstoned(This,pbTombstoned)
#define IWdsTransportNamespaceAutoCast_get_TombstoneTime(This,pTombstoneTime) (This)->lpVtbl->get_TombstoneTime(This,pTombstoneTime)
#define IWdsTransportNamespaceAutoCast_get_TransmissionStarted(This,pbTransmissionStarted) (This)->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted)
#define IWdsTransportNamespaceAutoCast_Register(This) (This)->lpVtbl->Register(This)
#define IWdsTransportNamespaceAutoCast_Deregister(This,bTerminateSessions) (This)->lpVtbl->Deregister(This,bTerminateSessions)
#define IWdsTransportNamespaceAutoCast_Clone(This,ppWdsTransportNamespaceClone) (This)->lpVtbl->Clone(This,ppWdsTransportNamespaceClone)
#define IWdsTransportNamespaceAutoCast_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportNamespaceAutoCast_RetrieveContents(This,ppWdsTransportContents) (This)->lpVtbl->RetrieveContents(This,ppWdsTransportContents)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportNamespaceAutoCast_QueryInterface(IWdsTransportNamespaceAutoCast* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportNamespaceAutoCast_AddRef(IWdsTransportNamespaceAutoCast* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportNamespaceAutoCast_Release(IWdsTransportNamespaceAutoCast* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportNamespaceAutoCast_GetTypeInfoCount(IWdsTransportNamespaceAutoCast* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_GetTypeInfo(IWdsTransportNamespaceAutoCast* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_GetIDsOfNames(IWdsTransportNamespaceAutoCast* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_Invoke(IWdsTransportNamespaceAutoCast* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportNamespace methods ***/
static inline HRESULT IWdsTransportNamespaceAutoCast_get_Type(IWdsTransportNamespaceAutoCast* This,WDSTRANSPORT_NAMESPACE_TYPE *pType) {
    return This->lpVtbl->get_Type(This,pType);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_Id(IWdsTransportNamespaceAutoCast* This,ULONG *pulId) {
    return This->lpVtbl->get_Id(This,pulId);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_Name(IWdsTransportNamespaceAutoCast* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_put_Name(IWdsTransportNamespaceAutoCast* This,BSTR bszName) {
    return This->lpVtbl->put_Name(This,bszName);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_FriendlyName(IWdsTransportNamespaceAutoCast* This,BSTR *pbszFriendlyName) {
    return This->lpVtbl->get_FriendlyName(This,pbszFriendlyName);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_put_FriendlyName(IWdsTransportNamespaceAutoCast* This,BSTR bszFriendlyName) {
    return This->lpVtbl->put_FriendlyName(This,bszFriendlyName);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_Description(IWdsTransportNamespaceAutoCast* This,BSTR *pbszDescription) {
    return This->lpVtbl->get_Description(This,pbszDescription);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_put_Description(IWdsTransportNamespaceAutoCast* This,BSTR bszDescription) {
    return This->lpVtbl->put_Description(This,bszDescription);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_ContentProvider(IWdsTransportNamespaceAutoCast* This,BSTR *pbszContentProvider) {
    return This->lpVtbl->get_ContentProvider(This,pbszContentProvider);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_put_ContentProvider(IWdsTransportNamespaceAutoCast* This,BSTR bszContentProvider) {
    return This->lpVtbl->put_ContentProvider(This,bszContentProvider);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_Configuration(IWdsTransportNamespaceAutoCast* This,BSTR *pbszConfiguration) {
    return This->lpVtbl->get_Configuration(This,pbszConfiguration);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_put_Configuration(IWdsTransportNamespaceAutoCast* This,BSTR bszConfiguration) {
    return This->lpVtbl->put_Configuration(This,bszConfiguration);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_Registered(IWdsTransportNamespaceAutoCast* This,VARIANT_BOOL *pbRegistered) {
    return This->lpVtbl->get_Registered(This,pbRegistered);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_Tombstoned(IWdsTransportNamespaceAutoCast* This,VARIANT_BOOL *pbTombstoned) {
    return This->lpVtbl->get_Tombstoned(This,pbTombstoned);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_TombstoneTime(IWdsTransportNamespaceAutoCast* This,DATE *pTombstoneTime) {
    return This->lpVtbl->get_TombstoneTime(This,pTombstoneTime);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_get_TransmissionStarted(IWdsTransportNamespaceAutoCast* This,VARIANT_BOOL *pbTransmissionStarted) {
    return This->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_Register(IWdsTransportNamespaceAutoCast* This) {
    return This->lpVtbl->Register(This);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_Deregister(IWdsTransportNamespaceAutoCast* This,VARIANT_BOOL bTerminateSessions) {
    return This->lpVtbl->Deregister(This,bTerminateSessions);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_Clone(IWdsTransportNamespaceAutoCast* This,IWdsTransportNamespace **ppWdsTransportNamespaceClone) {
    return This->lpVtbl->Clone(This,ppWdsTransportNamespaceClone);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_Refresh(IWdsTransportNamespaceAutoCast* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportNamespaceAutoCast_RetrieveContents(IWdsTransportNamespaceAutoCast* This,IWdsTransportCollection **ppWdsTransportContents) {
    return This->lpVtbl->RetrieveContents(This,ppWdsTransportContents);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportNamespaceAutoCast_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportNamespaceScheduledCast interface
 */
#ifndef __IWdsTransportNamespaceScheduledCast_INTERFACE_DEFINED__
#define __IWdsTransportNamespaceScheduledCast_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportNamespaceScheduledCast, 0x3840cecf, 0xd76c, 0x416e, 0xa4,0xcc, 0x31,0xc7,0x41,0xd2,0x87,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3840cecf-d76c-416e-a4cc-31c741d2874b")
IWdsTransportNamespaceScheduledCast : public IWdsTransportNamespace
{
    virtual HRESULT STDMETHODCALLTYPE StartTransmission(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportNamespaceScheduledCast, 0x3840cecf, 0xd76c, 0x416e, 0xa4,0xcc, 0x31,0xc7,0x41,0xd2,0x87,0x4b)
#endif
#else
typedef struct IWdsTransportNamespaceScheduledCastVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportNamespaceScheduledCast *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportNamespaceScheduledCast *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportNamespaceScheduledCast *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportNamespaceScheduledCast *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportNamespaceScheduledCast *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportNamespaceScheduledCast *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportNamespaceScheduledCast *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportNamespace methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWdsTransportNamespaceScheduledCast *This,
        WDSTRANSPORT_NAMESPACE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWdsTransportNamespaceScheduledCast *This,
        ULONG *pulId);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR bszName);

    HRESULT (STDMETHODCALLTYPE *get_FriendlyName)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR *pbszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *put_FriendlyName)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR bszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR *pbszDescription);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR bszDescription);

    HRESULT (STDMETHODCALLTYPE *get_ContentProvider)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR *pbszContentProvider);

    HRESULT (STDMETHODCALLTYPE *put_ContentProvider)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR bszContentProvider);

    HRESULT (STDMETHODCALLTYPE *get_Configuration)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR *pbszConfiguration);

    HRESULT (STDMETHODCALLTYPE *put_Configuration)(
        IWdsTransportNamespaceScheduledCast *This,
        BSTR bszConfiguration);

    HRESULT (STDMETHODCALLTYPE *get_Registered)(
        IWdsTransportNamespaceScheduledCast *This,
        VARIANT_BOOL *pbRegistered);

    HRESULT (STDMETHODCALLTYPE *get_Tombstoned)(
        IWdsTransportNamespaceScheduledCast *This,
        VARIANT_BOOL *pbTombstoned);

    HRESULT (STDMETHODCALLTYPE *get_TombstoneTime)(
        IWdsTransportNamespaceScheduledCast *This,
        DATE *pTombstoneTime);

    HRESULT (STDMETHODCALLTYPE *get_TransmissionStarted)(
        IWdsTransportNamespaceScheduledCast *This,
        VARIANT_BOOL *pbTransmissionStarted);

    HRESULT (STDMETHODCALLTYPE *Register)(
        IWdsTransportNamespaceScheduledCast *This);

    HRESULT (STDMETHODCALLTYPE *Deregister)(
        IWdsTransportNamespaceScheduledCast *This,
        VARIANT_BOOL bTerminateSessions);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWdsTransportNamespaceScheduledCast *This,
        IWdsTransportNamespace **ppWdsTransportNamespaceClone);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportNamespaceScheduledCast *This);

    HRESULT (STDMETHODCALLTYPE *RetrieveContents)(
        IWdsTransportNamespaceScheduledCast *This,
        IWdsTransportCollection **ppWdsTransportContents);

    /*** IWdsTransportNamespaceScheduledCast methods ***/
    HRESULT (STDMETHODCALLTYPE *StartTransmission)(
        IWdsTransportNamespaceScheduledCast *This);

    END_INTERFACE
} IWdsTransportNamespaceScheduledCastVtbl;

interface IWdsTransportNamespaceScheduledCast {
    CONST_VTBL IWdsTransportNamespaceScheduledCastVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportNamespaceScheduledCast_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportNamespaceScheduledCast_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportNamespaceScheduledCast_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportNamespaceScheduledCast_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportNamespaceScheduledCast_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportNamespaceScheduledCast_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportNamespaceScheduledCast_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportNamespace methods ***/
#define IWdsTransportNamespaceScheduledCast_get_Type(This,pType) (This)->lpVtbl->get_Type(This,pType)
#define IWdsTransportNamespaceScheduledCast_get_Id(This,pulId) (This)->lpVtbl->get_Id(This,pulId)
#define IWdsTransportNamespaceScheduledCast_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportNamespaceScheduledCast_put_Name(This,bszName) (This)->lpVtbl->put_Name(This,bszName)
#define IWdsTransportNamespaceScheduledCast_get_FriendlyName(This,pbszFriendlyName) (This)->lpVtbl->get_FriendlyName(This,pbszFriendlyName)
#define IWdsTransportNamespaceScheduledCast_put_FriendlyName(This,bszFriendlyName) (This)->lpVtbl->put_FriendlyName(This,bszFriendlyName)
#define IWdsTransportNamespaceScheduledCast_get_Description(This,pbszDescription) (This)->lpVtbl->get_Description(This,pbszDescription)
#define IWdsTransportNamespaceScheduledCast_put_Description(This,bszDescription) (This)->lpVtbl->put_Description(This,bszDescription)
#define IWdsTransportNamespaceScheduledCast_get_ContentProvider(This,pbszContentProvider) (This)->lpVtbl->get_ContentProvider(This,pbszContentProvider)
#define IWdsTransportNamespaceScheduledCast_put_ContentProvider(This,bszContentProvider) (This)->lpVtbl->put_ContentProvider(This,bszContentProvider)
#define IWdsTransportNamespaceScheduledCast_get_Configuration(This,pbszConfiguration) (This)->lpVtbl->get_Configuration(This,pbszConfiguration)
#define IWdsTransportNamespaceScheduledCast_put_Configuration(This,bszConfiguration) (This)->lpVtbl->put_Configuration(This,bszConfiguration)
#define IWdsTransportNamespaceScheduledCast_get_Registered(This,pbRegistered) (This)->lpVtbl->get_Registered(This,pbRegistered)
#define IWdsTransportNamespaceScheduledCast_get_Tombstoned(This,pbTombstoned) (This)->lpVtbl->get_Tombstoned(This,pbTombstoned)
#define IWdsTransportNamespaceScheduledCast_get_TombstoneTime(This,pTombstoneTime) (This)->lpVtbl->get_TombstoneTime(This,pTombstoneTime)
#define IWdsTransportNamespaceScheduledCast_get_TransmissionStarted(This,pbTransmissionStarted) (This)->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted)
#define IWdsTransportNamespaceScheduledCast_Register(This) (This)->lpVtbl->Register(This)
#define IWdsTransportNamespaceScheduledCast_Deregister(This,bTerminateSessions) (This)->lpVtbl->Deregister(This,bTerminateSessions)
#define IWdsTransportNamespaceScheduledCast_Clone(This,ppWdsTransportNamespaceClone) (This)->lpVtbl->Clone(This,ppWdsTransportNamespaceClone)
#define IWdsTransportNamespaceScheduledCast_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportNamespaceScheduledCast_RetrieveContents(This,ppWdsTransportContents) (This)->lpVtbl->RetrieveContents(This,ppWdsTransportContents)
/*** IWdsTransportNamespaceScheduledCast methods ***/
#define IWdsTransportNamespaceScheduledCast_StartTransmission(This) (This)->lpVtbl->StartTransmission(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCast_QueryInterface(IWdsTransportNamespaceScheduledCast* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportNamespaceScheduledCast_AddRef(IWdsTransportNamespaceScheduledCast* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportNamespaceScheduledCast_Release(IWdsTransportNamespaceScheduledCast* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCast_GetTypeInfoCount(IWdsTransportNamespaceScheduledCast* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_GetTypeInfo(IWdsTransportNamespaceScheduledCast* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_GetIDsOfNames(IWdsTransportNamespaceScheduledCast* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_Invoke(IWdsTransportNamespaceScheduledCast* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportNamespace methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_Type(IWdsTransportNamespaceScheduledCast* This,WDSTRANSPORT_NAMESPACE_TYPE *pType) {
    return This->lpVtbl->get_Type(This,pType);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_Id(IWdsTransportNamespaceScheduledCast* This,ULONG *pulId) {
    return This->lpVtbl->get_Id(This,pulId);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_Name(IWdsTransportNamespaceScheduledCast* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_put_Name(IWdsTransportNamespaceScheduledCast* This,BSTR bszName) {
    return This->lpVtbl->put_Name(This,bszName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_FriendlyName(IWdsTransportNamespaceScheduledCast* This,BSTR *pbszFriendlyName) {
    return This->lpVtbl->get_FriendlyName(This,pbszFriendlyName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_put_FriendlyName(IWdsTransportNamespaceScheduledCast* This,BSTR bszFriendlyName) {
    return This->lpVtbl->put_FriendlyName(This,bszFriendlyName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_Description(IWdsTransportNamespaceScheduledCast* This,BSTR *pbszDescription) {
    return This->lpVtbl->get_Description(This,pbszDescription);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_put_Description(IWdsTransportNamespaceScheduledCast* This,BSTR bszDescription) {
    return This->lpVtbl->put_Description(This,bszDescription);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_ContentProvider(IWdsTransportNamespaceScheduledCast* This,BSTR *pbszContentProvider) {
    return This->lpVtbl->get_ContentProvider(This,pbszContentProvider);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_put_ContentProvider(IWdsTransportNamespaceScheduledCast* This,BSTR bszContentProvider) {
    return This->lpVtbl->put_ContentProvider(This,bszContentProvider);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_Configuration(IWdsTransportNamespaceScheduledCast* This,BSTR *pbszConfiguration) {
    return This->lpVtbl->get_Configuration(This,pbszConfiguration);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_put_Configuration(IWdsTransportNamespaceScheduledCast* This,BSTR bszConfiguration) {
    return This->lpVtbl->put_Configuration(This,bszConfiguration);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_Registered(IWdsTransportNamespaceScheduledCast* This,VARIANT_BOOL *pbRegistered) {
    return This->lpVtbl->get_Registered(This,pbRegistered);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_Tombstoned(IWdsTransportNamespaceScheduledCast* This,VARIANT_BOOL *pbTombstoned) {
    return This->lpVtbl->get_Tombstoned(This,pbTombstoned);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_TombstoneTime(IWdsTransportNamespaceScheduledCast* This,DATE *pTombstoneTime) {
    return This->lpVtbl->get_TombstoneTime(This,pTombstoneTime);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_get_TransmissionStarted(IWdsTransportNamespaceScheduledCast* This,VARIANT_BOOL *pbTransmissionStarted) {
    return This->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_Register(IWdsTransportNamespaceScheduledCast* This) {
    return This->lpVtbl->Register(This);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_Deregister(IWdsTransportNamespaceScheduledCast* This,VARIANT_BOOL bTerminateSessions) {
    return This->lpVtbl->Deregister(This,bTerminateSessions);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_Clone(IWdsTransportNamespaceScheduledCast* This,IWdsTransportNamespace **ppWdsTransportNamespaceClone) {
    return This->lpVtbl->Clone(This,ppWdsTransportNamespaceClone);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_Refresh(IWdsTransportNamespaceScheduledCast* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportNamespaceScheduledCast_RetrieveContents(IWdsTransportNamespaceScheduledCast* This,IWdsTransportCollection **ppWdsTransportContents) {
    return This->lpVtbl->RetrieveContents(This,ppWdsTransportContents);
}
/*** IWdsTransportNamespaceScheduledCast methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCast_StartTransmission(IWdsTransportNamespaceScheduledCast* This) {
    return This->lpVtbl->StartTransmission(This);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportNamespaceScheduledCast_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportNamespaceScheduledCastManualStart interface
 */
#ifndef __IWdsTransportNamespaceScheduledCastManualStart_INTERFACE_DEFINED__
#define __IWdsTransportNamespaceScheduledCastManualStart_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportNamespaceScheduledCastManualStart, 0x013e6e4c, 0xe6a7, 0x4fb5, 0xb7,0xff, 0xd9,0xf5,0xda,0x80,0x5c,0x31);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("013e6e4c-e6a7-4fb5-b7ff-d9f5da805c31")
IWdsTransportNamespaceScheduledCastManualStart : public IWdsTransportNamespaceScheduledCast
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportNamespaceScheduledCastManualStart, 0x013e6e4c, 0xe6a7, 0x4fb5, 0xb7,0xff, 0xd9,0xf5,0xda,0x80,0x5c,0x31)
#endif
#else
typedef struct IWdsTransportNamespaceScheduledCastManualStartVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportNamespaceScheduledCastManualStart *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportNamespaceScheduledCastManualStart *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportNamespace methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        WDSTRANSPORT_NAMESPACE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        ULONG *pulId);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR bszName);

    HRESULT (STDMETHODCALLTYPE *get_FriendlyName)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR *pbszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *put_FriendlyName)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR bszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR *pbszDescription);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR bszDescription);

    HRESULT (STDMETHODCALLTYPE *get_ContentProvider)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR *pbszContentProvider);

    HRESULT (STDMETHODCALLTYPE *put_ContentProvider)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR bszContentProvider);

    HRESULT (STDMETHODCALLTYPE *get_Configuration)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR *pbszConfiguration);

    HRESULT (STDMETHODCALLTYPE *put_Configuration)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        BSTR bszConfiguration);

    HRESULT (STDMETHODCALLTYPE *get_Registered)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        VARIANT_BOOL *pbRegistered);

    HRESULT (STDMETHODCALLTYPE *get_Tombstoned)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        VARIANT_BOOL *pbTombstoned);

    HRESULT (STDMETHODCALLTYPE *get_TombstoneTime)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        DATE *pTombstoneTime);

    HRESULT (STDMETHODCALLTYPE *get_TransmissionStarted)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        VARIANT_BOOL *pbTransmissionStarted);

    HRESULT (STDMETHODCALLTYPE *Register)(
        IWdsTransportNamespaceScheduledCastManualStart *This);

    HRESULT (STDMETHODCALLTYPE *Deregister)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        VARIANT_BOOL bTerminateSessions);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        IWdsTransportNamespace **ppWdsTransportNamespaceClone);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportNamespaceScheduledCastManualStart *This);

    HRESULT (STDMETHODCALLTYPE *RetrieveContents)(
        IWdsTransportNamespaceScheduledCastManualStart *This,
        IWdsTransportCollection **ppWdsTransportContents);

    /*** IWdsTransportNamespaceScheduledCast methods ***/
    HRESULT (STDMETHODCALLTYPE *StartTransmission)(
        IWdsTransportNamespaceScheduledCastManualStart *This);

    END_INTERFACE
} IWdsTransportNamespaceScheduledCastManualStartVtbl;

interface IWdsTransportNamespaceScheduledCastManualStart {
    CONST_VTBL IWdsTransportNamespaceScheduledCastManualStartVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportNamespaceScheduledCastManualStart_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportNamespaceScheduledCastManualStart_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportNamespaceScheduledCastManualStart_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportNamespaceScheduledCastManualStart_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportNamespaceScheduledCastManualStart_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportNamespaceScheduledCastManualStart_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportNamespaceScheduledCastManualStart_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportNamespace methods ***/
#define IWdsTransportNamespaceScheduledCastManualStart_get_Type(This,pType) (This)->lpVtbl->get_Type(This,pType)
#define IWdsTransportNamespaceScheduledCastManualStart_get_Id(This,pulId) (This)->lpVtbl->get_Id(This,pulId)
#define IWdsTransportNamespaceScheduledCastManualStart_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportNamespaceScheduledCastManualStart_put_Name(This,bszName) (This)->lpVtbl->put_Name(This,bszName)
#define IWdsTransportNamespaceScheduledCastManualStart_get_FriendlyName(This,pbszFriendlyName) (This)->lpVtbl->get_FriendlyName(This,pbszFriendlyName)
#define IWdsTransportNamespaceScheduledCastManualStart_put_FriendlyName(This,bszFriendlyName) (This)->lpVtbl->put_FriendlyName(This,bszFriendlyName)
#define IWdsTransportNamespaceScheduledCastManualStart_get_Description(This,pbszDescription) (This)->lpVtbl->get_Description(This,pbszDescription)
#define IWdsTransportNamespaceScheduledCastManualStart_put_Description(This,bszDescription) (This)->lpVtbl->put_Description(This,bszDescription)
#define IWdsTransportNamespaceScheduledCastManualStart_get_ContentProvider(This,pbszContentProvider) (This)->lpVtbl->get_ContentProvider(This,pbszContentProvider)
#define IWdsTransportNamespaceScheduledCastManualStart_put_ContentProvider(This,bszContentProvider) (This)->lpVtbl->put_ContentProvider(This,bszContentProvider)
#define IWdsTransportNamespaceScheduledCastManualStart_get_Configuration(This,pbszConfiguration) (This)->lpVtbl->get_Configuration(This,pbszConfiguration)
#define IWdsTransportNamespaceScheduledCastManualStart_put_Configuration(This,bszConfiguration) (This)->lpVtbl->put_Configuration(This,bszConfiguration)
#define IWdsTransportNamespaceScheduledCastManualStart_get_Registered(This,pbRegistered) (This)->lpVtbl->get_Registered(This,pbRegistered)
#define IWdsTransportNamespaceScheduledCastManualStart_get_Tombstoned(This,pbTombstoned) (This)->lpVtbl->get_Tombstoned(This,pbTombstoned)
#define IWdsTransportNamespaceScheduledCastManualStart_get_TombstoneTime(This,pTombstoneTime) (This)->lpVtbl->get_TombstoneTime(This,pTombstoneTime)
#define IWdsTransportNamespaceScheduledCastManualStart_get_TransmissionStarted(This,pbTransmissionStarted) (This)->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted)
#define IWdsTransportNamespaceScheduledCastManualStart_Register(This) (This)->lpVtbl->Register(This)
#define IWdsTransportNamespaceScheduledCastManualStart_Deregister(This,bTerminateSessions) (This)->lpVtbl->Deregister(This,bTerminateSessions)
#define IWdsTransportNamespaceScheduledCastManualStart_Clone(This,ppWdsTransportNamespaceClone) (This)->lpVtbl->Clone(This,ppWdsTransportNamespaceClone)
#define IWdsTransportNamespaceScheduledCastManualStart_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportNamespaceScheduledCastManualStart_RetrieveContents(This,ppWdsTransportContents) (This)->lpVtbl->RetrieveContents(This,ppWdsTransportContents)
/*** IWdsTransportNamespaceScheduledCast methods ***/
#define IWdsTransportNamespaceScheduledCastManualStart_StartTransmission(This) (This)->lpVtbl->StartTransmission(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_QueryInterface(IWdsTransportNamespaceScheduledCastManualStart* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportNamespaceScheduledCastManualStart_AddRef(IWdsTransportNamespaceScheduledCastManualStart* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportNamespaceScheduledCastManualStart_Release(IWdsTransportNamespaceScheduledCastManualStart* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_GetTypeInfoCount(IWdsTransportNamespaceScheduledCastManualStart* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_GetTypeInfo(IWdsTransportNamespaceScheduledCastManualStart* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_GetIDsOfNames(IWdsTransportNamespaceScheduledCastManualStart* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_Invoke(IWdsTransportNamespaceScheduledCastManualStart* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportNamespace methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_Type(IWdsTransportNamespaceScheduledCastManualStart* This,WDSTRANSPORT_NAMESPACE_TYPE *pType) {
    return This->lpVtbl->get_Type(This,pType);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_Id(IWdsTransportNamespaceScheduledCastManualStart* This,ULONG *pulId) {
    return This->lpVtbl->get_Id(This,pulId);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_Name(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_put_Name(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR bszName) {
    return This->lpVtbl->put_Name(This,bszName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_FriendlyName(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR *pbszFriendlyName) {
    return This->lpVtbl->get_FriendlyName(This,pbszFriendlyName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_put_FriendlyName(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR bszFriendlyName) {
    return This->lpVtbl->put_FriendlyName(This,bszFriendlyName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_Description(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR *pbszDescription) {
    return This->lpVtbl->get_Description(This,pbszDescription);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_put_Description(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR bszDescription) {
    return This->lpVtbl->put_Description(This,bszDescription);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_ContentProvider(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR *pbszContentProvider) {
    return This->lpVtbl->get_ContentProvider(This,pbszContentProvider);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_put_ContentProvider(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR bszContentProvider) {
    return This->lpVtbl->put_ContentProvider(This,bszContentProvider);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_Configuration(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR *pbszConfiguration) {
    return This->lpVtbl->get_Configuration(This,pbszConfiguration);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_put_Configuration(IWdsTransportNamespaceScheduledCastManualStart* This,BSTR bszConfiguration) {
    return This->lpVtbl->put_Configuration(This,bszConfiguration);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_Registered(IWdsTransportNamespaceScheduledCastManualStart* This,VARIANT_BOOL *pbRegistered) {
    return This->lpVtbl->get_Registered(This,pbRegistered);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_Tombstoned(IWdsTransportNamespaceScheduledCastManualStart* This,VARIANT_BOOL *pbTombstoned) {
    return This->lpVtbl->get_Tombstoned(This,pbTombstoned);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_TombstoneTime(IWdsTransportNamespaceScheduledCastManualStart* This,DATE *pTombstoneTime) {
    return This->lpVtbl->get_TombstoneTime(This,pTombstoneTime);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_get_TransmissionStarted(IWdsTransportNamespaceScheduledCastManualStart* This,VARIANT_BOOL *pbTransmissionStarted) {
    return This->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_Register(IWdsTransportNamespaceScheduledCastManualStart* This) {
    return This->lpVtbl->Register(This);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_Deregister(IWdsTransportNamespaceScheduledCastManualStart* This,VARIANT_BOOL bTerminateSessions) {
    return This->lpVtbl->Deregister(This,bTerminateSessions);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_Clone(IWdsTransportNamespaceScheduledCastManualStart* This,IWdsTransportNamespace **ppWdsTransportNamespaceClone) {
    return This->lpVtbl->Clone(This,ppWdsTransportNamespaceClone);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_Refresh(IWdsTransportNamespaceScheduledCastManualStart* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_RetrieveContents(IWdsTransportNamespaceScheduledCastManualStart* This,IWdsTransportCollection **ppWdsTransportContents) {
    return This->lpVtbl->RetrieveContents(This,ppWdsTransportContents);
}
/*** IWdsTransportNamespaceScheduledCast methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastManualStart_StartTransmission(IWdsTransportNamespaceScheduledCastManualStart* This) {
    return This->lpVtbl->StartTransmission(This);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportNamespaceScheduledCastManualStart_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportNamespaceScheduledCastAutoStart interface
 */
#ifndef __IWdsTransportNamespaceScheduledCastAutoStart_INTERFACE_DEFINED__
#define __IWdsTransportNamespaceScheduledCastAutoStart_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportNamespaceScheduledCastAutoStart, 0xd606af3d, 0xea9c, 0x4219, 0x96,0x1e, 0x74,0x91,0xd6,0x18,0xd9,0xb9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d606af3d-ea9c-4219-961e-7491d618d9b9")
IWdsTransportNamespaceScheduledCastAutoStart : public IWdsTransportNamespaceScheduledCast
{
    virtual HRESULT STDMETHODCALLTYPE get_MinimumClients(
        ULONG *pulMinimumClients) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MinimumClients(
        ULONG ulMinimumClients) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StartTime(
        DATE *pStartTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StartTime(
        DATE StartTime) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportNamespaceScheduledCastAutoStart, 0xd606af3d, 0xea9c, 0x4219, 0x96,0x1e, 0x74,0x91,0xd6,0x18,0xd9,0xb9)
#endif
#else
typedef struct IWdsTransportNamespaceScheduledCastAutoStartVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportNamespaceScheduledCastAutoStart *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportNamespaceScheduledCastAutoStart *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportNamespace methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        WDSTRANSPORT_NAMESPACE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        ULONG *pulId);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR bszName);

    HRESULT (STDMETHODCALLTYPE *get_FriendlyName)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR *pbszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *put_FriendlyName)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR bszFriendlyName);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR *pbszDescription);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR bszDescription);

    HRESULT (STDMETHODCALLTYPE *get_ContentProvider)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR *pbszContentProvider);

    HRESULT (STDMETHODCALLTYPE *put_ContentProvider)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR bszContentProvider);

    HRESULT (STDMETHODCALLTYPE *get_Configuration)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR *pbszConfiguration);

    HRESULT (STDMETHODCALLTYPE *put_Configuration)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        BSTR bszConfiguration);

    HRESULT (STDMETHODCALLTYPE *get_Registered)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        VARIANT_BOOL *pbRegistered);

    HRESULT (STDMETHODCALLTYPE *get_Tombstoned)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        VARIANT_BOOL *pbTombstoned);

    HRESULT (STDMETHODCALLTYPE *get_TombstoneTime)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        DATE *pTombstoneTime);

    HRESULT (STDMETHODCALLTYPE *get_TransmissionStarted)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        VARIANT_BOOL *pbTransmissionStarted);

    HRESULT (STDMETHODCALLTYPE *Register)(
        IWdsTransportNamespaceScheduledCastAutoStart *This);

    HRESULT (STDMETHODCALLTYPE *Deregister)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        VARIANT_BOOL bTerminateSessions);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        IWdsTransportNamespace **ppWdsTransportNamespaceClone);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportNamespaceScheduledCastAutoStart *This);

    HRESULT (STDMETHODCALLTYPE *RetrieveContents)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        IWdsTransportCollection **ppWdsTransportContents);

    /*** IWdsTransportNamespaceScheduledCast methods ***/
    HRESULT (STDMETHODCALLTYPE *StartTransmission)(
        IWdsTransportNamespaceScheduledCastAutoStart *This);

    /*** IWdsTransportNamespaceScheduledCastAutoStart methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MinimumClients)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        ULONG *pulMinimumClients);

    HRESULT (STDMETHODCALLTYPE *put_MinimumClients)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        ULONG ulMinimumClients);

    HRESULT (STDMETHODCALLTYPE *get_StartTime)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        DATE *pStartTime);

    HRESULT (STDMETHODCALLTYPE *put_StartTime)(
        IWdsTransportNamespaceScheduledCastAutoStart *This,
        DATE StartTime);

    END_INTERFACE
} IWdsTransportNamespaceScheduledCastAutoStartVtbl;

interface IWdsTransportNamespaceScheduledCastAutoStart {
    CONST_VTBL IWdsTransportNamespaceScheduledCastAutoStartVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportNamespaceScheduledCastAutoStart_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportNamespaceScheduledCastAutoStart_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportNamespaceScheduledCastAutoStart_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportNamespaceScheduledCastAutoStart_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportNamespaceScheduledCastAutoStart_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportNamespaceScheduledCastAutoStart_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportNamespaceScheduledCastAutoStart_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportNamespace methods ***/
#define IWdsTransportNamespaceScheduledCastAutoStart_get_Type(This,pType) (This)->lpVtbl->get_Type(This,pType)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_Id(This,pulId) (This)->lpVtbl->get_Id(This,pulId)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportNamespaceScheduledCastAutoStart_put_Name(This,bszName) (This)->lpVtbl->put_Name(This,bszName)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_FriendlyName(This,pbszFriendlyName) (This)->lpVtbl->get_FriendlyName(This,pbszFriendlyName)
#define IWdsTransportNamespaceScheduledCastAutoStart_put_FriendlyName(This,bszFriendlyName) (This)->lpVtbl->put_FriendlyName(This,bszFriendlyName)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_Description(This,pbszDescription) (This)->lpVtbl->get_Description(This,pbszDescription)
#define IWdsTransportNamespaceScheduledCastAutoStart_put_Description(This,bszDescription) (This)->lpVtbl->put_Description(This,bszDescription)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_ContentProvider(This,pbszContentProvider) (This)->lpVtbl->get_ContentProvider(This,pbszContentProvider)
#define IWdsTransportNamespaceScheduledCastAutoStart_put_ContentProvider(This,bszContentProvider) (This)->lpVtbl->put_ContentProvider(This,bszContentProvider)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_Configuration(This,pbszConfiguration) (This)->lpVtbl->get_Configuration(This,pbszConfiguration)
#define IWdsTransportNamespaceScheduledCastAutoStart_put_Configuration(This,bszConfiguration) (This)->lpVtbl->put_Configuration(This,bszConfiguration)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_Registered(This,pbRegistered) (This)->lpVtbl->get_Registered(This,pbRegistered)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_Tombstoned(This,pbTombstoned) (This)->lpVtbl->get_Tombstoned(This,pbTombstoned)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_TombstoneTime(This,pTombstoneTime) (This)->lpVtbl->get_TombstoneTime(This,pTombstoneTime)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_TransmissionStarted(This,pbTransmissionStarted) (This)->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted)
#define IWdsTransportNamespaceScheduledCastAutoStart_Register(This) (This)->lpVtbl->Register(This)
#define IWdsTransportNamespaceScheduledCastAutoStart_Deregister(This,bTerminateSessions) (This)->lpVtbl->Deregister(This,bTerminateSessions)
#define IWdsTransportNamespaceScheduledCastAutoStart_Clone(This,ppWdsTransportNamespaceClone) (This)->lpVtbl->Clone(This,ppWdsTransportNamespaceClone)
#define IWdsTransportNamespaceScheduledCastAutoStart_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportNamespaceScheduledCastAutoStart_RetrieveContents(This,ppWdsTransportContents) (This)->lpVtbl->RetrieveContents(This,ppWdsTransportContents)
/*** IWdsTransportNamespaceScheduledCast methods ***/
#define IWdsTransportNamespaceScheduledCastAutoStart_StartTransmission(This) (This)->lpVtbl->StartTransmission(This)
/*** IWdsTransportNamespaceScheduledCastAutoStart methods ***/
#define IWdsTransportNamespaceScheduledCastAutoStart_get_MinimumClients(This,pulMinimumClients) (This)->lpVtbl->get_MinimumClients(This,pulMinimumClients)
#define IWdsTransportNamespaceScheduledCastAutoStart_put_MinimumClients(This,ulMinimumClients) (This)->lpVtbl->put_MinimumClients(This,ulMinimumClients)
#define IWdsTransportNamespaceScheduledCastAutoStart_get_StartTime(This,pStartTime) (This)->lpVtbl->get_StartTime(This,pStartTime)
#define IWdsTransportNamespaceScheduledCastAutoStart_put_StartTime(This,StartTime) (This)->lpVtbl->put_StartTime(This,StartTime)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_QueryInterface(IWdsTransportNamespaceScheduledCastAutoStart* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportNamespaceScheduledCastAutoStart_AddRef(IWdsTransportNamespaceScheduledCastAutoStart* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportNamespaceScheduledCastAutoStart_Release(IWdsTransportNamespaceScheduledCastAutoStart* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_GetTypeInfoCount(IWdsTransportNamespaceScheduledCastAutoStart* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_GetTypeInfo(IWdsTransportNamespaceScheduledCastAutoStart* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_GetIDsOfNames(IWdsTransportNamespaceScheduledCastAutoStart* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_Invoke(IWdsTransportNamespaceScheduledCastAutoStart* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportNamespace methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_Type(IWdsTransportNamespaceScheduledCastAutoStart* This,WDSTRANSPORT_NAMESPACE_TYPE *pType) {
    return This->lpVtbl->get_Type(This,pType);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_Id(IWdsTransportNamespaceScheduledCastAutoStart* This,ULONG *pulId) {
    return This->lpVtbl->get_Id(This,pulId);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_Name(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_put_Name(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR bszName) {
    return This->lpVtbl->put_Name(This,bszName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_FriendlyName(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR *pbszFriendlyName) {
    return This->lpVtbl->get_FriendlyName(This,pbszFriendlyName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_put_FriendlyName(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR bszFriendlyName) {
    return This->lpVtbl->put_FriendlyName(This,bszFriendlyName);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_Description(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR *pbszDescription) {
    return This->lpVtbl->get_Description(This,pbszDescription);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_put_Description(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR bszDescription) {
    return This->lpVtbl->put_Description(This,bszDescription);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_ContentProvider(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR *pbszContentProvider) {
    return This->lpVtbl->get_ContentProvider(This,pbszContentProvider);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_put_ContentProvider(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR bszContentProvider) {
    return This->lpVtbl->put_ContentProvider(This,bszContentProvider);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_Configuration(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR *pbszConfiguration) {
    return This->lpVtbl->get_Configuration(This,pbszConfiguration);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_put_Configuration(IWdsTransportNamespaceScheduledCastAutoStart* This,BSTR bszConfiguration) {
    return This->lpVtbl->put_Configuration(This,bszConfiguration);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_Registered(IWdsTransportNamespaceScheduledCastAutoStart* This,VARIANT_BOOL *pbRegistered) {
    return This->lpVtbl->get_Registered(This,pbRegistered);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_Tombstoned(IWdsTransportNamespaceScheduledCastAutoStart* This,VARIANT_BOOL *pbTombstoned) {
    return This->lpVtbl->get_Tombstoned(This,pbTombstoned);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_TombstoneTime(IWdsTransportNamespaceScheduledCastAutoStart* This,DATE *pTombstoneTime) {
    return This->lpVtbl->get_TombstoneTime(This,pTombstoneTime);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_TransmissionStarted(IWdsTransportNamespaceScheduledCastAutoStart* This,VARIANT_BOOL *pbTransmissionStarted) {
    return This->lpVtbl->get_TransmissionStarted(This,pbTransmissionStarted);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_Register(IWdsTransportNamespaceScheduledCastAutoStart* This) {
    return This->lpVtbl->Register(This);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_Deregister(IWdsTransportNamespaceScheduledCastAutoStart* This,VARIANT_BOOL bTerminateSessions) {
    return This->lpVtbl->Deregister(This,bTerminateSessions);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_Clone(IWdsTransportNamespaceScheduledCastAutoStart* This,IWdsTransportNamespace **ppWdsTransportNamespaceClone) {
    return This->lpVtbl->Clone(This,ppWdsTransportNamespaceClone);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_Refresh(IWdsTransportNamespaceScheduledCastAutoStart* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_RetrieveContents(IWdsTransportNamespaceScheduledCastAutoStart* This,IWdsTransportCollection **ppWdsTransportContents) {
    return This->lpVtbl->RetrieveContents(This,ppWdsTransportContents);
}
/*** IWdsTransportNamespaceScheduledCast methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_StartTransmission(IWdsTransportNamespaceScheduledCastAutoStart* This) {
    return This->lpVtbl->StartTransmission(This);
}
/*** IWdsTransportNamespaceScheduledCastAutoStart methods ***/
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_MinimumClients(IWdsTransportNamespaceScheduledCastAutoStart* This,ULONG *pulMinimumClients) {
    return This->lpVtbl->get_MinimumClients(This,pulMinimumClients);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_put_MinimumClients(IWdsTransportNamespaceScheduledCastAutoStart* This,ULONG ulMinimumClients) {
    return This->lpVtbl->put_MinimumClients(This,ulMinimumClients);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_get_StartTime(IWdsTransportNamespaceScheduledCastAutoStart* This,DATE *pStartTime) {
    return This->lpVtbl->get_StartTime(This,pStartTime);
}
static inline HRESULT IWdsTransportNamespaceScheduledCastAutoStart_put_StartTime(IWdsTransportNamespaceScheduledCastAutoStart* This,DATE StartTime) {
    return This->lpVtbl->put_StartTime(This,StartTime);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportNamespaceScheduledCastAutoStart_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportNamespaceManager interface
 */
#ifndef __IWdsTransportNamespaceManager_INTERFACE_DEFINED__
#define __IWdsTransportNamespaceManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportNamespaceManager, 0x3e22d9f6, 0x3777, 0x4d98, 0x83,0xe1, 0xf9,0x86,0x96,0x71,0x7b,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3e22d9f6-3777-4d98-83e1-f98696717ba3")
IWdsTransportNamespaceManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE CreateNamespace(
        WDSTRANSPORT_NAMESPACE_TYPE NamespaceType,
        BSTR bszNamespaceName,
        BSTR bszContentProvider,
        BSTR bszConfiguration,
        IWdsTransportNamespace **ppWdsTransportNamespace) = 0;

    virtual HRESULT STDMETHODCALLTYPE RetrieveNamespace(
        BSTR bszNamespaceName,
        IWdsTransportNamespace **ppWdsTransportNamespace) = 0;

    virtual HRESULT STDMETHODCALLTYPE RetrieveNamespaces(
        BSTR bszContentProvider,
        BSTR bszNamespaceName,
        VARIANT_BOOL bIncludeTombstones,
        IWdsTransportCollection **ppWdsTransportNamespaces) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportNamespaceManager, 0x3e22d9f6, 0x3777, 0x4d98, 0x83,0xe1, 0xf9,0x86,0x96,0x71,0x7b,0xa3)
#endif
#else
typedef struct IWdsTransportNamespaceManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportNamespaceManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportNamespaceManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportNamespaceManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportNamespaceManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportNamespaceManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportNamespaceManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportNamespaceManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportNamespaceManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateNamespace)(
        IWdsTransportNamespaceManager *This,
        WDSTRANSPORT_NAMESPACE_TYPE NamespaceType,
        BSTR bszNamespaceName,
        BSTR bszContentProvider,
        BSTR bszConfiguration,
        IWdsTransportNamespace **ppWdsTransportNamespace);

    HRESULT (STDMETHODCALLTYPE *RetrieveNamespace)(
        IWdsTransportNamespaceManager *This,
        BSTR bszNamespaceName,
        IWdsTransportNamespace **ppWdsTransportNamespace);

    HRESULT (STDMETHODCALLTYPE *RetrieveNamespaces)(
        IWdsTransportNamespaceManager *This,
        BSTR bszContentProvider,
        BSTR bszNamespaceName,
        VARIANT_BOOL bIncludeTombstones,
        IWdsTransportCollection **ppWdsTransportNamespaces);

    END_INTERFACE
} IWdsTransportNamespaceManagerVtbl;

interface IWdsTransportNamespaceManager {
    CONST_VTBL IWdsTransportNamespaceManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportNamespaceManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportNamespaceManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportNamespaceManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportNamespaceManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportNamespaceManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportNamespaceManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportNamespaceManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportNamespaceManager methods ***/
#define IWdsTransportNamespaceManager_CreateNamespace(This,NamespaceType,bszNamespaceName,bszContentProvider,bszConfiguration,ppWdsTransportNamespace) (This)->lpVtbl->CreateNamespace(This,NamespaceType,bszNamespaceName,bszContentProvider,bszConfiguration,ppWdsTransportNamespace)
#define IWdsTransportNamespaceManager_RetrieveNamespace(This,bszNamespaceName,ppWdsTransportNamespace) (This)->lpVtbl->RetrieveNamespace(This,bszNamespaceName,ppWdsTransportNamespace)
#define IWdsTransportNamespaceManager_RetrieveNamespaces(This,bszContentProvider,bszNamespaceName,bIncludeTombstones,ppWdsTransportNamespaces) (This)->lpVtbl->RetrieveNamespaces(This,bszContentProvider,bszNamespaceName,bIncludeTombstones,ppWdsTransportNamespaces)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportNamespaceManager_QueryInterface(IWdsTransportNamespaceManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportNamespaceManager_AddRef(IWdsTransportNamespaceManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportNamespaceManager_Release(IWdsTransportNamespaceManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportNamespaceManager_GetTypeInfoCount(IWdsTransportNamespaceManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportNamespaceManager_GetTypeInfo(IWdsTransportNamespaceManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportNamespaceManager_GetIDsOfNames(IWdsTransportNamespaceManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportNamespaceManager_Invoke(IWdsTransportNamespaceManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportNamespaceManager methods ***/
static inline HRESULT IWdsTransportNamespaceManager_CreateNamespace(IWdsTransportNamespaceManager* This,WDSTRANSPORT_NAMESPACE_TYPE NamespaceType,BSTR bszNamespaceName,BSTR bszContentProvider,BSTR bszConfiguration,IWdsTransportNamespace **ppWdsTransportNamespace) {
    return This->lpVtbl->CreateNamespace(This,NamespaceType,bszNamespaceName,bszContentProvider,bszConfiguration,ppWdsTransportNamespace);
}
static inline HRESULT IWdsTransportNamespaceManager_RetrieveNamespace(IWdsTransportNamespaceManager* This,BSTR bszNamespaceName,IWdsTransportNamespace **ppWdsTransportNamespace) {
    return This->lpVtbl->RetrieveNamespace(This,bszNamespaceName,ppWdsTransportNamespace);
}
static inline HRESULT IWdsTransportNamespaceManager_RetrieveNamespaces(IWdsTransportNamespaceManager* This,BSTR bszContentProvider,BSTR bszNamespaceName,VARIANT_BOOL bIncludeTombstones,IWdsTransportCollection **ppWdsTransportNamespaces) {
    return This->lpVtbl->RetrieveNamespaces(This,bszContentProvider,bszNamespaceName,bIncludeTombstones,ppWdsTransportNamespaces);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportNamespaceManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportServer interface
 */
#ifndef __IWdsTransportServer_INTERFACE_DEFINED__
#define __IWdsTransportServer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportServer, 0x09ccd093, 0x830d, 0x4344, 0xa3,0x0a, 0x73,0xae,0x8e,0x8f,0xca,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("09ccd093-830d-4344-a30a-73ae8e8fca90")
IWdsTransportServer : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SetupManager(
        IWdsTransportSetupManager **ppWdsTransportSetupManager) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ConfigurationManager(
        IWdsTransportConfigurationManager **ppWdsTransportConfigurationManager) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NamespaceManager(
        IWdsTransportNamespaceManager **ppWdsTransportNamespaceManager) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisconnectClient(
        ULONG ulClientId,
        WDSTRANSPORT_DISCONNECT_TYPE DisconnectionType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportServer, 0x09ccd093, 0x830d, 0x4344, 0xa3,0x0a, 0x73,0xae,0x8e,0x8f,0xca,0x90)
#endif
#else
typedef struct IWdsTransportServerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportServer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportServer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportServer *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportServer *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportServer *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportServer *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportServer *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportServer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportServer *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *get_SetupManager)(
        IWdsTransportServer *This,
        IWdsTransportSetupManager **ppWdsTransportSetupManager);

    HRESULT (STDMETHODCALLTYPE *get_ConfigurationManager)(
        IWdsTransportServer *This,
        IWdsTransportConfigurationManager **ppWdsTransportConfigurationManager);

    HRESULT (STDMETHODCALLTYPE *get_NamespaceManager)(
        IWdsTransportServer *This,
        IWdsTransportNamespaceManager **ppWdsTransportNamespaceManager);

    HRESULT (STDMETHODCALLTYPE *DisconnectClient)(
        IWdsTransportServer *This,
        ULONG ulClientId,
        WDSTRANSPORT_DISCONNECT_TYPE DisconnectionType);

    END_INTERFACE
} IWdsTransportServerVtbl;

interface IWdsTransportServer {
    CONST_VTBL IWdsTransportServerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportServer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportServer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportServer_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportServer_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportServer_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportServer_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportServer_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportServer methods ***/
#define IWdsTransportServer_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportServer_get_SetupManager(This,ppWdsTransportSetupManager) (This)->lpVtbl->get_SetupManager(This,ppWdsTransportSetupManager)
#define IWdsTransportServer_get_ConfigurationManager(This,ppWdsTransportConfigurationManager) (This)->lpVtbl->get_ConfigurationManager(This,ppWdsTransportConfigurationManager)
#define IWdsTransportServer_get_NamespaceManager(This,ppWdsTransportNamespaceManager) (This)->lpVtbl->get_NamespaceManager(This,ppWdsTransportNamespaceManager)
#define IWdsTransportServer_DisconnectClient(This,ulClientId,DisconnectionType) (This)->lpVtbl->DisconnectClient(This,ulClientId,DisconnectionType)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportServer_QueryInterface(IWdsTransportServer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportServer_AddRef(IWdsTransportServer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportServer_Release(IWdsTransportServer* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportServer_GetTypeInfoCount(IWdsTransportServer* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportServer_GetTypeInfo(IWdsTransportServer* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportServer_GetIDsOfNames(IWdsTransportServer* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportServer_Invoke(IWdsTransportServer* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportServer methods ***/
static inline HRESULT IWdsTransportServer_get_Name(IWdsTransportServer* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportServer_get_SetupManager(IWdsTransportServer* This,IWdsTransportSetupManager **ppWdsTransportSetupManager) {
    return This->lpVtbl->get_SetupManager(This,ppWdsTransportSetupManager);
}
static inline HRESULT IWdsTransportServer_get_ConfigurationManager(IWdsTransportServer* This,IWdsTransportConfigurationManager **ppWdsTransportConfigurationManager) {
    return This->lpVtbl->get_ConfigurationManager(This,ppWdsTransportConfigurationManager);
}
static inline HRESULT IWdsTransportServer_get_NamespaceManager(IWdsTransportServer* This,IWdsTransportNamespaceManager **ppWdsTransportNamespaceManager) {
    return This->lpVtbl->get_NamespaceManager(This,ppWdsTransportNamespaceManager);
}
static inline HRESULT IWdsTransportServer_DisconnectClient(IWdsTransportServer* This,ULONG ulClientId,WDSTRANSPORT_DISCONNECT_TYPE DisconnectionType) {
    return This->lpVtbl->DisconnectClient(This,ulClientId,DisconnectionType);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportServer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportServer2 interface
 */
#ifndef __IWdsTransportServer2_INTERFACE_DEFINED__
#define __IWdsTransportServer2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportServer2, 0x256e999f, 0x6df4, 0x4538, 0x81,0xb9, 0x85,0x7b,0x9a,0xb8,0xfb,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("256e999f-6df4-4538-81b9-857b9ab8fb47")
IWdsTransportServer2 : public IWdsTransportServer
{
    virtual HRESULT STDMETHODCALLTYPE get_TftpManager(
        IWdsTransportTftpManager **ppWdsTransportTftpManager) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportServer2, 0x256e999f, 0x6df4, 0x4538, 0x81,0xb9, 0x85,0x7b,0x9a,0xb8,0xfb,0x47)
#endif
#else
typedef struct IWdsTransportServer2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportServer2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportServer2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportServer2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportServer2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportServer2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportServer2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportServer2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportServer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportServer2 *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *get_SetupManager)(
        IWdsTransportServer2 *This,
        IWdsTransportSetupManager **ppWdsTransportSetupManager);

    HRESULT (STDMETHODCALLTYPE *get_ConfigurationManager)(
        IWdsTransportServer2 *This,
        IWdsTransportConfigurationManager **ppWdsTransportConfigurationManager);

    HRESULT (STDMETHODCALLTYPE *get_NamespaceManager)(
        IWdsTransportServer2 *This,
        IWdsTransportNamespaceManager **ppWdsTransportNamespaceManager);

    HRESULT (STDMETHODCALLTYPE *DisconnectClient)(
        IWdsTransportServer2 *This,
        ULONG ulClientId,
        WDSTRANSPORT_DISCONNECT_TYPE DisconnectionType);

    /*** IWdsTransportServer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TftpManager)(
        IWdsTransportServer2 *This,
        IWdsTransportTftpManager **ppWdsTransportTftpManager);

    END_INTERFACE
} IWdsTransportServer2Vtbl;

interface IWdsTransportServer2 {
    CONST_VTBL IWdsTransportServer2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportServer2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportServer2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportServer2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportServer2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportServer2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportServer2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportServer2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportServer methods ***/
#define IWdsTransportServer2_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportServer2_get_SetupManager(This,ppWdsTransportSetupManager) (This)->lpVtbl->get_SetupManager(This,ppWdsTransportSetupManager)
#define IWdsTransportServer2_get_ConfigurationManager(This,ppWdsTransportConfigurationManager) (This)->lpVtbl->get_ConfigurationManager(This,ppWdsTransportConfigurationManager)
#define IWdsTransportServer2_get_NamespaceManager(This,ppWdsTransportNamespaceManager) (This)->lpVtbl->get_NamespaceManager(This,ppWdsTransportNamespaceManager)
#define IWdsTransportServer2_DisconnectClient(This,ulClientId,DisconnectionType) (This)->lpVtbl->DisconnectClient(This,ulClientId,DisconnectionType)
/*** IWdsTransportServer2 methods ***/
#define IWdsTransportServer2_get_TftpManager(This,ppWdsTransportTftpManager) (This)->lpVtbl->get_TftpManager(This,ppWdsTransportTftpManager)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportServer2_QueryInterface(IWdsTransportServer2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportServer2_AddRef(IWdsTransportServer2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportServer2_Release(IWdsTransportServer2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportServer2_GetTypeInfoCount(IWdsTransportServer2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportServer2_GetTypeInfo(IWdsTransportServer2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportServer2_GetIDsOfNames(IWdsTransportServer2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportServer2_Invoke(IWdsTransportServer2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportServer methods ***/
static inline HRESULT IWdsTransportServer2_get_Name(IWdsTransportServer2* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportServer2_get_SetupManager(IWdsTransportServer2* This,IWdsTransportSetupManager **ppWdsTransportSetupManager) {
    return This->lpVtbl->get_SetupManager(This,ppWdsTransportSetupManager);
}
static inline HRESULT IWdsTransportServer2_get_ConfigurationManager(IWdsTransportServer2* This,IWdsTransportConfigurationManager **ppWdsTransportConfigurationManager) {
    return This->lpVtbl->get_ConfigurationManager(This,ppWdsTransportConfigurationManager);
}
static inline HRESULT IWdsTransportServer2_get_NamespaceManager(IWdsTransportServer2* This,IWdsTransportNamespaceManager **ppWdsTransportNamespaceManager) {
    return This->lpVtbl->get_NamespaceManager(This,ppWdsTransportNamespaceManager);
}
static inline HRESULT IWdsTransportServer2_DisconnectClient(IWdsTransportServer2* This,ULONG ulClientId,WDSTRANSPORT_DISCONNECT_TYPE DisconnectionType) {
    return This->lpVtbl->DisconnectClient(This,ulClientId,DisconnectionType);
}
/*** IWdsTransportServer2 methods ***/
static inline HRESULT IWdsTransportServer2_get_TftpManager(IWdsTransportServer2* This,IWdsTransportTftpManager **ppWdsTransportTftpManager) {
    return This->lpVtbl->get_TftpManager(This,ppWdsTransportTftpManager);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportServer2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportServicePolicy interface
 */
#ifndef __IWdsTransportServicePolicy_INTERFACE_DEFINED__
#define __IWdsTransportServicePolicy_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportServicePolicy, 0xb9468578, 0x9f2b, 0x48cc, 0xb2,0x7a, 0xa6,0x07,0x99,0xc2,0x75,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b9468578-9f2b-48cc-b27a-a60799c2750c")
IWdsTransportServicePolicy : public IWdsTransportCacheable
{
    virtual HRESULT STDMETHODCALLTYPE get_IpAddressSource(
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE *pSourceType) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IpAddressSource(
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE SourceType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StartIpAddress(
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR *pbszStartIpAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StartIpAddress(
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR bszStartIpAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EndIpAddress(
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR *pbszEndIpAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EndIpAddress(
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR bszEndIpAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StartPort(
        ULONG *pulStartPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StartPort(
        ULONG ulStartPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EndPort(
        ULONG *pulEndPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EndPort(
        ULONG ulEndPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NetworkProfile(
        WDSTRANSPORT_NETWORK_PROFILE_TYPE *pProfileType) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_NetworkProfile(
        WDSTRANSPORT_NETWORK_PROFILE_TYPE ProfileType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportServicePolicy, 0xb9468578, 0x9f2b, 0x48cc, 0xb2,0x7a, 0xa6,0x07,0x99,0xc2,0x75,0x0c)
#endif
#else
typedef struct IWdsTransportServicePolicyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportServicePolicy *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportServicePolicy *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportServicePolicy *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportServicePolicy *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportServicePolicy *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportServicePolicy *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportServicePolicy *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportCacheable methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Dirty)(
        IWdsTransportServicePolicy *This,
        VARIANT_BOOL *pbDirty);

    HRESULT (STDMETHODCALLTYPE *Discard)(
        IWdsTransportServicePolicy *This);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportServicePolicy *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWdsTransportServicePolicy *This);

    /*** IWdsTransportServicePolicy methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IpAddressSource)(
        IWdsTransportServicePolicy *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE *pSourceType);

    HRESULT (STDMETHODCALLTYPE *put_IpAddressSource)(
        IWdsTransportServicePolicy *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE SourceType);

    HRESULT (STDMETHODCALLTYPE *get_StartIpAddress)(
        IWdsTransportServicePolicy *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR *pbszStartIpAddress);

    HRESULT (STDMETHODCALLTYPE *put_StartIpAddress)(
        IWdsTransportServicePolicy *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR bszStartIpAddress);

    HRESULT (STDMETHODCALLTYPE *get_EndIpAddress)(
        IWdsTransportServicePolicy *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR *pbszEndIpAddress);

    HRESULT (STDMETHODCALLTYPE *put_EndIpAddress)(
        IWdsTransportServicePolicy *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR bszEndIpAddress);

    HRESULT (STDMETHODCALLTYPE *get_StartPort)(
        IWdsTransportServicePolicy *This,
        ULONG *pulStartPort);

    HRESULT (STDMETHODCALLTYPE *put_StartPort)(
        IWdsTransportServicePolicy *This,
        ULONG ulStartPort);

    HRESULT (STDMETHODCALLTYPE *get_EndPort)(
        IWdsTransportServicePolicy *This,
        ULONG *pulEndPort);

    HRESULT (STDMETHODCALLTYPE *put_EndPort)(
        IWdsTransportServicePolicy *This,
        ULONG ulEndPort);

    HRESULT (STDMETHODCALLTYPE *get_NetworkProfile)(
        IWdsTransportServicePolicy *This,
        WDSTRANSPORT_NETWORK_PROFILE_TYPE *pProfileType);

    HRESULT (STDMETHODCALLTYPE *put_NetworkProfile)(
        IWdsTransportServicePolicy *This,
        WDSTRANSPORT_NETWORK_PROFILE_TYPE ProfileType);

    END_INTERFACE
} IWdsTransportServicePolicyVtbl;

interface IWdsTransportServicePolicy {
    CONST_VTBL IWdsTransportServicePolicyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportServicePolicy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportServicePolicy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportServicePolicy_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportServicePolicy_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportServicePolicy_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportServicePolicy_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportServicePolicy_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportCacheable methods ***/
#define IWdsTransportServicePolicy_get_Dirty(This,pbDirty) (This)->lpVtbl->get_Dirty(This,pbDirty)
#define IWdsTransportServicePolicy_Discard(This) (This)->lpVtbl->Discard(This)
#define IWdsTransportServicePolicy_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportServicePolicy_Commit(This) (This)->lpVtbl->Commit(This)
/*** IWdsTransportServicePolicy methods ***/
#define IWdsTransportServicePolicy_get_IpAddressSource(This,AddressType,pSourceType) (This)->lpVtbl->get_IpAddressSource(This,AddressType,pSourceType)
#define IWdsTransportServicePolicy_put_IpAddressSource(This,AddressType,SourceType) (This)->lpVtbl->put_IpAddressSource(This,AddressType,SourceType)
#define IWdsTransportServicePolicy_get_StartIpAddress(This,AddressType,pbszStartIpAddress) (This)->lpVtbl->get_StartIpAddress(This,AddressType,pbszStartIpAddress)
#define IWdsTransportServicePolicy_put_StartIpAddress(This,AddressType,bszStartIpAddress) (This)->lpVtbl->put_StartIpAddress(This,AddressType,bszStartIpAddress)
#define IWdsTransportServicePolicy_get_EndIpAddress(This,AddressType,pbszEndIpAddress) (This)->lpVtbl->get_EndIpAddress(This,AddressType,pbszEndIpAddress)
#define IWdsTransportServicePolicy_put_EndIpAddress(This,AddressType,bszEndIpAddress) (This)->lpVtbl->put_EndIpAddress(This,AddressType,bszEndIpAddress)
#define IWdsTransportServicePolicy_get_StartPort(This,pulStartPort) (This)->lpVtbl->get_StartPort(This,pulStartPort)
#define IWdsTransportServicePolicy_put_StartPort(This,ulStartPort) (This)->lpVtbl->put_StartPort(This,ulStartPort)
#define IWdsTransportServicePolicy_get_EndPort(This,pulEndPort) (This)->lpVtbl->get_EndPort(This,pulEndPort)
#define IWdsTransportServicePolicy_put_EndPort(This,ulEndPort) (This)->lpVtbl->put_EndPort(This,ulEndPort)
#define IWdsTransportServicePolicy_get_NetworkProfile(This,pProfileType) (This)->lpVtbl->get_NetworkProfile(This,pProfileType)
#define IWdsTransportServicePolicy_put_NetworkProfile(This,ProfileType) (This)->lpVtbl->put_NetworkProfile(This,ProfileType)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportServicePolicy_QueryInterface(IWdsTransportServicePolicy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportServicePolicy_AddRef(IWdsTransportServicePolicy* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportServicePolicy_Release(IWdsTransportServicePolicy* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportServicePolicy_GetTypeInfoCount(IWdsTransportServicePolicy* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportServicePolicy_GetTypeInfo(IWdsTransportServicePolicy* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportServicePolicy_GetIDsOfNames(IWdsTransportServicePolicy* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportServicePolicy_Invoke(IWdsTransportServicePolicy* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportCacheable methods ***/
static inline HRESULT IWdsTransportServicePolicy_get_Dirty(IWdsTransportServicePolicy* This,VARIANT_BOOL *pbDirty) {
    return This->lpVtbl->get_Dirty(This,pbDirty);
}
static inline HRESULT IWdsTransportServicePolicy_Discard(IWdsTransportServicePolicy* This) {
    return This->lpVtbl->Discard(This);
}
static inline HRESULT IWdsTransportServicePolicy_Refresh(IWdsTransportServicePolicy* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportServicePolicy_Commit(IWdsTransportServicePolicy* This) {
    return This->lpVtbl->Commit(This);
}
/*** IWdsTransportServicePolicy methods ***/
static inline HRESULT IWdsTransportServicePolicy_get_IpAddressSource(IWdsTransportServicePolicy* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE *pSourceType) {
    return This->lpVtbl->get_IpAddressSource(This,AddressType,pSourceType);
}
static inline HRESULT IWdsTransportServicePolicy_put_IpAddressSource(IWdsTransportServicePolicy* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE SourceType) {
    return This->lpVtbl->put_IpAddressSource(This,AddressType,SourceType);
}
static inline HRESULT IWdsTransportServicePolicy_get_StartIpAddress(IWdsTransportServicePolicy* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,BSTR *pbszStartIpAddress) {
    return This->lpVtbl->get_StartIpAddress(This,AddressType,pbszStartIpAddress);
}
static inline HRESULT IWdsTransportServicePolicy_put_StartIpAddress(IWdsTransportServicePolicy* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,BSTR bszStartIpAddress) {
    return This->lpVtbl->put_StartIpAddress(This,AddressType,bszStartIpAddress);
}
static inline HRESULT IWdsTransportServicePolicy_get_EndIpAddress(IWdsTransportServicePolicy* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,BSTR *pbszEndIpAddress) {
    return This->lpVtbl->get_EndIpAddress(This,AddressType,pbszEndIpAddress);
}
static inline HRESULT IWdsTransportServicePolicy_put_EndIpAddress(IWdsTransportServicePolicy* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,BSTR bszEndIpAddress) {
    return This->lpVtbl->put_EndIpAddress(This,AddressType,bszEndIpAddress);
}
static inline HRESULT IWdsTransportServicePolicy_get_StartPort(IWdsTransportServicePolicy* This,ULONG *pulStartPort) {
    return This->lpVtbl->get_StartPort(This,pulStartPort);
}
static inline HRESULT IWdsTransportServicePolicy_put_StartPort(IWdsTransportServicePolicy* This,ULONG ulStartPort) {
    return This->lpVtbl->put_StartPort(This,ulStartPort);
}
static inline HRESULT IWdsTransportServicePolicy_get_EndPort(IWdsTransportServicePolicy* This,ULONG *pulEndPort) {
    return This->lpVtbl->get_EndPort(This,pulEndPort);
}
static inline HRESULT IWdsTransportServicePolicy_put_EndPort(IWdsTransportServicePolicy* This,ULONG ulEndPort) {
    return This->lpVtbl->put_EndPort(This,ulEndPort);
}
static inline HRESULT IWdsTransportServicePolicy_get_NetworkProfile(IWdsTransportServicePolicy* This,WDSTRANSPORT_NETWORK_PROFILE_TYPE *pProfileType) {
    return This->lpVtbl->get_NetworkProfile(This,pProfileType);
}
static inline HRESULT IWdsTransportServicePolicy_put_NetworkProfile(IWdsTransportServicePolicy* This,WDSTRANSPORT_NETWORK_PROFILE_TYPE ProfileType) {
    return This->lpVtbl->put_NetworkProfile(This,ProfileType);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportServicePolicy_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportServicePolicy2 interface
 */
#ifndef __IWdsTransportServicePolicy2_INTERFACE_DEFINED__
#define __IWdsTransportServicePolicy2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportServicePolicy2, 0x65c19e5c, 0xaa7e, 0x4b91, 0x89,0x44, 0x91,0xe0,0xe5,0x57,0x27,0x97);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("65c19e5c-aa7e-4b91-8944-91e0e5572797")
IWdsTransportServicePolicy2 : public IWdsTransportServicePolicy
{
    virtual HRESULT STDMETHODCALLTYPE get_UdpPortPolicy(
        WDSTRANSPORT_UDP_PORT_POLICY *pUdpPortPolicy) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UdpPortPolicy(
        WDSTRANSPORT_UDP_PORT_POLICY UdpPortPolicy) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TftpMaximumBlockSize(
        ULONG *pulTftpMaximumBlockSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_TftpMaximumBlockSize(
        ULONG ulTftpMaximumBlockSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EnableTftpVariableWindowExtension(
        VARIANT_BOOL *pbEnableTftpVariableWindowExtension) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EnableTftpVariableWindowExtension(
        VARIANT_BOOL bEnableTftpVariableWindowExtension) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportServicePolicy2, 0x65c19e5c, 0xaa7e, 0x4b91, 0x89,0x44, 0x91,0xe0,0xe5,0x57,0x27,0x97)
#endif
#else
typedef struct IWdsTransportServicePolicy2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportServicePolicy2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportServicePolicy2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportServicePolicy2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportServicePolicy2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportServicePolicy2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportServicePolicy2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportServicePolicy2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportCacheable methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Dirty)(
        IWdsTransportServicePolicy2 *This,
        VARIANT_BOOL *pbDirty);

    HRESULT (STDMETHODCALLTYPE *Discard)(
        IWdsTransportServicePolicy2 *This);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportServicePolicy2 *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWdsTransportServicePolicy2 *This);

    /*** IWdsTransportServicePolicy methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IpAddressSource)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE *pSourceType);

    HRESULT (STDMETHODCALLTYPE *put_IpAddressSource)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE SourceType);

    HRESULT (STDMETHODCALLTYPE *get_StartIpAddress)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR *pbszStartIpAddress);

    HRESULT (STDMETHODCALLTYPE *put_StartIpAddress)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR bszStartIpAddress);

    HRESULT (STDMETHODCALLTYPE *get_EndIpAddress)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR *pbszEndIpAddress);

    HRESULT (STDMETHODCALLTYPE *put_EndIpAddress)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,
        BSTR bszEndIpAddress);

    HRESULT (STDMETHODCALLTYPE *get_StartPort)(
        IWdsTransportServicePolicy2 *This,
        ULONG *pulStartPort);

    HRESULT (STDMETHODCALLTYPE *put_StartPort)(
        IWdsTransportServicePolicy2 *This,
        ULONG ulStartPort);

    HRESULT (STDMETHODCALLTYPE *get_EndPort)(
        IWdsTransportServicePolicy2 *This,
        ULONG *pulEndPort);

    HRESULT (STDMETHODCALLTYPE *put_EndPort)(
        IWdsTransportServicePolicy2 *This,
        ULONG ulEndPort);

    HRESULT (STDMETHODCALLTYPE *get_NetworkProfile)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_NETWORK_PROFILE_TYPE *pProfileType);

    HRESULT (STDMETHODCALLTYPE *put_NetworkProfile)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_NETWORK_PROFILE_TYPE ProfileType);

    /*** IWdsTransportServicePolicy2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UdpPortPolicy)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_UDP_PORT_POLICY *pUdpPortPolicy);

    HRESULT (STDMETHODCALLTYPE *put_UdpPortPolicy)(
        IWdsTransportServicePolicy2 *This,
        WDSTRANSPORT_UDP_PORT_POLICY UdpPortPolicy);

    HRESULT (STDMETHODCALLTYPE *get_TftpMaximumBlockSize)(
        IWdsTransportServicePolicy2 *This,
        ULONG *pulTftpMaximumBlockSize);

    HRESULT (STDMETHODCALLTYPE *put_TftpMaximumBlockSize)(
        IWdsTransportServicePolicy2 *This,
        ULONG ulTftpMaximumBlockSize);

    HRESULT (STDMETHODCALLTYPE *get_EnableTftpVariableWindowExtension)(
        IWdsTransportServicePolicy2 *This,
        VARIANT_BOOL *pbEnableTftpVariableWindowExtension);

    HRESULT (STDMETHODCALLTYPE *put_EnableTftpVariableWindowExtension)(
        IWdsTransportServicePolicy2 *This,
        VARIANT_BOOL bEnableTftpVariableWindowExtension);

    END_INTERFACE
} IWdsTransportServicePolicy2Vtbl;

interface IWdsTransportServicePolicy2 {
    CONST_VTBL IWdsTransportServicePolicy2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportServicePolicy2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportServicePolicy2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportServicePolicy2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportServicePolicy2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportServicePolicy2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportServicePolicy2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportServicePolicy2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportCacheable methods ***/
#define IWdsTransportServicePolicy2_get_Dirty(This,pbDirty) (This)->lpVtbl->get_Dirty(This,pbDirty)
#define IWdsTransportServicePolicy2_Discard(This) (This)->lpVtbl->Discard(This)
#define IWdsTransportServicePolicy2_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportServicePolicy2_Commit(This) (This)->lpVtbl->Commit(This)
/*** IWdsTransportServicePolicy methods ***/
#define IWdsTransportServicePolicy2_get_IpAddressSource(This,AddressType,pSourceType) (This)->lpVtbl->get_IpAddressSource(This,AddressType,pSourceType)
#define IWdsTransportServicePolicy2_put_IpAddressSource(This,AddressType,SourceType) (This)->lpVtbl->put_IpAddressSource(This,AddressType,SourceType)
#define IWdsTransportServicePolicy2_get_StartIpAddress(This,AddressType,pbszStartIpAddress) (This)->lpVtbl->get_StartIpAddress(This,AddressType,pbszStartIpAddress)
#define IWdsTransportServicePolicy2_put_StartIpAddress(This,AddressType,bszStartIpAddress) (This)->lpVtbl->put_StartIpAddress(This,AddressType,bszStartIpAddress)
#define IWdsTransportServicePolicy2_get_EndIpAddress(This,AddressType,pbszEndIpAddress) (This)->lpVtbl->get_EndIpAddress(This,AddressType,pbszEndIpAddress)
#define IWdsTransportServicePolicy2_put_EndIpAddress(This,AddressType,bszEndIpAddress) (This)->lpVtbl->put_EndIpAddress(This,AddressType,bszEndIpAddress)
#define IWdsTransportServicePolicy2_get_StartPort(This,pulStartPort) (This)->lpVtbl->get_StartPort(This,pulStartPort)
#define IWdsTransportServicePolicy2_put_StartPort(This,ulStartPort) (This)->lpVtbl->put_StartPort(This,ulStartPort)
#define IWdsTransportServicePolicy2_get_EndPort(This,pulEndPort) (This)->lpVtbl->get_EndPort(This,pulEndPort)
#define IWdsTransportServicePolicy2_put_EndPort(This,ulEndPort) (This)->lpVtbl->put_EndPort(This,ulEndPort)
#define IWdsTransportServicePolicy2_get_NetworkProfile(This,pProfileType) (This)->lpVtbl->get_NetworkProfile(This,pProfileType)
#define IWdsTransportServicePolicy2_put_NetworkProfile(This,ProfileType) (This)->lpVtbl->put_NetworkProfile(This,ProfileType)
/*** IWdsTransportServicePolicy2 methods ***/
#define IWdsTransportServicePolicy2_get_UdpPortPolicy(This,pUdpPortPolicy) (This)->lpVtbl->get_UdpPortPolicy(This,pUdpPortPolicy)
#define IWdsTransportServicePolicy2_put_UdpPortPolicy(This,UdpPortPolicy) (This)->lpVtbl->put_UdpPortPolicy(This,UdpPortPolicy)
#define IWdsTransportServicePolicy2_get_TftpMaximumBlockSize(This,pulTftpMaximumBlockSize) (This)->lpVtbl->get_TftpMaximumBlockSize(This,pulTftpMaximumBlockSize)
#define IWdsTransportServicePolicy2_put_TftpMaximumBlockSize(This,ulTftpMaximumBlockSize) (This)->lpVtbl->put_TftpMaximumBlockSize(This,ulTftpMaximumBlockSize)
#define IWdsTransportServicePolicy2_get_EnableTftpVariableWindowExtension(This,pbEnableTftpVariableWindowExtension) (This)->lpVtbl->get_EnableTftpVariableWindowExtension(This,pbEnableTftpVariableWindowExtension)
#define IWdsTransportServicePolicy2_put_EnableTftpVariableWindowExtension(This,bEnableTftpVariableWindowExtension) (This)->lpVtbl->put_EnableTftpVariableWindowExtension(This,bEnableTftpVariableWindowExtension)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportServicePolicy2_QueryInterface(IWdsTransportServicePolicy2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportServicePolicy2_AddRef(IWdsTransportServicePolicy2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportServicePolicy2_Release(IWdsTransportServicePolicy2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportServicePolicy2_GetTypeInfoCount(IWdsTransportServicePolicy2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportServicePolicy2_GetTypeInfo(IWdsTransportServicePolicy2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportServicePolicy2_GetIDsOfNames(IWdsTransportServicePolicy2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportServicePolicy2_Invoke(IWdsTransportServicePolicy2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportCacheable methods ***/
static inline HRESULT IWdsTransportServicePolicy2_get_Dirty(IWdsTransportServicePolicy2* This,VARIANT_BOOL *pbDirty) {
    return This->lpVtbl->get_Dirty(This,pbDirty);
}
static inline HRESULT IWdsTransportServicePolicy2_Discard(IWdsTransportServicePolicy2* This) {
    return This->lpVtbl->Discard(This);
}
static inline HRESULT IWdsTransportServicePolicy2_Refresh(IWdsTransportServicePolicy2* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportServicePolicy2_Commit(IWdsTransportServicePolicy2* This) {
    return This->lpVtbl->Commit(This);
}
/*** IWdsTransportServicePolicy methods ***/
static inline HRESULT IWdsTransportServicePolicy2_get_IpAddressSource(IWdsTransportServicePolicy2* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE *pSourceType) {
    return This->lpVtbl->get_IpAddressSource(This,AddressType,pSourceType);
}
static inline HRESULT IWdsTransportServicePolicy2_put_IpAddressSource(IWdsTransportServicePolicy2* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,WDSTRANSPORT_IP_ADDRESS_SOURCE_TYPE SourceType) {
    return This->lpVtbl->put_IpAddressSource(This,AddressType,SourceType);
}
static inline HRESULT IWdsTransportServicePolicy2_get_StartIpAddress(IWdsTransportServicePolicy2* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,BSTR *pbszStartIpAddress) {
    return This->lpVtbl->get_StartIpAddress(This,AddressType,pbszStartIpAddress);
}
static inline HRESULT IWdsTransportServicePolicy2_put_StartIpAddress(IWdsTransportServicePolicy2* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,BSTR bszStartIpAddress) {
    return This->lpVtbl->put_StartIpAddress(This,AddressType,bszStartIpAddress);
}
static inline HRESULT IWdsTransportServicePolicy2_get_EndIpAddress(IWdsTransportServicePolicy2* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,BSTR *pbszEndIpAddress) {
    return This->lpVtbl->get_EndIpAddress(This,AddressType,pbszEndIpAddress);
}
static inline HRESULT IWdsTransportServicePolicy2_put_EndIpAddress(IWdsTransportServicePolicy2* This,WDSTRANSPORT_IP_ADDRESS_TYPE AddressType,BSTR bszEndIpAddress) {
    return This->lpVtbl->put_EndIpAddress(This,AddressType,bszEndIpAddress);
}
static inline HRESULT IWdsTransportServicePolicy2_get_StartPort(IWdsTransportServicePolicy2* This,ULONG *pulStartPort) {
    return This->lpVtbl->get_StartPort(This,pulStartPort);
}
static inline HRESULT IWdsTransportServicePolicy2_put_StartPort(IWdsTransportServicePolicy2* This,ULONG ulStartPort) {
    return This->lpVtbl->put_StartPort(This,ulStartPort);
}
static inline HRESULT IWdsTransportServicePolicy2_get_EndPort(IWdsTransportServicePolicy2* This,ULONG *pulEndPort) {
    return This->lpVtbl->get_EndPort(This,pulEndPort);
}
static inline HRESULT IWdsTransportServicePolicy2_put_EndPort(IWdsTransportServicePolicy2* This,ULONG ulEndPort) {
    return This->lpVtbl->put_EndPort(This,ulEndPort);
}
static inline HRESULT IWdsTransportServicePolicy2_get_NetworkProfile(IWdsTransportServicePolicy2* This,WDSTRANSPORT_NETWORK_PROFILE_TYPE *pProfileType) {
    return This->lpVtbl->get_NetworkProfile(This,pProfileType);
}
static inline HRESULT IWdsTransportServicePolicy2_put_NetworkProfile(IWdsTransportServicePolicy2* This,WDSTRANSPORT_NETWORK_PROFILE_TYPE ProfileType) {
    return This->lpVtbl->put_NetworkProfile(This,ProfileType);
}
/*** IWdsTransportServicePolicy2 methods ***/
static inline HRESULT IWdsTransportServicePolicy2_get_UdpPortPolicy(IWdsTransportServicePolicy2* This,WDSTRANSPORT_UDP_PORT_POLICY *pUdpPortPolicy) {
    return This->lpVtbl->get_UdpPortPolicy(This,pUdpPortPolicy);
}
static inline HRESULT IWdsTransportServicePolicy2_put_UdpPortPolicy(IWdsTransportServicePolicy2* This,WDSTRANSPORT_UDP_PORT_POLICY UdpPortPolicy) {
    return This->lpVtbl->put_UdpPortPolicy(This,UdpPortPolicy);
}
static inline HRESULT IWdsTransportServicePolicy2_get_TftpMaximumBlockSize(IWdsTransportServicePolicy2* This,ULONG *pulTftpMaximumBlockSize) {
    return This->lpVtbl->get_TftpMaximumBlockSize(This,pulTftpMaximumBlockSize);
}
static inline HRESULT IWdsTransportServicePolicy2_put_TftpMaximumBlockSize(IWdsTransportServicePolicy2* This,ULONG ulTftpMaximumBlockSize) {
    return This->lpVtbl->put_TftpMaximumBlockSize(This,ulTftpMaximumBlockSize);
}
static inline HRESULT IWdsTransportServicePolicy2_get_EnableTftpVariableWindowExtension(IWdsTransportServicePolicy2* This,VARIANT_BOOL *pbEnableTftpVariableWindowExtension) {
    return This->lpVtbl->get_EnableTftpVariableWindowExtension(This,pbEnableTftpVariableWindowExtension);
}
static inline HRESULT IWdsTransportServicePolicy2_put_EnableTftpVariableWindowExtension(IWdsTransportServicePolicy2* This,VARIANT_BOOL bEnableTftpVariableWindowExtension) {
    return This->lpVtbl->put_EnableTftpVariableWindowExtension(This,bEnableTftpVariableWindowExtension);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportServicePolicy2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportSetupManager interface
 */
#ifndef __IWdsTransportSetupManager_INTERFACE_DEFINED__
#define __IWdsTransportSetupManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportSetupManager, 0xf7238425, 0xefa8, 0x40a4, 0xae,0xf9, 0xc9,0x8d,0x96,0x9c,0x0b,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f7238425-efa8-40a4-aef9-c98d969c0b75")
IWdsTransportSetupManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Version(
        ULONGLONG *pullVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InstalledFeatures(
        ULONG *pulInstalledFeatures) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Protocols(
        ULONG *pulProtocols) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterContentProvider(
        BSTR bszName,
        BSTR bszDescription,
        BSTR bszFilePath,
        BSTR bszInitializationRoutine) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeregisterContentProvider(
        BSTR bszName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportSetupManager, 0xf7238425, 0xefa8, 0x40a4, 0xae,0xf9, 0xc9,0x8d,0x96,0x9c,0x0b,0x75)
#endif
#else
typedef struct IWdsTransportSetupManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportSetupManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportSetupManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportSetupManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportSetupManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportSetupManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportSetupManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportSetupManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportSetupManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Version)(
        IWdsTransportSetupManager *This,
        ULONGLONG *pullVersion);

    HRESULT (STDMETHODCALLTYPE *get_InstalledFeatures)(
        IWdsTransportSetupManager *This,
        ULONG *pulInstalledFeatures);

    HRESULT (STDMETHODCALLTYPE *get_Protocols)(
        IWdsTransportSetupManager *This,
        ULONG *pulProtocols);

    HRESULT (STDMETHODCALLTYPE *RegisterContentProvider)(
        IWdsTransportSetupManager *This,
        BSTR bszName,
        BSTR bszDescription,
        BSTR bszFilePath,
        BSTR bszInitializationRoutine);

    HRESULT (STDMETHODCALLTYPE *DeregisterContentProvider)(
        IWdsTransportSetupManager *This,
        BSTR bszName);

    END_INTERFACE
} IWdsTransportSetupManagerVtbl;

interface IWdsTransportSetupManager {
    CONST_VTBL IWdsTransportSetupManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportSetupManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportSetupManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportSetupManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportSetupManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportSetupManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportSetupManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportSetupManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportSetupManager methods ***/
#define IWdsTransportSetupManager_get_Version(This,pullVersion) (This)->lpVtbl->get_Version(This,pullVersion)
#define IWdsTransportSetupManager_get_InstalledFeatures(This,pulInstalledFeatures) (This)->lpVtbl->get_InstalledFeatures(This,pulInstalledFeatures)
#define IWdsTransportSetupManager_get_Protocols(This,pulProtocols) (This)->lpVtbl->get_Protocols(This,pulProtocols)
#define IWdsTransportSetupManager_RegisterContentProvider(This,bszName,bszDescription,bszFilePath,bszInitializationRoutine) (This)->lpVtbl->RegisterContentProvider(This,bszName,bszDescription,bszFilePath,bszInitializationRoutine)
#define IWdsTransportSetupManager_DeregisterContentProvider(This,bszName) (This)->lpVtbl->DeregisterContentProvider(This,bszName)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportSetupManager_QueryInterface(IWdsTransportSetupManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportSetupManager_AddRef(IWdsTransportSetupManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportSetupManager_Release(IWdsTransportSetupManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportSetupManager_GetTypeInfoCount(IWdsTransportSetupManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportSetupManager_GetTypeInfo(IWdsTransportSetupManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportSetupManager_GetIDsOfNames(IWdsTransportSetupManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportSetupManager_Invoke(IWdsTransportSetupManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportSetupManager methods ***/
static inline HRESULT IWdsTransportSetupManager_get_Version(IWdsTransportSetupManager* This,ULONGLONG *pullVersion) {
    return This->lpVtbl->get_Version(This,pullVersion);
}
static inline HRESULT IWdsTransportSetupManager_get_InstalledFeatures(IWdsTransportSetupManager* This,ULONG *pulInstalledFeatures) {
    return This->lpVtbl->get_InstalledFeatures(This,pulInstalledFeatures);
}
static inline HRESULT IWdsTransportSetupManager_get_Protocols(IWdsTransportSetupManager* This,ULONG *pulProtocols) {
    return This->lpVtbl->get_Protocols(This,pulProtocols);
}
static inline HRESULT IWdsTransportSetupManager_RegisterContentProvider(IWdsTransportSetupManager* This,BSTR bszName,BSTR bszDescription,BSTR bszFilePath,BSTR bszInitializationRoutine) {
    return This->lpVtbl->RegisterContentProvider(This,bszName,bszDescription,bszFilePath,bszInitializationRoutine);
}
static inline HRESULT IWdsTransportSetupManager_DeregisterContentProvider(IWdsTransportSetupManager* This,BSTR bszName) {
    return This->lpVtbl->DeregisterContentProvider(This,bszName);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportSetupManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportSetupManager2 interface
 */
#ifndef __IWdsTransportSetupManager2_INTERFACE_DEFINED__
#define __IWdsTransportSetupManager2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportSetupManager2, 0x02be79da, 0x7e9e, 0x4366, 0x8b,0x6e, 0x2a,0xa9,0xa9,0x1b,0xe4,0x7f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("02be79da-7e9e-4366-8b6e-2aa9a91be47f")
IWdsTransportSetupManager2 : public IWdsTransportSetupManager
{
    virtual HRESULT STDMETHODCALLTYPE get_TftpCapabilities(
        ULONG *pulTftpCapabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ContentProviders(
        IWdsTransportCollection **ppProviderCollection) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportSetupManager2, 0x02be79da, 0x7e9e, 0x4366, 0x8b,0x6e, 0x2a,0xa9,0xa9,0x1b,0xe4,0x7f)
#endif
#else
typedef struct IWdsTransportSetupManager2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportSetupManager2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportSetupManager2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportSetupManager2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportSetupManager2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportSetupManager2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportSetupManager2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportSetupManager2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportSetupManager methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Version)(
        IWdsTransportSetupManager2 *This,
        ULONGLONG *pullVersion);

    HRESULT (STDMETHODCALLTYPE *get_InstalledFeatures)(
        IWdsTransportSetupManager2 *This,
        ULONG *pulInstalledFeatures);

    HRESULT (STDMETHODCALLTYPE *get_Protocols)(
        IWdsTransportSetupManager2 *This,
        ULONG *pulProtocols);

    HRESULT (STDMETHODCALLTYPE *RegisterContentProvider)(
        IWdsTransportSetupManager2 *This,
        BSTR bszName,
        BSTR bszDescription,
        BSTR bszFilePath,
        BSTR bszInitializationRoutine);

    HRESULT (STDMETHODCALLTYPE *DeregisterContentProvider)(
        IWdsTransportSetupManager2 *This,
        BSTR bszName);

    /*** IWdsTransportSetupManager2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TftpCapabilities)(
        IWdsTransportSetupManager2 *This,
        ULONG *pulTftpCapabilities);

    HRESULT (STDMETHODCALLTYPE *get_ContentProviders)(
        IWdsTransportSetupManager2 *This,
        IWdsTransportCollection **ppProviderCollection);

    END_INTERFACE
} IWdsTransportSetupManager2Vtbl;

interface IWdsTransportSetupManager2 {
    CONST_VTBL IWdsTransportSetupManager2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportSetupManager2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportSetupManager2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportSetupManager2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportSetupManager2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportSetupManager2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportSetupManager2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportSetupManager2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportSetupManager methods ***/
#define IWdsTransportSetupManager2_get_Version(This,pullVersion) (This)->lpVtbl->get_Version(This,pullVersion)
#define IWdsTransportSetupManager2_get_InstalledFeatures(This,pulInstalledFeatures) (This)->lpVtbl->get_InstalledFeatures(This,pulInstalledFeatures)
#define IWdsTransportSetupManager2_get_Protocols(This,pulProtocols) (This)->lpVtbl->get_Protocols(This,pulProtocols)
#define IWdsTransportSetupManager2_RegisterContentProvider(This,bszName,bszDescription,bszFilePath,bszInitializationRoutine) (This)->lpVtbl->RegisterContentProvider(This,bszName,bszDescription,bszFilePath,bszInitializationRoutine)
#define IWdsTransportSetupManager2_DeregisterContentProvider(This,bszName) (This)->lpVtbl->DeregisterContentProvider(This,bszName)
/*** IWdsTransportSetupManager2 methods ***/
#define IWdsTransportSetupManager2_get_TftpCapabilities(This,pulTftpCapabilities) (This)->lpVtbl->get_TftpCapabilities(This,pulTftpCapabilities)
#define IWdsTransportSetupManager2_get_ContentProviders(This,ppProviderCollection) (This)->lpVtbl->get_ContentProviders(This,ppProviderCollection)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportSetupManager2_QueryInterface(IWdsTransportSetupManager2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportSetupManager2_AddRef(IWdsTransportSetupManager2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportSetupManager2_Release(IWdsTransportSetupManager2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportSetupManager2_GetTypeInfoCount(IWdsTransportSetupManager2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportSetupManager2_GetTypeInfo(IWdsTransportSetupManager2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportSetupManager2_GetIDsOfNames(IWdsTransportSetupManager2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportSetupManager2_Invoke(IWdsTransportSetupManager2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportSetupManager methods ***/
static inline HRESULT IWdsTransportSetupManager2_get_Version(IWdsTransportSetupManager2* This,ULONGLONG *pullVersion) {
    return This->lpVtbl->get_Version(This,pullVersion);
}
static inline HRESULT IWdsTransportSetupManager2_get_InstalledFeatures(IWdsTransportSetupManager2* This,ULONG *pulInstalledFeatures) {
    return This->lpVtbl->get_InstalledFeatures(This,pulInstalledFeatures);
}
static inline HRESULT IWdsTransportSetupManager2_get_Protocols(IWdsTransportSetupManager2* This,ULONG *pulProtocols) {
    return This->lpVtbl->get_Protocols(This,pulProtocols);
}
static inline HRESULT IWdsTransportSetupManager2_RegisterContentProvider(IWdsTransportSetupManager2* This,BSTR bszName,BSTR bszDescription,BSTR bszFilePath,BSTR bszInitializationRoutine) {
    return This->lpVtbl->RegisterContentProvider(This,bszName,bszDescription,bszFilePath,bszInitializationRoutine);
}
static inline HRESULT IWdsTransportSetupManager2_DeregisterContentProvider(IWdsTransportSetupManager2* This,BSTR bszName) {
    return This->lpVtbl->DeregisterContentProvider(This,bszName);
}
/*** IWdsTransportSetupManager2 methods ***/
static inline HRESULT IWdsTransportSetupManager2_get_TftpCapabilities(IWdsTransportSetupManager2* This,ULONG *pulTftpCapabilities) {
    return This->lpVtbl->get_TftpCapabilities(This,pulTftpCapabilities);
}
static inline HRESULT IWdsTransportSetupManager2_get_ContentProviders(IWdsTransportSetupManager2* This,IWdsTransportCollection **ppProviderCollection) {
    return This->lpVtbl->get_ContentProviders(This,ppProviderCollection);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportSetupManager2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportTftpManager interface
 */
#ifndef __IWdsTransportTftpManager_INTERFACE_DEFINED__
#define __IWdsTransportTftpManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportTftpManager, 0x1327a7c8, 0xae8a, 0x4fb3, 0x81,0x50, 0x13,0x62,0x27,0xc3,0x7e,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1327a7c8-ae8a-4fb3-8150-136227c37e9a")
IWdsTransportTftpManager : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE RetrieveTftpClients(
        IWdsTransportCollection **ppWdsTransportTftpClients) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportTftpManager, 0x1327a7c8, 0xae8a, 0x4fb3, 0x81,0x50, 0x13,0x62,0x27,0xc3,0x7e,0x9a)
#endif
#else
typedef struct IWdsTransportTftpManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportTftpManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportTftpManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportTftpManager *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportTftpManager *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportTftpManager *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportTftpManager *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportTftpManager *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportTftpManager methods ***/
    HRESULT (STDMETHODCALLTYPE *RetrieveTftpClients)(
        IWdsTransportTftpManager *This,
        IWdsTransportCollection **ppWdsTransportTftpClients);

    END_INTERFACE
} IWdsTransportTftpManagerVtbl;

interface IWdsTransportTftpManager {
    CONST_VTBL IWdsTransportTftpManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportTftpManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportTftpManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportTftpManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportTftpManager_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportTftpManager_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportTftpManager_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportTftpManager_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportTftpManager methods ***/
#define IWdsTransportTftpManager_RetrieveTftpClients(This,ppWdsTransportTftpClients) (This)->lpVtbl->RetrieveTftpClients(This,ppWdsTransportTftpClients)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportTftpManager_QueryInterface(IWdsTransportTftpManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportTftpManager_AddRef(IWdsTransportTftpManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportTftpManager_Release(IWdsTransportTftpManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportTftpManager_GetTypeInfoCount(IWdsTransportTftpManager* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportTftpManager_GetTypeInfo(IWdsTransportTftpManager* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportTftpManager_GetIDsOfNames(IWdsTransportTftpManager* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportTftpManager_Invoke(IWdsTransportTftpManager* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportTftpManager methods ***/
static inline HRESULT IWdsTransportTftpManager_RetrieveTftpClients(IWdsTransportTftpManager* This,IWdsTransportCollection **ppWdsTransportTftpClients) {
    return This->lpVtbl->RetrieveTftpClients(This,ppWdsTransportTftpClients);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportTftpManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportMulticastSessionPolicy interface
 */
#ifndef __IWdsTransportMulticastSessionPolicy_INTERFACE_DEFINED__
#define __IWdsTransportMulticastSessionPolicy_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportMulticastSessionPolicy, 0x4e5753cf, 0x68ec, 0x4504, 0xa9,0x51, 0x4a,0x00,0x32,0x66,0x60,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4e5753cf-68ec-4504-a951-4a003266606b")
IWdsTransportMulticastSessionPolicy : public IWdsTransportCacheable
{
    virtual HRESULT STDMETHODCALLTYPE get_SlowClientHandling(
        WDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE *pSlowClientHandling) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SlowClientHandling(
        WDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE SlowClientHandling) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AutoDisconnectThreshold(
        ULONG *pulThreshold) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AutoDisconnectThreshold(
        ULONG ulThreshold) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MultistreamStreamCount(
        ULONG *pulStreamCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MultistreamStreamCount(
        ULONG ulStreamCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SlowClientFallback(
        VARIANT_BOOL *pbClientFallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SlowClientFallback(
        VARIANT_BOOL bClientFallback) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportMulticastSessionPolicy, 0x4e5753cf, 0x68ec, 0x4504, 0xa9,0x51, 0x4a,0x00,0x32,0x66,0x60,0x6b)
#endif
#else
typedef struct IWdsTransportMulticastSessionPolicyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportMulticastSessionPolicy *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportMulticastSessionPolicy *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportMulticastSessionPolicy *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportMulticastSessionPolicy *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportMulticastSessionPolicy *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportMulticastSessionPolicy *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportMulticastSessionPolicy *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportCacheable methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Dirty)(
        IWdsTransportMulticastSessionPolicy *This,
        VARIANT_BOOL *pbDirty);

    HRESULT (STDMETHODCALLTYPE *Discard)(
        IWdsTransportMulticastSessionPolicy *This);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWdsTransportMulticastSessionPolicy *This);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IWdsTransportMulticastSessionPolicy *This);

    /*** IWdsTransportMulticastSessionPolicy methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SlowClientHandling)(
        IWdsTransportMulticastSessionPolicy *This,
        WDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE *pSlowClientHandling);

    HRESULT (STDMETHODCALLTYPE *put_SlowClientHandling)(
        IWdsTransportMulticastSessionPolicy *This,
        WDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE SlowClientHandling);

    HRESULT (STDMETHODCALLTYPE *get_AutoDisconnectThreshold)(
        IWdsTransportMulticastSessionPolicy *This,
        ULONG *pulThreshold);

    HRESULT (STDMETHODCALLTYPE *put_AutoDisconnectThreshold)(
        IWdsTransportMulticastSessionPolicy *This,
        ULONG ulThreshold);

    HRESULT (STDMETHODCALLTYPE *get_MultistreamStreamCount)(
        IWdsTransportMulticastSessionPolicy *This,
        ULONG *pulStreamCount);

    HRESULT (STDMETHODCALLTYPE *put_MultistreamStreamCount)(
        IWdsTransportMulticastSessionPolicy *This,
        ULONG ulStreamCount);

    HRESULT (STDMETHODCALLTYPE *get_SlowClientFallback)(
        IWdsTransportMulticastSessionPolicy *This,
        VARIANT_BOOL *pbClientFallback);

    HRESULT (STDMETHODCALLTYPE *put_SlowClientFallback)(
        IWdsTransportMulticastSessionPolicy *This,
        VARIANT_BOOL bClientFallback);

    END_INTERFACE
} IWdsTransportMulticastSessionPolicyVtbl;

interface IWdsTransportMulticastSessionPolicy {
    CONST_VTBL IWdsTransportMulticastSessionPolicyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportMulticastSessionPolicy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportMulticastSessionPolicy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportMulticastSessionPolicy_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportMulticastSessionPolicy_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportMulticastSessionPolicy_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportMulticastSessionPolicy_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportMulticastSessionPolicy_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportCacheable methods ***/
#define IWdsTransportMulticastSessionPolicy_get_Dirty(This,pbDirty) (This)->lpVtbl->get_Dirty(This,pbDirty)
#define IWdsTransportMulticastSessionPolicy_Discard(This) (This)->lpVtbl->Discard(This)
#define IWdsTransportMulticastSessionPolicy_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IWdsTransportMulticastSessionPolicy_Commit(This) (This)->lpVtbl->Commit(This)
/*** IWdsTransportMulticastSessionPolicy methods ***/
#define IWdsTransportMulticastSessionPolicy_get_SlowClientHandling(This,pSlowClientHandling) (This)->lpVtbl->get_SlowClientHandling(This,pSlowClientHandling)
#define IWdsTransportMulticastSessionPolicy_put_SlowClientHandling(This,SlowClientHandling) (This)->lpVtbl->put_SlowClientHandling(This,SlowClientHandling)
#define IWdsTransportMulticastSessionPolicy_get_AutoDisconnectThreshold(This,pulThreshold) (This)->lpVtbl->get_AutoDisconnectThreshold(This,pulThreshold)
#define IWdsTransportMulticastSessionPolicy_put_AutoDisconnectThreshold(This,ulThreshold) (This)->lpVtbl->put_AutoDisconnectThreshold(This,ulThreshold)
#define IWdsTransportMulticastSessionPolicy_get_MultistreamStreamCount(This,pulStreamCount) (This)->lpVtbl->get_MultistreamStreamCount(This,pulStreamCount)
#define IWdsTransportMulticastSessionPolicy_put_MultistreamStreamCount(This,ulStreamCount) (This)->lpVtbl->put_MultistreamStreamCount(This,ulStreamCount)
#define IWdsTransportMulticastSessionPolicy_get_SlowClientFallback(This,pbClientFallback) (This)->lpVtbl->get_SlowClientFallback(This,pbClientFallback)
#define IWdsTransportMulticastSessionPolicy_put_SlowClientFallback(This,bClientFallback) (This)->lpVtbl->put_SlowClientFallback(This,bClientFallback)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportMulticastSessionPolicy_QueryInterface(IWdsTransportMulticastSessionPolicy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportMulticastSessionPolicy_AddRef(IWdsTransportMulticastSessionPolicy* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportMulticastSessionPolicy_Release(IWdsTransportMulticastSessionPolicy* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportMulticastSessionPolicy_GetTypeInfoCount(IWdsTransportMulticastSessionPolicy* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_GetTypeInfo(IWdsTransportMulticastSessionPolicy* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_GetIDsOfNames(IWdsTransportMulticastSessionPolicy* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_Invoke(IWdsTransportMulticastSessionPolicy* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportCacheable methods ***/
static inline HRESULT IWdsTransportMulticastSessionPolicy_get_Dirty(IWdsTransportMulticastSessionPolicy* This,VARIANT_BOOL *pbDirty) {
    return This->lpVtbl->get_Dirty(This,pbDirty);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_Discard(IWdsTransportMulticastSessionPolicy* This) {
    return This->lpVtbl->Discard(This);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_Refresh(IWdsTransportMulticastSessionPolicy* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_Commit(IWdsTransportMulticastSessionPolicy* This) {
    return This->lpVtbl->Commit(This);
}
/*** IWdsTransportMulticastSessionPolicy methods ***/
static inline HRESULT IWdsTransportMulticastSessionPolicy_get_SlowClientHandling(IWdsTransportMulticastSessionPolicy* This,WDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE *pSlowClientHandling) {
    return This->lpVtbl->get_SlowClientHandling(This,pSlowClientHandling);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_put_SlowClientHandling(IWdsTransportMulticastSessionPolicy* This,WDSTRANSPORT_SLOW_CLIENT_HANDLING_TYPE SlowClientHandling) {
    return This->lpVtbl->put_SlowClientHandling(This,SlowClientHandling);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_get_AutoDisconnectThreshold(IWdsTransportMulticastSessionPolicy* This,ULONG *pulThreshold) {
    return This->lpVtbl->get_AutoDisconnectThreshold(This,pulThreshold);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_put_AutoDisconnectThreshold(IWdsTransportMulticastSessionPolicy* This,ULONG ulThreshold) {
    return This->lpVtbl->put_AutoDisconnectThreshold(This,ulThreshold);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_get_MultistreamStreamCount(IWdsTransportMulticastSessionPolicy* This,ULONG *pulStreamCount) {
    return This->lpVtbl->get_MultistreamStreamCount(This,pulStreamCount);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_put_MultistreamStreamCount(IWdsTransportMulticastSessionPolicy* This,ULONG ulStreamCount) {
    return This->lpVtbl->put_MultistreamStreamCount(This,ulStreamCount);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_get_SlowClientFallback(IWdsTransportMulticastSessionPolicy* This,VARIANT_BOOL *pbClientFallback) {
    return This->lpVtbl->get_SlowClientFallback(This,pbClientFallback);
}
static inline HRESULT IWdsTransportMulticastSessionPolicy_put_SlowClientFallback(IWdsTransportMulticastSessionPolicy* This,VARIANT_BOOL bClientFallback) {
    return This->lpVtbl->put_SlowClientFallback(This,bClientFallback);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportMulticastSessionPolicy_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportContent interface
 */
#ifndef __IWdsTransportContent_INTERFACE_DEFINED__
#define __IWdsTransportContent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportContent, 0xd405d711, 0x0296, 0x4ab4, 0xa8,0x60, 0xac,0x7d,0x32,0xe6,0x57,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d405d711-0296-4ab4-a860-ac7d32e65798")
IWdsTransportContent : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Namespace(
        IWdsTransportNamespace **ppWdsTransportNamespace) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        ULONG *pulId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE RetrieveSessions(
        IWdsTransportCollection **ppWdsTransportSessions) = 0;

    virtual HRESULT STDMETHODCALLTYPE Terminate(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportContent, 0xd405d711, 0x0296, 0x4ab4, 0xa8,0x60, 0xac,0x7d,0x32,0xe6,0x57,0x98)
#endif
#else
typedef struct IWdsTransportContentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportContent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportContent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportContent *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportContent *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportContent *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportContent *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportContent *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportContent methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Namespace)(
        IWdsTransportContent *This,
        IWdsTransportNamespace **ppWdsTransportNamespace);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWdsTransportContent *This,
        ULONG *pulId);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportContent *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *RetrieveSessions)(
        IWdsTransportContent *This,
        IWdsTransportCollection **ppWdsTransportSessions);

    HRESULT (STDMETHODCALLTYPE *Terminate)(
        IWdsTransportContent *This);

    END_INTERFACE
} IWdsTransportContentVtbl;

interface IWdsTransportContent {
    CONST_VTBL IWdsTransportContentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportContent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportContent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportContent_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportContent_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportContent_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportContent_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportContent_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportContent methods ***/
#define IWdsTransportContent_get_Namespace(This,ppWdsTransportNamespace) (This)->lpVtbl->get_Namespace(This,ppWdsTransportNamespace)
#define IWdsTransportContent_get_Id(This,pulId) (This)->lpVtbl->get_Id(This,pulId)
#define IWdsTransportContent_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportContent_RetrieveSessions(This,ppWdsTransportSessions) (This)->lpVtbl->RetrieveSessions(This,ppWdsTransportSessions)
#define IWdsTransportContent_Terminate(This) (This)->lpVtbl->Terminate(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportContent_QueryInterface(IWdsTransportContent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportContent_AddRef(IWdsTransportContent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportContent_Release(IWdsTransportContent* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportContent_GetTypeInfoCount(IWdsTransportContent* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportContent_GetTypeInfo(IWdsTransportContent* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportContent_GetIDsOfNames(IWdsTransportContent* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportContent_Invoke(IWdsTransportContent* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportContent methods ***/
static inline HRESULT IWdsTransportContent_get_Namespace(IWdsTransportContent* This,IWdsTransportNamespace **ppWdsTransportNamespace) {
    return This->lpVtbl->get_Namespace(This,ppWdsTransportNamespace);
}
static inline HRESULT IWdsTransportContent_get_Id(IWdsTransportContent* This,ULONG *pulId) {
    return This->lpVtbl->get_Id(This,pulId);
}
static inline HRESULT IWdsTransportContent_get_Name(IWdsTransportContent* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportContent_RetrieveSessions(IWdsTransportContent* This,IWdsTransportCollection **ppWdsTransportSessions) {
    return This->lpVtbl->RetrieveSessions(This,ppWdsTransportSessions);
}
static inline HRESULT IWdsTransportContent_Terminate(IWdsTransportContent* This) {
    return This->lpVtbl->Terminate(This);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportContent_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportSession interface
 */
#ifndef __IWdsTransportSession_INTERFACE_DEFINED__
#define __IWdsTransportSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportSession, 0xf4efea88, 0x65b1, 0x4f30, 0xa4,0xb9, 0x27,0x93,0x98,0x77,0x96,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f4efea88-65b1-4f30-a4b9-2793987796fb")
IWdsTransportSession : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Content(
        IWdsTransportContent **ppWdsTransportContent) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        ULONG *pulId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NetworkInterfaceName(
        BSTR *pbszNetworkInterfaceName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NetworkInterfaceAddress(
        BSTR *pbszNetworkInterfaceAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TransferRate(
        ULONG *pulTransferRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MasterClientId(
        ULONG *pulMasterClientId) = 0;

    virtual HRESULT STDMETHODCALLTYPE RetrieveClients(
        IWdsTransportCollection **ppWdsTransportClients) = 0;

    virtual HRESULT STDMETHODCALLTYPE Terminate(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportSession, 0xf4efea88, 0x65b1, 0x4f30, 0xa4,0xb9, 0x27,0x93,0x98,0x77,0x96,0xfb)
#endif
#else
typedef struct IWdsTransportSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportSession *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportSession *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportSession *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportSession *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportSession *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportSession methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Content)(
        IWdsTransportSession *This,
        IWdsTransportContent **ppWdsTransportContent);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWdsTransportSession *This,
        ULONG *pulId);

    HRESULT (STDMETHODCALLTYPE *get_NetworkInterfaceName)(
        IWdsTransportSession *This,
        BSTR *pbszNetworkInterfaceName);

    HRESULT (STDMETHODCALLTYPE *get_NetworkInterfaceAddress)(
        IWdsTransportSession *This,
        BSTR *pbszNetworkInterfaceAddress);

    HRESULT (STDMETHODCALLTYPE *get_TransferRate)(
        IWdsTransportSession *This,
        ULONG *pulTransferRate);

    HRESULT (STDMETHODCALLTYPE *get_MasterClientId)(
        IWdsTransportSession *This,
        ULONG *pulMasterClientId);

    HRESULT (STDMETHODCALLTYPE *RetrieveClients)(
        IWdsTransportSession *This,
        IWdsTransportCollection **ppWdsTransportClients);

    HRESULT (STDMETHODCALLTYPE *Terminate)(
        IWdsTransportSession *This);

    END_INTERFACE
} IWdsTransportSessionVtbl;

interface IWdsTransportSession {
    CONST_VTBL IWdsTransportSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportSession_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportSession_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportSession_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportSession_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportSession_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportSession methods ***/
#define IWdsTransportSession_get_Content(This,ppWdsTransportContent) (This)->lpVtbl->get_Content(This,ppWdsTransportContent)
#define IWdsTransportSession_get_Id(This,pulId) (This)->lpVtbl->get_Id(This,pulId)
#define IWdsTransportSession_get_NetworkInterfaceName(This,pbszNetworkInterfaceName) (This)->lpVtbl->get_NetworkInterfaceName(This,pbszNetworkInterfaceName)
#define IWdsTransportSession_get_NetworkInterfaceAddress(This,pbszNetworkInterfaceAddress) (This)->lpVtbl->get_NetworkInterfaceAddress(This,pbszNetworkInterfaceAddress)
#define IWdsTransportSession_get_TransferRate(This,pulTransferRate) (This)->lpVtbl->get_TransferRate(This,pulTransferRate)
#define IWdsTransportSession_get_MasterClientId(This,pulMasterClientId) (This)->lpVtbl->get_MasterClientId(This,pulMasterClientId)
#define IWdsTransportSession_RetrieveClients(This,ppWdsTransportClients) (This)->lpVtbl->RetrieveClients(This,ppWdsTransportClients)
#define IWdsTransportSession_Terminate(This) (This)->lpVtbl->Terminate(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportSession_QueryInterface(IWdsTransportSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportSession_AddRef(IWdsTransportSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportSession_Release(IWdsTransportSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportSession_GetTypeInfoCount(IWdsTransportSession* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportSession_GetTypeInfo(IWdsTransportSession* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportSession_GetIDsOfNames(IWdsTransportSession* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportSession_Invoke(IWdsTransportSession* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportSession methods ***/
static inline HRESULT IWdsTransportSession_get_Content(IWdsTransportSession* This,IWdsTransportContent **ppWdsTransportContent) {
    return This->lpVtbl->get_Content(This,ppWdsTransportContent);
}
static inline HRESULT IWdsTransportSession_get_Id(IWdsTransportSession* This,ULONG *pulId) {
    return This->lpVtbl->get_Id(This,pulId);
}
static inline HRESULT IWdsTransportSession_get_NetworkInterfaceName(IWdsTransportSession* This,BSTR *pbszNetworkInterfaceName) {
    return This->lpVtbl->get_NetworkInterfaceName(This,pbszNetworkInterfaceName);
}
static inline HRESULT IWdsTransportSession_get_NetworkInterfaceAddress(IWdsTransportSession* This,BSTR *pbszNetworkInterfaceAddress) {
    return This->lpVtbl->get_NetworkInterfaceAddress(This,pbszNetworkInterfaceAddress);
}
static inline HRESULT IWdsTransportSession_get_TransferRate(IWdsTransportSession* This,ULONG *pulTransferRate) {
    return This->lpVtbl->get_TransferRate(This,pulTransferRate);
}
static inline HRESULT IWdsTransportSession_get_MasterClientId(IWdsTransportSession* This,ULONG *pulMasterClientId) {
    return This->lpVtbl->get_MasterClientId(This,pulMasterClientId);
}
static inline HRESULT IWdsTransportSession_RetrieveClients(IWdsTransportSession* This,IWdsTransportCollection **ppWdsTransportClients) {
    return This->lpVtbl->RetrieveClients(This,ppWdsTransportClients);
}
static inline HRESULT IWdsTransportSession_Terminate(IWdsTransportSession* This) {
    return This->lpVtbl->Terminate(This);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportSession_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportClient interface
 */
#ifndef __IWdsTransportClient_INTERFACE_DEFINED__
#define __IWdsTransportClient_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportClient, 0xb5dbc93a, 0xcabe, 0x46ca, 0x83,0x7f, 0x3e,0x44,0xe9,0x3c,0x65,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b5dbc93a-cabe-46ca-837f-3e44e93c6545")
IWdsTransportClient : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Session(
        IWdsTransportSession **ppWdsTransportSession) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        ULONG *pulId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MacAddress(
        BSTR *pbszMacAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IpAddress(
        BSTR *pbszIpAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PercentCompletion(
        ULONG *pulPercentCompletion) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_JoinDuration(
        ULONG *pulJoinDuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CpuUtilization(
        ULONG *pulCpuUtilization) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MemoryUtilization(
        ULONG *pulMemoryUtilization) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NetworkUtilization(
        ULONG *pulNetworkUtilization) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserIdentity(
        BSTR *pbszUserIdentity) = 0;

    virtual HRESULT STDMETHODCALLTYPE Disconnect(
        WDSTRANSPORT_DISCONNECT_TYPE DisconnectionType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportClient, 0xb5dbc93a, 0xcabe, 0x46ca, 0x83,0x7f, 0x3e,0x44,0xe9,0x3c,0x65,0x45)
#endif
#else
typedef struct IWdsTransportClientVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportClient *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportClient *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportClient *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportClient *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportClient *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportClient *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportClient *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportClient methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Session)(
        IWdsTransportClient *This,
        IWdsTransportSession **ppWdsTransportSession);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWdsTransportClient *This,
        ULONG *pulId);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportClient *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *get_MacAddress)(
        IWdsTransportClient *This,
        BSTR *pbszMacAddress);

    HRESULT (STDMETHODCALLTYPE *get_IpAddress)(
        IWdsTransportClient *This,
        BSTR *pbszIpAddress);

    HRESULT (STDMETHODCALLTYPE *get_PercentCompletion)(
        IWdsTransportClient *This,
        ULONG *pulPercentCompletion);

    HRESULT (STDMETHODCALLTYPE *get_JoinDuration)(
        IWdsTransportClient *This,
        ULONG *pulJoinDuration);

    HRESULT (STDMETHODCALLTYPE *get_CpuUtilization)(
        IWdsTransportClient *This,
        ULONG *pulCpuUtilization);

    HRESULT (STDMETHODCALLTYPE *get_MemoryUtilization)(
        IWdsTransportClient *This,
        ULONG *pulMemoryUtilization);

    HRESULT (STDMETHODCALLTYPE *get_NetworkUtilization)(
        IWdsTransportClient *This,
        ULONG *pulNetworkUtilization);

    HRESULT (STDMETHODCALLTYPE *get_UserIdentity)(
        IWdsTransportClient *This,
        BSTR *pbszUserIdentity);

    HRESULT (STDMETHODCALLTYPE *Disconnect)(
        IWdsTransportClient *This,
        WDSTRANSPORT_DISCONNECT_TYPE DisconnectionType);

    END_INTERFACE
} IWdsTransportClientVtbl;

interface IWdsTransportClient {
    CONST_VTBL IWdsTransportClientVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportClient_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportClient_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportClient_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportClient_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportClient_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportClient_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportClient_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportClient methods ***/
#define IWdsTransportClient_get_Session(This,ppWdsTransportSession) (This)->lpVtbl->get_Session(This,ppWdsTransportSession)
#define IWdsTransportClient_get_Id(This,pulId) (This)->lpVtbl->get_Id(This,pulId)
#define IWdsTransportClient_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportClient_get_MacAddress(This,pbszMacAddress) (This)->lpVtbl->get_MacAddress(This,pbszMacAddress)
#define IWdsTransportClient_get_IpAddress(This,pbszIpAddress) (This)->lpVtbl->get_IpAddress(This,pbszIpAddress)
#define IWdsTransportClient_get_PercentCompletion(This,pulPercentCompletion) (This)->lpVtbl->get_PercentCompletion(This,pulPercentCompletion)
#define IWdsTransportClient_get_JoinDuration(This,pulJoinDuration) (This)->lpVtbl->get_JoinDuration(This,pulJoinDuration)
#define IWdsTransportClient_get_CpuUtilization(This,pulCpuUtilization) (This)->lpVtbl->get_CpuUtilization(This,pulCpuUtilization)
#define IWdsTransportClient_get_MemoryUtilization(This,pulMemoryUtilization) (This)->lpVtbl->get_MemoryUtilization(This,pulMemoryUtilization)
#define IWdsTransportClient_get_NetworkUtilization(This,pulNetworkUtilization) (This)->lpVtbl->get_NetworkUtilization(This,pulNetworkUtilization)
#define IWdsTransportClient_get_UserIdentity(This,pbszUserIdentity) (This)->lpVtbl->get_UserIdentity(This,pbszUserIdentity)
#define IWdsTransportClient_Disconnect(This,DisconnectionType) (This)->lpVtbl->Disconnect(This,DisconnectionType)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportClient_QueryInterface(IWdsTransportClient* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportClient_AddRef(IWdsTransportClient* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportClient_Release(IWdsTransportClient* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportClient_GetTypeInfoCount(IWdsTransportClient* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportClient_GetTypeInfo(IWdsTransportClient* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportClient_GetIDsOfNames(IWdsTransportClient* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportClient_Invoke(IWdsTransportClient* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportClient methods ***/
static inline HRESULT IWdsTransportClient_get_Session(IWdsTransportClient* This,IWdsTransportSession **ppWdsTransportSession) {
    return This->lpVtbl->get_Session(This,ppWdsTransportSession);
}
static inline HRESULT IWdsTransportClient_get_Id(IWdsTransportClient* This,ULONG *pulId) {
    return This->lpVtbl->get_Id(This,pulId);
}
static inline HRESULT IWdsTransportClient_get_Name(IWdsTransportClient* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportClient_get_MacAddress(IWdsTransportClient* This,BSTR *pbszMacAddress) {
    return This->lpVtbl->get_MacAddress(This,pbszMacAddress);
}
static inline HRESULT IWdsTransportClient_get_IpAddress(IWdsTransportClient* This,BSTR *pbszIpAddress) {
    return This->lpVtbl->get_IpAddress(This,pbszIpAddress);
}
static inline HRESULT IWdsTransportClient_get_PercentCompletion(IWdsTransportClient* This,ULONG *pulPercentCompletion) {
    return This->lpVtbl->get_PercentCompletion(This,pulPercentCompletion);
}
static inline HRESULT IWdsTransportClient_get_JoinDuration(IWdsTransportClient* This,ULONG *pulJoinDuration) {
    return This->lpVtbl->get_JoinDuration(This,pulJoinDuration);
}
static inline HRESULT IWdsTransportClient_get_CpuUtilization(IWdsTransportClient* This,ULONG *pulCpuUtilization) {
    return This->lpVtbl->get_CpuUtilization(This,pulCpuUtilization);
}
static inline HRESULT IWdsTransportClient_get_MemoryUtilization(IWdsTransportClient* This,ULONG *pulMemoryUtilization) {
    return This->lpVtbl->get_MemoryUtilization(This,pulMemoryUtilization);
}
static inline HRESULT IWdsTransportClient_get_NetworkUtilization(IWdsTransportClient* This,ULONG *pulNetworkUtilization) {
    return This->lpVtbl->get_NetworkUtilization(This,pulNetworkUtilization);
}
static inline HRESULT IWdsTransportClient_get_UserIdentity(IWdsTransportClient* This,BSTR *pbszUserIdentity) {
    return This->lpVtbl->get_UserIdentity(This,pbszUserIdentity);
}
static inline HRESULT IWdsTransportClient_Disconnect(IWdsTransportClient* This,WDSTRANSPORT_DISCONNECT_TYPE DisconnectionType) {
    return This->lpVtbl->Disconnect(This,DisconnectionType);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportClient_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportTftpClient interface
 */
#ifndef __IWdsTransportTftpClient_INTERFACE_DEFINED__
#define __IWdsTransportTftpClient_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportTftpClient, 0xb022d3ae, 0x884d, 0x4d85, 0xb1,0x46, 0x53,0x32,0x0e,0x76,0xef,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b022d3ae-884d-4d85-b146-53320e76ef62")
IWdsTransportTftpClient : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_FileName(
        BSTR *pbszFileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IpAddress(
        BSTR *pbszIpAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Timeout(
        ULONG *pulTimeout) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentFileOffset(
        ULONG64 *pul64CurrentOffset) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FileSize(
        ULONG64 *pul64FileSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BlockSize(
        ULONG *pulBlockSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WindowSize(
        ULONG *pulWindowSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportTftpClient, 0xb022d3ae, 0x884d, 0x4d85, 0xb1,0x46, 0x53,0x32,0x0e,0x76,0xef,0x62)
#endif
#else
typedef struct IWdsTransportTftpClientVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportTftpClient *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportTftpClient *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportTftpClient *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportTftpClient *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportTftpClient *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportTftpClient *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportTftpClient *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportTftpClient methods ***/
    HRESULT (STDMETHODCALLTYPE *get_FileName)(
        IWdsTransportTftpClient *This,
        BSTR *pbszFileName);

    HRESULT (STDMETHODCALLTYPE *get_IpAddress)(
        IWdsTransportTftpClient *This,
        BSTR *pbszIpAddress);

    HRESULT (STDMETHODCALLTYPE *get_Timeout)(
        IWdsTransportTftpClient *This,
        ULONG *pulTimeout);

    HRESULT (STDMETHODCALLTYPE *get_CurrentFileOffset)(
        IWdsTransportTftpClient *This,
        ULONG64 *pul64CurrentOffset);

    HRESULT (STDMETHODCALLTYPE *get_FileSize)(
        IWdsTransportTftpClient *This,
        ULONG64 *pul64FileSize);

    HRESULT (STDMETHODCALLTYPE *get_BlockSize)(
        IWdsTransportTftpClient *This,
        ULONG *pulBlockSize);

    HRESULT (STDMETHODCALLTYPE *get_WindowSize)(
        IWdsTransportTftpClient *This,
        ULONG *pulWindowSize);

    END_INTERFACE
} IWdsTransportTftpClientVtbl;

interface IWdsTransportTftpClient {
    CONST_VTBL IWdsTransportTftpClientVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportTftpClient_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportTftpClient_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportTftpClient_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportTftpClient_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportTftpClient_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportTftpClient_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportTftpClient_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportTftpClient methods ***/
#define IWdsTransportTftpClient_get_FileName(This,pbszFileName) (This)->lpVtbl->get_FileName(This,pbszFileName)
#define IWdsTransportTftpClient_get_IpAddress(This,pbszIpAddress) (This)->lpVtbl->get_IpAddress(This,pbszIpAddress)
#define IWdsTransportTftpClient_get_Timeout(This,pulTimeout) (This)->lpVtbl->get_Timeout(This,pulTimeout)
#define IWdsTransportTftpClient_get_CurrentFileOffset(This,pul64CurrentOffset) (This)->lpVtbl->get_CurrentFileOffset(This,pul64CurrentOffset)
#define IWdsTransportTftpClient_get_FileSize(This,pul64FileSize) (This)->lpVtbl->get_FileSize(This,pul64FileSize)
#define IWdsTransportTftpClient_get_BlockSize(This,pulBlockSize) (This)->lpVtbl->get_BlockSize(This,pulBlockSize)
#define IWdsTransportTftpClient_get_WindowSize(This,pulWindowSize) (This)->lpVtbl->get_WindowSize(This,pulWindowSize)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportTftpClient_QueryInterface(IWdsTransportTftpClient* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportTftpClient_AddRef(IWdsTransportTftpClient* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportTftpClient_Release(IWdsTransportTftpClient* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportTftpClient_GetTypeInfoCount(IWdsTransportTftpClient* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportTftpClient_GetTypeInfo(IWdsTransportTftpClient* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportTftpClient_GetIDsOfNames(IWdsTransportTftpClient* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportTftpClient_Invoke(IWdsTransportTftpClient* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportTftpClient methods ***/
static inline HRESULT IWdsTransportTftpClient_get_FileName(IWdsTransportTftpClient* This,BSTR *pbszFileName) {
    return This->lpVtbl->get_FileName(This,pbszFileName);
}
static inline HRESULT IWdsTransportTftpClient_get_IpAddress(IWdsTransportTftpClient* This,BSTR *pbszIpAddress) {
    return This->lpVtbl->get_IpAddress(This,pbszIpAddress);
}
static inline HRESULT IWdsTransportTftpClient_get_Timeout(IWdsTransportTftpClient* This,ULONG *pulTimeout) {
    return This->lpVtbl->get_Timeout(This,pulTimeout);
}
static inline HRESULT IWdsTransportTftpClient_get_CurrentFileOffset(IWdsTransportTftpClient* This,ULONG64 *pul64CurrentOffset) {
    return This->lpVtbl->get_CurrentFileOffset(This,pul64CurrentOffset);
}
static inline HRESULT IWdsTransportTftpClient_get_FileSize(IWdsTransportTftpClient* This,ULONG64 *pul64FileSize) {
    return This->lpVtbl->get_FileSize(This,pul64FileSize);
}
static inline HRESULT IWdsTransportTftpClient_get_BlockSize(IWdsTransportTftpClient* This,ULONG *pulBlockSize) {
    return This->lpVtbl->get_BlockSize(This,pulBlockSize);
}
static inline HRESULT IWdsTransportTftpClient_get_WindowSize(IWdsTransportTftpClient* This,ULONG *pulWindowSize) {
    return This->lpVtbl->get_WindowSize(This,pulWindowSize);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportTftpClient_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWdsTransportContentProvider interface
 */
#ifndef __IWdsTransportContentProvider_INTERFACE_DEFINED__
#define __IWdsTransportContentProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWdsTransportContentProvider, 0xb9489f24, 0xf219, 0x4acf, 0xaa,0xd7, 0x26,0x5c,0x7c,0x08,0xa6,0xae);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b9489f24-f219-4acf-aad7-265c7c08a6ae")
IWdsTransportContentProvider : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pbszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *pbszDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FilePath(
        BSTR *pbszFilePath) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InitializationRoutine(
        BSTR *pbszInitializationRoutine) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWdsTransportContentProvider, 0xb9489f24, 0xf219, 0x4acf, 0xaa,0xd7, 0x26,0x5c,0x7c,0x08,0xa6,0xae)
#endif
#else
typedef struct IWdsTransportContentProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWdsTransportContentProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWdsTransportContentProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWdsTransportContentProvider *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWdsTransportContentProvider *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWdsTransportContentProvider *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWdsTransportContentProvider *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWdsTransportContentProvider *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWdsTransportContentProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IWdsTransportContentProvider *This,
        BSTR *pbszName);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IWdsTransportContentProvider *This,
        BSTR *pbszDescription);

    HRESULT (STDMETHODCALLTYPE *get_FilePath)(
        IWdsTransportContentProvider *This,
        BSTR *pbszFilePath);

    HRESULT (STDMETHODCALLTYPE *get_InitializationRoutine)(
        IWdsTransportContentProvider *This,
        BSTR *pbszInitializationRoutine);

    END_INTERFACE
} IWdsTransportContentProviderVtbl;

interface IWdsTransportContentProvider {
    CONST_VTBL IWdsTransportContentProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWdsTransportContentProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWdsTransportContentProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWdsTransportContentProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWdsTransportContentProvider_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWdsTransportContentProvider_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWdsTransportContentProvider_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWdsTransportContentProvider_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWdsTransportContentProvider methods ***/
#define IWdsTransportContentProvider_get_Name(This,pbszName) (This)->lpVtbl->get_Name(This,pbszName)
#define IWdsTransportContentProvider_get_Description(This,pbszDescription) (This)->lpVtbl->get_Description(This,pbszDescription)
#define IWdsTransportContentProvider_get_FilePath(This,pbszFilePath) (This)->lpVtbl->get_FilePath(This,pbszFilePath)
#define IWdsTransportContentProvider_get_InitializationRoutine(This,pbszInitializationRoutine) (This)->lpVtbl->get_InitializationRoutine(This,pbszInitializationRoutine)
#else
/*** IUnknown methods ***/
static inline HRESULT IWdsTransportContentProvider_QueryInterface(IWdsTransportContentProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWdsTransportContentProvider_AddRef(IWdsTransportContentProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWdsTransportContentProvider_Release(IWdsTransportContentProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWdsTransportContentProvider_GetTypeInfoCount(IWdsTransportContentProvider* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWdsTransportContentProvider_GetTypeInfo(IWdsTransportContentProvider* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWdsTransportContentProvider_GetIDsOfNames(IWdsTransportContentProvider* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWdsTransportContentProvider_Invoke(IWdsTransportContentProvider* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWdsTransportContentProvider methods ***/
static inline HRESULT IWdsTransportContentProvider_get_Name(IWdsTransportContentProvider* This,BSTR *pbszName) {
    return This->lpVtbl->get_Name(This,pbszName);
}
static inline HRESULT IWdsTransportContentProvider_get_Description(IWdsTransportContentProvider* This,BSTR *pbszDescription) {
    return This->lpVtbl->get_Description(This,pbszDescription);
}
static inline HRESULT IWdsTransportContentProvider_get_FilePath(IWdsTransportContentProvider* This,BSTR *pbszFilePath) {
    return This->lpVtbl->get_FilePath(This,pbszFilePath);
}
static inline HRESULT IWdsTransportContentProvider_get_InitializationRoutine(IWdsTransportContentProvider* This,BSTR *pbszInitializationRoutine) {
    return This->lpVtbl->get_InitializationRoutine(This,pbszInitializationRoutine);
}
#endif
#endif

#endif


#endif  /* __IWdsTransportContentProvider_INTERFACE_DEFINED__ */


#ifndef __WdsTptMgmtLib_LIBRARY_DEFINED__
#define __WdsTptMgmtLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_WdsTptMgmtLib, 0x9212887f, 0xf5bc, 0x45dd, 0xa5,0x10, 0x26,0x54,0x13,0xa1,0x8e,0xd7);

/*****************************************************************************
 * WdsTransportCacheable coclass
 */

DEFINE_GUID(CLSID_WdsTransportCacheable, 0x70590b16, 0xf146, 0x46bd, 0xbd,0x9d, 0x4a,0xaa,0x90,0x08,0x4b,0xf5);

#ifdef __cplusplus
class DECLSPEC_UUID("70590b16-f146-46bd-bd9d-4aaa90084bf5") WdsTransportCacheable;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportCacheable, 0x70590b16, 0xf146, 0x46bd, 0xbd,0x9d, 0x4a,0xaa,0x90,0x08,0x4b,0xf5)
#endif
#endif

/*****************************************************************************
 * WdsTransportCollection coclass
 */

DEFINE_GUID(CLSID_WdsTransportCollection, 0xc7f18b09, 0x391e, 0x436e, 0xb1,0x0b, 0xc3,0xef,0x46,0xf2,0xc3,0x4f);

#ifdef __cplusplus
class DECLSPEC_UUID("c7f18b09-391e-436e-b10b-c3ef46f2c34f") WdsTransportCollection;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportCollection, 0xc7f18b09, 0x391e, 0x436e, 0xb1,0x0b, 0xc3,0xef,0x46,0xf2,0xc3,0x4f)
#endif
#endif

/*****************************************************************************
 * WdsTransportManager coclass
 */

DEFINE_GUID(CLSID_WdsTransportManager, 0xf21523f6, 0x837c, 0x4a58, 0xaf,0x99, 0x8a,0x7e,0x27,0xf8,0xff,0x59);

#ifdef __cplusplus
class DECLSPEC_UUID("f21523f6-837c-4a58-af99-8a7e27f8ff59") WdsTransportManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportManager, 0xf21523f6, 0x837c, 0x4a58, 0xaf,0x99, 0x8a,0x7e,0x27,0xf8,0xff,0x59)
#endif
#endif

/*****************************************************************************
 * WdsTransportServer coclass
 */

DEFINE_GUID(CLSID_WdsTransportServer, 0xea19b643, 0x4adf, 0x4413, 0x94,0x2c, 0x14,0xf3,0x79,0x11,0x87,0x60);

#ifdef __cplusplus
class DECLSPEC_UUID("ea19b643-4adf-4413-942c-14f379118760") WdsTransportServer;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportServer, 0xea19b643, 0x4adf, 0x4413, 0x94,0x2c, 0x14,0xf3,0x79,0x11,0x87,0x60)
#endif
#endif

/*****************************************************************************
 * WdsTransportSetupManager coclass
 */

DEFINE_GUID(CLSID_WdsTransportSetupManager, 0xc7beeaad, 0x9f04, 0x4923, 0x9f,0x0c, 0xfb,0xf5,0x2b,0xc7,0x59,0x0f);

#ifdef __cplusplus
class DECLSPEC_UUID("c7beeaad-9f04-4923-9f0c-fbf52bc7590f") WdsTransportSetupManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportSetupManager, 0xc7beeaad, 0x9f04, 0x4923, 0x9f,0x0c, 0xfb,0xf5,0x2b,0xc7,0x59,0x0f)
#endif
#endif

/*****************************************************************************
 * WdsTransportConfigurationManager coclass
 */

DEFINE_GUID(CLSID_WdsTransportConfigurationManager, 0x8743f674, 0x904c, 0x47ca, 0x85,0x12, 0x35,0xfe,0x98,0xf6,0xb0,0xac);

#ifdef __cplusplus
class DECLSPEC_UUID("8743f674-904c-47ca-8512-35fe98f6b0ac") WdsTransportConfigurationManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportConfigurationManager, 0x8743f674, 0x904c, 0x47ca, 0x85,0x12, 0x35,0xfe,0x98,0xf6,0xb0,0xac)
#endif
#endif

/*****************************************************************************
 * WdsTransportNamespaceManager coclass
 */

DEFINE_GUID(CLSID_WdsTransportNamespaceManager, 0xf08cdb63, 0x85de, 0x4a28, 0xa1,0xa9, 0x5c,0xa3,0xe7,0xef,0xda,0x73);

#ifdef __cplusplus
class DECLSPEC_UUID("f08cdb63-85de-4a28-a1a9-5ca3e7efda73") WdsTransportNamespaceManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportNamespaceManager, 0xf08cdb63, 0x85de, 0x4a28, 0xa1,0xa9, 0x5c,0xa3,0xe7,0xef,0xda,0x73)
#endif
#endif

/*****************************************************************************
 * WdsTransportServicePolicy coclass
 */

DEFINE_GUID(CLSID_WdsTransportServicePolicy, 0x65aceadc, 0x2f0b, 0x4f43, 0x9f,0x4d, 0x81,0x18,0x65,0xd8,0xce,0xad);

#ifdef __cplusplus
class DECLSPEC_UUID("65aceadc-2f0b-4f43-9f4d-811865d8cead") WdsTransportServicePolicy;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportServicePolicy, 0x65aceadc, 0x2f0b, 0x4f43, 0x9f,0x4d, 0x81,0x18,0x65,0xd8,0xce,0xad)
#endif
#endif

/*****************************************************************************
 * WdsTransportDiagnosticsPolicy coclass
 */

DEFINE_GUID(CLSID_WdsTransportDiagnosticsPolicy, 0xeb3333e1, 0xa7ad, 0x46f5, 0x80,0xd6, 0x6b,0x74,0x02,0x04,0xe5,0x09);

#ifdef __cplusplus
class DECLSPEC_UUID("eb3333e1-a7ad-46f5-80d6-6b740204e509") WdsTransportDiagnosticsPolicy;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportDiagnosticsPolicy, 0xeb3333e1, 0xa7ad, 0x46f5, 0x80,0xd6, 0x6b,0x74,0x02,0x04,0xe5,0x09)
#endif
#endif

/*****************************************************************************
 * WdsTransportMulticastSessionPolicy coclass
 */

DEFINE_GUID(CLSID_WdsTransportMulticastSessionPolicy, 0x3c6bc3f4, 0x6418, 0x472a, 0xb6,0xf1, 0x52,0xd4,0x57,0x19,0x54,0x37);

#ifdef __cplusplus
class DECLSPEC_UUID("3c6bc3f4-6418-472a-b6f1-52d457195437") WdsTransportMulticastSessionPolicy;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportMulticastSessionPolicy, 0x3c6bc3f4, 0x6418, 0x472a, 0xb6,0xf1, 0x52,0xd4,0x57,0x19,0x54,0x37)
#endif
#endif

/*****************************************************************************
 * WdsTransportNamespace coclass
 */

DEFINE_GUID(CLSID_WdsTransportNamespace, 0xd8385768, 0x0732, 0x4ec1, 0x95,0xea, 0x16,0xda,0x58,0x19,0x08,0xa1);

#ifdef __cplusplus
class DECLSPEC_UUID("d8385768-0732-4ec1-95ea-16da581908a1") WdsTransportNamespace;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportNamespace, 0xd8385768, 0x0732, 0x4ec1, 0x95,0xea, 0x16,0xda,0x58,0x19,0x08,0xa1)
#endif
#endif

/*****************************************************************************
 * WdsTransportNamespaceAutoCast coclass
 */

DEFINE_GUID(CLSID_WdsTransportNamespaceAutoCast, 0xb091f5a8, 0x6a99, 0x478d, 0xb2,0x3b, 0x09,0xe8,0xfe,0xe0,0x45,0x74);

#ifdef __cplusplus
class DECLSPEC_UUID("b091f5a8-6a99-478d-b23b-09e8fee04574") WdsTransportNamespaceAutoCast;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportNamespaceAutoCast, 0xb091f5a8, 0x6a99, 0x478d, 0xb2,0x3b, 0x09,0xe8,0xfe,0xe0,0x45,0x74)
#endif
#endif

/*****************************************************************************
 * WdsTransportNamespaceScheduledCast coclass
 */

DEFINE_GUID(CLSID_WdsTransportNamespaceScheduledCast, 0xbadc1897, 0x7025, 0x44eb, 0x91,0x08, 0xfb,0x61,0xc4,0x05,0x57,0x92);

#ifdef __cplusplus
class DECLSPEC_UUID("badc1897-7025-44eb-9108-fb61c4055792") WdsTransportNamespaceScheduledCast;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportNamespaceScheduledCast, 0xbadc1897, 0x7025, 0x44eb, 0x91,0x08, 0xfb,0x61,0xc4,0x05,0x57,0x92)
#endif
#endif

/*****************************************************************************
 * WdsTransportNamespaceScheduledCastManualStart coclass
 */

DEFINE_GUID(CLSID_WdsTransportNamespaceScheduledCastManualStart, 0xd3e1a2aa, 0xcaac, 0x460e, 0xb9,0x8a, 0x47,0xf9,0xf3,0x18,0xa1,0xfa);

#ifdef __cplusplus
class DECLSPEC_UUID("d3e1a2aa-caac-460e-b98a-47f9f318a1fa") WdsTransportNamespaceScheduledCastManualStart;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportNamespaceScheduledCastManualStart, 0xd3e1a2aa, 0xcaac, 0x460e, 0xb9,0x8a, 0x47,0xf9,0xf3,0x18,0xa1,0xfa)
#endif
#endif

/*****************************************************************************
 * WdsTransportNamespaceScheduledCastAutoStart coclass
 */

DEFINE_GUID(CLSID_WdsTransportNamespaceScheduledCastAutoStart, 0xa1107052, 0x122c, 0x4b81, 0x9b,0x7c, 0x38,0x6e,0x68,0x55,0x38,0x3f);

#ifdef __cplusplus
class DECLSPEC_UUID("a1107052-122c-4b81-9b7c-386e6855383f") WdsTransportNamespaceScheduledCastAutoStart;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportNamespaceScheduledCastAutoStart, 0xa1107052, 0x122c, 0x4b81, 0x9b,0x7c, 0x38,0x6e,0x68,0x55,0x38,0x3f)
#endif
#endif

/*****************************************************************************
 * WdsTransportContent coclass
 */

DEFINE_GUID(CLSID_WdsTransportContent, 0x0a891fe7, 0x4a3f, 0x4c65, 0xb6,0xf2, 0x14,0x67,0x61,0x96,0x79,0xea);

#ifdef __cplusplus
class DECLSPEC_UUID("0a891fe7-4a3f-4c65-b6f2-1467619679ea") WdsTransportContent;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportContent, 0x0a891fe7, 0x4a3f, 0x4c65, 0xb6,0xf2, 0x14,0x67,0x61,0x96,0x79,0xea)
#endif
#endif

/*****************************************************************************
 * WdsTransportSession coclass
 */

DEFINE_GUID(CLSID_WdsTransportSession, 0x749ac4e0, 0x67bc, 0x4743, 0xbf,0xe5, 0xca,0xcb,0x1f,0x26,0xf5,0x7f);

#ifdef __cplusplus
class DECLSPEC_UUID("749ac4e0-67bc-4743-bfe5-cacb1f26f57f") WdsTransportSession;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportSession, 0x749ac4e0, 0x67bc, 0x4743, 0xbf,0xe5, 0xca,0xcb,0x1f,0x26,0xf5,0x7f)
#endif
#endif

/*****************************************************************************
 * WdsTransportClient coclass
 */

DEFINE_GUID(CLSID_WdsTransportClient, 0x66d2c5e9, 0x0ff6, 0x49ec, 0x97,0x33, 0xda,0xfb,0x1e,0x01,0xdf,0x1c);

#ifdef __cplusplus
class DECLSPEC_UUID("66d2c5e9-0ff6-49ec-9733-dafb1e01df1c") WdsTransportClient;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportClient, 0x66d2c5e9, 0x0ff6, 0x49ec, 0x97,0x33, 0xda,0xfb,0x1e,0x01,0xdf,0x1c)
#endif
#endif

/*****************************************************************************
 * WdsTransportTftpClient coclass
 */

DEFINE_GUID(CLSID_WdsTransportTftpClient, 0x50343925, 0x7c5c, 0x4c8c, 0x96,0xc4, 0xad,0x9f,0xa5,0x00,0x5f,0xba);

#ifdef __cplusplus
class DECLSPEC_UUID("50343925-7c5c-4c8c-96c4-ad9fa5005fba") WdsTransportTftpClient;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportTftpClient, 0x50343925, 0x7c5c, 0x4c8c, 0x96,0xc4, 0xad,0x9f,0xa5,0x00,0x5f,0xba)
#endif
#endif

/*****************************************************************************
 * WdsTransportTftpManager coclass
 */

DEFINE_GUID(CLSID_WdsTransportTftpManager, 0xc8e9dca2, 0x3241, 0x4e4d, 0xb8,0x06, 0xbc,0x74,0x01,0x9d,0xfe,0xda);

#ifdef __cplusplus
class DECLSPEC_UUID("c8e9dca2-3241-4e4d-b806-bc74019dfeda") WdsTransportTftpManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportTftpManager, 0xc8e9dca2, 0x3241, 0x4e4d, 0xb8,0x06, 0xbc,0x74,0x01,0x9d,0xfe,0xda)
#endif
#endif

/*****************************************************************************
 * WdsTransportContentProvider coclass
 */

DEFINE_GUID(CLSID_WdsTransportContentProvider, 0xe0be741f, 0x5a75, 0x4eb9, 0x8a,0x2d, 0x5e,0x18,0x9b,0x45,0xf3,0x27);

#ifdef __cplusplus
class DECLSPEC_UUID("e0be741f-5a75-4eb9-8a2d-5e189b45f327") WdsTransportContentProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WdsTransportContentProvider, 0xe0be741f, 0x5a75, 0x4eb9, 0x8a,0x2d, 0x5e,0x18,0x9b,0x45,0xf3,0x27)
#endif
#endif

#endif /* __WdsTptMgmtLib_LIBRARY_DEFINED__ */
#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wdstptmgmt_h__ */
