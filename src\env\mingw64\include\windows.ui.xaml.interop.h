/*** Autogenerated by WIDL 10.8 from include/windows.ui.xaml.interop.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_ui_xaml_interop_h__
#define __windows_ui_xaml_interop_h__

/* Forward declarations */

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeKind __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeKind;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                namespace Interop {
                    typedef struct TypeName TypeName;
                }
            }
        }
    }
}
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                namespace Interop {
                    enum TypeKind {
                        TypeKind_Primitive = 0,
                        TypeKind_Metadata = 1,
                        TypeKind_Custom = 2
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeKind {
    TypeKind_Primitive = 0,
    TypeKind_Metadata = 1,
    TypeKind_Custom = 2
};
#ifdef WIDL_using_Windows_UI_Xaml_Interop
#define TypeKind __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeKind
#endif /* WIDL_using_Windows_UI_Xaml_Interop */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Xaml {
                namespace Interop {
                    struct TypeName {
                        HSTRING Name;
                        ABI::Windows::UI::Xaml::Interop::TypeKind Kind;
                    };
                }
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName {
    HSTRING Name;
    __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeKind Kind;
};
#ifdef WIDL_using_Windows_UI_Xaml_Interop
#define TypeName __x_ABI_CWindows_CUI_CXaml_CInterop_CTypeName
#endif /* WIDL_using_Windows_UI_Xaml_Interop */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_ui_xaml_interop_h__ */
