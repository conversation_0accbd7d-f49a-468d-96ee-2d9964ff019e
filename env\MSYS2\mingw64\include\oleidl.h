/*** Autogenerated by WIDL 10.8 from include/oleidl.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __oleidl_h__
#define __oleidl_h__

/* Forward declarations */

#ifndef __IOleAdviseHolder_FWD_DEFINED__
#define __IOleAdviseHolder_FWD_DEFINED__
typedef interface IOleAdviseHolder IOleAdviseHolder;
#ifdef __cplusplus
interface IOleAdviseHolder;
#endif /* __cplusplus */
#endif

#ifndef __IOleCache_FWD_DEFINED__
#define __IOleCache_FWD_DEFINED__
typedef interface IOleCache IOleCache;
#ifdef __cplusplus
interface IOleCache;
#endif /* __cplusplus */
#endif

#ifndef __IOleCache2_FWD_DEFINED__
#define __IOleCache2_FWD_DEFINED__
typedef interface IOleCache2 IOleCache2;
#ifdef __cplusplus
interface IOleCache2;
#endif /* __cplusplus */
#endif

#ifndef __IOleCacheControl_FWD_DEFINED__
#define __IOleCacheControl_FWD_DEFINED__
typedef interface IOleCacheControl IOleCacheControl;
#ifdef __cplusplus
interface IOleCacheControl;
#endif /* __cplusplus */
#endif

#ifndef __IParseDisplayName_FWD_DEFINED__
#define __IParseDisplayName_FWD_DEFINED__
typedef interface IParseDisplayName IParseDisplayName;
#ifdef __cplusplus
interface IParseDisplayName;
#endif /* __cplusplus */
#endif

#ifndef __IOleContainer_FWD_DEFINED__
#define __IOleContainer_FWD_DEFINED__
typedef interface IOleContainer IOleContainer;
#ifdef __cplusplus
interface IOleContainer;
#endif /* __cplusplus */
#endif

#ifndef __IOleClientSite_FWD_DEFINED__
#define __IOleClientSite_FWD_DEFINED__
typedef interface IOleClientSite IOleClientSite;
#ifdef __cplusplus
interface IOleClientSite;
#endif /* __cplusplus */
#endif

#ifndef __IOleObject_FWD_DEFINED__
#define __IOleObject_FWD_DEFINED__
typedef interface IOleObject IOleObject;
#ifdef __cplusplus
interface IOleObject;
#endif /* __cplusplus */
#endif

#ifndef __IOleWindow_FWD_DEFINED__
#define __IOleWindow_FWD_DEFINED__
typedef interface IOleWindow IOleWindow;
#ifdef __cplusplus
interface IOleWindow;
#endif /* __cplusplus */
#endif

#ifndef __IOleLink_FWD_DEFINED__
#define __IOleLink_FWD_DEFINED__
typedef interface IOleLink IOleLink;
#ifdef __cplusplus
interface IOleLink;
#endif /* __cplusplus */
#endif

#ifndef __IOleItemContainer_FWD_DEFINED__
#define __IOleItemContainer_FWD_DEFINED__
typedef interface IOleItemContainer IOleItemContainer;
#ifdef __cplusplus
interface IOleItemContainer;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceUIWindow_FWD_DEFINED__
#define __IOleInPlaceUIWindow_FWD_DEFINED__
typedef interface IOleInPlaceUIWindow IOleInPlaceUIWindow;
#ifdef __cplusplus
interface IOleInPlaceUIWindow;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceActiveObject_FWD_DEFINED__
#define __IOleInPlaceActiveObject_FWD_DEFINED__
typedef interface IOleInPlaceActiveObject IOleInPlaceActiveObject;
#ifdef __cplusplus
interface IOleInPlaceActiveObject;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceFrame_FWD_DEFINED__
#define __IOleInPlaceFrame_FWD_DEFINED__
typedef interface IOleInPlaceFrame IOleInPlaceFrame;
#ifdef __cplusplus
interface IOleInPlaceFrame;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceObject_FWD_DEFINED__
#define __IOleInPlaceObject_FWD_DEFINED__
typedef interface IOleInPlaceObject IOleInPlaceObject;
#ifdef __cplusplus
interface IOleInPlaceObject;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceSite_FWD_DEFINED__
#define __IOleInPlaceSite_FWD_DEFINED__
typedef interface IOleInPlaceSite IOleInPlaceSite;
#ifdef __cplusplus
interface IOleInPlaceSite;
#endif /* __cplusplus */
#endif

#ifndef __IContinue_FWD_DEFINED__
#define __IContinue_FWD_DEFINED__
typedef interface IContinue IContinue;
#ifdef __cplusplus
interface IContinue;
#endif /* __cplusplus */
#endif

#ifndef __IViewObject_FWD_DEFINED__
#define __IViewObject_FWD_DEFINED__
typedef interface IViewObject IViewObject;
#ifdef __cplusplus
interface IViewObject;
#endif /* __cplusplus */
#endif

#ifndef __IViewObject2_FWD_DEFINED__
#define __IViewObject2_FWD_DEFINED__
typedef interface IViewObject2 IViewObject2;
#ifdef __cplusplus
interface IViewObject2;
#endif /* __cplusplus */
#endif

#ifndef __IDropSource_FWD_DEFINED__
#define __IDropSource_FWD_DEFINED__
typedef interface IDropSource IDropSource;
#ifdef __cplusplus
interface IDropSource;
#endif /* __cplusplus */
#endif

#ifndef __IDropTarget_FWD_DEFINED__
#define __IDropTarget_FWD_DEFINED__
typedef interface IDropTarget IDropTarget;
#ifdef __cplusplus
interface IDropTarget;
#endif /* __cplusplus */
#endif

#ifndef __IDropSourceNotify_FWD_DEFINED__
#define __IDropSourceNotify_FWD_DEFINED__
typedef interface IDropSourceNotify IDropSourceNotify;
#ifdef __cplusplus
interface IDropSourceNotify;
#endif /* __cplusplus */
#endif

#ifndef __IEnumOLEVERB_FWD_DEFINED__
#define __IEnumOLEVERB_FWD_DEFINED__
typedef interface IEnumOLEVERB IEnumOLEVERB;
#ifdef __cplusplus
interface IEnumOLEVERB;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <objidl.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */


#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IOleInPlaceActiveObject_FWD_DEFINED__
#define __IOleInPlaceActiveObject_FWD_DEFINED__
typedef interface IOleInPlaceActiveObject IOleInPlaceActiveObject;
#ifdef __cplusplus
interface IOleInPlaceActiveObject;
#endif /* __cplusplus */
#endif

#ifndef __IEnumOLEVERB_FWD_DEFINED__
#define __IEnumOLEVERB_FWD_DEFINED__
typedef interface IEnumOLEVERB IEnumOLEVERB;
#ifdef __cplusplus
interface IEnumOLEVERB;
#endif /* __cplusplus */
#endif


/*****************************************************************************
 * IOleAdviseHolder interface
 */
#ifndef __IOleAdviseHolder_INTERFACE_DEFINED__
#define __IOleAdviseHolder_INTERFACE_DEFINED__

typedef IOleAdviseHolder *LPOLEADVISEHOLDER;

DEFINE_GUID(IID_IOleAdviseHolder, 0x00000111, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000111-0000-0000-c000-000000000046")
IOleAdviseHolder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Advise(
        IAdviseSink *pAdvise,
        DWORD *pdwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unadvise(
        DWORD dwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumAdvise(
        IEnumSTATDATA **ppenumAdvise) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendOnRename(
        IMoniker *pmk) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendOnSave(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendOnClose(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleAdviseHolder, 0x00000111, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleAdviseHolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleAdviseHolder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleAdviseHolder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleAdviseHolder *This);

    /*** IOleAdviseHolder methods ***/
    HRESULT (STDMETHODCALLTYPE *Advise)(
        IOleAdviseHolder *This,
        IAdviseSink *pAdvise,
        DWORD *pdwConnection);

    HRESULT (STDMETHODCALLTYPE *Unadvise)(
        IOleAdviseHolder *This,
        DWORD dwConnection);

    HRESULT (STDMETHODCALLTYPE *EnumAdvise)(
        IOleAdviseHolder *This,
        IEnumSTATDATA **ppenumAdvise);

    HRESULT (STDMETHODCALLTYPE *SendOnRename)(
        IOleAdviseHolder *This,
        IMoniker *pmk);

    HRESULT (STDMETHODCALLTYPE *SendOnSave)(
        IOleAdviseHolder *This);

    HRESULT (STDMETHODCALLTYPE *SendOnClose)(
        IOleAdviseHolder *This);

    END_INTERFACE
} IOleAdviseHolderVtbl;

interface IOleAdviseHolder {
    CONST_VTBL IOleAdviseHolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleAdviseHolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleAdviseHolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleAdviseHolder_Release(This) (This)->lpVtbl->Release(This)
/*** IOleAdviseHolder methods ***/
#define IOleAdviseHolder_Advise(This,pAdvise,pdwConnection) (This)->lpVtbl->Advise(This,pAdvise,pdwConnection)
#define IOleAdviseHolder_Unadvise(This,dwConnection) (This)->lpVtbl->Unadvise(This,dwConnection)
#define IOleAdviseHolder_EnumAdvise(This,ppenumAdvise) (This)->lpVtbl->EnumAdvise(This,ppenumAdvise)
#define IOleAdviseHolder_SendOnRename(This,pmk) (This)->lpVtbl->SendOnRename(This,pmk)
#define IOleAdviseHolder_SendOnSave(This) (This)->lpVtbl->SendOnSave(This)
#define IOleAdviseHolder_SendOnClose(This) (This)->lpVtbl->SendOnClose(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleAdviseHolder_QueryInterface(IOleAdviseHolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleAdviseHolder_AddRef(IOleAdviseHolder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleAdviseHolder_Release(IOleAdviseHolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleAdviseHolder methods ***/
static inline HRESULT IOleAdviseHolder_Advise(IOleAdviseHolder* This,IAdviseSink *pAdvise,DWORD *pdwConnection) {
    return This->lpVtbl->Advise(This,pAdvise,pdwConnection);
}
static inline HRESULT IOleAdviseHolder_Unadvise(IOleAdviseHolder* This,DWORD dwConnection) {
    return This->lpVtbl->Unadvise(This,dwConnection);
}
static inline HRESULT IOleAdviseHolder_EnumAdvise(IOleAdviseHolder* This,IEnumSTATDATA **ppenumAdvise) {
    return This->lpVtbl->EnumAdvise(This,ppenumAdvise);
}
static inline HRESULT IOleAdviseHolder_SendOnRename(IOleAdviseHolder* This,IMoniker *pmk) {
    return This->lpVtbl->SendOnRename(This,pmk);
}
static inline HRESULT IOleAdviseHolder_SendOnSave(IOleAdviseHolder* This) {
    return This->lpVtbl->SendOnSave(This);
}
static inline HRESULT IOleAdviseHolder_SendOnClose(IOleAdviseHolder* This) {
    return This->lpVtbl->SendOnClose(This);
}
#endif
#endif

#endif


#endif  /* __IOleAdviseHolder_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IOleCache interface
 */
#ifndef __IOleCache_INTERFACE_DEFINED__
#define __IOleCache_INTERFACE_DEFINED__

typedef IOleCache *LPOLECACHE;

DEFINE_GUID(IID_IOleCache, 0x0000011e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000011e-0000-0000-c000-000000000046")
IOleCache : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Cache(
        FORMATETC *pformatetc,
        DWORD advf,
        DWORD *pdwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE Uncache(
        DWORD dwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumCache(
        IEnumSTATDATA **ppenumSTATDATA) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitCache(
        IDataObject *pDataObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetData(
        FORMATETC *pformatetc,
        STGMEDIUM *pmedium,
        WINBOOL fRelease) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleCache, 0x0000011e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleCacheVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleCache *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleCache *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleCache *This);

    /*** IOleCache methods ***/
    HRESULT (STDMETHODCALLTYPE *Cache)(
        IOleCache *This,
        FORMATETC *pformatetc,
        DWORD advf,
        DWORD *pdwConnection);

    HRESULT (STDMETHODCALLTYPE *Uncache)(
        IOleCache *This,
        DWORD dwConnection);

    HRESULT (STDMETHODCALLTYPE *EnumCache)(
        IOleCache *This,
        IEnumSTATDATA **ppenumSTATDATA);

    HRESULT (STDMETHODCALLTYPE *InitCache)(
        IOleCache *This,
        IDataObject *pDataObject);

    HRESULT (STDMETHODCALLTYPE *SetData)(
        IOleCache *This,
        FORMATETC *pformatetc,
        STGMEDIUM *pmedium,
        WINBOOL fRelease);

    END_INTERFACE
} IOleCacheVtbl;

interface IOleCache {
    CONST_VTBL IOleCacheVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleCache_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleCache_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleCache_Release(This) (This)->lpVtbl->Release(This)
/*** IOleCache methods ***/
#define IOleCache_Cache(This,pformatetc,advf,pdwConnection) (This)->lpVtbl->Cache(This,pformatetc,advf,pdwConnection)
#define IOleCache_Uncache(This,dwConnection) (This)->lpVtbl->Uncache(This,dwConnection)
#define IOleCache_EnumCache(This,ppenumSTATDATA) (This)->lpVtbl->EnumCache(This,ppenumSTATDATA)
#define IOleCache_InitCache(This,pDataObject) (This)->lpVtbl->InitCache(This,pDataObject)
#define IOleCache_SetData(This,pformatetc,pmedium,fRelease) (This)->lpVtbl->SetData(This,pformatetc,pmedium,fRelease)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleCache_QueryInterface(IOleCache* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleCache_AddRef(IOleCache* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleCache_Release(IOleCache* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleCache methods ***/
static inline HRESULT IOleCache_Cache(IOleCache* This,FORMATETC *pformatetc,DWORD advf,DWORD *pdwConnection) {
    return This->lpVtbl->Cache(This,pformatetc,advf,pdwConnection);
}
static inline HRESULT IOleCache_Uncache(IOleCache* This,DWORD dwConnection) {
    return This->lpVtbl->Uncache(This,dwConnection);
}
static inline HRESULT IOleCache_EnumCache(IOleCache* This,IEnumSTATDATA **ppenumSTATDATA) {
    return This->lpVtbl->EnumCache(This,ppenumSTATDATA);
}
static inline HRESULT IOleCache_InitCache(IOleCache* This,IDataObject *pDataObject) {
    return This->lpVtbl->InitCache(This,pDataObject);
}
static inline HRESULT IOleCache_SetData(IOleCache* This,FORMATETC *pformatetc,STGMEDIUM *pmedium,WINBOOL fRelease) {
    return This->lpVtbl->SetData(This,pformatetc,pmedium,fRelease);
}
#endif
#endif

#endif


#endif  /* __IOleCache_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleCache2 interface
 */
#ifndef __IOleCache2_INTERFACE_DEFINED__
#define __IOleCache2_INTERFACE_DEFINED__

typedef IOleCache2 *LPOLECACHE2;

#define UPDFCACHE_NODATACACHE (0x1)

#define UPDFCACHE_ONSAVECACHE (0x2)

#define UPDFCACHE_ONSTOPCACHE (0x4)

#define UPDFCACHE_NORMALCACHE (0x8)

#define UPDFCACHE_IFBLANK (0x10)

#define UPDFCACHE_ONLYIFBLANK (0x80000000)

#define UPDFCACHE_IFBLANKORONSAVECACHE (UPDFCACHE_IFBLANK | UPDFCACHE_ONSAVECACHE)

#define UPDFCACHE_ALL ((DWORD)~UPDFCACHE_ONLYIFBLANK)

#define UPDFCACHE_ALLBUTNODATACACHE (UPDFCACHE_ALL & (DWORD)~UPDFCACHE_NODATACACHE)


typedef enum tagDISCARDCACHE {
    DISCARDCACHE_SAVEIFDIRTY = 0,
    DISCARDCACHE_NOSAVE = 1
} DISCARDCACHE;

DEFINE_GUID(IID_IOleCache2, 0x00000128, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000128-0000-0000-c000-000000000046")
IOleCache2 : public IOleCache
{
    virtual HRESULT STDMETHODCALLTYPE UpdateCache(
        LPDATAOBJECT pDataObject,
        DWORD grfUpdf,
        LPVOID pReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE DiscardCache(
        DWORD dwDiscardOptions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleCache2, 0x00000128, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleCache2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleCache2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleCache2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleCache2 *This);

    /*** IOleCache methods ***/
    HRESULT (STDMETHODCALLTYPE *Cache)(
        IOleCache2 *This,
        FORMATETC *pformatetc,
        DWORD advf,
        DWORD *pdwConnection);

    HRESULT (STDMETHODCALLTYPE *Uncache)(
        IOleCache2 *This,
        DWORD dwConnection);

    HRESULT (STDMETHODCALLTYPE *EnumCache)(
        IOleCache2 *This,
        IEnumSTATDATA **ppenumSTATDATA);

    HRESULT (STDMETHODCALLTYPE *InitCache)(
        IOleCache2 *This,
        IDataObject *pDataObject);

    HRESULT (STDMETHODCALLTYPE *SetData)(
        IOleCache2 *This,
        FORMATETC *pformatetc,
        STGMEDIUM *pmedium,
        WINBOOL fRelease);

    /*** IOleCache2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UpdateCache)(
        IOleCache2 *This,
        LPDATAOBJECT pDataObject,
        DWORD grfUpdf,
        LPVOID pReserved);

    HRESULT (STDMETHODCALLTYPE *DiscardCache)(
        IOleCache2 *This,
        DWORD dwDiscardOptions);

    END_INTERFACE
} IOleCache2Vtbl;

interface IOleCache2 {
    CONST_VTBL IOleCache2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleCache2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleCache2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleCache2_Release(This) (This)->lpVtbl->Release(This)
/*** IOleCache methods ***/
#define IOleCache2_Cache(This,pformatetc,advf,pdwConnection) (This)->lpVtbl->Cache(This,pformatetc,advf,pdwConnection)
#define IOleCache2_Uncache(This,dwConnection) (This)->lpVtbl->Uncache(This,dwConnection)
#define IOleCache2_EnumCache(This,ppenumSTATDATA) (This)->lpVtbl->EnumCache(This,ppenumSTATDATA)
#define IOleCache2_InitCache(This,pDataObject) (This)->lpVtbl->InitCache(This,pDataObject)
#define IOleCache2_SetData(This,pformatetc,pmedium,fRelease) (This)->lpVtbl->SetData(This,pformatetc,pmedium,fRelease)
/*** IOleCache2 methods ***/
#define IOleCache2_UpdateCache(This,pDataObject,grfUpdf,pReserved) (This)->lpVtbl->UpdateCache(This,pDataObject,grfUpdf,pReserved)
#define IOleCache2_DiscardCache(This,dwDiscardOptions) (This)->lpVtbl->DiscardCache(This,dwDiscardOptions)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleCache2_QueryInterface(IOleCache2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleCache2_AddRef(IOleCache2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleCache2_Release(IOleCache2* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleCache methods ***/
static inline HRESULT IOleCache2_Cache(IOleCache2* This,FORMATETC *pformatetc,DWORD advf,DWORD *pdwConnection) {
    return This->lpVtbl->Cache(This,pformatetc,advf,pdwConnection);
}
static inline HRESULT IOleCache2_Uncache(IOleCache2* This,DWORD dwConnection) {
    return This->lpVtbl->Uncache(This,dwConnection);
}
static inline HRESULT IOleCache2_EnumCache(IOleCache2* This,IEnumSTATDATA **ppenumSTATDATA) {
    return This->lpVtbl->EnumCache(This,ppenumSTATDATA);
}
static inline HRESULT IOleCache2_InitCache(IOleCache2* This,IDataObject *pDataObject) {
    return This->lpVtbl->InitCache(This,pDataObject);
}
static inline HRESULT IOleCache2_SetData(IOleCache2* This,FORMATETC *pformatetc,STGMEDIUM *pmedium,WINBOOL fRelease) {
    return This->lpVtbl->SetData(This,pformatetc,pmedium,fRelease);
}
/*** IOleCache2 methods ***/
static inline HRESULT IOleCache2_UpdateCache(IOleCache2* This,LPDATAOBJECT pDataObject,DWORD grfUpdf,LPVOID pReserved) {
    return This->lpVtbl->UpdateCache(This,pDataObject,grfUpdf,pReserved);
}
static inline HRESULT IOleCache2_DiscardCache(IOleCache2* This,DWORD dwDiscardOptions) {
    return This->lpVtbl->DiscardCache(This,dwDiscardOptions);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IOleCache2_RemoteUpdateCache_Proxy(
    IOleCache2* This,
    LPDATAOBJECT pDataObject,
    DWORD grfUpdf,
    LONG_PTR pReserved);
void __RPC_STUB IOleCache2_RemoteUpdateCache_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IOleCache2_UpdateCache_Proxy(
    IOleCache2* This,
    LPDATAOBJECT pDataObject,
    DWORD grfUpdf,
    LPVOID pReserved);
HRESULT __RPC_STUB IOleCache2_UpdateCache_Stub(
    IOleCache2* This,
    LPDATAOBJECT pDataObject,
    DWORD grfUpdf,
    LONG_PTR pReserved);

#endif  /* __IOleCache2_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IOleCacheControl interface
 */
#ifndef __IOleCacheControl_INTERFACE_DEFINED__
#define __IOleCacheControl_INTERFACE_DEFINED__

typedef IOleCacheControl *LPOLECACHECONTROL;

DEFINE_GUID(IID_IOleCacheControl, 0x00000129, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000129-0000-0000-c000-000000000046")
IOleCacheControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnRun(
        LPDATAOBJECT pDataObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStop(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleCacheControl, 0x00000129, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleCacheControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleCacheControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleCacheControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleCacheControl *This);

    /*** IOleCacheControl methods ***/
    HRESULT (STDMETHODCALLTYPE *OnRun)(
        IOleCacheControl *This,
        LPDATAOBJECT pDataObject);

    HRESULT (STDMETHODCALLTYPE *OnStop)(
        IOleCacheControl *This);

    END_INTERFACE
} IOleCacheControlVtbl;

interface IOleCacheControl {
    CONST_VTBL IOleCacheControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleCacheControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleCacheControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleCacheControl_Release(This) (This)->lpVtbl->Release(This)
/*** IOleCacheControl methods ***/
#define IOleCacheControl_OnRun(This,pDataObject) (This)->lpVtbl->OnRun(This,pDataObject)
#define IOleCacheControl_OnStop(This) (This)->lpVtbl->OnStop(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleCacheControl_QueryInterface(IOleCacheControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleCacheControl_AddRef(IOleCacheControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleCacheControl_Release(IOleCacheControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleCacheControl methods ***/
static inline HRESULT IOleCacheControl_OnRun(IOleCacheControl* This,LPDATAOBJECT pDataObject) {
    return This->lpVtbl->OnRun(This,pDataObject);
}
static inline HRESULT IOleCacheControl_OnStop(IOleCacheControl* This) {
    return This->lpVtbl->OnStop(This);
}
#endif
#endif

#endif


#endif  /* __IOleCacheControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IParseDisplayName interface
 */
#ifndef __IParseDisplayName_INTERFACE_DEFINED__
#define __IParseDisplayName_INTERFACE_DEFINED__

typedef IParseDisplayName *LPPARSEDISPLAYNAME;

DEFINE_GUID(IID_IParseDisplayName, 0x0000011a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000011a-0000-0000-c000-000000000046")
IParseDisplayName : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ParseDisplayName(
        IBindCtx *pbc,
        LPOLESTR pszDisplayName,
        ULONG *pchEaten,
        IMoniker **ppmkOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IParseDisplayName, 0x0000011a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IParseDisplayNameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IParseDisplayName *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IParseDisplayName *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IParseDisplayName *This);

    /*** IParseDisplayName methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseDisplayName)(
        IParseDisplayName *This,
        IBindCtx *pbc,
        LPOLESTR pszDisplayName,
        ULONG *pchEaten,
        IMoniker **ppmkOut);

    END_INTERFACE
} IParseDisplayNameVtbl;

interface IParseDisplayName {
    CONST_VTBL IParseDisplayNameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IParseDisplayName_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IParseDisplayName_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IParseDisplayName_Release(This) (This)->lpVtbl->Release(This)
/*** IParseDisplayName methods ***/
#define IParseDisplayName_ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut) (This)->lpVtbl->ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut)
#else
/*** IUnknown methods ***/
static inline HRESULT IParseDisplayName_QueryInterface(IParseDisplayName* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IParseDisplayName_AddRef(IParseDisplayName* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IParseDisplayName_Release(IParseDisplayName* This) {
    return This->lpVtbl->Release(This);
}
/*** IParseDisplayName methods ***/
static inline HRESULT IParseDisplayName_ParseDisplayName(IParseDisplayName* This,IBindCtx *pbc,LPOLESTR pszDisplayName,ULONG *pchEaten,IMoniker **ppmkOut) {
    return This->lpVtbl->ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut);
}
#endif
#endif

#endif


#endif  /* __IParseDisplayName_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleContainer interface
 */
#ifndef __IOleContainer_INTERFACE_DEFINED__
#define __IOleContainer_INTERFACE_DEFINED__

typedef IOleContainer *LPOLECONTAINER;

DEFINE_GUID(IID_IOleContainer, 0x0000011b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000011b-0000-0000-c000-000000000046")
IOleContainer : public IParseDisplayName
{
    virtual HRESULT STDMETHODCALLTYPE EnumObjects(
        DWORD grfFlags,
        IEnumUnknown **ppenum) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockContainer(
        WINBOOL fLock) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleContainer, 0x0000011b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleContainer *This);

    /*** IParseDisplayName methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseDisplayName)(
        IOleContainer *This,
        IBindCtx *pbc,
        LPOLESTR pszDisplayName,
        ULONG *pchEaten,
        IMoniker **ppmkOut);

    /*** IOleContainer methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumObjects)(
        IOleContainer *This,
        DWORD grfFlags,
        IEnumUnknown **ppenum);

    HRESULT (STDMETHODCALLTYPE *LockContainer)(
        IOleContainer *This,
        WINBOOL fLock);

    END_INTERFACE
} IOleContainerVtbl;

interface IOleContainer {
    CONST_VTBL IOleContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IParseDisplayName methods ***/
#define IOleContainer_ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut) (This)->lpVtbl->ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut)
/*** IOleContainer methods ***/
#define IOleContainer_EnumObjects(This,grfFlags,ppenum) (This)->lpVtbl->EnumObjects(This,grfFlags,ppenum)
#define IOleContainer_LockContainer(This,fLock) (This)->lpVtbl->LockContainer(This,fLock)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleContainer_QueryInterface(IOleContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleContainer_AddRef(IOleContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleContainer_Release(IOleContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IParseDisplayName methods ***/
static inline HRESULT IOleContainer_ParseDisplayName(IOleContainer* This,IBindCtx *pbc,LPOLESTR pszDisplayName,ULONG *pchEaten,IMoniker **ppmkOut) {
    return This->lpVtbl->ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut);
}
/*** IOleContainer methods ***/
static inline HRESULT IOleContainer_EnumObjects(IOleContainer* This,DWORD grfFlags,IEnumUnknown **ppenum) {
    return This->lpVtbl->EnumObjects(This,grfFlags,ppenum);
}
static inline HRESULT IOleContainer_LockContainer(IOleContainer* This,WINBOOL fLock) {
    return This->lpVtbl->LockContainer(This,fLock);
}
#endif
#endif

#endif


#endif  /* __IOleContainer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleClientSite interface
 */
#ifndef __IOleClientSite_INTERFACE_DEFINED__
#define __IOleClientSite_INTERFACE_DEFINED__

typedef IOleClientSite *LPOLECLIENTSITE;

DEFINE_GUID(IID_IOleClientSite, 0x00000118, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000118-0000-0000-c000-000000000046")
IOleClientSite : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SaveObject(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMoniker(
        DWORD dwAssign,
        DWORD dwWhichMoniker,
        IMoniker **ppmk) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContainer(
        IOleContainer **ppContainer) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowObject(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnShowWindow(
        WINBOOL fShow) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestNewObjectLayout(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleClientSite, 0x00000118, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleClientSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleClientSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleClientSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleClientSite *This);

    /*** IOleClientSite methods ***/
    HRESULT (STDMETHODCALLTYPE *SaveObject)(
        IOleClientSite *This);

    HRESULT (STDMETHODCALLTYPE *GetMoniker)(
        IOleClientSite *This,
        DWORD dwAssign,
        DWORD dwWhichMoniker,
        IMoniker **ppmk);

    HRESULT (STDMETHODCALLTYPE *GetContainer)(
        IOleClientSite *This,
        IOleContainer **ppContainer);

    HRESULT (STDMETHODCALLTYPE *ShowObject)(
        IOleClientSite *This);

    HRESULT (STDMETHODCALLTYPE *OnShowWindow)(
        IOleClientSite *This,
        WINBOOL fShow);

    HRESULT (STDMETHODCALLTYPE *RequestNewObjectLayout)(
        IOleClientSite *This);

    END_INTERFACE
} IOleClientSiteVtbl;

interface IOleClientSite {
    CONST_VTBL IOleClientSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleClientSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleClientSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleClientSite_Release(This) (This)->lpVtbl->Release(This)
/*** IOleClientSite methods ***/
#define IOleClientSite_SaveObject(This) (This)->lpVtbl->SaveObject(This)
#define IOleClientSite_GetMoniker(This,dwAssign,dwWhichMoniker,ppmk) (This)->lpVtbl->GetMoniker(This,dwAssign,dwWhichMoniker,ppmk)
#define IOleClientSite_GetContainer(This,ppContainer) (This)->lpVtbl->GetContainer(This,ppContainer)
#define IOleClientSite_ShowObject(This) (This)->lpVtbl->ShowObject(This)
#define IOleClientSite_OnShowWindow(This,fShow) (This)->lpVtbl->OnShowWindow(This,fShow)
#define IOleClientSite_RequestNewObjectLayout(This) (This)->lpVtbl->RequestNewObjectLayout(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleClientSite_QueryInterface(IOleClientSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleClientSite_AddRef(IOleClientSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleClientSite_Release(IOleClientSite* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleClientSite methods ***/
static inline HRESULT IOleClientSite_SaveObject(IOleClientSite* This) {
    return This->lpVtbl->SaveObject(This);
}
static inline HRESULT IOleClientSite_GetMoniker(IOleClientSite* This,DWORD dwAssign,DWORD dwWhichMoniker,IMoniker **ppmk) {
    return This->lpVtbl->GetMoniker(This,dwAssign,dwWhichMoniker,ppmk);
}
static inline HRESULT IOleClientSite_GetContainer(IOleClientSite* This,IOleContainer **ppContainer) {
    return This->lpVtbl->GetContainer(This,ppContainer);
}
static inline HRESULT IOleClientSite_ShowObject(IOleClientSite* This) {
    return This->lpVtbl->ShowObject(This);
}
static inline HRESULT IOleClientSite_OnShowWindow(IOleClientSite* This,WINBOOL fShow) {
    return This->lpVtbl->OnShowWindow(This,fShow);
}
static inline HRESULT IOleClientSite_RequestNewObjectLayout(IOleClientSite* This) {
    return This->lpVtbl->RequestNewObjectLayout(This);
}
#endif
#endif

#endif


#endif  /* __IOleClientSite_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleObject interface
 */
#ifndef __IOleObject_INTERFACE_DEFINED__
#define __IOleObject_INTERFACE_DEFINED__

typedef IOleObject *LPOLEOBJECT;

typedef enum tagOLEGETMONIKER {
    OLEGETMONIKER_ONLYIFTHERE = 1,
    OLEGETMONIKER_FORCEASSIGN = 2,
    OLEGETMONIKER_UNASSIGN = 3,
    OLEGETMONIKER_TEMPFORUSER = 4
} OLEGETMONIKER;

typedef enum tagOLEWHICHMK {
    OLEWHICHMK_CONTAINER = 1,
    OLEWHICHMK_OBJREL = 2,
    OLEWHICHMK_OBJFULL = 3
} OLEWHICHMK;

typedef enum tagUSERCLASSTYPE {
    USERCLASSTYPE_FULL = 1,
    USERCLASSTYPE_SHORT = 2,
    USERCLASSTYPE_APPNAME = 3
} USERCLASSTYPE;

typedef enum tagOLEMISC {
    OLEMISC_RECOMPOSEONRESIZE = 0x1,
    OLEMISC_ONLYICONIC = 0x2,
    OLEMISC_INSERTNOTREPLACE = 0x4,
    OLEMISC_STATIC = 0x8,
    OLEMISC_CANTLINKINSIDE = 0x10,
    OLEMISC_CANLINKBYOLE1 = 0x20,
    OLEMISC_ISLINKOBJECT = 0x40,
    OLEMISC_INSIDEOUT = 0x80,
    OLEMISC_ACTIVATEWHENVISIBLE = 0x100,
    OLEMISC_RENDERINGISDEVICEINDEPENDENT = 0x200,
    OLEMISC_INVISIBLEATRUNTIME = 0x400,
    OLEMISC_ALWAYSRUN = 0x800,
    OLEMISC_ACTSLIKEBUTTON = 0x1000,
    OLEMISC_ACTSLIKELABEL = 0x2000,
    OLEMISC_NOUIACTIVATE = 0x4000,
    OLEMISC_ALIGNABLE = 0x8000,
    OLEMISC_SIMPLEFRAME = 0x10000,
    OLEMISC_SETCLIENTSITEFIRST = 0x20000,
    OLEMISC_IMEMODE = 0x40000,
    OLEMISC_IGNOREACTIVATEWHENVISIBLE = 0x80000,
    OLEMISC_WANTSTOMENUMERGE = 0x100000,
    OLEMISC_SUPPORTSMULTILEVELUNDO = 0x200000
} OLEMISC;
typedef enum tagOLECLOSE {
    OLECLOSE_SAVEIFDIRTY = 0,
    OLECLOSE_NOSAVE = 1,
    OLECLOSE_PROMPTSAVE = 2
} OLECLOSE;

DEFINE_GUID(IID_IOleObject, 0x00000112, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000112-0000-0000-c000-000000000046")
IOleObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetClientSite(
        IOleClientSite *pClientSite) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClientSite(
        IOleClientSite **ppClientSite) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHostNames(
        LPCOLESTR szContainerApp,
        LPCOLESTR szContainerObj) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        DWORD dwSaveOption) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMoniker(
        DWORD dwWhichMoniker,
        IMoniker *pmk) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMoniker(
        DWORD dwAssign,
        DWORD dwWhichMoniker,
        IMoniker **ppmk) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitFromData(
        IDataObject *pDataObject,
        WINBOOL fCreation,
        DWORD dwReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClipboardData(
        DWORD dwReserved,
        IDataObject **ppDataObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoVerb(
        LONG iVerb,
        LPMSG lpmsg,
        IOleClientSite *pActiveSite,
        LONG lindex,
        HWND hwndParent,
        LPCRECT lprcPosRect) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumVerbs(
        IEnumOLEVERB **ppEnumOleVerb) = 0;

    virtual HRESULT STDMETHODCALLTYPE Update(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsUpToDate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserClassID(
        CLSID *pClsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserType(
        DWORD dwFormOfType,
        LPOLESTR *pszUserType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetExtent(
        DWORD dwDrawAspect,
        SIZEL *psizel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExtent(
        DWORD dwDrawAspect,
        SIZEL *psizel) = 0;

    virtual HRESULT STDMETHODCALLTYPE Advise(
        IAdviseSink *pAdvSink,
        DWORD *pdwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unadvise(
        DWORD dwConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumAdvise(
        IEnumSTATDATA **ppenumAdvise) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMiscStatus(
        DWORD dwAspect,
        DWORD *pdwStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColorScheme(
        LOGPALETTE *pLogpal) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleObject, 0x00000112, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleObject *This);

    /*** IOleObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetClientSite)(
        IOleObject *This,
        IOleClientSite *pClientSite);

    HRESULT (STDMETHODCALLTYPE *GetClientSite)(
        IOleObject *This,
        IOleClientSite **ppClientSite);

    HRESULT (STDMETHODCALLTYPE *SetHostNames)(
        IOleObject *This,
        LPCOLESTR szContainerApp,
        LPCOLESTR szContainerObj);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IOleObject *This,
        DWORD dwSaveOption);

    HRESULT (STDMETHODCALLTYPE *SetMoniker)(
        IOleObject *This,
        DWORD dwWhichMoniker,
        IMoniker *pmk);

    HRESULT (STDMETHODCALLTYPE *GetMoniker)(
        IOleObject *This,
        DWORD dwAssign,
        DWORD dwWhichMoniker,
        IMoniker **ppmk);

    HRESULT (STDMETHODCALLTYPE *InitFromData)(
        IOleObject *This,
        IDataObject *pDataObject,
        WINBOOL fCreation,
        DWORD dwReserved);

    HRESULT (STDMETHODCALLTYPE *GetClipboardData)(
        IOleObject *This,
        DWORD dwReserved,
        IDataObject **ppDataObject);

    HRESULT (STDMETHODCALLTYPE *DoVerb)(
        IOleObject *This,
        LONG iVerb,
        LPMSG lpmsg,
        IOleClientSite *pActiveSite,
        LONG lindex,
        HWND hwndParent,
        LPCRECT lprcPosRect);

    HRESULT (STDMETHODCALLTYPE *EnumVerbs)(
        IOleObject *This,
        IEnumOLEVERB **ppEnumOleVerb);

    HRESULT (STDMETHODCALLTYPE *Update)(
        IOleObject *This);

    HRESULT (STDMETHODCALLTYPE *IsUpToDate)(
        IOleObject *This);

    HRESULT (STDMETHODCALLTYPE *GetUserClassID)(
        IOleObject *This,
        CLSID *pClsid);

    HRESULT (STDMETHODCALLTYPE *GetUserType)(
        IOleObject *This,
        DWORD dwFormOfType,
        LPOLESTR *pszUserType);

    HRESULT (STDMETHODCALLTYPE *SetExtent)(
        IOleObject *This,
        DWORD dwDrawAspect,
        SIZEL *psizel);

    HRESULT (STDMETHODCALLTYPE *GetExtent)(
        IOleObject *This,
        DWORD dwDrawAspect,
        SIZEL *psizel);

    HRESULT (STDMETHODCALLTYPE *Advise)(
        IOleObject *This,
        IAdviseSink *pAdvSink,
        DWORD *pdwConnection);

    HRESULT (STDMETHODCALLTYPE *Unadvise)(
        IOleObject *This,
        DWORD dwConnection);

    HRESULT (STDMETHODCALLTYPE *EnumAdvise)(
        IOleObject *This,
        IEnumSTATDATA **ppenumAdvise);

    HRESULT (STDMETHODCALLTYPE *GetMiscStatus)(
        IOleObject *This,
        DWORD dwAspect,
        DWORD *pdwStatus);

    HRESULT (STDMETHODCALLTYPE *SetColorScheme)(
        IOleObject *This,
        LOGPALETTE *pLogpal);

    END_INTERFACE
} IOleObjectVtbl;

interface IOleObject {
    CONST_VTBL IOleObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleObject_Release(This) (This)->lpVtbl->Release(This)
/*** IOleObject methods ***/
#define IOleObject_SetClientSite(This,pClientSite) (This)->lpVtbl->SetClientSite(This,pClientSite)
#define IOleObject_GetClientSite(This,ppClientSite) (This)->lpVtbl->GetClientSite(This,ppClientSite)
#define IOleObject_SetHostNames(This,szContainerApp,szContainerObj) (This)->lpVtbl->SetHostNames(This,szContainerApp,szContainerObj)
#define IOleObject_Close(This,dwSaveOption) (This)->lpVtbl->Close(This,dwSaveOption)
#define IOleObject_SetMoniker(This,dwWhichMoniker,pmk) (This)->lpVtbl->SetMoniker(This,dwWhichMoniker,pmk)
#define IOleObject_GetMoniker(This,dwAssign,dwWhichMoniker,ppmk) (This)->lpVtbl->GetMoniker(This,dwAssign,dwWhichMoniker,ppmk)
#define IOleObject_InitFromData(This,pDataObject,fCreation,dwReserved) (This)->lpVtbl->InitFromData(This,pDataObject,fCreation,dwReserved)
#define IOleObject_GetClipboardData(This,dwReserved,ppDataObject) (This)->lpVtbl->GetClipboardData(This,dwReserved,ppDataObject)
#define IOleObject_DoVerb(This,iVerb,lpmsg,pActiveSite,lindex,hwndParent,lprcPosRect) (This)->lpVtbl->DoVerb(This,iVerb,lpmsg,pActiveSite,lindex,hwndParent,lprcPosRect)
#define IOleObject_EnumVerbs(This,ppEnumOleVerb) (This)->lpVtbl->EnumVerbs(This,ppEnumOleVerb)
#define IOleObject_Update(This) (This)->lpVtbl->Update(This)
#define IOleObject_IsUpToDate(This) (This)->lpVtbl->IsUpToDate(This)
#define IOleObject_GetUserClassID(This,pClsid) (This)->lpVtbl->GetUserClassID(This,pClsid)
#define IOleObject_GetUserType(This,dwFormOfType,pszUserType) (This)->lpVtbl->GetUserType(This,dwFormOfType,pszUserType)
#define IOleObject_SetExtent(This,dwDrawAspect,psizel) (This)->lpVtbl->SetExtent(This,dwDrawAspect,psizel)
#define IOleObject_GetExtent(This,dwDrawAspect,psizel) (This)->lpVtbl->GetExtent(This,dwDrawAspect,psizel)
#define IOleObject_Advise(This,pAdvSink,pdwConnection) (This)->lpVtbl->Advise(This,pAdvSink,pdwConnection)
#define IOleObject_Unadvise(This,dwConnection) (This)->lpVtbl->Unadvise(This,dwConnection)
#define IOleObject_EnumAdvise(This,ppenumAdvise) (This)->lpVtbl->EnumAdvise(This,ppenumAdvise)
#define IOleObject_GetMiscStatus(This,dwAspect,pdwStatus) (This)->lpVtbl->GetMiscStatus(This,dwAspect,pdwStatus)
#define IOleObject_SetColorScheme(This,pLogpal) (This)->lpVtbl->SetColorScheme(This,pLogpal)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleObject_QueryInterface(IOleObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleObject_AddRef(IOleObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleObject_Release(IOleObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleObject methods ***/
static inline HRESULT IOleObject_SetClientSite(IOleObject* This,IOleClientSite *pClientSite) {
    return This->lpVtbl->SetClientSite(This,pClientSite);
}
static inline HRESULT IOleObject_GetClientSite(IOleObject* This,IOleClientSite **ppClientSite) {
    return This->lpVtbl->GetClientSite(This,ppClientSite);
}
static inline HRESULT IOleObject_SetHostNames(IOleObject* This,LPCOLESTR szContainerApp,LPCOLESTR szContainerObj) {
    return This->lpVtbl->SetHostNames(This,szContainerApp,szContainerObj);
}
static inline HRESULT IOleObject_Close(IOleObject* This,DWORD dwSaveOption) {
    return This->lpVtbl->Close(This,dwSaveOption);
}
static inline HRESULT IOleObject_SetMoniker(IOleObject* This,DWORD dwWhichMoniker,IMoniker *pmk) {
    return This->lpVtbl->SetMoniker(This,dwWhichMoniker,pmk);
}
static inline HRESULT IOleObject_GetMoniker(IOleObject* This,DWORD dwAssign,DWORD dwWhichMoniker,IMoniker **ppmk) {
    return This->lpVtbl->GetMoniker(This,dwAssign,dwWhichMoniker,ppmk);
}
static inline HRESULT IOleObject_InitFromData(IOleObject* This,IDataObject *pDataObject,WINBOOL fCreation,DWORD dwReserved) {
    return This->lpVtbl->InitFromData(This,pDataObject,fCreation,dwReserved);
}
static inline HRESULT IOleObject_GetClipboardData(IOleObject* This,DWORD dwReserved,IDataObject **ppDataObject) {
    return This->lpVtbl->GetClipboardData(This,dwReserved,ppDataObject);
}
static inline HRESULT IOleObject_DoVerb(IOleObject* This,LONG iVerb,LPMSG lpmsg,IOleClientSite *pActiveSite,LONG lindex,HWND hwndParent,LPCRECT lprcPosRect) {
    return This->lpVtbl->DoVerb(This,iVerb,lpmsg,pActiveSite,lindex,hwndParent,lprcPosRect);
}
static inline HRESULT IOleObject_EnumVerbs(IOleObject* This,IEnumOLEVERB **ppEnumOleVerb) {
    return This->lpVtbl->EnumVerbs(This,ppEnumOleVerb);
}
static inline HRESULT IOleObject_Update(IOleObject* This) {
    return This->lpVtbl->Update(This);
}
static inline HRESULT IOleObject_IsUpToDate(IOleObject* This) {
    return This->lpVtbl->IsUpToDate(This);
}
static inline HRESULT IOleObject_GetUserClassID(IOleObject* This,CLSID *pClsid) {
    return This->lpVtbl->GetUserClassID(This,pClsid);
}
static inline HRESULT IOleObject_GetUserType(IOleObject* This,DWORD dwFormOfType,LPOLESTR *pszUserType) {
    return This->lpVtbl->GetUserType(This,dwFormOfType,pszUserType);
}
static inline HRESULT IOleObject_SetExtent(IOleObject* This,DWORD dwDrawAspect,SIZEL *psizel) {
    return This->lpVtbl->SetExtent(This,dwDrawAspect,psizel);
}
static inline HRESULT IOleObject_GetExtent(IOleObject* This,DWORD dwDrawAspect,SIZEL *psizel) {
    return This->lpVtbl->GetExtent(This,dwDrawAspect,psizel);
}
static inline HRESULT IOleObject_Advise(IOleObject* This,IAdviseSink *pAdvSink,DWORD *pdwConnection) {
    return This->lpVtbl->Advise(This,pAdvSink,pdwConnection);
}
static inline HRESULT IOleObject_Unadvise(IOleObject* This,DWORD dwConnection) {
    return This->lpVtbl->Unadvise(This,dwConnection);
}
static inline HRESULT IOleObject_EnumAdvise(IOleObject* This,IEnumSTATDATA **ppenumAdvise) {
    return This->lpVtbl->EnumAdvise(This,ppenumAdvise);
}
static inline HRESULT IOleObject_GetMiscStatus(IOleObject* This,DWORD dwAspect,DWORD *pdwStatus) {
    return This->lpVtbl->GetMiscStatus(This,dwAspect,pdwStatus);
}
static inline HRESULT IOleObject_SetColorScheme(IOleObject* This,LOGPALETTE *pLogpal) {
    return This->lpVtbl->SetColorScheme(This,pLogpal);
}
#endif
#endif

#endif


#endif  /* __IOleObject_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOLETypes interface (v0.0)
 */
#ifndef __IOLETypes_INTERFACE_DEFINED__
#define __IOLETypes_INTERFACE_DEFINED__

extern RPC_IF_HANDLE IOLETypes_v0_0_c_ifspec;
extern RPC_IF_HANDLE IOLETypes_v0_0_s_ifspec;
typedef enum tagOLERENDER {
    OLERENDER_NONE = 0,
    OLERENDER_DRAW = 1,
    OLERENDER_FORMAT = 2,
    OLERENDER_ASIS = 3
} OLERENDER;

typedef OLERENDER *LPOLERENDER;

typedef struct tagOBJECTDESCRIPTOR {
    ULONG cbSize;
    CLSID clsid;
    DWORD dwDrawAspect;
    SIZEL sizel;
    POINTL pointl;
    DWORD dwStatus;
    DWORD dwFullUserTypeName;
    DWORD dwSrcOfCopy;
} OBJECTDESCRIPTOR;
typedef struct tagOBJECTDESCRIPTOR *POBJECTDESCRIPTOR;
typedef struct tagOBJECTDESCRIPTOR *LPOBJECTDESCRIPTOR;
typedef struct tagOBJECTDESCRIPTOR LINKSRCDESCRIPTOR;
typedef struct tagOBJECTDESCRIPTOR *PLINKSRCDESCRIPTOR;
typedef struct tagOBJECTDESCRIPTOR *LPLINKSRCDESCRIPTOR;

#endif  /* __IOLETypes_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleWindow interface
 */
#ifndef __IOleWindow_INTERFACE_DEFINED__
#define __IOleWindow_INTERFACE_DEFINED__

typedef IOleWindow *LPOLEWINDOW;

DEFINE_GUID(IID_IOleWindow, 0x00000114, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000114-0000-0000-c000-000000000046")
IOleWindow : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetWindow(
        HWND *phwnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE ContextSensitiveHelp(
        WINBOOL fEnterMode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleWindow, 0x00000114, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleWindowVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleWindow *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleWindow *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleWindow *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleWindow *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleWindow *This,
        WINBOOL fEnterMode);

    END_INTERFACE
} IOleWindowVtbl;

interface IOleWindow {
    CONST_VTBL IOleWindowVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleWindow_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleWindow_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleWindow_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleWindow_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleWindow_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleWindow_QueryInterface(IOleWindow* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleWindow_AddRef(IOleWindow* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleWindow_Release(IOleWindow* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleWindow_GetWindow(IOleWindow* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleWindow_ContextSensitiveHelp(IOleWindow* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
#endif
#endif

#endif


#endif  /* __IOleWindow_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleLink interface
 */
#ifndef __IOleLink_INTERFACE_DEFINED__
#define __IOleLink_INTERFACE_DEFINED__

typedef IOleLink *LPOLELINK;

typedef enum tagOLEUPDATE {
    OLEUPDATE_ALWAYS = 1,
    OLEUPDATE_ONCALL = 3
} OLEUPDATE;

typedef OLEUPDATE *LPOLEUPDATE;
typedef OLEUPDATE *POLEUPDATE;

typedef enum tagOLELINKBIND {
    OLELINKBIND_EVENIFCLASSDIFF = 1
} OLELINKBIND;

DEFINE_GUID(IID_IOleLink, 0x0000011d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000011d-0000-0000-c000-000000000046")
IOleLink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetUpdateOptions(
        DWORD dwUpdateOpt) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUpdateOptions(
        DWORD *pdwUpdateOpt) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSourceMoniker(
        IMoniker *pmk,
        REFCLSID rclsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceMoniker(
        IMoniker **ppmk) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSourceDisplayName(
        LPCOLESTR pszStatusText) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceDisplayName(
        LPOLESTR *ppszDisplayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE BindToSource(
        DWORD bindflags,
        IBindCtx *pbc) = 0;

    virtual HRESULT STDMETHODCALLTYPE BindIfRunning(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoundSource(
        IUnknown **ppunk) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnbindSource(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Update(
        IBindCtx *pbc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleLink, 0x0000011d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleLinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleLink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleLink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleLink *This);

    /*** IOleLink methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUpdateOptions)(
        IOleLink *This,
        DWORD dwUpdateOpt);

    HRESULT (STDMETHODCALLTYPE *GetUpdateOptions)(
        IOleLink *This,
        DWORD *pdwUpdateOpt);

    HRESULT (STDMETHODCALLTYPE *SetSourceMoniker)(
        IOleLink *This,
        IMoniker *pmk,
        REFCLSID rclsid);

    HRESULT (STDMETHODCALLTYPE *GetSourceMoniker)(
        IOleLink *This,
        IMoniker **ppmk);

    HRESULT (STDMETHODCALLTYPE *SetSourceDisplayName)(
        IOleLink *This,
        LPCOLESTR pszStatusText);

    HRESULT (STDMETHODCALLTYPE *GetSourceDisplayName)(
        IOleLink *This,
        LPOLESTR *ppszDisplayName);

    HRESULT (STDMETHODCALLTYPE *BindToSource)(
        IOleLink *This,
        DWORD bindflags,
        IBindCtx *pbc);

    HRESULT (STDMETHODCALLTYPE *BindIfRunning)(
        IOleLink *This);

    HRESULT (STDMETHODCALLTYPE *GetBoundSource)(
        IOleLink *This,
        IUnknown **ppunk);

    HRESULT (STDMETHODCALLTYPE *UnbindSource)(
        IOleLink *This);

    HRESULT (STDMETHODCALLTYPE *Update)(
        IOleLink *This,
        IBindCtx *pbc);

    END_INTERFACE
} IOleLinkVtbl;

interface IOleLink {
    CONST_VTBL IOleLinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleLink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleLink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleLink_Release(This) (This)->lpVtbl->Release(This)
/*** IOleLink methods ***/
#define IOleLink_SetUpdateOptions(This,dwUpdateOpt) (This)->lpVtbl->SetUpdateOptions(This,dwUpdateOpt)
#define IOleLink_GetUpdateOptions(This,pdwUpdateOpt) (This)->lpVtbl->GetUpdateOptions(This,pdwUpdateOpt)
#define IOleLink_SetSourceMoniker(This,pmk,rclsid) (This)->lpVtbl->SetSourceMoniker(This,pmk,rclsid)
#define IOleLink_GetSourceMoniker(This,ppmk) (This)->lpVtbl->GetSourceMoniker(This,ppmk)
#define IOleLink_SetSourceDisplayName(This,pszStatusText) (This)->lpVtbl->SetSourceDisplayName(This,pszStatusText)
#define IOleLink_GetSourceDisplayName(This,ppszDisplayName) (This)->lpVtbl->GetSourceDisplayName(This,ppszDisplayName)
#define IOleLink_BindToSource(This,bindflags,pbc) (This)->lpVtbl->BindToSource(This,bindflags,pbc)
#define IOleLink_BindIfRunning(This) (This)->lpVtbl->BindIfRunning(This)
#define IOleLink_GetBoundSource(This,ppunk) (This)->lpVtbl->GetBoundSource(This,ppunk)
#define IOleLink_UnbindSource(This) (This)->lpVtbl->UnbindSource(This)
#define IOleLink_Update(This,pbc) (This)->lpVtbl->Update(This,pbc)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleLink_QueryInterface(IOleLink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleLink_AddRef(IOleLink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleLink_Release(IOleLink* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleLink methods ***/
static inline HRESULT IOleLink_SetUpdateOptions(IOleLink* This,DWORD dwUpdateOpt) {
    return This->lpVtbl->SetUpdateOptions(This,dwUpdateOpt);
}
static inline HRESULT IOleLink_GetUpdateOptions(IOleLink* This,DWORD *pdwUpdateOpt) {
    return This->lpVtbl->GetUpdateOptions(This,pdwUpdateOpt);
}
static inline HRESULT IOleLink_SetSourceMoniker(IOleLink* This,IMoniker *pmk,REFCLSID rclsid) {
    return This->lpVtbl->SetSourceMoniker(This,pmk,rclsid);
}
static inline HRESULT IOleLink_GetSourceMoniker(IOleLink* This,IMoniker **ppmk) {
    return This->lpVtbl->GetSourceMoniker(This,ppmk);
}
static inline HRESULT IOleLink_SetSourceDisplayName(IOleLink* This,LPCOLESTR pszStatusText) {
    return This->lpVtbl->SetSourceDisplayName(This,pszStatusText);
}
static inline HRESULT IOleLink_GetSourceDisplayName(IOleLink* This,LPOLESTR *ppszDisplayName) {
    return This->lpVtbl->GetSourceDisplayName(This,ppszDisplayName);
}
static inline HRESULT IOleLink_BindToSource(IOleLink* This,DWORD bindflags,IBindCtx *pbc) {
    return This->lpVtbl->BindToSource(This,bindflags,pbc);
}
static inline HRESULT IOleLink_BindIfRunning(IOleLink* This) {
    return This->lpVtbl->BindIfRunning(This);
}
static inline HRESULT IOleLink_GetBoundSource(IOleLink* This,IUnknown **ppunk) {
    return This->lpVtbl->GetBoundSource(This,ppunk);
}
static inline HRESULT IOleLink_UnbindSource(IOleLink* This) {
    return This->lpVtbl->UnbindSource(This);
}
static inline HRESULT IOleLink_Update(IOleLink* This,IBindCtx *pbc) {
    return This->lpVtbl->Update(This,pbc);
}
#endif
#endif

#endif


#endif  /* __IOleLink_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleItemContainer interface
 */
#ifndef __IOleItemContainer_INTERFACE_DEFINED__
#define __IOleItemContainer_INTERFACE_DEFINED__

typedef IOleItemContainer *LPOLEITEMCONTAINER;

typedef enum tagBINDSPEED {
    BINDSPEED_INDEFINITE = 1,
    BINDSPEED_MODERATE = 2,
    BINDSPEED_IMMEDIATE = 3
} BINDSPEED;

typedef enum tagOLECONTF {
    OLECONTF_EMBEDDINGS = 1,
    OLECONTF_LINKS = 2,
    OLECONTF_OTHERS = 4,
    OLECONTF_ONLYUSER = 8,
    OLECONTF_ONLYIFRUNNING = 16
} OLECONTF;

DEFINE_GUID(IID_IOleItemContainer, 0x0000011c, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000011c-0000-0000-c000-000000000046")
IOleItemContainer : public IOleContainer
{
    virtual HRESULT STDMETHODCALLTYPE GetObject(
        LPOLESTR pszItem,
        DWORD dwSpeedNeeded,
        IBindCtx *pbc,
        REFIID riid,
        void **ppvObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectStorage(
        LPOLESTR pszItem,
        IBindCtx *pbc,
        REFIID riid,
        void **ppvStorage) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRunning(
        LPOLESTR pszItem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleItemContainer, 0x0000011c, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleItemContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleItemContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleItemContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleItemContainer *This);

    /*** IParseDisplayName methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseDisplayName)(
        IOleItemContainer *This,
        IBindCtx *pbc,
        LPOLESTR pszDisplayName,
        ULONG *pchEaten,
        IMoniker **ppmkOut);

    /*** IOleContainer methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumObjects)(
        IOleItemContainer *This,
        DWORD grfFlags,
        IEnumUnknown **ppenum);

    HRESULT (STDMETHODCALLTYPE *LockContainer)(
        IOleItemContainer *This,
        WINBOOL fLock);

    /*** IOleItemContainer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetObject)(
        IOleItemContainer *This,
        LPOLESTR pszItem,
        DWORD dwSpeedNeeded,
        IBindCtx *pbc,
        REFIID riid,
        void **ppvObject);

    HRESULT (STDMETHODCALLTYPE *GetObjectStorage)(
        IOleItemContainer *This,
        LPOLESTR pszItem,
        IBindCtx *pbc,
        REFIID riid,
        void **ppvStorage);

    HRESULT (STDMETHODCALLTYPE *IsRunning)(
        IOleItemContainer *This,
        LPOLESTR pszItem);

    END_INTERFACE
} IOleItemContainerVtbl;

interface IOleItemContainer {
    CONST_VTBL IOleItemContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleItemContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleItemContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleItemContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IParseDisplayName methods ***/
#define IOleItemContainer_ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut) (This)->lpVtbl->ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut)
/*** IOleContainer methods ***/
#define IOleItemContainer_EnumObjects(This,grfFlags,ppenum) (This)->lpVtbl->EnumObjects(This,grfFlags,ppenum)
#define IOleItemContainer_LockContainer(This,fLock) (This)->lpVtbl->LockContainer(This,fLock)
/*** IOleItemContainer methods ***/
#define IOleItemContainer_GetObject(This,pszItem,dwSpeedNeeded,pbc,riid,ppvObject) (This)->lpVtbl->GetObject(This,pszItem,dwSpeedNeeded,pbc,riid,ppvObject)
#define IOleItemContainer_GetObjectStorage(This,pszItem,pbc,riid,ppvStorage) (This)->lpVtbl->GetObjectStorage(This,pszItem,pbc,riid,ppvStorage)
#define IOleItemContainer_IsRunning(This,pszItem) (This)->lpVtbl->IsRunning(This,pszItem)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleItemContainer_QueryInterface(IOleItemContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleItemContainer_AddRef(IOleItemContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleItemContainer_Release(IOleItemContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IParseDisplayName methods ***/
static inline HRESULT IOleItemContainer_ParseDisplayName(IOleItemContainer* This,IBindCtx *pbc,LPOLESTR pszDisplayName,ULONG *pchEaten,IMoniker **ppmkOut) {
    return This->lpVtbl->ParseDisplayName(This,pbc,pszDisplayName,pchEaten,ppmkOut);
}
/*** IOleContainer methods ***/
static inline HRESULT IOleItemContainer_EnumObjects(IOleItemContainer* This,DWORD grfFlags,IEnumUnknown **ppenum) {
    return This->lpVtbl->EnumObjects(This,grfFlags,ppenum);
}
static inline HRESULT IOleItemContainer_LockContainer(IOleItemContainer* This,WINBOOL fLock) {
    return This->lpVtbl->LockContainer(This,fLock);
}
/*** IOleItemContainer methods ***/
static inline HRESULT IOleItemContainer_GetObject(IOleItemContainer* This,LPOLESTR pszItem,DWORD dwSpeedNeeded,IBindCtx *pbc,REFIID riid,void **ppvObject) {
    return This->lpVtbl->GetObject(This,pszItem,dwSpeedNeeded,pbc,riid,ppvObject);
}
static inline HRESULT IOleItemContainer_GetObjectStorage(IOleItemContainer* This,LPOLESTR pszItem,IBindCtx *pbc,REFIID riid,void **ppvStorage) {
    return This->lpVtbl->GetObjectStorage(This,pszItem,pbc,riid,ppvStorage);
}
static inline HRESULT IOleItemContainer_IsRunning(IOleItemContainer* This,LPOLESTR pszItem) {
    return This->lpVtbl->IsRunning(This,pszItem);
}
#endif
#endif

#endif


#endif  /* __IOleItemContainer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleInPlaceUIWindow interface
 */
#ifndef __IOleInPlaceUIWindow_INTERFACE_DEFINED__
#define __IOleInPlaceUIWindow_INTERFACE_DEFINED__

typedef IOleInPlaceUIWindow *LPOLEINPLACEUIWINDOW;
typedef RECT BORDERWIDTHS;
typedef LPRECT LPBORDERWIDTHS;
typedef LPCRECT LPCBORDERWIDTHS;

DEFINE_GUID(IID_IOleInPlaceUIWindow, 0x00000115, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000115-0000-0000-c000-000000000046")
IOleInPlaceUIWindow : public IOleWindow
{
    virtual HRESULT STDMETHODCALLTYPE GetBorder(
        LPRECT lprectBorder) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestBorderSpace(
        LPCBORDERWIDTHS pborderwidths) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBorderSpace(
        LPCBORDERWIDTHS pborderwidths) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetActiveObject(
        IOleInPlaceActiveObject *pActiveObject,
        LPCOLESTR pszObjName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleInPlaceUIWindow, 0x00000115, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleInPlaceUIWindowVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleInPlaceUIWindow *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleInPlaceUIWindow *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleInPlaceUIWindow *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleInPlaceUIWindow *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleInPlaceUIWindow *This,
        WINBOOL fEnterMode);

    /*** IOleInPlaceUIWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBorder)(
        IOleInPlaceUIWindow *This,
        LPRECT lprectBorder);

    HRESULT (STDMETHODCALLTYPE *RequestBorderSpace)(
        IOleInPlaceUIWindow *This,
        LPCBORDERWIDTHS pborderwidths);

    HRESULT (STDMETHODCALLTYPE *SetBorderSpace)(
        IOleInPlaceUIWindow *This,
        LPCBORDERWIDTHS pborderwidths);

    HRESULT (STDMETHODCALLTYPE *SetActiveObject)(
        IOleInPlaceUIWindow *This,
        IOleInPlaceActiveObject *pActiveObject,
        LPCOLESTR pszObjName);

    END_INTERFACE
} IOleInPlaceUIWindowVtbl;

interface IOleInPlaceUIWindow {
    CONST_VTBL IOleInPlaceUIWindowVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleInPlaceUIWindow_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleInPlaceUIWindow_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleInPlaceUIWindow_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleInPlaceUIWindow_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleInPlaceUIWindow_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
/*** IOleInPlaceUIWindow methods ***/
#define IOleInPlaceUIWindow_GetBorder(This,lprectBorder) (This)->lpVtbl->GetBorder(This,lprectBorder)
#define IOleInPlaceUIWindow_RequestBorderSpace(This,pborderwidths) (This)->lpVtbl->RequestBorderSpace(This,pborderwidths)
#define IOleInPlaceUIWindow_SetBorderSpace(This,pborderwidths) (This)->lpVtbl->SetBorderSpace(This,pborderwidths)
#define IOleInPlaceUIWindow_SetActiveObject(This,pActiveObject,pszObjName) (This)->lpVtbl->SetActiveObject(This,pActiveObject,pszObjName)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleInPlaceUIWindow_QueryInterface(IOleInPlaceUIWindow* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleInPlaceUIWindow_AddRef(IOleInPlaceUIWindow* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleInPlaceUIWindow_Release(IOleInPlaceUIWindow* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleInPlaceUIWindow_GetWindow(IOleInPlaceUIWindow* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleInPlaceUIWindow_ContextSensitiveHelp(IOleInPlaceUIWindow* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
/*** IOleInPlaceUIWindow methods ***/
static inline HRESULT IOleInPlaceUIWindow_GetBorder(IOleInPlaceUIWindow* This,LPRECT lprectBorder) {
    return This->lpVtbl->GetBorder(This,lprectBorder);
}
static inline HRESULT IOleInPlaceUIWindow_RequestBorderSpace(IOleInPlaceUIWindow* This,LPCBORDERWIDTHS pborderwidths) {
    return This->lpVtbl->RequestBorderSpace(This,pborderwidths);
}
static inline HRESULT IOleInPlaceUIWindow_SetBorderSpace(IOleInPlaceUIWindow* This,LPCBORDERWIDTHS pborderwidths) {
    return This->lpVtbl->SetBorderSpace(This,pborderwidths);
}
static inline HRESULT IOleInPlaceUIWindow_SetActiveObject(IOleInPlaceUIWindow* This,IOleInPlaceActiveObject *pActiveObject,LPCOLESTR pszObjName) {
    return This->lpVtbl->SetActiveObject(This,pActiveObject,pszObjName);
}
#endif
#endif

#endif


#endif  /* __IOleInPlaceUIWindow_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleInPlaceActiveObject interface
 */
#ifndef __IOleInPlaceActiveObject_INTERFACE_DEFINED__
#define __IOleInPlaceActiveObject_INTERFACE_DEFINED__

typedef IOleInPlaceActiveObject *LPOLEINPLACEACTIVEOBJECT;

DEFINE_GUID(IID_IOleInPlaceActiveObject, 0x00000117, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000117-0000-0000-c000-000000000046")
IOleInPlaceActiveObject : public IOleWindow
{
    virtual HRESULT STDMETHODCALLTYPE TranslateAccelerator(
        LPMSG lpmsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnFrameWindowActivate(
        WINBOOL fActivate) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDocWindowActivate(
        WINBOOL fActivate) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResizeBorder(
        LPCRECT prcBorder,
        IOleInPlaceUIWindow *pUIWindow,
        WINBOOL fFrameWindow) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableModeless(
        WINBOOL fEnable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleInPlaceActiveObject, 0x00000117, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleInPlaceActiveObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleInPlaceActiveObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleInPlaceActiveObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleInPlaceActiveObject *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleInPlaceActiveObject *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleInPlaceActiveObject *This,
        WINBOOL fEnterMode);

    /*** IOleInPlaceActiveObject methods ***/
    HRESULT (STDMETHODCALLTYPE *TranslateAccelerator)(
        IOleInPlaceActiveObject *This,
        LPMSG lpmsg);

    HRESULT (STDMETHODCALLTYPE *OnFrameWindowActivate)(
        IOleInPlaceActiveObject *This,
        WINBOOL fActivate);

    HRESULT (STDMETHODCALLTYPE *OnDocWindowActivate)(
        IOleInPlaceActiveObject *This,
        WINBOOL fActivate);

    HRESULT (STDMETHODCALLTYPE *ResizeBorder)(
        IOleInPlaceActiveObject *This,
        LPCRECT prcBorder,
        IOleInPlaceUIWindow *pUIWindow,
        WINBOOL fFrameWindow);

    HRESULT (STDMETHODCALLTYPE *EnableModeless)(
        IOleInPlaceActiveObject *This,
        WINBOOL fEnable);

    END_INTERFACE
} IOleInPlaceActiveObjectVtbl;

interface IOleInPlaceActiveObject {
    CONST_VTBL IOleInPlaceActiveObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleInPlaceActiveObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleInPlaceActiveObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleInPlaceActiveObject_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleInPlaceActiveObject_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleInPlaceActiveObject_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
/*** IOleInPlaceActiveObject methods ***/
#define IOleInPlaceActiveObject_TranslateAccelerator(This,lpmsg) (This)->lpVtbl->TranslateAccelerator(This,lpmsg)
#define IOleInPlaceActiveObject_OnFrameWindowActivate(This,fActivate) (This)->lpVtbl->OnFrameWindowActivate(This,fActivate)
#define IOleInPlaceActiveObject_OnDocWindowActivate(This,fActivate) (This)->lpVtbl->OnDocWindowActivate(This,fActivate)
#define IOleInPlaceActiveObject_ResizeBorder(This,prcBorder,pUIWindow,fFrameWindow) (This)->lpVtbl->ResizeBorder(This,prcBorder,pUIWindow,fFrameWindow)
#define IOleInPlaceActiveObject_EnableModeless(This,fEnable) (This)->lpVtbl->EnableModeless(This,fEnable)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleInPlaceActiveObject_QueryInterface(IOleInPlaceActiveObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleInPlaceActiveObject_AddRef(IOleInPlaceActiveObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleInPlaceActiveObject_Release(IOleInPlaceActiveObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleInPlaceActiveObject_GetWindow(IOleInPlaceActiveObject* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleInPlaceActiveObject_ContextSensitiveHelp(IOleInPlaceActiveObject* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
/*** IOleInPlaceActiveObject methods ***/
static inline HRESULT IOleInPlaceActiveObject_TranslateAccelerator(IOleInPlaceActiveObject* This,LPMSG lpmsg) {
    return This->lpVtbl->TranslateAccelerator(This,lpmsg);
}
static inline HRESULT IOleInPlaceActiveObject_OnFrameWindowActivate(IOleInPlaceActiveObject* This,WINBOOL fActivate) {
    return This->lpVtbl->OnFrameWindowActivate(This,fActivate);
}
static inline HRESULT IOleInPlaceActiveObject_OnDocWindowActivate(IOleInPlaceActiveObject* This,WINBOOL fActivate) {
    return This->lpVtbl->OnDocWindowActivate(This,fActivate);
}
static inline HRESULT IOleInPlaceActiveObject_ResizeBorder(IOleInPlaceActiveObject* This,LPCRECT prcBorder,IOleInPlaceUIWindow *pUIWindow,WINBOOL fFrameWindow) {
    return This->lpVtbl->ResizeBorder(This,prcBorder,pUIWindow,fFrameWindow);
}
static inline HRESULT IOleInPlaceActiveObject_EnableModeless(IOleInPlaceActiveObject* This,WINBOOL fEnable) {
    return This->lpVtbl->EnableModeless(This,fEnable);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IOleInPlaceActiveObject_RemoteTranslateAccelerator_Proxy(
    IOleInPlaceActiveObject* This);
void __RPC_STUB IOleInPlaceActiveObject_RemoteTranslateAccelerator_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IOleInPlaceActiveObject_RemoteResizeBorder_Proxy(
    IOleInPlaceActiveObject* This,
    LPCRECT prcBorder,
    REFIID riid,
    IOleInPlaceUIWindow *pUIWindow,
    WINBOOL fFrameWindow);
void __RPC_STUB IOleInPlaceActiveObject_RemoteResizeBorder_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IOleInPlaceActiveObject_TranslateAccelerator_Proxy(
    IOleInPlaceActiveObject* This,
    LPMSG lpmsg);
HRESULT __RPC_STUB IOleInPlaceActiveObject_TranslateAccelerator_Stub(
    IOleInPlaceActiveObject* This);
HRESULT CALLBACK IOleInPlaceActiveObject_ResizeBorder_Proxy(
    IOleInPlaceActiveObject* This,
    LPCRECT prcBorder,
    IOleInPlaceUIWindow *pUIWindow,
    WINBOOL fFrameWindow);
HRESULT __RPC_STUB IOleInPlaceActiveObject_ResizeBorder_Stub(
    IOleInPlaceActiveObject* This,
    LPCRECT prcBorder,
    REFIID riid,
    IOleInPlaceUIWindow *pUIWindow,
    WINBOOL fFrameWindow);

#endif  /* __IOleInPlaceActiveObject_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleInPlaceFrame interface
 */
#ifndef __IOleInPlaceFrame_INTERFACE_DEFINED__
#define __IOleInPlaceFrame_INTERFACE_DEFINED__

typedef IOleInPlaceFrame *LPOLEINPLACEFRAME;

typedef struct tagOIFI {
    UINT cb;
    WINBOOL fMDIApp;
    HWND hwndFrame;
    HACCEL haccel;
    UINT cAccelEntries;
} OLEINPLACEFRAMEINFO;
typedef struct tagOIFI *LPOLEINPLACEFRAMEINFO;

typedef struct tagOleMenuGroupWidths {
    LONG width[6];
} OLEMENUGROUPWIDTHS;
typedef struct tagOleMenuGroupWidths *LPOLEMENUGROUPWIDTHS;

typedef HGLOBAL HOLEMENU;

DEFINE_GUID(IID_IOleInPlaceFrame, 0x00000116, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000116-0000-0000-c000-000000000046")
IOleInPlaceFrame : public IOleInPlaceUIWindow
{
    virtual HRESULT STDMETHODCALLTYPE InsertMenus(
        HMENU hmenuShared,
        LPOLEMENUGROUPWIDTHS lpMenuWidths) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMenu(
        HMENU hmenuShared,
        HOLEMENU holemenu,
        HWND hwndActiveObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveMenus(
        HMENU hmenuShared) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStatusText(
        LPCOLESTR pszStatusText) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableModeless(
        WINBOOL fEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE TranslateAccelerator(
        LPMSG lpmsg,
        WORD wID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleInPlaceFrame, 0x00000116, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleInPlaceFrameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleInPlaceFrame *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleInPlaceFrame *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleInPlaceFrame *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleInPlaceFrame *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleInPlaceFrame *This,
        WINBOOL fEnterMode);

    /*** IOleInPlaceUIWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBorder)(
        IOleInPlaceFrame *This,
        LPRECT lprectBorder);

    HRESULT (STDMETHODCALLTYPE *RequestBorderSpace)(
        IOleInPlaceFrame *This,
        LPCBORDERWIDTHS pborderwidths);

    HRESULT (STDMETHODCALLTYPE *SetBorderSpace)(
        IOleInPlaceFrame *This,
        LPCBORDERWIDTHS pborderwidths);

    HRESULT (STDMETHODCALLTYPE *SetActiveObject)(
        IOleInPlaceFrame *This,
        IOleInPlaceActiveObject *pActiveObject,
        LPCOLESTR pszObjName);

    /*** IOleInPlaceFrame methods ***/
    HRESULT (STDMETHODCALLTYPE *InsertMenus)(
        IOleInPlaceFrame *This,
        HMENU hmenuShared,
        LPOLEMENUGROUPWIDTHS lpMenuWidths);

    HRESULT (STDMETHODCALLTYPE *SetMenu)(
        IOleInPlaceFrame *This,
        HMENU hmenuShared,
        HOLEMENU holemenu,
        HWND hwndActiveObject);

    HRESULT (STDMETHODCALLTYPE *RemoveMenus)(
        IOleInPlaceFrame *This,
        HMENU hmenuShared);

    HRESULT (STDMETHODCALLTYPE *SetStatusText)(
        IOleInPlaceFrame *This,
        LPCOLESTR pszStatusText);

    HRESULT (STDMETHODCALLTYPE *EnableModeless)(
        IOleInPlaceFrame *This,
        WINBOOL fEnable);

    HRESULT (STDMETHODCALLTYPE *TranslateAccelerator)(
        IOleInPlaceFrame *This,
        LPMSG lpmsg,
        WORD wID);

    END_INTERFACE
} IOleInPlaceFrameVtbl;

interface IOleInPlaceFrame {
    CONST_VTBL IOleInPlaceFrameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleInPlaceFrame_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleInPlaceFrame_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleInPlaceFrame_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleInPlaceFrame_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleInPlaceFrame_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
/*** IOleInPlaceUIWindow methods ***/
#define IOleInPlaceFrame_GetBorder(This,lprectBorder) (This)->lpVtbl->GetBorder(This,lprectBorder)
#define IOleInPlaceFrame_RequestBorderSpace(This,pborderwidths) (This)->lpVtbl->RequestBorderSpace(This,pborderwidths)
#define IOleInPlaceFrame_SetBorderSpace(This,pborderwidths) (This)->lpVtbl->SetBorderSpace(This,pborderwidths)
#define IOleInPlaceFrame_SetActiveObject(This,pActiveObject,pszObjName) (This)->lpVtbl->SetActiveObject(This,pActiveObject,pszObjName)
/*** IOleInPlaceFrame methods ***/
#define IOleInPlaceFrame_InsertMenus(This,hmenuShared,lpMenuWidths) (This)->lpVtbl->InsertMenus(This,hmenuShared,lpMenuWidths)
#define IOleInPlaceFrame_SetMenu(This,hmenuShared,holemenu,hwndActiveObject) (This)->lpVtbl->SetMenu(This,hmenuShared,holemenu,hwndActiveObject)
#define IOleInPlaceFrame_RemoveMenus(This,hmenuShared) (This)->lpVtbl->RemoveMenus(This,hmenuShared)
#define IOleInPlaceFrame_SetStatusText(This,pszStatusText) (This)->lpVtbl->SetStatusText(This,pszStatusText)
#define IOleInPlaceFrame_EnableModeless(This,fEnable) (This)->lpVtbl->EnableModeless(This,fEnable)
#define IOleInPlaceFrame_TranslateAccelerator(This,lpmsg,wID) (This)->lpVtbl->TranslateAccelerator(This,lpmsg,wID)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleInPlaceFrame_QueryInterface(IOleInPlaceFrame* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleInPlaceFrame_AddRef(IOleInPlaceFrame* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleInPlaceFrame_Release(IOleInPlaceFrame* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleInPlaceFrame_GetWindow(IOleInPlaceFrame* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleInPlaceFrame_ContextSensitiveHelp(IOleInPlaceFrame* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
/*** IOleInPlaceUIWindow methods ***/
static inline HRESULT IOleInPlaceFrame_GetBorder(IOleInPlaceFrame* This,LPRECT lprectBorder) {
    return This->lpVtbl->GetBorder(This,lprectBorder);
}
static inline HRESULT IOleInPlaceFrame_RequestBorderSpace(IOleInPlaceFrame* This,LPCBORDERWIDTHS pborderwidths) {
    return This->lpVtbl->RequestBorderSpace(This,pborderwidths);
}
static inline HRESULT IOleInPlaceFrame_SetBorderSpace(IOleInPlaceFrame* This,LPCBORDERWIDTHS pborderwidths) {
    return This->lpVtbl->SetBorderSpace(This,pborderwidths);
}
static inline HRESULT IOleInPlaceFrame_SetActiveObject(IOleInPlaceFrame* This,IOleInPlaceActiveObject *pActiveObject,LPCOLESTR pszObjName) {
    return This->lpVtbl->SetActiveObject(This,pActiveObject,pszObjName);
}
/*** IOleInPlaceFrame methods ***/
static inline HRESULT IOleInPlaceFrame_InsertMenus(IOleInPlaceFrame* This,HMENU hmenuShared,LPOLEMENUGROUPWIDTHS lpMenuWidths) {
    return This->lpVtbl->InsertMenus(This,hmenuShared,lpMenuWidths);
}
static inline HRESULT IOleInPlaceFrame_SetMenu(IOleInPlaceFrame* This,HMENU hmenuShared,HOLEMENU holemenu,HWND hwndActiveObject) {
    return This->lpVtbl->SetMenu(This,hmenuShared,holemenu,hwndActiveObject);
}
static inline HRESULT IOleInPlaceFrame_RemoveMenus(IOleInPlaceFrame* This,HMENU hmenuShared) {
    return This->lpVtbl->RemoveMenus(This,hmenuShared);
}
static inline HRESULT IOleInPlaceFrame_SetStatusText(IOleInPlaceFrame* This,LPCOLESTR pszStatusText) {
    return This->lpVtbl->SetStatusText(This,pszStatusText);
}
static inline HRESULT IOleInPlaceFrame_EnableModeless(IOleInPlaceFrame* This,WINBOOL fEnable) {
    return This->lpVtbl->EnableModeless(This,fEnable);
}
static inline HRESULT IOleInPlaceFrame_TranslateAccelerator(IOleInPlaceFrame* This,LPMSG lpmsg,WORD wID) {
    return This->lpVtbl->TranslateAccelerator(This,lpmsg,wID);
}
#endif
#endif

#endif


#endif  /* __IOleInPlaceFrame_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleInPlaceObject interface
 */
#ifndef __IOleInPlaceObject_INTERFACE_DEFINED__
#define __IOleInPlaceObject_INTERFACE_DEFINED__

typedef IOleInPlaceObject *LPOLEINPLACEOBJECT;

DEFINE_GUID(IID_IOleInPlaceObject, 0x00000113, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000113-0000-0000-c000-000000000046")
IOleInPlaceObject : public IOleWindow
{
    virtual HRESULT STDMETHODCALLTYPE InPlaceDeactivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE UIDeactivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetObjectRects(
        LPCRECT lprcPosRect,
        LPCRECT lprcClipRect) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReactivateAndUndo(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleInPlaceObject, 0x00000113, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleInPlaceObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleInPlaceObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleInPlaceObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleInPlaceObject *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleInPlaceObject *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleInPlaceObject *This,
        WINBOOL fEnterMode);

    /*** IOleInPlaceObject methods ***/
    HRESULT (STDMETHODCALLTYPE *InPlaceDeactivate)(
        IOleInPlaceObject *This);

    HRESULT (STDMETHODCALLTYPE *UIDeactivate)(
        IOleInPlaceObject *This);

    HRESULT (STDMETHODCALLTYPE *SetObjectRects)(
        IOleInPlaceObject *This,
        LPCRECT lprcPosRect,
        LPCRECT lprcClipRect);

    HRESULT (STDMETHODCALLTYPE *ReactivateAndUndo)(
        IOleInPlaceObject *This);

    END_INTERFACE
} IOleInPlaceObjectVtbl;

interface IOleInPlaceObject {
    CONST_VTBL IOleInPlaceObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleInPlaceObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleInPlaceObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleInPlaceObject_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleInPlaceObject_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleInPlaceObject_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
/*** IOleInPlaceObject methods ***/
#define IOleInPlaceObject_InPlaceDeactivate(This) (This)->lpVtbl->InPlaceDeactivate(This)
#define IOleInPlaceObject_UIDeactivate(This) (This)->lpVtbl->UIDeactivate(This)
#define IOleInPlaceObject_SetObjectRects(This,lprcPosRect,lprcClipRect) (This)->lpVtbl->SetObjectRects(This,lprcPosRect,lprcClipRect)
#define IOleInPlaceObject_ReactivateAndUndo(This) (This)->lpVtbl->ReactivateAndUndo(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleInPlaceObject_QueryInterface(IOleInPlaceObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleInPlaceObject_AddRef(IOleInPlaceObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleInPlaceObject_Release(IOleInPlaceObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleInPlaceObject_GetWindow(IOleInPlaceObject* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleInPlaceObject_ContextSensitiveHelp(IOleInPlaceObject* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
/*** IOleInPlaceObject methods ***/
static inline HRESULT IOleInPlaceObject_InPlaceDeactivate(IOleInPlaceObject* This) {
    return This->lpVtbl->InPlaceDeactivate(This);
}
static inline HRESULT IOleInPlaceObject_UIDeactivate(IOleInPlaceObject* This) {
    return This->lpVtbl->UIDeactivate(This);
}
static inline HRESULT IOleInPlaceObject_SetObjectRects(IOleInPlaceObject* This,LPCRECT lprcPosRect,LPCRECT lprcClipRect) {
    return This->lpVtbl->SetObjectRects(This,lprcPosRect,lprcClipRect);
}
static inline HRESULT IOleInPlaceObject_ReactivateAndUndo(IOleInPlaceObject* This) {
    return This->lpVtbl->ReactivateAndUndo(This);
}
#endif
#endif

#endif


#endif  /* __IOleInPlaceObject_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleInPlaceSite interface
 */
#ifndef __IOleInPlaceSite_INTERFACE_DEFINED__
#define __IOleInPlaceSite_INTERFACE_DEFINED__

typedef IOleInPlaceSite *LPOLEINPLACESITE;

DEFINE_GUID(IID_IOleInPlaceSite, 0x00000119, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000119-0000-0000-c000-000000000046")
IOleInPlaceSite : public IOleWindow
{
    virtual HRESULT STDMETHODCALLTYPE CanInPlaceActivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnInPlaceActivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnUIActivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWindowContext(
        IOleInPlaceFrame **ppFrame,
        IOleInPlaceUIWindow **ppDoc,
        LPRECT lprcPosRect,
        LPRECT lprcClipRect,
        LPOLEINPLACEFRAMEINFO lpFrameInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE Scroll(
        SIZE scrollExtant) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnUIDeactivate(
        WINBOOL fUndoable) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnInPlaceDeactivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DiscardUndoState(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeactivateAndUndo(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnPosRectChange(
        LPCRECT lprcPosRect) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleInPlaceSite, 0x00000119, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IOleInPlaceSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleInPlaceSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleInPlaceSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleInPlaceSite *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleInPlaceSite *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleInPlaceSite *This,
        WINBOOL fEnterMode);

    /*** IOleInPlaceSite methods ***/
    HRESULT (STDMETHODCALLTYPE *CanInPlaceActivate)(
        IOleInPlaceSite *This);

    HRESULT (STDMETHODCALLTYPE *OnInPlaceActivate)(
        IOleInPlaceSite *This);

    HRESULT (STDMETHODCALLTYPE *OnUIActivate)(
        IOleInPlaceSite *This);

    HRESULT (STDMETHODCALLTYPE *GetWindowContext)(
        IOleInPlaceSite *This,
        IOleInPlaceFrame **ppFrame,
        IOleInPlaceUIWindow **ppDoc,
        LPRECT lprcPosRect,
        LPRECT lprcClipRect,
        LPOLEINPLACEFRAMEINFO lpFrameInfo);

    HRESULT (STDMETHODCALLTYPE *Scroll)(
        IOleInPlaceSite *This,
        SIZE scrollExtant);

    HRESULT (STDMETHODCALLTYPE *OnUIDeactivate)(
        IOleInPlaceSite *This,
        WINBOOL fUndoable);

    HRESULT (STDMETHODCALLTYPE *OnInPlaceDeactivate)(
        IOleInPlaceSite *This);

    HRESULT (STDMETHODCALLTYPE *DiscardUndoState)(
        IOleInPlaceSite *This);

    HRESULT (STDMETHODCALLTYPE *DeactivateAndUndo)(
        IOleInPlaceSite *This);

    HRESULT (STDMETHODCALLTYPE *OnPosRectChange)(
        IOleInPlaceSite *This,
        LPCRECT lprcPosRect);

    END_INTERFACE
} IOleInPlaceSiteVtbl;

interface IOleInPlaceSite {
    CONST_VTBL IOleInPlaceSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleInPlaceSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleInPlaceSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleInPlaceSite_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleInPlaceSite_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleInPlaceSite_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
/*** IOleInPlaceSite methods ***/
#define IOleInPlaceSite_CanInPlaceActivate(This) (This)->lpVtbl->CanInPlaceActivate(This)
#define IOleInPlaceSite_OnInPlaceActivate(This) (This)->lpVtbl->OnInPlaceActivate(This)
#define IOleInPlaceSite_OnUIActivate(This) (This)->lpVtbl->OnUIActivate(This)
#define IOleInPlaceSite_GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo) (This)->lpVtbl->GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo)
#define IOleInPlaceSite_Scroll(This,scrollExtant) (This)->lpVtbl->Scroll(This,scrollExtant)
#define IOleInPlaceSite_OnUIDeactivate(This,fUndoable) (This)->lpVtbl->OnUIDeactivate(This,fUndoable)
#define IOleInPlaceSite_OnInPlaceDeactivate(This) (This)->lpVtbl->OnInPlaceDeactivate(This)
#define IOleInPlaceSite_DiscardUndoState(This) (This)->lpVtbl->DiscardUndoState(This)
#define IOleInPlaceSite_DeactivateAndUndo(This) (This)->lpVtbl->DeactivateAndUndo(This)
#define IOleInPlaceSite_OnPosRectChange(This,lprcPosRect) (This)->lpVtbl->OnPosRectChange(This,lprcPosRect)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleInPlaceSite_QueryInterface(IOleInPlaceSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleInPlaceSite_AddRef(IOleInPlaceSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleInPlaceSite_Release(IOleInPlaceSite* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleInPlaceSite_GetWindow(IOleInPlaceSite* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleInPlaceSite_ContextSensitiveHelp(IOleInPlaceSite* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
/*** IOleInPlaceSite methods ***/
static inline HRESULT IOleInPlaceSite_CanInPlaceActivate(IOleInPlaceSite* This) {
    return This->lpVtbl->CanInPlaceActivate(This);
}
static inline HRESULT IOleInPlaceSite_OnInPlaceActivate(IOleInPlaceSite* This) {
    return This->lpVtbl->OnInPlaceActivate(This);
}
static inline HRESULT IOleInPlaceSite_OnUIActivate(IOleInPlaceSite* This) {
    return This->lpVtbl->OnUIActivate(This);
}
static inline HRESULT IOleInPlaceSite_GetWindowContext(IOleInPlaceSite* This,IOleInPlaceFrame **ppFrame,IOleInPlaceUIWindow **ppDoc,LPRECT lprcPosRect,LPRECT lprcClipRect,LPOLEINPLACEFRAMEINFO lpFrameInfo) {
    return This->lpVtbl->GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo);
}
static inline HRESULT IOleInPlaceSite_Scroll(IOleInPlaceSite* This,SIZE scrollExtant) {
    return This->lpVtbl->Scroll(This,scrollExtant);
}
static inline HRESULT IOleInPlaceSite_OnUIDeactivate(IOleInPlaceSite* This,WINBOOL fUndoable) {
    return This->lpVtbl->OnUIDeactivate(This,fUndoable);
}
static inline HRESULT IOleInPlaceSite_OnInPlaceDeactivate(IOleInPlaceSite* This) {
    return This->lpVtbl->OnInPlaceDeactivate(This);
}
static inline HRESULT IOleInPlaceSite_DiscardUndoState(IOleInPlaceSite* This) {
    return This->lpVtbl->DiscardUndoState(This);
}
static inline HRESULT IOleInPlaceSite_DeactivateAndUndo(IOleInPlaceSite* This) {
    return This->lpVtbl->DeactivateAndUndo(This);
}
static inline HRESULT IOleInPlaceSite_OnPosRectChange(IOleInPlaceSite* This,LPCRECT lprcPosRect) {
    return This->lpVtbl->OnPosRectChange(This,lprcPosRect);
}
#endif
#endif

#endif


#endif  /* __IOleInPlaceSite_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IContinue interface
 */
#ifndef __IContinue_INTERFACE_DEFINED__
#define __IContinue_INTERFACE_DEFINED__

DEFINE_GUID(IID_IContinue, 0x0000012a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000012a-0000-0000-c000-000000000046")
IContinue : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE FContinue(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IContinue, 0x0000012a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IContinueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IContinue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IContinue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IContinue *This);

    /*** IContinue methods ***/
    HRESULT (STDMETHODCALLTYPE *FContinue)(
        IContinue *This);

    END_INTERFACE
} IContinueVtbl;

interface IContinue {
    CONST_VTBL IContinueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IContinue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IContinue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IContinue_Release(This) (This)->lpVtbl->Release(This)
/*** IContinue methods ***/
#define IContinue_FContinue(This) (This)->lpVtbl->FContinue(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IContinue_QueryInterface(IContinue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IContinue_AddRef(IContinue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IContinue_Release(IContinue* This) {
    return This->lpVtbl->Release(This);
}
/*** IContinue methods ***/
static inline HRESULT IContinue_FContinue(IContinue* This) {
    return This->lpVtbl->FContinue(This);
}
#endif
#endif

#endif


#endif  /* __IContinue_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IViewObject interface
 */
#ifndef __IViewObject_INTERFACE_DEFINED__
#define __IViewObject_INTERFACE_DEFINED__

typedef IViewObject *LPVIEWOBJECT;

DEFINE_GUID(IID_IViewObject, 0x0000010d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000010d-0000-0000-c000-000000000046")
IViewObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Draw(
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DVTARGETDEVICE *ptd,
        HDC hdcTargetDev,
        HDC hdcDraw,
        LPCRECTL lprcBounds,
        LPCRECTL lprcWBounds,
        WINBOOL (STDMETHODCALLTYPE *pfnContinue)(ULONG_PTR dwContinue),
        ULONG_PTR dwContinue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetColorSet(
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DVTARGETDEVICE *ptd,
        HDC hicTargetDev,
        LOGPALETTE **ppColorSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE Freeze(
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DWORD *pdwFreeze) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unfreeze(
        DWORD dwFreeze) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAdvise(
        DWORD aspects,
        DWORD advf,
        IAdviseSink *pAdvSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAdvise(
        DWORD *pAspects,
        DWORD *pAdvf,
        IAdviseSink **ppAdvSink) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IViewObject, 0x0000010d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IViewObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IViewObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IViewObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IViewObject *This);

    /*** IViewObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Draw)(
        IViewObject *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DVTARGETDEVICE *ptd,
        HDC hdcTargetDev,
        HDC hdcDraw,
        LPCRECTL lprcBounds,
        LPCRECTL lprcWBounds,
        WINBOOL (STDMETHODCALLTYPE *pfnContinue)(ULONG_PTR dwContinue),
        ULONG_PTR dwContinue);

    HRESULT (STDMETHODCALLTYPE *GetColorSet)(
        IViewObject *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DVTARGETDEVICE *ptd,
        HDC hicTargetDev,
        LOGPALETTE **ppColorSet);

    HRESULT (STDMETHODCALLTYPE *Freeze)(
        IViewObject *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DWORD *pdwFreeze);

    HRESULT (STDMETHODCALLTYPE *Unfreeze)(
        IViewObject *This,
        DWORD dwFreeze);

    HRESULT (STDMETHODCALLTYPE *SetAdvise)(
        IViewObject *This,
        DWORD aspects,
        DWORD advf,
        IAdviseSink *pAdvSink);

    HRESULT (STDMETHODCALLTYPE *GetAdvise)(
        IViewObject *This,
        DWORD *pAspects,
        DWORD *pAdvf,
        IAdviseSink **ppAdvSink);

    END_INTERFACE
} IViewObjectVtbl;

interface IViewObject {
    CONST_VTBL IViewObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IViewObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IViewObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IViewObject_Release(This) (This)->lpVtbl->Release(This)
/*** IViewObject methods ***/
#define IViewObject_Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue) (This)->lpVtbl->Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue)
#define IViewObject_GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet) (This)->lpVtbl->GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet)
#define IViewObject_Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze) (This)->lpVtbl->Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze)
#define IViewObject_Unfreeze(This,dwFreeze) (This)->lpVtbl->Unfreeze(This,dwFreeze)
#define IViewObject_SetAdvise(This,aspects,advf,pAdvSink) (This)->lpVtbl->SetAdvise(This,aspects,advf,pAdvSink)
#define IViewObject_GetAdvise(This,pAspects,pAdvf,ppAdvSink) (This)->lpVtbl->GetAdvise(This,pAspects,pAdvf,ppAdvSink)
#else
/*** IUnknown methods ***/
static inline HRESULT IViewObject_QueryInterface(IViewObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IViewObject_AddRef(IViewObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IViewObject_Release(IViewObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IViewObject methods ***/
static inline HRESULT IViewObject_Draw(IViewObject* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DVTARGETDEVICE *ptd,HDC hdcTargetDev,HDC hdcDraw,LPCRECTL lprcBounds,LPCRECTL lprcWBounds,WINBOOL (STDMETHODCALLTYPE *pfnContinue)(ULONG_PTR dwContinue),ULONG_PTR dwContinue) {
    return This->lpVtbl->Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue);
}
static inline HRESULT IViewObject_GetColorSet(IViewObject* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DVTARGETDEVICE *ptd,HDC hicTargetDev,LOGPALETTE **ppColorSet) {
    return This->lpVtbl->GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet);
}
static inline HRESULT IViewObject_Freeze(IViewObject* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DWORD *pdwFreeze) {
    return This->lpVtbl->Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze);
}
static inline HRESULT IViewObject_Unfreeze(IViewObject* This,DWORD dwFreeze) {
    return This->lpVtbl->Unfreeze(This,dwFreeze);
}
static inline HRESULT IViewObject_SetAdvise(IViewObject* This,DWORD aspects,DWORD advf,IAdviseSink *pAdvSink) {
    return This->lpVtbl->SetAdvise(This,aspects,advf,pAdvSink);
}
static inline HRESULT IViewObject_GetAdvise(IViewObject* This,DWORD *pAspects,DWORD *pAdvf,IAdviseSink **ppAdvSink) {
    return This->lpVtbl->GetAdvise(This,pAspects,pAdvf,ppAdvSink);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IViewObject_RemoteDraw_Proxy(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    ULONG_PTR pvAspect,
    DVTARGETDEVICE *ptd,
    HDC hdcTargetDev,
    HDC hdcDraw,
    LPCRECTL lprcBounds,
    LPCRECTL lprcWBounds,
    IContinue *pContinue);
void __RPC_STUB IViewObject_RemoteDraw_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IViewObject_RemoteGetColorSet_Proxy(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    ULONG_PTR pvAspect,
    DVTARGETDEVICE *ptd,
    ULONG_PTR hicTargetDev,
    LOGPALETTE **ppColorSet);
void __RPC_STUB IViewObject_RemoteGetColorSet_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IViewObject_RemoteFreeze_Proxy(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    ULONG_PTR pvAspect,
    DWORD *pdwFreeze);
void __RPC_STUB IViewObject_RemoteFreeze_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IViewObject_RemoteGetAdvise_Proxy(
    IViewObject* This,
    DWORD *pAspects,
    DWORD *pAdvf,
    IAdviseSink **ppAdvSink);
void __RPC_STUB IViewObject_RemoteGetAdvise_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IViewObject_Draw_Proxy(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    void *pvAspect,
    DVTARGETDEVICE *ptd,
    HDC hdcTargetDev,
    HDC hdcDraw,
    LPCRECTL lprcBounds,
    LPCRECTL lprcWBounds,
    WINBOOL (STDMETHODCALLTYPE *pfnContinue)(ULONG_PTR dwContinue),
    ULONG_PTR dwContinue);
HRESULT __RPC_STUB IViewObject_Draw_Stub(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    ULONG_PTR pvAspect,
    DVTARGETDEVICE *ptd,
    HDC hdcTargetDev,
    HDC hdcDraw,
    LPCRECTL lprcBounds,
    LPCRECTL lprcWBounds,
    IContinue *pContinue);
HRESULT CALLBACK IViewObject_GetColorSet_Proxy(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    void *pvAspect,
    DVTARGETDEVICE *ptd,
    HDC hicTargetDev,
    LOGPALETTE **ppColorSet);
HRESULT __RPC_STUB IViewObject_GetColorSet_Stub(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    ULONG_PTR pvAspect,
    DVTARGETDEVICE *ptd,
    ULONG_PTR hicTargetDev,
    LOGPALETTE **ppColorSet);
HRESULT CALLBACK IViewObject_Freeze_Proxy(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    void *pvAspect,
    DWORD *pdwFreeze);
HRESULT __RPC_STUB IViewObject_Freeze_Stub(
    IViewObject* This,
    DWORD dwDrawAspect,
    LONG lindex,
    ULONG_PTR pvAspect,
    DWORD *pdwFreeze);
HRESULT CALLBACK IViewObject_GetAdvise_Proxy(
    IViewObject* This,
    DWORD *pAspects,
    DWORD *pAdvf,
    IAdviseSink **ppAdvSink);
HRESULT __RPC_STUB IViewObject_GetAdvise_Stub(
    IViewObject* This,
    DWORD *pAspects,
    DWORD *pAdvf,
    IAdviseSink **ppAdvSink);

#endif  /* __IViewObject_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IViewObject2 interface
 */
#ifndef __IViewObject2_INTERFACE_DEFINED__
#define __IViewObject2_INTERFACE_DEFINED__

typedef IViewObject2 *LPVIEWOBJECT2;

DEFINE_GUID(IID_IViewObject2, 0x00000127, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000127-0000-0000-c000-000000000046")
IViewObject2 : public IViewObject
{
    virtual HRESULT STDMETHODCALLTYPE GetExtent(
        DWORD dwDrawAspect,
        LONG lindex,
        DVTARGETDEVICE *ptd,
        LPSIZEL lpsizel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IViewObject2, 0x00000127, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IViewObject2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IViewObject2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IViewObject2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IViewObject2 *This);

    /*** IViewObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Draw)(
        IViewObject2 *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DVTARGETDEVICE *ptd,
        HDC hdcTargetDev,
        HDC hdcDraw,
        LPCRECTL lprcBounds,
        LPCRECTL lprcWBounds,
        WINBOOL (STDMETHODCALLTYPE *pfnContinue)(ULONG_PTR dwContinue),
        ULONG_PTR dwContinue);

    HRESULT (STDMETHODCALLTYPE *GetColorSet)(
        IViewObject2 *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DVTARGETDEVICE *ptd,
        HDC hicTargetDev,
        LOGPALETTE **ppColorSet);

    HRESULT (STDMETHODCALLTYPE *Freeze)(
        IViewObject2 *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DWORD *pdwFreeze);

    HRESULT (STDMETHODCALLTYPE *Unfreeze)(
        IViewObject2 *This,
        DWORD dwFreeze);

    HRESULT (STDMETHODCALLTYPE *SetAdvise)(
        IViewObject2 *This,
        DWORD aspects,
        DWORD advf,
        IAdviseSink *pAdvSink);

    HRESULT (STDMETHODCALLTYPE *GetAdvise)(
        IViewObject2 *This,
        DWORD *pAspects,
        DWORD *pAdvf,
        IAdviseSink **ppAdvSink);

    /*** IViewObject2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExtent)(
        IViewObject2 *This,
        DWORD dwDrawAspect,
        LONG lindex,
        DVTARGETDEVICE *ptd,
        LPSIZEL lpsizel);

    END_INTERFACE
} IViewObject2Vtbl;

interface IViewObject2 {
    CONST_VTBL IViewObject2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IViewObject2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IViewObject2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IViewObject2_Release(This) (This)->lpVtbl->Release(This)
/*** IViewObject methods ***/
#define IViewObject2_Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue) (This)->lpVtbl->Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue)
#define IViewObject2_GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet) (This)->lpVtbl->GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet)
#define IViewObject2_Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze) (This)->lpVtbl->Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze)
#define IViewObject2_Unfreeze(This,dwFreeze) (This)->lpVtbl->Unfreeze(This,dwFreeze)
#define IViewObject2_SetAdvise(This,aspects,advf,pAdvSink) (This)->lpVtbl->SetAdvise(This,aspects,advf,pAdvSink)
#define IViewObject2_GetAdvise(This,pAspects,pAdvf,ppAdvSink) (This)->lpVtbl->GetAdvise(This,pAspects,pAdvf,ppAdvSink)
/*** IViewObject2 methods ***/
#define IViewObject2_GetExtent(This,dwDrawAspect,lindex,ptd,lpsizel) (This)->lpVtbl->GetExtent(This,dwDrawAspect,lindex,ptd,lpsizel)
#else
/*** IUnknown methods ***/
static inline HRESULT IViewObject2_QueryInterface(IViewObject2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IViewObject2_AddRef(IViewObject2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IViewObject2_Release(IViewObject2* This) {
    return This->lpVtbl->Release(This);
}
/*** IViewObject methods ***/
static inline HRESULT IViewObject2_Draw(IViewObject2* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DVTARGETDEVICE *ptd,HDC hdcTargetDev,HDC hdcDraw,LPCRECTL lprcBounds,LPCRECTL lprcWBounds,WINBOOL (STDMETHODCALLTYPE *pfnContinue)(ULONG_PTR dwContinue),ULONG_PTR dwContinue) {
    return This->lpVtbl->Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue);
}
static inline HRESULT IViewObject2_GetColorSet(IViewObject2* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DVTARGETDEVICE *ptd,HDC hicTargetDev,LOGPALETTE **ppColorSet) {
    return This->lpVtbl->GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet);
}
static inline HRESULT IViewObject2_Freeze(IViewObject2* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DWORD *pdwFreeze) {
    return This->lpVtbl->Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze);
}
static inline HRESULT IViewObject2_Unfreeze(IViewObject2* This,DWORD dwFreeze) {
    return This->lpVtbl->Unfreeze(This,dwFreeze);
}
static inline HRESULT IViewObject2_SetAdvise(IViewObject2* This,DWORD aspects,DWORD advf,IAdviseSink *pAdvSink) {
    return This->lpVtbl->SetAdvise(This,aspects,advf,pAdvSink);
}
static inline HRESULT IViewObject2_GetAdvise(IViewObject2* This,DWORD *pAspects,DWORD *pAdvf,IAdviseSink **ppAdvSink) {
    return This->lpVtbl->GetAdvise(This,pAspects,pAdvf,ppAdvSink);
}
/*** IViewObject2 methods ***/
static inline HRESULT IViewObject2_GetExtent(IViewObject2* This,DWORD dwDrawAspect,LONG lindex,DVTARGETDEVICE *ptd,LPSIZEL lpsizel) {
    return This->lpVtbl->GetExtent(This,dwDrawAspect,lindex,ptd,lpsizel);
}
#endif
#endif

#endif


#endif  /* __IViewObject2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDropSource interface
 */
#ifndef __IDropSource_INTERFACE_DEFINED__
#define __IDropSource_INTERFACE_DEFINED__

typedef IDropSource *LPDROPSOURCE;

DEFINE_GUID(IID_IDropSource, 0x00000121, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000121-0000-0000-c000-000000000046")
IDropSource : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryContinueDrag(
        WINBOOL fEscapePressed,
        DWORD grfKeyState) = 0;

    virtual HRESULT STDMETHODCALLTYPE GiveFeedback(
        DWORD dwEffect) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDropSource, 0x00000121, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IDropSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDropSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDropSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDropSource *This);

    /*** IDropSource methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryContinueDrag)(
        IDropSource *This,
        WINBOOL fEscapePressed,
        DWORD grfKeyState);

    HRESULT (STDMETHODCALLTYPE *GiveFeedback)(
        IDropSource *This,
        DWORD dwEffect);

    END_INTERFACE
} IDropSourceVtbl;

interface IDropSource {
    CONST_VTBL IDropSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDropSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDropSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDropSource_Release(This) (This)->lpVtbl->Release(This)
/*** IDropSource methods ***/
#define IDropSource_QueryContinueDrag(This,fEscapePressed,grfKeyState) (This)->lpVtbl->QueryContinueDrag(This,fEscapePressed,grfKeyState)
#define IDropSource_GiveFeedback(This,dwEffect) (This)->lpVtbl->GiveFeedback(This,dwEffect)
#else
/*** IUnknown methods ***/
static inline HRESULT IDropSource_QueryInterface(IDropSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDropSource_AddRef(IDropSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDropSource_Release(IDropSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IDropSource methods ***/
static inline HRESULT IDropSource_QueryContinueDrag(IDropSource* This,WINBOOL fEscapePressed,DWORD grfKeyState) {
    return This->lpVtbl->QueryContinueDrag(This,fEscapePressed,grfKeyState);
}
static inline HRESULT IDropSource_GiveFeedback(IDropSource* This,DWORD dwEffect) {
    return This->lpVtbl->GiveFeedback(This,dwEffect);
}
#endif
#endif

#endif


#endif  /* __IDropSource_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDropTarget interface
 */
#ifndef __IDropTarget_INTERFACE_DEFINED__
#define __IDropTarget_INTERFACE_DEFINED__

typedef IDropTarget *LPDROPTARGET;

#define MK_ALT (0x20)

#define DROPEFFECT_NONE (0)

#define DROPEFFECT_COPY (1)

#define DROPEFFECT_MOVE (2)

#define DROPEFFECT_LINK (4)

#define DROPEFFECT_SCROLL (0x80000000)


#define DD_DEFSCROLLINSET (11)


#define DD_DEFSCROLLDELAY (50)


#define DD_DEFSCROLLINTERVAL (50)


#define DD_DEFDRAGDELAY (200)


#define DD_DEFDRAGMINDIST (2)


DEFINE_GUID(IID_IDropTarget, 0x00000122, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000122-0000-0000-c000-000000000046")
IDropTarget : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE DragEnter(
        IDataObject *pDataObj,
        DWORD grfKeyState,
        POINTL pt,
        DWORD *pdwEffect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DragOver(
        DWORD grfKeyState,
        POINTL pt,
        DWORD *pdwEffect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DragLeave(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Drop(
        IDataObject *pDataObj,
        DWORD grfKeyState,
        POINTL pt,
        DWORD *pdwEffect) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDropTarget, 0x00000122, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IDropTargetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDropTarget *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDropTarget *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDropTarget *This);

    /*** IDropTarget methods ***/
    HRESULT (STDMETHODCALLTYPE *DragEnter)(
        IDropTarget *This,
        IDataObject *pDataObj,
        DWORD grfKeyState,
        POINTL pt,
        DWORD *pdwEffect);

    HRESULT (STDMETHODCALLTYPE *DragOver)(
        IDropTarget *This,
        DWORD grfKeyState,
        POINTL pt,
        DWORD *pdwEffect);

    HRESULT (STDMETHODCALLTYPE *DragLeave)(
        IDropTarget *This);

    HRESULT (STDMETHODCALLTYPE *Drop)(
        IDropTarget *This,
        IDataObject *pDataObj,
        DWORD grfKeyState,
        POINTL pt,
        DWORD *pdwEffect);

    END_INTERFACE
} IDropTargetVtbl;

interface IDropTarget {
    CONST_VTBL IDropTargetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDropTarget_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDropTarget_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDropTarget_Release(This) (This)->lpVtbl->Release(This)
/*** IDropTarget methods ***/
#define IDropTarget_DragEnter(This,pDataObj,grfKeyState,pt,pdwEffect) (This)->lpVtbl->DragEnter(This,pDataObj,grfKeyState,pt,pdwEffect)
#define IDropTarget_DragOver(This,grfKeyState,pt,pdwEffect) (This)->lpVtbl->DragOver(This,grfKeyState,pt,pdwEffect)
#define IDropTarget_DragLeave(This) (This)->lpVtbl->DragLeave(This)
#define IDropTarget_Drop(This,pDataObj,grfKeyState,pt,pdwEffect) (This)->lpVtbl->Drop(This,pDataObj,grfKeyState,pt,pdwEffect)
#else
/*** IUnknown methods ***/
static inline HRESULT IDropTarget_QueryInterface(IDropTarget* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDropTarget_AddRef(IDropTarget* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDropTarget_Release(IDropTarget* This) {
    return This->lpVtbl->Release(This);
}
/*** IDropTarget methods ***/
static inline HRESULT IDropTarget_DragEnter(IDropTarget* This,IDataObject *pDataObj,DWORD grfKeyState,POINTL pt,DWORD *pdwEffect) {
    return This->lpVtbl->DragEnter(This,pDataObj,grfKeyState,pt,pdwEffect);
}
static inline HRESULT IDropTarget_DragOver(IDropTarget* This,DWORD grfKeyState,POINTL pt,DWORD *pdwEffect) {
    return This->lpVtbl->DragOver(This,grfKeyState,pt,pdwEffect);
}
static inline HRESULT IDropTarget_DragLeave(IDropTarget* This) {
    return This->lpVtbl->DragLeave(This);
}
static inline HRESULT IDropTarget_Drop(IDropTarget* This,IDataObject *pDataObj,DWORD grfKeyState,POINTL pt,DWORD *pdwEffect) {
    return This->lpVtbl->Drop(This,pDataObj,grfKeyState,pt,pdwEffect);
}
#endif
#endif

#endif


#endif  /* __IDropTarget_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDropSourceNotify interface
 */
#ifndef __IDropSourceNotify_INTERFACE_DEFINED__
#define __IDropSourceNotify_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDropSourceNotify, 0x0000012b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000012b-0000-0000-c000-000000000046")
IDropSourceNotify : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE DragEnterTarget(
        HWND hwndTarget) = 0;

    virtual HRESULT STDMETHODCALLTYPE DragLeaveTarget(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDropSourceNotify, 0x0000012b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IDropSourceNotifyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDropSourceNotify *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDropSourceNotify *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDropSourceNotify *This);

    /*** IDropSourceNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *DragEnterTarget)(
        IDropSourceNotify *This,
        HWND hwndTarget);

    HRESULT (STDMETHODCALLTYPE *DragLeaveTarget)(
        IDropSourceNotify *This);

    END_INTERFACE
} IDropSourceNotifyVtbl;

interface IDropSourceNotify {
    CONST_VTBL IDropSourceNotifyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDropSourceNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDropSourceNotify_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDropSourceNotify_Release(This) (This)->lpVtbl->Release(This)
/*** IDropSourceNotify methods ***/
#define IDropSourceNotify_DragEnterTarget(This,hwndTarget) (This)->lpVtbl->DragEnterTarget(This,hwndTarget)
#define IDropSourceNotify_DragLeaveTarget(This) (This)->lpVtbl->DragLeaveTarget(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDropSourceNotify_QueryInterface(IDropSourceNotify* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDropSourceNotify_AddRef(IDropSourceNotify* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDropSourceNotify_Release(IDropSourceNotify* This) {
    return This->lpVtbl->Release(This);
}
/*** IDropSourceNotify methods ***/
static inline HRESULT IDropSourceNotify_DragEnterTarget(IDropSourceNotify* This,HWND hwndTarget) {
    return This->lpVtbl->DragEnterTarget(This,hwndTarget);
}
static inline HRESULT IDropSourceNotify_DragLeaveTarget(IDropSourceNotify* This) {
    return This->lpVtbl->DragLeaveTarget(This);
}
#endif
#endif

#endif


#endif  /* __IDropSourceNotify_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IEnumOLEVERB interface
 */
#ifndef __IEnumOLEVERB_INTERFACE_DEFINED__
#define __IEnumOLEVERB_INTERFACE_DEFINED__

typedef IEnumOLEVERB *LPENUMOLEVERB;

typedef struct tagOLEVERB {
    LONG lVerb;
    LPOLESTR lpszVerbName;
    DWORD fuFlags;
    DWORD grfAttribs;
} OLEVERB;
typedef struct tagOLEVERB *LPOLEVERB;

typedef enum tagOLEVERBATTRIB {
    OLEVERBATTRIB_NEVERDIRTIES = 1,
    OLEVERBATTRIB_ONCONTAINERMENU = 2
} OLEVERBATTRIB;

DEFINE_GUID(IID_IEnumOLEVERB, 0x00000104, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000104-0000-0000-c000-000000000046")
IEnumOLEVERB : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        LPOLEVERB rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumOLEVERB **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumOLEVERB, 0x00000104, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumOLEVERBVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumOLEVERB *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumOLEVERB *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumOLEVERB *This);

    /*** IEnumOLEVERB methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumOLEVERB *This,
        ULONG celt,
        LPOLEVERB rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumOLEVERB *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumOLEVERB *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumOLEVERB *This,
        IEnumOLEVERB **ppenum);

    END_INTERFACE
} IEnumOLEVERBVtbl;

interface IEnumOLEVERB {
    CONST_VTBL IEnumOLEVERBVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumOLEVERB_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumOLEVERB_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumOLEVERB_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumOLEVERB methods ***/
#define IEnumOLEVERB_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumOLEVERB_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumOLEVERB_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumOLEVERB_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumOLEVERB_QueryInterface(IEnumOLEVERB* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumOLEVERB_AddRef(IEnumOLEVERB* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumOLEVERB_Release(IEnumOLEVERB* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumOLEVERB methods ***/
static inline HRESULT IEnumOLEVERB_Next(IEnumOLEVERB* This,ULONG celt,LPOLEVERB rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumOLEVERB_Skip(IEnumOLEVERB* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumOLEVERB_Reset(IEnumOLEVERB* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumOLEVERB_Clone(IEnumOLEVERB* This,IEnumOLEVERB **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumOLEVERB_RemoteNext_Proxy(
    IEnumOLEVERB* This,
    ULONG celt,
    LPOLEVERB rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumOLEVERB_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumOLEVERB_Next_Proxy(
    IEnumOLEVERB* This,
    ULONG celt,
    LPOLEVERB rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumOLEVERB_Next_Stub(
    IEnumOLEVERB* This,
    ULONG celt,
    LPOLEVERB rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumOLEVERB_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER CLIPFORMAT_UserSize     (ULONG *, ULONG, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserMarshal  (ULONG *, unsigned char *, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserUnmarshal(ULONG *, unsigned char *, CLIPFORMAT *);
void            __RPC_USER CLIPFORMAT_UserFree     (ULONG *, CLIPFORMAT *);
ULONG           __RPC_USER STGMEDIUM_UserSize     (ULONG *, ULONG, STGMEDIUM *);
unsigned char * __RPC_USER STGMEDIUM_UserMarshal  (ULONG *, unsigned char *, STGMEDIUM *);
unsigned char * __RPC_USER STGMEDIUM_UserUnmarshal(ULONG *, unsigned char *, STGMEDIUM *);
void            __RPC_USER STGMEDIUM_UserFree     (ULONG *, STGMEDIUM *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);
ULONG           __RPC_USER HMENU_UserSize     (ULONG *, ULONG, HMENU *);
unsigned char * __RPC_USER HMENU_UserMarshal  (ULONG *, unsigned char *, HMENU *);
unsigned char * __RPC_USER HMENU_UserUnmarshal(ULONG *, unsigned char *, HMENU *);
void            __RPC_USER HMENU_UserFree     (ULONG *, HMENU *);
ULONG           __RPC_USER HGLOBAL_UserSize     (ULONG *, ULONG, HGLOBAL *);
unsigned char * __RPC_USER HGLOBAL_UserMarshal  (ULONG *, unsigned char *, HGLOBAL *);
unsigned char * __RPC_USER HGLOBAL_UserUnmarshal(ULONG *, unsigned char *, HGLOBAL *);
void            __RPC_USER HGLOBAL_UserFree     (ULONG *, HGLOBAL *);
ULONG           __RPC_USER HACCEL_UserSize     (ULONG *, ULONG, HACCEL *);
unsigned char * __RPC_USER HACCEL_UserMarshal  (ULONG *, unsigned char *, HACCEL *);
unsigned char * __RPC_USER HACCEL_UserUnmarshal(ULONG *, unsigned char *, HACCEL *);
void            __RPC_USER HACCEL_UserFree     (ULONG *, HACCEL *);
ULONG           __RPC_USER HDC_UserSize     (ULONG *, ULONG, HDC *);
unsigned char * __RPC_USER HDC_UserMarshal  (ULONG *, unsigned char *, HDC *);
unsigned char * __RPC_USER HDC_UserUnmarshal(ULONG *, unsigned char *, HDC *);
void            __RPC_USER HDC_UserFree     (ULONG *, HDC *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __oleidl_h__ */
