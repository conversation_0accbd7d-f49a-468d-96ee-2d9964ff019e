MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="----- =_aaaaaaaaaa0"

------- =_aaaaaaaaaa0
Content-Type: multipart/mixed; boundary="----- =_aaaaaaaaaa1"
Content-ID: <<EMAIL>>

------- =_aaaaaaaaaa1
Content-Type: multipart/alternative; boundary="----- =_aaaaaaaaaa2"
Content-ID: <<EMAIL>>

------- =_aaaaaaaaaa2
Content-Type: text/plain
Content-ID: <<EMAIL>>
Content-Description: very tricky
Content-Transfer-Encoding: 7bit


Unlike the test test_nested-multiples-with-internal-boundary, this
piece of text not only contains the outer boundary tags 
------- =_aaaaaaaaaa1 
and 
------- =_aaaaaaaaaa0 
but puts them at the start of a line! And, to be even nastier, it
even includes a couple of end tags, such as this one:

------- =_aaaaaaaaaa1--

and this one, which is from a multipart we haven't even seen yet!

------- =_aaaaaaaaaa4--

This will, I'm sure, cause much breakage of MIME parsers. But, as 
far as I can tell, it's perfectly legal. I have not yet ever seen
a case of this in the wild, but I've seen *similar* things.


------- =_aaaaaaaaaa2
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch2
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa2--

------- =_aaaaaaaaaa1
Content-Type: multipart/alternative; boundary="----- =_aaaaaaaaaa3"
Content-ID: <<EMAIL>>

------- =_aaaaaaaaaa3
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch3
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa3
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch4
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa3--

------- =_aaaaaaaaaa1
Content-Type: multipart/alternative; boundary="----- =_aaaaaaaaaa4"
Content-ID: <<EMAIL>>

------- =_aaaaaaaaaa4
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch5
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa4
Content-Type: application/octet-stream
Content-ID: <<EMAIL>>
Content-Description: patch6
Content-Transfer-Encoding: base64

XXX

------- =_aaaaaaaaaa4--

------- =_aaaaaaaaaa1--

------- =_aaaaaaaaaa0
Content-Type: text/plain; charset="us-ascii"
Content-ID: <<EMAIL>>

--
It's never too late to have a happy childhood.

------- =_aaaaaaaaaa0--
