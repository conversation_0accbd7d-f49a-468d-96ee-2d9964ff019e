/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

import "naptypes.idl";
import "unknwn.idl";

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

[pointer_default (unique)]
interface INapSoHTypes {
  typedef enum tagSoHAttributeType {
    sohAttributeTypeSystemHealthId = 2,
    sohAttributeTypeIpv4FixupServers = 3,
    sohAttributeTypeComplianceResultCodes = 4,
    sohAttributeTypeTimeOfLastUpdate = 5,
    sohAttributeTypeClientId = 6,
    sohAttributeTypeVendorSpecific = 7,
    sohAttributeTypeHealthClass = 8,
    sohAttributeTypeSoftwareVersion = 9,
    sohAttributeTypeProductName = 10,
    sohAttributeTypeHealthClassStatus = 11,
    sohAttributeTypeSoHGenerationTime = 12,
    sohAttributeTypeErrorCodes = 13,
    sohAttributeTypeFailureCategory = 14,
    sohAttributeTypeIpv6FixupServers = 15,
    sohAttributeTypeExtendedIsolationState = 16
  } SoHAttributeType;

  typedef enum tagHealthClassValue {
    healthClassFirewall = 0,
    healthClassPatchLevel = 1,
    healthClassAntiVirus = 2,
    healthClassCriticalUpdate = 3,
    healthClassReserved = 128
  } HealthClassValue;

  typedef [switch_type (SoHAttributeType)] union tagSoHAttributeValue {
    [case (sohAttributeTypeSystemHealthId)] SystemHealthEntityId idVal;
    [case (sohAttributeTypeIpv4FixupServers)] struct tagIpv4Addresses {
      [range (1, maxIpv4CountPerSoHAttribute)] UINT16 count;
      [size_is (count)] Ipv4Address *addresses;
    } v4AddressesVal;
    [case (sohAttributeTypeIpv6FixupServers)] struct tagIpv6Addresses {
      [range (1, maxIpv6CountPerSoHAttribute)] UINT16 count;
      [size_is (count)] Ipv6Address *addresses;
    } v6AddressesVal;
    [case (sohAttributeTypeComplianceResultCodes, sohAttributeTypeErrorCodes)] ResultCodes codesVal;
    [case (sohAttributeTypeTimeOfLastUpdate, sohAttributeTypeSoHGenerationTime)] FILETIME dateTimeVal;
    [case (sohAttributeTypeVendorSpecific)] struct tagVendorSpecific {
      UINT32 vendorId;
      [range (0, maxSoHAttributeSize - 4)] UINT16 size;
      [size_is (size)] BYTE *vendorSpecificData;
    } vendorSpecificVal;
    [case (sohAttributeTypeHealthClass, sohAttributeTypeFailureCategory, sohAttributeTypeExtendedIsolationState)] UINT8 uint8Val;
    [default] struct tagOctetString {
      [range (0, maxSoHAttributeSize)] UINT16 size;
      [size_is (size)] BYTE *data;
    } octetStringVal;
  } SoHAttributeValue;
};

[object, uuid (35298344-96a6-45e7-9b6b-62ecc6e09920), pointer_default (unique)]
interface INapSoHConstructor : IUnknown {
  HRESULT Initialize ([in] SystemHealthEntityId id,[in] BOOL isRequest);
  HRESULT AppendAttribute ([in] SoHAttributeType type,[in, switch_is (type)] const SoHAttributeValue *value);
  HRESULT GetSoH ([out] SoH **soh);
  HRESULT Validate ([in] const SoH *soh,[in] BOOL isRequest);
};

[object, uuid (FB2FA8B0-2cd5-457d-ABA8-4376f63ea1c0), pointer_default (unique)]
interface INapSoHProcessor : IUnknown {
  HRESULT Initialize ([in] const SoH *soh,[in] BOOL isRequest,[out] SystemHealthEntityId *id);
  HRESULT FindNextAttribute ([in] UINT16 fromLocation,[in] SoHAttributeType type,[out] UINT16 *attributeLocation);
  HRESULT GetAttribute ([in] UINT16 attributeLocation,[out] SoHAttributeType *type,[out, switch_is (*type)] SoHAttributeValue **value);
  HRESULT GetNumberOfAttributes ([out] UINT16 *attributeCount);
};

cpp_quote("EXTERN_C const CLSID CLSID_NapSoHConstructor;")
cpp_quote("EXTERN_C const CLSID CLSID_NapSoHProcessor;")
cpp_quote("#endif")
