// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/graph_transfer_info.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
namespace tensorflow {
class GraphTransferConstNodeInfo;
struct GraphTransferConstNodeInfoDefaultTypeInternal;
extern GraphTransferConstNodeInfoDefaultTypeInternal _GraphTransferConstNodeInfo_default_instance_;
class GraphTransferGraphInputNodeInfo;
struct GraphTransferGraphInputNodeInfoDefaultTypeInternal;
extern GraphTransferGraphInputNodeInfoDefaultTypeInternal _GraphTransferGraphInputNodeInfo_default_instance_;
class GraphTransferGraphOutputNodeInfo;
struct GraphTransferGraphOutputNodeInfoDefaultTypeInternal;
extern GraphTransferGraphOutputNodeInfoDefaultTypeInternal _GraphTransferGraphOutputNodeInfo_default_instance_;
class GraphTransferInfo;
struct GraphTransferInfoDefaultTypeInternal;
extern GraphTransferInfoDefaultTypeInternal _GraphTransferInfo_default_instance_;
class GraphTransferNodeInfo;
struct GraphTransferNodeInfoDefaultTypeInternal;
extern GraphTransferNodeInfoDefaultTypeInternal _GraphTransferNodeInfo_default_instance_;
class GraphTransferNodeInput;
struct GraphTransferNodeInputDefaultTypeInternal;
extern GraphTransferNodeInputDefaultTypeInternal _GraphTransferNodeInput_default_instance_;
class GraphTransferNodeInputInfo;
struct GraphTransferNodeInputInfoDefaultTypeInternal;
extern GraphTransferNodeInputInfoDefaultTypeInternal _GraphTransferNodeInputInfo_default_instance_;
class GraphTransferNodeOutputInfo;
struct GraphTransferNodeOutputInfoDefaultTypeInternal;
extern GraphTransferNodeOutputInfoDefaultTypeInternal _GraphTransferNodeOutputInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::GraphTransferConstNodeInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferConstNodeInfo>(Arena*);
template<> ::tensorflow::GraphTransferGraphInputNodeInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferGraphInputNodeInfo>(Arena*);
template<> ::tensorflow::GraphTransferGraphOutputNodeInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferGraphOutputNodeInfo>(Arena*);
template<> ::tensorflow::GraphTransferInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferInfo>(Arena*);
template<> ::tensorflow::GraphTransferNodeInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferNodeInfo>(Arena*);
template<> ::tensorflow::GraphTransferNodeInput* Arena::CreateMaybeMessage<::tensorflow::GraphTransferNodeInput>(Arena*);
template<> ::tensorflow::GraphTransferNodeInputInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferNodeInputInfo>(Arena*);
template<> ::tensorflow::GraphTransferNodeOutputInfo* Arena::CreateMaybeMessage<::tensorflow::GraphTransferNodeOutputInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum GraphTransferInfo_Destination : int {
  GraphTransferInfo_Destination_NOP = 0,
  GraphTransferInfo_Destination_HEXAGON = 1,
  GraphTransferInfo_Destination_GraphTransferInfo_Destination_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  GraphTransferInfo_Destination_GraphTransferInfo_Destination_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool GraphTransferInfo_Destination_IsValid(int value);
constexpr GraphTransferInfo_Destination GraphTransferInfo_Destination_Destination_MIN = GraphTransferInfo_Destination_NOP;
constexpr GraphTransferInfo_Destination GraphTransferInfo_Destination_Destination_MAX = GraphTransferInfo_Destination_HEXAGON;
constexpr int GraphTransferInfo_Destination_Destination_ARRAYSIZE = GraphTransferInfo_Destination_Destination_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GraphTransferInfo_Destination_descriptor();
template<typename T>
inline const std::string& GraphTransferInfo_Destination_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GraphTransferInfo_Destination>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GraphTransferInfo_Destination_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GraphTransferInfo_Destination_descriptor(), enum_t_value);
}
inline bool GraphTransferInfo_Destination_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, GraphTransferInfo_Destination* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GraphTransferInfo_Destination>(
    GraphTransferInfo_Destination_descriptor(), name, value);
}
// ===================================================================

class GraphTransferNodeInput final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferNodeInput) */ {
 public:
  inline GraphTransferNodeInput() : GraphTransferNodeInput(nullptr) {}
  ~GraphTransferNodeInput() override;
  explicit PROTOBUF_CONSTEXPR GraphTransferNodeInput(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphTransferNodeInput(const GraphTransferNodeInput& from);
  GraphTransferNodeInput(GraphTransferNodeInput&& from) noexcept
    : GraphTransferNodeInput() {
    *this = ::std::move(from);
  }

  inline GraphTransferNodeInput& operator=(const GraphTransferNodeInput& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferNodeInput& operator=(GraphTransferNodeInput&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphTransferNodeInput& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphTransferNodeInput* internal_default_instance() {
    return reinterpret_cast<const GraphTransferNodeInput*>(
               &_GraphTransferNodeInput_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GraphTransferNodeInput& a, GraphTransferNodeInput& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferNodeInput* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferNodeInput* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphTransferNodeInput* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphTransferNodeInput>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphTransferNodeInput& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphTransferNodeInput& from) {
    GraphTransferNodeInput::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferNodeInput* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferNodeInput";
  }
  protected:
  explicit GraphTransferNodeInput(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
    kOutputPortFieldNumber = 2,
  };
  // int32 node_id = 1;
  void clear_node_id();
  int32_t node_id() const;
  void set_node_id(int32_t value);
  private:
  int32_t _internal_node_id() const;
  void _internal_set_node_id(int32_t value);
  public:

  // int32 output_port = 2;
  void clear_output_port();
  int32_t output_port() const;
  void set_output_port(int32_t value);
  private:
  int32_t _internal_output_port() const;
  void _internal_set_output_port(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferNodeInput)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t node_id_;
    int32_t output_port_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferNodeInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferNodeInfo) */ {
 public:
  inline GraphTransferNodeInfo() : GraphTransferNodeInfo(nullptr) {}
  ~GraphTransferNodeInfo() override;
  explicit PROTOBUF_CONSTEXPR GraphTransferNodeInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphTransferNodeInfo(const GraphTransferNodeInfo& from);
  GraphTransferNodeInfo(GraphTransferNodeInfo&& from) noexcept
    : GraphTransferNodeInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferNodeInfo& operator=(const GraphTransferNodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferNodeInfo& operator=(GraphTransferNodeInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphTransferNodeInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphTransferNodeInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferNodeInfo*>(
               &_GraphTransferNodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GraphTransferNodeInfo& a, GraphTransferNodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferNodeInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferNodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphTransferNodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphTransferNodeInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphTransferNodeInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphTransferNodeInfo& from) {
    GraphTransferNodeInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferNodeInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferNodeInfo";
  }
  protected:
  explicit GraphTransferNodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kTypeNameFieldNumber = 3,
    kNodeIdFieldNumber = 2,
    kSocOpIdFieldNumber = 4,
    kPaddingIdFieldNumber = 5,
    kInputCountFieldNumber = 6,
    kOutputCountFieldNumber = 7,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string type_name = 3;
  void clear_type_name();
  const std::string& type_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type_name();
  PROTOBUF_NODISCARD std::string* release_type_name();
  void set_allocated_type_name(std::string* type_name);
  private:
  const std::string& _internal_type_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type_name(const std::string& value);
  std::string* _internal_mutable_type_name();
  public:

  // int32 node_id = 2;
  void clear_node_id();
  int32_t node_id() const;
  void set_node_id(int32_t value);
  private:
  int32_t _internal_node_id() const;
  void _internal_set_node_id(int32_t value);
  public:

  // int32 soc_op_id = 4;
  void clear_soc_op_id();
  int32_t soc_op_id() const;
  void set_soc_op_id(int32_t value);
  private:
  int32_t _internal_soc_op_id() const;
  void _internal_set_soc_op_id(int32_t value);
  public:

  // int32 padding_id = 5;
  void clear_padding_id();
  int32_t padding_id() const;
  void set_padding_id(int32_t value);
  private:
  int32_t _internal_padding_id() const;
  void _internal_set_padding_id(int32_t value);
  public:

  // int32 input_count = 6;
  void clear_input_count();
  int32_t input_count() const;
  void set_input_count(int32_t value);
  private:
  int32_t _internal_input_count() const;
  void _internal_set_input_count(int32_t value);
  public:

  // int32 output_count = 7;
  void clear_output_count();
  int32_t output_count() const;
  void set_output_count(int32_t value);
  private:
  int32_t _internal_output_count() const;
  void _internal_set_output_count(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferNodeInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_name_;
    int32_t node_id_;
    int32_t soc_op_id_;
    int32_t padding_id_;
    int32_t input_count_;
    int32_t output_count_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferConstNodeInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferConstNodeInfo) */ {
 public:
  inline GraphTransferConstNodeInfo() : GraphTransferConstNodeInfo(nullptr) {}
  ~GraphTransferConstNodeInfo() override;
  explicit PROTOBUF_CONSTEXPR GraphTransferConstNodeInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphTransferConstNodeInfo(const GraphTransferConstNodeInfo& from);
  GraphTransferConstNodeInfo(GraphTransferConstNodeInfo&& from) noexcept
    : GraphTransferConstNodeInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferConstNodeInfo& operator=(const GraphTransferConstNodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferConstNodeInfo& operator=(GraphTransferConstNodeInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphTransferConstNodeInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphTransferConstNodeInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferConstNodeInfo*>(
               &_GraphTransferConstNodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GraphTransferConstNodeInfo& a, GraphTransferConstNodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferConstNodeInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferConstNodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphTransferConstNodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphTransferConstNodeInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphTransferConstNodeInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphTransferConstNodeInfo& from) {
    GraphTransferConstNodeInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferConstNodeInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferConstNodeInfo";
  }
  protected:
  explicit GraphTransferConstNodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 3,
    kNameFieldNumber = 1,
    kDataFieldNumber = 4,
    kNodeIdFieldNumber = 2,
    kDtypeFieldNumber = 5,
  };
  // repeated int64 shape = 3;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  private:
  int64_t _internal_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_shape() const;
  void _internal_add_shape(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_shape();
  public:
  int64_t shape(int index) const;
  void set_shape(int index, int64_t value);
  void add_shape(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_shape();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bytes data = 4;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // int32 node_id = 2;
  void clear_node_id();
  int32_t node_id() const;
  void set_node_id(int32_t value);
  private:
  int32_t _internal_node_id() const;
  void _internal_set_node_id(int32_t value);
  public:

  // .tensorflow.DataType dtype = 5;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferConstNodeInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > shape_;
    mutable std::atomic<int> _shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
    int32_t node_id_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferNodeInputInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferNodeInputInfo) */ {
 public:
  inline GraphTransferNodeInputInfo() : GraphTransferNodeInputInfo(nullptr) {}
  ~GraphTransferNodeInputInfo() override;
  explicit PROTOBUF_CONSTEXPR GraphTransferNodeInputInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphTransferNodeInputInfo(const GraphTransferNodeInputInfo& from);
  GraphTransferNodeInputInfo(GraphTransferNodeInputInfo&& from) noexcept
    : GraphTransferNodeInputInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferNodeInputInfo& operator=(const GraphTransferNodeInputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferNodeInputInfo& operator=(GraphTransferNodeInputInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphTransferNodeInputInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphTransferNodeInputInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferNodeInputInfo*>(
               &_GraphTransferNodeInputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GraphTransferNodeInputInfo& a, GraphTransferNodeInputInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferNodeInputInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferNodeInputInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphTransferNodeInputInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphTransferNodeInputInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphTransferNodeInputInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphTransferNodeInputInfo& from) {
    GraphTransferNodeInputInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferNodeInputInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferNodeInputInfo";
  }
  protected:
  explicit GraphTransferNodeInputInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeInputFieldNumber = 2,
    kNodeIdFieldNumber = 1,
  };
  // repeated .tensorflow.GraphTransferNodeInput node_input = 2;
  int node_input_size() const;
  private:
  int _internal_node_input_size() const;
  public:
  void clear_node_input();
  ::tensorflow::GraphTransferNodeInput* mutable_node_input(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput >*
      mutable_node_input();
  private:
  const ::tensorflow::GraphTransferNodeInput& _internal_node_input(int index) const;
  ::tensorflow::GraphTransferNodeInput* _internal_add_node_input();
  public:
  const ::tensorflow::GraphTransferNodeInput& node_input(int index) const;
  ::tensorflow::GraphTransferNodeInput* add_node_input();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput >&
      node_input() const;

  // int32 node_id = 1;
  void clear_node_id();
  int32_t node_id() const;
  void set_node_id(int32_t value);
  private:
  int32_t _internal_node_id() const;
  void _internal_set_node_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferNodeInputInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput > node_input_;
    int32_t node_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferNodeOutputInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferNodeOutputInfo) */ {
 public:
  inline GraphTransferNodeOutputInfo() : GraphTransferNodeOutputInfo(nullptr) {}
  ~GraphTransferNodeOutputInfo() override;
  explicit PROTOBUF_CONSTEXPR GraphTransferNodeOutputInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphTransferNodeOutputInfo(const GraphTransferNodeOutputInfo& from);
  GraphTransferNodeOutputInfo(GraphTransferNodeOutputInfo&& from) noexcept
    : GraphTransferNodeOutputInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferNodeOutputInfo& operator=(const GraphTransferNodeOutputInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferNodeOutputInfo& operator=(GraphTransferNodeOutputInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphTransferNodeOutputInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphTransferNodeOutputInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferNodeOutputInfo*>(
               &_GraphTransferNodeOutputInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GraphTransferNodeOutputInfo& a, GraphTransferNodeOutputInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferNodeOutputInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferNodeOutputInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphTransferNodeOutputInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphTransferNodeOutputInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphTransferNodeOutputInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphTransferNodeOutputInfo& from) {
    GraphTransferNodeOutputInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferNodeOutputInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferNodeOutputInfo";
  }
  protected:
  explicit GraphTransferNodeOutputInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMaxByteSizeFieldNumber = 2,
    kNodeIdFieldNumber = 1,
  };
  // repeated int32 max_byte_size = 2;
  int max_byte_size_size() const;
  private:
  int _internal_max_byte_size_size() const;
  public:
  void clear_max_byte_size();
  private:
  int32_t _internal_max_byte_size(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_max_byte_size() const;
  void _internal_add_max_byte_size(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_max_byte_size();
  public:
  int32_t max_byte_size(int index) const;
  void set_max_byte_size(int index, int32_t value);
  void add_max_byte_size(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      max_byte_size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_max_byte_size();

  // int32 node_id = 1;
  void clear_node_id();
  int32_t node_id() const;
  void set_node_id(int32_t value);
  private:
  int32_t _internal_node_id() const;
  void _internal_set_node_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferNodeOutputInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > max_byte_size_;
    mutable std::atomic<int> _max_byte_size_cached_byte_size_;
    int32_t node_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferGraphInputNodeInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferGraphInputNodeInfo) */ {
 public:
  inline GraphTransferGraphInputNodeInfo() : GraphTransferGraphInputNodeInfo(nullptr) {}
  ~GraphTransferGraphInputNodeInfo() override;
  explicit PROTOBUF_CONSTEXPR GraphTransferGraphInputNodeInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphTransferGraphInputNodeInfo(const GraphTransferGraphInputNodeInfo& from);
  GraphTransferGraphInputNodeInfo(GraphTransferGraphInputNodeInfo&& from) noexcept
    : GraphTransferGraphInputNodeInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferGraphInputNodeInfo& operator=(const GraphTransferGraphInputNodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferGraphInputNodeInfo& operator=(GraphTransferGraphInputNodeInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphTransferGraphInputNodeInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphTransferGraphInputNodeInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferGraphInputNodeInfo*>(
               &_GraphTransferGraphInputNodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GraphTransferGraphInputNodeInfo& a, GraphTransferGraphInputNodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferGraphInputNodeInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferGraphInputNodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphTransferGraphInputNodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphTransferGraphInputNodeInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphTransferGraphInputNodeInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphTransferGraphInputNodeInfo& from) {
    GraphTransferGraphInputNodeInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferGraphInputNodeInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferGraphInputNodeInfo";
  }
  protected:
  explicit GraphTransferGraphInputNodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kNameFieldNumber = 1,
    kDtypeFieldNumber = 3,
  };
  // repeated int64 shape = 2;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  private:
  int64_t _internal_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_shape() const;
  void _internal_add_shape(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_shape();
  public:
  int64_t shape(int index) const;
  void set_shape(int index, int64_t value);
  void add_shape(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_shape();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferGraphInputNodeInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > shape_;
    mutable std::atomic<int> _shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferGraphOutputNodeInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferGraphOutputNodeInfo) */ {
 public:
  inline GraphTransferGraphOutputNodeInfo() : GraphTransferGraphOutputNodeInfo(nullptr) {}
  ~GraphTransferGraphOutputNodeInfo() override;
  explicit PROTOBUF_CONSTEXPR GraphTransferGraphOutputNodeInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphTransferGraphOutputNodeInfo(const GraphTransferGraphOutputNodeInfo& from);
  GraphTransferGraphOutputNodeInfo(GraphTransferGraphOutputNodeInfo&& from) noexcept
    : GraphTransferGraphOutputNodeInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferGraphOutputNodeInfo& operator=(const GraphTransferGraphOutputNodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferGraphOutputNodeInfo& operator=(GraphTransferGraphOutputNodeInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphTransferGraphOutputNodeInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphTransferGraphOutputNodeInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferGraphOutputNodeInfo*>(
               &_GraphTransferGraphOutputNodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(GraphTransferGraphOutputNodeInfo& a, GraphTransferGraphOutputNodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferGraphOutputNodeInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferGraphOutputNodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphTransferGraphOutputNodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphTransferGraphOutputNodeInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphTransferGraphOutputNodeInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphTransferGraphOutputNodeInfo& from) {
    GraphTransferGraphOutputNodeInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferGraphOutputNodeInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferGraphOutputNodeInfo";
  }
  protected:
  explicit GraphTransferGraphOutputNodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kNameFieldNumber = 1,
    kDtypeFieldNumber = 3,
  };
  // repeated int64 shape = 2;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  private:
  int64_t _internal_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_shape() const;
  void _internal_add_shape(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_shape();
  public:
  int64_t shape(int index) const;
  void set_shape(int index, int64_t value);
  void add_shape(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_shape();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferGraphOutputNodeInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > shape_;
    mutable std::atomic<int> _shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// -------------------------------------------------------------------

class GraphTransferInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GraphTransferInfo) */ {
 public:
  inline GraphTransferInfo() : GraphTransferInfo(nullptr) {}
  ~GraphTransferInfo() override;
  explicit PROTOBUF_CONSTEXPR GraphTransferInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphTransferInfo(const GraphTransferInfo& from);
  GraphTransferInfo(GraphTransferInfo&& from) noexcept
    : GraphTransferInfo() {
    *this = ::std::move(from);
  }

  inline GraphTransferInfo& operator=(const GraphTransferInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphTransferInfo& operator=(GraphTransferInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphTransferInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphTransferInfo* internal_default_instance() {
    return reinterpret_cast<const GraphTransferInfo*>(
               &_GraphTransferInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(GraphTransferInfo& a, GraphTransferInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphTransferInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphTransferInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphTransferInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphTransferInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphTransferInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphTransferInfo& from) {
    GraphTransferInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphTransferInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GraphTransferInfo";
  }
  protected:
  explicit GraphTransferInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef GraphTransferInfo_Destination Destination;
  static constexpr Destination NOP =
    GraphTransferInfo_Destination_NOP;
  static constexpr Destination HEXAGON =
    GraphTransferInfo_Destination_HEXAGON;
  static inline bool Destination_IsValid(int value) {
    return GraphTransferInfo_Destination_IsValid(value);
  }
  static constexpr Destination Destination_MIN =
    GraphTransferInfo_Destination_Destination_MIN;
  static constexpr Destination Destination_MAX =
    GraphTransferInfo_Destination_Destination_MAX;
  static constexpr int Destination_ARRAYSIZE =
    GraphTransferInfo_Destination_Destination_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Destination_descriptor() {
    return GraphTransferInfo_Destination_descriptor();
  }
  template<typename T>
  static inline const std::string& Destination_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Destination>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Destination_Name.");
    return GraphTransferInfo_Destination_Name(enum_t_value);
  }
  static inline bool Destination_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Destination* value) {
    return GraphTransferInfo_Destination_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kNodeInfoFieldNumber = 1,
    kConstNodeInfoFieldNumber = 2,
    kNodeInputInfoFieldNumber = 3,
    kNodeOutputInfoFieldNumber = 4,
    kGraphInputNodeInfoFieldNumber = 5,
    kGraphOutputNodeInfoFieldNumber = 6,
    kDestinationFieldNumber = 7,
  };
  // repeated .tensorflow.GraphTransferNodeInfo node_info = 1;
  int node_info_size() const;
  private:
  int _internal_node_info_size() const;
  public:
  void clear_node_info();
  ::tensorflow::GraphTransferNodeInfo* mutable_node_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo >*
      mutable_node_info();
  private:
  const ::tensorflow::GraphTransferNodeInfo& _internal_node_info(int index) const;
  ::tensorflow::GraphTransferNodeInfo* _internal_add_node_info();
  public:
  const ::tensorflow::GraphTransferNodeInfo& node_info(int index) const;
  ::tensorflow::GraphTransferNodeInfo* add_node_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo >&
      node_info() const;

  // repeated .tensorflow.GraphTransferConstNodeInfo const_node_info = 2;
  int const_node_info_size() const;
  private:
  int _internal_const_node_info_size() const;
  public:
  void clear_const_node_info();
  ::tensorflow::GraphTransferConstNodeInfo* mutable_const_node_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo >*
      mutable_const_node_info();
  private:
  const ::tensorflow::GraphTransferConstNodeInfo& _internal_const_node_info(int index) const;
  ::tensorflow::GraphTransferConstNodeInfo* _internal_add_const_node_info();
  public:
  const ::tensorflow::GraphTransferConstNodeInfo& const_node_info(int index) const;
  ::tensorflow::GraphTransferConstNodeInfo* add_const_node_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo >&
      const_node_info() const;

  // repeated .tensorflow.GraphTransferNodeInputInfo node_input_info = 3;
  int node_input_info_size() const;
  private:
  int _internal_node_input_info_size() const;
  public:
  void clear_node_input_info();
  ::tensorflow::GraphTransferNodeInputInfo* mutable_node_input_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo >*
      mutable_node_input_info();
  private:
  const ::tensorflow::GraphTransferNodeInputInfo& _internal_node_input_info(int index) const;
  ::tensorflow::GraphTransferNodeInputInfo* _internal_add_node_input_info();
  public:
  const ::tensorflow::GraphTransferNodeInputInfo& node_input_info(int index) const;
  ::tensorflow::GraphTransferNodeInputInfo* add_node_input_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo >&
      node_input_info() const;

  // repeated .tensorflow.GraphTransferNodeOutputInfo node_output_info = 4;
  int node_output_info_size() const;
  private:
  int _internal_node_output_info_size() const;
  public:
  void clear_node_output_info();
  ::tensorflow::GraphTransferNodeOutputInfo* mutable_node_output_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo >*
      mutable_node_output_info();
  private:
  const ::tensorflow::GraphTransferNodeOutputInfo& _internal_node_output_info(int index) const;
  ::tensorflow::GraphTransferNodeOutputInfo* _internal_add_node_output_info();
  public:
  const ::tensorflow::GraphTransferNodeOutputInfo& node_output_info(int index) const;
  ::tensorflow::GraphTransferNodeOutputInfo* add_node_output_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo >&
      node_output_info() const;

  // repeated .tensorflow.GraphTransferGraphInputNodeInfo graph_input_node_info = 5;
  int graph_input_node_info_size() const;
  private:
  int _internal_graph_input_node_info_size() const;
  public:
  void clear_graph_input_node_info();
  ::tensorflow::GraphTransferGraphInputNodeInfo* mutable_graph_input_node_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo >*
      mutable_graph_input_node_info();
  private:
  const ::tensorflow::GraphTransferGraphInputNodeInfo& _internal_graph_input_node_info(int index) const;
  ::tensorflow::GraphTransferGraphInputNodeInfo* _internal_add_graph_input_node_info();
  public:
  const ::tensorflow::GraphTransferGraphInputNodeInfo& graph_input_node_info(int index) const;
  ::tensorflow::GraphTransferGraphInputNodeInfo* add_graph_input_node_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo >&
      graph_input_node_info() const;

  // repeated .tensorflow.GraphTransferGraphOutputNodeInfo graph_output_node_info = 6;
  int graph_output_node_info_size() const;
  private:
  int _internal_graph_output_node_info_size() const;
  public:
  void clear_graph_output_node_info();
  ::tensorflow::GraphTransferGraphOutputNodeInfo* mutable_graph_output_node_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo >*
      mutable_graph_output_node_info();
  private:
  const ::tensorflow::GraphTransferGraphOutputNodeInfo& _internal_graph_output_node_info(int index) const;
  ::tensorflow::GraphTransferGraphOutputNodeInfo* _internal_add_graph_output_node_info();
  public:
  const ::tensorflow::GraphTransferGraphOutputNodeInfo& graph_output_node_info(int index) const;
  ::tensorflow::GraphTransferGraphOutputNodeInfo* add_graph_output_node_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo >&
      graph_output_node_info() const;

  // .tensorflow.GraphTransferInfo.Destination destination = 7;
  void clear_destination();
  ::tensorflow::GraphTransferInfo_Destination destination() const;
  void set_destination(::tensorflow::GraphTransferInfo_Destination value);
  private:
  ::tensorflow::GraphTransferInfo_Destination _internal_destination() const;
  void _internal_set_destination(::tensorflow::GraphTransferInfo_Destination value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GraphTransferInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo > node_info_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo > const_node_info_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo > node_input_info_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo > node_output_info_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo > graph_input_node_info_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo > graph_output_node_info_;
    int destination_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GraphTransferNodeInput

// int32 node_id = 1;
inline void GraphTransferNodeInput::clear_node_id() {
  _impl_.node_id_ = 0;
}
inline int32_t GraphTransferNodeInput::_internal_node_id() const {
  return _impl_.node_id_;
}
inline int32_t GraphTransferNodeInput::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInput.node_id)
  return _internal_node_id();
}
inline void GraphTransferNodeInput::_internal_set_node_id(int32_t value) {
  
  _impl_.node_id_ = value;
}
inline void GraphTransferNodeInput::set_node_id(int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInput.node_id)
}

// int32 output_port = 2;
inline void GraphTransferNodeInput::clear_output_port() {
  _impl_.output_port_ = 0;
}
inline int32_t GraphTransferNodeInput::_internal_output_port() const {
  return _impl_.output_port_;
}
inline int32_t GraphTransferNodeInput::output_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInput.output_port)
  return _internal_output_port();
}
inline void GraphTransferNodeInput::_internal_set_output_port(int32_t value) {
  
  _impl_.output_port_ = value;
}
inline void GraphTransferNodeInput::set_output_port(int32_t value) {
  _internal_set_output_port(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInput.output_port)
}

// -------------------------------------------------------------------

// GraphTransferNodeInfo

// string name = 1;
inline void GraphTransferNodeInfo::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& GraphTransferNodeInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphTransferNodeInfo::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.name)
}
inline std::string* GraphTransferNodeInfo::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferNodeInfo.name)
  return _s;
}
inline const std::string& GraphTransferNodeInfo::_internal_name() const {
  return _impl_.name_.Get();
}
inline void GraphTransferNodeInfo::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphTransferNodeInfo::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphTransferNodeInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferNodeInfo.name)
  return _impl_.name_.Release();
}
inline void GraphTransferNodeInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferNodeInfo.name)
}

// int32 node_id = 2;
inline void GraphTransferNodeInfo::clear_node_id() {
  _impl_.node_id_ = 0;
}
inline int32_t GraphTransferNodeInfo::_internal_node_id() const {
  return _impl_.node_id_;
}
inline int32_t GraphTransferNodeInfo::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.node_id)
  return _internal_node_id();
}
inline void GraphTransferNodeInfo::_internal_set_node_id(int32_t value) {
  
  _impl_.node_id_ = value;
}
inline void GraphTransferNodeInfo::set_node_id(int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.node_id)
}

// string type_name = 3;
inline void GraphTransferNodeInfo::clear_type_name() {
  _impl_.type_name_.ClearToEmpty();
}
inline const std::string& GraphTransferNodeInfo::type_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.type_name)
  return _internal_type_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphTransferNodeInfo::set_type_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.type_name)
}
inline std::string* GraphTransferNodeInfo::mutable_type_name() {
  std::string* _s = _internal_mutable_type_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferNodeInfo.type_name)
  return _s;
}
inline const std::string& GraphTransferNodeInfo::_internal_type_name() const {
  return _impl_.type_name_.Get();
}
inline void GraphTransferNodeInfo::_internal_set_type_name(const std::string& value) {
  
  _impl_.type_name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphTransferNodeInfo::_internal_mutable_type_name() {
  
  return _impl_.type_name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphTransferNodeInfo::release_type_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferNodeInfo.type_name)
  return _impl_.type_name_.Release();
}
inline void GraphTransferNodeInfo::set_allocated_type_name(std::string* type_name) {
  if (type_name != nullptr) {
    
  } else {
    
  }
  _impl_.type_name_.SetAllocated(type_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_name_.IsDefault()) {
    _impl_.type_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferNodeInfo.type_name)
}

// int32 soc_op_id = 4;
inline void GraphTransferNodeInfo::clear_soc_op_id() {
  _impl_.soc_op_id_ = 0;
}
inline int32_t GraphTransferNodeInfo::_internal_soc_op_id() const {
  return _impl_.soc_op_id_;
}
inline int32_t GraphTransferNodeInfo::soc_op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.soc_op_id)
  return _internal_soc_op_id();
}
inline void GraphTransferNodeInfo::_internal_set_soc_op_id(int32_t value) {
  
  _impl_.soc_op_id_ = value;
}
inline void GraphTransferNodeInfo::set_soc_op_id(int32_t value) {
  _internal_set_soc_op_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.soc_op_id)
}

// int32 padding_id = 5;
inline void GraphTransferNodeInfo::clear_padding_id() {
  _impl_.padding_id_ = 0;
}
inline int32_t GraphTransferNodeInfo::_internal_padding_id() const {
  return _impl_.padding_id_;
}
inline int32_t GraphTransferNodeInfo::padding_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.padding_id)
  return _internal_padding_id();
}
inline void GraphTransferNodeInfo::_internal_set_padding_id(int32_t value) {
  
  _impl_.padding_id_ = value;
}
inline void GraphTransferNodeInfo::set_padding_id(int32_t value) {
  _internal_set_padding_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.padding_id)
}

// int32 input_count = 6;
inline void GraphTransferNodeInfo::clear_input_count() {
  _impl_.input_count_ = 0;
}
inline int32_t GraphTransferNodeInfo::_internal_input_count() const {
  return _impl_.input_count_;
}
inline int32_t GraphTransferNodeInfo::input_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.input_count)
  return _internal_input_count();
}
inline void GraphTransferNodeInfo::_internal_set_input_count(int32_t value) {
  
  _impl_.input_count_ = value;
}
inline void GraphTransferNodeInfo::set_input_count(int32_t value) {
  _internal_set_input_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.input_count)
}

// int32 output_count = 7;
inline void GraphTransferNodeInfo::clear_output_count() {
  _impl_.output_count_ = 0;
}
inline int32_t GraphTransferNodeInfo::_internal_output_count() const {
  return _impl_.output_count_;
}
inline int32_t GraphTransferNodeInfo::output_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInfo.output_count)
  return _internal_output_count();
}
inline void GraphTransferNodeInfo::_internal_set_output_count(int32_t value) {
  
  _impl_.output_count_ = value;
}
inline void GraphTransferNodeInfo::set_output_count(int32_t value) {
  _internal_set_output_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInfo.output_count)
}

// -------------------------------------------------------------------

// GraphTransferConstNodeInfo

// string name = 1;
inline void GraphTransferConstNodeInfo::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& GraphTransferConstNodeInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphTransferConstNodeInfo::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.name)
}
inline std::string* GraphTransferConstNodeInfo::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferConstNodeInfo.name)
  return _s;
}
inline const std::string& GraphTransferConstNodeInfo::_internal_name() const {
  return _impl_.name_.Get();
}
inline void GraphTransferConstNodeInfo::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphTransferConstNodeInfo::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphTransferConstNodeInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferConstNodeInfo.name)
  return _impl_.name_.Release();
}
inline void GraphTransferConstNodeInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferConstNodeInfo.name)
}

// int32 node_id = 2;
inline void GraphTransferConstNodeInfo::clear_node_id() {
  _impl_.node_id_ = 0;
}
inline int32_t GraphTransferConstNodeInfo::_internal_node_id() const {
  return _impl_.node_id_;
}
inline int32_t GraphTransferConstNodeInfo::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.node_id)
  return _internal_node_id();
}
inline void GraphTransferConstNodeInfo::_internal_set_node_id(int32_t value) {
  
  _impl_.node_id_ = value;
}
inline void GraphTransferConstNodeInfo::set_node_id(int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.node_id)
}

// repeated int64 shape = 3;
inline int GraphTransferConstNodeInfo::_internal_shape_size() const {
  return _impl_.shape_.size();
}
inline int GraphTransferConstNodeInfo::shape_size() const {
  return _internal_shape_size();
}
inline void GraphTransferConstNodeInfo::clear_shape() {
  _impl_.shape_.Clear();
}
inline int64_t GraphTransferConstNodeInfo::_internal_shape(int index) const {
  return _impl_.shape_.Get(index);
}
inline int64_t GraphTransferConstNodeInfo::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.shape)
  return _internal_shape(index);
}
inline void GraphTransferConstNodeInfo::set_shape(int index, int64_t value) {
  _impl_.shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.shape)
}
inline void GraphTransferConstNodeInfo::_internal_add_shape(int64_t value) {
  _impl_.shape_.Add(value);
}
inline void GraphTransferConstNodeInfo::add_shape(int64_t value) {
  _internal_add_shape(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferConstNodeInfo.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
GraphTransferConstNodeInfo::_internal_shape() const {
  return _impl_.shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
GraphTransferConstNodeInfo::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferConstNodeInfo.shape)
  return _internal_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
GraphTransferConstNodeInfo::_internal_mutable_shape() {
  return &_impl_.shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
GraphTransferConstNodeInfo::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferConstNodeInfo.shape)
  return _internal_mutable_shape();
}

// bytes data = 4;
inline void GraphTransferConstNodeInfo::clear_data() {
  _impl_.data_.ClearToEmpty();
}
inline const std::string& GraphTransferConstNodeInfo::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphTransferConstNodeInfo::set_data(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.data)
}
inline std::string* GraphTransferConstNodeInfo::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferConstNodeInfo.data)
  return _s;
}
inline const std::string& GraphTransferConstNodeInfo::_internal_data() const {
  return _impl_.data_.Get();
}
inline void GraphTransferConstNodeInfo::_internal_set_data(const std::string& value) {
  
  _impl_.data_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphTransferConstNodeInfo::_internal_mutable_data() {
  
  return _impl_.data_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphTransferConstNodeInfo::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferConstNodeInfo.data)
  return _impl_.data_.Release();
}
inline void GraphTransferConstNodeInfo::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  _impl_.data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_.IsDefault()) {
    _impl_.data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferConstNodeInfo.data)
}

// .tensorflow.DataType dtype = 5;
inline void GraphTransferConstNodeInfo::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType GraphTransferConstNodeInfo::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType GraphTransferConstNodeInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferConstNodeInfo.dtype)
  return _internal_dtype();
}
inline void GraphTransferConstNodeInfo::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void GraphTransferConstNodeInfo::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferConstNodeInfo.dtype)
}

// -------------------------------------------------------------------

// GraphTransferNodeInputInfo

// int32 node_id = 1;
inline void GraphTransferNodeInputInfo::clear_node_id() {
  _impl_.node_id_ = 0;
}
inline int32_t GraphTransferNodeInputInfo::_internal_node_id() const {
  return _impl_.node_id_;
}
inline int32_t GraphTransferNodeInputInfo::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInputInfo.node_id)
  return _internal_node_id();
}
inline void GraphTransferNodeInputInfo::_internal_set_node_id(int32_t value) {
  
  _impl_.node_id_ = value;
}
inline void GraphTransferNodeInputInfo::set_node_id(int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeInputInfo.node_id)
}

// repeated .tensorflow.GraphTransferNodeInput node_input = 2;
inline int GraphTransferNodeInputInfo::_internal_node_input_size() const {
  return _impl_.node_input_.size();
}
inline int GraphTransferNodeInputInfo::node_input_size() const {
  return _internal_node_input_size();
}
inline void GraphTransferNodeInputInfo::clear_node_input() {
  _impl_.node_input_.Clear();
}
inline ::tensorflow::GraphTransferNodeInput* GraphTransferNodeInputInfo::mutable_node_input(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferNodeInputInfo.node_input)
  return _impl_.node_input_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput >*
GraphTransferNodeInputInfo::mutable_node_input() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferNodeInputInfo.node_input)
  return &_impl_.node_input_;
}
inline const ::tensorflow::GraphTransferNodeInput& GraphTransferNodeInputInfo::_internal_node_input(int index) const {
  return _impl_.node_input_.Get(index);
}
inline const ::tensorflow::GraphTransferNodeInput& GraphTransferNodeInputInfo::node_input(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeInputInfo.node_input)
  return _internal_node_input(index);
}
inline ::tensorflow::GraphTransferNodeInput* GraphTransferNodeInputInfo::_internal_add_node_input() {
  return _impl_.node_input_.Add();
}
inline ::tensorflow::GraphTransferNodeInput* GraphTransferNodeInputInfo::add_node_input() {
  ::tensorflow::GraphTransferNodeInput* _add = _internal_add_node_input();
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferNodeInputInfo.node_input)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInput >&
GraphTransferNodeInputInfo::node_input() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferNodeInputInfo.node_input)
  return _impl_.node_input_;
}

// -------------------------------------------------------------------

// GraphTransferNodeOutputInfo

// int32 node_id = 1;
inline void GraphTransferNodeOutputInfo::clear_node_id() {
  _impl_.node_id_ = 0;
}
inline int32_t GraphTransferNodeOutputInfo::_internal_node_id() const {
  return _impl_.node_id_;
}
inline int32_t GraphTransferNodeOutputInfo::node_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeOutputInfo.node_id)
  return _internal_node_id();
}
inline void GraphTransferNodeOutputInfo::_internal_set_node_id(int32_t value) {
  
  _impl_.node_id_ = value;
}
inline void GraphTransferNodeOutputInfo::set_node_id(int32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeOutputInfo.node_id)
}

// repeated int32 max_byte_size = 2;
inline int GraphTransferNodeOutputInfo::_internal_max_byte_size_size() const {
  return _impl_.max_byte_size_.size();
}
inline int GraphTransferNodeOutputInfo::max_byte_size_size() const {
  return _internal_max_byte_size_size();
}
inline void GraphTransferNodeOutputInfo::clear_max_byte_size() {
  _impl_.max_byte_size_.Clear();
}
inline int32_t GraphTransferNodeOutputInfo::_internal_max_byte_size(int index) const {
  return _impl_.max_byte_size_.Get(index);
}
inline int32_t GraphTransferNodeOutputInfo::max_byte_size(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
  return _internal_max_byte_size(index);
}
inline void GraphTransferNodeOutputInfo::set_max_byte_size(int index, int32_t value) {
  _impl_.max_byte_size_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
}
inline void GraphTransferNodeOutputInfo::_internal_add_max_byte_size(int32_t value) {
  _impl_.max_byte_size_.Add(value);
}
inline void GraphTransferNodeOutputInfo::add_max_byte_size(int32_t value) {
  _internal_add_max_byte_size(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GraphTransferNodeOutputInfo::_internal_max_byte_size() const {
  return _impl_.max_byte_size_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
GraphTransferNodeOutputInfo::max_byte_size() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
  return _internal_max_byte_size();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GraphTransferNodeOutputInfo::_internal_mutable_max_byte_size() {
  return &_impl_.max_byte_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
GraphTransferNodeOutputInfo::mutable_max_byte_size() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferNodeOutputInfo.max_byte_size)
  return _internal_mutable_max_byte_size();
}

// -------------------------------------------------------------------

// GraphTransferGraphInputNodeInfo

// string name = 1;
inline void GraphTransferGraphInputNodeInfo::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& GraphTransferGraphInputNodeInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphInputNodeInfo.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphTransferGraphInputNodeInfo::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphInputNodeInfo.name)
}
inline std::string* GraphTransferGraphInputNodeInfo::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferGraphInputNodeInfo.name)
  return _s;
}
inline const std::string& GraphTransferGraphInputNodeInfo::_internal_name() const {
  return _impl_.name_.Get();
}
inline void GraphTransferGraphInputNodeInfo::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphTransferGraphInputNodeInfo::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphTransferGraphInputNodeInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferGraphInputNodeInfo.name)
  return _impl_.name_.Release();
}
inline void GraphTransferGraphInputNodeInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferGraphInputNodeInfo.name)
}

// repeated int64 shape = 2;
inline int GraphTransferGraphInputNodeInfo::_internal_shape_size() const {
  return _impl_.shape_.size();
}
inline int GraphTransferGraphInputNodeInfo::shape_size() const {
  return _internal_shape_size();
}
inline void GraphTransferGraphInputNodeInfo::clear_shape() {
  _impl_.shape_.Clear();
}
inline int64_t GraphTransferGraphInputNodeInfo::_internal_shape(int index) const {
  return _impl_.shape_.Get(index);
}
inline int64_t GraphTransferGraphInputNodeInfo::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphInputNodeInfo.shape)
  return _internal_shape(index);
}
inline void GraphTransferGraphInputNodeInfo::set_shape(int index, int64_t value) {
  _impl_.shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphInputNodeInfo.shape)
}
inline void GraphTransferGraphInputNodeInfo::_internal_add_shape(int64_t value) {
  _impl_.shape_.Add(value);
}
inline void GraphTransferGraphInputNodeInfo::add_shape(int64_t value) {
  _internal_add_shape(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferGraphInputNodeInfo.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
GraphTransferGraphInputNodeInfo::_internal_shape() const {
  return _impl_.shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
GraphTransferGraphInputNodeInfo::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferGraphInputNodeInfo.shape)
  return _internal_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
GraphTransferGraphInputNodeInfo::_internal_mutable_shape() {
  return &_impl_.shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
GraphTransferGraphInputNodeInfo::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferGraphInputNodeInfo.shape)
  return _internal_mutable_shape();
}

// .tensorflow.DataType dtype = 3;
inline void GraphTransferGraphInputNodeInfo::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType GraphTransferGraphInputNodeInfo::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType GraphTransferGraphInputNodeInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphInputNodeInfo.dtype)
  return _internal_dtype();
}
inline void GraphTransferGraphInputNodeInfo::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void GraphTransferGraphInputNodeInfo::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphInputNodeInfo.dtype)
}

// -------------------------------------------------------------------

// GraphTransferGraphOutputNodeInfo

// string name = 1;
inline void GraphTransferGraphOutputNodeInfo::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& GraphTransferGraphOutputNodeInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphOutputNodeInfo.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphTransferGraphOutputNodeInfo::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphOutputNodeInfo.name)
}
inline std::string* GraphTransferGraphOutputNodeInfo::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferGraphOutputNodeInfo.name)
  return _s;
}
inline const std::string& GraphTransferGraphOutputNodeInfo::_internal_name() const {
  return _impl_.name_.Get();
}
inline void GraphTransferGraphOutputNodeInfo::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphTransferGraphOutputNodeInfo::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphTransferGraphOutputNodeInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.GraphTransferGraphOutputNodeInfo.name)
  return _impl_.name_.Release();
}
inline void GraphTransferGraphOutputNodeInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GraphTransferGraphOutputNodeInfo.name)
}

// repeated int64 shape = 2;
inline int GraphTransferGraphOutputNodeInfo::_internal_shape_size() const {
  return _impl_.shape_.size();
}
inline int GraphTransferGraphOutputNodeInfo::shape_size() const {
  return _internal_shape_size();
}
inline void GraphTransferGraphOutputNodeInfo::clear_shape() {
  _impl_.shape_.Clear();
}
inline int64_t GraphTransferGraphOutputNodeInfo::_internal_shape(int index) const {
  return _impl_.shape_.Get(index);
}
inline int64_t GraphTransferGraphOutputNodeInfo::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
  return _internal_shape(index);
}
inline void GraphTransferGraphOutputNodeInfo::set_shape(int index, int64_t value) {
  _impl_.shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
}
inline void GraphTransferGraphOutputNodeInfo::_internal_add_shape(int64_t value) {
  _impl_.shape_.Add(value);
}
inline void GraphTransferGraphOutputNodeInfo::add_shape(int64_t value) {
  _internal_add_shape(value);
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
GraphTransferGraphOutputNodeInfo::_internal_shape() const {
  return _impl_.shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
GraphTransferGraphOutputNodeInfo::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
  return _internal_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
GraphTransferGraphOutputNodeInfo::_internal_mutable_shape() {
  return &_impl_.shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
GraphTransferGraphOutputNodeInfo::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferGraphOutputNodeInfo.shape)
  return _internal_mutable_shape();
}

// .tensorflow.DataType dtype = 3;
inline void GraphTransferGraphOutputNodeInfo::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType GraphTransferGraphOutputNodeInfo::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType GraphTransferGraphOutputNodeInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferGraphOutputNodeInfo.dtype)
  return _internal_dtype();
}
inline void GraphTransferGraphOutputNodeInfo::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void GraphTransferGraphOutputNodeInfo::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferGraphOutputNodeInfo.dtype)
}

// -------------------------------------------------------------------

// GraphTransferInfo

// repeated .tensorflow.GraphTransferNodeInfo node_info = 1;
inline int GraphTransferInfo::_internal_node_info_size() const {
  return _impl_.node_info_.size();
}
inline int GraphTransferInfo::node_info_size() const {
  return _internal_node_info_size();
}
inline void GraphTransferInfo::clear_node_info() {
  _impl_.node_info_.Clear();
}
inline ::tensorflow::GraphTransferNodeInfo* GraphTransferInfo::mutable_node_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.node_info)
  return _impl_.node_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo >*
GraphTransferInfo::mutable_node_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.node_info)
  return &_impl_.node_info_;
}
inline const ::tensorflow::GraphTransferNodeInfo& GraphTransferInfo::_internal_node_info(int index) const {
  return _impl_.node_info_.Get(index);
}
inline const ::tensorflow::GraphTransferNodeInfo& GraphTransferInfo::node_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.node_info)
  return _internal_node_info(index);
}
inline ::tensorflow::GraphTransferNodeInfo* GraphTransferInfo::_internal_add_node_info() {
  return _impl_.node_info_.Add();
}
inline ::tensorflow::GraphTransferNodeInfo* GraphTransferInfo::add_node_info() {
  ::tensorflow::GraphTransferNodeInfo* _add = _internal_add_node_info();
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.node_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInfo >&
GraphTransferInfo::node_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.node_info)
  return _impl_.node_info_;
}

// repeated .tensorflow.GraphTransferConstNodeInfo const_node_info = 2;
inline int GraphTransferInfo::_internal_const_node_info_size() const {
  return _impl_.const_node_info_.size();
}
inline int GraphTransferInfo::const_node_info_size() const {
  return _internal_const_node_info_size();
}
inline void GraphTransferInfo::clear_const_node_info() {
  _impl_.const_node_info_.Clear();
}
inline ::tensorflow::GraphTransferConstNodeInfo* GraphTransferInfo::mutable_const_node_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.const_node_info)
  return _impl_.const_node_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo >*
GraphTransferInfo::mutable_const_node_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.const_node_info)
  return &_impl_.const_node_info_;
}
inline const ::tensorflow::GraphTransferConstNodeInfo& GraphTransferInfo::_internal_const_node_info(int index) const {
  return _impl_.const_node_info_.Get(index);
}
inline const ::tensorflow::GraphTransferConstNodeInfo& GraphTransferInfo::const_node_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.const_node_info)
  return _internal_const_node_info(index);
}
inline ::tensorflow::GraphTransferConstNodeInfo* GraphTransferInfo::_internal_add_const_node_info() {
  return _impl_.const_node_info_.Add();
}
inline ::tensorflow::GraphTransferConstNodeInfo* GraphTransferInfo::add_const_node_info() {
  ::tensorflow::GraphTransferConstNodeInfo* _add = _internal_add_const_node_info();
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.const_node_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferConstNodeInfo >&
GraphTransferInfo::const_node_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.const_node_info)
  return _impl_.const_node_info_;
}

// repeated .tensorflow.GraphTransferNodeInputInfo node_input_info = 3;
inline int GraphTransferInfo::_internal_node_input_info_size() const {
  return _impl_.node_input_info_.size();
}
inline int GraphTransferInfo::node_input_info_size() const {
  return _internal_node_input_info_size();
}
inline void GraphTransferInfo::clear_node_input_info() {
  _impl_.node_input_info_.Clear();
}
inline ::tensorflow::GraphTransferNodeInputInfo* GraphTransferInfo::mutable_node_input_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.node_input_info)
  return _impl_.node_input_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo >*
GraphTransferInfo::mutable_node_input_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.node_input_info)
  return &_impl_.node_input_info_;
}
inline const ::tensorflow::GraphTransferNodeInputInfo& GraphTransferInfo::_internal_node_input_info(int index) const {
  return _impl_.node_input_info_.Get(index);
}
inline const ::tensorflow::GraphTransferNodeInputInfo& GraphTransferInfo::node_input_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.node_input_info)
  return _internal_node_input_info(index);
}
inline ::tensorflow::GraphTransferNodeInputInfo* GraphTransferInfo::_internal_add_node_input_info() {
  return _impl_.node_input_info_.Add();
}
inline ::tensorflow::GraphTransferNodeInputInfo* GraphTransferInfo::add_node_input_info() {
  ::tensorflow::GraphTransferNodeInputInfo* _add = _internal_add_node_input_info();
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.node_input_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeInputInfo >&
GraphTransferInfo::node_input_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.node_input_info)
  return _impl_.node_input_info_;
}

// repeated .tensorflow.GraphTransferNodeOutputInfo node_output_info = 4;
inline int GraphTransferInfo::_internal_node_output_info_size() const {
  return _impl_.node_output_info_.size();
}
inline int GraphTransferInfo::node_output_info_size() const {
  return _internal_node_output_info_size();
}
inline void GraphTransferInfo::clear_node_output_info() {
  _impl_.node_output_info_.Clear();
}
inline ::tensorflow::GraphTransferNodeOutputInfo* GraphTransferInfo::mutable_node_output_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.node_output_info)
  return _impl_.node_output_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo >*
GraphTransferInfo::mutable_node_output_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.node_output_info)
  return &_impl_.node_output_info_;
}
inline const ::tensorflow::GraphTransferNodeOutputInfo& GraphTransferInfo::_internal_node_output_info(int index) const {
  return _impl_.node_output_info_.Get(index);
}
inline const ::tensorflow::GraphTransferNodeOutputInfo& GraphTransferInfo::node_output_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.node_output_info)
  return _internal_node_output_info(index);
}
inline ::tensorflow::GraphTransferNodeOutputInfo* GraphTransferInfo::_internal_add_node_output_info() {
  return _impl_.node_output_info_.Add();
}
inline ::tensorflow::GraphTransferNodeOutputInfo* GraphTransferInfo::add_node_output_info() {
  ::tensorflow::GraphTransferNodeOutputInfo* _add = _internal_add_node_output_info();
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.node_output_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferNodeOutputInfo >&
GraphTransferInfo::node_output_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.node_output_info)
  return _impl_.node_output_info_;
}

// repeated .tensorflow.GraphTransferGraphInputNodeInfo graph_input_node_info = 5;
inline int GraphTransferInfo::_internal_graph_input_node_info_size() const {
  return _impl_.graph_input_node_info_.size();
}
inline int GraphTransferInfo::graph_input_node_info_size() const {
  return _internal_graph_input_node_info_size();
}
inline void GraphTransferInfo::clear_graph_input_node_info() {
  _impl_.graph_input_node_info_.Clear();
}
inline ::tensorflow::GraphTransferGraphInputNodeInfo* GraphTransferInfo::mutable_graph_input_node_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.graph_input_node_info)
  return _impl_.graph_input_node_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo >*
GraphTransferInfo::mutable_graph_input_node_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.graph_input_node_info)
  return &_impl_.graph_input_node_info_;
}
inline const ::tensorflow::GraphTransferGraphInputNodeInfo& GraphTransferInfo::_internal_graph_input_node_info(int index) const {
  return _impl_.graph_input_node_info_.Get(index);
}
inline const ::tensorflow::GraphTransferGraphInputNodeInfo& GraphTransferInfo::graph_input_node_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.graph_input_node_info)
  return _internal_graph_input_node_info(index);
}
inline ::tensorflow::GraphTransferGraphInputNodeInfo* GraphTransferInfo::_internal_add_graph_input_node_info() {
  return _impl_.graph_input_node_info_.Add();
}
inline ::tensorflow::GraphTransferGraphInputNodeInfo* GraphTransferInfo::add_graph_input_node_info() {
  ::tensorflow::GraphTransferGraphInputNodeInfo* _add = _internal_add_graph_input_node_info();
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.graph_input_node_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphInputNodeInfo >&
GraphTransferInfo::graph_input_node_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.graph_input_node_info)
  return _impl_.graph_input_node_info_;
}

// repeated .tensorflow.GraphTransferGraphOutputNodeInfo graph_output_node_info = 6;
inline int GraphTransferInfo::_internal_graph_output_node_info_size() const {
  return _impl_.graph_output_node_info_.size();
}
inline int GraphTransferInfo::graph_output_node_info_size() const {
  return _internal_graph_output_node_info_size();
}
inline void GraphTransferInfo::clear_graph_output_node_info() {
  _impl_.graph_output_node_info_.Clear();
}
inline ::tensorflow::GraphTransferGraphOutputNodeInfo* GraphTransferInfo::mutable_graph_output_node_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GraphTransferInfo.graph_output_node_info)
  return _impl_.graph_output_node_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo >*
GraphTransferInfo::mutable_graph_output_node_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GraphTransferInfo.graph_output_node_info)
  return &_impl_.graph_output_node_info_;
}
inline const ::tensorflow::GraphTransferGraphOutputNodeInfo& GraphTransferInfo::_internal_graph_output_node_info(int index) const {
  return _impl_.graph_output_node_info_.Get(index);
}
inline const ::tensorflow::GraphTransferGraphOutputNodeInfo& GraphTransferInfo::graph_output_node_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.graph_output_node_info)
  return _internal_graph_output_node_info(index);
}
inline ::tensorflow::GraphTransferGraphOutputNodeInfo* GraphTransferInfo::_internal_add_graph_output_node_info() {
  return _impl_.graph_output_node_info_.Add();
}
inline ::tensorflow::GraphTransferGraphOutputNodeInfo* GraphTransferInfo::add_graph_output_node_info() {
  ::tensorflow::GraphTransferGraphOutputNodeInfo* _add = _internal_add_graph_output_node_info();
  // @@protoc_insertion_point(field_add:tensorflow.GraphTransferInfo.graph_output_node_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphTransferGraphOutputNodeInfo >&
GraphTransferInfo::graph_output_node_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.GraphTransferInfo.graph_output_node_info)
  return _impl_.graph_output_node_info_;
}

// .tensorflow.GraphTransferInfo.Destination destination = 7;
inline void GraphTransferInfo::clear_destination() {
  _impl_.destination_ = 0;
}
inline ::tensorflow::GraphTransferInfo_Destination GraphTransferInfo::_internal_destination() const {
  return static_cast< ::tensorflow::GraphTransferInfo_Destination >(_impl_.destination_);
}
inline ::tensorflow::GraphTransferInfo_Destination GraphTransferInfo::destination() const {
  // @@protoc_insertion_point(field_get:tensorflow.GraphTransferInfo.destination)
  return _internal_destination();
}
inline void GraphTransferInfo::_internal_set_destination(::tensorflow::GraphTransferInfo_Destination value) {
  
  _impl_.destination_ = value;
}
inline void GraphTransferInfo::set_destination(::tensorflow::GraphTransferInfo_Destination value) {
  _internal_set_destination(value);
  // @@protoc_insertion_point(field_set:tensorflow.GraphTransferInfo.destination)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::GraphTransferInfo_Destination> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::GraphTransferInfo_Destination>() {
  return ::tensorflow::GraphTransferInfo_Destination_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fgraph_5ftransfer_5finfo_2eproto
