// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/control_flow.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto;
namespace tensorflow {
class CondContextDef;
struct CondContextDefDefaultTypeInternal;
extern CondContextDefDefaultTypeInternal _CondContextDef_default_instance_;
class ControlFlowContextDef;
struct ControlFlowContextDefDefaultTypeInternal;
extern ControlFlowContextDefDefaultTypeInternal _ControlFlowContextDef_default_instance_;
class ValuesDef;
struct ValuesDefDefaultTypeInternal;
extern ValuesDefDefaultTypeInternal _ValuesDef_default_instance_;
class ValuesDef_ExternalValuesEntry_DoNotUse;
struct ValuesDef_ExternalValuesEntry_DoNotUseDefaultTypeInternal;
extern ValuesDef_ExternalValuesEntry_DoNotUseDefaultTypeInternal _ValuesDef_ExternalValuesEntry_DoNotUse_default_instance_;
class WhileContextDef;
struct WhileContextDefDefaultTypeInternal;
extern WhileContextDefDefaultTypeInternal _WhileContextDef_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CondContextDef* Arena::CreateMaybeMessage<::tensorflow::CondContextDef>(Arena*);
template<> ::tensorflow::ControlFlowContextDef* Arena::CreateMaybeMessage<::tensorflow::ControlFlowContextDef>(Arena*);
template<> ::tensorflow::ValuesDef* Arena::CreateMaybeMessage<::tensorflow::ValuesDef>(Arena*);
template<> ::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ValuesDef_ExternalValuesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::WhileContextDef* Arena::CreateMaybeMessage<::tensorflow::WhileContextDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class ValuesDef_ExternalValuesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ValuesDef_ExternalValuesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ValuesDef_ExternalValuesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  ValuesDef_ExternalValuesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ValuesDef_ExternalValuesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ValuesDef_ExternalValuesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ValuesDef_ExternalValuesEntry_DoNotUse& other);
  static const ValuesDef_ExternalValuesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ValuesDef_ExternalValuesEntry_DoNotUse*>(&_ValuesDef_ExternalValuesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ValuesDef.ExternalValuesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ValuesDef.ExternalValuesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto;
};

// -------------------------------------------------------------------

class ValuesDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ValuesDef) */ {
 public:
  inline ValuesDef() : ValuesDef(nullptr) {}
  ~ValuesDef() override;
  explicit PROTOBUF_CONSTEXPR ValuesDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ValuesDef(const ValuesDef& from);
  ValuesDef(ValuesDef&& from) noexcept
    : ValuesDef() {
    *this = ::std::move(from);
  }

  inline ValuesDef& operator=(const ValuesDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline ValuesDef& operator=(ValuesDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ValuesDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const ValuesDef* internal_default_instance() {
    return reinterpret_cast<const ValuesDef*>(
               &_ValuesDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ValuesDef& a, ValuesDef& b) {
    a.Swap(&b);
  }
  inline void Swap(ValuesDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ValuesDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ValuesDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ValuesDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ValuesDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ValuesDef& from) {
    ValuesDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ValuesDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ValuesDef";
  }
  protected:
  explicit ValuesDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kValuesFieldNumber = 1,
    kExternalValuesFieldNumber = 2,
  };
  // repeated string values = 1;
  int values_size() const;
  private:
  int _internal_values_size() const;
  public:
  void clear_values();
  const std::string& values(int index) const;
  std::string* mutable_values(int index);
  void set_values(int index, const std::string& value);
  void set_values(int index, std::string&& value);
  void set_values(int index, const char* value);
  void set_values(int index, const char* value, size_t size);
  std::string* add_values();
  void add_values(const std::string& value);
  void add_values(std::string&& value);
  void add_values(const char* value);
  void add_values(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& values() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_values();
  private:
  const std::string& _internal_values(int index) const;
  std::string* _internal_add_values();
  public:

  // map<string, string> external_values = 2;
  int external_values_size() const;
  private:
  int _internal_external_values_size() const;
  public:
  void clear_external_values();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_external_values() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_external_values();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      external_values() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_external_values();

  // @@protoc_insertion_point(class_scope:tensorflow.ValuesDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> values_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ValuesDef_ExternalValuesEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> external_values_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto;
};
// -------------------------------------------------------------------

class ControlFlowContextDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ControlFlowContextDef) */ {
 public:
  inline ControlFlowContextDef() : ControlFlowContextDef(nullptr) {}
  ~ControlFlowContextDef() override;
  explicit PROTOBUF_CONSTEXPR ControlFlowContextDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ControlFlowContextDef(const ControlFlowContextDef& from);
  ControlFlowContextDef(ControlFlowContextDef&& from) noexcept
    : ControlFlowContextDef() {
    *this = ::std::move(from);
  }

  inline ControlFlowContextDef& operator=(const ControlFlowContextDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline ControlFlowContextDef& operator=(ControlFlowContextDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ControlFlowContextDef& default_instance() {
    return *internal_default_instance();
  }
  enum CtxtCase {
    kCondCtxt = 1,
    kWhileCtxt = 2,
    CTXT_NOT_SET = 0,
  };

  static inline const ControlFlowContextDef* internal_default_instance() {
    return reinterpret_cast<const ControlFlowContextDef*>(
               &_ControlFlowContextDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ControlFlowContextDef& a, ControlFlowContextDef& b) {
    a.Swap(&b);
  }
  inline void Swap(ControlFlowContextDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ControlFlowContextDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ControlFlowContextDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ControlFlowContextDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ControlFlowContextDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ControlFlowContextDef& from) {
    ControlFlowContextDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ControlFlowContextDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ControlFlowContextDef";
  }
  protected:
  explicit ControlFlowContextDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCondCtxtFieldNumber = 1,
    kWhileCtxtFieldNumber = 2,
  };
  // .tensorflow.CondContextDef cond_ctxt = 1;
  bool has_cond_ctxt() const;
  private:
  bool _internal_has_cond_ctxt() const;
  public:
  void clear_cond_ctxt();
  const ::tensorflow::CondContextDef& cond_ctxt() const;
  PROTOBUF_NODISCARD ::tensorflow::CondContextDef* release_cond_ctxt();
  ::tensorflow::CondContextDef* mutable_cond_ctxt();
  void set_allocated_cond_ctxt(::tensorflow::CondContextDef* cond_ctxt);
  private:
  const ::tensorflow::CondContextDef& _internal_cond_ctxt() const;
  ::tensorflow::CondContextDef* _internal_mutable_cond_ctxt();
  public:
  void unsafe_arena_set_allocated_cond_ctxt(
      ::tensorflow::CondContextDef* cond_ctxt);
  ::tensorflow::CondContextDef* unsafe_arena_release_cond_ctxt();

  // .tensorflow.WhileContextDef while_ctxt = 2;
  bool has_while_ctxt() const;
  private:
  bool _internal_has_while_ctxt() const;
  public:
  void clear_while_ctxt();
  const ::tensorflow::WhileContextDef& while_ctxt() const;
  PROTOBUF_NODISCARD ::tensorflow::WhileContextDef* release_while_ctxt();
  ::tensorflow::WhileContextDef* mutable_while_ctxt();
  void set_allocated_while_ctxt(::tensorflow::WhileContextDef* while_ctxt);
  private:
  const ::tensorflow::WhileContextDef& _internal_while_ctxt() const;
  ::tensorflow::WhileContextDef* _internal_mutable_while_ctxt();
  public:
  void unsafe_arena_set_allocated_while_ctxt(
      ::tensorflow::WhileContextDef* while_ctxt);
  ::tensorflow::WhileContextDef* unsafe_arena_release_while_ctxt();

  void clear_ctxt();
  CtxtCase ctxt_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.ControlFlowContextDef)
 private:
  class _Internal;
  void set_has_cond_ctxt();
  void set_has_while_ctxt();

  inline bool has_ctxt() const;
  inline void clear_has_ctxt();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union CtxtUnion {
      constexpr CtxtUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::CondContextDef* cond_ctxt_;
      ::tensorflow::WhileContextDef* while_ctxt_;
    } ctxt_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto;
};
// -------------------------------------------------------------------

class CondContextDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CondContextDef) */ {
 public:
  inline CondContextDef() : CondContextDef(nullptr) {}
  ~CondContextDef() override;
  explicit PROTOBUF_CONSTEXPR CondContextDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CondContextDef(const CondContextDef& from);
  CondContextDef(CondContextDef&& from) noexcept
    : CondContextDef() {
    *this = ::std::move(from);
  }

  inline CondContextDef& operator=(const CondContextDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline CondContextDef& operator=(CondContextDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CondContextDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const CondContextDef* internal_default_instance() {
    return reinterpret_cast<const CondContextDef*>(
               &_CondContextDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CondContextDef& a, CondContextDef& b) {
    a.Swap(&b);
  }
  inline void Swap(CondContextDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CondContextDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CondContextDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CondContextDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CondContextDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CondContextDef& from) {
    CondContextDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CondContextDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CondContextDef";
  }
  protected:
  explicit CondContextDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNestedContextsFieldNumber = 6,
    kContextNameFieldNumber = 1,
    kPredNameFieldNumber = 2,
    kPivotNameFieldNumber = 3,
    kValuesDefFieldNumber = 5,
    kBranchFieldNumber = 4,
  };
  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 6;
  int nested_contexts_size() const;
  private:
  int _internal_nested_contexts_size() const;
  public:
  void clear_nested_contexts();
  ::tensorflow::ControlFlowContextDef* mutable_nested_contexts(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >*
      mutable_nested_contexts();
  private:
  const ::tensorflow::ControlFlowContextDef& _internal_nested_contexts(int index) const;
  ::tensorflow::ControlFlowContextDef* _internal_add_nested_contexts();
  public:
  const ::tensorflow::ControlFlowContextDef& nested_contexts(int index) const;
  ::tensorflow::ControlFlowContextDef* add_nested_contexts();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >&
      nested_contexts() const;

  // string context_name = 1;
  void clear_context_name();
  const std::string& context_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_context_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_context_name();
  PROTOBUF_NODISCARD std::string* release_context_name();
  void set_allocated_context_name(std::string* context_name);
  private:
  const std::string& _internal_context_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_context_name(const std::string& value);
  std::string* _internal_mutable_context_name();
  public:

  // string pred_name = 2;
  void clear_pred_name();
  const std::string& pred_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pred_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pred_name();
  PROTOBUF_NODISCARD std::string* release_pred_name();
  void set_allocated_pred_name(std::string* pred_name);
  private:
  const std::string& _internal_pred_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pred_name(const std::string& value);
  std::string* _internal_mutable_pred_name();
  public:

  // string pivot_name = 3;
  void clear_pivot_name();
  const std::string& pivot_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pivot_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pivot_name();
  PROTOBUF_NODISCARD std::string* release_pivot_name();
  void set_allocated_pivot_name(std::string* pivot_name);
  private:
  const std::string& _internal_pivot_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pivot_name(const std::string& value);
  std::string* _internal_mutable_pivot_name();
  public:

  // .tensorflow.ValuesDef values_def = 5;
  bool has_values_def() const;
  private:
  bool _internal_has_values_def() const;
  public:
  void clear_values_def();
  const ::tensorflow::ValuesDef& values_def() const;
  PROTOBUF_NODISCARD ::tensorflow::ValuesDef* release_values_def();
  ::tensorflow::ValuesDef* mutable_values_def();
  void set_allocated_values_def(::tensorflow::ValuesDef* values_def);
  private:
  const ::tensorflow::ValuesDef& _internal_values_def() const;
  ::tensorflow::ValuesDef* _internal_mutable_values_def();
  public:
  void unsafe_arena_set_allocated_values_def(
      ::tensorflow::ValuesDef* values_def);
  ::tensorflow::ValuesDef* unsafe_arena_release_values_def();

  // int32 branch = 4;
  void clear_branch();
  int32_t branch() const;
  void set_branch(int32_t value);
  private:
  int32_t _internal_branch() const;
  void _internal_set_branch(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CondContextDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef > nested_contexts_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr context_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pred_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pivot_name_;
    ::tensorflow::ValuesDef* values_def_;
    int32_t branch_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto;
};
// -------------------------------------------------------------------

class WhileContextDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.WhileContextDef) */ {
 public:
  inline WhileContextDef() : WhileContextDef(nullptr) {}
  ~WhileContextDef() override;
  explicit PROTOBUF_CONSTEXPR WhileContextDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WhileContextDef(const WhileContextDef& from);
  WhileContextDef(WhileContextDef&& from) noexcept
    : WhileContextDef() {
    *this = ::std::move(from);
  }

  inline WhileContextDef& operator=(const WhileContextDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline WhileContextDef& operator=(WhileContextDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WhileContextDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const WhileContextDef* internal_default_instance() {
    return reinterpret_cast<const WhileContextDef*>(
               &_WhileContextDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(WhileContextDef& a, WhileContextDef& b) {
    a.Swap(&b);
  }
  inline void Swap(WhileContextDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WhileContextDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WhileContextDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WhileContextDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WhileContextDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WhileContextDef& from) {
    WhileContextDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WhileContextDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.WhileContextDef";
  }
  protected:
  explicit WhileContextDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLoopExitNamesFieldNumber = 8,
    kLoopEnterNamesFieldNumber = 10,
    kNestedContextsFieldNumber = 12,
    kContextNameFieldNumber = 1,
    kPivotNameFieldNumber = 5,
    kPivotForPredNameFieldNumber = 6,
    kPivotForBodyNameFieldNumber = 7,
    kMaximumIterationsNameFieldNumber = 11,
    kValuesDefFieldNumber = 9,
    kParallelIterationsFieldNumber = 2,
    kBackPropFieldNumber = 3,
    kSwapMemoryFieldNumber = 4,
  };
  // repeated string loop_exit_names = 8;
  int loop_exit_names_size() const;
  private:
  int _internal_loop_exit_names_size() const;
  public:
  void clear_loop_exit_names();
  const std::string& loop_exit_names(int index) const;
  std::string* mutable_loop_exit_names(int index);
  void set_loop_exit_names(int index, const std::string& value);
  void set_loop_exit_names(int index, std::string&& value);
  void set_loop_exit_names(int index, const char* value);
  void set_loop_exit_names(int index, const char* value, size_t size);
  std::string* add_loop_exit_names();
  void add_loop_exit_names(const std::string& value);
  void add_loop_exit_names(std::string&& value);
  void add_loop_exit_names(const char* value);
  void add_loop_exit_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& loop_exit_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_loop_exit_names();
  private:
  const std::string& _internal_loop_exit_names(int index) const;
  std::string* _internal_add_loop_exit_names();
  public:

  // repeated string loop_enter_names = 10;
  int loop_enter_names_size() const;
  private:
  int _internal_loop_enter_names_size() const;
  public:
  void clear_loop_enter_names();
  const std::string& loop_enter_names(int index) const;
  std::string* mutable_loop_enter_names(int index);
  void set_loop_enter_names(int index, const std::string& value);
  void set_loop_enter_names(int index, std::string&& value);
  void set_loop_enter_names(int index, const char* value);
  void set_loop_enter_names(int index, const char* value, size_t size);
  std::string* add_loop_enter_names();
  void add_loop_enter_names(const std::string& value);
  void add_loop_enter_names(std::string&& value);
  void add_loop_enter_names(const char* value);
  void add_loop_enter_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& loop_enter_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_loop_enter_names();
  private:
  const std::string& _internal_loop_enter_names(int index) const;
  std::string* _internal_add_loop_enter_names();
  public:

  // repeated .tensorflow.ControlFlowContextDef nested_contexts = 12;
  int nested_contexts_size() const;
  private:
  int _internal_nested_contexts_size() const;
  public:
  void clear_nested_contexts();
  ::tensorflow::ControlFlowContextDef* mutable_nested_contexts(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >*
      mutable_nested_contexts();
  private:
  const ::tensorflow::ControlFlowContextDef& _internal_nested_contexts(int index) const;
  ::tensorflow::ControlFlowContextDef* _internal_add_nested_contexts();
  public:
  const ::tensorflow::ControlFlowContextDef& nested_contexts(int index) const;
  ::tensorflow::ControlFlowContextDef* add_nested_contexts();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >&
      nested_contexts() const;

  // string context_name = 1;
  void clear_context_name();
  const std::string& context_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_context_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_context_name();
  PROTOBUF_NODISCARD std::string* release_context_name();
  void set_allocated_context_name(std::string* context_name);
  private:
  const std::string& _internal_context_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_context_name(const std::string& value);
  std::string* _internal_mutable_context_name();
  public:

  // string pivot_name = 5;
  void clear_pivot_name();
  const std::string& pivot_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pivot_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pivot_name();
  PROTOBUF_NODISCARD std::string* release_pivot_name();
  void set_allocated_pivot_name(std::string* pivot_name);
  private:
  const std::string& _internal_pivot_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pivot_name(const std::string& value);
  std::string* _internal_mutable_pivot_name();
  public:

  // string pivot_for_pred_name = 6;
  void clear_pivot_for_pred_name();
  const std::string& pivot_for_pred_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pivot_for_pred_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pivot_for_pred_name();
  PROTOBUF_NODISCARD std::string* release_pivot_for_pred_name();
  void set_allocated_pivot_for_pred_name(std::string* pivot_for_pred_name);
  private:
  const std::string& _internal_pivot_for_pred_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pivot_for_pred_name(const std::string& value);
  std::string* _internal_mutable_pivot_for_pred_name();
  public:

  // string pivot_for_body_name = 7;
  void clear_pivot_for_body_name();
  const std::string& pivot_for_body_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_pivot_for_body_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_pivot_for_body_name();
  PROTOBUF_NODISCARD std::string* release_pivot_for_body_name();
  void set_allocated_pivot_for_body_name(std::string* pivot_for_body_name);
  private:
  const std::string& _internal_pivot_for_body_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_pivot_for_body_name(const std::string& value);
  std::string* _internal_mutable_pivot_for_body_name();
  public:

  // string maximum_iterations_name = 11;
  void clear_maximum_iterations_name();
  const std::string& maximum_iterations_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_maximum_iterations_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_maximum_iterations_name();
  PROTOBUF_NODISCARD std::string* release_maximum_iterations_name();
  void set_allocated_maximum_iterations_name(std::string* maximum_iterations_name);
  private:
  const std::string& _internal_maximum_iterations_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_maximum_iterations_name(const std::string& value);
  std::string* _internal_mutable_maximum_iterations_name();
  public:

  // .tensorflow.ValuesDef values_def = 9;
  bool has_values_def() const;
  private:
  bool _internal_has_values_def() const;
  public:
  void clear_values_def();
  const ::tensorflow::ValuesDef& values_def() const;
  PROTOBUF_NODISCARD ::tensorflow::ValuesDef* release_values_def();
  ::tensorflow::ValuesDef* mutable_values_def();
  void set_allocated_values_def(::tensorflow::ValuesDef* values_def);
  private:
  const ::tensorflow::ValuesDef& _internal_values_def() const;
  ::tensorflow::ValuesDef* _internal_mutable_values_def();
  public:
  void unsafe_arena_set_allocated_values_def(
      ::tensorflow::ValuesDef* values_def);
  ::tensorflow::ValuesDef* unsafe_arena_release_values_def();

  // int32 parallel_iterations = 2;
  void clear_parallel_iterations();
  int32_t parallel_iterations() const;
  void set_parallel_iterations(int32_t value);
  private:
  int32_t _internal_parallel_iterations() const;
  void _internal_set_parallel_iterations(int32_t value);
  public:

  // bool back_prop = 3;
  void clear_back_prop();
  bool back_prop() const;
  void set_back_prop(bool value);
  private:
  bool _internal_back_prop() const;
  void _internal_set_back_prop(bool value);
  public:

  // bool swap_memory = 4;
  void clear_swap_memory();
  bool swap_memory() const;
  void set_swap_memory(bool value);
  private:
  bool _internal_swap_memory() const;
  void _internal_set_swap_memory(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.WhileContextDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> loop_exit_names_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> loop_enter_names_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef > nested_contexts_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr context_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pivot_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pivot_for_pred_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr pivot_for_body_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr maximum_iterations_name_;
    ::tensorflow::ValuesDef* values_def_;
    int32_t parallel_iterations_;
    bool back_prop_;
    bool swap_memory_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// ValuesDef

// repeated string values = 1;
inline int ValuesDef::_internal_values_size() const {
  return _impl_.values_.size();
}
inline int ValuesDef::values_size() const {
  return _internal_values_size();
}
inline void ValuesDef::clear_values() {
  _impl_.values_.Clear();
}
inline std::string* ValuesDef::add_values() {
  std::string* _s = _internal_add_values();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ValuesDef.values)
  return _s;
}
inline const std::string& ValuesDef::_internal_values(int index) const {
  return _impl_.values_.Get(index);
}
inline const std::string& ValuesDef::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ValuesDef.values)
  return _internal_values(index);
}
inline std::string* ValuesDef::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ValuesDef.values)
  return _impl_.values_.Mutable(index);
}
inline void ValuesDef::set_values(int index, const std::string& value) {
  _impl_.values_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.ValuesDef.values)
}
inline void ValuesDef::set_values(int index, std::string&& value) {
  _impl_.values_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.ValuesDef.values)
}
inline void ValuesDef::set_values(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.values_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ValuesDef.values)
}
inline void ValuesDef::set_values(int index, const char* value, size_t size) {
  _impl_.values_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ValuesDef.values)
}
inline std::string* ValuesDef::_internal_add_values() {
  return _impl_.values_.Add();
}
inline void ValuesDef::add_values(const std::string& value) {
  _impl_.values_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ValuesDef.values)
}
inline void ValuesDef::add_values(std::string&& value) {
  _impl_.values_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ValuesDef.values)
}
inline void ValuesDef::add_values(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.values_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ValuesDef.values)
}
inline void ValuesDef::add_values(const char* value, size_t size) {
  _impl_.values_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ValuesDef.values)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ValuesDef::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.ValuesDef.values)
  return _impl_.values_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ValuesDef::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ValuesDef.values)
  return &_impl_.values_;
}

// map<string, string> external_values = 2;
inline int ValuesDef::_internal_external_values_size() const {
  return _impl_.external_values_.size();
}
inline int ValuesDef::external_values_size() const {
  return _internal_external_values_size();
}
inline void ValuesDef::clear_external_values() {
  _impl_.external_values_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
ValuesDef::_internal_external_values() const {
  return _impl_.external_values_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
ValuesDef::external_values() const {
  // @@protoc_insertion_point(field_map:tensorflow.ValuesDef.external_values)
  return _internal_external_values();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
ValuesDef::_internal_mutable_external_values() {
  return _impl_.external_values_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
ValuesDef::mutable_external_values() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ValuesDef.external_values)
  return _internal_mutable_external_values();
}

// -------------------------------------------------------------------

// ControlFlowContextDef

// .tensorflow.CondContextDef cond_ctxt = 1;
inline bool ControlFlowContextDef::_internal_has_cond_ctxt() const {
  return ctxt_case() == kCondCtxt;
}
inline bool ControlFlowContextDef::has_cond_ctxt() const {
  return _internal_has_cond_ctxt();
}
inline void ControlFlowContextDef::set_has_cond_ctxt() {
  _impl_._oneof_case_[0] = kCondCtxt;
}
inline void ControlFlowContextDef::clear_cond_ctxt() {
  if (_internal_has_cond_ctxt()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.ctxt_.cond_ctxt_;
    }
    clear_has_ctxt();
  }
}
inline ::tensorflow::CondContextDef* ControlFlowContextDef::release_cond_ctxt() {
  // @@protoc_insertion_point(field_release:tensorflow.ControlFlowContextDef.cond_ctxt)
  if (_internal_has_cond_ctxt()) {
    clear_has_ctxt();
    ::tensorflow::CondContextDef* temp = _impl_.ctxt_.cond_ctxt_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.ctxt_.cond_ctxt_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CondContextDef& ControlFlowContextDef::_internal_cond_ctxt() const {
  return _internal_has_cond_ctxt()
      ? *_impl_.ctxt_.cond_ctxt_
      : reinterpret_cast< ::tensorflow::CondContextDef&>(::tensorflow::_CondContextDef_default_instance_);
}
inline const ::tensorflow::CondContextDef& ControlFlowContextDef::cond_ctxt() const {
  // @@protoc_insertion_point(field_get:tensorflow.ControlFlowContextDef.cond_ctxt)
  return _internal_cond_ctxt();
}
inline ::tensorflow::CondContextDef* ControlFlowContextDef::unsafe_arena_release_cond_ctxt() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ControlFlowContextDef.cond_ctxt)
  if (_internal_has_cond_ctxt()) {
    clear_has_ctxt();
    ::tensorflow::CondContextDef* temp = _impl_.ctxt_.cond_ctxt_;
    _impl_.ctxt_.cond_ctxt_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ControlFlowContextDef::unsafe_arena_set_allocated_cond_ctxt(::tensorflow::CondContextDef* cond_ctxt) {
  clear_ctxt();
  if (cond_ctxt) {
    set_has_cond_ctxt();
    _impl_.ctxt_.cond_ctxt_ = cond_ctxt;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ControlFlowContextDef.cond_ctxt)
}
inline ::tensorflow::CondContextDef* ControlFlowContextDef::_internal_mutable_cond_ctxt() {
  if (!_internal_has_cond_ctxt()) {
    clear_ctxt();
    set_has_cond_ctxt();
    _impl_.ctxt_.cond_ctxt_ = CreateMaybeMessage< ::tensorflow::CondContextDef >(GetArenaForAllocation());
  }
  return _impl_.ctxt_.cond_ctxt_;
}
inline ::tensorflow::CondContextDef* ControlFlowContextDef::mutable_cond_ctxt() {
  ::tensorflow::CondContextDef* _msg = _internal_mutable_cond_ctxt();
  // @@protoc_insertion_point(field_mutable:tensorflow.ControlFlowContextDef.cond_ctxt)
  return _msg;
}

// .tensorflow.WhileContextDef while_ctxt = 2;
inline bool ControlFlowContextDef::_internal_has_while_ctxt() const {
  return ctxt_case() == kWhileCtxt;
}
inline bool ControlFlowContextDef::has_while_ctxt() const {
  return _internal_has_while_ctxt();
}
inline void ControlFlowContextDef::set_has_while_ctxt() {
  _impl_._oneof_case_[0] = kWhileCtxt;
}
inline void ControlFlowContextDef::clear_while_ctxt() {
  if (_internal_has_while_ctxt()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.ctxt_.while_ctxt_;
    }
    clear_has_ctxt();
  }
}
inline ::tensorflow::WhileContextDef* ControlFlowContextDef::release_while_ctxt() {
  // @@protoc_insertion_point(field_release:tensorflow.ControlFlowContextDef.while_ctxt)
  if (_internal_has_while_ctxt()) {
    clear_has_ctxt();
    ::tensorflow::WhileContextDef* temp = _impl_.ctxt_.while_ctxt_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.ctxt_.while_ctxt_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::WhileContextDef& ControlFlowContextDef::_internal_while_ctxt() const {
  return _internal_has_while_ctxt()
      ? *_impl_.ctxt_.while_ctxt_
      : reinterpret_cast< ::tensorflow::WhileContextDef&>(::tensorflow::_WhileContextDef_default_instance_);
}
inline const ::tensorflow::WhileContextDef& ControlFlowContextDef::while_ctxt() const {
  // @@protoc_insertion_point(field_get:tensorflow.ControlFlowContextDef.while_ctxt)
  return _internal_while_ctxt();
}
inline ::tensorflow::WhileContextDef* ControlFlowContextDef::unsafe_arena_release_while_ctxt() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.ControlFlowContextDef.while_ctxt)
  if (_internal_has_while_ctxt()) {
    clear_has_ctxt();
    ::tensorflow::WhileContextDef* temp = _impl_.ctxt_.while_ctxt_;
    _impl_.ctxt_.while_ctxt_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ControlFlowContextDef::unsafe_arena_set_allocated_while_ctxt(::tensorflow::WhileContextDef* while_ctxt) {
  clear_ctxt();
  if (while_ctxt) {
    set_has_while_ctxt();
    _impl_.ctxt_.while_ctxt_ = while_ctxt;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ControlFlowContextDef.while_ctxt)
}
inline ::tensorflow::WhileContextDef* ControlFlowContextDef::_internal_mutable_while_ctxt() {
  if (!_internal_has_while_ctxt()) {
    clear_ctxt();
    set_has_while_ctxt();
    _impl_.ctxt_.while_ctxt_ = CreateMaybeMessage< ::tensorflow::WhileContextDef >(GetArenaForAllocation());
  }
  return _impl_.ctxt_.while_ctxt_;
}
inline ::tensorflow::WhileContextDef* ControlFlowContextDef::mutable_while_ctxt() {
  ::tensorflow::WhileContextDef* _msg = _internal_mutable_while_ctxt();
  // @@protoc_insertion_point(field_mutable:tensorflow.ControlFlowContextDef.while_ctxt)
  return _msg;
}

inline bool ControlFlowContextDef::has_ctxt() const {
  return ctxt_case() != CTXT_NOT_SET;
}
inline void ControlFlowContextDef::clear_has_ctxt() {
  _impl_._oneof_case_[0] = CTXT_NOT_SET;
}
inline ControlFlowContextDef::CtxtCase ControlFlowContextDef::ctxt_case() const {
  return ControlFlowContextDef::CtxtCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// CondContextDef

// string context_name = 1;
inline void CondContextDef::clear_context_name() {
  _impl_.context_name_.ClearToEmpty();
}
inline const std::string& CondContextDef::context_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.context_name)
  return _internal_context_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CondContextDef::set_context_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.context_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CondContextDef.context_name)
}
inline std::string* CondContextDef::mutable_context_name() {
  std::string* _s = _internal_mutable_context_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.context_name)
  return _s;
}
inline const std::string& CondContextDef::_internal_context_name() const {
  return _impl_.context_name_.Get();
}
inline void CondContextDef::_internal_set_context_name(const std::string& value) {
  
  _impl_.context_name_.Set(value, GetArenaForAllocation());
}
inline std::string* CondContextDef::_internal_mutable_context_name() {
  
  return _impl_.context_name_.Mutable(GetArenaForAllocation());
}
inline std::string* CondContextDef::release_context_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CondContextDef.context_name)
  return _impl_.context_name_.Release();
}
inline void CondContextDef::set_allocated_context_name(std::string* context_name) {
  if (context_name != nullptr) {
    
  } else {
    
  }
  _impl_.context_name_.SetAllocated(context_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.context_name_.IsDefault()) {
    _impl_.context_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CondContextDef.context_name)
}

// string pred_name = 2;
inline void CondContextDef::clear_pred_name() {
  _impl_.pred_name_.ClearToEmpty();
}
inline const std::string& CondContextDef::pred_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.pred_name)
  return _internal_pred_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CondContextDef::set_pred_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.pred_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CondContextDef.pred_name)
}
inline std::string* CondContextDef::mutable_pred_name() {
  std::string* _s = _internal_mutable_pred_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.pred_name)
  return _s;
}
inline const std::string& CondContextDef::_internal_pred_name() const {
  return _impl_.pred_name_.Get();
}
inline void CondContextDef::_internal_set_pred_name(const std::string& value) {
  
  _impl_.pred_name_.Set(value, GetArenaForAllocation());
}
inline std::string* CondContextDef::_internal_mutable_pred_name() {
  
  return _impl_.pred_name_.Mutable(GetArenaForAllocation());
}
inline std::string* CondContextDef::release_pred_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CondContextDef.pred_name)
  return _impl_.pred_name_.Release();
}
inline void CondContextDef::set_allocated_pred_name(std::string* pred_name) {
  if (pred_name != nullptr) {
    
  } else {
    
  }
  _impl_.pred_name_.SetAllocated(pred_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pred_name_.IsDefault()) {
    _impl_.pred_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CondContextDef.pred_name)
}

// string pivot_name = 3;
inline void CondContextDef::clear_pivot_name() {
  _impl_.pivot_name_.ClearToEmpty();
}
inline const std::string& CondContextDef::pivot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.pivot_name)
  return _internal_pivot_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CondContextDef::set_pivot_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.pivot_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CondContextDef.pivot_name)
}
inline std::string* CondContextDef::mutable_pivot_name() {
  std::string* _s = _internal_mutable_pivot_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.pivot_name)
  return _s;
}
inline const std::string& CondContextDef::_internal_pivot_name() const {
  return _impl_.pivot_name_.Get();
}
inline void CondContextDef::_internal_set_pivot_name(const std::string& value) {
  
  _impl_.pivot_name_.Set(value, GetArenaForAllocation());
}
inline std::string* CondContextDef::_internal_mutable_pivot_name() {
  
  return _impl_.pivot_name_.Mutable(GetArenaForAllocation());
}
inline std::string* CondContextDef::release_pivot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CondContextDef.pivot_name)
  return _impl_.pivot_name_.Release();
}
inline void CondContextDef::set_allocated_pivot_name(std::string* pivot_name) {
  if (pivot_name != nullptr) {
    
  } else {
    
  }
  _impl_.pivot_name_.SetAllocated(pivot_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pivot_name_.IsDefault()) {
    _impl_.pivot_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CondContextDef.pivot_name)
}

// int32 branch = 4;
inline void CondContextDef::clear_branch() {
  _impl_.branch_ = 0;
}
inline int32_t CondContextDef::_internal_branch() const {
  return _impl_.branch_;
}
inline int32_t CondContextDef::branch() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.branch)
  return _internal_branch();
}
inline void CondContextDef::_internal_set_branch(int32_t value) {
  
  _impl_.branch_ = value;
}
inline void CondContextDef::set_branch(int32_t value) {
  _internal_set_branch(value);
  // @@protoc_insertion_point(field_set:tensorflow.CondContextDef.branch)
}

// .tensorflow.ValuesDef values_def = 5;
inline bool CondContextDef::_internal_has_values_def() const {
  return this != internal_default_instance() && _impl_.values_def_ != nullptr;
}
inline bool CondContextDef::has_values_def() const {
  return _internal_has_values_def();
}
inline void CondContextDef::clear_values_def() {
  if (GetArenaForAllocation() == nullptr && _impl_.values_def_ != nullptr) {
    delete _impl_.values_def_;
  }
  _impl_.values_def_ = nullptr;
}
inline const ::tensorflow::ValuesDef& CondContextDef::_internal_values_def() const {
  const ::tensorflow::ValuesDef* p = _impl_.values_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ValuesDef&>(
      ::tensorflow::_ValuesDef_default_instance_);
}
inline const ::tensorflow::ValuesDef& CondContextDef::values_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.values_def)
  return _internal_values_def();
}
inline void CondContextDef::unsafe_arena_set_allocated_values_def(
    ::tensorflow::ValuesDef* values_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.values_def_);
  }
  _impl_.values_def_ = values_def;
  if (values_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CondContextDef.values_def)
}
inline ::tensorflow::ValuesDef* CondContextDef::release_values_def() {
  
  ::tensorflow::ValuesDef* temp = _impl_.values_def_;
  _impl_.values_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ValuesDef* CondContextDef::unsafe_arena_release_values_def() {
  // @@protoc_insertion_point(field_release:tensorflow.CondContextDef.values_def)
  
  ::tensorflow::ValuesDef* temp = _impl_.values_def_;
  _impl_.values_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ValuesDef* CondContextDef::_internal_mutable_values_def() {
  
  if (_impl_.values_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ValuesDef>(GetArenaForAllocation());
    _impl_.values_def_ = p;
  }
  return _impl_.values_def_;
}
inline ::tensorflow::ValuesDef* CondContextDef::mutable_values_def() {
  ::tensorflow::ValuesDef* _msg = _internal_mutable_values_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.values_def)
  return _msg;
}
inline void CondContextDef::set_allocated_values_def(::tensorflow::ValuesDef* values_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.values_def_;
  }
  if (values_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(values_def);
    if (message_arena != submessage_arena) {
      values_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, values_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.values_def_ = values_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CondContextDef.values_def)
}

// repeated .tensorflow.ControlFlowContextDef nested_contexts = 6;
inline int CondContextDef::_internal_nested_contexts_size() const {
  return _impl_.nested_contexts_.size();
}
inline int CondContextDef::nested_contexts_size() const {
  return _internal_nested_contexts_size();
}
inline void CondContextDef::clear_nested_contexts() {
  _impl_.nested_contexts_.Clear();
}
inline ::tensorflow::ControlFlowContextDef* CondContextDef::mutable_nested_contexts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CondContextDef.nested_contexts)
  return _impl_.nested_contexts_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >*
CondContextDef::mutable_nested_contexts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CondContextDef.nested_contexts)
  return &_impl_.nested_contexts_;
}
inline const ::tensorflow::ControlFlowContextDef& CondContextDef::_internal_nested_contexts(int index) const {
  return _impl_.nested_contexts_.Get(index);
}
inline const ::tensorflow::ControlFlowContextDef& CondContextDef::nested_contexts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CondContextDef.nested_contexts)
  return _internal_nested_contexts(index);
}
inline ::tensorflow::ControlFlowContextDef* CondContextDef::_internal_add_nested_contexts() {
  return _impl_.nested_contexts_.Add();
}
inline ::tensorflow::ControlFlowContextDef* CondContextDef::add_nested_contexts() {
  ::tensorflow::ControlFlowContextDef* _add = _internal_add_nested_contexts();
  // @@protoc_insertion_point(field_add:tensorflow.CondContextDef.nested_contexts)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >&
CondContextDef::nested_contexts() const {
  // @@protoc_insertion_point(field_list:tensorflow.CondContextDef.nested_contexts)
  return _impl_.nested_contexts_;
}

// -------------------------------------------------------------------

// WhileContextDef

// string context_name = 1;
inline void WhileContextDef::clear_context_name() {
  _impl_.context_name_.ClearToEmpty();
}
inline const std::string& WhileContextDef::context_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.context_name)
  return _internal_context_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WhileContextDef::set_context_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.context_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.context_name)
}
inline std::string* WhileContextDef::mutable_context_name() {
  std::string* _s = _internal_mutable_context_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.context_name)
  return _s;
}
inline const std::string& WhileContextDef::_internal_context_name() const {
  return _impl_.context_name_.Get();
}
inline void WhileContextDef::_internal_set_context_name(const std::string& value) {
  
  _impl_.context_name_.Set(value, GetArenaForAllocation());
}
inline std::string* WhileContextDef::_internal_mutable_context_name() {
  
  return _impl_.context_name_.Mutable(GetArenaForAllocation());
}
inline std::string* WhileContextDef::release_context_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.context_name)
  return _impl_.context_name_.Release();
}
inline void WhileContextDef::set_allocated_context_name(std::string* context_name) {
  if (context_name != nullptr) {
    
  } else {
    
  }
  _impl_.context_name_.SetAllocated(context_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.context_name_.IsDefault()) {
    _impl_.context_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.context_name)
}

// int32 parallel_iterations = 2;
inline void WhileContextDef::clear_parallel_iterations() {
  _impl_.parallel_iterations_ = 0;
}
inline int32_t WhileContextDef::_internal_parallel_iterations() const {
  return _impl_.parallel_iterations_;
}
inline int32_t WhileContextDef::parallel_iterations() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.parallel_iterations)
  return _internal_parallel_iterations();
}
inline void WhileContextDef::_internal_set_parallel_iterations(int32_t value) {
  
  _impl_.parallel_iterations_ = value;
}
inline void WhileContextDef::set_parallel_iterations(int32_t value) {
  _internal_set_parallel_iterations(value);
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.parallel_iterations)
}

// bool back_prop = 3;
inline void WhileContextDef::clear_back_prop() {
  _impl_.back_prop_ = false;
}
inline bool WhileContextDef::_internal_back_prop() const {
  return _impl_.back_prop_;
}
inline bool WhileContextDef::back_prop() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.back_prop)
  return _internal_back_prop();
}
inline void WhileContextDef::_internal_set_back_prop(bool value) {
  
  _impl_.back_prop_ = value;
}
inline void WhileContextDef::set_back_prop(bool value) {
  _internal_set_back_prop(value);
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.back_prop)
}

// bool swap_memory = 4;
inline void WhileContextDef::clear_swap_memory() {
  _impl_.swap_memory_ = false;
}
inline bool WhileContextDef::_internal_swap_memory() const {
  return _impl_.swap_memory_;
}
inline bool WhileContextDef::swap_memory() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.swap_memory)
  return _internal_swap_memory();
}
inline void WhileContextDef::_internal_set_swap_memory(bool value) {
  
  _impl_.swap_memory_ = value;
}
inline void WhileContextDef::set_swap_memory(bool value) {
  _internal_set_swap_memory(value);
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.swap_memory)
}

// string pivot_name = 5;
inline void WhileContextDef::clear_pivot_name() {
  _impl_.pivot_name_.ClearToEmpty();
}
inline const std::string& WhileContextDef::pivot_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.pivot_name)
  return _internal_pivot_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WhileContextDef::set_pivot_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.pivot_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.pivot_name)
}
inline std::string* WhileContextDef::mutable_pivot_name() {
  std::string* _s = _internal_mutable_pivot_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.pivot_name)
  return _s;
}
inline const std::string& WhileContextDef::_internal_pivot_name() const {
  return _impl_.pivot_name_.Get();
}
inline void WhileContextDef::_internal_set_pivot_name(const std::string& value) {
  
  _impl_.pivot_name_.Set(value, GetArenaForAllocation());
}
inline std::string* WhileContextDef::_internal_mutable_pivot_name() {
  
  return _impl_.pivot_name_.Mutable(GetArenaForAllocation());
}
inline std::string* WhileContextDef::release_pivot_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.pivot_name)
  return _impl_.pivot_name_.Release();
}
inline void WhileContextDef::set_allocated_pivot_name(std::string* pivot_name) {
  if (pivot_name != nullptr) {
    
  } else {
    
  }
  _impl_.pivot_name_.SetAllocated(pivot_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pivot_name_.IsDefault()) {
    _impl_.pivot_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.pivot_name)
}

// string pivot_for_pred_name = 6;
inline void WhileContextDef::clear_pivot_for_pred_name() {
  _impl_.pivot_for_pred_name_.ClearToEmpty();
}
inline const std::string& WhileContextDef::pivot_for_pred_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.pivot_for_pred_name)
  return _internal_pivot_for_pred_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WhileContextDef::set_pivot_for_pred_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.pivot_for_pred_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.pivot_for_pred_name)
}
inline std::string* WhileContextDef::mutable_pivot_for_pred_name() {
  std::string* _s = _internal_mutable_pivot_for_pred_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.pivot_for_pred_name)
  return _s;
}
inline const std::string& WhileContextDef::_internal_pivot_for_pred_name() const {
  return _impl_.pivot_for_pred_name_.Get();
}
inline void WhileContextDef::_internal_set_pivot_for_pred_name(const std::string& value) {
  
  _impl_.pivot_for_pred_name_.Set(value, GetArenaForAllocation());
}
inline std::string* WhileContextDef::_internal_mutable_pivot_for_pred_name() {
  
  return _impl_.pivot_for_pred_name_.Mutable(GetArenaForAllocation());
}
inline std::string* WhileContextDef::release_pivot_for_pred_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.pivot_for_pred_name)
  return _impl_.pivot_for_pred_name_.Release();
}
inline void WhileContextDef::set_allocated_pivot_for_pred_name(std::string* pivot_for_pred_name) {
  if (pivot_for_pred_name != nullptr) {
    
  } else {
    
  }
  _impl_.pivot_for_pred_name_.SetAllocated(pivot_for_pred_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pivot_for_pred_name_.IsDefault()) {
    _impl_.pivot_for_pred_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.pivot_for_pred_name)
}

// string pivot_for_body_name = 7;
inline void WhileContextDef::clear_pivot_for_body_name() {
  _impl_.pivot_for_body_name_.ClearToEmpty();
}
inline const std::string& WhileContextDef::pivot_for_body_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.pivot_for_body_name)
  return _internal_pivot_for_body_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WhileContextDef::set_pivot_for_body_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.pivot_for_body_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.pivot_for_body_name)
}
inline std::string* WhileContextDef::mutable_pivot_for_body_name() {
  std::string* _s = _internal_mutable_pivot_for_body_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.pivot_for_body_name)
  return _s;
}
inline const std::string& WhileContextDef::_internal_pivot_for_body_name() const {
  return _impl_.pivot_for_body_name_.Get();
}
inline void WhileContextDef::_internal_set_pivot_for_body_name(const std::string& value) {
  
  _impl_.pivot_for_body_name_.Set(value, GetArenaForAllocation());
}
inline std::string* WhileContextDef::_internal_mutable_pivot_for_body_name() {
  
  return _impl_.pivot_for_body_name_.Mutable(GetArenaForAllocation());
}
inline std::string* WhileContextDef::release_pivot_for_body_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.pivot_for_body_name)
  return _impl_.pivot_for_body_name_.Release();
}
inline void WhileContextDef::set_allocated_pivot_for_body_name(std::string* pivot_for_body_name) {
  if (pivot_for_body_name != nullptr) {
    
  } else {
    
  }
  _impl_.pivot_for_body_name_.SetAllocated(pivot_for_body_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.pivot_for_body_name_.IsDefault()) {
    _impl_.pivot_for_body_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.pivot_for_body_name)
}

// repeated string loop_exit_names = 8;
inline int WhileContextDef::_internal_loop_exit_names_size() const {
  return _impl_.loop_exit_names_.size();
}
inline int WhileContextDef::loop_exit_names_size() const {
  return _internal_loop_exit_names_size();
}
inline void WhileContextDef::clear_loop_exit_names() {
  _impl_.loop_exit_names_.Clear();
}
inline std::string* WhileContextDef::add_loop_exit_names() {
  std::string* _s = _internal_add_loop_exit_names();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.WhileContextDef.loop_exit_names)
  return _s;
}
inline const std::string& WhileContextDef::_internal_loop_exit_names(int index) const {
  return _impl_.loop_exit_names_.Get(index);
}
inline const std::string& WhileContextDef::loop_exit_names(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.loop_exit_names)
  return _internal_loop_exit_names(index);
}
inline std::string* WhileContextDef::mutable_loop_exit_names(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.loop_exit_names)
  return _impl_.loop_exit_names_.Mutable(index);
}
inline void WhileContextDef::set_loop_exit_names(int index, const std::string& value) {
  _impl_.loop_exit_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.loop_exit_names)
}
inline void WhileContextDef::set_loop_exit_names(int index, std::string&& value) {
  _impl_.loop_exit_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.loop_exit_names)
}
inline void WhileContextDef::set_loop_exit_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.loop_exit_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.loop_exit_names)
}
inline void WhileContextDef::set_loop_exit_names(int index, const char* value, size_t size) {
  _impl_.loop_exit_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.loop_exit_names)
}
inline std::string* WhileContextDef::_internal_add_loop_exit_names() {
  return _impl_.loop_exit_names_.Add();
}
inline void WhileContextDef::add_loop_exit_names(const std::string& value) {
  _impl_.loop_exit_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.loop_exit_names)
}
inline void WhileContextDef::add_loop_exit_names(std::string&& value) {
  _impl_.loop_exit_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.loop_exit_names)
}
inline void WhileContextDef::add_loop_exit_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.loop_exit_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.WhileContextDef.loop_exit_names)
}
inline void WhileContextDef::add_loop_exit_names(const char* value, size_t size) {
  _impl_.loop_exit_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.WhileContextDef.loop_exit_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
WhileContextDef::loop_exit_names() const {
  // @@protoc_insertion_point(field_list:tensorflow.WhileContextDef.loop_exit_names)
  return _impl_.loop_exit_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
WhileContextDef::mutable_loop_exit_names() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.WhileContextDef.loop_exit_names)
  return &_impl_.loop_exit_names_;
}

// repeated string loop_enter_names = 10;
inline int WhileContextDef::_internal_loop_enter_names_size() const {
  return _impl_.loop_enter_names_.size();
}
inline int WhileContextDef::loop_enter_names_size() const {
  return _internal_loop_enter_names_size();
}
inline void WhileContextDef::clear_loop_enter_names() {
  _impl_.loop_enter_names_.Clear();
}
inline std::string* WhileContextDef::add_loop_enter_names() {
  std::string* _s = _internal_add_loop_enter_names();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.WhileContextDef.loop_enter_names)
  return _s;
}
inline const std::string& WhileContextDef::_internal_loop_enter_names(int index) const {
  return _impl_.loop_enter_names_.Get(index);
}
inline const std::string& WhileContextDef::loop_enter_names(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.loop_enter_names)
  return _internal_loop_enter_names(index);
}
inline std::string* WhileContextDef::mutable_loop_enter_names(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.loop_enter_names)
  return _impl_.loop_enter_names_.Mutable(index);
}
inline void WhileContextDef::set_loop_enter_names(int index, const std::string& value) {
  _impl_.loop_enter_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.loop_enter_names)
}
inline void WhileContextDef::set_loop_enter_names(int index, std::string&& value) {
  _impl_.loop_enter_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.loop_enter_names)
}
inline void WhileContextDef::set_loop_enter_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.loop_enter_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.WhileContextDef.loop_enter_names)
}
inline void WhileContextDef::set_loop_enter_names(int index, const char* value, size_t size) {
  _impl_.loop_enter_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.WhileContextDef.loop_enter_names)
}
inline std::string* WhileContextDef::_internal_add_loop_enter_names() {
  return _impl_.loop_enter_names_.Add();
}
inline void WhileContextDef::add_loop_enter_names(const std::string& value) {
  _impl_.loop_enter_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.loop_enter_names)
}
inline void WhileContextDef::add_loop_enter_names(std::string&& value) {
  _impl_.loop_enter_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.loop_enter_names)
}
inline void WhileContextDef::add_loop_enter_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.loop_enter_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.WhileContextDef.loop_enter_names)
}
inline void WhileContextDef::add_loop_enter_names(const char* value, size_t size) {
  _impl_.loop_enter_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.WhileContextDef.loop_enter_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
WhileContextDef::loop_enter_names() const {
  // @@protoc_insertion_point(field_list:tensorflow.WhileContextDef.loop_enter_names)
  return _impl_.loop_enter_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
WhileContextDef::mutable_loop_enter_names() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.WhileContextDef.loop_enter_names)
  return &_impl_.loop_enter_names_;
}

// .tensorflow.ValuesDef values_def = 9;
inline bool WhileContextDef::_internal_has_values_def() const {
  return this != internal_default_instance() && _impl_.values_def_ != nullptr;
}
inline bool WhileContextDef::has_values_def() const {
  return _internal_has_values_def();
}
inline void WhileContextDef::clear_values_def() {
  if (GetArenaForAllocation() == nullptr && _impl_.values_def_ != nullptr) {
    delete _impl_.values_def_;
  }
  _impl_.values_def_ = nullptr;
}
inline const ::tensorflow::ValuesDef& WhileContextDef::_internal_values_def() const {
  const ::tensorflow::ValuesDef* p = _impl_.values_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ValuesDef&>(
      ::tensorflow::_ValuesDef_default_instance_);
}
inline const ::tensorflow::ValuesDef& WhileContextDef::values_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.values_def)
  return _internal_values_def();
}
inline void WhileContextDef::unsafe_arena_set_allocated_values_def(
    ::tensorflow::ValuesDef* values_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.values_def_);
  }
  _impl_.values_def_ = values_def;
  if (values_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WhileContextDef.values_def)
}
inline ::tensorflow::ValuesDef* WhileContextDef::release_values_def() {
  
  ::tensorflow::ValuesDef* temp = _impl_.values_def_;
  _impl_.values_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ValuesDef* WhileContextDef::unsafe_arena_release_values_def() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.values_def)
  
  ::tensorflow::ValuesDef* temp = _impl_.values_def_;
  _impl_.values_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ValuesDef* WhileContextDef::_internal_mutable_values_def() {
  
  if (_impl_.values_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ValuesDef>(GetArenaForAllocation());
    _impl_.values_def_ = p;
  }
  return _impl_.values_def_;
}
inline ::tensorflow::ValuesDef* WhileContextDef::mutable_values_def() {
  ::tensorflow::ValuesDef* _msg = _internal_mutable_values_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.values_def)
  return _msg;
}
inline void WhileContextDef::set_allocated_values_def(::tensorflow::ValuesDef* values_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.values_def_;
  }
  if (values_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(values_def);
    if (message_arena != submessage_arena) {
      values_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, values_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.values_def_ = values_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.values_def)
}

// string maximum_iterations_name = 11;
inline void WhileContextDef::clear_maximum_iterations_name() {
  _impl_.maximum_iterations_name_.ClearToEmpty();
}
inline const std::string& WhileContextDef::maximum_iterations_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.maximum_iterations_name)
  return _internal_maximum_iterations_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WhileContextDef::set_maximum_iterations_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.maximum_iterations_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.WhileContextDef.maximum_iterations_name)
}
inline std::string* WhileContextDef::mutable_maximum_iterations_name() {
  std::string* _s = _internal_mutable_maximum_iterations_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.maximum_iterations_name)
  return _s;
}
inline const std::string& WhileContextDef::_internal_maximum_iterations_name() const {
  return _impl_.maximum_iterations_name_.Get();
}
inline void WhileContextDef::_internal_set_maximum_iterations_name(const std::string& value) {
  
  _impl_.maximum_iterations_name_.Set(value, GetArenaForAllocation());
}
inline std::string* WhileContextDef::_internal_mutable_maximum_iterations_name() {
  
  return _impl_.maximum_iterations_name_.Mutable(GetArenaForAllocation());
}
inline std::string* WhileContextDef::release_maximum_iterations_name() {
  // @@protoc_insertion_point(field_release:tensorflow.WhileContextDef.maximum_iterations_name)
  return _impl_.maximum_iterations_name_.Release();
}
inline void WhileContextDef::set_allocated_maximum_iterations_name(std::string* maximum_iterations_name) {
  if (maximum_iterations_name != nullptr) {
    
  } else {
    
  }
  _impl_.maximum_iterations_name_.SetAllocated(maximum_iterations_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.maximum_iterations_name_.IsDefault()) {
    _impl_.maximum_iterations_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WhileContextDef.maximum_iterations_name)
}

// repeated .tensorflow.ControlFlowContextDef nested_contexts = 12;
inline int WhileContextDef::_internal_nested_contexts_size() const {
  return _impl_.nested_contexts_.size();
}
inline int WhileContextDef::nested_contexts_size() const {
  return _internal_nested_contexts_size();
}
inline void WhileContextDef::clear_nested_contexts() {
  _impl_.nested_contexts_.Clear();
}
inline ::tensorflow::ControlFlowContextDef* WhileContextDef::mutable_nested_contexts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.WhileContextDef.nested_contexts)
  return _impl_.nested_contexts_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >*
WhileContextDef::mutable_nested_contexts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.WhileContextDef.nested_contexts)
  return &_impl_.nested_contexts_;
}
inline const ::tensorflow::ControlFlowContextDef& WhileContextDef::_internal_nested_contexts(int index) const {
  return _impl_.nested_contexts_.Get(index);
}
inline const ::tensorflow::ControlFlowContextDef& WhileContextDef::nested_contexts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.WhileContextDef.nested_contexts)
  return _internal_nested_contexts(index);
}
inline ::tensorflow::ControlFlowContextDef* WhileContextDef::_internal_add_nested_contexts() {
  return _impl_.nested_contexts_.Add();
}
inline ::tensorflow::ControlFlowContextDef* WhileContextDef::add_nested_contexts() {
  ::tensorflow::ControlFlowContextDef* _add = _internal_add_nested_contexts();
  // @@protoc_insertion_point(field_add:tensorflow.WhileContextDef.nested_contexts)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ControlFlowContextDef >&
WhileContextDef::nested_contexts() const {
  // @@protoc_insertion_point(field_list:tensorflow.WhileContextDef.nested_contexts)
  return _impl_.nested_contexts_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fcontrol_5fflow_2eproto
