// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/snapshot.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
namespace tensorflow {
namespace data {
namespace experimental {
class DistributedSnapshotMetadata;
struct DistributedSnapshotMetadataDefaultTypeInternal;
extern DistributedSnapshotMetadataDefaultTypeInternal _DistributedSnapshotMetadata_default_instance_;
class SnapshotMetadataRecord;
struct SnapshotMetadataRecordDefaultTypeInternal;
extern SnapshotMetadataRecordDefaultTypeInternal _SnapshotMetadataRecord_default_instance_;
class SnapshotRecord;
struct SnapshotRecordDefaultTypeInternal;
extern SnapshotRecordDefaultTypeInternal _SnapshotRecord_default_instance_;
class SnapshotTensorMetadata;
struct SnapshotTensorMetadataDefaultTypeInternal;
extern SnapshotTensorMetadataDefaultTypeInternal _SnapshotTensorMetadata_default_instance_;
class TensorMetadata;
struct TensorMetadataDefaultTypeInternal;
extern TensorMetadataDefaultTypeInternal _TensorMetadata_default_instance_;
}  // namespace experimental
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::experimental::DistributedSnapshotMetadata* Arena::CreateMaybeMessage<::tensorflow::data::experimental::DistributedSnapshotMetadata>(Arena*);
template<> ::tensorflow::data::experimental::SnapshotMetadataRecord* Arena::CreateMaybeMessage<::tensorflow::data::experimental::SnapshotMetadataRecord>(Arena*);
template<> ::tensorflow::data::experimental::SnapshotRecord* Arena::CreateMaybeMessage<::tensorflow::data::experimental::SnapshotRecord>(Arena*);
template<> ::tensorflow::data::experimental::SnapshotTensorMetadata* Arena::CreateMaybeMessage<::tensorflow::data::experimental::SnapshotTensorMetadata>(Arena*);
template<> ::tensorflow::data::experimental::TensorMetadata* Arena::CreateMaybeMessage<::tensorflow::data::experimental::TensorMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {
namespace experimental {

// ===================================================================

class SnapshotRecord final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.SnapshotRecord) */ {
 public:
  inline SnapshotRecord() : SnapshotRecord(nullptr) {}
  ~SnapshotRecord() override;
  explicit PROTOBUF_CONSTEXPR SnapshotRecord(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SnapshotRecord(const SnapshotRecord& from);
  SnapshotRecord(SnapshotRecord&& from) noexcept
    : SnapshotRecord() {
    *this = ::std::move(from);
  }

  inline SnapshotRecord& operator=(const SnapshotRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapshotRecord& operator=(SnapshotRecord&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SnapshotRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const SnapshotRecord* internal_default_instance() {
    return reinterpret_cast<const SnapshotRecord*>(
               &_SnapshotRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SnapshotRecord& a, SnapshotRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapshotRecord* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SnapshotRecord* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SnapshotRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SnapshotRecord>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SnapshotRecord& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SnapshotRecord& from) {
    SnapshotRecord::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapshotRecord* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.SnapshotRecord";
  }
  protected:
  explicit SnapshotRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorFieldNumber = 1,
  };
  // repeated .tensorflow.TensorProto tensor = 1;
  int tensor_size() const;
  private:
  int _internal_tensor_size() const;
  public:
  void clear_tensor();
  ::tensorflow::TensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor();
  private:
  const ::tensorflow::TensorProto& _internal_tensor(int index) const;
  ::tensorflow::TensorProto* _internal_add_tensor();
  public:
  const ::tensorflow::TensorProto& tensor(int index) const;
  ::tensorflow::TensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor() const;

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotRecord)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// -------------------------------------------------------------------

class SnapshotMetadataRecord final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.SnapshotMetadataRecord) */ {
 public:
  inline SnapshotMetadataRecord() : SnapshotMetadataRecord(nullptr) {}
  ~SnapshotMetadataRecord() override;
  explicit PROTOBUF_CONSTEXPR SnapshotMetadataRecord(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SnapshotMetadataRecord(const SnapshotMetadataRecord& from);
  SnapshotMetadataRecord(SnapshotMetadataRecord&& from) noexcept
    : SnapshotMetadataRecord() {
    *this = ::std::move(from);
  }

  inline SnapshotMetadataRecord& operator=(const SnapshotMetadataRecord& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapshotMetadataRecord& operator=(SnapshotMetadataRecord&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SnapshotMetadataRecord& default_instance() {
    return *internal_default_instance();
  }
  static inline const SnapshotMetadataRecord* internal_default_instance() {
    return reinterpret_cast<const SnapshotMetadataRecord*>(
               &_SnapshotMetadataRecord_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SnapshotMetadataRecord& a, SnapshotMetadataRecord& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapshotMetadataRecord* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SnapshotMetadataRecord* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SnapshotMetadataRecord* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SnapshotMetadataRecord>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SnapshotMetadataRecord& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SnapshotMetadataRecord& from) {
    SnapshotMetadataRecord::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapshotMetadataRecord* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.SnapshotMetadataRecord";
  }
  protected:
  explicit SnapshotMetadataRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDtypeFieldNumber = 5,
    kGraphHashFieldNumber = 1,
    kRunIdFieldNumber = 2,
    kCreationTimestampFieldNumber = 3,
    kVersionFieldNumber = 4,
    kNumElementsFieldNumber = 6,
    kFinalizedFieldNumber = 1000,
  };
  // repeated .tensorflow.DataType dtype = 5;
  int dtype_size() const;
  private:
  int _internal_dtype_size() const;
  public:
  void clear_dtype();
  private:
  ::tensorflow::DataType _internal_dtype(int index) const;
  void _internal_add_dtype(::tensorflow::DataType value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_dtype();
  public:
  ::tensorflow::DataType dtype(int index) const;
  void set_dtype(int index, ::tensorflow::DataType value);
  void add_dtype(::tensorflow::DataType value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& dtype() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_dtype();

  // string graph_hash = 1;
  void clear_graph_hash();
  const std::string& graph_hash() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_hash(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_hash();
  PROTOBUF_NODISCARD std::string* release_graph_hash();
  void set_allocated_graph_hash(std::string* graph_hash);
  private:
  const std::string& _internal_graph_hash() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_hash(const std::string& value);
  std::string* _internal_mutable_graph_hash();
  public:

  // string run_id = 2;
  void clear_run_id();
  const std::string& run_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_run_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_run_id();
  PROTOBUF_NODISCARD std::string* release_run_id();
  void set_allocated_run_id(std::string* run_id);
  private:
  const std::string& _internal_run_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_run_id(const std::string& value);
  std::string* _internal_mutable_run_id();
  public:

  // int64 creation_timestamp = 3;
  void clear_creation_timestamp();
  int64_t creation_timestamp() const;
  void set_creation_timestamp(int64_t value);
  private:
  int64_t _internal_creation_timestamp() const;
  void _internal_set_creation_timestamp(int64_t value);
  public:

  // int64 version = 4;
  void clear_version();
  int64_t version() const;
  void set_version(int64_t value);
  private:
  int64_t _internal_version() const;
  void _internal_set_version(int64_t value);
  public:

  // int64 num_elements = 6;
  void clear_num_elements();
  int64_t num_elements() const;
  void set_num_elements(int64_t value);
  private:
  int64_t _internal_num_elements() const;
  void _internal_set_num_elements(int64_t value);
  public:

  // bool finalized = 1000;
  void clear_finalized();
  bool finalized() const;
  void set_finalized(bool value);
  private:
  bool _internal_finalized() const;
  void _internal_set_finalized(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotMetadataRecord)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> dtype_;
    mutable std::atomic<int> _dtype_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_hash_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr run_id_;
    int64_t creation_timestamp_;
    int64_t version_;
    int64_t num_elements_;
    bool finalized_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// -------------------------------------------------------------------

class TensorMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.TensorMetadata) */ {
 public:
  inline TensorMetadata() : TensorMetadata(nullptr) {}
  ~TensorMetadata() override;
  explicit PROTOBUF_CONSTEXPR TensorMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorMetadata(const TensorMetadata& from);
  TensorMetadata(TensorMetadata&& from) noexcept
    : TensorMetadata() {
    *this = ::std::move(from);
  }

  inline TensorMetadata& operator=(const TensorMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorMetadata& operator=(TensorMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorMetadata* internal_default_instance() {
    return reinterpret_cast<const TensorMetadata*>(
               &_TensorMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TensorMetadata& a, TensorMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorMetadata& from) {
    TensorMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.TensorMetadata";
  }
  protected:
  explicit TensorMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorShapeFieldNumber = 2,
    kTensorSizeBytesFieldNumber = 3,
  };
  // .tensorflow.TensorShapeProto tensor_shape = 2;
  bool has_tensor_shape() const;
  private:
  bool _internal_has_tensor_shape() const;
  public:
  void clear_tensor_shape();
  const ::tensorflow::TensorShapeProto& tensor_shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_tensor_shape();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape();
  void set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_tensor_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_tensor_shape();
  public:
  void unsafe_arena_set_allocated_tensor_shape(
      ::tensorflow::TensorShapeProto* tensor_shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_tensor_shape();

  // int64 tensor_size_bytes = 3;
  void clear_tensor_size_bytes();
  int64_t tensor_size_bytes() const;
  void set_tensor_size_bytes(int64_t value);
  private:
  int64_t _internal_tensor_size_bytes() const;
  void _internal_set_tensor_size_bytes(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.TensorMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* tensor_shape_;
    int64_t tensor_size_bytes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// -------------------------------------------------------------------

class SnapshotTensorMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.SnapshotTensorMetadata) */ {
 public:
  inline SnapshotTensorMetadata() : SnapshotTensorMetadata(nullptr) {}
  ~SnapshotTensorMetadata() override;
  explicit PROTOBUF_CONSTEXPR SnapshotTensorMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SnapshotTensorMetadata(const SnapshotTensorMetadata& from);
  SnapshotTensorMetadata(SnapshotTensorMetadata&& from) noexcept
    : SnapshotTensorMetadata() {
    *this = ::std::move(from);
  }

  inline SnapshotTensorMetadata& operator=(const SnapshotTensorMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapshotTensorMetadata& operator=(SnapshotTensorMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SnapshotTensorMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const SnapshotTensorMetadata* internal_default_instance() {
    return reinterpret_cast<const SnapshotTensorMetadata*>(
               &_SnapshotTensorMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SnapshotTensorMetadata& a, SnapshotTensorMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapshotTensorMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SnapshotTensorMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SnapshotTensorMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SnapshotTensorMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SnapshotTensorMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SnapshotTensorMetadata& from) {
    SnapshotTensorMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapshotTensorMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.SnapshotTensorMetadata";
  }
  protected:
  explicit SnapshotTensorMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorMetadataFieldNumber = 1,
  };
  // repeated .tensorflow.data.experimental.TensorMetadata tensor_metadata = 1;
  int tensor_metadata_size() const;
  private:
  int _internal_tensor_metadata_size() const;
  public:
  void clear_tensor_metadata();
  ::tensorflow::data::experimental::TensorMetadata* mutable_tensor_metadata(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata >*
      mutable_tensor_metadata();
  private:
  const ::tensorflow::data::experimental::TensorMetadata& _internal_tensor_metadata(int index) const;
  ::tensorflow::data::experimental::TensorMetadata* _internal_add_tensor_metadata();
  public:
  const ::tensorflow::data::experimental::TensorMetadata& tensor_metadata(int index) const;
  ::tensorflow::data::experimental::TensorMetadata* add_tensor_metadata();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata >&
      tensor_metadata() const;

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.SnapshotTensorMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata > tensor_metadata_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// -------------------------------------------------------------------

class DistributedSnapshotMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.DistributedSnapshotMetadata) */ {
 public:
  inline DistributedSnapshotMetadata() : DistributedSnapshotMetadata(nullptr) {}
  ~DistributedSnapshotMetadata() override;
  explicit PROTOBUF_CONSTEXPR DistributedSnapshotMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DistributedSnapshotMetadata(const DistributedSnapshotMetadata& from);
  DistributedSnapshotMetadata(DistributedSnapshotMetadata&& from) noexcept
    : DistributedSnapshotMetadata() {
    *this = ::std::move(from);
  }

  inline DistributedSnapshotMetadata& operator=(const DistributedSnapshotMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline DistributedSnapshotMetadata& operator=(DistributedSnapshotMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DistributedSnapshotMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const DistributedSnapshotMetadata* internal_default_instance() {
    return reinterpret_cast<const DistributedSnapshotMetadata*>(
               &_DistributedSnapshotMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(DistributedSnapshotMetadata& a, DistributedSnapshotMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(DistributedSnapshotMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DistributedSnapshotMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DistributedSnapshotMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DistributedSnapshotMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DistributedSnapshotMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DistributedSnapshotMetadata& from) {
    DistributedSnapshotMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DistributedSnapshotMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.DistributedSnapshotMetadata";
  }
  protected:
  explicit DistributedSnapshotMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kElementSpecFieldNumber = 1,
    kCompressionFieldNumber = 2,
  };
  // bytes element_spec = 1;
  void clear_element_spec();
  const std::string& element_spec() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_element_spec(ArgT0&& arg0, ArgT... args);
  std::string* mutable_element_spec();
  PROTOBUF_NODISCARD std::string* release_element_spec();
  void set_allocated_element_spec(std::string* element_spec);
  private:
  const std::string& _internal_element_spec() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_element_spec(const std::string& value);
  std::string* _internal_mutable_element_spec();
  public:

  // string compression = 2;
  void clear_compression();
  const std::string& compression() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_compression(ArgT0&& arg0, ArgT... args);
  std::string* mutable_compression();
  PROTOBUF_NODISCARD std::string* release_compression();
  void set_allocated_compression(std::string* compression);
  private:
  const std::string& _internal_compression() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_compression(const std::string& value);
  std::string* _internal_mutable_compression();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.DistributedSnapshotMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr element_spec_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr compression_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SnapshotRecord

// repeated .tensorflow.TensorProto tensor = 1;
inline int SnapshotRecord::_internal_tensor_size() const {
  return _impl_.tensor_.size();
}
inline int SnapshotRecord::tensor_size() const {
  return _internal_tensor_size();
}
inline ::tensorflow::TensorProto* SnapshotRecord::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.SnapshotRecord.tensor)
  return _impl_.tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
SnapshotRecord::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.experimental.SnapshotRecord.tensor)
  return &_impl_.tensor_;
}
inline const ::tensorflow::TensorProto& SnapshotRecord::_internal_tensor(int index) const {
  return _impl_.tensor_.Get(index);
}
inline const ::tensorflow::TensorProto& SnapshotRecord::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotRecord.tensor)
  return _internal_tensor(index);
}
inline ::tensorflow::TensorProto* SnapshotRecord::_internal_add_tensor() {
  return _impl_.tensor_.Add();
}
inline ::tensorflow::TensorProto* SnapshotRecord::add_tensor() {
  ::tensorflow::TensorProto* _add = _internal_add_tensor();
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.SnapshotRecord.tensor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
SnapshotRecord::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.experimental.SnapshotRecord.tensor)
  return _impl_.tensor_;
}

// -------------------------------------------------------------------

// SnapshotMetadataRecord

// string graph_hash = 1;
inline void SnapshotMetadataRecord::clear_graph_hash() {
  _impl_.graph_hash_.ClearToEmpty();
}
inline const std::string& SnapshotMetadataRecord::graph_hash() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
  return _internal_graph_hash();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SnapshotMetadataRecord::set_graph_hash(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_hash_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
}
inline std::string* SnapshotMetadataRecord::mutable_graph_hash() {
  std::string* _s = _internal_mutable_graph_hash();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
  return _s;
}
inline const std::string& SnapshotMetadataRecord::_internal_graph_hash() const {
  return _impl_.graph_hash_.Get();
}
inline void SnapshotMetadataRecord::_internal_set_graph_hash(const std::string& value) {
  
  _impl_.graph_hash_.Set(value, GetArenaForAllocation());
}
inline std::string* SnapshotMetadataRecord::_internal_mutable_graph_hash() {
  
  return _impl_.graph_hash_.Mutable(GetArenaForAllocation());
}
inline std::string* SnapshotMetadataRecord::release_graph_hash() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
  return _impl_.graph_hash_.Release();
}
inline void SnapshotMetadataRecord::set_allocated_graph_hash(std::string* graph_hash) {
  if (graph_hash != nullptr) {
    
  } else {
    
  }
  _impl_.graph_hash_.SetAllocated(graph_hash, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_hash_.IsDefault()) {
    _impl_.graph_hash_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.SnapshotMetadataRecord.graph_hash)
}

// string run_id = 2;
inline void SnapshotMetadataRecord::clear_run_id() {
  _impl_.run_id_.ClearToEmpty();
}
inline const std::string& SnapshotMetadataRecord::run_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
  return _internal_run_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SnapshotMetadataRecord::set_run_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.run_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
}
inline std::string* SnapshotMetadataRecord::mutable_run_id() {
  std::string* _s = _internal_mutable_run_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
  return _s;
}
inline const std::string& SnapshotMetadataRecord::_internal_run_id() const {
  return _impl_.run_id_.Get();
}
inline void SnapshotMetadataRecord::_internal_set_run_id(const std::string& value) {
  
  _impl_.run_id_.Set(value, GetArenaForAllocation());
}
inline std::string* SnapshotMetadataRecord::_internal_mutable_run_id() {
  
  return _impl_.run_id_.Mutable(GetArenaForAllocation());
}
inline std::string* SnapshotMetadataRecord::release_run_id() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
  return _impl_.run_id_.Release();
}
inline void SnapshotMetadataRecord::set_allocated_run_id(std::string* run_id) {
  if (run_id != nullptr) {
    
  } else {
    
  }
  _impl_.run_id_.SetAllocated(run_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.run_id_.IsDefault()) {
    _impl_.run_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.SnapshotMetadataRecord.run_id)
}

// int64 creation_timestamp = 3;
inline void SnapshotMetadataRecord::clear_creation_timestamp() {
  _impl_.creation_timestamp_ = int64_t{0};
}
inline int64_t SnapshotMetadataRecord::_internal_creation_timestamp() const {
  return _impl_.creation_timestamp_;
}
inline int64_t SnapshotMetadataRecord::creation_timestamp() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.creation_timestamp)
  return _internal_creation_timestamp();
}
inline void SnapshotMetadataRecord::_internal_set_creation_timestamp(int64_t value) {
  
  _impl_.creation_timestamp_ = value;
}
inline void SnapshotMetadataRecord::set_creation_timestamp(int64_t value) {
  _internal_set_creation_timestamp(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.creation_timestamp)
}

// int64 version = 4;
inline void SnapshotMetadataRecord::clear_version() {
  _impl_.version_ = int64_t{0};
}
inline int64_t SnapshotMetadataRecord::_internal_version() const {
  return _impl_.version_;
}
inline int64_t SnapshotMetadataRecord::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.version)
  return _internal_version();
}
inline void SnapshotMetadataRecord::_internal_set_version(int64_t value) {
  
  _impl_.version_ = value;
}
inline void SnapshotMetadataRecord::set_version(int64_t value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.version)
}

// repeated .tensorflow.DataType dtype = 5;
inline int SnapshotMetadataRecord::_internal_dtype_size() const {
  return _impl_.dtype_.size();
}
inline int SnapshotMetadataRecord::dtype_size() const {
  return _internal_dtype_size();
}
inline void SnapshotMetadataRecord::clear_dtype() {
  _impl_.dtype_.Clear();
}
inline ::tensorflow::DataType SnapshotMetadataRecord::_internal_dtype(int index) const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_.Get(index));
}
inline ::tensorflow::DataType SnapshotMetadataRecord::dtype(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
  return _internal_dtype(index);
}
inline void SnapshotMetadataRecord::set_dtype(int index, ::tensorflow::DataType value) {
  _impl_.dtype_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
}
inline void SnapshotMetadataRecord::_internal_add_dtype(::tensorflow::DataType value) {
  _impl_.dtype_.Add(value);
}
inline void SnapshotMetadataRecord::add_dtype(::tensorflow::DataType value) {
  _internal_add_dtype(value);
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
SnapshotMetadataRecord::dtype() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
  return _impl_.dtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
SnapshotMetadataRecord::_internal_mutable_dtype() {
  return &_impl_.dtype_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
SnapshotMetadataRecord::mutable_dtype() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.experimental.SnapshotMetadataRecord.dtype)
  return _internal_mutable_dtype();
}

// int64 num_elements = 6;
inline void SnapshotMetadataRecord::clear_num_elements() {
  _impl_.num_elements_ = int64_t{0};
}
inline int64_t SnapshotMetadataRecord::_internal_num_elements() const {
  return _impl_.num_elements_;
}
inline int64_t SnapshotMetadataRecord::num_elements() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.num_elements)
  return _internal_num_elements();
}
inline void SnapshotMetadataRecord::_internal_set_num_elements(int64_t value) {
  
  _impl_.num_elements_ = value;
}
inline void SnapshotMetadataRecord::set_num_elements(int64_t value) {
  _internal_set_num_elements(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.num_elements)
}

// bool finalized = 1000;
inline void SnapshotMetadataRecord::clear_finalized() {
  _impl_.finalized_ = false;
}
inline bool SnapshotMetadataRecord::_internal_finalized() const {
  return _impl_.finalized_;
}
inline bool SnapshotMetadataRecord::finalized() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotMetadataRecord.finalized)
  return _internal_finalized();
}
inline void SnapshotMetadataRecord::_internal_set_finalized(bool value) {
  
  _impl_.finalized_ = value;
}
inline void SnapshotMetadataRecord::set_finalized(bool value) {
  _internal_set_finalized(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.SnapshotMetadataRecord.finalized)
}

// -------------------------------------------------------------------

// TensorMetadata

// .tensorflow.TensorShapeProto tensor_shape = 2;
inline bool TensorMetadata::_internal_has_tensor_shape() const {
  return this != internal_default_instance() && _impl_.tensor_shape_ != nullptr;
}
inline bool TensorMetadata::has_tensor_shape() const {
  return _internal_has_tensor_shape();
}
inline const ::tensorflow::TensorShapeProto& TensorMetadata::_internal_tensor_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.tensor_shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& TensorMetadata::tensor_shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.TensorMetadata.tensor_shape)
  return _internal_tensor_shape();
}
inline void TensorMetadata::unsafe_arena_set_allocated_tensor_shape(
    ::tensorflow::TensorShapeProto* tensor_shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_shape_);
  }
  _impl_.tensor_shape_ = tensor_shape;
  if (tensor_shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.data.experimental.TensorMetadata.tensor_shape)
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::release_tensor_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.tensor_shape_;
  _impl_.tensor_shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::unsafe_arena_release_tensor_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.TensorMetadata.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.tensor_shape_;
  _impl_.tensor_shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::_internal_mutable_tensor_shape() {
  
  if (_impl_.tensor_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.tensor_shape_ = p;
  }
  return _impl_.tensor_shape_;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::mutable_tensor_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_tensor_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.TensorMetadata.tensor_shape)
  return _msg;
}
inline void TensorMetadata::set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_shape_);
  }
  if (tensor_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_shape));
    if (message_arena != submessage_arena) {
      tensor_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_shape_ = tensor_shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.TensorMetadata.tensor_shape)
}

// int64 tensor_size_bytes = 3;
inline void TensorMetadata::clear_tensor_size_bytes() {
  _impl_.tensor_size_bytes_ = int64_t{0};
}
inline int64_t TensorMetadata::_internal_tensor_size_bytes() const {
  return _impl_.tensor_size_bytes_;
}
inline int64_t TensorMetadata::tensor_size_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.TensorMetadata.tensor_size_bytes)
  return _internal_tensor_size_bytes();
}
inline void TensorMetadata::_internal_set_tensor_size_bytes(int64_t value) {
  
  _impl_.tensor_size_bytes_ = value;
}
inline void TensorMetadata::set_tensor_size_bytes(int64_t value) {
  _internal_set_tensor_size_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.TensorMetadata.tensor_size_bytes)
}

// -------------------------------------------------------------------

// SnapshotTensorMetadata

// repeated .tensorflow.data.experimental.TensorMetadata tensor_metadata = 1;
inline int SnapshotTensorMetadata::_internal_tensor_metadata_size() const {
  return _impl_.tensor_metadata_.size();
}
inline int SnapshotTensorMetadata::tensor_metadata_size() const {
  return _internal_tensor_metadata_size();
}
inline void SnapshotTensorMetadata::clear_tensor_metadata() {
  _impl_.tensor_metadata_.Clear();
}
inline ::tensorflow::data::experimental::TensorMetadata* SnapshotTensorMetadata::mutable_tensor_metadata(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return _impl_.tensor_metadata_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata >*
SnapshotTensorMetadata::mutable_tensor_metadata() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return &_impl_.tensor_metadata_;
}
inline const ::tensorflow::data::experimental::TensorMetadata& SnapshotTensorMetadata::_internal_tensor_metadata(int index) const {
  return _impl_.tensor_metadata_.Get(index);
}
inline const ::tensorflow::data::experimental::TensorMetadata& SnapshotTensorMetadata::tensor_metadata(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return _internal_tensor_metadata(index);
}
inline ::tensorflow::data::experimental::TensorMetadata* SnapshotTensorMetadata::_internal_add_tensor_metadata() {
  return _impl_.tensor_metadata_.Add();
}
inline ::tensorflow::data::experimental::TensorMetadata* SnapshotTensorMetadata::add_tensor_metadata() {
  ::tensorflow::data::experimental::TensorMetadata* _add = _internal_add_tensor_metadata();
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::data::experimental::TensorMetadata >&
SnapshotTensorMetadata::tensor_metadata() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.experimental.SnapshotTensorMetadata.tensor_metadata)
  return _impl_.tensor_metadata_;
}

// -------------------------------------------------------------------

// DistributedSnapshotMetadata

// bytes element_spec = 1;
inline void DistributedSnapshotMetadata::clear_element_spec() {
  _impl_.element_spec_.ClearToEmpty();
}
inline const std::string& DistributedSnapshotMetadata::element_spec() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DistributedSnapshotMetadata.element_spec)
  return _internal_element_spec();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DistributedSnapshotMetadata::set_element_spec(ArgT0&& arg0, ArgT... args) {
 
 _impl_.element_spec_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DistributedSnapshotMetadata.element_spec)
}
inline std::string* DistributedSnapshotMetadata::mutable_element_spec() {
  std::string* _s = _internal_mutable_element_spec();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.DistributedSnapshotMetadata.element_spec)
  return _s;
}
inline const std::string& DistributedSnapshotMetadata::_internal_element_spec() const {
  return _impl_.element_spec_.Get();
}
inline void DistributedSnapshotMetadata::_internal_set_element_spec(const std::string& value) {
  
  _impl_.element_spec_.Set(value, GetArenaForAllocation());
}
inline std::string* DistributedSnapshotMetadata::_internal_mutable_element_spec() {
  
  return _impl_.element_spec_.Mutable(GetArenaForAllocation());
}
inline std::string* DistributedSnapshotMetadata::release_element_spec() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.DistributedSnapshotMetadata.element_spec)
  return _impl_.element_spec_.Release();
}
inline void DistributedSnapshotMetadata::set_allocated_element_spec(std::string* element_spec) {
  if (element_spec != nullptr) {
    
  } else {
    
  }
  _impl_.element_spec_.SetAllocated(element_spec, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.element_spec_.IsDefault()) {
    _impl_.element_spec_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.DistributedSnapshotMetadata.element_spec)
}

// string compression = 2;
inline void DistributedSnapshotMetadata::clear_compression() {
  _impl_.compression_.ClearToEmpty();
}
inline const std::string& DistributedSnapshotMetadata::compression() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DistributedSnapshotMetadata.compression)
  return _internal_compression();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DistributedSnapshotMetadata::set_compression(ArgT0&& arg0, ArgT... args) {
 
 _impl_.compression_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DistributedSnapshotMetadata.compression)
}
inline std::string* DistributedSnapshotMetadata::mutable_compression() {
  std::string* _s = _internal_mutable_compression();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.DistributedSnapshotMetadata.compression)
  return _s;
}
inline const std::string& DistributedSnapshotMetadata::_internal_compression() const {
  return _impl_.compression_.Get();
}
inline void DistributedSnapshotMetadata::_internal_set_compression(const std::string& value) {
  
  _impl_.compression_.Set(value, GetArenaForAllocation());
}
inline std::string* DistributedSnapshotMetadata::_internal_mutable_compression() {
  
  return _impl_.compression_.Mutable(GetArenaForAllocation());
}
inline std::string* DistributedSnapshotMetadata::release_compression() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.DistributedSnapshotMetadata.compression)
  return _impl_.compression_.Release();
}
inline void DistributedSnapshotMetadata::set_allocated_compression(std::string* compression) {
  if (compression != nullptr) {
    
  } else {
    
  }
  _impl_.compression_.SetAllocated(compression, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.compression_.IsDefault()) {
    _impl_.compression_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.DistributedSnapshotMetadata.compression)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace experimental
}  // namespace data
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsnapshot_2eproto
