/*** Autogenerated by WIDL 10.8 from include/windows.devices.geolocation.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_geolocation_h__
#define __windows_devices_geolocation_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator ABI::Windows::Devices::Geolocation::IGeolocator
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IGeolocator;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition ABI::Windows::Devices::Geolocation::IGeoposition
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IGeoposition;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate ABI::Windows::Devices::Geolocation::IGeocoordinate
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IGeocoordinate;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs ABI::Windows::Devices::Geolocation::IPositionChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IPositionChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs ABI::Windows::Devices::Geolocation::IStatusChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IStatusChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress ABI::Windows::Devices::Geolocation::ICivicAddress
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface ICivicAddress;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CGeolocator_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CGeolocator_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                class Geolocator;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CGeolocator __x_ABI_CWindows_CDevices_CGeolocation_CGeolocator;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CGeolocation_CGeolocator_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CGeoposition_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CGeoposition_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                class Geoposition;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CGeoposition __x_ABI_CWindows_CDevices_CGeolocation_CGeoposition;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CGeolocation_CGeoposition_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CGeocoordinate_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CGeocoordinate_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                class Geocoordinate;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CGeocoordinate __x_ABI_CWindows_CDevices_CGeolocation_CGeocoordinate;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CGeolocation_CGeocoordinate_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CPositionChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CPositionChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                class PositionChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CPositionChangedEventArgs __x_ABI_CWindows_CDevices_CGeolocation_CPositionChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CGeolocation_CPositionChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CStatusChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CStatusChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                class StatusChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CStatusChangedEventArgs __x_ABI_CWindows_CDevices_CGeolocation_CStatusChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CGeolocation_CStatusChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CCivicAddress_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CCivicAddress_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                class CivicAddress;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CCivicAddress __x_ABI_CWindows_CDevices_CGeolocation_CCivicAddress;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CGeolocation_CCivicAddress_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Geolocation::Geoposition* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::PositionChangedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::StatusChangedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CGeolocation_CPositionAccuracy __x_ABI_CWindows_CDevices_CGeolocation_CPositionAccuracy;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CGeolocation_CPositionStatus __x_ABI_CWindows_CDevices_CGeolocation_CPositionStatus;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator ABI::Windows::Devices::Geolocation::IGeolocator
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IGeolocator;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition ABI::Windows::Devices::Geolocation::IGeoposition
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IGeoposition;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate ABI::Windows::Devices::Geolocation::IGeocoordinate
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IGeocoordinate;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs ABI::Windows::Devices::Geolocation::IPositionChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IPositionChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs ABI::Windows::Devices::Geolocation::IStatusChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface IStatusChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress ABI::Windows::Devices::Geolocation::ICivicAddress
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                interface ICivicAddress;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                enum PositionAccuracy {
                    PositionAccuracy_Default = 0,
                    PositionAccuracy_High = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CGeolocation_CPositionAccuracy {
    PositionAccuracy_Default = 0,
    PositionAccuracy_High = 1
};
#ifdef WIDL_using_Windows_Devices_Geolocation
#define PositionAccuracy __x_ABI_CWindows_CDevices_CGeolocation_CPositionAccuracy
#endif /* WIDL_using_Windows_Devices_Geolocation */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                enum PositionStatus {
                    PositionStatus_Ready = 0,
                    PositionStatus_Initializing = 1,
                    PositionStatus_NoData = 2,
                    PositionStatus_Disabled = 3,
                    PositionStatus_NotInitialized = 4,
                    PositionStatus_NotAvailable = 5
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CGeolocation_CPositionStatus {
    PositionStatus_Ready = 0,
    PositionStatus_Initializing = 1,
    PositionStatus_NoData = 2,
    PositionStatus_Disabled = 3,
    PositionStatus_NotInitialized = 4,
    PositionStatus_NotAvailable = 5
};
#ifdef WIDL_using_Windows_Devices_Geolocation
#define PositionStatus __x_ABI_CWindows_CDevices_CGeolocation_CPositionStatus
#endif /* WIDL_using_Windows_Devices_Geolocation */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IGeolocator interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator, 0xa9c3bf62, 0x4524, 0x4989, 0x8a,0xa9, 0xde,0x01,0x9d,0x2e,0x55,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                MIDL_INTERFACE("a9c3bf62-4524-4989-8aa9-de019d2e551f")
                IGeolocator : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DesiredAccuracy(
                        ABI::Windows::Devices::Geolocation::PositionAccuracy *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_DesiredAccuracy(
                        ABI::Windows::Devices::Geolocation::PositionAccuracy value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MovementThreshold(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_MovementThreshold(
                        DOUBLE value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ReportInterval(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_ReportInterval(
                        UINT32 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_LocationStatus(
                        ABI::Windows::Devices::Geolocation::PositionStatus *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetGeopositionAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetGeopositionAsyncWithAgeAndTimeout(
                        ABI::Windows::Foundation::TimeSpan maximum_age,
                        ABI::Windows::Foundation::TimeSpan timeout,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_PositionChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::PositionChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_PositionChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_StatusChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::StatusChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_StatusChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator, 0xa9c3bf62, 0x4524, 0x4989, 0x8a,0xa9, 0xde,0x01,0x9d,0x2e,0x55,0x1f)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        TrustLevel *trustLevel);

    /*** IGeolocator methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DesiredAccuracy)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CPositionAccuracy *value);

    HRESULT (STDMETHODCALLTYPE *put_DesiredAccuracy)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CPositionAccuracy value);

    HRESULT (STDMETHODCALLTYPE *get_MovementThreshold)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *put_MovementThreshold)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *get_ReportInterval)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_ReportInterval)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        UINT32 value);

    HRESULT (STDMETHODCALLTYPE *get_LocationStatus)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CPositionStatus *value);

    HRESULT (STDMETHODCALLTYPE *GetGeopositionAsync)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition **value);

    HRESULT (STDMETHODCALLTYPE *GetGeopositionAsyncWithAgeAndTimeout)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan maximum_age,
        __x_ABI_CWindows_CFoundation_CTimeSpan timeout,
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition **value);

    HRESULT (STDMETHODCALLTYPE *add_PositionChanged)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_PositionChanged)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_StatusChanged)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_StatusChanged)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocatorVtbl;

interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator {
    CONST_VTBL __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGeolocator methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_DesiredAccuracy(This,value) (This)->lpVtbl->get_DesiredAccuracy(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_DesiredAccuracy(This,value) (This)->lpVtbl->put_DesiredAccuracy(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_MovementThreshold(This,value) (This)->lpVtbl->get_MovementThreshold(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_MovementThreshold(This,value) (This)->lpVtbl->put_MovementThreshold(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_ReportInterval(This,value) (This)->lpVtbl->get_ReportInterval(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_ReportInterval(This,value) (This)->lpVtbl->put_ReportInterval(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_LocationStatus(This,value) (This)->lpVtbl->get_LocationStatus(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetGeopositionAsync(This,value) (This)->lpVtbl->GetGeopositionAsync(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetGeopositionAsyncWithAgeAndTimeout(This,maximum_age,timeout,value) (This)->lpVtbl->GetGeopositionAsyncWithAgeAndTimeout(This,maximum_age,timeout,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_add_PositionChanged(This,handler,token) (This)->lpVtbl->add_PositionChanged(This,handler,token)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_remove_PositionChanged(This,token) (This)->lpVtbl->remove_PositionChanged(This,token)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_add_StatusChanged(This,handler,token) (This)->lpVtbl->add_StatusChanged(This,handler,token)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_remove_StatusChanged(This,token) (This)->lpVtbl->remove_StatusChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_QueryInterface(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_AddRef(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_Release(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetIids(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetTrustLevel(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGeolocator methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_DesiredAccuracy(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,__x_ABI_CWindows_CDevices_CGeolocation_CPositionAccuracy *value) {
    return This->lpVtbl->get_DesiredAccuracy(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_DesiredAccuracy(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,__x_ABI_CWindows_CDevices_CGeolocation_CPositionAccuracy value) {
    return This->lpVtbl->put_DesiredAccuracy(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_MovementThreshold(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,DOUBLE *value) {
    return This->lpVtbl->get_MovementThreshold(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_MovementThreshold(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,DOUBLE value) {
    return This->lpVtbl->put_MovementThreshold(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_ReportInterval(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,UINT32 *value) {
    return This->lpVtbl->get_ReportInterval(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_ReportInterval(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,UINT32 value) {
    return This->lpVtbl->put_ReportInterval(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_LocationStatus(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,__x_ABI_CWindows_CDevices_CGeolocation_CPositionStatus *value) {
    return This->lpVtbl->get_LocationStatus(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetGeopositionAsync(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition **value) {
    return This->lpVtbl->GetGeopositionAsync(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetGeopositionAsyncWithAgeAndTimeout(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,__x_ABI_CWindows_CFoundation_CTimeSpan maximum_age,__x_ABI_CWindows_CFoundation_CTimeSpan timeout,__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition **value) {
    return This->lpVtbl->GetGeopositionAsyncWithAgeAndTimeout(This,maximum_age,timeout,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_add_PositionChanged(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_PositionChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_remove_PositionChanged(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_PositionChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_add_StatusChanged(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_StatusChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_remove_StatusChanged(__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_StatusChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Devices_Geolocation
#define IID_IGeolocator IID___x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator
#define IGeolocatorVtbl __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocatorVtbl
#define IGeolocator __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator
#define IGeolocator_QueryInterface __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_QueryInterface
#define IGeolocator_AddRef __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_AddRef
#define IGeolocator_Release __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_Release
#define IGeolocator_GetIids __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetIids
#define IGeolocator_GetRuntimeClassName __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetRuntimeClassName
#define IGeolocator_GetTrustLevel __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetTrustLevel
#define IGeolocator_get_DesiredAccuracy __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_DesiredAccuracy
#define IGeolocator_put_DesiredAccuracy __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_DesiredAccuracy
#define IGeolocator_get_MovementThreshold __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_MovementThreshold
#define IGeolocator_put_MovementThreshold __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_MovementThreshold
#define IGeolocator_get_ReportInterval __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_ReportInterval
#define IGeolocator_put_ReportInterval __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_put_ReportInterval
#define IGeolocator_get_LocationStatus __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_get_LocationStatus
#define IGeolocator_GetGeopositionAsync __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetGeopositionAsync
#define IGeolocator_GetGeopositionAsyncWithAgeAndTimeout __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_GetGeopositionAsyncWithAgeAndTimeout
#define IGeolocator_add_PositionChanged __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_add_PositionChanged
#define IGeolocator_remove_PositionChanged __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_remove_PositionChanged
#define IGeolocator_add_StatusChanged __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_add_StatusChanged
#define IGeolocator_remove_StatusChanged __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_remove_StatusChanged
#endif /* WIDL_using_Windows_Devices_Geolocation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IGeoposition interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition, 0xc18d0454, 0x7d41, 0x4ff7, 0xa9,0x57, 0x9d,0xff,0xb4,0xef,0x7f,0x5b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                MIDL_INTERFACE("c18d0454-7d41-4ff7-a957-9dffb4ef7f5b")
                IGeoposition : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Coordinate(
                        ABI::Windows::Devices::Geolocation::IGeocoordinate **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CivicAddress(
                        ABI::Windows::Devices::Geolocation::ICivicAddress **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition, 0xc18d0454, 0x7d41, 0x4ff7, 0xa9,0x57, 0x9d,0xff,0xb4,0xef,0x7f,0x5b)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CIGeopositionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition *This,
        TrustLevel *trustLevel);

    /*** IGeoposition methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Coordinate)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate **value);

    HRESULT (STDMETHODCALLTYPE *get_CivicAddress)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress **value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CGeolocation_CIGeopositionVtbl;

interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition {
    CONST_VTBL __x_ABI_CWindows_CDevices_CGeolocation_CIGeopositionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGeoposition methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_get_Coordinate(This,value) (This)->lpVtbl->get_Coordinate(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_get_CivicAddress(This,value) (This)->lpVtbl->get_CivicAddress(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_QueryInterface(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_AddRef(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_Release(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetIids(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetTrustLevel(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGeoposition methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_get_Coordinate(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition* This,__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate **value) {
    return This->lpVtbl->get_Coordinate(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_get_CivicAddress(__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition* This,__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress **value) {
    return This->lpVtbl->get_CivicAddress(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Geolocation
#define IID_IGeoposition IID___x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition
#define IGeopositionVtbl __x_ABI_CWindows_CDevices_CGeolocation_CIGeopositionVtbl
#define IGeoposition __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition
#define IGeoposition_QueryInterface __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_QueryInterface
#define IGeoposition_AddRef __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_AddRef
#define IGeoposition_Release __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_Release
#define IGeoposition_GetIids __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetIids
#define IGeoposition_GetRuntimeClassName __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetRuntimeClassName
#define IGeoposition_GetTrustLevel __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_GetTrustLevel
#define IGeoposition_get_Coordinate __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_get_Coordinate
#define IGeoposition_get_CivicAddress __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_get_CivicAddress
#endif /* WIDL_using_Windows_Devices_Geolocation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IGeocoordinate interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate, 0xee21a3aa, 0x976a, 0xac70, 0x80,0x3d, 0x08,0x3e,0xa5,0x5b,0xcb,0xc4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                MIDL_INTERFACE("ee21a3aa-976a-ac70-803d-083ea55bcbc4")
                IGeocoordinate : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Latitude(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Longitude(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Altitude(
                        ABI::Windows::Foundation::IReference<DOUBLE > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Accuracy(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_AltitudeAccuracy(
                        ABI::Windows::Foundation::IReference<DOUBLE > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Heading(
                        ABI::Windows::Foundation::IReference<DOUBLE > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Speed(
                        ABI::Windows::Foundation::IReference<DOUBLE > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Timestamp(
                        ABI::Windows::Foundation::DateTime *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate, 0xee21a3aa, 0x976a, 0xac70, 0x80,0x3d, 0x08,0x3e,0xa5,0x5b,0xcb,0xc4)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        TrustLevel *trustLevel);

    /*** IGeocoordinate methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Latitude)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *get_Longitude)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *get_Altitude)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        __FIReference_1_DOUBLE **value);

    HRESULT (STDMETHODCALLTYPE *get_Accuracy)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *get_AltitudeAccuracy)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        __FIReference_1_DOUBLE **value);

    HRESULT (STDMETHODCALLTYPE *get_Heading)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        __FIReference_1_DOUBLE **value);

    HRESULT (STDMETHODCALLTYPE *get_Speed)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        __FIReference_1_DOUBLE **value);

    HRESULT (STDMETHODCALLTYPE *get_Timestamp)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinateVtbl;

interface __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate {
    CONST_VTBL __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGeocoordinate methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Latitude(This,value) (This)->lpVtbl->get_Latitude(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Longitude(This,value) (This)->lpVtbl->get_Longitude(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Altitude(This,value) (This)->lpVtbl->get_Altitude(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Accuracy(This,value) (This)->lpVtbl->get_Accuracy(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_AltitudeAccuracy(This,value) (This)->lpVtbl->get_AltitudeAccuracy(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Heading(This,value) (This)->lpVtbl->get_Heading(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Speed(This,value) (This)->lpVtbl->get_Speed(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Timestamp(This,value) (This)->lpVtbl->get_Timestamp(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_QueryInterface(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_AddRef(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_Release(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetIids(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetTrustLevel(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGeocoordinate methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Latitude(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,DOUBLE *value) {
    return This->lpVtbl->get_Latitude(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Longitude(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,DOUBLE *value) {
    return This->lpVtbl->get_Longitude(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Altitude(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,__FIReference_1_DOUBLE **value) {
    return This->lpVtbl->get_Altitude(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Accuracy(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,DOUBLE *value) {
    return This->lpVtbl->get_Accuracy(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_AltitudeAccuracy(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,__FIReference_1_DOUBLE **value) {
    return This->lpVtbl->get_AltitudeAccuracy(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Heading(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,__FIReference_1_DOUBLE **value) {
    return This->lpVtbl->get_Heading(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Speed(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,__FIReference_1_DOUBLE **value) {
    return This->lpVtbl->get_Speed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Timestamp(__x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_Timestamp(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Geolocation
#define IID_IGeocoordinate IID___x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate
#define IGeocoordinateVtbl __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinateVtbl
#define IGeocoordinate __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate
#define IGeocoordinate_QueryInterface __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_QueryInterface
#define IGeocoordinate_AddRef __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_AddRef
#define IGeocoordinate_Release __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_Release
#define IGeocoordinate_GetIids __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetIids
#define IGeocoordinate_GetRuntimeClassName __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetRuntimeClassName
#define IGeocoordinate_GetTrustLevel __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_GetTrustLevel
#define IGeocoordinate_get_Latitude __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Latitude
#define IGeocoordinate_get_Longitude __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Longitude
#define IGeocoordinate_get_Altitude __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Altitude
#define IGeocoordinate_get_Accuracy __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Accuracy
#define IGeocoordinate_get_AltitudeAccuracy __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_AltitudeAccuracy
#define IGeocoordinate_get_Heading __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Heading
#define IGeocoordinate_get_Speed __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Speed
#define IGeocoordinate_get_Timestamp __x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_get_Timestamp
#endif /* WIDL_using_Windows_Devices_Geolocation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CGeolocation_CIGeocoordinate_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPositionChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs, 0x37859ce5, 0x9d1e, 0x46c5, 0xbf,0x3b, 0x6a,0xd8,0xca,0xc1,0xa0,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                MIDL_INTERFACE("37859ce5-9d1e-46c5-bf3b-6ad8cac1a093")
                IPositionChangedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Position(
                        ABI::Windows::Devices::Geolocation::IGeoposition **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs, 0x37859ce5, 0x9d1e, 0x46c5, 0xbf,0x3b, 0x6a,0xd8,0xca,0xc1,0xa0,0x93)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IPositionChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Position)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition **value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgsVtbl;

interface __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPositionChangedEventArgs methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_get_Position(This,value) (This)->lpVtbl->get_Position(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_QueryInterface(__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_AddRef(__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_Release(__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetIids(__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPositionChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_get_Position(__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs* This,__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition **value) {
    return This->lpVtbl->get_Position(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Geolocation
#define IID_IPositionChangedEventArgs IID___x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs
#define IPositionChangedEventArgsVtbl __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgsVtbl
#define IPositionChangedEventArgs __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs
#define IPositionChangedEventArgs_QueryInterface __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_QueryInterface
#define IPositionChangedEventArgs_AddRef __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_AddRef
#define IPositionChangedEventArgs_Release __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_Release
#define IPositionChangedEventArgs_GetIids __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetIids
#define IPositionChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetRuntimeClassName
#define IPositionChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_GetTrustLevel
#define IPositionChangedEventArgs_get_Position __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_get_Position
#endif /* WIDL_using_Windows_Devices_Geolocation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IStatusChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs, 0x3453d2da, 0x8c93, 0x4111, 0xa2,0x05, 0x9a,0xec,0xfc,0x9b,0xe5,0xc0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                MIDL_INTERFACE("3453d2da-8c93-4111-a205-9aecfc9be5c0")
                IStatusChangedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Status(
                        ABI::Windows::Devices::Geolocation::PositionStatus *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs, 0x3453d2da, 0x8c93, 0x4111, 0xa2,0x05, 0x9a,0xec,0xfc,0x9b,0xe5,0xc0)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IStatusChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CPositionStatus *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgsVtbl;

interface __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IStatusChangedEventArgs methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_get_Status(This,value) (This)->lpVtbl->get_Status(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_QueryInterface(__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_AddRef(__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_Release(__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetIids(__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IStatusChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_get_Status(__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs* This,__x_ABI_CWindows_CDevices_CGeolocation_CPositionStatus *value) {
    return This->lpVtbl->get_Status(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Geolocation
#define IID_IStatusChangedEventArgs IID___x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs
#define IStatusChangedEventArgsVtbl __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgsVtbl
#define IStatusChangedEventArgs __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs
#define IStatusChangedEventArgs_QueryInterface __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_QueryInterface
#define IStatusChangedEventArgs_AddRef __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_AddRef
#define IStatusChangedEventArgs_Release __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_Release
#define IStatusChangedEventArgs_GetIids __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetIids
#define IStatusChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetRuntimeClassName
#define IStatusChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_GetTrustLevel
#define IStatusChangedEventArgs_get_Status __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_get_Status
#endif /* WIDL_using_Windows_Devices_Geolocation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICivicAddress interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress, 0xa8567a1a, 0x64f4, 0x4d48, 0xbc,0xea, 0xf6,0xb0,0x08,0xec,0xa3,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Geolocation {
                MIDL_INTERFACE("a8567a1a-64f4-4d48-bcea-f6b008eca34c")
                ICivicAddress : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Country(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_State(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_City(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PostalCode(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Timestamp(
                        ABI::Windows::Foundation::DateTime *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress, 0xa8567a1a, 0x64f4, 0x4d48, 0xbc,0xea, 0xf6,0xb0,0x08,0xec,0xa3,0x4c)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        TrustLevel *trustLevel);

    /*** ICivicAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Country)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_City)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_PostalCode)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Timestamp)(
        __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddressVtbl;

interface __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress {
    CONST_VTBL __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICivicAddress methods ***/
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_Country(This,value) (This)->lpVtbl->get_Country(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_State(This,value) (This)->lpVtbl->get_State(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_City(This,value) (This)->lpVtbl->get_City(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_PostalCode(This,value) (This)->lpVtbl->get_PostalCode(This,value)
#define __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_Timestamp(This,value) (This)->lpVtbl->get_Timestamp(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_QueryInterface(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_AddRef(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_Release(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetIids(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetTrustLevel(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICivicAddress methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_Country(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,HSTRING *value) {
    return This->lpVtbl->get_Country(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_State(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,HSTRING *value) {
    return This->lpVtbl->get_State(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_City(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,HSTRING *value) {
    return This->lpVtbl->get_City(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_PostalCode(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,HSTRING *value) {
    return This->lpVtbl->get_PostalCode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_Timestamp(__x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_Timestamp(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Geolocation
#define IID_ICivicAddress IID___x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress
#define ICivicAddressVtbl __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddressVtbl
#define ICivicAddress __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress
#define ICivicAddress_QueryInterface __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_QueryInterface
#define ICivicAddress_AddRef __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_AddRef
#define ICivicAddress_Release __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_Release
#define ICivicAddress_GetIids __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetIids
#define ICivicAddress_GetRuntimeClassName __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetRuntimeClassName
#define ICivicAddress_GetTrustLevel __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_GetTrustLevel
#define ICivicAddress_get_Country __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_Country
#define ICivicAddress_get_State __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_State
#define ICivicAddress_get_City __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_City
#define ICivicAddress_get_PostalCode __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_PostalCode
#define ICivicAddress_get_Timestamp __x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_get_Timestamp
#endif /* WIDL_using_Windows_Devices_Geolocation */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CGeolocation_CICivicAddress_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Geolocation.Geolocator
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Geolocation_Geolocator_DEFINED
#define RUNTIMECLASS_Windows_Devices_Geolocation_Geolocator_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Geolocation_Geolocator[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','G','e','o','l','o','c','a','t','o','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_Geolocator[] = L"Windows.Devices.Geolocation.Geolocator";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_Geolocator[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','G','e','o','l','o','c','a','t','o','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Geolocation_Geolocator_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Geolocation.Geoposition
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Geolocation_Geoposition_DEFINED
#define RUNTIMECLASS_Windows_Devices_Geolocation_Geoposition_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Geolocation_Geoposition[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','G','e','o','p','o','s','i','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_Geoposition[] = L"Windows.Devices.Geolocation.Geoposition";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_Geoposition[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','G','e','o','p','o','s','i','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Geolocation_Geoposition_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Geolocation.Geocoordinate
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Geolocation_Geocoordinate_DEFINED
#define RUNTIMECLASS_Windows_Devices_Geolocation_Geocoordinate_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Geolocation_Geocoordinate[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','G','e','o','c','o','o','r','d','i','n','a','t','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_Geocoordinate[] = L"Windows.Devices.Geolocation.Geocoordinate";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_Geocoordinate[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','G','e','o','c','o','o','r','d','i','n','a','t','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Geolocation_Geocoordinate_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Geolocation.PositionChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Geolocation_PositionChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Devices_Geolocation_PositionChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Geolocation_PositionChangedEventArgs[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','P','o','s','i','t','i','o','n','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_PositionChangedEventArgs[] = L"Windows.Devices.Geolocation.PositionChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_PositionChangedEventArgs[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','P','o','s','i','t','i','o','n','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Geolocation_PositionChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Geolocation.StatusChangedEventArgs
 */
#ifndef RUNTIMECLASS_Windows_Devices_Geolocation_StatusChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Devices_Geolocation_StatusChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Geolocation_StatusChangedEventArgs[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','S','t','a','t','u','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_StatusChangedEventArgs[] = L"Windows.Devices.Geolocation.StatusChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_StatusChangedEventArgs[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','S','t','a','t','u','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Geolocation_StatusChangedEventArgs_DEFINED */

/*
 * Class Windows.Devices.Geolocation.CivicAddress
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Geolocation_CivicAddress_DEFINED
#define RUNTIMECLASS_Windows_Devices_Geolocation_CivicAddress_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Geolocation_CivicAddress[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','C','i','v','i','c','A','d','d','r','e','s','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_CivicAddress[] = L"Windows.Devices.Geolocation.CivicAddress";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Geolocation_CivicAddress[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','G','e','o','l','o','c','a','t','i','o','n','.','C','i','v','i','c','A','d','d','r','e','s','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Geolocation_CivicAddress_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Geolocation::Geoposition* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition, 0x7668a704, 0x244e, 0x5e12, 0x8d,0xcb, 0x92,0xa3,0x29,0x9e,0xba,0x26);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("7668a704-244e-5e12-8dcb-92a3299eba26")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Geolocation::Geoposition* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Geolocation::Geoposition*, ABI::Windows::Devices::Geolocation::IGeoposition* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition, 0x7668a704, 0x244e, 0x5e12, 0x8d,0xcb, 0x92,0xa3,0x29,0x9e,0xba,0x26)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeopositionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Geolocation::Geoposition* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeopositionVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeopositionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Geolocation::Geoposition* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Geolocation::Geoposition* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition* This,__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_Geoposition IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition
#define IAsyncOperationCompletedHandler_GeopositionVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeopositionVtbl
#define IAsyncOperationCompletedHandler_Geoposition __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition
#define IAsyncOperationCompletedHandler_Geoposition_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_QueryInterface
#define IAsyncOperationCompletedHandler_Geoposition_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_AddRef
#define IAsyncOperationCompletedHandler_Geoposition_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_Release
#define IAsyncOperationCompletedHandler_Geoposition_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition, 0xee73ecf0, 0x099d, 0x57e5, 0x84,0x07, 0x5b,0x32,0xe5,0xaf,0x1c,0xc4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("ee73ecf0-099d-57e5-8407-5b32e5af1cc4")
            IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Geolocation::Geoposition*, ABI::Windows::Devices::Geolocation::IGeoposition* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition, 0xee73ecf0, 0x099d, 0x57e5, 0x84,0x07, 0x5b,0x32,0xe5,0xaf,0x1c,0xc4)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeopositionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeopositionVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeopositionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_AddRef(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_Release(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetIids(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Geolocation::Geoposition* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CGeolocation__CGeoposition **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetResults(__FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition* This,__x_ABI_CWindows_CDevices_CGeolocation_CIGeoposition **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_Geoposition IID___FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition
#define IAsyncOperation_GeopositionVtbl __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeopositionVtbl
#define IAsyncOperation_Geoposition __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition
#define IAsyncOperation_Geoposition_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_QueryInterface
#define IAsyncOperation_Geoposition_AddRef __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_AddRef
#define IAsyncOperation_Geoposition_Release __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_Release
#define IAsyncOperation_Geoposition_GetIids __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetIids
#define IAsyncOperation_Geoposition_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetRuntimeClassName
#define IAsyncOperation_Geoposition_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetTrustLevel
#define IAsyncOperation_Geoposition_put_Completed __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_put_Completed
#define IAsyncOperation_Geoposition_get_Completed __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_get_Completed
#define IAsyncOperation_Geoposition_GetResults __FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CGeolocation__CGeoposition_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::PositionChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs, 0xdf3c6164, 0x4e7b, 0x5e8e, 0x9a,0x7e, 0x13,0xda,0x05,0x9d,0xec,0x1e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("df3c6164-4e7b-5e8e-9a7e-13da059dec1e")
            ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::PositionChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Geolocation::Geolocator*, ABI::Windows::Devices::Geolocation::IGeolocator* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Geolocation::PositionChangedEventArgs*, ABI::Windows::Devices::Geolocation::IPositionChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs, 0xdf3c6164, 0x4e7b, 0x5e8e, 0x9a,0x7e, 0x13,0xda,0x05,0x9d,0xec,0x1e)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::PositionChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *sender,
        __x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::PositionChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::PositionChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs* This,__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *sender,__x_ABI_CWindows_CDevices_CGeolocation_CIPositionChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_Geolocator_PositionChangedEventArgs IID___FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs
#define ITypedEventHandler_Geolocator_PositionChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgsVtbl
#define ITypedEventHandler_Geolocator_PositionChangedEventArgs __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs
#define ITypedEventHandler_Geolocator_PositionChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_QueryInterface
#define ITypedEventHandler_Geolocator_PositionChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_AddRef
#define ITypedEventHandler_Geolocator_PositionChangedEventArgs_Release __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_Release
#define ITypedEventHandler_Geolocator_PositionChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CPositionChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::StatusChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs, 0x97fcf582, 0xde6b, 0x5cd3, 0x96,0x90, 0xe2,0xec,0xbb,0x66,0xda,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("97fcf582-de6b-5cd3-9690-e2ecbb66da4d")
            ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::StatusChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Geolocation::Geolocator*, ABI::Windows::Devices::Geolocation::IGeolocator* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Geolocation::StatusChangedEventArgs*, ABI::Windows::Devices::Geolocation::IStatusChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs, 0x97fcf582, 0xde6b, 0x5cd3, 0x96,0x90, 0xe2,0xec,0xbb,0x66,0xda,0x4d)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::StatusChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs *This,
        __x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *sender,
        __x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::StatusChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Geolocation::Geolocator*,ABI::Windows::Devices::Geolocation::StatusChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs* This,__x_ABI_CWindows_CDevices_CGeolocation_CIGeolocator *sender,__x_ABI_CWindows_CDevices_CGeolocation_CIStatusChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_Geolocator_StatusChangedEventArgs IID___FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs
#define ITypedEventHandler_Geolocator_StatusChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgsVtbl
#define ITypedEventHandler_Geolocator_StatusChangedEventArgs __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs
#define ITypedEventHandler_Geolocator_StatusChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_QueryInterface
#define ITypedEventHandler_Geolocator_StatusChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_AddRef
#define ITypedEventHandler_Geolocator_StatusChangedEventArgs_Release __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_Release
#define ITypedEventHandler_Geolocator_StatusChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CGeolocation__CGeolocator_Windows__CDevices__CGeolocation__CStatusChangedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_geolocation_h__ */
