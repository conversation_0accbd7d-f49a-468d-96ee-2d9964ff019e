/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCL<PERSON>IMER within this package.
 */

import "naptypes.idl";
import "unknwn.idl";

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

interface INapSystemHealthAgentBinding;
interface INapSystemHealthAgentCallback;
interface INapSystemHealthAgentRequest;

cpp_quote("EXTERN_C const CLSID CLSID_NapSystemHealthAgentBinding;")

[object, uuid (A6894F43-9cc7-44c9-A23F-19dbf36bad28), pointer_default (unique)]
interface INapSystemHealthAgentBinding : IUnknown {
  HRESULT Initialize ([in] SystemHealthEntityId id,[in] INapSystemHealthAgentCallback *callback);
  HRESULT Uninitialize ();
  HRESULT NotifySoHChange ();
  HRESULT GetSystemIsolationInfo ([out] IsolationInfo **isolationInfo,[out] WINBOOL *unknownConnections);
  HRESULT FlushCache ();
};

[object, uuid (1140c38e-5100-4ea1-8d43-87d326724028), pointer_default (unique)]
interface INapSystemHealthAgentBinding2 : INapSystemHealthAgentBinding {
  HRESULT GetSystemIsolationInfoEx ([out] IsolationInfoEx **isolationInfo,[out] WINBOOL *unknownConnections);
};

[object, uuid (F1072A57-214f-4ee2-8377-14ef140cd9f3), async_uuid (860d8fd9-0219-43ea-AE7F-59611f4cc16a), pointer_default (unique)]
interface INapSystemHealthAgentCallback : IUnknown {
  HRESULT GetSoHRequest ([in] INapSystemHealthAgentRequest *request);
  HRESULT ProcessSoHResponse ([in] INapSystemHealthAgentRequest *request);
  HRESULT NotifySystemIsolationStateChange ();
  HRESULT GetFixupInfo ([out] FixupInfo **info);
  HRESULT CompareSoHRequests ([in] const SoHRequest *lhs,[in] const SoHRequest *rhs,[out] WINBOOL *isEqual);
  HRESULT NotifyOrphanedSoHRequest ([in] const CorrelationId *correlationId);
};

[object, uuid (5b360a69-212d-440d-B398-7eefd497853a), pointer_default (unique)]
interface INapSystemHealthAgentRequest : IUnknown {
  HRESULT GetCorrelationId ([out] CorrelationId *correlationId);
  HRESULT GetStringCorrelationId ([out] StringCorrelationId **correlationId);
  HRESULT SetSoHRequest ([in] const SoHRequest *sohRequest,[in] WINBOOL cacheSohForLaterUse);
  HRESULT GetSoHRequest ([out] SoHRequest **sohRequest);
  HRESULT GetSoHResponse ([out] SoHResponse **sohResponse,[out] UINT8 *flags);
  HRESULT GetCacheSoHFlag (WINBOOL *cacheSohForLaterUse);
};

cpp_quote("#endif")
