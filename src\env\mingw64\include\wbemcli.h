/*** Autogenerated by WIDL 10.8 from include/wbemcli.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wbemcli_h__
#define __wbemcli_h__

/* Forward declarations */

#ifndef __WbemBackupRestore_FWD_DEFINED__
#define __WbemBackupRestore_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemBackupRestore WbemBackupRestore;
#else
typedef struct WbemBackupRestore WbemBackupRestore;
#endif /* defined __cplusplus */
#endif /* defined __WbemBackupRestore_FWD_DEFINED__ */

#ifndef __WbemClassObject_FWD_DEFINED__
#define __WbemClassObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemClassObject WbemClassObject;
#else
typedef struct WbemClassObject WbemClassObject;
#endif /* defined __cplusplus */
#endif /* defined __WbemClassObject_FWD_DEFINED__ */

#ifndef __WbemContext_FWD_DEFINED__
#define __WbemContext_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemContext WbemContext;
#else
typedef struct WbemContext WbemContext;
#endif /* defined __cplusplus */
#endif /* defined __WbemContext_FWD_DEFINED__ */

#ifndef __WbemLocator_FWD_DEFINED__
#define __WbemLocator_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemLocator WbemLocator;
#else
typedef struct WbemLocator WbemLocator;
#endif /* defined __cplusplus */
#endif /* defined __WbemLocator_FWD_DEFINED__ */

#ifndef __WbemStatusCodeText_FWD_DEFINED__
#define __WbemStatusCodeText_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemStatusCodeText WbemStatusCodeText;
#else
typedef struct WbemStatusCodeText WbemStatusCodeText;
#endif /* defined __cplusplus */
#endif /* defined __WbemStatusCodeText_FWD_DEFINED__ */

#ifndef __UnsecuredApartment_FWD_DEFINED__
#define __UnsecuredApartment_FWD_DEFINED__
#ifdef __cplusplus
typedef class UnsecuredApartment UnsecuredApartment;
#else
typedef struct UnsecuredApartment UnsecuredApartment;
#endif /* defined __cplusplus */
#endif /* defined __UnsecuredApartment_FWD_DEFINED__ */

#ifndef __MofCompiler_FWD_DEFINED__
#define __MofCompiler_FWD_DEFINED__
#ifdef __cplusplus
typedef class MofCompiler MofCompiler;
#else
typedef struct MofCompiler MofCompiler;
#endif /* defined __cplusplus */
#endif /* defined __MofCompiler_FWD_DEFINED__ */

#ifndef __WbemObjectTextSrc_FWD_DEFINED__
#define __WbemObjectTextSrc_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemObjectTextSrc WbemObjectTextSrc;
#else
typedef struct WbemObjectTextSrc WbemObjectTextSrc;
#endif /* defined __cplusplus */
#endif /* defined __WbemObjectTextSrc_FWD_DEFINED__ */

#ifndef __WbemRefresher_FWD_DEFINED__
#define __WbemRefresher_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemRefresher WbemRefresher;
#else
typedef struct WbemRefresher WbemRefresher;
#endif /* defined __cplusplus */
#endif /* defined __WbemRefresher_FWD_DEFINED__ */

#ifndef __IWbemClassObject_FWD_DEFINED__
#define __IWbemClassObject_FWD_DEFINED__
typedef interface IWbemClassObject IWbemClassObject;
#ifdef __cplusplus
interface IWbemClassObject;
#endif /* __cplusplus */
#endif

#ifndef __IWbemQualifierSet_FWD_DEFINED__
#define __IWbemQualifierSet_FWD_DEFINED__
typedef interface IWbemQualifierSet IWbemQualifierSet;
#ifdef __cplusplus
interface IWbemQualifierSet;
#endif /* __cplusplus */
#endif

#ifndef __IWbemLocator_FWD_DEFINED__
#define __IWbemLocator_FWD_DEFINED__
typedef interface IWbemLocator IWbemLocator;
#ifdef __cplusplus
interface IWbemLocator;
#endif /* __cplusplus */
#endif

#ifndef __IWbemObjectSink_FWD_DEFINED__
#define __IWbemObjectSink_FWD_DEFINED__
typedef interface IWbemObjectSink IWbemObjectSink;
#ifdef __cplusplus
interface IWbemObjectSink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemObjectSinkEx_FWD_DEFINED__
#define __IWbemObjectSinkEx_FWD_DEFINED__
typedef interface IWbemObjectSinkEx IWbemObjectSinkEx;
#ifdef __cplusplus
interface IWbemObjectSinkEx;
#endif /* __cplusplus */
#endif

#ifndef __IEnumWbemClassObject_FWD_DEFINED__
#define __IEnumWbemClassObject_FWD_DEFINED__
typedef interface IEnumWbemClassObject IEnumWbemClassObject;
#ifdef __cplusplus
interface IEnumWbemClassObject;
#endif /* __cplusplus */
#endif

#ifndef __IWbemContext_FWD_DEFINED__
#define __IWbemContext_FWD_DEFINED__
typedef interface IWbemContext IWbemContext;
#ifdef __cplusplus
interface IWbemContext;
#endif /* __cplusplus */
#endif

#ifndef __IWbemCallResult_FWD_DEFINED__
#define __IWbemCallResult_FWD_DEFINED__
typedef interface IWbemCallResult IWbemCallResult;
#ifdef __cplusplus
interface IWbemCallResult;
#endif /* __cplusplus */
#endif

#ifndef __IWbemServices_FWD_DEFINED__
#define __IWbemServices_FWD_DEFINED__
typedef interface IWbemServices IWbemServices;
#ifdef __cplusplus
interface IWbemServices;
#endif /* __cplusplus */
#endif

#ifndef __IWbemShutdown_FWD_DEFINED__
#define __IWbemShutdown_FWD_DEFINED__
typedef interface IWbemShutdown IWbemShutdown;
#ifdef __cplusplus
interface IWbemShutdown;
#endif /* __cplusplus */
#endif

#ifndef __IWbemObjectTextSrc_FWD_DEFINED__
#define __IWbemObjectTextSrc_FWD_DEFINED__
typedef interface IWbemObjectTextSrc IWbemObjectTextSrc;
#ifdef __cplusplus
interface IWbemObjectTextSrc;
#endif /* __cplusplus */
#endif

#ifndef __IWbemObjectAccess_FWD_DEFINED__
#define __IWbemObjectAccess_FWD_DEFINED__
typedef interface IWbemObjectAccess IWbemObjectAccess;
#ifdef __cplusplus
interface IWbemObjectAccess;
#endif /* __cplusplus */
#endif

#ifndef __IMofCompiler_FWD_DEFINED__
#define __IMofCompiler_FWD_DEFINED__
typedef interface IMofCompiler IMofCompiler;
#ifdef __cplusplus
interface IMofCompiler;
#endif /* __cplusplus */
#endif

#ifndef __IUnsecuredApartment_FWD_DEFINED__
#define __IUnsecuredApartment_FWD_DEFINED__
typedef interface IUnsecuredApartment IUnsecuredApartment;
#ifdef __cplusplus
interface IUnsecuredApartment;
#endif /* __cplusplus */
#endif

#ifndef __IWbemUnsecuredApartment_FWD_DEFINED__
#define __IWbemUnsecuredApartment_FWD_DEFINED__
typedef interface IWbemUnsecuredApartment IWbemUnsecuredApartment;
#ifdef __cplusplus
interface IWbemUnsecuredApartment;
#endif /* __cplusplus */
#endif

#ifndef __IWbemStatusCodeText_FWD_DEFINED__
#define __IWbemStatusCodeText_FWD_DEFINED__
typedef interface IWbemStatusCodeText IWbemStatusCodeText;
#ifdef __cplusplus
interface IWbemStatusCodeText;
#endif /* __cplusplus */
#endif

#ifndef __IWbemBackupRestore_FWD_DEFINED__
#define __IWbemBackupRestore_FWD_DEFINED__
typedef interface IWbemBackupRestore IWbemBackupRestore;
#ifdef __cplusplus
interface IWbemBackupRestore;
#endif /* __cplusplus */
#endif

#ifndef __IWbemBackupRestoreEx_FWD_DEFINED__
#define __IWbemBackupRestoreEx_FWD_DEFINED__
typedef interface IWbemBackupRestoreEx IWbemBackupRestoreEx;
#ifdef __cplusplus
interface IWbemBackupRestoreEx;
#endif /* __cplusplus */
#endif

#ifndef __IWbemRefresher_FWD_DEFINED__
#define __IWbemRefresher_FWD_DEFINED__
typedef interface IWbemRefresher IWbemRefresher;
#ifdef __cplusplus
interface IWbemRefresher;
#endif /* __cplusplus */
#endif

#ifndef __IWbemHiPerfEnum_FWD_DEFINED__
#define __IWbemHiPerfEnum_FWD_DEFINED__
typedef interface IWbemHiPerfEnum IWbemHiPerfEnum;
#ifdef __cplusplus
interface IWbemHiPerfEnum;
#endif /* __cplusplus */
#endif

#ifndef __IWbemConfigureRefresher_FWD_DEFINED__
#define __IWbemConfigureRefresher_FWD_DEFINED__
typedef interface IWbemConfigureRefresher IWbemConfigureRefresher;
#ifdef __cplusplus
interface IWbemConfigureRefresher;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <ocidl.h>
#include <oleidl.h>
#include <oaidl.h>
#include <servprov.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#ifndef __IEnumWbemClassObject_FWD_DEFINED__
#define __IEnumWbemClassObject_FWD_DEFINED__
typedef interface IEnumWbemClassObject IEnumWbemClassObject;
#ifdef __cplusplus
interface IEnumWbemClassObject;
#endif /* __cplusplus */
#endif

#ifndef __IUnsecuredApartment_FWD_DEFINED__
#define __IUnsecuredApartment_FWD_DEFINED__
typedef interface IUnsecuredApartment IUnsecuredApartment;
#ifdef __cplusplus
interface IUnsecuredApartment;
#endif /* __cplusplus */
#endif

#ifndef __IWbemBackupRestore_FWD_DEFINED__
#define __IWbemBackupRestore_FWD_DEFINED__
typedef interface IWbemBackupRestore IWbemBackupRestore;
#ifdef __cplusplus
interface IWbemBackupRestore;
#endif /* __cplusplus */
#endif

#ifndef __IWbemBackupRestoreEx_FWD_DEFINED__
#define __IWbemBackupRestoreEx_FWD_DEFINED__
typedef interface IWbemBackupRestoreEx IWbemBackupRestoreEx;
#ifdef __cplusplus
interface IWbemBackupRestoreEx;
#endif /* __cplusplus */
#endif

#ifndef __IWbemCallResult_FWD_DEFINED__
#define __IWbemCallResult_FWD_DEFINED__
typedef interface IWbemCallResult IWbemCallResult;
#ifdef __cplusplus
interface IWbemCallResult;
#endif /* __cplusplus */
#endif

#ifndef __IWbemClassObject_FWD_DEFINED__
#define __IWbemClassObject_FWD_DEFINED__
typedef interface IWbemClassObject IWbemClassObject;
#ifdef __cplusplus
interface IWbemClassObject;
#endif /* __cplusplus */
#endif

#ifndef __IWbemConfigureRefresher_FWD_DEFINED__
#define __IWbemConfigureRefresher_FWD_DEFINED__
typedef interface IWbemConfigureRefresher IWbemConfigureRefresher;
#ifdef __cplusplus
interface IWbemConfigureRefresher;
#endif /* __cplusplus */
#endif

#ifndef __IWbemConnection_FWD_DEFINED__
#define __IWbemConnection_FWD_DEFINED__
typedef interface IWbemConnection IWbemConnection;
#ifdef __cplusplus
interface IWbemConnection;
#endif /* __cplusplus */
#endif

#ifndef __IWbemContext_FWD_DEFINED__
#define __IWbemContext_FWD_DEFINED__
typedef interface IWbemContext IWbemContext;
#ifdef __cplusplus
interface IWbemContext;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventSink_FWD_DEFINED__
#define __IWbemEventSink_FWD_DEFINED__
typedef interface IWbemEventSink IWbemEventSink;
#ifdef __cplusplus
interface IWbemEventSink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemHiPerfEnum_FWD_DEFINED__
#define __IWbemHiPerfEnum_FWD_DEFINED__
typedef interface IWbemHiPerfEnum IWbemHiPerfEnum;
#ifdef __cplusplus
interface IWbemHiPerfEnum;
#endif /* __cplusplus */
#endif

#ifndef __IWbemLocator_FWD_DEFINED__
#define __IWbemLocator_FWD_DEFINED__
typedef interface IWbemLocator IWbemLocator;
#ifdef __cplusplus
interface IWbemLocator;
#endif /* __cplusplus */
#endif

#ifndef __IWbemObjectAccess_FWD_DEFINED__
#define __IWbemObjectAccess_FWD_DEFINED__
typedef interface IWbemObjectAccess IWbemObjectAccess;
#ifdef __cplusplus
interface IWbemObjectAccess;
#endif /* __cplusplus */
#endif

#ifndef __IWbemObjectSink_FWD_DEFINED__
#define __IWbemObjectSink_FWD_DEFINED__
typedef interface IWbemObjectSink IWbemObjectSink;
#ifdef __cplusplus
interface IWbemObjectSink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemQualifierSet_FWD_DEFINED__
#define __IWbemQualifierSet_FWD_DEFINED__
typedef interface IWbemQualifierSet IWbemQualifierSet;
#ifdef __cplusplus
interface IWbemQualifierSet;
#endif /* __cplusplus */
#endif

#ifndef __IWbemRefresher_FWD_DEFINED__
#define __IWbemRefresher_FWD_DEFINED__
typedef interface IWbemRefresher IWbemRefresher;
#ifdef __cplusplus
interface IWbemRefresher;
#endif /* __cplusplus */
#endif

#ifndef __IWbemSecureObjectSink_FWD_DEFINED__
#define __IWbemSecureObjectSink_FWD_DEFINED__
typedef interface IWbemSecureObjectSink IWbemSecureObjectSink;
#ifdef __cplusplus
interface IWbemSecureObjectSink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemServices_FWD_DEFINED__
#define __IWbemServices_FWD_DEFINED__
typedef interface IWbemServices IWbemServices;
#ifdef __cplusplus
interface IWbemServices;
#endif /* __cplusplus */
#endif

#ifndef __IWbemStatusCodeText_FWD_DEFINED__
#define __IWbemStatusCodeText_FWD_DEFINED__
typedef interface IWbemStatusCodeText IWbemStatusCodeText;
#ifdef __cplusplus
interface IWbemStatusCodeText;
#endif /* __cplusplus */
#endif

#ifndef __IWbemUnsecuredApartment_FWD_DEFINED__
#define __IWbemUnsecuredApartment_FWD_DEFINED__
typedef interface IWbemUnsecuredApartment IWbemUnsecuredApartment;
#ifdef __cplusplus
interface IWbemUnsecuredApartment;
#endif /* __cplusplus */
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __WbemClient_v1_LIBRARY_DEFINED__
#define __WbemClient_v1_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_WbemClient_v1, 0x7ec196fe, 0x7005, 0x11d1, 0xad,0x90, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);

typedef enum tag_WBEM_GENUS_TYPE {
    WBEM_GENUS_CLASS = 1,
    WBEM_GENUS_INSTANCE = 2
} WBEM_GENUS_TYPE;
typedef enum tag_WBEM_CHANGE_FLAG_TYPE {
    WBEM_FLAG_CREATE_OR_UPDATE = 0,
    WBEM_FLAG_UPDATE_ONLY = 0x1,
    WBEM_FLAG_CREATE_ONLY = 0x2,
    WBEM_FLAG_UPDATE_COMPATIBLE = 0x0,
    WBEM_FLAG_UPDATE_SAFE_MODE = 0x20,
    WBEM_FLAG_UPDATE_FORCE_MODE = 0x40,
    WBEM_MASK_UPDATE_MODE = 0x60,
    WBEM_FLAG_ADVISORY = 0x10000
} WBEM_CHANGE_FLAG_TYPE;
typedef enum tag_WBEM_GENERIC_FLAG_TYPE {
    WBEM_FLAG_RETURN_IMMEDIATELY = 0x10,
    WBEM_FLAG_RETURN_WBEM_COMPLETE = 0,
    WBEM_FLAG_BIDIRECTIONAL = 0,
    WBEM_FLAG_FORWARD_ONLY = 0x20,
    WBEM_FLAG_NO_ERROR_OBJECT = 0x40,
    WBEM_FLAG_RETURN_ERROR_OBJECT = 0,
    WBEM_FLAG_SEND_STATUS = 0x80,
    WBEM_FLAG_DONT_SEND_STATUS = 0,
    WBEM_FLAG_ENSURE_LOCATABLE = 0x100,
    WBEM_FLAG_DIRECT_READ = 0x200,
    WBEM_FLAG_SEND_ONLY_SELECTED = 0,
    WBEM_RETURN_WHEN_COMPLETE = 0,
    WBEM_RETURN_IMMEDIATELY = 0x10,
    WBEM_MASK_RESERVED_FLAGS = 0x1f000,
    WBEM_FLAG_USE_AMENDED_QUALIFIERS = 0x20000,
    WBEM_FLAG_STRONG_VALIDATION = 0x100000
} WBEM_GENERIC_FLAG_TYPE;
typedef enum tag_WBEM_STATUS_TYPE {
    WBEM_STATUS_COMPLETE = 0,
    WBEM_STATUS_REQUIREMENTS = 1,
    WBEM_STATUS_PROGRESS = 2,
    WBEM_STATUS_LOGGING_INFORMATION = 0x100,
    WBEM_STATUS_LOGGING_INFORMATION_PROVIDER = 0x200,
    WBEM_STATUS_LOGGING_INFORMATION_HOST = 0x400,
    WBEM_STATUS_LOGGING_INFORMATION_REPOSITORY = 0x800,
    WBEM_STATUS_LOGGING_INFORMATION_ESS = 0x1000
} WBEM_STATUS_TYPE;
typedef enum tag_WBEM_TIMEOUT_TYPE {
    WBEM_NO_WAIT = 0,
    WBEM_INFINITE = 0xffffffff
} WBEM_TIMEOUT_TYPE;
typedef enum tag_WBEM_CONDITION_FLAG_TYPE {
    WBEM_FLAG_ALWAYS = 0,
    WBEM_FLAG_ONLY_IF_TRUE = 0x1,
    WBEM_FLAG_ONLY_IF_FALSE = 0x2,
    WBEM_FLAG_ONLY_IF_IDENTICAL = 0x3,
    WBEM_MASK_PRIMARY_CONDITION = 0x3,
    WBEM_FLAG_KEYS_ONLY = 0x4,
    WBEM_FLAG_REFS_ONLY = 0x8,
    WBEM_FLAG_LOCAL_ONLY = 0x10,
    WBEM_FLAG_PROPAGATED_ONLY = 0x20,
    WBEM_FLAG_SYSTEM_ONLY = 0x30,
    WBEM_FLAG_NONSYSTEM_ONLY = 0x40,
    WBEM_MASK_CONDITION_ORIGIN = 0x70,
    WBEM_FLAG_CLASS_OVERRIDES_ONLY = 0x100,
    WBEM_FLAG_CLASS_LOCAL_AND_OVERRIDES = 0x200,
    WBEM_MASK_CLASS_CONDITION = 0x300
} WBEM_CONDITION_FLAG_TYPE;
typedef enum tag_WBEM_FLAVOR_TYPE {
    WBEM_FLAVOR_DONT_PROPAGATE = 0,
    WBEM_FLAVOR_FLAG_PROPAGATE_TO_INSTANCE = 0x1,
    WBEM_FLAVOR_FLAG_PROPAGATE_TO_DERIVED_CLASS = 0x2,
    WBEM_FLAVOR_MASK_PROPAGATION = 0xf,
    WBEM_FLAVOR_OVERRIDABLE = 0,
    WBEM_FLAVOR_NOT_OVERRIDABLE = 0x10,
    WBEM_FLAVOR_MASK_PERMISSIONS = 0x10,
    WBEM_FLAVOR_ORIGIN_LOCAL = 0,
    WBEM_FLAVOR_ORIGIN_PROPAGATED = 0x20,
    WBEM_FLAVOR_ORIGIN_SYSTEM = 0x40,
    WBEM_FLAVOR_MASK_ORIGIN = 0x60,
    WBEM_FLAVOR_NOT_AMENDED = 0,
    WBEM_FLAVOR_AMENDED = 0x80,
    WBEM_FLAVOR_MASK_AMENDED = 0x80
} WBEM_FLAVOR_TYPE;
typedef enum tag_WBEM_QUERY_FLAG_TYPE {
    WBEM_FLAG_DEEP = 0,
    WBEM_FLAG_SHALLOW = 1,
    WBEM_FLAG_PROTOTYPE = 2
} WBEM_QUERY_FLAG_TYPE;
typedef enum tag_WBEM_SECURITY_FLAGS {
    WBEM_ENABLE = 1,
    WBEM_METHOD_EXECUTE = 2,
    WBEM_FULL_WRITE_REP = 4,
    WBEM_PARTIAL_WRITE_REP = 8,
    WBEM_WRITE_PROVIDER = 0x10,
    WBEM_REMOTE_ACCESS = 0x20,
    WBEM_RIGHT_SUBSCRIBE = 0x40,
    WBEM_RIGHT_PUBLISH = 0x80
} WBEM_SECURITY_FLAGS;
typedef enum tag_WBEM_LIMITATION_FLAG_TYPE {
    WBEM_FLAG_EXCLUDE_OBJECT_QUALIFIERS = 0x10,
    WBEM_FLAG_EXCLUDE_PROPERTY_QUALIFIERS = 0x20
} WBEM_LIMITATION_FLAG_TYPE;
typedef enum tag_WBEM_TEXT_FLAG_TYPE {
    WBEM_FLAG_NO_FLAVORS = 0x1
} WBEM_TEXT_FLAG_TYPE;
typedef enum tag_WBEM_COMPARISON_FLAG {
    WBEM_COMPARISON_INCLUDE_ALL = 0,
    WBEM_FLAG_IGNORE_QUALIFIERS = 0x1,
    WBEM_FLAG_IGNORE_OBJECT_SOURCE = 0x2,
    WBEM_FLAG_IGNORE_DEFAULT_VALUES = 0x4,
    WBEM_FLAG_IGNORE_CLASS = 0x8,
    WBEM_FLAG_IGNORE_CASE = 0x10,
    WBEM_FLAG_IGNORE_FLAVOR = 0x20
} WBEM_COMPARISON_FLAG;
typedef enum tag_WBEM_LOCKING {
    WBEM_FLAG_ALLOW_READ = 0x1
} WBEM_LOCKING_FLAG_TYPE;
typedef enum tag_CIMTYPE_ENUMERATION {
    CIM_ILLEGAL = 0xfff,
    CIM_EMPTY = 0,
    CIM_SINT8 = 16,
    CIM_UINT8 = 17,
    CIM_SINT16 = 2,
    CIM_UINT16 = 18,
    CIM_SINT32 = 3,
    CIM_UINT32 = 19,
    CIM_SINT64 = 20,
    CIM_UINT64 = 21,
    CIM_REAL32 = 4,
    CIM_REAL64 = 5,
    CIM_BOOLEAN = 11,
    CIM_STRING = 8,
    CIM_DATETIME = 101,
    CIM_REFERENCE = 102,
    CIM_CHAR16 = 103,
    CIM_OBJECT = 13,
    CIM_FLAG_ARRAY = 0x2000
} CIMTYPE_ENUMERATION;
typedef enum tag_WBEM_BACKUP_RESTORE_FLAGS {
    WBEM_FLAG_BACKUP_RESTORE_DEFAULT = 0,
    WBEM_FLAG_BACKUP_RESTORE_FORCE_SHUTDOWN = 1
} WBEM_BACKUP_RESTORE_FLAGS;
typedef enum tag_WBEM_REFRESHER_FLAGS {
    WBEM_FLAG_REFRESH_AUTO_RECONNECT = 0,
    WBEM_FLAG_REFRESH_NO_AUTO_RECONNECT = 1
} WBEM_REFRESHER_FLAGS;
typedef enum tag_WBEM_SHUTDOWN_FLAGS {
    WBEM_SHUTDOWN_UNLOAD_COMPONENT = 1,
    WBEM_SHUTDOWN_WMI = 2,
    WBEM_SHUTDOWN_OS = 3
} WBEM_SHUTDOWN_FLAGS;
typedef enum tag_WBEMSTATUS_FORMAT {
    WBEMSTATUS_FORMAT_NEWLINE = 0,
    WBEMSTATUS_FORMAT_NO_NEWLINE = 1
} WBEMSTATUS_FORMAT;
typedef enum tag_WBEM_LIMITS {
    WBEM_MAX_IDENTIFIER = 0x1000,
    WBEM_MAX_QUERY = 0x4000,
    WBEM_MAX_PATH = 0x2000,
    WBEM_MAX_OBJECT_NESTING = 64,
    WBEM_MAX_USER_PROPERTIES = 1024
} WBEM_LIMITS;
typedef enum tag_WBEMSTATUS {
    WBEM_NO_ERROR = 0,
    WBEM_S_NO_ERROR = 0,
    WBEM_S_SAME = 0,
    WBEM_S_FALSE = 1,
    WBEM_S_ALREADY_EXISTS = 0x40001,
    WBEM_S_RESET_TO_DEFAULT = 0x40002,
    WBEM_S_DIFFERENT = 0x40003,
    WBEM_S_TIMEDOUT = 0x40004,
    WBEM_S_NO_MORE_DATA = 0x40005,
    WBEM_S_OPERATION_CANCELLED = 0x40006,
    WBEM_S_PENDING = 0x40007,
    WBEM_S_DUPLICATE_OBJECTS = 0x40008,
    WBEM_S_ACCESS_DENIED = 0x40009,
    WBEM_S_PARTIAL_RESULTS = 0x40010,
    WBEM_S_SOURCE_NOT_AVAILABLE = 0x40017,
    WBEM_E_FAILED = 0x80041001,
    WBEM_E_NOT_FOUND = 0x80041002,
    WBEM_E_ACCESS_DENIED = 0x80041003,
    WBEM_E_PROVIDER_FAILURE = 0x80041004,
    WBEM_E_TYPE_MISMATCH = 0x80041005,
    WBEM_E_OUT_OF_MEMORY = 0x80041006,
    WBEM_E_INVALID_CONTEXT = 0x80041007,
    WBEM_E_INVALID_PARAMETER = 0x80041008,
    WBEM_E_NOT_AVAILABLE = 0x80041009,
    WBEM_E_CRITICAL_ERROR = 0x8004100a,
    WBEM_E_INVALID_STREAM = 0x8004100b,
    WBEM_E_NOT_SUPPORTED = 0x8004100c,
    WBEM_E_INVALID_SUPERCLASS = 0x8004100d,
    WBEM_E_INVALID_NAMESPACE = 0x8004100e,
    WBEM_E_INVALID_OBJECT = 0x8004100f,
    WBEM_E_INVALID_CLASS = 0x80041010,
    WBEM_E_PROVIDER_NOT_FOUND = 0x80041011,
    WBEM_E_INVALID_PROVIDER_REGISTRATION = 0x80041012,
    WBEM_E_PROVIDER_LOAD_FAILURE = 0x80041013,
    WBEM_E_INITIALIZATION_FAILURE = 0x80041014,
    WBEM_E_TRANSPORT_FAILURE = 0x80041015,
    WBEM_E_INVALID_OPERATION = 0x80041016,
    WBEM_E_INVALID_QUERY = 0x80041017,
    WBEM_E_INVALID_QUERY_TYPE = 0x80041018,
    WBEM_E_ALREADY_EXISTS = 0x80041019,
    WBEM_E_OVERRIDE_NOT_ALLOWED = 0x8004101a,
    WBEM_E_PROPAGATED_QUALIFIER = 0x8004101b,
    WBEM_E_PROPAGATED_PROPERTY = 0x8004101c,
    WBEM_E_UNEXPECTED = 0x8004101d,
    WBEM_E_ILLEGAL_OPERATION = 0x8004101e,
    WBEM_E_CANNOT_BE_KEY = 0x8004101f,
    WBEM_E_INCOMPLETE_CLASS = 0x80041020,
    WBEM_E_INVALID_SYNTAX = 0x80041021,
    WBEM_E_NONDECORATED_OBJECT = 0x80041022,
    WBEM_E_READ_ONLY = 0x80041023,
    WBEM_E_PROVIDER_NOT_CAPABLE = 0x80041024,
    WBEM_E_CLASS_HAS_CHILDREN = 0x80041025,
    WBEM_E_CLASS_HAS_INSTANCES = 0x80041026,
    WBEM_E_QUERY_NOT_IMPLEMENTED = 0x80041027,
    WBEM_E_ILLEGAL_NULL = 0x80041028,
    WBEM_E_INVALID_QUALIFIER_TYPE = 0x80041029,
    WBEM_E_INVALID_PROPERTY_TYPE = 0x8004102a,
    WBEM_E_VALUE_OUT_OF_RANGE = 0x8004102b,
    WBEM_E_CANNOT_BE_SINGLETON = 0x8004102c,
    WBEM_E_INVALID_CIM_TYPE = 0x8004102d,
    WBEM_E_INVALID_METHOD = 0x8004102e,
    WBEM_E_INVALID_METHOD_PARAMETERS = 0x8004102f,
    WBEM_E_SYSTEM_PROPERTY = 0x80041030,
    WBEM_E_INVALID_PROPERTY = 0x80041031,
    WBEM_E_CALL_CANCELLED = 0x80041032,
    WBEM_E_SHUTTING_DOWN = 0x80041033,
    WBEM_E_PROPAGATED_METHOD = 0x80041034,
    WBEM_E_UNSUPPORTED_PARAMETER = 0x80041035,
    WBEM_E_MISSING_PARAMETER_ID = 0x80041036,
    WBEM_E_INVALID_PARAMETER_ID = 0x80041037,
    WBEM_E_NONCONSECUTIVE_PARAMETER_IDS = 0x80041038,
    WBEM_E_PARAMETER_ID_ON_RETVAL = 0x80041039,
    WBEM_E_INVALID_OBJECT_PATH = 0x8004103a,
    WBEM_E_OUT_OF_DISK_SPACE = 0x8004103b,
    WBEM_E_BUFFER_TOO_SMALL = 0x8004103c,
    WBEM_E_UNSUPPORTED_PUT_EXTENSION = 0x8004103d,
    WBEM_E_UNKNOWN_OBJECT_TYPE = 0x8004103e,
    WBEM_E_UNKNOWN_PACKET_TYPE = 0x8004103f,
    WBEM_E_MARSHAL_VERSION_MISMATCH = 0x80041040,
    WBEM_E_MARSHAL_INVALID_SIGNATURE = 0x80041041,
    WBEM_E_INVALID_QUALIFIER = 0x80041042,
    WBEM_E_INVALID_DUPLICATE_PARAMETER = 0x80041043,
    WBEM_E_TOO_MUCH_DATA = 0x80041044,
    WBEM_E_SERVER_TOO_BUSY = 0x80041045,
    WBEM_E_INVALID_FLAVOR = 0x80041046,
    WBEM_E_CIRCULAR_REFERENCE = 0x80041047,
    WBEM_E_UNSUPPORTED_CLASS_UPDATE = 0x80041048,
    WBEM_E_CANNOT_CHANGE_KEY_INHERITANCE = 0x80041049,
    WBEM_E_CANNOT_CHANGE_INDEX_INHERITANCE = 0x80041050,
    WBEM_E_TOO_MANY_PROPERTIES = 0x80041051,
    WBEM_E_UPDATE_TYPE_MISMATCH = 0x80041052,
    WBEM_E_UPDATE_OVERRIDE_NOT_ALLOWED = 0x80041053,
    WBEM_E_UPDATE_PROPAGATED_METHOD = 0x80041054,
    WBEM_E_METHOD_NOT_IMPLEMENTED = 0x80041055,
    WBEM_E_METHOD_DISABLED = 0x80041056,
    WBEM_E_REFRESHER_BUSY = 0x80041057,
    WBEM_E_UNPARSABLE_QUERY = 0x80041058,
    WBEM_E_NOT_EVENT_CLASS = 0x80041059,
    WBEM_E_MISSING_GROUP_WITHIN = 0x8004105a,
    WBEM_E_MISSING_AGGREGATION_LIST = 0x8004105b,
    WBEM_E_PROPERTY_NOT_AN_OBJECT = 0x8004105c,
    WBEM_E_AGGREGATING_BY_OBJECT = 0x8004105d,
    WBEM_E_UNINTERPRETABLE_PROVIDER_QUERY = 0x8004105f,
    WBEM_E_BACKUP_RESTORE_WINMGMT_RUNNING = 0x80041060,
    WBEM_E_QUEUE_OVERFLOW = 0x80041061,
    WBEM_E_PRIVILEGE_NOT_HELD = 0x80041062,
    WBEM_E_INVALID_OPERATOR = 0x80041063,
    WBEM_E_LOCAL_CREDENTIALS = 0x80041064,
    WBEM_E_CANNOT_BE_ABSTRACT = 0x80041065,
    WBEM_E_AMENDED_OBJECT = 0x80041066,
    WBEM_E_CLIENT_TOO_SLOW = 0x80041067,
    WBEM_E_NULL_SECURITY_DESCRIPTOR = 0x80041068,
    WBEM_E_TIMED_OUT = 0x80041069,
    WBEM_E_INVALID_ASSOCIATION = 0x8004106a,
    WBEM_E_AMBIGUOUS_OPERATION = 0x8004106b,
    WBEM_E_QUOTA_VIOLATION = 0x8004106c,
    WBEM_E_RESERVED_001 = 0x8004106d,
    WBEM_E_RESERVED_002 = 0x8004106e,
    WBEM_E_UNSUPPORTED_LOCALE = 0x8004106f,
    WBEM_E_HANDLE_OUT_OF_DATE = 0x80041070,
    WBEM_E_CONNECTION_FAILED = 0x80041071,
    WBEM_E_INVALID_HANDLE_REQUEST = 0x80041072,
    WBEM_E_PROPERTY_NAME_TOO_WIDE = 0x80041073,
    WBEM_E_CLASS_NAME_TOO_WIDE = 0x80041074,
    WBEM_E_METHOD_NAME_TOO_WIDE = 0x80041075,
    WBEM_E_QUALIFIER_NAME_TOO_WIDE = 0x80041076,
    WBEM_E_RERUN_COMMAND = 0x80041077,
    WBEM_E_DATABASE_VER_MISMATCH = 0x80041078,
    WBEM_E_VETO_DELETE = 0x80041079,
    WBEM_E_VETO_PUT = 0x8004107a,
    WBEM_E_INVALID_LOCALE = 0x80041080,
    WBEM_E_PROVIDER_SUSPENDED = 0x80041081,
    WBEM_E_SYNCHRONIZATION_REQUIRED = 0x80041082,
    WBEM_E_NO_SCHEMA = 0x80041083,
    WBEM_E_PROVIDER_ALREADY_REGISTERED = 0x80041084,
    WBEM_E_PROVIDER_NOT_REGISTERED = 0x80041085,
    WBEM_E_FATAL_TRANSPORT_ERROR = 0x80041086,
    WBEM_E_ENCRYPTED_CONNECTION_REQUIRED = 0x80041087,
    WBEM_E_PROVIDER_TIMED_OUT = 0x80041088,
    WBEM_E_NO_KEY = 0x80041089,
    WBEM_E_PROVIDER_DISABLED = 0x8004108a,
    WBEMESS_E_REGISTRATION_TOO_BROAD = 0x80042001,
    WBEMESS_E_REGISTRATION_TOO_PRECISE = 0x80042002,
    WBEMESS_E_AUTHZ_NOT_PRIVILEGED = 0x80042003,
    WBEMMOF_E_EXPECTED_QUALIFIER_NAME = 0x80044001,
    WBEMMOF_E_EXPECTED_SEMI = 0x80044002,
    WBEMMOF_E_EXPECTED_OPEN_BRACE = 0x80044003,
    WBEMMOF_E_EXPECTED_CLOSE_BRACE = 0x80044004,
    WBEMMOF_E_EXPECTED_CLOSE_BRACKET = 0x80044005,
    WBEMMOF_E_EXPECTED_CLOSE_PAREN = 0x80044006,
    WBEMMOF_E_ILLEGAL_CONSTANT_VALUE = 0x80044007,
    WBEMMOF_E_EXPECTED_TYPE_IDENTIFIER = 0x80044008,
    WBEMMOF_E_EXPECTED_OPEN_PAREN = 0x80044009,
    WBEMMOF_E_UNRECOGNIZED_TOKEN = 0x8004400a,
    WBEMMOF_E_UNRECOGNIZED_TYPE = 0x8004400b,
    WBEMMOF_E_EXPECTED_PROPERTY_NAME = 0x8004400c,
    WBEMMOF_E_TYPEDEF_NOT_SUPPORTED = 0x8004400d,
    WBEMMOF_E_UNEXPECTED_ALIAS = 0x8004400e,
    WBEMMOF_E_UNEXPECTED_ARRAY_INIT = 0x8004400f,
    WBEMMOF_E_INVALID_AMENDMENT_SYNTAX = 0x80044010,
    WBEMMOF_E_INVALID_DUPLICATE_AMENDMENT = 0x80044011,
    WBEMMOF_E_INVALID_PRAGMA = 0x80044012,
    WBEMMOF_E_INVALID_NAMESPACE_SYNTAX = 0x80044013,
    WBEMMOF_E_EXPECTED_CLASS_NAME = 0x80044014,
    WBEMMOF_E_TYPE_MISMATCH = 0x80044015,
    WBEMMOF_E_EXPECTED_ALIAS_NAME = 0x80044016,
    WBEMMOF_E_INVALID_CLASS_DECLARATION = 0x80044017,
    WBEMMOF_E_INVALID_INSTANCE_DECLARATION = 0x80044018,
    WBEMMOF_E_EXPECTED_DOLLAR = 0x80044019,
    WBEMMOF_E_CIMTYPE_QUALIFIER = 0x8004401a,
    WBEMMOF_E_DUPLICATE_PROPERTY = 0x8004401b,
    WBEMMOF_E_INVALID_NAMESPACE_SPECIFICATION = 0x8004401c,
    WBEMMOF_E_OUT_OF_RANGE = 0x8004401d,
    WBEMMOF_E_INVALID_FILE = 0x8004401e,
    WBEMMOF_E_ALIASES_IN_EMBEDDED = 0x8004401f,
    WBEMMOF_E_NULL_ARRAY_ELEM = 0x80044020,
    WBEMMOF_E_DUPLICATE_QUALIFIER = 0x80044021,
    WBEMMOF_E_EXPECTED_FLAVOR_TYPE = 0x80044022,
    WBEMMOF_E_INCOMPATIBLE_FLAVOR_TYPES = 0x80044023,
    WBEMMOF_E_MULTIPLE_ALIASES = 0x80044024,
    WBEMMOF_E_INCOMPATIBLE_FLAVOR_TYPES2 = 0x80044025,
    WBEMMOF_E_NO_ARRAYS_RETURNED = 0x80044026,
    WBEMMOF_E_MUST_BE_IN_OR_OUT = 0x80044027,
    WBEMMOF_E_INVALID_FLAGS_SYNTAX = 0x80044028,
    WBEMMOF_E_EXPECTED_BRACE_OR_BAD_TYPE = 0x80044029,
    WBEMMOF_E_UNSUPPORTED_CIMV22_QUAL_VALUE = 0x8004402a,
    WBEMMOF_E_UNSUPPORTED_CIMV22_DATA_TYPE = 0x8004402b,
    WBEMMOF_E_INVALID_DELETEINSTANCE_SYNTAX = 0x8004402c,
    WBEMMOF_E_INVALID_QUALIFIER_SYNTAX = 0x8004402d,
    WBEMMOF_E_QUALIFIER_USED_OUTSIDE_SCOPE = 0x8004402e,
    WBEMMOF_E_ERROR_CREATING_TEMP_FILE = 0x8004402f,
    WBEMMOF_E_ERROR_INVALID_INCLUDE_FILE = 0x80044030,
    WBEMMOF_E_INVALID_DELETECLASS_SYNTAX = 0x80044031
} WBEMSTATUS;
typedef enum tag_WMI_OBJ_TEXT {
    WMI_OBJ_TEXT_CIM_DTD_2_0 = 1,
    WMI_OBJ_TEXT_WMI_DTD_2_0 = 2,
    WMI_OBJ_TEXT_WMI_EXT1 = 3,
    WMI_OBJ_TEXT_WMI_EXT2 = 4,
    WMI_OBJ_TEXT_WMI_EXT3 = 5,
    WMI_OBJ_TEXT_WMI_EXT4 = 6,
    WMI_OBJ_TEXT_WMI_EXT5 = 7,
    WMI_OBJ_TEXT_WMI_EXT6 = 8,
    WMI_OBJ_TEXT_WMI_EXT7 = 9,
    WMI_OBJ_TEXT_WMI_EXT8 = 10,
    WMI_OBJ_TEXT_WMI_EXT9 = 11,
    WMI_OBJ_TEXT_WMI_EXT10 = 12,
    WMI_OBJ_TEXT_LAST = 13
} WMI_OBJ_TEXT;
typedef enum tag_WBEM_COMPILER_OPTIONS {
    WBEM_FLAG_CHECK_ONLY = 0x1,
    WBEM_FLAG_AUTORECOVER = 0x2,
    WBEM_FLAG_WMI_CHECK = 0x4,
    WBEM_FLAG_CONSOLE_PRINT = 0x8,
    WBEM_FLAG_DONT_ADD_TO_LIST = 0x10,
    WBEM_FLAG_SPLIT_FILES = 0x20,
    WBEM_FLAG_STORE_FILE = 0x100
} WBEM_COMPILER_OPTIONS;
typedef enum tag_WBEM_CONNECT_OPTIONS {
    WBEM_FLAG_CONNECT_REPOSITORY_ONLY = 0x40,
    WBEM_FLAG_CONNECT_USE_MAX_WAIT = 0x80,
    WBEM_FLAG_CONNECT_PROVIDERS = 0x100
} WBEM_CONNECT_OPTIONS;
typedef enum tag_WBEM_UNSECAPP_FLAG_TYPE {
    WBEM_FLAG_UNSECAPP_DEFAULT_CHECK_ACCESS = 0,
    WBEM_FLAG_UNSECAPP_CHECK_ACCESS = 1,
    WBEM_FLAG_UNSECAPP_DONT_CHECK_ACCESS = 2
} WBEM_UNSECAPP_FLAG_TYPE;
typedef enum tag_WBEM_INFORMATION_FLAG_TYPE {
    WBEM_FLAG_SHORT_NAME = 0x1,
    WBEM_FLAG_LONG_NAME = 0x2
} WBEM_INFORMATION_FLAG_TYPE;
typedef struct tag_CompileStatusInfo {
    LONG lPhaseError;
    HRESULT hRes;
    LONG ObjectNum;
    LONG FirstLine;
    LONG LastLine;
    DWORD dwOutFlags;
} WBEM_COMPILE_STATUS_INFO;
typedef LONG CIMTYPE;
/*****************************************************************************
 * WbemBackupRestore coclass
 */

DEFINE_GUID(CLSID_WbemBackupRestore, 0xc49e32c6, 0xbc8b, 0x11d2, 0x85,0xd4, 0x00,0x10,0x5a,0x1f,0x83,0x04);

#ifdef __cplusplus
class DECLSPEC_UUID("c49e32c6-bc8b-11d2-85d4-00105a1f8304") WbemBackupRestore;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemBackupRestore, 0xc49e32c6, 0xbc8b, 0x11d2, 0x85,0xd4, 0x00,0x10,0x5a,0x1f,0x83,0x04)
#endif
#endif

/*****************************************************************************
 * WbemClassObject coclass
 */

DEFINE_GUID(CLSID_WbemClassObject, 0x9a653086, 0x174f, 0x11d2, 0xb5,0xf9, 0x00,0x10,0x4b,0x70,0x3e,0xfd);

#ifdef __cplusplus
class DECLSPEC_UUID("9a653086-174f-11d2-b5f9-00104b703efd") WbemClassObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemClassObject, 0x9a653086, 0x174f, 0x11d2, 0xb5,0xf9, 0x00,0x10,0x4b,0x70,0x3e,0xfd)
#endif
#endif

/*****************************************************************************
 * WbemContext coclass
 */

DEFINE_GUID(CLSID_WbemContext, 0x674b6698, 0xee92, 0x11d0, 0xad,0x71, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);

#ifdef __cplusplus
class DECLSPEC_UUID("674b6698-ee92-11d0-ad71-00c04fd8fdff") WbemContext;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemContext, 0x674b6698, 0xee92, 0x11d0, 0xad,0x71, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#endif

/*****************************************************************************
 * WbemLocator coclass
 */

DEFINE_GUID(CLSID_WbemLocator, 0x4590f811, 0x1d3a, 0x11d0, 0x89,0x1f, 0x00,0xaa,0x00,0x4b,0x2e,0x24);

#ifdef __cplusplus
class DECLSPEC_UUID("4590f811-1d3a-11d0-891f-00aa004b2e24") WbemLocator;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemLocator, 0x4590f811, 0x1d3a, 0x11d0, 0x89,0x1f, 0x00,0xaa,0x00,0x4b,0x2e,0x24)
#endif
#endif

/*****************************************************************************
 * WbemStatusCodeText coclass
 */

DEFINE_GUID(CLSID_WbemStatusCodeText, 0xeb87e1bd, 0x3233, 0x11d2, 0xae,0xc9, 0x00,0xc0,0x4f,0xb6,0x88,0x20);

#ifdef __cplusplus
class DECLSPEC_UUID("eb87e1bd-3233-11d2-aec9-00c04fb68820") WbemStatusCodeText;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemStatusCodeText, 0xeb87e1bd, 0x3233, 0x11d2, 0xae,0xc9, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#endif

/*****************************************************************************
 * UnsecuredApartment coclass
 */

DEFINE_GUID(CLSID_UnsecuredApartment, 0x49bd2028, 0x1523, 0x11d1, 0xad,0x79, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);

#ifdef __cplusplus
class DECLSPEC_UUID("49bd2028-1523-11d1-ad79-00c04fd8fdff") UnsecuredApartment;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UnsecuredApartment, 0x49bd2028, 0x1523, 0x11d1, 0xad,0x79, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#endif

/*****************************************************************************
 * MofCompiler coclass
 */

DEFINE_GUID(CLSID_MofCompiler, 0x6daf9757, 0x2e37, 0x11d2, 0xae,0xc9, 0x00,0xc0,0x4f,0xb6,0x88,0x20);

#ifdef __cplusplus
class DECLSPEC_UUID("6daf9757-2e37-11d2-aec9-00c04fb68820") MofCompiler;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(MofCompiler, 0x6daf9757, 0x2e37, 0x11d2, 0xae,0xc9, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#endif

/*****************************************************************************
 * WbemObjectTextSrc coclass
 */

DEFINE_GUID(CLSID_WbemObjectTextSrc, 0x8d1c559d, 0x84f0, 0x4bb3, 0xa7,0xd5, 0x56,0xa7,0x43,0x5a,0x9b,0xa6);

#ifdef __cplusplus
class DECLSPEC_UUID("8d1c559d-84f0-4bb3-a7d5-56a7435a9ba6") WbemObjectTextSrc;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemObjectTextSrc, 0x8d1c559d, 0x84f0, 0x4bb3, 0xa7,0xd5, 0x56,0xa7,0x43,0x5a,0x9b,0xa6)
#endif
#endif

/*****************************************************************************
 * WbemRefresher coclass
 */

DEFINE_GUID(CLSID_WbemRefresher, 0xc71566f2, 0x561e, 0x11d1, 0xad,0x87, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);

#ifdef __cplusplus
class DECLSPEC_UUID("c71566f2-561e-11d1-ad87-00c04fd8fdff") WbemRefresher;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemRefresher, 0xc71566f2, 0x561e, 0x11d1, 0xad,0x87, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#endif

#endif /* __WbemClient_v1_LIBRARY_DEFINED__ */
/*****************************************************************************
 * IWbemClassObject interface
 */
#ifndef __IWbemClassObject_INTERFACE_DEFINED__
#define __IWbemClassObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemClassObject, 0xdc12a681, 0x737f, 0x11cf, 0x88,0x4d, 0x00,0xaa,0x00,0x4b,0x2e,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dc12a681-737f-11cf-884d-00aa004b2e24")
IWbemClassObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetQualifierSet(
        IWbemQualifierSet **ppQualSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE Get(
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pVal,
        CIMTYPE *pType,
        LONG *plFlavor) = 0;

    virtual HRESULT STDMETHODCALLTYPE Put(
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pVal,
        CIMTYPE Type) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        LPCWSTR wszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNames(
        LPCWSTR wszQualifierName,
        LONG lFlags,
        VARIANT *pQualifierVal,
        SAFEARRAY **pNames) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginEnumeration(
        LONG lEnumFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        LONG lFlags,
        BSTR *strName,
        VARIANT *pVal,
        CIMTYPE *pType,
        LONG *plFlavor) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndEnumeration(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyQualifierSet(
        LPCWSTR wszProperty,
        IWbemQualifierSet **ppQualSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IWbemClassObject **ppCopy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectText(
        LONG lFlags,
        BSTR *pstrObjectText) = 0;

    virtual HRESULT STDMETHODCALLTYPE SpawnDerivedClass(
        LONG lFlags,
        IWbemClassObject **ppNewClass) = 0;

    virtual HRESULT STDMETHODCALLTYPE SpawnInstance(
        LONG lFlags,
        IWbemClassObject **ppNewInstance) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompareTo(
        LONG lFlags,
        IWbemClassObject *pCompareTo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyOrigin(
        LPCWSTR wszName,
        BSTR *pstrClassName) = 0;

    virtual HRESULT STDMETHODCALLTYPE InheritsFrom(
        LPCWSTR strAncestor) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMethod(
        LPCWSTR wszName,
        LONG lFlags,
        IWbemClassObject **ppInSignature,
        IWbemClassObject **ppOutSignature) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutMethod(
        LPCWSTR wszName,
        LONG lFlags,
        IWbemClassObject *pInSignature,
        IWbemClassObject *pOutSignature) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteMethod(
        LPCWSTR wszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginMethodEnumeration(
        LONG lEnumFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE NextMethod(
        LONG lFlags,
        BSTR *pstrName,
        IWbemClassObject **ppInSignature,
        IWbemClassObject **ppOutSignature) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndMethodEnumeration(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMethodQualifierSet(
        LPCWSTR wszMethod,
        IWbemQualifierSet **ppQualSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMethodOrigin(
        LPCWSTR wszMethodName,
        BSTR *pstrClassName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemClassObject, 0xdc12a681, 0x737f, 0x11cf, 0x88,0x4d, 0x00,0xaa,0x00,0x4b,0x2e,0x24)
#endif
#else
typedef struct IWbemClassObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemClassObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemClassObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemClassObject *This);

    /*** IWbemClassObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetQualifierSet)(
        IWbemClassObject *This,
        IWbemQualifierSet **ppQualSet);

    HRESULT (STDMETHODCALLTYPE *Get)(
        IWbemClassObject *This,
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pVal,
        CIMTYPE *pType,
        LONG *plFlavor);

    HRESULT (STDMETHODCALLTYPE *Put)(
        IWbemClassObject *This,
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pVal,
        CIMTYPE Type);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IWbemClassObject *This,
        LPCWSTR wszName);

    HRESULT (STDMETHODCALLTYPE *GetNames)(
        IWbemClassObject *This,
        LPCWSTR wszQualifierName,
        LONG lFlags,
        VARIANT *pQualifierVal,
        SAFEARRAY **pNames);

    HRESULT (STDMETHODCALLTYPE *BeginEnumeration)(
        IWbemClassObject *This,
        LONG lEnumFlags);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IWbemClassObject *This,
        LONG lFlags,
        BSTR *strName,
        VARIANT *pVal,
        CIMTYPE *pType,
        LONG *plFlavor);

    HRESULT (STDMETHODCALLTYPE *EndEnumeration)(
        IWbemClassObject *This);

    HRESULT (STDMETHODCALLTYPE *GetPropertyQualifierSet)(
        IWbemClassObject *This,
        LPCWSTR wszProperty,
        IWbemQualifierSet **ppQualSet);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWbemClassObject *This,
        IWbemClassObject **ppCopy);

    HRESULT (STDMETHODCALLTYPE *GetObjectText)(
        IWbemClassObject *This,
        LONG lFlags,
        BSTR *pstrObjectText);

    HRESULT (STDMETHODCALLTYPE *SpawnDerivedClass)(
        IWbemClassObject *This,
        LONG lFlags,
        IWbemClassObject **ppNewClass);

    HRESULT (STDMETHODCALLTYPE *SpawnInstance)(
        IWbemClassObject *This,
        LONG lFlags,
        IWbemClassObject **ppNewInstance);

    HRESULT (STDMETHODCALLTYPE *CompareTo)(
        IWbemClassObject *This,
        LONG lFlags,
        IWbemClassObject *pCompareTo);

    HRESULT (STDMETHODCALLTYPE *GetPropertyOrigin)(
        IWbemClassObject *This,
        LPCWSTR wszName,
        BSTR *pstrClassName);

    HRESULT (STDMETHODCALLTYPE *InheritsFrom)(
        IWbemClassObject *This,
        LPCWSTR strAncestor);

    HRESULT (STDMETHODCALLTYPE *GetMethod)(
        IWbemClassObject *This,
        LPCWSTR wszName,
        LONG lFlags,
        IWbemClassObject **ppInSignature,
        IWbemClassObject **ppOutSignature);

    HRESULT (STDMETHODCALLTYPE *PutMethod)(
        IWbemClassObject *This,
        LPCWSTR wszName,
        LONG lFlags,
        IWbemClassObject *pInSignature,
        IWbemClassObject *pOutSignature);

    HRESULT (STDMETHODCALLTYPE *DeleteMethod)(
        IWbemClassObject *This,
        LPCWSTR wszName);

    HRESULT (STDMETHODCALLTYPE *BeginMethodEnumeration)(
        IWbemClassObject *This,
        LONG lEnumFlags);

    HRESULT (STDMETHODCALLTYPE *NextMethod)(
        IWbemClassObject *This,
        LONG lFlags,
        BSTR *pstrName,
        IWbemClassObject **ppInSignature,
        IWbemClassObject **ppOutSignature);

    HRESULT (STDMETHODCALLTYPE *EndMethodEnumeration)(
        IWbemClassObject *This);

    HRESULT (STDMETHODCALLTYPE *GetMethodQualifierSet)(
        IWbemClassObject *This,
        LPCWSTR wszMethod,
        IWbemQualifierSet **ppQualSet);

    HRESULT (STDMETHODCALLTYPE *GetMethodOrigin)(
        IWbemClassObject *This,
        LPCWSTR wszMethodName,
        BSTR *pstrClassName);

    END_INTERFACE
} IWbemClassObjectVtbl;

interface IWbemClassObject {
    CONST_VTBL IWbemClassObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemClassObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemClassObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemClassObject_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemClassObject methods ***/
#define IWbemClassObject_GetQualifierSet(This,ppQualSet) (This)->lpVtbl->GetQualifierSet(This,ppQualSet)
#define IWbemClassObject_Get(This,wszName,lFlags,pVal,pType,plFlavor) (This)->lpVtbl->Get(This,wszName,lFlags,pVal,pType,plFlavor)
#define IWbemClassObject_Put(This,wszName,lFlags,pVal,Type) (This)->lpVtbl->Put(This,wszName,lFlags,pVal,Type)
#define IWbemClassObject_Delete(This,wszName) (This)->lpVtbl->Delete(This,wszName)
#define IWbemClassObject_GetNames(This,wszQualifierName,lFlags,pQualifierVal,pNames) (This)->lpVtbl->GetNames(This,wszQualifierName,lFlags,pQualifierVal,pNames)
#define IWbemClassObject_BeginEnumeration(This,lEnumFlags) (This)->lpVtbl->BeginEnumeration(This,lEnumFlags)
#define IWbemClassObject_Next(This,lFlags,strName,pVal,pType,plFlavor) (This)->lpVtbl->Next(This,lFlags,strName,pVal,pType,plFlavor)
#define IWbemClassObject_EndEnumeration(This) (This)->lpVtbl->EndEnumeration(This)
#define IWbemClassObject_GetPropertyQualifierSet(This,wszProperty,ppQualSet) (This)->lpVtbl->GetPropertyQualifierSet(This,wszProperty,ppQualSet)
#define IWbemClassObject_Clone(This,ppCopy) (This)->lpVtbl->Clone(This,ppCopy)
#define IWbemClassObject_GetObjectText(This,lFlags,pstrObjectText) (This)->lpVtbl->GetObjectText(This,lFlags,pstrObjectText)
#define IWbemClassObject_SpawnDerivedClass(This,lFlags,ppNewClass) (This)->lpVtbl->SpawnDerivedClass(This,lFlags,ppNewClass)
#define IWbemClassObject_SpawnInstance(This,lFlags,ppNewInstance) (This)->lpVtbl->SpawnInstance(This,lFlags,ppNewInstance)
#define IWbemClassObject_CompareTo(This,lFlags,pCompareTo) (This)->lpVtbl->CompareTo(This,lFlags,pCompareTo)
#define IWbemClassObject_GetPropertyOrigin(This,wszName,pstrClassName) (This)->lpVtbl->GetPropertyOrigin(This,wszName,pstrClassName)
#define IWbemClassObject_InheritsFrom(This,strAncestor) (This)->lpVtbl->InheritsFrom(This,strAncestor)
#define IWbemClassObject_GetMethod(This,wszName,lFlags,ppInSignature,ppOutSignature) (This)->lpVtbl->GetMethod(This,wszName,lFlags,ppInSignature,ppOutSignature)
#define IWbemClassObject_PutMethod(This,wszName,lFlags,pInSignature,pOutSignature) (This)->lpVtbl->PutMethod(This,wszName,lFlags,pInSignature,pOutSignature)
#define IWbemClassObject_DeleteMethod(This,wszName) (This)->lpVtbl->DeleteMethod(This,wszName)
#define IWbemClassObject_BeginMethodEnumeration(This,lEnumFlags) (This)->lpVtbl->BeginMethodEnumeration(This,lEnumFlags)
#define IWbemClassObject_NextMethod(This,lFlags,pstrName,ppInSignature,ppOutSignature) (This)->lpVtbl->NextMethod(This,lFlags,pstrName,ppInSignature,ppOutSignature)
#define IWbemClassObject_EndMethodEnumeration(This) (This)->lpVtbl->EndMethodEnumeration(This)
#define IWbemClassObject_GetMethodQualifierSet(This,wszMethod,ppQualSet) (This)->lpVtbl->GetMethodQualifierSet(This,wszMethod,ppQualSet)
#define IWbemClassObject_GetMethodOrigin(This,wszMethodName,pstrClassName) (This)->lpVtbl->GetMethodOrigin(This,wszMethodName,pstrClassName)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemClassObject_QueryInterface(IWbemClassObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemClassObject_AddRef(IWbemClassObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemClassObject_Release(IWbemClassObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemClassObject methods ***/
static inline HRESULT IWbemClassObject_GetQualifierSet(IWbemClassObject* This,IWbemQualifierSet **ppQualSet) {
    return This->lpVtbl->GetQualifierSet(This,ppQualSet);
}
static inline HRESULT IWbemClassObject_Get(IWbemClassObject* This,LPCWSTR wszName,LONG lFlags,VARIANT *pVal,CIMTYPE *pType,LONG *plFlavor) {
    return This->lpVtbl->Get(This,wszName,lFlags,pVal,pType,plFlavor);
}
static inline HRESULT IWbemClassObject_Put(IWbemClassObject* This,LPCWSTR wszName,LONG lFlags,VARIANT *pVal,CIMTYPE Type) {
    return This->lpVtbl->Put(This,wszName,lFlags,pVal,Type);
}
static inline HRESULT IWbemClassObject_Delete(IWbemClassObject* This,LPCWSTR wszName) {
    return This->lpVtbl->Delete(This,wszName);
}
static inline HRESULT IWbemClassObject_GetNames(IWbemClassObject* This,LPCWSTR wszQualifierName,LONG lFlags,VARIANT *pQualifierVal,SAFEARRAY **pNames) {
    return This->lpVtbl->GetNames(This,wszQualifierName,lFlags,pQualifierVal,pNames);
}
static inline HRESULT IWbemClassObject_BeginEnumeration(IWbemClassObject* This,LONG lEnumFlags) {
    return This->lpVtbl->BeginEnumeration(This,lEnumFlags);
}
static inline HRESULT IWbemClassObject_Next(IWbemClassObject* This,LONG lFlags,BSTR *strName,VARIANT *pVal,CIMTYPE *pType,LONG *plFlavor) {
    return This->lpVtbl->Next(This,lFlags,strName,pVal,pType,plFlavor);
}
static inline HRESULT IWbemClassObject_EndEnumeration(IWbemClassObject* This) {
    return This->lpVtbl->EndEnumeration(This);
}
static inline HRESULT IWbemClassObject_GetPropertyQualifierSet(IWbemClassObject* This,LPCWSTR wszProperty,IWbemQualifierSet **ppQualSet) {
    return This->lpVtbl->GetPropertyQualifierSet(This,wszProperty,ppQualSet);
}
static inline HRESULT IWbemClassObject_Clone(IWbemClassObject* This,IWbemClassObject **ppCopy) {
    return This->lpVtbl->Clone(This,ppCopy);
}
static inline HRESULT IWbemClassObject_GetObjectText(IWbemClassObject* This,LONG lFlags,BSTR *pstrObjectText) {
    return This->lpVtbl->GetObjectText(This,lFlags,pstrObjectText);
}
static inline HRESULT IWbemClassObject_SpawnDerivedClass(IWbemClassObject* This,LONG lFlags,IWbemClassObject **ppNewClass) {
    return This->lpVtbl->SpawnDerivedClass(This,lFlags,ppNewClass);
}
static inline HRESULT IWbemClassObject_SpawnInstance(IWbemClassObject* This,LONG lFlags,IWbemClassObject **ppNewInstance) {
    return This->lpVtbl->SpawnInstance(This,lFlags,ppNewInstance);
}
static inline HRESULT IWbemClassObject_CompareTo(IWbemClassObject* This,LONG lFlags,IWbemClassObject *pCompareTo) {
    return This->lpVtbl->CompareTo(This,lFlags,pCompareTo);
}
static inline HRESULT IWbemClassObject_GetPropertyOrigin(IWbemClassObject* This,LPCWSTR wszName,BSTR *pstrClassName) {
    return This->lpVtbl->GetPropertyOrigin(This,wszName,pstrClassName);
}
static inline HRESULT IWbemClassObject_InheritsFrom(IWbemClassObject* This,LPCWSTR strAncestor) {
    return This->lpVtbl->InheritsFrom(This,strAncestor);
}
static inline HRESULT IWbemClassObject_GetMethod(IWbemClassObject* This,LPCWSTR wszName,LONG lFlags,IWbemClassObject **ppInSignature,IWbemClassObject **ppOutSignature) {
    return This->lpVtbl->GetMethod(This,wszName,lFlags,ppInSignature,ppOutSignature);
}
static inline HRESULT IWbemClassObject_PutMethod(IWbemClassObject* This,LPCWSTR wszName,LONG lFlags,IWbemClassObject *pInSignature,IWbemClassObject *pOutSignature) {
    return This->lpVtbl->PutMethod(This,wszName,lFlags,pInSignature,pOutSignature);
}
static inline HRESULT IWbemClassObject_DeleteMethod(IWbemClassObject* This,LPCWSTR wszName) {
    return This->lpVtbl->DeleteMethod(This,wszName);
}
static inline HRESULT IWbemClassObject_BeginMethodEnumeration(IWbemClassObject* This,LONG lEnumFlags) {
    return This->lpVtbl->BeginMethodEnumeration(This,lEnumFlags);
}
static inline HRESULT IWbemClassObject_NextMethod(IWbemClassObject* This,LONG lFlags,BSTR *pstrName,IWbemClassObject **ppInSignature,IWbemClassObject **ppOutSignature) {
    return This->lpVtbl->NextMethod(This,lFlags,pstrName,ppInSignature,ppOutSignature);
}
static inline HRESULT IWbemClassObject_EndMethodEnumeration(IWbemClassObject* This) {
    return This->lpVtbl->EndMethodEnumeration(This);
}
static inline HRESULT IWbemClassObject_GetMethodQualifierSet(IWbemClassObject* This,LPCWSTR wszMethod,IWbemQualifierSet **ppQualSet) {
    return This->lpVtbl->GetMethodQualifierSet(This,wszMethod,ppQualSet);
}
static inline HRESULT IWbemClassObject_GetMethodOrigin(IWbemClassObject* This,LPCWSTR wszMethodName,BSTR *pstrClassName) {
    return This->lpVtbl->GetMethodOrigin(This,wszMethodName,pstrClassName);
}
#endif
#endif

#endif


#endif  /* __IWbemClassObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemQualifierSet interface
 */
#ifndef __IWbemQualifierSet_INTERFACE_DEFINED__
#define __IWbemQualifierSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemQualifierSet, 0xdc12a680, 0x737f, 0x11cf, 0x88,0x4d, 0x00,0xaa,0x00,0x4b,0x2e,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dc12a680-737f-11cf-884d-00aa004b2e24")
IWbemQualifierSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Get(
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pVal,
        LONG *plFlavor) = 0;

    virtual HRESULT STDMETHODCALLTYPE Put(
        LPCWSTR wszName,
        VARIANT *pVal,
        LONG lFlavor) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        LPCWSTR wszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNames(
        LONG lFlags,
        SAFEARRAY **pNames) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginEnumeration(
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        LONG lFlags,
        BSTR *pstrName,
        VARIANT *pVal,
        LONG *plFlavor) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndEnumeration(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemQualifierSet, 0xdc12a680, 0x737f, 0x11cf, 0x88,0x4d, 0x00,0xaa,0x00,0x4b,0x2e,0x24)
#endif
#else
typedef struct IWbemQualifierSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemQualifierSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemQualifierSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemQualifierSet *This);

    /*** IWbemQualifierSet methods ***/
    HRESULT (STDMETHODCALLTYPE *Get)(
        IWbemQualifierSet *This,
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pVal,
        LONG *plFlavor);

    HRESULT (STDMETHODCALLTYPE *Put)(
        IWbemQualifierSet *This,
        LPCWSTR wszName,
        VARIANT *pVal,
        LONG lFlavor);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IWbemQualifierSet *This,
        LPCWSTR wszName);

    HRESULT (STDMETHODCALLTYPE *GetNames)(
        IWbemQualifierSet *This,
        LONG lFlags,
        SAFEARRAY **pNames);

    HRESULT (STDMETHODCALLTYPE *BeginEnumeration)(
        IWbemQualifierSet *This,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IWbemQualifierSet *This,
        LONG lFlags,
        BSTR *pstrName,
        VARIANT *pVal,
        LONG *plFlavor);

    HRESULT (STDMETHODCALLTYPE *EndEnumeration)(
        IWbemQualifierSet *This);

    END_INTERFACE
} IWbemQualifierSetVtbl;

interface IWbemQualifierSet {
    CONST_VTBL IWbemQualifierSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemQualifierSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemQualifierSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemQualifierSet_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemQualifierSet methods ***/
#define IWbemQualifierSet_Get(This,wszName,lFlags,pVal,plFlavor) (This)->lpVtbl->Get(This,wszName,lFlags,pVal,plFlavor)
#define IWbemQualifierSet_Put(This,wszName,pVal,lFlavor) (This)->lpVtbl->Put(This,wszName,pVal,lFlavor)
#define IWbemQualifierSet_Delete(This,wszName) (This)->lpVtbl->Delete(This,wszName)
#define IWbemQualifierSet_GetNames(This,lFlags,pNames) (This)->lpVtbl->GetNames(This,lFlags,pNames)
#define IWbemQualifierSet_BeginEnumeration(This,lFlags) (This)->lpVtbl->BeginEnumeration(This,lFlags)
#define IWbemQualifierSet_Next(This,lFlags,pstrName,pVal,plFlavor) (This)->lpVtbl->Next(This,lFlags,pstrName,pVal,plFlavor)
#define IWbemQualifierSet_EndEnumeration(This) (This)->lpVtbl->EndEnumeration(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemQualifierSet_QueryInterface(IWbemQualifierSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemQualifierSet_AddRef(IWbemQualifierSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemQualifierSet_Release(IWbemQualifierSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemQualifierSet methods ***/
static inline HRESULT IWbemQualifierSet_Get(IWbemQualifierSet* This,LPCWSTR wszName,LONG lFlags,VARIANT *pVal,LONG *plFlavor) {
    return This->lpVtbl->Get(This,wszName,lFlags,pVal,plFlavor);
}
static inline HRESULT IWbemQualifierSet_Put(IWbemQualifierSet* This,LPCWSTR wszName,VARIANT *pVal,LONG lFlavor) {
    return This->lpVtbl->Put(This,wszName,pVal,lFlavor);
}
static inline HRESULT IWbemQualifierSet_Delete(IWbemQualifierSet* This,LPCWSTR wszName) {
    return This->lpVtbl->Delete(This,wszName);
}
static inline HRESULT IWbemQualifierSet_GetNames(IWbemQualifierSet* This,LONG lFlags,SAFEARRAY **pNames) {
    return This->lpVtbl->GetNames(This,lFlags,pNames);
}
static inline HRESULT IWbemQualifierSet_BeginEnumeration(IWbemQualifierSet* This,LONG lFlags) {
    return This->lpVtbl->BeginEnumeration(This,lFlags);
}
static inline HRESULT IWbemQualifierSet_Next(IWbemQualifierSet* This,LONG lFlags,BSTR *pstrName,VARIANT *pVal,LONG *plFlavor) {
    return This->lpVtbl->Next(This,lFlags,pstrName,pVal,plFlavor);
}
static inline HRESULT IWbemQualifierSet_EndEnumeration(IWbemQualifierSet* This) {
    return This->lpVtbl->EndEnumeration(This);
}
#endif
#endif

#endif


#endif  /* __IWbemQualifierSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemLocator interface
 */
#ifndef __IWbemLocator_INTERFACE_DEFINED__
#define __IWbemLocator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemLocator, 0xdc12a687, 0x737f, 0x11cf, 0x88,0x4d, 0x00,0xaa,0x00,0x4b,0x2e,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dc12a687-737f-11cf-884d-00aa004b2e24")
IWbemLocator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ConnectServer(
        const BSTR strNetworkResource,
        const BSTR strUser,
        const BSTR strPassword,
        const BSTR strLocale,
        LONG lSecurityFlags,
        const BSTR strAuthority,
        IWbemContext *pCtx,
        IWbemServices **ppNamespace) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemLocator, 0xdc12a687, 0x737f, 0x11cf, 0x88,0x4d, 0x00,0xaa,0x00,0x4b,0x2e,0x24)
#endif
#else
typedef struct IWbemLocatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemLocator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemLocator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemLocator *This);

    /*** IWbemLocator methods ***/
    HRESULT (STDMETHODCALLTYPE *ConnectServer)(
        IWbemLocator *This,
        const BSTR strNetworkResource,
        const BSTR strUser,
        const BSTR strPassword,
        const BSTR strLocale,
        LONG lSecurityFlags,
        const BSTR strAuthority,
        IWbemContext *pCtx,
        IWbemServices **ppNamespace);

    END_INTERFACE
} IWbemLocatorVtbl;

interface IWbemLocator {
    CONST_VTBL IWbemLocatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemLocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemLocator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemLocator_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemLocator methods ***/
#define IWbemLocator_ConnectServer(This,strNetworkResource,strUser,strPassword,strLocale,lSecurityFlags,strAuthority,pCtx,ppNamespace) (This)->lpVtbl->ConnectServer(This,strNetworkResource,strUser,strPassword,strLocale,lSecurityFlags,strAuthority,pCtx,ppNamespace)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemLocator_QueryInterface(IWbemLocator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemLocator_AddRef(IWbemLocator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemLocator_Release(IWbemLocator* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemLocator methods ***/
static inline HRESULT IWbemLocator_ConnectServer(IWbemLocator* This,const BSTR strNetworkResource,const BSTR strUser,const BSTR strPassword,const BSTR strLocale,LONG lSecurityFlags,const BSTR strAuthority,IWbemContext *pCtx,IWbemServices **ppNamespace) {
    return This->lpVtbl->ConnectServer(This,strNetworkResource,strUser,strPassword,strLocale,lSecurityFlags,strAuthority,pCtx,ppNamespace);
}
#endif
#endif

#endif


#endif  /* __IWbemLocator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemObjectSink interface
 */
#ifndef __IWbemObjectSink_INTERFACE_DEFINED__
#define __IWbemObjectSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemObjectSink, 0x7c857801, 0x7381, 0x11cf, 0x88,0x4d, 0x00,0xaa,0x00,0x4b,0x2e,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7c857801-7381-11cf-884d-00aa004b2e24")
IWbemObjectSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Indicate(
        LONG lObjectCount,
        IWbemClassObject **apObjArray) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStatus(
        LONG lFlags,
        HRESULT hResult,
        BSTR strParam,
        IWbemClassObject *pObjParam) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemObjectSink, 0x7c857801, 0x7381, 0x11cf, 0x88,0x4d, 0x00,0xaa,0x00,0x4b,0x2e,0x24)
#endif
#else
typedef struct IWbemObjectSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemObjectSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemObjectSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemObjectSink *This);

    /*** IWbemObjectSink methods ***/
    HRESULT (STDMETHODCALLTYPE *Indicate)(
        IWbemObjectSink *This,
        LONG lObjectCount,
        IWbemClassObject **apObjArray);

    HRESULT (STDMETHODCALLTYPE *SetStatus)(
        IWbemObjectSink *This,
        LONG lFlags,
        HRESULT hResult,
        BSTR strParam,
        IWbemClassObject *pObjParam);

    END_INTERFACE
} IWbemObjectSinkVtbl;

interface IWbemObjectSink {
    CONST_VTBL IWbemObjectSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemObjectSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemObjectSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemObjectSink_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemObjectSink methods ***/
#define IWbemObjectSink_Indicate(This,lObjectCount,apObjArray) (This)->lpVtbl->Indicate(This,lObjectCount,apObjArray)
#define IWbemObjectSink_SetStatus(This,lFlags,hResult,strParam,pObjParam) (This)->lpVtbl->SetStatus(This,lFlags,hResult,strParam,pObjParam)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemObjectSink_QueryInterface(IWbemObjectSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemObjectSink_AddRef(IWbemObjectSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemObjectSink_Release(IWbemObjectSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemObjectSink methods ***/
static inline HRESULT IWbemObjectSink_Indicate(IWbemObjectSink* This,LONG lObjectCount,IWbemClassObject **apObjArray) {
    return This->lpVtbl->Indicate(This,lObjectCount,apObjArray);
}
static inline HRESULT IWbemObjectSink_SetStatus(IWbemObjectSink* This,LONG lFlags,HRESULT hResult,BSTR strParam,IWbemClassObject *pObjParam) {
    return This->lpVtbl->SetStatus(This,lFlags,hResult,strParam,pObjParam);
}
#endif
#endif

#endif


#endif  /* __IWbemObjectSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemObjectSinkEx interface
 */
#ifndef __IWbemObjectSinkEx_INTERFACE_DEFINED__
#define __IWbemObjectSinkEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemObjectSinkEx, 0xe7d35cfa, 0x348b, 0x485e, 0xb5,0x24, 0x25,0x27,0x25,0xd6,0x97,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e7d35cfa-348b-485e-b524-252725d697ca")
IWbemObjectSinkEx : public IWbemObjectSink
{
    virtual HRESULT STDMETHODCALLTYPE WriteMessage(
        ULONG uChannel,
        const BSTR strMessage) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteError(
        IWbemClassObject *pObjError,
        unsigned char *puReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE PromptUser(
        const BSTR strMessage,
        unsigned char uPromptType,
        unsigned char *puReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteProgress(
        const BSTR strActivity,
        const BSTR strCurrentOperation,
        const BSTR strStatusDescription,
        ULONG uPercentComplete,
        ULONG uSecondsRemaining) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteStreamParameter(
        const BSTR strName,
        VARIANT *vtValue,
        ULONG ulType,
        ULONG ulFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemObjectSinkEx, 0xe7d35cfa, 0x348b, 0x485e, 0xb5,0x24, 0x25,0x27,0x25,0xd6,0x97,0xca)
#endif
#else
typedef struct IWbemObjectSinkExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemObjectSinkEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemObjectSinkEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemObjectSinkEx *This);

    /*** IWbemObjectSink methods ***/
    HRESULT (STDMETHODCALLTYPE *Indicate)(
        IWbemObjectSinkEx *This,
        LONG lObjectCount,
        IWbemClassObject **apObjArray);

    HRESULT (STDMETHODCALLTYPE *SetStatus)(
        IWbemObjectSinkEx *This,
        LONG lFlags,
        HRESULT hResult,
        BSTR strParam,
        IWbemClassObject *pObjParam);

    /*** IWbemObjectSinkEx methods ***/
    HRESULT (STDMETHODCALLTYPE *WriteMessage)(
        IWbemObjectSinkEx *This,
        ULONG uChannel,
        const BSTR strMessage);

    HRESULT (STDMETHODCALLTYPE *WriteError)(
        IWbemObjectSinkEx *This,
        IWbemClassObject *pObjError,
        unsigned char *puReturned);

    HRESULT (STDMETHODCALLTYPE *PromptUser)(
        IWbemObjectSinkEx *This,
        const BSTR strMessage,
        unsigned char uPromptType,
        unsigned char *puReturned);

    HRESULT (STDMETHODCALLTYPE *WriteProgress)(
        IWbemObjectSinkEx *This,
        const BSTR strActivity,
        const BSTR strCurrentOperation,
        const BSTR strStatusDescription,
        ULONG uPercentComplete,
        ULONG uSecondsRemaining);

    HRESULT (STDMETHODCALLTYPE *WriteStreamParameter)(
        IWbemObjectSinkEx *This,
        const BSTR strName,
        VARIANT *vtValue,
        ULONG ulType,
        ULONG ulFlags);

    END_INTERFACE
} IWbemObjectSinkExVtbl;

interface IWbemObjectSinkEx {
    CONST_VTBL IWbemObjectSinkExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemObjectSinkEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemObjectSinkEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemObjectSinkEx_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemObjectSink methods ***/
#define IWbemObjectSinkEx_Indicate(This,lObjectCount,apObjArray) (This)->lpVtbl->Indicate(This,lObjectCount,apObjArray)
#define IWbemObjectSinkEx_SetStatus(This,lFlags,hResult,strParam,pObjParam) (This)->lpVtbl->SetStatus(This,lFlags,hResult,strParam,pObjParam)
/*** IWbemObjectSinkEx methods ***/
#define IWbemObjectSinkEx_WriteMessage(This,uChannel,strMessage) (This)->lpVtbl->WriteMessage(This,uChannel,strMessage)
#define IWbemObjectSinkEx_WriteError(This,pObjError,puReturned) (This)->lpVtbl->WriteError(This,pObjError,puReturned)
#define IWbemObjectSinkEx_PromptUser(This,strMessage,uPromptType,puReturned) (This)->lpVtbl->PromptUser(This,strMessage,uPromptType,puReturned)
#define IWbemObjectSinkEx_WriteProgress(This,strActivity,strCurrentOperation,strStatusDescription,uPercentComplete,uSecondsRemaining) (This)->lpVtbl->WriteProgress(This,strActivity,strCurrentOperation,strStatusDescription,uPercentComplete,uSecondsRemaining)
#define IWbemObjectSinkEx_WriteStreamParameter(This,strName,vtValue,ulType,ulFlags) (This)->lpVtbl->WriteStreamParameter(This,strName,vtValue,ulType,ulFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemObjectSinkEx_QueryInterface(IWbemObjectSinkEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemObjectSinkEx_AddRef(IWbemObjectSinkEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemObjectSinkEx_Release(IWbemObjectSinkEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemObjectSink methods ***/
static inline HRESULT IWbemObjectSinkEx_Indicate(IWbemObjectSinkEx* This,LONG lObjectCount,IWbemClassObject **apObjArray) {
    return This->lpVtbl->Indicate(This,lObjectCount,apObjArray);
}
static inline HRESULT IWbemObjectSinkEx_SetStatus(IWbemObjectSinkEx* This,LONG lFlags,HRESULT hResult,BSTR strParam,IWbemClassObject *pObjParam) {
    return This->lpVtbl->SetStatus(This,lFlags,hResult,strParam,pObjParam);
}
/*** IWbemObjectSinkEx methods ***/
static inline HRESULT IWbemObjectSinkEx_WriteMessage(IWbemObjectSinkEx* This,ULONG uChannel,const BSTR strMessage) {
    return This->lpVtbl->WriteMessage(This,uChannel,strMessage);
}
static inline HRESULT IWbemObjectSinkEx_WriteError(IWbemObjectSinkEx* This,IWbemClassObject *pObjError,unsigned char *puReturned) {
    return This->lpVtbl->WriteError(This,pObjError,puReturned);
}
static inline HRESULT IWbemObjectSinkEx_PromptUser(IWbemObjectSinkEx* This,const BSTR strMessage,unsigned char uPromptType,unsigned char *puReturned) {
    return This->lpVtbl->PromptUser(This,strMessage,uPromptType,puReturned);
}
static inline HRESULT IWbemObjectSinkEx_WriteProgress(IWbemObjectSinkEx* This,const BSTR strActivity,const BSTR strCurrentOperation,const BSTR strStatusDescription,ULONG uPercentComplete,ULONG uSecondsRemaining) {
    return This->lpVtbl->WriteProgress(This,strActivity,strCurrentOperation,strStatusDescription,uPercentComplete,uSecondsRemaining);
}
static inline HRESULT IWbemObjectSinkEx_WriteStreamParameter(IWbemObjectSinkEx* This,const BSTR strName,VARIANT *vtValue,ULONG ulType,ULONG ulFlags) {
    return This->lpVtbl->WriteStreamParameter(This,strName,vtValue,ulType,ulFlags);
}
#endif
#endif

#endif


#endif  /* __IWbemObjectSinkEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumWbemClassObject interface
 */
#ifndef __IEnumWbemClassObject_INTERFACE_DEFINED__
#define __IEnumWbemClassObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumWbemClassObject, 0x027947e1, 0xd731, 0x11ce, 0xa3,0x57, 0x00,0x00,0x00,0x00,0x00,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("027947e1-d731-11ce-a357-000000000001")
IEnumWbemClassObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        LONG lTimeout,
        ULONG uCount,
        IWbemClassObject **apObjects,
        ULONG *puReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE NextAsync(
        ULONG uCount,
        IWbemObjectSink *pSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumWbemClassObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        LONG lTimeout,
        ULONG nCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumWbemClassObject, 0x027947e1, 0xd731, 0x11ce, 0xa3,0x57, 0x00,0x00,0x00,0x00,0x00,0x01)
#endif
#else
typedef struct IEnumWbemClassObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumWbemClassObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumWbemClassObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumWbemClassObject *This);

    /*** IEnumWbemClassObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumWbemClassObject *This);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumWbemClassObject *This,
        LONG lTimeout,
        ULONG uCount,
        IWbemClassObject **apObjects,
        ULONG *puReturned);

    HRESULT (STDMETHODCALLTYPE *NextAsync)(
        IEnumWbemClassObject *This,
        ULONG uCount,
        IWbemObjectSink *pSink);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumWbemClassObject *This,
        IEnumWbemClassObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumWbemClassObject *This,
        LONG lTimeout,
        ULONG nCount);

    END_INTERFACE
} IEnumWbemClassObjectVtbl;

interface IEnumWbemClassObject {
    CONST_VTBL IEnumWbemClassObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumWbemClassObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumWbemClassObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumWbemClassObject_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumWbemClassObject methods ***/
#define IEnumWbemClassObject_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumWbemClassObject_Next(This,lTimeout,uCount,apObjects,puReturned) (This)->lpVtbl->Next(This,lTimeout,uCount,apObjects,puReturned)
#define IEnumWbemClassObject_NextAsync(This,uCount,pSink) (This)->lpVtbl->NextAsync(This,uCount,pSink)
#define IEnumWbemClassObject_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#define IEnumWbemClassObject_Skip(This,lTimeout,nCount) (This)->lpVtbl->Skip(This,lTimeout,nCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumWbemClassObject_QueryInterface(IEnumWbemClassObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumWbemClassObject_AddRef(IEnumWbemClassObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumWbemClassObject_Release(IEnumWbemClassObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumWbemClassObject methods ***/
static inline HRESULT IEnumWbemClassObject_Reset(IEnumWbemClassObject* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumWbemClassObject_Next(IEnumWbemClassObject* This,LONG lTimeout,ULONG uCount,IWbemClassObject **apObjects,ULONG *puReturned) {
    return This->lpVtbl->Next(This,lTimeout,uCount,apObjects,puReturned);
}
static inline HRESULT IEnumWbemClassObject_NextAsync(IEnumWbemClassObject* This,ULONG uCount,IWbemObjectSink *pSink) {
    return This->lpVtbl->NextAsync(This,uCount,pSink);
}
static inline HRESULT IEnumWbemClassObject_Clone(IEnumWbemClassObject* This,IEnumWbemClassObject **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
static inline HRESULT IEnumWbemClassObject_Skip(IEnumWbemClassObject* This,LONG lTimeout,ULONG nCount) {
    return This->lpVtbl->Skip(This,lTimeout,nCount);
}
#endif
#endif

#endif


#endif  /* __IEnumWbemClassObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemContext interface
 */
#ifndef __IWbemContext_INTERFACE_DEFINED__
#define __IWbemContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemContext, 0x44aca674, 0xe8fc, 0x11d0, 0xa0,0x7c, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("44aca674-e8fc-11d0-a07c-00c04fb68820")
IWbemContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Clone(
        IWbemContext **ppNewCopy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNames(
        LONG lFlags,
        SAFEARRAY **pNames) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginEnumeration(
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Next(
        LONG lFlags,
        BSTR *pstrName,
        VARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndEnumeration(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValue(
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteValue(
        LPCWSTR wszName,
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteAll(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemContext, 0x44aca674, 0xe8fc, 0x11d0, 0xa0,0x7c, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemContext *This);

    /*** IWbemContext methods ***/
    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWbemContext *This,
        IWbemContext **ppNewCopy);

    HRESULT (STDMETHODCALLTYPE *GetNames)(
        IWbemContext *This,
        LONG lFlags,
        SAFEARRAY **pNames);

    HRESULT (STDMETHODCALLTYPE *BeginEnumeration)(
        IWbemContext *This,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IWbemContext *This,
        LONG lFlags,
        BSTR *pstrName,
        VARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *EndEnumeration)(
        IWbemContext *This);

    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IWbemContext *This,
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IWbemContext *This,
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pValue);

    HRESULT (STDMETHODCALLTYPE *DeleteValue)(
        IWbemContext *This,
        LPCWSTR wszName,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *DeleteAll)(
        IWbemContext *This);

    END_INTERFACE
} IWbemContextVtbl;

interface IWbemContext {
    CONST_VTBL IWbemContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemContext_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemContext methods ***/
#define IWbemContext_Clone(This,ppNewCopy) (This)->lpVtbl->Clone(This,ppNewCopy)
#define IWbemContext_GetNames(This,lFlags,pNames) (This)->lpVtbl->GetNames(This,lFlags,pNames)
#define IWbemContext_BeginEnumeration(This,lFlags) (This)->lpVtbl->BeginEnumeration(This,lFlags)
#define IWbemContext_Next(This,lFlags,pstrName,pValue) (This)->lpVtbl->Next(This,lFlags,pstrName,pValue)
#define IWbemContext_EndEnumeration(This) (This)->lpVtbl->EndEnumeration(This)
#define IWbemContext_SetValue(This,wszName,lFlags,pValue) (This)->lpVtbl->SetValue(This,wszName,lFlags,pValue)
#define IWbemContext_GetValue(This,wszName,lFlags,pValue) (This)->lpVtbl->GetValue(This,wszName,lFlags,pValue)
#define IWbemContext_DeleteValue(This,wszName,lFlags) (This)->lpVtbl->DeleteValue(This,wszName,lFlags)
#define IWbemContext_DeleteAll(This) (This)->lpVtbl->DeleteAll(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemContext_QueryInterface(IWbemContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemContext_AddRef(IWbemContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemContext_Release(IWbemContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemContext methods ***/
static inline HRESULT IWbemContext_Clone(IWbemContext* This,IWbemContext **ppNewCopy) {
    return This->lpVtbl->Clone(This,ppNewCopy);
}
static inline HRESULT IWbemContext_GetNames(IWbemContext* This,LONG lFlags,SAFEARRAY **pNames) {
    return This->lpVtbl->GetNames(This,lFlags,pNames);
}
static inline HRESULT IWbemContext_BeginEnumeration(IWbemContext* This,LONG lFlags) {
    return This->lpVtbl->BeginEnumeration(This,lFlags);
}
static inline HRESULT IWbemContext_Next(IWbemContext* This,LONG lFlags,BSTR *pstrName,VARIANT *pValue) {
    return This->lpVtbl->Next(This,lFlags,pstrName,pValue);
}
static inline HRESULT IWbemContext_EndEnumeration(IWbemContext* This) {
    return This->lpVtbl->EndEnumeration(This);
}
static inline HRESULT IWbemContext_SetValue(IWbemContext* This,LPCWSTR wszName,LONG lFlags,VARIANT *pValue) {
    return This->lpVtbl->SetValue(This,wszName,lFlags,pValue);
}
static inline HRESULT IWbemContext_GetValue(IWbemContext* This,LPCWSTR wszName,LONG lFlags,VARIANT *pValue) {
    return This->lpVtbl->GetValue(This,wszName,lFlags,pValue);
}
static inline HRESULT IWbemContext_DeleteValue(IWbemContext* This,LPCWSTR wszName,LONG lFlags) {
    return This->lpVtbl->DeleteValue(This,wszName,lFlags);
}
static inline HRESULT IWbemContext_DeleteAll(IWbemContext* This) {
    return This->lpVtbl->DeleteAll(This);
}
#endif
#endif

#endif


#endif  /* __IWbemContext_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemCallResult interface
 */
#ifndef __IWbemCallResult_INTERFACE_DEFINED__
#define __IWbemCallResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemCallResult, 0x44aca675, 0xe8fc, 0x11d0, 0xa0,0x7c, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("44aca675-e8fc-11d0-a07c-00c04fb68820")
IWbemCallResult : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetResultObject(
        LONG lTimeout,
        IWbemClassObject **ppResultObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResultString(
        LONG lTimeout,
        BSTR *pstrResultString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResultServices(
        LONG lTimeout,
        IWbemServices **ppServices) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCallStatus(
        LONG lTimeout,
        LONG *plStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemCallResult, 0x44aca675, 0xe8fc, 0x11d0, 0xa0,0x7c, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemCallResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemCallResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemCallResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemCallResult *This);

    /*** IWbemCallResult methods ***/
    HRESULT (STDMETHODCALLTYPE *GetResultObject)(
        IWbemCallResult *This,
        LONG lTimeout,
        IWbemClassObject **ppResultObject);

    HRESULT (STDMETHODCALLTYPE *GetResultString)(
        IWbemCallResult *This,
        LONG lTimeout,
        BSTR *pstrResultString);

    HRESULT (STDMETHODCALLTYPE *GetResultServices)(
        IWbemCallResult *This,
        LONG lTimeout,
        IWbemServices **ppServices);

    HRESULT (STDMETHODCALLTYPE *GetCallStatus)(
        IWbemCallResult *This,
        LONG lTimeout,
        LONG *plStatus);

    END_INTERFACE
} IWbemCallResultVtbl;

interface IWbemCallResult {
    CONST_VTBL IWbemCallResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemCallResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemCallResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemCallResult_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemCallResult methods ***/
#define IWbemCallResult_GetResultObject(This,lTimeout,ppResultObject) (This)->lpVtbl->GetResultObject(This,lTimeout,ppResultObject)
#define IWbemCallResult_GetResultString(This,lTimeout,pstrResultString) (This)->lpVtbl->GetResultString(This,lTimeout,pstrResultString)
#define IWbemCallResult_GetResultServices(This,lTimeout,ppServices) (This)->lpVtbl->GetResultServices(This,lTimeout,ppServices)
#define IWbemCallResult_GetCallStatus(This,lTimeout,plStatus) (This)->lpVtbl->GetCallStatus(This,lTimeout,plStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemCallResult_QueryInterface(IWbemCallResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemCallResult_AddRef(IWbemCallResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemCallResult_Release(IWbemCallResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemCallResult methods ***/
static inline HRESULT IWbemCallResult_GetResultObject(IWbemCallResult* This,LONG lTimeout,IWbemClassObject **ppResultObject) {
    return This->lpVtbl->GetResultObject(This,lTimeout,ppResultObject);
}
static inline HRESULT IWbemCallResult_GetResultString(IWbemCallResult* This,LONG lTimeout,BSTR *pstrResultString) {
    return This->lpVtbl->GetResultString(This,lTimeout,pstrResultString);
}
static inline HRESULT IWbemCallResult_GetResultServices(IWbemCallResult* This,LONG lTimeout,IWbemServices **ppServices) {
    return This->lpVtbl->GetResultServices(This,lTimeout,ppServices);
}
static inline HRESULT IWbemCallResult_GetCallStatus(IWbemCallResult* This,LONG lTimeout,LONG *plStatus) {
    return This->lpVtbl->GetCallStatus(This,lTimeout,plStatus);
}
#endif
#endif

#endif


#endif  /* __IWbemCallResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemServices interface
 */
#ifndef __IWbemServices_INTERFACE_DEFINED__
#define __IWbemServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemServices, 0x9556dc99, 0x828c, 0x11cf, 0xa3,0x7e, 0x00,0xaa,0x00,0x32,0x40,0xc7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9556dc99-828c-11cf-a37e-00aa003240c7")
IWbemServices : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OpenNamespace(
        const BSTR strNamespace,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemServices **ppWorkingNamespace,
        IWbemCallResult **ppResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelAsyncCall(
        IWbemObjectSink *pSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryObjectSink(
        LONG lFlags,
        IWbemObjectSink **ppResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObject(
        const BSTR strObjectPath,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemClassObject **ppObject,
        IWbemCallResult **ppCallResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectAsync(
        const BSTR strObjectPath,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutClass(
        IWbemClassObject *pObject,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemCallResult **ppCallResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutClassAsync(
        IWbemClassObject *pObject,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteClass(
        const BSTR strClass,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemCallResult **ppCallResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteClassAsync(
        const BSTR strClass,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateClassEnum(
        const BSTR strSuperclass,
        LONG lFlags,
        IWbemContext *pCtx,
        IEnumWbemClassObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateClassEnumAsync(
        const BSTR strSuperclass,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutInstance(
        IWbemClassObject *pInst,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemCallResult **ppCallResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutInstanceAsync(
        IWbemClassObject *pInst,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteInstance(
        const BSTR strObjectPath,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemCallResult **ppCallResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteInstanceAsync(
        const BSTR strObjectPath,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstanceEnum(
        const BSTR strFilter,
        LONG lFlags,
        IWbemContext *pCtx,
        IEnumWbemClassObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstanceEnumAsync(
        const BSTR strFilter,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecQuery(
        const BSTR strQueryLanguage,
        const BSTR strQuery,
        LONG lFlags,
        IWbemContext *pCtx,
        IEnumWbemClassObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecQueryAsync(
        const BSTR strQueryLanguage,
        const BSTR strQuery,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecNotificationQuery(
        const BSTR strQueryLanguage,
        const BSTR strQuery,
        LONG lFlags,
        IWbemContext *pCtx,
        IEnumWbemClassObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecNotificationQueryAsync(
        const BSTR strQueryLanguage,
        const BSTR strQuery,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecMethod(
        const BSTR strObjectPath,
        const BSTR strMethodName,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemClassObject *pInParams,
        IWbemClassObject **ppOutParams,
        IWbemCallResult **ppCallResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecMethodAsync(
        const BSTR strObjectPath,
        const BSTR strMethodName,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemClassObject *pInParams,
        IWbemObjectSink *pResponseHandler) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemServices, 0x9556dc99, 0x828c, 0x11cf, 0xa3,0x7e, 0x00,0xaa,0x00,0x32,0x40,0xc7)
#endif
#else
typedef struct IWbemServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemServices *This);

    /*** IWbemServices methods ***/
    HRESULT (STDMETHODCALLTYPE *OpenNamespace)(
        IWbemServices *This,
        const BSTR strNamespace,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemServices **ppWorkingNamespace,
        IWbemCallResult **ppResult);

    HRESULT (STDMETHODCALLTYPE *CancelAsyncCall)(
        IWbemServices *This,
        IWbemObjectSink *pSink);

    HRESULT (STDMETHODCALLTYPE *QueryObjectSink)(
        IWbemServices *This,
        LONG lFlags,
        IWbemObjectSink **ppResponseHandler);

    HRESULT (STDMETHODCALLTYPE *GetObject)(
        IWbemServices *This,
        const BSTR strObjectPath,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemClassObject **ppObject,
        IWbemCallResult **ppCallResult);

    HRESULT (STDMETHODCALLTYPE *GetObjectAsync)(
        IWbemServices *This,
        const BSTR strObjectPath,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *PutClass)(
        IWbemServices *This,
        IWbemClassObject *pObject,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemCallResult **ppCallResult);

    HRESULT (STDMETHODCALLTYPE *PutClassAsync)(
        IWbemServices *This,
        IWbemClassObject *pObject,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *DeleteClass)(
        IWbemServices *This,
        const BSTR strClass,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemCallResult **ppCallResult);

    HRESULT (STDMETHODCALLTYPE *DeleteClassAsync)(
        IWbemServices *This,
        const BSTR strClass,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *CreateClassEnum)(
        IWbemServices *This,
        const BSTR strSuperclass,
        LONG lFlags,
        IWbemContext *pCtx,
        IEnumWbemClassObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *CreateClassEnumAsync)(
        IWbemServices *This,
        const BSTR strSuperclass,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *PutInstance)(
        IWbemServices *This,
        IWbemClassObject *pInst,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemCallResult **ppCallResult);

    HRESULT (STDMETHODCALLTYPE *PutInstanceAsync)(
        IWbemServices *This,
        IWbemClassObject *pInst,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *DeleteInstance)(
        IWbemServices *This,
        const BSTR strObjectPath,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemCallResult **ppCallResult);

    HRESULT (STDMETHODCALLTYPE *DeleteInstanceAsync)(
        IWbemServices *This,
        const BSTR strObjectPath,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceEnum)(
        IWbemServices *This,
        const BSTR strFilter,
        LONG lFlags,
        IWbemContext *pCtx,
        IEnumWbemClassObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceEnumAsync)(
        IWbemServices *This,
        const BSTR strFilter,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *ExecQuery)(
        IWbemServices *This,
        const BSTR strQueryLanguage,
        const BSTR strQuery,
        LONG lFlags,
        IWbemContext *pCtx,
        IEnumWbemClassObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *ExecQueryAsync)(
        IWbemServices *This,
        const BSTR strQueryLanguage,
        const BSTR strQuery,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *ExecNotificationQuery)(
        IWbemServices *This,
        const BSTR strQueryLanguage,
        const BSTR strQuery,
        LONG lFlags,
        IWbemContext *pCtx,
        IEnumWbemClassObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *ExecNotificationQueryAsync)(
        IWbemServices *This,
        const BSTR strQueryLanguage,
        const BSTR strQuery,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pResponseHandler);

    HRESULT (STDMETHODCALLTYPE *ExecMethod)(
        IWbemServices *This,
        const BSTR strObjectPath,
        const BSTR strMethodName,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemClassObject *pInParams,
        IWbemClassObject **ppOutParams,
        IWbemCallResult **ppCallResult);

    HRESULT (STDMETHODCALLTYPE *ExecMethodAsync)(
        IWbemServices *This,
        const BSTR strObjectPath,
        const BSTR strMethodName,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemClassObject *pInParams,
        IWbemObjectSink *pResponseHandler);

    END_INTERFACE
} IWbemServicesVtbl;

interface IWbemServices {
    CONST_VTBL IWbemServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemServices_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemServices methods ***/
#define IWbemServices_OpenNamespace(This,strNamespace,lFlags,pCtx,ppWorkingNamespace,ppResult) (This)->lpVtbl->OpenNamespace(This,strNamespace,lFlags,pCtx,ppWorkingNamespace,ppResult)
#define IWbemServices_CancelAsyncCall(This,pSink) (This)->lpVtbl->CancelAsyncCall(This,pSink)
#define IWbemServices_QueryObjectSink(This,lFlags,ppResponseHandler) (This)->lpVtbl->QueryObjectSink(This,lFlags,ppResponseHandler)
#define IWbemServices_GetObject(This,strObjectPath,lFlags,pCtx,ppObject,ppCallResult) (This)->lpVtbl->GetObject(This,strObjectPath,lFlags,pCtx,ppObject,ppCallResult)
#define IWbemServices_GetObjectAsync(This,strObjectPath,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->GetObjectAsync(This,strObjectPath,lFlags,pCtx,pResponseHandler)
#define IWbemServices_PutClass(This,pObject,lFlags,pCtx,ppCallResult) (This)->lpVtbl->PutClass(This,pObject,lFlags,pCtx,ppCallResult)
#define IWbemServices_PutClassAsync(This,pObject,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->PutClassAsync(This,pObject,lFlags,pCtx,pResponseHandler)
#define IWbemServices_DeleteClass(This,strClass,lFlags,pCtx,ppCallResult) (This)->lpVtbl->DeleteClass(This,strClass,lFlags,pCtx,ppCallResult)
#define IWbemServices_DeleteClassAsync(This,strClass,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->DeleteClassAsync(This,strClass,lFlags,pCtx,pResponseHandler)
#define IWbemServices_CreateClassEnum(This,strSuperclass,lFlags,pCtx,ppEnum) (This)->lpVtbl->CreateClassEnum(This,strSuperclass,lFlags,pCtx,ppEnum)
#define IWbemServices_CreateClassEnumAsync(This,strSuperclass,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->CreateClassEnumAsync(This,strSuperclass,lFlags,pCtx,pResponseHandler)
#define IWbemServices_PutInstance(This,pInst,lFlags,pCtx,ppCallResult) (This)->lpVtbl->PutInstance(This,pInst,lFlags,pCtx,ppCallResult)
#define IWbemServices_PutInstanceAsync(This,pInst,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->PutInstanceAsync(This,pInst,lFlags,pCtx,pResponseHandler)
#define IWbemServices_DeleteInstance(This,strObjectPath,lFlags,pCtx,ppCallResult) (This)->lpVtbl->DeleteInstance(This,strObjectPath,lFlags,pCtx,ppCallResult)
#define IWbemServices_DeleteInstanceAsync(This,strObjectPath,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->DeleteInstanceAsync(This,strObjectPath,lFlags,pCtx,pResponseHandler)
#define IWbemServices_CreateInstanceEnum(This,strFilter,lFlags,pCtx,ppEnum) (This)->lpVtbl->CreateInstanceEnum(This,strFilter,lFlags,pCtx,ppEnum)
#define IWbemServices_CreateInstanceEnumAsync(This,strFilter,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->CreateInstanceEnumAsync(This,strFilter,lFlags,pCtx,pResponseHandler)
#define IWbemServices_ExecQuery(This,strQueryLanguage,strQuery,lFlags,pCtx,ppEnum) (This)->lpVtbl->ExecQuery(This,strQueryLanguage,strQuery,lFlags,pCtx,ppEnum)
#define IWbemServices_ExecQueryAsync(This,strQueryLanguage,strQuery,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->ExecQueryAsync(This,strQueryLanguage,strQuery,lFlags,pCtx,pResponseHandler)
#define IWbemServices_ExecNotificationQuery(This,strQueryLanguage,strQuery,lFlags,pCtx,ppEnum) (This)->lpVtbl->ExecNotificationQuery(This,strQueryLanguage,strQuery,lFlags,pCtx,ppEnum)
#define IWbemServices_ExecNotificationQueryAsync(This,strQueryLanguage,strQuery,lFlags,pCtx,pResponseHandler) (This)->lpVtbl->ExecNotificationQueryAsync(This,strQueryLanguage,strQuery,lFlags,pCtx,pResponseHandler)
#define IWbemServices_ExecMethod(This,strObjectPath,strMethodName,lFlags,pCtx,pInParams,ppOutParams,ppCallResult) (This)->lpVtbl->ExecMethod(This,strObjectPath,strMethodName,lFlags,pCtx,pInParams,ppOutParams,ppCallResult)
#define IWbemServices_ExecMethodAsync(This,strObjectPath,strMethodName,lFlags,pCtx,pInParams,pResponseHandler) (This)->lpVtbl->ExecMethodAsync(This,strObjectPath,strMethodName,lFlags,pCtx,pInParams,pResponseHandler)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemServices_QueryInterface(IWbemServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemServices_AddRef(IWbemServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemServices_Release(IWbemServices* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemServices methods ***/
static inline HRESULT IWbemServices_OpenNamespace(IWbemServices* This,const BSTR strNamespace,LONG lFlags,IWbemContext *pCtx,IWbemServices **ppWorkingNamespace,IWbemCallResult **ppResult) {
    return This->lpVtbl->OpenNamespace(This,strNamespace,lFlags,pCtx,ppWorkingNamespace,ppResult);
}
static inline HRESULT IWbemServices_CancelAsyncCall(IWbemServices* This,IWbemObjectSink *pSink) {
    return This->lpVtbl->CancelAsyncCall(This,pSink);
}
static inline HRESULT IWbemServices_QueryObjectSink(IWbemServices* This,LONG lFlags,IWbemObjectSink **ppResponseHandler) {
    return This->lpVtbl->QueryObjectSink(This,lFlags,ppResponseHandler);
}
static inline HRESULT IWbemServices_GetObject(IWbemServices* This,const BSTR strObjectPath,LONG lFlags,IWbemContext *pCtx,IWbemClassObject **ppObject,IWbemCallResult **ppCallResult) {
    return This->lpVtbl->GetObject(This,strObjectPath,lFlags,pCtx,ppObject,ppCallResult);
}
static inline HRESULT IWbemServices_GetObjectAsync(IWbemServices* This,const BSTR strObjectPath,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->GetObjectAsync(This,strObjectPath,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_PutClass(IWbemServices* This,IWbemClassObject *pObject,LONG lFlags,IWbemContext *pCtx,IWbemCallResult **ppCallResult) {
    return This->lpVtbl->PutClass(This,pObject,lFlags,pCtx,ppCallResult);
}
static inline HRESULT IWbemServices_PutClassAsync(IWbemServices* This,IWbemClassObject *pObject,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->PutClassAsync(This,pObject,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_DeleteClass(IWbemServices* This,const BSTR strClass,LONG lFlags,IWbemContext *pCtx,IWbemCallResult **ppCallResult) {
    return This->lpVtbl->DeleteClass(This,strClass,lFlags,pCtx,ppCallResult);
}
static inline HRESULT IWbemServices_DeleteClassAsync(IWbemServices* This,const BSTR strClass,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->DeleteClassAsync(This,strClass,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_CreateClassEnum(IWbemServices* This,const BSTR strSuperclass,LONG lFlags,IWbemContext *pCtx,IEnumWbemClassObject **ppEnum) {
    return This->lpVtbl->CreateClassEnum(This,strSuperclass,lFlags,pCtx,ppEnum);
}
static inline HRESULT IWbemServices_CreateClassEnumAsync(IWbemServices* This,const BSTR strSuperclass,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->CreateClassEnumAsync(This,strSuperclass,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_PutInstance(IWbemServices* This,IWbemClassObject *pInst,LONG lFlags,IWbemContext *pCtx,IWbemCallResult **ppCallResult) {
    return This->lpVtbl->PutInstance(This,pInst,lFlags,pCtx,ppCallResult);
}
static inline HRESULT IWbemServices_PutInstanceAsync(IWbemServices* This,IWbemClassObject *pInst,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->PutInstanceAsync(This,pInst,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_DeleteInstance(IWbemServices* This,const BSTR strObjectPath,LONG lFlags,IWbemContext *pCtx,IWbemCallResult **ppCallResult) {
    return This->lpVtbl->DeleteInstance(This,strObjectPath,lFlags,pCtx,ppCallResult);
}
static inline HRESULT IWbemServices_DeleteInstanceAsync(IWbemServices* This,const BSTR strObjectPath,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->DeleteInstanceAsync(This,strObjectPath,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_CreateInstanceEnum(IWbemServices* This,const BSTR strFilter,LONG lFlags,IWbemContext *pCtx,IEnumWbemClassObject **ppEnum) {
    return This->lpVtbl->CreateInstanceEnum(This,strFilter,lFlags,pCtx,ppEnum);
}
static inline HRESULT IWbemServices_CreateInstanceEnumAsync(IWbemServices* This,const BSTR strFilter,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->CreateInstanceEnumAsync(This,strFilter,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_ExecQuery(IWbemServices* This,const BSTR strQueryLanguage,const BSTR strQuery,LONG lFlags,IWbemContext *pCtx,IEnumWbemClassObject **ppEnum) {
    return This->lpVtbl->ExecQuery(This,strQueryLanguage,strQuery,lFlags,pCtx,ppEnum);
}
static inline HRESULT IWbemServices_ExecQueryAsync(IWbemServices* This,const BSTR strQueryLanguage,const BSTR strQuery,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->ExecQueryAsync(This,strQueryLanguage,strQuery,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_ExecNotificationQuery(IWbemServices* This,const BSTR strQueryLanguage,const BSTR strQuery,LONG lFlags,IWbemContext *pCtx,IEnumWbemClassObject **ppEnum) {
    return This->lpVtbl->ExecNotificationQuery(This,strQueryLanguage,strQuery,lFlags,pCtx,ppEnum);
}
static inline HRESULT IWbemServices_ExecNotificationQueryAsync(IWbemServices* This,const BSTR strQueryLanguage,const BSTR strQuery,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->ExecNotificationQueryAsync(This,strQueryLanguage,strQuery,lFlags,pCtx,pResponseHandler);
}
static inline HRESULT IWbemServices_ExecMethod(IWbemServices* This,const BSTR strObjectPath,const BSTR strMethodName,LONG lFlags,IWbemContext *pCtx,IWbemClassObject *pInParams,IWbemClassObject **ppOutParams,IWbemCallResult **ppCallResult) {
    return This->lpVtbl->ExecMethod(This,strObjectPath,strMethodName,lFlags,pCtx,pInParams,ppOutParams,ppCallResult);
}
static inline HRESULT IWbemServices_ExecMethodAsync(IWbemServices* This,const BSTR strObjectPath,const BSTR strMethodName,LONG lFlags,IWbemContext *pCtx,IWbemClassObject *pInParams,IWbemObjectSink *pResponseHandler) {
    return This->lpVtbl->ExecMethodAsync(This,strObjectPath,strMethodName,lFlags,pCtx,pInParams,pResponseHandler);
}
#endif
#endif

#endif


#endif  /* __IWbemServices_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemShutdown interface
 */
#ifndef __IWbemShutdown_INTERFACE_DEFINED__
#define __IWbemShutdown_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemShutdown, 0xb7b31df9, 0xd515, 0x11d3, 0xa1,0x1c, 0x00,0x10,0x5a,0x1f,0x51,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b7b31df9-d515-11d3-a11c-00105a1f515a")
IWbemShutdown : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        LONG uReason,
        ULONG uMaxMilliseconds,
        IWbemContext *pCtx) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemShutdown, 0xb7b31df9, 0xd515, 0x11d3, 0xa1,0x1c, 0x00,0x10,0x5a,0x1f,0x51,0x5a)
#endif
#else
typedef struct IWbemShutdownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemShutdown *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemShutdown *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemShutdown *This);

    /*** IWbemShutdown methods ***/
    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IWbemShutdown *This,
        LONG uReason,
        ULONG uMaxMilliseconds,
        IWbemContext *pCtx);

    END_INTERFACE
} IWbemShutdownVtbl;

interface IWbemShutdown {
    CONST_VTBL IWbemShutdownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemShutdown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemShutdown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemShutdown_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemShutdown methods ***/
#define IWbemShutdown_Shutdown(This,uReason,uMaxMilliseconds,pCtx) (This)->lpVtbl->Shutdown(This,uReason,uMaxMilliseconds,pCtx)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemShutdown_QueryInterface(IWbemShutdown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemShutdown_AddRef(IWbemShutdown* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemShutdown_Release(IWbemShutdown* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemShutdown methods ***/
static inline HRESULT IWbemShutdown_Shutdown(IWbemShutdown* This,LONG uReason,ULONG uMaxMilliseconds,IWbemContext *pCtx) {
    return This->lpVtbl->Shutdown(This,uReason,uMaxMilliseconds,pCtx);
}
#endif
#endif

#endif


#endif  /* __IWbemShutdown_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemObjectTextSrc interface
 */
#ifndef __IWbemObjectTextSrc_INTERFACE_DEFINED__
#define __IWbemObjectTextSrc_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemObjectTextSrc, 0xbfbf883a, 0xcad7, 0x11d3, 0xa1,0x1b, 0x00,0x10,0x5a,0x1f,0x51,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bfbf883a-cad7-11d3-a11b-00105a1f515a")
IWbemObjectTextSrc : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetText(
        LONG lFlags,
        IWbemClassObject *pObj,
        ULONG uObjTextFormat,
        IWbemContext *pCtx,
        BSTR *strText) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFromText(
        LONG lFlags,
        BSTR strText,
        ULONG uObjTextFormat,
        IWbemContext *pCtx,
        IWbemClassObject **pNewObj) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemObjectTextSrc, 0xbfbf883a, 0xcad7, 0x11d3, 0xa1,0x1b, 0x00,0x10,0x5a,0x1f,0x51,0x5a)
#endif
#else
typedef struct IWbemObjectTextSrcVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemObjectTextSrc *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemObjectTextSrc *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemObjectTextSrc *This);

    /*** IWbemObjectTextSrc methods ***/
    HRESULT (STDMETHODCALLTYPE *GetText)(
        IWbemObjectTextSrc *This,
        LONG lFlags,
        IWbemClassObject *pObj,
        ULONG uObjTextFormat,
        IWbemContext *pCtx,
        BSTR *strText);

    HRESULT (STDMETHODCALLTYPE *CreateFromText)(
        IWbemObjectTextSrc *This,
        LONG lFlags,
        BSTR strText,
        ULONG uObjTextFormat,
        IWbemContext *pCtx,
        IWbemClassObject **pNewObj);

    END_INTERFACE
} IWbemObjectTextSrcVtbl;

interface IWbemObjectTextSrc {
    CONST_VTBL IWbemObjectTextSrcVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemObjectTextSrc_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemObjectTextSrc_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemObjectTextSrc_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemObjectTextSrc methods ***/
#define IWbemObjectTextSrc_GetText(This,lFlags,pObj,uObjTextFormat,pCtx,strText) (This)->lpVtbl->GetText(This,lFlags,pObj,uObjTextFormat,pCtx,strText)
#define IWbemObjectTextSrc_CreateFromText(This,lFlags,strText,uObjTextFormat,pCtx,pNewObj) (This)->lpVtbl->CreateFromText(This,lFlags,strText,uObjTextFormat,pCtx,pNewObj)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemObjectTextSrc_QueryInterface(IWbemObjectTextSrc* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemObjectTextSrc_AddRef(IWbemObjectTextSrc* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemObjectTextSrc_Release(IWbemObjectTextSrc* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemObjectTextSrc methods ***/
static inline HRESULT IWbemObjectTextSrc_GetText(IWbemObjectTextSrc* This,LONG lFlags,IWbemClassObject *pObj,ULONG uObjTextFormat,IWbemContext *pCtx,BSTR *strText) {
    return This->lpVtbl->GetText(This,lFlags,pObj,uObjTextFormat,pCtx,strText);
}
static inline HRESULT IWbemObjectTextSrc_CreateFromText(IWbemObjectTextSrc* This,LONG lFlags,BSTR strText,ULONG uObjTextFormat,IWbemContext *pCtx,IWbemClassObject **pNewObj) {
    return This->lpVtbl->CreateFromText(This,lFlags,strText,uObjTextFormat,pCtx,pNewObj);
}
#endif
#endif

#endif


#endif  /* __IWbemObjectTextSrc_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemObjectAccess interface
 */
#ifndef __IWbemObjectAccess_INTERFACE_DEFINED__
#define __IWbemObjectAccess_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemObjectAccess, 0x49353c9a, 0x516b, 0x11d1, 0xae,0xa6, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("49353c9a-516b-11d1-aea6-00c04fb68820")
IWbemObjectAccess : public IWbemClassObject
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyHandle(
        LPCWSTR wszPropertyName,
        CIMTYPE *pType,
        LONG *plHandle) = 0;

    virtual HRESULT STDMETHODCALLTYPE WritePropertyValue(
        LONG lHandle,
        LONG lNumBytes,
        const byte *aData) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReadPropertyValue(
        LONG lHandle,
        LONG lBufferSize,
        LONG *plNumBytes,
        byte *aData) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReadDWORD(
        LONG lHandle,
        DWORD *pdw) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteDWORD(
        LONG lHandle,
        DWORD dw) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReadQWORD(
        LONG lHandle,
        UINT64 *pqw) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteQWORD(
        LONG lHandle,
        UINT64 pw) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyInfoByHandle(
        LONG lHandle,
        BSTR *pstrName,
        CIMTYPE *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE Lock(
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unlock(
        LONG lFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemObjectAccess, 0x49353c9a, 0x516b, 0x11d1, 0xae,0xa6, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemObjectAccessVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemObjectAccess *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemObjectAccess *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemObjectAccess *This);

    /*** IWbemClassObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetQualifierSet)(
        IWbemObjectAccess *This,
        IWbemQualifierSet **ppQualSet);

    HRESULT (STDMETHODCALLTYPE *Get)(
        IWbemObjectAccess *This,
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pVal,
        CIMTYPE *pType,
        LONG *plFlavor);

    HRESULT (STDMETHODCALLTYPE *Put)(
        IWbemObjectAccess *This,
        LPCWSTR wszName,
        LONG lFlags,
        VARIANT *pVal,
        CIMTYPE Type);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IWbemObjectAccess *This,
        LPCWSTR wszName);

    HRESULT (STDMETHODCALLTYPE *GetNames)(
        IWbemObjectAccess *This,
        LPCWSTR wszQualifierName,
        LONG lFlags,
        VARIANT *pQualifierVal,
        SAFEARRAY **pNames);

    HRESULT (STDMETHODCALLTYPE *BeginEnumeration)(
        IWbemObjectAccess *This,
        LONG lEnumFlags);

    HRESULT (STDMETHODCALLTYPE *Next)(
        IWbemObjectAccess *This,
        LONG lFlags,
        BSTR *strName,
        VARIANT *pVal,
        CIMTYPE *pType,
        LONG *plFlavor);

    HRESULT (STDMETHODCALLTYPE *EndEnumeration)(
        IWbemObjectAccess *This);

    HRESULT (STDMETHODCALLTYPE *GetPropertyQualifierSet)(
        IWbemObjectAccess *This,
        LPCWSTR wszProperty,
        IWbemQualifierSet **ppQualSet);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IWbemObjectAccess *This,
        IWbemClassObject **ppCopy);

    HRESULT (STDMETHODCALLTYPE *GetObjectText)(
        IWbemObjectAccess *This,
        LONG lFlags,
        BSTR *pstrObjectText);

    HRESULT (STDMETHODCALLTYPE *SpawnDerivedClass)(
        IWbemObjectAccess *This,
        LONG lFlags,
        IWbemClassObject **ppNewClass);

    HRESULT (STDMETHODCALLTYPE *SpawnInstance)(
        IWbemObjectAccess *This,
        LONG lFlags,
        IWbemClassObject **ppNewInstance);

    HRESULT (STDMETHODCALLTYPE *CompareTo)(
        IWbemObjectAccess *This,
        LONG lFlags,
        IWbemClassObject *pCompareTo);

    HRESULT (STDMETHODCALLTYPE *GetPropertyOrigin)(
        IWbemObjectAccess *This,
        LPCWSTR wszName,
        BSTR *pstrClassName);

    HRESULT (STDMETHODCALLTYPE *InheritsFrom)(
        IWbemObjectAccess *This,
        LPCWSTR strAncestor);

    HRESULT (STDMETHODCALLTYPE *GetMethod)(
        IWbemObjectAccess *This,
        LPCWSTR wszName,
        LONG lFlags,
        IWbemClassObject **ppInSignature,
        IWbemClassObject **ppOutSignature);

    HRESULT (STDMETHODCALLTYPE *PutMethod)(
        IWbemObjectAccess *This,
        LPCWSTR wszName,
        LONG lFlags,
        IWbemClassObject *pInSignature,
        IWbemClassObject *pOutSignature);

    HRESULT (STDMETHODCALLTYPE *DeleteMethod)(
        IWbemObjectAccess *This,
        LPCWSTR wszName);

    HRESULT (STDMETHODCALLTYPE *BeginMethodEnumeration)(
        IWbemObjectAccess *This,
        LONG lEnumFlags);

    HRESULT (STDMETHODCALLTYPE *NextMethod)(
        IWbemObjectAccess *This,
        LONG lFlags,
        BSTR *pstrName,
        IWbemClassObject **ppInSignature,
        IWbemClassObject **ppOutSignature);

    HRESULT (STDMETHODCALLTYPE *EndMethodEnumeration)(
        IWbemObjectAccess *This);

    HRESULT (STDMETHODCALLTYPE *GetMethodQualifierSet)(
        IWbemObjectAccess *This,
        LPCWSTR wszMethod,
        IWbemQualifierSet **ppQualSet);

    HRESULT (STDMETHODCALLTYPE *GetMethodOrigin)(
        IWbemObjectAccess *This,
        LPCWSTR wszMethodName,
        BSTR *pstrClassName);

    /*** IWbemObjectAccess methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyHandle)(
        IWbemObjectAccess *This,
        LPCWSTR wszPropertyName,
        CIMTYPE *pType,
        LONG *plHandle);

    HRESULT (STDMETHODCALLTYPE *WritePropertyValue)(
        IWbemObjectAccess *This,
        LONG lHandle,
        LONG lNumBytes,
        const byte *aData);

    HRESULT (STDMETHODCALLTYPE *ReadPropertyValue)(
        IWbemObjectAccess *This,
        LONG lHandle,
        LONG lBufferSize,
        LONG *plNumBytes,
        byte *aData);

    HRESULT (STDMETHODCALLTYPE *ReadDWORD)(
        IWbemObjectAccess *This,
        LONG lHandle,
        DWORD *pdw);

    HRESULT (STDMETHODCALLTYPE *WriteDWORD)(
        IWbemObjectAccess *This,
        LONG lHandle,
        DWORD dw);

    HRESULT (STDMETHODCALLTYPE *ReadQWORD)(
        IWbemObjectAccess *This,
        LONG lHandle,
        UINT64 *pqw);

    HRESULT (STDMETHODCALLTYPE *WriteQWORD)(
        IWbemObjectAccess *This,
        LONG lHandle,
        UINT64 pw);

    HRESULT (STDMETHODCALLTYPE *GetPropertyInfoByHandle)(
        IWbemObjectAccess *This,
        LONG lHandle,
        BSTR *pstrName,
        CIMTYPE *pType);

    HRESULT (STDMETHODCALLTYPE *Lock)(
        IWbemObjectAccess *This,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *Unlock)(
        IWbemObjectAccess *This,
        LONG lFlags);

    END_INTERFACE
} IWbemObjectAccessVtbl;

interface IWbemObjectAccess {
    CONST_VTBL IWbemObjectAccessVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemObjectAccess_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemObjectAccess_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemObjectAccess_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemClassObject methods ***/
#define IWbemObjectAccess_GetQualifierSet(This,ppQualSet) (This)->lpVtbl->GetQualifierSet(This,ppQualSet)
#define IWbemObjectAccess_Get(This,wszName,lFlags,pVal,pType,plFlavor) (This)->lpVtbl->Get(This,wszName,lFlags,pVal,pType,plFlavor)
#define IWbemObjectAccess_Put(This,wszName,lFlags,pVal,Type) (This)->lpVtbl->Put(This,wszName,lFlags,pVal,Type)
#define IWbemObjectAccess_Delete(This,wszName) (This)->lpVtbl->Delete(This,wszName)
#define IWbemObjectAccess_GetNames(This,wszQualifierName,lFlags,pQualifierVal,pNames) (This)->lpVtbl->GetNames(This,wszQualifierName,lFlags,pQualifierVal,pNames)
#define IWbemObjectAccess_BeginEnumeration(This,lEnumFlags) (This)->lpVtbl->BeginEnumeration(This,lEnumFlags)
#define IWbemObjectAccess_Next(This,lFlags,strName,pVal,pType,plFlavor) (This)->lpVtbl->Next(This,lFlags,strName,pVal,pType,plFlavor)
#define IWbemObjectAccess_EndEnumeration(This) (This)->lpVtbl->EndEnumeration(This)
#define IWbemObjectAccess_GetPropertyQualifierSet(This,wszProperty,ppQualSet) (This)->lpVtbl->GetPropertyQualifierSet(This,wszProperty,ppQualSet)
#define IWbemObjectAccess_Clone(This,ppCopy) (This)->lpVtbl->Clone(This,ppCopy)
#define IWbemObjectAccess_GetObjectText(This,lFlags,pstrObjectText) (This)->lpVtbl->GetObjectText(This,lFlags,pstrObjectText)
#define IWbemObjectAccess_SpawnDerivedClass(This,lFlags,ppNewClass) (This)->lpVtbl->SpawnDerivedClass(This,lFlags,ppNewClass)
#define IWbemObjectAccess_SpawnInstance(This,lFlags,ppNewInstance) (This)->lpVtbl->SpawnInstance(This,lFlags,ppNewInstance)
#define IWbemObjectAccess_CompareTo(This,lFlags,pCompareTo) (This)->lpVtbl->CompareTo(This,lFlags,pCompareTo)
#define IWbemObjectAccess_GetPropertyOrigin(This,wszName,pstrClassName) (This)->lpVtbl->GetPropertyOrigin(This,wszName,pstrClassName)
#define IWbemObjectAccess_InheritsFrom(This,strAncestor) (This)->lpVtbl->InheritsFrom(This,strAncestor)
#define IWbemObjectAccess_GetMethod(This,wszName,lFlags,ppInSignature,ppOutSignature) (This)->lpVtbl->GetMethod(This,wszName,lFlags,ppInSignature,ppOutSignature)
#define IWbemObjectAccess_PutMethod(This,wszName,lFlags,pInSignature,pOutSignature) (This)->lpVtbl->PutMethod(This,wszName,lFlags,pInSignature,pOutSignature)
#define IWbemObjectAccess_DeleteMethod(This,wszName) (This)->lpVtbl->DeleteMethod(This,wszName)
#define IWbemObjectAccess_BeginMethodEnumeration(This,lEnumFlags) (This)->lpVtbl->BeginMethodEnumeration(This,lEnumFlags)
#define IWbemObjectAccess_NextMethod(This,lFlags,pstrName,ppInSignature,ppOutSignature) (This)->lpVtbl->NextMethod(This,lFlags,pstrName,ppInSignature,ppOutSignature)
#define IWbemObjectAccess_EndMethodEnumeration(This) (This)->lpVtbl->EndMethodEnumeration(This)
#define IWbemObjectAccess_GetMethodQualifierSet(This,wszMethod,ppQualSet) (This)->lpVtbl->GetMethodQualifierSet(This,wszMethod,ppQualSet)
#define IWbemObjectAccess_GetMethodOrigin(This,wszMethodName,pstrClassName) (This)->lpVtbl->GetMethodOrigin(This,wszMethodName,pstrClassName)
/*** IWbemObjectAccess methods ***/
#define IWbemObjectAccess_GetPropertyHandle(This,wszPropertyName,pType,plHandle) (This)->lpVtbl->GetPropertyHandle(This,wszPropertyName,pType,plHandle)
#define IWbemObjectAccess_WritePropertyValue(This,lHandle,lNumBytes,aData) (This)->lpVtbl->WritePropertyValue(This,lHandle,lNumBytes,aData)
#define IWbemObjectAccess_ReadPropertyValue(This,lHandle,lBufferSize,plNumBytes,aData) (This)->lpVtbl->ReadPropertyValue(This,lHandle,lBufferSize,plNumBytes,aData)
#define IWbemObjectAccess_ReadDWORD(This,lHandle,pdw) (This)->lpVtbl->ReadDWORD(This,lHandle,pdw)
#define IWbemObjectAccess_WriteDWORD(This,lHandle,dw) (This)->lpVtbl->WriteDWORD(This,lHandle,dw)
#define IWbemObjectAccess_ReadQWORD(This,lHandle,pqw) (This)->lpVtbl->ReadQWORD(This,lHandle,pqw)
#define IWbemObjectAccess_WriteQWORD(This,lHandle,pw) (This)->lpVtbl->WriteQWORD(This,lHandle,pw)
#define IWbemObjectAccess_GetPropertyInfoByHandle(This,lHandle,pstrName,pType) (This)->lpVtbl->GetPropertyInfoByHandle(This,lHandle,pstrName,pType)
#define IWbemObjectAccess_Lock(This,lFlags) (This)->lpVtbl->Lock(This,lFlags)
#define IWbemObjectAccess_Unlock(This,lFlags) (This)->lpVtbl->Unlock(This,lFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemObjectAccess_QueryInterface(IWbemObjectAccess* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemObjectAccess_AddRef(IWbemObjectAccess* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemObjectAccess_Release(IWbemObjectAccess* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemClassObject methods ***/
static inline HRESULT IWbemObjectAccess_GetQualifierSet(IWbemObjectAccess* This,IWbemQualifierSet **ppQualSet) {
    return This->lpVtbl->GetQualifierSet(This,ppQualSet);
}
static inline HRESULT IWbemObjectAccess_Get(IWbemObjectAccess* This,LPCWSTR wszName,LONG lFlags,VARIANT *pVal,CIMTYPE *pType,LONG *plFlavor) {
    return This->lpVtbl->Get(This,wszName,lFlags,pVal,pType,plFlavor);
}
static inline HRESULT IWbemObjectAccess_Put(IWbemObjectAccess* This,LPCWSTR wszName,LONG lFlags,VARIANT *pVal,CIMTYPE Type) {
    return This->lpVtbl->Put(This,wszName,lFlags,pVal,Type);
}
static inline HRESULT IWbemObjectAccess_Delete(IWbemObjectAccess* This,LPCWSTR wszName) {
    return This->lpVtbl->Delete(This,wszName);
}
static inline HRESULT IWbemObjectAccess_GetNames(IWbemObjectAccess* This,LPCWSTR wszQualifierName,LONG lFlags,VARIANT *pQualifierVal,SAFEARRAY **pNames) {
    return This->lpVtbl->GetNames(This,wszQualifierName,lFlags,pQualifierVal,pNames);
}
static inline HRESULT IWbemObjectAccess_BeginEnumeration(IWbemObjectAccess* This,LONG lEnumFlags) {
    return This->lpVtbl->BeginEnumeration(This,lEnumFlags);
}
static inline HRESULT IWbemObjectAccess_Next(IWbemObjectAccess* This,LONG lFlags,BSTR *strName,VARIANT *pVal,CIMTYPE *pType,LONG *plFlavor) {
    return This->lpVtbl->Next(This,lFlags,strName,pVal,pType,plFlavor);
}
static inline HRESULT IWbemObjectAccess_EndEnumeration(IWbemObjectAccess* This) {
    return This->lpVtbl->EndEnumeration(This);
}
static inline HRESULT IWbemObjectAccess_GetPropertyQualifierSet(IWbemObjectAccess* This,LPCWSTR wszProperty,IWbemQualifierSet **ppQualSet) {
    return This->lpVtbl->GetPropertyQualifierSet(This,wszProperty,ppQualSet);
}
static inline HRESULT IWbemObjectAccess_Clone(IWbemObjectAccess* This,IWbemClassObject **ppCopy) {
    return This->lpVtbl->Clone(This,ppCopy);
}
static inline HRESULT IWbemObjectAccess_GetObjectText(IWbemObjectAccess* This,LONG lFlags,BSTR *pstrObjectText) {
    return This->lpVtbl->GetObjectText(This,lFlags,pstrObjectText);
}
static inline HRESULT IWbemObjectAccess_SpawnDerivedClass(IWbemObjectAccess* This,LONG lFlags,IWbemClassObject **ppNewClass) {
    return This->lpVtbl->SpawnDerivedClass(This,lFlags,ppNewClass);
}
static inline HRESULT IWbemObjectAccess_SpawnInstance(IWbemObjectAccess* This,LONG lFlags,IWbemClassObject **ppNewInstance) {
    return This->lpVtbl->SpawnInstance(This,lFlags,ppNewInstance);
}
static inline HRESULT IWbemObjectAccess_CompareTo(IWbemObjectAccess* This,LONG lFlags,IWbemClassObject *pCompareTo) {
    return This->lpVtbl->CompareTo(This,lFlags,pCompareTo);
}
static inline HRESULT IWbemObjectAccess_GetPropertyOrigin(IWbemObjectAccess* This,LPCWSTR wszName,BSTR *pstrClassName) {
    return This->lpVtbl->GetPropertyOrigin(This,wszName,pstrClassName);
}
static inline HRESULT IWbemObjectAccess_InheritsFrom(IWbemObjectAccess* This,LPCWSTR strAncestor) {
    return This->lpVtbl->InheritsFrom(This,strAncestor);
}
static inline HRESULT IWbemObjectAccess_GetMethod(IWbemObjectAccess* This,LPCWSTR wszName,LONG lFlags,IWbemClassObject **ppInSignature,IWbemClassObject **ppOutSignature) {
    return This->lpVtbl->GetMethod(This,wszName,lFlags,ppInSignature,ppOutSignature);
}
static inline HRESULT IWbemObjectAccess_PutMethod(IWbemObjectAccess* This,LPCWSTR wszName,LONG lFlags,IWbemClassObject *pInSignature,IWbemClassObject *pOutSignature) {
    return This->lpVtbl->PutMethod(This,wszName,lFlags,pInSignature,pOutSignature);
}
static inline HRESULT IWbemObjectAccess_DeleteMethod(IWbemObjectAccess* This,LPCWSTR wszName) {
    return This->lpVtbl->DeleteMethod(This,wszName);
}
static inline HRESULT IWbemObjectAccess_BeginMethodEnumeration(IWbemObjectAccess* This,LONG lEnumFlags) {
    return This->lpVtbl->BeginMethodEnumeration(This,lEnumFlags);
}
static inline HRESULT IWbemObjectAccess_NextMethod(IWbemObjectAccess* This,LONG lFlags,BSTR *pstrName,IWbemClassObject **ppInSignature,IWbemClassObject **ppOutSignature) {
    return This->lpVtbl->NextMethod(This,lFlags,pstrName,ppInSignature,ppOutSignature);
}
static inline HRESULT IWbemObjectAccess_EndMethodEnumeration(IWbemObjectAccess* This) {
    return This->lpVtbl->EndMethodEnumeration(This);
}
static inline HRESULT IWbemObjectAccess_GetMethodQualifierSet(IWbemObjectAccess* This,LPCWSTR wszMethod,IWbemQualifierSet **ppQualSet) {
    return This->lpVtbl->GetMethodQualifierSet(This,wszMethod,ppQualSet);
}
static inline HRESULT IWbemObjectAccess_GetMethodOrigin(IWbemObjectAccess* This,LPCWSTR wszMethodName,BSTR *pstrClassName) {
    return This->lpVtbl->GetMethodOrigin(This,wszMethodName,pstrClassName);
}
/*** IWbemObjectAccess methods ***/
static inline HRESULT IWbemObjectAccess_GetPropertyHandle(IWbemObjectAccess* This,LPCWSTR wszPropertyName,CIMTYPE *pType,LONG *plHandle) {
    return This->lpVtbl->GetPropertyHandle(This,wszPropertyName,pType,plHandle);
}
static inline HRESULT IWbemObjectAccess_WritePropertyValue(IWbemObjectAccess* This,LONG lHandle,LONG lNumBytes,const byte *aData) {
    return This->lpVtbl->WritePropertyValue(This,lHandle,lNumBytes,aData);
}
static inline HRESULT IWbemObjectAccess_ReadPropertyValue(IWbemObjectAccess* This,LONG lHandle,LONG lBufferSize,LONG *plNumBytes,byte *aData) {
    return This->lpVtbl->ReadPropertyValue(This,lHandle,lBufferSize,plNumBytes,aData);
}
static inline HRESULT IWbemObjectAccess_ReadDWORD(IWbemObjectAccess* This,LONG lHandle,DWORD *pdw) {
    return This->lpVtbl->ReadDWORD(This,lHandle,pdw);
}
static inline HRESULT IWbemObjectAccess_WriteDWORD(IWbemObjectAccess* This,LONG lHandle,DWORD dw) {
    return This->lpVtbl->WriteDWORD(This,lHandle,dw);
}
static inline HRESULT IWbemObjectAccess_ReadQWORD(IWbemObjectAccess* This,LONG lHandle,UINT64 *pqw) {
    return This->lpVtbl->ReadQWORD(This,lHandle,pqw);
}
static inline HRESULT IWbemObjectAccess_WriteQWORD(IWbemObjectAccess* This,LONG lHandle,UINT64 pw) {
    return This->lpVtbl->WriteQWORD(This,lHandle,pw);
}
static inline HRESULT IWbemObjectAccess_GetPropertyInfoByHandle(IWbemObjectAccess* This,LONG lHandle,BSTR *pstrName,CIMTYPE *pType) {
    return This->lpVtbl->GetPropertyInfoByHandle(This,lHandle,pstrName,pType);
}
static inline HRESULT IWbemObjectAccess_Lock(IWbemObjectAccess* This,LONG lFlags) {
    return This->lpVtbl->Lock(This,lFlags);
}
static inline HRESULT IWbemObjectAccess_Unlock(IWbemObjectAccess* This,LONG lFlags) {
    return This->lpVtbl->Unlock(This,lFlags);
}
#endif
#endif

#endif


#endif  /* __IWbemObjectAccess_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMofCompiler interface
 */
#ifndef __IMofCompiler_INTERFACE_DEFINED__
#define __IMofCompiler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMofCompiler, 0x6daf974e, 0x2e37, 0x11d2, 0xae,0xc9, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6daf974e-2e37-11d2-aec9-00c04fb68820")
IMofCompiler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CompileFile(
        LPWSTR FileName,
        LPWSTR ServerAndNamespace,
        LPWSTR User,
        LPWSTR Authority,
        LPWSTR Password,
        LONG lOptionFlags,
        LONG lClassFlags,
        LONG lInstanceFlags,
        WBEM_COMPILE_STATUS_INFO *pInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE CompileBuffer(
        LONG BuffSize,
        BYTE *pBuffer,
        LPWSTR ServerAndNamespace,
        LPWSTR User,
        LPWSTR Authority,
        LPWSTR Password,
        LONG lOptionFlags,
        LONG lClassFlags,
        LONG lInstanceFlags,
        WBEM_COMPILE_STATUS_INFO *pInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateBMOF(
        LPWSTR TextFileName,
        LPWSTR BMOFFileName,
        LPWSTR ServerAndNamespace,
        LONG lOptionFlags,
        LONG lClassFlags,
        LONG lInstanceFlags,
        WBEM_COMPILE_STATUS_INFO *pInfo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMofCompiler, 0x6daf974e, 0x2e37, 0x11d2, 0xae,0xc9, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IMofCompilerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMofCompiler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMofCompiler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMofCompiler *This);

    /*** IMofCompiler methods ***/
    HRESULT (STDMETHODCALLTYPE *CompileFile)(
        IMofCompiler *This,
        LPWSTR FileName,
        LPWSTR ServerAndNamespace,
        LPWSTR User,
        LPWSTR Authority,
        LPWSTR Password,
        LONG lOptionFlags,
        LONG lClassFlags,
        LONG lInstanceFlags,
        WBEM_COMPILE_STATUS_INFO *pInfo);

    HRESULT (STDMETHODCALLTYPE *CompileBuffer)(
        IMofCompiler *This,
        LONG BuffSize,
        BYTE *pBuffer,
        LPWSTR ServerAndNamespace,
        LPWSTR User,
        LPWSTR Authority,
        LPWSTR Password,
        LONG lOptionFlags,
        LONG lClassFlags,
        LONG lInstanceFlags,
        WBEM_COMPILE_STATUS_INFO *pInfo);

    HRESULT (STDMETHODCALLTYPE *CreateBMOF)(
        IMofCompiler *This,
        LPWSTR TextFileName,
        LPWSTR BMOFFileName,
        LPWSTR ServerAndNamespace,
        LONG lOptionFlags,
        LONG lClassFlags,
        LONG lInstanceFlags,
        WBEM_COMPILE_STATUS_INFO *pInfo);

    END_INTERFACE
} IMofCompilerVtbl;

interface IMofCompiler {
    CONST_VTBL IMofCompilerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMofCompiler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMofCompiler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMofCompiler_Release(This) (This)->lpVtbl->Release(This)
/*** IMofCompiler methods ***/
#define IMofCompiler_CompileFile(This,FileName,ServerAndNamespace,User,Authority,Password,lOptionFlags,lClassFlags,lInstanceFlags,pInfo) (This)->lpVtbl->CompileFile(This,FileName,ServerAndNamespace,User,Authority,Password,lOptionFlags,lClassFlags,lInstanceFlags,pInfo)
#define IMofCompiler_CompileBuffer(This,BuffSize,pBuffer,ServerAndNamespace,User,Authority,Password,lOptionFlags,lClassFlags,lInstanceFlags,pInfo) (This)->lpVtbl->CompileBuffer(This,BuffSize,pBuffer,ServerAndNamespace,User,Authority,Password,lOptionFlags,lClassFlags,lInstanceFlags,pInfo)
#define IMofCompiler_CreateBMOF(This,TextFileName,BMOFFileName,ServerAndNamespace,lOptionFlags,lClassFlags,lInstanceFlags,pInfo) (This)->lpVtbl->CreateBMOF(This,TextFileName,BMOFFileName,ServerAndNamespace,lOptionFlags,lClassFlags,lInstanceFlags,pInfo)
#else
/*** IUnknown methods ***/
static inline HRESULT IMofCompiler_QueryInterface(IMofCompiler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMofCompiler_AddRef(IMofCompiler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMofCompiler_Release(IMofCompiler* This) {
    return This->lpVtbl->Release(This);
}
/*** IMofCompiler methods ***/
static inline HRESULT IMofCompiler_CompileFile(IMofCompiler* This,LPWSTR FileName,LPWSTR ServerAndNamespace,LPWSTR User,LPWSTR Authority,LPWSTR Password,LONG lOptionFlags,LONG lClassFlags,LONG lInstanceFlags,WBEM_COMPILE_STATUS_INFO *pInfo) {
    return This->lpVtbl->CompileFile(This,FileName,ServerAndNamespace,User,Authority,Password,lOptionFlags,lClassFlags,lInstanceFlags,pInfo);
}
static inline HRESULT IMofCompiler_CompileBuffer(IMofCompiler* This,LONG BuffSize,BYTE *pBuffer,LPWSTR ServerAndNamespace,LPWSTR User,LPWSTR Authority,LPWSTR Password,LONG lOptionFlags,LONG lClassFlags,LONG lInstanceFlags,WBEM_COMPILE_STATUS_INFO *pInfo) {
    return This->lpVtbl->CompileBuffer(This,BuffSize,pBuffer,ServerAndNamespace,User,Authority,Password,lOptionFlags,lClassFlags,lInstanceFlags,pInfo);
}
static inline HRESULT IMofCompiler_CreateBMOF(IMofCompiler* This,LPWSTR TextFileName,LPWSTR BMOFFileName,LPWSTR ServerAndNamespace,LONG lOptionFlags,LONG lClassFlags,LONG lInstanceFlags,WBEM_COMPILE_STATUS_INFO *pInfo) {
    return This->lpVtbl->CreateBMOF(This,TextFileName,BMOFFileName,ServerAndNamespace,lOptionFlags,lClassFlags,lInstanceFlags,pInfo);
}
#endif
#endif

#endif


#endif  /* __IMofCompiler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUnsecuredApartment interface
 */
#ifndef __IUnsecuredApartment_INTERFACE_DEFINED__
#define __IUnsecuredApartment_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUnsecuredApartment, 0x1cfaba8c, 0x1523, 0x11d1, 0xad,0x79, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1cfaba8c-1523-11d1-ad79-00c04fd8fdff")
IUnsecuredApartment : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateObjectStub(
        IUnknown *pObject,
        IUnknown **ppStub) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUnsecuredApartment, 0x1cfaba8c, 0x1523, 0x11d1, 0xad,0x79, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#else
typedef struct IUnsecuredApartmentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUnsecuredApartment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUnsecuredApartment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUnsecuredApartment *This);

    /*** IUnsecuredApartment methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateObjectStub)(
        IUnsecuredApartment *This,
        IUnknown *pObject,
        IUnknown **ppStub);

    END_INTERFACE
} IUnsecuredApartmentVtbl;

interface IUnsecuredApartment {
    CONST_VTBL IUnsecuredApartmentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUnsecuredApartment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUnsecuredApartment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUnsecuredApartment_Release(This) (This)->lpVtbl->Release(This)
/*** IUnsecuredApartment methods ***/
#define IUnsecuredApartment_CreateObjectStub(This,pObject,ppStub) (This)->lpVtbl->CreateObjectStub(This,pObject,ppStub)
#else
/*** IUnknown methods ***/
static inline HRESULT IUnsecuredApartment_QueryInterface(IUnsecuredApartment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUnsecuredApartment_AddRef(IUnsecuredApartment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUnsecuredApartment_Release(IUnsecuredApartment* This) {
    return This->lpVtbl->Release(This);
}
/*** IUnsecuredApartment methods ***/
static inline HRESULT IUnsecuredApartment_CreateObjectStub(IUnsecuredApartment* This,IUnknown *pObject,IUnknown **ppStub) {
    return This->lpVtbl->CreateObjectStub(This,pObject,ppStub);
}
#endif
#endif

#endif


#endif  /* __IUnsecuredApartment_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemUnsecuredApartment interface
 */
#ifndef __IWbemUnsecuredApartment_INTERFACE_DEFINED__
#define __IWbemUnsecuredApartment_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemUnsecuredApartment, 0x31739d04, 0x3471, 0x4cf4, 0x9a,0x7c, 0x57,0xa4,0x4a,0xe7,0x19,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("31739d04-3471-4cf4-9a7c-57a44ae71956")
IWbemUnsecuredApartment : public IUnsecuredApartment
{
    virtual HRESULT STDMETHODCALLTYPE CreateSinkStub(
        IWbemObjectSink *pSink,
        DWORD dwFlags,
        LPCWSTR wszReserved,
        IWbemObjectSink **ppStub) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemUnsecuredApartment, 0x31739d04, 0x3471, 0x4cf4, 0x9a,0x7c, 0x57,0xa4,0x4a,0xe7,0x19,0x56)
#endif
#else
typedef struct IWbemUnsecuredApartmentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemUnsecuredApartment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemUnsecuredApartment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemUnsecuredApartment *This);

    /*** IUnsecuredApartment methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateObjectStub)(
        IWbemUnsecuredApartment *This,
        IUnknown *pObject,
        IUnknown **ppStub);

    /*** IWbemUnsecuredApartment methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateSinkStub)(
        IWbemUnsecuredApartment *This,
        IWbemObjectSink *pSink,
        DWORD dwFlags,
        LPCWSTR wszReserved,
        IWbemObjectSink **ppStub);

    END_INTERFACE
} IWbemUnsecuredApartmentVtbl;

interface IWbemUnsecuredApartment {
    CONST_VTBL IWbemUnsecuredApartmentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemUnsecuredApartment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemUnsecuredApartment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemUnsecuredApartment_Release(This) (This)->lpVtbl->Release(This)
/*** IUnsecuredApartment methods ***/
#define IWbemUnsecuredApartment_CreateObjectStub(This,pObject,ppStub) (This)->lpVtbl->CreateObjectStub(This,pObject,ppStub)
/*** IWbemUnsecuredApartment methods ***/
#define IWbemUnsecuredApartment_CreateSinkStub(This,pSink,dwFlags,wszReserved,ppStub) (This)->lpVtbl->CreateSinkStub(This,pSink,dwFlags,wszReserved,ppStub)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemUnsecuredApartment_QueryInterface(IWbemUnsecuredApartment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemUnsecuredApartment_AddRef(IWbemUnsecuredApartment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemUnsecuredApartment_Release(IWbemUnsecuredApartment* This) {
    return This->lpVtbl->Release(This);
}
/*** IUnsecuredApartment methods ***/
static inline HRESULT IWbemUnsecuredApartment_CreateObjectStub(IWbemUnsecuredApartment* This,IUnknown *pObject,IUnknown **ppStub) {
    return This->lpVtbl->CreateObjectStub(This,pObject,ppStub);
}
/*** IWbemUnsecuredApartment methods ***/
static inline HRESULT IWbemUnsecuredApartment_CreateSinkStub(IWbemUnsecuredApartment* This,IWbemObjectSink *pSink,DWORD dwFlags,LPCWSTR wszReserved,IWbemObjectSink **ppStub) {
    return This->lpVtbl->CreateSinkStub(This,pSink,dwFlags,wszReserved,ppStub);
}
#endif
#endif

#endif


#endif  /* __IWbemUnsecuredApartment_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemStatusCodeText interface
 */
#ifndef __IWbemStatusCodeText_INTERFACE_DEFINED__
#define __IWbemStatusCodeText_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemStatusCodeText, 0xeb87e1bc, 0x3233, 0x11d2, 0xae,0xc9, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eb87e1bc-3233-11d2-aec9-00c04fb68820")
IWbemStatusCodeText : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetErrorCodeText(
        HRESULT hRes,
        LCID LocaleId,
        LONG lFlags,
        BSTR *MessageText) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFacilityCodeText(
        HRESULT hRes,
        LCID LocaleId,
        LONG lFlags,
        BSTR *MessageText) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemStatusCodeText, 0xeb87e1bc, 0x3233, 0x11d2, 0xae,0xc9, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemStatusCodeTextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemStatusCodeText *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemStatusCodeText *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemStatusCodeText *This);

    /*** IWbemStatusCodeText methods ***/
    HRESULT (STDMETHODCALLTYPE *GetErrorCodeText)(
        IWbemStatusCodeText *This,
        HRESULT hRes,
        LCID LocaleId,
        LONG lFlags,
        BSTR *MessageText);

    HRESULT (STDMETHODCALLTYPE *GetFacilityCodeText)(
        IWbemStatusCodeText *This,
        HRESULT hRes,
        LCID LocaleId,
        LONG lFlags,
        BSTR *MessageText);

    END_INTERFACE
} IWbemStatusCodeTextVtbl;

interface IWbemStatusCodeText {
    CONST_VTBL IWbemStatusCodeTextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemStatusCodeText_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemStatusCodeText_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemStatusCodeText_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemStatusCodeText methods ***/
#define IWbemStatusCodeText_GetErrorCodeText(This,hRes,LocaleId,lFlags,MessageText) (This)->lpVtbl->GetErrorCodeText(This,hRes,LocaleId,lFlags,MessageText)
#define IWbemStatusCodeText_GetFacilityCodeText(This,hRes,LocaleId,lFlags,MessageText) (This)->lpVtbl->GetFacilityCodeText(This,hRes,LocaleId,lFlags,MessageText)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemStatusCodeText_QueryInterface(IWbemStatusCodeText* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemStatusCodeText_AddRef(IWbemStatusCodeText* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemStatusCodeText_Release(IWbemStatusCodeText* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemStatusCodeText methods ***/
static inline HRESULT IWbemStatusCodeText_GetErrorCodeText(IWbemStatusCodeText* This,HRESULT hRes,LCID LocaleId,LONG lFlags,BSTR *MessageText) {
    return This->lpVtbl->GetErrorCodeText(This,hRes,LocaleId,lFlags,MessageText);
}
static inline HRESULT IWbemStatusCodeText_GetFacilityCodeText(IWbemStatusCodeText* This,HRESULT hRes,LCID LocaleId,LONG lFlags,BSTR *MessageText) {
    return This->lpVtbl->GetFacilityCodeText(This,hRes,LocaleId,lFlags,MessageText);
}
#endif
#endif

#endif


#endif  /* __IWbemStatusCodeText_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemBackupRestore interface
 */
#ifndef __IWbemBackupRestore_INTERFACE_DEFINED__
#define __IWbemBackupRestore_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemBackupRestore, 0xc49e32c7, 0xbc8b, 0x11d2, 0x85,0xd4, 0x00,0x10,0x5a,0x1f,0x83,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c49e32c7-bc8b-11d2-85d4-00105a1f8304")
IWbemBackupRestore : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Backup(
        LPCWSTR strBackupToFile,
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Restore(
        LPCWSTR strRestoreFromFile,
        LONG lFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemBackupRestore, 0xc49e32c7, 0xbc8b, 0x11d2, 0x85,0xd4, 0x00,0x10,0x5a,0x1f,0x83,0x04)
#endif
#else
typedef struct IWbemBackupRestoreVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemBackupRestore *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemBackupRestore *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemBackupRestore *This);

    /*** IWbemBackupRestore methods ***/
    HRESULT (STDMETHODCALLTYPE *Backup)(
        IWbemBackupRestore *This,
        LPCWSTR strBackupToFile,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *Restore)(
        IWbemBackupRestore *This,
        LPCWSTR strRestoreFromFile,
        LONG lFlags);

    END_INTERFACE
} IWbemBackupRestoreVtbl;

interface IWbemBackupRestore {
    CONST_VTBL IWbemBackupRestoreVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemBackupRestore_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemBackupRestore_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemBackupRestore_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemBackupRestore methods ***/
#define IWbemBackupRestore_Backup(This,strBackupToFile,lFlags) (This)->lpVtbl->Backup(This,strBackupToFile,lFlags)
#define IWbemBackupRestore_Restore(This,strRestoreFromFile,lFlags) (This)->lpVtbl->Restore(This,strRestoreFromFile,lFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemBackupRestore_QueryInterface(IWbemBackupRestore* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemBackupRestore_AddRef(IWbemBackupRestore* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemBackupRestore_Release(IWbemBackupRestore* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemBackupRestore methods ***/
static inline HRESULT IWbemBackupRestore_Backup(IWbemBackupRestore* This,LPCWSTR strBackupToFile,LONG lFlags) {
    return This->lpVtbl->Backup(This,strBackupToFile,lFlags);
}
static inline HRESULT IWbemBackupRestore_Restore(IWbemBackupRestore* This,LPCWSTR strRestoreFromFile,LONG lFlags) {
    return This->lpVtbl->Restore(This,strRestoreFromFile,lFlags);
}
#endif
#endif

#endif


#endif  /* __IWbemBackupRestore_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemBackupRestoreEx interface
 */
#ifndef __IWbemBackupRestoreEx_INTERFACE_DEFINED__
#define __IWbemBackupRestoreEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemBackupRestoreEx, 0xa359dec5, 0xe813, 0x4834, 0x8a,0x2a, 0xba,0x7f,0x1d,0x77,0x7d,0x76);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a359dec5-e813-4834-8a2a-ba7f1d777d76")
IWbemBackupRestoreEx : public IWbemBackupRestore
{
    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemBackupRestoreEx, 0xa359dec5, 0xe813, 0x4834, 0x8a,0x2a, 0xba,0x7f,0x1d,0x77,0x7d,0x76)
#endif
#else
typedef struct IWbemBackupRestoreExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemBackupRestoreEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemBackupRestoreEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemBackupRestoreEx *This);

    /*** IWbemBackupRestore methods ***/
    HRESULT (STDMETHODCALLTYPE *Backup)(
        IWbemBackupRestoreEx *This,
        LPCWSTR strBackupToFile,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *Restore)(
        IWbemBackupRestoreEx *This,
        LPCWSTR strRestoreFromFile,
        LONG lFlags);

    /*** IWbemBackupRestoreEx methods ***/
    HRESULT (STDMETHODCALLTYPE *Pause)(
        IWbemBackupRestoreEx *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IWbemBackupRestoreEx *This);

    END_INTERFACE
} IWbemBackupRestoreExVtbl;

interface IWbemBackupRestoreEx {
    CONST_VTBL IWbemBackupRestoreExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemBackupRestoreEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemBackupRestoreEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemBackupRestoreEx_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemBackupRestore methods ***/
#define IWbemBackupRestoreEx_Backup(This,strBackupToFile,lFlags) (This)->lpVtbl->Backup(This,strBackupToFile,lFlags)
#define IWbemBackupRestoreEx_Restore(This,strRestoreFromFile,lFlags) (This)->lpVtbl->Restore(This,strRestoreFromFile,lFlags)
/*** IWbemBackupRestoreEx methods ***/
#define IWbemBackupRestoreEx_Pause(This) (This)->lpVtbl->Pause(This)
#define IWbemBackupRestoreEx_Resume(This) (This)->lpVtbl->Resume(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemBackupRestoreEx_QueryInterface(IWbemBackupRestoreEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemBackupRestoreEx_AddRef(IWbemBackupRestoreEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemBackupRestoreEx_Release(IWbemBackupRestoreEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemBackupRestore methods ***/
static inline HRESULT IWbemBackupRestoreEx_Backup(IWbemBackupRestoreEx* This,LPCWSTR strBackupToFile,LONG lFlags) {
    return This->lpVtbl->Backup(This,strBackupToFile,lFlags);
}
static inline HRESULT IWbemBackupRestoreEx_Restore(IWbemBackupRestoreEx* This,LPCWSTR strRestoreFromFile,LONG lFlags) {
    return This->lpVtbl->Restore(This,strRestoreFromFile,lFlags);
}
/*** IWbemBackupRestoreEx methods ***/
static inline HRESULT IWbemBackupRestoreEx_Pause(IWbemBackupRestoreEx* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IWbemBackupRestoreEx_Resume(IWbemBackupRestoreEx* This) {
    return This->lpVtbl->Resume(This);
}
#endif
#endif

#endif


#endif  /* __IWbemBackupRestoreEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemRefresher interface
 */
#ifndef __IWbemRefresher_INTERFACE_DEFINED__
#define __IWbemRefresher_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemRefresher, 0x49353c99, 0x516b, 0x11d1, 0xae,0xa6, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("49353c99-516b-11d1-aea6-00c04fb68820")
IWbemRefresher : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Refresh(
        LONG lFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemRefresher, 0x49353c99, 0x516b, 0x11d1, 0xae,0xa6, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemRefresherVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemRefresher *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemRefresher *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemRefresher *This);

    /*** IWbemRefresher methods ***/
    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IWbemRefresher *This,
        LONG lFlags);

    END_INTERFACE
} IWbemRefresherVtbl;

interface IWbemRefresher {
    CONST_VTBL IWbemRefresherVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemRefresher_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemRefresher_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemRefresher_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemRefresher methods ***/
#define IWbemRefresher_Refresh(This,lFlags) (This)->lpVtbl->Refresh(This,lFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemRefresher_QueryInterface(IWbemRefresher* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemRefresher_AddRef(IWbemRefresher* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemRefresher_Release(IWbemRefresher* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemRefresher methods ***/
static inline HRESULT IWbemRefresher_Refresh(IWbemRefresher* This,LONG lFlags) {
    return This->lpVtbl->Refresh(This,lFlags);
}
#endif
#endif

#endif


#endif  /* __IWbemRefresher_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemHiPerfEnum interface
 */
#ifndef __IWbemHiPerfEnum_INTERFACE_DEFINED__
#define __IWbemHiPerfEnum_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemHiPerfEnum, 0x2705c288, 0x79ae, 0x11d2, 0xb3,0x48, 0x00,0x10,0x5a,0x1f,0x81,0x77);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2705c288-79ae-11d2-b348-00105a1f8177")
IWbemHiPerfEnum : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddObjects(
        LONG lFlags,
        ULONG uNumObjects,
        LONG *apIds,
        IWbemObjectAccess **apObj) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveObjects(
        LONG lFlags,
        ULONG uNumObjects,
        LONG *apIds) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjects(
        LONG lFlags,
        ULONG uNumObjects,
        IWbemObjectAccess **apObj,
        ULONG *puReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAll(
        LONG lFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemHiPerfEnum, 0x2705c288, 0x79ae, 0x11d2, 0xb3,0x48, 0x00,0x10,0x5a,0x1f,0x81,0x77)
#endif
#else
typedef struct IWbemHiPerfEnumVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemHiPerfEnum *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemHiPerfEnum *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemHiPerfEnum *This);

    /*** IWbemHiPerfEnum methods ***/
    HRESULT (STDMETHODCALLTYPE *AddObjects)(
        IWbemHiPerfEnum *This,
        LONG lFlags,
        ULONG uNumObjects,
        LONG *apIds,
        IWbemObjectAccess **apObj);

    HRESULT (STDMETHODCALLTYPE *RemoveObjects)(
        IWbemHiPerfEnum *This,
        LONG lFlags,
        ULONG uNumObjects,
        LONG *apIds);

    HRESULT (STDMETHODCALLTYPE *GetObjects)(
        IWbemHiPerfEnum *This,
        LONG lFlags,
        ULONG uNumObjects,
        IWbemObjectAccess **apObj,
        ULONG *puReturned);

    HRESULT (STDMETHODCALLTYPE *RemoveAll)(
        IWbemHiPerfEnum *This,
        LONG lFlags);

    END_INTERFACE
} IWbemHiPerfEnumVtbl;

interface IWbemHiPerfEnum {
    CONST_VTBL IWbemHiPerfEnumVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemHiPerfEnum_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemHiPerfEnum_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemHiPerfEnum_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemHiPerfEnum methods ***/
#define IWbemHiPerfEnum_AddObjects(This,lFlags,uNumObjects,apIds,apObj) (This)->lpVtbl->AddObjects(This,lFlags,uNumObjects,apIds,apObj)
#define IWbemHiPerfEnum_RemoveObjects(This,lFlags,uNumObjects,apIds) (This)->lpVtbl->RemoveObjects(This,lFlags,uNumObjects,apIds)
#define IWbemHiPerfEnum_GetObjects(This,lFlags,uNumObjects,apObj,puReturned) (This)->lpVtbl->GetObjects(This,lFlags,uNumObjects,apObj,puReturned)
#define IWbemHiPerfEnum_RemoveAll(This,lFlags) (This)->lpVtbl->RemoveAll(This,lFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemHiPerfEnum_QueryInterface(IWbemHiPerfEnum* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemHiPerfEnum_AddRef(IWbemHiPerfEnum* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemHiPerfEnum_Release(IWbemHiPerfEnum* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemHiPerfEnum methods ***/
static inline HRESULT IWbemHiPerfEnum_AddObjects(IWbemHiPerfEnum* This,LONG lFlags,ULONG uNumObjects,LONG *apIds,IWbemObjectAccess **apObj) {
    return This->lpVtbl->AddObjects(This,lFlags,uNumObjects,apIds,apObj);
}
static inline HRESULT IWbemHiPerfEnum_RemoveObjects(IWbemHiPerfEnum* This,LONG lFlags,ULONG uNumObjects,LONG *apIds) {
    return This->lpVtbl->RemoveObjects(This,lFlags,uNumObjects,apIds);
}
static inline HRESULT IWbemHiPerfEnum_GetObjects(IWbemHiPerfEnum* This,LONG lFlags,ULONG uNumObjects,IWbemObjectAccess **apObj,ULONG *puReturned) {
    return This->lpVtbl->GetObjects(This,lFlags,uNumObjects,apObj,puReturned);
}
static inline HRESULT IWbemHiPerfEnum_RemoveAll(IWbemHiPerfEnum* This,LONG lFlags) {
    return This->lpVtbl->RemoveAll(This,lFlags);
}
#endif
#endif

#endif


#endif  /* __IWbemHiPerfEnum_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemConfigureRefresher interface
 */
#ifndef __IWbemConfigureRefresher_INTERFACE_DEFINED__
#define __IWbemConfigureRefresher_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemConfigureRefresher, 0x49353c92, 0x516b, 0x11d1, 0xae,0xa6, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("49353c92-516b-11d1-aea6-00c04fb68820")
IWbemConfigureRefresher : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddObjectByPath(
        IWbemServices *pNamespace,
        LPCWSTR wszPath,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemClassObject **ppRefreshable,
        LONG *plId) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddObjectByTemplate(
        IWbemServices *pNamespace,
        IWbemClassObject *pTemplate,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemClassObject **ppRefreshable,
        LONG *plId) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddRefresher(
        IWbemRefresher *pRefresher,
        LONG lFlags,
        LONG *plId) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        LONG lId,
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddEnum(
        IWbemServices *pNamespace,
        LPCWSTR wszClassName,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemHiPerfEnum **ppEnum,
        LONG *plId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemConfigureRefresher, 0x49353c92, 0x516b, 0x11d1, 0xae,0xa6, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemConfigureRefresherVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemConfigureRefresher *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemConfigureRefresher *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemConfigureRefresher *This);

    /*** IWbemConfigureRefresher methods ***/
    HRESULT (STDMETHODCALLTYPE *AddObjectByPath)(
        IWbemConfigureRefresher *This,
        IWbemServices *pNamespace,
        LPCWSTR wszPath,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemClassObject **ppRefreshable,
        LONG *plId);

    HRESULT (STDMETHODCALLTYPE *AddObjectByTemplate)(
        IWbemConfigureRefresher *This,
        IWbemServices *pNamespace,
        IWbemClassObject *pTemplate,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemClassObject **ppRefreshable,
        LONG *plId);

    HRESULT (STDMETHODCALLTYPE *AddRefresher)(
        IWbemConfigureRefresher *This,
        IWbemRefresher *pRefresher,
        LONG lFlags,
        LONG *plId);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IWbemConfigureRefresher *This,
        LONG lId,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *AddEnum)(
        IWbemConfigureRefresher *This,
        IWbemServices *pNamespace,
        LPCWSTR wszClassName,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemHiPerfEnum **ppEnum,
        LONG *plId);

    END_INTERFACE
} IWbemConfigureRefresherVtbl;

interface IWbemConfigureRefresher {
    CONST_VTBL IWbemConfigureRefresherVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemConfigureRefresher_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemConfigureRefresher_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemConfigureRefresher_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemConfigureRefresher methods ***/
#define IWbemConfigureRefresher_AddObjectByPath(This,pNamespace,wszPath,lFlags,pContext,ppRefreshable,plId) (This)->lpVtbl->AddObjectByPath(This,pNamespace,wszPath,lFlags,pContext,ppRefreshable,plId)
#define IWbemConfigureRefresher_AddObjectByTemplate(This,pNamespace,pTemplate,lFlags,pContext,ppRefreshable,plId) (This)->lpVtbl->AddObjectByTemplate(This,pNamespace,pTemplate,lFlags,pContext,ppRefreshable,plId)
#define IWbemConfigureRefresher_AddRefresher(This,pRefresher,lFlags,plId) (This)->lpVtbl->AddRefresher(This,pRefresher,lFlags,plId)
#define IWbemConfigureRefresher_Remove(This,lId,lFlags) (This)->lpVtbl->Remove(This,lId,lFlags)
#define IWbemConfigureRefresher_AddEnum(This,pNamespace,wszClassName,lFlags,pContext,ppEnum,plId) (This)->lpVtbl->AddEnum(This,pNamespace,wszClassName,lFlags,pContext,ppEnum,plId)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemConfigureRefresher_QueryInterface(IWbemConfigureRefresher* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemConfigureRefresher_AddRef(IWbemConfigureRefresher* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemConfigureRefresher_Release(IWbemConfigureRefresher* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemConfigureRefresher methods ***/
static inline HRESULT IWbemConfigureRefresher_AddObjectByPath(IWbemConfigureRefresher* This,IWbemServices *pNamespace,LPCWSTR wszPath,LONG lFlags,IWbemContext *pContext,IWbemClassObject **ppRefreshable,LONG *plId) {
    return This->lpVtbl->AddObjectByPath(This,pNamespace,wszPath,lFlags,pContext,ppRefreshable,plId);
}
static inline HRESULT IWbemConfigureRefresher_AddObjectByTemplate(IWbemConfigureRefresher* This,IWbemServices *pNamespace,IWbemClassObject *pTemplate,LONG lFlags,IWbemContext *pContext,IWbemClassObject **ppRefreshable,LONG *plId) {
    return This->lpVtbl->AddObjectByTemplate(This,pNamespace,pTemplate,lFlags,pContext,ppRefreshable,plId);
}
static inline HRESULT IWbemConfigureRefresher_AddRefresher(IWbemConfigureRefresher* This,IWbemRefresher *pRefresher,LONG lFlags,LONG *plId) {
    return This->lpVtbl->AddRefresher(This,pRefresher,lFlags,plId);
}
static inline HRESULT IWbemConfigureRefresher_Remove(IWbemConfigureRefresher* This,LONG lId,LONG lFlags) {
    return This->lpVtbl->Remove(This,lId,lFlags);
}
static inline HRESULT IWbemConfigureRefresher_AddEnum(IWbemConfigureRefresher* This,IWbemServices *pNamespace,LPCWSTR wszClassName,LONG lFlags,IWbemContext *pContext,IWbemHiPerfEnum **ppEnum,LONG *plId) {
    return This->lpVtbl->AddEnum(This,pNamespace,wszClassName,lFlags,pContext,ppEnum,plId);
}
#endif
#endif

#endif


#endif  /* __IWbemConfigureRefresher_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wbemcli_h__ */
