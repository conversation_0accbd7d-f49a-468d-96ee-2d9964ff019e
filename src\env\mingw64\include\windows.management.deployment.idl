/*
 * Copyright 2023 R<PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "windows.foundation.idl";
import "windows.applicationmodel.idl";

namespace Windows.Management.Deployment {

    typedef enum DeploymentOptions DeploymentOptions;
    typedef enum DeploymentProgressState DeploymentProgressState;
    typedef enum PackageInstallState PackageInstallState;
    typedef enum PackageState PackageState;
    typedef enum PackageTypes PackageTypes;
    typedef enum RemovalOptions RemovalOptions;

    typedef struct DeploymentProgress DeploymentProgress;

    interface IDeploymentResult;
    interface IPackageManager;
    interface IPackageManager2;
    interface IPackageUserInformation;

    runtimeclass DeploymentResult;
    runtimeclass PackageManager;
    runtimeclass PackageUserInformation;

    declare {
        interface Windows.Foundation.Collections.IIterable<Windows.Foundation.Uri *>;
        interface Windows.Foundation.Collections.IIterator<Windows.Foundation.Uri *>;
        interface Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *>;
        interface Windows.Foundation.Collections.IIterator<Windows.ApplicationModel.Package *>;
        interface Windows.Foundation.Collections.IIterable<Windows.Management.Deployment.PackageUserInformation *>;
        interface Windows.Foundation.Collections.IIterator<Windows.Management.Deployment.PackageUserInformation *>;
        interface Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress>;
        interface Windows.Foundation.AsyncOperationProgressHandler<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress>;
        interface Windows.Foundation.AsyncOperationWithProgressCompletedHandler<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress>;
    }

    [contract(Windows.Foundation.UniversalApiContract, 1.0)]
    [flags]
    enum DeploymentOptions
    {
        None                            = 0x0,
        ForceApplicationShutdown        = 0x1,
        DevelopmentMode                 = 0x2,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        InstallAllResources             = 0x20,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        ForceTargetApplicationShutdown  = 0x40,
        [contract(Windows.Foundation.UniversalApiContract, 4.0)]
        RequiredContentGroupOnly        = 0x100,
        [contract(Windows.Foundation.UniversalApiContract, 7.0)]
        ForceUpdateFromAnyVersion       = 0x40000,
        [contract(Windows.Foundation.UniversalApiContract, 10.0)]
        RetainFilesOnFailure            = 0x200000,
        [contract(Windows.Foundation.UniversalApiContract, 10.0)]
        StageInPlace                    = 0x400000,
    };

    [contract(Windows.Foundation.UniversalApiContract, 1.0)]
    enum DeploymentProgressState
    {
        Queued     = 0,
        Processing = 1,
    };

    [contract(Windows.Foundation.UniversalApiContract, 1.0)]
    enum PackageInstallState
    {
        NotInstalled = 0,
        Staged       = 1,
        Installed    = 2,
        [contract(Windows.Foundation.UniversalApiContract, 2.0)]
        Paused       = 6,
    };

    [contract(Windows.Foundation.UniversalApiContract, 1.0)]
    enum PackageState
    {
        Normal         = 0,
        LicenseInvalid = 1,
        Modified       = 2,
        Tampered       = 3,
    };

    [contract(Windows.Foundation.UniversalApiContract, 1.0)]
    [flags]
    enum PackageTypes
    {
        None      = 0x0,
        Main      = 0x1,
        Framework = 0x2,
        Resource  = 0x4,
        Bundle    = 0x8,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Xap       = 0x10,
        [contract(Windows.Foundation.UniversalApiContract, 3.0)]
        Optional  = 0x20,
        [contract(Windows.Foundation.UniversalApiContract, 10.0)]
        All       = 0xffffffff,
    };

    [contract(Windows.Foundation.UniversalApiContract, 1.0)]
    [flags]
    enum RemovalOptions
    {
        None                            = 0x0,
        PreserveApplicationData         = 0x1000,
        [contract(Windows.Foundation.UniversalApiContract, 10.0)]
        PreserveRoamableApplicationData = 0x80,
        [contract(Windows.Foundation.UniversalApiContract, 7.0)]
        RemoveForAllUsers               = 0x80000,
    };

    [contract(Windows.Foundation.UniversalApiContract, 1.0)]
    struct DeploymentProgress
    {
        Windows.Management.Deployment.DeploymentProgressState state;
        UINT32 percentage;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Management.Deployment.DeploymentResult),
        uuid(2563b9ae-b77d-4c1f-8a7b-20e6ad515ef3)
    ]
    interface IDeploymentResult : IInspectable
    {
        [propget] HRESULT ErrorText([out, retval] HSTRING *value);
        [propget] HRESULT ActivityId([out, retval] GUID *value);
        [propget] HRESULT ExtendedErrorCode([out, retval] HRESULT *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Management.Deployment.PackageManager),
        uuid(9a7d4b65-5e8f-4fc7-a2e5-7f6925cb8b53)
    ]
    interface IPackageManager : IInspectable
    {
        [overload("AddPackageAsync")]
        HRESULT AddPackageAsync([in] Windows.Foundation.Uri *uri, [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Uri *> *dependencies, [in] Windows.Management.Deployment.DeploymentOptions options,
                                [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        HRESULT UpdatePackageAsync([in] Windows.Foundation.Uri *uri, [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Uri *> *dependencies, [in] Windows.Management.Deployment.DeploymentOptions options,
                                   [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        HRESULT RemovePackageAsync([in] HSTRING name, [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        HRESULT StagePackageAsync([in] Windows.Foundation.Uri *uri, [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Uri *> *dependencies,
                                  [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        [overload("RegisterPackageAsync")]
        HRESULT RegisterPackageAsync([in] Windows.Foundation.Uri* uri, [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Uri *> *dependencies, [in] Windows.Management.Deployment.DeploymentOptions options,
                                     [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        [overload("FindPackages")]
        HRESULT FindPackages([out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackagesForUser")]
        HRESULT FindPackagesByUserSecurityId([in] HSTRING sid, [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackages")]
        HRESULT FindPackagesByNamePublisher([in] HSTRING name, [in] HSTRING publisher, [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackagesForUser")]
        HRESULT FindPackagesByUserSecurityIdNamePublisher([in] HSTRING sid, [in] HSTRING name, [in] HSTRING publisher, [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        HRESULT FindUsers([in] HSTRING name, [out, retval] Windows.Foundation.Collections.IIterable<Windows.Management.Deployment.PackageUserInformation *> **users);
        HRESULT SetPackageState([in] HSTRING name, [in] Windows.Management.Deployment.PackageState state);
        [overload("FindPackage")]
        HRESULT FindPackageByPackageFullName([in] HSTRING name, [out, retval] Windows.ApplicationModel.Package **package);
        HRESULT CleanupPackageForUserAsync([in] HSTRING name, [in] HSTRING sid, [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        [overload("FindPackages")]
        HRESULT FindPackagesByPackageFamilyName([in] HSTRING family_name, [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackagesForUser")]
        HRESULT FindPackagesByUserSecurityIdPackageFamilyName([in] HSTRING sid, [in] HSTRING family_name, [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackageForUser")]
        HRESULT FindPackageByUserSecurityIdPackageFullName([in] HSTRING sid, [in] HSTRING name, [out, retval] Windows.ApplicationModel.Package **package);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Management.Deployment.PackageManager),
        uuid(f7aad08d-0840-46f2-b5d8-cad47693a095)
    ]
    interface IPackageManager2 : IInspectable
    {
        [overload("RemovePackageAsync")]
        HRESULT RemovePackageWithOptionsAsync([in] HSTRING name, [in] Windows.Management.Deployment.RemovalOptions options,
                                              [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        [overload("StagePackageAsync")]
        HRESULT StagePackageWithOptionsAsync([in] Windows.Foundation.Uri *uri, [in] Windows.Foundation.Collections.IIterable<Windows.Foundation.Uri *> *dependencies,
                                             [in] Windows.Management.Deployment.DeploymentOptions options,
                                             [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        HRESULT RegisterPackageByFullNameAsync([in] HSTRING name, [in] Windows.Foundation.Collections.IIterable<HSTRING> *dependencies,
                                               [in] Windows.Management.Deployment.DeploymentOptions options,
                                               [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
        [overload("FindPackagesWithPackageTypes")]
        HRESULT FindPackagesWithPackageTypes([in] Windows.Management.Deployment.PackageTypes types, [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackagesForUserWithPackageTypes")]
        HRESULT FindPackagesByUserSecurityIdWithPackageTypes([in] HSTRING sid, [in] Windows.Management.Deployment.PackageTypes types,
                                                             [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackagesWithPackageTypes")]
        HRESULT FindPackagesByNamePublisherWithPackageTypes([in] HSTRING name, [in] HSTRING publisher, [in] Windows.Management.Deployment.PackageTypes types,
                                                            [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackagesForUserWithPackageTypes")]
        HRESULT FindPackagesByUserSecurityIdNamePublisherWithPackageTypes([in] HSTRING sid, [in] HSTRING name, [in] HSTRING publisher, [in] Windows.Management.Deployment.PackageTypes types,
                                                                          [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackagesWithPackageTypes")]
        HRESULT FindPackagesByPackageFamilyNameWithPackageTypes([in] HSTRING family_name, [in] Windows.Management.Deployment.PackageTypes types,
                                                                [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("FindPackagesForUserWithPackageTypes")]
        HRESULT FindPackagesByUserSecurityIdPackageFamilyNameWithPackageTypes([in] HSTRING sid, [in] HSTRING family_name, [in] Windows.Management.Deployment.PackageTypes types,
                                                                              [out, retval] Windows.Foundation.Collections.IIterable<Windows.ApplicationModel.Package *> **packages);
        [overload("StageUserDataAsync")]
        HRESULT StageUserDataAsync([in] HSTRING name, [out, retval] Windows.Foundation.IAsyncOperationWithProgress<Windows.Management.Deployment.DeploymentResult *, Windows.Management.Deployment.DeploymentProgress> **operation);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.Management.Deployment.PackageUserInformation),
        uuid(f6383423-fa09-4cbc-9055-15ca275e2e7e)
    ]
    interface IPackageUserInformation : IInspectable
    {
        [propget] HRESULT UserSecurityId([out, retval] HSTRING *value);
        [propget] HRESULT InstallState([out, retval] Windows.Management.Deployment.PackageInstallState *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass DeploymentResult
    {
        [default] interface Windows.Management.Deployment.IDeploymentResult;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.Management.Deployment.IDeploymentResult2;
    }

    [
        activatable(Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass PackageManager
    {
        [default] interface Windows.Management.Deployment.IPackageManager;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.Management.Deployment.IPackageManager2;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.Management.Deployment.IPackageManager3;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.Management.Deployment.IPackageManager4;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.Management.Deployment.IPackageManager5;
        [contract(Windows.Foundation.UniversalApiContract, 5.0)] interface Windows.Management.Deployment.IPackageManager6;
        [contract(Windows.Foundation.UniversalApiContract, 6.0)] interface Windows.Management.Deployment.IPackageManager7;
        [contract(Windows.Foundation.UniversalApiContract, 7.0)] interface Windows.Management.Deployment.IPackageManager8;
        [contract(Windows.Foundation.UniversalApiContract, 10.0)] interface Windows.Management.Deployment.IPackageManager9;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass PackageUserInformation
    {
        [default] interface Windows.Management.Deployment.IPackageUserInformation;
    }

}
