IMPORTED_NO_SYSTEM
------------------

.. versionadded:: 3.23

.. deprecated:: 3.25

  ``IMPORTED_NO_SYSTEM`` is deprecated. Please use the following alternatives
  instead:

  * Set :prop_tgt:`SYSTEM` to false if you don't want a target's include
    directories to be treated as system directories when compiling consumers.
  * Set :prop_tgt:`EXPORT_NO_SYSTEM` to true if you don't want the include
    directories of the imported target generated by :command:`install(EXPORT)`
    and :command:`export` commands to be treated as system directories when
    compiling consumers.

Setting ``IMPORTED_NO_SYSTEM`` to true on an
:ref:`imported target <Imported Targets>` specifies that it is not a
system target.  This has the following effects:

* Entries of :prop_tgt:`INTERFACE_INCLUDE_DIRECTORIES` are not treated
  as system include directories when compiling consumers (regardless of
  the value of the consumed target's :prop_tgt:`SYSTEM` property), as they
  would be by default.   Entries of
  :prop_tgt:`INTERFACE_SYSTEM_INCLUDE_DIRECTORIES` are not affected,
  and will always be treated as system include directories.
* On Apple platforms, when the target is a framework, it will not be treated as
  system.

This property can also be enabled on a non-imported target.  Doing so does
not affect the build system, but does tell the :command:`install(EXPORT)` and
:command:`export` commands to enable it on the imported targets they generate.

See the :prop_tgt:`NO_SYSTEM_FROM_IMPORTED` target property to set this
behavior on the target *consuming* the include directories rather than
the one *providing* them.
