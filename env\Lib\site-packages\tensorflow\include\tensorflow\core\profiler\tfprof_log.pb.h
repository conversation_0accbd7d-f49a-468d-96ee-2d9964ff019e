// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_log.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/step_stats.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
namespace tensorflow {
namespace tfprof {
class CodeDef;
struct CodeDefDefaultTypeInternal;
extern CodeDefDefaultTypeInternal _CodeDef_default_instance_;
class CodeDef_Trace;
struct CodeDef_TraceDefaultTypeInternal;
extern CodeDef_TraceDefaultTypeInternal _CodeDef_Trace_default_instance_;
class ExecMemory;
struct ExecMemoryDefaultTypeInternal;
extern ExecMemoryDefaultTypeInternal _ExecMemory_default_instance_;
class ExecMemory_OutputMemoryEntry_DoNotUse;
struct ExecMemory_OutputMemoryEntry_DoNotUseDefaultTypeInternal;
extern ExecMemory_OutputMemoryEntry_DoNotUseDefaultTypeInternal _ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_;
class ExecProfile;
struct ExecProfileDefaultTypeInternal;
extern ExecProfileDefaultTypeInternal _ExecProfile_default_instance_;
class ExecProfile_AcceleratorExecsEntry_DoNotUse;
struct ExecProfile_AcceleratorExecsEntry_DoNotUseDefaultTypeInternal;
extern ExecProfile_AcceleratorExecsEntry_DoNotUseDefaultTypeInternal _ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_;
class ExecProfile_CpuExecsEntry_DoNotUse;
struct ExecProfile_CpuExecsEntry_DoNotUseDefaultTypeInternal;
extern ExecProfile_CpuExecsEntry_DoNotUseDefaultTypeInternal _ExecProfile_CpuExecsEntry_DoNotUse_default_instance_;
class ExecTime;
struct ExecTimeDefaultTypeInternal;
extern ExecTimeDefaultTypeInternal _ExecTime_default_instance_;
class Memory;
struct MemoryDefaultTypeInternal;
extern MemoryDefaultTypeInternal _Memory_default_instance_;
class OpLogEntry;
struct OpLogEntryDefaultTypeInternal;
extern OpLogEntryDefaultTypeInternal _OpLogEntry_default_instance_;
class OpLogProto;
struct OpLogProtoDefaultTypeInternal;
extern OpLogProtoDefaultTypeInternal _OpLogProto_default_instance_;
class OpLogProto_IdToStringEntry_DoNotUse;
struct OpLogProto_IdToStringEntry_DoNotUseDefaultTypeInternal;
extern OpLogProto_IdToStringEntry_DoNotUseDefaultTypeInternal _OpLogProto_IdToStringEntry_DoNotUse_default_instance_;
class ProfileNode;
struct ProfileNodeDefaultTypeInternal;
extern ProfileNodeDefaultTypeInternal _ProfileNode_default_instance_;
class ProfileNode_AttrsEntry_DoNotUse;
struct ProfileNode_AttrsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_AttrsEntry_DoNotUseDefaultTypeInternal _ProfileNode_AttrsEntry_DoNotUse_default_instance_;
class ProfileNode_ExecsEntry_DoNotUse;
struct ProfileNode_ExecsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_ExecsEntry_DoNotUseDefaultTypeInternal _ProfileNode_ExecsEntry_DoNotUse_default_instance_;
class ProfileNode_InputShapesEntry_DoNotUse;
struct ProfileNode_InputShapesEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_InputShapesEntry_DoNotUseDefaultTypeInternal _ProfileNode_InputShapesEntry_DoNotUse_default_instance_;
class ProfileNode_InputsEntry_DoNotUse;
struct ProfileNode_InputsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_InputsEntry_DoNotUseDefaultTypeInternal _ProfileNode_InputsEntry_DoNotUse_default_instance_;
class ProfileNode_OutputShapesEntry_DoNotUse;
struct ProfileNode_OutputShapesEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_OutputShapesEntry_DoNotUseDefaultTypeInternal _ProfileNode_OutputShapesEntry_DoNotUse_default_instance_;
class ProfileNode_OutputsEntry_DoNotUse;
struct ProfileNode_OutputsEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_OutputsEntry_DoNotUseDefaultTypeInternal _ProfileNode_OutputsEntry_DoNotUse_default_instance_;
class ProfileNode_SrcOutputIndexEntry_DoNotUse;
struct ProfileNode_SrcOutputIndexEntry_DoNotUseDefaultTypeInternal;
extern ProfileNode_SrcOutputIndexEntry_DoNotUseDefaultTypeInternal _ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_;
class ProfileProto;
struct ProfileProtoDefaultTypeInternal;
extern ProfileProtoDefaultTypeInternal _ProfileProto_default_instance_;
class ProfileProto_IdToStringEntry_DoNotUse;
struct ProfileProto_IdToStringEntry_DoNotUseDefaultTypeInternal;
extern ProfileProto_IdToStringEntry_DoNotUseDefaultTypeInternal _ProfileProto_IdToStringEntry_DoNotUse_default_instance_;
class ProfileProto_NodesEntry_DoNotUse;
struct ProfileProto_NodesEntry_DoNotUseDefaultTypeInternal;
extern ProfileProto_NodesEntry_DoNotUseDefaultTypeInternal _ProfileProto_NodesEntry_DoNotUse_default_instance_;
class Tuple;
struct TupleDefaultTypeInternal;
extern TupleDefaultTypeInternal _Tuple_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tfprof::CodeDef* Arena::CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(Arena*);
template<> ::tensorflow::tfprof::CodeDef_Trace* Arena::CreateMaybeMessage<::tensorflow::tfprof::CodeDef_Trace>(Arena*);
template<> ::tensorflow::tfprof::ExecMemory* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecMemory>(Arena*);
template<> ::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecMemory_OutputMemoryEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile_AcceleratorExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecProfile_CpuExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ExecTime* Arena::CreateMaybeMessage<::tensorflow::tfprof::ExecTime>(Arena*);
template<> ::tensorflow::tfprof::Memory* Arena::CreateMaybeMessage<::tensorflow::tfprof::Memory>(Arena*);
template<> ::tensorflow::tfprof::OpLogEntry* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogEntry>(Arena*);
template<> ::tensorflow::tfprof::OpLogProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogProto>(Arena*);
template<> ::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::OpLogProto_IdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_AttrsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_ExecsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_InputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_InputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_OutputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_OutputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileNode_SrcOutputIndexEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto_IdToStringEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::ProfileProto_NodesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::Tuple* Arena::CreateMaybeMessage<::tensorflow::tfprof::Tuple>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tfprof {

// ===================================================================

class CodeDef_Trace final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.CodeDef.Trace) */ {
 public:
  inline CodeDef_Trace() : CodeDef_Trace(nullptr) {}
  ~CodeDef_Trace() override;
  explicit PROTOBUF_CONSTEXPR CodeDef_Trace(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CodeDef_Trace(const CodeDef_Trace& from);
  CodeDef_Trace(CodeDef_Trace&& from) noexcept
    : CodeDef_Trace() {
    *this = ::std::move(from);
  }

  inline CodeDef_Trace& operator=(const CodeDef_Trace& from) {
    CopyFrom(from);
    return *this;
  }
  inline CodeDef_Trace& operator=(CodeDef_Trace&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CodeDef_Trace& default_instance() {
    return *internal_default_instance();
  }
  static inline const CodeDef_Trace* internal_default_instance() {
    return reinterpret_cast<const CodeDef_Trace*>(
               &_CodeDef_Trace_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CodeDef_Trace& a, CodeDef_Trace& b) {
    a.Swap(&b);
  }
  inline void Swap(CodeDef_Trace* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CodeDef_Trace* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CodeDef_Trace* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CodeDef_Trace>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CodeDef_Trace& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CodeDef_Trace& from) {
    CodeDef_Trace::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CodeDef_Trace* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.CodeDef.Trace";
  }
  protected:
  explicit CodeDef_Trace(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFileFieldNumber = 1,
    kFunctionFieldNumber = 3,
    kLineFieldNumber = 4,
    kLinenoFieldNumber = 2,
    kFuncStartLineFieldNumber = 5,
    kFileIdFieldNumber = 6,
    kFunctionIdFieldNumber = 7,
    kLineIdFieldNumber = 8,
  };
  // string file = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_file();
  PROTOBUF_DEPRECATED const std::string& file() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_file(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_file();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_file();
  PROTOBUF_DEPRECATED void set_allocated_file(std::string* file);
  private:
  const std::string& _internal_file() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file(const std::string& value);
  std::string* _internal_mutable_file();
  public:

  // string function = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_function();
  PROTOBUF_DEPRECATED const std::string& function() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_function(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_function();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_function();
  PROTOBUF_DEPRECATED void set_allocated_function(std::string* function);
  private:
  const std::string& _internal_function() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_function(const std::string& value);
  std::string* _internal_mutable_function();
  public:

  // string line = 4 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_line();
  PROTOBUF_DEPRECATED const std::string& line() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_line(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_line();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_line();
  PROTOBUF_DEPRECATED void set_allocated_line(std::string* line);
  private:
  const std::string& _internal_line() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_line(const std::string& value);
  std::string* _internal_mutable_line();
  public:

  // int32 lineno = 2;
  void clear_lineno();
  int32_t lineno() const;
  void set_lineno(int32_t value);
  private:
  int32_t _internal_lineno() const;
  void _internal_set_lineno(int32_t value);
  public:

  // int32 func_start_line = 5;
  void clear_func_start_line();
  int32_t func_start_line() const;
  void set_func_start_line(int32_t value);
  private:
  int32_t _internal_func_start_line() const;
  void _internal_set_func_start_line(int32_t value);
  public:

  // int64 file_id = 6;
  void clear_file_id();
  int64_t file_id() const;
  void set_file_id(int64_t value);
  private:
  int64_t _internal_file_id() const;
  void _internal_set_file_id(int64_t value);
  public:

  // int64 function_id = 7;
  void clear_function_id();
  int64_t function_id() const;
  void set_function_id(int64_t value);
  private:
  int64_t _internal_function_id() const;
  void _internal_set_function_id(int64_t value);
  public:

  // int64 line_id = 8;
  void clear_line_id();
  int64_t line_id() const;
  void set_line_id(int64_t value);
  private:
  int64_t _internal_line_id() const;
  void _internal_set_line_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.CodeDef.Trace)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr function_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr line_;
    int32_t lineno_;
    int32_t func_start_line_;
    int64_t file_id_;
    int64_t function_id_;
    int64_t line_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class CodeDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.CodeDef) */ {
 public:
  inline CodeDef() : CodeDef(nullptr) {}
  ~CodeDef() override;
  explicit PROTOBUF_CONSTEXPR CodeDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CodeDef(const CodeDef& from);
  CodeDef(CodeDef&& from) noexcept
    : CodeDef() {
    *this = ::std::move(from);
  }

  inline CodeDef& operator=(const CodeDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline CodeDef& operator=(CodeDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CodeDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const CodeDef* internal_default_instance() {
    return reinterpret_cast<const CodeDef*>(
               &_CodeDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CodeDef& a, CodeDef& b) {
    a.Swap(&b);
  }
  inline void Swap(CodeDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CodeDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CodeDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CodeDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CodeDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CodeDef& from) {
    CodeDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CodeDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.CodeDef";
  }
  protected:
  explicit CodeDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CodeDef_Trace Trace;

  // accessors -------------------------------------------------------

  enum : int {
    kTracesFieldNumber = 1,
  };
  // repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
  int traces_size() const;
  private:
  int _internal_traces_size() const;
  public:
  void clear_traces();
  ::tensorflow::tfprof::CodeDef_Trace* mutable_traces(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >*
      mutable_traces();
  private:
  const ::tensorflow::tfprof::CodeDef_Trace& _internal_traces(int index) const;
  ::tensorflow::tfprof::CodeDef_Trace* _internal_add_traces();
  public:
  const ::tensorflow::tfprof::CodeDef_Trace& traces(int index) const;
  ::tensorflow::tfprof::CodeDef_Trace* add_traces();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >&
      traces() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.CodeDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace > traces_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class OpLogEntry final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OpLogEntry) */ {
 public:
  inline OpLogEntry() : OpLogEntry(nullptr) {}
  ~OpLogEntry() override;
  explicit PROTOBUF_CONSTEXPR OpLogEntry(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpLogEntry(const OpLogEntry& from);
  OpLogEntry(OpLogEntry&& from) noexcept
    : OpLogEntry() {
    *this = ::std::move(from);
  }

  inline OpLogEntry& operator=(const OpLogEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpLogEntry& operator=(OpLogEntry&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpLogEntry& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpLogEntry* internal_default_instance() {
    return reinterpret_cast<const OpLogEntry*>(
               &_OpLogEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OpLogEntry& a, OpLogEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(OpLogEntry* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpLogEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpLogEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpLogEntry>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpLogEntry& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpLogEntry& from) {
    OpLogEntry::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpLogEntry* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.OpLogEntry";
  }
  protected:
  explicit OpLogEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypesFieldNumber = 3,
    kNameFieldNumber = 1,
    kCodeDefFieldNumber = 4,
    kFloatOpsFieldNumber = 2,
  };
  // repeated string types = 3;
  int types_size() const;
  private:
  int _internal_types_size() const;
  public:
  void clear_types();
  const std::string& types(int index) const;
  std::string* mutable_types(int index);
  void set_types(int index, const std::string& value);
  void set_types(int index, std::string&& value);
  void set_types(int index, const char* value);
  void set_types(int index, const char* value, size_t size);
  std::string* add_types();
  void add_types(const std::string& value);
  void add_types(std::string&& value);
  void add_types(const char* value);
  void add_types(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& types() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_types();
  private:
  const std::string& _internal_types(int index) const;
  std::string* _internal_add_types();
  public:

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.tfprof.CodeDef code_def = 4;
  bool has_code_def() const;
  private:
  bool _internal_has_code_def() const;
  public:
  void clear_code_def();
  const ::tensorflow::tfprof::CodeDef& code_def() const;
  PROTOBUF_NODISCARD ::tensorflow::tfprof::CodeDef* release_code_def();
  ::tensorflow::tfprof::CodeDef* mutable_code_def();
  void set_allocated_code_def(::tensorflow::tfprof::CodeDef* code_def);
  private:
  const ::tensorflow::tfprof::CodeDef& _internal_code_def() const;
  ::tensorflow::tfprof::CodeDef* _internal_mutable_code_def();
  public:
  void unsafe_arena_set_allocated_code_def(
      ::tensorflow::tfprof::CodeDef* code_def);
  ::tensorflow::tfprof::CodeDef* unsafe_arena_release_code_def();

  // int64 float_ops = 2;
  void clear_float_ops();
  int64_t float_ops() const;
  void set_float_ops(int64_t value);
  private:
  int64_t _internal_float_ops() const;
  void _internal_set_float_ops(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogEntry)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> types_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::tfprof::CodeDef* code_def_;
    int64_t float_ops_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class OpLogProto_IdToStringEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<OpLogProto_IdToStringEntry_DoNotUse, 
    int64_t, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<OpLogProto_IdToStringEntry_DoNotUse, 
    int64_t, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  OpLogProto_IdToStringEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR OpLogProto_IdToStringEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit OpLogProto_IdToStringEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const OpLogProto_IdToStringEntry_DoNotUse& other);
  static const OpLogProto_IdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const OpLogProto_IdToStringEntry_DoNotUse*>(&_OpLogProto_IdToStringEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.OpLogProto.IdToStringEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class OpLogProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.OpLogProto) */ {
 public:
  inline OpLogProto() : OpLogProto(nullptr) {}
  ~OpLogProto() override;
  explicit PROTOBUF_CONSTEXPR OpLogProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpLogProto(const OpLogProto& from);
  OpLogProto(OpLogProto&& from) noexcept
    : OpLogProto() {
    *this = ::std::move(from);
  }

  inline OpLogProto& operator=(const OpLogProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpLogProto& operator=(OpLogProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpLogProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpLogProto* internal_default_instance() {
    return reinterpret_cast<const OpLogProto*>(
               &_OpLogProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(OpLogProto& a, OpLogProto& b) {
    a.Swap(&b);
  }
  inline void Swap(OpLogProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpLogProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpLogProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpLogProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpLogProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpLogProto& from) {
    OpLogProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpLogProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.OpLogProto";
  }
  protected:
  explicit OpLogProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kLogEntriesFieldNumber = 1,
    kIdToStringFieldNumber = 2,
  };
  // repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
  int log_entries_size() const;
  private:
  int _internal_log_entries_size() const;
  public:
  void clear_log_entries();
  ::tensorflow::tfprof::OpLogEntry* mutable_log_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >*
      mutable_log_entries();
  private:
  const ::tensorflow::tfprof::OpLogEntry& _internal_log_entries(int index) const;
  ::tensorflow::tfprof::OpLogEntry* _internal_add_log_entries();
  public:
  const ::tensorflow::tfprof::OpLogEntry& log_entries(int index) const;
  ::tensorflow::tfprof::OpLogEntry* add_log_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >&
      log_entries() const;

  // map<int64, string> id_to_string = 2;
  int id_to_string_size() const;
  private:
  int _internal_id_to_string_size() const;
  public:
  void clear_id_to_string();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
      _internal_id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
      _internal_mutable_id_to_string();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
      id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
      mutable_id_to_string();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.OpLogProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry > log_entries_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        OpLogProto_IdToStringEntry_DoNotUse,
        int64_t, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> id_to_string_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ProfileProto_NodesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileProto_NodesEntry_DoNotUse, 
    int64_t, ::tensorflow::tfprof::ProfileNode,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileProto_NodesEntry_DoNotUse, 
    int64_t, ::tensorflow::tfprof::ProfileNode,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ProfileProto_NodesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileProto_NodesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileProto_NodesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileProto_NodesEntry_DoNotUse& other);
  static const ProfileProto_NodesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileProto_NodesEntry_DoNotUse*>(&_ProfileProto_NodesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileProto_IdToStringEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileProto_IdToStringEntry_DoNotUse, 
    int64_t, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileProto_IdToStringEntry_DoNotUse, 
    int64_t, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  ProfileProto_IdToStringEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileProto_IdToStringEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileProto_IdToStringEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileProto_IdToStringEntry_DoNotUse& other);
  static const ProfileProto_IdToStringEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileProto_IdToStringEntry_DoNotUse*>(&_ProfileProto_IdToStringEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.ProfileProto.IdToStringEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ProfileProto) */ {
 public:
  inline ProfileProto() : ProfileProto(nullptr) {}
  ~ProfileProto() override;
  explicit PROTOBUF_CONSTEXPR ProfileProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileProto(const ProfileProto& from);
  ProfileProto(ProfileProto&& from) noexcept
    : ProfileProto() {
    *this = ::std::move(from);
  }

  inline ProfileProto& operator=(const ProfileProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileProto& operator=(ProfileProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileProto* internal_default_instance() {
    return reinterpret_cast<const ProfileProto*>(
               &_ProfileProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ProfileProto& a, ProfileProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileProto& from) {
    ProfileProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ProfileProto";
  }
  protected:
  explicit ProfileProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 1,
    kStepsFieldNumber = 3,
    kIdToStringFieldNumber = 4,
    kHasTraceFieldNumber = 2,
    kMissAcceleratorStreamFieldNumber = 5,
  };
  // map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
  int nodes_size() const;
  private:
  int _internal_nodes_size() const;
  public:
  void clear_nodes();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ProfileNode >&
      _internal_nodes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ProfileNode >*
      _internal_mutable_nodes();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ProfileNode >&
      nodes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ProfileNode >*
      mutable_nodes();

  // repeated int64 steps = 3;
  int steps_size() const;
  private:
  int _internal_steps_size() const;
  public:
  void clear_steps();
  private:
  int64_t _internal_steps(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_steps() const;
  void _internal_add_steps(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_steps();
  public:
  int64_t steps(int index) const;
  void set_steps(int index, int64_t value);
  void add_steps(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      steps() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_steps();

  // map<int64, string> id_to_string = 4;
  int id_to_string_size() const;
  private:
  int _internal_id_to_string_size() const;
  public:
  void clear_id_to_string();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
      _internal_id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
      _internal_mutable_id_to_string();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
      id_to_string() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
      mutable_id_to_string();

  // bool has_trace = 2;
  void clear_has_trace();
  bool has_trace() const;
  void set_has_trace(bool value);
  private:
  bool _internal_has_trace() const;
  void _internal_set_has_trace(bool value);
  public:

  // bool miss_accelerator_stream = 5;
  void clear_miss_accelerator_stream();
  bool miss_accelerator_stream() const;
  void set_miss_accelerator_stream(bool value);
  private:
  bool _internal_miss_accelerator_stream() const;
  void _internal_set_miss_accelerator_stream(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileProto_NodesEntry_DoNotUse,
        int64_t, ::tensorflow::tfprof::ProfileNode,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> nodes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > steps_;
    mutable std::atomic<int> _steps_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileProto_IdToStringEntry_DoNotUse,
        int64_t, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> id_to_string_;
    bool has_trace_;
    bool miss_accelerator_stream_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ProfileNode_InputsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_InputsEntry_DoNotUse, 
    int32_t, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_InputsEntry_DoNotUse, 
    int32_t, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> SuperType;
  ProfileNode_InputsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileNode_InputsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileNode_InputsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_InputsEntry_DoNotUse& other);
  static const ProfileNode_InputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_InputsEntry_DoNotUse*>(&_ProfileNode_InputsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileNode_InputShapesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_InputShapesEntry_DoNotUse, 
    int32_t, ::tensorflow::tfprof::Tuple,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_InputShapesEntry_DoNotUse, 
    int32_t, ::tensorflow::tfprof::Tuple,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ProfileNode_InputShapesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileNode_InputShapesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileNode_InputShapesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_InputShapesEntry_DoNotUse& other);
  static const ProfileNode_InputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_InputShapesEntry_DoNotUse*>(&_ProfileNode_InputShapesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileNode_OutputsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_OutputsEntry_DoNotUse, 
    int32_t, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_OutputsEntry_DoNotUse, 
    int32_t, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> SuperType;
  ProfileNode_OutputsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileNode_OutputsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileNode_OutputsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_OutputsEntry_DoNotUse& other);
  static const ProfileNode_OutputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_OutputsEntry_DoNotUse*>(&_ProfileNode_OutputsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileNode_OutputShapesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_OutputShapesEntry_DoNotUse, 
    int32_t, ::tensorflow::tfprof::Tuple,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_OutputShapesEntry_DoNotUse, 
    int32_t, ::tensorflow::tfprof::Tuple,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ProfileNode_OutputShapesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileNode_OutputShapesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileNode_OutputShapesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_OutputShapesEntry_DoNotUse& other);
  static const ProfileNode_OutputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_OutputShapesEntry_DoNotUse*>(&_ProfileNode_OutputShapesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileNode_SrcOutputIndexEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_SrcOutputIndexEntry_DoNotUse, 
    int64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_SrcOutputIndexEntry_DoNotUse, 
    int64_t, int32_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> SuperType;
  ProfileNode_SrcOutputIndexEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileNode_SrcOutputIndexEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileNode_SrcOutputIndexEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_SrcOutputIndexEntry_DoNotUse& other);
  static const ProfileNode_SrcOutputIndexEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_SrcOutputIndexEntry_DoNotUse*>(&_ProfileNode_SrcOutputIndexEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileNode_AttrsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_AttrsEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_AttrsEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ProfileNode_AttrsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileNode_AttrsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileNode_AttrsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_AttrsEntry_DoNotUse& other);
  static const ProfileNode_AttrsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_AttrsEntry_DoNotUse*>(&_ProfileNode_AttrsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.ProfileNode.AttrsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileNode_ExecsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_ExecsEntry_DoNotUse, 
    int64_t, ::tensorflow::tfprof::ExecProfile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileNode_ExecsEntry_DoNotUse, 
    int64_t, ::tensorflow::tfprof::ExecProfile,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ProfileNode_ExecsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileNode_ExecsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileNode_ExecsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileNode_ExecsEntry_DoNotUse& other);
  static const ProfileNode_ExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileNode_ExecsEntry_DoNotUse*>(&_ProfileNode_ExecsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ProfileNode final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ProfileNode) */ {
 public:
  inline ProfileNode() : ProfileNode(nullptr) {}
  ~ProfileNode() override;
  explicit PROTOBUF_CONSTEXPR ProfileNode(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileNode(const ProfileNode& from);
  ProfileNode(ProfileNode&& from) noexcept
    : ProfileNode() {
    *this = ::std::move(from);
  }

  inline ProfileNode& operator=(const ProfileNode& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileNode& operator=(ProfileNode&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileNode& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileNode* internal_default_instance() {
    return reinterpret_cast<const ProfileNode*>(
               &_ProfileNode_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(ProfileNode& a, ProfileNode& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileNode* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileNode* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileNode* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileNode>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileNode& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileNode& from) {
    ProfileNode::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileNode* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ProfileNode";
  }
  protected:
  explicit ProfileNode(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kInputsFieldNumber = 2,
    kOutputsFieldNumber = 3,
    kShapeFieldNumber = 4,
    kOpTypesFieldNumber = 5,
    kAttrsFieldNumber = 11,
    kExecsFieldNumber = 12,
    kSrcOutputIndexFieldNumber = 14,
    kOutputShapesFieldNumber = 15,
    kInputShapesFieldNumber = 16,
    kNameFieldNumber = 1,
    kCanonicalDeviceFieldNumber = 6,
    kHostDeviceFieldNumber = 7,
    kOpFieldNumber = 9,
    kTraceFieldNumber = 10,
    kFloatOpsFieldNumber = 8,
    kIdFieldNumber = 13,
  };
  // map<int32, int64> inputs = 2;
  int inputs_size() const;
  private:
  int _internal_inputs_size() const;
  public:
  void clear_inputs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >&
      _internal_inputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >*
      _internal_mutable_inputs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >&
      inputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >*
      mutable_inputs();

  // map<int32, int64> outputs = 3;
  int outputs_size() const;
  private:
  int _internal_outputs_size() const;
  public:
  void clear_outputs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >&
      _internal_outputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >*
      _internal_mutable_outputs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >&
      outputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >*
      mutable_outputs();

  // repeated int64 shape = 4;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  private:
  int64_t _internal_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_shape() const;
  void _internal_add_shape(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_shape();
  public:
  int64_t shape(int index) const;
  void set_shape(int index, int64_t value);
  void add_shape(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_shape();

  // repeated string op_types = 5;
  int op_types_size() const;
  private:
  int _internal_op_types_size() const;
  public:
  void clear_op_types();
  const std::string& op_types(int index) const;
  std::string* mutable_op_types(int index);
  void set_op_types(int index, const std::string& value);
  void set_op_types(int index, std::string&& value);
  void set_op_types(int index, const char* value);
  void set_op_types(int index, const char* value, size_t size);
  std::string* add_op_types();
  void add_op_types(const std::string& value);
  void add_op_types(std::string&& value);
  void add_op_types(const char* value);
  void add_op_types(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& op_types() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_op_types();
  private:
  const std::string& _internal_op_types(int index) const;
  std::string* _internal_add_op_types();
  public:

  // map<string, .tensorflow.AttrValue> attrs = 11;
  int attrs_size() const;
  private:
  int _internal_attrs_size() const;
  public:
  void clear_attrs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      _internal_attrs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      _internal_mutable_attrs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attrs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attrs();

  // map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
  int execs_size() const;
  private:
  int _internal_execs_size() const;
  public:
  void clear_execs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ExecProfile >&
      _internal_execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ExecProfile >*
      _internal_mutable_execs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ExecProfile >&
      execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ExecProfile >*
      mutable_execs();

  // map<int64, int32> src_output_index = 14;
  int src_output_index_size() const;
  private:
  int _internal_src_output_index_size() const;
  public:
  void clear_src_output_index();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
      _internal_src_output_index() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
      _internal_mutable_src_output_index();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
      src_output_index() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
      mutable_src_output_index();

  // map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
  int output_shapes_size() const;
  private:
  int _internal_output_shapes_size() const;
  public:
  void clear_output_shapes();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >&
      _internal_output_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >*
      _internal_mutable_output_shapes();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >&
      output_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >*
      mutable_output_shapes();

  // map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
  int input_shapes_size() const;
  private:
  int _internal_input_shapes_size() const;
  public:
  void clear_input_shapes();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >&
      _internal_input_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >*
      _internal_mutable_input_shapes();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >&
      input_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >*
      mutable_input_shapes();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string canonical_device = 6;
  void clear_canonical_device();
  const std::string& canonical_device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_canonical_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_canonical_device();
  PROTOBUF_NODISCARD std::string* release_canonical_device();
  void set_allocated_canonical_device(std::string* canonical_device);
  private:
  const std::string& _internal_canonical_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_canonical_device(const std::string& value);
  std::string* _internal_mutable_canonical_device();
  public:

  // string host_device = 7;
  void clear_host_device();
  const std::string& host_device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_host_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_host_device();
  PROTOBUF_NODISCARD std::string* release_host_device();
  void set_allocated_host_device(std::string* host_device);
  private:
  const std::string& _internal_host_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_host_device(const std::string& value);
  std::string* _internal_mutable_host_device();
  public:

  // string op = 9;
  void clear_op();
  const std::string& op() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op();
  PROTOBUF_NODISCARD std::string* release_op();
  void set_allocated_op(std::string* op);
  private:
  const std::string& _internal_op() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op(const std::string& value);
  std::string* _internal_mutable_op();
  public:

  // .tensorflow.tfprof.CodeDef trace = 10;
  bool has_trace() const;
  private:
  bool _internal_has_trace() const;
  public:
  void clear_trace();
  const ::tensorflow::tfprof::CodeDef& trace() const;
  PROTOBUF_NODISCARD ::tensorflow::tfprof::CodeDef* release_trace();
  ::tensorflow::tfprof::CodeDef* mutable_trace();
  void set_allocated_trace(::tensorflow::tfprof::CodeDef* trace);
  private:
  const ::tensorflow::tfprof::CodeDef& _internal_trace() const;
  ::tensorflow::tfprof::CodeDef* _internal_mutable_trace();
  public:
  void unsafe_arena_set_allocated_trace(
      ::tensorflow::tfprof::CodeDef* trace);
  ::tensorflow::tfprof::CodeDef* unsafe_arena_release_trace();

  // int64 float_ops = 8;
  void clear_float_ops();
  int64_t float_ops() const;
  void set_float_ops(int64_t value);
  private:
  int64_t _internal_float_ops() const;
  void _internal_set_float_ops(int64_t value);
  public:

  // int64 id = 13;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ProfileNode)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileNode_InputsEntry_DoNotUse,
        int32_t, int64_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> inputs_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileNode_OutputsEntry_DoNotUse,
        int32_t, int64_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> outputs_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > shape_;
    mutable std::atomic<int> _shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> op_types_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileNode_AttrsEntry_DoNotUse,
        std::string, ::tensorflow::AttrValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attrs_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileNode_ExecsEntry_DoNotUse,
        int64_t, ::tensorflow::tfprof::ExecProfile,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> execs_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileNode_SrcOutputIndexEntry_DoNotUse,
        int64_t, int32_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32> src_output_index_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileNode_OutputShapesEntry_DoNotUse,
        int32_t, ::tensorflow::tfprof::Tuple,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> output_shapes_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileNode_InputShapesEntry_DoNotUse,
        int32_t, ::tensorflow::tfprof::Tuple,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> input_shapes_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr canonical_device_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_device_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
    ::tensorflow::tfprof::CodeDef* trace_;
    int64_t float_ops_;
    int64_t id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ExecProfile_AcceleratorExecsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecProfile_AcceleratorExecsEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::ExecTime,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecProfile_AcceleratorExecsEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::ExecTime,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ExecProfile_AcceleratorExecsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ExecProfile_AcceleratorExecsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ExecProfile_AcceleratorExecsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExecProfile_AcceleratorExecsEntry_DoNotUse& other);
  static const ExecProfile_AcceleratorExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecProfile_AcceleratorExecsEntry_DoNotUse*>(&_ExecProfile_AcceleratorExecsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.ExecProfile.AcceleratorExecsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ExecProfile_CpuExecsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecProfile_CpuExecsEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::ExecTime,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecProfile_CpuExecsEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::ExecTime,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ExecProfile_CpuExecsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ExecProfile_CpuExecsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ExecProfile_CpuExecsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExecProfile_CpuExecsEntry_DoNotUse& other);
  static const ExecProfile_CpuExecsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecProfile_CpuExecsEntry_DoNotUse*>(&_ExecProfile_CpuExecsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.ExecProfile.CpuExecsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ExecProfile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecProfile) */ {
 public:
  inline ExecProfile() : ExecProfile(nullptr) {}
  ~ExecProfile() override;
  explicit PROTOBUF_CONSTEXPR ExecProfile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExecProfile(const ExecProfile& from);
  ExecProfile(ExecProfile&& from) noexcept
    : ExecProfile() {
    *this = ::std::move(from);
  }

  inline ExecProfile& operator=(const ExecProfile& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecProfile& operator=(ExecProfile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExecProfile& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExecProfile* internal_default_instance() {
    return reinterpret_cast<const ExecProfile*>(
               &_ExecProfile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(ExecProfile& a, ExecProfile& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecProfile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecProfile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExecProfile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExecProfile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExecProfile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExecProfile& from) {
    ExecProfile::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecProfile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ExecProfile";
  }
  protected:
  explicit ExecProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kAcceleratorExecsFieldNumber = 4,
    kCpuExecsFieldNumber = 5,
    kDevicesFieldNumber = 6,
    kMemoryExecsFieldNumber = 7,
    kAllocationsFieldNumber = 11,
    kRunCountFieldNumber = 1,
    kAllStartMicrosFieldNumber = 2,
    kLatestEndMicrosFieldNumber = 3,
  };
  // map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
  int accelerator_execs_size() const;
  private:
  int _internal_accelerator_execs_size() const;
  public:
  void clear_accelerator_execs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
      _internal_accelerator_execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
      _internal_mutable_accelerator_execs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
      accelerator_execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
      mutable_accelerator_execs();

  // map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
  int cpu_execs_size() const;
  private:
  int _internal_cpu_execs_size() const;
  public:
  void clear_cpu_execs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
      _internal_cpu_execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
      _internal_mutable_cpu_execs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
      cpu_execs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
      mutable_cpu_execs();

  // repeated string devices = 6;
  int devices_size() const;
  private:
  int _internal_devices_size() const;
  public:
  void clear_devices();
  const std::string& devices(int index) const;
  std::string* mutable_devices(int index);
  void set_devices(int index, const std::string& value);
  void set_devices(int index, std::string&& value);
  void set_devices(int index, const char* value);
  void set_devices(int index, const char* value, size_t size);
  std::string* add_devices();
  void add_devices(const std::string& value);
  void add_devices(std::string&& value);
  void add_devices(const char* value);
  void add_devices(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& devices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_devices();
  private:
  const std::string& _internal_devices(int index) const;
  std::string* _internal_add_devices();
  public:

  // repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
  int memory_execs_size() const;
  private:
  int _internal_memory_execs_size() const;
  public:
  void clear_memory_execs();
  ::tensorflow::tfprof::ExecMemory* mutable_memory_execs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >*
      mutable_memory_execs();
  private:
  const ::tensorflow::tfprof::ExecMemory& _internal_memory_execs(int index) const;
  ::tensorflow::tfprof::ExecMemory* _internal_add_memory_execs();
  public:
  const ::tensorflow::tfprof::ExecMemory& memory_execs(int index) const;
  ::tensorflow::tfprof::ExecMemory* add_memory_execs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >&
      memory_execs() const;

  // repeated .tensorflow.AllocationRecord allocations = 11;
  int allocations_size() const;
  private:
  int _internal_allocations_size() const;
  public:
  void clear_allocations();
  ::tensorflow::AllocationRecord* mutable_allocations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >*
      mutable_allocations();
  private:
  const ::tensorflow::AllocationRecord& _internal_allocations(int index) const;
  ::tensorflow::AllocationRecord* _internal_add_allocations();
  public:
  const ::tensorflow::AllocationRecord& allocations(int index) const;
  ::tensorflow::AllocationRecord* add_allocations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >&
      allocations() const;

  // int64 run_count = 1;
  void clear_run_count();
  int64_t run_count() const;
  void set_run_count(int64_t value);
  private:
  int64_t _internal_run_count() const;
  void _internal_set_run_count(int64_t value);
  public:

  // int64 all_start_micros = 2;
  void clear_all_start_micros();
  int64_t all_start_micros() const;
  void set_all_start_micros(int64_t value);
  private:
  int64_t _internal_all_start_micros() const;
  void _internal_set_all_start_micros(int64_t value);
  public:

  // int64 latest_end_micros = 3;
  void clear_latest_end_micros();
  int64_t latest_end_micros() const;
  void set_latest_end_micros(int64_t value);
  private:
  int64_t _internal_latest_end_micros() const;
  void _internal_set_latest_end_micros(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecProfile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ExecProfile_AcceleratorExecsEntry_DoNotUse,
        std::string, ::tensorflow::tfprof::ExecTime,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> accelerator_execs_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ExecProfile_CpuExecsEntry_DoNotUse,
        std::string, ::tensorflow::tfprof::ExecTime,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> cpu_execs_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> devices_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory > memory_execs_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord > allocations_;
    int64_t run_count_;
    int64_t all_start_micros_;
    int64_t latest_end_micros_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ExecTime final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecTime) */ {
 public:
  inline ExecTime() : ExecTime(nullptr) {}
  ~ExecTime() override;
  explicit PROTOBUF_CONSTEXPR ExecTime(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExecTime(const ExecTime& from);
  ExecTime(ExecTime&& from) noexcept
    : ExecTime() {
    *this = ::std::move(from);
  }

  inline ExecTime& operator=(const ExecTime& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecTime& operator=(ExecTime&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExecTime& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExecTime* internal_default_instance() {
    return reinterpret_cast<const ExecTime*>(
               &_ExecTime_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(ExecTime& a, ExecTime& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecTime* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecTime* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExecTime* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExecTime>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExecTime& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExecTime& from) {
    ExecTime::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecTime* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ExecTime";
  }
  protected:
  explicit ExecTime(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimesFieldNumber = 1,
  };
  // repeated .tensorflow.tfprof.Tuple times = 1;
  int times_size() const;
  private:
  int _internal_times_size() const;
  public:
  void clear_times();
  ::tensorflow::tfprof::Tuple* mutable_times(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple >*
      mutable_times();
  private:
  const ::tensorflow::tfprof::Tuple& _internal_times(int index) const;
  ::tensorflow::tfprof::Tuple* _internal_add_times();
  public:
  const ::tensorflow::tfprof::Tuple& times(int index) const;
  ::tensorflow::tfprof::Tuple* add_times();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple >&
      times() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecTime)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple > times_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class ExecMemory_OutputMemoryEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecMemory_OutputMemoryEntry_DoNotUse, 
    int32_t, ::tensorflow::tfprof::Memory,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExecMemory_OutputMemoryEntry_DoNotUse, 
    int32_t, ::tensorflow::tfprof::Memory,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ExecMemory_OutputMemoryEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ExecMemory_OutputMemoryEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ExecMemory_OutputMemoryEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExecMemory_OutputMemoryEntry_DoNotUse& other);
  static const ExecMemory_OutputMemoryEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExecMemory_OutputMemoryEntry_DoNotUse*>(&_ExecMemory_OutputMemoryEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};

// -------------------------------------------------------------------

class ExecMemory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.ExecMemory) */ {
 public:
  inline ExecMemory() : ExecMemory(nullptr) {}
  ~ExecMemory() override;
  explicit PROTOBUF_CONSTEXPR ExecMemory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExecMemory(const ExecMemory& from);
  ExecMemory(ExecMemory&& from) noexcept
    : ExecMemory() {
    *this = ::std::move(from);
  }

  inline ExecMemory& operator=(const ExecMemory& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecMemory& operator=(ExecMemory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExecMemory& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExecMemory* internal_default_instance() {
    return reinterpret_cast<const ExecMemory*>(
               &_ExecMemory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(ExecMemory& a, ExecMemory& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecMemory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecMemory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExecMemory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExecMemory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExecMemory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExecMemory& from) {
    ExecMemory::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecMemory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.ExecMemory";
  }
  protected:
  explicit ExecMemory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kOutputMemoryFieldNumber = 11,
    kMemoryMicrosFieldNumber = 1,
    kHostTempBytesFieldNumber = 2,
    kHostPersistentBytesFieldNumber = 3,
    kAcceleratorTempBytesFieldNumber = 4,
    kAcceleratorPersistentBytesFieldNumber = 5,
    kRequestedBytesFieldNumber = 6,
    kPeakBytesFieldNumber = 7,
    kResidualBytesFieldNumber = 8,
    kOutputBytesFieldNumber = 9,
    kAllocatorBytesInUseFieldNumber = 10,
  };
  // map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
  int output_memory_size() const;
  private:
  int _internal_output_memory_size() const;
  public:
  void clear_output_memory();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Memory >&
      _internal_output_memory() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Memory >*
      _internal_mutable_output_memory();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Memory >&
      output_memory() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Memory >*
      mutable_output_memory();

  // int64 memory_micros = 1;
  void clear_memory_micros();
  int64_t memory_micros() const;
  void set_memory_micros(int64_t value);
  private:
  int64_t _internal_memory_micros() const;
  void _internal_set_memory_micros(int64_t value);
  public:

  // int64 host_temp_bytes = 2;
  void clear_host_temp_bytes();
  int64_t host_temp_bytes() const;
  void set_host_temp_bytes(int64_t value);
  private:
  int64_t _internal_host_temp_bytes() const;
  void _internal_set_host_temp_bytes(int64_t value);
  public:

  // int64 host_persistent_bytes = 3;
  void clear_host_persistent_bytes();
  int64_t host_persistent_bytes() const;
  void set_host_persistent_bytes(int64_t value);
  private:
  int64_t _internal_host_persistent_bytes() const;
  void _internal_set_host_persistent_bytes(int64_t value);
  public:

  // int64 accelerator_temp_bytes = 4;
  void clear_accelerator_temp_bytes();
  int64_t accelerator_temp_bytes() const;
  void set_accelerator_temp_bytes(int64_t value);
  private:
  int64_t _internal_accelerator_temp_bytes() const;
  void _internal_set_accelerator_temp_bytes(int64_t value);
  public:

  // int64 accelerator_persistent_bytes = 5;
  void clear_accelerator_persistent_bytes();
  int64_t accelerator_persistent_bytes() const;
  void set_accelerator_persistent_bytes(int64_t value);
  private:
  int64_t _internal_accelerator_persistent_bytes() const;
  void _internal_set_accelerator_persistent_bytes(int64_t value);
  public:

  // int64 requested_bytes = 6;
  void clear_requested_bytes();
  int64_t requested_bytes() const;
  void set_requested_bytes(int64_t value);
  private:
  int64_t _internal_requested_bytes() const;
  void _internal_set_requested_bytes(int64_t value);
  public:

  // int64 peak_bytes = 7;
  void clear_peak_bytes();
  int64_t peak_bytes() const;
  void set_peak_bytes(int64_t value);
  private:
  int64_t _internal_peak_bytes() const;
  void _internal_set_peak_bytes(int64_t value);
  public:

  // int64 residual_bytes = 8;
  void clear_residual_bytes();
  int64_t residual_bytes() const;
  void set_residual_bytes(int64_t value);
  private:
  int64_t _internal_residual_bytes() const;
  void _internal_set_residual_bytes(int64_t value);
  public:

  // int64 output_bytes = 9;
  void clear_output_bytes();
  int64_t output_bytes() const;
  void set_output_bytes(int64_t value);
  private:
  int64_t _internal_output_bytes() const;
  void _internal_set_output_bytes(int64_t value);
  public:

  // int64 allocator_bytes_in_use = 10;
  void clear_allocator_bytes_in_use();
  int64_t allocator_bytes_in_use() const;
  void set_allocator_bytes_in_use(int64_t value);
  private:
  int64_t _internal_allocator_bytes_in_use() const;
  void _internal_set_allocator_bytes_in_use(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.ExecMemory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ExecMemory_OutputMemoryEntry_DoNotUse,
        int32_t, ::tensorflow::tfprof::Memory,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> output_memory_;
    int64_t memory_micros_;
    int64_t host_temp_bytes_;
    int64_t host_persistent_bytes_;
    int64_t accelerator_temp_bytes_;
    int64_t accelerator_persistent_bytes_;
    int64_t requested_bytes_;
    int64_t peak_bytes_;
    int64_t residual_bytes_;
    int64_t output_bytes_;
    int64_t allocator_bytes_in_use_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class Tuple final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.Tuple) */ {
 public:
  inline Tuple() : Tuple(nullptr) {}
  ~Tuple() override;
  explicit PROTOBUF_CONSTEXPR Tuple(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Tuple(const Tuple& from);
  Tuple(Tuple&& from) noexcept
    : Tuple() {
    *this = ::std::move(from);
  }

  inline Tuple& operator=(const Tuple& from) {
    CopyFrom(from);
    return *this;
  }
  inline Tuple& operator=(Tuple&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Tuple& default_instance() {
    return *internal_default_instance();
  }
  static inline const Tuple* internal_default_instance() {
    return reinterpret_cast<const Tuple*>(
               &_Tuple_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(Tuple& a, Tuple& b) {
    a.Swap(&b);
  }
  inline void Swap(Tuple* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Tuple* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Tuple* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Tuple>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Tuple& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Tuple& from) {
    Tuple::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Tuple* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.Tuple";
  }
  protected:
  explicit Tuple(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInt64ValuesFieldNumber = 1,
  };
  // repeated int64 int64_values = 1;
  int int64_values_size() const;
  private:
  int _internal_int64_values_size() const;
  public:
  void clear_int64_values();
  private:
  int64_t _internal_int64_values(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_int64_values() const;
  void _internal_add_int64_values(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_int64_values();
  public:
  int64_t int64_values(int index) const;
  void set_int64_values(int index, int64_t value);
  void add_int64_values(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      int64_values() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_int64_values();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.Tuple)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > int64_values_;
    mutable std::atomic<int> _int64_values_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// -------------------------------------------------------------------

class Memory final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.Memory) */ {
 public:
  inline Memory() : Memory(nullptr) {}
  ~Memory() override;
  explicit PROTOBUF_CONSTEXPR Memory(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Memory(const Memory& from);
  Memory(Memory&& from) noexcept
    : Memory() {
    *this = ::std::move(from);
  }

  inline Memory& operator=(const Memory& from) {
    CopyFrom(from);
    return *this;
  }
  inline Memory& operator=(Memory&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Memory& default_instance() {
    return *internal_default_instance();
  }
  static inline const Memory* internal_default_instance() {
    return reinterpret_cast<const Memory*>(
               &_Memory_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(Memory& a, Memory& b) {
    a.Swap(&b);
  }
  inline void Swap(Memory* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Memory* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Memory* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Memory>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Memory& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Memory& from) {
    Memory::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Memory* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.Memory";
  }
  protected:
  explicit Memory(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBytesFieldNumber = 1,
    kPtrFieldNumber = 2,
  };
  // int64 bytes = 1;
  void clear_bytes();
  int64_t bytes() const;
  void set_bytes(int64_t value);
  private:
  int64_t _internal_bytes() const;
  void _internal_set_bytes(int64_t value);
  public:

  // uint64 ptr = 2;
  void clear_ptr();
  uint64_t ptr() const;
  void set_ptr(uint64_t value);
  private:
  uint64_t _internal_ptr() const;
  void _internal_set_ptr(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.Memory)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t bytes_;
    uint64_t ptr_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CodeDef_Trace

// string file = 1 [deprecated = true];
inline void CodeDef_Trace::clear_file() {
  _impl_.file_.ClearToEmpty();
}
inline const std::string& CodeDef_Trace::file() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.file)
  return _internal_file();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CodeDef_Trace::set_file(ArgT0&& arg0, ArgT... args) {
 
 _impl_.file_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.file)
}
inline std::string* CodeDef_Trace::mutable_file() {
  std::string* _s = _internal_mutable_file();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.file)
  return _s;
}
inline const std::string& CodeDef_Trace::_internal_file() const {
  return _impl_.file_.Get();
}
inline void CodeDef_Trace::_internal_set_file(const std::string& value) {
  
  _impl_.file_.Set(value, GetArenaForAllocation());
}
inline std::string* CodeDef_Trace::_internal_mutable_file() {
  
  return _impl_.file_.Mutable(GetArenaForAllocation());
}
inline std::string* CodeDef_Trace::release_file() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.file)
  return _impl_.file_.Release();
}
inline void CodeDef_Trace::set_allocated_file(std::string* file) {
  if (file != nullptr) {
    
  } else {
    
  }
  _impl_.file_.SetAllocated(file, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.file_.IsDefault()) {
    _impl_.file_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.file)
}

// int64 file_id = 6;
inline void CodeDef_Trace::clear_file_id() {
  _impl_.file_id_ = int64_t{0};
}
inline int64_t CodeDef_Trace::_internal_file_id() const {
  return _impl_.file_id_;
}
inline int64_t CodeDef_Trace::file_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.file_id)
  return _internal_file_id();
}
inline void CodeDef_Trace::_internal_set_file_id(int64_t value) {
  
  _impl_.file_id_ = value;
}
inline void CodeDef_Trace::set_file_id(int64_t value) {
  _internal_set_file_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.file_id)
}

// int32 lineno = 2;
inline void CodeDef_Trace::clear_lineno() {
  _impl_.lineno_ = 0;
}
inline int32_t CodeDef_Trace::_internal_lineno() const {
  return _impl_.lineno_;
}
inline int32_t CodeDef_Trace::lineno() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.lineno)
  return _internal_lineno();
}
inline void CodeDef_Trace::_internal_set_lineno(int32_t value) {
  
  _impl_.lineno_ = value;
}
inline void CodeDef_Trace::set_lineno(int32_t value) {
  _internal_set_lineno(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.lineno)
}

// string function = 3 [deprecated = true];
inline void CodeDef_Trace::clear_function() {
  _impl_.function_.ClearToEmpty();
}
inline const std::string& CodeDef_Trace::function() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.function)
  return _internal_function();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CodeDef_Trace::set_function(ArgT0&& arg0, ArgT... args) {
 
 _impl_.function_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.function)
}
inline std::string* CodeDef_Trace::mutable_function() {
  std::string* _s = _internal_mutable_function();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.function)
  return _s;
}
inline const std::string& CodeDef_Trace::_internal_function() const {
  return _impl_.function_.Get();
}
inline void CodeDef_Trace::_internal_set_function(const std::string& value) {
  
  _impl_.function_.Set(value, GetArenaForAllocation());
}
inline std::string* CodeDef_Trace::_internal_mutable_function() {
  
  return _impl_.function_.Mutable(GetArenaForAllocation());
}
inline std::string* CodeDef_Trace::release_function() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.function)
  return _impl_.function_.Release();
}
inline void CodeDef_Trace::set_allocated_function(std::string* function) {
  if (function != nullptr) {
    
  } else {
    
  }
  _impl_.function_.SetAllocated(function, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.function_.IsDefault()) {
    _impl_.function_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.function)
}

// int64 function_id = 7;
inline void CodeDef_Trace::clear_function_id() {
  _impl_.function_id_ = int64_t{0};
}
inline int64_t CodeDef_Trace::_internal_function_id() const {
  return _impl_.function_id_;
}
inline int64_t CodeDef_Trace::function_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.function_id)
  return _internal_function_id();
}
inline void CodeDef_Trace::_internal_set_function_id(int64_t value) {
  
  _impl_.function_id_ = value;
}
inline void CodeDef_Trace::set_function_id(int64_t value) {
  _internal_set_function_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.function_id)
}

// string line = 4 [deprecated = true];
inline void CodeDef_Trace::clear_line() {
  _impl_.line_.ClearToEmpty();
}
inline const std::string& CodeDef_Trace::line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.line)
  return _internal_line();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CodeDef_Trace::set_line(ArgT0&& arg0, ArgT... args) {
 
 _impl_.line_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.line)
}
inline std::string* CodeDef_Trace::mutable_line() {
  std::string* _s = _internal_mutable_line();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.Trace.line)
  return _s;
}
inline const std::string& CodeDef_Trace::_internal_line() const {
  return _impl_.line_.Get();
}
inline void CodeDef_Trace::_internal_set_line(const std::string& value) {
  
  _impl_.line_.Set(value, GetArenaForAllocation());
}
inline std::string* CodeDef_Trace::_internal_mutable_line() {
  
  return _impl_.line_.Mutable(GetArenaForAllocation());
}
inline std::string* CodeDef_Trace::release_line() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.CodeDef.Trace.line)
  return _impl_.line_.Release();
}
inline void CodeDef_Trace::set_allocated_line(std::string* line) {
  if (line != nullptr) {
    
  } else {
    
  }
  _impl_.line_.SetAllocated(line, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.line_.IsDefault()) {
    _impl_.line_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.CodeDef.Trace.line)
}

// int64 line_id = 8;
inline void CodeDef_Trace::clear_line_id() {
  _impl_.line_id_ = int64_t{0};
}
inline int64_t CodeDef_Trace::_internal_line_id() const {
  return _impl_.line_id_;
}
inline int64_t CodeDef_Trace::line_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.line_id)
  return _internal_line_id();
}
inline void CodeDef_Trace::_internal_set_line_id(int64_t value) {
  
  _impl_.line_id_ = value;
}
inline void CodeDef_Trace::set_line_id(int64_t value) {
  _internal_set_line_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.line_id)
}

// int32 func_start_line = 5;
inline void CodeDef_Trace::clear_func_start_line() {
  _impl_.func_start_line_ = 0;
}
inline int32_t CodeDef_Trace::_internal_func_start_line() const {
  return _impl_.func_start_line_;
}
inline int32_t CodeDef_Trace::func_start_line() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.Trace.func_start_line)
  return _internal_func_start_line();
}
inline void CodeDef_Trace::_internal_set_func_start_line(int32_t value) {
  
  _impl_.func_start_line_ = value;
}
inline void CodeDef_Trace::set_func_start_line(int32_t value) {
  _internal_set_func_start_line(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.CodeDef.Trace.func_start_line)
}

// -------------------------------------------------------------------

// CodeDef

// repeated .tensorflow.tfprof.CodeDef.Trace traces = 1;
inline int CodeDef::_internal_traces_size() const {
  return _impl_.traces_.size();
}
inline int CodeDef::traces_size() const {
  return _internal_traces_size();
}
inline void CodeDef::clear_traces() {
  _impl_.traces_.Clear();
}
inline ::tensorflow::tfprof::CodeDef_Trace* CodeDef::mutable_traces(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.CodeDef.traces)
  return _impl_.traces_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >*
CodeDef::mutable_traces() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.CodeDef.traces)
  return &_impl_.traces_;
}
inline const ::tensorflow::tfprof::CodeDef_Trace& CodeDef::_internal_traces(int index) const {
  return _impl_.traces_.Get(index);
}
inline const ::tensorflow::tfprof::CodeDef_Trace& CodeDef::traces(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.CodeDef.traces)
  return _internal_traces(index);
}
inline ::tensorflow::tfprof::CodeDef_Trace* CodeDef::_internal_add_traces() {
  return _impl_.traces_.Add();
}
inline ::tensorflow::tfprof::CodeDef_Trace* CodeDef::add_traces() {
  ::tensorflow::tfprof::CodeDef_Trace* _add = _internal_add_traces();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.CodeDef.traces)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::CodeDef_Trace >&
CodeDef::traces() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.CodeDef.traces)
  return _impl_.traces_;
}

// -------------------------------------------------------------------

// OpLogEntry

// string name = 1;
inline void OpLogEntry::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& OpLogEntry::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpLogEntry::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.name)
}
inline std::string* OpLogEntry::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.name)
  return _s;
}
inline const std::string& OpLogEntry::_internal_name() const {
  return _impl_.name_.Get();
}
inline void OpLogEntry::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* OpLogEntry::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* OpLogEntry::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OpLogEntry.name)
  return _impl_.name_.Release();
}
inline void OpLogEntry::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OpLogEntry.name)
}

// int64 float_ops = 2;
inline void OpLogEntry::clear_float_ops() {
  _impl_.float_ops_ = int64_t{0};
}
inline int64_t OpLogEntry::_internal_float_ops() const {
  return _impl_.float_ops_;
}
inline int64_t OpLogEntry::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.float_ops)
  return _internal_float_ops();
}
inline void OpLogEntry::_internal_set_float_ops(int64_t value) {
  
  _impl_.float_ops_ = value;
}
inline void OpLogEntry::set_float_ops(int64_t value) {
  _internal_set_float_ops(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.float_ops)
}

// repeated string types = 3;
inline int OpLogEntry::_internal_types_size() const {
  return _impl_.types_.size();
}
inline int OpLogEntry::types_size() const {
  return _internal_types_size();
}
inline void OpLogEntry::clear_types() {
  _impl_.types_.Clear();
}
inline std::string* OpLogEntry::add_types() {
  std::string* _s = _internal_add_types();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.OpLogEntry.types)
  return _s;
}
inline const std::string& OpLogEntry::_internal_types(int index) const {
  return _impl_.types_.Get(index);
}
inline const std::string& OpLogEntry::types(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.types)
  return _internal_types(index);
}
inline std::string* OpLogEntry::mutable_types(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.types)
  return _impl_.types_.Mutable(index);
}
inline void OpLogEntry::set_types(int index, const std::string& value) {
  _impl_.types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::set_types(int index, std::string&& value) {
  _impl_.types_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::set_types(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::set_types(int index, const char* value, size_t size) {
  _impl_.types_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.OpLogEntry.types)
}
inline std::string* OpLogEntry::_internal_add_types() {
  return _impl_.types_.Add();
}
inline void OpLogEntry::add_types(const std::string& value) {
  _impl_.types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::add_types(std::string&& value) {
  _impl_.types_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::add_types(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.OpLogEntry.types)
}
inline void OpLogEntry::add_types(const char* value, size_t size) {
  _impl_.types_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.OpLogEntry.types)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OpLogEntry::types() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OpLogEntry.types)
  return _impl_.types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OpLogEntry::mutable_types() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OpLogEntry.types)
  return &_impl_.types_;
}

// .tensorflow.tfprof.CodeDef code_def = 4;
inline bool OpLogEntry::_internal_has_code_def() const {
  return this != internal_default_instance() && _impl_.code_def_ != nullptr;
}
inline bool OpLogEntry::has_code_def() const {
  return _internal_has_code_def();
}
inline void OpLogEntry::clear_code_def() {
  if (GetArenaForAllocation() == nullptr && _impl_.code_def_ != nullptr) {
    delete _impl_.code_def_;
  }
  _impl_.code_def_ = nullptr;
}
inline const ::tensorflow::tfprof::CodeDef& OpLogEntry::_internal_code_def() const {
  const ::tensorflow::tfprof::CodeDef* p = _impl_.code_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tfprof::CodeDef&>(
      ::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline const ::tensorflow::tfprof::CodeDef& OpLogEntry::code_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogEntry.code_def)
  return _internal_code_def();
}
inline void OpLogEntry::unsafe_arena_set_allocated_code_def(
    ::tensorflow::tfprof::CodeDef* code_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.code_def_);
  }
  _impl_.code_def_ = code_def;
  if (code_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tfprof.OpLogEntry.code_def)
}
inline ::tensorflow::tfprof::CodeDef* OpLogEntry::release_code_def() {
  
  ::tensorflow::tfprof::CodeDef* temp = _impl_.code_def_;
  _impl_.code_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* OpLogEntry::unsafe_arena_release_code_def() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.OpLogEntry.code_def)
  
  ::tensorflow::tfprof::CodeDef* temp = _impl_.code_def_;
  _impl_.code_def_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* OpLogEntry::_internal_mutable_code_def() {
  
  if (_impl_.code_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaForAllocation());
    _impl_.code_def_ = p;
  }
  return _impl_.code_def_;
}
inline ::tensorflow::tfprof::CodeDef* OpLogEntry::mutable_code_def() {
  ::tensorflow::tfprof::CodeDef* _msg = _internal_mutable_code_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogEntry.code_def)
  return _msg;
}
inline void OpLogEntry::set_allocated_code_def(::tensorflow::tfprof::CodeDef* code_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.code_def_;
  }
  if (code_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(code_def);
    if (message_arena != submessage_arena) {
      code_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, code_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.code_def_ = code_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.OpLogEntry.code_def)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// OpLogProto

// repeated .tensorflow.tfprof.OpLogEntry log_entries = 1;
inline int OpLogProto::_internal_log_entries_size() const {
  return _impl_.log_entries_.size();
}
inline int OpLogProto::log_entries_size() const {
  return _internal_log_entries_size();
}
inline void OpLogProto::clear_log_entries() {
  _impl_.log_entries_.Clear();
}
inline ::tensorflow::tfprof::OpLogEntry* OpLogProto::mutable_log_entries(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.OpLogProto.log_entries)
  return _impl_.log_entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >*
OpLogProto::mutable_log_entries() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.OpLogProto.log_entries)
  return &_impl_.log_entries_;
}
inline const ::tensorflow::tfprof::OpLogEntry& OpLogProto::_internal_log_entries(int index) const {
  return _impl_.log_entries_.Get(index);
}
inline const ::tensorflow::tfprof::OpLogEntry& OpLogProto::log_entries(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.OpLogProto.log_entries)
  return _internal_log_entries(index);
}
inline ::tensorflow::tfprof::OpLogEntry* OpLogProto::_internal_add_log_entries() {
  return _impl_.log_entries_.Add();
}
inline ::tensorflow::tfprof::OpLogEntry* OpLogProto::add_log_entries() {
  ::tensorflow::tfprof::OpLogEntry* _add = _internal_add_log_entries();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.OpLogProto.log_entries)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::OpLogEntry >&
OpLogProto::log_entries() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.OpLogProto.log_entries)
  return _impl_.log_entries_;
}

// map<int64, string> id_to_string = 2;
inline int OpLogProto::_internal_id_to_string_size() const {
  return _impl_.id_to_string_.size();
}
inline int OpLogProto::id_to_string_size() const {
  return _internal_id_to_string_size();
}
inline void OpLogProto::clear_id_to_string() {
  _impl_.id_to_string_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
OpLogProto::_internal_id_to_string() const {
  return _impl_.id_to_string_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
OpLogProto::id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.OpLogProto.id_to_string)
  return _internal_id_to_string();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
OpLogProto::_internal_mutable_id_to_string() {
  return _impl_.id_to_string_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
OpLogProto::mutable_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.OpLogProto.id_to_string)
  return _internal_mutable_id_to_string();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileProto

// map<int64, .tensorflow.tfprof.ProfileNode> nodes = 1;
inline int ProfileProto::_internal_nodes_size() const {
  return _impl_.nodes_.size();
}
inline int ProfileProto::nodes_size() const {
  return _internal_nodes_size();
}
inline void ProfileProto::clear_nodes() {
  _impl_.nodes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ProfileNode >&
ProfileProto::_internal_nodes() const {
  return _impl_.nodes_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ProfileNode >&
ProfileProto::nodes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileProto.nodes)
  return _internal_nodes();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ProfileNode >*
ProfileProto::_internal_mutable_nodes() {
  return _impl_.nodes_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ProfileNode >*
ProfileProto::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileProto.nodes)
  return _internal_mutable_nodes();
}

// bool has_trace = 2;
inline void ProfileProto::clear_has_trace() {
  _impl_.has_trace_ = false;
}
inline bool ProfileProto::_internal_has_trace() const {
  return _impl_.has_trace_;
}
inline bool ProfileProto::has_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.has_trace)
  return _internal_has_trace();
}
inline void ProfileProto::_internal_set_has_trace(bool value) {
  
  _impl_.has_trace_ = value;
}
inline void ProfileProto::set_has_trace(bool value) {
  _internal_set_has_trace(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.has_trace)
}

// bool miss_accelerator_stream = 5;
inline void ProfileProto::clear_miss_accelerator_stream() {
  _impl_.miss_accelerator_stream_ = false;
}
inline bool ProfileProto::_internal_miss_accelerator_stream() const {
  return _impl_.miss_accelerator_stream_;
}
inline bool ProfileProto::miss_accelerator_stream() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.miss_accelerator_stream)
  return _internal_miss_accelerator_stream();
}
inline void ProfileProto::_internal_set_miss_accelerator_stream(bool value) {
  
  _impl_.miss_accelerator_stream_ = value;
}
inline void ProfileProto::set_miss_accelerator_stream(bool value) {
  _internal_set_miss_accelerator_stream(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.miss_accelerator_stream)
}

// repeated int64 steps = 3;
inline int ProfileProto::_internal_steps_size() const {
  return _impl_.steps_.size();
}
inline int ProfileProto::steps_size() const {
  return _internal_steps_size();
}
inline void ProfileProto::clear_steps() {
  _impl_.steps_.Clear();
}
inline int64_t ProfileProto::_internal_steps(int index) const {
  return _impl_.steps_.Get(index);
}
inline int64_t ProfileProto::steps(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileProto.steps)
  return _internal_steps(index);
}
inline void ProfileProto::set_steps(int index, int64_t value) {
  _impl_.steps_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileProto.steps)
}
inline void ProfileProto::_internal_add_steps(int64_t value) {
  _impl_.steps_.Add(value);
}
inline void ProfileProto::add_steps(int64_t value) {
  _internal_add_steps(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileProto.steps)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ProfileProto::_internal_steps() const {
  return _impl_.steps_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ProfileProto::steps() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileProto.steps)
  return _internal_steps();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ProfileProto::_internal_mutable_steps() {
  return &_impl_.steps_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ProfileProto::mutable_steps() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileProto.steps)
  return _internal_mutable_steps();
}

// map<int64, string> id_to_string = 4;
inline int ProfileProto::_internal_id_to_string_size() const {
  return _impl_.id_to_string_.size();
}
inline int ProfileProto::id_to_string_size() const {
  return _internal_id_to_string_size();
}
inline void ProfileProto::clear_id_to_string() {
  _impl_.id_to_string_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
ProfileProto::_internal_id_to_string() const {
  return _impl_.id_to_string_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >&
ProfileProto::id_to_string() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileProto.id_to_string)
  return _internal_id_to_string();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
ProfileProto::_internal_mutable_id_to_string() {
  return _impl_.id_to_string_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, std::string >*
ProfileProto::mutable_id_to_string() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileProto.id_to_string)
  return _internal_mutable_id_to_string();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileNode

// string name = 1;
inline void ProfileNode::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ProfileNode::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileNode::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.name)
}
inline std::string* ProfileNode::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.name)
  return _s;
}
inline const std::string& ProfileNode::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ProfileNode::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileNode::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileNode::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.name)
  return _impl_.name_.Release();
}
inline void ProfileNode::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.name)
}

// string op = 9;
inline void ProfileNode::clear_op() {
  _impl_.op_.ClearToEmpty();
}
inline const std::string& ProfileNode::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.op)
  return _internal_op();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileNode::set_op(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op)
}
inline std::string* ProfileNode::mutable_op() {
  std::string* _s = _internal_mutable_op();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.op)
  return _s;
}
inline const std::string& ProfileNode::_internal_op() const {
  return _impl_.op_.Get();
}
inline void ProfileNode::_internal_set_op(const std::string& value) {
  
  _impl_.op_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileNode::_internal_mutable_op() {
  
  return _impl_.op_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileNode::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.op)
  return _impl_.op_.Release();
}
inline void ProfileNode::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  _impl_.op_.SetAllocated(op, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_.IsDefault()) {
    _impl_.op_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.op)
}

// int64 id = 13;
inline void ProfileNode::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t ProfileNode::_internal_id() const {
  return _impl_.id_;
}
inline int64_t ProfileNode::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.id)
  return _internal_id();
}
inline void ProfileNode::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void ProfileNode::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.id)
}

// map<int32, int64> inputs = 2;
inline int ProfileNode::_internal_inputs_size() const {
  return _impl_.inputs_.size();
}
inline int ProfileNode::inputs_size() const {
  return _internal_inputs_size();
}
inline void ProfileNode::clear_inputs() {
  _impl_.inputs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >&
ProfileNode::_internal_inputs() const {
  return _impl_.inputs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >&
ProfileNode::inputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.inputs)
  return _internal_inputs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >*
ProfileNode::_internal_mutable_inputs() {
  return _impl_.inputs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >*
ProfileNode::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.inputs)
  return _internal_mutable_inputs();
}

// map<int32, .tensorflow.tfprof.Tuple> input_shapes = 16;
inline int ProfileNode::_internal_input_shapes_size() const {
  return _impl_.input_shapes_.size();
}
inline int ProfileNode::input_shapes_size() const {
  return _internal_input_shapes_size();
}
inline void ProfileNode::clear_input_shapes() {
  _impl_.input_shapes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >&
ProfileNode::_internal_input_shapes() const {
  return _impl_.input_shapes_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >&
ProfileNode::input_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.input_shapes)
  return _internal_input_shapes();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >*
ProfileNode::_internal_mutable_input_shapes() {
  return _impl_.input_shapes_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >*
ProfileNode::mutable_input_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.input_shapes)
  return _internal_mutable_input_shapes();
}

// map<int32, int64> outputs = 3;
inline int ProfileNode::_internal_outputs_size() const {
  return _impl_.outputs_.size();
}
inline int ProfileNode::outputs_size() const {
  return _internal_outputs_size();
}
inline void ProfileNode::clear_outputs() {
  _impl_.outputs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >&
ProfileNode::_internal_outputs() const {
  return _impl_.outputs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >&
ProfileNode::outputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.outputs)
  return _internal_outputs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >*
ProfileNode::_internal_mutable_outputs() {
  return _impl_.outputs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, int64_t >*
ProfileNode::mutable_outputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.outputs)
  return _internal_mutable_outputs();
}

// map<int32, .tensorflow.tfprof.Tuple> output_shapes = 15;
inline int ProfileNode::_internal_output_shapes_size() const {
  return _impl_.output_shapes_.size();
}
inline int ProfileNode::output_shapes_size() const {
  return _internal_output_shapes_size();
}
inline void ProfileNode::clear_output_shapes() {
  _impl_.output_shapes_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >&
ProfileNode::_internal_output_shapes() const {
  return _impl_.output_shapes_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >&
ProfileNode::output_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.output_shapes)
  return _internal_output_shapes();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >*
ProfileNode::_internal_mutable_output_shapes() {
  return _impl_.output_shapes_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Tuple >*
ProfileNode::mutable_output_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.output_shapes)
  return _internal_mutable_output_shapes();
}

// map<int64, int32> src_output_index = 14;
inline int ProfileNode::_internal_src_output_index_size() const {
  return _impl_.src_output_index_.size();
}
inline int ProfileNode::src_output_index_size() const {
  return _internal_src_output_index_size();
}
inline void ProfileNode::clear_src_output_index() {
  _impl_.src_output_index_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
ProfileNode::_internal_src_output_index() const {
  return _impl_.src_output_index_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >&
ProfileNode::src_output_index() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.src_output_index)
  return _internal_src_output_index();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
ProfileNode::_internal_mutable_src_output_index() {
  return _impl_.src_output_index_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, int32_t >*
ProfileNode::mutable_src_output_index() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.src_output_index)
  return _internal_mutable_src_output_index();
}

// repeated int64 shape = 4;
inline int ProfileNode::_internal_shape_size() const {
  return _impl_.shape_.size();
}
inline int ProfileNode::shape_size() const {
  return _internal_shape_size();
}
inline void ProfileNode::clear_shape() {
  _impl_.shape_.Clear();
}
inline int64_t ProfileNode::_internal_shape(int index) const {
  return _impl_.shape_.Get(index);
}
inline int64_t ProfileNode::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.shape)
  return _internal_shape(index);
}
inline void ProfileNode::set_shape(int index, int64_t value) {
  _impl_.shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.shape)
}
inline void ProfileNode::_internal_add_shape(int64_t value) {
  _impl_.shape_.Add(value);
}
inline void ProfileNode::add_shape(int64_t value) {
  _internal_add_shape(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ProfileNode::_internal_shape() const {
  return _impl_.shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
ProfileNode::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileNode.shape)
  return _internal_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ProfileNode::_internal_mutable_shape() {
  return &_impl_.shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
ProfileNode::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileNode.shape)
  return _internal_mutable_shape();
}

// repeated string op_types = 5;
inline int ProfileNode::_internal_op_types_size() const {
  return _impl_.op_types_.size();
}
inline int ProfileNode::op_types_size() const {
  return _internal_op_types_size();
}
inline void ProfileNode::clear_op_types() {
  _impl_.op_types_.Clear();
}
inline std::string* ProfileNode::add_op_types() {
  std::string* _s = _internal_add_op_types();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.ProfileNode.op_types)
  return _s;
}
inline const std::string& ProfileNode::_internal_op_types(int index) const {
  return _impl_.op_types_.Get(index);
}
inline const std::string& ProfileNode::op_types(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.op_types)
  return _internal_op_types(index);
}
inline std::string* ProfileNode::mutable_op_types(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.op_types)
  return _impl_.op_types_.Mutable(index);
}
inline void ProfileNode::set_op_types(int index, const std::string& value) {
  _impl_.op_types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::set_op_types(int index, std::string&& value) {
  _impl_.op_types_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::set_op_types(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.op_types_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::set_op_types(int index, const char* value, size_t size) {
  _impl_.op_types_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ProfileNode.op_types)
}
inline std::string* ProfileNode::_internal_add_op_types() {
  return _impl_.op_types_.Add();
}
inline void ProfileNode::add_op_types(const std::string& value) {
  _impl_.op_types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::add_op_types(std::string&& value) {
  _impl_.op_types_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::add_op_types(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.op_types_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.ProfileNode.op_types)
}
inline void ProfileNode::add_op_types(const char* value, size_t size) {
  _impl_.op_types_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.ProfileNode.op_types)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ProfileNode::op_types() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ProfileNode.op_types)
  return _impl_.op_types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ProfileNode::mutable_op_types() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ProfileNode.op_types)
  return &_impl_.op_types_;
}

// string canonical_device = 6;
inline void ProfileNode::clear_canonical_device() {
  _impl_.canonical_device_.ClearToEmpty();
}
inline const std::string& ProfileNode::canonical_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.canonical_device)
  return _internal_canonical_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileNode::set_canonical_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.canonical_device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.canonical_device)
}
inline std::string* ProfileNode::mutable_canonical_device() {
  std::string* _s = _internal_mutable_canonical_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.canonical_device)
  return _s;
}
inline const std::string& ProfileNode::_internal_canonical_device() const {
  return _impl_.canonical_device_.Get();
}
inline void ProfileNode::_internal_set_canonical_device(const std::string& value) {
  
  _impl_.canonical_device_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileNode::_internal_mutable_canonical_device() {
  
  return _impl_.canonical_device_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileNode::release_canonical_device() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.canonical_device)
  return _impl_.canonical_device_.Release();
}
inline void ProfileNode::set_allocated_canonical_device(std::string* canonical_device) {
  if (canonical_device != nullptr) {
    
  } else {
    
  }
  _impl_.canonical_device_.SetAllocated(canonical_device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.canonical_device_.IsDefault()) {
    _impl_.canonical_device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.canonical_device)
}

// string host_device = 7;
inline void ProfileNode::clear_host_device() {
  _impl_.host_device_.ClearToEmpty();
}
inline const std::string& ProfileNode::host_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.host_device)
  return _internal_host_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileNode::set_host_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.host_device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.host_device)
}
inline std::string* ProfileNode::mutable_host_device() {
  std::string* _s = _internal_mutable_host_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.host_device)
  return _s;
}
inline const std::string& ProfileNode::_internal_host_device() const {
  return _impl_.host_device_.Get();
}
inline void ProfileNode::_internal_set_host_device(const std::string& value) {
  
  _impl_.host_device_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileNode::_internal_mutable_host_device() {
  
  return _impl_.host_device_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileNode::release_host_device() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.host_device)
  return _impl_.host_device_.Release();
}
inline void ProfileNode::set_allocated_host_device(std::string* host_device) {
  if (host_device != nullptr) {
    
  } else {
    
  }
  _impl_.host_device_.SetAllocated(host_device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.host_device_.IsDefault()) {
    _impl_.host_device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.host_device)
}

// int64 float_ops = 8;
inline void ProfileNode::clear_float_ops() {
  _impl_.float_ops_ = int64_t{0};
}
inline int64_t ProfileNode::_internal_float_ops() const {
  return _impl_.float_ops_;
}
inline int64_t ProfileNode::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.float_ops)
  return _internal_float_ops();
}
inline void ProfileNode::_internal_set_float_ops(int64_t value) {
  
  _impl_.float_ops_ = value;
}
inline void ProfileNode::set_float_ops(int64_t value) {
  _internal_set_float_ops(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ProfileNode.float_ops)
}

// .tensorflow.tfprof.CodeDef trace = 10;
inline bool ProfileNode::_internal_has_trace() const {
  return this != internal_default_instance() && _impl_.trace_ != nullptr;
}
inline bool ProfileNode::has_trace() const {
  return _internal_has_trace();
}
inline void ProfileNode::clear_trace() {
  if (GetArenaForAllocation() == nullptr && _impl_.trace_ != nullptr) {
    delete _impl_.trace_;
  }
  _impl_.trace_ = nullptr;
}
inline const ::tensorflow::tfprof::CodeDef& ProfileNode::_internal_trace() const {
  const ::tensorflow::tfprof::CodeDef* p = _impl_.trace_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tfprof::CodeDef&>(
      ::tensorflow::tfprof::_CodeDef_default_instance_);
}
inline const ::tensorflow::tfprof::CodeDef& ProfileNode::trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ProfileNode.trace)
  return _internal_trace();
}
inline void ProfileNode::unsafe_arena_set_allocated_trace(
    ::tensorflow::tfprof::CodeDef* trace) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.trace_);
  }
  _impl_.trace_ = trace;
  if (trace) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tfprof.ProfileNode.trace)
}
inline ::tensorflow::tfprof::CodeDef* ProfileNode::release_trace() {
  
  ::tensorflow::tfprof::CodeDef* temp = _impl_.trace_;
  _impl_.trace_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* ProfileNode::unsafe_arena_release_trace() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.ProfileNode.trace)
  
  ::tensorflow::tfprof::CodeDef* temp = _impl_.trace_;
  _impl_.trace_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::CodeDef* ProfileNode::_internal_mutable_trace() {
  
  if (_impl_.trace_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::CodeDef>(GetArenaForAllocation());
    _impl_.trace_ = p;
  }
  return _impl_.trace_;
}
inline ::tensorflow::tfprof::CodeDef* ProfileNode::mutable_trace() {
  ::tensorflow::tfprof::CodeDef* _msg = _internal_mutable_trace();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ProfileNode.trace)
  return _msg;
}
inline void ProfileNode::set_allocated_trace(::tensorflow::tfprof::CodeDef* trace) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.trace_;
  }
  if (trace) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(trace);
    if (message_arena != submessage_arena) {
      trace = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, trace, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.trace_ = trace;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.ProfileNode.trace)
}

// map<string, .tensorflow.AttrValue> attrs = 11;
inline int ProfileNode::_internal_attrs_size() const {
  return _impl_.attrs_.size();
}
inline int ProfileNode::attrs_size() const {
  return _internal_attrs_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
ProfileNode::_internal_attrs() const {
  return _impl_.attrs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
ProfileNode::attrs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.attrs)
  return _internal_attrs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
ProfileNode::_internal_mutable_attrs() {
  return _impl_.attrs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
ProfileNode::mutable_attrs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.attrs)
  return _internal_mutable_attrs();
}

// map<int64, .tensorflow.tfprof.ExecProfile> execs = 12;
inline int ProfileNode::_internal_execs_size() const {
  return _impl_.execs_.size();
}
inline int ProfileNode::execs_size() const {
  return _internal_execs_size();
}
inline void ProfileNode::clear_execs() {
  _impl_.execs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ExecProfile >&
ProfileNode::_internal_execs() const {
  return _impl_.execs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ExecProfile >&
ProfileNode::execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ProfileNode.execs)
  return _internal_execs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ExecProfile >*
ProfileNode::_internal_mutable_execs() {
  return _impl_.execs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int64_t, ::tensorflow::tfprof::ExecProfile >*
ProfileNode::mutable_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ProfileNode.execs)
  return _internal_mutable_execs();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExecProfile

// int64 run_count = 1;
inline void ExecProfile::clear_run_count() {
  _impl_.run_count_ = int64_t{0};
}
inline int64_t ExecProfile::_internal_run_count() const {
  return _impl_.run_count_;
}
inline int64_t ExecProfile::run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.run_count)
  return _internal_run_count();
}
inline void ExecProfile::_internal_set_run_count(int64_t value) {
  
  _impl_.run_count_ = value;
}
inline void ExecProfile::set_run_count(int64_t value) {
  _internal_set_run_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.run_count)
}

// int64 all_start_micros = 2;
inline void ExecProfile::clear_all_start_micros() {
  _impl_.all_start_micros_ = int64_t{0};
}
inline int64_t ExecProfile::_internal_all_start_micros() const {
  return _impl_.all_start_micros_;
}
inline int64_t ExecProfile::all_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.all_start_micros)
  return _internal_all_start_micros();
}
inline void ExecProfile::_internal_set_all_start_micros(int64_t value) {
  
  _impl_.all_start_micros_ = value;
}
inline void ExecProfile::set_all_start_micros(int64_t value) {
  _internal_set_all_start_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.all_start_micros)
}

// int64 latest_end_micros = 3;
inline void ExecProfile::clear_latest_end_micros() {
  _impl_.latest_end_micros_ = int64_t{0};
}
inline int64_t ExecProfile::_internal_latest_end_micros() const {
  return _impl_.latest_end_micros_;
}
inline int64_t ExecProfile::latest_end_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.latest_end_micros)
  return _internal_latest_end_micros();
}
inline void ExecProfile::_internal_set_latest_end_micros(int64_t value) {
  
  _impl_.latest_end_micros_ = value;
}
inline void ExecProfile::set_latest_end_micros(int64_t value) {
  _internal_set_latest_end_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.latest_end_micros)
}

// map<string, .tensorflow.tfprof.ExecTime> accelerator_execs = 4;
inline int ExecProfile::_internal_accelerator_execs_size() const {
  return _impl_.accelerator_execs_.size();
}
inline int ExecProfile::accelerator_execs_size() const {
  return _internal_accelerator_execs_size();
}
inline void ExecProfile::clear_accelerator_execs() {
  _impl_.accelerator_execs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
ExecProfile::_internal_accelerator_execs() const {
  return _impl_.accelerator_execs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
ExecProfile::accelerator_execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecProfile.accelerator_execs)
  return _internal_accelerator_execs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
ExecProfile::_internal_mutable_accelerator_execs() {
  return _impl_.accelerator_execs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
ExecProfile::mutable_accelerator_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecProfile.accelerator_execs)
  return _internal_mutable_accelerator_execs();
}

// map<string, .tensorflow.tfprof.ExecTime> cpu_execs = 5;
inline int ExecProfile::_internal_cpu_execs_size() const {
  return _impl_.cpu_execs_.size();
}
inline int ExecProfile::cpu_execs_size() const {
  return _internal_cpu_execs_size();
}
inline void ExecProfile::clear_cpu_execs() {
  _impl_.cpu_execs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
ExecProfile::_internal_cpu_execs() const {
  return _impl_.cpu_execs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >&
ExecProfile::cpu_execs() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecProfile.cpu_execs)
  return _internal_cpu_execs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
ExecProfile::_internal_mutable_cpu_execs() {
  return _impl_.cpu_execs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::ExecTime >*
ExecProfile::mutable_cpu_execs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecProfile.cpu_execs)
  return _internal_mutable_cpu_execs();
}

// repeated .tensorflow.tfprof.ExecMemory memory_execs = 7;
inline int ExecProfile::_internal_memory_execs_size() const {
  return _impl_.memory_execs_.size();
}
inline int ExecProfile::memory_execs_size() const {
  return _internal_memory_execs_size();
}
inline void ExecProfile::clear_memory_execs() {
  _impl_.memory_execs_.Clear();
}
inline ::tensorflow::tfprof::ExecMemory* ExecProfile::mutable_memory_execs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.memory_execs)
  return _impl_.memory_execs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >*
ExecProfile::mutable_memory_execs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.memory_execs)
  return &_impl_.memory_execs_;
}
inline const ::tensorflow::tfprof::ExecMemory& ExecProfile::_internal_memory_execs(int index) const {
  return _impl_.memory_execs_.Get(index);
}
inline const ::tensorflow::tfprof::ExecMemory& ExecProfile::memory_execs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.memory_execs)
  return _internal_memory_execs(index);
}
inline ::tensorflow::tfprof::ExecMemory* ExecProfile::_internal_add_memory_execs() {
  return _impl_.memory_execs_.Add();
}
inline ::tensorflow::tfprof::ExecMemory* ExecProfile::add_memory_execs() {
  ::tensorflow::tfprof::ExecMemory* _add = _internal_add_memory_execs();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.memory_execs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::ExecMemory >&
ExecProfile::memory_execs() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.memory_execs)
  return _impl_.memory_execs_;
}

// repeated .tensorflow.AllocationRecord allocations = 11;
inline int ExecProfile::_internal_allocations_size() const {
  return _impl_.allocations_.size();
}
inline int ExecProfile::allocations_size() const {
  return _internal_allocations_size();
}
inline ::tensorflow::AllocationRecord* ExecProfile::mutable_allocations(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.allocations)
  return _impl_.allocations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >*
ExecProfile::mutable_allocations() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.allocations)
  return &_impl_.allocations_;
}
inline const ::tensorflow::AllocationRecord& ExecProfile::_internal_allocations(int index) const {
  return _impl_.allocations_.Get(index);
}
inline const ::tensorflow::AllocationRecord& ExecProfile::allocations(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.allocations)
  return _internal_allocations(index);
}
inline ::tensorflow::AllocationRecord* ExecProfile::_internal_add_allocations() {
  return _impl_.allocations_.Add();
}
inline ::tensorflow::AllocationRecord* ExecProfile::add_allocations() {
  ::tensorflow::AllocationRecord* _add = _internal_add_allocations();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.allocations)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AllocationRecord >&
ExecProfile::allocations() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.allocations)
  return _impl_.allocations_;
}

// repeated string devices = 6;
inline int ExecProfile::_internal_devices_size() const {
  return _impl_.devices_.size();
}
inline int ExecProfile::devices_size() const {
  return _internal_devices_size();
}
inline void ExecProfile::clear_devices() {
  _impl_.devices_.Clear();
}
inline std::string* ExecProfile::add_devices() {
  std::string* _s = _internal_add_devices();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.ExecProfile.devices)
  return _s;
}
inline const std::string& ExecProfile::_internal_devices(int index) const {
  return _impl_.devices_.Get(index);
}
inline const std::string& ExecProfile::devices(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecProfile.devices)
  return _internal_devices(index);
}
inline std::string* ExecProfile::mutable_devices(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecProfile.devices)
  return _impl_.devices_.Mutable(index);
}
inline void ExecProfile::set_devices(int index, const std::string& value) {
  _impl_.devices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::set_devices(int index, std::string&& value) {
  _impl_.devices_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::set_devices(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.devices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::set_devices(int index, const char* value, size_t size) {
  _impl_.devices_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.ExecProfile.devices)
}
inline std::string* ExecProfile::_internal_add_devices() {
  return _impl_.devices_.Add();
}
inline void ExecProfile::add_devices(const std::string& value) {
  _impl_.devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::add_devices(std::string&& value) {
  _impl_.devices_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::add_devices(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.ExecProfile.devices)
}
inline void ExecProfile::add_devices(const char* value, size_t size) {
  _impl_.devices_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.ExecProfile.devices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ExecProfile::devices() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecProfile.devices)
  return _impl_.devices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ExecProfile::mutable_devices() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecProfile.devices)
  return &_impl_.devices_;
}

// -------------------------------------------------------------------

// ExecTime

// repeated .tensorflow.tfprof.Tuple times = 1;
inline int ExecTime::_internal_times_size() const {
  return _impl_.times_.size();
}
inline int ExecTime::times_size() const {
  return _internal_times_size();
}
inline void ExecTime::clear_times() {
  _impl_.times_.Clear();
}
inline ::tensorflow::tfprof::Tuple* ExecTime::mutable_times(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.ExecTime.times)
  return _impl_.times_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple >*
ExecTime::mutable_times() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.ExecTime.times)
  return &_impl_.times_;
}
inline const ::tensorflow::tfprof::Tuple& ExecTime::_internal_times(int index) const {
  return _impl_.times_.Get(index);
}
inline const ::tensorflow::tfprof::Tuple& ExecTime::times(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecTime.times)
  return _internal_times(index);
}
inline ::tensorflow::tfprof::Tuple* ExecTime::_internal_add_times() {
  return _impl_.times_.Add();
}
inline ::tensorflow::tfprof::Tuple* ExecTime::add_times() {
  ::tensorflow::tfprof::Tuple* _add = _internal_add_times();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.ExecTime.times)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::Tuple >&
ExecTime::times() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.ExecTime.times)
  return _impl_.times_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ExecMemory

// int64 memory_micros = 1;
inline void ExecMemory::clear_memory_micros() {
  _impl_.memory_micros_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_memory_micros() const {
  return _impl_.memory_micros_;
}
inline int64_t ExecMemory::memory_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.memory_micros)
  return _internal_memory_micros();
}
inline void ExecMemory::_internal_set_memory_micros(int64_t value) {
  
  _impl_.memory_micros_ = value;
}
inline void ExecMemory::set_memory_micros(int64_t value) {
  _internal_set_memory_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.memory_micros)
}

// int64 host_temp_bytes = 2;
inline void ExecMemory::clear_host_temp_bytes() {
  _impl_.host_temp_bytes_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_host_temp_bytes() const {
  return _impl_.host_temp_bytes_;
}
inline int64_t ExecMemory::host_temp_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.host_temp_bytes)
  return _internal_host_temp_bytes();
}
inline void ExecMemory::_internal_set_host_temp_bytes(int64_t value) {
  
  _impl_.host_temp_bytes_ = value;
}
inline void ExecMemory::set_host_temp_bytes(int64_t value) {
  _internal_set_host_temp_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.host_temp_bytes)
}

// int64 host_persistent_bytes = 3;
inline void ExecMemory::clear_host_persistent_bytes() {
  _impl_.host_persistent_bytes_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_host_persistent_bytes() const {
  return _impl_.host_persistent_bytes_;
}
inline int64_t ExecMemory::host_persistent_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.host_persistent_bytes)
  return _internal_host_persistent_bytes();
}
inline void ExecMemory::_internal_set_host_persistent_bytes(int64_t value) {
  
  _impl_.host_persistent_bytes_ = value;
}
inline void ExecMemory::set_host_persistent_bytes(int64_t value) {
  _internal_set_host_persistent_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.host_persistent_bytes)
}

// int64 accelerator_temp_bytes = 4;
inline void ExecMemory::clear_accelerator_temp_bytes() {
  _impl_.accelerator_temp_bytes_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_accelerator_temp_bytes() const {
  return _impl_.accelerator_temp_bytes_;
}
inline int64_t ExecMemory::accelerator_temp_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.accelerator_temp_bytes)
  return _internal_accelerator_temp_bytes();
}
inline void ExecMemory::_internal_set_accelerator_temp_bytes(int64_t value) {
  
  _impl_.accelerator_temp_bytes_ = value;
}
inline void ExecMemory::set_accelerator_temp_bytes(int64_t value) {
  _internal_set_accelerator_temp_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.accelerator_temp_bytes)
}

// int64 accelerator_persistent_bytes = 5;
inline void ExecMemory::clear_accelerator_persistent_bytes() {
  _impl_.accelerator_persistent_bytes_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_accelerator_persistent_bytes() const {
  return _impl_.accelerator_persistent_bytes_;
}
inline int64_t ExecMemory::accelerator_persistent_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.accelerator_persistent_bytes)
  return _internal_accelerator_persistent_bytes();
}
inline void ExecMemory::_internal_set_accelerator_persistent_bytes(int64_t value) {
  
  _impl_.accelerator_persistent_bytes_ = value;
}
inline void ExecMemory::set_accelerator_persistent_bytes(int64_t value) {
  _internal_set_accelerator_persistent_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.accelerator_persistent_bytes)
}

// int64 requested_bytes = 6;
inline void ExecMemory::clear_requested_bytes() {
  _impl_.requested_bytes_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_requested_bytes() const {
  return _impl_.requested_bytes_;
}
inline int64_t ExecMemory::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.requested_bytes)
  return _internal_requested_bytes();
}
inline void ExecMemory::_internal_set_requested_bytes(int64_t value) {
  
  _impl_.requested_bytes_ = value;
}
inline void ExecMemory::set_requested_bytes(int64_t value) {
  _internal_set_requested_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.requested_bytes)
}

// int64 peak_bytes = 7;
inline void ExecMemory::clear_peak_bytes() {
  _impl_.peak_bytes_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_peak_bytes() const {
  return _impl_.peak_bytes_;
}
inline int64_t ExecMemory::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.peak_bytes)
  return _internal_peak_bytes();
}
inline void ExecMemory::_internal_set_peak_bytes(int64_t value) {
  
  _impl_.peak_bytes_ = value;
}
inline void ExecMemory::set_peak_bytes(int64_t value) {
  _internal_set_peak_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.peak_bytes)
}

// int64 residual_bytes = 8;
inline void ExecMemory::clear_residual_bytes() {
  _impl_.residual_bytes_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_residual_bytes() const {
  return _impl_.residual_bytes_;
}
inline int64_t ExecMemory::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.residual_bytes)
  return _internal_residual_bytes();
}
inline void ExecMemory::_internal_set_residual_bytes(int64_t value) {
  
  _impl_.residual_bytes_ = value;
}
inline void ExecMemory::set_residual_bytes(int64_t value) {
  _internal_set_residual_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.residual_bytes)
}

// int64 output_bytes = 9;
inline void ExecMemory::clear_output_bytes() {
  _impl_.output_bytes_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_output_bytes() const {
  return _impl_.output_bytes_;
}
inline int64_t ExecMemory::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.output_bytes)
  return _internal_output_bytes();
}
inline void ExecMemory::_internal_set_output_bytes(int64_t value) {
  
  _impl_.output_bytes_ = value;
}
inline void ExecMemory::set_output_bytes(int64_t value) {
  _internal_set_output_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.output_bytes)
}

// int64 allocator_bytes_in_use = 10;
inline void ExecMemory::clear_allocator_bytes_in_use() {
  _impl_.allocator_bytes_in_use_ = int64_t{0};
}
inline int64_t ExecMemory::_internal_allocator_bytes_in_use() const {
  return _impl_.allocator_bytes_in_use_;
}
inline int64_t ExecMemory::allocator_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.ExecMemory.allocator_bytes_in_use)
  return _internal_allocator_bytes_in_use();
}
inline void ExecMemory::_internal_set_allocator_bytes_in_use(int64_t value) {
  
  _impl_.allocator_bytes_in_use_ = value;
}
inline void ExecMemory::set_allocator_bytes_in_use(int64_t value) {
  _internal_set_allocator_bytes_in_use(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.ExecMemory.allocator_bytes_in_use)
}

// map<int32, .tensorflow.tfprof.Memory> output_memory = 11;
inline int ExecMemory::_internal_output_memory_size() const {
  return _impl_.output_memory_.size();
}
inline int ExecMemory::output_memory_size() const {
  return _internal_output_memory_size();
}
inline void ExecMemory::clear_output_memory() {
  _impl_.output_memory_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Memory >&
ExecMemory::_internal_output_memory() const {
  return _impl_.output_memory_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Memory >&
ExecMemory::output_memory() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.ExecMemory.output_memory)
  return _internal_output_memory();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Memory >*
ExecMemory::_internal_mutable_output_memory() {
  return _impl_.output_memory_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::tfprof::Memory >*
ExecMemory::mutable_output_memory() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.ExecMemory.output_memory)
  return _internal_mutable_output_memory();
}

// -------------------------------------------------------------------

// Tuple

// repeated int64 int64_values = 1;
inline int Tuple::_internal_int64_values_size() const {
  return _impl_.int64_values_.size();
}
inline int Tuple::int64_values_size() const {
  return _internal_int64_values_size();
}
inline void Tuple::clear_int64_values() {
  _impl_.int64_values_.Clear();
}
inline int64_t Tuple::_internal_int64_values(int index) const {
  return _impl_.int64_values_.Get(index);
}
inline int64_t Tuple::int64_values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Tuple.int64_values)
  return _internal_int64_values(index);
}
inline void Tuple::set_int64_values(int index, int64_t value) {
  _impl_.int64_values_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Tuple.int64_values)
}
inline void Tuple::_internal_add_int64_values(int64_t value) {
  _impl_.int64_values_.Add(value);
}
inline void Tuple::add_int64_values(int64_t value) {
  _internal_add_int64_values(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.Tuple.int64_values)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Tuple::_internal_int64_values() const {
  return _impl_.int64_values_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Tuple::int64_values() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.Tuple.int64_values)
  return _internal_int64_values();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Tuple::_internal_mutable_int64_values() {
  return &_impl_.int64_values_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Tuple::mutable_int64_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.Tuple.int64_values)
  return _internal_mutable_int64_values();
}

// -------------------------------------------------------------------

// Memory

// int64 bytes = 1;
inline void Memory::clear_bytes() {
  _impl_.bytes_ = int64_t{0};
}
inline int64_t Memory::_internal_bytes() const {
  return _impl_.bytes_;
}
inline int64_t Memory::bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Memory.bytes)
  return _internal_bytes();
}
inline void Memory::_internal_set_bytes(int64_t value) {
  
  _impl_.bytes_ = value;
}
inline void Memory::set_bytes(int64_t value) {
  _internal_set_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Memory.bytes)
}

// uint64 ptr = 2;
inline void Memory::clear_ptr() {
  _impl_.ptr_ = uint64_t{0u};
}
inline uint64_t Memory::_internal_ptr() const {
  return _impl_.ptr_;
}
inline uint64_t Memory::ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.Memory.ptr)
  return _internal_ptr();
}
inline void Memory::_internal_set_ptr(uint64_t value) {
  
  _impl_.ptr_ = value;
}
inline void Memory::set_ptr(uint64_t value) {
  _internal_set_ptr(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.Memory.ptr)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5flog_2eproto
