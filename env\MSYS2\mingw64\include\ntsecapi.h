/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifdef DEFINE_GUID

#if !defined(INITGUID) || !defined(Audit_System_SecurityStateChange_defined)
DEFINE_GUID(Audit_System_SecurityStateChange, 0x0cce9210, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_System_SecurityStateChange_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_System_SecuritySubsystemExtension_defined)
DEFINE_GUID(Audit_System_SecuritySubsystemExtension, 0x0cce9211, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_System_SecuritySubsystemExtension_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_System_Integrity_defined)
DEFINE_GUID(Audit_System_Integrity, 0x0cce9212, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_System_Integrity_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_System_IPSecDriverEvents_defined)
DEFINE_GUID(Audit_System_IPSecDriverEvents, 0x0cce9213, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_System_IPSecDriverEvents_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_System_Others_defined)
DEFINE_GUID(Audit_System_Others, 0x0cce9214, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_System_Others_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_Logon_defined)
DEFINE_GUID(Audit_Logon_Logon, 0x0cce9215, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_Logon_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_Logoff_defined)
DEFINE_GUID(Audit_Logon_Logoff, 0x0cce9216, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_Logoff_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_AccountLockout_defined)
DEFINE_GUID(Audit_Logon_AccountLockout, 0x0cce9217, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_AccountLockout_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_IPSecMainMode_defined)
DEFINE_GUID(Audit_Logon_IPSecMainMode, 0x0cce9218, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_IPSecMainMode_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_IPSecQuickMode_defined)
DEFINE_GUID(Audit_Logon_IPSecQuickMode, 0x0cce9219, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_IPSecQuickMode_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_IPSecUserMode_defined)
DEFINE_GUID(Audit_Logon_IPSecUserMode, 0x0cce921a, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_IPSecUserMode_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_SpecialLogon_defined)
DEFINE_GUID(Audit_Logon_SpecialLogon, 0x0cce921b, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_SpecialLogon_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_Others_defined)
DEFINE_GUID(Audit_Logon_Others, 0x0cce921c, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_Others_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_FileSystem_defined)
DEFINE_GUID(Audit_ObjectAccess_FileSystem, 0x0cce921d, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_FileSystem_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_Registry_defined)
DEFINE_GUID(Audit_ObjectAccess_Registry, 0x0cce921e, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_Registry_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_Kernel_defined)
DEFINE_GUID(Audit_ObjectAccess_Kernel, 0x0cce921f, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_Kernel_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_Sam_defined)
DEFINE_GUID(Audit_ObjectAccess_Sam, 0x0cce9220, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_Sam_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_CertificationServices_defined)
DEFINE_GUID(Audit_ObjectAccess_CertificationServices, 0x0cce9221, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_CertificationServices_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_ApplicationGenerated_defined)
DEFINE_GUID(Audit_ObjectAccess_ApplicationGenerated, 0x0cce9222, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_ApplicationGenerated_defined
#endif
#endif
#if !defined(INITGUID) || !defined(Audit_ObjectAccess_Handle_defined)
DEFINE_GUID(Audit_ObjectAccess_Handle, 0x0cce9223, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_Handle_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_Share_defined)
DEFINE_GUID(Audit_ObjectAccess_Share, 0x0cce9224, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_Share_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_FirewallPacketDrops_defined)
DEFINE_GUID(Audit_ObjectAccess_FirewallPacketDrops, 0x0cce9225, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_FirewallPacketDrops_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_FirewallConnection_defined)
DEFINE_GUID(Audit_ObjectAccess_FirewallConnection, 0x0cce9226, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_FirewallConnection_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_Other_defined)
DEFINE_GUID(Audit_ObjectAccess_Other, 0x0cce9227, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_Other_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PrivilegeUse_Sensitive_defined)
DEFINE_GUID(Audit_PrivilegeUse_Sensitive, 0x0cce9228, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PrivilegeUse_Sensitive_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PrivilegeUse_NonSensitive_defined)
DEFINE_GUID(Audit_PrivilegeUse_NonSensitive, 0x0cce9229, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PrivilegeUse_NonSensitive_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PrivilegeUse_Others_defined)
DEFINE_GUID(Audit_PrivilegeUse_Others, 0x0cce922a, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PrivilegeUse_Others_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DetailedTracking_ProcessCreation_defined)
DEFINE_GUID(Audit_DetailedTracking_ProcessCreation, 0x0cce922b, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DetailedTracking_ProcessCreation_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DetailedTracking_ProcessTermination_defined)
DEFINE_GUID(Audit_DetailedTracking_ProcessTermination, 0x0cce922c, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DetailedTracking_ProcessTermination_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DetailedTracking_DpapiActivity_defined)
DEFINE_GUID(Audit_DetailedTracking_DpapiActivity, 0x0cce922d, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DetailedTracking_DpapiActivity_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DetailedTracking_RpcCall_defined)
DEFINE_GUID(Audit_DetailedTracking_RpcCall, 0x0cce922e, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DetailedTracking_RpcCall_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PolicyChange_AuditPolicy_defined)
DEFINE_GUID(Audit_PolicyChange_AuditPolicy, 0x0cce922f, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PolicyChange_AuditPolicy_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PolicyChange_AuthenticationPolicy_defined)
DEFINE_GUID(Audit_PolicyChange_AuthenticationPolicy, 0x0cce9230, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PolicyChange_AuthenticationPolicy_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PolicyChange_AuthorizationPolicy_defined)
DEFINE_GUID(Audit_PolicyChange_AuthorizationPolicy, 0x0cce9231, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PolicyChange_AuthorizationPolicy_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PolicyChange_MpsscvRulePolicy_defined)
DEFINE_GUID(Audit_PolicyChange_MpsscvRulePolicy, 0x0cce9232, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PolicyChange_MpsscvRulePolicy_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PolicyChange_WfpIPSecPolicy_defined)
DEFINE_GUID(Audit_PolicyChange_WfpIPSecPolicy, 0x0cce9233, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PolicyChange_WfpIPSecPolicy_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PolicyChange_Others_defined)
DEFINE_GUID(Audit_PolicyChange_Others, 0x0cce9234, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PolicyChange_Others_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountManagement_UserAccount_defined)
DEFINE_GUID(Audit_AccountManagement_UserAccount, 0x0cce9235, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountManagement_UserAccount_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountManagement_ComputerAccount_defined)
DEFINE_GUID(Audit_AccountManagement_ComputerAccount, 0x0cce9236, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountManagement_ComputerAccount_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountManagement_SecurityGroup_defined)
DEFINE_GUID(Audit_AccountManagement_SecurityGroup, 0x0cce9237, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountManagement_SecurityGroup_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountManagement_DistributionGroup_defined)
DEFINE_GUID(Audit_AccountManagement_DistributionGroup, 0x0cce9238, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountManagement_DistributionGroup_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountManagement_ApplicationGroup_defined)
DEFINE_GUID(Audit_AccountManagement_ApplicationGroup, 0x0cce9239, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountManagement_ApplicationGroup_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountManagement_Others_defined)
DEFINE_GUID(Audit_AccountManagement_Others, 0x0cce923a, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountManagement_Others_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DSAccess_DSAccess_defined)
DEFINE_GUID(Audit_DSAccess_DSAccess, 0x0cce923b, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DSAccess_DSAccess_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DsAccess_AdAuditChanges_defined)
DEFINE_GUID(Audit_DsAccess_AdAuditChanges, 0x0cce923c, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DsAccess_AdAuditChanges_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Ds_Replication_defined)
DEFINE_GUID(Audit_Ds_Replication, 0x0cce923d, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Ds_Replication_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Ds_DetailedReplication_defined)
DEFINE_GUID(Audit_Ds_DetailedReplication, 0x0cce923e, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Ds_DetailedReplication_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountLogon_CredentialValidation_defined)
DEFINE_GUID(Audit_AccountLogon_CredentialValidation, 0x0cce923f, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountLogon_CredentialValidation_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountLogon_Kerberos_defined)
DEFINE_GUID(Audit_AccountLogon_Kerberos, 0x0cce9240, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountLogon_Kerberos_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountLogon_Others_defined)
DEFINE_GUID(Audit_AccountLogon_Others, 0x0cce9241, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountLogon_Others_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountLogon_KerbCredentialValidation_defined)
DEFINE_GUID(Audit_AccountLogon_KerbCredentialValidation, 0x0cce9242, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountLogon_KerbCredentialValidation_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_NPS_defined)
DEFINE_GUID(Audit_Logon_NPS, 0x0cce9243, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_NPS_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_DetailedFileShare_defined)
DEFINE_GUID(Audit_ObjectAccess_DetailedFileShare, 0x0cce9244, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_DetailedFileShare_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_RemovableStorage_defined)
DEFINE_GUID(Audit_ObjectAccess_RemovableStorage, 0x0cce9245, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_RemovableStorage_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_CbacStaging_defined)
DEFINE_GUID(Audit_ObjectAccess_CbacStaging, 0x0cce9246, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_CbacStaging_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_Claims_defined)
DEFINE_GUID(Audit_Logon_Claims, 0x0cce9247, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_Claims_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DetailedTracking_PnpActivity_defined)
DEFINE_GUID(Audit_DetailedTracking_PnpActivity, 0x0cce9248, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DetailedTracking_PnpActivity_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_Groups_defined)
DEFINE_GUID(Audit_Logon_Groups, 0x0cce9249, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_Groups_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DetailedTracking_TokenRightAdjusted_defined)
DEFINE_GUID(Audit_DetailedTracking_TokenRightAdjusted, 0x0cce924a, 0x69ae, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DetailedTracking_TokenRightAdjusted_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_System_defined)
DEFINE_GUID(Audit_System, 0x69979848, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_System_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_Logon_defined)
DEFINE_GUID(Audit_Logon, 0x69979849, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_Logon_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_ObjectAccess_defined)
DEFINE_GUID(Audit_ObjectAccess, 0x6997984a, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_ObjectAccess_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PrivilegeUse_defined)
DEFINE_GUID(Audit_PrivilegeUse, 0x6997984b, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PrivilegeUse_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DetailedTracking_defined)
DEFINE_GUID(Audit_DetailedTracking, 0x6997984c, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DetailedTracking_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_PolicyChange_defined)
DEFINE_GUID(Audit_PolicyChange, 0x6997984d, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_PolicyChange_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountManagement_defined)
DEFINE_GUID(Audit_AccountManagement, 0x6997984e, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountManagement_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_DirectoryServiceAccess_defined)
DEFINE_GUID(Audit_DirectoryServiceAccess, 0x6997984f, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_DirectoryServiceAccess_defined
#endif
#endif

#if !defined(INITGUID) || !defined(Audit_AccountLogon_defined)
DEFINE_GUID(Audit_AccountLogon, 0x69979850, 0x797a, 0x11d9, 0xbe, 0xd3, 0x50, 0x50, 0x54, 0x50, 0x30, 0x30);
#ifdef INITGUID
#define Audit_AccountLogon_defined
#endif
#endif
#endif

#ifndef _NTSECAPI_
#define _NTSECAPI_

#ifdef __cplusplus
extern "C" {
#endif

#if !defined (_NTDEF_) && !defined (_NTSTATUS_PSDK)
#define _NTSTATUS_PSDK
  typedef LONG NTSTATUS,*PNTSTATUS;
#endif

#ifndef _NTLSA_IFS_
  typedef ULONG LSA_OPERATIONAL_MODE,*PLSA_OPERATIONAL_MODE;
#endif

#define LSA_MODE_PASSWORD_PROTECTED (__MSABI_LONG(0x00000001))
#define LSA_MODE_INDIVIDUAL_ACCOUNTS (__MSABI_LONG(0x00000002))
#define LSA_MODE_MANDATORY_ACCESS (__MSABI_LONG(0x00000004))
#define LSA_MODE_LOG_FULL (__MSABI_LONG(0x00000008))

#ifndef _NTLSA_IFS_
  typedef enum _SECURITY_LOGON_TYPE {
    UndefinedLogonType = 0,
    Interactive = 2,
    Network,
    Batch,
    Service,
    Proxy,
    Unlock,
    NetworkCleartext,
    NewCredentials
#if _WIN32_WINNT >= 0x0501
    ,RemoteInteractive
    ,CachedInteractive
#endif
#if _WIN32_WINNT >= 0x0502
    ,CachedRemoteInteractive
    ,CachedUnlock
#endif
  } SECURITY_LOGON_TYPE, *PSECURITY_LOGON_TYPE;

#endif

#ifndef _NTLSA_IFS_

#ifndef _NTLSA_AUDIT_
#define _NTLSA_AUDIT_

typedef enum _SE_ADT_PARAMETER_TYPE {
    SeAdtParmTypeNone = 0,
    SeAdtParmTypeString,
    SeAdtParmTypeFileSpec,
    SeAdtParmTypeUlong,
    SeAdtParmTypeSid,
    SeAdtParmTypeLogonId,
    SeAdtParmTypeNoLogonId,
    SeAdtParmTypeAccessMask,
    SeAdtParmTypePrivs,
    SeAdtParmTypeObjectTypes,
    SeAdtParmTypeHexUlong,
    SeAdtParmTypePtr,
    SeAdtParmTypeTime,
    SeAdtParmTypeGuid,
    SeAdtParmTypeLuid,
    SeAdtParmTypeHexInt64,
    SeAdtParmTypeStringList,
    SeAdtParmTypeSidList,
    SeAdtParmTypeDuration,
    SeAdtParmTypeUserAccountControl,
    SeAdtParmTypeNoUac,
    SeAdtParmTypeMessage,
    SeAdtParmTypeDateTime,
    SeAdtParmTypeSockAddr,
    SeAdtParmTypeSD,
    SeAdtParmTypeLogonHours,
    SeAdtParmTypeLogonIdNoSid,
    SeAdtParmTypeUlongNoConv,
    SeAdtParmTypeSockAddrNoPort,
    SeAdtParmTypeAccessReason,
    SeAdtParmTypeStagingReason,
    SeAdtParmTypeResourceAttribute,
    SeAdtParmTypeClaims,
    SeAdtParmTypeLogonIdAsSid,
    SeAdtParmTypeMultiSzString,
    SeAdtParmTypeLogonIdEx
  } SE_ADT_PARAMETER_TYPE, *PSE_ADT_PARAMETER_TYPE;

#include <guiddef.h>

#define SE_ADT_OBJECT_ONLY 0x1

  typedef struct _SE_ADT_OBJECT_TYPE {
    GUID ObjectType;
    USHORT Flags;
    USHORT Level;
    ACCESS_MASK AccessMask;
  } SE_ADT_OBJECT_TYPE,*PSE_ADT_OBJECT_TYPE;

  typedef struct _SE_ADT_PARAMETER_ARRAY_ENTRY {
    SE_ADT_PARAMETER_TYPE Type;
    ULONG Length;
    ULONG_PTR Data[2];
    PVOID Address;
  } SE_ADT_PARAMETER_ARRAY_ENTRY,*PSE_ADT_PARAMETER_ARRAY_ENTRY;

  typedef struct _SE_ADT_ACCESS_REASON {
    ACCESS_MASK AccessMask;
    ULONG AccessReasons[32];
    ULONG ObjectTypeIndex;
    ULONG AccessGranted;
    PSECURITY_DESCRIPTOR SecurityDescriptor;
  } SE_ADT_ACCESS_REASON, *PSE_ADT_ACCESS_REASON;

  typedef struct _SE_ADT_CLAIMS {
    ULONG Length;
    PCLAIMS_BLOB Claims;
  } SE_ADT_CLAIMS, *PSE_ADT_CLAIMS;

#define SE_MAX_AUDIT_PARAMETERS 32
#define SE_MAX_GENERIC_AUDIT_PARAMETERS 28

  typedef struct _SE_ADT_PARAMETER_ARRAY {
    ULONG CategoryId;
    ULONG AuditId;
    ULONG ParameterCount;
    ULONG Length;
    USHORT Type;
    ULONG Flags;
    SE_ADT_PARAMETER_ARRAY_ENTRY Parameters[SE_MAX_AUDIT_PARAMETERS];
  } SE_ADT_PARAMETER_ARRAY,*PSE_ADT_PARAMETER_ARRAY;

  typedef struct _SE_ADT_PARAMETER_ARRAY_EX {
    ULONG CategoryId;
    ULONG AuditId;
    ULONG Version;
    ULONG ParameterCount;
    ULONG Length;
    USHORT FlatSubCategoryId;
    USHORT Type;
    ULONG Flags;
    SE_ADT_PARAMETER_ARRAY_ENTRY Parameters[SE_MAX_AUDIT_PARAMETERS];
  } SE_ADT_PARAMETER_ARRAY_EX, *PSE_ADT_PARAMETER_ARRAY_EX;

#define SE_ADT_PARAMETERS_SELF_RELATIVE 0x00000001
#define SE_ADT_PARAMETERS_SEND_TO_LSA 0x00000002
#define SE_ADT_PARAMETER_EXTENSIBLE_AUDIT 0x00000004
#define SE_ADT_PARAMETER_GENERIC_AUDIT 0x00000008
#define SE_ADT_PARAMETER_WRITE_SYNCHRONOUS 0x00000010

#define LSAP_SE_ADT_PARAMETER_ARRAY_TRUE_SIZE(AuditParameters) (sizeof(SE_ADT_PARAMETER_ARRAY) - sizeof(SE_ADT_PARAMETER_ARRAY_ENTRY) * (SE_MAX_AUDIT_PARAMETERS - AuditParameters->ParameterCount))

#endif /* _NTLSA_AUDIT_ */
#endif /* _NTLSA_IFS_ */

  typedef enum _POLICY_AUDIT_EVENT_TYPE {
    AuditCategorySystem = 0,AuditCategoryLogon,AuditCategoryObjectAccess,AuditCategoryPrivilegeUse,AuditCategoryDetailedTracking,
    AuditCategoryPolicyChange,AuditCategoryAccountManagement,AuditCategoryDirectoryServiceAccess,AuditCategoryAccountLogon
  } POLICY_AUDIT_EVENT_TYPE,*PPOLICY_AUDIT_EVENT_TYPE;

#define POLICY_AUDIT_EVENT_UNCHANGED (__MSABI_LONG(0x00000000))
#define POLICY_AUDIT_EVENT_SUCCESS (__MSABI_LONG(0x00000001))
#define POLICY_AUDIT_EVENT_FAILURE (__MSABI_LONG(0x00000002))
#define POLICY_AUDIT_EVENT_NONE (__MSABI_LONG(0x00000004))
#define POLICY_AUDIT_EVENT_MASK (POLICY_AUDIT_EVENT_SUCCESS | POLICY_AUDIT_EVENT_FAILURE | POLICY_AUDIT_EVENT_UNCHANGED | POLICY_AUDIT_EVENT_NONE)

#ifdef _NTDEF_
  typedef UNICODE_STRING LSA_UNICODE_STRING,*PLSA_UNICODE_STRING;
  typedef STRING LSA_STRING,*PLSA_STRING;
  typedef OBJECT_ATTRIBUTES LSA_OBJECT_ATTRIBUTES,*PLSA_OBJECT_ATTRIBUTES;
#else

#ifndef _NO_W32_PSEUDO_MODIFIERS
#ifndef IN
#define IN
#endif
#ifndef OUT
#define OUT
#endif
#ifndef OPTIONAL
#define OPTIONAL
#endif
#endif

  typedef struct _LSA_UNICODE_STRING {
    USHORT Length;
    USHORT MaximumLength;
    PWSTR Buffer;
  } LSA_UNICODE_STRING,*PLSA_UNICODE_STRING;

  typedef struct _LSA_STRING {
    USHORT Length;
    USHORT MaximumLength;
    PCHAR Buffer;
  } LSA_STRING,*PLSA_STRING;

  typedef struct _LSA_OBJECT_ATTRIBUTES {
    ULONG Length;
    HANDLE RootDirectory;
    PLSA_UNICODE_STRING ObjectName;
    ULONG Attributes;
    PVOID SecurityDescriptor;
    PVOID SecurityQualityOfService;
  } LSA_OBJECT_ATTRIBUTES,*PLSA_OBJECT_ATTRIBUTES;
#endif

#define LSA_SUCCESS(Error) ((LONG)(Error) >= 0)

#ifndef _NTLSA_IFS_
  NTSTATUS NTAPI LsaRegisterLogonProcess(PLSA_STRING LogonProcessName,PHANDLE LsaHandle,PLSA_OPERATIONAL_MODE SecurityMode);
  NTSTATUS NTAPI LsaLogonUser(HANDLE LsaHandle,PLSA_STRING OriginName,SECURITY_LOGON_TYPE LogonType,ULONG AuthenticationPackage,PVOID AuthenticationInformation,ULONG AuthenticationInformationLength,PTOKEN_GROUPS LocalGroups,PTOKEN_SOURCE SourceContext,PVOID *ProfileBuffer,PULONG ProfileBufferLength,PLUID LogonId,PHANDLE Token,PQUOTA_LIMITS Quotas,PNTSTATUS SubStatus);
  NTSTATUS NTAPI LsaLookupAuthenticationPackage(HANDLE LsaHandle,PLSA_STRING PackageName,PULONG AuthenticationPackage);
  NTSTATUS NTAPI LsaFreeReturnBuffer (PVOID Buffer);
  NTSTATUS NTAPI LsaCallAuthenticationPackage(HANDLE LsaHandle,ULONG AuthenticationPackage,PVOID ProtocolSubmitBuffer,ULONG SubmitBufferLength,PVOID *ProtocolReturnBuffer,PULONG ReturnBufferLength,PNTSTATUS ProtocolStatus);
  NTSTATUS NTAPI LsaDeregisterLogonProcess(HANDLE LsaHandle);
  NTSTATUS NTAPI LsaConnectUntrusted(PHANDLE LsaHandle);
  NTSTATUS NTAPI LsaInsertProtectedProcessAddress(PVOID BufferAddress,ULONG BufferSize);
  NTSTATUS NTAPI LsaRemoveProtectedProcessAddress(PVOID BufferAddress,ULONG BufferSize);
#endif

#define POLICY_VIEW_LOCAL_INFORMATION __MSABI_LONG(0x00000001)
#define POLICY_VIEW_AUDIT_INFORMATION __MSABI_LONG(0x00000002)
#define POLICY_GET_PRIVATE_INFORMATION __MSABI_LONG(0x00000004)
#define POLICY_TRUST_ADMIN __MSABI_LONG(0x00000008)
#define POLICY_CREATE_ACCOUNT __MSABI_LONG(0x00000010)
#define POLICY_CREATE_SECRET __MSABI_LONG(0x00000020)
#define POLICY_CREATE_PRIVILEGE __MSABI_LONG(0x00000040)
#define POLICY_SET_DEFAULT_QUOTA_LIMITS __MSABI_LONG(0x00000080)
#define POLICY_SET_AUDIT_REQUIREMENTS __MSABI_LONG(0x00000100)
#define POLICY_AUDIT_LOG_ADMIN __MSABI_LONG(0x00000200)
#define POLICY_SERVER_ADMIN __MSABI_LONG(0x00000400)
#define POLICY_LOOKUP_NAMES __MSABI_LONG(0x00000800)
#define POLICY_NOTIFICATION __MSABI_LONG(0x00001000)

#define POLICY_ALL_ACCESS (STANDARD_RIGHTS_REQUIRED | POLICY_VIEW_LOCAL_INFORMATION | POLICY_VIEW_AUDIT_INFORMATION | POLICY_GET_PRIVATE_INFORMATION | POLICY_TRUST_ADMIN | POLICY_CREATE_ACCOUNT | POLICY_CREATE_SECRET | POLICY_CREATE_PRIVILEGE | POLICY_SET_DEFAULT_QUOTA_LIMITS | POLICY_SET_AUDIT_REQUIREMENTS | POLICY_AUDIT_LOG_ADMIN | POLICY_SERVER_ADMIN | POLICY_LOOKUP_NAMES)
#define POLICY_READ (STANDARD_RIGHTS_READ | POLICY_VIEW_AUDIT_INFORMATION | POLICY_GET_PRIVATE_INFORMATION)
#define POLICY_WRITE (STANDARD_RIGHTS_WRITE | POLICY_TRUST_ADMIN | POLICY_CREATE_ACCOUNT | POLICY_CREATE_SECRET | POLICY_CREATE_PRIVILEGE | POLICY_SET_DEFAULT_QUOTA_LIMITS | POLICY_SET_AUDIT_REQUIREMENTS | POLICY_AUDIT_LOG_ADMIN | POLICY_SERVER_ADMIN)
#define POLICY_EXECUTE (STANDARD_RIGHTS_EXECUTE | POLICY_VIEW_LOCAL_INFORMATION | POLICY_LOOKUP_NAMES)

  typedef struct _LSA_TRUST_INFORMATION {
    LSA_UNICODE_STRING Name;
    PSID Sid;
  } LSA_TRUST_INFORMATION,*PLSA_TRUST_INFORMATION;

  typedef struct _LSA_REFERENCED_DOMAIN_LIST {
    ULONG Entries;
    PLSA_TRUST_INFORMATION Domains;
  } LSA_REFERENCED_DOMAIN_LIST,*PLSA_REFERENCED_DOMAIN_LIST;

  typedef struct _LSA_TRANSLATED_SID {
    SID_NAME_USE Use;
    ULONG RelativeId;
    LONG DomainIndex;
  } LSA_TRANSLATED_SID,*PLSA_TRANSLATED_SID;

  typedef struct _LSA_TRANSLATED_SID2 {
    SID_NAME_USE Use;
    PSID Sid;
    LONG DomainIndex;
    ULONG Flags;
  } LSA_TRANSLATED_SID2,*PLSA_TRANSLATED_SID2;

  typedef struct _LSA_TRANSLATED_NAME {
    SID_NAME_USE Use;
    LSA_UNICODE_STRING Name;
    LONG DomainIndex;
  } LSA_TRANSLATED_NAME,*PLSA_TRANSLATED_NAME;

  typedef enum _POLICY_LSA_SERVER_ROLE {
    PolicyServerRoleBackup = 2,PolicyServerRolePrimary
  } POLICY_LSA_SERVER_ROLE,*PPOLICY_LSA_SERVER_ROLE;

  typedef ULONG POLICY_AUDIT_EVENT_OPTIONS,*PPOLICY_AUDIT_EVENT_OPTIONS;

  typedef enum _POLICY_INFORMATION_CLASS {
    PolicyAuditLogInformation = 1,
    PolicyAuditEventsInformation,
    PolicyPrimaryDomainInformation,
    PolicyPdAccountInformation,
    PolicyAccountDomainInformation,
    PolicyLsaServerRoleInformation,
    PolicyReplicaSourceInformation,
    PolicyDefaultQuotaInformation,
    PolicyModificationInformation,
    PolicyAuditFullSetInformation,
    PolicyAuditFullQueryInformation,
    PolicyDnsDomainInformation,
    PolicyDnsDomainInformationInt,
    PolicyLocalAccountDomainInformation,
    PolicyMachineAccountInformation,
    PolicyMachineAccountInformation2,
    PolicyLastEntry
  } POLICY_INFORMATION_CLASS, *PPOLICY_INFORMATION_CLASS;

  typedef struct _POLICY_AUDIT_LOG_INFO {
    ULONG AuditLogPercentFull;
    ULONG MaximumLogSize;
    LARGE_INTEGER AuditRetentionPeriod;
    BOOLEAN AuditLogFullShutdownInProgress;
    LARGE_INTEGER TimeToShutdown;
    ULONG NextAuditRecordId;
  } POLICY_AUDIT_LOG_INFO,*PPOLICY_AUDIT_LOG_INFO;

  typedef struct _POLICY_AUDIT_EVENTS_INFO {
    BOOLEAN AuditingMode;
    PPOLICY_AUDIT_EVENT_OPTIONS EventAuditingOptions;
    ULONG MaximumAuditEventCount;
  } POLICY_AUDIT_EVENTS_INFO,*PPOLICY_AUDIT_EVENTS_INFO;

  typedef struct _POLICY_AUDIT_SUBCATEGORIES_INFO {
    ULONG MaximumSubCategoryCount;
    PPOLICY_AUDIT_EVENT_OPTIONS EventAuditingOptions;
  } POLICY_AUDIT_SUBCATEGORIES_INFO, *PPOLICY_AUDIT_SUBCATEGORIES_INFO;

  typedef struct _POLICY_AUDIT_CATEGORIES_INFO {
    ULONG MaximumCategoryCount;
    PPOLICY_AUDIT_SUBCATEGORIES_INFO SubCategoriesInfo;
  } POLICY_AUDIT_CATEGORIES_INFO, *PPOLICY_AUDIT_CATEGORIES_INFO;

  typedef struct _POLICY_ACCOUNT_DOMAIN_INFO {
    LSA_UNICODE_STRING DomainName;
    PSID DomainSid;
  } POLICY_ACCOUNT_DOMAIN_INFO,*PPOLICY_ACCOUNT_DOMAIN_INFO;

  typedef struct _POLICY_PRIMARY_DOMAIN_INFO {
    LSA_UNICODE_STRING Name;
    PSID Sid;
  } POLICY_PRIMARY_DOMAIN_INFO,*PPOLICY_PRIMARY_DOMAIN_INFO;

  typedef struct _POLICY_DNS_DOMAIN_INFO {
    LSA_UNICODE_STRING Name;
    LSA_UNICODE_STRING DnsDomainName;
    LSA_UNICODE_STRING DnsForestName;
    GUID DomainGuid;
    PSID Sid;
  } POLICY_DNS_DOMAIN_INFO,*PPOLICY_DNS_DOMAIN_INFO;

  typedef struct _POLICY_PD_ACCOUNT_INFO {
    LSA_UNICODE_STRING Name;
  } POLICY_PD_ACCOUNT_INFO,*PPOLICY_PD_ACCOUNT_INFO;

  typedef struct _POLICY_LSA_SERVER_ROLE_INFO {
    POLICY_LSA_SERVER_ROLE LsaServerRole;
  } POLICY_LSA_SERVER_ROLE_INFO,*PPOLICY_LSA_SERVER_ROLE_INFO;

  typedef struct _POLICY_REPLICA_SOURCE_INFO {
    LSA_UNICODE_STRING ReplicaSource;
    LSA_UNICODE_STRING ReplicaAccountName;
  } POLICY_REPLICA_SOURCE_INFO,*PPOLICY_REPLICA_SOURCE_INFO;

  typedef struct _POLICY_DEFAULT_QUOTA_INFO {
    QUOTA_LIMITS QuotaLimits;
  } POLICY_DEFAULT_QUOTA_INFO,*PPOLICY_DEFAULT_QUOTA_INFO;

  typedef struct _POLICY_MODIFICATION_INFO {
    LARGE_INTEGER ModifiedId;
    LARGE_INTEGER DatabaseCreationTime;
  } POLICY_MODIFICATION_INFO,*PPOLICY_MODIFICATION_INFO;

  typedef struct _POLICY_AUDIT_FULL_SET_INFO {
    BOOLEAN ShutDownOnFull;
  } POLICY_AUDIT_FULL_SET_INFO,*PPOLICY_AUDIT_FULL_SET_INFO;

  typedef struct _POLICY_AUDIT_FULL_QUERY_INFO {
    BOOLEAN ShutDownOnFull;
    BOOLEAN LogIsFull;
  } POLICY_AUDIT_FULL_QUERY_INFO,*PPOLICY_AUDIT_FULL_QUERY_INFO;

  typedef enum _POLICY_DOMAIN_INFORMATION_CLASS {
#if _WIN32_WINNT <= 0x0500
    PolicyDomainQualityOfServiceInformation = 1,
#endif
    PolicyDomainEfsInformation = 2
    ,PolicyDomainKerberosTicketInformation
  } POLICY_DOMAIN_INFORMATION_CLASS, *PPOLICY_DOMAIN_INFORMATION_CLASS;

  typedef struct _POLICY_DOMAIN_EFS_INFO {
    ULONG InfoLength;
    PUCHAR EfsBlob;
  } POLICY_DOMAIN_EFS_INFO,*PPOLICY_DOMAIN_EFS_INFO;

#define POLICY_KERBEROS_VALIDATE_CLIENT 0x00000080

  typedef struct _POLICY_DOMAIN_KERBEROS_TICKET_INFO {
    ULONG AuthenticationOptions;
    LARGE_INTEGER MaxServiceTicketAge;
    LARGE_INTEGER MaxTicketAge;
    LARGE_INTEGER MaxRenewAge;
    LARGE_INTEGER MaxClockSkew;
    LARGE_INTEGER Reserved;
  } POLICY_DOMAIN_KERBEROS_TICKET_INFO,*PPOLICY_DOMAIN_KERBEROS_TICKET_INFO;

  typedef struct _POLICY_MACHINE_ACCT_INFO {
    ULONG Rid;
    PSID Sid;
  } POLICY_MACHINE_ACCT_INFO, *PPOLICY_MACHINE_ACCT_INFO;

  typedef struct _POLICY_MACHINE_ACCT_INFO2 {
    ULONG Rid;
    PSID Sid;
    GUID ObjectGuid;
  } POLICY_MACHINE_ACCT_INFO2, *PPOLICY_MACHINE_ACCT_INFO2;

  typedef enum _POLICY_NOTIFICATION_INFORMATION_CLASS {
    PolicyNotifyAuditEventsInformation = 1,
    PolicyNotifyAccountDomainInformation,
    PolicyNotifyServerRoleInformation,
    PolicyNotifyDnsDomainInformation,
    PolicyNotifyDomainEfsInformation,
    PolicyNotifyDomainKerberosTicketInformation,
    PolicyNotifyMachineAccountPasswordInformation,
    PolicyNotifyGlobalSaclInformation,
    PolicyNotifyMax
  } POLICY_NOTIFICATION_INFORMATION_CLASS, *PPOLICY_NOTIFICATION_INFORMATION_CLASS;

  typedef PVOID LSA_HANDLE,*PLSA_HANDLE;

#define LSAD_AES_CRYPT_SHA512_HASH_SIZE 64
#define LSAD_AES_KEY_SIZE 16
#define LSAD_AES_SALT_SIZE 16
#define LSAD_AES_BLOCK_SIZE 16

  typedef enum _TRUSTED_INFORMATION_CLASS {
    TrustedDomainNameInformation = 1,
    TrustedControllersInformation,
    TrustedPosixOffsetInformation,
    TrustedPasswordInformation,
    TrustedDomainInformationBasic,
    TrustedDomainInformationEx,
    TrustedDomainAuthInformation,
    TrustedDomainFullInformation,
    TrustedDomainAuthInformationInternal,
    TrustedDomainFullInformationInternal,
    TrustedDomainInformationEx2Internal,
    TrustedDomainFullInformation2Internal,
    TrustedDomainSupportedEncryptionTypes,
    TrustedDomainAuthInformationInternalAes,
    TrustedDomainFullInformationInternalAes
  } TRUSTED_INFORMATION_CLASS,*PTRUSTED_INFORMATION_CLASS;

  typedef struct _TRUSTED_DOMAIN_NAME_INFO {
    LSA_UNICODE_STRING Name;
  } TRUSTED_DOMAIN_NAME_INFO,*PTRUSTED_DOMAIN_NAME_INFO;

  typedef struct _TRUSTED_CONTROLLERS_INFO {
    ULONG Entries;
    PLSA_UNICODE_STRING Names;
  } TRUSTED_CONTROLLERS_INFO,*PTRUSTED_CONTROLLERS_INFO;

  typedef struct _TRUSTED_POSIX_OFFSET_INFO {
    ULONG Offset;
  } TRUSTED_POSIX_OFFSET_INFO,*PTRUSTED_POSIX_OFFSET_INFO;

  typedef struct _TRUSTED_PASSWORD_INFO {
    LSA_UNICODE_STRING Password;
    LSA_UNICODE_STRING OldPassword;
  } TRUSTED_PASSWORD_INFO,*PTRUSTED_PASSWORD_INFO;

  typedef LSA_TRUST_INFORMATION TRUSTED_DOMAIN_INFORMATION_BASIC;
  typedef PLSA_TRUST_INFORMATION PTRUSTED_DOMAIN_INFORMATION_BASIC;

#define TRUST_DIRECTION_DISABLED 0x00000000
#define TRUST_DIRECTION_INBOUND 0x00000001
#define TRUST_DIRECTION_OUTBOUND 0x00000002
#define TRUST_DIRECTION_BIDIRECTIONAL (TRUST_DIRECTION_INBOUND | TRUST_DIRECTION_OUTBOUND)

#define TRUST_TYPE_DOWNLEVEL 0x00000001
#define TRUST_TYPE_UPLEVEL 0x00000002
#define TRUST_TYPE_MIT 0x00000003
#define TRUST_TYPE_AAD 0x00000005

#define TRUST_ATTRIBUTE_NON_TRANSITIVE 0x00000001
#define TRUST_ATTRIBUTE_UPLEVEL_ONLY 0x00000002
#define TRUST_ATTRIBUTE_QUARANTINED_DOMAIN 0x00000004
#define TRUST_ATTRIBUTE_FOREST_TRANSITIVE 0x00000008
#define TRUST_ATTRIBUTE_CROSS_ORGANIZATION 0x00000010
#define TRUST_ATTRIBUTE_WITHIN_FOREST 0x00000020
#define TRUST_ATTRIBUTE_TREAT_AS_EXTERNAL 0x00000040

#if _WIN32_WINNT >= 0x0600
#define TRUST_ATTRIBUTE_TRUST_USES_RC4_ENCRYPTION 0x00000080
#define TRUST_ATTRIBUTE_TRUST_USES_AES_KEYS 0x00000100
#endif

#if _WIN32_WINNT >= 0x0602
#define TRUST_ATTRIBUTE_CROSS_ORGANIZATION_NO_TGT_DELEGATION 0x00000200
#define TRUST_ATTRIBUTE_PIM_TRUST 0x00000400
#endif

#if _WIN32_WINNT >= 0x0603
#define TRUST_ATTRIBUTE_CROSS_ORGANIZATION_ENABLE_TGT_DELEGATION 0x00000800
#endif

#define TRUST_ATTRIBUTES_VALID 0xFF03FFFF
#define TRUST_ATTRIBUTES_USER 0xFF000000

  typedef struct _TRUSTED_DOMAIN_INFORMATION_EX {
    LSA_UNICODE_STRING Name;
    LSA_UNICODE_STRING FlatName;
    PSID Sid;
    ULONG TrustDirection;
    ULONG TrustType;
    ULONG TrustAttributes;
  } TRUSTED_DOMAIN_INFORMATION_EX,*PTRUSTED_DOMAIN_INFORMATION_EX;

  typedef struct _TRUSTED_DOMAIN_INFORMATION_EX2 {
    LSA_UNICODE_STRING Name;
    LSA_UNICODE_STRING FlatName;
    PSID Sid;
    ULONG TrustDirection;
    ULONG TrustType;
    ULONG TrustAttributes;
    ULONG ForestTrustLength;
    PUCHAR ForestTrustInfo;
  } TRUSTED_DOMAIN_INFORMATION_EX2,*PTRUSTED_DOMAIN_INFORMATION_EX2;

#define TRUST_AUTH_TYPE_NONE 0
#define TRUST_AUTH_TYPE_NT4OWF 1
#define TRUST_AUTH_TYPE_CLEAR 2
#define TRUST_AUTH_TYPE_VERSION 3

  typedef struct _LSA_AUTH_INFORMATION {
    LARGE_INTEGER LastUpdateTime;
    ULONG AuthType;
    ULONG AuthInfoLength;
    PUCHAR AuthInfo;
  } LSA_AUTH_INFORMATION,*PLSA_AUTH_INFORMATION;

  typedef struct _TRUSTED_DOMAIN_AUTH_INFORMATION {
    ULONG IncomingAuthInfos;
    PLSA_AUTH_INFORMATION IncomingAuthenticationInformation;
    PLSA_AUTH_INFORMATION IncomingPreviousAuthenticationInformation;
    ULONG OutgoingAuthInfos;
    PLSA_AUTH_INFORMATION OutgoingAuthenticationInformation;
    PLSA_AUTH_INFORMATION OutgoingPreviousAuthenticationInformation;
  } TRUSTED_DOMAIN_AUTH_INFORMATION,*PTRUSTED_DOMAIN_AUTH_INFORMATION;

  typedef struct _TRUSTED_DOMAIN_FULL_INFORMATION {
    TRUSTED_DOMAIN_INFORMATION_EX Information;
    TRUSTED_POSIX_OFFSET_INFO PosixOffset;
    TRUSTED_DOMAIN_AUTH_INFORMATION AuthInformation;
  } TRUSTED_DOMAIN_FULL_INFORMATION,*PTRUSTED_DOMAIN_FULL_INFORMATION;

  typedef struct _TRUSTED_DOMAIN_FULL_INFORMATION2 {
    TRUSTED_DOMAIN_INFORMATION_EX2 Information;
    TRUSTED_POSIX_OFFSET_INFO PosixOffset;
    TRUSTED_DOMAIN_AUTH_INFORMATION AuthInformation;
  } TRUSTED_DOMAIN_FULL_INFORMATION2,*PTRUSTED_DOMAIN_FULL_INFORMATION2;

  typedef struct _TRUSTED_DOMAIN_SUPPORTED_ENCRYPTION_TYPES {
    ULONG SupportedEncryptionTypes;
  } TRUSTED_DOMAIN_SUPPORTED_ENCRYPTION_TYPES,*PTRUSTED_DOMAIN_SUPPORTED_ENCRYPTION_TYPES;

  typedef enum {
    ForestTrustTopLevelName,
    ForestTrustTopLevelNameEx,
    ForestTrustDomainInfo,
    ForestTrustBinaryInfo,
    ForestTrustScannerInfo,
    ForestTrustRecordTypeLast = ForestTrustScannerInfo
  } LSA_FOREST_TRUST_RECORD_TYPE;

#define LSA_FTRECORD_DISABLED_REASONS (__MSABI_LONG(0x0000FFFF))

#define LSA_TLN_DISABLED_NEW (__MSABI_LONG(0x00000001))
#define LSA_TLN_DISABLED_ADMIN (__MSABI_LONG(0x00000002))
#define LSA_TLN_DISABLED_CONFLICT (__MSABI_LONG(0x00000004))

#define LSA_SID_DISABLED_ADMIN (__MSABI_LONG(0x00000001))
#define LSA_SID_DISABLED_CONFLICT (__MSABI_LONG(0x00000002))
#define LSA_NB_DISABLED_ADMIN (__MSABI_LONG(0x00000004))
#define LSA_NB_DISABLED_CONFLICT (__MSABI_LONG(0x00000008))

#define LSA_SCANNER_INFO_DISABLE_AUTH_TARGET_VALIDATION (__MSABI_LONG(0x00000001))
#define LSA_SCANNER_INFO_ADMIN_ALL_FLAGS (LSA_SCANNER_INFO_DISABLE_AUTH_TARGET_VALIDATION)

  typedef struct _LSA_FOREST_TRUST_DOMAIN_INFO {
    PSID Sid;
    LSA_UNICODE_STRING DnsName;
    LSA_UNICODE_STRING NetbiosName;
  } LSA_FOREST_TRUST_DOMAIN_INFO,*PLSA_FOREST_TRUST_DOMAIN_INFO;

  typedef struct _LSA_FOREST_TRUST_SCANNER_INFO {
#ifdef __WIDL__
    [unique] PISID DomainSid;
#else
    PSID DomainSid;
#endif
    LSA_UNICODE_STRING DnsName;
    LSA_UNICODE_STRING NetbiosName;
  } LSA_FOREST_TRUST_SCANNER_INFO,*PLSA_FOREST_TRUST_SCANNER_INFO;

#define MAX_FOREST_TRUST_BINARY_DATA_SIZE (128*1024)

  typedef struct _LSA_FOREST_TRUST_BINARY_DATA {
    ULONG Length;
    PUCHAR Buffer;
  } LSA_FOREST_TRUST_BINARY_DATA,*PLSA_FOREST_TRUST_BINARY_DATA;

  typedef struct _LSA_FOREST_TRUST_RECORD {
    ULONG Flags;
    LSA_FOREST_TRUST_RECORD_TYPE ForestTrustType;
    LARGE_INTEGER Time;
    union {
      LSA_UNICODE_STRING TopLevelName;
      LSA_FOREST_TRUST_DOMAIN_INFO DomainInfo;
      LSA_FOREST_TRUST_BINARY_DATA Data;
    } ForestTrustData;
  } LSA_FOREST_TRUST_RECORD,*PLSA_FOREST_TRUST_RECORD;

  typedef struct _LSA_FOREST_TRUST_RECORD2 {
    ULONG Flags;
    LSA_FOREST_TRUST_RECORD_TYPE ForestTrustType;
    LARGE_INTEGER Time;
#ifdef __WIDL__
    [switch_type(LSA_FOREST_TRUST_RECORD_TYPE), switch_is(ForestTrustType)]
#endif
    union {
#ifdef __WIDL__
        [case(ForestTrustTopLevelName, ForestTrustTopLevelNameEx)] LSA_UNICODE_STRING TopLevelName;
        [case(ForestTrustDomainInfo)] LSA_FOREST_TRUST_DOMAIN_INFO DomainInfo;
        [case(ForestTrustBinaryInfo)] LSA_FOREST_TRUST_BINARY_DATA BinaryData;
        [case(ForestTrustScannerInfo)] LSA_FOREST_TRUST_SCANNER_INFO ScannerInfo;
#else
        LSA_UNICODE_STRING TopLevelName;
        LSA_FOREST_TRUST_DOMAIN_INFO DomainInfo;
        LSA_FOREST_TRUST_BINARY_DATA BinaryData;
        LSA_FOREST_TRUST_SCANNER_INFO ScannerInfo;
#endif
    } ForestTrustData;
  } LSA_FOREST_TRUST_RECORD2,*PLSA_FOREST_TRUST_RECORD2;

#define MAX_RECORDS_IN_FOREST_TRUST_INFO 4000

  typedef struct _LSA_FOREST_TRUST_INFORMATION {
    ULONG RecordCount;
    PLSA_FOREST_TRUST_RECORD *Entries;
  } LSA_FOREST_TRUST_INFORMATION,*PLSA_FOREST_TRUST_INFORMATION;

  typedef struct _LSA_FOREST_TRUST_INFORMATION2 {
#ifdef __WIDL__
    [range(0, MAX_RECORDS_IN_FOREST_TRUST_INFO)] ULONG RecordCount;
    [size_is(RecordCount)] PLSA_FOREST_TRUST_RECORD2 *Entries;
#else
    ULONG RecordCount;
    PLSA_FOREST_TRUST_RECORD2 *Entries;
#endif
  } LSA_FOREST_TRUST_INFORMATION2,*PLSA_FOREST_TRUST_INFORMATION2;

  typedef enum {
    CollisionTdo,CollisionXref,CollisionOther
  } LSA_FOREST_TRUST_COLLISION_RECORD_TYPE;

  typedef struct _LSA_FOREST_TRUST_COLLISION_RECORD {
    ULONG Index;
    LSA_FOREST_TRUST_COLLISION_RECORD_TYPE Type;
    ULONG Flags;
    LSA_UNICODE_STRING Name;
  } LSA_FOREST_TRUST_COLLISION_RECORD,*PLSA_FOREST_TRUST_COLLISION_RECORD;

  typedef struct _LSA_FOREST_TRUST_COLLISION_INFORMATION {
    ULONG RecordCount;
    PLSA_FOREST_TRUST_COLLISION_RECORD *Entries;
  } LSA_FOREST_TRUST_COLLISION_INFORMATION,*PLSA_FOREST_TRUST_COLLISION_INFORMATION;

  typedef ULONG LSA_ENUMERATION_HANDLE,*PLSA_ENUMERATION_HANDLE;

  typedef struct _LSA_ENUMERATION_INFORMATION {
    PSID Sid;
  } LSA_ENUMERATION_INFORMATION,*PLSA_ENUMERATION_INFORMATION;

  NTSTATUS NTAPI LsaFreeMemory(PVOID Buffer);
  NTSTATUS NTAPI LsaClose(LSA_HANDLE ObjectHandle);

  #if (_WIN32_WINNT >= 0x0600)
  typedef struct _LSA_LAST_INTER_LOGON_INFO {
    LARGE_INTEGER LastSuccessfulLogon;
    LARGE_INTEGER LastFailedLogon;
    ULONG FailedAttemptCountSinceLastSuccessfulLogon;
  } LSA_LAST_INTER_LOGON_INFO,*PLSA_LAST_INTER_LOGON_INFO;
  #endif

  typedef struct _SECURITY_LOGON_SESSION_DATA {
    ULONG Size;
    LUID LogonId;
    LSA_UNICODE_STRING UserName;
    LSA_UNICODE_STRING LogonDomain;
    LSA_UNICODE_STRING AuthenticationPackage;
    ULONG LogonType;
    ULONG Session;
    PSID Sid;
    LARGE_INTEGER LogonTime;
    LSA_UNICODE_STRING LogonServer;
    LSA_UNICODE_STRING DnsDomainName;
    LSA_UNICODE_STRING Upn;
    #if (_WIN32_WINNT >= 0x0600)
    ULONG UserFlags;
    LSA_LAST_INTER_LOGON_INFO LastLogonInfo;
    LSA_UNICODE_STRING LogonScript;
    LSA_UNICODE_STRING ProfilePath;
    LSA_UNICODE_STRING HomeDirectory;
    LSA_UNICODE_STRING HomeDirectoryDrive;
    LARGE_INTEGER LogoffTime;
    LARGE_INTEGER KickOffTime;
    LARGE_INTEGER PasswordLastSet;
    LARGE_INTEGER PasswordCanChange;
    LARGE_INTEGER PasswordMustChange;
    #endif
  } SECURITY_LOGON_SESSION_DATA,*PSECURITY_LOGON_SESSION_DATA;

  NTSTATUS NTAPI LsaEnumerateLogonSessions(PULONG LogonSessionCount,PLUID *LogonSessionList);
  NTSTATUS NTAPI LsaGetLogonSessionData(PLUID LogonId,PSECURITY_LOGON_SESSION_DATA *ppLogonSessionData);
  NTSTATUS NTAPI LsaOpenPolicy(PLSA_UNICODE_STRING SystemName,PLSA_OBJECT_ATTRIBUTES ObjectAttributes,ACCESS_MASK DesiredAccess,PLSA_HANDLE PolicyHandle);
  NTSTATUS NTAPI LsaQueryInformationPolicy(LSA_HANDLE PolicyHandle,POLICY_INFORMATION_CLASS InformationClass,PVOID *Buffer);
  NTSTATUS NTAPI LsaSetInformationPolicy(LSA_HANDLE PolicyHandle,POLICY_INFORMATION_CLASS InformationClass,PVOID Buffer);
  NTSTATUS NTAPI LsaQueryDomainInformationPolicy(LSA_HANDLE PolicyHandle,POLICY_DOMAIN_INFORMATION_CLASS InformationClass,PVOID *Buffer);
  NTSTATUS NTAPI LsaSetDomainInformationPolicy(LSA_HANDLE PolicyHandle,POLICY_DOMAIN_INFORMATION_CLASS InformationClass,PVOID Buffer);
  NTSTATUS NTAPI LsaRegisterPolicyChangeNotification(POLICY_NOTIFICATION_INFORMATION_CLASS InformationClass,HANDLE NotificationEventHandle);
  NTSTATUS NTAPI LsaUnregisterPolicyChangeNotification(POLICY_NOTIFICATION_INFORMATION_CLASS InformationClass,HANDLE NotificationEventHandle);
  NTSTATUS NTAPI LsaEnumerateTrustedDomains(LSA_HANDLE PolicyHandle,PLSA_ENUMERATION_HANDLE EnumerationContext,PVOID *Buffer,ULONG PreferedMaximumLength,PULONG CountReturned);
  NTSTATUS NTAPI LsaLookupNames(LSA_HANDLE PolicyHandle,ULONG Count,PLSA_UNICODE_STRING Names,PLSA_REFERENCED_DOMAIN_LIST *ReferencedDomains,PLSA_TRANSLATED_SID *Sids);
  NTSTATUS NTAPI LsaLookupNames2(LSA_HANDLE PolicyHandle,ULONG Flags,ULONG Count,PLSA_UNICODE_STRING Names,PLSA_REFERENCED_DOMAIN_LIST *ReferencedDomains,PLSA_TRANSLATED_SID2 *Sids);
  NTSTATUS NTAPI LsaLookupSids(LSA_HANDLE PolicyHandle,ULONG Count,PSID *Sids,PLSA_REFERENCED_DOMAIN_LIST *ReferencedDomains,PLSA_TRANSLATED_NAME *Names);
  NTSTATUS NTAPI LsaLookupSids2(LSA_HANDLE PolicyHandle,ULONG LookupOptions,ULONG Count,PSID *Sids,PLSA_REFERENCED_DOMAIN_LIST *ReferencedDomains,PLSA_TRANSLATED_NAME *Names);
  NTSTATUS NTAPI LsaSetCAPs(PLSA_UNICODE_STRING CAPDNs,ULONG CAPDNCount,ULONG Flags);
  NTSTATUS NTAPI LsaGetAppliedCAPIDs(PLSA_UNICODE_STRING SystemName,PSID **CAPIDs,PULONG CAPIDCount);

  #define MAXIMUM_CAPES_PER_CAP 0x7f

  #define CENTRAL_ACCESS_POLICY_OWNER_RIGHTS_PRESENT_FLAG 0x00000001
  #define CENTRAL_ACCESS_POLICY_STAGED_OWNER_RIGHTS_PRESENT_FLAG 0x00000100
  #define CENTRAL_ACCESS_POLICY_STAGED_FLAG 0x00010000
  #define STAGING_FLAG(Effective) ((Effective & 0xf) << 8)

  #define CENTRAL_ACCESS_POLICY_VALID_FLAG_MASK (CENTRAL_ACCESS_POLICY_OWNER_RIGHTS_PRESENT_FLAG | \
                                                 CENTRAL_ACCESS_POLICY_STAGED_OWNER_RIGHTS_PRESENT_FLAG  | \
                                                 CENTRAL_ACCESS_POLICY_STAGED_FLAG)

  #define LSASETCAPS_RELOAD_FLAG 0x00000001
  #define LSASETCAPS_VALID_FLAG_MASK LSASETCAPS_RELOAD_FLAG

  typedef struct _CENTRAL_ACCESS_POLICY_ENTRY {
    LSA_UNICODE_STRING Name;
    LSA_UNICODE_STRING Description;
    LSA_UNICODE_STRING ChangeId;
    ULONG LengthAppliesTo;
    PUCHAR AppliesTo;
    ULONG LengthSD;
    PSECURITY_DESCRIPTOR SD;
    ULONG LengthStagedSD;
    PSECURITY_DESCRIPTOR StagedSD;
    ULONG Flags;
  } CENTRAL_ACCESS_POLICY_ENTRY, *PCENTRAL_ACCESS_POLICY_ENTRY;

  typedef const CENTRAL_ACCESS_POLICY_ENTRY *PCCENTRAL_ACCESS_POLICY_ENTRY;

  typedef struct _CENTRAL_ACCESS_POLICY {
    PSID CAPID;
    LSA_UNICODE_STRING Name;
    LSA_UNICODE_STRING Description;
    LSA_UNICODE_STRING ChangeId;
    ULONG Flags;
    ULONG CAPECount;
    PCENTRAL_ACCESS_POLICY_ENTRY *CAPEs;
  } CENTRAL_ACCESS_POLICY, *PCENTRAL_ACCESS_POLICY;

  typedef const CENTRAL_ACCESS_POLICY *PCCENTRAL_ACCESS_POLICY;

  NTSTATUS NTAPI LsaQueryCAPs(PSID *CAPIDs,ULONG CAPIDCount,PCENTRAL_ACCESS_POLICY *CAPs,PULONG CAPCount);

#define SE_INTERACTIVE_LOGON_NAME TEXT("SeInteractiveLogonRight")
#define SE_NETWORK_LOGON_NAME TEXT("SeNetworkLogonRight")
#define SE_BATCH_LOGON_NAME TEXT("SeBatchLogonRight")
#define SE_SERVICE_LOGON_NAME TEXT("SeServiceLogonRight")
#define SE_DENY_INTERACTIVE_LOGON_NAME TEXT("SeDenyInteractiveLogonRight")
#define SE_DENY_NETWORK_LOGON_NAME TEXT("SeDenyNetworkLogonRight")
#define SE_DENY_BATCH_LOGON_NAME TEXT("SeDenyBatchLogonRight")
#define SE_DENY_SERVICE_LOGON_NAME TEXT("SeDenyServiceLogonRight")
#define SE_REMOTE_INTERACTIVE_LOGON_NAME TEXT("SeRemoteInteractiveLogonRight")
#define SE_DENY_REMOTE_INTERACTIVE_LOGON_NAME TEXT("SeDenyRemoteInteractiveLogonRight")

  NTSTATUS NTAPI LsaEnumerateAccountsWithUserRight(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING UserRight,PVOID *Buffer,PULONG CountReturned);
  NTSTATUS NTAPI LsaEnumerateAccountRights(LSA_HANDLE PolicyHandle,PSID AccountSid,PLSA_UNICODE_STRING *UserRights,PULONG CountOfRights);
  NTSTATUS NTAPI LsaAddAccountRights(LSA_HANDLE PolicyHandle,PSID AccountSid,PLSA_UNICODE_STRING UserRights,ULONG CountOfRights);
  NTSTATUS NTAPI LsaRemoveAccountRights(LSA_HANDLE PolicyHandle,PSID AccountSid,BOOLEAN AllRights,PLSA_UNICODE_STRING UserRights,ULONG CountOfRights);
  NTSTATUS NTAPI LsaOpenTrustedDomainByName(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,ACCESS_MASK DesiredAccess,PLSA_HANDLE TrustedDomainHandle);
  NTSTATUS NTAPI LsaQueryTrustedDomainInfo(LSA_HANDLE PolicyHandle,PSID TrustedDomainSid,TRUSTED_INFORMATION_CLASS InformationClass,PVOID *Buffer);
  NTSTATUS NTAPI LsaSetTrustedDomainInformation(LSA_HANDLE PolicyHandle,PSID TrustedDomainSid,TRUSTED_INFORMATION_CLASS InformationClass,PVOID Buffer);
  NTSTATUS NTAPI LsaDeleteTrustedDomain(LSA_HANDLE PolicyHandle,PSID TrustedDomainSid);
  NTSTATUS NTAPI LsaQueryTrustedDomainInfoByName(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,TRUSTED_INFORMATION_CLASS InformationClass,PVOID *Buffer);
  NTSTATUS NTAPI LsaSetTrustedDomainInfoByName(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,TRUSTED_INFORMATION_CLASS InformationClass,PVOID Buffer);
  NTSTATUS NTAPI LsaEnumerateTrustedDomainsEx(LSA_HANDLE PolicyHandle,PLSA_ENUMERATION_HANDLE EnumerationContext,PVOID *Buffer,ULONG PreferedMaximumLength,PULONG CountReturned);
  NTSTATUS NTAPI LsaCreateTrustedDomainEx(LSA_HANDLE PolicyHandle,PTRUSTED_DOMAIN_INFORMATION_EX TrustedDomainInformation,PTRUSTED_DOMAIN_AUTH_INFORMATION AuthenticationInformation,ACCESS_MASK DesiredAccess,PLSA_HANDLE TrustedDomainHandle);
  NTSTATUS NTAPI LsaQueryForestTrustInformation(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,PLSA_FOREST_TRUST_INFORMATION *ForestTrustInfo);
  NTSTATUS NTAPI LsaSetForestTrustInformation(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,PLSA_FOREST_TRUST_INFORMATION ForestTrustInfo,BOOLEAN CheckOnly,PLSA_FOREST_TRUST_COLLISION_INFORMATION *CollisionInfo);

#ifdef TESTING_MATCHING_ROUTINE
  NTSTATUS NTAPI LsaForestTrustFindMatch(LSA_HANDLE PolicyHandle,ULONG Type,PLSA_UNICODE_STRING Name,PLSA_UNICODE_STRING *Match);
#endif

  NTSTATUS NTAPI LsaStorePrivateData(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING KeyName,PLSA_UNICODE_STRING PrivateData);
  NTSTATUS NTAPI LsaRetrievePrivateData(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING KeyName,PLSA_UNICODE_STRING *PrivateData);
  ULONG NTAPI LsaNtStatusToWinError(NTSTATUS Status);
  NTSTATUS NTAPI LsaQueryForestTrustInformation2(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,LSA_FOREST_TRUST_RECORD_TYPE HighestRecordType,PLSA_FOREST_TRUST_INFORMATION2 *ForestTrustInfo);
  NTSTATUS NTAPI LsaSetForestTrustInformation2(LSA_HANDLE PolicyHandle,PLSA_UNICODE_STRING TrustedDomainName,LSA_FOREST_TRUST_RECORD_TYPE HighestRecordType,PLSA_FOREST_TRUST_INFORMATION2 ForestTrustInfo,BOOLEAN CheckOnly,PLSA_FOREST_TRUST_COLLISION_INFORMATION *CollisionInfo);

#ifndef _NTLSA_IFS_
#define _NTLSA_IFS_
#endif

  enum NEGOTIATE_MESSAGES {
    NegEnumPackagePrefixes = 0,
    NegGetCallerName = 1,
    NegTransferCredentials = 2,
    NegMsgReserved1 = 3,
    NegCallPackageMax
  };

#define NEGOTIATE_MAX_PREFIX 32

  typedef struct _NEGOTIATE_PACKAGE_PREFIX {
    ULONG_PTR PackageId;
    PVOID PackageDataA;
    PVOID PackageDataW;
    ULONG_PTR PrefixLen;
    UCHAR Prefix[NEGOTIATE_MAX_PREFIX ];
  } NEGOTIATE_PACKAGE_PREFIX,*PNEGOTIATE_PACKAGE_PREFIX;

  typedef struct _NEGOTIATE_PACKAGE_PREFIXES {
    ULONG MessageType;
    ULONG PrefixCount;
    ULONG Offset;
    ULONG Pad;
  } NEGOTIATE_PACKAGE_PREFIXES,*PNEGOTIATE_PACKAGE_PREFIXES;

  typedef struct _NEGOTIATE_CALLER_NAME_REQUEST {
    ULONG MessageType;
    LUID LogonId;
  } NEGOTIATE_CALLER_NAME_REQUEST,*PNEGOTIATE_CALLER_NAME_REQUEST;

  typedef struct _NEGOTIATE_CALLER_NAME_RESPONSE {
    ULONG MessageType;
    PWSTR CallerName;
  } NEGOTIATE_CALLER_NAME_RESPONSE,*PNEGOTIATE_CALLER_NAME_RESPONSE;

#ifndef _NTDEF_
#ifndef __UNICODE_STRING_DEFINED
#define __UNICODE_STRING_DEFINED
  typedef LSA_UNICODE_STRING UNICODE_STRING,*PUNICODE_STRING;
#endif
#ifndef __STRING_DEFINED
#define __STRING_DEFINED
  typedef LSA_STRING STRING,*PSTRING;
#endif
#endif

#ifndef _DOMAIN_PASSWORD_INFORMATION_DEFINED
#define _DOMAIN_PASSWORD_INFORMATION_DEFINED
  typedef struct _DOMAIN_PASSWORD_INFORMATION {
    USHORT MinPasswordLength;
    USHORT PasswordHistoryLength;
    ULONG PasswordProperties;
    LARGE_INTEGER MaxPasswordAge;
    LARGE_INTEGER MinPasswordAge;
  } DOMAIN_PASSWORD_INFORMATION,*PDOMAIN_PASSWORD_INFORMATION;
#endif

#define DOMAIN_PASSWORD_COMPLEX __MSABI_LONG(0x00000001)
#define DOMAIN_PASSWORD_NO_ANON_CHANGE __MSABI_LONG(0x00000002)
#define DOMAIN_PASSWORD_NO_CLEAR_CHANGE __MSABI_LONG(0x00000004)
#define DOMAIN_LOCKOUT_ADMINS __MSABI_LONG(0x00000008)
#define DOMAIN_PASSWORD_STORE_CLEARTEXT __MSABI_LONG(0x00000010)
#define DOMAIN_REFUSE_PASSWORD_CHANGE __MSABI_LONG(0x00000020)

#if _WIN32_WINNT >= 0x0502
#define DOMAIN_NO_LM_OWF_CHANGE __MSABI_LONG(0x00000040)
#endif

#ifndef _PASSWORD_NOTIFICATION_DEFINED
#define _PASSWORD_NOTIFICATION_DEFINED
  typedef NTSTATUS (*PSAM_PASSWORD_NOTIFICATION_ROUTINE)(PUNICODE_STRING UserName,ULONG RelativeId,PUNICODE_STRING NewPassword);

#define SAM_PASSWORD_CHANGE_NOTIFY_ROUTINE "PasswordChangeNotify"

  typedef BOOLEAN (*PSAM_INIT_NOTIFICATION_ROUTINE)();

#define SAM_INIT_NOTIFICATION_ROUTINE "InitializeChangeNotify"
#define SAM_PASSWORD_FILTER_ROUTINE "PasswordFilter"

  typedef BOOLEAN (*PSAM_PASSWORD_FILTER_ROUTINE)(PUNICODE_STRING AccountName,PUNICODE_STRING FullName,PUNICODE_STRING Password,BOOLEAN SetOperation);
#endif

#define MSV1_0_PACKAGE_NAME "MICROSOFT_AUTHENTICATION_PACKAGE_V1_0"
#define MSV1_0_PACKAGE_NAMEW L"MICROSOFT_AUTHENTICATION_PACKAGE_V1_0"
#define MSV1_0_PACKAGE_NAMEW_LENGTH sizeof(MSV1_0_PACKAGE_NAMEW) - sizeof(WCHAR)

#define MSV1_0_SUBAUTHENTICATION_KEY "SYSTEM\\CurrentControlSet\\Control\\Lsa\\MSV1_0"
#define MSV1_0_SUBAUTHENTICATION_VALUE "Auth"

  typedef enum _MSV1_0_LOGON_SUBMIT_TYPE {
    MsV1_0InteractiveLogon = 2,
    MsV1_0Lm20Logon,
    MsV1_0NetworkLogon,
    MsV1_0SubAuthLogon,
    MsV1_0WorkstationUnlockLogon = 7,
    MsV1_0S4ULogon = 12,
    MsV1_0VirtualLogon = 82,
    MsV1_0NoElevationLogon,
    MsV1_0LuidLogon
  } MSV1_0_LOGON_SUBMIT_TYPE,*PMSV1_0_LOGON_SUBMIT_TYPE;

  typedef enum _MSV1_0_PROFILE_BUFFER_TYPE {
    MsV1_0InteractiveProfile = 2,MsV1_0Lm20LogonProfile,MsV1_0SmartCardProfile
  } MSV1_0_PROFILE_BUFFER_TYPE,*PMSV1_0_PROFILE_BUFFER_TYPE;

  typedef struct _MSV1_0_INTERACTIVE_LOGON {
    MSV1_0_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING LogonDomainName;
    UNICODE_STRING UserName;
    UNICODE_STRING Password;
  } MSV1_0_INTERACTIVE_LOGON,*PMSV1_0_INTERACTIVE_LOGON;

  typedef struct _MSV1_0_INTERACTIVE_PROFILE {
    MSV1_0_PROFILE_BUFFER_TYPE MessageType;
    USHORT LogonCount;
    USHORT BadPasswordCount;
    LARGE_INTEGER LogonTime;
    LARGE_INTEGER LogoffTime;
    LARGE_INTEGER KickOffTime;
    LARGE_INTEGER PasswordLastSet;
    LARGE_INTEGER PasswordCanChange;
    LARGE_INTEGER PasswordMustChange;
    UNICODE_STRING LogonScript;
    UNICODE_STRING HomeDirectory;
    UNICODE_STRING FullName;
    UNICODE_STRING ProfilePath;
    UNICODE_STRING HomeDirectoryDrive;
    UNICODE_STRING LogonServer;
    ULONG UserFlags;
  } MSV1_0_INTERACTIVE_PROFILE,*PMSV1_0_INTERACTIVE_PROFILE;

#define MSV1_0_CHALLENGE_LENGTH 8
#define MSV1_0_USER_SESSION_KEY_LENGTH 16
#define MSV1_0_LANMAN_SESSION_KEY_LENGTH 8

#define MSV1_0_CLEARTEXT_PASSWORD_ALLOWED 0x02
#define MSV1_0_UPDATE_LOGON_STATISTICS 0x04
#define MSV1_0_RETURN_USER_PARAMETERS 0x08
#define MSV1_0_DONT_TRY_GUEST_ACCOUNT 0x10
#define MSV1_0_ALLOW_SERVER_TRUST_ACCOUNT 0x20
#define MSV1_0_RETURN_PASSWORD_EXPIRY 0x40

#define MSV1_0_USE_CLIENT_CHALLENGE 0x80
#define MSV1_0_TRY_GUEST_ACCOUNT_ONLY 0x100
#define MSV1_0_RETURN_PROFILE_PATH 0x200
#define MSV1_0_TRY_SPECIFIED_DOMAIN_ONLY 0x400
#define MSV1_0_ALLOW_WORKSTATION_TRUST_ACCOUNT 0x800
#define MSV1_0_DISABLE_PERSONAL_FALLBACK 0x00001000
#define MSV1_0_ALLOW_FORCE_GUEST 0x00002000
#define MSV1_0_CLEARTEXT_PASSWORD_SUPPLIED 0x00004000
#define MSV1_0_USE_DOMAIN_FOR_ROUTING_ONLY 0x00008000
#define MSV1_0_SUBAUTHENTICATION_DLL_EX 0x00100000
#define MSV1_0_ALLOW_MSVCHAPV2 0x00010000

#if _WIN32_WINNT >= 0x0600
#define MSV1_0_S4U2SELF 0x00020000
#define MSV1_0_CHECK_LOGONHOURS_FOR_S4U 0x00040000
#endif

#if _WIN32_WINNT >= 0x0602
#define MSV1_0_INTERNET_DOMAIN 0x00080000
#endif

#define MSV1_0_SUBAUTHENTICATION_DLL 0xFF000000
#define MSV1_0_SUBAUTHENTICATION_DLL_SHIFT 24
#define MSV1_0_MNS_LOGON 0x01000000

#define MSV1_0_SUBAUTHENTICATION_DLL_RAS 2
#define MSV1_0_SUBAUTHENTICATION_DLL_IIS 132

  typedef struct _MSV1_0_LM20_LOGON {
    MSV1_0_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING LogonDomainName;
    UNICODE_STRING UserName;
    UNICODE_STRING Workstation;
    UCHAR ChallengeToClient[MSV1_0_CHALLENGE_LENGTH];
    STRING CaseSensitiveChallengeResponse;
    STRING CaseInsensitiveChallengeResponse;
    ULONG ParameterControl;
  } MSV1_0_LM20_LOGON,*PMSV1_0_LM20_LOGON;

  typedef struct _MSV1_0_SUBAUTH_LOGON{
    MSV1_0_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING LogonDomainName;
    UNICODE_STRING UserName;
    UNICODE_STRING Workstation;
    UCHAR ChallengeToClient[MSV1_0_CHALLENGE_LENGTH];
    STRING AuthenticationInfo1;
    STRING AuthenticationInfo2;
    ULONG ParameterControl;
    ULONG SubAuthPackageId;
  } MSV1_0_SUBAUTH_LOGON,*PMSV1_0_SUBAUTH_LOGON;

#if _WIN32_WINNT >= 0x0600

#define MSV1_0_S4U_LOGON_FLAG_CHECK_LOGONHOURS 0x2

  typedef struct _MSV1_0_S4U_LOGON {
    MSV1_0_LOGON_SUBMIT_TYPE MessageType;
    ULONG Flags;
    UNICODE_STRING UserPrincipalName;
    UNICODE_STRING DomainName;
  } MSV1_0_S4U_LOGON, *PMSV1_0_S4U_LOGON;

#endif

#define LOGON_GUEST 0x01
#define LOGON_NOENCRYPTION 0x02
#define LOGON_CACHED_ACCOUNT 0x04
#define LOGON_USED_LM_PASSWORD 0x08
#define LOGON_EXTRA_SIDS 0x20
#define LOGON_SUBAUTH_SESSION_KEY 0x40
#define LOGON_SERVER_TRUST_ACCOUNT 0x80
#define LOGON_NTLMV2_ENABLED 0x100
#define LOGON_RESOURCE_GROUPS 0x200
#define LOGON_PROFILE_PATH_RETURNED 0x400
#define LOGON_NT_V2 0x800
#define LOGON_LM_V2 0x1000
#define LOGON_NTLM_V2 0x2000

#if _WIN32_WINNT >= 0x0600
#define LOGON_OPTIMIZED 0x4000
#define LOGON_WINLOGON 0x8000
#define LOGON_PKINIT 0x10000
#define LOGON_NO_OPTIMIZED 0x20000
#endif

#if _WIN32_WINNT >= 0x0602
#define LOGON_NO_ELEVATION 0x40000
#define LOGON_MANAGED_SERVICE 0x80000
#endif

#define MSV1_0_SUBAUTHENTICATION_FLAGS 0xFF000000

#define LOGON_GRACE_LOGON 0x01000000

  typedef struct _MSV1_0_LM20_LOGON_PROFILE {
    MSV1_0_PROFILE_BUFFER_TYPE MessageType;
    LARGE_INTEGER KickOffTime;
    LARGE_INTEGER LogoffTime;
    ULONG UserFlags;
    UCHAR UserSessionKey[MSV1_0_USER_SESSION_KEY_LENGTH];
    UNICODE_STRING LogonDomainName;
    UCHAR LanmanSessionKey[MSV1_0_LANMAN_SESSION_KEY_LENGTH];
    UNICODE_STRING LogonServer;
    UNICODE_STRING UserParameters;
  } MSV1_0_LM20_LOGON_PROFILE,*PMSV1_0_LM20_LOGON_PROFILE;

#define MSV1_0_OWF_PASSWORD_LENGTH 16
#define MSV1_0_SHA_PASSWORD_LENGTH 20
#define MSV1_0_CREDENTIAL_KEY_LENGTH 20
#define MSV1_0_CRED_LM_PRESENT 0x1
#define MSV1_0_CRED_NT_PRESENT 0x2
#define MSV1_0_CRED_REMOVED 0x4
#define MSV1_0_CRED_CREDKEY_PRESENT 0x8
#define MSV1_0_CRED_SHA_PRESENT 0x10

#define MSV1_0_CRED_VERSION 0
#define MSV1_0_CRED_VERSION_V2 2
#define MSV1_0_CRED_VERSION_V3 4
#define MSV1_0_CRED_VERSION_IUM 0xffff0001
#define MSV1_0_CRED_VERSION_REMOTE 0xffff0002
#define MSV1_0_CRED_VERSION_ARSO 0xffff0003
#define MSV1_0_CRED_VERSION_RESERVED_1 0xfffffffe
#define MSV1_0_CRED_VERSION_INVALID 0xffffffff

  typedef enum _MSV1_0_CREDENTIAL_KEY_TYPE {
    InvalidCredKey,
    DeprecatedIUMCredKey,
    DomainUserCredKey,
    LocalUserCredKey,
    ExternallySuppliedCredKey
  } MSV1_0_CREDENTIAL_KEY_TYPE;

  typedef struct _MSV1_0_CREDENTIAL_KEY {
    UCHAR Data[MSV1_0_CREDENTIAL_KEY_LENGTH];
  } MSV1_0_CREDENTIAL_KEY, *PMSV1_0_CREDENTIAL_KEY;

  typedef struct _MSV1_0_SUPPLEMENTAL_CREDENTIAL {
    ULONG Version;
    ULONG Flags;
    UCHAR LmPassword[MSV1_0_OWF_PASSWORD_LENGTH];
    UCHAR NtPassword[MSV1_0_OWF_PASSWORD_LENGTH];
  } MSV1_0_SUPPLEMENTAL_CREDENTIAL,*PMSV1_0_SUPPLEMENTAL_CREDENTIAL;

  typedef struct _MSV1_0_SUPPLEMENTAL_CREDENTIAL_V2 {
    ULONG Version;
    ULONG Flags;
    UCHAR NtPassword[MSV1_0_OWF_PASSWORD_LENGTH];
    MSV1_0_CREDENTIAL_KEY CredentialKey;
  } MSV1_0_SUPPLEMENTAL_CREDENTIAL_V2, *PMSV1_0_SUPPLEMENTAL_CREDENTIAL_V2;

  typedef struct _MSV1_0_SUPPLEMENTAL_CREDENTIAL_V3 {
    ULONG Version;
    ULONG Flags;
    MSV1_0_CREDENTIAL_KEY_TYPE CredentialKeyType;
    UCHAR NtPassword[MSV1_0_OWF_PASSWORD_LENGTH];
    MSV1_0_CREDENTIAL_KEY CredentialKey;
    UCHAR ShaPassword[MSV1_0_SHA_PASSWORD_LENGTH];
  } MSV1_0_SUPPLEMENTAL_CREDENTIAL_V3, *PMSV1_0_SUPPLEMENTAL_CREDENTIAL_V3;

  typedef struct _MSV1_0_IUM_SUPPLEMENTAL_CREDENTIAL {
    ULONG Version;
    ULONG EncryptedCredsSize;
    UCHAR EncryptedCreds[1];
  } MSV1_0_IUM_SUPPLEMENTAL_CREDENTIAL, *PMSV1_0_IUM_SUPPLEMENTAL_CREDENTIAL;

#define MSV1_0_IUM_SUPPLEMENTAL_CREDENTIAL_SIZE(Creds) \
  (FIELD_OFFSET(MSV1_0_IUM_SUPPLEMENTAL_CREDENTIAL, EncryptedCreds) + (Creds)->EncryptedCredsSize)

  typedef struct _MSV1_0_REMOTE_SUPPLEMENTAL_CREDENTIAL {
    ULONG Version;
    ULONG Flags;
    MSV1_0_CREDENTIAL_KEY CredentialKey;
    MSV1_0_CREDENTIAL_KEY_TYPE CredentialKeyType;
    ULONG EncryptedCredsSize;
    UCHAR EncryptedCreds[1];
  } MSV1_0_REMOTE_SUPPLEMENTAL_CREDENTIAL, *PMSV1_0_REMOTE_SUPPLEMENTAL_CREDENTIAL;

#define MSV1_0_NTLM3_RESPONSE_LENGTH 16
#define MSV1_0_NTLM3_OWF_LENGTH 16

#define MSV1_0_MAX_NTLM3_LIFE 129600
#define MSV1_0_MAX_AVL_SIZE 64000

#define MSV1_0_AV_FLAG_FORCE_GUEST 0x00000001

#if _WIN32_WINNT >= 0x0600
#define MSV1_0_AV_FLAG_MIC_HANDSHAKE_MESSAGES 0x00000002
#endif

#if _WIN32_WINNT >= 0x0601
#define MSV1_0_AV_FLAG_UNVERIFIED_TARGET 0x00000004
#endif

  typedef struct _MSV1_0_NTLM3_RESPONSE {
    UCHAR Response[MSV1_0_NTLM3_RESPONSE_LENGTH];
    UCHAR RespType;
    UCHAR HiRespType;
    USHORT Flags;
    ULONG MsgWord;
    ULONGLONG TimeStamp;
    UCHAR ChallengeFromClient[MSV1_0_CHALLENGE_LENGTH];
    ULONG AvPairsOff;
    UCHAR Buffer[1];
  } MSV1_0_NTLM3_RESPONSE,*PMSV1_0_NTLM3_RESPONSE;

#define MSV1_0_NTLM3_INPUT_LENGTH (sizeof(MSV1_0_NTLM3_RESPONSE) - MSV1_0_NTLM3_RESPONSE_LENGTH)
#define MSV1_0_NTLM3_MIN_NT_RESPONSE_LENGTH RTL_SIZEOF_THROUGH_FIELD(MSV1_0_NTLM3_RESPONSE,AvPairsOff)

/* MsvAvSingleHost present in MS-NLMP specifications but not in WinSDK */
  typedef enum {
    MsvAvEOL,
    MsvAvNbComputerName,
    MsvAvNbDomainName,
    MsvAvDnsComputerName,
    MsvAvDnsDomainName
#if _WIN32_WINNT >= 0x0501
    ,MsvAvDnsTreeName
    ,MsvAvFlags
#if _WIN32_WINNT >= 0x0600
    ,MsvAvTimestamp
    ,MsvAvRestrictions
    ,MsvAvSingleHost = MsvAvRestrictions
    ,MsvAvTargetName
    ,MsvAvChannelBindings
#endif
#endif
  } MSV1_0_AVID;

  typedef struct _MSV1_0_AV_PAIR {
    USHORT AvId;
    USHORT AvLen;
  } MSV1_0_AV_PAIR,*PMSV1_0_AV_PAIR;

  typedef enum _MSV1_0_PROTOCOL_MESSAGE_TYPE {
    MsV1_0Lm20ChallengeRequest = 0,
    MsV1_0Lm20GetChallengeResponse,
    MsV1_0EnumerateUsers,
    MsV1_0GetUserInfo,
    MsV1_0ReLogonUsers,
    MsV1_0ChangePassword,
    MsV1_0ChangeCachedPassword,
    MsV1_0GenericPassthrough,
    MsV1_0CacheLogon,
    MsV1_0SubAuth,
    MsV1_0DeriveCredential,
    MsV1_0CacheLookup,
#if _WIN32_WINNT >= 0x0501
    MsV1_0SetProcessOption,
#endif
#if _WIN32_WINNT >= 0x0600
    MsV1_0ConfigLocalAliases,
    MsV1_0ClearCachedCredentials,
#endif
#if _WIN32_WINNT >= 0x0601
    MsV1_0LookupToken,
#endif
#if _WIN32_WINNT >= 0x0602
    MsV1_0ValidateAuth,
    MsV1_0CacheLookupEx,
    MsV1_0GetCredentialKey,
    MsV1_0SetThreadOption,
#endif
#if _WIN32_WINNT >= 0x0A00
    MsV1_0DecryptDpapiMasterKey,
    MsV1_0GetStrongCredentialKey,
    MsV1_0TransferCred,
    MsV1_0ProvisionTbal,
    MsV1_0DeleteTbalSecrets
#endif
  } MSV1_0_PROTOCOL_MESSAGE_TYPE,*PMSV1_0_PROTOCOL_MESSAGE_TYPE;

  typedef struct _MSV1_0_CHANGEPASSWORD_REQUEST {
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING DomainName;
    UNICODE_STRING AccountName;
    UNICODE_STRING OldPassword;
    UNICODE_STRING NewPassword;
    BOOLEAN Impersonating;
  } MSV1_0_CHANGEPASSWORD_REQUEST,*PMSV1_0_CHANGEPASSWORD_REQUEST;

  typedef struct _MSV1_0_CHANGEPASSWORD_RESPONSE {
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    BOOLEAN PasswordInfoValid;
    DOMAIN_PASSWORD_INFORMATION DomainPasswordInfo;
  } MSV1_0_CHANGEPASSWORD_RESPONSE,*PMSV1_0_CHANGEPASSWORD_RESPONSE;

  typedef struct _MSV1_0_PASSTHROUGH_REQUEST {
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING DomainName;
    UNICODE_STRING PackageName;
    ULONG DataLength;
    PUCHAR LogonData;
    ULONG Pad;
  } MSV1_0_PASSTHROUGH_REQUEST,*PMSV1_0_PASSTHROUGH_REQUEST;

  typedef struct _MSV1_0_PASSTHROUGH_RESPONSE {
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Pad;
    ULONG DataLength;
    PUCHAR ValidationData;
  } MSV1_0_PASSTHROUGH_RESPONSE,*PMSV1_0_PASSTHROUGH_RESPONSE;

  typedef struct _MSV1_0_SUBAUTH_REQUEST{
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG SubAuthPackageId;
    ULONG SubAuthInfoLength;
    PUCHAR SubAuthSubmitBuffer;
  } MSV1_0_SUBAUTH_REQUEST,*PMSV1_0_SUBAUTH_REQUEST;

  typedef struct _MSV1_0_SUBAUTH_RESPONSE{
    MSV1_0_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG SubAuthInfoLength;
    PUCHAR SubAuthReturnBuffer;
  } MSV1_0_SUBAUTH_RESPONSE,*PMSV1_0_SUBAUTH_RESPONSE;

#define RtlGenRandom SystemFunction036
#define RtlEncryptMemory SystemFunction040
#define RtlDecryptMemory SystemFunction041

  BOOLEAN WINAPI RtlGenRandom(PVOID RandomBuffer,ULONG RandomBufferLength);

#define RTL_ENCRYPT_MEMORY_SIZE 8
#define RTL_ENCRYPT_OPTION_CROSS_PROCESS 0x01
#define RTL_ENCRYPT_OPTION_SAME_LOGON 0x02
#define RTL_ENCRYPT_OPTION_FOR_SYSTEM 0x04

  NTSTATUS WINAPI RtlEncryptMemory(PVOID Memory,ULONG MemorySize,ULONG OptionFlags);
  NTSTATUS WINAPI RtlDecryptMemory(PVOID Memory,ULONG MemorySize,ULONG OptionFlags);

#define KERBEROS_VERSION 5
#define KERBEROS_REVISION 6

#define KERB_ETYPE_NULL 0
#define KERB_ETYPE_DES_CBC_CRC 1
#define KERB_ETYPE_DES_CBC_MD4 2
#define KERB_ETYPE_DES_CBC_MD5 3
#define KERB_ETYPE_AES128_CTS_HMAC_SHA1_96 17
#define KERB_ETYPE_AES256_CTS_HMAC_SHA1_96 18

#define KERB_ETYPE_RC4_MD4 -128
#define KERB_ETYPE_RC4_PLAIN2 -129
#define KERB_ETYPE_RC4_LM -130
#define KERB_ETYPE_RC4_SHA -131
#define KERB_ETYPE_DES_PLAIN -132
#define KERB_ETYPE_RC4_HMAC_OLD -133
#define KERB_ETYPE_RC4_PLAIN_OLD -134
#define KERB_ETYPE_RC4_HMAC_OLD_EXP -135
#define KERB_ETYPE_RC4_PLAIN_OLD_EXP -136
#define KERB_ETYPE_RC4_PLAIN -140
#define KERB_ETYPE_RC4_PLAIN_EXP -141
#define KERB_ETYPE_AES128_CTS_HMAC_SHA1_96_PLAIN -148
#define KERB_ETYPE_AES256_CTS_HMAC_SHA1_96_PLAIN -149

#define KERB_ETYPE_DSA_SHA1_CMS 9
#define KERB_ETYPE_RSA_MD5_CMS 10
#define KERB_ETYPE_RSA_SHA1_CMS 11
#define KERB_ETYPE_RC2_CBC_ENV 12
#define KERB_ETYPE_RSA_ENV 13
#define KERB_ETYPE_RSA_ES_OEAP_ENV 14
#define KERB_ETYPE_DES_EDE3_CBC_ENV 15

#define KERB_ETYPE_DSA_SIGN 8
#define KERB_ETYPE_RSA_PRIV 9
#define KERB_ETYPE_RSA_PUB 10
#define KERB_ETYPE_RSA_PUB_MD5 11
#define KERB_ETYPE_RSA_PUB_SHA1 12
#define KERB_ETYPE_PKCS7_PUB 13

#define KERB_ETYPE_DES3_CBC_MD5 5
#define KERB_ETYPE_DES3_CBC_SHA1 7
#define KERB_ETYPE_DES3_CBC_SHA1_KD 16

#define KERB_ETYPE_DES_CBC_MD5_NT 20
#define KERB_ETYPE_RC4_HMAC_NT 23
#define KERB_ETYPE_RC4_HMAC_NT_EXP 24

#define KERB_CHECKSUM_NONE 0
#define KERB_CHECKSUM_CRC32 1
#define KERB_CHECKSUM_MD4 2
#define KERB_CHECKSUM_KRB_DES_MAC 4
#define KERB_CHECKSUM_KRB_DES_MAC_K 5
#define KERB_CHECKSUM_MD5 7
#define KERB_CHECKSUM_MD5_DES 8
#define KERB_CHECKSUM_SHA1_NEW 14
#define KERB_CHECKSUM_HMAC_SHA1_96_AES128 15
#define KERB_CHECKSUM_HMAC_SHA1_96_AES256 16

#define KERB_CHECKSUM_LM -130
#define KERB_CHECKSUM_SHA1 -131
#define KERB_CHECKSUM_REAL_CRC32 -132
#define KERB_CHECKSUM_DES_MAC -133
#define KERB_CHECKSUM_DES_MAC_MD5 -134
#define KERB_CHECKSUM_MD25 -135
#define KERB_CHECKSUM_RC4_MD5 -136
#define KERB_CHECKSUM_MD5_HMAC -137
#define KERB_CHECKSUM_HMAC_MD5 -138
#define KERB_CHECKSUM_SHA256 -139
#define KERB_CHECKSUM_SHA384 -140
#define KERB_CHECKSUM_SHA512 -141
#define KERB_CHECKSUM_HMAC_SHA1_96_AES128_Ki -150
#define KERB_CHECKSUM_HMAC_SHA1_96_AES256_Ki -151

#define AUTH_REQ_ALLOW_FORWARDABLE 0x00000001
#define AUTH_REQ_ALLOW_PROXIABLE 0x00000002
#define AUTH_REQ_ALLOW_POSTDATE 0x00000004
#define AUTH_REQ_ALLOW_RENEWABLE 0x00000008
#define AUTH_REQ_ALLOW_NOADDRESS 0x00000010
#define AUTH_REQ_ALLOW_ENC_TKT_IN_SKEY 0x00000020
#define AUTH_REQ_ALLOW_VALIDATE 0x00000040
#define AUTH_REQ_VALIDATE_CLIENT 0x00000080
#define AUTH_REQ_OK_AS_DELEGATE 0x00000100
#define AUTH_REQ_PREAUTH_REQUIRED 0x00000200
#define AUTH_REQ_TRANSITIVE_TRUST 0x00000400
#define AUTH_REQ_ALLOW_S4U_DELEGATE 0x00000800

#define AUTH_REQ_PER_USER_FLAGS (AUTH_REQ_ALLOW_FORWARDABLE | AUTH_REQ_ALLOW_PROXIABLE | AUTH_REQ_ALLOW_POSTDATE | AUTH_REQ_ALLOW_RENEWABLE | AUTH_REQ_ALLOW_VALIDATE)

#define KERB_TICKET_FLAGS_reserved 0x80000000
#define KERB_TICKET_FLAGS_forwardable 0x40000000
#define KERB_TICKET_FLAGS_forwarded 0x20000000
#define KERB_TICKET_FLAGS_proxiable 0x10000000
#define KERB_TICKET_FLAGS_proxy 0x08000000
#define KERB_TICKET_FLAGS_may_postdate 0x04000000
#define KERB_TICKET_FLAGS_postdated 0x02000000
#define KERB_TICKET_FLAGS_invalid 0x01000000
#define KERB_TICKET_FLAGS_renewable 0x00800000
#define KERB_TICKET_FLAGS_initial 0x00400000
#define KERB_TICKET_FLAGS_pre_authent 0x00200000
#define KERB_TICKET_FLAGS_hw_authent 0x00100000
#define KERB_TICKET_FLAGS_ok_as_delegate 0x00040000
#define KERB_TICKET_FLAGS_name_canonicalize 0x00010000
#if _WIN32_WINNT == 0x0501
#define KERB_TICKET_FLAGS_cname_in_pa_data 0x00040000
#endif
#define KERB_TICKET_FLAGS_enc_pa_rep 0x00010000
#define KERB_TICKET_FLAGS_reserved1 0x00000001

#define KRB_NT_UNKNOWN 0
#define KRB_NT_PRINCIPAL 1
#define KRB_NT_PRINCIPAL_AND_ID -131
#define KRB_NT_SRV_INST 2
#define KRB_NT_SRV_INST_AND_ID -132
#define KRB_NT_SRV_HST 3
#define KRB_NT_SRV_XHST 4
#define KRB_NT_UID 5
#define KRB_NT_ENTERPRISE_PRINCIPAL 10
#define KRB_NT_WELLKNOWN 11
#define KRB_NT_MS_BRANCH_ID -133
#define KRB_NT_ENT_PRINCIPAL_AND_ID -130
#define KRB_NT_MS_PRINCIPAL -128
#define KRB_NT_MS_PRINCIPAL_AND_ID -129

#define KERB_IS_MS_PRINCIPAL(_x_) (((_x_) <= KRB_NT_MS_PRINCIPAL) || ((_x_) >= KRB_NT_ENTERPRISE_PRINCIPAL))

#if _WIN32_WINNT >= 0x0600
#define KRB_NT_X500_PRINCIPAL 6
#endif

#define KRB_WELLKNOWN_STRING L"WELLKNOWN"
#define KRB_ANONYMOUS_STRING L"ANONYMOUS"

#ifndef MICROSOFT_KERBEROS_NAME_A

#define MICROSOFT_KERBEROS_NAME_A "Kerberos"
#define MICROSOFT_KERBEROS_NAME_W L"Kerberos"
#ifdef WIN32_CHICAGO
#define MICROSOFT_KERBEROS_NAME MICROSOFT_KERBEROS_NAME_A
#else
#define MICROSOFT_KERBEROS_NAME MICROSOFT_KERBEROS_NAME_W
#endif
#endif

#define KERB_WRAP_NO_ENCRYPT 0x80000001

  typedef enum _KERB_LOGON_SUBMIT_TYPE {
    KerbInteractiveLogon = 2,
    KerbSmartCardLogon = 6,
    KerbWorkstationUnlockLogon = 7,
    KerbSmartCardUnlockLogon = 8,
    KerbProxyLogon = 9,
    KerbTicketLogon = 10,
    KerbTicketUnlockLogon = 11,
    KerbS4ULogon = 12,
#if (_WIN32_WINNT >= 0x0600)
    KerbCertificateLogon = 13,
    KerbCertificateS4ULogon = 14,
    KerbCertificateUnlockLogon = 15,
#endif
#if (_WIN32_WINNT >= 0x0602)
    KerbNoElevationLogon = 83,
    KerbLuidLogon = 84
#endif
  } KERB_LOGON_SUBMIT_TYPE,*PKERB_LOGON_SUBMIT_TYPE;

  typedef struct _KERB_INTERACTIVE_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING LogonDomainName;
    UNICODE_STRING UserName;
    UNICODE_STRING Password;
  } KERB_INTERACTIVE_LOGON,*PKERB_INTERACTIVE_LOGON;

  typedef struct _KERB_INTERACTIVE_UNLOCK_LOGON {
    KERB_INTERACTIVE_LOGON Logon;
    LUID LogonId;
  } KERB_INTERACTIVE_UNLOCK_LOGON,*PKERB_INTERACTIVE_UNLOCK_LOGON;

  typedef struct _KERB_SMART_CARD_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING Pin;
    ULONG CspDataLength;
    PUCHAR CspData;
  } KERB_SMART_CARD_LOGON,*PKERB_SMART_CARD_LOGON;

  typedef struct _KERB_SMART_CARD_UNLOCK_LOGON {
    KERB_SMART_CARD_LOGON Logon;
    LUID LogonId;
  } KERB_SMART_CARD_UNLOCK_LOGON,*PKERB_SMART_CARD_UNLOCK_LOGON;

  typedef struct _KERB_TICKET_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    ULONG Flags;
    ULONG ServiceTicketLength;
    ULONG TicketGrantingTicketLength;
    PUCHAR ServiceTicket;
    PUCHAR TicketGrantingTicket;
  } KERB_TICKET_LOGON,*PKERB_TICKET_LOGON;

#define KERB_LOGON_FLAG_ALLOW_EXPIRED_TICKET 0x1
#define KERB_LOGON_FLAG_REDIRECTED 0x2

  typedef struct _KERB_TICKET_UNLOCK_LOGON {
    KERB_TICKET_LOGON Logon;
    LUID LogonId;
  } KERB_TICKET_UNLOCK_LOGON,*PKERB_TICKET_UNLOCK_LOGON;

#if _WIN32_WINNT >= 0x0600
#define KERB_S4U_LOGON_FLAG_CHECK_LOGONHOURS 0x2
#define KERB_S4U_LOGON_FLAG_IDENTIFY 0x8
#endif

  typedef struct _KERB_S4U_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    ULONG Flags;
    UNICODE_STRING ClientUpn;
    UNICODE_STRING ClientRealm;
  } KERB_S4U_LOGON,*PKERB_S4U_LOGON;

  typedef enum _KERB_PROFILE_BUFFER_TYPE {
    KerbInteractiveProfile = 2,KerbSmartCardProfile = 4,KerbTicketProfile = 6
  } KERB_PROFILE_BUFFER_TYPE,*PKERB_PROFILE_BUFFER_TYPE;

  typedef struct _KERB_INTERACTIVE_PROFILE {
    KERB_PROFILE_BUFFER_TYPE MessageType;
    USHORT LogonCount;
    USHORT BadPasswordCount;
    LARGE_INTEGER LogonTime;
    LARGE_INTEGER LogoffTime;
    LARGE_INTEGER KickOffTime;
    LARGE_INTEGER PasswordLastSet;
    LARGE_INTEGER PasswordCanChange;
    LARGE_INTEGER PasswordMustChange;
    UNICODE_STRING LogonScript;
    UNICODE_STRING HomeDirectory;
    UNICODE_STRING FullName;
    UNICODE_STRING ProfilePath;
    UNICODE_STRING HomeDirectoryDrive;
    UNICODE_STRING LogonServer;
    ULONG UserFlags;
  } KERB_INTERACTIVE_PROFILE,*PKERB_INTERACTIVE_PROFILE;

  typedef struct _KERB_SMART_CARD_PROFILE {
    KERB_INTERACTIVE_PROFILE Profile;
    ULONG CertificateSize;
    PUCHAR CertificateData;
  } KERB_SMART_CARD_PROFILE,*PKERB_SMART_CARD_PROFILE;

  typedef struct KERB_CRYPTO_KEY {
    LONG KeyType;
    ULONG Length;
    PUCHAR Value;
  } KERB_CRYPTO_KEY,*PKERB_CRYPTO_KEY;

  typedef struct KERB_CRYPTO_KEY32 {
    LONG KeyType;
    ULONG Length;
    ULONG Offset;
  } KERB_CRYPTO_KEY32,*PKERB_CRYPTO_KEY32;

  typedef struct _KERB_TICKET_PROFILE {
    KERB_INTERACTIVE_PROFILE Profile;
    KERB_CRYPTO_KEY SessionKey;
  } KERB_TICKET_PROFILE,*PKERB_TICKET_PROFILE;

  typedef enum _KERB_PROTOCOL_MESSAGE_TYPE {
    KerbDebugRequestMessage = 0,
    KerbQueryTicketCacheMessage,
    KerbChangeMachinePasswordMessage,
    KerbVerifyPacMessage,
    KerbRetrieveTicketMessage,
    KerbUpdateAddressesMessage,
    KerbPurgeTicketCacheMessage,
    KerbChangePasswordMessage,
    KerbRetrieveEncodedTicketMessage,
    KerbDecryptDataMessage,
    KerbAddBindingCacheEntryMessage,
    KerbSetPasswordMessage,
    KerbSetPasswordExMessage,
#if _WIN32_WINNT >= 0x0501
    KerbVerifyCredentialsMessage,
    KerbQueryTicketCacheExMessage,
    KerbPurgeTicketCacheExMessage,
#endif
#if _WIN32_WINNT >= 0x0502
    KerbRefreshSmartcardCredentialsMessage,
    KerbAddExtraCredentialsMessage,
    KerbQuerySupplementalCredentialsMessage,
#endif
#if _WIN32_WINNT >= 0x0600
    KerbTransferCredentialsMessage,
    KerbQueryTicketCacheEx2Message,
    KerbSubmitTicketMessage,
    KerbAddExtraCredentialsExMessage,
#endif
#if _WIN32_WINNT >= 0x0602
    KerbQueryKdcProxyCacheMessage,
    KerbPurgeKdcProxyCacheMessage,
    KerbQueryTicketCacheEx3Message,
    KerbCleanupMachinePkinitCredsMessage,
    KerbAddBindingCacheEntryExMessage,
    KerbQueryBindingCacheMessage,
    KerbPurgeBindingCacheMessage,
    KerbPinKdcMessage,
    KerbUnpinAllKdcsMessage,
    KerbQueryDomainExtendedPoliciesMessage,
    KerbQueryS4U2ProxyCacheMessage
#endif
#if _WIN32_WINNT >= 0x0A00
    ,KerbRetrieveKeyTabMessage
    ,KerbRefreshPolicyMessage
    ,KerbPrintCloudKerberosDebugMessage
#endif
  } KERB_PROTOCOL_MESSAGE_TYPE,*PKERB_PROTOCOL_MESSAGE_TYPE;

  typedef struct _KERB_QUERY_TKT_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
  } KERB_QUERY_TKT_CACHE_REQUEST,*PKERB_QUERY_TKT_CACHE_REQUEST;

  typedef struct _KERB_TICKET_CACHE_INFO {
    UNICODE_STRING ServerName;
    UNICODE_STRING RealmName;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewTime;
    LONG EncryptionType;
    ULONG TicketFlags;
  } KERB_TICKET_CACHE_INFO,*PKERB_TICKET_CACHE_INFO;

  typedef struct _KERB_TICKET_CACHE_INFO_EX {
    UNICODE_STRING ClientName;
    UNICODE_STRING ClientRealm;
    UNICODE_STRING ServerName;
    UNICODE_STRING ServerRealm;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewTime;
    LONG EncryptionType;
    ULONG TicketFlags;
  } KERB_TICKET_CACHE_INFO_EX,*PKERB_TICKET_CACHE_INFO_EX;

  typedef struct _KERB_TICKET_CACHE_INFO_EX2 {
    UNICODE_STRING ClientName;
    UNICODE_STRING ClientRealm;
    UNICODE_STRING ServerName;
    UNICODE_STRING ServerRealm;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewTime;
    LONG EncryptionType;
    ULONG TicketFlags;
    ULONG SessionKeyType;
  } KERB_TICKET_CACHE_INFO_EX2,*PKERB_TICKET_CACHE_INFO_EX2;

#if _WIN32_WINNT >= 0x0602
  typedef struct _KERB_TICKET_CACHE_INFO_EX3 {
    UNICODE_STRING ClientName;
    UNICODE_STRING ClientRealm;
    UNICODE_STRING ServerName;
    UNICODE_STRING ServerRealm;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewTime;
    LONG EncryptionType;
    ULONG TicketFlags;
    ULONG SessionKeyType;
    ULONG BranchId;
    ULONG CacheFlags;
    UNICODE_STRING KdcCalled;
  } KERB_TICKET_CACHE_INFO_EX3, *PKERB_TICKET_CACHE_INFO_EX3;
#endif

  typedef struct _KERB_QUERY_TKT_CACHE_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfTickets;
    KERB_TICKET_CACHE_INFO Tickets[ANYSIZE_ARRAY];
  } KERB_QUERY_TKT_CACHE_RESPONSE,*PKERB_QUERY_TKT_CACHE_RESPONSE;

  typedef struct _KERB_QUERY_TKT_CACHE_EX_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfTickets;
    KERB_TICKET_CACHE_INFO_EX Tickets[ANYSIZE_ARRAY];
  } KERB_QUERY_TKT_CACHE_EX_RESPONSE,*PKERB_QUERY_TKT_CACHE_EX_RESPONSE;

  typedef struct _KERB_QUERY_TKT_CACHE_EX2_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfTickets;
    KERB_TICKET_CACHE_INFO_EX2 Tickets[ANYSIZE_ARRAY];
  } KERB_QUERY_TKT_CACHE_EX2_RESPONSE,*PKERB_QUERY_TKT_CACHE_EX2_RESPONSE;

#if _WIN32_WINNT >= 0x0602
  typedef struct _KERB_QUERY_TKT_CACHE_EX3_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfTickets;
    KERB_TICKET_CACHE_INFO_EX3 Tickets[ANYSIZE_ARRAY];
  } KERB_QUERY_TKT_CACHE_EX3_RESPONSE, *PKERB_QUERY_TKT_CACHE_EX3_RESPONSE;
#endif

#ifndef __SECHANDLE_DEFINED__
  typedef struct _SecHandle {
    ULONG_PTR dwLower;
    ULONG_PTR dwUpper;
  } SecHandle,*PSecHandle;

#define __SECHANDLE_DEFINED__
#endif

#define KERB_USE_DEFAULT_TICKET_FLAGS 0x0

#define KERB_RETRIEVE_TICKET_DEFAULT 0x0
#define KERB_RETRIEVE_TICKET_DONT_USE_CACHE 0x1
#define KERB_RETRIEVE_TICKET_USE_CACHE_ONLY 0x2
#define KERB_RETRIEVE_TICKET_USE_CREDHANDLE 0x4
#define KERB_RETRIEVE_TICKET_AS_KERB_CRED 0x8
#define KERB_RETRIEVE_TICKET_WITH_SEC_CRED 0x10

#if _WIN32_WINNT >= 0x0600
#define KERB_RETRIEVE_TICKET_CACHE_TICKET 0x20
#endif

#if _WIN32_WINNT >= 0x0601
#define KERB_RETRIEVE_TICKET_MAX_LIFETIME 0x40
#endif

#define KERB_ETYPE_DEFAULT 0x0

  typedef struct _KERB_AUTH_DATA {
    ULONG Type;
    ULONG Length;
    PUCHAR Data;
  } KERB_AUTH_DATA,*PKERB_AUTH_DATA;

  typedef struct _KERB_NET_ADDRESS {
    ULONG Family;
    ULONG Length;
    PCHAR Address;
  } KERB_NET_ADDRESS,*PKERB_NET_ADDRESS;

  typedef struct _KERB_NET_ADDRESSES {
    ULONG Number;
    KERB_NET_ADDRESS Addresses[ANYSIZE_ARRAY];
  } KERB_NET_ADDRESSES,*PKERB_NET_ADDRESSES;

  typedef struct _KERB_EXTERNAL_NAME {
    SHORT NameType;
    USHORT NameCount;
    UNICODE_STRING Names[ANYSIZE_ARRAY];
  } KERB_EXTERNAL_NAME,*PKERB_EXTERNAL_NAME;

  typedef struct _KERB_EXTERNAL_TICKET {
    PKERB_EXTERNAL_NAME ServiceName;
    PKERB_EXTERNAL_NAME TargetName;
    PKERB_EXTERNAL_NAME ClientName;
    UNICODE_STRING DomainName;
    UNICODE_STRING TargetDomainName;
    UNICODE_STRING AltTargetDomainName;
    KERB_CRYPTO_KEY SessionKey;
    ULONG TicketFlags;
    ULONG Flags;
    LARGE_INTEGER KeyExpirationTime;
    LARGE_INTEGER StartTime;
    LARGE_INTEGER EndTime;
    LARGE_INTEGER RenewUntil;
    LARGE_INTEGER TimeSkew;
    ULONG EncodedTicketSize;
    PUCHAR EncodedTicket;
  } KERB_EXTERNAL_TICKET,*PKERB_EXTERNAL_TICKET;

  typedef struct _KERB_RETRIEVE_TKT_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    UNICODE_STRING TargetName;
    ULONG TicketFlags;
    ULONG CacheOptions;
    LONG EncryptionType;
    SecHandle CredentialsHandle;
  } KERB_RETRIEVE_TKT_REQUEST,*PKERB_RETRIEVE_TKT_REQUEST;

  typedef struct _KERB_RETRIEVE_TKT_RESPONSE {
    KERB_EXTERNAL_TICKET Ticket;
  } KERB_RETRIEVE_TKT_RESPONSE,*PKERB_RETRIEVE_TKT_RESPONSE;

  typedef struct _KERB_PURGE_TKT_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    UNICODE_STRING ServerName;
    UNICODE_STRING RealmName;
  } KERB_PURGE_TKT_CACHE_REQUEST,*PKERB_PURGE_TKT_CACHE_REQUEST;

#define KERB_PURGE_ALL_TICKETS 1

  typedef struct _KERB_PURGE_TKT_CACHE_EX_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    ULONG Flags;
    KERB_TICKET_CACHE_INFO_EX TicketTemplate;
  } KERB_PURGE_TKT_CACHE_EX_REQUEST,*PKERB_PURGE_TKT_CACHE_EX_REQUEST;

  typedef struct _KERB_SUBMIT_TKT_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    ULONG Flags;
    KERB_CRYPTO_KEY32 Key;
    ULONG KerbCredSize;
    ULONG KerbCredOffset;
  } KERB_SUBMIT_TKT_REQUEST, *PKERB_SUBMIT_TKT_REQUEST;

#if _WIN32_WINNT >= 0x0602

  typedef struct _KERB_QUERY_KDC_PROXY_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Flags;
    LUID LogonId;
  } KERB_QUERY_KDC_PROXY_CACHE_REQUEST, *PKERB_QUERY_KDC_PROXY_CACHE_REQUEST;

  typedef struct _KDC_PROXY_CACHE_ENTRY_DATA {
    ULONG64 SinceLastUsed;
    UNICODE_STRING DomainName;
    UNICODE_STRING ProxyServerName;
    UNICODE_STRING ProxyServerVdir;
    USHORT ProxyServerPort;
    LUID LogonId;
    UNICODE_STRING CredUserName;
    UNICODE_STRING CredDomainName;
    BOOLEAN GlobalCache;
  } KDC_PROXY_CACHE_ENTRY_DATA, *PKDC_PROXY_CACHE_ENTRY_DATA;

  typedef struct _KERB_QUERY_KDC_PROXY_CACHE_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfEntries;
    PKDC_PROXY_CACHE_ENTRY_DATA Entries;
  } KERB_QUERY_KDC_PROXY_CACHE_RESPONSE, *PKERB_QUERY_KDC_PROXY_CACHE_RESPONSE;

  typedef struct _KERB_PURGE_KDC_PROXY_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Flags;
    LUID LogonId;
  } KERB_PURGE_KDC_PROXY_CACHE_REQUEST, *PKERB_PURGE_KDC_PROXY_CACHE_REQUEST;

  typedef struct _KERB_PURGE_KDC_PROXY_CACHE_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfPurged;
  } KERB_PURGE_KDC_PROXY_CACHE_RESPONSE, *PKERB_PURGE_KDC_PROXY_CACHE_RESPONSE;

#define KERB_S4U2PROXY_CACHE_ENTRY_INFO_FLAG_NEGATIVE 0x1

  typedef struct _KERB_S4U2PROXY_CACHE_ENTRY_INFO {
    UNICODE_STRING ServerName;
    ULONG Flags;
    NTSTATUS LastStatus;
    LARGE_INTEGER  Expiry;
  } KERB_S4U2PROXY_CACHE_ENTRY_INFO, *PKERB_S4U2PROXY_CACHE_ENTRY_INFO;

#define KERB_S4U2PROXY_CRED_FLAG_NEGATIVE 0x1

  typedef struct _KERB_S4U2PROXY_CRED {
    UNICODE_STRING UserName;
    UNICODE_STRING DomainName;
    ULONG Flags;
    NTSTATUS LastStatus;
    LARGE_INTEGER Expiry;
    ULONG CountOfEntries;
    PKERB_S4U2PROXY_CACHE_ENTRY_INFO Entries;
  } KERB_S4U2PROXY_CRED, *PKERB_S4U2PROXY_CRED;

  typedef struct _KERB_QUERY_S4U2PROXY_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Flags;
    LUID LogonId;
  } KERB_QUERY_S4U2PROXY_CACHE_REQUEST, *PKERB_QUERY_S4U2PROXY_CACHE_REQUEST;

  typedef struct _KERB_QUERY_S4U2PROXY_CACHE_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfCreds;
    PKERB_S4U2PROXY_CRED Creds;
  } KERB_QUERY_S4U2PROXY_CACHE_RESPONSE, *PKERB_QUERY_S4U2PROXY_CACHE_RESPONSE;

#endif

#if _WIN32_WINNT >= 0x0A00

  typedef struct _KERB_RETRIEVE_KEY_TAB_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Flags;
    UNICODE_STRING UserName;
    UNICODE_STRING DomainName;
    UNICODE_STRING Password;
  } KERB_RETRIEVE_KEY_TAB_REQUEST, *PKERB_RETRIEVE_KEY_TAB_REQUEST;

  typedef struct _KERB_RETRIEVE_KEY_TAB_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG KeyTabLength;
    PUCHAR KeyTab;
  } KERB_RETRIEVE_KEY_TAB_RESPONSE, *PKERB_RETRIEVE_KEY_TAB_RESPONSE;

#define KERB_REFRESH_POLICY_KERBEROS 0x1
#define KERB_REFRESH_POLICY_KDC 0x2

  typedef struct _KERB_REFRESH_POLICY_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Flags;
  } KERB_REFRESH_POLICY_REQUEST, *PKERB_REFRESH_POLICY_REQUEST;

  typedef struct _KERB_REFRESH_POLICY_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Flags;
  } KERB_REFRESH_POLICY_RESPONSE, *PKERB_REFRESH_POLICY_RESPONSE;

  typedef struct _KERB_CLOUD_KERBEROS_DEBUG_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
  } KERB_CLOUD_KERBEROS_DEBUG_REQUEST, *PKERB_CLOUD_KERBEROS_DEBUG_REQUEST;

  typedef struct _KERB_CLOUD_KERBEROS_DEBUG_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Version;
    ULONG Length;
    ULONG Data[ANYSIZE_ARRAY];
  } KERB_CLOUD_KERBEROS_DEBUG_RESPONSE, *PKERB_CLOUD_KERBEROS_DEBUG_RESPONSE;

#define KERB_CLOUD_KERBEROS_DEBUG_DATA_VERSION 1

  typedef struct _KERB_CLOUD_KERBEROS_DEBUG_DATA_V0 {
    unsigned int EnabledByPolicy : 1;
    unsigned int AsRepCallbackPresent : 1;
    unsigned int AsRepCallbackUsed : 1;
    unsigned int CloudReferralTgtAvailable : 1;
    unsigned int SpnOracleConfigured : 1;
    unsigned int KdcProxyPresent : 1;
  } KERB_CLOUD_KERBEROS_DEBUG_DATA_V0, *PKERB_CLOUD_KERBEROS_DEBUG_DATA_V0;

  typedef struct _KERB_CLOUD_KERBEROS_DEBUG_DATA {
    unsigned int EnabledByPolicy : 1;
    unsigned int AsRepCallbackPresent : 1;
    unsigned int AsRepCallbackUsed : 1;
    unsigned int CloudReferralTgtAvailable : 1;
    unsigned int SpnOracleConfigured : 1;
    unsigned int KdcProxyPresent : 1;
    unsigned int PublicKeyCredsPresent : 1;
    unsigned int PasswordKeysPresent : 1;
    unsigned int PasswordPresent : 1;
    unsigned int AsRepSourceCred : 8;
  } KERB_CLOUD_KERBEROS_DEBUG_DATA, *PKERB_CLOUD_KERBEROS_DEBUG_DATA;

#endif /* _WIN32_WINNT >= 0x0A00 */

  typedef struct _KERB_CHANGEPASSWORD_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING DomainName;
    UNICODE_STRING AccountName;
    UNICODE_STRING OldPassword;
    UNICODE_STRING NewPassword;
    BOOLEAN Impersonating;
  } KERB_CHANGEPASSWORD_REQUEST,*PKERB_CHANGEPASSWORD_REQUEST;

  typedef struct _KERB_SETPASSWORD_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    SecHandle CredentialsHandle;
    ULONG Flags;
    UNICODE_STRING DomainName;
    UNICODE_STRING AccountName;
    UNICODE_STRING Password;
  } KERB_SETPASSWORD_REQUEST,*PKERB_SETPASSWORD_REQUEST;

  typedef struct _KERB_SETPASSWORD_EX_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    SecHandle CredentialsHandle;
    ULONG Flags;
    UNICODE_STRING AccountRealm;
    UNICODE_STRING AccountName;
    UNICODE_STRING Password;
    UNICODE_STRING ClientRealm;
    UNICODE_STRING ClientName;
    BOOLEAN Impersonating;
    UNICODE_STRING KdcAddress;
    ULONG KdcAddressType;
  } KERB_SETPASSWORD_EX_REQUEST,*PKERB_SETPASSWORD_EX_REQUEST;

#define DS_UNKNOWN_ADDRESS_TYPE 0
#define KERB_SETPASS_USE_LOGONID 1
#define KERB_SETPASS_USE_CREDHANDLE 2

  typedef struct _KERB_DECRYPT_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
    ULONG Flags;
    LONG CryptoType;
    LONG KeyUsage;
    KERB_CRYPTO_KEY Key;
    ULONG EncryptedDataSize;
    ULONG InitialVectorSize;
    PUCHAR InitialVector;
    PUCHAR EncryptedData;
  } KERB_DECRYPT_REQUEST,*PKERB_DECRYPT_REQUEST;

#define KERB_DECRYPT_FLAG_DEFAULT_KEY 0x00000001

  typedef struct _KERB_DECRYPT_RESPONSE {
    UCHAR DecryptedData[ANYSIZE_ARRAY];
  } KERB_DECRYPT_RESPONSE,*PKERB_DECRYPT_RESPONSE;

  typedef struct _KERB_ADD_BINDING_CACHE_ENTRY_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING RealmName;
    UNICODE_STRING KdcAddress;
    ULONG AddressType;
  } KERB_ADD_BINDING_CACHE_ENTRY_REQUEST,*PKERB_ADD_BINDING_CACHE_ENTRY_REQUEST;

  typedef struct _KERB_REFRESH_SCCRED_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING CredentialBlob;
    LUID LogonId;
    ULONG Flags;
  } KERB_REFRESH_SCCRED_REQUEST,*PKERB_REFRESH_SCCRED_REQUEST;

#define KERB_REFRESH_SCCRED_RELEASE 0x0
#define KERB_REFRESH_SCCRED_GETTGT 0x1

  typedef struct _KERB_ADD_CREDENTIALS_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING UserName;
    UNICODE_STRING DomainName;
    UNICODE_STRING Password;
    LUID LogonId;
    ULONG Flags;
  } KERB_ADD_CREDENTIALS_REQUEST,*PKERB_ADD_CREDENTIALS_REQUEST;

#define KERB_REQUEST_ADD_CREDENTIAL 1
#define KERB_REQUEST_REPLACE_CREDENTIAL 2
#define KERB_REQUEST_REMOVE_CREDENTIAL 4

#if _WIN32_WINNT >= 0x0600

  typedef struct _KERB_ADD_CREDENTIALS_REQUEST_EX {
    KERB_ADD_CREDENTIALS_REQUEST Credentials;
    ULONG PrincipalNameCount;
    UNICODE_STRING PrincipalNames[1];
  } KERB_ADD_CREDENTIALS_REQUEST_EX, *PKERB_ADD_CREDENTIALS_REQUEST_EX;

#endif

  typedef struct _KERB_TRANSFER_CRED_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID OriginLogonId;
    LUID DestinationLogonId;
    ULONG Flags;
  } KERB_TRANSFER_CRED_REQUEST,*PKERB_TRANSFER_CRED_REQUEST;

#define KERB_TRANSFER_CRED_WITH_TICKETS 1
#define KERB_TRANSFER_CRED_CLEANUP_CREDENTIALS 2

#if _WIN32_WINNT >= 0x0602

  typedef struct _KERB_CLEANUP_MACHINE_PKINIT_CREDS_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    LUID LogonId;
  } KERB_CLEANUP_MACHINE_PKINIT_CREDS_REQUEST, *PKERB_CLEANUP_MACHINE_PKINIT_CREDS_REQUEST;

  typedef struct _KERB_BINDING_CACHE_ENTRY_DATA {
    ULONG64 DiscoveryTime;
    UNICODE_STRING RealmName;
    UNICODE_STRING KdcAddress;
    ULONG AddressType;
    ULONG Flags;
    ULONG DcFlags;
    ULONG CacheFlags;
    UNICODE_STRING KdcName;
  } KERB_BINDING_CACHE_ENTRY_DATA, *PKERB_BINDING_CACHE_ENTRY_DATA;

  typedef struct _KERB_QUERY_BINDING_CACHE_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG CountOfEntries;
    PKERB_BINDING_CACHE_ENTRY_DATA Entries;
  } KERB_QUERY_BINDING_CACHE_RESPONSE, *PKERB_QUERY_BINDING_CACHE_RESPONSE;

  typedef struct _KERB_ADD_BINDING_CACHE_ENTRY_EX_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    UNICODE_STRING RealmName;
    UNICODE_STRING KdcAddress;
    ULONG AddressType;
    ULONG DcFlags;
  } KERB_ADD_BINDING_CACHE_ENTRY_EX_REQUEST, *PKERB_ADD_BINDING_CACHE_ENTRY_EX_REQUEST;

  typedef struct _KERB_QUERY_BINDING_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
  } KERB_QUERY_BINDING_CACHE_REQUEST, *PKERB_QUERY_BINDING_CACHE_REQUEST;

  typedef struct _KERB_PURGE_BINDING_CACHE_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
  } KERB_PURGE_BINDING_CACHE_REQUEST, *PKERB_PURGE_BINDING_CACHE_REQUEST;

  typedef struct _KERB_QUERY_DOMAIN_EXTENDED_POLICIES_REQUEST {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Flags;
    UNICODE_STRING DomainName;
  } KERB_QUERY_DOMAIN_EXTENDED_POLICIES_REQUEST, *PKERB_QUERY_DOMAIN_EXTENDED_POLICIES_REQUEST;

#define KERB_QUERY_DOMAIN_EXTENDED_POLICIES_RESPONSE_FLAG_DAC_DISABLED 1

  typedef struct _KERB_QUERY_DOMAIN_EXTENDED_POLICIES_RESPONSE {
    KERB_PROTOCOL_MESSAGE_TYPE MessageType;
    ULONG Flags;
    ULONG ExtendedPolicies;
    ULONG DsFlags;
  } KERB_QUERY_DOMAIN_EXTENDED_POLICIES_RESPONSE, *PKERB_QUERY_DOMAIN_EXTENDED_POLICIES_RESPONSE;

  typedef enum _KERB_CERTIFICATE_INFO_TYPE {
    CertHashInfo = 1
  } KERB_CERTIFICATE_INFO_TYPE, *PKERB_CERTIFICATE_INFO_TYPE;

  typedef struct _KERB_CERTIFICATE_HASHINFO {
    USHORT StoreNameLength;
    USHORT HashLength;
  } KERB_CERTIFICATE_HASHINFO, *PKERB_CERTIFICATE_HASHINFO;

  typedef struct _KERB_CERTIFICATE_INFO {
    ULONG CertInfoSize;
    ULONG InfoType;
  } KERB_CERTIFICATE_INFO, *PKERB_CERTIFICATE_INFO;

#endif

#define PER_USER_POLICY_UNCHANGED 0x00
#define PER_USER_AUDIT_SUCCESS_INCLUDE 0x01
#define PER_USER_AUDIT_SUCCESS_EXCLUDE 0x02
#define PER_USER_AUDIT_FAILURE_INCLUDE 0x04
#define PER_USER_AUDIT_FAILURE_EXCLUDE 0x08
#define PER_USER_AUDIT_NONE 0x10

#define VALID_PER_USER_AUDIT_POLICY_FLAG (PER_USER_AUDIT_SUCCESS_INCLUDE | PER_USER_AUDIT_SUCCESS_EXCLUDE | PER_USER_AUDIT_FAILURE_INCLUDE | PER_USER_AUDIT_FAILURE_EXCLUDE | PER_USER_AUDIT_NONE)

  typedef struct _AUDIT_POLICY_INFORMATION {
    GUID  AuditSubCategoryGuid;
    ULONG AuditingInformation;
    GUID  AuditCategoryGuid;
  } AUDIT_POLICY_INFORMATION, *PAUDIT_POLICY_INFORMATION;
  typedef const PAUDIT_POLICY_INFORMATION PCAUDIT_POLICY_INFORMATION, LPCAUDIT_POLICY_INFORMATION;

#define AUDIT_SET_SYSTEM_POLICY 0x0001
#define AUDIT_QUERY_SYSTEM_POLICY 0x0002
#define AUDIT_SET_USER_POLICY 0x0004
#define AUDIT_QUERY_USER_POLICY 0x0008
#define AUDIT_ENUMERATE_USERS 0x0010
#define AUDIT_SET_MISC_POLICY 0x0020
#define AUDIT_QUERY_MISC_POLICY 0x0040

#define AUDIT_GENERIC_ALL (STANDARD_RIGHTS_REQUIRED | AUDIT_SET_SYSTEM_POLICY | AUDIT_QUERY_SYSTEM_POLICY | \
                           AUDIT_SET_USER_POLICY | AUDIT_QUERY_USER_POLICY | AUDIT_ENUMERATE_USERS | \
                           AUDIT_SET_MISC_POLICY | AUDIT_QUERY_MISC_POLICY)

#define AUDIT_GENERIC_READ (STANDARD_RIGHTS_READ | AUDIT_QUERY_SYSTEM_POLICY | AUDIT_QUERY_USER_POLICY | \
                            AUDIT_ENUMERATE_USERS | AUDIT_QUERY_MISC_POLICY)

#define AUDIT_GENERIC_WRITE (STANDARD_RIGHTS_WRITE | AUDIT_SET_USER_POLICY | AUDIT_SET_MISC_POLICY | \
                             AUDIT_SET_SYSTEM_POLICY)

#define AUDIT_GENERIC_EXECUTE STANDARD_RIGHTS_EXECUTE

  typedef struct _POLICY_AUDIT_SID_ARRAY {
    ULONG UsersCount;
    PSID  *UserSidArray;
  } POLICY_AUDIT_SID_ARRAY, *PPOLICY_AUDIT_SID_ARRAY;

#if _WIN32_WINNT >= 0x0600

#define KERB_CERTIFICATE_LOGON_FLAG_CHECK_DUPLICATES 0x1
#define KERB_CERTIFICATE_LOGON_FLAG_USE_CERTIFICATE_INFO 0x2

  typedef struct _KERB_CERTIFICATE_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    UNICODE_STRING         DomainName;
    UNICODE_STRING         UserName;
    UNICODE_STRING         Pin;
    ULONG                  Flags;
    ULONG                  CspDataLength;
    PUCHAR                 CspData;
  } KERB_CERTIFICATE_LOGON, *PKERB_CERTIFICATE_LOGON;

  typedef struct _KERB_CERTIFICATE_UNLOCK_LOGON {
    KERB_CERTIFICATE_LOGON Logon;
    LUID                   LogonId;
  } KERB_CERTIFICATE_UNLOCK_LOGON, *PKERB_CERTIFICATE_UNLOCK_LOGON;

#define KERB_CERTIFICATE_S4U_LOGON_FLAG_CHECK_DUPLICATES 0x1
#define KERB_CERTIFICATE_S4U_LOGON_FLAG_CHECK_LOGONHOURS 0x2
#define KERB_CERTIFICATE_S4U_LOGON_FLAG_FAIL_IF_NT_AUTH_POLICY_REQUIRED 0x4
#define KERB_CERTIFICATE_S4U_LOGON_FLAG_IDENTIFY 0x8

  typedef struct _KERB_CERTIFICATE_S4U_LOGON {
    KERB_LOGON_SUBMIT_TYPE MessageType;
    ULONG Flags;
    UNICODE_STRING UserPrincipalName;
    UNICODE_STRING DomainName;
    ULONG CertificateLength;
    PUCHAR Certificate;
  } KERB_CERTIFICATE_S4U_LOGON, *PKERB_CERTIFICATE_S4U_LOGON;

  typedef struct _KERB_SMARTCARD_CSP_INFO {
    DWORD dwCspInfoLen;
    DWORD MessageType;
    __C89_NAMELESS union {
      PVOID   ContextInformation;
      ULONG64 SpaceHolderForWow64;
    };
    DWORD flags;
    DWORD KeySpec;
    ULONG nCardNameOffset;
    ULONG nReaderNameOffset;
    ULONG nContainerNameOffset;
    ULONG nCSPNameOffset;
    TCHAR bBuffer;
  } KERB_SMARTCARD_CSP_INFO, *PKERB_SMARTCARD_CSP_INFO;

#endif

  BOOLEAN WINAPI AuditComputeEffectivePolicyBySid(
    const PSID pSid,
    const GUID *pSubCategoryGuids,
    ULONG PolicyCount,
    PAUDIT_POLICY_INFORMATION *ppAuditPolicy
  );

  VOID WINAPI AuditFree(
    PVOID Buffer
  );

  BOOLEAN WINAPI AuditSetSystemPolicy(
    PCAUDIT_POLICY_INFORMATION pAuditPolicy,
    ULONG PolicyCount
  );

  BOOLEAN WINAPI AuditQuerySystemPolicy(
    const GUID *pSubCategoryGuids,
    ULONG PolicyCount,
    PAUDIT_POLICY_INFORMATION *ppAuditPolicy
  );

  BOOLEAN WINAPI AuditSetPerUserPolicy(
    const PSID pSid,
    PCAUDIT_POLICY_INFORMATION pAuditPolicy,
    ULONG PolicyCount
  );

  BOOLEAN WINAPI AuditQueryPerUserPolicy(
    const PSID pSid,
    const GUID *pSubCategoryGuids,
    ULONG PolicyCount,
    PAUDIT_POLICY_INFORMATION *ppAuditPolicy
  );

  BOOLEAN WINAPI AuditComputeEffectivePolicyByToken(
    HANDLE hTokenHandle,
    const GUID *pSubCategoryGuids,
    ULONG PolicyCount,
    PAUDIT_POLICY_INFORMATION *ppAuditPolicy
  );

  BOOLEAN WINAPI AuditEnumerateCategories(
    GUID **ppAuditCategoriesArray,
    PULONG pCountReturned
  );

  BOOLEAN WINAPI AuditEnumeratePerUserPolicy(
    PPOLICY_AUDIT_SID_ARRAY *ppAuditSidArray
  );

  BOOLEAN WINAPI AuditEnumerateSubCategories(
    const GUID *pAuditCategoryGuid,
    BOOLEAN bRetrieveAllSubCategories,
    GUID **ppAuditSubCategoriesArray,
    PULONG pCountReturned
  );

  BOOLEAN WINAPI AuditLookupCategoryGuidFromCategoryId(
    POLICY_AUDIT_EVENT_TYPE AuditCategoryId,
    GUID *pAuditCategoryGuid
  );

  BOOLEAN WINAPI AuditQuerySecurity(
    SECURITY_INFORMATION SecurityInformation,
    PSECURITY_DESCRIPTOR *ppSecurityDescriptor
  );

#define AuditLookupSubCategoryName __MINGW_NAME_AW(AuditLookupSubCategoryName)
#define AuditLookupCategoryName __MINGW_NAME_AW(AuditLookupCategoryName)

  BOOLEAN WINAPI AuditLookupSubCategoryNameA(
    const GUID *pAuditSubCategoryGuid,
    LPSTR *ppszSubCategoryName
  );

  BOOLEAN WINAPI AuditLookupSubCategoryNameW(
    const GUID *pAuditSubCategoryGuid,
    LPWSTR *ppszSubCategoryName
  );

  BOOLEAN WINAPI AuditLookupCategoryNameA(
    const GUID *pAuditCategoryGuid,
    LPSTR *ppszCategoryName
  );

  BOOLEAN WINAPI AuditLookupCategoryNameW(
    const GUID *pAuditCategoryGuid,
    LPWSTR *ppszCategoryName
  );

  BOOLEAN WINAPI AuditLookupCategoryIdFromCategoryGuid(
    const GUID *pAuditCategoryGuid,
    PPOLICY_AUDIT_EVENT_TYPE pAuditCategoryId
  );

  BOOLEAN WINAPI AuditSetSecurity(
    SECURITY_INFORMATION SecurityInformation,
    PSECURITY_DESCRIPTOR pSecurityDescriptor
  );

  BOOLEAN NTAPI AuditSetGlobalSaclW(
    PCWSTR ObjectTypeName,
    PACL Acl
  );

  BOOLEAN NTAPI AuditSetGlobalSaclA(
    PCSTR ObjectTypeName,
    PACL Acl
  );

#define AuditSetGlobalSacl __MINGW_NAME_AW(AuditSetGlobalSacl)

  BOOLEAN NTAPI AuditQueryGlobalSaclW(
    PCWSTR ObjectTypeName,
    PACL *Acl
  );

  BOOLEAN NTAPI AuditQueryGlobalSaclA(
    PCSTR ObjectTypeName,
    PACL *Acl
  );

#define AuditQueryGlobalSacl __MINGW_NAME_AW(AuditQueryGlobalSacl)

#if _WIN32_WINNT >= 0x0601

#define PKU2U_PACKAGE_NAME_A "pku2u"
#define PKU2U_PACKAGE_NAME L"pku2u"
#define PKU2U_PACKAGE_NAME_W PKU2U_PACKAGE_NAME

  typedef struct _PKU2U_CERT_BLOB {
    ULONG CertOffset;
    USHORT CertLength;
  } PKU2U_CERT_BLOB, *PPKU2U_CERT_BLOB;

#define PKU2U_CREDUI_CONTEXT_VERSION 0x4154414454524543

  typedef struct _PKU2U_CREDUI_CONTEXT {
    ULONG64 Version;
    USHORT cbHeaderLength;
    ULONG cbStructureLength;
    USHORT CertArrayCount;
    ULONG CertArrayOffset;
  } PKU2U_CREDUI_CONTEXT, *PPKU2U_CREDUI_CONTEXT;

  typedef enum _PKU2U_LOGON_SUBMIT_TYPE {
    Pku2uCertificateS4ULogon = 14
  } PKU2U_LOGON_SUBMIT_TYPE, *PPKU2U_LOGON_SUBMIT_TYPE;

  typedef struct _PKU2U_CERTIFICATE_S4U_LOGON {
    PKU2U_LOGON_SUBMIT_TYPE MessageType;
    ULONG Flags;
    UNICODE_STRING UserPrincipalName;
    UNICODE_STRING DomainName;
    ULONG CertificateLength;
    PUCHAR Certificate;
  } PKU2U_CERTIFICATE_S4U_LOGON, *PPKU2U_CERTIFICATE_S4U_LOGON;

#endif

#ifdef __cplusplus
}
#endif
#endif
