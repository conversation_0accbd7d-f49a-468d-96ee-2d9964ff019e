/*
 * Copyright 2006 <PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "objidl.idl";
import "oleidl.idl";

/*****************************************************************************
 *    IOptionArray interface
 */
[
    local,
    object,
    uuid(22b6d492-0f88-11d1-ba19-00c04fd912d0),
    pointer_default(unique)
]
interface IOptionArray : IUnknown
{
    typedef [unique] IOptionArray *LPOPTIONARRAY;

    HRESULT QueryOption(
        [in] DWORD  dwOption,
        [out, size_is(*pcbBuf)] LPVOID pBuffer,
        [in, out] ULONG *pcbBuf);

    HRESULT SetOption(
        [in] DWORD dwOption,
        [in, size_is(cbBuf)] LPVOID pBuffer,
        [in] ULONG cbBuf);
}

/*****************************************************************************
 *    IHtmlLoadOptions interface
 */
[
    local,
    object,
    uuid(a71a0808-0f88-11d1-ba19-00c04fd912d0),
    pointer_default(unique)
]
interface IHtmlLoadOptions : IOptionArray
{
    typedef enum {
        HTMLLOADOPTION_CODEPAGE,
        HTMLLOADOPTION_INETSHORTCUTPATH,
        HTMLLOADOPTION_HYPERLINK,
        HTMLLOADOPTION_FRAMELOAD
    } HTMLLOADOPTION;
}

cpp_quote("DEFINE_GUID(CLSID_HTMLLoadOptions, 0x18845040, 0x0FA5, 0x11D1, 0xBA,0x19, 0x00,0xC0,0x4F,0xD9,0x12,0xD0);")
