Avoid ctest truncation of output: CTEST_FULL_OUTPUT
========================================================
=== MAIN VARIABLES
========================================================
CMAKE_STATIC_LIBRARY_PREFIX == "${CMAKE_STATIC_LIBRARY_PREFIX}"
CMAKE_STATIC_LIBRARY_SUFFIX == "${CMAKE_STATIC_LIBRARY_SUFFIX}"
CMAKE_SHARED_LIBRARY_PREFIX == "${CMAKE_SHARED_LIBRARY_PREFIX}"
CMAKE_SHARED_LIBRARY_SUFFIX == "${CMAKE_SHARED_LIBRARY_SUFFIX}"
CMAKE_SHARED_LIBRARY_ARCHIVE_SUFFIX == "${CMAKE_SHARED_LIBRARY_ARCHIVE_SUFFIX}"
CMAKE_SHARED_MODULE_PREFIX == "${CMAKE_SHARED_MODULE_PREFIX}"
CMAKE_SHARED_MODULE_SUFFIX == "${CMAKE_SHARED_MODULE_SUFFIX}"


CMAKE_DL_LIBS == "${CMAKE_DL_LIBS}"
CMAKE_LIBRARY_PATH_FLAG == "${CMAKE_LIBRARY_PATH_FLAG}"
CMAKE_LINK_LIBRARY_FLAG == "${CMAKE_LINK_LIBRARY_FLAG}"
CMAKE_SKIP_RPATH == "${CMAKE_SKIP_RPATH}"
CMAKE_SYSTEM_INFO_FILE == "${CMAKE_SYSTEM_INFO_FILE}"
CMAKE_SYSTEM_NAME == "${CMAKE_SYSTEM_NAME}"
CMAKE_SYSTEM == "${CMAKE_SYSTEM}"
CMAKE_CXX_COMPILER == "${CMAKE_CXX_COMPILER}"
CMAKE_C_COMPILER == "${CMAKE_C_COMPILER}"
CMAKE_COMPILER_IS_GNUCC == "${CMAKE_COMPILER_IS_GNUCC}"
CMAKE_COMPILER_IS_GNUCXX == "${CMAKE_COMPILER_IS_GNUCXX}"

// C shared library flag
CMAKE_SHARED_LIBRARY_C_FLAGS == "${CMAKE_SHARED_LIBRARY_C_FLAGS}"
CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS == "${CMAKE_SHARED_LIBRARY_CREATE_C_FLAGS}"
CMAKE_SHARED_LIBRARY_LINK_FLAGS == "${CMAKE_SHARED_LIBRARY_LINK_FLAGS}"
CMAKE_SHARED_LIBRARY_RUNTIME_FLAG == "${CMAKE_SHARED_LIBRARY_RUNTIME_FLAG}"
CMAKE_SHARED_LIBRARY_RUNTIME_FLAG_SEP == "${CMAKE_SHARED_LIBRARY_RUNTIME_FLAG_SEP}"
CMAKE_SHARED_LIBRARY_LINK_STATIC_C_FLAGS == "${CMAKE_SHARED_LIBRARY_LINK_STATIC_C_FLAGS}"
CMAKE_SHARED_LIBRARY_LINK_DYNAMIC_C_FLAGS == "${CMAKE_SHARED_LIBRARY_LINK_DYNAMIC_C_FLAGS}"

// C shared module flags
CMAKE_SHARED_MODULE_C_FLAGS  == "${CMAKE_SHARED_MODULE_C_FLAGS}"
CMAKE_SHARED_MODULE_CREATE_C_FLAGS == "${CMAKE_SHARED_MODULE_CREATE_C_FLAGS}"
CMAKE_SHARED_MODULE_LINK_STATIC_C_FLAGS == "${CMAKE_SHARED_MODULE_LINK_STATIC_C_FLAGS}"
CMAKE_SHARED_MODULE_LINK_DYNAMIC_C_FLAGS == "${CMAKE_SHARED_MODULE_LINK_DYNAMIC_C_FLAGS}"

// C exe flags
CMAKE_EXE_LINK_STATIC_C_FLAGS == "${CMAKE_EXE_LINK_STATIC_C_FLAGS}"
CMAKE_EXE_LINK_DYNAMIC_C_FLAGS == "${CMAKE_EXE_LINK_DYNAMIC_C_FLAGS}"

// CXX shared library flags
CMAKE_SHARED_LIBRARY_CREATE_CXX_FLAGS == "${CMAKE_SHARED_LIBRARY_CREATE_CXX_FLAGS}"
CMAKE_SHARED_LIBRARY_CXX_FLAGS == "${CMAKE_SHARED_LIBRARY_CXX_FLAGS}"
CMAKE_SHARED_LIBRARY_LINK_CXX_FLAGS == "${CMAKE_SHARED_LIBRARY_LINK_CXX_FLAGS}"
CMAKE_SHARED_LIBRARY_RUNTIME_CXX_FLAG == "${CMAKE_SHARED_LIBRARY_RUNTIME_CXX_FLAG}"
CMAKE_SHARED_LIBRARY_RUNTIME_CXX_FLAG_SEP == "${CMAKE_SHARED_LIBRARY_RUNTIME_CXX_FLAG_SEP}"
CMAKE_SHARED_LIBRARY_LINK_STATIC_CXX_FLAGS == "${CMAKE_SHARED_LIBRARY_LINK_STATIC_CXX_FLAGS}"
CMAKE_SHARED_LIBRARY_LINK_DYNAMIC_CXX_FLAGS == "${CMAKE_SHARED_LIBRARY_LINK_DYNAMIC_CXX_FLAGS}"

// CXX shared module flags
CMAKE_SHARED_MODULE_CREATE_CXX_FLAGS == "${CMAKE_SHARED_MODULE_CREATE_CXX_FLAGS}"
CMAKE_SHARED_MODULE_CXX_FLAGS == "${CMAKE_SHARED_MODULE_CXX_FLAGS}"
CMAKE_SHARED_MODULE_LINK_STATIC_CXX_FLAGS == "${CMAKE_SHARED_MODULE_LINK_STATIC_CXX_FLAGS}"
CMAKE_SHARED_MODULE_LINK_DYNAMIC_CXX_FLAGS == "${CMAKE_SHARED_MODULE_LINK_DYNAMIC_CXX_FLAGS}"

// CXX exe flags
CMAKE_EXE_LINK_STATIC_CXX_FLAGS == "${CMAKE_EXE_LINK_STATIC_CXX_FLAGS}"
CMAKE_EXE_LINK_DYNAMIC_CXX_FLAGS == "${CMAKE_EXE_LINK_DYNAMIC_CXX_FLAGS}"

CMAKE_USER_MAKE_RULES_OVERRIDE == "${CMAKE_USER_MAKE_RULES_OVERRIDE}"
CMAKE_VERBOSE_MAKEFILE == "${CMAKE_VERBOSE_MAKEFILE}"
CMAKE_BUILD_TYPE == "${CMAKE_BUILD_TYPE}"
CMAKE_CXX_FLAGS == "${CMAKE_CXX_FLAGS}"
CMAKE_CXX_FLAGS_DEBUG == "${CMAKE_CXX_FLAGS_DEBUG}"
CMAKE_CXX_FLAGS_MINSIZEREL == "${CMAKE_CXX_FLAGS_MINSIZEREL}"
CMAKE_CXX_FLAGS_RELEASE == "${CMAKE_CXX_FLAGS_RELEASE}"
CMAKE_CXX_FLAGS_RELWITHDEBINFO == "${CMAKE_CXX_FLAGS_RELWITHDEBINFO}"

CMAKE_C_FLAGS == "${CMAKE_C_FLAGS}"
CMAKE_C_FLAGS_DEBUG == "${CMAKE_C_FLAGS_DEBUG}"
CMAKE_C_FLAGS_MINSIZEREL == "${CMAKE_C_FLAGS_MINSIZEREL}"
CMAKE_C_FLAGS_RELEASE == "${CMAKE_C_FLAGS_RELEASE}"
CMAKE_C_FLAGS_RELWITHDEBINFO == "${CMAKE_C_FLAGS_RELWITHDEBINFO}"

// build rules
CMAKE_CXX_CREATE_SHARED_LIBRARY == "${CMAKE_CXX_CREATE_SHARED_LIBRARY}"
CMAKE_CXX_CREATE_SHARED_MODULE == "${CMAKE_CXX_CREATE_SHARED_MODULE}"
CMAKE_C_CREATE_SHARED_LIBRARY == "${CMAKE_C_CREATE_SHARED_LIBRARY}"
CMAKE_C_CREATE_SHARED_MODULE == "${CMAKE_C_CREATE_SHARED_MODULE}"
CMAKE_CXX_CREATE_STATIC_LIBRARY == "${CMAKE_CXX_CREATE_STATIC_LIBRARY}"
CMAKE_C_CREATE_STATIC_LIBRARY == "${CMAKE_C_CREATE_STATIC_LIBRARY}"
CMAKE_CXX_COMPILE_OBJECT == "${CMAKE_CXX_COMPILE_OBJECT}"
CMAKE_C_COMPILE_OBJECT == "${CMAKE_C_COMPILE_OBJECT}"
CMAKE_C_LINK_EXECUTABLE == "${CMAKE_C_LINK_EXECUTABLE}"
CMAKE_CXX_LINK_EXECUTABLE == "${CMAKE_CXX_LINK_EXECUTABLE}"
