+{
   locale_version => 1.31,
   entry => <<'ENTRY', # for DUCET v13.0.0
0063 0068 ; [.1FD7.0020.0002] # <LATIN SMALL LETTER C, LATIN SMALL LETTER H>
0043 0068 ; [.1FD7.0020.0007] # <LATIN CAPITAL LETTER C, LATIN SMALL LETTER H>
0043 0048 ; [.1FD7.0020.0008] # <LATIN CAPITAL LETTER C, LATIN CAPITAL LETTER H>
006C 006C ; [.20D7.0020.0002] # <LATIN SMALL LETTER L, LATIN SMALL LETTER L>
004C 006C ; [.20D7.0020.0007] # <LATIN CAPITAL LETTER L, LATIN SMALL LETTER L>
004C 004C ; [.20D7.0020.0008] # <LATIN CAPITAL LETTER L, LATIN CAPITAL LETTER L>
00F1      ; [.2119.0020.0002] # LATIN SMALL LETTER N WITH TILDE
006E 0303 ; [.2119.0020.0002] # LATIN SMALL LETTER N WITH TILDE
00D1      ; [.2119.0020.0008] # LATIN CAPITAL LETTER N WITH TILDE
004E 0303 ; [.2119.0020.0008] # LATIN CAPITAL LETTER N WITH TILDE
ENTRY
};
