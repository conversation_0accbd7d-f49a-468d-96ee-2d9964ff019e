// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/node_def.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/full_type.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto;
namespace tensorflow {
class NodeDef;
struct NodeDefDefaultTypeInternal;
extern NodeDefDefaultTypeInternal _NodeDef_default_instance_;
class NodeDef_AttrEntry_DoNotUse;
struct NodeDef_AttrEntry_DoNotUseDefaultTypeInternal;
extern NodeDef_AttrEntry_DoNotUseDefaultTypeInternal _NodeDef_AttrEntry_DoNotUse_default_instance_;
class NodeDef_ExperimentalDebugInfo;
struct NodeDef_ExperimentalDebugInfoDefaultTypeInternal;
extern NodeDef_ExperimentalDebugInfoDefaultTypeInternal _NodeDef_ExperimentalDebugInfo_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::NodeDef* Arena::CreateMaybeMessage<::tensorflow::NodeDef>(Arena*);
template<> ::tensorflow::NodeDef_AttrEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::NodeDef_AttrEntry_DoNotUse>(Arena*);
template<> ::tensorflow::NodeDef_ExperimentalDebugInfo* Arena::CreateMaybeMessage<::tensorflow::NodeDef_ExperimentalDebugInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class NodeDef_AttrEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<NodeDef_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<NodeDef_AttrEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  NodeDef_AttrEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR NodeDef_AttrEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit NodeDef_AttrEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const NodeDef_AttrEntry_DoNotUse& other);
  static const NodeDef_AttrEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const NodeDef_AttrEntry_DoNotUse*>(&_NodeDef_AttrEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.NodeDef.AttrEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto;
};

// -------------------------------------------------------------------

class NodeDef_ExperimentalDebugInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NodeDef.ExperimentalDebugInfo) */ {
 public:
  inline NodeDef_ExperimentalDebugInfo() : NodeDef_ExperimentalDebugInfo(nullptr) {}
  ~NodeDef_ExperimentalDebugInfo() override;
  explicit PROTOBUF_CONSTEXPR NodeDef_ExperimentalDebugInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NodeDef_ExperimentalDebugInfo(const NodeDef_ExperimentalDebugInfo& from);
  NodeDef_ExperimentalDebugInfo(NodeDef_ExperimentalDebugInfo&& from) noexcept
    : NodeDef_ExperimentalDebugInfo() {
    *this = ::std::move(from);
  }

  inline NodeDef_ExperimentalDebugInfo& operator=(const NodeDef_ExperimentalDebugInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeDef_ExperimentalDebugInfo& operator=(NodeDef_ExperimentalDebugInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeDef_ExperimentalDebugInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeDef_ExperimentalDebugInfo* internal_default_instance() {
    return reinterpret_cast<const NodeDef_ExperimentalDebugInfo*>(
               &_NodeDef_ExperimentalDebugInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(NodeDef_ExperimentalDebugInfo& a, NodeDef_ExperimentalDebugInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeDef_ExperimentalDebugInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeDef_ExperimentalDebugInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeDef_ExperimentalDebugInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NodeDef_ExperimentalDebugInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NodeDef_ExperimentalDebugInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NodeDef_ExperimentalDebugInfo& from) {
    NodeDef_ExperimentalDebugInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeDef_ExperimentalDebugInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NodeDef.ExperimentalDebugInfo";
  }
  protected:
  explicit NodeDef_ExperimentalDebugInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOriginalNodeNamesFieldNumber = 1,
    kOriginalFuncNamesFieldNumber = 2,
  };
  // repeated string original_node_names = 1;
  int original_node_names_size() const;
  private:
  int _internal_original_node_names_size() const;
  public:
  void clear_original_node_names();
  const std::string& original_node_names(int index) const;
  std::string* mutable_original_node_names(int index);
  void set_original_node_names(int index, const std::string& value);
  void set_original_node_names(int index, std::string&& value);
  void set_original_node_names(int index, const char* value);
  void set_original_node_names(int index, const char* value, size_t size);
  std::string* add_original_node_names();
  void add_original_node_names(const std::string& value);
  void add_original_node_names(std::string&& value);
  void add_original_node_names(const char* value);
  void add_original_node_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& original_node_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_original_node_names();
  private:
  const std::string& _internal_original_node_names(int index) const;
  std::string* _internal_add_original_node_names();
  public:

  // repeated string original_func_names = 2;
  int original_func_names_size() const;
  private:
  int _internal_original_func_names_size() const;
  public:
  void clear_original_func_names();
  const std::string& original_func_names(int index) const;
  std::string* mutable_original_func_names(int index);
  void set_original_func_names(int index, const std::string& value);
  void set_original_func_names(int index, std::string&& value);
  void set_original_func_names(int index, const char* value);
  void set_original_func_names(int index, const char* value, size_t size);
  std::string* add_original_func_names();
  void add_original_func_names(const std::string& value);
  void add_original_func_names(std::string&& value);
  void add_original_func_names(const char* value);
  void add_original_func_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& original_func_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_original_func_names();
  private:
  const std::string& _internal_original_func_names(int index) const;
  std::string* _internal_add_original_func_names();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.NodeDef.ExperimentalDebugInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> original_node_names_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> original_func_names_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto;
};
// -------------------------------------------------------------------

class NodeDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NodeDef) */ {
 public:
  inline NodeDef() : NodeDef(nullptr) {}
  ~NodeDef() override;
  explicit PROTOBUF_CONSTEXPR NodeDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NodeDef(const NodeDef& from);
  NodeDef(NodeDef&& from) noexcept
    : NodeDef() {
    *this = ::std::move(from);
  }

  inline NodeDef& operator=(const NodeDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeDef& operator=(NodeDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeDef* internal_default_instance() {
    return reinterpret_cast<const NodeDef*>(
               &_NodeDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(NodeDef& a, NodeDef& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NodeDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NodeDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NodeDef& from) {
    NodeDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NodeDef";
  }
  protected:
  explicit NodeDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef NodeDef_ExperimentalDebugInfo ExperimentalDebugInfo;

  // accessors -------------------------------------------------------

  enum : int {
    kInputFieldNumber = 3,
    kAttrFieldNumber = 5,
    kNameFieldNumber = 1,
    kOpFieldNumber = 2,
    kDeviceFieldNumber = 4,
    kExperimentalDebugInfoFieldNumber = 6,
    kExperimentalTypeFieldNumber = 7,
  };
  // repeated string input = 3;
  int input_size() const;
  private:
  int _internal_input_size() const;
  public:
  void clear_input();
  const std::string& input(int index) const;
  std::string* mutable_input(int index);
  void set_input(int index, const std::string& value);
  void set_input(int index, std::string&& value);
  void set_input(int index, const char* value);
  void set_input(int index, const char* value, size_t size);
  std::string* add_input();
  void add_input(const std::string& value);
  void add_input(std::string&& value);
  void add_input(const char* value);
  void add_input(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& input() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_input();
  private:
  const std::string& _internal_input(int index) const;
  std::string* _internal_add_input();
  public:

  // map<string, .tensorflow.AttrValue> attr = 5;
  int attr_size() const;
  private:
  int _internal_attr_size() const;
  public:
  void clear_attr();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      _internal_attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      _internal_mutable_attr();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attr() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attr();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string op = 2;
  void clear_op();
  const std::string& op() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op();
  PROTOBUF_NODISCARD std::string* release_op();
  void set_allocated_op(std::string* op);
  private:
  const std::string& _internal_op() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op(const std::string& value);
  std::string* _internal_mutable_op();
  public:

  // string device = 4;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // .tensorflow.NodeDef.ExperimentalDebugInfo experimental_debug_info = 6;
  bool has_experimental_debug_info() const;
  private:
  bool _internal_has_experimental_debug_info() const;
  public:
  void clear_experimental_debug_info();
  const ::tensorflow::NodeDef_ExperimentalDebugInfo& experimental_debug_info() const;
  PROTOBUF_NODISCARD ::tensorflow::NodeDef_ExperimentalDebugInfo* release_experimental_debug_info();
  ::tensorflow::NodeDef_ExperimentalDebugInfo* mutable_experimental_debug_info();
  void set_allocated_experimental_debug_info(::tensorflow::NodeDef_ExperimentalDebugInfo* experimental_debug_info);
  private:
  const ::tensorflow::NodeDef_ExperimentalDebugInfo& _internal_experimental_debug_info() const;
  ::tensorflow::NodeDef_ExperimentalDebugInfo* _internal_mutable_experimental_debug_info();
  public:
  void unsafe_arena_set_allocated_experimental_debug_info(
      ::tensorflow::NodeDef_ExperimentalDebugInfo* experimental_debug_info);
  ::tensorflow::NodeDef_ExperimentalDebugInfo* unsafe_arena_release_experimental_debug_info();

  // .tensorflow.FullTypeDef experimental_type = 7;
  bool has_experimental_type() const;
  private:
  bool _internal_has_experimental_type() const;
  public:
  void clear_experimental_type();
  const ::tensorflow::FullTypeDef& experimental_type() const;
  PROTOBUF_NODISCARD ::tensorflow::FullTypeDef* release_experimental_type();
  ::tensorflow::FullTypeDef* mutable_experimental_type();
  void set_allocated_experimental_type(::tensorflow::FullTypeDef* experimental_type);
  private:
  const ::tensorflow::FullTypeDef& _internal_experimental_type() const;
  ::tensorflow::FullTypeDef* _internal_mutable_experimental_type();
  public:
  void unsafe_arena_set_allocated_experimental_type(
      ::tensorflow::FullTypeDef* experimental_type);
  ::tensorflow::FullTypeDef* unsafe_arena_release_experimental_type();

  // @@protoc_insertion_point(class_scope:tensorflow.NodeDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> input_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        NodeDef_AttrEntry_DoNotUse,
        std::string, ::tensorflow::AttrValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attr_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    ::tensorflow::NodeDef_ExperimentalDebugInfo* experimental_debug_info_;
    ::tensorflow::FullTypeDef* experimental_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// NodeDef_ExperimentalDebugInfo

// repeated string original_node_names = 1;
inline int NodeDef_ExperimentalDebugInfo::_internal_original_node_names_size() const {
  return _impl_.original_node_names_.size();
}
inline int NodeDef_ExperimentalDebugInfo::original_node_names_size() const {
  return _internal_original_node_names_size();
}
inline void NodeDef_ExperimentalDebugInfo::clear_original_node_names() {
  _impl_.original_node_names_.Clear();
}
inline std::string* NodeDef_ExperimentalDebugInfo::add_original_node_names() {
  std::string* _s = _internal_add_original_node_names();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
  return _s;
}
inline const std::string& NodeDef_ExperimentalDebugInfo::_internal_original_node_names(int index) const {
  return _impl_.original_node_names_.Get(index);
}
inline const std::string& NodeDef_ExperimentalDebugInfo::original_node_names(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
  return _internal_original_node_names(index);
}
inline std::string* NodeDef_ExperimentalDebugInfo::mutable_original_node_names(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
  return _impl_.original_node_names_.Mutable(index);
}
inline void NodeDef_ExperimentalDebugInfo::set_original_node_names(int index, const std::string& value) {
  _impl_.original_node_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
}
inline void NodeDef_ExperimentalDebugInfo::set_original_node_names(int index, std::string&& value) {
  _impl_.original_node_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
}
inline void NodeDef_ExperimentalDebugInfo::set_original_node_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.original_node_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
}
inline void NodeDef_ExperimentalDebugInfo::set_original_node_names(int index, const char* value, size_t size) {
  _impl_.original_node_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
}
inline std::string* NodeDef_ExperimentalDebugInfo::_internal_add_original_node_names() {
  return _impl_.original_node_names_.Add();
}
inline void NodeDef_ExperimentalDebugInfo::add_original_node_names(const std::string& value) {
  _impl_.original_node_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
}
inline void NodeDef_ExperimentalDebugInfo::add_original_node_names(std::string&& value) {
  _impl_.original_node_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
}
inline void NodeDef_ExperimentalDebugInfo::add_original_node_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.original_node_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
}
inline void NodeDef_ExperimentalDebugInfo::add_original_node_names(const char* value, size_t size) {
  _impl_.original_node_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NodeDef_ExperimentalDebugInfo::original_node_names() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
  return _impl_.original_node_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NodeDef_ExperimentalDebugInfo::mutable_original_node_names() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeDef.ExperimentalDebugInfo.original_node_names)
  return &_impl_.original_node_names_;
}

// repeated string original_func_names = 2;
inline int NodeDef_ExperimentalDebugInfo::_internal_original_func_names_size() const {
  return _impl_.original_func_names_.size();
}
inline int NodeDef_ExperimentalDebugInfo::original_func_names_size() const {
  return _internal_original_func_names_size();
}
inline void NodeDef_ExperimentalDebugInfo::clear_original_func_names() {
  _impl_.original_func_names_.Clear();
}
inline std::string* NodeDef_ExperimentalDebugInfo::add_original_func_names() {
  std::string* _s = _internal_add_original_func_names();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
  return _s;
}
inline const std::string& NodeDef_ExperimentalDebugInfo::_internal_original_func_names(int index) const {
  return _impl_.original_func_names_.Get(index);
}
inline const std::string& NodeDef_ExperimentalDebugInfo::original_func_names(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
  return _internal_original_func_names(index);
}
inline std::string* NodeDef_ExperimentalDebugInfo::mutable_original_func_names(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
  return _impl_.original_func_names_.Mutable(index);
}
inline void NodeDef_ExperimentalDebugInfo::set_original_func_names(int index, const std::string& value) {
  _impl_.original_func_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
}
inline void NodeDef_ExperimentalDebugInfo::set_original_func_names(int index, std::string&& value) {
  _impl_.original_func_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
}
inline void NodeDef_ExperimentalDebugInfo::set_original_func_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.original_func_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
}
inline void NodeDef_ExperimentalDebugInfo::set_original_func_names(int index, const char* value, size_t size) {
  _impl_.original_func_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
}
inline std::string* NodeDef_ExperimentalDebugInfo::_internal_add_original_func_names() {
  return _impl_.original_func_names_.Add();
}
inline void NodeDef_ExperimentalDebugInfo::add_original_func_names(const std::string& value) {
  _impl_.original_func_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
}
inline void NodeDef_ExperimentalDebugInfo::add_original_func_names(std::string&& value) {
  _impl_.original_func_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
}
inline void NodeDef_ExperimentalDebugInfo::add_original_func_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.original_func_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
}
inline void NodeDef_ExperimentalDebugInfo::add_original_func_names(const char* value, size_t size) {
  _impl_.original_func_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NodeDef_ExperimentalDebugInfo::original_func_names() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
  return _impl_.original_func_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NodeDef_ExperimentalDebugInfo::mutable_original_func_names() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeDef.ExperimentalDebugInfo.original_func_names)
  return &_impl_.original_func_names_;
}

// -------------------------------------------------------------------

// NodeDef

// string name = 1;
inline void NodeDef::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& NodeDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeDef.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeDef::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.name)
}
inline std::string* NodeDef::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeDef.name)
  return _s;
}
inline const std::string& NodeDef::_internal_name() const {
  return _impl_.name_.Get();
}
inline void NodeDef::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* NodeDef::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* NodeDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeDef.name)
  return _impl_.name_.Release();
}
inline void NodeDef::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeDef.name)
}

// string op = 2;
inline void NodeDef::clear_op() {
  _impl_.op_.ClearToEmpty();
}
inline const std::string& NodeDef::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeDef.op)
  return _internal_op();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeDef::set_op(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.op)
}
inline std::string* NodeDef::mutable_op() {
  std::string* _s = _internal_mutable_op();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeDef.op)
  return _s;
}
inline const std::string& NodeDef::_internal_op() const {
  return _impl_.op_.Get();
}
inline void NodeDef::_internal_set_op(const std::string& value) {
  
  _impl_.op_.Set(value, GetArenaForAllocation());
}
inline std::string* NodeDef::_internal_mutable_op() {
  
  return _impl_.op_.Mutable(GetArenaForAllocation());
}
inline std::string* NodeDef::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeDef.op)
  return _impl_.op_.Release();
}
inline void NodeDef::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  _impl_.op_.SetAllocated(op, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_.IsDefault()) {
    _impl_.op_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeDef.op)
}

// repeated string input = 3;
inline int NodeDef::_internal_input_size() const {
  return _impl_.input_.size();
}
inline int NodeDef::input_size() const {
  return _internal_input_size();
}
inline void NodeDef::clear_input() {
  _impl_.input_.Clear();
}
inline std::string* NodeDef::add_input() {
  std::string* _s = _internal_add_input();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.NodeDef.input)
  return _s;
}
inline const std::string& NodeDef::_internal_input(int index) const {
  return _impl_.input_.Get(index);
}
inline const std::string& NodeDef::input(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeDef.input)
  return _internal_input(index);
}
inline std::string* NodeDef::mutable_input(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeDef.input)
  return _impl_.input_.Mutable(index);
}
inline void NodeDef::set_input(int index, const std::string& value) {
  _impl_.input_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.input)
}
inline void NodeDef::set_input(int index, std::string&& value) {
  _impl_.input_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.input)
}
inline void NodeDef::set_input(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.input_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.NodeDef.input)
}
inline void NodeDef::set_input(int index, const char* value, size_t size) {
  _impl_.input_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.NodeDef.input)
}
inline std::string* NodeDef::_internal_add_input() {
  return _impl_.input_.Add();
}
inline void NodeDef::add_input(const std::string& value) {
  _impl_.input_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.NodeDef.input)
}
inline void NodeDef::add_input(std::string&& value) {
  _impl_.input_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.NodeDef.input)
}
inline void NodeDef::add_input(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.input_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.NodeDef.input)
}
inline void NodeDef::add_input(const char* value, size_t size) {
  _impl_.input_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.NodeDef.input)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
NodeDef::input() const {
  // @@protoc_insertion_point(field_list:tensorflow.NodeDef.input)
  return _impl_.input_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
NodeDef::mutable_input() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NodeDef.input)
  return &_impl_.input_;
}

// string device = 4;
inline void NodeDef::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& NodeDef::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeDef.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeDef::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NodeDef.device)
}
inline std::string* NodeDef::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeDef.device)
  return _s;
}
inline const std::string& NodeDef::_internal_device() const {
  return _impl_.device_.Get();
}
inline void NodeDef::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* NodeDef::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* NodeDef::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeDef.device)
  return _impl_.device_.Release();
}
inline void NodeDef::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeDef.device)
}

// map<string, .tensorflow.AttrValue> attr = 5;
inline int NodeDef::_internal_attr_size() const {
  return _impl_.attr_.size();
}
inline int NodeDef::attr_size() const {
  return _internal_attr_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
NodeDef::_internal_attr() const {
  return _impl_.attr_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
NodeDef::attr() const {
  // @@protoc_insertion_point(field_map:tensorflow.NodeDef.attr)
  return _internal_attr();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
NodeDef::_internal_mutable_attr() {
  return _impl_.attr_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
NodeDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.NodeDef.attr)
  return _internal_mutable_attr();
}

// .tensorflow.NodeDef.ExperimentalDebugInfo experimental_debug_info = 6;
inline bool NodeDef::_internal_has_experimental_debug_info() const {
  return this != internal_default_instance() && _impl_.experimental_debug_info_ != nullptr;
}
inline bool NodeDef::has_experimental_debug_info() const {
  return _internal_has_experimental_debug_info();
}
inline void NodeDef::clear_experimental_debug_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.experimental_debug_info_ != nullptr) {
    delete _impl_.experimental_debug_info_;
  }
  _impl_.experimental_debug_info_ = nullptr;
}
inline const ::tensorflow::NodeDef_ExperimentalDebugInfo& NodeDef::_internal_experimental_debug_info() const {
  const ::tensorflow::NodeDef_ExperimentalDebugInfo* p = _impl_.experimental_debug_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::NodeDef_ExperimentalDebugInfo&>(
      ::tensorflow::_NodeDef_ExperimentalDebugInfo_default_instance_);
}
inline const ::tensorflow::NodeDef_ExperimentalDebugInfo& NodeDef::experimental_debug_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeDef.experimental_debug_info)
  return _internal_experimental_debug_info();
}
inline void NodeDef::unsafe_arena_set_allocated_experimental_debug_info(
    ::tensorflow::NodeDef_ExperimentalDebugInfo* experimental_debug_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.experimental_debug_info_);
  }
  _impl_.experimental_debug_info_ = experimental_debug_info;
  if (experimental_debug_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeDef.experimental_debug_info)
}
inline ::tensorflow::NodeDef_ExperimentalDebugInfo* NodeDef::release_experimental_debug_info() {
  
  ::tensorflow::NodeDef_ExperimentalDebugInfo* temp = _impl_.experimental_debug_info_;
  _impl_.experimental_debug_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::NodeDef_ExperimentalDebugInfo* NodeDef::unsafe_arena_release_experimental_debug_info() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeDef.experimental_debug_info)
  
  ::tensorflow::NodeDef_ExperimentalDebugInfo* temp = _impl_.experimental_debug_info_;
  _impl_.experimental_debug_info_ = nullptr;
  return temp;
}
inline ::tensorflow::NodeDef_ExperimentalDebugInfo* NodeDef::_internal_mutable_experimental_debug_info() {
  
  if (_impl_.experimental_debug_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::NodeDef_ExperimentalDebugInfo>(GetArenaForAllocation());
    _impl_.experimental_debug_info_ = p;
  }
  return _impl_.experimental_debug_info_;
}
inline ::tensorflow::NodeDef_ExperimentalDebugInfo* NodeDef::mutable_experimental_debug_info() {
  ::tensorflow::NodeDef_ExperimentalDebugInfo* _msg = _internal_mutable_experimental_debug_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeDef.experimental_debug_info)
  return _msg;
}
inline void NodeDef::set_allocated_experimental_debug_info(::tensorflow::NodeDef_ExperimentalDebugInfo* experimental_debug_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.experimental_debug_info_;
  }
  if (experimental_debug_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(experimental_debug_info);
    if (message_arena != submessage_arena) {
      experimental_debug_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental_debug_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.experimental_debug_info_ = experimental_debug_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeDef.experimental_debug_info)
}

// .tensorflow.FullTypeDef experimental_type = 7;
inline bool NodeDef::_internal_has_experimental_type() const {
  return this != internal_default_instance() && _impl_.experimental_type_ != nullptr;
}
inline bool NodeDef::has_experimental_type() const {
  return _internal_has_experimental_type();
}
inline const ::tensorflow::FullTypeDef& NodeDef::_internal_experimental_type() const {
  const ::tensorflow::FullTypeDef* p = _impl_.experimental_type_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::FullTypeDef&>(
      ::tensorflow::_FullTypeDef_default_instance_);
}
inline const ::tensorflow::FullTypeDef& NodeDef::experimental_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.NodeDef.experimental_type)
  return _internal_experimental_type();
}
inline void NodeDef::unsafe_arena_set_allocated_experimental_type(
    ::tensorflow::FullTypeDef* experimental_type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.experimental_type_);
  }
  _impl_.experimental_type_ = experimental_type;
  if (experimental_type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NodeDef.experimental_type)
}
inline ::tensorflow::FullTypeDef* NodeDef::release_experimental_type() {
  
  ::tensorflow::FullTypeDef* temp = _impl_.experimental_type_;
  _impl_.experimental_type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::FullTypeDef* NodeDef::unsafe_arena_release_experimental_type() {
  // @@protoc_insertion_point(field_release:tensorflow.NodeDef.experimental_type)
  
  ::tensorflow::FullTypeDef* temp = _impl_.experimental_type_;
  _impl_.experimental_type_ = nullptr;
  return temp;
}
inline ::tensorflow::FullTypeDef* NodeDef::_internal_mutable_experimental_type() {
  
  if (_impl_.experimental_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FullTypeDef>(GetArenaForAllocation());
    _impl_.experimental_type_ = p;
  }
  return _impl_.experimental_type_;
}
inline ::tensorflow::FullTypeDef* NodeDef::mutable_experimental_type() {
  ::tensorflow::FullTypeDef* _msg = _internal_mutable_experimental_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.NodeDef.experimental_type)
  return _msg;
}
inline void NodeDef::set_allocated_experimental_type(::tensorflow::FullTypeDef* experimental_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.experimental_type_);
  }
  if (experimental_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(experimental_type));
    if (message_arena != submessage_arena) {
      experimental_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental_type, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.experimental_type_ = experimental_type;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NodeDef.experimental_type)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fnode_5fdef_2eproto
