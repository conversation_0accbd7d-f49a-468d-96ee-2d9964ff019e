/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCL<PERSON>IMER within this package.
 */

import "naptypes.idl";
import "unknwn.idl";

cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

interface INapEnforcementClientBinding;
interface INapEnforcementClientCallback;
interface INapEnforcementClientConnection;
interface INapEnforcementClientConnection2;

[object, uuid (92b93223-7487-42d9-9a91-5b8507720384), pointer_default (unique)]
interface INapEnforcementClientBinding : IUnknown {
  HRESULT Initialize ([in] EnforcementEntityId id,[in] INapEnforcementClientCallback *callback);
  HRESULT Uninitialize ();
  HRESULT CreateConnection ([out] INapEnforcementClientConnection **connection);
  HRESULT GetSoHRequest ([in] INapEnforcementClientConnection *connection,[out] WINBOOL *retriggerHint);
  HRESULT ProcessSoHResponse ([in] INapEnforcementClientConnection *connection);
  HRESULT NotifyConnectionStateDown ([in] INapEnforcementClientConnection *downCxn);
  HRESULT NotifySoHChangeFailure ();
};

[object, uuid (F5A0B90A-83a1-4f76-BA3F-025418682814), pointer_default (unique)]
interface INapEnforcementClientCallback : IUnknown {
  typedef struct tagConnections {
    [range (0, maxConnectionCountPerEnforcer)] UINT16 count;
    [size_is (count)] INapEnforcementClientConnection **connections;
  } Connections;

  HRESULT NotifySoHChange ();
  HRESULT GetConnections ([out] Connections **connections);
};

[object, uuid (FB3A3505-DDB1-468a-B307-F328A57419D8), pointer_default (unique)]
interface INapEnforcementClientConnection : IUnknown {
  const UINT32 defaultProtocolMaxSize = 4000;
  const UINT32 minProtocolMaxSize = 300;
  const UINT32 maxProtocolMaxSize = 0xffff;

  typedef [range (minProtocolMaxSize, maxProtocolMaxSize)] UINT32 ProtocolMaxSize;

  HRESULT Initialize ([in] EnforcementEntityId id);
  HRESULT SetMaxSize ([in] ProtocolMaxSize maxSize);
  HRESULT GetMaxSize ([out] ProtocolMaxSize *maxSize);
  HRESULT SetFlags ([in] UINT8 flags);
  HRESULT GetFlags ([out] UINT8 *flags);
  HRESULT SetConnectionId ([in] const ConnectionId *connectionId);
  HRESULT GetConnectionId ([out] ConnectionId **connectionId);
  HRESULT GetCorrelationId ([out] CorrelationId *correlationId);
  HRESULT GetStringCorrelationId ([out] StringCorrelationId **correlationId);
  HRESULT SetCorrelationId ([in] CorrelationId correlationId);
  HRESULT SetSoHRequest ([in, unique] const NetworkSoHRequest *sohRequest);
  HRESULT GetSoHRequest ([out] NetworkSoHRequest **sohRequest);
  HRESULT SetSoHResponse ([in] const NetworkSoHResponse *sohResponse);
  HRESULT GetSoHResponse ([out] NetworkSoHResponse **sohResponse);
  HRESULT SetIsolationInfo ([in] const IsolationInfo *isolationInfo);
  HRESULT GetIsolationInfo ([out] IsolationInfo **isolationInfo);
  HRESULT SetPrivateData ([in] const PrivateData *privateData);
  HRESULT GetPrivateData ([out] PrivateData **privateData);
  HRESULT SetEnforcerPrivateData ([in] const PrivateData *privateData);
  HRESULT GetEnforcerPrivateData ([out] PrivateData **privateData);
};

[object, uuid (bd244906-70dd-4690-beea-************), pointer_default (unique)]
interface INapEnforcementClientConnection2 : INapEnforcementClientConnection {
  HRESULT SetIsolationInfoEx ([in] const IsolationInfoEx *isolationInfo);
  HRESULT GetIsolationInfoEx ([out] IsolationInfoEx **isolationInfo);
  HRESULT GetInstalledShvs ([out] SystemHealthEntityCount *count,[out, size_is (,*count)] SystemHealthEntityId **ids);
  HRESULT SetInstalledShvs ([in] SystemHealthEntityCount count,[in, size_is (count)] SystemHealthEntityId *ids);
};

cpp_quote("EXTERN_C const CLSID CLSID_NapEnforcementClientBinding;")
cpp_quote("#endif")
