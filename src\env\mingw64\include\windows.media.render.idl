/*
 * Copyright (C) 2023 B<PERSON>wa<PERSON><PERSON>yo <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "windows.foundation.idl";

namespace Windows.Media.Render {
    typedef enum AudioRenderCategory AudioRenderCategory;

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum AudioRenderCategory
    {
        Other                  = 0,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        ForegroundOnlyMedia    = 1,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        BackgroundCapableMedia = 2,
        Communications         = 3,
        Alerts                 = 4,
        SoundEffects           = 5,
        GameEffects            = 6,
        GameMedia              = 7,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        GameChat               = 8,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Speech                 = 9,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Movie                  = 10,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Media                  = 11,
    };
}
