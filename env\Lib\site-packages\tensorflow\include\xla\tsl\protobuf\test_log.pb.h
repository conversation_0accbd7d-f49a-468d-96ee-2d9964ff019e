// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/tsl/protobuf/test_log.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
namespace tensorflow {
class AvailableDeviceInfo;
struct AvailableDeviceInfoDefaultTypeInternal;
extern AvailableDeviceInfoDefaultTypeInternal _AvailableDeviceInfo_default_instance_;
class BenchmarkEntries;
struct BenchmarkEntriesDefaultTypeInternal;
extern BenchmarkEntriesDefaultTypeInternal _BenchmarkEntries_default_instance_;
class BenchmarkEntry;
struct BenchmarkEntryDefaultTypeInternal;
extern BenchmarkEntryDefaultTypeInternal _BenchmarkEntry_default_instance_;
class BenchmarkEntry_ExtrasEntry_DoNotUse;
struct BenchmarkEntry_ExtrasEntry_DoNotUseDefaultTypeInternal;
extern BenchmarkEntry_ExtrasEntry_DoNotUseDefaultTypeInternal _BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_;
class BuildConfiguration;
struct BuildConfigurationDefaultTypeInternal;
extern BuildConfigurationDefaultTypeInternal _BuildConfiguration_default_instance_;
class CPUInfo;
struct CPUInfoDefaultTypeInternal;
extern CPUInfoDefaultTypeInternal _CPUInfo_default_instance_;
class CPUInfo_CacheSizeEntry_DoNotUse;
struct CPUInfo_CacheSizeEntry_DoNotUseDefaultTypeInternal;
extern CPUInfo_CacheSizeEntry_DoNotUseDefaultTypeInternal _CPUInfo_CacheSizeEntry_DoNotUse_default_instance_;
class CommitId;
struct CommitIdDefaultTypeInternal;
extern CommitIdDefaultTypeInternal _CommitId_default_instance_;
class EntryValue;
struct EntryValueDefaultTypeInternal;
extern EntryValueDefaultTypeInternal _EntryValue_default_instance_;
class GPUInfo;
struct GPUInfoDefaultTypeInternal;
extern GPUInfoDefaultTypeInternal _GPUInfo_default_instance_;
class MachineConfiguration;
struct MachineConfigurationDefaultTypeInternal;
extern MachineConfigurationDefaultTypeInternal _MachineConfiguration_default_instance_;
class MemoryInfo;
struct MemoryInfoDefaultTypeInternal;
extern MemoryInfoDefaultTypeInternal _MemoryInfo_default_instance_;
class MetricEntry;
struct MetricEntryDefaultTypeInternal;
extern MetricEntryDefaultTypeInternal _MetricEntry_default_instance_;
class PlatformInfo;
struct PlatformInfoDefaultTypeInternal;
extern PlatformInfoDefaultTypeInternal _PlatformInfo_default_instance_;
class RunConfiguration;
struct RunConfigurationDefaultTypeInternal;
extern RunConfigurationDefaultTypeInternal _RunConfiguration_default_instance_;
class RunConfiguration_EnvVarsEntry_DoNotUse;
struct RunConfiguration_EnvVarsEntry_DoNotUseDefaultTypeInternal;
extern RunConfiguration_EnvVarsEntry_DoNotUseDefaultTypeInternal _RunConfiguration_EnvVarsEntry_DoNotUse_default_instance_;
class TestResults;
struct TestResultsDefaultTypeInternal;
extern TestResultsDefaultTypeInternal _TestResults_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AvailableDeviceInfo* Arena::CreateMaybeMessage<::tensorflow::AvailableDeviceInfo>(Arena*);
template<> ::tensorflow::BenchmarkEntries* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntries>(Arena*);
template<> ::tensorflow::BenchmarkEntry* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntry>(Arena*);
template<> ::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::BenchmarkEntry_ExtrasEntry_DoNotUse>(Arena*);
template<> ::tensorflow::BuildConfiguration* Arena::CreateMaybeMessage<::tensorflow::BuildConfiguration>(Arena*);
template<> ::tensorflow::CPUInfo* Arena::CreateMaybeMessage<::tensorflow::CPUInfo>(Arena*);
template<> ::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::CPUInfo_CacheSizeEntry_DoNotUse>(Arena*);
template<> ::tensorflow::CommitId* Arena::CreateMaybeMessage<::tensorflow::CommitId>(Arena*);
template<> ::tensorflow::EntryValue* Arena::CreateMaybeMessage<::tensorflow::EntryValue>(Arena*);
template<> ::tensorflow::GPUInfo* Arena::CreateMaybeMessage<::tensorflow::GPUInfo>(Arena*);
template<> ::tensorflow::MachineConfiguration* Arena::CreateMaybeMessage<::tensorflow::MachineConfiguration>(Arena*);
template<> ::tensorflow::MemoryInfo* Arena::CreateMaybeMessage<::tensorflow::MemoryInfo>(Arena*);
template<> ::tensorflow::MetricEntry* Arena::CreateMaybeMessage<::tensorflow::MetricEntry>(Arena*);
template<> ::tensorflow::PlatformInfo* Arena::CreateMaybeMessage<::tensorflow::PlatformInfo>(Arena*);
template<> ::tensorflow::RunConfiguration* Arena::CreateMaybeMessage<::tensorflow::RunConfiguration>(Arena*);
template<> ::tensorflow::RunConfiguration_EnvVarsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::RunConfiguration_EnvVarsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::TestResults* Arena::CreateMaybeMessage<::tensorflow::TestResults>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum TestResults_BenchmarkType : int {
  TestResults_BenchmarkType_UNKNOWN = 0,
  TestResults_BenchmarkType_CPP_MICROBENCHMARK = 1,
  TestResults_BenchmarkType_PYTHON_BENCHMARK = 2,
  TestResults_BenchmarkType_ANDROID_BENCHMARK = 3,
  TestResults_BenchmarkType_EDGE_BENCHMARK = 4,
  TestResults_BenchmarkType_IOS_BENCHMARK = 5,
  TestResults_BenchmarkType_TestResults_BenchmarkType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TestResults_BenchmarkType_TestResults_BenchmarkType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TestResults_BenchmarkType_IsValid(int value);
constexpr TestResults_BenchmarkType TestResults_BenchmarkType_BenchmarkType_MIN = TestResults_BenchmarkType_UNKNOWN;
constexpr TestResults_BenchmarkType TestResults_BenchmarkType_BenchmarkType_MAX = TestResults_BenchmarkType_IOS_BENCHMARK;
constexpr int TestResults_BenchmarkType_BenchmarkType_ARRAYSIZE = TestResults_BenchmarkType_BenchmarkType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TestResults_BenchmarkType_descriptor();
template<typename T>
inline const std::string& TestResults_BenchmarkType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TestResults_BenchmarkType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TestResults_BenchmarkType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TestResults_BenchmarkType_descriptor(), enum_t_value);
}
inline bool TestResults_BenchmarkType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TestResults_BenchmarkType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TestResults_BenchmarkType>(
    TestResults_BenchmarkType_descriptor(), name, value);
}
// ===================================================================

class EntryValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.EntryValue) */ {
 public:
  inline EntryValue() : EntryValue(nullptr) {}
  ~EntryValue() override;
  explicit PROTOBUF_CONSTEXPR EntryValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EntryValue(const EntryValue& from);
  EntryValue(EntryValue&& from) noexcept
    : EntryValue() {
    *this = ::std::move(from);
  }

  inline EntryValue& operator=(const EntryValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline EntryValue& operator=(EntryValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EntryValue& default_instance() {
    return *internal_default_instance();
  }
  enum KindCase {
    kDoubleValue = 1,
    kStringValue = 2,
    KIND_NOT_SET = 0,
  };

  static inline const EntryValue* internal_default_instance() {
    return reinterpret_cast<const EntryValue*>(
               &_EntryValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(EntryValue& a, EntryValue& b) {
    a.Swap(&b);
  }
  inline void Swap(EntryValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EntryValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EntryValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EntryValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EntryValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const EntryValue& from) {
    EntryValue::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EntryValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.EntryValue";
  }
  protected:
  explicit EntryValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDoubleValueFieldNumber = 1,
    kStringValueFieldNumber = 2,
  };
  // double double_value = 1;
  bool has_double_value() const;
  private:
  bool _internal_has_double_value() const;
  public:
  void clear_double_value();
  double double_value() const;
  void set_double_value(double value);
  private:
  double _internal_double_value() const;
  void _internal_set_double_value(double value);
  public:

  // string string_value = 2;
  bool has_string_value() const;
  private:
  bool _internal_has_string_value() const;
  public:
  void clear_string_value();
  const std::string& string_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_value();
  PROTOBUF_NODISCARD std::string* release_string_value();
  void set_allocated_string_value(std::string* string_value);
  private:
  const std::string& _internal_string_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_value(const std::string& value);
  std::string* _internal_mutable_string_value();
  public:

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.EntryValue)
 private:
  class _Internal;
  void set_has_double_value();
  void set_has_string_value();

  inline bool has_kind() const;
  inline void clear_has_kind();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union KindUnion {
      constexpr KindUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      double double_value_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_value_;
    } kind_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class MetricEntry final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MetricEntry) */ {
 public:
  inline MetricEntry() : MetricEntry(nullptr) {}
  ~MetricEntry() override;
  explicit PROTOBUF_CONSTEXPR MetricEntry(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MetricEntry(const MetricEntry& from);
  MetricEntry(MetricEntry&& from) noexcept
    : MetricEntry() {
    *this = ::std::move(from);
  }

  inline MetricEntry& operator=(const MetricEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline MetricEntry& operator=(MetricEntry&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MetricEntry& default_instance() {
    return *internal_default_instance();
  }
  static inline const MetricEntry* internal_default_instance() {
    return reinterpret_cast<const MetricEntry*>(
               &_MetricEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MetricEntry& a, MetricEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(MetricEntry* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MetricEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MetricEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MetricEntry>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MetricEntry& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MetricEntry& from) {
    MetricEntry::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MetricEntry* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MetricEntry";
  }
  protected:
  explicit MetricEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kMinValueFieldNumber = 3,
    kMaxValueFieldNumber = 4,
    kValueFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .google.protobuf.DoubleValue min_value = 3;
  bool has_min_value() const;
  private:
  bool _internal_has_min_value() const;
  public:
  void clear_min_value();
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue& min_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::DoubleValue* release_min_value();
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* mutable_min_value();
  void set_allocated_min_value(::PROTOBUF_NAMESPACE_ID::DoubleValue* min_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue& _internal_min_value() const;
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* _internal_mutable_min_value();
  public:
  void unsafe_arena_set_allocated_min_value(
      ::PROTOBUF_NAMESPACE_ID::DoubleValue* min_value);
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* unsafe_arena_release_min_value();

  // .google.protobuf.DoubleValue max_value = 4;
  bool has_max_value() const;
  private:
  bool _internal_has_max_value() const;
  public:
  void clear_max_value();
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue& max_value() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::DoubleValue* release_max_value();
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* mutable_max_value();
  void set_allocated_max_value(::PROTOBUF_NAMESPACE_ID::DoubleValue* max_value);
  private:
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue& _internal_max_value() const;
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* _internal_mutable_max_value();
  public:
  void unsafe_arena_set_allocated_max_value(
      ::PROTOBUF_NAMESPACE_ID::DoubleValue* max_value);
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* unsafe_arena_release_max_value();

  // double value = 2;
  void clear_value();
  double value() const;
  void set_value(double value);
  private:
  double _internal_value() const;
  void _internal_set_value(double value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MetricEntry)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::DoubleValue* min_value_;
    ::PROTOBUF_NAMESPACE_ID::DoubleValue* max_value_;
    double value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class BenchmarkEntry_ExtrasEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<BenchmarkEntry_ExtrasEntry_DoNotUse, 
    std::string, ::tensorflow::EntryValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<BenchmarkEntry_ExtrasEntry_DoNotUse, 
    std::string, ::tensorflow::EntryValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  BenchmarkEntry_ExtrasEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR BenchmarkEntry_ExtrasEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit BenchmarkEntry_ExtrasEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const BenchmarkEntry_ExtrasEntry_DoNotUse& other);
  static const BenchmarkEntry_ExtrasEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const BenchmarkEntry_ExtrasEntry_DoNotUse*>(&_BenchmarkEntry_ExtrasEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.BenchmarkEntry.ExtrasEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};

// -------------------------------------------------------------------

class BenchmarkEntry final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BenchmarkEntry) */ {
 public:
  inline BenchmarkEntry() : BenchmarkEntry(nullptr) {}
  ~BenchmarkEntry() override;
  explicit PROTOBUF_CONSTEXPR BenchmarkEntry(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BenchmarkEntry(const BenchmarkEntry& from);
  BenchmarkEntry(BenchmarkEntry&& from) noexcept
    : BenchmarkEntry() {
    *this = ::std::move(from);
  }

  inline BenchmarkEntry& operator=(const BenchmarkEntry& from) {
    CopyFrom(from);
    return *this;
  }
  inline BenchmarkEntry& operator=(BenchmarkEntry&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BenchmarkEntry& default_instance() {
    return *internal_default_instance();
  }
  static inline const BenchmarkEntry* internal_default_instance() {
    return reinterpret_cast<const BenchmarkEntry*>(
               &_BenchmarkEntry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(BenchmarkEntry& a, BenchmarkEntry& b) {
    a.Swap(&b);
  }
  inline void Swap(BenchmarkEntry* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BenchmarkEntry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BenchmarkEntry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BenchmarkEntry>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BenchmarkEntry& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BenchmarkEntry& from) {
    BenchmarkEntry::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BenchmarkEntry* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BenchmarkEntry";
  }
  protected:
  explicit BenchmarkEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kExtrasFieldNumber = 6,
    kMetricsFieldNumber = 7,
    kNameFieldNumber = 1,
    kItersFieldNumber = 2,
    kCpuTimeFieldNumber = 3,
    kWallTimeFieldNumber = 4,
    kThroughputFieldNumber = 5,
  };
  // map<string, .tensorflow.EntryValue> extras = 6;
  int extras_size() const;
  private:
  int _internal_extras_size() const;
  public:
  void clear_extras();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >&
      _internal_extras() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >*
      _internal_mutable_extras();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >&
      extras() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >*
      mutable_extras();

  // repeated .tensorflow.MetricEntry metrics = 7;
  int metrics_size() const;
  private:
  int _internal_metrics_size() const;
  public:
  void clear_metrics();
  ::tensorflow::MetricEntry* mutable_metrics(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry >*
      mutable_metrics();
  private:
  const ::tensorflow::MetricEntry& _internal_metrics(int index) const;
  ::tensorflow::MetricEntry* _internal_add_metrics();
  public:
  const ::tensorflow::MetricEntry& metrics(int index) const;
  ::tensorflow::MetricEntry* add_metrics();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry >&
      metrics() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 iters = 2;
  void clear_iters();
  int64_t iters() const;
  void set_iters(int64_t value);
  private:
  int64_t _internal_iters() const;
  void _internal_set_iters(int64_t value);
  public:

  // double cpu_time = 3;
  void clear_cpu_time();
  double cpu_time() const;
  void set_cpu_time(double value);
  private:
  double _internal_cpu_time() const;
  void _internal_set_cpu_time(double value);
  public:

  // double wall_time = 4;
  void clear_wall_time();
  double wall_time() const;
  void set_wall_time(double value);
  private:
  double _internal_wall_time() const;
  void _internal_set_wall_time(double value);
  public:

  // double throughput = 5;
  void clear_throughput();
  double throughput() const;
  void set_throughput(double value);
  private:
  double _internal_throughput() const;
  void _internal_set_throughput(double value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BenchmarkEntry)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        BenchmarkEntry_ExtrasEntry_DoNotUse,
        std::string, ::tensorflow::EntryValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> extras_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry > metrics_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int64_t iters_;
    double cpu_time_;
    double wall_time_;
    double throughput_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class BenchmarkEntries final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BenchmarkEntries) */ {
 public:
  inline BenchmarkEntries() : BenchmarkEntries(nullptr) {}
  ~BenchmarkEntries() override;
  explicit PROTOBUF_CONSTEXPR BenchmarkEntries(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BenchmarkEntries(const BenchmarkEntries& from);
  BenchmarkEntries(BenchmarkEntries&& from) noexcept
    : BenchmarkEntries() {
    *this = ::std::move(from);
  }

  inline BenchmarkEntries& operator=(const BenchmarkEntries& from) {
    CopyFrom(from);
    return *this;
  }
  inline BenchmarkEntries& operator=(BenchmarkEntries&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BenchmarkEntries& default_instance() {
    return *internal_default_instance();
  }
  static inline const BenchmarkEntries* internal_default_instance() {
    return reinterpret_cast<const BenchmarkEntries*>(
               &_BenchmarkEntries_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(BenchmarkEntries& a, BenchmarkEntries& b) {
    a.Swap(&b);
  }
  inline void Swap(BenchmarkEntries* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BenchmarkEntries* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BenchmarkEntries* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BenchmarkEntries>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BenchmarkEntries& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BenchmarkEntries& from) {
    BenchmarkEntries::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BenchmarkEntries* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BenchmarkEntries";
  }
  protected:
  explicit BenchmarkEntries(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEntryFieldNumber = 1,
  };
  // repeated .tensorflow.BenchmarkEntry entry = 1;
  int entry_size() const;
  private:
  int _internal_entry_size() const;
  public:
  void clear_entry();
  ::tensorflow::BenchmarkEntry* mutable_entry(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry >*
      mutable_entry();
  private:
  const ::tensorflow::BenchmarkEntry& _internal_entry(int index) const;
  ::tensorflow::BenchmarkEntry* _internal_add_entry();
  public:
  const ::tensorflow::BenchmarkEntry& entry(int index) const;
  ::tensorflow::BenchmarkEntry* add_entry();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry >&
      entry() const;

  // @@protoc_insertion_point(class_scope:tensorflow.BenchmarkEntries)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry > entry_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class BuildConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BuildConfiguration) */ {
 public:
  inline BuildConfiguration() : BuildConfiguration(nullptr) {}
  ~BuildConfiguration() override;
  explicit PROTOBUF_CONSTEXPR BuildConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BuildConfiguration(const BuildConfiguration& from);
  BuildConfiguration(BuildConfiguration&& from) noexcept
    : BuildConfiguration() {
    *this = ::std::move(from);
  }

  inline BuildConfiguration& operator=(const BuildConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline BuildConfiguration& operator=(BuildConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BuildConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const BuildConfiguration* internal_default_instance() {
    return reinterpret_cast<const BuildConfiguration*>(
               &_BuildConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(BuildConfiguration& a, BuildConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(BuildConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BuildConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BuildConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BuildConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BuildConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BuildConfiguration& from) {
    BuildConfiguration::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BuildConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BuildConfiguration";
  }
  protected:
  explicit BuildConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCcFlagsFieldNumber = 2,
    kOptsFieldNumber = 3,
    kModeFieldNumber = 1,
  };
  // repeated string cc_flags = 2;
  int cc_flags_size() const;
  private:
  int _internal_cc_flags_size() const;
  public:
  void clear_cc_flags();
  const std::string& cc_flags(int index) const;
  std::string* mutable_cc_flags(int index);
  void set_cc_flags(int index, const std::string& value);
  void set_cc_flags(int index, std::string&& value);
  void set_cc_flags(int index, const char* value);
  void set_cc_flags(int index, const char* value, size_t size);
  std::string* add_cc_flags();
  void add_cc_flags(const std::string& value);
  void add_cc_flags(std::string&& value);
  void add_cc_flags(const char* value);
  void add_cc_flags(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& cc_flags() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_cc_flags();
  private:
  const std::string& _internal_cc_flags(int index) const;
  std::string* _internal_add_cc_flags();
  public:

  // repeated string opts = 3;
  int opts_size() const;
  private:
  int _internal_opts_size() const;
  public:
  void clear_opts();
  const std::string& opts(int index) const;
  std::string* mutable_opts(int index);
  void set_opts(int index, const std::string& value);
  void set_opts(int index, std::string&& value);
  void set_opts(int index, const char* value);
  void set_opts(int index, const char* value, size_t size);
  std::string* add_opts();
  void add_opts(const std::string& value);
  void add_opts(std::string&& value);
  void add_opts(const char* value);
  void add_opts(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& opts() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_opts();
  private:
  const std::string& _internal_opts(int index) const;
  std::string* _internal_add_opts();
  public:

  // string mode = 1;
  void clear_mode();
  const std::string& mode() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_mode(ArgT0&& arg0, ArgT... args);
  std::string* mutable_mode();
  PROTOBUF_NODISCARD std::string* release_mode();
  void set_allocated_mode(std::string* mode);
  private:
  const std::string& _internal_mode() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_mode(const std::string& value);
  std::string* _internal_mutable_mode();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BuildConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> cc_flags_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> opts_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mode_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class CommitId final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CommitId) */ {
 public:
  inline CommitId() : CommitId(nullptr) {}
  ~CommitId() override;
  explicit PROTOBUF_CONSTEXPR CommitId(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CommitId(const CommitId& from);
  CommitId(CommitId&& from) noexcept
    : CommitId() {
    *this = ::std::move(from);
  }

  inline CommitId& operator=(const CommitId& from) {
    CopyFrom(from);
    return *this;
  }
  inline CommitId& operator=(CommitId&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CommitId& default_instance() {
    return *internal_default_instance();
  }
  enum KindCase {
    kChangelist = 1,
    kHash = 2,
    KIND_NOT_SET = 0,
  };

  static inline const CommitId* internal_default_instance() {
    return reinterpret_cast<const CommitId*>(
               &_CommitId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(CommitId& a, CommitId& b) {
    a.Swap(&b);
  }
  inline void Swap(CommitId* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CommitId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CommitId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CommitId>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CommitId& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CommitId& from) {
    CommitId::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CommitId* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CommitId";
  }
  protected:
  explicit CommitId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSnapshotFieldNumber = 3,
    kPendingChangelistFieldNumber = 4,
    kChangelistFieldNumber = 1,
    kHashFieldNumber = 2,
  };
  // string snapshot = 3;
  void clear_snapshot();
  const std::string& snapshot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_snapshot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_snapshot();
  PROTOBUF_NODISCARD std::string* release_snapshot();
  void set_allocated_snapshot(std::string* snapshot);
  private:
  const std::string& _internal_snapshot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_snapshot(const std::string& value);
  std::string* _internal_mutable_snapshot();
  public:

  // int64 pending_changelist = 4;
  void clear_pending_changelist();
  int64_t pending_changelist() const;
  void set_pending_changelist(int64_t value);
  private:
  int64_t _internal_pending_changelist() const;
  void _internal_set_pending_changelist(int64_t value);
  public:

  // int64 changelist = 1;
  bool has_changelist() const;
  private:
  bool _internal_has_changelist() const;
  public:
  void clear_changelist();
  int64_t changelist() const;
  void set_changelist(int64_t value);
  private:
  int64_t _internal_changelist() const;
  void _internal_set_changelist(int64_t value);
  public:

  // string hash = 2;
  bool has_hash() const;
  private:
  bool _internal_has_hash() const;
  public:
  void clear_hash();
  const std::string& hash() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_hash(ArgT0&& arg0, ArgT... args);
  std::string* mutable_hash();
  PROTOBUF_NODISCARD std::string* release_hash();
  void set_allocated_hash(std::string* hash);
  private:
  const std::string& _internal_hash() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_hash(const std::string& value);
  std::string* _internal_mutable_hash();
  public:

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.CommitId)
 private:
  class _Internal;
  void set_has_changelist();
  void set_has_hash();

  inline bool has_kind() const;
  inline void clear_has_kind();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr snapshot_;
    int64_t pending_changelist_;
    union KindUnion {
      constexpr KindUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      int64_t changelist_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hash_;
    } kind_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class CPUInfo_CacheSizeEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CPUInfo_CacheSizeEntry_DoNotUse, 
    std::string, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<CPUInfo_CacheSizeEntry_DoNotUse, 
    std::string, int64_t,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> SuperType;
  CPUInfo_CacheSizeEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR CPUInfo_CacheSizeEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit CPUInfo_CacheSizeEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const CPUInfo_CacheSizeEntry_DoNotUse& other);
  static const CPUInfo_CacheSizeEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const CPUInfo_CacheSizeEntry_DoNotUse*>(&_CPUInfo_CacheSizeEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.CPUInfo.CacheSizeEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};

// -------------------------------------------------------------------

class CPUInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CPUInfo) */ {
 public:
  inline CPUInfo() : CPUInfo(nullptr) {}
  ~CPUInfo() override;
  explicit PROTOBUF_CONSTEXPR CPUInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CPUInfo(const CPUInfo& from);
  CPUInfo(CPUInfo&& from) noexcept
    : CPUInfo() {
    *this = ::std::move(from);
  }

  inline CPUInfo& operator=(const CPUInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline CPUInfo& operator=(CPUInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CPUInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const CPUInfo* internal_default_instance() {
    return reinterpret_cast<const CPUInfo*>(
               &_CPUInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(CPUInfo& a, CPUInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(CPUInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CPUInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CPUInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CPUInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CPUInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CPUInfo& from) {
    CPUInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CPUInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CPUInfo";
  }
  protected:
  explicit CPUInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kCacheSizeFieldNumber = 6,
    kCpuInfoFieldNumber = 4,
    kCpuGovernorFieldNumber = 5,
    kNumCoresFieldNumber = 1,
    kNumCoresAllowedFieldNumber = 2,
    kMhzPerCpuFieldNumber = 3,
  };
  // map<string, int64> cache_size = 6;
  int cache_size_size() const;
  private:
  int _internal_cache_size_size() const;
  public:
  void clear_cache_size();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >&
      _internal_cache_size() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >*
      _internal_mutable_cache_size();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >&
      cache_size() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >*
      mutable_cache_size();

  // string cpu_info = 4;
  void clear_cpu_info();
  const std::string& cpu_info() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cpu_info(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cpu_info();
  PROTOBUF_NODISCARD std::string* release_cpu_info();
  void set_allocated_cpu_info(std::string* cpu_info);
  private:
  const std::string& _internal_cpu_info() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cpu_info(const std::string& value);
  std::string* _internal_mutable_cpu_info();
  public:

  // string cpu_governor = 5;
  void clear_cpu_governor();
  const std::string& cpu_governor() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cpu_governor(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cpu_governor();
  PROTOBUF_NODISCARD std::string* release_cpu_governor();
  void set_allocated_cpu_governor(std::string* cpu_governor);
  private:
  const std::string& _internal_cpu_governor() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cpu_governor(const std::string& value);
  std::string* _internal_mutable_cpu_governor();
  public:

  // int64 num_cores = 1;
  void clear_num_cores();
  int64_t num_cores() const;
  void set_num_cores(int64_t value);
  private:
  int64_t _internal_num_cores() const;
  void _internal_set_num_cores(int64_t value);
  public:

  // int64 num_cores_allowed = 2;
  void clear_num_cores_allowed();
  int64_t num_cores_allowed() const;
  void set_num_cores_allowed(int64_t value);
  private:
  int64_t _internal_num_cores_allowed() const;
  void _internal_set_num_cores_allowed(int64_t value);
  public:

  // double mhz_per_cpu = 3;
  void clear_mhz_per_cpu();
  double mhz_per_cpu() const;
  void set_mhz_per_cpu(double value);
  private:
  double _internal_mhz_per_cpu() const;
  void _internal_set_mhz_per_cpu(double value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CPUInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        CPUInfo_CacheSizeEntry_DoNotUse,
        std::string, int64_t,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT64> cache_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cpu_info_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cpu_governor_;
    int64_t num_cores_;
    int64_t num_cores_allowed_;
    double mhz_per_cpu_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class MemoryInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryInfo) */ {
 public:
  inline MemoryInfo() : MemoryInfo(nullptr) {}
  ~MemoryInfo() override;
  explicit PROTOBUF_CONSTEXPR MemoryInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryInfo(const MemoryInfo& from);
  MemoryInfo(MemoryInfo&& from) noexcept
    : MemoryInfo() {
    *this = ::std::move(from);
  }

  inline MemoryInfo& operator=(const MemoryInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryInfo& operator=(MemoryInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryInfo* internal_default_instance() {
    return reinterpret_cast<const MemoryInfo*>(
               &_MemoryInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(MemoryInfo& a, MemoryInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryInfo& from) {
    MemoryInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryInfo";
  }
  protected:
  explicit MemoryInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTotalFieldNumber = 1,
    kAvailableFieldNumber = 2,
  };
  // int64 total = 1;
  void clear_total();
  int64_t total() const;
  void set_total(int64_t value);
  private:
  int64_t _internal_total() const;
  void _internal_set_total(int64_t value);
  public:

  // int64 available = 2;
  void clear_available();
  int64_t available() const;
  void set_available(int64_t value);
  private:
  int64_t _internal_available() const;
  void _internal_set_available(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t total_;
    int64_t available_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class GPUInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GPUInfo) */ {
 public:
  inline GPUInfo() : GPUInfo(nullptr) {}
  ~GPUInfo() override;
  explicit PROTOBUF_CONSTEXPR GPUInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GPUInfo(const GPUInfo& from);
  GPUInfo(GPUInfo&& from) noexcept
    : GPUInfo() {
    *this = ::std::move(from);
  }

  inline GPUInfo& operator=(const GPUInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GPUInfo& operator=(GPUInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GPUInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GPUInfo* internal_default_instance() {
    return reinterpret_cast<const GPUInfo*>(
               &_GPUInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GPUInfo& a, GPUInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GPUInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GPUInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GPUInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GPUInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GPUInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GPUInfo& from) {
    GPUInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GPUInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GPUInfo";
  }
  protected:
  explicit GPUInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelFieldNumber = 1,
    kUuidFieldNumber = 2,
    kBusIdFieldNumber = 3,
  };
  // string model = 1;
  void clear_model();
  const std::string& model() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model();
  PROTOBUF_NODISCARD std::string* release_model();
  void set_allocated_model(std::string* model);
  private:
  const std::string& _internal_model() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model(const std::string& value);
  std::string* _internal_mutable_model();
  public:

  // string uuid = 2;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // string bus_id = 3;
  void clear_bus_id();
  const std::string& bus_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bus_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bus_id();
  PROTOBUF_NODISCARD std::string* release_bus_id();
  void set_allocated_bus_id(std::string* bus_id);
  private:
  const std::string& _internal_bus_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bus_id(const std::string& value);
  std::string* _internal_mutable_bus_id();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GPUInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bus_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class PlatformInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PlatformInfo) */ {
 public:
  inline PlatformInfo() : PlatformInfo(nullptr) {}
  ~PlatformInfo() override;
  explicit PROTOBUF_CONSTEXPR PlatformInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlatformInfo(const PlatformInfo& from);
  PlatformInfo(PlatformInfo&& from) noexcept
    : PlatformInfo() {
    *this = ::std::move(from);
  }

  inline PlatformInfo& operator=(const PlatformInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlatformInfo& operator=(PlatformInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PlatformInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const PlatformInfo* internal_default_instance() {
    return reinterpret_cast<const PlatformInfo*>(
               &_PlatformInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(PlatformInfo& a, PlatformInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(PlatformInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlatformInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlatformInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlatformInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PlatformInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PlatformInfo& from) {
    PlatformInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlatformInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PlatformInfo";
  }
  protected:
  explicit PlatformInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBitsFieldNumber = 1,
    kLinkageFieldNumber = 2,
    kMachineFieldNumber = 3,
    kReleaseFieldNumber = 4,
    kSystemFieldNumber = 5,
    kVersionFieldNumber = 6,
  };
  // string bits = 1;
  void clear_bits();
  const std::string& bits() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bits(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bits();
  PROTOBUF_NODISCARD std::string* release_bits();
  void set_allocated_bits(std::string* bits);
  private:
  const std::string& _internal_bits() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bits(const std::string& value);
  std::string* _internal_mutable_bits();
  public:

  // string linkage = 2;
  void clear_linkage();
  const std::string& linkage() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_linkage(ArgT0&& arg0, ArgT... args);
  std::string* mutable_linkage();
  PROTOBUF_NODISCARD std::string* release_linkage();
  void set_allocated_linkage(std::string* linkage);
  private:
  const std::string& _internal_linkage() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_linkage(const std::string& value);
  std::string* _internal_mutable_linkage();
  public:

  // string machine = 3;
  void clear_machine();
  const std::string& machine() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_machine(ArgT0&& arg0, ArgT... args);
  std::string* mutable_machine();
  PROTOBUF_NODISCARD std::string* release_machine();
  void set_allocated_machine(std::string* machine);
  private:
  const std::string& _internal_machine() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_machine(const std::string& value);
  std::string* _internal_mutable_machine();
  public:

  // string release = 4;
  void clear_release();
  const std::string& release() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_release(ArgT0&& arg0, ArgT... args);
  std::string* mutable_release();
  PROTOBUF_NODISCARD std::string* release_release();
  void set_allocated_release(std::string* release);
  private:
  const std::string& _internal_release() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_release(const std::string& value);
  std::string* _internal_mutable_release();
  public:

  // string system = 5;
  void clear_system();
  const std::string& system() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_system(ArgT0&& arg0, ArgT... args);
  std::string* mutable_system();
  PROTOBUF_NODISCARD std::string* release_system();
  void set_allocated_system(std::string* system);
  private:
  const std::string& _internal_system() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_system(const std::string& value);
  std::string* _internal_mutable_system();
  public:

  // string version = 6;
  void clear_version();
  const std::string& version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_version();
  PROTOBUF_NODISCARD std::string* release_version();
  void set_allocated_version(std::string* version);
  private:
  const std::string& _internal_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_version(const std::string& value);
  std::string* _internal_mutable_version();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.PlatformInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bits_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr linkage_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr machine_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr release_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr system_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class AvailableDeviceInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AvailableDeviceInfo) */ {
 public:
  inline AvailableDeviceInfo() : AvailableDeviceInfo(nullptr) {}
  ~AvailableDeviceInfo() override;
  explicit PROTOBUF_CONSTEXPR AvailableDeviceInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AvailableDeviceInfo(const AvailableDeviceInfo& from);
  AvailableDeviceInfo(AvailableDeviceInfo&& from) noexcept
    : AvailableDeviceInfo() {
    *this = ::std::move(from);
  }

  inline AvailableDeviceInfo& operator=(const AvailableDeviceInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline AvailableDeviceInfo& operator=(AvailableDeviceInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AvailableDeviceInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const AvailableDeviceInfo* internal_default_instance() {
    return reinterpret_cast<const AvailableDeviceInfo*>(
               &_AvailableDeviceInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(AvailableDeviceInfo& a, AvailableDeviceInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(AvailableDeviceInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AvailableDeviceInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AvailableDeviceInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AvailableDeviceInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AvailableDeviceInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AvailableDeviceInfo& from) {
    AvailableDeviceInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AvailableDeviceInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AvailableDeviceInfo";
  }
  protected:
  explicit AvailableDeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kTypeFieldNumber = 2,
    kPhysicalDescriptionFieldNumber = 4,
    kMemoryLimitFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string type = 2;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // string physical_description = 4;
  void clear_physical_description();
  const std::string& physical_description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_physical_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_physical_description();
  PROTOBUF_NODISCARD std::string* release_physical_description();
  void set_allocated_physical_description(std::string* physical_description);
  private:
  const std::string& _internal_physical_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_physical_description(const std::string& value);
  std::string* _internal_mutable_physical_description();
  public:

  // int64 memory_limit = 3;
  void clear_memory_limit();
  int64_t memory_limit() const;
  void set_memory_limit(int64_t value);
  private:
  int64_t _internal_memory_limit() const;
  void _internal_set_memory_limit(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.AvailableDeviceInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr physical_description_;
    int64_t memory_limit_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class MachineConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MachineConfiguration) */ {
 public:
  inline MachineConfiguration() : MachineConfiguration(nullptr) {}
  ~MachineConfiguration() override;
  explicit PROTOBUF_CONSTEXPR MachineConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MachineConfiguration(const MachineConfiguration& from);
  MachineConfiguration(MachineConfiguration&& from) noexcept
    : MachineConfiguration() {
    *this = ::std::move(from);
  }

  inline MachineConfiguration& operator=(const MachineConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline MachineConfiguration& operator=(MachineConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MachineConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const MachineConfiguration* internal_default_instance() {
    return reinterpret_cast<const MachineConfiguration*>(
               &_MachineConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(MachineConfiguration& a, MachineConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(MachineConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MachineConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MachineConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MachineConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MachineConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MachineConfiguration& from) {
    MachineConfiguration::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MachineConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MachineConfiguration";
  }
  protected:
  explicit MachineConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceInfoFieldNumber = 4,
    kAvailableDeviceInfoFieldNumber = 5,
    kHostnameFieldNumber = 1,
    kSerialIdentifierFieldNumber = 7,
    kPlatformInfoFieldNumber = 2,
    kCpuInfoFieldNumber = 3,
    kMemoryInfoFieldNumber = 6,
  };
  // repeated .google.protobuf.Any device_info = 4;
  int device_info_size() const;
  private:
  int _internal_device_info_size() const;
  public:
  void clear_device_info();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_device_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >*
      mutable_device_info();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_device_info(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_add_device_info();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Any& device_info(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Any* add_device_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >&
      device_info() const;

  // repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
  int available_device_info_size() const;
  private:
  int _internal_available_device_info_size() const;
  public:
  void clear_available_device_info();
  ::tensorflow::AvailableDeviceInfo* mutable_available_device_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >*
      mutable_available_device_info();
  private:
  const ::tensorflow::AvailableDeviceInfo& _internal_available_device_info(int index) const;
  ::tensorflow::AvailableDeviceInfo* _internal_add_available_device_info();
  public:
  const ::tensorflow::AvailableDeviceInfo& available_device_info(int index) const;
  ::tensorflow::AvailableDeviceInfo* add_available_device_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >&
      available_device_info() const;

  // string hostname = 1;
  void clear_hostname();
  const std::string& hostname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_hostname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_hostname();
  PROTOBUF_NODISCARD std::string* release_hostname();
  void set_allocated_hostname(std::string* hostname);
  private:
  const std::string& _internal_hostname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_hostname(const std::string& value);
  std::string* _internal_mutable_hostname();
  public:

  // string serial_identifier = 7;
  void clear_serial_identifier();
  const std::string& serial_identifier() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial_identifier(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial_identifier();
  PROTOBUF_NODISCARD std::string* release_serial_identifier();
  void set_allocated_serial_identifier(std::string* serial_identifier);
  private:
  const std::string& _internal_serial_identifier() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial_identifier(const std::string& value);
  std::string* _internal_mutable_serial_identifier();
  public:

  // .tensorflow.PlatformInfo platform_info = 2;
  bool has_platform_info() const;
  private:
  bool _internal_has_platform_info() const;
  public:
  void clear_platform_info();
  const ::tensorflow::PlatformInfo& platform_info() const;
  PROTOBUF_NODISCARD ::tensorflow::PlatformInfo* release_platform_info();
  ::tensorflow::PlatformInfo* mutable_platform_info();
  void set_allocated_platform_info(::tensorflow::PlatformInfo* platform_info);
  private:
  const ::tensorflow::PlatformInfo& _internal_platform_info() const;
  ::tensorflow::PlatformInfo* _internal_mutable_platform_info();
  public:
  void unsafe_arena_set_allocated_platform_info(
      ::tensorflow::PlatformInfo* platform_info);
  ::tensorflow::PlatformInfo* unsafe_arena_release_platform_info();

  // .tensorflow.CPUInfo cpu_info = 3;
  bool has_cpu_info() const;
  private:
  bool _internal_has_cpu_info() const;
  public:
  void clear_cpu_info();
  const ::tensorflow::CPUInfo& cpu_info() const;
  PROTOBUF_NODISCARD ::tensorflow::CPUInfo* release_cpu_info();
  ::tensorflow::CPUInfo* mutable_cpu_info();
  void set_allocated_cpu_info(::tensorflow::CPUInfo* cpu_info);
  private:
  const ::tensorflow::CPUInfo& _internal_cpu_info() const;
  ::tensorflow::CPUInfo* _internal_mutable_cpu_info();
  public:
  void unsafe_arena_set_allocated_cpu_info(
      ::tensorflow::CPUInfo* cpu_info);
  ::tensorflow::CPUInfo* unsafe_arena_release_cpu_info();

  // .tensorflow.MemoryInfo memory_info = 6;
  bool has_memory_info() const;
  private:
  bool _internal_has_memory_info() const;
  public:
  void clear_memory_info();
  const ::tensorflow::MemoryInfo& memory_info() const;
  PROTOBUF_NODISCARD ::tensorflow::MemoryInfo* release_memory_info();
  ::tensorflow::MemoryInfo* mutable_memory_info();
  void set_allocated_memory_info(::tensorflow::MemoryInfo* memory_info);
  private:
  const ::tensorflow::MemoryInfo& _internal_memory_info() const;
  ::tensorflow::MemoryInfo* _internal_mutable_memory_info();
  public:
  void unsafe_arena_set_allocated_memory_info(
      ::tensorflow::MemoryInfo* memory_info);
  ::tensorflow::MemoryInfo* unsafe_arena_release_memory_info();

  // @@protoc_insertion_point(class_scope:tensorflow.MachineConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any > device_info_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo > available_device_info_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hostname_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_identifier_;
    ::tensorflow::PlatformInfo* platform_info_;
    ::tensorflow::CPUInfo* cpu_info_;
    ::tensorflow::MemoryInfo* memory_info_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class RunConfiguration_EnvVarsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RunConfiguration_EnvVarsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RunConfiguration_EnvVarsEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  RunConfiguration_EnvVarsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR RunConfiguration_EnvVarsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit RunConfiguration_EnvVarsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const RunConfiguration_EnvVarsEntry_DoNotUse& other);
  static const RunConfiguration_EnvVarsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const RunConfiguration_EnvVarsEntry_DoNotUse*>(&_RunConfiguration_EnvVarsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.RunConfiguration.EnvVarsEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.RunConfiguration.EnvVarsEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};

// -------------------------------------------------------------------

class RunConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunConfiguration) */ {
 public:
  inline RunConfiguration() : RunConfiguration(nullptr) {}
  ~RunConfiguration() override;
  explicit PROTOBUF_CONSTEXPR RunConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunConfiguration(const RunConfiguration& from);
  RunConfiguration(RunConfiguration&& from) noexcept
    : RunConfiguration() {
    *this = ::std::move(from);
  }

  inline RunConfiguration& operator=(const RunConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunConfiguration& operator=(RunConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunConfiguration* internal_default_instance() {
    return reinterpret_cast<const RunConfiguration*>(
               &_RunConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(RunConfiguration& a, RunConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(RunConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunConfiguration& from) {
    RunConfiguration::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunConfiguration";
  }
  protected:
  explicit RunConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kArgumentFieldNumber = 1,
    kEnvVarsFieldNumber = 2,
  };
  // repeated string argument = 1;
  int argument_size() const;
  private:
  int _internal_argument_size() const;
  public:
  void clear_argument();
  const std::string& argument(int index) const;
  std::string* mutable_argument(int index);
  void set_argument(int index, const std::string& value);
  void set_argument(int index, std::string&& value);
  void set_argument(int index, const char* value);
  void set_argument(int index, const char* value, size_t size);
  std::string* add_argument();
  void add_argument(const std::string& value);
  void add_argument(std::string&& value);
  void add_argument(const char* value);
  void add_argument(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& argument() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_argument();
  private:
  const std::string& _internal_argument(int index) const;
  std::string* _internal_add_argument();
  public:

  // map<string, string> env_vars = 2;
  int env_vars_size() const;
  private:
  int _internal_env_vars_size() const;
  public:
  void clear_env_vars();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_env_vars() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_env_vars();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      env_vars() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_env_vars();

  // @@protoc_insertion_point(class_scope:tensorflow.RunConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> argument_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        RunConfiguration_EnvVarsEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> env_vars_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// -------------------------------------------------------------------

class TestResults final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TestResults) */ {
 public:
  inline TestResults() : TestResults(nullptr) {}
  ~TestResults() override;
  explicit PROTOBUF_CONSTEXPR TestResults(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TestResults(const TestResults& from);
  TestResults(TestResults&& from) noexcept
    : TestResults() {
    *this = ::std::move(from);
  }

  inline TestResults& operator=(const TestResults& from) {
    CopyFrom(from);
    return *this;
  }
  inline TestResults& operator=(TestResults&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TestResults& default_instance() {
    return *internal_default_instance();
  }
  static inline const TestResults* internal_default_instance() {
    return reinterpret_cast<const TestResults*>(
               &_TestResults_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(TestResults& a, TestResults& b) {
    a.Swap(&b);
  }
  inline void Swap(TestResults* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TestResults* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TestResults* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TestResults>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TestResults& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TestResults& from) {
    TestResults::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TestResults* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TestResults";
  }
  protected:
  explicit TestResults(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TestResults_BenchmarkType BenchmarkType;
  static constexpr BenchmarkType UNKNOWN =
    TestResults_BenchmarkType_UNKNOWN;
  static constexpr BenchmarkType CPP_MICROBENCHMARK =
    TestResults_BenchmarkType_CPP_MICROBENCHMARK;
  static constexpr BenchmarkType PYTHON_BENCHMARK =
    TestResults_BenchmarkType_PYTHON_BENCHMARK;
  static constexpr BenchmarkType ANDROID_BENCHMARK =
    TestResults_BenchmarkType_ANDROID_BENCHMARK;
  static constexpr BenchmarkType EDGE_BENCHMARK =
    TestResults_BenchmarkType_EDGE_BENCHMARK;
  static constexpr BenchmarkType IOS_BENCHMARK =
    TestResults_BenchmarkType_IOS_BENCHMARK;
  static inline bool BenchmarkType_IsValid(int value) {
    return TestResults_BenchmarkType_IsValid(value);
  }
  static constexpr BenchmarkType BenchmarkType_MIN =
    TestResults_BenchmarkType_BenchmarkType_MIN;
  static constexpr BenchmarkType BenchmarkType_MAX =
    TestResults_BenchmarkType_BenchmarkType_MAX;
  static constexpr int BenchmarkType_ARRAYSIZE =
    TestResults_BenchmarkType_BenchmarkType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  BenchmarkType_descriptor() {
    return TestResults_BenchmarkType_descriptor();
  }
  template<typename T>
  static inline const std::string& BenchmarkType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, BenchmarkType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function BenchmarkType_Name.");
    return TestResults_BenchmarkType_Name(enum_t_value);
  }
  static inline bool BenchmarkType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      BenchmarkType* value) {
    return TestResults_BenchmarkType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTargetFieldNumber = 1,
    kNameFieldNumber = 9,
    kRunModeFieldNumber = 11,
    kTfVersionFieldNumber = 12,
    kEntriesFieldNumber = 2,
    kBuildConfigurationFieldNumber = 3,
    kCommitIdFieldNumber = 4,
    kMachineConfigurationFieldNumber = 7,
    kRunConfigurationFieldNumber = 8,
    kStartTimeFieldNumber = 5,
    kRunTimeFieldNumber = 6,
    kBenchmarkTypeFieldNumber = 10,
  };
  // string target = 1;
  void clear_target();
  const std::string& target() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_target(ArgT0&& arg0, ArgT... args);
  std::string* mutable_target();
  PROTOBUF_NODISCARD std::string* release_target();
  void set_allocated_target(std::string* target);
  private:
  const std::string& _internal_target() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_target(const std::string& value);
  std::string* _internal_mutable_target();
  public:

  // string name = 9;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string run_mode = 11;
  void clear_run_mode();
  const std::string& run_mode() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_run_mode(ArgT0&& arg0, ArgT... args);
  std::string* mutable_run_mode();
  PROTOBUF_NODISCARD std::string* release_run_mode();
  void set_allocated_run_mode(std::string* run_mode);
  private:
  const std::string& _internal_run_mode() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_run_mode(const std::string& value);
  std::string* _internal_mutable_run_mode();
  public:

  // string tf_version = 12;
  void clear_tf_version();
  const std::string& tf_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tf_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tf_version();
  PROTOBUF_NODISCARD std::string* release_tf_version();
  void set_allocated_tf_version(std::string* tf_version);
  private:
  const std::string& _internal_tf_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tf_version(const std::string& value);
  std::string* _internal_mutable_tf_version();
  public:

  // .tensorflow.BenchmarkEntries entries = 2;
  bool has_entries() const;
  private:
  bool _internal_has_entries() const;
  public:
  void clear_entries();
  const ::tensorflow::BenchmarkEntries& entries() const;
  PROTOBUF_NODISCARD ::tensorflow::BenchmarkEntries* release_entries();
  ::tensorflow::BenchmarkEntries* mutable_entries();
  void set_allocated_entries(::tensorflow::BenchmarkEntries* entries);
  private:
  const ::tensorflow::BenchmarkEntries& _internal_entries() const;
  ::tensorflow::BenchmarkEntries* _internal_mutable_entries();
  public:
  void unsafe_arena_set_allocated_entries(
      ::tensorflow::BenchmarkEntries* entries);
  ::tensorflow::BenchmarkEntries* unsafe_arena_release_entries();

  // .tensorflow.BuildConfiguration build_configuration = 3;
  bool has_build_configuration() const;
  private:
  bool _internal_has_build_configuration() const;
  public:
  void clear_build_configuration();
  const ::tensorflow::BuildConfiguration& build_configuration() const;
  PROTOBUF_NODISCARD ::tensorflow::BuildConfiguration* release_build_configuration();
  ::tensorflow::BuildConfiguration* mutable_build_configuration();
  void set_allocated_build_configuration(::tensorflow::BuildConfiguration* build_configuration);
  private:
  const ::tensorflow::BuildConfiguration& _internal_build_configuration() const;
  ::tensorflow::BuildConfiguration* _internal_mutable_build_configuration();
  public:
  void unsafe_arena_set_allocated_build_configuration(
      ::tensorflow::BuildConfiguration* build_configuration);
  ::tensorflow::BuildConfiguration* unsafe_arena_release_build_configuration();

  // .tensorflow.CommitId commit_id = 4;
  bool has_commit_id() const;
  private:
  bool _internal_has_commit_id() const;
  public:
  void clear_commit_id();
  const ::tensorflow::CommitId& commit_id() const;
  PROTOBUF_NODISCARD ::tensorflow::CommitId* release_commit_id();
  ::tensorflow::CommitId* mutable_commit_id();
  void set_allocated_commit_id(::tensorflow::CommitId* commit_id);
  private:
  const ::tensorflow::CommitId& _internal_commit_id() const;
  ::tensorflow::CommitId* _internal_mutable_commit_id();
  public:
  void unsafe_arena_set_allocated_commit_id(
      ::tensorflow::CommitId* commit_id);
  ::tensorflow::CommitId* unsafe_arena_release_commit_id();

  // .tensorflow.MachineConfiguration machine_configuration = 7;
  bool has_machine_configuration() const;
  private:
  bool _internal_has_machine_configuration() const;
  public:
  void clear_machine_configuration();
  const ::tensorflow::MachineConfiguration& machine_configuration() const;
  PROTOBUF_NODISCARD ::tensorflow::MachineConfiguration* release_machine_configuration();
  ::tensorflow::MachineConfiguration* mutable_machine_configuration();
  void set_allocated_machine_configuration(::tensorflow::MachineConfiguration* machine_configuration);
  private:
  const ::tensorflow::MachineConfiguration& _internal_machine_configuration() const;
  ::tensorflow::MachineConfiguration* _internal_mutable_machine_configuration();
  public:
  void unsafe_arena_set_allocated_machine_configuration(
      ::tensorflow::MachineConfiguration* machine_configuration);
  ::tensorflow::MachineConfiguration* unsafe_arena_release_machine_configuration();

  // .tensorflow.RunConfiguration run_configuration = 8;
  bool has_run_configuration() const;
  private:
  bool _internal_has_run_configuration() const;
  public:
  void clear_run_configuration();
  const ::tensorflow::RunConfiguration& run_configuration() const;
  PROTOBUF_NODISCARD ::tensorflow::RunConfiguration* release_run_configuration();
  ::tensorflow::RunConfiguration* mutable_run_configuration();
  void set_allocated_run_configuration(::tensorflow::RunConfiguration* run_configuration);
  private:
  const ::tensorflow::RunConfiguration& _internal_run_configuration() const;
  ::tensorflow::RunConfiguration* _internal_mutable_run_configuration();
  public:
  void unsafe_arena_set_allocated_run_configuration(
      ::tensorflow::RunConfiguration* run_configuration);
  ::tensorflow::RunConfiguration* unsafe_arena_release_run_configuration();

  // int64 start_time = 5;
  void clear_start_time();
  int64_t start_time() const;
  void set_start_time(int64_t value);
  private:
  int64_t _internal_start_time() const;
  void _internal_set_start_time(int64_t value);
  public:

  // double run_time = 6;
  void clear_run_time();
  double run_time() const;
  void set_run_time(double value);
  private:
  double _internal_run_time() const;
  void _internal_set_run_time(double value);
  public:

  // .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
  void clear_benchmark_type();
  ::tensorflow::TestResults_BenchmarkType benchmark_type() const;
  void set_benchmark_type(::tensorflow::TestResults_BenchmarkType value);
  private:
  ::tensorflow::TestResults_BenchmarkType _internal_benchmark_type() const;
  void _internal_set_benchmark_type(::tensorflow::TestResults_BenchmarkType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TestResults)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr target_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr run_mode_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tf_version_;
    ::tensorflow::BenchmarkEntries* entries_;
    ::tensorflow::BuildConfiguration* build_configuration_;
    ::tensorflow::CommitId* commit_id_;
    ::tensorflow::MachineConfiguration* machine_configuration_;
    ::tensorflow::RunConfiguration* run_configuration_;
    int64_t start_time_;
    double run_time_;
    int benchmark_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EntryValue

// double double_value = 1;
inline bool EntryValue::_internal_has_double_value() const {
  return kind_case() == kDoubleValue;
}
inline bool EntryValue::has_double_value() const {
  return _internal_has_double_value();
}
inline void EntryValue::set_has_double_value() {
  _impl_._oneof_case_[0] = kDoubleValue;
}
inline void EntryValue::clear_double_value() {
  if (_internal_has_double_value()) {
    _impl_.kind_.double_value_ = 0;
    clear_has_kind();
  }
}
inline double EntryValue::_internal_double_value() const {
  if (_internal_has_double_value()) {
    return _impl_.kind_.double_value_;
  }
  return 0;
}
inline void EntryValue::_internal_set_double_value(double value) {
  if (!_internal_has_double_value()) {
    clear_kind();
    set_has_double_value();
  }
  _impl_.kind_.double_value_ = value;
}
inline double EntryValue::double_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.EntryValue.double_value)
  return _internal_double_value();
}
inline void EntryValue::set_double_value(double value) {
  _internal_set_double_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.EntryValue.double_value)
}

// string string_value = 2;
inline bool EntryValue::_internal_has_string_value() const {
  return kind_case() == kStringValue;
}
inline bool EntryValue::has_string_value() const {
  return _internal_has_string_value();
}
inline void EntryValue::set_has_string_value() {
  _impl_._oneof_case_[0] = kStringValue;
}
inline void EntryValue::clear_string_value() {
  if (_internal_has_string_value()) {
    _impl_.kind_.string_value_.Destroy();
    clear_has_kind();
  }
}
inline const std::string& EntryValue::string_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.EntryValue.string_value)
  return _internal_string_value();
}
template <typename ArgT0, typename... ArgT>
inline void EntryValue::set_string_value(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_string_value()) {
    clear_kind();
    set_has_string_value();
    _impl_.kind_.string_value_.InitDefault();
  }
  _impl_.kind_.string_value_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.EntryValue.string_value)
}
inline std::string* EntryValue::mutable_string_value() {
  std::string* _s = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.EntryValue.string_value)
  return _s;
}
inline const std::string& EntryValue::_internal_string_value() const {
  if (_internal_has_string_value()) {
    return _impl_.kind_.string_value_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void EntryValue::_internal_set_string_value(const std::string& value) {
  if (!_internal_has_string_value()) {
    clear_kind();
    set_has_string_value();
    _impl_.kind_.string_value_.InitDefault();
  }
  _impl_.kind_.string_value_.Set(value, GetArenaForAllocation());
}
inline std::string* EntryValue::_internal_mutable_string_value() {
  if (!_internal_has_string_value()) {
    clear_kind();
    set_has_string_value();
    _impl_.kind_.string_value_.InitDefault();
  }
  return _impl_.kind_.string_value_.Mutable(      GetArenaForAllocation());
}
inline std::string* EntryValue::release_string_value() {
  // @@protoc_insertion_point(field_release:tensorflow.EntryValue.string_value)
  if (_internal_has_string_value()) {
    clear_has_kind();
    return _impl_.kind_.string_value_.Release();
  } else {
    return nullptr;
  }
}
inline void EntryValue::set_allocated_string_value(std::string* string_value) {
  if (has_kind()) {
    clear_kind();
  }
  if (string_value != nullptr) {
    set_has_string_value();
    _impl_.kind_.string_value_.InitAllocated(string_value, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.EntryValue.string_value)
}

inline bool EntryValue::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void EntryValue::clear_has_kind() {
  _impl_._oneof_case_[0] = KIND_NOT_SET;
}
inline EntryValue::KindCase EntryValue::kind_case() const {
  return EntryValue::KindCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// MetricEntry

// string name = 1;
inline void MetricEntry::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& MetricEntry::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetricEntry.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MetricEntry::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MetricEntry.name)
}
inline std::string* MetricEntry::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetricEntry.name)
  return _s;
}
inline const std::string& MetricEntry::_internal_name() const {
  return _impl_.name_.Get();
}
inline void MetricEntry::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* MetricEntry::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* MetricEntry::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MetricEntry.name)
  return _impl_.name_.Release();
}
inline void MetricEntry::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetricEntry.name)
}

// double value = 2;
inline void MetricEntry::clear_value() {
  _impl_.value_ = 0;
}
inline double MetricEntry::_internal_value() const {
  return _impl_.value_;
}
inline double MetricEntry::value() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetricEntry.value)
  return _internal_value();
}
inline void MetricEntry::_internal_set_value(double value) {
  
  _impl_.value_ = value;
}
inline void MetricEntry::set_value(double value) {
  _internal_set_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.MetricEntry.value)
}

// .google.protobuf.DoubleValue min_value = 3;
inline bool MetricEntry::_internal_has_min_value() const {
  return this != internal_default_instance() && _impl_.min_value_ != nullptr;
}
inline bool MetricEntry::has_min_value() const {
  return _internal_has_min_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::DoubleValue& MetricEntry::_internal_min_value() const {
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue* p = _impl_.min_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::DoubleValue&>(
      ::PROTOBUF_NAMESPACE_ID::_DoubleValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::DoubleValue& MetricEntry::min_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetricEntry.min_value)
  return _internal_min_value();
}
inline void MetricEntry::unsafe_arena_set_allocated_min_value(
    ::PROTOBUF_NAMESPACE_ID::DoubleValue* min_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.min_value_);
  }
  _impl_.min_value_ = min_value;
  if (min_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetricEntry.min_value)
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::release_min_value() {
  
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* temp = _impl_.min_value_;
  _impl_.min_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::unsafe_arena_release_min_value() {
  // @@protoc_insertion_point(field_release:tensorflow.MetricEntry.min_value)
  
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* temp = _impl_.min_value_;
  _impl_.min_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::_internal_mutable_min_value() {
  
  if (_impl_.min_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::DoubleValue>(GetArenaForAllocation());
    _impl_.min_value_ = p;
  }
  return _impl_.min_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::mutable_min_value() {
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* _msg = _internal_mutable_min_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetricEntry.min_value)
  return _msg;
}
inline void MetricEntry::set_allocated_min_value(::PROTOBUF_NAMESPACE_ID::DoubleValue* min_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.min_value_);
  }
  if (min_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(min_value));
    if (message_arena != submessage_arena) {
      min_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, min_value, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.min_value_ = min_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetricEntry.min_value)
}

// .google.protobuf.DoubleValue max_value = 4;
inline bool MetricEntry::_internal_has_max_value() const {
  return this != internal_default_instance() && _impl_.max_value_ != nullptr;
}
inline bool MetricEntry::has_max_value() const {
  return _internal_has_max_value();
}
inline const ::PROTOBUF_NAMESPACE_ID::DoubleValue& MetricEntry::_internal_max_value() const {
  const ::PROTOBUF_NAMESPACE_ID::DoubleValue* p = _impl_.max_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::DoubleValue&>(
      ::PROTOBUF_NAMESPACE_ID::_DoubleValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::DoubleValue& MetricEntry::max_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetricEntry.max_value)
  return _internal_max_value();
}
inline void MetricEntry::unsafe_arena_set_allocated_max_value(
    ::PROTOBUF_NAMESPACE_ID::DoubleValue* max_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.max_value_);
  }
  _impl_.max_value_ = max_value;
  if (max_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetricEntry.max_value)
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::release_max_value() {
  
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* temp = _impl_.max_value_;
  _impl_.max_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::unsafe_arena_release_max_value() {
  // @@protoc_insertion_point(field_release:tensorflow.MetricEntry.max_value)
  
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* temp = _impl_.max_value_;
  _impl_.max_value_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::_internal_mutable_max_value() {
  
  if (_impl_.max_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::DoubleValue>(GetArenaForAllocation());
    _impl_.max_value_ = p;
  }
  return _impl_.max_value_;
}
inline ::PROTOBUF_NAMESPACE_ID::DoubleValue* MetricEntry::mutable_max_value() {
  ::PROTOBUF_NAMESPACE_ID::DoubleValue* _msg = _internal_mutable_max_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetricEntry.max_value)
  return _msg;
}
inline void MetricEntry::set_allocated_max_value(::PROTOBUF_NAMESPACE_ID::DoubleValue* max_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.max_value_);
  }
  if (max_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(max_value));
    if (message_arena != submessage_arena) {
      max_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, max_value, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.max_value_ = max_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetricEntry.max_value)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// BenchmarkEntry

// string name = 1;
inline void BenchmarkEntry::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& BenchmarkEntry::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BenchmarkEntry::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.name)
}
inline std::string* BenchmarkEntry::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.BenchmarkEntry.name)
  return _s;
}
inline const std::string& BenchmarkEntry::_internal_name() const {
  return _impl_.name_.Get();
}
inline void BenchmarkEntry::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* BenchmarkEntry::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* BenchmarkEntry::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.BenchmarkEntry.name)
  return _impl_.name_.Release();
}
inline void BenchmarkEntry::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BenchmarkEntry.name)
}

// int64 iters = 2;
inline void BenchmarkEntry::clear_iters() {
  _impl_.iters_ = int64_t{0};
}
inline int64_t BenchmarkEntry::_internal_iters() const {
  return _impl_.iters_;
}
inline int64_t BenchmarkEntry::iters() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.iters)
  return _internal_iters();
}
inline void BenchmarkEntry::_internal_set_iters(int64_t value) {
  
  _impl_.iters_ = value;
}
inline void BenchmarkEntry::set_iters(int64_t value) {
  _internal_set_iters(value);
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.iters)
}

// double cpu_time = 3;
inline void BenchmarkEntry::clear_cpu_time() {
  _impl_.cpu_time_ = 0;
}
inline double BenchmarkEntry::_internal_cpu_time() const {
  return _impl_.cpu_time_;
}
inline double BenchmarkEntry::cpu_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.cpu_time)
  return _internal_cpu_time();
}
inline void BenchmarkEntry::_internal_set_cpu_time(double value) {
  
  _impl_.cpu_time_ = value;
}
inline void BenchmarkEntry::set_cpu_time(double value) {
  _internal_set_cpu_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.cpu_time)
}

// double wall_time = 4;
inline void BenchmarkEntry::clear_wall_time() {
  _impl_.wall_time_ = 0;
}
inline double BenchmarkEntry::_internal_wall_time() const {
  return _impl_.wall_time_;
}
inline double BenchmarkEntry::wall_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.wall_time)
  return _internal_wall_time();
}
inline void BenchmarkEntry::_internal_set_wall_time(double value) {
  
  _impl_.wall_time_ = value;
}
inline void BenchmarkEntry::set_wall_time(double value) {
  _internal_set_wall_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.wall_time)
}

// double throughput = 5;
inline void BenchmarkEntry::clear_throughput() {
  _impl_.throughput_ = 0;
}
inline double BenchmarkEntry::_internal_throughput() const {
  return _impl_.throughput_;
}
inline double BenchmarkEntry::throughput() const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.throughput)
  return _internal_throughput();
}
inline void BenchmarkEntry::_internal_set_throughput(double value) {
  
  _impl_.throughput_ = value;
}
inline void BenchmarkEntry::set_throughput(double value) {
  _internal_set_throughput(value);
  // @@protoc_insertion_point(field_set:tensorflow.BenchmarkEntry.throughput)
}

// map<string, .tensorflow.EntryValue> extras = 6;
inline int BenchmarkEntry::_internal_extras_size() const {
  return _impl_.extras_.size();
}
inline int BenchmarkEntry::extras_size() const {
  return _internal_extras_size();
}
inline void BenchmarkEntry::clear_extras() {
  _impl_.extras_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >&
BenchmarkEntry::_internal_extras() const {
  return _impl_.extras_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >&
BenchmarkEntry::extras() const {
  // @@protoc_insertion_point(field_map:tensorflow.BenchmarkEntry.extras)
  return _internal_extras();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >*
BenchmarkEntry::_internal_mutable_extras() {
  return _impl_.extras_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::EntryValue >*
BenchmarkEntry::mutable_extras() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.BenchmarkEntry.extras)
  return _internal_mutable_extras();
}

// repeated .tensorflow.MetricEntry metrics = 7;
inline int BenchmarkEntry::_internal_metrics_size() const {
  return _impl_.metrics_.size();
}
inline int BenchmarkEntry::metrics_size() const {
  return _internal_metrics_size();
}
inline void BenchmarkEntry::clear_metrics() {
  _impl_.metrics_.Clear();
}
inline ::tensorflow::MetricEntry* BenchmarkEntry::mutable_metrics(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BenchmarkEntry.metrics)
  return _impl_.metrics_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry >*
BenchmarkEntry::mutable_metrics() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BenchmarkEntry.metrics)
  return &_impl_.metrics_;
}
inline const ::tensorflow::MetricEntry& BenchmarkEntry::_internal_metrics(int index) const {
  return _impl_.metrics_.Get(index);
}
inline const ::tensorflow::MetricEntry& BenchmarkEntry::metrics(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntry.metrics)
  return _internal_metrics(index);
}
inline ::tensorflow::MetricEntry* BenchmarkEntry::_internal_add_metrics() {
  return _impl_.metrics_.Add();
}
inline ::tensorflow::MetricEntry* BenchmarkEntry::add_metrics() {
  ::tensorflow::MetricEntry* _add = _internal_add_metrics();
  // @@protoc_insertion_point(field_add:tensorflow.BenchmarkEntry.metrics)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MetricEntry >&
BenchmarkEntry::metrics() const {
  // @@protoc_insertion_point(field_list:tensorflow.BenchmarkEntry.metrics)
  return _impl_.metrics_;
}

// -------------------------------------------------------------------

// BenchmarkEntries

// repeated .tensorflow.BenchmarkEntry entry = 1;
inline int BenchmarkEntries::_internal_entry_size() const {
  return _impl_.entry_.size();
}
inline int BenchmarkEntries::entry_size() const {
  return _internal_entry_size();
}
inline void BenchmarkEntries::clear_entry() {
  _impl_.entry_.Clear();
}
inline ::tensorflow::BenchmarkEntry* BenchmarkEntries::mutable_entry(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BenchmarkEntries.entry)
  return _impl_.entry_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry >*
BenchmarkEntries::mutable_entry() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BenchmarkEntries.entry)
  return &_impl_.entry_;
}
inline const ::tensorflow::BenchmarkEntry& BenchmarkEntries::_internal_entry(int index) const {
  return _impl_.entry_.Get(index);
}
inline const ::tensorflow::BenchmarkEntry& BenchmarkEntries::entry(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BenchmarkEntries.entry)
  return _internal_entry(index);
}
inline ::tensorflow::BenchmarkEntry* BenchmarkEntries::_internal_add_entry() {
  return _impl_.entry_.Add();
}
inline ::tensorflow::BenchmarkEntry* BenchmarkEntries::add_entry() {
  ::tensorflow::BenchmarkEntry* _add = _internal_add_entry();
  // @@protoc_insertion_point(field_add:tensorflow.BenchmarkEntries.entry)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BenchmarkEntry >&
BenchmarkEntries::entry() const {
  // @@protoc_insertion_point(field_list:tensorflow.BenchmarkEntries.entry)
  return _impl_.entry_;
}

// -------------------------------------------------------------------

// BuildConfiguration

// string mode = 1;
inline void BuildConfiguration::clear_mode() {
  _impl_.mode_.ClearToEmpty();
}
inline const std::string& BuildConfiguration::mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.mode)
  return _internal_mode();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BuildConfiguration::set_mode(ArgT0&& arg0, ArgT... args) {
 
 _impl_.mode_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.mode)
}
inline std::string* BuildConfiguration::mutable_mode() {
  std::string* _s = _internal_mutable_mode();
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.mode)
  return _s;
}
inline const std::string& BuildConfiguration::_internal_mode() const {
  return _impl_.mode_.Get();
}
inline void BuildConfiguration::_internal_set_mode(const std::string& value) {
  
  _impl_.mode_.Set(value, GetArenaForAllocation());
}
inline std::string* BuildConfiguration::_internal_mutable_mode() {
  
  return _impl_.mode_.Mutable(GetArenaForAllocation());
}
inline std::string* BuildConfiguration::release_mode() {
  // @@protoc_insertion_point(field_release:tensorflow.BuildConfiguration.mode)
  return _impl_.mode_.Release();
}
inline void BuildConfiguration::set_allocated_mode(std::string* mode) {
  if (mode != nullptr) {
    
  } else {
    
  }
  _impl_.mode_.SetAllocated(mode, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.mode_.IsDefault()) {
    _impl_.mode_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BuildConfiguration.mode)
}

// repeated string cc_flags = 2;
inline int BuildConfiguration::_internal_cc_flags_size() const {
  return _impl_.cc_flags_.size();
}
inline int BuildConfiguration::cc_flags_size() const {
  return _internal_cc_flags_size();
}
inline void BuildConfiguration::clear_cc_flags() {
  _impl_.cc_flags_.Clear();
}
inline std::string* BuildConfiguration::add_cc_flags() {
  std::string* _s = _internal_add_cc_flags();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BuildConfiguration.cc_flags)
  return _s;
}
inline const std::string& BuildConfiguration::_internal_cc_flags(int index) const {
  return _impl_.cc_flags_.Get(index);
}
inline const std::string& BuildConfiguration::cc_flags(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.cc_flags)
  return _internal_cc_flags(index);
}
inline std::string* BuildConfiguration::mutable_cc_flags(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.cc_flags)
  return _impl_.cc_flags_.Mutable(index);
}
inline void BuildConfiguration::set_cc_flags(int index, const std::string& value) {
  _impl_.cc_flags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::set_cc_flags(int index, std::string&& value) {
  _impl_.cc_flags_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::set_cc_flags(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.cc_flags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::set_cc_flags(int index, const char* value, size_t size) {
  _impl_.cc_flags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BuildConfiguration.cc_flags)
}
inline std::string* BuildConfiguration::_internal_add_cc_flags() {
  return _impl_.cc_flags_.Add();
}
inline void BuildConfiguration::add_cc_flags(const std::string& value) {
  _impl_.cc_flags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::add_cc_flags(std::string&& value) {
  _impl_.cc_flags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::add_cc_flags(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.cc_flags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BuildConfiguration.cc_flags)
}
inline void BuildConfiguration::add_cc_flags(const char* value, size_t size) {
  _impl_.cc_flags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BuildConfiguration.cc_flags)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
BuildConfiguration::cc_flags() const {
  // @@protoc_insertion_point(field_list:tensorflow.BuildConfiguration.cc_flags)
  return _impl_.cc_flags_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
BuildConfiguration::mutable_cc_flags() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BuildConfiguration.cc_flags)
  return &_impl_.cc_flags_;
}

// repeated string opts = 3;
inline int BuildConfiguration::_internal_opts_size() const {
  return _impl_.opts_.size();
}
inline int BuildConfiguration::opts_size() const {
  return _internal_opts_size();
}
inline void BuildConfiguration::clear_opts() {
  _impl_.opts_.Clear();
}
inline std::string* BuildConfiguration::add_opts() {
  std::string* _s = _internal_add_opts();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.BuildConfiguration.opts)
  return _s;
}
inline const std::string& BuildConfiguration::_internal_opts(int index) const {
  return _impl_.opts_.Get(index);
}
inline const std::string& BuildConfiguration::opts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.BuildConfiguration.opts)
  return _internal_opts(index);
}
inline std::string* BuildConfiguration::mutable_opts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.BuildConfiguration.opts)
  return _impl_.opts_.Mutable(index);
}
inline void BuildConfiguration::set_opts(int index, const std::string& value) {
  _impl_.opts_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::set_opts(int index, std::string&& value) {
  _impl_.opts_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::set_opts(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.opts_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::set_opts(int index, const char* value, size_t size) {
  _impl_.opts_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.BuildConfiguration.opts)
}
inline std::string* BuildConfiguration::_internal_add_opts() {
  return _impl_.opts_.Add();
}
inline void BuildConfiguration::add_opts(const std::string& value) {
  _impl_.opts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::add_opts(std::string&& value) {
  _impl_.opts_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::add_opts(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.opts_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.BuildConfiguration.opts)
}
inline void BuildConfiguration::add_opts(const char* value, size_t size) {
  _impl_.opts_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.BuildConfiguration.opts)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
BuildConfiguration::opts() const {
  // @@protoc_insertion_point(field_list:tensorflow.BuildConfiguration.opts)
  return _impl_.opts_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
BuildConfiguration::mutable_opts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.BuildConfiguration.opts)
  return &_impl_.opts_;
}

// -------------------------------------------------------------------

// CommitId

// int64 changelist = 1;
inline bool CommitId::_internal_has_changelist() const {
  return kind_case() == kChangelist;
}
inline bool CommitId::has_changelist() const {
  return _internal_has_changelist();
}
inline void CommitId::set_has_changelist() {
  _impl_._oneof_case_[0] = kChangelist;
}
inline void CommitId::clear_changelist() {
  if (_internal_has_changelist()) {
    _impl_.kind_.changelist_ = int64_t{0};
    clear_has_kind();
  }
}
inline int64_t CommitId::_internal_changelist() const {
  if (_internal_has_changelist()) {
    return _impl_.kind_.changelist_;
  }
  return int64_t{0};
}
inline void CommitId::_internal_set_changelist(int64_t value) {
  if (!_internal_has_changelist()) {
    clear_kind();
    set_has_changelist();
  }
  _impl_.kind_.changelist_ = value;
}
inline int64_t CommitId::changelist() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.changelist)
  return _internal_changelist();
}
inline void CommitId::set_changelist(int64_t value) {
  _internal_set_changelist(value);
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.changelist)
}

// string hash = 2;
inline bool CommitId::_internal_has_hash() const {
  return kind_case() == kHash;
}
inline bool CommitId::has_hash() const {
  return _internal_has_hash();
}
inline void CommitId::set_has_hash() {
  _impl_._oneof_case_[0] = kHash;
}
inline void CommitId::clear_hash() {
  if (_internal_has_hash()) {
    _impl_.kind_.hash_.Destroy();
    clear_has_kind();
  }
}
inline const std::string& CommitId::hash() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.hash)
  return _internal_hash();
}
template <typename ArgT0, typename... ArgT>
inline void CommitId::set_hash(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_hash()) {
    clear_kind();
    set_has_hash();
    _impl_.kind_.hash_.InitDefault();
  }
  _impl_.kind_.hash_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.hash)
}
inline std::string* CommitId::mutable_hash() {
  std::string* _s = _internal_mutable_hash();
  // @@protoc_insertion_point(field_mutable:tensorflow.CommitId.hash)
  return _s;
}
inline const std::string& CommitId::_internal_hash() const {
  if (_internal_has_hash()) {
    return _impl_.kind_.hash_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void CommitId::_internal_set_hash(const std::string& value) {
  if (!_internal_has_hash()) {
    clear_kind();
    set_has_hash();
    _impl_.kind_.hash_.InitDefault();
  }
  _impl_.kind_.hash_.Set(value, GetArenaForAllocation());
}
inline std::string* CommitId::_internal_mutable_hash() {
  if (!_internal_has_hash()) {
    clear_kind();
    set_has_hash();
    _impl_.kind_.hash_.InitDefault();
  }
  return _impl_.kind_.hash_.Mutable(      GetArenaForAllocation());
}
inline std::string* CommitId::release_hash() {
  // @@protoc_insertion_point(field_release:tensorflow.CommitId.hash)
  if (_internal_has_hash()) {
    clear_has_kind();
    return _impl_.kind_.hash_.Release();
  } else {
    return nullptr;
  }
}
inline void CommitId::set_allocated_hash(std::string* hash) {
  if (has_kind()) {
    clear_kind();
  }
  if (hash != nullptr) {
    set_has_hash();
    _impl_.kind_.hash_.InitAllocated(hash, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CommitId.hash)
}

// string snapshot = 3;
inline void CommitId::clear_snapshot() {
  _impl_.snapshot_.ClearToEmpty();
}
inline const std::string& CommitId::snapshot() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.snapshot)
  return _internal_snapshot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CommitId::set_snapshot(ArgT0&& arg0, ArgT... args) {
 
 _impl_.snapshot_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.snapshot)
}
inline std::string* CommitId::mutable_snapshot() {
  std::string* _s = _internal_mutable_snapshot();
  // @@protoc_insertion_point(field_mutable:tensorflow.CommitId.snapshot)
  return _s;
}
inline const std::string& CommitId::_internal_snapshot() const {
  return _impl_.snapshot_.Get();
}
inline void CommitId::_internal_set_snapshot(const std::string& value) {
  
  _impl_.snapshot_.Set(value, GetArenaForAllocation());
}
inline std::string* CommitId::_internal_mutable_snapshot() {
  
  return _impl_.snapshot_.Mutable(GetArenaForAllocation());
}
inline std::string* CommitId::release_snapshot() {
  // @@protoc_insertion_point(field_release:tensorflow.CommitId.snapshot)
  return _impl_.snapshot_.Release();
}
inline void CommitId::set_allocated_snapshot(std::string* snapshot) {
  if (snapshot != nullptr) {
    
  } else {
    
  }
  _impl_.snapshot_.SetAllocated(snapshot, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.snapshot_.IsDefault()) {
    _impl_.snapshot_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CommitId.snapshot)
}

// int64 pending_changelist = 4;
inline void CommitId::clear_pending_changelist() {
  _impl_.pending_changelist_ = int64_t{0};
}
inline int64_t CommitId::_internal_pending_changelist() const {
  return _impl_.pending_changelist_;
}
inline int64_t CommitId::pending_changelist() const {
  // @@protoc_insertion_point(field_get:tensorflow.CommitId.pending_changelist)
  return _internal_pending_changelist();
}
inline void CommitId::_internal_set_pending_changelist(int64_t value) {
  
  _impl_.pending_changelist_ = value;
}
inline void CommitId::set_pending_changelist(int64_t value) {
  _internal_set_pending_changelist(value);
  // @@protoc_insertion_point(field_set:tensorflow.CommitId.pending_changelist)
}

inline bool CommitId::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void CommitId::clear_has_kind() {
  _impl_._oneof_case_[0] = KIND_NOT_SET;
}
inline CommitId::KindCase CommitId::kind_case() const {
  return CommitId::KindCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// CPUInfo

// int64 num_cores = 1;
inline void CPUInfo::clear_num_cores() {
  _impl_.num_cores_ = int64_t{0};
}
inline int64_t CPUInfo::_internal_num_cores() const {
  return _impl_.num_cores_;
}
inline int64_t CPUInfo::num_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.num_cores)
  return _internal_num_cores();
}
inline void CPUInfo::_internal_set_num_cores(int64_t value) {
  
  _impl_.num_cores_ = value;
}
inline void CPUInfo::set_num_cores(int64_t value) {
  _internal_set_num_cores(value);
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.num_cores)
}

// int64 num_cores_allowed = 2;
inline void CPUInfo::clear_num_cores_allowed() {
  _impl_.num_cores_allowed_ = int64_t{0};
}
inline int64_t CPUInfo::_internal_num_cores_allowed() const {
  return _impl_.num_cores_allowed_;
}
inline int64_t CPUInfo::num_cores_allowed() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.num_cores_allowed)
  return _internal_num_cores_allowed();
}
inline void CPUInfo::_internal_set_num_cores_allowed(int64_t value) {
  
  _impl_.num_cores_allowed_ = value;
}
inline void CPUInfo::set_num_cores_allowed(int64_t value) {
  _internal_set_num_cores_allowed(value);
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.num_cores_allowed)
}

// double mhz_per_cpu = 3;
inline void CPUInfo::clear_mhz_per_cpu() {
  _impl_.mhz_per_cpu_ = 0;
}
inline double CPUInfo::_internal_mhz_per_cpu() const {
  return _impl_.mhz_per_cpu_;
}
inline double CPUInfo::mhz_per_cpu() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.mhz_per_cpu)
  return _internal_mhz_per_cpu();
}
inline void CPUInfo::_internal_set_mhz_per_cpu(double value) {
  
  _impl_.mhz_per_cpu_ = value;
}
inline void CPUInfo::set_mhz_per_cpu(double value) {
  _internal_set_mhz_per_cpu(value);
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.mhz_per_cpu)
}

// string cpu_info = 4;
inline void CPUInfo::clear_cpu_info() {
  _impl_.cpu_info_.ClearToEmpty();
}
inline const std::string& CPUInfo::cpu_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.cpu_info)
  return _internal_cpu_info();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CPUInfo::set_cpu_info(ArgT0&& arg0, ArgT... args) {
 
 _impl_.cpu_info_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.cpu_info)
}
inline std::string* CPUInfo::mutable_cpu_info() {
  std::string* _s = _internal_mutable_cpu_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.CPUInfo.cpu_info)
  return _s;
}
inline const std::string& CPUInfo::_internal_cpu_info() const {
  return _impl_.cpu_info_.Get();
}
inline void CPUInfo::_internal_set_cpu_info(const std::string& value) {
  
  _impl_.cpu_info_.Set(value, GetArenaForAllocation());
}
inline std::string* CPUInfo::_internal_mutable_cpu_info() {
  
  return _impl_.cpu_info_.Mutable(GetArenaForAllocation());
}
inline std::string* CPUInfo::release_cpu_info() {
  // @@protoc_insertion_point(field_release:tensorflow.CPUInfo.cpu_info)
  return _impl_.cpu_info_.Release();
}
inline void CPUInfo::set_allocated_cpu_info(std::string* cpu_info) {
  if (cpu_info != nullptr) {
    
  } else {
    
  }
  _impl_.cpu_info_.SetAllocated(cpu_info, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.cpu_info_.IsDefault()) {
    _impl_.cpu_info_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CPUInfo.cpu_info)
}

// string cpu_governor = 5;
inline void CPUInfo::clear_cpu_governor() {
  _impl_.cpu_governor_.ClearToEmpty();
}
inline const std::string& CPUInfo::cpu_governor() const {
  // @@protoc_insertion_point(field_get:tensorflow.CPUInfo.cpu_governor)
  return _internal_cpu_governor();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CPUInfo::set_cpu_governor(ArgT0&& arg0, ArgT... args) {
 
 _impl_.cpu_governor_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CPUInfo.cpu_governor)
}
inline std::string* CPUInfo::mutable_cpu_governor() {
  std::string* _s = _internal_mutable_cpu_governor();
  // @@protoc_insertion_point(field_mutable:tensorflow.CPUInfo.cpu_governor)
  return _s;
}
inline const std::string& CPUInfo::_internal_cpu_governor() const {
  return _impl_.cpu_governor_.Get();
}
inline void CPUInfo::_internal_set_cpu_governor(const std::string& value) {
  
  _impl_.cpu_governor_.Set(value, GetArenaForAllocation());
}
inline std::string* CPUInfo::_internal_mutable_cpu_governor() {
  
  return _impl_.cpu_governor_.Mutable(GetArenaForAllocation());
}
inline std::string* CPUInfo::release_cpu_governor() {
  // @@protoc_insertion_point(field_release:tensorflow.CPUInfo.cpu_governor)
  return _impl_.cpu_governor_.Release();
}
inline void CPUInfo::set_allocated_cpu_governor(std::string* cpu_governor) {
  if (cpu_governor != nullptr) {
    
  } else {
    
  }
  _impl_.cpu_governor_.SetAllocated(cpu_governor, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.cpu_governor_.IsDefault()) {
    _impl_.cpu_governor_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CPUInfo.cpu_governor)
}

// map<string, int64> cache_size = 6;
inline int CPUInfo::_internal_cache_size_size() const {
  return _impl_.cache_size_.size();
}
inline int CPUInfo::cache_size_size() const {
  return _internal_cache_size_size();
}
inline void CPUInfo::clear_cache_size() {
  _impl_.cache_size_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >&
CPUInfo::_internal_cache_size() const {
  return _impl_.cache_size_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >&
CPUInfo::cache_size() const {
  // @@protoc_insertion_point(field_map:tensorflow.CPUInfo.cache_size)
  return _internal_cache_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >*
CPUInfo::_internal_mutable_cache_size() {
  return _impl_.cache_size_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, int64_t >*
CPUInfo::mutable_cache_size() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.CPUInfo.cache_size)
  return _internal_mutable_cache_size();
}

// -------------------------------------------------------------------

// MemoryInfo

// int64 total = 1;
inline void MemoryInfo::clear_total() {
  _impl_.total_ = int64_t{0};
}
inline int64_t MemoryInfo::_internal_total() const {
  return _impl_.total_;
}
inline int64_t MemoryInfo::total() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryInfo.total)
  return _internal_total();
}
inline void MemoryInfo::_internal_set_total(int64_t value) {
  
  _impl_.total_ = value;
}
inline void MemoryInfo::set_total(int64_t value) {
  _internal_set_total(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryInfo.total)
}

// int64 available = 2;
inline void MemoryInfo::clear_available() {
  _impl_.available_ = int64_t{0};
}
inline int64_t MemoryInfo::_internal_available() const {
  return _impl_.available_;
}
inline int64_t MemoryInfo::available() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryInfo.available)
  return _internal_available();
}
inline void MemoryInfo::_internal_set_available(int64_t value) {
  
  _impl_.available_ = value;
}
inline void MemoryInfo::set_available(int64_t value) {
  _internal_set_available(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemoryInfo.available)
}

// -------------------------------------------------------------------

// GPUInfo

// string model = 1;
inline void GPUInfo::clear_model() {
  _impl_.model_.ClearToEmpty();
}
inline const std::string& GPUInfo::model() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.model)
  return _internal_model();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GPUInfo::set_model(ArgT0&& arg0, ArgT... args) {
 
 _impl_.model_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.model)
}
inline std::string* GPUInfo::mutable_model() {
  std::string* _s = _internal_mutable_model();
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.model)
  return _s;
}
inline const std::string& GPUInfo::_internal_model() const {
  return _impl_.model_.Get();
}
inline void GPUInfo::_internal_set_model(const std::string& value) {
  
  _impl_.model_.Set(value, GetArenaForAllocation());
}
inline std::string* GPUInfo::_internal_mutable_model() {
  
  return _impl_.model_.Mutable(GetArenaForAllocation());
}
inline std::string* GPUInfo::release_model() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.model)
  return _impl_.model_.Release();
}
inline void GPUInfo::set_allocated_model(std::string* model) {
  if (model != nullptr) {
    
  } else {
    
  }
  _impl_.model_.SetAllocated(model, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.model_.IsDefault()) {
    _impl_.model_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.model)
}

// string uuid = 2;
inline void GPUInfo::clear_uuid() {
  _impl_.uuid_.ClearToEmpty();
}
inline const std::string& GPUInfo::uuid() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GPUInfo::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 _impl_.uuid_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.uuid)
}
inline std::string* GPUInfo::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.uuid)
  return _s;
}
inline const std::string& GPUInfo::_internal_uuid() const {
  return _impl_.uuid_.Get();
}
inline void GPUInfo::_internal_set_uuid(const std::string& value) {
  
  _impl_.uuid_.Set(value, GetArenaForAllocation());
}
inline std::string* GPUInfo::_internal_mutable_uuid() {
  
  return _impl_.uuid_.Mutable(GetArenaForAllocation());
}
inline std::string* GPUInfo::release_uuid() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.uuid)
  return _impl_.uuid_.Release();
}
inline void GPUInfo::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  _impl_.uuid_.SetAllocated(uuid, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.uuid_.IsDefault()) {
    _impl_.uuid_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.uuid)
}

// string bus_id = 3;
inline void GPUInfo::clear_bus_id() {
  _impl_.bus_id_.ClearToEmpty();
}
inline const std::string& GPUInfo::bus_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.GPUInfo.bus_id)
  return _internal_bus_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GPUInfo::set_bus_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.bus_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.GPUInfo.bus_id)
}
inline std::string* GPUInfo::mutable_bus_id() {
  std::string* _s = _internal_mutable_bus_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.GPUInfo.bus_id)
  return _s;
}
inline const std::string& GPUInfo::_internal_bus_id() const {
  return _impl_.bus_id_.Get();
}
inline void GPUInfo::_internal_set_bus_id(const std::string& value) {
  
  _impl_.bus_id_.Set(value, GetArenaForAllocation());
}
inline std::string* GPUInfo::_internal_mutable_bus_id() {
  
  return _impl_.bus_id_.Mutable(GetArenaForAllocation());
}
inline std::string* GPUInfo::release_bus_id() {
  // @@protoc_insertion_point(field_release:tensorflow.GPUInfo.bus_id)
  return _impl_.bus_id_.Release();
}
inline void GPUInfo::set_allocated_bus_id(std::string* bus_id) {
  if (bus_id != nullptr) {
    
  } else {
    
  }
  _impl_.bus_id_.SetAllocated(bus_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.bus_id_.IsDefault()) {
    _impl_.bus_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.GPUInfo.bus_id)
}

// -------------------------------------------------------------------

// PlatformInfo

// string bits = 1;
inline void PlatformInfo::clear_bits() {
  _impl_.bits_.ClearToEmpty();
}
inline const std::string& PlatformInfo::bits() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.bits)
  return _internal_bits();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlatformInfo::set_bits(ArgT0&& arg0, ArgT... args) {
 
 _impl_.bits_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.bits)
}
inline std::string* PlatformInfo::mutable_bits() {
  std::string* _s = _internal_mutable_bits();
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.bits)
  return _s;
}
inline const std::string& PlatformInfo::_internal_bits() const {
  return _impl_.bits_.Get();
}
inline void PlatformInfo::_internal_set_bits(const std::string& value) {
  
  _impl_.bits_.Set(value, GetArenaForAllocation());
}
inline std::string* PlatformInfo::_internal_mutable_bits() {
  
  return _impl_.bits_.Mutable(GetArenaForAllocation());
}
inline std::string* PlatformInfo::release_bits() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.bits)
  return _impl_.bits_.Release();
}
inline void PlatformInfo::set_allocated_bits(std::string* bits) {
  if (bits != nullptr) {
    
  } else {
    
  }
  _impl_.bits_.SetAllocated(bits, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.bits_.IsDefault()) {
    _impl_.bits_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.bits)
}

// string linkage = 2;
inline void PlatformInfo::clear_linkage() {
  _impl_.linkage_.ClearToEmpty();
}
inline const std::string& PlatformInfo::linkage() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.linkage)
  return _internal_linkage();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlatformInfo::set_linkage(ArgT0&& arg0, ArgT... args) {
 
 _impl_.linkage_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.linkage)
}
inline std::string* PlatformInfo::mutable_linkage() {
  std::string* _s = _internal_mutable_linkage();
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.linkage)
  return _s;
}
inline const std::string& PlatformInfo::_internal_linkage() const {
  return _impl_.linkage_.Get();
}
inline void PlatformInfo::_internal_set_linkage(const std::string& value) {
  
  _impl_.linkage_.Set(value, GetArenaForAllocation());
}
inline std::string* PlatformInfo::_internal_mutable_linkage() {
  
  return _impl_.linkage_.Mutable(GetArenaForAllocation());
}
inline std::string* PlatformInfo::release_linkage() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.linkage)
  return _impl_.linkage_.Release();
}
inline void PlatformInfo::set_allocated_linkage(std::string* linkage) {
  if (linkage != nullptr) {
    
  } else {
    
  }
  _impl_.linkage_.SetAllocated(linkage, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.linkage_.IsDefault()) {
    _impl_.linkage_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.linkage)
}

// string machine = 3;
inline void PlatformInfo::clear_machine() {
  _impl_.machine_.ClearToEmpty();
}
inline const std::string& PlatformInfo::machine() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.machine)
  return _internal_machine();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlatformInfo::set_machine(ArgT0&& arg0, ArgT... args) {
 
 _impl_.machine_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.machine)
}
inline std::string* PlatformInfo::mutable_machine() {
  std::string* _s = _internal_mutable_machine();
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.machine)
  return _s;
}
inline const std::string& PlatformInfo::_internal_machine() const {
  return _impl_.machine_.Get();
}
inline void PlatformInfo::_internal_set_machine(const std::string& value) {
  
  _impl_.machine_.Set(value, GetArenaForAllocation());
}
inline std::string* PlatformInfo::_internal_mutable_machine() {
  
  return _impl_.machine_.Mutable(GetArenaForAllocation());
}
inline std::string* PlatformInfo::release_machine() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.machine)
  return _impl_.machine_.Release();
}
inline void PlatformInfo::set_allocated_machine(std::string* machine) {
  if (machine != nullptr) {
    
  } else {
    
  }
  _impl_.machine_.SetAllocated(machine, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.machine_.IsDefault()) {
    _impl_.machine_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.machine)
}

// string release = 4;
inline void PlatformInfo::clear_release() {
  _impl_.release_.ClearToEmpty();
}
inline const std::string& PlatformInfo::release() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.release)
  return _internal_release();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlatformInfo::set_release(ArgT0&& arg0, ArgT... args) {
 
 _impl_.release_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.release)
}
inline std::string* PlatformInfo::mutable_release() {
  std::string* _s = _internal_mutable_release();
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.release)
  return _s;
}
inline const std::string& PlatformInfo::_internal_release() const {
  return _impl_.release_.Get();
}
inline void PlatformInfo::_internal_set_release(const std::string& value) {
  
  _impl_.release_.Set(value, GetArenaForAllocation());
}
inline std::string* PlatformInfo::_internal_mutable_release() {
  
  return _impl_.release_.Mutable(GetArenaForAllocation());
}
inline std::string* PlatformInfo::release_release() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.release)
  return _impl_.release_.Release();
}
inline void PlatformInfo::set_allocated_release(std::string* release) {
  if (release != nullptr) {
    
  } else {
    
  }
  _impl_.release_.SetAllocated(release, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.release_.IsDefault()) {
    _impl_.release_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.release)
}

// string system = 5;
inline void PlatformInfo::clear_system() {
  _impl_.system_.ClearToEmpty();
}
inline const std::string& PlatformInfo::system() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.system)
  return _internal_system();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlatformInfo::set_system(ArgT0&& arg0, ArgT... args) {
 
 _impl_.system_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.system)
}
inline std::string* PlatformInfo::mutable_system() {
  std::string* _s = _internal_mutable_system();
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.system)
  return _s;
}
inline const std::string& PlatformInfo::_internal_system() const {
  return _impl_.system_.Get();
}
inline void PlatformInfo::_internal_set_system(const std::string& value) {
  
  _impl_.system_.Set(value, GetArenaForAllocation());
}
inline std::string* PlatformInfo::_internal_mutable_system() {
  
  return _impl_.system_.Mutable(GetArenaForAllocation());
}
inline std::string* PlatformInfo::release_system() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.system)
  return _impl_.system_.Release();
}
inline void PlatformInfo::set_allocated_system(std::string* system) {
  if (system != nullptr) {
    
  } else {
    
  }
  _impl_.system_.SetAllocated(system, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.system_.IsDefault()) {
    _impl_.system_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.system)
}

// string version = 6;
inline void PlatformInfo::clear_version() {
  _impl_.version_.ClearToEmpty();
}
inline const std::string& PlatformInfo::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.PlatformInfo.version)
  return _internal_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlatformInfo::set_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PlatformInfo.version)
}
inline std::string* PlatformInfo::mutable_version() {
  std::string* _s = _internal_mutable_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.PlatformInfo.version)
  return _s;
}
inline const std::string& PlatformInfo::_internal_version() const {
  return _impl_.version_.Get();
}
inline void PlatformInfo::_internal_set_version(const std::string& value) {
  
  _impl_.version_.Set(value, GetArenaForAllocation());
}
inline std::string* PlatformInfo::_internal_mutable_version() {
  
  return _impl_.version_.Mutable(GetArenaForAllocation());
}
inline std::string* PlatformInfo::release_version() {
  // @@protoc_insertion_point(field_release:tensorflow.PlatformInfo.version)
  return _impl_.version_.Release();
}
inline void PlatformInfo::set_allocated_version(std::string* version) {
  if (version != nullptr) {
    
  } else {
    
  }
  _impl_.version_.SetAllocated(version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.version_.IsDefault()) {
    _impl_.version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PlatformInfo.version)
}

// -------------------------------------------------------------------

// AvailableDeviceInfo

// string name = 1;
inline void AvailableDeviceInfo::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& AvailableDeviceInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AvailableDeviceInfo::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.name)
}
inline std::string* AvailableDeviceInfo::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.name)
  return _s;
}
inline const std::string& AvailableDeviceInfo::_internal_name() const {
  return _impl_.name_.Get();
}
inline void AvailableDeviceInfo::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* AvailableDeviceInfo::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* AvailableDeviceInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.name)
  return _impl_.name_.Release();
}
inline void AvailableDeviceInfo::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.name)
}

// string type = 2;
inline void AvailableDeviceInfo::clear_type() {
  _impl_.type_.ClearToEmpty();
}
inline const std::string& AvailableDeviceInfo::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AvailableDeviceInfo::set_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.type)
}
inline std::string* AvailableDeviceInfo::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.type)
  return _s;
}
inline const std::string& AvailableDeviceInfo::_internal_type() const {
  return _impl_.type_.Get();
}
inline void AvailableDeviceInfo::_internal_set_type(const std::string& value) {
  
  _impl_.type_.Set(value, GetArenaForAllocation());
}
inline std::string* AvailableDeviceInfo::_internal_mutable_type() {
  
  return _impl_.type_.Mutable(GetArenaForAllocation());
}
inline std::string* AvailableDeviceInfo::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.type)
  return _impl_.type_.Release();
}
inline void AvailableDeviceInfo::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  _impl_.type_.SetAllocated(type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_.IsDefault()) {
    _impl_.type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.type)
}

// int64 memory_limit = 3;
inline void AvailableDeviceInfo::clear_memory_limit() {
  _impl_.memory_limit_ = int64_t{0};
}
inline int64_t AvailableDeviceInfo::_internal_memory_limit() const {
  return _impl_.memory_limit_;
}
inline int64_t AvailableDeviceInfo::memory_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.memory_limit)
  return _internal_memory_limit();
}
inline void AvailableDeviceInfo::_internal_set_memory_limit(int64_t value) {
  
  _impl_.memory_limit_ = value;
}
inline void AvailableDeviceInfo::set_memory_limit(int64_t value) {
  _internal_set_memory_limit(value);
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.memory_limit)
}

// string physical_description = 4;
inline void AvailableDeviceInfo::clear_physical_description() {
  _impl_.physical_description_.ClearToEmpty();
}
inline const std::string& AvailableDeviceInfo::physical_description() const {
  // @@protoc_insertion_point(field_get:tensorflow.AvailableDeviceInfo.physical_description)
  return _internal_physical_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AvailableDeviceInfo::set_physical_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.physical_description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.AvailableDeviceInfo.physical_description)
}
inline std::string* AvailableDeviceInfo::mutable_physical_description() {
  std::string* _s = _internal_mutable_physical_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.AvailableDeviceInfo.physical_description)
  return _s;
}
inline const std::string& AvailableDeviceInfo::_internal_physical_description() const {
  return _impl_.physical_description_.Get();
}
inline void AvailableDeviceInfo::_internal_set_physical_description(const std::string& value) {
  
  _impl_.physical_description_.Set(value, GetArenaForAllocation());
}
inline std::string* AvailableDeviceInfo::_internal_mutable_physical_description() {
  
  return _impl_.physical_description_.Mutable(GetArenaForAllocation());
}
inline std::string* AvailableDeviceInfo::release_physical_description() {
  // @@protoc_insertion_point(field_release:tensorflow.AvailableDeviceInfo.physical_description)
  return _impl_.physical_description_.Release();
}
inline void AvailableDeviceInfo::set_allocated_physical_description(std::string* physical_description) {
  if (physical_description != nullptr) {
    
  } else {
    
  }
  _impl_.physical_description_.SetAllocated(physical_description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.physical_description_.IsDefault()) {
    _impl_.physical_description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AvailableDeviceInfo.physical_description)
}

// -------------------------------------------------------------------

// MachineConfiguration

// string hostname = 1;
inline void MachineConfiguration::clear_hostname() {
  _impl_.hostname_.ClearToEmpty();
}
inline const std::string& MachineConfiguration::hostname() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.hostname)
  return _internal_hostname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MachineConfiguration::set_hostname(ArgT0&& arg0, ArgT... args) {
 
 _impl_.hostname_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MachineConfiguration.hostname)
}
inline std::string* MachineConfiguration::mutable_hostname() {
  std::string* _s = _internal_mutable_hostname();
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.hostname)
  return _s;
}
inline const std::string& MachineConfiguration::_internal_hostname() const {
  return _impl_.hostname_.Get();
}
inline void MachineConfiguration::_internal_set_hostname(const std::string& value) {
  
  _impl_.hostname_.Set(value, GetArenaForAllocation());
}
inline std::string* MachineConfiguration::_internal_mutable_hostname() {
  
  return _impl_.hostname_.Mutable(GetArenaForAllocation());
}
inline std::string* MachineConfiguration::release_hostname() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.hostname)
  return _impl_.hostname_.Release();
}
inline void MachineConfiguration::set_allocated_hostname(std::string* hostname) {
  if (hostname != nullptr) {
    
  } else {
    
  }
  _impl_.hostname_.SetAllocated(hostname, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.hostname_.IsDefault()) {
    _impl_.hostname_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.hostname)
}

// string serial_identifier = 7;
inline void MachineConfiguration::clear_serial_identifier() {
  _impl_.serial_identifier_.ClearToEmpty();
}
inline const std::string& MachineConfiguration::serial_identifier() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.serial_identifier)
  return _internal_serial_identifier();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MachineConfiguration::set_serial_identifier(ArgT0&& arg0, ArgT... args) {
 
 _impl_.serial_identifier_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MachineConfiguration.serial_identifier)
}
inline std::string* MachineConfiguration::mutable_serial_identifier() {
  std::string* _s = _internal_mutable_serial_identifier();
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.serial_identifier)
  return _s;
}
inline const std::string& MachineConfiguration::_internal_serial_identifier() const {
  return _impl_.serial_identifier_.Get();
}
inline void MachineConfiguration::_internal_set_serial_identifier(const std::string& value) {
  
  _impl_.serial_identifier_.Set(value, GetArenaForAllocation());
}
inline std::string* MachineConfiguration::_internal_mutable_serial_identifier() {
  
  return _impl_.serial_identifier_.Mutable(GetArenaForAllocation());
}
inline std::string* MachineConfiguration::release_serial_identifier() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.serial_identifier)
  return _impl_.serial_identifier_.Release();
}
inline void MachineConfiguration::set_allocated_serial_identifier(std::string* serial_identifier) {
  if (serial_identifier != nullptr) {
    
  } else {
    
  }
  _impl_.serial_identifier_.SetAllocated(serial_identifier, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.serial_identifier_.IsDefault()) {
    _impl_.serial_identifier_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.serial_identifier)
}

// .tensorflow.PlatformInfo platform_info = 2;
inline bool MachineConfiguration::_internal_has_platform_info() const {
  return this != internal_default_instance() && _impl_.platform_info_ != nullptr;
}
inline bool MachineConfiguration::has_platform_info() const {
  return _internal_has_platform_info();
}
inline void MachineConfiguration::clear_platform_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.platform_info_ != nullptr) {
    delete _impl_.platform_info_;
  }
  _impl_.platform_info_ = nullptr;
}
inline const ::tensorflow::PlatformInfo& MachineConfiguration::_internal_platform_info() const {
  const ::tensorflow::PlatformInfo* p = _impl_.platform_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::PlatformInfo&>(
      ::tensorflow::_PlatformInfo_default_instance_);
}
inline const ::tensorflow::PlatformInfo& MachineConfiguration::platform_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.platform_info)
  return _internal_platform_info();
}
inline void MachineConfiguration::unsafe_arena_set_allocated_platform_info(
    ::tensorflow::PlatformInfo* platform_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.platform_info_);
  }
  _impl_.platform_info_ = platform_info;
  if (platform_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.platform_info)
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::release_platform_info() {
  
  ::tensorflow::PlatformInfo* temp = _impl_.platform_info_;
  _impl_.platform_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::unsafe_arena_release_platform_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.platform_info)
  
  ::tensorflow::PlatformInfo* temp = _impl_.platform_info_;
  _impl_.platform_info_ = nullptr;
  return temp;
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::_internal_mutable_platform_info() {
  
  if (_impl_.platform_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::PlatformInfo>(GetArenaForAllocation());
    _impl_.platform_info_ = p;
  }
  return _impl_.platform_info_;
}
inline ::tensorflow::PlatformInfo* MachineConfiguration::mutable_platform_info() {
  ::tensorflow::PlatformInfo* _msg = _internal_mutable_platform_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.platform_info)
  return _msg;
}
inline void MachineConfiguration::set_allocated_platform_info(::tensorflow::PlatformInfo* platform_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.platform_info_;
  }
  if (platform_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(platform_info);
    if (message_arena != submessage_arena) {
      platform_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, platform_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.platform_info_ = platform_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.platform_info)
}

// .tensorflow.CPUInfo cpu_info = 3;
inline bool MachineConfiguration::_internal_has_cpu_info() const {
  return this != internal_default_instance() && _impl_.cpu_info_ != nullptr;
}
inline bool MachineConfiguration::has_cpu_info() const {
  return _internal_has_cpu_info();
}
inline void MachineConfiguration::clear_cpu_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.cpu_info_ != nullptr) {
    delete _impl_.cpu_info_;
  }
  _impl_.cpu_info_ = nullptr;
}
inline const ::tensorflow::CPUInfo& MachineConfiguration::_internal_cpu_info() const {
  const ::tensorflow::CPUInfo* p = _impl_.cpu_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CPUInfo&>(
      ::tensorflow::_CPUInfo_default_instance_);
}
inline const ::tensorflow::CPUInfo& MachineConfiguration::cpu_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.cpu_info)
  return _internal_cpu_info();
}
inline void MachineConfiguration::unsafe_arena_set_allocated_cpu_info(
    ::tensorflow::CPUInfo* cpu_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.cpu_info_);
  }
  _impl_.cpu_info_ = cpu_info;
  if (cpu_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.cpu_info)
}
inline ::tensorflow::CPUInfo* MachineConfiguration::release_cpu_info() {
  
  ::tensorflow::CPUInfo* temp = _impl_.cpu_info_;
  _impl_.cpu_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CPUInfo* MachineConfiguration::unsafe_arena_release_cpu_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.cpu_info)
  
  ::tensorflow::CPUInfo* temp = _impl_.cpu_info_;
  _impl_.cpu_info_ = nullptr;
  return temp;
}
inline ::tensorflow::CPUInfo* MachineConfiguration::_internal_mutable_cpu_info() {
  
  if (_impl_.cpu_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CPUInfo>(GetArenaForAllocation());
    _impl_.cpu_info_ = p;
  }
  return _impl_.cpu_info_;
}
inline ::tensorflow::CPUInfo* MachineConfiguration::mutable_cpu_info() {
  ::tensorflow::CPUInfo* _msg = _internal_mutable_cpu_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.cpu_info)
  return _msg;
}
inline void MachineConfiguration::set_allocated_cpu_info(::tensorflow::CPUInfo* cpu_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.cpu_info_;
  }
  if (cpu_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(cpu_info);
    if (message_arena != submessage_arena) {
      cpu_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cpu_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.cpu_info_ = cpu_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.cpu_info)
}

// repeated .google.protobuf.Any device_info = 4;
inline int MachineConfiguration::_internal_device_info_size() const {
  return _impl_.device_info_.size();
}
inline int MachineConfiguration::device_info_size() const {
  return _internal_device_info_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Any* MachineConfiguration::mutable_device_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.device_info)
  return _impl_.device_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >*
MachineConfiguration::mutable_device_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MachineConfiguration.device_info)
  return &_impl_.device_info_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& MachineConfiguration::_internal_device_info(int index) const {
  return _impl_.device_info_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& MachineConfiguration::device_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.device_info)
  return _internal_device_info(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Any* MachineConfiguration::_internal_add_device_info() {
  return _impl_.device_info_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Any* MachineConfiguration::add_device_info() {
  ::PROTOBUF_NAMESPACE_ID::Any* _add = _internal_add_device_info();
  // @@protoc_insertion_point(field_add:tensorflow.MachineConfiguration.device_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >&
MachineConfiguration::device_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.MachineConfiguration.device_info)
  return _impl_.device_info_;
}

// repeated .tensorflow.AvailableDeviceInfo available_device_info = 5;
inline int MachineConfiguration::_internal_available_device_info_size() const {
  return _impl_.available_device_info_.size();
}
inline int MachineConfiguration::available_device_info_size() const {
  return _internal_available_device_info_size();
}
inline void MachineConfiguration::clear_available_device_info() {
  _impl_.available_device_info_.Clear();
}
inline ::tensorflow::AvailableDeviceInfo* MachineConfiguration::mutable_available_device_info(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.available_device_info)
  return _impl_.available_device_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >*
MachineConfiguration::mutable_available_device_info() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MachineConfiguration.available_device_info)
  return &_impl_.available_device_info_;
}
inline const ::tensorflow::AvailableDeviceInfo& MachineConfiguration::_internal_available_device_info(int index) const {
  return _impl_.available_device_info_.Get(index);
}
inline const ::tensorflow::AvailableDeviceInfo& MachineConfiguration::available_device_info(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.available_device_info)
  return _internal_available_device_info(index);
}
inline ::tensorflow::AvailableDeviceInfo* MachineConfiguration::_internal_add_available_device_info() {
  return _impl_.available_device_info_.Add();
}
inline ::tensorflow::AvailableDeviceInfo* MachineConfiguration::add_available_device_info() {
  ::tensorflow::AvailableDeviceInfo* _add = _internal_add_available_device_info();
  // @@protoc_insertion_point(field_add:tensorflow.MachineConfiguration.available_device_info)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AvailableDeviceInfo >&
MachineConfiguration::available_device_info() const {
  // @@protoc_insertion_point(field_list:tensorflow.MachineConfiguration.available_device_info)
  return _impl_.available_device_info_;
}

// .tensorflow.MemoryInfo memory_info = 6;
inline bool MachineConfiguration::_internal_has_memory_info() const {
  return this != internal_default_instance() && _impl_.memory_info_ != nullptr;
}
inline bool MachineConfiguration::has_memory_info() const {
  return _internal_has_memory_info();
}
inline void MachineConfiguration::clear_memory_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.memory_info_ != nullptr) {
    delete _impl_.memory_info_;
  }
  _impl_.memory_info_ = nullptr;
}
inline const ::tensorflow::MemoryInfo& MachineConfiguration::_internal_memory_info() const {
  const ::tensorflow::MemoryInfo* p = _impl_.memory_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::MemoryInfo&>(
      ::tensorflow::_MemoryInfo_default_instance_);
}
inline const ::tensorflow::MemoryInfo& MachineConfiguration::memory_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.MachineConfiguration.memory_info)
  return _internal_memory_info();
}
inline void MachineConfiguration::unsafe_arena_set_allocated_memory_info(
    ::tensorflow::MemoryInfo* memory_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.memory_info_);
  }
  _impl_.memory_info_ = memory_info;
  if (memory_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MachineConfiguration.memory_info)
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::release_memory_info() {
  
  ::tensorflow::MemoryInfo* temp = _impl_.memory_info_;
  _impl_.memory_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::unsafe_arena_release_memory_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MachineConfiguration.memory_info)
  
  ::tensorflow::MemoryInfo* temp = _impl_.memory_info_;
  _impl_.memory_info_ = nullptr;
  return temp;
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::_internal_mutable_memory_info() {
  
  if (_impl_.memory_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MemoryInfo>(GetArenaForAllocation());
    _impl_.memory_info_ = p;
  }
  return _impl_.memory_info_;
}
inline ::tensorflow::MemoryInfo* MachineConfiguration::mutable_memory_info() {
  ::tensorflow::MemoryInfo* _msg = _internal_mutable_memory_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.MachineConfiguration.memory_info)
  return _msg;
}
inline void MachineConfiguration::set_allocated_memory_info(::tensorflow::MemoryInfo* memory_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.memory_info_;
  }
  if (memory_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(memory_info);
    if (message_arena != submessage_arena) {
      memory_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, memory_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.memory_info_ = memory_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MachineConfiguration.memory_info)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// RunConfiguration

// repeated string argument = 1;
inline int RunConfiguration::_internal_argument_size() const {
  return _impl_.argument_.size();
}
inline int RunConfiguration::argument_size() const {
  return _internal_argument_size();
}
inline void RunConfiguration::clear_argument() {
  _impl_.argument_.Clear();
}
inline std::string* RunConfiguration::add_argument() {
  std::string* _s = _internal_add_argument();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunConfiguration.argument)
  return _s;
}
inline const std::string& RunConfiguration::_internal_argument(int index) const {
  return _impl_.argument_.Get(index);
}
inline const std::string& RunConfiguration::argument(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunConfiguration.argument)
  return _internal_argument(index);
}
inline std::string* RunConfiguration::mutable_argument(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunConfiguration.argument)
  return _impl_.argument_.Mutable(index);
}
inline void RunConfiguration::set_argument(int index, const std::string& value) {
  _impl_.argument_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::set_argument(int index, std::string&& value) {
  _impl_.argument_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::set_argument(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.argument_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::set_argument(int index, const char* value, size_t size) {
  _impl_.argument_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunConfiguration.argument)
}
inline std::string* RunConfiguration::_internal_add_argument() {
  return _impl_.argument_.Add();
}
inline void RunConfiguration::add_argument(const std::string& value) {
  _impl_.argument_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::add_argument(std::string&& value) {
  _impl_.argument_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::add_argument(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.argument_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunConfiguration.argument)
}
inline void RunConfiguration::add_argument(const char* value, size_t size) {
  _impl_.argument_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunConfiguration.argument)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RunConfiguration::argument() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunConfiguration.argument)
  return _impl_.argument_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RunConfiguration::mutable_argument() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunConfiguration.argument)
  return &_impl_.argument_;
}

// map<string, string> env_vars = 2;
inline int RunConfiguration::_internal_env_vars_size() const {
  return _impl_.env_vars_.size();
}
inline int RunConfiguration::env_vars_size() const {
  return _internal_env_vars_size();
}
inline void RunConfiguration::clear_env_vars() {
  _impl_.env_vars_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
RunConfiguration::_internal_env_vars() const {
  return _impl_.env_vars_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
RunConfiguration::env_vars() const {
  // @@protoc_insertion_point(field_map:tensorflow.RunConfiguration.env_vars)
  return _internal_env_vars();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
RunConfiguration::_internal_mutable_env_vars() {
  return _impl_.env_vars_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
RunConfiguration::mutable_env_vars() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.RunConfiguration.env_vars)
  return _internal_mutable_env_vars();
}

// -------------------------------------------------------------------

// TestResults

// string target = 1;
inline void TestResults::clear_target() {
  _impl_.target_.ClearToEmpty();
}
inline const std::string& TestResults::target() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.target)
  return _internal_target();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestResults::set_target(ArgT0&& arg0, ArgT... args) {
 
 _impl_.target_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.target)
}
inline std::string* TestResults::mutable_target() {
  std::string* _s = _internal_mutable_target();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.target)
  return _s;
}
inline const std::string& TestResults::_internal_target() const {
  return _impl_.target_.Get();
}
inline void TestResults::_internal_set_target(const std::string& value) {
  
  _impl_.target_.Set(value, GetArenaForAllocation());
}
inline std::string* TestResults::_internal_mutable_target() {
  
  return _impl_.target_.Mutable(GetArenaForAllocation());
}
inline std::string* TestResults::release_target() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.target)
  return _impl_.target_.Release();
}
inline void TestResults::set_allocated_target(std::string* target) {
  if (target != nullptr) {
    
  } else {
    
  }
  _impl_.target_.SetAllocated(target, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.target_.IsDefault()) {
    _impl_.target_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.target)
}

// .tensorflow.BenchmarkEntries entries = 2;
inline bool TestResults::_internal_has_entries() const {
  return this != internal_default_instance() && _impl_.entries_ != nullptr;
}
inline bool TestResults::has_entries() const {
  return _internal_has_entries();
}
inline void TestResults::clear_entries() {
  if (GetArenaForAllocation() == nullptr && _impl_.entries_ != nullptr) {
    delete _impl_.entries_;
  }
  _impl_.entries_ = nullptr;
}
inline const ::tensorflow::BenchmarkEntries& TestResults::_internal_entries() const {
  const ::tensorflow::BenchmarkEntries* p = _impl_.entries_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::BenchmarkEntries&>(
      ::tensorflow::_BenchmarkEntries_default_instance_);
}
inline const ::tensorflow::BenchmarkEntries& TestResults::entries() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.entries)
  return _internal_entries();
}
inline void TestResults::unsafe_arena_set_allocated_entries(
    ::tensorflow::BenchmarkEntries* entries) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.entries_);
  }
  _impl_.entries_ = entries;
  if (entries) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.entries)
}
inline ::tensorflow::BenchmarkEntries* TestResults::release_entries() {
  
  ::tensorflow::BenchmarkEntries* temp = _impl_.entries_;
  _impl_.entries_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::BenchmarkEntries* TestResults::unsafe_arena_release_entries() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.entries)
  
  ::tensorflow::BenchmarkEntries* temp = _impl_.entries_;
  _impl_.entries_ = nullptr;
  return temp;
}
inline ::tensorflow::BenchmarkEntries* TestResults::_internal_mutable_entries() {
  
  if (_impl_.entries_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::BenchmarkEntries>(GetArenaForAllocation());
    _impl_.entries_ = p;
  }
  return _impl_.entries_;
}
inline ::tensorflow::BenchmarkEntries* TestResults::mutable_entries() {
  ::tensorflow::BenchmarkEntries* _msg = _internal_mutable_entries();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.entries)
  return _msg;
}
inline void TestResults::set_allocated_entries(::tensorflow::BenchmarkEntries* entries) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.entries_;
  }
  if (entries) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(entries);
    if (message_arena != submessage_arena) {
      entries = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, entries, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.entries_ = entries;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.entries)
}

// .tensorflow.BuildConfiguration build_configuration = 3;
inline bool TestResults::_internal_has_build_configuration() const {
  return this != internal_default_instance() && _impl_.build_configuration_ != nullptr;
}
inline bool TestResults::has_build_configuration() const {
  return _internal_has_build_configuration();
}
inline void TestResults::clear_build_configuration() {
  if (GetArenaForAllocation() == nullptr && _impl_.build_configuration_ != nullptr) {
    delete _impl_.build_configuration_;
  }
  _impl_.build_configuration_ = nullptr;
}
inline const ::tensorflow::BuildConfiguration& TestResults::_internal_build_configuration() const {
  const ::tensorflow::BuildConfiguration* p = _impl_.build_configuration_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::BuildConfiguration&>(
      ::tensorflow::_BuildConfiguration_default_instance_);
}
inline const ::tensorflow::BuildConfiguration& TestResults::build_configuration() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.build_configuration)
  return _internal_build_configuration();
}
inline void TestResults::unsafe_arena_set_allocated_build_configuration(
    ::tensorflow::BuildConfiguration* build_configuration) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.build_configuration_);
  }
  _impl_.build_configuration_ = build_configuration;
  if (build_configuration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.build_configuration)
}
inline ::tensorflow::BuildConfiguration* TestResults::release_build_configuration() {
  
  ::tensorflow::BuildConfiguration* temp = _impl_.build_configuration_;
  _impl_.build_configuration_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::BuildConfiguration* TestResults::unsafe_arena_release_build_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.build_configuration)
  
  ::tensorflow::BuildConfiguration* temp = _impl_.build_configuration_;
  _impl_.build_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::BuildConfiguration* TestResults::_internal_mutable_build_configuration() {
  
  if (_impl_.build_configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::BuildConfiguration>(GetArenaForAllocation());
    _impl_.build_configuration_ = p;
  }
  return _impl_.build_configuration_;
}
inline ::tensorflow::BuildConfiguration* TestResults::mutable_build_configuration() {
  ::tensorflow::BuildConfiguration* _msg = _internal_mutable_build_configuration();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.build_configuration)
  return _msg;
}
inline void TestResults::set_allocated_build_configuration(::tensorflow::BuildConfiguration* build_configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.build_configuration_;
  }
  if (build_configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(build_configuration);
    if (message_arena != submessage_arena) {
      build_configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, build_configuration, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.build_configuration_ = build_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.build_configuration)
}

// .tensorflow.CommitId commit_id = 4;
inline bool TestResults::_internal_has_commit_id() const {
  return this != internal_default_instance() && _impl_.commit_id_ != nullptr;
}
inline bool TestResults::has_commit_id() const {
  return _internal_has_commit_id();
}
inline void TestResults::clear_commit_id() {
  if (GetArenaForAllocation() == nullptr && _impl_.commit_id_ != nullptr) {
    delete _impl_.commit_id_;
  }
  _impl_.commit_id_ = nullptr;
}
inline const ::tensorflow::CommitId& TestResults::_internal_commit_id() const {
  const ::tensorflow::CommitId* p = _impl_.commit_id_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CommitId&>(
      ::tensorflow::_CommitId_default_instance_);
}
inline const ::tensorflow::CommitId& TestResults::commit_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.commit_id)
  return _internal_commit_id();
}
inline void TestResults::unsafe_arena_set_allocated_commit_id(
    ::tensorflow::CommitId* commit_id) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.commit_id_);
  }
  _impl_.commit_id_ = commit_id;
  if (commit_id) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.commit_id)
}
inline ::tensorflow::CommitId* TestResults::release_commit_id() {
  
  ::tensorflow::CommitId* temp = _impl_.commit_id_;
  _impl_.commit_id_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CommitId* TestResults::unsafe_arena_release_commit_id() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.commit_id)
  
  ::tensorflow::CommitId* temp = _impl_.commit_id_;
  _impl_.commit_id_ = nullptr;
  return temp;
}
inline ::tensorflow::CommitId* TestResults::_internal_mutable_commit_id() {
  
  if (_impl_.commit_id_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CommitId>(GetArenaForAllocation());
    _impl_.commit_id_ = p;
  }
  return _impl_.commit_id_;
}
inline ::tensorflow::CommitId* TestResults::mutable_commit_id() {
  ::tensorflow::CommitId* _msg = _internal_mutable_commit_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.commit_id)
  return _msg;
}
inline void TestResults::set_allocated_commit_id(::tensorflow::CommitId* commit_id) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.commit_id_;
  }
  if (commit_id) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(commit_id);
    if (message_arena != submessage_arena) {
      commit_id = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, commit_id, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.commit_id_ = commit_id;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.commit_id)
}

// int64 start_time = 5;
inline void TestResults::clear_start_time() {
  _impl_.start_time_ = int64_t{0};
}
inline int64_t TestResults::_internal_start_time() const {
  return _impl_.start_time_;
}
inline int64_t TestResults::start_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.start_time)
  return _internal_start_time();
}
inline void TestResults::_internal_set_start_time(int64_t value) {
  
  _impl_.start_time_ = value;
}
inline void TestResults::set_start_time(int64_t value) {
  _internal_set_start_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.start_time)
}

// double run_time = 6;
inline void TestResults::clear_run_time() {
  _impl_.run_time_ = 0;
}
inline double TestResults::_internal_run_time() const {
  return _impl_.run_time_;
}
inline double TestResults::run_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_time)
  return _internal_run_time();
}
inline void TestResults::_internal_set_run_time(double value) {
  
  _impl_.run_time_ = value;
}
inline void TestResults::set_run_time(double value) {
  _internal_set_run_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.run_time)
}

// .tensorflow.MachineConfiguration machine_configuration = 7;
inline bool TestResults::_internal_has_machine_configuration() const {
  return this != internal_default_instance() && _impl_.machine_configuration_ != nullptr;
}
inline bool TestResults::has_machine_configuration() const {
  return _internal_has_machine_configuration();
}
inline void TestResults::clear_machine_configuration() {
  if (GetArenaForAllocation() == nullptr && _impl_.machine_configuration_ != nullptr) {
    delete _impl_.machine_configuration_;
  }
  _impl_.machine_configuration_ = nullptr;
}
inline const ::tensorflow::MachineConfiguration& TestResults::_internal_machine_configuration() const {
  const ::tensorflow::MachineConfiguration* p = _impl_.machine_configuration_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::MachineConfiguration&>(
      ::tensorflow::_MachineConfiguration_default_instance_);
}
inline const ::tensorflow::MachineConfiguration& TestResults::machine_configuration() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.machine_configuration)
  return _internal_machine_configuration();
}
inline void TestResults::unsafe_arena_set_allocated_machine_configuration(
    ::tensorflow::MachineConfiguration* machine_configuration) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.machine_configuration_);
  }
  _impl_.machine_configuration_ = machine_configuration;
  if (machine_configuration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.machine_configuration)
}
inline ::tensorflow::MachineConfiguration* TestResults::release_machine_configuration() {
  
  ::tensorflow::MachineConfiguration* temp = _impl_.machine_configuration_;
  _impl_.machine_configuration_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::MachineConfiguration* TestResults::unsafe_arena_release_machine_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.machine_configuration)
  
  ::tensorflow::MachineConfiguration* temp = _impl_.machine_configuration_;
  _impl_.machine_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::MachineConfiguration* TestResults::_internal_mutable_machine_configuration() {
  
  if (_impl_.machine_configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MachineConfiguration>(GetArenaForAllocation());
    _impl_.machine_configuration_ = p;
  }
  return _impl_.machine_configuration_;
}
inline ::tensorflow::MachineConfiguration* TestResults::mutable_machine_configuration() {
  ::tensorflow::MachineConfiguration* _msg = _internal_mutable_machine_configuration();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.machine_configuration)
  return _msg;
}
inline void TestResults::set_allocated_machine_configuration(::tensorflow::MachineConfiguration* machine_configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.machine_configuration_;
  }
  if (machine_configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(machine_configuration);
    if (message_arena != submessage_arena) {
      machine_configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, machine_configuration, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.machine_configuration_ = machine_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.machine_configuration)
}

// .tensorflow.RunConfiguration run_configuration = 8;
inline bool TestResults::_internal_has_run_configuration() const {
  return this != internal_default_instance() && _impl_.run_configuration_ != nullptr;
}
inline bool TestResults::has_run_configuration() const {
  return _internal_has_run_configuration();
}
inline void TestResults::clear_run_configuration() {
  if (GetArenaForAllocation() == nullptr && _impl_.run_configuration_ != nullptr) {
    delete _impl_.run_configuration_;
  }
  _impl_.run_configuration_ = nullptr;
}
inline const ::tensorflow::RunConfiguration& TestResults::_internal_run_configuration() const {
  const ::tensorflow::RunConfiguration* p = _impl_.run_configuration_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RunConfiguration&>(
      ::tensorflow::_RunConfiguration_default_instance_);
}
inline const ::tensorflow::RunConfiguration& TestResults::run_configuration() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_configuration)
  return _internal_run_configuration();
}
inline void TestResults::unsafe_arena_set_allocated_run_configuration(
    ::tensorflow::RunConfiguration* run_configuration) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.run_configuration_);
  }
  _impl_.run_configuration_ = run_configuration;
  if (run_configuration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TestResults.run_configuration)
}
inline ::tensorflow::RunConfiguration* TestResults::release_run_configuration() {
  
  ::tensorflow::RunConfiguration* temp = _impl_.run_configuration_;
  _impl_.run_configuration_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RunConfiguration* TestResults::unsafe_arena_release_run_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.run_configuration)
  
  ::tensorflow::RunConfiguration* temp = _impl_.run_configuration_;
  _impl_.run_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::RunConfiguration* TestResults::_internal_mutable_run_configuration() {
  
  if (_impl_.run_configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RunConfiguration>(GetArenaForAllocation());
    _impl_.run_configuration_ = p;
  }
  return _impl_.run_configuration_;
}
inline ::tensorflow::RunConfiguration* TestResults::mutable_run_configuration() {
  ::tensorflow::RunConfiguration* _msg = _internal_mutable_run_configuration();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.run_configuration)
  return _msg;
}
inline void TestResults::set_allocated_run_configuration(::tensorflow::RunConfiguration* run_configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.run_configuration_;
  }
  if (run_configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(run_configuration);
    if (message_arena != submessage_arena) {
      run_configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, run_configuration, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.run_configuration_ = run_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.run_configuration)
}

// string name = 9;
inline void TestResults::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& TestResults::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestResults::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.name)
}
inline std::string* TestResults::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.name)
  return _s;
}
inline const std::string& TestResults::_internal_name() const {
  return _impl_.name_.Get();
}
inline void TestResults::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* TestResults::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* TestResults::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.name)
  return _impl_.name_.Release();
}
inline void TestResults::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.name)
}

// .tensorflow.TestResults.BenchmarkType benchmark_type = 10;
inline void TestResults::clear_benchmark_type() {
  _impl_.benchmark_type_ = 0;
}
inline ::tensorflow::TestResults_BenchmarkType TestResults::_internal_benchmark_type() const {
  return static_cast< ::tensorflow::TestResults_BenchmarkType >(_impl_.benchmark_type_);
}
inline ::tensorflow::TestResults_BenchmarkType TestResults::benchmark_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.benchmark_type)
  return _internal_benchmark_type();
}
inline void TestResults::_internal_set_benchmark_type(::tensorflow::TestResults_BenchmarkType value) {
  
  _impl_.benchmark_type_ = value;
}
inline void TestResults::set_benchmark_type(::tensorflow::TestResults_BenchmarkType value) {
  _internal_set_benchmark_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.benchmark_type)
}

// string run_mode = 11;
inline void TestResults::clear_run_mode() {
  _impl_.run_mode_.ClearToEmpty();
}
inline const std::string& TestResults::run_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.run_mode)
  return _internal_run_mode();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestResults::set_run_mode(ArgT0&& arg0, ArgT... args) {
 
 _impl_.run_mode_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.run_mode)
}
inline std::string* TestResults::mutable_run_mode() {
  std::string* _s = _internal_mutable_run_mode();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.run_mode)
  return _s;
}
inline const std::string& TestResults::_internal_run_mode() const {
  return _impl_.run_mode_.Get();
}
inline void TestResults::_internal_set_run_mode(const std::string& value) {
  
  _impl_.run_mode_.Set(value, GetArenaForAllocation());
}
inline std::string* TestResults::_internal_mutable_run_mode() {
  
  return _impl_.run_mode_.Mutable(GetArenaForAllocation());
}
inline std::string* TestResults::release_run_mode() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.run_mode)
  return _impl_.run_mode_.Release();
}
inline void TestResults::set_allocated_run_mode(std::string* run_mode) {
  if (run_mode != nullptr) {
    
  } else {
    
  }
  _impl_.run_mode_.SetAllocated(run_mode, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.run_mode_.IsDefault()) {
    _impl_.run_mode_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.run_mode)
}

// string tf_version = 12;
inline void TestResults::clear_tf_version() {
  _impl_.tf_version_.ClearToEmpty();
}
inline const std::string& TestResults::tf_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.TestResults.tf_version)
  return _internal_tf_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TestResults::set_tf_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tf_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TestResults.tf_version)
}
inline std::string* TestResults::mutable_tf_version() {
  std::string* _s = _internal_mutable_tf_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.TestResults.tf_version)
  return _s;
}
inline const std::string& TestResults::_internal_tf_version() const {
  return _impl_.tf_version_.Get();
}
inline void TestResults::_internal_set_tf_version(const std::string& value) {
  
  _impl_.tf_version_.Set(value, GetArenaForAllocation());
}
inline std::string* TestResults::_internal_mutable_tf_version() {
  
  return _impl_.tf_version_.Mutable(GetArenaForAllocation());
}
inline std::string* TestResults::release_tf_version() {
  // @@protoc_insertion_point(field_release:tensorflow.TestResults.tf_version)
  return _impl_.tf_version_.Release();
}
inline void TestResults::set_allocated_tf_version(std::string* tf_version) {
  if (tf_version != nullptr) {
    
  } else {
    
  }
  _impl_.tf_version_.SetAllocated(tf_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tf_version_.IsDefault()) {
    _impl_.tf_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TestResults.tf_version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::TestResults_BenchmarkType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::TestResults_BenchmarkType>() {
  return ::tensorflow::TestResults_BenchmarkType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2ftsl_2fprotobuf_2ftest_5flog_2eproto
