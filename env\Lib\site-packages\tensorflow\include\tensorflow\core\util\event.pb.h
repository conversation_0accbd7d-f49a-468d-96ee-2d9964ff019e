// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/util/event.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fevent_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fevent_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/summary.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2futil_2fevent_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2futil_2fevent_2eproto;
namespace tensorflow {
class Event;
struct EventDefaultTypeInternal;
extern EventDefaultTypeInternal _Event_default_instance_;
class LogMessage;
struct LogMessageDefaultTypeInternal;
extern LogMessageDefaultTypeInternal _LogMessage_default_instance_;
class RequestedExitCode;
struct RequestedExitCodeDefaultTypeInternal;
extern RequestedExitCodeDefaultTypeInternal _RequestedExitCode_default_instance_;
class SessionLog;
struct SessionLogDefaultTypeInternal;
extern SessionLogDefaultTypeInternal _SessionLog_default_instance_;
class SourceMetadata;
struct SourceMetadataDefaultTypeInternal;
extern SourceMetadataDefaultTypeInternal _SourceMetadata_default_instance_;
class TaggedRunMetadata;
struct TaggedRunMetadataDefaultTypeInternal;
extern TaggedRunMetadataDefaultTypeInternal _TaggedRunMetadata_default_instance_;
class WatchdogConfig;
struct WatchdogConfigDefaultTypeInternal;
extern WatchdogConfigDefaultTypeInternal _WatchdogConfig_default_instance_;
class WorkerHeartbeatRequest;
struct WorkerHeartbeatRequestDefaultTypeInternal;
extern WorkerHeartbeatRequestDefaultTypeInternal _WorkerHeartbeatRequest_default_instance_;
class WorkerHeartbeatResponse;
struct WorkerHeartbeatResponseDefaultTypeInternal;
extern WorkerHeartbeatResponseDefaultTypeInternal _WorkerHeartbeatResponse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::Event* Arena::CreateMaybeMessage<::tensorflow::Event>(Arena*);
template<> ::tensorflow::LogMessage* Arena::CreateMaybeMessage<::tensorflow::LogMessage>(Arena*);
template<> ::tensorflow::RequestedExitCode* Arena::CreateMaybeMessage<::tensorflow::RequestedExitCode>(Arena*);
template<> ::tensorflow::SessionLog* Arena::CreateMaybeMessage<::tensorflow::SessionLog>(Arena*);
template<> ::tensorflow::SourceMetadata* Arena::CreateMaybeMessage<::tensorflow::SourceMetadata>(Arena*);
template<> ::tensorflow::TaggedRunMetadata* Arena::CreateMaybeMessage<::tensorflow::TaggedRunMetadata>(Arena*);
template<> ::tensorflow::WatchdogConfig* Arena::CreateMaybeMessage<::tensorflow::WatchdogConfig>(Arena*);
template<> ::tensorflow::WorkerHeartbeatRequest* Arena::CreateMaybeMessage<::tensorflow::WorkerHeartbeatRequest>(Arena*);
template<> ::tensorflow::WorkerHeartbeatResponse* Arena::CreateMaybeMessage<::tensorflow::WorkerHeartbeatResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum LogMessage_Level : int {
  LogMessage_Level_UNKNOWN = 0,
  LogMessage_Level_DEBUGGING = 10,
  LogMessage_Level_INFO = 20,
  LogMessage_Level_WARN = 30,
  LogMessage_Level_ERROR = 40,
  LogMessage_Level_FATAL = 50,
  LogMessage_Level_LogMessage_Level_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  LogMessage_Level_LogMessage_Level_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool LogMessage_Level_IsValid(int value);
constexpr LogMessage_Level LogMessage_Level_Level_MIN = LogMessage_Level_UNKNOWN;
constexpr LogMessage_Level LogMessage_Level_Level_MAX = LogMessage_Level_FATAL;
constexpr int LogMessage_Level_Level_ARRAYSIZE = LogMessage_Level_Level_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LogMessage_Level_descriptor();
template<typename T>
inline const std::string& LogMessage_Level_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LogMessage_Level>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LogMessage_Level_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LogMessage_Level_descriptor(), enum_t_value);
}
inline bool LogMessage_Level_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LogMessage_Level* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LogMessage_Level>(
    LogMessage_Level_descriptor(), name, value);
}
enum SessionLog_SessionStatus : int {
  SessionLog_SessionStatus_STATUS_UNSPECIFIED = 0,
  SessionLog_SessionStatus_START = 1,
  SessionLog_SessionStatus_STOP = 2,
  SessionLog_SessionStatus_CHECKPOINT = 3,
  SessionLog_SessionStatus_SessionLog_SessionStatus_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SessionLog_SessionStatus_SessionLog_SessionStatus_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SessionLog_SessionStatus_IsValid(int value);
constexpr SessionLog_SessionStatus SessionLog_SessionStatus_SessionStatus_MIN = SessionLog_SessionStatus_STATUS_UNSPECIFIED;
constexpr SessionLog_SessionStatus SessionLog_SessionStatus_SessionStatus_MAX = SessionLog_SessionStatus_CHECKPOINT;
constexpr int SessionLog_SessionStatus_SessionStatus_ARRAYSIZE = SessionLog_SessionStatus_SessionStatus_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SessionLog_SessionStatus_descriptor();
template<typename T>
inline const std::string& SessionLog_SessionStatus_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SessionLog_SessionStatus>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SessionLog_SessionStatus_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SessionLog_SessionStatus_descriptor(), enum_t_value);
}
inline bool SessionLog_SessionStatus_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SessionLog_SessionStatus* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SessionLog_SessionStatus>(
    SessionLog_SessionStatus_descriptor(), name, value);
}
enum WorkerHealth : int {
  OK = 0,
  RECEIVED_SHUTDOWN_SIGNAL = 1,
  INTERNAL_ERROR = 2,
  SHUTTING_DOWN = 3,
  WorkerHealth_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  WorkerHealth_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool WorkerHealth_IsValid(int value);
constexpr WorkerHealth WorkerHealth_MIN = OK;
constexpr WorkerHealth WorkerHealth_MAX = SHUTTING_DOWN;
constexpr int WorkerHealth_ARRAYSIZE = WorkerHealth_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* WorkerHealth_descriptor();
template<typename T>
inline const std::string& WorkerHealth_Name(T enum_t_value) {
  static_assert(::std::is_same<T, WorkerHealth>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function WorkerHealth_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    WorkerHealth_descriptor(), enum_t_value);
}
inline bool WorkerHealth_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, WorkerHealth* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<WorkerHealth>(
    WorkerHealth_descriptor(), name, value);
}
enum WorkerShutdownMode : int {
  DEFAULT = 0,
  NOT_CONFIGURED = 1,
  WAIT_FOR_COORDINATOR = 2,
  SHUTDOWN_AFTER_TIMEOUT = 3,
  WorkerShutdownMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  WorkerShutdownMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool WorkerShutdownMode_IsValid(int value);
constexpr WorkerShutdownMode WorkerShutdownMode_MIN = DEFAULT;
constexpr WorkerShutdownMode WorkerShutdownMode_MAX = SHUTDOWN_AFTER_TIMEOUT;
constexpr int WorkerShutdownMode_ARRAYSIZE = WorkerShutdownMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* WorkerShutdownMode_descriptor();
template<typename T>
inline const std::string& WorkerShutdownMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, WorkerShutdownMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function WorkerShutdownMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    WorkerShutdownMode_descriptor(), enum_t_value);
}
inline bool WorkerShutdownMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, WorkerShutdownMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<WorkerShutdownMode>(
    WorkerShutdownMode_descriptor(), name, value);
}
// ===================================================================

class Event final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.Event) */ {
 public:
  inline Event() : Event(nullptr) {}
  ~Event() override;
  explicit PROTOBUF_CONSTEXPR Event(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Event(const Event& from);
  Event(Event&& from) noexcept
    : Event() {
    *this = ::std::move(from);
  }

  inline Event& operator=(const Event& from) {
    CopyFrom(from);
    return *this;
  }
  inline Event& operator=(Event&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Event& default_instance() {
    return *internal_default_instance();
  }
  enum WhatCase {
    kFileVersion = 3,
    kGraphDef = 4,
    kSummary = 5,
    kLogMessage = 6,
    kSessionLog = 7,
    kTaggedRunMetadata = 8,
    kMetaGraphDef = 9,
    WHAT_NOT_SET = 0,
  };

  static inline const Event* internal_default_instance() {
    return reinterpret_cast<const Event*>(
               &_Event_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Event& a, Event& b) {
    a.Swap(&b);
  }
  inline void Swap(Event* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Event* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Event* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Event>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Event& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Event& from) {
    Event::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Event* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.Event";
  }
  protected:
  explicit Event(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceMetadataFieldNumber = 10,
    kWallTimeFieldNumber = 1,
    kStepFieldNumber = 2,
    kFileVersionFieldNumber = 3,
    kGraphDefFieldNumber = 4,
    kSummaryFieldNumber = 5,
    kLogMessageFieldNumber = 6,
    kSessionLogFieldNumber = 7,
    kTaggedRunMetadataFieldNumber = 8,
    kMetaGraphDefFieldNumber = 9,
  };
  // .tensorflow.SourceMetadata source_metadata = 10;
  bool has_source_metadata() const;
  private:
  bool _internal_has_source_metadata() const;
  public:
  void clear_source_metadata();
  const ::tensorflow::SourceMetadata& source_metadata() const;
  PROTOBUF_NODISCARD ::tensorflow::SourceMetadata* release_source_metadata();
  ::tensorflow::SourceMetadata* mutable_source_metadata();
  void set_allocated_source_metadata(::tensorflow::SourceMetadata* source_metadata);
  private:
  const ::tensorflow::SourceMetadata& _internal_source_metadata() const;
  ::tensorflow::SourceMetadata* _internal_mutable_source_metadata();
  public:
  void unsafe_arena_set_allocated_source_metadata(
      ::tensorflow::SourceMetadata* source_metadata);
  ::tensorflow::SourceMetadata* unsafe_arena_release_source_metadata();

  // double wall_time = 1;
  void clear_wall_time();
  double wall_time() const;
  void set_wall_time(double value);
  private:
  double _internal_wall_time() const;
  void _internal_set_wall_time(double value);
  public:

  // int64 step = 2;
  void clear_step();
  int64_t step() const;
  void set_step(int64_t value);
  private:
  int64_t _internal_step() const;
  void _internal_set_step(int64_t value);
  public:

  // string file_version = 3;
  bool has_file_version() const;
  private:
  bool _internal_has_file_version() const;
  public:
  void clear_file_version();
  const std::string& file_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file_version();
  PROTOBUF_NODISCARD std::string* release_file_version();
  void set_allocated_file_version(std::string* file_version);
  private:
  const std::string& _internal_file_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file_version(const std::string& value);
  std::string* _internal_mutable_file_version();
  public:

  // bytes graph_def = 4;
  bool has_graph_def() const;
  private:
  bool _internal_has_graph_def() const;
  public:
  void clear_graph_def();
  const std::string& graph_def() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_def(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_def();
  PROTOBUF_NODISCARD std::string* release_graph_def();
  void set_allocated_graph_def(std::string* graph_def);
  private:
  const std::string& _internal_graph_def() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_def(const std::string& value);
  std::string* _internal_mutable_graph_def();
  public:

  // .tensorflow.Summary summary = 5;
  bool has_summary() const;
  private:
  bool _internal_has_summary() const;
  public:
  void clear_summary();
  const ::tensorflow::Summary& summary() const;
  PROTOBUF_NODISCARD ::tensorflow::Summary* release_summary();
  ::tensorflow::Summary* mutable_summary();
  void set_allocated_summary(::tensorflow::Summary* summary);
  private:
  const ::tensorflow::Summary& _internal_summary() const;
  ::tensorflow::Summary* _internal_mutable_summary();
  public:
  void unsafe_arena_set_allocated_summary(
      ::tensorflow::Summary* summary);
  ::tensorflow::Summary* unsafe_arena_release_summary();

  // .tensorflow.LogMessage log_message = 6 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_log_message() const;
  private:
  bool _internal_has_log_message() const;
  public:
  PROTOBUF_DEPRECATED void clear_log_message();
  PROTOBUF_DEPRECATED const ::tensorflow::LogMessage& log_message() const;
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED ::tensorflow::LogMessage* release_log_message();
  PROTOBUF_DEPRECATED ::tensorflow::LogMessage* mutable_log_message();
  PROTOBUF_DEPRECATED void set_allocated_log_message(::tensorflow::LogMessage* log_message);
  private:
  const ::tensorflow::LogMessage& _internal_log_message() const;
  ::tensorflow::LogMessage* _internal_mutable_log_message();
  public:
  PROTOBUF_DEPRECATED void unsafe_arena_set_allocated_log_message(
      ::tensorflow::LogMessage* log_message);
  PROTOBUF_DEPRECATED ::tensorflow::LogMessage* unsafe_arena_release_log_message();

  // .tensorflow.SessionLog session_log = 7;
  bool has_session_log() const;
  private:
  bool _internal_has_session_log() const;
  public:
  void clear_session_log();
  const ::tensorflow::SessionLog& session_log() const;
  PROTOBUF_NODISCARD ::tensorflow::SessionLog* release_session_log();
  ::tensorflow::SessionLog* mutable_session_log();
  void set_allocated_session_log(::tensorflow::SessionLog* session_log);
  private:
  const ::tensorflow::SessionLog& _internal_session_log() const;
  ::tensorflow::SessionLog* _internal_mutable_session_log();
  public:
  void unsafe_arena_set_allocated_session_log(
      ::tensorflow::SessionLog* session_log);
  ::tensorflow::SessionLog* unsafe_arena_release_session_log();

  // .tensorflow.TaggedRunMetadata tagged_run_metadata = 8;
  bool has_tagged_run_metadata() const;
  private:
  bool _internal_has_tagged_run_metadata() const;
  public:
  void clear_tagged_run_metadata();
  const ::tensorflow::TaggedRunMetadata& tagged_run_metadata() const;
  PROTOBUF_NODISCARD ::tensorflow::TaggedRunMetadata* release_tagged_run_metadata();
  ::tensorflow::TaggedRunMetadata* mutable_tagged_run_metadata();
  void set_allocated_tagged_run_metadata(::tensorflow::TaggedRunMetadata* tagged_run_metadata);
  private:
  const ::tensorflow::TaggedRunMetadata& _internal_tagged_run_metadata() const;
  ::tensorflow::TaggedRunMetadata* _internal_mutable_tagged_run_metadata();
  public:
  void unsafe_arena_set_allocated_tagged_run_metadata(
      ::tensorflow::TaggedRunMetadata* tagged_run_metadata);
  ::tensorflow::TaggedRunMetadata* unsafe_arena_release_tagged_run_metadata();

  // bytes meta_graph_def = 9;
  bool has_meta_graph_def() const;
  private:
  bool _internal_has_meta_graph_def() const;
  public:
  void clear_meta_graph_def();
  const std::string& meta_graph_def() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_meta_graph_def(ArgT0&& arg0, ArgT... args);
  std::string* mutable_meta_graph_def();
  PROTOBUF_NODISCARD std::string* release_meta_graph_def();
  void set_allocated_meta_graph_def(std::string* meta_graph_def);
  private:
  const std::string& _internal_meta_graph_def() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_meta_graph_def(const std::string& value);
  std::string* _internal_mutable_meta_graph_def();
  public:

  void clear_what();
  WhatCase what_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.Event)
 private:
  class _Internal;
  void set_has_file_version();
  void set_has_graph_def();
  void set_has_summary();
  void set_has_log_message();
  void set_has_session_log();
  void set_has_tagged_run_metadata();
  void set_has_meta_graph_def();

  inline bool has_what() const;
  inline void clear_has_what();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::SourceMetadata* source_metadata_;
    double wall_time_;
    int64_t step_;
    union WhatUnion {
      constexpr WhatUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_version_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_def_;
      ::tensorflow::Summary* summary_;
      ::tensorflow::LogMessage* log_message_;
      ::tensorflow::SessionLog* session_log_;
      ::tensorflow::TaggedRunMetadata* tagged_run_metadata_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr meta_graph_def_;
    } what_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// -------------------------------------------------------------------

class SourceMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SourceMetadata) */ {
 public:
  inline SourceMetadata() : SourceMetadata(nullptr) {}
  ~SourceMetadata() override;
  explicit PROTOBUF_CONSTEXPR SourceMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SourceMetadata(const SourceMetadata& from);
  SourceMetadata(SourceMetadata&& from) noexcept
    : SourceMetadata() {
    *this = ::std::move(from);
  }

  inline SourceMetadata& operator=(const SourceMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline SourceMetadata& operator=(SourceMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SourceMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const SourceMetadata* internal_default_instance() {
    return reinterpret_cast<const SourceMetadata*>(
               &_SourceMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SourceMetadata& a, SourceMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(SourceMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SourceMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SourceMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SourceMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SourceMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SourceMetadata& from) {
    SourceMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SourceMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SourceMetadata";
  }
  protected:
  explicit SourceMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWriterFieldNumber = 1,
  };
  // string writer = 1;
  void clear_writer();
  const std::string& writer() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_writer(ArgT0&& arg0, ArgT... args);
  std::string* mutable_writer();
  PROTOBUF_NODISCARD std::string* release_writer();
  void set_allocated_writer(std::string* writer);
  private:
  const std::string& _internal_writer() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_writer(const std::string& value);
  std::string* _internal_mutable_writer();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SourceMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr writer_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// -------------------------------------------------------------------

class LogMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LogMessage) */ {
 public:
  inline LogMessage() : LogMessage(nullptr) {}
  ~LogMessage() override;
  explicit PROTOBUF_CONSTEXPR LogMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LogMessage(const LogMessage& from);
  LogMessage(LogMessage&& from) noexcept
    : LogMessage() {
    *this = ::std::move(from);
  }

  inline LogMessage& operator=(const LogMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline LogMessage& operator=(LogMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LogMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const LogMessage* internal_default_instance() {
    return reinterpret_cast<const LogMessage*>(
               &_LogMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LogMessage& a, LogMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(LogMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LogMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LogMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LogMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LogMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LogMessage& from) {
    LogMessage::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LogMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LogMessage";
  }
  protected:
  explicit LogMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef LogMessage_Level Level;
  static constexpr Level UNKNOWN =
    LogMessage_Level_UNKNOWN;
  static constexpr Level DEBUGGING =
    LogMessage_Level_DEBUGGING;
  static constexpr Level INFO =
    LogMessage_Level_INFO;
  static constexpr Level WARN =
    LogMessage_Level_WARN;
  static constexpr Level ERROR =
    LogMessage_Level_ERROR;
  static constexpr Level FATAL =
    LogMessage_Level_FATAL;
  static inline bool Level_IsValid(int value) {
    return LogMessage_Level_IsValid(value);
  }
  static constexpr Level Level_MIN =
    LogMessage_Level_Level_MIN;
  static constexpr Level Level_MAX =
    LogMessage_Level_Level_MAX;
  static constexpr int Level_ARRAYSIZE =
    LogMessage_Level_Level_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Level_descriptor() {
    return LogMessage_Level_descriptor();
  }
  template<typename T>
  static inline const std::string& Level_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Level>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Level_Name.");
    return LogMessage_Level_Name(enum_t_value);
  }
  static inline bool Level_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Level* value) {
    return LogMessage_Level_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kLevelFieldNumber = 1,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // .tensorflow.LogMessage.Level level = 1;
  void clear_level();
  ::tensorflow::LogMessage_Level level() const;
  void set_level(::tensorflow::LogMessage_Level value);
  private:
  ::tensorflow::LogMessage_Level _internal_level() const;
  void _internal_set_level(::tensorflow::LogMessage_Level value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.LogMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
    int level_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// -------------------------------------------------------------------

class SessionLog final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SessionLog) */ {
 public:
  inline SessionLog() : SessionLog(nullptr) {}
  ~SessionLog() override;
  explicit PROTOBUF_CONSTEXPR SessionLog(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SessionLog(const SessionLog& from);
  SessionLog(SessionLog&& from) noexcept
    : SessionLog() {
    *this = ::std::move(from);
  }

  inline SessionLog& operator=(const SessionLog& from) {
    CopyFrom(from);
    return *this;
  }
  inline SessionLog& operator=(SessionLog&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SessionLog& default_instance() {
    return *internal_default_instance();
  }
  static inline const SessionLog* internal_default_instance() {
    return reinterpret_cast<const SessionLog*>(
               &_SessionLog_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SessionLog& a, SessionLog& b) {
    a.Swap(&b);
  }
  inline void Swap(SessionLog* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SessionLog* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SessionLog* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SessionLog>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SessionLog& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SessionLog& from) {
    SessionLog::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SessionLog* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SessionLog";
  }
  protected:
  explicit SessionLog(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SessionLog_SessionStatus SessionStatus;
  static constexpr SessionStatus STATUS_UNSPECIFIED =
    SessionLog_SessionStatus_STATUS_UNSPECIFIED;
  static constexpr SessionStatus START =
    SessionLog_SessionStatus_START;
  static constexpr SessionStatus STOP =
    SessionLog_SessionStatus_STOP;
  static constexpr SessionStatus CHECKPOINT =
    SessionLog_SessionStatus_CHECKPOINT;
  static inline bool SessionStatus_IsValid(int value) {
    return SessionLog_SessionStatus_IsValid(value);
  }
  static constexpr SessionStatus SessionStatus_MIN =
    SessionLog_SessionStatus_SessionStatus_MIN;
  static constexpr SessionStatus SessionStatus_MAX =
    SessionLog_SessionStatus_SessionStatus_MAX;
  static constexpr int SessionStatus_ARRAYSIZE =
    SessionLog_SessionStatus_SessionStatus_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  SessionStatus_descriptor() {
    return SessionLog_SessionStatus_descriptor();
  }
  template<typename T>
  static inline const std::string& SessionStatus_Name(T enum_t_value) {
    static_assert(::std::is_same<T, SessionStatus>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function SessionStatus_Name.");
    return SessionLog_SessionStatus_Name(enum_t_value);
  }
  static inline bool SessionStatus_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      SessionStatus* value) {
    return SessionLog_SessionStatus_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kCheckpointPathFieldNumber = 2,
    kMsgFieldNumber = 3,
    kStatusFieldNumber = 1,
  };
  // string checkpoint_path = 2;
  void clear_checkpoint_path();
  const std::string& checkpoint_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_checkpoint_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_checkpoint_path();
  PROTOBUF_NODISCARD std::string* release_checkpoint_path();
  void set_allocated_checkpoint_path(std::string* checkpoint_path);
  private:
  const std::string& _internal_checkpoint_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_checkpoint_path(const std::string& value);
  std::string* _internal_mutable_checkpoint_path();
  public:

  // string msg = 3;
  void clear_msg();
  const std::string& msg() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_msg(ArgT0&& arg0, ArgT... args);
  std::string* mutable_msg();
  PROTOBUF_NODISCARD std::string* release_msg();
  void set_allocated_msg(std::string* msg);
  private:
  const std::string& _internal_msg() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_msg(const std::string& value);
  std::string* _internal_mutable_msg();
  public:

  // .tensorflow.SessionLog.SessionStatus status = 1;
  void clear_status();
  ::tensorflow::SessionLog_SessionStatus status() const;
  void set_status(::tensorflow::SessionLog_SessionStatus value);
  private:
  ::tensorflow::SessionLog_SessionStatus _internal_status() const;
  void _internal_set_status(::tensorflow::SessionLog_SessionStatus value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SessionLog)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr checkpoint_path_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr msg_;
    int status_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// -------------------------------------------------------------------

class TaggedRunMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TaggedRunMetadata) */ {
 public:
  inline TaggedRunMetadata() : TaggedRunMetadata(nullptr) {}
  ~TaggedRunMetadata() override;
  explicit PROTOBUF_CONSTEXPR TaggedRunMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TaggedRunMetadata(const TaggedRunMetadata& from);
  TaggedRunMetadata(TaggedRunMetadata&& from) noexcept
    : TaggedRunMetadata() {
    *this = ::std::move(from);
  }

  inline TaggedRunMetadata& operator=(const TaggedRunMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline TaggedRunMetadata& operator=(TaggedRunMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TaggedRunMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const TaggedRunMetadata* internal_default_instance() {
    return reinterpret_cast<const TaggedRunMetadata*>(
               &_TaggedRunMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TaggedRunMetadata& a, TaggedRunMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(TaggedRunMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TaggedRunMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TaggedRunMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TaggedRunMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TaggedRunMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TaggedRunMetadata& from) {
    TaggedRunMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TaggedRunMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TaggedRunMetadata";
  }
  protected:
  explicit TaggedRunMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTagFieldNumber = 1,
    kRunMetadataFieldNumber = 2,
  };
  // string tag = 1;
  void clear_tag();
  const std::string& tag() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tag(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tag();
  PROTOBUF_NODISCARD std::string* release_tag();
  void set_allocated_tag(std::string* tag);
  private:
  const std::string& _internal_tag() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tag(const std::string& value);
  std::string* _internal_mutable_tag();
  public:

  // bytes run_metadata = 2;
  void clear_run_metadata();
  const std::string& run_metadata() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_run_metadata(ArgT0&& arg0, ArgT... args);
  std::string* mutable_run_metadata();
  PROTOBUF_NODISCARD std::string* release_run_metadata();
  void set_allocated_run_metadata(std::string* run_metadata);
  private:
  const std::string& _internal_run_metadata() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_run_metadata(const std::string& value);
  std::string* _internal_mutable_run_metadata();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TaggedRunMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tag_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr run_metadata_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// -------------------------------------------------------------------

class WatchdogConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.WatchdogConfig) */ {
 public:
  inline WatchdogConfig() : WatchdogConfig(nullptr) {}
  ~WatchdogConfig() override;
  explicit PROTOBUF_CONSTEXPR WatchdogConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WatchdogConfig(const WatchdogConfig& from);
  WatchdogConfig(WatchdogConfig&& from) noexcept
    : WatchdogConfig() {
    *this = ::std::move(from);
  }

  inline WatchdogConfig& operator=(const WatchdogConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline WatchdogConfig& operator=(WatchdogConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WatchdogConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const WatchdogConfig* internal_default_instance() {
    return reinterpret_cast<const WatchdogConfig*>(
               &_WatchdogConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(WatchdogConfig& a, WatchdogConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(WatchdogConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WatchdogConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WatchdogConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WatchdogConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WatchdogConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WatchdogConfig& from) {
    WatchdogConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WatchdogConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.WatchdogConfig";
  }
  protected:
  explicit WatchdogConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimeoutMsFieldNumber = 1,
  };
  // int64 timeout_ms = 1;
  void clear_timeout_ms();
  int64_t timeout_ms() const;
  void set_timeout_ms(int64_t value);
  private:
  int64_t _internal_timeout_ms() const;
  void _internal_set_timeout_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.WatchdogConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t timeout_ms_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// -------------------------------------------------------------------

class RequestedExitCode final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RequestedExitCode) */ {
 public:
  inline RequestedExitCode() : RequestedExitCode(nullptr) {}
  ~RequestedExitCode() override;
  explicit PROTOBUF_CONSTEXPR RequestedExitCode(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RequestedExitCode(const RequestedExitCode& from);
  RequestedExitCode(RequestedExitCode&& from) noexcept
    : RequestedExitCode() {
    *this = ::std::move(from);
  }

  inline RequestedExitCode& operator=(const RequestedExitCode& from) {
    CopyFrom(from);
    return *this;
  }
  inline RequestedExitCode& operator=(RequestedExitCode&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RequestedExitCode& default_instance() {
    return *internal_default_instance();
  }
  static inline const RequestedExitCode* internal_default_instance() {
    return reinterpret_cast<const RequestedExitCode*>(
               &_RequestedExitCode_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RequestedExitCode& a, RequestedExitCode& b) {
    a.Swap(&b);
  }
  inline void Swap(RequestedExitCode* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RequestedExitCode* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RequestedExitCode* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RequestedExitCode>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RequestedExitCode& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RequestedExitCode& from) {
    RequestedExitCode::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RequestedExitCode* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RequestedExitCode";
  }
  protected:
  explicit RequestedExitCode(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExitCodeFieldNumber = 1,
  };
  // int32 exit_code = 1;
  void clear_exit_code();
  int32_t exit_code() const;
  void set_exit_code(int32_t value);
  private:
  int32_t _internal_exit_code() const;
  void _internal_set_exit_code(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RequestedExitCode)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t exit_code_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// -------------------------------------------------------------------

class WorkerHeartbeatRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.WorkerHeartbeatRequest) */ {
 public:
  inline WorkerHeartbeatRequest() : WorkerHeartbeatRequest(nullptr) {}
  ~WorkerHeartbeatRequest() override;
  explicit PROTOBUF_CONSTEXPR WorkerHeartbeatRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WorkerHeartbeatRequest(const WorkerHeartbeatRequest& from);
  WorkerHeartbeatRequest(WorkerHeartbeatRequest&& from) noexcept
    : WorkerHeartbeatRequest() {
    *this = ::std::move(from);
  }

  inline WorkerHeartbeatRequest& operator=(const WorkerHeartbeatRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline WorkerHeartbeatRequest& operator=(WorkerHeartbeatRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WorkerHeartbeatRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const WorkerHeartbeatRequest* internal_default_instance() {
    return reinterpret_cast<const WorkerHeartbeatRequest*>(
               &_WorkerHeartbeatRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(WorkerHeartbeatRequest& a, WorkerHeartbeatRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(WorkerHeartbeatRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WorkerHeartbeatRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WorkerHeartbeatRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WorkerHeartbeatRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WorkerHeartbeatRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WorkerHeartbeatRequest& from) {
    WorkerHeartbeatRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WorkerHeartbeatRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.WorkerHeartbeatRequest";
  }
  protected:
  explicit WorkerHeartbeatRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWatchdogConfigFieldNumber = 2,
    kExitCodeFieldNumber = 3,
    kShutdownModeFieldNumber = 1,
  };
  // .tensorflow.WatchdogConfig watchdog_config = 2;
  bool has_watchdog_config() const;
  private:
  bool _internal_has_watchdog_config() const;
  public:
  void clear_watchdog_config();
  const ::tensorflow::WatchdogConfig& watchdog_config() const;
  PROTOBUF_NODISCARD ::tensorflow::WatchdogConfig* release_watchdog_config();
  ::tensorflow::WatchdogConfig* mutable_watchdog_config();
  void set_allocated_watchdog_config(::tensorflow::WatchdogConfig* watchdog_config);
  private:
  const ::tensorflow::WatchdogConfig& _internal_watchdog_config() const;
  ::tensorflow::WatchdogConfig* _internal_mutable_watchdog_config();
  public:
  void unsafe_arena_set_allocated_watchdog_config(
      ::tensorflow::WatchdogConfig* watchdog_config);
  ::tensorflow::WatchdogConfig* unsafe_arena_release_watchdog_config();

  // .tensorflow.RequestedExitCode exit_code = 3;
  bool has_exit_code() const;
  private:
  bool _internal_has_exit_code() const;
  public:
  void clear_exit_code();
  const ::tensorflow::RequestedExitCode& exit_code() const;
  PROTOBUF_NODISCARD ::tensorflow::RequestedExitCode* release_exit_code();
  ::tensorflow::RequestedExitCode* mutable_exit_code();
  void set_allocated_exit_code(::tensorflow::RequestedExitCode* exit_code);
  private:
  const ::tensorflow::RequestedExitCode& _internal_exit_code() const;
  ::tensorflow::RequestedExitCode* _internal_mutable_exit_code();
  public:
  void unsafe_arena_set_allocated_exit_code(
      ::tensorflow::RequestedExitCode* exit_code);
  ::tensorflow::RequestedExitCode* unsafe_arena_release_exit_code();

  // .tensorflow.WorkerShutdownMode shutdown_mode = 1;
  void clear_shutdown_mode();
  ::tensorflow::WorkerShutdownMode shutdown_mode() const;
  void set_shutdown_mode(::tensorflow::WorkerShutdownMode value);
  private:
  ::tensorflow::WorkerShutdownMode _internal_shutdown_mode() const;
  void _internal_set_shutdown_mode(::tensorflow::WorkerShutdownMode value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.WorkerHeartbeatRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::WatchdogConfig* watchdog_config_;
    ::tensorflow::RequestedExitCode* exit_code_;
    int shutdown_mode_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// -------------------------------------------------------------------

class WorkerHeartbeatResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.WorkerHeartbeatResponse) */ {
 public:
  inline WorkerHeartbeatResponse() : WorkerHeartbeatResponse(nullptr) {}
  ~WorkerHeartbeatResponse() override;
  explicit PROTOBUF_CONSTEXPR WorkerHeartbeatResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WorkerHeartbeatResponse(const WorkerHeartbeatResponse& from);
  WorkerHeartbeatResponse(WorkerHeartbeatResponse&& from) noexcept
    : WorkerHeartbeatResponse() {
    *this = ::std::move(from);
  }

  inline WorkerHeartbeatResponse& operator=(const WorkerHeartbeatResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline WorkerHeartbeatResponse& operator=(WorkerHeartbeatResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WorkerHeartbeatResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const WorkerHeartbeatResponse* internal_default_instance() {
    return reinterpret_cast<const WorkerHeartbeatResponse*>(
               &_WorkerHeartbeatResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(WorkerHeartbeatResponse& a, WorkerHeartbeatResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(WorkerHeartbeatResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WorkerHeartbeatResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WorkerHeartbeatResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WorkerHeartbeatResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WorkerHeartbeatResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WorkerHeartbeatResponse& from) {
    WorkerHeartbeatResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WorkerHeartbeatResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.WorkerHeartbeatResponse";
  }
  protected:
  explicit WorkerHeartbeatResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWorkerLogFieldNumber = 2,
    kHostnameFieldNumber = 3,
    kHealthStatusFieldNumber = 1,
  };
  // repeated .tensorflow.Event worker_log = 2;
  int worker_log_size() const;
  private:
  int _internal_worker_log_size() const;
  public:
  void clear_worker_log();
  ::tensorflow::Event* mutable_worker_log(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Event >*
      mutable_worker_log();
  private:
  const ::tensorflow::Event& _internal_worker_log(int index) const;
  ::tensorflow::Event* _internal_add_worker_log();
  public:
  const ::tensorflow::Event& worker_log(int index) const;
  ::tensorflow::Event* add_worker_log();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Event >&
      worker_log() const;

  // string hostname = 3;
  void clear_hostname();
  const std::string& hostname() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_hostname(ArgT0&& arg0, ArgT... args);
  std::string* mutable_hostname();
  PROTOBUF_NODISCARD std::string* release_hostname();
  void set_allocated_hostname(std::string* hostname);
  private:
  const std::string& _internal_hostname() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_hostname(const std::string& value);
  std::string* _internal_mutable_hostname();
  public:

  // .tensorflow.WorkerHealth health_status = 1;
  void clear_health_status();
  ::tensorflow::WorkerHealth health_status() const;
  void set_health_status(::tensorflow::WorkerHealth value);
  private:
  ::tensorflow::WorkerHealth _internal_health_status() const;
  void _internal_set_health_status(::tensorflow::WorkerHealth value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.WorkerHeartbeatResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Event > worker_log_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hostname_;
    int health_status_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2futil_2fevent_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Event

// double wall_time = 1;
inline void Event::clear_wall_time() {
  _impl_.wall_time_ = 0;
}
inline double Event::_internal_wall_time() const {
  return _impl_.wall_time_;
}
inline double Event::wall_time() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.wall_time)
  return _internal_wall_time();
}
inline void Event::_internal_set_wall_time(double value) {
  
  _impl_.wall_time_ = value;
}
inline void Event::set_wall_time(double value) {
  _internal_set_wall_time(value);
  // @@protoc_insertion_point(field_set:tensorflow.Event.wall_time)
}

// int64 step = 2;
inline void Event::clear_step() {
  _impl_.step_ = int64_t{0};
}
inline int64_t Event::_internal_step() const {
  return _impl_.step_;
}
inline int64_t Event::step() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.step)
  return _internal_step();
}
inline void Event::_internal_set_step(int64_t value) {
  
  _impl_.step_ = value;
}
inline void Event::set_step(int64_t value) {
  _internal_set_step(value);
  // @@protoc_insertion_point(field_set:tensorflow.Event.step)
}

// string file_version = 3;
inline bool Event::_internal_has_file_version() const {
  return what_case() == kFileVersion;
}
inline bool Event::has_file_version() const {
  return _internal_has_file_version();
}
inline void Event::set_has_file_version() {
  _impl_._oneof_case_[0] = kFileVersion;
}
inline void Event::clear_file_version() {
  if (_internal_has_file_version()) {
    _impl_.what_.file_version_.Destroy();
    clear_has_what();
  }
}
inline const std::string& Event::file_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.file_version)
  return _internal_file_version();
}
template <typename ArgT0, typename... ArgT>
inline void Event::set_file_version(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_file_version()) {
    clear_what();
    set_has_file_version();
    _impl_.what_.file_version_.InitDefault();
  }
  _impl_.what_.file_version_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Event.file_version)
}
inline std::string* Event::mutable_file_version() {
  std::string* _s = _internal_mutable_file_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.file_version)
  return _s;
}
inline const std::string& Event::_internal_file_version() const {
  if (_internal_has_file_version()) {
    return _impl_.what_.file_version_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void Event::_internal_set_file_version(const std::string& value) {
  if (!_internal_has_file_version()) {
    clear_what();
    set_has_file_version();
    _impl_.what_.file_version_.InitDefault();
  }
  _impl_.what_.file_version_.Set(value, GetArenaForAllocation());
}
inline std::string* Event::_internal_mutable_file_version() {
  if (!_internal_has_file_version()) {
    clear_what();
    set_has_file_version();
    _impl_.what_.file_version_.InitDefault();
  }
  return _impl_.what_.file_version_.Mutable(      GetArenaForAllocation());
}
inline std::string* Event::release_file_version() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.file_version)
  if (_internal_has_file_version()) {
    clear_has_what();
    return _impl_.what_.file_version_.Release();
  } else {
    return nullptr;
  }
}
inline void Event::set_allocated_file_version(std::string* file_version) {
  if (has_what()) {
    clear_what();
  }
  if (file_version != nullptr) {
    set_has_file_version();
    _impl_.what_.file_version_.InitAllocated(file_version, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.file_version)
}

// bytes graph_def = 4;
inline bool Event::_internal_has_graph_def() const {
  return what_case() == kGraphDef;
}
inline bool Event::has_graph_def() const {
  return _internal_has_graph_def();
}
inline void Event::set_has_graph_def() {
  _impl_._oneof_case_[0] = kGraphDef;
}
inline void Event::clear_graph_def() {
  if (_internal_has_graph_def()) {
    _impl_.what_.graph_def_.Destroy();
    clear_has_what();
  }
}
inline const std::string& Event::graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.graph_def)
  return _internal_graph_def();
}
template <typename ArgT0, typename... ArgT>
inline void Event::set_graph_def(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_graph_def()) {
    clear_what();
    set_has_graph_def();
    _impl_.what_.graph_def_.InitDefault();
  }
  _impl_.what_.graph_def_.SetBytes( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Event.graph_def)
}
inline std::string* Event::mutable_graph_def() {
  std::string* _s = _internal_mutable_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.graph_def)
  return _s;
}
inline const std::string& Event::_internal_graph_def() const {
  if (_internal_has_graph_def()) {
    return _impl_.what_.graph_def_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void Event::_internal_set_graph_def(const std::string& value) {
  if (!_internal_has_graph_def()) {
    clear_what();
    set_has_graph_def();
    _impl_.what_.graph_def_.InitDefault();
  }
  _impl_.what_.graph_def_.Set(value, GetArenaForAllocation());
}
inline std::string* Event::_internal_mutable_graph_def() {
  if (!_internal_has_graph_def()) {
    clear_what();
    set_has_graph_def();
    _impl_.what_.graph_def_.InitDefault();
  }
  return _impl_.what_.graph_def_.Mutable(      GetArenaForAllocation());
}
inline std::string* Event::release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.graph_def)
  if (_internal_has_graph_def()) {
    clear_has_what();
    return _impl_.what_.graph_def_.Release();
  } else {
    return nullptr;
  }
}
inline void Event::set_allocated_graph_def(std::string* graph_def) {
  if (has_what()) {
    clear_what();
  }
  if (graph_def != nullptr) {
    set_has_graph_def();
    _impl_.what_.graph_def_.InitAllocated(graph_def, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.graph_def)
}

// .tensorflow.Summary summary = 5;
inline bool Event::_internal_has_summary() const {
  return what_case() == kSummary;
}
inline bool Event::has_summary() const {
  return _internal_has_summary();
}
inline void Event::set_has_summary() {
  _impl_._oneof_case_[0] = kSummary;
}
inline ::tensorflow::Summary* Event::release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.summary)
  if (_internal_has_summary()) {
    clear_has_what();
    ::tensorflow::Summary* temp = _impl_.what_.summary_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.summary_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::Summary& Event::_internal_summary() const {
  return _internal_has_summary()
      ? *_impl_.what_.summary_
      : reinterpret_cast< ::tensorflow::Summary&>(::tensorflow::_Summary_default_instance_);
}
inline const ::tensorflow::Summary& Event::summary() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.summary)
  return _internal_summary();
}
inline ::tensorflow::Summary* Event::unsafe_arena_release_summary() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.summary)
  if (_internal_has_summary()) {
    clear_has_what();
    ::tensorflow::Summary* temp = _impl_.what_.summary_;
    _impl_.what_.summary_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_summary(::tensorflow::Summary* summary) {
  clear_what();
  if (summary) {
    set_has_summary();
    _impl_.what_.summary_ = summary;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.summary)
}
inline ::tensorflow::Summary* Event::_internal_mutable_summary() {
  if (!_internal_has_summary()) {
    clear_what();
    set_has_summary();
    _impl_.what_.summary_ = CreateMaybeMessage< ::tensorflow::Summary >(GetArenaForAllocation());
  }
  return _impl_.what_.summary_;
}
inline ::tensorflow::Summary* Event::mutable_summary() {
  ::tensorflow::Summary* _msg = _internal_mutable_summary();
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.summary)
  return _msg;
}

// .tensorflow.LogMessage log_message = 6 [deprecated = true];
inline bool Event::_internal_has_log_message() const {
  return what_case() == kLogMessage;
}
inline bool Event::has_log_message() const {
  return _internal_has_log_message();
}
inline void Event::set_has_log_message() {
  _impl_._oneof_case_[0] = kLogMessage;
}
inline void Event::clear_log_message() {
  if (_internal_has_log_message()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.log_message_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::LogMessage* Event::release_log_message() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.log_message)
  if (_internal_has_log_message()) {
    clear_has_what();
    ::tensorflow::LogMessage* temp = _impl_.what_.log_message_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.log_message_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::LogMessage& Event::_internal_log_message() const {
  return _internal_has_log_message()
      ? *_impl_.what_.log_message_
      : reinterpret_cast< ::tensorflow::LogMessage&>(::tensorflow::_LogMessage_default_instance_);
}
inline const ::tensorflow::LogMessage& Event::log_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.log_message)
  return _internal_log_message();
}
inline ::tensorflow::LogMessage* Event::unsafe_arena_release_log_message() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.log_message)
  if (_internal_has_log_message()) {
    clear_has_what();
    ::tensorflow::LogMessage* temp = _impl_.what_.log_message_;
    _impl_.what_.log_message_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_log_message(::tensorflow::LogMessage* log_message) {
  clear_what();
  if (log_message) {
    set_has_log_message();
    _impl_.what_.log_message_ = log_message;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.log_message)
}
inline ::tensorflow::LogMessage* Event::_internal_mutable_log_message() {
  if (!_internal_has_log_message()) {
    clear_what();
    set_has_log_message();
    _impl_.what_.log_message_ = CreateMaybeMessage< ::tensorflow::LogMessage >(GetArenaForAllocation());
  }
  return _impl_.what_.log_message_;
}
inline ::tensorflow::LogMessage* Event::mutable_log_message() {
  ::tensorflow::LogMessage* _msg = _internal_mutable_log_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.log_message)
  return _msg;
}

// .tensorflow.SessionLog session_log = 7;
inline bool Event::_internal_has_session_log() const {
  return what_case() == kSessionLog;
}
inline bool Event::has_session_log() const {
  return _internal_has_session_log();
}
inline void Event::set_has_session_log() {
  _impl_._oneof_case_[0] = kSessionLog;
}
inline void Event::clear_session_log() {
  if (_internal_has_session_log()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.session_log_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::SessionLog* Event::release_session_log() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.session_log)
  if (_internal_has_session_log()) {
    clear_has_what();
    ::tensorflow::SessionLog* temp = _impl_.what_.session_log_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.session_log_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::SessionLog& Event::_internal_session_log() const {
  return _internal_has_session_log()
      ? *_impl_.what_.session_log_
      : reinterpret_cast< ::tensorflow::SessionLog&>(::tensorflow::_SessionLog_default_instance_);
}
inline const ::tensorflow::SessionLog& Event::session_log() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.session_log)
  return _internal_session_log();
}
inline ::tensorflow::SessionLog* Event::unsafe_arena_release_session_log() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.session_log)
  if (_internal_has_session_log()) {
    clear_has_what();
    ::tensorflow::SessionLog* temp = _impl_.what_.session_log_;
    _impl_.what_.session_log_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_session_log(::tensorflow::SessionLog* session_log) {
  clear_what();
  if (session_log) {
    set_has_session_log();
    _impl_.what_.session_log_ = session_log;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.session_log)
}
inline ::tensorflow::SessionLog* Event::_internal_mutable_session_log() {
  if (!_internal_has_session_log()) {
    clear_what();
    set_has_session_log();
    _impl_.what_.session_log_ = CreateMaybeMessage< ::tensorflow::SessionLog >(GetArenaForAllocation());
  }
  return _impl_.what_.session_log_;
}
inline ::tensorflow::SessionLog* Event::mutable_session_log() {
  ::tensorflow::SessionLog* _msg = _internal_mutable_session_log();
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.session_log)
  return _msg;
}

// .tensorflow.TaggedRunMetadata tagged_run_metadata = 8;
inline bool Event::_internal_has_tagged_run_metadata() const {
  return what_case() == kTaggedRunMetadata;
}
inline bool Event::has_tagged_run_metadata() const {
  return _internal_has_tagged_run_metadata();
}
inline void Event::set_has_tagged_run_metadata() {
  _impl_._oneof_case_[0] = kTaggedRunMetadata;
}
inline void Event::clear_tagged_run_metadata() {
  if (_internal_has_tagged_run_metadata()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.what_.tagged_run_metadata_;
    }
    clear_has_what();
  }
}
inline ::tensorflow::TaggedRunMetadata* Event::release_tagged_run_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.tagged_run_metadata)
  if (_internal_has_tagged_run_metadata()) {
    clear_has_what();
    ::tensorflow::TaggedRunMetadata* temp = _impl_.what_.tagged_run_metadata_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.what_.tagged_run_metadata_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TaggedRunMetadata& Event::_internal_tagged_run_metadata() const {
  return _internal_has_tagged_run_metadata()
      ? *_impl_.what_.tagged_run_metadata_
      : reinterpret_cast< ::tensorflow::TaggedRunMetadata&>(::tensorflow::_TaggedRunMetadata_default_instance_);
}
inline const ::tensorflow::TaggedRunMetadata& Event::tagged_run_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.tagged_run_metadata)
  return _internal_tagged_run_metadata();
}
inline ::tensorflow::TaggedRunMetadata* Event::unsafe_arena_release_tagged_run_metadata() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.Event.tagged_run_metadata)
  if (_internal_has_tagged_run_metadata()) {
    clear_has_what();
    ::tensorflow::TaggedRunMetadata* temp = _impl_.what_.tagged_run_metadata_;
    _impl_.what_.tagged_run_metadata_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_tagged_run_metadata(::tensorflow::TaggedRunMetadata* tagged_run_metadata) {
  clear_what();
  if (tagged_run_metadata) {
    set_has_tagged_run_metadata();
    _impl_.what_.tagged_run_metadata_ = tagged_run_metadata;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.tagged_run_metadata)
}
inline ::tensorflow::TaggedRunMetadata* Event::_internal_mutable_tagged_run_metadata() {
  if (!_internal_has_tagged_run_metadata()) {
    clear_what();
    set_has_tagged_run_metadata();
    _impl_.what_.tagged_run_metadata_ = CreateMaybeMessage< ::tensorflow::TaggedRunMetadata >(GetArenaForAllocation());
  }
  return _impl_.what_.tagged_run_metadata_;
}
inline ::tensorflow::TaggedRunMetadata* Event::mutable_tagged_run_metadata() {
  ::tensorflow::TaggedRunMetadata* _msg = _internal_mutable_tagged_run_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.tagged_run_metadata)
  return _msg;
}

// bytes meta_graph_def = 9;
inline bool Event::_internal_has_meta_graph_def() const {
  return what_case() == kMetaGraphDef;
}
inline bool Event::has_meta_graph_def() const {
  return _internal_has_meta_graph_def();
}
inline void Event::set_has_meta_graph_def() {
  _impl_._oneof_case_[0] = kMetaGraphDef;
}
inline void Event::clear_meta_graph_def() {
  if (_internal_has_meta_graph_def()) {
    _impl_.what_.meta_graph_def_.Destroy();
    clear_has_what();
  }
}
inline const std::string& Event::meta_graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.meta_graph_def)
  return _internal_meta_graph_def();
}
template <typename ArgT0, typename... ArgT>
inline void Event::set_meta_graph_def(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_meta_graph_def()) {
    clear_what();
    set_has_meta_graph_def();
    _impl_.what_.meta_graph_def_.InitDefault();
  }
  _impl_.what_.meta_graph_def_.SetBytes( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.Event.meta_graph_def)
}
inline std::string* Event::mutable_meta_graph_def() {
  std::string* _s = _internal_mutable_meta_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.meta_graph_def)
  return _s;
}
inline const std::string& Event::_internal_meta_graph_def() const {
  if (_internal_has_meta_graph_def()) {
    return _impl_.what_.meta_graph_def_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void Event::_internal_set_meta_graph_def(const std::string& value) {
  if (!_internal_has_meta_graph_def()) {
    clear_what();
    set_has_meta_graph_def();
    _impl_.what_.meta_graph_def_.InitDefault();
  }
  _impl_.what_.meta_graph_def_.Set(value, GetArenaForAllocation());
}
inline std::string* Event::_internal_mutable_meta_graph_def() {
  if (!_internal_has_meta_graph_def()) {
    clear_what();
    set_has_meta_graph_def();
    _impl_.what_.meta_graph_def_.InitDefault();
  }
  return _impl_.what_.meta_graph_def_.Mutable(      GetArenaForAllocation());
}
inline std::string* Event::release_meta_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.meta_graph_def)
  if (_internal_has_meta_graph_def()) {
    clear_has_what();
    return _impl_.what_.meta_graph_def_.Release();
  } else {
    return nullptr;
  }
}
inline void Event::set_allocated_meta_graph_def(std::string* meta_graph_def) {
  if (has_what()) {
    clear_what();
  }
  if (meta_graph_def != nullptr) {
    set_has_meta_graph_def();
    _impl_.what_.meta_graph_def_.InitAllocated(meta_graph_def, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.meta_graph_def)
}

// .tensorflow.SourceMetadata source_metadata = 10;
inline bool Event::_internal_has_source_metadata() const {
  return this != internal_default_instance() && _impl_.source_metadata_ != nullptr;
}
inline bool Event::has_source_metadata() const {
  return _internal_has_source_metadata();
}
inline void Event::clear_source_metadata() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_metadata_ != nullptr) {
    delete _impl_.source_metadata_;
  }
  _impl_.source_metadata_ = nullptr;
}
inline const ::tensorflow::SourceMetadata& Event::_internal_source_metadata() const {
  const ::tensorflow::SourceMetadata* p = _impl_.source_metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SourceMetadata&>(
      ::tensorflow::_SourceMetadata_default_instance_);
}
inline const ::tensorflow::SourceMetadata& Event::source_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.Event.source_metadata)
  return _internal_source_metadata();
}
inline void Event::unsafe_arena_set_allocated_source_metadata(
    ::tensorflow::SourceMetadata* source_metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_metadata_);
  }
  _impl_.source_metadata_ = source_metadata;
  if (source_metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.Event.source_metadata)
}
inline ::tensorflow::SourceMetadata* Event::release_source_metadata() {
  
  ::tensorflow::SourceMetadata* temp = _impl_.source_metadata_;
  _impl_.source_metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SourceMetadata* Event::unsafe_arena_release_source_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.Event.source_metadata)
  
  ::tensorflow::SourceMetadata* temp = _impl_.source_metadata_;
  _impl_.source_metadata_ = nullptr;
  return temp;
}
inline ::tensorflow::SourceMetadata* Event::_internal_mutable_source_metadata() {
  
  if (_impl_.source_metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SourceMetadata>(GetArenaForAllocation());
    _impl_.source_metadata_ = p;
  }
  return _impl_.source_metadata_;
}
inline ::tensorflow::SourceMetadata* Event::mutable_source_metadata() {
  ::tensorflow::SourceMetadata* _msg = _internal_mutable_source_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.Event.source_metadata)
  return _msg;
}
inline void Event::set_allocated_source_metadata(::tensorflow::SourceMetadata* source_metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_metadata_;
  }
  if (source_metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_metadata);
    if (message_arena != submessage_arena) {
      source_metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_metadata, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_metadata_ = source_metadata;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.Event.source_metadata)
}

inline bool Event::has_what() const {
  return what_case() != WHAT_NOT_SET;
}
inline void Event::clear_has_what() {
  _impl_._oneof_case_[0] = WHAT_NOT_SET;
}
inline Event::WhatCase Event::what_case() const {
  return Event::WhatCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// SourceMetadata

// string writer = 1;
inline void SourceMetadata::clear_writer() {
  _impl_.writer_.ClearToEmpty();
}
inline const std::string& SourceMetadata::writer() const {
  // @@protoc_insertion_point(field_get:tensorflow.SourceMetadata.writer)
  return _internal_writer();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SourceMetadata::set_writer(ArgT0&& arg0, ArgT... args) {
 
 _impl_.writer_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SourceMetadata.writer)
}
inline std::string* SourceMetadata::mutable_writer() {
  std::string* _s = _internal_mutable_writer();
  // @@protoc_insertion_point(field_mutable:tensorflow.SourceMetadata.writer)
  return _s;
}
inline const std::string& SourceMetadata::_internal_writer() const {
  return _impl_.writer_.Get();
}
inline void SourceMetadata::_internal_set_writer(const std::string& value) {
  
  _impl_.writer_.Set(value, GetArenaForAllocation());
}
inline std::string* SourceMetadata::_internal_mutable_writer() {
  
  return _impl_.writer_.Mutable(GetArenaForAllocation());
}
inline std::string* SourceMetadata::release_writer() {
  // @@protoc_insertion_point(field_release:tensorflow.SourceMetadata.writer)
  return _impl_.writer_.Release();
}
inline void SourceMetadata::set_allocated_writer(std::string* writer) {
  if (writer != nullptr) {
    
  } else {
    
  }
  _impl_.writer_.SetAllocated(writer, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.writer_.IsDefault()) {
    _impl_.writer_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SourceMetadata.writer)
}

// -------------------------------------------------------------------

// LogMessage

// .tensorflow.LogMessage.Level level = 1;
inline void LogMessage::clear_level() {
  _impl_.level_ = 0;
}
inline ::tensorflow::LogMessage_Level LogMessage::_internal_level() const {
  return static_cast< ::tensorflow::LogMessage_Level >(_impl_.level_);
}
inline ::tensorflow::LogMessage_Level LogMessage::level() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogMessage.level)
  return _internal_level();
}
inline void LogMessage::_internal_set_level(::tensorflow::LogMessage_Level value) {
  
  _impl_.level_ = value;
}
inline void LogMessage::set_level(::tensorflow::LogMessage_Level value) {
  _internal_set_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.LogMessage.level)
}

// string message = 2;
inline void LogMessage::clear_message() {
  _impl_.message_.ClearToEmpty();
}
inline const std::string& LogMessage::message() const {
  // @@protoc_insertion_point(field_get:tensorflow.LogMessage.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LogMessage::set_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.LogMessage.message)
}
inline std::string* LogMessage::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.LogMessage.message)
  return _s;
}
inline const std::string& LogMessage::_internal_message() const {
  return _impl_.message_.Get();
}
inline void LogMessage::_internal_set_message(const std::string& value) {
  
  _impl_.message_.Set(value, GetArenaForAllocation());
}
inline std::string* LogMessage::_internal_mutable_message() {
  
  return _impl_.message_.Mutable(GetArenaForAllocation());
}
inline std::string* LogMessage::release_message() {
  // @@protoc_insertion_point(field_release:tensorflow.LogMessage.message)
  return _impl_.message_.Release();
}
inline void LogMessage::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  _impl_.message_.SetAllocated(message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.message_.IsDefault()) {
    _impl_.message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.LogMessage.message)
}

// -------------------------------------------------------------------

// SessionLog

// .tensorflow.SessionLog.SessionStatus status = 1;
inline void SessionLog::clear_status() {
  _impl_.status_ = 0;
}
inline ::tensorflow::SessionLog_SessionStatus SessionLog::_internal_status() const {
  return static_cast< ::tensorflow::SessionLog_SessionStatus >(_impl_.status_);
}
inline ::tensorflow::SessionLog_SessionStatus SessionLog::status() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionLog.status)
  return _internal_status();
}
inline void SessionLog::_internal_set_status(::tensorflow::SessionLog_SessionStatus value) {
  
  _impl_.status_ = value;
}
inline void SessionLog::set_status(::tensorflow::SessionLog_SessionStatus value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:tensorflow.SessionLog.status)
}

// string checkpoint_path = 2;
inline void SessionLog::clear_checkpoint_path() {
  _impl_.checkpoint_path_.ClearToEmpty();
}
inline const std::string& SessionLog::checkpoint_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionLog.checkpoint_path)
  return _internal_checkpoint_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SessionLog::set_checkpoint_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.checkpoint_path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SessionLog.checkpoint_path)
}
inline std::string* SessionLog::mutable_checkpoint_path() {
  std::string* _s = _internal_mutable_checkpoint_path();
  // @@protoc_insertion_point(field_mutable:tensorflow.SessionLog.checkpoint_path)
  return _s;
}
inline const std::string& SessionLog::_internal_checkpoint_path() const {
  return _impl_.checkpoint_path_.Get();
}
inline void SessionLog::_internal_set_checkpoint_path(const std::string& value) {
  
  _impl_.checkpoint_path_.Set(value, GetArenaForAllocation());
}
inline std::string* SessionLog::_internal_mutable_checkpoint_path() {
  
  return _impl_.checkpoint_path_.Mutable(GetArenaForAllocation());
}
inline std::string* SessionLog::release_checkpoint_path() {
  // @@protoc_insertion_point(field_release:tensorflow.SessionLog.checkpoint_path)
  return _impl_.checkpoint_path_.Release();
}
inline void SessionLog::set_allocated_checkpoint_path(std::string* checkpoint_path) {
  if (checkpoint_path != nullptr) {
    
  } else {
    
  }
  _impl_.checkpoint_path_.SetAllocated(checkpoint_path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.checkpoint_path_.IsDefault()) {
    _impl_.checkpoint_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SessionLog.checkpoint_path)
}

// string msg = 3;
inline void SessionLog::clear_msg() {
  _impl_.msg_.ClearToEmpty();
}
inline const std::string& SessionLog::msg() const {
  // @@protoc_insertion_point(field_get:tensorflow.SessionLog.msg)
  return _internal_msg();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SessionLog::set_msg(ArgT0&& arg0, ArgT... args) {
 
 _impl_.msg_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SessionLog.msg)
}
inline std::string* SessionLog::mutable_msg() {
  std::string* _s = _internal_mutable_msg();
  // @@protoc_insertion_point(field_mutable:tensorflow.SessionLog.msg)
  return _s;
}
inline const std::string& SessionLog::_internal_msg() const {
  return _impl_.msg_.Get();
}
inline void SessionLog::_internal_set_msg(const std::string& value) {
  
  _impl_.msg_.Set(value, GetArenaForAllocation());
}
inline std::string* SessionLog::_internal_mutable_msg() {
  
  return _impl_.msg_.Mutable(GetArenaForAllocation());
}
inline std::string* SessionLog::release_msg() {
  // @@protoc_insertion_point(field_release:tensorflow.SessionLog.msg)
  return _impl_.msg_.Release();
}
inline void SessionLog::set_allocated_msg(std::string* msg) {
  if (msg != nullptr) {
    
  } else {
    
  }
  _impl_.msg_.SetAllocated(msg, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.msg_.IsDefault()) {
    _impl_.msg_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SessionLog.msg)
}

// -------------------------------------------------------------------

// TaggedRunMetadata

// string tag = 1;
inline void TaggedRunMetadata::clear_tag() {
  _impl_.tag_.ClearToEmpty();
}
inline const std::string& TaggedRunMetadata::tag() const {
  // @@protoc_insertion_point(field_get:tensorflow.TaggedRunMetadata.tag)
  return _internal_tag();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TaggedRunMetadata::set_tag(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tag_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TaggedRunMetadata.tag)
}
inline std::string* TaggedRunMetadata::mutable_tag() {
  std::string* _s = _internal_mutable_tag();
  // @@protoc_insertion_point(field_mutable:tensorflow.TaggedRunMetadata.tag)
  return _s;
}
inline const std::string& TaggedRunMetadata::_internal_tag() const {
  return _impl_.tag_.Get();
}
inline void TaggedRunMetadata::_internal_set_tag(const std::string& value) {
  
  _impl_.tag_.Set(value, GetArenaForAllocation());
}
inline std::string* TaggedRunMetadata::_internal_mutable_tag() {
  
  return _impl_.tag_.Mutable(GetArenaForAllocation());
}
inline std::string* TaggedRunMetadata::release_tag() {
  // @@protoc_insertion_point(field_release:tensorflow.TaggedRunMetadata.tag)
  return _impl_.tag_.Release();
}
inline void TaggedRunMetadata::set_allocated_tag(std::string* tag) {
  if (tag != nullptr) {
    
  } else {
    
  }
  _impl_.tag_.SetAllocated(tag, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tag_.IsDefault()) {
    _impl_.tag_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TaggedRunMetadata.tag)
}

// bytes run_metadata = 2;
inline void TaggedRunMetadata::clear_run_metadata() {
  _impl_.run_metadata_.ClearToEmpty();
}
inline const std::string& TaggedRunMetadata::run_metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.TaggedRunMetadata.run_metadata)
  return _internal_run_metadata();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TaggedRunMetadata::set_run_metadata(ArgT0&& arg0, ArgT... args) {
 
 _impl_.run_metadata_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TaggedRunMetadata.run_metadata)
}
inline std::string* TaggedRunMetadata::mutable_run_metadata() {
  std::string* _s = _internal_mutable_run_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.TaggedRunMetadata.run_metadata)
  return _s;
}
inline const std::string& TaggedRunMetadata::_internal_run_metadata() const {
  return _impl_.run_metadata_.Get();
}
inline void TaggedRunMetadata::_internal_set_run_metadata(const std::string& value) {
  
  _impl_.run_metadata_.Set(value, GetArenaForAllocation());
}
inline std::string* TaggedRunMetadata::_internal_mutable_run_metadata() {
  
  return _impl_.run_metadata_.Mutable(GetArenaForAllocation());
}
inline std::string* TaggedRunMetadata::release_run_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.TaggedRunMetadata.run_metadata)
  return _impl_.run_metadata_.Release();
}
inline void TaggedRunMetadata::set_allocated_run_metadata(std::string* run_metadata) {
  if (run_metadata != nullptr) {
    
  } else {
    
  }
  _impl_.run_metadata_.SetAllocated(run_metadata, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.run_metadata_.IsDefault()) {
    _impl_.run_metadata_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TaggedRunMetadata.run_metadata)
}

// -------------------------------------------------------------------

// WatchdogConfig

// int64 timeout_ms = 1;
inline void WatchdogConfig::clear_timeout_ms() {
  _impl_.timeout_ms_ = int64_t{0};
}
inline int64_t WatchdogConfig::_internal_timeout_ms() const {
  return _impl_.timeout_ms_;
}
inline int64_t WatchdogConfig::timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.WatchdogConfig.timeout_ms)
  return _internal_timeout_ms();
}
inline void WatchdogConfig::_internal_set_timeout_ms(int64_t value) {
  
  _impl_.timeout_ms_ = value;
}
inline void WatchdogConfig::set_timeout_ms(int64_t value) {
  _internal_set_timeout_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.WatchdogConfig.timeout_ms)
}

// -------------------------------------------------------------------

// RequestedExitCode

// int32 exit_code = 1;
inline void RequestedExitCode::clear_exit_code() {
  _impl_.exit_code_ = 0;
}
inline int32_t RequestedExitCode::_internal_exit_code() const {
  return _impl_.exit_code_;
}
inline int32_t RequestedExitCode::exit_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.RequestedExitCode.exit_code)
  return _internal_exit_code();
}
inline void RequestedExitCode::_internal_set_exit_code(int32_t value) {
  
  _impl_.exit_code_ = value;
}
inline void RequestedExitCode::set_exit_code(int32_t value) {
  _internal_set_exit_code(value);
  // @@protoc_insertion_point(field_set:tensorflow.RequestedExitCode.exit_code)
}

// -------------------------------------------------------------------

// WorkerHeartbeatRequest

// .tensorflow.WorkerShutdownMode shutdown_mode = 1;
inline void WorkerHeartbeatRequest::clear_shutdown_mode() {
  _impl_.shutdown_mode_ = 0;
}
inline ::tensorflow::WorkerShutdownMode WorkerHeartbeatRequest::_internal_shutdown_mode() const {
  return static_cast< ::tensorflow::WorkerShutdownMode >(_impl_.shutdown_mode_);
}
inline ::tensorflow::WorkerShutdownMode WorkerHeartbeatRequest::shutdown_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatRequest.shutdown_mode)
  return _internal_shutdown_mode();
}
inline void WorkerHeartbeatRequest::_internal_set_shutdown_mode(::tensorflow::WorkerShutdownMode value) {
  
  _impl_.shutdown_mode_ = value;
}
inline void WorkerHeartbeatRequest::set_shutdown_mode(::tensorflow::WorkerShutdownMode value) {
  _internal_set_shutdown_mode(value);
  // @@protoc_insertion_point(field_set:tensorflow.WorkerHeartbeatRequest.shutdown_mode)
}

// .tensorflow.WatchdogConfig watchdog_config = 2;
inline bool WorkerHeartbeatRequest::_internal_has_watchdog_config() const {
  return this != internal_default_instance() && _impl_.watchdog_config_ != nullptr;
}
inline bool WorkerHeartbeatRequest::has_watchdog_config() const {
  return _internal_has_watchdog_config();
}
inline void WorkerHeartbeatRequest::clear_watchdog_config() {
  if (GetArenaForAllocation() == nullptr && _impl_.watchdog_config_ != nullptr) {
    delete _impl_.watchdog_config_;
  }
  _impl_.watchdog_config_ = nullptr;
}
inline const ::tensorflow::WatchdogConfig& WorkerHeartbeatRequest::_internal_watchdog_config() const {
  const ::tensorflow::WatchdogConfig* p = _impl_.watchdog_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::WatchdogConfig&>(
      ::tensorflow::_WatchdogConfig_default_instance_);
}
inline const ::tensorflow::WatchdogConfig& WorkerHeartbeatRequest::watchdog_config() const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatRequest.watchdog_config)
  return _internal_watchdog_config();
}
inline void WorkerHeartbeatRequest::unsafe_arena_set_allocated_watchdog_config(
    ::tensorflow::WatchdogConfig* watchdog_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.watchdog_config_);
  }
  _impl_.watchdog_config_ = watchdog_config;
  if (watchdog_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WorkerHeartbeatRequest.watchdog_config)
}
inline ::tensorflow::WatchdogConfig* WorkerHeartbeatRequest::release_watchdog_config() {
  
  ::tensorflow::WatchdogConfig* temp = _impl_.watchdog_config_;
  _impl_.watchdog_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::WatchdogConfig* WorkerHeartbeatRequest::unsafe_arena_release_watchdog_config() {
  // @@protoc_insertion_point(field_release:tensorflow.WorkerHeartbeatRequest.watchdog_config)
  
  ::tensorflow::WatchdogConfig* temp = _impl_.watchdog_config_;
  _impl_.watchdog_config_ = nullptr;
  return temp;
}
inline ::tensorflow::WatchdogConfig* WorkerHeartbeatRequest::_internal_mutable_watchdog_config() {
  
  if (_impl_.watchdog_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::WatchdogConfig>(GetArenaForAllocation());
    _impl_.watchdog_config_ = p;
  }
  return _impl_.watchdog_config_;
}
inline ::tensorflow::WatchdogConfig* WorkerHeartbeatRequest::mutable_watchdog_config() {
  ::tensorflow::WatchdogConfig* _msg = _internal_mutable_watchdog_config();
  // @@protoc_insertion_point(field_mutable:tensorflow.WorkerHeartbeatRequest.watchdog_config)
  return _msg;
}
inline void WorkerHeartbeatRequest::set_allocated_watchdog_config(::tensorflow::WatchdogConfig* watchdog_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.watchdog_config_;
  }
  if (watchdog_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(watchdog_config);
    if (message_arena != submessage_arena) {
      watchdog_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, watchdog_config, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.watchdog_config_ = watchdog_config;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WorkerHeartbeatRequest.watchdog_config)
}

// .tensorflow.RequestedExitCode exit_code = 3;
inline bool WorkerHeartbeatRequest::_internal_has_exit_code() const {
  return this != internal_default_instance() && _impl_.exit_code_ != nullptr;
}
inline bool WorkerHeartbeatRequest::has_exit_code() const {
  return _internal_has_exit_code();
}
inline void WorkerHeartbeatRequest::clear_exit_code() {
  if (GetArenaForAllocation() == nullptr && _impl_.exit_code_ != nullptr) {
    delete _impl_.exit_code_;
  }
  _impl_.exit_code_ = nullptr;
}
inline const ::tensorflow::RequestedExitCode& WorkerHeartbeatRequest::_internal_exit_code() const {
  const ::tensorflow::RequestedExitCode* p = _impl_.exit_code_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::RequestedExitCode&>(
      ::tensorflow::_RequestedExitCode_default_instance_);
}
inline const ::tensorflow::RequestedExitCode& WorkerHeartbeatRequest::exit_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatRequest.exit_code)
  return _internal_exit_code();
}
inline void WorkerHeartbeatRequest::unsafe_arena_set_allocated_exit_code(
    ::tensorflow::RequestedExitCode* exit_code) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.exit_code_);
  }
  _impl_.exit_code_ = exit_code;
  if (exit_code) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.WorkerHeartbeatRequest.exit_code)
}
inline ::tensorflow::RequestedExitCode* WorkerHeartbeatRequest::release_exit_code() {
  
  ::tensorflow::RequestedExitCode* temp = _impl_.exit_code_;
  _impl_.exit_code_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::RequestedExitCode* WorkerHeartbeatRequest::unsafe_arena_release_exit_code() {
  // @@protoc_insertion_point(field_release:tensorflow.WorkerHeartbeatRequest.exit_code)
  
  ::tensorflow::RequestedExitCode* temp = _impl_.exit_code_;
  _impl_.exit_code_ = nullptr;
  return temp;
}
inline ::tensorflow::RequestedExitCode* WorkerHeartbeatRequest::_internal_mutable_exit_code() {
  
  if (_impl_.exit_code_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::RequestedExitCode>(GetArenaForAllocation());
    _impl_.exit_code_ = p;
  }
  return _impl_.exit_code_;
}
inline ::tensorflow::RequestedExitCode* WorkerHeartbeatRequest::mutable_exit_code() {
  ::tensorflow::RequestedExitCode* _msg = _internal_mutable_exit_code();
  // @@protoc_insertion_point(field_mutable:tensorflow.WorkerHeartbeatRequest.exit_code)
  return _msg;
}
inline void WorkerHeartbeatRequest::set_allocated_exit_code(::tensorflow::RequestedExitCode* exit_code) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.exit_code_;
  }
  if (exit_code) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(exit_code);
    if (message_arena != submessage_arena) {
      exit_code = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, exit_code, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.exit_code_ = exit_code;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WorkerHeartbeatRequest.exit_code)
}

// -------------------------------------------------------------------

// WorkerHeartbeatResponse

// .tensorflow.WorkerHealth health_status = 1;
inline void WorkerHeartbeatResponse::clear_health_status() {
  _impl_.health_status_ = 0;
}
inline ::tensorflow::WorkerHealth WorkerHeartbeatResponse::_internal_health_status() const {
  return static_cast< ::tensorflow::WorkerHealth >(_impl_.health_status_);
}
inline ::tensorflow::WorkerHealth WorkerHeartbeatResponse::health_status() const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatResponse.health_status)
  return _internal_health_status();
}
inline void WorkerHeartbeatResponse::_internal_set_health_status(::tensorflow::WorkerHealth value) {
  
  _impl_.health_status_ = value;
}
inline void WorkerHeartbeatResponse::set_health_status(::tensorflow::WorkerHealth value) {
  _internal_set_health_status(value);
  // @@protoc_insertion_point(field_set:tensorflow.WorkerHeartbeatResponse.health_status)
}

// repeated .tensorflow.Event worker_log = 2;
inline int WorkerHeartbeatResponse::_internal_worker_log_size() const {
  return _impl_.worker_log_.size();
}
inline int WorkerHeartbeatResponse::worker_log_size() const {
  return _internal_worker_log_size();
}
inline void WorkerHeartbeatResponse::clear_worker_log() {
  _impl_.worker_log_.Clear();
}
inline ::tensorflow::Event* WorkerHeartbeatResponse::mutable_worker_log(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.WorkerHeartbeatResponse.worker_log)
  return _impl_.worker_log_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Event >*
WorkerHeartbeatResponse::mutable_worker_log() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.WorkerHeartbeatResponse.worker_log)
  return &_impl_.worker_log_;
}
inline const ::tensorflow::Event& WorkerHeartbeatResponse::_internal_worker_log(int index) const {
  return _impl_.worker_log_.Get(index);
}
inline const ::tensorflow::Event& WorkerHeartbeatResponse::worker_log(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatResponse.worker_log)
  return _internal_worker_log(index);
}
inline ::tensorflow::Event* WorkerHeartbeatResponse::_internal_add_worker_log() {
  return _impl_.worker_log_.Add();
}
inline ::tensorflow::Event* WorkerHeartbeatResponse::add_worker_log() {
  ::tensorflow::Event* _add = _internal_add_worker_log();
  // @@protoc_insertion_point(field_add:tensorflow.WorkerHeartbeatResponse.worker_log)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::Event >&
WorkerHeartbeatResponse::worker_log() const {
  // @@protoc_insertion_point(field_list:tensorflow.WorkerHeartbeatResponse.worker_log)
  return _impl_.worker_log_;
}

// string hostname = 3;
inline void WorkerHeartbeatResponse::clear_hostname() {
  _impl_.hostname_.ClearToEmpty();
}
inline const std::string& WorkerHeartbeatResponse::hostname() const {
  // @@protoc_insertion_point(field_get:tensorflow.WorkerHeartbeatResponse.hostname)
  return _internal_hostname();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WorkerHeartbeatResponse::set_hostname(ArgT0&& arg0, ArgT... args) {
 
 _impl_.hostname_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.WorkerHeartbeatResponse.hostname)
}
inline std::string* WorkerHeartbeatResponse::mutable_hostname() {
  std::string* _s = _internal_mutable_hostname();
  // @@protoc_insertion_point(field_mutable:tensorflow.WorkerHeartbeatResponse.hostname)
  return _s;
}
inline const std::string& WorkerHeartbeatResponse::_internal_hostname() const {
  return _impl_.hostname_.Get();
}
inline void WorkerHeartbeatResponse::_internal_set_hostname(const std::string& value) {
  
  _impl_.hostname_.Set(value, GetArenaForAllocation());
}
inline std::string* WorkerHeartbeatResponse::_internal_mutable_hostname() {
  
  return _impl_.hostname_.Mutable(GetArenaForAllocation());
}
inline std::string* WorkerHeartbeatResponse::release_hostname() {
  // @@protoc_insertion_point(field_release:tensorflow.WorkerHeartbeatResponse.hostname)
  return _impl_.hostname_.Release();
}
inline void WorkerHeartbeatResponse::set_allocated_hostname(std::string* hostname) {
  if (hostname != nullptr) {
    
  } else {
    
  }
  _impl_.hostname_.SetAllocated(hostname, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.hostname_.IsDefault()) {
    _impl_.hostname_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.WorkerHeartbeatResponse.hostname)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::LogMessage_Level> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::LogMessage_Level>() {
  return ::tensorflow::LogMessage_Level_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::SessionLog_SessionStatus> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::SessionLog_SessionStatus>() {
  return ::tensorflow::SessionLog_SessionStatus_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::WorkerHealth> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::WorkerHealth>() {
  return ::tensorflow::WorkerHealth_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::WorkerShutdownMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::WorkerShutdownMode>() {
  return ::tensorflow::WorkerShutdownMode_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2futil_2fevent_2eproto
