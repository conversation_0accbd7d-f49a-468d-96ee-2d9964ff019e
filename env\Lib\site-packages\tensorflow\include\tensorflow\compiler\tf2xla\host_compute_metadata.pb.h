// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/tf2xla/host_compute_metadata.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto;
namespace tensorflow {
namespace tf2xla {
class HostComputeMetadata;
struct HostComputeMetadataDefaultTypeInternal;
extern HostComputeMetadataDefaultTypeInternal _HostComputeMetadata_default_instance_;
class HostTransferMetadata;
struct HostTransferMetadataDefaultTypeInternal;
extern HostTransferMetadataDefaultTypeInternal _HostTransferMetadata_default_instance_;
class TensorMetadata;
struct TensorMetadataDefaultTypeInternal;
extern TensorMetadataDefaultTypeInternal _TensorMetadata_default_instance_;
}  // namespace tf2xla
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tf2xla::HostComputeMetadata* Arena::CreateMaybeMessage<::tensorflow::tf2xla::HostComputeMetadata>(Arena*);
template<> ::tensorflow::tf2xla::HostTransferMetadata* Arena::CreateMaybeMessage<::tensorflow::tf2xla::HostTransferMetadata>(Arena*);
template<> ::tensorflow::tf2xla::TensorMetadata* Arena::CreateMaybeMessage<::tensorflow::tf2xla::TensorMetadata>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tf2xla {

// ===================================================================

class TensorMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.TensorMetadata) */ {
 public:
  inline TensorMetadata() : TensorMetadata(nullptr) {}
  ~TensorMetadata() override;
  explicit PROTOBUF_CONSTEXPR TensorMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorMetadata(const TensorMetadata& from);
  TensorMetadata(TensorMetadata&& from) noexcept
    : TensorMetadata() {
    *this = ::std::move(from);
  }

  inline TensorMetadata& operator=(const TensorMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorMetadata& operator=(TensorMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorMetadata* internal_default_instance() {
    return reinterpret_cast<const TensorMetadata*>(
               &_TensorMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TensorMetadata& a, TensorMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorMetadata& from) {
    TensorMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.TensorMetadata";
  }
  protected:
  explicit TensorMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kChannelIdFieldNumber = 3,
    kTypeFieldNumber = 1,
  };
  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // int64 channel_id = 3;
  void clear_channel_id();
  int64_t channel_id() const;
  void set_channel_id(int64_t value);
  private:
  int64_t _internal_channel_id() const;
  void _internal_set_channel_id(int64_t value);
  public:

  // .tensorflow.DataType type = 1;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_type() const;
  void _internal_set_type(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.TensorMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* shape_;
    int64_t channel_id_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class HostTransferMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.HostTransferMetadata) */ {
 public:
  inline HostTransferMetadata() : HostTransferMetadata(nullptr) {}
  ~HostTransferMetadata() override;
  explicit PROTOBUF_CONSTEXPR HostTransferMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HostTransferMetadata(const HostTransferMetadata& from);
  HostTransferMetadata(HostTransferMetadata&& from) noexcept
    : HostTransferMetadata() {
    *this = ::std::move(from);
  }

  inline HostTransferMetadata& operator=(const HostTransferMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline HostTransferMetadata& operator=(HostTransferMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HostTransferMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const HostTransferMetadata* internal_default_instance() {
    return reinterpret_cast<const HostTransferMetadata*>(
               &_HostTransferMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(HostTransferMetadata& a, HostTransferMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(HostTransferMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HostTransferMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HostTransferMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HostTransferMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HostTransferMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HostTransferMetadata& from) {
    HostTransferMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HostTransferMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.HostTransferMetadata";
  }
  protected:
  explicit HostTransferMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMetadataFieldNumber = 2,
    kKeyFieldNumber = 1,
  };
  // repeated .tensorflow.tf2xla.TensorMetadata metadata = 2;
  int metadata_size() const;
  private:
  int _internal_metadata_size() const;
  public:
  void clear_metadata();
  ::tensorflow::tf2xla::TensorMetadata* mutable_metadata(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata >*
      mutable_metadata();
  private:
  const ::tensorflow::tf2xla::TensorMetadata& _internal_metadata(int index) const;
  ::tensorflow::tf2xla::TensorMetadata* _internal_add_metadata();
  public:
  const ::tensorflow::tf2xla::TensorMetadata& metadata(int index) const;
  ::tensorflow::tf2xla::TensorMetadata* add_metadata();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata >&
      metadata() const;

  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.HostTransferMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata > metadata_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class HostComputeMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tf2xla.HostComputeMetadata) */ {
 public:
  inline HostComputeMetadata() : HostComputeMetadata(nullptr) {}
  ~HostComputeMetadata() override;
  explicit PROTOBUF_CONSTEXPR HostComputeMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HostComputeMetadata(const HostComputeMetadata& from);
  HostComputeMetadata(HostComputeMetadata&& from) noexcept
    : HostComputeMetadata() {
    *this = ::std::move(from);
  }

  inline HostComputeMetadata& operator=(const HostComputeMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline HostComputeMetadata& operator=(HostComputeMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HostComputeMetadata& default_instance() {
    return *internal_default_instance();
  }
  static inline const HostComputeMetadata* internal_default_instance() {
    return reinterpret_cast<const HostComputeMetadata*>(
               &_HostComputeMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(HostComputeMetadata& a, HostComputeMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(HostComputeMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HostComputeMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HostComputeMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HostComputeMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HostComputeMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HostComputeMetadata& from) {
    HostComputeMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HostComputeMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tf2xla.HostComputeMetadata";
  }
  protected:
  explicit HostComputeMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceToHostFieldNumber = 1,
    kHostToDeviceFieldNumber = 2,
  };
  // repeated .tensorflow.tf2xla.HostTransferMetadata device_to_host = 1;
  int device_to_host_size() const;
  private:
  int _internal_device_to_host_size() const;
  public:
  void clear_device_to_host();
  ::tensorflow::tf2xla::HostTransferMetadata* mutable_device_to_host(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >*
      mutable_device_to_host();
  private:
  const ::tensorflow::tf2xla::HostTransferMetadata& _internal_device_to_host(int index) const;
  ::tensorflow::tf2xla::HostTransferMetadata* _internal_add_device_to_host();
  public:
  const ::tensorflow::tf2xla::HostTransferMetadata& device_to_host(int index) const;
  ::tensorflow::tf2xla::HostTransferMetadata* add_device_to_host();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >&
      device_to_host() const;

  // repeated .tensorflow.tf2xla.HostTransferMetadata host_to_device = 2;
  int host_to_device_size() const;
  private:
  int _internal_host_to_device_size() const;
  public:
  void clear_host_to_device();
  ::tensorflow::tf2xla::HostTransferMetadata* mutable_host_to_device(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >*
      mutable_host_to_device();
  private:
  const ::tensorflow::tf2xla::HostTransferMetadata& _internal_host_to_device(int index) const;
  ::tensorflow::tf2xla::HostTransferMetadata* _internal_add_host_to_device();
  public:
  const ::tensorflow::tf2xla::HostTransferMetadata& host_to_device(int index) const;
  ::tensorflow::tf2xla::HostTransferMetadata* add_host_to_device();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >&
      host_to_device() const;

  // @@protoc_insertion_point(class_scope:tensorflow.tf2xla.HostComputeMetadata)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata > device_to_host_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata > host_to_device_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorMetadata

// .tensorflow.DataType type = 1;
inline void TensorMetadata::clear_type() {
  _impl_.type_ = 0;
}
inline ::tensorflow::DataType TensorMetadata::_internal_type() const {
  return static_cast< ::tensorflow::DataType >(_impl_.type_);
}
inline ::tensorflow::DataType TensorMetadata::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorMetadata.type)
  return _internal_type();
}
inline void TensorMetadata::_internal_set_type(::tensorflow::DataType value) {
  
  _impl_.type_ = value;
}
inline void TensorMetadata::set_type(::tensorflow::DataType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorMetadata.type)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool TensorMetadata::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool TensorMetadata::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& TensorMetadata::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& TensorMetadata::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorMetadata.shape)
  return _internal_shape();
}
inline void TensorMetadata::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tf2xla.TensorMetadata.shape)
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.TensorMetadata.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* TensorMetadata::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.TensorMetadata.shape)
  return _msg;
}
inline void TensorMetadata::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.TensorMetadata.shape)
}

// int64 channel_id = 3;
inline void TensorMetadata::clear_channel_id() {
  _impl_.channel_id_ = int64_t{0};
}
inline int64_t TensorMetadata::_internal_channel_id() const {
  return _impl_.channel_id_;
}
inline int64_t TensorMetadata::channel_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.TensorMetadata.channel_id)
  return _internal_channel_id();
}
inline void TensorMetadata::_internal_set_channel_id(int64_t value) {
  
  _impl_.channel_id_ = value;
}
inline void TensorMetadata::set_channel_id(int64_t value) {
  _internal_set_channel_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.TensorMetadata.channel_id)
}

// -------------------------------------------------------------------

// HostTransferMetadata

// string key = 1;
inline void HostTransferMetadata::clear_key() {
  _impl_.key_.ClearToEmpty();
}
inline const std::string& HostTransferMetadata::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.HostTransferMetadata.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HostTransferMetadata::set_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tf2xla.HostTransferMetadata.key)
}
inline std::string* HostTransferMetadata::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.HostTransferMetadata.key)
  return _s;
}
inline const std::string& HostTransferMetadata::_internal_key() const {
  return _impl_.key_.Get();
}
inline void HostTransferMetadata::_internal_set_key(const std::string& value) {
  
  _impl_.key_.Set(value, GetArenaForAllocation());
}
inline std::string* HostTransferMetadata::_internal_mutable_key() {
  
  return _impl_.key_.Mutable(GetArenaForAllocation());
}
inline std::string* HostTransferMetadata::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.tf2xla.HostTransferMetadata.key)
  return _impl_.key_.Release();
}
inline void HostTransferMetadata::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  _impl_.key_.SetAllocated(key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.key_.IsDefault()) {
    _impl_.key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tf2xla.HostTransferMetadata.key)
}

// repeated .tensorflow.tf2xla.TensorMetadata metadata = 2;
inline int HostTransferMetadata::_internal_metadata_size() const {
  return _impl_.metadata_.size();
}
inline int HostTransferMetadata::metadata_size() const {
  return _internal_metadata_size();
}
inline void HostTransferMetadata::clear_metadata() {
  _impl_.metadata_.Clear();
}
inline ::tensorflow::tf2xla::TensorMetadata* HostTransferMetadata::mutable_metadata(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return _impl_.metadata_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata >*
HostTransferMetadata::mutable_metadata() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return &_impl_.metadata_;
}
inline const ::tensorflow::tf2xla::TensorMetadata& HostTransferMetadata::_internal_metadata(int index) const {
  return _impl_.metadata_.Get(index);
}
inline const ::tensorflow::tf2xla::TensorMetadata& HostTransferMetadata::metadata(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return _internal_metadata(index);
}
inline ::tensorflow::tf2xla::TensorMetadata* HostTransferMetadata::_internal_add_metadata() {
  return _impl_.metadata_.Add();
}
inline ::tensorflow::tf2xla::TensorMetadata* HostTransferMetadata::add_metadata() {
  ::tensorflow::tf2xla::TensorMetadata* _add = _internal_add_metadata();
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::TensorMetadata >&
HostTransferMetadata::metadata() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.HostTransferMetadata.metadata)
  return _impl_.metadata_;
}

// -------------------------------------------------------------------

// HostComputeMetadata

// repeated .tensorflow.tf2xla.HostTransferMetadata device_to_host = 1;
inline int HostComputeMetadata::_internal_device_to_host_size() const {
  return _impl_.device_to_host_.size();
}
inline int HostComputeMetadata::device_to_host_size() const {
  return _internal_device_to_host_size();
}
inline void HostComputeMetadata::clear_device_to_host() {
  _impl_.device_to_host_.Clear();
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::mutable_device_to_host(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return _impl_.device_to_host_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >*
HostComputeMetadata::mutable_device_to_host() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return &_impl_.device_to_host_;
}
inline const ::tensorflow::tf2xla::HostTransferMetadata& HostComputeMetadata::_internal_device_to_host(int index) const {
  return _impl_.device_to_host_.Get(index);
}
inline const ::tensorflow::tf2xla::HostTransferMetadata& HostComputeMetadata::device_to_host(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return _internal_device_to_host(index);
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::_internal_add_device_to_host() {
  return _impl_.device_to_host_.Add();
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::add_device_to_host() {
  ::tensorflow::tf2xla::HostTransferMetadata* _add = _internal_add_device_to_host();
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >&
HostComputeMetadata::device_to_host() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.HostComputeMetadata.device_to_host)
  return _impl_.device_to_host_;
}

// repeated .tensorflow.tf2xla.HostTransferMetadata host_to_device = 2;
inline int HostComputeMetadata::_internal_host_to_device_size() const {
  return _impl_.host_to_device_.size();
}
inline int HostComputeMetadata::host_to_device_size() const {
  return _internal_host_to_device_size();
}
inline void HostComputeMetadata::clear_host_to_device() {
  _impl_.host_to_device_.Clear();
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::mutable_host_to_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return _impl_.host_to_device_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >*
HostComputeMetadata::mutable_host_to_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return &_impl_.host_to_device_;
}
inline const ::tensorflow::tf2xla::HostTransferMetadata& HostComputeMetadata::_internal_host_to_device(int index) const {
  return _impl_.host_to_device_.Get(index);
}
inline const ::tensorflow::tf2xla::HostTransferMetadata& HostComputeMetadata::host_to_device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return _internal_host_to_device(index);
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::_internal_add_host_to_device() {
  return _impl_.host_to_device_.Add();
}
inline ::tensorflow::tf2xla::HostTransferMetadata* HostComputeMetadata::add_host_to_device() {
  ::tensorflow::tf2xla::HostTransferMetadata* _add = _internal_add_host_to_device();
  // @@protoc_insertion_point(field_add:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tf2xla::HostTransferMetadata >&
HostComputeMetadata::host_to_device() const {
  // @@protoc_insertion_point(field_list:tensorflow.tf2xla.HostComputeMetadata.host_to_device)
  return _impl_.host_to_device_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tf2xla
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2ftf2xla_2fhost_5fcompute_5fmetadata_2eproto
