+{
   locale_version => 1.31,
   entry => <<'ENTRY', # for DUCET v13.0.0
00E4      ; [.1FA3.0020.0002] # LATIN SMALL LETTER A WITH DIAERESIS
0061 0308 ; [.1FA3.0020.0002] # LATIN SMALL LETTER A WITH DIAERESIS
00C4      ; [.1FA3.0020.0008] # LATIN CAPITAL LETTER A WITH DIAERESIS
0041 0308 ; [.1FA3.0020.0008] # LATIN CAPITAL LETTER A WITH DIAERESIS
01DF      ; [.1FA3.0020.0002][.0000.0032.0002] # LATIN SMALL LETTER A WITH DIAERESIS AND MACRON
01DE      ; [.1FA3.0020.0008][.0000.0032.0002] # LATIN CAPITAL LETTER A WITH DIAERESIS AND MACRON
00F6      ; [.213D.0020.0002] # LATIN SMALL LETTER O WITH DIAERESIS
006F 0308 ; [.213D.0020.0002] # LATIN SMALL LETTER O WITH DIAERESIS
00D6      ; [.213D.0020.0008] # LATIN CAPITAL LETTER O WITH DIAERESIS
004F 0308 ; [.213D.0020.0008] # LATIN CAPITAL LETTER O WITH DIAERESIS
022B      ; [.213D.0020.0002][.0000.0032.0002] # LATIN SMALL LETTER O WITH DIAERESIS AND MACRON
022A      ; [.213D.0020.0008][.0000.0032.0002] # LATIN CAPITAL LETTER O WITH DIAERESIS AND MACRON
00FC      ; [.2218.0020.0002] # LATIN SMALL LETTER U WITH DIAERESIS
0075 0308 ; [.2218.0020.0002] # LATIN SMALL LETTER U WITH DIAERESIS
00DC      ; [.2218.0020.0008] # LATIN CAPITAL LETTER U WITH DIAERESIS
0055 0308 ; [.2218.0020.0008] # LATIN CAPITAL LETTER U WITH DIAERESIS
01DC      ; [.2218.0020.0002][.0000.0025.0002] # LATIN SMALL LETTER U WITH DIAERESIS AND GRAVE
01DB      ; [.2218.0020.0008][.0000.0025.0002] # LATIN CAPITAL LETTER U WITH DIAERESIS AND GRAVE
01D8      ; [.2218.0020.0002][.0000.0024.0002] # LATIN SMALL LETTER U WITH DIAERESIS AND ACUTE
01D7      ; [.2218.0020.0008][.0000.0024.0002] # LATIN CAPITAL LETTER U WITH DIAERESIS AND ACUTE
01D6      ; [.2218.0020.0002][.0000.0032.0002] # LATIN SMALL LETTER U WITH DIAERESIS AND MACRON
01D5      ; [.2218.0020.0008][.0000.0032.0002] # LATIN CAPITAL LETTER U WITH DIAERESIS AND MACRON
01DA      ; [.2218.0020.0002][.0000.0028.0002] # LATIN SMALL LETTER U WITH DIAERESIS AND CARON
01D9      ; [.2218.0020.0008][.0000.0028.0002] # LATIN CAPITAL LETTER U WITH DIAERESIS AND CARON
ENTRY
};
