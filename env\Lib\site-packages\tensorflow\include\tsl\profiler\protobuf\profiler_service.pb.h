// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tsl/profiler/protobuf/profiler_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tsl/profiler/protobuf/profiler_options.pb.h"
#include "tsl/profiler/protobuf/profiler_service_monitor_result.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
namespace tensorflow {
class MonitorRequest;
struct MonitorRequestDefaultTypeInternal;
extern MonitorRequestDefaultTypeInternal _MonitorRequest_default_instance_;
class MonitorResponse;
struct MonitorResponseDefaultTypeInternal;
extern MonitorResponseDefaultTypeInternal _MonitorResponse_default_instance_;
class ProfileRequest;
struct ProfileRequestDefaultTypeInternal;
extern ProfileRequestDefaultTypeInternal _ProfileRequest_default_instance_;
class ProfileRequest_ToolOptionsEntry_DoNotUse;
struct ProfileRequest_ToolOptionsEntry_DoNotUseDefaultTypeInternal;
extern ProfileRequest_ToolOptionsEntry_DoNotUseDefaultTypeInternal _ProfileRequest_ToolOptionsEntry_DoNotUse_default_instance_;
class ProfileResponse;
struct ProfileResponseDefaultTypeInternal;
extern ProfileResponseDefaultTypeInternal _ProfileResponse_default_instance_;
class ProfileToolData;
struct ProfileToolDataDefaultTypeInternal;
extern ProfileToolDataDefaultTypeInternal _ProfileToolData_default_instance_;
class TerminateRequest;
struct TerminateRequestDefaultTypeInternal;
extern TerminateRequestDefaultTypeInternal _TerminateRequest_default_instance_;
class TerminateResponse;
struct TerminateResponseDefaultTypeInternal;
extern TerminateResponseDefaultTypeInternal _TerminateResponse_default_instance_;
class ToolRequestOptions;
struct ToolRequestOptionsDefaultTypeInternal;
extern ToolRequestOptionsDefaultTypeInternal _ToolRequestOptions_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::MonitorRequest* Arena::CreateMaybeMessage<::tensorflow::MonitorRequest>(Arena*);
template<> ::tensorflow::MonitorResponse* Arena::CreateMaybeMessage<::tensorflow::MonitorResponse>(Arena*);
template<> ::tensorflow::ProfileRequest* Arena::CreateMaybeMessage<::tensorflow::ProfileRequest>(Arena*);
template<> ::tensorflow::ProfileRequest_ToolOptionsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::ProfileRequest_ToolOptionsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ProfileResponse* Arena::CreateMaybeMessage<::tensorflow::ProfileResponse>(Arena*);
template<> ::tensorflow::ProfileToolData* Arena::CreateMaybeMessage<::tensorflow::ProfileToolData>(Arena*);
template<> ::tensorflow::TerminateRequest* Arena::CreateMaybeMessage<::tensorflow::TerminateRequest>(Arena*);
template<> ::tensorflow::TerminateResponse* Arena::CreateMaybeMessage<::tensorflow::TerminateResponse>(Arena*);
template<> ::tensorflow::ToolRequestOptions* Arena::CreateMaybeMessage<::tensorflow::ToolRequestOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class ToolRequestOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ToolRequestOptions) */ {
 public:
  inline ToolRequestOptions() : ToolRequestOptions(nullptr) {}
  ~ToolRequestOptions() override;
  explicit PROTOBUF_CONSTEXPR ToolRequestOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ToolRequestOptions(const ToolRequestOptions& from);
  ToolRequestOptions(ToolRequestOptions&& from) noexcept
    : ToolRequestOptions() {
    *this = ::std::move(from);
  }

  inline ToolRequestOptions& operator=(const ToolRequestOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ToolRequestOptions& operator=(ToolRequestOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ToolRequestOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const ToolRequestOptions* internal_default_instance() {
    return reinterpret_cast<const ToolRequestOptions*>(
               &_ToolRequestOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ToolRequestOptions& a, ToolRequestOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ToolRequestOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ToolRequestOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ToolRequestOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ToolRequestOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ToolRequestOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ToolRequestOptions& from) {
    ToolRequestOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ToolRequestOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ToolRequestOptions";
  }
  protected:
  explicit ToolRequestOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputFormatsFieldNumber = 2,
    kSaveToRepoFieldNumber = 3,
  };
  // string output_formats = 2;
  void clear_output_formats();
  const std::string& output_formats() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_output_formats(ArgT0&& arg0, ArgT... args);
  std::string* mutable_output_formats();
  PROTOBUF_NODISCARD std::string* release_output_formats();
  void set_allocated_output_formats(std::string* output_formats);
  private:
  const std::string& _internal_output_formats() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_output_formats(const std::string& value);
  std::string* _internal_mutable_output_formats();
  public:

  // bool save_to_repo = 3;
  void clear_save_to_repo();
  bool save_to_repo() const;
  void set_save_to_repo(bool value);
  private:
  bool _internal_save_to_repo() const;
  void _internal_set_save_to_repo(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ToolRequestOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_formats_;
    bool save_to_repo_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ProfileRequest_ToolOptionsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileRequest_ToolOptionsEntry_DoNotUse, 
    std::string, ::tensorflow::ToolRequestOptions,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ProfileRequest_ToolOptionsEntry_DoNotUse, 
    std::string, ::tensorflow::ToolRequestOptions,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  ProfileRequest_ToolOptionsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ProfileRequest_ToolOptionsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ProfileRequest_ToolOptionsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ProfileRequest_ToolOptionsEntry_DoNotUse& other);
  static const ProfileRequest_ToolOptionsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ProfileRequest_ToolOptionsEntry_DoNotUse*>(&_ProfileRequest_ToolOptionsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.ProfileRequest.ToolOptionsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};

// -------------------------------------------------------------------

class ProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileRequest) */ {
 public:
  inline ProfileRequest() : ProfileRequest(nullptr) {}
  ~ProfileRequest() override;
  explicit PROTOBUF_CONSTEXPR ProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileRequest(const ProfileRequest& from);
  ProfileRequest(ProfileRequest&& from) noexcept
    : ProfileRequest() {
    *this = ::std::move(from);
  }

  inline ProfileRequest& operator=(const ProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileRequest& operator=(ProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileRequest* internal_default_instance() {
    return reinterpret_cast<const ProfileRequest*>(
               &_ProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ProfileRequest& a, ProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileRequest& from) {
    ProfileRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileRequest";
  }
  protected:
  explicit ProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kToolsFieldNumber = 3,
    kToolOptionsFieldNumber = 8,
    kRepositoryRootFieldNumber = 5,
    kSessionIdFieldNumber = 6,
    kHostNameFieldNumber = 7,
    kOptsFieldNumber = 4,
    kDurationMsFieldNumber = 1,
    kMaxEventsFieldNumber = 2,
  };
  // repeated string tools = 3;
  int tools_size() const;
  private:
  int _internal_tools_size() const;
  public:
  void clear_tools();
  const std::string& tools(int index) const;
  std::string* mutable_tools(int index);
  void set_tools(int index, const std::string& value);
  void set_tools(int index, std::string&& value);
  void set_tools(int index, const char* value);
  void set_tools(int index, const char* value, size_t size);
  std::string* add_tools();
  void add_tools(const std::string& value);
  void add_tools(std::string&& value);
  void add_tools(const char* value);
  void add_tools(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& tools() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_tools();
  private:
  const std::string& _internal_tools(int index) const;
  std::string* _internal_add_tools();
  public:

  // map<string, .tensorflow.ToolRequestOptions> tool_options = 8;
  int tool_options_size() const;
  private:
  int _internal_tool_options_size() const;
  public:
  void clear_tool_options();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >&
      _internal_tool_options() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >*
      _internal_mutable_tool_options();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >&
      tool_options() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >*
      mutable_tool_options();

  // string repository_root = 5;
  void clear_repository_root();
  const std::string& repository_root() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_repository_root(ArgT0&& arg0, ArgT... args);
  std::string* mutable_repository_root();
  PROTOBUF_NODISCARD std::string* release_repository_root();
  void set_allocated_repository_root(std::string* repository_root);
  private:
  const std::string& _internal_repository_root() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_repository_root(const std::string& value);
  std::string* _internal_mutable_repository_root();
  public:

  // string session_id = 6;
  void clear_session_id();
  const std::string& session_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_id();
  PROTOBUF_NODISCARD std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);
  private:
  const std::string& _internal_session_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_id(const std::string& value);
  std::string* _internal_mutable_session_id();
  public:

  // string host_name = 7;
  void clear_host_name();
  const std::string& host_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_host_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_host_name();
  PROTOBUF_NODISCARD std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);
  private:
  const std::string& _internal_host_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_host_name(const std::string& value);
  std::string* _internal_mutable_host_name();
  public:

  // .tensorflow.ProfileOptions opts = 4;
  bool has_opts() const;
  private:
  bool _internal_has_opts() const;
  public:
  void clear_opts();
  const ::tensorflow::ProfileOptions& opts() const;
  PROTOBUF_NODISCARD ::tensorflow::ProfileOptions* release_opts();
  ::tensorflow::ProfileOptions* mutable_opts();
  void set_allocated_opts(::tensorflow::ProfileOptions* opts);
  private:
  const ::tensorflow::ProfileOptions& _internal_opts() const;
  ::tensorflow::ProfileOptions* _internal_mutable_opts();
  public:
  void unsafe_arena_set_allocated_opts(
      ::tensorflow::ProfileOptions* opts);
  ::tensorflow::ProfileOptions* unsafe_arena_release_opts();

  // uint64 duration_ms = 1;
  void clear_duration_ms();
  uint64_t duration_ms() const;
  void set_duration_ms(uint64_t value);
  private:
  uint64_t _internal_duration_ms() const;
  void _internal_set_duration_ms(uint64_t value);
  public:

  // uint64 max_events = 2;
  void clear_max_events();
  uint64_t max_events() const;
  void set_max_events(uint64_t value);
  private:
  uint64_t _internal_max_events() const;
  void _internal_set_max_events(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> tools_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ProfileRequest_ToolOptionsEntry_DoNotUse,
        std::string, ::tensorflow::ToolRequestOptions,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> tool_options_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_root_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
    ::tensorflow::ProfileOptions* opts_;
    uint64_t duration_ms_;
    uint64_t max_events_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ProfileToolData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileToolData) */ {
 public:
  inline ProfileToolData() : ProfileToolData(nullptr) {}
  ~ProfileToolData() override;
  explicit PROTOBUF_CONSTEXPR ProfileToolData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileToolData(const ProfileToolData& from);
  ProfileToolData(ProfileToolData&& from) noexcept
    : ProfileToolData() {
    *this = ::std::move(from);
  }

  inline ProfileToolData& operator=(const ProfileToolData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileToolData& operator=(ProfileToolData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileToolData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileToolData* internal_default_instance() {
    return reinterpret_cast<const ProfileToolData*>(
               &_ProfileToolData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ProfileToolData& a, ProfileToolData& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileToolData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileToolData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileToolData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileToolData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileToolData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileToolData& from) {
    ProfileToolData::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileToolData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileToolData";
  }
  protected:
  explicit ProfileToolData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kDataFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bytes data = 2;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileToolData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ProfileResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileResponse) */ {
 public:
  inline ProfileResponse() : ProfileResponse(nullptr) {}
  ~ProfileResponse() override;
  explicit PROTOBUF_CONSTEXPR ProfileResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileResponse(const ProfileResponse& from);
  ProfileResponse(ProfileResponse&& from) noexcept
    : ProfileResponse() {
    *this = ::std::move(from);
  }

  inline ProfileResponse& operator=(const ProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileResponse& operator=(ProfileResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileResponse* internal_default_instance() {
    return reinterpret_cast<const ProfileResponse*>(
               &_ProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ProfileResponse& a, ProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileResponse& from) {
    ProfileResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileResponse";
  }
  protected:
  explicit ProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kToolDataFieldNumber = 6,
    kEmptyTraceFieldNumber = 7,
  };
  // repeated .tensorflow.ProfileToolData tool_data = 6;
  int tool_data_size() const;
  private:
  int _internal_tool_data_size() const;
  public:
  void clear_tool_data();
  ::tensorflow::ProfileToolData* mutable_tool_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData >*
      mutable_tool_data();
  private:
  const ::tensorflow::ProfileToolData& _internal_tool_data(int index) const;
  ::tensorflow::ProfileToolData* _internal_add_tool_data();
  public:
  const ::tensorflow::ProfileToolData& tool_data(int index) const;
  ::tensorflow::ProfileToolData* add_tool_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData >&
      tool_data() const;

  // bool empty_trace = 7;
  void clear_empty_trace();
  bool empty_trace() const;
  void set_empty_trace(bool value);
  private:
  bool _internal_empty_trace() const;
  void _internal_set_empty_trace(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData > tool_data_;
    bool empty_trace_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class TerminateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TerminateRequest) */ {
 public:
  inline TerminateRequest() : TerminateRequest(nullptr) {}
  ~TerminateRequest() override;
  explicit PROTOBUF_CONSTEXPR TerminateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TerminateRequest(const TerminateRequest& from);
  TerminateRequest(TerminateRequest&& from) noexcept
    : TerminateRequest() {
    *this = ::std::move(from);
  }

  inline TerminateRequest& operator=(const TerminateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TerminateRequest& operator=(TerminateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TerminateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const TerminateRequest* internal_default_instance() {
    return reinterpret_cast<const TerminateRequest*>(
               &_TerminateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(TerminateRequest& a, TerminateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TerminateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TerminateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TerminateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TerminateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TerminateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TerminateRequest& from) {
    TerminateRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TerminateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TerminateRequest";
  }
  protected:
  explicit TerminateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionIdFieldNumber = 1,
  };
  // string session_id = 1;
  void clear_session_id();
  const std::string& session_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_id();
  PROTOBUF_NODISCARD std::string* release_session_id();
  void set_allocated_session_id(std::string* session_id);
  private:
  const std::string& _internal_session_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_id(const std::string& value);
  std::string* _internal_mutable_session_id();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TerminateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class TerminateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.TerminateResponse) */ {
 public:
  inline TerminateResponse() : TerminateResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR TerminateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TerminateResponse(const TerminateResponse& from);
  TerminateResponse(TerminateResponse&& from) noexcept
    : TerminateResponse() {
    *this = ::std::move(from);
  }

  inline TerminateResponse& operator=(const TerminateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TerminateResponse& operator=(TerminateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TerminateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const TerminateResponse* internal_default_instance() {
    return reinterpret_cast<const TerminateResponse*>(
               &_TerminateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(TerminateResponse& a, TerminateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TerminateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TerminateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TerminateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TerminateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const TerminateResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const TerminateResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TerminateResponse";
  }
  protected:
  explicit TerminateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.TerminateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class MonitorRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MonitorRequest) */ {
 public:
  inline MonitorRequest() : MonitorRequest(nullptr) {}
  ~MonitorRequest() override;
  explicit PROTOBUF_CONSTEXPR MonitorRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MonitorRequest(const MonitorRequest& from);
  MonitorRequest(MonitorRequest&& from) noexcept
    : MonitorRequest() {
    *this = ::std::move(from);
  }

  inline MonitorRequest& operator=(const MonitorRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MonitorRequest& operator=(MonitorRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MonitorRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MonitorRequest* internal_default_instance() {
    return reinterpret_cast<const MonitorRequest*>(
               &_MonitorRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(MonitorRequest& a, MonitorRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MonitorRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MonitorRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MonitorRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MonitorRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MonitorRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MonitorRequest& from) {
    MonitorRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MonitorRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MonitorRequest";
  }
  protected:
  explicit MonitorRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDurationMsFieldNumber = 1,
    kMonitoringLevelFieldNumber = 2,
    kTimestampFieldNumber = 3,
  };
  // uint64 duration_ms = 1;
  void clear_duration_ms();
  uint64_t duration_ms() const;
  void set_duration_ms(uint64_t value);
  private:
  uint64_t _internal_duration_ms() const;
  void _internal_set_duration_ms(uint64_t value);
  public:

  // int32 monitoring_level = 2;
  void clear_monitoring_level();
  int32_t monitoring_level() const;
  void set_monitoring_level(int32_t value);
  private:
  int32_t _internal_monitoring_level() const;
  void _internal_set_monitoring_level(int32_t value);
  public:

  // bool timestamp = 3;
  void clear_timestamp();
  bool timestamp() const;
  void set_timestamp(bool value);
  private:
  bool _internal_timestamp() const;
  void _internal_set_timestamp(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MonitorRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t duration_ms_;
    int32_t monitoring_level_;
    bool timestamp_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};
// -------------------------------------------------------------------

class MonitorResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MonitorResponse) */ {
 public:
  inline MonitorResponse() : MonitorResponse(nullptr) {}
  ~MonitorResponse() override;
  explicit PROTOBUF_CONSTEXPR MonitorResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MonitorResponse(const MonitorResponse& from);
  MonitorResponse(MonitorResponse&& from) noexcept
    : MonitorResponse() {
    *this = ::std::move(from);
  }

  inline MonitorResponse& operator=(const MonitorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MonitorResponse& operator=(MonitorResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MonitorResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const MonitorResponse* internal_default_instance() {
    return reinterpret_cast<const MonitorResponse*>(
               &_MonitorResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(MonitorResponse& a, MonitorResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MonitorResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MonitorResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MonitorResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MonitorResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MonitorResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MonitorResponse& from) {
    MonitorResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MonitorResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MonitorResponse";
  }
  protected:
  explicit MonitorResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kMonitorResultFieldNumber = 10,
  };
  // string data = 1;
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // .tensorflow.ProfilerServiceMonitorResult monitor_result = 10;
  bool has_monitor_result() const;
  private:
  bool _internal_has_monitor_result() const;
  public:
  void clear_monitor_result();
  const ::tensorflow::ProfilerServiceMonitorResult& monitor_result() const;
  PROTOBUF_NODISCARD ::tensorflow::ProfilerServiceMonitorResult* release_monitor_result();
  ::tensorflow::ProfilerServiceMonitorResult* mutable_monitor_result();
  void set_allocated_monitor_result(::tensorflow::ProfilerServiceMonitorResult* monitor_result);
  private:
  const ::tensorflow::ProfilerServiceMonitorResult& _internal_monitor_result() const;
  ::tensorflow::ProfilerServiceMonitorResult* _internal_mutable_monitor_result();
  public:
  void unsafe_arena_set_allocated_monitor_result(
      ::tensorflow::ProfilerServiceMonitorResult* monitor_result);
  ::tensorflow::ProfilerServiceMonitorResult* unsafe_arena_release_monitor_result();

  // @@protoc_insertion_point(class_scope:tensorflow.MonitorResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
    ::tensorflow::ProfilerServiceMonitorResult* monitor_result_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ToolRequestOptions

// string output_formats = 2;
inline void ToolRequestOptions::clear_output_formats() {
  _impl_.output_formats_.ClearToEmpty();
}
inline const std::string& ToolRequestOptions::output_formats() const {
  // @@protoc_insertion_point(field_get:tensorflow.ToolRequestOptions.output_formats)
  return _internal_output_formats();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ToolRequestOptions::set_output_formats(ArgT0&& arg0, ArgT... args) {
 
 _impl_.output_formats_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ToolRequestOptions.output_formats)
}
inline std::string* ToolRequestOptions::mutable_output_formats() {
  std::string* _s = _internal_mutable_output_formats();
  // @@protoc_insertion_point(field_mutable:tensorflow.ToolRequestOptions.output_formats)
  return _s;
}
inline const std::string& ToolRequestOptions::_internal_output_formats() const {
  return _impl_.output_formats_.Get();
}
inline void ToolRequestOptions::_internal_set_output_formats(const std::string& value) {
  
  _impl_.output_formats_.Set(value, GetArenaForAllocation());
}
inline std::string* ToolRequestOptions::_internal_mutable_output_formats() {
  
  return _impl_.output_formats_.Mutable(GetArenaForAllocation());
}
inline std::string* ToolRequestOptions::release_output_formats() {
  // @@protoc_insertion_point(field_release:tensorflow.ToolRequestOptions.output_formats)
  return _impl_.output_formats_.Release();
}
inline void ToolRequestOptions::set_allocated_output_formats(std::string* output_formats) {
  if (output_formats != nullptr) {
    
  } else {
    
  }
  _impl_.output_formats_.SetAllocated(output_formats, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.output_formats_.IsDefault()) {
    _impl_.output_formats_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ToolRequestOptions.output_formats)
}

// bool save_to_repo = 3;
inline void ToolRequestOptions::clear_save_to_repo() {
  _impl_.save_to_repo_ = false;
}
inline bool ToolRequestOptions::_internal_save_to_repo() const {
  return _impl_.save_to_repo_;
}
inline bool ToolRequestOptions::save_to_repo() const {
  // @@protoc_insertion_point(field_get:tensorflow.ToolRequestOptions.save_to_repo)
  return _internal_save_to_repo();
}
inline void ToolRequestOptions::_internal_set_save_to_repo(bool value) {
  
  _impl_.save_to_repo_ = value;
}
inline void ToolRequestOptions::set_save_to_repo(bool value) {
  _internal_set_save_to_repo(value);
  // @@protoc_insertion_point(field_set:tensorflow.ToolRequestOptions.save_to_repo)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// ProfileRequest

// uint64 duration_ms = 1;
inline void ProfileRequest::clear_duration_ms() {
  _impl_.duration_ms_ = uint64_t{0u};
}
inline uint64_t ProfileRequest::_internal_duration_ms() const {
  return _impl_.duration_ms_;
}
inline uint64_t ProfileRequest::duration_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.duration_ms)
  return _internal_duration_ms();
}
inline void ProfileRequest::_internal_set_duration_ms(uint64_t value) {
  
  _impl_.duration_ms_ = value;
}
inline void ProfileRequest::set_duration_ms(uint64_t value) {
  _internal_set_duration_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.duration_ms)
}

// uint64 max_events = 2;
inline void ProfileRequest::clear_max_events() {
  _impl_.max_events_ = uint64_t{0u};
}
inline uint64_t ProfileRequest::_internal_max_events() const {
  return _impl_.max_events_;
}
inline uint64_t ProfileRequest::max_events() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.max_events)
  return _internal_max_events();
}
inline void ProfileRequest::_internal_set_max_events(uint64_t value) {
  
  _impl_.max_events_ = value;
}
inline void ProfileRequest::set_max_events(uint64_t value) {
  _internal_set_max_events(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.max_events)
}

// repeated string tools = 3;
inline int ProfileRequest::_internal_tools_size() const {
  return _impl_.tools_.size();
}
inline int ProfileRequest::tools_size() const {
  return _internal_tools_size();
}
inline void ProfileRequest::clear_tools() {
  _impl_.tools_.Clear();
}
inline std::string* ProfileRequest::add_tools() {
  std::string* _s = _internal_add_tools();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.ProfileRequest.tools)
  return _s;
}
inline const std::string& ProfileRequest::_internal_tools(int index) const {
  return _impl_.tools_.Get(index);
}
inline const std::string& ProfileRequest::tools(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.tools)
  return _internal_tools(index);
}
inline std::string* ProfileRequest::mutable_tools(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.tools)
  return _impl_.tools_.Mutable(index);
}
inline void ProfileRequest::set_tools(int index, const std::string& value) {
  _impl_.tools_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::set_tools(int index, std::string&& value) {
  _impl_.tools_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::set_tools(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tools_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::set_tools(int index, const char* value, size_t size) {
  _impl_.tools_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.ProfileRequest.tools)
}
inline std::string* ProfileRequest::_internal_add_tools() {
  return _impl_.tools_.Add();
}
inline void ProfileRequest::add_tools(const std::string& value) {
  _impl_.tools_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::add_tools(std::string&& value) {
  _impl_.tools_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::add_tools(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tools_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.ProfileRequest.tools)
}
inline void ProfileRequest::add_tools(const char* value, size_t size) {
  _impl_.tools_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.ProfileRequest.tools)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ProfileRequest::tools() const {
  // @@protoc_insertion_point(field_list:tensorflow.ProfileRequest.tools)
  return _impl_.tools_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ProfileRequest::mutable_tools() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ProfileRequest.tools)
  return &_impl_.tools_;
}

// map<string, .tensorflow.ToolRequestOptions> tool_options = 8;
inline int ProfileRequest::_internal_tool_options_size() const {
  return _impl_.tool_options_.size();
}
inline int ProfileRequest::tool_options_size() const {
  return _internal_tool_options_size();
}
inline void ProfileRequest::clear_tool_options() {
  _impl_.tool_options_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >&
ProfileRequest::_internal_tool_options() const {
  return _impl_.tool_options_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >&
ProfileRequest::tool_options() const {
  // @@protoc_insertion_point(field_map:tensorflow.ProfileRequest.tool_options)
  return _internal_tool_options();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >*
ProfileRequest::_internal_mutable_tool_options() {
  return _impl_.tool_options_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::ToolRequestOptions >*
ProfileRequest::mutable_tool_options() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.ProfileRequest.tool_options)
  return _internal_mutable_tool_options();
}

// .tensorflow.ProfileOptions opts = 4;
inline bool ProfileRequest::_internal_has_opts() const {
  return this != internal_default_instance() && _impl_.opts_ != nullptr;
}
inline bool ProfileRequest::has_opts() const {
  return _internal_has_opts();
}
inline const ::tensorflow::ProfileOptions& ProfileRequest::_internal_opts() const {
  const ::tensorflow::ProfileOptions* p = _impl_.opts_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ProfileOptions&>(
      ::tensorflow::_ProfileOptions_default_instance_);
}
inline const ::tensorflow::ProfileOptions& ProfileRequest::opts() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.opts)
  return _internal_opts();
}
inline void ProfileRequest::unsafe_arena_set_allocated_opts(
    ::tensorflow::ProfileOptions* opts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.opts_);
  }
  _impl_.opts_ = opts;
  if (opts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ProfileRequest.opts)
}
inline ::tensorflow::ProfileOptions* ProfileRequest::release_opts() {
  
  ::tensorflow::ProfileOptions* temp = _impl_.opts_;
  _impl_.opts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ProfileOptions* ProfileRequest::unsafe_arena_release_opts() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileRequest.opts)
  
  ::tensorflow::ProfileOptions* temp = _impl_.opts_;
  _impl_.opts_ = nullptr;
  return temp;
}
inline ::tensorflow::ProfileOptions* ProfileRequest::_internal_mutable_opts() {
  
  if (_impl_.opts_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ProfileOptions>(GetArenaForAllocation());
    _impl_.opts_ = p;
  }
  return _impl_.opts_;
}
inline ::tensorflow::ProfileOptions* ProfileRequest::mutable_opts() {
  ::tensorflow::ProfileOptions* _msg = _internal_mutable_opts();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.opts)
  return _msg;
}
inline void ProfileRequest::set_allocated_opts(::tensorflow::ProfileOptions* opts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.opts_);
  }
  if (opts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(opts));
    if (message_arena != submessage_arena) {
      opts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, opts, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.opts_ = opts;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileRequest.opts)
}

// string repository_root = 5;
inline void ProfileRequest::clear_repository_root() {
  _impl_.repository_root_.ClearToEmpty();
}
inline const std::string& ProfileRequest::repository_root() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.repository_root)
  return _internal_repository_root();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileRequest::set_repository_root(ArgT0&& arg0, ArgT... args) {
 
 _impl_.repository_root_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.repository_root)
}
inline std::string* ProfileRequest::mutable_repository_root() {
  std::string* _s = _internal_mutable_repository_root();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.repository_root)
  return _s;
}
inline const std::string& ProfileRequest::_internal_repository_root() const {
  return _impl_.repository_root_.Get();
}
inline void ProfileRequest::_internal_set_repository_root(const std::string& value) {
  
  _impl_.repository_root_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileRequest::_internal_mutable_repository_root() {
  
  return _impl_.repository_root_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileRequest::release_repository_root() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileRequest.repository_root)
  return _impl_.repository_root_.Release();
}
inline void ProfileRequest::set_allocated_repository_root(std::string* repository_root) {
  if (repository_root != nullptr) {
    
  } else {
    
  }
  _impl_.repository_root_.SetAllocated(repository_root, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.repository_root_.IsDefault()) {
    _impl_.repository_root_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileRequest.repository_root)
}

// string session_id = 6;
inline void ProfileRequest::clear_session_id() {
  _impl_.session_id_.ClearToEmpty();
}
inline const std::string& ProfileRequest::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.session_id)
  return _internal_session_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileRequest::set_session_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.session_id)
}
inline std::string* ProfileRequest::mutable_session_id() {
  std::string* _s = _internal_mutable_session_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.session_id)
  return _s;
}
inline const std::string& ProfileRequest::_internal_session_id() const {
  return _impl_.session_id_.Get();
}
inline void ProfileRequest::_internal_set_session_id(const std::string& value) {
  
  _impl_.session_id_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileRequest::_internal_mutable_session_id() {
  
  return _impl_.session_id_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileRequest::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileRequest.session_id)
  return _impl_.session_id_.Release();
}
inline void ProfileRequest::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  _impl_.session_id_.SetAllocated(session_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_id_.IsDefault()) {
    _impl_.session_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileRequest.session_id)
}

// string host_name = 7;
inline void ProfileRequest::clear_host_name() {
  _impl_.host_name_.ClearToEmpty();
}
inline const std::string& ProfileRequest::host_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileRequest.host_name)
  return _internal_host_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileRequest::set_host_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.host_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileRequest.host_name)
}
inline std::string* ProfileRequest::mutable_host_name() {
  std::string* _s = _internal_mutable_host_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileRequest.host_name)
  return _s;
}
inline const std::string& ProfileRequest::_internal_host_name() const {
  return _impl_.host_name_.Get();
}
inline void ProfileRequest::_internal_set_host_name(const std::string& value) {
  
  _impl_.host_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileRequest::_internal_mutable_host_name() {
  
  return _impl_.host_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileRequest::release_host_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileRequest.host_name)
  return _impl_.host_name_.Release();
}
inline void ProfileRequest::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  _impl_.host_name_.SetAllocated(host_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.host_name_.IsDefault()) {
    _impl_.host_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileRequest.host_name)
}

// -------------------------------------------------------------------

// ProfileToolData

// string name = 1;
inline void ProfileToolData::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ProfileToolData::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileToolData.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileToolData::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileToolData.name)
}
inline std::string* ProfileToolData::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileToolData.name)
  return _s;
}
inline const std::string& ProfileToolData::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ProfileToolData::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileToolData::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileToolData::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileToolData.name)
  return _impl_.name_.Release();
}
inline void ProfileToolData::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileToolData.name)
}

// bytes data = 2;
inline void ProfileToolData::clear_data() {
  _impl_.data_.ClearToEmpty();
}
inline const std::string& ProfileToolData::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileToolData.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileToolData::set_data(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileToolData.data)
}
inline std::string* ProfileToolData::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileToolData.data)
  return _s;
}
inline const std::string& ProfileToolData::_internal_data() const {
  return _impl_.data_.Get();
}
inline void ProfileToolData::_internal_set_data(const std::string& value) {
  
  _impl_.data_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileToolData::_internal_mutable_data() {
  
  return _impl_.data_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileToolData::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileToolData.data)
  return _impl_.data_.Release();
}
inline void ProfileToolData::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  _impl_.data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_.IsDefault()) {
    _impl_.data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileToolData.data)
}

// -------------------------------------------------------------------

// ProfileResponse

// repeated .tensorflow.ProfileToolData tool_data = 6;
inline int ProfileResponse::_internal_tool_data_size() const {
  return _impl_.tool_data_.size();
}
inline int ProfileResponse::tool_data_size() const {
  return _internal_tool_data_size();
}
inline void ProfileResponse::clear_tool_data() {
  _impl_.tool_data_.Clear();
}
inline ::tensorflow::ProfileToolData* ProfileResponse::mutable_tool_data(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileResponse.tool_data)
  return _impl_.tool_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData >*
ProfileResponse::mutable_tool_data() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ProfileResponse.tool_data)
  return &_impl_.tool_data_;
}
inline const ::tensorflow::ProfileToolData& ProfileResponse::_internal_tool_data(int index) const {
  return _impl_.tool_data_.Get(index);
}
inline const ::tensorflow::ProfileToolData& ProfileResponse::tool_data(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileResponse.tool_data)
  return _internal_tool_data(index);
}
inline ::tensorflow::ProfileToolData* ProfileResponse::_internal_add_tool_data() {
  return _impl_.tool_data_.Add();
}
inline ::tensorflow::ProfileToolData* ProfileResponse::add_tool_data() {
  ::tensorflow::ProfileToolData* _add = _internal_add_tool_data();
  // @@protoc_insertion_point(field_add:tensorflow.ProfileResponse.tool_data)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ProfileToolData >&
ProfileResponse::tool_data() const {
  // @@protoc_insertion_point(field_list:tensorflow.ProfileResponse.tool_data)
  return _impl_.tool_data_;
}

// bool empty_trace = 7;
inline void ProfileResponse::clear_empty_trace() {
  _impl_.empty_trace_ = false;
}
inline bool ProfileResponse::_internal_empty_trace() const {
  return _impl_.empty_trace_;
}
inline bool ProfileResponse::empty_trace() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileResponse.empty_trace)
  return _internal_empty_trace();
}
inline void ProfileResponse::_internal_set_empty_trace(bool value) {
  
  _impl_.empty_trace_ = value;
}
inline void ProfileResponse::set_empty_trace(bool value) {
  _internal_set_empty_trace(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileResponse.empty_trace)
}

// -------------------------------------------------------------------

// TerminateRequest

// string session_id = 1;
inline void TerminateRequest::clear_session_id() {
  _impl_.session_id_.ClearToEmpty();
}
inline const std::string& TerminateRequest::session_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.TerminateRequest.session_id)
  return _internal_session_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TerminateRequest::set_session_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TerminateRequest.session_id)
}
inline std::string* TerminateRequest::mutable_session_id() {
  std::string* _s = _internal_mutable_session_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.TerminateRequest.session_id)
  return _s;
}
inline const std::string& TerminateRequest::_internal_session_id() const {
  return _impl_.session_id_.Get();
}
inline void TerminateRequest::_internal_set_session_id(const std::string& value) {
  
  _impl_.session_id_.Set(value, GetArenaForAllocation());
}
inline std::string* TerminateRequest::_internal_mutable_session_id() {
  
  return _impl_.session_id_.Mutable(GetArenaForAllocation());
}
inline std::string* TerminateRequest::release_session_id() {
  // @@protoc_insertion_point(field_release:tensorflow.TerminateRequest.session_id)
  return _impl_.session_id_.Release();
}
inline void TerminateRequest::set_allocated_session_id(std::string* session_id) {
  if (session_id != nullptr) {
    
  } else {
    
  }
  _impl_.session_id_.SetAllocated(session_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_id_.IsDefault()) {
    _impl_.session_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TerminateRequest.session_id)
}

// -------------------------------------------------------------------

// TerminateResponse

// -------------------------------------------------------------------

// MonitorRequest

// uint64 duration_ms = 1;
inline void MonitorRequest::clear_duration_ms() {
  _impl_.duration_ms_ = uint64_t{0u};
}
inline uint64_t MonitorRequest::_internal_duration_ms() const {
  return _impl_.duration_ms_;
}
inline uint64_t MonitorRequest::duration_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorRequest.duration_ms)
  return _internal_duration_ms();
}
inline void MonitorRequest::_internal_set_duration_ms(uint64_t value) {
  
  _impl_.duration_ms_ = value;
}
inline void MonitorRequest::set_duration_ms(uint64_t value) {
  _internal_set_duration_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.MonitorRequest.duration_ms)
}

// int32 monitoring_level = 2;
inline void MonitorRequest::clear_monitoring_level() {
  _impl_.monitoring_level_ = 0;
}
inline int32_t MonitorRequest::_internal_monitoring_level() const {
  return _impl_.monitoring_level_;
}
inline int32_t MonitorRequest::monitoring_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorRequest.monitoring_level)
  return _internal_monitoring_level();
}
inline void MonitorRequest::_internal_set_monitoring_level(int32_t value) {
  
  _impl_.monitoring_level_ = value;
}
inline void MonitorRequest::set_monitoring_level(int32_t value) {
  _internal_set_monitoring_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.MonitorRequest.monitoring_level)
}

// bool timestamp = 3;
inline void MonitorRequest::clear_timestamp() {
  _impl_.timestamp_ = false;
}
inline bool MonitorRequest::_internal_timestamp() const {
  return _impl_.timestamp_;
}
inline bool MonitorRequest::timestamp() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorRequest.timestamp)
  return _internal_timestamp();
}
inline void MonitorRequest::_internal_set_timestamp(bool value) {
  
  _impl_.timestamp_ = value;
}
inline void MonitorRequest::set_timestamp(bool value) {
  _internal_set_timestamp(value);
  // @@protoc_insertion_point(field_set:tensorflow.MonitorRequest.timestamp)
}

// -------------------------------------------------------------------

// MonitorResponse

// string data = 1;
inline void MonitorResponse::clear_data() {
  _impl_.data_.ClearToEmpty();
}
inline const std::string& MonitorResponse::data() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorResponse.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MonitorResponse::set_data(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MonitorResponse.data)
}
inline std::string* MonitorResponse::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:tensorflow.MonitorResponse.data)
  return _s;
}
inline const std::string& MonitorResponse::_internal_data() const {
  return _impl_.data_.Get();
}
inline void MonitorResponse::_internal_set_data(const std::string& value) {
  
  _impl_.data_.Set(value, GetArenaForAllocation());
}
inline std::string* MonitorResponse::_internal_mutable_data() {
  
  return _impl_.data_.Mutable(GetArenaForAllocation());
}
inline std::string* MonitorResponse::release_data() {
  // @@protoc_insertion_point(field_release:tensorflow.MonitorResponse.data)
  return _impl_.data_.Release();
}
inline void MonitorResponse::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    
  } else {
    
  }
  _impl_.data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_.IsDefault()) {
    _impl_.data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MonitorResponse.data)
}

// .tensorflow.ProfilerServiceMonitorResult monitor_result = 10;
inline bool MonitorResponse::_internal_has_monitor_result() const {
  return this != internal_default_instance() && _impl_.monitor_result_ != nullptr;
}
inline bool MonitorResponse::has_monitor_result() const {
  return _internal_has_monitor_result();
}
inline const ::tensorflow::ProfilerServiceMonitorResult& MonitorResponse::_internal_monitor_result() const {
  const ::tensorflow::ProfilerServiceMonitorResult* p = _impl_.monitor_result_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ProfilerServiceMonitorResult&>(
      ::tensorflow::_ProfilerServiceMonitorResult_default_instance_);
}
inline const ::tensorflow::ProfilerServiceMonitorResult& MonitorResponse::monitor_result() const {
  // @@protoc_insertion_point(field_get:tensorflow.MonitorResponse.monitor_result)
  return _internal_monitor_result();
}
inline void MonitorResponse::unsafe_arena_set_allocated_monitor_result(
    ::tensorflow::ProfilerServiceMonitorResult* monitor_result) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.monitor_result_);
  }
  _impl_.monitor_result_ = monitor_result;
  if (monitor_result) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MonitorResponse.monitor_result)
}
inline ::tensorflow::ProfilerServiceMonitorResult* MonitorResponse::release_monitor_result() {
  
  ::tensorflow::ProfilerServiceMonitorResult* temp = _impl_.monitor_result_;
  _impl_.monitor_result_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ProfilerServiceMonitorResult* MonitorResponse::unsafe_arena_release_monitor_result() {
  // @@protoc_insertion_point(field_release:tensorflow.MonitorResponse.monitor_result)
  
  ::tensorflow::ProfilerServiceMonitorResult* temp = _impl_.monitor_result_;
  _impl_.monitor_result_ = nullptr;
  return temp;
}
inline ::tensorflow::ProfilerServiceMonitorResult* MonitorResponse::_internal_mutable_monitor_result() {
  
  if (_impl_.monitor_result_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ProfilerServiceMonitorResult>(GetArenaForAllocation());
    _impl_.monitor_result_ = p;
  }
  return _impl_.monitor_result_;
}
inline ::tensorflow::ProfilerServiceMonitorResult* MonitorResponse::mutable_monitor_result() {
  ::tensorflow::ProfilerServiceMonitorResult* _msg = _internal_mutable_monitor_result();
  // @@protoc_insertion_point(field_mutable:tensorflow.MonitorResponse.monitor_result)
  return _msg;
}
inline void MonitorResponse::set_allocated_monitor_result(::tensorflow::ProfilerServiceMonitorResult* monitor_result) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.monitor_result_);
  }
  if (monitor_result) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(monitor_result));
    if (message_arena != submessage_arena) {
      monitor_result = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, monitor_result, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.monitor_result_ = monitor_result;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MonitorResponse.monitor_result)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5fservice_2eproto
