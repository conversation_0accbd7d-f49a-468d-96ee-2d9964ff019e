.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_policies_set" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_policies_set \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_policies_set(gnutls_x509_policies_t " policies ", const struct gnutls_x509_policy_st * " policy ");"
.SH ARGUMENTS
.IP "gnutls_x509_policies_t policies" 12
An initialized policies
.IP "const struct gnutls_x509_policy_st * policy" 12
Contains the policy to set
.SH "DESCRIPTION"
This function will store the specified policy in
the provided  \fIpolicies\fP .
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0), otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
