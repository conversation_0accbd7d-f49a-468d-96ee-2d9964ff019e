%FILES%
mingw64/
mingw64/bin/
mingw64/bin/libjsoncpp-26.dll
mingw64/include/
mingw64/include/json/
mingw64/include/json/allocator.h
mingw64/include/json/assertions.h
mingw64/include/json/config.h
mingw64/include/json/forwards.h
mingw64/include/json/json.h
mingw64/include/json/json_features.h
mingw64/include/json/reader.h
mingw64/include/json/value.h
mingw64/include/json/version.h
mingw64/include/json/writer.h
mingw64/lib/
mingw64/lib/cmake/
mingw64/lib/cmake/jsoncpp/
mingw64/lib/cmake/jsoncpp/jsoncpp-namespaced-targets.cmake
mingw64/lib/cmake/jsoncpp/jsoncpp-targets-release.cmake
mingw64/lib/cmake/jsoncpp/jsoncpp-targets.cmake
mingw64/lib/cmake/jsoncpp/jsoncppConfig.cmake
mingw64/lib/cmake/jsoncpp/jsoncppConfigVersion.cmake
mingw64/lib/libjsoncpp.a
mingw64/lib/libjsoncpp.dll.a
mingw64/lib/pkgconfig/
mingw64/lib/pkgconfig/jsoncpp.pc
mingw64/share/
mingw64/share/licenses/
mingw64/share/licenses/jsoncpp/
mingw64/share/licenses/jsoncpp/LICENSE

