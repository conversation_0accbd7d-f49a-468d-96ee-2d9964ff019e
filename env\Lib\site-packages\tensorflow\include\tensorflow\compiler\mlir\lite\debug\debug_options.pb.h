// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/lite/debug/debug_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2flite_2fdebug_2fdebug_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2flite_2fdebug_2fdebug_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2flite_2fdebug_2fdebug_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fdebug_2fdebug_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2flite_2fdebug_2fdebug_5foptions_2eproto;
namespace tensorflow {
namespace converter {
class DebugOptions;
struct DebugOptionsDefaultTypeInternal;
extern DebugOptionsDefaultTypeInternal _DebugOptions_default_instance_;
}  // namespace converter
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::converter::DebugOptions* Arena::CreateMaybeMessage<::tensorflow::converter::DebugOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace converter {

// ===================================================================

class DebugOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.converter.DebugOptions) */ {
 public:
  inline DebugOptions() : DebugOptions(nullptr) {}
  ~DebugOptions() override;
  explicit PROTOBUF_CONSTEXPR DebugOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebugOptions(const DebugOptions& from);
  DebugOptions(DebugOptions&& from) noexcept
    : DebugOptions() {
    *this = ::std::move(from);
  }

  inline DebugOptions& operator=(const DebugOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugOptions& operator=(DebugOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebugOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebugOptions* internal_default_instance() {
    return reinterpret_cast<const DebugOptions*>(
               &_DebugOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DebugOptions& a, DebugOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebugOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebugOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebugOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebugOptions& from) {
    DebugOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.converter.DebugOptions";
  }
  protected:
  explicit DebugOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIrDumpDirFieldNumber = 1,
    kIrDumpPassRegexFieldNumber = 2,
    kIrDumpFuncRegexFieldNumber = 3,
    kPrintIrBeforeFieldNumber = 5,
    kPrintIrAfterFieldNumber = 6,
    kElideElementsattrsIfLargerFieldNumber = 8,
    kEnableTimingFieldNumber = 4,
    kPrintIrModuleScopeFieldNumber = 7,
  };
  // optional string ir_dump_dir = 1 [default = ""];
  bool has_ir_dump_dir() const;
  private:
  bool _internal_has_ir_dump_dir() const;
  public:
  void clear_ir_dump_dir();
  const std::string& ir_dump_dir() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ir_dump_dir(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ir_dump_dir();
  PROTOBUF_NODISCARD std::string* release_ir_dump_dir();
  void set_allocated_ir_dump_dir(std::string* ir_dump_dir);
  private:
  const std::string& _internal_ir_dump_dir() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ir_dump_dir(const std::string& value);
  std::string* _internal_mutable_ir_dump_dir();
  public:

  // optional string ir_dump_pass_regex = 2 [default = ".*"];
  bool has_ir_dump_pass_regex() const;
  private:
  bool _internal_has_ir_dump_pass_regex() const;
  public:
  void clear_ir_dump_pass_regex();
  const std::string& ir_dump_pass_regex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ir_dump_pass_regex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ir_dump_pass_regex();
  PROTOBUF_NODISCARD std::string* release_ir_dump_pass_regex();
  void set_allocated_ir_dump_pass_regex(std::string* ir_dump_pass_regex);
  private:
  const std::string& _internal_ir_dump_pass_regex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ir_dump_pass_regex(const std::string& value);
  std::string* _internal_mutable_ir_dump_pass_regex();
  public:

  // optional string ir_dump_func_regex = 3 [default = ".*"];
  bool has_ir_dump_func_regex() const;
  private:
  bool _internal_has_ir_dump_func_regex() const;
  public:
  void clear_ir_dump_func_regex();
  const std::string& ir_dump_func_regex() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_ir_dump_func_regex(ArgT0&& arg0, ArgT... args);
  std::string* mutable_ir_dump_func_regex();
  PROTOBUF_NODISCARD std::string* release_ir_dump_func_regex();
  void set_allocated_ir_dump_func_regex(std::string* ir_dump_func_regex);
  private:
  const std::string& _internal_ir_dump_func_regex() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_ir_dump_func_regex(const std::string& value);
  std::string* _internal_mutable_ir_dump_func_regex();
  public:

  // optional string print_ir_before = 5 [default = ""];
  bool has_print_ir_before() const;
  private:
  bool _internal_has_print_ir_before() const;
  public:
  void clear_print_ir_before();
  const std::string& print_ir_before() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_print_ir_before(ArgT0&& arg0, ArgT... args);
  std::string* mutable_print_ir_before();
  PROTOBUF_NODISCARD std::string* release_print_ir_before();
  void set_allocated_print_ir_before(std::string* print_ir_before);
  private:
  const std::string& _internal_print_ir_before() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_print_ir_before(const std::string& value);
  std::string* _internal_mutable_print_ir_before();
  public:

  // optional string print_ir_after = 6 [default = ""];
  bool has_print_ir_after() const;
  private:
  bool _internal_has_print_ir_after() const;
  public:
  void clear_print_ir_after();
  const std::string& print_ir_after() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_print_ir_after(ArgT0&& arg0, ArgT... args);
  std::string* mutable_print_ir_after();
  PROTOBUF_NODISCARD std::string* release_print_ir_after();
  void set_allocated_print_ir_after(std::string* print_ir_after);
  private:
  const std::string& _internal_print_ir_after() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_print_ir_after(const std::string& value);
  std::string* _internal_mutable_print_ir_after();
  public:

  // optional int64 elide_elementsattrs_if_larger = 8;
  bool has_elide_elementsattrs_if_larger() const;
  private:
  bool _internal_has_elide_elementsattrs_if_larger() const;
  public:
  void clear_elide_elementsattrs_if_larger();
  int64_t elide_elementsattrs_if_larger() const;
  void set_elide_elementsattrs_if_larger(int64_t value);
  private:
  int64_t _internal_elide_elementsattrs_if_larger() const;
  void _internal_set_elide_elementsattrs_if_larger(int64_t value);
  public:

  // optional bool enable_timing = 4 [default = false];
  bool has_enable_timing() const;
  private:
  bool _internal_has_enable_timing() const;
  public:
  void clear_enable_timing();
  bool enable_timing() const;
  void set_enable_timing(bool value);
  private:
  bool _internal_enable_timing() const;
  void _internal_set_enable_timing(bool value);
  public:

  // optional bool print_ir_module_scope = 7 [default = true];
  bool has_print_ir_module_scope() const;
  private:
  bool _internal_has_print_ir_module_scope() const;
  public:
  void clear_print_ir_module_scope();
  bool print_ir_module_scope() const;
  void set_print_ir_module_scope(bool value);
  private:
  bool _internal_print_ir_module_scope() const;
  void _internal_set_print_ir_module_scope(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.converter.DebugOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ir_dump_dir_;
    static const ::PROTOBUF_NAMESPACE_ID::internal::LazyString _i_give_permission_to_break_this_code_default_ir_dump_pass_regex_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ir_dump_pass_regex_;
    static const ::PROTOBUF_NAMESPACE_ID::internal::LazyString _i_give_permission_to_break_this_code_default_ir_dump_func_regex_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr ir_dump_func_regex_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr print_ir_before_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr print_ir_after_;
    int64_t elide_elementsattrs_if_larger_;
    bool enable_timing_;
    bool print_ir_module_scope_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fdebug_2fdebug_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebugOptions

// optional string ir_dump_dir = 1 [default = ""];
inline bool DebugOptions::_internal_has_ir_dump_dir() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool DebugOptions::has_ir_dump_dir() const {
  return _internal_has_ir_dump_dir();
}
inline void DebugOptions::clear_ir_dump_dir() {
  _impl_.ir_dump_dir_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& DebugOptions::ir_dump_dir() const {
  // @@protoc_insertion_point(field_get:tensorflow.converter.DebugOptions.ir_dump_dir)
  return _internal_ir_dump_dir();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugOptions::set_ir_dump_dir(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.ir_dump_dir_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.converter.DebugOptions.ir_dump_dir)
}
inline std::string* DebugOptions::mutable_ir_dump_dir() {
  std::string* _s = _internal_mutable_ir_dump_dir();
  // @@protoc_insertion_point(field_mutable:tensorflow.converter.DebugOptions.ir_dump_dir)
  return _s;
}
inline const std::string& DebugOptions::_internal_ir_dump_dir() const {
  return _impl_.ir_dump_dir_.Get();
}
inline void DebugOptions::_internal_set_ir_dump_dir(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.ir_dump_dir_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugOptions::_internal_mutable_ir_dump_dir() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.ir_dump_dir_.Mutable(GetArenaForAllocation());
}
inline std::string* DebugOptions::release_ir_dump_dir() {
  // @@protoc_insertion_point(field_release:tensorflow.converter.DebugOptions.ir_dump_dir)
  if (!_internal_has_ir_dump_dir()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.ir_dump_dir_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.ir_dump_dir_.IsDefault()) {
    _impl_.ir_dump_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void DebugOptions::set_allocated_ir_dump_dir(std::string* ir_dump_dir) {
  if (ir_dump_dir != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.ir_dump_dir_.SetAllocated(ir_dump_dir, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.ir_dump_dir_.IsDefault()) {
    _impl_.ir_dump_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.converter.DebugOptions.ir_dump_dir)
}

// optional string ir_dump_pass_regex = 2 [default = ".*"];
inline bool DebugOptions::_internal_has_ir_dump_pass_regex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool DebugOptions::has_ir_dump_pass_regex() const {
  return _internal_has_ir_dump_pass_regex();
}
inline void DebugOptions::clear_ir_dump_pass_regex() {
  _impl_.ir_dump_pass_regex_.ClearToDefault(::tensorflow::converter::DebugOptions::Impl_::_i_give_permission_to_break_this_code_default_ir_dump_pass_regex_, GetArenaForAllocation());
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& DebugOptions::ir_dump_pass_regex() const {
  // @@protoc_insertion_point(field_get:tensorflow.converter.DebugOptions.ir_dump_pass_regex)
  if (_impl_.ir_dump_pass_regex_.IsDefault()) return Impl_::_i_give_permission_to_break_this_code_default_ir_dump_pass_regex_.get();
  return _internal_ir_dump_pass_regex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugOptions::set_ir_dump_pass_regex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.ir_dump_pass_regex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.converter.DebugOptions.ir_dump_pass_regex)
}
inline std::string* DebugOptions::mutable_ir_dump_pass_regex() {
  std::string* _s = _internal_mutable_ir_dump_pass_regex();
  // @@protoc_insertion_point(field_mutable:tensorflow.converter.DebugOptions.ir_dump_pass_regex)
  return _s;
}
inline const std::string& DebugOptions::_internal_ir_dump_pass_regex() const {
  return _impl_.ir_dump_pass_regex_.Get();
}
inline void DebugOptions::_internal_set_ir_dump_pass_regex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.ir_dump_pass_regex_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugOptions::_internal_mutable_ir_dump_pass_regex() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.ir_dump_pass_regex_.Mutable(::tensorflow::converter::DebugOptions::Impl_::_i_give_permission_to_break_this_code_default_ir_dump_pass_regex_, GetArenaForAllocation());
}
inline std::string* DebugOptions::release_ir_dump_pass_regex() {
  // @@protoc_insertion_point(field_release:tensorflow.converter.DebugOptions.ir_dump_pass_regex)
  if (!_internal_has_ir_dump_pass_regex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.ir_dump_pass_regex_.Release();
  return p;
}
inline void DebugOptions::set_allocated_ir_dump_pass_regex(std::string* ir_dump_pass_regex) {
  if (ir_dump_pass_regex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.ir_dump_pass_regex_.SetAllocated(ir_dump_pass_regex, GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.converter.DebugOptions.ir_dump_pass_regex)
}

// optional string ir_dump_func_regex = 3 [default = ".*"];
inline bool DebugOptions::_internal_has_ir_dump_func_regex() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool DebugOptions::has_ir_dump_func_regex() const {
  return _internal_has_ir_dump_func_regex();
}
inline void DebugOptions::clear_ir_dump_func_regex() {
  _impl_.ir_dump_func_regex_.ClearToDefault(::tensorflow::converter::DebugOptions::Impl_::_i_give_permission_to_break_this_code_default_ir_dump_func_regex_, GetArenaForAllocation());
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const std::string& DebugOptions::ir_dump_func_regex() const {
  // @@protoc_insertion_point(field_get:tensorflow.converter.DebugOptions.ir_dump_func_regex)
  if (_impl_.ir_dump_func_regex_.IsDefault()) return Impl_::_i_give_permission_to_break_this_code_default_ir_dump_func_regex_.get();
  return _internal_ir_dump_func_regex();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugOptions::set_ir_dump_func_regex(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000004u;
 _impl_.ir_dump_func_regex_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.converter.DebugOptions.ir_dump_func_regex)
}
inline std::string* DebugOptions::mutable_ir_dump_func_regex() {
  std::string* _s = _internal_mutable_ir_dump_func_regex();
  // @@protoc_insertion_point(field_mutable:tensorflow.converter.DebugOptions.ir_dump_func_regex)
  return _s;
}
inline const std::string& DebugOptions::_internal_ir_dump_func_regex() const {
  return _impl_.ir_dump_func_regex_.Get();
}
inline void DebugOptions::_internal_set_ir_dump_func_regex(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.ir_dump_func_regex_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugOptions::_internal_mutable_ir_dump_func_regex() {
  _impl_._has_bits_[0] |= 0x00000004u;
  return _impl_.ir_dump_func_regex_.Mutable(::tensorflow::converter::DebugOptions::Impl_::_i_give_permission_to_break_this_code_default_ir_dump_func_regex_, GetArenaForAllocation());
}
inline std::string* DebugOptions::release_ir_dump_func_regex() {
  // @@protoc_insertion_point(field_release:tensorflow.converter.DebugOptions.ir_dump_func_regex)
  if (!_internal_has_ir_dump_func_regex()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000004u;
  auto* p = _impl_.ir_dump_func_regex_.Release();
  return p;
}
inline void DebugOptions::set_allocated_ir_dump_func_regex(std::string* ir_dump_func_regex) {
  if (ir_dump_func_regex != nullptr) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.ir_dump_func_regex_.SetAllocated(ir_dump_func_regex, GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:tensorflow.converter.DebugOptions.ir_dump_func_regex)
}

// optional bool enable_timing = 4 [default = false];
inline bool DebugOptions::_internal_has_enable_timing() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool DebugOptions::has_enable_timing() const {
  return _internal_has_enable_timing();
}
inline void DebugOptions::clear_enable_timing() {
  _impl_.enable_timing_ = false;
  _impl_._has_bits_[0] &= ~0x00000040u;
}
inline bool DebugOptions::_internal_enable_timing() const {
  return _impl_.enable_timing_;
}
inline bool DebugOptions::enable_timing() const {
  // @@protoc_insertion_point(field_get:tensorflow.converter.DebugOptions.enable_timing)
  return _internal_enable_timing();
}
inline void DebugOptions::_internal_set_enable_timing(bool value) {
  _impl_._has_bits_[0] |= 0x00000040u;
  _impl_.enable_timing_ = value;
}
inline void DebugOptions::set_enable_timing(bool value) {
  _internal_set_enable_timing(value);
  // @@protoc_insertion_point(field_set:tensorflow.converter.DebugOptions.enable_timing)
}

// optional string print_ir_before = 5 [default = ""];
inline bool DebugOptions::_internal_has_print_ir_before() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool DebugOptions::has_print_ir_before() const {
  return _internal_has_print_ir_before();
}
inline void DebugOptions::clear_print_ir_before() {
  _impl_.print_ir_before_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline const std::string& DebugOptions::print_ir_before() const {
  // @@protoc_insertion_point(field_get:tensorflow.converter.DebugOptions.print_ir_before)
  return _internal_print_ir_before();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugOptions::set_print_ir_before(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000008u;
 _impl_.print_ir_before_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.converter.DebugOptions.print_ir_before)
}
inline std::string* DebugOptions::mutable_print_ir_before() {
  std::string* _s = _internal_mutable_print_ir_before();
  // @@protoc_insertion_point(field_mutable:tensorflow.converter.DebugOptions.print_ir_before)
  return _s;
}
inline const std::string& DebugOptions::_internal_print_ir_before() const {
  return _impl_.print_ir_before_.Get();
}
inline void DebugOptions::_internal_set_print_ir_before(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.print_ir_before_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugOptions::_internal_mutable_print_ir_before() {
  _impl_._has_bits_[0] |= 0x00000008u;
  return _impl_.print_ir_before_.Mutable(GetArenaForAllocation());
}
inline std::string* DebugOptions::release_print_ir_before() {
  // @@protoc_insertion_point(field_release:tensorflow.converter.DebugOptions.print_ir_before)
  if (!_internal_has_print_ir_before()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000008u;
  auto* p = _impl_.print_ir_before_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.print_ir_before_.IsDefault()) {
    _impl_.print_ir_before_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void DebugOptions::set_allocated_print_ir_before(std::string* print_ir_before) {
  if (print_ir_before != nullptr) {
    _impl_._has_bits_[0] |= 0x00000008u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000008u;
  }
  _impl_.print_ir_before_.SetAllocated(print_ir_before, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.print_ir_before_.IsDefault()) {
    _impl_.print_ir_before_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.converter.DebugOptions.print_ir_before)
}

// optional string print_ir_after = 6 [default = ""];
inline bool DebugOptions::_internal_has_print_ir_after() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool DebugOptions::has_print_ir_after() const {
  return _internal_has_print_ir_after();
}
inline void DebugOptions::clear_print_ir_after() {
  _impl_.print_ir_after_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline const std::string& DebugOptions::print_ir_after() const {
  // @@protoc_insertion_point(field_get:tensorflow.converter.DebugOptions.print_ir_after)
  return _internal_print_ir_after();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugOptions::set_print_ir_after(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000010u;
 _impl_.print_ir_after_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.converter.DebugOptions.print_ir_after)
}
inline std::string* DebugOptions::mutable_print_ir_after() {
  std::string* _s = _internal_mutable_print_ir_after();
  // @@protoc_insertion_point(field_mutable:tensorflow.converter.DebugOptions.print_ir_after)
  return _s;
}
inline const std::string& DebugOptions::_internal_print_ir_after() const {
  return _impl_.print_ir_after_.Get();
}
inline void DebugOptions::_internal_set_print_ir_after(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.print_ir_after_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugOptions::_internal_mutable_print_ir_after() {
  _impl_._has_bits_[0] |= 0x00000010u;
  return _impl_.print_ir_after_.Mutable(GetArenaForAllocation());
}
inline std::string* DebugOptions::release_print_ir_after() {
  // @@protoc_insertion_point(field_release:tensorflow.converter.DebugOptions.print_ir_after)
  if (!_internal_has_print_ir_after()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000010u;
  auto* p = _impl_.print_ir_after_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.print_ir_after_.IsDefault()) {
    _impl_.print_ir_after_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void DebugOptions::set_allocated_print_ir_after(std::string* print_ir_after) {
  if (print_ir_after != nullptr) {
    _impl_._has_bits_[0] |= 0x00000010u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000010u;
  }
  _impl_.print_ir_after_.SetAllocated(print_ir_after, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.print_ir_after_.IsDefault()) {
    _impl_.print_ir_after_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.converter.DebugOptions.print_ir_after)
}

// optional bool print_ir_module_scope = 7 [default = true];
inline bool DebugOptions::_internal_has_print_ir_module_scope() const {
  bool value = (_impl_._has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool DebugOptions::has_print_ir_module_scope() const {
  return _internal_has_print_ir_module_scope();
}
inline void DebugOptions::clear_print_ir_module_scope() {
  _impl_.print_ir_module_scope_ = true;
  _impl_._has_bits_[0] &= ~0x00000080u;
}
inline bool DebugOptions::_internal_print_ir_module_scope() const {
  return _impl_.print_ir_module_scope_;
}
inline bool DebugOptions::print_ir_module_scope() const {
  // @@protoc_insertion_point(field_get:tensorflow.converter.DebugOptions.print_ir_module_scope)
  return _internal_print_ir_module_scope();
}
inline void DebugOptions::_internal_set_print_ir_module_scope(bool value) {
  _impl_._has_bits_[0] |= 0x00000080u;
  _impl_.print_ir_module_scope_ = value;
}
inline void DebugOptions::set_print_ir_module_scope(bool value) {
  _internal_set_print_ir_module_scope(value);
  // @@protoc_insertion_point(field_set:tensorflow.converter.DebugOptions.print_ir_module_scope)
}

// optional int64 elide_elementsattrs_if_larger = 8;
inline bool DebugOptions::_internal_has_elide_elementsattrs_if_larger() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool DebugOptions::has_elide_elementsattrs_if_larger() const {
  return _internal_has_elide_elementsattrs_if_larger();
}
inline void DebugOptions::clear_elide_elementsattrs_if_larger() {
  _impl_.elide_elementsattrs_if_larger_ = int64_t{0};
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline int64_t DebugOptions::_internal_elide_elementsattrs_if_larger() const {
  return _impl_.elide_elementsattrs_if_larger_;
}
inline int64_t DebugOptions::elide_elementsattrs_if_larger() const {
  // @@protoc_insertion_point(field_get:tensorflow.converter.DebugOptions.elide_elementsattrs_if_larger)
  return _internal_elide_elementsattrs_if_larger();
}
inline void DebugOptions::_internal_set_elide_elementsattrs_if_larger(int64_t value) {
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.elide_elementsattrs_if_larger_ = value;
}
inline void DebugOptions::set_elide_elementsattrs_if_larger(int64_t value) {
  _internal_set_elide_elementsattrs_if_larger(value);
  // @@protoc_insertion_point(field_set:tensorflow.converter.DebugOptions.elide_elementsattrs_if_larger)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace converter
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2flite_2fdebug_2fdebug_5foptions_2eproto
