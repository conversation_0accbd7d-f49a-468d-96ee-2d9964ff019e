.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ext_import_tlsfeatures" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ext_import_tlsfeatures \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ext_import_tlsfeatures(const gnutls_datum_t * " ext ", gnutls_x509_tlsfeatures_t " f ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * ext" 12
The DER\-encoded extension data
.IP "gnutls_x509_tlsfeatures_t f" 12
The features structure
.IP "unsigned int flags" 12
zero or \fBGNUTLS_EXT_FLAG_APPEND\fP
.SH "DESCRIPTION"
This function will export the features in the provided DER\-encoded
TLS Features PKIX extension, to a \fBgnutls_x509_tlsfeatures_t\fP type.  \fIf\fP must be initialized.

When the  \fIflags\fP is set to \fBGNUTLS_EXT_FLAG_APPEND\fP,
then if the  \fIfeatures\fP structure is empty this function will behave
identically as if the flag was not set. Otherwise if there are elements 
in the  \fIfeatures\fP structure then they will be merged with.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.5.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
