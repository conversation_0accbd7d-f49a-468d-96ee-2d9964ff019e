/*** Autogenerated by WIDL 10.8 from include/windows.perception.spatial.surfaces.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_perception_spatial_surfaces_h__
#define __windows_perception_spatial_surfaces_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceInfo
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceInfo;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMesh
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceMesh;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshBuffer
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceMeshBuffer;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshOptions
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceMeshOptions;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshOptionsStatics
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceMeshOptionsStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceObserver
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceObserver;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceObserverStatics
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceObserverStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceObserverStatics2
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceObserverStatics2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceInfo_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    class SpatialSurfaceInfo;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceInfo __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceInfo;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceInfo_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMesh_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMesh_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    class SpatialSurfaceMesh;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMesh __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMesh;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMesh_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshBuffer_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    class SpatialSurfaceMeshBuffer;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshBuffer __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshBuffer;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshBuffer_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshOptions_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    class SpatialSurfaceMeshOptions;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshOptions __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshOptions;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceMeshOptions_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceObserver_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceObserver_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    class SpatialSurfaceObserver;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceObserver __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceObserver;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CSpatialSurfaceObserver_FWD_DEFINED__ */

#ifndef ____FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
#define ____FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
typedef interface __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo;
#ifdef __cplusplus
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
#define ____FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
typedef interface __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo;
#ifdef __cplusplus
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
typedef interface __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo;
#ifdef __cplusplus
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
typedef interface __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo;
#ifdef __cplusplus
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceObserver*,IInspectable* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.foundation.numerics.h>
#include <windows.graphics.directx.h>
#include <windows.perception.spatial.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceInfo
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceInfo;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMesh
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceMesh;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshBuffer
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceMeshBuffer;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshOptions
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceMeshOptions;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshOptionsStatics
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceMeshOptionsStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceObserver
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceObserver;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceObserverStatics
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceObserverStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceObserverStatics2
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    interface ISpatialSurfaceObserverStatics2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
#define ____FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
typedef interface __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo;
#ifdef __cplusplus
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
#define ____FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
typedef interface __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo;
#ifdef __cplusplus
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
typedef interface __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo;
#ifdef __cplusplus
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_FWD_DEFINED__
typedef interface __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo;
#ifdef __cplusplus
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ISpatialSurfaceInfo interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo, 0xf8e9ebe7, 0x39b7, 0x3962, 0xbb,0x03, 0x57,0xf5,0x6e,0x1f,0xb0,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    MIDL_INTERFACE("f8e9ebe7-39b7-3962-bb03-57f56e1fb0a1")
                    ISpatialSurfaceInfo : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Id(
                            GUID *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_UpdateTime(
                            ABI::Windows::Foundation::DateTime *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryGetBounds(
                            ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem *system,
                            ABI::Windows::Foundation::IReference<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox > **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryComputeLatestMeshAsync(
                            DOUBLE triangles,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryComputeLatestMeshWithOptionsAsync(
                            DOUBLE triangles,
                            ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshOptions *options,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo, 0xf8e9ebe7, 0x39b7, 0x3962, 0xbb,0x03, 0x57,0xf5,0x6e,0x1f,0xb0,0xa1)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        TrustLevel *trustLevel);

    /*** ISpatialSurfaceInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *get_UpdateTime)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    HRESULT (STDMETHODCALLTYPE *TryGetBounds)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,
        __FIReference_1_SpatialBoundingOrientedBox **value);

    HRESULT (STDMETHODCALLTYPE *TryComputeLatestMeshAsync)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        DOUBLE triangles,
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh **value);

    HRESULT (STDMETHODCALLTYPE *TryComputeLatestMeshWithOptionsAsync)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo *This,
        DOUBLE triangles,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *options,
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh **value);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfoVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialSurfaceInfo methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_get_UpdateTime(This,value) (This)->lpVtbl->get_UpdateTime(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryGetBounds(This,system,value) (This)->lpVtbl->TryGetBounds(This,system,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryComputeLatestMeshAsync(This,triangles,value) (This)->lpVtbl->TryComputeLatestMeshAsync(This,triangles,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryComputeLatestMeshWithOptionsAsync(This,triangles,options,value) (This)->lpVtbl->TryComputeLatestMeshWithOptionsAsync(This,triangles,options,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_Release(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialSurfaceInfo methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_get_Id(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,GUID *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_get_UpdateTime(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_UpdateTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryGetBounds(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,__FIReference_1_SpatialBoundingOrientedBox **value) {
    return This->lpVtbl->TryGetBounds(This,system,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryComputeLatestMeshAsync(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,DOUBLE triangles,__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh **value) {
    return This->lpVtbl->TryComputeLatestMeshAsync(This,triangles,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryComputeLatestMeshWithOptionsAsync(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo* This,DOUBLE triangles,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *options,__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh **value) {
    return This->lpVtbl->TryComputeLatestMeshWithOptionsAsync(This,triangles,options,value);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial_Surfaces
#define IID_ISpatialSurfaceInfo IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo
#define ISpatialSurfaceInfoVtbl __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfoVtbl
#define ISpatialSurfaceInfo __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo
#define ISpatialSurfaceInfo_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_QueryInterface
#define ISpatialSurfaceInfo_AddRef __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_AddRef
#define ISpatialSurfaceInfo_Release __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_Release
#define ISpatialSurfaceInfo_GetIids __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetIids
#define ISpatialSurfaceInfo_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetRuntimeClassName
#define ISpatialSurfaceInfo_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_GetTrustLevel
#define ISpatialSurfaceInfo_get_Id __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_get_Id
#define ISpatialSurfaceInfo_get_UpdateTime __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_get_UpdateTime
#define ISpatialSurfaceInfo_TryGetBounds __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryGetBounds
#define ISpatialSurfaceInfo_TryComputeLatestMeshAsync __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryComputeLatestMeshAsync
#define ISpatialSurfaceInfo_TryComputeLatestMeshWithOptionsAsync __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_TryComputeLatestMeshWithOptionsAsync
#endif /* WIDL_using_Windows_Perception_Spatial_Surfaces */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialSurfaceMesh interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh, 0x108f57d9, 0xdf0d, 0x3950, 0xa0,0xfd, 0xf9,0x72,0xc7,0x7c,0x27,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    MIDL_INTERFACE("108f57d9-df0d-3950-a0fd-f972c77c27b4")
                    ISpatialSurfaceMesh : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_SurfaceInfo(
                            ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceInfo **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_CoordinateSystem(
                            ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_TriangleIndices(
                            ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshBuffer **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_VertexPositions(
                            ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshBuffer **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_VertexPositionScale(
                            ABI::Windows::Foundation::Numerics::Vector3 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_VertexNormals(
                            ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMeshBuffer **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh, 0x108f57d9, 0xdf0d, 0x3950, 0xa0,0xfd, 0xf9,0x72,0xc7,0x7c,0x27,0xb4)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        TrustLevel *trustLevel);

    /*** ISpatialSurfaceMesh methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SurfaceInfo)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo **value);

    HRESULT (STDMETHODCALLTYPE *get_CoordinateSystem)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem **value);

    HRESULT (STDMETHODCALLTYPE *get_TriangleIndices)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer **value);

    HRESULT (STDMETHODCALLTYPE *get_VertexPositions)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer **value);

    HRESULT (STDMETHODCALLTYPE *get_VertexPositionScale)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        __x_ABI_CWindows_CFoundation_CNumerics_CVector3 *value);

    HRESULT (STDMETHODCALLTYPE *get_VertexNormals)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer **value);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialSurfaceMesh methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_SurfaceInfo(This,value) (This)->lpVtbl->get_SurfaceInfo(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_CoordinateSystem(This,value) (This)->lpVtbl->get_CoordinateSystem(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_TriangleIndices(This,value) (This)->lpVtbl->get_TriangleIndices(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexPositions(This,value) (This)->lpVtbl->get_VertexPositions(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexPositionScale(This,value) (This)->lpVtbl->get_VertexPositionScale(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexNormals(This,value) (This)->lpVtbl->get_VertexNormals(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_Release(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialSurfaceMesh methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_SurfaceInfo(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo **value) {
    return This->lpVtbl->get_SurfaceInfo(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_CoordinateSystem(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem **value) {
    return This->lpVtbl->get_CoordinateSystem(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_TriangleIndices(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer **value) {
    return This->lpVtbl->get_TriangleIndices(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexPositions(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer **value) {
    return This->lpVtbl->get_VertexPositions(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexPositionScale(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,__x_ABI_CWindows_CFoundation_CNumerics_CVector3 *value) {
    return This->lpVtbl->get_VertexPositionScale(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexNormals(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh* This,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer **value) {
    return This->lpVtbl->get_VertexNormals(This,value);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial_Surfaces
#define IID_ISpatialSurfaceMesh IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh
#define ISpatialSurfaceMeshVtbl __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshVtbl
#define ISpatialSurfaceMesh __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh
#define ISpatialSurfaceMesh_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_QueryInterface
#define ISpatialSurfaceMesh_AddRef __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_AddRef
#define ISpatialSurfaceMesh_Release __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_Release
#define ISpatialSurfaceMesh_GetIids __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetIids
#define ISpatialSurfaceMesh_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetRuntimeClassName
#define ISpatialSurfaceMesh_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_GetTrustLevel
#define ISpatialSurfaceMesh_get_SurfaceInfo __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_SurfaceInfo
#define ISpatialSurfaceMesh_get_CoordinateSystem __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_CoordinateSystem
#define ISpatialSurfaceMesh_get_TriangleIndices __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_TriangleIndices
#define ISpatialSurfaceMesh_get_VertexPositions __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexPositions
#define ISpatialSurfaceMesh_get_VertexPositionScale __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexPositionScale
#define ISpatialSurfaceMesh_get_VertexNormals __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_get_VertexNormals
#endif /* WIDL_using_Windows_Perception_Spatial_Surfaces */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialSurfaceMeshBuffer interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer, 0x93cf59e0, 0x871f, 0x33f8, 0x98,0xb2, 0x03,0xd1,0x01,0x45,0x8f,0x6f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    MIDL_INTERFACE("93cf59e0-871f-33f8-98b2-03d101458f6f")
                    ISpatialSurfaceMeshBuffer : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Format(
                            ABI::Windows::Graphics::DirectX::DirectXPixelFormat *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Stride(
                            UINT32 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ElementCount(
                            UINT32 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Data(
                            ABI::Windows::Storage::Streams::IBuffer **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer, 0x93cf59e0, 0x871f, 0x33f8, 0x98,0xb2, 0x03,0xd1,0x01,0x45,0x8f,0x6f)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This,
        TrustLevel *trustLevel);

    /*** ISpatialSurfaceMeshBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Format)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat *value);

    HRESULT (STDMETHODCALLTYPE *get_Stride)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_ElementCount)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Data)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer **value);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBufferVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialSurfaceMeshBuffer methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Format(This,value) (This)->lpVtbl->get_Format(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Stride(This,value) (This)->lpVtbl->get_Stride(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_ElementCount(This,value) (This)->lpVtbl->get_ElementCount(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Data(This,value) (This)->lpVtbl->get_Data(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_Release(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialSurfaceMeshBuffer methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Format(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat *value) {
    return This->lpVtbl->get_Format(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Stride(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This,UINT32 *value) {
    return This->lpVtbl->get_Stride(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_ElementCount(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This,UINT32 *value) {
    return This->lpVtbl->get_ElementCount(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Data(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer **value) {
    return This->lpVtbl->get_Data(This,value);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial_Surfaces
#define IID_ISpatialSurfaceMeshBuffer IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer
#define ISpatialSurfaceMeshBufferVtbl __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBufferVtbl
#define ISpatialSurfaceMeshBuffer __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer
#define ISpatialSurfaceMeshBuffer_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_QueryInterface
#define ISpatialSurfaceMeshBuffer_AddRef __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_AddRef
#define ISpatialSurfaceMeshBuffer_Release __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_Release
#define ISpatialSurfaceMeshBuffer_GetIids __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetIids
#define ISpatialSurfaceMeshBuffer_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetRuntimeClassName
#define ISpatialSurfaceMeshBuffer_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_GetTrustLevel
#define ISpatialSurfaceMeshBuffer_get_Format __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Format
#define ISpatialSurfaceMeshBuffer_get_Stride __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Stride
#define ISpatialSurfaceMeshBuffer_get_ElementCount __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_ElementCount
#define ISpatialSurfaceMeshBuffer_get_Data __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_get_Data
#endif /* WIDL_using_Windows_Perception_Spatial_Surfaces */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshBuffer_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialSurfaceMeshOptions interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions, 0xd2759f89, 0x3572, 0x3d2d, 0xa1,0x0d, 0x5f,0xee,0x93,0x94,0xaa,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    MIDL_INTERFACE("d2759f89-3572-3d2d-a10d-5fee9394aa37")
                    ISpatialSurfaceMeshOptions : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_VertexPositionFormat(
                            ABI::Windows::Graphics::DirectX::DirectXPixelFormat *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_VertexPositionFormat(
                            ABI::Windows::Graphics::DirectX::DirectXPixelFormat value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_TriangleIndexFormat(
                            ABI::Windows::Graphics::DirectX::DirectXPixelFormat *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_TriangleIndexFormat(
                            ABI::Windows::Graphics::DirectX::DirectXPixelFormat value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_VertexNormalFormat(
                            ABI::Windows::Graphics::DirectX::DirectXPixelFormat *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_VertexNormalFormat(
                            ABI::Windows::Graphics::DirectX::DirectXPixelFormat value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_IncludeVertexNormals(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_IncludeVertexNormals(
                            boolean value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions, 0xd2759f89, 0x3572, 0x3d2d, 0xa1,0x0d, 0x5f,0xee,0x93,0x94,0xaa,0x37)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        TrustLevel *trustLevel);

    /*** ISpatialSurfaceMeshOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *get_VertexPositionFormat)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat *value);

    HRESULT (STDMETHODCALLTYPE *put_VertexPositionFormat)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat value);

    HRESULT (STDMETHODCALLTYPE *get_TriangleIndexFormat)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat *value);

    HRESULT (STDMETHODCALLTYPE *put_TriangleIndexFormat)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat value);

    HRESULT (STDMETHODCALLTYPE *get_VertexNormalFormat)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat *value);

    HRESULT (STDMETHODCALLTYPE *put_VertexNormalFormat)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat value);

    HRESULT (STDMETHODCALLTYPE *get_IncludeVertexNormals)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IncludeVertexNormals)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialSurfaceMeshOptions methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_VertexPositionFormat(This,value) (This)->lpVtbl->get_VertexPositionFormat(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_VertexPositionFormat(This,value) (This)->lpVtbl->put_VertexPositionFormat(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_TriangleIndexFormat(This,value) (This)->lpVtbl->get_TriangleIndexFormat(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_TriangleIndexFormat(This,value) (This)->lpVtbl->put_TriangleIndexFormat(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_VertexNormalFormat(This,value) (This)->lpVtbl->get_VertexNormalFormat(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_VertexNormalFormat(This,value) (This)->lpVtbl->put_VertexNormalFormat(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_IncludeVertexNormals(This,value) (This)->lpVtbl->get_IncludeVertexNormals(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_IncludeVertexNormals(This,value) (This)->lpVtbl->put_IncludeVertexNormals(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_Release(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialSurfaceMeshOptions methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_VertexPositionFormat(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat *value) {
    return This->lpVtbl->get_VertexPositionFormat(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_VertexPositionFormat(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat value) {
    return This->lpVtbl->put_VertexPositionFormat(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_TriangleIndexFormat(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat *value) {
    return This->lpVtbl->get_TriangleIndexFormat(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_TriangleIndexFormat(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat value) {
    return This->lpVtbl->put_TriangleIndexFormat(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_VertexNormalFormat(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat *value) {
    return This->lpVtbl->get_VertexNormalFormat(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_VertexNormalFormat(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat value) {
    return This->lpVtbl->put_VertexNormalFormat(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_IncludeVertexNormals(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,boolean *value) {
    return This->lpVtbl->get_IncludeVertexNormals(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_IncludeVertexNormals(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions* This,boolean value) {
    return This->lpVtbl->put_IncludeVertexNormals(This,value);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial_Surfaces
#define IID_ISpatialSurfaceMeshOptions IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions
#define ISpatialSurfaceMeshOptionsVtbl __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsVtbl
#define ISpatialSurfaceMeshOptions __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions
#define ISpatialSurfaceMeshOptions_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_QueryInterface
#define ISpatialSurfaceMeshOptions_AddRef __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_AddRef
#define ISpatialSurfaceMeshOptions_Release __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_Release
#define ISpatialSurfaceMeshOptions_GetIids __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetIids
#define ISpatialSurfaceMeshOptions_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetRuntimeClassName
#define ISpatialSurfaceMeshOptions_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_GetTrustLevel
#define ISpatialSurfaceMeshOptions_get_VertexPositionFormat __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_VertexPositionFormat
#define ISpatialSurfaceMeshOptions_put_VertexPositionFormat __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_VertexPositionFormat
#define ISpatialSurfaceMeshOptions_get_TriangleIndexFormat __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_TriangleIndexFormat
#define ISpatialSurfaceMeshOptions_put_TriangleIndexFormat __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_TriangleIndexFormat
#define ISpatialSurfaceMeshOptions_get_VertexNormalFormat __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_VertexNormalFormat
#define ISpatialSurfaceMeshOptions_put_VertexNormalFormat __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_VertexNormalFormat
#define ISpatialSurfaceMeshOptions_get_IncludeVertexNormals __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_get_IncludeVertexNormals
#define ISpatialSurfaceMeshOptions_put_IncludeVertexNormals __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_put_IncludeVertexNormals
#endif /* WIDL_using_Windows_Perception_Spatial_Surfaces */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptions_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialSurfaceMeshOptionsStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics, 0x9b340abf, 0x9781, 0x4505, 0x89,0x35, 0x01,0x35,0x75,0xca,0xae,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    MIDL_INTERFACE("9b340abf-**************-013575caae5e")
                    ISpatialSurfaceMeshOptionsStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_SupportedVertexPositionFormats(
                            ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::DirectX::DirectXPixelFormat > **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_SupportedTriangleIndexFormats(
                            ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::DirectX::DirectXPixelFormat > **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_SupportedVertexNormalFormats(
                            ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::DirectX::DirectXPixelFormat > **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics, 0x9b340abf, 0x9781, 0x4505, 0x89,0x35, 0x01,0x35,0x75,0xca,0xae,0x5e)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This,
        TrustLevel *trustLevel);

    /*** ISpatialSurfaceMeshOptionsStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SupportedVertexPositionFormats)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This,
        __FIVectorView_1_DirectXPixelFormat **value);

    HRESULT (STDMETHODCALLTYPE *get_SupportedTriangleIndexFormats)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This,
        __FIVectorView_1_DirectXPixelFormat **value);

    HRESULT (STDMETHODCALLTYPE *get_SupportedVertexNormalFormats)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics *This,
        __FIVectorView_1_DirectXPixelFormat **value);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStaticsVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialSurfaceMeshOptionsStatics methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedVertexPositionFormats(This,value) (This)->lpVtbl->get_SupportedVertexPositionFormats(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedTriangleIndexFormats(This,value) (This)->lpVtbl->get_SupportedTriangleIndexFormats(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedVertexNormalFormats(This,value) (This)->lpVtbl->get_SupportedVertexNormalFormats(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_Release(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialSurfaceMeshOptionsStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedVertexPositionFormats(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This,__FIVectorView_1_DirectXPixelFormat **value) {
    return This->lpVtbl->get_SupportedVertexPositionFormats(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedTriangleIndexFormats(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This,__FIVectorView_1_DirectXPixelFormat **value) {
    return This->lpVtbl->get_SupportedTriangleIndexFormats(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedVertexNormalFormats(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics* This,__FIVectorView_1_DirectXPixelFormat **value) {
    return This->lpVtbl->get_SupportedVertexNormalFormats(This,value);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial_Surfaces
#define IID_ISpatialSurfaceMeshOptionsStatics IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics
#define ISpatialSurfaceMeshOptionsStaticsVtbl __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStaticsVtbl
#define ISpatialSurfaceMeshOptionsStatics __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics
#define ISpatialSurfaceMeshOptionsStatics_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_QueryInterface
#define ISpatialSurfaceMeshOptionsStatics_AddRef __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_AddRef
#define ISpatialSurfaceMeshOptionsStatics_Release __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_Release
#define ISpatialSurfaceMeshOptionsStatics_GetIids __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetIids
#define ISpatialSurfaceMeshOptionsStatics_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetRuntimeClassName
#define ISpatialSurfaceMeshOptionsStatics_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_GetTrustLevel
#define ISpatialSurfaceMeshOptionsStatics_get_SupportedVertexPositionFormats __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedVertexPositionFormats
#define ISpatialSurfaceMeshOptionsStatics_get_SupportedTriangleIndexFormats __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedTriangleIndexFormats
#define ISpatialSurfaceMeshOptionsStatics_get_SupportedVertexNormalFormats __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_get_SupportedVertexNormalFormats
#endif /* WIDL_using_Windows_Perception_Spatial_Surfaces */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMeshOptionsStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialSurfaceObserver interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver, 0x10b69819, 0xddca, 0x3483, 0xac,0x3a, 0x74,0x8f,0xe8,0xc8,0x6d,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    MIDL_INTERFACE("10b69819-ddca-3483-ac3a-748fe8c86df5")
                    ISpatialSurfaceObserver : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE GetObservedSurfaces(
                            ABI::Windows::Foundation::Collections::IMapView<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetBoundingVolume(
                            ABI::Windows::Perception::Spatial::ISpatialBoundingVolume *bounds) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SetBoundingVolumes(
                            ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > *bounds) = 0;

                        virtual HRESULT STDMETHODCALLTYPE add_ObservedSurfacesChanged(
                            ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceObserver*,IInspectable* > *handler,
                            EventRegistrationToken *token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE remove_ObservedSurfacesChanged(
                            EventRegistrationToken token) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver, 0x10b69819, 0xddca, 0x3483, 0xac,0x3a, 0x74,0x8f,0xe8,0xc8,0x6d,0xf5)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        TrustLevel *trustLevel);

    /*** ISpatialSurfaceObserver methods ***/
    HRESULT (STDMETHODCALLTYPE *GetObservedSurfaces)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **value);

    HRESULT (STDMETHODCALLTYPE *SetBoundingVolume)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume *bounds);

    HRESULT (STDMETHODCALLTYPE *SetBoundingVolumes)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *bounds);

    HRESULT (STDMETHODCALLTYPE *add_ObservedSurfacesChanged)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ObservedSurfacesChanged)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialSurfaceObserver methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetObservedSurfaces(This,value) (This)->lpVtbl->GetObservedSurfaces(This,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_SetBoundingVolume(This,bounds) (This)->lpVtbl->SetBoundingVolume(This,bounds)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_SetBoundingVolumes(This,bounds) (This)->lpVtbl->SetBoundingVolumes(This,bounds)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_add_ObservedSurfacesChanged(This,handler,token) (This)->lpVtbl->add_ObservedSurfacesChanged(This,handler,token)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_remove_ObservedSurfacesChanged(This,token) (This)->lpVtbl->remove_ObservedSurfacesChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_Release(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialSurfaceObserver methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetObservedSurfaces(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **value) {
    return This->lpVtbl->GetObservedSurfaces(This,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_SetBoundingVolume(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume *bounds) {
    return This->lpVtbl->SetBoundingVolume(This,bounds);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_SetBoundingVolumes(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *bounds) {
    return This->lpVtbl->SetBoundingVolumes(This,bounds);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_add_ObservedSurfacesChanged(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,__FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_ObservedSurfacesChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_remove_ObservedSurfacesChanged(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ObservedSurfacesChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial_Surfaces
#define IID_ISpatialSurfaceObserver IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver
#define ISpatialSurfaceObserverVtbl __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverVtbl
#define ISpatialSurfaceObserver __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver
#define ISpatialSurfaceObserver_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_QueryInterface
#define ISpatialSurfaceObserver_AddRef __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_AddRef
#define ISpatialSurfaceObserver_Release __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_Release
#define ISpatialSurfaceObserver_GetIids __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetIids
#define ISpatialSurfaceObserver_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetRuntimeClassName
#define ISpatialSurfaceObserver_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetTrustLevel
#define ISpatialSurfaceObserver_GetObservedSurfaces __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_GetObservedSurfaces
#define ISpatialSurfaceObserver_SetBoundingVolume __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_SetBoundingVolume
#define ISpatialSurfaceObserver_SetBoundingVolumes __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_SetBoundingVolumes
#define ISpatialSurfaceObserver_add_ObservedSurfacesChanged __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_add_ObservedSurfacesChanged
#define ISpatialSurfaceObserver_remove_ObservedSurfacesChanged __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_remove_ObservedSurfacesChanged
#endif /* WIDL_using_Windows_Perception_Spatial_Surfaces */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialSurfaceObserverStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics, 0x165951ed, 0x2108, 0x4168, 0x91,0x75, 0x87,0xe0,0x27,0xbc,0x92,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    MIDL_INTERFACE("165951ed-**************-87e027bc9285")
                    ISpatialSurfaceObserverStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE RequestAccessAsync(
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > **result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics, 0x165951ed, 0x2108, 0x4168, 0x91,0x75, 0x87,0xe0,0x27,0xbc,0x92,0x85)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics *This,
        TrustLevel *trustLevel);

    /*** ISpatialSurfaceObserverStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *RequestAccessAsync)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics *This,
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus **result);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStaticsVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialSurfaceObserverStatics methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_RequestAccessAsync(This,result) (This)->lpVtbl->RequestAccessAsync(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_Release(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialSurfaceObserverStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_RequestAccessAsync(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics* This,__FIAsyncOperation_1_SpatialPerceptionAccessStatus **result) {
    return This->lpVtbl->RequestAccessAsync(This,result);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial_Surfaces
#define IID_ISpatialSurfaceObserverStatics IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics
#define ISpatialSurfaceObserverStaticsVtbl __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStaticsVtbl
#define ISpatialSurfaceObserverStatics __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics
#define ISpatialSurfaceObserverStatics_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_QueryInterface
#define ISpatialSurfaceObserverStatics_AddRef __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_AddRef
#define ISpatialSurfaceObserverStatics_Release __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_Release
#define ISpatialSurfaceObserverStatics_GetIids __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetIids
#define ISpatialSurfaceObserverStatics_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetRuntimeClassName
#define ISpatialSurfaceObserverStatics_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_GetTrustLevel
#define ISpatialSurfaceObserverStatics_RequestAccessAsync __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_RequestAccessAsync
#endif /* WIDL_using_Windows_Perception_Spatial_Surfaces */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialSurfaceObserverStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2, 0x0f534261, 0xc55d, 0x4e6b, 0xa8,0x95, 0xa1,0x9d,0xe6,0x9a,0x42,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                namespace Surfaces {
                    MIDL_INTERFACE("0f534261-c55d-4e6b-a895-a19de69a42e3")
                    ISpatialSurfaceObserverStatics2 : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE IsSupported(
                            boolean *value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2, 0x0f534261, 0xc55d, 0x4e6b, 0xa8,0x95, 0xa1,0x9d,0xe6,0x9a,0x42,0xe3)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 *This,
        TrustLevel *trustLevel);

    /*** ISpatialSurfaceObserverStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsSupported)(
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2Vtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2 {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialSurfaceObserverStatics2 methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_IsSupported(This,value) (This)->lpVtbl->IsSupported(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_Release(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialSurfaceObserverStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_IsSupported(__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2* This,boolean *value) {
    return This->lpVtbl->IsSupported(This,value);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial_Surfaces
#define IID_ISpatialSurfaceObserverStatics2 IID___x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2
#define ISpatialSurfaceObserverStatics2Vtbl __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2Vtbl
#define ISpatialSurfaceObserverStatics2 __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2
#define ISpatialSurfaceObserverStatics2_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_QueryInterface
#define ISpatialSurfaceObserverStatics2_AddRef __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_AddRef
#define ISpatialSurfaceObserverStatics2_Release __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_Release
#define ISpatialSurfaceObserverStatics2_GetIids __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetIids
#define ISpatialSurfaceObserverStatics2_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetRuntimeClassName
#define ISpatialSurfaceObserverStatics2_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_GetTrustLevel
#define ISpatialSurfaceObserverStatics2_IsSupported __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_IsSupported
#endif /* WIDL_using_Windows_Perception_Spatial_Surfaces */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserverStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceInfo_DEFINED
#define RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceInfo_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceInfo[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','I','n','f','o',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceInfo[] = L"Windows.Perception.Spatial.Surfaces.SpatialSurfaceInfo";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceInfo[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','I','n','f','o',0};
#endif
#endif /* RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceInfo_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Perception.Spatial.Surfaces.SpatialSurfaceMesh
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMesh_DEFINED
#define RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMesh_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMesh[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','M','e','s','h',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMesh[] = L"Windows.Perception.Spatial.Surfaces.SpatialSurfaceMesh";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMesh[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','M','e','s','h',0};
#endif
#endif /* RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMesh_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshBuffer
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshBuffer_DEFINED
#define RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshBuffer_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshBuffer[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','M','e','s','h','B','u','f','f','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshBuffer[] = L"Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshBuffer";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshBuffer[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','M','e','s','h','B','u','f','f','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshBuffer_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshOptions
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshOptions_DEFINED
#define RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshOptions_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshOptions[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','M','e','s','h','O','p','t','i','o','n','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshOptions[] = L"Windows.Perception.Spatial.Surfaces.SpatialSurfaceMeshOptions";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshOptions[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','M','e','s','h','O','p','t','i','o','n','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceMeshOptions_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Perception.Spatial.Surfaces.SpatialSurfaceObserver
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceObserver_DEFINED
#define RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceObserver_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceObserver[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','O','b','s','e','r','v','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceObserver[] = L"Windows.Perception.Spatial.Surfaces.SpatialSurfaceObserver";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_Surfaces_SpatialSurfaceObserver[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','u','r','f','a','c','e','s','.','S','p','a','t','i','a','l','S','u','r','f','a','c','e','O','b','s','e','r','v','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Perception_Spatial_Surfaces_SpatialSurfaceObserver_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * IMapView<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > interface
 */
#ifndef ____FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__
#define ____FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo, 0xeaa722b9, 0x2859, 0x593d, 0xbb,0x66, 0x0c,0x53,0x8e,0x41,0x5e,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("eaa722b9-2859-593d-bb66-0c538e415e71")
                IMapView<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > : IMapView_impl<GUID, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo*, ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceInfo* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo, 0xeaa722b9, 0x2859, 0x593d, 0xbb,0x66, 0x0c,0x53,0x8e,0x41,0x5e,0x71)
#endif
#else
typedef struct __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        TrustLevel *trustLevel);

    /*** IMapView<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        GUID key,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        GUID key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **first,
        __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **second);

    END_INTERFACE
} __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl;

interface __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo {
    CONST_VTBL __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > methods ***/
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > methods ***/
static inline HRESULT __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Lookup(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,GUID key,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Size(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_HasKey(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,GUID key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Split(__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **first,__FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_GUID_SpatialSurfaceInfo IID___FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo
#define IMapView_GUID_SpatialSurfaceInfoVtbl __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl
#define IMapView_GUID_SpatialSurfaceInfo __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo
#define IMapView_GUID_SpatialSurfaceInfo_QueryInterface __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface
#define IMapView_GUID_SpatialSurfaceInfo_AddRef __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef
#define IMapView_GUID_SpatialSurfaceInfo_Release __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release
#define IMapView_GUID_SpatialSurfaceInfo_GetIids __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids
#define IMapView_GUID_SpatialSurfaceInfo_GetRuntimeClassName __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName
#define IMapView_GUID_SpatialSurfaceInfo_GetTrustLevel __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel
#define IMapView_GUID_SpatialSurfaceInfo_Lookup __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Lookup
#define IMapView_GUID_SpatialSurfaceInfo_get_Size __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Size
#define IMapView_GUID_SpatialSurfaceInfo_HasKey __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_HasKey
#define IMapView_GUID_SpatialSurfaceInfo_Split __FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > interface
 */
#ifndef ____FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__
#define ____FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo, 0xa6bdf94a, 0x2697, 0x5ff2, 0x89,0xdc, 0xa1,0x7c,0xec,0xdc,0xda,0x6c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("a6bdf94a-2697-5ff2-89dc-a17cecdcda6c")
                IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > : IKeyValuePair_impl<GUID, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo*, ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceInfo* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo, 0xa6bdf94a, 0x2697, 0x5ff2, 0x89,0xdc, 0xa1,0x7c,0xec,0xdc,0xda,0x6c)
#endif
#else
typedef struct __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        TrustLevel *trustLevel);

    /*** IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Key)(
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        GUID *key);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo **value);

    END_INTERFACE
} __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl;

interface __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo {
    CONST_VTBL __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > methods ***/
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Key(This,key) (This)->lpVtbl->get_Key(This,key)
#define __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* > methods ***/
static inline HRESULT __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Key(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,GUID *key) {
    return This->lpVtbl->get_Key(This,key);
}
static inline HRESULT __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Value(__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceInfo **value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IKeyValuePair_GUID_SpatialSurfaceInfo IID___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo
#define IKeyValuePair_GUID_SpatialSurfaceInfoVtbl __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl
#define IKeyValuePair_GUID_SpatialSurfaceInfo __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo
#define IKeyValuePair_GUID_SpatialSurfaceInfo_QueryInterface __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface
#define IKeyValuePair_GUID_SpatialSurfaceInfo_AddRef __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef
#define IKeyValuePair_GUID_SpatialSurfaceInfo_Release __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release
#define IKeyValuePair_GUID_SpatialSurfaceInfo_GetIids __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids
#define IKeyValuePair_GUID_SpatialSurfaceInfo_GetRuntimeClassName __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName
#define IKeyValuePair_GUID_SpatialSurfaceInfo_GetTrustLevel __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel
#define IKeyValuePair_GUID_SpatialSurfaceInfo_get_Key __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Key
#define IKeyValuePair_GUID_SpatialSurfaceInfo_get_Value __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Value
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > interface
 */
#ifndef ____FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo, 0x868757d1, 0xbe21, 0x51d9, 0x8d,0xee, 0xa9,0x58,0xb9,0xde,0xec,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("868757d1-be21-51d9-8dee-a958b9deec71")
                IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > : IIterable_impl<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo, 0x868757d1, 0xbe21, 0x51d9, 0x8d,0xee, 0xa9,0x58,0xb9,0xde,0xec,0x71)
#endif
#else
typedef struct __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **value);

    END_INTERFACE
} __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl;

interface __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo {
    CONST_VTBL __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > methods ***/
#define __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface(__FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef(__FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release(__FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids(__FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName(__FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel(__FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_First(__FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo IID___FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfoVtbl __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo_QueryInterface __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo_AddRef __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo_Release __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo_GetIids __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo_GetRuntimeClassName __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo_GetTrustLevel __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel
#define IIterable_IKeyValuePair_GUID_SpatialSurfaceInfo_First __FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > interface
 */
#ifndef ____FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo, 0x6d328390, 0xf279, 0x5f39, 0x96,0x82, 0xbb,0xa0,0xcd,0x81,0x48,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("6d328390-f279-5f39-9682-bba0cd81489b")
                IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > : IIterator_impl<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo, 0x6d328390, 0xf279, 0x5f39, 0x96,0x82, 0xbb,0xa0,0xcd,0x81,0x48,0x9b)
#endif
#else
typedef struct __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo *This,
        UINT32 items_size,
        __FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl;

interface __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo {
    CONST_VTBL __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > methods ***/
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<GUID,ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceInfo* >* > methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Current(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_HasCurrent(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_MoveNext(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetMany(__FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo* This,UINT32 items_size,__FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo IID___FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfoVtbl __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfoVtbl
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_QueryInterface __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_QueryInterface
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_AddRef __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_AddRef
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_Release __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_Release
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_GetIids __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetIids
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_GetRuntimeClassName __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetRuntimeClassName
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_GetTrustLevel __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetTrustLevel
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_get_Current __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_Current
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_get_HasCurrent __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_get_HasCurrent
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_MoveNext __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_MoveNext
#define IIterator_IKeyValuePair_GUID_SpatialSurfaceInfo_GetMany __FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1___FIKeyValuePair_2_GUID_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh, 0x4680f7f6, 0x44c5, 0x5fc6, 0x8d,0x51, 0xd6,0x96,0x29,0x15,0xfa,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("4680f7f6-44c5-5fc6-8d51-d6962915fa23")
            IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh*, ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMesh* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh, 0x4680f7f6, 0x44c5, 0x5fc6, 0x8d,0x51, 0xd6,0x96,0x29,0x15,0xfa,0x23)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMeshVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMeshVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMeshVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Release(__FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_SpatialSurfaceMesh IID___FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh
#define IAsyncOperationCompletedHandler_SpatialSurfaceMeshVtbl __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMeshVtbl
#define IAsyncOperationCompletedHandler_SpatialSurfaceMesh __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh
#define IAsyncOperationCompletedHandler_SpatialSurfaceMesh_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_QueryInterface
#define IAsyncOperationCompletedHandler_SpatialSurfaceMesh_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_AddRef
#define IAsyncOperationCompletedHandler_SpatialSurfaceMesh_Release __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Release
#define IAsyncOperationCompletedHandler_SpatialSurfaceMesh_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh, 0xf5938fad, 0xa8a1, 0x5f7e, 0x94,0x40, 0xbd,0xb7,0x81,0xad,0x26,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("f5938fad-a8a1-5f7e-9440-bdb781ad26b6")
            IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh*, ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceMesh* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh, 0xf5938fad, 0xa8a1, 0x5f7e, 0x94,0x40, 0xbd,0xb7,0x81,0xad,0x26,0xb6)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMeshVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMeshVtbl;

interface __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh {
    CONST_VTBL __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMeshVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > methods ***/
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_QueryInterface(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_AddRef(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Release(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetIids(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetTrustLevel(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceMesh* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_put_Completed(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,__FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_get_Completed(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,__FIAsyncOperationCompletedHandler_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetResults(__FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh* This,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceMesh **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_SpatialSurfaceMesh IID___FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh
#define IAsyncOperation_SpatialSurfaceMeshVtbl __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMeshVtbl
#define IAsyncOperation_SpatialSurfaceMesh __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh
#define IAsyncOperation_SpatialSurfaceMesh_QueryInterface __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_QueryInterface
#define IAsyncOperation_SpatialSurfaceMesh_AddRef __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_AddRef
#define IAsyncOperation_SpatialSurfaceMesh_Release __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_Release
#define IAsyncOperation_SpatialSurfaceMesh_GetIids __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetIids
#define IAsyncOperation_SpatialSurfaceMesh_GetRuntimeClassName __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetRuntimeClassName
#define IAsyncOperation_SpatialSurfaceMesh_GetTrustLevel __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetTrustLevel
#define IAsyncOperation_SpatialSurfaceMesh_put_Completed __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_put_Completed
#define IAsyncOperation_SpatialSurfaceMesh_get_Completed __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_get_Completed
#define IAsyncOperation_SpatialSurfaceMesh_GetResults __FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceMesh_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceObserver*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable, 0x8b31274a, 0x7693, 0x52be, 0x90,0x14, 0xb0,0xf5,0xf6,0x5a,0x35,0x39);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("8b31274a-7693-52be-9014-b0f5f65a3539")
            ITypedEventHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceObserver*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceObserver*, ABI::Windows::Perception::Spatial::Surfaces::ISpatialSurfaceObserver* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable, 0x8b31274a, 0x7693, 0x52be, 0x90,0x14, 0xb0,0xf5,0xf6,0x5a,0x35,0x39)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceObserver*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceObserver*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_Release(__FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Perception::Spatial::Surfaces::SpatialSurfaceObserver*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable* This,__x_ABI_CWindows_CPerception_CSpatial_CSurfaces_CISpatialSurfaceObserver *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SpatialSurfaceObserver_IInspectable IID___FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable
#define ITypedEventHandler_SpatialSurfaceObserver_IInspectableVtbl __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectableVtbl
#define ITypedEventHandler_SpatialSurfaceObserver_IInspectable __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable
#define ITypedEventHandler_SpatialSurfaceObserver_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_QueryInterface
#define ITypedEventHandler_SpatialSurfaceObserver_IInspectable_AddRef __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_AddRef
#define ITypedEventHandler_SpatialSurfaceObserver_IInspectable_Release __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_Release
#define ITypedEventHandler_SpatialSurfaceObserver_IInspectable_Invoke __FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CPerception__CSpatial__CSurfaces__CSpatialSurfaceObserver_IInspectable_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_perception_spatial_surfaces_h__ */
