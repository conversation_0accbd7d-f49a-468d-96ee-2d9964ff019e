// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/quantization/tensorflow/exported_model.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fexported_5fmodel_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fexported_5fmodel_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/protobuf/meta_graph.pb.h"
#include "tensorflow/core/protobuf/saver.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fexported_5fmodel_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fexported_5fmodel_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fexported_5fmodel_2eproto;
namespace tensorflow {
namespace quantization {
class ExportedModel;
struct ExportedModelDefaultTypeInternal;
extern ExportedModelDefaultTypeInternal _ExportedModel_default_instance_;
class ExportedModel_FunctionAliasesEntry_DoNotUse;
struct ExportedModel_FunctionAliasesEntry_DoNotUseDefaultTypeInternal;
extern ExportedModel_FunctionAliasesEntry_DoNotUseDefaultTypeInternal _ExportedModel_FunctionAliasesEntry_DoNotUse_default_instance_;
}  // namespace quantization
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::quantization::ExportedModel* Arena::CreateMaybeMessage<::tensorflow::quantization::ExportedModel>(Arena*);
template<> ::tensorflow::quantization::ExportedModel_FunctionAliasesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::quantization::ExportedModel_FunctionAliasesEntry_DoNotUse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace quantization {

// ===================================================================

class ExportedModel_FunctionAliasesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExportedModel_FunctionAliasesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<ExportedModel_FunctionAliasesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  ExportedModel_FunctionAliasesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR ExportedModel_FunctionAliasesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit ExportedModel_FunctionAliasesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const ExportedModel_FunctionAliasesEntry_DoNotUse& other);
  static const ExportedModel_FunctionAliasesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const ExportedModel_FunctionAliasesEntry_DoNotUse*>(&_ExportedModel_FunctionAliasesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.quantization.ExportedModel.FunctionAliasesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.quantization.ExportedModel.FunctionAliasesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fexported_5fmodel_2eproto;
};

// -------------------------------------------------------------------

class ExportedModel final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.quantization.ExportedModel) */ {
 public:
  inline ExportedModel() : ExportedModel(nullptr) {}
  ~ExportedModel() override;
  explicit PROTOBUF_CONSTEXPR ExportedModel(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExportedModel(const ExportedModel& from);
  ExportedModel(ExportedModel&& from) noexcept
    : ExportedModel() {
    *this = ::std::move(from);
  }

  inline ExportedModel& operator=(const ExportedModel& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExportedModel& operator=(ExportedModel&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExportedModel& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExportedModel* internal_default_instance() {
    return reinterpret_cast<const ExportedModel*>(
               &_ExportedModel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ExportedModel& a, ExportedModel& b) {
    a.Swap(&b);
  }
  inline void Swap(ExportedModel* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExportedModel* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExportedModel* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExportedModel>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExportedModel& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExportedModel& from) {
    ExportedModel::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExportedModel* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.quantization.ExportedModel";
  }
  protected:
  explicit ExportedModel(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFunctionAliasesFieldNumber = 6,
    kAssetFileDefsFieldNumber = 8,
    kInitNodeNameFieldNumber = 2,
    kCheckpointDirFieldNumber = 5,
    kGraphDefFieldNumber = 1,
    kSaverDefFieldNumber = 10,
  };
  // map<string, string> function_aliases = 6;
  int function_aliases_size() const;
  private:
  int _internal_function_aliases_size() const;
  public:
  void clear_function_aliases();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_function_aliases() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_function_aliases();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      function_aliases() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_function_aliases();

  // repeated .tensorflow.AssetFileDef asset_file_defs = 8;
  int asset_file_defs_size() const;
  private:
  int _internal_asset_file_defs_size() const;
  public:
  void clear_asset_file_defs();
  ::tensorflow::AssetFileDef* mutable_asset_file_defs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef >*
      mutable_asset_file_defs();
  private:
  const ::tensorflow::AssetFileDef& _internal_asset_file_defs(int index) const;
  ::tensorflow::AssetFileDef* _internal_add_asset_file_defs();
  public:
  const ::tensorflow::AssetFileDef& asset_file_defs(int index) const;
  ::tensorflow::AssetFileDef* add_asset_file_defs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef >&
      asset_file_defs() const;

  // string init_node_name = 2;
  void clear_init_node_name();
  const std::string& init_node_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_init_node_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_init_node_name();
  PROTOBUF_NODISCARD std::string* release_init_node_name();
  void set_allocated_init_node_name(std::string* init_node_name);
  private:
  const std::string& _internal_init_node_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_init_node_name(const std::string& value);
  std::string* _internal_mutable_init_node_name();
  public:

  // string checkpoint_dir = 5;
  void clear_checkpoint_dir();
  const std::string& checkpoint_dir() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_checkpoint_dir(ArgT0&& arg0, ArgT... args);
  std::string* mutable_checkpoint_dir();
  PROTOBUF_NODISCARD std::string* release_checkpoint_dir();
  void set_allocated_checkpoint_dir(std::string* checkpoint_dir);
  private:
  const std::string& _internal_checkpoint_dir() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_checkpoint_dir(const std::string& value);
  std::string* _internal_mutable_checkpoint_dir();
  public:

  // .tensorflow.GraphDef graph_def = 1;
  bool has_graph_def() const;
  private:
  bool _internal_has_graph_def() const;
  public:
  void clear_graph_def();
  const ::tensorflow::GraphDef& graph_def() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  ::tensorflow::GraphDef* _internal_mutable_graph_def();
  public:
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.SaverDef saver_def = 10;
  bool has_saver_def() const;
  private:
  bool _internal_has_saver_def() const;
  public:
  void clear_saver_def();
  const ::tensorflow::SaverDef& saver_def() const;
  PROTOBUF_NODISCARD ::tensorflow::SaverDef* release_saver_def();
  ::tensorflow::SaverDef* mutable_saver_def();
  void set_allocated_saver_def(::tensorflow::SaverDef* saver_def);
  private:
  const ::tensorflow::SaverDef& _internal_saver_def() const;
  ::tensorflow::SaverDef* _internal_mutable_saver_def();
  public:
  void unsafe_arena_set_allocated_saver_def(
      ::tensorflow::SaverDef* saver_def);
  ::tensorflow::SaverDef* unsafe_arena_release_saver_def();

  // @@protoc_insertion_point(class_scope:tensorflow.quantization.ExportedModel)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        ExportedModel_FunctionAliasesEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> function_aliases_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef > asset_file_defs_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr init_node_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr checkpoint_dir_;
    ::tensorflow::GraphDef* graph_def_;
    ::tensorflow::SaverDef* saver_def_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fexported_5fmodel_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// ExportedModel

// .tensorflow.GraphDef graph_def = 1;
inline bool ExportedModel::_internal_has_graph_def() const {
  return this != internal_default_instance() && _impl_.graph_def_ != nullptr;
}
inline bool ExportedModel::has_graph_def() const {
  return _internal_has_graph_def();
}
inline const ::tensorflow::GraphDef& ExportedModel::_internal_graph_def() const {
  const ::tensorflow::GraphDef* p = _impl_.graph_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphDef&>(
      ::tensorflow::_GraphDef_default_instance_);
}
inline const ::tensorflow::GraphDef& ExportedModel::graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.ExportedModel.graph_def)
  return _internal_graph_def();
}
inline void ExportedModel::unsafe_arena_set_allocated_graph_def(
    ::tensorflow::GraphDef* graph_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  _impl_.graph_def_ = graph_def;
  if (graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.quantization.ExportedModel.graph_def)
}
inline ::tensorflow::GraphDef* ExportedModel::release_graph_def() {
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphDef* ExportedModel::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.ExportedModel.graph_def)
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* ExportedModel::_internal_mutable_graph_def() {
  
  if (_impl_.graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaForAllocation());
    _impl_.graph_def_ = p;
  }
  return _impl_.graph_def_;
}
inline ::tensorflow::GraphDef* ExportedModel::mutable_graph_def() {
  ::tensorflow::GraphDef* _msg = _internal_mutable_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.ExportedModel.graph_def)
  return _msg;
}
inline void ExportedModel::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  if (graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def));
    if (message_arena != submessage_arena) {
      graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.ExportedModel.graph_def)
}

// string init_node_name = 2;
inline void ExportedModel::clear_init_node_name() {
  _impl_.init_node_name_.ClearToEmpty();
}
inline const std::string& ExportedModel::init_node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.ExportedModel.init_node_name)
  return _internal_init_node_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExportedModel::set_init_node_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.init_node_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.quantization.ExportedModel.init_node_name)
}
inline std::string* ExportedModel::mutable_init_node_name() {
  std::string* _s = _internal_mutable_init_node_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.ExportedModel.init_node_name)
  return _s;
}
inline const std::string& ExportedModel::_internal_init_node_name() const {
  return _impl_.init_node_name_.Get();
}
inline void ExportedModel::_internal_set_init_node_name(const std::string& value) {
  
  _impl_.init_node_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ExportedModel::_internal_mutable_init_node_name() {
  
  return _impl_.init_node_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ExportedModel::release_init_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.ExportedModel.init_node_name)
  return _impl_.init_node_name_.Release();
}
inline void ExportedModel::set_allocated_init_node_name(std::string* init_node_name) {
  if (init_node_name != nullptr) {
    
  } else {
    
  }
  _impl_.init_node_name_.SetAllocated(init_node_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.init_node_name_.IsDefault()) {
    _impl_.init_node_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.ExportedModel.init_node_name)
}

// string checkpoint_dir = 5;
inline void ExportedModel::clear_checkpoint_dir() {
  _impl_.checkpoint_dir_.ClearToEmpty();
}
inline const std::string& ExportedModel::checkpoint_dir() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.ExportedModel.checkpoint_dir)
  return _internal_checkpoint_dir();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExportedModel::set_checkpoint_dir(ArgT0&& arg0, ArgT... args) {
 
 _impl_.checkpoint_dir_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.quantization.ExportedModel.checkpoint_dir)
}
inline std::string* ExportedModel::mutable_checkpoint_dir() {
  std::string* _s = _internal_mutable_checkpoint_dir();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.ExportedModel.checkpoint_dir)
  return _s;
}
inline const std::string& ExportedModel::_internal_checkpoint_dir() const {
  return _impl_.checkpoint_dir_.Get();
}
inline void ExportedModel::_internal_set_checkpoint_dir(const std::string& value) {
  
  _impl_.checkpoint_dir_.Set(value, GetArenaForAllocation());
}
inline std::string* ExportedModel::_internal_mutable_checkpoint_dir() {
  
  return _impl_.checkpoint_dir_.Mutable(GetArenaForAllocation());
}
inline std::string* ExportedModel::release_checkpoint_dir() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.ExportedModel.checkpoint_dir)
  return _impl_.checkpoint_dir_.Release();
}
inline void ExportedModel::set_allocated_checkpoint_dir(std::string* checkpoint_dir) {
  if (checkpoint_dir != nullptr) {
    
  } else {
    
  }
  _impl_.checkpoint_dir_.SetAllocated(checkpoint_dir, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.checkpoint_dir_.IsDefault()) {
    _impl_.checkpoint_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.ExportedModel.checkpoint_dir)
}

// map<string, string> function_aliases = 6;
inline int ExportedModel::_internal_function_aliases_size() const {
  return _impl_.function_aliases_.size();
}
inline int ExportedModel::function_aliases_size() const {
  return _internal_function_aliases_size();
}
inline void ExportedModel::clear_function_aliases() {
  _impl_.function_aliases_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
ExportedModel::_internal_function_aliases() const {
  return _impl_.function_aliases_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
ExportedModel::function_aliases() const {
  // @@protoc_insertion_point(field_map:tensorflow.quantization.ExportedModel.function_aliases)
  return _internal_function_aliases();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
ExportedModel::_internal_mutable_function_aliases() {
  return _impl_.function_aliases_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
ExportedModel::mutable_function_aliases() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.quantization.ExportedModel.function_aliases)
  return _internal_mutable_function_aliases();
}

// repeated .tensorflow.AssetFileDef asset_file_defs = 8;
inline int ExportedModel::_internal_asset_file_defs_size() const {
  return _impl_.asset_file_defs_.size();
}
inline int ExportedModel::asset_file_defs_size() const {
  return _internal_asset_file_defs_size();
}
inline ::tensorflow::AssetFileDef* ExportedModel::mutable_asset_file_defs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.ExportedModel.asset_file_defs)
  return _impl_.asset_file_defs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef >*
ExportedModel::mutable_asset_file_defs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.quantization.ExportedModel.asset_file_defs)
  return &_impl_.asset_file_defs_;
}
inline const ::tensorflow::AssetFileDef& ExportedModel::_internal_asset_file_defs(int index) const {
  return _impl_.asset_file_defs_.Get(index);
}
inline const ::tensorflow::AssetFileDef& ExportedModel::asset_file_defs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.ExportedModel.asset_file_defs)
  return _internal_asset_file_defs(index);
}
inline ::tensorflow::AssetFileDef* ExportedModel::_internal_add_asset_file_defs() {
  return _impl_.asset_file_defs_.Add();
}
inline ::tensorflow::AssetFileDef* ExportedModel::add_asset_file_defs() {
  ::tensorflow::AssetFileDef* _add = _internal_add_asset_file_defs();
  // @@protoc_insertion_point(field_add:tensorflow.quantization.ExportedModel.asset_file_defs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef >&
ExportedModel::asset_file_defs() const {
  // @@protoc_insertion_point(field_list:tensorflow.quantization.ExportedModel.asset_file_defs)
  return _impl_.asset_file_defs_;
}

// .tensorflow.SaverDef saver_def = 10;
inline bool ExportedModel::_internal_has_saver_def() const {
  return this != internal_default_instance() && _impl_.saver_def_ != nullptr;
}
inline bool ExportedModel::has_saver_def() const {
  return _internal_has_saver_def();
}
inline const ::tensorflow::SaverDef& ExportedModel::_internal_saver_def() const {
  const ::tensorflow::SaverDef* p = _impl_.saver_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SaverDef&>(
      ::tensorflow::_SaverDef_default_instance_);
}
inline const ::tensorflow::SaverDef& ExportedModel::saver_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.quantization.ExportedModel.saver_def)
  return _internal_saver_def();
}
inline void ExportedModel::unsafe_arena_set_allocated_saver_def(
    ::tensorflow::SaverDef* saver_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.saver_def_);
  }
  _impl_.saver_def_ = saver_def;
  if (saver_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.quantization.ExportedModel.saver_def)
}
inline ::tensorflow::SaverDef* ExportedModel::release_saver_def() {
  
  ::tensorflow::SaverDef* temp = _impl_.saver_def_;
  _impl_.saver_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SaverDef* ExportedModel::unsafe_arena_release_saver_def() {
  // @@protoc_insertion_point(field_release:tensorflow.quantization.ExportedModel.saver_def)
  
  ::tensorflow::SaverDef* temp = _impl_.saver_def_;
  _impl_.saver_def_ = nullptr;
  return temp;
}
inline ::tensorflow::SaverDef* ExportedModel::_internal_mutable_saver_def() {
  
  if (_impl_.saver_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SaverDef>(GetArenaForAllocation());
    _impl_.saver_def_ = p;
  }
  return _impl_.saver_def_;
}
inline ::tensorflow::SaverDef* ExportedModel::mutable_saver_def() {
  ::tensorflow::SaverDef* _msg = _internal_mutable_saver_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.quantization.ExportedModel.saver_def)
  return _msg;
}
inline void ExportedModel::set_allocated_saver_def(::tensorflow::SaverDef* saver_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.saver_def_);
  }
  if (saver_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(saver_def));
    if (message_arena != submessage_arena) {
      saver_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, saver_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.saver_def_ = saver_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.quantization.ExportedModel.saver_def)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace quantization
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2fquantization_2ftensorflow_2fexported_5fmodel_2eproto
