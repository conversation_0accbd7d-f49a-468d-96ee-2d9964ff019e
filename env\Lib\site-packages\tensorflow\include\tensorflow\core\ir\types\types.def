/* Copyright 2021 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

// This file contains descriptions of the various TensorFlow types. This is
// used as a central place for enumerating the different types.

#ifdef HANDLE_TF_TYPE

//             class, enumerant, name
HANDLE_TF_TYPE(Qint8, QINT8, "qint8")
HANDLE_TF_TYPE(Qint16, QINT16, "qint16")
HANDLE_TF_TYPE(Qint32, QINT32, "qint32")
HANDLE_TF_TYPE(Quint8, QUINT8, "quint8")
HANDLE_TF_TYPE(Quint16, QUINT16, "quint16")
HANDLE_TF_TYPE(String, STRING, "string")

#ifndef HANDLE_CUSTOM_TF_TYPE
#define HANDLE_CUSTOM_TF_TYPE(class, enumerant, name) \
  HANDLE_TF_TYPE(class, enumerant, name)
#endif
HANDLE_CUSTOM_TF_TYPE(Resource, RESOURCE, "resource")
HANDLE_CUSTOM_TF_TYPE(Variant, VARIANT, "variant")
#undef HANDLE_CUSTOM_TF_TYPE

// All ref types are listed below this line and FloatRef is the first ref type.
// This helps in easily differentiating ref and non-ref types, and converting
// a type to/from ref types.

#ifndef HANDLE_TF_REF_TYPE
#define HANDLE_TF_REF_TYPE(class, enumerant, name) \
  HANDLE_TF_TYPE(class, enumerant, name)
#endif
HANDLE_TF_REF_TYPE(FloatRef, FLOAT_REF, "f32ref")
HANDLE_TF_REF_TYPE(DoubleRef, DOUBLE_REF, "f64ref")
HANDLE_TF_REF_TYPE(Uint4Ref, UINT4_REF, "uint4ref")
HANDLE_TF_REF_TYPE(Int4Ref, INT4_REF, "int4ref")
HANDLE_TF_REF_TYPE(Uint8Ref, UINT8_REF, "uint8ref")
HANDLE_TF_REF_TYPE(Int8Ref, INT8_REF, "int8ref")
HANDLE_TF_REF_TYPE(Uint16Ref, UINT16_REF, "uint16ref")
HANDLE_TF_REF_TYPE(Int16Ref, INT16_REF, "int16ref")
HANDLE_TF_REF_TYPE(Uint32Ref, UINT32_REF, "uint32ref")
HANDLE_TF_REF_TYPE(Int32Ref, INT32_REF, "int32ref")
HANDLE_TF_REF_TYPE(Uint64Ref, UINT64_REF, "uint64ref")
HANDLE_TF_REF_TYPE(Int64Ref, INT64_REF, "int64ref")
HANDLE_TF_REF_TYPE(StringRef, STRING_REF, "stringref")
HANDLE_TF_REF_TYPE(BoolRef, BOOL_REF, "boolref")
HANDLE_TF_REF_TYPE(Quint8Ref, QUINT8_REF, "quint8ref")
HANDLE_TF_REF_TYPE(Qint8Ref, QINT8_REF, "qint8ref")
HANDLE_TF_REF_TYPE(Quint16Ref, QUINT16_REF, "quint16ref")
HANDLE_TF_REF_TYPE(Qint16Ref, QINT16_REF, "qint16ref")
HANDLE_TF_REF_TYPE(Qint32Ref, QINT32_REF, "qint32ref")
HANDLE_TF_REF_TYPE(Bfloat16Ref, BFLOAT16_REF, "bfloat16ref")
HANDLE_TF_REF_TYPE(Complex64Ref, COMPLEX64_REF, "complex64ref")
HANDLE_TF_REF_TYPE(Complex128Ref, COMPLEX128_REF, "complex128ref")
HANDLE_TF_REF_TYPE(HalfRef, HALF_REF, "halfref")
HANDLE_TF_REF_TYPE(ResourceRef, RESOURCE_REF, "resourceref")
HANDLE_TF_REF_TYPE(Float8E4M3FNRef, FLOAT8_E4M3FN_REF, "float8e4m3fnref")
HANDLE_TF_REF_TYPE(Float8E5M2Ref, FLOAT8_E5M2_REF, "float8e5m2ref")
HANDLE_TF_REF_TYPE(Float8E4M3FNUZRef, FLOAT8_E4M3FNUZ_REF, "float8e4m3fnuzref")
HANDLE_TF_REF_TYPE(Float8E4M3B11FNUZRef, FLOAT8_E4M3B11FNUZ_REF, "float8e4m3b11fnuzref")
HANDLE_TF_REF_TYPE(Float8E5M2FNUZRef, FLOAT8_E5M2FNUZ_REF, "float8e5m2fnuzref")

#ifndef HANDLE_LAST_TF_TYPE
#define HANDLE_LAST_TF_TYPE(class, enumerant, name) \
  HANDLE_TF_REF_TYPE(class, enumerant, name)
#endif
HANDLE_LAST_TF_TYPE(VariantRef, VARIANT_REF, "variantref")
#undef HANDLE_LAST_TF_TYPE

#undef HANDLE_TF_REF_TYPE
#undef HANDLE_TF_TYPE
#endif
