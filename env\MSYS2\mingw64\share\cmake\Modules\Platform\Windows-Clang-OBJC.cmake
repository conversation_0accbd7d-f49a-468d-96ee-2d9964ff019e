include(Platform/Windows-Clang)
__windows_compiler_clang(OBJC)

if("x${CMAKE_OBJC_COMPILER_FRONTEND_VARIANT}" STREQUAL "xMSVC")
  if((NOT DEFINED CMAKE_DEPENDS_USE_COMPILER OR CMAKE_DEPENDS_USE_COMPILER)
      AND CMAKE_GENERATOR MATCHES "Makefiles|WMake"
      AND CMAKE_DEPFILE_FLAGS_OBJC)
    set(CMAKE_OBJC_DEPENDS_USE_COMPILER TRUE)
  endif()
elseif("x${CMAKE_OBJC_COMPILER_FRONTEND_VARIANT}" STREQUAL "xGNU")
  if((NOT DEFINED CMAKE_DEPENDS_USE_COMPILER OR CMAKE_DEPENDS_USE_COMPILER)
      AND CMAKE_GENERATOR MATCHES "Makefiles|WMake"
      AND CMAKE_DEPFILE_FLAGS_OBJC)
    # dependencies are computed by the compiler itself
    set(CMAKE_OBJC_DEPFILE_FORMAT gcc)
    set(CMAKE_OBJC_DEPENDS_USE_COMPILER TRUE)
  endif()
endif()
