#!/usr/bin/env python3
"""
下载RNNoise预训练模型的Python脚本
替代download_model.sh在Windows环境下使用
"""

import os
import sys
import urllib.request
import hashlib
import tarfile

def download_file(url, filename):
    """下载文件并显示进度"""
    print(f"正在下载: {url}")
    print(f"保存到: {filename}")
    
    def progress_hook(block_num, block_size, total_size):
        downloaded = block_num * block_size
        if total_size > 0:
            percent = min(100, downloaded * 100 / total_size)
            print(f"\r下载进度: {percent:.1f}% ({downloaded:,}/{total_size:,} 字节)", end='')
        else:
            print(f"\r已下载: {downloaded:,} 字节", end='')
    
    try:
        urllib.request.urlretrieve(url, filename, progress_hook)
        print("\n✓ 下载完成")
        return True
    except Exception as e:
        print(f"\n✗ 下载失败: {e}")
        return False

def calculate_sha256(filename):
    """计算文件的SHA256哈希值"""
    hash_sha256 = hashlib.sha256()
    try:
        with open(filename, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception as e:
        print(f"计算哈希值失败: {e}")
        return None

def extract_tar_gz(filename):
    """解压tar.gz文件"""
    try:
        print(f"正在解压: {filename}")
        with tarfile.open(filename, 'r:gz') as tar:
            tar.extractall()
        print("✓ 解压完成")
        return True
    except Exception as e:
        print(f"✗ 解压失败: {e}")
        return False

def main():
    # 读取模型版本哈希
    try:
        with open('model_version', 'r') as f:
            hash_value = f.read().strip()
    except FileNotFoundError:
        print("错误: 找不到model_version文件")
        return 1
    
    model_filename = f"rnnoise_data-{hash_value}.tar.gz"
    model_url = f"https://media.xiph.org/rnnoise/models/{model_filename}"
    
    print("=" * 50)
    print("RNNoise 模型下载脚本")
    print("=" * 50)
    print(f"模型哈希: {hash_value}")
    print(f"模型文件: {model_filename}")
    print(f"下载地址: {model_url}")
    print()
    
    # 检查文件是否已存在
    if os.path.exists(model_filename):
        print(f"文件 {model_filename} 已存在，验证哈希值...")
        file_hash = calculate_sha256(model_filename)
        if file_hash == hash_value:
            print("✓ 哈希值匹配，文件完整")
        else:
            print("✗ 哈希值不匹配，重新下载...")
            os.remove(model_filename)
        
    # 下载文件
    if not os.path.exists(model_filename):
        if not download_file(model_url, model_filename):
            return 1
        
        # 验证哈希值
        print("验证文件完整性...")
        file_hash = calculate_sha256(model_filename)
        if file_hash != hash_value:
            print(f"✗ 哈希值不匹配!")
            print(f"期望: {hash_value}")
            print(f"实际: {file_hash}")
            print("文件可能已损坏，请删除后重新下载")
            return 1
        else:
            print("✓ 哈希值匹配，文件完整")
    
    # 解压文件
    if extract_tar_gz(model_filename):
        print()
        print("=" * 50)
        print("✓ 模型下载和解压完成!")
        print("=" * 50)
        
        # 检查解压后的文件
        if os.path.exists('src/rnnoise_data.c'):
            print("✓ 找到 src/rnnoise_data.c")
        if os.path.exists('src/rnnoise_data.h'):
            print("✓ 找到 src/rnnoise_data.h")
        
        print("\n现在可以运行编译脚本了!")
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
