/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _WINERROR_
#define _WINERROR_

#define __IN__WINERROR_ 1

#define FACILITY_NULL 0
#define FACILITY_RPC 1
#define FACILITY_DISPATCH 2
#define FACILITY_STORAGE 3
#define FACILITY_ITF 4
#define FACILITY_WIN32 7
#define FACILITY_WINDOWS 8
#define FACILITY_SSPI 9
#define FACILITY_SECURITY 9
#define FACILITY_CONTROL 10
#define FACILITY_CERT 11
#define FACILITY_INTERNET 12
#define FACILITY_MEDIASERVER 13
#define FACILITY_MSMQ 14
#define FACILITY_SETUPAPI 15
#define FACILITY_SCARD 16
#define FACILITY_COMPLUS 17
#define FACILITY_AAF 18
#define FACILITY_URT 19
#define FACILITY_ACS 20
#define FACILITY_DPLAY 21
#define FACILITY_UMI 22
#define FACILITY_SXS 23
#define FACILITY_WINDOWS_CE 24
#define FACILITY_HTTP 25
#define FACILITY_USERMODE_COMMONLOG 26
#define FACILITY_WER 27
#define FACILITY_USERMODE_FILTER_MANAGER 31
#define FACILITY_BACKGROUNDCOPY 32
#define FACILITY_CONFIGURATION 33
#define FACILITY_WIA 33
#define FACILITY_STATE_MANAGEMENT 34
#define FACILITY_METADIRECTORY 35
#define FACILITY_WINDOWSUPDATE 36
#define FACILITY_DIRECTORYSERVICE 37
#define FACILITY_GRAPHICS 38
#define FACILITY_SHELL 39
#define FACILITY_NAP 39
#define FACILITY_TPM_SERVICES 40
#define FACILITY_TPM_SOFTWARE 41
#define FACILITY_UI 42
#define FACILITY_XAML 43
#define FACILITY_ACTION_QUEUE 44
#define FACILITY_PLA 48
#define FACILITY_WINDOWS_SETUP 48
#define FACILITY_FVE 49
#define FACILITY_FWP 50
#define FACILITY_WINRM 51
#define FACILITY_NDIS 52
#define FACILITY_USERMODE_HYPERVISOR 53
#define FACILITY_CMI 54
#define FACILITY_USERMODE_VIRTUALIZATION 55
#define FACILITY_USERMODE_VOLMGR 56
#define FACILITY_BCD 57
#define FACILITY_USERMODE_VHD 58
#define FACILITY_USERMODE_HNS 59
#define FACILITY_SDIAG 60
#define FACILITY_WEBSERVICES 61
#define FACILITY_WINPE 61
#define FACILITY_WPN 62
#define FACILITY_WINDOWS_STORE 63
#define FACILITY_INPUT 64
#define FACILITY_QUIC 65
#define FACILITY_EAP 66
#define FACILITY_IORING 70
#define FACILITY_WINDOWS_DEFENDER 80
#define FACILITY_OPC 81
#define FACILITY_XPS 82
#define FACILITY_MBN 84
#define FACILITY_POWERSHELL 84
#define FACILITY_RAS 83
#define FACILITY_P2P_INT 98
#define FACILITY_P2P 99
#define FACILITY_DAF 100
#define FACILITY_BLUETOOTH_ATT 101
#define FACILITY_AUDIO 102
#define FACILITY_STATEREPOSITORY 103
#define FACILITY_VISUALCPP 109
#define FACILITY_SCRIPT 112
#define FACILITY_PARSE 113
#define FACILITY_BLB 120
#define FACILITY_BLB_CLI 121
#define FACILITY_WSBAPP 122
#define FACILITY_BLBUI 128
#define FACILITY_USN 129
#define FACILITY_USERMODE_VOLSNAP 130
#define FACILITY_TIERING 131
#define FACILITY_WSB_ONLINE 133
#define FACILITY_ONLINE_ID 134
#define FACILITY_DEVICE_UPDATE_AGENT 135
#define FACILITY_DRVSERVICING 136
#define FACILITY_DLS 153
#define FACILITY_DELIVERY_OPTIMIZATION 208
#define FACILITY_USERMODE_SPACES 231
#define FACILITY_USER_MODE_SECURITY_CORE 232
#define FACILITY_USERMODE_LICENSING 234
#define FACILITY_SOS 160
#define FACILITY_OCP_UPDATE_AGENT 173
#define FACILITY_DEBUGGERS 176
#define FACILITY_SPP 256
#define FACILITY_RESTORE 256
#define FACILITY_DMSERVER 256
#define FACILITY_DEPLOYMENT_SERVICES_SERVER 257
#define FACILITY_DEPLOYMENT_SERVICES_IMAGING 258
#define FACILITY_DEPLOYMENT_SERVICES_MANAGEMENT 259
#define FACILITY_DEPLOYMENT_SERVICES_UTIL 260
#define FACILITY_DEPLOYMENT_SERVICES_BINLSVC 261
#define FACILITY_DEPLOYMENT_SERVICES_PXE 263
#define FACILITY_DEPLOYMENT_SERVICES_TFTP 264
#define FACILITY_DEPLOYMENT_SERVICES_TRANSPORT_MANAGEMENT 272
#define FACILITY_DEPLOYMENT_SERVICES_DRIVER_PROVISIONING 278
#define FACILITY_DEPLOYMENT_SERVICES_MULTICAST_SERVER 289
#define FACILITY_DEPLOYMENT_SERVICES_MULTICAST_CLIENT 290
#define FACILITY_DEPLOYMENT_SERVICES_CONTENT_PROVIDER 293
#define FACILITY_HSP_SERVICES 296
#define FACILITY_HSP_SOFTWARE 297
#define FACILITY_LINGUISTIC_SERVICES 305
#define FACILITY_AUDIOSTREAMING 1094
#define FACILITY_TTD 1490
#define FACILITY_ACCELERATOR 1536
#define FACILITY_WMAAECMA 1996
#define FACILITY_DIRECTMUSIC 2168
#define FACILITY_DIRECT3D10 2169
#define FACILITY_DXGI 2170
#define FACILITY_DXGI_DDI 2171
#define FACILITY_DIRECT3D11 2172
#define FACILITY_DIRECT3D11_DEBUG 2173
#define FACILITY_DIRECT3D12 2174
#define FACILITY_DIRECT3D12_DEBUG 2175
#define FACILITY_DXCORE 2176
#define FACILITY_PRESENTATION 2177
#define FACILITY_LEAP 2184
#define FACILITY_AUDCLNT 2185
#define FACILITY_WINCODEC_DWRITE_DWM 2200
#define FACILITY_WINML 2192
#define FACILITY_DIRECT2D 2201
#define FACILITY_DEFRAG 2304
#define FACILITY_USERMODE_SDBUS 2305
#define FACILITY_JSCRIPT 2306
#define FACILITY_PIDGENX 2561
#define FACILITY_EAS 85
#define FACILITY_WEB 885
#define FACILITY_WEB_SOCKET 886
#define FACILITY_MOBILE 1793
#define FACILITY_SQLITE 1967
#define FACILITY_SERVICE_FABRIC 1968
#define FACILITY_UTC 1989
#define FACILITY_WEP 2049
#define FACILITY_SYNCENGINE 2050
#define FACILITY_XBOX 2339
#define FACILITY_GAME 2340
#define FACILITY_USERMODE_UNIONFS 2341
#define FACILITY_USERMODE_PRM 2342
#define FACILITY_USERMODE_WIN_ACCEL 2343
#define FACILITY_PIX 2748

#define ERROR_SUCCESS __MSABI_LONG(0)
#define NO_ERROR __MSABI_LONG(0)
#define SEC_E_OK ((HRESULT)0x00000000)
#define ERROR_INVALID_FUNCTION __MSABI_LONG(1)
#define ERROR_FILE_NOT_FOUND __MSABI_LONG(2)
#define ERROR_PATH_NOT_FOUND __MSABI_LONG(3)
#define ERROR_TOO_MANY_OPEN_FILES __MSABI_LONG(4)
#define ERROR_ACCESS_DENIED __MSABI_LONG(5)
#define ERROR_INVALID_HANDLE __MSABI_LONG(6)
#define ERROR_ARENA_TRASHED __MSABI_LONG(7)
#define ERROR_NOT_ENOUGH_MEMORY __MSABI_LONG(8)
#define ERROR_INVALID_BLOCK __MSABI_LONG(9)
#define ERROR_BAD_ENVIRONMENT __MSABI_LONG(10)
#define ERROR_BAD_FORMAT __MSABI_LONG(11)
#define ERROR_INVALID_ACCESS __MSABI_LONG(12)
#define ERROR_INVALID_DATA __MSABI_LONG(13)
#define ERROR_OUTOFMEMORY __MSABI_LONG(14)
#define ERROR_INVALID_DRIVE __MSABI_LONG(15)
#define ERROR_CURRENT_DIRECTORY __MSABI_LONG(16)
#define ERROR_NOT_SAME_DEVICE __MSABI_LONG(17)
#define ERROR_NO_MORE_FILES __MSABI_LONG(18)
#define ERROR_WRITE_PROTECT __MSABI_LONG(19)
#define ERROR_BAD_UNIT __MSABI_LONG(20)
#define ERROR_NOT_READY __MSABI_LONG(21)
#define ERROR_BAD_COMMAND __MSABI_LONG(22)
#define ERROR_CRC __MSABI_LONG(23)
#define ERROR_BAD_LENGTH __MSABI_LONG(24)
#define ERROR_SEEK __MSABI_LONG(25)
#define ERROR_NOT_DOS_DISK __MSABI_LONG(26)
#define ERROR_SECTOR_NOT_FOUND __MSABI_LONG(27)
#define ERROR_OUT_OF_PAPER __MSABI_LONG(28)
#define ERROR_WRITE_FAULT __MSABI_LONG(29)
#define ERROR_READ_FAULT __MSABI_LONG(30)
#define ERROR_GEN_FAILURE __MSABI_LONG(31)
#define ERROR_SHARING_VIOLATION __MSABI_LONG(32)
#define ERROR_LOCK_VIOLATION __MSABI_LONG(33)
#define ERROR_WRONG_DISK __MSABI_LONG(34)
#define ERROR_SHARING_BUFFER_EXCEEDED __MSABI_LONG(36)
#define ERROR_HANDLE_EOF __MSABI_LONG(38)
#define ERROR_HANDLE_DISK_FULL __MSABI_LONG(39)
#define ERROR_NOT_SUPPORTED __MSABI_LONG(50)
#define ERROR_REM_NOT_LIST __MSABI_LONG(51)
#define ERROR_DUP_NAME __MSABI_LONG(52)
#define ERROR_BAD_NETPATH __MSABI_LONG(53)
#define ERROR_NETWORK_BUSY __MSABI_LONG(54)
#define ERROR_DEV_NOT_EXIST __MSABI_LONG(55)
#define ERROR_TOO_MANY_CMDS __MSABI_LONG(56)
#define ERROR_ADAP_HDW_ERR __MSABI_LONG(57)
#define ERROR_BAD_NET_RESP __MSABI_LONG(58)
#define ERROR_UNEXP_NET_ERR __MSABI_LONG(59)
#define ERROR_BAD_REM_ADAP __MSABI_LONG(60)
#define ERROR_PRINTQ_FULL __MSABI_LONG(61)
#define ERROR_NO_SPOOL_SPACE __MSABI_LONG(62)
#define ERROR_PRINT_CANCELLED __MSABI_LONG(63)
#define ERROR_NETNAME_DELETED __MSABI_LONG(64)
#define ERROR_NETWORK_ACCESS_DENIED __MSABI_LONG(65)
#define ERROR_BAD_DEV_TYPE __MSABI_LONG(66)
#define ERROR_BAD_NET_NAME __MSABI_LONG(67)
#define ERROR_TOO_MANY_NAMES __MSABI_LONG(68)
#define ERROR_TOO_MANY_SESS __MSABI_LONG(69)
#define ERROR_SHARING_PAUSED __MSABI_LONG(70)
#define ERROR_REQ_NOT_ACCEP __MSABI_LONG(71)
#define ERROR_REDIR_PAUSED __MSABI_LONG(72)
#define ERROR_FILE_EXISTS __MSABI_LONG(80)
#define ERROR_CANNOT_MAKE __MSABI_LONG(82)
#define ERROR_FAIL_I24 __MSABI_LONG(83)
#define ERROR_OUT_OF_STRUCTURES __MSABI_LONG(84)
#define ERROR_ALREADY_ASSIGNED __MSABI_LONG(85)
#define ERROR_INVALID_PASSWORD __MSABI_LONG(86)
#define ERROR_INVALID_PARAMETER __MSABI_LONG(87)
#define ERROR_NET_WRITE_FAULT __MSABI_LONG(88)
#define ERROR_NO_PROC_SLOTS __MSABI_LONG(89)
#define ERROR_TOO_MANY_SEMAPHORES __MSABI_LONG(100)
#define ERROR_EXCL_SEM_ALREADY_OWNED __MSABI_LONG(101)
#define ERROR_SEM_IS_SET __MSABI_LONG(102)
#define ERROR_TOO_MANY_SEM_REQUESTS __MSABI_LONG(103)
#define ERROR_INVALID_AT_INTERRUPT_TIME __MSABI_LONG(104)
#define ERROR_SEM_OWNER_DIED __MSABI_LONG(105)
#define ERROR_SEM_USER_LIMIT __MSABI_LONG(106)
#define ERROR_DISK_CHANGE __MSABI_LONG(107)
#define ERROR_DRIVE_LOCKED __MSABI_LONG(108)
#define ERROR_BROKEN_PIPE __MSABI_LONG(109)
#define ERROR_OPEN_FAILED __MSABI_LONG(110)
#define ERROR_BUFFER_OVERFLOW __MSABI_LONG(111)
#define ERROR_DISK_FULL __MSABI_LONG(112)
#define ERROR_NO_MORE_SEARCH_HANDLES __MSABI_LONG(113)
#define ERROR_INVALID_TARGET_HANDLE __MSABI_LONG(114)
#define ERROR_INVALID_CATEGORY __MSABI_LONG(117)
#define ERROR_INVALID_VERIFY_SWITCH __MSABI_LONG(118)
#define ERROR_BAD_DRIVER_LEVEL __MSABI_LONG(119)
#define ERROR_CALL_NOT_IMPLEMENTED __MSABI_LONG(120)
#define ERROR_SEM_TIMEOUT __MSABI_LONG(121)
#define ERROR_INSUFFICIENT_BUFFER __MSABI_LONG(122)
#define ERROR_INVALID_NAME __MSABI_LONG(123)
#define ERROR_INVALID_LEVEL __MSABI_LONG(124)
#define ERROR_NO_VOLUME_LABEL __MSABI_LONG(125)
#define ERROR_MOD_NOT_FOUND __MSABI_LONG(126)
#define ERROR_PROC_NOT_FOUND __MSABI_LONG(127)
#define ERROR_WAIT_NO_CHILDREN __MSABI_LONG(128)
#define ERROR_CHILD_NOT_COMPLETE __MSABI_LONG(129)
#define ERROR_DIRECT_ACCESS_HANDLE __MSABI_LONG(130)
#define ERROR_NEGATIVE_SEEK __MSABI_LONG(131)
#define ERROR_SEEK_ON_DEVICE __MSABI_LONG(132)
#define ERROR_IS_JOIN_TARGET __MSABI_LONG(133)
#define ERROR_IS_JOINED __MSABI_LONG(134)
#define ERROR_IS_SUBSTED __MSABI_LONG(135)
#define ERROR_NOT_JOINED __MSABI_LONG(136)
#define ERROR_NOT_SUBSTED __MSABI_LONG(137)
#define ERROR_JOIN_TO_JOIN __MSABI_LONG(138)
#define ERROR_SUBST_TO_SUBST __MSABI_LONG(139)
#define ERROR_JOIN_TO_SUBST __MSABI_LONG(140)
#define ERROR_SUBST_TO_JOIN __MSABI_LONG(141)
#define ERROR_BUSY_DRIVE __MSABI_LONG(142)
#define ERROR_SAME_DRIVE __MSABI_LONG(143)
#define ERROR_DIR_NOT_ROOT __MSABI_LONG(144)
#define ERROR_DIR_NOT_EMPTY __MSABI_LONG(145)
#define ERROR_IS_SUBST_PATH __MSABI_LONG(146)
#define ERROR_IS_JOIN_PATH __MSABI_LONG(147)
#define ERROR_PATH_BUSY __MSABI_LONG(148)
#define ERROR_IS_SUBST_TARGET __MSABI_LONG(149)
#define ERROR_SYSTEM_TRACE __MSABI_LONG(150)
#define ERROR_INVALID_EVENT_COUNT __MSABI_LONG(151)
#define ERROR_TOO_MANY_MUXWAITERS __MSABI_LONG(152)
#define ERROR_INVALID_LIST_FORMAT __MSABI_LONG(153)
#define ERROR_LABEL_TOO_LONG __MSABI_LONG(154)
#define ERROR_TOO_MANY_TCBS __MSABI_LONG(155)
#define ERROR_SIGNAL_REFUSED __MSABI_LONG(156)
#define ERROR_DISCARDED __MSABI_LONG(157)
#define ERROR_NOT_LOCKED __MSABI_LONG(158)
#define ERROR_BAD_THREADID_ADDR __MSABI_LONG(159)
#define ERROR_BAD_ARGUMENTS __MSABI_LONG(160)
#define ERROR_BAD_PATHNAME __MSABI_LONG(161)
#define ERROR_SIGNAL_PENDING __MSABI_LONG(162)
#define ERROR_MAX_THRDS_REACHED __MSABI_LONG(164)
#define ERROR_LOCK_FAILED __MSABI_LONG(167)
#define ERROR_BUSY __MSABI_LONG(170)
#define ERROR_DEVICE_SUPPORT_IN_PROGRESS __MSABI_LONG(171)
#define ERROR_CANCEL_VIOLATION __MSABI_LONG(173)
#define ERROR_ATOMIC_LOCKS_NOT_SUPPORTED __MSABI_LONG(174)
#define ERROR_INVALID_SEGMENT_NUMBER __MSABI_LONG(180)
#define ERROR_INVALID_ORDINAL __MSABI_LONG(182)
#define ERROR_ALREADY_EXISTS __MSABI_LONG(183)
#define ERROR_INVALID_FLAG_NUMBER __MSABI_LONG(186)
#define ERROR_SEM_NOT_FOUND __MSABI_LONG(187)
#define ERROR_INVALID_STARTING_CODESEG __MSABI_LONG(188)
#define ERROR_INVALID_STACKSEG __MSABI_LONG(189)
#define ERROR_INVALID_MODULETYPE __MSABI_LONG(190)
#define ERROR_INVALID_EXE_SIGNATURE __MSABI_LONG(191)
#define ERROR_EXE_MARKED_INVALID __MSABI_LONG(192)
#define ERROR_BAD_EXE_FORMAT __MSABI_LONG(193)
#define ERROR_ITERATED_DATA_EXCEEDS_64k __MSABI_LONG(194)
#define ERROR_INVALID_MINALLOCSIZE __MSABI_LONG(195)
#define ERROR_DYNLINK_FROM_INVALID_RING __MSABI_LONG(196)
#define ERROR_IOPL_NOT_ENABLED __MSABI_LONG(197)
#define ERROR_INVALID_SEGDPL __MSABI_LONG(198)
#define ERROR_AUTODATASEG_EXCEEDS_64k __MSABI_LONG(199)
#define ERROR_RING2SEG_MUST_BE_MOVABLE __MSABI_LONG(200)
#define ERROR_RELOC_CHAIN_XEEDS_SEGLIM __MSABI_LONG(201)
#define ERROR_INFLOOP_IN_RELOC_CHAIN __MSABI_LONG(202)
#define ERROR_ENVVAR_NOT_FOUND __MSABI_LONG(203)
#define ERROR_NO_SIGNAL_SENT __MSABI_LONG(205)
#define ERROR_FILENAME_EXCED_RANGE __MSABI_LONG(206)
#define ERROR_RING2_STACK_IN_USE __MSABI_LONG(207)
#define ERROR_META_EXPANSION_TOO_LONG __MSABI_LONG(208)
#define ERROR_INVALID_SIGNAL_NUMBER __MSABI_LONG(209)
#define ERROR_THREAD_1_INACTIVE __MSABI_LONG(210)
#define ERROR_LOCKED __MSABI_LONG(212)
#define ERROR_TOO_MANY_MODULES __MSABI_LONG(214)
#define ERROR_NESTING_NOT_ALLOWED __MSABI_LONG(215)
#define ERROR_EXE_MACHINE_TYPE_MISMATCH __MSABI_LONG(216)
#define ERROR_EXE_CANNOT_MODIFY_SIGNED_BINARY __MSABI_LONG(217)
#define ERROR_EXE_CANNOT_MODIFY_STRONG_SIGNED_BINARY __MSABI_LONG(218)
#define ERROR_FILE_CHECKED_OUT __MSABI_LONG(220)
#define ERROR_CHECKOUT_REQUIRED __MSABI_LONG(221)
#define ERROR_BAD_FILE_TYPE __MSABI_LONG(222)
#define ERROR_FILE_TOO_LARGE __MSABI_LONG(223)
#define ERROR_FORMS_AUTH_REQUIRED __MSABI_LONG(224)
#define ERROR_VIRUS_INFECTED __MSABI_LONG(225)
#define ERROR_VIRUS_DELETED __MSABI_LONG(226)
#define ERROR_PIPE_LOCAL __MSABI_LONG(229)
#define ERROR_BAD_PIPE __MSABI_LONG(230)
#define ERROR_PIPE_BUSY __MSABI_LONG(231)
#define ERROR_NO_DATA __MSABI_LONG(232)
#define ERROR_PIPE_NOT_CONNECTED __MSABI_LONG(233)
#define ERROR_MORE_DATA __MSABI_LONG(234)
#define ERROR_NO_WORK_DONE __MSABI_LONG(235)
#define ERROR_VC_DISCONNECTED __MSABI_LONG(240)
#define ERROR_INVALID_EA_NAME __MSABI_LONG(254)
#define ERROR_EA_LIST_INCONSISTENT __MSABI_LONG(255)
#define WAIT_TIMEOUT __MSABI_LONG(258)
#define ERROR_NO_MORE_ITEMS __MSABI_LONG(259)
#define ERROR_CANNOT_COPY __MSABI_LONG(266)
#define ERROR_DIRECTORY __MSABI_LONG(267)
#define ERROR_EAS_DIDNT_FIT __MSABI_LONG(275)
#define ERROR_EA_FILE_CORRUPT __MSABI_LONG(276)
#define ERROR_EA_TABLE_FULL __MSABI_LONG(277)
#define ERROR_INVALID_EA_HANDLE __MSABI_LONG(278)
#define ERROR_EAS_NOT_SUPPORTED __MSABI_LONG(282)
#define ERROR_NOT_OWNER __MSABI_LONG(288)
#define ERROR_TOO_MANY_POSTS __MSABI_LONG(298)
#define ERROR_PARTIAL_COPY __MSABI_LONG(299)
#define ERROR_OPLOCK_NOT_GRANTED __MSABI_LONG(300)
#define ERROR_INVALID_OPLOCK_PROTOCOL __MSABI_LONG(301)
#define ERROR_DISK_TOO_FRAGMENTED __MSABI_LONG(302)
#define ERROR_DELETE_PENDING __MSABI_LONG(303)
#define ERROR_INCOMPATIBLE_WITH_GLOBAL_SHORT_NAME_REGISTRY_SETTING __MSABI_LONG(304)
#define ERROR_SHORT_NAMES_NOT_ENABLED_ON_VOLUME __MSABI_LONG(305)
#define ERROR_SECURITY_STREAM_IS_INCONSISTENT __MSABI_LONG(306)
#define ERROR_INVALID_LOCK_RANGE __MSABI_LONG(307)
#define ERROR_IMAGE_SUBSYSTEM_NOT_PRESENT __MSABI_LONG(308)
#define ERROR_NOTIFICATION_GUID_ALREADY_DEFINED __MSABI_LONG(309)
#define ERROR_INVALID_EXCEPTION_HANDLER __MSABI_LONG(310)
#define ERROR_DUPLICATE_PRIVILEGES __MSABI_LONG(311)
#define ERROR_NO_RANGES_PROCESSED __MSABI_LONG(312)
#define ERROR_NOT_ALLOWED_ON_SYSTEM_FILE __MSABI_LONG(313)
#define ERROR_DISK_RESOURCES_EXHAUSTED __MSABI_LONG(314)
#define ERROR_INVALID_TOKEN __MSABI_LONG(315)
#define ERROR_DEVICE_FEATURE_NOT_SUPPORTED __MSABI_LONG(316)
#define ERROR_MR_MID_NOT_FOUND __MSABI_LONG(317)
#define ERROR_SCOPE_NOT_FOUND __MSABI_LONG(318)
#define ERROR_UNDEFINED_SCOPE __MSABI_LONG(319)
#define ERROR_INVALID_CAP __MSABI_LONG(320)
#define ERROR_DEVICE_UNREACHABLE __MSABI_LONG(321)
#define ERROR_DEVICE_NO_RESOURCES __MSABI_LONG(322)
#define ERROR_DATA_CHECKSUM_ERROR __MSABI_LONG(323)
#define ERROR_INTERMIXED_KERNEL_EA_OPERATION __MSABI_LONG(324)
#define ERROR_FILE_LEVEL_TRIM_NOT_SUPPORTED __MSABI_LONG(326)
#define ERROR_OFFSET_ALIGNMENT_VIOLATION __MSABI_LONG(327)
#define ERROR_INVALID_FIELD_IN_PARAMETER_LIST __MSABI_LONG(328)
#define ERROR_OPERATION_IN_PROGRESS __MSABI_LONG(329)
#define ERROR_BAD_DEVICE_PATH __MSABI_LONG(330)
#define ERROR_TOO_MANY_DESCRIPTORS __MSABI_LONG(331)
#define ERROR_SCRUB_DATA_DISABLED __MSABI_LONG(332)
#define ERROR_NOT_REDUNDANT_STORAGE __MSABI_LONG(333)
#define ERROR_RESIDENT_FILE_NOT_SUPPORTED __MSABI_LONG(334)
#define ERROR_COMPRESSED_FILE_NOT_SUPPORTED __MSABI_LONG(335)
#define ERROR_DIRECTORY_NOT_SUPPORTED __MSABI_LONG(336)
#define ERROR_NOT_READ_FROM_COPY __MSABI_LONG(337)
#define ERROR_FT_WRITE_FAILURE __MSABI_LONG(338)
#define ERROR_FT_DI_SCAN_REQUIRED __MSABI_LONG(339)
#define ERROR_INVALID_KERNEL_INFO_VERSION __MSABI_LONG(340)
#define ERROR_INVALID_PEP_INFO_VERSION __MSABI_LONG(341)
#define ERROR_OBJECT_NOT_EXTERNALLY_BACKED __MSABI_LONG(342)
#define ERROR_EXTERNAL_BACKING_PROVIDER_UNKNOWN __MSABI_LONG(343)
#define ERROR_COMPRESSION_NOT_BENEFICIAL __MSABI_LONG(344)
#define ERROR_STORAGE_TOPOLOGY_ID_MISMATCH __MSABI_LONG(345)
#define ERROR_BLOCKED_BY_PARENTAL_CONTROLS __MSABI_LONG(346)
#define ERROR_BLOCK_TOO_MANY_REFERENCES __MSABI_LONG(347)
#define ERROR_MARKED_TO_DISALLOW_WRITES __MSABI_LONG(348)
#define ERROR_ENCLAVE_FAILURE __MSABI_LONG(349)
#define ERROR_FAIL_NOACTION_REBOOT __MSABI_LONG(350)
#define ERROR_FAIL_SHUTDOWN __MSABI_LONG(351)
#define ERROR_FAIL_RESTART __MSABI_LONG(352)
#define ERROR_MAX_SESSIONS_REACHED __MSABI_LONG(353)
#define ERROR_NETWORK_ACCESS_DENIED_EDP __MSABI_LONG(354)
#define ERROR_DEVICE_HINT_NAME_BUFFER_TOO_SMALL __MSABI_LONG(355)
#define ERROR_EDP_POLICY_DENIES_OPERATION __MSABI_LONG(356)
#define ERROR_EDP_DPL_POLICY_CANT_BE_SATISFIED __MSABI_LONG(357)
#define ERROR_CLOUD_FILE_SYNC_ROOT_METADATA_CORRUPT __MSABI_LONG(358)
#define ERROR_DEVICE_IN_MAINTENANCE __MSABI_LONG(359)
#define ERROR_NOT_SUPPORTED_ON_DAX __MSABI_LONG(360)
#define ERROR_DAX_MAPPING_EXISTS __MSABI_LONG(361)
#define ERROR_CLOUD_FILE_PROVIDER_NOT_RUNNING __MSABI_LONG(362)
#define ERROR_CLOUD_FILE_METADATA_CORRUPT __MSABI_LONG(363)
#define ERROR_CLOUD_FILE_METADATA_TOO_LARGE __MSABI_LONG(364)
#define ERROR_CLOUD_FILE_PROPERTY_BLOB_TOO_LARGE __MSABI_LONG(365)
#define ERROR_CLOUD_FILE_PROPERTY_BLOB_CHECKSUM_MISMATCH __MSABI_LONG(366)
#define ERROR_CHILD_PROCESS_BLOCKED __MSABI_LONG(367)
#define ERROR_STORAGE_LOST_DATA_PERSISTENCE __MSABI_LONG(368)
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_UNAVAILABLE __MSABI_LONG(369)
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_METADATA_CORRUPT __MSABI_LONG(370)
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_BUSY __MSABI_LONG(371)
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_PROVIDER_UNKNOWN __MSABI_LONG(372)
#define ERROR_GDI_HANDLE_LEAK __MSABI_LONG(373)
#define ERROR_CLOUD_FILE_TOO_MANY_PROPERTY_BLOBS __MSABI_LONG(374)
#define ERROR_CLOUD_FILE_PROPERTY_VERSION_NOT_SUPPORTED __MSABI_LONG(375)
#define ERROR_NOT_A_CLOUD_FILE __MSABI_LONG(376)
#define ERROR_CLOUD_FILE_NOT_IN_SYNC __MSABI_LONG(377)
#define ERROR_CLOUD_FILE_ALREADY_CONNECTED __MSABI_LONG(378)
#define ERROR_CLOUD_FILE_NOT_SUPPORTED __MSABI_LONG(379)
#define ERROR_CLOUD_FILE_INVALID_REQUEST __MSABI_LONG(380)
#define ERROR_CLOUD_FILE_READ_ONLY_VOLUME __MSABI_LONG(381)
#define ERROR_CLOUD_FILE_CONNECTED_PROVIDER_ONLY __MSABI_LONG(382)
#define ERROR_CLOUD_FILE_VALIDATION_FAILED __MSABI_LONG(383)
#define ERROR_SMB1_NOT_AVAILABLE __MSABI_LONG(384)
#define ERROR_FILE_SYSTEM_VIRTUALIZATION_INVALID_OPERATION __MSABI_LONG(385)
#define ERROR_CLOUD_FILE_AUTHENTICATION_FAILED __MSABI_LONG(386)
#define ERROR_CLOUD_FILE_INSUFFICIENT_RESOURCES __MSABI_LONG(387)
#define ERROR_CLOUD_FILE_NETWORK_UNAVAILABLE __MSABI_LONG(388)
#define ERROR_CLOUD_FILE_UNSUCCESSFUL __MSABI_LONG(389)
#define ERROR_CLOUD_FILE_NOT_UNDER_SYNC_ROOT __MSABI_LONG(390)
#define ERROR_CLOUD_FILE_IN_USE __MSABI_LONG(391)
#define ERROR_CLOUD_FILE_PINNED __MSABI_LONG(392)
#define ERROR_CLOUD_FILE_REQUEST_ABORTED __MSABI_LONG(393)
#define ERROR_CLOUD_FILE_PROPERTY_CORRUPT __MSABI_LONG(394)
#define ERROR_CLOUD_FILE_ACCESS_DENIED __MSABI_LONG(395)
#define ERROR_CLOUD_FILE_INCOMPATIBLE_HARDLINKS __MSABI_LONG(396)
#define ERROR_CLOUD_FILE_PROPERTY_LOCK_CONFLICT __MSABI_LONG(397)
#define ERROR_CLOUD_FILE_REQUEST_CANCELED __MSABI_LONG(398)
#define ERROR_EXTERNAL_SYSKEY_NOT_SUPPORTED __MSABI_LONG(399)
#define ERROR_THREAD_MODE_ALREADY_BACKGROUND __MSABI_LONG(400)
#define ERROR_THREAD_MODE_NOT_BACKGROUND __MSABI_LONG(401)
#define ERROR_PROCESS_MODE_ALREADY_BACKGROUND __MSABI_LONG(402)
#define ERROR_PROCESS_MODE_NOT_BACKGROUND __MSABI_LONG(403)
#define ERROR_CLOUD_FILE_PROVIDER_TERMINATED __MSABI_LONG(404)
#define ERROR_NOT_A_CLOUD_SYNC_ROOT __MSABI_LONG(405)
#define ERROR_FILE_PROTECTED_UNDER_DPL __MSABI_LONG(406)
#define ERROR_VOLUME_NOT_CLUSTER_ALIGNED __MSABI_LONG(407)
#define ERROR_NO_PHYSICALLY_ALIGNED_FREE_SPACE_FOUND __MSABI_LONG(408)
#define ERROR_APPX_FILE_NOT_ENCRYPTED __MSABI_LONG(409)
#define ERROR_RWRAW_ENCRYPTED_FILE_NOT_ENCRYPTED __MSABI_LONG(410)
#define ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_FILEOFFSET __MSABI_LONG(411)
#define ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_FILERANGE __MSABI_LONG(412)
#define ERROR_RWRAW_ENCRYPTED_INVALID_EDATAINFO_PARAMETER __MSABI_LONG(413)
#define ERROR_LINUX_SUBSYSTEM_NOT_PRESENT __MSABI_LONG(414)
#define ERROR_FT_READ_FAILURE __MSABI_LONG(415)
#define ERROR_STORAGE_RESERVE_ID_INVALID __MSABI_LONG(416)
#define ERROR_STORAGE_RESERVE_DOES_NOT_EXIST __MSABI_LONG(417)
#define ERROR_STORAGE_RESERVE_ALREADY_EXISTS __MSABI_LONG(418)
#define ERROR_STORAGE_RESERVE_NOT_EMPTY __MSABI_LONG(419)
#define ERROR_NOT_A_DAX_VOLUME __MSABI_LONG(420)
#define ERROR_NOT_DAX_MAPPABLE __MSABI_LONG(421)
#define ERROR_TIME_SENSITIVE_THREAD __MSABI_LONG(422)
#define ERROR_DPL_NOT_SUPPORTED_FOR_USER __MSABI_LONG(423)
#define ERROR_CASE_DIFFERING_NAMES_IN_DIR __MSABI_LONG(424)
#define ERROR_FILE_NOT_SUPPORTED __MSABI_LONG(425)
#define ERROR_CLOUD_FILE_REQUEST_TIMEOUT __MSABI_LONG(426)
#define ERROR_NO_TASK_QUEUE __MSABI_LONG(427)
#define ERROR_SRC_SRV_DLL_LOAD_FAILED __MSABI_LONG(428)
#define ERROR_NOT_SUPPORTED_WITH_BTT __MSABI_LONG(429)
#define ERROR_ENCRYPTION_DISABLED __MSABI_LONG(430)
#define ERROR_ENCRYPTING_METADATA_DISALLOWED __MSABI_LONG(431)
#define ERROR_CANT_CLEAR_ENCRYPTION_FLAG __MSABI_LONG(432)
#define ERROR_NO_SUCH_DEVICE __MSABI_LONG(433)
#define ERROR_CLOUD_FILE_DEHYDRATION_DISALLOWED __MSABI_LONG(434)
#define ERROR_FILE_SNAP_IN_PROGRESS __MSABI_LONG(435)
#define ERROR_FILE_SNAP_USER_SECTION_NOT_SUPPORTED __MSABI_LONG(436)
#define ERROR_FILE_SNAP_MODIFY_NOT_SUPPORTED __MSABI_LONG(437)
#define ERROR_FILE_SNAP_IO_NOT_COORDINATED __MSABI_LONG(438)
#define ERROR_FILE_SNAP_UNEXPECTED_ERROR __MSABI_LONG(439)
#define ERROR_FILE_SNAP_INVALID_PARAMETER __MSABI_LONG(440)
#define ERROR_UNSATISFIED_DEPENDENCIES __MSABI_LONG(441)
#define ERROR_CASE_SENSITIVE_PATH __MSABI_LONG(442)
#define ERROR_UNEXPECTED_NTCACHEMANAGER_ERROR __MSABI_LONG(443)
#define ERROR_LINUX_SUBSYSTEM_UPDATE_REQUIRED __MSABI_LONG(444)
#define ERROR_DLP_POLICY_WARNS_AGAINST_OPERATION __MSABI_LONG(445)
#define ERROR_DLP_POLICY_DENIES_OPERATION __MSABI_LONG(446)
#define ERROR_SECURITY_DENIES_OPERATION __MSABI_LONG(447)
#define ERROR_UNTRUSTED_MOUNT_POINT __MSABI_LONG(448)
#define ERROR_DLP_POLICY_SILENTLY_FAIL __MSABI_LONG(449)
#define ERROR_CAPAUTHZ_NOT_DEVUNLOCKED __MSABI_LONG(450)
#define ERROR_CAPAUTHZ_CHANGE_TYPE __MSABI_LONG(451)
#define ERROR_CAPAUTHZ_NOT_PROVISIONED __MSABI_LONG(452)
#define ERROR_CAPAUTHZ_NOT_AUTHORIZED __MSABI_LONG(453)
#define ERROR_CAPAUTHZ_NO_POLICY __MSABI_LONG(454)
#define ERROR_CAPAUTHZ_DB_CORRUPTED __MSABI_LONG(455)
#define ERROR_CAPAUTHZ_SCCD_INVALID_CATALOG __MSABI_LONG(456)
#define ERROR_CAPAUTHZ_SCCD_NO_AUTH_ENTITY __MSABI_LONG(457)
#define ERROR_CAPAUTHZ_SCCD_PARSE_ERROR __MSABI_LONG(458)
#define ERROR_CAPAUTHZ_SCCD_DEV_MODE_REQUIRED __MSABI_LONG(459)
#define ERROR_CAPAUTHZ_SCCD_NO_CAPABILITY_MATCH __MSABI_LONG(460)
#define ERROR_CIMFS_IMAGE_CORRUPT __MSABI_LONG(470)
#define ERROR_CIMFS_IMAGE_VERSION_NOT_SUPPORTED __MSABI_LONG(471)
#define ERROR_STORAGE_STACK_ACCESS_DENIED __MSABI_LONG(472)
#define ERROR_INSUFFICIENT_VIRTUAL_ADDR_RESOURCES __MSABI_LONG(473)
#define ERROR_INDEX_OUT_OF_BOUNDS __MSABI_LONG(474)
#define ERROR_CLOUD_FILE_US_MESSAGE_TIMEOUT __MSABI_LONG(475)
#define ERROR_NOT_A_DEV_VOLUME __MSABI_LONG(476)
#define ERROR_FS_GUID_MISMATCH __MSABI_LONG(477)
#define ERROR_CANT_ATTACH_TO_DEV_VOLUME __MSABI_LONG(478)
#define ERROR_MEMORY_DECOMPRESSION_FAILURE __MSABI_LONG(479)
#define ERROR_PNP_QUERY_REMOVE_DEVICE_TIMEOUT __MSABI_LONG(480)
#define ERROR_PNP_QUERY_REMOVE_RELATED_DEVICE_TIMEOUT __MSABI_LONG(481)
#define ERROR_PNP_QUERY_REMOVE_UNRELATED_DEVICE_TIMEOUT __MSABI_LONG(482)
#define ERROR_DEVICE_HARDWARE_ERROR __MSABI_LONG(483)
#define ERROR_INVALID_ADDRESS __MSABI_LONG(487)
#define ERROR_HAS_SYSTEM_CRITICAL_FILES __MSABI_LONG(488)
#define ERROR_ENCRYPTED_FILE_NOT_SUPPORTED __MSABI_LONG(489)
#define ERROR_SPARSE_FILE_NOT_SUPPORTED __MSABI_LONG(490)
#define ERROR_PAGEFILE_NOT_SUPPORTED __MSABI_LONG(491)
#define ERROR_VOLUME_NOT_SUPPORTED __MSABI_LONG(492)
#define ERROR_NOT_SUPPORTED_WITH_BYPASSIO __MSABI_LONG(493)
#define ERROR_NO_BYPASSIO_DRIVER_SUPPORT __MSABI_LONG(494)
#define ERROR_NOT_SUPPORTED_WITH_ENCRYPTION __MSABI_LONG(495)
#define ERROR_NOT_SUPPORTED_WITH_COMPRESSION __MSABI_LONG(496)
#define ERROR_NOT_SUPPORTED_WITH_REPLICATION __MSABI_LONG(497)
#define ERROR_NOT_SUPPORTED_WITH_DEDUPLICATION __MSABI_LONG(498)
#define ERROR_NOT_SUPPORTED_WITH_AUDITING __MSABI_LONG(499)
#define ERROR_USER_PROFILE_LOAD __MSABI_LONG(500)
#define ERROR_SESSION_KEY_TOO_SHORT __MSABI_LONG(501)
#define ERROR_ACCESS_DENIED_APPDATA __MSABI_LONG(502)
#define ERROR_NOT_SUPPORTED_WITH_MONITORING __MSABI_LONG(503)
#define ERROR_NOT_SUPPORTED_WITH_SNAPSHOT __MSABI_LONG(504)
#define ERROR_NOT_SUPPORTED_WITH_VIRTUALIZATION __MSABI_LONG(505)
#define ERROR_BYPASSIO_FLT_NOT_SUPPORTED __MSABI_LONG(506)
#define ERROR_DEVICE_RESET_REQUIRED __MSABI_LONG(507)
#define ERROR_VOLUME_WRITE_ACCESS_DENIED __MSABI_LONG(508)
#define ERROR_NOT_SUPPORTED_WITH_CACHED_HANDLE __MSABI_LONG(509)
#define ERROR_FS_METADATA_INCONSISTENT __MSABI_LONG(510)
#define ERROR_BLOCK_WEAK_REFERENCE_INVALID __MSABI_LONG(511)
#define ERROR_BLOCK_SOURCE_WEAK_REFERENCE_INVALID __MSABI_LONG(512)
#define ERROR_BLOCK_TARGET_WEAK_REFERENCE_INVALID __MSABI_LONG(513)
#define ERROR_BLOCK_SHARED __MSABI_LONG(514)
#define ERROR_VOLUME_UPGRADE_NOT_NEEDED __MSABI_LONG(515)
#define ERROR_VOLUME_UPGRADE_PENDING __MSABI_LONG(516)
#define ERROR_VOLUME_UPGRADE_DISABLED __MSABI_LONG(517)
#define ERROR_VOLUME_UPGRADE_DISABLED_TILL_OS_DOWNGRADE_EXPIRED __MSABI_LONG(518)
#define ERROR_INVALID_CONFIG_VALUE __MSABI_LONG(519)
#define ERROR_MEMORY_DECOMPRESSION_HW_ERROR __MSABI_LONG(520)
#define ERROR_VOLUME_ROLLBACK_DETECTED __MSABI_LONG(521)
#define ERROR_ARITHMETIC_OVERFLOW __MSABI_LONG(534)
#define ERROR_PIPE_CONNECTED __MSABI_LONG(535)
#define ERROR_PIPE_LISTENING __MSABI_LONG(536)
#define ERROR_VERIFIER_STOP __MSABI_LONG(537)
#define ERROR_ABIOS_ERROR __MSABI_LONG(538)
#define ERROR_WX86_WARNING __MSABI_LONG(539)
#define ERROR_WX86_ERROR __MSABI_LONG(540)
#define ERROR_TIMER_NOT_CANCELED __MSABI_LONG(541)
#define ERROR_UNWIND __MSABI_LONG(542)
#define ERROR_BAD_STACK __MSABI_LONG(543)
#define ERROR_INVALID_UNWIND_TARGET __MSABI_LONG(544)
#define ERROR_INVALID_PORT_ATTRIBUTES __MSABI_LONG(545)
#define ERROR_PORT_MESSAGE_TOO_LONG __MSABI_LONG(546)
#define ERROR_INVALID_QUOTA_LOWER __MSABI_LONG(547)
#define ERROR_DEVICE_ALREADY_ATTACHED __MSABI_LONG(548)
#define ERROR_INSTRUCTION_MISALIGNMENT __MSABI_LONG(549)
#define ERROR_PROFILING_NOT_STARTED __MSABI_LONG(550)
#define ERROR_PROFILING_NOT_STOPPED __MSABI_LONG(551)
#define ERROR_COULD_NOT_INTERPRET __MSABI_LONG(552)
#define ERROR_PROFILING_AT_LIMIT __MSABI_LONG(553)
#define ERROR_CANT_WAIT __MSABI_LONG(554)
#define ERROR_CANT_TERMINATE_SELF __MSABI_LONG(555)
#define ERROR_UNEXPECTED_MM_CREATE_ERR __MSABI_LONG(556)
#define ERROR_UNEXPECTED_MM_MAP_ERROR __MSABI_LONG(557)
#define ERROR_UNEXPECTED_MM_EXTEND_ERR __MSABI_LONG(558)
#define ERROR_BAD_FUNCTION_TABLE __MSABI_LONG(559)
#define ERROR_NO_GUID_TRANSLATION __MSABI_LONG(560)
#define ERROR_INVALID_LDT_SIZE __MSABI_LONG(561)
#define ERROR_INVALID_LDT_OFFSET __MSABI_LONG(563)
#define ERROR_INVALID_LDT_DESCRIPTOR __MSABI_LONG(564)
#define ERROR_TOO_MANY_THREADS __MSABI_LONG(565)
#define ERROR_THREAD_NOT_IN_PROCESS __MSABI_LONG(566)
#define ERROR_PAGEFILE_QUOTA_EXCEEDED __MSABI_LONG(567)
#define ERROR_LOGON_SERVER_CONFLICT __MSABI_LONG(568)
#define ERROR_SYNCHRONIZATION_REQUIRED __MSABI_LONG(569)
#define ERROR_NET_OPEN_FAILED __MSABI_LONG(570)
#define ERROR_IO_PRIVILEGE_FAILED __MSABI_LONG(571)
#define ERROR_CONTROL_C_EXIT __MSABI_LONG(572)
#define ERROR_MISSING_SYSTEMFILE __MSABI_LONG(573)
#define ERROR_UNHANDLED_EXCEPTION __MSABI_LONG(574)
#define ERROR_APP_INIT_FAILURE __MSABI_LONG(575)
#define ERROR_PAGEFILE_CREATE_FAILED __MSABI_LONG(576)
#define ERROR_INVALID_IMAGE_HASH __MSABI_LONG(577)
#define ERROR_NO_PAGEFILE __MSABI_LONG(578)
#define ERROR_ILLEGAL_FLOAT_CONTEXT __MSABI_LONG(579)
#define ERROR_NO_EVENT_PAIR __MSABI_LONG(580)
#define ERROR_DOMAIN_CTRLR_CONFIG_ERROR __MSABI_LONG(581)
#define ERROR_ILLEGAL_CHARACTER __MSABI_LONG(582)
#define ERROR_UNDEFINED_CHARACTER __MSABI_LONG(583)
#define ERROR_FLOPPY_VOLUME __MSABI_LONG(584)
#define ERROR_BIOS_FAILED_TO_CONNECT_INTERRUPT __MSABI_LONG(585)
#define ERROR_BACKUP_CONTROLLER __MSABI_LONG(586)
#define ERROR_MUTANT_LIMIT_EXCEEDED __MSABI_LONG(587)
#define ERROR_FS_DRIVER_REQUIRED __MSABI_LONG(588)
#define ERROR_CANNOT_LOAD_REGISTRY_FILE __MSABI_LONG(589)
#define ERROR_DEBUG_ATTACH_FAILED __MSABI_LONG(590)
#define ERROR_SYSTEM_PROCESS_TERMINATED __MSABI_LONG(591)
#define ERROR_DATA_NOT_ACCEPTED __MSABI_LONG(592)
#define ERROR_VDM_HARD_ERROR __MSABI_LONG(593)
#define ERROR_DRIVER_CANCEL_TIMEOUT __MSABI_LONG(594)
#define ERROR_REPLY_MESSAGE_MISMATCH __MSABI_LONG(595)
#define ERROR_LOST_WRITEBEHIND_DATA __MSABI_LONG(596)
#define ERROR_CLIENT_SERVER_PARAMETERS_INVALID __MSABI_LONG(597)
#define ERROR_NOT_TINY_STREAM __MSABI_LONG(598)
#define ERROR_STACK_OVERFLOW_READ __MSABI_LONG(599)
#define ERROR_CONVERT_TO_LARGE __MSABI_LONG(600)
#define ERROR_FOUND_OUT_OF_SCOPE __MSABI_LONG(601)
#define ERROR_ALLOCATE_BUCKET __MSABI_LONG(602)
#define ERROR_MARSHALL_OVERFLOW __MSABI_LONG(603)
#define ERROR_INVALID_VARIANT __MSABI_LONG(604)
#define ERROR_BAD_COMPRESSION_BUFFER __MSABI_LONG(605)
#define ERROR_AUDIT_FAILED __MSABI_LONG(606)
#define ERROR_TIMER_RESOLUTION_NOT_SET __MSABI_LONG(607)
#define ERROR_INSUFFICIENT_LOGON_INFO __MSABI_LONG(608)
#define ERROR_BAD_DLL_ENTRYPOINT __MSABI_LONG(609)
#define ERROR_BAD_SERVICE_ENTRYPOINT __MSABI_LONG(610)
#define ERROR_IP_ADDRESS_CONFLICT1 __MSABI_LONG(611)
#define ERROR_IP_ADDRESS_CONFLICT2 __MSABI_LONG(612)
#define ERROR_REGISTRY_QUOTA_LIMIT __MSABI_LONG(613)
#define ERROR_NO_CALLBACK_ACTIVE __MSABI_LONG(614)
#define ERROR_PWD_TOO_SHORT __MSABI_LONG(615)
#define ERROR_PWD_TOO_RECENT __MSABI_LONG(616)
#define ERROR_PWD_HISTORY_CONFLICT __MSABI_LONG(617)
#define ERROR_UNSUPPORTED_COMPRESSION __MSABI_LONG(618)
#define ERROR_INVALID_HW_PROFILE __MSABI_LONG(619)
#define ERROR_INVALID_PLUGPLAY_DEVICE_PATH __MSABI_LONG(620)
#define ERROR_QUOTA_LIST_INCONSISTENT __MSABI_LONG(621)
#define ERROR_EVALUATION_EXPIRATION __MSABI_LONG(622)
#define ERROR_ILLEGAL_DLL_RELOCATION __MSABI_LONG(623)
#define ERROR_DLL_INIT_FAILED_LOGOFF __MSABI_LONG(624)
#define ERROR_VALIDATE_CONTINUE __MSABI_LONG(625)
#define ERROR_NO_MORE_MATCHES __MSABI_LONG(626)
#define ERROR_RANGE_LIST_CONFLICT __MSABI_LONG(627)
#define ERROR_SERVER_SID_MISMATCH __MSABI_LONG(628)
#define ERROR_CANT_ENABLE_DENY_ONLY __MSABI_LONG(629)
#define ERROR_FLOAT_MULTIPLE_FAULTS __MSABI_LONG(630)
#define ERROR_FLOAT_MULTIPLE_TRAPS __MSABI_LONG(631)
#define ERROR_NOINTERFACE __MSABI_LONG(632)
#define ERROR_DRIVER_FAILED_SLEEP __MSABI_LONG(633)
#define ERROR_CORRUPT_SYSTEM_FILE __MSABI_LONG(634)
#define ERROR_COMMITMENT_MINIMUM __MSABI_LONG(635)
#define ERROR_PNP_RESTART_ENUMERATION __MSABI_LONG(636)
#define ERROR_SYSTEM_IMAGE_BAD_SIGNATURE __MSABI_LONG(637)
#define ERROR_PNP_REBOOT_REQUIRED __MSABI_LONG(638)
#define ERROR_INSUFFICIENT_POWER __MSABI_LONG(639)
#define ERROR_MULTIPLE_FAULT_VIOLATION __MSABI_LONG(640)
#define ERROR_SYSTEM_SHUTDOWN __MSABI_LONG(641)
#define ERROR_PORT_NOT_SET __MSABI_LONG(642)
#define ERROR_DS_VERSION_CHECK_FAILURE __MSABI_LONG(643)
#define ERROR_RANGE_NOT_FOUND __MSABI_LONG(644)
#define ERROR_NOT_SAFE_MODE_DRIVER __MSABI_LONG(646)
#define ERROR_FAILED_DRIVER_ENTRY __MSABI_LONG(647)
#define ERROR_DEVICE_ENUMERATION_ERROR __MSABI_LONG(648)
#define ERROR_MOUNT_POINT_NOT_RESOLVED __MSABI_LONG(649)
#define ERROR_INVALID_DEVICE_OBJECT_PARAMETER __MSABI_LONG(650)
#define ERROR_MCA_OCCURED __MSABI_LONG(651)
#define ERROR_DRIVER_DATABASE_ERROR __MSABI_LONG(652)
#define ERROR_SYSTEM_HIVE_TOO_LARGE __MSABI_LONG(653)
#define ERROR_DRIVER_FAILED_PRIOR_UNLOAD __MSABI_LONG(654)
#define ERROR_VOLSNAP_PREPARE_HIBERNATE __MSABI_LONG(655)
#define ERROR_HIBERNATION_FAILURE __MSABI_LONG(656)
#define ERROR_PWD_TOO_LONG __MSABI_LONG(657)
#define ERROR_FILE_SYSTEM_LIMITATION __MSABI_LONG(665)
#define ERROR_ASSERTION_FAILURE __MSABI_LONG(668)
#define ERROR_ACPI_ERROR __MSABI_LONG(669)
#define ERROR_WOW_ASSERTION __MSABI_LONG(670)
#define ERROR_PNP_BAD_MPS_TABLE __MSABI_LONG(671)
#define ERROR_PNP_TRANSLATION_FAILED __MSABI_LONG(672)
#define ERROR_PNP_IRQ_TRANSLATION_FAILED __MSABI_LONG(673)
#define ERROR_PNP_INVALID_ID __MSABI_LONG(674)
#define ERROR_WAKE_SYSTEM_DEBUGGER __MSABI_LONG(675)
#define ERROR_HANDLES_CLOSED __MSABI_LONG(676)
#define ERROR_EXTRANEOUS_INFORMATION __MSABI_LONG(677)
#define ERROR_RXACT_COMMIT_NECESSARY __MSABI_LONG(678)
#define ERROR_MEDIA_CHECK __MSABI_LONG(679)
#define ERROR_GUID_SUBSTITUTION_MADE __MSABI_LONG(680)
#define ERROR_STOPPED_ON_SYMLINK __MSABI_LONG(681)
#define ERROR_LONGJUMP __MSABI_LONG(682)
#define ERROR_PLUGPLAY_QUERY_VETOED __MSABI_LONG(683)
#define ERROR_UNWIND_CONSOLIDATE __MSABI_LONG(684)
#define ERROR_REGISTRY_HIVE_RECOVERED __MSABI_LONG(685)
#define ERROR_DLL_MIGHT_BE_INSECURE __MSABI_LONG(686)
#define ERROR_DLL_MIGHT_BE_INCOMPATIBLE __MSABI_LONG(687)
#define ERROR_DBG_EXCEPTION_NOT_HANDLED __MSABI_LONG(688)
#define ERROR_DBG_REPLY_LATER __MSABI_LONG(689)
#define ERROR_DBG_UNABLE_TO_PROVIDE_HANDLE __MSABI_LONG(690)
#define ERROR_DBG_TERMINATE_THREAD __MSABI_LONG(691)
#define ERROR_DBG_TERMINATE_PROCESS __MSABI_LONG(692)
#define ERROR_DBG_CONTROL_C __MSABI_LONG(693)
#define ERROR_DBG_PRINTEXCEPTION_C __MSABI_LONG(694)
#define ERROR_DBG_RIPEXCEPTION __MSABI_LONG(695)
#define ERROR_DBG_CONTROL_BREAK __MSABI_LONG(696)
#define ERROR_DBG_COMMAND_EXCEPTION __MSABI_LONG(697)
#define ERROR_OBJECT_NAME_EXISTS __MSABI_LONG(698)
#define ERROR_THREAD_WAS_SUSPENDED __MSABI_LONG(699)
#define ERROR_IMAGE_NOT_AT_BASE __MSABI_LONG(700)
#define ERROR_RXACT_STATE_CREATED __MSABI_LONG(701)
#define ERROR_SEGMENT_NOTIFICATION __MSABI_LONG(702)
#define ERROR_BAD_CURRENT_DIRECTORY __MSABI_LONG(703)
#define ERROR_FT_READ_RECOVERY_FROM_BACKUP __MSABI_LONG(704)
#define ERROR_FT_WRITE_RECOVERY __MSABI_LONG(705)
#define ERROR_IMAGE_MACHINE_TYPE_MISMATCH __MSABI_LONG(706)
#define ERROR_RECEIVE_PARTIAL __MSABI_LONG(707)
#define ERROR_RECEIVE_EXPEDITED __MSABI_LONG(708)
#define ERROR_RECEIVE_PARTIAL_EXPEDITED __MSABI_LONG(709)
#define ERROR_EVENT_DONE __MSABI_LONG(710)
#define ERROR_EVENT_PENDING __MSABI_LONG(711)
#define ERROR_CHECKING_FILE_SYSTEM __MSABI_LONG(712)
#define ERROR_FATAL_APP_EXIT __MSABI_LONG(713)
#define ERROR_PREDEFINED_HANDLE __MSABI_LONG(714)
#define ERROR_WAS_UNLOCKED __MSABI_LONG(715)
#define ERROR_SERVICE_NOTIFICATION __MSABI_LONG(716)
#define ERROR_WAS_LOCKED __MSABI_LONG(717)
#define ERROR_LOG_HARD_ERROR __MSABI_LONG(718)
#define ERROR_ALREADY_WIN32 __MSABI_LONG(719)
#define ERROR_IMAGE_MACHINE_TYPE_MISMATCH_EXE __MSABI_LONG(720)
#define ERROR_NO_YIELD_PERFORMED __MSABI_LONG(721)
#define ERROR_TIMER_RESUME_IGNORED __MSABI_LONG(722)
#define ERROR_ARBITRATION_UNHANDLED __MSABI_LONG(723)
#define ERROR_CARDBUS_NOT_SUPPORTED __MSABI_LONG(724)
#define ERROR_MP_PROCESSOR_MISMATCH __MSABI_LONG(725)
#define ERROR_HIBERNATED __MSABI_LONG(726)
#define ERROR_RESUME_HIBERNATION __MSABI_LONG(727)
#define ERROR_FIRMWARE_UPDATED __MSABI_LONG(728)
#define ERROR_DRIVERS_LEAKING_LOCKED_PAGES __MSABI_LONG(729)
#define ERROR_WAKE_SYSTEM __MSABI_LONG(730)
#define ERROR_WAIT_1 __MSABI_LONG(731)
#define ERROR_WAIT_2 __MSABI_LONG(732)
#define ERROR_WAIT_3 __MSABI_LONG(733)
#define ERROR_WAIT_63 __MSABI_LONG(734)
#define ERROR_ABANDONED_WAIT_0 __MSABI_LONG(735)
#define ERROR_ABANDONED_WAIT_63 __MSABI_LONG(736)
#define ERROR_USER_APC __MSABI_LONG(737)
#define ERROR_KERNEL_APC __MSABI_LONG(738)
#define ERROR_ALERTED __MSABI_LONG(739)
#define ERROR_ELEVATION_REQUIRED __MSABI_LONG(740)
#define ERROR_REPARSE __MSABI_LONG(741)
#define ERROR_OPLOCK_BREAK_IN_PROGRESS __MSABI_LONG(742)
#define ERROR_VOLUME_MOUNTED __MSABI_LONG(743)
#define ERROR_RXACT_COMMITTED __MSABI_LONG(744)
#define ERROR_NOTIFY_CLEANUP __MSABI_LONG(745)
#define ERROR_PRIMARY_TRANSPORT_CONNECT_FAILED __MSABI_LONG(746)
#define ERROR_PAGE_FAULT_TRANSITION __MSABI_LONG(747)
#define ERROR_PAGE_FAULT_DEMAND_ZERO __MSABI_LONG(748)
#define ERROR_PAGE_FAULT_COPY_ON_WRITE __MSABI_LONG(749)
#define ERROR_PAGE_FAULT_GUARD_PAGE __MSABI_LONG(750)
#define ERROR_PAGE_FAULT_PAGING_FILE __MSABI_LONG(751)
#define ERROR_CACHE_PAGE_LOCKED __MSABI_LONG(752)
#define ERROR_CRASH_DUMP __MSABI_LONG(753)
#define ERROR_BUFFER_ALL_ZEROS __MSABI_LONG(754)
#define ERROR_REPARSE_OBJECT __MSABI_LONG(755)
#define ERROR_RESOURCE_REQUIREMENTS_CHANGED __MSABI_LONG(756)
#define ERROR_TRANSLATION_COMPLETE __MSABI_LONG(757)
#define ERROR_NOTHING_TO_TERMINATE __MSABI_LONG(758)
#define ERROR_PROCESS_NOT_IN_JOB __MSABI_LONG(759)
#define ERROR_PROCESS_IN_JOB __MSABI_LONG(760)
#define ERROR_VOLSNAP_HIBERNATE_READY __MSABI_LONG(761)
#define ERROR_FSFILTER_OP_COMPLETED_SUCCESSFULLY __MSABI_LONG(762)
#define ERROR_INTERRUPT_VECTOR_ALREADY_CONNECTED __MSABI_LONG(763)
#define ERROR_INTERRUPT_STILL_CONNECTED __MSABI_LONG(764)
#define ERROR_WAIT_FOR_OPLOCK __MSABI_LONG(765)
#define ERROR_DBG_EXCEPTION_HANDLED __MSABI_LONG(766)
#define ERROR_DBG_CONTINUE __MSABI_LONG(767)
#define ERROR_CALLBACK_POP_STACK __MSABI_LONG(768)
#define ERROR_COMPRESSION_DISABLED __MSABI_LONG(769)
#define ERROR_CANTFETCHBACKWARDS __MSABI_LONG(770)
#define ERROR_CANTSCROLLBACKWARDS __MSABI_LONG(771)
#define ERROR_ROWSNOTRELEASED __MSABI_LONG(772)
#define ERROR_BAD_ACCESSOR_FLAGS __MSABI_LONG(773)
#define ERROR_ERRORS_ENCOUNTERED __MSABI_LONG(774)
#define ERROR_NOT_CAPABLE __MSABI_LONG(775)
#define ERROR_REQUEST_OUT_OF_SEQUENCE __MSABI_LONG(776)
#define ERROR_VERSION_PARSE_ERROR __MSABI_LONG(777)
#define ERROR_BADSTARTPOSITION __MSABI_LONG(778)
#define ERROR_MEMORY_HARDWARE __MSABI_LONG(779)
#define ERROR_DISK_REPAIR_DISABLED __MSABI_LONG(780)
#define ERROR_INSUFFICIENT_RESOURCE_FOR_SPECIFIED_SHARED_SECTION_SIZE __MSABI_LONG(781)
#define ERROR_SYSTEM_POWERSTATE_TRANSITION __MSABI_LONG(782)
#define ERROR_SYSTEM_POWERSTATE_COMPLEX_TRANSITION __MSABI_LONG(783)
#define ERROR_MCA_EXCEPTION __MSABI_LONG(784)
#define ERROR_ACCESS_AUDIT_BY_POLICY __MSABI_LONG(785)
#define ERROR_ACCESS_DISABLED_NO_SAFER_UI_BY_POLICY __MSABI_LONG(786)
#define ERROR_ABANDON_HIBERFILE __MSABI_LONG(787)
#define ERROR_LOST_WRITEBEHIND_DATA_NETWORK_DISCONNECTED __MSABI_LONG(788)
#define ERROR_LOST_WRITEBEHIND_DATA_NETWORK_SERVER_ERROR __MSABI_LONG(789)
#define ERROR_LOST_WRITEBEHIND_DATA_LOCAL_DISK_ERROR __MSABI_LONG(790)
#define ERROR_BAD_MCFG_TABLE __MSABI_LONG(791)
#define ERROR_DISK_REPAIR_REDIRECTED __MSABI_LONG(792)
#define ERROR_DISK_REPAIR_UNSUCCESSFUL __MSABI_LONG(793)
#define ERROR_CORRUPT_LOG_OVERFULL __MSABI_LONG(794)
#define ERROR_CORRUPT_LOG_CORRUPTED __MSABI_LONG(795)
#define ERROR_CORRUPT_LOG_UNAVAILABLE __MSABI_LONG(796)
#define ERROR_CORRUPT_LOG_DELETED_FULL __MSABI_LONG(797)
#define ERROR_CORRUPT_LOG_CLEARED __MSABI_LONG(798)
#define ERROR_ORPHAN_NAME_EXHAUSTED __MSABI_LONG(799)
#define ERROR_OPLOCK_SWITCHED_TO_NEW_HANDLE __MSABI_LONG(800)
#define ERROR_CANNOT_GRANT_REQUESTED_OPLOCK __MSABI_LONG(801)
#define ERROR_CANNOT_BREAK_OPLOCK __MSABI_LONG(802)
#define ERROR_OPLOCK_HANDLE_CLOSED __MSABI_LONG(803)
#define ERROR_NO_ACE_CONDITION __MSABI_LONG(804)
#define ERROR_INVALID_ACE_CONDITION __MSABI_LONG(805)
#define ERROR_FILE_HANDLE_REVOKED __MSABI_LONG(806)
#define ERROR_IMAGE_AT_DIFFERENT_BASE __MSABI_LONG(807)
#define ERROR_ENCRYPTED_IO_NOT_POSSIBLE __MSABI_LONG(808)
#define ERROR_FILE_METADATA_OPTIMIZATION_IN_PROGRESS __MSABI_LONG(809)
#define ERROR_QUOTA_ACTIVITY __MSABI_LONG(810)
#define ERROR_HANDLE_REVOKED __MSABI_LONG(811)
#define ERROR_CALLBACK_INVOKE_INLINE __MSABI_LONG(812)
#define ERROR_CPU_SET_INVALID __MSABI_LONG(813)
#define ERROR_ENCLAVE_NOT_TERMINATED __MSABI_LONG(814)
#define ERROR_ENCLAVE_VIOLATION __MSABI_LONG(815)
#define ERROR_SERVER_TRANSPORT_CONFLICT __MSABI_LONG(816)
#define ERROR_CERTIFICATE_VALIDATION_PREFERENCE_CONFLICT __MSABI_LONG(817)
#define ERROR_FT_READ_FROM_COPY_FAILURE __MSABI_LONG(818)
#define ERROR_SECTION_DIRECT_MAP_ONLY __MSABI_LONG(819)
#define ERROR_EA_ACCESS_DENIED __MSABI_LONG(994)
#define ERROR_OPERATION_ABORTED __MSABI_LONG(995)
#define ERROR_IO_INCOMPLETE __MSABI_LONG(996)
#define ERROR_IO_PENDING __MSABI_LONG(997)
#define ERROR_NOACCESS __MSABI_LONG(998)
#define ERROR_SWAPERROR __MSABI_LONG(999)
#define ERROR_STACK_OVERFLOW __MSABI_LONG(1001)
#define ERROR_INVALID_MESSAGE __MSABI_LONG(1002)
#define ERROR_CAN_NOT_COMPLETE __MSABI_LONG(1003)
#define ERROR_INVALID_FLAGS __MSABI_LONG(1004)
#define ERROR_UNRECOGNIZED_VOLUME __MSABI_LONG(1005)
#define ERROR_FILE_INVALID __MSABI_LONG(1006)
#define ERROR_FULLSCREEN_MODE __MSABI_LONG(1007)
#define ERROR_NO_TOKEN __MSABI_LONG(1008)
#define ERROR_BADDB __MSABI_LONG(1009)
#define ERROR_BADKEY __MSABI_LONG(1010)
#define ERROR_CANTOPEN __MSABI_LONG(1011)
#define ERROR_CANTREAD __MSABI_LONG(1012)
#define ERROR_CANTWRITE __MSABI_LONG(1013)
#define ERROR_REGISTRY_RECOVERED __MSABI_LONG(1014)
#define ERROR_REGISTRY_CORRUPT __MSABI_LONG(1015)
#define ERROR_REGISTRY_IO_FAILED __MSABI_LONG(1016)
#define ERROR_NOT_REGISTRY_FILE __MSABI_LONG(1017)
#define ERROR_KEY_DELETED __MSABI_LONG(1018)
#define ERROR_NO_LOG_SPACE __MSABI_LONG(1019)
#define ERROR_KEY_HAS_CHILDREN __MSABI_LONG(1020)
#define ERROR_CHILD_MUST_BE_VOLATILE __MSABI_LONG(1021)
#define ERROR_NOTIFY_ENUM_DIR __MSABI_LONG(1022)
#define ERROR_DEPENDENT_SERVICES_RUNNING __MSABI_LONG(1051)
#define ERROR_INVALID_SERVICE_CONTROL __MSABI_LONG(1052)
#define ERROR_SERVICE_REQUEST_TIMEOUT __MSABI_LONG(1053)
#define ERROR_SERVICE_NO_THREAD __MSABI_LONG(1054)
#define ERROR_SERVICE_DATABASE_LOCKED __MSABI_LONG(1055)
#define ERROR_SERVICE_ALREADY_RUNNING __MSABI_LONG(1056)
#define ERROR_INVALID_SERVICE_ACCOUNT __MSABI_LONG(1057)
#define ERROR_SERVICE_DISABLED __MSABI_LONG(1058)
#define ERROR_CIRCULAR_DEPENDENCY __MSABI_LONG(1059)
#define ERROR_SERVICE_DOES_NOT_EXIST __MSABI_LONG(1060)
#define ERROR_SERVICE_CANNOT_ACCEPT_CTRL __MSABI_LONG(1061)
#define ERROR_SERVICE_NOT_ACTIVE __MSABI_LONG(1062)
#define ERROR_FAILED_SERVICE_CONTROLLER_CONNECT __MSABI_LONG(1063)
#define ERROR_EXCEPTION_IN_SERVICE __MSABI_LONG(1064)
#define ERROR_DATABASE_DOES_NOT_EXIST __MSABI_LONG(1065)
#define ERROR_SERVICE_SPECIFIC_ERROR __MSABI_LONG(1066)
#define ERROR_PROCESS_ABORTED __MSABI_LONG(1067)
#define ERROR_SERVICE_DEPENDENCY_FAIL __MSABI_LONG(1068)
#define ERROR_SERVICE_LOGON_FAILED __MSABI_LONG(1069)
#define ERROR_SERVICE_START_HANG __MSABI_LONG(1070)
#define ERROR_INVALID_SERVICE_LOCK __MSABI_LONG(1071)
#define ERROR_SERVICE_MARKED_FOR_DELETE __MSABI_LONG(1072)
#define ERROR_SERVICE_EXISTS __MSABI_LONG(1073)
#define ERROR_ALREADY_RUNNING_LKG __MSABI_LONG(1074)
#define ERROR_SERVICE_DEPENDENCY_DELETED __MSABI_LONG(1075)
#define ERROR_BOOT_ALREADY_ACCEPTED __MSABI_LONG(1076)
#define ERROR_SERVICE_NEVER_STARTED __MSABI_LONG(1077)
#define ERROR_DUPLICATE_SERVICE_NAME __MSABI_LONG(1078)
#define ERROR_DIFFERENT_SERVICE_ACCOUNT __MSABI_LONG(1079)
#define ERROR_CANNOT_DETECT_DRIVER_FAILURE __MSABI_LONG(1080)
#define ERROR_CANNOT_DETECT_PROCESS_ABORT __MSABI_LONG(1081)
#define ERROR_NO_RECOVERY_PROGRAM __MSABI_LONG(1082)
#define ERROR_SERVICE_NOT_IN_EXE __MSABI_LONG(1083)
#define ERROR_NOT_SAFEBOOT_SERVICE __MSABI_LONG(1084)
#define ERROR_END_OF_MEDIA __MSABI_LONG(1100)
#define ERROR_FILEMARK_DETECTED __MSABI_LONG(1101)
#define ERROR_BEGINNING_OF_MEDIA __MSABI_LONG(1102)
#define ERROR_SETMARK_DETECTED __MSABI_LONG(1103)
#define ERROR_NO_DATA_DETECTED __MSABI_LONG(1104)
#define ERROR_PARTITION_FAILURE __MSABI_LONG(1105)
#define ERROR_INVALID_BLOCK_LENGTH __MSABI_LONG(1106)
#define ERROR_DEVICE_NOT_PARTITIONED __MSABI_LONG(1107)
#define ERROR_UNABLE_TO_LOCK_MEDIA __MSABI_LONG(1108)
#define ERROR_UNABLE_TO_UNLOAD_MEDIA __MSABI_LONG(1109)
#define ERROR_MEDIA_CHANGED __MSABI_LONG(1110)
#define ERROR_BUS_RESET __MSABI_LONG(1111)
#define ERROR_NO_MEDIA_IN_DRIVE __MSABI_LONG(1112)
#define ERROR_NO_UNICODE_TRANSLATION __MSABI_LONG(1113)
#define ERROR_DLL_INIT_FAILED __MSABI_LONG(1114)
#define ERROR_SHUTDOWN_IN_PROGRESS __MSABI_LONG(1115)
#define ERROR_NO_SHUTDOWN_IN_PROGRESS __MSABI_LONG(1116)
#define ERROR_IO_DEVICE __MSABI_LONG(1117)
#define ERROR_SERIAL_NO_DEVICE __MSABI_LONG(1118)
#define ERROR_IRQ_BUSY __MSABI_LONG(1119)
#define ERROR_MORE_WRITES __MSABI_LONG(1120)
#define ERROR_COUNTER_TIMEOUT __MSABI_LONG(1121)
#define ERROR_FLOPPY_ID_MARK_NOT_FOUND __MSABI_LONG(1122)
#define ERROR_FLOPPY_WRONG_CYLINDER __MSABI_LONG(1123)
#define ERROR_FLOPPY_UNKNOWN_ERROR __MSABI_LONG(1124)
#define ERROR_FLOPPY_BAD_REGISTERS __MSABI_LONG(1125)
#define ERROR_DISK_RECALIBRATE_FAILED __MSABI_LONG(1126)
#define ERROR_DISK_OPERATION_FAILED __MSABI_LONG(1127)
#define ERROR_DISK_RESET_FAILED __MSABI_LONG(1128)
#define ERROR_EOM_OVERFLOW __MSABI_LONG(1129)
#define ERROR_NOT_ENOUGH_SERVER_MEMORY __MSABI_LONG(1130)
#define ERROR_POSSIBLE_DEADLOCK __MSABI_LONG(1131)
#define ERROR_MAPPED_ALIGNMENT __MSABI_LONG(1132)
#define ERROR_SET_POWER_STATE_VETOED __MSABI_LONG(1140)
#define ERROR_SET_POWER_STATE_FAILED __MSABI_LONG(1141)
#define ERROR_TOO_MANY_LINKS __MSABI_LONG(1142)
#define ERROR_OLD_WIN_VERSION __MSABI_LONG(1150)
#define ERROR_APP_WRONG_OS __MSABI_LONG(1151)
#define ERROR_SINGLE_INSTANCE_APP __MSABI_LONG(1152)
#define ERROR_RMODE_APP __MSABI_LONG(1153)
#define ERROR_INVALID_DLL __MSABI_LONG(1154)
#define ERROR_NO_ASSOCIATION __MSABI_LONG(1155)
#define ERROR_DDE_FAIL __MSABI_LONG(1156)
#define ERROR_DLL_NOT_FOUND __MSABI_LONG(1157)
#define ERROR_NO_MORE_USER_HANDLES __MSABI_LONG(1158)
#define ERROR_MESSAGE_SYNC_ONLY __MSABI_LONG(1159)
#define ERROR_SOURCE_ELEMENT_EMPTY __MSABI_LONG(1160)
#define ERROR_DESTINATION_ELEMENT_FULL __MSABI_LONG(1161)
#define ERROR_ILLEGAL_ELEMENT_ADDRESS __MSABI_LONG(1162)
#define ERROR_MAGAZINE_NOT_PRESENT __MSABI_LONG(1163)
#define ERROR_DEVICE_REINITIALIZATION_NEEDED __MSABI_LONG(1164)
#define ERROR_DEVICE_REQUIRES_CLEANING __MSABI_LONG(1165)
#define ERROR_DEVICE_DOOR_OPEN __MSABI_LONG(1166)
#define ERROR_DEVICE_NOT_CONNECTED __MSABI_LONG(1167)
#define ERROR_NOT_FOUND __MSABI_LONG(1168)
#define ERROR_NO_MATCH __MSABI_LONG(1169)
#define ERROR_SET_NOT_FOUND __MSABI_LONG(1170)
#define ERROR_POINT_NOT_FOUND __MSABI_LONG(1171)
#define ERROR_NO_TRACKING_SERVICE __MSABI_LONG(1172)
#define ERROR_NO_VOLUME_ID __MSABI_LONG(1173)
#define ERROR_UNABLE_TO_REMOVE_REPLACED __MSABI_LONG(1175)
#define ERROR_UNABLE_TO_MOVE_REPLACEMENT __MSABI_LONG(1176)
#define ERROR_UNABLE_TO_MOVE_REPLACEMENT_2 __MSABI_LONG(1177)
#define ERROR_JOURNAL_DELETE_IN_PROGRESS __MSABI_LONG(1178)
#define ERROR_JOURNAL_NOT_ACTIVE __MSABI_LONG(1179)
#define ERROR_POTENTIAL_FILE_FOUND __MSABI_LONG(1180)
#define ERROR_JOURNAL_ENTRY_DELETED __MSABI_LONG(1181)
#define ERROR_PARTITION_TERMINATING __MSABI_LONG(1184)
#define ERROR_SHUTDOWN_IS_SCHEDULED __MSABI_LONG(1190)
#define ERROR_SHUTDOWN_USERS_LOGGED_ON __MSABI_LONG(1191)
#define ERROR_SHUTDOWN_DISKS_NOT_IN_MAINTENANCE_MODE __MSABI_LONG(1192)
#define ERROR_BAD_DEVICE __MSABI_LONG(1200)
#define ERROR_CONNECTION_UNAVAIL __MSABI_LONG(1201)
#define ERROR_DEVICE_ALREADY_REMEMBERED __MSABI_LONG(1202)
#define ERROR_NO_NET_OR_BAD_PATH __MSABI_LONG(1203)
#define ERROR_BAD_PROVIDER __MSABI_LONG(1204)
#define ERROR_CANNOT_OPEN_PROFILE __MSABI_LONG(1205)
#define ERROR_BAD_PROFILE __MSABI_LONG(1206)
#define ERROR_NOT_CONTAINER __MSABI_LONG(1207)
#define ERROR_EXTENDED_ERROR __MSABI_LONG(1208)
#define ERROR_INVALID_GROUPNAME __MSABI_LONG(1209)
#define ERROR_INVALID_COMPUTERNAME __MSABI_LONG(1210)
#define ERROR_INVALID_EVENTNAME __MSABI_LONG(1211)
#define ERROR_INVALID_DOMAINNAME __MSABI_LONG(1212)
#define ERROR_INVALID_SERVICENAME __MSABI_LONG(1213)
#define ERROR_INVALID_NETNAME __MSABI_LONG(1214)
#define ERROR_INVALID_SHARENAME __MSABI_LONG(1215)
#define ERROR_INVALID_PASSWORDNAME __MSABI_LONG(1216)
#define ERROR_INVALID_MESSAGENAME __MSABI_LONG(1217)
#define ERROR_INVALID_MESSAGEDEST __MSABI_LONG(1218)
#define ERROR_SESSION_CREDENTIAL_CONFLICT __MSABI_LONG(1219)
#define ERROR_REMOTE_SESSION_LIMIT_EXCEEDED __MSABI_LONG(1220)
#define ERROR_DUP_DOMAINNAME __MSABI_LONG(1221)
#define ERROR_NO_NETWORK __MSABI_LONG(1222)
#define ERROR_CANCELLED __MSABI_LONG(1223)
#define ERROR_USER_MAPPED_FILE __MSABI_LONG(1224)
#define ERROR_CONNECTION_REFUSED __MSABI_LONG(1225)
#define ERROR_GRACEFUL_DISCONNECT __MSABI_LONG(1226)
#define ERROR_ADDRESS_ALREADY_ASSOCIATED __MSABI_LONG(1227)
#define ERROR_ADDRESS_NOT_ASSOCIATED __MSABI_LONG(1228)
#define ERROR_CONNECTION_INVALID __MSABI_LONG(1229)
#define ERROR_CONNECTION_ACTIVE __MSABI_LONG(1230)
#define ERROR_NETWORK_UNREACHABLE __MSABI_LONG(1231)
#define ERROR_HOST_UNREACHABLE __MSABI_LONG(1232)
#define ERROR_PROTOCOL_UNREACHABLE __MSABI_LONG(1233)
#define ERROR_PORT_UNREACHABLE __MSABI_LONG(1234)
#define ERROR_REQUEST_ABORTED __MSABI_LONG(1235)
#define ERROR_CONNECTION_ABORTED __MSABI_LONG(1236)
#define ERROR_RETRY __MSABI_LONG(1237)
#define ERROR_CONNECTION_COUNT_LIMIT __MSABI_LONG(1238)
#define ERROR_LOGIN_TIME_RESTRICTION __MSABI_LONG(1239)
#define ERROR_LOGIN_WKSTA_RESTRICTION __MSABI_LONG(1240)
#define ERROR_INCORRECT_ADDRESS __MSABI_LONG(1241)
#define ERROR_ALREADY_REGISTERED __MSABI_LONG(1242)
#define ERROR_SERVICE_NOT_FOUND __MSABI_LONG(1243)
#define ERROR_NOT_AUTHENTICATED __MSABI_LONG(1244)
#define ERROR_NOT_LOGGED_ON __MSABI_LONG(1245)
#define ERROR_CONTINUE __MSABI_LONG(1246)
#define ERROR_ALREADY_INITIALIZED __MSABI_LONG(1247)
#define ERROR_NO_MORE_DEVICES __MSABI_LONG(1248)
#define ERROR_NO_SUCH_SITE __MSABI_LONG(1249)
#define ERROR_DOMAIN_CONTROLLER_EXISTS __MSABI_LONG(1250)
#define ERROR_ONLY_IF_CONNECTED __MSABI_LONG(1251)
#define ERROR_OVERRIDE_NOCHANGES __MSABI_LONG(1252)
#define ERROR_BAD_USER_PROFILE __MSABI_LONG(1253)
#define ERROR_NOT_SUPPORTED_ON_SBS __MSABI_LONG(1254)
#define ERROR_SERVER_SHUTDOWN_IN_PROGRESS __MSABI_LONG(1255)
#define ERROR_HOST_DOWN __MSABI_LONG(1256)
#define ERROR_NON_ACCOUNT_SID __MSABI_LONG(1257)
#define ERROR_NON_DOMAIN_SID __MSABI_LONG(1258)
#define ERROR_APPHELP_BLOCK __MSABI_LONG(1259)
#define ERROR_ACCESS_DISABLED_BY_POLICY __MSABI_LONG(1260)
#define ERROR_REG_NAT_CONSUMPTION __MSABI_LONG(1261)
#define ERROR_CSCSHARE_OFFLINE __MSABI_LONG(1262)
#define ERROR_PKINIT_FAILURE __MSABI_LONG(1263)
#define ERROR_SMARTCARD_SUBSYSTEM_FAILURE __MSABI_LONG(1264)
#define ERROR_DOWNGRADE_DETECTED __MSABI_LONG(1265)
#define ERROR_MACHINE_LOCKED __MSABI_LONG(1271)
#define ERROR_SMB_GUEST_LOGON_BLOCKED __MSABI_LONG(1272)
#define ERROR_CALLBACK_SUPPLIED_INVALID_DATA __MSABI_LONG(1273)
#define ERROR_SYNC_FOREGROUND_REFRESH_REQUIRED __MSABI_LONG(1274)
#define ERROR_DRIVER_BLOCKED __MSABI_LONG(1275)
#define ERROR_INVALID_IMPORT_OF_NON_DLL __MSABI_LONG(1276)
#define ERROR_ACCESS_DISABLED_WEBBLADE __MSABI_LONG(1277)
#define ERROR_ACCESS_DISABLED_WEBBLADE_TAMPER __MSABI_LONG(1278)
#define ERROR_RECOVERY_FAILURE __MSABI_LONG(1279)
#define ERROR_ALREADY_FIBER __MSABI_LONG(1280)
#define ERROR_ALREADY_THREAD __MSABI_LONG(1281)
#define ERROR_STACK_BUFFER_OVERRUN __MSABI_LONG(1282)
#define ERROR_PARAMETER_QUOTA_EXCEEDED __MSABI_LONG(1283)
#define ERROR_DEBUGGER_INACTIVE __MSABI_LONG(1284)
#define ERROR_DELAY_LOAD_FAILED __MSABI_LONG(1285)
#define ERROR_VDM_DISALLOWED __MSABI_LONG(1286)
#define ERROR_UNIDENTIFIED_ERROR __MSABI_LONG(1287)
#define ERROR_INVALID_CRUNTIME_PARAMETER __MSABI_LONG(1288)
#define ERROR_BEYOND_VDL __MSABI_LONG(1289)
#define ERROR_INCOMPATIBLE_SERVICE_SID_TYPE __MSABI_LONG(1290)
#define ERROR_DRIVER_PROCESS_TERMINATED __MSABI_LONG(1291)
#define ERROR_IMPLEMENTATION_LIMIT __MSABI_LONG(1292)
#define ERROR_PROCESS_IS_PROTECTED __MSABI_LONG(1293)
#define ERROR_SERVICE_NOTIFY_CLIENT_LAGGING __MSABI_LONG(1294)
#define ERROR_DISK_QUOTA_EXCEEDED __MSABI_LONG(1295)
#define ERROR_CONTENT_BLOCKED __MSABI_LONG(1296)
#define ERROR_INCOMPATIBLE_SERVICE_PRIVILEGE __MSABI_LONG(1297)
#define ERROR_APP_HANG __MSABI_LONG(1298)
#define ERROR_INVALID_LABEL __MSABI_LONG(1299)
#define ERROR_NOT_ALL_ASSIGNED __MSABI_LONG(1300)
#define ERROR_SOME_NOT_MAPPED __MSABI_LONG(1301)
#define ERROR_NO_QUOTAS_FOR_ACCOUNT __MSABI_LONG(1302)
#define ERROR_LOCAL_USER_SESSION_KEY __MSABI_LONG(1303)
#define ERROR_NULL_LM_PASSWORD __MSABI_LONG(1304)
#define ERROR_UNKNOWN_REVISION __MSABI_LONG(1305)
#define ERROR_REVISION_MISMATCH __MSABI_LONG(1306)
#define ERROR_INVALID_OWNER __MSABI_LONG(1307)
#define ERROR_INVALID_PRIMARY_GROUP __MSABI_LONG(1308)
#define ERROR_NO_IMPERSONATION_TOKEN __MSABI_LONG(1309)
#define ERROR_CANT_DISABLE_MANDATORY __MSABI_LONG(1310)
#define ERROR_NO_LOGON_SERVERS __MSABI_LONG(1311)
#define ERROR_NO_SUCH_LOGON_SESSION __MSABI_LONG(1312)
#define ERROR_NO_SUCH_PRIVILEGE __MSABI_LONG(1313)
#define ERROR_PRIVILEGE_NOT_HELD __MSABI_LONG(1314)
#define ERROR_INVALID_ACCOUNT_NAME __MSABI_LONG(1315)
#define ERROR_USER_EXISTS __MSABI_LONG(1316)
#define ERROR_NO_SUCH_USER __MSABI_LONG(1317)
#define ERROR_GROUP_EXISTS __MSABI_LONG(1318)
#define ERROR_NO_SUCH_GROUP __MSABI_LONG(1319)
#define ERROR_MEMBER_IN_GROUP __MSABI_LONG(1320)
#define ERROR_MEMBER_NOT_IN_GROUP __MSABI_LONG(1321)
#define ERROR_LAST_ADMIN __MSABI_LONG(1322)
#define ERROR_WRONG_PASSWORD __MSABI_LONG(1323)
#define ERROR_ILL_FORMED_PASSWORD __MSABI_LONG(1324)
#define ERROR_PASSWORD_RESTRICTION __MSABI_LONG(1325)
#define ERROR_LOGON_FAILURE __MSABI_LONG(1326)
#define ERROR_ACCOUNT_RESTRICTION __MSABI_LONG(1327)
#define ERROR_INVALID_LOGON_HOURS __MSABI_LONG(1328)
#define ERROR_INVALID_WORKSTATION __MSABI_LONG(1329)
#define ERROR_PASSWORD_EXPIRED __MSABI_LONG(1330)
#define ERROR_ACCOUNT_DISABLED __MSABI_LONG(1331)
#define ERROR_NONE_MAPPED __MSABI_LONG(1332)
#define ERROR_TOO_MANY_LUIDS_REQUESTED __MSABI_LONG(1333)
#define ERROR_LUIDS_EXHAUSTED __MSABI_LONG(1334)
#define ERROR_INVALID_SUB_AUTHORITY __MSABI_LONG(1335)
#define ERROR_INVALID_ACL __MSABI_LONG(1336)
#define ERROR_INVALID_SID __MSABI_LONG(1337)
#define ERROR_INVALID_SECURITY_DESCR __MSABI_LONG(1338)
#define ERROR_BAD_INHERITANCE_ACL __MSABI_LONG(1340)
#define ERROR_SERVER_DISABLED __MSABI_LONG(1341)
#define ERROR_SERVER_NOT_DISABLED __MSABI_LONG(1342)
#define ERROR_INVALID_ID_AUTHORITY __MSABI_LONG(1343)
#define ERROR_ALLOTTED_SPACE_EXCEEDED __MSABI_LONG(1344)
#define ERROR_INVALID_GROUP_ATTRIBUTES __MSABI_LONG(1345)
#define ERROR_BAD_IMPERSONATION_LEVEL __MSABI_LONG(1346)
#define ERROR_CANT_OPEN_ANONYMOUS __MSABI_LONG(1347)
#define ERROR_BAD_VALIDATION_CLASS __MSABI_LONG(1348)
#define ERROR_BAD_TOKEN_TYPE __MSABI_LONG(1349)
#define ERROR_NO_SECURITY_ON_OBJECT __MSABI_LONG(1350)
#define ERROR_CANT_ACCESS_DOMAIN_INFO __MSABI_LONG(1351)
#define ERROR_INVALID_SERVER_STATE __MSABI_LONG(1352)
#define ERROR_INVALID_DOMAIN_STATE __MSABI_LONG(1353)
#define ERROR_INVALID_DOMAIN_ROLE __MSABI_LONG(1354)
#define ERROR_NO_SUCH_DOMAIN __MSABI_LONG(1355)
#define ERROR_DOMAIN_EXISTS __MSABI_LONG(1356)
#define ERROR_DOMAIN_LIMIT_EXCEEDED __MSABI_LONG(1357)
#define ERROR_INTERNAL_DB_CORRUPTION __MSABI_LONG(1358)
#define ERROR_INTERNAL_ERROR __MSABI_LONG(1359)
#define ERROR_GENERIC_NOT_MAPPED __MSABI_LONG(1360)
#define ERROR_BAD_DESCRIPTOR_FORMAT __MSABI_LONG(1361)
#define ERROR_NOT_LOGON_PROCESS __MSABI_LONG(1362)
#define ERROR_LOGON_SESSION_EXISTS __MSABI_LONG(1363)
#define ERROR_NO_SUCH_PACKAGE __MSABI_LONG(1364)
#define ERROR_BAD_LOGON_SESSION_STATE __MSABI_LONG(1365)
#define ERROR_LOGON_SESSION_COLLISION __MSABI_LONG(1366)
#define ERROR_INVALID_LOGON_TYPE __MSABI_LONG(1367)
#define ERROR_CANNOT_IMPERSONATE __MSABI_LONG(1368)
#define ERROR_RXACT_INVALID_STATE __MSABI_LONG(1369)
#define ERROR_RXACT_COMMIT_FAILURE __MSABI_LONG(1370)
#define ERROR_SPECIAL_ACCOUNT __MSABI_LONG(1371)
#define ERROR_SPECIAL_GROUP __MSABI_LONG(1372)
#define ERROR_SPECIAL_USER __MSABI_LONG(1373)
#define ERROR_MEMBERS_PRIMARY_GROUP __MSABI_LONG(1374)
#define ERROR_TOKEN_ALREADY_IN_USE __MSABI_LONG(1375)
#define ERROR_NO_SUCH_ALIAS __MSABI_LONG(1376)
#define ERROR_MEMBER_NOT_IN_ALIAS __MSABI_LONG(1377)
#define ERROR_MEMBER_IN_ALIAS __MSABI_LONG(1378)
#define ERROR_ALIAS_EXISTS __MSABI_LONG(1379)
#define ERROR_LOGON_NOT_GRANTED __MSABI_LONG(1380)
#define ERROR_TOO_MANY_SECRETS __MSABI_LONG(1381)
#define ERROR_SECRET_TOO_LONG __MSABI_LONG(1382)
#define ERROR_INTERNAL_DB_ERROR __MSABI_LONG(1383)
#define ERROR_TOO_MANY_CONTEXT_IDS __MSABI_LONG(1384)
#define ERROR_LOGON_TYPE_NOT_GRANTED __MSABI_LONG(1385)
#define ERROR_NT_CROSS_ENCRYPTION_REQUIRED __MSABI_LONG(1386)
#define ERROR_NO_SUCH_MEMBER __MSABI_LONG(1387)
#define ERROR_INVALID_MEMBER __MSABI_LONG(1388)
#define ERROR_TOO_MANY_SIDS __MSABI_LONG(1389)
#define ERROR_LM_CROSS_ENCRYPTION_REQUIRED __MSABI_LONG(1390)
#define ERROR_NO_INHERITANCE __MSABI_LONG(1391)
#define ERROR_FILE_CORRUPT __MSABI_LONG(1392)
#define ERROR_DISK_CORRUPT __MSABI_LONG(1393)
#define ERROR_NO_USER_SESSION_KEY __MSABI_LONG(1394)
#define ERROR_LICENSE_QUOTA_EXCEEDED __MSABI_LONG(1395)
#define ERROR_WRONG_TARGET_NAME __MSABI_LONG(1396)
#define ERROR_MUTUAL_AUTH_FAILED __MSABI_LONG(1397)
#define ERROR_TIME_SKEW __MSABI_LONG(1398)
#define ERROR_CURRENT_DOMAIN_NOT_ALLOWED __MSABI_LONG(1399)
#define ERROR_INVALID_WINDOW_HANDLE __MSABI_LONG(1400)
#define ERROR_INVALID_MENU_HANDLE __MSABI_LONG(1401)
#define ERROR_INVALID_CURSOR_HANDLE __MSABI_LONG(1402)
#define ERROR_INVALID_ACCEL_HANDLE __MSABI_LONG(1403)
#define ERROR_INVALID_HOOK_HANDLE __MSABI_LONG(1404)
#define ERROR_INVALID_DWP_HANDLE __MSABI_LONG(1405)
#define ERROR_TLW_WITH_WSCHILD __MSABI_LONG(1406)
#define ERROR_CANNOT_FIND_WND_CLASS __MSABI_LONG(1407)
#define ERROR_WINDOW_OF_OTHER_THREAD __MSABI_LONG(1408)
#define ERROR_HOTKEY_ALREADY_REGISTERED __MSABI_LONG(1409)
#define ERROR_CLASS_ALREADY_EXISTS __MSABI_LONG(1410)
#define ERROR_CLASS_DOES_NOT_EXIST __MSABI_LONG(1411)
#define ERROR_CLASS_HAS_WINDOWS __MSABI_LONG(1412)
#define ERROR_INVALID_INDEX __MSABI_LONG(1413)
#define ERROR_INVALID_ICON_HANDLE __MSABI_LONG(1414)
#define ERROR_PRIVATE_DIALOG_INDEX __MSABI_LONG(1415)
#define ERROR_LISTBOX_ID_NOT_FOUND __MSABI_LONG(1416)
#define ERROR_NO_WILDCARD_CHARACTERS __MSABI_LONG(1417)
#define ERROR_CLIPBOARD_NOT_OPEN __MSABI_LONG(1418)
#define ERROR_HOTKEY_NOT_REGISTERED __MSABI_LONG(1419)
#define ERROR_WINDOW_NOT_DIALOG __MSABI_LONG(1420)
#define ERROR_CONTROL_ID_NOT_FOUND __MSABI_LONG(1421)
#define ERROR_INVALID_COMBOBOX_MESSAGE __MSABI_LONG(1422)
#define ERROR_WINDOW_NOT_COMBOBOX __MSABI_LONG(1423)
#define ERROR_INVALID_EDIT_HEIGHT __MSABI_LONG(1424)
#define ERROR_DC_NOT_FOUND __MSABI_LONG(1425)
#define ERROR_INVALID_HOOK_FILTER __MSABI_LONG(1426)
#define ERROR_INVALID_FILTER_PROC __MSABI_LONG(1427)
#define ERROR_HOOK_NEEDS_HMOD __MSABI_LONG(1428)
#define ERROR_GLOBAL_ONLY_HOOK __MSABI_LONG(1429)
#define ERROR_JOURNAL_HOOK_SET __MSABI_LONG(1430)
#define ERROR_HOOK_NOT_INSTALLED __MSABI_LONG(1431)
#define ERROR_INVALID_LB_MESSAGE __MSABI_LONG(1432)
#define ERROR_SETCOUNT_ON_BAD_LB __MSABI_LONG(1433)
#define ERROR_LB_WITHOUT_TABSTOPS __MSABI_LONG(1434)
#define ERROR_DESTROY_OBJECT_OF_OTHER_THREAD __MSABI_LONG(1435)
#define ERROR_CHILD_WINDOW_MENU __MSABI_LONG(1436)
#define ERROR_NO_SYSTEM_MENU __MSABI_LONG(1437)
#define ERROR_INVALID_MSGBOX_STYLE __MSABI_LONG(1438)
#define ERROR_INVALID_SPI_VALUE __MSABI_LONG(1439)
#define ERROR_SCREEN_ALREADY_LOCKED __MSABI_LONG(1440)
#define ERROR_HWNDS_HAVE_DIFF_PARENT __MSABI_LONG(1441)
#define ERROR_NOT_CHILD_WINDOW __MSABI_LONG(1442)
#define ERROR_INVALID_GW_COMMAND __MSABI_LONG(1443)
#define ERROR_INVALID_THREAD_ID __MSABI_LONG(1444)
#define ERROR_NON_MDICHILD_WINDOW __MSABI_LONG(1445)
#define ERROR_POPUP_ALREADY_ACTIVE __MSABI_LONG(1446)
#define ERROR_NO_SCROLLBARS __MSABI_LONG(1447)
#define ERROR_INVALID_SCROLLBAR_RANGE __MSABI_LONG(1448)
#define ERROR_INVALID_SHOWWIN_COMMAND __MSABI_LONG(1449)
#define ERROR_NO_SYSTEM_RESOURCES __MSABI_LONG(1450)
#define ERROR_NONPAGED_SYSTEM_RESOURCES __MSABI_LONG(1451)
#define ERROR_PAGED_SYSTEM_RESOURCES __MSABI_LONG(1452)
#define ERROR_WORKING_SET_QUOTA __MSABI_LONG(1453)
#define ERROR_PAGEFILE_QUOTA __MSABI_LONG(1454)
#define ERROR_COMMITMENT_LIMIT __MSABI_LONG(1455)
#define ERROR_MENU_ITEM_NOT_FOUND __MSABI_LONG(1456)
#define ERROR_INVALID_KEYBOARD_HANDLE __MSABI_LONG(1457)
#define ERROR_HOOK_TYPE_NOT_ALLOWED __MSABI_LONG(1458)
#define ERROR_REQUIRES_INTERACTIVE_WINDOWSTATION __MSABI_LONG(1459)
#define ERROR_TIMEOUT __MSABI_LONG(1460)
#define ERROR_INVALID_MONITOR_HANDLE __MSABI_LONG(1461)
#define ERROR_INCORRECT_SIZE __MSABI_LONG(1462)
#define ERROR_SYMLINK_CLASS_DISABLED __MSABI_LONG(1463)
#define ERROR_SYMLINK_NOT_SUPPORTED __MSABI_LONG(1464)
#define ERROR_XML_PARSE_ERROR __MSABI_LONG(1465)
#define ERROR_XMLDSIG_ERROR __MSABI_LONG(1466)
#define ERROR_RESTART_APPLICATION __MSABI_LONG(1467)
#define ERROR_WRONG_COMPARTMENT __MSABI_LONG(1468)
#define ERROR_AUTHIP_FAILURE __MSABI_LONG(1469)
#define ERROR_NO_NVRAM_RESOURCES __MSABI_LONG(1470)
#define ERROR_NOT_GUI_PROCESS __MSABI_LONG(1471)
#define ERROR_EVENTLOG_FILE_CORRUPT __MSABI_LONG(1500)
#define ERROR_EVENTLOG_CANT_START __MSABI_LONG(1501)
#define ERROR_LOG_FILE_FULL __MSABI_LONG(1502)
#define ERROR_EVENTLOG_FILE_CHANGED __MSABI_LONG(1503)
#define ERROR_CONTAINER_ASSIGNED __MSABI_LONG(1504)
#define ERROR_JOB_NO_CONTAINER __MSABI_LONG(1505)
#define ERROR_INVALID_TASK_NAME __MSABI_LONG(1550)
#define ERROR_INVALID_TASK_INDEX __MSABI_LONG(1551)
#define ERROR_THREAD_ALREADY_IN_TASK __MSABI_LONG(1552)
#define ERROR_INSTALL_SERVICE_FAILURE __MSABI_LONG(1601)
#define ERROR_INSTALL_USEREXIT __MSABI_LONG(1602)
#define ERROR_INSTALL_FAILURE __MSABI_LONG(1603)
#define ERROR_INSTALL_SUSPEND __MSABI_LONG(1604)
#define ERROR_UNKNOWN_PRODUCT __MSABI_LONG(1605)
#define ERROR_UNKNOWN_FEATURE __MSABI_LONG(1606)
#define ERROR_UNKNOWN_COMPONENT __MSABI_LONG(1607)
#define ERROR_UNKNOWN_PROPERTY __MSABI_LONG(1608)
#define ERROR_INVALID_HANDLE_STATE __MSABI_LONG(1609)
#define ERROR_BAD_CONFIGURATION __MSABI_LONG(1610)
#define ERROR_INDEX_ABSENT __MSABI_LONG(1611)
#define ERROR_INSTALL_SOURCE_ABSENT __MSABI_LONG(1612)
#define ERROR_INSTALL_PACKAGE_VERSION __MSABI_LONG(1613)
#define ERROR_PRODUCT_UNINSTALLED __MSABI_LONG(1614)
#define ERROR_BAD_QUERY_SYNTAX __MSABI_LONG(1615)
#define ERROR_INVALID_FIELD __MSABI_LONG(1616)
#define ERROR_DEVICE_REMOVED __MSABI_LONG(1617)
#define ERROR_INSTALL_ALREADY_RUNNING __MSABI_LONG(1618)
#define ERROR_INSTALL_PACKAGE_OPEN_FAILED __MSABI_LONG(1619)
#define ERROR_INSTALL_PACKAGE_INVALID __MSABI_LONG(1620)
#define ERROR_INSTALL_UI_FAILURE __MSABI_LONG(1621)
#define ERROR_INSTALL_LOG_FAILURE __MSABI_LONG(1622)
#define ERROR_INSTALL_LANGUAGE_UNSUPPORTED __MSABI_LONG(1623)
#define ERROR_INSTALL_TRANSFORM_FAILURE __MSABI_LONG(1624)
#define ERROR_INSTALL_PACKAGE_REJECTED __MSABI_LONG(1625)
#define ERROR_FUNCTION_NOT_CALLED __MSABI_LONG(1626)
#define ERROR_FUNCTION_FAILED __MSABI_LONG(1627)
#define ERROR_INVALID_TABLE __MSABI_LONG(1628)
#define ERROR_DATATYPE_MISMATCH __MSABI_LONG(1629)
#define ERROR_UNSUPPORTED_TYPE __MSABI_LONG(1630)
#define ERROR_CREATE_FAILED __MSABI_LONG(1631)
#define ERROR_INSTALL_TEMP_UNWRITABLE __MSABI_LONG(1632)
#define ERROR_INSTALL_PLATFORM_UNSUPPORTED __MSABI_LONG(1633)
#define ERROR_INSTALL_NOTUSED __MSABI_LONG(1634)
#define ERROR_PATCH_PACKAGE_OPEN_FAILED __MSABI_LONG(1635)
#define ERROR_PATCH_PACKAGE_INVALID __MSABI_LONG(1636)
#define ERROR_PATCH_PACKAGE_UNSUPPORTED __MSABI_LONG(1637)
#define ERROR_PRODUCT_VERSION __MSABI_LONG(1638)
#define ERROR_INVALID_COMMAND_LINE __MSABI_LONG(1639)
#define ERROR_INSTALL_REMOTE_DISALLOWED __MSABI_LONG(1640)
#define ERROR_SUCCESS_REBOOT_INITIATED __MSABI_LONG(1641)
#define ERROR_PATCH_TARGET_NOT_FOUND __MSABI_LONG(1642)
#define ERROR_PATCH_PACKAGE_REJECTED __MSABI_LONG(1643)
#define ERROR_INSTALL_TRANSFORM_REJECTED __MSABI_LONG(1644)
#define ERROR_INSTALL_REMOTE_PROHIBITED __MSABI_LONG(1645)
#define ERROR_PATCH_REMOVAL_UNSUPPORTED __MSABI_LONG(1646)
#define ERROR_UNKNOWN_PATCH __MSABI_LONG(1647)
#define ERROR_PATCH_NO_SEQUENCE __MSABI_LONG(1648)
#define ERROR_PATCH_REMOVAL_DISALLOWED __MSABI_LONG(1649)
#define ERROR_INVALID_PATCH_XML __MSABI_LONG(1650)
#define ERROR_PATCH_MANAGED_ADVERTISED_PRODUCT __MSABI_LONG(1651)
#define ERROR_INSTALL_SERVICE_SAFEBOOT __MSABI_LONG(1652)
#define ERROR_FAIL_FAST_EXCEPTION __MSABI_LONG(1653)
#define ERROR_INSTALL_REJECTED __MSABI_LONG(1654)
#define ERROR_DYNAMIC_CODE_BLOCKED __MSABI_LONG(1655)
#define ERROR_NOT_SAME_OBJECT __MSABI_LONG(1656)
#define ERROR_STRICT_CFG_VIOLATION __MSABI_LONG(1657)
#define ERROR_SET_CONTEXT_DENIED __MSABI_LONG(1660)
#define ERROR_CROSS_PARTITION_VIOLATION __MSABI_LONG(1661)
#define ERROR_RETURN_ADDRESS_HIJACK_ATTEMPT __MSABI_LONG(1662)
#define RPC_S_INVALID_STRING_BINDING __MSABI_LONG(1700)
#define RPC_S_WRONG_KIND_OF_BINDING __MSABI_LONG(1701)
#define RPC_S_INVALID_BINDING __MSABI_LONG(1702)
#define RPC_S_PROTSEQ_NOT_SUPPORTED __MSABI_LONG(1703)
#define RPC_S_INVALID_RPC_PROTSEQ __MSABI_LONG(1704)
#define RPC_S_INVALID_STRING_UUID __MSABI_LONG(1705)
#define RPC_S_INVALID_ENDPOINT_FORMAT __MSABI_LONG(1706)
#define RPC_S_INVALID_NET_ADDR __MSABI_LONG(1707)
#define RPC_S_NO_ENDPOINT_FOUND __MSABI_LONG(1708)
#define RPC_S_INVALID_TIMEOUT __MSABI_LONG(1709)
#define RPC_S_OBJECT_NOT_FOUND __MSABI_LONG(1710)
#define RPC_S_ALREADY_REGISTERED __MSABI_LONG(1711)
#define RPC_S_TYPE_ALREADY_REGISTERED __MSABI_LONG(1712)
#define RPC_S_ALREADY_LISTENING __MSABI_LONG(1713)
#define RPC_S_NO_PROTSEQS_REGISTERED __MSABI_LONG(1714)
#define RPC_S_NOT_LISTENING __MSABI_LONG(1715)
#define RPC_S_UNKNOWN_MGR_TYPE __MSABI_LONG(1716)
#define RPC_S_UNKNOWN_IF __MSABI_LONG(1717)
#define RPC_S_NO_BINDINGS __MSABI_LONG(1718)
#define RPC_S_NO_PROTSEQS __MSABI_LONG(1719)
#define RPC_S_CANT_CREATE_ENDPOINT __MSABI_LONG(1720)
#define RPC_S_OUT_OF_RESOURCES __MSABI_LONG(1721)
#define RPC_S_SERVER_UNAVAILABLE __MSABI_LONG(1722)
#define RPC_S_SERVER_TOO_BUSY __MSABI_LONG(1723)
#define RPC_S_INVALID_NETWORK_OPTIONS __MSABI_LONG(1724)
#define RPC_S_NO_CALL_ACTIVE __MSABI_LONG(1725)
#define RPC_S_CALL_FAILED __MSABI_LONG(1726)
#define RPC_S_CALL_FAILED_DNE __MSABI_LONG(1727)
#define RPC_S_PROTOCOL_ERROR __MSABI_LONG(1728)
#define RPC_S_PROXY_ACCESS_DENIED __MSABI_LONG(1729)
#define RPC_S_UNSUPPORTED_TRANS_SYN __MSABI_LONG(1730)
#define RPC_S_UNSUPPORTED_TYPE __MSABI_LONG(1732)
#define RPC_S_INVALID_TAG __MSABI_LONG(1733)
#define RPC_S_INVALID_BOUND __MSABI_LONG(1734)
#define RPC_S_NO_ENTRY_NAME __MSABI_LONG(1735)
#define RPC_S_INVALID_NAME_SYNTAX __MSABI_LONG(1736)
#define RPC_S_UNSUPPORTED_NAME_SYNTAX __MSABI_LONG(1737)
#define RPC_S_UUID_NO_ADDRESS __MSABI_LONG(1739)
#define RPC_S_DUPLICATE_ENDPOINT __MSABI_LONG(1740)
#define RPC_S_UNKNOWN_AUTHN_TYPE __MSABI_LONG(1741)
#define RPC_S_MAX_CALLS_TOO_SMALL __MSABI_LONG(1742)
#define RPC_S_STRING_TOO_LONG __MSABI_LONG(1743)
#define RPC_S_PROTSEQ_NOT_FOUND __MSABI_LONG(1744)
#define RPC_S_PROCNUM_OUT_OF_RANGE __MSABI_LONG(1745)
#define RPC_S_BINDING_HAS_NO_AUTH __MSABI_LONG(1746)
#define RPC_S_UNKNOWN_AUTHN_SERVICE __MSABI_LONG(1747)
#define RPC_S_UNKNOWN_AUTHN_LEVEL __MSABI_LONG(1748)
#define RPC_S_INVALID_AUTH_IDENTITY __MSABI_LONG(1749)
#define RPC_S_UNKNOWN_AUTHZ_SERVICE __MSABI_LONG(1750)
#define EPT_S_INVALID_ENTRY __MSABI_LONG(1751)
#define EPT_S_CANT_PERFORM_OP __MSABI_LONG(1752)
#define EPT_S_NOT_REGISTERED __MSABI_LONG(1753)
#define RPC_S_NOTHING_TO_EXPORT __MSABI_LONG(1754)
#define RPC_S_INCOMPLETE_NAME __MSABI_LONG(1755)
#define RPC_S_INVALID_VERS_OPTION __MSABI_LONG(1756)
#define RPC_S_NO_MORE_MEMBERS __MSABI_LONG(1757)
#define RPC_S_NOT_ALL_OBJS_UNEXPORTED __MSABI_LONG(1758)
#define RPC_S_INTERFACE_NOT_FOUND __MSABI_LONG(1759)
#define RPC_S_ENTRY_ALREADY_EXISTS __MSABI_LONG(1760)
#define RPC_S_ENTRY_NOT_FOUND __MSABI_LONG(1761)
#define RPC_S_NAME_SERVICE_UNAVAILABLE __MSABI_LONG(1762)
#define RPC_S_INVALID_NAF_ID __MSABI_LONG(1763)
#define RPC_S_CANNOT_SUPPORT __MSABI_LONG(1764)
#define RPC_S_NO_CONTEXT_AVAILABLE __MSABI_LONG(1765)
#define RPC_S_INTERNAL_ERROR __MSABI_LONG(1766)
#define RPC_S_ZERO_DIVIDE __MSABI_LONG(1767)
#define RPC_S_ADDRESS_ERROR __MSABI_LONG(1768)
#define RPC_S_FP_DIV_ZERO __MSABI_LONG(1769)
#define RPC_S_FP_UNDERFLOW __MSABI_LONG(1770)
#define RPC_S_FP_OVERFLOW __MSABI_LONG(1771)
#define RPC_X_NO_MORE_ENTRIES __MSABI_LONG(1772)
#define RPC_X_SS_CHAR_TRANS_OPEN_FAIL __MSABI_LONG(1773)
#define RPC_X_SS_CHAR_TRANS_SHORT_FILE __MSABI_LONG(1774)
#define RPC_X_SS_IN_NULL_CONTEXT __MSABI_LONG(1775)
#define RPC_X_SS_CONTEXT_DAMAGED __MSABI_LONG(1777)
#define RPC_X_SS_HANDLES_MISMATCH __MSABI_LONG(1778)
#define RPC_X_SS_CANNOT_GET_CALL_HANDLE __MSABI_LONG(1779)
#define RPC_X_NULL_REF_POINTER __MSABI_LONG(1780)
#define RPC_X_ENUM_VALUE_OUT_OF_RANGE __MSABI_LONG(1781)
#define RPC_X_BYTE_COUNT_TOO_SMALL __MSABI_LONG(1782)
#define RPC_X_BAD_STUB_DATA __MSABI_LONG(1783)
#define ERROR_INVALID_USER_BUFFER __MSABI_LONG(1784)
#define ERROR_UNRECOGNIZED_MEDIA __MSABI_LONG(1785)
#define ERROR_NO_TRUST_LSA_SECRET __MSABI_LONG(1786)
#define ERROR_NO_TRUST_SAM_ACCOUNT __MSABI_LONG(1787)
#define ERROR_TRUSTED_DOMAIN_FAILURE __MSABI_LONG(1788)
#define ERROR_TRUSTED_RELATIONSHIP_FAILURE __MSABI_LONG(1789)
#define ERROR_TRUST_FAILURE __MSABI_LONG(1790)
#define RPC_S_CALL_IN_PROGRESS __MSABI_LONG(1791)
#define ERROR_NETLOGON_NOT_STARTED __MSABI_LONG(1792)
#define ERROR_ACCOUNT_EXPIRED __MSABI_LONG(1793)
#define ERROR_REDIRECTOR_HAS_OPEN_HANDLES __MSABI_LONG(1794)
#define ERROR_PRINTER_DRIVER_ALREADY_INSTALLED __MSABI_LONG(1795)
#define ERROR_UNKNOWN_PORT __MSABI_LONG(1796)
#define ERROR_UNKNOWN_PRINTER_DRIVER __MSABI_LONG(1797)
#define ERROR_UNKNOWN_PRINTPROCESSOR __MSABI_LONG(1798)
#define ERROR_INVALID_SEPARATOR_FILE __MSABI_LONG(1799)
#define ERROR_INVALID_PRIORITY __MSABI_LONG(1800)
#define ERROR_INVALID_PRINTER_NAME __MSABI_LONG(1801)
#define ERROR_PRINTER_ALREADY_EXISTS __MSABI_LONG(1802)
#define ERROR_INVALID_PRINTER_COMMAND __MSABI_LONG(1803)
#define ERROR_INVALID_DATATYPE __MSABI_LONG(1804)
#define ERROR_INVALID_ENVIRONMENT __MSABI_LONG(1805)
#define RPC_S_NO_MORE_BINDINGS __MSABI_LONG(1806)
#define ERROR_NOLOGON_INTERDOMAIN_TRUST_ACCOUNT __MSABI_LONG(1807)
#define ERROR_NOLOGON_WORKSTATION_TRUST_ACCOUNT __MSABI_LONG(1808)
#define ERROR_NOLOGON_SERVER_TRUST_ACCOUNT __MSABI_LONG(1809)
#define ERROR_DOMAIN_TRUST_INCONSISTENT __MSABI_LONG(1810)
#define ERROR_SERVER_HAS_OPEN_HANDLES __MSABI_LONG(1811)
#define ERROR_RESOURCE_DATA_NOT_FOUND __MSABI_LONG(1812)
#define ERROR_RESOURCE_TYPE_NOT_FOUND __MSABI_LONG(1813)
#define ERROR_RESOURCE_NAME_NOT_FOUND __MSABI_LONG(1814)
#define ERROR_RESOURCE_LANG_NOT_FOUND __MSABI_LONG(1815)
#define ERROR_NOT_ENOUGH_QUOTA __MSABI_LONG(1816)
#define RPC_S_NO_INTERFACES __MSABI_LONG(1817)
#define RPC_S_CALL_CANCELLED __MSABI_LONG(1818)
#define RPC_S_BINDING_INCOMPLETE __MSABI_LONG(1819)
#define RPC_S_COMM_FAILURE __MSABI_LONG(1820)
#define RPC_S_UNSUPPORTED_AUTHN_LEVEL __MSABI_LONG(1821)
#define RPC_S_NO_PRINC_NAME __MSABI_LONG(1822)
#define RPC_S_NOT_RPC_ERROR __MSABI_LONG(1823)
#define RPC_S_UUID_LOCAL_ONLY __MSABI_LONG(1824)
#define RPC_S_SEC_PKG_ERROR __MSABI_LONG(1825)
#define RPC_S_NOT_CANCELLED __MSABI_LONG(1826)
#define RPC_X_INVALID_ES_ACTION __MSABI_LONG(1827)
#define RPC_X_WRONG_ES_VERSION __MSABI_LONG(1828)
#define RPC_X_WRONG_STUB_VERSION __MSABI_LONG(1829)
#define RPC_X_INVALID_PIPE_OBJECT __MSABI_LONG(1830)
#define RPC_X_WRONG_PIPE_ORDER __MSABI_LONG(1831)
#define RPC_X_WRONG_PIPE_VERSION __MSABI_LONG(1832)
#define RPC_S_COOKIE_AUTH_FAILED __MSABI_LONG(1833)
#define RPC_S_DO_NOT_DISTURB __MSABI_LONG(1834)
#define RPC_S_SYSTEM_HANDLE_COUNT_EXCEEDED __MSABI_LONG(1835)
#define RPC_S_SYSTEM_HANDLE_TYPE_MISMATCH __MSABI_LONG(1836)
#define RPC_S_GROUP_MEMBER_NOT_FOUND __MSABI_LONG(1898)
#define EPT_S_CANT_CREATE __MSABI_LONG(1899)
#define RPC_S_INVALID_OBJECT __MSABI_LONG(1900)
#define ERROR_INVALID_TIME __MSABI_LONG(1901)
#define ERROR_INVALID_FORM_NAME __MSABI_LONG(1902)
#define ERROR_INVALID_FORM_SIZE __MSABI_LONG(1903)
#define ERROR_ALREADY_WAITING __MSABI_LONG(1904)
#define ERROR_PRINTER_DELETED __MSABI_LONG(1905)
#define ERROR_INVALID_PRINTER_STATE __MSABI_LONG(1906)
#define ERROR_PASSWORD_MUST_CHANGE __MSABI_LONG(1907)
#define ERROR_DOMAIN_CONTROLLER_NOT_FOUND __MSABI_LONG(1908)
#define ERROR_ACCOUNT_LOCKED_OUT __MSABI_LONG(1909)
#define OR_INVALID_OXID __MSABI_LONG(1910)
#define OR_INVALID_OID __MSABI_LONG(1911)
#define OR_INVALID_SET __MSABI_LONG(1912)
#define RPC_S_SEND_INCOMPLETE __MSABI_LONG(1913)
#define RPC_S_INVALID_ASYNC_HANDLE __MSABI_LONG(1914)
#define RPC_S_INVALID_ASYNC_CALL __MSABI_LONG(1915)
#define RPC_X_PIPE_CLOSED __MSABI_LONG(1916)
#define RPC_X_PIPE_DISCIPLINE_ERROR __MSABI_LONG(1917)
#define RPC_X_PIPE_EMPTY __MSABI_LONG(1918)
#define ERROR_NO_SITENAME __MSABI_LONG(1919)
#define ERROR_CANT_ACCESS_FILE __MSABI_LONG(1920)
#define ERROR_CANT_RESOLVE_FILENAME __MSABI_LONG(1921)
#define RPC_S_ENTRY_TYPE_MISMATCH __MSABI_LONG(1922)
#define RPC_S_NOT_ALL_OBJS_EXPORTED __MSABI_LONG(1923)
#define RPC_S_INTERFACE_NOT_EXPORTED __MSABI_LONG(1924)
#define RPC_S_PROFILE_NOT_ADDED __MSABI_LONG(1925)
#define RPC_S_PRF_ELT_NOT_ADDED __MSABI_LONG(1926)
#define RPC_S_PRF_ELT_NOT_REMOVED __MSABI_LONG(1927)
#define RPC_S_GRP_ELT_NOT_ADDED __MSABI_LONG(1928)
#define RPC_S_GRP_ELT_NOT_REMOVED __MSABI_LONG(1929)
#define ERROR_KM_DRIVER_BLOCKED __MSABI_LONG(1930)
#define ERROR_CONTEXT_EXPIRED __MSABI_LONG(1931)
#define ERROR_PER_USER_TRUST_QUOTA_EXCEEDED __MSABI_LONG(1932)
#define ERROR_ALL_USER_TRUST_QUOTA_EXCEEDED __MSABI_LONG(1933)
#define ERROR_USER_DELETE_TRUST_QUOTA_EXCEEDED __MSABI_LONG(1934)
#define ERROR_AUTHENTICATION_FIREWALL_FAILED __MSABI_LONG(1935)
#define ERROR_REMOTE_PRINT_CONNECTIONS_BLOCKED __MSABI_LONG(1936)
#define ERROR_NTLM_BLOCKED __MSABI_LONG(1937)
#define ERROR_PASSWORD_CHANGE_REQUIRED __MSABI_LONG(1938)
#define ERROR_LOST_MODE_LOGON_RESTRICTION __MSABI_LONG(1939)
#define ERROR_INVALID_PIXEL_FORMAT __MSABI_LONG(2000)
#define ERROR_BAD_DRIVER __MSABI_LONG(2001)
#define ERROR_INVALID_WINDOW_STYLE __MSABI_LONG(2002)
#define ERROR_METAFILE_NOT_SUPPORTED __MSABI_LONG(2003)
#define ERROR_TRANSFORM_NOT_SUPPORTED __MSABI_LONG(2004)
#define ERROR_CLIPPING_NOT_SUPPORTED __MSABI_LONG(2005)
#define ERROR_INVALID_CMM __MSABI_LONG(2010)
#define ERROR_INVALID_PROFILE __MSABI_LONG(2011)
#define ERROR_TAG_NOT_FOUND __MSABI_LONG(2012)
#define ERROR_TAG_NOT_PRESENT __MSABI_LONG(2013)
#define ERROR_DUPLICATE_TAG __MSABI_LONG(2014)
#define ERROR_PROFILE_NOT_ASSOCIATED_WITH_DEVICE __MSABI_LONG(2015)
#define ERROR_PROFILE_NOT_FOUND __MSABI_LONG(2016)
#define ERROR_INVALID_COLORSPACE __MSABI_LONG(2017)
#define ERROR_ICM_NOT_ENABLED __MSABI_LONG(2018)
#define ERROR_DELETING_ICM_XFORM __MSABI_LONG(2019)
#define ERROR_INVALID_TRANSFORM __MSABI_LONG(2020)
#define ERROR_COLORSPACE_MISMATCH __MSABI_LONG(2021)
#define ERROR_INVALID_COLORINDEX __MSABI_LONG(2022)
#define ERROR_PROFILE_DOES_NOT_MATCH_DEVICE __MSABI_LONG(2023)
#define ERROR_CONNECTED_OTHER_PASSWORD __MSABI_LONG(2108)
#define ERROR_CONNECTED_OTHER_PASSWORD_DEFAULT __MSABI_LONG(2109)
#define ERROR_BAD_USERNAME __MSABI_LONG(2202)
#define ERROR_NOT_CONNECTED __MSABI_LONG(2250)
#define ERROR_OPEN_FILES __MSABI_LONG(2401)
#define ERROR_ACTIVE_CONNECTIONS __MSABI_LONG(2402)
#define ERROR_DEVICE_IN_USE __MSABI_LONG(2404)
#define ERROR_UNKNOWN_PRINT_MONITOR __MSABI_LONG(3000)
#define ERROR_PRINTER_DRIVER_IN_USE __MSABI_LONG(3001)
#define ERROR_SPOOL_FILE_NOT_FOUND __MSABI_LONG(3002)
#define ERROR_SPL_NO_STARTDOC __MSABI_LONG(3003)
#define ERROR_SPL_NO_ADDJOB __MSABI_LONG(3004)
#define ERROR_PRINT_PROCESSOR_ALREADY_INSTALLED __MSABI_LONG(3005)
#define ERROR_PRINT_MONITOR_ALREADY_INSTALLED __MSABI_LONG(3006)
#define ERROR_INVALID_PRINT_MONITOR __MSABI_LONG(3007)
#define ERROR_PRINT_MONITOR_IN_USE __MSABI_LONG(3008)
#define ERROR_PRINTER_HAS_JOBS_QUEUED __MSABI_LONG(3009)
#define ERROR_SUCCESS_REBOOT_REQUIRED __MSABI_LONG(3010)
#define ERROR_SUCCESS_RESTART_REQUIRED __MSABI_LONG(3011)
#define ERROR_PRINTER_NOT_FOUND __MSABI_LONG(3012)
#define ERROR_PRINTER_DRIVER_WARNED __MSABI_LONG(3013)
#define ERROR_PRINTER_DRIVER_BLOCKED __MSABI_LONG(3014)
#define ERROR_PRINTER_DRIVER_PACKAGE_IN_USE __MSABI_LONG(3015)
#define ERROR_CORE_DRIVER_PACKAGE_NOT_FOUND __MSABI_LONG(3016)
#define ERROR_FAIL_REBOOT_REQUIRED __MSABI_LONG(3017)
#define ERROR_FAIL_REBOOT_INITIATED __MSABI_LONG(3018)
#define ERROR_PRINTER_DRIVER_DOWNLOAD_NEEDED __MSABI_LONG(3019)
#define ERROR_PRINT_JOB_RESTART_REQUIRED __MSABI_LONG(3020)
#define ERROR_INVALID_PRINTER_DRIVER_MANIFEST __MSABI_LONG(3021)
#define ERROR_PRINTER_NOT_SHAREABLE __MSABI_LONG(3022)
#define ERROR_SERVER_SERVICE_CALL_REQUIRES_SMB1 __MSABI_LONG(3023)
#define ERROR_NETWORK_AUTHENTICATION_PROMPT_CANCELED __MSABI_LONG(3024)
#define ERROR_REMOTE_MAILSLOTS_DEPRECATED __MSABI_LONG(3025)
#define ERROR_REQUEST_PAUSED __MSABI_LONG(3050)
#define ERROR_APPEXEC_CONDITION_NOT_SATISFIED __MSABI_LONG(3060)
#define ERROR_APPEXEC_HANDLE_INVALIDATED __MSABI_LONG(3061)
#define ERROR_APPEXEC_INVALID_HOST_GENERATION __MSABI_LONG(3062)
#define ERROR_APPEXEC_UNEXPECTED_PROCESS_REGISTRATION __MSABI_LONG(3063)
#define ERROR_APPEXEC_INVALID_HOST_STATE __MSABI_LONG(3064)
#define ERROR_APPEXEC_NO_DONOR __MSABI_LONG(3065)
#define ERROR_APPEXEC_HOST_ID_MISMATCH __MSABI_LONG(3066)
#define ERROR_APPEXEC_UNKNOWN_USER __MSABI_LONG(3067)
#define ERROR_APPEXEC_APP_COMPAT_BLOCK __MSABI_LONG(3068)
#define ERROR_APPEXEC_CALLER_WAIT_TIMEOUT __MSABI_LONG(3069)
#define ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_TERMINATION __MSABI_LONG(3070)
#define ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_LICENSING __MSABI_LONG(3071)
#define ERROR_APPEXEC_CALLER_WAIT_TIMEOUT_RESOURCES __MSABI_LONG(3072)
#define ERROR_VRF_VOLATILE_CFG_AND_IO_ENABLED __MSABI_LONG(3080)
#define ERROR_VRF_VOLATILE_NOT_STOPPABLE __MSABI_LONG(3081)
#define ERROR_VRF_VOLATILE_SAFE_MODE __MSABI_LONG(3082)
#define ERROR_VRF_VOLATILE_NOT_RUNNABLE_SYSTEM __MSABI_LONG(3083)
#define ERROR_VRF_VOLATILE_NOT_SUPPORTED_RULECLASS __MSABI_LONG(3084)
#define ERROR_VRF_VOLATILE_PROTECTED_DRIVER __MSABI_LONG(3085)
#define ERROR_VRF_VOLATILE_NMI_REGISTERED __MSABI_LONG(3086)
#define ERROR_VRF_VOLATILE_SETTINGS_CONFLICT __MSABI_LONG(3087)
#define ERROR_CAR_LKD_IN_PROGRESS __MSABI_LONG(3088)
#define ERROR_DIF_ZERO_SIZE_INFORMATION __MSABI_LONG(3187)
#define ERROR_DIF_DRIVER_PLUGIN_MISMATCH __MSABI_LONG(3188)
#define ERROR_DIF_DRIVER_THUNKS_NOT_ALLOWED __MSABI_LONG(3189)
#define ERROR_DIF_IOCALLBACK_NOT_REPLACED __MSABI_LONG(3190)
#define ERROR_DIF_LIVEDUMP_LIMIT_EXCEEDED __MSABI_LONG(3191)
#define ERROR_DIF_VOLATILE_SECTION_NOT_LOCKED __MSABI_LONG(3192)
#define ERROR_DIF_VOLATILE_DRIVER_HOTPATCHED __MSABI_LONG(3193)
#define ERROR_DIF_VOLATILE_INVALID_INFO __MSABI_LONG(3194)
#define ERROR_DIF_VOLATILE_DRIVER_IS_NOT_RUNNING __MSABI_LONG(3195)
#define ERROR_DIF_VOLATILE_PLUGIN_IS_NOT_RUNNING __MSABI_LONG(3196)
#define ERROR_DIF_VOLATILE_PLUGIN_CHANGE_NOT_ALLOWED __MSABI_LONG(3197)
#define ERROR_DIF_VOLATILE_NOT_ALLOWED __MSABI_LONG(3198)
#define ERROR_DIF_BINDING_API_NOT_FOUND __MSABI_LONG(3199)
#define ERROR_IO_REISSUE_AS_CACHED __MSABI_LONG(3950)
#define ERROR_WINS_INTERNAL __MSABI_LONG(4000)
#define ERROR_CAN_NOT_DEL_LOCAL_WINS __MSABI_LONG(4001)
#define ERROR_STATIC_INIT __MSABI_LONG(4002)
#define ERROR_INC_BACKUP __MSABI_LONG(4003)
#define ERROR_FULL_BACKUP __MSABI_LONG(4004)
#define ERROR_REC_NON_EXISTENT __MSABI_LONG(4005)
#define ERROR_RPL_NOT_ALLOWED __MSABI_LONG(4006)
#define PEERDIST_ERROR_CONTENTINFO_VERSION_UNSUPPORTED __MSABI_LONG(4050)
#define PEERDIST_ERROR_CANNOT_PARSE_CONTENTINFO __MSABI_LONG(4051)
#define PEERDIST_ERROR_MISSING_DATA __MSABI_LONG(4052)
#define PEERDIST_ERROR_NO_MORE __MSABI_LONG(4053)
#define PEERDIST_ERROR_NOT_INITIALIZED __MSABI_LONG(4054)
#define PEERDIST_ERROR_ALREADY_INITIALIZED __MSABI_LONG(4055)
#define PEERDIST_ERROR_SHUTDOWN_IN_PROGRESS __MSABI_LONG(4056)
#define PEERDIST_ERROR_INVALIDATED __MSABI_LONG(4057)
#define PEERDIST_ERROR_ALREADY_EXISTS __MSABI_LONG(4058)
#define PEERDIST_ERROR_OPERATION_NOTFOUND __MSABI_LONG(4059)
#define PEERDIST_ERROR_ALREADY_COMPLETED __MSABI_LONG(4060)
#define PEERDIST_ERROR_OUT_OF_BOUNDS __MSABI_LONG(4061)
#define PEERDIST_ERROR_VERSION_UNSUPPORTED __MSABI_LONG(4062)
#define PEERDIST_ERROR_INVALID_CONFIGURATION __MSABI_LONG(4063)
#define PEERDIST_ERROR_NOT_LICENSED __MSABI_LONG(4064)
#define PEERDIST_ERROR_SERVICE_UNAVAILABLE __MSABI_LONG(4065)
#define PEERDIST_ERROR_TRUST_FAILURE __MSABI_LONG(4066)
#define ERROR_DHCP_ADDRESS_CONFLICT __MSABI_LONG(4100)
#define ERROR_WMI_GUID_NOT_FOUND __MSABI_LONG(4200)
#define ERROR_WMI_INSTANCE_NOT_FOUND __MSABI_LONG(4201)
#define ERROR_WMI_ITEMID_NOT_FOUND __MSABI_LONG(4202)
#define ERROR_WMI_TRY_AGAIN __MSABI_LONG(4203)
#define ERROR_WMI_DP_NOT_FOUND __MSABI_LONG(4204)
#define ERROR_WMI_UNRESOLVED_INSTANCE_REF __MSABI_LONG(4205)
#define ERROR_WMI_ALREADY_ENABLED __MSABI_LONG(4206)
#define ERROR_WMI_GUID_DISCONNECTED __MSABI_LONG(4207)
#define ERROR_WMI_SERVER_UNAVAILABLE __MSABI_LONG(4208)
#define ERROR_WMI_DP_FAILED __MSABI_LONG(4209)
#define ERROR_WMI_INVALID_MOF __MSABI_LONG(4210)
#define ERROR_WMI_INVALID_REGINFO __MSABI_LONG(4211)
#define ERROR_WMI_ALREADY_DISABLED __MSABI_LONG(4212)
#define ERROR_WMI_READ_ONLY __MSABI_LONG(4213)
#define ERROR_WMI_SET_FAILURE __MSABI_LONG(4214)
#define ERROR_NOT_APPCONTAINER __MSABI_LONG(4250)
#define ERROR_APPCONTAINER_REQUIRED __MSABI_LONG(4251)
#define ERROR_NOT_SUPPORTED_IN_APPCONTAINER __MSABI_LONG(4252)
#define ERROR_INVALID_PACKAGE_SID_LENGTH __MSABI_LONG(4253)
#define ERROR_INVALID_MEDIA __MSABI_LONG(4300)
#define ERROR_INVALID_LIBRARY __MSABI_LONG(4301)
#define ERROR_INVALID_MEDIA_POOL __MSABI_LONG(4302)
#define ERROR_DRIVE_MEDIA_MISMATCH __MSABI_LONG(4303)
#define ERROR_MEDIA_OFFLINE __MSABI_LONG(4304)
#define ERROR_LIBRARY_OFFLINE __MSABI_LONG(4305)
#define ERROR_EMPTY __MSABI_LONG(4306)
#define ERROR_NOT_EMPTY __MSABI_LONG(4307)
#define ERROR_MEDIA_UNAVAILABLE __MSABI_LONG(4308)
#define ERROR_RESOURCE_DISABLED __MSABI_LONG(4309)
#define ERROR_INVALID_CLEANER __MSABI_LONG(4310)
#define ERROR_UNABLE_TO_CLEAN __MSABI_LONG(4311)
#define ERROR_OBJECT_NOT_FOUND __MSABI_LONG(4312)
#define ERROR_DATABASE_FAILURE __MSABI_LONG(4313)
#define ERROR_DATABASE_FULL __MSABI_LONG(4314)
#define ERROR_MEDIA_INCOMPATIBLE __MSABI_LONG(4315)
#define ERROR_RESOURCE_NOT_PRESENT __MSABI_LONG(4316)
#define ERROR_INVALID_OPERATION __MSABI_LONG(4317)
#define ERROR_MEDIA_NOT_AVAILABLE __MSABI_LONG(4318)
#define ERROR_DEVICE_NOT_AVAILABLE __MSABI_LONG(4319)
#define ERROR_REQUEST_REFUSED __MSABI_LONG(4320)
#define ERROR_INVALID_DRIVE_OBJECT __MSABI_LONG(4321)
#define ERROR_LIBRARY_FULL __MSABI_LONG(4322)
#define ERROR_MEDIUM_NOT_ACCESSIBLE __MSABI_LONG(4323)
#define ERROR_UNABLE_TO_LOAD_MEDIUM __MSABI_LONG(4324)
#define ERROR_UNABLE_TO_INVENTORY_DRIVE __MSABI_LONG(4325)
#define ERROR_UNABLE_TO_INVENTORY_SLOT __MSABI_LONG(4326)
#define ERROR_UNABLE_TO_INVENTORY_TRANSPORT __MSABI_LONG(4327)
#define ERROR_TRANSPORT_FULL __MSABI_LONG(4328)
#define ERROR_CONTROLLING_IEPORT __MSABI_LONG(4329)
#define ERROR_UNABLE_TO_EJECT_MOUNTED_MEDIA __MSABI_LONG(4330)
#define ERROR_CLEANER_SLOT_SET __MSABI_LONG(4331)
#define ERROR_CLEANER_SLOT_NOT_SET __MSABI_LONG(4332)
#define ERROR_CLEANER_CARTRIDGE_SPENT __MSABI_LONG(4333)
#define ERROR_UNEXPECTED_OMID __MSABI_LONG(4334)
#define ERROR_CANT_DELETE_LAST_ITEM __MSABI_LONG(4335)
#define ERROR_MESSAGE_EXCEEDS_MAX_SIZE __MSABI_LONG(4336)
#define ERROR_VOLUME_CONTAINS_SYS_FILES __MSABI_LONG(4337)
#define ERROR_INDIGENOUS_TYPE __MSABI_LONG(4338)
#define ERROR_NO_SUPPORTING_DRIVES __MSABI_LONG(4339)
#define ERROR_CLEANER_CARTRIDGE_INSTALLED __MSABI_LONG(4340)
#define ERROR_IEPORT_FULL __MSABI_LONG(4341)
#define ERROR_FILE_OFFLINE __MSABI_LONG(4350)
#define ERROR_REMOTE_STORAGE_NOT_ACTIVE __MSABI_LONG(4351)
#define ERROR_REMOTE_STORAGE_MEDIA_ERROR __MSABI_LONG(4352)
#define ERROR_NOT_A_REPARSE_POINT __MSABI_LONG(4390)
#define ERROR_REPARSE_ATTRIBUTE_CONFLICT __MSABI_LONG(4391)
#define ERROR_INVALID_REPARSE_DATA __MSABI_LONG(4392)
#define ERROR_REPARSE_TAG_INVALID __MSABI_LONG(4393)
#define ERROR_REPARSE_TAG_MISMATCH __MSABI_LONG(4394)
#define ERROR_REPARSE_POINT_ENCOUNTERED __MSABI_LONG(4395)
#define ERROR_APP_DATA_NOT_FOUND __MSABI_LONG(4400)
#define ERROR_APP_DATA_EXPIRED __MSABI_LONG(4401)
#define ERROR_APP_DATA_CORRUPT __MSABI_LONG(4402)
#define ERROR_APP_DATA_LIMIT_EXCEEDED __MSABI_LONG(4403)
#define ERROR_APP_DATA_REBOOT_REQUIRED __MSABI_LONG(4404)
#define ERROR_SECUREBOOT_ROLLBACK_DETECTED __MSABI_LONG(4420)
#define ERROR_SECUREBOOT_POLICY_VIOLATION __MSABI_LONG(4421)
#define ERROR_SECUREBOOT_INVALID_POLICY __MSABI_LONG(4422)
#define ERROR_SECUREBOOT_POLICY_PUBLISHER_NOT_FOUND __MSABI_LONG(4423)
#define ERROR_SECUREBOOT_POLICY_NOT_SIGNED __MSABI_LONG(4424)
#define ERROR_SECUREBOOT_NOT_ENABLED __MSABI_LONG(4425)
#define ERROR_SECUREBOOT_FILE_REPLACED __MSABI_LONG(4426)
#define ERROR_SECUREBOOT_POLICY_NOT_AUTHORIZED __MSABI_LONG(4427)
#define ERROR_SECUREBOOT_POLICY_UNKNOWN __MSABI_LONG(4428)
#define ERROR_SECUREBOOT_POLICY_MISSING_ANTIROLLBACKVERSION __MSABI_LONG(4429)
#define ERROR_SECUREBOOT_PLATFORM_ID_MISMATCH __MSABI_LONG(4430)
#define ERROR_SECUREBOOT_POLICY_ROLLBACK_DETECTED __MSABI_LONG(4431)
#define ERROR_SECUREBOOT_POLICY_UPGRADE_MISMATCH __MSABI_LONG(4432)
#define ERROR_SECUREBOOT_REQUIRED_POLICY_FILE_MISSING __MSABI_LONG(4433)
#define ERROR_SECUREBOOT_NOT_BASE_POLICY __MSABI_LONG(4434)
#define ERROR_SECUREBOOT_NOT_SUPPLEMENTAL_POLICY __MSABI_LONG(4435)
#define ERROR_OFFLOAD_READ_FLT_NOT_SUPPORTED __MSABI_LONG(4440)
#define ERROR_OFFLOAD_WRITE_FLT_NOT_SUPPORTED __MSABI_LONG(4441)
#define ERROR_OFFLOAD_READ_FILE_NOT_SUPPORTED __MSABI_LONG(4442)
#define ERROR_OFFLOAD_WRITE_FILE_NOT_SUPPORTED __MSABI_LONG(4443)
#define ERROR_ALREADY_HAS_STREAM_ID __MSABI_LONG(4444)
#define ERROR_SMR_GARBAGE_COLLECTION_REQUIRED __MSABI_LONG(4445)
#define ERROR_WOF_WIM_HEADER_CORRUPT __MSABI_LONG(4446)
#define ERROR_WOF_WIM_RESOURCE_TABLE_CORRUPT __MSABI_LONG(4447)
#define ERROR_WOF_FILE_RESOURCE_TABLE_CORRUPT __MSABI_LONG(4448)
#define ERROR_OBJECT_IS_IMMUTABLE __MSABI_LONG(4449)
#define ERROR_VOLUME_NOT_SIS_ENABLED __MSABI_LONG(4500)
#define ERROR_SYSTEM_INTEGRITY_ROLLBACK_DETECTED __MSABI_LONG(4550)
#define ERROR_SYSTEM_INTEGRITY_POLICY_VIOLATION __MSABI_LONG(4551)
#define ERROR_SYSTEM_INTEGRITY_INVALID_POLICY __MSABI_LONG(4552)
#define ERROR_SYSTEM_INTEGRITY_POLICY_NOT_SIGNED __MSABI_LONG(4553)
#define ERROR_SYSTEM_INTEGRITY_TOO_MANY_POLICIES __MSABI_LONG(4554)
#define ERROR_SYSTEM_INTEGRITY_SUPPLEMENTAL_POLICY_NOT_AUTHORIZED __MSABI_LONG(4555)
#define ERROR_SYSTEM_INTEGRITY_REPUTATION_MALICIOUS __MSABI_LONG(4556)
#define ERROR_SYSTEM_INTEGRITY_REPUTATION_PUA __MSABI_LONG(4557)
#define ERROR_SYSTEM_INTEGRITY_REPUTATION_DANGEROUS_EXT __MSABI_LONG(4558)
#define ERROR_SYSTEM_INTEGRITY_REPUTATION_OFFLINE __MSABI_LONG(4559)
#define ERROR_VSM_NOT_INITIALIZED __MSABI_LONG(4560)
#define ERROR_VSM_DMA_PROTECTION_NOT_IN_USE __MSABI_LONG(4561)
#define ERROR_PLATFORM_MANIFEST_NOT_AUTHORIZED __MSABI_LONG(4570)
#define ERROR_PLATFORM_MANIFEST_INVALID __MSABI_LONG(4571)
#define ERROR_PLATFORM_MANIFEST_FILE_NOT_AUTHORIZED __MSABI_LONG(4572)
#define ERROR_PLATFORM_MANIFEST_CATALOG_NOT_AUTHORIZED __MSABI_LONG(4573)
#define ERROR_PLATFORM_MANIFEST_BINARY_ID_NOT_FOUND __MSABI_LONG(4574)
#define ERROR_PLATFORM_MANIFEST_NOT_ACTIVE __MSABI_LONG(4575)
#define ERROR_PLATFORM_MANIFEST_NOT_SIGNED __MSABI_LONG(4576)
#define ERROR_SYSTEM_INTEGRITY_REPUTATION_UNFRIENDLY_FILE __MSABI_LONG(4580)
#define ERROR_SYSTEM_INTEGRITY_REPUTATION_UNATTAINABLE __MSABI_LONG(4581)
#define ERROR_SYSTEM_INTEGRITY_REPUTATION_EXPLICIT_DENY_FILE __MSABI_LONG(4582)
#define ERROR_SYSTEM_INTEGRITY_WHQL_NOT_SATISFIED __MSABI_LONG(4583)
#define ERROR_DEPENDENT_RESOURCE_EXISTS __MSABI_LONG(5001)
#define ERROR_DEPENDENCY_NOT_FOUND __MSABI_LONG(5002)
#define ERROR_DEPENDENCY_ALREADY_EXISTS __MSABI_LONG(5003)
#define ERROR_RESOURCE_NOT_ONLINE __MSABI_LONG(5004)
#define ERROR_HOST_NODE_NOT_AVAILABLE __MSABI_LONG(5005)
#define ERROR_RESOURCE_NOT_AVAILABLE __MSABI_LONG(5006)
#define ERROR_RESOURCE_NOT_FOUND __MSABI_LONG(5007)
#define ERROR_SHUTDOWN_CLUSTER __MSABI_LONG(5008)
#define ERROR_CANT_EVICT_ACTIVE_NODE __MSABI_LONG(5009)
#define ERROR_OBJECT_ALREADY_EXISTS __MSABI_LONG(5010)
#define ERROR_OBJECT_IN_LIST __MSABI_LONG(5011)
#define ERROR_GROUP_NOT_AVAILABLE __MSABI_LONG(5012)
#define ERROR_GROUP_NOT_FOUND __MSABI_LONG(5013)
#define ERROR_GROUP_NOT_ONLINE __MSABI_LONG(5014)
#define ERROR_HOST_NODE_NOT_RESOURCE_OWNER __MSABI_LONG(5015)
#define ERROR_HOST_NODE_NOT_GROUP_OWNER __MSABI_LONG(5016)
#define ERROR_RESMON_CREATE_FAILED __MSABI_LONG(5017)
#define ERROR_RESMON_ONLINE_FAILED __MSABI_LONG(5018)
#define ERROR_RESOURCE_ONLINE __MSABI_LONG(5019)
#define ERROR_QUORUM_RESOURCE __MSABI_LONG(5020)
#define ERROR_NOT_QUORUM_CAPABLE __MSABI_LONG(5021)
#define ERROR_CLUSTER_SHUTTING_DOWN __MSABI_LONG(5022)
#define ERROR_INVALID_STATE __MSABI_LONG(5023)
#define ERROR_RESOURCE_PROPERTIES_STORED __MSABI_LONG(5024)
#define ERROR_NOT_QUORUM_CLASS __MSABI_LONG(5025)
#define ERROR_CORE_RESOURCE __MSABI_LONG(5026)
#define ERROR_QUORUM_RESOURCE_ONLINE_FAILED __MSABI_LONG(5027)
#define ERROR_QUORUMLOG_OPEN_FAILED __MSABI_LONG(5028)
#define ERROR_CLUSTERLOG_CORRUPT __MSABI_LONG(5029)
#define ERROR_CLUSTERLOG_RECORD_EXCEEDS_MAXSIZE __MSABI_LONG(5030)
#define ERROR_CLUSTERLOG_EXCEEDS_MAXSIZE __MSABI_LONG(5031)
#define ERROR_CLUSTERLOG_CHKPOINT_NOT_FOUND __MSABI_LONG(5032)
#define ERROR_CLUSTERLOG_NOT_ENOUGH_SPACE __MSABI_LONG(5033)
#define ERROR_QUORUM_OWNER_ALIVE __MSABI_LONG(5034)
#define ERROR_NETWORK_NOT_AVAILABLE __MSABI_LONG(5035)
#define ERROR_NODE_NOT_AVAILABLE __MSABI_LONG(5036)
#define ERROR_ALL_NODES_NOT_AVAILABLE __MSABI_LONG(5037)
#define ERROR_RESOURCE_FAILED __MSABI_LONG(5038)
#define ERROR_CLUSTER_INVALID_NODE __MSABI_LONG(5039)
#define ERROR_CLUSTER_NODE_EXISTS __MSABI_LONG(5040)
#define ERROR_CLUSTER_JOIN_IN_PROGRESS __MSABI_LONG(5041)
#define ERROR_CLUSTER_NODE_NOT_FOUND __MSABI_LONG(5042)
#define ERROR_CLUSTER_LOCAL_NODE_NOT_FOUND __MSABI_LONG(5043)
#define ERROR_CLUSTER_NETWORK_EXISTS __MSABI_LONG(5044)
#define ERROR_CLUSTER_NETWORK_NOT_FOUND __MSABI_LONG(5045)
#define ERROR_CLUSTER_NETINTERFACE_EXISTS __MSABI_LONG(5046)
#define ERROR_CLUSTER_NETINTERFACE_NOT_FOUND __MSABI_LONG(5047)
#define ERROR_CLUSTER_INVALID_REQUEST __MSABI_LONG(5048)
#define ERROR_CLUSTER_INVALID_NETWORK_PROVIDER __MSABI_LONG(5049)
#define ERROR_CLUSTER_NODE_DOWN __MSABI_LONG(5050)
#define ERROR_CLUSTER_NODE_UNREACHABLE __MSABI_LONG(5051)
#define ERROR_CLUSTER_NODE_NOT_MEMBER __MSABI_LONG(5052)
#define ERROR_CLUSTER_JOIN_NOT_IN_PROGRESS __MSABI_LONG(5053)
#define ERROR_CLUSTER_INVALID_NETWORK __MSABI_LONG(5054)
#define ERROR_CLUSTER_NODE_UP __MSABI_LONG(5056)
#define ERROR_CLUSTER_IPADDR_IN_USE __MSABI_LONG(5057)
#define ERROR_CLUSTER_NODE_NOT_PAUSED __MSABI_LONG(5058)
#define ERROR_CLUSTER_NO_SECURITY_CONTEXT __MSABI_LONG(5059)
#define ERROR_CLUSTER_NETWORK_NOT_INTERNAL __MSABI_LONG(5060)
#define ERROR_CLUSTER_NODE_ALREADY_UP __MSABI_LONG(5061)
#define ERROR_CLUSTER_NODE_ALREADY_DOWN __MSABI_LONG(5062)
#define ERROR_CLUSTER_NETWORK_ALREADY_ONLINE __MSABI_LONG(5063)
#define ERROR_CLUSTER_NETWORK_ALREADY_OFFLINE __MSABI_LONG(5064)
#define ERROR_CLUSTER_NODE_ALREADY_MEMBER __MSABI_LONG(5065)
#define ERROR_CLUSTER_LAST_INTERNAL_NETWORK __MSABI_LONG(5066)
#define ERROR_CLUSTER_NETWORK_HAS_DEPENDENTS __MSABI_LONG(5067)
#define ERROR_INVALID_OPERATION_ON_QUORUM __MSABI_LONG(5068)
#define ERROR_DEPENDENCY_NOT_ALLOWED __MSABI_LONG(5069)
#define ERROR_CLUSTER_NODE_PAUSED __MSABI_LONG(5070)
#define ERROR_NODE_CANT_HOST_RESOURCE __MSABI_LONG(5071)
#define ERROR_CLUSTER_NODE_NOT_READY __MSABI_LONG(5072)
#define ERROR_CLUSTER_NODE_SHUTTING_DOWN __MSABI_LONG(5073)
#define ERROR_CLUSTER_JOIN_ABORTED __MSABI_LONG(5074)
#define ERROR_CLUSTER_INCOMPATIBLE_VERSIONS __MSABI_LONG(5075)
#define ERROR_CLUSTER_MAXNUM_OF_RESOURCES_EXCEEDED __MSABI_LONG(5076)
#define ERROR_CLUSTER_SYSTEM_CONFIG_CHANGED __MSABI_LONG(5077)
#define ERROR_CLUSTER_RESOURCE_TYPE_NOT_FOUND __MSABI_LONG(5078)
#define ERROR_CLUSTER_RESTYPE_NOT_SUPPORTED __MSABI_LONG(5079)
#define ERROR_CLUSTER_RESNAME_NOT_FOUND __MSABI_LONG(5080)
#define ERROR_CLUSTER_NO_RPC_PACKAGES_REGISTERED __MSABI_LONG(5081)
#define ERROR_CLUSTER_OWNER_NOT_IN_PREFLIST __MSABI_LONG(5082)
#define ERROR_CLUSTER_DATABASE_SEQMISMATCH __MSABI_LONG(5083)
#define ERROR_RESMON_INVALID_STATE __MSABI_LONG(5084)
#define ERROR_CLUSTER_GUM_NOT_LOCKER __MSABI_LONG(5085)
#define ERROR_QUORUM_DISK_NOT_FOUND __MSABI_LONG(5086)
#define ERROR_DATABASE_BACKUP_CORRUPT __MSABI_LONG(5087)
#define ERROR_CLUSTER_NODE_ALREADY_HAS_DFS_ROOT __MSABI_LONG(5088)
#define ERROR_RESOURCE_PROPERTY_UNCHANGEABLE __MSABI_LONG(5089)
#define ERROR_NO_ADMIN_ACCESS_POINT __MSABI_LONG(5090)
#define ERROR_CLUSTER_MEMBERSHIP_INVALID_STATE __MSABI_LONG(5890)
#define ERROR_CLUSTER_QUORUMLOG_NOT_FOUND __MSABI_LONG(5891)
#define ERROR_CLUSTER_MEMBERSHIP_HALT __MSABI_LONG(5892)
#define ERROR_CLUSTER_INSTANCE_ID_MISMATCH __MSABI_LONG(5893)
#define ERROR_CLUSTER_NETWORK_NOT_FOUND_FOR_IP __MSABI_LONG(5894)
#define ERROR_CLUSTER_PROPERTY_DATA_TYPE_MISMATCH __MSABI_LONG(5895)
#define ERROR_CLUSTER_EVICT_WITHOUT_CLEANUP __MSABI_LONG(5896)
#define ERROR_CLUSTER_PARAMETER_MISMATCH __MSABI_LONG(5897)
#define ERROR_NODE_CANNOT_BE_CLUSTERED __MSABI_LONG(5898)
#define ERROR_CLUSTER_WRONG_OS_VERSION __MSABI_LONG(5899)
#define ERROR_CLUSTER_CANT_CREATE_DUP_CLUSTER_NAME __MSABI_LONG(5900)
#define ERROR_CLUSCFG_ALREADY_COMMITTED __MSABI_LONG(5901)
#define ERROR_CLUSCFG_ROLLBACK_FAILED __MSABI_LONG(5902)
#define ERROR_CLUSCFG_SYSTEM_DISK_DRIVE_LETTER_CONFLICT __MSABI_LONG(5903)
#define ERROR_CLUSTER_OLD_VERSION __MSABI_LONG(5904)
#define ERROR_CLUSTER_MISMATCHED_COMPUTER_ACCT_NAME __MSABI_LONG(5905)
#define ERROR_CLUSTER_NO_NET_ADAPTERS __MSABI_LONG(5906)
#define ERROR_CLUSTER_POISONED __MSABI_LONG(5907)
#define ERROR_CLUSTER_GROUP_MOVING __MSABI_LONG(5908)
#define ERROR_CLUSTER_RESOURCE_TYPE_BUSY __MSABI_LONG(5909)
#define ERROR_RESOURCE_CALL_TIMED_OUT __MSABI_LONG(5910)
#define ERROR_INVALID_CLUSTER_IPV6_ADDRESS __MSABI_LONG(5911)
#define ERROR_CLUSTER_INTERNAL_INVALID_FUNCTION __MSABI_LONG(5912)
#define ERROR_CLUSTER_PARAMETER_OUT_OF_BOUNDS __MSABI_LONG(5913)
#define ERROR_CLUSTER_PARTIAL_SEND __MSABI_LONG(5914)
#define ERROR_CLUSTER_REGISTRY_INVALID_FUNCTION __MSABI_LONG(5915)
#define ERROR_CLUSTER_INVALID_STRING_TERMINATION __MSABI_LONG(5916)
#define ERROR_CLUSTER_INVALID_STRING_FORMAT __MSABI_LONG(5917)
#define ERROR_CLUSTER_DATABASE_TRANSACTION_IN_PROGRESS __MSABI_LONG(5918)
#define ERROR_CLUSTER_DATABASE_TRANSACTION_NOT_IN_PROGRESS __MSABI_LONG(5919)
#define ERROR_CLUSTER_NULL_DATA __MSABI_LONG(5920)
#define ERROR_CLUSTER_PARTIAL_READ __MSABI_LONG(5921)
#define ERROR_CLUSTER_PARTIAL_WRITE __MSABI_LONG(5922)
#define ERROR_CLUSTER_CANT_DESERIALIZE_DATA __MSABI_LONG(5923)
#define ERROR_DEPENDENT_RESOURCE_PROPERTY_CONFLICT __MSABI_LONG(5924)
#define ERROR_CLUSTER_NO_QUORUM __MSABI_LONG(5925)
#define ERROR_CLUSTER_INVALID_IPV6_NETWORK __MSABI_LONG(5926)
#define ERROR_CLUSTER_INVALID_IPV6_TUNNEL_NETWORK __MSABI_LONG(5927)
#define ERROR_QUORUM_NOT_ALLOWED_IN_THIS_GROUP __MSABI_LONG(5928)
#define ERROR_DEPENDENCY_TREE_TOO_COMPLEX __MSABI_LONG(5929)
#define ERROR_EXCEPTION_IN_RESOURCE_CALL __MSABI_LONG(5930)
#define ERROR_CLUSTER_RHS_FAILED_INITIALIZATION __MSABI_LONG(5931)
#define ERROR_CLUSTER_NOT_INSTALLED __MSABI_LONG(5932)
#define ERROR_CLUSTER_RESOURCES_MUST_BE_ONLINE_ON_THE_SAME_NODE __MSABI_LONG(5933)
#define ERROR_CLUSTER_MAX_NODES_IN_CLUSTER __MSABI_LONG(5934)
#define ERROR_CLUSTER_TOO_MANY_NODES __MSABI_LONG(5935)
#define ERROR_CLUSTER_OBJECT_ALREADY_USED __MSABI_LONG(5936)
#define ERROR_NONCORE_GROUPS_FOUND __MSABI_LONG(5937)
#define ERROR_FILE_SHARE_RESOURCE_CONFLICT __MSABI_LONG(5938)
#define ERROR_CLUSTER_EVICT_INVALID_REQUEST __MSABI_LONG(5939)
#define ERROR_CLUSTER_SINGLETON_RESOURCE __MSABI_LONG(5940)
#define ERROR_CLUSTER_GROUP_SINGLETON_RESOURCE __MSABI_LONG(5941)
#define ERROR_CLUSTER_RESOURCE_PROVIDER_FAILED __MSABI_LONG(5942)
#define ERROR_CLUSTER_RESOURCE_CONFIGURATION_ERROR __MSABI_LONG(5943)
#define ERROR_CLUSTER_GROUP_BUSY __MSABI_LONG(5944)
#define ERROR_CLUSTER_NOT_SHARED_VOLUME __MSABI_LONG(5945)
#define ERROR_CLUSTER_INVALID_SECURITY_DESCRIPTOR __MSABI_LONG(5946)
#define ERROR_CLUSTER_SHARED_VOLUMES_IN_USE __MSABI_LONG(5947)
#define ERROR_CLUSTER_USE_SHARED_VOLUMES_API __MSABI_LONG(5948)
#define ERROR_CLUSTER_BACKUP_IN_PROGRESS __MSABI_LONG(5949)
#define ERROR_NON_CSV_PATH __MSABI_LONG(5950)
#define ERROR_CSV_VOLUME_NOT_LOCAL __MSABI_LONG(5951)
#define ERROR_CLUSTER_WATCHDOG_TERMINATING __MSABI_LONG(5952)
#define ERROR_CLUSTER_RESOURCE_VETOED_MOVE_INCOMPATIBLE_NODES __MSABI_LONG(5953)
#define ERROR_CLUSTER_INVALID_NODE_WEIGHT __MSABI_LONG(5954)
#define ERROR_CLUSTER_RESOURCE_VETOED_CALL __MSABI_LONG(5955)
#define ERROR_RESMON_SYSTEM_RESOURCES_LACKING __MSABI_LONG(5956)
#define ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_DESTINATION __MSABI_LONG(5957)
#define ERROR_CLUSTER_RESOURCE_VETOED_MOVE_NOT_ENOUGH_RESOURCES_ON_SOURCE __MSABI_LONG(5958)
#define ERROR_CLUSTER_GROUP_QUEUED __MSABI_LONG(5959)
#define ERROR_CLUSTER_RESOURCE_LOCKED_STATUS __MSABI_LONG(5960)
#define ERROR_CLUSTER_SHARED_VOLUME_FAILOVER_NOT_ALLOWED __MSABI_LONG(5961)
#define ERROR_CLUSTER_NODE_DRAIN_IN_PROGRESS __MSABI_LONG(5962)
#define ERROR_CLUSTER_DISK_NOT_CONNECTED __MSABI_LONG(5963)
#define ERROR_DISK_NOT_CSV_CAPABLE __MSABI_LONG(5964)
#define ERROR_RESOURCE_NOT_IN_AVAILABLE_STORAGE __MSABI_LONG(5965)
#define ERROR_CLUSTER_SHARED_VOLUME_REDIRECTED __MSABI_LONG(5966)
#define ERROR_CLUSTER_SHARED_VOLUME_NOT_REDIRECTED __MSABI_LONG(5967)
#define ERROR_CLUSTER_CANNOT_RETURN_PROPERTIES __MSABI_LONG(5968)
#define ERROR_CLUSTER_RESOURCE_CONTAINS_UNSUPPORTED_DIFF_AREA_FOR_SHARED_VOLUMES __MSABI_LONG(5969)
#define ERROR_CLUSTER_RESOURCE_IS_IN_MAINTENANCE_MODE __MSABI_LONG(5970)
#define ERROR_CLUSTER_AFFINITY_CONFLICT __MSABI_LONG(5971)
#define ERROR_CLUSTER_RESOURCE_IS_REPLICA_VIRTUAL_MACHINE __MSABI_LONG(5972)
#define ERROR_CLUSTER_UPGRADE_INCOMPATIBLE_VERSIONS __MSABI_LONG(5973)
#define ERROR_CLUSTER_UPGRADE_FIX_QUORUM_NOT_SUPPORTED __MSABI_LONG(5974)
#define ERROR_CLUSTER_UPGRADE_RESTART_REQUIRED __MSABI_LONG(5975)
#define ERROR_CLUSTER_UPGRADE_IN_PROGRESS __MSABI_LONG(5976)
#define ERROR_CLUSTER_UPGRADE_INCOMPLETE __MSABI_LONG(5977)
#define ERROR_CLUSTER_NODE_IN_GRACE_PERIOD __MSABI_LONG(5978)
#define ERROR_CLUSTER_CSV_IO_PAUSE_TIMEOUT __MSABI_LONG(5979)
#define ERROR_NODE_NOT_ACTIVE_CLUSTER_MEMBER __MSABI_LONG(5980)
#define ERROR_CLUSTER_RESOURCE_NOT_MONITORED __MSABI_LONG(5981)
#define ERROR_CLUSTER_RESOURCE_DOES_NOT_SUPPORT_UNMONITORED __MSABI_LONG(5982)
#define ERROR_CLUSTER_RESOURCE_IS_REPLICATED __MSABI_LONG(5983)
#define ERROR_CLUSTER_NODE_ISOLATED __MSABI_LONG(5984)
#define ERROR_CLUSTER_NODE_QUARANTINED __MSABI_LONG(5985)
#define ERROR_CLUSTER_DATABASE_UPDATE_CONDITION_FAILED __MSABI_LONG(5986)
#define ERROR_CLUSTER_SPACE_DEGRADED __MSABI_LONG(5987)
#define ERROR_CLUSTER_TOKEN_DELEGATION_NOT_SUPPORTED __MSABI_LONG(5988)
#define ERROR_CLUSTER_CSV_INVALID_HANDLE __MSABI_LONG(5989)
#define ERROR_CLUSTER_CSV_SUPPORTED_ONLY_ON_COORDINATOR __MSABI_LONG(5990)
#define ERROR_GROUPSET_NOT_AVAILABLE __MSABI_LONG(5991)
#define ERROR_GROUPSET_NOT_FOUND __MSABI_LONG(5992)
#define ERROR_GROUPSET_CANT_PROVIDE __MSABI_LONG(5993)
#define ERROR_CLUSTER_FAULT_DOMAIN_PARENT_NOT_FOUND __MSABI_LONG(5994)
#define ERROR_CLUSTER_FAULT_DOMAIN_INVALID_HIERARCHY __MSABI_LONG(5995)
#define ERROR_CLUSTER_FAULT_DOMAIN_FAILED_S2D_VALIDATION __MSABI_LONG(5996)
#define ERROR_CLUSTER_FAULT_DOMAIN_S2D_CONNECTIVITY_LOSS __MSABI_LONG(5997)
#define ERROR_CLUSTER_INVALID_INFRASTRUCTURE_FILESERVER_NAME __MSABI_LONG(5998)
#define ERROR_CLUSTERSET_MANAGEMENT_CLUSTER_UNREACHABLE __MSABI_LONG(5999)
#define ERROR_ENCRYPTION_FAILED __MSABI_LONG(6000)
#define ERROR_DECRYPTION_FAILED __MSABI_LONG(6001)
#define ERROR_FILE_ENCRYPTED __MSABI_LONG(6002)
#define ERROR_NO_RECOVERY_POLICY __MSABI_LONG(6003)
#define ERROR_NO_EFS __MSABI_LONG(6004)
#define ERROR_WRONG_EFS __MSABI_LONG(6005)
#define ERROR_NO_USER_KEYS __MSABI_LONG(6006)
#define ERROR_FILE_NOT_ENCRYPTED __MSABI_LONG(6007)
#define ERROR_NOT_EXPORT_FORMAT __MSABI_LONG(6008)
#define ERROR_FILE_READ_ONLY __MSABI_LONG(6009)
#define ERROR_DIR_EFS_DISALLOWED __MSABI_LONG(6010)
#define ERROR_EFS_SERVER_NOT_TRUSTED __MSABI_LONG(6011)
#define ERROR_BAD_RECOVERY_POLICY __MSABI_LONG(6012)
#define ERROR_EFS_ALG_BLOB_TOO_BIG __MSABI_LONG(6013)
#define ERROR_VOLUME_NOT_SUPPORT_EFS __MSABI_LONG(6014)
#define ERROR_EFS_DISABLED __MSABI_LONG(6015)
#define ERROR_EFS_VERSION_NOT_SUPPORT __MSABI_LONG(6016)
#define ERROR_CS_ENCRYPTION_INVALID_SERVER_RESPONSE __MSABI_LONG(6017)
#define ERROR_CS_ENCRYPTION_UNSUPPORTED_SERVER __MSABI_LONG(6018)
#define ERROR_CS_ENCRYPTION_EXISTING_ENCRYPTED_FILE __MSABI_LONG(6019)
#define ERROR_CS_ENCRYPTION_NEW_ENCRYPTED_FILE __MSABI_LONG(6020)
#define ERROR_CS_ENCRYPTION_FILE_NOT_CSE __MSABI_LONG(6021)
#define ERROR_ENCRYPTION_POLICY_DENIES_OPERATION __MSABI_LONG(6022)
#define ERROR_WIP_ENCRYPTION_FAILED __MSABI_LONG(6023)
#define ERROR_PDE_ENCRYPTION_UNAVAILABLE_FAILURE __MSABI_LONG(6024)
#define ERROR_PDE_DECRYPTION_UNAVAILABLE_FAILURE __MSABI_LONG(6025)
#define ERROR_PDE_DECRYPTION_UNAVAILABLE __MSABI_LONG(6026)
#define ERROR_NO_BROWSER_SERVERS_FOUND __MSABI_LONG(6118)
#define SCHED_E_SERVICE_NOT_LOCALSYSTEM __MSABI_LONG(6200)
#define ERROR_CLUSTER_OBJECT_IS_CLUSTER_SET_VM __MSABI_LONG(6250)
#define ERROR_CNU_TEMPLATE_ALREADY_EXISTS __MSABI_LONG(6251)
#define ERROR_CNU_TEMPLATE_NAME_NOT_FOUND __MSABI_LONG(6252)
#define ERROR_CNU_RUN_NAME_NOT_FOUND __MSABI_LONG(6253)
#define ERROR_CNU_RUN_ALREADY_IN_PROGRESS __MSABI_LONG(6254)
#define ERROR_CNU_RUN_NOT_IN_PROGRESS __MSABI_LONG(6255)
#define ERROR_CNU_NOT_READY __MSABI_LONG(6256)
#define ERROR_LOG_SECTOR_INVALID __MSABI_LONG(6600)
#define ERROR_LOG_SECTOR_PARITY_INVALID __MSABI_LONG(6601)
#define ERROR_LOG_SECTOR_REMAPPED __MSABI_LONG(6602)
#define ERROR_LOG_BLOCK_INCOMPLETE __MSABI_LONG(6603)
#define ERROR_LOG_INVALID_RANGE __MSABI_LONG(6604)
#define ERROR_LOG_BLOCKS_EXHAUSTED __MSABI_LONG(6605)
#define ERROR_LOG_READ_CONTEXT_INVALID __MSABI_LONG(6606)
#define ERROR_LOG_RESTART_INVALID __MSABI_LONG(6607)
#define ERROR_LOG_BLOCK_VERSION __MSABI_LONG(6608)
#define ERROR_LOG_BLOCK_INVALID __MSABI_LONG(6609)
#define ERROR_LOG_READ_MODE_INVALID __MSABI_LONG(6610)
#define ERROR_LOG_NO_RESTART __MSABI_LONG(6611)
#define ERROR_LOG_METADATA_CORRUPT __MSABI_LONG(6612)
#define ERROR_LOG_METADATA_INVALID __MSABI_LONG(6613)
#define ERROR_LOG_METADATA_INCONSISTENT __MSABI_LONG(6614)
#define ERROR_LOG_RESERVATION_INVALID __MSABI_LONG(6615)
#define ERROR_LOG_CANT_DELETE __MSABI_LONG(6616)
#define ERROR_LOG_CONTAINER_LIMIT_EXCEEDED __MSABI_LONG(6617)
#define ERROR_LOG_START_OF_LOG __MSABI_LONG(6618)
#define ERROR_LOG_POLICY_ALREADY_INSTALLED __MSABI_LONG(6619)
#define ERROR_LOG_POLICY_NOT_INSTALLED __MSABI_LONG(6620)
#define ERROR_LOG_POLICY_INVALID __MSABI_LONG(6621)
#define ERROR_LOG_POLICY_CONFLICT __MSABI_LONG(6622)
#define ERROR_LOG_PINNED_ARCHIVE_TAIL __MSABI_LONG(6623)
#define ERROR_LOG_RECORD_NONEXISTENT __MSABI_LONG(6624)
#define ERROR_LOG_RECORDS_RESERVED_INVALID __MSABI_LONG(6625)
#define ERROR_LOG_SPACE_RESERVED_INVALID __MSABI_LONG(6626)
#define ERROR_LOG_TAIL_INVALID __MSABI_LONG(6627)
#define ERROR_LOG_FULL __MSABI_LONG(6628)
#define ERROR_COULD_NOT_RESIZE_LOG __MSABI_LONG(6629)
#define ERROR_LOG_MULTIPLEXED __MSABI_LONG(6630)
#define ERROR_LOG_DEDICATED __MSABI_LONG(6631)
#define ERROR_LOG_ARCHIVE_NOT_IN_PROGRESS __MSABI_LONG(6632)
#define ERROR_LOG_ARCHIVE_IN_PROGRESS __MSABI_LONG(6633)
#define ERROR_LOG_EPHEMERAL __MSABI_LONG(6634)
#define ERROR_LOG_NOT_ENOUGH_CONTAINERS __MSABI_LONG(6635)
#define ERROR_LOG_CLIENT_ALREADY_REGISTERED __MSABI_LONG(6636)
#define ERROR_LOG_CLIENT_NOT_REGISTERED __MSABI_LONG(6637)
#define ERROR_LOG_FULL_HANDLER_IN_PROGRESS __MSABI_LONG(6638)
#define ERROR_LOG_CONTAINER_READ_FAILED __MSABI_LONG(6639)
#define ERROR_LOG_CONTAINER_WRITE_FAILED __MSABI_LONG(6640)
#define ERROR_LOG_CONTAINER_OPEN_FAILED __MSABI_LONG(6641)
#define ERROR_LOG_CONTAINER_STATE_INVALID __MSABI_LONG(6642)
#define ERROR_LOG_STATE_INVALID __MSABI_LONG(6643)
#define ERROR_LOG_PINNED __MSABI_LONG(6644)
#define ERROR_LOG_METADATA_FLUSH_FAILED __MSABI_LONG(6645)
#define ERROR_LOG_INCONSISTENT_SECURITY __MSABI_LONG(6646)
#define ERROR_LOG_APPENDED_FLUSH_FAILED __MSABI_LONG(6647)
#define ERROR_LOG_PINNED_RESERVATION __MSABI_LONG(6648)
#define ERROR_INVALID_TRANSACTION __MSABI_LONG(6700)
#define ERROR_TRANSACTION_NOT_ACTIVE __MSABI_LONG(6701)
#define ERROR_TRANSACTION_REQUEST_NOT_VALID __MSABI_LONG(6702)
#define ERROR_TRANSACTION_NOT_REQUESTED __MSABI_LONG(6703)
#define ERROR_TRANSACTION_ALREADY_ABORTED __MSABI_LONG(6704)
#define ERROR_TRANSACTION_ALREADY_COMMITTED __MSABI_LONG(6705)
#define ERROR_TM_INITIALIZATION_FAILED __MSABI_LONG(6706)
#define ERROR_RESOURCEMANAGER_READ_ONLY __MSABI_LONG(6707)
#define ERROR_TRANSACTION_NOT_JOINED __MSABI_LONG(6708)
#define ERROR_TRANSACTION_SUPERIOR_EXISTS __MSABI_LONG(6709)
#define ERROR_CRM_PROTOCOL_ALREADY_EXISTS __MSABI_LONG(6710)
#define ERROR_TRANSACTION_PROPAGATION_FAILED __MSABI_LONG(6711)
#define ERROR_CRM_PROTOCOL_NOT_FOUND __MSABI_LONG(6712)
#define ERROR_TRANSACTION_INVALID_MARSHALL_BUFFER __MSABI_LONG(6713)
#define ERROR_CURRENT_TRANSACTION_NOT_VALID __MSABI_LONG(6714)
#define ERROR_TRANSACTION_NOT_FOUND __MSABI_LONG(6715)
#define ERROR_RESOURCEMANAGER_NOT_FOUND __MSABI_LONG(6716)
#define ERROR_ENLISTMENT_NOT_FOUND __MSABI_LONG(6717)
#define ERROR_TRANSACTIONMANAGER_NOT_FOUND __MSABI_LONG(6718)
#define ERROR_TRANSACTIONMANAGER_NOT_ONLINE __MSABI_LONG(6719)
#define ERROR_TRANSACTIONMANAGER_RECOVERY_NAME_COLLISION __MSABI_LONG(6720)
#define ERROR_TRANSACTION_NOT_ROOT __MSABI_LONG(6721)
#define ERROR_TRANSACTION_OBJECT_EXPIRED __MSABI_LONG(6722)
#define ERROR_TRANSACTION_RESPONSE_NOT_ENLISTED __MSABI_LONG(6723)
#define ERROR_TRANSACTION_RECORD_TOO_LONG __MSABI_LONG(6724)
#define ERROR_IMPLICIT_TRANSACTION_NOT_SUPPORTED __MSABI_LONG(6725)
#define ERROR_TRANSACTION_INTEGRITY_VIOLATED __MSABI_LONG(6726)
#define ERROR_TRANSACTIONMANAGER_IDENTITY_MISMATCH __MSABI_LONG(6727)
#define ERROR_RM_CANNOT_BE_FROZEN_FOR_SNAPSHOT __MSABI_LONG(6728)
#define ERROR_TRANSACTION_MUST_WRITETHROUGH __MSABI_LONG(6729)
#define ERROR_TRANSACTION_NO_SUPERIOR __MSABI_LONG(6730)
#define ERROR_HEURISTIC_DAMAGE_POSSIBLE __MSABI_LONG(6731)
#define ERROR_TRANSACTIONAL_CONFLICT __MSABI_LONG(6800)
#define ERROR_RM_NOT_ACTIVE __MSABI_LONG(6801)
#define ERROR_RM_METADATA_CORRUPT __MSABI_LONG(6802)
#define ERROR_DIRECTORY_NOT_RM __MSABI_LONG(6803)
#define ERROR_TRANSACTIONS_UNSUPPORTED_REMOTE __MSABI_LONG(6805)
#define ERROR_LOG_RESIZE_INVALID_SIZE __MSABI_LONG(6806)
#define ERROR_OBJECT_NO_LONGER_EXISTS __MSABI_LONG(6807)
#define ERROR_STREAM_MINIVERSION_NOT_FOUND __MSABI_LONG(6808)
#define ERROR_STREAM_MINIVERSION_NOT_VALID __MSABI_LONG(6809)
#define ERROR_MINIVERSION_INACCESSIBLE_FROM_SPECIFIED_TRANSACTION __MSABI_LONG(6810)
#define ERROR_CANT_OPEN_MINIVERSION_WITH_MODIFY_INTENT __MSABI_LONG(6811)
#define ERROR_CANT_CREATE_MORE_STREAM_MINIVERSIONS __MSABI_LONG(6812)
#define ERROR_REMOTE_FILE_VERSION_MISMATCH __MSABI_LONG(6814)
#define ERROR_HANDLE_NO_LONGER_VALID __MSABI_LONG(6815)
#define ERROR_NO_TXF_METADATA __MSABI_LONG(6816)
#define ERROR_LOG_CORRUPTION_DETECTED __MSABI_LONG(6817)
#define ERROR_CANT_RECOVER_WITH_HANDLE_OPEN __MSABI_LONG(6818)
#define ERROR_RM_DISCONNECTED __MSABI_LONG(6819)
#define ERROR_ENLISTMENT_NOT_SUPERIOR __MSABI_LONG(6820)
#define ERROR_RECOVERY_NOT_NEEDED __MSABI_LONG(6821)
#define ERROR_RM_ALREADY_STARTED __MSABI_LONG(6822)
#define ERROR_FILE_IDENTITY_NOT_PERSISTENT __MSABI_LONG(6823)
#define ERROR_CANT_BREAK_TRANSACTIONAL_DEPENDENCY __MSABI_LONG(6824)
#define ERROR_CANT_CROSS_RM_BOUNDARY __MSABI_LONG(6825)
#define ERROR_TXF_DIR_NOT_EMPTY __MSABI_LONG(6826)
#define ERROR_INDOUBT_TRANSACTIONS_EXIST __MSABI_LONG(6827)
#define ERROR_TM_VOLATILE __MSABI_LONG(6828)
#define ERROR_ROLLBACK_TIMER_EXPIRED __MSABI_LONG(6829)
#define ERROR_TXF_ATTRIBUTE_CORRUPT __MSABI_LONG(6830)
#define ERROR_EFS_NOT_ALLOWED_IN_TRANSACTION __MSABI_LONG(6831)
#define ERROR_TRANSACTIONAL_OPEN_NOT_ALLOWED __MSABI_LONG(6832)
#define ERROR_LOG_GROWTH_FAILED __MSABI_LONG(6833)
#define ERROR_TRANSACTED_MAPPING_UNSUPPORTED_REMOTE __MSABI_LONG(6834)
#define ERROR_TXF_METADATA_ALREADY_PRESENT __MSABI_LONG(6835)
#define ERROR_TRANSACTION_SCOPE_CALLBACKS_NOT_SET __MSABI_LONG(6836)
#define ERROR_TRANSACTION_REQUIRED_PROMOTION __MSABI_LONG(6837)
#define ERROR_CANNOT_EXECUTE_FILE_IN_TRANSACTION __MSABI_LONG(6838)
#define ERROR_TRANSACTIONS_NOT_FROZEN __MSABI_LONG(6839)
#define ERROR_TRANSACTION_FREEZE_IN_PROGRESS __MSABI_LONG(6840)
#define ERROR_NOT_SNAPSHOT_VOLUME __MSABI_LONG(6841)
#define ERROR_NO_SAVEPOINT_WITH_OPEN_FILES __MSABI_LONG(6842)
#define ERROR_DATA_LOST_REPAIR __MSABI_LONG(6843)
#define ERROR_SPARSE_NOT_ALLOWED_IN_TRANSACTION __MSABI_LONG(6844)
#define ERROR_TM_IDENTITY_MISMATCH __MSABI_LONG(6845)
#define ERROR_FLOATED_SECTION __MSABI_LONG(6846)
#define ERROR_CANNOT_ACCEPT_TRANSACTED_WORK __MSABI_LONG(6847)
#define ERROR_CANNOT_ABORT_TRANSACTIONS __MSABI_LONG(6848)
#define ERROR_BAD_CLUSTERS __MSABI_LONG(6849)
#define ERROR_COMPRESSION_NOT_ALLOWED_IN_TRANSACTION __MSABI_LONG(6850)
#define ERROR_VOLUME_DIRTY __MSABI_LONG(6851)
#define ERROR_NO_LINK_TRACKING_IN_TRANSACTION __MSABI_LONG(6852)
#define ERROR_OPERATION_NOT_SUPPORTED_IN_TRANSACTION __MSABI_LONG(6853)
#define ERROR_EXPIRED_HANDLE __MSABI_LONG(6854)
#define ERROR_TRANSACTION_NOT_ENLISTED __MSABI_LONG(6855)
#define ERROR_CTX_WINSTATION_NAME_INVALID __MSABI_LONG(7001)
#define ERROR_CTX_INVALID_PD __MSABI_LONG(7002)
#define ERROR_CTX_PD_NOT_FOUND __MSABI_LONG(7003)
#define ERROR_CTX_WD_NOT_FOUND __MSABI_LONG(7004)
#define ERROR_CTX_CANNOT_MAKE_EVENTLOG_ENTRY __MSABI_LONG(7005)
#define ERROR_CTX_SERVICE_NAME_COLLISION __MSABI_LONG(7006)
#define ERROR_CTX_CLOSE_PENDING __MSABI_LONG(7007)
#define ERROR_CTX_NO_OUTBUF __MSABI_LONG(7008)
#define ERROR_CTX_MODEM_INF_NOT_FOUND __MSABI_LONG(7009)
#define ERROR_CTX_INVALID_MODEMNAME __MSABI_LONG(7010)
#define ERROR_CTX_MODEM_RESPONSE_ERROR __MSABI_LONG(7011)
#define ERROR_CTX_MODEM_RESPONSE_TIMEOUT __MSABI_LONG(7012)
#define ERROR_CTX_MODEM_RESPONSE_NO_CARRIER __MSABI_LONG(7013)
#define ERROR_CTX_MODEM_RESPONSE_NO_DIALTONE __MSABI_LONG(7014)
#define ERROR_CTX_MODEM_RESPONSE_BUSY __MSABI_LONG(7015)
#define ERROR_CTX_MODEM_RESPONSE_VOICE __MSABI_LONG(7016)
#define ERROR_CTX_TD_ERROR __MSABI_LONG(7017)
#define ERROR_CTX_WINSTATION_NOT_FOUND __MSABI_LONG(7022)
#define ERROR_CTX_WINSTATION_ALREADY_EXISTS __MSABI_LONG(7023)
#define ERROR_CTX_WINSTATION_BUSY __MSABI_LONG(7024)
#define ERROR_CTX_BAD_VIDEO_MODE __MSABI_LONG(7025)
#define ERROR_CTX_GRAPHICS_INVALID __MSABI_LONG(7035)
#define ERROR_CTX_LOGON_DISABLED __MSABI_LONG(7037)
#define ERROR_CTX_NOT_CONSOLE __MSABI_LONG(7038)
#define ERROR_CTX_CLIENT_QUERY_TIMEOUT __MSABI_LONG(7040)
#define ERROR_CTX_CONSOLE_DISCONNECT __MSABI_LONG(7041)
#define ERROR_CTX_CONSOLE_CONNECT __MSABI_LONG(7042)
#define ERROR_CTX_SHADOW_DENIED __MSABI_LONG(7044)
#define ERROR_CTX_WINSTATION_ACCESS_DENIED __MSABI_LONG(7045)
#define ERROR_CTX_INVALID_WD __MSABI_LONG(7049)
#define ERROR_CTX_SHADOW_INVALID __MSABI_LONG(7050)
#define ERROR_CTX_SHADOW_DISABLED __MSABI_LONG(7051)
#define ERROR_CTX_CLIENT_LICENSE_IN_USE __MSABI_LONG(7052)
#define ERROR_CTX_CLIENT_LICENSE_NOT_SET __MSABI_LONG(7053)
#define ERROR_CTX_LICENSE_NOT_AVAILABLE __MSABI_LONG(7054)
#define ERROR_CTX_LICENSE_CLIENT_INVALID __MSABI_LONG(7055)
#define ERROR_CTX_LICENSE_EXPIRED __MSABI_LONG(7056)
#define ERROR_CTX_SHADOW_NOT_RUNNING __MSABI_LONG(7057)
#define ERROR_CTX_SHADOW_ENDED_BY_MODE_CHANGE __MSABI_LONG(7058)
#define ERROR_ACTIVATION_COUNT_EXCEEDED __MSABI_LONG(7059)
#define ERROR_CTX_WINSTATIONS_DISABLED __MSABI_LONG(7060)
#define ERROR_CTX_ENCRYPTION_LEVEL_REQUIRED __MSABI_LONG(7061)
#define ERROR_CTX_SESSION_IN_USE __MSABI_LONG(7062)
#define ERROR_CTX_NO_FORCE_LOGOFF __MSABI_LONG(7063)
#define ERROR_CTX_ACCOUNT_RESTRICTION __MSABI_LONG(7064)
#define ERROR_RDP_PROTOCOL_ERROR __MSABI_LONG(7065)
#define ERROR_CTX_CDM_CONNECT __MSABI_LONG(7066)
#define ERROR_CTX_CDM_DISCONNECT __MSABI_LONG(7067)
#define ERROR_CTX_SECURITY_LAYER_ERROR __MSABI_LONG(7068)
#define ERROR_TS_INCOMPATIBLE_SESSIONS __MSABI_LONG(7069)
#define ERROR_TS_VIDEO_SUBSYSTEM_ERROR __MSABI_LONG(7070)
#define FRS_ERR_INVALID_API_SEQUENCE __MSABI_LONG(8001)
#define FRS_ERR_STARTING_SERVICE __MSABI_LONG(8002)
#define FRS_ERR_STOPPING_SERVICE __MSABI_LONG(8003)
#define FRS_ERR_INTERNAL_API __MSABI_LONG(8004)
#define FRS_ERR_INTERNAL __MSABI_LONG(8005)
#define FRS_ERR_SERVICE_COMM __MSABI_LONG(8006)
#define FRS_ERR_INSUFFICIENT_PRIV __MSABI_LONG(8007)
#define FRS_ERR_AUTHENTICATION __MSABI_LONG(8008)
#define FRS_ERR_PARENT_INSUFFICIENT_PRIV __MSABI_LONG(8009)
#define FRS_ERR_PARENT_AUTHENTICATION __MSABI_LONG(8010)
#define FRS_ERR_CHILD_TO_PARENT_COMM __MSABI_LONG(8011)
#define FRS_ERR_PARENT_TO_CHILD_COMM __MSABI_LONG(8012)
#define FRS_ERR_SYSVOL_POPULATE __MSABI_LONG(8013)
#define FRS_ERR_SYSVOL_POPULATE_TIMEOUT __MSABI_LONG(8014)
#define FRS_ERR_SYSVOL_IS_BUSY __MSABI_LONG(8015)
#define FRS_ERR_SYSVOL_DEMOTE __MSABI_LONG(8016)
#define FRS_ERR_INVALID_SERVICE_PARAMETER __MSABI_LONG(8017)
#define DS_S_SUCCESS NO_ERROR
#define ERROR_DS_NOT_INSTALLED __MSABI_LONG(8200)
#define ERROR_DS_MEMBERSHIP_EVALUATED_LOCALLY __MSABI_LONG(8201)
#define ERROR_DS_NO_ATTRIBUTE_OR_VALUE __MSABI_LONG(8202)
#define ERROR_DS_INVALID_ATTRIBUTE_SYNTAX __MSABI_LONG(8203)
#define ERROR_DS_ATTRIBUTE_TYPE_UNDEFINED __MSABI_LONG(8204)
#define ERROR_DS_ATTRIBUTE_OR_VALUE_EXISTS __MSABI_LONG(8205)
#define ERROR_DS_BUSY __MSABI_LONG(8206)
#define ERROR_DS_UNAVAILABLE __MSABI_LONG(8207)
#define ERROR_DS_NO_RIDS_ALLOCATED __MSABI_LONG(8208)
#define ERROR_DS_NO_MORE_RIDS __MSABI_LONG(8209)
#define ERROR_DS_INCORRECT_ROLE_OWNER __MSABI_LONG(8210)
#define ERROR_DS_RIDMGR_INIT_ERROR __MSABI_LONG(8211)
#define ERROR_DS_OBJ_CLASS_VIOLATION __MSABI_LONG(8212)
#define ERROR_DS_CANT_ON_NON_LEAF __MSABI_LONG(8213)
#define ERROR_DS_CANT_ON_RDN __MSABI_LONG(8214)
#define ERROR_DS_CANT_MOD_OBJ_CLASS __MSABI_LONG(8215)
#define ERROR_DS_CROSS_DOM_MOVE_ERROR __MSABI_LONG(8216)
#define ERROR_DS_GC_NOT_AVAILABLE __MSABI_LONG(8217)
#define ERROR_SHARED_POLICY __MSABI_LONG(8218)
#define ERROR_POLICY_OBJECT_NOT_FOUND __MSABI_LONG(8219)
#define ERROR_POLICY_ONLY_IN_DS __MSABI_LONG(8220)
#define ERROR_PROMOTION_ACTIVE __MSABI_LONG(8221)
#define ERROR_NO_PROMOTION_ACTIVE __MSABI_LONG(8222)
#define ERROR_DS_OPERATIONS_ERROR __MSABI_LONG(8224)
#define ERROR_DS_PROTOCOL_ERROR __MSABI_LONG(8225)
#define ERROR_DS_TIMELIMIT_EXCEEDED __MSABI_LONG(8226)
#define ERROR_DS_SIZELIMIT_EXCEEDED __MSABI_LONG(8227)
#define ERROR_DS_ADMIN_LIMIT_EXCEEDED __MSABI_LONG(8228)
#define ERROR_DS_COMPARE_FALSE __MSABI_LONG(8229)
#define ERROR_DS_COMPARE_TRUE __MSABI_LONG(8230)
#define ERROR_DS_AUTH_METHOD_NOT_SUPPORTED __MSABI_LONG(8231)
#define ERROR_DS_STRONG_AUTH_REQUIRED __MSABI_LONG(8232)
#define ERROR_DS_INAPPROPRIATE_AUTH __MSABI_LONG(8233)
#define ERROR_DS_AUTH_UNKNOWN __MSABI_LONG(8234)
#define ERROR_DS_REFERRAL __MSABI_LONG(8235)
#define ERROR_DS_UNAVAILABLE_CRIT_EXTENSION __MSABI_LONG(8236)
#define ERROR_DS_CONFIDENTIALITY_REQUIRED __MSABI_LONG(8237)
#define ERROR_DS_INAPPROPRIATE_MATCHING __MSABI_LONG(8238)
#define ERROR_DS_CONSTRAINT_VIOLATION __MSABI_LONG(8239)
#define ERROR_DS_NO_SUCH_OBJECT __MSABI_LONG(8240)
#define ERROR_DS_ALIAS_PROBLEM __MSABI_LONG(8241)
#define ERROR_DS_INVALID_DN_SYNTAX __MSABI_LONG(8242)
#define ERROR_DS_IS_LEAF __MSABI_LONG(8243)
#define ERROR_DS_ALIAS_DEREF_PROBLEM __MSABI_LONG(8244)
#define ERROR_DS_UNWILLING_TO_PERFORM __MSABI_LONG(8245)
#define ERROR_DS_LOOP_DETECT __MSABI_LONG(8246)
#define ERROR_DS_NAMING_VIOLATION __MSABI_LONG(8247)
#define ERROR_DS_OBJECT_RESULTS_TOO_LARGE __MSABI_LONG(8248)
#define ERROR_DS_AFFECTS_MULTIPLE_DSAS __MSABI_LONG(8249)
#define ERROR_DS_SERVER_DOWN __MSABI_LONG(8250)
#define ERROR_DS_LOCAL_ERROR __MSABI_LONG(8251)
#define ERROR_DS_ENCODING_ERROR __MSABI_LONG(8252)
#define ERROR_DS_DECODING_ERROR __MSABI_LONG(8253)
#define ERROR_DS_FILTER_UNKNOWN __MSABI_LONG(8254)
#define ERROR_DS_PARAM_ERROR __MSABI_LONG(8255)
#define ERROR_DS_NOT_SUPPORTED __MSABI_LONG(8256)
#define ERROR_DS_NO_RESULTS_RETURNED __MSABI_LONG(8257)
#define ERROR_DS_CONTROL_NOT_FOUND __MSABI_LONG(8258)
#define ERROR_DS_CLIENT_LOOP __MSABI_LONG(8259)
#define ERROR_DS_REFERRAL_LIMIT_EXCEEDED __MSABI_LONG(8260)
#define ERROR_DS_SORT_CONTROL_MISSING __MSABI_LONG(8261)
#define ERROR_DS_OFFSET_RANGE_ERROR __MSABI_LONG(8262)
#define ERROR_DS_RIDMGR_DISABLED __MSABI_LONG(8263)
#define ERROR_DS_ROOT_MUST_BE_NC __MSABI_LONG(8301)
#define ERROR_DS_ADD_REPLICA_INHIBITED __MSABI_LONG(8302)
#define ERROR_DS_ATT_NOT_DEF_IN_SCHEMA __MSABI_LONG(8303)
#define ERROR_DS_MAX_OBJ_SIZE_EXCEEDED __MSABI_LONG(8304)
#define ERROR_DS_OBJ_STRING_NAME_EXISTS __MSABI_LONG(8305)
#define ERROR_DS_NO_RDN_DEFINED_IN_SCHEMA __MSABI_LONG(8306)
#define ERROR_DS_RDN_DOESNT_MATCH_SCHEMA __MSABI_LONG(8307)
#define ERROR_DS_NO_REQUESTED_ATTS_FOUND __MSABI_LONG(8308)
#define ERROR_DS_USER_BUFFER_TO_SMALL __MSABI_LONG(8309)
#define ERROR_DS_ATT_IS_NOT_ON_OBJ __MSABI_LONG(8310)
#define ERROR_DS_ILLEGAL_MOD_OPERATION __MSABI_LONG(8311)
#define ERROR_DS_OBJ_TOO_LARGE __MSABI_LONG(8312)
#define ERROR_DS_BAD_INSTANCE_TYPE __MSABI_LONG(8313)
#define ERROR_DS_MASTERDSA_REQUIRED __MSABI_LONG(8314)
#define ERROR_DS_OBJECT_CLASS_REQUIRED __MSABI_LONG(8315)
#define ERROR_DS_MISSING_REQUIRED_ATT __MSABI_LONG(8316)
#define ERROR_DS_ATT_NOT_DEF_FOR_CLASS __MSABI_LONG(8317)
#define ERROR_DS_ATT_ALREADY_EXISTS __MSABI_LONG(8318)
#define ERROR_DS_CANT_ADD_ATT_VALUES __MSABI_LONG(8320)
#define ERROR_DS_SINGLE_VALUE_CONSTRAINT __MSABI_LONG(8321)
#define ERROR_DS_RANGE_CONSTRAINT __MSABI_LONG(8322)
#define ERROR_DS_ATT_VAL_ALREADY_EXISTS __MSABI_LONG(8323)
#define ERROR_DS_CANT_REM_MISSING_ATT __MSABI_LONG(8324)
#define ERROR_DS_CANT_REM_MISSING_ATT_VAL __MSABI_LONG(8325)
#define ERROR_DS_ROOT_CANT_BE_SUBREF __MSABI_LONG(8326)
#define ERROR_DS_NO_CHAINING __MSABI_LONG(8327)
#define ERROR_DS_NO_CHAINED_EVAL __MSABI_LONG(8328)
#define ERROR_DS_NO_PARENT_OBJECT __MSABI_LONG(8329)
#define ERROR_DS_PARENT_IS_AN_ALIAS __MSABI_LONG(8330)
#define ERROR_DS_CANT_MIX_MASTER_AND_REPS __MSABI_LONG(8331)
#define ERROR_DS_CHILDREN_EXIST __MSABI_LONG(8332)
#define ERROR_DS_OBJ_NOT_FOUND __MSABI_LONG(8333)
#define ERROR_DS_ALIASED_OBJ_MISSING __MSABI_LONG(8334)
#define ERROR_DS_BAD_NAME_SYNTAX __MSABI_LONG(8335)
#define ERROR_DS_ALIAS_POINTS_TO_ALIAS __MSABI_LONG(8336)
#define ERROR_DS_CANT_DEREF_ALIAS __MSABI_LONG(8337)
#define ERROR_DS_OUT_OF_SCOPE __MSABI_LONG(8338)
#define ERROR_DS_OBJECT_BEING_REMOVED __MSABI_LONG(8339)
#define ERROR_DS_CANT_DELETE_DSA_OBJ __MSABI_LONG(8340)
#define ERROR_DS_GENERIC_ERROR __MSABI_LONG(8341)
#define ERROR_DS_DSA_MUST_BE_INT_MASTER __MSABI_LONG(8342)
#define ERROR_DS_CLASS_NOT_DSA __MSABI_LONG(8343)
#define ERROR_DS_INSUFF_ACCESS_RIGHTS __MSABI_LONG(8344)
#define ERROR_DS_ILLEGAL_SUPERIOR __MSABI_LONG(8345)
#define ERROR_DS_ATTRIBUTE_OWNED_BY_SAM __MSABI_LONG(8346)
#define ERROR_DS_NAME_TOO_MANY_PARTS __MSABI_LONG(8347)
#define ERROR_DS_NAME_TOO_LONG __MSABI_LONG(8348)
#define ERROR_DS_NAME_VALUE_TOO_LONG __MSABI_LONG(8349)
#define ERROR_DS_NAME_UNPARSEABLE __MSABI_LONG(8350)
#define ERROR_DS_NAME_TYPE_UNKNOWN __MSABI_LONG(8351)
#define ERROR_DS_NOT_AN_OBJECT __MSABI_LONG(8352)
#define ERROR_DS_SEC_DESC_TOO_SHORT __MSABI_LONG(8353)
#define ERROR_DS_SEC_DESC_INVALID __MSABI_LONG(8354)
#define ERROR_DS_NO_DELETED_NAME __MSABI_LONG(8355)
#define ERROR_DS_SUBREF_MUST_HAVE_PARENT __MSABI_LONG(8356)
#define ERROR_DS_NCNAME_MUST_BE_NC __MSABI_LONG(8357)
#define ERROR_DS_CANT_ADD_SYSTEM_ONLY __MSABI_LONG(8358)
#define ERROR_DS_CLASS_MUST_BE_CONCRETE __MSABI_LONG(8359)
#define ERROR_DS_INVALID_DMD __MSABI_LONG(8360)
#define ERROR_DS_OBJ_GUID_EXISTS __MSABI_LONG(8361)
#define ERROR_DS_NOT_ON_BACKLINK __MSABI_LONG(8362)
#define ERROR_DS_NO_CROSSREF_FOR_NC __MSABI_LONG(8363)
#define ERROR_DS_SHUTTING_DOWN __MSABI_LONG(8364)
#define ERROR_DS_UNKNOWN_OPERATION __MSABI_LONG(8365)
#define ERROR_DS_INVALID_ROLE_OWNER __MSABI_LONG(8366)
#define ERROR_DS_COULDNT_CONTACT_FSMO __MSABI_LONG(8367)
#define ERROR_DS_CROSS_NC_DN_RENAME __MSABI_LONG(8368)
#define ERROR_DS_CANT_MOD_SYSTEM_ONLY __MSABI_LONG(8369)
#define ERROR_DS_REPLICATOR_ONLY __MSABI_LONG(8370)
#define ERROR_DS_OBJ_CLASS_NOT_DEFINED __MSABI_LONG(8371)
#define ERROR_DS_OBJ_CLASS_NOT_SUBCLASS __MSABI_LONG(8372)
#define ERROR_DS_NAME_REFERENCE_INVALID __MSABI_LONG(8373)
#define ERROR_DS_CROSS_REF_EXISTS __MSABI_LONG(8374)
#define ERROR_DS_CANT_DEL_MASTER_CROSSREF __MSABI_LONG(8375)
#define ERROR_DS_SUBTREE_NOTIFY_NOT_NC_HEAD __MSABI_LONG(8376)
#define ERROR_DS_NOTIFY_FILTER_TOO_COMPLEX __MSABI_LONG(8377)
#define ERROR_DS_DUP_RDN __MSABI_LONG(8378)
#define ERROR_DS_DUP_OID __MSABI_LONG(8379)
#define ERROR_DS_DUP_MAPI_ID __MSABI_LONG(8380)
#define ERROR_DS_DUP_SCHEMA_ID_GUID __MSABI_LONG(8381)
#define ERROR_DS_DUP_LDAP_DISPLAY_NAME __MSABI_LONG(8382)
#define ERROR_DS_SEMANTIC_ATT_TEST __MSABI_LONG(8383)
#define ERROR_DS_SYNTAX_MISMATCH __MSABI_LONG(8384)
#define ERROR_DS_EXISTS_IN_MUST_HAVE __MSABI_LONG(8385)
#define ERROR_DS_EXISTS_IN_MAY_HAVE __MSABI_LONG(8386)
#define ERROR_DS_NONEXISTENT_MAY_HAVE __MSABI_LONG(8387)
#define ERROR_DS_NONEXISTENT_MUST_HAVE __MSABI_LONG(8388)
#define ERROR_DS_AUX_CLS_TEST_FAIL __MSABI_LONG(8389)
#define ERROR_DS_NONEXISTENT_POSS_SUP __MSABI_LONG(8390)
#define ERROR_DS_SUB_CLS_TEST_FAIL __MSABI_LONG(8391)
#define ERROR_DS_BAD_RDN_ATT_ID_SYNTAX __MSABI_LONG(8392)
#define ERROR_DS_EXISTS_IN_AUX_CLS __MSABI_LONG(8393)
#define ERROR_DS_EXISTS_IN_SUB_CLS __MSABI_LONG(8394)
#define ERROR_DS_EXISTS_IN_POSS_SUP __MSABI_LONG(8395)
#define ERROR_DS_RECALCSCHEMA_FAILED __MSABI_LONG(8396)
#define ERROR_DS_TREE_DELETE_NOT_FINISHED __MSABI_LONG(8397)
#define ERROR_DS_CANT_DELETE __MSABI_LONG(8398)
#define ERROR_DS_ATT_SCHEMA_REQ_ID __MSABI_LONG(8399)
#define ERROR_DS_BAD_ATT_SCHEMA_SYNTAX __MSABI_LONG(8400)
#define ERROR_DS_CANT_CACHE_ATT __MSABI_LONG(8401)
#define ERROR_DS_CANT_CACHE_CLASS __MSABI_LONG(8402)
#define ERROR_DS_CANT_REMOVE_ATT_CACHE __MSABI_LONG(8403)
#define ERROR_DS_CANT_REMOVE_CLASS_CACHE __MSABI_LONG(8404)
#define ERROR_DS_CANT_RETRIEVE_DN __MSABI_LONG(8405)
#define ERROR_DS_MISSING_SUPREF __MSABI_LONG(8406)
#define ERROR_DS_CANT_RETRIEVE_INSTANCE __MSABI_LONG(8407)
#define ERROR_DS_CODE_INCONSISTENCY __MSABI_LONG(8408)
#define ERROR_DS_DATABASE_ERROR __MSABI_LONG(8409)
#define ERROR_DS_GOVERNSID_MISSING __MSABI_LONG(8410)
#define ERROR_DS_MISSING_EXPECTED_ATT __MSABI_LONG(8411)
#define ERROR_DS_NCNAME_MISSING_CR_REF __MSABI_LONG(8412)
#define ERROR_DS_SECURITY_CHECKING_ERROR __MSABI_LONG(8413)
#define ERROR_DS_SCHEMA_NOT_LOADED __MSABI_LONG(8414)
#define ERROR_DS_SCHEMA_ALLOC_FAILED __MSABI_LONG(8415)
#define ERROR_DS_ATT_SCHEMA_REQ_SYNTAX __MSABI_LONG(8416)
#define ERROR_DS_GCVERIFY_ERROR __MSABI_LONG(8417)
#define ERROR_DS_DRA_SCHEMA_MISMATCH __MSABI_LONG(8418)
#define ERROR_DS_CANT_FIND_DSA_OBJ __MSABI_LONG(8419)
#define ERROR_DS_CANT_FIND_EXPECTED_NC __MSABI_LONG(8420)
#define ERROR_DS_CANT_FIND_NC_IN_CACHE __MSABI_LONG(8421)
#define ERROR_DS_CANT_RETRIEVE_CHILD __MSABI_LONG(8422)
#define ERROR_DS_SECURITY_ILLEGAL_MODIFY __MSABI_LONG(8423)
#define ERROR_DS_CANT_REPLACE_HIDDEN_REC __MSABI_LONG(8424)
#define ERROR_DS_BAD_HIERARCHY_FILE __MSABI_LONG(8425)
#define ERROR_DS_BUILD_HIERARCHY_TABLE_FAILED __MSABI_LONG(8426)
#define ERROR_DS_CONFIG_PARAM_MISSING __MSABI_LONG(8427)
#define ERROR_DS_COUNTING_AB_INDICES_FAILED __MSABI_LONG(8428)
#define ERROR_DS_HIERARCHY_TABLE_MALLOC_FAILED __MSABI_LONG(8429)
#define ERROR_DS_INTERNAL_FAILURE __MSABI_LONG(8430)
#define ERROR_DS_UNKNOWN_ERROR __MSABI_LONG(8431)
#define ERROR_DS_ROOT_REQUIRES_CLASS_TOP __MSABI_LONG(8432)
#define ERROR_DS_REFUSING_FSMO_ROLES __MSABI_LONG(8433)
#define ERROR_DS_MISSING_FSMO_SETTINGS __MSABI_LONG(8434)
#define ERROR_DS_UNABLE_TO_SURRENDER_ROLES __MSABI_LONG(8435)
#define ERROR_DS_DRA_GENERIC __MSABI_LONG(8436)
#define ERROR_DS_DRA_INVALID_PARAMETER __MSABI_LONG(8437)
#define ERROR_DS_DRA_BUSY __MSABI_LONG(8438)
#define ERROR_DS_DRA_BAD_DN __MSABI_LONG(8439)
#define ERROR_DS_DRA_BAD_NC __MSABI_LONG(8440)
#define ERROR_DS_DRA_DN_EXISTS __MSABI_LONG(8441)
#define ERROR_DS_DRA_INTERNAL_ERROR __MSABI_LONG(8442)
#define ERROR_DS_DRA_INCONSISTENT_DIT __MSABI_LONG(8443)
#define ERROR_DS_DRA_CONNECTION_FAILED __MSABI_LONG(8444)
#define ERROR_DS_DRA_BAD_INSTANCE_TYPE __MSABI_LONG(8445)
#define ERROR_DS_DRA_OUT_OF_MEM __MSABI_LONG(8446)
#define ERROR_DS_DRA_MAIL_PROBLEM __MSABI_LONG(8447)
#define ERROR_DS_DRA_REF_ALREADY_EXISTS __MSABI_LONG(8448)
#define ERROR_DS_DRA_REF_NOT_FOUND __MSABI_LONG(8449)
#define ERROR_DS_DRA_OBJ_IS_REP_SOURCE __MSABI_LONG(8450)
#define ERROR_DS_DRA_DB_ERROR __MSABI_LONG(8451)
#define ERROR_DS_DRA_NO_REPLICA __MSABI_LONG(8452)
#define ERROR_DS_DRA_ACCESS_DENIED __MSABI_LONG(8453)
#define ERROR_DS_DRA_NOT_SUPPORTED __MSABI_LONG(8454)
#define ERROR_DS_DRA_RPC_CANCELLED __MSABI_LONG(8455)
#define ERROR_DS_DRA_SOURCE_DISABLED __MSABI_LONG(8456)
#define ERROR_DS_DRA_SINK_DISABLED __MSABI_LONG(8457)
#define ERROR_DS_DRA_NAME_COLLISION __MSABI_LONG(8458)
#define ERROR_DS_DRA_SOURCE_REINSTALLED __MSABI_LONG(8459)
#define ERROR_DS_DRA_MISSING_PARENT __MSABI_LONG(8460)
#define ERROR_DS_DRA_PREEMPTED __MSABI_LONG(8461)
#define ERROR_DS_DRA_ABANDON_SYNC __MSABI_LONG(8462)
#define ERROR_DS_DRA_SHUTDOWN __MSABI_LONG(8463)
#define ERROR_DS_DRA_INCOMPATIBLE_PARTIAL_SET __MSABI_LONG(8464)
#define ERROR_DS_DRA_SOURCE_IS_PARTIAL_REPLICA __MSABI_LONG(8465)
#define ERROR_DS_DRA_EXTN_CONNECTION_FAILED __MSABI_LONG(8466)
#define ERROR_DS_INSTALL_SCHEMA_MISMATCH __MSABI_LONG(8467)
#define ERROR_DS_DUP_LINK_ID __MSABI_LONG(8468)
#define ERROR_DS_NAME_ERROR_RESOLVING __MSABI_LONG(8469)
#define ERROR_DS_NAME_ERROR_NOT_FOUND __MSABI_LONG(8470)
#define ERROR_DS_NAME_ERROR_NOT_UNIQUE __MSABI_LONG(8471)
#define ERROR_DS_NAME_ERROR_NO_MAPPING __MSABI_LONG(8472)
#define ERROR_DS_NAME_ERROR_DOMAIN_ONLY __MSABI_LONG(8473)
#define ERROR_DS_NAME_ERROR_NO_SYNTACTICAL_MAPPING __MSABI_LONG(8474)
#define ERROR_DS_CONSTRUCTED_ATT_MOD __MSABI_LONG(8475)
#define ERROR_DS_WRONG_OM_OBJ_CLASS __MSABI_LONG(8476)
#define ERROR_DS_DRA_REPL_PENDING __MSABI_LONG(8477)
#define ERROR_DS_DS_REQUIRED __MSABI_LONG(8478)
#define ERROR_DS_INVALID_LDAP_DISPLAY_NAME __MSABI_LONG(8479)
#define ERROR_DS_NON_BASE_SEARCH __MSABI_LONG(8480)
#define ERROR_DS_CANT_RETRIEVE_ATTS __MSABI_LONG(8481)
#define ERROR_DS_BACKLINK_WITHOUT_LINK __MSABI_LONG(8482)
#define ERROR_DS_EPOCH_MISMATCH __MSABI_LONG(8483)
#define ERROR_DS_SRC_NAME_MISMATCH __MSABI_LONG(8484)
#define ERROR_DS_SRC_AND_DST_NC_IDENTICAL __MSABI_LONG(8485)
#define ERROR_DS_DST_NC_MISMATCH __MSABI_LONG(8486)
#define ERROR_DS_NOT_AUTHORITIVE_FOR_DST_NC __MSABI_LONG(8487)
#define ERROR_DS_SRC_GUID_MISMATCH __MSABI_LONG(8488)
#define ERROR_DS_CANT_MOVE_DELETED_OBJECT __MSABI_LONG(8489)
#define ERROR_DS_PDC_OPERATION_IN_PROGRESS __MSABI_LONG(8490)
#define ERROR_DS_CROSS_DOMAIN_CLEANUP_REQD __MSABI_LONG(8491)
#define ERROR_DS_ILLEGAL_XDOM_MOVE_OPERATION __MSABI_LONG(8492)
#define ERROR_DS_CANT_WITH_ACCT_GROUP_MEMBERSHPS __MSABI_LONG(8493)
#define ERROR_DS_NC_MUST_HAVE_NC_PARENT __MSABI_LONG(8494)
#define ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE __MSABI_LONG(8495)
#define ERROR_DS_DST_DOMAIN_NOT_NATIVE __MSABI_LONG(8496)
#define ERROR_DS_MISSING_INFRASTRUCTURE_CONTAINER __MSABI_LONG(8497)
#define ERROR_DS_CANT_MOVE_ACCOUNT_GROUP __MSABI_LONG(8498)
#define ERROR_DS_CANT_MOVE_RESOURCE_GROUP __MSABI_LONG(8499)
#define ERROR_DS_INVALID_SEARCH_FLAG __MSABI_LONG(8500)
#define ERROR_DS_NO_TREE_DELETE_ABOVE_NC __MSABI_LONG(8501)
#define ERROR_DS_COULDNT_LOCK_TREE_FOR_DELETE __MSABI_LONG(8502)
#define ERROR_DS_COULDNT_IDENTIFY_OBJECTS_FOR_TREE_DELETE __MSABI_LONG(8503)
#define ERROR_DS_SAM_INIT_FAILURE __MSABI_LONG(8504)
#define ERROR_DS_SENSITIVE_GROUP_VIOLATION __MSABI_LONG(8505)
#define ERROR_DS_CANT_MOD_PRIMARYGROUPID __MSABI_LONG(8506)
#define ERROR_DS_ILLEGAL_BASE_SCHEMA_MOD __MSABI_LONG(8507)
#define ERROR_DS_NONSAFE_SCHEMA_CHANGE __MSABI_LONG(8508)
#define ERROR_DS_SCHEMA_UPDATE_DISALLOWED __MSABI_LONG(8509)
#define ERROR_DS_CANT_CREATE_UNDER_SCHEMA __MSABI_LONG(8510)
#define ERROR_DS_INSTALL_NO_SRC_SCH_VERSION __MSABI_LONG(8511)
#define ERROR_DS_INSTALL_NO_SCH_VERSION_IN_INIFILE __MSABI_LONG(8512)
#define ERROR_DS_INVALID_GROUP_TYPE __MSABI_LONG(8513)
#define ERROR_DS_NO_NEST_GLOBALGROUP_IN_MIXEDDOMAIN __MSABI_LONG(8514)
#define ERROR_DS_NO_NEST_LOCALGROUP_IN_MIXEDDOMAIN __MSABI_LONG(8515)
#define ERROR_DS_GLOBAL_CANT_HAVE_LOCAL_MEMBER __MSABI_LONG(8516)
#define ERROR_DS_GLOBAL_CANT_HAVE_UNIVERSAL_MEMBER __MSABI_LONG(8517)
#define ERROR_DS_UNIVERSAL_CANT_HAVE_LOCAL_MEMBER __MSABI_LONG(8518)
#define ERROR_DS_GLOBAL_CANT_HAVE_CROSSDOMAIN_MEMBER __MSABI_LONG(8519)
#define ERROR_DS_LOCAL_CANT_HAVE_CROSSDOMAIN_LOCAL_MEMBER __MSABI_LONG(8520)
#define ERROR_DS_HAVE_PRIMARY_MEMBERS __MSABI_LONG(8521)
#define ERROR_DS_STRING_SD_CONVERSION_FAILED __MSABI_LONG(8522)
#define ERROR_DS_NAMING_MASTER_GC __MSABI_LONG(8523)
#define ERROR_DS_DNS_LOOKUP_FAILURE __MSABI_LONG(8524)
#define ERROR_DS_COULDNT_UPDATE_SPNS __MSABI_LONG(8525)
#define ERROR_DS_CANT_RETRIEVE_SD __MSABI_LONG(8526)
#define ERROR_DS_KEY_NOT_UNIQUE __MSABI_LONG(8527)
#define ERROR_DS_WRONG_LINKED_ATT_SYNTAX __MSABI_LONG(8528)
#define ERROR_DS_SAM_NEED_BOOTKEY_PASSWORD __MSABI_LONG(8529)
#define ERROR_DS_SAM_NEED_BOOTKEY_FLOPPY __MSABI_LONG(8530)
#define ERROR_DS_CANT_START __MSABI_LONG(8531)
#define ERROR_DS_INIT_FAILURE __MSABI_LONG(8532)
#define ERROR_DS_NO_PKT_PRIVACY_ON_CONNECTION __MSABI_LONG(8533)
#define ERROR_DS_SOURCE_DOMAIN_IN_FOREST __MSABI_LONG(8534)
#define ERROR_DS_DESTINATION_DOMAIN_NOT_IN_FOREST __MSABI_LONG(8535)
#define ERROR_DS_DESTINATION_AUDITING_NOT_ENABLED __MSABI_LONG(8536)
#define ERROR_DS_CANT_FIND_DC_FOR_SRC_DOMAIN __MSABI_LONG(8537)
#define ERROR_DS_SRC_OBJ_NOT_GROUP_OR_USER __MSABI_LONG(8538)
#define ERROR_DS_SRC_SID_EXISTS_IN_FOREST __MSABI_LONG(8539)
#define ERROR_DS_SRC_AND_DST_OBJECT_CLASS_MISMATCH __MSABI_LONG(8540)
#define ERROR_SAM_INIT_FAILURE __MSABI_LONG(8541)
#define ERROR_DS_DRA_SCHEMA_INFO_SHIP __MSABI_LONG(8542)
#define ERROR_DS_DRA_SCHEMA_CONFLICT __MSABI_LONG(8543)
#define ERROR_DS_DRA_EARLIER_SCHEMA_CONFLICT __MSABI_LONG(8544)
#define ERROR_DS_DRA_OBJ_NC_MISMATCH __MSABI_LONG(8545)
#define ERROR_DS_NC_STILL_HAS_DSAS __MSABI_LONG(8546)
#define ERROR_DS_GC_REQUIRED __MSABI_LONG(8547)
#define ERROR_DS_LOCAL_MEMBER_OF_LOCAL_ONLY __MSABI_LONG(8548)
#define ERROR_DS_NO_FPO_IN_UNIVERSAL_GROUPS __MSABI_LONG(8549)
#define ERROR_DS_CANT_ADD_TO_GC __MSABI_LONG(8550)
#define ERROR_DS_NO_CHECKPOINT_WITH_PDC __MSABI_LONG(8551)
#define ERROR_DS_SOURCE_AUDITING_NOT_ENABLED __MSABI_LONG(8552)
#define ERROR_DS_CANT_CREATE_IN_NONDOMAIN_NC __MSABI_LONG(8553)
#define ERROR_DS_INVALID_NAME_FOR_SPN __MSABI_LONG(8554)
#define ERROR_DS_FILTER_USES_CONTRUCTED_ATTRS __MSABI_LONG(8555)
#define ERROR_DS_UNICODEPWD_NOT_IN_QUOTES __MSABI_LONG(8556)
#define ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED __MSABI_LONG(8557)
#define ERROR_DS_MUST_BE_RUN_ON_DST_DC __MSABI_LONG(8558)
#define ERROR_DS_SRC_DC_MUST_BE_SP4_OR_GREATER __MSABI_LONG(8559)
#define ERROR_DS_CANT_TREE_DELETE_CRITICAL_OBJ __MSABI_LONG(8560)
#define ERROR_DS_INIT_FAILURE_CONSOLE __MSABI_LONG(8561)
#define ERROR_DS_SAM_INIT_FAILURE_CONSOLE __MSABI_LONG(8562)
#define ERROR_DS_FOREST_VERSION_TOO_HIGH __MSABI_LONG(8563)
#define ERROR_DS_DOMAIN_VERSION_TOO_HIGH __MSABI_LONG(8564)
#define ERROR_DS_FOREST_VERSION_TOO_LOW __MSABI_LONG(8565)
#define ERROR_DS_DOMAIN_VERSION_TOO_LOW __MSABI_LONG(8566)
#define ERROR_DS_INCOMPATIBLE_VERSION __MSABI_LONG(8567)
#define ERROR_DS_LOW_DSA_VERSION __MSABI_LONG(8568)
#define ERROR_DS_NO_BEHAVIOR_VERSION_IN_MIXEDDOMAIN __MSABI_LONG(8569)
#define ERROR_DS_NOT_SUPPORTED_SORT_ORDER __MSABI_LONG(8570)
#define ERROR_DS_NAME_NOT_UNIQUE __MSABI_LONG(8571)
#define ERROR_DS_MACHINE_ACCOUNT_CREATED_PRENT4 __MSABI_LONG(8572)
#define ERROR_DS_OUT_OF_VERSION_STORE __MSABI_LONG(8573)
#define ERROR_DS_INCOMPATIBLE_CONTROLS_USED __MSABI_LONG(8574)
#define ERROR_DS_NO_REF_DOMAIN __MSABI_LONG(8575)
#define ERROR_DS_RESERVED_LINK_ID __MSABI_LONG(8576)
#define ERROR_DS_LINK_ID_NOT_AVAILABLE __MSABI_LONG(8577)
#define ERROR_DS_AG_CANT_HAVE_UNIVERSAL_MEMBER __MSABI_LONG(8578)
#define ERROR_DS_MODIFYDN_DISALLOWED_BY_INSTANCE_TYPE __MSABI_LONG(8579)
#define ERROR_DS_NO_OBJECT_MOVE_IN_SCHEMA_NC __MSABI_LONG(8580)
#define ERROR_DS_MODIFYDN_DISALLOWED_BY_FLAG __MSABI_LONG(8581)
#define ERROR_DS_MODIFYDN_WRONG_GRANDPARENT __MSABI_LONG(8582)
#define ERROR_DS_NAME_ERROR_TRUST_REFERRAL __MSABI_LONG(8583)
#define ERROR_NOT_SUPPORTED_ON_STANDARD_SERVER __MSABI_LONG(8584)
#define ERROR_DS_CANT_ACCESS_REMOTE_PART_OF_AD __MSABI_LONG(8585)
#define ERROR_DS_CR_IMPOSSIBLE_TO_VALIDATE_V2 __MSABI_LONG(8586)
#define ERROR_DS_THREAD_LIMIT_EXCEEDED __MSABI_LONG(8587)
#define ERROR_DS_NOT_CLOSEST __MSABI_LONG(8588)
#define ERROR_DS_CANT_DERIVE_SPN_WITHOUT_SERVER_REF __MSABI_LONG(8589)
#define ERROR_DS_SINGLE_USER_MODE_FAILED __MSABI_LONG(8590)
#define ERROR_DS_NTDSCRIPT_SYNTAX_ERROR __MSABI_LONG(8591)
#define ERROR_DS_NTDSCRIPT_PROCESS_ERROR __MSABI_LONG(8592)
#define ERROR_DS_DIFFERENT_REPL_EPOCHS __MSABI_LONG(8593)
#define ERROR_DS_DRS_EXTENSIONS_CHANGED __MSABI_LONG(8594)
#define ERROR_DS_REPLICA_SET_CHANGE_NOT_ALLOWED_ON_DISABLED_CR __MSABI_LONG(8595)
#define ERROR_DS_NO_MSDS_INTID __MSABI_LONG(8596)
#define ERROR_DS_DUP_MSDS_INTID __MSABI_LONG(8597)
#define ERROR_DS_EXISTS_IN_RDNATTID __MSABI_LONG(8598)
#define ERROR_DS_AUTHORIZATION_FAILED __MSABI_LONG(8599)
#define ERROR_DS_INVALID_SCRIPT __MSABI_LONG(8600)
#define ERROR_DS_REMOTE_CROSSREF_OP_FAILED __MSABI_LONG(8601)
#define ERROR_DS_CROSS_REF_BUSY __MSABI_LONG(8602)
#define ERROR_DS_CANT_DERIVE_SPN_FOR_DELETED_DOMAIN __MSABI_LONG(8603)
#define ERROR_DS_CANT_DEMOTE_WITH_WRITEABLE_NC __MSABI_LONG(8604)
#define ERROR_DS_DUPLICATE_ID_FOUND __MSABI_LONG(8605)
#define ERROR_DS_INSUFFICIENT_ATTR_TO_CREATE_OBJECT __MSABI_LONG(8606)
#define ERROR_DS_GROUP_CONVERSION_ERROR __MSABI_LONG(8607)
#define ERROR_DS_CANT_MOVE_APP_BASIC_GROUP __MSABI_LONG(8608)
#define ERROR_DS_CANT_MOVE_APP_QUERY_GROUP __MSABI_LONG(8609)
#define ERROR_DS_ROLE_NOT_VERIFIED __MSABI_LONG(8610)
#define ERROR_DS_WKO_CONTAINER_CANNOT_BE_SPECIAL __MSABI_LONG(8611)
#define ERROR_DS_DOMAIN_RENAME_IN_PROGRESS __MSABI_LONG(8612)
#define ERROR_DS_EXISTING_AD_CHILD_NC __MSABI_LONG(8613)
#define ERROR_DS_REPL_LIFETIME_EXCEEDED __MSABI_LONG(8614)
#define ERROR_DS_DISALLOWED_IN_SYSTEM_CONTAINER __MSABI_LONG(8615)
#define ERROR_DS_LDAP_SEND_QUEUE_FULL __MSABI_LONG(8616)
#define ERROR_DS_DRA_OUT_SCHEDULE_WINDOW __MSABI_LONG(8617)
#define ERROR_DS_POLICY_NOT_KNOWN __MSABI_LONG(8618)
#define ERROR_NO_SITE_SETTINGS_OBJECT __MSABI_LONG(8619)
#define ERROR_NO_SECRETS __MSABI_LONG(8620)
#define ERROR_NO_WRITABLE_DC_FOUND __MSABI_LONG(8621)
#define ERROR_DS_NO_SERVER_OBJECT __MSABI_LONG(8622)
#define ERROR_DS_NO_NTDSA_OBJECT __MSABI_LONG(8623)
#define ERROR_DS_NON_ASQ_SEARCH __MSABI_LONG(8624)
#define ERROR_DS_AUDIT_FAILURE __MSABI_LONG(8625)
#define ERROR_DS_INVALID_SEARCH_FLAG_SUBTREE __MSABI_LONG(8626)
#define ERROR_DS_INVALID_SEARCH_FLAG_TUPLE __MSABI_LONG(8627)
#define ERROR_DS_HIERARCHY_TABLE_TOO_DEEP __MSABI_LONG(8628)
#define ERROR_DS_DRA_CORRUPT_UTD_VECTOR __MSABI_LONG(8629)
#define ERROR_DS_DRA_SECRETS_DENIED __MSABI_LONG(8630)
#define ERROR_DS_RESERVED_MAPI_ID __MSABI_LONG(8631)
#define ERROR_DS_MAPI_ID_NOT_AVAILABLE __MSABI_LONG(8632)
#define ERROR_DS_DRA_MISSING_KRBTGT_SECRET __MSABI_LONG(8633)
#define ERROR_DS_DOMAIN_NAME_EXISTS_IN_FOREST __MSABI_LONG(8634)
#define ERROR_DS_FLAT_NAME_EXISTS_IN_FOREST __MSABI_LONG(8635)
#define ERROR_INVALID_USER_PRINCIPAL_NAME __MSABI_LONG(8636)
#define ERROR_DS_OID_MAPPED_GROUP_CANT_HAVE_MEMBERS __MSABI_LONG(8637)
#define ERROR_DS_OID_NOT_FOUND __MSABI_LONG(8638)
#define ERROR_DS_DRA_RECYCLED_TARGET __MSABI_LONG(8639)
#define ERROR_DS_DISALLOWED_NC_REDIRECT __MSABI_LONG(8640)
#define ERROR_DS_HIGH_ADLDS_FFL __MSABI_LONG(8641)
#define ERROR_DS_HIGH_DSA_VERSION __MSABI_LONG(8642)
#define ERROR_DS_LOW_ADLDS_FFL __MSABI_LONG(8643)
#define ERROR_DOMAIN_SID_SAME_AS_LOCAL_WORKSTATION __MSABI_LONG(8644)
#define ERROR_DS_UNDELETE_SAM_VALIDATION_FAILED __MSABI_LONG(8645)
#define ERROR_INCORRECT_ACCOUNT_TYPE __MSABI_LONG(8646)
#define ERROR_DS_SPN_VALUE_NOT_UNIQUE_IN_FOREST __MSABI_LONG(8647)
#define ERROR_DS_UPN_VALUE_NOT_UNIQUE_IN_FOREST __MSABI_LONG(8648)
#define ERROR_DS_MISSING_FOREST_TRUST __MSABI_LONG(8649)
#define ERROR_DS_VALUE_KEY_NOT_UNIQUE __MSABI_LONG(8650)
#define ERROR_WEAK_WHFBKEY_BLOCKED __MSABI_LONG(8651)
#define ERROR_DS_PER_ATTRIBUTE_AUTHZ_FAILED_DURING_ADD __MSABI_LONG(8652)
#define ERROR_LOCAL_POLICY_MODIFICATION_NOT_SUPPORTED __MSABI_LONG(8653)
#define DNS_ERROR_RESPONSE_CODES_BASE 9000
#define DNS_ERROR_RCODE_NO_ERROR NO_ERROR
#define DNS_ERROR_MASK 0x00002328
#define DNS_ERROR_RCODE_FORMAT_ERROR __MSABI_LONG(9001)
#define DNS_ERROR_RCODE_SERVER_FAILURE __MSABI_LONG(9002)
#define DNS_ERROR_RCODE_NAME_ERROR __MSABI_LONG(9003)
#define DNS_ERROR_RCODE_NOT_IMPLEMENTED __MSABI_LONG(9004)
#define DNS_ERROR_RCODE_REFUSED __MSABI_LONG(9005)
#define DNS_ERROR_RCODE_YXDOMAIN __MSABI_LONG(9006)
#define DNS_ERROR_RCODE_YXRRSET __MSABI_LONG(9007)
#define DNS_ERROR_RCODE_NXRRSET __MSABI_LONG(9008)
#define DNS_ERROR_RCODE_NOTAUTH __MSABI_LONG(9009)
#define DNS_ERROR_RCODE_NOTZONE __MSABI_LONG(9010)
#define DNS_ERROR_RCODE_BADSIG __MSABI_LONG(9016)
#define DNS_ERROR_RCODE_BADKEY __MSABI_LONG(9017)
#define DNS_ERROR_RCODE_BADTIME __MSABI_LONG(9018)
#define DNS_ERROR_RCODE_LAST DNS_ERROR_RCODE_BADTIME
#define DNS_ERROR_DNSSEC_BASE 9100
#define DNS_ERROR_KEYMASTER_REQUIRED __MSABI_LONG(9101)
#define DNS_ERROR_NOT_ALLOWED_ON_SIGNED_ZONE __MSABI_LONG(9102)
#define DNS_ERROR_NSEC3_INCOMPATIBLE_WITH_RSA_SHA1 __MSABI_LONG(9103)
#define DNS_ERROR_NOT_ENOUGH_SIGNING_KEY_DESCRIPTORS __MSABI_LONG(9104)
#define DNS_ERROR_UNSUPPORTED_ALGORITHM __MSABI_LONG(9105)
#define DNS_ERROR_INVALID_KEY_SIZE __MSABI_LONG(9106)
#define DNS_ERROR_SIGNING_KEY_NOT_ACCESSIBLE __MSABI_LONG(9107)
#define DNS_ERROR_KSP_DOES_NOT_SUPPORT_PROTECTION __MSABI_LONG(9108)
#define DNS_ERROR_UNEXPECTED_DATA_PROTECTION_ERROR __MSABI_LONG(9109)
#define DNS_ERROR_UNEXPECTED_CNG_ERROR __MSABI_LONG(9110)
#define DNS_ERROR_UNKNOWN_SIGNING_PARAMETER_VERSION __MSABI_LONG(9111)
#define DNS_ERROR_KSP_NOT_ACCESSIBLE __MSABI_LONG(9112)
#define DNS_ERROR_TOO_MANY_SKDS __MSABI_LONG(9113)
#define DNS_ERROR_INVALID_ROLLOVER_PERIOD __MSABI_LONG(9114)
#define DNS_ERROR_INVALID_INITIAL_ROLLOVER_OFFSET __MSABI_LONG(9115)
#define DNS_ERROR_ROLLOVER_IN_PROGRESS __MSABI_LONG(9116)
#define DNS_ERROR_STANDBY_KEY_NOT_PRESENT __MSABI_LONG(9117)
#define DNS_ERROR_NOT_ALLOWED_ON_ZSK __MSABI_LONG(9118)
#define DNS_ERROR_NOT_ALLOWED_ON_ACTIVE_SKD __MSABI_LONG(9119)
#define DNS_ERROR_ROLLOVER_ALREADY_QUEUED __MSABI_LONG(9120)
#define DNS_ERROR_NOT_ALLOWED_ON_UNSIGNED_ZONE __MSABI_LONG(9121)
#define DNS_ERROR_BAD_KEYMASTER __MSABI_LONG(9122)
#define DNS_ERROR_INVALID_SIGNATURE_VALIDITY_PERIOD __MSABI_LONG(9123)
#define DNS_ERROR_INVALID_NSEC3_ITERATION_COUNT __MSABI_LONG(9124)
#define DNS_ERROR_DNSSEC_IS_DISABLED __MSABI_LONG(9125)
#define DNS_ERROR_INVALID_XML __MSABI_LONG(9126)
#define DNS_ERROR_NO_VALID_TRUST_ANCHORS __MSABI_LONG(9127)
#define DNS_ERROR_ROLLOVER_NOT_POKEABLE __MSABI_LONG(9128)
#define DNS_ERROR_NSEC3_NAME_COLLISION __MSABI_LONG(9129)
#define DNS_ERROR_NSEC_INCOMPATIBLE_WITH_NSEC3_RSA_SHA1 __MSABI_LONG(9130)
#define DNS_ERROR_PACKET_FMT_BASE 9500
#define DNS_INFO_NO_RECORDS __MSABI_LONG(9501)
#define DNS_ERROR_BAD_PACKET __MSABI_LONG(9502)
#define DNS_ERROR_NO_PACKET __MSABI_LONG(9503)
#define DNS_ERROR_RCODE __MSABI_LONG(9504)
#define DNS_ERROR_UNSECURE_PACKET __MSABI_LONG(9505)
#define DNS_STATUS_PACKET_UNSECURE DNS_ERROR_UNSECURE_PACKET
#define DNS_REQUEST_PENDING __MSABI_LONG(9506)
#define DNS_ERROR_NO_MEMORY ERROR_OUTOFMEMORY
#define DNS_ERROR_INVALID_NAME ERROR_INVALID_NAME
#define DNS_ERROR_INVALID_DATA ERROR_INVALID_DATA
#define DNS_ERROR_GENERAL_API_BASE 9550
#define DNS_ERROR_INVALID_TYPE __MSABI_LONG(9551)
#define DNS_ERROR_INVALID_IP_ADDRESS __MSABI_LONG(9552)
#define DNS_ERROR_INVALID_PROPERTY __MSABI_LONG(9553)
#define DNS_ERROR_TRY_AGAIN_LATER __MSABI_LONG(9554)
#define DNS_ERROR_NOT_UNIQUE __MSABI_LONG(9555)
#define DNS_ERROR_NON_RFC_NAME __MSABI_LONG(9556)
#define DNS_STATUS_FQDN __MSABI_LONG(9557)
#define DNS_STATUS_DOTTED_NAME __MSABI_LONG(9558)
#define DNS_STATUS_SINGLE_PART_NAME __MSABI_LONG(9559)
#define DNS_ERROR_INVALID_NAME_CHAR __MSABI_LONG(9560)
#define DNS_ERROR_NUMERIC_NAME __MSABI_LONG(9561)
#define DNS_ERROR_NOT_ALLOWED_ON_ROOT_SERVER __MSABI_LONG(9562)
#define DNS_ERROR_NOT_ALLOWED_UNDER_DELEGATION __MSABI_LONG(9563)
#define DNS_ERROR_CANNOT_FIND_ROOT_HINTS __MSABI_LONG(9564)
#define DNS_ERROR_INCONSISTENT_ROOT_HINTS __MSABI_LONG(9565)
#define DNS_ERROR_DWORD_VALUE_TOO_SMALL __MSABI_LONG(9566)
#define DNS_ERROR_DWORD_VALUE_TOO_LARGE __MSABI_LONG(9567)
#define DNS_ERROR_BACKGROUND_LOADING __MSABI_LONG(9568)
#define DNS_ERROR_NOT_ALLOWED_ON_RODC __MSABI_LONG(9569)
#define DNS_ERROR_NOT_ALLOWED_UNDER_DNAME __MSABI_LONG(9570)
#define DNS_ERROR_DELEGATION_REQUIRED __MSABI_LONG(9571)
#define DNS_ERROR_INVALID_POLICY_TABLE __MSABI_LONG(9572)
#define DNS_ERROR_ADDRESS_REQUIRED __MSABI_LONG(9573)
#define DNS_ERROR_ZONE_BASE 9600
#define DNS_ERROR_ZONE_DOES_NOT_EXIST __MSABI_LONG(9601)
#define DNS_ERROR_NO_ZONE_INFO __MSABI_LONG(9602)
#define DNS_ERROR_INVALID_ZONE_OPERATION __MSABI_LONG(9603)
#define DNS_ERROR_ZONE_CONFIGURATION_ERROR __MSABI_LONG(9604)
#define DNS_ERROR_ZONE_HAS_NO_SOA_RECORD __MSABI_LONG(9605)
#define DNS_ERROR_ZONE_HAS_NO_NS_RECORDS __MSABI_LONG(9606)
#define DNS_ERROR_ZONE_LOCKED __MSABI_LONG(9607)
#define DNS_ERROR_ZONE_CREATION_FAILED __MSABI_LONG(9608)
#define DNS_ERROR_ZONE_ALREADY_EXISTS __MSABI_LONG(9609)
#define DNS_ERROR_AUTOZONE_ALREADY_EXISTS __MSABI_LONG(9610)
#define DNS_ERROR_INVALID_ZONE_TYPE __MSABI_LONG(9611)
#define DNS_ERROR_SECONDARY_REQUIRES_MASTER_IP __MSABI_LONG(9612)
#define DNS_ERROR_ZONE_NOT_SECONDARY __MSABI_LONG(9613)
#define DNS_ERROR_NEED_SECONDARY_ADDRESSES __MSABI_LONG(9614)
#define DNS_ERROR_WINS_INIT_FAILED __MSABI_LONG(9615)
#define DNS_ERROR_NEED_WINS_SERVERS __MSABI_LONG(9616)
#define DNS_ERROR_NBSTAT_INIT_FAILED __MSABI_LONG(9617)
#define DNS_ERROR_SOA_DELETE_INVALID __MSABI_LONG(9618)
#define DNS_ERROR_FORWARDER_ALREADY_EXISTS __MSABI_LONG(9619)
#define DNS_ERROR_ZONE_REQUIRES_MASTER_IP __MSABI_LONG(9620)
#define DNS_ERROR_ZONE_IS_SHUTDOWN __MSABI_LONG(9621)
#define DNS_ERROR_ZONE_LOCKED_FOR_SIGNING __MSABI_LONG(9622)
#define DNS_ERROR_DATAFILE_BASE 9650
#define DNS_ERROR_PRIMARY_REQUIRES_DATAFILE __MSABI_LONG(9651)
#define DNS_ERROR_INVALID_DATAFILE_NAME __MSABI_LONG(9652)
#define DNS_ERROR_DATAFILE_OPEN_FAILURE __MSABI_LONG(9653)
#define DNS_ERROR_FILE_WRITEBACK_FAILED __MSABI_LONG(9654)
#define DNS_ERROR_DATAFILE_PARSING __MSABI_LONG(9655)
#define DNS_ERROR_DATABASE_BASE 9700
#define DNS_ERROR_RECORD_DOES_NOT_EXIST __MSABI_LONG(9701)
#define DNS_ERROR_RECORD_FORMAT __MSABI_LONG(9702)
#define DNS_ERROR_NODE_CREATION_FAILED __MSABI_LONG(9703)
#define DNS_ERROR_UNKNOWN_RECORD_TYPE __MSABI_LONG(9704)
#define DNS_ERROR_RECORD_TIMED_OUT __MSABI_LONG(9705)
#define DNS_ERROR_NAME_NOT_IN_ZONE __MSABI_LONG(9706)
#define DNS_ERROR_CNAME_LOOP __MSABI_LONG(9707)
#define DNS_ERROR_NODE_IS_CNAME __MSABI_LONG(9708)
#define DNS_ERROR_CNAME_COLLISION __MSABI_LONG(9709)
#define DNS_ERROR_RECORD_ONLY_AT_ZONE_ROOT __MSABI_LONG(9710)
#define DNS_ERROR_RECORD_ALREADY_EXISTS __MSABI_LONG(9711)
#define DNS_ERROR_SECONDARY_DATA __MSABI_LONG(9712)
#define DNS_ERROR_NO_CREATE_CACHE_DATA __MSABI_LONG(9713)
#define DNS_ERROR_NAME_DOES_NOT_EXIST __MSABI_LONG(9714)
#define DNS_WARNING_PTR_CREATE_FAILED __MSABI_LONG(9715)
#define DNS_WARNING_DOMAIN_UNDELETED __MSABI_LONG(9716)
#define DNS_ERROR_DS_UNAVAILABLE __MSABI_LONG(9717)
#define DNS_ERROR_DS_ZONE_ALREADY_EXISTS __MSABI_LONG(9718)
#define DNS_ERROR_NO_BOOTFILE_IF_DS_ZONE __MSABI_LONG(9719)
#define DNS_ERROR_NODE_IS_DNAME __MSABI_LONG(9720)
#define DNS_ERROR_DNAME_COLLISION __MSABI_LONG(9721)
#define DNS_ERROR_ALIAS_LOOP __MSABI_LONG(9722)
#define DNS_ERROR_OPERATION_BASE 9750
#define DNS_INFO_AXFR_COMPLETE __MSABI_LONG(9751)
#define DNS_ERROR_AXFR __MSABI_LONG(9752)
#define DNS_INFO_ADDED_LOCAL_WINS __MSABI_LONG(9753)
#define DNS_ERROR_SECURE_BASE 9800
#define DNS_STATUS_CONTINUE_NEEDED __MSABI_LONG(9801)
#define DNS_ERROR_SETUP_BASE 9850
#define DNS_ERROR_NO_TCPIP __MSABI_LONG(9851)
#define DNS_ERROR_NO_DNS_SERVERS __MSABI_LONG(9852)
#define DNS_ERROR_DP_BASE 9900
#define DNS_ERROR_DP_DOES_NOT_EXIST __MSABI_LONG(9901)
#define DNS_ERROR_DP_ALREADY_EXISTS __MSABI_LONG(9902)
#define DNS_ERROR_DP_NOT_ENLISTED __MSABI_LONG(9903)
#define DNS_ERROR_DP_ALREADY_ENLISTED __MSABI_LONG(9904)
#define DNS_ERROR_DP_NOT_AVAILABLE __MSABI_LONG(9905)
#define DNS_ERROR_DP_FSMO_ERROR __MSABI_LONG(9906)
#define DNS_ERROR_RRL_NOT_ENABLED __MSABI_LONG(9911)
#define DNS_ERROR_RRL_INVALID_WINDOW_SIZE __MSABI_LONG(9912)
#define DNS_ERROR_RRL_INVALID_IPV4_PREFIX __MSABI_LONG(9913)
#define DNS_ERROR_RRL_INVALID_IPV6_PREFIX __MSABI_LONG(9914)
#define DNS_ERROR_RRL_INVALID_TC_RATE __MSABI_LONG(9915)
#define DNS_ERROR_RRL_INVALID_LEAK_RATE __MSABI_LONG(9916)
#define DNS_ERROR_RRL_LEAK_RATE_LESSTHAN_TC_RATE __MSABI_LONG(9917)
#define DNS_ERROR_VIRTUALIZATION_INSTANCE_ALREADY_EXISTS __MSABI_LONG(9921)
#define DNS_ERROR_VIRTUALIZATION_INSTANCE_DOES_NOT_EXIST __MSABI_LONG(9922)
#define DNS_ERROR_VIRTUALIZATION_TREE_LOCKED __MSABI_LONG(9923)
#define DNS_ERROR_INVAILD_VIRTUALIZATION_INSTANCE_NAME __MSABI_LONG(9924)
#define DNS_ERROR_DEFAULT_VIRTUALIZATION_INSTANCE __MSABI_LONG(9925)
#define DNS_ERROR_ZONESCOPE_ALREADY_EXISTS __MSABI_LONG(9951)
#define DNS_ERROR_ZONESCOPE_DOES_NOT_EXIST __MSABI_LONG(9952)
#define DNS_ERROR_DEFAULT_ZONESCOPE __MSABI_LONG(9953)
#define DNS_ERROR_INVALID_ZONESCOPE_NAME __MSABI_LONG(9954)
#define DNS_ERROR_NOT_ALLOWED_WITH_ZONESCOPES __MSABI_LONG(9955)
#define DNS_ERROR_LOAD_ZONESCOPE_FAILED __MSABI_LONG(9956)
#define DNS_ERROR_ZONESCOPE_FILE_WRITEBACK_FAILED __MSABI_LONG(9957)
#define DNS_ERROR_INVALID_SCOPE_NAME __MSABI_LONG(9958)
#define DNS_ERROR_SCOPE_DOES_NOT_EXIST __MSABI_LONG(9959)
#define DNS_ERROR_DEFAULT_SCOPE __MSABI_LONG(9960)
#define DNS_ERROR_INVALID_SCOPE_OPERATION __MSABI_LONG(9961)
#define DNS_ERROR_SCOPE_LOCKED __MSABI_LONG(9962)
#define DNS_ERROR_SCOPE_ALREADY_EXISTS __MSABI_LONG(9963)
#define DNS_ERROR_POLICY_ALREADY_EXISTS __MSABI_LONG(9971)
#define DNS_ERROR_POLICY_DOES_NOT_EXIST __MSABI_LONG(9972)
#define DNS_ERROR_POLICY_INVALID_CRITERIA __MSABI_LONG(9973)
#define DNS_ERROR_POLICY_INVALID_SETTINGS __MSABI_LONG(9974)
#define DNS_ERROR_CLIENT_SUBNET_IS_ACCESSED __MSABI_LONG(9975)
#define DNS_ERROR_CLIENT_SUBNET_DOES_NOT_EXIST __MSABI_LONG(9976)
#define DNS_ERROR_CLIENT_SUBNET_ALREADY_EXISTS __MSABI_LONG(9977)
#define DNS_ERROR_SUBNET_DOES_NOT_EXIST __MSABI_LONG(9978)
#define DNS_ERROR_SUBNET_ALREADY_EXISTS __MSABI_LONG(9979)
#define DNS_ERROR_POLICY_LOCKED __MSABI_LONG(9980)
#define DNS_ERROR_POLICY_INVALID_WEIGHT __MSABI_LONG(9981)
#define DNS_ERROR_POLICY_INVALID_NAME __MSABI_LONG(9982)
#define DNS_ERROR_POLICY_MISSING_CRITERIA __MSABI_LONG(9983)
#define DNS_ERROR_INVALID_CLIENT_SUBNET_NAME __MSABI_LONG(9984)
#define DNS_ERROR_POLICY_PROCESSING_ORDER_INVALID __MSABI_LONG(9985)
#define DNS_ERROR_POLICY_SCOPE_MISSING __MSABI_LONG(9986)
#define DNS_ERROR_POLICY_SCOPE_NOT_ALLOWED __MSABI_LONG(9987)
#define DNS_ERROR_SERVERSCOPE_IS_REFERENCED __MSABI_LONG(9988)
#define DNS_ERROR_ZONESCOPE_IS_REFERENCED __MSABI_LONG(9989)
#define DNS_ERROR_POLICY_INVALID_CRITERIA_CLIENT_SUBNET __MSABI_LONG(9990)
#define DNS_ERROR_POLICY_INVALID_CRITERIA_TRANSPORT_PROTOCOL __MSABI_LONG(9991)
#define DNS_ERROR_POLICY_INVALID_CRITERIA_NETWORK_PROTOCOL __MSABI_LONG(9992)
#define DNS_ERROR_POLICY_INVALID_CRITERIA_INTERFACE __MSABI_LONG(9993)
#define DNS_ERROR_POLICY_INVALID_CRITERIA_FQDN __MSABI_LONG(9994)
#define DNS_ERROR_POLICY_INVALID_CRITERIA_QUERY_TYPE __MSABI_LONG(9995)
#define DNS_ERROR_POLICY_INVALID_CRITERIA_TIME_OF_DAY __MSABI_LONG(9996)

#ifndef WSABASEERR
#define WSABASEERR 10000
#define WSAEINTR (WSABASEERR + 4)
#define WSAEBADF (WSABASEERR + 9)
#define WSAEACCES (WSABASEERR + 13)
#define WSAEFAULT (WSABASEERR + 14)
#define WSAEINVAL (WSABASEERR + 22)
#define WSAEMFILE (WSABASEERR + 24)
#define WSAEWOULDBLOCK (WSABASEERR + 35)
#define WSAEINPROGRESS (WSABASEERR + 36)
#define WSAEALREADY (WSABASEERR + 37)
#define WSAENOTSOCK (WSABASEERR + 38)
#define WSAEDESTADDRREQ (WSABASEERR + 39)
#define WSAEMSGSIZE (WSABASEERR + 40)
#define WSAEPROTOTYPE (WSABASEERR + 41)
#define WSAENOPROTOOPT (WSABASEERR + 42)
#define WSAEPROTONOSUPPORT (WSABASEERR + 43)
#define WSAESOCKTNOSUPPORT (WSABASEERR + 44)
#define WSAEOPNOTSUPP (WSABASEERR + 45)
#define WSAEPFNOSUPPORT (WSABASEERR + 46)
#define WSAEAFNOSUPPORT (WSABASEERR + 47)
#define WSAEADDRINUSE (WSABASEERR + 48)
#define WSAEADDRNOTAVAIL (WSABASEERR + 49)
#define WSAENETDOWN (WSABASEERR + 50)
#define WSAENETUNREACH (WSABASEERR + 51)
#define WSAENETRESET (WSABASEERR + 52)
#define WSAECONNABORTED (WSABASEERR + 53)
#define WSAECONNRESET (WSABASEERR + 54)
#define WSAENOBUFS (WSABASEERR + 55)
#define WSAEISCONN (WSABASEERR + 56)
#define WSAENOTCONN (WSABASEERR + 57)
#define WSAESHUTDOWN (WSABASEERR + 58)
#define WSAETOOMANYREFS (WSABASEERR + 59)
#define WSAETIMEDOUT (WSABASEERR + 60)
#define WSAECONNREFUSED (WSABASEERR + 61)
#define WSAELOOP (WSABASEERR + 62)
#define WSAENAMETOOLONG (WSABASEERR + 63)
#define WSAEHOSTDOWN (WSABASEERR + 64)
#define WSAEHOSTUNREACH (WSABASEERR + 65)
#define WSAENOTEMPTY (WSABASEERR + 66)
#define WSAEPROCLIM (WSABASEERR + 67)
#define WSAEUSERS (WSABASEERR + 68)
#define WSAEDQUOT (WSABASEERR + 69)
#define WSAESTALE (WSABASEERR + 70)
#define WSAEREMOTE (WSABASEERR + 71)
#define WSASYSNOTREADY (WSABASEERR + 91)
#define WSAVERNOTSUPPORTED (WSABASEERR + 92)
#define WSANOTINITIALISED (WSABASEERR + 93)
#define WSAEDISCON (WSABASEERR + 101)
#define WSAENOMORE (WSABASEERR + 102)
#define WSAECANCELLED (WSABASEERR + 103)
#define WSAEINVALIDPROCTABLE (WSABASEERR + 104)
#define WSAEINVALIDPROVIDER (WSABASEERR + 105)
#define WSAEPROVIDERFAILEDINIT (WSABASEERR + 106)
#define WSASYSCALLFAILURE (WSABASEERR + 107)
#define WSASERVICE_NOT_FOUND (WSABASEERR + 108)
#define WSATYPE_NOT_FOUND (WSABASEERR + 109)
#define WSA_E_NO_MORE (WSABASEERR + 110)
#define WSA_E_CANCELLED (WSABASEERR + 111)
#define WSAEREFUSED (WSABASEERR + 112)
#ifndef WSAHOST_NOT_FOUND
#define WSAHOST_NOT_FOUND (WSABASEERR + 1001)
#endif
#ifndef WSATRY_AGAIN
#define WSATRY_AGAIN (WSABASEERR + 1002)
#endif
#ifndef WSANO_RECOVERY
#define WSANO_RECOVERY (WSABASEERR + 1003)
#endif
#ifndef WSANO_DATA
#define WSANO_DATA (WSABASEERR + 1004)
#endif
#ifndef WSA_QOS_RECEIVERS
#define WSA_QOS_RECEIVERS (WSABASEERR + 1005)
#endif
#ifndef WSA_QOS_SENDERS
#define WSA_QOS_SENDERS (WSABASEERR + 1006)
#endif
#ifndef WSA_QOS_NO_SENDERS
#define WSA_QOS_NO_SENDERS (WSABASEERR + 1007)
#endif
#ifndef WSA_QOS_NO_RECEIVERS
#define WSA_QOS_NO_RECEIVERS (WSABASEERR + 1008)
#endif
#ifndef WSA_QOS_REQUEST_CONFIRMED
#define WSA_QOS_REQUEST_CONFIRMED (WSABASEERR + 1009)
#endif
#ifndef WSA_QOS_ADMISSION_FAILURE
#define WSA_QOS_ADMISSION_FAILURE (WSABASEERR + 1010)
#endif
#ifndef WSA_QOS_POLICY_FAILURE
#define WSA_QOS_POLICY_FAILURE (WSABASEERR + 1011)
#endif
#ifndef WSA_QOS_BAD_STYLE
#define WSA_QOS_BAD_STYLE (WSABASEERR + 1012)
#endif
#ifndef WSA_QOS_BAD_OBJECT
#define WSA_QOS_BAD_OBJECT (WSABASEERR + 1013)
#endif
#ifndef WSA_QOS_TRAFFIC_CTRL_ERROR
#define WSA_QOS_TRAFFIC_CTRL_ERROR (WSABASEERR + 1014)
#endif
#ifndef WSA_QOS_GENERIC_ERROR
#define WSA_QOS_GENERIC_ERROR (WSABASEERR + 1015)
#endif
#ifndef WSA_QOS_ESERVICETYPE
#define WSA_QOS_ESERVICETYPE (WSABASEERR + 1016)
#endif
#ifndef WSA_QOS_EFLOWSPEC
#define WSA_QOS_EFLOWSPEC (WSABASEERR + 1017)
#endif
#ifndef WSA_QOS_EPROVSPECBUF
#define WSA_QOS_EPROVSPECBUF (WSABASEERR + 1018)
#endif
#ifndef WSA_QOS_EFILTERSTYLE
#define WSA_QOS_EFILTERSTYLE (WSABASEERR + 1019)
#endif
#ifndef WSA_QOS_EFILTERTYPE
#define WSA_QOS_EFILTERTYPE (WSABASEERR + 1020)
#endif
#ifndef WSA_QOS_EFILTERCOUNT
#define WSA_QOS_EFILTERCOUNT (WSABASEERR + 1021)
#endif
#ifndef WSA_QOS_EOBJLENGTH
#define WSA_QOS_EOBJLENGTH (WSABASEERR + 1022)
#endif
#ifndef WSA_QOS_EFLOWCOUNT
#define WSA_QOS_EFLOWCOUNT (WSABASEERR + 1023)
#endif
#ifndef WSA_QOS_EUNKNOWNPSOBJ
#define WSA_QOS_EUNKNOWNPSOBJ (WSABASEERR + 1024)
#endif
#ifndef WSA_QOS_EUNKOWNPSOBJ
#define WSA_QOS_EUNKOWNPSOBJ WSA_QOS_EUNKNOWNPSOBJ
#endif
#ifndef WSA_QOS_EPOLICYOBJ
#define WSA_QOS_EPOLICYOBJ (WSABASEERR + 1025)
#endif
#ifndef WSA_QOS_EFLOWDESC
#define WSA_QOS_EFLOWDESC (WSABASEERR + 1026)
#endif
#ifndef WSA_QOS_EPSFLOWSPEC
#define WSA_QOS_EPSFLOWSPEC (WSABASEERR + 1027)
#endif
#ifndef WSA_QOS_EPSFILTERSPEC
#define WSA_QOS_EPSFILTERSPEC (WSABASEERR + 1028)
#endif
#ifndef WSA_QOS_ESDMODEOBJ
#define WSA_QOS_ESDMODEOBJ (WSABASEERR + 1029)
#endif
#ifndef WSA_QOS_ESHAPERATEOBJ
#define WSA_QOS_ESHAPERATEOBJ (WSABASEERR + 1030)
#endif
#ifndef WSA_QOS_RESERVED_PETYPE
#define WSA_QOS_RESERVED_PETYPE (WSABASEERR + 1031)
#endif
#ifndef WSA_SECURE_HOST_NOT_FOUND
#define WSA_SECURE_HOST_NOT_FOUND (WSABASEERR + 1032)
#endif
#ifndef WSA_IPSEC_NAME_POLICY_ERROR
#define WSA_IPSEC_NAME_POLICY_ERROR (WSABASEERR + 1033)
#endif
#endif /* WSABASEERR */

#define ERROR_SXS_SECTION_NOT_FOUND __MSABI_LONG(14000)
#define ERROR_SXS_CANT_GEN_ACTCTX __MSABI_LONG(14001)
#define ERROR_SXS_INVALID_ACTCTXDATA_FORMAT __MSABI_LONG(14002)
#define ERROR_SXS_ASSEMBLY_NOT_FOUND __MSABI_LONG(14003)
#define ERROR_SXS_MANIFEST_FORMAT_ERROR __MSABI_LONG(14004)
#define ERROR_SXS_MANIFEST_PARSE_ERROR __MSABI_LONG(14005)
#define ERROR_SXS_ACTIVATION_CONTEXT_DISABLED __MSABI_LONG(14006)
#define ERROR_SXS_KEY_NOT_FOUND __MSABI_LONG(14007)
#define ERROR_SXS_VERSION_CONFLICT __MSABI_LONG(14008)
#define ERROR_SXS_WRONG_SECTION_TYPE __MSABI_LONG(14009)
#define ERROR_SXS_THREAD_QUERIES_DISABLED __MSABI_LONG(14010)
#define ERROR_SXS_PROCESS_DEFAULT_ALREADY_SET __MSABI_LONG(14011)
#define ERROR_SXS_UNKNOWN_ENCODING_GROUP __MSABI_LONG(14012)
#define ERROR_SXS_UNKNOWN_ENCODING __MSABI_LONG(14013)
#define ERROR_SXS_INVALID_XML_NAMESPACE_URI __MSABI_LONG(14014)
#define ERROR_SXS_ROOT_MANIFEST_DEPENDENCY_NOT_INSTALLED __MSABI_LONG(14015)
#define ERROR_SXS_LEAF_MANIFEST_DEPENDENCY_NOT_INSTALLED __MSABI_LONG(14016)
#define ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE __MSABI_LONG(14017)
#define ERROR_SXS_MANIFEST_MISSING_REQUIRED_DEFAULT_NAMESPACE __MSABI_LONG(14018)
#define ERROR_SXS_MANIFEST_INVALID_REQUIRED_DEFAULT_NAMESPACE __MSABI_LONG(14019)
#define ERROR_SXS_PRIVATE_MANIFEST_CROSS_PATH_WITH_REPARSE_POINT __MSABI_LONG(14020)
#define ERROR_SXS_DUPLICATE_DLL_NAME __MSABI_LONG(14021)
#define ERROR_SXS_DUPLICATE_WINDOWCLASS_NAME __MSABI_LONG(14022)
#define ERROR_SXS_DUPLICATE_CLSID __MSABI_LONG(14023)
#define ERROR_SXS_DUPLICATE_IID __MSABI_LONG(14024)
#define ERROR_SXS_DUPLICATE_TLBID __MSABI_LONG(14025)
#define ERROR_SXS_DUPLICATE_PROGID __MSABI_LONG(14026)
#define ERROR_SXS_DUPLICATE_ASSEMBLY_NAME __MSABI_LONG(14027)
#define ERROR_SXS_FILE_HASH_MISMATCH __MSABI_LONG(14028)
#define ERROR_SXS_POLICY_PARSE_ERROR __MSABI_LONG(14029)
#define ERROR_SXS_XML_E_MISSINGQUOTE __MSABI_LONG(14030)
#define ERROR_SXS_XML_E_COMMENTSYNTAX __MSABI_LONG(14031)
#define ERROR_SXS_XML_E_BADSTARTNAMECHAR __MSABI_LONG(14032)
#define ERROR_SXS_XML_E_BADNAMECHAR __MSABI_LONG(14033)
#define ERROR_SXS_XML_E_BADCHARINSTRING __MSABI_LONG(14034)
#define ERROR_SXS_XML_E_XMLDECLSYNTAX __MSABI_LONG(14035)
#define ERROR_SXS_XML_E_BADCHARDATA __MSABI_LONG(14036)
#define ERROR_SXS_XML_E_MISSINGWHITESPACE __MSABI_LONG(14037)
#define ERROR_SXS_XML_E_EXPECTINGTAGEND __MSABI_LONG(14038)
#define ERROR_SXS_XML_E_MISSINGSEMICOLON __MSABI_LONG(14039)
#define ERROR_SXS_XML_E_UNBALANCEDPAREN __MSABI_LONG(14040)
#define ERROR_SXS_XML_E_INTERNALERROR __MSABI_LONG(14041)
#define ERROR_SXS_XML_E_UNEXPECTED_WHITESPACE __MSABI_LONG(14042)
#define ERROR_SXS_XML_E_INCOMPLETE_ENCODING __MSABI_LONG(14043)
#define ERROR_SXS_XML_E_MISSING_PAREN __MSABI_LONG(14044)
#define ERROR_SXS_XML_E_EXPECTINGCLOSEQUOTE __MSABI_LONG(14045)
#define ERROR_SXS_XML_E_MULTIPLE_COLONS __MSABI_LONG(14046)
#define ERROR_SXS_XML_E_INVALID_DECIMAL __MSABI_LONG(14047)
#define ERROR_SXS_XML_E_INVALID_HEXIDECIMAL __MSABI_LONG(14048)
#define ERROR_SXS_XML_E_INVALID_UNICODE __MSABI_LONG(14049)
#define ERROR_SXS_XML_E_WHITESPACEORQUESTIONMARK __MSABI_LONG(14050)
#define ERROR_SXS_XML_E_UNEXPECTEDENDTAG __MSABI_LONG(14051)
#define ERROR_SXS_XML_E_UNCLOSEDTAG __MSABI_LONG(14052)
#define ERROR_SXS_XML_E_DUPLICATEATTRIBUTE __MSABI_LONG(14053)
#define ERROR_SXS_XML_E_MULTIPLEROOTS __MSABI_LONG(14054)
#define ERROR_SXS_XML_E_INVALIDATROOTLEVEL __MSABI_LONG(14055)
#define ERROR_SXS_XML_E_BADXMLDECL __MSABI_LONG(14056)
#define ERROR_SXS_XML_E_MISSINGROOT __MSABI_LONG(14057)
#define ERROR_SXS_XML_E_UNEXPECTEDEOF __MSABI_LONG(14058)
#define ERROR_SXS_XML_E_BADPEREFINSUBSET __MSABI_LONG(14059)
#define ERROR_SXS_XML_E_UNCLOSEDSTARTTAG __MSABI_LONG(14060)
#define ERROR_SXS_XML_E_UNCLOSEDENDTAG __MSABI_LONG(14061)
#define ERROR_SXS_XML_E_UNCLOSEDSTRING __MSABI_LONG(14062)
#define ERROR_SXS_XML_E_UNCLOSEDCOMMENT __MSABI_LONG(14063)
#define ERROR_SXS_XML_E_UNCLOSEDDECL __MSABI_LONG(14064)
#define ERROR_SXS_XML_E_UNCLOSEDCDATA __MSABI_LONG(14065)
#define ERROR_SXS_XML_E_RESERVEDNAMESPACE __MSABI_LONG(14066)
#define ERROR_SXS_XML_E_INVALIDENCODING __MSABI_LONG(14067)
#define ERROR_SXS_XML_E_INVALIDSWITCH __MSABI_LONG(14068)
#define ERROR_SXS_XML_E_BADXMLCASE __MSABI_LONG(14069)
#define ERROR_SXS_XML_E_INVALID_STANDALONE __MSABI_LONG(14070)
#define ERROR_SXS_XML_E_UNEXPECTED_STANDALONE __MSABI_LONG(14071)
#define ERROR_SXS_XML_E_INVALID_VERSION __MSABI_LONG(14072)
#define ERROR_SXS_XML_E_MISSINGEQUALS __MSABI_LONG(14073)
#define ERROR_SXS_PROTECTION_RECOVERY_FAILED __MSABI_LONG(14074)
#define ERROR_SXS_PROTECTION_PUBLIC_KEY_TOO_SHORT __MSABI_LONG(14075)
#define ERROR_SXS_PROTECTION_CATALOG_NOT_VALID __MSABI_LONG(14076)
#define ERROR_SXS_UNTRANSLATABLE_HRESULT __MSABI_LONG(14077)
#define ERROR_SXS_PROTECTION_CATALOG_FILE_MISSING __MSABI_LONG(14078)
#define ERROR_SXS_MISSING_ASSEMBLY_IDENTITY_ATTRIBUTE __MSABI_LONG(14079)
#define ERROR_SXS_INVALID_ASSEMBLY_IDENTITY_ATTRIBUTE_NAME __MSABI_LONG(14080)
#define ERROR_SXS_ASSEMBLY_MISSING __MSABI_LONG(14081)
#define ERROR_SXS_CORRUPT_ACTIVATION_STACK __MSABI_LONG(14082)
#define ERROR_SXS_CORRUPTION __MSABI_LONG(14083)
#define ERROR_SXS_EARLY_DEACTIVATION __MSABI_LONG(14084)
#define ERROR_SXS_INVALID_DEACTIVATION __MSABI_LONG(14085)
#define ERROR_SXS_MULTIPLE_DEACTIVATION __MSABI_LONG(14086)
#define ERROR_SXS_PROCESS_TERMINATION_REQUESTED __MSABI_LONG(14087)
#define ERROR_SXS_RELEASE_ACTIVATION_CONTEXT __MSABI_LONG(14088)
#define ERROR_SXS_SYSTEM_DEFAULT_ACTIVATION_CONTEXT_EMPTY __MSABI_LONG(14089)
#define ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_VALUE __MSABI_LONG(14090)
#define ERROR_SXS_INVALID_IDENTITY_ATTRIBUTE_NAME __MSABI_LONG(14091)
#define ERROR_SXS_IDENTITY_DUPLICATE_ATTRIBUTE __MSABI_LONG(14092)
#define ERROR_SXS_IDENTITY_PARSE_ERROR __MSABI_LONG(14093)
#define ERROR_MALFORMED_SUBSTITUTION_STRING __MSABI_LONG(14094)
#define ERROR_SXS_INCORRECT_PUBLIC_KEY_TOKEN __MSABI_LONG(14095)
#define ERROR_UNMAPPED_SUBSTITUTION_STRING __MSABI_LONG(14096)
#define ERROR_SXS_ASSEMBLY_NOT_LOCKED __MSABI_LONG(14097)
#define ERROR_SXS_COMPONENT_STORE_CORRUPT __MSABI_LONG(14098)
#define ERROR_ADVANCED_INSTALLER_FAILED __MSABI_LONG(14099)
#define ERROR_XML_ENCODING_MISMATCH __MSABI_LONG(14100)
#define ERROR_SXS_MANIFEST_IDENTITY_SAME_BUT_CONTENTS_DIFFERENT __MSABI_LONG(14101)
#define ERROR_SXS_IDENTITIES_DIFFERENT __MSABI_LONG(14102)
#define ERROR_SXS_ASSEMBLY_IS_NOT_A_DEPLOYMENT __MSABI_LONG(14103)
#define ERROR_SXS_FILE_NOT_PART_OF_ASSEMBLY __MSABI_LONG(14104)
#define ERROR_SXS_MANIFEST_TOO_BIG __MSABI_LONG(14105)
#define ERROR_SXS_SETTING_NOT_REGISTERED __MSABI_LONG(14106)
#define ERROR_SXS_TRANSACTION_CLOSURE_INCOMPLETE __MSABI_LONG(14107)
#define ERROR_SMI_PRIMITIVE_INSTALLER_FAILED __MSABI_LONG(14108)
#define ERROR_GENERIC_COMMAND_FAILED __MSABI_LONG(14109)
#define ERROR_SXS_FILE_HASH_MISSING __MSABI_LONG(14110)
#define ERROR_IPSEC_QM_POLICY_EXISTS __MSABI_LONG(13000)
#define ERROR_IPSEC_QM_POLICY_NOT_FOUND __MSABI_LONG(13001)
#define ERROR_IPSEC_QM_POLICY_IN_USE __MSABI_LONG(13002)
#define ERROR_IPSEC_MM_POLICY_EXISTS __MSABI_LONG(13003)
#define ERROR_IPSEC_MM_POLICY_NOT_FOUND __MSABI_LONG(13004)
#define ERROR_IPSEC_MM_POLICY_IN_USE __MSABI_LONG(13005)
#define ERROR_IPSEC_MM_FILTER_EXISTS __MSABI_LONG(13006)
#define ERROR_IPSEC_MM_FILTER_NOT_FOUND __MSABI_LONG(13007)
#define ERROR_IPSEC_TRANSPORT_FILTER_EXISTS __MSABI_LONG(13008)
#define ERROR_IPSEC_TRANSPORT_FILTER_NOT_FOUND __MSABI_LONG(13009)
#define ERROR_IPSEC_MM_AUTH_EXISTS __MSABI_LONG(13010)
#define ERROR_IPSEC_MM_AUTH_NOT_FOUND __MSABI_LONG(13011)
#define ERROR_IPSEC_MM_AUTH_IN_USE __MSABI_LONG(13012)
#define ERROR_IPSEC_DEFAULT_MM_POLICY_NOT_FOUND __MSABI_LONG(13013)
#define ERROR_IPSEC_DEFAULT_MM_AUTH_NOT_FOUND __MSABI_LONG(13014)
#define ERROR_IPSEC_DEFAULT_QM_POLICY_NOT_FOUND __MSABI_LONG(13015)
#define ERROR_IPSEC_TUNNEL_FILTER_EXISTS __MSABI_LONG(13016)
#define ERROR_IPSEC_TUNNEL_FILTER_NOT_FOUND __MSABI_LONG(13017)
#define ERROR_IPSEC_MM_FILTER_PENDING_DELETION __MSABI_LONG(13018)
#define ERROR_IPSEC_TRANSPORT_FILTER_PENDING_DELETION __MSABI_LONG(13019)
#define ERROR_IPSEC_TUNNEL_FILTER_PENDING_DELETION __MSABI_LONG(13020)
#define ERROR_IPSEC_MM_POLICY_PENDING_DELETION __MSABI_LONG(13021)
#define ERROR_IPSEC_MM_AUTH_PENDING_DELETION __MSABI_LONG(13022)
#define ERROR_IPSEC_QM_POLICY_PENDING_DELETION __MSABI_LONG(13023)
#define WARNING_IPSEC_MM_POLICY_PRUNED __MSABI_LONG(13024)
#define WARNING_IPSEC_QM_POLICY_PRUNED __MSABI_LONG(13025)
#define ERROR_IPSEC_IKE_NEG_STATUS_BEGIN __MSABI_LONG(13800)
#define ERROR_IPSEC_IKE_AUTH_FAIL __MSABI_LONG(13801)
#define ERROR_IPSEC_IKE_ATTRIB_FAIL __MSABI_LONG(13802)
#define ERROR_IPSEC_IKE_NEGOTIATION_PENDING __MSABI_LONG(13803)
#define ERROR_IPSEC_IKE_GENERAL_PROCESSING_ERROR __MSABI_LONG(13804)
#define ERROR_IPSEC_IKE_TIMED_OUT __MSABI_LONG(13805)
#define ERROR_IPSEC_IKE_NO_CERT __MSABI_LONG(13806)
#define ERROR_IPSEC_IKE_SA_DELETED __MSABI_LONG(13807)
#define ERROR_IPSEC_IKE_SA_REAPED __MSABI_LONG(13808)
#define ERROR_IPSEC_IKE_MM_ACQUIRE_DROP __MSABI_LONG(13809)
#define ERROR_IPSEC_IKE_QM_ACQUIRE_DROP __MSABI_LONG(13810)
#define ERROR_IPSEC_IKE_QUEUE_DROP_MM __MSABI_LONG(13811)
#define ERROR_IPSEC_IKE_QUEUE_DROP_NO_MM __MSABI_LONG(13812)
#define ERROR_IPSEC_IKE_DROP_NO_RESPONSE __MSABI_LONG(13813)
#define ERROR_IPSEC_IKE_MM_DELAY_DROP __MSABI_LONG(13814)
#define ERROR_IPSEC_IKE_QM_DELAY_DROP __MSABI_LONG(13815)
#define ERROR_IPSEC_IKE_ERROR __MSABI_LONG(13816)
#define ERROR_IPSEC_IKE_CRL_FAILED __MSABI_LONG(13817)
#define ERROR_IPSEC_IKE_INVALID_KEY_USAGE __MSABI_LONG(13818)
#define ERROR_IPSEC_IKE_INVALID_CERT_TYPE __MSABI_LONG(13819)
#define ERROR_IPSEC_IKE_NO_PRIVATE_KEY __MSABI_LONG(13820)
#define ERROR_IPSEC_IKE_DH_FAIL __MSABI_LONG(13822)
#define ERROR_IPSEC_IKE_INVALID_HEADER __MSABI_LONG(13824)
#define ERROR_IPSEC_IKE_NO_POLICY __MSABI_LONG(13825)
#define ERROR_IPSEC_IKE_INVALID_SIGNATURE __MSABI_LONG(13826)
#define ERROR_IPSEC_IKE_KERBEROS_ERROR __MSABI_LONG(13827)
#define ERROR_IPSEC_IKE_NO_PUBLIC_KEY __MSABI_LONG(13828)
#define ERROR_IPSEC_IKE_PROCESS_ERR __MSABI_LONG(13829)
#define ERROR_IPSEC_IKE_PROCESS_ERR_SA __MSABI_LONG(13830)
#define ERROR_IPSEC_IKE_PROCESS_ERR_PROP __MSABI_LONG(13831)
#define ERROR_IPSEC_IKE_PROCESS_ERR_TRANS __MSABI_LONG(13832)
#define ERROR_IPSEC_IKE_PROCESS_ERR_KE __MSABI_LONG(13833)
#define ERROR_IPSEC_IKE_PROCESS_ERR_ID __MSABI_LONG(13834)
#define ERROR_IPSEC_IKE_PROCESS_ERR_CERT __MSABI_LONG(13835)
#define ERROR_IPSEC_IKE_PROCESS_ERR_CERT_REQ __MSABI_LONG(13836)
#define ERROR_IPSEC_IKE_PROCESS_ERR_HASH __MSABI_LONG(13837)
#define ERROR_IPSEC_IKE_PROCESS_ERR_SIG __MSABI_LONG(13838)
#define ERROR_IPSEC_IKE_PROCESS_ERR_NONCE __MSABI_LONG(13839)
#define ERROR_IPSEC_IKE_PROCESS_ERR_NOTIFY __MSABI_LONG(13840)
#define ERROR_IPSEC_IKE_PROCESS_ERR_DELETE __MSABI_LONG(13841)
#define ERROR_IPSEC_IKE_PROCESS_ERR_VENDOR __MSABI_LONG(13842)
#define ERROR_IPSEC_IKE_INVALID_PAYLOAD __MSABI_LONG(13843)
#define ERROR_IPSEC_IKE_LOAD_SOFT_SA __MSABI_LONG(13844)
#define ERROR_IPSEC_IKE_SOFT_SA_TORN_DOWN __MSABI_LONG(13845)
#define ERROR_IPSEC_IKE_INVALID_COOKIE __MSABI_LONG(13846)
#define ERROR_IPSEC_IKE_NO_PEER_CERT __MSABI_LONG(13847)
#define ERROR_IPSEC_IKE_PEER_CRL_FAILED __MSABI_LONG(13848)
#define ERROR_IPSEC_IKE_POLICY_CHANGE __MSABI_LONG(13849)
#define ERROR_IPSEC_IKE_NO_MM_POLICY __MSABI_LONG(13850)
#define ERROR_IPSEC_IKE_NOTCBPRIV __MSABI_LONG(13851)
#define ERROR_IPSEC_IKE_SECLOADFAIL __MSABI_LONG(13852)
#define ERROR_IPSEC_IKE_FAILSSPINIT __MSABI_LONG(13853)
#define ERROR_IPSEC_IKE_FAILQUERYSSP __MSABI_LONG(13854)
#define ERROR_IPSEC_IKE_SRVACQFAIL __MSABI_LONG(13855)
#define ERROR_IPSEC_IKE_SRVQUERYCRED __MSABI_LONG(13856)
#define ERROR_IPSEC_IKE_GETSPIFAIL __MSABI_LONG(13857)
#define ERROR_IPSEC_IKE_INVALID_FILTER __MSABI_LONG(13858)
#define ERROR_IPSEC_IKE_OUT_OF_MEMORY __MSABI_LONG(13859)
#define ERROR_IPSEC_IKE_ADD_UPDATE_KEY_FAILED __MSABI_LONG(13860)
#define ERROR_IPSEC_IKE_INVALID_POLICY __MSABI_LONG(13861)
#define ERROR_IPSEC_IKE_UNKNOWN_DOI __MSABI_LONG(13862)
#define ERROR_IPSEC_IKE_INVALID_SITUATION __MSABI_LONG(13863)
#define ERROR_IPSEC_IKE_DH_FAILURE __MSABI_LONG(13864)
#define ERROR_IPSEC_IKE_INVALID_GROUP __MSABI_LONG(13865)
#define ERROR_IPSEC_IKE_ENCRYPT __MSABI_LONG(13866)
#define ERROR_IPSEC_IKE_DECRYPT __MSABI_LONG(13867)
#define ERROR_IPSEC_IKE_POLICY_MATCH __MSABI_LONG(13868)
#define ERROR_IPSEC_IKE_UNSUPPORTED_ID __MSABI_LONG(13869)
#define ERROR_IPSEC_IKE_INVALID_HASH __MSABI_LONG(13870)
#define ERROR_IPSEC_IKE_INVALID_HASH_ALG __MSABI_LONG(13871)
#define ERROR_IPSEC_IKE_INVALID_HASH_SIZE __MSABI_LONG(13872)
#define ERROR_IPSEC_IKE_INVALID_ENCRYPT_ALG __MSABI_LONG(13873)
#define ERROR_IPSEC_IKE_INVALID_AUTH_ALG __MSABI_LONG(13874)
#define ERROR_IPSEC_IKE_INVALID_SIG __MSABI_LONG(13875)
#define ERROR_IPSEC_IKE_LOAD_FAILED __MSABI_LONG(13876)
#define ERROR_IPSEC_IKE_RPC_DELETE __MSABI_LONG(13877)
#define ERROR_IPSEC_IKE_BENIGN_REINIT __MSABI_LONG(13878)
#define ERROR_IPSEC_IKE_INVALID_RESPONDER_LIFETIME_NOTIFY __MSABI_LONG(13879)
#define ERROR_IPSEC_IKE_INVALID_CERT_KEYLEN __MSABI_LONG(13881)
#define ERROR_IPSEC_IKE_MM_LIMIT __MSABI_LONG(13882)
#define ERROR_IPSEC_IKE_NEGOTIATION_DISABLED __MSABI_LONG(13883)
/* ********************   WARNING   ********************
 * This is inconsistent with MSDN!
 * In MSDN:
 *     ERROR_IPSEC_IKE_QM_LIMIT => 13884 (here not defined)
 *     ERROR_IPSEC_IKE_NEG_STATUS_END => 13897 (here 13884)
 * Source:
 *     WinINet Reference -> WinINet Constants -> Error Messages
 *     https://msdn.microsoft.com/en-us/library/windows/desktop/aa385465%28v=vs.85%29.aspx
 * ******************** END WARNING ******************** */
/*#define ERROR_IPSEC_IKE_NEG_STATUS_END __MSABI_LONG(13884)*/
#define ERROR_IPSEC_IKE_QM_LIMIT __MSABI_LONG(13884)
#define ERROR_IPSEC_IKE_MM_EXPIRED __MSABI_LONG(13885)
#define ERROR_IPSEC_IKE_PEER_MM_ASSUMED_INVALID __MSABI_LONG(13886)
#define ERROR_IPSEC_IKE_CERT_CHAIN_POLICY_MISMATCH __MSABI_LONG(13887)
#define ERROR_IPSEC_IKE_UNEXPECTED_MESSAGE_ID __MSABI_LONG(13888)
#define ERROR_IPSEC_IKE_INVALID_AUTH_PAYLOAD __MSABI_LONG(13889)
#define ERROR_IPSEC_IKE_DOS_COOKIE_SENT __MSABI_LONG(13890)
#define ERROR_IPSEC_IKE_SHUTTING_DOWN __MSABI_LONG(13891)
#define ERROR_IPSEC_IKE_CGA_AUTH_FAILED __MSABI_LONG(13892)
#define ERROR_IPSEC_IKE_PROCESS_ERR_NATOA __MSABI_LONG(13893)
#define ERROR_IPSEC_IKE_INVALID_MM_FOR_QM __MSABI_LONG(13894)
#define ERROR_IPSEC_IKE_QM_EXPIRED __MSABI_LONG(13895)
#define ERROR_IPSEC_IKE_TOO_MANY_FILTERS __MSABI_LONG(13896)
#define ERROR_IPSEC_IKE_NEG_STATUS_END __MSABI_LONG(13897)
#define ERROR_IPSEC_IKE_KILL_DUMMY_NAP_TUNNEL __MSABI_LONG(13898)
#define ERROR_IPSEC_IKE_INNER_IP_ASSIGNMENT_FAILURE __MSABI_LONG(13899)
#define ERROR_IPSEC_IKE_REQUIRE_CP_PAYLOAD_MISSING __MSABI_LONG(13900)
#define ERROR_IPSEC_KEY_MODULE_IMPERSONATION_NEGOTIATION_PENDING __MSABI_LONG(13901)
#define ERROR_IPSEC_IKE_COEXISTENCE_SUPPRESS __MSABI_LONG(13902)
#define ERROR_IPSEC_IKE_RATELIMIT_DROP __MSABI_LONG(13903)
#define ERROR_IPSEC_IKE_PEER_DOESNT_SUPPORT_MOBIKE __MSABI_LONG(13904)
#define ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE __MSABI_LONG(13905)
#define ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_FAILURE __MSABI_LONG(13906)
#define ERROR_IPSEC_IKE_AUTHORIZATION_FAILURE_WITH_OPTIONAL_RETRY __MSABI_LONG(13907)
#define ERROR_IPSEC_IKE_STRONG_CRED_AUTHORIZATION_AND_CERTMAP_FAILURE __MSABI_LONG(13908)
#define ERROR_IPSEC_IKE_NEG_STATUS_EXTENDED_END __MSABI_LONG(13909)
#define ERROR_IPSEC_BAD_SPI __MSABI_LONG(13910)
#define ERROR_IPSEC_SA_LIFETIME_EXPIRED __MSABI_LONG(13911)
#define ERROR_IPSEC_WRONG_SA __MSABI_LONG(13912)
#define ERROR_IPSEC_REPLAY_CHECK_FAILED __MSABI_LONG(13913)
#define ERROR_IPSEC_INVALID_PACKET __MSABI_LONG(13914)
#define ERROR_IPSEC_INTEGRITY_CHECK_FAILED __MSABI_LONG(13915)
#define ERROR_IPSEC_CLEAR_TEXT_DROP __MSABI_LONG(13916)
#define ERROR_IPSEC_AUTH_FIREWALL_DROP __MSABI_LONG(13917)
#define ERROR_IPSEC_THROTTLE_DROP __MSABI_LONG(13918)
#define ERROR_IPSEC_DOSP_BLOCK __MSABI_LONG(13925)
#define ERROR_IPSEC_DOSP_RECEIVED_MULTICAST __MSABI_LONG(13926)
#define ERROR_IPSEC_DOSP_INVALID_PACKET __MSABI_LONG(13927)
#define ERROR_IPSEC_DOSP_STATE_LOOKUP_FAILED __MSABI_LONG(13928)
#define ERROR_IPSEC_DOSP_MAX_ENTRIES __MSABI_LONG(13929)
#define ERROR_IPSEC_DOSP_KEYMOD_NOT_ALLOWED __MSABI_LONG(13930)
#define ERROR_IPSEC_DOSP_NOT_INSTALLED __MSABI_LONG(13931)
#define ERROR_IPSEC_DOSP_MAX_PER_IP_RATELIMIT_QUEUES __MSABI_LONG(13932)
#define ERROR_EVT_INVALID_CHANNEL_PATH __MSABI_LONG(15000)
#define ERROR_EVT_INVALID_QUERY __MSABI_LONG(15001)
#define ERROR_EVT_PUBLISHER_METADATA_NOT_FOUND __MSABI_LONG(15002)
#define ERROR_EVT_EVENT_TEMPLATE_NOT_FOUND __MSABI_LONG(15003)
#define ERROR_EVT_INVALID_PUBLISHER_NAME __MSABI_LONG(15004)
#define ERROR_EVT_INVALID_EVENT_DATA __MSABI_LONG(15005)
#define ERROR_EVT_CHANNEL_NOT_FOUND __MSABI_LONG(15007)
#define ERROR_EVT_MALFORMED_XML_TEXT __MSABI_LONG(15008)
#define ERROR_EVT_SUBSCRIPTION_TO_DIRECT_CHANNEL __MSABI_LONG(15009)
#define ERROR_EVT_CONFIGURATION_ERROR __MSABI_LONG(15010)
#define ERROR_EVT_QUERY_RESULT_STALE __MSABI_LONG(15011)
#define ERROR_EVT_QUERY_RESULT_INVALID_POSITION __MSABI_LONG(15012)
#define ERROR_EVT_NON_VALIDATING_MSXML __MSABI_LONG(15013)
#define ERROR_EVT_FILTER_ALREADYSCOPED __MSABI_LONG(15014)
#define ERROR_EVT_FILTER_NOTELTSET __MSABI_LONG(15015)
#define ERROR_EVT_FILTER_INVARG __MSABI_LONG(15016)
#define ERROR_EVT_FILTER_INVTEST __MSABI_LONG(15017)
#define ERROR_EVT_FILTER_INVTYPE __MSABI_LONG(15018)
#define ERROR_EVT_FILTER_PARSEERR __MSABI_LONG(15019)
#define ERROR_EVT_FILTER_UNSUPPORTEDOP __MSABI_LONG(15020)
#define ERROR_EVT_FILTER_UNEXPECTEDTOKEN __MSABI_LONG(15021)
#define ERROR_EVT_INVALID_OPERATION_OVER_ENABLED_DIRECT_CHANNEL __MSABI_LONG(15022)
#define ERROR_EVT_INVALID_CHANNEL_PROPERTY_VALUE __MSABI_LONG(15023)
#define ERROR_EVT_INVALID_PUBLISHER_PROPERTY_VALUE __MSABI_LONG(15024)
#define ERROR_EVT_CHANNEL_CANNOT_ACTIVATE __MSABI_LONG(15025)
#define ERROR_EVT_FILTER_TOO_COMPLEX __MSABI_LONG(15026)
#define ERROR_EVT_MESSAGE_NOT_FOUND __MSABI_LONG(15027)
#define ERROR_EVT_MESSAGE_ID_NOT_FOUND __MSABI_LONG(15028)
#define ERROR_EVT_UNRESOLVED_VALUE_INSERT __MSABI_LONG(15029)
#define ERROR_EVT_UNRESOLVED_PARAMETER_INSERT __MSABI_LONG(15030)
#define ERROR_EVT_MAX_INSERTS_REACHED __MSABI_LONG(15031)
#define ERROR_EVT_EVENT_DEFINITION_NOT_FOUND __MSABI_LONG(15032)
#define ERROR_EVT_MESSAGE_LOCALE_NOT_FOUND __MSABI_LONG(15033)
#define ERROR_EVT_VERSION_TOO_OLD __MSABI_LONG(15034)
#define ERROR_EVT_VERSION_TOO_NEW __MSABI_LONG(15035)
#define ERROR_EVT_CANNOT_OPEN_CHANNEL_OF_QUERY __MSABI_LONG(15036)
#define ERROR_EVT_PUBLISHER_DISABLED __MSABI_LONG(15037)
#define ERROR_EVT_FILTER_OUT_OF_RANGE __MSABI_LONG(15038)
#define ERROR_EC_SUBSCRIPTION_CANNOT_ACTIVATE __MSABI_LONG(15080)
#define ERROR_EC_LOG_DISABLED __MSABI_LONG(15081)
#define ERROR_EC_CIRCULAR_FORWARDING __MSABI_LONG(15082)
#define ERROR_EC_CREDSTORE_FULL __MSABI_LONG(15083)
#define ERROR_EC_CRED_NOT_FOUND __MSABI_LONG(15084)
#define ERROR_EC_NO_ACTIVE_CHANNEL __MSABI_LONG(15085)
#define ERROR_MUI_FILE_NOT_FOUND __MSABI_LONG(15100)
#define ERROR_MUI_INVALID_FILE __MSABI_LONG(15101)
#define ERROR_MUI_INVALID_RC_CONFIG __MSABI_LONG(15102)
#define ERROR_MUI_INVALID_LOCALE_NAME __MSABI_LONG(15103)
#define ERROR_MUI_INVALID_ULTIMATEFALLBACK_NAME __MSABI_LONG(15104)
#define ERROR_MUI_FILE_NOT_LOADED __MSABI_LONG(15105)
#define ERROR_RESOURCE_ENUM_USER_STOP __MSABI_LONG(15106)
#define ERROR_MUI_INTLSETTINGS_UILANG_NOT_INSTALLED __MSABI_LONG(15107)
#define ERROR_MUI_INTLSETTINGS_INVALID_LOCALE_NAME __MSABI_LONG(15108)
#define ERROR_MRM_RUNTIME_NO_DEFAULT_OR_NEUTRAL_RESOURCE __MSABI_LONG(15110)
#define ERROR_MRM_INVALID_PRICONFIG __MSABI_LONG(15111)
#define ERROR_MRM_INVALID_FILE_TYPE __MSABI_LONG(15112)
#define ERROR_MRM_UNKNOWN_QUALIFIER __MSABI_LONG(15113)
#define ERROR_MRM_INVALID_QUALIFIER_VALUE __MSABI_LONG(15114)
#define ERROR_MRM_NO_CANDIDATE __MSABI_LONG(15115)
#define ERROR_MRM_NO_MATCH_OR_DEFAULT_CANDIDATE __MSABI_LONG(15116)
#define ERROR_MRM_RESOURCE_TYPE_MISMATCH __MSABI_LONG(15117)
#define ERROR_MRM_DUPLICATE_MAP_NAME __MSABI_LONG(15118)
#define ERROR_MRM_DUPLICATE_ENTRY __MSABI_LONG(15119)
#define ERROR_MRM_INVALID_RESOURCE_IDENTIFIER __MSABI_LONG(15120)
#define ERROR_MRM_FILEPATH_TOO_LONG __MSABI_LONG(15121)
#define ERROR_MRM_UNSUPPORTED_DIRECTORY_TYPE __MSABI_LONG(15122)
#define ERROR_MRM_INVALID_PRI_FILE __MSABI_LONG(15126)
#define ERROR_MRM_NAMED_RESOURCE_NOT_FOUND __MSABI_LONG(15127)
#define ERROR_MRM_MAP_NOT_FOUND __MSABI_LONG(15135)
#define ERROR_MRM_UNSUPPORTED_PROFILE_TYPE __MSABI_LONG(15136)
#define ERROR_MRM_INVALID_QUALIFIER_OPERATOR __MSABI_LONG(15137)
#define ERROR_MRM_INDETERMINATE_QUALIFIER_VALUE __MSABI_LONG(15138)
#define ERROR_MRM_AUTOMERGE_ENABLED __MSABI_LONG(15139)
#define ERROR_MRM_TOO_MANY_RESOURCES __MSABI_LONG(15140)
#define ERROR_MCA_INVALID_CAPABILITIES_STRING __MSABI_LONG(15200)
#define ERROR_MCA_INVALID_VCP_VERSION __MSABI_LONG(15201)
#define ERROR_MCA_MONITOR_VIOLATES_MCCS_SPECIFICATION __MSABI_LONG(15202)
#define ERROR_MCA_MCCS_VERSION_MISMATCH __MSABI_LONG(15203)
#define ERROR_MCA_UNSUPPORTED_MCCS_VERSION __MSABI_LONG(15204)
#define ERROR_MCA_INTERNAL_ERROR __MSABI_LONG(15205)
#define ERROR_MCA_INVALID_TECHNOLOGY_TYPE_RETURNED __MSABI_LONG(15206)
#define ERROR_MCA_UNSUPPORTED_COLOR_TEMPERATURE __MSABI_LONG(15207)
#define ERROR_AMBIGUOUS_SYSTEM_DEVICE __MSABI_LONG(15250)
#define ERROR_SYSTEM_DEVICE_NOT_FOUND __MSABI_LONG(15299)
#define ERROR_HASH_NOT_SUPPORTED __MSABI_LONG(15300)
#define ERROR_HASH_NOT_PRESENT __MSABI_LONG(15301)
#define ERROR_SECONDARY_IC_PROVIDER_NOT_REGISTERED __MSABI_LONG(15321)
#define ERROR_GPIO_CLIENT_INFORMATION_INVALID __MSABI_LONG(15322)
#define ERROR_GPIO_VERSION_NOT_SUPPORTED __MSABI_LONG(15323)
#define ERROR_GPIO_INVALID_REGISTRATION_PACKET __MSABI_LONG(15324)
#define ERROR_GPIO_OPERATION_DENIED __MSABI_LONG(15325)
#define ERROR_GPIO_INCOMPATIBLE_CONNECT_MODE __MSABI_LONG(15326)
#define ERROR_GPIO_INTERRUPT_ALREADY_UNMASKED __MSABI_LONG(15327)
#define ERROR_CANNOT_SWITCH_RUNLEVEL __MSABI_LONG(15400)
#define ERROR_INVALID_RUNLEVEL_SETTING __MSABI_LONG(15401)
#define ERROR_RUNLEVEL_SWITCH_TIMEOUT __MSABI_LONG(15402)
#define ERROR_RUNLEVEL_SWITCH_AGENT_TIMEOUT __MSABI_LONG(15403)
#define ERROR_RUNLEVEL_SWITCH_IN_PROGRESS __MSABI_LONG(15404)
#define ERROR_SERVICES_FAILED_AUTOSTART __MSABI_LONG(15405)
#define ERROR_COM_TASK_STOP_PENDING __MSABI_LONG(15501)
#define ERROR_INSTALL_OPEN_PACKAGE_FAILED __MSABI_LONG(15600)
#define ERROR_INSTALL_PACKAGE_NOT_FOUND __MSABI_LONG(15601)
#define ERROR_INSTALL_INVALID_PACKAGE __MSABI_LONG(15602)
#define ERROR_INSTALL_RESOLVE_DEPENDENCY_FAILED __MSABI_LONG(15603)
#define ERROR_INSTALL_OUT_OF_DISK_SPACE __MSABI_LONG(15604)
#define ERROR_INSTALL_NETWORK_FAILURE __MSABI_LONG(15605)
#define ERROR_INSTALL_REGISTRATION_FAILURE __MSABI_LONG(15606)
#define ERROR_INSTALL_DEREGISTRATION_FAILURE __MSABI_LONG(15607)
#define ERROR_INSTALL_CANCEL __MSABI_LONG(15608)
#define ERROR_INSTALL_FAILED __MSABI_LONG(15609)
#define ERROR_REMOVE_FAILED __MSABI_LONG(15610)
#define ERROR_PACKAGE_ALREADY_EXISTS __MSABI_LONG(15611)
#define ERROR_NEEDS_REMEDIATION __MSABI_LONG(15612)
#define ERROR_INSTALL_PREREQUISITE_FAILED __MSABI_LONG(15613)
#define ERROR_PACKAGE_REPOSITORY_CORRUPTED __MSABI_LONG(15614)
#define ERROR_INSTALL_POLICY_FAILURE __MSABI_LONG(15615)
#define ERROR_PACKAGE_UPDATING __MSABI_LONG(15616)
#define ERROR_DEPLOYMENT_BLOCKED_BY_POLICY __MSABI_LONG(15617)
#define ERROR_PACKAGES_IN_USE __MSABI_LONG(15618)
#define ERROR_RECOVERY_FILE_CORRUPT __MSABI_LONG(15619)
#define ERROR_INVALID_STAGED_SIGNATURE __MSABI_LONG(15620)
#define ERROR_DELETING_EXISTING_APPLICATIONDATA_STORE_FAILED __MSABI_LONG(15621)
#define ERROR_INSTALL_PACKAGE_DOWNGRADE __MSABI_LONG(15622)
#define ERROR_SYSTEM_NEEDS_REMEDIATION __MSABI_LONG(15623)
#define ERROR_APPX_INTEGRITY_FAILURE_CLR_NGEN __MSABI_LONG(15624)
#define ERROR_RESILIENCY_FILE_CORRUPT __MSABI_LONG(15625)
#define ERROR_INSTALL_FIREWALL_SERVICE_NOT_RUNNING __MSABI_LONG(15626)
#define APPMODEL_ERROR_NO_PACKAGE __MSABI_LONG(15700)
#define APPMODEL_ERROR_PACKAGE_RUNTIME_CORRUPT __MSABI_LONG(15701)
#define APPMODEL_ERROR_PACKAGE_IDENTITY_CORRUPT __MSABI_LONG(15702)
#define APPMODEL_ERROR_NO_APPLICATION __MSABI_LONG(15703)
#define ERROR_STATE_LOAD_STORE_FAILED __MSABI_LONG(15800)
#define ERROR_STATE_GET_VERSION_FAILED __MSABI_LONG(15801)
#define ERROR_STATE_SET_VERSION_FAILED __MSABI_LONG(15802)
#define ERROR_STATE_STRUCTURED_RESET_FAILED __MSABI_LONG(15803)
#define ERROR_STATE_OPEN_CONTAINER_FAILED __MSABI_LONG(15804)
#define ERROR_STATE_CREATE_CONTAINER_FAILED __MSABI_LONG(15805)
#define ERROR_STATE_DELETE_CONTAINER_FAILED __MSABI_LONG(15806)
#define ERROR_STATE_READ_SETTING_FAILED __MSABI_LONG(15807)
#define ERROR_STATE_WRITE_SETTING_FAILED __MSABI_LONG(15808)
#define ERROR_STATE_DELETE_SETTING_FAILED __MSABI_LONG(15809)
#define ERROR_STATE_QUERY_SETTING_FAILED __MSABI_LONG(15810)
#define ERROR_STATE_READ_COMPOSITE_SETTING_FAILED __MSABI_LONG(15811)
#define ERROR_STATE_WRITE_COMPOSITE_SETTING_FAILED __MSABI_LONG(15812)
#define ERROR_STATE_ENUMERATE_CONTAINER_FAILED __MSABI_LONG(15813)
#define ERROR_STATE_ENUMERATE_SETTINGS_FAILED __MSABI_LONG(15814)
#define ERROR_STATE_COMPOSITE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED __MSABI_LONG(15815)
#define ERROR_STATE_SETTING_VALUE_SIZE_LIMIT_EXCEEDED __MSABI_LONG(15816)
#define ERROR_STATE_SETTING_NAME_SIZE_LIMIT_EXCEEDED __MSABI_LONG(15817)
#define ERROR_STATE_CONTAINER_NAME_SIZE_LIMIT_EXCEEDED __MSABI_LONG(15818)
#define ERROR_API_UNAVAILABLE __MSABI_LONG(15841)
#define SEVERITY_SUCCESS 0
#define SEVERITY_ERROR 1
#define SUCCEEDED(hr) ((HRESULT)(hr) >= 0)
#define FAILED(hr) ((HRESULT)(hr) < 0)
#define IS_ERROR(Status) ((unsigned __LONG32)(Status) >> 31==SEVERITY_ERROR)
#define HRESULT_CODE(hr) ((hr) & 0xFFFF)
#define SCODE_CODE(sc) ((sc) & 0xFFFF)
#define HRESULT_FACILITY(hr) (((hr) >> 16) & 0x1fff)
#define SCODE_FACILITY(sc) (((sc) >> 16) & 0x1fff)
#define HRESULT_SEVERITY(hr) (((hr) >> 31) & 0x1)
#define SCODE_SEVERITY(sc) (((sc) >> 31) & 0x1)
#define MAKE_HRESULT(sev,fac,code) ((HRESULT) (((unsigned __LONG32)(sev)<<31) | ((unsigned __LONG32)(fac)<<16) | ((unsigned __LONG32)(code))))
#define MAKE_SCODE(sev,fac,code) ((SCODE) (((unsigned __LONG32)(sev)<<31) | ((unsigned __LONG32)(fac)<<16) | ((unsigned __LONG32)(code))))
#define FACILITY_NT_BIT 0x10000000
#define __HRESULT_FROM_WIN32(x) ((HRESULT)(x) <= 0 ? ((HRESULT)(x)) : ((HRESULT) (((x) & 0x0000FFFF) | (FACILITY_WIN32 << 16) | 0x80000000)))
#ifdef INLINE_HRESULT_FROM_WIN32
#ifndef _HRESULT_DEFINED
#define _HRESULT_DEFINED
typedef __LONG32 HRESULT;
#endif
#ifndef __CRT__NO_INLINE
__CRT_INLINE HRESULT HRESULT_FROM_WIN32(__LONG32 x) { return x <= 0 ? (HRESULT)x : (HRESULT) (((x) & 0x0000FFFF) | (FACILITY_WIN32 << 16) | 0x80000000);}
#endif /* !__CRT__NO_INLINE */
#else
#define HRESULT_FROM_WIN32(x) __HRESULT_FROM_WIN32(x)
#endif
#define HRESULT_FROM_NT(x) ((HRESULT) ((x) | FACILITY_NT_BIT))
#define GetScode(hr) ((SCODE) (hr))
#define ResultFromScode(sc) ((HRESULT) (sc))
#define PropagateResult(hrPrevious,scBase) ((HRESULT) scBase)
#if defined (RC_INVOKED) || defined (__WIDL__)
#define _HRESULT_TYPEDEF_(_sc) _sc
#else
#define _HRESULT_TYPEDEF_(_sc) ((HRESULT)_sc)
#endif
#define NOERROR 0
#define E_UNEXPECTED _HRESULT_TYPEDEF_(0x8000FFFF)
#define E_NOTIMPL _HRESULT_TYPEDEF_(0x80004001)
#define E_OUTOFMEMORY _HRESULT_TYPEDEF_(0x8007000E)
#define E_INVALIDARG _HRESULT_TYPEDEF_(0x80070057)
#define E_NOINTERFACE _HRESULT_TYPEDEF_(0x80004002)
#define E_POINTER _HRESULT_TYPEDEF_(0x80004003)
#define E_HANDLE _HRESULT_TYPEDEF_(0x80070006)
#define E_ABORT _HRESULT_TYPEDEF_(0x80004004)
#define E_FAIL _HRESULT_TYPEDEF_(0x80004005)
#define E_ACCESSDENIED _HRESULT_TYPEDEF_(0x80070005)
#define E_PENDING _HRESULT_TYPEDEF_(0x8000000A)
#define E_BOUNDS _HRESULT_TYPEDEF_(0x8000000B)
#define E_NOT_SET HRESULT_FROM_WIN32(ERROR_NOT_FOUND)
#define E_NOT_VALID_STATE HRESULT_FROM_WIN32(ERROR_INVALID_STATE)
#define E_NOT_SUFFICIENT_BUFFER HRESULT_FROM_WIN32(ERROR_INSUFFICIENT_BUFFER)
#define E_TIME_SENSITIVE_THREAD HRESULT_FROM_WIN32(ERROR_TIME_SENSITIVE_THREAD)
#define E_NO_TASK_QUEUE HRESULT_FROM_WIN32(ERROR_NO_TASK_QUEUE)
#define CO_E_INIT_TLS _HRESULT_TYPEDEF_(0x80004006)
#define CO_E_INIT_SHARED_ALLOCATOR _HRESULT_TYPEDEF_(0x80004007)
#define CO_E_INIT_MEMORY_ALLOCATOR _HRESULT_TYPEDEF_(0x80004008)
#define CO_E_INIT_CLASS_CACHE _HRESULT_TYPEDEF_(0x80004009)
#define CO_E_INIT_RPC_CHANNEL _HRESULT_TYPEDEF_(0x8000400A)
#define CO_E_INIT_TLS_SET_CHANNEL_CONTROL _HRESULT_TYPEDEF_(0x8000400B)
#define CO_E_INIT_TLS_CHANNEL_CONTROL _HRESULT_TYPEDEF_(0x8000400C)
#define CO_E_INIT_UNACCEPTED_USER_ALLOCATOR _HRESULT_TYPEDEF_(0x8000400D)
#define CO_E_INIT_SCM_MUTEX_EXISTS _HRESULT_TYPEDEF_(0x8000400E)
#define CO_E_INIT_SCM_FILE_MAPPING_EXISTS _HRESULT_TYPEDEF_(0x8000400F)
#define CO_E_INIT_SCM_MAP_VIEW_OF_FILE _HRESULT_TYPEDEF_(0x80004010)
#define CO_E_INIT_SCM_EXEC_FAILURE _HRESULT_TYPEDEF_(0x80004011)
#define CO_E_INIT_ONLY_SINGLE_THREADED _HRESULT_TYPEDEF_(0x80004012)
#define CO_E_CANT_REMOTE _HRESULT_TYPEDEF_(0x80004013)
#define CO_E_BAD_SERVER_NAME _HRESULT_TYPEDEF_(0x80004014)
#define CO_E_WRONG_SERVER_IDENTITY _HRESULT_TYPEDEF_(0x80004015)
#define CO_E_OLE1DDE_DISABLED _HRESULT_TYPEDEF_(0x80004016)
#define CO_E_RUNAS_SYNTAX _HRESULT_TYPEDEF_(0x80004017)
#define CO_E_CREATEPROCESS_FAILURE _HRESULT_TYPEDEF_(0x80004018)
#define CO_E_RUNAS_CREATEPROCESS_FAILURE _HRESULT_TYPEDEF_(0x80004019)
#define CO_E_RUNAS_LOGON_FAILURE _HRESULT_TYPEDEF_(0x8000401A)
#define CO_E_LAUNCH_PERMSSION_DENIED _HRESULT_TYPEDEF_(0x8000401B)
#define CO_E_START_SERVICE_FAILURE _HRESULT_TYPEDEF_(0x8000401C)
#define CO_E_REMOTE_COMMUNICATION_FAILURE _HRESULT_TYPEDEF_(0x8000401D)
#define CO_E_SERVER_START_TIMEOUT _HRESULT_TYPEDEF_(0x8000401E)
#define CO_E_CLSREG_INCONSISTENT _HRESULT_TYPEDEF_(0x8000401F)
#define CO_E_IIDREG_INCONSISTENT _HRESULT_TYPEDEF_(0x80004020)
#define CO_E_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x80004021)
#define CO_E_RELOAD_DLL _HRESULT_TYPEDEF_(0x80004022)
#define CO_E_MSI_ERROR _HRESULT_TYPEDEF_(0x80004023)
#define CO_E_ATTEMPT_TO_CREATE_OUTSIDE_CLIENT_CONTEXT _HRESULT_TYPEDEF_(0x80004024)
#define CO_E_SERVER_PAUSED _HRESULT_TYPEDEF_(0x80004025)
#define CO_E_SERVER_NOT_PAUSED _HRESULT_TYPEDEF_(0x80004026)
#define CO_E_CLASS_DISABLED _HRESULT_TYPEDEF_(0x80004027)
#define CO_E_CLRNOTAVAILABLE _HRESULT_TYPEDEF_(0x80004028)
#define CO_E_ASYNC_WORK_REJECTED _HRESULT_TYPEDEF_(0x80004029)
#define CO_E_SERVER_INIT_TIMEOUT _HRESULT_TYPEDEF_(0x8000402A)
#define CO_E_NO_SECCTX_IN_ACTIVATE _HRESULT_TYPEDEF_(0x8000402B)
#define CO_E_TRACKER_CONFIG _HRESULT_TYPEDEF_(0x80004030)
#define CO_E_THREADPOOL_CONFIG _HRESULT_TYPEDEF_(0x80004031)
#define CO_E_SXS_CONFIG _HRESULT_TYPEDEF_(0x80004032)
#define CO_E_MALFORMED_SPN _HRESULT_TYPEDEF_(0x80004033)
#define S_OK ((HRESULT)0x00000000)
#define S_FALSE ((HRESULT)0x00000001)
#define OLE_E_FIRST ((HRESULT)0x80040000)
#define OLE_E_LAST ((HRESULT)0x800400FF)
#define OLE_S_FIRST ((HRESULT)0x00040000)
#define OLE_S_LAST ((HRESULT)0x000400FF)
#define OLE_E_OLEVERB _HRESULT_TYPEDEF_(0x80040000)
#define OLE_E_ADVF _HRESULT_TYPEDEF_(0x80040001)
#define OLE_E_ENUM_NOMORE _HRESULT_TYPEDEF_(0x80040002)
#define OLE_E_ADVISENOTSUPPORTED _HRESULT_TYPEDEF_(0x80040003)
#define OLE_E_NOCONNECTION _HRESULT_TYPEDEF_(0x80040004)
#define OLE_E_NOTRUNNING _HRESULT_TYPEDEF_(0x80040005)
#define OLE_E_NOCACHE _HRESULT_TYPEDEF_(0x80040006)
#define OLE_E_BLANK _HRESULT_TYPEDEF_(0x80040007)
#define OLE_E_CLASSDIFF _HRESULT_TYPEDEF_(0x80040008)
#define OLE_E_CANT_GETMONIKER _HRESULT_TYPEDEF_(0x80040009)
#define OLE_E_CANT_BINDTOSOURCE _HRESULT_TYPEDEF_(0x8004000A)
#define OLE_E_STATIC _HRESULT_TYPEDEF_(0x8004000B)
#define OLE_E_PROMPTSAVECANCELLED _HRESULT_TYPEDEF_(0x8004000C)
#define OLE_E_INVALIDRECT _HRESULT_TYPEDEF_(0x8004000D)
#define OLE_E_WRONGCOMPOBJ _HRESULT_TYPEDEF_(0x8004000E)
#define OLE_E_INVALIDHWND _HRESULT_TYPEDEF_(0x8004000F)
#define OLE_E_NOT_INPLACEACTIVE _HRESULT_TYPEDEF_(0x80040010)
#define OLE_E_CANTCONVERT _HRESULT_TYPEDEF_(0x80040011)
#define OLE_E_NOSTORAGE _HRESULT_TYPEDEF_(0x80040012)
#define DV_E_FORMATETC _HRESULT_TYPEDEF_(0x80040064)
#define DV_E_DVTARGETDEVICE _HRESULT_TYPEDEF_(0x80040065)
#define DV_E_STGMEDIUM _HRESULT_TYPEDEF_(0x80040066)
#define DV_E_STATDATA _HRESULT_TYPEDEF_(0x80040067)
#define DV_E_LINDEX _HRESULT_TYPEDEF_(0x80040068)
#define DV_E_TYMED _HRESULT_TYPEDEF_(0x80040069)
#define DV_E_CLIPFORMAT _HRESULT_TYPEDEF_(0x8004006A)
#define DV_E_DVASPECT _HRESULT_TYPEDEF_(0x8004006B)
#define DV_E_DVTARGETDEVICE_SIZE _HRESULT_TYPEDEF_(0x8004006C)
#define DV_E_NOIVIEWOBJECT _HRESULT_TYPEDEF_(0x8004006D)
#define DRAGDROP_E_FIRST __MSABI_LONG(0x80040100)
#define DRAGDROP_E_LAST __MSABI_LONG(0x8004010F)
#define DRAGDROP_S_FIRST __MSABI_LONG(0x00040100)
#define DRAGDROP_S_LAST __MSABI_LONG(0x0004010F)
#define DRAGDROP_E_NOTREGISTERED _HRESULT_TYPEDEF_(0x80040100)
#define DRAGDROP_E_ALREADYREGISTERED _HRESULT_TYPEDEF_(0x80040101)
#define DRAGDROP_E_INVALIDHWND _HRESULT_TYPEDEF_(0x80040102)
#define CLASSFACTORY_E_FIRST __MSABI_LONG(0x80040110)
#define CLASSFACTORY_E_LAST __MSABI_LONG(0x8004011F)
#define CLASSFACTORY_S_FIRST __MSABI_LONG(0x00040110)
#define CLASSFACTORY_S_LAST __MSABI_LONG(0x0004011F)
#define CLASS_E_NOAGGREGATION _HRESULT_TYPEDEF_(0x80040110)
#define CLASS_E_CLASSNOTAVAILABLE _HRESULT_TYPEDEF_(0x80040111)
#define CLASS_E_NOTLICENSED _HRESULT_TYPEDEF_(0x80040112)
#define MARSHAL_E_FIRST __MSABI_LONG(0x80040120)
#define MARSHAL_E_LAST __MSABI_LONG(0x8004012F)
#define MARSHAL_S_FIRST __MSABI_LONG(0x00040120)
#define MARSHAL_S_LAST __MSABI_LONG(0x0004012F)
#define DATA_E_FIRST __MSABI_LONG(0x80040130)
#define DATA_E_LAST __MSABI_LONG(0x8004013F)
#define DATA_S_FIRST __MSABI_LONG(0x00040130)
#define DATA_S_LAST __MSABI_LONG(0x0004013F)
#define VIEW_E_FIRST __MSABI_LONG(0x80040140)
#define VIEW_E_LAST __MSABI_LONG(0x8004014F)
#define VIEW_S_FIRST __MSABI_LONG(0x00040140)
#define VIEW_S_LAST __MSABI_LONG(0x0004014F)
#define VIEW_E_DRAW _HRESULT_TYPEDEF_(0x80040140)
#define REGDB_E_FIRST __MSABI_LONG(0x80040150)
#define REGDB_E_LAST __MSABI_LONG(0x8004015F)
#define REGDB_S_FIRST __MSABI_LONG(0x00040150)
#define REGDB_S_LAST __MSABI_LONG(0x0004015F)
#define REGDB_E_READREGDB _HRESULT_TYPEDEF_(0x80040150)
#define REGDB_E_WRITEREGDB _HRESULT_TYPEDEF_(0x80040151)
#define REGDB_E_KEYMISSING _HRESULT_TYPEDEF_(0x80040152)
#define REGDB_E_INVALIDVALUE _HRESULT_TYPEDEF_(0x80040153)
#define REGDB_E_CLASSNOTREG _HRESULT_TYPEDEF_(0x80040154)
#define REGDB_E_IIDNOTREG _HRESULT_TYPEDEF_(0x80040155)
#define REGDB_E_BADTHREADINGMODEL _HRESULT_TYPEDEF_(0x80040156)
#define CAT_E_FIRST __MSABI_LONG(0x80040160)
#define CAT_E_LAST __MSABI_LONG(0x80040161)
#define CAT_E_CATIDNOEXIST _HRESULT_TYPEDEF_(0x80040160)
#define CAT_E_NODESCRIPTION _HRESULT_TYPEDEF_(0x80040161)
#define CS_E_FIRST __MSABI_LONG(0x80040164)
#define CS_E_LAST __MSABI_LONG(0x8004016F)
#define CS_E_PACKAGE_NOTFOUND _HRESULT_TYPEDEF_(0x80040164)
#define CS_E_NOT_DELETABLE _HRESULT_TYPEDEF_(0x80040165)
#define CS_E_CLASS_NOTFOUND _HRESULT_TYPEDEF_(0x80040166)
#define CS_E_INVALID_VERSION _HRESULT_TYPEDEF_(0x80040167)
#define CS_E_NO_CLASSSTORE _HRESULT_TYPEDEF_(0x80040168)
#define CS_E_OBJECT_NOTFOUND _HRESULT_TYPEDEF_(0x80040169)
#define CS_E_OBJECT_ALREADY_EXISTS _HRESULT_TYPEDEF_(0x8004016A)
#define CS_E_INVALID_PATH _HRESULT_TYPEDEF_(0x8004016B)
#define CS_E_NETWORK_ERROR _HRESULT_TYPEDEF_(0x8004016C)
#define CS_E_ADMIN_LIMIT_EXCEEDED _HRESULT_TYPEDEF_(0x8004016D)
#define CS_E_SCHEMA_MISMATCH _HRESULT_TYPEDEF_(0x8004016E)
#define CS_E_INTERNAL_ERROR _HRESULT_TYPEDEF_(0x8004016F)
#define CACHE_E_FIRST __MSABI_LONG(0x80040170)
#define CACHE_E_LAST __MSABI_LONG(0x8004017F)
#define CACHE_S_FIRST __MSABI_LONG(0x00040170)
#define CACHE_S_LAST __MSABI_LONG(0x0004017F)
#define CACHE_E_NOCACHE_UPDATED _HRESULT_TYPEDEF_(0x80040170)
#define OLEOBJ_E_FIRST __MSABI_LONG(0x80040180)
#define OLEOBJ_E_LAST __MSABI_LONG(0x8004018F)
#define OLEOBJ_S_FIRST __MSABI_LONG(0x00040180)
#define OLEOBJ_S_LAST __MSABI_LONG(0x0004018F)
#define OLEOBJ_E_NOVERBS _HRESULT_TYPEDEF_(0x80040180)
#define OLEOBJ_E_INVALIDVERB _HRESULT_TYPEDEF_(0x80040181)
#define CLIENTSITE_E_FIRST __MSABI_LONG(0x80040190)
#define CLIENTSITE_E_LAST __MSABI_LONG(0x8004019F)
#define CLIENTSITE_S_FIRST __MSABI_LONG(0x00040190)
#define CLIENTSITE_S_LAST __MSABI_LONG(0x0004019F)
#define INPLACE_E_NOTUNDOABLE _HRESULT_TYPEDEF_(0x800401A0)
#define INPLACE_E_NOTOOLSPACE _HRESULT_TYPEDEF_(0x800401A1)
#define INPLACE_E_FIRST __MSABI_LONG(0x800401A0)
#define INPLACE_E_LAST __MSABI_LONG(0x800401AF)
#define INPLACE_S_FIRST __MSABI_LONG(0x000401A0)
#define INPLACE_S_LAST __MSABI_LONG(0x000401AF)
#define ENUM_E_FIRST __MSABI_LONG(0x800401B0)
#define ENUM_E_LAST __MSABI_LONG(0x800401BF)
#define ENUM_S_FIRST __MSABI_LONG(0x000401B0)
#define ENUM_S_LAST __MSABI_LONG(0x000401BF)
#define CONVERT10_E_FIRST __MSABI_LONG(0x800401C0)
#define CONVERT10_E_LAST __MSABI_LONG(0x800401CF)
#define CONVERT10_S_FIRST __MSABI_LONG(0x000401C0)
#define CONVERT10_S_LAST __MSABI_LONG(0x000401CF)
#define CONVERT10_E_OLESTREAM_GET _HRESULT_TYPEDEF_(0x800401C0)
#define CONVERT10_E_OLESTREAM_PUT _HRESULT_TYPEDEF_(0x800401C1)
#define CONVERT10_E_OLESTREAM_FMT _HRESULT_TYPEDEF_(0x800401C2)
#define CONVERT10_E_OLESTREAM_BITMAP_TO_DIB _HRESULT_TYPEDEF_(0x800401C3)
#define CONVERT10_E_STG_FMT _HRESULT_TYPEDEF_(0x800401C4)
#define CONVERT10_E_STG_NO_STD_STREAM _HRESULT_TYPEDEF_(0x800401C5)
#define CONVERT10_E_STG_DIB_TO_BITMAP _HRESULT_TYPEDEF_(0x800401C6)
#define CLIPBRD_E_FIRST __MSABI_LONG(0x800401D0)
#define CLIPBRD_E_LAST __MSABI_LONG(0x800401DF)
#define CLIPBRD_S_FIRST __MSABI_LONG(0x000401D0)
#define CLIPBRD_S_LAST __MSABI_LONG(0x000401DF)
#define CLIPBRD_E_CANT_OPEN _HRESULT_TYPEDEF_(0x800401D0)
#define CLIPBRD_E_CANT_EMPTY _HRESULT_TYPEDEF_(0x800401D1)
#define CLIPBRD_E_CANT_SET _HRESULT_TYPEDEF_(0x800401D2)
#define CLIPBRD_E_BAD_DATA _HRESULT_TYPEDEF_(0x800401D3)
#define CLIPBRD_E_CANT_CLOSE _HRESULT_TYPEDEF_(0x800401D4)
#define MK_E_FIRST __MSABI_LONG(0x800401E0)
#define MK_E_LAST __MSABI_LONG(0x800401EF)
#define MK_S_FIRST __MSABI_LONG(0x000401E0)
#define MK_S_LAST __MSABI_LONG(0x000401EF)
#define MK_E_CONNECTMANUALLY _HRESULT_TYPEDEF_(0x800401E0)
#define MK_E_EXCEEDEDDEADLINE _HRESULT_TYPEDEF_(0x800401E1)
#define MK_E_NEEDGENERIC _HRESULT_TYPEDEF_(0x800401E2)
#define MK_E_UNAVAILABLE _HRESULT_TYPEDEF_(0x800401E3)
#define MK_E_SYNTAX _HRESULT_TYPEDEF_(0x800401E4)
#define MK_E_NOOBJECT _HRESULT_TYPEDEF_(0x800401E5)
#define MK_E_INVALIDEXTENSION _HRESULT_TYPEDEF_(0x800401E6)
#define MK_E_INTERMEDIATEINTERFACENOTSUPPORTED _HRESULT_TYPEDEF_(0x800401E7)
#define MK_E_NOTBINDABLE _HRESULT_TYPEDEF_(0x800401E8)
#define MK_E_NOTBOUND _HRESULT_TYPEDEF_(0x800401E9)
#define MK_E_CANTOPENFILE _HRESULT_TYPEDEF_(0x800401EA)
#define MK_E_MUSTBOTHERUSER _HRESULT_TYPEDEF_(0x800401EB)
#define MK_E_NOINVERSE _HRESULT_TYPEDEF_(0x800401EC)
#define MK_E_NOSTORAGE _HRESULT_TYPEDEF_(0x800401ED)
#define MK_E_NOPREFIX _HRESULT_TYPEDEF_(0x800401EE)
#define MK_E_ENUMERATION_FAILED _HRESULT_TYPEDEF_(0x800401EF)
#define CO_E_FIRST __MSABI_LONG(0x800401F0)
#define CO_E_LAST __MSABI_LONG(0x800401FF)
#define CO_S_FIRST __MSABI_LONG(0x000401F0)
#define CO_S_LAST __MSABI_LONG(0x000401FF)
#define CO_E_NOTINITIALIZED _HRESULT_TYPEDEF_(0x800401F0)
#define CO_E_ALREADYINITIALIZED _HRESULT_TYPEDEF_(0x800401F1)
#define CO_E_CANTDETERMINECLASS _HRESULT_TYPEDEF_(0x800401F2)
#define CO_E_CLASSSTRING _HRESULT_TYPEDEF_(0x800401F3)
#define CO_E_IIDSTRING _HRESULT_TYPEDEF_(0x800401F4)
#define CO_E_APPNOTFOUND _HRESULT_TYPEDEF_(0x800401F5)
#define CO_E_APPSINGLEUSE _HRESULT_TYPEDEF_(0x800401F6)
#define CO_E_ERRORINAPP _HRESULT_TYPEDEF_(0x800401F7)
#define CO_E_DLLNOTFOUND _HRESULT_TYPEDEF_(0x800401F8)
#define CO_E_ERRORINDLL _HRESULT_TYPEDEF_(0x800401F9)
#define CO_E_WRONGOSFORAPP _HRESULT_TYPEDEF_(0x800401FA)
#define CO_E_OBJNOTREG _HRESULT_TYPEDEF_(0x800401FB)
#define CO_E_OBJISREG _HRESULT_TYPEDEF_(0x800401FC)
#define CO_E_OBJNOTCONNECTED _HRESULT_TYPEDEF_(0x800401FD)
#define CO_E_APPDIDNTREG _HRESULT_TYPEDEF_(0x800401FE)
#define CO_E_RELEASED _HRESULT_TYPEDEF_(0x800401FF)
#define EVENT_E_FIRST __MSABI_LONG(0x80040200)
#define EVENT_E_LAST __MSABI_LONG(0x8004021F)
#define EVENT_S_FIRST __MSABI_LONG(0x00040200)
#define EVENT_S_LAST __MSABI_LONG(0x0004021F)
#define EVENT_S_SOME_SUBSCRIBERS_FAILED _HRESULT_TYPEDEF_(0x00040200)
#define EVENT_E_ALL_SUBSCRIBERS_FAILED _HRESULT_TYPEDEF_(0x80040201)
#define EVENT_S_NOSUBSCRIBERS _HRESULT_TYPEDEF_(0x00040202)
#define EVENT_E_QUERYSYNTAX _HRESULT_TYPEDEF_(0x80040203)
#define EVENT_E_QUERYFIELD _HRESULT_TYPEDEF_(0x80040204)
#define EVENT_E_INTERNALEXCEPTION _HRESULT_TYPEDEF_(0x80040205)
#define EVENT_E_INTERNALERROR _HRESULT_TYPEDEF_(0x80040206)
#define EVENT_E_INVALID_PER_USER_SID _HRESULT_TYPEDEF_(0x80040207)
#define EVENT_E_USER_EXCEPTION _HRESULT_TYPEDEF_(0x80040208)
#define EVENT_E_TOO_MANY_METHODS _HRESULT_TYPEDEF_(0x80040209)
#define EVENT_E_MISSING_EVENTCLASS _HRESULT_TYPEDEF_(0x8004020A)
#define EVENT_E_NOT_ALL_REMOVED _HRESULT_TYPEDEF_(0x8004020B)
#define EVENT_E_COMPLUS_NOT_INSTALLED _HRESULT_TYPEDEF_(0x8004020C)
#define EVENT_E_CANT_MODIFY_OR_DELETE_UNCONFIGURED_OBJECT _HRESULT_TYPEDEF_(0x8004020D)
#define EVENT_E_CANT_MODIFY_OR_DELETE_CONFIGURED_OBJECT _HRESULT_TYPEDEF_(0x8004020E)
#define EVENT_E_INVALID_EVENT_CLASS_PARTITION _HRESULT_TYPEDEF_(0x8004020F)
#define EVENT_E_PER_USER_SID_NOT_LOGGED_ON _HRESULT_TYPEDEF_(0x80040210)
#define XACT_E_FIRST 0x8004D000
#define XACT_E_LAST 0x8004D029
#define XACT_S_FIRST 0x0004D000
#define XACT_S_LAST 0x0004D010
#define XACT_E_ALREADYOTHERSINGLEPHASE _HRESULT_TYPEDEF_(0x8004D000)
#define XACT_E_CANTRETAIN _HRESULT_TYPEDEF_(0x8004D001)
#define XACT_E_COMMITFAILED _HRESULT_TYPEDEF_(0x8004D002)
#define XACT_E_COMMITPREVENTED _HRESULT_TYPEDEF_(0x8004D003)
#define XACT_E_HEURISTICABORT _HRESULT_TYPEDEF_(0x8004D004)
#define XACT_E_HEURISTICCOMMIT _HRESULT_TYPEDEF_(0x8004D005)
#define XACT_E_HEURISTICDAMAGE _HRESULT_TYPEDEF_(0x8004D006)
#define XACT_E_HEURISTICDANGER _HRESULT_TYPEDEF_(0x8004D007)
#define XACT_E_ISOLATIONLEVEL _HRESULT_TYPEDEF_(0x8004D008)
#define XACT_E_NOASYNC _HRESULT_TYPEDEF_(0x8004D009)
#define XACT_E_NOENLIST _HRESULT_TYPEDEF_(0x8004D00A)
#define XACT_E_NOISORETAIN _HRESULT_TYPEDEF_(0x8004D00B)
#define XACT_E_NORESOURCE _HRESULT_TYPEDEF_(0x8004D00C)
#define XACT_E_NOTCURRENT _HRESULT_TYPEDEF_(0x8004D00D)
#define XACT_E_NOTRANSACTION _HRESULT_TYPEDEF_(0x8004D00E)
#define XACT_E_NOTSUPPORTED _HRESULT_TYPEDEF_(0x8004D00F)
#define XACT_E_UNKNOWNRMGRID _HRESULT_TYPEDEF_(0x8004D010)
#define XACT_E_WRONGSTATE _HRESULT_TYPEDEF_(0x8004D011)
#define XACT_E_WRONGUOW _HRESULT_TYPEDEF_(0x8004D012)
#define XACT_E_XTIONEXISTS _HRESULT_TYPEDEF_(0x8004D013)
#define XACT_E_NOIMPORTOBJECT _HRESULT_TYPEDEF_(0x8004D014)
#define XACT_E_INVALIDCOOKIE _HRESULT_TYPEDEF_(0x8004D015)
#define XACT_E_INDOUBT _HRESULT_TYPEDEF_(0x8004D016)
#define XACT_E_NOTIMEOUT _HRESULT_TYPEDEF_(0x8004D017)
#define XACT_E_ALREADYINPROGRESS _HRESULT_TYPEDEF_(0x8004D018)
#define XACT_E_ABORTED _HRESULT_TYPEDEF_(0x8004D019)
#define XACT_E_LOGFULL _HRESULT_TYPEDEF_(0x8004D01A)
#define XACT_E_TMNOTAVAILABLE _HRESULT_TYPEDEF_(0x8004D01B)
#define XACT_E_CONNECTION_DOWN _HRESULT_TYPEDEF_(0x8004D01C)
#define XACT_E_CONNECTION_DENIED _HRESULT_TYPEDEF_(0x8004D01D)
#define XACT_E_REENLISTTIMEOUT _HRESULT_TYPEDEF_(0x8004D01E)
#define XACT_E_TIP_CONNECT_FAILED _HRESULT_TYPEDEF_(0x8004D01F)
#define XACT_E_TIP_PROTOCOL_ERROR _HRESULT_TYPEDEF_(0x8004D020)
#define XACT_E_TIP_PULL_FAILED _HRESULT_TYPEDEF_(0x8004D021)
#define XACT_E_DEST_TMNOTAVAILABLE _HRESULT_TYPEDEF_(0x8004D022)
#define XACT_E_TIP_DISABLED _HRESULT_TYPEDEF_(0x8004D023)
#define XACT_E_NETWORK_TX_DISABLED _HRESULT_TYPEDEF_(0x8004D024)
#define XACT_E_PARTNER_NETWORK_TX_DISABLED _HRESULT_TYPEDEF_(0x8004D025)
#define XACT_E_XA_TX_DISABLED _HRESULT_TYPEDEF_(0x8004D026)
#define XACT_E_UNABLE_TO_READ_DTC_CONFIG _HRESULT_TYPEDEF_(0x8004D027)
#define XACT_E_UNABLE_TO_LOAD_DTC_PROXY _HRESULT_TYPEDEF_(0x8004D028)
#define XACT_E_ABORTING _HRESULT_TYPEDEF_(0x8004D029)
#define XACT_E_CLERKNOTFOUND _HRESULT_TYPEDEF_(0x8004D080)
#define XACT_E_CLERKEXISTS _HRESULT_TYPEDEF_(0x8004D081)
#define XACT_E_RECOVERYINPROGRESS _HRESULT_TYPEDEF_(0x8004D082)
#define XACT_E_TRANSACTIONCLOSED _HRESULT_TYPEDEF_(0x8004D083)
#define XACT_E_INVALIDLSN _HRESULT_TYPEDEF_(0x8004D084)
#define XACT_E_REPLAYREQUEST _HRESULT_TYPEDEF_(0x8004D085)
#define XACT_S_ASYNC _HRESULT_TYPEDEF_(0x0004D000)
#define XACT_S_DEFECT _HRESULT_TYPEDEF_(0x0004D001)
#define XACT_S_READONLY _HRESULT_TYPEDEF_(0x0004D002)
#define XACT_S_SOMENORETAIN _HRESULT_TYPEDEF_(0x0004D003)
#define XACT_S_OKINFORM _HRESULT_TYPEDEF_(0x0004D004)
#define XACT_S_MADECHANGESCONTENT _HRESULT_TYPEDEF_(0x0004D005)
#define XACT_S_MADECHANGESINFORM _HRESULT_TYPEDEF_(0x0004D006)
#define XACT_S_ALLNORETAIN _HRESULT_TYPEDEF_(0x0004D007)
#define XACT_S_ABORTING _HRESULT_TYPEDEF_(0x0004D008)
#define XACT_S_SINGLEPHASE _HRESULT_TYPEDEF_(0x0004D009)
#define XACT_S_LOCALLY_OK _HRESULT_TYPEDEF_(0x0004D00A)
#define XACT_S_LASTRESOURCEMANAGER _HRESULT_TYPEDEF_(0x0004D010)
#define CONTEXT_E_FIRST __MSABI_LONG(0x8004E000)
#define CONTEXT_E_LAST __MSABI_LONG(0x8004E02F)
#define CONTEXT_S_FIRST __MSABI_LONG(0x0004E000)
#define CONTEXT_S_LAST __MSABI_LONG(0x0004E02F)
#define CONTEXT_E_ABORTED _HRESULT_TYPEDEF_(0x8004E002)
#define CONTEXT_E_ABORTING _HRESULT_TYPEDEF_(0x8004E003)
#define CONTEXT_E_NOCONTEXT _HRESULT_TYPEDEF_(0x8004E004)
#define CONTEXT_E_WOULD_DEADLOCK _HRESULT_TYPEDEF_(0x8004E005)
#define CONTEXT_E_SYNCH_TIMEOUT _HRESULT_TYPEDEF_(0x8004E006)
#define CONTEXT_E_OLDREF _HRESULT_TYPEDEF_(0x8004E007)
#define CONTEXT_E_ROLENOTFOUND _HRESULT_TYPEDEF_(0x8004E00C)
#define CONTEXT_E_TMNOTAVAILABLE _HRESULT_TYPEDEF_(0x8004E00F)
#define CO_E_ACTIVATIONFAILED _HRESULT_TYPEDEF_(0x8004E021)
#define CO_E_ACTIVATIONFAILED_EVENTLOGGED _HRESULT_TYPEDEF_(0x8004E022)
#define CO_E_ACTIVATIONFAILED_CATALOGERROR _HRESULT_TYPEDEF_(0x8004E023)
#define CO_E_ACTIVATIONFAILED_TIMEOUT _HRESULT_TYPEDEF_(0x8004E024)
#define CO_E_INITIALIZATIONFAILED _HRESULT_TYPEDEF_(0x8004E025)
#define CONTEXT_E_NOJIT _HRESULT_TYPEDEF_(0x8004E026)
#define CONTEXT_E_NOTRANSACTION _HRESULT_TYPEDEF_(0x8004E027)
#define CO_E_THREADINGMODEL_CHANGED _HRESULT_TYPEDEF_(0x8004E028)
#define CO_E_NOIISINTRINSICS _HRESULT_TYPEDEF_(0x8004E029)
#define CO_E_NOCOOKIES _HRESULT_TYPEDEF_(0x8004E02A)
#define CO_E_DBERROR _HRESULT_TYPEDEF_(0x8004E02B)
#define CO_E_NOTPOOLED _HRESULT_TYPEDEF_(0x8004E02C)
#define CO_E_NOTCONSTRUCTED _HRESULT_TYPEDEF_(0x8004E02D)
#define CO_E_NOSYNCHRONIZATION _HRESULT_TYPEDEF_(0x8004E02E)
#define CO_E_ISOLEVELMISMATCH _HRESULT_TYPEDEF_(0x8004E02F)
#define OLE_S_USEREG _HRESULT_TYPEDEF_(0x00040000)
#define OLE_S_STATIC _HRESULT_TYPEDEF_(0x00040001)
#define OLE_S_MAC_CLIPFORMAT _HRESULT_TYPEDEF_(0x00040002)
#define DRAGDROP_S_DROP _HRESULT_TYPEDEF_(0x00040100)
#define DRAGDROP_S_CANCEL _HRESULT_TYPEDEF_(0x00040101)
#define DRAGDROP_S_USEDEFAULTCURSORS _HRESULT_TYPEDEF_(0x00040102)
#define DATA_S_SAMEFORMATETC _HRESULT_TYPEDEF_(0x00040130)
#define VIEW_S_ALREADY_FROZEN _HRESULT_TYPEDEF_(0x00040140)
#define CACHE_S_FORMATETC_NOTSUPPORTED _HRESULT_TYPEDEF_(0x00040170)
#define CACHE_S_SAMECACHE _HRESULT_TYPEDEF_(0x00040171)
#define CACHE_S_SOMECACHES_NOTUPDATED _HRESULT_TYPEDEF_(0x00040172)
#define OLEOBJ_S_INVALIDVERB _HRESULT_TYPEDEF_(0x00040180)
#define OLEOBJ_S_CANNOT_DOVERB_NOW _HRESULT_TYPEDEF_(0x00040181)
#define OLEOBJ_S_INVALIDHWND _HRESULT_TYPEDEF_(0x00040182)
#define INPLACE_S_TRUNCATED _HRESULT_TYPEDEF_(0x000401A0)
#define CONVERT10_S_NO_PRESENTATION _HRESULT_TYPEDEF_(0x000401C0)
#define MK_S_REDUCED_TO_SELF _HRESULT_TYPEDEF_(0x000401E2)
#define MK_S_ME _HRESULT_TYPEDEF_(0x000401E4)
#define MK_S_HIM _HRESULT_TYPEDEF_(0x000401E5)
#define MK_S_US _HRESULT_TYPEDEF_(0x000401E6)
#define MK_S_MONIKERALREADYREGISTERED _HRESULT_TYPEDEF_(0x000401E7)
#define SCHED_S_TASK_READY _HRESULT_TYPEDEF_(0x00041300)
#define SCHED_S_TASK_RUNNING _HRESULT_TYPEDEF_(0x00041301)
#define SCHED_S_TASK_DISABLED _HRESULT_TYPEDEF_(0x00041302)
#define SCHED_S_TASK_HAS_NOT_RUN _HRESULT_TYPEDEF_(0x00041303)
#define SCHED_S_TASK_NO_MORE_RUNS _HRESULT_TYPEDEF_(0x00041304)
#define SCHED_S_TASK_NOT_SCHEDULED _HRESULT_TYPEDEF_(0x00041305)
#define SCHED_S_TASK_TERMINATED _HRESULT_TYPEDEF_(0x00041306)
#define SCHED_S_TASK_NO_VALID_TRIGGERS _HRESULT_TYPEDEF_(0x00041307)
#define SCHED_S_EVENT_TRIGGER _HRESULT_TYPEDEF_(0x00041308)
#define SCHED_E_TRIGGER_NOT_FOUND _HRESULT_TYPEDEF_(0x80041309)
#define SCHED_E_TASK_NOT_READY _HRESULT_TYPEDEF_(0x8004130A)
#define SCHED_E_TASK_NOT_RUNNING _HRESULT_TYPEDEF_(0x8004130B)
#define SCHED_E_SERVICE_NOT_INSTALLED _HRESULT_TYPEDEF_(0x8004130C)
#define SCHED_E_CANNOT_OPEN_TASK _HRESULT_TYPEDEF_(0x8004130D)
#define SCHED_E_INVALID_TASK _HRESULT_TYPEDEF_(0x8004130E)
#define SCHED_E_ACCOUNT_INFORMATION_NOT_SET _HRESULT_TYPEDEF_(0x8004130F)
#define SCHED_E_ACCOUNT_NAME_NOT_FOUND _HRESULT_TYPEDEF_(0x80041310)
#define SCHED_E_ACCOUNT_DBASE_CORRUPT _HRESULT_TYPEDEF_(0x80041311)
#define SCHED_E_NO_SECURITY_SERVICES _HRESULT_TYPEDEF_(0x80041312)
#define SCHED_E_UNKNOWN_OBJECT_VERSION _HRESULT_TYPEDEF_(0x80041313)
#define SCHED_E_UNSUPPORTED_ACCOUNT_OPTION _HRESULT_TYPEDEF_(0x80041314)
#define SCHED_E_SERVICE_NOT_RUNNING _HRESULT_TYPEDEF_(0x80041315)
#define CO_E_CLASS_CREATE_FAILED _HRESULT_TYPEDEF_(0x80080001)
#define CO_E_SCM_ERROR _HRESULT_TYPEDEF_(0x80080002)
#define CO_E_SCM_RPC_FAILURE _HRESULT_TYPEDEF_(0x80080003)
#define CO_E_BAD_PATH _HRESULT_TYPEDEF_(0x80080004)
#define CO_E_SERVER_EXEC_FAILURE _HRESULT_TYPEDEF_(0x80080005)
#define CO_E_OBJSRV_RPC_FAILURE _HRESULT_TYPEDEF_(0x80080006)
#define MK_E_NO_NORMALIZED _HRESULT_TYPEDEF_(0x80080007)
#define CO_E_SERVER_STOPPING _HRESULT_TYPEDEF_(0x80080008)
#define MEM_E_INVALID_ROOT _HRESULT_TYPEDEF_(0x80080009)
#define MEM_E_INVALID_LINK _HRESULT_TYPEDEF_(0x80080010)
#define MEM_E_INVALID_SIZE _HRESULT_TYPEDEF_(0x80080011)
#define CO_S_NOTALLINTERFACES _HRESULT_TYPEDEF_(0x00080012)
#define CO_S_MACHINENAMENOTFOUND _HRESULT_TYPEDEF_(0x00080013)
#define DISP_E_UNKNOWNINTERFACE _HRESULT_TYPEDEF_(0x80020001)
#define DISP_E_MEMBERNOTFOUND _HRESULT_TYPEDEF_(0x80020003)
#define DISP_E_PARAMNOTFOUND _HRESULT_TYPEDEF_(0x80020004)
#define DISP_E_TYPEMISMATCH _HRESULT_TYPEDEF_(0x80020005)
#define DISP_E_UNKNOWNNAME _HRESULT_TYPEDEF_(0x80020006)
#define DISP_E_NONAMEDARGS _HRESULT_TYPEDEF_(0x80020007)
#define DISP_E_BADVARTYPE _HRESULT_TYPEDEF_(0x80020008)
#define DISP_E_EXCEPTION _HRESULT_TYPEDEF_(0x80020009)
#define DISP_E_OVERFLOW _HRESULT_TYPEDEF_(0x8002000A)
#define DISP_E_BADINDEX _HRESULT_TYPEDEF_(0x8002000B)
#define DISP_E_UNKNOWNLCID _HRESULT_TYPEDEF_(0x8002000C)
#define DISP_E_ARRAYISLOCKED _HRESULT_TYPEDEF_(0x8002000D)
#define DISP_E_BADPARAMCOUNT _HRESULT_TYPEDEF_(0x8002000E)
#define DISP_E_PARAMNOTOPTIONAL _HRESULT_TYPEDEF_(0x8002000F)
#define DISP_E_BADCALLEE _HRESULT_TYPEDEF_(0x80020010)
#define DISP_E_NOTACOLLECTION _HRESULT_TYPEDEF_(0x80020011)
#define DISP_E_DIVBYZERO _HRESULT_TYPEDEF_(0x80020012)
#define DISP_E_BUFFERTOOSMALL _HRESULT_TYPEDEF_(0x80020013)
#define TYPE_E_BUFFERTOOSMALL _HRESULT_TYPEDEF_(0x80028016)
#define TYPE_E_FIELDNOTFOUND _HRESULT_TYPEDEF_(0x80028017)
#define TYPE_E_INVDATAREAD _HRESULT_TYPEDEF_(0x80028018)
#define TYPE_E_UNSUPFORMAT _HRESULT_TYPEDEF_(0x80028019)
#define TYPE_E_REGISTRYACCESS _HRESULT_TYPEDEF_(0x8002801C)
#define TYPE_E_LIBNOTREGISTERED _HRESULT_TYPEDEF_(0x8002801D)
#define TYPE_E_UNDEFINEDTYPE _HRESULT_TYPEDEF_(0x80028027)
#define TYPE_E_QUALIFIEDNAMEDISALLOWED _HRESULT_TYPEDEF_(0x80028028)
#define TYPE_E_INVALIDSTATE _HRESULT_TYPEDEF_(0x80028029)
#define TYPE_E_WRONGTYPEKIND _HRESULT_TYPEDEF_(0x8002802A)
#define TYPE_E_ELEMENTNOTFOUND _HRESULT_TYPEDEF_(0x8002802B)
#define TYPE_E_AMBIGUOUSNAME _HRESULT_TYPEDEF_(0x8002802C)
#define TYPE_E_NAMECONFLICT _HRESULT_TYPEDEF_(0x8002802D)
#define TYPE_E_UNKNOWNLCID _HRESULT_TYPEDEF_(0x8002802E)
#define TYPE_E_DLLFUNCTIONNOTFOUND _HRESULT_TYPEDEF_(0x8002802F)
#define TYPE_E_BADMODULEKIND _HRESULT_TYPEDEF_(0x800288BD)
#define TYPE_E_SIZETOOBIG _HRESULT_TYPEDEF_(0x800288C5)
#define TYPE_E_DUPLICATEID _HRESULT_TYPEDEF_(0x800288C6)
#define TYPE_E_INVALIDID _HRESULT_TYPEDEF_(0x800288CF)
#define TYPE_E_TYPEMISMATCH _HRESULT_TYPEDEF_(0x80028CA0)
#define TYPE_E_OUTOFBOUNDS _HRESULT_TYPEDEF_(0x80028CA1)
#define TYPE_E_IOERROR _HRESULT_TYPEDEF_(0x80028CA2)
#define TYPE_E_CANTCREATETMPFILE _HRESULT_TYPEDEF_(0x80028CA3)
#define TYPE_E_CANTLOADLIBRARY _HRESULT_TYPEDEF_(0x80029C4A)
#define TYPE_E_INCONSISTENTPROPFUNCS _HRESULT_TYPEDEF_(0x80029C83)
#define TYPE_E_CIRCULARTYPE _HRESULT_TYPEDEF_(0x80029C84)
#define STG_E_INVALIDFUNCTION _HRESULT_TYPEDEF_(0x80030001)
#define STG_E_FILENOTFOUND _HRESULT_TYPEDEF_(0x80030002)
#define STG_E_PATHNOTFOUND _HRESULT_TYPEDEF_(0x80030003)
#define STG_E_TOOMANYOPENFILES _HRESULT_TYPEDEF_(0x80030004)
#define STG_E_ACCESSDENIED _HRESULT_TYPEDEF_(0x80030005)
#define STG_E_INVALIDHANDLE _HRESULT_TYPEDEF_(0x80030006)
#define STG_E_INSUFFICIENTMEMORY _HRESULT_TYPEDEF_(0x80030008)
#define STG_E_INVALIDPOINTER _HRESULT_TYPEDEF_(0x80030009)
#define STG_E_NOMOREFILES _HRESULT_TYPEDEF_(0x80030012)
#define STG_E_DISKISWRITEPROTECTED _HRESULT_TYPEDEF_(0x80030013)
#define STG_E_SEEKERROR _HRESULT_TYPEDEF_(0x80030019)
#define STG_E_WRITEFAULT _HRESULT_TYPEDEF_(0x8003001D)
#define STG_E_READFAULT _HRESULT_TYPEDEF_(0x8003001E)
#define STG_E_SHAREVIOLATION _HRESULT_TYPEDEF_(0x80030020)
#define STG_E_LOCKVIOLATION _HRESULT_TYPEDEF_(0x80030021)
#define STG_E_FILEALREADYEXISTS _HRESULT_TYPEDEF_(0x80030050)
#define STG_E_INVALIDPARAMETER _HRESULT_TYPEDEF_(0x80030057)
#define STG_E_MEDIUMFULL _HRESULT_TYPEDEF_(0x80030070)
#define STG_E_PROPSETMISMATCHED _HRESULT_TYPEDEF_(0x800300F0)
#define STG_E_ABNORMALAPIEXIT _HRESULT_TYPEDEF_(0x800300FA)
#define STG_E_INVALIDHEADER _HRESULT_TYPEDEF_(0x800300FB)
#define STG_E_INVALIDNAME _HRESULT_TYPEDEF_(0x800300FC)
#define STG_E_UNKNOWN _HRESULT_TYPEDEF_(0x800300FD)
#define STG_E_UNIMPLEMENTEDFUNCTION _HRESULT_TYPEDEF_(0x800300FE)
#define STG_E_INVALIDFLAG _HRESULT_TYPEDEF_(0x800300FF)
#define STG_E_INUSE _HRESULT_TYPEDEF_(0x80030100)
#define STG_E_NOTCURRENT _HRESULT_TYPEDEF_(0x80030101)
#define STG_E_REVERTED _HRESULT_TYPEDEF_(0x80030102)
#define STG_E_CANTSAVE _HRESULT_TYPEDEF_(0x80030103)
#define STG_E_OLDFORMAT _HRESULT_TYPEDEF_(0x80030104)
#define STG_E_OLDDLL _HRESULT_TYPEDEF_(0x80030105)
#define STG_E_SHAREREQUIRED _HRESULT_TYPEDEF_(0x80030106)
#define STG_E_NOTFILEBASEDSTORAGE _HRESULT_TYPEDEF_(0x80030107)
#define STG_E_EXTANTMARSHALLINGS _HRESULT_TYPEDEF_(0x80030108)
#define STG_E_DOCFILECORRUPT _HRESULT_TYPEDEF_(0x80030109)
#define STG_E_BADBASEADDRESS _HRESULT_TYPEDEF_(0x80030110)
#define STG_E_DOCFILETOOLARGE _HRESULT_TYPEDEF_(0x80030111)
#define STG_E_NOTSIMPLEFORMAT _HRESULT_TYPEDEF_(0x80030112)
#define STG_E_INCOMPLETE _HRESULT_TYPEDEF_(0x80030201)
#define STG_E_TERMINATED _HRESULT_TYPEDEF_(0x80030202)
#define STG_S_CONVERTED _HRESULT_TYPEDEF_(0x00030200)
#define STG_S_BLOCK _HRESULT_TYPEDEF_(0x00030201)
#define STG_S_RETRYNOW _HRESULT_TYPEDEF_(0x00030202)
#define STG_S_MONITORING _HRESULT_TYPEDEF_(0x00030203)
#define STG_S_MULTIPLEOPENS _HRESULT_TYPEDEF_(0x00030204)
#define STG_S_CONSOLIDATIONFAILED _HRESULT_TYPEDEF_(0x00030205)
#define STG_S_CANNOTCONSOLIDATE _HRESULT_TYPEDEF_(0x00030206)
#define STG_E_STATUS_COPY_PROTECTION_FAILURE _HRESULT_TYPEDEF_(0x80030305)
#define STG_E_CSS_AUTHENTICATION_FAILURE _HRESULT_TYPEDEF_(0x80030306)
#define STG_E_CSS_KEY_NOT_PRESENT _HRESULT_TYPEDEF_(0x80030307)
#define STG_E_CSS_KEY_NOT_ESTABLISHED _HRESULT_TYPEDEF_(0x80030308)
#define STG_E_CSS_SCRAMBLED_SECTOR _HRESULT_TYPEDEF_(0x80030309)
#define STG_E_CSS_REGION_MISMATCH _HRESULT_TYPEDEF_(0x8003030A)
#define STG_E_RESETS_EXHAUSTED _HRESULT_TYPEDEF_(0x8003030B)
#define RPC_E_CALL_REJECTED _HRESULT_TYPEDEF_(0x80010001)
#define RPC_E_CALL_CANCELED _HRESULT_TYPEDEF_(0x80010002)
#define RPC_E_CANTPOST_INSENDCALL _HRESULT_TYPEDEF_(0x80010003)
#define RPC_E_CANTCALLOUT_INASYNCCALL _HRESULT_TYPEDEF_(0x80010004)
#define RPC_E_CANTCALLOUT_INEXTERNALCALL _HRESULT_TYPEDEF_(0x80010005)
#define RPC_E_CONNECTION_TERMINATED _HRESULT_TYPEDEF_(0x80010006)
#define RPC_E_SERVER_DIED _HRESULT_TYPEDEF_(0x80010007)
#define RPC_E_CLIENT_DIED _HRESULT_TYPEDEF_(0x80010008)
#define RPC_E_INVALID_DATAPACKET _HRESULT_TYPEDEF_(0x80010009)
#define RPC_E_CANTTRANSMIT_CALL _HRESULT_TYPEDEF_(0x8001000A)
#define RPC_E_CLIENT_CANTMARSHAL_DATA _HRESULT_TYPEDEF_(0x8001000B)
#define RPC_E_CLIENT_CANTUNMARSHAL_DATA _HRESULT_TYPEDEF_(0x8001000C)
#define RPC_E_SERVER_CANTMARSHAL_DATA _HRESULT_TYPEDEF_(0x8001000D)
#define RPC_E_SERVER_CANTUNMARSHAL_DATA _HRESULT_TYPEDEF_(0x8001000E)
#define RPC_E_INVALID_DATA _HRESULT_TYPEDEF_(0x8001000F)
#define RPC_E_INVALID_PARAMETER _HRESULT_TYPEDEF_(0x80010010)
#define RPC_E_CANTCALLOUT_AGAIN _HRESULT_TYPEDEF_(0x80010011)
#define RPC_E_SERVER_DIED_DNE _HRESULT_TYPEDEF_(0x80010012)
#define RPC_E_SYS_CALL_FAILED _HRESULT_TYPEDEF_(0x80010100)
#define RPC_E_OUT_OF_RESOURCES _HRESULT_TYPEDEF_(0x80010101)
#define RPC_E_ATTEMPTED_MULTITHREAD _HRESULT_TYPEDEF_(0x80010102)
#define RPC_E_NOT_REGISTERED _HRESULT_TYPEDEF_(0x80010103)
#define RPC_E_FAULT _HRESULT_TYPEDEF_(0x80010104)
#define RPC_E_SERVERFAULT _HRESULT_TYPEDEF_(0x80010105)
#define RPC_E_CHANGED_MODE _HRESULT_TYPEDEF_(0x80010106)
#define RPC_E_INVALIDMETHOD _HRESULT_TYPEDEF_(0x80010107)
#define RPC_E_DISCONNECTED _HRESULT_TYPEDEF_(0x80010108)
#define RPC_E_RETRY _HRESULT_TYPEDEF_(0x80010109)
#define RPC_E_SERVERCALL_RETRYLATER _HRESULT_TYPEDEF_(0x8001010A)
#define RPC_E_SERVERCALL_REJECTED _HRESULT_TYPEDEF_(0x8001010B)
#define RPC_E_INVALID_CALLDATA _HRESULT_TYPEDEF_(0x8001010C)
#define RPC_E_CANTCALLOUT_ININPUTSYNCCALL _HRESULT_TYPEDEF_(0x8001010D)
#define RPC_E_WRONG_THREAD _HRESULT_TYPEDEF_(0x8001010E)
#define RPC_E_THREAD_NOT_INIT _HRESULT_TYPEDEF_(0x8001010F)
#define RPC_E_VERSION_MISMATCH _HRESULT_TYPEDEF_(0x80010110)
#define RPC_E_INVALID_HEADER _HRESULT_TYPEDEF_(0x80010111)
#define RPC_E_INVALID_EXTENSION _HRESULT_TYPEDEF_(0x80010112)
#define RPC_E_INVALID_IPID _HRESULT_TYPEDEF_(0x80010113)
#define RPC_E_INVALID_OBJECT _HRESULT_TYPEDEF_(0x80010114)
#define RPC_S_CALLPENDING _HRESULT_TYPEDEF_(0x80010115)
#define RPC_S_WAITONTIMER _HRESULT_TYPEDEF_(0x80010116)
#define RPC_E_CALL_COMPLETE _HRESULT_TYPEDEF_(0x80010117)
#define RPC_E_UNSECURE_CALL _HRESULT_TYPEDEF_(0x80010118)
#define RPC_E_TOO_LATE _HRESULT_TYPEDEF_(0x80010119)
#define RPC_E_NO_GOOD_SECURITY_PACKAGES _HRESULT_TYPEDEF_(0x8001011A)
#define RPC_E_ACCESS_DENIED _HRESULT_TYPEDEF_(0x8001011B)
#define RPC_E_REMOTE_DISABLED _HRESULT_TYPEDEF_(0x8001011C)
#define RPC_E_INVALID_OBJREF _HRESULT_TYPEDEF_(0x8001011D)
#define RPC_E_NO_CONTEXT _HRESULT_TYPEDEF_(0x8001011E)
#define RPC_E_TIMEOUT _HRESULT_TYPEDEF_(0x8001011F)
#define RPC_E_NO_SYNC _HRESULT_TYPEDEF_(0x80010120)
#define RPC_E_FULLSIC_REQUIRED _HRESULT_TYPEDEF_(0x80010121)
#define RPC_E_INVALID_STD_NAME _HRESULT_TYPEDEF_(0x80010122)
#define CO_E_FAILEDTOIMPERSONATE _HRESULT_TYPEDEF_(0x80010123)
#define CO_E_FAILEDTOGETSECCTX _HRESULT_TYPEDEF_(0x80010124)
#define CO_E_FAILEDTOOPENTHREADTOKEN _HRESULT_TYPEDEF_(0x80010125)
#define CO_E_FAILEDTOGETTOKENINFO _HRESULT_TYPEDEF_(0x80010126)
#define CO_E_TRUSTEEDOESNTMATCHCLIENT _HRESULT_TYPEDEF_(0x80010127)
#define CO_E_FAILEDTOQUERYCLIENTBLANKET _HRESULT_TYPEDEF_(0x80010128)
#define CO_E_FAILEDTOSETDACL _HRESULT_TYPEDEF_(0x80010129)
#define CO_E_ACCESSCHECKFAILED _HRESULT_TYPEDEF_(0x8001012A)
#define CO_E_NETACCESSAPIFAILED _HRESULT_TYPEDEF_(0x8001012B)
#define CO_E_WRONGTRUSTEENAMESYNTAX _HRESULT_TYPEDEF_(0x8001012C)
#define CO_E_INVALIDSID _HRESULT_TYPEDEF_(0x8001012D)
#define CO_E_CONVERSIONFAILED _HRESULT_TYPEDEF_(0x8001012E)
#define CO_E_NOMATCHINGSIDFOUND _HRESULT_TYPEDEF_(0x8001012F)
#define CO_E_LOOKUPACCSIDFAILED _HRESULT_TYPEDEF_(0x80010130)
#define CO_E_NOMATCHINGNAMEFOUND _HRESULT_TYPEDEF_(0x80010131)
#define CO_E_LOOKUPACCNAMEFAILED _HRESULT_TYPEDEF_(0x80010132)
#define CO_E_SETSERLHNDLFAILED _HRESULT_TYPEDEF_(0x80010133)
#define CO_E_FAILEDTOGETWINDIR _HRESULT_TYPEDEF_(0x80010134)
#define CO_E_PATHTOOLONG _HRESULT_TYPEDEF_(0x80010135)
#define CO_E_FAILEDTOGENUUID _HRESULT_TYPEDEF_(0x80010136)
#define CO_E_FAILEDTOCREATEFILE _HRESULT_TYPEDEF_(0x80010137)
#define CO_E_FAILEDTOCLOSEHANDLE _HRESULT_TYPEDEF_(0x80010138)
#define CO_E_EXCEEDSYSACLLIMIT _HRESULT_TYPEDEF_(0x80010139)
#define CO_E_ACESINWRONGORDER _HRESULT_TYPEDEF_(0x8001013A)
#define CO_E_INCOMPATIBLESTREAMVERSION _HRESULT_TYPEDEF_(0x8001013B)
#define CO_E_FAILEDTOOPENPROCESSTOKEN _HRESULT_TYPEDEF_(0x8001013C)
#define CO_E_DECODEFAILED _HRESULT_TYPEDEF_(0x8001013D)
#define CO_E_ACNOTINITIALIZED _HRESULT_TYPEDEF_(0x8001013F)
#define CO_E_CANCEL_DISABLED _HRESULT_TYPEDEF_(0x80010140)
#define RPC_E_UNEXPECTED _HRESULT_TYPEDEF_(0x8001FFFF)
#define ERROR_AUDITING_DISABLED _HRESULT_TYPEDEF_(0xC0090001)
#define ERROR_ALL_SIDS_FILTERED _HRESULT_TYPEDEF_(0xC0090002)
#define NTE_BAD_UID _HRESULT_TYPEDEF_(0x80090001)
#define NTE_BAD_HASH _HRESULT_TYPEDEF_(0x80090002)
#define NTE_BAD_KEY _HRESULT_TYPEDEF_(0x80090003)
#define NTE_BAD_LEN _HRESULT_TYPEDEF_(0x80090004)
#define NTE_BAD_DATA _HRESULT_TYPEDEF_(0x80090005)
#define NTE_BAD_SIGNATURE _HRESULT_TYPEDEF_(0x80090006)
#define NTE_BAD_VER _HRESULT_TYPEDEF_(0x80090007)
#define NTE_BAD_ALGID _HRESULT_TYPEDEF_(0x80090008)
#define NTE_BAD_FLAGS _HRESULT_TYPEDEF_(0x80090009)
#define NTE_BAD_TYPE _HRESULT_TYPEDEF_(0x8009000A)
#define NTE_BAD_KEY_STATE _HRESULT_TYPEDEF_(0x8009000B)
#define NTE_BAD_HASH_STATE _HRESULT_TYPEDEF_(0x8009000C)
#define NTE_NO_KEY _HRESULT_TYPEDEF_(0x8009000D)
#define NTE_NO_MEMORY _HRESULT_TYPEDEF_(0x8009000E)
#define NTE_EXISTS _HRESULT_TYPEDEF_(0x8009000F)
#define NTE_PERM _HRESULT_TYPEDEF_(0x80090010)
#define NTE_NOT_FOUND _HRESULT_TYPEDEF_(0x80090011)
#define NTE_DOUBLE_ENCRYPT _HRESULT_TYPEDEF_(0x80090012)
#define NTE_BAD_PROVIDER _HRESULT_TYPEDEF_(0x80090013)
#define NTE_BAD_PROV_TYPE _HRESULT_TYPEDEF_(0x80090014)
#define NTE_BAD_PUBLIC_KEY _HRESULT_TYPEDEF_(0x80090015)
#define NTE_BAD_KEYSET _HRESULT_TYPEDEF_(0x80090016)
#define NTE_PROV_TYPE_NOT_DEF _HRESULT_TYPEDEF_(0x80090017)
#define NTE_PROV_TYPE_ENTRY_BAD _HRESULT_TYPEDEF_(0x80090018)
#define NTE_KEYSET_NOT_DEF _HRESULT_TYPEDEF_(0x80090019)
#define NTE_KEYSET_ENTRY_BAD _HRESULT_TYPEDEF_(0x8009001A)
#define NTE_PROV_TYPE_NO_MATCH _HRESULT_TYPEDEF_(0x8009001B)
#define NTE_SIGNATURE_FILE_BAD _HRESULT_TYPEDEF_(0x8009001C)
#define NTE_PROVIDER_DLL_FAIL _HRESULT_TYPEDEF_(0x8009001D)
#define NTE_PROV_DLL_NOT_FOUND _HRESULT_TYPEDEF_(0x8009001E)
#define NTE_BAD_KEYSET_PARAM _HRESULT_TYPEDEF_(0x8009001F)
#define NTE_FAIL _HRESULT_TYPEDEF_(0x80090020)
#define NTE_SYS_ERR _HRESULT_TYPEDEF_(0x80090021)
#define NTE_SILENT_CONTEXT _HRESULT_TYPEDEF_(0x80090022)
#define NTE_TOKEN_KEYSET_STORAGE_FULL _HRESULT_TYPEDEF_(0x80090023)
#define NTE_TEMPORARY_PROFILE _HRESULT_TYPEDEF_(0x80090024)
#define NTE_FIXEDPARAMETER _HRESULT_TYPEDEF_(0x80090025)
#define NTE_INVALID_HANDLE _HRESULT_TYPEDEF_(0x80090026)
#define NTE_INVALID_PARAMETER _HRESULT_TYPEDEF_(0x80090027)
#define NTE_BUFFER_TOO_SMALL _HRESULT_TYPEDEF_(0x80090028)
#define NTE_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x80090029)
#define NTE_NO_MORE_ITEMS _HRESULT_TYPEDEF_(0x8009002A)
#define NTE_BUFFERS_OVERLAP _HRESULT_TYPEDEF_(0x8009002B)
#define NTE_DECRYPTION_FAILURE _HRESULT_TYPEDEF_(0x8009002C)
#define NTE_INTERNAL_ERROR _HRESULT_TYPEDEF_(0x8009002D)
#define NTE_UI_REQUIRED _HRESULT_TYPEDEF_(0x8009002E)
#define NTE_HMAC_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x8009002F)
#define NTE_DEVICE_NOT_READY _HRESULT_TYPEDEF_(0x80090030)
#define NTE_AUTHENTICATION_IGNORED _HRESULT_TYPEDEF_(0x80090031)
#define NTE_VALIDATION_FAILED _HRESULT_TYPEDEF_(0x80090032)
#define NTE_INCORRECT_PASSWORD _HRESULT_TYPEDEF_(0x80090033)
#define NTE_ENCRYPTION_FAILURE _HRESULT_TYPEDEF_(0x80090034)
#define NTE_DEVICE_NOT_FOUND _HRESULT_TYPEDEF_(0x80090035)
#define NTE_USER_CANCELLED _HRESULT_TYPEDEF_(0x80090036)
#define NTE_PASSWORD_CHANGE_REQUIRED _HRESULT_TYPEDEF_(0x80090037)
#define NTE_NOT_ACTIVE_CONSOLE _HRESULT_TYPEDEF_(0x80090038)
#define SEC_E_INSUFFICIENT_MEMORY _HRESULT_TYPEDEF_(0x80090300)
#define SEC_E_INVALID_HANDLE _HRESULT_TYPEDEF_(0x80090301)
#define SEC_E_UNSUPPORTED_FUNCTION _HRESULT_TYPEDEF_(0x80090302)
#define SEC_E_TARGET_UNKNOWN _HRESULT_TYPEDEF_(0x80090303)
#define SEC_E_INTERNAL_ERROR _HRESULT_TYPEDEF_(0x80090304)
#define SEC_E_SECPKG_NOT_FOUND _HRESULT_TYPEDEF_(0x80090305)
#define SEC_E_NOT_OWNER _HRESULT_TYPEDEF_(0x80090306)
#define SEC_E_CANNOT_INSTALL _HRESULT_TYPEDEF_(0x80090307)
#define SEC_E_INVALID_TOKEN _HRESULT_TYPEDEF_(0x80090308)
#define SEC_E_CANNOT_PACK _HRESULT_TYPEDEF_(0x80090309)
#define SEC_E_QOP_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x8009030A)
#define SEC_E_NO_IMPERSONATION _HRESULT_TYPEDEF_(0x8009030B)
#define SEC_E_LOGON_DENIED _HRESULT_TYPEDEF_(0x8009030C)
#define SEC_E_UNKNOWN_CREDENTIALS _HRESULT_TYPEDEF_(0x8009030D)
#define SEC_E_NO_CREDENTIALS _HRESULT_TYPEDEF_(0x8009030E)
#define SEC_E_MESSAGE_ALTERED _HRESULT_TYPEDEF_(0x8009030F)
#define SEC_E_OUT_OF_SEQUENCE _HRESULT_TYPEDEF_(0x80090310)
#define SEC_E_NO_AUTHENTICATING_AUTHORITY _HRESULT_TYPEDEF_(0x80090311)
#define SEC_I_CONTINUE_NEEDED _HRESULT_TYPEDEF_(0x00090312)
#define SEC_I_COMPLETE_NEEDED _HRESULT_TYPEDEF_(0x00090313)
#define SEC_I_COMPLETE_AND_CONTINUE _HRESULT_TYPEDEF_(0x00090314)
#define SEC_I_LOCAL_LOGON _HRESULT_TYPEDEF_(0x00090315)
#define SEC_I_GENERIC_EXTENSION_RECEIVED _HRESULT_TYPEDEF_(0x00090316)
#define SEC_E_BAD_PKGID _HRESULT_TYPEDEF_(0x80090316)
#define SEC_E_CONTEXT_EXPIRED _HRESULT_TYPEDEF_(0x80090317)
#define SEC_I_CONTEXT_EXPIRED _HRESULT_TYPEDEF_(0x00090317)
#define SEC_E_INCOMPLETE_MESSAGE _HRESULT_TYPEDEF_(0x80090318)
#define SEC_E_INCOMPLETE_CREDENTIALS _HRESULT_TYPEDEF_(0x80090320)
#define SEC_E_BUFFER_TOO_SMALL _HRESULT_TYPEDEF_(0x80090321)
#define SEC_I_INCOMPLETE_CREDENTIALS _HRESULT_TYPEDEF_(0x00090320)
#define SEC_I_RENEGOTIATE _HRESULT_TYPEDEF_(0x00090321)
#define SEC_E_WRONG_PRINCIPAL _HRESULT_TYPEDEF_(0x80090322)
#define SEC_I_NO_LSA_CONTEXT _HRESULT_TYPEDEF_(0x00090323)
#define SEC_E_TIME_SKEW _HRESULT_TYPEDEF_(0x80090324)
#define SEC_E_UNTRUSTED_ROOT _HRESULT_TYPEDEF_(0x80090325)
#define SEC_E_ILLEGAL_MESSAGE _HRESULT_TYPEDEF_(0x80090326)
#define SEC_E_CERT_UNKNOWN _HRESULT_TYPEDEF_(0x80090327)
#define SEC_E_CERT_EXPIRED _HRESULT_TYPEDEF_(0x80090328)
#define SEC_E_ENCRYPT_FAILURE _HRESULT_TYPEDEF_(0x80090329)
#define SEC_E_DECRYPT_FAILURE _HRESULT_TYPEDEF_(0x80090330)
#define SEC_E_ALGORITHM_MISMATCH _HRESULT_TYPEDEF_(0x80090331)
#define SEC_E_SECURITY_QOS_FAILED _HRESULT_TYPEDEF_(0x80090332)
#define SEC_E_UNFINISHED_CONTEXT_DELETED _HRESULT_TYPEDEF_(0x80090333)
#define SEC_E_NO_TGT_REPLY _HRESULT_TYPEDEF_(0x80090334)
#define SEC_E_NO_IP_ADDRESSES _HRESULT_TYPEDEF_(0x80090335)
#define SEC_E_WRONG_CREDENTIAL_HANDLE _HRESULT_TYPEDEF_(0x80090336)
#define SEC_E_CRYPTO_SYSTEM_INVALID _HRESULT_TYPEDEF_(0x80090337)
#define SEC_E_MAX_REFERRALS_EXCEEDED _HRESULT_TYPEDEF_(0x80090338)
#define SEC_E_MUST_BE_KDC _HRESULT_TYPEDEF_(0x80090339)
#define SEC_E_STRONG_CRYPTO_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x8009033A)
#define SEC_E_TOO_MANY_PRINCIPALS _HRESULT_TYPEDEF_(0x8009033B)
#define SEC_E_NO_PA_DATA _HRESULT_TYPEDEF_(0x8009033C)
#define SEC_E_PKINIT_NAME_MISMATCH _HRESULT_TYPEDEF_(0x8009033D)
#define SEC_E_SMARTCARD_LOGON_REQUIRED _HRESULT_TYPEDEF_(0x8009033E)
#define SEC_E_SHUTDOWN_IN_PROGRESS _HRESULT_TYPEDEF_(0x8009033F)
#define SEC_E_KDC_INVALID_REQUEST _HRESULT_TYPEDEF_(0x80090340)
#define SEC_E_KDC_UNABLE_TO_REFER _HRESULT_TYPEDEF_(0x80090341)
#define SEC_E_KDC_UNKNOWN_ETYPE _HRESULT_TYPEDEF_(0x80090342)
#define SEC_E_UNSUPPORTED_PREAUTH _HRESULT_TYPEDEF_(0x80090343)
#define SEC_E_DELEGATION_REQUIRED _HRESULT_TYPEDEF_(0x80090345)
#define SEC_E_BAD_BINDINGS _HRESULT_TYPEDEF_(0x80090346)
#define SEC_E_MULTIPLE_ACCOUNTS _HRESULT_TYPEDEF_(0x80090347)
#define SEC_E_NO_KERB_KEY _HRESULT_TYPEDEF_(0x80090348)
#define SEC_E_CERT_WRONG_USAGE _HRESULT_TYPEDEF_(0x80090349)
#define SEC_E_DOWNGRADE_DETECTED _HRESULT_TYPEDEF_(0x80090350)
#define SEC_E_SMARTCARD_CERT_REVOKED _HRESULT_TYPEDEF_(0x80090351)
#define SEC_E_ISSUING_CA_UNTRUSTED _HRESULT_TYPEDEF_(0x80090352)
#define SEC_E_REVOCATION_OFFLINE_C _HRESULT_TYPEDEF_(0x80090353)
#define SEC_E_PKINIT_CLIENT_FAILURE _HRESULT_TYPEDEF_(0x80090354)
#define SEC_E_SMARTCARD_CERT_EXPIRED _HRESULT_TYPEDEF_(0x80090355)
#define SEC_E_NO_S4U_PROT_SUPPORT _HRESULT_TYPEDEF_(0x80090356)
#define SEC_E_CROSSREALM_DELEGATION_FAILURE _HRESULT_TYPEDEF_(0x80090357)
#define SEC_E_REVOCATION_OFFLINE_KDC _HRESULT_TYPEDEF_(0x80090358)
#define SEC_E_ISSUING_CA_UNTRUSTED_KDC _HRESULT_TYPEDEF_(0x80090359)
#define SEC_E_KDC_CERT_EXPIRED _HRESULT_TYPEDEF_(0x8009035A)
#define SEC_E_KDC_CERT_REVOKED _HRESULT_TYPEDEF_(0x8009035B)
#define SEC_I_SIGNATURE_NEEDED _HRESULT_TYPEDEF_(0x0009035C)
#define SEC_E_INVALID_PARAMETER _HRESULT_TYPEDEF_(0x8009035D)
#define SEC_E_DELEGATION_POLICY _HRESULT_TYPEDEF_(0x8009035E)
#define SEC_E_POLICY_NLTM_ONLY _HRESULT_TYPEDEF_(0x8009035F)
#define SEC_I_NO_RENEGOTIATION _HRESULT_TYPEDEF_(0x00090360)
#define SEC_E_NO_CONTEXT _HRESULT_TYPEDEF_(0x80090361)
#define SEC_E_PKU2U_CERT_FAILURE _HRESULT_TYPEDEF_(0x80090362)
#define SEC_E_MUTUAL_AUTH_FAILED _HRESULT_TYPEDEF_(0x80090363)
#define SEC_I_MESSAGE_FRAGMENT _HRESULT_TYPEDEF_(0x00090364)
#define SEC_E_ONLY_HTTPS_ALLOWED _HRESULT_TYPEDEF_(0x80090365)
#define SEC_I_CONTINUE_NEEDED_MESSAGE_OK _HRESULT_TYPEDEF_(0x00090366)
#define SEC_E_APPLICATION_PROTOCOL_MISMATCH _HRESULT_TYPEDEF_(0x80090367)
#define SEC_I_ASYNC_CALL_PENDING _HRESULT_TYPEDEF_(0x00090368)
#define SEC_E_INVALID_UPN_NAME _HRESULT_TYPEDEF_(0x80090369)
#define SEC_E_EXT_BUFFER_TOO_SMALL _HRESULT_TYPEDEF_(0x8009036A)
#define SEC_E_INSUFFICIENT_BUFFERS _HRESULT_TYPEDEF_(0x8009036B)
#define SEC_E_NO_SPM SEC_E_INTERNAL_ERROR
#define SEC_E_NOT_SUPPORTED SEC_E_UNSUPPORTED_FUNCTION
#define CRYPT_E_MSG_ERROR _HRESULT_TYPEDEF_(0x80091001)
#define CRYPT_E_UNKNOWN_ALGO _HRESULT_TYPEDEF_(0x80091002)
#define CRYPT_E_OID_FORMAT _HRESULT_TYPEDEF_(0x80091003)
#define CRYPT_E_INVALID_MSG_TYPE _HRESULT_TYPEDEF_(0x80091004)
#define CRYPT_E_UNEXPECTED_ENCODING _HRESULT_TYPEDEF_(0x80091005)
#define CRYPT_E_AUTH_ATTR_MISSING _HRESULT_TYPEDEF_(0x80091006)
#define CRYPT_E_HASH_VALUE _HRESULT_TYPEDEF_(0x80091007)
#define CRYPT_E_INVALID_INDEX _HRESULT_TYPEDEF_(0x80091008)
#define CRYPT_E_ALREADY_DECRYPTED _HRESULT_TYPEDEF_(0x80091009)
#define CRYPT_E_NOT_DECRYPTED _HRESULT_TYPEDEF_(0x8009100A)
#define CRYPT_E_RECIPIENT_NOT_FOUND _HRESULT_TYPEDEF_(0x8009100B)
#define CRYPT_E_CONTROL_TYPE _HRESULT_TYPEDEF_(0x8009100C)
#define CRYPT_E_ISSUER_SERIALNUMBER _HRESULT_TYPEDEF_(0x8009100D)
#define CRYPT_E_SIGNER_NOT_FOUND _HRESULT_TYPEDEF_(0x8009100E)
#define CRYPT_E_ATTRIBUTES_MISSING _HRESULT_TYPEDEF_(0x8009100F)
#define CRYPT_E_STREAM_MSG_NOT_READY _HRESULT_TYPEDEF_(0x80091010)
#define CRYPT_E_STREAM_INSUFFICIENT_DATA _HRESULT_TYPEDEF_(0x80091011)
#define CRYPT_I_NEW_PROTECTION_REQUIRED _HRESULT_TYPEDEF_(0x00091012)
#define CRYPT_E_BAD_LEN _HRESULT_TYPEDEF_(0x80092001)
#define CRYPT_E_BAD_ENCODE _HRESULT_TYPEDEF_(0x80092002)
#define CRYPT_E_FILE_ERROR _HRESULT_TYPEDEF_(0x80092003)
#define CRYPT_E_NOT_FOUND _HRESULT_TYPEDEF_(0x80092004)
#define CRYPT_E_EXISTS _HRESULT_TYPEDEF_(0x80092005)
#define CRYPT_E_NO_PROVIDER _HRESULT_TYPEDEF_(0x80092006)
#define CRYPT_E_SELF_SIGNED _HRESULT_TYPEDEF_(0x80092007)
#define CRYPT_E_DELETED_PREV _HRESULT_TYPEDEF_(0x80092008)
#define CRYPT_E_NO_MATCH _HRESULT_TYPEDEF_(0x80092009)
#define CRYPT_E_UNEXPECTED_MSG_TYPE _HRESULT_TYPEDEF_(0x8009200A)
#define CRYPT_E_NO_KEY_PROPERTY _HRESULT_TYPEDEF_(0x8009200B)
#define CRYPT_E_NO_DECRYPT_CERT _HRESULT_TYPEDEF_(0x8009200C)
#define CRYPT_E_BAD_MSG _HRESULT_TYPEDEF_(0x8009200D)
#define CRYPT_E_NO_SIGNER _HRESULT_TYPEDEF_(0x8009200E)
#define CRYPT_E_PENDING_CLOSE _HRESULT_TYPEDEF_(0x8009200F)
#define CRYPT_E_REVOKED _HRESULT_TYPEDEF_(0x80092010)
#define CRYPT_E_NO_REVOCATION_DLL _HRESULT_TYPEDEF_(0x80092011)
#define CRYPT_E_NO_REVOCATION_CHECK _HRESULT_TYPEDEF_(0x80092012)
#define CRYPT_E_REVOCATION_OFFLINE _HRESULT_TYPEDEF_(0x80092013)
#define CRYPT_E_NOT_IN_REVOCATION_DATABASE _HRESULT_TYPEDEF_(0x80092014)
#define CRYPT_E_INVALID_NUMERIC_STRING _HRESULT_TYPEDEF_(0x80092020)
#define CRYPT_E_INVALID_PRINTABLE_STRING _HRESULT_TYPEDEF_(0x80092021)
#define CRYPT_E_INVALID_IA5_STRING _HRESULT_TYPEDEF_(0x80092022)
#define CRYPT_E_INVALID_X500_STRING _HRESULT_TYPEDEF_(0x80092023)
#define CRYPT_E_NOT_CHAR_STRING _HRESULT_TYPEDEF_(0x80092024)
#define CRYPT_E_FILERESIZED _HRESULT_TYPEDEF_(0x80092025)
#define CRYPT_E_SECURITY_SETTINGS _HRESULT_TYPEDEF_(0x80092026)
#define CRYPT_E_NO_VERIFY_USAGE_DLL _HRESULT_TYPEDEF_(0x80092027)
#define CRYPT_E_NO_VERIFY_USAGE_CHECK _HRESULT_TYPEDEF_(0x80092028)
#define CRYPT_E_VERIFY_USAGE_OFFLINE _HRESULT_TYPEDEF_(0x80092029)
#define CRYPT_E_NOT_IN_CTL _HRESULT_TYPEDEF_(0x8009202A)
#define CRYPT_E_NO_TRUSTED_SIGNER _HRESULT_TYPEDEF_(0x8009202B)
#define CRYPT_E_MISSING_PUBKEY_PARA _HRESULT_TYPEDEF_(0x8009202C)
#define CRYPT_E_OSS_ERROR _HRESULT_TYPEDEF_(0x80093000)
#define OSS_MORE_BUF _HRESULT_TYPEDEF_(0x80093001)
#define OSS_NEGATIVE_UINTEGER _HRESULT_TYPEDEF_(0x80093002)
#define OSS_PDU_RANGE _HRESULT_TYPEDEF_(0x80093003)
#define OSS_MORE_INPUT _HRESULT_TYPEDEF_(0x80093004)
#define OSS_DATA_ERROR _HRESULT_TYPEDEF_(0x80093005)
#define OSS_BAD_ARG _HRESULT_TYPEDEF_(0x80093006)
#define OSS_BAD_VERSION _HRESULT_TYPEDEF_(0x80093007)
#define OSS_OUT_MEMORY _HRESULT_TYPEDEF_(0x80093008)
#define OSS_PDU_MISMATCH _HRESULT_TYPEDEF_(0x80093009)
#define OSS_LIMITED _HRESULT_TYPEDEF_(0x8009300A)
#define OSS_BAD_PTR _HRESULT_TYPEDEF_(0x8009300B)
#define OSS_BAD_TIME _HRESULT_TYPEDEF_(0x8009300C)
#define OSS_INDEFINITE_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x8009300D)
#define OSS_MEM_ERROR _HRESULT_TYPEDEF_(0x8009300E)
#define OSS_BAD_TABLE _HRESULT_TYPEDEF_(0x8009300F)
#define OSS_TOO_LONG _HRESULT_TYPEDEF_(0x80093010)
#define OSS_CONSTRAINT_VIOLATED _HRESULT_TYPEDEF_(0x80093011)
#define OSS_FATAL_ERROR _HRESULT_TYPEDEF_(0x80093012)
#define OSS_ACCESS_SERIALIZATION_ERROR _HRESULT_TYPEDEF_(0x80093013)
#define OSS_NULL_TBL _HRESULT_TYPEDEF_(0x80093014)
#define OSS_NULL_FCN _HRESULT_TYPEDEF_(0x80093015)
#define OSS_BAD_ENCRULES _HRESULT_TYPEDEF_(0x80093016)
#define OSS_UNAVAIL_ENCRULES _HRESULT_TYPEDEF_(0x80093017)
#define OSS_CANT_OPEN_TRACE_WINDOW _HRESULT_TYPEDEF_(0x80093018)
#define OSS_UNIMPLEMENTED _HRESULT_TYPEDEF_(0x80093019)
#define OSS_OID_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x8009301A)
#define OSS_CANT_OPEN_TRACE_FILE _HRESULT_TYPEDEF_(0x8009301B)
#define OSS_TRACE_FILE_ALREADY_OPEN _HRESULT_TYPEDEF_(0x8009301C)
#define OSS_TABLE_MISMATCH _HRESULT_TYPEDEF_(0x8009301D)
#define OSS_TYPE_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x8009301E)
#define OSS_REAL_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x8009301F)
#define OSS_REAL_CODE_NOT_LINKED _HRESULT_TYPEDEF_(0x80093020)
#define OSS_OUT_OF_RANGE _HRESULT_TYPEDEF_(0x80093021)
#define OSS_COPIER_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x80093022)
#define OSS_CONSTRAINT_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x80093023)
#define OSS_COMPARATOR_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x80093024)
#define OSS_COMPARATOR_CODE_NOT_LINKED _HRESULT_TYPEDEF_(0x80093025)
#define OSS_MEM_MGR_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x80093026)
#define OSS_PDV_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x80093027)
#define OSS_PDV_CODE_NOT_LINKED _HRESULT_TYPEDEF_(0x80093028)
#define OSS_API_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x80093029)
#define OSS_BERDER_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x8009302A)
#define OSS_PER_DLL_NOT_LINKED _HRESULT_TYPEDEF_(0x8009302B)
#define OSS_OPEN_TYPE_ERROR _HRESULT_TYPEDEF_(0x8009302C)
#define OSS_MUTEX_NOT_CREATED _HRESULT_TYPEDEF_(0x8009302D)
#define OSS_CANT_CLOSE_TRACE_FILE _HRESULT_TYPEDEF_(0x8009302E)
#define CRYPT_E_ASN1_ERROR _HRESULT_TYPEDEF_(0x80093100)
#define CRYPT_E_ASN1_INTERNAL _HRESULT_TYPEDEF_(0x80093101)
#define CRYPT_E_ASN1_EOD _HRESULT_TYPEDEF_(0x80093102)
#define CRYPT_E_ASN1_CORRUPT _HRESULT_TYPEDEF_(0x80093103)
#define CRYPT_E_ASN1_LARGE _HRESULT_TYPEDEF_(0x80093104)
#define CRYPT_E_ASN1_CONSTRAINT _HRESULT_TYPEDEF_(0x80093105)
#define CRYPT_E_ASN1_MEMORY _HRESULT_TYPEDEF_(0x80093106)
#define CRYPT_E_ASN1_OVERFLOW _HRESULT_TYPEDEF_(0x80093107)
#define CRYPT_E_ASN1_BADPDU _HRESULT_TYPEDEF_(0x80093108)
#define CRYPT_E_ASN1_BADARGS _HRESULT_TYPEDEF_(0x80093109)
#define CRYPT_E_ASN1_BADREAL _HRESULT_TYPEDEF_(0x8009310A)
#define CRYPT_E_ASN1_BADTAG _HRESULT_TYPEDEF_(0x8009310B)
#define CRYPT_E_ASN1_CHOICE _HRESULT_TYPEDEF_(0x8009310C)
#define CRYPT_E_ASN1_RULE _HRESULT_TYPEDEF_(0x8009310D)
#define CRYPT_E_ASN1_UTF8 _HRESULT_TYPEDEF_(0x8009310E)
#define CRYPT_E_ASN1_PDU_TYPE _HRESULT_TYPEDEF_(0x80093133)
#define CRYPT_E_ASN1_NYI _HRESULT_TYPEDEF_(0x80093134)
#define CRYPT_E_ASN1_EXTENDED _HRESULT_TYPEDEF_(0x80093201)
#define CRYPT_E_ASN1_NOEOD _HRESULT_TYPEDEF_(0x80093202)
#define CERTSRV_E_BAD_REQUESTSUBJECT _HRESULT_TYPEDEF_(0x80094001)
#define CERTSRV_E_NO_REQUEST _HRESULT_TYPEDEF_(0x80094002)
#define CERTSRV_E_BAD_REQUESTSTATUS _HRESULT_TYPEDEF_(0x80094003)
#define CERTSRV_E_PROPERTY_EMPTY _HRESULT_TYPEDEF_(0x80094004)
#define CERTSRV_E_INVALID_CA_CERTIFICATE _HRESULT_TYPEDEF_(0x80094005)
#define CERTSRV_E_SERVER_SUSPENDED _HRESULT_TYPEDEF_(0x80094006)
#define CERTSRV_E_ENCODING_LENGTH _HRESULT_TYPEDEF_(0x80094007)
#define CERTSRV_E_ROLECONFLICT _HRESULT_TYPEDEF_(0x80094008)
#define CERTSRV_E_RESTRICTEDOFFICER _HRESULT_TYPEDEF_(0x80094009)
#define CERTSRV_E_KEY_ARCHIVAL_NOT_CONFIGURED _HRESULT_TYPEDEF_(0x8009400A)
#define CERTSRV_E_NO_VALID_KRA _HRESULT_TYPEDEF_(0x8009400B)
#define CERTSRV_E_BAD_REQUEST_KEY_ARCHIVAL _HRESULT_TYPEDEF_(0x8009400C)
#define CERTSRV_E_NO_CAADMIN_DEFINED _HRESULT_TYPEDEF_(0x8009400D)
#define CERTSRV_E_BAD_RENEWAL_CERT_ATTRIBUTE _HRESULT_TYPEDEF_(0x8009400E)
#define CERTSRV_E_NO_DB_SESSIONS _HRESULT_TYPEDEF_(0x8009400F)
#define CERTSRV_E_ALIGNMENT_FAULT _HRESULT_TYPEDEF_(0x80094010)
#define CERTSRV_E_ENROLL_DENIED _HRESULT_TYPEDEF_(0x80094011)
#define CERTSRV_E_TEMPLATE_DENIED _HRESULT_TYPEDEF_(0x80094012)
#define CERTSRV_E_DOWNLEVEL_DC_SSL_OR_UPGRADE _HRESULT_TYPEDEF_(0x80094013)
#define CERTSRV_E_UNSUPPORTED_CERT_TYPE _HRESULT_TYPEDEF_(0x80094800)
#define CERTSRV_E_NO_CERT_TYPE _HRESULT_TYPEDEF_(0x80094801)
#define CERTSRV_E_TEMPLATE_CONFLICT _HRESULT_TYPEDEF_(0x80094802)
#define CERTSRV_E_SUBJECT_ALT_NAME_REQUIRED _HRESULT_TYPEDEF_(0x80094803)
#define CERTSRV_E_ARCHIVED_KEY_REQUIRED _HRESULT_TYPEDEF_(0x80094804)
#define CERTSRV_E_SMIME_REQUIRED _HRESULT_TYPEDEF_(0x80094805)
#define CERTSRV_E_BAD_RENEWAL_SUBJECT _HRESULT_TYPEDEF_(0x80094806)
#define CERTSRV_E_BAD_TEMPLATE_VERSION _HRESULT_TYPEDEF_(0x80094807)
#define CERTSRV_E_TEMPLATE_POLICY_REQUIRED _HRESULT_TYPEDEF_(0x80094808)
#define CERTSRV_E_SIGNATURE_POLICY_REQUIRED _HRESULT_TYPEDEF_(0x80094809)
#define CERTSRV_E_SIGNATURE_COUNT _HRESULT_TYPEDEF_(0x8009480A)
#define CERTSRV_E_SIGNATURE_REJECTED _HRESULT_TYPEDEF_(0x8009480B)
#define CERTSRV_E_ISSUANCE_POLICY_REQUIRED _HRESULT_TYPEDEF_(0x8009480C)
#define CERTSRV_E_SUBJECT_UPN_REQUIRED _HRESULT_TYPEDEF_(0x8009480D)
#define CERTSRV_E_SUBJECT_DIRECTORY_GUID_REQUIRED _HRESULT_TYPEDEF_(0x8009480E)
#define CERTSRV_E_SUBJECT_DNS_REQUIRED _HRESULT_TYPEDEF_(0x8009480F)
#define CERTSRV_E_ARCHIVED_KEY_UNEXPECTED _HRESULT_TYPEDEF_(0x80094810)
#define CERTSRV_E_KEY_LENGTH _HRESULT_TYPEDEF_(0x80094811)
#define CERTSRV_E_SUBJECT_EMAIL_REQUIRED _HRESULT_TYPEDEF_(0x80094812)
#define CERTSRV_E_UNKNOWN_CERT_TYPE _HRESULT_TYPEDEF_(0x80094813)
#define CERTSRV_E_CERT_TYPE_OVERLAP _HRESULT_TYPEDEF_(0x80094814)
#define XENROLL_E_KEY_NOT_EXPORTABLE _HRESULT_TYPEDEF_(0x80095000)
#define XENROLL_E_CANNOT_ADD_ROOT_CERT _HRESULT_TYPEDEF_(0x80095001)
#define XENROLL_E_RESPONSE_KA_HASH_NOT_FOUND _HRESULT_TYPEDEF_(0x80095002)
#define XENROLL_E_RESPONSE_UNEXPECTED_KA_HASH _HRESULT_TYPEDEF_(0x80095003)
#define XENROLL_E_RESPONSE_KA_HASH_MISMATCH _HRESULT_TYPEDEF_(0x80095004)
#define XENROLL_E_KEYSPEC_SMIME_MISMATCH _HRESULT_TYPEDEF_(0x80095005)
#define TRUST_E_SYSTEM_ERROR _HRESULT_TYPEDEF_(0x80096001)
#define TRUST_E_NO_SIGNER_CERT _HRESULT_TYPEDEF_(0x80096002)
#define TRUST_E_COUNTER_SIGNER _HRESULT_TYPEDEF_(0x80096003)
#define TRUST_E_CERT_SIGNATURE _HRESULT_TYPEDEF_(0x80096004)
#define TRUST_E_TIME_STAMP _HRESULT_TYPEDEF_(0x80096005)
#define TRUST_E_BAD_DIGEST _HRESULT_TYPEDEF_(0x80096010)
#define TRUST_E_BASIC_CONSTRAINTS _HRESULT_TYPEDEF_(0x80096019)
#define TRUST_E_FINANCIAL_CRITERIA _HRESULT_TYPEDEF_(0x8009601E)
#define MSSIPOTF_E_OUTOFMEMRANGE _HRESULT_TYPEDEF_(0x80097001)
#define MSSIPOTF_E_CANTGETOBJECT _HRESULT_TYPEDEF_(0x80097002)
#define MSSIPOTF_E_NOHEADTABLE _HRESULT_TYPEDEF_(0x80097003)
#define MSSIPOTF_E_BAD_MAGICNUMBER _HRESULT_TYPEDEF_(0x80097004)
#define MSSIPOTF_E_BAD_OFFSET_TABLE _HRESULT_TYPEDEF_(0x80097005)
#define MSSIPOTF_E_TABLE_TAGORDER _HRESULT_TYPEDEF_(0x80097006)
#define MSSIPOTF_E_TABLE_LONGWORD _HRESULT_TYPEDEF_(0x80097007)
#define MSSIPOTF_E_BAD_FIRST_TABLE_PLACEMENT _HRESULT_TYPEDEF_(0x80097008)
#define MSSIPOTF_E_TABLES_OVERLAP _HRESULT_TYPEDEF_(0x80097009)
#define MSSIPOTF_E_TABLE_PADBYTES _HRESULT_TYPEDEF_(0x8009700A)
#define MSSIPOTF_E_FILETOOSMALL _HRESULT_TYPEDEF_(0x8009700B)
#define MSSIPOTF_E_TABLE_CHECKSUM _HRESULT_TYPEDEF_(0x8009700C)
#define MSSIPOTF_E_FILE_CHECKSUM _HRESULT_TYPEDEF_(0x8009700D)
#define MSSIPOTF_E_FAILED_POLICY _HRESULT_TYPEDEF_(0x80097010)
#define MSSIPOTF_E_FAILED_HINTS_CHECK _HRESULT_TYPEDEF_(0x80097011)
#define MSSIPOTF_E_NOT_OPENTYPE _HRESULT_TYPEDEF_(0x80097012)
#define MSSIPOTF_E_FILE _HRESULT_TYPEDEF_(0x80097013)
#define MSSIPOTF_E_CRYPT _HRESULT_TYPEDEF_(0x80097014)
#define MSSIPOTF_E_BADVERSION _HRESULT_TYPEDEF_(0x80097015)
#define MSSIPOTF_E_DSIG_STRUCTURE _HRESULT_TYPEDEF_(0x80097016)
#define MSSIPOTF_E_PCONST_CHECK _HRESULT_TYPEDEF_(0x80097017)
#define MSSIPOTF_E_STRUCTURE _HRESULT_TYPEDEF_(0x80097018)
#define NTE_OP_OK 0
#define TRUST_E_PROVIDER_UNKNOWN _HRESULT_TYPEDEF_(0x800B0001)
#define TRUST_E_ACTION_UNKNOWN _HRESULT_TYPEDEF_(0x800B0002)
#define TRUST_E_SUBJECT_FORM_UNKNOWN _HRESULT_TYPEDEF_(0x800B0003)
#define TRUST_E_SUBJECT_NOT_TRUSTED _HRESULT_TYPEDEF_(0x800B0004)
#define DIGSIG_E_ENCODE _HRESULT_TYPEDEF_(0x800B0005)
#define DIGSIG_E_DECODE _HRESULT_TYPEDEF_(0x800B0006)
#define DIGSIG_E_EXTENSIBILITY _HRESULT_TYPEDEF_(0x800B0007)
#define DIGSIG_E_CRYPTO _HRESULT_TYPEDEF_(0x800B0008)
#define PERSIST_E_SIZEDEFINITE _HRESULT_TYPEDEF_(0x800B0009)
#define PERSIST_E_SIZEINDEFINITE _HRESULT_TYPEDEF_(0x800B000A)
#define PERSIST_E_NOTSELFSIZING _HRESULT_TYPEDEF_(0x800B000B)
#define TRUST_E_NOSIGNATURE _HRESULT_TYPEDEF_(0x800B0100)
#define CERT_E_EXPIRED _HRESULT_TYPEDEF_(0x800B0101)
#define CERT_E_VALIDITYPERIODNESTING _HRESULT_TYPEDEF_(0x800B0102)
#define CERT_E_ROLE _HRESULT_TYPEDEF_(0x800B0103)
#define CERT_E_PATHLENCONST _HRESULT_TYPEDEF_(0x800B0104)
#define CERT_E_CRITICAL _HRESULT_TYPEDEF_(0x800B0105)
#define CERT_E_PURPOSE _HRESULT_TYPEDEF_(0x800B0106)
#define CERT_E_ISSUERCHAINING _HRESULT_TYPEDEF_(0x800B0107)
#define CERT_E_MALFORMED _HRESULT_TYPEDEF_(0x800B0108)
#define CERT_E_UNTRUSTEDROOT _HRESULT_TYPEDEF_(0x800B0109)
#define CERT_E_CHAINING _HRESULT_TYPEDEF_(0x800B010A)
#define TRUST_E_FAIL _HRESULT_TYPEDEF_(0x800B010B)
#define CERT_E_REVOKED _HRESULT_TYPEDEF_(0x800B010C)
#define CERT_E_UNTRUSTEDTESTROOT _HRESULT_TYPEDEF_(0x800B010D)
#define CERT_E_REVOCATION_FAILURE _HRESULT_TYPEDEF_(0x800B010E)
#define CERT_E_CN_NO_MATCH _HRESULT_TYPEDEF_(0x800B010F)
#define CERT_E_WRONG_USAGE _HRESULT_TYPEDEF_(0x800B0110)
#define TRUST_E_EXPLICIT_DISTRUST _HRESULT_TYPEDEF_(0x800B0111)
#define CERT_E_UNTRUSTEDCA _HRESULT_TYPEDEF_(0x800B0112)
#define CERT_E_INVALID_POLICY _HRESULT_TYPEDEF_(0x800B0113)
#define CERT_E_INVALID_NAME _HRESULT_TYPEDEF_(0x800B0114)
#define HRESULT_FROM_SETUPAPI(x) ((((x) & (APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR))==(APPLICATION_ERROR_MASK|ERROR_SEVERITY_ERROR)) ? ((HRESULT) (((x) & 0x0000FFFF) | (FACILITY_SETUPAPI << 16) | 0x80000000)) : HRESULT_FROM_WIN32(x))
#define SPAPI_E_EXPECTED_SECTION_NAME _HRESULT_TYPEDEF_(0x800F0000)
#define SPAPI_E_BAD_SECTION_NAME_LINE _HRESULT_TYPEDEF_(0x800F0001)
#define SPAPI_E_SECTION_NAME_TOO_LONG _HRESULT_TYPEDEF_(0x800F0002)
#define SPAPI_E_GENERAL_SYNTAX _HRESULT_TYPEDEF_(0x800F0003)
#define SPAPI_E_WRONG_INF_STYLE _HRESULT_TYPEDEF_(0x800F0100)
#define SPAPI_E_SECTION_NOT_FOUND _HRESULT_TYPEDEF_(0x800F0101)
#define SPAPI_E_LINE_NOT_FOUND _HRESULT_TYPEDEF_(0x800F0102)
#define SPAPI_E_NO_BACKUP _HRESULT_TYPEDEF_(0x800F0103)
#define SPAPI_E_NO_ASSOCIATED_CLASS _HRESULT_TYPEDEF_(0x800F0200)
#define SPAPI_E_CLASS_MISMATCH _HRESULT_TYPEDEF_(0x800F0201)
#define SPAPI_E_DUPLICATE_FOUND _HRESULT_TYPEDEF_(0x800F0202)
#define SPAPI_E_NO_DRIVER_SELECTED _HRESULT_TYPEDEF_(0x800F0203)
#define SPAPI_E_KEY_DOES_NOT_EXIST _HRESULT_TYPEDEF_(0x800F0204)
#define SPAPI_E_INVALID_DEVINST_NAME _HRESULT_TYPEDEF_(0x800F0205)
#define SPAPI_E_INVALID_CLASS _HRESULT_TYPEDEF_(0x800F0206)
#define SPAPI_E_DEVINST_ALREADY_EXISTS _HRESULT_TYPEDEF_(0x800F0207)
#define SPAPI_E_DEVINFO_NOT_REGISTERED _HRESULT_TYPEDEF_(0x800F0208)
#define SPAPI_E_INVALID_REG_PROPERTY _HRESULT_TYPEDEF_(0x800F0209)
#define SPAPI_E_NO_INF _HRESULT_TYPEDEF_(0x800F020A)
#define SPAPI_E_NO_SUCH_DEVINST _HRESULT_TYPEDEF_(0x800F020B)
#define SPAPI_E_CANT_LOAD_CLASS_ICON _HRESULT_TYPEDEF_(0x800F020C)
#define SPAPI_E_INVALID_CLASS_INSTALLER _HRESULT_TYPEDEF_(0x800F020D)
#define SPAPI_E_DI_DO_DEFAULT _HRESULT_TYPEDEF_(0x800F020E)
#define SPAPI_E_DI_NOFILECOPY _HRESULT_TYPEDEF_(0x800F020F)
#define SPAPI_E_INVALID_HWPROFILE _HRESULT_TYPEDEF_(0x800F0210)
#define SPAPI_E_NO_DEVICE_SELECTED _HRESULT_TYPEDEF_(0x800F0211)
#define SPAPI_E_DEVINFO_LIST_LOCKED _HRESULT_TYPEDEF_(0x800F0212)
#define SPAPI_E_DEVINFO_DATA_LOCKED _HRESULT_TYPEDEF_(0x800F0213)
#define SPAPI_E_DI_BAD_PATH _HRESULT_TYPEDEF_(0x800F0214)
#define SPAPI_E_NO_CLASSINSTALL_PARAMS _HRESULT_TYPEDEF_(0x800F0215)
#define SPAPI_E_FILEQUEUE_LOCKED _HRESULT_TYPEDEF_(0x800F0216)
#define SPAPI_E_BAD_SERVICE_INSTALLSECT _HRESULT_TYPEDEF_(0x800F0217)
#define SPAPI_E_NO_CLASS_DRIVER_LIST _HRESULT_TYPEDEF_(0x800F0218)
#define SPAPI_E_NO_ASSOCIATED_SERVICE _HRESULT_TYPEDEF_(0x800F0219)
#define SPAPI_E_NO_DEFAULT_DEVICE_INTERFACE _HRESULT_TYPEDEF_(0x800F021A)
#define SPAPI_E_DEVICE_INTERFACE_ACTIVE _HRESULT_TYPEDEF_(0x800F021B)
#define SPAPI_E_DEVICE_INTERFACE_REMOVED _HRESULT_TYPEDEF_(0x800F021C)
#define SPAPI_E_BAD_INTERFACE_INSTALLSECT _HRESULT_TYPEDEF_(0x800F021D)
#define SPAPI_E_NO_SUCH_INTERFACE_CLASS _HRESULT_TYPEDEF_(0x800F021E)
#define SPAPI_E_INVALID_REFERENCE_STRING _HRESULT_TYPEDEF_(0x800F021F)
#define SPAPI_E_INVALID_MACHINENAME _HRESULT_TYPEDEF_(0x800F0220)
#define SPAPI_E_REMOTE_COMM_FAILURE _HRESULT_TYPEDEF_(0x800F0221)
#define SPAPI_E_MACHINE_UNAVAILABLE _HRESULT_TYPEDEF_(0x800F0222)
#define SPAPI_E_NO_CONFIGMGR_SERVICES _HRESULT_TYPEDEF_(0x800F0223)
#define SPAPI_E_INVALID_PROPPAGE_PROVIDER _HRESULT_TYPEDEF_(0x800F0224)
#define SPAPI_E_NO_SUCH_DEVICE_INTERFACE _HRESULT_TYPEDEF_(0x800F0225)
#define SPAPI_E_DI_POSTPROCESSING_REQUIRED _HRESULT_TYPEDEF_(0x800F0226)
#define SPAPI_E_INVALID_COINSTALLER _HRESULT_TYPEDEF_(0x800F0227)
#define SPAPI_E_NO_COMPAT_DRIVERS _HRESULT_TYPEDEF_(0x800F0228)
#define SPAPI_E_NO_DEVICE_ICON _HRESULT_TYPEDEF_(0x800F0229)
#define SPAPI_E_INVALID_INF_LOGCONFIG _HRESULT_TYPEDEF_(0x800F022A)
#define SPAPI_E_DI_DONT_INSTALL _HRESULT_TYPEDEF_(0x800F022B)
#define SPAPI_E_INVALID_FILTER_DRIVER _HRESULT_TYPEDEF_(0x800F022C)
#define SPAPI_E_NON_WINDOWS_NT_DRIVER _HRESULT_TYPEDEF_(0x800F022D)
#define SPAPI_E_NON_WINDOWS_DRIVER _HRESULT_TYPEDEF_(0x800F022E)
#define SPAPI_E_NO_CATALOG_FOR_OEM_INF _HRESULT_TYPEDEF_(0x800F022F)
#define SPAPI_E_DEVINSTALL_QUEUE_NONNATIVE _HRESULT_TYPEDEF_(0x800F0230)
#define SPAPI_E_NOT_DISABLEABLE _HRESULT_TYPEDEF_(0x800F0231)
#define SPAPI_E_CANT_REMOVE_DEVINST _HRESULT_TYPEDEF_(0x800F0232)
#define SPAPI_E_INVALID_TARGET _HRESULT_TYPEDEF_(0x800F0233)
#define SPAPI_E_DRIVER_NONNATIVE _HRESULT_TYPEDEF_(0x800F0234)
#define SPAPI_E_IN_WOW64 _HRESULT_TYPEDEF_(0x800F0235)
#define SPAPI_E_SET_SYSTEM_RESTORE_POINT _HRESULT_TYPEDEF_(0x800F0236)
#define SPAPI_E_INCORRECTLY_COPIED_INF _HRESULT_TYPEDEF_(0x800F0237)
#define SPAPI_E_SCE_DISABLED _HRESULT_TYPEDEF_(0x800F0238)
#define SPAPI_E_UNKNOWN_EXCEPTION _HRESULT_TYPEDEF_(0x800F0239)
#define SPAPI_E_PNP_REGISTRY_ERROR _HRESULT_TYPEDEF_(0x800F023A)
#define SPAPI_E_REMOTE_REQUEST_UNSUPPORTED _HRESULT_TYPEDEF_(0x800F023B)
#define SPAPI_E_NOT_AN_INSTALLED_OEM_INF _HRESULT_TYPEDEF_(0x800F023C)
#define SPAPI_E_INF_IN_USE_BY_DEVICES _HRESULT_TYPEDEF_(0x800F023D)
#define SPAPI_E_DI_FUNCTION_OBSOLETE _HRESULT_TYPEDEF_(0x800F023E)
#define SPAPI_E_NO_AUTHENTICODE_CATALOG _HRESULT_TYPEDEF_(0x800F023F)
#define SPAPI_E_AUTHENTICODE_DISALLOWED _HRESULT_TYPEDEF_(0x800F0240)
#define SPAPI_E_AUTHENTICODE_TRUSTED_PUBLISHER _HRESULT_TYPEDEF_(0x800F0241)
#define SPAPI_E_AUTHENTICODE_TRUST_NOT_ESTABLISHED _HRESULT_TYPEDEF_(0x800F0242)
#define SPAPI_E_AUTHENTICODE_PUBLISHER_NOT_TRUSTED _HRESULT_TYPEDEF_(0x800F0243)
#define SPAPI_E_SIGNATURE_OSATTRIBUTE_MISMATCH _HRESULT_TYPEDEF_(0x800F0244)
#define SPAPI_E_ONLY_VALIDATE_VIA_AUTHENTICODE _HRESULT_TYPEDEF_(0x800F0245)
#define SPAPI_E_UNRECOVERABLE_STACK_OVERFLOW _HRESULT_TYPEDEF_(0x800F0300)
#define SPAPI_E_ERROR_NOT_INSTALLED _HRESULT_TYPEDEF_(0x800F1000)
#define SCARD_S_SUCCESS NO_ERROR
#define SCARD_F_INTERNAL_ERROR _HRESULT_TYPEDEF_(0x80100001)
#define SCARD_E_CANCELLED _HRESULT_TYPEDEF_(0x80100002)
#define SCARD_E_INVALID_HANDLE _HRESULT_TYPEDEF_(0x80100003)
#define SCARD_E_INVALID_PARAMETER _HRESULT_TYPEDEF_(0x80100004)
#define SCARD_E_INVALID_TARGET _HRESULT_TYPEDEF_(0x80100005)
#define SCARD_E_NO_MEMORY _HRESULT_TYPEDEF_(0x80100006)
#define SCARD_F_WAITED_TOO_LONG _HRESULT_TYPEDEF_(0x80100007)
#define SCARD_E_INSUFFICIENT_BUFFER _HRESULT_TYPEDEF_(0x80100008)
#define SCARD_E_UNKNOWN_READER _HRESULT_TYPEDEF_(0x80100009)
#define SCARD_E_TIMEOUT _HRESULT_TYPEDEF_(0x8010000A)
#define SCARD_E_SHARING_VIOLATION _HRESULT_TYPEDEF_(0x8010000B)
#define SCARD_E_NO_SMARTCARD _HRESULT_TYPEDEF_(0x8010000C)
#define SCARD_E_UNKNOWN_CARD _HRESULT_TYPEDEF_(0x8010000D)
#define SCARD_E_CANT_DISPOSE _HRESULT_TYPEDEF_(0x8010000E)
#define SCARD_E_PROTO_MISMATCH _HRESULT_TYPEDEF_(0x8010000F)
#define SCARD_E_NOT_READY _HRESULT_TYPEDEF_(0x80100010)
#define SCARD_E_INVALID_VALUE _HRESULT_TYPEDEF_(0x80100011)
#define SCARD_E_SYSTEM_CANCELLED _HRESULT_TYPEDEF_(0x80100012)
#define SCARD_F_COMM_ERROR _HRESULT_TYPEDEF_(0x80100013)
#define SCARD_F_UNKNOWN_ERROR _HRESULT_TYPEDEF_(0x80100014)
#define SCARD_E_INVALID_ATR _HRESULT_TYPEDEF_(0x80100015)
#define SCARD_E_NOT_TRANSACTED _HRESULT_TYPEDEF_(0x80100016)
#define SCARD_E_READER_UNAVAILABLE _HRESULT_TYPEDEF_(0x80100017)
#define SCARD_P_SHUTDOWN _HRESULT_TYPEDEF_(0x80100018)
#define SCARD_E_PCI_TOO_SMALL _HRESULT_TYPEDEF_(0x80100019)
#define SCARD_E_READER_UNSUPPORTED _HRESULT_TYPEDEF_(0x8010001A)
#define SCARD_E_DUPLICATE_READER _HRESULT_TYPEDEF_(0x8010001B)
#define SCARD_E_CARD_UNSUPPORTED _HRESULT_TYPEDEF_(0x8010001C)
#define SCARD_E_NO_SERVICE _HRESULT_TYPEDEF_(0x8010001D)
#define SCARD_E_SERVICE_STOPPED _HRESULT_TYPEDEF_(0x8010001E)
#define SCARD_E_UNEXPECTED _HRESULT_TYPEDEF_(0x8010001F)
#define SCARD_E_ICC_INSTALLATION _HRESULT_TYPEDEF_(0x80100020)
#define SCARD_E_ICC_CREATEORDER _HRESULT_TYPEDEF_(0x80100021)
#define SCARD_E_UNSUPPORTED_FEATURE _HRESULT_TYPEDEF_(0x80100022)
#define SCARD_E_DIR_NOT_FOUND _HRESULT_TYPEDEF_(0x80100023)
#define SCARD_E_FILE_NOT_FOUND _HRESULT_TYPEDEF_(0x80100024)
#define SCARD_E_NO_DIR _HRESULT_TYPEDEF_(0x80100025)
#define SCARD_E_NO_FILE _HRESULT_TYPEDEF_(0x80100026)
#define SCARD_E_NO_ACCESS _HRESULT_TYPEDEF_(0x80100027)
#define SCARD_E_WRITE_TOO_MANY _HRESULT_TYPEDEF_(0x80100028)
#define SCARD_E_BAD_SEEK _HRESULT_TYPEDEF_(0x80100029)
#define SCARD_E_INVALID_CHV _HRESULT_TYPEDEF_(0x8010002A)
#define SCARD_E_UNKNOWN_RES_MNG _HRESULT_TYPEDEF_(0x8010002B)
#define SCARD_E_NO_SUCH_CERTIFICATE _HRESULT_TYPEDEF_(0x8010002C)
#define SCARD_E_CERTIFICATE_UNAVAILABLE _HRESULT_TYPEDEF_(0x8010002D)
#define SCARD_E_NO_READERS_AVAILABLE _HRESULT_TYPEDEF_(0x8010002E)
#define SCARD_E_COMM_DATA_LOST _HRESULT_TYPEDEF_(0x8010002F)
#define SCARD_E_NO_KEY_CONTAINER _HRESULT_TYPEDEF_(0x80100030)
#define SCARD_E_SERVER_TOO_BUSY _HRESULT_TYPEDEF_(0x80100031)
#define SCARD_E_PIN_CACHE_EXPIRED _HRESULT_TYPEDEF_(0x80100032)
#define SCARD_E_NO_PIN_CACHE _HRESULT_TYPEDEF_(0x80100033)
#define SCARD_E_READ_ONLY_CARD _HRESULT_TYPEDEF_(0x80100034)
#define SCARD_W_UNSUPPORTED_CARD _HRESULT_TYPEDEF_(0x80100065)
#define SCARD_W_UNRESPONSIVE_CARD _HRESULT_TYPEDEF_(0x80100066)
#define SCARD_W_UNPOWERED_CARD _HRESULT_TYPEDEF_(0x80100067)
#define SCARD_W_RESET_CARD _HRESULT_TYPEDEF_(0x80100068)
#define SCARD_W_REMOVED_CARD _HRESULT_TYPEDEF_(0x80100069)
#define SCARD_W_SECURITY_VIOLATION _HRESULT_TYPEDEF_(0x8010006A)
#define SCARD_W_WRONG_CHV _HRESULT_TYPEDEF_(0x8010006B)
#define SCARD_W_CHV_BLOCKED _HRESULT_TYPEDEF_(0x8010006C)
#define SCARD_W_EOF _HRESULT_TYPEDEF_(0x8010006D)
#define SCARD_W_CANCELLED_BY_USER _HRESULT_TYPEDEF_(0x8010006E)
#define SCARD_W_CARD_NOT_AUTHENTICATED _HRESULT_TYPEDEF_(0x8010006F)
#define SCARD_W_CACHE_ITEM_NOT_FOUND _HRESULT_TYPEDEF_(0x80100070)
#define SCARD_W_CACHE_ITEM_STALE _HRESULT_TYPEDEF_(0x80100071)
#define SCARD_W_CACHE_ITEM_TOO_BIG _HRESULT_TYPEDEF_(0x80100072)
#define COMADMIN_E_OBJECTERRORS _HRESULT_TYPEDEF_(0x80110401)
#define COMADMIN_E_OBJECTINVALID _HRESULT_TYPEDEF_(0x80110402)
#define COMADMIN_E_KEYMISSING _HRESULT_TYPEDEF_(0x80110403)
#define COMADMIN_E_ALREADYINSTALLED _HRESULT_TYPEDEF_(0x80110404)
#define COMADMIN_E_APP_FILE_WRITEFAIL _HRESULT_TYPEDEF_(0x80110407)
#define COMADMIN_E_APP_FILE_READFAIL _HRESULT_TYPEDEF_(0x80110408)
#define COMADMIN_E_APP_FILE_VERSION _HRESULT_TYPEDEF_(0x80110409)
#define COMADMIN_E_BADPATH _HRESULT_TYPEDEF_(0x8011040A)
#define COMADMIN_E_APPLICATIONEXISTS _HRESULT_TYPEDEF_(0x8011040B)
#define COMADMIN_E_ROLEEXISTS _HRESULT_TYPEDEF_(0x8011040C)
#define COMADMIN_E_CANTCOPYFILE _HRESULT_TYPEDEF_(0x8011040D)
#define COMADMIN_E_NOUSER _HRESULT_TYPEDEF_(0x8011040F)
#define COMADMIN_E_INVALIDUSERIDS _HRESULT_TYPEDEF_(0x80110410)
#define COMADMIN_E_NOREGISTRYCLSID _HRESULT_TYPEDEF_(0x80110411)
#define COMADMIN_E_BADREGISTRYPROGID _HRESULT_TYPEDEF_(0x80110412)
#define COMADMIN_E_AUTHENTICATIONLEVEL _HRESULT_TYPEDEF_(0x80110413)
#define COMADMIN_E_USERPASSWDNOTVALID _HRESULT_TYPEDEF_(0x80110414)
#define COMADMIN_E_CLSIDORIIDMISMATCH _HRESULT_TYPEDEF_(0x80110418)
#define COMADMIN_E_REMOTEINTERFACE _HRESULT_TYPEDEF_(0x80110419)
#define COMADMIN_E_DLLREGISTERSERVER _HRESULT_TYPEDEF_(0x8011041A)
#define COMADMIN_E_NOSERVERSHARE _HRESULT_TYPEDEF_(0x8011041B)
#define COMADMIN_E_DLLLOADFAILED _HRESULT_TYPEDEF_(0x8011041D)
#define COMADMIN_E_BADREGISTRYLIBID _HRESULT_TYPEDEF_(0x8011041E)
#define COMADMIN_E_APPDIRNOTFOUND _HRESULT_TYPEDEF_(0x8011041F)
#define COMADMIN_E_REGISTRARFAILED _HRESULT_TYPEDEF_(0x80110423)
#define COMADMIN_E_COMPFILE_DOESNOTEXIST _HRESULT_TYPEDEF_(0x80110424)
#define COMADMIN_E_COMPFILE_LOADDLLFAIL _HRESULT_TYPEDEF_(0x80110425)
#define COMADMIN_E_COMPFILE_GETCLASSOBJ _HRESULT_TYPEDEF_(0x80110426)
#define COMADMIN_E_COMPFILE_CLASSNOTAVAIL _HRESULT_TYPEDEF_(0x80110427)
#define COMADMIN_E_COMPFILE_BADTLB _HRESULT_TYPEDEF_(0x80110428)
#define COMADMIN_E_COMPFILE_NOTINSTALLABLE _HRESULT_TYPEDEF_(0x80110429)
#define COMADMIN_E_NOTCHANGEABLE _HRESULT_TYPEDEF_(0x8011042A)
#define COMADMIN_E_NOTDELETEABLE _HRESULT_TYPEDEF_(0x8011042B)
#define COMADMIN_E_SESSION _HRESULT_TYPEDEF_(0x8011042C)
#define COMADMIN_E_COMP_MOVE_LOCKED _HRESULT_TYPEDEF_(0x8011042D)
#define COMADMIN_E_COMP_MOVE_BAD_DEST _HRESULT_TYPEDEF_(0x8011042E)
#define COMADMIN_E_REGISTERTLB _HRESULT_TYPEDEF_(0x80110430)
#define COMADMIN_E_SYSTEMAPP _HRESULT_TYPEDEF_(0x80110433)
#define COMADMIN_E_COMPFILE_NOREGISTRAR _HRESULT_TYPEDEF_(0x80110434)
#define COMADMIN_E_COREQCOMPINSTALLED _HRESULT_TYPEDEF_(0x80110435)
#define COMADMIN_E_SERVICENOTINSTALLED _HRESULT_TYPEDEF_(0x80110436)
#define COMADMIN_E_PROPERTYSAVEFAILED _HRESULT_TYPEDEF_(0x80110437)
#define COMADMIN_E_OBJECTEXISTS _HRESULT_TYPEDEF_(0x80110438)
#define COMADMIN_E_COMPONENTEXISTS _HRESULT_TYPEDEF_(0x80110439)
#define COMADMIN_E_REGFILE_CORRUPT _HRESULT_TYPEDEF_(0x8011043B)
#define COMADMIN_E_PROPERTY_OVERFLOW _HRESULT_TYPEDEF_(0x8011043C)
#define COMADMIN_E_NOTINREGISTRY _HRESULT_TYPEDEF_(0x8011043E)
#define COMADMIN_E_OBJECTNOTPOOLABLE _HRESULT_TYPEDEF_(0x8011043F)
#define COMADMIN_E_APPLID_MATCHES_CLSID _HRESULT_TYPEDEF_(0x80110446)
#define COMADMIN_E_ROLE_DOES_NOT_EXIST _HRESULT_TYPEDEF_(0x80110447)
#define COMADMIN_E_START_APP_NEEDS_COMPONENTS _HRESULT_TYPEDEF_(0x80110448)
#define COMADMIN_E_REQUIRES_DIFFERENT_PLATFORM _HRESULT_TYPEDEF_(0x80110449)
#define COMADMIN_E_CAN_NOT_EXPORT_APP_PROXY _HRESULT_TYPEDEF_(0x8011044A)
#define COMADMIN_E_CAN_NOT_START_APP _HRESULT_TYPEDEF_(0x8011044B)
#define COMADMIN_E_CAN_NOT_EXPORT_SYS_APP _HRESULT_TYPEDEF_(0x8011044C)
#define COMADMIN_E_CANT_SUBSCRIBE_TO_COMPONENT _HRESULT_TYPEDEF_(0x8011044D)
#define COMADMIN_E_EVENTCLASS_CANT_BE_SUBSCRIBER _HRESULT_TYPEDEF_(0x8011044E)
#define COMADMIN_E_LIB_APP_PROXY_INCOMPATIBLE _HRESULT_TYPEDEF_(0x8011044F)
#define COMADMIN_E_BASE_PARTITION_ONLY _HRESULT_TYPEDEF_(0x80110450)
#define COMADMIN_E_START_APP_DISABLED _HRESULT_TYPEDEF_(0x80110451)
#define COMADMIN_E_CAT_DUPLICATE_PARTITION_NAME _HRESULT_TYPEDEF_(0x80110457)
#define COMADMIN_E_CAT_INVALID_PARTITION_NAME _HRESULT_TYPEDEF_(0x80110458)
#define COMADMIN_E_CAT_PARTITION_IN_USE _HRESULT_TYPEDEF_(0x80110459)
#define COMADMIN_E_FILE_PARTITION_DUPLICATE_FILES _HRESULT_TYPEDEF_(0x8011045A)
#define COMADMIN_E_CAT_IMPORTED_COMPONENTS_NOT_ALLOWED _HRESULT_TYPEDEF_(0x8011045B)
#define COMADMIN_E_AMBIGUOUS_APPLICATION_NAME _HRESULT_TYPEDEF_(0x8011045C)
#define COMADMIN_E_AMBIGUOUS_PARTITION_NAME _HRESULT_TYPEDEF_(0x8011045D)
#define COMADMIN_E_REGDB_NOTINITIALIZED _HRESULT_TYPEDEF_(0x80110472)
#define COMADMIN_E_REGDB_NOTOPEN _HRESULT_TYPEDEF_(0x80110473)
#define COMADMIN_E_REGDB_SYSTEMERR _HRESULT_TYPEDEF_(0x80110474)
#define COMADMIN_E_REGDB_ALREADYRUNNING _HRESULT_TYPEDEF_(0x80110475)
#define COMADMIN_E_MIG_VERSIONNOTSUPPORTED _HRESULT_TYPEDEF_(0x80110480)
#define COMADMIN_E_MIG_SCHEMANOTFOUND _HRESULT_TYPEDEF_(0x80110481)
#define COMADMIN_E_CAT_BITNESSMISMATCH _HRESULT_TYPEDEF_(0x80110482)
#define COMADMIN_E_CAT_UNACCEPTABLEBITNESS _HRESULT_TYPEDEF_(0x80110483)
#define COMADMIN_E_CAT_WRONGAPPBITNESS _HRESULT_TYPEDEF_(0x80110484)
#define COMADMIN_E_CAT_PAUSE_RESUME_NOT_SUPPORTED _HRESULT_TYPEDEF_(0x80110485)
#define COMADMIN_E_CAT_SERVERFAULT _HRESULT_TYPEDEF_(0x80110486)
#define COMQC_E_APPLICATION_NOT_QUEUED _HRESULT_TYPEDEF_(0x80110600)
#define COMQC_E_NO_QUEUEABLE_INTERFACES _HRESULT_TYPEDEF_(0x80110601)
#define COMQC_E_QUEUING_SERVICE_NOT_AVAILABLE _HRESULT_TYPEDEF_(0x80110602)
#define COMQC_E_NO_IPERSISTSTREAM _HRESULT_TYPEDEF_(0x80110603)
#define COMQC_E_BAD_MESSAGE _HRESULT_TYPEDEF_(0x80110604)
#define COMQC_E_UNAUTHENTICATED _HRESULT_TYPEDEF_(0x80110605)
#define COMQC_E_UNTRUSTED_ENQUEUER _HRESULT_TYPEDEF_(0x80110606)
#define MSDTC_E_DUPLICATE_RESOURCE _HRESULT_TYPEDEF_(0x80110701)
#define COMADMIN_E_OBJECT_PARENT_MISSING _HRESULT_TYPEDEF_(0x80110808)
#define COMADMIN_E_OBJECT_DOES_NOT_EXIST _HRESULT_TYPEDEF_(0x80110809)
#define COMADMIN_E_APP_NOT_RUNNING _HRESULT_TYPEDEF_(0x8011080A)
#define COMADMIN_E_INVALID_PARTITION _HRESULT_TYPEDEF_(0x8011080B)
#define COMADMIN_E_SVCAPP_NOT_POOLABLE_OR_RECYCLABLE _HRESULT_TYPEDEF_(0x8011080D)
#define COMADMIN_E_USER_IN_SET _HRESULT_TYPEDEF_(0x8011080E)
#define COMADMIN_E_CANTRECYCLELIBRARYAPPS _HRESULT_TYPEDEF_(0x8011080F)
#define COMADMIN_E_CANTRECYCLESERVICEAPPS _HRESULT_TYPEDEF_(0x80110811)
#define COMADMIN_E_PROCESSALREADYRECYCLED _HRESULT_TYPEDEF_(0x80110812)
#define COMADMIN_E_PAUSEDPROCESSMAYNOTBERECYCLED _HRESULT_TYPEDEF_(0x80110813)
#define COMADMIN_E_CANTMAKEINPROCSERVICE _HRESULT_TYPEDEF_(0x80110814)
#define COMADMIN_E_PROGIDINUSEBYCLSID _HRESULT_TYPEDEF_(0x80110815)
#define COMADMIN_E_DEFAULT_PARTITION_NOT_IN_SET _HRESULT_TYPEDEF_(0x80110816)
#define COMADMIN_E_RECYCLEDPROCESSMAYNOTBEPAUSED _HRESULT_TYPEDEF_(0x80110817)
#define COMADMIN_E_PARTITION_ACCESSDENIED _HRESULT_TYPEDEF_(0x80110818)
#define COMADMIN_E_PARTITION_MSI_ONLY _HRESULT_TYPEDEF_(0x80110819)
#define COMADMIN_E_LEGACYCOMPS_NOT_ALLOWED_IN_1_0_FORMAT _HRESULT_TYPEDEF_(0x8011081A)
#define COMADMIN_E_LEGACYCOMPS_NOT_ALLOWED_IN_NONBASE_PARTITIONS _HRESULT_TYPEDEF_(0x8011081B)
#define COMADMIN_E_COMP_MOVE_SOURCE _HRESULT_TYPEDEF_(0x8011081C)
#define COMADMIN_E_COMP_MOVE_DEST _HRESULT_TYPEDEF_(0x8011081D)
#define COMADMIN_E_COMP_MOVE_PRIVATE _HRESULT_TYPEDEF_(0x8011081E)
#define COMADMIN_E_BASEPARTITION_REQUIRED_IN_SET _HRESULT_TYPEDEF_(0x8011081F)
#define COMADMIN_E_CANNOT_ALIAS_EVENTCLASS _HRESULT_TYPEDEF_(0x80110820)
#define COMADMIN_E_PRIVATE_ACCESSDENIED _HRESULT_TYPEDEF_(0x80110821)
#define COMADMIN_E_SAFERINVALID _HRESULT_TYPEDEF_(0x80110822)
#define COMADMIN_E_REGISTRY_ACCESSDENIED _HRESULT_TYPEDEF_(0x80110823)
#define COMADMIN_E_PARTITIONS_DISABLED _HRESULT_TYPEDEF_(0x80110824)

#define DXGI_STATUS_OCCLUDED                    _HRESULT_TYPEDEF_(0x087a0001)
#define DXGI_STATUS_CLIPPED                     _HRESULT_TYPEDEF_(0x087a0002)
#define DXGI_STATUS_NO_REDIRECTION              _HRESULT_TYPEDEF_(0x087a0004)
#define DXGI_STATUS_NO_DESKTOP_ACCESS           _HRESULT_TYPEDEF_(0x087a0005)
#define DXGI_STATUS_GRAPHICS_VIDPN_SOURCE_IN_USE _HRESULT_TYPEDEF_(0x087a0006)
#define DXGI_STATUS_MODE_CHANGED                _HRESULT_TYPEDEF_(0x087a0007)
#define DXGI_STATUS_MODE_CHANGE_IN_PROGRESS     _HRESULT_TYPEDEF_(0x087a0008)
#define DXGI_STATUS_UNOCCLUDED                  _HRESULT_TYPEDEF_(0x087a0009)
#define DXGI_STATUS_DDA_WAS_STILL_DRAWING       _HRESULT_TYPEDEF_(0x087a000a)
#define DXGI_STATUS_PRESENT_REQUIRED            _HRESULT_TYPEDEF_(0x087a002f)

#define DXGI_ERROR_INVALID_CALL                 _HRESULT_TYPEDEF_(0x887A0001)
#define DXGI_ERROR_NOT_FOUND                    _HRESULT_TYPEDEF_(0x887A0002)
#define DXGI_ERROR_MORE_DATA                    _HRESULT_TYPEDEF_(0x887A0003)
#define DXGI_ERROR_UNSUPPORTED                  _HRESULT_TYPEDEF_(0x887A0004)
#define DXGI_ERROR_DEVICE_REMOVED               _HRESULT_TYPEDEF_(0x887A0005)
#define DXGI_ERROR_DEVICE_HUNG                  _HRESULT_TYPEDEF_(0x887A0006)
#define DXGI_ERROR_DEVICE_RESET                 _HRESULT_TYPEDEF_(0x887A0007)
#define DXGI_ERROR_WAS_STILL_DRAWING            _HRESULT_TYPEDEF_(0x887A000A)
#define DXGI_ERROR_FRAME_STATISTICS_DISJOINT    _HRESULT_TYPEDEF_(0x887A000B)
#define DXGI_ERROR_GRAPHICS_VIDPN_SOURCE_IN_USE _HRESULT_TYPEDEF_(0x887A000C)
#define DXGI_ERROR_DRIVER_INTERNAL_ERROR        _HRESULT_TYPEDEF_(0x887A0020)
#define DXGI_ERROR_NONEXCLUSIVE                 _HRESULT_TYPEDEF_(0x887A0021)
#define DXGI_ERROR_NOT_CURRENTLY_AVAILABLE      _HRESULT_TYPEDEF_(0x887A0022)
#define DXGI_ERROR_REMOTE_CLIENT_DISCONNECTED   _HRESULT_TYPEDEF_(0x887A0023)
#define DXGI_ERROR_REMOTE_OUTOFMEMORY           _HRESULT_TYPEDEF_(0x887A0024)
#define DXGI_ERROR_ACCESS_LOST                  _HRESULT_TYPEDEF_(0x887A0026)
#define DXGI_ERROR_WAIT_TIMEOUT                 _HRESULT_TYPEDEF_(0x887A0027)
#define DXGI_ERROR_SESSION_DISCONNECTED         _HRESULT_TYPEDEF_(0x887A0028)
#define DXGI_ERROR_RESTRICT_TO_OUTPUT_STALE     _HRESULT_TYPEDEF_(0x887A0029)
#define DXGI_ERROR_CANNOT_PROTECT_CONTENT       _HRESULT_TYPEDEF_(0x887A002A)
#define DXGI_ERROR_ACCESS_DENIED                _HRESULT_TYPEDEF_(0x887A002B)
#define DXGI_ERROR_NAME_ALREADY_EXISTS          _HRESULT_TYPEDEF_(0x887A002C)
#define DXGI_ERROR_SDK_COMPONENT_MISSING        _HRESULT_TYPEDEF_(0x887A002D)
#define DXGI_ERROR_NOT_CURRENT                  _HRESULT_TYPEDEF_(0x887A002E)
#define DXGI_ERROR_HW_PROTECTION_OUTOFMEMORY    _HRESULT_TYPEDEF_(0x887A0030)
#define DXGI_ERROR_DYNAMIC_CODE_POLICY_VIOLATION _HRESULT_TYPEDEF_(0x887A0031)
#define DXGI_ERROR_NON_COMPOSITED_UI            _HRESULT_TYPEDEF_(0x887A0032)
#define DXGI_ERROR_MODE_CHANGE_IN_PROGRESS      _HRESULT_TYPEDEF_(0x887A0025)
#define DXGI_ERROR_CACHE_CORRUPT                _HRESULT_TYPEDEF_(0x887A0033)
#define DXGI_ERROR_CACHE_FULL                   _HRESULT_TYPEDEF_(0x887A0034)
#define DXGI_ERROR_CACHE_HASH_COLLISION         _HRESULT_TYPEDEF_(0x887A0035)
#define DXGI_ERROR_ALREADY_EXISTS               _HRESULT_TYPEDEF_(0x887A0036)
#define DXGI_DDI_ERR_WASSTILLDRAWING            _HRESULT_TYPEDEF_(0x887B0001)
#define DXGI_DDI_ERR_UNSUPPORTED                _HRESULT_TYPEDEF_(0x887B0002)
#define DXGI_DDI_ERR_NONEXCLUSIVE               _HRESULT_TYPEDEF_(0x887B0003)

#define D3D10_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS    _HRESULT_TYPEDEF_(0x88790001)
#define D3D10_ERROR_FILE_NOT_FOUND                   _HRESULT_TYPEDEF_(0x88790002)

#define D3D11_ERROR_TOO_MANY_UNIQUE_STATE_OBJECTS    _HRESULT_TYPEDEF_(0x887C0001)
#define D3D11_ERROR_FILE_NOT_FOUND                   _HRESULT_TYPEDEF_(0x887C0002)
#define D3D11_ERROR_TOO_MANY_UNIQUE_VIEW_OBJECTS     _HRESULT_TYPEDEF_(0x887C0003)
#define D3D11_ERROR_DEFERRED_CONTEXT_MAP_WITHOUT_INITIAL_DISCARD _HRESULT_TYPEDEF_(0x887C0004)

#define D3D12_ERROR_ADAPTER_NOT_FOUND                _HRESULT_TYPEDEF_(0x887E0001)
#define D3D12_ERROR_DRIVER_VERSION_MISMATCH          _HRESULT_TYPEDEF_(0x887E0002)

#ifndef D2DERR_WRONG_STATE
/* may be defined from d2derr.h */
#define D2DERR_WRONG_STATE                           _HRESULT_TYPEDEF_(0x88990001)
#define D2DERR_NOT_INITIALIZED                       _HRESULT_TYPEDEF_(0x88990002)
#define D2DERR_UNSUPPORTED_OPERATION                 _HRESULT_TYPEDEF_(0x88990003)
#define D2DERR_SCANNER_FAILED                        _HRESULT_TYPEDEF_(0x88990004)
#define D2DERR_SCREEN_ACCESS_DENIED                  _HRESULT_TYPEDEF_(0x88990005)
#define D2DERR_DISPLAY_STATE_INVALID                 _HRESULT_TYPEDEF_(0x88990006)
#define D2DERR_ZERO_VECTOR                           _HRESULT_TYPEDEF_(0x88990007)
#define D2DERR_INTERNAL_ERROR                        _HRESULT_TYPEDEF_(0x88990008)
#define D2DERR_DISPLAY_FORMAT_NOT_SUPPORTED          _HRESULT_TYPEDEF_(0x88990009)
#define D2DERR_INVALID_CALL                          _HRESULT_TYPEDEF_(0x8899000A)
#define D2DERR_NO_HARDWARE_DEVICE                    _HRESULT_TYPEDEF_(0x8899000B)
#define D2DERR_RECREATE_TARGET                       _HRESULT_TYPEDEF_(0x8899000C)
#define D2DERR_TOO_MANY_SHADER_ELEMENTS              _HRESULT_TYPEDEF_(0x8899000D)
#define D2DERR_SHADER_COMPILE_FAILED                 _HRESULT_TYPEDEF_(0x8899000E)
#define D2DERR_MAX_TEXTURE_SIZE_EXCEEDED             _HRESULT_TYPEDEF_(0x8899000F)
#define D2DERR_UNSUPPORTED_VERSION                   _HRESULT_TYPEDEF_(0x88990010)
#define D2DERR_BAD_NUMBER                            _HRESULT_TYPEDEF_(0x88990011)
#define D2DERR_WRONG_FACTORY                         _HRESULT_TYPEDEF_(0x88990012)
#define D2DERR_LAYER_ALREADY_IN_USE                  _HRESULT_TYPEDEF_(0x88990013)
#define D2DERR_POP_CALL_DID_NOT_MATCH_PUSH           _HRESULT_TYPEDEF_(0x88990014)
#define D2DERR_WRONG_RESOURCE_DOMAIN                 _HRESULT_TYPEDEF_(0x88990015)
#define D2DERR_PUSH_POP_UNBALANCED                   _HRESULT_TYPEDEF_(0x88990016)
#define D2DERR_RENDER_TARGET_HAS_LAYER_OR_CLIPRECT   _HRESULT_TYPEDEF_(0x88990017)
#define D2DERR_INCOMPATIBLE_BRUSH_TYPES              _HRESULT_TYPEDEF_(0x88990018)
#define D2DERR_WIN32_ERROR                           _HRESULT_TYPEDEF_(0x88990019)
#define D2DERR_TARGET_NOT_GDI_COMPATIBLE             _HRESULT_TYPEDEF_(0x8899001A)
#define D2DERR_TEXT_EFFECT_IS_WRONG_TYPE             _HRESULT_TYPEDEF_(0x8899001B)
#define D2DERR_TEXT_RENDERER_NOT_RELEASED            _HRESULT_TYPEDEF_(0x8899001C)
#define D2DERR_EXCEEDS_MAX_BITMAP_SIZE               _HRESULT_TYPEDEF_(0x8899001D)
#endif
#define D2DERR_INVALID_GRAPH_CONFIGURATION           _HRESULT_TYPEDEF_(0x8899001E)
#define D2DERR_INVALID_INTERNAL_GRAPH_CONFIGURATION  _HRESULT_TYPEDEF_(0x8899001F)
#define D2DERR_CYCLIC_GRAPH                          _HRESULT_TYPEDEF_(0x88990020)
#define D2DERR_BITMAP_CANNOT_DRAW                    _HRESULT_TYPEDEF_(0x88990021)
#define D2DERR_OUTSTANDING_BITMAP_REFERENCES         _HRESULT_TYPEDEF_(0x88990022)
#define D2DERR_ORIGINAL_TARGET_NOT_BOUND             _HRESULT_TYPEDEF_(0x88990023)
#define D2DERR_INVALID_TARGET                        _HRESULT_TYPEDEF_(0x88990024)
#define D2DERR_BITMAP_BOUND_AS_TARGET                _HRESULT_TYPEDEF_(0x88990025)
#define D2DERR_INSUFFICIENT_DEVICE_CAPABILITIES      _HRESULT_TYPEDEF_(0x88990026)
#define D2DERR_INTERMEDIATE_TOO_LARGE                _HRESULT_TYPEDEF_(0x88990027)
#define D2DERR_EFFECT_IS_NOT_REGISTERED              _HRESULT_TYPEDEF_(0x88990028)
#define D2DERR_INVALID_PROPERTY                      _HRESULT_TYPEDEF_(0x88990029)
#define D2DERR_NO_SUBPROPERTIES                      _HRESULT_TYPEDEF_(0x8899002A)
#define D2DERR_PRINT_JOB_CLOSED                      _HRESULT_TYPEDEF_(0x8899002B)
#define D2DERR_PRINT_FORMAT_NOT_SUPPORTED            _HRESULT_TYPEDEF_(0x8899002C)
#define D2DERR_TOO_MANY_TRANSFORM_INPUTS             _HRESULT_TYPEDEF_(0x8899002D)
#define D2DERR_INVALID_GLYPH_IMAGE                   _HRESULT_TYPEDEF_(0x8899002E)

#if (_WIN32_WINNT >= 0x0600)
#define TBS_E_INTERNAL_ERROR          _HRESULT_TYPEDEF_(0x80284001)
#define TBS_E_BAD_PARAMETER           _HRESULT_TYPEDEF_(0x80284002)
#define TBS_E_INVALID_OUTPUT_POINTER  _HRESULT_TYPEDEF_(0x80284003)
#define TBS_E_INSUFFICIENT_BUFFER     _HRESULT_TYPEDEF_(0x80284005)
#define TBS_E_IOERROR                 _HRESULT_TYPEDEF_(0x80284006)
#define TBS_E_INVALID_CONTEXT_PARAM   _HRESULT_TYPEDEF_(0x80284007)
#define TBS_E_SERVICE_NOT_RUNNING     _HRESULT_TYPEDEF_(0x80284008)
#define TBS_E_TOO_MANY_TBS_CONTEXTS   _HRESULT_TYPEDEF_(0x80284009)
#define TBS_E_SERVICE_START_PENDING   _HRESULT_TYPEDEF_(0x8028400B)
#define TBS_E_BUFFER_TOO_LARGE        _HRESULT_TYPEDEF_(0x8028400E)
#define TBS_E_TPM_NOT_FOUND           _HRESULT_TYPEDEF_(0x8028400F)
#define TBS_E_SERVICE_DISABLED        _HRESULT_TYPEDEF_(0x80284010)
#define TBS_E_DEACTIVATED             _HRESULT_TYPEDEF_(0x80284016)

#define FWP_E_CALLOUT_NOT_FOUND                 _HRESULT_TYPEDEF_(0x80320001)
#define FWP_E_CONDITION_NOT_FOUND               _HRESULT_TYPEDEF_(0x80320002)
#define FWP_E_FILTER_NOT_FOUND                  _HRESULT_TYPEDEF_(0x80320003)
#define FWP_E_LAYER_NOT_FOUND                   _HRESULT_TYPEDEF_(0x80320004)
#define FWP_E_PROVIDER_NOT_FOUND                _HRESULT_TYPEDEF_(0x80320005)
#define FWP_E_PROVIDER_CONTEXT_NOT_FOUND        _HRESULT_TYPEDEF_(0x80320006)
#define FWP_E_SUBLAYER_NOT_FOUND                _HRESULT_TYPEDEF_(0x80320007)
#define FWP_E_NOT_FOUND                         _HRESULT_TYPEDEF_(0x80320008)
#define FWP_E_ALREADY_EXISTS                    _HRESULT_TYPEDEF_(0x80320009)
#define FWP_E_IN_USE                            _HRESULT_TYPEDEF_(0x8032000A)
#define FWP_E_DYNAMIC_SESSION_IN_PROGRESS       _HRESULT_TYPEDEF_(0x8032000B)
#define FWP_E_WRONG_SESSION                     _HRESULT_TYPEDEF_(0x8032000C)
#define FWP_E_NO_TXN_IN_PROGRESS                _HRESULT_TYPEDEF_(0x8032000D)
#define FWP_E_TXN_IN_PROGRESS                   _HRESULT_TYPEDEF_(0x8032000E)
#define FWP_E_TXN_ABORTED                       _HRESULT_TYPEDEF_(0x8032000F)
#define FWP_E_SESSION_ABORTED                   _HRESULT_TYPEDEF_(0x80320010)
#define FWP_E_INCOMPATIBLE_TXN                  _HRESULT_TYPEDEF_(0x80320011)
#define FWP_E_TIMEOUT                           _HRESULT_TYPEDEF_(0x80320012)
#define FWP_E_NET_EVENTS_DISABLED               _HRESULT_TYPEDEF_(0x80320013)
#define FWP_E_INCOMPATIBLE_LAYER                _HRESULT_TYPEDEF_(0x80320014)
#define FWP_E_KM_CLIENTS_ONLY                   _HRESULT_TYPEDEF_(0x80320015)
#define FWP_E_LIFETIME_MISMATCH                 _HRESULT_TYPEDEF_(0x80320016)
#define FWP_E_BUILTIN_OBJECT                    _HRESULT_TYPEDEF_(0x80320017)
#define FWP_E_TOO_MANY_CALLOUTS                 _HRESULT_TYPEDEF_(0x80320018)
#define FWP_E_NOTIFICATION_DROPPED              _HRESULT_TYPEDEF_(0x80320019)
#define FWP_E_TRAFFIC_MISMATCH                  _HRESULT_TYPEDEF_(0x8032001A)
#define FWP_E_INCOMPATIBLE_SA_STATE             _HRESULT_TYPEDEF_(0x8032001B)
#define FWP_E_NULL_POINTER                      _HRESULT_TYPEDEF_(0x8032001C)
#define FWP_E_INVALID_ENUMERATOR                _HRESULT_TYPEDEF_(0x8032001D)
#define FWP_E_INVALID_FLAGS                     _HRESULT_TYPEDEF_(0x8032001E)
#define FWP_E_INVALID_NET_MASK                  _HRESULT_TYPEDEF_(0x8032001F)
#define FWP_E_INVALID_RANGE                     _HRESULT_TYPEDEF_(0x80320020)
#define FWP_E_INVALID_INTERVAL                  _HRESULT_TYPEDEF_(0x80320021)
#define FWP_E_ZERO_LENGTH_ARRAY                 _HRESULT_TYPEDEF_(0x80320022)
#define FWP_E_NULL_DISPLAY_NAME                 _HRESULT_TYPEDEF_(0x80320023)
#define FWP_E_INVALID_ACTION_TYPE               _HRESULT_TYPEDEF_(0x80320024)
#define FWP_E_INVALID_WEIGHT                    _HRESULT_TYPEDEF_(0x80320025)
#define FWP_E_MATCH_TYPE_MISMATCH               _HRESULT_TYPEDEF_(0x80320026)
#define FWP_E_TYPE_MISMATCH                     _HRESULT_TYPEDEF_(0x80320027)
#define FWP_E_OUT_OF_BOUNDS                     _HRESULT_TYPEDEF_(0x80320028)
#define FWP_E_RESERVED                          _HRESULT_TYPEDEF_(0x80320029)
#define FWP_E_DUPLICATE_CONDITION               _HRESULT_TYPEDEF_(0x8032002A)
#define FWP_E_DUPLICATE_KEYMOD                  _HRESULT_TYPEDEF_(0x8032002B)
#define FWP_E_ACTION_INCOMPATIBLE_WITH_LAYER    _HRESULT_TYPEDEF_(0x8032002C)
#define FWP_E_ACTION_INCOMPATIBLE_WITH_SUBLAYER _HRESULT_TYPEDEF_(0x8032002D)
#define FWP_E_CONTEXT_INCOMPATIBLE_WITH_LAYER   _HRESULT_TYPEDEF_(0x8032002E)
#define FWP_E_CONTEXT_INCOMPATIBLE_WITH_CALLOUT _HRESULT_TYPEDEF_(0x8032002F)
#define FWP_E_INCOMPATIBLE_AUTH_METHOD          _HRESULT_TYPEDEF_(0x80320030)
#define FWP_E_INCOMPATIBLE_DH_GROUP             _HRESULT_TYPEDEF_(0x80320031)
#define FWP_E_EM_NOT_SUPPORTED                  _HRESULT_TYPEDEF_(0x80320032)
#define FWP_E_NEVER_MATCH                       _HRESULT_TYPEDEF_(0x80320033)
#define FWP_E_PROVIDER_CONTEXT_MISMATCH         _HRESULT_TYPEDEF_(0x80320034)
#define FWP_E_INVALID_PARAMETER                 _HRESULT_TYPEDEF_(0x80320035)
#define FWP_E_TOO_MANY_SUBLAYERS                _HRESULT_TYPEDEF_(0x80320036)
#define FWP_E_CALLOUT_NOTIFICATION_FAILED       _HRESULT_TYPEDEF_(0x80320037)
#define FWP_E_INVALID_AUTH_TRANSFORM            _HRESULT_TYPEDEF_(0x80320038)
#define FWP_E_INVALID_CIPHER_TRANSFORM          _HRESULT_TYPEDEF_(0x80320039)

#endif /*(_WIN32_WINNT >= 0x0600)*/

#define WINCODEC_ERR_WRONGSTATE                            _HRESULT_TYPEDEF_(0x88982f04)
#define WINCODEC_ERR_VALUEOUTOFRANGE                       _HRESULT_TYPEDEF_(0x88982f05)
#define WINCODEC_ERR_UNKNOWNIMAGEFORMAT                    _HRESULT_TYPEDEF_(0x88982f07)
#define WINCODEC_ERR_UNSUPPORTEDVERSION                    _HRESULT_TYPEDEF_(0x88982f0b)
#define WINCODEC_ERR_NOTINITIALIZED                        _HRESULT_TYPEDEF_(0x88982f0c)
#define WINCODEC_ERR_ALREADYLOCKED                         _HRESULT_TYPEDEF_(0x88982f0d)
#define WINCODEC_ERR_PROPERTYNOTFOUND                      _HRESULT_TYPEDEF_(0x88982f40)
#define WINCODEC_ERR_PROPERTYNOTSUPPORTED                  _HRESULT_TYPEDEF_(0x88982f41)
#define WINCODEC_ERR_PROPERTYSIZE                          _HRESULT_TYPEDEF_(0x88982f42)
#define WINCODEC_ERR_CODECPRESENT                          _HRESULT_TYPEDEF_(0x88982f43)
#define WINCODEC_ERR_CODECNOTHUMBNAIL                      _HRESULT_TYPEDEF_(0x88982f44)
#define WINCODEC_ERR_PALETTEUNAVAILABLE                    _HRESULT_TYPEDEF_(0x88982f45)
#define WINCODEC_ERR_CODECTOOMANYSCANLINES                 _HRESULT_TYPEDEF_(0x88982f46)
#define WINCODEC_ERR_INTERNALERROR                         _HRESULT_TYPEDEF_(0x88982f48)
#define WINCODEC_ERR_SOURCERECTDOESNOTMATCHDIMENSIONS      _HRESULT_TYPEDEF_(0x88982f49)
#define WINCODEC_ERR_COMPONENTNOTFOUND                     _HRESULT_TYPEDEF_(0x88982f50)
#define WINCODEC_ERR_IMAGESIZEOUTOFRANGE                   _HRESULT_TYPEDEF_(0x88982f51)
#define WINCODEC_ERR_TOOMUCHMETADATA                       _HRESULT_TYPEDEF_(0x88982f52)
#define WINCODEC_ERR_BADIMAGE                              _HRESULT_TYPEDEF_(0x88982f60)
#define WINCODEC_ERR_BADHEADER                             _HRESULT_TYPEDEF_(0x88982f61)
#define WINCODEC_ERR_FRAMEMISSING                          _HRESULT_TYPEDEF_(0x88982f62)
#define WINCODEC_ERR_BADMETADATAHEADER                     _HRESULT_TYPEDEF_(0x88982f63)
#define WINCODEC_ERR_BADSTREAMDATA                         _HRESULT_TYPEDEF_(0x88982f70)
#define WINCODEC_ERR_STREAMWRITE                           _HRESULT_TYPEDEF_(0x88982f71)
#define WINCODEC_ERR_STREAMREAD                            _HRESULT_TYPEDEF_(0x88982f72)
#define WINCODEC_ERR_STREAMNOTAVAILABLE                    _HRESULT_TYPEDEF_(0x88982f73)
#define WINCODEC_ERR_UNSUPPORTEDPIXELFORMAT                _HRESULT_TYPEDEF_(0x88982f80)
#define WINCODEC_ERR_UNSUPPORTEDOPERATION                  _HRESULT_TYPEDEF_(0x88982f81)
#define WINCODEC_ERR_INVALIDREGISTRATION                   _HRESULT_TYPEDEF_(0x88982f8a)
#define WINCODEC_ERR_COMPONENTINITIALIZEFAILURE            _HRESULT_TYPEDEF_(0x88982f8b)
#define WINCODEC_ERR_INSUFFICIENTBUFFER                    _HRESULT_TYPEDEF_(0x88982f8c)
#define WINCODEC_ERR_DUPLICATEMETADATAPRESENT              _HRESULT_TYPEDEF_(0x88982f8d)
#define WINCODEC_ERR_PROPERTYUNEXPECTEDTYPE                _HRESULT_TYPEDEF_(0x88982f8e)
#define WINCODEC_ERR_UNEXPECTEDSIZE                        _HRESULT_TYPEDEF_(0x88982f8f)
#define WINCODEC_ERR_INVALIDQUERYREQUEST                   _HRESULT_TYPEDEF_(0x88982f90)
#define WINCODEC_ERR_UNEXPECTEDMETADATATYPE                _HRESULT_TYPEDEF_(0x88982f91)
#define WINCODEC_ERR_REQUESTONLYVALIDATMETADATAROOT        _HRESULT_TYPEDEF_(0x88982f92)
#define WINCODEC_ERR_INVALIDQUERYCHARACTER                 _HRESULT_TYPEDEF_(0x88982f93)
#define WINCODEC_ERR_WIN32ERROR                            _HRESULT_TYPEDEF_(0x88982f94)
#define WINCODEC_ERR_INVALIDPROGRESSIVELEVEL               _HRESULT_TYPEDEF_(0x88982f95)
#define WINCODEC_ERR_INVALIDJPEGSCANINDEX                  _HRESULT_TYPEDEF_(0x88982f96)

#define NAP_E_INVALID_PACKET            _HRESULT_TYPEDEF_(0x80270001)
#define NAP_E_MISSING_SOH               _HRESULT_TYPEDEF_(0x80270002)
#define NAP_E_CONFLICTING_ID            _HRESULT_TYPEDEF_(0x80270003)
#define NAP_E_NO_CACHED_SOH             _HRESULT_TYPEDEF_(0x80270004)
#define NAP_E_STILL_BOUND               _HRESULT_TYPEDEF_(0x80270005)
#define NAP_E_NOT_REGISTERED            _HRESULT_TYPEDEF_(0x80270006)
#define NAP_E_NOT_INITIALIZED           _HRESULT_TYPEDEF_(0x80270007)
#define NAP_E_MISMATCHED_ID             _HRESULT_TYPEDEF_(0x80270008)
#define NAP_E_NOT_PENDING               _HRESULT_TYPEDEF_(0x80270009)
#define NAP_E_ID_NOT_FOUND              _HRESULT_TYPEDEF_(0x8027000A)
#define NAP_E_MAXSIZE_TOO_SMALL         _HRESULT_TYPEDEF_(0x8027000B)
#define NAP_E_SERVICE_NOT_RUNNING       _HRESULT_TYPEDEF_(0x8027000C)
#define NAP_S_CERT_ALREADY_PRESENT      _HRESULT_TYPEDEF_(0x0027000D)
#define NAP_E_ENTITY_DISABLED           _HRESULT_TYPEDEF_(0x8027000E)
#define NAP_E_NETSH_GROUPPOLICY_ERROR   _HRESULT_TYPEDEF_(0x8027000F)
#define NAP_E_TOO_MANY_CALLS            _HRESULT_TYPEDEF_(0x80270010)

#define NAP_E_SHV_CONFIG_EXISTED        _HRESULT_TYPEDEF_(0x80270011)
#define NAP_E_SHV_CONFIG_NOT_FOUND      _HRESULT_TYPEDEF_(0x80270012)
#define NAP_E_SHV_TIMEOUT               _HRESULT_TYPEDEF_(0x80270013)

#define E_BLUETOOTH_ATT_INVALID_HANDLE                   _HRESULT_TYPEDEF_(0x80650001)
#define E_BLUETOOTH_ATT_READ_NOT_PERMITTED               _HRESULT_TYPEDEF_(0x80650002)
#define E_BLUETOOTH_ATT_WRITE_NOT_PERMITTED              _HRESULT_TYPEDEF_(0x80650003)
#define E_BLUETOOTH_ATT_INVALID_PDU                      _HRESULT_TYPEDEF_(0x80650004)
#define E_BLUETOOTH_ATT_INSUFFICIENT_AUTHENTICATION      _HRESULT_TYPEDEF_(0x80650005)
#define E_BLUETOOTH_ATT_REQUEST_NOT_SUPPORTED            _HRESULT_TYPEDEF_(0x80650006)
#define E_BLUETOOTH_ATT_INVALID_OFFSET                   _HRESULT_TYPEDEF_(0x80650007)
#define E_BLUETOOTH_ATT_INSUFFICIENT_AUTHORIZATION       _HRESULT_TYPEDEF_(0x80650008)
#define E_BLUETOOTH_ATT_PREPARE_QUEUE_FULL               _HRESULT_TYPEDEF_(0x80650009)
#define E_BLUETOOTH_ATT_ATTRIBUTE_NOT_FOUND              _HRESULT_TYPEDEF_(0x8065000a)
#define E_BLUETOOTH_ATT_ATTRIBUTE_NOT_LONG               _HRESULT_TYPEDEF_(0x8065000b)
#define E_BLUETOOTH_ATT_INSUFFICIENT_ENCRYPTION_KEY_SIZE _HRESULT_TYPEDEF_(0x8065000c)
#define E_BLUETOOTH_ATT_INVALID_ATTRIBUTE_VALUE_LENGTH   _HRESULT_TYPEDEF_(0x8065000d)
#define E_BLUETOOTH_ATT_UNLIKELY                         _HRESULT_TYPEDEF_(0x8065000e)
#define E_BLUETOOTH_ATT_INSUFFICIENT_ENCRYPTION          _HRESULT_TYPEDEF_(0x8065000f)
#define E_BLUETOOTH_ATT_UNSUPPORTED_GROUP_TYPE           _HRESULT_TYPEDEF_(0x80650010)
#define E_BLUETOOTH_ATT_INSUFFICIENT_RESOURCES           _HRESULT_TYPEDEF_(0x80650011)
#define E_BLUETOOTH_ATT_UNKNOWN_ERROR                    _HRESULT_TYPEDEF_(0x80651000)

#define DWRITE_E_FILEFORMAT               _HRESULT_TYPEDEF_(0x88985000L)
#define DWRITE_E_UNEXPECTED               _HRESULT_TYPEDEF_(0x88985001L)
#define DWRITE_E_NOFONT                   _HRESULT_TYPEDEF_(0x88985002L)
#define DWRITE_E_FILENOTFOUND             _HRESULT_TYPEDEF_(0x88985003L)
#define DWRITE_E_FILEACCESS               _HRESULT_TYPEDEF_(0x88985004L)
#define DWRITE_E_FONTCOLLECTIONOBSOLETE   _HRESULT_TYPEDEF_(0x88985005L)
#define DWRITE_E_ALREADYREGISTERED        _HRESULT_TYPEDEF_(0x88985006L)
#define DWRITE_E_CACHEFORMAT              _HRESULT_TYPEDEF_(0x88985007L)
#define DWRITE_E_CACHEVERSION             _HRESULT_TYPEDEF_(0x88985008L)
#define DWRITE_E_UNSUPPORTEDOPERATION     _HRESULT_TYPEDEF_(0x88985009L)
#define DWRITE_E_TEXTRENDERERINCOMPATIBLE _HRESULT_TYPEDEF_(0x8898500AL)
#define DWRITE_E_FLOWDIRECTIONCONFLICTS   _HRESULT_TYPEDEF_(0x8898500BL)
#define DWRITE_E_NOCOLOR                  _HRESULT_TYPEDEF_(0x8898500CL)
#define DWRITE_E_REMOTEFONT               _HRESULT_TYPEDEF_(0x8898500DL)
#define DWRITE_E_DOWNLOADCANCELLED        _HRESULT_TYPEDEF_(0x8898500EL)
#define DWRITE_E_DOWNLOADFAILED           _HRESULT_TYPEDEF_(0x8898500FL)
#define DWRITE_E_TOOMANYDOWNLOADS         _HRESULT_TYPEDEF_(0x88985010L)

#include <fltwinerror.h>

#undef __IN__WINERROR_

#endif /* _WINERROR_.  */
