/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

cpp_quote("#include <winapifamily.h>")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")

import "oaidl.idl";
import "ocidl.idl";

cpp_quote("#include \"vsserror.h\" ")

cpp_quote("")
typedef WCHAR *VSS_PWSZ;
typedef LONGLONG VSS_TIMESTAMP;
typedef GUID VSS_ID;

cpp_quote("")
typedef enum _VSS_OBJECT_TYPE {
  VSS_OBJECT_UNKNOWN = 0,
  VSS_OBJECT_NONE,
  VSS_OBJECT_SNAPSHOT_SET,
  VSS_OBJECT_SNAPSHOT,
  VSS_OBJECT_PROVIDER,
  VSS_OBJECT_TYPE_COUNT
} VSS_OBJECT_TYPE, *PVSS_OBJECT_TYPE;

cpp_quote("")
typedef enum _VSS_SNAPSHOT_STATE {
  VSS_SS_UNKNOWN = 0,
  VSS_SS_PREPARING,
  VSS_SS_PROCESSING_PREPARE,
  VSS_SS_PREPARED,
  VSS_SS_PROCESSING_PRECOMMIT,
  VSS_SS_PRECOMMITTED,
  VSS_SS_PROCESSING_COMMIT,
  VSS_SS_COMMITTED,
  VSS_SS_PROCESSING_POSTCOMMIT,
  VSS_SS_PROCESSING_PREFINALCOMMIT,
  VSS_SS_PREFINALCOMMITTED,
  VSS_SS_PROCESSING_POSTFINALCOMMIT,
  VSS_SS_CREATED,
  VSS_SS_ABORTED,
  VSS_SS_DELETED,
  VSS_SS_POSTCOMMITTED,
  VSS_SS_COUNT
} VSS_SNAPSHOT_STATE, *PVSS_SNAPSHOT_STATE;

cpp_quote("")
typedef enum _VSS_VOLUME_SNAPSHOT_ATTRIBUTES {
  VSS_VOLSNAP_ATTR_PERSISTENT = 0x00000001,
  VSS_VOLSNAP_ATTR_NO_AUTORECOVERY = 0x00000002,
  VSS_VOLSNAP_ATTR_CLIENT_ACCESSIBLE = 0x00000004,
  VSS_VOLSNAP_ATTR_NO_AUTO_RELEASE = 0x00000008,
  VSS_VOLSNAP_ATTR_NO_WRITERS = 0x00000010,
  VSS_VOLSNAP_ATTR_TRANSPORTABLE = 0x00000020,
  VSS_VOLSNAP_ATTR_NOT_SURFACED = 0x00000040,
  VSS_VOLSNAP_ATTR_NOT_TRANSACTED = 0x00000080,
  VSS_VOLSNAP_ATTR_HARDWARE_ASSISTED = 0x00010000,
  VSS_VOLSNAP_ATTR_DIFFERENTIAL = 0x00020000,
  VSS_VOLSNAP_ATTR_PLEX = 0x00040000,
  VSS_VOLSNAP_ATTR_IMPORTED = 0x00080000,
  VSS_VOLSNAP_ATTR_EXPOSED_LOCALLY = 0x00100000,
  VSS_VOLSNAP_ATTR_EXPOSED_REMOTELY = 0x00200000,
  VSS_VOLSNAP_ATTR_AUTORECOVER = 0x00400000,
  VSS_VOLSNAP_ATTR_ROLLBACK_RECOVERY = 0x00800000,
  VSS_VOLSNAP_ATTR_DELAYED_POSTSNAPSHOT = 0x01000000,
  VSS_VOLSNAP_ATTR_TXF_RECOVERY = 0x02000000,
  VSS_VOLSNAP_ATTR_FILE_SHARE = 0x04000000
} VSS_VOLUME_SNAPSHOT_ATTRIBUTES, *PVSS_VOLUME_SNAPSHOT_ATTRIBUTES;

cpp_quote("")
typedef enum _VSS_SNAPSHOT_CONTEXT {
  VSS_CTX_BACKUP = 0x00000000,
  VSS_CTX_FILE_SHARE_BACKUP = VSS_VOLSNAP_ATTR_NO_WRITERS,
  VSS_CTX_NAS_ROLLBACK = VSS_VOLSNAP_ATTR_PERSISTENT | VSS_VOLSNAP_ATTR_NO_AUTO_RELEASE | VSS_VOLSNAP_ATTR_NO_WRITERS,
  VSS_CTX_APP_ROLLBACK = VSS_VOLSNAP_ATTR_PERSISTENT | VSS_VOLSNAP_ATTR_NO_AUTO_RELEASE,
  VSS_CTX_CLIENT_ACCESSIBLE = VSS_VOLSNAP_ATTR_PERSISTENT | VSS_VOLSNAP_ATTR_CLIENT_ACCESSIBLE | VSS_VOLSNAP_ATTR_NO_AUTO_RELEASE | VSS_VOLSNAP_ATTR_NO_WRITERS,
  VSS_CTX_CLIENT_ACCESSIBLE_WRITERS = VSS_VOLSNAP_ATTR_PERSISTENT | VSS_VOLSNAP_ATTR_CLIENT_ACCESSIBLE | VSS_VOLSNAP_ATTR_NO_AUTO_RELEASE,
  VSS_CTX_ALL = 0xFFFFFFFF
} VSS_SNAPSHOT_CONTEXT, *PVSS_SNAPSHOT_CONTEXT;

cpp_quote("")
typedef enum _VSS_PROVIDER_CAPABILITIES {
  VSS_PRV_CAPABILITY_LEGACY = 0x00000001,
  VSS_PRV_CAPABILITY_COMPLIANT = 0x00000002,
  VSS_PRV_CAPABILITY_LUN_REPOINT = 0x00000004,
  VSS_PRV_CAPABILITY_LUN_RESYNC = 0x00000008,
  VSS_PRV_CAPABILITY_OFFLINE_CREATION = 0x00000010,
  VSS_PRV_CAPABILITY_MULTIPLE_IMPORT = 0x00000020,
  VSS_PRV_CAPABILITY_RECYCLING = 0x00000040,
  VSS_PRV_CAPABILITY_PLEX = 0x00000080,
  VSS_PRV_CAPABILITY_DIFFERENTIAL = 0x00000100,
  VSS_PRV_CAPABILITY_CLUSTERED = 0x00000200
} VSS_PROVIDER_CAPABILITIES, *PVSS_PROVIDER_CAPABILITIES;

cpp_quote("")
typedef enum _VSS_HARDWARE_OPTIONS {
  VSS_BREAKEX_FLAG_MASK_LUNS = 0x00000001,
  VSS_BREAKEX_FLAG_MAKE_READ_WRITE = 0x00000002,
  VSS_BREAKEX_FLAG_REVERT_IDENTITY_ALL = 0x00000004,
  VSS_BREAKEX_FLAG_REVERT_IDENTITY_NONE = 0x00000008,
  VSS_ONLUNSTATECHANGE_NOTIFY_READ_WRITE = 0x00000100,
  VSS_ONLUNSTATECHANGE_NOTIFY_LUN_PRE_RECOVERY = 0x00000200,
  VSS_ONLUNSTATECHANGE_NOTIFY_LUN_POST_RECOVERY = 0x00000400,
  VSS_ONLUNSTATECHANGE_DO_MASK_LUNS = 0x00000800
} VSS_HARDWARE_OPTIONS, *PVSS_HARDWARE_OPTIONS;

cpp_quote("")
typedef enum _VSS_RECOVERY_OPTIONS {
  VSS_RECOVERY_REVERT_IDENTITY_ALL = 0x00000100,
  VSS_RECOVERY_NO_VOLUME_CHECK = 0x00000200
} VSS_RECOVERY_OPTIONS, *PVSS_RECOVERY_OPTIONS;

cpp_quote("")
typedef enum _VSS_WRITER_STATE {
  VSS_WS_UNKNOWN = 0,
  VSS_WS_STABLE,
  VSS_WS_WAITING_FOR_FREEZE,
  VSS_WS_WAITING_FOR_THAW,
  VSS_WS_WAITING_FOR_POST_SNAPSHOT,
  VSS_WS_WAITING_FOR_BACKUP_COMPLETE,
  VSS_WS_FAILED_AT_IDENTIFY,
  VSS_WS_FAILED_AT_PREPARE_BACKUP,
  VSS_WS_FAILED_AT_PREPARE_SNAPSHOT,
  VSS_WS_FAILED_AT_FREEZE,
  VSS_WS_FAILED_AT_THAW,
  VSS_WS_FAILED_AT_POST_SNAPSHOT,
  VSS_WS_FAILED_AT_BACKUP_COMPLETE,
  VSS_WS_FAILED_AT_PRE_RESTORE,
  VSS_WS_FAILED_AT_POST_RESTORE,
  VSS_WS_FAILED_AT_BACKUPSHUTDOWN,
  VSS_WS_COUNT
} VSS_WRITER_STATE, *PVSS_WRITER_STATE;

cpp_quote("")
typedef enum _VSS_BACKUP_TYPE {
  VSS_BT_UNDEFINED,
  VSS_BT_FULL,
  VSS_BT_INCREMENTAL,
  VSS_BT_DIFFERENTIAL,
  VSS_BT_LOG,
  VSS_BT_COPY,
  VSS_BT_OTHER
} VSS_BACKUP_TYPE, *PVSS_BACKUP_TYPE;

cpp_quote("")
typedef enum _VSS_RESTORE_TYPE {
  VSS_RTYPE_UNDEFINED,
  VSS_RTYPE_BY_COPY,
  VSS_RTYPE_IMPORT,
  VSS_RTYPE_OTHER
} VSS_RESTORE_TYPE, *PVSS_RESTORE_TYPE;

cpp_quote("")
typedef enum _VSS_ROLLFORWARD_TYPE {
  VSS_RF_UNDEFINED,
  VSS_RF_NONE,
  VSS_RF_ALL,
  VSS_RF_PARTIAL
} VSS_ROLLFORWARD_TYPE, *PVSS_ROLLFORWARD_TYPE;

cpp_quote("")
typedef enum _VSS_PROVIDER_TYPE {
  VSS_PROV_UNKNOWN = 0,
  VSS_PROV_SYSTEM = 1,
  VSS_PROV_SOFTWARE = 2,
  VSS_PROV_HARDWARE = 3,
  VSS_PROV_FILESHARE = 4
} VSS_PROVIDER_TYPE, *PVSS_PROVIDER_TYPE;

cpp_quote("")
typedef enum _VSS_APPLICATION_LEVEL {
  VSS_APP_UNKNOWN = 0,
  VSS_APP_SYSTEM = 1,
  VSS_APP_BACK_END = 2,
  VSS_APP_FRONT_END = 3,
  VSS_APP_SYSTEM_RM = 4,
  VSS_APP_AUTO = -1
} VSS_APPLICATION_LEVEL, *PVSS_APPLICATION_LEVEL;

cpp_quote("")
typedef enum _VSS_SNAPSHOT_COMPATIBILITY {
  VSS_SC_DISABLE_DEFRAG = 0x1,
  VSS_SC_DISABLE_CONTENTINDEX = 0x2
} VSS_SNAPSHOT_COMPATIBILITY;

cpp_quote("")
typedef enum _VSS_SNAPSHOT_PROPERTY_ID {
  VSS_SPROPID_UNKNOWN = 0x00000000,
  VSS_SPROPID_SNAPSHOT_ID = 0x00000001,
  VSS_SPROPID_SNAPSHOT_SET_ID = 0x00000002,
  VSS_SPROPID_SNAPSHOTS_COUNT = 0x00000003,
  VSS_SPROPID_SNAPSHOT_DEVICE = 0x00000004,
  VSS_SPROPID_ORIGINAL_VOLUME = 0x00000005,
  VSS_SPROPID_ORIGINATING_MACHINE = 0x00000006,
  VSS_SPROPID_SERVICE_MACHINE = 0x00000007,
  VSS_SPROPID_EXPOSED_NAME = 0x00000008,
  VSS_SPROPID_EXPOSED_PATH = 0x00000009,
  VSS_SPROPID_PROVIDER_ID = 0x0000000A,
  VSS_SPROPID_SNAPSHOT_ATTRIBUTES = 0x0000000B,
  VSS_SPROPID_CREATION_TIMESTAMP = 0x0000000C,
  VSS_SPROPID_STATUS = 0x0000000D
} VSS_SNAPSHOT_PROPERTY_ID, *PVSS_SNAPSHOT_PROPERTY_ID;

cpp_quote("")
typedef enum _VSS_FILE_SPEC_BACKUP_TYPE {
  VSS_FSBT_FULL_BACKUP_REQUIRED = 0x00000001,
  VSS_FSBT_DIFFERENTIAL_BACKUP_REQUIRED = 0x00000002,
  VSS_FSBT_INCREMENTAL_BACKUP_REQUIRED = 0x00000004,
  VSS_FSBT_LOG_BACKUP_REQUIRED = 0x00000008,
  VSS_FSBT_FULL_SNAPSHOT_REQUIRED = 0x00000100,
  VSS_FSBT_DIFFERENTIAL_SNAPSHOT_REQUIRED = 0x00000200,
  VSS_FSBT_INCREMENTAL_SNAPSHOT_REQUIRED = 0x00000400,
  VSS_FSBT_LOG_SNAPSHOT_REQUIRED = 0x00000800,
  VSS_FSBT_CREATED_DURING_BACKUP = 0x00010000,
  VSS_FSBT_ALL_BACKUP_REQUIRED = 0x0000000F,
  VSS_FSBT_ALL_SNAPSHOT_REQUIRED = 0x00000F00
} VSS_FILE_SPEC_BACKUP_TYPE, *PVSS_FILE_SPEC_BACKUP_TYPE;

cpp_quote("")
typedef enum _VSS_BACKUP_SCHEMA {
  VSS_BS_UNDEFINED = 0x00000000,
  VSS_BS_DIFFERENTIAL = 0x00000001,
  VSS_BS_INCREMENTAL = 0x00000002,
  VSS_BS_EXCLUSIVE_INCREMENTAL_DIFFERENTIAL = 0x00000004,
  VSS_BS_LOG = 0x00000008,
  VSS_BS_COPY = 0x00000010,
  VSS_BS_TIMESTAMPED = 0x00000020,
  VSS_BS_LAST_MODIFY = 0x00000040,
  VSS_BS_LSN = 0x00000080,
  VSS_BS_WRITER_SUPPORTS_NEW_TARGET = 0x00000100,
  VSS_BS_WRITER_SUPPORTS_RESTORE_WITH_MOVE = 0x00000200,
  VSS_BS_INDEPENDENT_SYSTEM_STATE = 0x00000400,
  VSS_BS_ROLLFORWARD_RESTORE = 0x00001000,
  VSS_BS_RESTORE_RENAME = 0x00002000,
  VSS_BS_AUTHORITATIVE_RESTORE = 0x00004000,
  VSS_BS_WRITER_SUPPORTS_PARALLEL_RESTORES = 0x00008000
} VSS_BACKUP_SCHEMA, *PVSS_BACKUP_SCHEMA;

cpp_quote("")
typedef struct _VSS_SNAPSHOT_PROP {
  VSS_ID m_SnapshotId;
  VSS_ID m_SnapshotSetId;
  LONG m_lSnapshotsCount;
  VSS_PWSZ m_pwszSnapshotDeviceObject;
  VSS_PWSZ m_pwszOriginalVolumeName;
  VSS_PWSZ m_pwszOriginatingMachine;
  VSS_PWSZ m_pwszServiceMachine;
  VSS_PWSZ m_pwszExposedName;
  VSS_PWSZ m_pwszExposedPath;
  VSS_ID m_ProviderId;
  LONG m_lSnapshotAttributes;
  VSS_TIMESTAMP m_tsCreationTimestamp;
  VSS_SNAPSHOT_STATE m_eStatus;
} VSS_SNAPSHOT_PROP, *PVSS_SNAPSHOT_PROP;

cpp_quote("")
typedef struct _VSS_PROVIDER_PROP {
  VSS_ID m_ProviderId;
  VSS_PWSZ m_pwszProviderName;
  VSS_PROVIDER_TYPE m_eProviderType;
  VSS_PWSZ m_pwszProviderVersion;
  VSS_ID m_ProviderVersionId;
  CLSID m_ClassId;
} VSS_PROVIDER_PROP, *PVSS_PROVIDER_PROP;

cpp_quote("")
[switch_type(VSS_OBJECT_TYPE)]
typedef union {
  [case(VSS_OBJECT_SNAPSHOT)] VSS_SNAPSHOT_PROP Snap;
  [case(VSS_OBJECT_PROVIDER)] VSS_PROVIDER_PROP Prov;
  [default];
} VSS_OBJECT_UNION;

cpp_quote("")
typedef struct _VSS_OBJECT_PROP {
  VSS_OBJECT_TYPE Type;
  [switch_is(Type)] VSS_OBJECT_UNION Obj;
} VSS_OBJECT_PROP, *PVSS_OBJECT_PROP;

cpp_quote("")
[
  object,
  uuid(ae1c7110-2f60-11d3-8a39-00c04f72d8e3),
  pointer_default(unique)
]
interface IVssEnumObject : IUnknown {
  HRESULT Next([in] ULONG celt, [out, size_is(celt), length_is(*pceltFetched)] VSS_OBJECT_PROP *rgelt, [out] ULONG *pceltFetched);
  HRESULT Skip([in] ULONG celt);
  HRESULT Reset();
  HRESULT Clone([in, out] IVssEnumObject **ppenum);
};

cpp_quote("")
[
  object,
  uuid(507c37b4-cf5b-4e95-b0af-14eb9767467e),
  pointer_default(unique)
]
interface IVssAsync : IUnknown {
  HRESULT Cancel();
  HRESULT Wait([in, defaultvalue(0xFFFFFFFF)] DWORD dwMilliseconds);
  HRESULT QueryStatus([out] HRESULT *pHrResult, [in, out, unique] INT *pReserved);
};

cpp_quote("#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */")
