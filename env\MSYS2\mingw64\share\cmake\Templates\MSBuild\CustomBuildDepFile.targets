<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Update AdditionalInputs with depfile-discovered inputs.  -->
  <Target Name="CMakeCustomBuildDepFileAdditionalInputs" BeforeTargets="CustomBuild" Condition="'@(CustomBuild)' != ''">
    <ItemGroup>
      <!-- Save original AdditionalInputs generated by CMake.  -->
      <CustomBuild>
        <CMakeAdditionalInputs>%(CustomBuild.AdditionalInputs)</CMakeAdditionalInputs>
      </CustomBuild>
      <!-- Read depfile-discovered inputs.  -->
      <CustomBuild Condition="Exists('%(CustomBuild.DepFileAdditionalInputsFile)')">
        <DepFileAdditionalInputs>$([System.IO.File]::ReadAllText('%(CustomBuild.DepFileAdditionalInputsFile)').TrimEnd())</DepFileAdditionalInputs>
      </CustomBuild>
      <!-- Add depfile-discovered inputs to AdditionalInputs.  -->
      <CustomBuild Condition="'%(CustomBuild.DepFileAdditionalInputs)' != ''">
        <AdditionalInputs Condition="'%(CustomBuild.AdditionalInputs)' == ''">%(CustomBuild.DepFileAdditionalInputs)</AdditionalInputs>
        <AdditionalInputs Condition="'%(CustomBuild.AdditionalInputs)' != ''">%(CustomBuild.AdditionalInputs);%(CustomBuild.DepFileAdditionalInputs)</AdditionalInputs>
      </CustomBuild>
    </ItemGroup>
  </Target>

  <!-- Update the tracking log with depfile-discovered inputs.  -->
  <Target Name="CMakeCustomBuildDepFileTrackingLog" AfterTargets="CustomBuild" Condition="'@(CustomBuild)' != ''">
    <!-- Compute the tracking log content for each CustomBuild item individually.  -->
    <ItemGroup>
      <!-- Read depfile-discovered inputs.  -->
      <CustomBuild Condition="Exists('%(CustomBuild.DepFileAdditionalInputsFile)')">
        <DepFileAdditionalInputs>$([System.IO.File]::ReadAllText('%(CustomBuild.DepFileAdditionalInputsFile)').TrimEnd())</DepFileAdditionalInputs>
      </CustomBuild>
      <!-- Generate tracking log representation of all inputs.  -->
      <CustomBuild>
        <ReadTLog>^%(CustomBuild.Identity)&#xD;&#xA;</ReadTLog>
      </CustomBuild>
      <CustomBuild Condition="'%(CustomBuild.CMakeAdditionalInputs)' != ''">
        <ReadTLog>%(ReadTLog)$([System.String]::Copy('%(CustomBuild.CMakeAdditionalInputs)').Trim(';').Replace(';', '&#xD;&#xA;'))&#xD;&#xA;</ReadTLog>
      </CustomBuild>
      <CustomBuild Condition="'%(CustomBuild.DepFileAdditionalInputs)' != ''">
        <ReadTLog>%(ReadTLog)$([System.String]::Copy('%(CustomBuild.DepFileAdditionalInputs)').Trim(';').Replace(';', '&#xD;&#xA;'))&#xD;&#xA;</ReadTLog>
      </CustomBuild>
    </ItemGroup>
    <!-- Compute the combined tracking log for all CustomBuild items together.  -->
    <PropertyGroup>
      <CustomBuildReadTLog>@(CustomBuild->'%(ReadTLog)','')</CustomBuildReadTLog>
    </PropertyGroup>
    <!-- Replace the combined tracking log on disk.  -->
    <WriteLinesToFile File="$(TLogLocation)CustomBuild.read.1.tlog" Overwrite="true" Lines="$(CustomBuildReadTLog.ToUpper())" />
  </Target>
</Project>
