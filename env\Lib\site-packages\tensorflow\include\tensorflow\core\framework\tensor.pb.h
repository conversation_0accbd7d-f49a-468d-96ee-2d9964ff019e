// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/tensor.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/resource_handle.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2ftensor_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2ftensor_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2ftensor_2eproto;
namespace tensorflow {
class TensorProto;
struct TensorProtoDefaultTypeInternal;
extern TensorProtoDefaultTypeInternal _TensorProto_default_instance_;
class VariantTensorDataProto;
struct VariantTensorDataProtoDefaultTypeInternal;
extern VariantTensorDataProtoDefaultTypeInternal _VariantTensorDataProto_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::TensorProto* Arena::CreateMaybeMessage<::tensorflow::TensorProto>(Arena*);
template<> ::tensorflow::VariantTensorDataProto* Arena::CreateMaybeMessage<::tensorflow::VariantTensorDataProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class TensorProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorProto) */ {
 public:
  inline TensorProto() : TensorProto(nullptr) {}
  ~TensorProto() override;
  explicit PROTOBUF_CONSTEXPR TensorProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorProto(const TensorProto& from);
  TensorProto(TensorProto&& from) noexcept
    : TensorProto() {
    *this = ::std::move(from);
  }

  inline TensorProto& operator=(const TensorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorProto& operator=(TensorProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorProto* internal_default_instance() {
    return reinterpret_cast<const TensorProto*>(
               &_TensorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TensorProto& a, TensorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorProto& from) {
    TensorProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorProto";
  }
  protected:
  explicit TensorProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFloatValFieldNumber = 5,
    kDoubleValFieldNumber = 6,
    kIntValFieldNumber = 7,
    kStringValFieldNumber = 8,
    kScomplexValFieldNumber = 9,
    kInt64ValFieldNumber = 10,
    kBoolValFieldNumber = 11,
    kDcomplexValFieldNumber = 12,
    kHalfValFieldNumber = 13,
    kResourceHandleValFieldNumber = 14,
    kVariantValFieldNumber = 15,
    kUint32ValFieldNumber = 16,
    kUint64ValFieldNumber = 17,
    kTensorContentFieldNumber = 4,
    kFloat8ValFieldNumber = 18,
    kTensorShapeFieldNumber = 2,
    kDtypeFieldNumber = 1,
    kVersionNumberFieldNumber = 3,
  };
  // repeated float float_val = 5 [packed = true];
  int float_val_size() const;
  private:
  int _internal_float_val_size() const;
  public:
  void clear_float_val();
  private:
  float _internal_float_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_float_val() const;
  void _internal_add_float_val(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_float_val();
  public:
  float float_val(int index) const;
  void set_float_val(int index, float value);
  void add_float_val(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      float_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_float_val();

  // repeated double double_val = 6 [packed = true];
  int double_val_size() const;
  private:
  int _internal_double_val_size() const;
  public:
  void clear_double_val();
  private:
  double _internal_double_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_double_val() const;
  void _internal_add_double_val(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_double_val();
  public:
  double double_val(int index) const;
  void set_double_val(int index, double value);
  void add_double_val(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      double_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_double_val();

  // repeated int32 int_val = 7 [packed = true];
  int int_val_size() const;
  private:
  int _internal_int_val_size() const;
  public:
  void clear_int_val();
  private:
  int32_t _internal_int_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_int_val() const;
  void _internal_add_int_val(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_int_val();
  public:
  int32_t int_val(int index) const;
  void set_int_val(int index, int32_t value);
  void add_int_val(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      int_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_int_val();

  // repeated bytes string_val = 8;
  int string_val_size() const;
  private:
  int _internal_string_val_size() const;
  public:
  void clear_string_val();
  const std::string& string_val(int index) const;
  std::string* mutable_string_val(int index);
  void set_string_val(int index, const std::string& value);
  void set_string_val(int index, std::string&& value);
  void set_string_val(int index, const char* value);
  void set_string_val(int index, const void* value, size_t size);
  std::string* add_string_val();
  void add_string_val(const std::string& value);
  void add_string_val(std::string&& value);
  void add_string_val(const char* value);
  void add_string_val(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& string_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_string_val();
  private:
  const std::string& _internal_string_val(int index) const;
  std::string* _internal_add_string_val();
  public:

  // repeated float scomplex_val = 9 [packed = true];
  int scomplex_val_size() const;
  private:
  int _internal_scomplex_val_size() const;
  public:
  void clear_scomplex_val();
  private:
  float _internal_scomplex_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_scomplex_val() const;
  void _internal_add_scomplex_val(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_scomplex_val();
  public:
  float scomplex_val(int index) const;
  void set_scomplex_val(int index, float value);
  void add_scomplex_val(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      scomplex_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_scomplex_val();

  // repeated int64 int64_val = 10 [packed = true];
  int int64_val_size() const;
  private:
  int _internal_int64_val_size() const;
  public:
  void clear_int64_val();
  private:
  int64_t _internal_int64_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_int64_val() const;
  void _internal_add_int64_val(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_int64_val();
  public:
  int64_t int64_val(int index) const;
  void set_int64_val(int index, int64_t value);
  void add_int64_val(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      int64_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_int64_val();

  // repeated bool bool_val = 11 [packed = true];
  int bool_val_size() const;
  private:
  int _internal_bool_val_size() const;
  public:
  void clear_bool_val();
  private:
  bool _internal_bool_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_bool_val() const;
  void _internal_add_bool_val(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_bool_val();
  public:
  bool bool_val(int index) const;
  void set_bool_val(int index, bool value);
  void add_bool_val(bool value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      bool_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_bool_val();

  // repeated double dcomplex_val = 12 [packed = true];
  int dcomplex_val_size() const;
  private:
  int _internal_dcomplex_val_size() const;
  public:
  void clear_dcomplex_val();
  private:
  double _internal_dcomplex_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_dcomplex_val() const;
  void _internal_add_dcomplex_val(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_dcomplex_val();
  public:
  double dcomplex_val(int index) const;
  void set_dcomplex_val(int index, double value);
  void add_dcomplex_val(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      dcomplex_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_dcomplex_val();

  // repeated int32 half_val = 13 [packed = true];
  int half_val_size() const;
  private:
  int _internal_half_val_size() const;
  public:
  void clear_half_val();
  private:
  int32_t _internal_half_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_half_val() const;
  void _internal_add_half_val(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_half_val();
  public:
  int32_t half_val(int index) const;
  void set_half_val(int index, int32_t value);
  void add_half_val(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      half_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_half_val();

  // repeated .tensorflow.ResourceHandleProto resource_handle_val = 14;
  int resource_handle_val_size() const;
  private:
  int _internal_resource_handle_val_size() const;
  public:
  void clear_resource_handle_val();
  ::tensorflow::ResourceHandleProto* mutable_resource_handle_val(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto >*
      mutable_resource_handle_val();
  private:
  const ::tensorflow::ResourceHandleProto& _internal_resource_handle_val(int index) const;
  ::tensorflow::ResourceHandleProto* _internal_add_resource_handle_val();
  public:
  const ::tensorflow::ResourceHandleProto& resource_handle_val(int index) const;
  ::tensorflow::ResourceHandleProto* add_resource_handle_val();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto >&
      resource_handle_val() const;

  // repeated .tensorflow.VariantTensorDataProto variant_val = 15;
  int variant_val_size() const;
  private:
  int _internal_variant_val_size() const;
  public:
  void clear_variant_val();
  ::tensorflow::VariantTensorDataProto* mutable_variant_val(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::VariantTensorDataProto >*
      mutable_variant_val();
  private:
  const ::tensorflow::VariantTensorDataProto& _internal_variant_val(int index) const;
  ::tensorflow::VariantTensorDataProto* _internal_add_variant_val();
  public:
  const ::tensorflow::VariantTensorDataProto& variant_val(int index) const;
  ::tensorflow::VariantTensorDataProto* add_variant_val();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::VariantTensorDataProto >&
      variant_val() const;

  // repeated uint32 uint32_val = 16 [packed = true];
  int uint32_val_size() const;
  private:
  int _internal_uint32_val_size() const;
  public:
  void clear_uint32_val();
  private:
  uint32_t _internal_uint32_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_uint32_val() const;
  void _internal_add_uint32_val(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_uint32_val();
  public:
  uint32_t uint32_val(int index) const;
  void set_uint32_val(int index, uint32_t value);
  void add_uint32_val(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      uint32_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_uint32_val();

  // repeated uint64 uint64_val = 17 [packed = true];
  int uint64_val_size() const;
  private:
  int _internal_uint64_val_size() const;
  public:
  void clear_uint64_val();
  private:
  uint64_t _internal_uint64_val(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      _internal_uint64_val() const;
  void _internal_add_uint64_val(uint64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      _internal_mutable_uint64_val();
  public:
  uint64_t uint64_val(int index) const;
  void set_uint64_val(int index, uint64_t value);
  void add_uint64_val(uint64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
      uint64_val() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
      mutable_uint64_val();

  // bytes tensor_content = 4;
  void clear_tensor_content();
  const std::string& tensor_content() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tensor_content(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tensor_content();
  PROTOBUF_NODISCARD std::string* release_tensor_content();
  void set_allocated_tensor_content(std::string* tensor_content);
  private:
  const std::string& _internal_tensor_content() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tensor_content(const std::string& value);
  std::string* _internal_mutable_tensor_content();
  public:

  // bytes float8_val = 18;
  void clear_float8_val();
  const std::string& float8_val() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_float8_val(ArgT0&& arg0, ArgT... args);
  std::string* mutable_float8_val();
  PROTOBUF_NODISCARD std::string* release_float8_val();
  void set_allocated_float8_val(std::string* float8_val);
  private:
  const std::string& _internal_float8_val() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_float8_val(const std::string& value);
  std::string* _internal_mutable_float8_val();
  public:

  // .tensorflow.TensorShapeProto tensor_shape = 2;
  bool has_tensor_shape() const;
  private:
  bool _internal_has_tensor_shape() const;
  public:
  void clear_tensor_shape();
  const ::tensorflow::TensorShapeProto& tensor_shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_tensor_shape();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape();
  void set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_tensor_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_tensor_shape();
  public:
  void unsafe_arena_set_allocated_tensor_shape(
      ::tensorflow::TensorShapeProto* tensor_shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_tensor_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // int32 version_number = 3;
  void clear_version_number();
  int32_t version_number() const;
  void set_version_number(int32_t value);
  private:
  int32_t _internal_version_number() const;
  void _internal_set_version_number(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TensorProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > float_val_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > double_val_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > int_val_;
    mutable std::atomic<int> _int_val_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> string_val_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > scomplex_val_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > int64_val_;
    mutable std::atomic<int> _int64_val_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > bool_val_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > dcomplex_val_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > half_val_;
    mutable std::atomic<int> _half_val_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto > resource_handle_val_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::VariantTensorDataProto > variant_val_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > uint32_val_;
    mutable std::atomic<int> _uint32_val_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t > uint64_val_;
    mutable std::atomic<int> _uint64_val_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tensor_content_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr float8_val_;
    ::tensorflow::TensorShapeProto* tensor_shape_;
    int dtype_;
    int32_t version_number_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ftensor_2eproto;
};
// -------------------------------------------------------------------

class VariantTensorDataProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.VariantTensorDataProto) */ {
 public:
  inline VariantTensorDataProto() : VariantTensorDataProto(nullptr) {}
  ~VariantTensorDataProto() override;
  explicit PROTOBUF_CONSTEXPR VariantTensorDataProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VariantTensorDataProto(const VariantTensorDataProto& from);
  VariantTensorDataProto(VariantTensorDataProto&& from) noexcept
    : VariantTensorDataProto() {
    *this = ::std::move(from);
  }

  inline VariantTensorDataProto& operator=(const VariantTensorDataProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline VariantTensorDataProto& operator=(VariantTensorDataProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VariantTensorDataProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const VariantTensorDataProto* internal_default_instance() {
    return reinterpret_cast<const VariantTensorDataProto*>(
               &_VariantTensorDataProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(VariantTensorDataProto& a, VariantTensorDataProto& b) {
    a.Swap(&b);
  }
  inline void Swap(VariantTensorDataProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VariantTensorDataProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VariantTensorDataProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VariantTensorDataProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VariantTensorDataProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const VariantTensorDataProto& from) {
    VariantTensorDataProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VariantTensorDataProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.VariantTensorDataProto";
  }
  protected:
  explicit VariantTensorDataProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorsFieldNumber = 3,
    kTypeNameFieldNumber = 1,
    kMetadataFieldNumber = 2,
  };
  // repeated .tensorflow.TensorProto tensors = 3;
  int tensors_size() const;
  private:
  int _internal_tensors_size() const;
  public:
  void clear_tensors();
  ::tensorflow::TensorProto* mutable_tensors(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensors();
  private:
  const ::tensorflow::TensorProto& _internal_tensors(int index) const;
  ::tensorflow::TensorProto* _internal_add_tensors();
  public:
  const ::tensorflow::TensorProto& tensors(int index) const;
  ::tensorflow::TensorProto* add_tensors();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensors() const;

  // string type_name = 1;
  void clear_type_name();
  const std::string& type_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type_name();
  PROTOBUF_NODISCARD std::string* release_type_name();
  void set_allocated_type_name(std::string* type_name);
  private:
  const std::string& _internal_type_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type_name(const std::string& value);
  std::string* _internal_mutable_type_name();
  public:

  // bytes metadata = 2;
  void clear_metadata();
  const std::string& metadata() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_metadata(ArgT0&& arg0, ArgT... args);
  std::string* mutable_metadata();
  PROTOBUF_NODISCARD std::string* release_metadata();
  void set_allocated_metadata(std::string* metadata);
  private:
  const std::string& _internal_metadata() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_metadata(const std::string& value);
  std::string* _internal_mutable_metadata();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.VariantTensorDataProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensors_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr metadata_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2ftensor_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TensorProto

// .tensorflow.DataType dtype = 1;
inline void TensorProto::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType TensorProto::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType TensorProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.dtype)
  return _internal_dtype();
}
inline void TensorProto::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void TensorProto::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.dtype)
}

// .tensorflow.TensorShapeProto tensor_shape = 2;
inline bool TensorProto::_internal_has_tensor_shape() const {
  return this != internal_default_instance() && _impl_.tensor_shape_ != nullptr;
}
inline bool TensorProto::has_tensor_shape() const {
  return _internal_has_tensor_shape();
}
inline const ::tensorflow::TensorShapeProto& TensorProto::_internal_tensor_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.tensor_shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& TensorProto::tensor_shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.tensor_shape)
  return _internal_tensor_shape();
}
inline void TensorProto::unsafe_arena_set_allocated_tensor_shape(
    ::tensorflow::TensorShapeProto* tensor_shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_shape_);
  }
  _impl_.tensor_shape_ = tensor_shape;
  if (tensor_shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorProto.tensor_shape)
}
inline ::tensorflow::TensorShapeProto* TensorProto::release_tensor_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.tensor_shape_;
  _impl_.tensor_shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorProto::unsafe_arena_release_tensor_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorProto.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.tensor_shape_;
  _impl_.tensor_shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorProto::_internal_mutable_tensor_shape() {
  
  if (_impl_.tensor_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.tensor_shape_ = p;
  }
  return _impl_.tensor_shape_;
}
inline ::tensorflow::TensorShapeProto* TensorProto::mutable_tensor_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_tensor_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.tensor_shape)
  return _msg;
}
inline void TensorProto::set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_shape_);
  }
  if (tensor_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_shape));
    if (message_arena != submessage_arena) {
      tensor_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_shape_ = tensor_shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorProto.tensor_shape)
}

// int32 version_number = 3;
inline void TensorProto::clear_version_number() {
  _impl_.version_number_ = 0;
}
inline int32_t TensorProto::_internal_version_number() const {
  return _impl_.version_number_;
}
inline int32_t TensorProto::version_number() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.version_number)
  return _internal_version_number();
}
inline void TensorProto::_internal_set_version_number(int32_t value) {
  
  _impl_.version_number_ = value;
}
inline void TensorProto::set_version_number(int32_t value) {
  _internal_set_version_number(value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.version_number)
}

// bytes tensor_content = 4;
inline void TensorProto::clear_tensor_content() {
  _impl_.tensor_content_.ClearToEmpty();
}
inline const std::string& TensorProto::tensor_content() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.tensor_content)
  return _internal_tensor_content();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorProto::set_tensor_content(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tensor_content_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.tensor_content)
}
inline std::string* TensorProto::mutable_tensor_content() {
  std::string* _s = _internal_mutable_tensor_content();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.tensor_content)
  return _s;
}
inline const std::string& TensorProto::_internal_tensor_content() const {
  return _impl_.tensor_content_.Get();
}
inline void TensorProto::_internal_set_tensor_content(const std::string& value) {
  
  _impl_.tensor_content_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorProto::_internal_mutable_tensor_content() {
  
  return _impl_.tensor_content_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorProto::release_tensor_content() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorProto.tensor_content)
  return _impl_.tensor_content_.Release();
}
inline void TensorProto::set_allocated_tensor_content(std::string* tensor_content) {
  if (tensor_content != nullptr) {
    
  } else {
    
  }
  _impl_.tensor_content_.SetAllocated(tensor_content, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tensor_content_.IsDefault()) {
    _impl_.tensor_content_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorProto.tensor_content)
}

// repeated int32 half_val = 13 [packed = true];
inline int TensorProto::_internal_half_val_size() const {
  return _impl_.half_val_.size();
}
inline int TensorProto::half_val_size() const {
  return _internal_half_val_size();
}
inline void TensorProto::clear_half_val() {
  _impl_.half_val_.Clear();
}
inline int32_t TensorProto::_internal_half_val(int index) const {
  return _impl_.half_val_.Get(index);
}
inline int32_t TensorProto::half_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.half_val)
  return _internal_half_val(index);
}
inline void TensorProto::set_half_val(int index, int32_t value) {
  _impl_.half_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.half_val)
}
inline void TensorProto::_internal_add_half_val(int32_t value) {
  _impl_.half_val_.Add(value);
}
inline void TensorProto::add_half_val(int32_t value) {
  _internal_add_half_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.half_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TensorProto::_internal_half_val() const {
  return _impl_.half_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TensorProto::half_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.half_val)
  return _internal_half_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TensorProto::_internal_mutable_half_val() {
  return &_impl_.half_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TensorProto::mutable_half_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.half_val)
  return _internal_mutable_half_val();
}

// repeated float float_val = 5 [packed = true];
inline int TensorProto::_internal_float_val_size() const {
  return _impl_.float_val_.size();
}
inline int TensorProto::float_val_size() const {
  return _internal_float_val_size();
}
inline void TensorProto::clear_float_val() {
  _impl_.float_val_.Clear();
}
inline float TensorProto::_internal_float_val(int index) const {
  return _impl_.float_val_.Get(index);
}
inline float TensorProto::float_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.float_val)
  return _internal_float_val(index);
}
inline void TensorProto::set_float_val(int index, float value) {
  _impl_.float_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.float_val)
}
inline void TensorProto::_internal_add_float_val(float value) {
  _impl_.float_val_.Add(value);
}
inline void TensorProto::add_float_val(float value) {
  _internal_add_float_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.float_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TensorProto::_internal_float_val() const {
  return _impl_.float_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TensorProto::float_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.float_val)
  return _internal_float_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TensorProto::_internal_mutable_float_val() {
  return &_impl_.float_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TensorProto::mutable_float_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.float_val)
  return _internal_mutable_float_val();
}

// repeated double double_val = 6 [packed = true];
inline int TensorProto::_internal_double_val_size() const {
  return _impl_.double_val_.size();
}
inline int TensorProto::double_val_size() const {
  return _internal_double_val_size();
}
inline void TensorProto::clear_double_val() {
  _impl_.double_val_.Clear();
}
inline double TensorProto::_internal_double_val(int index) const {
  return _impl_.double_val_.Get(index);
}
inline double TensorProto::double_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.double_val)
  return _internal_double_val(index);
}
inline void TensorProto::set_double_val(int index, double value) {
  _impl_.double_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.double_val)
}
inline void TensorProto::_internal_add_double_val(double value) {
  _impl_.double_val_.Add(value);
}
inline void TensorProto::add_double_val(double value) {
  _internal_add_double_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.double_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TensorProto::_internal_double_val() const {
  return _impl_.double_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TensorProto::double_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.double_val)
  return _internal_double_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TensorProto::_internal_mutable_double_val() {
  return &_impl_.double_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TensorProto::mutable_double_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.double_val)
  return _internal_mutable_double_val();
}

// repeated int32 int_val = 7 [packed = true];
inline int TensorProto::_internal_int_val_size() const {
  return _impl_.int_val_.size();
}
inline int TensorProto::int_val_size() const {
  return _internal_int_val_size();
}
inline void TensorProto::clear_int_val() {
  _impl_.int_val_.Clear();
}
inline int32_t TensorProto::_internal_int_val(int index) const {
  return _impl_.int_val_.Get(index);
}
inline int32_t TensorProto::int_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.int_val)
  return _internal_int_val(index);
}
inline void TensorProto::set_int_val(int index, int32_t value) {
  _impl_.int_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.int_val)
}
inline void TensorProto::_internal_add_int_val(int32_t value) {
  _impl_.int_val_.Add(value);
}
inline void TensorProto::add_int_val(int32_t value) {
  _internal_add_int_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.int_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TensorProto::_internal_int_val() const {
  return _impl_.int_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TensorProto::int_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.int_val)
  return _internal_int_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TensorProto::_internal_mutable_int_val() {
  return &_impl_.int_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TensorProto::mutable_int_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.int_val)
  return _internal_mutable_int_val();
}

// repeated bytes string_val = 8;
inline int TensorProto::_internal_string_val_size() const {
  return _impl_.string_val_.size();
}
inline int TensorProto::string_val_size() const {
  return _internal_string_val_size();
}
inline void TensorProto::clear_string_val() {
  _impl_.string_val_.Clear();
}
inline std::string* TensorProto::add_string_val() {
  std::string* _s = _internal_add_string_val();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.TensorProto.string_val)
  return _s;
}
inline const std::string& TensorProto::_internal_string_val(int index) const {
  return _impl_.string_val_.Get(index);
}
inline const std::string& TensorProto::string_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.string_val)
  return _internal_string_val(index);
}
inline std::string* TensorProto::mutable_string_val(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.string_val)
  return _impl_.string_val_.Mutable(index);
}
inline void TensorProto::set_string_val(int index, const std::string& value) {
  _impl_.string_val_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.string_val)
}
inline void TensorProto::set_string_val(int index, std::string&& value) {
  _impl_.string_val_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.string_val)
}
inline void TensorProto::set_string_val(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.string_val_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.TensorProto.string_val)
}
inline void TensorProto::set_string_val(int index, const void* value, size_t size) {
  _impl_.string_val_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TensorProto.string_val)
}
inline std::string* TensorProto::_internal_add_string_val() {
  return _impl_.string_val_.Add();
}
inline void TensorProto::add_string_val(const std::string& value) {
  _impl_.string_val_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.string_val)
}
inline void TensorProto::add_string_val(std::string&& value) {
  _impl_.string_val_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.string_val)
}
inline void TensorProto::add_string_val(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.string_val_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.TensorProto.string_val)
}
inline void TensorProto::add_string_val(const void* value, size_t size) {
  _impl_.string_val_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.TensorProto.string_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TensorProto::string_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.string_val)
  return _impl_.string_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TensorProto::mutable_string_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.string_val)
  return &_impl_.string_val_;
}

// repeated float scomplex_val = 9 [packed = true];
inline int TensorProto::_internal_scomplex_val_size() const {
  return _impl_.scomplex_val_.size();
}
inline int TensorProto::scomplex_val_size() const {
  return _internal_scomplex_val_size();
}
inline void TensorProto::clear_scomplex_val() {
  _impl_.scomplex_val_.Clear();
}
inline float TensorProto::_internal_scomplex_val(int index) const {
  return _impl_.scomplex_val_.Get(index);
}
inline float TensorProto::scomplex_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.scomplex_val)
  return _internal_scomplex_val(index);
}
inline void TensorProto::set_scomplex_val(int index, float value) {
  _impl_.scomplex_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.scomplex_val)
}
inline void TensorProto::_internal_add_scomplex_val(float value) {
  _impl_.scomplex_val_.Add(value);
}
inline void TensorProto::add_scomplex_val(float value) {
  _internal_add_scomplex_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.scomplex_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TensorProto::_internal_scomplex_val() const {
  return _impl_.scomplex_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
TensorProto::scomplex_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.scomplex_val)
  return _internal_scomplex_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TensorProto::_internal_mutable_scomplex_val() {
  return &_impl_.scomplex_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
TensorProto::mutable_scomplex_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.scomplex_val)
  return _internal_mutable_scomplex_val();
}

// repeated int64 int64_val = 10 [packed = true];
inline int TensorProto::_internal_int64_val_size() const {
  return _impl_.int64_val_.size();
}
inline int TensorProto::int64_val_size() const {
  return _internal_int64_val_size();
}
inline void TensorProto::clear_int64_val() {
  _impl_.int64_val_.Clear();
}
inline int64_t TensorProto::_internal_int64_val(int index) const {
  return _impl_.int64_val_.Get(index);
}
inline int64_t TensorProto::int64_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.int64_val)
  return _internal_int64_val(index);
}
inline void TensorProto::set_int64_val(int index, int64_t value) {
  _impl_.int64_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.int64_val)
}
inline void TensorProto::_internal_add_int64_val(int64_t value) {
  _impl_.int64_val_.Add(value);
}
inline void TensorProto::add_int64_val(int64_t value) {
  _internal_add_int64_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.int64_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TensorProto::_internal_int64_val() const {
  return _impl_.int64_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TensorProto::int64_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.int64_val)
  return _internal_int64_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TensorProto::_internal_mutable_int64_val() {
  return &_impl_.int64_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TensorProto::mutable_int64_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.int64_val)
  return _internal_mutable_int64_val();
}

// repeated bool bool_val = 11 [packed = true];
inline int TensorProto::_internal_bool_val_size() const {
  return _impl_.bool_val_.size();
}
inline int TensorProto::bool_val_size() const {
  return _internal_bool_val_size();
}
inline void TensorProto::clear_bool_val() {
  _impl_.bool_val_.Clear();
}
inline bool TensorProto::_internal_bool_val(int index) const {
  return _impl_.bool_val_.Get(index);
}
inline bool TensorProto::bool_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.bool_val)
  return _internal_bool_val(index);
}
inline void TensorProto::set_bool_val(int index, bool value) {
  _impl_.bool_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.bool_val)
}
inline void TensorProto::_internal_add_bool_val(bool value) {
  _impl_.bool_val_.Add(value);
}
inline void TensorProto::add_bool_val(bool value) {
  _internal_add_bool_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.bool_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
TensorProto::_internal_bool_val() const {
  return _impl_.bool_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
TensorProto::bool_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.bool_val)
  return _internal_bool_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
TensorProto::_internal_mutable_bool_val() {
  return &_impl_.bool_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
TensorProto::mutable_bool_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.bool_val)
  return _internal_mutable_bool_val();
}

// repeated double dcomplex_val = 12 [packed = true];
inline int TensorProto::_internal_dcomplex_val_size() const {
  return _impl_.dcomplex_val_.size();
}
inline int TensorProto::dcomplex_val_size() const {
  return _internal_dcomplex_val_size();
}
inline void TensorProto::clear_dcomplex_val() {
  _impl_.dcomplex_val_.Clear();
}
inline double TensorProto::_internal_dcomplex_val(int index) const {
  return _impl_.dcomplex_val_.Get(index);
}
inline double TensorProto::dcomplex_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.dcomplex_val)
  return _internal_dcomplex_val(index);
}
inline void TensorProto::set_dcomplex_val(int index, double value) {
  _impl_.dcomplex_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.dcomplex_val)
}
inline void TensorProto::_internal_add_dcomplex_val(double value) {
  _impl_.dcomplex_val_.Add(value);
}
inline void TensorProto::add_dcomplex_val(double value) {
  _internal_add_dcomplex_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.dcomplex_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TensorProto::_internal_dcomplex_val() const {
  return _impl_.dcomplex_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TensorProto::dcomplex_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.dcomplex_val)
  return _internal_dcomplex_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TensorProto::_internal_mutable_dcomplex_val() {
  return &_impl_.dcomplex_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TensorProto::mutable_dcomplex_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.dcomplex_val)
  return _internal_mutable_dcomplex_val();
}

// repeated .tensorflow.ResourceHandleProto resource_handle_val = 14;
inline int TensorProto::_internal_resource_handle_val_size() const {
  return _impl_.resource_handle_val_.size();
}
inline int TensorProto::resource_handle_val_size() const {
  return _internal_resource_handle_val_size();
}
inline ::tensorflow::ResourceHandleProto* TensorProto::mutable_resource_handle_val(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.resource_handle_val)
  return _impl_.resource_handle_val_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto >*
TensorProto::mutable_resource_handle_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.resource_handle_val)
  return &_impl_.resource_handle_val_;
}
inline const ::tensorflow::ResourceHandleProto& TensorProto::_internal_resource_handle_val(int index) const {
  return _impl_.resource_handle_val_.Get(index);
}
inline const ::tensorflow::ResourceHandleProto& TensorProto::resource_handle_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.resource_handle_val)
  return _internal_resource_handle_val(index);
}
inline ::tensorflow::ResourceHandleProto* TensorProto::_internal_add_resource_handle_val() {
  return _impl_.resource_handle_val_.Add();
}
inline ::tensorflow::ResourceHandleProto* TensorProto::add_resource_handle_val() {
  ::tensorflow::ResourceHandleProto* _add = _internal_add_resource_handle_val();
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.resource_handle_val)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto >&
TensorProto::resource_handle_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.resource_handle_val)
  return _impl_.resource_handle_val_;
}

// repeated .tensorflow.VariantTensorDataProto variant_val = 15;
inline int TensorProto::_internal_variant_val_size() const {
  return _impl_.variant_val_.size();
}
inline int TensorProto::variant_val_size() const {
  return _internal_variant_val_size();
}
inline void TensorProto::clear_variant_val() {
  _impl_.variant_val_.Clear();
}
inline ::tensorflow::VariantTensorDataProto* TensorProto::mutable_variant_val(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.variant_val)
  return _impl_.variant_val_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::VariantTensorDataProto >*
TensorProto::mutable_variant_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.variant_val)
  return &_impl_.variant_val_;
}
inline const ::tensorflow::VariantTensorDataProto& TensorProto::_internal_variant_val(int index) const {
  return _impl_.variant_val_.Get(index);
}
inline const ::tensorflow::VariantTensorDataProto& TensorProto::variant_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.variant_val)
  return _internal_variant_val(index);
}
inline ::tensorflow::VariantTensorDataProto* TensorProto::_internal_add_variant_val() {
  return _impl_.variant_val_.Add();
}
inline ::tensorflow::VariantTensorDataProto* TensorProto::add_variant_val() {
  ::tensorflow::VariantTensorDataProto* _add = _internal_add_variant_val();
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.variant_val)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::VariantTensorDataProto >&
TensorProto::variant_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.variant_val)
  return _impl_.variant_val_;
}

// repeated uint32 uint32_val = 16 [packed = true];
inline int TensorProto::_internal_uint32_val_size() const {
  return _impl_.uint32_val_.size();
}
inline int TensorProto::uint32_val_size() const {
  return _internal_uint32_val_size();
}
inline void TensorProto::clear_uint32_val() {
  _impl_.uint32_val_.Clear();
}
inline uint32_t TensorProto::_internal_uint32_val(int index) const {
  return _impl_.uint32_val_.Get(index);
}
inline uint32_t TensorProto::uint32_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.uint32_val)
  return _internal_uint32_val(index);
}
inline void TensorProto::set_uint32_val(int index, uint32_t value) {
  _impl_.uint32_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.uint32_val)
}
inline void TensorProto::_internal_add_uint32_val(uint32_t value) {
  _impl_.uint32_val_.Add(value);
}
inline void TensorProto::add_uint32_val(uint32_t value) {
  _internal_add_uint32_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.uint32_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
TensorProto::_internal_uint32_val() const {
  return _impl_.uint32_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
TensorProto::uint32_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.uint32_val)
  return _internal_uint32_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
TensorProto::_internal_mutable_uint32_val() {
  return &_impl_.uint32_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
TensorProto::mutable_uint32_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.uint32_val)
  return _internal_mutable_uint32_val();
}

// repeated uint64 uint64_val = 17 [packed = true];
inline int TensorProto::_internal_uint64_val_size() const {
  return _impl_.uint64_val_.size();
}
inline int TensorProto::uint64_val_size() const {
  return _internal_uint64_val_size();
}
inline void TensorProto::clear_uint64_val() {
  _impl_.uint64_val_.Clear();
}
inline uint64_t TensorProto::_internal_uint64_val(int index) const {
  return _impl_.uint64_val_.Get(index);
}
inline uint64_t TensorProto::uint64_val(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.uint64_val)
  return _internal_uint64_val(index);
}
inline void TensorProto::set_uint64_val(int index, uint64_t value) {
  _impl_.uint64_val_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.uint64_val)
}
inline void TensorProto::_internal_add_uint64_val(uint64_t value) {
  _impl_.uint64_val_.Add(value);
}
inline void TensorProto::add_uint64_val(uint64_t value) {
  _internal_add_uint64_val(value);
  // @@protoc_insertion_point(field_add:tensorflow.TensorProto.uint64_val)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
TensorProto::_internal_uint64_val() const {
  return _impl_.uint64_val_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >&
TensorProto::uint64_val() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorProto.uint64_val)
  return _internal_uint64_val();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
TensorProto::_internal_mutable_uint64_val() {
  return &_impl_.uint64_val_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint64_t >*
TensorProto::mutable_uint64_val() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorProto.uint64_val)
  return _internal_mutable_uint64_val();
}

// bytes float8_val = 18;
inline void TensorProto::clear_float8_val() {
  _impl_.float8_val_.ClearToEmpty();
}
inline const std::string& TensorProto::float8_val() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorProto.float8_val)
  return _internal_float8_val();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorProto::set_float8_val(ArgT0&& arg0, ArgT... args) {
 
 _impl_.float8_val_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorProto.float8_val)
}
inline std::string* TensorProto::mutable_float8_val() {
  std::string* _s = _internal_mutable_float8_val();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorProto.float8_val)
  return _s;
}
inline const std::string& TensorProto::_internal_float8_val() const {
  return _impl_.float8_val_.Get();
}
inline void TensorProto::_internal_set_float8_val(const std::string& value) {
  
  _impl_.float8_val_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorProto::_internal_mutable_float8_val() {
  
  return _impl_.float8_val_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorProto::release_float8_val() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorProto.float8_val)
  return _impl_.float8_val_.Release();
}
inline void TensorProto::set_allocated_float8_val(std::string* float8_val) {
  if (float8_val != nullptr) {
    
  } else {
    
  }
  _impl_.float8_val_.SetAllocated(float8_val, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.float8_val_.IsDefault()) {
    _impl_.float8_val_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorProto.float8_val)
}

// -------------------------------------------------------------------

// VariantTensorDataProto

// string type_name = 1;
inline void VariantTensorDataProto::clear_type_name() {
  _impl_.type_name_.ClearToEmpty();
}
inline const std::string& VariantTensorDataProto::type_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariantTensorDataProto.type_name)
  return _internal_type_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VariantTensorDataProto::set_type_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VariantTensorDataProto.type_name)
}
inline std::string* VariantTensorDataProto::mutable_type_name() {
  std::string* _s = _internal_mutable_type_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.VariantTensorDataProto.type_name)
  return _s;
}
inline const std::string& VariantTensorDataProto::_internal_type_name() const {
  return _impl_.type_name_.Get();
}
inline void VariantTensorDataProto::_internal_set_type_name(const std::string& value) {
  
  _impl_.type_name_.Set(value, GetArenaForAllocation());
}
inline std::string* VariantTensorDataProto::_internal_mutable_type_name() {
  
  return _impl_.type_name_.Mutable(GetArenaForAllocation());
}
inline std::string* VariantTensorDataProto::release_type_name() {
  // @@protoc_insertion_point(field_release:tensorflow.VariantTensorDataProto.type_name)
  return _impl_.type_name_.Release();
}
inline void VariantTensorDataProto::set_allocated_type_name(std::string* type_name) {
  if (type_name != nullptr) {
    
  } else {
    
  }
  _impl_.type_name_.SetAllocated(type_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_name_.IsDefault()) {
    _impl_.type_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariantTensorDataProto.type_name)
}

// bytes metadata = 2;
inline void VariantTensorDataProto::clear_metadata() {
  _impl_.metadata_.ClearToEmpty();
}
inline const std::string& VariantTensorDataProto::metadata() const {
  // @@protoc_insertion_point(field_get:tensorflow.VariantTensorDataProto.metadata)
  return _internal_metadata();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VariantTensorDataProto::set_metadata(ArgT0&& arg0, ArgT... args) {
 
 _impl_.metadata_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VariantTensorDataProto.metadata)
}
inline std::string* VariantTensorDataProto::mutable_metadata() {
  std::string* _s = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:tensorflow.VariantTensorDataProto.metadata)
  return _s;
}
inline const std::string& VariantTensorDataProto::_internal_metadata() const {
  return _impl_.metadata_.Get();
}
inline void VariantTensorDataProto::_internal_set_metadata(const std::string& value) {
  
  _impl_.metadata_.Set(value, GetArenaForAllocation());
}
inline std::string* VariantTensorDataProto::_internal_mutable_metadata() {
  
  return _impl_.metadata_.Mutable(GetArenaForAllocation());
}
inline std::string* VariantTensorDataProto::release_metadata() {
  // @@protoc_insertion_point(field_release:tensorflow.VariantTensorDataProto.metadata)
  return _impl_.metadata_.Release();
}
inline void VariantTensorDataProto::set_allocated_metadata(std::string* metadata) {
  if (metadata != nullptr) {
    
  } else {
    
  }
  _impl_.metadata_.SetAllocated(metadata, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.metadata_.IsDefault()) {
    _impl_.metadata_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VariantTensorDataProto.metadata)
}

// repeated .tensorflow.TensorProto tensors = 3;
inline int VariantTensorDataProto::_internal_tensors_size() const {
  return _impl_.tensors_.size();
}
inline int VariantTensorDataProto::tensors_size() const {
  return _internal_tensors_size();
}
inline void VariantTensorDataProto::clear_tensors() {
  _impl_.tensors_.Clear();
}
inline ::tensorflow::TensorProto* VariantTensorDataProto::mutable_tensors(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.VariantTensorDataProto.tensors)
  return _impl_.tensors_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
VariantTensorDataProto::mutable_tensors() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.VariantTensorDataProto.tensors)
  return &_impl_.tensors_;
}
inline const ::tensorflow::TensorProto& VariantTensorDataProto::_internal_tensors(int index) const {
  return _impl_.tensors_.Get(index);
}
inline const ::tensorflow::TensorProto& VariantTensorDataProto::tensors(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.VariantTensorDataProto.tensors)
  return _internal_tensors(index);
}
inline ::tensorflow::TensorProto* VariantTensorDataProto::_internal_add_tensors() {
  return _impl_.tensors_.Add();
}
inline ::tensorflow::TensorProto* VariantTensorDataProto::add_tensors() {
  ::tensorflow::TensorProto* _add = _internal_add_tensors();
  // @@protoc_insertion_point(field_add:tensorflow.VariantTensorDataProto.tensors)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
VariantTensorDataProto::tensors() const {
  // @@protoc_insertion_point(field_list:tensorflow.VariantTensorDataProto.tensors)
  return _impl_.tensors_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2ftensor_2eproto
