/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

OUR_GUID_ENTRY(MEDIATYPE_MPEG2_PACK,
		0x36523B13,0x8EE5,0x11d1,0x8C,0xA3,0x00,0x60,0xB0,0x57,0x66,0x4A)

OUR_GUID_ENTRY(MEDIATYPE_MPEG2_PES,
		0xe06d8020,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_WMDRM_TRANSPORT,
		0x18BEC4EA,0x4676,0x450e,0xB4,0x78,0x0C,0xD8,0x4C,0x54,0xB3,0x27)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_VIDEO,
		0xe06d8026,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(FORMAT_MPEG2_VIDEO,
		0xe06d80e3,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(FORMAT_VIDEOINFO2,
		0xf72a76A0,0xeb0a,0x11d0,0xac,0xe4,0x0,0x0,0xc0,0xcc,0x16,0xba)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_PROGRAM,
		0xe06d8022,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_TRANSPORT,
		0xe06d8023,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_AUDIO,
		0xe06d802b,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_DOLBY_AC3,
		0xe06d802c,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_DVD_SUBPICTURE,
		0xe06d802d,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_DVD_LPCM_AUDIO,
		0xe06d8032,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIATYPE_DVD_ENCRYPTED_PACK,
		0xed0b916a,0x044d,0x11d1,0xaa,0x78,0x00,0xc0,0x04f,0xc3,0x1d,0x60)

OUR_GUID_ENTRY(MEDIATYPE_DVD_NAVIGATION,
		0xe06d802e,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_DVD_NAVIGATION_PCI,
		0xe06d802f,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_DVD_NAVIGATION_DSI,
		0xe06d8030,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_DVD_NAVIGATION_PROVIDER,
		0xe06d8031,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(FORMAT_MPEG2Video,
		0xe06d80e3,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(FORMAT_DolbyAC3,
		0xe06d80e4,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(FORMAT_MPEG2Audio,
		0xe06d80e5,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(FORMAT_DVD_LPCMAudio,
		0xe06d80e6,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(AM_KSPROPSETID_AC3,
		0xBFABE720,0x6E1F,0x11D0,0xBC,0xF2,0x44,0x45,0x53,0x54,0x00,0x00)

OUR_GUID_ENTRY(AM_KSPROPSETID_DvdSubPic,
		0xac390460,0x43af,0x11d0,0xbd,0x6a,0x00,0x35,0x05,0xc1,0x03,0xa9)

OUR_GUID_ENTRY(AM_KSPROPSETID_CopyProt,
		0x0E8A0A40,0x6AEF,0x11D0,0x9E,0xD0,0x00,0xA0,0x24,0xCA,0x19,0xB3)

OUR_GUID_ENTRY(AM_KSPROPSETID_TSRateChange,
		0xa503c5c0,0x1d1d,0x11d1,0xad,0x80,0x44,0x45,0x53,0x54,0x0,0x0)

OUR_GUID_ENTRY(AM_KSPROPSETID_MPEG4_MediaType_Attributes,
		0xff6c4bfa,0x7a9,0x4c7b,0xa2,0x37,0x67,0x2f,0x9d,0x68,0x6,0x5f)

OUR_GUID_ENTRY(AM_KSCATEGORY_CAPTURE,
		0x65E8773D,0x8F56,0x11D0,0xA3,0xB9,0x00,0xA0,0xC9,0x22,0x31,0x96)

OUR_GUID_ENTRY(AM_KSCATEGORY_RENDER,
		0x65E8773E,0x8F56,0x11D0,0xA3,0xB9,0x00,0xA0,0xC9,0x22,0x31,0x96)

OUR_GUID_ENTRY(AM_KSCATEGORY_DATACOMPRESSOR,
		0x1E84C900,0x7E70,0x11D0,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00)

OUR_GUID_ENTRY(AM_KSCATEGORY_AUDIO,
		0x6994AD04,0x93EF,0x11D0,0xA3,0xCC,0x00,0xA0,0xC9,0x22,0x31,0x96)

OUR_GUID_ENTRY(AM_KSCATEGORY_VIDEO,
		0x6994AD05,0x93EF,0x11D0,0xA3,0xCC,0x00,0xA0,0xC9,0x22,0x31,0x96)

OUR_GUID_ENTRY(AM_KSCATEGORY_TVTUNER,
		0xa799a800,0xa46d,0x11d0,0xa1,0x8c,0x00,0xa0,0x24,0x01,0xdc,0xd4)

OUR_GUID_ENTRY(AM_KSCATEGORY_CROSSBAR,
		0xa799a801,0xa46d,0x11d0,0xa1,0x8c,0x00,0xa0,0x24,0x01,0xdc,0xd4)

OUR_GUID_ENTRY(AM_KSCATEGORY_TVAUDIO,
		0xa799a802,0xa46d,0x11d0,0xa1,0x8c,0x00,0xa0,0x24,0x01,0xdc,0xd4)

OUR_GUID_ENTRY(AM_KSCATEGORY_VBICODEC,
		0x07dad660,0x22f1,0x11d1,0xa9,0xf4,0x00,0xc0,0x4f,0xbb,0xde,0x8f)

OUR_GUID_ENTRY(AM_KSCATEGORY_SPLITTER,
		0x0A4252A0,0x7E70,0x11D0,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00)

OUR_GUID_ENTRY(IID_IKsInterfaceHandler,
		0xD3ABC7E0,0x9A61,0x11D0,0xA4,0x0D,0x00,0xA0,0xC9,0x22,0x31,0x96)

OUR_GUID_ENTRY(IID_IKsDataTypeHandler,
		0x5FFBAA02,0x49A3,0x11D0,0x9F,0x36,0x00,0xAA,0x00,0xA2,0x16,0xA1)

OUR_GUID_ENTRY(IID_IKsPin,
		0xb61178d1,0xa2d9,0x11cf,0x9e,0x53,0x00,0xaa,0x00,0xa2,0x16,0xa1)

OUR_GUID_ENTRY(IID_IKsControl,
		0x28F54685,0x06FD,0x11D2,0xB2,0x7A,0x00,0xA0,0xC9,0x22,0x31,0x96)

OUR_GUID_ENTRY(IID_IKsPinFactory,
		0xCD5EBE6B,0x8B6E,0x11D1,0x8A,0xE0,0x00,0xA0,0xC9,0x22,0x31,0x96)

OUR_GUID_ENTRY(AM_INTERFACESETID_Standard,
		0x1A8766A0,0x62CE,0x11CF,0xA5,0xD6,0x28,0xDB,0x04,0xC1,0x00,0x00)

#if ( (NTDDI_VERSION >= NTDDI_WINXPSP2) && (NTDDI_VERSION < NTDDI_WS03) ) || (NTDDI_VERSION >= NTDDI_WS03SP1)
OUR_GUID_ENTRY(MEDIATYPE_MPEG2_SECTIONS,
		0x455f176c,0x4b06,0x47ce,0x9a,0xef,0x8c,0xae,0xf7,0x3d,0xf7,0xb5)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_VERSIONED_TABLES,
		0x1ed988b0,0x3ffc,0x4523,0x87,0x25,0x34,0x7b,0xee,0xc1,0xa8,0xa0)

OUR_GUID_ENTRY(MEDIASUBTYPE_ATSC_SI,
		0xb3c7397c,0xd303,0x414d,0xb3,0x3c,0x4e,0xd2,0xc9,0xd2,0x97,0x33)

OUR_GUID_ENTRY(MEDIASUBTYPE_DVB_SI,
		0xe9dd31a3,0x221d,0x4adb,0x85,0x32,0x9a,0xf3,0x9,0xc1,0xa4,0x8)

OUR_GUID_ENTRY(MEDIASUBTYPE_ISDB_SI,
		0xe89ad298,0x3601,0x4b06,0xaa,0xec,0x9d,0xde,0xed,0xcc,0x5b,0xd0)

OUR_GUID_ENTRY(MEDIASUBTYPE_TIF_SI,
		0xec232eb2,0xcb96,0x4191,0xb2,0x26,0xe,0xa1,0x29,0xf3,0x82,0x50)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2DATA,
		0xc892e55b,0x252d,0x42b5,0xa3,0x16,0xd9,0x97,0xe7,0xa5,0xd9,0x95)
#endif
/* ( (NTDDI_VERSION >= NTDDI_WINXPSP2) && (NTDDI_VERSION < NTDDI_WS03) ) ||
						(NTDDI_VERSION >= NTDDI_WS03SP1) */

#if (NTDDI_VERSION >= NTDDI_WINXP)
OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_TRANSPORT_STRIDE,
		0x138aa9a4,0x1ee2,0x4c5b,0x98,0x8e,0x19,0xab,0xfd,0xbc,0x8a,0x11)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_UDCR_TRANSPORT,
		0x18BEC4EA,0x4676,0x450e,0xB4,0x78,0x0C,0xD8,0x4C,0x54,0xB3,0x27)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_PBDA_TRANSPORT_RAW,
		0x0d7aed42,0xcb9a,0x11db,0x97,0x5,0x0,0x50,0x56,0xc0,0x0,0x8)

OUR_GUID_ENTRY(MEDIASUBTYPE_MPEG2_PBDA_TRANSPORT_PROCESSED,
		0xaf748dd4,0xd80,0x11db,0x97,0x5,0x0,0x50,0x56,0xc0,0x0,0x8)

OUR_GUID_ENTRY(MEDIASUBTYPE_DTS,
		0xe06d8033,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(MEDIASUBTYPE_SDDS,
		0xe06d8034,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x05f,0x6c,0xbb,0xea)

OUR_GUID_ENTRY(AM_KSPROPSETID_DVD_RateChange,
		0x3577eb09,0x9582,0x477f,0xb2,0x9c,0xb0,0xc4,0x52,0xa4,0xff,0x9a)

OUR_GUID_ENTRY(AM_KSPROPSETID_DvdKaraoke,
		0xae4720ae,0xaa71,0x42d8,0xb8,0x2a,0xff,0xfd,0xf5,0x8b,0x76,0xfd)

OUR_GUID_ENTRY(AM_KSPROPSETID_FrameStep,
		0xc830acbd,0xab07,0x492f,0x88,0x52,0x45,0xb6,0x98,0x7c,0x29,0x79)
#endif /* NTDDI_VERSION >= NTDDI_WINXP */

#if (NTDDI_VERSION >= NTDDI_WS03SP1)
OUR_GUID_ENTRY(AM_KSCATEGORY_VBICODEC_MI,
		0x9c24a977,0x951,0x451a,0x80,0x6,0xe,0x49,0xbd,0x28,0xcd,0x5f)
#endif /* NTDDI_VERSION >= NTDDI_WS03SP1 */

