// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/service_config.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fservice_5fconfig_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fservice_5fconfig_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/data_service.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fservice_5fconfig_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fservice_5fconfig_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fservice_5fconfig_2eproto;
namespace tensorflow {
namespace data {
namespace experimental {
class DispatcherConfig;
struct DispatcherConfigDefaultTypeInternal;
extern DispatcherConfigDefaultTypeInternal _DispatcherConfig_default_instance_;
class WorkerConfig;
struct WorkerConfigDefaultTypeInternal;
extern WorkerConfigDefaultTypeInternal _WorkerConfig_default_instance_;
}  // namespace experimental
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::experimental::DispatcherConfig* Arena::CreateMaybeMessage<::tensorflow::data::experimental::DispatcherConfig>(Arena*);
template<> ::tensorflow::data::experimental::WorkerConfig* Arena::CreateMaybeMessage<::tensorflow::data::experimental::WorkerConfig>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {
namespace experimental {

// ===================================================================

class DispatcherConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.DispatcherConfig) */ {
 public:
  inline DispatcherConfig() : DispatcherConfig(nullptr) {}
  ~DispatcherConfig() override;
  explicit PROTOBUF_CONSTEXPR DispatcherConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DispatcherConfig(const DispatcherConfig& from);
  DispatcherConfig(DispatcherConfig&& from) noexcept
    : DispatcherConfig() {
    *this = ::std::move(from);
  }

  inline DispatcherConfig& operator=(const DispatcherConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline DispatcherConfig& operator=(DispatcherConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DispatcherConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const DispatcherConfig* internal_default_instance() {
    return reinterpret_cast<const DispatcherConfig*>(
               &_DispatcherConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DispatcherConfig& a, DispatcherConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(DispatcherConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DispatcherConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DispatcherConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DispatcherConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DispatcherConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DispatcherConfig& from) {
    DispatcherConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DispatcherConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.DispatcherConfig";
  }
  protected:
  explicit DispatcherConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWorkerAddressesFieldNumber = 7,
    kProtocolFieldNumber = 2,
    kWorkDirFieldNumber = 3,
    kPortFieldNumber = 1,
    kJobGcCheckIntervalMsFieldNumber = 5,
    kJobGcTimeoutMsFieldNumber = 6,
    kClientTimeoutMsFieldNumber = 8,
    kFaultTolerantModeFieldNumber = 4,
    kGcDynamicShardingJobsFieldNumber = 11,
    kDeploymentModeFieldNumber = 9,
    kWorkerTimeoutMsFieldNumber = 10,
    kWorkerMaxConcurrentSnapshotsFieldNumber = 12,
  };
  // repeated string worker_addresses = 7;
  int worker_addresses_size() const;
  private:
  int _internal_worker_addresses_size() const;
  public:
  void clear_worker_addresses();
  const std::string& worker_addresses(int index) const;
  std::string* mutable_worker_addresses(int index);
  void set_worker_addresses(int index, const std::string& value);
  void set_worker_addresses(int index, std::string&& value);
  void set_worker_addresses(int index, const char* value);
  void set_worker_addresses(int index, const char* value, size_t size);
  std::string* add_worker_addresses();
  void add_worker_addresses(const std::string& value);
  void add_worker_addresses(std::string&& value);
  void add_worker_addresses(const char* value);
  void add_worker_addresses(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& worker_addresses() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_worker_addresses();
  private:
  const std::string& _internal_worker_addresses(int index) const;
  std::string* _internal_add_worker_addresses();
  public:

  // string protocol = 2;
  void clear_protocol();
  const std::string& protocol() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_protocol(ArgT0&& arg0, ArgT... args);
  std::string* mutable_protocol();
  PROTOBUF_NODISCARD std::string* release_protocol();
  void set_allocated_protocol(std::string* protocol);
  private:
  const std::string& _internal_protocol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_protocol(const std::string& value);
  std::string* _internal_mutable_protocol();
  public:

  // string work_dir = 3;
  void clear_work_dir();
  const std::string& work_dir() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_work_dir(ArgT0&& arg0, ArgT... args);
  std::string* mutable_work_dir();
  PROTOBUF_NODISCARD std::string* release_work_dir();
  void set_allocated_work_dir(std::string* work_dir);
  private:
  const std::string& _internal_work_dir() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_work_dir(const std::string& value);
  std::string* _internal_mutable_work_dir();
  public:

  // int64 port = 1;
  void clear_port();
  int64_t port() const;
  void set_port(int64_t value);
  private:
  int64_t _internal_port() const;
  void _internal_set_port(int64_t value);
  public:

  // int64 job_gc_check_interval_ms = 5;
  void clear_job_gc_check_interval_ms();
  int64_t job_gc_check_interval_ms() const;
  void set_job_gc_check_interval_ms(int64_t value);
  private:
  int64_t _internal_job_gc_check_interval_ms() const;
  void _internal_set_job_gc_check_interval_ms(int64_t value);
  public:

  // int64 job_gc_timeout_ms = 6;
  void clear_job_gc_timeout_ms();
  int64_t job_gc_timeout_ms() const;
  void set_job_gc_timeout_ms(int64_t value);
  private:
  int64_t _internal_job_gc_timeout_ms() const;
  void _internal_set_job_gc_timeout_ms(int64_t value);
  public:

  // int64 client_timeout_ms = 8;
  void clear_client_timeout_ms();
  int64_t client_timeout_ms() const;
  void set_client_timeout_ms(int64_t value);
  private:
  int64_t _internal_client_timeout_ms() const;
  void _internal_set_client_timeout_ms(int64_t value);
  public:

  // bool fault_tolerant_mode = 4;
  void clear_fault_tolerant_mode();
  bool fault_tolerant_mode() const;
  void set_fault_tolerant_mode(bool value);
  private:
  bool _internal_fault_tolerant_mode() const;
  void _internal_set_fault_tolerant_mode(bool value);
  public:

  // bool gc_dynamic_sharding_jobs = 11;
  void clear_gc_dynamic_sharding_jobs();
  bool gc_dynamic_sharding_jobs() const;
  void set_gc_dynamic_sharding_jobs(bool value);
  private:
  bool _internal_gc_dynamic_sharding_jobs() const;
  void _internal_set_gc_dynamic_sharding_jobs(bool value);
  public:

  // .tensorflow.data.DeploymentMode deployment_mode = 9;
  void clear_deployment_mode();
  ::tensorflow::data::DeploymentMode deployment_mode() const;
  void set_deployment_mode(::tensorflow::data::DeploymentMode value);
  private:
  ::tensorflow::data::DeploymentMode _internal_deployment_mode() const;
  void _internal_set_deployment_mode(::tensorflow::data::DeploymentMode value);
  public:

  // int64 worker_timeout_ms = 10;
  void clear_worker_timeout_ms();
  int64_t worker_timeout_ms() const;
  void set_worker_timeout_ms(int64_t value);
  private:
  int64_t _internal_worker_timeout_ms() const;
  void _internal_set_worker_timeout_ms(int64_t value);
  public:

  // int64 worker_max_concurrent_snapshots = 12;
  void clear_worker_max_concurrent_snapshots();
  int64_t worker_max_concurrent_snapshots() const;
  void set_worker_max_concurrent_snapshots(int64_t value);
  private:
  int64_t _internal_worker_max_concurrent_snapshots() const;
  void _internal_set_worker_max_concurrent_snapshots(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.DispatcherConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> worker_addresses_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr protocol_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr work_dir_;
    int64_t port_;
    int64_t job_gc_check_interval_ms_;
    int64_t job_gc_timeout_ms_;
    int64_t client_timeout_ms_;
    bool fault_tolerant_mode_;
    bool gc_dynamic_sharding_jobs_;
    int deployment_mode_;
    int64_t worker_timeout_ms_;
    int64_t worker_max_concurrent_snapshots_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fservice_5fconfig_2eproto;
};
// -------------------------------------------------------------------

class WorkerConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.experimental.WorkerConfig) */ {
 public:
  inline WorkerConfig() : WorkerConfig(nullptr) {}
  ~WorkerConfig() override;
  explicit PROTOBUF_CONSTEXPR WorkerConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WorkerConfig(const WorkerConfig& from);
  WorkerConfig(WorkerConfig&& from) noexcept
    : WorkerConfig() {
    *this = ::std::move(from);
  }

  inline WorkerConfig& operator=(const WorkerConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline WorkerConfig& operator=(WorkerConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WorkerConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const WorkerConfig* internal_default_instance() {
    return reinterpret_cast<const WorkerConfig*>(
               &_WorkerConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(WorkerConfig& a, WorkerConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(WorkerConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WorkerConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WorkerConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WorkerConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WorkerConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WorkerConfig& from) {
    WorkerConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WorkerConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.experimental.WorkerConfig";
  }
  protected:
  explicit WorkerConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWorkerTagsFieldNumber = 10,
    kProtocolFieldNumber = 2,
    kDispatcherAddressFieldNumber = 3,
    kWorkerAddressFieldNumber = 4,
    kDataTransferProtocolFieldNumber = 7,
    kDataTransferAddressFieldNumber = 8,
    kPortFieldNumber = 1,
    kHeartbeatIntervalMsFieldNumber = 5,
    kDispatcherTimeoutMsFieldNumber = 6,
    kShutdownQuietPeriodMsFieldNumber = 9,
    kCrossTrainerCacheSizeBytesFieldNumber = 11,
    kSnapshotMaxChunkSizeBytesFieldNumber = 12,
    kDataTransferPortFieldNumber = 13,
  };
  // repeated string worker_tags = 10;
  int worker_tags_size() const;
  private:
  int _internal_worker_tags_size() const;
  public:
  void clear_worker_tags();
  const std::string& worker_tags(int index) const;
  std::string* mutable_worker_tags(int index);
  void set_worker_tags(int index, const std::string& value);
  void set_worker_tags(int index, std::string&& value);
  void set_worker_tags(int index, const char* value);
  void set_worker_tags(int index, const char* value, size_t size);
  std::string* add_worker_tags();
  void add_worker_tags(const std::string& value);
  void add_worker_tags(std::string&& value);
  void add_worker_tags(const char* value);
  void add_worker_tags(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& worker_tags() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_worker_tags();
  private:
  const std::string& _internal_worker_tags(int index) const;
  std::string* _internal_add_worker_tags();
  public:

  // string protocol = 2;
  void clear_protocol();
  const std::string& protocol() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_protocol(ArgT0&& arg0, ArgT... args);
  std::string* mutable_protocol();
  PROTOBUF_NODISCARD std::string* release_protocol();
  void set_allocated_protocol(std::string* protocol);
  private:
  const std::string& _internal_protocol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_protocol(const std::string& value);
  std::string* _internal_mutable_protocol();
  public:

  // string dispatcher_address = 3;
  void clear_dispatcher_address();
  const std::string& dispatcher_address() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dispatcher_address(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dispatcher_address();
  PROTOBUF_NODISCARD std::string* release_dispatcher_address();
  void set_allocated_dispatcher_address(std::string* dispatcher_address);
  private:
  const std::string& _internal_dispatcher_address() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dispatcher_address(const std::string& value);
  std::string* _internal_mutable_dispatcher_address();
  public:

  // string worker_address = 4;
  void clear_worker_address();
  const std::string& worker_address() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_worker_address(ArgT0&& arg0, ArgT... args);
  std::string* mutable_worker_address();
  PROTOBUF_NODISCARD std::string* release_worker_address();
  void set_allocated_worker_address(std::string* worker_address);
  private:
  const std::string& _internal_worker_address() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_worker_address(const std::string& value);
  std::string* _internal_mutable_worker_address();
  public:

  // string data_transfer_protocol = 7;
  void clear_data_transfer_protocol();
  const std::string& data_transfer_protocol() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data_transfer_protocol(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data_transfer_protocol();
  PROTOBUF_NODISCARD std::string* release_data_transfer_protocol();
  void set_allocated_data_transfer_protocol(std::string* data_transfer_protocol);
  private:
  const std::string& _internal_data_transfer_protocol() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data_transfer_protocol(const std::string& value);
  std::string* _internal_mutable_data_transfer_protocol();
  public:

  // string data_transfer_address = 8;
  void clear_data_transfer_address();
  const std::string& data_transfer_address() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data_transfer_address(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data_transfer_address();
  PROTOBUF_NODISCARD std::string* release_data_transfer_address();
  void set_allocated_data_transfer_address(std::string* data_transfer_address);
  private:
  const std::string& _internal_data_transfer_address() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data_transfer_address(const std::string& value);
  std::string* _internal_mutable_data_transfer_address();
  public:

  // int64 port = 1;
  void clear_port();
  int64_t port() const;
  void set_port(int64_t value);
  private:
  int64_t _internal_port() const;
  void _internal_set_port(int64_t value);
  public:

  // int64 heartbeat_interval_ms = 5;
  void clear_heartbeat_interval_ms();
  int64_t heartbeat_interval_ms() const;
  void set_heartbeat_interval_ms(int64_t value);
  private:
  int64_t _internal_heartbeat_interval_ms() const;
  void _internal_set_heartbeat_interval_ms(int64_t value);
  public:

  // int64 dispatcher_timeout_ms = 6;
  void clear_dispatcher_timeout_ms();
  int64_t dispatcher_timeout_ms() const;
  void set_dispatcher_timeout_ms(int64_t value);
  private:
  int64_t _internal_dispatcher_timeout_ms() const;
  void _internal_set_dispatcher_timeout_ms(int64_t value);
  public:

  // int64 shutdown_quiet_period_ms = 9;
  void clear_shutdown_quiet_period_ms();
  int64_t shutdown_quiet_period_ms() const;
  void set_shutdown_quiet_period_ms(int64_t value);
  private:
  int64_t _internal_shutdown_quiet_period_ms() const;
  void _internal_set_shutdown_quiet_period_ms(int64_t value);
  public:

  // int64 cross_trainer_cache_size_bytes = 11;
  void clear_cross_trainer_cache_size_bytes();
  int64_t cross_trainer_cache_size_bytes() const;
  void set_cross_trainer_cache_size_bytes(int64_t value);
  private:
  int64_t _internal_cross_trainer_cache_size_bytes() const;
  void _internal_set_cross_trainer_cache_size_bytes(int64_t value);
  public:

  // int64 snapshot_max_chunk_size_bytes = 12;
  void clear_snapshot_max_chunk_size_bytes();
  int64_t snapshot_max_chunk_size_bytes() const;
  void set_snapshot_max_chunk_size_bytes(int64_t value);
  private:
  int64_t _internal_snapshot_max_chunk_size_bytes() const;
  void _internal_set_snapshot_max_chunk_size_bytes(int64_t value);
  public:

  // int64 data_transfer_port = 13;
  void clear_data_transfer_port();
  int64_t data_transfer_port() const;
  void set_data_transfer_port(int64_t value);
  private:
  int64_t _internal_data_transfer_port() const;
  void _internal_set_data_transfer_port(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.experimental.WorkerConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> worker_tags_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr protocol_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dispatcher_address_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr worker_address_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_transfer_protocol_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_transfer_address_;
    int64_t port_;
    int64_t heartbeat_interval_ms_;
    int64_t dispatcher_timeout_ms_;
    int64_t shutdown_quiet_period_ms_;
    int64_t cross_trainer_cache_size_bytes_;
    int64_t snapshot_max_chunk_size_bytes_;
    int64_t data_transfer_port_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fservice_5fconfig_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DispatcherConfig

// int64 port = 1;
inline void DispatcherConfig::clear_port() {
  _impl_.port_ = int64_t{0};
}
inline int64_t DispatcherConfig::_internal_port() const {
  return _impl_.port_;
}
inline int64_t DispatcherConfig::port() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.port)
  return _internal_port();
}
inline void DispatcherConfig::_internal_set_port(int64_t value) {
  
  _impl_.port_ = value;
}
inline void DispatcherConfig::set_port(int64_t value) {
  _internal_set_port(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.port)
}

// string protocol = 2;
inline void DispatcherConfig::clear_protocol() {
  _impl_.protocol_.ClearToEmpty();
}
inline const std::string& DispatcherConfig::protocol() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.protocol)
  return _internal_protocol();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DispatcherConfig::set_protocol(ArgT0&& arg0, ArgT... args) {
 
 _impl_.protocol_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.protocol)
}
inline std::string* DispatcherConfig::mutable_protocol() {
  std::string* _s = _internal_mutable_protocol();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.DispatcherConfig.protocol)
  return _s;
}
inline const std::string& DispatcherConfig::_internal_protocol() const {
  return _impl_.protocol_.Get();
}
inline void DispatcherConfig::_internal_set_protocol(const std::string& value) {
  
  _impl_.protocol_.Set(value, GetArenaForAllocation());
}
inline std::string* DispatcherConfig::_internal_mutable_protocol() {
  
  return _impl_.protocol_.Mutable(GetArenaForAllocation());
}
inline std::string* DispatcherConfig::release_protocol() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.DispatcherConfig.protocol)
  return _impl_.protocol_.Release();
}
inline void DispatcherConfig::set_allocated_protocol(std::string* protocol) {
  if (protocol != nullptr) {
    
  } else {
    
  }
  _impl_.protocol_.SetAllocated(protocol, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.protocol_.IsDefault()) {
    _impl_.protocol_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.DispatcherConfig.protocol)
}

// string work_dir = 3;
inline void DispatcherConfig::clear_work_dir() {
  _impl_.work_dir_.ClearToEmpty();
}
inline const std::string& DispatcherConfig::work_dir() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.work_dir)
  return _internal_work_dir();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DispatcherConfig::set_work_dir(ArgT0&& arg0, ArgT... args) {
 
 _impl_.work_dir_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.work_dir)
}
inline std::string* DispatcherConfig::mutable_work_dir() {
  std::string* _s = _internal_mutable_work_dir();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.DispatcherConfig.work_dir)
  return _s;
}
inline const std::string& DispatcherConfig::_internal_work_dir() const {
  return _impl_.work_dir_.Get();
}
inline void DispatcherConfig::_internal_set_work_dir(const std::string& value) {
  
  _impl_.work_dir_.Set(value, GetArenaForAllocation());
}
inline std::string* DispatcherConfig::_internal_mutable_work_dir() {
  
  return _impl_.work_dir_.Mutable(GetArenaForAllocation());
}
inline std::string* DispatcherConfig::release_work_dir() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.DispatcherConfig.work_dir)
  return _impl_.work_dir_.Release();
}
inline void DispatcherConfig::set_allocated_work_dir(std::string* work_dir) {
  if (work_dir != nullptr) {
    
  } else {
    
  }
  _impl_.work_dir_.SetAllocated(work_dir, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.work_dir_.IsDefault()) {
    _impl_.work_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.DispatcherConfig.work_dir)
}

// bool fault_tolerant_mode = 4;
inline void DispatcherConfig::clear_fault_tolerant_mode() {
  _impl_.fault_tolerant_mode_ = false;
}
inline bool DispatcherConfig::_internal_fault_tolerant_mode() const {
  return _impl_.fault_tolerant_mode_;
}
inline bool DispatcherConfig::fault_tolerant_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.fault_tolerant_mode)
  return _internal_fault_tolerant_mode();
}
inline void DispatcherConfig::_internal_set_fault_tolerant_mode(bool value) {
  
  _impl_.fault_tolerant_mode_ = value;
}
inline void DispatcherConfig::set_fault_tolerant_mode(bool value) {
  _internal_set_fault_tolerant_mode(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.fault_tolerant_mode)
}

// repeated string worker_addresses = 7;
inline int DispatcherConfig::_internal_worker_addresses_size() const {
  return _impl_.worker_addresses_.size();
}
inline int DispatcherConfig::worker_addresses_size() const {
  return _internal_worker_addresses_size();
}
inline void DispatcherConfig::clear_worker_addresses() {
  _impl_.worker_addresses_.Clear();
}
inline std::string* DispatcherConfig::add_worker_addresses() {
  std::string* _s = _internal_add_worker_addresses();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
  return _s;
}
inline const std::string& DispatcherConfig::_internal_worker_addresses(int index) const {
  return _impl_.worker_addresses_.Get(index);
}
inline const std::string& DispatcherConfig::worker_addresses(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
  return _internal_worker_addresses(index);
}
inline std::string* DispatcherConfig::mutable_worker_addresses(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
  return _impl_.worker_addresses_.Mutable(index);
}
inline void DispatcherConfig::set_worker_addresses(int index, const std::string& value) {
  _impl_.worker_addresses_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
}
inline void DispatcherConfig::set_worker_addresses(int index, std::string&& value) {
  _impl_.worker_addresses_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
}
inline void DispatcherConfig::set_worker_addresses(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.worker_addresses_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
}
inline void DispatcherConfig::set_worker_addresses(int index, const char* value, size_t size) {
  _impl_.worker_addresses_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
}
inline std::string* DispatcherConfig::_internal_add_worker_addresses() {
  return _impl_.worker_addresses_.Add();
}
inline void DispatcherConfig::add_worker_addresses(const std::string& value) {
  _impl_.worker_addresses_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
}
inline void DispatcherConfig::add_worker_addresses(std::string&& value) {
  _impl_.worker_addresses_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
}
inline void DispatcherConfig::add_worker_addresses(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.worker_addresses_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
}
inline void DispatcherConfig::add_worker_addresses(const char* value, size_t size) {
  _impl_.worker_addresses_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DispatcherConfig::worker_addresses() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
  return _impl_.worker_addresses_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DispatcherConfig::mutable_worker_addresses() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.experimental.DispatcherConfig.worker_addresses)
  return &_impl_.worker_addresses_;
}

// .tensorflow.data.DeploymentMode deployment_mode = 9;
inline void DispatcherConfig::clear_deployment_mode() {
  _impl_.deployment_mode_ = 0;
}
inline ::tensorflow::data::DeploymentMode DispatcherConfig::_internal_deployment_mode() const {
  return static_cast< ::tensorflow::data::DeploymentMode >(_impl_.deployment_mode_);
}
inline ::tensorflow::data::DeploymentMode DispatcherConfig::deployment_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.deployment_mode)
  return _internal_deployment_mode();
}
inline void DispatcherConfig::_internal_set_deployment_mode(::tensorflow::data::DeploymentMode value) {
  
  _impl_.deployment_mode_ = value;
}
inline void DispatcherConfig::set_deployment_mode(::tensorflow::data::DeploymentMode value) {
  _internal_set_deployment_mode(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.deployment_mode)
}

// int64 job_gc_check_interval_ms = 5;
inline void DispatcherConfig::clear_job_gc_check_interval_ms() {
  _impl_.job_gc_check_interval_ms_ = int64_t{0};
}
inline int64_t DispatcherConfig::_internal_job_gc_check_interval_ms() const {
  return _impl_.job_gc_check_interval_ms_;
}
inline int64_t DispatcherConfig::job_gc_check_interval_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.job_gc_check_interval_ms)
  return _internal_job_gc_check_interval_ms();
}
inline void DispatcherConfig::_internal_set_job_gc_check_interval_ms(int64_t value) {
  
  _impl_.job_gc_check_interval_ms_ = value;
}
inline void DispatcherConfig::set_job_gc_check_interval_ms(int64_t value) {
  _internal_set_job_gc_check_interval_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.job_gc_check_interval_ms)
}

// int64 job_gc_timeout_ms = 6;
inline void DispatcherConfig::clear_job_gc_timeout_ms() {
  _impl_.job_gc_timeout_ms_ = int64_t{0};
}
inline int64_t DispatcherConfig::_internal_job_gc_timeout_ms() const {
  return _impl_.job_gc_timeout_ms_;
}
inline int64_t DispatcherConfig::job_gc_timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.job_gc_timeout_ms)
  return _internal_job_gc_timeout_ms();
}
inline void DispatcherConfig::_internal_set_job_gc_timeout_ms(int64_t value) {
  
  _impl_.job_gc_timeout_ms_ = value;
}
inline void DispatcherConfig::set_job_gc_timeout_ms(int64_t value) {
  _internal_set_job_gc_timeout_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.job_gc_timeout_ms)
}

// bool gc_dynamic_sharding_jobs = 11;
inline void DispatcherConfig::clear_gc_dynamic_sharding_jobs() {
  _impl_.gc_dynamic_sharding_jobs_ = false;
}
inline bool DispatcherConfig::_internal_gc_dynamic_sharding_jobs() const {
  return _impl_.gc_dynamic_sharding_jobs_;
}
inline bool DispatcherConfig::gc_dynamic_sharding_jobs() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.gc_dynamic_sharding_jobs)
  return _internal_gc_dynamic_sharding_jobs();
}
inline void DispatcherConfig::_internal_set_gc_dynamic_sharding_jobs(bool value) {
  
  _impl_.gc_dynamic_sharding_jobs_ = value;
}
inline void DispatcherConfig::set_gc_dynamic_sharding_jobs(bool value) {
  _internal_set_gc_dynamic_sharding_jobs(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.gc_dynamic_sharding_jobs)
}

// int64 client_timeout_ms = 8;
inline void DispatcherConfig::clear_client_timeout_ms() {
  _impl_.client_timeout_ms_ = int64_t{0};
}
inline int64_t DispatcherConfig::_internal_client_timeout_ms() const {
  return _impl_.client_timeout_ms_;
}
inline int64_t DispatcherConfig::client_timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.client_timeout_ms)
  return _internal_client_timeout_ms();
}
inline void DispatcherConfig::_internal_set_client_timeout_ms(int64_t value) {
  
  _impl_.client_timeout_ms_ = value;
}
inline void DispatcherConfig::set_client_timeout_ms(int64_t value) {
  _internal_set_client_timeout_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.client_timeout_ms)
}

// int64 worker_timeout_ms = 10;
inline void DispatcherConfig::clear_worker_timeout_ms() {
  _impl_.worker_timeout_ms_ = int64_t{0};
}
inline int64_t DispatcherConfig::_internal_worker_timeout_ms() const {
  return _impl_.worker_timeout_ms_;
}
inline int64_t DispatcherConfig::worker_timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.worker_timeout_ms)
  return _internal_worker_timeout_ms();
}
inline void DispatcherConfig::_internal_set_worker_timeout_ms(int64_t value) {
  
  _impl_.worker_timeout_ms_ = value;
}
inline void DispatcherConfig::set_worker_timeout_ms(int64_t value) {
  _internal_set_worker_timeout_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.worker_timeout_ms)
}

// int64 worker_max_concurrent_snapshots = 12;
inline void DispatcherConfig::clear_worker_max_concurrent_snapshots() {
  _impl_.worker_max_concurrent_snapshots_ = int64_t{0};
}
inline int64_t DispatcherConfig::_internal_worker_max_concurrent_snapshots() const {
  return _impl_.worker_max_concurrent_snapshots_;
}
inline int64_t DispatcherConfig::worker_max_concurrent_snapshots() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.DispatcherConfig.worker_max_concurrent_snapshots)
  return _internal_worker_max_concurrent_snapshots();
}
inline void DispatcherConfig::_internal_set_worker_max_concurrent_snapshots(int64_t value) {
  
  _impl_.worker_max_concurrent_snapshots_ = value;
}
inline void DispatcherConfig::set_worker_max_concurrent_snapshots(int64_t value) {
  _internal_set_worker_max_concurrent_snapshots(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.DispatcherConfig.worker_max_concurrent_snapshots)
}

// -------------------------------------------------------------------

// WorkerConfig

// int64 port = 1;
inline void WorkerConfig::clear_port() {
  _impl_.port_ = int64_t{0};
}
inline int64_t WorkerConfig::_internal_port() const {
  return _impl_.port_;
}
inline int64_t WorkerConfig::port() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.port)
  return _internal_port();
}
inline void WorkerConfig::_internal_set_port(int64_t value) {
  
  _impl_.port_ = value;
}
inline void WorkerConfig::set_port(int64_t value) {
  _internal_set_port(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.port)
}

// string protocol = 2;
inline void WorkerConfig::clear_protocol() {
  _impl_.protocol_.ClearToEmpty();
}
inline const std::string& WorkerConfig::protocol() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.protocol)
  return _internal_protocol();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WorkerConfig::set_protocol(ArgT0&& arg0, ArgT... args) {
 
 _impl_.protocol_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.protocol)
}
inline std::string* WorkerConfig::mutable_protocol() {
  std::string* _s = _internal_mutable_protocol();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.WorkerConfig.protocol)
  return _s;
}
inline const std::string& WorkerConfig::_internal_protocol() const {
  return _impl_.protocol_.Get();
}
inline void WorkerConfig::_internal_set_protocol(const std::string& value) {
  
  _impl_.protocol_.Set(value, GetArenaForAllocation());
}
inline std::string* WorkerConfig::_internal_mutable_protocol() {
  
  return _impl_.protocol_.Mutable(GetArenaForAllocation());
}
inline std::string* WorkerConfig::release_protocol() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.WorkerConfig.protocol)
  return _impl_.protocol_.Release();
}
inline void WorkerConfig::set_allocated_protocol(std::string* protocol) {
  if (protocol != nullptr) {
    
  } else {
    
  }
  _impl_.protocol_.SetAllocated(protocol, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.protocol_.IsDefault()) {
    _impl_.protocol_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.WorkerConfig.protocol)
}

// string dispatcher_address = 3;
inline void WorkerConfig::clear_dispatcher_address() {
  _impl_.dispatcher_address_.ClearToEmpty();
}
inline const std::string& WorkerConfig::dispatcher_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.dispatcher_address)
  return _internal_dispatcher_address();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WorkerConfig::set_dispatcher_address(ArgT0&& arg0, ArgT... args) {
 
 _impl_.dispatcher_address_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.dispatcher_address)
}
inline std::string* WorkerConfig::mutable_dispatcher_address() {
  std::string* _s = _internal_mutable_dispatcher_address();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.WorkerConfig.dispatcher_address)
  return _s;
}
inline const std::string& WorkerConfig::_internal_dispatcher_address() const {
  return _impl_.dispatcher_address_.Get();
}
inline void WorkerConfig::_internal_set_dispatcher_address(const std::string& value) {
  
  _impl_.dispatcher_address_.Set(value, GetArenaForAllocation());
}
inline std::string* WorkerConfig::_internal_mutable_dispatcher_address() {
  
  return _impl_.dispatcher_address_.Mutable(GetArenaForAllocation());
}
inline std::string* WorkerConfig::release_dispatcher_address() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.WorkerConfig.dispatcher_address)
  return _impl_.dispatcher_address_.Release();
}
inline void WorkerConfig::set_allocated_dispatcher_address(std::string* dispatcher_address) {
  if (dispatcher_address != nullptr) {
    
  } else {
    
  }
  _impl_.dispatcher_address_.SetAllocated(dispatcher_address, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dispatcher_address_.IsDefault()) {
    _impl_.dispatcher_address_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.WorkerConfig.dispatcher_address)
}

// string worker_address = 4;
inline void WorkerConfig::clear_worker_address() {
  _impl_.worker_address_.ClearToEmpty();
}
inline const std::string& WorkerConfig::worker_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.worker_address)
  return _internal_worker_address();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WorkerConfig::set_worker_address(ArgT0&& arg0, ArgT... args) {
 
 _impl_.worker_address_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.worker_address)
}
inline std::string* WorkerConfig::mutable_worker_address() {
  std::string* _s = _internal_mutable_worker_address();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.WorkerConfig.worker_address)
  return _s;
}
inline const std::string& WorkerConfig::_internal_worker_address() const {
  return _impl_.worker_address_.Get();
}
inline void WorkerConfig::_internal_set_worker_address(const std::string& value) {
  
  _impl_.worker_address_.Set(value, GetArenaForAllocation());
}
inline std::string* WorkerConfig::_internal_mutable_worker_address() {
  
  return _impl_.worker_address_.Mutable(GetArenaForAllocation());
}
inline std::string* WorkerConfig::release_worker_address() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.WorkerConfig.worker_address)
  return _impl_.worker_address_.Release();
}
inline void WorkerConfig::set_allocated_worker_address(std::string* worker_address) {
  if (worker_address != nullptr) {
    
  } else {
    
  }
  _impl_.worker_address_.SetAllocated(worker_address, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.worker_address_.IsDefault()) {
    _impl_.worker_address_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.WorkerConfig.worker_address)
}

// repeated string worker_tags = 10;
inline int WorkerConfig::_internal_worker_tags_size() const {
  return _impl_.worker_tags_.size();
}
inline int WorkerConfig::worker_tags_size() const {
  return _internal_worker_tags_size();
}
inline void WorkerConfig::clear_worker_tags() {
  _impl_.worker_tags_.Clear();
}
inline std::string* WorkerConfig::add_worker_tags() {
  std::string* _s = _internal_add_worker_tags();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.data.experimental.WorkerConfig.worker_tags)
  return _s;
}
inline const std::string& WorkerConfig::_internal_worker_tags(int index) const {
  return _impl_.worker_tags_.Get(index);
}
inline const std::string& WorkerConfig::worker_tags(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.worker_tags)
  return _internal_worker_tags(index);
}
inline std::string* WorkerConfig::mutable_worker_tags(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.WorkerConfig.worker_tags)
  return _impl_.worker_tags_.Mutable(index);
}
inline void WorkerConfig::set_worker_tags(int index, const std::string& value) {
  _impl_.worker_tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.worker_tags)
}
inline void WorkerConfig::set_worker_tags(int index, std::string&& value) {
  _impl_.worker_tags_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.worker_tags)
}
inline void WorkerConfig::set_worker_tags(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.worker_tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.data.experimental.WorkerConfig.worker_tags)
}
inline void WorkerConfig::set_worker_tags(int index, const char* value, size_t size) {
  _impl_.worker_tags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.data.experimental.WorkerConfig.worker_tags)
}
inline std::string* WorkerConfig::_internal_add_worker_tags() {
  return _impl_.worker_tags_.Add();
}
inline void WorkerConfig::add_worker_tags(const std::string& value) {
  _impl_.worker_tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.WorkerConfig.worker_tags)
}
inline void WorkerConfig::add_worker_tags(std::string&& value) {
  _impl_.worker_tags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.data.experimental.WorkerConfig.worker_tags)
}
inline void WorkerConfig::add_worker_tags(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.worker_tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.data.experimental.WorkerConfig.worker_tags)
}
inline void WorkerConfig::add_worker_tags(const char* value, size_t size) {
  _impl_.worker_tags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.data.experimental.WorkerConfig.worker_tags)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
WorkerConfig::worker_tags() const {
  // @@protoc_insertion_point(field_list:tensorflow.data.experimental.WorkerConfig.worker_tags)
  return _impl_.worker_tags_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
WorkerConfig::mutable_worker_tags() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.data.experimental.WorkerConfig.worker_tags)
  return &_impl_.worker_tags_;
}

// int64 heartbeat_interval_ms = 5;
inline void WorkerConfig::clear_heartbeat_interval_ms() {
  _impl_.heartbeat_interval_ms_ = int64_t{0};
}
inline int64_t WorkerConfig::_internal_heartbeat_interval_ms() const {
  return _impl_.heartbeat_interval_ms_;
}
inline int64_t WorkerConfig::heartbeat_interval_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.heartbeat_interval_ms)
  return _internal_heartbeat_interval_ms();
}
inline void WorkerConfig::_internal_set_heartbeat_interval_ms(int64_t value) {
  
  _impl_.heartbeat_interval_ms_ = value;
}
inline void WorkerConfig::set_heartbeat_interval_ms(int64_t value) {
  _internal_set_heartbeat_interval_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.heartbeat_interval_ms)
}

// int64 dispatcher_timeout_ms = 6;
inline void WorkerConfig::clear_dispatcher_timeout_ms() {
  _impl_.dispatcher_timeout_ms_ = int64_t{0};
}
inline int64_t WorkerConfig::_internal_dispatcher_timeout_ms() const {
  return _impl_.dispatcher_timeout_ms_;
}
inline int64_t WorkerConfig::dispatcher_timeout_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.dispatcher_timeout_ms)
  return _internal_dispatcher_timeout_ms();
}
inline void WorkerConfig::_internal_set_dispatcher_timeout_ms(int64_t value) {
  
  _impl_.dispatcher_timeout_ms_ = value;
}
inline void WorkerConfig::set_dispatcher_timeout_ms(int64_t value) {
  _internal_set_dispatcher_timeout_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.dispatcher_timeout_ms)
}

// string data_transfer_protocol = 7;
inline void WorkerConfig::clear_data_transfer_protocol() {
  _impl_.data_transfer_protocol_.ClearToEmpty();
}
inline const std::string& WorkerConfig::data_transfer_protocol() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.data_transfer_protocol)
  return _internal_data_transfer_protocol();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WorkerConfig::set_data_transfer_protocol(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_transfer_protocol_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.data_transfer_protocol)
}
inline std::string* WorkerConfig::mutable_data_transfer_protocol() {
  std::string* _s = _internal_mutable_data_transfer_protocol();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.WorkerConfig.data_transfer_protocol)
  return _s;
}
inline const std::string& WorkerConfig::_internal_data_transfer_protocol() const {
  return _impl_.data_transfer_protocol_.Get();
}
inline void WorkerConfig::_internal_set_data_transfer_protocol(const std::string& value) {
  
  _impl_.data_transfer_protocol_.Set(value, GetArenaForAllocation());
}
inline std::string* WorkerConfig::_internal_mutable_data_transfer_protocol() {
  
  return _impl_.data_transfer_protocol_.Mutable(GetArenaForAllocation());
}
inline std::string* WorkerConfig::release_data_transfer_protocol() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.WorkerConfig.data_transfer_protocol)
  return _impl_.data_transfer_protocol_.Release();
}
inline void WorkerConfig::set_allocated_data_transfer_protocol(std::string* data_transfer_protocol) {
  if (data_transfer_protocol != nullptr) {
    
  } else {
    
  }
  _impl_.data_transfer_protocol_.SetAllocated(data_transfer_protocol, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_transfer_protocol_.IsDefault()) {
    _impl_.data_transfer_protocol_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.WorkerConfig.data_transfer_protocol)
}

// int64 data_transfer_port = 13;
inline void WorkerConfig::clear_data_transfer_port() {
  _impl_.data_transfer_port_ = int64_t{0};
}
inline int64_t WorkerConfig::_internal_data_transfer_port() const {
  return _impl_.data_transfer_port_;
}
inline int64_t WorkerConfig::data_transfer_port() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.data_transfer_port)
  return _internal_data_transfer_port();
}
inline void WorkerConfig::_internal_set_data_transfer_port(int64_t value) {
  
  _impl_.data_transfer_port_ = value;
}
inline void WorkerConfig::set_data_transfer_port(int64_t value) {
  _internal_set_data_transfer_port(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.data_transfer_port)
}

// string data_transfer_address = 8;
inline void WorkerConfig::clear_data_transfer_address() {
  _impl_.data_transfer_address_.ClearToEmpty();
}
inline const std::string& WorkerConfig::data_transfer_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.data_transfer_address)
  return _internal_data_transfer_address();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void WorkerConfig::set_data_transfer_address(ArgT0&& arg0, ArgT... args) {
 
 _impl_.data_transfer_address_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.data_transfer_address)
}
inline std::string* WorkerConfig::mutable_data_transfer_address() {
  std::string* _s = _internal_mutable_data_transfer_address();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.experimental.WorkerConfig.data_transfer_address)
  return _s;
}
inline const std::string& WorkerConfig::_internal_data_transfer_address() const {
  return _impl_.data_transfer_address_.Get();
}
inline void WorkerConfig::_internal_set_data_transfer_address(const std::string& value) {
  
  _impl_.data_transfer_address_.Set(value, GetArenaForAllocation());
}
inline std::string* WorkerConfig::_internal_mutable_data_transfer_address() {
  
  return _impl_.data_transfer_address_.Mutable(GetArenaForAllocation());
}
inline std::string* WorkerConfig::release_data_transfer_address() {
  // @@protoc_insertion_point(field_release:tensorflow.data.experimental.WorkerConfig.data_transfer_address)
  return _impl_.data_transfer_address_.Release();
}
inline void WorkerConfig::set_allocated_data_transfer_address(std::string* data_transfer_address) {
  if (data_transfer_address != nullptr) {
    
  } else {
    
  }
  _impl_.data_transfer_address_.SetAllocated(data_transfer_address, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.data_transfer_address_.IsDefault()) {
    _impl_.data_transfer_address_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.experimental.WorkerConfig.data_transfer_address)
}

// int64 cross_trainer_cache_size_bytes = 11;
inline void WorkerConfig::clear_cross_trainer_cache_size_bytes() {
  _impl_.cross_trainer_cache_size_bytes_ = int64_t{0};
}
inline int64_t WorkerConfig::_internal_cross_trainer_cache_size_bytes() const {
  return _impl_.cross_trainer_cache_size_bytes_;
}
inline int64_t WorkerConfig::cross_trainer_cache_size_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.cross_trainer_cache_size_bytes)
  return _internal_cross_trainer_cache_size_bytes();
}
inline void WorkerConfig::_internal_set_cross_trainer_cache_size_bytes(int64_t value) {
  
  _impl_.cross_trainer_cache_size_bytes_ = value;
}
inline void WorkerConfig::set_cross_trainer_cache_size_bytes(int64_t value) {
  _internal_set_cross_trainer_cache_size_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.cross_trainer_cache_size_bytes)
}

// int64 snapshot_max_chunk_size_bytes = 12;
inline void WorkerConfig::clear_snapshot_max_chunk_size_bytes() {
  _impl_.snapshot_max_chunk_size_bytes_ = int64_t{0};
}
inline int64_t WorkerConfig::_internal_snapshot_max_chunk_size_bytes() const {
  return _impl_.snapshot_max_chunk_size_bytes_;
}
inline int64_t WorkerConfig::snapshot_max_chunk_size_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.snapshot_max_chunk_size_bytes)
  return _internal_snapshot_max_chunk_size_bytes();
}
inline void WorkerConfig::_internal_set_snapshot_max_chunk_size_bytes(int64_t value) {
  
  _impl_.snapshot_max_chunk_size_bytes_ = value;
}
inline void WorkerConfig::set_snapshot_max_chunk_size_bytes(int64_t value) {
  _internal_set_snapshot_max_chunk_size_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.snapshot_max_chunk_size_bytes)
}

// int64 shutdown_quiet_period_ms = 9;
inline void WorkerConfig::clear_shutdown_quiet_period_ms() {
  _impl_.shutdown_quiet_period_ms_ = int64_t{0};
}
inline int64_t WorkerConfig::_internal_shutdown_quiet_period_ms() const {
  return _impl_.shutdown_quiet_period_ms_;
}
inline int64_t WorkerConfig::shutdown_quiet_period_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.experimental.WorkerConfig.shutdown_quiet_period_ms)
  return _internal_shutdown_quiet_period_ms();
}
inline void WorkerConfig::_internal_set_shutdown_quiet_period_ms(int64_t value) {
  
  _impl_.shutdown_quiet_period_ms_ = value;
}
inline void WorkerConfig::set_shutdown_quiet_period_ms(int64_t value) {
  _internal_set_shutdown_quiet_period_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.experimental.WorkerConfig.shutdown_quiet_period_ms)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace experimental
}  // namespace data
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fservice_5fconfig_2eproto
