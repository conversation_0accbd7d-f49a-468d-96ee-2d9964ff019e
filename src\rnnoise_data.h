/* 简化的rnnoise_data.h文件，用于编译dump_features */
#ifndef RNNOISE_DATA_H
#define RNNOISE_DATA_H

/* 简化的数据结构定义 */
typedef struct {
    int nb_inputs;
    int nb_neurons;
    int activation;
    const float *input_weights;
    const float *recurrent_weights;
    const float *bias;
} DenseLayer;

typedef struct {
    int nb_inputs;
    int nb_neurons;
    int activation;
    const float *input_weights;
    const float *recurrent_weights;
    const float *bias;
} GRULayer;

/* 简化的网络结构 */
extern const DenseLayer input_dense;
extern const GRULayer vad_gru;
extern const DenseLayer vad_output;
extern const GRULayer noise_gru;
extern const GRULayer denoise_gru;
extern const DenseLayer denoise_output;

/* 简化的初始化函数 */
void rnn_init(void *model);

#endif /* RNNOISE_DATA_H */
