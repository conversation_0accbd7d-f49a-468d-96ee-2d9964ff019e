/*
 * Copyright 2024 <PERSON><PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";

namespace Windows.UI.Xaml.Interop {
    typedef enum TypeKind TypeKind;
    typedef struct TypeName TypeName;

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum TypeKind
    {
        Primitive = 0,
        Metadata  = 1,
        Custom    = 2
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    struct TypeName
    {
        HSTRING Name;
        Windows.UI.Xaml.Interop.TypeKind Kind;
    };
}
