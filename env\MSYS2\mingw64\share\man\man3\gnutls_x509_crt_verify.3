.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_verify" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_verify \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crt_verify(gnutls_x509_crt_t " cert ", const gnutls_x509_crt_t * " CA_list ", unsigned " CA_list_length ", unsigned int " flags ", unsigned int * " verify ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
is the certificate to be verified
.IP "const gnutls_x509_crt_t * CA_list" 12
is one certificate that is considered to be trusted one
.IP "unsigned CA_list_length" 12
holds the number of CA certificate in CA_list
.IP "unsigned int flags" 12
Flags that may be used to change the verification algorithm. Use OR of the gnutls_certificate_verify_flags enumerations.
.IP "unsigned int * verify" 12
will hold the certificate verification output.
.SH "DESCRIPTION"
This function will try to verify the given certificate and return
its status. Note that a verification error does not imply a negative
return status. In that case the  \fIverify\fP status is set.

The details of the verification are the same
as in \fBgnutls_x509_trust_list_verify_crt2()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
