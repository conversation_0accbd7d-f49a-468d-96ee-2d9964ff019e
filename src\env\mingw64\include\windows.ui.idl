/*
 * Copyright 2022 Mohamad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";

namespace Windows.UI {
    typedef struct Color Color;
    typedef struct WindowId WindowId;

    interface IColorHelper;
    interface IColorHelperStatics;
    interface IColorHelperStatics2;
    interface IColors;
    interface IColorsStatics;
    interface IUIContentRoot;
    interface IUIContext;

    runtimeclass ColorHelper;
    runtimeclass Colors;
    runtimeclass UIContentRoot;
    runtimeclass UIContext;

    declare {
        interface Windows.Foundation.Collections.IIterable<Windows.UI.Color>;
        interface Windows.Foundation.Collections.IIterable<Windows.UI.WindowId>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.Color>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.WindowId>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.WindowId>;
        interface Windows.Foundation.IReference<Windows.UI.Color>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    struct Color
    {
        BYTE A;
        BYTE R;
        BYTE G;
        BYTE B;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 12.0)
    ]
    struct WindowId
    {
        UINT64 Value;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ColorHelper),
        uuid(193cfbe7-65c7-4540-ad08-6283ba76879a)
    ]
    interface IColorHelper : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.ColorHelper),
        uuid(8504dbea-fb6a-4144-a6c2-33499c9284f5)
    ]
    interface IColorHelperStatics : IInspectable
    {
        HRESULT FromArgb([in] BYTE a, [in] BYTE r, [in] BYTE g, [in] BYTE b, [out, retval] Windows.UI.Color *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.ColorHelper),
        uuid(24d9af02-6eb0-4b94-855c-fcf0818d9a16)
    ]
    interface IColorHelperStatics2 : IInspectable
    {
        HRESULT ToDisplayName([in] Windows.UI.Color color, [out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Colors),
        uuid(9b8c9326-4ca6-4ce5-8994-9eff65cabdcc)
    ]
    interface IColors : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Colors),
        uuid(cff52e04-cca6-4614-a17e-754910c84a99)
    ]
    interface IColorsStatics : IInspectable
    {
        [propget] HRESULT AliceBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT AntiqueWhite([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Aqua([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Aquamarine([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Azure([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Beige([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Bisque([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Black([out, retval] Windows.UI.Color *value);
        [propget] HRESULT BlanchedAlmond([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Blue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT BlueViolet([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Brown([out, retval] Windows.UI.Color *value);
        [propget] HRESULT BurlyWood([out, retval] Windows.UI.Color *value);
        [propget] HRESULT CadetBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Chartreuse([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Chocolate([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Coral([out, retval] Windows.UI.Color *value);
        [propget] HRESULT CornflowerBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Cornsilk([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Crimson([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Cyan([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkCyan([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkGoldenrod([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkGray([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkKhaki([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkMagenta([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkOliveGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkOrange([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkOrchid([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkRed([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkSalmon([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkSeaGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkSlateBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkSlateGray([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkTurquoise([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DarkViolet([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DeepPink([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DeepSkyBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DimGray([out, retval] Windows.UI.Color *value);
        [propget] HRESULT DodgerBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Firebrick([out, retval] Windows.UI.Color *value);
        [propget] HRESULT FloralWhite([out, retval] Windows.UI.Color *value);
        [propget] HRESULT ForestGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Fuchsia([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Gainsboro([out, retval] Windows.UI.Color *value);
        [propget] HRESULT GhostWhite([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Gold([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Goldenrod([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Gray([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Green([out, retval] Windows.UI.Color *value);
        [propget] HRESULT GreenYellow([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Honeydew([out, retval] Windows.UI.Color *value);
        [propget] HRESULT HotPink([out, retval] Windows.UI.Color *value);
        [propget] HRESULT IndianRed([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Indigo([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Ivory([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Khaki([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Lavender([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LavenderBlush([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LawnGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LemonChiffon([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightCoral([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightCyan([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightGoldenrodYellow([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightGray([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightPink([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightSalmon([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightSeaGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightSkyBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightSlateGray([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightSteelBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LightYellow([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Lime([out, retval] Windows.UI.Color *value);
        [propget] HRESULT LimeGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Linen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Magenta([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Maroon([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumAquamarine([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumOrchid([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumPurple([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumSeaGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumSlateBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumSpringGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumTurquoise([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MediumVioletRed([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MidnightBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MintCream([out, retval] Windows.UI.Color *value);
        [propget] HRESULT MistyRose([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Moccasin([out, retval] Windows.UI.Color *value);
        [propget] HRESULT NavajoWhite([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Navy([out, retval] Windows.UI.Color *value);
        [propget] HRESULT OldLace([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Olive([out, retval] Windows.UI.Color *value);
        [propget] HRESULT OliveDrab([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Orange([out, retval] Windows.UI.Color *value);
        [propget] HRESULT OrangeRed([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Orchid([out, retval] Windows.UI.Color *value);
        [propget] HRESULT PaleGoldenrod([out, retval] Windows.UI.Color *value);
        [propget] HRESULT PaleGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT PaleTurquoise([out, retval] Windows.UI.Color *value);
        [propget] HRESULT PaleVioletRed([out, retval] Windows.UI.Color *value);
        [propget] HRESULT PapayaWhip([out, retval] Windows.UI.Color *value);
        [propget] HRESULT PeachPuff([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Peru([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Pink([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Plum([out, retval] Windows.UI.Color *value);
        [propget] HRESULT PowderBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Purple([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Red([out, retval] Windows.UI.Color *value);
        [propget] HRESULT RosyBrown([out, retval] Windows.UI.Color *value);
        [propget] HRESULT RoyalBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SaddleBrown([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Salmon([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SandyBrown([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SeaGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SeaShell([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Sienna([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Silver([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SkyBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SlateBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SlateGray([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Snow([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SpringGreen([out, retval] Windows.UI.Color *value);
        [propget] HRESULT SteelBlue([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Tan([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Teal([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Thistle([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Tomato([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Transparent([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Turquoise([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Violet([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Wheat([out, retval] Windows.UI.Color *value);
        [propget] HRESULT White([out, retval] Windows.UI.Color *value);
        [propget] HRESULT WhiteSmoke([out, retval] Windows.UI.Color *value);
        [propget] HRESULT Yellow([out, retval] Windows.UI.Color *value);
        [propget] HRESULT YellowGreen([out, retval] Windows.UI.Color *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        exclusiveto(Windows.UI.UIContentRoot),
        uuid(1dfcbac6-b36b-5cb9-9bc5-2b7a0eddc378)
    ]
    interface IUIContentRoot : IInspectable
    {
        [propget] HRESULT UIContext([out, retval] Windows.UI.UIContext **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        exclusiveto(Windows.UI.UIContext),
        uuid(bb5cfacd-5bd8-59d0-a59e-1c17a4d6d243)
    ]
    interface IUIContext : IInspectable
    {
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.UI.IColorHelperStatics, Windows.Foundation.UniversalApiContract, 1.0),
        static(Windows.UI.IColorHelperStatics2, Windows.Foundation.UniversalApiContract, 4.0),
        threading(both)
    ]
    runtimeclass ColorHelper
    {
        [default] interface Windows.UI.IColorHelper;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile),
        static(Windows.UI.IColorsStatics, Windows.Foundation.UniversalApiContract, 1.0),
        threading(both)
    ]
    runtimeclass Colors
    {
        [default] interface Windows.UI.IColors;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass UIContentRoot
    {
        [default] interface Windows.UI.IUIContentRoot;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        marshaling_behavior(agile),
        threading(both)
    ]
    runtimeclass UIContext
    {
        [default] interface Windows.UI.IUIContext;
    }
}
