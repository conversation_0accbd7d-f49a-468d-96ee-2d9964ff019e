/*** Autogenerated by WIDL 10.8 from include/adhoc.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __adhoc_h__
#define __adhoc_h__

/* Forward declarations */

#ifndef __IDot11AdHocManager_FWD_DEFINED__
#define __IDot11AdHocManager_FWD_DEFINED__
typedef interface IDot11AdHocManager IDot11AdHocManager;
#ifdef __cplusplus
interface IDot11AdHocManager;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocManagerNotificationSink_FWD_DEFINED__
#define __IDot11AdHocManagerNotificationSink_FWD_DEFINED__
typedef interface IDot11AdHocManagerNotificationSink IDot11AdHocManagerNotificationSink;
#ifdef __cplusplus
interface IDot11AdHocManagerNotificationSink;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDot11AdHocNetworks_FWD_DEFINED__
#define __IEnumDot11AdHocNetworks_FWD_DEFINED__
typedef interface IEnumDot11AdHocNetworks IEnumDot11AdHocNetworks;
#ifdef __cplusplus
interface IEnumDot11AdHocNetworks;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocNetwork_FWD_DEFINED__
#define __IDot11AdHocNetwork_FWD_DEFINED__
typedef interface IDot11AdHocNetwork IDot11AdHocNetwork;
#ifdef __cplusplus
interface IDot11AdHocNetwork;
#endif /* __cplusplus */
#endif

#ifndef __IDot1*****************************_FWD_DEFINED__
#define __IDot1*****************************_FWD_DEFINED__
typedef interface IDot1***************************** IDot1*****************************;
#ifdef __cplusplus
interface IDot1*****************************;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocInterface_FWD_DEFINED__
#define __IDot11AdHocInterface_FWD_DEFINED__
typedef interface IDot11AdHocInterface IDot11AdHocInterface;
#ifdef __cplusplus
interface IDot11AdHocInterface;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDot11AdHocInterfaces_FWD_DEFINED__
#define __IEnumDot11AdHocInterfaces_FWD_DEFINED__
typedef interface IEnumDot11AdHocInterfaces IEnumDot11AdHocInterfaces;
#ifdef __cplusplus
interface IEnumDot11AdHocInterfaces;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDot11AdHocSecuritySettings_FWD_DEFINED__
#define __IEnumDot11AdHocSecuritySettings_FWD_DEFINED__
typedef interface IEnumDot11AdHocSecuritySettings IEnumDot11AdHocSecuritySettings;
#ifdef __cplusplus
interface IEnumDot11AdHocSecuritySettings;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocSecuritySettings_FWD_DEFINED__
#define __IDot11AdHocSecuritySettings_FWD_DEFINED__
typedef interface IDot11AdHocSecuritySettings IDot11AdHocSecuritySettings;
#ifdef __cplusplus
interface IDot11AdHocSecuritySettings;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocInterfaceNotificationSink_FWD_DEFINED__
#define __IDot11AdHocInterfaceNotificationSink_FWD_DEFINED__
typedef interface IDot11AdHocInterfaceNotificationSink IDot11AdHocInterfaceNotificationSink;
#ifdef __cplusplus
interface IDot11AdHocInterfaceNotificationSink;
#endif /* __cplusplus */
#endif

#ifndef __Dot11AdHocManager_FWD_DEFINED__
#define __Dot11AdHocManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class Dot11AdHocManager Dot11AdHocManager;
#else
typedef struct Dot11AdHocManager Dot11AdHocManager;
#endif /* defined __cplusplus */
#endif /* defined __Dot11AdHocManager_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <unknwn.h>
#include <wtypes.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IDot11AdHocManager_FWD_DEFINED__
#define __IDot11AdHocManager_FWD_DEFINED__
typedef interface IDot11AdHocManager IDot11AdHocManager;
#ifdef __cplusplus
interface IDot11AdHocManager;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocManagerNotificationSink_FWD_DEFINED__
#define __IDot11AdHocManagerNotificationSink_FWD_DEFINED__
typedef interface IDot11AdHocManagerNotificationSink IDot11AdHocManagerNotificationSink;
#ifdef __cplusplus
interface IDot11AdHocManagerNotificationSink;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocNetwork_FWD_DEFINED__
#define __IDot11AdHocNetwork_FWD_DEFINED__
typedef interface IDot11AdHocNetwork IDot11AdHocNetwork;
#ifdef __cplusplus
interface IDot11AdHocNetwork;
#endif /* __cplusplus */
#endif

#ifndef __IDot1*****************************_FWD_DEFINED__
#define __IDot1*****************************_FWD_DEFINED__
typedef interface IDot1***************************** IDot1*****************************;
#ifdef __cplusplus
interface IDot1*****************************;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocInterface_FWD_DEFINED__
#define __IDot11AdHocInterface_FWD_DEFINED__
typedef interface IDot11AdHocInterface IDot11AdHocInterface;
#ifdef __cplusplus
interface IDot11AdHocInterface;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocInterfaceNotificationSink_FWD_DEFINED__
#define __IDot11AdHocInterfaceNotificationSink_FWD_DEFINED__
typedef interface IDot11AdHocInterfaceNotificationSink IDot11AdHocInterfaceNotificationSink;
#ifdef __cplusplus
interface IDot11AdHocInterfaceNotificationSink;
#endif /* __cplusplus */
#endif

#ifndef __IDot11AdHocSecuritySettings_FWD_DEFINED__
#define __IDot11AdHocSecuritySettings_FWD_DEFINED__
typedef interface IDot11AdHocSecuritySettings IDot11AdHocSecuritySettings;
#ifdef __cplusplus
interface IDot11AdHocSecuritySettings;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDot11AdHocInterfaces_FWD_DEFINED__
#define __IEnumDot11AdHocInterfaces_FWD_DEFINED__
typedef interface IEnumDot11AdHocInterfaces IEnumDot11AdHocInterfaces;
#ifdef __cplusplus
interface IEnumDot11AdHocInterfaces;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDot11AdHocNetworks_FWD_DEFINED__
#define __IEnumDot11AdHocNetworks_FWD_DEFINED__
typedef interface IEnumDot11AdHocNetworks IEnumDot11AdHocNetworks;
#ifdef __cplusplus
interface IEnumDot11AdHocNetworks;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDot11AdHocSecuritySettings_FWD_DEFINED__
#define __IEnumDot11AdHocSecuritySettings_FWD_DEFINED__
typedef interface IEnumDot11AdHocSecuritySettings IEnumDot11AdHocSecuritySettings;
#ifdef __cplusplus
interface IEnumDot11AdHocSecuritySettings;
#endif /* __cplusplus */
#endif

typedef enum tagDOT11_ADHOC_CIPHER_ALGORITHM {
    DOT11_ADHOC_CIPHER_ALGO_INVALID = -1,
    DOT11_ADHOC_CIPHER_ALGO_NONE = 0x0,
    DOT11_ADHOC_CIPHER_ALGO_CCMP = 0x4,
    DOT11_ADHOC_CIPHER_ALGO_WEP = 0x101
} DOT11_ADHOC_CIPHER_ALGORITHM;
typedef enum tagDOT11_ADHOC_AUTH_ALGORITHM {
    DOT11_ADHOC_AUTH_ALGO_INVALID = -1,
    DOT11_ADHOC_AUTH_ALGO_80211_OPEN = 1,
    DOT11_ADHOC_AUTH_ALGO_RSNA_PSK = 7
} DOT11_ADHOC_AUTH_ALGORITHM;
typedef enum tagDOT11_ADHOC_NETWORK_CONNECTION_STATUS {
    DOT11_ADHOC_NETWORK_CONNECTION_STATUS_INVALID = 0,
    DOT11_ADHOC_NETWORK_CONNECTION_STATUS_DISCONNECTED = 11,
    DOT11_ADHOC_NETWORK_CONNECTION_STATUS_CONNECTING = 12,
    DOT11_ADHOC_NETWORK_CONNECTION_STATUS_CONNECTED = 13,
    DOT11_ADHOC_NETWORK_CONNECTION_STATUS_FORMED = 14
} DOT11_ADHOC_NETWORK_CONNECTION_STATUS;
typedef enum tagDOT11_ADHOC_CONNECT_FAIL_REASON {
    DOT11_ADHOC_CONNECT_FAIL_DOMAIN_MISMATCH = 0,
    DOT11_ADHOC_CONNECT_FAIL_PASSPHRASE_MISMATCH = 1,
    DOT11_ADHOC_CONNECT_FAIL_OTHER = 2
} DOT11_ADHOC_CONNECT_FAIL_REASON;
EXTERN_C const CLSID CLSID_AdHocManager;
/*****************************************************************************
 * IDot11AdHocManager interface
 */
#ifndef __IDot11AdHocManager_INTERFACE_DEFINED__
#define __IDot11AdHocManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDot11AdHocManager, 0x8f10cc26, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc26-cf0d-42a0-acbe-e2de7007384d")
IDot11AdHocManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateNetwork(
        LPCWSTR Name,
        LPCWSTR Password,
        LONG GeographicalId,
        IDot11AdHocInterface *pInterface,
        IDot11AdHocSecuritySettings *pSecurity,
        GUID *pContextGuid,
        IDot11AdHocNetwork **pIAdHoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommitCreatedNetwork(
        IDot11AdHocNetwork *pIAdHoc,
        BOOLEAN fSaveProfile,
        BOOLEAN fMakeSavedProfileUserSpecific) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIEnumDot11AdHocNetworks(
        GUID *pContextGuid,
        IEnumDot11AdHocNetworks **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIEnumDot11AdHocInterfaces(
        IEnumDot11AdHocInterfaces **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNetwork(
        GUID *NetworkSignature,
        IDot11AdHocNetwork **pNetwork) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDot11AdHocManager, 0x8f10cc26, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IDot11AdHocManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDot11AdHocManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDot11AdHocManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDot11AdHocManager *This);

    /*** IDot11AdHocManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateNetwork)(
        IDot11AdHocManager *This,
        LPCWSTR Name,
        LPCWSTR Password,
        LONG GeographicalId,
        IDot11AdHocInterface *pInterface,
        IDot11AdHocSecuritySettings *pSecurity,
        GUID *pContextGuid,
        IDot11AdHocNetwork **pIAdHoc);

    HRESULT (STDMETHODCALLTYPE *CommitCreatedNetwork)(
        IDot11AdHocManager *This,
        IDot11AdHocNetwork *pIAdHoc,
        BOOLEAN fSaveProfile,
        BOOLEAN fMakeSavedProfileUserSpecific);

    HRESULT (STDMETHODCALLTYPE *GetIEnumDot11AdHocNetworks)(
        IDot11AdHocManager *This,
        GUID *pContextGuid,
        IEnumDot11AdHocNetworks **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetIEnumDot11AdHocInterfaces)(
        IDot11AdHocManager *This,
        IEnumDot11AdHocInterfaces **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetNetwork)(
        IDot11AdHocManager *This,
        GUID *NetworkSignature,
        IDot11AdHocNetwork **pNetwork);

    END_INTERFACE
} IDot11AdHocManagerVtbl;

interface IDot11AdHocManager {
    CONST_VTBL IDot11AdHocManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDot11AdHocManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDot11AdHocManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDot11AdHocManager_Release(This) (This)->lpVtbl->Release(This)
/*** IDot11AdHocManager methods ***/
#define IDot11AdHocManager_CreateNetwork(This,Name,Password,GeographicalId,pInterface,pSecurity,pContextGuid,pIAdHoc) (This)->lpVtbl->CreateNetwork(This,Name,Password,GeographicalId,pInterface,pSecurity,pContextGuid,pIAdHoc)
#define IDot11AdHocManager_CommitCreatedNetwork(This,pIAdHoc,fSaveProfile,fMakeSavedProfileUserSpecific) (This)->lpVtbl->CommitCreatedNetwork(This,pIAdHoc,fSaveProfile,fMakeSavedProfileUserSpecific)
#define IDot11AdHocManager_GetIEnumDot11AdHocNetworks(This,pContextGuid,ppEnum) (This)->lpVtbl->GetIEnumDot11AdHocNetworks(This,pContextGuid,ppEnum)
#define IDot11AdHocManager_GetIEnumDot11AdHocInterfaces(This,ppEnum) (This)->lpVtbl->GetIEnumDot11AdHocInterfaces(This,ppEnum)
#define IDot11AdHocManager_GetNetwork(This,NetworkSignature,pNetwork) (This)->lpVtbl->GetNetwork(This,NetworkSignature,pNetwork)
#else
/*** IUnknown methods ***/
static inline HRESULT IDot11AdHocManager_QueryInterface(IDot11AdHocManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDot11AdHocManager_AddRef(IDot11AdHocManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDot11AdHocManager_Release(IDot11AdHocManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IDot11AdHocManager methods ***/
static inline HRESULT IDot11AdHocManager_CreateNetwork(IDot11AdHocManager* This,LPCWSTR Name,LPCWSTR Password,LONG GeographicalId,IDot11AdHocInterface *pInterface,IDot11AdHocSecuritySettings *pSecurity,GUID *pContextGuid,IDot11AdHocNetwork **pIAdHoc) {
    return This->lpVtbl->CreateNetwork(This,Name,Password,GeographicalId,pInterface,pSecurity,pContextGuid,pIAdHoc);
}
static inline HRESULT IDot11AdHocManager_CommitCreatedNetwork(IDot11AdHocManager* This,IDot11AdHocNetwork *pIAdHoc,BOOLEAN fSaveProfile,BOOLEAN fMakeSavedProfileUserSpecific) {
    return This->lpVtbl->CommitCreatedNetwork(This,pIAdHoc,fSaveProfile,fMakeSavedProfileUserSpecific);
}
static inline HRESULT IDot11AdHocManager_GetIEnumDot11AdHocNetworks(IDot11AdHocManager* This,GUID *pContextGuid,IEnumDot11AdHocNetworks **ppEnum) {
    return This->lpVtbl->GetIEnumDot11AdHocNetworks(This,pContextGuid,ppEnum);
}
static inline HRESULT IDot11AdHocManager_GetIEnumDot11AdHocInterfaces(IDot11AdHocManager* This,IEnumDot11AdHocInterfaces **ppEnum) {
    return This->lpVtbl->GetIEnumDot11AdHocInterfaces(This,ppEnum);
}
static inline HRESULT IDot11AdHocManager_GetNetwork(IDot11AdHocManager* This,GUID *NetworkSignature,IDot11AdHocNetwork **pNetwork) {
    return This->lpVtbl->GetNetwork(This,NetworkSignature,pNetwork);
}
#endif
#endif

#endif


#endif  /* __IDot11AdHocManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDot11AdHocManagerNotificationSink interface
 */
#ifndef __IDot11AdHocManagerNotificationSink_INTERFACE_DEFINED__
#define __IDot11AdHocManagerNotificationSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDot11AdHocManagerNotificationSink, 0x8f10cc27, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc27-cf0d-42a0-acbe-e2de7007384d")
IDot11AdHocManagerNotificationSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnNetworkAdd(
        IDot11AdHocNetwork *pIAdHocNetwork) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnNetworkRemove(
        GUID *Signature) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnInterfaceAdd(
        IDot11AdHocInterface *pIAdHocInterface) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnInterfaceRemove(
        GUID *Signature) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDot11AdHocManagerNotificationSink, 0x8f10cc27, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IDot11AdHocManagerNotificationSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDot11AdHocManagerNotificationSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDot11AdHocManagerNotificationSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDot11AdHocManagerNotificationSink *This);

    /*** IDot11AdHocManagerNotificationSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnNetworkAdd)(
        IDot11AdHocManagerNotificationSink *This,
        IDot11AdHocNetwork *pIAdHocNetwork);

    HRESULT (STDMETHODCALLTYPE *OnNetworkRemove)(
        IDot11AdHocManagerNotificationSink *This,
        GUID *Signature);

    HRESULT (STDMETHODCALLTYPE *OnInterfaceAdd)(
        IDot11AdHocManagerNotificationSink *This,
        IDot11AdHocInterface *pIAdHocInterface);

    HRESULT (STDMETHODCALLTYPE *OnInterfaceRemove)(
        IDot11AdHocManagerNotificationSink *This,
        GUID *Signature);

    END_INTERFACE
} IDot11AdHocManagerNotificationSinkVtbl;

interface IDot11AdHocManagerNotificationSink {
    CONST_VTBL IDot11AdHocManagerNotificationSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDot11AdHocManagerNotificationSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDot11AdHocManagerNotificationSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDot11AdHocManagerNotificationSink_Release(This) (This)->lpVtbl->Release(This)
/*** IDot11AdHocManagerNotificationSink methods ***/
#define IDot11AdHocManagerNotificationSink_OnNetworkAdd(This,pIAdHocNetwork) (This)->lpVtbl->OnNetworkAdd(This,pIAdHocNetwork)
#define IDot11AdHocManagerNotificationSink_OnNetworkRemove(This,Signature) (This)->lpVtbl->OnNetworkRemove(This,Signature)
#define IDot11AdHocManagerNotificationSink_OnInterfaceAdd(This,pIAdHocInterface) (This)->lpVtbl->OnInterfaceAdd(This,pIAdHocInterface)
#define IDot11AdHocManagerNotificationSink_OnInterfaceRemove(This,Signature) (This)->lpVtbl->OnInterfaceRemove(This,Signature)
#else
/*** IUnknown methods ***/
static inline HRESULT IDot11AdHocManagerNotificationSink_QueryInterface(IDot11AdHocManagerNotificationSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDot11AdHocManagerNotificationSink_AddRef(IDot11AdHocManagerNotificationSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDot11AdHocManagerNotificationSink_Release(IDot11AdHocManagerNotificationSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IDot11AdHocManagerNotificationSink methods ***/
static inline HRESULT IDot11AdHocManagerNotificationSink_OnNetworkAdd(IDot11AdHocManagerNotificationSink* This,IDot11AdHocNetwork *pIAdHocNetwork) {
    return This->lpVtbl->OnNetworkAdd(This,pIAdHocNetwork);
}
static inline HRESULT IDot11AdHocManagerNotificationSink_OnNetworkRemove(IDot11AdHocManagerNotificationSink* This,GUID *Signature) {
    return This->lpVtbl->OnNetworkRemove(This,Signature);
}
static inline HRESULT IDot11AdHocManagerNotificationSink_OnInterfaceAdd(IDot11AdHocManagerNotificationSink* This,IDot11AdHocInterface *pIAdHocInterface) {
    return This->lpVtbl->OnInterfaceAdd(This,pIAdHocInterface);
}
static inline HRESULT IDot11AdHocManagerNotificationSink_OnInterfaceRemove(IDot11AdHocManagerNotificationSink* This,GUID *Signature) {
    return This->lpVtbl->OnInterfaceRemove(This,Signature);
}
#endif
#endif

#endif


#endif  /* __IDot11AdHocManagerNotificationSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumDot11AdHocNetworks interface
 */
#ifndef __IEnumDot11AdHocNetworks_INTERFACE_DEFINED__
#define __IEnumDot11AdHocNetworks_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDot11AdHocNetworks, 0x8f10cc28, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc28-cf0d-42a0-acbe-e2de7007384d")
IEnumDot11AdHocNetworks : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG cElt,
        IDot11AdHocNetwork **rgElt,
        ULONG *pcEltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG cElt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDot11AdHocNetworks **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDot11AdHocNetworks, 0x8f10cc28, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IEnumDot11AdHocNetworksVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDot11AdHocNetworks *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDot11AdHocNetworks *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDot11AdHocNetworks *This);

    /*** IEnumDot11AdHocNetworks methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumDot11AdHocNetworks *This,
        ULONG cElt,
        IDot11AdHocNetwork **rgElt,
        ULONG *pcEltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDot11AdHocNetworks *This,
        ULONG cElt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDot11AdHocNetworks *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDot11AdHocNetworks *This,
        IEnumDot11AdHocNetworks **ppEnum);

    END_INTERFACE
} IEnumDot11AdHocNetworksVtbl;

interface IEnumDot11AdHocNetworks {
    CONST_VTBL IEnumDot11AdHocNetworksVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDot11AdHocNetworks_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDot11AdHocNetworks_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDot11AdHocNetworks_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDot11AdHocNetworks methods ***/
#define IEnumDot11AdHocNetworks_Next(This,cElt,rgElt,pcEltFetched) (This)->lpVtbl->Next(This,cElt,rgElt,pcEltFetched)
#define IEnumDot11AdHocNetworks_Skip(This,cElt) (This)->lpVtbl->Skip(This,cElt)
#define IEnumDot11AdHocNetworks_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDot11AdHocNetworks_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDot11AdHocNetworks_QueryInterface(IEnumDot11AdHocNetworks* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDot11AdHocNetworks_AddRef(IEnumDot11AdHocNetworks* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDot11AdHocNetworks_Release(IEnumDot11AdHocNetworks* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDot11AdHocNetworks methods ***/
static inline HRESULT IEnumDot11AdHocNetworks_Next(IEnumDot11AdHocNetworks* This,ULONG cElt,IDot11AdHocNetwork **rgElt,ULONG *pcEltFetched) {
    return This->lpVtbl->Next(This,cElt,rgElt,pcEltFetched);
}
static inline HRESULT IEnumDot11AdHocNetworks_Skip(IEnumDot11AdHocNetworks* This,ULONG cElt) {
    return This->lpVtbl->Skip(This,cElt);
}
static inline HRESULT IEnumDot11AdHocNetworks_Reset(IEnumDot11AdHocNetworks* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDot11AdHocNetworks_Clone(IEnumDot11AdHocNetworks* This,IEnumDot11AdHocNetworks **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IEnumDot11AdHocNetworks_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDot11AdHocNetwork interface
 */
#ifndef __IDot11AdHocNetwork_INTERFACE_DEFINED__
#define __IDot11AdHocNetwork_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDot11AdHocNetwork, 0x8f10cc29, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc29-cf0d-42a0-acbe-e2de7007384d")
IDot11AdHocNetwork : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        DOT11_ADHOC_NETWORK_CONNECTION_STATUS *eStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSSID(
        LPWSTR *ppszwSSID) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasProfile(
        BOOLEAN *pf11d) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProfileName(
        LPWSTR *ppszwProfileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteProfile(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignalQuality(
        ULONG *puStrengthValue,
        ULONG *puStrengthMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSecuritySetting(
        IDot11AdHocSecuritySettings **pAdHocSecuritySetting) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContextGuid(
        GUID *pContextGuid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignature(
        GUID *pSignature) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInterface(
        IDot11AdHocInterface **pAdHocInterface) = 0;

    virtual HRESULT STDMETHODCALLTYPE Connect(
        LPCWSTR Passphrase,
        LONG GeographicalId,
        BOOLEAN fSaveProfile,
        BOOLEAN fMakeSavedProfileUserSpecific) = 0;

    virtual HRESULT STDMETHODCALLTYPE Disconnect(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDot11AdHocNetwork, 0x8f10cc29, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IDot11AdHocNetworkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDot11AdHocNetwork *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDot11AdHocNetwork *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDot11AdHocNetwork *This);

    /*** IDot11AdHocNetwork methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IDot11AdHocNetwork *This,
        DOT11_ADHOC_NETWORK_CONNECTION_STATUS *eStatus);

    HRESULT (STDMETHODCALLTYPE *GetSSID)(
        IDot11AdHocNetwork *This,
        LPWSTR *ppszwSSID);

    HRESULT (STDMETHODCALLTYPE *HasProfile)(
        IDot11AdHocNetwork *This,
        BOOLEAN *pf11d);

    HRESULT (STDMETHODCALLTYPE *GetProfileName)(
        IDot11AdHocNetwork *This,
        LPWSTR *ppszwProfileName);

    HRESULT (STDMETHODCALLTYPE *DeleteProfile)(
        IDot11AdHocNetwork *This);

    HRESULT (STDMETHODCALLTYPE *GetSignalQuality)(
        IDot11AdHocNetwork *This,
        ULONG *puStrengthValue,
        ULONG *puStrengthMax);

    HRESULT (STDMETHODCALLTYPE *GetSecuritySetting)(
        IDot11AdHocNetwork *This,
        IDot11AdHocSecuritySettings **pAdHocSecuritySetting);

    HRESULT (STDMETHODCALLTYPE *GetContextGuid)(
        IDot11AdHocNetwork *This,
        GUID *pContextGuid);

    HRESULT (STDMETHODCALLTYPE *GetSignature)(
        IDot11AdHocNetwork *This,
        GUID *pSignature);

    HRESULT (STDMETHODCALLTYPE *GetInterface)(
        IDot11AdHocNetwork *This,
        IDot11AdHocInterface **pAdHocInterface);

    HRESULT (STDMETHODCALLTYPE *Connect)(
        IDot11AdHocNetwork *This,
        LPCWSTR Passphrase,
        LONG GeographicalId,
        BOOLEAN fSaveProfile,
        BOOLEAN fMakeSavedProfileUserSpecific);

    HRESULT (STDMETHODCALLTYPE *Disconnect)(
        IDot11AdHocNetwork *This);

    END_INTERFACE
} IDot11AdHocNetworkVtbl;

interface IDot11AdHocNetwork {
    CONST_VTBL IDot11AdHocNetworkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDot11AdHocNetwork_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDot11AdHocNetwork_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDot11AdHocNetwork_Release(This) (This)->lpVtbl->Release(This)
/*** IDot11AdHocNetwork methods ***/
#define IDot11AdHocNetwork_GetStatus(This,eStatus) (This)->lpVtbl->GetStatus(This,eStatus)
#define IDot11AdHocNetwork_GetSSID(This,ppszwSSID) (This)->lpVtbl->GetSSID(This,ppszwSSID)
#define IDot11AdHocNetwork_HasProfile(This,pf11d) (This)->lpVtbl->HasProfile(This,pf11d)
#define IDot11AdHocNetwork_GetProfileName(This,ppszwProfileName) (This)->lpVtbl->GetProfileName(This,ppszwProfileName)
#define IDot11AdHocNetwork_DeleteProfile(This) (This)->lpVtbl->DeleteProfile(This)
#define IDot11AdHocNetwork_GetSignalQuality(This,puStrengthValue,puStrengthMax) (This)->lpVtbl->GetSignalQuality(This,puStrengthValue,puStrengthMax)
#define IDot11AdHocNetwork_GetSecuritySetting(This,pAdHocSecuritySetting) (This)->lpVtbl->GetSecuritySetting(This,pAdHocSecuritySetting)
#define IDot11AdHocNetwork_GetContextGuid(This,pContextGuid) (This)->lpVtbl->GetContextGuid(This,pContextGuid)
#define IDot11AdHocNetwork_GetSignature(This,pSignature) (This)->lpVtbl->GetSignature(This,pSignature)
#define IDot11AdHocNetwork_GetInterface(This,pAdHocInterface) (This)->lpVtbl->GetInterface(This,pAdHocInterface)
#define IDot11AdHocNetwork_Connect(This,Passphrase,GeographicalId,fSaveProfile,fMakeSavedProfileUserSpecific) (This)->lpVtbl->Connect(This,Passphrase,GeographicalId,fSaveProfile,fMakeSavedProfileUserSpecific)
#define IDot11AdHocNetwork_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDot11AdHocNetwork_QueryInterface(IDot11AdHocNetwork* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDot11AdHocNetwork_AddRef(IDot11AdHocNetwork* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDot11AdHocNetwork_Release(IDot11AdHocNetwork* This) {
    return This->lpVtbl->Release(This);
}
/*** IDot11AdHocNetwork methods ***/
static inline HRESULT IDot11AdHocNetwork_GetStatus(IDot11AdHocNetwork* This,DOT11_ADHOC_NETWORK_CONNECTION_STATUS *eStatus) {
    return This->lpVtbl->GetStatus(This,eStatus);
}
static inline HRESULT IDot11AdHocNetwork_GetSSID(IDot11AdHocNetwork* This,LPWSTR *ppszwSSID) {
    return This->lpVtbl->GetSSID(This,ppszwSSID);
}
static inline HRESULT IDot11AdHocNetwork_HasProfile(IDot11AdHocNetwork* This,BOOLEAN *pf11d) {
    return This->lpVtbl->HasProfile(This,pf11d);
}
static inline HRESULT IDot11AdHocNetwork_GetProfileName(IDot11AdHocNetwork* This,LPWSTR *ppszwProfileName) {
    return This->lpVtbl->GetProfileName(This,ppszwProfileName);
}
static inline HRESULT IDot11AdHocNetwork_DeleteProfile(IDot11AdHocNetwork* This) {
    return This->lpVtbl->DeleteProfile(This);
}
static inline HRESULT IDot11AdHocNetwork_GetSignalQuality(IDot11AdHocNetwork* This,ULONG *puStrengthValue,ULONG *puStrengthMax) {
    return This->lpVtbl->GetSignalQuality(This,puStrengthValue,puStrengthMax);
}
static inline HRESULT IDot11AdHocNetwork_GetSecuritySetting(IDot11AdHocNetwork* This,IDot11AdHocSecuritySettings **pAdHocSecuritySetting) {
    return This->lpVtbl->GetSecuritySetting(This,pAdHocSecuritySetting);
}
static inline HRESULT IDot11AdHocNetwork_GetContextGuid(IDot11AdHocNetwork* This,GUID *pContextGuid) {
    return This->lpVtbl->GetContextGuid(This,pContextGuid);
}
static inline HRESULT IDot11AdHocNetwork_GetSignature(IDot11AdHocNetwork* This,GUID *pSignature) {
    return This->lpVtbl->GetSignature(This,pSignature);
}
static inline HRESULT IDot11AdHocNetwork_GetInterface(IDot11AdHocNetwork* This,IDot11AdHocInterface **pAdHocInterface) {
    return This->lpVtbl->GetInterface(This,pAdHocInterface);
}
static inline HRESULT IDot11AdHocNetwork_Connect(IDot11AdHocNetwork* This,LPCWSTR Passphrase,LONG GeographicalId,BOOLEAN fSaveProfile,BOOLEAN fMakeSavedProfileUserSpecific) {
    return This->lpVtbl->Connect(This,Passphrase,GeographicalId,fSaveProfile,fMakeSavedProfileUserSpecific);
}
static inline HRESULT IDot11AdHocNetwork_Disconnect(IDot11AdHocNetwork* This) {
    return This->lpVtbl->Disconnect(This);
}
#endif
#endif

#endif


#endif  /* __IDot11AdHocNetwork_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDot1***************************** interface
 */
#ifndef __IDot1*****************************_INTERFACE_DEFINED__
#define __IDot1*****************************_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDot1*****************************, 0x8f10cc2a, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc2a-cf0d-42a0-acbe-e2de7007384d")
IDot1***************************** : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStatusChange(
        DOT11_ADHOC_NETWORK_CONNECTION_STATUS eStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnConnectFail(
        DOT11_ADHOC_CONNECT_FAIL_REASON eFailReason) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDot1*****************************, 0x8f10cc2a, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IDot1*****************************Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDot1***************************** *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDot1***************************** *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDot1***************************** *This);

    /*** IDot1***************************** methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStatusChange)(
        IDot1***************************** *This,
        DOT11_ADHOC_NETWORK_CONNECTION_STATUS eStatus);

    HRESULT (STDMETHODCALLTYPE *OnConnectFail)(
        IDot1***************************** *This,
        DOT11_ADHOC_CONNECT_FAIL_REASON eFailReason);

    END_INTERFACE
} IDot1*****************************Vtbl;

interface IDot1***************************** {
    CONST_VTBL IDot1*****************************Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDot1*****************************_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDot1*****************************_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDot1*****************************_Release(This) (This)->lpVtbl->Release(This)
/*** IDot1***************************** methods ***/
#define IDot1*****************************_OnStatusChange(This,eStatus) (This)->lpVtbl->OnStatusChange(This,eStatus)
#define IDot1*****************************_OnConnectFail(This,eFailReason) (This)->lpVtbl->OnConnectFail(This,eFailReason)
#else
/*** IUnknown methods ***/
static inline HRESULT IDot1*****************************_QueryInterface(IDot1****************************** This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDot1*****************************_AddRef(IDot1****************************** This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDot1*****************************_Release(IDot1****************************** This) {
    return This->lpVtbl->Release(This);
}
/*** IDot1***************************** methods ***/
static inline HRESULT IDot1*****************************_OnStatusChange(IDot1****************************** This,DOT11_ADHOC_NETWORK_CONNECTION_STATUS eStatus) {
    return This->lpVtbl->OnStatusChange(This,eStatus);
}
static inline HRESULT IDot1*****************************_OnConnectFail(IDot1****************************** This,DOT11_ADHOC_CONNECT_FAIL_REASON eFailReason) {
    return This->lpVtbl->OnConnectFail(This,eFailReason);
}
#endif
#endif

#endif


#endif  /* __IDot1*****************************_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDot11AdHocInterface interface
 */
#ifndef __IDot11AdHocInterface_INTERFACE_DEFINED__
#define __IDot11AdHocInterface_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDot11AdHocInterface, 0x8f10cc2b, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc2b-cf0d-42a0-acbe-e2de7007384d")
IDot11AdHocInterface : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDeviceSignature(
        GUID *pSignature) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFriendlyName(
        LPWSTR *ppszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsDot11d(
        BOOLEAN *pf11d) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsAdHocCapable(
        BOOLEAN *pfAdHocCapable) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRadioOn(
        BOOLEAN *pfIsRadioOn) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetActiveNetwork(
        IDot11AdHocNetwork **ppNetwork) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIEnumSecuritySettings(
        IEnumDot11AdHocSecuritySettings **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIEnumDot11AdHocNetworks(
        GUID *pFilterGuid,
        IEnumDot11AdHocNetworks **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        DOT11_ADHOC_NETWORK_CONNECTION_STATUS *pState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDot11AdHocInterface, 0x8f10cc2b, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IDot11AdHocInterfaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDot11AdHocInterface *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDot11AdHocInterface *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDot11AdHocInterface *This);

    /*** IDot11AdHocInterface methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceSignature)(
        IDot11AdHocInterface *This,
        GUID *pSignature);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IDot11AdHocInterface *This,
        LPWSTR *ppszName);

    HRESULT (STDMETHODCALLTYPE *IsDot11d)(
        IDot11AdHocInterface *This,
        BOOLEAN *pf11d);

    HRESULT (STDMETHODCALLTYPE *IsAdHocCapable)(
        IDot11AdHocInterface *This,
        BOOLEAN *pfAdHocCapable);

    HRESULT (STDMETHODCALLTYPE *IsRadioOn)(
        IDot11AdHocInterface *This,
        BOOLEAN *pfIsRadioOn);

    HRESULT (STDMETHODCALLTYPE *GetActiveNetwork)(
        IDot11AdHocInterface *This,
        IDot11AdHocNetwork **ppNetwork);

    HRESULT (STDMETHODCALLTYPE *GetIEnumSecuritySettings)(
        IDot11AdHocInterface *This,
        IEnumDot11AdHocSecuritySettings **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetIEnumDot11AdHocNetworks)(
        IDot11AdHocInterface *This,
        GUID *pFilterGuid,
        IEnumDot11AdHocNetworks **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IDot11AdHocInterface *This,
        DOT11_ADHOC_NETWORK_CONNECTION_STATUS *pState);

    END_INTERFACE
} IDot11AdHocInterfaceVtbl;

interface IDot11AdHocInterface {
    CONST_VTBL IDot11AdHocInterfaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDot11AdHocInterface_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDot11AdHocInterface_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDot11AdHocInterface_Release(This) (This)->lpVtbl->Release(This)
/*** IDot11AdHocInterface methods ***/
#define IDot11AdHocInterface_GetDeviceSignature(This,pSignature) (This)->lpVtbl->GetDeviceSignature(This,pSignature)
#define IDot11AdHocInterface_GetFriendlyName(This,ppszName) (This)->lpVtbl->GetFriendlyName(This,ppszName)
#define IDot11AdHocInterface_IsDot11d(This,pf11d) (This)->lpVtbl->IsDot11d(This,pf11d)
#define IDot11AdHocInterface_IsAdHocCapable(This,pfAdHocCapable) (This)->lpVtbl->IsAdHocCapable(This,pfAdHocCapable)
#define IDot11AdHocInterface_IsRadioOn(This,pfIsRadioOn) (This)->lpVtbl->IsRadioOn(This,pfIsRadioOn)
#define IDot11AdHocInterface_GetActiveNetwork(This,ppNetwork) (This)->lpVtbl->GetActiveNetwork(This,ppNetwork)
#define IDot11AdHocInterface_GetIEnumSecuritySettings(This,ppEnum) (This)->lpVtbl->GetIEnumSecuritySettings(This,ppEnum)
#define IDot11AdHocInterface_GetIEnumDot11AdHocNetworks(This,pFilterGuid,ppEnum) (This)->lpVtbl->GetIEnumDot11AdHocNetworks(This,pFilterGuid,ppEnum)
#define IDot11AdHocInterface_GetStatus(This,pState) (This)->lpVtbl->GetStatus(This,pState)
#else
/*** IUnknown methods ***/
static inline HRESULT IDot11AdHocInterface_QueryInterface(IDot11AdHocInterface* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDot11AdHocInterface_AddRef(IDot11AdHocInterface* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDot11AdHocInterface_Release(IDot11AdHocInterface* This) {
    return This->lpVtbl->Release(This);
}
/*** IDot11AdHocInterface methods ***/
static inline HRESULT IDot11AdHocInterface_GetDeviceSignature(IDot11AdHocInterface* This,GUID *pSignature) {
    return This->lpVtbl->GetDeviceSignature(This,pSignature);
}
static inline HRESULT IDot11AdHocInterface_GetFriendlyName(IDot11AdHocInterface* This,LPWSTR *ppszName) {
    return This->lpVtbl->GetFriendlyName(This,ppszName);
}
static inline HRESULT IDot11AdHocInterface_IsDot11d(IDot11AdHocInterface* This,BOOLEAN *pf11d) {
    return This->lpVtbl->IsDot11d(This,pf11d);
}
static inline HRESULT IDot11AdHocInterface_IsAdHocCapable(IDot11AdHocInterface* This,BOOLEAN *pfAdHocCapable) {
    return This->lpVtbl->IsAdHocCapable(This,pfAdHocCapable);
}
static inline HRESULT IDot11AdHocInterface_IsRadioOn(IDot11AdHocInterface* This,BOOLEAN *pfIsRadioOn) {
    return This->lpVtbl->IsRadioOn(This,pfIsRadioOn);
}
static inline HRESULT IDot11AdHocInterface_GetActiveNetwork(IDot11AdHocInterface* This,IDot11AdHocNetwork **ppNetwork) {
    return This->lpVtbl->GetActiveNetwork(This,ppNetwork);
}
static inline HRESULT IDot11AdHocInterface_GetIEnumSecuritySettings(IDot11AdHocInterface* This,IEnumDot11AdHocSecuritySettings **ppEnum) {
    return This->lpVtbl->GetIEnumSecuritySettings(This,ppEnum);
}
static inline HRESULT IDot11AdHocInterface_GetIEnumDot11AdHocNetworks(IDot11AdHocInterface* This,GUID *pFilterGuid,IEnumDot11AdHocNetworks **ppEnum) {
    return This->lpVtbl->GetIEnumDot11AdHocNetworks(This,pFilterGuid,ppEnum);
}
static inline HRESULT IDot11AdHocInterface_GetStatus(IDot11AdHocInterface* This,DOT11_ADHOC_NETWORK_CONNECTION_STATUS *pState) {
    return This->lpVtbl->GetStatus(This,pState);
}
#endif
#endif

#endif


#endif  /* __IDot11AdHocInterface_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumDot11AdHocInterfaces interface
 */
#ifndef __IEnumDot11AdHocInterfaces_INTERFACE_DEFINED__
#define __IEnumDot11AdHocInterfaces_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDot11AdHocInterfaces, 0x8f10cc2c, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc2c-cf0d-42a0-acbe-e2de7007384d")
IEnumDot11AdHocInterfaces : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG cElt,
        IDot11AdHocInterface **rgElt,
        ULONG *pcEltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG cElt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDot11AdHocInterfaces **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDot11AdHocInterfaces, 0x8f10cc2c, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IEnumDot11AdHocInterfacesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDot11AdHocInterfaces *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDot11AdHocInterfaces *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDot11AdHocInterfaces *This);

    /*** IEnumDot11AdHocInterfaces methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumDot11AdHocInterfaces *This,
        ULONG cElt,
        IDot11AdHocInterface **rgElt,
        ULONG *pcEltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDot11AdHocInterfaces *This,
        ULONG cElt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDot11AdHocInterfaces *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDot11AdHocInterfaces *This,
        IEnumDot11AdHocInterfaces **ppEnum);

    END_INTERFACE
} IEnumDot11AdHocInterfacesVtbl;

interface IEnumDot11AdHocInterfaces {
    CONST_VTBL IEnumDot11AdHocInterfacesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDot11AdHocInterfaces_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDot11AdHocInterfaces_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDot11AdHocInterfaces_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDot11AdHocInterfaces methods ***/
#define IEnumDot11AdHocInterfaces_Next(This,cElt,rgElt,pcEltFetched) (This)->lpVtbl->Next(This,cElt,rgElt,pcEltFetched)
#define IEnumDot11AdHocInterfaces_Skip(This,cElt) (This)->lpVtbl->Skip(This,cElt)
#define IEnumDot11AdHocInterfaces_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDot11AdHocInterfaces_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDot11AdHocInterfaces_QueryInterface(IEnumDot11AdHocInterfaces* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDot11AdHocInterfaces_AddRef(IEnumDot11AdHocInterfaces* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDot11AdHocInterfaces_Release(IEnumDot11AdHocInterfaces* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDot11AdHocInterfaces methods ***/
static inline HRESULT IEnumDot11AdHocInterfaces_Next(IEnumDot11AdHocInterfaces* This,ULONG cElt,IDot11AdHocInterface **rgElt,ULONG *pcEltFetched) {
    return This->lpVtbl->Next(This,cElt,rgElt,pcEltFetched);
}
static inline HRESULT IEnumDot11AdHocInterfaces_Skip(IEnumDot11AdHocInterfaces* This,ULONG cElt) {
    return This->lpVtbl->Skip(This,cElt);
}
static inline HRESULT IEnumDot11AdHocInterfaces_Reset(IEnumDot11AdHocInterfaces* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDot11AdHocInterfaces_Clone(IEnumDot11AdHocInterfaces* This,IEnumDot11AdHocInterfaces **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IEnumDot11AdHocInterfaces_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumDot11AdHocSecuritySettings interface
 */
#ifndef __IEnumDot11AdHocSecuritySettings_INTERFACE_DEFINED__
#define __IEnumDot11AdHocSecuritySettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDot11AdHocSecuritySettings, 0x8f10cc2d, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc2d-cf0d-42a0-acbe-e2de7007384d")
IEnumDot11AdHocSecuritySettings : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG cElt,
        IDot11AdHocSecuritySettings **rgElt,
        ULONG *pcEltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG cElt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDot11AdHocSecuritySettings **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDot11AdHocSecuritySettings, 0x8f10cc2d, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IEnumDot11AdHocSecuritySettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDot11AdHocSecuritySettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDot11AdHocSecuritySettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDot11AdHocSecuritySettings *This);

    /*** IEnumDot11AdHocSecuritySettings methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumDot11AdHocSecuritySettings *This,
        ULONG cElt,
        IDot11AdHocSecuritySettings **rgElt,
        ULONG *pcEltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDot11AdHocSecuritySettings *This,
        ULONG cElt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDot11AdHocSecuritySettings *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDot11AdHocSecuritySettings *This,
        IEnumDot11AdHocSecuritySettings **ppEnum);

    END_INTERFACE
} IEnumDot11AdHocSecuritySettingsVtbl;

interface IEnumDot11AdHocSecuritySettings {
    CONST_VTBL IEnumDot11AdHocSecuritySettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDot11AdHocSecuritySettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDot11AdHocSecuritySettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDot11AdHocSecuritySettings_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDot11AdHocSecuritySettings methods ***/
#define IEnumDot11AdHocSecuritySettings_Next(This,cElt,rgElt,pcEltFetched) (This)->lpVtbl->Next(This,cElt,rgElt,pcEltFetched)
#define IEnumDot11AdHocSecuritySettings_Skip(This,cElt) (This)->lpVtbl->Skip(This,cElt)
#define IEnumDot11AdHocSecuritySettings_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDot11AdHocSecuritySettings_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDot11AdHocSecuritySettings_QueryInterface(IEnumDot11AdHocSecuritySettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDot11AdHocSecuritySettings_AddRef(IEnumDot11AdHocSecuritySettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDot11AdHocSecuritySettings_Release(IEnumDot11AdHocSecuritySettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDot11AdHocSecuritySettings methods ***/
static inline HRESULT IEnumDot11AdHocSecuritySettings_Next(IEnumDot11AdHocSecuritySettings* This,ULONG cElt,IDot11AdHocSecuritySettings **rgElt,ULONG *pcEltFetched) {
    return This->lpVtbl->Next(This,cElt,rgElt,pcEltFetched);
}
static inline HRESULT IEnumDot11AdHocSecuritySettings_Skip(IEnumDot11AdHocSecuritySettings* This,ULONG cElt) {
    return This->lpVtbl->Skip(This,cElt);
}
static inline HRESULT IEnumDot11AdHocSecuritySettings_Reset(IEnumDot11AdHocSecuritySettings* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDot11AdHocSecuritySettings_Clone(IEnumDot11AdHocSecuritySettings* This,IEnumDot11AdHocSecuritySettings **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IEnumDot11AdHocSecuritySettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDot11AdHocSecuritySettings interface
 */
#ifndef __IDot11AdHocSecuritySettings_INTERFACE_DEFINED__
#define __IDot11AdHocSecuritySettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDot11AdHocSecuritySettings, 0x8f10cc2e, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc2e-cf0d-42a0-acbe-e2de7007384d")
IDot11AdHocSecuritySettings : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDot11AuthAlgorithm(
        DOT11_ADHOC_AUTH_ALGORITHM *pAuth) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDot11CipherAlgorithm(
        DOT11_ADHOC_CIPHER_ALGORITHM *pCipher) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDot11AdHocSecuritySettings, 0x8f10cc2e, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IDot11AdHocSecuritySettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDot11AdHocSecuritySettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDot11AdHocSecuritySettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDot11AdHocSecuritySettings *This);

    /*** IDot11AdHocSecuritySettings methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDot11AuthAlgorithm)(
        IDot11AdHocSecuritySettings *This,
        DOT11_ADHOC_AUTH_ALGORITHM *pAuth);

    HRESULT (STDMETHODCALLTYPE *GetDot11CipherAlgorithm)(
        IDot11AdHocSecuritySettings *This,
        DOT11_ADHOC_CIPHER_ALGORITHM *pCipher);

    END_INTERFACE
} IDot11AdHocSecuritySettingsVtbl;

interface IDot11AdHocSecuritySettings {
    CONST_VTBL IDot11AdHocSecuritySettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDot11AdHocSecuritySettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDot11AdHocSecuritySettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDot11AdHocSecuritySettings_Release(This) (This)->lpVtbl->Release(This)
/*** IDot11AdHocSecuritySettings methods ***/
#define IDot11AdHocSecuritySettings_GetDot11AuthAlgorithm(This,pAuth) (This)->lpVtbl->GetDot11AuthAlgorithm(This,pAuth)
#define IDot11AdHocSecuritySettings_GetDot11CipherAlgorithm(This,pCipher) (This)->lpVtbl->GetDot11CipherAlgorithm(This,pCipher)
#else
/*** IUnknown methods ***/
static inline HRESULT IDot11AdHocSecuritySettings_QueryInterface(IDot11AdHocSecuritySettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDot11AdHocSecuritySettings_AddRef(IDot11AdHocSecuritySettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDot11AdHocSecuritySettings_Release(IDot11AdHocSecuritySettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDot11AdHocSecuritySettings methods ***/
static inline HRESULT IDot11AdHocSecuritySettings_GetDot11AuthAlgorithm(IDot11AdHocSecuritySettings* This,DOT11_ADHOC_AUTH_ALGORITHM *pAuth) {
    return This->lpVtbl->GetDot11AuthAlgorithm(This,pAuth);
}
static inline HRESULT IDot11AdHocSecuritySettings_GetDot11CipherAlgorithm(IDot11AdHocSecuritySettings* This,DOT11_ADHOC_CIPHER_ALGORITHM *pCipher) {
    return This->lpVtbl->GetDot11CipherAlgorithm(This,pCipher);
}
#endif
#endif

#endif


#endif  /* __IDot11AdHocSecuritySettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDot11AdHocInterfaceNotificationSink interface
 */
#ifndef __IDot11AdHocInterfaceNotificationSink_INTERFACE_DEFINED__
#define __IDot11AdHocInterfaceNotificationSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDot11AdHocInterfaceNotificationSink, 0x8f10cc2f, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f10cc2f-cf0d-42a0-acbe-e2de7007384d")
IDot11AdHocInterfaceNotificationSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnConnectionStatusChange(
        DOT11_ADHOC_NETWORK_CONNECTION_STATUS eStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDot11AdHocInterfaceNotificationSink, 0x8f10cc2f, 0xcf0d, 0x42a0, 0xac,0xbe, 0xe2,0xde,0x70,0x07,0x38,0x4d)
#endif
#else
typedef struct IDot11AdHocInterfaceNotificationSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDot11AdHocInterfaceNotificationSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDot11AdHocInterfaceNotificationSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDot11AdHocInterfaceNotificationSink *This);

    /*** IDot11AdHocInterfaceNotificationSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnConnectionStatusChange)(
        IDot11AdHocInterfaceNotificationSink *This,
        DOT11_ADHOC_NETWORK_CONNECTION_STATUS eStatus);

    END_INTERFACE
} IDot11AdHocInterfaceNotificationSinkVtbl;

interface IDot11AdHocInterfaceNotificationSink {
    CONST_VTBL IDot11AdHocInterfaceNotificationSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDot11AdHocInterfaceNotificationSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDot11AdHocInterfaceNotificationSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDot11AdHocInterfaceNotificationSink_Release(This) (This)->lpVtbl->Release(This)
/*** IDot11AdHocInterfaceNotificationSink methods ***/
#define IDot11AdHocInterfaceNotificationSink_OnConnectionStatusChange(This,eStatus) (This)->lpVtbl->OnConnectionStatusChange(This,eStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IDot11AdHocInterfaceNotificationSink_QueryInterface(IDot11AdHocInterfaceNotificationSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDot11AdHocInterfaceNotificationSink_AddRef(IDot11AdHocInterfaceNotificationSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDot11AdHocInterfaceNotificationSink_Release(IDot11AdHocInterfaceNotificationSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IDot11AdHocInterfaceNotificationSink methods ***/
static inline HRESULT IDot11AdHocInterfaceNotificationSink_OnConnectionStatusChange(IDot11AdHocInterfaceNotificationSink* This,DOT11_ADHOC_NETWORK_CONNECTION_STATUS eStatus) {
    return This->lpVtbl->OnConnectionStatusChange(This,eStatus);
}
#endif
#endif

#endif


#endif  /* __IDot11AdHocInterfaceNotificationSink_INTERFACE_DEFINED__ */

#ifndef __ADHOCLib_LIBRARY_DEFINED__
#define __ADHOCLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_ADHOCLib, 0x45357166, 0xff38, 0x4302, 0x8f,0x5c, 0xdf,0x5b,0x70,0x3a,0x6e,0x3d);

/*****************************************************************************
 * Dot11AdHocManager coclass
 */

DEFINE_GUID(CLSID_Dot11AdHocManager, 0xdd06a84f, 0x83bd, 0x4d01, 0x8a,0xb9, 0x23,0x89,0xfe,0xa0,0x86,0x9e);

#ifdef __cplusplus
class DECLSPEC_UUID("dd06a84f-83bd-4d01-8ab9-2389fea0869e") Dot11AdHocManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(Dot11AdHocManager, 0xdd06a84f, 0x83bd, 0x4d01, 0x8a,0xb9, 0x23,0x89,0xfe,0xa0,0x86,0x9e)
#endif
#endif

#endif /* __ADHOCLib_LIBRARY_DEFINED__ */
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __adhoc_h__ */
