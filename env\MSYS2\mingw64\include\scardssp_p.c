/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#define USE_STUBLESS_PROXY

#ifndef __REDQ_RPCPROXY_H_VERSION__
#define __REQUIRED_RPCPROXY_H_VERSION__ 475
#endif

#include "rpcproxy.h"
#ifndef __RPCPROXY_H_VERSION__
#error This stub requires an updated version of <rpcproxy.h>
#endif

#include "scardssp.h"

#define TYPE_FORMAT_STRING_SIZE 1313
#define PROC_FORMAT_STRING_SIZE 3859
#define TRANSMIT_AS_TABLE_SIZE 0
#define WIRE_MARSHAL_TABLE_SIZE 3

typedef struct _MIDL_TYPE_FORMAT_STRING {
  short Pad;
  unsigned char Format[TYPE_FORMAT_STRING_SIZE];
} MIDL_TYPE_FORMAT_STRING;

typedef struct _MIDL_PROC_FORMAT_STRING {
  short Pad;
  unsigned char Format[PROC_FORMAT_STRING_SIZE];
} MIDL_PROC_FORMAT_STRING;

extern const MIDL_TYPE_FORMAT_STRING __MIDL_TypeFormatString;
extern const MIDL_PROC_FORMAT_STRING __MIDL_ProcFormatString;

static const MIDL_STUB_DESC Object_StubDesc;
static const MIDL_SERVER_INFO IByteBuffer_ServerInfo;

#pragma code_seg(".orpc")
static const unsigned short IByteBuffer_FormatStringOffsetTable[] = {
  (unsigned short) -1,(unsigned short) -1,(unsigned short) -1,(unsigned short) -1,0,36,72,108,144,198,240,288,336,366,414,450,492,540
};

static const MIDL_SERVER_INFO IByteBuffer_ServerInfo = {
  &Object_StubDesc,0,__MIDL_ProcFormatString.Format,&IByteBuffer_FormatStringOffsetTable[-3],0,0,0,0
};

static const MIDL_STUBLESS_PROXY_INFO IByteBuffer_ProxyInfo = {
  &Object_StubDesc,__MIDL_ProcFormatString.Format,&IByteBuffer_FormatStringOffsetTable[-3],0,0,0
};

CINTERFACE_PROXY_VTABLE(21) _IByteBufferProxyVtbl = {
  &IByteBuffer_ProxyInfo,&IID_IByteBuffer,IUnknown_QueryInterface_Proxy,IUnknown_AddRef_Proxy,IUnknown_Release_Proxy ,0 ,0 ,0 ,0 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1
};

static const PRPC_STUB_FUNCTION IByteBuffer_table[] = {
  STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2
};

CInterfaceStubVtbl _IByteBufferStubVtbl = {
  &IID_IByteBuffer,&IByteBuffer_ServerInfo,21,&IByteBuffer_table[-3],CStdStubBuffer_DELEGATING_METHODS
};

static const MIDL_SERVER_INFO ISCardTypeConv_ServerInfo;

#pragma code_seg(".orpc")
static const unsigned short ISCardTypeConv_FormatStringOffsetTable[] = {
  (unsigned short) -1,(unsigned short) -1,(unsigned short) -1,(unsigned short) -1,588,636,678,720,762,804,846,888,930,972
};

static const MIDL_SERVER_INFO ISCardTypeConv_ServerInfo = {
  &Object_StubDesc,0,__MIDL_ProcFormatString.Format,&ISCardTypeConv_FormatStringOffsetTable[-3],0,0,0,0
};

static const MIDL_STUBLESS_PROXY_INFO ISCardTypeConv_ProxyInfo = {
  &Object_StubDesc,__MIDL_ProcFormatString.Format,&ISCardTypeConv_FormatStringOffsetTable[-3],0,0,0
};

CINTERFACE_PROXY_VTABLE(17) _ISCardTypeConvProxyVtbl = {
  &ISCardTypeConv_ProxyInfo,&IID_ISCardTypeConv,IUnknown_QueryInterface_Proxy,IUnknown_AddRef_Proxy,IUnknown_Release_Proxy ,0 ,0 ,0 ,0 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1
};

static const PRPC_STUB_FUNCTION ISCardTypeConv_table[] = {
  STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2
};

CInterfaceStubVtbl _ISCardTypeConvStubVtbl = {
  &IID_ISCardTypeConv,&ISCardTypeConv_ServerInfo,17,&ISCardTypeConv_table[-3],CStdStubBuffer_DELEGATING_METHODS
};

static const MIDL_STUB_DESC Object_StubDesc;
static const MIDL_SERVER_INFO ISCardCmd_ServerInfo;

#pragma code_seg(".orpc")
static const unsigned short ISCardCmd_FormatStringOffsetTable[] = {
  (unsigned short) -1,(unsigned short) -1,(unsigned short) -1,(unsigned short) -1,1014,1050,1086,1122,1158,1194,1230,1266,1302,1338,1374,1410,1446,1482,1518,1554,1590,1626,1662,1698,1734,1770,1806,1842,1878,1914,1950,1986,2022,2088,2118,2160,2196
};

static const MIDL_SERVER_INFO ISCardCmd_ServerInfo = {
  &Object_StubDesc,0,__MIDL_ProcFormatString.Format,&ISCardCmd_FormatStringOffsetTable[-3],0,0,0,0
};

static const MIDL_STUBLESS_PROXY_INFO ISCardCmd_ProxyInfo = {
  &Object_StubDesc,__MIDL_ProcFormatString.Format,&ISCardCmd_FormatStringOffsetTable[-3],0,0,0
};

CINTERFACE_PROXY_VTABLE(40) _ISCardCmdProxyVtbl = {
  &ISCardCmd_ProxyInfo,&IID_ISCardCmd,IUnknown_QueryInterface_Proxy,IUnknown_AddRef_Proxy,IUnknown_Release_Proxy ,0 ,0 ,0 ,0 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1
};

static const PRPC_STUB_FUNCTION ISCardCmd_table[] = {
  STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2
};

CInterfaceStubVtbl _ISCardCmdStubVtbl = {
  &IID_ISCardCmd,&ISCardCmd_ServerInfo,40,&ISCardCmd_table[-3],CStdStubBuffer_DELEGATING_METHODS
};

static const MIDL_STUB_DESC Object_StubDesc;
static const MIDL_SERVER_INFO ISCardISO7816_ServerInfo;

#pragma code_seg(".orpc")
static const unsigned short ISCardISO7816_FormatStringOffsetTable[] = {
  (unsigned short) -1,(unsigned short) -1,(unsigned short) -1,(unsigned short) -1,2232,2280,2334,2388,2430,2484,2538,2598,2646,2700,2754,2808,1446,2868,2922,2976,3024,3078
};

static const MIDL_SERVER_INFO ISCardISO7816_ServerInfo = {
  &Object_StubDesc,0,__MIDL_ProcFormatString.Format,&ISCardISO7816_FormatStringOffsetTable[-3],0,0,0,0
};

static const MIDL_STUBLESS_PROXY_INFO ISCardISO7816_ProxyInfo = {
  &Object_StubDesc,__MIDL_ProcFormatString.Format,&ISCardISO7816_FormatStringOffsetTable[-3],0,0,0
};

CINTERFACE_PROXY_VTABLE(25) _ISCardISO7816ProxyVtbl = {
  &ISCardISO7816_ProxyInfo,&IID_ISCardISO7816,IUnknown_QueryInterface_Proxy,IUnknown_AddRef_Proxy,IUnknown_Release_Proxy ,0 ,0 ,0 ,0 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1
};

static const PRPC_STUB_FUNCTION ISCardISO7816_table[] = {
  STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2
};

CInterfaceStubVtbl _ISCardISO7816StubVtbl = {
  &IID_ISCardISO7816,&ISCardISO7816_ServerInfo,25,&ISCardISO7816_table[-3],CStdStubBuffer_DELEGATING_METHODS
};

static const MIDL_STUB_DESC Object_StubDesc;
static const MIDL_SERVER_INFO ISCard_ServerInfo;

#pragma code_seg(".orpc")
static const unsigned short ISCard_FormatStringOffsetTable[] = {
  (unsigned short) -1,(unsigned short) -1,(unsigned short) -1,(unsigned short) -1,1014,3132,1086,3168,3204,3240,3276,3324,336,3360,3402,3438
};

static const MIDL_SERVER_INFO ISCard_ServerInfo = {
  &Object_StubDesc,0,__MIDL_ProcFormatString.Format,&ISCard_FormatStringOffsetTable[-3],0,0,0,0
};

static const MIDL_STUBLESS_PROXY_INFO ISCard_ProxyInfo = {
  &Object_StubDesc,__MIDL_ProcFormatString.Format,&ISCard_FormatStringOffsetTable[-3],0,0,0
};

CINTERFACE_PROXY_VTABLE(19) _ISCardProxyVtbl = {
  &ISCard_ProxyInfo,&IID_ISCard,IUnknown_QueryInterface_Proxy,IUnknown_AddRef_Proxy,IUnknown_Release_Proxy ,0 ,0 ,0 ,0 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1
};

static const PRPC_STUB_FUNCTION ISCard_table[] = {
  STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2
};

CInterfaceStubVtbl _ISCardStubVtbl = {
  &IID_ISCard,&ISCard_ServerInfo,19,&ISCard_table[-3],CStdStubBuffer_DELEGATING_METHODS
};

static const MIDL_STUB_DESC Object_StubDesc;
static const MIDL_SERVER_INFO ISCardDatabase_ServerInfo;

#pragma code_seg(".orpc")
static const unsigned short ISCardDatabase_FormatStringOffsetTable[] = {
  (unsigned short) -1,(unsigned short) -1,(unsigned short) -1,(unsigned short) -1,3474,3516,3558,3612,3654
};

static const MIDL_SERVER_INFO ISCardDatabase_ServerInfo = {
  &Object_StubDesc,0,__MIDL_ProcFormatString.Format,&ISCardDatabase_FormatStringOffsetTable[-3],0,0,0,0
};

static const MIDL_STUBLESS_PROXY_INFO ISCardDatabase_ProxyInfo = {
  &Object_StubDesc,__MIDL_ProcFormatString.Format,&ISCardDatabase_FormatStringOffsetTable[-3],0,0,0
};

CINTERFACE_PROXY_VTABLE(12) _ISCardDatabaseProxyVtbl = {
  &ISCardDatabase_ProxyInfo,&IID_ISCardDatabase,IUnknown_QueryInterface_Proxy,IUnknown_AddRef_Proxy,IUnknown_Release_Proxy ,0 ,0 ,0 ,0 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1 ,(void *)-1
};

static const PRPC_STUB_FUNCTION ISCardDatabase_table[] = {
  STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2,NdrStubCall2
};

CInterfaceStubVtbl _ISCardDatabaseStubVtbl = {
  &IID_ISCardDatabase,&ISCardDatabase_ServerInfo,12,&ISCardDatabase_table[-3],CStdStubBuffer_DELEGATING_METHODS
};

static const MIDL_STUB_DESC Object_StubDesc;
static const MIDL_SERVER_INFO ISCardLocate_ServerInfo;

#pragma code_seg(".orpc")
static const unsigned short ISCardLocate_FormatStringOffsetTable[] = {
  (unsigned short) -1,(unsigned short) -1,(unsigned short) -1,(unsigned short) -1,3696,3750,3804
};

static const MIDL_SERVER_INFO ISCardLocate_ServerInfo = {
  &Object_StubDesc,0,__MIDL_ProcFormatString.Format,&ISCardLocate_FormatStringOffsetTable[-3],0,0,0,0
};

static const MIDL_STUBLESS_PROXY_INFO ISCardLocate_ProxyInfo = {
  &Object_StubDesc,__MIDL_ProcFormatString.Format,&ISCardLocate_FormatStringOffsetTable[-3],0,0,0
};

CINTERFACE_PROXY_VTABLE(10) _ISCardLocateProxyVtbl = {
  &ISCardLocate_ProxyInfo,&IID_ISCardLocate,IUnknown_QueryInterface_Proxy,IUnknown_AddRef_Proxy,IUnknown_Release_Proxy ,0 ,0 ,0 ,0 ,(void *)-1 ,(void *)-1 ,(void *)-1
};

static const PRPC_STUB_FUNCTION ISCardLocate_table[] = {
  STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,STUB_FORWARDING_FUNCTION,NdrStubCall2,NdrStubCall2,NdrStubCall2
};

CInterfaceStubVtbl _ISCardLocateStubVtbl = {
  &IID_ISCardLocate,&ISCardLocate_ServerInfo,10,&ISCardLocate_table[-3],CStdStubBuffer_DELEGATING_METHODS
};

static const USER_MARSHAL_ROUTINE_QUADRUPLE UserMarshalRoutines[WIRE_MARSHAL_TABLE_SIZE];

static const MIDL_STUB_DESC Object_StubDesc = {
  0,NdrOleAllocate,NdrOleFree,0,0,0,0,0,__MIDL_TypeFormatString.Format,1,0x50002,0,0x5030117,0,UserMarshalRoutines,0,0x1,0,0,0
};

#pragma data_seg(".rdata")
static const USER_MARSHAL_ROUTINE_QUADRUPLE UserMarshalRoutines[WIRE_MARSHAL_TABLE_SIZE] = {
  { HGLOBAL_UserSize,HGLOBAL_UserMarshal,HGLOBAL_UserUnmarshal,HGLOBAL_UserFree },
  { LPSAFEARRAY_UserSize,LPSAFEARRAY_UserMarshal,LPSAFEARRAY_UserUnmarshal,LPSAFEARRAY_UserFree},
  { BSTR_UserSize,BSTR_UserMarshal,BSTR_UserUnmarshal,BSTR_UserFree }
};

#if !defined(__RPC_WIN32__)
#error Invalid build platform for this stub.
#endif

const MIDL_PROC_FORMAT_STRING __MIDL_ProcFormatString = {
  0,{ 0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x7),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x45,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x13),NdrFcShort(0x4),NdrFcShort(0x2),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x8),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x46,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x6),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x9),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x47,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x201b),NdrFcShort(0x4),NdrFcShort(0x18),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xa),NdrFcShort(0xc),NdrFcShort(0x8),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xb),NdrFcShort(0x18),NdrFcShort(0x18),NdrFcShort(0x18),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x201b),NdrFcShort(0x4),NdrFcShort(0x18),NdrFcShort(0x48),NdrFcShort(0x8),0x8,0x0,NdrFcShort(0x158),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x158),NdrFcShort(0x10),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xc),NdrFcShort(0x10),NdrFcShort(0xd),NdrFcShort(0x8),0x44,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x148),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xd),NdrFcShort(0x14),NdrFcShort(0x18),NdrFcShort(0x8),0x44,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x8,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xe),NdrFcShort(0x14),NdrFcShort(0x15),NdrFcShort(0x15),0x44,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x158),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x8,0x0,NdrFcShort(0x158),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xf),NdrFcShort(0x8),NdrFcShort(0x0),NdrFcShort(0x8),0x44,0x1,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x70),NdrFcShort(0x4),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x10),NdrFcShort(0x14),NdrFcShort(0x18),NdrFcShort(0x10),0x44,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x8,0x0,NdrFcShort(0x158),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x11),NdrFcShort(0xc),NdrFcShort(0x8),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x12),NdrFcShort(0x10),NdrFcShort(0x24),NdrFcShort(0x24),0x44,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x11a),NdrFcShort(0x4),NdrFcShort(0x3a),NdrFcShort(0x48),NdrFcShort(0x8),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x13),NdrFcShort(0x14),NdrFcShort(0x18),NdrFcShort(0x8),0x44,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x8,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x14),NdrFcShort(0x14),NdrFcShort(0x15),NdrFcShort(0x15),0x44,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x158),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x8,0x0,NdrFcShort(0x158),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x7),NdrFcShort(0x14),NdrFcShort(0xd),NdrFcShort(0x8),0x45,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x148),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x8,0x0,NdrFcShort(0x13),NdrFcShort(0xc),NdrFcShort(0x44),NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x8),NdrFcShort(0x10),NdrFcShort(0x0),NdrFcShort(0x8),0x47,0x3,0x8,0x3,NdrFcShort(0x3),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x1c),NdrFcShort(0x2013),NdrFcShort(0x8),NdrFcShort(0x48),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x9),NdrFcShort(0x10),NdrFcShort(0x0),NdrFcShort(0x8),0x47,0x3,0x8,0x3,NdrFcShort(0x12),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x1c),NdrFcShort(0x2113),NdrFcShort(0x8),NdrFcShort(0x4a6),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xa),NdrFcShort(0x10),NdrFcShort(0x0),NdrFcShort(0x8),0x47,0x3,0x8,0x5,NdrFcShort(0x0),NdrFcShort(0x12),NdrFcShort(0x0),NdrFcShort(0x8b),NdrFcShort(0x4),NdrFcShort(0x4b8),NdrFcShort(0x13),NdrFcShort(0x8),NdrFcShort(0x44),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xb),NdrFcShort(0x10),NdrFcShort(0x8),NdrFcShort(0x15),0x44,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x2012),NdrFcShort(0x8),NdrFcShort(0x4c2),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xc),NdrFcShort(0x10),NdrFcShort(0x8),NdrFcShort(0x8),0x45,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x13),NdrFcShort(0x8),NdrFcShort(0x44),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xd),NdrFcShort(0x10),NdrFcShort(0x8),NdrFcShort(0x8),0x45,0x3,0x8,0x3,NdrFcShort(0x12),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x2113),NdrFcShort(0x8),NdrFcShort(0x4a6),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xe),NdrFcShort(0x10),NdrFcShort(0x5),NdrFcShort(0x8),0x46,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x6),NdrFcShort(0x148),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xf),NdrFcShort(0x10),NdrFcShort(0x0),NdrFcShort(0x8),0x47,0x3,0x8,0x3,NdrFcShort(0x3),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x6),NdrFcShort(0x2013),NdrFcShort(0x8),NdrFcShort(0x48),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x10),NdrFcShort(0x10),NdrFcShort(0x0),NdrFcShort(0x18),0x46,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x6),NdrFcShort(0x2112),NdrFcShort(0x8),NdrFcShort(0x2ce),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x7),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x45,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x13),NdrFcShort(0x4),NdrFcShort(0x44),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x8),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x46,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x1c),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x9),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x10),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xa),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x45,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x13),NdrFcShort(0x4),NdrFcShort(0x44),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x46,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x1c),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xc),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x10),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xd),NdrFcShort(0xc),NdrFcShort(0x8),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xe),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xf),NdrFcShort(0xc),NdrFcShort(0x5),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x10),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x45,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x13),NdrFcShort(0x4),NdrFcShort(0x44),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x11),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x46,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x1c),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x12),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x13),NdrFcShort(0xc),NdrFcShort(0x5),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x14),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x10),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x15),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x16),NdrFcShort(0xc),NdrFcShort(0x5),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x17),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x18),NdrFcShort(0xc),NdrFcShort(0x5),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x19),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x1a),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xe),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x6,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x1b),NdrFcShort(0xc),NdrFcShort(0x6),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x6,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x1c),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x1d),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x1e),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xe),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2010),NdrFcShort(0x4),NdrFcShort(0x4d6),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x1f),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x20),NdrFcShort(0xc),NdrFcShort(0x5),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x21),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x22),NdrFcShort(0xc),NdrFcShort(0x5),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x23),NdrFcShort(0x20),NdrFcShort(0x1c),NdrFcShort(0x8),0x46,0x7,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x10),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0x14),NdrFcShort(0x1c),NdrFcShort(0x148),NdrFcShort(0x18),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x1c),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x24),NdrFcShort(0x8),NdrFcShort(0x0),NdrFcShort(0x8),0x44,0x1,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x70),NdrFcShort(0x4),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x25),NdrFcShort(0x10),NdrFcShort(0x6),NdrFcShort(0x8),0x46,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x1c),NdrFcShort(0x48),NdrFcShort(0x8),0xd,0x0,NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x26),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xd),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x27),NdrFcShort(0xc),NdrFcShort(0x5),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x7),NdrFcShort(0x14),NdrFcShort(0x5),NdrFcShort(0x8),0x47,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0x8),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0xc),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x8),NdrFcShort(0x18),NdrFcShort(0xa),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x9),NdrFcShort(0x18),NdrFcShort(0xa),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xa),NdrFcShort(0x10),NdrFcShort(0x8),NdrFcShort(0x8),0x47,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x201b),NdrFcShort(0x8),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xb),NdrFcShort(0x18),NdrFcShort(0x12),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xc),NdrFcShort(0x18),NdrFcShort(0x12),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xd),NdrFcShort(0x1c),NdrFcShort(0x12),NdrFcShort(0x8),0x47,0x6,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x48),NdrFcShort(0x10),0x8,0x0,NdrFcShort(0x201b),NdrFcShort(0x14),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x18),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xe),NdrFcShort(0x14),NdrFcShort(0xa),NdrFcShort(0x8),0x47,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0x201b),NdrFcShort(0xc),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xf),NdrFcShort(0x18),NdrFcShort(0xa),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x10),NdrFcShort(0x18),NdrFcShort(0x12),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x11),NdrFcShort(0x18),NdrFcShort(0x12),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x12),NdrFcShort(0x1c),NdrFcShort(0x12),NdrFcShort(0x8),0x47,0x6,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x48),NdrFcShort(0x10),0x8,0x0,NdrFcShort(0x201b),NdrFcShort(0x14),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x18),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x14),NdrFcShort(0x18),NdrFcShort(0xa),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x15),NdrFcShort(0x18),NdrFcShort(0xa),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x16),NdrFcShort(0x14),NdrFcShort(0x5),NdrFcShort(0x8),0x47,0x4,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0x8),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0xc),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x17),NdrFcShort(0x18),NdrFcShort(0xa),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x18),NdrFcShort(0x18),NdrFcShort(0xa),NdrFcShort(0x8),0x47,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x2,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0x2,0x0,NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x1c),NdrFcShort(0x201b),NdrFcShort(0x10),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x8),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x10),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xa),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x10),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0xe,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xb),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x10),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x2150),NdrFcShort(0x4),0xe,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xc),NdrFcShort(0xc),NdrFcShort(0x8),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xd),NdrFcShort(0x14),NdrFcShort(0x10),NdrFcShort(0x8),0x46,0x4,0x8,0x5,NdrFcShort(0x0),NdrFcShort(0x1),NdrFcShort(0x0),NdrFcShort(0x8b),NdrFcShort(0x4),NdrFcShort(0x4f8),NdrFcShort(0x48),NdrFcShort(0x8),0xe,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0xe,0x0,NdrFcShort(0x70),NdrFcShort(0x10),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xe),NdrFcShort(0xc),NdrFcShort(0x8),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0xe,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x10),NdrFcShort(0x10),NdrFcShort(0x10),NdrFcShort(0x8),0x44,0x3,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0xe,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0xe,0x0,NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x11),NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0x8),0x47,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x201b),NdrFcShort(0x4),NdrFcShort(0x4da),NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x12),NdrFcShort(0xc),NdrFcShort(0x8),NdrFcShort(0x8),0x44,0x2,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0xe,0x0,NdrFcShort(0x70),NdrFcShort(0x8),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x7),NdrFcShort(0x10),NdrFcShort(0x0),NdrFcShort(0x30),0x46,0x3,0x8,0x5,NdrFcShort(0x0),NdrFcShort(0x1),NdrFcShort(0x0),NdrFcShort(0x8b),NdrFcShort(0x4),NdrFcShort(0x4f8),NdrFcShort(0x2012),NdrFcShort(0x8),NdrFcShort(0x502),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x8),NdrFcShort(0x10),NdrFcShort(0x0),NdrFcShort(0x8),0x47,0x3,0x8,0x7,NdrFcShort(0x12),NdrFcShort(0x1),NdrFcShort(0x0),NdrFcShort(0x8b),NdrFcShort(0x4),NdrFcShort(0x4f8),NdrFcShort(0x2113),NdrFcShort(0x8),NdrFcShort(0x4a6),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x9),NdrFcShort(0x18),NdrFcShort(0x8),NdrFcShort(0x8),0x47,0x5,0x8,0x7,NdrFcShort(0x12),NdrFcShort(0x12),NdrFcShort(0x0),NdrFcShort(0xb),NdrFcShort(0x4),NdrFcShort(0x1c),NdrFcShort(0x8b),NdrFcShort(0x8),NdrFcShort(0x4b8),NdrFcShort(0x48),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x2113),NdrFcShort(0x10),NdrFcShort(0x4a6),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xa),NdrFcShort(0x10),NdrFcShort(0x8),NdrFcShort(0x8),0x45,0x3,0x8,0x3,NdrFcShort(0x12),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x2113),NdrFcShort(0x8),NdrFcShort(0x4a6),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0xb),NdrFcShort(0x10),NdrFcShort(0x8),NdrFcShort(0x8),0x45,0x3,0x8,0x3,NdrFcShort(0x12),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0x8,0x0,NdrFcShort(0x2113),NdrFcShort(0x8),NdrFcShort(0x4a6),NdrFcShort(0x70),NdrFcShort(0xc),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x7),NdrFcShort(0x18),NdrFcShort(0x8),NdrFcShort(0x8),0x46,0x5,0x8,0x5,NdrFcShort(0x0),NdrFcShort(0x25),NdrFcShort(0x0),NdrFcShort(0x8b),NdrFcShort(0x4),NdrFcShort(0x4b8),NdrFcShort(0x8b),NdrFcShort(0x8),NdrFcShort(0x4b8),NdrFcShort(0x8b),NdrFcShort(0xc),NdrFcShort(0x4f8),NdrFcShort(0x48),NdrFcShort(0x10),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x8),NdrFcShort(0x18),NdrFcShort(0x8),NdrFcShort(0x8),0x46,0x5,0x8,0x5,NdrFcShort(0x0),NdrFcShort(0x25),NdrFcShort(0x0),NdrFcShort(0x8b),NdrFcShort(0x4),NdrFcShort(0x4b8),NdrFcShort(0x8b),NdrFcShort(0x8),NdrFcShort(0x4b8),NdrFcShort(0x8b),NdrFcShort(0xc),NdrFcShort(0x4f8),NdrFcShort(0x48),NdrFcShort(0x10),0x8,0x0,NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x33,0x6c,NdrFcLong(0x0),NdrFcShort(0x9),NdrFcShort(0x18),NdrFcShort(0x18),NdrFcShort(0x38),0x44,0x5,0x8,0x1,NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x0),NdrFcShort(0x48),NdrFcShort(0x4),0xe,0x0,NdrFcShort(0x48),NdrFcShort(0x8),0xe,0x0,NdrFcShort(0x48),NdrFcShort(0xc),0x8,0x0,NdrFcShort(0x2012),NdrFcShort(0x10),NdrFcShort(0x50a),NdrFcShort(0x70),NdrFcShort(0x14),0x8,0x0,0x0 }
};

const MIDL_TYPE_FORMAT_STRING __MIDL_TypeFormatString = {
  0,{ NdrFcShort(0x0),0x11,0x10,NdrFcShort(0x2),0x2f,0x5a,NdrFcLong(0xc),NdrFcShort(0x0),NdrFcShort(0x0),0xc0,0x0,0x0,0x0,0x0,0x0,0x0,0x46,0x11,0x14,NdrFcShort(0x2),0x2f,0x5a,NdrFcLong(0xe126f8fe),NdrFcShort(0xa7af),NdrFcShort(0x11d0),0xb8,0x8a,0x0,0xc0,0x4f,0xd4,0x24,0xb9,0x11,0x8,0x8,0x5c,0x11,0x8,0x2,0x5c,0x11,0x0,NdrFcShort(0x2),0x15,0x3,NdrFcShort(0x14),0x8,0x8,0x8,0x8,0x8,0x5b,0x11,0x10,NdrFcShort(0xffffffd6),0x11,0x14,NdrFcShort(0x2),0x13,0x0,NdrFcShort(0x50),0x13,0x0,NdrFcShort(0x2),0x2a,0x88,NdrFcShort(0x8),NdrFcShort(0x3),NdrFcLong(0x48746457),NdrFcShort(0x8008),NdrFcLong(0x52746457),NdrFcShort(0xa),NdrFcLong(0x50746457),NdrFcShort(0x800b),NdrFcShort(0xffffffff),0x13,0x0,NdrFcShort(0xe),0x1b,0x0,NdrFcShort(0x1),0x9,0x0,NdrFcShort(0xfffc),NdrFcShort(0x1),0x1,0x5b,0x17,0x3,NdrFcShort(0x8),NdrFcShort(0xfffffff0),0x8,0x8,0x5c,0x5b,0xb4,0x83,NdrFcShort(0x0),NdrFcShort(0x4),NdrFcShort(0x0),NdrFcShort(0xffffffc0),0x1b,0x0,NdrFcShort(0x1),0x19,0x0,NdrFcShort(0x4),NdrFcShort(0x1),0x2,0x5b,0x1a,0x3,NdrFcShort(0xc),NdrFcShort(0x0),NdrFcShort(0xa),0x4c,0x0,NdrFcShort(0xffffffe0),0x8,0x36,0x5c,0x5b,0x13,0x0,NdrFcShort(0xffffffe2),0x11,0x4,NdrFcShort(0x3f2),0x13,0x10,NdrFcShort(0x2),0x13,0x0,NdrFcShort(0x3d8),0x2a,0x49,NdrFcShort(0x18),NdrFcShort(0xa),NdrFcLong(0x8),NdrFcShort(0x70),NdrFcLong(0xd),NdrFcShort(0xa6),NdrFcLong(0x9),NdrFcShort(0xd8),NdrFcLong(0xc),NdrFcShort(0x2bc),NdrFcLong(0x24),NdrFcShort(0x2e6),NdrFcLong(0x800d),NdrFcShort(0x302),NdrFcLong(0x10),NdrFcShort(0x31c),NdrFcLong(0x2),NdrFcShort(0x336),NdrFcLong(0x3),NdrFcShort(0x350),NdrFcLong(0x14),NdrFcShort(0x36a),NdrFcShort(0xffffffff),0x1b,0x1,NdrFcShort(0x2),0x9,0x0,NdrFcShort(0xfffc),NdrFcShort(0x1),0x6,0x5b,0x17,0x3,NdrFcShort(0x8),NdrFcShort(0xfffffff0),0x8,0x8,0x5c,0x5b,0x1b,0x3,NdrFcShort(0x4),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),0x4b,0x5c,0x48,0x49,NdrFcShort(0x4),NdrFcShort(0x0),NdrFcShort(0x1),NdrFcShort(0x0),NdrFcShort(0x0),0x13,0x0,NdrFcShort(0xffffffdc),0x5b,0x8,0x5c,0x5b,0x16,0x3,NdrFcShort(0x8),0x4b,0x5c,0x46,0x5c,NdrFcShort(0x4),NdrFcShort(0x4),0x11,0x0,NdrFcShort(0xffffffd2),0x5b,0x8,0x8,0x5b,0x2f,0x5a,NdrFcLong(0x0),NdrFcShort(0x0),NdrFcShort(0x0),0xc0,0x0,0x0,0x0,0x0,0x0,0x0,0x46,0x21,0x3,NdrFcShort(0x0),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),NdrFcLong(0xffffffff),NdrFcShort(0x0),0x4c,0x0,NdrFcShort(0xffffffdc),0x5c,0x5b,0x1a,0x3,NdrFcShort(0x8),NdrFcShort(0x0),NdrFcShort(0x6),0x8,0x36,0x5c,0x5b,0x11,0x0,NdrFcShort(0xffffffdc),0x2f,0x5a,NdrFcLong(0x20400),NdrFcShort(0x0),NdrFcShort(0x0),0xc0,0x0,0x0,0x0,0x0,0x0,0x0,0x46,0x21,0x3,NdrFcShort(0x0),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),NdrFcLong(0xffffffff),NdrFcShort(0x0),0x4c,0x0,NdrFcShort(0xffffffdc),0x5c,0x5b,0x1a,0x3,NdrFcShort(0x8),NdrFcShort(0x0),NdrFcShort(0x6),0x8,0x36,0x5c,0x5b,0x11,0x0,NdrFcShort(0xffffffdc),0x2b,0x9,0x7,0x0,NdrFcShort(0xfff8),NdrFcShort(0x1),NdrFcShort(0x2),NdrFcShort(0x10),NdrFcShort(0x2b),NdrFcLong(0x3),NdrFcShort(0x8008),NdrFcLong(0x11),NdrFcShort(0x8001),NdrFcLong(0x2),NdrFcShort(0x8006),NdrFcLong(0x4),NdrFcShort(0x800a),NdrFcLong(0x5),NdrFcShort(0x800c),NdrFcLong(0xb),NdrFcShort(0x8006),NdrFcLong(0xa),NdrFcShort(0x8008),NdrFcLong(0x6),NdrFcShort(0xd6),NdrFcLong(0x7),NdrFcShort(0x800c),NdrFcLong(0x8),NdrFcShort(0xd0),NdrFcLong(0xd),NdrFcShort(0xffffff42),NdrFcLong(0x9),NdrFcShort(0xffffff74),NdrFcLong(0x2000),NdrFcShort(0xc2),NdrFcLong(0x24),NdrFcShort(0xc4),NdrFcLong(0x4024),NdrFcShort(0xbe),NdrFcLong(0x4011),NdrFcShort(0xee),NdrFcLong(0x4002),NdrFcShort(0xec),NdrFcLong(0x4003),NdrFcShort(0xea),NdrFcLong(0x4004),NdrFcShort(0xe8),NdrFcLong(0x4005),NdrFcShort(0xe6),NdrFcLong(0x400b),NdrFcShort(0xd4),NdrFcLong(0x400a),NdrFcShort(0xd2),NdrFcLong(0x4006),NdrFcShort(0xd8),NdrFcLong(0x4007),NdrFcShort(0xce),NdrFcLong(0x4008),NdrFcShort(0xd0),NdrFcLong(0x400d),NdrFcShort(0xce),NdrFcLong(0x4009),NdrFcShort(0xcc),NdrFcLong(0x6000),NdrFcShort(0xca),NdrFcLong(0x400c),NdrFcShort(0xd0),NdrFcLong(0x10),NdrFcShort(0x8002),NdrFcLong(0x12),NdrFcShort(0x8006),NdrFcLong(0x13),NdrFcShort(0x8008),NdrFcLong(0x16),NdrFcShort(0x8008),NdrFcLong(0x17),NdrFcShort(0x8008),NdrFcLong(0xe),NdrFcShort(0xb4),NdrFcLong(0x400e),NdrFcShort(0xba),NdrFcLong(0x4010),NdrFcShort(0xb8),NdrFcLong(0x4012),NdrFcShort(0x6e),NdrFcLong(0x4013),NdrFcShort(0x6c),NdrFcLong(0x4016),NdrFcShort(0x66),NdrFcLong(0x4017),NdrFcShort(0x60),NdrFcLong(0x0),NdrFcShort(0x0),NdrFcLong(0x1),NdrFcShort(0x0),NdrFcShort(0xffffffff),0x15,0x7,NdrFcShort(0x8),0xb,0x5b,0x13,0x0,NdrFcShort(0xfffffe38),0x13,0x10,NdrFcShort(0x2),0x13,0x0,NdrFcShort(0x1b6),0x13,0x0,NdrFcShort(0x20),0x2f,0x5a,NdrFcLong(0x2f),NdrFcShort(0x0),NdrFcShort(0x0),0xc0,0x0,0x0,0x0,0x0,0x0,0x0,0x46,0x1b,0x0,NdrFcShort(0x1),0x19,0x0,NdrFcShort(0x4),NdrFcShort(0x1),0x1,0x5b,0x1a,0x3,NdrFcShort(0x10),NdrFcShort(0x0),NdrFcShort(0xa),0x8,0x8,0x4c,0x0,NdrFcShort(0xffffffd6),0x36,0x5b,0x13,0x0,NdrFcShort(0xffffffe2),0x13,0x8,0x1,0x5c,0x13,0x8,0x6,0x5c,0x13,0x8,0x8,0x5c,0x13,0x8,0xa,0x5c,0x13,0x8,0xc,0x5c,0x13,0x0,NdrFcShort(0xffffffa2),0x13,0x10,NdrFcShort(0xffffffa4),0x13,0x10,NdrFcShort(0xfffffe18),0x13,0x10,NdrFcShort(0xfffffe4c),0x13,0x10,NdrFcShort(0x2),0x13,0x10,NdrFcShort(0x2),0x13,0x0,NdrFcShort(0x150),0x13,0x10,NdrFcShort(0x2),0x13,0x0,NdrFcShort(0x16),0x15,0x7,NdrFcShort(0x10),0x6,0x1,0x1,0x38,0x8,0x39,0xb,0x5b,0x13,0x0,NdrFcShort(0xfffffff2),0x13,0x8,0x2,0x5c,0x1a,0x7,NdrFcShort(0x20),NdrFcShort(0x0),NdrFcShort(0x0),0x8,0x8,0x6,0x6,0x6,0x6,0x4c,0x0,NdrFcShort(0xfffffe4a),0x5c,0x5b,0x1b,0x3,NdrFcShort(0x4),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),0x4b,0x5c,0x48,0x49,NdrFcShort(0x4),NdrFcShort(0x0),NdrFcShort(0x1),NdrFcShort(0x0),NdrFcShort(0x0),0x13,0x0,NdrFcShort(0xffffffd2),0x5b,0x8,0x5c,0x5b,0x1a,0x3,NdrFcShort(0x8),NdrFcShort(0x0),NdrFcShort(0x6),0x8,0x36,0x5c,0x5b,0x11,0x0,NdrFcShort(0xffffffd2),0x1b,0x3,NdrFcShort(0x4),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),0x4b,0x5c,0x48,0x49,NdrFcShort(0x4),NdrFcShort(0x0),NdrFcShort(0x1),NdrFcShort(0x0),NdrFcShort(0x0),0x13,0x0,NdrFcShort(0xffffff42),0x5b,0x8,0x5c,0x5b,0x1a,0x3,NdrFcShort(0x8),NdrFcShort(0x0),NdrFcShort(0x6),0x8,0x36,0x5c,0x5b,0x11,0x0,NdrFcShort(0xffffffd2),0x1d,0x0,NdrFcShort(0x8),0x2,0x5b,0x15,0x3,NdrFcShort(0x10),0x8,0x6,0x6,0x4c,0x0,NdrFcShort(0xfffffff1),0x5b,0x1a,0x3,NdrFcShort(0x18),NdrFcShort(0x0),NdrFcShort(0xa),0x8,0x36,0x4c,0x0,NdrFcShort(0xffffffe8),0x5c,0x5b,0x11,0x0,NdrFcShort(0xfffffd64),0x1b,0x0,NdrFcShort(0x1),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),0x1,0x5b,0x16,0x3,NdrFcShort(0x8),0x4b,0x5c,0x46,0x5c,NdrFcShort(0x4),NdrFcShort(0x4),0x13,0x0,NdrFcShort(0xffffffe6),0x5b,0x8,0x8,0x5b,0x1b,0x1,NdrFcShort(0x2),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),0x6,0x5b,0x16,0x3,NdrFcShort(0x8),0x4b,0x5c,0x46,0x5c,NdrFcShort(0x4),NdrFcShort(0x4),0x13,0x0,NdrFcShort(0xffffffe6),0x5b,0x8,0x8,0x5b,0x1b,0x3,NdrFcShort(0x4),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),0x8,0x5b,0x16,0x3,NdrFcShort(0x8),0x4b,0x5c,0x46,0x5c,NdrFcShort(0x4),NdrFcShort(0x4),0x13,0x0,NdrFcShort(0xffffffe6),0x5b,0x8,0x8,0x5b,0x1b,0x7,NdrFcShort(0x8),0x19,0x0,NdrFcShort(0x0),NdrFcShort(0x1),0xb,0x5b,0x16,0x3,NdrFcShort(0x8),0x4b,0x5c,0x46,0x5c,NdrFcShort(0x4),NdrFcShort(0x4),0x13,0x0,NdrFcShort(0xffffffe6),0x5b,0x8,0x8,0x5b,0x15,0x3,NdrFcShort(0x8),0x8,0x8,0x5c,0x5b,0x1b,0x3,NdrFcShort(0x8),0x7,0x0,NdrFcShort(0xffd8),NdrFcShort(0x1),0x4c,0x0,NdrFcShort(0xffffffec),0x5c,0x5b,0x1a,0x3,NdrFcShort(0x28),NdrFcShort(0xffffffec),NdrFcShort(0x0),0x6,0x6,0x38,0x8,0x8,0x4c,0x0,NdrFcShort(0xfffffc1b),0x5b,0xb4,0x83,NdrFcShort(0x1),NdrFcShort(0x4),NdrFcShort(0x0),NdrFcShort(0xfffffc08),0x12,0x10,NdrFcShort(0x2),0x12,0x0,NdrFcShort(0xffffffde),0xb4,0x83,NdrFcShort(0x1),NdrFcShort(0x4),NdrFcShort(0x0),NdrFcShort(0xfffffff0),0x11,0x14,NdrFcShort(0xfffffe9a),0x11,0x4,NdrFcShort(0xfffffe06),0x11,0xc,0x8,0x5c,0x11,0xc,0x2,0x5c,0x11,0xc,0x6,0x5c,0x11,0xc,0xd,0x5c,0x11,0x14,NdrFcShort(0x2),0x2f,0x5a,NdrFcLong(0xd5778ae3),NdrFcShort(0x43de),NdrFcShort(0x11d0),0x91,0x71,0x0,0xaa,0x0,0xc1,0x80,0x68,0x11,0xc,0xe,0x5c,0x12,0x0,NdrFcShort(0xfffffc18),0xb4,0x83,NdrFcShort(0x2),NdrFcShort(0x4),NdrFcShort(0x0),NdrFcShort(0xfffffff4),0x11,0x14,NdrFcShort(0x2),0x13,0x0,NdrFcShort(0xfffffed4),0x11,0x14,NdrFcShort(0x2),0x13,0x0,NdrFcShort(0x2),0x15,0x3,NdrFcShort(0x20),0x8,0x8,0xe,0xe,0x8,0x8,0x8,0x8,0x5c,0x5b,0x0 }
};

const CInterfaceProxyVtbl *_scardssp_ProxyVtblList[] = {
  (CInterfaceProxyVtbl *) &_ISCardTypeConvProxyVtbl,(CInterfaceProxyVtbl *) &_ISCardISO7816ProxyVtbl,(CInterfaceProxyVtbl *) &_ISCardProxyVtbl,(CInterfaceProxyVtbl *) &_ISCardDatabaseProxyVtbl,(CInterfaceProxyVtbl *) &_ISCardLocateProxyVtbl,(CInterfaceProxyVtbl *) &_ISCardCmdProxyVtbl,(CInterfaceProxyVtbl *) &_IByteBufferProxyVtbl,0
};

const CInterfaceStubVtbl *_scardssp_StubVtblList[] = {
  (CInterfaceStubVtbl *) &_ISCardTypeConvStubVtbl,(CInterfaceStubVtbl *) &_ISCardISO7816StubVtbl,(CInterfaceStubVtbl *) &_ISCardStubVtbl,(CInterfaceStubVtbl *) &_ISCardDatabaseStubVtbl,(CInterfaceStubVtbl *) &_ISCardLocateStubVtbl,(CInterfaceStubVtbl *) &_ISCardCmdStubVtbl,(CInterfaceStubVtbl *) &_IByteBufferStubVtbl,0
};

PCInterfaceName const _scardssp_InterfaceNamesList[] = {
  "ISCardTypeConv","ISCardISO7816","ISCard","ISCardDatabase","ISCardLocate","ISCardCmd","IByteBuffer",0
};

const IID *_scardssp_BaseIIDList[] = {
  &IID_IDispatch,&IID_IDispatch,&IID_IDispatch,&IID_IDispatch,&IID_IDispatch,&IID_IDispatch,&IID_IDispatch,0
};

#define _scardssp_CHECK_IID(n) IID_GENERIC_CHECK_IID(_scardssp,pIID,n)

int __stdcall _scardssp_IID_Lookup(const IID *pIID,int *pIndex) {
  IID_BS_LOOKUP_SETUP
    IID_BS_LOOKUP_INITIAL_TEST(_scardssp,7,4)
    IID_BS_LOOKUP_NEXT_TEST(_scardssp,2)
    IID_BS_LOOKUP_NEXT_TEST(_scardssp,1)
    IID_BS_LOOKUP_RETURN_RESULT(_scardssp,7,*pIndex)
}

const ExtendedProxyFileInfo scardssp_ProxyFileInfo = {
  (PCInterfaceProxyVtblList *) & _scardssp_ProxyVtblList,(PCInterfaceStubVtblList *) & _scardssp_StubVtblList,(const PCInterfaceName *) & _scardssp_InterfaceNamesList,(const IID **) & _scardssp_BaseIIDList,& _scardssp_IID_Lookup,7,2,0,0,0,0
};
