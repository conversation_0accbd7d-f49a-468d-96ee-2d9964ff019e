/*** Autogenerated by WIDL 10.8 from include/ocidl.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __ocidl_h__
#define __ocidl_h__

/* Forward declarations */

#ifndef __IEnumConnections_FWD_DEFINED__
#define __IEnumConnections_FWD_DEFINED__
typedef interface IEnumConnections IEnumConnections;
#ifdef __cplusplus
interface IEnumConnections;
#endif /* __cplusplus */
#endif

#ifndef __IConnectionPoint_FWD_DEFINED__
#define __IConnectionPoint_FWD_DEFINED__
typedef interface IConnectionPoint IConnectionPoint;
#ifdef __cplusplus
interface IConnectionPoint;
#endif /* __cplusplus */
#endif

#ifndef __IEnumConnectionPoints_FWD_DEFINED__
#define __IEnumConnectionPoints_FWD_DEFINED__
typedef interface IEnumConnectionPoints IEnumConnectionPoints;
#ifdef __cplusplus
interface IEnumConnectionPoints;
#endif /* __cplusplus */
#endif

#ifndef __IConnectionPointContainer_FWD_DEFINED__
#define __IConnectionPointContainer_FWD_DEFINED__
typedef interface IConnectionPointContainer IConnectionPointContainer;
#ifdef __cplusplus
interface IConnectionPointContainer;
#endif /* __cplusplus */
#endif

#ifndef __IClassFactory2_FWD_DEFINED__
#define __IClassFactory2_FWD_DEFINED__
typedef interface IClassFactory2 IClassFactory2;
#ifdef __cplusplus
interface IClassFactory2;
#endif /* __cplusplus */
#endif

#ifndef __IProvideClassInfo_FWD_DEFINED__
#define __IProvideClassInfo_FWD_DEFINED__
typedef interface IProvideClassInfo IProvideClassInfo;
#ifdef __cplusplus
interface IProvideClassInfo;
#endif /* __cplusplus */
#endif

#ifndef __IProvideClassInfo2_FWD_DEFINED__
#define __IProvideClassInfo2_FWD_DEFINED__
typedef interface IProvideClassInfo2 IProvideClassInfo2;
#ifdef __cplusplus
interface IProvideClassInfo2;
#endif /* __cplusplus */
#endif

#ifndef __IProvideMultipleClassInfo_FWD_DEFINED__
#define __IProvideMultipleClassInfo_FWD_DEFINED__
typedef interface IProvideMultipleClassInfo IProvideMultipleClassInfo;
#ifdef __cplusplus
interface IProvideMultipleClassInfo;
#endif /* __cplusplus */
#endif

#ifndef __IOleControl_FWD_DEFINED__
#define __IOleControl_FWD_DEFINED__
typedef interface IOleControl IOleControl;
#ifdef __cplusplus
interface IOleControl;
#endif /* __cplusplus */
#endif

#ifndef __IOleControlSite_FWD_DEFINED__
#define __IOleControlSite_FWD_DEFINED__
typedef interface IOleControlSite IOleControlSite;
#ifdef __cplusplus
interface IOleControlSite;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyPage_FWD_DEFINED__
#define __IPropertyPage_FWD_DEFINED__
typedef interface IPropertyPage IPropertyPage;
#ifdef __cplusplus
interface IPropertyPage;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyPage2_FWD_DEFINED__
#define __IPropertyPage2_FWD_DEFINED__
typedef interface IPropertyPage2 IPropertyPage2;
#ifdef __cplusplus
interface IPropertyPage2;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyPageSite_FWD_DEFINED__
#define __IPropertyPageSite_FWD_DEFINED__
typedef interface IPropertyPageSite IPropertyPageSite;
#ifdef __cplusplus
interface IPropertyPageSite;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyNotifySink_FWD_DEFINED__
#define __IPropertyNotifySink_FWD_DEFINED__
typedef interface IPropertyNotifySink IPropertyNotifySink;
#ifdef __cplusplus
interface IPropertyNotifySink;
#endif /* __cplusplus */
#endif

#ifndef __ISpecifyPropertyPages_FWD_DEFINED__
#define __ISpecifyPropertyPages_FWD_DEFINED__
typedef interface ISpecifyPropertyPages ISpecifyPropertyPages;
#ifdef __cplusplus
interface ISpecifyPropertyPages;
#endif /* __cplusplus */
#endif

#ifndef __IPersistMemory_FWD_DEFINED__
#define __IPersistMemory_FWD_DEFINED__
typedef interface IPersistMemory IPersistMemory;
#ifdef __cplusplus
interface IPersistMemory;
#endif /* __cplusplus */
#endif

#ifndef __IPersistStreamInit_FWD_DEFINED__
#define __IPersistStreamInit_FWD_DEFINED__
typedef interface IPersistStreamInit IPersistStreamInit;
#ifdef __cplusplus
interface IPersistStreamInit;
#endif /* __cplusplus */
#endif

#ifndef __IPersistPropertyBag_FWD_DEFINED__
#define __IPersistPropertyBag_FWD_DEFINED__
typedef interface IPersistPropertyBag IPersistPropertyBag;
#ifdef __cplusplus
interface IPersistPropertyBag;
#endif /* __cplusplus */
#endif

#ifndef __ISimpleFrameSite_FWD_DEFINED__
#define __ISimpleFrameSite_FWD_DEFINED__
typedef interface ISimpleFrameSite ISimpleFrameSite;
#ifdef __cplusplus
interface ISimpleFrameSite;
#endif /* __cplusplus */
#endif

#ifndef __IFont_FWD_DEFINED__
#define __IFont_FWD_DEFINED__
typedef interface IFont IFont;
#ifdef __cplusplus
interface IFont;
#endif /* __cplusplus */
#endif

#ifndef __IPicture_FWD_DEFINED__
#define __IPicture_FWD_DEFINED__
typedef interface IPicture IPicture;
#ifdef __cplusplus
interface IPicture;
#endif /* __cplusplus */
#endif

#ifndef __IPicture2_FWD_DEFINED__
#define __IPicture2_FWD_DEFINED__
typedef interface IPicture2 IPicture2;
#ifdef __cplusplus
interface IPicture2;
#endif /* __cplusplus */
#endif

#ifndef __IFontEventsDisp_FWD_DEFINED__
#define __IFontEventsDisp_FWD_DEFINED__
typedef interface IFontEventsDisp IFontEventsDisp;
#ifdef __cplusplus
interface IFontEventsDisp;
#endif /* __cplusplus */
#endif

#ifndef __IFontDisp_FWD_DEFINED__
#define __IFontDisp_FWD_DEFINED__
typedef interface IFontDisp IFontDisp;
#ifdef __cplusplus
interface IFontDisp;
#endif /* __cplusplus */
#endif

#ifndef __IPictureDisp_FWD_DEFINED__
#define __IPictureDisp_FWD_DEFINED__
typedef interface IPictureDisp IPictureDisp;
#ifdef __cplusplus
interface IPictureDisp;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceObjectWindowless_FWD_DEFINED__
#define __IOleInPlaceObjectWindowless_FWD_DEFINED__
typedef interface IOleInPlaceObjectWindowless IOleInPlaceObjectWindowless;
#ifdef __cplusplus
interface IOleInPlaceObjectWindowless;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceSiteEx_FWD_DEFINED__
#define __IOleInPlaceSiteEx_FWD_DEFINED__
typedef interface IOleInPlaceSiteEx IOleInPlaceSiteEx;
#ifdef __cplusplus
interface IOleInPlaceSiteEx;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceSiteWindowless_FWD_DEFINED__
#define __IOleInPlaceSiteWindowless_FWD_DEFINED__
typedef interface IOleInPlaceSiteWindowless IOleInPlaceSiteWindowless;
#ifdef __cplusplus
interface IOleInPlaceSiteWindowless;
#endif /* __cplusplus */
#endif

#ifndef __IViewObjectEx_FWD_DEFINED__
#define __IViewObjectEx_FWD_DEFINED__
typedef interface IViewObjectEx IViewObjectEx;
#ifdef __cplusplus
interface IViewObjectEx;
#endif /* __cplusplus */
#endif

#ifndef __IOleUndoUnit_FWD_DEFINED__
#define __IOleUndoUnit_FWD_DEFINED__
typedef interface IOleUndoUnit IOleUndoUnit;
#ifdef __cplusplus
interface IOleUndoUnit;
#endif /* __cplusplus */
#endif

#ifndef __IOleParentUndoUnit_FWD_DEFINED__
#define __IOleParentUndoUnit_FWD_DEFINED__
typedef interface IOleParentUndoUnit IOleParentUndoUnit;
#ifdef __cplusplus
interface IOleParentUndoUnit;
#endif /* __cplusplus */
#endif

#ifndef __IEnumOleUndoUnits_FWD_DEFINED__
#define __IEnumOleUndoUnits_FWD_DEFINED__
typedef interface IEnumOleUndoUnits IEnumOleUndoUnits;
#ifdef __cplusplus
interface IEnumOleUndoUnits;
#endif /* __cplusplus */
#endif

#ifndef __IOleUndoManager_FWD_DEFINED__
#define __IOleUndoManager_FWD_DEFINED__
typedef interface IOleUndoManager IOleUndoManager;
#ifdef __cplusplus
interface IOleUndoManager;
#endif /* __cplusplus */
#endif

#ifndef __IPointerInactive_FWD_DEFINED__
#define __IPointerInactive_FWD_DEFINED__
typedef interface IPointerInactive IPointerInactive;
#ifdef __cplusplus
interface IPointerInactive;
#endif /* __cplusplus */
#endif

#ifndef __IObjectWithSite_FWD_DEFINED__
#define __IObjectWithSite_FWD_DEFINED__
typedef interface IObjectWithSite IObjectWithSite;
#ifdef __cplusplus
interface IObjectWithSite;
#endif /* __cplusplus */
#endif

#ifndef __IPerPropertyBrowsing_FWD_DEFINED__
#define __IPerPropertyBrowsing_FWD_DEFINED__
typedef interface IPerPropertyBrowsing IPerPropertyBrowsing;
#ifdef __cplusplus
interface IPerPropertyBrowsing;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyBag2_FWD_DEFINED__
#define __IPropertyBag2_FWD_DEFINED__
typedef interface IPropertyBag2 IPropertyBag2;
#ifdef __cplusplus
interface IPropertyBag2;
#endif /* __cplusplus */
#endif

#ifndef __IPersistPropertyBag2_FWD_DEFINED__
#define __IPersistPropertyBag2_FWD_DEFINED__
typedef interface IPersistPropertyBag2 IPersistPropertyBag2;
#ifdef __cplusplus
interface IPersistPropertyBag2;
#endif /* __cplusplus */
#endif

#ifndef __IAdviseSinkEx_FWD_DEFINED__
#define __IAdviseSinkEx_FWD_DEFINED__
typedef interface IAdviseSinkEx IAdviseSinkEx;
#ifdef __cplusplus
interface IAdviseSinkEx;
#endif /* __cplusplus */
#endif

#ifndef __IQuickActivate_FWD_DEFINED__
#define __IQuickActivate_FWD_DEFINED__
typedef interface IQuickActivate IQuickActivate;
#ifdef __cplusplus
interface IQuickActivate;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oleidl.h>
#include <oaidl.h>
#include <oleidl.h>
#include <servprov.h>
#include <urlmon.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>


#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#ifndef __IEnumConnections_FWD_DEFINED__
#define __IEnumConnections_FWD_DEFINED__
typedef interface IEnumConnections IEnumConnections;
#ifdef __cplusplus
interface IEnumConnections;
#endif /* __cplusplus */
#endif

#ifndef __IEnumConnectionPoints_FWD_DEFINED__
#define __IEnumConnectionPoints_FWD_DEFINED__
typedef interface IEnumConnectionPoints IEnumConnectionPoints;
#ifdef __cplusplus
interface IEnumConnectionPoints;
#endif /* __cplusplus */
#endif

#ifndef __IConnectionPoint_FWD_DEFINED__
#define __IConnectionPoint_FWD_DEFINED__
typedef interface IConnectionPoint IConnectionPoint;
#ifdef __cplusplus
interface IConnectionPoint;
#endif /* __cplusplus */
#endif

#ifndef __IConnectionPointContainer_FWD_DEFINED__
#define __IConnectionPointContainer_FWD_DEFINED__
typedef interface IConnectionPointContainer IConnectionPointContainer;
#ifdef __cplusplus
interface IConnectionPointContainer;
#endif /* __cplusplus */
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IBindHost_FWD_DEFINED__
#define __IBindHost_FWD_DEFINED__
typedef interface IBindHost IBindHost;
#ifdef __cplusplus
interface IBindHost;
#endif /* __cplusplus */
#endif

#ifndef __IClassFactory2_FWD_DEFINED__
#define __IClassFactory2_FWD_DEFINED__
typedef interface IClassFactory2 IClassFactory2;
#ifdef __cplusplus
interface IClassFactory2;
#endif /* __cplusplus */
#endif

#ifndef __IDropTarget_FWD_DEFINED__
#define __IDropTarget_FWD_DEFINED__
typedef interface IDropTarget IDropTarget;
#ifdef __cplusplus
interface IDropTarget;
#endif /* __cplusplus */
#endif

#ifndef __IProvideClassInfo_FWD_DEFINED__
#define __IProvideClassInfo_FWD_DEFINED__
typedef interface IProvideClassInfo IProvideClassInfo;
#ifdef __cplusplus
interface IProvideClassInfo;
#endif /* __cplusplus */
#endif

#ifndef __IProvideClassInfo2_FWD_DEFINED__
#define __IProvideClassInfo2_FWD_DEFINED__
typedef interface IProvideClassInfo2 IProvideClassInfo2;
#ifdef __cplusplus
interface IProvideClassInfo2;
#endif /* __cplusplus */
#endif

#ifndef __IProvideMultipleClassInfo_FWD_DEFINED__
#define __IProvideMultipleClassInfo_FWD_DEFINED__
typedef interface IProvideMultipleClassInfo IProvideMultipleClassInfo;
#ifdef __cplusplus
interface IProvideMultipleClassInfo;
#endif /* __cplusplus */
#endif

#ifndef __IOleClientSite_FWD_DEFINED__
#define __IOleClientSite_FWD_DEFINED__
typedef interface IOleClientSite IOleClientSite;
#ifdef __cplusplus
interface IOleClientSite;
#endif /* __cplusplus */
#endif

#ifndef __IOleControl_FWD_DEFINED__
#define __IOleControl_FWD_DEFINED__
typedef interface IOleControl IOleControl;
#ifdef __cplusplus
interface IOleControl;
#endif /* __cplusplus */
#endif

#ifndef __IOleControlSite_FWD_DEFINED__
#define __IOleControlSite_FWD_DEFINED__
typedef interface IOleControlSite IOleControlSite;
#ifdef __cplusplus
interface IOleControlSite;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyPage_FWD_DEFINED__
#define __IPropertyPage_FWD_DEFINED__
typedef interface IPropertyPage IPropertyPage;
#ifdef __cplusplus
interface IPropertyPage;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyPage2_FWD_DEFINED__
#define __IPropertyPage2_FWD_DEFINED__
typedef interface IPropertyPage2 IPropertyPage2;
#ifdef __cplusplus
interface IPropertyPage2;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyPageSite_FWD_DEFINED__
#define __IPropertyPageSite_FWD_DEFINED__
typedef interface IPropertyPageSite IPropertyPageSite;
#ifdef __cplusplus
interface IPropertyPageSite;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyNotifySink_FWD_DEFINED__
#define __IPropertyNotifySink_FWD_DEFINED__
typedef interface IPropertyNotifySink IPropertyNotifySink;
#ifdef __cplusplus
interface IPropertyNotifySink;
#endif /* __cplusplus */
#endif

#ifndef __ISpecifyPropertyPages_FWD_DEFINED__
#define __ISpecifyPropertyPages_FWD_DEFINED__
typedef interface ISpecifyPropertyPages ISpecifyPropertyPages;
#ifdef __cplusplus
interface ISpecifyPropertyPages;
#endif /* __cplusplus */
#endif

#ifndef __IPersistMemory_FWD_DEFINED__
#define __IPersistMemory_FWD_DEFINED__
typedef interface IPersistMemory IPersistMemory;
#ifdef __cplusplus
interface IPersistMemory;
#endif /* __cplusplus */
#endif

#ifndef __IPersistStreamInit_FWD_DEFINED__
#define __IPersistStreamInit_FWD_DEFINED__
typedef interface IPersistStreamInit IPersistStreamInit;
#ifdef __cplusplus
interface IPersistStreamInit;
#endif /* __cplusplus */
#endif

#ifndef __IPersistPropertyBag_FWD_DEFINED__
#define __IPersistPropertyBag_FWD_DEFINED__
typedef interface IPersistPropertyBag IPersistPropertyBag;
#ifdef __cplusplus
interface IPersistPropertyBag;
#endif /* __cplusplus */
#endif

#ifndef __ISimpleFrameSite_FWD_DEFINED__
#define __ISimpleFrameSite_FWD_DEFINED__
typedef interface ISimpleFrameSite ISimpleFrameSite;
#ifdef __cplusplus
interface ISimpleFrameSite;
#endif /* __cplusplus */
#endif

#ifndef __IFont_FWD_DEFINED__
#define __IFont_FWD_DEFINED__
typedef interface IFont IFont;
#ifdef __cplusplus
interface IFont;
#endif /* __cplusplus */
#endif

#ifndef __IPicture_FWD_DEFINED__
#define __IPicture_FWD_DEFINED__
typedef interface IPicture IPicture;
#ifdef __cplusplus
interface IPicture;
#endif /* __cplusplus */
#endif

#ifndef __IFontEventsDisp_FWD_DEFINED__
#define __IFontEventsDisp_FWD_DEFINED__
typedef interface IFontEventsDisp IFontEventsDisp;
#ifdef __cplusplus
interface IFontEventsDisp;
#endif /* __cplusplus */
#endif

#ifndef __IFontDisp_FWD_DEFINED__
#define __IFontDisp_FWD_DEFINED__
typedef interface IFontDisp IFontDisp;
#ifdef __cplusplus
interface IFontDisp;
#endif /* __cplusplus */
#endif

#ifndef __IPictureDisp_FWD_DEFINED__
#define __IPictureDisp_FWD_DEFINED__
typedef interface IPictureDisp IPictureDisp;
#ifdef __cplusplus
interface IPictureDisp;
#endif /* __cplusplus */
#endif

#ifndef __IAdviseSinkEx_FWD_DEFINED__
#define __IAdviseSinkEx_FWD_DEFINED__
typedef interface IAdviseSinkEx IAdviseSinkEx;
#ifdef __cplusplus
interface IAdviseSinkEx;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceObjectWindowless_FWD_DEFINED__
#define __IOleInPlaceObjectWindowless_FWD_DEFINED__
typedef interface IOleInPlaceObjectWindowless IOleInPlaceObjectWindowless;
#ifdef __cplusplus
interface IOleInPlaceObjectWindowless;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceSite_FWD_DEFINED__
#define __IOleInPlaceSite_FWD_DEFINED__
typedef interface IOleInPlaceSite IOleInPlaceSite;
#ifdef __cplusplus
interface IOleInPlaceSite;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceSiteEx_FWD_DEFINED__
#define __IOleInPlaceSiteEx_FWD_DEFINED__
typedef interface IOleInPlaceSiteEx IOleInPlaceSiteEx;
#ifdef __cplusplus
interface IOleInPlaceSiteEx;
#endif /* __cplusplus */
#endif

#ifndef __IOleInPlaceSiteWindowless_FWD_DEFINED__
#define __IOleInPlaceSiteWindowless_FWD_DEFINED__
typedef interface IOleInPlaceSiteWindowless IOleInPlaceSiteWindowless;
#ifdef __cplusplus
interface IOleInPlaceSiteWindowless;
#endif /* __cplusplus */
#endif

#ifndef __IViewObject2_FWD_DEFINED__
#define __IViewObject2_FWD_DEFINED__
typedef interface IViewObject2 IViewObject2;
#ifdef __cplusplus
interface IViewObject2;
#endif /* __cplusplus */
#endif

#ifndef __IViewObjectEx_FWD_DEFINED__
#define __IViewObjectEx_FWD_DEFINED__
typedef interface IViewObjectEx IViewObjectEx;
#ifdef __cplusplus
interface IViewObjectEx;
#endif /* __cplusplus */
#endif

#ifndef __IOleUndoUnit_FWD_DEFINED__
#define __IOleUndoUnit_FWD_DEFINED__
typedef interface IOleUndoUnit IOleUndoUnit;
#ifdef __cplusplus
interface IOleUndoUnit;
#endif /* __cplusplus */
#endif

#ifndef __IOleParentUndoUnit_FWD_DEFINED__
#define __IOleParentUndoUnit_FWD_DEFINED__
typedef interface IOleParentUndoUnit IOleParentUndoUnit;
#ifdef __cplusplus
interface IOleParentUndoUnit;
#endif /* __cplusplus */
#endif

#ifndef __IEnumOleUndoUnits_FWD_DEFINED__
#define __IEnumOleUndoUnits_FWD_DEFINED__
typedef interface IEnumOleUndoUnits IEnumOleUndoUnits;
#ifdef __cplusplus
interface IEnumOleUndoUnits;
#endif /* __cplusplus */
#endif

#ifndef __IOleUndoManager_FWD_DEFINED__
#define __IOleUndoManager_FWD_DEFINED__
typedef interface IOleUndoManager IOleUndoManager;
#ifdef __cplusplus
interface IOleUndoManager;
#endif /* __cplusplus */
#endif

#ifndef __IPointerInactive_FWD_DEFINED__
#define __IPointerInactive_FWD_DEFINED__
typedef interface IPointerInactive IPointerInactive;
#ifdef __cplusplus
interface IPointerInactive;
#endif /* __cplusplus */
#endif

#ifndef __IObjectWithSite_FWD_DEFINED__
#define __IObjectWithSite_FWD_DEFINED__
typedef interface IObjectWithSite IObjectWithSite;
#ifdef __cplusplus
interface IObjectWithSite;
#endif /* __cplusplus */
#endif

#ifndef __IErrorLog_FWD_DEFINED__
#define __IErrorLog_FWD_DEFINED__
typedef interface IErrorLog IErrorLog;
#ifdef __cplusplus
interface IErrorLog;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyBag_FWD_DEFINED__
#define __IPropertyBag_FWD_DEFINED__
typedef interface IPropertyBag IPropertyBag;
#ifdef __cplusplus
interface IPropertyBag;
#endif /* __cplusplus */
#endif

#ifndef __IPerPropertyBrowsing_FWD_DEFINED__
#define __IPerPropertyBrowsing_FWD_DEFINED__
typedef interface IPerPropertyBrowsing IPerPropertyBrowsing;
#ifdef __cplusplus
interface IPerPropertyBrowsing;
#endif /* __cplusplus */
#endif

#ifndef __IPropertyBag2_FWD_DEFINED__
#define __IPropertyBag2_FWD_DEFINED__
typedef interface IPropertyBag2 IPropertyBag2;
#ifdef __cplusplus
interface IPropertyBag2;
#endif /* __cplusplus */
#endif

#ifndef __IPersistPropertyBag2_FWD_DEFINED__
#define __IPersistPropertyBag2_FWD_DEFINED__
typedef interface IPersistPropertyBag2 IPersistPropertyBag2;
#ifdef __cplusplus
interface IPersistPropertyBag2;
#endif /* __cplusplus */
#endif

#ifndef __IQuickActivate_FWD_DEFINED__
#define __IQuickActivate_FWD_DEFINED__
typedef interface IQuickActivate IQuickActivate;
#ifdef __cplusplus
interface IQuickActivate;
#endif /* __cplusplus */
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IOleControlTypes interface (v1.0)
 */
#ifndef __IOleControlTypes_INTERFACE_DEFINED__
#define __IOleControlTypes_INTERFACE_DEFINED__

extern RPC_IF_HANDLE IOleControlTypes_v1_0_c_ifspec;
extern RPC_IF_HANDLE IOleControlTypes_v1_0_s_ifspec;
typedef enum tagUASFLAGS {
    UAS_NORMAL = 0x0,
    UAS_BLOCKED = 0x1,
    UAS_NOPARENTENABLE = 0x2,
    UAS_MASK = 0x3
} UASFLAGS;

typedef enum tagREADYSTATE {
    READYSTATE_UNINITIALIZED = 0,
    READYSTATE_LOADING = 1,
    READYSTATE_LOADED = 2,
    READYSTATE_INTERACTIVE = 3,
    READYSTATE_COMPLETE = 4
} READYSTATE;

#endif  /* __IOleControlTypes_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IEnumConnections interface
 */
#ifndef __IEnumConnections_INTERFACE_DEFINED__
#define __IEnumConnections_INTERFACE_DEFINED__

typedef IEnumConnections *PENUMCONNECTIONS;
typedef IEnumConnections *LPENUMCONNECTIONS;
typedef struct tagCONNECTDATA {
    IUnknown *pUnk;
    DWORD dwCookie;
} CONNECTDATA;

typedef struct tagCONNECTDATA *PCONNECTDATA;
typedef struct tagCONNECTDATA *LPCONNECTDATA;

DEFINE_GUID(IID_IEnumConnections, 0xb196b287, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b287-bab4-101a-b69c-00aa00341d07")
IEnumConnections : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG cConnections,
        LPCONNECTDATA rgcd,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG cConnections) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumConnections **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumConnections, 0xb196b287, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IEnumConnectionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumConnections *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumConnections *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumConnections *This);

    /*** IEnumConnections methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumConnections *This,
        ULONG cConnections,
        LPCONNECTDATA rgcd,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumConnections *This,
        ULONG cConnections);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumConnections *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumConnections *This,
        IEnumConnections **ppEnum);

    END_INTERFACE
} IEnumConnectionsVtbl;

interface IEnumConnections {
    CONST_VTBL IEnumConnectionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumConnections_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumConnections_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumConnections_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumConnections methods ***/
#define IEnumConnections_Next(This,cConnections,rgcd,pcFetched) (This)->lpVtbl->Next(This,cConnections,rgcd,pcFetched)
#define IEnumConnections_Skip(This,cConnections) (This)->lpVtbl->Skip(This,cConnections)
#define IEnumConnections_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumConnections_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumConnections_QueryInterface(IEnumConnections* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumConnections_AddRef(IEnumConnections* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumConnections_Release(IEnumConnections* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumConnections methods ***/
static inline HRESULT IEnumConnections_Next(IEnumConnections* This,ULONG cConnections,LPCONNECTDATA rgcd,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,cConnections,rgcd,pcFetched);
}
static inline HRESULT IEnumConnections_Skip(IEnumConnections* This,ULONG cConnections) {
    return This->lpVtbl->Skip(This,cConnections);
}
static inline HRESULT IEnumConnections_Reset(IEnumConnections* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumConnections_Clone(IEnumConnections* This,IEnumConnections **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumConnections_RemoteNext_Proxy(
    IEnumConnections* This,
    ULONG cConnections,
    LPCONNECTDATA rgcd,
    ULONG *pcFetched);
void __RPC_STUB IEnumConnections_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumConnections_Next_Proxy(
    IEnumConnections* This,
    ULONG cConnections,
    LPCONNECTDATA rgcd,
    ULONG *pcFetched);
HRESULT __RPC_STUB IEnumConnections_Next_Stub(
    IEnumConnections* This,
    ULONG cConnections,
    LPCONNECTDATA rgcd,
    ULONG *pcFetched);

#endif  /* __IEnumConnections_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IConnectionPoint interface
 */
#ifndef __IConnectionPoint_INTERFACE_DEFINED__
#define __IConnectionPoint_INTERFACE_DEFINED__

typedef IConnectionPoint *PCONNECTIONPOINT;
typedef IConnectionPoint *LPCONNECTIONPOINT;

DEFINE_GUID(IID_IConnectionPoint, 0xb196b286, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b286-bab4-101a-b69c-00aa00341d07")
IConnectionPoint : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetConnectionInterface(
        IID *pIID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectionPointContainer(
        IConnectionPointContainer **ppCPC) = 0;

    virtual HRESULT STDMETHODCALLTYPE Advise(
        IUnknown *pUnkSink,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unadvise(
        DWORD dwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumConnections(
        IEnumConnections **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IConnectionPoint, 0xb196b286, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IConnectionPointVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IConnectionPoint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IConnectionPoint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IConnectionPoint *This);

    /*** IConnectionPoint methods ***/
    HRESULT (STDMETHODCALLTYPE *GetConnectionInterface)(
        IConnectionPoint *This,
        IID *pIID);

    HRESULT (STDMETHODCALLTYPE *GetConnectionPointContainer)(
        IConnectionPoint *This,
        IConnectionPointContainer **ppCPC);

    HRESULT (STDMETHODCALLTYPE *Advise)(
        IConnectionPoint *This,
        IUnknown *pUnkSink,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *Unadvise)(
        IConnectionPoint *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *EnumConnections)(
        IConnectionPoint *This,
        IEnumConnections **ppEnum);

    END_INTERFACE
} IConnectionPointVtbl;

interface IConnectionPoint {
    CONST_VTBL IConnectionPointVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IConnectionPoint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConnectionPoint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConnectionPoint_Release(This) (This)->lpVtbl->Release(This)
/*** IConnectionPoint methods ***/
#define IConnectionPoint_GetConnectionInterface(This,pIID) (This)->lpVtbl->GetConnectionInterface(This,pIID)
#define IConnectionPoint_GetConnectionPointContainer(This,ppCPC) (This)->lpVtbl->GetConnectionPointContainer(This,ppCPC)
#define IConnectionPoint_Advise(This,pUnkSink,pdwCookie) (This)->lpVtbl->Advise(This,pUnkSink,pdwCookie)
#define IConnectionPoint_Unadvise(This,dwCookie) (This)->lpVtbl->Unadvise(This,dwCookie)
#define IConnectionPoint_EnumConnections(This,ppEnum) (This)->lpVtbl->EnumConnections(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IConnectionPoint_QueryInterface(IConnectionPoint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IConnectionPoint_AddRef(IConnectionPoint* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IConnectionPoint_Release(IConnectionPoint* This) {
    return This->lpVtbl->Release(This);
}
/*** IConnectionPoint methods ***/
static inline HRESULT IConnectionPoint_GetConnectionInterface(IConnectionPoint* This,IID *pIID) {
    return This->lpVtbl->GetConnectionInterface(This,pIID);
}
static inline HRESULT IConnectionPoint_GetConnectionPointContainer(IConnectionPoint* This,IConnectionPointContainer **ppCPC) {
    return This->lpVtbl->GetConnectionPointContainer(This,ppCPC);
}
static inline HRESULT IConnectionPoint_Advise(IConnectionPoint* This,IUnknown *pUnkSink,DWORD *pdwCookie) {
    return This->lpVtbl->Advise(This,pUnkSink,pdwCookie);
}
static inline HRESULT IConnectionPoint_Unadvise(IConnectionPoint* This,DWORD dwCookie) {
    return This->lpVtbl->Unadvise(This,dwCookie);
}
static inline HRESULT IConnectionPoint_EnumConnections(IConnectionPoint* This,IEnumConnections **ppEnum) {
    return This->lpVtbl->EnumConnections(This,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IConnectionPoint_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumConnectionPoints interface
 */
#ifndef __IEnumConnectionPoints_INTERFACE_DEFINED__
#define __IEnumConnectionPoints_INTERFACE_DEFINED__

typedef IEnumConnectionPoints *PENUMCONNECTIONPOINTS;
typedef IEnumConnectionPoints *LPENUMCONNECTIONPOINTS;

DEFINE_GUID(IID_IEnumConnectionPoints, 0xb196b285, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b285-bab4-101a-b69c-00aa00341d07")
IEnumConnectionPoints : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG cConnections,
        LPCONNECTIONPOINT *ppCP,
        ULONG *pcFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG cConnections) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumConnectionPoints **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumConnectionPoints, 0xb196b285, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IEnumConnectionPointsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumConnectionPoints *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumConnectionPoints *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumConnectionPoints *This);

    /*** IEnumConnectionPoints methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumConnectionPoints *This,
        ULONG cConnections,
        LPCONNECTIONPOINT *ppCP,
        ULONG *pcFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumConnectionPoints *This,
        ULONG cConnections);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumConnectionPoints *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumConnectionPoints *This,
        IEnumConnectionPoints **ppEnum);

    END_INTERFACE
} IEnumConnectionPointsVtbl;

interface IEnumConnectionPoints {
    CONST_VTBL IEnumConnectionPointsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumConnectionPoints_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumConnectionPoints_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumConnectionPoints_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumConnectionPoints methods ***/
#define IEnumConnectionPoints_Next(This,cConnections,ppCP,pcFetched) (This)->lpVtbl->Next(This,cConnections,ppCP,pcFetched)
#define IEnumConnectionPoints_Skip(This,cConnections) (This)->lpVtbl->Skip(This,cConnections)
#define IEnumConnectionPoints_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumConnectionPoints_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumConnectionPoints_QueryInterface(IEnumConnectionPoints* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumConnectionPoints_AddRef(IEnumConnectionPoints* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumConnectionPoints_Release(IEnumConnectionPoints* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumConnectionPoints methods ***/
static inline HRESULT IEnumConnectionPoints_Next(IEnumConnectionPoints* This,ULONG cConnections,LPCONNECTIONPOINT *ppCP,ULONG *pcFetched) {
    return This->lpVtbl->Next(This,cConnections,ppCP,pcFetched);
}
static inline HRESULT IEnumConnectionPoints_Skip(IEnumConnectionPoints* This,ULONG cConnections) {
    return This->lpVtbl->Skip(This,cConnections);
}
static inline HRESULT IEnumConnectionPoints_Reset(IEnumConnectionPoints* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumConnectionPoints_Clone(IEnumConnectionPoints* This,IEnumConnectionPoints **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumConnectionPoints_RemoteNext_Proxy(
    IEnumConnectionPoints* This,
    ULONG cConnections,
    LPCONNECTIONPOINT *ppCP,
    ULONG *pcFetched);
void __RPC_STUB IEnumConnectionPoints_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumConnectionPoints_Next_Proxy(
    IEnumConnectionPoints* This,
    ULONG cConnections,
    LPCONNECTIONPOINT *ppCP,
    ULONG *pcFetched);
HRESULT __RPC_STUB IEnumConnectionPoints_Next_Stub(
    IEnumConnectionPoints* This,
    ULONG cConnections,
    LPCONNECTIONPOINT *ppCP,
    ULONG *pcFetched);

#endif  /* __IEnumConnectionPoints_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IConnectionPointContainer interface
 */
#ifndef __IConnectionPointContainer_INTERFACE_DEFINED__
#define __IConnectionPointContainer_INTERFACE_DEFINED__

typedef IConnectionPointContainer *PCONNECTIONPOINTCONTAINER;
typedef IConnectionPointContainer *LPCONNECTIONPOINTCONTAINER;

DEFINE_GUID(IID_IConnectionPointContainer, 0xb196b284, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b284-bab4-101a-b69c-00aa00341d07")
IConnectionPointContainer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EnumConnectionPoints(
        IEnumConnectionPoints **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindConnectionPoint(
        REFIID riid,
        IConnectionPoint **ppCP) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IConnectionPointContainer, 0xb196b284, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IConnectionPointContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IConnectionPointContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IConnectionPointContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IConnectionPointContainer *This);

    /*** IConnectionPointContainer methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumConnectionPoints)(
        IConnectionPointContainer *This,
        IEnumConnectionPoints **ppEnum);

    HRESULT (STDMETHODCALLTYPE *FindConnectionPoint)(
        IConnectionPointContainer *This,
        REFIID riid,
        IConnectionPoint **ppCP);

    END_INTERFACE
} IConnectionPointContainerVtbl;

interface IConnectionPointContainer {
    CONST_VTBL IConnectionPointContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IConnectionPointContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IConnectionPointContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IConnectionPointContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IConnectionPointContainer methods ***/
#define IConnectionPointContainer_EnumConnectionPoints(This,ppEnum) (This)->lpVtbl->EnumConnectionPoints(This,ppEnum)
#define IConnectionPointContainer_FindConnectionPoint(This,riid,ppCP) (This)->lpVtbl->FindConnectionPoint(This,riid,ppCP)
#else
/*** IUnknown methods ***/
static inline HRESULT IConnectionPointContainer_QueryInterface(IConnectionPointContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IConnectionPointContainer_AddRef(IConnectionPointContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IConnectionPointContainer_Release(IConnectionPointContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IConnectionPointContainer methods ***/
static inline HRESULT IConnectionPointContainer_EnumConnectionPoints(IConnectionPointContainer* This,IEnumConnectionPoints **ppEnum) {
    return This->lpVtbl->EnumConnectionPoints(This,ppEnum);
}
static inline HRESULT IConnectionPointContainer_FindConnectionPoint(IConnectionPointContainer* This,REFIID riid,IConnectionPoint **ppCP) {
    return This->lpVtbl->FindConnectionPoint(This,riid,ppCP);
}
#endif
#endif

#endif


#endif  /* __IConnectionPointContainer_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IClassFactory2 interface
 */
#ifndef __IClassFactory2_INTERFACE_DEFINED__
#define __IClassFactory2_INTERFACE_DEFINED__

typedef IClassFactory2 *LPCLASSFACTORY2;

typedef struct tagLICINFO {
    LONG cbLicInfo;
    WINBOOL fRuntimeKeyAvail;
    WINBOOL fLicVerified;
} LICINFO;

typedef struct tagLICINFO *LPLICINFO;

DEFINE_GUID(IID_IClassFactory2, 0xb196b28f, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b28f-bab4-101a-b69c-00aa00341d07")
IClassFactory2 : public IClassFactory
{
    virtual HRESULT STDMETHODCALLTYPE GetLicInfo(
        LICINFO *pLicInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestLicKey(
        DWORD dwReserved,
        BSTR *pBstrKey) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstanceLic(
        IUnknown *pUnkOuter,
        IUnknown *pUnkReserved,
        REFIID riid,
        BSTR bstrKey,
        PVOID *ppvObj) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IClassFactory2, 0xb196b28f, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IClassFactory2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IClassFactory2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IClassFactory2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IClassFactory2 *This);

    /*** IClassFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IClassFactory2 *This,
        IUnknown *pUnkOuter,
        REFIID riid,
        void **ppvObject);

    HRESULT (STDMETHODCALLTYPE *LockServer)(
        IClassFactory2 *This,
        WINBOOL fLock);

    /*** IClassFactory2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLicInfo)(
        IClassFactory2 *This,
        LICINFO *pLicInfo);

    HRESULT (STDMETHODCALLTYPE *RequestLicKey)(
        IClassFactory2 *This,
        DWORD dwReserved,
        BSTR *pBstrKey);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceLic)(
        IClassFactory2 *This,
        IUnknown *pUnkOuter,
        IUnknown *pUnkReserved,
        REFIID riid,
        BSTR bstrKey,
        PVOID *ppvObj);

    END_INTERFACE
} IClassFactory2Vtbl;

interface IClassFactory2 {
    CONST_VTBL IClassFactory2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IClassFactory2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IClassFactory2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IClassFactory2_Release(This) (This)->lpVtbl->Release(This)
/*** IClassFactory methods ***/
#define IClassFactory2_CreateInstance(This,pUnkOuter,riid,ppvObject) (This)->lpVtbl->CreateInstance(This,pUnkOuter,riid,ppvObject)
#define IClassFactory2_LockServer(This,fLock) (This)->lpVtbl->LockServer(This,fLock)
/*** IClassFactory2 methods ***/
#define IClassFactory2_GetLicInfo(This,pLicInfo) (This)->lpVtbl->GetLicInfo(This,pLicInfo)
#define IClassFactory2_RequestLicKey(This,dwReserved,pBstrKey) (This)->lpVtbl->RequestLicKey(This,dwReserved,pBstrKey)
#define IClassFactory2_CreateInstanceLic(This,pUnkOuter,pUnkReserved,riid,bstrKey,ppvObj) (This)->lpVtbl->CreateInstanceLic(This,pUnkOuter,pUnkReserved,riid,bstrKey,ppvObj)
#else
/*** IUnknown methods ***/
static inline HRESULT IClassFactory2_QueryInterface(IClassFactory2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IClassFactory2_AddRef(IClassFactory2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IClassFactory2_Release(IClassFactory2* This) {
    return This->lpVtbl->Release(This);
}
/*** IClassFactory methods ***/
static inline HRESULT IClassFactory2_CreateInstance(IClassFactory2* This,IUnknown *pUnkOuter,REFIID riid,void **ppvObject) {
    return This->lpVtbl->CreateInstance(This,pUnkOuter,riid,ppvObject);
}
static inline HRESULT IClassFactory2_LockServer(IClassFactory2* This,WINBOOL fLock) {
    return This->lpVtbl->LockServer(This,fLock);
}
/*** IClassFactory2 methods ***/
static inline HRESULT IClassFactory2_GetLicInfo(IClassFactory2* This,LICINFO *pLicInfo) {
    return This->lpVtbl->GetLicInfo(This,pLicInfo);
}
static inline HRESULT IClassFactory2_RequestLicKey(IClassFactory2* This,DWORD dwReserved,BSTR *pBstrKey) {
    return This->lpVtbl->RequestLicKey(This,dwReserved,pBstrKey);
}
static inline HRESULT IClassFactory2_CreateInstanceLic(IClassFactory2* This,IUnknown *pUnkOuter,IUnknown *pUnkReserved,REFIID riid,BSTR bstrKey,PVOID *ppvObj) {
    return This->lpVtbl->CreateInstanceLic(This,pUnkOuter,pUnkReserved,riid,bstrKey,ppvObj);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IClassFactory2_RemoteCreateInstanceLic_Proxy(
    IClassFactory2* This,
    REFIID riid,
    BSTR bstrKey,
    IUnknown **ppvObj);
void __RPC_STUB IClassFactory2_RemoteCreateInstanceLic_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IClassFactory2_CreateInstanceLic_Proxy(
    IClassFactory2* This,
    IUnknown *pUnkOuter,
    IUnknown *pUnkReserved,
    REFIID riid,
    BSTR bstrKey,
    PVOID *ppvObj);
HRESULT __RPC_STUB IClassFactory2_CreateInstanceLic_Stub(
    IClassFactory2* This,
    REFIID riid,
    BSTR bstrKey,
    IUnknown **ppvObj);

#endif  /* __IClassFactory2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IProvideClassInfo interface
 */
#ifndef __IProvideClassInfo_INTERFACE_DEFINED__
#define __IProvideClassInfo_INTERFACE_DEFINED__

typedef IProvideClassInfo *LPPROVIDECLASSINFO;

DEFINE_GUID(IID_IProvideClassInfo, 0xb196b283, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b283-bab4-101a-b69c-00aa00341d07")
IProvideClassInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClassInfo(
        ITypeInfo **ppTI) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProvideClassInfo, 0xb196b283, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IProvideClassInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProvideClassInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProvideClassInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProvideClassInfo *This);

    /*** IProvideClassInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassInfo)(
        IProvideClassInfo *This,
        ITypeInfo **ppTI);

    END_INTERFACE
} IProvideClassInfoVtbl;

interface IProvideClassInfo {
    CONST_VTBL IProvideClassInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProvideClassInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProvideClassInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProvideClassInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IProvideClassInfo methods ***/
#define IProvideClassInfo_GetClassInfo(This,ppTI) (This)->lpVtbl->GetClassInfo(This,ppTI)
#else
/*** IUnknown methods ***/
static inline HRESULT IProvideClassInfo_QueryInterface(IProvideClassInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProvideClassInfo_AddRef(IProvideClassInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProvideClassInfo_Release(IProvideClassInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IProvideClassInfo methods ***/
static inline HRESULT IProvideClassInfo_GetClassInfo(IProvideClassInfo* This,ITypeInfo **ppTI) {
    return This->lpVtbl->GetClassInfo(This,ppTI);
}
#endif
#endif

#endif


#endif  /* __IProvideClassInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IProvideClassInfo2 interface
 */
#ifndef __IProvideClassInfo2_INTERFACE_DEFINED__
#define __IProvideClassInfo2_INTERFACE_DEFINED__

typedef IProvideClassInfo2 *LPPROVIDECLASSINFO2;

typedef enum tagGUIDKIND {
    GUIDKIND_DEFAULT_SOURCE_DISP_IID = 1
} GUIDKIND;

DEFINE_GUID(IID_IProvideClassInfo2, 0xa6bc3ac0, 0xdbaa, 0x11ce, 0x9d,0xe3, 0x00,0xaa,0x00,0x4b,0xb8,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a6bc3ac0-dbaa-11ce-9de3-00aa004bb851")
IProvideClassInfo2 : public IProvideClassInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetGUID(
        DWORD dwGuidKind,
        GUID *pGUID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProvideClassInfo2, 0xa6bc3ac0, 0xdbaa, 0x11ce, 0x9d,0xe3, 0x00,0xaa,0x00,0x4b,0xb8,0x51)
#endif
#else
typedef struct IProvideClassInfo2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProvideClassInfo2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProvideClassInfo2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProvideClassInfo2 *This);

    /*** IProvideClassInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassInfo)(
        IProvideClassInfo2 *This,
        ITypeInfo **ppTI);

    /*** IProvideClassInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IProvideClassInfo2 *This,
        DWORD dwGuidKind,
        GUID *pGUID);

    END_INTERFACE
} IProvideClassInfo2Vtbl;

interface IProvideClassInfo2 {
    CONST_VTBL IProvideClassInfo2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProvideClassInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProvideClassInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProvideClassInfo2_Release(This) (This)->lpVtbl->Release(This)
/*** IProvideClassInfo methods ***/
#define IProvideClassInfo2_GetClassInfo(This,ppTI) (This)->lpVtbl->GetClassInfo(This,ppTI)
/*** IProvideClassInfo2 methods ***/
#define IProvideClassInfo2_GetGUID(This,dwGuidKind,pGUID) (This)->lpVtbl->GetGUID(This,dwGuidKind,pGUID)
#else
/*** IUnknown methods ***/
static inline HRESULT IProvideClassInfo2_QueryInterface(IProvideClassInfo2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProvideClassInfo2_AddRef(IProvideClassInfo2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProvideClassInfo2_Release(IProvideClassInfo2* This) {
    return This->lpVtbl->Release(This);
}
/*** IProvideClassInfo methods ***/
static inline HRESULT IProvideClassInfo2_GetClassInfo(IProvideClassInfo2* This,ITypeInfo **ppTI) {
    return This->lpVtbl->GetClassInfo(This,ppTI);
}
/*** IProvideClassInfo2 methods ***/
static inline HRESULT IProvideClassInfo2_GetGUID(IProvideClassInfo2* This,DWORD dwGuidKind,GUID *pGUID) {
    return This->lpVtbl->GetGUID(This,dwGuidKind,pGUID);
}
#endif
#endif

#endif


#endif  /* __IProvideClassInfo2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IProvideMultipleClassInfo interface
 */
#ifndef __IProvideMultipleClassInfo_INTERFACE_DEFINED__
#define __IProvideMultipleClassInfo_INTERFACE_DEFINED__

#define MULTICLASSINFO_GETTYPEINFO 0x1
#define MULTICLASSINFO_GETNUMRESERVEDDISPIDS 0x2
#define MULTICLASSINFO_GETIIDPRIMARY 0x4
#define MULTICLASSINFO_GETIIDSOURCE 0x8

#define TIFLAGS_EXTENDDISPATCHONLY 0x1

typedef IProvideMultipleClassInfo *LPPROVIDEMULTIPLECLASSINFO;

DEFINE_GUID(IID_IProvideMultipleClassInfo, 0xa7aba9c1, 0x8983, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a7aba9c1-8983-11cf-8f20-00805f2cd064")
IProvideMultipleClassInfo : public IProvideClassInfo2
{
    virtual HRESULT STDMETHODCALLTYPE GetMultiTypeInfoCount(
        ULONG *pcti) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInfoOfIndex(
        ULONG iti,
        DWORD dwFlags,
        ITypeInfo **pptiCoClass,
        DWORD *pdwTIFlags,
        ULONG *pcdispidReserved,
        IID *piidPrimary,
        IID *piidSource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProvideMultipleClassInfo, 0xa7aba9c1, 0x8983, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#else
typedef struct IProvideMultipleClassInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProvideMultipleClassInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProvideMultipleClassInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProvideMultipleClassInfo *This);

    /*** IProvideClassInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassInfo)(
        IProvideMultipleClassInfo *This,
        ITypeInfo **ppTI);

    /*** IProvideClassInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetGUID)(
        IProvideMultipleClassInfo *This,
        DWORD dwGuidKind,
        GUID *pGUID);

    /*** IProvideMultipleClassInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMultiTypeInfoCount)(
        IProvideMultipleClassInfo *This,
        ULONG *pcti);

    HRESULT (STDMETHODCALLTYPE *GetInfoOfIndex)(
        IProvideMultipleClassInfo *This,
        ULONG iti,
        DWORD dwFlags,
        ITypeInfo **pptiCoClass,
        DWORD *pdwTIFlags,
        ULONG *pcdispidReserved,
        IID *piidPrimary,
        IID *piidSource);

    END_INTERFACE
} IProvideMultipleClassInfoVtbl;

interface IProvideMultipleClassInfo {
    CONST_VTBL IProvideMultipleClassInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProvideMultipleClassInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProvideMultipleClassInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProvideMultipleClassInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IProvideClassInfo methods ***/
#define IProvideMultipleClassInfo_GetClassInfo(This,ppTI) (This)->lpVtbl->GetClassInfo(This,ppTI)
/*** IProvideClassInfo2 methods ***/
#define IProvideMultipleClassInfo_GetGUID(This,dwGuidKind,pGUID) (This)->lpVtbl->GetGUID(This,dwGuidKind,pGUID)
/*** IProvideMultipleClassInfo methods ***/
#define IProvideMultipleClassInfo_GetMultiTypeInfoCount(This,pcti) (This)->lpVtbl->GetMultiTypeInfoCount(This,pcti)
#define IProvideMultipleClassInfo_GetInfoOfIndex(This,iti,dwFlags,pptiCoClass,pdwTIFlags,pcdispidReserved,piidPrimary,piidSource) (This)->lpVtbl->GetInfoOfIndex(This,iti,dwFlags,pptiCoClass,pdwTIFlags,pcdispidReserved,piidPrimary,piidSource)
#else
/*** IUnknown methods ***/
static inline HRESULT IProvideMultipleClassInfo_QueryInterface(IProvideMultipleClassInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProvideMultipleClassInfo_AddRef(IProvideMultipleClassInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProvideMultipleClassInfo_Release(IProvideMultipleClassInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IProvideClassInfo methods ***/
static inline HRESULT IProvideMultipleClassInfo_GetClassInfo(IProvideMultipleClassInfo* This,ITypeInfo **ppTI) {
    return This->lpVtbl->GetClassInfo(This,ppTI);
}
/*** IProvideClassInfo2 methods ***/
static inline HRESULT IProvideMultipleClassInfo_GetGUID(IProvideMultipleClassInfo* This,DWORD dwGuidKind,GUID *pGUID) {
    return This->lpVtbl->GetGUID(This,dwGuidKind,pGUID);
}
/*** IProvideMultipleClassInfo methods ***/
static inline HRESULT IProvideMultipleClassInfo_GetMultiTypeInfoCount(IProvideMultipleClassInfo* This,ULONG *pcti) {
    return This->lpVtbl->GetMultiTypeInfoCount(This,pcti);
}
static inline HRESULT IProvideMultipleClassInfo_GetInfoOfIndex(IProvideMultipleClassInfo* This,ULONG iti,DWORD dwFlags,ITypeInfo **pptiCoClass,DWORD *pdwTIFlags,ULONG *pcdispidReserved,IID *piidPrimary,IID *piidSource) {
    return This->lpVtbl->GetInfoOfIndex(This,iti,dwFlags,pptiCoClass,pdwTIFlags,pcdispidReserved,piidPrimary,piidSource);
}
#endif
#endif

#endif


#endif  /* __IProvideMultipleClassInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleControl interface
 */
#ifndef __IOleControl_INTERFACE_DEFINED__
#define __IOleControl_INTERFACE_DEFINED__

typedef IOleControl *LPOLECONTROL;

typedef struct tagCONTROLINFO {
    ULONG cb;
    HACCEL hAccel;
    USHORT cAccel;
    DWORD dwFlags;
} CONTROLINFO;

typedef struct tagCONTROLINFO *LPCONTROLINFO;

typedef enum tagCTRLINFO {
    CTRLINFO_EATS_RETURN = 1,
    CTRLINFO_EATS_ESCAPE = 2
} CTRLINFO;

DEFINE_GUID(IID_IOleControl, 0xb196b288, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b288-bab4-101a-b69c-00aa00341d07")
IOleControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetControlInfo(
        CONTROLINFO *pCI) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnMnemonic(
        MSG *pMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnAmbientPropertyChange(
        DISPID dispID) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreezeEvents(
        WINBOOL bFreeze) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleControl, 0xb196b288, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IOleControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleControl *This);

    /*** IOleControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetControlInfo)(
        IOleControl *This,
        CONTROLINFO *pCI);

    HRESULT (STDMETHODCALLTYPE *OnMnemonic)(
        IOleControl *This,
        MSG *pMsg);

    HRESULT (STDMETHODCALLTYPE *OnAmbientPropertyChange)(
        IOleControl *This,
        DISPID dispID);

    HRESULT (STDMETHODCALLTYPE *FreezeEvents)(
        IOleControl *This,
        WINBOOL bFreeze);

    END_INTERFACE
} IOleControlVtbl;

interface IOleControl {
    CONST_VTBL IOleControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleControl_Release(This) (This)->lpVtbl->Release(This)
/*** IOleControl methods ***/
#define IOleControl_GetControlInfo(This,pCI) (This)->lpVtbl->GetControlInfo(This,pCI)
#define IOleControl_OnMnemonic(This,pMsg) (This)->lpVtbl->OnMnemonic(This,pMsg)
#define IOleControl_OnAmbientPropertyChange(This,dispID) (This)->lpVtbl->OnAmbientPropertyChange(This,dispID)
#define IOleControl_FreezeEvents(This,bFreeze) (This)->lpVtbl->FreezeEvents(This,bFreeze)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleControl_QueryInterface(IOleControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleControl_AddRef(IOleControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleControl_Release(IOleControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleControl methods ***/
static inline HRESULT IOleControl_GetControlInfo(IOleControl* This,CONTROLINFO *pCI) {
    return This->lpVtbl->GetControlInfo(This,pCI);
}
static inline HRESULT IOleControl_OnMnemonic(IOleControl* This,MSG *pMsg) {
    return This->lpVtbl->OnMnemonic(This,pMsg);
}
static inline HRESULT IOleControl_OnAmbientPropertyChange(IOleControl* This,DISPID dispID) {
    return This->lpVtbl->OnAmbientPropertyChange(This,dispID);
}
static inline HRESULT IOleControl_FreezeEvents(IOleControl* This,WINBOOL bFreeze) {
    return This->lpVtbl->FreezeEvents(This,bFreeze);
}
#endif
#endif

#endif


#endif  /* __IOleControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleControlSite interface
 */
#ifndef __IOleControlSite_INTERFACE_DEFINED__
#define __IOleControlSite_INTERFACE_DEFINED__

typedef IOleControlSite *LPOLECONTROLSITE;

typedef struct tagPOINTF {
    FLOAT x;
    FLOAT y;
} POINTF;

typedef struct tagPOINTF *LPPOINTF;

typedef enum tagXFORMCOORDS {
    XFORMCOORDS_POSITION = 0x1,
    XFORMCOORDS_SIZE = 0x2,
    XFORMCOORDS_HIMETRICTOCONTAINER = 0x4,
    XFORMCOORDS_CONTAINERTOHIMETRIC = 0x8,
    XFORMCOORDS_EVENTCOMPAT = 0x10
} XFORMCOORDS;

DEFINE_GUID(IID_IOleControlSite, 0xb196b289, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b289-bab4-101a-b69c-00aa00341d07")
IOleControlSite : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnControlInfoChanged(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockInPlaceActive(
        WINBOOL fLock) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExtendedControl(
        IDispatch **ppDisp) = 0;

    virtual HRESULT STDMETHODCALLTYPE TransformCoords(
        POINTL *pPtlHimetric,
        POINTF *pPtfContainer,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE TranslateAccelerator(
        MSG *pMsg,
        DWORD grfModifiers) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnFocus(
        WINBOOL fGotFocus) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowPropertyFrame(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleControlSite, 0xb196b289, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IOleControlSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleControlSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleControlSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleControlSite *This);

    /*** IOleControlSite methods ***/
    HRESULT (STDMETHODCALLTYPE *OnControlInfoChanged)(
        IOleControlSite *This);

    HRESULT (STDMETHODCALLTYPE *LockInPlaceActive)(
        IOleControlSite *This,
        WINBOOL fLock);

    HRESULT (STDMETHODCALLTYPE *GetExtendedControl)(
        IOleControlSite *This,
        IDispatch **ppDisp);

    HRESULT (STDMETHODCALLTYPE *TransformCoords)(
        IOleControlSite *This,
        POINTL *pPtlHimetric,
        POINTF *pPtfContainer,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *TranslateAccelerator)(
        IOleControlSite *This,
        MSG *pMsg,
        DWORD grfModifiers);

    HRESULT (STDMETHODCALLTYPE *OnFocus)(
        IOleControlSite *This,
        WINBOOL fGotFocus);

    HRESULT (STDMETHODCALLTYPE *ShowPropertyFrame)(
        IOleControlSite *This);

    END_INTERFACE
} IOleControlSiteVtbl;

interface IOleControlSite {
    CONST_VTBL IOleControlSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleControlSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleControlSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleControlSite_Release(This) (This)->lpVtbl->Release(This)
/*** IOleControlSite methods ***/
#define IOleControlSite_OnControlInfoChanged(This) (This)->lpVtbl->OnControlInfoChanged(This)
#define IOleControlSite_LockInPlaceActive(This,fLock) (This)->lpVtbl->LockInPlaceActive(This,fLock)
#define IOleControlSite_GetExtendedControl(This,ppDisp) (This)->lpVtbl->GetExtendedControl(This,ppDisp)
#define IOleControlSite_TransformCoords(This,pPtlHimetric,pPtfContainer,dwFlags) (This)->lpVtbl->TransformCoords(This,pPtlHimetric,pPtfContainer,dwFlags)
#define IOleControlSite_TranslateAccelerator(This,pMsg,grfModifiers) (This)->lpVtbl->TranslateAccelerator(This,pMsg,grfModifiers)
#define IOleControlSite_OnFocus(This,fGotFocus) (This)->lpVtbl->OnFocus(This,fGotFocus)
#define IOleControlSite_ShowPropertyFrame(This) (This)->lpVtbl->ShowPropertyFrame(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleControlSite_QueryInterface(IOleControlSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleControlSite_AddRef(IOleControlSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleControlSite_Release(IOleControlSite* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleControlSite methods ***/
static inline HRESULT IOleControlSite_OnControlInfoChanged(IOleControlSite* This) {
    return This->lpVtbl->OnControlInfoChanged(This);
}
static inline HRESULT IOleControlSite_LockInPlaceActive(IOleControlSite* This,WINBOOL fLock) {
    return This->lpVtbl->LockInPlaceActive(This,fLock);
}
static inline HRESULT IOleControlSite_GetExtendedControl(IOleControlSite* This,IDispatch **ppDisp) {
    return This->lpVtbl->GetExtendedControl(This,ppDisp);
}
static inline HRESULT IOleControlSite_TransformCoords(IOleControlSite* This,POINTL *pPtlHimetric,POINTF *pPtfContainer,DWORD dwFlags) {
    return This->lpVtbl->TransformCoords(This,pPtlHimetric,pPtfContainer,dwFlags);
}
static inline HRESULT IOleControlSite_TranslateAccelerator(IOleControlSite* This,MSG *pMsg,DWORD grfModifiers) {
    return This->lpVtbl->TranslateAccelerator(This,pMsg,grfModifiers);
}
static inline HRESULT IOleControlSite_OnFocus(IOleControlSite* This,WINBOOL fGotFocus) {
    return This->lpVtbl->OnFocus(This,fGotFocus);
}
static inline HRESULT IOleControlSite_ShowPropertyFrame(IOleControlSite* This) {
    return This->lpVtbl->ShowPropertyFrame(This);
}
#endif
#endif

#endif


#endif  /* __IOleControlSite_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyPage interface
 */
#ifndef __IPropertyPage_INTERFACE_DEFINED__
#define __IPropertyPage_INTERFACE_DEFINED__

typedef IPropertyPage *LPPROPERTYPAGE;

typedef struct tagPROPPAGEINFO {
    ULONG cb;
    LPOLESTR pszTitle;
    SIZE size;
    LPOLESTR pszDocString;
    LPOLESTR pszHelpFile;
    DWORD dwHelpContext;
} PROPPAGEINFO;

typedef struct tagPROPPAGEINFO *LPPROPPAGEINFO;

DEFINE_GUID(IID_IPropertyPage, 0xb196b28d, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b28d-bab4-101a-b69c-00aa00341d07")
IPropertyPage : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetPageSite(
        IPropertyPageSite *pPageSite) = 0;

    virtual HRESULT STDMETHODCALLTYPE Activate(
        HWND hWndParent,
        LPCRECT pRect,
        WINBOOL bModal) = 0;

    virtual HRESULT STDMETHODCALLTYPE Deactivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPageInfo(
        PROPPAGEINFO *pPageInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetObjects(
        ULONG cObjects,
        IUnknown **ppUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Show(
        UINT nCmdShow) = 0;

    virtual HRESULT STDMETHODCALLTYPE Move(
        LPCRECT pRect) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPageDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Apply(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Help(
        LPCOLESTR pszHelpDir) = 0;

    virtual HRESULT STDMETHODCALLTYPE TranslateAccelerator(
        MSG *pMsg) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyPage, 0xb196b28d, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IPropertyPageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyPage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyPage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyPage *This);

    /*** IPropertyPage methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPageSite)(
        IPropertyPage *This,
        IPropertyPageSite *pPageSite);

    HRESULT (STDMETHODCALLTYPE *Activate)(
        IPropertyPage *This,
        HWND hWndParent,
        LPCRECT pRect,
        WINBOOL bModal);

    HRESULT (STDMETHODCALLTYPE *Deactivate)(
        IPropertyPage *This);

    HRESULT (STDMETHODCALLTYPE *GetPageInfo)(
        IPropertyPage *This,
        PROPPAGEINFO *pPageInfo);

    HRESULT (STDMETHODCALLTYPE *SetObjects)(
        IPropertyPage *This,
        ULONG cObjects,
        IUnknown **ppUnk);

    HRESULT (STDMETHODCALLTYPE *Show)(
        IPropertyPage *This,
        UINT nCmdShow);

    HRESULT (STDMETHODCALLTYPE *Move)(
        IPropertyPage *This,
        LPCRECT pRect);

    HRESULT (STDMETHODCALLTYPE *IsPageDirty)(
        IPropertyPage *This);

    HRESULT (STDMETHODCALLTYPE *Apply)(
        IPropertyPage *This);

    HRESULT (STDMETHODCALLTYPE *Help)(
        IPropertyPage *This,
        LPCOLESTR pszHelpDir);

    HRESULT (STDMETHODCALLTYPE *TranslateAccelerator)(
        IPropertyPage *This,
        MSG *pMsg);

    END_INTERFACE
} IPropertyPageVtbl;

interface IPropertyPage {
    CONST_VTBL IPropertyPageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyPage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyPage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyPage_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyPage methods ***/
#define IPropertyPage_SetPageSite(This,pPageSite) (This)->lpVtbl->SetPageSite(This,pPageSite)
#define IPropertyPage_Activate(This,hWndParent,pRect,bModal) (This)->lpVtbl->Activate(This,hWndParent,pRect,bModal)
#define IPropertyPage_Deactivate(This) (This)->lpVtbl->Deactivate(This)
#define IPropertyPage_GetPageInfo(This,pPageInfo) (This)->lpVtbl->GetPageInfo(This,pPageInfo)
#define IPropertyPage_SetObjects(This,cObjects,ppUnk) (This)->lpVtbl->SetObjects(This,cObjects,ppUnk)
#define IPropertyPage_Show(This,nCmdShow) (This)->lpVtbl->Show(This,nCmdShow)
#define IPropertyPage_Move(This,pRect) (This)->lpVtbl->Move(This,pRect)
#define IPropertyPage_IsPageDirty(This) (This)->lpVtbl->IsPageDirty(This)
#define IPropertyPage_Apply(This) (This)->lpVtbl->Apply(This)
#define IPropertyPage_Help(This,pszHelpDir) (This)->lpVtbl->Help(This,pszHelpDir)
#define IPropertyPage_TranslateAccelerator(This,pMsg) (This)->lpVtbl->TranslateAccelerator(This,pMsg)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyPage_QueryInterface(IPropertyPage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyPage_AddRef(IPropertyPage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyPage_Release(IPropertyPage* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyPage methods ***/
static inline HRESULT IPropertyPage_SetPageSite(IPropertyPage* This,IPropertyPageSite *pPageSite) {
    return This->lpVtbl->SetPageSite(This,pPageSite);
}
static inline HRESULT IPropertyPage_Activate(IPropertyPage* This,HWND hWndParent,LPCRECT pRect,WINBOOL bModal) {
    return This->lpVtbl->Activate(This,hWndParent,pRect,bModal);
}
static inline HRESULT IPropertyPage_Deactivate(IPropertyPage* This) {
    return This->lpVtbl->Deactivate(This);
}
static inline HRESULT IPropertyPage_GetPageInfo(IPropertyPage* This,PROPPAGEINFO *pPageInfo) {
    return This->lpVtbl->GetPageInfo(This,pPageInfo);
}
static inline HRESULT IPropertyPage_SetObjects(IPropertyPage* This,ULONG cObjects,IUnknown **ppUnk) {
    return This->lpVtbl->SetObjects(This,cObjects,ppUnk);
}
static inline HRESULT IPropertyPage_Show(IPropertyPage* This,UINT nCmdShow) {
    return This->lpVtbl->Show(This,nCmdShow);
}
static inline HRESULT IPropertyPage_Move(IPropertyPage* This,LPCRECT pRect) {
    return This->lpVtbl->Move(This,pRect);
}
static inline HRESULT IPropertyPage_IsPageDirty(IPropertyPage* This) {
    return This->lpVtbl->IsPageDirty(This);
}
static inline HRESULT IPropertyPage_Apply(IPropertyPage* This) {
    return This->lpVtbl->Apply(This);
}
static inline HRESULT IPropertyPage_Help(IPropertyPage* This,LPCOLESTR pszHelpDir) {
    return This->lpVtbl->Help(This,pszHelpDir);
}
static inline HRESULT IPropertyPage_TranslateAccelerator(IPropertyPage* This,MSG *pMsg) {
    return This->lpVtbl->TranslateAccelerator(This,pMsg);
}
#endif
#endif

#endif


#endif  /* __IPropertyPage_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyPage2 interface
 */
#ifndef __IPropertyPage2_INTERFACE_DEFINED__
#define __IPropertyPage2_INTERFACE_DEFINED__

typedef IPropertyPage2 *LPPROPERTYPAGE2;

DEFINE_GUID(IID_IPropertyPage2, 0x01e44665, 0x24ac, 0x101b, 0x84,0xed, 0x08,0x00,0x2b,0x2e,0xc7,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("01e44665-24ac-101b-84ed-08002b2ec713")
IPropertyPage2 : public IPropertyPage
{
    virtual HRESULT STDMETHODCALLTYPE EditProperty(
        DISPID dispID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyPage2, 0x01e44665, 0x24ac, 0x101b, 0x84,0xed, 0x08,0x00,0x2b,0x2e,0xc7,0x13)
#endif
#else
typedef struct IPropertyPage2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyPage2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyPage2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyPage2 *This);

    /*** IPropertyPage methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPageSite)(
        IPropertyPage2 *This,
        IPropertyPageSite *pPageSite);

    HRESULT (STDMETHODCALLTYPE *Activate)(
        IPropertyPage2 *This,
        HWND hWndParent,
        LPCRECT pRect,
        WINBOOL bModal);

    HRESULT (STDMETHODCALLTYPE *Deactivate)(
        IPropertyPage2 *This);

    HRESULT (STDMETHODCALLTYPE *GetPageInfo)(
        IPropertyPage2 *This,
        PROPPAGEINFO *pPageInfo);

    HRESULT (STDMETHODCALLTYPE *SetObjects)(
        IPropertyPage2 *This,
        ULONG cObjects,
        IUnknown **ppUnk);

    HRESULT (STDMETHODCALLTYPE *Show)(
        IPropertyPage2 *This,
        UINT nCmdShow);

    HRESULT (STDMETHODCALLTYPE *Move)(
        IPropertyPage2 *This,
        LPCRECT pRect);

    HRESULT (STDMETHODCALLTYPE *IsPageDirty)(
        IPropertyPage2 *This);

    HRESULT (STDMETHODCALLTYPE *Apply)(
        IPropertyPage2 *This);

    HRESULT (STDMETHODCALLTYPE *Help)(
        IPropertyPage2 *This,
        LPCOLESTR pszHelpDir);

    HRESULT (STDMETHODCALLTYPE *TranslateAccelerator)(
        IPropertyPage2 *This,
        MSG *pMsg);

    /*** IPropertyPage2 methods ***/
    HRESULT (STDMETHODCALLTYPE *EditProperty)(
        IPropertyPage2 *This,
        DISPID dispID);

    END_INTERFACE
} IPropertyPage2Vtbl;

interface IPropertyPage2 {
    CONST_VTBL IPropertyPage2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyPage2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyPage2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyPage2_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyPage methods ***/
#define IPropertyPage2_SetPageSite(This,pPageSite) (This)->lpVtbl->SetPageSite(This,pPageSite)
#define IPropertyPage2_Activate(This,hWndParent,pRect,bModal) (This)->lpVtbl->Activate(This,hWndParent,pRect,bModal)
#define IPropertyPage2_Deactivate(This) (This)->lpVtbl->Deactivate(This)
#define IPropertyPage2_GetPageInfo(This,pPageInfo) (This)->lpVtbl->GetPageInfo(This,pPageInfo)
#define IPropertyPage2_SetObjects(This,cObjects,ppUnk) (This)->lpVtbl->SetObjects(This,cObjects,ppUnk)
#define IPropertyPage2_Show(This,nCmdShow) (This)->lpVtbl->Show(This,nCmdShow)
#define IPropertyPage2_Move(This,pRect) (This)->lpVtbl->Move(This,pRect)
#define IPropertyPage2_IsPageDirty(This) (This)->lpVtbl->IsPageDirty(This)
#define IPropertyPage2_Apply(This) (This)->lpVtbl->Apply(This)
#define IPropertyPage2_Help(This,pszHelpDir) (This)->lpVtbl->Help(This,pszHelpDir)
#define IPropertyPage2_TranslateAccelerator(This,pMsg) (This)->lpVtbl->TranslateAccelerator(This,pMsg)
/*** IPropertyPage2 methods ***/
#define IPropertyPage2_EditProperty(This,dispID) (This)->lpVtbl->EditProperty(This,dispID)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyPage2_QueryInterface(IPropertyPage2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyPage2_AddRef(IPropertyPage2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyPage2_Release(IPropertyPage2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyPage methods ***/
static inline HRESULT IPropertyPage2_SetPageSite(IPropertyPage2* This,IPropertyPageSite *pPageSite) {
    return This->lpVtbl->SetPageSite(This,pPageSite);
}
static inline HRESULT IPropertyPage2_Activate(IPropertyPage2* This,HWND hWndParent,LPCRECT pRect,WINBOOL bModal) {
    return This->lpVtbl->Activate(This,hWndParent,pRect,bModal);
}
static inline HRESULT IPropertyPage2_Deactivate(IPropertyPage2* This) {
    return This->lpVtbl->Deactivate(This);
}
static inline HRESULT IPropertyPage2_GetPageInfo(IPropertyPage2* This,PROPPAGEINFO *pPageInfo) {
    return This->lpVtbl->GetPageInfo(This,pPageInfo);
}
static inline HRESULT IPropertyPage2_SetObjects(IPropertyPage2* This,ULONG cObjects,IUnknown **ppUnk) {
    return This->lpVtbl->SetObjects(This,cObjects,ppUnk);
}
static inline HRESULT IPropertyPage2_Show(IPropertyPage2* This,UINT nCmdShow) {
    return This->lpVtbl->Show(This,nCmdShow);
}
static inline HRESULT IPropertyPage2_Move(IPropertyPage2* This,LPCRECT pRect) {
    return This->lpVtbl->Move(This,pRect);
}
static inline HRESULT IPropertyPage2_IsPageDirty(IPropertyPage2* This) {
    return This->lpVtbl->IsPageDirty(This);
}
static inline HRESULT IPropertyPage2_Apply(IPropertyPage2* This) {
    return This->lpVtbl->Apply(This);
}
static inline HRESULT IPropertyPage2_Help(IPropertyPage2* This,LPCOLESTR pszHelpDir) {
    return This->lpVtbl->Help(This,pszHelpDir);
}
static inline HRESULT IPropertyPage2_TranslateAccelerator(IPropertyPage2* This,MSG *pMsg) {
    return This->lpVtbl->TranslateAccelerator(This,pMsg);
}
/*** IPropertyPage2 methods ***/
static inline HRESULT IPropertyPage2_EditProperty(IPropertyPage2* This,DISPID dispID) {
    return This->lpVtbl->EditProperty(This,dispID);
}
#endif
#endif

#endif


#endif  /* __IPropertyPage2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyPageSite interface
 */
#ifndef __IPropertyPageSite_INTERFACE_DEFINED__
#define __IPropertyPageSite_INTERFACE_DEFINED__

typedef IPropertyPageSite *LPPROPERTYPAGESITE;

typedef enum tagPROPPAGESTATUS {
    PROPPAGESTATUS_DIRTY = 0x1,
    PROPPAGESTATUS_VALIDATE = 0x2,
    PROPPAGESTATUS_CLEAN = 0x4
} PROPPAGESTATUS;

DEFINE_GUID(IID_IPropertyPageSite, 0xb196b28c, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b28c-bab4-101a-b69c-00aa00341d07")
IPropertyPageSite : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStatusChange(
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocaleID(
        LCID *pLocaleID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPageContainer(
        IUnknown **ppUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE TranslateAccelerator(
        MSG *pMsg) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyPageSite, 0xb196b28c, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IPropertyPageSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyPageSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyPageSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyPageSite *This);

    /*** IPropertyPageSite methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStatusChange)(
        IPropertyPageSite *This,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetLocaleID)(
        IPropertyPageSite *This,
        LCID *pLocaleID);

    HRESULT (STDMETHODCALLTYPE *GetPageContainer)(
        IPropertyPageSite *This,
        IUnknown **ppUnk);

    HRESULT (STDMETHODCALLTYPE *TranslateAccelerator)(
        IPropertyPageSite *This,
        MSG *pMsg);

    END_INTERFACE
} IPropertyPageSiteVtbl;

interface IPropertyPageSite {
    CONST_VTBL IPropertyPageSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyPageSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyPageSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyPageSite_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyPageSite methods ***/
#define IPropertyPageSite_OnStatusChange(This,dwFlags) (This)->lpVtbl->OnStatusChange(This,dwFlags)
#define IPropertyPageSite_GetLocaleID(This,pLocaleID) (This)->lpVtbl->GetLocaleID(This,pLocaleID)
#define IPropertyPageSite_GetPageContainer(This,ppUnk) (This)->lpVtbl->GetPageContainer(This,ppUnk)
#define IPropertyPageSite_TranslateAccelerator(This,pMsg) (This)->lpVtbl->TranslateAccelerator(This,pMsg)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyPageSite_QueryInterface(IPropertyPageSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyPageSite_AddRef(IPropertyPageSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyPageSite_Release(IPropertyPageSite* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyPageSite methods ***/
static inline HRESULT IPropertyPageSite_OnStatusChange(IPropertyPageSite* This,DWORD dwFlags) {
    return This->lpVtbl->OnStatusChange(This,dwFlags);
}
static inline HRESULT IPropertyPageSite_GetLocaleID(IPropertyPageSite* This,LCID *pLocaleID) {
    return This->lpVtbl->GetLocaleID(This,pLocaleID);
}
static inline HRESULT IPropertyPageSite_GetPageContainer(IPropertyPageSite* This,IUnknown **ppUnk) {
    return This->lpVtbl->GetPageContainer(This,ppUnk);
}
static inline HRESULT IPropertyPageSite_TranslateAccelerator(IPropertyPageSite* This,MSG *pMsg) {
    return This->lpVtbl->TranslateAccelerator(This,pMsg);
}
#endif
#endif

#endif


#endif  /* __IPropertyPageSite_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPropertyNotifySink interface
 */
#ifndef __IPropertyNotifySink_INTERFACE_DEFINED__
#define __IPropertyNotifySink_INTERFACE_DEFINED__

typedef IPropertyNotifySink *LPPROPERTYNOTIFYSINK;

DEFINE_GUID(IID_IPropertyNotifySink, 0x9bfbbc02, 0xeff1, 0x101a, 0x84,0xed, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9bfbbc02-eff1-101a-84ed-00aa00341d07")
IPropertyNotifySink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnChanged(
        DISPID dispID) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnRequestEdit(
        DISPID dispID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyNotifySink, 0x9bfbbc02, 0xeff1, 0x101a, 0x84,0xed, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct IPropertyNotifySinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyNotifySink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyNotifySink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyNotifySink *This);

    /*** IPropertyNotifySink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnChanged)(
        IPropertyNotifySink *This,
        DISPID dispID);

    HRESULT (STDMETHODCALLTYPE *OnRequestEdit)(
        IPropertyNotifySink *This,
        DISPID dispID);

    END_INTERFACE
} IPropertyNotifySinkVtbl;

interface IPropertyNotifySink {
    CONST_VTBL IPropertyNotifySinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyNotifySink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyNotifySink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyNotifySink_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyNotifySink methods ***/
#define IPropertyNotifySink_OnChanged(This,dispID) (This)->lpVtbl->OnChanged(This,dispID)
#define IPropertyNotifySink_OnRequestEdit(This,dispID) (This)->lpVtbl->OnRequestEdit(This,dispID)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyNotifySink_QueryInterface(IPropertyNotifySink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyNotifySink_AddRef(IPropertyNotifySink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyNotifySink_Release(IPropertyNotifySink* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyNotifySink methods ***/
static inline HRESULT IPropertyNotifySink_OnChanged(IPropertyNotifySink* This,DISPID dispID) {
    return This->lpVtbl->OnChanged(This,dispID);
}
static inline HRESULT IPropertyNotifySink_OnRequestEdit(IPropertyNotifySink* This,DISPID dispID) {
    return This->lpVtbl->OnRequestEdit(This,dispID);
}
#endif
#endif

#endif


#endif  /* __IPropertyNotifySink_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISpecifyPropertyPages interface
 */
#ifndef __ISpecifyPropertyPages_INTERFACE_DEFINED__
#define __ISpecifyPropertyPages_INTERFACE_DEFINED__

typedef ISpecifyPropertyPages *LPSPECIFYPROPERTYPAGES;

typedef struct tagCAUUID {
    ULONG cElems;
    GUID *pElems;
} CAUUID;

typedef struct tagCAUUID *LPCAUUID;

DEFINE_GUID(IID_ISpecifyPropertyPages, 0xb196b28b, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b196b28b-bab4-101a-b69c-00aa00341d07")
ISpecifyPropertyPages : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPages(
        CAUUID *pPages) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISpecifyPropertyPages, 0xb196b28b, 0xbab4, 0x101a, 0xb6,0x9c, 0x00,0xaa,0x00,0x34,0x1d,0x07)
#endif
#else
typedef struct ISpecifyPropertyPagesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISpecifyPropertyPages *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISpecifyPropertyPages *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISpecifyPropertyPages *This);

    /*** ISpecifyPropertyPages methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPages)(
        ISpecifyPropertyPages *This,
        CAUUID *pPages);

    END_INTERFACE
} ISpecifyPropertyPagesVtbl;

interface ISpecifyPropertyPages {
    CONST_VTBL ISpecifyPropertyPagesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISpecifyPropertyPages_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISpecifyPropertyPages_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISpecifyPropertyPages_Release(This) (This)->lpVtbl->Release(This)
/*** ISpecifyPropertyPages methods ***/
#define ISpecifyPropertyPages_GetPages(This,pPages) (This)->lpVtbl->GetPages(This,pPages)
#else
/*** IUnknown methods ***/
static inline HRESULT ISpecifyPropertyPages_QueryInterface(ISpecifyPropertyPages* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISpecifyPropertyPages_AddRef(ISpecifyPropertyPages* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISpecifyPropertyPages_Release(ISpecifyPropertyPages* This) {
    return This->lpVtbl->Release(This);
}
/*** ISpecifyPropertyPages methods ***/
static inline HRESULT ISpecifyPropertyPages_GetPages(ISpecifyPropertyPages* This,CAUUID *pPages) {
    return This->lpVtbl->GetPages(This,pPages);
}
#endif
#endif

#endif


#endif  /* __ISpecifyPropertyPages_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPersistMemory interface
 */
#ifndef __IPersistMemory_INTERFACE_DEFINED__
#define __IPersistMemory_INTERFACE_DEFINED__

typedef IPersistMemory *LPPERSISTMEMORY;

DEFINE_GUID(IID_IPersistMemory, 0xbd1ae5e0, 0xa6ae, 0x11ce, 0xbd,0x37, 0x50,0x42,0x00,0xc1,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bd1ae5e0-a6ae-11ce-bd37-504200c10000")
IPersistMemory : public IPersist
{
    virtual HRESULT STDMETHODCALLTYPE IsDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        LPVOID pMem,
        ULONG cbSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        LPVOID pMem,
        WINBOOL fClearDirty,
        ULONG cbSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSizeMax(
        ULONG *pCbSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitNew(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistMemory, 0xbd1ae5e0, 0xa6ae, 0x11ce, 0xbd,0x37, 0x50,0x42,0x00,0xc1,0x00,0x00)
#endif
#else
typedef struct IPersistMemoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistMemory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistMemory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistMemory *This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersistMemory *This,
        CLSID *pClassID);

    /*** IPersistMemory methods ***/
    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IPersistMemory *This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IPersistMemory *This,
        LPVOID pMem,
        ULONG cbSize);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IPersistMemory *This,
        LPVOID pMem,
        WINBOOL fClearDirty,
        ULONG cbSize);

    HRESULT (STDMETHODCALLTYPE *GetSizeMax)(
        IPersistMemory *This,
        ULONG *pCbSize);

    HRESULT (STDMETHODCALLTYPE *InitNew)(
        IPersistMemory *This);

    END_INTERFACE
} IPersistMemoryVtbl;

interface IPersistMemory {
    CONST_VTBL IPersistMemoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistMemory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistMemory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistMemory_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IPersistMemory_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistMemory methods ***/
#define IPersistMemory_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#define IPersistMemory_Load(This,pMem,cbSize) (This)->lpVtbl->Load(This,pMem,cbSize)
#define IPersistMemory_Save(This,pMem,fClearDirty,cbSize) (This)->lpVtbl->Save(This,pMem,fClearDirty,cbSize)
#define IPersistMemory_GetSizeMax(This,pCbSize) (This)->lpVtbl->GetSizeMax(This,pCbSize)
#define IPersistMemory_InitNew(This) (This)->lpVtbl->InitNew(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPersistMemory_QueryInterface(IPersistMemory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPersistMemory_AddRef(IPersistMemory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPersistMemory_Release(IPersistMemory* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static inline HRESULT IPersistMemory_GetClassID(IPersistMemory* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistMemory methods ***/
static inline HRESULT IPersistMemory_IsDirty(IPersistMemory* This) {
    return This->lpVtbl->IsDirty(This);
}
static inline HRESULT IPersistMemory_Load(IPersistMemory* This,LPVOID pMem,ULONG cbSize) {
    return This->lpVtbl->Load(This,pMem,cbSize);
}
static inline HRESULT IPersistMemory_Save(IPersistMemory* This,LPVOID pMem,WINBOOL fClearDirty,ULONG cbSize) {
    return This->lpVtbl->Save(This,pMem,fClearDirty,cbSize);
}
static inline HRESULT IPersistMemory_GetSizeMax(IPersistMemory* This,ULONG *pCbSize) {
    return This->lpVtbl->GetSizeMax(This,pCbSize);
}
static inline HRESULT IPersistMemory_InitNew(IPersistMemory* This) {
    return This->lpVtbl->InitNew(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPersistMemory_RemoteLoad_Proxy(
    IPersistMemory* This,
    BYTE *pMem,
    ULONG cbSize);
void __RPC_STUB IPersistMemory_RemoteLoad_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IPersistMemory_RemoteSave_Proxy(
    IPersistMemory* This,
    BYTE *pMem,
    WINBOOL fClearDirty,
    ULONG cbSize);
void __RPC_STUB IPersistMemory_RemoteSave_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IPersistMemory_Load_Proxy(
    IPersistMemory* This,
    LPVOID pMem,
    ULONG cbSize);
HRESULT __RPC_STUB IPersistMemory_Load_Stub(
    IPersistMemory* This,
    BYTE *pMem,
    ULONG cbSize);
HRESULT CALLBACK IPersistMemory_Save_Proxy(
    IPersistMemory* This,
    LPVOID pMem,
    WINBOOL fClearDirty,
    ULONG cbSize);
HRESULT __RPC_STUB IPersistMemory_Save_Stub(
    IPersistMemory* This,
    BYTE *pMem,
    WINBOOL fClearDirty,
    ULONG cbSize);

#endif  /* __IPersistMemory_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPersistStreamInit interface
 */
#ifndef __IPersistStreamInit_INTERFACE_DEFINED__
#define __IPersistStreamInit_INTERFACE_DEFINED__

typedef IPersistStreamInit *LPPERSISTSTREAMINIT;

DEFINE_GUID(IID_IPersistStreamInit, 0x7fd52380, 0x4e07, 0x101b, 0xae,0x2d, 0x08,0x00,0x2b,0x2e,0xc7,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7fd52380-4e07-101b-ae2d-08002b2ec713")
IPersistStreamInit : public IPersist
{
    virtual HRESULT STDMETHODCALLTYPE IsDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        LPSTREAM pStm) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        LPSTREAM pStm,
        WINBOOL fClearDirty) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSizeMax(
        ULARGE_INTEGER *pCbSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitNew(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistStreamInit, 0x7fd52380, 0x4e07, 0x101b, 0xae,0x2d, 0x08,0x00,0x2b,0x2e,0xc7,0x13)
#endif
#else
typedef struct IPersistStreamInitVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistStreamInit *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistStreamInit *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistStreamInit *This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersistStreamInit *This,
        CLSID *pClassID);

    /*** IPersistStreamInit methods ***/
    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IPersistStreamInit *This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IPersistStreamInit *This,
        LPSTREAM pStm);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IPersistStreamInit *This,
        LPSTREAM pStm,
        WINBOOL fClearDirty);

    HRESULT (STDMETHODCALLTYPE *GetSizeMax)(
        IPersistStreamInit *This,
        ULARGE_INTEGER *pCbSize);

    HRESULT (STDMETHODCALLTYPE *InitNew)(
        IPersistStreamInit *This);

    END_INTERFACE
} IPersistStreamInitVtbl;

interface IPersistStreamInit {
    CONST_VTBL IPersistStreamInitVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistStreamInit_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistStreamInit_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistStreamInit_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IPersistStreamInit_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistStreamInit methods ***/
#define IPersistStreamInit_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#define IPersistStreamInit_Load(This,pStm) (This)->lpVtbl->Load(This,pStm)
#define IPersistStreamInit_Save(This,pStm,fClearDirty) (This)->lpVtbl->Save(This,pStm,fClearDirty)
#define IPersistStreamInit_GetSizeMax(This,pCbSize) (This)->lpVtbl->GetSizeMax(This,pCbSize)
#define IPersistStreamInit_InitNew(This) (This)->lpVtbl->InitNew(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPersistStreamInit_QueryInterface(IPersistStreamInit* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPersistStreamInit_AddRef(IPersistStreamInit* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPersistStreamInit_Release(IPersistStreamInit* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static inline HRESULT IPersistStreamInit_GetClassID(IPersistStreamInit* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistStreamInit methods ***/
static inline HRESULT IPersistStreamInit_IsDirty(IPersistStreamInit* This) {
    return This->lpVtbl->IsDirty(This);
}
static inline HRESULT IPersistStreamInit_Load(IPersistStreamInit* This,LPSTREAM pStm) {
    return This->lpVtbl->Load(This,pStm);
}
static inline HRESULT IPersistStreamInit_Save(IPersistStreamInit* This,LPSTREAM pStm,WINBOOL fClearDirty) {
    return This->lpVtbl->Save(This,pStm,fClearDirty);
}
static inline HRESULT IPersistStreamInit_GetSizeMax(IPersistStreamInit* This,ULARGE_INTEGER *pCbSize) {
    return This->lpVtbl->GetSizeMax(This,pCbSize);
}
static inline HRESULT IPersistStreamInit_InitNew(IPersistStreamInit* This) {
    return This->lpVtbl->InitNew(This);
}
#endif
#endif

#endif


#endif  /* __IPersistStreamInit_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPersistPropertyBag interface
 */
#ifndef __IPersistPropertyBag_INTERFACE_DEFINED__
#define __IPersistPropertyBag_INTERFACE_DEFINED__

typedef IPersistPropertyBag *LPPERSISTPROPERTYBAG;

DEFINE_GUID(IID_IPersistPropertyBag, 0x37d84f60, 0x42cb, 0x11ce, 0x81,0x35, 0x00,0xaa,0x00,0x4b,0xb8,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("37d84f60-42cb-11ce-8135-00aa004bb851")
IPersistPropertyBag : public IPersist
{
    virtual HRESULT STDMETHODCALLTYPE InitNew(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        IPropertyBag *pPropBag,
        IErrorLog *pErrorLog) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        IPropertyBag *pPropBag,
        WINBOOL fClearDirty,
        WINBOOL fSaveAllProperties) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistPropertyBag, 0x37d84f60, 0x42cb, 0x11ce, 0x81,0x35, 0x00,0xaa,0x00,0x4b,0xb8,0x51)
#endif
#else
typedef struct IPersistPropertyBagVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistPropertyBag *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistPropertyBag *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistPropertyBag *This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersistPropertyBag *This,
        CLSID *pClassID);

    /*** IPersistPropertyBag methods ***/
    HRESULT (STDMETHODCALLTYPE *InitNew)(
        IPersistPropertyBag *This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IPersistPropertyBag *This,
        IPropertyBag *pPropBag,
        IErrorLog *pErrorLog);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IPersistPropertyBag *This,
        IPropertyBag *pPropBag,
        WINBOOL fClearDirty,
        WINBOOL fSaveAllProperties);

    END_INTERFACE
} IPersistPropertyBagVtbl;

interface IPersistPropertyBag {
    CONST_VTBL IPersistPropertyBagVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistPropertyBag_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistPropertyBag_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistPropertyBag_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IPersistPropertyBag_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistPropertyBag methods ***/
#define IPersistPropertyBag_InitNew(This) (This)->lpVtbl->InitNew(This)
#define IPersistPropertyBag_Load(This,pPropBag,pErrorLog) (This)->lpVtbl->Load(This,pPropBag,pErrorLog)
#define IPersistPropertyBag_Save(This,pPropBag,fClearDirty,fSaveAllProperties) (This)->lpVtbl->Save(This,pPropBag,fClearDirty,fSaveAllProperties)
#else
/*** IUnknown methods ***/
static inline HRESULT IPersistPropertyBag_QueryInterface(IPersistPropertyBag* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPersistPropertyBag_AddRef(IPersistPropertyBag* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPersistPropertyBag_Release(IPersistPropertyBag* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static inline HRESULT IPersistPropertyBag_GetClassID(IPersistPropertyBag* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistPropertyBag methods ***/
static inline HRESULT IPersistPropertyBag_InitNew(IPersistPropertyBag* This) {
    return This->lpVtbl->InitNew(This);
}
static inline HRESULT IPersistPropertyBag_Load(IPersistPropertyBag* This,IPropertyBag *pPropBag,IErrorLog *pErrorLog) {
    return This->lpVtbl->Load(This,pPropBag,pErrorLog);
}
static inline HRESULT IPersistPropertyBag_Save(IPersistPropertyBag* This,IPropertyBag *pPropBag,WINBOOL fClearDirty,WINBOOL fSaveAllProperties) {
    return This->lpVtbl->Save(This,pPropBag,fClearDirty,fSaveAllProperties);
}
#endif
#endif

#endif


#endif  /* __IPersistPropertyBag_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISimpleFrameSite interface
 */
#ifndef __ISimpleFrameSite_INTERFACE_DEFINED__
#define __ISimpleFrameSite_INTERFACE_DEFINED__

typedef ISimpleFrameSite *LPSIMPLEFRAMESITE;

DEFINE_GUID(IID_ISimpleFrameSite, 0x742b0e01, 0x14e6, 0x101b, 0x91,0x4e, 0x00,0xaa,0x00,0x30,0x0c,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("742b0e01-14e6-101b-914e-00aa00300cab")
ISimpleFrameSite : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PreMessageFilter(
        HWND hWnd,
        UINT msg,
        WPARAM wp,
        LPARAM lp,
        LRESULT *plResult,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE PostMessageFilter(
        HWND hWnd,
        UINT msg,
        WPARAM wp,
        LPARAM lp,
        LRESULT *plResult,
        DWORD dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISimpleFrameSite, 0x742b0e01, 0x14e6, 0x101b, 0x91,0x4e, 0x00,0xaa,0x00,0x30,0x0c,0xab)
#endif
#else
typedef struct ISimpleFrameSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISimpleFrameSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISimpleFrameSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISimpleFrameSite *This);

    /*** ISimpleFrameSite methods ***/
    HRESULT (STDMETHODCALLTYPE *PreMessageFilter)(
        ISimpleFrameSite *This,
        HWND hWnd,
        UINT msg,
        WPARAM wp,
        LPARAM lp,
        LRESULT *plResult,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *PostMessageFilter)(
        ISimpleFrameSite *This,
        HWND hWnd,
        UINT msg,
        WPARAM wp,
        LPARAM lp,
        LRESULT *plResult,
        DWORD dwCookie);

    END_INTERFACE
} ISimpleFrameSiteVtbl;

interface ISimpleFrameSite {
    CONST_VTBL ISimpleFrameSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISimpleFrameSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISimpleFrameSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISimpleFrameSite_Release(This) (This)->lpVtbl->Release(This)
/*** ISimpleFrameSite methods ***/
#define ISimpleFrameSite_PreMessageFilter(This,hWnd,msg,wp,lp,plResult,pdwCookie) (This)->lpVtbl->PreMessageFilter(This,hWnd,msg,wp,lp,plResult,pdwCookie)
#define ISimpleFrameSite_PostMessageFilter(This,hWnd,msg,wp,lp,plResult,dwCookie) (This)->lpVtbl->PostMessageFilter(This,hWnd,msg,wp,lp,plResult,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT ISimpleFrameSite_QueryInterface(ISimpleFrameSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISimpleFrameSite_AddRef(ISimpleFrameSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISimpleFrameSite_Release(ISimpleFrameSite* This) {
    return This->lpVtbl->Release(This);
}
/*** ISimpleFrameSite methods ***/
static inline HRESULT ISimpleFrameSite_PreMessageFilter(ISimpleFrameSite* This,HWND hWnd,UINT msg,WPARAM wp,LPARAM lp,LRESULT *plResult,DWORD *pdwCookie) {
    return This->lpVtbl->PreMessageFilter(This,hWnd,msg,wp,lp,plResult,pdwCookie);
}
static inline HRESULT ISimpleFrameSite_PostMessageFilter(ISimpleFrameSite* This,HWND hWnd,UINT msg,WPARAM wp,LPARAM lp,LRESULT *plResult,DWORD dwCookie) {
    return This->lpVtbl->PostMessageFilter(This,hWnd,msg,wp,lp,plResult,dwCookie);
}
#endif
#endif

#endif


#endif  /* __ISimpleFrameSite_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IFont interface
 */
#ifndef __IFont_INTERFACE_DEFINED__
#define __IFont_INTERFACE_DEFINED__

typedef IFont *LPFONT;

#ifndef OLE2ANSI
typedef TEXTMETRICW TEXTMETRICOLE;
#else
typedef TEXTMETRIC TEXTMETRICOLE;
#endif

typedef TEXTMETRICOLE *LPTEXTMETRICOLE;

DEFINE_GUID(IID_IFont, 0xbef6e002, 0xa874, 0x101a, 0x8b,0xba, 0x00,0xaa,0x00,0x30,0x0c,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bef6e002-a874-101a-8bba-00aa00300cab")
IFont : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Size(
        CY *pSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Size(
        CY size) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Bold(
        WINBOOL *pBold) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Bold(
        WINBOOL bold) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Italic(
        WINBOOL *pItalic) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Italic(
        WINBOOL italic) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Underline(
        WINBOOL *pUnderline) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Underline(
        WINBOOL underline) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Strikethrough(
        WINBOOL *pStrikethrough) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Strikethrough(
        WINBOOL strikethrough) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Weight(
        SHORT *pWeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Weight(
        SHORT weight) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Charset(
        SHORT *pCharset) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Charset(
        SHORT charset) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_hFont(
        HFONT *phFont) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IFont **ppFont) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEqual(
        IFont *pFontOther) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRatio(
        LONG cyLogical,
        LONG cyHimetric) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryTextMetrics(
        TEXTMETRICOLE *pTM) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddRefHfont(
        HFONT hFont) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseHfont(
        HFONT hFont) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHdc(
        HDC hDC) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFont, 0xbef6e002, 0xa874, 0x101a, 0x8b,0xba, 0x00,0xaa,0x00,0x30,0x0c,0xab)
#endif
#else
typedef struct IFontVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFont *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFont *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFont *This);

    /*** IFont methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IFont *This,
        BSTR *pName);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        IFont *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        IFont *This,
        CY *pSize);

    HRESULT (STDMETHODCALLTYPE *put_Size)(
        IFont *This,
        CY size);

    HRESULT (STDMETHODCALLTYPE *get_Bold)(
        IFont *This,
        WINBOOL *pBold);

    HRESULT (STDMETHODCALLTYPE *put_Bold)(
        IFont *This,
        WINBOOL bold);

    HRESULT (STDMETHODCALLTYPE *get_Italic)(
        IFont *This,
        WINBOOL *pItalic);

    HRESULT (STDMETHODCALLTYPE *put_Italic)(
        IFont *This,
        WINBOOL italic);

    HRESULT (STDMETHODCALLTYPE *get_Underline)(
        IFont *This,
        WINBOOL *pUnderline);

    HRESULT (STDMETHODCALLTYPE *put_Underline)(
        IFont *This,
        WINBOOL underline);

    HRESULT (STDMETHODCALLTYPE *get_Strikethrough)(
        IFont *This,
        WINBOOL *pStrikethrough);

    HRESULT (STDMETHODCALLTYPE *put_Strikethrough)(
        IFont *This,
        WINBOOL strikethrough);

    HRESULT (STDMETHODCALLTYPE *get_Weight)(
        IFont *This,
        SHORT *pWeight);

    HRESULT (STDMETHODCALLTYPE *put_Weight)(
        IFont *This,
        SHORT weight);

    HRESULT (STDMETHODCALLTYPE *get_Charset)(
        IFont *This,
        SHORT *pCharset);

    HRESULT (STDMETHODCALLTYPE *put_Charset)(
        IFont *This,
        SHORT charset);

    HRESULT (STDMETHODCALLTYPE *get_hFont)(
        IFont *This,
        HFONT *phFont);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IFont *This,
        IFont **ppFont);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        IFont *This,
        IFont *pFontOther);

    HRESULT (STDMETHODCALLTYPE *SetRatio)(
        IFont *This,
        LONG cyLogical,
        LONG cyHimetric);

    HRESULT (STDMETHODCALLTYPE *QueryTextMetrics)(
        IFont *This,
        TEXTMETRICOLE *pTM);

    HRESULT (STDMETHODCALLTYPE *AddRefHfont)(
        IFont *This,
        HFONT hFont);

    HRESULT (STDMETHODCALLTYPE *ReleaseHfont)(
        IFont *This,
        HFONT hFont);

    HRESULT (STDMETHODCALLTYPE *SetHdc)(
        IFont *This,
        HDC hDC);

    END_INTERFACE
} IFontVtbl;

interface IFont {
    CONST_VTBL IFontVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFont_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFont_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFont_Release(This) (This)->lpVtbl->Release(This)
/*** IFont methods ***/
#define IFont_get_Name(This,pName) (This)->lpVtbl->get_Name(This,pName)
#define IFont_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define IFont_get_Size(This,pSize) (This)->lpVtbl->get_Size(This,pSize)
#define IFont_put_Size(This,size) (This)->lpVtbl->put_Size(This,size)
#define IFont_get_Bold(This,pBold) (This)->lpVtbl->get_Bold(This,pBold)
#define IFont_put_Bold(This,bold) (This)->lpVtbl->put_Bold(This,bold)
#define IFont_get_Italic(This,pItalic) (This)->lpVtbl->get_Italic(This,pItalic)
#define IFont_put_Italic(This,italic) (This)->lpVtbl->put_Italic(This,italic)
#define IFont_get_Underline(This,pUnderline) (This)->lpVtbl->get_Underline(This,pUnderline)
#define IFont_put_Underline(This,underline) (This)->lpVtbl->put_Underline(This,underline)
#define IFont_get_Strikethrough(This,pStrikethrough) (This)->lpVtbl->get_Strikethrough(This,pStrikethrough)
#define IFont_put_Strikethrough(This,strikethrough) (This)->lpVtbl->put_Strikethrough(This,strikethrough)
#define IFont_get_Weight(This,pWeight) (This)->lpVtbl->get_Weight(This,pWeight)
#define IFont_put_Weight(This,weight) (This)->lpVtbl->put_Weight(This,weight)
#define IFont_get_Charset(This,pCharset) (This)->lpVtbl->get_Charset(This,pCharset)
#define IFont_put_Charset(This,charset) (This)->lpVtbl->put_Charset(This,charset)
#define IFont_get_hFont(This,phFont) (This)->lpVtbl->get_hFont(This,phFont)
#define IFont_Clone(This,ppFont) (This)->lpVtbl->Clone(This,ppFont)
#define IFont_IsEqual(This,pFontOther) (This)->lpVtbl->IsEqual(This,pFontOther)
#define IFont_SetRatio(This,cyLogical,cyHimetric) (This)->lpVtbl->SetRatio(This,cyLogical,cyHimetric)
#define IFont_QueryTextMetrics(This,pTM) (This)->lpVtbl->QueryTextMetrics(This,pTM)
#define IFont_AddRefHfont(This,hFont) (This)->lpVtbl->AddRefHfont(This,hFont)
#define IFont_ReleaseHfont(This,hFont) (This)->lpVtbl->ReleaseHfont(This,hFont)
#define IFont_SetHdc(This,hDC) (This)->lpVtbl->SetHdc(This,hDC)
#else
/*** IUnknown methods ***/
static inline HRESULT IFont_QueryInterface(IFont* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFont_AddRef(IFont* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFont_Release(IFont* This) {
    return This->lpVtbl->Release(This);
}
/*** IFont methods ***/
static inline HRESULT IFont_get_Name(IFont* This,BSTR *pName) {
    return This->lpVtbl->get_Name(This,pName);
}
static inline HRESULT IFont_put_Name(IFont* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT IFont_get_Size(IFont* This,CY *pSize) {
    return This->lpVtbl->get_Size(This,pSize);
}
static inline HRESULT IFont_put_Size(IFont* This,CY size) {
    return This->lpVtbl->put_Size(This,size);
}
static inline HRESULT IFont_get_Bold(IFont* This,WINBOOL *pBold) {
    return This->lpVtbl->get_Bold(This,pBold);
}
static inline HRESULT IFont_put_Bold(IFont* This,WINBOOL bold) {
    return This->lpVtbl->put_Bold(This,bold);
}
static inline HRESULT IFont_get_Italic(IFont* This,WINBOOL *pItalic) {
    return This->lpVtbl->get_Italic(This,pItalic);
}
static inline HRESULT IFont_put_Italic(IFont* This,WINBOOL italic) {
    return This->lpVtbl->put_Italic(This,italic);
}
static inline HRESULT IFont_get_Underline(IFont* This,WINBOOL *pUnderline) {
    return This->lpVtbl->get_Underline(This,pUnderline);
}
static inline HRESULT IFont_put_Underline(IFont* This,WINBOOL underline) {
    return This->lpVtbl->put_Underline(This,underline);
}
static inline HRESULT IFont_get_Strikethrough(IFont* This,WINBOOL *pStrikethrough) {
    return This->lpVtbl->get_Strikethrough(This,pStrikethrough);
}
static inline HRESULT IFont_put_Strikethrough(IFont* This,WINBOOL strikethrough) {
    return This->lpVtbl->put_Strikethrough(This,strikethrough);
}
static inline HRESULT IFont_get_Weight(IFont* This,SHORT *pWeight) {
    return This->lpVtbl->get_Weight(This,pWeight);
}
static inline HRESULT IFont_put_Weight(IFont* This,SHORT weight) {
    return This->lpVtbl->put_Weight(This,weight);
}
static inline HRESULT IFont_get_Charset(IFont* This,SHORT *pCharset) {
    return This->lpVtbl->get_Charset(This,pCharset);
}
static inline HRESULT IFont_put_Charset(IFont* This,SHORT charset) {
    return This->lpVtbl->put_Charset(This,charset);
}
static inline HRESULT IFont_get_hFont(IFont* This,HFONT *phFont) {
    return This->lpVtbl->get_hFont(This,phFont);
}
static inline HRESULT IFont_Clone(IFont* This,IFont **ppFont) {
    return This->lpVtbl->Clone(This,ppFont);
}
static inline HRESULT IFont_IsEqual(IFont* This,IFont *pFontOther) {
    return This->lpVtbl->IsEqual(This,pFontOther);
}
static inline HRESULT IFont_SetRatio(IFont* This,LONG cyLogical,LONG cyHimetric) {
    return This->lpVtbl->SetRatio(This,cyLogical,cyHimetric);
}
static inline HRESULT IFont_QueryTextMetrics(IFont* This,TEXTMETRICOLE *pTM) {
    return This->lpVtbl->QueryTextMetrics(This,pTM);
}
static inline HRESULT IFont_AddRefHfont(IFont* This,HFONT hFont) {
    return This->lpVtbl->AddRefHfont(This,hFont);
}
static inline HRESULT IFont_ReleaseHfont(IFont* This,HFONT hFont) {
    return This->lpVtbl->ReleaseHfont(This,hFont);
}
static inline HRESULT IFont_SetHdc(IFont* This,HDC hDC) {
    return This->lpVtbl->SetHdc(This,hDC);
}
#endif
#endif

#endif


#endif  /* __IFont_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPicture interface
 */
#ifndef __IPicture_INTERFACE_DEFINED__
#define __IPicture_INTERFACE_DEFINED__

typedef IPicture *LPPICTURE;

typedef enum tagPictureAttributes {
    PICTURE_SCALABLE = 0x1,
    PICTURE_TRANSPARENT = 0x2
} PICTUREATTRIBUTES;

typedef UINT OLE_HANDLE;
typedef LONG OLE_XPOS_HIMETRIC;
typedef LONG OLE_YPOS_HIMETRIC;
typedef LONG OLE_XSIZE_HIMETRIC;
typedef LONG OLE_YSIZE_HIMETRIC;

DEFINE_GUID(IID_IPicture, 0x7bf80980, 0xbf32, 0x101a, 0x8b,0xbb, 0x00,0xaa,0x00,0x30,0x0c,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7bf80980-bf32-101a-8bbb-00aa00300cab")
IPicture : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Handle(
        OLE_HANDLE *pHandle) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_hPal(
        OLE_HANDLE *phPal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        SHORT *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Width(
        OLE_XSIZE_HIMETRIC *pWidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Height(
        OLE_YSIZE_HIMETRIC *pHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE Render(
        HDC hDC,
        LONG x,
        LONG y,
        LONG cx,
        LONG cy,
        OLE_XPOS_HIMETRIC xSrc,
        OLE_YPOS_HIMETRIC ySrc,
        OLE_XSIZE_HIMETRIC cxSrc,
        OLE_YSIZE_HIMETRIC cySrc,
        LPCRECT pRcWBounds) = 0;

    virtual HRESULT STDMETHODCALLTYPE set_hPal(
        OLE_HANDLE hPal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurDC(
        HDC *phDC) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectPicture(
        HDC hDCIn,
        HDC *phDCOut,
        OLE_HANDLE *phBmpOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_KeepOriginalFormat(
        WINBOOL *pKeep) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_KeepOriginalFormat(
        WINBOOL keep) = 0;

    virtual HRESULT STDMETHODCALLTYPE PictureChanged(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveAsFile(
        LPSTREAM pStream,
        WINBOOL fSaveMemCopy,
        LONG *pCbSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Attributes(
        DWORD *pDwAttr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPicture, 0x7bf80980, 0xbf32, 0x101a, 0x8b,0xbb, 0x00,0xaa,0x00,0x30,0x0c,0xab)
#endif
#else
typedef struct IPictureVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPicture *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPicture *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPicture *This);

    /*** IPicture methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Handle)(
        IPicture *This,
        OLE_HANDLE *pHandle);

    HRESULT (STDMETHODCALLTYPE *get_hPal)(
        IPicture *This,
        OLE_HANDLE *phPal);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IPicture *This,
        SHORT *pType);

    HRESULT (STDMETHODCALLTYPE *get_Width)(
        IPicture *This,
        OLE_XSIZE_HIMETRIC *pWidth);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        IPicture *This,
        OLE_YSIZE_HIMETRIC *pHeight);

    HRESULT (STDMETHODCALLTYPE *Render)(
        IPicture *This,
        HDC hDC,
        LONG x,
        LONG y,
        LONG cx,
        LONG cy,
        OLE_XPOS_HIMETRIC xSrc,
        OLE_YPOS_HIMETRIC ySrc,
        OLE_XSIZE_HIMETRIC cxSrc,
        OLE_YSIZE_HIMETRIC cySrc,
        LPCRECT pRcWBounds);

    HRESULT (STDMETHODCALLTYPE *set_hPal)(
        IPicture *This,
        OLE_HANDLE hPal);

    HRESULT (STDMETHODCALLTYPE *get_CurDC)(
        IPicture *This,
        HDC *phDC);

    HRESULT (STDMETHODCALLTYPE *SelectPicture)(
        IPicture *This,
        HDC hDCIn,
        HDC *phDCOut,
        OLE_HANDLE *phBmpOut);

    HRESULT (STDMETHODCALLTYPE *get_KeepOriginalFormat)(
        IPicture *This,
        WINBOOL *pKeep);

    HRESULT (STDMETHODCALLTYPE *put_KeepOriginalFormat)(
        IPicture *This,
        WINBOOL keep);

    HRESULT (STDMETHODCALLTYPE *PictureChanged)(
        IPicture *This);

    HRESULT (STDMETHODCALLTYPE *SaveAsFile)(
        IPicture *This,
        LPSTREAM pStream,
        WINBOOL fSaveMemCopy,
        LONG *pCbSize);

    HRESULT (STDMETHODCALLTYPE *get_Attributes)(
        IPicture *This,
        DWORD *pDwAttr);

    END_INTERFACE
} IPictureVtbl;

interface IPicture {
    CONST_VTBL IPictureVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPicture_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPicture_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPicture_Release(This) (This)->lpVtbl->Release(This)
/*** IPicture methods ***/
#define IPicture_get_Handle(This,pHandle) (This)->lpVtbl->get_Handle(This,pHandle)
#define IPicture_get_hPal(This,phPal) (This)->lpVtbl->get_hPal(This,phPal)
#define IPicture_get_Type(This,pType) (This)->lpVtbl->get_Type(This,pType)
#define IPicture_get_Width(This,pWidth) (This)->lpVtbl->get_Width(This,pWidth)
#define IPicture_get_Height(This,pHeight) (This)->lpVtbl->get_Height(This,pHeight)
#define IPicture_Render(This,hDC,x,y,cx,cy,xSrc,ySrc,cxSrc,cySrc,pRcWBounds) (This)->lpVtbl->Render(This,hDC,x,y,cx,cy,xSrc,ySrc,cxSrc,cySrc,pRcWBounds)
#define IPicture_set_hPal(This,hPal) (This)->lpVtbl->set_hPal(This,hPal)
#define IPicture_get_CurDC(This,phDC) (This)->lpVtbl->get_CurDC(This,phDC)
#define IPicture_SelectPicture(This,hDCIn,phDCOut,phBmpOut) (This)->lpVtbl->SelectPicture(This,hDCIn,phDCOut,phBmpOut)
#define IPicture_get_KeepOriginalFormat(This,pKeep) (This)->lpVtbl->get_KeepOriginalFormat(This,pKeep)
#define IPicture_put_KeepOriginalFormat(This,keep) (This)->lpVtbl->put_KeepOriginalFormat(This,keep)
#define IPicture_PictureChanged(This) (This)->lpVtbl->PictureChanged(This)
#define IPicture_SaveAsFile(This,pStream,fSaveMemCopy,pCbSize) (This)->lpVtbl->SaveAsFile(This,pStream,fSaveMemCopy,pCbSize)
#define IPicture_get_Attributes(This,pDwAttr) (This)->lpVtbl->get_Attributes(This,pDwAttr)
#else
/*** IUnknown methods ***/
static inline HRESULT IPicture_QueryInterface(IPicture* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPicture_AddRef(IPicture* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPicture_Release(IPicture* This) {
    return This->lpVtbl->Release(This);
}
/*** IPicture methods ***/
static inline HRESULT IPicture_get_Handle(IPicture* This,OLE_HANDLE *pHandle) {
    return This->lpVtbl->get_Handle(This,pHandle);
}
static inline HRESULT IPicture_get_hPal(IPicture* This,OLE_HANDLE *phPal) {
    return This->lpVtbl->get_hPal(This,phPal);
}
static inline HRESULT IPicture_get_Type(IPicture* This,SHORT *pType) {
    return This->lpVtbl->get_Type(This,pType);
}
static inline HRESULT IPicture_get_Width(IPicture* This,OLE_XSIZE_HIMETRIC *pWidth) {
    return This->lpVtbl->get_Width(This,pWidth);
}
static inline HRESULT IPicture_get_Height(IPicture* This,OLE_YSIZE_HIMETRIC *pHeight) {
    return This->lpVtbl->get_Height(This,pHeight);
}
static inline HRESULT IPicture_Render(IPicture* This,HDC hDC,LONG x,LONG y,LONG cx,LONG cy,OLE_XPOS_HIMETRIC xSrc,OLE_YPOS_HIMETRIC ySrc,OLE_XSIZE_HIMETRIC cxSrc,OLE_YSIZE_HIMETRIC cySrc,LPCRECT pRcWBounds) {
    return This->lpVtbl->Render(This,hDC,x,y,cx,cy,xSrc,ySrc,cxSrc,cySrc,pRcWBounds);
}
static inline HRESULT IPicture_set_hPal(IPicture* This,OLE_HANDLE hPal) {
    return This->lpVtbl->set_hPal(This,hPal);
}
static inline HRESULT IPicture_get_CurDC(IPicture* This,HDC *phDC) {
    return This->lpVtbl->get_CurDC(This,phDC);
}
static inline HRESULT IPicture_SelectPicture(IPicture* This,HDC hDCIn,HDC *phDCOut,OLE_HANDLE *phBmpOut) {
    return This->lpVtbl->SelectPicture(This,hDCIn,phDCOut,phBmpOut);
}
static inline HRESULT IPicture_get_KeepOriginalFormat(IPicture* This,WINBOOL *pKeep) {
    return This->lpVtbl->get_KeepOriginalFormat(This,pKeep);
}
static inline HRESULT IPicture_put_KeepOriginalFormat(IPicture* This,WINBOOL keep) {
    return This->lpVtbl->put_KeepOriginalFormat(This,keep);
}
static inline HRESULT IPicture_PictureChanged(IPicture* This) {
    return This->lpVtbl->PictureChanged(This);
}
static inline HRESULT IPicture_SaveAsFile(IPicture* This,LPSTREAM pStream,WINBOOL fSaveMemCopy,LONG *pCbSize) {
    return This->lpVtbl->SaveAsFile(This,pStream,fSaveMemCopy,pCbSize);
}
static inline HRESULT IPicture_get_Attributes(IPicture* This,DWORD *pDwAttr) {
    return This->lpVtbl->get_Attributes(This,pDwAttr);
}
#endif
#endif

#endif


#endif  /* __IPicture_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPicture2 interface
 */
#ifndef __IPicture2_INTERFACE_DEFINED__
#define __IPicture2_INTERFACE_DEFINED__

typedef IPicture2 *LPPICTURE2;
typedef UINT_PTR HHANDLE;

DEFINE_GUID(IID_IPicture2, 0xf5185dd8, 0x2012, 0x4b0b, 0xaa,0xd9, 0xf0,0x52,0xc6,0xbd,0x48,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f5185dd8-2012-4b0b-aad9-f052c6bd482b")
IPicture2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Handle(
        HHANDLE *pHandle) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_hPal(
        HHANDLE *phPal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        SHORT *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Width(
        OLE_XSIZE_HIMETRIC *pWidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Height(
        OLE_YSIZE_HIMETRIC *pHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE Render(
        HDC hDC,
        LONG x,
        LONG y,
        LONG cx,
        LONG cy,
        OLE_XPOS_HIMETRIC xSrc,
        OLE_YPOS_HIMETRIC ySrc,
        OLE_XSIZE_HIMETRIC cxSrc,
        OLE_YSIZE_HIMETRIC cySrc,
        LPCRECT pRcWBounds) = 0;

    virtual HRESULT STDMETHODCALLTYPE set_hPal(
        HHANDLE hPal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurDC(
        HDC *phDC) = 0;

    virtual HRESULT STDMETHODCALLTYPE SelectPicture(
        HDC hDCIn,
        HDC *phDCOut,
        HHANDLE *phBmpOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_KeepOriginalFormat(
        WINBOOL *pKeep) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_KeepOriginalFormat(
        WINBOOL keep) = 0;

    virtual HRESULT STDMETHODCALLTYPE PictureChanged(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveAsFile(
        LPSTREAM pStream,
        WINBOOL fSaveMemCopy,
        LONG *pCbSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Attributes(
        DWORD *pDwAttr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPicture2, 0xf5185dd8, 0x2012, 0x4b0b, 0xaa,0xd9, 0xf0,0x52,0xc6,0xbd,0x48,0x2b)
#endif
#else
typedef struct IPicture2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPicture2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPicture2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPicture2 *This);

    /*** IPicture2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Handle)(
        IPicture2 *This,
        HHANDLE *pHandle);

    HRESULT (STDMETHODCALLTYPE *get_hPal)(
        IPicture2 *This,
        HHANDLE *phPal);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IPicture2 *This,
        SHORT *pType);

    HRESULT (STDMETHODCALLTYPE *get_Width)(
        IPicture2 *This,
        OLE_XSIZE_HIMETRIC *pWidth);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        IPicture2 *This,
        OLE_YSIZE_HIMETRIC *pHeight);

    HRESULT (STDMETHODCALLTYPE *Render)(
        IPicture2 *This,
        HDC hDC,
        LONG x,
        LONG y,
        LONG cx,
        LONG cy,
        OLE_XPOS_HIMETRIC xSrc,
        OLE_YPOS_HIMETRIC ySrc,
        OLE_XSIZE_HIMETRIC cxSrc,
        OLE_YSIZE_HIMETRIC cySrc,
        LPCRECT pRcWBounds);

    HRESULT (STDMETHODCALLTYPE *set_hPal)(
        IPicture2 *This,
        HHANDLE hPal);

    HRESULT (STDMETHODCALLTYPE *get_CurDC)(
        IPicture2 *This,
        HDC *phDC);

    HRESULT (STDMETHODCALLTYPE *SelectPicture)(
        IPicture2 *This,
        HDC hDCIn,
        HDC *phDCOut,
        HHANDLE *phBmpOut);

    HRESULT (STDMETHODCALLTYPE *get_KeepOriginalFormat)(
        IPicture2 *This,
        WINBOOL *pKeep);

    HRESULT (STDMETHODCALLTYPE *put_KeepOriginalFormat)(
        IPicture2 *This,
        WINBOOL keep);

    HRESULT (STDMETHODCALLTYPE *PictureChanged)(
        IPicture2 *This);

    HRESULT (STDMETHODCALLTYPE *SaveAsFile)(
        IPicture2 *This,
        LPSTREAM pStream,
        WINBOOL fSaveMemCopy,
        LONG *pCbSize);

    HRESULT (STDMETHODCALLTYPE *get_Attributes)(
        IPicture2 *This,
        DWORD *pDwAttr);

    END_INTERFACE
} IPicture2Vtbl;

interface IPicture2 {
    CONST_VTBL IPicture2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPicture2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPicture2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPicture2_Release(This) (This)->lpVtbl->Release(This)
/*** IPicture2 methods ***/
#define IPicture2_get_Handle(This,pHandle) (This)->lpVtbl->get_Handle(This,pHandle)
#define IPicture2_get_hPal(This,phPal) (This)->lpVtbl->get_hPal(This,phPal)
#define IPicture2_get_Type(This,pType) (This)->lpVtbl->get_Type(This,pType)
#define IPicture2_get_Width(This,pWidth) (This)->lpVtbl->get_Width(This,pWidth)
#define IPicture2_get_Height(This,pHeight) (This)->lpVtbl->get_Height(This,pHeight)
#define IPicture2_Render(This,hDC,x,y,cx,cy,xSrc,ySrc,cxSrc,cySrc,pRcWBounds) (This)->lpVtbl->Render(This,hDC,x,y,cx,cy,xSrc,ySrc,cxSrc,cySrc,pRcWBounds)
#define IPicture2_set_hPal(This,hPal) (This)->lpVtbl->set_hPal(This,hPal)
#define IPicture2_get_CurDC(This,phDC) (This)->lpVtbl->get_CurDC(This,phDC)
#define IPicture2_SelectPicture(This,hDCIn,phDCOut,phBmpOut) (This)->lpVtbl->SelectPicture(This,hDCIn,phDCOut,phBmpOut)
#define IPicture2_get_KeepOriginalFormat(This,pKeep) (This)->lpVtbl->get_KeepOriginalFormat(This,pKeep)
#define IPicture2_put_KeepOriginalFormat(This,keep) (This)->lpVtbl->put_KeepOriginalFormat(This,keep)
#define IPicture2_PictureChanged(This) (This)->lpVtbl->PictureChanged(This)
#define IPicture2_SaveAsFile(This,pStream,fSaveMemCopy,pCbSize) (This)->lpVtbl->SaveAsFile(This,pStream,fSaveMemCopy,pCbSize)
#define IPicture2_get_Attributes(This,pDwAttr) (This)->lpVtbl->get_Attributes(This,pDwAttr)
#else
/*** IUnknown methods ***/
static inline HRESULT IPicture2_QueryInterface(IPicture2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPicture2_AddRef(IPicture2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPicture2_Release(IPicture2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPicture2 methods ***/
static inline HRESULT IPicture2_get_Handle(IPicture2* This,HHANDLE *pHandle) {
    return This->lpVtbl->get_Handle(This,pHandle);
}
static inline HRESULT IPicture2_get_hPal(IPicture2* This,HHANDLE *phPal) {
    return This->lpVtbl->get_hPal(This,phPal);
}
static inline HRESULT IPicture2_get_Type(IPicture2* This,SHORT *pType) {
    return This->lpVtbl->get_Type(This,pType);
}
static inline HRESULT IPicture2_get_Width(IPicture2* This,OLE_XSIZE_HIMETRIC *pWidth) {
    return This->lpVtbl->get_Width(This,pWidth);
}
static inline HRESULT IPicture2_get_Height(IPicture2* This,OLE_YSIZE_HIMETRIC *pHeight) {
    return This->lpVtbl->get_Height(This,pHeight);
}
static inline HRESULT IPicture2_Render(IPicture2* This,HDC hDC,LONG x,LONG y,LONG cx,LONG cy,OLE_XPOS_HIMETRIC xSrc,OLE_YPOS_HIMETRIC ySrc,OLE_XSIZE_HIMETRIC cxSrc,OLE_YSIZE_HIMETRIC cySrc,LPCRECT pRcWBounds) {
    return This->lpVtbl->Render(This,hDC,x,y,cx,cy,xSrc,ySrc,cxSrc,cySrc,pRcWBounds);
}
static inline HRESULT IPicture2_set_hPal(IPicture2* This,HHANDLE hPal) {
    return This->lpVtbl->set_hPal(This,hPal);
}
static inline HRESULT IPicture2_get_CurDC(IPicture2* This,HDC *phDC) {
    return This->lpVtbl->get_CurDC(This,phDC);
}
static inline HRESULT IPicture2_SelectPicture(IPicture2* This,HDC hDCIn,HDC *phDCOut,HHANDLE *phBmpOut) {
    return This->lpVtbl->SelectPicture(This,hDCIn,phDCOut,phBmpOut);
}
static inline HRESULT IPicture2_get_KeepOriginalFormat(IPicture2* This,WINBOOL *pKeep) {
    return This->lpVtbl->get_KeepOriginalFormat(This,pKeep);
}
static inline HRESULT IPicture2_put_KeepOriginalFormat(IPicture2* This,WINBOOL keep) {
    return This->lpVtbl->put_KeepOriginalFormat(This,keep);
}
static inline HRESULT IPicture2_PictureChanged(IPicture2* This) {
    return This->lpVtbl->PictureChanged(This);
}
static inline HRESULT IPicture2_SaveAsFile(IPicture2* This,LPSTREAM pStream,WINBOOL fSaveMemCopy,LONG *pCbSize) {
    return This->lpVtbl->SaveAsFile(This,pStream,fSaveMemCopy,pCbSize);
}
static inline HRESULT IPicture2_get_Attributes(IPicture2* This,DWORD *pDwAttr) {
    return This->lpVtbl->get_Attributes(This,pDwAttr);
}
#endif
#endif

#endif


#endif  /* __IPicture2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IFontEventsDisp interface
 */
#ifndef __IFontEventsDisp_INTERFACE_DEFINED__
#define __IFontEventsDisp_INTERFACE_DEFINED__

typedef IFontEventsDisp *LPFONTEVENTS;
DEFINE_GUID(IID_IFontEventsDisp, 0x4ef6100a, 0xaf88, 0x11d0, 0x98,0x46, 0x00,0xc0,0x4f,0xc2,0x99,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4ef6100a-af88-11d0-9846-00c04fc29993")
IFontEventsDisp : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFontEventsDisp, 0x4ef6100a, 0xaf88, 0x11d0, 0x98,0x46, 0x00,0xc0,0x4f,0xc2,0x99,0x93)
#endif
#else
typedef struct IFontEventsDispVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFontEventsDisp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFontEventsDisp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFontEventsDisp *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFontEventsDisp *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFontEventsDisp *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFontEventsDisp *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFontEventsDisp *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} IFontEventsDispVtbl;

interface IFontEventsDisp {
    CONST_VTBL IFontEventsDispVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFontEventsDisp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFontEventsDisp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFontEventsDisp_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFontEventsDisp_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFontEventsDisp_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFontEventsDisp_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFontEventsDisp_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT IFontEventsDisp_QueryInterface(IFontEventsDisp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFontEventsDisp_AddRef(IFontEventsDisp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFontEventsDisp_Release(IFontEventsDisp* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFontEventsDisp_GetTypeInfoCount(IFontEventsDisp* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFontEventsDisp_GetTypeInfo(IFontEventsDisp* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFontEventsDisp_GetIDsOfNames(IFontEventsDisp* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFontEventsDisp_Invoke(IFontEventsDisp* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif


#endif  /* __IFontEventsDisp_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IFontDisp interface
 */
#ifndef __IFontDisp_INTERFACE_DEFINED__
#define __IFontDisp_INTERFACE_DEFINED__

typedef IFontDisp *LPFONTDISP;
DEFINE_GUID(IID_IFontDisp, 0xbef6e003, 0xa874, 0x101a, 0x8b,0xba, 0x00,0xaa,0x00,0x30,0x0c,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bef6e003-a874-101a-8bba-00aa00300cab")
IFontDisp : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFontDisp, 0xbef6e003, 0xa874, 0x101a, 0x8b,0xba, 0x00,0xaa,0x00,0x30,0x0c,0xab)
#endif
#else
typedef struct IFontDispVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFontDisp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFontDisp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFontDisp *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IFontDisp *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IFontDisp *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IFontDisp *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IFontDisp *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} IFontDispVtbl;

interface IFontDisp {
    CONST_VTBL IFontDispVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFontDisp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFontDisp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFontDisp_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IFontDisp_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IFontDisp_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IFontDisp_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IFontDisp_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT IFontDisp_QueryInterface(IFontDisp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFontDisp_AddRef(IFontDisp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFontDisp_Release(IFontDisp* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IFontDisp_GetTypeInfoCount(IFontDisp* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IFontDisp_GetTypeInfo(IFontDisp* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IFontDisp_GetIDsOfNames(IFontDisp* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IFontDisp_Invoke(IFontDisp* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif


#endif  /* __IFontDisp_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPictureDisp interface
 */
#ifndef __IPictureDisp_INTERFACE_DEFINED__
#define __IPictureDisp_INTERFACE_DEFINED__

typedef IPictureDisp *LPPICTUREDISP;
DEFINE_GUID(IID_IPictureDisp, 0x7bf80981, 0xbf32, 0x101a, 0x8b,0xbb, 0x00,0xaa,0x00,0x30,0x0c,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7bf80981-bf32-101a-8bbb-00aa00300cab")
IPictureDisp : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPictureDisp, 0x7bf80981, 0xbf32, 0x101a, 0x8b,0xbb, 0x00,0xaa,0x00,0x30,0x0c,0xab)
#endif
#else
typedef struct IPictureDispVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPictureDisp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPictureDisp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPictureDisp *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IPictureDisp *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IPictureDisp *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IPictureDisp *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IPictureDisp *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} IPictureDispVtbl;

interface IPictureDisp {
    CONST_VTBL IPictureDispVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPictureDisp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPictureDisp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPictureDisp_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IPictureDisp_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IPictureDisp_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IPictureDisp_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IPictureDisp_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT IPictureDisp_QueryInterface(IPictureDisp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPictureDisp_AddRef(IPictureDisp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPictureDisp_Release(IPictureDisp* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IPictureDisp_GetTypeInfoCount(IPictureDisp* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IPictureDisp_GetTypeInfo(IPictureDisp* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IPictureDisp_GetIDsOfNames(IPictureDisp* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IPictureDisp_Invoke(IPictureDisp* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif


#endif  /* __IPictureDisp_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleInPlaceObjectWindowless interface
 */
#ifndef __IOleInPlaceObjectWindowless_INTERFACE_DEFINED__
#define __IOleInPlaceObjectWindowless_INTERFACE_DEFINED__

typedef IOleInPlaceObjectWindowless *LPOLEINPLACEOBJECTWINDOWLESS;

DEFINE_GUID(IID_IOleInPlaceObjectWindowless, 0x1c2056cc, 0x5ef4, 0x101b, 0x8b,0xc8, 0x00,0xaa,0x00,0x3e,0x3b,0x29);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1c2056cc-5ef4-101b-8bc8-00aa003e3b29")
IOleInPlaceObjectWindowless : public IOleInPlaceObject
{
    virtual HRESULT STDMETHODCALLTYPE OnWindowMessage(
        UINT msg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDropTarget(
        IDropTarget **ppDropTarget) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleInPlaceObjectWindowless, 0x1c2056cc, 0x5ef4, 0x101b, 0x8b,0xc8, 0x00,0xaa,0x00,0x3e,0x3b,0x29)
#endif
#else
typedef struct IOleInPlaceObjectWindowlessVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleInPlaceObjectWindowless *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleInPlaceObjectWindowless *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleInPlaceObjectWindowless *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleInPlaceObjectWindowless *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleInPlaceObjectWindowless *This,
        WINBOOL fEnterMode);

    /*** IOleInPlaceObject methods ***/
    HRESULT (STDMETHODCALLTYPE *InPlaceDeactivate)(
        IOleInPlaceObjectWindowless *This);

    HRESULT (STDMETHODCALLTYPE *UIDeactivate)(
        IOleInPlaceObjectWindowless *This);

    HRESULT (STDMETHODCALLTYPE *SetObjectRects)(
        IOleInPlaceObjectWindowless *This,
        LPCRECT lprcPosRect,
        LPCRECT lprcClipRect);

    HRESULT (STDMETHODCALLTYPE *ReactivateAndUndo)(
        IOleInPlaceObjectWindowless *This);

    /*** IOleInPlaceObjectWindowless methods ***/
    HRESULT (STDMETHODCALLTYPE *OnWindowMessage)(
        IOleInPlaceObjectWindowless *This,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult);

    HRESULT (STDMETHODCALLTYPE *GetDropTarget)(
        IOleInPlaceObjectWindowless *This,
        IDropTarget **ppDropTarget);

    END_INTERFACE
} IOleInPlaceObjectWindowlessVtbl;

interface IOleInPlaceObjectWindowless {
    CONST_VTBL IOleInPlaceObjectWindowlessVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleInPlaceObjectWindowless_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleInPlaceObjectWindowless_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleInPlaceObjectWindowless_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleInPlaceObjectWindowless_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleInPlaceObjectWindowless_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
/*** IOleInPlaceObject methods ***/
#define IOleInPlaceObjectWindowless_InPlaceDeactivate(This) (This)->lpVtbl->InPlaceDeactivate(This)
#define IOleInPlaceObjectWindowless_UIDeactivate(This) (This)->lpVtbl->UIDeactivate(This)
#define IOleInPlaceObjectWindowless_SetObjectRects(This,lprcPosRect,lprcClipRect) (This)->lpVtbl->SetObjectRects(This,lprcPosRect,lprcClipRect)
#define IOleInPlaceObjectWindowless_ReactivateAndUndo(This) (This)->lpVtbl->ReactivateAndUndo(This)
/*** IOleInPlaceObjectWindowless methods ***/
#define IOleInPlaceObjectWindowless_OnWindowMessage(This,msg,wParam,lParam,plResult) (This)->lpVtbl->OnWindowMessage(This,msg,wParam,lParam,plResult)
#define IOleInPlaceObjectWindowless_GetDropTarget(This,ppDropTarget) (This)->lpVtbl->GetDropTarget(This,ppDropTarget)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleInPlaceObjectWindowless_QueryInterface(IOleInPlaceObjectWindowless* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleInPlaceObjectWindowless_AddRef(IOleInPlaceObjectWindowless* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleInPlaceObjectWindowless_Release(IOleInPlaceObjectWindowless* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleInPlaceObjectWindowless_GetWindow(IOleInPlaceObjectWindowless* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleInPlaceObjectWindowless_ContextSensitiveHelp(IOleInPlaceObjectWindowless* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
/*** IOleInPlaceObject methods ***/
static inline HRESULT IOleInPlaceObjectWindowless_InPlaceDeactivate(IOleInPlaceObjectWindowless* This) {
    return This->lpVtbl->InPlaceDeactivate(This);
}
static inline HRESULT IOleInPlaceObjectWindowless_UIDeactivate(IOleInPlaceObjectWindowless* This) {
    return This->lpVtbl->UIDeactivate(This);
}
static inline HRESULT IOleInPlaceObjectWindowless_SetObjectRects(IOleInPlaceObjectWindowless* This,LPCRECT lprcPosRect,LPCRECT lprcClipRect) {
    return This->lpVtbl->SetObjectRects(This,lprcPosRect,lprcClipRect);
}
static inline HRESULT IOleInPlaceObjectWindowless_ReactivateAndUndo(IOleInPlaceObjectWindowless* This) {
    return This->lpVtbl->ReactivateAndUndo(This);
}
/*** IOleInPlaceObjectWindowless methods ***/
static inline HRESULT IOleInPlaceObjectWindowless_OnWindowMessage(IOleInPlaceObjectWindowless* This,UINT msg,WPARAM wParam,LPARAM lParam,LRESULT *plResult) {
    return This->lpVtbl->OnWindowMessage(This,msg,wParam,lParam,plResult);
}
static inline HRESULT IOleInPlaceObjectWindowless_GetDropTarget(IOleInPlaceObjectWindowless* This,IDropTarget **ppDropTarget) {
    return This->lpVtbl->GetDropTarget(This,ppDropTarget);
}
#endif
#endif

#endif


#endif  /* __IOleInPlaceObjectWindowless_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleInPlaceSiteEx interface
 */
#ifndef __IOleInPlaceSiteEx_INTERFACE_DEFINED__
#define __IOleInPlaceSiteEx_INTERFACE_DEFINED__

typedef IOleInPlaceSiteEx *LPOLEINPLACESITEEX;

typedef enum tagACTIVATEFLAGS {
    ACTIVATE_WINDOWLESS = 1
} ACTIVATEFLAGS;

DEFINE_GUID(IID_IOleInPlaceSiteEx, 0x9c2cad80, 0x3424, 0x11cf, 0xb6,0x70, 0x00,0xaa,0x00,0x4c,0xd6,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c2cad80-3424-11cf-b670-00aa004cd6d8")
IOleInPlaceSiteEx : public IOleInPlaceSite
{
    virtual HRESULT STDMETHODCALLTYPE OnInPlaceActivateEx(
        WINBOOL *pfNoRedraw,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnInPlaceDeactivateEx(
        WINBOOL fNoRedraw) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestUIActivate(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleInPlaceSiteEx, 0x9c2cad80, 0x3424, 0x11cf, 0xb6,0x70, 0x00,0xaa,0x00,0x4c,0xd6,0xd8)
#endif
#else
typedef struct IOleInPlaceSiteExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleInPlaceSiteEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleInPlaceSiteEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleInPlaceSiteEx *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleInPlaceSiteEx *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleInPlaceSiteEx *This,
        WINBOOL fEnterMode);

    /*** IOleInPlaceSite methods ***/
    HRESULT (STDMETHODCALLTYPE *CanInPlaceActivate)(
        IOleInPlaceSiteEx *This);

    HRESULT (STDMETHODCALLTYPE *OnInPlaceActivate)(
        IOleInPlaceSiteEx *This);

    HRESULT (STDMETHODCALLTYPE *OnUIActivate)(
        IOleInPlaceSiteEx *This);

    HRESULT (STDMETHODCALLTYPE *GetWindowContext)(
        IOleInPlaceSiteEx *This,
        IOleInPlaceFrame **ppFrame,
        IOleInPlaceUIWindow **ppDoc,
        LPRECT lprcPosRect,
        LPRECT lprcClipRect,
        LPOLEINPLACEFRAMEINFO lpFrameInfo);

    HRESULT (STDMETHODCALLTYPE *Scroll)(
        IOleInPlaceSiteEx *This,
        SIZE scrollExtant);

    HRESULT (STDMETHODCALLTYPE *OnUIDeactivate)(
        IOleInPlaceSiteEx *This,
        WINBOOL fUndoable);

    HRESULT (STDMETHODCALLTYPE *OnInPlaceDeactivate)(
        IOleInPlaceSiteEx *This);

    HRESULT (STDMETHODCALLTYPE *DiscardUndoState)(
        IOleInPlaceSiteEx *This);

    HRESULT (STDMETHODCALLTYPE *DeactivateAndUndo)(
        IOleInPlaceSiteEx *This);

    HRESULT (STDMETHODCALLTYPE *OnPosRectChange)(
        IOleInPlaceSiteEx *This,
        LPCRECT lprcPosRect);

    /*** IOleInPlaceSiteEx methods ***/
    HRESULT (STDMETHODCALLTYPE *OnInPlaceActivateEx)(
        IOleInPlaceSiteEx *This,
        WINBOOL *pfNoRedraw,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *OnInPlaceDeactivateEx)(
        IOleInPlaceSiteEx *This,
        WINBOOL fNoRedraw);

    HRESULT (STDMETHODCALLTYPE *RequestUIActivate)(
        IOleInPlaceSiteEx *This);

    END_INTERFACE
} IOleInPlaceSiteExVtbl;

interface IOleInPlaceSiteEx {
    CONST_VTBL IOleInPlaceSiteExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleInPlaceSiteEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleInPlaceSiteEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleInPlaceSiteEx_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleInPlaceSiteEx_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleInPlaceSiteEx_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
/*** IOleInPlaceSite methods ***/
#define IOleInPlaceSiteEx_CanInPlaceActivate(This) (This)->lpVtbl->CanInPlaceActivate(This)
#define IOleInPlaceSiteEx_OnInPlaceActivate(This) (This)->lpVtbl->OnInPlaceActivate(This)
#define IOleInPlaceSiteEx_OnUIActivate(This) (This)->lpVtbl->OnUIActivate(This)
#define IOleInPlaceSiteEx_GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo) (This)->lpVtbl->GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo)
#define IOleInPlaceSiteEx_Scroll(This,scrollExtant) (This)->lpVtbl->Scroll(This,scrollExtant)
#define IOleInPlaceSiteEx_OnUIDeactivate(This,fUndoable) (This)->lpVtbl->OnUIDeactivate(This,fUndoable)
#define IOleInPlaceSiteEx_OnInPlaceDeactivate(This) (This)->lpVtbl->OnInPlaceDeactivate(This)
#define IOleInPlaceSiteEx_DiscardUndoState(This) (This)->lpVtbl->DiscardUndoState(This)
#define IOleInPlaceSiteEx_DeactivateAndUndo(This) (This)->lpVtbl->DeactivateAndUndo(This)
#define IOleInPlaceSiteEx_OnPosRectChange(This,lprcPosRect) (This)->lpVtbl->OnPosRectChange(This,lprcPosRect)
/*** IOleInPlaceSiteEx methods ***/
#define IOleInPlaceSiteEx_OnInPlaceActivateEx(This,pfNoRedraw,dwFlags) (This)->lpVtbl->OnInPlaceActivateEx(This,pfNoRedraw,dwFlags)
#define IOleInPlaceSiteEx_OnInPlaceDeactivateEx(This,fNoRedraw) (This)->lpVtbl->OnInPlaceDeactivateEx(This,fNoRedraw)
#define IOleInPlaceSiteEx_RequestUIActivate(This) (This)->lpVtbl->RequestUIActivate(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleInPlaceSiteEx_QueryInterface(IOleInPlaceSiteEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleInPlaceSiteEx_AddRef(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleInPlaceSiteEx_Release(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleInPlaceSiteEx_GetWindow(IOleInPlaceSiteEx* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleInPlaceSiteEx_ContextSensitiveHelp(IOleInPlaceSiteEx* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
/*** IOleInPlaceSite methods ***/
static inline HRESULT IOleInPlaceSiteEx_CanInPlaceActivate(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->CanInPlaceActivate(This);
}
static inline HRESULT IOleInPlaceSiteEx_OnInPlaceActivate(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->OnInPlaceActivate(This);
}
static inline HRESULT IOleInPlaceSiteEx_OnUIActivate(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->OnUIActivate(This);
}
static inline HRESULT IOleInPlaceSiteEx_GetWindowContext(IOleInPlaceSiteEx* This,IOleInPlaceFrame **ppFrame,IOleInPlaceUIWindow **ppDoc,LPRECT lprcPosRect,LPRECT lprcClipRect,LPOLEINPLACEFRAMEINFO lpFrameInfo) {
    return This->lpVtbl->GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo);
}
static inline HRESULT IOleInPlaceSiteEx_Scroll(IOleInPlaceSiteEx* This,SIZE scrollExtant) {
    return This->lpVtbl->Scroll(This,scrollExtant);
}
static inline HRESULT IOleInPlaceSiteEx_OnUIDeactivate(IOleInPlaceSiteEx* This,WINBOOL fUndoable) {
    return This->lpVtbl->OnUIDeactivate(This,fUndoable);
}
static inline HRESULT IOleInPlaceSiteEx_OnInPlaceDeactivate(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->OnInPlaceDeactivate(This);
}
static inline HRESULT IOleInPlaceSiteEx_DiscardUndoState(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->DiscardUndoState(This);
}
static inline HRESULT IOleInPlaceSiteEx_DeactivateAndUndo(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->DeactivateAndUndo(This);
}
static inline HRESULT IOleInPlaceSiteEx_OnPosRectChange(IOleInPlaceSiteEx* This,LPCRECT lprcPosRect) {
    return This->lpVtbl->OnPosRectChange(This,lprcPosRect);
}
/*** IOleInPlaceSiteEx methods ***/
static inline HRESULT IOleInPlaceSiteEx_OnInPlaceActivateEx(IOleInPlaceSiteEx* This,WINBOOL *pfNoRedraw,DWORD dwFlags) {
    return This->lpVtbl->OnInPlaceActivateEx(This,pfNoRedraw,dwFlags);
}
static inline HRESULT IOleInPlaceSiteEx_OnInPlaceDeactivateEx(IOleInPlaceSiteEx* This,WINBOOL fNoRedraw) {
    return This->lpVtbl->OnInPlaceDeactivateEx(This,fNoRedraw);
}
static inline HRESULT IOleInPlaceSiteEx_RequestUIActivate(IOleInPlaceSiteEx* This) {
    return This->lpVtbl->RequestUIActivate(This);
}
#endif
#endif

#endif


#endif  /* __IOleInPlaceSiteEx_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleInPlaceSiteWindowless interface
 */
#ifndef __IOleInPlaceSiteWindowless_INTERFACE_DEFINED__
#define __IOleInPlaceSiteWindowless_INTERFACE_DEFINED__

typedef IOleInPlaceSiteWindowless *LPOLEINPLACESITEWINDOWLESS;

typedef enum tagOLEDCFLAGS {
    OLEDC_NODRAW = 0x1,
    OLEDC_PAINTBKGND = 0x2,
    OLEDC_OFFSCREEN = 0x4
} OLEDCFLAGS;

DEFINE_GUID(IID_IOleInPlaceSiteWindowless, 0x922eada0, 0x3424, 0x11cf, 0xb6,0x70, 0x00,0xaa,0x00,0x4c,0xd6,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("922eada0-3424-11cf-b670-00aa004cd6d8")
IOleInPlaceSiteWindowless : public IOleInPlaceSiteEx
{
    virtual HRESULT STDMETHODCALLTYPE CanWindowlessActivate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCapture(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCapture(
        WINBOOL fCapture) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFocus(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFocus(
        WINBOOL fFocus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDC(
        LPCRECT pRect,
        DWORD grfFlags,
        HDC *phDC) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseDC(
        HDC hDC) = 0;

    virtual HRESULT STDMETHODCALLTYPE InvalidateRect(
        LPCRECT pRect,
        WINBOOL fErase) = 0;

    virtual HRESULT STDMETHODCALLTYPE InvalidateRgn(
        HRGN hRGN,
        WINBOOL fErase) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScrollRect(
        INT dx,
        INT dy,
        LPCRECT pRectScroll,
        LPCRECT pRectClip) = 0;

    virtual HRESULT STDMETHODCALLTYPE AdjustRect(
        LPRECT prc) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDefWindowMessage(
        UINT msg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleInPlaceSiteWindowless, 0x922eada0, 0x3424, 0x11cf, 0xb6,0x70, 0x00,0xaa,0x00,0x4c,0xd6,0xd8)
#endif
#else
typedef struct IOleInPlaceSiteWindowlessVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleInPlaceSiteWindowless *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleInPlaceSiteWindowless *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleInPlaceSiteWindowless *This);

    /*** IOleWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IOleInPlaceSiteWindowless *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *ContextSensitiveHelp)(
        IOleInPlaceSiteWindowless *This,
        WINBOOL fEnterMode);

    /*** IOleInPlaceSite methods ***/
    HRESULT (STDMETHODCALLTYPE *CanInPlaceActivate)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *OnInPlaceActivate)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *OnUIActivate)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *GetWindowContext)(
        IOleInPlaceSiteWindowless *This,
        IOleInPlaceFrame **ppFrame,
        IOleInPlaceUIWindow **ppDoc,
        LPRECT lprcPosRect,
        LPRECT lprcClipRect,
        LPOLEINPLACEFRAMEINFO lpFrameInfo);

    HRESULT (STDMETHODCALLTYPE *Scroll)(
        IOleInPlaceSiteWindowless *This,
        SIZE scrollExtant);

    HRESULT (STDMETHODCALLTYPE *OnUIDeactivate)(
        IOleInPlaceSiteWindowless *This,
        WINBOOL fUndoable);

    HRESULT (STDMETHODCALLTYPE *OnInPlaceDeactivate)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *DiscardUndoState)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *DeactivateAndUndo)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *OnPosRectChange)(
        IOleInPlaceSiteWindowless *This,
        LPCRECT lprcPosRect);

    /*** IOleInPlaceSiteEx methods ***/
    HRESULT (STDMETHODCALLTYPE *OnInPlaceActivateEx)(
        IOleInPlaceSiteWindowless *This,
        WINBOOL *pfNoRedraw,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *OnInPlaceDeactivateEx)(
        IOleInPlaceSiteWindowless *This,
        WINBOOL fNoRedraw);

    HRESULT (STDMETHODCALLTYPE *RequestUIActivate)(
        IOleInPlaceSiteWindowless *This);

    /*** IOleInPlaceSiteWindowless methods ***/
    HRESULT (STDMETHODCALLTYPE *CanWindowlessActivate)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *GetCapture)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *SetCapture)(
        IOleInPlaceSiteWindowless *This,
        WINBOOL fCapture);

    HRESULT (STDMETHODCALLTYPE *GetFocus)(
        IOleInPlaceSiteWindowless *This);

    HRESULT (STDMETHODCALLTYPE *SetFocus)(
        IOleInPlaceSiteWindowless *This,
        WINBOOL fFocus);

    HRESULT (STDMETHODCALLTYPE *GetDC)(
        IOleInPlaceSiteWindowless *This,
        LPCRECT pRect,
        DWORD grfFlags,
        HDC *phDC);

    HRESULT (STDMETHODCALLTYPE *ReleaseDC)(
        IOleInPlaceSiteWindowless *This,
        HDC hDC);

    HRESULT (STDMETHODCALLTYPE *InvalidateRect)(
        IOleInPlaceSiteWindowless *This,
        LPCRECT pRect,
        WINBOOL fErase);

    HRESULT (STDMETHODCALLTYPE *InvalidateRgn)(
        IOleInPlaceSiteWindowless *This,
        HRGN hRGN,
        WINBOOL fErase);

    HRESULT (STDMETHODCALLTYPE *ScrollRect)(
        IOleInPlaceSiteWindowless *This,
        INT dx,
        INT dy,
        LPCRECT pRectScroll,
        LPCRECT pRectClip);

    HRESULT (STDMETHODCALLTYPE *AdjustRect)(
        IOleInPlaceSiteWindowless *This,
        LPRECT prc);

    HRESULT (STDMETHODCALLTYPE *OnDefWindowMessage)(
        IOleInPlaceSiteWindowless *This,
        UINT msg,
        WPARAM wParam,
        LPARAM lParam,
        LRESULT *plResult);

    END_INTERFACE
} IOleInPlaceSiteWindowlessVtbl;

interface IOleInPlaceSiteWindowless {
    CONST_VTBL IOleInPlaceSiteWindowlessVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleInPlaceSiteWindowless_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleInPlaceSiteWindowless_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleInPlaceSiteWindowless_Release(This) (This)->lpVtbl->Release(This)
/*** IOleWindow methods ***/
#define IOleInPlaceSiteWindowless_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IOleInPlaceSiteWindowless_ContextSensitiveHelp(This,fEnterMode) (This)->lpVtbl->ContextSensitiveHelp(This,fEnterMode)
/*** IOleInPlaceSite methods ***/
#define IOleInPlaceSiteWindowless_CanInPlaceActivate(This) (This)->lpVtbl->CanInPlaceActivate(This)
#define IOleInPlaceSiteWindowless_OnInPlaceActivate(This) (This)->lpVtbl->OnInPlaceActivate(This)
#define IOleInPlaceSiteWindowless_OnUIActivate(This) (This)->lpVtbl->OnUIActivate(This)
#define IOleInPlaceSiteWindowless_GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo) (This)->lpVtbl->GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo)
#define IOleInPlaceSiteWindowless_Scroll(This,scrollExtant) (This)->lpVtbl->Scroll(This,scrollExtant)
#define IOleInPlaceSiteWindowless_OnUIDeactivate(This,fUndoable) (This)->lpVtbl->OnUIDeactivate(This,fUndoable)
#define IOleInPlaceSiteWindowless_OnInPlaceDeactivate(This) (This)->lpVtbl->OnInPlaceDeactivate(This)
#define IOleInPlaceSiteWindowless_DiscardUndoState(This) (This)->lpVtbl->DiscardUndoState(This)
#define IOleInPlaceSiteWindowless_DeactivateAndUndo(This) (This)->lpVtbl->DeactivateAndUndo(This)
#define IOleInPlaceSiteWindowless_OnPosRectChange(This,lprcPosRect) (This)->lpVtbl->OnPosRectChange(This,lprcPosRect)
/*** IOleInPlaceSiteEx methods ***/
#define IOleInPlaceSiteWindowless_OnInPlaceActivateEx(This,pfNoRedraw,dwFlags) (This)->lpVtbl->OnInPlaceActivateEx(This,pfNoRedraw,dwFlags)
#define IOleInPlaceSiteWindowless_OnInPlaceDeactivateEx(This,fNoRedraw) (This)->lpVtbl->OnInPlaceDeactivateEx(This,fNoRedraw)
#define IOleInPlaceSiteWindowless_RequestUIActivate(This) (This)->lpVtbl->RequestUIActivate(This)
/*** IOleInPlaceSiteWindowless methods ***/
#define IOleInPlaceSiteWindowless_CanWindowlessActivate(This) (This)->lpVtbl->CanWindowlessActivate(This)
#define IOleInPlaceSiteWindowless_GetCapture(This) (This)->lpVtbl->GetCapture(This)
#define IOleInPlaceSiteWindowless_SetCapture(This,fCapture) (This)->lpVtbl->SetCapture(This,fCapture)
#define IOleInPlaceSiteWindowless_GetFocus(This) (This)->lpVtbl->GetFocus(This)
#define IOleInPlaceSiteWindowless_SetFocus(This,fFocus) (This)->lpVtbl->SetFocus(This,fFocus)
#define IOleInPlaceSiteWindowless_GetDC(This,pRect,grfFlags,phDC) (This)->lpVtbl->GetDC(This,pRect,grfFlags,phDC)
#define IOleInPlaceSiteWindowless_ReleaseDC(This,hDC) (This)->lpVtbl->ReleaseDC(This,hDC)
#define IOleInPlaceSiteWindowless_InvalidateRect(This,pRect,fErase) (This)->lpVtbl->InvalidateRect(This,pRect,fErase)
#define IOleInPlaceSiteWindowless_InvalidateRgn(This,hRGN,fErase) (This)->lpVtbl->InvalidateRgn(This,hRGN,fErase)
#define IOleInPlaceSiteWindowless_ScrollRect(This,dx,dy,pRectScroll,pRectClip) (This)->lpVtbl->ScrollRect(This,dx,dy,pRectScroll,pRectClip)
#define IOleInPlaceSiteWindowless_AdjustRect(This,prc) (This)->lpVtbl->AdjustRect(This,prc)
#define IOleInPlaceSiteWindowless_OnDefWindowMessage(This,msg,wParam,lParam,plResult) (This)->lpVtbl->OnDefWindowMessage(This,msg,wParam,lParam,plResult)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleInPlaceSiteWindowless_QueryInterface(IOleInPlaceSiteWindowless* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleInPlaceSiteWindowless_AddRef(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleInPlaceSiteWindowless_Release(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleWindow methods ***/
static inline HRESULT IOleInPlaceSiteWindowless_GetWindow(IOleInPlaceSiteWindowless* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IOleInPlaceSiteWindowless_ContextSensitiveHelp(IOleInPlaceSiteWindowless* This,WINBOOL fEnterMode) {
    return This->lpVtbl->ContextSensitiveHelp(This,fEnterMode);
}
/*** IOleInPlaceSite methods ***/
static inline HRESULT IOleInPlaceSiteWindowless_CanInPlaceActivate(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->CanInPlaceActivate(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_OnInPlaceActivate(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->OnInPlaceActivate(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_OnUIActivate(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->OnUIActivate(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_GetWindowContext(IOleInPlaceSiteWindowless* This,IOleInPlaceFrame **ppFrame,IOleInPlaceUIWindow **ppDoc,LPRECT lprcPosRect,LPRECT lprcClipRect,LPOLEINPLACEFRAMEINFO lpFrameInfo) {
    return This->lpVtbl->GetWindowContext(This,ppFrame,ppDoc,lprcPosRect,lprcClipRect,lpFrameInfo);
}
static inline HRESULT IOleInPlaceSiteWindowless_Scroll(IOleInPlaceSiteWindowless* This,SIZE scrollExtant) {
    return This->lpVtbl->Scroll(This,scrollExtant);
}
static inline HRESULT IOleInPlaceSiteWindowless_OnUIDeactivate(IOleInPlaceSiteWindowless* This,WINBOOL fUndoable) {
    return This->lpVtbl->OnUIDeactivate(This,fUndoable);
}
static inline HRESULT IOleInPlaceSiteWindowless_OnInPlaceDeactivate(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->OnInPlaceDeactivate(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_DiscardUndoState(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->DiscardUndoState(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_DeactivateAndUndo(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->DeactivateAndUndo(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_OnPosRectChange(IOleInPlaceSiteWindowless* This,LPCRECT lprcPosRect) {
    return This->lpVtbl->OnPosRectChange(This,lprcPosRect);
}
/*** IOleInPlaceSiteEx methods ***/
static inline HRESULT IOleInPlaceSiteWindowless_OnInPlaceActivateEx(IOleInPlaceSiteWindowless* This,WINBOOL *pfNoRedraw,DWORD dwFlags) {
    return This->lpVtbl->OnInPlaceActivateEx(This,pfNoRedraw,dwFlags);
}
static inline HRESULT IOleInPlaceSiteWindowless_OnInPlaceDeactivateEx(IOleInPlaceSiteWindowless* This,WINBOOL fNoRedraw) {
    return This->lpVtbl->OnInPlaceDeactivateEx(This,fNoRedraw);
}
static inline HRESULT IOleInPlaceSiteWindowless_RequestUIActivate(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->RequestUIActivate(This);
}
/*** IOleInPlaceSiteWindowless methods ***/
static inline HRESULT IOleInPlaceSiteWindowless_CanWindowlessActivate(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->CanWindowlessActivate(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_GetCapture(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->GetCapture(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_SetCapture(IOleInPlaceSiteWindowless* This,WINBOOL fCapture) {
    return This->lpVtbl->SetCapture(This,fCapture);
}
static inline HRESULT IOleInPlaceSiteWindowless_GetFocus(IOleInPlaceSiteWindowless* This) {
    return This->lpVtbl->GetFocus(This);
}
static inline HRESULT IOleInPlaceSiteWindowless_SetFocus(IOleInPlaceSiteWindowless* This,WINBOOL fFocus) {
    return This->lpVtbl->SetFocus(This,fFocus);
}
static inline HRESULT IOleInPlaceSiteWindowless_GetDC(IOleInPlaceSiteWindowless* This,LPCRECT pRect,DWORD grfFlags,HDC *phDC) {
    return This->lpVtbl->GetDC(This,pRect,grfFlags,phDC);
}
static inline HRESULT IOleInPlaceSiteWindowless_ReleaseDC(IOleInPlaceSiteWindowless* This,HDC hDC) {
    return This->lpVtbl->ReleaseDC(This,hDC);
}
static inline HRESULT IOleInPlaceSiteWindowless_InvalidateRect(IOleInPlaceSiteWindowless* This,LPCRECT pRect,WINBOOL fErase) {
    return This->lpVtbl->InvalidateRect(This,pRect,fErase);
}
static inline HRESULT IOleInPlaceSiteWindowless_InvalidateRgn(IOleInPlaceSiteWindowless* This,HRGN hRGN,WINBOOL fErase) {
    return This->lpVtbl->InvalidateRgn(This,hRGN,fErase);
}
static inline HRESULT IOleInPlaceSiteWindowless_ScrollRect(IOleInPlaceSiteWindowless* This,INT dx,INT dy,LPCRECT pRectScroll,LPCRECT pRectClip) {
    return This->lpVtbl->ScrollRect(This,dx,dy,pRectScroll,pRectClip);
}
static inline HRESULT IOleInPlaceSiteWindowless_AdjustRect(IOleInPlaceSiteWindowless* This,LPRECT prc) {
    return This->lpVtbl->AdjustRect(This,prc);
}
static inline HRESULT IOleInPlaceSiteWindowless_OnDefWindowMessage(IOleInPlaceSiteWindowless* This,UINT msg,WPARAM wParam,LPARAM lParam,LRESULT *plResult) {
    return This->lpVtbl->OnDefWindowMessage(This,msg,wParam,lParam,plResult);
}
#endif
#endif

#endif


#endif  /* __IOleInPlaceSiteWindowless_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IViewObjectEx interface
 */
#ifndef __IViewObjectEx_INTERFACE_DEFINED__
#define __IViewObjectEx_INTERFACE_DEFINED__

typedef IViewObjectEx *LPVIEWOBJECTEX;

typedef enum tagVIEWSTATUS {
    VIEWSTATUS_OPAQUE = 1,
    VIEWSTATUS_SOLIDBKGND = 2,
    VIEWSTATUS_DVASPECTOPAQUE = 4,
    VIEWSTATUS_DVASPECTTRANSPARENT = 8,
    VIEWSTATUS_SURFACE = 16,
    VIEWSTATUS_3DSURFACE = 32
} VIEWSTATUS;

typedef enum tagHITRESULT {
    HITRESULT_OUTSIDE = 0,
    HITRESULT_TRANSPARENT = 1,
    HITRESULT_CLOSE = 2,
    HITRESULT_HIT = 3
} HITRESULT;

typedef enum tagDVASPECT2 {
    DVASPECT_OPAQUE = 16,
    DVASPECT_TRANSPARENT = 32
} DVASPECT2;

typedef struct tagExtentInfo {
    ULONG cb;
    DWORD dwExtentMode;
    SIZEL sizelProposed;
} DVEXTENTINFO;

typedef enum tagExtentMode {
    DVEXTENT_CONTENT = 0,
    DVEXTENT_INTEGRAL = 1
} DVEXTENTMODE;

typedef enum tagAspectInfoFlag {
    DVASPECTINFOFLAG_CANOPTIMIZE = 1
} DVASPECTINFOFLAG;

typedef struct tagAspectInfo {
    ULONG cb;
    DWORD dwFlags;
} DVASPECTINFO;

DEFINE_GUID(IID_IViewObjectEx, 0x3af24292, 0x0c96, 0x11ce, 0xa0,0xcf, 0x00,0xaa,0x00,0x60,0x0a,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3af24292-0c96-11ce-a0cf-00aa00600ab8")
IViewObjectEx : public IViewObject2
{
    virtual HRESULT STDMETHODCALLTYPE GetRect(
        DWORD dwAspect,
        LPRECTL pRect) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetViewStatus(
        DWORD *pdwStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryHitPoint(
        DWORD dwAspect,
        LPCRECT pRectBounds,
        POINT ptlLoc,
        LONG lCloseHint,
        DWORD *pHitResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryHitRect(
        DWORD dwAspect,
        LPCRECT pRectBounds,
        LPCRECT pRectLoc,
        LONG lCloseHint,
        DWORD *pHitResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNaturalExtent(
        DWORD dwAspect,
        LONG lindex,
        DVTARGETDEVICE *ptd,
        HDC hicTargetDev,
        DVEXTENTINFO *pExtentInfo,
        LPSIZEL pSizel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IViewObjectEx, 0x3af24292, 0x0c96, 0x11ce, 0xa0,0xcf, 0x00,0xaa,0x00,0x60,0x0a,0xb8)
#endif
#else
typedef struct IViewObjectExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IViewObjectEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IViewObjectEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IViewObjectEx *This);

    /*** IViewObject methods ***/
    HRESULT (STDMETHODCALLTYPE *Draw)(
        IViewObjectEx *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DVTARGETDEVICE *ptd,
        HDC hdcTargetDev,
        HDC hdcDraw,
        LPCRECTL lprcBounds,
        LPCRECTL lprcWBounds,
        WINBOOL (STDMETHODCALLTYPE *pfnContinue)(ULONG_PTR dwContinue),
        ULONG_PTR dwContinue);

    HRESULT (STDMETHODCALLTYPE *GetColorSet)(
        IViewObjectEx *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DVTARGETDEVICE *ptd,
        HDC hicTargetDev,
        LOGPALETTE **ppColorSet);

    HRESULT (STDMETHODCALLTYPE *Freeze)(
        IViewObjectEx *This,
        DWORD dwDrawAspect,
        LONG lindex,
        void *pvAspect,
        DWORD *pdwFreeze);

    HRESULT (STDMETHODCALLTYPE *Unfreeze)(
        IViewObjectEx *This,
        DWORD dwFreeze);

    HRESULT (STDMETHODCALLTYPE *SetAdvise)(
        IViewObjectEx *This,
        DWORD aspects,
        DWORD advf,
        IAdviseSink *pAdvSink);

    HRESULT (STDMETHODCALLTYPE *GetAdvise)(
        IViewObjectEx *This,
        DWORD *pAspects,
        DWORD *pAdvf,
        IAdviseSink **ppAdvSink);

    /*** IViewObject2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExtent)(
        IViewObjectEx *This,
        DWORD dwDrawAspect,
        LONG lindex,
        DVTARGETDEVICE *ptd,
        LPSIZEL lpsizel);

    /*** IViewObjectEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRect)(
        IViewObjectEx *This,
        DWORD dwAspect,
        LPRECTL pRect);

    HRESULT (STDMETHODCALLTYPE *GetViewStatus)(
        IViewObjectEx *This,
        DWORD *pdwStatus);

    HRESULT (STDMETHODCALLTYPE *QueryHitPoint)(
        IViewObjectEx *This,
        DWORD dwAspect,
        LPCRECT pRectBounds,
        POINT ptlLoc,
        LONG lCloseHint,
        DWORD *pHitResult);

    HRESULT (STDMETHODCALLTYPE *QueryHitRect)(
        IViewObjectEx *This,
        DWORD dwAspect,
        LPCRECT pRectBounds,
        LPCRECT pRectLoc,
        LONG lCloseHint,
        DWORD *pHitResult);

    HRESULT (STDMETHODCALLTYPE *GetNaturalExtent)(
        IViewObjectEx *This,
        DWORD dwAspect,
        LONG lindex,
        DVTARGETDEVICE *ptd,
        HDC hicTargetDev,
        DVEXTENTINFO *pExtentInfo,
        LPSIZEL pSizel);

    END_INTERFACE
} IViewObjectExVtbl;

interface IViewObjectEx {
    CONST_VTBL IViewObjectExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IViewObjectEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IViewObjectEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IViewObjectEx_Release(This) (This)->lpVtbl->Release(This)
/*** IViewObject methods ***/
#define IViewObjectEx_Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue) (This)->lpVtbl->Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue)
#define IViewObjectEx_GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet) (This)->lpVtbl->GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet)
#define IViewObjectEx_Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze) (This)->lpVtbl->Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze)
#define IViewObjectEx_Unfreeze(This,dwFreeze) (This)->lpVtbl->Unfreeze(This,dwFreeze)
#define IViewObjectEx_SetAdvise(This,aspects,advf,pAdvSink) (This)->lpVtbl->SetAdvise(This,aspects,advf,pAdvSink)
#define IViewObjectEx_GetAdvise(This,pAspects,pAdvf,ppAdvSink) (This)->lpVtbl->GetAdvise(This,pAspects,pAdvf,ppAdvSink)
/*** IViewObject2 methods ***/
#define IViewObjectEx_GetExtent(This,dwDrawAspect,lindex,ptd,lpsizel) (This)->lpVtbl->GetExtent(This,dwDrawAspect,lindex,ptd,lpsizel)
/*** IViewObjectEx methods ***/
#define IViewObjectEx_GetRect(This,dwAspect,pRect) (This)->lpVtbl->GetRect(This,dwAspect,pRect)
#define IViewObjectEx_GetViewStatus(This,pdwStatus) (This)->lpVtbl->GetViewStatus(This,pdwStatus)
#define IViewObjectEx_QueryHitPoint(This,dwAspect,pRectBounds,ptlLoc,lCloseHint,pHitResult) (This)->lpVtbl->QueryHitPoint(This,dwAspect,pRectBounds,ptlLoc,lCloseHint,pHitResult)
#define IViewObjectEx_QueryHitRect(This,dwAspect,pRectBounds,pRectLoc,lCloseHint,pHitResult) (This)->lpVtbl->QueryHitRect(This,dwAspect,pRectBounds,pRectLoc,lCloseHint,pHitResult)
#define IViewObjectEx_GetNaturalExtent(This,dwAspect,lindex,ptd,hicTargetDev,pExtentInfo,pSizel) (This)->lpVtbl->GetNaturalExtent(This,dwAspect,lindex,ptd,hicTargetDev,pExtentInfo,pSizel)
#else
/*** IUnknown methods ***/
static inline HRESULT IViewObjectEx_QueryInterface(IViewObjectEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IViewObjectEx_AddRef(IViewObjectEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IViewObjectEx_Release(IViewObjectEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IViewObject methods ***/
static inline HRESULT IViewObjectEx_Draw(IViewObjectEx* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DVTARGETDEVICE *ptd,HDC hdcTargetDev,HDC hdcDraw,LPCRECTL lprcBounds,LPCRECTL lprcWBounds,WINBOOL (STDMETHODCALLTYPE *pfnContinue)(ULONG_PTR dwContinue),ULONG_PTR dwContinue) {
    return This->lpVtbl->Draw(This,dwDrawAspect,lindex,pvAspect,ptd,hdcTargetDev,hdcDraw,lprcBounds,lprcWBounds,pfnContinue,dwContinue);
}
static inline HRESULT IViewObjectEx_GetColorSet(IViewObjectEx* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DVTARGETDEVICE *ptd,HDC hicTargetDev,LOGPALETTE **ppColorSet) {
    return This->lpVtbl->GetColorSet(This,dwDrawAspect,lindex,pvAspect,ptd,hicTargetDev,ppColorSet);
}
static inline HRESULT IViewObjectEx_Freeze(IViewObjectEx* This,DWORD dwDrawAspect,LONG lindex,void *pvAspect,DWORD *pdwFreeze) {
    return This->lpVtbl->Freeze(This,dwDrawAspect,lindex,pvAspect,pdwFreeze);
}
static inline HRESULT IViewObjectEx_Unfreeze(IViewObjectEx* This,DWORD dwFreeze) {
    return This->lpVtbl->Unfreeze(This,dwFreeze);
}
static inline HRESULT IViewObjectEx_SetAdvise(IViewObjectEx* This,DWORD aspects,DWORD advf,IAdviseSink *pAdvSink) {
    return This->lpVtbl->SetAdvise(This,aspects,advf,pAdvSink);
}
static inline HRESULT IViewObjectEx_GetAdvise(IViewObjectEx* This,DWORD *pAspects,DWORD *pAdvf,IAdviseSink **ppAdvSink) {
    return This->lpVtbl->GetAdvise(This,pAspects,pAdvf,ppAdvSink);
}
/*** IViewObject2 methods ***/
static inline HRESULT IViewObjectEx_GetExtent(IViewObjectEx* This,DWORD dwDrawAspect,LONG lindex,DVTARGETDEVICE *ptd,LPSIZEL lpsizel) {
    return This->lpVtbl->GetExtent(This,dwDrawAspect,lindex,ptd,lpsizel);
}
/*** IViewObjectEx methods ***/
static inline HRESULT IViewObjectEx_GetRect(IViewObjectEx* This,DWORD dwAspect,LPRECTL pRect) {
    return This->lpVtbl->GetRect(This,dwAspect,pRect);
}
static inline HRESULT IViewObjectEx_GetViewStatus(IViewObjectEx* This,DWORD *pdwStatus) {
    return This->lpVtbl->GetViewStatus(This,pdwStatus);
}
static inline HRESULT IViewObjectEx_QueryHitPoint(IViewObjectEx* This,DWORD dwAspect,LPCRECT pRectBounds,POINT ptlLoc,LONG lCloseHint,DWORD *pHitResult) {
    return This->lpVtbl->QueryHitPoint(This,dwAspect,pRectBounds,ptlLoc,lCloseHint,pHitResult);
}
static inline HRESULT IViewObjectEx_QueryHitRect(IViewObjectEx* This,DWORD dwAspect,LPCRECT pRectBounds,LPCRECT pRectLoc,LONG lCloseHint,DWORD *pHitResult) {
    return This->lpVtbl->QueryHitRect(This,dwAspect,pRectBounds,pRectLoc,lCloseHint,pHitResult);
}
static inline HRESULT IViewObjectEx_GetNaturalExtent(IViewObjectEx* This,DWORD dwAspect,LONG lindex,DVTARGETDEVICE *ptd,HDC hicTargetDev,DVEXTENTINFO *pExtentInfo,LPSIZEL pSizel) {
    return This->lpVtbl->GetNaturalExtent(This,dwAspect,lindex,ptd,hicTargetDev,pExtentInfo,pSizel);
}
#endif
#endif

#endif


#endif  /* __IViewObjectEx_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleUndoUnit interface
 */
#ifndef __IOleUndoUnit_INTERFACE_DEFINED__
#define __IOleUndoUnit_INTERFACE_DEFINED__

typedef IOleUndoUnit *LPOLEUNDOUNIT;

DEFINE_GUID(IID_IOleUndoUnit, 0x894ad3b0, 0xef97, 0x11ce, 0x9b,0xc9, 0x00,0xaa,0x00,0x60,0x8e,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("894ad3b0-ef97-11ce-9bc9-00aa00608e01")
IOleUndoUnit : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Do(
        IOleUndoManager *pUndoManager) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        BSTR *pBstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUnitType(
        CLSID *pClsid,
        LONG *plID) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnNextAdd(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleUndoUnit, 0x894ad3b0, 0xef97, 0x11ce, 0x9b,0xc9, 0x00,0xaa,0x00,0x60,0x8e,0x01)
#endif
#else
typedef struct IOleUndoUnitVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleUndoUnit *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleUndoUnit *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleUndoUnit *This);

    /*** IOleUndoUnit methods ***/
    HRESULT (STDMETHODCALLTYPE *Do)(
        IOleUndoUnit *This,
        IOleUndoManager *pUndoManager);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IOleUndoUnit *This,
        BSTR *pBstr);

    HRESULT (STDMETHODCALLTYPE *GetUnitType)(
        IOleUndoUnit *This,
        CLSID *pClsid,
        LONG *plID);

    HRESULT (STDMETHODCALLTYPE *OnNextAdd)(
        IOleUndoUnit *This);

    END_INTERFACE
} IOleUndoUnitVtbl;

interface IOleUndoUnit {
    CONST_VTBL IOleUndoUnitVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleUndoUnit_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleUndoUnit_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleUndoUnit_Release(This) (This)->lpVtbl->Release(This)
/*** IOleUndoUnit methods ***/
#define IOleUndoUnit_Do(This,pUndoManager) (This)->lpVtbl->Do(This,pUndoManager)
#define IOleUndoUnit_GetDescription(This,pBstr) (This)->lpVtbl->GetDescription(This,pBstr)
#define IOleUndoUnit_GetUnitType(This,pClsid,plID) (This)->lpVtbl->GetUnitType(This,pClsid,plID)
#define IOleUndoUnit_OnNextAdd(This) (This)->lpVtbl->OnNextAdd(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleUndoUnit_QueryInterface(IOleUndoUnit* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleUndoUnit_AddRef(IOleUndoUnit* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleUndoUnit_Release(IOleUndoUnit* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleUndoUnit methods ***/
static inline HRESULT IOleUndoUnit_Do(IOleUndoUnit* This,IOleUndoManager *pUndoManager) {
    return This->lpVtbl->Do(This,pUndoManager);
}
static inline HRESULT IOleUndoUnit_GetDescription(IOleUndoUnit* This,BSTR *pBstr) {
    return This->lpVtbl->GetDescription(This,pBstr);
}
static inline HRESULT IOleUndoUnit_GetUnitType(IOleUndoUnit* This,CLSID *pClsid,LONG *plID) {
    return This->lpVtbl->GetUnitType(This,pClsid,plID);
}
static inline HRESULT IOleUndoUnit_OnNextAdd(IOleUndoUnit* This) {
    return This->lpVtbl->OnNextAdd(This);
}
#endif
#endif

#endif


#endif  /* __IOleUndoUnit_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleParentUndoUnit interface
 */
#ifndef __IOleParentUndoUnit_INTERFACE_DEFINED__
#define __IOleParentUndoUnit_INTERFACE_DEFINED__

typedef IOleParentUndoUnit *LPOLEPARENTUNDOUNIT;

DEFINE_GUID(IID_IOleParentUndoUnit, 0xa1faf330, 0xef97, 0x11ce, 0x9b,0xc9, 0x00,0xaa,0x00,0x60,0x8e,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a1faf330-ef97-11ce-9bc9-00aa00608e01")
IOleParentUndoUnit : public IOleUndoUnit
{
    virtual HRESULT STDMETHODCALLTYPE Open(
        IOleParentUndoUnit *pPUU) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        IOleParentUndoUnit *pPUU,
        WINBOOL fCommit) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        IOleUndoUnit *pUU) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindUnit(
        IOleUndoUnit *pUU) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParentState(
        DWORD *pdwState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleParentUndoUnit, 0xa1faf330, 0xef97, 0x11ce, 0x9b,0xc9, 0x00,0xaa,0x00,0x60,0x8e,0x01)
#endif
#else
typedef struct IOleParentUndoUnitVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleParentUndoUnit *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleParentUndoUnit *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleParentUndoUnit *This);

    /*** IOleUndoUnit methods ***/
    HRESULT (STDMETHODCALLTYPE *Do)(
        IOleParentUndoUnit *This,
        IOleUndoManager *pUndoManager);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IOleParentUndoUnit *This,
        BSTR *pBstr);

    HRESULT (STDMETHODCALLTYPE *GetUnitType)(
        IOleParentUndoUnit *This,
        CLSID *pClsid,
        LONG *plID);

    HRESULT (STDMETHODCALLTYPE *OnNextAdd)(
        IOleParentUndoUnit *This);

    /*** IOleParentUndoUnit methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IOleParentUndoUnit *This,
        IOleParentUndoUnit *pPUU);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IOleParentUndoUnit *This,
        IOleParentUndoUnit *pPUU,
        WINBOOL fCommit);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IOleParentUndoUnit *This,
        IOleUndoUnit *pUU);

    HRESULT (STDMETHODCALLTYPE *FindUnit)(
        IOleParentUndoUnit *This,
        IOleUndoUnit *pUU);

    HRESULT (STDMETHODCALLTYPE *GetParentState)(
        IOleParentUndoUnit *This,
        DWORD *pdwState);

    END_INTERFACE
} IOleParentUndoUnitVtbl;

interface IOleParentUndoUnit {
    CONST_VTBL IOleParentUndoUnitVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleParentUndoUnit_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleParentUndoUnit_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleParentUndoUnit_Release(This) (This)->lpVtbl->Release(This)
/*** IOleUndoUnit methods ***/
#define IOleParentUndoUnit_Do(This,pUndoManager) (This)->lpVtbl->Do(This,pUndoManager)
#define IOleParentUndoUnit_GetDescription(This,pBstr) (This)->lpVtbl->GetDescription(This,pBstr)
#define IOleParentUndoUnit_GetUnitType(This,pClsid,plID) (This)->lpVtbl->GetUnitType(This,pClsid,plID)
#define IOleParentUndoUnit_OnNextAdd(This) (This)->lpVtbl->OnNextAdd(This)
/*** IOleParentUndoUnit methods ***/
#define IOleParentUndoUnit_Open(This,pPUU) (This)->lpVtbl->Open(This,pPUU)
#define IOleParentUndoUnit_Close(This,pPUU,fCommit) (This)->lpVtbl->Close(This,pPUU,fCommit)
#define IOleParentUndoUnit_Add(This,pUU) (This)->lpVtbl->Add(This,pUU)
#define IOleParentUndoUnit_FindUnit(This,pUU) (This)->lpVtbl->FindUnit(This,pUU)
#define IOleParentUndoUnit_GetParentState(This,pdwState) (This)->lpVtbl->GetParentState(This,pdwState)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleParentUndoUnit_QueryInterface(IOleParentUndoUnit* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleParentUndoUnit_AddRef(IOleParentUndoUnit* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleParentUndoUnit_Release(IOleParentUndoUnit* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleUndoUnit methods ***/
static inline HRESULT IOleParentUndoUnit_Do(IOleParentUndoUnit* This,IOleUndoManager *pUndoManager) {
    return This->lpVtbl->Do(This,pUndoManager);
}
static inline HRESULT IOleParentUndoUnit_GetDescription(IOleParentUndoUnit* This,BSTR *pBstr) {
    return This->lpVtbl->GetDescription(This,pBstr);
}
static inline HRESULT IOleParentUndoUnit_GetUnitType(IOleParentUndoUnit* This,CLSID *pClsid,LONG *plID) {
    return This->lpVtbl->GetUnitType(This,pClsid,plID);
}
static inline HRESULT IOleParentUndoUnit_OnNextAdd(IOleParentUndoUnit* This) {
    return This->lpVtbl->OnNextAdd(This);
}
/*** IOleParentUndoUnit methods ***/
static inline HRESULT IOleParentUndoUnit_Open(IOleParentUndoUnit* This,IOleParentUndoUnit *pPUU) {
    return This->lpVtbl->Open(This,pPUU);
}
static inline HRESULT IOleParentUndoUnit_Close(IOleParentUndoUnit* This,IOleParentUndoUnit *pPUU,WINBOOL fCommit) {
    return This->lpVtbl->Close(This,pPUU,fCommit);
}
static inline HRESULT IOleParentUndoUnit_Add(IOleParentUndoUnit* This,IOleUndoUnit *pUU) {
    return This->lpVtbl->Add(This,pUU);
}
static inline HRESULT IOleParentUndoUnit_FindUnit(IOleParentUndoUnit* This,IOleUndoUnit *pUU) {
    return This->lpVtbl->FindUnit(This,pUU);
}
static inline HRESULT IOleParentUndoUnit_GetParentState(IOleParentUndoUnit* This,DWORD *pdwState) {
    return This->lpVtbl->GetParentState(This,pdwState);
}
#endif
#endif

#endif


#endif  /* __IOleParentUndoUnit_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumOleUndoUnits interface
 */
#ifndef __IEnumOleUndoUnits_INTERFACE_DEFINED__
#define __IEnumOleUndoUnits_INTERFACE_DEFINED__

typedef IEnumOleUndoUnits *LPENUMOLEUNDOUNITS;

DEFINE_GUID(IID_IEnumOleUndoUnits, 0xb3e7c340, 0xef97, 0x11ce, 0x9b,0xc9, 0x00,0xaa,0x00,0x60,0x8e,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b3e7c340-ef97-11ce-9bc9-00aa00608e01")
IEnumOleUndoUnits : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG cElt,
        IOleUndoUnit **rgElt,
        ULONG *pcEltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG cElt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumOleUndoUnits **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumOleUndoUnits, 0xb3e7c340, 0xef97, 0x11ce, 0x9b,0xc9, 0x00,0xaa,0x00,0x60,0x8e,0x01)
#endif
#else
typedef struct IEnumOleUndoUnitsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumOleUndoUnits *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumOleUndoUnits *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumOleUndoUnits *This);

    /*** IEnumOleUndoUnits methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumOleUndoUnits *This,
        ULONG cElt,
        IOleUndoUnit **rgElt,
        ULONG *pcEltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumOleUndoUnits *This,
        ULONG cElt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumOleUndoUnits *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumOleUndoUnits *This,
        IEnumOleUndoUnits **ppEnum);

    END_INTERFACE
} IEnumOleUndoUnitsVtbl;

interface IEnumOleUndoUnits {
    CONST_VTBL IEnumOleUndoUnitsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumOleUndoUnits_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumOleUndoUnits_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumOleUndoUnits_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumOleUndoUnits methods ***/
#define IEnumOleUndoUnits_Next(This,cElt,rgElt,pcEltFetched) (This)->lpVtbl->Next(This,cElt,rgElt,pcEltFetched)
#define IEnumOleUndoUnits_Skip(This,cElt) (This)->lpVtbl->Skip(This,cElt)
#define IEnumOleUndoUnits_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumOleUndoUnits_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumOleUndoUnits_QueryInterface(IEnumOleUndoUnits* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumOleUndoUnits_AddRef(IEnumOleUndoUnits* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumOleUndoUnits_Release(IEnumOleUndoUnits* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumOleUndoUnits methods ***/
static inline HRESULT IEnumOleUndoUnits_Next(IEnumOleUndoUnits* This,ULONG cElt,IOleUndoUnit **rgElt,ULONG *pcEltFetched) {
    return This->lpVtbl->Next(This,cElt,rgElt,pcEltFetched);
}
static inline HRESULT IEnumOleUndoUnits_Skip(IEnumOleUndoUnits* This,ULONG cElt) {
    return This->lpVtbl->Skip(This,cElt);
}
static inline HRESULT IEnumOleUndoUnits_Reset(IEnumOleUndoUnits* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumOleUndoUnits_Clone(IEnumOleUndoUnits* This,IEnumOleUndoUnits **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumOleUndoUnits_RemoteNext_Proxy(
    IEnumOleUndoUnits* This,
    ULONG cElt,
    IOleUndoUnit **rgElt,
    ULONG *pcEltFetched);
void __RPC_STUB IEnumOleUndoUnits_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumOleUndoUnits_Next_Proxy(
    IEnumOleUndoUnits* This,
    ULONG cElt,
    IOleUndoUnit **rgElt,
    ULONG *pcEltFetched);
HRESULT __RPC_STUB IEnumOleUndoUnits_Next_Stub(
    IEnumOleUndoUnits* This,
    ULONG cElt,
    IOleUndoUnit **rgElt,
    ULONG *pcEltFetched);

#endif  /* __IEnumOleUndoUnits_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IOleUndoManager interface
 */
#ifndef __IOleUndoManager_INTERFACE_DEFINED__
#define __IOleUndoManager_INTERFACE_DEFINED__

#define SID_SOleUndoManager IID_IOleUndoManager

typedef IOleUndoManager *LPOLEUNDOMANAGER;

DEFINE_GUID(IID_IOleUndoManager, 0xd001f200, 0xef97, 0x11ce, 0x9b,0xc9, 0x00,0xaa,0x00,0x60,0x8e,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d001f200-ef97-11ce-9bc9-00aa00608e01")
IOleUndoManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Open(
        IOleParentUndoUnit *pPUU) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        IOleParentUndoUnit *pPUU,
        WINBOOL fCommit) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        IOleUndoUnit *pUU) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOpenParentState(
        DWORD *pdwState) = 0;

    virtual HRESULT STDMETHODCALLTYPE DiscardFrom(
        IOleUndoUnit *pUU) = 0;

    virtual HRESULT STDMETHODCALLTYPE UndoTo(
        IOleUndoUnit *pUU) = 0;

    virtual HRESULT STDMETHODCALLTYPE RedoTo(
        IOleUndoUnit *pUU) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumUndoable(
        IEnumOleUndoUnits **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumRedoable(
        IEnumOleUndoUnits **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLastUndoDescription(
        BSTR *pBstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLastRedoDescription(
        BSTR *pBstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE Enable(
        WINBOOL fEnable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOleUndoManager, 0xd001f200, 0xef97, 0x11ce, 0x9b,0xc9, 0x00,0xaa,0x00,0x60,0x8e,0x01)
#endif
#else
typedef struct IOleUndoManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOleUndoManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOleUndoManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOleUndoManager *This);

    /*** IOleUndoManager methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IOleUndoManager *This,
        IOleParentUndoUnit *pPUU);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IOleUndoManager *This,
        IOleParentUndoUnit *pPUU,
        WINBOOL fCommit);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IOleUndoManager *This,
        IOleUndoUnit *pUU);

    HRESULT (STDMETHODCALLTYPE *GetOpenParentState)(
        IOleUndoManager *This,
        DWORD *pdwState);

    HRESULT (STDMETHODCALLTYPE *DiscardFrom)(
        IOleUndoManager *This,
        IOleUndoUnit *pUU);

    HRESULT (STDMETHODCALLTYPE *UndoTo)(
        IOleUndoManager *This,
        IOleUndoUnit *pUU);

    HRESULT (STDMETHODCALLTYPE *RedoTo)(
        IOleUndoManager *This,
        IOleUndoUnit *pUU);

    HRESULT (STDMETHODCALLTYPE *EnumUndoable)(
        IOleUndoManager *This,
        IEnumOleUndoUnits **ppEnum);

    HRESULT (STDMETHODCALLTYPE *EnumRedoable)(
        IOleUndoManager *This,
        IEnumOleUndoUnits **ppEnum);

    HRESULT (STDMETHODCALLTYPE *GetLastUndoDescription)(
        IOleUndoManager *This,
        BSTR *pBstr);

    HRESULT (STDMETHODCALLTYPE *GetLastRedoDescription)(
        IOleUndoManager *This,
        BSTR *pBstr);

    HRESULT (STDMETHODCALLTYPE *Enable)(
        IOleUndoManager *This,
        WINBOOL fEnable);

    END_INTERFACE
} IOleUndoManagerVtbl;

interface IOleUndoManager {
    CONST_VTBL IOleUndoManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOleUndoManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOleUndoManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOleUndoManager_Release(This) (This)->lpVtbl->Release(This)
/*** IOleUndoManager methods ***/
#define IOleUndoManager_Open(This,pPUU) (This)->lpVtbl->Open(This,pPUU)
#define IOleUndoManager_Close(This,pPUU,fCommit) (This)->lpVtbl->Close(This,pPUU,fCommit)
#define IOleUndoManager_Add(This,pUU) (This)->lpVtbl->Add(This,pUU)
#define IOleUndoManager_GetOpenParentState(This,pdwState) (This)->lpVtbl->GetOpenParentState(This,pdwState)
#define IOleUndoManager_DiscardFrom(This,pUU) (This)->lpVtbl->DiscardFrom(This,pUU)
#define IOleUndoManager_UndoTo(This,pUU) (This)->lpVtbl->UndoTo(This,pUU)
#define IOleUndoManager_RedoTo(This,pUU) (This)->lpVtbl->RedoTo(This,pUU)
#define IOleUndoManager_EnumUndoable(This,ppEnum) (This)->lpVtbl->EnumUndoable(This,ppEnum)
#define IOleUndoManager_EnumRedoable(This,ppEnum) (This)->lpVtbl->EnumRedoable(This,ppEnum)
#define IOleUndoManager_GetLastUndoDescription(This,pBstr) (This)->lpVtbl->GetLastUndoDescription(This,pBstr)
#define IOleUndoManager_GetLastRedoDescription(This,pBstr) (This)->lpVtbl->GetLastRedoDescription(This,pBstr)
#define IOleUndoManager_Enable(This,fEnable) (This)->lpVtbl->Enable(This,fEnable)
#else
/*** IUnknown methods ***/
static inline HRESULT IOleUndoManager_QueryInterface(IOleUndoManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOleUndoManager_AddRef(IOleUndoManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOleUndoManager_Release(IOleUndoManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IOleUndoManager methods ***/
static inline HRESULT IOleUndoManager_Open(IOleUndoManager* This,IOleParentUndoUnit *pPUU) {
    return This->lpVtbl->Open(This,pPUU);
}
static inline HRESULT IOleUndoManager_Close(IOleUndoManager* This,IOleParentUndoUnit *pPUU,WINBOOL fCommit) {
    return This->lpVtbl->Close(This,pPUU,fCommit);
}
static inline HRESULT IOleUndoManager_Add(IOleUndoManager* This,IOleUndoUnit *pUU) {
    return This->lpVtbl->Add(This,pUU);
}
static inline HRESULT IOleUndoManager_GetOpenParentState(IOleUndoManager* This,DWORD *pdwState) {
    return This->lpVtbl->GetOpenParentState(This,pdwState);
}
static inline HRESULT IOleUndoManager_DiscardFrom(IOleUndoManager* This,IOleUndoUnit *pUU) {
    return This->lpVtbl->DiscardFrom(This,pUU);
}
static inline HRESULT IOleUndoManager_UndoTo(IOleUndoManager* This,IOleUndoUnit *pUU) {
    return This->lpVtbl->UndoTo(This,pUU);
}
static inline HRESULT IOleUndoManager_RedoTo(IOleUndoManager* This,IOleUndoUnit *pUU) {
    return This->lpVtbl->RedoTo(This,pUU);
}
static inline HRESULT IOleUndoManager_EnumUndoable(IOleUndoManager* This,IEnumOleUndoUnits **ppEnum) {
    return This->lpVtbl->EnumUndoable(This,ppEnum);
}
static inline HRESULT IOleUndoManager_EnumRedoable(IOleUndoManager* This,IEnumOleUndoUnits **ppEnum) {
    return This->lpVtbl->EnumRedoable(This,ppEnum);
}
static inline HRESULT IOleUndoManager_GetLastUndoDescription(IOleUndoManager* This,BSTR *pBstr) {
    return This->lpVtbl->GetLastUndoDescription(This,pBstr);
}
static inline HRESULT IOleUndoManager_GetLastRedoDescription(IOleUndoManager* This,BSTR *pBstr) {
    return This->lpVtbl->GetLastRedoDescription(This,pBstr);
}
static inline HRESULT IOleUndoManager_Enable(IOleUndoManager* This,WINBOOL fEnable) {
    return This->lpVtbl->Enable(This,fEnable);
}
#endif
#endif

#endif


#endif  /* __IOleUndoManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPointerInactive interface
 */
#ifndef __IPointerInactive_INTERFACE_DEFINED__
#define __IPointerInactive_INTERFACE_DEFINED__

typedef IPointerInactive *LPPOINTERINACTIVE;

typedef enum tagPOINTERINACTIVE {
    POINTERINACTIVE_ACTIVATEONENTRY = 1,
    POINTERINACTIVE_DEACTIVATEONLEAVE = 2,
    POINTERINACTIVE_ACTIVATEONDRAG = 4
} POINTERINACTIVE;

DEFINE_GUID(IID_IPointerInactive, 0x55980ba0, 0x35aa, 0x11cf, 0xb6,0x71, 0x00,0xaa,0x00,0x4c,0xd6,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("55980ba0-35aa-11cf-b671-00aa004cd6d8")
IPointerInactive : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetActivationPolicy(
        DWORD *pdwPolicy) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnInactiveMouseMove(
        LPCRECT pRectBounds,
        LONG x,
        LONG y,
        DWORD grfKeyState) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnInactiveSetCursor(
        LPCRECT pRectBounds,
        LONG x,
        LONG y,
        DWORD dwMouseMsg,
        WINBOOL fSetAlways) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPointerInactive, 0x55980ba0, 0x35aa, 0x11cf, 0xb6,0x71, 0x00,0xaa,0x00,0x4c,0xd6,0xd8)
#endif
#else
typedef struct IPointerInactiveVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPointerInactive *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPointerInactive *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPointerInactive *This);

    /*** IPointerInactive methods ***/
    HRESULT (STDMETHODCALLTYPE *GetActivationPolicy)(
        IPointerInactive *This,
        DWORD *pdwPolicy);

    HRESULT (STDMETHODCALLTYPE *OnInactiveMouseMove)(
        IPointerInactive *This,
        LPCRECT pRectBounds,
        LONG x,
        LONG y,
        DWORD grfKeyState);

    HRESULT (STDMETHODCALLTYPE *OnInactiveSetCursor)(
        IPointerInactive *This,
        LPCRECT pRectBounds,
        LONG x,
        LONG y,
        DWORD dwMouseMsg,
        WINBOOL fSetAlways);

    END_INTERFACE
} IPointerInactiveVtbl;

interface IPointerInactive {
    CONST_VTBL IPointerInactiveVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPointerInactive_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPointerInactive_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPointerInactive_Release(This) (This)->lpVtbl->Release(This)
/*** IPointerInactive methods ***/
#define IPointerInactive_GetActivationPolicy(This,pdwPolicy) (This)->lpVtbl->GetActivationPolicy(This,pdwPolicy)
#define IPointerInactive_OnInactiveMouseMove(This,pRectBounds,x,y,grfKeyState) (This)->lpVtbl->OnInactiveMouseMove(This,pRectBounds,x,y,grfKeyState)
#define IPointerInactive_OnInactiveSetCursor(This,pRectBounds,x,y,dwMouseMsg,fSetAlways) (This)->lpVtbl->OnInactiveSetCursor(This,pRectBounds,x,y,dwMouseMsg,fSetAlways)
#else
/*** IUnknown methods ***/
static inline HRESULT IPointerInactive_QueryInterface(IPointerInactive* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPointerInactive_AddRef(IPointerInactive* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPointerInactive_Release(IPointerInactive* This) {
    return This->lpVtbl->Release(This);
}
/*** IPointerInactive methods ***/
static inline HRESULT IPointerInactive_GetActivationPolicy(IPointerInactive* This,DWORD *pdwPolicy) {
    return This->lpVtbl->GetActivationPolicy(This,pdwPolicy);
}
static inline HRESULT IPointerInactive_OnInactiveMouseMove(IPointerInactive* This,LPCRECT pRectBounds,LONG x,LONG y,DWORD grfKeyState) {
    return This->lpVtbl->OnInactiveMouseMove(This,pRectBounds,x,y,grfKeyState);
}
static inline HRESULT IPointerInactive_OnInactiveSetCursor(IPointerInactive* This,LPCRECT pRectBounds,LONG x,LONG y,DWORD dwMouseMsg,WINBOOL fSetAlways) {
    return This->lpVtbl->OnInactiveSetCursor(This,pRectBounds,x,y,dwMouseMsg,fSetAlways);
}
#endif
#endif

#endif


#endif  /* __IPointerInactive_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IObjectWithSite interface
 */
#ifndef __IObjectWithSite_INTERFACE_DEFINED__
#define __IObjectWithSite_INTERFACE_DEFINED__

typedef IObjectWithSite *LPOBJECTWITHSITE;

DEFINE_GUID(IID_IObjectWithSite, 0xfc4801a3, 0x2ba9, 0x11cf, 0xa2,0x29, 0x00,0xaa,0x00,0x3d,0x73,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fc4801a3-2ba9-11cf-a229-00aa003d7352")
IObjectWithSite : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetSite(
        IUnknown *pUnkSite) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSite(
        REFIID riid,
        void **ppvSite) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IObjectWithSite, 0xfc4801a3, 0x2ba9, 0x11cf, 0xa2,0x29, 0x00,0xaa,0x00,0x3d,0x73,0x52)
#endif
#else
typedef struct IObjectWithSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IObjectWithSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IObjectWithSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IObjectWithSite *This);

    /*** IObjectWithSite methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSite)(
        IObjectWithSite *This,
        IUnknown *pUnkSite);

    HRESULT (STDMETHODCALLTYPE *GetSite)(
        IObjectWithSite *This,
        REFIID riid,
        void **ppvSite);

    END_INTERFACE
} IObjectWithSiteVtbl;

interface IObjectWithSite {
    CONST_VTBL IObjectWithSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IObjectWithSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IObjectWithSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IObjectWithSite_Release(This) (This)->lpVtbl->Release(This)
/*** IObjectWithSite methods ***/
#define IObjectWithSite_SetSite(This,pUnkSite) (This)->lpVtbl->SetSite(This,pUnkSite)
#define IObjectWithSite_GetSite(This,riid,ppvSite) (This)->lpVtbl->GetSite(This,riid,ppvSite)
#else
/*** IUnknown methods ***/
static inline HRESULT IObjectWithSite_QueryInterface(IObjectWithSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IObjectWithSite_AddRef(IObjectWithSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IObjectWithSite_Release(IObjectWithSite* This) {
    return This->lpVtbl->Release(This);
}
/*** IObjectWithSite methods ***/
static inline HRESULT IObjectWithSite_SetSite(IObjectWithSite* This,IUnknown *pUnkSite) {
    return This->lpVtbl->SetSite(This,pUnkSite);
}
static inline HRESULT IObjectWithSite_GetSite(IObjectWithSite* This,REFIID riid,void **ppvSite) {
    return This->lpVtbl->GetSite(This,riid,ppvSite);
}
#endif
#endif

#endif


#endif  /* __IObjectWithSite_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IPerPropertyBrowsing interface
 */
#ifndef __IPerPropertyBrowsing_INTERFACE_DEFINED__
#define __IPerPropertyBrowsing_INTERFACE_DEFINED__

typedef IPerPropertyBrowsing *LPPERPROPERTYBROWSING;

typedef struct tagCALPOLESTR {
    ULONG cElems;
    LPOLESTR *pElems;
} CALPOLESTR;

typedef struct tagCALPOLESTR *LPCALPOLESTR;

typedef struct tagCADWORD {
    ULONG cElems;
    DWORD *pElems;
} CADWORD;

typedef struct tagCADWORD *LPCADWORD;

DEFINE_GUID(IID_IPerPropertyBrowsing, 0x376bd3aa, 0x3845, 0x101b, 0x84,0xed, 0x08,0x00,0x2b,0x2e,0xc7,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("376bd3aa-3845-101b-84ed-08002b2ec713")
IPerPropertyBrowsing : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDisplayString(
        DISPID dispID,
        BSTR *pBstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE MapPropertyToPage(
        DISPID dispID,
        CLSID *pClsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPredefinedStrings(
        DISPID dispID,
        CALPOLESTR *pCaStringsOut,
        CADWORD *pCaCookiesOut) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPredefinedValue(
        DISPID dispID,
        DWORD dwCookie,
        VARIANT *pVarOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPerPropertyBrowsing, 0x376bd3aa, 0x3845, 0x101b, 0x84,0xed, 0x08,0x00,0x2b,0x2e,0xc7,0x13)
#endif
#else
typedef struct IPerPropertyBrowsingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPerPropertyBrowsing *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPerPropertyBrowsing *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPerPropertyBrowsing *This);

    /*** IPerPropertyBrowsing methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayString)(
        IPerPropertyBrowsing *This,
        DISPID dispID,
        BSTR *pBstr);

    HRESULT (STDMETHODCALLTYPE *MapPropertyToPage)(
        IPerPropertyBrowsing *This,
        DISPID dispID,
        CLSID *pClsid);

    HRESULT (STDMETHODCALLTYPE *GetPredefinedStrings)(
        IPerPropertyBrowsing *This,
        DISPID dispID,
        CALPOLESTR *pCaStringsOut,
        CADWORD *pCaCookiesOut);

    HRESULT (STDMETHODCALLTYPE *GetPredefinedValue)(
        IPerPropertyBrowsing *This,
        DISPID dispID,
        DWORD dwCookie,
        VARIANT *pVarOut);

    END_INTERFACE
} IPerPropertyBrowsingVtbl;

interface IPerPropertyBrowsing {
    CONST_VTBL IPerPropertyBrowsingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPerPropertyBrowsing_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPerPropertyBrowsing_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPerPropertyBrowsing_Release(This) (This)->lpVtbl->Release(This)
/*** IPerPropertyBrowsing methods ***/
#define IPerPropertyBrowsing_GetDisplayString(This,dispID,pBstr) (This)->lpVtbl->GetDisplayString(This,dispID,pBstr)
#define IPerPropertyBrowsing_MapPropertyToPage(This,dispID,pClsid) (This)->lpVtbl->MapPropertyToPage(This,dispID,pClsid)
#define IPerPropertyBrowsing_GetPredefinedStrings(This,dispID,pCaStringsOut,pCaCookiesOut) (This)->lpVtbl->GetPredefinedStrings(This,dispID,pCaStringsOut,pCaCookiesOut)
#define IPerPropertyBrowsing_GetPredefinedValue(This,dispID,dwCookie,pVarOut) (This)->lpVtbl->GetPredefinedValue(This,dispID,dwCookie,pVarOut)
#else
/*** IUnknown methods ***/
static inline HRESULT IPerPropertyBrowsing_QueryInterface(IPerPropertyBrowsing* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPerPropertyBrowsing_AddRef(IPerPropertyBrowsing* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPerPropertyBrowsing_Release(IPerPropertyBrowsing* This) {
    return This->lpVtbl->Release(This);
}
/*** IPerPropertyBrowsing methods ***/
static inline HRESULT IPerPropertyBrowsing_GetDisplayString(IPerPropertyBrowsing* This,DISPID dispID,BSTR *pBstr) {
    return This->lpVtbl->GetDisplayString(This,dispID,pBstr);
}
static inline HRESULT IPerPropertyBrowsing_MapPropertyToPage(IPerPropertyBrowsing* This,DISPID dispID,CLSID *pClsid) {
    return This->lpVtbl->MapPropertyToPage(This,dispID,pClsid);
}
static inline HRESULT IPerPropertyBrowsing_GetPredefinedStrings(IPerPropertyBrowsing* This,DISPID dispID,CALPOLESTR *pCaStringsOut,CADWORD *pCaCookiesOut) {
    return This->lpVtbl->GetPredefinedStrings(This,dispID,pCaStringsOut,pCaCookiesOut);
}
static inline HRESULT IPerPropertyBrowsing_GetPredefinedValue(IPerPropertyBrowsing* This,DISPID dispID,DWORD dwCookie,VARIANT *pVarOut) {
    return This->lpVtbl->GetPredefinedValue(This,dispID,dwCookie,pVarOut);
}
#endif
#endif

#endif


#endif  /* __IPerPropertyBrowsing_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IPropertyBag2 interface
 */
#ifndef __IPropertyBag2_INTERFACE_DEFINED__
#define __IPropertyBag2_INTERFACE_DEFINED__

typedef IPropertyBag2 *LPPROPERTYBAG2;

typedef enum tagPROPBAG2_TYPE {
    PROPBAG2_TYPE_UNDEFINED = 0,
    PROPBAG2_TYPE_DATA = 1,
    PROPBAG2_TYPE_URL = 2,
    PROPBAG2_TYPE_OBJECT = 3,
    PROPBAG2_TYPE_STREAM = 4,
    PROPBAG2_TYPE_STORAGE = 5,
    PROPBAG2_TYPE_MONIKER = 6
} PROPBAG2_TYPE;

typedef struct tagPROPBAG2 {
    DWORD dwType;
    VARTYPE vt;
    CLIPFORMAT cfType;
    DWORD dwHint;
    LPOLESTR pstrName;
    CLSID clsid;
} PROPBAG2;

DEFINE_GUID(IID_IPropertyBag2, 0x22f55882, 0x280b, 0x11d0, 0xa8,0xa9, 0x00,0xa0,0xc9,0x0c,0x20,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("22f55882-280b-11d0-a8a9-00a0c90c2004")
IPropertyBag2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Read(
        ULONG cProperties,
        PROPBAG2 *pPropBag,
        IErrorLog *pErrLog,
        VARIANT *pvarValue,
        HRESULT *phrError) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write(
        ULONG cProperties,
        PROPBAG2 *pPropBag,
        VARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE CountProperties(
        ULONG *pcProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyInfo(
        ULONG iProperty,
        ULONG cProperties,
        PROPBAG2 *pPropBag,
        ULONG *pcProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE LoadObject(
        LPCOLESTR pstrName,
        DWORD dwHint,
        IUnknown *pUnkObject,
        IErrorLog *pErrLog) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertyBag2, 0x22f55882, 0x280b, 0x11d0, 0xa8,0xa9, 0x00,0xa0,0xc9,0x0c,0x20,0x04)
#endif
#else
typedef struct IPropertyBag2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertyBag2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertyBag2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertyBag2 *This);

    /*** IPropertyBag2 methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IPropertyBag2 *This,
        ULONG cProperties,
        PROPBAG2 *pPropBag,
        IErrorLog *pErrLog,
        VARIANT *pvarValue,
        HRESULT *phrError);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IPropertyBag2 *This,
        ULONG cProperties,
        PROPBAG2 *pPropBag,
        VARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *CountProperties)(
        IPropertyBag2 *This,
        ULONG *pcProperties);

    HRESULT (STDMETHODCALLTYPE *GetPropertyInfo)(
        IPropertyBag2 *This,
        ULONG iProperty,
        ULONG cProperties,
        PROPBAG2 *pPropBag,
        ULONG *pcProperties);

    HRESULT (STDMETHODCALLTYPE *LoadObject)(
        IPropertyBag2 *This,
        LPCOLESTR pstrName,
        DWORD dwHint,
        IUnknown *pUnkObject,
        IErrorLog *pErrLog);

    END_INTERFACE
} IPropertyBag2Vtbl;

interface IPropertyBag2 {
    CONST_VTBL IPropertyBag2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertyBag2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertyBag2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertyBag2_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertyBag2 methods ***/
#define IPropertyBag2_Read(This,cProperties,pPropBag,pErrLog,pvarValue,phrError) (This)->lpVtbl->Read(This,cProperties,pPropBag,pErrLog,pvarValue,phrError)
#define IPropertyBag2_Write(This,cProperties,pPropBag,pvarValue) (This)->lpVtbl->Write(This,cProperties,pPropBag,pvarValue)
#define IPropertyBag2_CountProperties(This,pcProperties) (This)->lpVtbl->CountProperties(This,pcProperties)
#define IPropertyBag2_GetPropertyInfo(This,iProperty,cProperties,pPropBag,pcProperties) (This)->lpVtbl->GetPropertyInfo(This,iProperty,cProperties,pPropBag,pcProperties)
#define IPropertyBag2_LoadObject(This,pstrName,dwHint,pUnkObject,pErrLog) (This)->lpVtbl->LoadObject(This,pstrName,dwHint,pUnkObject,pErrLog)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertyBag2_QueryInterface(IPropertyBag2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertyBag2_AddRef(IPropertyBag2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertyBag2_Release(IPropertyBag2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertyBag2 methods ***/
static inline HRESULT IPropertyBag2_Read(IPropertyBag2* This,ULONG cProperties,PROPBAG2 *pPropBag,IErrorLog *pErrLog,VARIANT *pvarValue,HRESULT *phrError) {
    return This->lpVtbl->Read(This,cProperties,pPropBag,pErrLog,pvarValue,phrError);
}
static inline HRESULT IPropertyBag2_Write(IPropertyBag2* This,ULONG cProperties,PROPBAG2 *pPropBag,VARIANT *pvarValue) {
    return This->lpVtbl->Write(This,cProperties,pPropBag,pvarValue);
}
static inline HRESULT IPropertyBag2_CountProperties(IPropertyBag2* This,ULONG *pcProperties) {
    return This->lpVtbl->CountProperties(This,pcProperties);
}
static inline HRESULT IPropertyBag2_GetPropertyInfo(IPropertyBag2* This,ULONG iProperty,ULONG cProperties,PROPBAG2 *pPropBag,ULONG *pcProperties) {
    return This->lpVtbl->GetPropertyInfo(This,iProperty,cProperties,pPropBag,pcProperties);
}
static inline HRESULT IPropertyBag2_LoadObject(IPropertyBag2* This,LPCOLESTR pstrName,DWORD dwHint,IUnknown *pUnkObject,IErrorLog *pErrLog) {
    return This->lpVtbl->LoadObject(This,pstrName,dwHint,pUnkObject,pErrLog);
}
#endif
#endif

#endif


#endif  /* __IPropertyBag2_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IPersistPropertyBag2 interface
 */
#ifndef __IPersistPropertyBag2_INTERFACE_DEFINED__
#define __IPersistPropertyBag2_INTERFACE_DEFINED__

typedef IPersistPropertyBag2 *LPPERSISTPROPERTYBAG2;

DEFINE_GUID(IID_IPersistPropertyBag2, 0x22f55881, 0x280b, 0x11d0, 0xa8,0xa9, 0x00,0xa0,0xc9,0x0c,0x20,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("22f55881-280b-11d0-a8a9-00a0c90c2004")
IPersistPropertyBag2 : public IPersist
{
    virtual HRESULT STDMETHODCALLTYPE InitNew(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        IPropertyBag2 *pPropBag,
        IErrorLog *pErrLog) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        IPropertyBag2 *pPropBag,
        WINBOOL fClearDirty,
        WINBOOL fSaveAllProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsDirty(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistPropertyBag2, 0x22f55881, 0x280b, 0x11d0, 0xa8,0xa9, 0x00,0xa0,0xc9,0x0c,0x20,0x04)
#endif
#else
typedef struct IPersistPropertyBag2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistPropertyBag2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistPropertyBag2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistPropertyBag2 *This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IPersistPropertyBag2 *This,
        CLSID *pClassID);

    /*** IPersistPropertyBag2 methods ***/
    HRESULT (STDMETHODCALLTYPE *InitNew)(
        IPersistPropertyBag2 *This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IPersistPropertyBag2 *This,
        IPropertyBag2 *pPropBag,
        IErrorLog *pErrLog);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IPersistPropertyBag2 *This,
        IPropertyBag2 *pPropBag,
        WINBOOL fClearDirty,
        WINBOOL fSaveAllProperties);

    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IPersistPropertyBag2 *This);

    END_INTERFACE
} IPersistPropertyBag2Vtbl;

interface IPersistPropertyBag2 {
    CONST_VTBL IPersistPropertyBag2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistPropertyBag2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistPropertyBag2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistPropertyBag2_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IPersistPropertyBag2_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistPropertyBag2 methods ***/
#define IPersistPropertyBag2_InitNew(This) (This)->lpVtbl->InitNew(This)
#define IPersistPropertyBag2_Load(This,pPropBag,pErrLog) (This)->lpVtbl->Load(This,pPropBag,pErrLog)
#define IPersistPropertyBag2_Save(This,pPropBag,fClearDirty,fSaveAllProperties) (This)->lpVtbl->Save(This,pPropBag,fClearDirty,fSaveAllProperties)
#define IPersistPropertyBag2_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPersistPropertyBag2_QueryInterface(IPersistPropertyBag2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPersistPropertyBag2_AddRef(IPersistPropertyBag2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPersistPropertyBag2_Release(IPersistPropertyBag2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static inline HRESULT IPersistPropertyBag2_GetClassID(IPersistPropertyBag2* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistPropertyBag2 methods ***/
static inline HRESULT IPersistPropertyBag2_InitNew(IPersistPropertyBag2* This) {
    return This->lpVtbl->InitNew(This);
}
static inline HRESULT IPersistPropertyBag2_Load(IPersistPropertyBag2* This,IPropertyBag2 *pPropBag,IErrorLog *pErrLog) {
    return This->lpVtbl->Load(This,pPropBag,pErrLog);
}
static inline HRESULT IPersistPropertyBag2_Save(IPersistPropertyBag2* This,IPropertyBag2 *pPropBag,WINBOOL fClearDirty,WINBOOL fSaveAllProperties) {
    return This->lpVtbl->Save(This,pPropBag,fClearDirty,fSaveAllProperties);
}
static inline HRESULT IPersistPropertyBag2_IsDirty(IPersistPropertyBag2* This) {
    return This->lpVtbl->IsDirty(This);
}
#endif
#endif

#endif


#endif  /* __IPersistPropertyBag2_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IAdviseSinkEx interface
 */
#ifndef __IAdviseSinkEx_INTERFACE_DEFINED__
#define __IAdviseSinkEx_INTERFACE_DEFINED__

typedef IAdviseSinkEx *LPADVISESINKEX;

DEFINE_GUID(IID_IAdviseSinkEx, 0x3af24290, 0x0c96, 0x11ce, 0xa0,0xcf, 0x00,0xaa,0x00,0x60,0x0a,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3af24290-0c96-11ce-a0cf-00aa00600ab8")
IAdviseSinkEx : public IAdviseSink
{
    virtual void STDMETHODCALLTYPE OnViewStatusChange(
        DWORD dwViewStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAdviseSinkEx, 0x3af24290, 0x0c96, 0x11ce, 0xa0,0xcf, 0x00,0xaa,0x00,0x60,0x0a,0xb8)
#endif
#else
typedef struct IAdviseSinkExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAdviseSinkEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAdviseSinkEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAdviseSinkEx *This);

    /*** IAdviseSink methods ***/
    void (STDMETHODCALLTYPE *OnDataChange)(
        IAdviseSinkEx *This,
        FORMATETC *pFormatetc,
        STGMEDIUM *pStgmed);

    void (STDMETHODCALLTYPE *OnViewChange)(
        IAdviseSinkEx *This,
        DWORD dwAspect,
        LONG lindex);

    void (STDMETHODCALLTYPE *OnRename)(
        IAdviseSinkEx *This,
        IMoniker *pmk);

    void (STDMETHODCALLTYPE *OnSave)(
        IAdviseSinkEx *This);

    void (STDMETHODCALLTYPE *OnClose)(
        IAdviseSinkEx *This);

    /*** IAdviseSinkEx methods ***/
    void (STDMETHODCALLTYPE *OnViewStatusChange)(
        IAdviseSinkEx *This,
        DWORD dwViewStatus);

    END_INTERFACE
} IAdviseSinkExVtbl;

interface IAdviseSinkEx {
    CONST_VTBL IAdviseSinkExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAdviseSinkEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAdviseSinkEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAdviseSinkEx_Release(This) (This)->lpVtbl->Release(This)
/*** IAdviseSink methods ***/
#define IAdviseSinkEx_OnDataChange(This,pFormatetc,pStgmed) (This)->lpVtbl->OnDataChange(This,pFormatetc,pStgmed)
#define IAdviseSinkEx_OnViewChange(This,dwAspect,lindex) (This)->lpVtbl->OnViewChange(This,dwAspect,lindex)
#define IAdviseSinkEx_OnRename(This,pmk) (This)->lpVtbl->OnRename(This,pmk)
#define IAdviseSinkEx_OnSave(This) (This)->lpVtbl->OnSave(This)
#define IAdviseSinkEx_OnClose(This) (This)->lpVtbl->OnClose(This)
/*** IAdviseSinkEx methods ***/
#define IAdviseSinkEx_OnViewStatusChange(This,dwViewStatus) (This)->lpVtbl->OnViewStatusChange(This,dwViewStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IAdviseSinkEx_QueryInterface(IAdviseSinkEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAdviseSinkEx_AddRef(IAdviseSinkEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAdviseSinkEx_Release(IAdviseSinkEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IAdviseSink methods ***/
static inline void IAdviseSinkEx_OnDataChange(IAdviseSinkEx* This,FORMATETC *pFormatetc,STGMEDIUM *pStgmed) {
    This->lpVtbl->OnDataChange(This,pFormatetc,pStgmed);
}
static inline void IAdviseSinkEx_OnViewChange(IAdviseSinkEx* This,DWORD dwAspect,LONG lindex) {
    This->lpVtbl->OnViewChange(This,dwAspect,lindex);
}
static inline void IAdviseSinkEx_OnRename(IAdviseSinkEx* This,IMoniker *pmk) {
    This->lpVtbl->OnRename(This,pmk);
}
static inline void IAdviseSinkEx_OnSave(IAdviseSinkEx* This) {
    This->lpVtbl->OnSave(This);
}
static inline void IAdviseSinkEx_OnClose(IAdviseSinkEx* This) {
    This->lpVtbl->OnClose(This);
}
/*** IAdviseSinkEx methods ***/
static inline void IAdviseSinkEx_OnViewStatusChange(IAdviseSinkEx* This,DWORD dwViewStatus) {
    This->lpVtbl->OnViewStatusChange(This,dwViewStatus);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IAdviseSinkEx_RemoteOnViewStatusChange_Proxy(
    IAdviseSinkEx* This,
    DWORD dwViewStatus);
void __RPC_STUB IAdviseSinkEx_RemoteOnViewStatusChange_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
void CALLBACK IAdviseSinkEx_OnViewStatusChange_Proxy(
    IAdviseSinkEx* This,
    DWORD dwViewStatus);
HRESULT __RPC_STUB IAdviseSinkEx_OnViewStatusChange_Stub(
    IAdviseSinkEx* This,
    DWORD dwViewStatus);

#endif  /* __IAdviseSinkEx_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IQuickActivate interface
 */
#ifndef __IQuickActivate_INTERFACE_DEFINED__
#define __IQuickActivate_INTERFACE_DEFINED__

typedef IQuickActivate *LPQUICKACTIVATE;

typedef enum tagQACONTAINERFLAGS {
    QACONTAINER_SHOWHATCHING = 0x1,
    QACONTAINER_SHOWGRABHANDLES = 0x2,
    QACONTAINER_USERMODE = 0x4,
    QACONTAINER_DISPLAYASDEFAULT = 0x8,
    QACONTAINER_UIDEAD = 0x10,
    QACONTAINER_AUTOCLIP = 0x20,
    QACONTAINER_MESSAGEREFLECT = 0x40,
    QACONTAINER_SUPPORTSMNEMONICS = 0x80
} QACONTAINERFLAGS;

typedef DWORD OLE_COLOR;

typedef struct tagQACONTAINER {
    ULONG cbSize;
    IOleClientSite *pClientSite;
    IAdviseSinkEx *pAdviseSink;
    IPropertyNotifySink *pPropertyNotifySink;
    IUnknown *pUnkEventSink;
    DWORD dwAmbientFlags;
    OLE_COLOR colorFore;
    OLE_COLOR colorBack;
    IFont *pFont;
    IOleUndoManager *pUndoMgr;
    DWORD dwAppearance;
    LONG lcid;
    HPALETTE hpal;
    IBindHost *pBindHost;
    IOleControlSite *pOleControlSite;
    IServiceProvider *pServiceProvider;
} QACONTAINER;

typedef struct tagQACONTROL {
    ULONG cbSize;
    DWORD dwMiscStatus;
    DWORD dwViewStatus;
    DWORD dwEventCookie;
    DWORD dwPropNotifyCookie;
    DWORD dwPointerActivationPolicy;
} QACONTROL;

DEFINE_GUID(IID_IQuickActivate, 0xcf51ed10, 0x62fe, 0x11cf, 0xbf,0x86, 0x00,0xa0,0xc9,0x03,0x48,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cf51ed10-62fe-11cf-bf86-00a0c9034836")
IQuickActivate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QuickActivate(
        QACONTAINER *pQaContainer,
        QACONTROL *pQaControl) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContentExtent(
        LPSIZEL pSizel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentExtent(
        LPSIZEL pSizel) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IQuickActivate, 0xcf51ed10, 0x62fe, 0x11cf, 0xbf,0x86, 0x00,0xa0,0xc9,0x03,0x48,0x36)
#endif
#else
typedef struct IQuickActivateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IQuickActivate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IQuickActivate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IQuickActivate *This);

    /*** IQuickActivate methods ***/
    HRESULT (STDMETHODCALLTYPE *QuickActivate)(
        IQuickActivate *This,
        QACONTAINER *pQaContainer,
        QACONTROL *pQaControl);

    HRESULT (STDMETHODCALLTYPE *SetContentExtent)(
        IQuickActivate *This,
        LPSIZEL pSizel);

    HRESULT (STDMETHODCALLTYPE *GetContentExtent)(
        IQuickActivate *This,
        LPSIZEL pSizel);

    END_INTERFACE
} IQuickActivateVtbl;

interface IQuickActivate {
    CONST_VTBL IQuickActivateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IQuickActivate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IQuickActivate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IQuickActivate_Release(This) (This)->lpVtbl->Release(This)
/*** IQuickActivate methods ***/
#define IQuickActivate_QuickActivate(This,pQaContainer,pQaControl) (This)->lpVtbl->QuickActivate(This,pQaContainer,pQaControl)
#define IQuickActivate_SetContentExtent(This,pSizel) (This)->lpVtbl->SetContentExtent(This,pSizel)
#define IQuickActivate_GetContentExtent(This,pSizel) (This)->lpVtbl->GetContentExtent(This,pSizel)
#else
/*** IUnknown methods ***/
static inline HRESULT IQuickActivate_QueryInterface(IQuickActivate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IQuickActivate_AddRef(IQuickActivate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IQuickActivate_Release(IQuickActivate* This) {
    return This->lpVtbl->Release(This);
}
/*** IQuickActivate methods ***/
static inline HRESULT IQuickActivate_QuickActivate(IQuickActivate* This,QACONTAINER *pQaContainer,QACONTROL *pQaControl) {
    return This->lpVtbl->QuickActivate(This,pQaContainer,pQaControl);
}
static inline HRESULT IQuickActivate_SetContentExtent(IQuickActivate* This,LPSIZEL pSizel) {
    return This->lpVtbl->SetContentExtent(This,pSizel);
}
static inline HRESULT IQuickActivate_GetContentExtent(IQuickActivate* This,LPSIZEL pSizel) {
    return This->lpVtbl->GetContentExtent(This,pSizel);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IQuickActivate_RemoteQuickActivate_Proxy(
    IQuickActivate* This,
    QACONTAINER *pQaContainer,
    QACONTROL *pQaControl);
void __RPC_STUB IQuickActivate_RemoteQuickActivate_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IQuickActivate_QuickActivate_Proxy(
    IQuickActivate* This,
    QACONTAINER *pQaContainer,
    QACONTROL *pQaControl);
HRESULT __RPC_STUB IQuickActivate_QuickActivate_Stub(
    IQuickActivate* This,
    QACONTAINER *pQaContainer,
    QACONTROL *pQaControl);

#endif  /* __IQuickActivate_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER HACCEL_UserSize     (ULONG *, ULONG, HACCEL *);
unsigned char * __RPC_USER HACCEL_UserMarshal  (ULONG *, unsigned char *, HACCEL *);
unsigned char * __RPC_USER HACCEL_UserUnmarshal(ULONG *, unsigned char *, HACCEL *);
void            __RPC_USER HACCEL_UserFree     (ULONG *, HACCEL *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);
ULONG           __RPC_USER HFONT_UserSize     (ULONG *, ULONG, HFONT *);
unsigned char * __RPC_USER HFONT_UserMarshal  (ULONG *, unsigned char *, HFONT *);
unsigned char * __RPC_USER HFONT_UserUnmarshal(ULONG *, unsigned char *, HFONT *);
void            __RPC_USER HFONT_UserFree     (ULONG *, HFONT *);
ULONG           __RPC_USER HDC_UserSize     (ULONG *, ULONG, HDC *);
unsigned char * __RPC_USER HDC_UserMarshal  (ULONG *, unsigned char *, HDC *);
unsigned char * __RPC_USER HDC_UserUnmarshal(ULONG *, unsigned char *, HDC *);
void            __RPC_USER HDC_UserFree     (ULONG *, HDC *);
ULONG           __RPC_USER HRGN_UserSize     (ULONG *, ULONG, HRGN *);
unsigned char * __RPC_USER HRGN_UserMarshal  (ULONG *, unsigned char *, HRGN *);
unsigned char * __RPC_USER HRGN_UserUnmarshal(ULONG *, unsigned char *, HRGN *);
void            __RPC_USER HRGN_UserFree     (ULONG *, HRGN *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);
ULONG           __RPC_USER CLIPFORMAT_UserSize     (ULONG *, ULONG, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserMarshal  (ULONG *, unsigned char *, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserUnmarshal(ULONG *, unsigned char *, CLIPFORMAT *);
void            __RPC_USER CLIPFORMAT_UserFree     (ULONG *, CLIPFORMAT *);
ULONG           __RPC_USER HPALETTE_UserSize     (ULONG *, ULONG, HPALETTE *);
unsigned char * __RPC_USER HPALETTE_UserMarshal  (ULONG *, unsigned char *, HPALETTE *);
unsigned char * __RPC_USER HPALETTE_UserUnmarshal(ULONG *, unsigned char *, HPALETTE *);
void            __RPC_USER HPALETTE_UserFree     (ULONG *, HPALETTE *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __ocidl_h__ */
