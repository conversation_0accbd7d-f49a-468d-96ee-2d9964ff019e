// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: xla/service/cpu/executable.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fexecutable_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fexecutable_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "xla/backends/cpu/runtime/thunk.pb.h"
#include "xla/service/hlo.pb.h"
#include "xla/xla.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_xla_2fservice_2fcpu_2fexecutable_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_xla_2fservice_2fcpu_2fexecutable_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_xla_2fservice_2fcpu_2fexecutable_2eproto;
namespace xla {
namespace cpu {
class CompilationResultProto;
struct CompilationResultProtoDefaultTypeInternal;
extern CompilationResultProtoDefaultTypeInternal _CompilationResultProto_default_instance_;
class SymbolProto;
struct SymbolProtoDefaultTypeInternal;
extern SymbolProtoDefaultTypeInternal _SymbolProto_default_instance_;
}  // namespace cpu
}  // namespace xla
PROTOBUF_NAMESPACE_OPEN
template<> ::xla::cpu::CompilationResultProto* Arena::CreateMaybeMessage<::xla::cpu::CompilationResultProto>(Arena*);
template<> ::xla::cpu::SymbolProto* Arena::CreateMaybeMessage<::xla::cpu::SymbolProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace xla {
namespace cpu {

enum SymbolProto_FunctionTypeId : int {
  SymbolProto_FunctionTypeId_UNKNOWN = 0,
  SymbolProto_FunctionTypeId_KERNEL = 1,
  SymbolProto_FunctionTypeId_COMPARATOR = 2,
  SymbolProto_FunctionTypeId_SymbolProto_FunctionTypeId_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SymbolProto_FunctionTypeId_SymbolProto_FunctionTypeId_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SymbolProto_FunctionTypeId_IsValid(int value);
constexpr SymbolProto_FunctionTypeId SymbolProto_FunctionTypeId_FunctionTypeId_MIN = SymbolProto_FunctionTypeId_UNKNOWN;
constexpr SymbolProto_FunctionTypeId SymbolProto_FunctionTypeId_FunctionTypeId_MAX = SymbolProto_FunctionTypeId_COMPARATOR;
constexpr int SymbolProto_FunctionTypeId_FunctionTypeId_ARRAYSIZE = SymbolProto_FunctionTypeId_FunctionTypeId_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SymbolProto_FunctionTypeId_descriptor();
template<typename T>
inline const std::string& SymbolProto_FunctionTypeId_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SymbolProto_FunctionTypeId>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SymbolProto_FunctionTypeId_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SymbolProto_FunctionTypeId_descriptor(), enum_t_value);
}
inline bool SymbolProto_FunctionTypeId_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SymbolProto_FunctionTypeId* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SymbolProto_FunctionTypeId>(
    SymbolProto_FunctionTypeId_descriptor(), name, value);
}
enum CompilationResultProto_ObjFileKind : int {
  CompilationResultProto_ObjFileKind_UNKNOWN = 0,
  CompilationResultProto_ObjFileKind_CLASSIC = 1,
  CompilationResultProto_ObjFileKind_KERNELS = 2,
  CompilationResultProto_ObjFileKind_CompilationResultProto_ObjFileKind_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CompilationResultProto_ObjFileKind_CompilationResultProto_ObjFileKind_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CompilationResultProto_ObjFileKind_IsValid(int value);
constexpr CompilationResultProto_ObjFileKind CompilationResultProto_ObjFileKind_ObjFileKind_MIN = CompilationResultProto_ObjFileKind_UNKNOWN;
constexpr CompilationResultProto_ObjFileKind CompilationResultProto_ObjFileKind_ObjFileKind_MAX = CompilationResultProto_ObjFileKind_KERNELS;
constexpr int CompilationResultProto_ObjFileKind_ObjFileKind_ARRAYSIZE = CompilationResultProto_ObjFileKind_ObjFileKind_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CompilationResultProto_ObjFileKind_descriptor();
template<typename T>
inline const std::string& CompilationResultProto_ObjFileKind_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CompilationResultProto_ObjFileKind>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CompilationResultProto_ObjFileKind_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CompilationResultProto_ObjFileKind_descriptor(), enum_t_value);
}
inline bool CompilationResultProto_ObjFileKind_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CompilationResultProto_ObjFileKind* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CompilationResultProto_ObjFileKind>(
    CompilationResultProto_ObjFileKind_descriptor(), name, value);
}
// ===================================================================

class SymbolProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.SymbolProto) */ {
 public:
  inline SymbolProto() : SymbolProto(nullptr) {}
  ~SymbolProto() override;
  explicit PROTOBUF_CONSTEXPR SymbolProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SymbolProto(const SymbolProto& from);
  SymbolProto(SymbolProto&& from) noexcept
    : SymbolProto() {
    *this = ::std::move(from);
  }

  inline SymbolProto& operator=(const SymbolProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline SymbolProto& operator=(SymbolProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SymbolProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const SymbolProto* internal_default_instance() {
    return reinterpret_cast<const SymbolProto*>(
               &_SymbolProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SymbolProto& a, SymbolProto& b) {
    a.Swap(&b);
  }
  inline void Swap(SymbolProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SymbolProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SymbolProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SymbolProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SymbolProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SymbolProto& from) {
    SymbolProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SymbolProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.SymbolProto";
  }
  protected:
  explicit SymbolProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SymbolProto_FunctionTypeId FunctionTypeId;
  static constexpr FunctionTypeId UNKNOWN =
    SymbolProto_FunctionTypeId_UNKNOWN;
  static constexpr FunctionTypeId KERNEL =
    SymbolProto_FunctionTypeId_KERNEL;
  static constexpr FunctionTypeId COMPARATOR =
    SymbolProto_FunctionTypeId_COMPARATOR;
  static inline bool FunctionTypeId_IsValid(int value) {
    return SymbolProto_FunctionTypeId_IsValid(value);
  }
  static constexpr FunctionTypeId FunctionTypeId_MIN =
    SymbolProto_FunctionTypeId_FunctionTypeId_MIN;
  static constexpr FunctionTypeId FunctionTypeId_MAX =
    SymbolProto_FunctionTypeId_FunctionTypeId_MAX;
  static constexpr int FunctionTypeId_ARRAYSIZE =
    SymbolProto_FunctionTypeId_FunctionTypeId_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  FunctionTypeId_descriptor() {
    return SymbolProto_FunctionTypeId_descriptor();
  }
  template<typename T>
  static inline const std::string& FunctionTypeId_Name(T enum_t_value) {
    static_assert(::std::is_same<T, FunctionTypeId>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function FunctionTypeId_Name.");
    return SymbolProto_FunctionTypeId_Name(enum_t_value);
  }
  static inline bool FunctionTypeId_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      FunctionTypeId* value) {
    return SymbolProto_FunctionTypeId_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kFunctionTypeIdFieldNumber = 1,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .xla.cpu.SymbolProto.FunctionTypeId function_type_id = 1;
  void clear_function_type_id();
  ::xla::cpu::SymbolProto_FunctionTypeId function_type_id() const;
  void set_function_type_id(::xla::cpu::SymbolProto_FunctionTypeId value);
  private:
  ::xla::cpu::SymbolProto_FunctionTypeId _internal_function_type_id() const;
  void _internal_set_function_type_id(::xla::cpu::SymbolProto_FunctionTypeId value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.SymbolProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int function_type_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fexecutable_2eproto;
};
// -------------------------------------------------------------------

class CompilationResultProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:xla.cpu.CompilationResultProto) */ {
 public:
  inline CompilationResultProto() : CompilationResultProto(nullptr) {}
  ~CompilationResultProto() override;
  explicit PROTOBUF_CONSTEXPR CompilationResultProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompilationResultProto(const CompilationResultProto& from);
  CompilationResultProto(CompilationResultProto&& from) noexcept
    : CompilationResultProto() {
    *this = ::std::move(from);
  }

  inline CompilationResultProto& operator=(const CompilationResultProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompilationResultProto& operator=(CompilationResultProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompilationResultProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompilationResultProto* internal_default_instance() {
    return reinterpret_cast<const CompilationResultProto*>(
               &_CompilationResultProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CompilationResultProto& a, CompilationResultProto& b) {
    a.Swap(&b);
  }
  inline void Swap(CompilationResultProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompilationResultProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompilationResultProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompilationResultProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompilationResultProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompilationResultProto& from) {
    CompilationResultProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompilationResultProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "xla.cpu.CompilationResultProto";
  }
  protected:
  explicit CompilationResultProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CompilationResultProto_ObjFileKind ObjFileKind;
  static constexpr ObjFileKind UNKNOWN =
    CompilationResultProto_ObjFileKind_UNKNOWN;
  static constexpr ObjFileKind CLASSIC =
    CompilationResultProto_ObjFileKind_CLASSIC;
  static constexpr ObjFileKind KERNELS =
    CompilationResultProto_ObjFileKind_KERNELS;
  static inline bool ObjFileKind_IsValid(int value) {
    return CompilationResultProto_ObjFileKind_IsValid(value);
  }
  static constexpr ObjFileKind ObjFileKind_MIN =
    CompilationResultProto_ObjFileKind_ObjFileKind_MIN;
  static constexpr ObjFileKind ObjFileKind_MAX =
    CompilationResultProto_ObjFileKind_ObjFileKind_MAX;
  static constexpr int ObjFileKind_ARRAYSIZE =
    CompilationResultProto_ObjFileKind_ObjFileKind_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ObjFileKind_descriptor() {
    return CompilationResultProto_ObjFileKind_descriptor();
  }
  template<typename T>
  static inline const std::string& ObjFileKind_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ObjFileKind>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ObjFileKind_Name.");
    return CompilationResultProto_ObjFileKind_Name(enum_t_value);
  }
  static inline bool ObjFileKind_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ObjFileKind* value) {
    return CompilationResultProto_ObjFileKind_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kObjFilesFieldNumber = 4,
    kCompiledSymbolsFieldNumber = 7,
    kEntryFunctionNameFieldNumber = 3,
    kHloModuleFieldNumber = 1,
    kBufferAssignmentFieldNumber = 2,
    kThunkSequenceFieldNumber = 6,
    kObjFilesKindFieldNumber = 5,
  };
  // repeated bytes obj_files = 4;
  int obj_files_size() const;
  private:
  int _internal_obj_files_size() const;
  public:
  void clear_obj_files();
  const std::string& obj_files(int index) const;
  std::string* mutable_obj_files(int index);
  void set_obj_files(int index, const std::string& value);
  void set_obj_files(int index, std::string&& value);
  void set_obj_files(int index, const char* value);
  void set_obj_files(int index, const void* value, size_t size);
  std::string* add_obj_files();
  void add_obj_files(const std::string& value);
  void add_obj_files(std::string&& value);
  void add_obj_files(const char* value);
  void add_obj_files(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& obj_files() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_obj_files();
  private:
  const std::string& _internal_obj_files(int index) const;
  std::string* _internal_add_obj_files();
  public:

  // repeated .xla.cpu.SymbolProto compiled_symbols = 7;
  int compiled_symbols_size() const;
  private:
  int _internal_compiled_symbols_size() const;
  public:
  void clear_compiled_symbols();
  ::xla::cpu::SymbolProto* mutable_compiled_symbols(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::cpu::SymbolProto >*
      mutable_compiled_symbols();
  private:
  const ::xla::cpu::SymbolProto& _internal_compiled_symbols(int index) const;
  ::xla::cpu::SymbolProto* _internal_add_compiled_symbols();
  public:
  const ::xla::cpu::SymbolProto& compiled_symbols(int index) const;
  ::xla::cpu::SymbolProto* add_compiled_symbols();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::cpu::SymbolProto >&
      compiled_symbols() const;

  // string entry_function_name = 3;
  void clear_entry_function_name();
  const std::string& entry_function_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_entry_function_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_entry_function_name();
  PROTOBUF_NODISCARD std::string* release_entry_function_name();
  void set_allocated_entry_function_name(std::string* entry_function_name);
  private:
  const std::string& _internal_entry_function_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_entry_function_name(const std::string& value);
  std::string* _internal_mutable_entry_function_name();
  public:

  // .xla.HloModuleProtoWithConfig hlo_module = 1;
  bool has_hlo_module() const;
  private:
  bool _internal_has_hlo_module() const;
  public:
  void clear_hlo_module();
  const ::xla::HloModuleProtoWithConfig& hlo_module() const;
  PROTOBUF_NODISCARD ::xla::HloModuleProtoWithConfig* release_hlo_module();
  ::xla::HloModuleProtoWithConfig* mutable_hlo_module();
  void set_allocated_hlo_module(::xla::HloModuleProtoWithConfig* hlo_module);
  private:
  const ::xla::HloModuleProtoWithConfig& _internal_hlo_module() const;
  ::xla::HloModuleProtoWithConfig* _internal_mutable_hlo_module();
  public:
  void unsafe_arena_set_allocated_hlo_module(
      ::xla::HloModuleProtoWithConfig* hlo_module);
  ::xla::HloModuleProtoWithConfig* unsafe_arena_release_hlo_module();

  // .xla.BufferAssignmentProto buffer_assignment = 2;
  bool has_buffer_assignment() const;
  private:
  bool _internal_has_buffer_assignment() const;
  public:
  void clear_buffer_assignment();
  const ::xla::BufferAssignmentProto& buffer_assignment() const;
  PROTOBUF_NODISCARD ::xla::BufferAssignmentProto* release_buffer_assignment();
  ::xla::BufferAssignmentProto* mutable_buffer_assignment();
  void set_allocated_buffer_assignment(::xla::BufferAssignmentProto* buffer_assignment);
  private:
  const ::xla::BufferAssignmentProto& _internal_buffer_assignment() const;
  ::xla::BufferAssignmentProto* _internal_mutable_buffer_assignment();
  public:
  void unsafe_arena_set_allocated_buffer_assignment(
      ::xla::BufferAssignmentProto* buffer_assignment);
  ::xla::BufferAssignmentProto* unsafe_arena_release_buffer_assignment();

  // .xla.cpu.ThunkSequenceProto thunk_sequence = 6;
  bool has_thunk_sequence() const;
  private:
  bool _internal_has_thunk_sequence() const;
  public:
  void clear_thunk_sequence();
  const ::xla::cpu::ThunkSequenceProto& thunk_sequence() const;
  PROTOBUF_NODISCARD ::xla::cpu::ThunkSequenceProto* release_thunk_sequence();
  ::xla::cpu::ThunkSequenceProto* mutable_thunk_sequence();
  void set_allocated_thunk_sequence(::xla::cpu::ThunkSequenceProto* thunk_sequence);
  private:
  const ::xla::cpu::ThunkSequenceProto& _internal_thunk_sequence() const;
  ::xla::cpu::ThunkSequenceProto* _internal_mutable_thunk_sequence();
  public:
  void unsafe_arena_set_allocated_thunk_sequence(
      ::xla::cpu::ThunkSequenceProto* thunk_sequence);
  ::xla::cpu::ThunkSequenceProto* unsafe_arena_release_thunk_sequence();

  // .xla.cpu.CompilationResultProto.ObjFileKind obj_files_kind = 5;
  void clear_obj_files_kind();
  ::xla::cpu::CompilationResultProto_ObjFileKind obj_files_kind() const;
  void set_obj_files_kind(::xla::cpu::CompilationResultProto_ObjFileKind value);
  private:
  ::xla::cpu::CompilationResultProto_ObjFileKind _internal_obj_files_kind() const;
  void _internal_set_obj_files_kind(::xla::cpu::CompilationResultProto_ObjFileKind value);
  public:

  // @@protoc_insertion_point(class_scope:xla.cpu.CompilationResultProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> obj_files_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::cpu::SymbolProto > compiled_symbols_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr entry_function_name_;
    ::xla::HloModuleProtoWithConfig* hlo_module_;
    ::xla::BufferAssignmentProto* buffer_assignment_;
    ::xla::cpu::ThunkSequenceProto* thunk_sequence_;
    int obj_files_kind_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_xla_2fservice_2fcpu_2fexecutable_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SymbolProto

// .xla.cpu.SymbolProto.FunctionTypeId function_type_id = 1;
inline void SymbolProto::clear_function_type_id() {
  _impl_.function_type_id_ = 0;
}
inline ::xla::cpu::SymbolProto_FunctionTypeId SymbolProto::_internal_function_type_id() const {
  return static_cast< ::xla::cpu::SymbolProto_FunctionTypeId >(_impl_.function_type_id_);
}
inline ::xla::cpu::SymbolProto_FunctionTypeId SymbolProto::function_type_id() const {
  // @@protoc_insertion_point(field_get:xla.cpu.SymbolProto.function_type_id)
  return _internal_function_type_id();
}
inline void SymbolProto::_internal_set_function_type_id(::xla::cpu::SymbolProto_FunctionTypeId value) {
  
  _impl_.function_type_id_ = value;
}
inline void SymbolProto::set_function_type_id(::xla::cpu::SymbolProto_FunctionTypeId value) {
  _internal_set_function_type_id(value);
  // @@protoc_insertion_point(field_set:xla.cpu.SymbolProto.function_type_id)
}

// string name = 2;
inline void SymbolProto::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& SymbolProto::name() const {
  // @@protoc_insertion_point(field_get:xla.cpu.SymbolProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SymbolProto::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.cpu.SymbolProto.name)
}
inline std::string* SymbolProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:xla.cpu.SymbolProto.name)
  return _s;
}
inline const std::string& SymbolProto::_internal_name() const {
  return _impl_.name_.Get();
}
inline void SymbolProto::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* SymbolProto::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* SymbolProto::release_name() {
  // @@protoc_insertion_point(field_release:xla.cpu.SymbolProto.name)
  return _impl_.name_.Release();
}
inline void SymbolProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.SymbolProto.name)
}

// -------------------------------------------------------------------

// CompilationResultProto

// .xla.HloModuleProtoWithConfig hlo_module = 1;
inline bool CompilationResultProto::_internal_has_hlo_module() const {
  return this != internal_default_instance() && _impl_.hlo_module_ != nullptr;
}
inline bool CompilationResultProto::has_hlo_module() const {
  return _internal_has_hlo_module();
}
inline const ::xla::HloModuleProtoWithConfig& CompilationResultProto::_internal_hlo_module() const {
  const ::xla::HloModuleProtoWithConfig* p = _impl_.hlo_module_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::HloModuleProtoWithConfig&>(
      ::xla::_HloModuleProtoWithConfig_default_instance_);
}
inline const ::xla::HloModuleProtoWithConfig& CompilationResultProto::hlo_module() const {
  // @@protoc_insertion_point(field_get:xla.cpu.CompilationResultProto.hlo_module)
  return _internal_hlo_module();
}
inline void CompilationResultProto::unsafe_arena_set_allocated_hlo_module(
    ::xla::HloModuleProtoWithConfig* hlo_module) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hlo_module_);
  }
  _impl_.hlo_module_ = hlo_module;
  if (hlo_module) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.CompilationResultProto.hlo_module)
}
inline ::xla::HloModuleProtoWithConfig* CompilationResultProto::release_hlo_module() {
  
  ::xla::HloModuleProtoWithConfig* temp = _impl_.hlo_module_;
  _impl_.hlo_module_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::HloModuleProtoWithConfig* CompilationResultProto::unsafe_arena_release_hlo_module() {
  // @@protoc_insertion_point(field_release:xla.cpu.CompilationResultProto.hlo_module)
  
  ::xla::HloModuleProtoWithConfig* temp = _impl_.hlo_module_;
  _impl_.hlo_module_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProtoWithConfig* CompilationResultProto::_internal_mutable_hlo_module() {
  
  if (_impl_.hlo_module_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloModuleProtoWithConfig>(GetArenaForAllocation());
    _impl_.hlo_module_ = p;
  }
  return _impl_.hlo_module_;
}
inline ::xla::HloModuleProtoWithConfig* CompilationResultProto::mutable_hlo_module() {
  ::xla::HloModuleProtoWithConfig* _msg = _internal_mutable_hlo_module();
  // @@protoc_insertion_point(field_mutable:xla.cpu.CompilationResultProto.hlo_module)
  return _msg;
}
inline void CompilationResultProto::set_allocated_hlo_module(::xla::HloModuleProtoWithConfig* hlo_module) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hlo_module_);
  }
  if (hlo_module) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(hlo_module));
    if (message_arena != submessage_arena) {
      hlo_module = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hlo_module, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.hlo_module_ = hlo_module;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.CompilationResultProto.hlo_module)
}

// .xla.BufferAssignmentProto buffer_assignment = 2;
inline bool CompilationResultProto::_internal_has_buffer_assignment() const {
  return this != internal_default_instance() && _impl_.buffer_assignment_ != nullptr;
}
inline bool CompilationResultProto::has_buffer_assignment() const {
  return _internal_has_buffer_assignment();
}
inline const ::xla::BufferAssignmentProto& CompilationResultProto::_internal_buffer_assignment() const {
  const ::xla::BufferAssignmentProto* p = _impl_.buffer_assignment_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::BufferAssignmentProto&>(
      ::xla::_BufferAssignmentProto_default_instance_);
}
inline const ::xla::BufferAssignmentProto& CompilationResultProto::buffer_assignment() const {
  // @@protoc_insertion_point(field_get:xla.cpu.CompilationResultProto.buffer_assignment)
  return _internal_buffer_assignment();
}
inline void CompilationResultProto::unsafe_arena_set_allocated_buffer_assignment(
    ::xla::BufferAssignmentProto* buffer_assignment) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.buffer_assignment_);
  }
  _impl_.buffer_assignment_ = buffer_assignment;
  if (buffer_assignment) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.CompilationResultProto.buffer_assignment)
}
inline ::xla::BufferAssignmentProto* CompilationResultProto::release_buffer_assignment() {
  
  ::xla::BufferAssignmentProto* temp = _impl_.buffer_assignment_;
  _impl_.buffer_assignment_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::BufferAssignmentProto* CompilationResultProto::unsafe_arena_release_buffer_assignment() {
  // @@protoc_insertion_point(field_release:xla.cpu.CompilationResultProto.buffer_assignment)
  
  ::xla::BufferAssignmentProto* temp = _impl_.buffer_assignment_;
  _impl_.buffer_assignment_ = nullptr;
  return temp;
}
inline ::xla::BufferAssignmentProto* CompilationResultProto::_internal_mutable_buffer_assignment() {
  
  if (_impl_.buffer_assignment_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::BufferAssignmentProto>(GetArenaForAllocation());
    _impl_.buffer_assignment_ = p;
  }
  return _impl_.buffer_assignment_;
}
inline ::xla::BufferAssignmentProto* CompilationResultProto::mutable_buffer_assignment() {
  ::xla::BufferAssignmentProto* _msg = _internal_mutable_buffer_assignment();
  // @@protoc_insertion_point(field_mutable:xla.cpu.CompilationResultProto.buffer_assignment)
  return _msg;
}
inline void CompilationResultProto::set_allocated_buffer_assignment(::xla::BufferAssignmentProto* buffer_assignment) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.buffer_assignment_);
  }
  if (buffer_assignment) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(buffer_assignment));
    if (message_arena != submessage_arena) {
      buffer_assignment = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, buffer_assignment, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.buffer_assignment_ = buffer_assignment;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.CompilationResultProto.buffer_assignment)
}

// string entry_function_name = 3;
inline void CompilationResultProto::clear_entry_function_name() {
  _impl_.entry_function_name_.ClearToEmpty();
}
inline const std::string& CompilationResultProto::entry_function_name() const {
  // @@protoc_insertion_point(field_get:xla.cpu.CompilationResultProto.entry_function_name)
  return _internal_entry_function_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompilationResultProto::set_entry_function_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.entry_function_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:xla.cpu.CompilationResultProto.entry_function_name)
}
inline std::string* CompilationResultProto::mutable_entry_function_name() {
  std::string* _s = _internal_mutable_entry_function_name();
  // @@protoc_insertion_point(field_mutable:xla.cpu.CompilationResultProto.entry_function_name)
  return _s;
}
inline const std::string& CompilationResultProto::_internal_entry_function_name() const {
  return _impl_.entry_function_name_.Get();
}
inline void CompilationResultProto::_internal_set_entry_function_name(const std::string& value) {
  
  _impl_.entry_function_name_.Set(value, GetArenaForAllocation());
}
inline std::string* CompilationResultProto::_internal_mutable_entry_function_name() {
  
  return _impl_.entry_function_name_.Mutable(GetArenaForAllocation());
}
inline std::string* CompilationResultProto::release_entry_function_name() {
  // @@protoc_insertion_point(field_release:xla.cpu.CompilationResultProto.entry_function_name)
  return _impl_.entry_function_name_.Release();
}
inline void CompilationResultProto::set_allocated_entry_function_name(std::string* entry_function_name) {
  if (entry_function_name != nullptr) {
    
  } else {
    
  }
  _impl_.entry_function_name_.SetAllocated(entry_function_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.entry_function_name_.IsDefault()) {
    _impl_.entry_function_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.CompilationResultProto.entry_function_name)
}

// repeated bytes obj_files = 4;
inline int CompilationResultProto::_internal_obj_files_size() const {
  return _impl_.obj_files_.size();
}
inline int CompilationResultProto::obj_files_size() const {
  return _internal_obj_files_size();
}
inline void CompilationResultProto::clear_obj_files() {
  _impl_.obj_files_.Clear();
}
inline std::string* CompilationResultProto::add_obj_files() {
  std::string* _s = _internal_add_obj_files();
  // @@protoc_insertion_point(field_add_mutable:xla.cpu.CompilationResultProto.obj_files)
  return _s;
}
inline const std::string& CompilationResultProto::_internal_obj_files(int index) const {
  return _impl_.obj_files_.Get(index);
}
inline const std::string& CompilationResultProto::obj_files(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.CompilationResultProto.obj_files)
  return _internal_obj_files(index);
}
inline std::string* CompilationResultProto::mutable_obj_files(int index) {
  // @@protoc_insertion_point(field_mutable:xla.cpu.CompilationResultProto.obj_files)
  return _impl_.obj_files_.Mutable(index);
}
inline void CompilationResultProto::set_obj_files(int index, const std::string& value) {
  _impl_.obj_files_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:xla.cpu.CompilationResultProto.obj_files)
}
inline void CompilationResultProto::set_obj_files(int index, std::string&& value) {
  _impl_.obj_files_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:xla.cpu.CompilationResultProto.obj_files)
}
inline void CompilationResultProto::set_obj_files(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.obj_files_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:xla.cpu.CompilationResultProto.obj_files)
}
inline void CompilationResultProto::set_obj_files(int index, const void* value, size_t size) {
  _impl_.obj_files_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:xla.cpu.CompilationResultProto.obj_files)
}
inline std::string* CompilationResultProto::_internal_add_obj_files() {
  return _impl_.obj_files_.Add();
}
inline void CompilationResultProto::add_obj_files(const std::string& value) {
  _impl_.obj_files_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:xla.cpu.CompilationResultProto.obj_files)
}
inline void CompilationResultProto::add_obj_files(std::string&& value) {
  _impl_.obj_files_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:xla.cpu.CompilationResultProto.obj_files)
}
inline void CompilationResultProto::add_obj_files(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.obj_files_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:xla.cpu.CompilationResultProto.obj_files)
}
inline void CompilationResultProto::add_obj_files(const void* value, size_t size) {
  _impl_.obj_files_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:xla.cpu.CompilationResultProto.obj_files)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CompilationResultProto::obj_files() const {
  // @@protoc_insertion_point(field_list:xla.cpu.CompilationResultProto.obj_files)
  return _impl_.obj_files_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CompilationResultProto::mutable_obj_files() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.CompilationResultProto.obj_files)
  return &_impl_.obj_files_;
}

// .xla.cpu.CompilationResultProto.ObjFileKind obj_files_kind = 5;
inline void CompilationResultProto::clear_obj_files_kind() {
  _impl_.obj_files_kind_ = 0;
}
inline ::xla::cpu::CompilationResultProto_ObjFileKind CompilationResultProto::_internal_obj_files_kind() const {
  return static_cast< ::xla::cpu::CompilationResultProto_ObjFileKind >(_impl_.obj_files_kind_);
}
inline ::xla::cpu::CompilationResultProto_ObjFileKind CompilationResultProto::obj_files_kind() const {
  // @@protoc_insertion_point(field_get:xla.cpu.CompilationResultProto.obj_files_kind)
  return _internal_obj_files_kind();
}
inline void CompilationResultProto::_internal_set_obj_files_kind(::xla::cpu::CompilationResultProto_ObjFileKind value) {
  
  _impl_.obj_files_kind_ = value;
}
inline void CompilationResultProto::set_obj_files_kind(::xla::cpu::CompilationResultProto_ObjFileKind value) {
  _internal_set_obj_files_kind(value);
  // @@protoc_insertion_point(field_set:xla.cpu.CompilationResultProto.obj_files_kind)
}

// .xla.cpu.ThunkSequenceProto thunk_sequence = 6;
inline bool CompilationResultProto::_internal_has_thunk_sequence() const {
  return this != internal_default_instance() && _impl_.thunk_sequence_ != nullptr;
}
inline bool CompilationResultProto::has_thunk_sequence() const {
  return _internal_has_thunk_sequence();
}
inline const ::xla::cpu::ThunkSequenceProto& CompilationResultProto::_internal_thunk_sequence() const {
  const ::xla::cpu::ThunkSequenceProto* p = _impl_.thunk_sequence_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::cpu::ThunkSequenceProto&>(
      ::xla::cpu::_ThunkSequenceProto_default_instance_);
}
inline const ::xla::cpu::ThunkSequenceProto& CompilationResultProto::thunk_sequence() const {
  // @@protoc_insertion_point(field_get:xla.cpu.CompilationResultProto.thunk_sequence)
  return _internal_thunk_sequence();
}
inline void CompilationResultProto::unsafe_arena_set_allocated_thunk_sequence(
    ::xla::cpu::ThunkSequenceProto* thunk_sequence) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.thunk_sequence_);
  }
  _impl_.thunk_sequence_ = thunk_sequence;
  if (thunk_sequence) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:xla.cpu.CompilationResultProto.thunk_sequence)
}
inline ::xla::cpu::ThunkSequenceProto* CompilationResultProto::release_thunk_sequence() {
  
  ::xla::cpu::ThunkSequenceProto* temp = _impl_.thunk_sequence_;
  _impl_.thunk_sequence_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::cpu::ThunkSequenceProto* CompilationResultProto::unsafe_arena_release_thunk_sequence() {
  // @@protoc_insertion_point(field_release:xla.cpu.CompilationResultProto.thunk_sequence)
  
  ::xla::cpu::ThunkSequenceProto* temp = _impl_.thunk_sequence_;
  _impl_.thunk_sequence_ = nullptr;
  return temp;
}
inline ::xla::cpu::ThunkSequenceProto* CompilationResultProto::_internal_mutable_thunk_sequence() {
  
  if (_impl_.thunk_sequence_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::cpu::ThunkSequenceProto>(GetArenaForAllocation());
    _impl_.thunk_sequence_ = p;
  }
  return _impl_.thunk_sequence_;
}
inline ::xla::cpu::ThunkSequenceProto* CompilationResultProto::mutable_thunk_sequence() {
  ::xla::cpu::ThunkSequenceProto* _msg = _internal_mutable_thunk_sequence();
  // @@protoc_insertion_point(field_mutable:xla.cpu.CompilationResultProto.thunk_sequence)
  return _msg;
}
inline void CompilationResultProto::set_allocated_thunk_sequence(::xla::cpu::ThunkSequenceProto* thunk_sequence) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.thunk_sequence_);
  }
  if (thunk_sequence) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(thunk_sequence));
    if (message_arena != submessage_arena) {
      thunk_sequence = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, thunk_sequence, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.thunk_sequence_ = thunk_sequence;
  // @@protoc_insertion_point(field_set_allocated:xla.cpu.CompilationResultProto.thunk_sequence)
}

// repeated .xla.cpu.SymbolProto compiled_symbols = 7;
inline int CompilationResultProto::_internal_compiled_symbols_size() const {
  return _impl_.compiled_symbols_.size();
}
inline int CompilationResultProto::compiled_symbols_size() const {
  return _internal_compiled_symbols_size();
}
inline void CompilationResultProto::clear_compiled_symbols() {
  _impl_.compiled_symbols_.Clear();
}
inline ::xla::cpu::SymbolProto* CompilationResultProto::mutable_compiled_symbols(int index) {
  // @@protoc_insertion_point(field_mutable:xla.cpu.CompilationResultProto.compiled_symbols)
  return _impl_.compiled_symbols_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::cpu::SymbolProto >*
CompilationResultProto::mutable_compiled_symbols() {
  // @@protoc_insertion_point(field_mutable_list:xla.cpu.CompilationResultProto.compiled_symbols)
  return &_impl_.compiled_symbols_;
}
inline const ::xla::cpu::SymbolProto& CompilationResultProto::_internal_compiled_symbols(int index) const {
  return _impl_.compiled_symbols_.Get(index);
}
inline const ::xla::cpu::SymbolProto& CompilationResultProto::compiled_symbols(int index) const {
  // @@protoc_insertion_point(field_get:xla.cpu.CompilationResultProto.compiled_symbols)
  return _internal_compiled_symbols(index);
}
inline ::xla::cpu::SymbolProto* CompilationResultProto::_internal_add_compiled_symbols() {
  return _impl_.compiled_symbols_.Add();
}
inline ::xla::cpu::SymbolProto* CompilationResultProto::add_compiled_symbols() {
  ::xla::cpu::SymbolProto* _add = _internal_add_compiled_symbols();
  // @@protoc_insertion_point(field_add:xla.cpu.CompilationResultProto.compiled_symbols)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::xla::cpu::SymbolProto >&
CompilationResultProto::compiled_symbols() const {
  // @@protoc_insertion_point(field_list:xla.cpu.CompilationResultProto.compiled_symbols)
  return _impl_.compiled_symbols_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace cpu
}  // namespace xla

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::xla::cpu::SymbolProto_FunctionTypeId> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::cpu::SymbolProto_FunctionTypeId>() {
  return ::xla::cpu::SymbolProto_FunctionTypeId_descriptor();
}
template <> struct is_proto_enum< ::xla::cpu::CompilationResultProto_ObjFileKind> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::xla::cpu::CompilationResultProto_ObjFileKind>() {
  return ::xla::cpu::CompilationResultProto_ObjFileKind_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_xla_2fservice_2fcpu_2fexecutable_2eproto
