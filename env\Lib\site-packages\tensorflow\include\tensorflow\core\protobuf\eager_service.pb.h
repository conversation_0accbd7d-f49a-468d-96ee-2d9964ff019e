// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/eager_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/function.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/versions.pb.h"
#include "tensorflow/core/protobuf/remote_tensor_handle.pb.h"
#include "tensorflow/core/protobuf/tensorflow_server.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
namespace tensorflow {
namespace eager {
class CleanupFunctionOp;
struct CleanupFunctionOpDefaultTypeInternal;
extern CleanupFunctionOpDefaultTypeInternal _CleanupFunctionOp_default_instance_;
class CloseContextRequest;
struct CloseContextRequestDefaultTypeInternal;
extern CloseContextRequestDefaultTypeInternal _CloseContextRequest_default_instance_;
class CloseContextResponse;
struct CloseContextResponseDefaultTypeInternal;
extern CloseContextResponseDefaultTypeInternal _CloseContextResponse_default_instance_;
class CreateContextRequest;
struct CreateContextRequestDefaultTypeInternal;
extern CreateContextRequestDefaultTypeInternal _CreateContextRequest_default_instance_;
class CreateContextResponse;
struct CreateContextResponseDefaultTypeInternal;
extern CreateContextResponseDefaultTypeInternal _CreateContextResponse_default_instance_;
class EnqueueRequest;
struct EnqueueRequestDefaultTypeInternal;
extern EnqueueRequestDefaultTypeInternal _EnqueueRequest_default_instance_;
class EnqueueResponse;
struct EnqueueResponseDefaultTypeInternal;
extern EnqueueResponseDefaultTypeInternal _EnqueueResponse_default_instance_;
class KeepAliveRequest;
struct KeepAliveRequestDefaultTypeInternal;
extern KeepAliveRequestDefaultTypeInternal _KeepAliveRequest_default_instance_;
class KeepAliveResponse;
struct KeepAliveResponseDefaultTypeInternal;
extern KeepAliveResponseDefaultTypeInternal _KeepAliveResponse_default_instance_;
class Operation;
struct OperationDefaultTypeInternal;
extern OperationDefaultTypeInternal _Operation_default_instance_;
class Operation_AttrsEntry_DoNotUse;
struct Operation_AttrsEntry_DoNotUseDefaultTypeInternal;
extern Operation_AttrsEntry_DoNotUseDefaultTypeInternal _Operation_AttrsEntry_DoNotUse_default_instance_;
class Operation_Input;
struct Operation_InputDefaultTypeInternal;
extern Operation_InputDefaultTypeInternal _Operation_Input_default_instance_;
class QueueItem;
struct QueueItemDefaultTypeInternal;
extern QueueItemDefaultTypeInternal _QueueItem_default_instance_;
class QueueResponse;
struct QueueResponseDefaultTypeInternal;
extern QueueResponseDefaultTypeInternal _QueueResponse_default_instance_;
class RegisterFunctionOp;
struct RegisterFunctionOpDefaultTypeInternal;
extern RegisterFunctionOpDefaultTypeInternal _RegisterFunctionOp_default_instance_;
class RemoveFunctionOp;
struct RemoveFunctionOpDefaultTypeInternal;
extern RemoveFunctionOpDefaultTypeInternal _RemoveFunctionOp_default_instance_;
class RunComponentFunctionRequest;
struct RunComponentFunctionRequestDefaultTypeInternal;
extern RunComponentFunctionRequestDefaultTypeInternal _RunComponentFunctionRequest_default_instance_;
class RunComponentFunctionResponse;
struct RunComponentFunctionResponseDefaultTypeInternal;
extern RunComponentFunctionResponseDefaultTypeInternal _RunComponentFunctionResponse_default_instance_;
class SendPackedHandleOp;
struct SendPackedHandleOpDefaultTypeInternal;
extern SendPackedHandleOpDefaultTypeInternal _SendPackedHandleOp_default_instance_;
class SendPackedHandleOp_Handle;
struct SendPackedHandleOp_HandleDefaultTypeInternal;
extern SendPackedHandleOp_HandleDefaultTypeInternal _SendPackedHandleOp_Handle_default_instance_;
class SendPackedHandleOp_LocalTensorHandle;
struct SendPackedHandleOp_LocalTensorHandleDefaultTypeInternal;
extern SendPackedHandleOp_LocalTensorHandleDefaultTypeInternal _SendPackedHandleOp_LocalTensorHandle_default_instance_;
class SendTensorOp;
struct SendTensorOpDefaultTypeInternal;
extern SendTensorOpDefaultTypeInternal _SendTensorOp_default_instance_;
class SyncRemoteExecutorForStream;
struct SyncRemoteExecutorForStreamDefaultTypeInternal;
extern SyncRemoteExecutorForStreamDefaultTypeInternal _SyncRemoteExecutorForStream_default_instance_;
class UpdateContextRequest;
struct UpdateContextRequestDefaultTypeInternal;
extern UpdateContextRequestDefaultTypeInternal _UpdateContextRequest_default_instance_;
class UpdateContextResponse;
struct UpdateContextResponseDefaultTypeInternal;
extern UpdateContextResponseDefaultTypeInternal _UpdateContextResponse_default_instance_;
class WaitQueueDoneRequest;
struct WaitQueueDoneRequestDefaultTypeInternal;
extern WaitQueueDoneRequestDefaultTypeInternal _WaitQueueDoneRequest_default_instance_;
class WaitQueueDoneResponse;
struct WaitQueueDoneResponseDefaultTypeInternal;
extern WaitQueueDoneResponseDefaultTypeInternal _WaitQueueDoneResponse_default_instance_;
}  // namespace eager
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::eager::CleanupFunctionOp* Arena::CreateMaybeMessage<::tensorflow::eager::CleanupFunctionOp>(Arena*);
template<> ::tensorflow::eager::CloseContextRequest* Arena::CreateMaybeMessage<::tensorflow::eager::CloseContextRequest>(Arena*);
template<> ::tensorflow::eager::CloseContextResponse* Arena::CreateMaybeMessage<::tensorflow::eager::CloseContextResponse>(Arena*);
template<> ::tensorflow::eager::CreateContextRequest* Arena::CreateMaybeMessage<::tensorflow::eager::CreateContextRequest>(Arena*);
template<> ::tensorflow::eager::CreateContextResponse* Arena::CreateMaybeMessage<::tensorflow::eager::CreateContextResponse>(Arena*);
template<> ::tensorflow::eager::EnqueueRequest* Arena::CreateMaybeMessage<::tensorflow::eager::EnqueueRequest>(Arena*);
template<> ::tensorflow::eager::EnqueueResponse* Arena::CreateMaybeMessage<::tensorflow::eager::EnqueueResponse>(Arena*);
template<> ::tensorflow::eager::KeepAliveRequest* Arena::CreateMaybeMessage<::tensorflow::eager::KeepAliveRequest>(Arena*);
template<> ::tensorflow::eager::KeepAliveResponse* Arena::CreateMaybeMessage<::tensorflow::eager::KeepAliveResponse>(Arena*);
template<> ::tensorflow::eager::Operation* Arena::CreateMaybeMessage<::tensorflow::eager::Operation>(Arena*);
template<> ::tensorflow::eager::Operation_AttrsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::eager::Operation_AttrsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::eager::Operation_Input* Arena::CreateMaybeMessage<::tensorflow::eager::Operation_Input>(Arena*);
template<> ::tensorflow::eager::QueueItem* Arena::CreateMaybeMessage<::tensorflow::eager::QueueItem>(Arena*);
template<> ::tensorflow::eager::QueueResponse* Arena::CreateMaybeMessage<::tensorflow::eager::QueueResponse>(Arena*);
template<> ::tensorflow::eager::RegisterFunctionOp* Arena::CreateMaybeMessage<::tensorflow::eager::RegisterFunctionOp>(Arena*);
template<> ::tensorflow::eager::RemoveFunctionOp* Arena::CreateMaybeMessage<::tensorflow::eager::RemoveFunctionOp>(Arena*);
template<> ::tensorflow::eager::RunComponentFunctionRequest* Arena::CreateMaybeMessage<::tensorflow::eager::RunComponentFunctionRequest>(Arena*);
template<> ::tensorflow::eager::RunComponentFunctionResponse* Arena::CreateMaybeMessage<::tensorflow::eager::RunComponentFunctionResponse>(Arena*);
template<> ::tensorflow::eager::SendPackedHandleOp* Arena::CreateMaybeMessage<::tensorflow::eager::SendPackedHandleOp>(Arena*);
template<> ::tensorflow::eager::SendPackedHandleOp_Handle* Arena::CreateMaybeMessage<::tensorflow::eager::SendPackedHandleOp_Handle>(Arena*);
template<> ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* Arena::CreateMaybeMessage<::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle>(Arena*);
template<> ::tensorflow::eager::SendTensorOp* Arena::CreateMaybeMessage<::tensorflow::eager::SendTensorOp>(Arena*);
template<> ::tensorflow::eager::SyncRemoteExecutorForStream* Arena::CreateMaybeMessage<::tensorflow::eager::SyncRemoteExecutorForStream>(Arena*);
template<> ::tensorflow::eager::UpdateContextRequest* Arena::CreateMaybeMessage<::tensorflow::eager::UpdateContextRequest>(Arena*);
template<> ::tensorflow::eager::UpdateContextResponse* Arena::CreateMaybeMessage<::tensorflow::eager::UpdateContextResponse>(Arena*);
template<> ::tensorflow::eager::WaitQueueDoneRequest* Arena::CreateMaybeMessage<::tensorflow::eager::WaitQueueDoneRequest>(Arena*);
template<> ::tensorflow::eager::WaitQueueDoneResponse* Arena::CreateMaybeMessage<::tensorflow::eager::WaitQueueDoneResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace eager {

// ===================================================================

class Operation_Input final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.Operation.Input) */ {
 public:
  inline Operation_Input() : Operation_Input(nullptr) {}
  ~Operation_Input() override;
  explicit PROTOBUF_CONSTEXPR Operation_Input(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Operation_Input(const Operation_Input& from);
  Operation_Input(Operation_Input&& from) noexcept
    : Operation_Input() {
    *this = ::std::move(from);
  }

  inline Operation_Input& operator=(const Operation_Input& from) {
    CopyFrom(from);
    return *this;
  }
  inline Operation_Input& operator=(Operation_Input&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Operation_Input& default_instance() {
    return *internal_default_instance();
  }
  enum ItemCase {
    kRemoteHandle = 1,
    kTensor = 2,
    ITEM_NOT_SET = 0,
  };

  static inline const Operation_Input* internal_default_instance() {
    return reinterpret_cast<const Operation_Input*>(
               &_Operation_Input_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Operation_Input& a, Operation_Input& b) {
    a.Swap(&b);
  }
  inline void Swap(Operation_Input* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Operation_Input* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Operation_Input* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Operation_Input>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Operation_Input& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Operation_Input& from) {
    Operation_Input::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Operation_Input* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.Operation.Input";
  }
  protected:
  explicit Operation_Input(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRemoteHandleFieldNumber = 1,
    kTensorFieldNumber = 2,
  };
  // .tensorflow.eager.RemoteTensorHandle remote_handle = 1;
  bool has_remote_handle() const;
  private:
  bool _internal_has_remote_handle() const;
  public:
  void clear_remote_handle();
  const ::tensorflow::eager::RemoteTensorHandle& remote_handle() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::RemoteTensorHandle* release_remote_handle();
  ::tensorflow::eager::RemoteTensorHandle* mutable_remote_handle();
  void set_allocated_remote_handle(::tensorflow::eager::RemoteTensorHandle* remote_handle);
  private:
  const ::tensorflow::eager::RemoteTensorHandle& _internal_remote_handle() const;
  ::tensorflow::eager::RemoteTensorHandle* _internal_mutable_remote_handle();
  public:
  void unsafe_arena_set_allocated_remote_handle(
      ::tensorflow::eager::RemoteTensorHandle* remote_handle);
  ::tensorflow::eager::RemoteTensorHandle* unsafe_arena_release_remote_handle();

  // .tensorflow.TensorProto tensor = 2;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  ::tensorflow::TensorProto* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  void clear_item();
  ItemCase item_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.eager.Operation.Input)
 private:
  class _Internal;
  void set_has_remote_handle();
  void set_has_tensor();

  inline bool has_item() const;
  inline void clear_has_item();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union ItemUnion {
      constexpr ItemUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::eager::RemoteTensorHandle* remote_handle_;
      ::tensorflow::TensorProto* tensor_;
    } item_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class Operation_AttrsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Operation_AttrsEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<Operation_AttrsEntry_DoNotUse, 
    std::string, ::tensorflow::AttrValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  Operation_AttrsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR Operation_AttrsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit Operation_AttrsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const Operation_AttrsEntry_DoNotUse& other);
  static const Operation_AttrsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const Operation_AttrsEntry_DoNotUse*>(&_Operation_AttrsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.eager.Operation.AttrsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};

// -------------------------------------------------------------------

class Operation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.Operation) */ {
 public:
  inline Operation() : Operation(nullptr) {}
  ~Operation() override;
  explicit PROTOBUF_CONSTEXPR Operation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Operation(const Operation& from);
  Operation(Operation&& from) noexcept
    : Operation() {
    *this = ::std::move(from);
  }

  inline Operation& operator=(const Operation& from) {
    CopyFrom(from);
    return *this;
  }
  inline Operation& operator=(Operation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Operation& default_instance() {
    return *internal_default_instance();
  }
  static inline const Operation* internal_default_instance() {
    return reinterpret_cast<const Operation*>(
               &_Operation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Operation& a, Operation& b) {
    a.Swap(&b);
  }
  inline void Swap(Operation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Operation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Operation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Operation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Operation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Operation& from) {
    Operation::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Operation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.Operation";
  }
  protected:
  explicit Operation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Operation_Input Input;

  // accessors -------------------------------------------------------

  enum : int {
    kControlOpIdsFieldNumber = 4,
    kAttrsFieldNumber = 5,
    kOpInputsFieldNumber = 10,
    kNameFieldNumber = 2,
    kDeviceFieldNumber = 6,
    kIdFieldNumber = 1,
    kFuncStepIdFieldNumber = 8,
    kIsComponentFunctionFieldNumber = 7,
    kIsFunctionFieldNumber = 9,
  };
  // repeated int64 control_op_ids = 4;
  int control_op_ids_size() const;
  private:
  int _internal_control_op_ids_size() const;
  public:
  void clear_control_op_ids();
  private:
  int64_t _internal_control_op_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_control_op_ids() const;
  void _internal_add_control_op_ids(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_control_op_ids();
  public:
  int64_t control_op_ids(int index) const;
  void set_control_op_ids(int index, int64_t value);
  void add_control_op_ids(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      control_op_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_control_op_ids();

  // map<string, .tensorflow.AttrValue> attrs = 5;
  int attrs_size() const;
  private:
  int _internal_attrs_size() const;
  public:
  void clear_attrs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      _internal_attrs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      _internal_mutable_attrs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
      attrs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
      mutable_attrs();

  // repeated .tensorflow.eager.Operation.Input op_inputs = 10;
  int op_inputs_size() const;
  private:
  int _internal_op_inputs_size() const;
  public:
  void clear_op_inputs();
  ::tensorflow::eager::Operation_Input* mutable_op_inputs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input >*
      mutable_op_inputs();
  private:
  const ::tensorflow::eager::Operation_Input& _internal_op_inputs(int index) const;
  ::tensorflow::eager::Operation_Input* _internal_add_op_inputs();
  public:
  const ::tensorflow::eager::Operation_Input& op_inputs(int index) const;
  ::tensorflow::eager::Operation_Input* add_op_inputs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input >&
      op_inputs() const;

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string device = 6;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // int64 func_step_id = 8;
  void clear_func_step_id();
  int64_t func_step_id() const;
  void set_func_step_id(int64_t value);
  private:
  int64_t _internal_func_step_id() const;
  void _internal_set_func_step_id(int64_t value);
  public:

  // bool is_component_function = 7;
  void clear_is_component_function();
  bool is_component_function() const;
  void set_is_component_function(bool value);
  private:
  bool _internal_is_component_function() const;
  void _internal_set_is_component_function(bool value);
  public:

  // bool is_function = 9;
  void clear_is_function();
  bool is_function() const;
  void set_is_function(bool value);
  private:
  bool _internal_is_function() const;
  void _internal_set_is_function(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.Operation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > control_op_ids_;
    mutable std::atomic<int> _control_op_ids_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        Operation_AttrsEntry_DoNotUse,
        std::string, ::tensorflow::AttrValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> attrs_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input > op_inputs_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    int64_t id_;
    int64_t func_step_id_;
    bool is_component_function_;
    bool is_function_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class QueueItem final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.QueueItem) */ {
 public:
  inline QueueItem() : QueueItem(nullptr) {}
  ~QueueItem() override;
  explicit PROTOBUF_CONSTEXPR QueueItem(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QueueItem(const QueueItem& from);
  QueueItem(QueueItem&& from) noexcept
    : QueueItem() {
    *this = ::std::move(from);
  }

  inline QueueItem& operator=(const QueueItem& from) {
    CopyFrom(from);
    return *this;
  }
  inline QueueItem& operator=(QueueItem&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QueueItem& default_instance() {
    return *internal_default_instance();
  }
  enum ItemCase {
    kHandleToDecref = 1,
    kOperation = 2,
    kSendTensor = 3,
    kRegisterFunction = 4,
    kCleanupFunction = 5,
    kSyncRemoteExecutorForStream = 6,
    kSendPackedHandle = 7,
    kRemoveFunction = 8,
    ITEM_NOT_SET = 0,
  };

  static inline const QueueItem* internal_default_instance() {
    return reinterpret_cast<const QueueItem*>(
               &_QueueItem_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(QueueItem& a, QueueItem& b) {
    a.Swap(&b);
  }
  inline void Swap(QueueItem* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QueueItem* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QueueItem* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QueueItem>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QueueItem& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QueueItem& from) {
    QueueItem::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QueueItem* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.QueueItem";
  }
  protected:
  explicit QueueItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleToDecrefFieldNumber = 1,
    kOperationFieldNumber = 2,
    kSendTensorFieldNumber = 3,
    kRegisterFunctionFieldNumber = 4,
    kCleanupFunctionFieldNumber = 5,
    kSyncRemoteExecutorForStreamFieldNumber = 6,
    kSendPackedHandleFieldNumber = 7,
    kRemoveFunctionFieldNumber = 8,
  };
  // .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
  bool has_handle_to_decref() const;
  private:
  bool _internal_has_handle_to_decref() const;
  public:
  void clear_handle_to_decref();
  const ::tensorflow::eager::RemoteTensorHandle& handle_to_decref() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::RemoteTensorHandle* release_handle_to_decref();
  ::tensorflow::eager::RemoteTensorHandle* mutable_handle_to_decref();
  void set_allocated_handle_to_decref(::tensorflow::eager::RemoteTensorHandle* handle_to_decref);
  private:
  const ::tensorflow::eager::RemoteTensorHandle& _internal_handle_to_decref() const;
  ::tensorflow::eager::RemoteTensorHandle* _internal_mutable_handle_to_decref();
  public:
  void unsafe_arena_set_allocated_handle_to_decref(
      ::tensorflow::eager::RemoteTensorHandle* handle_to_decref);
  ::tensorflow::eager::RemoteTensorHandle* unsafe_arena_release_handle_to_decref();

  // .tensorflow.eager.Operation operation = 2;
  bool has_operation() const;
  private:
  bool _internal_has_operation() const;
  public:
  void clear_operation();
  const ::tensorflow::eager::Operation& operation() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::Operation* release_operation();
  ::tensorflow::eager::Operation* mutable_operation();
  void set_allocated_operation(::tensorflow::eager::Operation* operation);
  private:
  const ::tensorflow::eager::Operation& _internal_operation() const;
  ::tensorflow::eager::Operation* _internal_mutable_operation();
  public:
  void unsafe_arena_set_allocated_operation(
      ::tensorflow::eager::Operation* operation);
  ::tensorflow::eager::Operation* unsafe_arena_release_operation();

  // .tensorflow.eager.SendTensorOp send_tensor = 3;
  bool has_send_tensor() const;
  private:
  bool _internal_has_send_tensor() const;
  public:
  void clear_send_tensor();
  const ::tensorflow::eager::SendTensorOp& send_tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::SendTensorOp* release_send_tensor();
  ::tensorflow::eager::SendTensorOp* mutable_send_tensor();
  void set_allocated_send_tensor(::tensorflow::eager::SendTensorOp* send_tensor);
  private:
  const ::tensorflow::eager::SendTensorOp& _internal_send_tensor() const;
  ::tensorflow::eager::SendTensorOp* _internal_mutable_send_tensor();
  public:
  void unsafe_arena_set_allocated_send_tensor(
      ::tensorflow::eager::SendTensorOp* send_tensor);
  ::tensorflow::eager::SendTensorOp* unsafe_arena_release_send_tensor();

  // .tensorflow.eager.RegisterFunctionOp register_function = 4;
  bool has_register_function() const;
  private:
  bool _internal_has_register_function() const;
  public:
  void clear_register_function();
  const ::tensorflow::eager::RegisterFunctionOp& register_function() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::RegisterFunctionOp* release_register_function();
  ::tensorflow::eager::RegisterFunctionOp* mutable_register_function();
  void set_allocated_register_function(::tensorflow::eager::RegisterFunctionOp* register_function);
  private:
  const ::tensorflow::eager::RegisterFunctionOp& _internal_register_function() const;
  ::tensorflow::eager::RegisterFunctionOp* _internal_mutable_register_function();
  public:
  void unsafe_arena_set_allocated_register_function(
      ::tensorflow::eager::RegisterFunctionOp* register_function);
  ::tensorflow::eager::RegisterFunctionOp* unsafe_arena_release_register_function();

  // .tensorflow.eager.CleanupFunctionOp cleanup_function = 5;
  bool has_cleanup_function() const;
  private:
  bool _internal_has_cleanup_function() const;
  public:
  void clear_cleanup_function();
  const ::tensorflow::eager::CleanupFunctionOp& cleanup_function() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::CleanupFunctionOp* release_cleanup_function();
  ::tensorflow::eager::CleanupFunctionOp* mutable_cleanup_function();
  void set_allocated_cleanup_function(::tensorflow::eager::CleanupFunctionOp* cleanup_function);
  private:
  const ::tensorflow::eager::CleanupFunctionOp& _internal_cleanup_function() const;
  ::tensorflow::eager::CleanupFunctionOp* _internal_mutable_cleanup_function();
  public:
  void unsafe_arena_set_allocated_cleanup_function(
      ::tensorflow::eager::CleanupFunctionOp* cleanup_function);
  ::tensorflow::eager::CleanupFunctionOp* unsafe_arena_release_cleanup_function();

  // .tensorflow.eager.SyncRemoteExecutorForStream sync_remote_executor_for_stream = 6;
  bool has_sync_remote_executor_for_stream() const;
  private:
  bool _internal_has_sync_remote_executor_for_stream() const;
  public:
  void clear_sync_remote_executor_for_stream();
  const ::tensorflow::eager::SyncRemoteExecutorForStream& sync_remote_executor_for_stream() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::SyncRemoteExecutorForStream* release_sync_remote_executor_for_stream();
  ::tensorflow::eager::SyncRemoteExecutorForStream* mutable_sync_remote_executor_for_stream();
  void set_allocated_sync_remote_executor_for_stream(::tensorflow::eager::SyncRemoteExecutorForStream* sync_remote_executor_for_stream);
  private:
  const ::tensorflow::eager::SyncRemoteExecutorForStream& _internal_sync_remote_executor_for_stream() const;
  ::tensorflow::eager::SyncRemoteExecutorForStream* _internal_mutable_sync_remote_executor_for_stream();
  public:
  void unsafe_arena_set_allocated_sync_remote_executor_for_stream(
      ::tensorflow::eager::SyncRemoteExecutorForStream* sync_remote_executor_for_stream);
  ::tensorflow::eager::SyncRemoteExecutorForStream* unsafe_arena_release_sync_remote_executor_for_stream();

  // .tensorflow.eager.SendPackedHandleOp send_packed_handle = 7;
  bool has_send_packed_handle() const;
  private:
  bool _internal_has_send_packed_handle() const;
  public:
  void clear_send_packed_handle();
  const ::tensorflow::eager::SendPackedHandleOp& send_packed_handle() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::SendPackedHandleOp* release_send_packed_handle();
  ::tensorflow::eager::SendPackedHandleOp* mutable_send_packed_handle();
  void set_allocated_send_packed_handle(::tensorflow::eager::SendPackedHandleOp* send_packed_handle);
  private:
  const ::tensorflow::eager::SendPackedHandleOp& _internal_send_packed_handle() const;
  ::tensorflow::eager::SendPackedHandleOp* _internal_mutable_send_packed_handle();
  public:
  void unsafe_arena_set_allocated_send_packed_handle(
      ::tensorflow::eager::SendPackedHandleOp* send_packed_handle);
  ::tensorflow::eager::SendPackedHandleOp* unsafe_arena_release_send_packed_handle();

  // .tensorflow.eager.RemoveFunctionOp remove_function = 8;
  bool has_remove_function() const;
  private:
  bool _internal_has_remove_function() const;
  public:
  void clear_remove_function();
  const ::tensorflow::eager::RemoveFunctionOp& remove_function() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::RemoveFunctionOp* release_remove_function();
  ::tensorflow::eager::RemoveFunctionOp* mutable_remove_function();
  void set_allocated_remove_function(::tensorflow::eager::RemoveFunctionOp* remove_function);
  private:
  const ::tensorflow::eager::RemoveFunctionOp& _internal_remove_function() const;
  ::tensorflow::eager::RemoveFunctionOp* _internal_mutable_remove_function();
  public:
  void unsafe_arena_set_allocated_remove_function(
      ::tensorflow::eager::RemoveFunctionOp* remove_function);
  ::tensorflow::eager::RemoveFunctionOp* unsafe_arena_release_remove_function();

  void clear_item();
  ItemCase item_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.eager.QueueItem)
 private:
  class _Internal;
  void set_has_handle_to_decref();
  void set_has_operation();
  void set_has_send_tensor();
  void set_has_register_function();
  void set_has_cleanup_function();
  void set_has_sync_remote_executor_for_stream();
  void set_has_send_packed_handle();
  void set_has_remove_function();

  inline bool has_item() const;
  inline void clear_has_item();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union ItemUnion {
      constexpr ItemUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::eager::RemoteTensorHandle* handle_to_decref_;
      ::tensorflow::eager::Operation* operation_;
      ::tensorflow::eager::SendTensorOp* send_tensor_;
      ::tensorflow::eager::RegisterFunctionOp* register_function_;
      ::tensorflow::eager::CleanupFunctionOp* cleanup_function_;
      ::tensorflow::eager::SyncRemoteExecutorForStream* sync_remote_executor_for_stream_;
      ::tensorflow::eager::SendPackedHandleOp* send_packed_handle_;
      ::tensorflow::eager::RemoveFunctionOp* remove_function_;
    } item_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class QueueResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.QueueResponse) */ {
 public:
  inline QueueResponse() : QueueResponse(nullptr) {}
  ~QueueResponse() override;
  explicit PROTOBUF_CONSTEXPR QueueResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  QueueResponse(const QueueResponse& from);
  QueueResponse(QueueResponse&& from) noexcept
    : QueueResponse() {
    *this = ::std::move(from);
  }

  inline QueueResponse& operator=(const QueueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline QueueResponse& operator=(QueueResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const QueueResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const QueueResponse* internal_default_instance() {
    return reinterpret_cast<const QueueResponse*>(
               &_QueueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(QueueResponse& a, QueueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(QueueResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(QueueResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  QueueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<QueueResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const QueueResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const QueueResponse& from) {
    QueueResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(QueueResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.QueueResponse";
  }
  protected:
  explicit QueueResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 1,
    kTensorFieldNumber = 2,
    kDeviceFieldNumber = 3,
  };
  // repeated .tensorflow.TensorShapeProto shape = 1;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  ::tensorflow::TensorShapeProto* mutable_shape(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shape();
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape(int index) const;
  ::tensorflow::TensorShapeProto* _internal_add_shape();
  public:
  const ::tensorflow::TensorShapeProto& shape(int index) const;
  ::tensorflow::TensorShapeProto* add_shape();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shape() const;

  // repeated .tensorflow.TensorProto tensor = 2;
  int tensor_size() const;
  private:
  int _internal_tensor_size() const;
  public:
  void clear_tensor();
  ::tensorflow::TensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor();
  private:
  const ::tensorflow::TensorProto& _internal_tensor(int index) const;
  ::tensorflow::TensorProto* _internal_add_tensor();
  public:
  const ::tensorflow::TensorProto& tensor(int index) const;
  ::tensorflow::TensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor() const;

  // repeated string device = 3;
  int device_size() const;
  private:
  int _internal_device_size() const;
  public:
  void clear_device();
  const std::string& device(int index) const;
  std::string* mutable_device(int index);
  void set_device(int index, const std::string& value);
  void set_device(int index, std::string&& value);
  void set_device(int index, const char* value);
  void set_device(int index, const char* value, size_t size);
  std::string* add_device();
  void add_device(const std::string& value);
  void add_device(std::string&& value);
  void add_device(const char* value);
  void add_device(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& device() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_device();
  private:
  const std::string& _internal_device(int index) const;
  std::string* _internal_add_device();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.QueueResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto > shape_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> device_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CreateContextRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CreateContextRequest) */ {
 public:
  inline CreateContextRequest() : CreateContextRequest(nullptr) {}
  ~CreateContextRequest() override;
  explicit PROTOBUF_CONSTEXPR CreateContextRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateContextRequest(const CreateContextRequest& from);
  CreateContextRequest(CreateContextRequest&& from) noexcept
    : CreateContextRequest() {
    *this = ::std::move(from);
  }

  inline CreateContextRequest& operator=(const CreateContextRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateContextRequest& operator=(CreateContextRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateContextRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateContextRequest* internal_default_instance() {
    return reinterpret_cast<const CreateContextRequest*>(
               &_CreateContextRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(CreateContextRequest& a, CreateContextRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateContextRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateContextRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateContextRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateContextRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CreateContextRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CreateContextRequest& from) {
    CreateContextRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateContextRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CreateContextRequest";
  }
  protected:
  explicit CreateContextRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClusterDeviceAttributesFieldNumber = 6,
    kServerDefFieldNumber = 1,
    kVersionDefFieldNumber = 4,
    kKeepAliveSecsFieldNumber = 3,
    kContextIdFieldNumber = 7,
    kContextViewIdFieldNumber = 8,
    kAsyncFieldNumber = 2,
    kLazyCopyRemoteFunctionInputsFieldNumber = 9,
    kClearExistingContextsFieldNumber = 10,
  };
  // repeated .tensorflow.DeviceAttributes cluster_device_attributes = 6;
  int cluster_device_attributes_size() const;
  private:
  int _internal_cluster_device_attributes_size() const;
  public:
  void clear_cluster_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_cluster_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_cluster_device_attributes();
  private:
  const ::tensorflow::DeviceAttributes& _internal_cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_cluster_device_attributes();
  public:
  const ::tensorflow::DeviceAttributes& cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_cluster_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      cluster_device_attributes() const;

  // .tensorflow.ServerDef server_def = 1;
  bool has_server_def() const;
  private:
  bool _internal_has_server_def() const;
  public:
  void clear_server_def();
  const ::tensorflow::ServerDef& server_def() const;
  PROTOBUF_NODISCARD ::tensorflow::ServerDef* release_server_def();
  ::tensorflow::ServerDef* mutable_server_def();
  void set_allocated_server_def(::tensorflow::ServerDef* server_def);
  private:
  const ::tensorflow::ServerDef& _internal_server_def() const;
  ::tensorflow::ServerDef* _internal_mutable_server_def();
  public:
  void unsafe_arena_set_allocated_server_def(
      ::tensorflow::ServerDef* server_def);
  ::tensorflow::ServerDef* unsafe_arena_release_server_def();

  // .tensorflow.VersionDef version_def = 4;
  bool has_version_def() const;
  private:
  bool _internal_has_version_def() const;
  public:
  void clear_version_def();
  const ::tensorflow::VersionDef& version_def() const;
  PROTOBUF_NODISCARD ::tensorflow::VersionDef* release_version_def();
  ::tensorflow::VersionDef* mutable_version_def();
  void set_allocated_version_def(::tensorflow::VersionDef* version_def);
  private:
  const ::tensorflow::VersionDef& _internal_version_def() const;
  ::tensorflow::VersionDef* _internal_mutable_version_def();
  public:
  void unsafe_arena_set_allocated_version_def(
      ::tensorflow::VersionDef* version_def);
  ::tensorflow::VersionDef* unsafe_arena_release_version_def();

  // int64 keep_alive_secs = 3;
  void clear_keep_alive_secs();
  int64_t keep_alive_secs() const;
  void set_keep_alive_secs(int64_t value);
  private:
  int64_t _internal_keep_alive_secs() const;
  void _internal_set_keep_alive_secs(int64_t value);
  public:

  // fixed64 context_id = 7;
  void clear_context_id();
  uint64_t context_id() const;
  void set_context_id(uint64_t value);
  private:
  uint64_t _internal_context_id() const;
  void _internal_set_context_id(uint64_t value);
  public:

  // fixed64 context_view_id = 8;
  void clear_context_view_id();
  uint64_t context_view_id() const;
  void set_context_view_id(uint64_t value);
  private:
  uint64_t _internal_context_view_id() const;
  void _internal_set_context_view_id(uint64_t value);
  public:

  // bool async = 2;
  void clear_async();
  bool async() const;
  void set_async(bool value);
  private:
  bool _internal_async() const;
  void _internal_set_async(bool value);
  public:

  // bool lazy_copy_remote_function_inputs = 9;
  void clear_lazy_copy_remote_function_inputs();
  bool lazy_copy_remote_function_inputs() const;
  void set_lazy_copy_remote_function_inputs(bool value);
  private:
  bool _internal_lazy_copy_remote_function_inputs() const;
  void _internal_set_lazy_copy_remote_function_inputs(bool value);
  public:

  // bool clear_existing_contexts = 10;
  void clear_clear_existing_contexts();
  bool clear_existing_contexts() const;
  void set_clear_existing_contexts(bool value);
  private:
  bool _internal_clear_existing_contexts() const;
  void _internal_set_clear_existing_contexts(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CreateContextRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > cluster_device_attributes_;
    ::tensorflow::ServerDef* server_def_;
    ::tensorflow::VersionDef* version_def_;
    int64_t keep_alive_secs_;
    uint64_t context_id_;
    uint64_t context_view_id_;
    bool async_;
    bool lazy_copy_remote_function_inputs_;
    bool clear_existing_contexts_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CreateContextResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CreateContextResponse) */ {
 public:
  inline CreateContextResponse() : CreateContextResponse(nullptr) {}
  ~CreateContextResponse() override;
  explicit PROTOBUF_CONSTEXPR CreateContextResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateContextResponse(const CreateContextResponse& from);
  CreateContextResponse(CreateContextResponse&& from) noexcept
    : CreateContextResponse() {
    *this = ::std::move(from);
  }

  inline CreateContextResponse& operator=(const CreateContextResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateContextResponse& operator=(CreateContextResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateContextResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateContextResponse* internal_default_instance() {
    return reinterpret_cast<const CreateContextResponse*>(
               &_CreateContextResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(CreateContextResponse& a, CreateContextResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateContextResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateContextResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateContextResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateContextResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CreateContextResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CreateContextResponse& from) {
    CreateContextResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateContextResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CreateContextResponse";
  }
  protected:
  explicit CreateContextResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceAttributesFieldNumber = 2,
  };
  // repeated .tensorflow.DeviceAttributes device_attributes = 2;
  int device_attributes_size() const;
  private:
  int _internal_device_attributes_size() const;
  public:
  void clear_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  private:
  const ::tensorflow::DeviceAttributes& _internal_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_device_attributes();
  public:
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CreateContextResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class UpdateContextRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.UpdateContextRequest) */ {
 public:
  inline UpdateContextRequest() : UpdateContextRequest(nullptr) {}
  ~UpdateContextRequest() override;
  explicit PROTOBUF_CONSTEXPR UpdateContextRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpdateContextRequest(const UpdateContextRequest& from);
  UpdateContextRequest(UpdateContextRequest&& from) noexcept
    : UpdateContextRequest() {
    *this = ::std::move(from);
  }

  inline UpdateContextRequest& operator=(const UpdateContextRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpdateContextRequest& operator=(UpdateContextRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpdateContextRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpdateContextRequest* internal_default_instance() {
    return reinterpret_cast<const UpdateContextRequest*>(
               &_UpdateContextRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(UpdateContextRequest& a, UpdateContextRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UpdateContextRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpdateContextRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpdateContextRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpdateContextRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UpdateContextRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const UpdateContextRequest& from) {
    UpdateContextRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpdateContextRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.UpdateContextRequest";
  }
  protected:
  explicit UpdateContextRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClusterDeviceAttributesFieldNumber = 2,
    kServerDefFieldNumber = 1,
    kContextIdFieldNumber = 3,
    kContextViewIdFieldNumber = 4,
  };
  // repeated .tensorflow.DeviceAttributes cluster_device_attributes = 2;
  int cluster_device_attributes_size() const;
  private:
  int _internal_cluster_device_attributes_size() const;
  public:
  void clear_cluster_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_cluster_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_cluster_device_attributes();
  private:
  const ::tensorflow::DeviceAttributes& _internal_cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_cluster_device_attributes();
  public:
  const ::tensorflow::DeviceAttributes& cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_cluster_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      cluster_device_attributes() const;

  // .tensorflow.ServerDef server_def = 1;
  bool has_server_def() const;
  private:
  bool _internal_has_server_def() const;
  public:
  void clear_server_def();
  const ::tensorflow::ServerDef& server_def() const;
  PROTOBUF_NODISCARD ::tensorflow::ServerDef* release_server_def();
  ::tensorflow::ServerDef* mutable_server_def();
  void set_allocated_server_def(::tensorflow::ServerDef* server_def);
  private:
  const ::tensorflow::ServerDef& _internal_server_def() const;
  ::tensorflow::ServerDef* _internal_mutable_server_def();
  public:
  void unsafe_arena_set_allocated_server_def(
      ::tensorflow::ServerDef* server_def);
  ::tensorflow::ServerDef* unsafe_arena_release_server_def();

  // fixed64 context_id = 3;
  void clear_context_id();
  uint64_t context_id() const;
  void set_context_id(uint64_t value);
  private:
  uint64_t _internal_context_id() const;
  void _internal_set_context_id(uint64_t value);
  public:

  // fixed64 context_view_id = 4;
  void clear_context_view_id();
  uint64_t context_view_id() const;
  void set_context_view_id(uint64_t value);
  private:
  uint64_t _internal_context_view_id() const;
  void _internal_set_context_view_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.UpdateContextRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > cluster_device_attributes_;
    ::tensorflow::ServerDef* server_def_;
    uint64_t context_id_;
    uint64_t context_view_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class UpdateContextResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.UpdateContextResponse) */ {
 public:
  inline UpdateContextResponse() : UpdateContextResponse(nullptr) {}
  ~UpdateContextResponse() override;
  explicit PROTOBUF_CONSTEXPR UpdateContextResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpdateContextResponse(const UpdateContextResponse& from);
  UpdateContextResponse(UpdateContextResponse&& from) noexcept
    : UpdateContextResponse() {
    *this = ::std::move(from);
  }

  inline UpdateContextResponse& operator=(const UpdateContextResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpdateContextResponse& operator=(UpdateContextResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpdateContextResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpdateContextResponse* internal_default_instance() {
    return reinterpret_cast<const UpdateContextResponse*>(
               &_UpdateContextResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(UpdateContextResponse& a, UpdateContextResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(UpdateContextResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpdateContextResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpdateContextResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpdateContextResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UpdateContextResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const UpdateContextResponse& from) {
    UpdateContextResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpdateContextResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.UpdateContextResponse";
  }
  protected:
  explicit UpdateContextResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceAttributesFieldNumber = 1,
  };
  // repeated .tensorflow.DeviceAttributes device_attributes = 1;
  int device_attributes_size() const;
  private:
  int _internal_device_attributes_size() const;
  public:
  void clear_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  private:
  const ::tensorflow::DeviceAttributes& _internal_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_device_attributes();
  public:
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.UpdateContextResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class EnqueueRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.EnqueueRequest) */ {
 public:
  inline EnqueueRequest() : EnqueueRequest(nullptr) {}
  ~EnqueueRequest() override;
  explicit PROTOBUF_CONSTEXPR EnqueueRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnqueueRequest(const EnqueueRequest& from);
  EnqueueRequest(EnqueueRequest&& from) noexcept
    : EnqueueRequest() {
    *this = ::std::move(from);
  }

  inline EnqueueRequest& operator=(const EnqueueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnqueueRequest& operator=(EnqueueRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnqueueRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnqueueRequest* internal_default_instance() {
    return reinterpret_cast<const EnqueueRequest*>(
               &_EnqueueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(EnqueueRequest& a, EnqueueRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(EnqueueRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnqueueRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EnqueueRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EnqueueRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnqueueRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const EnqueueRequest& from) {
    EnqueueRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnqueueRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.EnqueueRequest";
  }
  protected:
  explicit EnqueueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQueueFieldNumber = 3,
    kContextIdFieldNumber = 1,
  };
  // repeated .tensorflow.eager.QueueItem queue = 3;
  int queue_size() const;
  private:
  int _internal_queue_size() const;
  public:
  void clear_queue();
  ::tensorflow::eager::QueueItem* mutable_queue(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem >*
      mutable_queue();
  private:
  const ::tensorflow::eager::QueueItem& _internal_queue(int index) const;
  ::tensorflow::eager::QueueItem* _internal_add_queue();
  public:
  const ::tensorflow::eager::QueueItem& queue(int index) const;
  ::tensorflow::eager::QueueItem* add_queue();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem >&
      queue() const;

  // fixed64 context_id = 1;
  void clear_context_id();
  uint64_t context_id() const;
  void set_context_id(uint64_t value);
  private:
  uint64_t _internal_context_id() const;
  void _internal_set_context_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.EnqueueRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem > queue_;
    uint64_t context_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class EnqueueResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.EnqueueResponse) */ {
 public:
  inline EnqueueResponse() : EnqueueResponse(nullptr) {}
  ~EnqueueResponse() override;
  explicit PROTOBUF_CONSTEXPR EnqueueResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EnqueueResponse(const EnqueueResponse& from);
  EnqueueResponse(EnqueueResponse&& from) noexcept
    : EnqueueResponse() {
    *this = ::std::move(from);
  }

  inline EnqueueResponse& operator=(const EnqueueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline EnqueueResponse& operator=(EnqueueResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EnqueueResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const EnqueueResponse* internal_default_instance() {
    return reinterpret_cast<const EnqueueResponse*>(
               &_EnqueueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(EnqueueResponse& a, EnqueueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(EnqueueResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EnqueueResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EnqueueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EnqueueResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EnqueueResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const EnqueueResponse& from) {
    EnqueueResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EnqueueResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.EnqueueResponse";
  }
  protected:
  explicit EnqueueResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQueueResponseFieldNumber = 1,
  };
  // repeated .tensorflow.eager.QueueResponse queue_response = 1;
  int queue_response_size() const;
  private:
  int _internal_queue_response_size() const;
  public:
  void clear_queue_response();
  ::tensorflow::eager::QueueResponse* mutable_queue_response(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse >*
      mutable_queue_response();
  private:
  const ::tensorflow::eager::QueueResponse& _internal_queue_response(int index) const;
  ::tensorflow::eager::QueueResponse* _internal_add_queue_response();
  public:
  const ::tensorflow::eager::QueueResponse& queue_response(int index) const;
  ::tensorflow::eager::QueueResponse* add_queue_response();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse >&
      queue_response() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.EnqueueResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse > queue_response_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class WaitQueueDoneRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.WaitQueueDoneRequest) */ {
 public:
  inline WaitQueueDoneRequest() : WaitQueueDoneRequest(nullptr) {}
  ~WaitQueueDoneRequest() override;
  explicit PROTOBUF_CONSTEXPR WaitQueueDoneRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WaitQueueDoneRequest(const WaitQueueDoneRequest& from);
  WaitQueueDoneRequest(WaitQueueDoneRequest&& from) noexcept
    : WaitQueueDoneRequest() {
    *this = ::std::move(from);
  }

  inline WaitQueueDoneRequest& operator=(const WaitQueueDoneRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline WaitQueueDoneRequest& operator=(WaitQueueDoneRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WaitQueueDoneRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const WaitQueueDoneRequest* internal_default_instance() {
    return reinterpret_cast<const WaitQueueDoneRequest*>(
               &_WaitQueueDoneRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(WaitQueueDoneRequest& a, WaitQueueDoneRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(WaitQueueDoneRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WaitQueueDoneRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WaitQueueDoneRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WaitQueueDoneRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WaitQueueDoneRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WaitQueueDoneRequest& from) {
    WaitQueueDoneRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WaitQueueDoneRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.WaitQueueDoneRequest";
  }
  protected:
  explicit WaitQueueDoneRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpIdFieldNumber = 2,
    kContextIdFieldNumber = 1,
  };
  // repeated int64 op_id = 2;
  int op_id_size() const;
  private:
  int _internal_op_id_size() const;
  public:
  void clear_op_id();
  private:
  int64_t _internal_op_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_op_id() const;
  void _internal_add_op_id(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_op_id();
  public:
  int64_t op_id(int index) const;
  void set_op_id(int index, int64_t value);
  void add_op_id(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      op_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_op_id();

  // fixed64 context_id = 1;
  void clear_context_id();
  uint64_t context_id() const;
  void set_context_id(uint64_t value);
  private:
  uint64_t _internal_context_id() const;
  void _internal_set_context_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.WaitQueueDoneRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > op_id_;
    mutable std::atomic<int> _op_id_cached_byte_size_;
    uint64_t context_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class WaitQueueDoneResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.eager.WaitQueueDoneResponse) */ {
 public:
  inline WaitQueueDoneResponse() : WaitQueueDoneResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR WaitQueueDoneResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WaitQueueDoneResponse(const WaitQueueDoneResponse& from);
  WaitQueueDoneResponse(WaitQueueDoneResponse&& from) noexcept
    : WaitQueueDoneResponse() {
    *this = ::std::move(from);
  }

  inline WaitQueueDoneResponse& operator=(const WaitQueueDoneResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline WaitQueueDoneResponse& operator=(WaitQueueDoneResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WaitQueueDoneResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const WaitQueueDoneResponse* internal_default_instance() {
    return reinterpret_cast<const WaitQueueDoneResponse*>(
               &_WaitQueueDoneResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(WaitQueueDoneResponse& a, WaitQueueDoneResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(WaitQueueDoneResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WaitQueueDoneResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WaitQueueDoneResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WaitQueueDoneResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const WaitQueueDoneResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const WaitQueueDoneResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.WaitQueueDoneResponse";
  }
  protected:
  explicit WaitQueueDoneResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.WaitQueueDoneResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RunComponentFunctionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RunComponentFunctionRequest) */ {
 public:
  inline RunComponentFunctionRequest() : RunComponentFunctionRequest(nullptr) {}
  ~RunComponentFunctionRequest() override;
  explicit PROTOBUF_CONSTEXPR RunComponentFunctionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunComponentFunctionRequest(const RunComponentFunctionRequest& from);
  RunComponentFunctionRequest(RunComponentFunctionRequest&& from) noexcept
    : RunComponentFunctionRequest() {
    *this = ::std::move(from);
  }

  inline RunComponentFunctionRequest& operator=(const RunComponentFunctionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunComponentFunctionRequest& operator=(RunComponentFunctionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunComponentFunctionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunComponentFunctionRequest* internal_default_instance() {
    return reinterpret_cast<const RunComponentFunctionRequest*>(
               &_RunComponentFunctionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RunComponentFunctionRequest& a, RunComponentFunctionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RunComponentFunctionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunComponentFunctionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunComponentFunctionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunComponentFunctionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunComponentFunctionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunComponentFunctionRequest& from) {
    RunComponentFunctionRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunComponentFunctionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.RunComponentFunctionRequest";
  }
  protected:
  explicit RunComponentFunctionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOutputNumFieldNumber = 3,
    kOperationFieldNumber = 2,
    kContextIdFieldNumber = 1,
  };
  // repeated int32 output_num = 3;
  int output_num_size() const;
  private:
  int _internal_output_num_size() const;
  public:
  void clear_output_num();
  private:
  int32_t _internal_output_num(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_output_num() const;
  void _internal_add_output_num(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_output_num();
  public:
  int32_t output_num(int index) const;
  void set_output_num(int index, int32_t value);
  void add_output_num(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      output_num() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_output_num();

  // .tensorflow.eager.Operation operation = 2;
  bool has_operation() const;
  private:
  bool _internal_has_operation() const;
  public:
  void clear_operation();
  const ::tensorflow::eager::Operation& operation() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::Operation* release_operation();
  ::tensorflow::eager::Operation* mutable_operation();
  void set_allocated_operation(::tensorflow::eager::Operation* operation);
  private:
  const ::tensorflow::eager::Operation& _internal_operation() const;
  ::tensorflow::eager::Operation* _internal_mutable_operation();
  public:
  void unsafe_arena_set_allocated_operation(
      ::tensorflow::eager::Operation* operation);
  ::tensorflow::eager::Operation* unsafe_arena_release_operation();

  // fixed64 context_id = 1;
  void clear_context_id();
  uint64_t context_id() const;
  void set_context_id(uint64_t value);
  private:
  uint64_t _internal_context_id() const;
  void _internal_set_context_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RunComponentFunctionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > output_num_;
    mutable std::atomic<int> _output_num_cached_byte_size_;
    ::tensorflow::eager::Operation* operation_;
    uint64_t context_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RunComponentFunctionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RunComponentFunctionResponse) */ {
 public:
  inline RunComponentFunctionResponse() : RunComponentFunctionResponse(nullptr) {}
  ~RunComponentFunctionResponse() override;
  explicit PROTOBUF_CONSTEXPR RunComponentFunctionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunComponentFunctionResponse(const RunComponentFunctionResponse& from);
  RunComponentFunctionResponse(RunComponentFunctionResponse&& from) noexcept
    : RunComponentFunctionResponse() {
    *this = ::std::move(from);
  }

  inline RunComponentFunctionResponse& operator=(const RunComponentFunctionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunComponentFunctionResponse& operator=(RunComponentFunctionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunComponentFunctionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunComponentFunctionResponse* internal_default_instance() {
    return reinterpret_cast<const RunComponentFunctionResponse*>(
               &_RunComponentFunctionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RunComponentFunctionResponse& a, RunComponentFunctionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RunComponentFunctionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunComponentFunctionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunComponentFunctionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunComponentFunctionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunComponentFunctionResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunComponentFunctionResponse& from) {
    RunComponentFunctionResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunComponentFunctionResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.RunComponentFunctionResponse";
  }
  protected:
  explicit RunComponentFunctionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 1,
    kTensorFieldNumber = 2,
  };
  // repeated .tensorflow.TensorShapeProto shape = 1;
  int shape_size() const;
  private:
  int _internal_shape_size() const;
  public:
  void clear_shape();
  ::tensorflow::TensorShapeProto* mutable_shape(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shape();
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape(int index) const;
  ::tensorflow::TensorShapeProto* _internal_add_shape();
  public:
  const ::tensorflow::TensorShapeProto& shape(int index) const;
  ::tensorflow::TensorShapeProto* add_shape();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shape() const;

  // repeated .tensorflow.TensorProto tensor = 2;
  int tensor_size() const;
  private:
  int _internal_tensor_size() const;
  public:
  void clear_tensor();
  ::tensorflow::TensorProto* mutable_tensor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensor();
  private:
  const ::tensorflow::TensorProto& _internal_tensor(int index) const;
  ::tensorflow::TensorProto* _internal_add_tensor();
  public:
  const ::tensorflow::TensorProto& tensor(int index) const;
  ::tensorflow::TensorProto* add_tensor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensor() const;

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RunComponentFunctionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto > shape_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensor_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class KeepAliveRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.KeepAliveRequest) */ {
 public:
  inline KeepAliveRequest() : KeepAliveRequest(nullptr) {}
  ~KeepAliveRequest() override;
  explicit PROTOBUF_CONSTEXPR KeepAliveRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  KeepAliveRequest(const KeepAliveRequest& from);
  KeepAliveRequest(KeepAliveRequest&& from) noexcept
    : KeepAliveRequest() {
    *this = ::std::move(from);
  }

  inline KeepAliveRequest& operator=(const KeepAliveRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline KeepAliveRequest& operator=(KeepAliveRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KeepAliveRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const KeepAliveRequest* internal_default_instance() {
    return reinterpret_cast<const KeepAliveRequest*>(
               &_KeepAliveRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(KeepAliveRequest& a, KeepAliveRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(KeepAliveRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KeepAliveRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KeepAliveRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KeepAliveRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const KeepAliveRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const KeepAliveRequest& from) {
    KeepAliveRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeepAliveRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.KeepAliveRequest";
  }
  protected:
  explicit KeepAliveRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextIdFieldNumber = 1,
  };
  // fixed64 context_id = 1;
  void clear_context_id();
  uint64_t context_id() const;
  void set_context_id(uint64_t value);
  private:
  uint64_t _internal_context_id() const;
  void _internal_set_context_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.KeepAliveRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t context_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class KeepAliveResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.KeepAliveResponse) */ {
 public:
  inline KeepAliveResponse() : KeepAliveResponse(nullptr) {}
  ~KeepAliveResponse() override;
  explicit PROTOBUF_CONSTEXPR KeepAliveResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  KeepAliveResponse(const KeepAliveResponse& from);
  KeepAliveResponse(KeepAliveResponse&& from) noexcept
    : KeepAliveResponse() {
    *this = ::std::move(from);
  }

  inline KeepAliveResponse& operator=(const KeepAliveResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline KeepAliveResponse& operator=(KeepAliveResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KeepAliveResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const KeepAliveResponse* internal_default_instance() {
    return reinterpret_cast<const KeepAliveResponse*>(
               &_KeepAliveResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(KeepAliveResponse& a, KeepAliveResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(KeepAliveResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KeepAliveResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KeepAliveResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KeepAliveResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const KeepAliveResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const KeepAliveResponse& from) {
    KeepAliveResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KeepAliveResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.KeepAliveResponse";
  }
  protected:
  explicit KeepAliveResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextViewIdFieldNumber = 1,
  };
  // fixed64 context_view_id = 1;
  void clear_context_view_id();
  uint64_t context_view_id() const;
  void set_context_view_id(uint64_t value);
  private:
  uint64_t _internal_context_view_id() const;
  void _internal_set_context_view_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.KeepAliveResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t context_view_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CloseContextRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CloseContextRequest) */ {
 public:
  inline CloseContextRequest() : CloseContextRequest(nullptr) {}
  ~CloseContextRequest() override;
  explicit PROTOBUF_CONSTEXPR CloseContextRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CloseContextRequest(const CloseContextRequest& from);
  CloseContextRequest(CloseContextRequest&& from) noexcept
    : CloseContextRequest() {
    *this = ::std::move(from);
  }

  inline CloseContextRequest& operator=(const CloseContextRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CloseContextRequest& operator=(CloseContextRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CloseContextRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CloseContextRequest* internal_default_instance() {
    return reinterpret_cast<const CloseContextRequest*>(
               &_CloseContextRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(CloseContextRequest& a, CloseContextRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CloseContextRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CloseContextRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CloseContextRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CloseContextRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CloseContextRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CloseContextRequest& from) {
    CloseContextRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CloseContextRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CloseContextRequest";
  }
  protected:
  explicit CloseContextRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextIdFieldNumber = 1,
    kContextViewIdFieldNumber = 2,
  };
  // fixed64 context_id = 1;
  void clear_context_id();
  uint64_t context_id() const;
  void set_context_id(uint64_t value);
  private:
  uint64_t _internal_context_id() const;
  void _internal_set_context_id(uint64_t value);
  public:

  // fixed64 context_view_id = 2;
  void clear_context_view_id();
  uint64_t context_view_id() const;
  void set_context_view_id(uint64_t value);
  private:
  uint64_t _internal_context_view_id() const;
  void _internal_set_context_view_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CloseContextRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t context_id_;
    uint64_t context_view_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CloseContextResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.eager.CloseContextResponse) */ {
 public:
  inline CloseContextResponse() : CloseContextResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR CloseContextResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CloseContextResponse(const CloseContextResponse& from);
  CloseContextResponse(CloseContextResponse&& from) noexcept
    : CloseContextResponse() {
    *this = ::std::move(from);
  }

  inline CloseContextResponse& operator=(const CloseContextResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CloseContextResponse& operator=(CloseContextResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CloseContextResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CloseContextResponse* internal_default_instance() {
    return reinterpret_cast<const CloseContextResponse*>(
               &_CloseContextResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(CloseContextResponse& a, CloseContextResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CloseContextResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CloseContextResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CloseContextResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CloseContextResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const CloseContextResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const CloseContextResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CloseContextResponse";
  }
  protected:
  explicit CloseContextResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CloseContextResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RegisterFunctionOp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RegisterFunctionOp) */ {
 public:
  inline RegisterFunctionOp() : RegisterFunctionOp(nullptr) {}
  ~RegisterFunctionOp() override;
  explicit PROTOBUF_CONSTEXPR RegisterFunctionOp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisterFunctionOp(const RegisterFunctionOp& from);
  RegisterFunctionOp(RegisterFunctionOp&& from) noexcept
    : RegisterFunctionOp() {
    *this = ::std::move(from);
  }

  inline RegisterFunctionOp& operator=(const RegisterFunctionOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterFunctionOp& operator=(RegisterFunctionOp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisterFunctionOp& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisterFunctionOp* internal_default_instance() {
    return reinterpret_cast<const RegisterFunctionOp*>(
               &_RegisterFunctionOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(RegisterFunctionOp& a, RegisterFunctionOp& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterFunctionOp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterFunctionOp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisterFunctionOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisterFunctionOp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegisterFunctionOp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RegisterFunctionOp& from) {
    RegisterFunctionOp::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterFunctionOp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.RegisterFunctionOp";
  }
  protected:
  explicit RegisterFunctionOp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionDefFieldNumber = 1,
    kLibraryFieldNumber = 3,
    kIsComponentFunctionFieldNumber = 2,
  };
  // .tensorflow.FunctionDef function_def = 1;
  bool has_function_def() const;
  private:
  bool _internal_has_function_def() const;
  public:
  void clear_function_def();
  const ::tensorflow::FunctionDef& function_def() const;
  PROTOBUF_NODISCARD ::tensorflow::FunctionDef* release_function_def();
  ::tensorflow::FunctionDef* mutable_function_def();
  void set_allocated_function_def(::tensorflow::FunctionDef* function_def);
  private:
  const ::tensorflow::FunctionDef& _internal_function_def() const;
  ::tensorflow::FunctionDef* _internal_mutable_function_def();
  public:
  void unsafe_arena_set_allocated_function_def(
      ::tensorflow::FunctionDef* function_def);
  ::tensorflow::FunctionDef* unsafe_arena_release_function_def();

  // .tensorflow.FunctionDefLibrary library = 3;
  bool has_library() const;
  private:
  bool _internal_has_library() const;
  public:
  void clear_library();
  const ::tensorflow::FunctionDefLibrary& library() const;
  PROTOBUF_NODISCARD ::tensorflow::FunctionDefLibrary* release_library();
  ::tensorflow::FunctionDefLibrary* mutable_library();
  void set_allocated_library(::tensorflow::FunctionDefLibrary* library);
  private:
  const ::tensorflow::FunctionDefLibrary& _internal_library() const;
  ::tensorflow::FunctionDefLibrary* _internal_mutable_library();
  public:
  void unsafe_arena_set_allocated_library(
      ::tensorflow::FunctionDefLibrary* library);
  ::tensorflow::FunctionDefLibrary* unsafe_arena_release_library();

  // bool is_component_function = 2;
  void clear_is_component_function();
  bool is_component_function() const;
  void set_is_component_function(bool value);
  private:
  bool _internal_is_component_function() const;
  void _internal_set_is_component_function(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RegisterFunctionOp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::FunctionDef* function_def_;
    ::tensorflow::FunctionDefLibrary* library_;
    bool is_component_function_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RemoveFunctionOp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RemoveFunctionOp) */ {
 public:
  inline RemoveFunctionOp() : RemoveFunctionOp(nullptr) {}
  ~RemoveFunctionOp() override;
  explicit PROTOBUF_CONSTEXPR RemoveFunctionOp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RemoveFunctionOp(const RemoveFunctionOp& from);
  RemoveFunctionOp(RemoveFunctionOp&& from) noexcept
    : RemoveFunctionOp() {
    *this = ::std::move(from);
  }

  inline RemoveFunctionOp& operator=(const RemoveFunctionOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline RemoveFunctionOp& operator=(RemoveFunctionOp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RemoveFunctionOp& default_instance() {
    return *internal_default_instance();
  }
  static inline const RemoveFunctionOp* internal_default_instance() {
    return reinterpret_cast<const RemoveFunctionOp*>(
               &_RemoveFunctionOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(RemoveFunctionOp& a, RemoveFunctionOp& b) {
    a.Swap(&b);
  }
  inline void Swap(RemoveFunctionOp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RemoveFunctionOp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RemoveFunctionOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RemoveFunctionOp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RemoveFunctionOp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RemoveFunctionOp& from) {
    RemoveFunctionOp::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RemoveFunctionOp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.RemoveFunctionOp";
  }
  protected:
  explicit RemoveFunctionOp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFunctionNameFieldNumber = 1,
  };
  // string function_name = 1;
  void clear_function_name();
  const std::string& function_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_function_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_function_name();
  PROTOBUF_NODISCARD std::string* release_function_name();
  void set_allocated_function_name(std::string* function_name);
  private:
  const std::string& _internal_function_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_function_name(const std::string& value);
  std::string* _internal_mutable_function_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RemoveFunctionOp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr function_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CleanupFunctionOp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.CleanupFunctionOp) */ {
 public:
  inline CleanupFunctionOp() : CleanupFunctionOp(nullptr) {}
  ~CleanupFunctionOp() override;
  explicit PROTOBUF_CONSTEXPR CleanupFunctionOp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CleanupFunctionOp(const CleanupFunctionOp& from);
  CleanupFunctionOp(CleanupFunctionOp&& from) noexcept
    : CleanupFunctionOp() {
    *this = ::std::move(from);
  }

  inline CleanupFunctionOp& operator=(const CleanupFunctionOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupFunctionOp& operator=(CleanupFunctionOp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CleanupFunctionOp& default_instance() {
    return *internal_default_instance();
  }
  static inline const CleanupFunctionOp* internal_default_instance() {
    return reinterpret_cast<const CleanupFunctionOp*>(
               &_CleanupFunctionOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(CleanupFunctionOp& a, CleanupFunctionOp& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupFunctionOp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupFunctionOp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CleanupFunctionOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CleanupFunctionOp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CleanupFunctionOp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CleanupFunctionOp& from) {
    CleanupFunctionOp::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupFunctionOp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.CleanupFunctionOp";
  }
  protected:
  explicit CleanupFunctionOp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepIdFieldNumber = 1,
  };
  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.CleanupFunctionOp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t step_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SyncRemoteExecutorForStream final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.eager.SyncRemoteExecutorForStream) */ {
 public:
  inline SyncRemoteExecutorForStream() : SyncRemoteExecutorForStream(nullptr) {}
  explicit PROTOBUF_CONSTEXPR SyncRemoteExecutorForStream(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SyncRemoteExecutorForStream(const SyncRemoteExecutorForStream& from);
  SyncRemoteExecutorForStream(SyncRemoteExecutorForStream&& from) noexcept
    : SyncRemoteExecutorForStream() {
    *this = ::std::move(from);
  }

  inline SyncRemoteExecutorForStream& operator=(const SyncRemoteExecutorForStream& from) {
    CopyFrom(from);
    return *this;
  }
  inline SyncRemoteExecutorForStream& operator=(SyncRemoteExecutorForStream&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SyncRemoteExecutorForStream& default_instance() {
    return *internal_default_instance();
  }
  static inline const SyncRemoteExecutorForStream* internal_default_instance() {
    return reinterpret_cast<const SyncRemoteExecutorForStream*>(
               &_SyncRemoteExecutorForStream_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(SyncRemoteExecutorForStream& a, SyncRemoteExecutorForStream& b) {
    a.Swap(&b);
  }
  inline void Swap(SyncRemoteExecutorForStream* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SyncRemoteExecutorForStream* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SyncRemoteExecutorForStream* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SyncRemoteExecutorForStream>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SyncRemoteExecutorForStream& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SyncRemoteExecutorForStream& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SyncRemoteExecutorForStream";
  }
  protected:
  explicit SyncRemoteExecutorForStream(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SyncRemoteExecutorForStream)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SendTensorOp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendTensorOp) */ {
 public:
  inline SendTensorOp() : SendTensorOp(nullptr) {}
  ~SendTensorOp() override;
  explicit PROTOBUF_CONSTEXPR SendTensorOp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SendTensorOp(const SendTensorOp& from);
  SendTensorOp(SendTensorOp&& from) noexcept
    : SendTensorOp() {
    *this = ::std::move(from);
  }

  inline SendTensorOp& operator=(const SendTensorOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline SendTensorOp& operator=(SendTensorOp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SendTensorOp& default_instance() {
    return *internal_default_instance();
  }
  static inline const SendTensorOp* internal_default_instance() {
    return reinterpret_cast<const SendTensorOp*>(
               &_SendTensorOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(SendTensorOp& a, SendTensorOp& b) {
    a.Swap(&b);
  }
  inline void Swap(SendTensorOp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SendTensorOp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SendTensorOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SendTensorOp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SendTensorOp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SendTensorOp& from) {
    SendTensorOp::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendTensorOp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SendTensorOp";
  }
  protected:
  explicit SendTensorOp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorsFieldNumber = 2,
    kDeviceNameFieldNumber = 3,
    kOpIdFieldNumber = 1,
  };
  // repeated .tensorflow.TensorProto tensors = 2;
  int tensors_size() const;
  private:
  int _internal_tensors_size() const;
  public:
  void clear_tensors();
  ::tensorflow::TensorProto* mutable_tensors(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
      mutable_tensors();
  private:
  const ::tensorflow::TensorProto& _internal_tensors(int index) const;
  ::tensorflow::TensorProto* _internal_add_tensors();
  public:
  const ::tensorflow::TensorProto& tensors(int index) const;
  ::tensorflow::TensorProto* add_tensors();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
      tensors() const;

  // string device_name = 3;
  void clear_device_name();
  const std::string& device_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_name();
  PROTOBUF_NODISCARD std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);
  private:
  const std::string& _internal_device_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_name(const std::string& value);
  std::string* _internal_mutable_device_name();
  public:

  // int64 op_id = 1;
  void clear_op_id();
  int64_t op_id() const;
  void set_op_id(int64_t value);
  private:
  int64_t _internal_op_id() const;
  void _internal_set_op_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendTensorOp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto > tensors_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
    int64_t op_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SendPackedHandleOp_LocalTensorHandle final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle) */ {
 public:
  inline SendPackedHandleOp_LocalTensorHandle() : SendPackedHandleOp_LocalTensorHandle(nullptr) {}
  ~SendPackedHandleOp_LocalTensorHandle() override;
  explicit PROTOBUF_CONSTEXPR SendPackedHandleOp_LocalTensorHandle(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SendPackedHandleOp_LocalTensorHandle(const SendPackedHandleOp_LocalTensorHandle& from);
  SendPackedHandleOp_LocalTensorHandle(SendPackedHandleOp_LocalTensorHandle&& from) noexcept
    : SendPackedHandleOp_LocalTensorHandle() {
    *this = ::std::move(from);
  }

  inline SendPackedHandleOp_LocalTensorHandle& operator=(const SendPackedHandleOp_LocalTensorHandle& from) {
    CopyFrom(from);
    return *this;
  }
  inline SendPackedHandleOp_LocalTensorHandle& operator=(SendPackedHandleOp_LocalTensorHandle&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SendPackedHandleOp_LocalTensorHandle& default_instance() {
    return *internal_default_instance();
  }
  static inline const SendPackedHandleOp_LocalTensorHandle* internal_default_instance() {
    return reinterpret_cast<const SendPackedHandleOp_LocalTensorHandle*>(
               &_SendPackedHandleOp_LocalTensorHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(SendPackedHandleOp_LocalTensorHandle& a, SendPackedHandleOp_LocalTensorHandle& b) {
    a.Swap(&b);
  }
  inline void Swap(SendPackedHandleOp_LocalTensorHandle* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SendPackedHandleOp_LocalTensorHandle* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SendPackedHandleOp_LocalTensorHandle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SendPackedHandleOp_LocalTensorHandle>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SendPackedHandleOp_LocalTensorHandle& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SendPackedHandleOp_LocalTensorHandle& from) {
    SendPackedHandleOp_LocalTensorHandle::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendPackedHandleOp_LocalTensorHandle* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SendPackedHandleOp.LocalTensorHandle";
  }
  protected:
  explicit SendPackedHandleOp_LocalTensorHandle(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFieldNumber = 2,
    kTensorFieldNumber = 1,
  };
  // string device = 2;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // .tensorflow.TensorProto tensor = 1;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  ::tensorflow::TensorProto* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    ::tensorflow::TensorProto* tensor_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SendPackedHandleOp_Handle final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendPackedHandleOp.Handle) */ {
 public:
  inline SendPackedHandleOp_Handle() : SendPackedHandleOp_Handle(nullptr) {}
  ~SendPackedHandleOp_Handle() override;
  explicit PROTOBUF_CONSTEXPR SendPackedHandleOp_Handle(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SendPackedHandleOp_Handle(const SendPackedHandleOp_Handle& from);
  SendPackedHandleOp_Handle(SendPackedHandleOp_Handle&& from) noexcept
    : SendPackedHandleOp_Handle() {
    *this = ::std::move(from);
  }

  inline SendPackedHandleOp_Handle& operator=(const SendPackedHandleOp_Handle& from) {
    CopyFrom(from);
    return *this;
  }
  inline SendPackedHandleOp_Handle& operator=(SendPackedHandleOp_Handle&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SendPackedHandleOp_Handle& default_instance() {
    return *internal_default_instance();
  }
  enum ItemCase {
    kLocalHandle = 1,
    kRemoteHandle = 2,
    ITEM_NOT_SET = 0,
  };

  static inline const SendPackedHandleOp_Handle* internal_default_instance() {
    return reinterpret_cast<const SendPackedHandleOp_Handle*>(
               &_SendPackedHandleOp_Handle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(SendPackedHandleOp_Handle& a, SendPackedHandleOp_Handle& b) {
    a.Swap(&b);
  }
  inline void Swap(SendPackedHandleOp_Handle* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SendPackedHandleOp_Handle* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SendPackedHandleOp_Handle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SendPackedHandleOp_Handle>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SendPackedHandleOp_Handle& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SendPackedHandleOp_Handle& from) {
    SendPackedHandleOp_Handle::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendPackedHandleOp_Handle* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SendPackedHandleOp.Handle";
  }
  protected:
  explicit SendPackedHandleOp_Handle(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocalHandleFieldNumber = 1,
    kRemoteHandleFieldNumber = 2,
  };
  // .tensorflow.eager.SendPackedHandleOp.LocalTensorHandle local_handle = 1;
  bool has_local_handle() const;
  private:
  bool _internal_has_local_handle() const;
  public:
  void clear_local_handle();
  const ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle& local_handle() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* release_local_handle();
  ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* mutable_local_handle();
  void set_allocated_local_handle(::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* local_handle);
  private:
  const ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle& _internal_local_handle() const;
  ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* _internal_mutable_local_handle();
  public:
  void unsafe_arena_set_allocated_local_handle(
      ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* local_handle);
  ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* unsafe_arena_release_local_handle();

  // .tensorflow.eager.RemoteTensorHandle remote_handle = 2;
  bool has_remote_handle() const;
  private:
  bool _internal_has_remote_handle() const;
  public:
  void clear_remote_handle();
  const ::tensorflow::eager::RemoteTensorHandle& remote_handle() const;
  PROTOBUF_NODISCARD ::tensorflow::eager::RemoteTensorHandle* release_remote_handle();
  ::tensorflow::eager::RemoteTensorHandle* mutable_remote_handle();
  void set_allocated_remote_handle(::tensorflow::eager::RemoteTensorHandle* remote_handle);
  private:
  const ::tensorflow::eager::RemoteTensorHandle& _internal_remote_handle() const;
  ::tensorflow::eager::RemoteTensorHandle* _internal_mutable_remote_handle();
  public:
  void unsafe_arena_set_allocated_remote_handle(
      ::tensorflow::eager::RemoteTensorHandle* remote_handle);
  ::tensorflow::eager::RemoteTensorHandle* unsafe_arena_release_remote_handle();

  void clear_item();
  ItemCase item_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendPackedHandleOp.Handle)
 private:
  class _Internal;
  void set_has_local_handle();
  void set_has_remote_handle();

  inline bool has_item() const;
  inline void clear_has_item();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union ItemUnion {
      constexpr ItemUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* local_handle_;
      ::tensorflow::eager::RemoteTensorHandle* remote_handle_;
    } item_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SendPackedHandleOp final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.SendPackedHandleOp) */ {
 public:
  inline SendPackedHandleOp() : SendPackedHandleOp(nullptr) {}
  ~SendPackedHandleOp() override;
  explicit PROTOBUF_CONSTEXPR SendPackedHandleOp(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SendPackedHandleOp(const SendPackedHandleOp& from);
  SendPackedHandleOp(SendPackedHandleOp&& from) noexcept
    : SendPackedHandleOp() {
    *this = ::std::move(from);
  }

  inline SendPackedHandleOp& operator=(const SendPackedHandleOp& from) {
    CopyFrom(from);
    return *this;
  }
  inline SendPackedHandleOp& operator=(SendPackedHandleOp&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SendPackedHandleOp& default_instance() {
    return *internal_default_instance();
  }
  static inline const SendPackedHandleOp* internal_default_instance() {
    return reinterpret_cast<const SendPackedHandleOp*>(
               &_SendPackedHandleOp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(SendPackedHandleOp& a, SendPackedHandleOp& b) {
    a.Swap(&b);
  }
  inline void Swap(SendPackedHandleOp* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SendPackedHandleOp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SendPackedHandleOp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SendPackedHandleOp>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SendPackedHandleOp& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SendPackedHandleOp& from) {
    SendPackedHandleOp::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SendPackedHandleOp* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.SendPackedHandleOp";
  }
  protected:
  explicit SendPackedHandleOp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SendPackedHandleOp_LocalTensorHandle LocalTensorHandle;
  typedef SendPackedHandleOp_Handle Handle;

  // accessors -------------------------------------------------------

  enum : int {
    kHandlesFieldNumber = 2,
    kDeviceNameFieldNumber = 3,
    kOpIdFieldNumber = 1,
  };
  // repeated .tensorflow.eager.SendPackedHandleOp.Handle handles = 2;
  int handles_size() const;
  private:
  int _internal_handles_size() const;
  public:
  void clear_handles();
  ::tensorflow::eager::SendPackedHandleOp_Handle* mutable_handles(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle >*
      mutable_handles();
  private:
  const ::tensorflow::eager::SendPackedHandleOp_Handle& _internal_handles(int index) const;
  ::tensorflow::eager::SendPackedHandleOp_Handle* _internal_add_handles();
  public:
  const ::tensorflow::eager::SendPackedHandleOp_Handle& handles(int index) const;
  ::tensorflow::eager::SendPackedHandleOp_Handle* add_handles();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle >&
      handles() const;

  // string device_name = 3;
  void clear_device_name();
  const std::string& device_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_name();
  PROTOBUF_NODISCARD std::string* release_device_name();
  void set_allocated_device_name(std::string* device_name);
  private:
  const std::string& _internal_device_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_name(const std::string& value);
  std::string* _internal_mutable_device_name();
  public:

  // int64 op_id = 1;
  void clear_op_id();
  int64_t op_id() const;
  void set_op_id(int64_t value);
  private:
  int64_t _internal_op_id() const;
  void _internal_set_op_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.SendPackedHandleOp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle > handles_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_name_;
    int64_t op_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Operation_Input

// .tensorflow.eager.RemoteTensorHandle remote_handle = 1;
inline bool Operation_Input::_internal_has_remote_handle() const {
  return item_case() == kRemoteHandle;
}
inline bool Operation_Input::has_remote_handle() const {
  return _internal_has_remote_handle();
}
inline void Operation_Input::set_has_remote_handle() {
  _impl_._oneof_case_[0] = kRemoteHandle;
}
inline ::tensorflow::eager::RemoteTensorHandle* Operation_Input::release_remote_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.Input.remote_handle)
  if (_internal_has_remote_handle()) {
    clear_has_item();
    ::tensorflow::eager::RemoteTensorHandle* temp = _impl_.item_.remote_handle_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.remote_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RemoteTensorHandle& Operation_Input::_internal_remote_handle() const {
  return _internal_has_remote_handle()
      ? *_impl_.item_.remote_handle_
      : reinterpret_cast< ::tensorflow::eager::RemoteTensorHandle&>(::tensorflow::eager::_RemoteTensorHandle_default_instance_);
}
inline const ::tensorflow::eager::RemoteTensorHandle& Operation_Input::remote_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.Input.remote_handle)
  return _internal_remote_handle();
}
inline ::tensorflow::eager::RemoteTensorHandle* Operation_Input::unsafe_arena_release_remote_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.Operation.Input.remote_handle)
  if (_internal_has_remote_handle()) {
    clear_has_item();
    ::tensorflow::eager::RemoteTensorHandle* temp = _impl_.item_.remote_handle_;
    _impl_.item_.remote_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Operation_Input::unsafe_arena_set_allocated_remote_handle(::tensorflow::eager::RemoteTensorHandle* remote_handle) {
  clear_item();
  if (remote_handle) {
    set_has_remote_handle();
    _impl_.item_.remote_handle_ = remote_handle;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.Operation.Input.remote_handle)
}
inline ::tensorflow::eager::RemoteTensorHandle* Operation_Input::_internal_mutable_remote_handle() {
  if (!_internal_has_remote_handle()) {
    clear_item();
    set_has_remote_handle();
    _impl_.item_.remote_handle_ = CreateMaybeMessage< ::tensorflow::eager::RemoteTensorHandle >(GetArenaForAllocation());
  }
  return _impl_.item_.remote_handle_;
}
inline ::tensorflow::eager::RemoteTensorHandle* Operation_Input::mutable_remote_handle() {
  ::tensorflow::eager::RemoteTensorHandle* _msg = _internal_mutable_remote_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.Input.remote_handle)
  return _msg;
}

// .tensorflow.TensorProto tensor = 2;
inline bool Operation_Input::_internal_has_tensor() const {
  return item_case() == kTensor;
}
inline bool Operation_Input::has_tensor() const {
  return _internal_has_tensor();
}
inline void Operation_Input::set_has_tensor() {
  _impl_._oneof_case_[0] = kTensor;
}
inline ::tensorflow::TensorProto* Operation_Input::release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.Input.tensor)
  if (_internal_has_tensor()) {
    clear_has_item();
    ::tensorflow::TensorProto* temp = _impl_.item_.tensor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorProto& Operation_Input::_internal_tensor() const {
  return _internal_has_tensor()
      ? *_impl_.item_.tensor_
      : reinterpret_cast< ::tensorflow::TensorProto&>(::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& Operation_Input::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.Input.tensor)
  return _internal_tensor();
}
inline ::tensorflow::TensorProto* Operation_Input::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.Operation.Input.tensor)
  if (_internal_has_tensor()) {
    clear_has_item();
    ::tensorflow::TensorProto* temp = _impl_.item_.tensor_;
    _impl_.item_.tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Operation_Input::unsafe_arena_set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  clear_item();
  if (tensor) {
    set_has_tensor();
    _impl_.item_.tensor_ = tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.Operation.Input.tensor)
}
inline ::tensorflow::TensorProto* Operation_Input::_internal_mutable_tensor() {
  if (!_internal_has_tensor()) {
    clear_item();
    set_has_tensor();
    _impl_.item_.tensor_ = CreateMaybeMessage< ::tensorflow::TensorProto >(GetArenaForAllocation());
  }
  return _impl_.item_.tensor_;
}
inline ::tensorflow::TensorProto* Operation_Input::mutable_tensor() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.Input.tensor)
  return _msg;
}

inline bool Operation_Input::has_item() const {
  return item_case() != ITEM_NOT_SET;
}
inline void Operation_Input::clear_has_item() {
  _impl_._oneof_case_[0] = ITEM_NOT_SET;
}
inline Operation_Input::ItemCase Operation_Input::item_case() const {
  return Operation_Input::ItemCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// Operation

// int64 id = 1;
inline void Operation::clear_id() {
  _impl_.id_ = int64_t{0};
}
inline int64_t Operation::_internal_id() const {
  return _impl_.id_;
}
inline int64_t Operation::id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.id)
  return _internal_id();
}
inline void Operation::_internal_set_id(int64_t value) {
  
  _impl_.id_ = value;
}
inline void Operation::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.id)
}

// string name = 2;
inline void Operation::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Operation::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Operation::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.name)
}
inline std::string* Operation::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.name)
  return _s;
}
inline const std::string& Operation::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Operation::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Operation::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Operation::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.name)
  return _impl_.name_.Release();
}
inline void Operation::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.Operation.name)
}

// repeated .tensorflow.eager.Operation.Input op_inputs = 10;
inline int Operation::_internal_op_inputs_size() const {
  return _impl_.op_inputs_.size();
}
inline int Operation::op_inputs_size() const {
  return _internal_op_inputs_size();
}
inline void Operation::clear_op_inputs() {
  _impl_.op_inputs_.Clear();
}
inline ::tensorflow::eager::Operation_Input* Operation::mutable_op_inputs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.op_inputs)
  return _impl_.op_inputs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input >*
Operation::mutable_op_inputs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.Operation.op_inputs)
  return &_impl_.op_inputs_;
}
inline const ::tensorflow::eager::Operation_Input& Operation::_internal_op_inputs(int index) const {
  return _impl_.op_inputs_.Get(index);
}
inline const ::tensorflow::eager::Operation_Input& Operation::op_inputs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.op_inputs)
  return _internal_op_inputs(index);
}
inline ::tensorflow::eager::Operation_Input* Operation::_internal_add_op_inputs() {
  return _impl_.op_inputs_.Add();
}
inline ::tensorflow::eager::Operation_Input* Operation::add_op_inputs() {
  ::tensorflow::eager::Operation_Input* _add = _internal_add_op_inputs();
  // @@protoc_insertion_point(field_add:tensorflow.eager.Operation.op_inputs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::Operation_Input >&
Operation::op_inputs() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.Operation.op_inputs)
  return _impl_.op_inputs_;
}

// repeated int64 control_op_ids = 4;
inline int Operation::_internal_control_op_ids_size() const {
  return _impl_.control_op_ids_.size();
}
inline int Operation::control_op_ids_size() const {
  return _internal_control_op_ids_size();
}
inline void Operation::clear_control_op_ids() {
  _impl_.control_op_ids_.Clear();
}
inline int64_t Operation::_internal_control_op_ids(int index) const {
  return _impl_.control_op_ids_.Get(index);
}
inline int64_t Operation::control_op_ids(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.control_op_ids)
  return _internal_control_op_ids(index);
}
inline void Operation::set_control_op_ids(int index, int64_t value) {
  _impl_.control_op_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.control_op_ids)
}
inline void Operation::_internal_add_control_op_ids(int64_t value) {
  _impl_.control_op_ids_.Add(value);
}
inline void Operation::add_control_op_ids(int64_t value) {
  _internal_add_control_op_ids(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.Operation.control_op_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Operation::_internal_control_op_ids() const {
  return _impl_.control_op_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
Operation::control_op_ids() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.Operation.control_op_ids)
  return _internal_control_op_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Operation::_internal_mutable_control_op_ids() {
  return &_impl_.control_op_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
Operation::mutable_control_op_ids() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.Operation.control_op_ids)
  return _internal_mutable_control_op_ids();
}

// map<string, .tensorflow.AttrValue> attrs = 5;
inline int Operation::_internal_attrs_size() const {
  return _impl_.attrs_.size();
}
inline int Operation::attrs_size() const {
  return _internal_attrs_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
Operation::_internal_attrs() const {
  return _impl_.attrs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >&
Operation::attrs() const {
  // @@protoc_insertion_point(field_map:tensorflow.eager.Operation.attrs)
  return _internal_attrs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
Operation::_internal_mutable_attrs() {
  return _impl_.attrs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::AttrValue >*
Operation::mutable_attrs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.eager.Operation.attrs)
  return _internal_mutable_attrs();
}

// string device = 6;
inline void Operation::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& Operation::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Operation::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.device)
}
inline std::string* Operation::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.Operation.device)
  return _s;
}
inline const std::string& Operation::_internal_device() const {
  return _impl_.device_.Get();
}
inline void Operation::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* Operation::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* Operation::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.Operation.device)
  return _impl_.device_.Release();
}
inline void Operation::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.Operation.device)
}

// bool is_component_function = 7;
inline void Operation::clear_is_component_function() {
  _impl_.is_component_function_ = false;
}
inline bool Operation::_internal_is_component_function() const {
  return _impl_.is_component_function_;
}
inline bool Operation::is_component_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.is_component_function)
  return _internal_is_component_function();
}
inline void Operation::_internal_set_is_component_function(bool value) {
  
  _impl_.is_component_function_ = value;
}
inline void Operation::set_is_component_function(bool value) {
  _internal_set_is_component_function(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.is_component_function)
}

// int64 func_step_id = 8;
inline void Operation::clear_func_step_id() {
  _impl_.func_step_id_ = int64_t{0};
}
inline int64_t Operation::_internal_func_step_id() const {
  return _impl_.func_step_id_;
}
inline int64_t Operation::func_step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.func_step_id)
  return _internal_func_step_id();
}
inline void Operation::_internal_set_func_step_id(int64_t value) {
  
  _impl_.func_step_id_ = value;
}
inline void Operation::set_func_step_id(int64_t value) {
  _internal_set_func_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.func_step_id)
}

// bool is_function = 9;
inline void Operation::clear_is_function() {
  _impl_.is_function_ = false;
}
inline bool Operation::_internal_is_function() const {
  return _impl_.is_function_;
}
inline bool Operation::is_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.Operation.is_function)
  return _internal_is_function();
}
inline void Operation::_internal_set_is_function(bool value) {
  
  _impl_.is_function_ = value;
}
inline void Operation::set_is_function(bool value) {
  _internal_set_is_function(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.Operation.is_function)
}

// -------------------------------------------------------------------

// QueueItem

// .tensorflow.eager.RemoteTensorHandle handle_to_decref = 1;
inline bool QueueItem::_internal_has_handle_to_decref() const {
  return item_case() == kHandleToDecref;
}
inline bool QueueItem::has_handle_to_decref() const {
  return _internal_has_handle_to_decref();
}
inline void QueueItem::set_has_handle_to_decref() {
  _impl_._oneof_case_[0] = kHandleToDecref;
}
inline ::tensorflow::eager::RemoteTensorHandle* QueueItem::release_handle_to_decref() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.handle_to_decref)
  if (_internal_has_handle_to_decref()) {
    clear_has_item();
    ::tensorflow::eager::RemoteTensorHandle* temp = _impl_.item_.handle_to_decref_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.handle_to_decref_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RemoteTensorHandle& QueueItem::_internal_handle_to_decref() const {
  return _internal_has_handle_to_decref()
      ? *_impl_.item_.handle_to_decref_
      : reinterpret_cast< ::tensorflow::eager::RemoteTensorHandle&>(::tensorflow::eager::_RemoteTensorHandle_default_instance_);
}
inline const ::tensorflow::eager::RemoteTensorHandle& QueueItem::handle_to_decref() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.handle_to_decref)
  return _internal_handle_to_decref();
}
inline ::tensorflow::eager::RemoteTensorHandle* QueueItem::unsafe_arena_release_handle_to_decref() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.QueueItem.handle_to_decref)
  if (_internal_has_handle_to_decref()) {
    clear_has_item();
    ::tensorflow::eager::RemoteTensorHandle* temp = _impl_.item_.handle_to_decref_;
    _impl_.item_.handle_to_decref_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QueueItem::unsafe_arena_set_allocated_handle_to_decref(::tensorflow::eager::RemoteTensorHandle* handle_to_decref) {
  clear_item();
  if (handle_to_decref) {
    set_has_handle_to_decref();
    _impl_.item_.handle_to_decref_ = handle_to_decref;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.QueueItem.handle_to_decref)
}
inline ::tensorflow::eager::RemoteTensorHandle* QueueItem::_internal_mutable_handle_to_decref() {
  if (!_internal_has_handle_to_decref()) {
    clear_item();
    set_has_handle_to_decref();
    _impl_.item_.handle_to_decref_ = CreateMaybeMessage< ::tensorflow::eager::RemoteTensorHandle >(GetArenaForAllocation());
  }
  return _impl_.item_.handle_to_decref_;
}
inline ::tensorflow::eager::RemoteTensorHandle* QueueItem::mutable_handle_to_decref() {
  ::tensorflow::eager::RemoteTensorHandle* _msg = _internal_mutable_handle_to_decref();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.handle_to_decref)
  return _msg;
}

// .tensorflow.eager.Operation operation = 2;
inline bool QueueItem::_internal_has_operation() const {
  return item_case() == kOperation;
}
inline bool QueueItem::has_operation() const {
  return _internal_has_operation();
}
inline void QueueItem::set_has_operation() {
  _impl_._oneof_case_[0] = kOperation;
}
inline void QueueItem::clear_operation() {
  if (_internal_has_operation()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.item_.operation_;
    }
    clear_has_item();
  }
}
inline ::tensorflow::eager::Operation* QueueItem::release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.operation)
  if (_internal_has_operation()) {
    clear_has_item();
    ::tensorflow::eager::Operation* temp = _impl_.item_.operation_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.operation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::Operation& QueueItem::_internal_operation() const {
  return _internal_has_operation()
      ? *_impl_.item_.operation_
      : reinterpret_cast< ::tensorflow::eager::Operation&>(::tensorflow::eager::_Operation_default_instance_);
}
inline const ::tensorflow::eager::Operation& QueueItem::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.operation)
  return _internal_operation();
}
inline ::tensorflow::eager::Operation* QueueItem::unsafe_arena_release_operation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.QueueItem.operation)
  if (_internal_has_operation()) {
    clear_has_item();
    ::tensorflow::eager::Operation* temp = _impl_.item_.operation_;
    _impl_.item_.operation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QueueItem::unsafe_arena_set_allocated_operation(::tensorflow::eager::Operation* operation) {
  clear_item();
  if (operation) {
    set_has_operation();
    _impl_.item_.operation_ = operation;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.QueueItem.operation)
}
inline ::tensorflow::eager::Operation* QueueItem::_internal_mutable_operation() {
  if (!_internal_has_operation()) {
    clear_item();
    set_has_operation();
    _impl_.item_.operation_ = CreateMaybeMessage< ::tensorflow::eager::Operation >(GetArenaForAllocation());
  }
  return _impl_.item_.operation_;
}
inline ::tensorflow::eager::Operation* QueueItem::mutable_operation() {
  ::tensorflow::eager::Operation* _msg = _internal_mutable_operation();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.operation)
  return _msg;
}

// .tensorflow.eager.SendTensorOp send_tensor = 3;
inline bool QueueItem::_internal_has_send_tensor() const {
  return item_case() == kSendTensor;
}
inline bool QueueItem::has_send_tensor() const {
  return _internal_has_send_tensor();
}
inline void QueueItem::set_has_send_tensor() {
  _impl_._oneof_case_[0] = kSendTensor;
}
inline void QueueItem::clear_send_tensor() {
  if (_internal_has_send_tensor()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.item_.send_tensor_;
    }
    clear_has_item();
  }
}
inline ::tensorflow::eager::SendTensorOp* QueueItem::release_send_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.send_tensor)
  if (_internal_has_send_tensor()) {
    clear_has_item();
    ::tensorflow::eager::SendTensorOp* temp = _impl_.item_.send_tensor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.send_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::SendTensorOp& QueueItem::_internal_send_tensor() const {
  return _internal_has_send_tensor()
      ? *_impl_.item_.send_tensor_
      : reinterpret_cast< ::tensorflow::eager::SendTensorOp&>(::tensorflow::eager::_SendTensorOp_default_instance_);
}
inline const ::tensorflow::eager::SendTensorOp& QueueItem::send_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.send_tensor)
  return _internal_send_tensor();
}
inline ::tensorflow::eager::SendTensorOp* QueueItem::unsafe_arena_release_send_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.QueueItem.send_tensor)
  if (_internal_has_send_tensor()) {
    clear_has_item();
    ::tensorflow::eager::SendTensorOp* temp = _impl_.item_.send_tensor_;
    _impl_.item_.send_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QueueItem::unsafe_arena_set_allocated_send_tensor(::tensorflow::eager::SendTensorOp* send_tensor) {
  clear_item();
  if (send_tensor) {
    set_has_send_tensor();
    _impl_.item_.send_tensor_ = send_tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.QueueItem.send_tensor)
}
inline ::tensorflow::eager::SendTensorOp* QueueItem::_internal_mutable_send_tensor() {
  if (!_internal_has_send_tensor()) {
    clear_item();
    set_has_send_tensor();
    _impl_.item_.send_tensor_ = CreateMaybeMessage< ::tensorflow::eager::SendTensorOp >(GetArenaForAllocation());
  }
  return _impl_.item_.send_tensor_;
}
inline ::tensorflow::eager::SendTensorOp* QueueItem::mutable_send_tensor() {
  ::tensorflow::eager::SendTensorOp* _msg = _internal_mutable_send_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.send_tensor)
  return _msg;
}

// .tensorflow.eager.RegisterFunctionOp register_function = 4;
inline bool QueueItem::_internal_has_register_function() const {
  return item_case() == kRegisterFunction;
}
inline bool QueueItem::has_register_function() const {
  return _internal_has_register_function();
}
inline void QueueItem::set_has_register_function() {
  _impl_._oneof_case_[0] = kRegisterFunction;
}
inline void QueueItem::clear_register_function() {
  if (_internal_has_register_function()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.item_.register_function_;
    }
    clear_has_item();
  }
}
inline ::tensorflow::eager::RegisterFunctionOp* QueueItem::release_register_function() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.register_function)
  if (_internal_has_register_function()) {
    clear_has_item();
    ::tensorflow::eager::RegisterFunctionOp* temp = _impl_.item_.register_function_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.register_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RegisterFunctionOp& QueueItem::_internal_register_function() const {
  return _internal_has_register_function()
      ? *_impl_.item_.register_function_
      : reinterpret_cast< ::tensorflow::eager::RegisterFunctionOp&>(::tensorflow::eager::_RegisterFunctionOp_default_instance_);
}
inline const ::tensorflow::eager::RegisterFunctionOp& QueueItem::register_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.register_function)
  return _internal_register_function();
}
inline ::tensorflow::eager::RegisterFunctionOp* QueueItem::unsafe_arena_release_register_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.QueueItem.register_function)
  if (_internal_has_register_function()) {
    clear_has_item();
    ::tensorflow::eager::RegisterFunctionOp* temp = _impl_.item_.register_function_;
    _impl_.item_.register_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QueueItem::unsafe_arena_set_allocated_register_function(::tensorflow::eager::RegisterFunctionOp* register_function) {
  clear_item();
  if (register_function) {
    set_has_register_function();
    _impl_.item_.register_function_ = register_function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.QueueItem.register_function)
}
inline ::tensorflow::eager::RegisterFunctionOp* QueueItem::_internal_mutable_register_function() {
  if (!_internal_has_register_function()) {
    clear_item();
    set_has_register_function();
    _impl_.item_.register_function_ = CreateMaybeMessage< ::tensorflow::eager::RegisterFunctionOp >(GetArenaForAllocation());
  }
  return _impl_.item_.register_function_;
}
inline ::tensorflow::eager::RegisterFunctionOp* QueueItem::mutable_register_function() {
  ::tensorflow::eager::RegisterFunctionOp* _msg = _internal_mutable_register_function();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.register_function)
  return _msg;
}

// .tensorflow.eager.CleanupFunctionOp cleanup_function = 5;
inline bool QueueItem::_internal_has_cleanup_function() const {
  return item_case() == kCleanupFunction;
}
inline bool QueueItem::has_cleanup_function() const {
  return _internal_has_cleanup_function();
}
inline void QueueItem::set_has_cleanup_function() {
  _impl_._oneof_case_[0] = kCleanupFunction;
}
inline void QueueItem::clear_cleanup_function() {
  if (_internal_has_cleanup_function()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.item_.cleanup_function_;
    }
    clear_has_item();
  }
}
inline ::tensorflow::eager::CleanupFunctionOp* QueueItem::release_cleanup_function() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.cleanup_function)
  if (_internal_has_cleanup_function()) {
    clear_has_item();
    ::tensorflow::eager::CleanupFunctionOp* temp = _impl_.item_.cleanup_function_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.cleanup_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::CleanupFunctionOp& QueueItem::_internal_cleanup_function() const {
  return _internal_has_cleanup_function()
      ? *_impl_.item_.cleanup_function_
      : reinterpret_cast< ::tensorflow::eager::CleanupFunctionOp&>(::tensorflow::eager::_CleanupFunctionOp_default_instance_);
}
inline const ::tensorflow::eager::CleanupFunctionOp& QueueItem::cleanup_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.cleanup_function)
  return _internal_cleanup_function();
}
inline ::tensorflow::eager::CleanupFunctionOp* QueueItem::unsafe_arena_release_cleanup_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.QueueItem.cleanup_function)
  if (_internal_has_cleanup_function()) {
    clear_has_item();
    ::tensorflow::eager::CleanupFunctionOp* temp = _impl_.item_.cleanup_function_;
    _impl_.item_.cleanup_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QueueItem::unsafe_arena_set_allocated_cleanup_function(::tensorflow::eager::CleanupFunctionOp* cleanup_function) {
  clear_item();
  if (cleanup_function) {
    set_has_cleanup_function();
    _impl_.item_.cleanup_function_ = cleanup_function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.QueueItem.cleanup_function)
}
inline ::tensorflow::eager::CleanupFunctionOp* QueueItem::_internal_mutable_cleanup_function() {
  if (!_internal_has_cleanup_function()) {
    clear_item();
    set_has_cleanup_function();
    _impl_.item_.cleanup_function_ = CreateMaybeMessage< ::tensorflow::eager::CleanupFunctionOp >(GetArenaForAllocation());
  }
  return _impl_.item_.cleanup_function_;
}
inline ::tensorflow::eager::CleanupFunctionOp* QueueItem::mutable_cleanup_function() {
  ::tensorflow::eager::CleanupFunctionOp* _msg = _internal_mutable_cleanup_function();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.cleanup_function)
  return _msg;
}

// .tensorflow.eager.SyncRemoteExecutorForStream sync_remote_executor_for_stream = 6;
inline bool QueueItem::_internal_has_sync_remote_executor_for_stream() const {
  return item_case() == kSyncRemoteExecutorForStream;
}
inline bool QueueItem::has_sync_remote_executor_for_stream() const {
  return _internal_has_sync_remote_executor_for_stream();
}
inline void QueueItem::set_has_sync_remote_executor_for_stream() {
  _impl_._oneof_case_[0] = kSyncRemoteExecutorForStream;
}
inline void QueueItem::clear_sync_remote_executor_for_stream() {
  if (_internal_has_sync_remote_executor_for_stream()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.item_.sync_remote_executor_for_stream_;
    }
    clear_has_item();
  }
}
inline ::tensorflow::eager::SyncRemoteExecutorForStream* QueueItem::release_sync_remote_executor_for_stream() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.sync_remote_executor_for_stream)
  if (_internal_has_sync_remote_executor_for_stream()) {
    clear_has_item();
    ::tensorflow::eager::SyncRemoteExecutorForStream* temp = _impl_.item_.sync_remote_executor_for_stream_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.sync_remote_executor_for_stream_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::SyncRemoteExecutorForStream& QueueItem::_internal_sync_remote_executor_for_stream() const {
  return _internal_has_sync_remote_executor_for_stream()
      ? *_impl_.item_.sync_remote_executor_for_stream_
      : reinterpret_cast< ::tensorflow::eager::SyncRemoteExecutorForStream&>(::tensorflow::eager::_SyncRemoteExecutorForStream_default_instance_);
}
inline const ::tensorflow::eager::SyncRemoteExecutorForStream& QueueItem::sync_remote_executor_for_stream() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.sync_remote_executor_for_stream)
  return _internal_sync_remote_executor_for_stream();
}
inline ::tensorflow::eager::SyncRemoteExecutorForStream* QueueItem::unsafe_arena_release_sync_remote_executor_for_stream() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.QueueItem.sync_remote_executor_for_stream)
  if (_internal_has_sync_remote_executor_for_stream()) {
    clear_has_item();
    ::tensorflow::eager::SyncRemoteExecutorForStream* temp = _impl_.item_.sync_remote_executor_for_stream_;
    _impl_.item_.sync_remote_executor_for_stream_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QueueItem::unsafe_arena_set_allocated_sync_remote_executor_for_stream(::tensorflow::eager::SyncRemoteExecutorForStream* sync_remote_executor_for_stream) {
  clear_item();
  if (sync_remote_executor_for_stream) {
    set_has_sync_remote_executor_for_stream();
    _impl_.item_.sync_remote_executor_for_stream_ = sync_remote_executor_for_stream;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.QueueItem.sync_remote_executor_for_stream)
}
inline ::tensorflow::eager::SyncRemoteExecutorForStream* QueueItem::_internal_mutable_sync_remote_executor_for_stream() {
  if (!_internal_has_sync_remote_executor_for_stream()) {
    clear_item();
    set_has_sync_remote_executor_for_stream();
    _impl_.item_.sync_remote_executor_for_stream_ = CreateMaybeMessage< ::tensorflow::eager::SyncRemoteExecutorForStream >(GetArenaForAllocation());
  }
  return _impl_.item_.sync_remote_executor_for_stream_;
}
inline ::tensorflow::eager::SyncRemoteExecutorForStream* QueueItem::mutable_sync_remote_executor_for_stream() {
  ::tensorflow::eager::SyncRemoteExecutorForStream* _msg = _internal_mutable_sync_remote_executor_for_stream();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.sync_remote_executor_for_stream)
  return _msg;
}

// .tensorflow.eager.SendPackedHandleOp send_packed_handle = 7;
inline bool QueueItem::_internal_has_send_packed_handle() const {
  return item_case() == kSendPackedHandle;
}
inline bool QueueItem::has_send_packed_handle() const {
  return _internal_has_send_packed_handle();
}
inline void QueueItem::set_has_send_packed_handle() {
  _impl_._oneof_case_[0] = kSendPackedHandle;
}
inline void QueueItem::clear_send_packed_handle() {
  if (_internal_has_send_packed_handle()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.item_.send_packed_handle_;
    }
    clear_has_item();
  }
}
inline ::tensorflow::eager::SendPackedHandleOp* QueueItem::release_send_packed_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.send_packed_handle)
  if (_internal_has_send_packed_handle()) {
    clear_has_item();
    ::tensorflow::eager::SendPackedHandleOp* temp = _impl_.item_.send_packed_handle_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.send_packed_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::SendPackedHandleOp& QueueItem::_internal_send_packed_handle() const {
  return _internal_has_send_packed_handle()
      ? *_impl_.item_.send_packed_handle_
      : reinterpret_cast< ::tensorflow::eager::SendPackedHandleOp&>(::tensorflow::eager::_SendPackedHandleOp_default_instance_);
}
inline const ::tensorflow::eager::SendPackedHandleOp& QueueItem::send_packed_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.send_packed_handle)
  return _internal_send_packed_handle();
}
inline ::tensorflow::eager::SendPackedHandleOp* QueueItem::unsafe_arena_release_send_packed_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.QueueItem.send_packed_handle)
  if (_internal_has_send_packed_handle()) {
    clear_has_item();
    ::tensorflow::eager::SendPackedHandleOp* temp = _impl_.item_.send_packed_handle_;
    _impl_.item_.send_packed_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QueueItem::unsafe_arena_set_allocated_send_packed_handle(::tensorflow::eager::SendPackedHandleOp* send_packed_handle) {
  clear_item();
  if (send_packed_handle) {
    set_has_send_packed_handle();
    _impl_.item_.send_packed_handle_ = send_packed_handle;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.QueueItem.send_packed_handle)
}
inline ::tensorflow::eager::SendPackedHandleOp* QueueItem::_internal_mutable_send_packed_handle() {
  if (!_internal_has_send_packed_handle()) {
    clear_item();
    set_has_send_packed_handle();
    _impl_.item_.send_packed_handle_ = CreateMaybeMessage< ::tensorflow::eager::SendPackedHandleOp >(GetArenaForAllocation());
  }
  return _impl_.item_.send_packed_handle_;
}
inline ::tensorflow::eager::SendPackedHandleOp* QueueItem::mutable_send_packed_handle() {
  ::tensorflow::eager::SendPackedHandleOp* _msg = _internal_mutable_send_packed_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.send_packed_handle)
  return _msg;
}

// .tensorflow.eager.RemoveFunctionOp remove_function = 8;
inline bool QueueItem::_internal_has_remove_function() const {
  return item_case() == kRemoveFunction;
}
inline bool QueueItem::has_remove_function() const {
  return _internal_has_remove_function();
}
inline void QueueItem::set_has_remove_function() {
  _impl_._oneof_case_[0] = kRemoveFunction;
}
inline void QueueItem::clear_remove_function() {
  if (_internal_has_remove_function()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.item_.remove_function_;
    }
    clear_has_item();
  }
}
inline ::tensorflow::eager::RemoveFunctionOp* QueueItem::release_remove_function() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.QueueItem.remove_function)
  if (_internal_has_remove_function()) {
    clear_has_item();
    ::tensorflow::eager::RemoveFunctionOp* temp = _impl_.item_.remove_function_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.remove_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RemoveFunctionOp& QueueItem::_internal_remove_function() const {
  return _internal_has_remove_function()
      ? *_impl_.item_.remove_function_
      : reinterpret_cast< ::tensorflow::eager::RemoveFunctionOp&>(::tensorflow::eager::_RemoveFunctionOp_default_instance_);
}
inline const ::tensorflow::eager::RemoveFunctionOp& QueueItem::remove_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueItem.remove_function)
  return _internal_remove_function();
}
inline ::tensorflow::eager::RemoveFunctionOp* QueueItem::unsafe_arena_release_remove_function() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.QueueItem.remove_function)
  if (_internal_has_remove_function()) {
    clear_has_item();
    ::tensorflow::eager::RemoveFunctionOp* temp = _impl_.item_.remove_function_;
    _impl_.item_.remove_function_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void QueueItem::unsafe_arena_set_allocated_remove_function(::tensorflow::eager::RemoveFunctionOp* remove_function) {
  clear_item();
  if (remove_function) {
    set_has_remove_function();
    _impl_.item_.remove_function_ = remove_function;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.QueueItem.remove_function)
}
inline ::tensorflow::eager::RemoveFunctionOp* QueueItem::_internal_mutable_remove_function() {
  if (!_internal_has_remove_function()) {
    clear_item();
    set_has_remove_function();
    _impl_.item_.remove_function_ = CreateMaybeMessage< ::tensorflow::eager::RemoveFunctionOp >(GetArenaForAllocation());
  }
  return _impl_.item_.remove_function_;
}
inline ::tensorflow::eager::RemoveFunctionOp* QueueItem::mutable_remove_function() {
  ::tensorflow::eager::RemoveFunctionOp* _msg = _internal_mutable_remove_function();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueItem.remove_function)
  return _msg;
}

inline bool QueueItem::has_item() const {
  return item_case() != ITEM_NOT_SET;
}
inline void QueueItem::clear_has_item() {
  _impl_._oneof_case_[0] = ITEM_NOT_SET;
}
inline QueueItem::ItemCase QueueItem::item_case() const {
  return QueueItem::ItemCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// QueueResponse

// repeated .tensorflow.TensorShapeProto shape = 1;
inline int QueueResponse::_internal_shape_size() const {
  return _impl_.shape_.size();
}
inline int QueueResponse::shape_size() const {
  return _internal_shape_size();
}
inline ::tensorflow::TensorShapeProto* QueueResponse::mutable_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueResponse.shape)
  return _impl_.shape_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
QueueResponse::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.QueueResponse.shape)
  return &_impl_.shape_;
}
inline const ::tensorflow::TensorShapeProto& QueueResponse::_internal_shape(int index) const {
  return _impl_.shape_.Get(index);
}
inline const ::tensorflow::TensorShapeProto& QueueResponse::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueResponse.shape)
  return _internal_shape(index);
}
inline ::tensorflow::TensorShapeProto* QueueResponse::_internal_add_shape() {
  return _impl_.shape_.Add();
}
inline ::tensorflow::TensorShapeProto* QueueResponse::add_shape() {
  ::tensorflow::TensorShapeProto* _add = _internal_add_shape();
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.shape)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
QueueResponse::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.QueueResponse.shape)
  return _impl_.shape_;
}

// repeated string device = 3;
inline int QueueResponse::_internal_device_size() const {
  return _impl_.device_.size();
}
inline int QueueResponse::device_size() const {
  return _internal_device_size();
}
inline void QueueResponse::clear_device() {
  _impl_.device_.Clear();
}
inline std::string* QueueResponse::add_device() {
  std::string* _s = _internal_add_device();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.eager.QueueResponse.device)
  return _s;
}
inline const std::string& QueueResponse::_internal_device(int index) const {
  return _impl_.device_.Get(index);
}
inline const std::string& QueueResponse::device(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueResponse.device)
  return _internal_device(index);
}
inline std::string* QueueResponse::mutable_device(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueResponse.device)
  return _impl_.device_.Mutable(index);
}
inline void QueueResponse::set_device(int index, const std::string& value) {
  _impl_.device_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::set_device(int index, std::string&& value) {
  _impl_.device_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::set_device(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.device_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::set_device(int index, const char* value, size_t size) {
  _impl_.device_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.eager.QueueResponse.device)
}
inline std::string* QueueResponse::_internal_add_device() {
  return _impl_.device_.Add();
}
inline void QueueResponse::add_device(const std::string& value) {
  _impl_.device_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::add_device(std::string&& value) {
  _impl_.device_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::add_device(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.device_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.eager.QueueResponse.device)
}
inline void QueueResponse::add_device(const char* value, size_t size) {
  _impl_.device_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.eager.QueueResponse.device)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
QueueResponse::device() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.QueueResponse.device)
  return _impl_.device_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
QueueResponse::mutable_device() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.QueueResponse.device)
  return &_impl_.device_;
}

// repeated .tensorflow.TensorProto tensor = 2;
inline int QueueResponse::_internal_tensor_size() const {
  return _impl_.tensor_.size();
}
inline int QueueResponse::tensor_size() const {
  return _internal_tensor_size();
}
inline ::tensorflow::TensorProto* QueueResponse::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.QueueResponse.tensor)
  return _impl_.tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
QueueResponse::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.QueueResponse.tensor)
  return &_impl_.tensor_;
}
inline const ::tensorflow::TensorProto& QueueResponse::_internal_tensor(int index) const {
  return _impl_.tensor_.Get(index);
}
inline const ::tensorflow::TensorProto& QueueResponse::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.QueueResponse.tensor)
  return _internal_tensor(index);
}
inline ::tensorflow::TensorProto* QueueResponse::_internal_add_tensor() {
  return _impl_.tensor_.Add();
}
inline ::tensorflow::TensorProto* QueueResponse::add_tensor() {
  ::tensorflow::TensorProto* _add = _internal_add_tensor();
  // @@protoc_insertion_point(field_add:tensorflow.eager.QueueResponse.tensor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
QueueResponse::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.QueueResponse.tensor)
  return _impl_.tensor_;
}

// -------------------------------------------------------------------

// CreateContextRequest

// .tensorflow.ServerDef server_def = 1;
inline bool CreateContextRequest::_internal_has_server_def() const {
  return this != internal_default_instance() && _impl_.server_def_ != nullptr;
}
inline bool CreateContextRequest::has_server_def() const {
  return _internal_has_server_def();
}
inline const ::tensorflow::ServerDef& CreateContextRequest::_internal_server_def() const {
  const ::tensorflow::ServerDef* p = _impl_.server_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ServerDef&>(
      ::tensorflow::_ServerDef_default_instance_);
}
inline const ::tensorflow::ServerDef& CreateContextRequest::server_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.server_def)
  return _internal_server_def();
}
inline void CreateContextRequest::unsafe_arena_set_allocated_server_def(
    ::tensorflow::ServerDef* server_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_def_);
  }
  _impl_.server_def_ = server_def;
  if (server_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.CreateContextRequest.server_def)
}
inline ::tensorflow::ServerDef* CreateContextRequest::release_server_def() {
  
  ::tensorflow::ServerDef* temp = _impl_.server_def_;
  _impl_.server_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ServerDef* CreateContextRequest::unsafe_arena_release_server_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.CreateContextRequest.server_def)
  
  ::tensorflow::ServerDef* temp = _impl_.server_def_;
  _impl_.server_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ServerDef* CreateContextRequest::_internal_mutable_server_def() {
  
  if (_impl_.server_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ServerDef>(GetArenaForAllocation());
    _impl_.server_def_ = p;
  }
  return _impl_.server_def_;
}
inline ::tensorflow::ServerDef* CreateContextRequest::mutable_server_def() {
  ::tensorflow::ServerDef* _msg = _internal_mutable_server_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextRequest.server_def)
  return _msg;
}
inline void CreateContextRequest::set_allocated_server_def(::tensorflow::ServerDef* server_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_def_);
  }
  if (server_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def));
    if (message_arena != submessage_arena) {
      server_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.server_def_ = server_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.CreateContextRequest.server_def)
}

// bool async = 2;
inline void CreateContextRequest::clear_async() {
  _impl_.async_ = false;
}
inline bool CreateContextRequest::_internal_async() const {
  return _impl_.async_;
}
inline bool CreateContextRequest::async() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.async)
  return _internal_async();
}
inline void CreateContextRequest::_internal_set_async(bool value) {
  
  _impl_.async_ = value;
}
inline void CreateContextRequest::set_async(bool value) {
  _internal_set_async(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.async)
}

// int64 keep_alive_secs = 3;
inline void CreateContextRequest::clear_keep_alive_secs() {
  _impl_.keep_alive_secs_ = int64_t{0};
}
inline int64_t CreateContextRequest::_internal_keep_alive_secs() const {
  return _impl_.keep_alive_secs_;
}
inline int64_t CreateContextRequest::keep_alive_secs() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.keep_alive_secs)
  return _internal_keep_alive_secs();
}
inline void CreateContextRequest::_internal_set_keep_alive_secs(int64_t value) {
  
  _impl_.keep_alive_secs_ = value;
}
inline void CreateContextRequest::set_keep_alive_secs(int64_t value) {
  _internal_set_keep_alive_secs(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.keep_alive_secs)
}

// .tensorflow.VersionDef version_def = 4;
inline bool CreateContextRequest::_internal_has_version_def() const {
  return this != internal_default_instance() && _impl_.version_def_ != nullptr;
}
inline bool CreateContextRequest::has_version_def() const {
  return _internal_has_version_def();
}
inline const ::tensorflow::VersionDef& CreateContextRequest::_internal_version_def() const {
  const ::tensorflow::VersionDef* p = _impl_.version_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::VersionDef&>(
      ::tensorflow::_VersionDef_default_instance_);
}
inline const ::tensorflow::VersionDef& CreateContextRequest::version_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.version_def)
  return _internal_version_def();
}
inline void CreateContextRequest::unsafe_arena_set_allocated_version_def(
    ::tensorflow::VersionDef* version_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.version_def_);
  }
  _impl_.version_def_ = version_def;
  if (version_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.CreateContextRequest.version_def)
}
inline ::tensorflow::VersionDef* CreateContextRequest::release_version_def() {
  
  ::tensorflow::VersionDef* temp = _impl_.version_def_;
  _impl_.version_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::VersionDef* CreateContextRequest::unsafe_arena_release_version_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.CreateContextRequest.version_def)
  
  ::tensorflow::VersionDef* temp = _impl_.version_def_;
  _impl_.version_def_ = nullptr;
  return temp;
}
inline ::tensorflow::VersionDef* CreateContextRequest::_internal_mutable_version_def() {
  
  if (_impl_.version_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VersionDef>(GetArenaForAllocation());
    _impl_.version_def_ = p;
  }
  return _impl_.version_def_;
}
inline ::tensorflow::VersionDef* CreateContextRequest::mutable_version_def() {
  ::tensorflow::VersionDef* _msg = _internal_mutable_version_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextRequest.version_def)
  return _msg;
}
inline void CreateContextRequest::set_allocated_version_def(::tensorflow::VersionDef* version_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.version_def_);
  }
  if (version_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(version_def));
    if (message_arena != submessage_arena) {
      version_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, version_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.version_def_ = version_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.CreateContextRequest.version_def)
}

// repeated .tensorflow.DeviceAttributes cluster_device_attributes = 6;
inline int CreateContextRequest::_internal_cluster_device_attributes_size() const {
  return _impl_.cluster_device_attributes_.size();
}
inline int CreateContextRequest::cluster_device_attributes_size() const {
  return _internal_cluster_device_attributes_size();
}
inline ::tensorflow::DeviceAttributes* CreateContextRequest::mutable_cluster_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return _impl_.cluster_device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CreateContextRequest::mutable_cluster_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return &_impl_.cluster_device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CreateContextRequest::_internal_cluster_device_attributes(int index) const {
  return _impl_.cluster_device_attributes_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& CreateContextRequest::cluster_device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return _internal_cluster_device_attributes(index);
}
inline ::tensorflow::DeviceAttributes* CreateContextRequest::_internal_add_cluster_device_attributes() {
  return _impl_.cluster_device_attributes_.Add();
}
inline ::tensorflow::DeviceAttributes* CreateContextRequest::add_cluster_device_attributes() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_cluster_device_attributes();
  // @@protoc_insertion_point(field_add:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CreateContextRequest::cluster_device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.CreateContextRequest.cluster_device_attributes)
  return _impl_.cluster_device_attributes_;
}

// fixed64 context_id = 7;
inline void CreateContextRequest::clear_context_id() {
  _impl_.context_id_ = uint64_t{0u};
}
inline uint64_t CreateContextRequest::_internal_context_id() const {
  return _impl_.context_id_;
}
inline uint64_t CreateContextRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.context_id)
  return _internal_context_id();
}
inline void CreateContextRequest::_internal_set_context_id(uint64_t value) {
  
  _impl_.context_id_ = value;
}
inline void CreateContextRequest::set_context_id(uint64_t value) {
  _internal_set_context_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.context_id)
}

// fixed64 context_view_id = 8;
inline void CreateContextRequest::clear_context_view_id() {
  _impl_.context_view_id_ = uint64_t{0u};
}
inline uint64_t CreateContextRequest::_internal_context_view_id() const {
  return _impl_.context_view_id_;
}
inline uint64_t CreateContextRequest::context_view_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.context_view_id)
  return _internal_context_view_id();
}
inline void CreateContextRequest::_internal_set_context_view_id(uint64_t value) {
  
  _impl_.context_view_id_ = value;
}
inline void CreateContextRequest::set_context_view_id(uint64_t value) {
  _internal_set_context_view_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.context_view_id)
}

// bool lazy_copy_remote_function_inputs = 9;
inline void CreateContextRequest::clear_lazy_copy_remote_function_inputs() {
  _impl_.lazy_copy_remote_function_inputs_ = false;
}
inline bool CreateContextRequest::_internal_lazy_copy_remote_function_inputs() const {
  return _impl_.lazy_copy_remote_function_inputs_;
}
inline bool CreateContextRequest::lazy_copy_remote_function_inputs() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.lazy_copy_remote_function_inputs)
  return _internal_lazy_copy_remote_function_inputs();
}
inline void CreateContextRequest::_internal_set_lazy_copy_remote_function_inputs(bool value) {
  
  _impl_.lazy_copy_remote_function_inputs_ = value;
}
inline void CreateContextRequest::set_lazy_copy_remote_function_inputs(bool value) {
  _internal_set_lazy_copy_remote_function_inputs(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.lazy_copy_remote_function_inputs)
}

// bool clear_existing_contexts = 10;
inline void CreateContextRequest::clear_clear_existing_contexts() {
  _impl_.clear_existing_contexts_ = false;
}
inline bool CreateContextRequest::_internal_clear_existing_contexts() const {
  return _impl_.clear_existing_contexts_;
}
inline bool CreateContextRequest::clear_existing_contexts() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextRequest.clear_existing_contexts)
  return _internal_clear_existing_contexts();
}
inline void CreateContextRequest::_internal_set_clear_existing_contexts(bool value) {
  
  _impl_.clear_existing_contexts_ = value;
}
inline void CreateContextRequest::set_clear_existing_contexts(bool value) {
  _internal_set_clear_existing_contexts(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CreateContextRequest.clear_existing_contexts)
}

// -------------------------------------------------------------------

// CreateContextResponse

// repeated .tensorflow.DeviceAttributes device_attributes = 2;
inline int CreateContextResponse::_internal_device_attributes_size() const {
  return _impl_.device_attributes_.size();
}
inline int CreateContextResponse::device_attributes_size() const {
  return _internal_device_attributes_size();
}
inline ::tensorflow::DeviceAttributes* CreateContextResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.CreateContextResponse.device_attributes)
  return _impl_.device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CreateContextResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.CreateContextResponse.device_attributes)
  return &_impl_.device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CreateContextResponse::_internal_device_attributes(int index) const {
  return _impl_.device_attributes_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& CreateContextResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CreateContextResponse.device_attributes)
  return _internal_device_attributes(index);
}
inline ::tensorflow::DeviceAttributes* CreateContextResponse::_internal_add_device_attributes() {
  return _impl_.device_attributes_.Add();
}
inline ::tensorflow::DeviceAttributes* CreateContextResponse::add_device_attributes() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_device_attributes();
  // @@protoc_insertion_point(field_add:tensorflow.eager.CreateContextResponse.device_attributes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CreateContextResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.CreateContextResponse.device_attributes)
  return _impl_.device_attributes_;
}

// -------------------------------------------------------------------

// UpdateContextRequest

// .tensorflow.ServerDef server_def = 1;
inline bool UpdateContextRequest::_internal_has_server_def() const {
  return this != internal_default_instance() && _impl_.server_def_ != nullptr;
}
inline bool UpdateContextRequest::has_server_def() const {
  return _internal_has_server_def();
}
inline const ::tensorflow::ServerDef& UpdateContextRequest::_internal_server_def() const {
  const ::tensorflow::ServerDef* p = _impl_.server_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ServerDef&>(
      ::tensorflow::_ServerDef_default_instance_);
}
inline const ::tensorflow::ServerDef& UpdateContextRequest::server_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextRequest.server_def)
  return _internal_server_def();
}
inline void UpdateContextRequest::unsafe_arena_set_allocated_server_def(
    ::tensorflow::ServerDef* server_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_def_);
  }
  _impl_.server_def_ = server_def;
  if (server_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.UpdateContextRequest.server_def)
}
inline ::tensorflow::ServerDef* UpdateContextRequest::release_server_def() {
  
  ::tensorflow::ServerDef* temp = _impl_.server_def_;
  _impl_.server_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ServerDef* UpdateContextRequest::unsafe_arena_release_server_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.UpdateContextRequest.server_def)
  
  ::tensorflow::ServerDef* temp = _impl_.server_def_;
  _impl_.server_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ServerDef* UpdateContextRequest::_internal_mutable_server_def() {
  
  if (_impl_.server_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ServerDef>(GetArenaForAllocation());
    _impl_.server_def_ = p;
  }
  return _impl_.server_def_;
}
inline ::tensorflow::ServerDef* UpdateContextRequest::mutable_server_def() {
  ::tensorflow::ServerDef* _msg = _internal_mutable_server_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.UpdateContextRequest.server_def)
  return _msg;
}
inline void UpdateContextRequest::set_allocated_server_def(::tensorflow::ServerDef* server_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_def_);
  }
  if (server_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def));
    if (message_arena != submessage_arena) {
      server_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.server_def_ = server_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.UpdateContextRequest.server_def)
}

// repeated .tensorflow.DeviceAttributes cluster_device_attributes = 2;
inline int UpdateContextRequest::_internal_cluster_device_attributes_size() const {
  return _impl_.cluster_device_attributes_.size();
}
inline int UpdateContextRequest::cluster_device_attributes_size() const {
  return _internal_cluster_device_attributes_size();
}
inline ::tensorflow::DeviceAttributes* UpdateContextRequest::mutable_cluster_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return _impl_.cluster_device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
UpdateContextRequest::mutable_cluster_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return &_impl_.cluster_device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& UpdateContextRequest::_internal_cluster_device_attributes(int index) const {
  return _impl_.cluster_device_attributes_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& UpdateContextRequest::cluster_device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return _internal_cluster_device_attributes(index);
}
inline ::tensorflow::DeviceAttributes* UpdateContextRequest::_internal_add_cluster_device_attributes() {
  return _impl_.cluster_device_attributes_.Add();
}
inline ::tensorflow::DeviceAttributes* UpdateContextRequest::add_cluster_device_attributes() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_cluster_device_attributes();
  // @@protoc_insertion_point(field_add:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
UpdateContextRequest::cluster_device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.UpdateContextRequest.cluster_device_attributes)
  return _impl_.cluster_device_attributes_;
}

// fixed64 context_id = 3;
inline void UpdateContextRequest::clear_context_id() {
  _impl_.context_id_ = uint64_t{0u};
}
inline uint64_t UpdateContextRequest::_internal_context_id() const {
  return _impl_.context_id_;
}
inline uint64_t UpdateContextRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextRequest.context_id)
  return _internal_context_id();
}
inline void UpdateContextRequest::_internal_set_context_id(uint64_t value) {
  
  _impl_.context_id_ = value;
}
inline void UpdateContextRequest::set_context_id(uint64_t value) {
  _internal_set_context_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.UpdateContextRequest.context_id)
}

// fixed64 context_view_id = 4;
inline void UpdateContextRequest::clear_context_view_id() {
  _impl_.context_view_id_ = uint64_t{0u};
}
inline uint64_t UpdateContextRequest::_internal_context_view_id() const {
  return _impl_.context_view_id_;
}
inline uint64_t UpdateContextRequest::context_view_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextRequest.context_view_id)
  return _internal_context_view_id();
}
inline void UpdateContextRequest::_internal_set_context_view_id(uint64_t value) {
  
  _impl_.context_view_id_ = value;
}
inline void UpdateContextRequest::set_context_view_id(uint64_t value) {
  _internal_set_context_view_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.UpdateContextRequest.context_view_id)
}

// -------------------------------------------------------------------

// UpdateContextResponse

// repeated .tensorflow.DeviceAttributes device_attributes = 1;
inline int UpdateContextResponse::_internal_device_attributes_size() const {
  return _impl_.device_attributes_.size();
}
inline int UpdateContextResponse::device_attributes_size() const {
  return _internal_device_attributes_size();
}
inline ::tensorflow::DeviceAttributes* UpdateContextResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.UpdateContextResponse.device_attributes)
  return _impl_.device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
UpdateContextResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.UpdateContextResponse.device_attributes)
  return &_impl_.device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& UpdateContextResponse::_internal_device_attributes(int index) const {
  return _impl_.device_attributes_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& UpdateContextResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.UpdateContextResponse.device_attributes)
  return _internal_device_attributes(index);
}
inline ::tensorflow::DeviceAttributes* UpdateContextResponse::_internal_add_device_attributes() {
  return _impl_.device_attributes_.Add();
}
inline ::tensorflow::DeviceAttributes* UpdateContextResponse::add_device_attributes() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_device_attributes();
  // @@protoc_insertion_point(field_add:tensorflow.eager.UpdateContextResponse.device_attributes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
UpdateContextResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.UpdateContextResponse.device_attributes)
  return _impl_.device_attributes_;
}

// -------------------------------------------------------------------

// EnqueueRequest

// fixed64 context_id = 1;
inline void EnqueueRequest::clear_context_id() {
  _impl_.context_id_ = uint64_t{0u};
}
inline uint64_t EnqueueRequest::_internal_context_id() const {
  return _impl_.context_id_;
}
inline uint64_t EnqueueRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueRequest.context_id)
  return _internal_context_id();
}
inline void EnqueueRequest::_internal_set_context_id(uint64_t value) {
  
  _impl_.context_id_ = value;
}
inline void EnqueueRequest::set_context_id(uint64_t value) {
  _internal_set_context_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.EnqueueRequest.context_id)
}

// repeated .tensorflow.eager.QueueItem queue = 3;
inline int EnqueueRequest::_internal_queue_size() const {
  return _impl_.queue_.size();
}
inline int EnqueueRequest::queue_size() const {
  return _internal_queue_size();
}
inline void EnqueueRequest::clear_queue() {
  _impl_.queue_.Clear();
}
inline ::tensorflow::eager::QueueItem* EnqueueRequest::mutable_queue(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.EnqueueRequest.queue)
  return _impl_.queue_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem >*
EnqueueRequest::mutable_queue() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.EnqueueRequest.queue)
  return &_impl_.queue_;
}
inline const ::tensorflow::eager::QueueItem& EnqueueRequest::_internal_queue(int index) const {
  return _impl_.queue_.Get(index);
}
inline const ::tensorflow::eager::QueueItem& EnqueueRequest::queue(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueRequest.queue)
  return _internal_queue(index);
}
inline ::tensorflow::eager::QueueItem* EnqueueRequest::_internal_add_queue() {
  return _impl_.queue_.Add();
}
inline ::tensorflow::eager::QueueItem* EnqueueRequest::add_queue() {
  ::tensorflow::eager::QueueItem* _add = _internal_add_queue();
  // @@protoc_insertion_point(field_add:tensorflow.eager.EnqueueRequest.queue)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueItem >&
EnqueueRequest::queue() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.EnqueueRequest.queue)
  return _impl_.queue_;
}

// -------------------------------------------------------------------

// EnqueueResponse

// repeated .tensorflow.eager.QueueResponse queue_response = 1;
inline int EnqueueResponse::_internal_queue_response_size() const {
  return _impl_.queue_response_.size();
}
inline int EnqueueResponse::queue_response_size() const {
  return _internal_queue_response_size();
}
inline void EnqueueResponse::clear_queue_response() {
  _impl_.queue_response_.Clear();
}
inline ::tensorflow::eager::QueueResponse* EnqueueResponse::mutable_queue_response(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.EnqueueResponse.queue_response)
  return _impl_.queue_response_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse >*
EnqueueResponse::mutable_queue_response() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.EnqueueResponse.queue_response)
  return &_impl_.queue_response_;
}
inline const ::tensorflow::eager::QueueResponse& EnqueueResponse::_internal_queue_response(int index) const {
  return _impl_.queue_response_.Get(index);
}
inline const ::tensorflow::eager::QueueResponse& EnqueueResponse::queue_response(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.EnqueueResponse.queue_response)
  return _internal_queue_response(index);
}
inline ::tensorflow::eager::QueueResponse* EnqueueResponse::_internal_add_queue_response() {
  return _impl_.queue_response_.Add();
}
inline ::tensorflow::eager::QueueResponse* EnqueueResponse::add_queue_response() {
  ::tensorflow::eager::QueueResponse* _add = _internal_add_queue_response();
  // @@protoc_insertion_point(field_add:tensorflow.eager.EnqueueResponse.queue_response)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::QueueResponse >&
EnqueueResponse::queue_response() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.EnqueueResponse.queue_response)
  return _impl_.queue_response_;
}

// -------------------------------------------------------------------

// WaitQueueDoneRequest

// fixed64 context_id = 1;
inline void WaitQueueDoneRequest::clear_context_id() {
  _impl_.context_id_ = uint64_t{0u};
}
inline uint64_t WaitQueueDoneRequest::_internal_context_id() const {
  return _impl_.context_id_;
}
inline uint64_t WaitQueueDoneRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.WaitQueueDoneRequest.context_id)
  return _internal_context_id();
}
inline void WaitQueueDoneRequest::_internal_set_context_id(uint64_t value) {
  
  _impl_.context_id_ = value;
}
inline void WaitQueueDoneRequest::set_context_id(uint64_t value) {
  _internal_set_context_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.WaitQueueDoneRequest.context_id)
}

// repeated int64 op_id = 2;
inline int WaitQueueDoneRequest::_internal_op_id_size() const {
  return _impl_.op_id_.size();
}
inline int WaitQueueDoneRequest::op_id_size() const {
  return _internal_op_id_size();
}
inline void WaitQueueDoneRequest::clear_op_id() {
  _impl_.op_id_.Clear();
}
inline int64_t WaitQueueDoneRequest::_internal_op_id(int index) const {
  return _impl_.op_id_.Get(index);
}
inline int64_t WaitQueueDoneRequest::op_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return _internal_op_id(index);
}
inline void WaitQueueDoneRequest::set_op_id(int index, int64_t value) {
  _impl_.op_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.WaitQueueDoneRequest.op_id)
}
inline void WaitQueueDoneRequest::_internal_add_op_id(int64_t value) {
  _impl_.op_id_.Add(value);
}
inline void WaitQueueDoneRequest::add_op_id(int64_t value) {
  _internal_add_op_id(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.WaitQueueDoneRequest.op_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
WaitQueueDoneRequest::_internal_op_id() const {
  return _impl_.op_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
WaitQueueDoneRequest::op_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return _internal_op_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
WaitQueueDoneRequest::_internal_mutable_op_id() {
  return &_impl_.op_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
WaitQueueDoneRequest::mutable_op_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.WaitQueueDoneRequest.op_id)
  return _internal_mutable_op_id();
}

// -------------------------------------------------------------------

// WaitQueueDoneResponse

// -------------------------------------------------------------------

// RunComponentFunctionRequest

// fixed64 context_id = 1;
inline void RunComponentFunctionRequest::clear_context_id() {
  _impl_.context_id_ = uint64_t{0u};
}
inline uint64_t RunComponentFunctionRequest::_internal_context_id() const {
  return _impl_.context_id_;
}
inline uint64_t RunComponentFunctionRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionRequest.context_id)
  return _internal_context_id();
}
inline void RunComponentFunctionRequest::_internal_set_context_id(uint64_t value) {
  
  _impl_.context_id_ = value;
}
inline void RunComponentFunctionRequest::set_context_id(uint64_t value) {
  _internal_set_context_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.RunComponentFunctionRequest.context_id)
}

// .tensorflow.eager.Operation operation = 2;
inline bool RunComponentFunctionRequest::_internal_has_operation() const {
  return this != internal_default_instance() && _impl_.operation_ != nullptr;
}
inline bool RunComponentFunctionRequest::has_operation() const {
  return _internal_has_operation();
}
inline void RunComponentFunctionRequest::clear_operation() {
  if (GetArenaForAllocation() == nullptr && _impl_.operation_ != nullptr) {
    delete _impl_.operation_;
  }
  _impl_.operation_ = nullptr;
}
inline const ::tensorflow::eager::Operation& RunComponentFunctionRequest::_internal_operation() const {
  const ::tensorflow::eager::Operation* p = _impl_.operation_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::eager::Operation&>(
      ::tensorflow::eager::_Operation_default_instance_);
}
inline const ::tensorflow::eager::Operation& RunComponentFunctionRequest::operation() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionRequest.operation)
  return _internal_operation();
}
inline void RunComponentFunctionRequest::unsafe_arena_set_allocated_operation(
    ::tensorflow::eager::Operation* operation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.operation_);
  }
  _impl_.operation_ = operation;
  if (operation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.RunComponentFunctionRequest.operation)
}
inline ::tensorflow::eager::Operation* RunComponentFunctionRequest::release_operation() {
  
  ::tensorflow::eager::Operation* temp = _impl_.operation_;
  _impl_.operation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::eager::Operation* RunComponentFunctionRequest::unsafe_arena_release_operation() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RunComponentFunctionRequest.operation)
  
  ::tensorflow::eager::Operation* temp = _impl_.operation_;
  _impl_.operation_ = nullptr;
  return temp;
}
inline ::tensorflow::eager::Operation* RunComponentFunctionRequest::_internal_mutable_operation() {
  
  if (_impl_.operation_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::eager::Operation>(GetArenaForAllocation());
    _impl_.operation_ = p;
  }
  return _impl_.operation_;
}
inline ::tensorflow::eager::Operation* RunComponentFunctionRequest::mutable_operation() {
  ::tensorflow::eager::Operation* _msg = _internal_mutable_operation();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RunComponentFunctionRequest.operation)
  return _msg;
}
inline void RunComponentFunctionRequest::set_allocated_operation(::tensorflow::eager::Operation* operation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.operation_;
  }
  if (operation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(operation);
    if (message_arena != submessage_arena) {
      operation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, operation, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.operation_ = operation;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RunComponentFunctionRequest.operation)
}

// repeated int32 output_num = 3;
inline int RunComponentFunctionRequest::_internal_output_num_size() const {
  return _impl_.output_num_.size();
}
inline int RunComponentFunctionRequest::output_num_size() const {
  return _internal_output_num_size();
}
inline void RunComponentFunctionRequest::clear_output_num() {
  _impl_.output_num_.Clear();
}
inline int32_t RunComponentFunctionRequest::_internal_output_num(int index) const {
  return _impl_.output_num_.Get(index);
}
inline int32_t RunComponentFunctionRequest::output_num(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionRequest.output_num)
  return _internal_output_num(index);
}
inline void RunComponentFunctionRequest::set_output_num(int index, int32_t value) {
  _impl_.output_num_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.RunComponentFunctionRequest.output_num)
}
inline void RunComponentFunctionRequest::_internal_add_output_num(int32_t value) {
  _impl_.output_num_.Add(value);
}
inline void RunComponentFunctionRequest::add_output_num(int32_t value) {
  _internal_add_output_num(value);
  // @@protoc_insertion_point(field_add:tensorflow.eager.RunComponentFunctionRequest.output_num)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
RunComponentFunctionRequest::_internal_output_num() const {
  return _impl_.output_num_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
RunComponentFunctionRequest::output_num() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.RunComponentFunctionRequest.output_num)
  return _internal_output_num();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
RunComponentFunctionRequest::_internal_mutable_output_num() {
  return &_impl_.output_num_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
RunComponentFunctionRequest::mutable_output_num() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.RunComponentFunctionRequest.output_num)
  return _internal_mutable_output_num();
}

// -------------------------------------------------------------------

// RunComponentFunctionResponse

// repeated .tensorflow.TensorShapeProto shape = 1;
inline int RunComponentFunctionResponse::_internal_shape_size() const {
  return _impl_.shape_.size();
}
inline int RunComponentFunctionResponse::shape_size() const {
  return _internal_shape_size();
}
inline ::tensorflow::TensorShapeProto* RunComponentFunctionResponse::mutable_shape(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RunComponentFunctionResponse.shape)
  return _impl_.shape_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
RunComponentFunctionResponse::mutable_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.RunComponentFunctionResponse.shape)
  return &_impl_.shape_;
}
inline const ::tensorflow::TensorShapeProto& RunComponentFunctionResponse::_internal_shape(int index) const {
  return _impl_.shape_.Get(index);
}
inline const ::tensorflow::TensorShapeProto& RunComponentFunctionResponse::shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionResponse.shape)
  return _internal_shape(index);
}
inline ::tensorflow::TensorShapeProto* RunComponentFunctionResponse::_internal_add_shape() {
  return _impl_.shape_.Add();
}
inline ::tensorflow::TensorShapeProto* RunComponentFunctionResponse::add_shape() {
  ::tensorflow::TensorShapeProto* _add = _internal_add_shape();
  // @@protoc_insertion_point(field_add:tensorflow.eager.RunComponentFunctionResponse.shape)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
RunComponentFunctionResponse::shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.RunComponentFunctionResponse.shape)
  return _impl_.shape_;
}

// repeated .tensorflow.TensorProto tensor = 2;
inline int RunComponentFunctionResponse::_internal_tensor_size() const {
  return _impl_.tensor_.size();
}
inline int RunComponentFunctionResponse::tensor_size() const {
  return _internal_tensor_size();
}
inline ::tensorflow::TensorProto* RunComponentFunctionResponse::mutable_tensor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return _impl_.tensor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
RunComponentFunctionResponse::mutable_tensor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return &_impl_.tensor_;
}
inline const ::tensorflow::TensorProto& RunComponentFunctionResponse::_internal_tensor(int index) const {
  return _impl_.tensor_.Get(index);
}
inline const ::tensorflow::TensorProto& RunComponentFunctionResponse::tensor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return _internal_tensor(index);
}
inline ::tensorflow::TensorProto* RunComponentFunctionResponse::_internal_add_tensor() {
  return _impl_.tensor_.Add();
}
inline ::tensorflow::TensorProto* RunComponentFunctionResponse::add_tensor() {
  ::tensorflow::TensorProto* _add = _internal_add_tensor();
  // @@protoc_insertion_point(field_add:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
RunComponentFunctionResponse::tensor() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.RunComponentFunctionResponse.tensor)
  return _impl_.tensor_;
}

// -------------------------------------------------------------------

// KeepAliveRequest

// fixed64 context_id = 1;
inline void KeepAliveRequest::clear_context_id() {
  _impl_.context_id_ = uint64_t{0u};
}
inline uint64_t KeepAliveRequest::_internal_context_id() const {
  return _impl_.context_id_;
}
inline uint64_t KeepAliveRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.KeepAliveRequest.context_id)
  return _internal_context_id();
}
inline void KeepAliveRequest::_internal_set_context_id(uint64_t value) {
  
  _impl_.context_id_ = value;
}
inline void KeepAliveRequest::set_context_id(uint64_t value) {
  _internal_set_context_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.KeepAliveRequest.context_id)
}

// -------------------------------------------------------------------

// KeepAliveResponse

// fixed64 context_view_id = 1;
inline void KeepAliveResponse::clear_context_view_id() {
  _impl_.context_view_id_ = uint64_t{0u};
}
inline uint64_t KeepAliveResponse::_internal_context_view_id() const {
  return _impl_.context_view_id_;
}
inline uint64_t KeepAliveResponse::context_view_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.KeepAliveResponse.context_view_id)
  return _internal_context_view_id();
}
inline void KeepAliveResponse::_internal_set_context_view_id(uint64_t value) {
  
  _impl_.context_view_id_ = value;
}
inline void KeepAliveResponse::set_context_view_id(uint64_t value) {
  _internal_set_context_view_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.KeepAliveResponse.context_view_id)
}

// -------------------------------------------------------------------

// CloseContextRequest

// fixed64 context_id = 1;
inline void CloseContextRequest::clear_context_id() {
  _impl_.context_id_ = uint64_t{0u};
}
inline uint64_t CloseContextRequest::_internal_context_id() const {
  return _impl_.context_id_;
}
inline uint64_t CloseContextRequest::context_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CloseContextRequest.context_id)
  return _internal_context_id();
}
inline void CloseContextRequest::_internal_set_context_id(uint64_t value) {
  
  _impl_.context_id_ = value;
}
inline void CloseContextRequest::set_context_id(uint64_t value) {
  _internal_set_context_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CloseContextRequest.context_id)
}

// fixed64 context_view_id = 2;
inline void CloseContextRequest::clear_context_view_id() {
  _impl_.context_view_id_ = uint64_t{0u};
}
inline uint64_t CloseContextRequest::_internal_context_view_id() const {
  return _impl_.context_view_id_;
}
inline uint64_t CloseContextRequest::context_view_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CloseContextRequest.context_view_id)
  return _internal_context_view_id();
}
inline void CloseContextRequest::_internal_set_context_view_id(uint64_t value) {
  
  _impl_.context_view_id_ = value;
}
inline void CloseContextRequest::set_context_view_id(uint64_t value) {
  _internal_set_context_view_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CloseContextRequest.context_view_id)
}

// -------------------------------------------------------------------

// CloseContextResponse

// -------------------------------------------------------------------

// RegisterFunctionOp

// .tensorflow.FunctionDef function_def = 1;
inline bool RegisterFunctionOp::_internal_has_function_def() const {
  return this != internal_default_instance() && _impl_.function_def_ != nullptr;
}
inline bool RegisterFunctionOp::has_function_def() const {
  return _internal_has_function_def();
}
inline const ::tensorflow::FunctionDef& RegisterFunctionOp::_internal_function_def() const {
  const ::tensorflow::FunctionDef* p = _impl_.function_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::FunctionDef&>(
      ::tensorflow::_FunctionDef_default_instance_);
}
inline const ::tensorflow::FunctionDef& RegisterFunctionOp::function_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RegisterFunctionOp.function_def)
  return _internal_function_def();
}
inline void RegisterFunctionOp::unsafe_arena_set_allocated_function_def(
    ::tensorflow::FunctionDef* function_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.function_def_);
  }
  _impl_.function_def_ = function_def;
  if (function_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.RegisterFunctionOp.function_def)
}
inline ::tensorflow::FunctionDef* RegisterFunctionOp::release_function_def() {
  
  ::tensorflow::FunctionDef* temp = _impl_.function_def_;
  _impl_.function_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::FunctionDef* RegisterFunctionOp::unsafe_arena_release_function_def() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RegisterFunctionOp.function_def)
  
  ::tensorflow::FunctionDef* temp = _impl_.function_def_;
  _impl_.function_def_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionDef* RegisterFunctionOp::_internal_mutable_function_def() {
  
  if (_impl_.function_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionDef>(GetArenaForAllocation());
    _impl_.function_def_ = p;
  }
  return _impl_.function_def_;
}
inline ::tensorflow::FunctionDef* RegisterFunctionOp::mutable_function_def() {
  ::tensorflow::FunctionDef* _msg = _internal_mutable_function_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RegisterFunctionOp.function_def)
  return _msg;
}
inline void RegisterFunctionOp::set_allocated_function_def(::tensorflow::FunctionDef* function_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.function_def_);
  }
  if (function_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(function_def));
    if (message_arena != submessage_arena) {
      function_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, function_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.function_def_ = function_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RegisterFunctionOp.function_def)
}

// bool is_component_function = 2;
inline void RegisterFunctionOp::clear_is_component_function() {
  _impl_.is_component_function_ = false;
}
inline bool RegisterFunctionOp::_internal_is_component_function() const {
  return _impl_.is_component_function_;
}
inline bool RegisterFunctionOp::is_component_function() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RegisterFunctionOp.is_component_function)
  return _internal_is_component_function();
}
inline void RegisterFunctionOp::_internal_set_is_component_function(bool value) {
  
  _impl_.is_component_function_ = value;
}
inline void RegisterFunctionOp::set_is_component_function(bool value) {
  _internal_set_is_component_function(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.RegisterFunctionOp.is_component_function)
}

// .tensorflow.FunctionDefLibrary library = 3;
inline bool RegisterFunctionOp::_internal_has_library() const {
  return this != internal_default_instance() && _impl_.library_ != nullptr;
}
inline bool RegisterFunctionOp::has_library() const {
  return _internal_has_library();
}
inline const ::tensorflow::FunctionDefLibrary& RegisterFunctionOp::_internal_library() const {
  const ::tensorflow::FunctionDefLibrary* p = _impl_.library_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::FunctionDefLibrary&>(
      ::tensorflow::_FunctionDefLibrary_default_instance_);
}
inline const ::tensorflow::FunctionDefLibrary& RegisterFunctionOp::library() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RegisterFunctionOp.library)
  return _internal_library();
}
inline void RegisterFunctionOp::unsafe_arena_set_allocated_library(
    ::tensorflow::FunctionDefLibrary* library) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.library_);
  }
  _impl_.library_ = library;
  if (library) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.RegisterFunctionOp.library)
}
inline ::tensorflow::FunctionDefLibrary* RegisterFunctionOp::release_library() {
  
  ::tensorflow::FunctionDefLibrary* temp = _impl_.library_;
  _impl_.library_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::FunctionDefLibrary* RegisterFunctionOp::unsafe_arena_release_library() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RegisterFunctionOp.library)
  
  ::tensorflow::FunctionDefLibrary* temp = _impl_.library_;
  _impl_.library_ = nullptr;
  return temp;
}
inline ::tensorflow::FunctionDefLibrary* RegisterFunctionOp::_internal_mutable_library() {
  
  if (_impl_.library_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FunctionDefLibrary>(GetArenaForAllocation());
    _impl_.library_ = p;
  }
  return _impl_.library_;
}
inline ::tensorflow::FunctionDefLibrary* RegisterFunctionOp::mutable_library() {
  ::tensorflow::FunctionDefLibrary* _msg = _internal_mutable_library();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RegisterFunctionOp.library)
  return _msg;
}
inline void RegisterFunctionOp::set_allocated_library(::tensorflow::FunctionDefLibrary* library) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.library_);
  }
  if (library) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(library));
    if (message_arena != submessage_arena) {
      library = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, library, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.library_ = library;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RegisterFunctionOp.library)
}

// -------------------------------------------------------------------

// RemoveFunctionOp

// string function_name = 1;
inline void RemoveFunctionOp::clear_function_name() {
  _impl_.function_name_.ClearToEmpty();
}
inline const std::string& RemoveFunctionOp::function_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoveFunctionOp.function_name)
  return _internal_function_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RemoveFunctionOp::set_function_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.function_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.eager.RemoveFunctionOp.function_name)
}
inline std::string* RemoveFunctionOp::mutable_function_name() {
  std::string* _s = _internal_mutable_function_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RemoveFunctionOp.function_name)
  return _s;
}
inline const std::string& RemoveFunctionOp::_internal_function_name() const {
  return _impl_.function_name_.Get();
}
inline void RemoveFunctionOp::_internal_set_function_name(const std::string& value) {
  
  _impl_.function_name_.Set(value, GetArenaForAllocation());
}
inline std::string* RemoveFunctionOp::_internal_mutable_function_name() {
  
  return _impl_.function_name_.Mutable(GetArenaForAllocation());
}
inline std::string* RemoveFunctionOp::release_function_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RemoveFunctionOp.function_name)
  return _impl_.function_name_.Release();
}
inline void RemoveFunctionOp::set_allocated_function_name(std::string* function_name) {
  if (function_name != nullptr) {
    
  } else {
    
  }
  _impl_.function_name_.SetAllocated(function_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.function_name_.IsDefault()) {
    _impl_.function_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RemoveFunctionOp.function_name)
}

// -------------------------------------------------------------------

// CleanupFunctionOp

// int64 step_id = 1;
inline void CleanupFunctionOp::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t CleanupFunctionOp::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t CleanupFunctionOp::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.CleanupFunctionOp.step_id)
  return _internal_step_id();
}
inline void CleanupFunctionOp::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void CleanupFunctionOp::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.CleanupFunctionOp.step_id)
}

// -------------------------------------------------------------------

// SyncRemoteExecutorForStream

// -------------------------------------------------------------------

// SendTensorOp

// int64 op_id = 1;
inline void SendTensorOp::clear_op_id() {
  _impl_.op_id_ = int64_t{0};
}
inline int64_t SendTensorOp::_internal_op_id() const {
  return _impl_.op_id_;
}
inline int64_t SendTensorOp::op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorOp.op_id)
  return _internal_op_id();
}
inline void SendTensorOp::_internal_set_op_id(int64_t value) {
  
  _impl_.op_id_ = value;
}
inline void SendTensorOp::set_op_id(int64_t value) {
  _internal_set_op_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendTensorOp.op_id)
}

// repeated .tensorflow.TensorProto tensors = 2;
inline int SendTensorOp::_internal_tensors_size() const {
  return _impl_.tensors_.size();
}
inline int SendTensorOp::tensors_size() const {
  return _internal_tensors_size();
}
inline ::tensorflow::TensorProto* SendTensorOp::mutable_tensors(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendTensorOp.tensors)
  return _impl_.tensors_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >*
SendTensorOp::mutable_tensors() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.SendTensorOp.tensors)
  return &_impl_.tensors_;
}
inline const ::tensorflow::TensorProto& SendTensorOp::_internal_tensors(int index) const {
  return _impl_.tensors_.Get(index);
}
inline const ::tensorflow::TensorProto& SendTensorOp::tensors(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorOp.tensors)
  return _internal_tensors(index);
}
inline ::tensorflow::TensorProto* SendTensorOp::_internal_add_tensors() {
  return _impl_.tensors_.Add();
}
inline ::tensorflow::TensorProto* SendTensorOp::add_tensors() {
  ::tensorflow::TensorProto* _add = _internal_add_tensors();
  // @@protoc_insertion_point(field_add:tensorflow.eager.SendTensorOp.tensors)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorProto >&
SendTensorOp::tensors() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.SendTensorOp.tensors)
  return _impl_.tensors_;
}

// string device_name = 3;
inline void SendTensorOp::clear_device_name() {
  _impl_.device_name_.ClearToEmpty();
}
inline const std::string& SendTensorOp::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendTensorOp.device_name)
  return _internal_device_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SendTensorOp::set_device_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendTensorOp.device_name)
}
inline std::string* SendTensorOp::mutable_device_name() {
  std::string* _s = _internal_mutable_device_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendTensorOp.device_name)
  return _s;
}
inline const std::string& SendTensorOp::_internal_device_name() const {
  return _impl_.device_name_.Get();
}
inline void SendTensorOp::_internal_set_device_name(const std::string& value) {
  
  _impl_.device_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SendTensorOp::_internal_mutable_device_name() {
  
  return _impl_.device_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SendTensorOp::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendTensorOp.device_name)
  return _impl_.device_name_.Release();
}
inline void SendTensorOp::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  _impl_.device_name_.SetAllocated(device_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_name_.IsDefault()) {
    _impl_.device_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendTensorOp.device_name)
}

// -------------------------------------------------------------------

// SendPackedHandleOp_LocalTensorHandle

// .tensorflow.TensorProto tensor = 1;
inline bool SendPackedHandleOp_LocalTensorHandle::_internal_has_tensor() const {
  return this != internal_default_instance() && _impl_.tensor_ != nullptr;
}
inline bool SendPackedHandleOp_LocalTensorHandle::has_tensor() const {
  return _internal_has_tensor();
}
inline const ::tensorflow::TensorProto& SendPackedHandleOp_LocalTensorHandle::_internal_tensor() const {
  const ::tensorflow::TensorProto* p = _impl_.tensor_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& SendPackedHandleOp_LocalTensorHandle::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
  return _internal_tensor();
}
inline void SendPackedHandleOp_LocalTensorHandle::unsafe_arena_set_allocated_tensor(
    ::tensorflow::TensorProto* tensor) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  _impl_.tensor_ = tensor;
  if (tensor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
}
inline ::tensorflow::TensorProto* SendPackedHandleOp_LocalTensorHandle::release_tensor() {
  
  ::tensorflow::TensorProto* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* SendPackedHandleOp_LocalTensorHandle::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
  
  ::tensorflow::TensorProto* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* SendPackedHandleOp_LocalTensorHandle::_internal_mutable_tensor() {
  
  if (_impl_.tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.tensor_ = p;
  }
  return _impl_.tensor_;
}
inline ::tensorflow::TensorProto* SendPackedHandleOp_LocalTensorHandle::mutable_tensor() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
  return _msg;
}
inline void SendPackedHandleOp_LocalTensorHandle::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor));
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.tensor)
}

// string device = 2;
inline void SendPackedHandleOp_LocalTensorHandle::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& SendPackedHandleOp_LocalTensorHandle::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SendPackedHandleOp_LocalTensorHandle::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
}
inline std::string* SendPackedHandleOp_LocalTensorHandle::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
  return _s;
}
inline const std::string& SendPackedHandleOp_LocalTensorHandle::_internal_device() const {
  return _impl_.device_.Get();
}
inline void SendPackedHandleOp_LocalTensorHandle::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* SendPackedHandleOp_LocalTensorHandle::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* SendPackedHandleOp_LocalTensorHandle::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
  return _impl_.device_.Release();
}
inline void SendPackedHandleOp_LocalTensorHandle::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendPackedHandleOp.LocalTensorHandle.device)
}

// -------------------------------------------------------------------

// SendPackedHandleOp_Handle

// .tensorflow.eager.SendPackedHandleOp.LocalTensorHandle local_handle = 1;
inline bool SendPackedHandleOp_Handle::_internal_has_local_handle() const {
  return item_case() == kLocalHandle;
}
inline bool SendPackedHandleOp_Handle::has_local_handle() const {
  return _internal_has_local_handle();
}
inline void SendPackedHandleOp_Handle::set_has_local_handle() {
  _impl_._oneof_case_[0] = kLocalHandle;
}
inline void SendPackedHandleOp_Handle::clear_local_handle() {
  if (_internal_has_local_handle()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.item_.local_handle_;
    }
    clear_has_item();
  }
}
inline ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* SendPackedHandleOp_Handle::release_local_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.Handle.local_handle)
  if (_internal_has_local_handle()) {
    clear_has_item();
    ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* temp = _impl_.item_.local_handle_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.local_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle& SendPackedHandleOp_Handle::_internal_local_handle() const {
  return _internal_has_local_handle()
      ? *_impl_.item_.local_handle_
      : reinterpret_cast< ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle&>(::tensorflow::eager::_SendPackedHandleOp_LocalTensorHandle_default_instance_);
}
inline const ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle& SendPackedHandleOp_Handle::local_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.Handle.local_handle)
  return _internal_local_handle();
}
inline ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* SendPackedHandleOp_Handle::unsafe_arena_release_local_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.SendPackedHandleOp.Handle.local_handle)
  if (_internal_has_local_handle()) {
    clear_has_item();
    ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* temp = _impl_.item_.local_handle_;
    _impl_.item_.local_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SendPackedHandleOp_Handle::unsafe_arena_set_allocated_local_handle(::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* local_handle) {
  clear_item();
  if (local_handle) {
    set_has_local_handle();
    _impl_.item_.local_handle_ = local_handle;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.SendPackedHandleOp.Handle.local_handle)
}
inline ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* SendPackedHandleOp_Handle::_internal_mutable_local_handle() {
  if (!_internal_has_local_handle()) {
    clear_item();
    set_has_local_handle();
    _impl_.item_.local_handle_ = CreateMaybeMessage< ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle >(GetArenaForAllocation());
  }
  return _impl_.item_.local_handle_;
}
inline ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* SendPackedHandleOp_Handle::mutable_local_handle() {
  ::tensorflow::eager::SendPackedHandleOp_LocalTensorHandle* _msg = _internal_mutable_local_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.Handle.local_handle)
  return _msg;
}

// .tensorflow.eager.RemoteTensorHandle remote_handle = 2;
inline bool SendPackedHandleOp_Handle::_internal_has_remote_handle() const {
  return item_case() == kRemoteHandle;
}
inline bool SendPackedHandleOp_Handle::has_remote_handle() const {
  return _internal_has_remote_handle();
}
inline void SendPackedHandleOp_Handle::set_has_remote_handle() {
  _impl_._oneof_case_[0] = kRemoteHandle;
}
inline ::tensorflow::eager::RemoteTensorHandle* SendPackedHandleOp_Handle::release_remote_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.Handle.remote_handle)
  if (_internal_has_remote_handle()) {
    clear_has_item();
    ::tensorflow::eager::RemoteTensorHandle* temp = _impl_.item_.remote_handle_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.item_.remote_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::eager::RemoteTensorHandle& SendPackedHandleOp_Handle::_internal_remote_handle() const {
  return _internal_has_remote_handle()
      ? *_impl_.item_.remote_handle_
      : reinterpret_cast< ::tensorflow::eager::RemoteTensorHandle&>(::tensorflow::eager::_RemoteTensorHandle_default_instance_);
}
inline const ::tensorflow::eager::RemoteTensorHandle& SendPackedHandleOp_Handle::remote_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.Handle.remote_handle)
  return _internal_remote_handle();
}
inline ::tensorflow::eager::RemoteTensorHandle* SendPackedHandleOp_Handle::unsafe_arena_release_remote_handle() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.eager.SendPackedHandleOp.Handle.remote_handle)
  if (_internal_has_remote_handle()) {
    clear_has_item();
    ::tensorflow::eager::RemoteTensorHandle* temp = _impl_.item_.remote_handle_;
    _impl_.item_.remote_handle_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void SendPackedHandleOp_Handle::unsafe_arena_set_allocated_remote_handle(::tensorflow::eager::RemoteTensorHandle* remote_handle) {
  clear_item();
  if (remote_handle) {
    set_has_remote_handle();
    _impl_.item_.remote_handle_ = remote_handle;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.SendPackedHandleOp.Handle.remote_handle)
}
inline ::tensorflow::eager::RemoteTensorHandle* SendPackedHandleOp_Handle::_internal_mutable_remote_handle() {
  if (!_internal_has_remote_handle()) {
    clear_item();
    set_has_remote_handle();
    _impl_.item_.remote_handle_ = CreateMaybeMessage< ::tensorflow::eager::RemoteTensorHandle >(GetArenaForAllocation());
  }
  return _impl_.item_.remote_handle_;
}
inline ::tensorflow::eager::RemoteTensorHandle* SendPackedHandleOp_Handle::mutable_remote_handle() {
  ::tensorflow::eager::RemoteTensorHandle* _msg = _internal_mutable_remote_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.Handle.remote_handle)
  return _msg;
}

inline bool SendPackedHandleOp_Handle::has_item() const {
  return item_case() != ITEM_NOT_SET;
}
inline void SendPackedHandleOp_Handle::clear_has_item() {
  _impl_._oneof_case_[0] = ITEM_NOT_SET;
}
inline SendPackedHandleOp_Handle::ItemCase SendPackedHandleOp_Handle::item_case() const {
  return SendPackedHandleOp_Handle::ItemCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// SendPackedHandleOp

// int64 op_id = 1;
inline void SendPackedHandleOp::clear_op_id() {
  _impl_.op_id_ = int64_t{0};
}
inline int64_t SendPackedHandleOp::_internal_op_id() const {
  return _impl_.op_id_;
}
inline int64_t SendPackedHandleOp::op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.op_id)
  return _internal_op_id();
}
inline void SendPackedHandleOp::_internal_set_op_id(int64_t value) {
  
  _impl_.op_id_ = value;
}
inline void SendPackedHandleOp::set_op_id(int64_t value) {
  _internal_set_op_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendPackedHandleOp.op_id)
}

// repeated .tensorflow.eager.SendPackedHandleOp.Handle handles = 2;
inline int SendPackedHandleOp::_internal_handles_size() const {
  return _impl_.handles_.size();
}
inline int SendPackedHandleOp::handles_size() const {
  return _internal_handles_size();
}
inline void SendPackedHandleOp::clear_handles() {
  _impl_.handles_.Clear();
}
inline ::tensorflow::eager::SendPackedHandleOp_Handle* SendPackedHandleOp::mutable_handles(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.handles)
  return _impl_.handles_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle >*
SendPackedHandleOp::mutable_handles() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.SendPackedHandleOp.handles)
  return &_impl_.handles_;
}
inline const ::tensorflow::eager::SendPackedHandleOp_Handle& SendPackedHandleOp::_internal_handles(int index) const {
  return _impl_.handles_.Get(index);
}
inline const ::tensorflow::eager::SendPackedHandleOp_Handle& SendPackedHandleOp::handles(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.handles)
  return _internal_handles(index);
}
inline ::tensorflow::eager::SendPackedHandleOp_Handle* SendPackedHandleOp::_internal_add_handles() {
  return _impl_.handles_.Add();
}
inline ::tensorflow::eager::SendPackedHandleOp_Handle* SendPackedHandleOp::add_handles() {
  ::tensorflow::eager::SendPackedHandleOp_Handle* _add = _internal_add_handles();
  // @@protoc_insertion_point(field_add:tensorflow.eager.SendPackedHandleOp.handles)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::SendPackedHandleOp_Handle >&
SendPackedHandleOp::handles() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.SendPackedHandleOp.handles)
  return _impl_.handles_;
}

// string device_name = 3;
inline void SendPackedHandleOp::clear_device_name() {
  _impl_.device_name_.ClearToEmpty();
}
inline const std::string& SendPackedHandleOp::device_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.SendPackedHandleOp.device_name)
  return _internal_device_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SendPackedHandleOp::set_device_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.eager.SendPackedHandleOp.device_name)
}
inline std::string* SendPackedHandleOp::mutable_device_name() {
  std::string* _s = _internal_mutable_device_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.SendPackedHandleOp.device_name)
  return _s;
}
inline const std::string& SendPackedHandleOp::_internal_device_name() const {
  return _impl_.device_name_.Get();
}
inline void SendPackedHandleOp::_internal_set_device_name(const std::string& value) {
  
  _impl_.device_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SendPackedHandleOp::_internal_mutable_device_name() {
  
  return _impl_.device_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SendPackedHandleOp::release_device_name() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.SendPackedHandleOp.device_name)
  return _impl_.device_name_.Release();
}
inline void SendPackedHandleOp::set_allocated_device_name(std::string* device_name) {
  if (device_name != nullptr) {
    
  } else {
    
  }
  _impl_.device_name_.SetAllocated(device_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_name_.IsDefault()) {
    _impl_.device_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.SendPackedHandleOp.device_name)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace eager
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto
