************************************* Invoked: Thu Jul 31 12:57:38 2025
[0] Arguments: D:\浏览器下载\msys2-x86_64-20250622.exe
[3] Operations sanity check succeeded.
[6] Using metadata cache from "C:/Users/<USER>/AppData/Local/cache\\qt-installer-framework\\d75f1c19-3379-3717-ae8d-1404b51494a9"
[6] Found 0 cached items.
[6] Language: zh-Hans-<PERSON><PERSON>
[58] Loaded control script ":/metadata/installer-config/control_js"
[58] Using control script: ":/metadata/installer-config/control_js"
[2009] Loading component scripts...
[50803] backup  operation: Mkdir
[50803] 	- arguments: D:/RNNoise/rnnoise-main/src/env
[50803] Done
[50803] perform  operation: Mkdir
[50803] 	- arguments: D:/RNNoise/rnnoise-main/src/env
[50803] Done
[50804] Preparing the installation...
[50804] Install size: 1 components
[50808] Preparing to unpack components...
[50808] backup com.msys2.root concurrent operation: Extract
[50808] 	- arguments: installer://com.msys2.root/20250622msys64.7z, D:/RNNoise/rnnoise-main/src/env
[50808] Started
[50954] Unpacking components...
[50955] perform com.msys2.root concurrent operation: Extract
[50955] 	- arguments: installer://com.msys2.root/20250622msys64.7z, D:/RNNoise/rnnoise-main/src/env
[50955] Started
[65869] Installing component MSYS2
[65870] backup com.msys2.root operation: CreateShortcut
[65870] 	- arguments: D:/RNNoise/rnnoise-main/src/env/mingw64.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 MINGW64.lnk, iconPath=D:/RNNoise/rnnoise-main/src/env/mingw64.exe
[65870] Done
[65871] perform com.msys2.root operation: CreateShortcut
[65871] 	- arguments: D:/RNNoise/rnnoise-main/src/env/mingw64.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 MINGW64.lnk
[65888] Done
[65888] backup com.msys2.root operation: CreateShortcut
[65888] 	- arguments: D:/RNNoise/rnnoise-main/src/env/ucrt64.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 UCRT64.lnk, iconPath=D:/RNNoise/rnnoise-main/src/env/ucrt64.exe
[65889] Done
[65889] perform com.msys2.root operation: CreateShortcut
[65889] 	- arguments: D:/RNNoise/rnnoise-main/src/env/ucrt64.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 UCRT64.lnk
[65896] Done
[65896] backup com.msys2.root operation: CreateShortcut
[65896] 	- arguments: D:/RNNoise/rnnoise-main/src/env/clang64.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 CLANG64.lnk, iconPath=D:/RNNoise/rnnoise-main/src/env/clang64.exe
[65896] Done
[65896] perform com.msys2.root operation: CreateShortcut
[65896] 	- arguments: D:/RNNoise/rnnoise-main/src/env/clang64.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 CLANG64.lnk
[65903] Done
[65903] backup com.msys2.root operation: CreateShortcut
[65903] 	- arguments: D:/RNNoise/rnnoise-main/src/env/clangarm64.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 CLANGARM64.lnk, iconPath=D:/RNNoise/rnnoise-main/src/env/clangarm64.exe
[65903] Done
[65903] perform com.msys2.root operation: CreateShortcut
[65903] 	- arguments: D:/RNNoise/rnnoise-main/src/env/clangarm64.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 CLANGARM64.lnk
[65910] Done
[65910] backup com.msys2.root operation: CreateShortcut
[65910] 	- arguments: D:/RNNoise/rnnoise-main/src/env/msys2.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 MSYS.lnk, iconPath=D:/RNNoise/rnnoise-main/src/env/msys2.exe
[65910] Done
[65910] perform com.msys2.root operation: CreateShortcut
[65910] 	- arguments: D:/RNNoise/rnnoise-main/src/env/msys2.exe, C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Cursor/MSYS2 MSYS.lnk
[65916] Done
[65916] backup com.msys2.root operation: Execute
[65917] 	- arguments: D:/RNNoise/rnnoise-main/src/env\usr\bin\bash.exe, --login, -c, exit
[65917] Done
[65917] perform com.msys2.root operation: Execute
[65917] 	- arguments: D:/RNNoise/rnnoise-main/src/env\usr\bin\bash.exe, --login, -c, exit
[65992] D:/RNNoise/rnnoise-main/src/env\\usr\\bin\\bash.exe" started, arguments: "--login -c exit
[66556] Copying skeleton files.\nThese files are for the users to personalise their msys2 experience.\n\nThey will never be overwritten nor automatically updated.\n\n
[66741] './.bashrc' -> '/home/<USER>/.bashrc'\n
[66763] './.bash_profile' -> '/home/<USER>/.bash_profile'\n
[66787] './.profile' -> '/home/<USER>/.profile'\n
[66969] 'C:\\Windows\\system32\\drivers\\etc\\hosts' -> '/etc/hosts'\n
[67062] 'C:\\Windows\\system32\\drivers\\etc\\protocol' -> '/etc/protocols'\n
[67144] 'C:\\Windows\\system32\\drivers\\etc\\services' -> '/etc/services'\n
[67195] 'C:\\Windows\\system32\\drivers\\etc\\networks' -> '/etc/networks'\n
[67894] ==> Generating pacman master key. This may take some time.\n
[69905] ==> Updating trust database...\n
[70098] ==> Appending keys from msys2.gpg...\n
[70762] ==> Locally signing trusted keys in keyring...\n
[71396]   -> Locally signed 5 keys.\n
[71409] ==> Importing owner trust values...\n
[71466] ==> Disabling revoked keys in keyring...\n
[71733]   -> Disabled 4 keys.\n
[71747] ==> Updating trust database...\n
[268106] Warning: [1;32mMSYS2 is starting for the first time. Executing the initial setup.[1;0m
gpg: /etc/pacman.d/gnupg/trustdb.gpg: trustdb created
gpg: no ultimately trusted keys found
gpg: starting migration from earlier GnuPG versions
gpg: porting secret keys from '/etc/pacman.d/gnupg/secring.gpg' to gpg-agent
gpg: migration succeeded
gpg: Generating pacman keyring master key...
gpg: directory '/etc/pacman.d/gnupg/openpgp-revocs.d' created
gpg: revocation certificate stored as '/etc/pacman.d/gnupg/openpgp-revocs.d/87C2C116810087E7E19454A14C071569795CE2A4.rev'
gpg: Done
gpg: marginals needed: 3  completes needed: 1  trust model: pgp
gpg: depth: 0  valid:   1  signed:   0  trust: 0-, 0q, 0n, 0m, 0f, 1u
gpg: setting ownertrust to 4
gpg: setting ownertrust to 4
gpg: setting ownertrust to 4
gpg: setting ownertrust to 4
gpg: setting ownertrust to 4
gpg: marginals needed: 3  completes needed: 1  trust model: pgp
gpg: depth: 0  valid:   1  signed:   5  trust: 0-, 0q, 0n, 0m, 0f, 1u
gpg: depth: 1  valid:   5  signed:   7  trust: 0-, 0q, 0n, 5m, 0f, 0u
gpg: depth: 2  valid:   4  signed:   2  trust: 4-, 0q, 0n, 0m, 0f, 0u
gpg: next trustdb check due at 2025-12-16
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key F40D263ECA25678A: "Alexey Pavlov (Alexpux) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key 790AE56A1D3CFDDC: "David Macek (MSYS2 master key) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key DA7EF2ABAEEA755C: "Martell Malone (martell) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key 755B8182ACD22879: "Christoph Reiter (MSYS2 master key) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key 9F418C233E652008: "Ignacio Casal Quinteiro <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key BBE514E53E0D0813: "Ray Donnelly (MSYS2 Developer - master key) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key 5F92EFC1A47D45A1: "Alexey Pavlov (Alexpux) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key 974C8BE49078F532: "David Macek <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key FA11531AA0AA7F57: "Christoph Reiter (MSYS2 development key) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Unknown host
gpg: error reading key: Unknown host
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key 794DCF97F93FC717: "Martell Malone (martell) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key D595C9AB2C51581E: "Martell Malone (MSYS2 Developer) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
gpg: error retrieving '<EMAIL>' via WKD: Connection timed out
gpg: error reading key: Connection timed out
gpg: refreshing 1 key from hkps://keyserver.ubuntu.com
gpg: key 4DF3B7664CA56930: "Ray Donnelly (MSYS2 Developer) <<EMAIL>>" not changed
gpg: Total number processed: 1
gpg:              unchanged: 1
[1;32mInitial setup complete. MSYS2 is now ready to use.[1;0m

[268106] Done
[268110] Writing maintenance tool: "D:/RNNoise/rnnoise-main/src/env/uninstall.exe.new"
[268110] Writing maintenance tool.
[268168] Wrote permissions for maintenance tool.
[268477] Maintenance tool hard restart: false.
[268480] Installation finished!
[272126] starting "D:/RNNoise/rnnoise-main/src/env/ucrt64.exe" QList()
