.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ext_export_subject_key_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ext_export_subject_key_id \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ext_export_subject_key_id(const gnutls_datum_t * " id ", gnutls_datum_t * " ext ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * id" 12
The key identifier
.IP "gnutls_datum_t * ext" 12
The DER\-encoded extension data; must be freed using \fBgnutls_free()\fP.
.SH "DESCRIPTION"
This function will convert the provided key identifier to a
DER\-encoded PKIX SubjectKeyIdentifier extension. 
The output data in  \fIext\fP will be allocated using
\fBgnutls_malloc()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a negative error value.
.SH "SINCE"
3.3.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
