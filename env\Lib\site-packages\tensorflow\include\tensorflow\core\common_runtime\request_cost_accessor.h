/* Copyright 2021 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_COMMON_RUNTIME_REQUEST_COST_ACCESSOR_H_
#define TENSORFLOW_CORE_COMMON_RUNTIME_REQUEST_COST_ACCESSOR_H_

#include <string>

#include "absl/container/flat_hash_map.h"
#include "absl/time/time.h"
#include "tensorflow/core/common_runtime/request_cost.h"

namespace tensorflow {

// An interface for accessing the RequestCost associated with the current rpc
// request.
class RequestCostAccessor {
 public:
  virtual ~RequestCostAccessor() {}
  virtual RequestCost* GetRequestCost() const = 0;
};

}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_COMMON_RUNTIME_REQUEST_COST_ACCESSOR_H_
