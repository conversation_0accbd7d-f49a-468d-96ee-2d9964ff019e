.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crt_get_activation_time" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crt_get_activation_time \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "time_t gnutls_x509_crt_get_activation_time(gnutls_x509_crt_t " cert ");"
.SH ARGUMENTS
.IP "gnutls_x509_crt_t cert" 12
should contain a \fBgnutls_x509_crt_t\fP type
.SH "DESCRIPTION"
This function will return the time this Certificate was or will be
activated.
.SH "RETURNS"
activation time, or (time_t)\-1 on error.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
