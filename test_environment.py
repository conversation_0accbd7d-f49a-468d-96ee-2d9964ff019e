#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试RNNoise项目环境是否正确安装
"""

def test_imports():
    """测试所有必需的包是否能正确导入"""
    print("正在测试Python环境...")
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__} - 导入成功")
    except ImportError as e:
        print(f"✗ NumPy 导入失败: {e}")
        return False
    
    try:
        import keras
        print(f"✓ Keras {keras.__version__} - 导入成功")
    except ImportError as e:
        print(f"✗ Keras 导入失败: {e}")
        return False
    
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow {tf.__version__} - 导入成功")
    except ImportError as e:
        print(f"✗ TensorFlow 导入失败: {e}")
        return False
    
    try:
        import h5py
        print(f"✓ h5py {h5py.__version__} - 导入成功")
    except ImportError as e:
        print(f"✗ h5py 导入失败: {e}")
        return False
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__} - 导入成功")
    except ImportError as e:
        print(f"✗ PyTorch 导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n正在测试基本功能...")
    
    try:
        import numpy as np
        # 测试numpy基本操作
        arr = np.array([1, 2, 3, 4, 5])
        result = np.mean(arr)
        print(f"✓ NumPy基本操作测试通过 (平均值: {result})")
        
        import torch
        # 测试torch基本操作
        tensor = torch.tensor([1.0, 2.0, 3.0])
        result = torch.mean(tensor)
        print(f"✓ PyTorch基本操作测试通过 (平均值: {result.item()})")
        
        import tensorflow as tf
        # 测试tensorflow基本操作
        tf_tensor = tf.constant([1.0, 2.0, 3.0])
        result = tf.reduce_mean(tf_tensor)
        print(f"✓ TensorFlow基本操作测试通过 (平均值: {result.numpy()})")
        
        return True
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("RNNoise项目环境测试")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试基本功能
        func_success = test_basic_functionality()
        
        if func_success:
            print("\n" + "=" * 50)
            print("🎉 所有测试通过！环境安装成功！")
            print("=" * 50)
            print("\n使用说明:")
            print("1. 激活环境: .\\env\\Scripts\\Activate.ps1")
            print("2. 运行训练脚本: python training/rnn_train.py")
            print("3. 或运行PyTorch版本: python torch/rnnoise/train_rnnoise.py")
        else:
            print("\n" + "=" * 50)
            print("❌ 功能测试失败，请检查安装")
            print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("❌ 导入测试失败，请检查依赖安装")
        print("=" * 50)
