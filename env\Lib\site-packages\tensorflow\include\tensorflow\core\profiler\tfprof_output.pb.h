// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/profiler/tfprof_output.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
namespace tensorflow {
namespace tfprof {
class AdviceProto;
struct AdviceProtoDefaultTypeInternal;
extern AdviceProtoDefaultTypeInternal _AdviceProto_default_instance_;
class AdviceProto_Checker;
struct AdviceProto_CheckerDefaultTypeInternal;
extern AdviceProto_CheckerDefaultTypeInternal _AdviceProto_Checker_default_instance_;
class AdviceProto_CheckersEntry_DoNotUse;
struct AdviceProto_CheckersEntry_DoNotUseDefaultTypeInternal;
extern AdviceProto_CheckersEntry_DoNotUseDefaultTypeInternal _AdviceProto_CheckersEntry_DoNotUse_default_instance_;
class GraphNodeProto;
struct GraphNodeProtoDefaultTypeInternal;
extern GraphNodeProtoDefaultTypeInternal _GraphNodeProto_default_instance_;
class GraphNodeProto_InputShapesEntry_DoNotUse;
struct GraphNodeProto_InputShapesEntry_DoNotUseDefaultTypeInternal;
extern GraphNodeProto_InputShapesEntry_DoNotUseDefaultTypeInternal _GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_;
class MultiGraphNodeProto;
struct MultiGraphNodeProtoDefaultTypeInternal;
extern MultiGraphNodeProtoDefaultTypeInternal _MultiGraphNodeProto_default_instance_;
class TFProfTensorProto;
struct TFProfTensorProtoDefaultTypeInternal;
extern TFProfTensorProtoDefaultTypeInternal _TFProfTensorProto_default_instance_;
}  // namespace tfprof
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tfprof::AdviceProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto>(Arena*);
template<> ::tensorflow::tfprof::AdviceProto_Checker* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto_Checker>(Arena*);
template<> ::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::AdviceProto_CheckersEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::GraphNodeProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::GraphNodeProto>(Arena*);
template<> ::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::tfprof::GraphNodeProto_InputShapesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::tfprof::MultiGraphNodeProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::MultiGraphNodeProto>(Arena*);
template<> ::tensorflow::tfprof::TFProfTensorProto* Arena::CreateMaybeMessage<::tensorflow::tfprof::TFProfTensorProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tfprof {

// ===================================================================

class TFProfTensorProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.TFProfTensorProto) */ {
 public:
  inline TFProfTensorProto() : TFProfTensorProto(nullptr) {}
  ~TFProfTensorProto() override;
  explicit PROTOBUF_CONSTEXPR TFProfTensorProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TFProfTensorProto(const TFProfTensorProto& from);
  TFProfTensorProto(TFProfTensorProto&& from) noexcept
    : TFProfTensorProto() {
    *this = ::std::move(from);
  }

  inline TFProfTensorProto& operator=(const TFProfTensorProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TFProfTensorProto& operator=(TFProfTensorProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TFProfTensorProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TFProfTensorProto* internal_default_instance() {
    return reinterpret_cast<const TFProfTensorProto*>(
               &_TFProfTensorProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TFProfTensorProto& a, TFProfTensorProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TFProfTensorProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TFProfTensorProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TFProfTensorProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TFProfTensorProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TFProfTensorProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TFProfTensorProto& from) {
    TFProfTensorProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TFProfTensorProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.TFProfTensorProto";
  }
  protected:
  explicit TFProfTensorProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueDoubleFieldNumber = 2,
    kValueInt64FieldNumber = 3,
    kValueStrFieldNumber = 4,
    kDtypeFieldNumber = 1,
  };
  // repeated double value_double = 2;
  int value_double_size() const;
  private:
  int _internal_value_double_size() const;
  public:
  void clear_value_double();
  private:
  double _internal_value_double(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_value_double() const;
  void _internal_add_value_double(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_value_double();
  public:
  double value_double(int index) const;
  void set_value_double(int index, double value);
  void add_value_double(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      value_double() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_value_double();

  // repeated int64 value_int64 = 3;
  int value_int64_size() const;
  private:
  int _internal_value_int64_size() const;
  public:
  void clear_value_int64();
  private:
  int64_t _internal_value_int64(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_value_int64() const;
  void _internal_add_value_int64(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_value_int64();
  public:
  int64_t value_int64(int index) const;
  void set_value_int64(int index, int64_t value);
  void add_value_int64(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      value_int64() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_value_int64();

  // repeated string value_str = 4;
  int value_str_size() const;
  private:
  int _internal_value_str_size() const;
  public:
  void clear_value_str();
  const std::string& value_str(int index) const;
  std::string* mutable_value_str(int index);
  void set_value_str(int index, const std::string& value);
  void set_value_str(int index, std::string&& value);
  void set_value_str(int index, const char* value);
  void set_value_str(int index, const char* value, size_t size);
  std::string* add_value_str();
  void add_value_str(const std::string& value);
  void add_value_str(std::string&& value);
  void add_value_str(const char* value);
  void add_value_str(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& value_str() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_value_str();
  private:
  const std::string& _internal_value_str(int index) const;
  std::string* _internal_add_value_str();
  public:

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.TFProfTensorProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > value_double_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > value_int64_;
    mutable std::atomic<int> _value_int64_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> value_str_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// -------------------------------------------------------------------

class GraphNodeProto_InputShapesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphNodeProto_InputShapesEntry_DoNotUse, 
    int32_t, ::tensorflow::TensorShapeProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GraphNodeProto_InputShapesEntry_DoNotUse, 
    int32_t, ::tensorflow::TensorShapeProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GraphNodeProto_InputShapesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR GraphNodeProto_InputShapesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GraphNodeProto_InputShapesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GraphNodeProto_InputShapesEntry_DoNotUse& other);
  static const GraphNodeProto_InputShapesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GraphNodeProto_InputShapesEntry_DoNotUse*>(&_GraphNodeProto_InputShapesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};

// -------------------------------------------------------------------

class GraphNodeProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.GraphNodeProto) */ {
 public:
  inline GraphNodeProto() : GraphNodeProto(nullptr) {}
  ~GraphNodeProto() override;
  explicit PROTOBUF_CONSTEXPR GraphNodeProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GraphNodeProto(const GraphNodeProto& from);
  GraphNodeProto(GraphNodeProto&& from) noexcept
    : GraphNodeProto() {
    *this = ::std::move(from);
  }

  inline GraphNodeProto& operator=(const GraphNodeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline GraphNodeProto& operator=(GraphNodeProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GraphNodeProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const GraphNodeProto* internal_default_instance() {
    return reinterpret_cast<const GraphNodeProto*>(
               &_GraphNodeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GraphNodeProto& a, GraphNodeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(GraphNodeProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GraphNodeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GraphNodeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GraphNodeProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GraphNodeProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GraphNodeProto& from) {
    GraphNodeProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GraphNodeProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.GraphNodeProto";
  }
  protected:
  explicit GraphNodeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kDevicesFieldNumber = 10,
    kShapesFieldNumber = 11,
    kChildrenFieldNumber = 12,
    kInputShapesFieldNumber = 16,
    kNameFieldNumber = 1,
    kTensorValueFieldNumber = 15,
    kExecMicrosFieldNumber = 2,
    kRequestedBytesFieldNumber = 3,
    kParametersFieldNumber = 4,
    kTotalExecMicrosFieldNumber = 6,
    kTotalRequestedBytesFieldNumber = 7,
    kTotalParametersFieldNumber = 8,
    kFloatOpsFieldNumber = 13,
    kTotalFloatOpsFieldNumber = 14,
    kAcceleratorExecMicrosFieldNumber = 17,
    kCpuExecMicrosFieldNumber = 18,
    kTotalAcceleratorExecMicrosFieldNumber = 19,
    kTotalCpuExecMicrosFieldNumber = 20,
    kRunCountFieldNumber = 21,
    kTotalRunCountFieldNumber = 22,
    kTotalDefinitionCountFieldNumber = 23,
    kPeakBytesFieldNumber = 24,
    kResidualBytesFieldNumber = 25,
    kOutputBytesFieldNumber = 26,
    kTotalPeakBytesFieldNumber = 27,
    kTotalResidualBytesFieldNumber = 28,
    kTotalOutputBytesFieldNumber = 29,
  };
  // repeated string devices = 10;
  int devices_size() const;
  private:
  int _internal_devices_size() const;
  public:
  void clear_devices();
  const std::string& devices(int index) const;
  std::string* mutable_devices(int index);
  void set_devices(int index, const std::string& value);
  void set_devices(int index, std::string&& value);
  void set_devices(int index, const char* value);
  void set_devices(int index, const char* value, size_t size);
  std::string* add_devices();
  void add_devices(const std::string& value);
  void add_devices(std::string&& value);
  void add_devices(const char* value);
  void add_devices(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& devices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_devices();
  private:
  const std::string& _internal_devices(int index) const;
  std::string* _internal_add_devices();
  public:

  // repeated .tensorflow.TensorShapeProto shapes = 11;
  int shapes_size() const;
  private:
  int _internal_shapes_size() const;
  public:
  void clear_shapes();
  ::tensorflow::TensorShapeProto* mutable_shapes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
      mutable_shapes();
  private:
  const ::tensorflow::TensorShapeProto& _internal_shapes(int index) const;
  ::tensorflow::TensorShapeProto* _internal_add_shapes();
  public:
  const ::tensorflow::TensorShapeProto& shapes(int index) const;
  ::tensorflow::TensorShapeProto* add_shapes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
      shapes() const;

  // repeated .tensorflow.tfprof.GraphNodeProto children = 12;
  int children_size() const;
  private:
  int _internal_children_size() const;
  public:
  void clear_children();
  ::tensorflow::tfprof::GraphNodeProto* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
      mutable_children();
  private:
  const ::tensorflow::tfprof::GraphNodeProto& _internal_children(int index) const;
  ::tensorflow::tfprof::GraphNodeProto* _internal_add_children();
  public:
  const ::tensorflow::tfprof::GraphNodeProto& children(int index) const;
  ::tensorflow::tfprof::GraphNodeProto* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
      children() const;

  // map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
  int input_shapes_size() const;
  private:
  int _internal_input_shapes_size() const;
  public:
  void clear_input_shapes();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TensorShapeProto >&
      _internal_input_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TensorShapeProto >*
      _internal_mutable_input_shapes();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TensorShapeProto >&
      input_shapes() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TensorShapeProto >*
      mutable_input_shapes();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
  bool has_tensor_value() const;
  private:
  bool _internal_has_tensor_value() const;
  public:
  void clear_tensor_value();
  const ::tensorflow::tfprof::TFProfTensorProto& tensor_value() const;
  PROTOBUF_NODISCARD ::tensorflow::tfprof::TFProfTensorProto* release_tensor_value();
  ::tensorflow::tfprof::TFProfTensorProto* mutable_tensor_value();
  void set_allocated_tensor_value(::tensorflow::tfprof::TFProfTensorProto* tensor_value);
  private:
  const ::tensorflow::tfprof::TFProfTensorProto& _internal_tensor_value() const;
  ::tensorflow::tfprof::TFProfTensorProto* _internal_mutable_tensor_value();
  public:
  void unsafe_arena_set_allocated_tensor_value(
      ::tensorflow::tfprof::TFProfTensorProto* tensor_value);
  ::tensorflow::tfprof::TFProfTensorProto* unsafe_arena_release_tensor_value();

  // int64 exec_micros = 2;
  void clear_exec_micros();
  int64_t exec_micros() const;
  void set_exec_micros(int64_t value);
  private:
  int64_t _internal_exec_micros() const;
  void _internal_set_exec_micros(int64_t value);
  public:

  // int64 requested_bytes = 3;
  void clear_requested_bytes();
  int64_t requested_bytes() const;
  void set_requested_bytes(int64_t value);
  private:
  int64_t _internal_requested_bytes() const;
  void _internal_set_requested_bytes(int64_t value);
  public:

  // int64 parameters = 4;
  void clear_parameters();
  int64_t parameters() const;
  void set_parameters(int64_t value);
  private:
  int64_t _internal_parameters() const;
  void _internal_set_parameters(int64_t value);
  public:

  // int64 total_exec_micros = 6;
  void clear_total_exec_micros();
  int64_t total_exec_micros() const;
  void set_total_exec_micros(int64_t value);
  private:
  int64_t _internal_total_exec_micros() const;
  void _internal_set_total_exec_micros(int64_t value);
  public:

  // int64 total_requested_bytes = 7;
  void clear_total_requested_bytes();
  int64_t total_requested_bytes() const;
  void set_total_requested_bytes(int64_t value);
  private:
  int64_t _internal_total_requested_bytes() const;
  void _internal_set_total_requested_bytes(int64_t value);
  public:

  // int64 total_parameters = 8;
  void clear_total_parameters();
  int64_t total_parameters() const;
  void set_total_parameters(int64_t value);
  private:
  int64_t _internal_total_parameters() const;
  void _internal_set_total_parameters(int64_t value);
  public:

  // int64 float_ops = 13;
  void clear_float_ops();
  int64_t float_ops() const;
  void set_float_ops(int64_t value);
  private:
  int64_t _internal_float_ops() const;
  void _internal_set_float_ops(int64_t value);
  public:

  // int64 total_float_ops = 14;
  void clear_total_float_ops();
  int64_t total_float_ops() const;
  void set_total_float_ops(int64_t value);
  private:
  int64_t _internal_total_float_ops() const;
  void _internal_set_total_float_ops(int64_t value);
  public:

  // int64 accelerator_exec_micros = 17;
  void clear_accelerator_exec_micros();
  int64_t accelerator_exec_micros() const;
  void set_accelerator_exec_micros(int64_t value);
  private:
  int64_t _internal_accelerator_exec_micros() const;
  void _internal_set_accelerator_exec_micros(int64_t value);
  public:

  // int64 cpu_exec_micros = 18;
  void clear_cpu_exec_micros();
  int64_t cpu_exec_micros() const;
  void set_cpu_exec_micros(int64_t value);
  private:
  int64_t _internal_cpu_exec_micros() const;
  void _internal_set_cpu_exec_micros(int64_t value);
  public:

  // int64 total_accelerator_exec_micros = 19;
  void clear_total_accelerator_exec_micros();
  int64_t total_accelerator_exec_micros() const;
  void set_total_accelerator_exec_micros(int64_t value);
  private:
  int64_t _internal_total_accelerator_exec_micros() const;
  void _internal_set_total_accelerator_exec_micros(int64_t value);
  public:

  // int64 total_cpu_exec_micros = 20;
  void clear_total_cpu_exec_micros();
  int64_t total_cpu_exec_micros() const;
  void set_total_cpu_exec_micros(int64_t value);
  private:
  int64_t _internal_total_cpu_exec_micros() const;
  void _internal_set_total_cpu_exec_micros(int64_t value);
  public:

  // int64 run_count = 21;
  void clear_run_count();
  int64_t run_count() const;
  void set_run_count(int64_t value);
  private:
  int64_t _internal_run_count() const;
  void _internal_set_run_count(int64_t value);
  public:

  // int64 total_run_count = 22;
  void clear_total_run_count();
  int64_t total_run_count() const;
  void set_total_run_count(int64_t value);
  private:
  int64_t _internal_total_run_count() const;
  void _internal_set_total_run_count(int64_t value);
  public:

  // int64 total_definition_count = 23;
  void clear_total_definition_count();
  int64_t total_definition_count() const;
  void set_total_definition_count(int64_t value);
  private:
  int64_t _internal_total_definition_count() const;
  void _internal_set_total_definition_count(int64_t value);
  public:

  // int64 peak_bytes = 24;
  void clear_peak_bytes();
  int64_t peak_bytes() const;
  void set_peak_bytes(int64_t value);
  private:
  int64_t _internal_peak_bytes() const;
  void _internal_set_peak_bytes(int64_t value);
  public:

  // int64 residual_bytes = 25;
  void clear_residual_bytes();
  int64_t residual_bytes() const;
  void set_residual_bytes(int64_t value);
  private:
  int64_t _internal_residual_bytes() const;
  void _internal_set_residual_bytes(int64_t value);
  public:

  // int64 output_bytes = 26;
  void clear_output_bytes();
  int64_t output_bytes() const;
  void set_output_bytes(int64_t value);
  private:
  int64_t _internal_output_bytes() const;
  void _internal_set_output_bytes(int64_t value);
  public:

  // int64 total_peak_bytes = 27;
  void clear_total_peak_bytes();
  int64_t total_peak_bytes() const;
  void set_total_peak_bytes(int64_t value);
  private:
  int64_t _internal_total_peak_bytes() const;
  void _internal_set_total_peak_bytes(int64_t value);
  public:

  // int64 total_residual_bytes = 28;
  void clear_total_residual_bytes();
  int64_t total_residual_bytes() const;
  void set_total_residual_bytes(int64_t value);
  private:
  int64_t _internal_total_residual_bytes() const;
  void _internal_set_total_residual_bytes(int64_t value);
  public:

  // int64 total_output_bytes = 29;
  void clear_total_output_bytes();
  int64_t total_output_bytes() const;
  void set_total_output_bytes(int64_t value);
  private:
  int64_t _internal_total_output_bytes() const;
  void _internal_set_total_output_bytes(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.GraphNodeProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> devices_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto > shapes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto > children_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        GraphNodeProto_InputShapesEntry_DoNotUse,
        int32_t, ::tensorflow::TensorShapeProto,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> input_shapes_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::tfprof::TFProfTensorProto* tensor_value_;
    int64_t exec_micros_;
    int64_t requested_bytes_;
    int64_t parameters_;
    int64_t total_exec_micros_;
    int64_t total_requested_bytes_;
    int64_t total_parameters_;
    int64_t float_ops_;
    int64_t total_float_ops_;
    int64_t accelerator_exec_micros_;
    int64_t cpu_exec_micros_;
    int64_t total_accelerator_exec_micros_;
    int64_t total_cpu_exec_micros_;
    int64_t run_count_;
    int64_t total_run_count_;
    int64_t total_definition_count_;
    int64_t peak_bytes_;
    int64_t residual_bytes_;
    int64_t output_bytes_;
    int64_t total_peak_bytes_;
    int64_t total_residual_bytes_;
    int64_t total_output_bytes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// -------------------------------------------------------------------

class MultiGraphNodeProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.MultiGraphNodeProto) */ {
 public:
  inline MultiGraphNodeProto() : MultiGraphNodeProto(nullptr) {}
  ~MultiGraphNodeProto() override;
  explicit PROTOBUF_CONSTEXPR MultiGraphNodeProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MultiGraphNodeProto(const MultiGraphNodeProto& from);
  MultiGraphNodeProto(MultiGraphNodeProto&& from) noexcept
    : MultiGraphNodeProto() {
    *this = ::std::move(from);
  }

  inline MultiGraphNodeProto& operator=(const MultiGraphNodeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline MultiGraphNodeProto& operator=(MultiGraphNodeProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MultiGraphNodeProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const MultiGraphNodeProto* internal_default_instance() {
    return reinterpret_cast<const MultiGraphNodeProto*>(
               &_MultiGraphNodeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MultiGraphNodeProto& a, MultiGraphNodeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(MultiGraphNodeProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MultiGraphNodeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MultiGraphNodeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MultiGraphNodeProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MultiGraphNodeProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MultiGraphNodeProto& from) {
    MultiGraphNodeProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MultiGraphNodeProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.MultiGraphNodeProto";
  }
  protected:
  explicit MultiGraphNodeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphNodesFieldNumber = 10,
    kChildrenFieldNumber = 11,
    kNameFieldNumber = 1,
    kExecMicrosFieldNumber = 2,
    kRequestedBytesFieldNumber = 3,
    kParametersFieldNumber = 4,
    kFloatOpsFieldNumber = 5,
    kTotalExecMicrosFieldNumber = 6,
    kTotalRequestedBytesFieldNumber = 7,
    kTotalParametersFieldNumber = 8,
    kTotalFloatOpsFieldNumber = 9,
    kAcceleratorExecMicrosFieldNumber = 12,
    kCpuExecMicrosFieldNumber = 13,
    kTotalAcceleratorExecMicrosFieldNumber = 14,
    kTotalCpuExecMicrosFieldNumber = 15,
    kPeakBytesFieldNumber = 16,
    kResidualBytesFieldNumber = 17,
    kOutputBytesFieldNumber = 18,
    kTotalPeakBytesFieldNumber = 19,
    kTotalResidualBytesFieldNumber = 20,
    kTotalOutputBytesFieldNumber = 21,
  };
  // repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
  int graph_nodes_size() const;
  private:
  int _internal_graph_nodes_size() const;
  public:
  void clear_graph_nodes();
  ::tensorflow::tfprof::GraphNodeProto* mutable_graph_nodes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
      mutable_graph_nodes();
  private:
  const ::tensorflow::tfprof::GraphNodeProto& _internal_graph_nodes(int index) const;
  ::tensorflow::tfprof::GraphNodeProto* _internal_add_graph_nodes();
  public:
  const ::tensorflow::tfprof::GraphNodeProto& graph_nodes(int index) const;
  ::tensorflow::tfprof::GraphNodeProto* add_graph_nodes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
      graph_nodes() const;

  // repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
  int children_size() const;
  private:
  int _internal_children_size() const;
  public:
  void clear_children();
  ::tensorflow::tfprof::MultiGraphNodeProto* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >*
      mutable_children();
  private:
  const ::tensorflow::tfprof::MultiGraphNodeProto& _internal_children(int index) const;
  ::tensorflow::tfprof::MultiGraphNodeProto* _internal_add_children();
  public:
  const ::tensorflow::tfprof::MultiGraphNodeProto& children(int index) const;
  ::tensorflow::tfprof::MultiGraphNodeProto* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >&
      children() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int64 exec_micros = 2;
  void clear_exec_micros();
  int64_t exec_micros() const;
  void set_exec_micros(int64_t value);
  private:
  int64_t _internal_exec_micros() const;
  void _internal_set_exec_micros(int64_t value);
  public:

  // int64 requested_bytes = 3;
  void clear_requested_bytes();
  int64_t requested_bytes() const;
  void set_requested_bytes(int64_t value);
  private:
  int64_t _internal_requested_bytes() const;
  void _internal_set_requested_bytes(int64_t value);
  public:

  // int64 parameters = 4;
  void clear_parameters();
  int64_t parameters() const;
  void set_parameters(int64_t value);
  private:
  int64_t _internal_parameters() const;
  void _internal_set_parameters(int64_t value);
  public:

  // int64 float_ops = 5;
  void clear_float_ops();
  int64_t float_ops() const;
  void set_float_ops(int64_t value);
  private:
  int64_t _internal_float_ops() const;
  void _internal_set_float_ops(int64_t value);
  public:

  // int64 total_exec_micros = 6;
  void clear_total_exec_micros();
  int64_t total_exec_micros() const;
  void set_total_exec_micros(int64_t value);
  private:
  int64_t _internal_total_exec_micros() const;
  void _internal_set_total_exec_micros(int64_t value);
  public:

  // int64 total_requested_bytes = 7;
  void clear_total_requested_bytes();
  int64_t total_requested_bytes() const;
  void set_total_requested_bytes(int64_t value);
  private:
  int64_t _internal_total_requested_bytes() const;
  void _internal_set_total_requested_bytes(int64_t value);
  public:

  // int64 total_parameters = 8;
  void clear_total_parameters();
  int64_t total_parameters() const;
  void set_total_parameters(int64_t value);
  private:
  int64_t _internal_total_parameters() const;
  void _internal_set_total_parameters(int64_t value);
  public:

  // int64 total_float_ops = 9;
  void clear_total_float_ops();
  int64_t total_float_ops() const;
  void set_total_float_ops(int64_t value);
  private:
  int64_t _internal_total_float_ops() const;
  void _internal_set_total_float_ops(int64_t value);
  public:

  // int64 accelerator_exec_micros = 12;
  void clear_accelerator_exec_micros();
  int64_t accelerator_exec_micros() const;
  void set_accelerator_exec_micros(int64_t value);
  private:
  int64_t _internal_accelerator_exec_micros() const;
  void _internal_set_accelerator_exec_micros(int64_t value);
  public:

  // int64 cpu_exec_micros = 13;
  void clear_cpu_exec_micros();
  int64_t cpu_exec_micros() const;
  void set_cpu_exec_micros(int64_t value);
  private:
  int64_t _internal_cpu_exec_micros() const;
  void _internal_set_cpu_exec_micros(int64_t value);
  public:

  // int64 total_accelerator_exec_micros = 14;
  void clear_total_accelerator_exec_micros();
  int64_t total_accelerator_exec_micros() const;
  void set_total_accelerator_exec_micros(int64_t value);
  private:
  int64_t _internal_total_accelerator_exec_micros() const;
  void _internal_set_total_accelerator_exec_micros(int64_t value);
  public:

  // int64 total_cpu_exec_micros = 15;
  void clear_total_cpu_exec_micros();
  int64_t total_cpu_exec_micros() const;
  void set_total_cpu_exec_micros(int64_t value);
  private:
  int64_t _internal_total_cpu_exec_micros() const;
  void _internal_set_total_cpu_exec_micros(int64_t value);
  public:

  // int64 peak_bytes = 16;
  void clear_peak_bytes();
  int64_t peak_bytes() const;
  void set_peak_bytes(int64_t value);
  private:
  int64_t _internal_peak_bytes() const;
  void _internal_set_peak_bytes(int64_t value);
  public:

  // int64 residual_bytes = 17;
  void clear_residual_bytes();
  int64_t residual_bytes() const;
  void set_residual_bytes(int64_t value);
  private:
  int64_t _internal_residual_bytes() const;
  void _internal_set_residual_bytes(int64_t value);
  public:

  // int64 output_bytes = 18;
  void clear_output_bytes();
  int64_t output_bytes() const;
  void set_output_bytes(int64_t value);
  private:
  int64_t _internal_output_bytes() const;
  void _internal_set_output_bytes(int64_t value);
  public:

  // int64 total_peak_bytes = 19;
  void clear_total_peak_bytes();
  int64_t total_peak_bytes() const;
  void set_total_peak_bytes(int64_t value);
  private:
  int64_t _internal_total_peak_bytes() const;
  void _internal_set_total_peak_bytes(int64_t value);
  public:

  // int64 total_residual_bytes = 20;
  void clear_total_residual_bytes();
  int64_t total_residual_bytes() const;
  void set_total_residual_bytes(int64_t value);
  private:
  int64_t _internal_total_residual_bytes() const;
  void _internal_set_total_residual_bytes(int64_t value);
  public:

  // int64 total_output_bytes = 21;
  void clear_total_output_bytes();
  int64_t total_output_bytes() const;
  void set_total_output_bytes(int64_t value);
  private:
  int64_t _internal_total_output_bytes() const;
  void _internal_set_total_output_bytes(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.MultiGraphNodeProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto > graph_nodes_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto > children_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int64_t exec_micros_;
    int64_t requested_bytes_;
    int64_t parameters_;
    int64_t float_ops_;
    int64_t total_exec_micros_;
    int64_t total_requested_bytes_;
    int64_t total_parameters_;
    int64_t total_float_ops_;
    int64_t accelerator_exec_micros_;
    int64_t cpu_exec_micros_;
    int64_t total_accelerator_exec_micros_;
    int64_t total_cpu_exec_micros_;
    int64_t peak_bytes_;
    int64_t residual_bytes_;
    int64_t output_bytes_;
    int64_t total_peak_bytes_;
    int64_t total_residual_bytes_;
    int64_t total_output_bytes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// -------------------------------------------------------------------

class AdviceProto_CheckersEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdviceProto_CheckersEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::AdviceProto_Checker,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<AdviceProto_CheckersEntry_DoNotUse, 
    std::string, ::tensorflow::tfprof::AdviceProto_Checker,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  AdviceProto_CheckersEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR AdviceProto_CheckersEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit AdviceProto_CheckersEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const AdviceProto_CheckersEntry_DoNotUse& other);
  static const AdviceProto_CheckersEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const AdviceProto_CheckersEntry_DoNotUse*>(&_AdviceProto_CheckersEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.tfprof.AdviceProto.CheckersEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};

// -------------------------------------------------------------------

class AdviceProto_Checker final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdviceProto.Checker) */ {
 public:
  inline AdviceProto_Checker() : AdviceProto_Checker(nullptr) {}
  ~AdviceProto_Checker() override;
  explicit PROTOBUF_CONSTEXPR AdviceProto_Checker(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AdviceProto_Checker(const AdviceProto_Checker& from);
  AdviceProto_Checker(AdviceProto_Checker&& from) noexcept
    : AdviceProto_Checker() {
    *this = ::std::move(from);
  }

  inline AdviceProto_Checker& operator=(const AdviceProto_Checker& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdviceProto_Checker& operator=(AdviceProto_Checker&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdviceProto_Checker& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdviceProto_Checker* internal_default_instance() {
    return reinterpret_cast<const AdviceProto_Checker*>(
               &_AdviceProto_Checker_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(AdviceProto_Checker& a, AdviceProto_Checker& b) {
    a.Swap(&b);
  }
  inline void Swap(AdviceProto_Checker* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdviceProto_Checker* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdviceProto_Checker* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AdviceProto_Checker>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AdviceProto_Checker& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AdviceProto_Checker& from) {
    AdviceProto_Checker::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdviceProto_Checker* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.AdviceProto.Checker";
  }
  protected:
  explicit AdviceProto_Checker(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kReportsFieldNumber = 2,
  };
  // repeated string reports = 2;
  int reports_size() const;
  private:
  int _internal_reports_size() const;
  public:
  void clear_reports();
  const std::string& reports(int index) const;
  std::string* mutable_reports(int index);
  void set_reports(int index, const std::string& value);
  void set_reports(int index, std::string&& value);
  void set_reports(int index, const char* value);
  void set_reports(int index, const char* value, size_t size);
  std::string* add_reports();
  void add_reports(const std::string& value);
  void add_reports(std::string&& value);
  void add_reports(const char* value);
  void add_reports(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& reports() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_reports();
  private:
  const std::string& _internal_reports(int index) const;
  std::string* _internal_add_reports();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdviceProto.Checker)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> reports_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// -------------------------------------------------------------------

class AdviceProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tfprof.AdviceProto) */ {
 public:
  inline AdviceProto() : AdviceProto(nullptr) {}
  ~AdviceProto() override;
  explicit PROTOBUF_CONSTEXPR AdviceProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AdviceProto(const AdviceProto& from);
  AdviceProto(AdviceProto&& from) noexcept
    : AdviceProto() {
    *this = ::std::move(from);
  }

  inline AdviceProto& operator=(const AdviceProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdviceProto& operator=(AdviceProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdviceProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdviceProto* internal_default_instance() {
    return reinterpret_cast<const AdviceProto*>(
               &_AdviceProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(AdviceProto& a, AdviceProto& b) {
    a.Swap(&b);
  }
  inline void Swap(AdviceProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdviceProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdviceProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AdviceProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AdviceProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AdviceProto& from) {
    AdviceProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdviceProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tfprof.AdviceProto";
  }
  protected:
  explicit AdviceProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef AdviceProto_Checker Checker;

  // accessors -------------------------------------------------------

  enum : int {
    kCheckersFieldNumber = 1,
  };
  // map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
  int checkers_size() const;
  private:
  int _internal_checkers_size() const;
  public:
  void clear_checkers();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >&
      _internal_checkers() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >*
      _internal_mutable_checkers();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >&
      checkers() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >*
      mutable_checkers();

  // @@protoc_insertion_point(class_scope:tensorflow.tfprof.AdviceProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        AdviceProto_CheckersEntry_DoNotUse,
        std::string, ::tensorflow::tfprof::AdviceProto_Checker,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> checkers_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TFProfTensorProto

// .tensorflow.DataType dtype = 1;
inline void TFProfTensorProto::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType TFProfTensorProto::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType TFProfTensorProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.dtype)
  return _internal_dtype();
}
inline void TFProfTensorProto::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void TFProfTensorProto::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.dtype)
}

// repeated double value_double = 2;
inline int TFProfTensorProto::_internal_value_double_size() const {
  return _impl_.value_double_.size();
}
inline int TFProfTensorProto::value_double_size() const {
  return _internal_value_double_size();
}
inline void TFProfTensorProto::clear_value_double() {
  _impl_.value_double_.Clear();
}
inline double TFProfTensorProto::_internal_value_double(int index) const {
  return _impl_.value_double_.Get(index);
}
inline double TFProfTensorProto::value_double(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_double)
  return _internal_value_double(index);
}
inline void TFProfTensorProto::set_value_double(int index, double value) {
  _impl_.value_double_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_double)
}
inline void TFProfTensorProto::_internal_add_value_double(double value) {
  _impl_.value_double_.Add(value);
}
inline void TFProfTensorProto::add_value_double(double value) {
  _internal_add_value_double(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_double)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TFProfTensorProto::_internal_value_double() const {
  return _impl_.value_double_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
TFProfTensorProto::value_double() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_double)
  return _internal_value_double();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TFProfTensorProto::_internal_mutable_value_double() {
  return &_impl_.value_double_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
TFProfTensorProto::mutable_value_double() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_double)
  return _internal_mutable_value_double();
}

// repeated int64 value_int64 = 3;
inline int TFProfTensorProto::_internal_value_int64_size() const {
  return _impl_.value_int64_.size();
}
inline int TFProfTensorProto::value_int64_size() const {
  return _internal_value_int64_size();
}
inline void TFProfTensorProto::clear_value_int64() {
  _impl_.value_int64_.Clear();
}
inline int64_t TFProfTensorProto::_internal_value_int64(int index) const {
  return _impl_.value_int64_.Get(index);
}
inline int64_t TFProfTensorProto::value_int64(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return _internal_value_int64(index);
}
inline void TFProfTensorProto::set_value_int64(int index, int64_t value) {
  _impl_.value_int64_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_int64)
}
inline void TFProfTensorProto::_internal_add_value_int64(int64_t value) {
  _impl_.value_int64_.Add(value);
}
inline void TFProfTensorProto::add_value_int64(int64_t value) {
  _internal_add_value_int64(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_int64)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TFProfTensorProto::_internal_value_int64() const {
  return _impl_.value_int64_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
TFProfTensorProto::value_int64() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return _internal_value_int64();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TFProfTensorProto::_internal_mutable_value_int64() {
  return &_impl_.value_int64_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
TFProfTensorProto::mutable_value_int64() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_int64)
  return _internal_mutable_value_int64();
}

// repeated string value_str = 4;
inline int TFProfTensorProto::_internal_value_str_size() const {
  return _impl_.value_str_.size();
}
inline int TFProfTensorProto::value_str_size() const {
  return _internal_value_str_size();
}
inline void TFProfTensorProto::clear_value_str() {
  _impl_.value_str_.Clear();
}
inline std::string* TFProfTensorProto::add_value_str() {
  std::string* _s = _internal_add_value_str();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.TFProfTensorProto.value_str)
  return _s;
}
inline const std::string& TFProfTensorProto::_internal_value_str(int index) const {
  return _impl_.value_str_.Get(index);
}
inline const std::string& TFProfTensorProto::value_str(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.TFProfTensorProto.value_str)
  return _internal_value_str(index);
}
inline std::string* TFProfTensorProto::mutable_value_str(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.TFProfTensorProto.value_str)
  return _impl_.value_str_.Mutable(index);
}
inline void TFProfTensorProto::set_value_str(int index, const std::string& value) {
  _impl_.value_str_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::set_value_str(int index, std::string&& value) {
  _impl_.value_str_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::set_value_str(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.value_str_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::set_value_str(int index, const char* value, size_t size) {
  _impl_.value_str_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline std::string* TFProfTensorProto::_internal_add_value_str() {
  return _impl_.value_str_.Add();
}
inline void TFProfTensorProto::add_value_str(const std::string& value) {
  _impl_.value_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::add_value_str(std::string&& value) {
  _impl_.value_str_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::add_value_str(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.value_str_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline void TFProfTensorProto::add_value_str(const char* value, size_t size) {
  _impl_.value_str_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.TFProfTensorProto.value_str)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TFProfTensorProto::value_str() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.TFProfTensorProto.value_str)
  return _impl_.value_str_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TFProfTensorProto::mutable_value_str() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.TFProfTensorProto.value_str)
  return &_impl_.value_str_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GraphNodeProto

// string name = 1;
inline void GraphNodeProto::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& GraphNodeProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GraphNodeProto::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.name)
}
inline std::string* GraphNodeProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.name)
  return _s;
}
inline const std::string& GraphNodeProto::_internal_name() const {
  return _impl_.name_.Get();
}
inline void GraphNodeProto::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* GraphNodeProto::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* GraphNodeProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.GraphNodeProto.name)
  return _impl_.name_.Release();
}
inline void GraphNodeProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.GraphNodeProto.name)
}

// .tensorflow.tfprof.TFProfTensorProto tensor_value = 15;
inline bool GraphNodeProto::_internal_has_tensor_value() const {
  return this != internal_default_instance() && _impl_.tensor_value_ != nullptr;
}
inline bool GraphNodeProto::has_tensor_value() const {
  return _internal_has_tensor_value();
}
inline void GraphNodeProto::clear_tensor_value() {
  if (GetArenaForAllocation() == nullptr && _impl_.tensor_value_ != nullptr) {
    delete _impl_.tensor_value_;
  }
  _impl_.tensor_value_ = nullptr;
}
inline const ::tensorflow::tfprof::TFProfTensorProto& GraphNodeProto::_internal_tensor_value() const {
  const ::tensorflow::tfprof::TFProfTensorProto* p = _impl_.tensor_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tfprof::TFProfTensorProto&>(
      ::tensorflow::tfprof::_TFProfTensorProto_default_instance_);
}
inline const ::tensorflow::tfprof::TFProfTensorProto& GraphNodeProto::tensor_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.tensor_value)
  return _internal_tensor_value();
}
inline void GraphNodeProto::unsafe_arena_set_allocated_tensor_value(
    ::tensorflow::tfprof::TFProfTensorProto* tensor_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_value_);
  }
  _impl_.tensor_value_ = tensor_value;
  if (tensor_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tfprof.GraphNodeProto.tensor_value)
}
inline ::tensorflow::tfprof::TFProfTensorProto* GraphNodeProto::release_tensor_value() {
  
  ::tensorflow::tfprof::TFProfTensorProto* temp = _impl_.tensor_value_;
  _impl_.tensor_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tfprof::TFProfTensorProto* GraphNodeProto::unsafe_arena_release_tensor_value() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.GraphNodeProto.tensor_value)
  
  ::tensorflow::tfprof::TFProfTensorProto* temp = _impl_.tensor_value_;
  _impl_.tensor_value_ = nullptr;
  return temp;
}
inline ::tensorflow::tfprof::TFProfTensorProto* GraphNodeProto::_internal_mutable_tensor_value() {
  
  if (_impl_.tensor_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tfprof::TFProfTensorProto>(GetArenaForAllocation());
    _impl_.tensor_value_ = p;
  }
  return _impl_.tensor_value_;
}
inline ::tensorflow::tfprof::TFProfTensorProto* GraphNodeProto::mutable_tensor_value() {
  ::tensorflow::tfprof::TFProfTensorProto* _msg = _internal_mutable_tensor_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.tensor_value)
  return _msg;
}
inline void GraphNodeProto::set_allocated_tensor_value(::tensorflow::tfprof::TFProfTensorProto* tensor_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.tensor_value_;
  }
  if (tensor_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(tensor_value);
    if (message_arena != submessage_arena) {
      tensor_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_value, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_value_ = tensor_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.GraphNodeProto.tensor_value)
}

// int64 run_count = 21;
inline void GraphNodeProto::clear_run_count() {
  _impl_.run_count_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_run_count() const {
  return _impl_.run_count_;
}
inline int64_t GraphNodeProto::run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.run_count)
  return _internal_run_count();
}
inline void GraphNodeProto::_internal_set_run_count(int64_t value) {
  
  _impl_.run_count_ = value;
}
inline void GraphNodeProto::set_run_count(int64_t value) {
  _internal_set_run_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.run_count)
}

// int64 exec_micros = 2;
inline void GraphNodeProto::clear_exec_micros() {
  _impl_.exec_micros_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_exec_micros() const {
  return _impl_.exec_micros_;
}
inline int64_t GraphNodeProto::exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.exec_micros)
  return _internal_exec_micros();
}
inline void GraphNodeProto::_internal_set_exec_micros(int64_t value) {
  
  _impl_.exec_micros_ = value;
}
inline void GraphNodeProto::set_exec_micros(int64_t value) {
  _internal_set_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.exec_micros)
}

// int64 accelerator_exec_micros = 17;
inline void GraphNodeProto::clear_accelerator_exec_micros() {
  _impl_.accelerator_exec_micros_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_accelerator_exec_micros() const {
  return _impl_.accelerator_exec_micros_;
}
inline int64_t GraphNodeProto::accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.accelerator_exec_micros)
  return _internal_accelerator_exec_micros();
}
inline void GraphNodeProto::_internal_set_accelerator_exec_micros(int64_t value) {
  
  _impl_.accelerator_exec_micros_ = value;
}
inline void GraphNodeProto::set_accelerator_exec_micros(int64_t value) {
  _internal_set_accelerator_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.accelerator_exec_micros)
}

// int64 cpu_exec_micros = 18;
inline void GraphNodeProto::clear_cpu_exec_micros() {
  _impl_.cpu_exec_micros_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_cpu_exec_micros() const {
  return _impl_.cpu_exec_micros_;
}
inline int64_t GraphNodeProto::cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.cpu_exec_micros)
  return _internal_cpu_exec_micros();
}
inline void GraphNodeProto::_internal_set_cpu_exec_micros(int64_t value) {
  
  _impl_.cpu_exec_micros_ = value;
}
inline void GraphNodeProto::set_cpu_exec_micros(int64_t value) {
  _internal_set_cpu_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.cpu_exec_micros)
}

// int64 requested_bytes = 3;
inline void GraphNodeProto::clear_requested_bytes() {
  _impl_.requested_bytes_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_requested_bytes() const {
  return _impl_.requested_bytes_;
}
inline int64_t GraphNodeProto::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.requested_bytes)
  return _internal_requested_bytes();
}
inline void GraphNodeProto::_internal_set_requested_bytes(int64_t value) {
  
  _impl_.requested_bytes_ = value;
}
inline void GraphNodeProto::set_requested_bytes(int64_t value) {
  _internal_set_requested_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.requested_bytes)
}

// int64 peak_bytes = 24;
inline void GraphNodeProto::clear_peak_bytes() {
  _impl_.peak_bytes_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_peak_bytes() const {
  return _impl_.peak_bytes_;
}
inline int64_t GraphNodeProto::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.peak_bytes)
  return _internal_peak_bytes();
}
inline void GraphNodeProto::_internal_set_peak_bytes(int64_t value) {
  
  _impl_.peak_bytes_ = value;
}
inline void GraphNodeProto::set_peak_bytes(int64_t value) {
  _internal_set_peak_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.peak_bytes)
}

// int64 residual_bytes = 25;
inline void GraphNodeProto::clear_residual_bytes() {
  _impl_.residual_bytes_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_residual_bytes() const {
  return _impl_.residual_bytes_;
}
inline int64_t GraphNodeProto::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.residual_bytes)
  return _internal_residual_bytes();
}
inline void GraphNodeProto::_internal_set_residual_bytes(int64_t value) {
  
  _impl_.residual_bytes_ = value;
}
inline void GraphNodeProto::set_residual_bytes(int64_t value) {
  _internal_set_residual_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.residual_bytes)
}

// int64 output_bytes = 26;
inline void GraphNodeProto::clear_output_bytes() {
  _impl_.output_bytes_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_output_bytes() const {
  return _impl_.output_bytes_;
}
inline int64_t GraphNodeProto::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.output_bytes)
  return _internal_output_bytes();
}
inline void GraphNodeProto::_internal_set_output_bytes(int64_t value) {
  
  _impl_.output_bytes_ = value;
}
inline void GraphNodeProto::set_output_bytes(int64_t value) {
  _internal_set_output_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.output_bytes)
}

// int64 parameters = 4;
inline void GraphNodeProto::clear_parameters() {
  _impl_.parameters_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_parameters() const {
  return _impl_.parameters_;
}
inline int64_t GraphNodeProto::parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.parameters)
  return _internal_parameters();
}
inline void GraphNodeProto::_internal_set_parameters(int64_t value) {
  
  _impl_.parameters_ = value;
}
inline void GraphNodeProto::set_parameters(int64_t value) {
  _internal_set_parameters(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.parameters)
}

// int64 float_ops = 13;
inline void GraphNodeProto::clear_float_ops() {
  _impl_.float_ops_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_float_ops() const {
  return _impl_.float_ops_;
}
inline int64_t GraphNodeProto::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.float_ops)
  return _internal_float_ops();
}
inline void GraphNodeProto::_internal_set_float_ops(int64_t value) {
  
  _impl_.float_ops_ = value;
}
inline void GraphNodeProto::set_float_ops(int64_t value) {
  _internal_set_float_ops(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.float_ops)
}

// repeated string devices = 10;
inline int GraphNodeProto::_internal_devices_size() const {
  return _impl_.devices_.size();
}
inline int GraphNodeProto::devices_size() const {
  return _internal_devices_size();
}
inline void GraphNodeProto::clear_devices() {
  _impl_.devices_.Clear();
}
inline std::string* GraphNodeProto::add_devices() {
  std::string* _s = _internal_add_devices();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.GraphNodeProto.devices)
  return _s;
}
inline const std::string& GraphNodeProto::_internal_devices(int index) const {
  return _impl_.devices_.Get(index);
}
inline const std::string& GraphNodeProto::devices(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.devices)
  return _internal_devices(index);
}
inline std::string* GraphNodeProto::mutable_devices(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.devices)
  return _impl_.devices_.Mutable(index);
}
inline void GraphNodeProto::set_devices(int index, const std::string& value) {
  _impl_.devices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::set_devices(int index, std::string&& value) {
  _impl_.devices_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::set_devices(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.devices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::set_devices(int index, const char* value, size_t size) {
  _impl_.devices_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.GraphNodeProto.devices)
}
inline std::string* GraphNodeProto::_internal_add_devices() {
  return _impl_.devices_.Add();
}
inline void GraphNodeProto::add_devices(const std::string& value) {
  _impl_.devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::add_devices(std::string&& value) {
  _impl_.devices_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::add_devices(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.devices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.GraphNodeProto.devices)
}
inline void GraphNodeProto::add_devices(const char* value, size_t size) {
  _impl_.devices_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.GraphNodeProto.devices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
GraphNodeProto::devices() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.devices)
  return _impl_.devices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
GraphNodeProto::mutable_devices() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.devices)
  return &_impl_.devices_;
}

// int64 total_definition_count = 23;
inline void GraphNodeProto::clear_total_definition_count() {
  _impl_.total_definition_count_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_definition_count() const {
  return _impl_.total_definition_count_;
}
inline int64_t GraphNodeProto::total_definition_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_definition_count)
  return _internal_total_definition_count();
}
inline void GraphNodeProto::_internal_set_total_definition_count(int64_t value) {
  
  _impl_.total_definition_count_ = value;
}
inline void GraphNodeProto::set_total_definition_count(int64_t value) {
  _internal_set_total_definition_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_definition_count)
}

// int64 total_run_count = 22;
inline void GraphNodeProto::clear_total_run_count() {
  _impl_.total_run_count_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_run_count() const {
  return _impl_.total_run_count_;
}
inline int64_t GraphNodeProto::total_run_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_run_count)
  return _internal_total_run_count();
}
inline void GraphNodeProto::_internal_set_total_run_count(int64_t value) {
  
  _impl_.total_run_count_ = value;
}
inline void GraphNodeProto::set_total_run_count(int64_t value) {
  _internal_set_total_run_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_run_count)
}

// int64 total_exec_micros = 6;
inline void GraphNodeProto::clear_total_exec_micros() {
  _impl_.total_exec_micros_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_exec_micros() const {
  return _impl_.total_exec_micros_;
}
inline int64_t GraphNodeProto::total_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_exec_micros)
  return _internal_total_exec_micros();
}
inline void GraphNodeProto::_internal_set_total_exec_micros(int64_t value) {
  
  _impl_.total_exec_micros_ = value;
}
inline void GraphNodeProto::set_total_exec_micros(int64_t value) {
  _internal_set_total_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_exec_micros)
}

// int64 total_accelerator_exec_micros = 19;
inline void GraphNodeProto::clear_total_accelerator_exec_micros() {
  _impl_.total_accelerator_exec_micros_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_accelerator_exec_micros() const {
  return _impl_.total_accelerator_exec_micros_;
}
inline int64_t GraphNodeProto::total_accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_accelerator_exec_micros)
  return _internal_total_accelerator_exec_micros();
}
inline void GraphNodeProto::_internal_set_total_accelerator_exec_micros(int64_t value) {
  
  _impl_.total_accelerator_exec_micros_ = value;
}
inline void GraphNodeProto::set_total_accelerator_exec_micros(int64_t value) {
  _internal_set_total_accelerator_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_accelerator_exec_micros)
}

// int64 total_cpu_exec_micros = 20;
inline void GraphNodeProto::clear_total_cpu_exec_micros() {
  _impl_.total_cpu_exec_micros_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_cpu_exec_micros() const {
  return _impl_.total_cpu_exec_micros_;
}
inline int64_t GraphNodeProto::total_cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_cpu_exec_micros)
  return _internal_total_cpu_exec_micros();
}
inline void GraphNodeProto::_internal_set_total_cpu_exec_micros(int64_t value) {
  
  _impl_.total_cpu_exec_micros_ = value;
}
inline void GraphNodeProto::set_total_cpu_exec_micros(int64_t value) {
  _internal_set_total_cpu_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_cpu_exec_micros)
}

// int64 total_requested_bytes = 7;
inline void GraphNodeProto::clear_total_requested_bytes() {
  _impl_.total_requested_bytes_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_requested_bytes() const {
  return _impl_.total_requested_bytes_;
}
inline int64_t GraphNodeProto::total_requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_requested_bytes)
  return _internal_total_requested_bytes();
}
inline void GraphNodeProto::_internal_set_total_requested_bytes(int64_t value) {
  
  _impl_.total_requested_bytes_ = value;
}
inline void GraphNodeProto::set_total_requested_bytes(int64_t value) {
  _internal_set_total_requested_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_requested_bytes)
}

// int64 total_peak_bytes = 27;
inline void GraphNodeProto::clear_total_peak_bytes() {
  _impl_.total_peak_bytes_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_peak_bytes() const {
  return _impl_.total_peak_bytes_;
}
inline int64_t GraphNodeProto::total_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_peak_bytes)
  return _internal_total_peak_bytes();
}
inline void GraphNodeProto::_internal_set_total_peak_bytes(int64_t value) {
  
  _impl_.total_peak_bytes_ = value;
}
inline void GraphNodeProto::set_total_peak_bytes(int64_t value) {
  _internal_set_total_peak_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_peak_bytes)
}

// int64 total_residual_bytes = 28;
inline void GraphNodeProto::clear_total_residual_bytes() {
  _impl_.total_residual_bytes_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_residual_bytes() const {
  return _impl_.total_residual_bytes_;
}
inline int64_t GraphNodeProto::total_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_residual_bytes)
  return _internal_total_residual_bytes();
}
inline void GraphNodeProto::_internal_set_total_residual_bytes(int64_t value) {
  
  _impl_.total_residual_bytes_ = value;
}
inline void GraphNodeProto::set_total_residual_bytes(int64_t value) {
  _internal_set_total_residual_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_residual_bytes)
}

// int64 total_output_bytes = 29;
inline void GraphNodeProto::clear_total_output_bytes() {
  _impl_.total_output_bytes_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_output_bytes() const {
  return _impl_.total_output_bytes_;
}
inline int64_t GraphNodeProto::total_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_output_bytes)
  return _internal_total_output_bytes();
}
inline void GraphNodeProto::_internal_set_total_output_bytes(int64_t value) {
  
  _impl_.total_output_bytes_ = value;
}
inline void GraphNodeProto::set_total_output_bytes(int64_t value) {
  _internal_set_total_output_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_output_bytes)
}

// int64 total_parameters = 8;
inline void GraphNodeProto::clear_total_parameters() {
  _impl_.total_parameters_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_parameters() const {
  return _impl_.total_parameters_;
}
inline int64_t GraphNodeProto::total_parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_parameters)
  return _internal_total_parameters();
}
inline void GraphNodeProto::_internal_set_total_parameters(int64_t value) {
  
  _impl_.total_parameters_ = value;
}
inline void GraphNodeProto::set_total_parameters(int64_t value) {
  _internal_set_total_parameters(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_parameters)
}

// int64 total_float_ops = 14;
inline void GraphNodeProto::clear_total_float_ops() {
  _impl_.total_float_ops_ = int64_t{0};
}
inline int64_t GraphNodeProto::_internal_total_float_ops() const {
  return _impl_.total_float_ops_;
}
inline int64_t GraphNodeProto::total_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.total_float_ops)
  return _internal_total_float_ops();
}
inline void GraphNodeProto::_internal_set_total_float_ops(int64_t value) {
  
  _impl_.total_float_ops_ = value;
}
inline void GraphNodeProto::set_total_float_ops(int64_t value) {
  _internal_set_total_float_ops(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.GraphNodeProto.total_float_ops)
}

// repeated .tensorflow.TensorShapeProto shapes = 11;
inline int GraphNodeProto::_internal_shapes_size() const {
  return _impl_.shapes_.size();
}
inline int GraphNodeProto::shapes_size() const {
  return _internal_shapes_size();
}
inline ::tensorflow::TensorShapeProto* GraphNodeProto::mutable_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.shapes)
  return _impl_.shapes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >*
GraphNodeProto::mutable_shapes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.shapes)
  return &_impl_.shapes_;
}
inline const ::tensorflow::TensorShapeProto& GraphNodeProto::_internal_shapes(int index) const {
  return _impl_.shapes_.Get(index);
}
inline const ::tensorflow::TensorShapeProto& GraphNodeProto::shapes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.shapes)
  return _internal_shapes(index);
}
inline ::tensorflow::TensorShapeProto* GraphNodeProto::_internal_add_shapes() {
  return _impl_.shapes_.Add();
}
inline ::tensorflow::TensorShapeProto* GraphNodeProto::add_shapes() {
  ::tensorflow::TensorShapeProto* _add = _internal_add_shapes();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.shapes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorShapeProto >&
GraphNodeProto::shapes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.shapes)
  return _impl_.shapes_;
}

// map<int32, .tensorflow.TensorShapeProto> input_shapes = 16;
inline int GraphNodeProto::_internal_input_shapes_size() const {
  return _impl_.input_shapes_.size();
}
inline int GraphNodeProto::input_shapes_size() const {
  return _internal_input_shapes_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TensorShapeProto >&
GraphNodeProto::_internal_input_shapes() const {
  return _impl_.input_shapes_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TensorShapeProto >&
GraphNodeProto::input_shapes() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.GraphNodeProto.input_shapes)
  return _internal_input_shapes();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TensorShapeProto >*
GraphNodeProto::_internal_mutable_input_shapes() {
  return _impl_.input_shapes_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TensorShapeProto >*
GraphNodeProto::mutable_input_shapes() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.GraphNodeProto.input_shapes)
  return _internal_mutable_input_shapes();
}

// repeated .tensorflow.tfprof.GraphNodeProto children = 12;
inline int GraphNodeProto::_internal_children_size() const {
  return _impl_.children_.size();
}
inline int GraphNodeProto::children_size() const {
  return _internal_children_size();
}
inline void GraphNodeProto::clear_children() {
  _impl_.children_.Clear();
}
inline ::tensorflow::tfprof::GraphNodeProto* GraphNodeProto::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.GraphNodeProto.children)
  return _impl_.children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
GraphNodeProto::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.GraphNodeProto.children)
  return &_impl_.children_;
}
inline const ::tensorflow::tfprof::GraphNodeProto& GraphNodeProto::_internal_children(int index) const {
  return _impl_.children_.Get(index);
}
inline const ::tensorflow::tfprof::GraphNodeProto& GraphNodeProto::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.GraphNodeProto.children)
  return _internal_children(index);
}
inline ::tensorflow::tfprof::GraphNodeProto* GraphNodeProto::_internal_add_children() {
  return _impl_.children_.Add();
}
inline ::tensorflow::tfprof::GraphNodeProto* GraphNodeProto::add_children() {
  ::tensorflow::tfprof::GraphNodeProto* _add = _internal_add_children();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.GraphNodeProto.children)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
GraphNodeProto::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.GraphNodeProto.children)
  return _impl_.children_;
}

// -------------------------------------------------------------------

// MultiGraphNodeProto

// string name = 1;
inline void MultiGraphNodeProto::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& MultiGraphNodeProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MultiGraphNodeProto::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.name)
}
inline std::string* MultiGraphNodeProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.name)
  return _s;
}
inline const std::string& MultiGraphNodeProto::_internal_name() const {
  return _impl_.name_.Get();
}
inline void MultiGraphNodeProto::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* MultiGraphNodeProto::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* MultiGraphNodeProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tfprof.MultiGraphNodeProto.name)
  return _impl_.name_.Release();
}
inline void MultiGraphNodeProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tfprof.MultiGraphNodeProto.name)
}

// int64 exec_micros = 2;
inline void MultiGraphNodeProto::clear_exec_micros() {
  _impl_.exec_micros_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_exec_micros() const {
  return _impl_.exec_micros_;
}
inline int64_t MultiGraphNodeProto::exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.exec_micros)
  return _internal_exec_micros();
}
inline void MultiGraphNodeProto::_internal_set_exec_micros(int64_t value) {
  
  _impl_.exec_micros_ = value;
}
inline void MultiGraphNodeProto::set_exec_micros(int64_t value) {
  _internal_set_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.exec_micros)
}

// int64 accelerator_exec_micros = 12;
inline void MultiGraphNodeProto::clear_accelerator_exec_micros() {
  _impl_.accelerator_exec_micros_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_accelerator_exec_micros() const {
  return _impl_.accelerator_exec_micros_;
}
inline int64_t MultiGraphNodeProto::accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.accelerator_exec_micros)
  return _internal_accelerator_exec_micros();
}
inline void MultiGraphNodeProto::_internal_set_accelerator_exec_micros(int64_t value) {
  
  _impl_.accelerator_exec_micros_ = value;
}
inline void MultiGraphNodeProto::set_accelerator_exec_micros(int64_t value) {
  _internal_set_accelerator_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.accelerator_exec_micros)
}

// int64 cpu_exec_micros = 13;
inline void MultiGraphNodeProto::clear_cpu_exec_micros() {
  _impl_.cpu_exec_micros_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_cpu_exec_micros() const {
  return _impl_.cpu_exec_micros_;
}
inline int64_t MultiGraphNodeProto::cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.cpu_exec_micros)
  return _internal_cpu_exec_micros();
}
inline void MultiGraphNodeProto::_internal_set_cpu_exec_micros(int64_t value) {
  
  _impl_.cpu_exec_micros_ = value;
}
inline void MultiGraphNodeProto::set_cpu_exec_micros(int64_t value) {
  _internal_set_cpu_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.cpu_exec_micros)
}

// int64 requested_bytes = 3;
inline void MultiGraphNodeProto::clear_requested_bytes() {
  _impl_.requested_bytes_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_requested_bytes() const {
  return _impl_.requested_bytes_;
}
inline int64_t MultiGraphNodeProto::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.requested_bytes)
  return _internal_requested_bytes();
}
inline void MultiGraphNodeProto::_internal_set_requested_bytes(int64_t value) {
  
  _impl_.requested_bytes_ = value;
}
inline void MultiGraphNodeProto::set_requested_bytes(int64_t value) {
  _internal_set_requested_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.requested_bytes)
}

// int64 peak_bytes = 16;
inline void MultiGraphNodeProto::clear_peak_bytes() {
  _impl_.peak_bytes_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_peak_bytes() const {
  return _impl_.peak_bytes_;
}
inline int64_t MultiGraphNodeProto::peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.peak_bytes)
  return _internal_peak_bytes();
}
inline void MultiGraphNodeProto::_internal_set_peak_bytes(int64_t value) {
  
  _impl_.peak_bytes_ = value;
}
inline void MultiGraphNodeProto::set_peak_bytes(int64_t value) {
  _internal_set_peak_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.peak_bytes)
}

// int64 residual_bytes = 17;
inline void MultiGraphNodeProto::clear_residual_bytes() {
  _impl_.residual_bytes_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_residual_bytes() const {
  return _impl_.residual_bytes_;
}
inline int64_t MultiGraphNodeProto::residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.residual_bytes)
  return _internal_residual_bytes();
}
inline void MultiGraphNodeProto::_internal_set_residual_bytes(int64_t value) {
  
  _impl_.residual_bytes_ = value;
}
inline void MultiGraphNodeProto::set_residual_bytes(int64_t value) {
  _internal_set_residual_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.residual_bytes)
}

// int64 output_bytes = 18;
inline void MultiGraphNodeProto::clear_output_bytes() {
  _impl_.output_bytes_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_output_bytes() const {
  return _impl_.output_bytes_;
}
inline int64_t MultiGraphNodeProto::output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.output_bytes)
  return _internal_output_bytes();
}
inline void MultiGraphNodeProto::_internal_set_output_bytes(int64_t value) {
  
  _impl_.output_bytes_ = value;
}
inline void MultiGraphNodeProto::set_output_bytes(int64_t value) {
  _internal_set_output_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.output_bytes)
}

// int64 parameters = 4;
inline void MultiGraphNodeProto::clear_parameters() {
  _impl_.parameters_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_parameters() const {
  return _impl_.parameters_;
}
inline int64_t MultiGraphNodeProto::parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.parameters)
  return _internal_parameters();
}
inline void MultiGraphNodeProto::_internal_set_parameters(int64_t value) {
  
  _impl_.parameters_ = value;
}
inline void MultiGraphNodeProto::set_parameters(int64_t value) {
  _internal_set_parameters(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.parameters)
}

// int64 float_ops = 5;
inline void MultiGraphNodeProto::clear_float_ops() {
  _impl_.float_ops_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_float_ops() const {
  return _impl_.float_ops_;
}
inline int64_t MultiGraphNodeProto::float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.float_ops)
  return _internal_float_ops();
}
inline void MultiGraphNodeProto::_internal_set_float_ops(int64_t value) {
  
  _impl_.float_ops_ = value;
}
inline void MultiGraphNodeProto::set_float_ops(int64_t value) {
  _internal_set_float_ops(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.float_ops)
}

// int64 total_exec_micros = 6;
inline void MultiGraphNodeProto::clear_total_exec_micros() {
  _impl_.total_exec_micros_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_exec_micros() const {
  return _impl_.total_exec_micros_;
}
inline int64_t MultiGraphNodeProto::total_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_exec_micros)
  return _internal_total_exec_micros();
}
inline void MultiGraphNodeProto::_internal_set_total_exec_micros(int64_t value) {
  
  _impl_.total_exec_micros_ = value;
}
inline void MultiGraphNodeProto::set_total_exec_micros(int64_t value) {
  _internal_set_total_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_exec_micros)
}

// int64 total_accelerator_exec_micros = 14;
inline void MultiGraphNodeProto::clear_total_accelerator_exec_micros() {
  _impl_.total_accelerator_exec_micros_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_accelerator_exec_micros() const {
  return _impl_.total_accelerator_exec_micros_;
}
inline int64_t MultiGraphNodeProto::total_accelerator_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_accelerator_exec_micros)
  return _internal_total_accelerator_exec_micros();
}
inline void MultiGraphNodeProto::_internal_set_total_accelerator_exec_micros(int64_t value) {
  
  _impl_.total_accelerator_exec_micros_ = value;
}
inline void MultiGraphNodeProto::set_total_accelerator_exec_micros(int64_t value) {
  _internal_set_total_accelerator_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_accelerator_exec_micros)
}

// int64 total_cpu_exec_micros = 15;
inline void MultiGraphNodeProto::clear_total_cpu_exec_micros() {
  _impl_.total_cpu_exec_micros_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_cpu_exec_micros() const {
  return _impl_.total_cpu_exec_micros_;
}
inline int64_t MultiGraphNodeProto::total_cpu_exec_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_cpu_exec_micros)
  return _internal_total_cpu_exec_micros();
}
inline void MultiGraphNodeProto::_internal_set_total_cpu_exec_micros(int64_t value) {
  
  _impl_.total_cpu_exec_micros_ = value;
}
inline void MultiGraphNodeProto::set_total_cpu_exec_micros(int64_t value) {
  _internal_set_total_cpu_exec_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_cpu_exec_micros)
}

// int64 total_requested_bytes = 7;
inline void MultiGraphNodeProto::clear_total_requested_bytes() {
  _impl_.total_requested_bytes_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_requested_bytes() const {
  return _impl_.total_requested_bytes_;
}
inline int64_t MultiGraphNodeProto::total_requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_requested_bytes)
  return _internal_total_requested_bytes();
}
inline void MultiGraphNodeProto::_internal_set_total_requested_bytes(int64_t value) {
  
  _impl_.total_requested_bytes_ = value;
}
inline void MultiGraphNodeProto::set_total_requested_bytes(int64_t value) {
  _internal_set_total_requested_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_requested_bytes)
}

// int64 total_peak_bytes = 19;
inline void MultiGraphNodeProto::clear_total_peak_bytes() {
  _impl_.total_peak_bytes_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_peak_bytes() const {
  return _impl_.total_peak_bytes_;
}
inline int64_t MultiGraphNodeProto::total_peak_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_peak_bytes)
  return _internal_total_peak_bytes();
}
inline void MultiGraphNodeProto::_internal_set_total_peak_bytes(int64_t value) {
  
  _impl_.total_peak_bytes_ = value;
}
inline void MultiGraphNodeProto::set_total_peak_bytes(int64_t value) {
  _internal_set_total_peak_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_peak_bytes)
}

// int64 total_residual_bytes = 20;
inline void MultiGraphNodeProto::clear_total_residual_bytes() {
  _impl_.total_residual_bytes_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_residual_bytes() const {
  return _impl_.total_residual_bytes_;
}
inline int64_t MultiGraphNodeProto::total_residual_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_residual_bytes)
  return _internal_total_residual_bytes();
}
inline void MultiGraphNodeProto::_internal_set_total_residual_bytes(int64_t value) {
  
  _impl_.total_residual_bytes_ = value;
}
inline void MultiGraphNodeProto::set_total_residual_bytes(int64_t value) {
  _internal_set_total_residual_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_residual_bytes)
}

// int64 total_output_bytes = 21;
inline void MultiGraphNodeProto::clear_total_output_bytes() {
  _impl_.total_output_bytes_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_output_bytes() const {
  return _impl_.total_output_bytes_;
}
inline int64_t MultiGraphNodeProto::total_output_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_output_bytes)
  return _internal_total_output_bytes();
}
inline void MultiGraphNodeProto::_internal_set_total_output_bytes(int64_t value) {
  
  _impl_.total_output_bytes_ = value;
}
inline void MultiGraphNodeProto::set_total_output_bytes(int64_t value) {
  _internal_set_total_output_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_output_bytes)
}

// int64 total_parameters = 8;
inline void MultiGraphNodeProto::clear_total_parameters() {
  _impl_.total_parameters_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_parameters() const {
  return _impl_.total_parameters_;
}
inline int64_t MultiGraphNodeProto::total_parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_parameters)
  return _internal_total_parameters();
}
inline void MultiGraphNodeProto::_internal_set_total_parameters(int64_t value) {
  
  _impl_.total_parameters_ = value;
}
inline void MultiGraphNodeProto::set_total_parameters(int64_t value) {
  _internal_set_total_parameters(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_parameters)
}

// int64 total_float_ops = 9;
inline void MultiGraphNodeProto::clear_total_float_ops() {
  _impl_.total_float_ops_ = int64_t{0};
}
inline int64_t MultiGraphNodeProto::_internal_total_float_ops() const {
  return _impl_.total_float_ops_;
}
inline int64_t MultiGraphNodeProto::total_float_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.total_float_ops)
  return _internal_total_float_ops();
}
inline void MultiGraphNodeProto::_internal_set_total_float_ops(int64_t value) {
  
  _impl_.total_float_ops_ = value;
}
inline void MultiGraphNodeProto::set_total_float_ops(int64_t value) {
  _internal_set_total_float_ops(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.MultiGraphNodeProto.total_float_ops)
}

// repeated .tensorflow.tfprof.GraphNodeProto graph_nodes = 10;
inline int MultiGraphNodeProto::_internal_graph_nodes_size() const {
  return _impl_.graph_nodes_.size();
}
inline int MultiGraphNodeProto::graph_nodes_size() const {
  return _internal_graph_nodes_size();
}
inline void MultiGraphNodeProto::clear_graph_nodes() {
  _impl_.graph_nodes_.Clear();
}
inline ::tensorflow::tfprof::GraphNodeProto* MultiGraphNodeProto::mutable_graph_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return _impl_.graph_nodes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >*
MultiGraphNodeProto::mutable_graph_nodes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return &_impl_.graph_nodes_;
}
inline const ::tensorflow::tfprof::GraphNodeProto& MultiGraphNodeProto::_internal_graph_nodes(int index) const {
  return _impl_.graph_nodes_.Get(index);
}
inline const ::tensorflow::tfprof::GraphNodeProto& MultiGraphNodeProto::graph_nodes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return _internal_graph_nodes(index);
}
inline ::tensorflow::tfprof::GraphNodeProto* MultiGraphNodeProto::_internal_add_graph_nodes() {
  return _impl_.graph_nodes_.Add();
}
inline ::tensorflow::tfprof::GraphNodeProto* MultiGraphNodeProto::add_graph_nodes() {
  ::tensorflow::tfprof::GraphNodeProto* _add = _internal_add_graph_nodes();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::GraphNodeProto >&
MultiGraphNodeProto::graph_nodes() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.MultiGraphNodeProto.graph_nodes)
  return _impl_.graph_nodes_;
}

// repeated .tensorflow.tfprof.MultiGraphNodeProto children = 11;
inline int MultiGraphNodeProto::_internal_children_size() const {
  return _impl_.children_.size();
}
inline int MultiGraphNodeProto::children_size() const {
  return _internal_children_size();
}
inline void MultiGraphNodeProto::clear_children() {
  _impl_.children_.Clear();
}
inline ::tensorflow::tfprof::MultiGraphNodeProto* MultiGraphNodeProto::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.MultiGraphNodeProto.children)
  return _impl_.children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >*
MultiGraphNodeProto::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.MultiGraphNodeProto.children)
  return &_impl_.children_;
}
inline const ::tensorflow::tfprof::MultiGraphNodeProto& MultiGraphNodeProto::_internal_children(int index) const {
  return _impl_.children_.Get(index);
}
inline const ::tensorflow::tfprof::MultiGraphNodeProto& MultiGraphNodeProto::children(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.MultiGraphNodeProto.children)
  return _internal_children(index);
}
inline ::tensorflow::tfprof::MultiGraphNodeProto* MultiGraphNodeProto::_internal_add_children() {
  return _impl_.children_.Add();
}
inline ::tensorflow::tfprof::MultiGraphNodeProto* MultiGraphNodeProto::add_children() {
  ::tensorflow::tfprof::MultiGraphNodeProto* _add = _internal_add_children();
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.MultiGraphNodeProto.children)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tfprof::MultiGraphNodeProto >&
MultiGraphNodeProto::children() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.MultiGraphNodeProto.children)
  return _impl_.children_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// AdviceProto_Checker

// repeated string reports = 2;
inline int AdviceProto_Checker::_internal_reports_size() const {
  return _impl_.reports_.size();
}
inline int AdviceProto_Checker::reports_size() const {
  return _internal_reports_size();
}
inline void AdviceProto_Checker::clear_reports() {
  _impl_.reports_.Clear();
}
inline std::string* AdviceProto_Checker::add_reports() {
  std::string* _s = _internal_add_reports();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.tfprof.AdviceProto.Checker.reports)
  return _s;
}
inline const std::string& AdviceProto_Checker::_internal_reports(int index) const {
  return _impl_.reports_.Get(index);
}
inline const std::string& AdviceProto_Checker::reports(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tfprof.AdviceProto.Checker.reports)
  return _internal_reports(index);
}
inline std::string* AdviceProto_Checker::mutable_reports(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tfprof.AdviceProto.Checker.reports)
  return _impl_.reports_.Mutable(index);
}
inline void AdviceProto_Checker::set_reports(int index, const std::string& value) {
  _impl_.reports_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::set_reports(int index, std::string&& value) {
  _impl_.reports_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::set_reports(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.reports_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::set_reports(int index, const char* value, size_t size) {
  _impl_.reports_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline std::string* AdviceProto_Checker::_internal_add_reports() {
  return _impl_.reports_.Add();
}
inline void AdviceProto_Checker::add_reports(const std::string& value) {
  _impl_.reports_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::add_reports(std::string&& value) {
  _impl_.reports_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::add_reports(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.reports_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline void AdviceProto_Checker::add_reports(const char* value, size_t size) {
  _impl_.reports_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.tfprof.AdviceProto.Checker.reports)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
AdviceProto_Checker::reports() const {
  // @@protoc_insertion_point(field_list:tensorflow.tfprof.AdviceProto.Checker.reports)
  return _impl_.reports_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
AdviceProto_Checker::mutable_reports() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tfprof.AdviceProto.Checker.reports)
  return &_impl_.reports_;
}

// -------------------------------------------------------------------

// AdviceProto

// map<string, .tensorflow.tfprof.AdviceProto.Checker> checkers = 1;
inline int AdviceProto::_internal_checkers_size() const {
  return _impl_.checkers_.size();
}
inline int AdviceProto::checkers_size() const {
  return _internal_checkers_size();
}
inline void AdviceProto::clear_checkers() {
  _impl_.checkers_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >&
AdviceProto::_internal_checkers() const {
  return _impl_.checkers_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >&
AdviceProto::checkers() const {
  // @@protoc_insertion_point(field_map:tensorflow.tfprof.AdviceProto.checkers)
  return _internal_checkers();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >*
AdviceProto::_internal_mutable_checkers() {
  return _impl_.checkers_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::tfprof::AdviceProto_Checker >*
AdviceProto::mutable_checkers() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.tfprof.AdviceProto.checkers)
  return _internal_mutable_checkers();
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tfprof
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprofiler_2ftfprof_5foutput_2eproto
