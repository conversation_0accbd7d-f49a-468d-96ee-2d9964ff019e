.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_export" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_export \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_export(gnutls_x509_privkey_t " key ", gnutls_x509_crt_fmt_t " format ", void * " output_data ", size_t * " output_data_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
Holds the key
.IP "gnutls_x509_crt_fmt_t format" 12
the format of output params. One of PEM or DER.
.IP "void * output_data" 12
will contain a private key PEM or DER encoded
.IP "size_t * output_data_size" 12
holds the size of output_data (and will be
replaced by the actual size of parameters)
.SH "DESCRIPTION"
This function will export the private key to a PKCS\fB1\fP structure for
RSA or RSA\-PSS keys, and integer sequence for DSA keys. Other keys types
will be exported in PKCS\fB8\fP form.

If the structure is PEM encoded, it will have a header
of "BEGIN RSA PRIVATE KEY".

It is recommended to use \fBgnutls_x509_privkey_export_pkcs8()\fP instead
of this function, when a consistent output format is required.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
