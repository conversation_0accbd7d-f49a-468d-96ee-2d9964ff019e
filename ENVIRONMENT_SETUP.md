# RNNoise 项目环境设置说明

## 环境概述

本项目已成功配置了Python虚拟环境，包含了运行RNNoise训练和推理所需的所有依赖包。

## 环境信息

- **环境名称**: `env`
- **Python版本**: 3.12
- **主要框架**:
  - NumPy 2.1.3
  - Keras 3.11.0
  - TensorFlow 2.19.0
  - PyTorch 2.7.1 (CPU版本)
  - h5py 3.14.0

## 快速开始

### 1. 激活环境

**Windows PowerShell:**
```powershell
.\env\Scripts\Activate.ps1
```

**Windows Command Prompt:**
```cmd
.\env\Scripts\activate.bat
```

**或者使用提供的快捷脚本:**
```powershell
.\activate_env.ps1
```
```cmd
activate_env.bat
```

### 2. 验证环境

运行测试脚本确认环境正常工作：
```bash
python test_environment.py
```

### 3. 运行训练脚本

**Keras版本 (传统实现):**
```bash
python training/rnn_train.py
```

**PyTorch版本 (现代实现):**
```bash
python torch/rnnoise/train_rnnoise.py
```

## 项目结构

```
rnnoise-main/
├── env/                    # Python虚拟环境
├── training/               # Keras训练脚本
│   ├── rnn_train.py       # 主训练脚本
│   ├── bin2hdf5.py        # 数据转换工具
│   └── dump_rnn.py        # 模型导出工具
├── torch/                  # PyTorch实现
│   └── rnnoise/
│       ├── train_rnnoise.py  # PyTorch训练脚本
│       ├── rnnoise.py        # 模型定义
│       └── dump_rnnoise_weights.py
├── src/                    # C源代码
├── data/                   # 训练数据
├── test_environment.py     # 环境测试脚本
├── activate_env.ps1        # PowerShell激活脚本
├── activate_env.bat        # CMD激活脚本
└── project_requirements.txt # 依赖列表
```

## 常见问题

### Q: 如何重新安装环境？
A: 删除`env`文件夹，然后运行：
```bash
python -m venv env
.\env\Scripts\Activate.ps1
pip install -r project_requirements.txt
```

### Q: 如何添加新的依赖？
A: 激活环境后使用pip安装：
```bash
.\env\Scripts\Activate.ps1
pip install 新包名
```

### Q: 训练需要GPU吗？
A: 当前安装的是CPU版本的PyTorch和TensorFlow。如需GPU支持，请安装对应的GPU版本。

### Q: 如何切换到GPU版本？
A: 激活环境后运行：
```bash
# 对于PyTorch
pip uninstall torch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 对于TensorFlow
pip uninstall tensorflow
pip install tensorflow-gpu
```

## 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.8+
2. 环境是否正确激活
3. 运行`python test_environment.py`检查依赖是否正常

## 更新日志

- 2025-07-31: 初始环境设置完成
  - 安装了所有核心依赖
  - 创建了测试和激活脚本
  - 验证了Keras和PyTorch两种实现
