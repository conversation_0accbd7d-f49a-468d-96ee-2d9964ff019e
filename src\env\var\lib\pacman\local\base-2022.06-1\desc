%NAME%
base

%VERSION%
2022.06-1

%BASE%
base

%DESC%
Minimal package set to define a basic MSYS2 installation

%URL%
https://www.msys2.org

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/7d84a7e0/**********)

%LICENSE%
GPL

%VALIDATION%
sha256
pgp

%DEPENDS%
bash
bash-completion
bsdtar
bzip2
coreutils
curl
dash
file
filesystem
findutils
gawk
getent
grep
gzip
inetutils
info
less
mintty
msys2-keyring
msys2-launcher
msys2-runtime
nano
pacman
pacman-contrib
pacman-mirrors
rebase
sed
tar
time
tzcode
util-linux
wget
which
zstd

