/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _WABAPI_H_
#define _WABAPI_H_

#ifdef __cplusplus
extern "C" {
#endif

  typedef struct _WABACTIONITEM *LPWABACTIONITEM;

#define CBIWABOBJECT sizeof(IWABOBJECT)
#define WAB_IWABOBJECT_METHODS(IPURE) MAPIMETHOD(GetLastError) (THIS_ HRESULT hResult,ULONG ulFlags,LPMAPIERROR *lppMAPIError) IPURE; MAPIMETHOD(AllocateBuffer) (THIS_ ULONG cbSize,LPVOID *lppBuffer) IPURE; MAPIMETHOD(AllocateMore) (THIS_ ULONG cbSize,LPVOID lpObject,LPVOID *lppBuffer) IPURE; MAPIMETHOD(FreeBuffer) (THIS_ LPVOID lpBuffer) IPURE; MAPIMETHOD(Backup) (THIS_ LPSTR lpFileName) IPURE; MAPIMETHOD(Import) (THIS_ LPSTR lpImportParam) IPURE; MAPIMETHOD(Find) (THIS_ LPADRBOOK lpIAB,HWND hWnd) IPURE; MAPIMETHOD(VCardDisplay) (THIS_ LPADRBOOK lpIAB,HWND hWnd,LPSTR lpszFileName) IPURE; MAPIMETHOD(LDAPUrl) (THIS_ LPADRBOOK lpIAB,HWND hWnd,ULONG ulFlags,LPSTR lpszURL,LPMAILUSER *lppMailUser) IPURE; MAPIMETHOD(VCardCreate) (THIS_ LPADRBOOK lpIAB,ULONG ulFlags,LPSTR lpszVCard,LPMAILUSER lpMailUser) IPURE; MAPIMETHOD(VCardRetrieve) (THIS_ LPADRBOOK lpIAB,ULONG ulFlags,LPSTR lpszVCard,LPMAILUSER *lppMailUser) IPURE; MAPIMETHOD(GetMe) (THIS_ LPADRBOOK lpIAB,ULONG ulFlags,DWORD *lpdwAction,SBinary *lpsbEID,ULONG ulParam) IPURE; MAPIMETHOD(SetMe) (THIS_ LPADRBOOK lpIAB,ULONG ulFlags,SBinary sbEID,ULONG ulParam) IPURE;

#undef INTERFACE
#define INTERFACE IWABObject
  DECLARE_MAPI_INTERFACE_(IWABObject,IUnknown) {
    BEGIN_INTERFACE
      MAPI_IUNKNOWN_METHODS(PURE)
      WAB_IWABOBJECT_METHODS(PURE)
  };

  DECLARE_MAPI_INTERFACE_PTR(IWABObject,LPWABOBJECT);

#undef INTERFACE
#define INTERFACE struct _IWABOBJECT

#undef METHOD_PREFIX
#define METHOD_PREFIX IWABOBJECT_
#undef LPVTBL_ELEM
#define LPVTBL_ELEM lpvtbl
#undef MAPIMETHOD_
#define MAPIMETHOD_(type,method) MAPIMETHOD_DECLARE(type,method,IWABOBJECT_)
  MAPI_IUNKNOWN_METHODS(IMPL)
    WAB_IWABOBJECT_METHODS(IMPL)
#undef MAPIMETHOD_
#define MAPIMETHOD_(type,method) MAPIMETHOD_TYPEDEF(type,method,IWABOBJECT_)
    MAPI_IUNKNOWN_METHODS(IMPL)
    WAB_IWABOBJECT_METHODS(IMPL)
#undef MAPIMETHOD_
#define MAPIMETHOD_(type,method) STDMETHOD_(type,method)

    DECLARE_MAPI_INTERFACE(IWABOBJECT_)
  {
    BEGIN_INTERFACE
      MAPI_IUNKNOWN_METHODS(IMPL)
      WAB_IWABOBJECT_METHODS(IMPL)
  };

#define WABOBJECT_LDAPURL_RETURN_MAILUSER 0x00000001
#define WABOBJECT_ME_NEW 0x00000001
#define WABOBJECT_ME_NOCREATE 0x00000002

#define WAB_VCARD_FILE 0x00000000
#define WAB_VCARD_STREAM 0x00000001

  typedef struct _tagWAB_PARAM {
    ULONG cbSize;
    HWND hwnd;
    LPSTR szFileName;
    ULONG ulFlags;
    GUID guidPSExt;
  } WAB_PARAM,*LPWAB_PARAM;

#define WAB_USE_OE_SENDMAIL 0x00000001
#define WAB_ENABLE_PROFILES 0x00400000

  STDMETHODIMP WABOpen(LPADRBOOK *lppAdrBook,LPWABOBJECT *lppWABObject,LPWAB_PARAM lpWP,DWORD Reserved2);

  typedef HRESULT (WINAPI WABOPEN)(LPADRBOOK *lppAdrBook,LPWABOBJECT *lppWABObject,LPWAB_PARAM lpWP,DWORD Reserved2);
  typedef WABOPEN *LPWABOPEN;

  STDMETHODIMP WABOpenEx(LPADRBOOK *lppAdrBook,LPWABOBJECT *lppWABObject,LPWAB_PARAM lpWP,DWORD Reserved,ALLOCATEBUFFER *fnAllocateBuffer,ALLOCATEMORE *fnAllocateMore,FREEBUFFER *fnFreeBuffer);

  typedef HRESULT (WINAPI WABOPENEX)(LPADRBOOK *lppAdrBook,LPWABOBJECT *lppWABObject,LPWAB_PARAM lpWP,DWORD Reserved,ALLOCATEBUFFER *fnAllocateBuffer,ALLOCATEMORE *fnAllocateMore,FREEBUFFER *fnFreeBuffer);
  typedef WABOPENEX *LPWABOPENEX;

  typedef struct _WABIMPORTPARAM {
    ULONG cbSize;
    LPADRBOOK lpAdrBook;
    HWND hWnd;
    ULONG ulFlags;
    LPSTR lpszFileName;
  } WABIMPORTPARAM,*LPWABIMPORTPARAM;

#define WAB_DISPLAY_LDAPURL 0x00000001
#define WAB_CONTEXT_ADRLIST 0x00000002
#define WAB_DISPLAY_ISNTDS 0x00000004

  typedef struct _WABEXTDISPLAY {
    ULONG cbSize;
    LPWABOBJECT lpWABObject;
    LPADRBOOK lpAdrBook;
    LPMAPIPROP lpPropObj;
    WINBOOL fReadOnly;
    WINBOOL fDataChanged;
    ULONG ulFlags;
    LPVOID lpv;
    LPTSTR lpsz;
  } WABEXTDISPLAY,*LPWABEXTDISPLAY;

#define WAB_IWABEXTINIT_METHODS(IPURE) MAPIMETHOD(Initialize) (THIS_ LPWABEXTDISPLAY lpWABExtDisplay) IPURE;
#undef INTERFACE
#define INTERFACE IWABExtInit
  DECLARE_MAPI_INTERFACE_(IWABExtInit,IUnknown) {
    BEGIN_INTERFACE
      MAPI_IUNKNOWN_METHODS(PURE)
      WAB_IWABEXTINIT_METHODS(PURE)
  };

  DECLARE_MAPI_INTERFACE_PTR(IWABExtInit,LPWABEXTINIT);
  DEFINE_GUID(IID_IWABExtInit,0xea22ebf0,0x87a4,0x11d1,0x9a,0xcf,0x0,0xa0,0xc9,0x1f,0x9c,0x8b);

#define WAB_DLL_NAME TEXT("WAB32.DLL")
#define WAB_DLL_PATH_KEY TEXT("Software\\Microsoft\\WAB\\DLLPath")

#ifdef __cplusplus
}
#endif
#endif
