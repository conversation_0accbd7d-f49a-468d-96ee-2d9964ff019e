.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_print" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_print \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_print(gnutls_x509_crl_t " crl ", gnutls_certificate_print_formats_t " format ", gnutls_datum_t * " out ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
The data to be printed
.IP "gnutls_certificate_print_formats_t format" 12
Indicate the format to use
.IP "gnutls_datum_t * out" 12
Newly allocated datum with null terminated string.
.SH "DESCRIPTION"
This function will pretty print a X.509 certificate revocation
list, suitable for display to a human.

The output  \fIout\fP needs to be deallocated using \fBgnutls_free()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
