/*** Autogenerated by WIDL 10.8 from include/windows.gaming.input.custom.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_gaming_input_custom_h__
#define __windows_gaming_input_custom_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink ABI::Windows::Gaming::Input::Custom::IGameControllerInputSink
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IGameControllerInputSink;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider ABI::Windows::Gaming::Input::Custom::IGameControllerProvider
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IGameControllerProvider;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink ABI::Windows::Gaming::Input::Custom::IHidGameControllerInputSink
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IHidGameControllerInputSink;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider ABI::Windows::Gaming::Input::Custom::IHidGameControllerProvider
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IHidGameControllerProvider;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink ABI::Windows::Gaming::Input::Custom::IXusbGameControllerInputSink
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IXusbGameControllerInputSink;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider ABI::Windows::Gaming::Input::Custom::IXusbGameControllerProvider
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IXusbGameControllerProvider;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory ABI::Windows::Gaming::Input::Custom::ICustomGameControllerFactory
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface ICustomGameControllerFactory;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics ABI::Windows::Gaming::Input::Custom::IGameControllerFactoryManagerStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IGameControllerFactoryManagerStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 ABI::Windows::Gaming::Input::Custom::IGameControllerFactoryManagerStatics2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IGameControllerFactoryManagerStatics2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerFactoryManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerFactoryManager_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    class GameControllerFactoryManager;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerFactoryManager __x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerFactoryManager;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerFactoryManager_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CHidGameControllerProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CHidGameControllerProvider_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    class HidGameControllerProvider;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CHidGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CHidGameControllerProvider;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CCustom_CHidGameControllerProvider_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CXusbGameControllerProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CXusbGameControllerProvider_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    class XusbGameControllerProvider;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbGameControllerProvider;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGaming_CInput_CCustom_CXusbGameControllerProvider_FWD_DEFINED__ */

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.gaming.input.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceSubtype __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceSubtype;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceType __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceType;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerVersionInfo __x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerVersionInfo;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    typedef struct GameControllerVersionInfo GameControllerVersionInfo;
                }
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink ABI::Windows::Gaming::Input::Custom::IGameControllerInputSink
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IGameControllerInputSink;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider ABI::Windows::Gaming::Input::Custom::IGameControllerProvider
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IGameControllerProvider;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink ABI::Windows::Gaming::Input::Custom::IHidGameControllerInputSink
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IHidGameControllerInputSink;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider ABI::Windows::Gaming::Input::Custom::IHidGameControllerProvider
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IHidGameControllerProvider;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink ABI::Windows::Gaming::Input::Custom::IXusbGameControllerInputSink
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IXusbGameControllerInputSink;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider ABI::Windows::Gaming::Input::Custom::IXusbGameControllerProvider
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IXusbGameControllerProvider;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory ABI::Windows::Gaming::Input::Custom::ICustomGameControllerFactory
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface ICustomGameControllerFactory;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics ABI::Windows::Gaming::Input::Custom::IGameControllerFactoryManagerStatics
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IGameControllerFactoryManagerStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 ABI::Windows::Gaming::Input::Custom::IGameControllerFactoryManagerStatics2
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    interface IGameControllerFactoryManagerStatics2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    enum XusbDeviceSubtype {
                        XusbDeviceSubtype_Unknown = 0,
                        XusbDeviceSubtype_Gamepad = 1,
                        XusbDeviceSubtype_ArcadePad = 2,
                        XusbDeviceSubtype_ArcadeStick = 3,
                        XusbDeviceSubtype_FlightStick = 4,
                        XusbDeviceSubtype_Wheel = 5,
                        XusbDeviceSubtype_Guitar = 6,
                        XusbDeviceSubtype_GuitarAlternate = 7,
                        XusbDeviceSubtype_GuitarBass = 8,
                        XusbDeviceSubtype_DrumKit = 9,
                        XusbDeviceSubtype_DancePad = 10
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceSubtype {
    XusbDeviceSubtype_Unknown = 0,
    XusbDeviceSubtype_Gamepad = 1,
    XusbDeviceSubtype_ArcadePad = 2,
    XusbDeviceSubtype_ArcadeStick = 3,
    XusbDeviceSubtype_FlightStick = 4,
    XusbDeviceSubtype_Wheel = 5,
    XusbDeviceSubtype_Guitar = 6,
    XusbDeviceSubtype_GuitarAlternate = 7,
    XusbDeviceSubtype_GuitarBass = 8,
    XusbDeviceSubtype_DrumKit = 9,
    XusbDeviceSubtype_DancePad = 10
};
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define XusbDeviceSubtype __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceSubtype
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    enum XusbDeviceType {
                        XusbDeviceType_Unknown = 0,
                        XusbDeviceType_Gamepad = 1
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceType {
    XusbDeviceType_Unknown = 0,
    XusbDeviceType_Gamepad = 1
};
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define XusbDeviceType __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceType
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    struct GameControllerVersionInfo {
                        UINT16 Major;
                        UINT16 Minor;
                        UINT16 Build;
                        UINT16 Revision;
                    };
                }
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerVersionInfo {
    UINT16 Major;
    UINT16 Minor;
    UINT16 Build;
    UINT16 Revision;
};
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define GameControllerVersionInfo __x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerVersionInfo
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
/*****************************************************************************
 * IGameControllerInputSink interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink, 0x1ff6f922, 0xc640, 0x4c78, 0xa8,0x20, 0x9a,0x71,0x5c,0x55,0x8b,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("1ff6f922-c640-4c78-a820-9a715c558bcb")
                    IGameControllerInputSink : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE OnInputResumed(
                            UINT64 timestamp) = 0;

                        virtual HRESULT STDMETHODCALLTYPE OnInputSuspended(
                            UINT64 timestamp) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink, 0x1ff6f922, 0xc640, 0x4c78, 0xa8,0x20, 0x9a,0x71,0x5c,0x55,0x8b,0xcb)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink *This,
        TrustLevel *trustLevel);

    /*** IGameControllerInputSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnInputResumed)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink *This,
        UINT64 timestamp);

    HRESULT (STDMETHODCALLTYPE *OnInputSuspended)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink *This,
        UINT64 timestamp);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSinkVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGameControllerInputSink methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_OnInputResumed(This,timestamp) (This)->lpVtbl->OnInputResumed(This,timestamp)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_OnInputSuspended(This,timestamp) (This)->lpVtbl->OnInputSuspended(This,timestamp)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGameControllerInputSink methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_OnInputResumed(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink* This,UINT64 timestamp) {
    return This->lpVtbl->OnInputResumed(This,timestamp);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_OnInputSuspended(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink* This,UINT64 timestamp) {
    return This->lpVtbl->OnInputSuspended(This,timestamp);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_IGameControllerInputSink IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink
#define IGameControllerInputSinkVtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSinkVtbl
#define IGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink
#define IGameControllerInputSink_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_QueryInterface
#define IGameControllerInputSink_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_AddRef
#define IGameControllerInputSink_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_Release
#define IGameControllerInputSink_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetIids
#define IGameControllerInputSink_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetRuntimeClassName
#define IGameControllerInputSink_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_GetTrustLevel
#define IGameControllerInputSink_OnInputResumed __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_OnInputResumed
#define IGameControllerInputSink_OnInputSuspended __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_OnInputSuspended
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerInputSink_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IGameControllerProvider interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider, 0xe6d73982, 0x2996, 0x4559, 0xb1,0x6c, 0x3e,0x57,0xd4,0x6e,0x58,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("e6d73982-2996-4559-b16c-3e57d46e58d6")
                    IGameControllerProvider : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_FirmwareVersionInfo(
                            ABI::Windows::Gaming::Input::Custom::GameControllerVersionInfo *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_HardwareProductId(
                            UINT16 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_HardwareVendorId(
                            UINT16 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_HardwareVersionInfo(
                            ABI::Windows::Gaming::Input::Custom::GameControllerVersionInfo *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_IsConnected(
                            boolean *value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider, 0xe6d73982, 0x2996, 0x4559, 0xb1,0x6c, 0x3e,0x57,0xd4,0x6e,0x58,0xd6)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        TrustLevel *trustLevel);

    /*** IGameControllerProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_FirmwareVersionInfo)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerVersionInfo *value);

    HRESULT (STDMETHODCALLTYPE *get_HardwareProductId)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        UINT16 *value);

    HRESULT (STDMETHODCALLTYPE *get_HardwareVendorId)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        UINT16 *value);

    HRESULT (STDMETHODCALLTYPE *get_HardwareVersionInfo)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerVersionInfo *value);

    HRESULT (STDMETHODCALLTYPE *get_IsConnected)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProviderVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGameControllerProvider methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_FirmwareVersionInfo(This,value) (This)->lpVtbl->get_FirmwareVersionInfo(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareProductId(This,value) (This)->lpVtbl->get_HardwareProductId(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareVendorId(This,value) (This)->lpVtbl->get_HardwareVendorId(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareVersionInfo(This,value) (This)->lpVtbl->get_HardwareVersionInfo(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_IsConnected(This,value) (This)->lpVtbl->get_IsConnected(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGameControllerProvider methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_FirmwareVersionInfo(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,__x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerVersionInfo *value) {
    return This->lpVtbl->get_FirmwareVersionInfo(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareProductId(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,UINT16 *value) {
    return This->lpVtbl->get_HardwareProductId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareVendorId(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,UINT16 *value) {
    return This->lpVtbl->get_HardwareVendorId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareVersionInfo(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,__x_ABI_CWindows_CGaming_CInput_CCustom_CGameControllerVersionInfo *value) {
    return This->lpVtbl->get_HardwareVersionInfo(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_IsConnected(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider* This,boolean *value) {
    return This->lpVtbl->get_IsConnected(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_IGameControllerProvider IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider
#define IGameControllerProviderVtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProviderVtbl
#define IGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider
#define IGameControllerProvider_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_QueryInterface
#define IGameControllerProvider_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_AddRef
#define IGameControllerProvider_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_Release
#define IGameControllerProvider_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetIids
#define IGameControllerProvider_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetRuntimeClassName
#define IGameControllerProvider_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_GetTrustLevel
#define IGameControllerProvider_get_FirmwareVersionInfo __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_FirmwareVersionInfo
#define IGameControllerProvider_get_HardwareProductId __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareProductId
#define IGameControllerProvider_get_HardwareVendorId __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareVendorId
#define IGameControllerProvider_get_HardwareVersionInfo __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_HardwareVersionInfo
#define IGameControllerProvider_get_IsConnected __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_get_IsConnected
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IHidGameControllerInputSink interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink, 0xf754c322, 0x182d, 0x40e4, 0xa1,0x26, 0xfc,0xee,0x4f,0xfa,0x1e,0x31);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("f754c322-182d-40e4-a126-fcee4ffa1e31")
                    IHidGameControllerInputSink : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE OnInputReportReceived(
                            UINT64 timestamp,
                            BYTE id,
                            UINT32 report_len,
                            BYTE *report_buf) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink, 0xf754c322, 0x182d, 0x40e4, 0xa1,0x26, 0xfc,0xee,0x4f,0xfa,0x1e,0x31)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink *This,
        TrustLevel *trustLevel);

    /*** IHidGameControllerInputSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnInputReportReceived)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink *This,
        UINT64 timestamp,
        BYTE id,
        UINT32 report_len,
        BYTE *report_buf);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSinkVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHidGameControllerInputSink methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_OnInputReportReceived(This,timestamp,id,report_len,report_buf) (This)->lpVtbl->OnInputReportReceived(This,timestamp,id,report_len,report_buf)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHidGameControllerInputSink methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_OnInputReportReceived(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink* This,UINT64 timestamp,BYTE id,UINT32 report_len,BYTE *report_buf) {
    return This->lpVtbl->OnInputReportReceived(This,timestamp,id,report_len,report_buf);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_IHidGameControllerInputSink IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink
#define IHidGameControllerInputSinkVtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSinkVtbl
#define IHidGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink
#define IHidGameControllerInputSink_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_QueryInterface
#define IHidGameControllerInputSink_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_AddRef
#define IHidGameControllerInputSink_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_Release
#define IHidGameControllerInputSink_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetIids
#define IHidGameControllerInputSink_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetRuntimeClassName
#define IHidGameControllerInputSink_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_GetTrustLevel
#define IHidGameControllerInputSink_OnInputReportReceived __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_OnInputReportReceived
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerInputSink_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IHidGameControllerProvider interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider, 0x95ce3af4, 0xabf0, 0x4b68, 0xa0,0x81, 0x3b,0x7d,0xe7,0x3f,0xf0,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("95ce3af4-abf0-4b68-a081-3b7de73ff0e7")
                    IHidGameControllerProvider : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_UsageId(
                            UINT16 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_UsagePage(
                            UINT16 *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetFeatureReport(
                            BYTE id,
                            UINT32 report_len,
                            BYTE *report_buf) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SendFeatureReport(
                            BYTE id,
                            UINT32 report_len,
                            BYTE *report_buf) = 0;

                        virtual HRESULT STDMETHODCALLTYPE SendOutputReport(
                            BYTE id,
                            UINT32 report_len,
                            BYTE *report_buf) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider, 0x95ce3af4, 0xabf0, 0x4b68, 0xa0,0x81, 0x3b,0x7d,0xe7,0x3f,0xf0,0xe7)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        TrustLevel *trustLevel);

    /*** IHidGameControllerProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UsageId)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        UINT16 *value);

    HRESULT (STDMETHODCALLTYPE *get_UsagePage)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        UINT16 *value);

    HRESULT (STDMETHODCALLTYPE *GetFeatureReport)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        BYTE id,
        UINT32 report_len,
        BYTE *report_buf);

    HRESULT (STDMETHODCALLTYPE *SendFeatureReport)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        BYTE id,
        UINT32 report_len,
        BYTE *report_buf);

    HRESULT (STDMETHODCALLTYPE *SendOutputReport)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider *This,
        BYTE id,
        UINT32 report_len,
        BYTE *report_buf);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProviderVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHidGameControllerProvider methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_get_UsageId(This,value) (This)->lpVtbl->get_UsageId(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_get_UsagePage(This,value) (This)->lpVtbl->get_UsagePage(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetFeatureReport(This,id,report_len,report_buf) (This)->lpVtbl->GetFeatureReport(This,id,report_len,report_buf)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_SendFeatureReport(This,id,report_len,report_buf) (This)->lpVtbl->SendFeatureReport(This,id,report_len,report_buf)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_SendOutputReport(This,id,report_len,report_buf) (This)->lpVtbl->SendOutputReport(This,id,report_len,report_buf)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHidGameControllerProvider methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_get_UsageId(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,UINT16 *value) {
    return This->lpVtbl->get_UsageId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_get_UsagePage(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,UINT16 *value) {
    return This->lpVtbl->get_UsagePage(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetFeatureReport(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,BYTE id,UINT32 report_len,BYTE *report_buf) {
    return This->lpVtbl->GetFeatureReport(This,id,report_len,report_buf);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_SendFeatureReport(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,BYTE id,UINT32 report_len,BYTE *report_buf) {
    return This->lpVtbl->SendFeatureReport(This,id,report_len,report_buf);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_SendOutputReport(__x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider* This,BYTE id,UINT32 report_len,BYTE *report_buf) {
    return This->lpVtbl->SendOutputReport(This,id,report_len,report_buf);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_IHidGameControllerProvider IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider
#define IHidGameControllerProviderVtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProviderVtbl
#define IHidGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider
#define IHidGameControllerProvider_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_QueryInterface
#define IHidGameControllerProvider_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_AddRef
#define IHidGameControllerProvider_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_Release
#define IHidGameControllerProvider_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetIids
#define IHidGameControllerProvider_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetRuntimeClassName
#define IHidGameControllerProvider_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetTrustLevel
#define IHidGameControllerProvider_get_UsageId __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_get_UsageId
#define IHidGameControllerProvider_get_UsagePage __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_get_UsagePage
#define IHidGameControllerProvider_GetFeatureReport __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_GetFeatureReport
#define IHidGameControllerProvider_SendFeatureReport __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_SendFeatureReport
#define IHidGameControllerProvider_SendOutputReport __x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_SendOutputReport
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CIHidGameControllerProvider_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IXusbGameControllerInputSink interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink, 0xb2ac1d95, 0x6ecb, 0x42b3, 0x8a,0xab, 0x02,0x54,0x01,0xca,0x47,0x12);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("b2ac1d95-6ecb-42b3-8aab-025401ca4712")
                    IXusbGameControllerInputSink : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE OnInputReceived(
                            UINT64 timestamp,
                            BYTE id,
                            UINT32 report_len,
                            BYTE *report_buf) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink, 0xb2ac1d95, 0x6ecb, 0x42b3, 0x8a,0xab, 0x02,0x54,0x01,0xca,0x47,0x12)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink *This,
        TrustLevel *trustLevel);

    /*** IXusbGameControllerInputSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnInputReceived)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink *This,
        UINT64 timestamp,
        BYTE id,
        UINT32 report_len,
        BYTE *report_buf);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSinkVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXusbGameControllerInputSink methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_OnInputReceived(This,timestamp,id,report_len,report_buf) (This)->lpVtbl->OnInputReceived(This,timestamp,id,report_len,report_buf)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXusbGameControllerInputSink methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_OnInputReceived(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink* This,UINT64 timestamp,BYTE id,UINT32 report_len,BYTE *report_buf) {
    return This->lpVtbl->OnInputReceived(This,timestamp,id,report_len,report_buf);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_IXusbGameControllerInputSink IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink
#define IXusbGameControllerInputSinkVtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSinkVtbl
#define IXusbGameControllerInputSink __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink
#define IXusbGameControllerInputSink_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_QueryInterface
#define IXusbGameControllerInputSink_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_AddRef
#define IXusbGameControllerInputSink_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_Release
#define IXusbGameControllerInputSink_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetIids
#define IXusbGameControllerInputSink_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetRuntimeClassName
#define IXusbGameControllerInputSink_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_GetTrustLevel
#define IXusbGameControllerInputSink_OnInputReceived __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_OnInputReceived
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerInputSink_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IXusbGameControllerProvider interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider, 0x6e2971eb, 0x0efb, 0x48b4, 0x80,0x8b, 0x83,0x76,0x43,0xb2,0xf2,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("6e2971eb-0efb-48b4-808b-837643b2f216")
                    IXusbGameControllerProvider : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE SetVibration(
                            DOUBLE rumble_intensity,
                            DOUBLE buzz_intensity) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider, 0x6e2971eb, 0x0efb, 0x48b4, 0x80,0x8b, 0x83,0x76,0x43,0xb2,0xf2,0x16)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider *This,
        TrustLevel *trustLevel);

    /*** IXusbGameControllerProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetVibration)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider *This,
        DOUBLE rumble_intensity,
        DOUBLE buzz_intensity);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProviderVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IXusbGameControllerProvider methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_SetVibration(This,rumble_intensity,buzz_intensity) (This)->lpVtbl->SetVibration(This,rumble_intensity,buzz_intensity)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IXusbGameControllerProvider methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_SetVibration(__x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider* This,DOUBLE rumble_intensity,DOUBLE buzz_intensity) {
    return This->lpVtbl->SetVibration(This,rumble_intensity,buzz_intensity);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_IXusbGameControllerProvider IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider
#define IXusbGameControllerProviderVtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProviderVtbl
#define IXusbGameControllerProvider __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider
#define IXusbGameControllerProvider_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_QueryInterface
#define IXusbGameControllerProvider_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_AddRef
#define IXusbGameControllerProvider_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_Release
#define IXusbGameControllerProvider_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetIids
#define IXusbGameControllerProvider_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetRuntimeClassName
#define IXusbGameControllerProvider_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_GetTrustLevel
#define IXusbGameControllerProvider_SetVibration __x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_SetVibration
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CIXusbGameControllerProvider_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * ICustomGameControllerFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory, 0x69a0ae5e, 0x758e, 0x4cbe, 0xac,0xe6, 0x62,0x15,0x5f,0xe9,0x12,0x6f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("69a0ae5e-758e-4cbe-ace6-62155fe9126f")
                    ICustomGameControllerFactory : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE CreateGameController(
                            ABI::Windows::Gaming::Input::Custom::IGameControllerProvider *provider,
                            IInspectable **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE OnGameControllerAdded(
                            ABI::Windows::Gaming::Input::IGameController *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE OnGameControllerRemoved(
                            ABI::Windows::Gaming::Input::IGameController *value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory, 0x69a0ae5e, 0x758e, 0x4cbe, 0xac,0xe6, 0x62,0x15,0x5f,0xe9,0x12,0x6f)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This,
        TrustLevel *trustLevel);

    /*** ICustomGameControllerFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateGameController)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *provider,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *OnGameControllerAdded)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *value);

    HRESULT (STDMETHODCALLTYPE *OnGameControllerRemoved)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *This,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactoryVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICustomGameControllerFactory methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_CreateGameController(This,provider,value) (This)->lpVtbl->CreateGameController(This,provider,value)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_OnGameControllerAdded(This,value) (This)->lpVtbl->OnGameControllerAdded(This,value)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_OnGameControllerRemoved(This,value) (This)->lpVtbl->OnGameControllerRemoved(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICustomGameControllerFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_CreateGameController(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This,__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerProvider *provider,IInspectable **value) {
    return This->lpVtbl->CreateGameController(This,provider,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_OnGameControllerAdded(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *value) {
    return This->lpVtbl->OnGameControllerAdded(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_OnGameControllerRemoved(__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory* This,__x_ABI_CWindows_CGaming_CInput_CIGameController *value) {
    return This->lpVtbl->OnGameControllerRemoved(This,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_ICustomGameControllerFactory IID___x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory
#define ICustomGameControllerFactoryVtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactoryVtbl
#define ICustomGameControllerFactory __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory
#define ICustomGameControllerFactory_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_QueryInterface
#define ICustomGameControllerFactory_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_AddRef
#define ICustomGameControllerFactory_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_Release
#define ICustomGameControllerFactory_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetIids
#define ICustomGameControllerFactory_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetRuntimeClassName
#define ICustomGameControllerFactory_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_GetTrustLevel
#define ICustomGameControllerFactory_CreateGameController __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_CreateGameController
#define ICustomGameControllerFactory_OnGameControllerAdded __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_OnGameControllerAdded
#define ICustomGameControllerFactory_OnGameControllerRemoved __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_OnGameControllerRemoved
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IGameControllerFactoryManagerStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics, 0x36cb66e3, 0xd0a1, 0x4986, 0xa2,0x4c, 0x40,0xb1,0x37,0xde,0xba,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("36cb66e3-d0a1-4986-a24c-40b137deba9e")
                    IGameControllerFactoryManagerStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE RegisterCustomFactoryForGipInterface(
                            ABI::Windows::Gaming::Input::Custom::ICustomGameControllerFactory *factory,
                            GUID interfaceId) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RegisterCustomFactoryForHardwareId(
                            ABI::Windows::Gaming::Input::Custom::ICustomGameControllerFactory *factory,
                            UINT16 vendor_id,
                            UINT16 product_id) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RegisterCustomFactoryForXusbType(
                            ABI::Windows::Gaming::Input::Custom::ICustomGameControllerFactory *factory,
                            ABI::Windows::Gaming::Input::Custom::XusbDeviceType type,
                            ABI::Windows::Gaming::Input::Custom::XusbDeviceSubtype subtype) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics, 0x36cb66e3, 0xd0a1, 0x4986, 0xa2,0x4c, 0x40,0xb1,0x37,0xde,0xba,0x9e)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This,
        TrustLevel *trustLevel);

    /*** IGameControllerFactoryManagerStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterCustomFactoryForGipInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *factory,
        GUID interfaceId);

    HRESULT (STDMETHODCALLTYPE *RegisterCustomFactoryForHardwareId)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *factory,
        UINT16 vendor_id,
        UINT16 product_id);

    HRESULT (STDMETHODCALLTYPE *RegisterCustomFactoryForXusbType)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics *This,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *factory,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceType type,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceSubtype subtype);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStaticsVtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGameControllerFactoryManagerStatics methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForGipInterface(This,factory,interfaceId) (This)->lpVtbl->RegisterCustomFactoryForGipInterface(This,factory,interfaceId)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForHardwareId(This,factory,vendor_id,product_id) (This)->lpVtbl->RegisterCustomFactoryForHardwareId(This,factory,vendor_id,product_id)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForXusbType(This,factory,type,subtype) (This)->lpVtbl->RegisterCustomFactoryForXusbType(This,factory,type,subtype)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGameControllerFactoryManagerStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForGipInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This,__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *factory,GUID interfaceId) {
    return This->lpVtbl->RegisterCustomFactoryForGipInterface(This,factory,interfaceId);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForHardwareId(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This,__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *factory,UINT16 vendor_id,UINT16 product_id) {
    return This->lpVtbl->RegisterCustomFactoryForHardwareId(This,factory,vendor_id,product_id);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForXusbType(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics* This,__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *factory,__x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceType type,__x_ABI_CWindows_CGaming_CInput_CCustom_CXusbDeviceSubtype subtype) {
    return This->lpVtbl->RegisterCustomFactoryForXusbType(This,factory,type,subtype);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_IGameControllerFactoryManagerStatics IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics
#define IGameControllerFactoryManagerStaticsVtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStaticsVtbl
#define IGameControllerFactoryManagerStatics __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics
#define IGameControllerFactoryManagerStatics_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_QueryInterface
#define IGameControllerFactoryManagerStatics_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_AddRef
#define IGameControllerFactoryManagerStatics_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_Release
#define IGameControllerFactoryManagerStatics_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetIids
#define IGameControllerFactoryManagerStatics_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetRuntimeClassName
#define IGameControllerFactoryManagerStatics_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_GetTrustLevel
#define IGameControllerFactoryManagerStatics_RegisterCustomFactoryForGipInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForGipInterface
#define IGameControllerFactoryManagerStatics_RegisterCustomFactoryForHardwareId __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForHardwareId
#define IGameControllerFactoryManagerStatics_RegisterCustomFactoryForXusbType __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_RegisterCustomFactoryForXusbType
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IGameControllerFactoryManagerStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2, 0xeace5644, 0x19df, 0x4115, 0xb3,0x2a, 0x27,0x93,0xe2,0xae,0xa3,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Gaming {
            namespace Input {
                namespace Custom {
                    MIDL_INTERFACE("eace5644-19df-4115-b32a-2793e2aea3bb")
                    IGameControllerFactoryManagerStatics2 : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE TryGetFactoryControllerFromGameController(
                            ABI::Windows::Gaming::Input::Custom::ICustomGameControllerFactory *factory,
                            ABI::Windows::Gaming::Input::IGameController *controller,
                            ABI::Windows::Gaming::Input::IGameController **value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2, 0xeace5644, 0x19df, 0x4115, 0xb3,0x2a, 0x27,0x93,0xe2,0xae,0xa3,0xbb)
#endif
#else
typedef struct __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 *This,
        TrustLevel *trustLevel);

    /*** IGameControllerFactoryManagerStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *TryGetFactoryControllerFromGameController)(
        __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 *This,
        __x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *factory,
        __x_ABI_CWindows_CGaming_CInput_CIGameController *controller,
        __x_ABI_CWindows_CGaming_CInput_CIGameController **value);

    END_INTERFACE
} __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2Vtbl;

interface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGameControllerFactoryManagerStatics2 methods ***/
#define __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_TryGetFactoryControllerFromGameController(This,factory,controller,value) (This)->lpVtbl->TryGetFactoryControllerFromGameController(This,factory,controller,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_QueryInterface(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_AddRef(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_Release(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetIids(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetTrustLevel(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGameControllerFactoryManagerStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_TryGetFactoryControllerFromGameController(__x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2* This,__x_ABI_CWindows_CGaming_CInput_CCustom_CICustomGameControllerFactory *factory,__x_ABI_CWindows_CGaming_CInput_CIGameController *controller,__x_ABI_CWindows_CGaming_CInput_CIGameController **value) {
    return This->lpVtbl->TryGetFactoryControllerFromGameController(This,factory,controller,value);
}
#endif
#ifdef WIDL_using_Windows_Gaming_Input_Custom
#define IID_IGameControllerFactoryManagerStatics2 IID___x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2
#define IGameControllerFactoryManagerStatics2Vtbl __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2Vtbl
#define IGameControllerFactoryManagerStatics2 __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2
#define IGameControllerFactoryManagerStatics2_QueryInterface __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_QueryInterface
#define IGameControllerFactoryManagerStatics2_AddRef __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_AddRef
#define IGameControllerFactoryManagerStatics2_Release __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_Release
#define IGameControllerFactoryManagerStatics2_GetIids __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetIids
#define IGameControllerFactoryManagerStatics2_GetRuntimeClassName __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetRuntimeClassName
#define IGameControllerFactoryManagerStatics2_GetTrustLevel __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_GetTrustLevel
#define IGameControllerFactoryManagerStatics2_TryGetFactoryControllerFromGameController __x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_TryGetFactoryControllerFromGameController
#endif /* WIDL_using_Windows_Gaming_Input_Custom */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGaming_CInput_CCustom_CIGameControllerFactoryManagerStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Gaming.Input.Custom.GameControllerFactoryManager
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_Custom_GameControllerFactoryManager_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_Custom_GameControllerFactoryManager_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_Custom_GameControllerFactoryManager[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','C','u','s','t','o','m','.','G','a','m','e','C','o','n','t','r','o','l','l','e','r','F','a','c','t','o','r','y','M','a','n','a','g','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Custom_GameControllerFactoryManager[] = L"Windows.Gaming.Input.Custom.GameControllerFactoryManager";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Custom_GameControllerFactoryManager[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','C','u','s','t','o','m','.','G','a','m','e','C','o','n','t','r','o','l','l','e','r','F','a','c','t','o','r','y','M','a','n','a','g','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_Custom_GameControllerFactoryManager_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.Gaming.Input.Custom.HidGameControllerProvider
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_Custom_HidGameControllerProvider_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_Custom_HidGameControllerProvider_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_Custom_HidGameControllerProvider[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','C','u','s','t','o','m','.','H','i','d','G','a','m','e','C','o','n','t','r','o','l','l','e','r','P','r','o','v','i','d','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Custom_HidGameControllerProvider[] = L"Windows.Gaming.Input.Custom.HidGameControllerProvider";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Custom_HidGameControllerProvider[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','C','u','s','t','o','m','.','H','i','d','G','a','m','e','C','o','n','t','r','o','l','l','e','r','P','r','o','v','i','d','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_Custom_HidGameControllerProvider_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Gaming.Input.Custom.XusbGameControllerProvider
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_Gaming_Input_Custom_XusbGameControllerProvider_DEFINED
#define RUNTIMECLASS_Windows_Gaming_Input_Custom_XusbGameControllerProvider_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Gaming_Input_Custom_XusbGameControllerProvider[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','C','u','s','t','o','m','.','X','u','s','b','G','a','m','e','C','o','n','t','r','o','l','l','e','r','P','r','o','v','i','d','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Custom_XusbGameControllerProvider[] = L"Windows.Gaming.Input.Custom.XusbGameControllerProvider";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Gaming_Input_Custom_XusbGameControllerProvider[] = {'W','i','n','d','o','w','s','.','G','a','m','i','n','g','.','I','n','p','u','t','.','C','u','s','t','o','m','.','X','u','s','b','G','a','m','e','C','o','n','t','r','o','l','l','e','r','P','r','o','v','i','d','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Gaming_Input_Custom_XusbGameControllerProvider_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_gaming_input_custom_h__ */
