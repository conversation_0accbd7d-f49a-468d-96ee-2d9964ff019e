.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_tlsfeatures_check_crt" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_tlsfeatures_check_crt \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "unsigned gnutls_x509_tlsfeatures_check_crt(gnutls_x509_tlsfeatures_t " feat ", gnutls_x509_crt_t " cert ");"
.SH ARGUMENTS
.IP "gnutls_x509_tlsfeatures_t feat" 12
a set of TLSFeatures
.IP "gnutls_x509_crt_t cert" 12
the certificate to be checked
.SH "DESCRIPTION"
This function will check the provided certificate against the TLSFeatures
set in  \fIfeat\fP using the RFC7633 p.4.2.2 rules. It will check whether the certificate
contains the features in  \fIfeat\fP or a superset.
.SH "RETURNS"
non\-zero if the provided certificate complies, and zero otherwise.
.SH "SINCE"
3.5.1
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
