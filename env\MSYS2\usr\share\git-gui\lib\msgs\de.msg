set ::msgcat::header "Project-Id-Version: git-gui\nReport-Msgid-Bugs-To: \nPO-Revision-Date: 2020-02-09 22:40+0100\nLast-Translator: <PERSON> <<EMAIL>>\nLanguage-Team: German\nLanguage: de_DE\nMIME-Version: 1.0\nContent-Type: text/plain; charset=UTF-8\nContent-Transfer-Encoding: 8bit\n"
::msgcat::mcset de "Invalid font specified in %s:" "Ung\u00fcltige Zeichensatz-Angabe in %s:"
::msgcat::mcset de "Main Font" "Programmschriftart"
::msgcat::mcset de "Diff/Console Font" "Vergleich-Schriftart"
::msgcat::mcset de "git-gui: fatal error" "git-gui: Programmfehler"
::msgcat::mcset de "Cannot find git in PATH." "Git kann im PATH nicht gefunden werden."
::msgcat::mcset de "Cannot parse Git version string:" "Git Versionsangabe kann nicht erkannt werden:"
::msgcat::mcset de "Git version cannot be determined.\n\n%s claims it is version '%s'.\n\n%s requires at least Git 1.5.0 or later.\n\nAssume '%s' is version 1.5.0?\n" "Die Version von Git kann nicht bestimmt werden.\n\n\u00bb%s\u00ab behauptet, es sei Version \u00bb%s\u00ab.\n\n%s ben\u00f6tigt mindestens Git 1.5.0 oder h\u00f6her.\n\nSoll angenommen werden, \u00bb%s\u00ab sei Version 1.5.0?\n"
::msgcat::mcset de "Git directory not found:" "Git-Verzeichnis nicht gefunden:"
::msgcat::mcset de "Cannot move to top of working directory:" "Es konnte nicht in das oberste Verzeichnis der Arbeitskopie gewechselt werden:"
::msgcat::mcset de "Cannot use bare repository:" "Blo\u00dfes Repository kann nicht benutzt werden:"
::msgcat::mcset de "No working directory" "Kein Arbeitsverzeichnis"
::msgcat::mcset de "Refreshing file status..." "Dateistatus aktualisieren..."
::msgcat::mcset de "Scanning for modified files ..." "Nach ge\u00e4nderten Dateien suchen..."
::msgcat::mcset de "Calling prepare-commit-msg hook..." "Aufrufen des \u00bbprepare-commit-msg hook\u00ab..."
::msgcat::mcset de "Commit declined by prepare-commit-msg hook." "Commit abgelehnt durch \u00bbprepare-commit-msg hook\u00ab."
::msgcat::mcset de "Ready." "Bereit."
::msgcat::mcset de "Display limit (gui.maxfilesdisplayed = %s) reached, not showing all %s files." "Anzeigelimit erreicht (gui.maxfilesdisplayed = %s) f\u00fcr Anzahl Eintr\u00e4ge. Es werden nicht alle %s Dateien gezeigt."
::msgcat::mcset de "Unmodified" "Unver\u00e4ndert"
::msgcat::mcset de "Modified, not staged" "Ver\u00e4ndert, nicht bereitgestellt"
::msgcat::mcset de "Staged for commit" "Bereitgestellt zum Committen"
::msgcat::mcset de "Portions staged for commit" "Teilweise bereitgestellt zum Committen"
::msgcat::mcset de "Staged for commit, missing" "Bereitgestellt zum Committen, fehlend"
::msgcat::mcset de "File type changed, not staged" "Dateityp ge\u00e4ndert, nicht bereitgestellt"
::msgcat::mcset de "File type changed, old type staged for commit" "Dateityp ge\u00e4ndert, alter Dateityp bereitgestellt"
::msgcat::mcset de "File type changed, staged" "Dateityp ge\u00e4ndert, bereitgestellt"
::msgcat::mcset de "File type change staged, modification not staged" "Dateityp-\u00c4nderung bereitgestellt, Inhalts\u00e4nderung nicht bereitgestellt"
::msgcat::mcset de "File type change staged, file missing" "Dateityp-\u00c4nderung bereitgestellt, Datei gel\u00f6scht"
::msgcat::mcset de "Untracked, not staged" "Unversioniert, nicht bereitgestellt"
::msgcat::mcset de "Missing" "Fehlend"
::msgcat::mcset de "Staged for removal" "Bereitgestellt zum L\u00f6schen"
::msgcat::mcset de "Staged for removal, still present" "Bereitgestellt zum L\u00f6schen, trotzdem vorhanden"
::msgcat::mcset de "Requires merge resolution" "Konfliktaufl\u00f6sung n\u00f6tig"
::msgcat::mcset de "Couldn't find gitk in PATH" "Gitk kann im PATH nicht gefunden werden."
::msgcat::mcset de "Starting %s... please wait..." "%s wird gestartet... bitte warten."
::msgcat::mcset de "Couldn't find git gui in PATH" "\u00bbGit gui\u00ab kann im PATH nicht gefunden werden."
::msgcat::mcset de "Repository" "Repository"
::msgcat::mcset de "Edit" "Bearbeiten"
::msgcat::mcset de "Branch" "Branch"
::msgcat::mcset de "Commit@@noun" "Commit"
::msgcat::mcset de "Merge" "Zusammenf\u00fchren"
::msgcat::mcset de "Remote" "Extern"
::msgcat::mcset de "Tools" "Werkzeuge"
::msgcat::mcset de "Explore Working Copy" "Arbeitskopie im Dateimanager \u00f6ffnen"
::msgcat::mcset de "Git Bash" "Git Bash"
::msgcat::mcset de "Browse Current Branch's Files" "Aktuellen Branch durchbl\u00e4ttern"
::msgcat::mcset de "Browse Branch Files..." "Branch durchbl\u00e4ttern..."
::msgcat::mcset de "Visualize Current Branch's History" "Aktuellen Branch darstellen"
::msgcat::mcset de "Visualize All Branch History" "Historie aller Branches darstellen"
::msgcat::mcset de "Browse %s's Files" "Branch \u00bb%s\u00ab durchbl\u00e4ttern"
::msgcat::mcset de "Visualize %s's History" "Historie von \u00bb%s\u00ab darstellen"
::msgcat::mcset de "Database Statistics" "Datenbankstatistik"
::msgcat::mcset de "Compress Database" "Datenbank komprimieren"
::msgcat::mcset de "Verify Database" "Datenbank \u00fcberpr\u00fcfen"
::msgcat::mcset de "Create Desktop Icon" "Desktop-Icon erstellen"
::msgcat::mcset de "Quit" "Beenden"
::msgcat::mcset de "Undo" "R\u00fcckg\u00e4ngig"
::msgcat::mcset de "Redo" "Wiederholen"
::msgcat::mcset de "Cut" "Ausschneiden"
::msgcat::mcset de "Copy" "Kopieren"
::msgcat::mcset de "Paste" "Einf\u00fcgen"
::msgcat::mcset de "Delete" "L\u00f6schen"
::msgcat::mcset de "Select All" "Alle ausw\u00e4hlen"
::msgcat::mcset de "Create..." "Erstellen..."
::msgcat::mcset de "Checkout..." "Auschecken..."
::msgcat::mcset de "Rename..." "Umbenennen..."
::msgcat::mcset de "Delete..." "L\u00f6schen..."
::msgcat::mcset de "Reset..." "\u00c4nderungen verwerfen..."
::msgcat::mcset de "Done" "Fertig"
::msgcat::mcset de "Commit@@verb" "Committen"
::msgcat::mcset de "Amend Last Commit" "Letzten Commit nachbessern"
::msgcat::mcset de "Rescan" "Neu laden"
::msgcat::mcset de "Stage To Commit" "F\u00fcr Commit bereitstellen"
::msgcat::mcset de "Stage Changed Files To Commit" "Ge\u00e4nderte Dateien f\u00fcr Commit bereitstellen"
::msgcat::mcset de "Unstage From Commit" "Aus Commit-Bereitstellung herausnehmen"
::msgcat::mcset de "Revert Changes" "\u00c4nderungen verwerfen"
::msgcat::mcset de "Show Less Context" "Weniger Zeilen anzeigen"
::msgcat::mcset de "Show More Context" "Mehr Zeilen anzeigen"
::msgcat::mcset de "Sign Off" "Abzeichnen"
::msgcat::mcset de "Local Merge..." "Lokales Zusammenf\u00fchren..."
::msgcat::mcset de "Abort Merge..." "Zusammenf\u00fchren abbrechen..."
::msgcat::mcset de "Add..." "Neues hinzuf\u00fcgen..."
::msgcat::mcset de "Push..." "Versenden..."
::msgcat::mcset de "Delete Branch..." "Branch l\u00f6schen..."
::msgcat::mcset de "Options..." "Optionen..."
::msgcat::mcset de "Remove..." "Entfernen..."
::msgcat::mcset de "Help" "Hilfe"
::msgcat::mcset de "About %s" "\u00dcber %s"
::msgcat::mcset de "Online Documentation" "Online-Dokumentation"
::msgcat::mcset de "Show SSH Key" "SSH-Schl\u00fcssel anzeigen"
::msgcat::mcset de "usage:" "Verwendung:"
::msgcat::mcset de "Usage" "Verwendung"
::msgcat::mcset de "Error" "Fehler"
::msgcat::mcset de "fatal: cannot stat path %s: No such file or directory" "Fehler: Verzeichnis \u00bb%s\u00ab kann nicht gelesen werden: Datei oder Verzeichnis nicht gefunden"
::msgcat::mcset de "Current Branch:" "Aktueller Branch:"
::msgcat::mcset de "Unstaged Changes" "Nicht bereitgestellte \u00c4nderungen"
::msgcat::mcset de "Staged Changes (Will Commit)" "Bereitstellung (zum Committen)"
::msgcat::mcset de "Stage Changed" "Alles bereitstellen"
::msgcat::mcset de "Push" "Versenden"
::msgcat::mcset de "Initial Commit Message:" "Erste Commit-Beschreibung:"
::msgcat::mcset de "Amended Commit Message:" "Nachgebesserte Beschreibung:"
::msgcat::mcset de "Amended Initial Commit Message:" "Nachgebesserte erste Beschreibung:"
::msgcat::mcset de "Amended Merge Commit Message:" "Nachgebesserte Zusammenf\u00fchrungs-Beschreibung:"
::msgcat::mcset de "Merge Commit Message:" "Zusammenf\u00fchrungs-Beschreibung:"
::msgcat::mcset de "Commit Message:" "Commit-Beschreibung:"
::msgcat::mcset de "Copy All" "Alle kopieren"
::msgcat::mcset de "File:" "Datei:"
::msgcat::mcset de "Open" "\u00d6ffnen"
::msgcat::mcset de "Refresh" "Aktualisieren"
::msgcat::mcset de "Decrease Font Size" "Schriftgr\u00f6\u00dfe verkleinern"
::msgcat::mcset de "Increase Font Size" "Schriftgr\u00f6\u00dfe vergr\u00f6\u00dfern"
::msgcat::mcset de "Encoding" "Zeichenkodierung"
::msgcat::mcset de "Apply/Reverse Hunk" "Patch-Block anwenden/zur\u00fccknehmen"
::msgcat::mcset de "Apply/Reverse Line" "Zeile anwenden/zur\u00fccknehmen"
::msgcat::mcset de "Revert Hunk" "Patch-Block zur\u00fccknehmen"
::msgcat::mcset de "Revert Line" "Zeilen\u00e4nderungen zur\u00fccknehmen"
::msgcat::mcset de "Undo Last Revert" "Letztes Zur\u00fccknehmen r\u00fcckg\u00e4ngig"
::msgcat::mcset de "Run Merge Tool" "Zusammenf\u00fchrungswerkzeug"
::msgcat::mcset de "Use Remote Version" "Externe Version benutzen"
::msgcat::mcset de "Use Local Version" "Lokale Version benutzen"
::msgcat::mcset de "Revert To Base" "Zur\u00fccksetzen auf urspr\u00fcnglichen Commit"
::msgcat::mcset de "Visualize These Changes In The Submodule" "Diese \u00c4nderungen im Submodul darstellen"
::msgcat::mcset de "Visualize Current Branch History In The Submodule" "Aktuellen Branch im Submodul darstellen"
::msgcat::mcset de "Visualize All Branch History In The Submodule" "Alle Branches im Submodul darstellen"
::msgcat::mcset de "Start git gui In The Submodule" "Git gui im Submodul starten"
::msgcat::mcset de "Unstage Hunk From Commit" "Patch-Block aus Bereitstellung herausnehmen"
::msgcat::mcset de "Unstage Lines From Commit" "Zeilen aus der Bereitstellung herausnehmen"
::msgcat::mcset de "Revert Lines" "Zeilen\u00e4nderung zur\u00fccknehmen"
::msgcat::mcset de "Unstage Line From Commit" "Zeile aus der Bereitstellung herausnehmen"
::msgcat::mcset de "Stage Hunk For Commit" "Patch-Block zur Bereitstellung hinzuf\u00fcgen"
::msgcat::mcset de "Stage Lines For Commit" "Zeilen zur Bereitstellung hinzuf\u00fcgen"
::msgcat::mcset de "Stage Line For Commit" "Zeile zur Bereitstellung hinzuf\u00fcgen"
::msgcat::mcset de "Initializing..." "Initialisieren..."
::msgcat::mcset de "Possible environment issues exist.\n\nThe following environment variables are probably\ngoing to be ignored by any Git subprocess run\nby %s:\n\n" "M\u00f6glicherweise gibt es Probleme mit manchen Umgebungsvariablen.\n\nDie folgenden Umgebungsvariablen k\u00f6nnen vermutlich nicht \nvon %s an Git weitergegeben werden:\n\n"
::msgcat::mcset de "\nThis is due to a known issue with the\nTcl binary distributed by Cygwin." "\nDies ist ein bekanntes Problem der Tcl-Version, die\nin Cygwin mitgeliefert wird."
::msgcat::mcset de "\n\nA good replacement for %s\nis placing values for the user.name and\nuser.email settings into your personal\n~/.gitconfig file.\n" "\n\nUm den Namen \u00bb%s\u00ab zu \u00e4ndern, sollten Sie die \ngew\u00fcnschten Werte f\u00fcr die Einstellung user.name und \nuser.email in Ihre Datei ~/.gitconfig einf\u00fcgen.\n"
::msgcat::mcset de "Unsupported spell checker" "Rechtschreibpr\u00fcfungsprogramm nicht unterst\u00fctzt"
::msgcat::mcset de "Spell checking is unavailable" "Rechtschreibpr\u00fcfung nicht verf\u00fcgbar"
::msgcat::mcset de "Invalid spell checking configuration" "Unbenutzbare Konfiguration der Rechtschreibpr\u00fcfung"
::msgcat::mcset de "Reverting dictionary to %s." "W\u00f6rterbuch auf %s zur\u00fcckgesetzt."
::msgcat::mcset de "Spell checker silently failed on startup" "Rechtschreibpr\u00fcfungsprogramm mit Fehler abgebrochen"
::msgcat::mcset de "Unrecognized spell checker" "Unbekanntes Rechtschreibpr\u00fcfungsprogramm"
::msgcat::mcset de "No Suggestions" "Keine Vorschl\u00e4ge"
::msgcat::mcset de "Unexpected EOF from spell checker" "Unerwartetes EOF vom Rechtschreibpr\u00fcfungsprogramm"
::msgcat::mcset de "Spell Checker Failed" "Rechtschreibpr\u00fcfung fehlgeschlagen"
::msgcat::mcset de "fetch %s" "\u00bb%s\u00ab anfordern"
::msgcat::mcset de "Fetching new changes from %s" "Neue \u00c4nderungen von \u00bb%s\u00ab holen"
::msgcat::mcset de "remote prune %s" "Gel\u00f6schte externe Branches aus \u00bb%s\u00ab entfernen"
::msgcat::mcset de "Pruning tracking branches deleted from %s" "Gel\u00f6schte externe Trackingbranches aus \u00bb%s\u00ab werden entfernt"
::msgcat::mcset de "fetch all remotes" "Abrufen aller externen"
::msgcat::mcset de "Fetching new changes from all remotes" "Neue \u00c4nderungen von allen externen anfordern"
::msgcat::mcset de "remote prune all remotes" "Extern veraltete Branches entfernen aller Repositories"
::msgcat::mcset de "Pruning tracking branches deleted from all remotes" "Gel\u00f6schte externe Trackingbranches aus allen Repositories werden entfernt"
::msgcat::mcset de "push %s" "\u00bb%s\u00ab versenden..."
::msgcat::mcset de "Pushing changes to %s" "\u00c4nderungen nach \u00bb%s\u00ab versenden"
::msgcat::mcset de "Mirroring to %s" "Spiegeln nach %s"
::msgcat::mcset de "Pushing %s %s to %s" "%s %s nach %s versenden"
::msgcat::mcset de "Push Branches" "Branches versenden"
::msgcat::mcset de "Cancel" "Abbrechen"
::msgcat::mcset de "Source Branches" "Lokale Branches"
::msgcat::mcset de "Destination Repository" "Ziel-Repository"
::msgcat::mcset de "Remote:" "Externes Repository:"
::msgcat::mcset de "Arbitrary Location:" "Beliebige Adresse:"
::msgcat::mcset de "Transfer Options" "Netzwerk-Einstellungen"
::msgcat::mcset de "Force overwrite existing branch (may discard changes)" "\u00dcberschreiben von existierenden Branches erzwingen (k\u00f6nnte \u00c4nderungen l\u00f6schen)"
::msgcat::mcset de "Use thin pack (for slow network connections)" "Kompaktes Datenformat benutzen (f\u00fcr langsame Netzverbindungen)"
::msgcat::mcset de "Include tags" "Mit Tags versenden"
::msgcat::mcset de "%s (%s): Push" "%s (%s): Versenden"
::msgcat::mcset de "Fetching %s from %s" "\u00c4nderungen \u00bb%s\u00ab von \u00bb%s\u00ab anfordern"
::msgcat::mcset de "fatal: Cannot resolve %s" "Fehler: \u00bb%s\u00ab kann nicht als Branch oder Version erkannt werden"
::msgcat::mcset de "Close" "Schlie\u00dfen"
::msgcat::mcset de "Branch '%s' does not exist." "Branch \u00bb%s\u00ab existiert nicht."
::msgcat::mcset de "Failed to configure simplified git-pull for '%s'." "Fehler beim Einrichten der vereinfachten git-pull f\u00fcr \u00bb%s\u00ab."
::msgcat::mcset de "Branch '%s' already exists." "Branch \u00bb%s\u00ab existiert bereits."
::msgcat::mcset de "Branch '%s' already exists.\n\nIt cannot fast-forward to %s.\nA merge is required." "Branch \u00bb%s\u00ab existiert bereits.\n\nBranch kann nicht auf \u00bb%s\u00ab vorgespult werden. Regul\u00e4res Zusammenf\u00fchren ist notwendig."
::msgcat::mcset de "Merge strategy '%s' not supported." "Zusammenf\u00fchrungsmethode \u00bb%s\u00ab nicht unterst\u00fctzt."
::msgcat::mcset de "Failed to update '%s'." "Aktualisieren von \u00bb%s\u00ab fehlgeschlagen."
::msgcat::mcset de "Staging area (index) is already locked." "Bereitstellung (\u00bbindex\u00ab) ist zur Bearbeitung gesperrt (\u00bblocked\u00ab)."
::msgcat::mcset de "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before the current branch can be changed.\n\nThe rescan will be automatically started now.\n" "Der letzte geladene Status stimmt nicht mehr mit dem Repository \u00fcberein.\n\nEin anderes Git-Programm hat das Repository seit dem letzten Laden ge\u00e4ndert.  Vor dem Wechseln des lokalen Branches muss neu geladen werden.\n\nEs wird gleich neu geladen.\n"
::msgcat::mcset de "Updating working directory to '%s'..." "Arbeitskopie aktualisieren auf \u00bb%s\u00ab..."
::msgcat::mcset de "files checked out" "Dateien aktualisiert"
::msgcat::mcset de "Aborted checkout of '%s' (file level merging is required)." "Branch \u00bb%s\u00ab Auschecken abgebrochen (Zusammenf\u00fchren der Dateien ist notwendig)."
::msgcat::mcset de "File level merge required." "Zusammenf\u00fchren der Dateien ist notwendig."
::msgcat::mcset de "Staying on branch '%s'." "Es wird auf Branch \u00bb%s\u00ab verblieben."
::msgcat::mcset de "You are no longer on a local branch.\n\nIf you wanted to be on a branch, create one now starting from 'This Detached Checkout'." "Die Arbeitskopie ist nicht auf einem lokalen Branch.\n\nWenn Sie auf einem Branch arbeiten m\u00f6chten, erstellen Sie bitte jetzt einen Branch mit der Auswahl \u00bbLosgel\u00f6ste Arbeitskopie-Version\u00ab."
::msgcat::mcset de "Checked out '%s'." "Umgestellt auf \u00bb%s\u00ab."
::msgcat::mcset de "Resetting '%s' to '%s' will lose the following commits:" "Umsetzen von \u00bb%s\u00ab nach \u00bb%s\u00ab wird folgende Commits verlieren:"
::msgcat::mcset de "Recovering lost commits may not be easy." "Verlorene Commits k\u00f6nnen nur mit gr\u00f6\u00dferem Aufwand wiederhergestellt werden."
::msgcat::mcset de "Reset '%s'?" "\u00bb%s\u00ab umsetzen?"
::msgcat::mcset de "Visualize" "Darstellen"
::msgcat::mcset de "Reset" "Umsetzen (Reset)"
::msgcat::mcset de "Failed to set current branch.\n\nThis working directory is only partially switched.  We successfully updated your files, but failed to update an internal Git file.\n\nThis should not have occurred.  %s will now close and give up." "Lokaler Branch kann nicht gesetzt werden.\n\nDiese Arbeitskopie ist nur teilweise umgestellt. Die Dateien sind korrekt aktualisiert, aber einige interne Git-Dateien konnten nicht ge\u00e4ndert werden.\n\nDies ist ein interner Programmfehler von %s. Programm wird jetzt abgebrochen."
::msgcat::mcset de "%s (%s): Add Remote" "%s (%s): Externes Repository hinzuf\u00fcgen"
::msgcat::mcset de "Add New Remote" "Neues externes Repository hinzuf\u00fcgen"
::msgcat::mcset de "Add" "Hinzuf\u00fcgen"
::msgcat::mcset de "Remote Details" "Einzelheiten des externen Repository"
::msgcat::mcset de "Name:" "Name:"
::msgcat::mcset de "Location:" "Adresse:"
::msgcat::mcset de "Further Action" "Weitere Aktion"
::msgcat::mcset de "Fetch Immediately" "Jetzt anfordern"
::msgcat::mcset de "Initialize Remote Repository and Push" "Externes Repository initialisieren und dahin versenden"
::msgcat::mcset de "Do Nothing Else Now" "Keine weitere Aktion"
::msgcat::mcset de "Please supply a remote name." "Bitte geben Sie einen Namen des externen Repository an."
::msgcat::mcset de "'%s' is not an acceptable remote name." "\u00bb%s\u00ab ist kein zul\u00e4ssiger Name eines externen Repository."
::msgcat::mcset de "Failed to add remote '%s' of location '%s'." "Fehler beim Hinzuf\u00fcgen des externen Repository \u00bb%s\u00ab aus Adresse \u00bb%s\u00ab."
::msgcat::mcset de "Fetching the %s" "\u00bb%s\u00ab anfordern"
::msgcat::mcset de "Do not know how to initialize repository at location '%s'." "Initialisieren eines externen Repositories an Adresse \u00bb%s\u00ab ist nicht m\u00f6glich."
::msgcat::mcset de "Setting up the %s (at %s)" "Einrichten von \u00bb%s\u00ab an \u00bb%s\u00ab"
::msgcat::mcset de "Starting..." "Starten..."
::msgcat::mcset de "%s (%s): File Browser" "%s (%s): Datei-Browser"
::msgcat::mcset de "Loading %s..." "%s laden..."
::msgcat::mcset de "\[Up To Parent\]" "\[Nach oben\]"
::msgcat::mcset de "%s (%s): Browse Branch Files" "%s (%s): Dateien des Branches durchbl\u00e4ttern"
::msgcat::mcset de "Browse Branch Files" "Dateien des Branches durchbl\u00e4ttern"
::msgcat::mcset de "Browse" "Bl\u00e4ttern"
::msgcat::mcset de "Revision" "Version"
::msgcat::mcset de "Unable to unlock the index." "Bereitstellung kann nicht wieder freigegeben werden."
::msgcat::mcset de "Index Error" "Fehler in Bereitstellung"
::msgcat::mcset de "Updating the Git index failed.  A rescan will be automatically started to resynchronize git-gui." "Das Aktualisieren der Git-Bereitstellung ist fehlgeschlagen. Eine allgemeine Git-Aktualisierung wird jetzt gestartet, um git-gui wieder mit git zu synchronisieren."
::msgcat::mcset de "Continue" "Fortsetzen"
::msgcat::mcset de "Unlock Index" "Bereitstellung freigeben"
::msgcat::mcset de "files" "Dateien"
::msgcat::mcset de "Unstaging selected files from commit" "Gew\u00e4hlte Dateien aus der Bereitstellung herausnehmen"
::msgcat::mcset de "Unstaging %s from commit" "Datei \u00bb%s\u00ab aus der Bereitstellung herausnehmen"
::msgcat::mcset de "Ready to commit." "Bereit zum Committen."
::msgcat::mcset de "Adding selected files" "Gew\u00e4hlte Dateien hinzuf\u00fcgen"
::msgcat::mcset de "Adding %s" "\u00bb%s\u00ab hinzuf\u00fcgen"
::msgcat::mcset de "Stage %d untracked files?" "%d unversionierte Dateien bereitstellen?"
::msgcat::mcset de "Adding all changed files" "Alle ge\u00e4nderten Dateien hinzuf\u00fcgen"
::msgcat::mcset de "Revert changes in file %s?" "\u00c4nderungen in Datei \u00bb%s\u00ab verwerfen?"
::msgcat::mcset de "Revert changes in these %i files?" "\u00c4nderungen in diesen %i Dateien verwerfen?"
::msgcat::mcset de "Any unstaged changes will be permanently lost by the revert." "Alle nicht bereitgestellten \u00c4nderungen werden beim Verwerfen verloren gehen."
::msgcat::mcset de "Do Nothing" "Nichts tun"
::msgcat::mcset de "Delete untracked file %s?" "Unversionierte Datei \u00bb%s\u00ab l\u00f6schen?"
::msgcat::mcset de "Delete these %i untracked files?" "Diese %i unversionierten Dateien l\u00f6schen?"
::msgcat::mcset de "Files will be permanently deleted." "Dateien werden endg\u00fcltig gel\u00f6scht."
::msgcat::mcset de "Delete Files" "Dateien l\u00f6schen"
::msgcat::mcset de "Deleting" "L\u00f6schen"
::msgcat::mcset de "Encountered errors deleting files:\n" "Fehler beim L\u00f6schen der Dateien:\n"
::msgcat::mcset de "None of the %d selected files could be deleted." "Keine der %d gew\u00e4hlten Dateien konnten gel\u00f6scht werden."
::msgcat::mcset de "%d of the %d selected files could not be deleted." "%d der %d gew\u00e4hlten Dateien konnten nicht gel\u00f6scht werden."
::msgcat::mcset de "Reverting selected files" "\u00c4nderungen in gew\u00e4hlten Dateien verwerfen"
::msgcat::mcset de "Reverting %s" "\u00c4nderungen in %s verwerfen"
::msgcat::mcset de "%s (%s): Checkout Branch" "%s (%s): Branch auschecken"
::msgcat::mcset de "Checkout Branch" "Branch auschecken"
::msgcat::mcset de "Checkout" "Auschecken"
::msgcat::mcset de "Options" "Optionen"
::msgcat::mcset de "Fetch Tracking Branch" "Trackingbranch anfordern"
::msgcat::mcset de "Detach From Local Branch" "Verbindung zu lokalem Branch l\u00f6sen"
::msgcat::mcset de "%s ... %*i of %*i %s (%3i%%)" "%s ... %*i von %*i %s (%3i%%)"
::msgcat::mcset de "Push to" "Versenden nach"
::msgcat::mcset de "Remove Remote" "Externes Repository entfernen"
::msgcat::mcset de "Prune from" "Veraltete Branches entfernen"
::msgcat::mcset de "Fetch from" "Anfordern"
::msgcat::mcset de "All" "Alle"
::msgcat::mcset de "%s (%s): Rename Branch" "%s (%s): Branch umbenennen"
::msgcat::mcset de "Rename Branch" "Branch umbenennen"
::msgcat::mcset de "Rename" "Umbenennen"
::msgcat::mcset de "Branch:" "Branch:"
::msgcat::mcset de "New Name:" "Neuer Name:"
::msgcat::mcset de "Please select a branch to rename." "Bitte w\u00e4hlen Sie einen Branch zum umbenennen."
::msgcat::mcset de "Please supply a branch name." "Bitte geben Sie einen Branchnamen an."
::msgcat::mcset de "'%s' is not an acceptable branch name." "\u00bb%s\u00ab ist kein zul\u00e4ssiger Branchname."
::msgcat::mcset de "Failed to rename '%s'." "Fehler beim Umbenennen von \u00bb%s\u00ab."
::msgcat::mcset de "Select" "Ausw\u00e4hlen"
::msgcat::mcset de "Font Family" "Schriftfamilie"
::msgcat::mcset de "Font Size" "Schriftgr\u00f6\u00dfe"
::msgcat::mcset de "Font Example" "Schriftbeispiel"
::msgcat::mcset de "This is example text.\nIf you like this text, it can be your font." "Dies ist ein Beispieltext.\nWenn Ihnen dieser Text gef\u00e4llt, sollten Sie diese Schriftart w\u00e4hlen."
::msgcat::mcset de "Invalid global encoding '%s'" "Ung\u00fcltige globale Zeichenkodierung \u00bb%s\u00ab"
::msgcat::mcset de "Invalid repo encoding '%s'" "Ung\u00fcltige Repository-Zeichenkodierung \u00bb%s\u00ab"
::msgcat::mcset de "Restore Defaults" "Voreinstellungen wiederherstellen"
::msgcat::mcset de "Save" "Speichern"
::msgcat::mcset de "%s Repository" "%s Repository"
::msgcat::mcset de "Global (All Repositories)" "Global (Alle Repositories)"
::msgcat::mcset de "User Name" "Benutzername"
::msgcat::mcset de "Email Address" "E-Mail-Adresse"
::msgcat::mcset de "Summarize Merge Commits" "Zusammenf\u00fchrungs-Commits zusammenfassen"
::msgcat::mcset de "Merge Verbosity" "Ausf\u00fchrlichkeit der Zusammenf\u00fchren-Meldungen"
::msgcat::mcset de "Show Diffstat After Merge" "Vergleichsstatistik nach Zusammenf\u00fchren anzeigen"
::msgcat::mcset de "Use Merge Tool" "Zusammenf\u00fchrungswerkzeug"
::msgcat::mcset de "Trust File Modification Timestamps" "Auf Datei\u00e4nderungsdatum verlassen"
::msgcat::mcset de "Prune Tracking Branches During Fetch" "Veraltete Trackingbranches entfernen w\u00e4hrend Anforderung"
::msgcat::mcset de "Match Tracking Branches" "Neue Branches automatisch als Trackingbranch"
::msgcat::mcset de "Use Textconv For Diffs and Blames" "Benutze \u00bbtextconv\u00ab f\u00fcr Vergleich und Annotieren"
::msgcat::mcset de "Blame Copy Only On Changed Files" "Kopie-Annotieren nur bei ge\u00e4nderten Dateien"
::msgcat::mcset de "Maximum Length of Recent Repositories List" "Anzahl Eintr\u00e4ge in \u00bbLetzte Repositories\u00ab"
::msgcat::mcset de "Minimum Letters To Blame Copy On" "Mindestzahl Zeichen f\u00fcr Kopie-Annotieren"
::msgcat::mcset de "Blame History Context Radius (days)" "Anzahl Tage f\u00fcr Annotieren-Historien-Kontext"
::msgcat::mcset de "Number of Diff Context Lines" "Anzahl der Kontextzeilen beim Vergleich"
::msgcat::mcset de "Additional Diff Parameters" "Zus\u00e4tzliche Vergleich-/diff-Parameter"
::msgcat::mcset de "Commit Message Text Width" "Textbreite der Commit-Beschreibung"
::msgcat::mcset de "New Branch Name Template" "Namensvorlage f\u00fcr neue Branches"
::msgcat::mcset de "Default File Contents Encoding" "Voreingestellte Zeichenkodierung"
::msgcat::mcset de "Warn before committing to a detached head" "Warnen vor Committen auf losgel\u00f6ste Branchspitze"
::msgcat::mcset de "Staging of untracked files" "Unversionierte Dateien bereitstellen"
::msgcat::mcset de "Show untracked files" "Unversionierte Dateien anzeigen"
::msgcat::mcset de "Tab spacing" "Tabulator-Breite"
::msgcat::mcset de "%s:" "%s:"
::msgcat::mcset de "Change" "\u00c4ndern"
::msgcat::mcset de "Spelling Dictionary:" "W\u00f6rterbuch Rechtschreibpr\u00fcfung:"
::msgcat::mcset de "Change Font" "Schriftart \u00e4ndern"
::msgcat::mcset de "Choose %s" "%s w\u00e4hlen"
::msgcat::mcset de "pt." "pt."
::msgcat::mcset de "Preferences" "Einstellungen"
::msgcat::mcset de "Failed to completely save options:" "Optionen konnten nicht gespeichert werden:"
::msgcat::mcset de "Default" "Voreinstellung"
::msgcat::mcset de "System (%s)" "Systemweit (%s)"
::msgcat::mcset de "Other" "Andere"
::msgcat::mcset de "Running %s requires a selected file." "Um \u00bb%s\u00ab zu starten, muss eine Datei ausgew\u00e4hlt sein."
::msgcat::mcset de "Are you sure you want to run %1\$s on file \"%2\$s\"?" "Wollen Sie %1\$s wirklich auf Datei \u00bb%2\$s\u00ab starten?"
::msgcat::mcset de "Are you sure you want to run %s?" "Wollen Sie %s wirklich starten?"
::msgcat::mcset de "Tool: %s" "Werkzeug: %s"
::msgcat::mcset de "Running: %s" "Starten: %s"
::msgcat::mcset de "Tool completed successfully: %s" "Werkzeug erfolgreich abgeschlossen: %s"
::msgcat::mcset de "Tool failed: %s" "Werkzeug fehlgeschlagen: %s"
::msgcat::mcset de "Force resolution to the base version?" "Konflikt durch Basisversion ersetzen?"
::msgcat::mcset de "Force resolution to this branch?" "Konflikt durch diesen Branch ersetzen?"
::msgcat::mcset de "Force resolution to the other branch?" "Konflikt durch anderen Branch ersetzen?"
::msgcat::mcset de "Note that the diff shows only conflicting changes.\n\n%s will be overwritten.\n\nThis operation can be undone only by restarting the merge." "Hinweis: Der Vergleich zeigt nur konfliktverursachende \u00c4nderungen an.\n\n\u00bb%s\u00ab wird \u00fcberschrieben.\n\nDiese Operation kann nur r\u00fcckg\u00e4ngig gemacht werden, wenn die\nZusammenf\u00fchrung erneut gestartet wird."
::msgcat::mcset de "File %s seems to have unresolved conflicts, still stage?" "Datei \u00bb%s\u00ab hat nicht aufgel\u00f6ste Konflikte. Trotzdem bereitstellen?"
::msgcat::mcset de "Adding resolution for %s" "Aufl\u00f6sung hinzugef\u00fcgt f\u00fcr %s"
::msgcat::mcset de "Cannot resolve deletion or link conflicts using a tool" "Konflikte durch gel\u00f6schte Dateien oder symbolische Links k\u00f6nnen nicht durch das Zusamenf\u00fchrungswerkzeug gel\u00f6st werden."
::msgcat::mcset de "Conflict file does not exist" "Konflikt-Datei existiert nicht"
::msgcat::mcset de "Not a GUI merge tool: '%s'" "Kein GUI Zusammenf\u00fchrungswerkzeug: \u00bb%s\u00ab"
::msgcat::mcset de "Unsupported merge tool '%s'" "Unbekanntes Zusammenf\u00fchrungswerkzeug: \u00bb%s\u00ab"
::msgcat::mcset de "Merge tool is already running, terminate it?" "Zusammenf\u00fchrungswerkzeug l\u00e4uft bereits. Soll es abgebrochen werden?"
::msgcat::mcset de "Error retrieving versions:\n%s" "Fehler beim Abrufen der Dateiversionen:\n%s"
::msgcat::mcset de "Could not start the merge tool:\n\n%s" "Zusammenf\u00fchrungswerkzeug konnte nicht gestartet werden:\n\n%s"
::msgcat::mcset de "Running merge tool..." "Zusammenf\u00fchrungswerkzeug starten..."
::msgcat::mcset de "Merge tool failed." "Zusammenf\u00fchrungswerkzeug fehlgeschlagen."
::msgcat::mcset de "%s (%s): Add Tool" "%s (%s): Werkzeug hinzuf\u00fcgen"
::msgcat::mcset de "Add New Tool Command" "Neues Kommando f\u00fcr Werkzeug hinzuf\u00fcgen"
::msgcat::mcset de "Add globally" "Global hinzuf\u00fcgen"
::msgcat::mcset de "Tool Details" "Einzelheiten des Werkzeugs"
::msgcat::mcset de "Use '/' separators to create a submenu tree:" "Benutzen Sie einen Schr\u00e4gstrich \u00bb/\u00ab, um Untermen\u00fcs zu erstellen:"
::msgcat::mcset de "Command:" "Kommando:"
::msgcat::mcset de "Show a dialog before running" "Best\u00e4tigungsfrage vor Starten anzeigen"
::msgcat::mcset de "Ask the user to select a revision (sets \$REVISION)" "Benutzer nach Version fragen (setzt \$REVISION)"
::msgcat::mcset de "Ask the user for additional arguments (sets \$ARGS)" "Benutzer nach zus\u00e4tzlichen Argumenten fragen (setzt \$ARGS)"
::msgcat::mcset de "Don't show the command output window" "Kein Ausgabefenster zeigen"
::msgcat::mcset de "Run only if a diff is selected (\$FILENAME not empty)" "Nur starten, wenn ein Vergleich gew\u00e4hlt ist (\$FILENAME ist nicht leer)"
::msgcat::mcset de "Please supply a name for the tool." "Bitte geben Sie einen Werkzeugnamen an."
::msgcat::mcset de "Tool '%s' already exists." "Werkzeug \u00bb%s\u00ab existiert bereits."
::msgcat::mcset de "Could not add tool:\n%s" "Werkzeug konnte nicht hinzugef\u00fcgt werden:\n\n%s"
::msgcat::mcset de "%s (%s): Remove Tool" "%s (%s): Werkzeug entfernen"
::msgcat::mcset de "Remove Tool Commands" "Werkzeugkommandos entfernen"
::msgcat::mcset de "Remove" "Entfernen"
::msgcat::mcset de "(Blue denotes repository-local tools)" "(Werkzeuge f\u00fcr lokales Repository werden in Blau angezeigt)"
::msgcat::mcset de "%s (%s):" "%s (%s):"
::msgcat::mcset de "Run Command: %s" "Kommando aufrufen: %s"
::msgcat::mcset de "Arguments" "Argumente"
::msgcat::mcset de "OK" "Ok"
::msgcat::mcset de "Find:" "Suchen:"
::msgcat::mcset de "Next" "N\u00e4chster"
::msgcat::mcset de "Prev" "Voriger"
::msgcat::mcset de "RegExp" "RegAusdruck"
::msgcat::mcset de "Case" "Gro\u00df/klein"
::msgcat::mcset de "%s (%s): Create Desktop Icon" "%s (%s): Desktop-Icon erstellen"
::msgcat::mcset de "Cannot write shortcut:" "Fehler beim Schreiben der Verkn\u00fcpfung:"
::msgcat::mcset de "Cannot write icon:" "Fehler beim Erstellen des Icons:"
::msgcat::mcset de "%s (%s): Delete Branch Remotely" "%s (%s): Branch in externem Repository l\u00f6schen"
::msgcat::mcset de "Delete Branch Remotely" "Branch in externem Repository l\u00f6schen"
::msgcat::mcset de "From Repository" "In Repository"
::msgcat::mcset de "Branches" "Branches"
::msgcat::mcset de "Delete Only If" "Nur l\u00f6schen, wenn"
::msgcat::mcset de "Merged Into:" "Zusammengef\u00fchrt mit:"
::msgcat::mcset de "Always (Do not perform merge checks)" "Immer (Keine Zusammenf\u00fchrungspr\u00fcfung)"
::msgcat::mcset de "A branch is required for 'Merged Into'." "F\u00fcr \u00bbZusammenf\u00fchren mit\u00ab muss ein Branch angegeben werden."
::msgcat::mcset de "The following branches are not completely merged into %s:\n\n - %s" "Folgende Branches sind noch nicht mit \u00bb%s\u00ab zusammengef\u00fchrt:\n\n - %s"
::msgcat::mcset de "One or more of the merge tests failed because you have not fetched the necessary commits.  Try fetching from %s first." "Ein oder mehrere Zusammenf\u00fchrungen sind fehlgeschlagen, da Sie nicht die notwendigen Commits vorher angefordert haben.  Sie sollten versuchen, zuerst von \u00bb%s\u00ab anzufordern."
::msgcat::mcset de "Please select one or more branches to delete." "Bitte w\u00e4hlen Sie mindestens einen Branch, der gel\u00f6scht werden soll."
::msgcat::mcset de "Recovering deleted branches is difficult.\n\nDelete the selected branches?" "Das Wiederherstellen von gel\u00f6schten Branches ist nur mit gr\u00f6\u00dferem Aufwand m\u00f6glich.\n\nSollen die ausgew\u00e4hlten Branches gel\u00f6scht werden?"
::msgcat::mcset de "Deleting branches from %s" "Branches auf \u00bb%s\u00ab werden gel\u00f6scht"
::msgcat::mcset de "No repository selected." "Kein Repository ausgew\u00e4hlt."
::msgcat::mcset de "Scanning %s..." "\u00bb%s\u00ab laden..."
::msgcat::mcset de "Git Gui" "Git Gui"
::msgcat::mcset de "Create New Repository" "Repository neu erstellen"
::msgcat::mcset de "New..." "Neu..."
::msgcat::mcset de "Clone Existing Repository" "Repository klonen"
::msgcat::mcset de "Clone..." "Klonen..."
::msgcat::mcset de "Open Existing Repository" "Repository \u00f6ffnen"
::msgcat::mcset de "Open..." "\u00d6ffnen..."
::msgcat::mcset de "Recent Repositories" "Letzte Repositories"
::msgcat::mcset de "Open Recent Repository:" "Zuletzt benutztes Repository \u00f6ffnen:"
::msgcat::mcset de "Failed to create repository %s:" "Repository \u00bb%s\u00ab konnte nicht erstellt werden:"
::msgcat::mcset de "Create" "Erstellen"
::msgcat::mcset de "Directory:" "Verzeichnis:"
::msgcat::mcset de "Git Repository" "Git Repository"
::msgcat::mcset de "Directory %s already exists." "Verzeichnis \u00bb%s\u00ab existiert bereits."
::msgcat::mcset de "File %s already exists." "Datei \u00bb%s\u00ab existiert bereits."
::msgcat::mcset de "Clone" "Klonen"
::msgcat::mcset de "Source Location:" "Herkunfts-Adresse:"
::msgcat::mcset de "Target Directory:" "Zielverzeichnis:"
::msgcat::mcset de "Clone Type:" "Art des Klonens:"
::msgcat::mcset de "Standard (Fast, Semi-Redundant, Hardlinks)" "Standard (schnell, teilweise redundant, Hardlinks)"
::msgcat::mcset de "Full Copy (Slower, Redundant Backup)" "Alles kopieren (langsamer, volle Redundanz)"
::msgcat::mcset de "Shared (Fastest, Not Recommended, No Backup)" "Verkn\u00fcpft (schnell, nicht empfohlen, kein Backup)"
::msgcat::mcset de "Recursively clone submodules too" "Rekursiv weitere Submodule klonen"
::msgcat::mcset de "Not a Git repository: %s" "Kein Git-Repository: %s"
::msgcat::mcset de "Standard only available for local repository." "Standard ist nur f\u00fcr lokale Repositories verf\u00fcgbar."
::msgcat::mcset de "Shared only available for local repository." "Verkn\u00fcpft ist nur f\u00fcr lokale Repositories verf\u00fcgbar."
::msgcat::mcset de "Location %s already exists." "Adresse \u00bb%s\u00ab existiert bereits."
::msgcat::mcset de "Failed to configure origin" "Der Ursprungsort konnte nicht eingerichtet werden"
::msgcat::mcset de "Counting objects" "Objekte werden gez\u00e4hlt"
::msgcat::mcset de "buckets" "Buckets"
::msgcat::mcset de "Unable to copy objects/info/alternates: %s" "Kopien von Objekten/Info/Alternates konnten nicht erstellt werden: %s"
::msgcat::mcset de "Nothing to clone from %s." "Von \u00bb%s\u00ab konnte nichts geklont werden."
::msgcat::mcset de "The 'master' branch has not been initialized." "Der \u00bbmaster\u00ab-Branch wurde noch nicht initialisiert."
::msgcat::mcset de "Hardlinks are unavailable.  Falling back to copying." "Hardlinks nicht verf\u00fcgbar. Stattdessen wird kopiert."
::msgcat::mcset de "Cloning from %s" "Kopieren von \u00bb%s\u00ab"
::msgcat::mcset de "Copying objects" "Objektdatenbank kopieren"
::msgcat::mcset de "KiB" "KB"
::msgcat::mcset de "Unable to copy object: %s" "Objekt kann nicht kopiert werden: %s"
::msgcat::mcset de "Linking objects" "Objekte verlinken"
::msgcat::mcset de "objects" "Objekte"
::msgcat::mcset de "Unable to hardlink object: %s" "F\u00fcr Objekt konnte kein Hardlink erstellt werden: %s"
::msgcat::mcset de "Cannot fetch branches and objects.  See console output for details." "Branches und Objekte konnten nicht angefordert werden.  Kontrollieren Sie die Ausgaben auf der Konsole f\u00fcr weitere Angaben."
::msgcat::mcset de "Cannot fetch tags.  See console output for details." "Tags konnten nicht angefordert werden.  Kontrollieren Sie die Ausgaben auf der Konsole f\u00fcr weitere Angaben."
::msgcat::mcset de "Cannot determine HEAD.  See console output for details." "Die Branchspitze (HEAD) konnte nicht gefunden werden.  Kontrollieren Sie die Ausgaben auf der Konsole f\u00fcr weitere Angaben."
::msgcat::mcset de "Unable to cleanup %s" "Verzeichnis \u00bb%s\u00ab kann nicht aufger\u00e4umt werden."
::msgcat::mcset de "Clone failed." "Klonen fehlgeschlagen."
::msgcat::mcset de "No default branch obtained." "Kein voreingestellter Branch gefunden."
::msgcat::mcset de "Cannot resolve %s as a commit." "\u00bb%s\u00ab wurde nicht als Commit gefunden."
::msgcat::mcset de "Creating working directory" "Arbeitskopie erstellen"
::msgcat::mcset de "Initial file checkout failed." "Erstellen der Arbeitskopie fehlgeschlagen."
::msgcat::mcset de "Cloning submodules" "Klone Submodul"
::msgcat::mcset de "Cannot clone submodules." "Submodul konnte nicht geklont werden."
::msgcat::mcset de "Repository:" "Repository:"
::msgcat::mcset de "Failed to open repository %s:" "Repository \u00bb%s\u00ab konnte nicht ge\u00f6ffnet werden."
::msgcat::mcset de "git-gui - a graphical user interface for Git." "git-gui - eine grafische Oberfl\u00e4che f\u00fcr Git."
::msgcat::mcset de "%s (%s): File Viewer" "%s (%s): Datei-Browser"
::msgcat::mcset de "Commit:" "Commit:"
::msgcat::mcset de "Copy Commit" "Commit kopieren"
::msgcat::mcset de "Find Text..." "Text suchen..."
::msgcat::mcset de "Goto Line..." "Gehe zu Zeile..."
::msgcat::mcset de "Do Full Copy Detection" "Volle Kopie-Erkennung"
::msgcat::mcset de "Show History Context" "Historien-Kontext anzeigen"
::msgcat::mcset de "Blame Parent Commit" "Elterncommit annotieren"
::msgcat::mcset de "Reading %s..." "%s lesen..."
::msgcat::mcset de "Loading copy/move tracking annotations..." "Annotierungen f\u00fcr Kopieren/Verschieben werden geladen..."
::msgcat::mcset de "lines annotated" "Zeilen annotiert"
::msgcat::mcset de "Loading original location annotations..." "Annotierungen f\u00fcr urspr\u00fcnglichen Ort werden geladen..."
::msgcat::mcset de "Annotation complete." "Annotierung vollst\u00e4ndig."
::msgcat::mcset de "Busy" "Verarbeitung l\u00e4uft"
::msgcat::mcset de "Annotation process is already running." "Annotierung l\u00e4uft bereits."
::msgcat::mcset de "Running thorough copy detection..." "Intensive Kopie-Erkennung l\u00e4uft..."
::msgcat::mcset de "Loading annotation..." "Annotierung laden..."
::msgcat::mcset de "Author:" "Autor:"
::msgcat::mcset de "Committer:" "Committer:"
::msgcat::mcset de "Original File:" "Urspr\u00fcngliche Datei:"
::msgcat::mcset de "Cannot find HEAD commit:" "Branchspitze (\u00bbHEAD commit\u00ab) kann nicht gefunden werden:"
::msgcat::mcset de "Cannot find parent commit:" "Elterncommit kann nicht gefunden werden:"
::msgcat::mcset de "Unable to display parent" "Elterncommit kann nicht angezeigt werden"
::msgcat::mcset de "Error loading diff:" "Fehler beim Laden des Vergleichs:"
::msgcat::mcset de "Originally By:" "Urspr\u00fcnglich von:"
::msgcat::mcset de "In File:" "In Datei:"
::msgcat::mcset de "Copied Or Moved Here By:" "Kopiert oder verschoben durch:"
::msgcat::mcset de "No differences detected.\n\n%s has no changes.\n\nThe modification date of this file was updated by another application, but the content within the file was not changed.\n\nA rescan will be automatically started to find other files which may have the same state." "Keine \u00c4nderungen feststellbar.\n\n\u00bb%s\u00ab enth\u00e4lt keine \u00c4nderungen. Zwar wurde das \u00c4nderungsdatum dieser Datei von einem anderen Programm modifiziert, aber der Inhalt der Datei ist unver\u00e4ndert.\n\nDas Arbeitsverzeichnis wird jetzt neu geladen, um diese \u00c4nderung bei allen Dateien zu pr\u00fcfen."
::msgcat::mcset de "Loading diff of %s..." "Vergleich von \u00bb%s\u00ab laden..."
::msgcat::mcset de "LOCAL: deleted\nREMOTE:\n" "LOKAL: gel\u00f6scht\nEXTERN:\n"
::msgcat::mcset de "REMOTE: deleted\nLOCAL:\n" "EXTERN: gel\u00f6scht\nLOKAL:\n"
::msgcat::mcset de "LOCAL:\n" "LOKAL:\n"
::msgcat::mcset de "REMOTE:\n" "EXTERN:\n"
::msgcat::mcset de "Unable to display %s" "Datei \u00bb%s\u00ab kann nicht angezeigt werden"
::msgcat::mcset de "Error loading file:" "Fehler beim Laden der Datei:"
::msgcat::mcset de "Git Repository (subproject)" "Git-Repository (Subprojekt)"
::msgcat::mcset de "* Binary file (not showing content)." "* Bin\u00e4rdatei (Inhalt wird nicht angezeigt)"
::msgcat::mcset de "* Untracked file is %d bytes.\n* Showing only first %d bytes.\n" "* Unversionierte Datei hat %d Bytes.\n* Nur erste %d Bytes werden angezeigt.\n"
::msgcat::mcset de "\n* Untracked file clipped here by %s.\n* To see the entire file, use an external editor.\n" "\n* Unversionierte Datei, hier abgeschnitten durch %s.\n* Zum Ansehen der vollst\u00e4ndigen Datei externen Editor benutzen.\n"
::msgcat::mcset de "Failed to unstage selected hunk." "Fehler beim Herausnehmen des gew\u00e4hlten Patch-Blocks aus der Bereitstellung."
::msgcat::mcset de "Failed to revert selected hunk." "Fehler beim Zur\u00fccknehmen des gew\u00e4hlten Patch-Blocks."
::msgcat::mcset de "Failed to stage selected hunk." "Fehler beim Bereitstellen des gew\u00e4hlten Patch-Blocks."
::msgcat::mcset de "Failed to unstage selected line." "Fehler beim Herausnehmen der gew\u00e4hlten Zeile aus der Bereitstellung."
::msgcat::mcset de "Failed to revert selected line." "Fehler beim Zur\u00fccknehmen der gew\u00e4hlten Zeile."
::msgcat::mcset de "Failed to stage selected line." "Fehler beim Bereitstellen der gew\u00e4hlten Zeile."
::msgcat::mcset de "Failed to undo last revert." "Fehler beim R\u00fcckg\u00e4ngigmachen des letzten Zur\u00fccknehmen-Commits"
::msgcat::mcset de "No keys found." "Keine Schl\u00fcssel gefunden."
::msgcat::mcset de "Found a public key in: %s" "\u00d6ffentlicher Schl\u00fcssel gefunden in: %s"
::msgcat::mcset de "Generate Key" "Schl\u00fcssel erzeugen"
::msgcat::mcset de "Copy To Clipboard" "In Zwischenablage kopieren"
::msgcat::mcset de "Your OpenSSH Public Key" "Ihr OpenSSH \u00f6ffenlicher Schl\u00fcssel"
::msgcat::mcset de "Generating..." "Erzeugen..."
::msgcat::mcset de "Could not start ssh-keygen:\n\n%s" "Konnte \u00bbssh-keygen\u00ab nicht starten:\n\n%s"
::msgcat::mcset de "Generation failed." "Schl\u00fcsselerzeugung fehlgeschlagen."
::msgcat::mcset de "Generation succeeded, but no keys found." "Schl\u00fcsselerzeugung erfolgreich, aber keine Schl\u00fcssel gefunden."
::msgcat::mcset de "Your key is in: %s" "Ihr Schl\u00fcssel ist abgelegt in: %s"
::msgcat::mcset de "%s (%s): Create Branch" "%s (%s): Branch erstellen"
::msgcat::mcset de "Create New Branch" "Neuen Branch erstellen"
::msgcat::mcset de "Branch Name" "Branchname"
::msgcat::mcset de "Match Tracking Branch Name" "Passend zu Trackingbranch-Name"
::msgcat::mcset de "Starting Revision" "Anfangsversion"
::msgcat::mcset de "Update Existing Branch:" "Existierenden Branch aktualisieren:"
::msgcat::mcset de "No" "Nein"
::msgcat::mcset de "Fast Forward Only" "Nur Vorspulen"
::msgcat::mcset de "Checkout After Creation" "Branch auschecken nach Erstellen"
::msgcat::mcset de "Please select a tracking branch." "Bitte w\u00e4hlen Sie einen Trackingbranch."
::msgcat::mcset de "Tracking branch %s is not a branch in the remote repository." "Trackingbranch \u00bb%s\u00ab ist kein Branch im externen Repository."
::msgcat::mcset de "Working... please wait..." "Verarbeitung. Bitte warten..."
::msgcat::mcset de "Success" "Erfolgreich"
::msgcat::mcset de "Error: Command Failed" "Fehler: Kommando fehlgeschlagen"
::msgcat::mcset de "Goto Line:" "Gehe zu Zeile:"
::msgcat::mcset de "Go" "Gehe"
::msgcat::mcset de "This Detached Checkout" "Losgel\u00f6ste Arbeitskopie-Version"
::msgcat::mcset de "Revision Expression:" "Version Regex-Ausdruck:"
::msgcat::mcset de "Local Branch" "Lokaler Branch"
::msgcat::mcset de "Tracking Branch" "Trackingbranch"
::msgcat::mcset de "Tag" "Tag"
::msgcat::mcset de "Invalid revision: %s" "Ung\u00fcltige Version: %s"
::msgcat::mcset de "No revision selected." "Keine Version ausgew\u00e4hlt."
::msgcat::mcset de "Revision expression is empty." "Versions-Ausdruck ist leer."
::msgcat::mcset de "Updated" "Aktualisiert"
::msgcat::mcset de "URL" "URL"
::msgcat::mcset de "There is nothing to amend.\n\nYou are about to create the initial commit.  There is no commit before this to amend.\n" "Kein Commit zur Nachbesserung vorhanden.\n\nSie sind dabei, den ersten Commit zu erstellen. Es gibt keinen existierenden Commit, den Sie nachbessern k\u00f6nnten.\n"
::msgcat::mcset de "Cannot amend while merging.\n\nYou are currently in the middle of a merge that has not been fully completed.  You cannot amend the prior commit unless you first abort the current merge activity.\n" "Nachbesserung bei Zusammenf\u00fchrung nicht m\u00f6glich.\n\nSie haben das Zusammenf\u00fchren von Commits angefangen, aber noch nicht beendet. Sie k\u00f6nnen keinen vorigen Commit nachbessern, solange eine unfertige Zusammenf\u00fchrung existiert. Dazu m\u00fcssen Sie die Zusammenf\u00fchrung beenden oder abbrechen.\n"
::msgcat::mcset de "Error loading commit data for amend:" "Fehler beim Laden der Commitdaten f\u00fcr Nachbessern:"
::msgcat::mcset de "Unable to obtain your identity:" "Benutzername konnte nicht bestimmt werden:"
::msgcat::mcset de "Invalid GIT_COMMITTER_IDENT:" "Ung\u00fcltiger Wert von GIT_COMMITTER_INDENT:"
::msgcat::mcset de "warning: Tcl does not support encoding '%s'." "Warning: Tcl/Tk unterst\u00fctzt die Zeichencodierung \u00bb%s\u00ab nicht."
::msgcat::mcset de "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before another commit can be created.\n\nThe rescan will be automatically started now.\n" "Der letzte geladene Status stimmt nicht mehr mit dem Repository \u00fcberein.\n\nEin anderes Git-Programm hat das Repository seit dem letzten Laden ge\u00e4ndert.  Vor dem n\u00e4chsten Commit muss neu geladen werden.\n\nEs wird gleich neu geladen.\n"
::msgcat::mcset de "Unmerged files cannot be committed.\n\nFile %s has merge conflicts.  You must resolve them and stage the file before committing.\n" "Nicht zusammengef\u00fchrte Dateien k\u00f6nnen nicht committet werden.\n\nDie Datei \u00bb%s\u00ab hat noch nicht aufgel\u00f6ste Zusammenf\u00fchrungs-Konflikte. Sie m\u00fcssen diese Konflikte aufl\u00f6sen und die Dateien in die Bereitstellung hinzuf\u00fcgen, bevor Sie committen k\u00f6nnen.\n"
::msgcat::mcset de "Unknown file state %s detected.\n\nFile %s cannot be committed by this program.\n" "Unbekannter Dateizustand \u00bb%s\u00ab.\n\nDatei \u00bb%s\u00ab kann nicht committet werden.\n"
::msgcat::mcset de "No changes to commit.\n\nYou must stage at least 1 file before you can commit.\n" "Keine \u00c4nderungen vorhanden, die committet werden k\u00f6nnten.\n\nSie m\u00fcssen mindestens eine Datei bereitstellen, bevor Sie committen k\u00f6nnen.\n"
::msgcat::mcset de "Please supply a commit message.\n\nA good commit message has the following format:\n\n- First line: Describe in one sentence what you did.\n- Second line: Blank\n- Remaining lines: Describe why this change is good.\n" "Bitte geben Sie eine Versionsbeschreibung ein.\n\nEine gute Versionsbeschreibung enth\u00e4lt folgende Abschnitte:\n\n- Erste Zeile: Eine Zusammenfassung, was man gemacht hat.\n\n- Zweite Zeile: Leerzeile\n\n- Rest: Eine ausf\u00fchrliche Beschreibung, warum diese \u00c4nderung hilfreich ist.\n"
::msgcat::mcset de "Calling pre-commit hook..." "Aufrufen des \u00bbpre-commit hook\u00ab..."
::msgcat::mcset de "Commit declined by pre-commit hook." "Committen abgelehnt durch \u00bbpre-commit hook\u00ab."
::msgcat::mcset de "You are about to commit on a detached head. This is a potentially dangerous thing to do because if you switch to another branch you will lose your changes and it can be difficult to retrieve them later from the reflog. You should probably cancel this commit and create a new branch to continue.\n \n Do you really want to proceed with your Commit?" "Sie sind dabei, einen Commit auf losgel\u00f6ste Branchspitze (\u00bbcommit to detached head\u00ab) zu erstellen. Das ist riskant, denn wenn Sie zu einem anderen Branch wechseln, w\u00fcrden Sie diese \u00c4nderungen verlieren und es ist nachtr\u00e4glich schwierig, diese aus dem Commit-Log (\u00bbreflog\u00ab) wiederzufinden. Es wird empfohlen, diesen Commit abzubrechen und zun\u00e4chst einen neuen Branch zu erstellen.\n\n Wollen Sie den Commit trotzdem in dieser Form erstellen?"
::msgcat::mcset de "Calling commit-msg hook..." "Aufrufen des \u00bbcommit-msg hook\u00ab..."
::msgcat::mcset de "Commit declined by commit-msg hook." "Committen abgelehnt durch \u00bbcommit-msg hook\u00ab."
::msgcat::mcset de "Committing changes..." "\u00c4nderungen committen..."
::msgcat::mcset de "write-tree failed:" "write-tree fehlgeschlagen:"
::msgcat::mcset de "Commit failed." "Committen fehlgeschlagen."
::msgcat::mcset de "Commit %s appears to be corrupt" "Version \u00bb%s\u00ab scheint besch\u00e4digt zu sein"
::msgcat::mcset de "No changes to commit.\n\nNo files were modified by this commit and it was not a merge commit.\n\nA rescan will be automatically started now.\n" "Keine \u00c4nderungen zum committen.\n\nEs gibt keine ge\u00e4nderte Datei in diesem Commit und es wurde auch nichts zusammengef\u00fchrt.\n\nDas Arbeitsverzeichnis wird daher jetzt neu geladen.\n"
::msgcat::mcset de "No changes to commit." "Keine \u00c4nderungen, die committet werden k\u00f6nnen."
::msgcat::mcset de "commit-tree failed:" "commit-tree fehlgeschlagen:"
::msgcat::mcset de "update-ref failed:" "update-ref fehlgeschlagen:"
::msgcat::mcset de "Created commit %s: %s" "Commit %s erstellt: %s"
::msgcat::mcset de "%s (%s): Delete Branch" "%s (%s): Branch l\u00f6schen"
::msgcat::mcset de "Delete Local Branch" "Lokalen Branch l\u00f6schen"
::msgcat::mcset de "Local Branches" "Lokale Branches"
::msgcat::mcset de "Delete Only If Merged Into" "Nur l\u00f6schen, wenn zusammengef\u00fchrt nach"
::msgcat::mcset de "The following branches are not completely merged into %s:" "Folgende Branches sind noch nicht mit \u00bb%s\u00ab zusammengef\u00fchrt:"
::msgcat::mcset de " - %s:" " - %s:"
::msgcat::mcset de "Failed to delete branches:\n%s" "Fehler beim L\u00f6schen der Branches:\n%s"
::msgcat::mcset de "Invalid date from Git: %s" "Ung\u00fcltiges Datum von Git: %s"
::msgcat::mcset de "Number of loose objects" "Anzahl unverkn\u00fcpfter Objekte"
::msgcat::mcset de "Disk space used by loose objects" "Festplattenplatz von unverkn\u00fcpften Objekten"
::msgcat::mcset de "Number of packed objects" "Anzahl komprimierter Objekte"
::msgcat::mcset de "Number of packs" "Anzahl Komprimierungseinheiten"
::msgcat::mcset de "Disk space used by packed objects" "Festplattenplatz von komprimierten Objekten"
::msgcat::mcset de "Packed objects waiting for pruning" "Komprimierte Objekte, die zum Aufr\u00e4umen vorgesehen sind"
::msgcat::mcset de "Garbage files" "Dateien im M\u00fclleimer"
::msgcat::mcset de "%s (%s): Database Statistics" "%s (%s): Datenbankstatistik"
::msgcat::mcset de "Compressing the object database" "Objektdatenbank komprimieren"
::msgcat::mcset de "Verifying the object database with fsck-objects" "Die Objektdatenbank durch \u00bbfsck-objects\u00ab \u00fcberpr\u00fcfen lassen"
::msgcat::mcset de "This repository currently has approximately %i loose objects.\n\nTo maintain optimal performance it is strongly recommended that you compress the database.\n\nCompress the database now?" "Dieses Repository enth\u00e4lt ungef\u00e4hr %i nicht verkn\u00fcpfte Objekte.\n\nF\u00fcr eine optimale Performance wird empfohlen, die Datenbank des Repository zu komprimieren.\n\nSoll die Datenbank jetzt komprimiert werden?"
::msgcat::mcset de "%s: error" "%s: Fehler"
::msgcat::mcset de "%s: warning" "%s: Warnung"
::msgcat::mcset de "%s hook failed:" "%s hook fehlgeschlagen:"
::msgcat::mcset de "You must correct the above errors before committing." "Sie m\u00fcssen die obigen Fehler zuerst beheben, bevor Sie committen k\u00f6nnen."
::msgcat::mcset de "%s (%s): error" "%s (%s): Fehler"
::msgcat::mcset de "Cannot merge while amending.\n\nYou must finish amending this commit before starting any type of merge.\n" "Zusammenf\u00fchren kann nicht gleichzeitig mit Nachbessern durchgef\u00fchrt werden.\n\nSie m\u00fcssen zuerst den Nachbesserungs-Commit abschlie\u00dfen, bevor Sie zusammenf\u00fchren k\u00f6nnen.\n"
::msgcat::mcset de "Last scanned state does not match repository state.\n\nAnother Git program has modified this repository since the last scan.  A rescan must be performed before a merge can be performed.\n\nThe rescan will be automatically started now.\n" "Der letzte geladene Status stimmt nicht mehr mit dem Repository \u00fcberein.\n\nEin anderes Git-Programm hat das Repository seit dem letzten Laden ge\u00e4ndert.  Vor einem Zusammenf\u00fchren muss neu geladen werden.\n\nEs wird gleich neu geladen.\n"
::msgcat::mcset de "You are in the middle of a conflicted merge.\n\nFile %s has merge conflicts.\n\nYou must resolve them, stage the file, and commit to complete the current merge.  Only then can you begin another merge.\n" "Zusammenf\u00fchrung mit Konflikten.\n\nDie Datei \u00bb%s\u00ab enth\u00e4lt Konflikte beim Zusammenf\u00fchren.\n\nSie m\u00fcssen diese Konflikte per Hand aufl\u00f6sen. Anschlie\u00dfend m\u00fcssen Sie die Datei wieder bereitstellen und committen, um die Zusammenf\u00fchrung abzuschlie\u00dfen. Erst danach kann eine neue Zusammenf\u00fchrung begonnen werden.\n"
::msgcat::mcset de "You are in the middle of a change.\n\nFile %s is modified.\n\nYou should complete the current commit before starting a merge.  Doing so will help you abort a failed merge, should the need arise.\n" "Es liegen \u00c4nderungen vor.\n\nDie Datei \u00bb%s\u00ab wurde ge\u00e4ndert.\n\nSie sollten zuerst den bereitgestellten Commit abschlie\u00dfen, bevor Sie eine Zusammenf\u00fchrung beginnen.  Mit dieser Reihenfolge k\u00f6nnen Sie m\u00f6gliche Konflikte beim Zusammenf\u00fchren wesentlich einfacher beheben oder abbrechen.\n"
::msgcat::mcset de "%s of %s" "%s von %s"
::msgcat::mcset de "Merging %s and %s..." "Zusammenf\u00fchren von %s und %s..."
::msgcat::mcset de "Merge completed successfully." "Zusammenf\u00fchren erfolgreich abgeschlossen."
::msgcat::mcset de "Merge failed.  Conflict resolution is required." "Zusammenf\u00fchren fehlgeschlagen. Konfliktaufl\u00f6sung ist notwendig."
::msgcat::mcset de "%s (%s): Merge" "%s (%s): Zusammenf\u00fchren"
::msgcat::mcset de "Merge Into %s" "Zusammenf\u00fchren in \u00bb%s\u00ab"
::msgcat::mcset de "Revision To Merge" "Zusammenzuf\u00fchrende Version"
::msgcat::mcset de "Cannot abort while amending.\n\nYou must finish amending this commit.\n" "Abbruch der Nachbesserung ist nicht m\u00f6glich.\n\nSie m\u00fcssen die Nachbesserung diese Commits abschlie\u00dfen.\n"
::msgcat::mcset de "Abort merge?\n\nAborting the current merge will cause *ALL* uncommitted changes to be lost.\n\nContinue with aborting the current merge?" "Zusammenf\u00fchren abbrechen?\n\nWenn Sie abbrechen, gehen alle noch nicht committeten \u00c4nderungen verloren.\n\nZusammenf\u00fchren jetzt abbrechen?"
::msgcat::mcset de "Reset changes?\n\nResetting the changes will cause *ALL* uncommitted changes to be lost.\n\nContinue with resetting the current changes?" "\u00c4nderungen verwerfen?\n\nAlle noch nicht committeten \u00c4nderungen w\u00fcrden verloren gehen.\n\n\u00c4nderungen jetzt verwerfen?"
::msgcat::mcset de "Aborting" "Abbruch"
::msgcat::mcset de "files reset" "Dateien zur\u00fcckgesetzt"
::msgcat::mcset de "Abort failed." "Abbruch fehlgeschlagen."
::msgcat::mcset de "Abort completed.  Ready." "Abbruch durchgef\u00fchrt. Bereit."
