-- 'f' code formatting, with explicit precision (>= 0).  Output always
-- has the given number of places after the point;  zeros are added if
-- necessary to make this true.

-- zeros
%.0f 0 -> 0
%.1f 0 -> 0.0
%.2f 0 -> 0.00
%.3f 0 -> 0.000
%.50f 0 -> 0.00000000000000000000000000000000000000000000000000

-- precision 0;  result should never include a .
%.0f 1.5 -> 2
%.0f 2.5 -> 2
%.0f 3.5 -> 4
%.0f 0.0 -> 0
%.0f 0.1 -> 0
%.0f 0.001 -> 0
%.0f 10.0 -> 10
%.0f 10.1 -> 10
%.0f 10.01 -> 10
%.0f 123.456 -> 123
%.0f 1234.56 -> 1235
%.0f 1e49 -> 9999999999999999464902769475481793196872414789632
%.0f 9.9999999999999987e+49 -> 99999999999999986860582406952576489172979654066176
%.0f 1e50 -> 100000000000000007629769841091887003294964970946560

-- precision 1
%.1f 0.0001 -> 0.0
%.1f 0.001 -> 0.0
%.1f 0.01 -> 0.0
%.1f 0.04 -> 0.0
%.1f 0.06 -> 0.1
%.1f 0.25 -> 0.2
%.1f 0.75 -> 0.8
%.1f 1.4 -> 1.4
%.1f 1.5 -> 1.5
%.1f 10.0 -> 10.0
%.1f 1000.03 -> 1000.0
%.1f 1234.5678 -> 1234.6
%.1f 1234.7499 -> 1234.7
%.1f 1234.75 -> 1234.8

-- precision 2
%.2f 0.0001 -> 0.00
%.2f 0.001 -> 0.00
%.2f 0.004999 -> 0.00
%.2f 0.005001 -> 0.01
%.2f 0.01 -> 0.01
%.2f 0.125 -> 0.12
%.2f 0.375 -> 0.38
%.2f 1234500 -> 1234500.00
%.2f 1234560 -> 1234560.00
%.2f 1234567 -> 1234567.00
%.2f 1234567.8 -> 1234567.80
%.2f 1234567.89 -> 1234567.89
%.2f 1234567.891 -> 1234567.89
%.2f 1234567.8912 -> 1234567.89

-- alternate form always includes a decimal point.  This only
-- makes a difference when the precision is 0.
%#.0f 0 -> 0.
%#.1f 0 -> 0.0
%#.0f 1.5 -> 2.
%#.0f 2.5 -> 2.
%#.0f 10.1 -> 10.
%#.0f 1234.56 -> 1235.
%#.1f 1.4 -> 1.4
%#.2f 0.375 -> 0.38

-- if precision is omitted it defaults to 6
%f 0 -> 0.000000
%f 1230000 -> 1230000.000000
%f 1234567 -> 1234567.000000
%f 123.4567 -> 123.456700
%f 1.23456789 -> 1.234568
%f 0.00012 -> 0.000120
%f 0.000123 -> 0.000123
%f 0.00012345 -> 0.000123
%f 0.000001 -> 0.000001
%f 0.0000005001 -> 0.000001
%f 0.0000004999 -> 0.000000

-- 'e' code formatting with explicit precision (>= 0). Output should
-- always have exactly the number of places after the point that were
-- requested.

-- zeros
%.0e 0 -> 0e+00
%.1e 0 -> 0.0e+00
%.2e 0 -> 0.00e+00
%.10e 0 -> 0.0000000000e+00
%.50e 0 -> 0.00000000000000000000000000000000000000000000000000e+00

-- precision 0.  no decimal point in the output
%.0e 0.01 -> 1e-02
%.0e 0.1 -> 1e-01
%.0e 1 -> 1e+00
%.0e 10 -> 1e+01
%.0e 100 -> 1e+02
%.0e 0.012 -> 1e-02
%.0e 0.12 -> 1e-01
%.0e 1.2 -> 1e+00
%.0e 12 -> 1e+01
%.0e 120 -> 1e+02
%.0e 123.456 -> 1e+02
%.0e 0.000123456 -> 1e-04
%.0e 123456000 -> 1e+08
%.0e 0.5 -> 5e-01
%.0e 1.4 -> 1e+00
%.0e 1.5 -> 2e+00
%.0e 1.6 -> 2e+00
%.0e 2.4999999 -> 2e+00
%.0e 2.5 -> 2e+00
%.0e 2.5000001 -> 3e+00
%.0e 3.499999999999 -> 3e+00
%.0e 3.5 -> 4e+00
%.0e 4.5 -> 4e+00
%.0e 5.5 -> 6e+00
%.0e 6.5 -> 6e+00
%.0e 7.5 -> 8e+00
%.0e 8.5 -> 8e+00
%.0e 9.4999 -> 9e+00
%.0e 9.5 -> 1e+01
%.0e 10.5 -> 1e+01
%.0e 14.999 -> 1e+01
%.0e 15 -> 2e+01

-- precision 1
%.1e 0.0001 -> 1.0e-04
%.1e 0.001 -> 1.0e-03
%.1e 0.01 -> 1.0e-02
%.1e 0.1 -> 1.0e-01
%.1e 1 -> 1.0e+00
%.1e 10 -> 1.0e+01
%.1e 100 -> 1.0e+02
%.1e 120 -> 1.2e+02
%.1e 123 -> 1.2e+02
%.1e 123.4 -> 1.2e+02

-- precision 2
%.2e 0.00013 -> 1.30e-04
%.2e 0.000135 -> 1.35e-04
%.2e 0.0001357 -> 1.36e-04
%.2e 0.0001 -> 1.00e-04
%.2e 0.001 -> 1.00e-03
%.2e 0.01 -> 1.00e-02
%.2e 0.1 -> 1.00e-01
%.2e 1 -> 1.00e+00
%.2e 10 -> 1.00e+01
%.2e 100 -> 1.00e+02
%.2e 1000 -> 1.00e+03
%.2e 1500 -> 1.50e+03
%.2e 1590 -> 1.59e+03
%.2e 1598 -> 1.60e+03
%.2e 1598.7 -> 1.60e+03
%.2e 1598.76 -> 1.60e+03
%.2e 9999 -> 1.00e+04

-- omitted precision defaults to 6
%e 0 -> 0.000000e+00
%e 165 -> 1.650000e+02
%e 1234567 -> 1.234567e+06
%e 12345678 -> 1.234568e+07
%e 1.1 -> 1.100000e+00

-- alternate form always contains a decimal point.  This only makes
-- a difference when precision is 0.

%#.0e 0.01 -> 1.e-02
%#.0e 0.1 -> 1.e-01
%#.0e 1 -> 1.e+00
%#.0e 10 -> 1.e+01
%#.0e 100 -> 1.e+02
%#.0e 0.012 -> 1.e-02
%#.0e 0.12 -> 1.e-01
%#.0e 1.2 -> 1.e+00
%#.0e 12 -> 1.e+01
%#.0e 120 -> 1.e+02
%#.0e 123.456 -> 1.e+02
%#.0e 0.000123456 -> 1.e-04
%#.0e 123456000 -> 1.e+08
%#.0e 0.5 -> 5.e-01
%#.0e 1.4 -> 1.e+00
%#.0e 1.5 -> 2.e+00
%#.0e 1.6 -> 2.e+00
%#.0e 2.4999999 -> 2.e+00
%#.0e 2.5 -> 2.e+00
%#.0e 2.5000001 -> 3.e+00
%#.0e 3.499999999999 -> 3.e+00
%#.0e 3.5 -> 4.e+00
%#.0e 4.5 -> 4.e+00
%#.0e 5.5 -> 6.e+00
%#.0e 6.5 -> 6.e+00
%#.0e 7.5 -> 8.e+00
%#.0e 8.5 -> 8.e+00
%#.0e 9.4999 -> 9.e+00
%#.0e 9.5 -> 1.e+01
%#.0e 10.5 -> 1.e+01
%#.0e 14.999 -> 1.e+01
%#.0e 15 -> 2.e+01
%#.1e 123.4 -> 1.2e+02
%#.2e 0.0001357 -> 1.36e-04

-- 'g' code formatting.

-- zeros
%.0g 0 -> 0
%.1g 0 -> 0
%.2g 0 -> 0
%.3g 0 -> 0
%.4g 0 -> 0
%.10g 0 -> 0
%.50g 0 -> 0
%.100g 0 -> 0

-- precision 0 doesn't make a lot of sense for the 'g' code (what does
-- it mean to have no significant digits?); in practice, it's interpreted
-- as identical to precision 1
%.0g 1000 -> 1e+03
%.0g 100 -> 1e+02
%.0g 10 -> 1e+01
%.0g 1 -> 1
%.0g 0.1 -> 0.1
%.0g 0.01 -> 0.01
%.0g 1e-3 -> 0.001
%.0g 1e-4 -> 0.0001
%.0g 1e-5 -> 1e-05
%.0g 1e-6 -> 1e-06
%.0g 12 -> 1e+01
%.0g 120 -> 1e+02
%.0g 1.2 -> 1
%.0g 0.12 -> 0.1
%.0g 0.012 -> 0.01
%.0g 0.0012 -> 0.001
%.0g 0.00012 -> 0.0001
%.0g 0.000012 -> 1e-05
%.0g 0.0000012 -> 1e-06

-- precision 1 identical to precision 0
%.1g 1000 -> 1e+03
%.1g 100 -> 1e+02
%.1g 10 -> 1e+01
%.1g 1 -> 1
%.1g 0.1 -> 0.1
%.1g 0.01 -> 0.01
%.1g 1e-3 -> 0.001
%.1g 1e-4 -> 0.0001
%.1g 1e-5 -> 1e-05
%.1g 1e-6 -> 1e-06
%.1g 12 -> 1e+01
%.1g 120 -> 1e+02
%.1g 1.2 -> 1
%.1g 0.12 -> 0.1
%.1g 0.012 -> 0.01
%.1g 0.0012 -> 0.001
%.1g 0.00012 -> 0.0001
%.1g 0.000012 -> 1e-05
%.1g 0.0000012 -> 1e-06

-- precision 2
%.2g 1000 -> 1e+03
%.2g 100 -> 1e+02
%.2g 10 -> 10
%.2g 1 -> 1
%.2g 0.1 -> 0.1
%.2g 0.01 -> 0.01
%.2g 0.001 -> 0.001
%.2g 1e-4 -> 0.0001
%.2g 1e-5 -> 1e-05
%.2g 1e-6 -> 1e-06
%.2g 1234 -> 1.2e+03
%.2g 123 -> 1.2e+02
%.2g 12.3 -> 12
%.2g 1.23 -> 1.2
%.2g 0.123 -> 0.12
%.2g 0.0123 -> 0.012
%.2g 0.00123 -> 0.0012
%.2g 0.000123 -> 0.00012
%.2g 0.0000123 -> 1.2e-05

-- bad cases from http://bugs.python.org/issue9980
%.12g 38210.0 -> 38210
%.12g 37210.0 -> 37210
%.12g 36210.0 -> 36210

-- alternate g formatting:  always include decimal point and
-- exactly <precision> significant digits.
%#.0g 0 -> 0.
%#.1g 0 -> 0.
%#.2g 0 -> 0.0
%#.3g 0 -> 0.00
%#.4g 0 -> 0.000

%#.0g 0.2 -> 0.2
%#.1g 0.2 -> 0.2
%#.2g 0.2 -> 0.20
%#.3g 0.2 -> 0.200
%#.4g 0.2 -> 0.2000
%#.10g 0.2 -> 0.2000000000

%#.0g 2 -> 2.
%#.1g 2 -> 2.
%#.2g 2 -> 2.0
%#.3g 2 -> 2.00
%#.4g 2 -> 2.000

%#.0g 20 -> 2.e+01
%#.1g 20 -> 2.e+01
%#.2g 20 -> 20.
%#.3g 20 -> 20.0
%#.4g 20 -> 20.00

%#.0g 234.56 -> 2.e+02
%#.1g 234.56 -> 2.e+02
%#.2g 234.56 -> 2.3e+02
%#.3g 234.56 -> 235.
%#.4g 234.56 -> 234.6
%#.5g 234.56 -> 234.56
%#.6g 234.56 -> 234.560

-- repr formatting.  Result always includes decimal point and at
-- least one digit after the point, or an exponent.
%r 0 -> 0.0
%r 1 -> 1.0

%r 0.01 -> 0.01
%r 0.02 -> 0.02
%r 0.03 -> 0.03
%r 0.04 -> 0.04
%r 0.05 -> 0.05

-- values >= 1e16 get an exponent
%r 10 -> 10.0
%r 100 -> 100.0
%r 1e15 -> 1000000000000000.0
%r 9.999e15 -> 9999000000000000.0
%r 9999999999999998 -> 9999999999999998.0
%r 9999999999999999 -> 1e+16
%r 1e16 -> 1e+16
%r 1e17 -> 1e+17

-- as do values < 1e-4
%r 1e-3 -> 0.001
%r 1.001e-4 -> 0.0001001
%r 1.0000000000000001e-4 -> 0.0001
%r 1.000000000000001e-4 -> 0.0001000000000000001
%r 1.00000000001e-4 -> 0.000100000000001
%r 1.0000000001e-4 -> 0.00010000000001
%r 1e-4 -> 0.0001
%r 0.99999999999999999e-4 -> 0.0001
%r 0.9999999999999999e-4 -> 9.999999999999999e-05
%r 0.999999999999e-4 -> 9.99999999999e-05
%r 0.999e-4 -> 9.99e-05
%r 1e-5 -> 1e-05
