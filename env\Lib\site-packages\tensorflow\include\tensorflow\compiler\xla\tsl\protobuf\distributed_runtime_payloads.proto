syntax = "proto3";

package tensorflow.distributed_runtime;

option cc_enable_arenas = true;
option go_package = "github.com/tsl/tsl/go/core/protobuf/for_core_protos_go_proto";

// Used to serialize and transmit tensorflow::Status payloads through
// grpc::Status `error_details` since grpc::Status lacks payload API.
// TODO(b/*********): Use GRPC API once supported.
message GrpcPayloadContainer {
  map<string, bytes> payloads = 1;
}

// If included as a payload, this message flags the Status to have lost payloads
// during the GRPC transmission.
// URI: "type.googleapis.com/tensorflow.distributed_runtime.GrpcPayloadsLost"
message GrpcPayloadsLost {}

// If included as a payload, this message flags the Status to be a possible
// outcome of a worker restart.
// URI:
// "type.googleapis.com/tensorflow.distributed_runtime.WorkerPossiblyRestarted"
message WorkerPossiblyRestarted {}
