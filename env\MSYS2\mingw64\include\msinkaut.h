/*** Autogenerated by WIDL 10.8 from include/msinkaut.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __msinkaut_h__
#define __msinkaut_h__

/* Forward declarations */

#ifndef __IInkExtendedProperty_FWD_DEFINED__
#define __IInkExtendedProperty_FWD_DEFINED__
typedef interface IInkExtendedProperty IInkExtendedProperty;
#ifdef __cplusplus
interface IInkExtendedProperty;
#endif /* __cplusplus */
#endif

#ifndef __IInkExtendedProperties_FWD_DEFINED__
#define __IInkExtendedProperties_FWD_DEFINED__
typedef interface IInkExtendedProperties IInkExtendedProperties;
#ifdef __cplusplus
interface IInkExtendedProperties;
#endif /* __cplusplus */
#endif

#ifndef __IInkDrawingAttributes_FWD_DEFINED__
#define __IInkDrawingAttributes_FWD_DEFINED__
typedef interface IInkDrawingAttributes IInkDrawingAttributes;
#ifdef __cplusplus
interface IInkDrawingAttributes;
#endif /* __cplusplus */
#endif

#ifndef __IInkRectangle_FWD_DEFINED__
#define __IInkRectangle_FWD_DEFINED__
typedef interface IInkRectangle IInkRectangle;
#ifdef __cplusplus
interface IInkRectangle;
#endif /* __cplusplus */
#endif

#ifndef __IInkTablet_FWD_DEFINED__
#define __IInkTablet_FWD_DEFINED__
typedef interface IInkTablet IInkTablet;
#ifdef __cplusplus
interface IInkTablet;
#endif /* __cplusplus */
#endif

#ifndef __IInkCursorButton_FWD_DEFINED__
#define __IInkCursorButton_FWD_DEFINED__
typedef interface IInkCursorButton IInkCursorButton;
#ifdef __cplusplus
interface IInkCursorButton;
#endif /* __cplusplus */
#endif

#ifndef __IInkCursorButtons_FWD_DEFINED__
#define __IInkCursorButtons_FWD_DEFINED__
typedef interface IInkCursorButtons IInkCursorButtons;
#ifdef __cplusplus
interface IInkCursorButtons;
#endif /* __cplusplus */
#endif

#ifndef __IInkCursor_FWD_DEFINED__
#define __IInkCursor_FWD_DEFINED__
typedef interface IInkCursor IInkCursor;
#ifdef __cplusplus
interface IInkCursor;
#endif /* __cplusplus */
#endif

#ifndef __IInkTransform_FWD_DEFINED__
#define __IInkTransform_FWD_DEFINED__
typedef interface IInkTransform IInkTransform;
#ifdef __cplusplus
interface IInkTransform;
#endif /* __cplusplus */
#endif

#ifndef __IInkRecognitionAlternates_FWD_DEFINED__
#define __IInkRecognitionAlternates_FWD_DEFINED__
typedef interface IInkRecognitionAlternates IInkRecognitionAlternates;
#ifdef __cplusplus
interface IInkRecognitionAlternates;
#endif /* __cplusplus */
#endif

#ifndef __IInkRecognitionAlternate_FWD_DEFINED__
#define __IInkRecognitionAlternate_FWD_DEFINED__
typedef interface IInkRecognitionAlternate IInkRecognitionAlternate;
#ifdef __cplusplus
interface IInkRecognitionAlternate;
#endif /* __cplusplus */
#endif

#ifndef __IInkRecognitionResult_FWD_DEFINED__
#define __IInkRecognitionResult_FWD_DEFINED__
typedef interface IInkRecognitionResult IInkRecognitionResult;
#ifdef __cplusplus
interface IInkRecognitionResult;
#endif /* __cplusplus */
#endif

#ifndef __IInkStrokeDisp_FWD_DEFINED__
#define __IInkStrokeDisp_FWD_DEFINED__
typedef interface IInkStrokeDisp IInkStrokeDisp;
#ifdef __cplusplus
interface IInkStrokeDisp;
#endif /* __cplusplus */
#endif

#ifndef __IInkCustomStrokes_FWD_DEFINED__
#define __IInkCustomStrokes_FWD_DEFINED__
typedef interface IInkCustomStrokes IInkCustomStrokes;
#ifdef __cplusplus
interface IInkCustomStrokes;
#endif /* __cplusplus */
#endif

#ifndef __IInkDisp_FWD_DEFINED__
#define __IInkDisp_FWD_DEFINED__
typedef interface IInkDisp IInkDisp;
#ifdef __cplusplus
interface IInkDisp;
#endif /* __cplusplus */
#endif

#ifndef __IInkStrokes_FWD_DEFINED__
#define __IInkStrokes_FWD_DEFINED__
typedef interface IInkStrokes IInkStrokes;
#ifdef __cplusplus
interface IInkStrokes;
#endif /* __cplusplus */
#endif

#ifndef __IInkRenderer_FWD_DEFINED__
#define __IInkRenderer_FWD_DEFINED__
typedef interface IInkRenderer IInkRenderer;
#ifdef __cplusplus
interface IInkRenderer;
#endif /* __cplusplus */
#endif

#ifndef __IInkCursors_FWD_DEFINED__
#define __IInkCursors_FWD_DEFINED__
typedef interface IInkCursors IInkCursors;
#ifdef __cplusplus
interface IInkCursors;
#endif /* __cplusplus */
#endif

#ifndef __IInkCollector_FWD_DEFINED__
#define __IInkCollector_FWD_DEFINED__
typedef interface IInkCollector IInkCollector;
#ifdef __cplusplus
interface IInkCollector;
#endif /* __cplusplus */
#endif

#ifndef ___IInkCollectorEvents_FWD_DEFINED__
#define ___IInkCollectorEvents_FWD_DEFINED__
typedef interface _IInkCollectorEvents _IInkCollectorEvents;
#ifdef __cplusplus
interface _IInkCollectorEvents;
#endif /* __cplusplus */
#endif

#ifndef __InkCollector_FWD_DEFINED__
#define __InkCollector_FWD_DEFINED__
#ifdef __cplusplus
typedef class InkCollector InkCollector;
#else
typedef struct InkCollector InkCollector;
#endif /* defined __cplusplus */
#endif /* defined __InkCollector_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <tpcshrd.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum InkRasterOperation {
    IRO_Black = 1,
    IRO_NotMergePen = 2,
    IRO_MaskNotPen = 3,
    IRO_NotCopyPen = 4,
    IRO_MaskPenNot = 5,
    IRO_Not = 6,
    IRO_XOrPen = 7,
    IRO_NotMaskPen = 8,
    IRO_MaskPen = 9,
    IRO_NotXOrPen = 10,
    IRO_NoOperation = 11,
    IRO_MergeNotPen = 12,
    IRO_CopyPen = 13,
    IRO_MergePenNot = 14,
    IRO_MergePen = 15,
    IRO_White = 16
} InkRasterOperation;
typedef enum InkPenTip {
    IPT_Ball = 0,
    IPT_Rectangle = 1
} InkPenTip;
typedef enum TabletHardwareCapabilities {
    THWC_Integrated = 0x1,
    THWC_CursorMustTouch = 0x2,
    THWC_HardProximity = 0x4,
    THWC_CursorsHavePhysicalIds = 0x8
} TabletHardwareCapabilities;
typedef enum TabletPropertyMetricUnit {
    TPMU_Default = 0,
    TPMU_Inches = 1,
    TPMU_Centimeters = 2,
    TPMU_Degrees = 3,
    TPMU_Radians = 4,
    TPMU_Seconds = 5,
    TPMU_Pounds = 6,
    TPMU_Grams = 7
} TabletPropertyMetricUnit;
typedef enum InkCursorButtonState {
    ICBS_Unavailable = 0,
    ICBS_Up = 1,
    ICBS_Down = 2
} InkCursorButtonState;
typedef enum InkRecognitionConfidence {
    IRC_Strong = 0,
    IRC_Intermediate = 1,
    IRC_Poor = 2
} InkRecognitionConfidence;
typedef enum InkBoundingBoxMode {
    IBBM_Default = 0,
    IBBM_NoCurveFit = 1,
    IBBM_CurveFit = 2,
    IBBM_PointsOnly = 3,
    IBBM_Union = 4
} InkBoundingBoxMode;
typedef enum InkExtractFlags {
    IEF_CopyFromOriginal = 0,
    IEF_RemoveFromOriginal = 1,
    IEF_Default = IEF_RemoveFromOriginal
} InkExtractFlags;
typedef enum InkPersistenceFormat {
    IPF_InkSerializedFormat = 0,
    IPF_Base64InkSerializedFormat = 1,
    IPF_GIF = 2,
    IPF_Base64GIF = 3
} InkPersistenceFormat;
typedef enum InkPersistenceCompressionMode {
    IPCM_Default = 0,
    IPCM_MaximumCompression = 1,
    IPCM_NoCompression = 2
} InkPersistenceCompressionMode;
typedef enum InkClipboardFormats {
    ICF_None = 0x0,
    ICF_InkSerializedFormat = 0x1,
    ICF_SketchInk = 0x2,
    ICF_TextInk = 0x6,
    ICF_EnhancedMetafile = 0x8,
    ICF_Metafile = 0x20,
    ICF_Bitmap = 0x40,
    ICF_PasteMask = 0x7,
    ICF_CopyMask = 0x7f,
    ICF_Default = ICF_CopyMask
} InkClipboardFormats;
typedef enum InkClipboardModes {
    ICB_Copy = 0x0,
    ICB_Cut = 0x1,
    ICB_ExtractOnly = 0x30,
    ICB_DelayedCopy = 0x20,
    ICB_Default = ICB_Copy
} InkClipboardModes;
typedef enum InkCollectionMode {
    ICM_InkOnly = 0,
    ICM_GestureOnly = 1,
    ICM_InkAndGesture = 2
} InkCollectionMode;
typedef enum InkMousePointer {
    IMP_Default = 0,
    IMP_Arrow = 1,
    IMP_Crosshair = 2,
    IMP_Ibeam = 3,
    IMP_SizeNESW = 4,
    IMP_SizeNS = 5,
    IMP_SizeNWSE = 6,
    IMP_SizeWE = 7,
    IMP_UpArrow = 8,
    IMP_Hourglass = 9,
    IMP_NoDrop = 10,
    IMP_ArrowHourglass = 11,
    IMP_ArrowQuestion = 12,
    IMP_SizeAll = 13,
    IMP_Hand = 14,
    IMP_Custom = 99
} InkMousePointer;
typedef enum InkApplicationGesture {
    IAG_AllGestures = 0x0,
    IAG_NoGesture = 0xf000,
    IAG_Scratchout = 0xf001,
    IAG_Triangle = 0xf002,
    IAG_Square = 0xf003,
    IAG_Star = 0xf004,
    IAG_Check = 0xf005,
    IAG_Curlicue = 0xf010,
    IAG_DoubleCurlicue = 0xf011,
    IAG_Circle = 0xf020,
    IAG_DoubleCircle = 0xf021,
    IAG_SemiCircleLeft = 0xf028,
    IAG_SemiCircleRight = 0xf029,
    IAG_ChevronUp = 0xf030,
    IAG_ChevronDown = 0xf031,
    IAG_ChevronLeft = 0xf032,
    IAG_ChevronRight = 0xf033,
    IAG_ArrowUp = 0xf038,
    IAG_ArrowDown = 0xf039,
    IAG_ArrowLeft = 0xf03a,
    IAG_ArrowRight = 0xf03b,
    IAG_Up = 0xf058,
    IAG_Down = 0xf059,
    IAG_Left = 0xf05a,
    IAG_Right = 0xf05b,
    IAG_UpDown = 0xf060,
    IAG_DownUp = 0xf061,
    IAG_LeftRight = 0xf062,
    IAG_RightLeft = 0xf063,
    IAG_UpLeftLong = 0xf064,
    IAG_UpRightLong = 0xf065,
    IAG_DownLeftLong = 0xf066,
    IAG_DownRightLong = 0xf067,
    IAG_UpLeft = 0xf068,
    IAG_UpRight = 0xf069,
    IAG_DownLeft = 0xf06a,
    IAG_DownRight = 0xf06b,
    IAG_LeftUp = 0xf06c,
    IAG_LeftDown = 0xf06d,
    IAG_RightUp = 0xf06e,
    IAG_RightDown = 0xf06f,
    IAG_Exclamation = 0xf0a4,
    IAG_Tap = 0xf0f0,
    IAG_DoubleTap = 0xf0f1
} InkApplicationGesture;
typedef enum InkCollectorEventInterest {
    ICEI_DefaultEvents = -1,
    ICEI_CursorDown = ICEI_DefaultEvents + 1,
    ICEI_Stroke = ICEI_CursorDown + 1,
    ICEI_NewPackets = ICEI_Stroke + 1,
    ICEI_NewInAirPackets = ICEI_NewPackets + 1,
    ICEI_CursorButtonDown = ICEI_NewInAirPackets + 1,
    ICEI_CursorButtonUp = ICEI_CursorButtonDown + 1,
    ICEI_CursorInRange = ICEI_CursorButtonUp + 1,
    ICEI_CursorOutOfRange = ICEI_CursorInRange + 1,
    ICEI_SystemGesture = ICEI_CursorOutOfRange + 1,
    ICEI_TabletAdded = ICEI_SystemGesture + 1,
    ICEI_TabletRemoved = ICEI_TabletAdded + 1,
    ICEI_MouseDown = ICEI_TabletRemoved + 1,
    ICEI_MouseMove = ICEI_MouseDown + 1,
    ICEI_MouseUp = ICEI_MouseMove + 1,
    ICEI_MouseWheel = ICEI_MouseUp + 1,
    ICEI_DblClick = ICEI_MouseWheel + 1,
    ICEI_AllEvents = ICEI_DblClick + 1
} InkCollectorEventInterest;
typedef enum DISPID_InkCollectorEvent {
    DISPID_ICEStroke = 1,
    DISPID_ICECursorDown = DISPID_ICEStroke + 1,
    DISPID_ICENewPackets = DISPID_ICECursorDown + 1,
    DISPID_ICENewInAirPackets = DISPID_ICENewPackets + 1,
    DISPID_ICECursorButtonDown = DISPID_ICENewInAirPackets + 1,
    DISPID_ICECursorButtonUp = DISPID_ICECursorButtonDown + 1,
    DISPID_ICECursorInRange = DISPID_ICECursorButtonUp + 1,
    DISPID_ICECursorOutOfRange = DISPID_ICECursorInRange + 1,
    DISPID_ICESystemGesture = DISPID_ICECursorOutOfRange + 1,
    DISPID_ICEGesture = DISPID_ICESystemGesture + 1,
    DISPID_ICETabletAdded = DISPID_ICEGesture + 1,
    DISPID_ICETabletRemoved = DISPID_ICETabletAdded + 1,
    DISPID_IOEPainting = DISPID_ICETabletRemoved + 1,
    DISPID_IOEPainted = DISPID_IOEPainting + 1,
    DISPID_IOESelectionChanging = DISPID_IOEPainted + 1,
    DISPID_IOESelectionChanged = DISPID_IOESelectionChanging + 1,
    DISPID_IOESelectionMoving = DISPID_IOESelectionChanged + 1,
    DISPID_IOESelectionMoved = DISPID_IOESelectionMoving + 1,
    DISPID_IOESelectionResizing = DISPID_IOESelectionMoved + 1,
    DISPID_IOESelectionResized = DISPID_IOESelectionResizing + 1,
    DISPID_IOEStrokesDeleting = DISPID_IOESelectionResized + 1,
    DISPID_IOEStrokesDeleted = DISPID_IOEStrokesDeleting + 1,
    DISPID_IPEChangeUICues = DISPID_IOEStrokesDeleted + 1,
    DISPID_IPEClick = DISPID_IPEChangeUICues + 1,
    DISPID_IPEDblClick = DISPID_IPEClick + 1,
    DISPID_IPEInvalidated = DISPID_IPEDblClick + 1,
    DISPID_IPEMouseDown = DISPID_IPEInvalidated + 1,
    DISPID_IPEMouseEnter = DISPID_IPEMouseDown + 1,
    DISPID_IPEMouseHover = DISPID_IPEMouseEnter + 1,
    DISPID_IPEMouseLeave = DISPID_IPEMouseHover + 1,
    DISPID_IPEMouseMove = DISPID_IPEMouseLeave + 1,
    DISPID_IPEMouseUp = DISPID_IPEMouseMove + 1,
    DISPID_IPEMouseWheel = DISPID_IPEMouseUp + 1,
    DISPID_IPESizeModeChanged = DISPID_IPEMouseWheel + 1,
    DISPID_IPEStyleChanged = DISPID_IPESizeModeChanged + 1,
    DISPID_IPESystemColorsChanged = DISPID_IPEStyleChanged + 1,
    DISPID_IPEKeyDown = DISPID_IPESystemColorsChanged + 1,
    DISPID_IPEKeyPress = DISPID_IPEKeyDown + 1,
    DISPID_IPEKeyUp = DISPID_IPEKeyPress + 1,
    DISPID_IPEResize = DISPID_IPEKeyUp + 1,
    DISPID_IPESizeChanged = DISPID_IPEResize + 1
} DISPID_InkCollectorEvent;
#ifndef __IInkDisp_FWD_DEFINED__
#define __IInkDisp_FWD_DEFINED__
typedef interface IInkDisp IInkDisp;
#ifdef __cplusplus
interface IInkDisp;
#endif /* __cplusplus */
#endif

#ifndef __IInkStrokes_FWD_DEFINED__
#define __IInkStrokes_FWD_DEFINED__
typedef interface IInkStrokes IInkStrokes;
#ifdef __cplusplus
interface IInkStrokes;
#endif /* __cplusplus */
#endif

#ifndef __IInkRecognitionAlternate_FWD_DEFINED__
#define __IInkRecognitionAlternate_FWD_DEFINED__
typedef interface IInkRecognitionAlternate IInkRecognitionAlternate;
#ifdef __cplusplus
interface IInkRecognitionAlternate;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IInkExtendedProperty interface
 */
#ifndef __IInkExtendedProperty_INTERFACE_DEFINED__
#define __IInkExtendedProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkExtendedProperty, 0xdb489209, 0xb7c3, 0x411d, 0x90,0xf6, 0x15,0x48,0xcf,0xff,0x27,0x1e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db489209-b7c3-411d-90f6-1548cfff271e")
IInkExtendedProperty : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Guid(
        BSTR *Guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Data(
        VARIANT *Data) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Data(
        VARIANT Data) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkExtendedProperty, 0xdb489209, 0xb7c3, 0x411d, 0x90,0xf6, 0x15,0x48,0xcf,0xff,0x27,0x1e)
#endif
#else
typedef struct IInkExtendedPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkExtendedProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkExtendedProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkExtendedProperty *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkExtendedProperty *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkExtendedProperty *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkExtendedProperty *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkExtendedProperty *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkExtendedProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Guid)(
        IInkExtendedProperty *This,
        BSTR *Guid);

    HRESULT (STDMETHODCALLTYPE *get_Data)(
        IInkExtendedProperty *This,
        VARIANT *Data);

    HRESULT (STDMETHODCALLTYPE *put_Data)(
        IInkExtendedProperty *This,
        VARIANT Data);

    END_INTERFACE
} IInkExtendedPropertyVtbl;

interface IInkExtendedProperty {
    CONST_VTBL IInkExtendedPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkExtendedProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkExtendedProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkExtendedProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkExtendedProperty_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkExtendedProperty_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkExtendedProperty_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkExtendedProperty_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkExtendedProperty methods ***/
#define IInkExtendedProperty_get_Guid(This,Guid) (This)->lpVtbl->get_Guid(This,Guid)
#define IInkExtendedProperty_get_Data(This,Data) (This)->lpVtbl->get_Data(This,Data)
#define IInkExtendedProperty_put_Data(This,Data) (This)->lpVtbl->put_Data(This,Data)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkExtendedProperty_QueryInterface(IInkExtendedProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkExtendedProperty_AddRef(IInkExtendedProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkExtendedProperty_Release(IInkExtendedProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkExtendedProperty_GetTypeInfoCount(IInkExtendedProperty* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkExtendedProperty_GetTypeInfo(IInkExtendedProperty* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkExtendedProperty_GetIDsOfNames(IInkExtendedProperty* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkExtendedProperty_Invoke(IInkExtendedProperty* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkExtendedProperty methods ***/
static inline HRESULT IInkExtendedProperty_get_Guid(IInkExtendedProperty* This,BSTR *Guid) {
    return This->lpVtbl->get_Guid(This,Guid);
}
static inline HRESULT IInkExtendedProperty_get_Data(IInkExtendedProperty* This,VARIANT *Data) {
    return This->lpVtbl->get_Data(This,Data);
}
static inline HRESULT IInkExtendedProperty_put_Data(IInkExtendedProperty* This,VARIANT Data) {
    return This->lpVtbl->put_Data(This,Data);
}
#endif
#endif

#endif


#endif  /* __IInkExtendedProperty_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkExtendedProperties interface
 */
#ifndef __IInkExtendedProperties_INTERFACE_DEFINED__
#define __IInkExtendedProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkExtendedProperties, 0x89f2a8be, 0x95a9, 0x4530, 0x8b,0x8f, 0x88,0xe9,0x71,0xe3,0xe2,0x5f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("89f2a8be-95a9-4530-8b8f-88e971e3e25f")
IInkExtendedProperties : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *Count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **_NewEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        VARIANT Identifier,
        IInkExtendedProperty **Item) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        BSTR Guid,
        VARIANT Data,
        IInkExtendedProperty **InkExtendedProperty) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        VARIANT Identifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoesPropertyExist(
        BSTR Guid,
        VARIANT_BOOL *DoesPropertyExist) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkExtendedProperties, 0x89f2a8be, 0x95a9, 0x4530, 0x8b,0x8f, 0x88,0xe9,0x71,0xe3,0xe2,0x5f)
#endif
#else
typedef struct IInkExtendedPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkExtendedProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkExtendedProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkExtendedProperties *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkExtendedProperties *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkExtendedProperties *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkExtendedProperties *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkExtendedProperties *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkExtendedProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IInkExtendedProperties *This,
        LONG *Count);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IInkExtendedProperties *This,
        IUnknown **_NewEnum);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IInkExtendedProperties *This,
        VARIANT Identifier,
        IInkExtendedProperty **Item);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IInkExtendedProperties *This,
        BSTR Guid,
        VARIANT Data,
        IInkExtendedProperty **InkExtendedProperty);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IInkExtendedProperties *This,
        VARIANT Identifier);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IInkExtendedProperties *This);

    HRESULT (STDMETHODCALLTYPE *DoesPropertyExist)(
        IInkExtendedProperties *This,
        BSTR Guid,
        VARIANT_BOOL *DoesPropertyExist);

    END_INTERFACE
} IInkExtendedPropertiesVtbl;

interface IInkExtendedProperties {
    CONST_VTBL IInkExtendedPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkExtendedProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkExtendedProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkExtendedProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkExtendedProperties_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkExtendedProperties_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkExtendedProperties_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkExtendedProperties_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkExtendedProperties methods ***/
#define IInkExtendedProperties_get_Count(This,Count) (This)->lpVtbl->get_Count(This,Count)
#define IInkExtendedProperties_get__NewEnum(This,_NewEnum) (This)->lpVtbl->get__NewEnum(This,_NewEnum)
#define IInkExtendedProperties_Item(This,Identifier,Item) (This)->lpVtbl->Item(This,Identifier,Item)
#define IInkExtendedProperties_Add(This,Guid,Data,InkExtendedProperty) (This)->lpVtbl->Add(This,Guid,Data,InkExtendedProperty)
#define IInkExtendedProperties_Remove(This,Identifier) (This)->lpVtbl->Remove(This,Identifier)
#define IInkExtendedProperties_Clear(This) (This)->lpVtbl->Clear(This)
#define IInkExtendedProperties_DoesPropertyExist(This,Guid,DoesPropertyExist) (This)->lpVtbl->DoesPropertyExist(This,Guid,DoesPropertyExist)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkExtendedProperties_QueryInterface(IInkExtendedProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkExtendedProperties_AddRef(IInkExtendedProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkExtendedProperties_Release(IInkExtendedProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkExtendedProperties_GetTypeInfoCount(IInkExtendedProperties* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkExtendedProperties_GetTypeInfo(IInkExtendedProperties* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkExtendedProperties_GetIDsOfNames(IInkExtendedProperties* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkExtendedProperties_Invoke(IInkExtendedProperties* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkExtendedProperties methods ***/
static inline HRESULT IInkExtendedProperties_get_Count(IInkExtendedProperties* This,LONG *Count) {
    return This->lpVtbl->get_Count(This,Count);
}
static inline HRESULT IInkExtendedProperties_get__NewEnum(IInkExtendedProperties* This,IUnknown **_NewEnum) {
    return This->lpVtbl->get__NewEnum(This,_NewEnum);
}
static inline HRESULT IInkExtendedProperties_Item(IInkExtendedProperties* This,VARIANT Identifier,IInkExtendedProperty **Item) {
    return This->lpVtbl->Item(This,Identifier,Item);
}
static inline HRESULT IInkExtendedProperties_Add(IInkExtendedProperties* This,BSTR Guid,VARIANT Data,IInkExtendedProperty **InkExtendedProperty) {
    return This->lpVtbl->Add(This,Guid,Data,InkExtendedProperty);
}
static inline HRESULT IInkExtendedProperties_Remove(IInkExtendedProperties* This,VARIANT Identifier) {
    return This->lpVtbl->Remove(This,Identifier);
}
static inline HRESULT IInkExtendedProperties_Clear(IInkExtendedProperties* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT IInkExtendedProperties_DoesPropertyExist(IInkExtendedProperties* This,BSTR Guid,VARIANT_BOOL *DoesPropertyExist) {
    return This->lpVtbl->DoesPropertyExist(This,Guid,DoesPropertyExist);
}
#endif
#endif

#endif


#endif  /* __IInkExtendedProperties_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkDrawingAttributes interface
 */
#ifndef __IInkDrawingAttributes_INTERFACE_DEFINED__
#define __IInkDrawingAttributes_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkDrawingAttributes, 0xbf519b75, 0x0a15, 0x4623, 0xad,0xc9, 0xc0,0x0d,0x43,0x6a,0x80,0x92);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bf519b75-0a15-4623-adc9-c00d436a8092")
IInkDrawingAttributes : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Color(
        LONG *CurrentColor) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Color(
        LONG NewColor) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Width(
        float *CurrentWidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Width(
        float NewWidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Height(
        float *CurrentHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Height(
        float NewHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FitToCurve(
        VARIANT_BOOL *Flag) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FitToCurve(
        VARIANT_BOOL Flag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IgnorePressure(
        VARIANT_BOOL *Flag) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IgnorePressure(
        VARIANT_BOOL Flag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AntiAliased(
        VARIANT_BOOL *Flag) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AntiAliased(
        VARIANT_BOOL Flag) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Transparency(
        LONG *CurrentTransparency) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Transparency(
        LONG NewTransparency) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RasterOperation(
        InkRasterOperation *CurrentRasterOperation) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RasterOperation(
        InkRasterOperation NewRasterOperation) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PenTip(
        InkPenTip *CurrentPenTip) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_PenTip(
        InkPenTip NewPenTip) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExtendedProperties(
        IInkExtendedProperties **Properties) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IInkDrawingAttributes **DrawingAttributes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkDrawingAttributes, 0xbf519b75, 0x0a15, 0x4623, 0xad,0xc9, 0xc0,0x0d,0x43,0x6a,0x80,0x92)
#endif
#else
typedef struct IInkDrawingAttributesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkDrawingAttributes *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkDrawingAttributes *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkDrawingAttributes *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkDrawingAttributes *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkDrawingAttributes *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkDrawingAttributes *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkDrawingAttributes *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkDrawingAttributes methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Color)(
        IInkDrawingAttributes *This,
        LONG *CurrentColor);

    HRESULT (STDMETHODCALLTYPE *put_Color)(
        IInkDrawingAttributes *This,
        LONG NewColor);

    HRESULT (STDMETHODCALLTYPE *get_Width)(
        IInkDrawingAttributes *This,
        float *CurrentWidth);

    HRESULT (STDMETHODCALLTYPE *put_Width)(
        IInkDrawingAttributes *This,
        float NewWidth);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        IInkDrawingAttributes *This,
        float *CurrentHeight);

    HRESULT (STDMETHODCALLTYPE *put_Height)(
        IInkDrawingAttributes *This,
        float NewHeight);

    HRESULT (STDMETHODCALLTYPE *get_FitToCurve)(
        IInkDrawingAttributes *This,
        VARIANT_BOOL *Flag);

    HRESULT (STDMETHODCALLTYPE *put_FitToCurve)(
        IInkDrawingAttributes *This,
        VARIANT_BOOL Flag);

    HRESULT (STDMETHODCALLTYPE *get_IgnorePressure)(
        IInkDrawingAttributes *This,
        VARIANT_BOOL *Flag);

    HRESULT (STDMETHODCALLTYPE *put_IgnorePressure)(
        IInkDrawingAttributes *This,
        VARIANT_BOOL Flag);

    HRESULT (STDMETHODCALLTYPE *get_AntiAliased)(
        IInkDrawingAttributes *This,
        VARIANT_BOOL *Flag);

    HRESULT (STDMETHODCALLTYPE *put_AntiAliased)(
        IInkDrawingAttributes *This,
        VARIANT_BOOL Flag);

    HRESULT (STDMETHODCALLTYPE *get_Transparency)(
        IInkDrawingAttributes *This,
        LONG *CurrentTransparency);

    HRESULT (STDMETHODCALLTYPE *put_Transparency)(
        IInkDrawingAttributes *This,
        LONG NewTransparency);

    HRESULT (STDMETHODCALLTYPE *get_RasterOperation)(
        IInkDrawingAttributes *This,
        InkRasterOperation *CurrentRasterOperation);

    HRESULT (STDMETHODCALLTYPE *put_RasterOperation)(
        IInkDrawingAttributes *This,
        InkRasterOperation NewRasterOperation);

    HRESULT (STDMETHODCALLTYPE *get_PenTip)(
        IInkDrawingAttributes *This,
        InkPenTip *CurrentPenTip);

    HRESULT (STDMETHODCALLTYPE *put_PenTip)(
        IInkDrawingAttributes *This,
        InkPenTip NewPenTip);

    HRESULT (STDMETHODCALLTYPE *get_ExtendedProperties)(
        IInkDrawingAttributes *This,
        IInkExtendedProperties **Properties);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IInkDrawingAttributes *This,
        IInkDrawingAttributes **DrawingAttributes);

    END_INTERFACE
} IInkDrawingAttributesVtbl;

interface IInkDrawingAttributes {
    CONST_VTBL IInkDrawingAttributesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkDrawingAttributes_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkDrawingAttributes_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkDrawingAttributes_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkDrawingAttributes_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkDrawingAttributes_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkDrawingAttributes_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkDrawingAttributes_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkDrawingAttributes methods ***/
#define IInkDrawingAttributes_get_Color(This,CurrentColor) (This)->lpVtbl->get_Color(This,CurrentColor)
#define IInkDrawingAttributes_put_Color(This,NewColor) (This)->lpVtbl->put_Color(This,NewColor)
#define IInkDrawingAttributes_get_Width(This,CurrentWidth) (This)->lpVtbl->get_Width(This,CurrentWidth)
#define IInkDrawingAttributes_put_Width(This,NewWidth) (This)->lpVtbl->put_Width(This,NewWidth)
#define IInkDrawingAttributes_get_Height(This,CurrentHeight) (This)->lpVtbl->get_Height(This,CurrentHeight)
#define IInkDrawingAttributes_put_Height(This,NewHeight) (This)->lpVtbl->put_Height(This,NewHeight)
#define IInkDrawingAttributes_get_FitToCurve(This,Flag) (This)->lpVtbl->get_FitToCurve(This,Flag)
#define IInkDrawingAttributes_put_FitToCurve(This,Flag) (This)->lpVtbl->put_FitToCurve(This,Flag)
#define IInkDrawingAttributes_get_IgnorePressure(This,Flag) (This)->lpVtbl->get_IgnorePressure(This,Flag)
#define IInkDrawingAttributes_put_IgnorePressure(This,Flag) (This)->lpVtbl->put_IgnorePressure(This,Flag)
#define IInkDrawingAttributes_get_AntiAliased(This,Flag) (This)->lpVtbl->get_AntiAliased(This,Flag)
#define IInkDrawingAttributes_put_AntiAliased(This,Flag) (This)->lpVtbl->put_AntiAliased(This,Flag)
#define IInkDrawingAttributes_get_Transparency(This,CurrentTransparency) (This)->lpVtbl->get_Transparency(This,CurrentTransparency)
#define IInkDrawingAttributes_put_Transparency(This,NewTransparency) (This)->lpVtbl->put_Transparency(This,NewTransparency)
#define IInkDrawingAttributes_get_RasterOperation(This,CurrentRasterOperation) (This)->lpVtbl->get_RasterOperation(This,CurrentRasterOperation)
#define IInkDrawingAttributes_put_RasterOperation(This,NewRasterOperation) (This)->lpVtbl->put_RasterOperation(This,NewRasterOperation)
#define IInkDrawingAttributes_get_PenTip(This,CurrentPenTip) (This)->lpVtbl->get_PenTip(This,CurrentPenTip)
#define IInkDrawingAttributes_put_PenTip(This,NewPenTip) (This)->lpVtbl->put_PenTip(This,NewPenTip)
#define IInkDrawingAttributes_get_ExtendedProperties(This,Properties) (This)->lpVtbl->get_ExtendedProperties(This,Properties)
#define IInkDrawingAttributes_Clone(This,DrawingAttributes) (This)->lpVtbl->Clone(This,DrawingAttributes)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkDrawingAttributes_QueryInterface(IInkDrawingAttributes* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkDrawingAttributes_AddRef(IInkDrawingAttributes* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkDrawingAttributes_Release(IInkDrawingAttributes* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkDrawingAttributes_GetTypeInfoCount(IInkDrawingAttributes* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkDrawingAttributes_GetTypeInfo(IInkDrawingAttributes* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkDrawingAttributes_GetIDsOfNames(IInkDrawingAttributes* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkDrawingAttributes_Invoke(IInkDrawingAttributes* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkDrawingAttributes methods ***/
static inline HRESULT IInkDrawingAttributes_get_Color(IInkDrawingAttributes* This,LONG *CurrentColor) {
    return This->lpVtbl->get_Color(This,CurrentColor);
}
static inline HRESULT IInkDrawingAttributes_put_Color(IInkDrawingAttributes* This,LONG NewColor) {
    return This->lpVtbl->put_Color(This,NewColor);
}
static inline HRESULT IInkDrawingAttributes_get_Width(IInkDrawingAttributes* This,float *CurrentWidth) {
    return This->lpVtbl->get_Width(This,CurrentWidth);
}
static inline HRESULT IInkDrawingAttributes_put_Width(IInkDrawingAttributes* This,float NewWidth) {
    return This->lpVtbl->put_Width(This,NewWidth);
}
static inline HRESULT IInkDrawingAttributes_get_Height(IInkDrawingAttributes* This,float *CurrentHeight) {
    return This->lpVtbl->get_Height(This,CurrentHeight);
}
static inline HRESULT IInkDrawingAttributes_put_Height(IInkDrawingAttributes* This,float NewHeight) {
    return This->lpVtbl->put_Height(This,NewHeight);
}
static inline HRESULT IInkDrawingAttributes_get_FitToCurve(IInkDrawingAttributes* This,VARIANT_BOOL *Flag) {
    return This->lpVtbl->get_FitToCurve(This,Flag);
}
static inline HRESULT IInkDrawingAttributes_put_FitToCurve(IInkDrawingAttributes* This,VARIANT_BOOL Flag) {
    return This->lpVtbl->put_FitToCurve(This,Flag);
}
static inline HRESULT IInkDrawingAttributes_get_IgnorePressure(IInkDrawingAttributes* This,VARIANT_BOOL *Flag) {
    return This->lpVtbl->get_IgnorePressure(This,Flag);
}
static inline HRESULT IInkDrawingAttributes_put_IgnorePressure(IInkDrawingAttributes* This,VARIANT_BOOL Flag) {
    return This->lpVtbl->put_IgnorePressure(This,Flag);
}
static inline HRESULT IInkDrawingAttributes_get_AntiAliased(IInkDrawingAttributes* This,VARIANT_BOOL *Flag) {
    return This->lpVtbl->get_AntiAliased(This,Flag);
}
static inline HRESULT IInkDrawingAttributes_put_AntiAliased(IInkDrawingAttributes* This,VARIANT_BOOL Flag) {
    return This->lpVtbl->put_AntiAliased(This,Flag);
}
static inline HRESULT IInkDrawingAttributes_get_Transparency(IInkDrawingAttributes* This,LONG *CurrentTransparency) {
    return This->lpVtbl->get_Transparency(This,CurrentTransparency);
}
static inline HRESULT IInkDrawingAttributes_put_Transparency(IInkDrawingAttributes* This,LONG NewTransparency) {
    return This->lpVtbl->put_Transparency(This,NewTransparency);
}
static inline HRESULT IInkDrawingAttributes_get_RasterOperation(IInkDrawingAttributes* This,InkRasterOperation *CurrentRasterOperation) {
    return This->lpVtbl->get_RasterOperation(This,CurrentRasterOperation);
}
static inline HRESULT IInkDrawingAttributes_put_RasterOperation(IInkDrawingAttributes* This,InkRasterOperation NewRasterOperation) {
    return This->lpVtbl->put_RasterOperation(This,NewRasterOperation);
}
static inline HRESULT IInkDrawingAttributes_get_PenTip(IInkDrawingAttributes* This,InkPenTip *CurrentPenTip) {
    return This->lpVtbl->get_PenTip(This,CurrentPenTip);
}
static inline HRESULT IInkDrawingAttributes_put_PenTip(IInkDrawingAttributes* This,InkPenTip NewPenTip) {
    return This->lpVtbl->put_PenTip(This,NewPenTip);
}
static inline HRESULT IInkDrawingAttributes_get_ExtendedProperties(IInkDrawingAttributes* This,IInkExtendedProperties **Properties) {
    return This->lpVtbl->get_ExtendedProperties(This,Properties);
}
static inline HRESULT IInkDrawingAttributes_Clone(IInkDrawingAttributes* This,IInkDrawingAttributes **DrawingAttributes) {
    return This->lpVtbl->Clone(This,DrawingAttributes);
}
#endif
#endif

#endif


#endif  /* __IInkDrawingAttributes_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkRectangle interface
 */
#ifndef __IInkRectangle_INTERFACE_DEFINED__
#define __IInkRectangle_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkRectangle, 0x9794ff82, 0x6071, 0x4717, 0x8a,0x8b, 0x6a,0xc7,0xc6,0x4a,0x68,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9794ff82-6071-4717-8a8b-6ac7c64a686e")
IInkRectangle : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Top(
        LONG *Units) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Top(
        LONG Units) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Left(
        LONG *Units) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Left(
        LONG Units) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Bottom(
        LONG *Units) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Bottom(
        LONG Units) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Right(
        LONG *Units) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Right(
        LONG Units) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Data(
        RECT *Rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Data(
        RECT Rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRectangle(
        LONG *Top,
        LONG *Left,
        LONG *Bottom,
        LONG *Right) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRectangle(
        LONG Top,
        LONG Left,
        LONG Bottom,
        LONG Right) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkRectangle, 0x9794ff82, 0x6071, 0x4717, 0x8a,0x8b, 0x6a,0xc7,0xc6,0x4a,0x68,0x6e)
#endif
#else
typedef struct IInkRectangleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkRectangle *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkRectangle *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkRectangle *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkRectangle *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkRectangle *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkRectangle *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkRectangle *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkRectangle methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Top)(
        IInkRectangle *This,
        LONG *Units);

    HRESULT (STDMETHODCALLTYPE *put_Top)(
        IInkRectangle *This,
        LONG Units);

    HRESULT (STDMETHODCALLTYPE *get_Left)(
        IInkRectangle *This,
        LONG *Units);

    HRESULT (STDMETHODCALLTYPE *put_Left)(
        IInkRectangle *This,
        LONG Units);

    HRESULT (STDMETHODCALLTYPE *get_Bottom)(
        IInkRectangle *This,
        LONG *Units);

    HRESULT (STDMETHODCALLTYPE *put_Bottom)(
        IInkRectangle *This,
        LONG Units);

    HRESULT (STDMETHODCALLTYPE *get_Right)(
        IInkRectangle *This,
        LONG *Units);

    HRESULT (STDMETHODCALLTYPE *put_Right)(
        IInkRectangle *This,
        LONG Units);

    HRESULT (STDMETHODCALLTYPE *get_Data)(
        IInkRectangle *This,
        RECT *Rect);

    HRESULT (STDMETHODCALLTYPE *put_Data)(
        IInkRectangle *This,
        RECT Rect);

    HRESULT (STDMETHODCALLTYPE *GetRectangle)(
        IInkRectangle *This,
        LONG *Top,
        LONG *Left,
        LONG *Bottom,
        LONG *Right);

    HRESULT (STDMETHODCALLTYPE *SetRectangle)(
        IInkRectangle *This,
        LONG Top,
        LONG Left,
        LONG Bottom,
        LONG Right);

    END_INTERFACE
} IInkRectangleVtbl;

interface IInkRectangle {
    CONST_VTBL IInkRectangleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkRectangle_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkRectangle_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkRectangle_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkRectangle_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkRectangle_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkRectangle_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkRectangle_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkRectangle methods ***/
#define IInkRectangle_get_Top(This,Units) (This)->lpVtbl->get_Top(This,Units)
#define IInkRectangle_put_Top(This,Units) (This)->lpVtbl->put_Top(This,Units)
#define IInkRectangle_get_Left(This,Units) (This)->lpVtbl->get_Left(This,Units)
#define IInkRectangle_put_Left(This,Units) (This)->lpVtbl->put_Left(This,Units)
#define IInkRectangle_get_Bottom(This,Units) (This)->lpVtbl->get_Bottom(This,Units)
#define IInkRectangle_put_Bottom(This,Units) (This)->lpVtbl->put_Bottom(This,Units)
#define IInkRectangle_get_Right(This,Units) (This)->lpVtbl->get_Right(This,Units)
#define IInkRectangle_put_Right(This,Units) (This)->lpVtbl->put_Right(This,Units)
#define IInkRectangle_get_Data(This,Rect) (This)->lpVtbl->get_Data(This,Rect)
#define IInkRectangle_put_Data(This,Rect) (This)->lpVtbl->put_Data(This,Rect)
#define IInkRectangle_GetRectangle(This,Top,Left,Bottom,Right) (This)->lpVtbl->GetRectangle(This,Top,Left,Bottom,Right)
#define IInkRectangle_SetRectangle(This,Top,Left,Bottom,Right) (This)->lpVtbl->SetRectangle(This,Top,Left,Bottom,Right)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkRectangle_QueryInterface(IInkRectangle* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkRectangle_AddRef(IInkRectangle* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkRectangle_Release(IInkRectangle* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkRectangle_GetTypeInfoCount(IInkRectangle* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkRectangle_GetTypeInfo(IInkRectangle* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkRectangle_GetIDsOfNames(IInkRectangle* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkRectangle_Invoke(IInkRectangle* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkRectangle methods ***/
static inline HRESULT IInkRectangle_get_Top(IInkRectangle* This,LONG *Units) {
    return This->lpVtbl->get_Top(This,Units);
}
static inline HRESULT IInkRectangle_put_Top(IInkRectangle* This,LONG Units) {
    return This->lpVtbl->put_Top(This,Units);
}
static inline HRESULT IInkRectangle_get_Left(IInkRectangle* This,LONG *Units) {
    return This->lpVtbl->get_Left(This,Units);
}
static inline HRESULT IInkRectangle_put_Left(IInkRectangle* This,LONG Units) {
    return This->lpVtbl->put_Left(This,Units);
}
static inline HRESULT IInkRectangle_get_Bottom(IInkRectangle* This,LONG *Units) {
    return This->lpVtbl->get_Bottom(This,Units);
}
static inline HRESULT IInkRectangle_put_Bottom(IInkRectangle* This,LONG Units) {
    return This->lpVtbl->put_Bottom(This,Units);
}
static inline HRESULT IInkRectangle_get_Right(IInkRectangle* This,LONG *Units) {
    return This->lpVtbl->get_Right(This,Units);
}
static inline HRESULT IInkRectangle_put_Right(IInkRectangle* This,LONG Units) {
    return This->lpVtbl->put_Right(This,Units);
}
static inline HRESULT IInkRectangle_get_Data(IInkRectangle* This,RECT *Rect) {
    return This->lpVtbl->get_Data(This,Rect);
}
static inline HRESULT IInkRectangle_put_Data(IInkRectangle* This,RECT Rect) {
    return This->lpVtbl->put_Data(This,Rect);
}
static inline HRESULT IInkRectangle_GetRectangle(IInkRectangle* This,LONG *Top,LONG *Left,LONG *Bottom,LONG *Right) {
    return This->lpVtbl->GetRectangle(This,Top,Left,Bottom,Right);
}
static inline HRESULT IInkRectangle_SetRectangle(IInkRectangle* This,LONG Top,LONG Left,LONG Bottom,LONG Right) {
    return This->lpVtbl->SetRectangle(This,Top,Left,Bottom,Right);
}
#endif
#endif

#endif


#endif  /* __IInkRectangle_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkTablet interface
 */
#ifndef __IInkTablet_INTERFACE_DEFINED__
#define __IInkTablet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkTablet, 0x2de25eaa, 0x6ef8, 0x42d5, 0xae,0xe9, 0x18,0x5b,0xc8,0x1b,0x91,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2de25eaa-6ef8-42d5-aee9-185bc81b912d")
IInkTablet : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *Name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PlugAndPlayId(
        BSTR *Id) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MaximumInputRectangle(
        IInkRectangle **Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HardwareCapabilities(
        TabletHardwareCapabilities *Capabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPacketPropertySupported(
        BSTR packetPropertyName,
        VARIANT_BOOL *Supported) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyMetrics(
        BSTR propertyName,
        LONG *Minimum,
        LONG *Maximum,
        TabletPropertyMetricUnit *Units,
        float *Resolution) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkTablet, 0x2de25eaa, 0x6ef8, 0x42d5, 0xae,0xe9, 0x18,0x5b,0xc8,0x1b,0x91,0x2d)
#endif
#else
typedef struct IInkTabletVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkTablet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkTablet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkTablet *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkTablet *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkTablet *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkTablet *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkTablet *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkTablet methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IInkTablet *This,
        BSTR *Name);

    HRESULT (STDMETHODCALLTYPE *get_PlugAndPlayId)(
        IInkTablet *This,
        BSTR *Id);

    HRESULT (STDMETHODCALLTYPE *get_MaximumInputRectangle)(
        IInkTablet *This,
        IInkRectangle **Rectangle);

    HRESULT (STDMETHODCALLTYPE *get_HardwareCapabilities)(
        IInkTablet *This,
        TabletHardwareCapabilities *Capabilities);

    HRESULT (STDMETHODCALLTYPE *IsPacketPropertySupported)(
        IInkTablet *This,
        BSTR packetPropertyName,
        VARIANT_BOOL *Supported);

    HRESULT (STDMETHODCALLTYPE *GetPropertyMetrics)(
        IInkTablet *This,
        BSTR propertyName,
        LONG *Minimum,
        LONG *Maximum,
        TabletPropertyMetricUnit *Units,
        float *Resolution);

    END_INTERFACE
} IInkTabletVtbl;

interface IInkTablet {
    CONST_VTBL IInkTabletVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkTablet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkTablet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkTablet_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkTablet_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkTablet_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkTablet_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkTablet_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkTablet methods ***/
#define IInkTablet_get_Name(This,Name) (This)->lpVtbl->get_Name(This,Name)
#define IInkTablet_get_PlugAndPlayId(This,Id) (This)->lpVtbl->get_PlugAndPlayId(This,Id)
#define IInkTablet_get_MaximumInputRectangle(This,Rectangle) (This)->lpVtbl->get_MaximumInputRectangle(This,Rectangle)
#define IInkTablet_get_HardwareCapabilities(This,Capabilities) (This)->lpVtbl->get_HardwareCapabilities(This,Capabilities)
#define IInkTablet_IsPacketPropertySupported(This,packetPropertyName,Supported) (This)->lpVtbl->IsPacketPropertySupported(This,packetPropertyName,Supported)
#define IInkTablet_GetPropertyMetrics(This,propertyName,Minimum,Maximum,Units,Resolution) (This)->lpVtbl->GetPropertyMetrics(This,propertyName,Minimum,Maximum,Units,Resolution)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkTablet_QueryInterface(IInkTablet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkTablet_AddRef(IInkTablet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkTablet_Release(IInkTablet* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkTablet_GetTypeInfoCount(IInkTablet* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkTablet_GetTypeInfo(IInkTablet* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkTablet_GetIDsOfNames(IInkTablet* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkTablet_Invoke(IInkTablet* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkTablet methods ***/
static inline HRESULT IInkTablet_get_Name(IInkTablet* This,BSTR *Name) {
    return This->lpVtbl->get_Name(This,Name);
}
static inline HRESULT IInkTablet_get_PlugAndPlayId(IInkTablet* This,BSTR *Id) {
    return This->lpVtbl->get_PlugAndPlayId(This,Id);
}
static inline HRESULT IInkTablet_get_MaximumInputRectangle(IInkTablet* This,IInkRectangle **Rectangle) {
    return This->lpVtbl->get_MaximumInputRectangle(This,Rectangle);
}
static inline HRESULT IInkTablet_get_HardwareCapabilities(IInkTablet* This,TabletHardwareCapabilities *Capabilities) {
    return This->lpVtbl->get_HardwareCapabilities(This,Capabilities);
}
static inline HRESULT IInkTablet_IsPacketPropertySupported(IInkTablet* This,BSTR packetPropertyName,VARIANT_BOOL *Supported) {
    return This->lpVtbl->IsPacketPropertySupported(This,packetPropertyName,Supported);
}
static inline HRESULT IInkTablet_GetPropertyMetrics(IInkTablet* This,BSTR propertyName,LONG *Minimum,LONG *Maximum,TabletPropertyMetricUnit *Units,float *Resolution) {
    return This->lpVtbl->GetPropertyMetrics(This,propertyName,Minimum,Maximum,Units,Resolution);
}
#endif
#endif

#endif


#endif  /* __IInkTablet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkCursorButton interface
 */
#ifndef __IInkCursorButton_INTERFACE_DEFINED__
#define __IInkCursorButton_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkCursorButton, 0x85ef9417, 0x1d59, 0x49b2, 0xa1,0x3c, 0x70,0x2c,0x85,0x43,0x08,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("85ef9417-1d59-49b2-a13c-702c85430894")
IInkCursorButton : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *Name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        BSTR *Id) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_State(
        InkCursorButtonState *CurrentState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkCursorButton, 0x85ef9417, 0x1d59, 0x49b2, 0xa1,0x3c, 0x70,0x2c,0x85,0x43,0x08,0x94)
#endif
#else
typedef struct IInkCursorButtonVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkCursorButton *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkCursorButton *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkCursorButton *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkCursorButton *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkCursorButton *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkCursorButton *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkCursorButton *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkCursorButton methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IInkCursorButton *This,
        BSTR *Name);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IInkCursorButton *This,
        BSTR *Id);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        IInkCursorButton *This,
        InkCursorButtonState *CurrentState);

    END_INTERFACE
} IInkCursorButtonVtbl;

interface IInkCursorButton {
    CONST_VTBL IInkCursorButtonVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkCursorButton_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkCursorButton_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkCursorButton_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkCursorButton_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkCursorButton_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkCursorButton_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkCursorButton_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkCursorButton methods ***/
#define IInkCursorButton_get_Name(This,Name) (This)->lpVtbl->get_Name(This,Name)
#define IInkCursorButton_get_Id(This,Id) (This)->lpVtbl->get_Id(This,Id)
#define IInkCursorButton_get_State(This,CurrentState) (This)->lpVtbl->get_State(This,CurrentState)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkCursorButton_QueryInterface(IInkCursorButton* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkCursorButton_AddRef(IInkCursorButton* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkCursorButton_Release(IInkCursorButton* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkCursorButton_GetTypeInfoCount(IInkCursorButton* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkCursorButton_GetTypeInfo(IInkCursorButton* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkCursorButton_GetIDsOfNames(IInkCursorButton* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkCursorButton_Invoke(IInkCursorButton* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkCursorButton methods ***/
static inline HRESULT IInkCursorButton_get_Name(IInkCursorButton* This,BSTR *Name) {
    return This->lpVtbl->get_Name(This,Name);
}
static inline HRESULT IInkCursorButton_get_Id(IInkCursorButton* This,BSTR *Id) {
    return This->lpVtbl->get_Id(This,Id);
}
static inline HRESULT IInkCursorButton_get_State(IInkCursorButton* This,InkCursorButtonState *CurrentState) {
    return This->lpVtbl->get_State(This,CurrentState);
}
#endif
#endif

#endif


#endif  /* __IInkCursorButton_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkCursorButtons interface
 */
#ifndef __IInkCursorButtons_INTERFACE_DEFINED__
#define __IInkCursorButtons_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkCursorButtons, 0x3671cc40, 0xb624, 0x4671, 0x9f,0xa0, 0xdb,0x11,0x9d,0x95,0x2d,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3671cc40-b624-4671-9fa0-db119d952d54")
IInkCursorButtons : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *Count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **_NewEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        VARIANT Identifier,
        IInkCursorButton **Button) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkCursorButtons, 0x3671cc40, 0xb624, 0x4671, 0x9f,0xa0, 0xdb,0x11,0x9d,0x95,0x2d,0x54)
#endif
#else
typedef struct IInkCursorButtonsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkCursorButtons *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkCursorButtons *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkCursorButtons *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkCursorButtons *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkCursorButtons *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkCursorButtons *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkCursorButtons *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkCursorButtons methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IInkCursorButtons *This,
        LONG *Count);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IInkCursorButtons *This,
        IUnknown **_NewEnum);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IInkCursorButtons *This,
        VARIANT Identifier,
        IInkCursorButton **Button);

    END_INTERFACE
} IInkCursorButtonsVtbl;

interface IInkCursorButtons {
    CONST_VTBL IInkCursorButtonsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkCursorButtons_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkCursorButtons_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkCursorButtons_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkCursorButtons_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkCursorButtons_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkCursorButtons_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkCursorButtons_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkCursorButtons methods ***/
#define IInkCursorButtons_get_Count(This,Count) (This)->lpVtbl->get_Count(This,Count)
#define IInkCursorButtons_get__NewEnum(This,_NewEnum) (This)->lpVtbl->get__NewEnum(This,_NewEnum)
#define IInkCursorButtons_Item(This,Identifier,Button) (This)->lpVtbl->Item(This,Identifier,Button)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkCursorButtons_QueryInterface(IInkCursorButtons* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkCursorButtons_AddRef(IInkCursorButtons* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkCursorButtons_Release(IInkCursorButtons* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkCursorButtons_GetTypeInfoCount(IInkCursorButtons* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkCursorButtons_GetTypeInfo(IInkCursorButtons* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkCursorButtons_GetIDsOfNames(IInkCursorButtons* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkCursorButtons_Invoke(IInkCursorButtons* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkCursorButtons methods ***/
static inline HRESULT IInkCursorButtons_get_Count(IInkCursorButtons* This,LONG *Count) {
    return This->lpVtbl->get_Count(This,Count);
}
static inline HRESULT IInkCursorButtons_get__NewEnum(IInkCursorButtons* This,IUnknown **_NewEnum) {
    return This->lpVtbl->get__NewEnum(This,_NewEnum);
}
static inline HRESULT IInkCursorButtons_Item(IInkCursorButtons* This,VARIANT Identifier,IInkCursorButton **Button) {
    return This->lpVtbl->Item(This,Identifier,Button);
}
#endif
#endif

#endif


#endif  /* __IInkCursorButtons_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkCursor interface
 */
#ifndef __IInkCursor_INTERFACE_DEFINED__
#define __IInkCursor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkCursor, 0xad30c630, 0x40c5, 0x4350, 0x84,0x05, 0x9c,0x71,0x01,0x2f,0xc5,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ad30c630-40c5-4350-8405-9c71012fc558")
IInkCursor : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *Name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        LONG *Id) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Inverted(
        VARIANT_BOOL *Status) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DrawingAttributes(
        IInkDrawingAttributes **Attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE putref_DrawingAttributes(
        IInkDrawingAttributes *Attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Tablet(
        IInkTablet **Tablet) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Buttons(
        IInkCursorButtons **Buttons) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkCursor, 0xad30c630, 0x40c5, 0x4350, 0x84,0x05, 0x9c,0x71,0x01,0x2f,0xc5,0x58)
#endif
#else
typedef struct IInkCursorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkCursor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkCursor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkCursor *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkCursor *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkCursor *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkCursor *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkCursor *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkCursor methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IInkCursor *This,
        BSTR *Name);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IInkCursor *This,
        LONG *Id);

    HRESULT (STDMETHODCALLTYPE *get_Inverted)(
        IInkCursor *This,
        VARIANT_BOOL *Status);

    HRESULT (STDMETHODCALLTYPE *get_DrawingAttributes)(
        IInkCursor *This,
        IInkDrawingAttributes **Attributes);

    HRESULT (STDMETHODCALLTYPE *putref_DrawingAttributes)(
        IInkCursor *This,
        IInkDrawingAttributes *Attributes);

    HRESULT (STDMETHODCALLTYPE *get_Tablet)(
        IInkCursor *This,
        IInkTablet **Tablet);

    HRESULT (STDMETHODCALLTYPE *get_Buttons)(
        IInkCursor *This,
        IInkCursorButtons **Buttons);

    END_INTERFACE
} IInkCursorVtbl;

interface IInkCursor {
    CONST_VTBL IInkCursorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkCursor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkCursor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkCursor_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkCursor_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkCursor_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkCursor_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkCursor_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkCursor methods ***/
#define IInkCursor_get_Name(This,Name) (This)->lpVtbl->get_Name(This,Name)
#define IInkCursor_get_Id(This,Id) (This)->lpVtbl->get_Id(This,Id)
#define IInkCursor_get_Inverted(This,Status) (This)->lpVtbl->get_Inverted(This,Status)
#define IInkCursor_get_DrawingAttributes(This,Attributes) (This)->lpVtbl->get_DrawingAttributes(This,Attributes)
#define IInkCursor_putref_DrawingAttributes(This,Attributes) (This)->lpVtbl->putref_DrawingAttributes(This,Attributes)
#define IInkCursor_get_Tablet(This,Tablet) (This)->lpVtbl->get_Tablet(This,Tablet)
#define IInkCursor_get_Buttons(This,Buttons) (This)->lpVtbl->get_Buttons(This,Buttons)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkCursor_QueryInterface(IInkCursor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkCursor_AddRef(IInkCursor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkCursor_Release(IInkCursor* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkCursor_GetTypeInfoCount(IInkCursor* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkCursor_GetTypeInfo(IInkCursor* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkCursor_GetIDsOfNames(IInkCursor* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkCursor_Invoke(IInkCursor* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkCursor methods ***/
static inline HRESULT IInkCursor_get_Name(IInkCursor* This,BSTR *Name) {
    return This->lpVtbl->get_Name(This,Name);
}
static inline HRESULT IInkCursor_get_Id(IInkCursor* This,LONG *Id) {
    return This->lpVtbl->get_Id(This,Id);
}
static inline HRESULT IInkCursor_get_Inverted(IInkCursor* This,VARIANT_BOOL *Status) {
    return This->lpVtbl->get_Inverted(This,Status);
}
static inline HRESULT IInkCursor_get_DrawingAttributes(IInkCursor* This,IInkDrawingAttributes **Attributes) {
    return This->lpVtbl->get_DrawingAttributes(This,Attributes);
}
static inline HRESULT IInkCursor_putref_DrawingAttributes(IInkCursor* This,IInkDrawingAttributes *Attributes) {
    return This->lpVtbl->putref_DrawingAttributes(This,Attributes);
}
static inline HRESULT IInkCursor_get_Tablet(IInkCursor* This,IInkTablet **Tablet) {
    return This->lpVtbl->get_Tablet(This,Tablet);
}
static inline HRESULT IInkCursor_get_Buttons(IInkCursor* This,IInkCursorButtons **Buttons) {
    return This->lpVtbl->get_Buttons(This,Buttons);
}
#endif
#endif

#endif


#endif  /* __IInkCursor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkTransform interface
 */
#ifndef __IInkTransform_INTERFACE_DEFINED__
#define __IInkTransform_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkTransform, 0x615f1d43, 0x8703, 0x4565, 0x88,0xe2, 0x82,0x01,0xd2,0xec,0xd7,0xb7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("615f1d43-8703-4565-88e2-8201d2ecd7b7")
IInkTransform : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Translate(
        float HorizontalComponent,
        float VerticalComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE Rotate(
        float Degrees,
        float x = 0,
        float y = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reflect(
        VARIANT_BOOL Horizontally,
        VARIANT_BOOL Vertically) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shear(
        float HorizontalComponent,
        float VerticalComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScaleTransform(
        float HorizontalMultiplier,
        float VerticalMultiplier) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransform(
        float *eM11,
        float *eM12,
        float *eM21,
        float *eM22,
        float *eDx,
        float *eDy) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransform(
        float eM11,
        float eM12,
        float eM21,
        float eM22,
        float eDx,
        float eDy) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_eM11(
        float *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_eM11(
        float Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_eM12(
        float *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_eM12(
        float Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_eM21(
        float *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_eM21(
        float Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_eM22(
        float *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_eM22(
        float Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_eDx(
        float *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_eDx(
        float Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_eDy(
        float *Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_eDy(
        float Value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Data(
        XFORM *XForm) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Data(
        XFORM XForm) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkTransform, 0x615f1d43, 0x8703, 0x4565, 0x88,0xe2, 0x82,0x01,0xd2,0xec,0xd7,0xb7)
#endif
#else
typedef struct IInkTransformVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkTransform *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkTransform *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkTransform *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkTransform *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkTransform *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkTransform *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkTransform *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkTransform methods ***/
    HRESULT (STDMETHODCALLTYPE *Reset)(
        IInkTransform *This);

    HRESULT (STDMETHODCALLTYPE *Translate)(
        IInkTransform *This,
        float HorizontalComponent,
        float VerticalComponent);

    HRESULT (STDMETHODCALLTYPE *Rotate)(
        IInkTransform *This,
        float Degrees,
        float x,
        float y);

    HRESULT (STDMETHODCALLTYPE *Reflect)(
        IInkTransform *This,
        VARIANT_BOOL Horizontally,
        VARIANT_BOOL Vertically);

    HRESULT (STDMETHODCALLTYPE *Shear)(
        IInkTransform *This,
        float HorizontalComponent,
        float VerticalComponent);

    HRESULT (STDMETHODCALLTYPE *ScaleTransform)(
        IInkTransform *This,
        float HorizontalMultiplier,
        float VerticalMultiplier);

    HRESULT (STDMETHODCALLTYPE *GetTransform)(
        IInkTransform *This,
        float *eM11,
        float *eM12,
        float *eM21,
        float *eM22,
        float *eDx,
        float *eDy);

    HRESULT (STDMETHODCALLTYPE *SetTransform)(
        IInkTransform *This,
        float eM11,
        float eM12,
        float eM21,
        float eM22,
        float eDx,
        float eDy);

    HRESULT (STDMETHODCALLTYPE *get_eM11)(
        IInkTransform *This,
        float *Value);

    HRESULT (STDMETHODCALLTYPE *put_eM11)(
        IInkTransform *This,
        float Value);

    HRESULT (STDMETHODCALLTYPE *get_eM12)(
        IInkTransform *This,
        float *Value);

    HRESULT (STDMETHODCALLTYPE *put_eM12)(
        IInkTransform *This,
        float Value);

    HRESULT (STDMETHODCALLTYPE *get_eM21)(
        IInkTransform *This,
        float *Value);

    HRESULT (STDMETHODCALLTYPE *put_eM21)(
        IInkTransform *This,
        float Value);

    HRESULT (STDMETHODCALLTYPE *get_eM22)(
        IInkTransform *This,
        float *Value);

    HRESULT (STDMETHODCALLTYPE *put_eM22)(
        IInkTransform *This,
        float Value);

    HRESULT (STDMETHODCALLTYPE *get_eDx)(
        IInkTransform *This,
        float *Value);

    HRESULT (STDMETHODCALLTYPE *put_eDx)(
        IInkTransform *This,
        float Value);

    HRESULT (STDMETHODCALLTYPE *get_eDy)(
        IInkTransform *This,
        float *Value);

    HRESULT (STDMETHODCALLTYPE *put_eDy)(
        IInkTransform *This,
        float Value);

    HRESULT (STDMETHODCALLTYPE *get_Data)(
        IInkTransform *This,
        XFORM *XForm);

    HRESULT (STDMETHODCALLTYPE *put_Data)(
        IInkTransform *This,
        XFORM XForm);

    END_INTERFACE
} IInkTransformVtbl;

interface IInkTransform {
    CONST_VTBL IInkTransformVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkTransform_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkTransform_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkTransform_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkTransform_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkTransform_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkTransform_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkTransform_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkTransform methods ***/
#define IInkTransform_Reset(This) (This)->lpVtbl->Reset(This)
#define IInkTransform_Translate(This,HorizontalComponent,VerticalComponent) (This)->lpVtbl->Translate(This,HorizontalComponent,VerticalComponent)
#define IInkTransform_Rotate(This,Degrees,x,y) (This)->lpVtbl->Rotate(This,Degrees,x,y)
#define IInkTransform_Reflect(This,Horizontally,Vertically) (This)->lpVtbl->Reflect(This,Horizontally,Vertically)
#define IInkTransform_Shear(This,HorizontalComponent,VerticalComponent) (This)->lpVtbl->Shear(This,HorizontalComponent,VerticalComponent)
#define IInkTransform_ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier) (This)->lpVtbl->ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier)
#define IInkTransform_GetTransform(This,eM11,eM12,eM21,eM22,eDx,eDy) (This)->lpVtbl->GetTransform(This,eM11,eM12,eM21,eM22,eDx,eDy)
#define IInkTransform_SetTransform(This,eM11,eM12,eM21,eM22,eDx,eDy) (This)->lpVtbl->SetTransform(This,eM11,eM12,eM21,eM22,eDx,eDy)
#define IInkTransform_get_eM11(This,Value) (This)->lpVtbl->get_eM11(This,Value)
#define IInkTransform_put_eM11(This,Value) (This)->lpVtbl->put_eM11(This,Value)
#define IInkTransform_get_eM12(This,Value) (This)->lpVtbl->get_eM12(This,Value)
#define IInkTransform_put_eM12(This,Value) (This)->lpVtbl->put_eM12(This,Value)
#define IInkTransform_get_eM21(This,Value) (This)->lpVtbl->get_eM21(This,Value)
#define IInkTransform_put_eM21(This,Value) (This)->lpVtbl->put_eM21(This,Value)
#define IInkTransform_get_eM22(This,Value) (This)->lpVtbl->get_eM22(This,Value)
#define IInkTransform_put_eM22(This,Value) (This)->lpVtbl->put_eM22(This,Value)
#define IInkTransform_get_eDx(This,Value) (This)->lpVtbl->get_eDx(This,Value)
#define IInkTransform_put_eDx(This,Value) (This)->lpVtbl->put_eDx(This,Value)
#define IInkTransform_get_eDy(This,Value) (This)->lpVtbl->get_eDy(This,Value)
#define IInkTransform_put_eDy(This,Value) (This)->lpVtbl->put_eDy(This,Value)
#define IInkTransform_get_Data(This,XForm) (This)->lpVtbl->get_Data(This,XForm)
#define IInkTransform_put_Data(This,XForm) (This)->lpVtbl->put_Data(This,XForm)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkTransform_QueryInterface(IInkTransform* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkTransform_AddRef(IInkTransform* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkTransform_Release(IInkTransform* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkTransform_GetTypeInfoCount(IInkTransform* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkTransform_GetTypeInfo(IInkTransform* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkTransform_GetIDsOfNames(IInkTransform* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkTransform_Invoke(IInkTransform* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkTransform methods ***/
static inline HRESULT IInkTransform_Reset(IInkTransform* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IInkTransform_Translate(IInkTransform* This,float HorizontalComponent,float VerticalComponent) {
    return This->lpVtbl->Translate(This,HorizontalComponent,VerticalComponent);
}
static inline HRESULT IInkTransform_Rotate(IInkTransform* This,float Degrees,float x,float y) {
    return This->lpVtbl->Rotate(This,Degrees,x,y);
}
static inline HRESULT IInkTransform_Reflect(IInkTransform* This,VARIANT_BOOL Horizontally,VARIANT_BOOL Vertically) {
    return This->lpVtbl->Reflect(This,Horizontally,Vertically);
}
static inline HRESULT IInkTransform_Shear(IInkTransform* This,float HorizontalComponent,float VerticalComponent) {
    return This->lpVtbl->Shear(This,HorizontalComponent,VerticalComponent);
}
static inline HRESULT IInkTransform_ScaleTransform(IInkTransform* This,float HorizontalMultiplier,float VerticalMultiplier) {
    return This->lpVtbl->ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier);
}
static inline HRESULT IInkTransform_GetTransform(IInkTransform* This,float *eM11,float *eM12,float *eM21,float *eM22,float *eDx,float *eDy) {
    return This->lpVtbl->GetTransform(This,eM11,eM12,eM21,eM22,eDx,eDy);
}
static inline HRESULT IInkTransform_SetTransform(IInkTransform* This,float eM11,float eM12,float eM21,float eM22,float eDx,float eDy) {
    return This->lpVtbl->SetTransform(This,eM11,eM12,eM21,eM22,eDx,eDy);
}
static inline HRESULT IInkTransform_get_eM11(IInkTransform* This,float *Value) {
    return This->lpVtbl->get_eM11(This,Value);
}
static inline HRESULT IInkTransform_put_eM11(IInkTransform* This,float Value) {
    return This->lpVtbl->put_eM11(This,Value);
}
static inline HRESULT IInkTransform_get_eM12(IInkTransform* This,float *Value) {
    return This->lpVtbl->get_eM12(This,Value);
}
static inline HRESULT IInkTransform_put_eM12(IInkTransform* This,float Value) {
    return This->lpVtbl->put_eM12(This,Value);
}
static inline HRESULT IInkTransform_get_eM21(IInkTransform* This,float *Value) {
    return This->lpVtbl->get_eM21(This,Value);
}
static inline HRESULT IInkTransform_put_eM21(IInkTransform* This,float Value) {
    return This->lpVtbl->put_eM21(This,Value);
}
static inline HRESULT IInkTransform_get_eM22(IInkTransform* This,float *Value) {
    return This->lpVtbl->get_eM22(This,Value);
}
static inline HRESULT IInkTransform_put_eM22(IInkTransform* This,float Value) {
    return This->lpVtbl->put_eM22(This,Value);
}
static inline HRESULT IInkTransform_get_eDx(IInkTransform* This,float *Value) {
    return This->lpVtbl->get_eDx(This,Value);
}
static inline HRESULT IInkTransform_put_eDx(IInkTransform* This,float Value) {
    return This->lpVtbl->put_eDx(This,Value);
}
static inline HRESULT IInkTransform_get_eDy(IInkTransform* This,float *Value) {
    return This->lpVtbl->get_eDy(This,Value);
}
static inline HRESULT IInkTransform_put_eDy(IInkTransform* This,float Value) {
    return This->lpVtbl->put_eDy(This,Value);
}
static inline HRESULT IInkTransform_get_Data(IInkTransform* This,XFORM *XForm) {
    return This->lpVtbl->get_Data(This,XForm);
}
static inline HRESULT IInkTransform_put_Data(IInkTransform* This,XFORM XForm) {
    return This->lpVtbl->put_Data(This,XForm);
}
#endif
#endif

#endif


#endif  /* __IInkTransform_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkRecognitionAlternates interface
 */
#ifndef __IInkRecognitionAlternates_INTERFACE_DEFINED__
#define __IInkRecognitionAlternates_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkRecognitionAlternates, 0x286a167f, 0x9f19, 0x4c61, 0x9d,0x53, 0x4f,0x07,0xbe,0x62,0x2b,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("286a167f-9f19-4c61-9d53-4f07be622b84")
IInkRecognitionAlternates : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *Count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **_NewEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Strokes(
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG Index,
        IInkRecognitionAlternate **InkRecoAlternate) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkRecognitionAlternates, 0x286a167f, 0x9f19, 0x4c61, 0x9d,0x53, 0x4f,0x07,0xbe,0x62,0x2b,0x84)
#endif
#else
typedef struct IInkRecognitionAlternatesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkRecognitionAlternates *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkRecognitionAlternates *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkRecognitionAlternates *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkRecognitionAlternates *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkRecognitionAlternates *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkRecognitionAlternates *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkRecognitionAlternates *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkRecognitionAlternates methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IInkRecognitionAlternates *This,
        LONG *Count);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IInkRecognitionAlternates *This,
        IUnknown **_NewEnum);

    HRESULT (STDMETHODCALLTYPE *get_Strokes)(
        IInkRecognitionAlternates *This,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IInkRecognitionAlternates *This,
        LONG Index,
        IInkRecognitionAlternate **InkRecoAlternate);

    END_INTERFACE
} IInkRecognitionAlternatesVtbl;

interface IInkRecognitionAlternates {
    CONST_VTBL IInkRecognitionAlternatesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkRecognitionAlternates_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkRecognitionAlternates_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkRecognitionAlternates_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkRecognitionAlternates_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkRecognitionAlternates_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkRecognitionAlternates_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkRecognitionAlternates_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkRecognitionAlternates methods ***/
#define IInkRecognitionAlternates_get_Count(This,Count) (This)->lpVtbl->get_Count(This,Count)
#define IInkRecognitionAlternates_get__NewEnum(This,_NewEnum) (This)->lpVtbl->get__NewEnum(This,_NewEnum)
#define IInkRecognitionAlternates_get_Strokes(This,Strokes) (This)->lpVtbl->get_Strokes(This,Strokes)
#define IInkRecognitionAlternates_Item(This,Index,InkRecoAlternate) (This)->lpVtbl->Item(This,Index,InkRecoAlternate)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkRecognitionAlternates_QueryInterface(IInkRecognitionAlternates* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkRecognitionAlternates_AddRef(IInkRecognitionAlternates* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkRecognitionAlternates_Release(IInkRecognitionAlternates* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkRecognitionAlternates_GetTypeInfoCount(IInkRecognitionAlternates* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkRecognitionAlternates_GetTypeInfo(IInkRecognitionAlternates* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkRecognitionAlternates_GetIDsOfNames(IInkRecognitionAlternates* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkRecognitionAlternates_Invoke(IInkRecognitionAlternates* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkRecognitionAlternates methods ***/
static inline HRESULT IInkRecognitionAlternates_get_Count(IInkRecognitionAlternates* This,LONG *Count) {
    return This->lpVtbl->get_Count(This,Count);
}
static inline HRESULT IInkRecognitionAlternates_get__NewEnum(IInkRecognitionAlternates* This,IUnknown **_NewEnum) {
    return This->lpVtbl->get__NewEnum(This,_NewEnum);
}
static inline HRESULT IInkRecognitionAlternates_get_Strokes(IInkRecognitionAlternates* This,IInkStrokes **Strokes) {
    return This->lpVtbl->get_Strokes(This,Strokes);
}
static inline HRESULT IInkRecognitionAlternates_Item(IInkRecognitionAlternates* This,LONG Index,IInkRecognitionAlternate **InkRecoAlternate) {
    return This->lpVtbl->Item(This,Index,InkRecoAlternate);
}
#endif
#endif

#endif


#endif  /* __IInkRecognitionAlternates_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkRecognitionAlternate interface
 */
#ifndef __IInkRecognitionAlternate_INTERFACE_DEFINED__
#define __IInkRecognitionAlternate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkRecognitionAlternate, 0xb7e660ad, 0x77e4, 0x429b, 0xad,0xda, 0x87,0x37,0x80,0xd1,0xfc,0x4a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b7e660ad-77e4-429b-adda-873780d1fc4a")
IInkRecognitionAlternate : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_String(
        BSTR *RecoString) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Confidence(
        InkRecognitionConfidence *Confidence) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Baseline(
        VARIANT *Baseline) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Midline(
        VARIANT *Midline) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Ascender(
        VARIANT *Ascender) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Descender(
        VARIANT *Descender) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LineNumber(
        LONG *LineNumber) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Strokes(
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LineAlternates(
        IInkRecognitionAlternates **LineAlternates) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ConfidenceAlternates(
        IInkRecognitionAlternates **ConfidenceAlternates) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokesFromStrokeRanges(
        IInkStrokes *Strokes,
        IInkStrokes **GetStrokesFromStrokeRanges) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStrokesFromTextRange(
        LONG *selectionStart,
        LONG *selectionLength,
        IInkStrokes **GetStrokesFromTextRange) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTextRangeFromStrokes(
        IInkStrokes *Strokes,
        LONG *selectionStart,
        LONG *selectionLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE AlternatesWithConstantPropertyValues(
        BSTR PropertyType,
        IInkRecognitionAlternates **AlternatesWithConstantPropertyValues) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyValue(
        BSTR PropertyType,
        VARIANT *PropertyValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkRecognitionAlternate, 0xb7e660ad, 0x77e4, 0x429b, 0xad,0xda, 0x87,0x37,0x80,0xd1,0xfc,0x4a)
#endif
#else
typedef struct IInkRecognitionAlternateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkRecognitionAlternate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkRecognitionAlternate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkRecognitionAlternate *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkRecognitionAlternate *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkRecognitionAlternate *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkRecognitionAlternate *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkRecognitionAlternate *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkRecognitionAlternate methods ***/
    HRESULT (STDMETHODCALLTYPE *get_String)(
        IInkRecognitionAlternate *This,
        BSTR *RecoString);

    HRESULT (STDMETHODCALLTYPE *get_Confidence)(
        IInkRecognitionAlternate *This,
        InkRecognitionConfidence *Confidence);

    HRESULT (STDMETHODCALLTYPE *get_Baseline)(
        IInkRecognitionAlternate *This,
        VARIANT *Baseline);

    HRESULT (STDMETHODCALLTYPE *get_Midline)(
        IInkRecognitionAlternate *This,
        VARIANT *Midline);

    HRESULT (STDMETHODCALLTYPE *get_Ascender)(
        IInkRecognitionAlternate *This,
        VARIANT *Ascender);

    HRESULT (STDMETHODCALLTYPE *get_Descender)(
        IInkRecognitionAlternate *This,
        VARIANT *Descender);

    HRESULT (STDMETHODCALLTYPE *get_LineNumber)(
        IInkRecognitionAlternate *This,
        LONG *LineNumber);

    HRESULT (STDMETHODCALLTYPE *get_Strokes)(
        IInkRecognitionAlternate *This,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *get_LineAlternates)(
        IInkRecognitionAlternate *This,
        IInkRecognitionAlternates **LineAlternates);

    HRESULT (STDMETHODCALLTYPE *get_ConfidenceAlternates)(
        IInkRecognitionAlternate *This,
        IInkRecognitionAlternates **ConfidenceAlternates);

    HRESULT (STDMETHODCALLTYPE *GetStrokesFromStrokeRanges)(
        IInkRecognitionAlternate *This,
        IInkStrokes *Strokes,
        IInkStrokes **GetStrokesFromStrokeRanges);

    HRESULT (STDMETHODCALLTYPE *GetStrokesFromTextRange)(
        IInkRecognitionAlternate *This,
        LONG *selectionStart,
        LONG *selectionLength,
        IInkStrokes **GetStrokesFromTextRange);

    HRESULT (STDMETHODCALLTYPE *GetTextRangeFromStrokes)(
        IInkRecognitionAlternate *This,
        IInkStrokes *Strokes,
        LONG *selectionStart,
        LONG *selectionLength);

    HRESULT (STDMETHODCALLTYPE *AlternatesWithConstantPropertyValues)(
        IInkRecognitionAlternate *This,
        BSTR PropertyType,
        IInkRecognitionAlternates **AlternatesWithConstantPropertyValues);

    HRESULT (STDMETHODCALLTYPE *GetPropertyValue)(
        IInkRecognitionAlternate *This,
        BSTR PropertyType,
        VARIANT *PropertyValue);

    END_INTERFACE
} IInkRecognitionAlternateVtbl;

interface IInkRecognitionAlternate {
    CONST_VTBL IInkRecognitionAlternateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkRecognitionAlternate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkRecognitionAlternate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkRecognitionAlternate_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkRecognitionAlternate_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkRecognitionAlternate_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkRecognitionAlternate_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkRecognitionAlternate_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkRecognitionAlternate methods ***/
#define IInkRecognitionAlternate_get_String(This,RecoString) (This)->lpVtbl->get_String(This,RecoString)
#define IInkRecognitionAlternate_get_Confidence(This,Confidence) (This)->lpVtbl->get_Confidence(This,Confidence)
#define IInkRecognitionAlternate_get_Baseline(This,Baseline) (This)->lpVtbl->get_Baseline(This,Baseline)
#define IInkRecognitionAlternate_get_Midline(This,Midline) (This)->lpVtbl->get_Midline(This,Midline)
#define IInkRecognitionAlternate_get_Ascender(This,Ascender) (This)->lpVtbl->get_Ascender(This,Ascender)
#define IInkRecognitionAlternate_get_Descender(This,Descender) (This)->lpVtbl->get_Descender(This,Descender)
#define IInkRecognitionAlternate_get_LineNumber(This,LineNumber) (This)->lpVtbl->get_LineNumber(This,LineNumber)
#define IInkRecognitionAlternate_get_Strokes(This,Strokes) (This)->lpVtbl->get_Strokes(This,Strokes)
#define IInkRecognitionAlternate_get_LineAlternates(This,LineAlternates) (This)->lpVtbl->get_LineAlternates(This,LineAlternates)
#define IInkRecognitionAlternate_get_ConfidenceAlternates(This,ConfidenceAlternates) (This)->lpVtbl->get_ConfidenceAlternates(This,ConfidenceAlternates)
#define IInkRecognitionAlternate_GetStrokesFromStrokeRanges(This,Strokes,GetStrokesFromStrokeRanges) (This)->lpVtbl->GetStrokesFromStrokeRanges(This,Strokes,GetStrokesFromStrokeRanges)
#define IInkRecognitionAlternate_GetStrokesFromTextRange(This,selectionStart,selectionLength,GetStrokesFromTextRange) (This)->lpVtbl->GetStrokesFromTextRange(This,selectionStart,selectionLength,GetStrokesFromTextRange)
#define IInkRecognitionAlternate_GetTextRangeFromStrokes(This,Strokes,selectionStart,selectionLength) (This)->lpVtbl->GetTextRangeFromStrokes(This,Strokes,selectionStart,selectionLength)
#define IInkRecognitionAlternate_AlternatesWithConstantPropertyValues(This,PropertyType,AlternatesWithConstantPropertyValues) (This)->lpVtbl->AlternatesWithConstantPropertyValues(This,PropertyType,AlternatesWithConstantPropertyValues)
#define IInkRecognitionAlternate_GetPropertyValue(This,PropertyType,PropertyValue) (This)->lpVtbl->GetPropertyValue(This,PropertyType,PropertyValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkRecognitionAlternate_QueryInterface(IInkRecognitionAlternate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkRecognitionAlternate_AddRef(IInkRecognitionAlternate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkRecognitionAlternate_Release(IInkRecognitionAlternate* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkRecognitionAlternate_GetTypeInfoCount(IInkRecognitionAlternate* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkRecognitionAlternate_GetTypeInfo(IInkRecognitionAlternate* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkRecognitionAlternate_GetIDsOfNames(IInkRecognitionAlternate* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkRecognitionAlternate_Invoke(IInkRecognitionAlternate* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkRecognitionAlternate methods ***/
static inline HRESULT IInkRecognitionAlternate_get_String(IInkRecognitionAlternate* This,BSTR *RecoString) {
    return This->lpVtbl->get_String(This,RecoString);
}
static inline HRESULT IInkRecognitionAlternate_get_Confidence(IInkRecognitionAlternate* This,InkRecognitionConfidence *Confidence) {
    return This->lpVtbl->get_Confidence(This,Confidence);
}
static inline HRESULT IInkRecognitionAlternate_get_Baseline(IInkRecognitionAlternate* This,VARIANT *Baseline) {
    return This->lpVtbl->get_Baseline(This,Baseline);
}
static inline HRESULT IInkRecognitionAlternate_get_Midline(IInkRecognitionAlternate* This,VARIANT *Midline) {
    return This->lpVtbl->get_Midline(This,Midline);
}
static inline HRESULT IInkRecognitionAlternate_get_Ascender(IInkRecognitionAlternate* This,VARIANT *Ascender) {
    return This->lpVtbl->get_Ascender(This,Ascender);
}
static inline HRESULT IInkRecognitionAlternate_get_Descender(IInkRecognitionAlternate* This,VARIANT *Descender) {
    return This->lpVtbl->get_Descender(This,Descender);
}
static inline HRESULT IInkRecognitionAlternate_get_LineNumber(IInkRecognitionAlternate* This,LONG *LineNumber) {
    return This->lpVtbl->get_LineNumber(This,LineNumber);
}
static inline HRESULT IInkRecognitionAlternate_get_Strokes(IInkRecognitionAlternate* This,IInkStrokes **Strokes) {
    return This->lpVtbl->get_Strokes(This,Strokes);
}
static inline HRESULT IInkRecognitionAlternate_get_LineAlternates(IInkRecognitionAlternate* This,IInkRecognitionAlternates **LineAlternates) {
    return This->lpVtbl->get_LineAlternates(This,LineAlternates);
}
static inline HRESULT IInkRecognitionAlternate_get_ConfidenceAlternates(IInkRecognitionAlternate* This,IInkRecognitionAlternates **ConfidenceAlternates) {
    return This->lpVtbl->get_ConfidenceAlternates(This,ConfidenceAlternates);
}
static inline HRESULT IInkRecognitionAlternate_GetStrokesFromStrokeRanges(IInkRecognitionAlternate* This,IInkStrokes *Strokes,IInkStrokes **GetStrokesFromStrokeRanges) {
    return This->lpVtbl->GetStrokesFromStrokeRanges(This,Strokes,GetStrokesFromStrokeRanges);
}
static inline HRESULT IInkRecognitionAlternate_GetStrokesFromTextRange(IInkRecognitionAlternate* This,LONG *selectionStart,LONG *selectionLength,IInkStrokes **GetStrokesFromTextRange) {
    return This->lpVtbl->GetStrokesFromTextRange(This,selectionStart,selectionLength,GetStrokesFromTextRange);
}
static inline HRESULT IInkRecognitionAlternate_GetTextRangeFromStrokes(IInkRecognitionAlternate* This,IInkStrokes *Strokes,LONG *selectionStart,LONG *selectionLength) {
    return This->lpVtbl->GetTextRangeFromStrokes(This,Strokes,selectionStart,selectionLength);
}
static inline HRESULT IInkRecognitionAlternate_AlternatesWithConstantPropertyValues(IInkRecognitionAlternate* This,BSTR PropertyType,IInkRecognitionAlternates **AlternatesWithConstantPropertyValues) {
    return This->lpVtbl->AlternatesWithConstantPropertyValues(This,PropertyType,AlternatesWithConstantPropertyValues);
}
static inline HRESULT IInkRecognitionAlternate_GetPropertyValue(IInkRecognitionAlternate* This,BSTR PropertyType,VARIANT *PropertyValue) {
    return This->lpVtbl->GetPropertyValue(This,PropertyType,PropertyValue);
}
#endif
#endif

#endif


#endif  /* __IInkRecognitionAlternate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkRecognitionResult interface
 */
#ifndef __IInkRecognitionResult_INTERFACE_DEFINED__
#define __IInkRecognitionResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkRecognitionResult, 0x3bc129a8, 0x86cd, 0x45ad, 0xbd,0xe8, 0xe0,0xd3,0x2d,0x61,0xc1,0x6d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3bc129a8-86cd-45ad-bde8-e0d32d61c16d")
IInkRecognitionResult : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_TopString(
        BSTR *TopString) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TopAlternate(
        IInkRecognitionAlternate **TopAlternate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TopConfidence(
        InkRecognitionConfidence *TopConfidence) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Strokes(
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE AlternatesFromSelection(
        LONG selectionStart,
        LONG selectionLength,
        LONG maximumAlternates,
        IInkRecognitionAlternates **AlternatesFromSelection) = 0;

    virtual HRESULT STDMETHODCALLTYPE ModifyTopAlternate(
        IInkRecognitionAlternate *Alternate) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetResultOnStrokes(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkRecognitionResult, 0x3bc129a8, 0x86cd, 0x45ad, 0xbd,0xe8, 0xe0,0xd3,0x2d,0x61,0xc1,0x6d)
#endif
#else
typedef struct IInkRecognitionResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkRecognitionResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkRecognitionResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkRecognitionResult *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkRecognitionResult *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkRecognitionResult *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkRecognitionResult *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkRecognitionResult *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkRecognitionResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TopString)(
        IInkRecognitionResult *This,
        BSTR *TopString);

    HRESULT (STDMETHODCALLTYPE *get_TopAlternate)(
        IInkRecognitionResult *This,
        IInkRecognitionAlternate **TopAlternate);

    HRESULT (STDMETHODCALLTYPE *get_TopConfidence)(
        IInkRecognitionResult *This,
        InkRecognitionConfidence *TopConfidence);

    HRESULT (STDMETHODCALLTYPE *get_Strokes)(
        IInkRecognitionResult *This,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *AlternatesFromSelection)(
        IInkRecognitionResult *This,
        LONG selectionStart,
        LONG selectionLength,
        LONG maximumAlternates,
        IInkRecognitionAlternates **AlternatesFromSelection);

    HRESULT (STDMETHODCALLTYPE *ModifyTopAlternate)(
        IInkRecognitionResult *This,
        IInkRecognitionAlternate *Alternate);

    HRESULT (STDMETHODCALLTYPE *SetResultOnStrokes)(
        IInkRecognitionResult *This);

    END_INTERFACE
} IInkRecognitionResultVtbl;

interface IInkRecognitionResult {
    CONST_VTBL IInkRecognitionResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkRecognitionResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkRecognitionResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkRecognitionResult_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkRecognitionResult_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkRecognitionResult_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkRecognitionResult_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkRecognitionResult_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkRecognitionResult methods ***/
#define IInkRecognitionResult_get_TopString(This,TopString) (This)->lpVtbl->get_TopString(This,TopString)
#define IInkRecognitionResult_get_TopAlternate(This,TopAlternate) (This)->lpVtbl->get_TopAlternate(This,TopAlternate)
#define IInkRecognitionResult_get_TopConfidence(This,TopConfidence) (This)->lpVtbl->get_TopConfidence(This,TopConfidence)
#define IInkRecognitionResult_get_Strokes(This,Strokes) (This)->lpVtbl->get_Strokes(This,Strokes)
#define IInkRecognitionResult_AlternatesFromSelection(This,selectionStart,selectionLength,maximumAlternates,AlternatesFromSelection) (This)->lpVtbl->AlternatesFromSelection(This,selectionStart,selectionLength,maximumAlternates,AlternatesFromSelection)
#define IInkRecognitionResult_ModifyTopAlternate(This,Alternate) (This)->lpVtbl->ModifyTopAlternate(This,Alternate)
#define IInkRecognitionResult_SetResultOnStrokes(This) (This)->lpVtbl->SetResultOnStrokes(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkRecognitionResult_QueryInterface(IInkRecognitionResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkRecognitionResult_AddRef(IInkRecognitionResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkRecognitionResult_Release(IInkRecognitionResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkRecognitionResult_GetTypeInfoCount(IInkRecognitionResult* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkRecognitionResult_GetTypeInfo(IInkRecognitionResult* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkRecognitionResult_GetIDsOfNames(IInkRecognitionResult* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkRecognitionResult_Invoke(IInkRecognitionResult* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkRecognitionResult methods ***/
static inline HRESULT IInkRecognitionResult_get_TopString(IInkRecognitionResult* This,BSTR *TopString) {
    return This->lpVtbl->get_TopString(This,TopString);
}
static inline HRESULT IInkRecognitionResult_get_TopAlternate(IInkRecognitionResult* This,IInkRecognitionAlternate **TopAlternate) {
    return This->lpVtbl->get_TopAlternate(This,TopAlternate);
}
static inline HRESULT IInkRecognitionResult_get_TopConfidence(IInkRecognitionResult* This,InkRecognitionConfidence *TopConfidence) {
    return This->lpVtbl->get_TopConfidence(This,TopConfidence);
}
static inline HRESULT IInkRecognitionResult_get_Strokes(IInkRecognitionResult* This,IInkStrokes **Strokes) {
    return This->lpVtbl->get_Strokes(This,Strokes);
}
static inline HRESULT IInkRecognitionResult_AlternatesFromSelection(IInkRecognitionResult* This,LONG selectionStart,LONG selectionLength,LONG maximumAlternates,IInkRecognitionAlternates **AlternatesFromSelection) {
    return This->lpVtbl->AlternatesFromSelection(This,selectionStart,selectionLength,maximumAlternates,AlternatesFromSelection);
}
static inline HRESULT IInkRecognitionResult_ModifyTopAlternate(IInkRecognitionResult* This,IInkRecognitionAlternate *Alternate) {
    return This->lpVtbl->ModifyTopAlternate(This,Alternate);
}
static inline HRESULT IInkRecognitionResult_SetResultOnStrokes(IInkRecognitionResult* This) {
    return This->lpVtbl->SetResultOnStrokes(This);
}
#endif
#endif

#endif


#endif  /* __IInkRecognitionResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkStrokeDisp interface
 */
#ifndef __IInkStrokeDisp_INTERFACE_DEFINED__
#define __IInkStrokeDisp_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkStrokeDisp, 0x43242fea, 0x91d1, 0x4a72, 0x96,0x3e, 0xfb,0xb9,0x18,0x29,0xcf,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("43242fea-91d1-4a72-963e-fbb91829cfa2")
IInkStrokeDisp : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ID(
        LONG *ID) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BezierPoints(
        VARIANT *Points) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DrawingAttributes(
        IInkDrawingAttributes **DrawAttrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE putref_DrawingAttributes(
        IInkDrawingAttributes *DrawAttrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Ink(
        IInkDisp **Ink) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExtendedProperties(
        IInkExtendedProperties **Properties) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PolylineCusps(
        VARIANT *Cusps) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BezierCusps(
        VARIANT *Cusps) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SelfIntersections(
        VARIANT *Intersections) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PacketCount(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PacketSize(
        LONG *plSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PacketDescription(
        VARIANT *PacketDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Deleted(
        VARIANT_BOOL *Deleted) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoundingBox(
        InkBoundingBoxMode BoundingBoxMode,
        IInkRectangle **Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindIntersections(
        IInkStrokes *Strokes,
        VARIANT *Intersections) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRectangleIntersections(
        IInkRectangle *Rectangle,
        VARIANT *Intersections) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clip(
        IInkRectangle *Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE HitTestCircle(
        LONG X,
        LONG Y,
        float Radius,
        VARIANT_BOOL *Intersects) = 0;

    virtual HRESULT STDMETHODCALLTYPE NearestPoint(
        LONG X,
        LONG Y,
        float *Distance,
        float *Point) = 0;

    virtual HRESULT STDMETHODCALLTYPE Split(
        float SplitAt,
        IInkStrokeDisp **NewStroke) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPacketDescriptionPropertyMetrics(
        BSTR PropertyName,
        LONG *Minimum,
        LONG *Maximum,
        TabletPropertyMetricUnit *Units,
        float *Resolution) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPoints(
        LONG Index,
        LONG Count,
        VARIANT *Points) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPoints(
        VARIANT Points,
        LONG Index,
        LONG Count,
        LONG *NumberOfPointsSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPacketData(
        LONG Index,
        LONG Count,
        VARIANT *PacketData) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPacketValuesByProperty(
        BSTR PropertyName,
        LONG Index,
        LONG Count,
        VARIANT *PacketValues) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPacketValuesByProperty(
        BSTR bstrPropertyName,
        VARIANT PacketValues,
        LONG Index,
        LONG Count,
        LONG *NumberOfPacketsSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFlattenedBezierPoints(
        LONG FittingError,
        VARIANT *FlattenedBezierPoints) = 0;

    virtual HRESULT STDMETHODCALLTYPE Transform(
        IInkTransform *Transform,
        VARIANT_BOOL ApplyOnPenWidth = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScaleToRectangle(
        IInkRectangle *Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE Move(
        float HorizontalComponent,
        float VerticalComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE Rotate(
        float Degrees,
        float x = 0,
        float y = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shear(
        float HorizontalMultiplier,
        float VerticalMultiplier) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScaleTransform(
        float HorizontalMultiplier,
        float VerticalMultiplier) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkStrokeDisp, 0x43242fea, 0x91d1, 0x4a72, 0x96,0x3e, 0xfb,0xb9,0x18,0x29,0xcf,0xa2)
#endif
#else
typedef struct IInkStrokeDispVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkStrokeDisp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkStrokeDisp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkStrokeDisp *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkStrokeDisp *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkStrokeDisp *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkStrokeDisp *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkStrokeDisp *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkStrokeDisp methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ID)(
        IInkStrokeDisp *This,
        LONG *ID);

    HRESULT (STDMETHODCALLTYPE *get_BezierPoints)(
        IInkStrokeDisp *This,
        VARIANT *Points);

    HRESULT (STDMETHODCALLTYPE *get_DrawingAttributes)(
        IInkStrokeDisp *This,
        IInkDrawingAttributes **DrawAttrs);

    HRESULT (STDMETHODCALLTYPE *putref_DrawingAttributes)(
        IInkStrokeDisp *This,
        IInkDrawingAttributes *DrawAttrs);

    HRESULT (STDMETHODCALLTYPE *get_Ink)(
        IInkStrokeDisp *This,
        IInkDisp **Ink);

    HRESULT (STDMETHODCALLTYPE *get_ExtendedProperties)(
        IInkStrokeDisp *This,
        IInkExtendedProperties **Properties);

    HRESULT (STDMETHODCALLTYPE *get_PolylineCusps)(
        IInkStrokeDisp *This,
        VARIANT *Cusps);

    HRESULT (STDMETHODCALLTYPE *get_BezierCusps)(
        IInkStrokeDisp *This,
        VARIANT *Cusps);

    HRESULT (STDMETHODCALLTYPE *get_SelfIntersections)(
        IInkStrokeDisp *This,
        VARIANT *Intersections);

    HRESULT (STDMETHODCALLTYPE *get_PacketCount)(
        IInkStrokeDisp *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *get_PacketSize)(
        IInkStrokeDisp *This,
        LONG *plSize);

    HRESULT (STDMETHODCALLTYPE *get_PacketDescription)(
        IInkStrokeDisp *This,
        VARIANT *PacketDescription);

    HRESULT (STDMETHODCALLTYPE *get_Deleted)(
        IInkStrokeDisp *This,
        VARIANT_BOOL *Deleted);

    HRESULT (STDMETHODCALLTYPE *GetBoundingBox)(
        IInkStrokeDisp *This,
        InkBoundingBoxMode BoundingBoxMode,
        IInkRectangle **Rectangle);

    HRESULT (STDMETHODCALLTYPE *FindIntersections)(
        IInkStrokeDisp *This,
        IInkStrokes *Strokes,
        VARIANT *Intersections);

    HRESULT (STDMETHODCALLTYPE *GetRectangleIntersections)(
        IInkStrokeDisp *This,
        IInkRectangle *Rectangle,
        VARIANT *Intersections);

    HRESULT (STDMETHODCALLTYPE *Clip)(
        IInkStrokeDisp *This,
        IInkRectangle *Rectangle);

    HRESULT (STDMETHODCALLTYPE *HitTestCircle)(
        IInkStrokeDisp *This,
        LONG X,
        LONG Y,
        float Radius,
        VARIANT_BOOL *Intersects);

    HRESULT (STDMETHODCALLTYPE *NearestPoint)(
        IInkStrokeDisp *This,
        LONG X,
        LONG Y,
        float *Distance,
        float *Point);

    HRESULT (STDMETHODCALLTYPE *Split)(
        IInkStrokeDisp *This,
        float SplitAt,
        IInkStrokeDisp **NewStroke);

    HRESULT (STDMETHODCALLTYPE *GetPacketDescriptionPropertyMetrics)(
        IInkStrokeDisp *This,
        BSTR PropertyName,
        LONG *Minimum,
        LONG *Maximum,
        TabletPropertyMetricUnit *Units,
        float *Resolution);

    HRESULT (STDMETHODCALLTYPE *GetPoints)(
        IInkStrokeDisp *This,
        LONG Index,
        LONG Count,
        VARIANT *Points);

    HRESULT (STDMETHODCALLTYPE *SetPoints)(
        IInkStrokeDisp *This,
        VARIANT Points,
        LONG Index,
        LONG Count,
        LONG *NumberOfPointsSet);

    HRESULT (STDMETHODCALLTYPE *GetPacketData)(
        IInkStrokeDisp *This,
        LONG Index,
        LONG Count,
        VARIANT *PacketData);

    HRESULT (STDMETHODCALLTYPE *GetPacketValuesByProperty)(
        IInkStrokeDisp *This,
        BSTR PropertyName,
        LONG Index,
        LONG Count,
        VARIANT *PacketValues);

    HRESULT (STDMETHODCALLTYPE *SetPacketValuesByProperty)(
        IInkStrokeDisp *This,
        BSTR bstrPropertyName,
        VARIANT PacketValues,
        LONG Index,
        LONG Count,
        LONG *NumberOfPacketsSet);

    HRESULT (STDMETHODCALLTYPE *GetFlattenedBezierPoints)(
        IInkStrokeDisp *This,
        LONG FittingError,
        VARIANT *FlattenedBezierPoints);

    HRESULT (STDMETHODCALLTYPE *Transform)(
        IInkStrokeDisp *This,
        IInkTransform *Transform,
        VARIANT_BOOL ApplyOnPenWidth);

    HRESULT (STDMETHODCALLTYPE *ScaleToRectangle)(
        IInkStrokeDisp *This,
        IInkRectangle *Rectangle);

    HRESULT (STDMETHODCALLTYPE *Move)(
        IInkStrokeDisp *This,
        float HorizontalComponent,
        float VerticalComponent);

    HRESULT (STDMETHODCALLTYPE *Rotate)(
        IInkStrokeDisp *This,
        float Degrees,
        float x,
        float y);

    HRESULT (STDMETHODCALLTYPE *Shear)(
        IInkStrokeDisp *This,
        float HorizontalMultiplier,
        float VerticalMultiplier);

    HRESULT (STDMETHODCALLTYPE *ScaleTransform)(
        IInkStrokeDisp *This,
        float HorizontalMultiplier,
        float VerticalMultiplier);

    END_INTERFACE
} IInkStrokeDispVtbl;

interface IInkStrokeDisp {
    CONST_VTBL IInkStrokeDispVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkStrokeDisp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkStrokeDisp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkStrokeDisp_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkStrokeDisp_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkStrokeDisp_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkStrokeDisp_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkStrokeDisp_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkStrokeDisp methods ***/
#define IInkStrokeDisp_get_ID(This,ID) (This)->lpVtbl->get_ID(This,ID)
#define IInkStrokeDisp_get_BezierPoints(This,Points) (This)->lpVtbl->get_BezierPoints(This,Points)
#define IInkStrokeDisp_get_DrawingAttributes(This,DrawAttrs) (This)->lpVtbl->get_DrawingAttributes(This,DrawAttrs)
#define IInkStrokeDisp_putref_DrawingAttributes(This,DrawAttrs) (This)->lpVtbl->putref_DrawingAttributes(This,DrawAttrs)
#define IInkStrokeDisp_get_Ink(This,Ink) (This)->lpVtbl->get_Ink(This,Ink)
#define IInkStrokeDisp_get_ExtendedProperties(This,Properties) (This)->lpVtbl->get_ExtendedProperties(This,Properties)
#define IInkStrokeDisp_get_PolylineCusps(This,Cusps) (This)->lpVtbl->get_PolylineCusps(This,Cusps)
#define IInkStrokeDisp_get_BezierCusps(This,Cusps) (This)->lpVtbl->get_BezierCusps(This,Cusps)
#define IInkStrokeDisp_get_SelfIntersections(This,Intersections) (This)->lpVtbl->get_SelfIntersections(This,Intersections)
#define IInkStrokeDisp_get_PacketCount(This,plCount) (This)->lpVtbl->get_PacketCount(This,plCount)
#define IInkStrokeDisp_get_PacketSize(This,plSize) (This)->lpVtbl->get_PacketSize(This,plSize)
#define IInkStrokeDisp_get_PacketDescription(This,PacketDescription) (This)->lpVtbl->get_PacketDescription(This,PacketDescription)
#define IInkStrokeDisp_get_Deleted(This,Deleted) (This)->lpVtbl->get_Deleted(This,Deleted)
#define IInkStrokeDisp_GetBoundingBox(This,BoundingBoxMode,Rectangle) (This)->lpVtbl->GetBoundingBox(This,BoundingBoxMode,Rectangle)
#define IInkStrokeDisp_FindIntersections(This,Strokes,Intersections) (This)->lpVtbl->FindIntersections(This,Strokes,Intersections)
#define IInkStrokeDisp_GetRectangleIntersections(This,Rectangle,Intersections) (This)->lpVtbl->GetRectangleIntersections(This,Rectangle,Intersections)
#define IInkStrokeDisp_Clip(This,Rectangle) (This)->lpVtbl->Clip(This,Rectangle)
#define IInkStrokeDisp_HitTestCircle(This,X,Y,Radius,Intersects) (This)->lpVtbl->HitTestCircle(This,X,Y,Radius,Intersects)
#define IInkStrokeDisp_NearestPoint(This,X,Y,Distance,Point) (This)->lpVtbl->NearestPoint(This,X,Y,Distance,Point)
#define IInkStrokeDisp_Split(This,SplitAt,NewStroke) (This)->lpVtbl->Split(This,SplitAt,NewStroke)
#define IInkStrokeDisp_GetPacketDescriptionPropertyMetrics(This,PropertyName,Minimum,Maximum,Units,Resolution) (This)->lpVtbl->GetPacketDescriptionPropertyMetrics(This,PropertyName,Minimum,Maximum,Units,Resolution)
#define IInkStrokeDisp_GetPoints(This,Index,Count,Points) (This)->lpVtbl->GetPoints(This,Index,Count,Points)
#define IInkStrokeDisp_SetPoints(This,Points,Index,Count,NumberOfPointsSet) (This)->lpVtbl->SetPoints(This,Points,Index,Count,NumberOfPointsSet)
#define IInkStrokeDisp_GetPacketData(This,Index,Count,PacketData) (This)->lpVtbl->GetPacketData(This,Index,Count,PacketData)
#define IInkStrokeDisp_GetPacketValuesByProperty(This,PropertyName,Index,Count,PacketValues) (This)->lpVtbl->GetPacketValuesByProperty(This,PropertyName,Index,Count,PacketValues)
#define IInkStrokeDisp_SetPacketValuesByProperty(This,bstrPropertyName,PacketValues,Index,Count,NumberOfPacketsSet) (This)->lpVtbl->SetPacketValuesByProperty(This,bstrPropertyName,PacketValues,Index,Count,NumberOfPacketsSet)
#define IInkStrokeDisp_GetFlattenedBezierPoints(This,FittingError,FlattenedBezierPoints) (This)->lpVtbl->GetFlattenedBezierPoints(This,FittingError,FlattenedBezierPoints)
#define IInkStrokeDisp_Transform(This,Transform,ApplyOnPenWidth) (This)->lpVtbl->Transform(This,Transform,ApplyOnPenWidth)
#define IInkStrokeDisp_ScaleToRectangle(This,Rectangle) (This)->lpVtbl->ScaleToRectangle(This,Rectangle)
#define IInkStrokeDisp_Move(This,HorizontalComponent,VerticalComponent) (This)->lpVtbl->Move(This,HorizontalComponent,VerticalComponent)
#define IInkStrokeDisp_Rotate(This,Degrees,x,y) (This)->lpVtbl->Rotate(This,Degrees,x,y)
#define IInkStrokeDisp_Shear(This,HorizontalMultiplier,VerticalMultiplier) (This)->lpVtbl->Shear(This,HorizontalMultiplier,VerticalMultiplier)
#define IInkStrokeDisp_ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier) (This)->lpVtbl->ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkStrokeDisp_QueryInterface(IInkStrokeDisp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkStrokeDisp_AddRef(IInkStrokeDisp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkStrokeDisp_Release(IInkStrokeDisp* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkStrokeDisp_GetTypeInfoCount(IInkStrokeDisp* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkStrokeDisp_GetTypeInfo(IInkStrokeDisp* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkStrokeDisp_GetIDsOfNames(IInkStrokeDisp* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkStrokeDisp_Invoke(IInkStrokeDisp* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkStrokeDisp methods ***/
static inline HRESULT IInkStrokeDisp_get_ID(IInkStrokeDisp* This,LONG *ID) {
    return This->lpVtbl->get_ID(This,ID);
}
static inline HRESULT IInkStrokeDisp_get_BezierPoints(IInkStrokeDisp* This,VARIANT *Points) {
    return This->lpVtbl->get_BezierPoints(This,Points);
}
static inline HRESULT IInkStrokeDisp_get_DrawingAttributes(IInkStrokeDisp* This,IInkDrawingAttributes **DrawAttrs) {
    return This->lpVtbl->get_DrawingAttributes(This,DrawAttrs);
}
static inline HRESULT IInkStrokeDisp_putref_DrawingAttributes(IInkStrokeDisp* This,IInkDrawingAttributes *DrawAttrs) {
    return This->lpVtbl->putref_DrawingAttributes(This,DrawAttrs);
}
static inline HRESULT IInkStrokeDisp_get_Ink(IInkStrokeDisp* This,IInkDisp **Ink) {
    return This->lpVtbl->get_Ink(This,Ink);
}
static inline HRESULT IInkStrokeDisp_get_ExtendedProperties(IInkStrokeDisp* This,IInkExtendedProperties **Properties) {
    return This->lpVtbl->get_ExtendedProperties(This,Properties);
}
static inline HRESULT IInkStrokeDisp_get_PolylineCusps(IInkStrokeDisp* This,VARIANT *Cusps) {
    return This->lpVtbl->get_PolylineCusps(This,Cusps);
}
static inline HRESULT IInkStrokeDisp_get_BezierCusps(IInkStrokeDisp* This,VARIANT *Cusps) {
    return This->lpVtbl->get_BezierCusps(This,Cusps);
}
static inline HRESULT IInkStrokeDisp_get_SelfIntersections(IInkStrokeDisp* This,VARIANT *Intersections) {
    return This->lpVtbl->get_SelfIntersections(This,Intersections);
}
static inline HRESULT IInkStrokeDisp_get_PacketCount(IInkStrokeDisp* This,LONG *plCount) {
    return This->lpVtbl->get_PacketCount(This,plCount);
}
static inline HRESULT IInkStrokeDisp_get_PacketSize(IInkStrokeDisp* This,LONG *plSize) {
    return This->lpVtbl->get_PacketSize(This,plSize);
}
static inline HRESULT IInkStrokeDisp_get_PacketDescription(IInkStrokeDisp* This,VARIANT *PacketDescription) {
    return This->lpVtbl->get_PacketDescription(This,PacketDescription);
}
static inline HRESULT IInkStrokeDisp_get_Deleted(IInkStrokeDisp* This,VARIANT_BOOL *Deleted) {
    return This->lpVtbl->get_Deleted(This,Deleted);
}
static inline HRESULT IInkStrokeDisp_GetBoundingBox(IInkStrokeDisp* This,InkBoundingBoxMode BoundingBoxMode,IInkRectangle **Rectangle) {
    return This->lpVtbl->GetBoundingBox(This,BoundingBoxMode,Rectangle);
}
static inline HRESULT IInkStrokeDisp_FindIntersections(IInkStrokeDisp* This,IInkStrokes *Strokes,VARIANT *Intersections) {
    return This->lpVtbl->FindIntersections(This,Strokes,Intersections);
}
static inline HRESULT IInkStrokeDisp_GetRectangleIntersections(IInkStrokeDisp* This,IInkRectangle *Rectangle,VARIANT *Intersections) {
    return This->lpVtbl->GetRectangleIntersections(This,Rectangle,Intersections);
}
static inline HRESULT IInkStrokeDisp_Clip(IInkStrokeDisp* This,IInkRectangle *Rectangle) {
    return This->lpVtbl->Clip(This,Rectangle);
}
static inline HRESULT IInkStrokeDisp_HitTestCircle(IInkStrokeDisp* This,LONG X,LONG Y,float Radius,VARIANT_BOOL *Intersects) {
    return This->lpVtbl->HitTestCircle(This,X,Y,Radius,Intersects);
}
static inline HRESULT IInkStrokeDisp_NearestPoint(IInkStrokeDisp* This,LONG X,LONG Y,float *Distance,float *Point) {
    return This->lpVtbl->NearestPoint(This,X,Y,Distance,Point);
}
static inline HRESULT IInkStrokeDisp_Split(IInkStrokeDisp* This,float SplitAt,IInkStrokeDisp **NewStroke) {
    return This->lpVtbl->Split(This,SplitAt,NewStroke);
}
static inline HRESULT IInkStrokeDisp_GetPacketDescriptionPropertyMetrics(IInkStrokeDisp* This,BSTR PropertyName,LONG *Minimum,LONG *Maximum,TabletPropertyMetricUnit *Units,float *Resolution) {
    return This->lpVtbl->GetPacketDescriptionPropertyMetrics(This,PropertyName,Minimum,Maximum,Units,Resolution);
}
static inline HRESULT IInkStrokeDisp_GetPoints(IInkStrokeDisp* This,LONG Index,LONG Count,VARIANT *Points) {
    return This->lpVtbl->GetPoints(This,Index,Count,Points);
}
static inline HRESULT IInkStrokeDisp_SetPoints(IInkStrokeDisp* This,VARIANT Points,LONG Index,LONG Count,LONG *NumberOfPointsSet) {
    return This->lpVtbl->SetPoints(This,Points,Index,Count,NumberOfPointsSet);
}
static inline HRESULT IInkStrokeDisp_GetPacketData(IInkStrokeDisp* This,LONG Index,LONG Count,VARIANT *PacketData) {
    return This->lpVtbl->GetPacketData(This,Index,Count,PacketData);
}
static inline HRESULT IInkStrokeDisp_GetPacketValuesByProperty(IInkStrokeDisp* This,BSTR PropertyName,LONG Index,LONG Count,VARIANT *PacketValues) {
    return This->lpVtbl->GetPacketValuesByProperty(This,PropertyName,Index,Count,PacketValues);
}
static inline HRESULT IInkStrokeDisp_SetPacketValuesByProperty(IInkStrokeDisp* This,BSTR bstrPropertyName,VARIANT PacketValues,LONG Index,LONG Count,LONG *NumberOfPacketsSet) {
    return This->lpVtbl->SetPacketValuesByProperty(This,bstrPropertyName,PacketValues,Index,Count,NumberOfPacketsSet);
}
static inline HRESULT IInkStrokeDisp_GetFlattenedBezierPoints(IInkStrokeDisp* This,LONG FittingError,VARIANT *FlattenedBezierPoints) {
    return This->lpVtbl->GetFlattenedBezierPoints(This,FittingError,FlattenedBezierPoints);
}
static inline HRESULT IInkStrokeDisp_Transform(IInkStrokeDisp* This,IInkTransform *Transform,VARIANT_BOOL ApplyOnPenWidth) {
    return This->lpVtbl->Transform(This,Transform,ApplyOnPenWidth);
}
static inline HRESULT IInkStrokeDisp_ScaleToRectangle(IInkStrokeDisp* This,IInkRectangle *Rectangle) {
    return This->lpVtbl->ScaleToRectangle(This,Rectangle);
}
static inline HRESULT IInkStrokeDisp_Move(IInkStrokeDisp* This,float HorizontalComponent,float VerticalComponent) {
    return This->lpVtbl->Move(This,HorizontalComponent,VerticalComponent);
}
static inline HRESULT IInkStrokeDisp_Rotate(IInkStrokeDisp* This,float Degrees,float x,float y) {
    return This->lpVtbl->Rotate(This,Degrees,x,y);
}
static inline HRESULT IInkStrokeDisp_Shear(IInkStrokeDisp* This,float HorizontalMultiplier,float VerticalMultiplier) {
    return This->lpVtbl->Shear(This,HorizontalMultiplier,VerticalMultiplier);
}
static inline HRESULT IInkStrokeDisp_ScaleTransform(IInkStrokeDisp* This,float HorizontalMultiplier,float VerticalMultiplier) {
    return This->lpVtbl->ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier);
}
#endif
#endif

#endif


#endif  /* __IInkStrokeDisp_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkCustomStrokes interface
 */
#ifndef __IInkCustomStrokes_INTERFACE_DEFINED__
#define __IInkCustomStrokes_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkCustomStrokes, 0x7e23a88f, 0xc30e, 0x420f, 0x9b,0xdb, 0x28,0x90,0x25,0x43,0xf0,0xc1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7e23a88f-c30e-420f-9bdb-28902543f0c1")
IInkCustomStrokes : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *Count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **_NewEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        VARIANT Identifier,
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        BSTR Name,
        IInkStrokes *Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        VARIANT Identifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkCustomStrokes, 0x7e23a88f, 0xc30e, 0x420f, 0x9b,0xdb, 0x28,0x90,0x25,0x43,0xf0,0xc1)
#endif
#else
typedef struct IInkCustomStrokesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkCustomStrokes *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkCustomStrokes *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkCustomStrokes *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkCustomStrokes *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkCustomStrokes *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkCustomStrokes *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkCustomStrokes *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkCustomStrokes methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IInkCustomStrokes *This,
        LONG *Count);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IInkCustomStrokes *This,
        IUnknown **_NewEnum);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IInkCustomStrokes *This,
        VARIANT Identifier,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IInkCustomStrokes *This,
        BSTR Name,
        IInkStrokes *Strokes);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IInkCustomStrokes *This,
        VARIANT Identifier);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IInkCustomStrokes *This);

    END_INTERFACE
} IInkCustomStrokesVtbl;

interface IInkCustomStrokes {
    CONST_VTBL IInkCustomStrokesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkCustomStrokes_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkCustomStrokes_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkCustomStrokes_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkCustomStrokes_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkCustomStrokes_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkCustomStrokes_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkCustomStrokes_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkCustomStrokes methods ***/
#define IInkCustomStrokes_get_Count(This,Count) (This)->lpVtbl->get_Count(This,Count)
#define IInkCustomStrokes_get__NewEnum(This,_NewEnum) (This)->lpVtbl->get__NewEnum(This,_NewEnum)
#define IInkCustomStrokes_Item(This,Identifier,Strokes) (This)->lpVtbl->Item(This,Identifier,Strokes)
#define IInkCustomStrokes_Add(This,Name,Strokes) (This)->lpVtbl->Add(This,Name,Strokes)
#define IInkCustomStrokes_Remove(This,Identifier) (This)->lpVtbl->Remove(This,Identifier)
#define IInkCustomStrokes_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkCustomStrokes_QueryInterface(IInkCustomStrokes* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkCustomStrokes_AddRef(IInkCustomStrokes* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkCustomStrokes_Release(IInkCustomStrokes* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkCustomStrokes_GetTypeInfoCount(IInkCustomStrokes* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkCustomStrokes_GetTypeInfo(IInkCustomStrokes* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkCustomStrokes_GetIDsOfNames(IInkCustomStrokes* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkCustomStrokes_Invoke(IInkCustomStrokes* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkCustomStrokes methods ***/
static inline HRESULT IInkCustomStrokes_get_Count(IInkCustomStrokes* This,LONG *Count) {
    return This->lpVtbl->get_Count(This,Count);
}
static inline HRESULT IInkCustomStrokes_get__NewEnum(IInkCustomStrokes* This,IUnknown **_NewEnum) {
    return This->lpVtbl->get__NewEnum(This,_NewEnum);
}
static inline HRESULT IInkCustomStrokes_Item(IInkCustomStrokes* This,VARIANT Identifier,IInkStrokes **Strokes) {
    return This->lpVtbl->Item(This,Identifier,Strokes);
}
static inline HRESULT IInkCustomStrokes_Add(IInkCustomStrokes* This,BSTR Name,IInkStrokes *Strokes) {
    return This->lpVtbl->Add(This,Name,Strokes);
}
static inline HRESULT IInkCustomStrokes_Remove(IInkCustomStrokes* This,VARIANT Identifier) {
    return This->lpVtbl->Remove(This,Identifier);
}
static inline HRESULT IInkCustomStrokes_Clear(IInkCustomStrokes* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __IInkCustomStrokes_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkDisp interface
 */
#ifndef __IInkDisp_INTERFACE_DEFINED__
#define __IInkDisp_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkDisp, 0x9d398fa0, 0xc4e2, 0x4fcd, 0x99,0x73, 0x97,0x5c,0xaa,0xf4,0x7e,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9d398fa0-c4e2-4fcd-9973-975caaf47ea6")
IInkDisp : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Strokes(
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExtendedProperties(
        IInkExtendedProperties **Properties) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Dirty(
        VARIANT_BOOL *Dirty) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Dirty(
        VARIANT_BOOL Dirty) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CustomStrokes(
        IInkCustomStrokes **ppunkInkCustomStrokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoundingBox(
        InkBoundingBoxMode BoundingBoxMode,
        IInkRectangle **Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteStrokes(
        IInkStrokes *Strokes = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteStroke(
        IInkStrokeDisp *Stroke) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExtractStrokes(
        IInkStrokes *Strokes,
        InkExtractFlags ExtractFlags,
        IInkDisp **ExtractedInk) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExtractWithRectangle(
        IInkRectangle *Rectangle,
        InkExtractFlags extractFlags,
        IInkDisp **ExtractedInk) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clip(
        IInkRectangle *Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IInkDisp **NewInk) = 0;

    virtual HRESULT STDMETHODCALLTYPE HitTestCircle(
        LONG X,
        LONG Y,
        float radius,
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE HitTestWithRectangle(
        IInkRectangle *SelectionRectangle,
        float IntersectPercent,
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE HitTestWithLasso(
        VARIANT Points,
        float IntersectPercent,
        VARIANT *LassoPoints,
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE NearestPoint(
        LONG X,
        LONG Y,
        float *PointOnStroke,
        float *DistanceFromPacket,
        IInkStrokeDisp **Stroke) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStrokes(
        VARIANT StrokeIds,
        IInkStrokes **Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStrokesAtRectangle(
        IInkStrokes *SourceStrokes,
        IInkRectangle *TargetRectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        InkPersistenceFormat PersistenceFormat,
        InkPersistenceCompressionMode CompressionMode,
        VARIANT *Data) = 0;

    virtual HRESULT STDMETHODCALLTYPE Load(
        VARIANT Data) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStroke(
        VARIANT PacketData,
        VARIANT PacketDescription,
        IInkStrokeDisp **Stroke) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClipboardCopyWithRectangle(
        IInkRectangle *Rectangle,
        InkClipboardFormats ClipboardFormats,
        InkClipboardModes ClipboardModes,
        IDataObject **DataObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClipboardCopy(
        IInkStrokes *strokes,
        InkClipboardFormats ClipboardFormats,
        InkClipboardModes ClipboardModes,
        IDataObject **DataObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE CanPaste(
        IDataObject *DataObject,
        VARIANT_BOOL *CanPaste) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClipboardPaste(
        LONG x,
        LONG y,
        IDataObject *DataObject,
        IInkStrokes **Strokes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkDisp, 0x9d398fa0, 0xc4e2, 0x4fcd, 0x99,0x73, 0x97,0x5c,0xaa,0xf4,0x7e,0xa6)
#endif
#else
typedef struct IInkDispVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkDisp *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkDisp *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkDisp *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkDisp *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkDisp *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkDisp *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkDisp *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkDisp methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Strokes)(
        IInkDisp *This,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *get_ExtendedProperties)(
        IInkDisp *This,
        IInkExtendedProperties **Properties);

    HRESULT (STDMETHODCALLTYPE *get_Dirty)(
        IInkDisp *This,
        VARIANT_BOOL *Dirty);

    HRESULT (STDMETHODCALLTYPE *put_Dirty)(
        IInkDisp *This,
        VARIANT_BOOL Dirty);

    HRESULT (STDMETHODCALLTYPE *get_CustomStrokes)(
        IInkDisp *This,
        IInkCustomStrokes **ppunkInkCustomStrokes);

    HRESULT (STDMETHODCALLTYPE *GetBoundingBox)(
        IInkDisp *This,
        InkBoundingBoxMode BoundingBoxMode,
        IInkRectangle **Rectangle);

    HRESULT (STDMETHODCALLTYPE *DeleteStrokes)(
        IInkDisp *This,
        IInkStrokes *Strokes);

    HRESULT (STDMETHODCALLTYPE *DeleteStroke)(
        IInkDisp *This,
        IInkStrokeDisp *Stroke);

    HRESULT (STDMETHODCALLTYPE *ExtractStrokes)(
        IInkDisp *This,
        IInkStrokes *Strokes,
        InkExtractFlags ExtractFlags,
        IInkDisp **ExtractedInk);

    HRESULT (STDMETHODCALLTYPE *ExtractWithRectangle)(
        IInkDisp *This,
        IInkRectangle *Rectangle,
        InkExtractFlags extractFlags,
        IInkDisp **ExtractedInk);

    HRESULT (STDMETHODCALLTYPE *Clip)(
        IInkDisp *This,
        IInkRectangle *Rectangle);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IInkDisp *This,
        IInkDisp **NewInk);

    HRESULT (STDMETHODCALLTYPE *HitTestCircle)(
        IInkDisp *This,
        LONG X,
        LONG Y,
        float radius,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *HitTestWithRectangle)(
        IInkDisp *This,
        IInkRectangle *SelectionRectangle,
        float IntersectPercent,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *HitTestWithLasso)(
        IInkDisp *This,
        VARIANT Points,
        float IntersectPercent,
        VARIANT *LassoPoints,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *NearestPoint)(
        IInkDisp *This,
        LONG X,
        LONG Y,
        float *PointOnStroke,
        float *DistanceFromPacket,
        IInkStrokeDisp **Stroke);

    HRESULT (STDMETHODCALLTYPE *CreateStrokes)(
        IInkDisp *This,
        VARIANT StrokeIds,
        IInkStrokes **Strokes);

    HRESULT (STDMETHODCALLTYPE *AddStrokesAtRectangle)(
        IInkDisp *This,
        IInkStrokes *SourceStrokes,
        IInkRectangle *TargetRectangle);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IInkDisp *This,
        InkPersistenceFormat PersistenceFormat,
        InkPersistenceCompressionMode CompressionMode,
        VARIANT *Data);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IInkDisp *This,
        VARIANT Data);

    HRESULT (STDMETHODCALLTYPE *CreateStroke)(
        IInkDisp *This,
        VARIANT PacketData,
        VARIANT PacketDescription,
        IInkStrokeDisp **Stroke);

    HRESULT (STDMETHODCALLTYPE *ClipboardCopyWithRectangle)(
        IInkDisp *This,
        IInkRectangle *Rectangle,
        InkClipboardFormats ClipboardFormats,
        InkClipboardModes ClipboardModes,
        IDataObject **DataObject);

    HRESULT (STDMETHODCALLTYPE *ClipboardCopy)(
        IInkDisp *This,
        IInkStrokes *strokes,
        InkClipboardFormats ClipboardFormats,
        InkClipboardModes ClipboardModes,
        IDataObject **DataObject);

    HRESULT (STDMETHODCALLTYPE *CanPaste)(
        IInkDisp *This,
        IDataObject *DataObject,
        VARIANT_BOOL *CanPaste);

    HRESULT (STDMETHODCALLTYPE *ClipboardPaste)(
        IInkDisp *This,
        LONG x,
        LONG y,
        IDataObject *DataObject,
        IInkStrokes **Strokes);

    END_INTERFACE
} IInkDispVtbl;

interface IInkDisp {
    CONST_VTBL IInkDispVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkDisp_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkDisp_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkDisp_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkDisp_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkDisp_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkDisp_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkDisp_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkDisp methods ***/
#define IInkDisp_get_Strokes(This,Strokes) (This)->lpVtbl->get_Strokes(This,Strokes)
#define IInkDisp_get_ExtendedProperties(This,Properties) (This)->lpVtbl->get_ExtendedProperties(This,Properties)
#define IInkDisp_get_Dirty(This,Dirty) (This)->lpVtbl->get_Dirty(This,Dirty)
#define IInkDisp_put_Dirty(This,Dirty) (This)->lpVtbl->put_Dirty(This,Dirty)
#define IInkDisp_get_CustomStrokes(This,ppunkInkCustomStrokes) (This)->lpVtbl->get_CustomStrokes(This,ppunkInkCustomStrokes)
#define IInkDisp_GetBoundingBox(This,BoundingBoxMode,Rectangle) (This)->lpVtbl->GetBoundingBox(This,BoundingBoxMode,Rectangle)
#define IInkDisp_DeleteStrokes(This,Strokes) (This)->lpVtbl->DeleteStrokes(This,Strokes)
#define IInkDisp_DeleteStroke(This,Stroke) (This)->lpVtbl->DeleteStroke(This,Stroke)
#define IInkDisp_ExtractStrokes(This,Strokes,ExtractFlags,ExtractedInk) (This)->lpVtbl->ExtractStrokes(This,Strokes,ExtractFlags,ExtractedInk)
#define IInkDisp_ExtractWithRectangle(This,Rectangle,extractFlags,ExtractedInk) (This)->lpVtbl->ExtractWithRectangle(This,Rectangle,extractFlags,ExtractedInk)
#define IInkDisp_Clip(This,Rectangle) (This)->lpVtbl->Clip(This,Rectangle)
#define IInkDisp_Clone(This,NewInk) (This)->lpVtbl->Clone(This,NewInk)
#define IInkDisp_HitTestCircle(This,X,Y,radius,Strokes) (This)->lpVtbl->HitTestCircle(This,X,Y,radius,Strokes)
#define IInkDisp_HitTestWithRectangle(This,SelectionRectangle,IntersectPercent,Strokes) (This)->lpVtbl->HitTestWithRectangle(This,SelectionRectangle,IntersectPercent,Strokes)
#define IInkDisp_HitTestWithLasso(This,Points,IntersectPercent,LassoPoints,Strokes) (This)->lpVtbl->HitTestWithLasso(This,Points,IntersectPercent,LassoPoints,Strokes)
#define IInkDisp_NearestPoint(This,X,Y,PointOnStroke,DistanceFromPacket,Stroke) (This)->lpVtbl->NearestPoint(This,X,Y,PointOnStroke,DistanceFromPacket,Stroke)
#define IInkDisp_CreateStrokes(This,StrokeIds,Strokes) (This)->lpVtbl->CreateStrokes(This,StrokeIds,Strokes)
#define IInkDisp_AddStrokesAtRectangle(This,SourceStrokes,TargetRectangle) (This)->lpVtbl->AddStrokesAtRectangle(This,SourceStrokes,TargetRectangle)
#define IInkDisp_Save(This,PersistenceFormat,CompressionMode,Data) (This)->lpVtbl->Save(This,PersistenceFormat,CompressionMode,Data)
#define IInkDisp_Load(This,Data) (This)->lpVtbl->Load(This,Data)
#define IInkDisp_CreateStroke(This,PacketData,PacketDescription,Stroke) (This)->lpVtbl->CreateStroke(This,PacketData,PacketDescription,Stroke)
#define IInkDisp_ClipboardCopyWithRectangle(This,Rectangle,ClipboardFormats,ClipboardModes,DataObject) (This)->lpVtbl->ClipboardCopyWithRectangle(This,Rectangle,ClipboardFormats,ClipboardModes,DataObject)
#define IInkDisp_ClipboardCopy(This,strokes,ClipboardFormats,ClipboardModes,DataObject) (This)->lpVtbl->ClipboardCopy(This,strokes,ClipboardFormats,ClipboardModes,DataObject)
#define IInkDisp_CanPaste(This,DataObject,CanPaste) (This)->lpVtbl->CanPaste(This,DataObject,CanPaste)
#define IInkDisp_ClipboardPaste(This,x,y,DataObject,Strokes) (This)->lpVtbl->ClipboardPaste(This,x,y,DataObject,Strokes)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkDisp_QueryInterface(IInkDisp* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkDisp_AddRef(IInkDisp* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkDisp_Release(IInkDisp* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkDisp_GetTypeInfoCount(IInkDisp* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkDisp_GetTypeInfo(IInkDisp* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkDisp_GetIDsOfNames(IInkDisp* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkDisp_Invoke(IInkDisp* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkDisp methods ***/
static inline HRESULT IInkDisp_get_Strokes(IInkDisp* This,IInkStrokes **Strokes) {
    return This->lpVtbl->get_Strokes(This,Strokes);
}
static inline HRESULT IInkDisp_get_ExtendedProperties(IInkDisp* This,IInkExtendedProperties **Properties) {
    return This->lpVtbl->get_ExtendedProperties(This,Properties);
}
static inline HRESULT IInkDisp_get_Dirty(IInkDisp* This,VARIANT_BOOL *Dirty) {
    return This->lpVtbl->get_Dirty(This,Dirty);
}
static inline HRESULT IInkDisp_put_Dirty(IInkDisp* This,VARIANT_BOOL Dirty) {
    return This->lpVtbl->put_Dirty(This,Dirty);
}
static inline HRESULT IInkDisp_get_CustomStrokes(IInkDisp* This,IInkCustomStrokes **ppunkInkCustomStrokes) {
    return This->lpVtbl->get_CustomStrokes(This,ppunkInkCustomStrokes);
}
static inline HRESULT IInkDisp_GetBoundingBox(IInkDisp* This,InkBoundingBoxMode BoundingBoxMode,IInkRectangle **Rectangle) {
    return This->lpVtbl->GetBoundingBox(This,BoundingBoxMode,Rectangle);
}
static inline HRESULT IInkDisp_DeleteStrokes(IInkDisp* This,IInkStrokes *Strokes) {
    return This->lpVtbl->DeleteStrokes(This,Strokes);
}
static inline HRESULT IInkDisp_DeleteStroke(IInkDisp* This,IInkStrokeDisp *Stroke) {
    return This->lpVtbl->DeleteStroke(This,Stroke);
}
static inline HRESULT IInkDisp_ExtractStrokes(IInkDisp* This,IInkStrokes *Strokes,InkExtractFlags ExtractFlags,IInkDisp **ExtractedInk) {
    return This->lpVtbl->ExtractStrokes(This,Strokes,ExtractFlags,ExtractedInk);
}
static inline HRESULT IInkDisp_ExtractWithRectangle(IInkDisp* This,IInkRectangle *Rectangle,InkExtractFlags extractFlags,IInkDisp **ExtractedInk) {
    return This->lpVtbl->ExtractWithRectangle(This,Rectangle,extractFlags,ExtractedInk);
}
static inline HRESULT IInkDisp_Clip(IInkDisp* This,IInkRectangle *Rectangle) {
    return This->lpVtbl->Clip(This,Rectangle);
}
static inline HRESULT IInkDisp_Clone(IInkDisp* This,IInkDisp **NewInk) {
    return This->lpVtbl->Clone(This,NewInk);
}
static inline HRESULT IInkDisp_HitTestCircle(IInkDisp* This,LONG X,LONG Y,float radius,IInkStrokes **Strokes) {
    return This->lpVtbl->HitTestCircle(This,X,Y,radius,Strokes);
}
static inline HRESULT IInkDisp_HitTestWithRectangle(IInkDisp* This,IInkRectangle *SelectionRectangle,float IntersectPercent,IInkStrokes **Strokes) {
    return This->lpVtbl->HitTestWithRectangle(This,SelectionRectangle,IntersectPercent,Strokes);
}
static inline HRESULT IInkDisp_HitTestWithLasso(IInkDisp* This,VARIANT Points,float IntersectPercent,VARIANT *LassoPoints,IInkStrokes **Strokes) {
    return This->lpVtbl->HitTestWithLasso(This,Points,IntersectPercent,LassoPoints,Strokes);
}
static inline HRESULT IInkDisp_NearestPoint(IInkDisp* This,LONG X,LONG Y,float *PointOnStroke,float *DistanceFromPacket,IInkStrokeDisp **Stroke) {
    return This->lpVtbl->NearestPoint(This,X,Y,PointOnStroke,DistanceFromPacket,Stroke);
}
static inline HRESULT IInkDisp_CreateStrokes(IInkDisp* This,VARIANT StrokeIds,IInkStrokes **Strokes) {
    return This->lpVtbl->CreateStrokes(This,StrokeIds,Strokes);
}
static inline HRESULT IInkDisp_AddStrokesAtRectangle(IInkDisp* This,IInkStrokes *SourceStrokes,IInkRectangle *TargetRectangle) {
    return This->lpVtbl->AddStrokesAtRectangle(This,SourceStrokes,TargetRectangle);
}
static inline HRESULT IInkDisp_Save(IInkDisp* This,InkPersistenceFormat PersistenceFormat,InkPersistenceCompressionMode CompressionMode,VARIANT *Data) {
    return This->lpVtbl->Save(This,PersistenceFormat,CompressionMode,Data);
}
static inline HRESULT IInkDisp_Load(IInkDisp* This,VARIANT Data) {
    return This->lpVtbl->Load(This,Data);
}
static inline HRESULT IInkDisp_CreateStroke(IInkDisp* This,VARIANT PacketData,VARIANT PacketDescription,IInkStrokeDisp **Stroke) {
    return This->lpVtbl->CreateStroke(This,PacketData,PacketDescription,Stroke);
}
static inline HRESULT IInkDisp_ClipboardCopyWithRectangle(IInkDisp* This,IInkRectangle *Rectangle,InkClipboardFormats ClipboardFormats,InkClipboardModes ClipboardModes,IDataObject **DataObject) {
    return This->lpVtbl->ClipboardCopyWithRectangle(This,Rectangle,ClipboardFormats,ClipboardModes,DataObject);
}
static inline HRESULT IInkDisp_ClipboardCopy(IInkDisp* This,IInkStrokes *strokes,InkClipboardFormats ClipboardFormats,InkClipboardModes ClipboardModes,IDataObject **DataObject) {
    return This->lpVtbl->ClipboardCopy(This,strokes,ClipboardFormats,ClipboardModes,DataObject);
}
static inline HRESULT IInkDisp_CanPaste(IInkDisp* This,IDataObject *DataObject,VARIANT_BOOL *CanPaste) {
    return This->lpVtbl->CanPaste(This,DataObject,CanPaste);
}
static inline HRESULT IInkDisp_ClipboardPaste(IInkDisp* This,LONG x,LONG y,IDataObject *DataObject,IInkStrokes **Strokes) {
    return This->lpVtbl->ClipboardPaste(This,x,y,DataObject,Strokes);
}
#endif
#endif

#endif


#endif  /* __IInkDisp_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkStrokes interface
 */
#ifndef __IInkStrokes_INTERFACE_DEFINED__
#define __IInkStrokes_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkStrokes, 0xf1f4c9d8, 0x590a, 0x4963, 0xb3,0xae, 0x19,0x35,0x67,0x1b,0xb6,0xf3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f1f4c9d8-590a-4963-b3ae-1935671bb6f3")
IInkStrokes : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *Count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **_NewEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Ink(
        IInkDisp **Ink) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RecognitionResult(
        IInkRecognitionResult **RecognitionResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE ToString(
        BSTR *ToString) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG Index,
        IInkStrokeDisp **Stroke) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        IInkStrokeDisp *InkStroke) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStrokes(
        IInkStrokes *InkStrokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        IInkStrokeDisp *InkStroke) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStrokes(
        IInkStrokes *InkStrokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE ModifyDrawingAttributes(
        IInkDrawingAttributes *DrawAttrs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoundingBox(
        InkBoundingBoxMode BoundingBoxMode,
        IInkRectangle **BoundingBox) = 0;

    virtual HRESULT STDMETHODCALLTYPE Transform(
        IInkTransform *Transform,
        VARIANT_BOOL ApplyOnPenWidth = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScaleToRectangle(
        IInkRectangle *Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE Move(
        float HorizontalComponent,
        float VerticalComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE Rotate(
        float Degrees,
        float x = 0,
        float y = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shear(
        float HorizontalMultiplier,
        float VerticalMultiplier) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScaleTransform(
        float HorizontalMultiplier,
        float VerticalMultiplier) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clip(
        IInkRectangle *Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveRecognitionResult(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkStrokes, 0xf1f4c9d8, 0x590a, 0x4963, 0xb3,0xae, 0x19,0x35,0x67,0x1b,0xb6,0xf3)
#endif
#else
typedef struct IInkStrokesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkStrokes *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkStrokes *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkStrokes *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkStrokes *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkStrokes *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkStrokes *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkStrokes *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkStrokes methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IInkStrokes *This,
        LONG *Count);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IInkStrokes *This,
        IUnknown **_NewEnum);

    HRESULT (STDMETHODCALLTYPE *get_Ink)(
        IInkStrokes *This,
        IInkDisp **Ink);

    HRESULT (STDMETHODCALLTYPE *get_RecognitionResult)(
        IInkStrokes *This,
        IInkRecognitionResult **RecognitionResult);

    HRESULT (STDMETHODCALLTYPE *ToString)(
        IInkStrokes *This,
        BSTR *ToString);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IInkStrokes *This,
        LONG Index,
        IInkStrokeDisp **Stroke);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IInkStrokes *This,
        IInkStrokeDisp *InkStroke);

    HRESULT (STDMETHODCALLTYPE *AddStrokes)(
        IInkStrokes *This,
        IInkStrokes *InkStrokes);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IInkStrokes *This,
        IInkStrokeDisp *InkStroke);

    HRESULT (STDMETHODCALLTYPE *RemoveStrokes)(
        IInkStrokes *This,
        IInkStrokes *InkStrokes);

    HRESULT (STDMETHODCALLTYPE *ModifyDrawingAttributes)(
        IInkStrokes *This,
        IInkDrawingAttributes *DrawAttrs);

    HRESULT (STDMETHODCALLTYPE *GetBoundingBox)(
        IInkStrokes *This,
        InkBoundingBoxMode BoundingBoxMode,
        IInkRectangle **BoundingBox);

    HRESULT (STDMETHODCALLTYPE *Transform)(
        IInkStrokes *This,
        IInkTransform *Transform,
        VARIANT_BOOL ApplyOnPenWidth);

    HRESULT (STDMETHODCALLTYPE *ScaleToRectangle)(
        IInkStrokes *This,
        IInkRectangle *Rectangle);

    HRESULT (STDMETHODCALLTYPE *Move)(
        IInkStrokes *This,
        float HorizontalComponent,
        float VerticalComponent);

    HRESULT (STDMETHODCALLTYPE *Rotate)(
        IInkStrokes *This,
        float Degrees,
        float x,
        float y);

    HRESULT (STDMETHODCALLTYPE *Shear)(
        IInkStrokes *This,
        float HorizontalMultiplier,
        float VerticalMultiplier);

    HRESULT (STDMETHODCALLTYPE *ScaleTransform)(
        IInkStrokes *This,
        float HorizontalMultiplier,
        float VerticalMultiplier);

    HRESULT (STDMETHODCALLTYPE *Clip)(
        IInkStrokes *This,
        IInkRectangle *Rectangle);

    HRESULT (STDMETHODCALLTYPE *RemoveRecognitionResult)(
        IInkStrokes *This);

    END_INTERFACE
} IInkStrokesVtbl;

interface IInkStrokes {
    CONST_VTBL IInkStrokesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkStrokes_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkStrokes_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkStrokes_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkStrokes_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkStrokes_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkStrokes_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkStrokes_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkStrokes methods ***/
#define IInkStrokes_get_Count(This,Count) (This)->lpVtbl->get_Count(This,Count)
#define IInkStrokes_get__NewEnum(This,_NewEnum) (This)->lpVtbl->get__NewEnum(This,_NewEnum)
#define IInkStrokes_get_Ink(This,Ink) (This)->lpVtbl->get_Ink(This,Ink)
#define IInkStrokes_get_RecognitionResult(This,RecognitionResult) (This)->lpVtbl->get_RecognitionResult(This,RecognitionResult)
#define IInkStrokes_ToString(This,ToString) (This)->lpVtbl->ToString(This,ToString)
#define IInkStrokes_Item(This,Index,Stroke) (This)->lpVtbl->Item(This,Index,Stroke)
#define IInkStrokes_Add(This,InkStroke) (This)->lpVtbl->Add(This,InkStroke)
#define IInkStrokes_AddStrokes(This,InkStrokes) (This)->lpVtbl->AddStrokes(This,InkStrokes)
#define IInkStrokes_Remove(This,InkStroke) (This)->lpVtbl->Remove(This,InkStroke)
#define IInkStrokes_RemoveStrokes(This,InkStrokes) (This)->lpVtbl->RemoveStrokes(This,InkStrokes)
#define IInkStrokes_ModifyDrawingAttributes(This,DrawAttrs) (This)->lpVtbl->ModifyDrawingAttributes(This,DrawAttrs)
#define IInkStrokes_GetBoundingBox(This,BoundingBoxMode,BoundingBox) (This)->lpVtbl->GetBoundingBox(This,BoundingBoxMode,BoundingBox)
#define IInkStrokes_Transform(This,Transform,ApplyOnPenWidth) (This)->lpVtbl->Transform(This,Transform,ApplyOnPenWidth)
#define IInkStrokes_ScaleToRectangle(This,Rectangle) (This)->lpVtbl->ScaleToRectangle(This,Rectangle)
#define IInkStrokes_Move(This,HorizontalComponent,VerticalComponent) (This)->lpVtbl->Move(This,HorizontalComponent,VerticalComponent)
#define IInkStrokes_Rotate(This,Degrees,x,y) (This)->lpVtbl->Rotate(This,Degrees,x,y)
#define IInkStrokes_Shear(This,HorizontalMultiplier,VerticalMultiplier) (This)->lpVtbl->Shear(This,HorizontalMultiplier,VerticalMultiplier)
#define IInkStrokes_ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier) (This)->lpVtbl->ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier)
#define IInkStrokes_Clip(This,Rectangle) (This)->lpVtbl->Clip(This,Rectangle)
#define IInkStrokes_RemoveRecognitionResult(This) (This)->lpVtbl->RemoveRecognitionResult(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkStrokes_QueryInterface(IInkStrokes* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkStrokes_AddRef(IInkStrokes* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkStrokes_Release(IInkStrokes* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkStrokes_GetTypeInfoCount(IInkStrokes* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkStrokes_GetTypeInfo(IInkStrokes* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkStrokes_GetIDsOfNames(IInkStrokes* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkStrokes_Invoke(IInkStrokes* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkStrokes methods ***/
static inline HRESULT IInkStrokes_get_Count(IInkStrokes* This,LONG *Count) {
    return This->lpVtbl->get_Count(This,Count);
}
static inline HRESULT IInkStrokes_get__NewEnum(IInkStrokes* This,IUnknown **_NewEnum) {
    return This->lpVtbl->get__NewEnum(This,_NewEnum);
}
static inline HRESULT IInkStrokes_get_Ink(IInkStrokes* This,IInkDisp **Ink) {
    return This->lpVtbl->get_Ink(This,Ink);
}
static inline HRESULT IInkStrokes_get_RecognitionResult(IInkStrokes* This,IInkRecognitionResult **RecognitionResult) {
    return This->lpVtbl->get_RecognitionResult(This,RecognitionResult);
}
static inline HRESULT IInkStrokes_ToString(IInkStrokes* This,BSTR *ToString) {
    return This->lpVtbl->ToString(This,ToString);
}
static inline HRESULT IInkStrokes_Item(IInkStrokes* This,LONG Index,IInkStrokeDisp **Stroke) {
    return This->lpVtbl->Item(This,Index,Stroke);
}
static inline HRESULT IInkStrokes_Add(IInkStrokes* This,IInkStrokeDisp *InkStroke) {
    return This->lpVtbl->Add(This,InkStroke);
}
static inline HRESULT IInkStrokes_AddStrokes(IInkStrokes* This,IInkStrokes *InkStrokes) {
    return This->lpVtbl->AddStrokes(This,InkStrokes);
}
static inline HRESULT IInkStrokes_Remove(IInkStrokes* This,IInkStrokeDisp *InkStroke) {
    return This->lpVtbl->Remove(This,InkStroke);
}
static inline HRESULT IInkStrokes_RemoveStrokes(IInkStrokes* This,IInkStrokes *InkStrokes) {
    return This->lpVtbl->RemoveStrokes(This,InkStrokes);
}
static inline HRESULT IInkStrokes_ModifyDrawingAttributes(IInkStrokes* This,IInkDrawingAttributes *DrawAttrs) {
    return This->lpVtbl->ModifyDrawingAttributes(This,DrawAttrs);
}
static inline HRESULT IInkStrokes_GetBoundingBox(IInkStrokes* This,InkBoundingBoxMode BoundingBoxMode,IInkRectangle **BoundingBox) {
    return This->lpVtbl->GetBoundingBox(This,BoundingBoxMode,BoundingBox);
}
static inline HRESULT IInkStrokes_Transform(IInkStrokes* This,IInkTransform *Transform,VARIANT_BOOL ApplyOnPenWidth) {
    return This->lpVtbl->Transform(This,Transform,ApplyOnPenWidth);
}
static inline HRESULT IInkStrokes_ScaleToRectangle(IInkStrokes* This,IInkRectangle *Rectangle) {
    return This->lpVtbl->ScaleToRectangle(This,Rectangle);
}
static inline HRESULT IInkStrokes_Move(IInkStrokes* This,float HorizontalComponent,float VerticalComponent) {
    return This->lpVtbl->Move(This,HorizontalComponent,VerticalComponent);
}
static inline HRESULT IInkStrokes_Rotate(IInkStrokes* This,float Degrees,float x,float y) {
    return This->lpVtbl->Rotate(This,Degrees,x,y);
}
static inline HRESULT IInkStrokes_Shear(IInkStrokes* This,float HorizontalMultiplier,float VerticalMultiplier) {
    return This->lpVtbl->Shear(This,HorizontalMultiplier,VerticalMultiplier);
}
static inline HRESULT IInkStrokes_ScaleTransform(IInkStrokes* This,float HorizontalMultiplier,float VerticalMultiplier) {
    return This->lpVtbl->ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier);
}
static inline HRESULT IInkStrokes_Clip(IInkStrokes* This,IInkRectangle *Rectangle) {
    return This->lpVtbl->Clip(This,Rectangle);
}
static inline HRESULT IInkStrokes_RemoveRecognitionResult(IInkStrokes* This) {
    return This->lpVtbl->RemoveRecognitionResult(This);
}
#endif
#endif

#endif


#endif  /* __IInkStrokes_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkRenderer interface
 */
#ifndef __IInkRenderer_INTERFACE_DEFINED__
#define __IInkRenderer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkRenderer, 0xe6257a9c, 0xb511, 0x4f4c, 0xa8,0xb0, 0xa7,0xdb,0xc9,0x50,0x6b,0x83);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e6257a9c-b511-4f4c-a8b0-a7dbc9506b83")
IInkRenderer : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetViewTransform(
        IInkTransform *ViewTransform) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetViewTransform(
        IInkTransform *ViewTransform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectTransform(
        IInkTransform *ObjectTransform) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetObjectTransform(
        IInkTransform *ObjectTransform) = 0;

    virtual HRESULT STDMETHODCALLTYPE Draw(
        LONG_PTR hDC,
        IInkStrokes *Strokes) = 0;

    virtual HRESULT STDMETHODCALLTYPE DrawStroke(
        LONG_PTR hDC,
        IInkStrokeDisp *Stroke,
        IInkDrawingAttributes *DrawingAttributes = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE PixelToInkSpace(
        LONG_PTR hDC,
        LONG *x,
        LONG *y) = 0;

    virtual HRESULT STDMETHODCALLTYPE InkSpaceToPixel(
        LONG_PTR hdcDisplay,
        LONG *x,
        LONG *y) = 0;

    virtual HRESULT STDMETHODCALLTYPE PixelToInkSpaceFromPoints(
        LONG_PTR hDC,
        VARIANT *Points) = 0;

    virtual HRESULT STDMETHODCALLTYPE InkSpaceToPixelFromPoints(
        LONG_PTR hDC,
        VARIANT *Points) = 0;

    virtual HRESULT STDMETHODCALLTYPE Measure(
        IInkStrokes *Strokes,
        IInkRectangle **Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE MeasureStroke(
        IInkStrokeDisp *Stroke,
        IInkDrawingAttributes *DrawingAttributes,
        IInkRectangle **Rectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE Move(
        float HorizontalComponent,
        float VerticalComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE Rotate(
        float Degrees,
        float x = 0,
        float y = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScaleTransform(
        float HorizontalMultiplier,
        float VerticalMultiplier,
        VARIANT_BOOL ApplyOnPenWidth = -1) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkRenderer, 0xe6257a9c, 0xb511, 0x4f4c, 0xa8,0xb0, 0xa7,0xdb,0xc9,0x50,0x6b,0x83)
#endif
#else
typedef struct IInkRendererVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkRenderer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkRenderer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkRenderer *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkRenderer *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkRenderer *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkRenderer *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkRenderer *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkRenderer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetViewTransform)(
        IInkRenderer *This,
        IInkTransform *ViewTransform);

    HRESULT (STDMETHODCALLTYPE *SetViewTransform)(
        IInkRenderer *This,
        IInkTransform *ViewTransform);

    HRESULT (STDMETHODCALLTYPE *GetObjectTransform)(
        IInkRenderer *This,
        IInkTransform *ObjectTransform);

    HRESULT (STDMETHODCALLTYPE *SetObjectTransform)(
        IInkRenderer *This,
        IInkTransform *ObjectTransform);

    HRESULT (STDMETHODCALLTYPE *Draw)(
        IInkRenderer *This,
        LONG_PTR hDC,
        IInkStrokes *Strokes);

    HRESULT (STDMETHODCALLTYPE *DrawStroke)(
        IInkRenderer *This,
        LONG_PTR hDC,
        IInkStrokeDisp *Stroke,
        IInkDrawingAttributes *DrawingAttributes);

    HRESULT (STDMETHODCALLTYPE *PixelToInkSpace)(
        IInkRenderer *This,
        LONG_PTR hDC,
        LONG *x,
        LONG *y);

    HRESULT (STDMETHODCALLTYPE *InkSpaceToPixel)(
        IInkRenderer *This,
        LONG_PTR hdcDisplay,
        LONG *x,
        LONG *y);

    HRESULT (STDMETHODCALLTYPE *PixelToInkSpaceFromPoints)(
        IInkRenderer *This,
        LONG_PTR hDC,
        VARIANT *Points);

    HRESULT (STDMETHODCALLTYPE *InkSpaceToPixelFromPoints)(
        IInkRenderer *This,
        LONG_PTR hDC,
        VARIANT *Points);

    HRESULT (STDMETHODCALLTYPE *Measure)(
        IInkRenderer *This,
        IInkStrokes *Strokes,
        IInkRectangle **Rectangle);

    HRESULT (STDMETHODCALLTYPE *MeasureStroke)(
        IInkRenderer *This,
        IInkStrokeDisp *Stroke,
        IInkDrawingAttributes *DrawingAttributes,
        IInkRectangle **Rectangle);

    HRESULT (STDMETHODCALLTYPE *Move)(
        IInkRenderer *This,
        float HorizontalComponent,
        float VerticalComponent);

    HRESULT (STDMETHODCALLTYPE *Rotate)(
        IInkRenderer *This,
        float Degrees,
        float x,
        float y);

    HRESULT (STDMETHODCALLTYPE *ScaleTransform)(
        IInkRenderer *This,
        float HorizontalMultiplier,
        float VerticalMultiplier,
        VARIANT_BOOL ApplyOnPenWidth);

    END_INTERFACE
} IInkRendererVtbl;

interface IInkRenderer {
    CONST_VTBL IInkRendererVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkRenderer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkRenderer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkRenderer_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkRenderer_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkRenderer_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkRenderer_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkRenderer_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkRenderer methods ***/
#define IInkRenderer_GetViewTransform(This,ViewTransform) (This)->lpVtbl->GetViewTransform(This,ViewTransform)
#define IInkRenderer_SetViewTransform(This,ViewTransform) (This)->lpVtbl->SetViewTransform(This,ViewTransform)
#define IInkRenderer_GetObjectTransform(This,ObjectTransform) (This)->lpVtbl->GetObjectTransform(This,ObjectTransform)
#define IInkRenderer_SetObjectTransform(This,ObjectTransform) (This)->lpVtbl->SetObjectTransform(This,ObjectTransform)
#define IInkRenderer_Draw(This,hDC,Strokes) (This)->lpVtbl->Draw(This,hDC,Strokes)
#define IInkRenderer_DrawStroke(This,hDC,Stroke,DrawingAttributes) (This)->lpVtbl->DrawStroke(This,hDC,Stroke,DrawingAttributes)
#define IInkRenderer_PixelToInkSpace(This,hDC,x,y) (This)->lpVtbl->PixelToInkSpace(This,hDC,x,y)
#define IInkRenderer_InkSpaceToPixel(This,hdcDisplay,x,y) (This)->lpVtbl->InkSpaceToPixel(This,hdcDisplay,x,y)
#define IInkRenderer_PixelToInkSpaceFromPoints(This,hDC,Points) (This)->lpVtbl->PixelToInkSpaceFromPoints(This,hDC,Points)
#define IInkRenderer_InkSpaceToPixelFromPoints(This,hDC,Points) (This)->lpVtbl->InkSpaceToPixelFromPoints(This,hDC,Points)
#define IInkRenderer_Measure(This,Strokes,Rectangle) (This)->lpVtbl->Measure(This,Strokes,Rectangle)
#define IInkRenderer_MeasureStroke(This,Stroke,DrawingAttributes,Rectangle) (This)->lpVtbl->MeasureStroke(This,Stroke,DrawingAttributes,Rectangle)
#define IInkRenderer_Move(This,HorizontalComponent,VerticalComponent) (This)->lpVtbl->Move(This,HorizontalComponent,VerticalComponent)
#define IInkRenderer_Rotate(This,Degrees,x,y) (This)->lpVtbl->Rotate(This,Degrees,x,y)
#define IInkRenderer_ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier,ApplyOnPenWidth) (This)->lpVtbl->ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier,ApplyOnPenWidth)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkRenderer_QueryInterface(IInkRenderer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkRenderer_AddRef(IInkRenderer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkRenderer_Release(IInkRenderer* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkRenderer_GetTypeInfoCount(IInkRenderer* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkRenderer_GetTypeInfo(IInkRenderer* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkRenderer_GetIDsOfNames(IInkRenderer* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkRenderer_Invoke(IInkRenderer* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkRenderer methods ***/
static inline HRESULT IInkRenderer_GetViewTransform(IInkRenderer* This,IInkTransform *ViewTransform) {
    return This->lpVtbl->GetViewTransform(This,ViewTransform);
}
static inline HRESULT IInkRenderer_SetViewTransform(IInkRenderer* This,IInkTransform *ViewTransform) {
    return This->lpVtbl->SetViewTransform(This,ViewTransform);
}
static inline HRESULT IInkRenderer_GetObjectTransform(IInkRenderer* This,IInkTransform *ObjectTransform) {
    return This->lpVtbl->GetObjectTransform(This,ObjectTransform);
}
static inline HRESULT IInkRenderer_SetObjectTransform(IInkRenderer* This,IInkTransform *ObjectTransform) {
    return This->lpVtbl->SetObjectTransform(This,ObjectTransform);
}
static inline HRESULT IInkRenderer_Draw(IInkRenderer* This,LONG_PTR hDC,IInkStrokes *Strokes) {
    return This->lpVtbl->Draw(This,hDC,Strokes);
}
static inline HRESULT IInkRenderer_DrawStroke(IInkRenderer* This,LONG_PTR hDC,IInkStrokeDisp *Stroke,IInkDrawingAttributes *DrawingAttributes) {
    return This->lpVtbl->DrawStroke(This,hDC,Stroke,DrawingAttributes);
}
static inline HRESULT IInkRenderer_PixelToInkSpace(IInkRenderer* This,LONG_PTR hDC,LONG *x,LONG *y) {
    return This->lpVtbl->PixelToInkSpace(This,hDC,x,y);
}
static inline HRESULT IInkRenderer_InkSpaceToPixel(IInkRenderer* This,LONG_PTR hdcDisplay,LONG *x,LONG *y) {
    return This->lpVtbl->InkSpaceToPixel(This,hdcDisplay,x,y);
}
static inline HRESULT IInkRenderer_PixelToInkSpaceFromPoints(IInkRenderer* This,LONG_PTR hDC,VARIANT *Points) {
    return This->lpVtbl->PixelToInkSpaceFromPoints(This,hDC,Points);
}
static inline HRESULT IInkRenderer_InkSpaceToPixelFromPoints(IInkRenderer* This,LONG_PTR hDC,VARIANT *Points) {
    return This->lpVtbl->InkSpaceToPixelFromPoints(This,hDC,Points);
}
static inline HRESULT IInkRenderer_Measure(IInkRenderer* This,IInkStrokes *Strokes,IInkRectangle **Rectangle) {
    return This->lpVtbl->Measure(This,Strokes,Rectangle);
}
static inline HRESULT IInkRenderer_MeasureStroke(IInkRenderer* This,IInkStrokeDisp *Stroke,IInkDrawingAttributes *DrawingAttributes,IInkRectangle **Rectangle) {
    return This->lpVtbl->MeasureStroke(This,Stroke,DrawingAttributes,Rectangle);
}
static inline HRESULT IInkRenderer_Move(IInkRenderer* This,float HorizontalComponent,float VerticalComponent) {
    return This->lpVtbl->Move(This,HorizontalComponent,VerticalComponent);
}
static inline HRESULT IInkRenderer_Rotate(IInkRenderer* This,float Degrees,float x,float y) {
    return This->lpVtbl->Rotate(This,Degrees,x,y);
}
static inline HRESULT IInkRenderer_ScaleTransform(IInkRenderer* This,float HorizontalMultiplier,float VerticalMultiplier,VARIANT_BOOL ApplyOnPenWidth) {
    return This->lpVtbl->ScaleTransform(This,HorizontalMultiplier,VerticalMultiplier,ApplyOnPenWidth);
}
#endif
#endif

#endif


#endif  /* __IInkRenderer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkCursors interface
 */
#ifndef __IInkCursors_INTERFACE_DEFINED__
#define __IInkCursors_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkCursors, 0xa248c1ac, 0xc698, 0x4e06, 0x9e,0x5c, 0xd5,0x7f,0x77,0xc7,0xe6,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a248c1ac-c698-4e06-9e5c-d57f77c7e647")
IInkCursors : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *Count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **_NewEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG Index,
        IInkCursor **Cursor) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkCursors, 0xa248c1ac, 0xc698, 0x4e06, 0x9e,0x5c, 0xd5,0x7f,0x77,0xc7,0xe6,0x47)
#endif
#else
typedef struct IInkCursorsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkCursors *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkCursors *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkCursors *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkCursors *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkCursors *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkCursors *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkCursors *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkCursors methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IInkCursors *This,
        LONG *Count);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IInkCursors *This,
        IUnknown **_NewEnum);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IInkCursors *This,
        LONG Index,
        IInkCursor **Cursor);

    END_INTERFACE
} IInkCursorsVtbl;

interface IInkCursors {
    CONST_VTBL IInkCursorsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkCursors_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkCursors_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkCursors_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkCursors_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkCursors_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkCursors_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkCursors_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkCursors methods ***/
#define IInkCursors_get_Count(This,Count) (This)->lpVtbl->get_Count(This,Count)
#define IInkCursors_get__NewEnum(This,_NewEnum) (This)->lpVtbl->get__NewEnum(This,_NewEnum)
#define IInkCursors_Item(This,Index,Cursor) (This)->lpVtbl->Item(This,Index,Cursor)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkCursors_QueryInterface(IInkCursors* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkCursors_AddRef(IInkCursors* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkCursors_Release(IInkCursors* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkCursors_GetTypeInfoCount(IInkCursors* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkCursors_GetTypeInfo(IInkCursors* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkCursors_GetIDsOfNames(IInkCursors* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkCursors_Invoke(IInkCursors* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkCursors methods ***/
static inline HRESULT IInkCursors_get_Count(IInkCursors* This,LONG *Count) {
    return This->lpVtbl->get_Count(This,Count);
}
static inline HRESULT IInkCursors_get__NewEnum(IInkCursors* This,IUnknown **_NewEnum) {
    return This->lpVtbl->get__NewEnum(This,_NewEnum);
}
static inline HRESULT IInkCursors_Item(IInkCursors* This,LONG Index,IInkCursor **Cursor) {
    return This->lpVtbl->Item(This,Index,Cursor);
}
#endif
#endif

#endif


#endif  /* __IInkCursors_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInkCollector interface
 */
#ifndef __IInkCollector_INTERFACE_DEFINED__
#define __IInkCollector_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInkCollector, 0xf0f060b5, 0x8b1f, 0x4a7c, 0x89,0xec, 0x88,0x06,0x92,0x58,0x8a,0x4f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f0f060b5-8b1f-4a7c-89ec-880692588a4f")
IInkCollector : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_hWnd(
        LONG_PTR *CurrentWindow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_hWnd(
        LONG_PTR NewWindow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *Collecting) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL Collecting) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DefaultDrawingAttributes(
        IInkDrawingAttributes **CurrentAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE putref_DefaultDrawingAttributes(
        IInkDrawingAttributes *NewAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Renderer(
        IInkRenderer **CurrentInkRenderer) = 0;

    virtual HRESULT STDMETHODCALLTYPE putref_Renderer(
        IInkRenderer *NewInkRenderer) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Ink(
        IInkDisp **Ink) = 0;

    virtual HRESULT STDMETHODCALLTYPE putref_Ink(
        IInkDisp *NewInk) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AutoRedraw(
        VARIANT_BOOL *AutoRedraw) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AutoRedraw(
        VARIANT_BOOL AutoRedraw) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CollectingInk(
        VARIANT_BOOL *Collecting) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CollectionMode(
        InkCollectionMode *Mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_CollectionMode(
        InkCollectionMode Mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DynamicRendering(
        VARIANT_BOOL *Enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DynamicRendering(
        VARIANT_BOOL Enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DesiredPacketDescription(
        VARIANT *PacketGuids) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DesiredPacketDescription(
        VARIANT PacketGuids) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MouseIcon(
        IPictureDisp **MouseIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MouseIcon(
        IPictureDisp *MouseIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE putref_MouseIcon(
        IPictureDisp *MouseIcon) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MousePointer(
        InkMousePointer *MousePointer) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MousePointer(
        InkMousePointer MousePointer) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Cursors(
        IInkCursors **Cursors) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MarginX(
        LONG *MarginX) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MarginX(
        LONG MarginX) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MarginY(
        LONG *MarginY) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MarginY(
        LONG MarginY) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Tablet(
        IInkTablet **SingleTablet) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SupportHighContrastInk(
        VARIANT_BOOL *Support) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SupportHighContrastInk(
        VARIANT_BOOL Support) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGestureStatus(
        InkApplicationGesture Gesture,
        VARIANT_BOOL Listen) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGestureStatus(
        InkApplicationGesture Gesture,
        VARIANT_BOOL *Listening) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWindowInputRectangle(
        IInkRectangle **WindowInputRectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetWindowInputRectangle(
        IInkRectangle *WindowInputRectangle) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAllTabletsMode(
        VARIANT_BOOL UseMouseForInput = -1) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSingleTabletIntegratedMode(
        IInkTablet *Tablet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEventInterest(
        InkCollectorEventInterest EventId,
        VARIANT_BOOL *Listen) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEventInterest(
        InkCollectorEventInterest EventId,
        VARIANT_BOOL Listen) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInkCollector, 0xf0f060b5, 0x8b1f, 0x4a7c, 0x89,0xec, 0x88,0x06,0x92,0x58,0x8a,0x4f)
#endif
#else
typedef struct IInkCollectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInkCollector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInkCollector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInkCollector *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInkCollector *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInkCollector *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInkCollector *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInkCollector *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInkCollector methods ***/
    HRESULT (STDMETHODCALLTYPE *get_hWnd)(
        IInkCollector *This,
        LONG_PTR *CurrentWindow);

    HRESULT (STDMETHODCALLTYPE *put_hWnd)(
        IInkCollector *This,
        LONG_PTR NewWindow);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IInkCollector *This,
        VARIANT_BOOL *Collecting);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IInkCollector *This,
        VARIANT_BOOL Collecting);

    HRESULT (STDMETHODCALLTYPE *get_DefaultDrawingAttributes)(
        IInkCollector *This,
        IInkDrawingAttributes **CurrentAttributes);

    HRESULT (STDMETHODCALLTYPE *putref_DefaultDrawingAttributes)(
        IInkCollector *This,
        IInkDrawingAttributes *NewAttributes);

    HRESULT (STDMETHODCALLTYPE *get_Renderer)(
        IInkCollector *This,
        IInkRenderer **CurrentInkRenderer);

    HRESULT (STDMETHODCALLTYPE *putref_Renderer)(
        IInkCollector *This,
        IInkRenderer *NewInkRenderer);

    HRESULT (STDMETHODCALLTYPE *get_Ink)(
        IInkCollector *This,
        IInkDisp **Ink);

    HRESULT (STDMETHODCALLTYPE *putref_Ink)(
        IInkCollector *This,
        IInkDisp *NewInk);

    HRESULT (STDMETHODCALLTYPE *get_AutoRedraw)(
        IInkCollector *This,
        VARIANT_BOOL *AutoRedraw);

    HRESULT (STDMETHODCALLTYPE *put_AutoRedraw)(
        IInkCollector *This,
        VARIANT_BOOL AutoRedraw);

    HRESULT (STDMETHODCALLTYPE *get_CollectingInk)(
        IInkCollector *This,
        VARIANT_BOOL *Collecting);

    HRESULT (STDMETHODCALLTYPE *get_CollectionMode)(
        IInkCollector *This,
        InkCollectionMode *Mode);

    HRESULT (STDMETHODCALLTYPE *put_CollectionMode)(
        IInkCollector *This,
        InkCollectionMode Mode);

    HRESULT (STDMETHODCALLTYPE *get_DynamicRendering)(
        IInkCollector *This,
        VARIANT_BOOL *Enabled);

    HRESULT (STDMETHODCALLTYPE *put_DynamicRendering)(
        IInkCollector *This,
        VARIANT_BOOL Enabled);

    HRESULT (STDMETHODCALLTYPE *get_DesiredPacketDescription)(
        IInkCollector *This,
        VARIANT *PacketGuids);

    HRESULT (STDMETHODCALLTYPE *put_DesiredPacketDescription)(
        IInkCollector *This,
        VARIANT PacketGuids);

    HRESULT (STDMETHODCALLTYPE *get_MouseIcon)(
        IInkCollector *This,
        IPictureDisp **MouseIcon);

    HRESULT (STDMETHODCALLTYPE *put_MouseIcon)(
        IInkCollector *This,
        IPictureDisp *MouseIcon);

    HRESULT (STDMETHODCALLTYPE *putref_MouseIcon)(
        IInkCollector *This,
        IPictureDisp *MouseIcon);

    HRESULT (STDMETHODCALLTYPE *get_MousePointer)(
        IInkCollector *This,
        InkMousePointer *MousePointer);

    HRESULT (STDMETHODCALLTYPE *put_MousePointer)(
        IInkCollector *This,
        InkMousePointer MousePointer);

    HRESULT (STDMETHODCALLTYPE *get_Cursors)(
        IInkCollector *This,
        IInkCursors **Cursors);

    HRESULT (STDMETHODCALLTYPE *get_MarginX)(
        IInkCollector *This,
        LONG *MarginX);

    HRESULT (STDMETHODCALLTYPE *put_MarginX)(
        IInkCollector *This,
        LONG MarginX);

    HRESULT (STDMETHODCALLTYPE *get_MarginY)(
        IInkCollector *This,
        LONG *MarginY);

    HRESULT (STDMETHODCALLTYPE *put_MarginY)(
        IInkCollector *This,
        LONG MarginY);

    HRESULT (STDMETHODCALLTYPE *get_Tablet)(
        IInkCollector *This,
        IInkTablet **SingleTablet);

    HRESULT (STDMETHODCALLTYPE *get_SupportHighContrastInk)(
        IInkCollector *This,
        VARIANT_BOOL *Support);

    HRESULT (STDMETHODCALLTYPE *put_SupportHighContrastInk)(
        IInkCollector *This,
        VARIANT_BOOL Support);

    HRESULT (STDMETHODCALLTYPE *SetGestureStatus)(
        IInkCollector *This,
        InkApplicationGesture Gesture,
        VARIANT_BOOL Listen);

    HRESULT (STDMETHODCALLTYPE *GetGestureStatus)(
        IInkCollector *This,
        InkApplicationGesture Gesture,
        VARIANT_BOOL *Listening);

    HRESULT (STDMETHODCALLTYPE *GetWindowInputRectangle)(
        IInkCollector *This,
        IInkRectangle **WindowInputRectangle);

    HRESULT (STDMETHODCALLTYPE *SetWindowInputRectangle)(
        IInkCollector *This,
        IInkRectangle *WindowInputRectangle);

    HRESULT (STDMETHODCALLTYPE *SetAllTabletsMode)(
        IInkCollector *This,
        VARIANT_BOOL UseMouseForInput);

    HRESULT (STDMETHODCALLTYPE *SetSingleTabletIntegratedMode)(
        IInkCollector *This,
        IInkTablet *Tablet);

    HRESULT (STDMETHODCALLTYPE *GetEventInterest)(
        IInkCollector *This,
        InkCollectorEventInterest EventId,
        VARIANT_BOOL *Listen);

    HRESULT (STDMETHODCALLTYPE *SetEventInterest)(
        IInkCollector *This,
        InkCollectorEventInterest EventId,
        VARIANT_BOOL Listen);

    END_INTERFACE
} IInkCollectorVtbl;

interface IInkCollector {
    CONST_VTBL IInkCollectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInkCollector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInkCollector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInkCollector_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInkCollector_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInkCollector_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInkCollector_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInkCollector_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInkCollector methods ***/
#define IInkCollector_get_hWnd(This,CurrentWindow) (This)->lpVtbl->get_hWnd(This,CurrentWindow)
#define IInkCollector_put_hWnd(This,NewWindow) (This)->lpVtbl->put_hWnd(This,NewWindow)
#define IInkCollector_get_Enabled(This,Collecting) (This)->lpVtbl->get_Enabled(This,Collecting)
#define IInkCollector_put_Enabled(This,Collecting) (This)->lpVtbl->put_Enabled(This,Collecting)
#define IInkCollector_get_DefaultDrawingAttributes(This,CurrentAttributes) (This)->lpVtbl->get_DefaultDrawingAttributes(This,CurrentAttributes)
#define IInkCollector_putref_DefaultDrawingAttributes(This,NewAttributes) (This)->lpVtbl->putref_DefaultDrawingAttributes(This,NewAttributes)
#define IInkCollector_get_Renderer(This,CurrentInkRenderer) (This)->lpVtbl->get_Renderer(This,CurrentInkRenderer)
#define IInkCollector_putref_Renderer(This,NewInkRenderer) (This)->lpVtbl->putref_Renderer(This,NewInkRenderer)
#define IInkCollector_get_Ink(This,Ink) (This)->lpVtbl->get_Ink(This,Ink)
#define IInkCollector_putref_Ink(This,NewInk) (This)->lpVtbl->putref_Ink(This,NewInk)
#define IInkCollector_get_AutoRedraw(This,AutoRedraw) (This)->lpVtbl->get_AutoRedraw(This,AutoRedraw)
#define IInkCollector_put_AutoRedraw(This,AutoRedraw) (This)->lpVtbl->put_AutoRedraw(This,AutoRedraw)
#define IInkCollector_get_CollectingInk(This,Collecting) (This)->lpVtbl->get_CollectingInk(This,Collecting)
#define IInkCollector_get_CollectionMode(This,Mode) (This)->lpVtbl->get_CollectionMode(This,Mode)
#define IInkCollector_put_CollectionMode(This,Mode) (This)->lpVtbl->put_CollectionMode(This,Mode)
#define IInkCollector_get_DynamicRendering(This,Enabled) (This)->lpVtbl->get_DynamicRendering(This,Enabled)
#define IInkCollector_put_DynamicRendering(This,Enabled) (This)->lpVtbl->put_DynamicRendering(This,Enabled)
#define IInkCollector_get_DesiredPacketDescription(This,PacketGuids) (This)->lpVtbl->get_DesiredPacketDescription(This,PacketGuids)
#define IInkCollector_put_DesiredPacketDescription(This,PacketGuids) (This)->lpVtbl->put_DesiredPacketDescription(This,PacketGuids)
#define IInkCollector_get_MouseIcon(This,MouseIcon) (This)->lpVtbl->get_MouseIcon(This,MouseIcon)
#define IInkCollector_put_MouseIcon(This,MouseIcon) (This)->lpVtbl->put_MouseIcon(This,MouseIcon)
#define IInkCollector_putref_MouseIcon(This,MouseIcon) (This)->lpVtbl->putref_MouseIcon(This,MouseIcon)
#define IInkCollector_get_MousePointer(This,MousePointer) (This)->lpVtbl->get_MousePointer(This,MousePointer)
#define IInkCollector_put_MousePointer(This,MousePointer) (This)->lpVtbl->put_MousePointer(This,MousePointer)
#define IInkCollector_get_Cursors(This,Cursors) (This)->lpVtbl->get_Cursors(This,Cursors)
#define IInkCollector_get_MarginX(This,MarginX) (This)->lpVtbl->get_MarginX(This,MarginX)
#define IInkCollector_put_MarginX(This,MarginX) (This)->lpVtbl->put_MarginX(This,MarginX)
#define IInkCollector_get_MarginY(This,MarginY) (This)->lpVtbl->get_MarginY(This,MarginY)
#define IInkCollector_put_MarginY(This,MarginY) (This)->lpVtbl->put_MarginY(This,MarginY)
#define IInkCollector_get_Tablet(This,SingleTablet) (This)->lpVtbl->get_Tablet(This,SingleTablet)
#define IInkCollector_get_SupportHighContrastInk(This,Support) (This)->lpVtbl->get_SupportHighContrastInk(This,Support)
#define IInkCollector_put_SupportHighContrastInk(This,Support) (This)->lpVtbl->put_SupportHighContrastInk(This,Support)
#define IInkCollector_SetGestureStatus(This,Gesture,Listen) (This)->lpVtbl->SetGestureStatus(This,Gesture,Listen)
#define IInkCollector_GetGestureStatus(This,Gesture,Listening) (This)->lpVtbl->GetGestureStatus(This,Gesture,Listening)
#define IInkCollector_GetWindowInputRectangle(This,WindowInputRectangle) (This)->lpVtbl->GetWindowInputRectangle(This,WindowInputRectangle)
#define IInkCollector_SetWindowInputRectangle(This,WindowInputRectangle) (This)->lpVtbl->SetWindowInputRectangle(This,WindowInputRectangle)
#define IInkCollector_SetAllTabletsMode(This,UseMouseForInput) (This)->lpVtbl->SetAllTabletsMode(This,UseMouseForInput)
#define IInkCollector_SetSingleTabletIntegratedMode(This,Tablet) (This)->lpVtbl->SetSingleTabletIntegratedMode(This,Tablet)
#define IInkCollector_GetEventInterest(This,EventId,Listen) (This)->lpVtbl->GetEventInterest(This,EventId,Listen)
#define IInkCollector_SetEventInterest(This,EventId,Listen) (This)->lpVtbl->SetEventInterest(This,EventId,Listen)
#else
/*** IUnknown methods ***/
static inline HRESULT IInkCollector_QueryInterface(IInkCollector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInkCollector_AddRef(IInkCollector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInkCollector_Release(IInkCollector* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInkCollector_GetTypeInfoCount(IInkCollector* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInkCollector_GetTypeInfo(IInkCollector* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInkCollector_GetIDsOfNames(IInkCollector* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInkCollector_Invoke(IInkCollector* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInkCollector methods ***/
static inline HRESULT IInkCollector_get_hWnd(IInkCollector* This,LONG_PTR *CurrentWindow) {
    return This->lpVtbl->get_hWnd(This,CurrentWindow);
}
static inline HRESULT IInkCollector_put_hWnd(IInkCollector* This,LONG_PTR NewWindow) {
    return This->lpVtbl->put_hWnd(This,NewWindow);
}
static inline HRESULT IInkCollector_get_Enabled(IInkCollector* This,VARIANT_BOOL *Collecting) {
    return This->lpVtbl->get_Enabled(This,Collecting);
}
static inline HRESULT IInkCollector_put_Enabled(IInkCollector* This,VARIANT_BOOL Collecting) {
    return This->lpVtbl->put_Enabled(This,Collecting);
}
static inline HRESULT IInkCollector_get_DefaultDrawingAttributes(IInkCollector* This,IInkDrawingAttributes **CurrentAttributes) {
    return This->lpVtbl->get_DefaultDrawingAttributes(This,CurrentAttributes);
}
static inline HRESULT IInkCollector_putref_DefaultDrawingAttributes(IInkCollector* This,IInkDrawingAttributes *NewAttributes) {
    return This->lpVtbl->putref_DefaultDrawingAttributes(This,NewAttributes);
}
static inline HRESULT IInkCollector_get_Renderer(IInkCollector* This,IInkRenderer **CurrentInkRenderer) {
    return This->lpVtbl->get_Renderer(This,CurrentInkRenderer);
}
static inline HRESULT IInkCollector_putref_Renderer(IInkCollector* This,IInkRenderer *NewInkRenderer) {
    return This->lpVtbl->putref_Renderer(This,NewInkRenderer);
}
static inline HRESULT IInkCollector_get_Ink(IInkCollector* This,IInkDisp **Ink) {
    return This->lpVtbl->get_Ink(This,Ink);
}
static inline HRESULT IInkCollector_putref_Ink(IInkCollector* This,IInkDisp *NewInk) {
    return This->lpVtbl->putref_Ink(This,NewInk);
}
static inline HRESULT IInkCollector_get_AutoRedraw(IInkCollector* This,VARIANT_BOOL *AutoRedraw) {
    return This->lpVtbl->get_AutoRedraw(This,AutoRedraw);
}
static inline HRESULT IInkCollector_put_AutoRedraw(IInkCollector* This,VARIANT_BOOL AutoRedraw) {
    return This->lpVtbl->put_AutoRedraw(This,AutoRedraw);
}
static inline HRESULT IInkCollector_get_CollectingInk(IInkCollector* This,VARIANT_BOOL *Collecting) {
    return This->lpVtbl->get_CollectingInk(This,Collecting);
}
static inline HRESULT IInkCollector_get_CollectionMode(IInkCollector* This,InkCollectionMode *Mode) {
    return This->lpVtbl->get_CollectionMode(This,Mode);
}
static inline HRESULT IInkCollector_put_CollectionMode(IInkCollector* This,InkCollectionMode Mode) {
    return This->lpVtbl->put_CollectionMode(This,Mode);
}
static inline HRESULT IInkCollector_get_DynamicRendering(IInkCollector* This,VARIANT_BOOL *Enabled) {
    return This->lpVtbl->get_DynamicRendering(This,Enabled);
}
static inline HRESULT IInkCollector_put_DynamicRendering(IInkCollector* This,VARIANT_BOOL Enabled) {
    return This->lpVtbl->put_DynamicRendering(This,Enabled);
}
static inline HRESULT IInkCollector_get_DesiredPacketDescription(IInkCollector* This,VARIANT *PacketGuids) {
    return This->lpVtbl->get_DesiredPacketDescription(This,PacketGuids);
}
static inline HRESULT IInkCollector_put_DesiredPacketDescription(IInkCollector* This,VARIANT PacketGuids) {
    return This->lpVtbl->put_DesiredPacketDescription(This,PacketGuids);
}
static inline HRESULT IInkCollector_get_MouseIcon(IInkCollector* This,IPictureDisp **MouseIcon) {
    return This->lpVtbl->get_MouseIcon(This,MouseIcon);
}
static inline HRESULT IInkCollector_put_MouseIcon(IInkCollector* This,IPictureDisp *MouseIcon) {
    return This->lpVtbl->put_MouseIcon(This,MouseIcon);
}
static inline HRESULT IInkCollector_putref_MouseIcon(IInkCollector* This,IPictureDisp *MouseIcon) {
    return This->lpVtbl->putref_MouseIcon(This,MouseIcon);
}
static inline HRESULT IInkCollector_get_MousePointer(IInkCollector* This,InkMousePointer *MousePointer) {
    return This->lpVtbl->get_MousePointer(This,MousePointer);
}
static inline HRESULT IInkCollector_put_MousePointer(IInkCollector* This,InkMousePointer MousePointer) {
    return This->lpVtbl->put_MousePointer(This,MousePointer);
}
static inline HRESULT IInkCollector_get_Cursors(IInkCollector* This,IInkCursors **Cursors) {
    return This->lpVtbl->get_Cursors(This,Cursors);
}
static inline HRESULT IInkCollector_get_MarginX(IInkCollector* This,LONG *MarginX) {
    return This->lpVtbl->get_MarginX(This,MarginX);
}
static inline HRESULT IInkCollector_put_MarginX(IInkCollector* This,LONG MarginX) {
    return This->lpVtbl->put_MarginX(This,MarginX);
}
static inline HRESULT IInkCollector_get_MarginY(IInkCollector* This,LONG *MarginY) {
    return This->lpVtbl->get_MarginY(This,MarginY);
}
static inline HRESULT IInkCollector_put_MarginY(IInkCollector* This,LONG MarginY) {
    return This->lpVtbl->put_MarginY(This,MarginY);
}
static inline HRESULT IInkCollector_get_Tablet(IInkCollector* This,IInkTablet **SingleTablet) {
    return This->lpVtbl->get_Tablet(This,SingleTablet);
}
static inline HRESULT IInkCollector_get_SupportHighContrastInk(IInkCollector* This,VARIANT_BOOL *Support) {
    return This->lpVtbl->get_SupportHighContrastInk(This,Support);
}
static inline HRESULT IInkCollector_put_SupportHighContrastInk(IInkCollector* This,VARIANT_BOOL Support) {
    return This->lpVtbl->put_SupportHighContrastInk(This,Support);
}
static inline HRESULT IInkCollector_SetGestureStatus(IInkCollector* This,InkApplicationGesture Gesture,VARIANT_BOOL Listen) {
    return This->lpVtbl->SetGestureStatus(This,Gesture,Listen);
}
static inline HRESULT IInkCollector_GetGestureStatus(IInkCollector* This,InkApplicationGesture Gesture,VARIANT_BOOL *Listening) {
    return This->lpVtbl->GetGestureStatus(This,Gesture,Listening);
}
static inline HRESULT IInkCollector_GetWindowInputRectangle(IInkCollector* This,IInkRectangle **WindowInputRectangle) {
    return This->lpVtbl->GetWindowInputRectangle(This,WindowInputRectangle);
}
static inline HRESULT IInkCollector_SetWindowInputRectangle(IInkCollector* This,IInkRectangle *WindowInputRectangle) {
    return This->lpVtbl->SetWindowInputRectangle(This,WindowInputRectangle);
}
static inline HRESULT IInkCollector_SetAllTabletsMode(IInkCollector* This,VARIANT_BOOL UseMouseForInput) {
    return This->lpVtbl->SetAllTabletsMode(This,UseMouseForInput);
}
static inline HRESULT IInkCollector_SetSingleTabletIntegratedMode(IInkCollector* This,IInkTablet *Tablet) {
    return This->lpVtbl->SetSingleTabletIntegratedMode(This,Tablet);
}
static inline HRESULT IInkCollector_GetEventInterest(IInkCollector* This,InkCollectorEventInterest EventId,VARIANT_BOOL *Listen) {
    return This->lpVtbl->GetEventInterest(This,EventId,Listen);
}
static inline HRESULT IInkCollector_SetEventInterest(IInkCollector* This,InkCollectorEventInterest EventId,VARIANT_BOOL Listen) {
    return This->lpVtbl->SetEventInterest(This,EventId,Listen);
}
#endif
#endif

#endif


#endif  /* __IInkCollector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * _IInkCollectorEvents dispinterface
 */
#ifndef ___IInkCollectorEvents_DISPINTERFACE_DEFINED__
#define ___IInkCollectorEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID__IInkCollectorEvents, 0x11a583f2, 0x712d, 0x4fea, 0xab,0xcf, 0xab,0x4a,0xf3,0x8e,0xa0,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("11a583f2-712d-4fea-abcf-ab4af38ea06b")
_IInkCollectorEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(_IInkCollectorEvents, 0x11a583f2, 0x712d, 0x4fea, 0xab,0xcf, 0xab,0x4a,0xf3,0x8e,0xa0,0x6b)
#endif
#else
typedef struct _IInkCollectorEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        _IInkCollectorEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        _IInkCollectorEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        _IInkCollectorEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        _IInkCollectorEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        _IInkCollectorEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        _IInkCollectorEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        _IInkCollectorEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} _IInkCollectorEventsVtbl;

interface _IInkCollectorEvents {
    CONST_VTBL _IInkCollectorEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define _IInkCollectorEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define _IInkCollectorEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define _IInkCollectorEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define _IInkCollectorEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define _IInkCollectorEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define _IInkCollectorEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define _IInkCollectorEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT _IInkCollectorEvents_QueryInterface(_IInkCollectorEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG _IInkCollectorEvents_AddRef(_IInkCollectorEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG _IInkCollectorEvents_Release(_IInkCollectorEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT _IInkCollectorEvents_GetTypeInfoCount(_IInkCollectorEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT _IInkCollectorEvents_GetTypeInfo(_IInkCollectorEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT _IInkCollectorEvents_GetIDsOfNames(_IInkCollectorEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT _IInkCollectorEvents_Invoke(_IInkCollectorEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* ___IInkCollectorEvents_DISPINTERFACE_DEFINED__ */

/*****************************************************************************
 * InkCollector coclass
 */

DEFINE_GUID(CLSID_InkCollector, 0x43fb1553, 0xad74, 0x4ee8, 0x88,0xe4, 0x3e,0x6d,0xaa,0xc9,0x15,0xdb);

#ifdef __cplusplus
class DECLSPEC_UUID("43fb1553-ad74-4ee8-88e4-3e6daac915db") InkCollector;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(InkCollector, 0x43fb1553, 0xad74, 0x4ee8, 0x88,0xe4, 0x3e,0x6d,0xaa,0xc9,0x15,0xdb)
#endif
#endif

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __msinkaut_h__ */
