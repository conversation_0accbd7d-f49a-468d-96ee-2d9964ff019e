/*** Autogenerated by WIDL 10.8 from include/windows.security.credentials.ui.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_security_credentials_ui_h__
#define __windows_security_credentials_ui_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics ABI::Windows::Security::Credentials::UI::IUserConsentVerifierStatics
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                namespace UI {
                    interface IUserConsentVerifierStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifier_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifier_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                namespace UI {
                    class UserConsentVerifier;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifier __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifier;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifier_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_UserConsentVerificationResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_UserConsentVerificationResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_UserConsentVerificationResult __FIAsyncOperation_1_UserConsentVerificationResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_UserConsentVerificationResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_UserConsentVerifierAvailability_FWD_DEFINED__
#define ____FIAsyncOperation_1_UserConsentVerifierAvailability_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_UserConsentVerifierAvailability __FIAsyncOperation_1_UserConsentVerifierAvailability;
#ifdef __cplusplus
#define __FIAsyncOperation_1_UserConsentVerifierAvailability ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerificationResult __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerificationResult;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifierAvailability __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifierAvailability;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics ABI::Windows::Security::Credentials::UI::IUserConsentVerifierStatics
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                namespace UI {
                    interface IUserConsentVerifierStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_UserConsentVerificationResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_UserConsentVerificationResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_UserConsentVerificationResult __FIAsyncOperation_1_UserConsentVerificationResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_UserConsentVerificationResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_UserConsentVerifierAvailability_FWD_DEFINED__
#define ____FIAsyncOperation_1_UserConsentVerifierAvailability_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_UserConsentVerifierAvailability __FIAsyncOperation_1_UserConsentVerifierAvailability;
#ifdef __cplusplus
#define __FIAsyncOperation_1_UserConsentVerifierAvailability ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                namespace UI {
                    enum UserConsentVerificationResult {
                        UserConsentVerificationResult_Verified = 0,
                        UserConsentVerificationResult_DeviceNotPresent = 1,
                        UserConsentVerificationResult_NotConfiguredForUser = 2,
                        UserConsentVerificationResult_DisabledByPolicy = 3,
                        UserConsentVerificationResult_DeviceBusy = 4,
                        UserConsentVerificationResult_RetriesExhausted = 5,
                        UserConsentVerificationResult_Canceled = 6
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerificationResult {
    UserConsentVerificationResult_Verified = 0,
    UserConsentVerificationResult_DeviceNotPresent = 1,
    UserConsentVerificationResult_NotConfiguredForUser = 2,
    UserConsentVerificationResult_DisabledByPolicy = 3,
    UserConsentVerificationResult_DeviceBusy = 4,
    UserConsentVerificationResult_RetriesExhausted = 5,
    UserConsentVerificationResult_Canceled = 6
};
#ifdef WIDL_using_Windows_Security_Credentials_UI
#define UserConsentVerificationResult __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerificationResult
#endif /* WIDL_using_Windows_Security_Credentials_UI */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                namespace UI {
                    enum UserConsentVerifierAvailability {
                        UserConsentVerifierAvailability_Available = 0,
                        UserConsentVerifierAvailability_DeviceNotPresent = 1,
                        UserConsentVerifierAvailability_NotConfiguredForUser = 2,
                        UserConsentVerifierAvailability_DisabledByPolicy = 3,
                        UserConsentVerifierAvailability_DeviceBusy = 4
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifierAvailability {
    UserConsentVerifierAvailability_Available = 0,
    UserConsentVerifierAvailability_DeviceNotPresent = 1,
    UserConsentVerifierAvailability_NotConfiguredForUser = 2,
    UserConsentVerifierAvailability_DisabledByPolicy = 3,
    UserConsentVerifierAvailability_DeviceBusy = 4
};
#ifdef WIDL_using_Windows_Security_Credentials_UI
#define UserConsentVerifierAvailability __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifierAvailability
#endif /* WIDL_using_Windows_Security_Credentials_UI */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IUserConsentVerifierStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics, 0xaf4f3f91, 0x564c, 0x4ddc, 0xb8,0xb5, 0x97,0x34,0x47,0x62,0x7c,0x65);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                namespace UI {
                    MIDL_INTERFACE("af4f3f91-564c-4ddc-b8b5-973447627c65")
                    IUserConsentVerifierStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE CheckAvailabilityAsync(
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > **result) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RequestVerificationAsync(
                            HSTRING message,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > **result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics, 0xaf4f3f91, 0x564c, 0x4ddc, 0xb8,0xb5, 0x97,0x34,0x47,0x62,0x7c,0x65)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics *This,
        TrustLevel *trustLevel);

    /*** IUserConsentVerifierStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckAvailabilityAsync)(
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics *This,
        __FIAsyncOperation_1_UserConsentVerifierAvailability **result);

    HRESULT (STDMETHODCALLTYPE *RequestVerificationAsync)(
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics *This,
        HSTRING message,
        __FIAsyncOperation_1_UserConsentVerificationResult **result);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStaticsVtbl;

interface __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUserConsentVerifierStatics methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_CheckAvailabilityAsync(This,result) (This)->lpVtbl->CheckAvailabilityAsync(This,result)
#define __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_RequestVerificationAsync(This,message,result) (This)->lpVtbl->RequestVerificationAsync(This,message,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_QueryInterface(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_AddRef(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_Release(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetIids(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetTrustLevel(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUserConsentVerifierStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_CheckAvailabilityAsync(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics* This,__FIAsyncOperation_1_UserConsentVerifierAvailability **result) {
    return This->lpVtbl->CheckAvailabilityAsync(This,result);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_RequestVerificationAsync(__x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics* This,HSTRING message,__FIAsyncOperation_1_UserConsentVerificationResult **result) {
    return This->lpVtbl->RequestVerificationAsync(This,message,result);
}
#endif
#ifdef WIDL_using_Windows_Security_Credentials_UI
#define IID_IUserConsentVerifierStatics IID___x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics
#define IUserConsentVerifierStaticsVtbl __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStaticsVtbl
#define IUserConsentVerifierStatics __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics
#define IUserConsentVerifierStatics_QueryInterface __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_QueryInterface
#define IUserConsentVerifierStatics_AddRef __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_AddRef
#define IUserConsentVerifierStatics_Release __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_Release
#define IUserConsentVerifierStatics_GetIids __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetIids
#define IUserConsentVerifierStatics_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetRuntimeClassName
#define IUserConsentVerifierStatics_GetTrustLevel __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_GetTrustLevel
#define IUserConsentVerifierStatics_CheckAvailabilityAsync __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_CheckAvailabilityAsync
#define IUserConsentVerifierStatics_RequestVerificationAsync __x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_RequestVerificationAsync
#endif /* WIDL_using_Windows_Security_Credentials_UI */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CCredentials_CUI_CIUserConsentVerifierStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Security.Credentials.UI.UserConsentVerifier
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Security_Credentials_UI_UserConsentVerifier_DEFINED
#define RUNTIMECLASS_Windows_Security_Credentials_UI_UserConsentVerifier_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Credentials_UI_UserConsentVerifier[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','C','r','e','d','e','n','t','i','a','l','s','.','U','I','.','U','s','e','r','C','o','n','s','e','n','t','V','e','r','i','f','i','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Credentials_UI_UserConsentVerifier[] = L"Windows.Security.Credentials.UI.UserConsentVerifier";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Credentials_UI_UserConsentVerifier[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','C','r','e','d','e','n','t','i','a','l','s','.','U','I','.','U','s','e','r','C','o','n','s','e','n','t','V','e','r','i','f','i','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Credentials_UI_UserConsentVerifier_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult, 0x0cffc6c9, 0x4c2b, 0x5cd4, 0xb3,0x8c, 0x7b,0x8d,0xf3,0xff,0x5a,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("0cffc6c9-4c2b-5cd4-b38c-7b8df3ff5afb")
            IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult, 0x0cffc6c9, 0x4c2b, 0x5cd4, 0xb3,0x8c, 0x7b,0x8d,0xf3,0xff,0x5a,0xfb)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult *This,
        __FIAsyncOperation_1_UserConsentVerificationResult *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResultVtbl;

interface __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > methods ***/
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_QueryInterface(__FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_AddRef(__FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_Release(__FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_Invoke(__FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult* This,__FIAsyncOperation_1_UserConsentVerificationResult *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_UserConsentVerificationResult IID___FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult
#define IAsyncOperationCompletedHandler_UserConsentVerificationResultVtbl __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResultVtbl
#define IAsyncOperationCompletedHandler_UserConsentVerificationResult __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult
#define IAsyncOperationCompletedHandler_UserConsentVerificationResult_QueryInterface __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_QueryInterface
#define IAsyncOperationCompletedHandler_UserConsentVerificationResult_AddRef __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_AddRef
#define IAsyncOperationCompletedHandler_UserConsentVerificationResult_Release __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_Release
#define IAsyncOperationCompletedHandler_UserConsentVerificationResult_Invoke __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability, 0x28988174, 0xace2, 0x5c15, 0xa0,0xdf, 0x58,0x0a,0x26,0xd9,0x42,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("28988174-ace2-5c15-a0df-580a26d94294")
            IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability, 0x28988174, 0xace2, 0x5c15, 0xa0,0xdf, 0x58,0x0a,0x26,0xd9,0x42,0x94)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailabilityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability *This,
        __FIAsyncOperation_1_UserConsentVerifierAvailability *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailabilityVtbl;

interface __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailabilityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > methods ***/
#define __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_QueryInterface(__FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_AddRef(__FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_Release(__FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_Invoke(__FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability* This,__FIAsyncOperation_1_UserConsentVerifierAvailability *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_UserConsentVerifierAvailability IID___FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability
#define IAsyncOperationCompletedHandler_UserConsentVerifierAvailabilityVtbl __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailabilityVtbl
#define IAsyncOperationCompletedHandler_UserConsentVerifierAvailability __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability
#define IAsyncOperationCompletedHandler_UserConsentVerifierAvailability_QueryInterface __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_QueryInterface
#define IAsyncOperationCompletedHandler_UserConsentVerifierAvailability_AddRef __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_AddRef
#define IAsyncOperationCompletedHandler_UserConsentVerifierAvailability_Release __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_Release
#define IAsyncOperationCompletedHandler_UserConsentVerifierAvailability_Invoke __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > interface
 */
#ifndef ____FIAsyncOperation_1_UserConsentVerificationResult_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_UserConsentVerificationResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_UserConsentVerificationResult, 0xfd596ffd, 0x2318, 0x558f, 0x9d,0xbe, 0xd2,0x1d,0xf4,0x37,0x64,0xa5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("fd596ffd-2318-558f-9dbe-d21df43764a5")
            IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > : IAsyncOperation_impl<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_UserConsentVerificationResult, 0xfd596ffd, 0x2318, 0x558f, 0x9d,0xbe, 0xd2,0x1d,0xf4,0x37,0x64,0xa5)
#endif
#else
typedef struct __FIAsyncOperation_1_UserConsentVerificationResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This,
        __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This,
        __FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_UserConsentVerificationResult *This,
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerificationResult *results);

    END_INTERFACE
} __FIAsyncOperation_1_UserConsentVerificationResultVtbl;

interface __FIAsyncOperation_1_UserConsentVerificationResult {
    CONST_VTBL __FIAsyncOperation_1_UserConsentVerificationResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_UserConsentVerificationResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_UserConsentVerificationResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_UserConsentVerificationResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_UserConsentVerificationResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_UserConsentVerificationResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_UserConsentVerificationResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > methods ***/
#define __FIAsyncOperation_1_UserConsentVerificationResult_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_UserConsentVerificationResult_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_UserConsentVerificationResult_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_UserConsentVerificationResult_QueryInterface(__FIAsyncOperation_1_UserConsentVerificationResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_UserConsentVerificationResult_AddRef(__FIAsyncOperation_1_UserConsentVerificationResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_UserConsentVerificationResult_Release(__FIAsyncOperation_1_UserConsentVerificationResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_UserConsentVerificationResult_GetIids(__FIAsyncOperation_1_UserConsentVerificationResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_UserConsentVerificationResult_GetRuntimeClassName(__FIAsyncOperation_1_UserConsentVerificationResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_UserConsentVerificationResult_GetTrustLevel(__FIAsyncOperation_1_UserConsentVerificationResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerificationResult > methods ***/
static inline HRESULT __FIAsyncOperation_1_UserConsentVerificationResult_put_Completed(__FIAsyncOperation_1_UserConsentVerificationResult* This,__FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_UserConsentVerificationResult_get_Completed(__FIAsyncOperation_1_UserConsentVerificationResult* This,__FIAsyncOperationCompletedHandler_1_UserConsentVerificationResult **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_UserConsentVerificationResult_GetResults(__FIAsyncOperation_1_UserConsentVerificationResult* This,__x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerificationResult *results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_UserConsentVerificationResult IID___FIAsyncOperation_1_UserConsentVerificationResult
#define IAsyncOperation_UserConsentVerificationResultVtbl __FIAsyncOperation_1_UserConsentVerificationResultVtbl
#define IAsyncOperation_UserConsentVerificationResult __FIAsyncOperation_1_UserConsentVerificationResult
#define IAsyncOperation_UserConsentVerificationResult_QueryInterface __FIAsyncOperation_1_UserConsentVerificationResult_QueryInterface
#define IAsyncOperation_UserConsentVerificationResult_AddRef __FIAsyncOperation_1_UserConsentVerificationResult_AddRef
#define IAsyncOperation_UserConsentVerificationResult_Release __FIAsyncOperation_1_UserConsentVerificationResult_Release
#define IAsyncOperation_UserConsentVerificationResult_GetIids __FIAsyncOperation_1_UserConsentVerificationResult_GetIids
#define IAsyncOperation_UserConsentVerificationResult_GetRuntimeClassName __FIAsyncOperation_1_UserConsentVerificationResult_GetRuntimeClassName
#define IAsyncOperation_UserConsentVerificationResult_GetTrustLevel __FIAsyncOperation_1_UserConsentVerificationResult_GetTrustLevel
#define IAsyncOperation_UserConsentVerificationResult_put_Completed __FIAsyncOperation_1_UserConsentVerificationResult_put_Completed
#define IAsyncOperation_UserConsentVerificationResult_get_Completed __FIAsyncOperation_1_UserConsentVerificationResult_get_Completed
#define IAsyncOperation_UserConsentVerificationResult_GetResults __FIAsyncOperation_1_UserConsentVerificationResult_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_UserConsentVerificationResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > interface
 */
#ifndef ____FIAsyncOperation_1_UserConsentVerifierAvailability_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_UserConsentVerifierAvailability_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_UserConsentVerifierAvailability, 0xddd384f3, 0xd818, 0x5d83, 0xab,0x4b, 0x32,0x11,0x9c,0x28,0x58,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("ddd384f3-d818-5d83-ab4b-32119c28587c")
            IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > : IAsyncOperation_impl<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_UserConsentVerifierAvailability, 0xddd384f3, 0xd818, 0x5d83, 0xab,0x4b, 0x32,0x11,0x9c,0x28,0x58,0x7c)
#endif
#else
typedef struct __FIAsyncOperation_1_UserConsentVerifierAvailabilityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This,
        __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This,
        __FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_UserConsentVerifierAvailability *This,
        __x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifierAvailability *results);

    END_INTERFACE
} __FIAsyncOperation_1_UserConsentVerifierAvailabilityVtbl;

interface __FIAsyncOperation_1_UserConsentVerifierAvailability {
    CONST_VTBL __FIAsyncOperation_1_UserConsentVerifierAvailabilityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > methods ***/
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_UserConsentVerifierAvailability_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_UserConsentVerifierAvailability_QueryInterface(__FIAsyncOperation_1_UserConsentVerifierAvailability* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_UserConsentVerifierAvailability_AddRef(__FIAsyncOperation_1_UserConsentVerifierAvailability* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_UserConsentVerifierAvailability_Release(__FIAsyncOperation_1_UserConsentVerifierAvailability* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_UserConsentVerifierAvailability_GetIids(__FIAsyncOperation_1_UserConsentVerifierAvailability* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_UserConsentVerifierAvailability_GetRuntimeClassName(__FIAsyncOperation_1_UserConsentVerifierAvailability* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_UserConsentVerifierAvailability_GetTrustLevel(__FIAsyncOperation_1_UserConsentVerifierAvailability* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Security::Credentials::UI::UserConsentVerifierAvailability > methods ***/
static inline HRESULT __FIAsyncOperation_1_UserConsentVerifierAvailability_put_Completed(__FIAsyncOperation_1_UserConsentVerifierAvailability* This,__FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_UserConsentVerifierAvailability_get_Completed(__FIAsyncOperation_1_UserConsentVerifierAvailability* This,__FIAsyncOperationCompletedHandler_1_UserConsentVerifierAvailability **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_UserConsentVerifierAvailability_GetResults(__FIAsyncOperation_1_UserConsentVerifierAvailability* This,__x_ABI_CWindows_CSecurity_CCredentials_CUI_CUserConsentVerifierAvailability *results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_UserConsentVerifierAvailability IID___FIAsyncOperation_1_UserConsentVerifierAvailability
#define IAsyncOperation_UserConsentVerifierAvailabilityVtbl __FIAsyncOperation_1_UserConsentVerifierAvailabilityVtbl
#define IAsyncOperation_UserConsentVerifierAvailability __FIAsyncOperation_1_UserConsentVerifierAvailability
#define IAsyncOperation_UserConsentVerifierAvailability_QueryInterface __FIAsyncOperation_1_UserConsentVerifierAvailability_QueryInterface
#define IAsyncOperation_UserConsentVerifierAvailability_AddRef __FIAsyncOperation_1_UserConsentVerifierAvailability_AddRef
#define IAsyncOperation_UserConsentVerifierAvailability_Release __FIAsyncOperation_1_UserConsentVerifierAvailability_Release
#define IAsyncOperation_UserConsentVerifierAvailability_GetIids __FIAsyncOperation_1_UserConsentVerifierAvailability_GetIids
#define IAsyncOperation_UserConsentVerifierAvailability_GetRuntimeClassName __FIAsyncOperation_1_UserConsentVerifierAvailability_GetRuntimeClassName
#define IAsyncOperation_UserConsentVerifierAvailability_GetTrustLevel __FIAsyncOperation_1_UserConsentVerifierAvailability_GetTrustLevel
#define IAsyncOperation_UserConsentVerifierAvailability_put_Completed __FIAsyncOperation_1_UserConsentVerifierAvailability_put_Completed
#define IAsyncOperation_UserConsentVerifierAvailability_get_Completed __FIAsyncOperation_1_UserConsentVerifierAvailability_get_Completed
#define IAsyncOperation_UserConsentVerifierAvailability_GetResults __FIAsyncOperation_1_UserConsentVerifierAvailability_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_UserConsentVerifierAvailability_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_security_credentials_ui_h__ */
