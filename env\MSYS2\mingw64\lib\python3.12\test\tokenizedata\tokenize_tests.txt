# Tests for the 'tokenize' module.
# Large bits stolen from test_grammar.py.

# Comments
"#"
#'
#"
#\
       #
    # abc
'''#
#'''

x = 1  #

# Balancing continuation

a = (3, 4,
  5, 6)
y = [3, 4,
  5]
z = {'a':5,
  'b':6}
x = (len(repr(y)) + 5*x - a[
   3 ]
   - x + len({
   }
    )
  )

# Backslash means line continuation:
x = 1 \
+ 1

# Backslash does not means continuation in comments :\
x = 0

# Ordinary integers
0xff != 255
0o377 != 255
2147483647   != 0o17777777777
-2147483647-1 != 0o20000000000
0o37777777777 != -1
0xffffffff != -1; 0o37777777777 != -1; -0o1234567 == 0O001234567; 0b10101 == 0B00010101

# Long integers
x = 0
x = 0
x = 0xffffffffffffffff
x = 0xffffffffffffffff
x = 0o77777777777777777
x = 0B11101010111111111
x = 123456789012345678901234567890
x = 123456789012345678901234567890

# Floating-point numbers
x = 3.14
x = 314.
x = 0.314
# XXX x = 000.314
x = .314
x = 3e14
x = 3E14
x = 3e-14
x = 3e+14
x = 3.e14
x = .3e14
x = 3.1e4

# String literals
x = ''; y = "";
x = '\''; y = "'";
x = '"'; y = "\"";
x = "doesn't \"shrink\" does it"
y = 'doesn\'t "shrink" does it'
x = "does \"shrink\" doesn't it"
y = 'does "shrink" doesn\'t it'
x = """
The "quick"
brown fox
jumps over
the 'lazy' dog.
"""
y = '\nThe "quick"\nbrown fox\njumps over\nthe \'lazy\' dog.\n'
y = '''
The "quick"
brown fox
jumps over
the 'lazy' dog.
''';
y = "\n\
The \"quick\"\n\
brown fox\n\
jumps over\n\
the 'lazy' dog.\n\
";
y = '\n\
The \"quick\"\n\
brown fox\n\
jumps over\n\
the \'lazy\' dog.\n\
';
x = r'\\' + R'\\'
x = r'\'' + ''
y = r'''
foo bar \\
baz''' + R'''
foo'''
y = r"""foo
bar \\ baz
""" + R'''spam
'''
x = b'abc' + B'ABC'
y = b"abc" + B"ABC"
x = br'abc' + Br'ABC' + bR'ABC' + BR'ABC'
y = br"abc" + Br"ABC" + bR"ABC" + BR"ABC"
x = rb'abc' + rB'ABC' + Rb'ABC' + RB'ABC'
y = rb"abc" + rB"ABC" + Rb"ABC" + RB"ABC"
x = br'\\' + BR'\\'
x = rb'\\' + RB'\\'
x = br'\'' + ''
x = rb'\'' + ''
y = br'''
foo bar \\
baz''' + BR'''
foo'''
y = Br"""foo
bar \\ baz
""" + bR'''spam
'''
y = rB"""foo
bar \\ baz
""" + Rb'''spam
'''

# Indentation
if 1:
    x = 2
if 1:
        x = 2
if 1:
    while 0:
     if 0:
           x = 2
     x = 2
if 0:
  if 2:
   while 0:
        if 1:
          x = 2

# Operators

def d22(a, b, c=1, d=2): pass
def d01v(a=1, *restt, **restd): pass

(x, y) != ({'a':1}, {'b':2})

# comparison
if 1 < 1 > 1 == 1 >= 1 <= 1 != 1 != 1 in 1 not in 1 is 1 is not 1: pass

# binary
x = 1 & 1
x = 1 ^ 1
x = 1 | 1

# shift
x = 1 << 1 >> 1

# additive
x = 1 - 1 + 1 - 1 + 1

# multiplicative
x = 1 / 1 * 1 % 1

# unary
x = ~1 ^ 1 & 1 | 1 & 1 ^ -1
x = -1*1/1 + 1*1 - ---1*1

# selector
import sys, time
x = sys.modules['time'].time()

@staticmethod
def foo(): pass

@staticmethod
def foo(x:1)->1: pass

