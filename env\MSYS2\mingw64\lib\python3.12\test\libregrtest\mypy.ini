# Config file for running mypy on libregrtest.
# Run mypy by invoking `mypy --config-file Lib/test/libregrtest/mypy.ini`
# on the command-line from the repo root

[mypy]
files = Lib/test/libregrtest
explicit_package_bases = True
python_version = 3.12
platform = linux
pretty = True

# Enable most stricter settings
enable_error_code = ignore-without-code
strict = True

# Various stricter settings that we can't yet enable
# Try to enable these in the following order:
disallow_any_generics = False
disallow_incomplete_defs = False
disallow_untyped_calls = False
disallow_untyped_defs = False
check_untyped_defs = False
warn_return_any = False

disable_error_code = return

# Enable --strict-optional for these ASAP:
[mypy-Lib.test.libregrtest.main.*,Lib.test.libregrtest.run_workers.*]
strict_optional = False

# Various internal modules that typeshed deliberately doesn't have stubs for:
[mypy-_abc.*,_opcode.*,_overlapped.*,_testcapi.*,_testinternalcapi.*,test.*]
ignore_missing_imports = True
