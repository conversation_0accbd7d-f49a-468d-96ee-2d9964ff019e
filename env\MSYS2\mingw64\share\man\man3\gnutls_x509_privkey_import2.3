.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_privkey_import2" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_privkey_import2 \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_privkey_import2(gnutls_x509_privkey_t " key ", const gnutls_datum_t * " data ", gnutls_x509_crt_fmt_t " format ", const char * " password ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_privkey_t key" 12
The data to store the parsed key
.IP "const gnutls_datum_t * data" 12
The DER or PEM encoded key.
.IP "gnutls_x509_crt_fmt_t format" 12
One of DER or PEM
.IP "const char * password" 12
A password (optional)
.IP "unsigned int flags" 12
an ORed sequence of gnutls_pkcs_encrypt_flags_t
.SH "DESCRIPTION"
This function will import the given DER or PEM encoded key, to 
the native \fBgnutls_x509_privkey_t\fP format, irrespective of the
input format. The input format is auto\-detected.

The supported formats are basic unencrypted key, PKCS8, PKCS12,
and the openssl format.

If the provided key is encrypted but no password was given, then
\fBGNUTLS_E_DECRYPTION_FAILED\fP is returned. Since GnuTLS 3.4.0 this
function will utilize the PIN callbacks if any.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
