/* Autogenerated by mlir-tblgen; don't manually edit */

#ifdef GEN_PASS_DECL
// Generate declarations for all passes.
#define GEN_PASS_DECL_ANNOTATEPARAMETERREPLICATIONPASS
#define GEN_PASS_DECL_DECOMPOSERESOURCEOPSINCLUSTERPASS
#define GEN_PASS_DECL_DECOMPOSERESOURCEOPSPASS
#define GEN_PASS_DECL_DEVICEATTRIBUTETOLAUNCHPASS
#define GEN_PASS_DECL_LAUNCHTODEVICEATTRIBUTEPASS
#define GEN_PASS_DECL_RESOURCEOPLIFTINGFORMAINFUNCTIONPASS
#define GEN_PASS_DECL_RESOURCEOPLIFTINGPASS
#define GEN_PASS_DECL_VERIFYNOOUTSIDECOMPILATIONMARKERSPASS
#define GEN_PASS_DECL_XLAINLINEDEVICEOPSPASS
#define GEN_PASS_DECL_XLAREWRITEPASS
#define GEN_PASS_DECL_XLAVALIDATEINPUTSPASS
#undef GEN_PASS_DECL
#endif // GEN_PASS_DECL

//===----------------------------------------------------------------------===//
// AnnotateParameterReplicationPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_ANNOTATEPARAMETERREPLICATIONPASS
#undef GEN_PASS_DECL_ANNOTATEPARAMETERREPLICATIONPASS
#endif // GEN_PASS_DECL_ANNOTATEPARAMETERREPLICATIONPASS
#ifdef GEN_PASS_DEF_ANNOTATEPARAMETERREPLICATIONPASS
namespace impl {

template <typename DerivedT>
class AnnotateParameterReplicationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AnnotateParameterReplicationPassBase;

  AnnotateParameterReplicationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AnnotateParameterReplicationPassBase(const AnnotateParameterReplicationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  AnnotateParameterReplicationPassBase& operator=(const AnnotateParameterReplicationPassBase &) = delete;
  AnnotateParameterReplicationPassBase(AnnotateParameterReplicationPassBase &&) = delete;
  AnnotateParameterReplicationPassBase& operator=(AnnotateParameterReplicationPassBase &&) = delete;
  ~AnnotateParameterReplicationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-annotate-parameter-replication");
  }
  ::llvm::StringRef getArgument() const override { return "tf-annotate-parameter-replication"; }

  ::llvm::StringRef getDescription() const override { return "Annotate whether a ClusterFuncOp's parameters have the same data across replicas."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AnnotateParameterReplicationPass");
  }
  ::llvm::StringRef getName() const override { return "AnnotateParameterReplicationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AnnotateParameterReplicationPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_ANNOTATEPARAMETERREPLICATIONPASS
#endif // GEN_PASS_DEF_ANNOTATEPARAMETERREPLICATIONPASS

//===----------------------------------------------------------------------===//
// DecomposeResourceOpsInClusterPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DECOMPOSERESOURCEOPSINCLUSTERPASS
#undef GEN_PASS_DECL_DECOMPOSERESOURCEOPSINCLUSTERPASS
#endif // GEN_PASS_DECL_DECOMPOSERESOURCEOPSINCLUSTERPASS
#ifdef GEN_PASS_DEF_DECOMPOSERESOURCEOPSINCLUSTERPASS
namespace impl {

template <typename DerivedT>
class DecomposeResourceOpsInClusterPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DecomposeResourceOpsInClusterPassBase;

  DecomposeResourceOpsInClusterPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DecomposeResourceOpsInClusterPassBase(const DecomposeResourceOpsInClusterPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  DecomposeResourceOpsInClusterPassBase& operator=(const DecomposeResourceOpsInClusterPassBase &) = delete;
  DecomposeResourceOpsInClusterPassBase(DecomposeResourceOpsInClusterPassBase &&) = delete;
  DecomposeResourceOpsInClusterPassBase& operator=(DecomposeResourceOpsInClusterPassBase &&) = delete;
  ~DecomposeResourceOpsInClusterPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-decompose-resource-ops-in-cluster");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-decompose-resource-ops-in-cluster"; }

  ::llvm::StringRef getDescription() const override { return "Decompose composite resource variable operations into primitive Read/AssignVariableOp and raw computation within device cluster and reachable functions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DecomposeResourceOpsInClusterPass");
  }
  ::llvm::StringRef getName() const override { return "DecomposeResourceOpsInClusterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DecomposeResourceOpsInClusterPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DECOMPOSERESOURCEOPSINCLUSTERPASS
#endif // GEN_PASS_DEF_DECOMPOSERESOURCEOPSINCLUSTERPASS

//===----------------------------------------------------------------------===//
// DecomposeResourceOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DECOMPOSERESOURCEOPSPASS
#undef GEN_PASS_DECL_DECOMPOSERESOURCEOPSPASS
#endif // GEN_PASS_DECL_DECOMPOSERESOURCEOPSPASS
#ifdef GEN_PASS_DEF_DECOMPOSERESOURCEOPSPASS
namespace impl {

template <typename DerivedT>
class DecomposeResourceOpsPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = DecomposeResourceOpsPassBase;

  DecomposeResourceOpsPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DecomposeResourceOpsPassBase(const DecomposeResourceOpsPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  DecomposeResourceOpsPassBase& operator=(const DecomposeResourceOpsPassBase &) = delete;
  DecomposeResourceOpsPassBase(DecomposeResourceOpsPassBase &&) = delete;
  DecomposeResourceOpsPassBase& operator=(DecomposeResourceOpsPassBase &&) = delete;
  ~DecomposeResourceOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-decompose-resource-ops");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-decompose-resource-ops"; }

  ::llvm::StringRef getDescription() const override { return "Decompose composite resource variable operations into primitive Read/AssignVariableOp and raw computation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DecomposeResourceOpsPass");
  }
  ::llvm::StringRef getName() const override { return "DecomposeResourceOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DecomposeResourceOpsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DECOMPOSERESOURCEOPSPASS
#endif // GEN_PASS_DEF_DECOMPOSERESOURCEOPSPASS

//===----------------------------------------------------------------------===//
// DeviceAttributeToLaunchPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_DEVICEATTRIBUTETOLAUNCHPASS
#undef GEN_PASS_DECL_DEVICEATTRIBUTETOLAUNCHPASS
#endif // GEN_PASS_DECL_DEVICEATTRIBUTETOLAUNCHPASS
#ifdef GEN_PASS_DEF_DEVICEATTRIBUTETOLAUNCHPASS
namespace impl {

template <typename DerivedT>
class DeviceAttributeToLaunchPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = DeviceAttributeToLaunchPassBase;

  DeviceAttributeToLaunchPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DeviceAttributeToLaunchPassBase(const DeviceAttributeToLaunchPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  DeviceAttributeToLaunchPassBase& operator=(const DeviceAttributeToLaunchPassBase &) = delete;
  DeviceAttributeToLaunchPassBase(DeviceAttributeToLaunchPassBase &&) = delete;
  DeviceAttributeToLaunchPassBase& operator=(DeviceAttributeToLaunchPassBase &&) = delete;
  ~DeviceAttributeToLaunchPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-attribute-to-launch");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-attribute-to-launch"; }

  ::llvm::StringRef getDescription() const override { return "Wraps each TF op which has a non-empty device attribute in a tf_device.launch."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DeviceAttributeToLaunchPass");
  }
  ::llvm::StringRef getName() const override { return "DeviceAttributeToLaunchPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DeviceAttributeToLaunchPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_DEVICEATTRIBUTETOLAUNCHPASS
#endif // GEN_PASS_DEF_DEVICEATTRIBUTETOLAUNCHPASS

//===----------------------------------------------------------------------===//
// LaunchToDeviceAttributePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_LAUNCHTODEVICEATTRIBUTEPASS
struct LaunchToDeviceAttributePassOptions {
  bool legacy_graph_export_ = true;
};
#undef GEN_PASS_DECL_LAUNCHTODEVICEATTRIBUTEPASS
#endif // GEN_PASS_DECL_LAUNCHTODEVICEATTRIBUTEPASS
#ifdef GEN_PASS_DEF_LAUNCHTODEVICEATTRIBUTEPASS
namespace impl {

template <typename DerivedT>
class LaunchToDeviceAttributePassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = LaunchToDeviceAttributePassBase;

  LaunchToDeviceAttributePassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LaunchToDeviceAttributePassBase(const LaunchToDeviceAttributePassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  LaunchToDeviceAttributePassBase& operator=(const LaunchToDeviceAttributePassBase &) = delete;
  LaunchToDeviceAttributePassBase(LaunchToDeviceAttributePassBase &&) = delete;
  LaunchToDeviceAttributePassBase& operator=(LaunchToDeviceAttributePassBase &&) = delete;
  ~LaunchToDeviceAttributePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-launch-to-device-attribute");
  }
  ::llvm::StringRef getArgument() const override { return "tf-launch-to-device-attribute"; }

  ::llvm::StringRef getDescription() const override { return "Hoists and annotates device launch inner ops with associated device attribute."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LaunchToDeviceAttributePass");
  }
  ::llvm::StringRef getName() const override { return "LaunchToDeviceAttributePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LaunchToDeviceAttributePassBase<DerivedT>)

  LaunchToDeviceAttributePassBase(LaunchToDeviceAttributePassOptions options) : LaunchToDeviceAttributePassBase() {
    legacy_graph_export_ = std::move(options.legacy_graph_export_);
  }
protected:
  ::mlir::Pass::Option<bool> legacy_graph_export_{*this, "legacy-graph-export", ::llvm::cl::desc("Determines whether or not this pass should execute logic that is reserved for the legacy graph export pipeline to maintain expected invariants. In the case of this pass, that means manually propagating controls to lifted parallel execute regions to the graph fetch to ensure the ops execute, as well as determining whether or not the islands created by this pass should be split after the replicated ops have been lifted."), ::llvm::cl::init(true)};
private:
};
} // namespace impl
#undef GEN_PASS_DEF_LAUNCHTODEVICEATTRIBUTEPASS
#endif // GEN_PASS_DEF_LAUNCHTODEVICEATTRIBUTEPASS

//===----------------------------------------------------------------------===//
// ResourceOpLiftingForMainFunctionPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_RESOURCEOPLIFTINGFORMAINFUNCTIONPASS
#undef GEN_PASS_DECL_RESOURCEOPLIFTINGFORMAINFUNCTIONPASS
#endif // GEN_PASS_DECL_RESOURCEOPLIFTINGFORMAINFUNCTIONPASS
#ifdef GEN_PASS_DEF_RESOURCEOPLIFTINGFORMAINFUNCTIONPASS
namespace impl {

template <typename DerivedT>
class ResourceOpLiftingForMainFunctionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ResourceOpLiftingForMainFunctionPassBase;

  ResourceOpLiftingForMainFunctionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ResourceOpLiftingForMainFunctionPassBase(const ResourceOpLiftingForMainFunctionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ResourceOpLiftingForMainFunctionPassBase& operator=(const ResourceOpLiftingForMainFunctionPassBase &) = delete;
  ResourceOpLiftingForMainFunctionPassBase(ResourceOpLiftingForMainFunctionPassBase &&) = delete;
  ResourceOpLiftingForMainFunctionPassBase& operator=(ResourceOpLiftingForMainFunctionPassBase &&) = delete;
  ~ResourceOpLiftingForMainFunctionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-resource-op-lifting-for-main-function");
  }
  ::llvm::StringRef getArgument() const override { return "tf-resource-op-lifting-for-main-function"; }

  ::llvm::StringRef getDescription() const override { return "Lifting resource operations out of control flow statements for the main function"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResourceOpLiftingForMainFunctionPass");
  }
  ::llvm::StringRef getName() const override { return "ResourceOpLiftingForMainFunctionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ResourceOpLiftingForMainFunctionPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_RESOURCEOPLIFTINGFORMAINFUNCTIONPASS
#endif // GEN_PASS_DEF_RESOURCEOPLIFTINGFORMAINFUNCTIONPASS

//===----------------------------------------------------------------------===//
// ResourceOpLiftingPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_RESOURCEOPLIFTINGPASS
#undef GEN_PASS_DECL_RESOURCEOPLIFTINGPASS
#endif // GEN_PASS_DECL_RESOURCEOPLIFTINGPASS
#ifdef GEN_PASS_DEF_RESOURCEOPLIFTINGPASS
namespace impl {

template <typename DerivedT>
class ResourceOpLiftingPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ResourceOpLiftingPassBase;

  ResourceOpLiftingPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ResourceOpLiftingPassBase(const ResourceOpLiftingPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ResourceOpLiftingPassBase& operator=(const ResourceOpLiftingPassBase &) = delete;
  ResourceOpLiftingPassBase(ResourceOpLiftingPassBase &&) = delete;
  ResourceOpLiftingPassBase& operator=(ResourceOpLiftingPassBase &&) = delete;
  ~ResourceOpLiftingPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-resource-op-lifting");
  }
  ::llvm::StringRef getArgument() const override { return "tf-resource-op-lifting"; }

  ::llvm::StringRef getDescription() const override { return "Lifting resource operations out of device computation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResourceOpLiftingPass");
  }
  ::llvm::StringRef getName() const override { return "ResourceOpLiftingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ResourceOpLiftingPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_RESOURCEOPLIFTINGPASS
#endif // GEN_PASS_DEF_RESOURCEOPLIFTINGPASS

//===----------------------------------------------------------------------===//
// VerifyNoOutsideCompilationMarkersPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_VERIFYNOOUTSIDECOMPILATIONMARKERSPASS
#undef GEN_PASS_DECL_VERIFYNOOUTSIDECOMPILATIONMARKERSPASS
#endif // GEN_PASS_DECL_VERIFYNOOUTSIDECOMPILATIONMARKERSPASS
#ifdef GEN_PASS_DEF_VERIFYNOOUTSIDECOMPILATIONMARKERSPASS
namespace impl {

template <typename DerivedT>
class VerifyNoOutsideCompilationMarkersPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyNoOutsideCompilationMarkersPassBase;

  VerifyNoOutsideCompilationMarkersPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyNoOutsideCompilationMarkersPassBase(const VerifyNoOutsideCompilationMarkersPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VerifyNoOutsideCompilationMarkersPassBase& operator=(const VerifyNoOutsideCompilationMarkersPassBase &) = delete;
  VerifyNoOutsideCompilationMarkersPassBase(VerifyNoOutsideCompilationMarkersPassBase &&) = delete;
  VerifyNoOutsideCompilationMarkersPassBase& operator=(VerifyNoOutsideCompilationMarkersPassBase &&) = delete;
  ~VerifyNoOutsideCompilationMarkersPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("verify-no-outside-compilation-markers");
  }
  ::llvm::StringRef getArgument() const override { return "verify-no-outside-compilation-markers"; }

  ::llvm::StringRef getDescription() const override { return "Verifies that after Outside Compilation passes complete, there are no more _xla_outside_compilation attributes and no tf_device.launch ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyNoOutsideCompilationMarkersPass");
  }
  ::llvm::StringRef getName() const override { return "VerifyNoOutsideCompilationMarkersPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyNoOutsideCompilationMarkersPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_VERIFYNOOUTSIDECOMPILATIONMARKERSPASS
#endif // GEN_PASS_DEF_VERIFYNOOUTSIDECOMPILATIONMARKERSPASS

//===----------------------------------------------------------------------===//
// XlaInlineDeviceOpsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_XLAINLINEDEVICEOPSPASS
#undef GEN_PASS_DECL_XLAINLINEDEVICEOPSPASS
#endif // GEN_PASS_DECL_XLAINLINEDEVICEOPSPASS
#ifdef GEN_PASS_DEF_XLAINLINEDEVICEOPSPASS
namespace impl {

template <typename DerivedT>
class XlaInlineDeviceOpsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = XlaInlineDeviceOpsPassBase;

  XlaInlineDeviceOpsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaInlineDeviceOpsPassBase(const XlaInlineDeviceOpsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  XlaInlineDeviceOpsPassBase& operator=(const XlaInlineDeviceOpsPassBase &) = delete;
  XlaInlineDeviceOpsPassBase(XlaInlineDeviceOpsPassBase &&) = delete;
  XlaInlineDeviceOpsPassBase& operator=(XlaInlineDeviceOpsPassBase &&) = delete;
  ~XlaInlineDeviceOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-inline-device-ops");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-inline-device-ops"; }

  ::llvm::StringRef getDescription() const override { return "Inline all Cluster op based in the parent region"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaInlineDeviceOpsPass");
  }
  ::llvm::StringRef getName() const override { return "XlaInlineDeviceOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaInlineDeviceOpsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_XLAINLINEDEVICEOPSPASS
#endif // GEN_PASS_DEF_XLAINLINEDEVICEOPSPASS

//===----------------------------------------------------------------------===//
// XlaRewritePass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_XLAREWRITEPASS
#undef GEN_PASS_DECL_XLAREWRITEPASS
#endif // GEN_PASS_DECL_XLAREWRITEPASS
#ifdef GEN_PASS_DEF_XLAREWRITEPASS
namespace impl {

template <typename DerivedT>
class XlaRewritePassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = XlaRewritePassBase;

  XlaRewritePassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaRewritePassBase(const XlaRewritePassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  XlaRewritePassBase& operator=(const XlaRewritePassBase &) = delete;
  XlaRewritePassBase(XlaRewritePassBase &&) = delete;
  XlaRewritePassBase& operator=(XlaRewritePassBase &&) = delete;
  ~XlaRewritePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Rewrites partition calls into Xla launch ops to make the attached function run on XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaRewritePass");
  }
  ::llvm::StringRef getName() const override { return "XlaRewritePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaRewritePassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_XLAREWRITEPASS
#endif // GEN_PASS_DEF_XLAREWRITEPASS

//===----------------------------------------------------------------------===//
// XlaValidateInputsPass
//===----------------------------------------------------------------------===//
#ifdef GEN_PASS_DECL_XLAVALIDATEINPUTSPASS
#undef GEN_PASS_DECL_XLAVALIDATEINPUTSPASS
#endif // GEN_PASS_DECL_XLAVALIDATEINPUTSPASS
#ifdef GEN_PASS_DEF_XLAVALIDATEINPUTSPASS
namespace impl {

template <typename DerivedT>
class XlaValidateInputsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = XlaValidateInputsPassBase;

  XlaValidateInputsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaValidateInputsPassBase(const XlaValidateInputsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  XlaValidateInputsPassBase& operator=(const XlaValidateInputsPassBase &) = delete;
  XlaValidateInputsPassBase(XlaValidateInputsPassBase &&) = delete;
  XlaValidateInputsPassBase& operator=(XlaValidateInputsPassBase &&) = delete;
  ~XlaValidateInputsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-validate-inputs");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-validate-inputs"; }

  ::llvm::StringRef getDescription() const override { return "Validtes inputs to the TF CPU/GPU bridge"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaValidateInputsPass");
  }
  ::llvm::StringRef getName() const override { return "XlaValidateInputsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Return the dialect that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaValidateInputsPassBase<DerivedT>)

protected:
private:
};
} // namespace impl
#undef GEN_PASS_DEF_XLAVALIDATEINPUTSPASS
#endif // GEN_PASS_DEF_XLAVALIDATEINPUTSPASS
#ifdef GEN_PASS_REGISTRATION

//===----------------------------------------------------------------------===//
// AnnotateParameterReplicationPass Registration
//===----------------------------------------------------------------------===//

inline void registerAnnotateParameterReplicationPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateAnnotateParameterReplicationPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerAnnotateParameterReplicationPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateAnnotateParameterReplicationPass();
  });
}

//===----------------------------------------------------------------------===//
// DecomposeResourceOpsInClusterPass Registration
//===----------------------------------------------------------------------===//

inline void registerDecomposeResourceOpsInClusterPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDecomposeResourceOpsInClusterPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDecomposeResourceOpsInClusterPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDecomposeResourceOpsInClusterPass();
  });
}

//===----------------------------------------------------------------------===//
// DecomposeResourceOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerDecomposeResourceOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDecomposeResourceOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDecomposeResourceOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDecomposeResourceOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// DeviceAttributeToLaunchPass Registration
//===----------------------------------------------------------------------===//

inline void registerDeviceAttributeToLaunchPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDeviceAttributeToLaunchPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerDeviceAttributeToLaunchPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateDeviceAttributeToLaunchPass();
  });
}

//===----------------------------------------------------------------------===//
// LaunchToDeviceAttributePass Registration
//===----------------------------------------------------------------------===//

inline void registerLaunchToDeviceAttributePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateLaunchToDeviceAttributePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerLaunchToDeviceAttributePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateLaunchToDeviceAttributePass();
  });
}

//===----------------------------------------------------------------------===//
// ResourceOpLiftingForMainFunctionPass Registration
//===----------------------------------------------------------------------===//

inline void registerResourceOpLiftingForMainFunctionPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateResourceOpLiftingForMainFunctionPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerResourceOpLiftingForMainFunctionPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateResourceOpLiftingForMainFunctionPass();
  });
}

//===----------------------------------------------------------------------===//
// ResourceOpLiftingPass Registration
//===----------------------------------------------------------------------===//

inline void registerResourceOpLiftingPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateResourceOpLiftingPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerResourceOpLiftingPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateResourceOpLiftingPass();
  });
}

//===----------------------------------------------------------------------===//
// VerifyNoOutsideCompilationMarkersPass Registration
//===----------------------------------------------------------------------===//

inline void registerVerifyNoOutsideCompilationMarkersPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateVerifyNoOutsideCompilationMarkersPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerVerifyNoOutsideCompilationMarkersPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateVerifyNoOutsideCompilationMarkersPass();
  });
}

//===----------------------------------------------------------------------===//
// XlaInlineDeviceOpsPass Registration
//===----------------------------------------------------------------------===//

inline void registerXlaInlineDeviceOpsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateXlaInlineDeviceOpsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerXlaInlineDeviceOpsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateXlaInlineDeviceOpsPass();
  });
}

//===----------------------------------------------------------------------===//
// XlaRewritePass Registration
//===----------------------------------------------------------------------===//

inline void registerXlaRewritePass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateXlaRewritePass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerXlaRewritePassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateXlaRewritePass();
  });
}

//===----------------------------------------------------------------------===//
// XlaValidateInputsPass Registration
//===----------------------------------------------------------------------===//

inline void registerXlaValidateInputsPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateXlaValidateInputsPass();
  });
}

// Old registration code, kept for temporary backwards compatibility.
inline void registerXlaValidateInputsPassPass() {
  ::mlir::registerPass([]() -> std::unique_ptr<::mlir::Pass> {
    return TFDevice::CreateXlaValidateInputsPass();
  });
}

//===----------------------------------------------------------------------===//
// TensorFlowDevice Registration
//===----------------------------------------------------------------------===//

inline void registerTensorFlowDevicePasses() {
  registerAnnotateParameterReplicationPass();
  registerDecomposeResourceOpsInClusterPass();
  registerDecomposeResourceOpsPass();
  registerDeviceAttributeToLaunchPass();
  registerLaunchToDeviceAttributePass();
  registerResourceOpLiftingForMainFunctionPass();
  registerResourceOpLiftingPass();
  registerVerifyNoOutsideCompilationMarkersPass();
  registerXlaInlineDeviceOpsPass();
  registerXlaRewritePass();
  registerXlaValidateInputsPass();
}
#undef GEN_PASS_REGISTRATION
#endif // GEN_PASS_REGISTRATION
// Deprecated. Please use the new per-pass macros.
#ifdef GEN_PASS_CLASSES

template <typename DerivedT>
class AnnotateParameterReplicationPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = AnnotateParameterReplicationPassBase;

  AnnotateParameterReplicationPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  AnnotateParameterReplicationPassBase(const AnnotateParameterReplicationPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  AnnotateParameterReplicationPassBase& operator=(const AnnotateParameterReplicationPassBase &) = delete;
  AnnotateParameterReplicationPassBase(AnnotateParameterReplicationPassBase &&) = delete;
  AnnotateParameterReplicationPassBase& operator=(AnnotateParameterReplicationPassBase &&) = delete;
  ~AnnotateParameterReplicationPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-annotate-parameter-replication");
  }
  ::llvm::StringRef getArgument() const override { return "tf-annotate-parameter-replication"; }

  ::llvm::StringRef getDescription() const override { return "Annotate whether a ClusterFuncOp's parameters have the same data across replicas."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("AnnotateParameterReplicationPass");
  }
  ::llvm::StringRef getName() const override { return "AnnotateParameterReplicationPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(AnnotateParameterReplicationPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class DecomposeResourceOpsInClusterPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = DecomposeResourceOpsInClusterPassBase;

  DecomposeResourceOpsInClusterPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  DecomposeResourceOpsInClusterPassBase(const DecomposeResourceOpsInClusterPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  DecomposeResourceOpsInClusterPassBase& operator=(const DecomposeResourceOpsInClusterPassBase &) = delete;
  DecomposeResourceOpsInClusterPassBase(DecomposeResourceOpsInClusterPassBase &&) = delete;
  DecomposeResourceOpsInClusterPassBase& operator=(DecomposeResourceOpsInClusterPassBase &&) = delete;
  ~DecomposeResourceOpsInClusterPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-decompose-resource-ops-in-cluster");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-decompose-resource-ops-in-cluster"; }

  ::llvm::StringRef getDescription() const override { return "Decompose composite resource variable operations into primitive Read/AssignVariableOp and raw computation within device cluster and reachable functions."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DecomposeResourceOpsInClusterPass");
  }
  ::llvm::StringRef getName() const override { return "DecomposeResourceOpsInClusterPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DecomposeResourceOpsInClusterPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class DecomposeResourceOpsPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = DecomposeResourceOpsPassBase;

  DecomposeResourceOpsPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DecomposeResourceOpsPassBase(const DecomposeResourceOpsPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  DecomposeResourceOpsPassBase& operator=(const DecomposeResourceOpsPassBase &) = delete;
  DecomposeResourceOpsPassBase(DecomposeResourceOpsPassBase &&) = delete;
  DecomposeResourceOpsPassBase& operator=(DecomposeResourceOpsPassBase &&) = delete;
  ~DecomposeResourceOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-decompose-resource-ops");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-decompose-resource-ops"; }

  ::llvm::StringRef getDescription() const override { return "Decompose composite resource variable operations into primitive Read/AssignVariableOp and raw computation."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DecomposeResourceOpsPass");
  }
  ::llvm::StringRef getName() const override { return "DecomposeResourceOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DecomposeResourceOpsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class DeviceAttributeToLaunchPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = DeviceAttributeToLaunchPassBase;

  DeviceAttributeToLaunchPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  DeviceAttributeToLaunchPassBase(const DeviceAttributeToLaunchPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  DeviceAttributeToLaunchPassBase& operator=(const DeviceAttributeToLaunchPassBase &) = delete;
  DeviceAttributeToLaunchPassBase(DeviceAttributeToLaunchPassBase &&) = delete;
  DeviceAttributeToLaunchPassBase& operator=(DeviceAttributeToLaunchPassBase &&) = delete;
  ~DeviceAttributeToLaunchPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-device-attribute-to-launch");
  }
  ::llvm::StringRef getArgument() const override { return "tf-device-attribute-to-launch"; }

  ::llvm::StringRef getDescription() const override { return "Wraps each TF op which has a non-empty device attribute in a tf_device.launch."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("DeviceAttributeToLaunchPass");
  }
  ::llvm::StringRef getName() const override { return "DeviceAttributeToLaunchPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(DeviceAttributeToLaunchPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class LaunchToDeviceAttributePassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = LaunchToDeviceAttributePassBase;

  LaunchToDeviceAttributePassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  LaunchToDeviceAttributePassBase(const LaunchToDeviceAttributePassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  LaunchToDeviceAttributePassBase& operator=(const LaunchToDeviceAttributePassBase &) = delete;
  LaunchToDeviceAttributePassBase(LaunchToDeviceAttributePassBase &&) = delete;
  LaunchToDeviceAttributePassBase& operator=(LaunchToDeviceAttributePassBase &&) = delete;
  ~LaunchToDeviceAttributePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-launch-to-device-attribute");
  }
  ::llvm::StringRef getArgument() const override { return "tf-launch-to-device-attribute"; }

  ::llvm::StringRef getDescription() const override { return "Hoists and annotates device launch inner ops with associated device attribute."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("LaunchToDeviceAttributePass");
  }
  ::llvm::StringRef getName() const override { return "LaunchToDeviceAttributePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(LaunchToDeviceAttributePassBase<DerivedT>)

protected:
  ::mlir::Pass::Option<bool> legacy_graph_export_{*this, "legacy-graph-export", ::llvm::cl::desc("Determines whether or not this pass should execute logic that is reserved for the legacy graph export pipeline to maintain expected invariants. In the case of this pass, that means manually propagating controls to lifted parallel execute regions to the graph fetch to ensure the ops execute, as well as determining whether or not the islands created by this pass should be split after the replicated ops have been lifted."), ::llvm::cl::init(true)};
};

template <typename DerivedT>
class ResourceOpLiftingForMainFunctionPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ResourceOpLiftingForMainFunctionPassBase;

  ResourceOpLiftingForMainFunctionPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ResourceOpLiftingForMainFunctionPassBase(const ResourceOpLiftingForMainFunctionPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ResourceOpLiftingForMainFunctionPassBase& operator=(const ResourceOpLiftingForMainFunctionPassBase &) = delete;
  ResourceOpLiftingForMainFunctionPassBase(ResourceOpLiftingForMainFunctionPassBase &&) = delete;
  ResourceOpLiftingForMainFunctionPassBase& operator=(ResourceOpLiftingForMainFunctionPassBase &&) = delete;
  ~ResourceOpLiftingForMainFunctionPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-resource-op-lifting-for-main-function");
  }
  ::llvm::StringRef getArgument() const override { return "tf-resource-op-lifting-for-main-function"; }

  ::llvm::StringRef getDescription() const override { return "Lifting resource operations out of control flow statements for the main function"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResourceOpLiftingForMainFunctionPass");
  }
  ::llvm::StringRef getName() const override { return "ResourceOpLiftingForMainFunctionPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ResourceOpLiftingForMainFunctionPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class ResourceOpLiftingPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = ResourceOpLiftingPassBase;

  ResourceOpLiftingPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  ResourceOpLiftingPassBase(const ResourceOpLiftingPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  ResourceOpLiftingPassBase& operator=(const ResourceOpLiftingPassBase &) = delete;
  ResourceOpLiftingPassBase(ResourceOpLiftingPassBase &&) = delete;
  ResourceOpLiftingPassBase& operator=(ResourceOpLiftingPassBase &&) = delete;
  ~ResourceOpLiftingPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-resource-op-lifting");
  }
  ::llvm::StringRef getArgument() const override { return "tf-resource-op-lifting"; }

  ::llvm::StringRef getDescription() const override { return "Lifting resource operations out of device computation"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("ResourceOpLiftingPass");
  }
  ::llvm::StringRef getName() const override { return "ResourceOpLiftingPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(ResourceOpLiftingPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class VerifyNoOutsideCompilationMarkersPassBase : public ::mlir::OperationPass<mlir::func::FuncOp> {
public:
  using Base = VerifyNoOutsideCompilationMarkersPassBase;

  VerifyNoOutsideCompilationMarkersPassBase() : ::mlir::OperationPass<mlir::func::FuncOp>(::mlir::TypeID::get<DerivedT>()) {}
  VerifyNoOutsideCompilationMarkersPassBase(const VerifyNoOutsideCompilationMarkersPassBase &other) : ::mlir::OperationPass<mlir::func::FuncOp>(other) {}
  VerifyNoOutsideCompilationMarkersPassBase& operator=(const VerifyNoOutsideCompilationMarkersPassBase &) = delete;
  VerifyNoOutsideCompilationMarkersPassBase(VerifyNoOutsideCompilationMarkersPassBase &&) = delete;
  VerifyNoOutsideCompilationMarkersPassBase& operator=(VerifyNoOutsideCompilationMarkersPassBase &&) = delete;
  ~VerifyNoOutsideCompilationMarkersPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("verify-no-outside-compilation-markers");
  }
  ::llvm::StringRef getArgument() const override { return "verify-no-outside-compilation-markers"; }

  ::llvm::StringRef getDescription() const override { return "Verifies that after Outside Compilation passes complete, there are no more _xla_outside_compilation attributes and no tf_device.launch ops."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("VerifyNoOutsideCompilationMarkersPass");
  }
  ::llvm::StringRef getName() const override { return "VerifyNoOutsideCompilationMarkersPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(VerifyNoOutsideCompilationMarkersPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class XlaInlineDeviceOpsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = XlaInlineDeviceOpsPassBase;

  XlaInlineDeviceOpsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaInlineDeviceOpsPassBase(const XlaInlineDeviceOpsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  XlaInlineDeviceOpsPassBase& operator=(const XlaInlineDeviceOpsPassBase &) = delete;
  XlaInlineDeviceOpsPassBase(XlaInlineDeviceOpsPassBase &&) = delete;
  XlaInlineDeviceOpsPassBase& operator=(XlaInlineDeviceOpsPassBase &&) = delete;
  ~XlaInlineDeviceOpsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-inline-device-ops");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-inline-device-ops"; }

  ::llvm::StringRef getDescription() const override { return "Inline all Cluster op based in the parent region"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaInlineDeviceOpsPass");
  }
  ::llvm::StringRef getName() const override { return "XlaInlineDeviceOpsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaInlineDeviceOpsPassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class XlaRewritePassBase : public ::mlir::OperationPass<mlir::ModuleOp> {
public:
  using Base = XlaRewritePassBase;

  XlaRewritePassBase() : ::mlir::OperationPass<mlir::ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaRewritePassBase(const XlaRewritePassBase &other) : ::mlir::OperationPass<mlir::ModuleOp>(other) {}
  XlaRewritePassBase& operator=(const XlaRewritePassBase &) = delete;
  XlaRewritePassBase(XlaRewritePassBase &&) = delete;
  XlaRewritePassBase& operator=(XlaRewritePassBase &&) = delete;
  ~XlaRewritePassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-rewrite");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-rewrite"; }

  ::llvm::StringRef getDescription() const override { return "Rewrites partition calls into Xla launch ops to make the attached function run on XLA."; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaRewritePass");
  }
  ::llvm::StringRef getName() const override { return "XlaRewritePass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaRewritePassBase<DerivedT>)

protected:
};

template <typename DerivedT>
class XlaValidateInputsPassBase : public ::mlir::OperationPass<ModuleOp> {
public:
  using Base = XlaValidateInputsPassBase;

  XlaValidateInputsPassBase() : ::mlir::OperationPass<ModuleOp>(::mlir::TypeID::get<DerivedT>()) {}
  XlaValidateInputsPassBase(const XlaValidateInputsPassBase &other) : ::mlir::OperationPass<ModuleOp>(other) {}
  XlaValidateInputsPassBase& operator=(const XlaValidateInputsPassBase &) = delete;
  XlaValidateInputsPassBase(XlaValidateInputsPassBase &&) = delete;
  XlaValidateInputsPassBase& operator=(XlaValidateInputsPassBase &&) = delete;
  ~XlaValidateInputsPassBase() = default;

  /// Returns the command-line argument attached to this pass.
  static constexpr ::llvm::StringLiteral getArgumentName() {
    return ::llvm::StringLiteral("tf-xla-validate-inputs");
  }
  ::llvm::StringRef getArgument() const override { return "tf-xla-validate-inputs"; }

  ::llvm::StringRef getDescription() const override { return "Validtes inputs to the TF CPU/GPU bridge"; }

  /// Returns the derived pass name.
  static constexpr ::llvm::StringLiteral getPassName() {
    return ::llvm::StringLiteral("XlaValidateInputsPass");
  }
  ::llvm::StringRef getName() const override { return "XlaValidateInputsPass"; }

  /// Support isa/dyn_cast functionality for the derived pass class.
  static bool classof(const ::mlir::Pass *pass) {
    return pass->getTypeID() == ::mlir::TypeID::get<DerivedT>();
  }

  /// A clone method to create a copy of this pass.
  std::unique_ptr<::mlir::Pass> clonePass() const override {
    return std::make_unique<DerivedT>(*static_cast<const DerivedT *>(this));
  }

  /// Register the dialects that must be loaded in the context before this pass.
  void getDependentDialects(::mlir::DialectRegistry &registry) const override {
    registry.insert<tf_device::TensorFlowDeviceDialect>();
  }

  /// Explicitly declare the TypeID for this class. We declare an explicit private
  /// instantiation because Pass classes should only be visible by the current
  /// library.
  MLIR_DEFINE_EXPLICIT_INTERNAL_INLINE_TYPE_ID(XlaValidateInputsPassBase<DerivedT>)

protected:
};
#undef GEN_PASS_CLASSES
#endif // GEN_PASS_CLASSES
