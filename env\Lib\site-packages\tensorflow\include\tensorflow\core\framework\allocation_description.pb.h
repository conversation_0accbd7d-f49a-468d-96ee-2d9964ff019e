// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/allocation_description.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto;
namespace tensorflow {
class AllocationDescription;
struct AllocationDescriptionDefaultTypeInternal;
extern AllocationDescriptionDefaultTypeInternal _AllocationDescription_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AllocationDescription* Arena::CreateMaybeMessage<::tensorflow::AllocationDescription>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class AllocationDescription final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AllocationDescription) */ {
 public:
  inline AllocationDescription() : AllocationDescription(nullptr) {}
  ~AllocationDescription() override;
  explicit PROTOBUF_CONSTEXPR AllocationDescription(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AllocationDescription(const AllocationDescription& from);
  AllocationDescription(AllocationDescription&& from) noexcept
    : AllocationDescription() {
    *this = ::std::move(from);
  }

  inline AllocationDescription& operator=(const AllocationDescription& from) {
    CopyFrom(from);
    return *this;
  }
  inline AllocationDescription& operator=(AllocationDescription&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AllocationDescription& default_instance() {
    return *internal_default_instance();
  }
  static inline const AllocationDescription* internal_default_instance() {
    return reinterpret_cast<const AllocationDescription*>(
               &_AllocationDescription_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AllocationDescription& a, AllocationDescription& b) {
    a.Swap(&b);
  }
  inline void Swap(AllocationDescription* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AllocationDescription* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AllocationDescription* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AllocationDescription>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AllocationDescription& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AllocationDescription& from) {
    AllocationDescription::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AllocationDescription* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AllocationDescription";
  }
  protected:
  explicit AllocationDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAllocatorNameFieldNumber = 3,
    kRequestedBytesFieldNumber = 1,
    kAllocatedBytesFieldNumber = 2,
    kAllocationIdFieldNumber = 4,
    kPtrFieldNumber = 6,
    kHasSingleReferenceFieldNumber = 5,
  };
  // string allocator_name = 3;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_allocator_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_allocator_name();
  PROTOBUF_NODISCARD std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  private:
  const std::string& _internal_allocator_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_allocator_name(const std::string& value);
  std::string* _internal_mutable_allocator_name();
  public:

  // int64 requested_bytes = 1;
  void clear_requested_bytes();
  int64_t requested_bytes() const;
  void set_requested_bytes(int64_t value);
  private:
  int64_t _internal_requested_bytes() const;
  void _internal_set_requested_bytes(int64_t value);
  public:

  // int64 allocated_bytes = 2;
  void clear_allocated_bytes();
  int64_t allocated_bytes() const;
  void set_allocated_bytes(int64_t value);
  private:
  int64_t _internal_allocated_bytes() const;
  void _internal_set_allocated_bytes(int64_t value);
  public:

  // int64 allocation_id = 4;
  void clear_allocation_id();
  int64_t allocation_id() const;
  void set_allocation_id(int64_t value);
  private:
  int64_t _internal_allocation_id() const;
  void _internal_set_allocation_id(int64_t value);
  public:

  // uint64 ptr = 6;
  void clear_ptr();
  uint64_t ptr() const;
  void set_ptr(uint64_t value);
  private:
  uint64_t _internal_ptr() const;
  void _internal_set_ptr(uint64_t value);
  public:

  // bool has_single_reference = 5;
  void clear_has_single_reference();
  bool has_single_reference() const;
  void set_has_single_reference(bool value);
  private:
  bool _internal_has_single_reference() const;
  void _internal_set_has_single_reference(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.AllocationDescription)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
    int64_t requested_bytes_;
    int64_t allocated_bytes_;
    int64_t allocation_id_;
    uint64_t ptr_;
    bool has_single_reference_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AllocationDescription

// int64 requested_bytes = 1;
inline void AllocationDescription::clear_requested_bytes() {
  _impl_.requested_bytes_ = int64_t{0};
}
inline int64_t AllocationDescription::_internal_requested_bytes() const {
  return _impl_.requested_bytes_;
}
inline int64_t AllocationDescription::requested_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationDescription.requested_bytes)
  return _internal_requested_bytes();
}
inline void AllocationDescription::_internal_set_requested_bytes(int64_t value) {
  
  _impl_.requested_bytes_ = value;
}
inline void AllocationDescription::set_requested_bytes(int64_t value) {
  _internal_set_requested_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocationDescription.requested_bytes)
}

// int64 allocated_bytes = 2;
inline void AllocationDescription::clear_allocated_bytes() {
  _impl_.allocated_bytes_ = int64_t{0};
}
inline int64_t AllocationDescription::_internal_allocated_bytes() const {
  return _impl_.allocated_bytes_;
}
inline int64_t AllocationDescription::allocated_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationDescription.allocated_bytes)
  return _internal_allocated_bytes();
}
inline void AllocationDescription::_internal_set_allocated_bytes(int64_t value) {
  
  _impl_.allocated_bytes_ = value;
}
inline void AllocationDescription::set_allocated_bytes(int64_t value) {
  _internal_set_allocated_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocationDescription.allocated_bytes)
}

// string allocator_name = 3;
inline void AllocationDescription::clear_allocator_name() {
  _impl_.allocator_name_.ClearToEmpty();
}
inline const std::string& AllocationDescription::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationDescription.allocator_name)
  return _internal_allocator_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AllocationDescription::set_allocator_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.allocator_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.AllocationDescription.allocator_name)
}
inline std::string* AllocationDescription::mutable_allocator_name() {
  std::string* _s = _internal_mutable_allocator_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.AllocationDescription.allocator_name)
  return _s;
}
inline const std::string& AllocationDescription::_internal_allocator_name() const {
  return _impl_.allocator_name_.Get();
}
inline void AllocationDescription::_internal_set_allocator_name(const std::string& value) {
  
  _impl_.allocator_name_.Set(value, GetArenaForAllocation());
}
inline std::string* AllocationDescription::_internal_mutable_allocator_name() {
  
  return _impl_.allocator_name_.Mutable(GetArenaForAllocation());
}
inline std::string* AllocationDescription::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.AllocationDescription.allocator_name)
  return _impl_.allocator_name_.Release();
}
inline void AllocationDescription::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  _impl_.allocator_name_.SetAllocated(allocator_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.allocator_name_.IsDefault()) {
    _impl_.allocator_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AllocationDescription.allocator_name)
}

// int64 allocation_id = 4;
inline void AllocationDescription::clear_allocation_id() {
  _impl_.allocation_id_ = int64_t{0};
}
inline int64_t AllocationDescription::_internal_allocation_id() const {
  return _impl_.allocation_id_;
}
inline int64_t AllocationDescription::allocation_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationDescription.allocation_id)
  return _internal_allocation_id();
}
inline void AllocationDescription::_internal_set_allocation_id(int64_t value) {
  
  _impl_.allocation_id_ = value;
}
inline void AllocationDescription::set_allocation_id(int64_t value) {
  _internal_set_allocation_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocationDescription.allocation_id)
}

// bool has_single_reference = 5;
inline void AllocationDescription::clear_has_single_reference() {
  _impl_.has_single_reference_ = false;
}
inline bool AllocationDescription::_internal_has_single_reference() const {
  return _impl_.has_single_reference_;
}
inline bool AllocationDescription::has_single_reference() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationDescription.has_single_reference)
  return _internal_has_single_reference();
}
inline void AllocationDescription::_internal_set_has_single_reference(bool value) {
  
  _impl_.has_single_reference_ = value;
}
inline void AllocationDescription::set_has_single_reference(bool value) {
  _internal_set_has_single_reference(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocationDescription.has_single_reference)
}

// uint64 ptr = 6;
inline void AllocationDescription::clear_ptr() {
  _impl_.ptr_ = uint64_t{0u};
}
inline uint64_t AllocationDescription::_internal_ptr() const {
  return _impl_.ptr_;
}
inline uint64_t AllocationDescription::ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.AllocationDescription.ptr)
  return _internal_ptr();
}
inline void AllocationDescription::_internal_set_ptr(uint64_t value) {
  
  _impl_.ptr_ = value;
}
inline void AllocationDescription::set_ptr(uint64_t value) {
  _internal_set_ptr(value);
  // @@protoc_insertion_point(field_set:tensorflow.AllocationDescription.ptr)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fallocation_5fdescription_2eproto
