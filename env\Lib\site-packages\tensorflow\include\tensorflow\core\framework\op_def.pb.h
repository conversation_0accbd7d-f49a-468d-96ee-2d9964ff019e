// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/op_def.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
#include "tensorflow/core/framework/full_type.pb.h"
#include "tensorflow/core/framework/resource_handle.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
namespace tensorflow {
class OpDef;
struct OpDefDefaultTypeInternal;
extern OpDefDefaultTypeInternal _OpDef_default_instance_;
class OpDef_ArgDef;
struct OpDef_ArgDefDefaultTypeInternal;
extern OpDef_ArgDefDefaultTypeInternal _OpDef_ArgDef_default_instance_;
class OpDef_AttrDef;
struct OpDef_AttrDefDefaultTypeInternal;
extern OpDef_AttrDefDefaultTypeInternal _OpDef_AttrDef_default_instance_;
class OpDeprecation;
struct OpDeprecationDefaultTypeInternal;
extern OpDeprecationDefaultTypeInternal _OpDeprecation_default_instance_;
class OpList;
struct OpListDefaultTypeInternal;
extern OpListDefaultTypeInternal _OpList_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::OpDef* Arena::CreateMaybeMessage<::tensorflow::OpDef>(Arena*);
template<> ::tensorflow::OpDef_ArgDef* Arena::CreateMaybeMessage<::tensorflow::OpDef_ArgDef>(Arena*);
template<> ::tensorflow::OpDef_AttrDef* Arena::CreateMaybeMessage<::tensorflow::OpDef_AttrDef>(Arena*);
template<> ::tensorflow::OpDeprecation* Arena::CreateMaybeMessage<::tensorflow::OpDeprecation>(Arena*);
template<> ::tensorflow::OpList* Arena::CreateMaybeMessage<::tensorflow::OpList>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class OpDef_ArgDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef.ArgDef) */ {
 public:
  inline OpDef_ArgDef() : OpDef_ArgDef(nullptr) {}
  ~OpDef_ArgDef() override;
  explicit PROTOBUF_CONSTEXPR OpDef_ArgDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpDef_ArgDef(const OpDef_ArgDef& from);
  OpDef_ArgDef(OpDef_ArgDef&& from) noexcept
    : OpDef_ArgDef() {
    *this = ::std::move(from);
  }

  inline OpDef_ArgDef& operator=(const OpDef_ArgDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpDef_ArgDef& operator=(OpDef_ArgDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpDef_ArgDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpDef_ArgDef* internal_default_instance() {
    return reinterpret_cast<const OpDef_ArgDef*>(
               &_OpDef_ArgDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(OpDef_ArgDef& a, OpDef_ArgDef& b) {
    a.Swap(&b);
  }
  inline void Swap(OpDef_ArgDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpDef_ArgDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpDef_ArgDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpDef_ArgDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpDef_ArgDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpDef_ArgDef& from) {
    OpDef_ArgDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef_ArgDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpDef.ArgDef";
  }
  protected:
  explicit OpDef_ArgDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHandleDataFieldNumber = 7,
    kNameFieldNumber = 1,
    kDescriptionFieldNumber = 2,
    kTypeAttrFieldNumber = 4,
    kNumberAttrFieldNumber = 5,
    kTypeListAttrFieldNumber = 6,
    kExperimentalFullTypeFieldNumber = 17,
    kTypeFieldNumber = 3,
    kIsRefFieldNumber = 16,
  };
  // repeated .tensorflow.ResourceHandleProto.DtypeAndShape handle_data = 7;
  int handle_data_size() const;
  private:
  int _internal_handle_data_size() const;
  public:
  void clear_handle_data();
  ::tensorflow::ResourceHandleProto_DtypeAndShape* mutable_handle_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >*
      mutable_handle_data();
  private:
  const ::tensorflow::ResourceHandleProto_DtypeAndShape& _internal_handle_data(int index) const;
  ::tensorflow::ResourceHandleProto_DtypeAndShape* _internal_add_handle_data();
  public:
  const ::tensorflow::ResourceHandleProto_DtypeAndShape& handle_data(int index) const;
  ::tensorflow::ResourceHandleProto_DtypeAndShape* add_handle_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >&
      handle_data() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string description = 2;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // string type_attr = 4;
  void clear_type_attr();
  const std::string& type_attr() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type_attr(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type_attr();
  PROTOBUF_NODISCARD std::string* release_type_attr();
  void set_allocated_type_attr(std::string* type_attr);
  private:
  const std::string& _internal_type_attr() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type_attr(const std::string& value);
  std::string* _internal_mutable_type_attr();
  public:

  // string number_attr = 5;
  void clear_number_attr();
  const std::string& number_attr() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_number_attr(ArgT0&& arg0, ArgT... args);
  std::string* mutable_number_attr();
  PROTOBUF_NODISCARD std::string* release_number_attr();
  void set_allocated_number_attr(std::string* number_attr);
  private:
  const std::string& _internal_number_attr() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_number_attr(const std::string& value);
  std::string* _internal_mutable_number_attr();
  public:

  // string type_list_attr = 6;
  void clear_type_list_attr();
  const std::string& type_list_attr() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type_list_attr(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type_list_attr();
  PROTOBUF_NODISCARD std::string* release_type_list_attr();
  void set_allocated_type_list_attr(std::string* type_list_attr);
  private:
  const std::string& _internal_type_list_attr() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type_list_attr(const std::string& value);
  std::string* _internal_mutable_type_list_attr();
  public:

  // .tensorflow.FullTypeDef experimental_full_type = 17;
  bool has_experimental_full_type() const;
  private:
  bool _internal_has_experimental_full_type() const;
  public:
  void clear_experimental_full_type();
  const ::tensorflow::FullTypeDef& experimental_full_type() const;
  PROTOBUF_NODISCARD ::tensorflow::FullTypeDef* release_experimental_full_type();
  ::tensorflow::FullTypeDef* mutable_experimental_full_type();
  void set_allocated_experimental_full_type(::tensorflow::FullTypeDef* experimental_full_type);
  private:
  const ::tensorflow::FullTypeDef& _internal_experimental_full_type() const;
  ::tensorflow::FullTypeDef* _internal_mutable_experimental_full_type();
  public:
  void unsafe_arena_set_allocated_experimental_full_type(
      ::tensorflow::FullTypeDef* experimental_full_type);
  ::tensorflow::FullTypeDef* unsafe_arena_release_experimental_full_type();

  // .tensorflow.DataType type = 3;
  void clear_type();
  ::tensorflow::DataType type() const;
  void set_type(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_type() const;
  void _internal_set_type(::tensorflow::DataType value);
  public:

  // bool is_ref = 16;
  void clear_is_ref();
  bool is_ref() const;
  void set_is_ref(bool value);
  private:
  bool _internal_is_ref() const;
  void _internal_set_is_ref(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef.ArgDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape > handle_data_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_attr_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr number_attr_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_list_attr_;
    ::tensorflow::FullTypeDef* experimental_full_type_;
    int type_;
    bool is_ref_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// -------------------------------------------------------------------

class OpDef_AttrDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef.AttrDef) */ {
 public:
  inline OpDef_AttrDef() : OpDef_AttrDef(nullptr) {}
  ~OpDef_AttrDef() override;
  explicit PROTOBUF_CONSTEXPR OpDef_AttrDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpDef_AttrDef(const OpDef_AttrDef& from);
  OpDef_AttrDef(OpDef_AttrDef&& from) noexcept
    : OpDef_AttrDef() {
    *this = ::std::move(from);
  }

  inline OpDef_AttrDef& operator=(const OpDef_AttrDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpDef_AttrDef& operator=(OpDef_AttrDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpDef_AttrDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpDef_AttrDef* internal_default_instance() {
    return reinterpret_cast<const OpDef_AttrDef*>(
               &_OpDef_AttrDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(OpDef_AttrDef& a, OpDef_AttrDef& b) {
    a.Swap(&b);
  }
  inline void Swap(OpDef_AttrDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpDef_AttrDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpDef_AttrDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpDef_AttrDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpDef_AttrDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpDef_AttrDef& from) {
    OpDef_AttrDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef_AttrDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpDef.AttrDef";
  }
  protected:
  explicit OpDef_AttrDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kTypeFieldNumber = 2,
    kDescriptionFieldNumber = 4,
    kDefaultValueFieldNumber = 3,
    kAllowedValuesFieldNumber = 7,
    kMinimumFieldNumber = 6,
    kHasMinimumFieldNumber = 5,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string type = 2;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // string description = 4;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // .tensorflow.AttrValue default_value = 3;
  bool has_default_value() const;
  private:
  bool _internal_has_default_value() const;
  public:
  void clear_default_value();
  const ::tensorflow::AttrValue& default_value() const;
  PROTOBUF_NODISCARD ::tensorflow::AttrValue* release_default_value();
  ::tensorflow::AttrValue* mutable_default_value();
  void set_allocated_default_value(::tensorflow::AttrValue* default_value);
  private:
  const ::tensorflow::AttrValue& _internal_default_value() const;
  ::tensorflow::AttrValue* _internal_mutable_default_value();
  public:
  void unsafe_arena_set_allocated_default_value(
      ::tensorflow::AttrValue* default_value);
  ::tensorflow::AttrValue* unsafe_arena_release_default_value();

  // .tensorflow.AttrValue allowed_values = 7;
  bool has_allowed_values() const;
  private:
  bool _internal_has_allowed_values() const;
  public:
  void clear_allowed_values();
  const ::tensorflow::AttrValue& allowed_values() const;
  PROTOBUF_NODISCARD ::tensorflow::AttrValue* release_allowed_values();
  ::tensorflow::AttrValue* mutable_allowed_values();
  void set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values);
  private:
  const ::tensorflow::AttrValue& _internal_allowed_values() const;
  ::tensorflow::AttrValue* _internal_mutable_allowed_values();
  public:
  void unsafe_arena_set_allocated_allowed_values(
      ::tensorflow::AttrValue* allowed_values);
  ::tensorflow::AttrValue* unsafe_arena_release_allowed_values();

  // int64 minimum = 6;
  void clear_minimum();
  int64_t minimum() const;
  void set_minimum(int64_t value);
  private:
  int64_t _internal_minimum() const;
  void _internal_set_minimum(int64_t value);
  public:

  // bool has_minimum = 5;
  void clear_has_minimum();
  bool has_minimum() const;
  void set_has_minimum(bool value);
  private:
  bool _internal_has_minimum() const;
  void _internal_set_has_minimum(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef.AttrDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
    ::tensorflow::AttrValue* default_value_;
    ::tensorflow::AttrValue* allowed_values_;
    int64_t minimum_;
    bool has_minimum_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// -------------------------------------------------------------------

class OpDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDef) */ {
 public:
  inline OpDef() : OpDef(nullptr) {}
  ~OpDef() override;
  explicit PROTOBUF_CONSTEXPR OpDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpDef(const OpDef& from);
  OpDef(OpDef&& from) noexcept
    : OpDef() {
    *this = ::std::move(from);
  }

  inline OpDef& operator=(const OpDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpDef& operator=(OpDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpDef* internal_default_instance() {
    return reinterpret_cast<const OpDef*>(
               &_OpDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(OpDef& a, OpDef& b) {
    a.Swap(&b);
  }
  inline void Swap(OpDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpDef& from) {
    OpDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpDef";
  }
  protected:
  explicit OpDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef OpDef_ArgDef ArgDef;
  typedef OpDef_AttrDef AttrDef;

  // accessors -------------------------------------------------------

  enum : int {
    kInputArgFieldNumber = 2,
    kOutputArgFieldNumber = 3,
    kAttrFieldNumber = 4,
    kControlOutputFieldNumber = 20,
    kNameFieldNumber = 1,
    kSummaryFieldNumber = 5,
    kDescriptionFieldNumber = 6,
    kDeprecationFieldNumber = 8,
    kIsCommutativeFieldNumber = 18,
    kIsAggregateFieldNumber = 16,
    kIsStatefulFieldNumber = 17,
    kAllowsUninitializedInputFieldNumber = 19,
    kIsDistributedCommunicationFieldNumber = 21,
  };
  // repeated .tensorflow.OpDef.ArgDef input_arg = 2;
  int input_arg_size() const;
  private:
  int _internal_input_arg_size() const;
  public:
  void clear_input_arg();
  ::tensorflow::OpDef_ArgDef* mutable_input_arg(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
      mutable_input_arg();
  private:
  const ::tensorflow::OpDef_ArgDef& _internal_input_arg(int index) const;
  ::tensorflow::OpDef_ArgDef* _internal_add_input_arg();
  public:
  const ::tensorflow::OpDef_ArgDef& input_arg(int index) const;
  ::tensorflow::OpDef_ArgDef* add_input_arg();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
      input_arg() const;

  // repeated .tensorflow.OpDef.ArgDef output_arg = 3;
  int output_arg_size() const;
  private:
  int _internal_output_arg_size() const;
  public:
  void clear_output_arg();
  ::tensorflow::OpDef_ArgDef* mutable_output_arg(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
      mutable_output_arg();
  private:
  const ::tensorflow::OpDef_ArgDef& _internal_output_arg(int index) const;
  ::tensorflow::OpDef_ArgDef* _internal_add_output_arg();
  public:
  const ::tensorflow::OpDef_ArgDef& output_arg(int index) const;
  ::tensorflow::OpDef_ArgDef* add_output_arg();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
      output_arg() const;

  // repeated .tensorflow.OpDef.AttrDef attr = 4;
  int attr_size() const;
  private:
  int _internal_attr_size() const;
  public:
  void clear_attr();
  ::tensorflow::OpDef_AttrDef* mutable_attr(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >*
      mutable_attr();
  private:
  const ::tensorflow::OpDef_AttrDef& _internal_attr(int index) const;
  ::tensorflow::OpDef_AttrDef* _internal_add_attr();
  public:
  const ::tensorflow::OpDef_AttrDef& attr(int index) const;
  ::tensorflow::OpDef_AttrDef* add_attr();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >&
      attr() const;

  // repeated string control_output = 20;
  int control_output_size() const;
  private:
  int _internal_control_output_size() const;
  public:
  void clear_control_output();
  const std::string& control_output(int index) const;
  std::string* mutable_control_output(int index);
  void set_control_output(int index, const std::string& value);
  void set_control_output(int index, std::string&& value);
  void set_control_output(int index, const char* value);
  void set_control_output(int index, const char* value, size_t size);
  std::string* add_control_output();
  void add_control_output(const std::string& value);
  void add_control_output(std::string&& value);
  void add_control_output(const char* value);
  void add_control_output(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& control_output() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_control_output();
  private:
  const std::string& _internal_control_output(int index) const;
  std::string* _internal_add_control_output();
  public:

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string summary = 5;
  void clear_summary();
  const std::string& summary() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_summary(ArgT0&& arg0, ArgT... args);
  std::string* mutable_summary();
  PROTOBUF_NODISCARD std::string* release_summary();
  void set_allocated_summary(std::string* summary);
  private:
  const std::string& _internal_summary() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_summary(const std::string& value);
  std::string* _internal_mutable_summary();
  public:

  // string description = 6;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // .tensorflow.OpDeprecation deprecation = 8;
  bool has_deprecation() const;
  private:
  bool _internal_has_deprecation() const;
  public:
  void clear_deprecation();
  const ::tensorflow::OpDeprecation& deprecation() const;
  PROTOBUF_NODISCARD ::tensorflow::OpDeprecation* release_deprecation();
  ::tensorflow::OpDeprecation* mutable_deprecation();
  void set_allocated_deprecation(::tensorflow::OpDeprecation* deprecation);
  private:
  const ::tensorflow::OpDeprecation& _internal_deprecation() const;
  ::tensorflow::OpDeprecation* _internal_mutable_deprecation();
  public:
  void unsafe_arena_set_allocated_deprecation(
      ::tensorflow::OpDeprecation* deprecation);
  ::tensorflow::OpDeprecation* unsafe_arena_release_deprecation();

  // bool is_commutative = 18;
  void clear_is_commutative();
  bool is_commutative() const;
  void set_is_commutative(bool value);
  private:
  bool _internal_is_commutative() const;
  void _internal_set_is_commutative(bool value);
  public:

  // bool is_aggregate = 16;
  void clear_is_aggregate();
  bool is_aggregate() const;
  void set_is_aggregate(bool value);
  private:
  bool _internal_is_aggregate() const;
  void _internal_set_is_aggregate(bool value);
  public:

  // bool is_stateful = 17;
  void clear_is_stateful();
  bool is_stateful() const;
  void set_is_stateful(bool value);
  private:
  bool _internal_is_stateful() const;
  void _internal_set_is_stateful(bool value);
  public:

  // bool allows_uninitialized_input = 19;
  void clear_allows_uninitialized_input();
  bool allows_uninitialized_input() const;
  void set_allows_uninitialized_input(bool value);
  private:
  bool _internal_allows_uninitialized_input() const;
  void _internal_set_allows_uninitialized_input(bool value);
  public:

  // bool is_distributed_communication = 21;
  void clear_is_distributed_communication();
  bool is_distributed_communication() const;
  void set_is_distributed_communication(bool value);
  private:
  bool _internal_is_distributed_communication() const;
  void _internal_set_is_distributed_communication(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.OpDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef > input_arg_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef > output_arg_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef > attr_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> control_output_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr summary_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
    ::tensorflow::OpDeprecation* deprecation_;
    bool is_commutative_;
    bool is_aggregate_;
    bool is_stateful_;
    bool allows_uninitialized_input_;
    bool is_distributed_communication_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// -------------------------------------------------------------------

class OpDeprecation final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpDeprecation) */ {
 public:
  inline OpDeprecation() : OpDeprecation(nullptr) {}
  ~OpDeprecation() override;
  explicit PROTOBUF_CONSTEXPR OpDeprecation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpDeprecation(const OpDeprecation& from);
  OpDeprecation(OpDeprecation&& from) noexcept
    : OpDeprecation() {
    *this = ::std::move(from);
  }

  inline OpDeprecation& operator=(const OpDeprecation& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpDeprecation& operator=(OpDeprecation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpDeprecation& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpDeprecation* internal_default_instance() {
    return reinterpret_cast<const OpDeprecation*>(
               &_OpDeprecation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(OpDeprecation& a, OpDeprecation& b) {
    a.Swap(&b);
  }
  inline void Swap(OpDeprecation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpDeprecation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpDeprecation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpDeprecation>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpDeprecation& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpDeprecation& from) {
    OpDeprecation::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpDeprecation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpDeprecation";
  }
  protected:
  explicit OpDeprecation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExplanationFieldNumber = 2,
    kVersionFieldNumber = 1,
  };
  // string explanation = 2;
  void clear_explanation();
  const std::string& explanation() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_explanation(ArgT0&& arg0, ArgT... args);
  std::string* mutable_explanation();
  PROTOBUF_NODISCARD std::string* release_explanation();
  void set_allocated_explanation(std::string* explanation);
  private:
  const std::string& _internal_explanation() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_explanation(const std::string& value);
  std::string* _internal_mutable_explanation();
  public:

  // int32 version = 1;
  void clear_version();
  int32_t version() const;
  void set_version(int32_t value);
  private:
  int32_t _internal_version() const;
  void _internal_set_version(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.OpDeprecation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr explanation_;
    int32_t version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// -------------------------------------------------------------------

class OpList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.OpList) */ {
 public:
  inline OpList() : OpList(nullptr) {}
  ~OpList() override;
  explicit PROTOBUF_CONSTEXPR OpList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OpList(const OpList& from);
  OpList(OpList&& from) noexcept
    : OpList() {
    *this = ::std::move(from);
  }

  inline OpList& operator=(const OpList& from) {
    CopyFrom(from);
    return *this;
  }
  inline OpList& operator=(OpList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OpList& default_instance() {
    return *internal_default_instance();
  }
  static inline const OpList* internal_default_instance() {
    return reinterpret_cast<const OpList*>(
               &_OpList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(OpList& a, OpList& b) {
    a.Swap(&b);
  }
  inline void Swap(OpList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OpList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OpList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OpList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OpList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OpList& from) {
    OpList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OpList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.OpList";
  }
  protected:
  explicit OpList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpFieldNumber = 1,
  };
  // repeated .tensorflow.OpDef op = 1;
  int op_size() const;
  private:
  int _internal_op_size() const;
  public:
  void clear_op();
  ::tensorflow::OpDef* mutable_op(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef >*
      mutable_op();
  private:
  const ::tensorflow::OpDef& _internal_op(int index) const;
  ::tensorflow::OpDef* _internal_add_op();
  public:
  const ::tensorflow::OpDef& op(int index) const;
  ::tensorflow::OpDef* add_op();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef >&
      op() const;

  // @@protoc_insertion_point(class_scope:tensorflow.OpList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef > op_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// OpDef_ArgDef

// string name = 1;
inline void OpDef_ArgDef::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& OpDef_ArgDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef_ArgDef::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.name)
}
inline std::string* OpDef_ArgDef::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.name)
  return _s;
}
inline const std::string& OpDef_ArgDef::_internal_name() const {
  return _impl_.name_.Get();
}
inline void OpDef_ArgDef::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.name)
  return _impl_.name_.Release();
}
inline void OpDef_ArgDef::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.name)
}

// string description = 2;
inline void OpDef_ArgDef::clear_description() {
  _impl_.description_.ClearToEmpty();
}
inline const std::string& OpDef_ArgDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef_ArgDef::set_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.description)
}
inline std::string* OpDef_ArgDef::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.description)
  return _s;
}
inline const std::string& OpDef_ArgDef::_internal_description() const {
  return _impl_.description_.Get();
}
inline void OpDef_ArgDef::_internal_set_description(const std::string& value) {
  
  _impl_.description_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::_internal_mutable_description() {
  
  return _impl_.description_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.description)
  return _impl_.description_.Release();
}
inline void OpDef_ArgDef::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  _impl_.description_.SetAllocated(description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_.IsDefault()) {
    _impl_.description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.description)
}

// .tensorflow.DataType type = 3;
inline void OpDef_ArgDef::clear_type() {
  _impl_.type_ = 0;
}
inline ::tensorflow::DataType OpDef_ArgDef::_internal_type() const {
  return static_cast< ::tensorflow::DataType >(_impl_.type_);
}
inline ::tensorflow::DataType OpDef_ArgDef::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type)
  return _internal_type();
}
inline void OpDef_ArgDef::_internal_set_type(::tensorflow::DataType value) {
  
  _impl_.type_ = value;
}
inline void OpDef_ArgDef::set_type(::tensorflow::DataType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type)
}

// string type_attr = 4;
inline void OpDef_ArgDef::clear_type_attr() {
  _impl_.type_attr_.ClearToEmpty();
}
inline const std::string& OpDef_ArgDef::type_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type_attr)
  return _internal_type_attr();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef_ArgDef::set_type_attr(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_attr_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type_attr)
}
inline std::string* OpDef_ArgDef::mutable_type_attr() {
  std::string* _s = _internal_mutable_type_attr();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.type_attr)
  return _s;
}
inline const std::string& OpDef_ArgDef::_internal_type_attr() const {
  return _impl_.type_attr_.Get();
}
inline void OpDef_ArgDef::_internal_set_type_attr(const std::string& value) {
  
  _impl_.type_attr_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::_internal_mutable_type_attr() {
  
  return _impl_.type_attr_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::release_type_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.type_attr)
  return _impl_.type_attr_.Release();
}
inline void OpDef_ArgDef::set_allocated_type_attr(std::string* type_attr) {
  if (type_attr != nullptr) {
    
  } else {
    
  }
  _impl_.type_attr_.SetAllocated(type_attr, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_attr_.IsDefault()) {
    _impl_.type_attr_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.type_attr)
}

// string number_attr = 5;
inline void OpDef_ArgDef::clear_number_attr() {
  _impl_.number_attr_.ClearToEmpty();
}
inline const std::string& OpDef_ArgDef::number_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.number_attr)
  return _internal_number_attr();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef_ArgDef::set_number_attr(ArgT0&& arg0, ArgT... args) {
 
 _impl_.number_attr_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.number_attr)
}
inline std::string* OpDef_ArgDef::mutable_number_attr() {
  std::string* _s = _internal_mutable_number_attr();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.number_attr)
  return _s;
}
inline const std::string& OpDef_ArgDef::_internal_number_attr() const {
  return _impl_.number_attr_.Get();
}
inline void OpDef_ArgDef::_internal_set_number_attr(const std::string& value) {
  
  _impl_.number_attr_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::_internal_mutable_number_attr() {
  
  return _impl_.number_attr_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::release_number_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.number_attr)
  return _impl_.number_attr_.Release();
}
inline void OpDef_ArgDef::set_allocated_number_attr(std::string* number_attr) {
  if (number_attr != nullptr) {
    
  } else {
    
  }
  _impl_.number_attr_.SetAllocated(number_attr, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.number_attr_.IsDefault()) {
    _impl_.number_attr_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.number_attr)
}

// string type_list_attr = 6;
inline void OpDef_ArgDef::clear_type_list_attr() {
  _impl_.type_list_attr_.ClearToEmpty();
}
inline const std::string& OpDef_ArgDef::type_list_attr() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.type_list_attr)
  return _internal_type_list_attr();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef_ArgDef::set_type_list_attr(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_list_attr_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.type_list_attr)
}
inline std::string* OpDef_ArgDef::mutable_type_list_attr() {
  std::string* _s = _internal_mutable_type_list_attr();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.type_list_attr)
  return _s;
}
inline const std::string& OpDef_ArgDef::_internal_type_list_attr() const {
  return _impl_.type_list_attr_.Get();
}
inline void OpDef_ArgDef::_internal_set_type_list_attr(const std::string& value) {
  
  _impl_.type_list_attr_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::_internal_mutable_type_list_attr() {
  
  return _impl_.type_list_attr_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef_ArgDef::release_type_list_attr() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.type_list_attr)
  return _impl_.type_list_attr_.Release();
}
inline void OpDef_ArgDef::set_allocated_type_list_attr(std::string* type_list_attr) {
  if (type_list_attr != nullptr) {
    
  } else {
    
  }
  _impl_.type_list_attr_.SetAllocated(type_list_attr, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_list_attr_.IsDefault()) {
    _impl_.type_list_attr_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.type_list_attr)
}

// repeated .tensorflow.ResourceHandleProto.DtypeAndShape handle_data = 7;
inline int OpDef_ArgDef::_internal_handle_data_size() const {
  return _impl_.handle_data_.size();
}
inline int OpDef_ArgDef::handle_data_size() const {
  return _internal_handle_data_size();
}
inline ::tensorflow::ResourceHandleProto_DtypeAndShape* OpDef_ArgDef::mutable_handle_data(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.handle_data)
  return _impl_.handle_data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >*
OpDef_ArgDef::mutable_handle_data() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.ArgDef.handle_data)
  return &_impl_.handle_data_;
}
inline const ::tensorflow::ResourceHandleProto_DtypeAndShape& OpDef_ArgDef::_internal_handle_data(int index) const {
  return _impl_.handle_data_.Get(index);
}
inline const ::tensorflow::ResourceHandleProto_DtypeAndShape& OpDef_ArgDef::handle_data(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.handle_data)
  return _internal_handle_data(index);
}
inline ::tensorflow::ResourceHandleProto_DtypeAndShape* OpDef_ArgDef::_internal_add_handle_data() {
  return _impl_.handle_data_.Add();
}
inline ::tensorflow::ResourceHandleProto_DtypeAndShape* OpDef_ArgDef::add_handle_data() {
  ::tensorflow::ResourceHandleProto_DtypeAndShape* _add = _internal_add_handle_data();
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.ArgDef.handle_data)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >&
OpDef_ArgDef::handle_data() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.ArgDef.handle_data)
  return _impl_.handle_data_;
}

// bool is_ref = 16;
inline void OpDef_ArgDef::clear_is_ref() {
  _impl_.is_ref_ = false;
}
inline bool OpDef_ArgDef::_internal_is_ref() const {
  return _impl_.is_ref_;
}
inline bool OpDef_ArgDef::is_ref() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.is_ref)
  return _internal_is_ref();
}
inline void OpDef_ArgDef::_internal_set_is_ref(bool value) {
  
  _impl_.is_ref_ = value;
}
inline void OpDef_ArgDef::set_is_ref(bool value) {
  _internal_set_is_ref(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.ArgDef.is_ref)
}

// .tensorflow.FullTypeDef experimental_full_type = 17;
inline bool OpDef_ArgDef::_internal_has_experimental_full_type() const {
  return this != internal_default_instance() && _impl_.experimental_full_type_ != nullptr;
}
inline bool OpDef_ArgDef::has_experimental_full_type() const {
  return _internal_has_experimental_full_type();
}
inline const ::tensorflow::FullTypeDef& OpDef_ArgDef::_internal_experimental_full_type() const {
  const ::tensorflow::FullTypeDef* p = _impl_.experimental_full_type_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::FullTypeDef&>(
      ::tensorflow::_FullTypeDef_default_instance_);
}
inline const ::tensorflow::FullTypeDef& OpDef_ArgDef::experimental_full_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.ArgDef.experimental_full_type)
  return _internal_experimental_full_type();
}
inline void OpDef_ArgDef::unsafe_arena_set_allocated_experimental_full_type(
    ::tensorflow::FullTypeDef* experimental_full_type) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.experimental_full_type_);
  }
  _impl_.experimental_full_type_ = experimental_full_type;
  if (experimental_full_type) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.ArgDef.experimental_full_type)
}
inline ::tensorflow::FullTypeDef* OpDef_ArgDef::release_experimental_full_type() {
  
  ::tensorflow::FullTypeDef* temp = _impl_.experimental_full_type_;
  _impl_.experimental_full_type_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::FullTypeDef* OpDef_ArgDef::unsafe_arena_release_experimental_full_type() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.ArgDef.experimental_full_type)
  
  ::tensorflow::FullTypeDef* temp = _impl_.experimental_full_type_;
  _impl_.experimental_full_type_ = nullptr;
  return temp;
}
inline ::tensorflow::FullTypeDef* OpDef_ArgDef::_internal_mutable_experimental_full_type() {
  
  if (_impl_.experimental_full_type_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::FullTypeDef>(GetArenaForAllocation());
    _impl_.experimental_full_type_ = p;
  }
  return _impl_.experimental_full_type_;
}
inline ::tensorflow::FullTypeDef* OpDef_ArgDef::mutable_experimental_full_type() {
  ::tensorflow::FullTypeDef* _msg = _internal_mutable_experimental_full_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.ArgDef.experimental_full_type)
  return _msg;
}
inline void OpDef_ArgDef::set_allocated_experimental_full_type(::tensorflow::FullTypeDef* experimental_full_type) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.experimental_full_type_);
  }
  if (experimental_full_type) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(experimental_full_type));
    if (message_arena != submessage_arena) {
      experimental_full_type = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, experimental_full_type, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.experimental_full_type_ = experimental_full_type;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.ArgDef.experimental_full_type)
}

// -------------------------------------------------------------------

// OpDef_AttrDef

// string name = 1;
inline void OpDef_AttrDef::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& OpDef_AttrDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef_AttrDef::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.name)
}
inline std::string* OpDef_AttrDef::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.name)
  return _s;
}
inline const std::string& OpDef_AttrDef::_internal_name() const {
  return _impl_.name_.Get();
}
inline void OpDef_AttrDef::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef_AttrDef::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef_AttrDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.name)
  return _impl_.name_.Release();
}
inline void OpDef_AttrDef::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.name)
}

// string type = 2;
inline void OpDef_AttrDef::clear_type() {
  _impl_.type_.ClearToEmpty();
}
inline const std::string& OpDef_AttrDef::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef_AttrDef::set_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.type)
}
inline std::string* OpDef_AttrDef::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.type)
  return _s;
}
inline const std::string& OpDef_AttrDef::_internal_type() const {
  return _impl_.type_.Get();
}
inline void OpDef_AttrDef::_internal_set_type(const std::string& value) {
  
  _impl_.type_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef_AttrDef::_internal_mutable_type() {
  
  return _impl_.type_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef_AttrDef::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.type)
  return _impl_.type_.Release();
}
inline void OpDef_AttrDef::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  _impl_.type_.SetAllocated(type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_.IsDefault()) {
    _impl_.type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.type)
}

// .tensorflow.AttrValue default_value = 3;
inline bool OpDef_AttrDef::_internal_has_default_value() const {
  return this != internal_default_instance() && _impl_.default_value_ != nullptr;
}
inline bool OpDef_AttrDef::has_default_value() const {
  return _internal_has_default_value();
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::_internal_default_value() const {
  const ::tensorflow::AttrValue* p = _impl_.default_value_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::AttrValue&>(
      ::tensorflow::_AttrValue_default_instance_);
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::default_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.default_value)
  return _internal_default_value();
}
inline void OpDef_AttrDef::unsafe_arena_set_allocated_default_value(
    ::tensorflow::AttrValue* default_value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.default_value_);
  }
  _impl_.default_value_ = default_value;
  if (default_value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.default_value)
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::release_default_value() {
  
  ::tensorflow::AttrValue* temp = _impl_.default_value_;
  _impl_.default_value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::unsafe_arena_release_default_value() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.default_value)
  
  ::tensorflow::AttrValue* temp = _impl_.default_value_;
  _impl_.default_value_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::_internal_mutable_default_value() {
  
  if (_impl_.default_value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaForAllocation());
    _impl_.default_value_ = p;
  }
  return _impl_.default_value_;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::mutable_default_value() {
  ::tensorflow::AttrValue* _msg = _internal_mutable_default_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.default_value)
  return _msg;
}
inline void OpDef_AttrDef::set_allocated_default_value(::tensorflow::AttrValue* default_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.default_value_);
  }
  if (default_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(default_value));
    if (message_arena != submessage_arena) {
      default_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, default_value, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.default_value_ = default_value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.default_value)
}

// string description = 4;
inline void OpDef_AttrDef::clear_description() {
  _impl_.description_.ClearToEmpty();
}
inline const std::string& OpDef_AttrDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef_AttrDef::set_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.description)
}
inline std::string* OpDef_AttrDef::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.description)
  return _s;
}
inline const std::string& OpDef_AttrDef::_internal_description() const {
  return _impl_.description_.Get();
}
inline void OpDef_AttrDef::_internal_set_description(const std::string& value) {
  
  _impl_.description_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef_AttrDef::_internal_mutable_description() {
  
  return _impl_.description_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef_AttrDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.description)
  return _impl_.description_.Release();
}
inline void OpDef_AttrDef::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  _impl_.description_.SetAllocated(description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_.IsDefault()) {
    _impl_.description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.description)
}

// bool has_minimum = 5;
inline void OpDef_AttrDef::clear_has_minimum() {
  _impl_.has_minimum_ = false;
}
inline bool OpDef_AttrDef::_internal_has_minimum() const {
  return _impl_.has_minimum_;
}
inline bool OpDef_AttrDef::has_minimum() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.has_minimum)
  return _internal_has_minimum();
}
inline void OpDef_AttrDef::_internal_set_has_minimum(bool value) {
  
  _impl_.has_minimum_ = value;
}
inline void OpDef_AttrDef::set_has_minimum(bool value) {
  _internal_set_has_minimum(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.has_minimum)
}

// int64 minimum = 6;
inline void OpDef_AttrDef::clear_minimum() {
  _impl_.minimum_ = int64_t{0};
}
inline int64_t OpDef_AttrDef::_internal_minimum() const {
  return _impl_.minimum_;
}
inline int64_t OpDef_AttrDef::minimum() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.minimum)
  return _internal_minimum();
}
inline void OpDef_AttrDef::_internal_set_minimum(int64_t value) {
  
  _impl_.minimum_ = value;
}
inline void OpDef_AttrDef::set_minimum(int64_t value) {
  _internal_set_minimum(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.AttrDef.minimum)
}

// .tensorflow.AttrValue allowed_values = 7;
inline bool OpDef_AttrDef::_internal_has_allowed_values() const {
  return this != internal_default_instance() && _impl_.allowed_values_ != nullptr;
}
inline bool OpDef_AttrDef::has_allowed_values() const {
  return _internal_has_allowed_values();
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::_internal_allowed_values() const {
  const ::tensorflow::AttrValue* p = _impl_.allowed_values_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::AttrValue&>(
      ::tensorflow::_AttrValue_default_instance_);
}
inline const ::tensorflow::AttrValue& OpDef_AttrDef::allowed_values() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.AttrDef.allowed_values)
  return _internal_allowed_values();
}
inline void OpDef_AttrDef::unsafe_arena_set_allocated_allowed_values(
    ::tensorflow::AttrValue* allowed_values) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.allowed_values_);
  }
  _impl_.allowed_values_ = allowed_values;
  if (allowed_values) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.AttrDef.allowed_values)
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::release_allowed_values() {
  
  ::tensorflow::AttrValue* temp = _impl_.allowed_values_;
  _impl_.allowed_values_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::unsafe_arena_release_allowed_values() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.AttrDef.allowed_values)
  
  ::tensorflow::AttrValue* temp = _impl_.allowed_values_;
  _impl_.allowed_values_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::_internal_mutable_allowed_values() {
  
  if (_impl_.allowed_values_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaForAllocation());
    _impl_.allowed_values_ = p;
  }
  return _impl_.allowed_values_;
}
inline ::tensorflow::AttrValue* OpDef_AttrDef::mutable_allowed_values() {
  ::tensorflow::AttrValue* _msg = _internal_mutable_allowed_values();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.AttrDef.allowed_values)
  return _msg;
}
inline void OpDef_AttrDef::set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.allowed_values_);
  }
  if (allowed_values) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(allowed_values));
    if (message_arena != submessage_arena) {
      allowed_values = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, allowed_values, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.allowed_values_ = allowed_values;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.AttrDef.allowed_values)
}

// -------------------------------------------------------------------

// OpDef

// string name = 1;
inline void OpDef::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& OpDef::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.name)
}
inline std::string* OpDef::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.name)
  return _s;
}
inline const std::string& OpDef::_internal_name() const {
  return _impl_.name_.Get();
}
inline void OpDef::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.name)
  return _impl_.name_.Release();
}
inline void OpDef::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.name)
}

// repeated .tensorflow.OpDef.ArgDef input_arg = 2;
inline int OpDef::_internal_input_arg_size() const {
  return _impl_.input_arg_.size();
}
inline int OpDef::input_arg_size() const {
  return _internal_input_arg_size();
}
inline void OpDef::clear_input_arg() {
  _impl_.input_arg_.Clear();
}
inline ::tensorflow::OpDef_ArgDef* OpDef::mutable_input_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.input_arg)
  return _impl_.input_arg_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
OpDef::mutable_input_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.input_arg)
  return &_impl_.input_arg_;
}
inline const ::tensorflow::OpDef_ArgDef& OpDef::_internal_input_arg(int index) const {
  return _impl_.input_arg_.Get(index);
}
inline const ::tensorflow::OpDef_ArgDef& OpDef::input_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.input_arg)
  return _internal_input_arg(index);
}
inline ::tensorflow::OpDef_ArgDef* OpDef::_internal_add_input_arg() {
  return _impl_.input_arg_.Add();
}
inline ::tensorflow::OpDef_ArgDef* OpDef::add_input_arg() {
  ::tensorflow::OpDef_ArgDef* _add = _internal_add_input_arg();
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.input_arg)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
OpDef::input_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.input_arg)
  return _impl_.input_arg_;
}

// repeated .tensorflow.OpDef.ArgDef output_arg = 3;
inline int OpDef::_internal_output_arg_size() const {
  return _impl_.output_arg_.size();
}
inline int OpDef::output_arg_size() const {
  return _internal_output_arg_size();
}
inline void OpDef::clear_output_arg() {
  _impl_.output_arg_.Clear();
}
inline ::tensorflow::OpDef_ArgDef* OpDef::mutable_output_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.output_arg)
  return _impl_.output_arg_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >*
OpDef::mutable_output_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.output_arg)
  return &_impl_.output_arg_;
}
inline const ::tensorflow::OpDef_ArgDef& OpDef::_internal_output_arg(int index) const {
  return _impl_.output_arg_.Get(index);
}
inline const ::tensorflow::OpDef_ArgDef& OpDef::output_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.output_arg)
  return _internal_output_arg(index);
}
inline ::tensorflow::OpDef_ArgDef* OpDef::_internal_add_output_arg() {
  return _impl_.output_arg_.Add();
}
inline ::tensorflow::OpDef_ArgDef* OpDef::add_output_arg() {
  ::tensorflow::OpDef_ArgDef* _add = _internal_add_output_arg();
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.output_arg)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_ArgDef >&
OpDef::output_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.output_arg)
  return _impl_.output_arg_;
}

// repeated string control_output = 20;
inline int OpDef::_internal_control_output_size() const {
  return _impl_.control_output_.size();
}
inline int OpDef::control_output_size() const {
  return _internal_control_output_size();
}
inline void OpDef::clear_control_output() {
  _impl_.control_output_.Clear();
}
inline std::string* OpDef::add_control_output() {
  std::string* _s = _internal_add_control_output();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.OpDef.control_output)
  return _s;
}
inline const std::string& OpDef::_internal_control_output(int index) const {
  return _impl_.control_output_.Get(index);
}
inline const std::string& OpDef::control_output(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.control_output)
  return _internal_control_output(index);
}
inline std::string* OpDef::mutable_control_output(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.control_output)
  return _impl_.control_output_.Mutable(index);
}
inline void OpDef::set_control_output(int index, const std::string& value) {
  _impl_.control_output_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.control_output)
}
inline void OpDef::set_control_output(int index, std::string&& value) {
  _impl_.control_output_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.control_output)
}
inline void OpDef::set_control_output(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.control_output_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.OpDef.control_output)
}
inline void OpDef::set_control_output(int index, const char* value, size_t size) {
  _impl_.control_output_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.OpDef.control_output)
}
inline std::string* OpDef::_internal_add_control_output() {
  return _impl_.control_output_.Add();
}
inline void OpDef::add_control_output(const std::string& value) {
  _impl_.control_output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.control_output)
}
inline void OpDef::add_control_output(std::string&& value) {
  _impl_.control_output_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.control_output)
}
inline void OpDef::add_control_output(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.control_output_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.OpDef.control_output)
}
inline void OpDef::add_control_output(const char* value, size_t size) {
  _impl_.control_output_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.OpDef.control_output)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
OpDef::control_output() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.control_output)
  return _impl_.control_output_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
OpDef::mutable_control_output() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.control_output)
  return &_impl_.control_output_;
}

// repeated .tensorflow.OpDef.AttrDef attr = 4;
inline int OpDef::_internal_attr_size() const {
  return _impl_.attr_.size();
}
inline int OpDef::attr_size() const {
  return _internal_attr_size();
}
inline void OpDef::clear_attr() {
  _impl_.attr_.Clear();
}
inline ::tensorflow::OpDef_AttrDef* OpDef::mutable_attr(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.attr)
  return _impl_.attr_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >*
OpDef::mutable_attr() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpDef.attr)
  return &_impl_.attr_;
}
inline const ::tensorflow::OpDef_AttrDef& OpDef::_internal_attr(int index) const {
  return _impl_.attr_.Get(index);
}
inline const ::tensorflow::OpDef_AttrDef& OpDef::attr(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.attr)
  return _internal_attr(index);
}
inline ::tensorflow::OpDef_AttrDef* OpDef::_internal_add_attr() {
  return _impl_.attr_.Add();
}
inline ::tensorflow::OpDef_AttrDef* OpDef::add_attr() {
  ::tensorflow::OpDef_AttrDef* _add = _internal_add_attr();
  // @@protoc_insertion_point(field_add:tensorflow.OpDef.attr)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef_AttrDef >&
OpDef::attr() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpDef.attr)
  return _impl_.attr_;
}

// .tensorflow.OpDeprecation deprecation = 8;
inline bool OpDef::_internal_has_deprecation() const {
  return this != internal_default_instance() && _impl_.deprecation_ != nullptr;
}
inline bool OpDef::has_deprecation() const {
  return _internal_has_deprecation();
}
inline void OpDef::clear_deprecation() {
  if (GetArenaForAllocation() == nullptr && _impl_.deprecation_ != nullptr) {
    delete _impl_.deprecation_;
  }
  _impl_.deprecation_ = nullptr;
}
inline const ::tensorflow::OpDeprecation& OpDef::_internal_deprecation() const {
  const ::tensorflow::OpDeprecation* p = _impl_.deprecation_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::OpDeprecation&>(
      ::tensorflow::_OpDeprecation_default_instance_);
}
inline const ::tensorflow::OpDeprecation& OpDef::deprecation() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.deprecation)
  return _internal_deprecation();
}
inline void OpDef::unsafe_arena_set_allocated_deprecation(
    ::tensorflow::OpDeprecation* deprecation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.deprecation_);
  }
  _impl_.deprecation_ = deprecation;
  if (deprecation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.OpDef.deprecation)
}
inline ::tensorflow::OpDeprecation* OpDef::release_deprecation() {
  
  ::tensorflow::OpDeprecation* temp = _impl_.deprecation_;
  _impl_.deprecation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::OpDeprecation* OpDef::unsafe_arena_release_deprecation() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.deprecation)
  
  ::tensorflow::OpDeprecation* temp = _impl_.deprecation_;
  _impl_.deprecation_ = nullptr;
  return temp;
}
inline ::tensorflow::OpDeprecation* OpDef::_internal_mutable_deprecation() {
  
  if (_impl_.deprecation_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpDeprecation>(GetArenaForAllocation());
    _impl_.deprecation_ = p;
  }
  return _impl_.deprecation_;
}
inline ::tensorflow::OpDeprecation* OpDef::mutable_deprecation() {
  ::tensorflow::OpDeprecation* _msg = _internal_mutable_deprecation();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.deprecation)
  return _msg;
}
inline void OpDef::set_allocated_deprecation(::tensorflow::OpDeprecation* deprecation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.deprecation_;
  }
  if (deprecation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(deprecation);
    if (message_arena != submessage_arena) {
      deprecation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, deprecation, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.deprecation_ = deprecation;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.deprecation)
}

// string summary = 5;
inline void OpDef::clear_summary() {
  _impl_.summary_.ClearToEmpty();
}
inline const std::string& OpDef::summary() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.summary)
  return _internal_summary();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef::set_summary(ArgT0&& arg0, ArgT... args) {
 
 _impl_.summary_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.summary)
}
inline std::string* OpDef::mutable_summary() {
  std::string* _s = _internal_mutable_summary();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.summary)
  return _s;
}
inline const std::string& OpDef::_internal_summary() const {
  return _impl_.summary_.Get();
}
inline void OpDef::_internal_set_summary(const std::string& value) {
  
  _impl_.summary_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef::_internal_mutable_summary() {
  
  return _impl_.summary_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef::release_summary() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.summary)
  return _impl_.summary_.Release();
}
inline void OpDef::set_allocated_summary(std::string* summary) {
  if (summary != nullptr) {
    
  } else {
    
  }
  _impl_.summary_.SetAllocated(summary, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.summary_.IsDefault()) {
    _impl_.summary_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.summary)
}

// string description = 6;
inline void OpDef::clear_description() {
  _impl_.description_.ClearToEmpty();
}
inline const std::string& OpDef::description() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDef::set_description(ArgT0&& arg0, ArgT... args) {
 
 _impl_.description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.description)
}
inline std::string* OpDef::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDef.description)
  return _s;
}
inline const std::string& OpDef::_internal_description() const {
  return _impl_.description_.Get();
}
inline void OpDef::_internal_set_description(const std::string& value) {
  
  _impl_.description_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDef::_internal_mutable_description() {
  
  return _impl_.description_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDef::release_description() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDef.description)
  return _impl_.description_.Release();
}
inline void OpDef::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  _impl_.description_.SetAllocated(description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.description_.IsDefault()) {
    _impl_.description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDef.description)
}

// bool is_commutative = 18;
inline void OpDef::clear_is_commutative() {
  _impl_.is_commutative_ = false;
}
inline bool OpDef::_internal_is_commutative() const {
  return _impl_.is_commutative_;
}
inline bool OpDef::is_commutative() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_commutative)
  return _internal_is_commutative();
}
inline void OpDef::_internal_set_is_commutative(bool value) {
  
  _impl_.is_commutative_ = value;
}
inline void OpDef::set_is_commutative(bool value) {
  _internal_set_is_commutative(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_commutative)
}

// bool is_aggregate = 16;
inline void OpDef::clear_is_aggregate() {
  _impl_.is_aggregate_ = false;
}
inline bool OpDef::_internal_is_aggregate() const {
  return _impl_.is_aggregate_;
}
inline bool OpDef::is_aggregate() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_aggregate)
  return _internal_is_aggregate();
}
inline void OpDef::_internal_set_is_aggregate(bool value) {
  
  _impl_.is_aggregate_ = value;
}
inline void OpDef::set_is_aggregate(bool value) {
  _internal_set_is_aggregate(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_aggregate)
}

// bool is_stateful = 17;
inline void OpDef::clear_is_stateful() {
  _impl_.is_stateful_ = false;
}
inline bool OpDef::_internal_is_stateful() const {
  return _impl_.is_stateful_;
}
inline bool OpDef::is_stateful() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_stateful)
  return _internal_is_stateful();
}
inline void OpDef::_internal_set_is_stateful(bool value) {
  
  _impl_.is_stateful_ = value;
}
inline void OpDef::set_is_stateful(bool value) {
  _internal_set_is_stateful(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_stateful)
}

// bool allows_uninitialized_input = 19;
inline void OpDef::clear_allows_uninitialized_input() {
  _impl_.allows_uninitialized_input_ = false;
}
inline bool OpDef::_internal_allows_uninitialized_input() const {
  return _impl_.allows_uninitialized_input_;
}
inline bool OpDef::allows_uninitialized_input() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.allows_uninitialized_input)
  return _internal_allows_uninitialized_input();
}
inline void OpDef::_internal_set_allows_uninitialized_input(bool value) {
  
  _impl_.allows_uninitialized_input_ = value;
}
inline void OpDef::set_allows_uninitialized_input(bool value) {
  _internal_set_allows_uninitialized_input(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.allows_uninitialized_input)
}

// bool is_distributed_communication = 21;
inline void OpDef::clear_is_distributed_communication() {
  _impl_.is_distributed_communication_ = false;
}
inline bool OpDef::_internal_is_distributed_communication() const {
  return _impl_.is_distributed_communication_;
}
inline bool OpDef::is_distributed_communication() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDef.is_distributed_communication)
  return _internal_is_distributed_communication();
}
inline void OpDef::_internal_set_is_distributed_communication(bool value) {
  
  _impl_.is_distributed_communication_ = value;
}
inline void OpDef::set_is_distributed_communication(bool value) {
  _internal_set_is_distributed_communication(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDef.is_distributed_communication)
}

// -------------------------------------------------------------------

// OpDeprecation

// int32 version = 1;
inline void OpDeprecation::clear_version() {
  _impl_.version_ = 0;
}
inline int32_t OpDeprecation::_internal_version() const {
  return _impl_.version_;
}
inline int32_t OpDeprecation::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDeprecation.version)
  return _internal_version();
}
inline void OpDeprecation::_internal_set_version(int32_t value) {
  
  _impl_.version_ = value;
}
inline void OpDeprecation::set_version(int32_t value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.OpDeprecation.version)
}

// string explanation = 2;
inline void OpDeprecation::clear_explanation() {
  _impl_.explanation_.ClearToEmpty();
}
inline const std::string& OpDeprecation::explanation() const {
  // @@protoc_insertion_point(field_get:tensorflow.OpDeprecation.explanation)
  return _internal_explanation();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OpDeprecation::set_explanation(ArgT0&& arg0, ArgT... args) {
 
 _impl_.explanation_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.OpDeprecation.explanation)
}
inline std::string* OpDeprecation::mutable_explanation() {
  std::string* _s = _internal_mutable_explanation();
  // @@protoc_insertion_point(field_mutable:tensorflow.OpDeprecation.explanation)
  return _s;
}
inline const std::string& OpDeprecation::_internal_explanation() const {
  return _impl_.explanation_.Get();
}
inline void OpDeprecation::_internal_set_explanation(const std::string& value) {
  
  _impl_.explanation_.Set(value, GetArenaForAllocation());
}
inline std::string* OpDeprecation::_internal_mutable_explanation() {
  
  return _impl_.explanation_.Mutable(GetArenaForAllocation());
}
inline std::string* OpDeprecation::release_explanation() {
  // @@protoc_insertion_point(field_release:tensorflow.OpDeprecation.explanation)
  return _impl_.explanation_.Release();
}
inline void OpDeprecation::set_allocated_explanation(std::string* explanation) {
  if (explanation != nullptr) {
    
  } else {
    
  }
  _impl_.explanation_.SetAllocated(explanation, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.explanation_.IsDefault()) {
    _impl_.explanation_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.OpDeprecation.explanation)
}

// -------------------------------------------------------------------

// OpList

// repeated .tensorflow.OpDef op = 1;
inline int OpList::_internal_op_size() const {
  return _impl_.op_.size();
}
inline int OpList::op_size() const {
  return _internal_op_size();
}
inline void OpList::clear_op() {
  _impl_.op_.Clear();
}
inline ::tensorflow::OpDef* OpList::mutable_op(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.OpList.op)
  return _impl_.op_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef >*
OpList::mutable_op() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.OpList.op)
  return &_impl_.op_;
}
inline const ::tensorflow::OpDef& OpList::_internal_op(int index) const {
  return _impl_.op_.Get(index);
}
inline const ::tensorflow::OpDef& OpList::op(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.OpList.op)
  return _internal_op(index);
}
inline ::tensorflow::OpDef* OpList::_internal_add_op() {
  return _impl_.op_.Add();
}
inline ::tensorflow::OpDef* OpList::add_op() {
  ::tensorflow::OpDef* _add = _internal_add_op();
  // @@protoc_insertion_point(field_add:tensorflow.OpList.op)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::OpDef >&
OpList::op() const {
  // @@protoc_insertion_point(field_list:tensorflow.OpList.op)
  return _impl_.op_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fop_5fdef_2eproto
