syntax = "proto3";

package tensorflow;

// Next ID: 12
message ProfileOptions {
  // Some default value of option are not proto3 default value. Use this version
  // to determine if we should use default option value instead of proto3
  // default value.
  uint32 version = 5;

  enum DeviceType {
    UNSPECIFIED = 0;
    CPU = 1;
    GPU = 2;
    TPU = 3;
    PLUGGABLE_DEVICE = 4;
  }

  // Device type to profile/trace: (version >= 1)
  // DeviceType::UNSPECIFIED: All registered device profiler will be enabled.
  // DeviceType::CPU: only CPU will be profiled.
  // DeviceType::GPU: only CPU/GPU will be profiled.
  // DeviceType::TPU: only CPU/TPU will be profiled.
  // DeviceType::PLUGGABLE_DEVICE: only CPU/pluggable devices with profilers
  // will be profiled.
  DeviceType device_type = 6;

  // We don't collect the dataset ops by default for better trace-viewer
  // scalability. The caller can manually set this field to include the ops.
  bool include_dataset_ops = 1;

  // Levels of host tracing: (version >= 1)
  // - Level 0 is used to disable host traces.
  // - Level 1 enables tracing of only user instrumented (or default) TraceMe.
  // - Level 2 enables tracing of all level 1 TraceMe(s) and instrumented high
  //           level program execution details (expensive TF ops, XLA ops, etc).
  //           This is the default.
  // - Level 3 enables tracing of all level 2 TraceMe(s) and more verbose
  //           (low-level) program execution details (cheap TF ops, etc).
  uint32 host_tracer_level = 2;

  // Levels of device tracing: (version >= 1)
  // - Level 0 is used to disable device traces.
  // - Level 1 is used to enable device traces.
  // - More levels might be defined for specific device for controlling the
  //   verbosity of the trace.
  uint32 device_tracer_level = 3;

  // Whether enable python function calls tracing. Runtime overhead ensues if
  // enabled. Default off. (version >= 1)
  uint32 python_tracer_level = 4;

  // Whether serialize hlo_proto when XLA is used. (version >= 1)
  bool enable_hlo_proto = 7;

  // The local profiler starts profiling at this Unix timestamp in nanoseconds.
  uint64 start_timestamp_ns = 8;

  // The local profiler collects `duration_ms` milliseconds of data. If the
  // value is 0, profiling continues until interrupted.
  uint64 duration_ms = 9;

  // Directory to save profile data to. No-op when empty.
  string repository_path = 10;

  message TraceOptions {
    // Filter mask for TraceMe events. If the traceme_filter_mask is set, a
    // TraceMe event will be recorded if it passes the filter.
    // Only lowest 32 bits of the mask are used. The higher 32 bits are reserved
    // and won't be applied if set.
    uint64 host_traceme_filter_mask = 1;
  }

  TraceOptions trace_options = 11;
}

// Options for remote profiler session manager.
// Next ID: 6
message RemoteProfilerSessionManagerOptions {
  // Options for each local profiler.
  ProfileOptions profiler_options = 1;

  // List of servers to profile. Supported formats: host:port.
  repeated string service_addresses = 2;

  // Unix timestamp of when the session was started.
  uint64 session_creation_timestamp_ns = 3;

  // Maximum time (in milliseconds) a profiling session manager waits for all
  // profilers to finish after issuing gRPC request. If value is 0, session
  // continues until interrupted. Otherwise, value must be greater than
  // profiler_options.duration_ms.
  uint64 max_session_duration_ms = 4;

  // Start of profiling is delayed by this much (in milliseconds).
  uint64 delay_ms = 5;
}
