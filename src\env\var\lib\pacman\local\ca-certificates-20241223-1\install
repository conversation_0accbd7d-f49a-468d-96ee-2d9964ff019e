export LC_ALL=C

post_install() {
  local DEST=etc/pki/ca-trust/extracted

  # OpenSSL PEM bundle that includes trust flags
  # (BEGIN TRUSTED CERTIFICATE)
  usr/bin/p11-kit.exe extract --format=openssl-bundle --filter=certificates --overwrite --comment $DEST/openssl/ca-bundle.trust.crt
  usr/bin/p11-kit.exe extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose server-auth $DEST/pem/tls-ca-bundle.pem
  usr/bin/p11-kit.exe extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose email $DEST/pem/email-ca-bundle.pem
  usr/bin/p11-kit.exe extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose code-signing $DEST/pem/objsign-ca-bundle.pem
  usr/bin/p11-kit.exe extract --format=java-cacerts --filter=ca-anchors --overwrite --purpose server-auth $DEST/java/cacerts

  mkdir -p usr/ssl/certs
  cp -f $DEST/pem/tls-ca-bundle.pem usr/ssl/certs/ca-bundle.crt
  cp -f $DEST/pem/tls-ca-bundle.pem usr/ssl/cert.pem
  cp -f $DEST/openssl/ca-bundle.trust.crt usr/ssl/certs/ca-bundle.trust.crt

  #usr/bin/update-ca-trust >/dev/null 2>&1
}

post_upgrade() {
  post_install
}

