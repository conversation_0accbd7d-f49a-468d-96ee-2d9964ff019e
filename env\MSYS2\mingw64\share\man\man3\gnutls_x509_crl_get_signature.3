.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_get_signature" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_get_signature \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_get_signature(gnutls_x509_crl_t " crl ", char * " sig ", size_t * " sizeof_sig ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
should contain a gnutls_x509_crl_t type
.IP "char * sig" 12
a pointer where the signature part will be copied (may be null).
.IP "size_t * sizeof_sig" 12
initially holds the size of  \fIsig\fP 
.SH "DESCRIPTION"
This function will extract the signature field of a CRL.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value. 
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
