/*** Autogenerated by WIDL 10.8 from include/windows.security.authorization.appcapabilityaccess.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_security_authorization_appcapabilityaccess_h__
#define __windows_security_authorization_appcapabilityaccess_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapability
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    interface IAppCapability;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapability2
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    interface IAppCapability2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapabilityAccessChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    interface IAppCapabilityAccessChangedEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapabilityStatics
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    interface IAppCapabilityStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapability_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapability_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    class AppCapability;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapability __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapability;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapability_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    class AppCapabilityAccessChangedEventArgs;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessChangedEventArgs __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessChangedEventArgs_FWD_DEFINED__ */

#ifndef ____FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_AppCapabilityAccessStatus __FIMapView_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_AppCapabilityAccessStatus __FIAsyncOperation_1_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1_AppCapabilityAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapability*,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessChangedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.system.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapability
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    interface IAppCapability;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapability2
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    interface IAppCapability2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapabilityAccessChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    interface IAppCapabilityAccessChangedEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapabilityStatics
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    interface IAppCapabilityStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_AppCapabilityAccessStatus __FIMapView_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_AppCapabilityAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1_AppCapabilityAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_AppCapabilityAccessStatus __FIAsyncOperation_1_AppCapabilityAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1_AppCapabilityAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    enum AppCapabilityAccessStatus {
                        AppCapabilityAccessStatus_DeniedBySystem = 0,
                        AppCapabilityAccessStatus_NotDeclaredByApp = 1,
                        AppCapabilityAccessStatus_DeniedByUser = 2,
                        AppCapabilityAccessStatus_UserPromptRequired = 3,
                        AppCapabilityAccessStatus_Allowed = 4
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus {
    AppCapabilityAccessStatus_DeniedBySystem = 0,
    AppCapabilityAccessStatus_NotDeclaredByApp = 1,
    AppCapabilityAccessStatus_DeniedByUser = 2,
    AppCapabilityAccessStatus_UserPromptRequired = 3,
    AppCapabilityAccessStatus_Allowed = 4
};
#ifdef WIDL_using_Windows_Security_Authorization_AppCapabilityAccess
#define AppCapabilityAccessStatus __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus
#endif /* WIDL_using_Windows_Security_Authorization_AppCapabilityAccess */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */
/*****************************************************************************
 * IAppCapability interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability, 0x4c49d915, 0x8a2a, 0x4295, 0x94,0x37, 0x2d,0xf7,0xc3,0x96,0xaf,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    MIDL_INTERFACE("4c49d915-8a2a-4295-9437-2df7c396aff4")
                    IAppCapability : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_CapabilityName(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_User(
                            ABI::Windows::System::IUser **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RequestAccessAsync(
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > **operation) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CheckAccess(
                            ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus *result) = 0;

                        virtual HRESULT STDMETHODCALLTYPE add_AccessChanged(
                            ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapability*,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessChangedEventArgs* > *handler,
                            EventRegistrationToken *token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE remove_AccessChanged(
                            EventRegistrationToken token) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability, 0x4c49d915, 0x8a2a, 0x4295, 0x94,0x37, 0x2d,0xf7,0xc3,0x96,0xaf,0xf4)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        TrustLevel *trustLevel);

    /*** IAppCapability methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CapabilityName)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_User)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        __x_ABI_CWindows_CSystem_CIUser **value);

    HRESULT (STDMETHODCALLTYPE *RequestAccessAsync)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        __FIAsyncOperation_1_AppCapabilityAccessStatus **operation);

    HRESULT (STDMETHODCALLTYPE *CheckAccess)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus *result);

    HRESULT (STDMETHODCALLTYPE *add_AccessChanged)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_AccessChanged)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityVtbl;

interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppCapability methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_get_CapabilityName(This,value) (This)->lpVtbl->get_CapabilityName(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_get_User(This,value) (This)->lpVtbl->get_User(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_RequestAccessAsync(This,operation) (This)->lpVtbl->RequestAccessAsync(This,operation)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_CheckAccess(This,result) (This)->lpVtbl->CheckAccess(This,result)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_add_AccessChanged(This,handler,token) (This)->lpVtbl->add_AccessChanged(This,handler,token)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_remove_AccessChanged(This,token) (This)->lpVtbl->remove_AccessChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_AddRef(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_Release(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetIids(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppCapability methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_get_CapabilityName(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,HSTRING *value) {
    return This->lpVtbl->get_CapabilityName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_get_User(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,__x_ABI_CWindows_CSystem_CIUser **value) {
    return This->lpVtbl->get_User(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_RequestAccessAsync(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,__FIAsyncOperation_1_AppCapabilityAccessStatus **operation) {
    return This->lpVtbl->RequestAccessAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_CheckAccess(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus *result) {
    return This->lpVtbl->CheckAccess(This,result);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_add_AccessChanged(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,__FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_AccessChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_remove_AccessChanged(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_AccessChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Security_Authorization_AppCapabilityAccess
#define IID_IAppCapability IID___x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability
#define IAppCapabilityVtbl __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityVtbl
#define IAppCapability __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability
#define IAppCapability_QueryInterface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_QueryInterface
#define IAppCapability_AddRef __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_AddRef
#define IAppCapability_Release __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_Release
#define IAppCapability_GetIids __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetIids
#define IAppCapability_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetRuntimeClassName
#define IAppCapability_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_GetTrustLevel
#define IAppCapability_get_CapabilityName __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_get_CapabilityName
#define IAppCapability_get_User __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_get_User
#define IAppCapability_RequestAccessAsync __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_RequestAccessAsync
#define IAppCapability_CheckAccess __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_CheckAccess
#define IAppCapability_add_AccessChanged __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_add_AccessChanged
#define IAppCapability_remove_AccessChanged __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_remove_AccessChanged
#endif /* WIDL_using_Windows_Security_Authorization_AppCapabilityAccess */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * IAppCapability2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xf0000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2, 0x11c7ccb6, 0xc74f, 0x50a3, 0xb9,0x60, 0x88,0x00,0x87,0x67,0xd9,0x39);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    MIDL_INTERFACE("11c7ccb6-c74f-50a3-b960-88008767d939")
                    IAppCapability2 : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_DisplayMessage(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_DisplayMessage(
                            HSTRING value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2, 0x11c7ccb6, 0xc74f, 0x50a3, 0xb9,0x60, 0x88,0x00,0x87,0x67,0xd9,0x39)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 *This,
        TrustLevel *trustLevel);

    /*** IAppCapability2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DisplayMessage)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_DisplayMessage)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 *This,
        HSTRING value);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2Vtbl;

interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2 {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppCapability2 methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_get_DisplayMessage(This,value) (This)->lpVtbl->get_DisplayMessage(This,value)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_put_DisplayMessage(This,value) (This)->lpVtbl->put_DisplayMessage(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_AddRef(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_Release(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetIids(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppCapability2 methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_get_DisplayMessage(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayMessage(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_put_DisplayMessage(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2* This,HSTRING value) {
    return This->lpVtbl->put_DisplayMessage(This,value);
}
#endif
#ifdef WIDL_using_Windows_Security_Authorization_AppCapabilityAccess
#define IID_IAppCapability2 IID___x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2
#define IAppCapability2Vtbl __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2Vtbl
#define IAppCapability2 __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2
#define IAppCapability2_QueryInterface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_QueryInterface
#define IAppCapability2_AddRef __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_AddRef
#define IAppCapability2_Release __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_Release
#define IAppCapability2_GetIids __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetIids
#define IAppCapability2_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetRuntimeClassName
#define IAppCapability2_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_GetTrustLevel
#define IAppCapability2_get_DisplayMessage __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_get_DisplayMessage
#define IAppCapability2_put_DisplayMessage __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_put_DisplayMessage
#endif /* WIDL_using_Windows_Security_Authorization_AppCapabilityAccess */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xf0000 */

/*****************************************************************************
 * IAppCapabilityAccessChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs, 0x0a578d15, 0xbdd7, 0x457e, 0x8c,0xca, 0x6f,0x53,0xbd,0x2e,0x59,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    MIDL_INTERFACE("0a578d15-bdd7-457e-8cca-6f53bd2e5944")
                    IAppCapabilityAccessChangedEventArgs : public IInspectable
                    {
                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs, 0x0a578d15, 0xbdd7, 0x457e, 0x8c,0xca, 0x6f,0x53,0xbd,0x2e,0x59,0x44)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgsVtbl;

interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_AddRef(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_Release(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetIids(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_Security_Authorization_AppCapabilityAccess
#define IID_IAppCapabilityAccessChangedEventArgs IID___x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs
#define IAppCapabilityAccessChangedEventArgsVtbl __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgsVtbl
#define IAppCapabilityAccessChangedEventArgs __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs
#define IAppCapabilityAccessChangedEventArgs_QueryInterface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_QueryInterface
#define IAppCapabilityAccessChangedEventArgs_AddRef __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_AddRef
#define IAppCapabilityAccessChangedEventArgs_Release __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_Release
#define IAppCapabilityAccessChangedEventArgs_GetIids __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetIids
#define IAppCapabilityAccessChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetRuntimeClassName
#define IAppCapabilityAccessChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_GetTrustLevel
#endif /* WIDL_using_Windows_Security_Authorization_AppCapabilityAccess */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * IAppCapabilityStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics, 0x7c353e2a, 0x46ee, 0x44e5, 0xaf,0x3d, 0x6a,0xd3,0xfc,0x49,0xbd,0x22);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Authorization {
                namespace AppCapabilityAccess {
                    MIDL_INTERFACE("7c353e2a-46ee-44e5-af3d-6ad3fc49bd22")
                    IAppCapabilityStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE RequestAccessForCapabilitiesAsync(
                            ABI::Windows::Foundation::Collections::IIterable<HSTRING > *capability_names,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > **operation) = 0;

                        virtual HRESULT STDMETHODCALLTYPE RequestAccessForCapabilitiesForUserAsync(
                            ABI::Windows::System::IUser *user,
                            ABI::Windows::Foundation::Collections::IIterable<HSTRING > *capability_names,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > **operation) = 0;

                        virtual HRESULT STDMETHODCALLTYPE Create(
                            HSTRING capability_name,
                            ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapability **result) = 0;

                        virtual HRESULT STDMETHODCALLTYPE CreateWithProcessIdForUser(
                            ABI::Windows::System::IUser *user,
                            HSTRING capability_name,
                            UINT32 pid,
                            ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapability **result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics, 0x7c353e2a, 0x46ee, 0x44e5, 0xaf,0x3d, 0x6a,0xd3,0xfc,0x49,0xbd,0x22)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This,
        TrustLevel *trustLevel);

    /*** IAppCapabilityStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *RequestAccessForCapabilitiesAsync)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This,
        __FIIterable_1_HSTRING *capability_names,
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus **operation);

    HRESULT (STDMETHODCALLTYPE *RequestAccessForCapabilitiesForUserAsync)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        __FIIterable_1_HSTRING *capability_names,
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus **operation);

    HRESULT (STDMETHODCALLTYPE *Create)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This,
        HSTRING capability_name,
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability **result);

    HRESULT (STDMETHODCALLTYPE *CreateWithProcessIdForUser)(
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        HSTRING capability_name,
        UINT32 pid,
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability **result);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStaticsVtbl;

interface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppCapabilityStatics methods ***/
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_RequestAccessForCapabilitiesAsync(This,capability_names,operation) (This)->lpVtbl->RequestAccessForCapabilitiesAsync(This,capability_names,operation)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_RequestAccessForCapabilitiesForUserAsync(This,user,capability_names,operation) (This)->lpVtbl->RequestAccessForCapabilitiesForUserAsync(This,user,capability_names,operation)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_Create(This,capability_name,result) (This)->lpVtbl->Create(This,capability_name,result)
#define __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_CreateWithProcessIdForUser(This,user,capability_name,pid,result) (This)->lpVtbl->CreateWithProcessIdForUser(This,user,capability_name,pid,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_QueryInterface(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_AddRef(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_Release(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetIids(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetTrustLevel(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppCapabilityStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_RequestAccessForCapabilitiesAsync(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This,__FIIterable_1_HSTRING *capability_names,__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus **operation) {
    return This->lpVtbl->RequestAccessForCapabilitiesAsync(This,capability_names,operation);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_RequestAccessForCapabilitiesForUserAsync(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This,__x_ABI_CWindows_CSystem_CIUser *user,__FIIterable_1_HSTRING *capability_names,__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus **operation) {
    return This->lpVtbl->RequestAccessForCapabilitiesForUserAsync(This,user,capability_names,operation);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_Create(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This,HSTRING capability_name,__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability **result) {
    return This->lpVtbl->Create(This,capability_name,result);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_CreateWithProcessIdForUser(__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics* This,__x_ABI_CWindows_CSystem_CIUser *user,HSTRING capability_name,UINT32 pid,__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability **result) {
    return This->lpVtbl->CreateWithProcessIdForUser(This,user,capability_name,pid,result);
}
#endif
#ifdef WIDL_using_Windows_Security_Authorization_AppCapabilityAccess
#define IID_IAppCapabilityStatics IID___x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics
#define IAppCapabilityStaticsVtbl __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStaticsVtbl
#define IAppCapabilityStatics __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics
#define IAppCapabilityStatics_QueryInterface __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_QueryInterface
#define IAppCapabilityStatics_AddRef __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_AddRef
#define IAppCapabilityStatics_Release __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_Release
#define IAppCapabilityStatics_GetIids __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetIids
#define IAppCapabilityStatics_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetRuntimeClassName
#define IAppCapabilityStatics_GetTrustLevel __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_GetTrustLevel
#define IAppCapabilityStatics_RequestAccessForCapabilitiesAsync __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_RequestAccessForCapabilitiesAsync
#define IAppCapabilityStatics_RequestAccessForCapabilitiesForUserAsync __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_RequestAccessForCapabilitiesForUserAsync
#define IAppCapabilityStatics_Create __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_Create
#define IAppCapabilityStatics_CreateWithProcessIdForUser __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_CreateWithProcessIdForUser
#endif /* WIDL_using_Windows_Security_Authorization_AppCapabilityAccess */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*
 * Class Windows.Security.Authorization.AppCapabilityAccess.AppCapability
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef RUNTIMECLASS_Windows_Security_Authorization_AppCapabilityAccess_AppCapability_DEFINED
#define RUNTIMECLASS_Windows_Security_Authorization_AppCapabilityAccess_AppCapability_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Authorization_AppCapabilityAccess_AppCapability[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','o','r','i','z','a','t','i','o','n','.','A','p','p','C','a','p','a','b','i','l','i','t','y','A','c','c','e','s','s','.','A','p','p','C','a','p','a','b','i','l','i','t','y',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authorization_AppCapabilityAccess_AppCapability[] = L"Windows.Security.Authorization.AppCapabilityAccess.AppCapability";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authorization_AppCapabilityAccess_AppCapability[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','o','r','i','z','a','t','i','o','n','.','A','p','p','C','a','p','a','b','i','l','i','t','y','A','c','c','e','s','s','.','A','p','p','C','a','p','a','b','i','l','i','t','y',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Authorization_AppCapabilityAccess_AppCapability_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*
 * Class Windows.Security.Authorization.AppCapabilityAccess.AppCapabilityAccessChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef RUNTIMECLASS_Windows_Security_Authorization_AppCapabilityAccess_AppCapabilityAccessChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Security_Authorization_AppCapabilityAccess_AppCapabilityAccessChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Authorization_AppCapabilityAccess_AppCapabilityAccessChangedEventArgs[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','o','r','i','z','a','t','i','o','n','.','A','p','p','C','a','p','a','b','i','l','i','t','y','A','c','c','e','s','s','.','A','p','p','C','a','p','a','b','i','l','i','t','y','A','c','c','e','s','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authorization_AppCapabilityAccess_AppCapabilityAccessChangedEventArgs[] = L"Windows.Security.Authorization.AppCapabilityAccess.AppCapabilityAccessChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Authorization_AppCapabilityAccess_AppCapabilityAccessChangedEventArgs[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','A','u','t','h','o','r','i','z','a','t','i','o','n','.','A','p','p','C','a','p','a','b','i','l','i','t','y','A','c','c','e','s','s','.','A','p','p','C','a','p','a','b','i','l','i','t','y','A','c','c','e','s','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Authorization_AppCapabilityAccess_AppCapabilityAccessChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > interface
 */
#ifndef ____FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus, 0xc0538d02, 0x01f7, 0x51a1, 0x99,0xbd, 0x3d,0x14,0x8d,0x05,0x5f,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("c0538d02-01f7-51a1-99bd-3d148d055fa1")
                IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > : IKeyValuePair_impl<HSTRING, ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus, 0xc0538d02, 0x01f7, 0x51a1, 0x99,0xbd, 0x3d,0x14,0x8d,0x05,0x5f,0xa1)
#endif
#else
typedef struct __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Key)(
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        HSTRING *key);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus *value);

    END_INTERFACE
} __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl;

interface __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus {
    CONST_VTBL __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Key(This,key) (This)->lpVtbl->get_Key(This,key)
#define __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Key(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,HSTRING *key) {
    return This->lpVtbl->get_Key(This,key);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Value(__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus *value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IKeyValuePair_HSTRING_AppCapabilityAccessStatus IID___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatusVtbl __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus_QueryInterface __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus_AddRef __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus_Release __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetIids __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetTrustLevel __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus_get_Key __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Key
#define IKeyValuePair_HSTRING_AppCapabilityAccessStatus_get_Value __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Value
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > interface
 */
#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus, 0x62e88ad9, 0xd63e, 0x5173, 0xba,0xa2, 0xbb,0x45,0x21,0xc7,0xe8,0x2a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("62e88ad9-d63e-5173-baa2-bb4521c7e82a")
                IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > : IIterable_impl<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus, 0x62e88ad9, 0xd63e, 0x5173, 0xba,0xa2, 0xbb,0x45,0x21,0xc7,0xe8,0x2a)
#endif
#else
typedef struct __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus **value);

    END_INTERFACE
} __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl;

interface __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus {
    CONST_VTBL __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(__FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef(__FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release(__FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids(__FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(__FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(__FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_First(__FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus IID___FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatusVtbl __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_QueryInterface __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_AddRef __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_Release __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetIids __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetTrustLevel __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel
#define IIterable_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_First __FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > interface
 */
#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus, 0x4e97286e, 0x7954, 0x5b79, 0xbe,0xa1, 0x83,0xaf,0x14,0x2e,0x4f,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("4e97286e-7954-5b79-bea1-83af142e4fb2")
                IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > : IIterator_impl<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus, 0x4e97286e, 0x7954, 0x5b79, 0xbe,0xa1, 0x83,0xaf,0x14,0x2e,0x4f,0xb2)
#endif
#else
typedef struct __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus *This,
        UINT32 items_size,
        __FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl;

interface __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus {
    CONST_VTBL __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Current(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_HasCurrent(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_MoveNext(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetMany(__FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus* This,UINT32 items_size,__FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus IID___FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatusVtbl __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatusVtbl
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_QueryInterface __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_QueryInterface
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_AddRef __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_AddRef
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_Release __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_Release
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetIids __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetIids
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetTrustLevel __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_get_Current __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_Current
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_get_HasCurrent __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_get_HasCurrent
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_MoveNext __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_MoveNext
#define IIterator_IKeyValuePair_HSTRING_AppCapabilityAccessStatus_GetMany __FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1___FIKeyValuePair_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > interface
 */
#ifndef ____FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__
#define ____FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_HSTRING_AppCapabilityAccessStatus, 0x20366438, 0x9fab, 0x5c12, 0x87,0xeb, 0xda,0x86,0x7e,0x38,0x3f,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("20366438-9fab-5c12-87eb-da867e383fe7")
                IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > : IMapView_impl<HSTRING, ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_HSTRING_AppCapabilityAccessStatus, 0x20366438, 0x9fab, 0x5c12, 0x87,0xeb, 0xda,0x86,0x7e,0x38,0x3f,0xe7)
#endif
#else
typedef struct __FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        HSTRING key,
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus *value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        HSTRING key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus **first,
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus **second);

    END_INTERFACE
} __FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl;

interface __FIMapView_2_HSTRING_AppCapabilityAccessStatus {
    CONST_VTBL __FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetIids(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
static inline HRESULT __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Lookup(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,HSTRING key,__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus *value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_HSTRING_AppCapabilityAccessStatus_get_Size(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_HSTRING_AppCapabilityAccessStatus_HasKey(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,HSTRING key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Split(__FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,__FIMapView_2_HSTRING_AppCapabilityAccessStatus **first,__FIMapView_2_HSTRING_AppCapabilityAccessStatus **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_HSTRING_AppCapabilityAccessStatus IID___FIMapView_2_HSTRING_AppCapabilityAccessStatus
#define IMapView_HSTRING_AppCapabilityAccessStatusVtbl __FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl
#define IMapView_HSTRING_AppCapabilityAccessStatus __FIMapView_2_HSTRING_AppCapabilityAccessStatus
#define IMapView_HSTRING_AppCapabilityAccessStatus_QueryInterface __FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface
#define IMapView_HSTRING_AppCapabilityAccessStatus_AddRef __FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef
#define IMapView_HSTRING_AppCapabilityAccessStatus_Release __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release
#define IMapView_HSTRING_AppCapabilityAccessStatus_GetIids __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetIids
#define IMapView_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName
#define IMapView_HSTRING_AppCapabilityAccessStatus_GetTrustLevel __FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel
#define IMapView_HSTRING_AppCapabilityAccessStatus_Lookup __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Lookup
#define IMapView_HSTRING_AppCapabilityAccessStatus_get_Size __FIMapView_2_HSTRING_AppCapabilityAccessStatus_get_Size
#define IMapView_HSTRING_AppCapabilityAccessStatus_HasKey __FIMapView_2_HSTRING_AppCapabilityAccessStatus_HasKey
#define IMapView_HSTRING_AppCapabilityAccessStatus_Split __FIMapView_2_HSTRING_AppCapabilityAccessStatus_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus, 0xa66001f3, 0xe332, 0x531a, 0xbf,0x49, 0x4e,0xdd,0x3a,0xf8,0x8d,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("a66001f3-e332-531a-bf49-4edd3af88de7")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus, 0xa66001f3, 0xe332, 0x531a, 0xbf,0x49, 0x4e,0xdd,0x3a,0xf8,0x8d,0xe7)
#endif
#else
typedef struct __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        __FIMapView_2_HSTRING_AppCapabilityAccessStatus **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl;

interface __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus {
    CONST_VTBL __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetIids(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_put_Completed(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_get_Completed(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetResults(__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,__FIMapView_2_HSTRING_AppCapabilityAccessStatus **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus IID___FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatusVtbl __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_QueryInterface __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_AddRef __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_Release __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_GetIids __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetIids
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetRuntimeClassName
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_GetTrustLevel __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetTrustLevel
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_put_Completed __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_put_Completed
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_get_Completed __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_get_Completed
#define IAsyncOperation_IMapView_HSTRING_AppCapabilityAccessStatus_GetResults __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > interface
 */
#ifndef ____FIAsyncOperation_1_AppCapabilityAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_AppCapabilityAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_AppCapabilityAccessStatus, 0x827caf42, 0x5fe6, 0x5b5b, 0x84,0xce, 0xc4,0x48,0x34,0x13,0x4d,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("827caf42-5fe6-5b5b-84ce-c44834134d3d")
            IAsyncOperation<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > : IAsyncOperation_impl<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_AppCapabilityAccessStatus, 0x827caf42, 0x5fe6, 0x5b5b, 0x84,0xce, 0xc4,0x48,0x34,0x13,0x4d,0x3d)
#endif
#else
typedef struct __FIAsyncOperation_1_AppCapabilityAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_AppCapabilityAccessStatus *This,
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus *results);

    END_INTERFACE
} __FIAsyncOperation_1_AppCapabilityAccessStatusVtbl;

interface __FIAsyncOperation_1_AppCapabilityAccessStatus {
    CONST_VTBL __FIAsyncOperation_1_AppCapabilityAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_AppCapabilityAccessStatus_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_AppCapabilityAccessStatus_QueryInterface(__FIAsyncOperation_1_AppCapabilityAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_AppCapabilityAccessStatus_AddRef(__FIAsyncOperation_1_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_AppCapabilityAccessStatus_Release(__FIAsyncOperation_1_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_AppCapabilityAccessStatus_GetIids(__FIAsyncOperation_1_AppCapabilityAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_AppCapabilityAccessStatus_GetRuntimeClassName(__FIAsyncOperation_1_AppCapabilityAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_AppCapabilityAccessStatus_GetTrustLevel(__FIAsyncOperation_1_AppCapabilityAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
static inline HRESULT __FIAsyncOperation_1_AppCapabilityAccessStatus_put_Completed(__FIAsyncOperation_1_AppCapabilityAccessStatus* This,__FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_AppCapabilityAccessStatus_get_Completed(__FIAsyncOperation_1_AppCapabilityAccessStatus* This,__FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_AppCapabilityAccessStatus_GetResults(__FIAsyncOperation_1_AppCapabilityAccessStatus* This,__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CAppCapabilityAccessStatus *results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_AppCapabilityAccessStatus IID___FIAsyncOperation_1_AppCapabilityAccessStatus
#define IAsyncOperation_AppCapabilityAccessStatusVtbl __FIAsyncOperation_1_AppCapabilityAccessStatusVtbl
#define IAsyncOperation_AppCapabilityAccessStatus __FIAsyncOperation_1_AppCapabilityAccessStatus
#define IAsyncOperation_AppCapabilityAccessStatus_QueryInterface __FIAsyncOperation_1_AppCapabilityAccessStatus_QueryInterface
#define IAsyncOperation_AppCapabilityAccessStatus_AddRef __FIAsyncOperation_1_AppCapabilityAccessStatus_AddRef
#define IAsyncOperation_AppCapabilityAccessStatus_Release __FIAsyncOperation_1_AppCapabilityAccessStatus_Release
#define IAsyncOperation_AppCapabilityAccessStatus_GetIids __FIAsyncOperation_1_AppCapabilityAccessStatus_GetIids
#define IAsyncOperation_AppCapabilityAccessStatus_GetRuntimeClassName __FIAsyncOperation_1_AppCapabilityAccessStatus_GetRuntimeClassName
#define IAsyncOperation_AppCapabilityAccessStatus_GetTrustLevel __FIAsyncOperation_1_AppCapabilityAccessStatus_GetTrustLevel
#define IAsyncOperation_AppCapabilityAccessStatus_put_Completed __FIAsyncOperation_1_AppCapabilityAccessStatus_put_Completed
#define IAsyncOperation_AppCapabilityAccessStatus_get_Completed __FIAsyncOperation_1_AppCapabilityAccessStatus_get_Completed
#define IAsyncOperation_AppCapabilityAccessStatus_GetResults __FIAsyncOperation_1_AppCapabilityAccessStatus_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_AppCapabilityAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus, 0xbdf03ead, 0xa75b, 0x510c, 0x87,0xd2, 0x5b,0x57,0x53,0xbd,0xf1,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("bdf03ead-a75b-510c-87d2-5b5753bdf1bd")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus, 0xbdf03ead, 0xa75b, 0x510c, 0x87,0xd2, 0x5b,0x57,0x53,0xbd,0xf1,0xbd)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *This,
        __FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Invoke(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus* This,__FIAsyncOperation_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IMapView_HSTRING_AppCapabilityAccessStatus IID___FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_AppCapabilityAccessStatusVtbl __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatusVtbl
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_AppCapabilityAccessStatus __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_AppCapabilityAccessStatus_QueryInterface __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_QueryInterface
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_AppCapabilityAccessStatus_AddRef __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_AddRef
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_AppCapabilityAccessStatus_Release __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Release
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_AppCapabilityAccessStatus_Invoke __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_AppCapabilityAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus, 0x6ea0f2e9, 0xbc97, 0x58e8, 0xa3,0xa6, 0xc8,0x29,0xb9,0xe5,0xf2,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("6ea0f2e9-bc97-58e8-a3a6-c829b9e5f2aa")
            IAsyncOperationCompletedHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus, 0x6ea0f2e9, 0xbc97, 0x58e8, 0xa3,0xa6, 0xc8,0x29,0xb9,0xe5,0xf2,0xaa)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus *This,
        __FIAsyncOperation_1_AppCapabilityAccessStatus *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatusVtbl;

interface __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
#define __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_QueryInterface(__FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_AddRef(__FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_Release(__FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessStatus > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_Invoke(__FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus* This,__FIAsyncOperation_1_AppCapabilityAccessStatus *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_AppCapabilityAccessStatus IID___FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus
#define IAsyncOperationCompletedHandler_AppCapabilityAccessStatusVtbl __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatusVtbl
#define IAsyncOperationCompletedHandler_AppCapabilityAccessStatus __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus
#define IAsyncOperationCompletedHandler_AppCapabilityAccessStatus_QueryInterface __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_QueryInterface
#define IAsyncOperationCompletedHandler_AppCapabilityAccessStatus_AddRef __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_AddRef
#define IAsyncOperationCompletedHandler_AppCapabilityAccessStatus_Release __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_Release
#define IAsyncOperationCompletedHandler_AppCapabilityAccessStatus_Invoke __FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_AppCapabilityAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapability*,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs, 0x6d923c95, 0x7b83, 0x5f59, 0x88,0x83, 0xf4,0x41,0x75,0x28,0x48,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("6d923c95-7b83-5f59-8883-f44175284898")
            ITypedEventHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapability*,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapability*, ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapability* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessChangedEventArgs*, ABI::Windows::Security::Authorization::AppCapabilityAccess::IAppCapabilityAccessChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs, 0x6d923c95, 0x7b83, 0x5f59, 0x88,0x83, 0xf4,0x41,0x75,0x28,0x48,0x98)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapability*,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs *This,
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *sender,
        __x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapability*,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapability*,ABI::Windows::Security::Authorization::AppCapabilityAccess::AppCapabilityAccessChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs* This,__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapability *sender,__x_ABI_CWindows_CSecurity_CAuthorization_CAppCapabilityAccess_CIAppCapabilityAccessChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_AppCapability_AppCapabilityAccessChangedEventArgs IID___FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs
#define ITypedEventHandler_AppCapability_AppCapabilityAccessChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgsVtbl
#define ITypedEventHandler_AppCapability_AppCapabilityAccessChangedEventArgs __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs
#define ITypedEventHandler_AppCapability_AppCapabilityAccessChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_QueryInterface
#define ITypedEventHandler_AppCapability_AppCapabilityAccessChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_AddRef
#define ITypedEventHandler_AppCapability_AppCapabilityAccessChangedEventArgs_Release __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_Release
#define ITypedEventHandler_AppCapability_AppCapabilityAccessChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapability_Windows__CSecurity__CAuthorization__CAppCapabilityAccess__CAppCapabilityAccessChangedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_security_authorization_appcapabilityaccess_h__ */
