/*
 * Copyright (C) 2023 Mo<PERSON>ad Al-Jaf
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.system.idl";
import "windows.ui.idl";
/* import "windows.ui.composition.idl"; */
import "windows.ui.input.idl";
/* import "windows.ui.input.inking.idl"; */
/* import "windows.ui.input.inking.core.idl"; */
/* import "windows.ui.popups.idl"; */
#endif

namespace Windows.UI.Input {
    runtimeclass PointerPoint;

    declare {
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.Input.PointerPoint *>;
        interface Windows.Foundation.Collections.IVector<Windows.UI.Input.PointerPoint *>;
    }
}

namespace Windows.UI.Core {
    typedef enum CoreCursorType CoreCursorType;
    typedef enum CoreDispatcherPriority CoreDispatcherPriority;
    typedef enum CoreProcessEventsOption CoreProcessEventsOption;
    typedef enum CoreVirtualKeyStates CoreVirtualKeyStates;
    typedef enum CoreWindowActivationMode CoreWindowActivationMode;
    typedef enum CoreWindowActivationState CoreWindowActivationState;
    typedef enum CoreWindowFlowDirection CoreWindowFlowDirection;
    typedef struct CorePhysicalKeyStatus CorePhysicalKeyStatus;
    typedef struct CoreProximityEvaluation CoreProximityEvaluation;

    interface IAutomationProviderRequestedEventArgs;
    interface ICharacterReceivedEventArgs;
    interface IClosestInteractiveBoundsRequestedEventArgs;
    interface ICoreAcceleratorKeys;
    interface ICoreCursor;
    interface ICoreCursorFactory;
    interface ICoreDispatcher;
    interface ICoreDispatcher2;
    interface ICoreDispatcherWithTaskPriority;
    interface ICoreWindow;
    interface ICoreWindow2;
    interface ICoreWindow3;
    interface ICoreWindow4;
    interface ICoreWindow5;
    interface ICoreWindowEventArgs;
    interface ICoreWindowStatic;
    interface IIdleDispatchedHandlerArgs;
    interface IInputEnabledEventArgs;
    interface IKeyEventArgs;
    interface IKeyEventArgs2;
    interface IPointerEventArgs;
    interface ITouchHitTestingEventArgs;
    interface IVisibilityChangedEventArgs;
    interface IWindowActivatedEventArgs;
    interface IWindowSizeChangedEventArgs;

    runtimeclass AutomationProviderRequestedEventArgs;
    runtimeclass AcceleratorKeyEventArgs;
    runtimeclass CharacterReceivedEventArgs;
    runtimeclass ClosestInteractiveBoundsRequestedEventArgs;
    runtimeclass CoreCursor;
    runtimeclass CoreDispatcher;
    runtimeclass CoreWindow;
    runtimeclass CoreWindowEventArgs;
    runtimeclass IdleDispatchedHandlerArgs;
    runtimeclass InputEnabledEventArgs;
    runtimeclass KeyEventArgs;
    runtimeclass PointerEventArgs;
    runtimeclass TouchHitTestingEventArgs;
    runtimeclass VisibilityChangedEventArgs;
    runtimeclass WindowActivatedEventArgs;
    runtimeclass WindowSizeChangedEventArgs;

    declare {
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, IInspectable *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.AutomationProviderRequestedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.CharacterReceivedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.ClosestInteractiveBoundsRequestedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.CoreWindowEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.InputEnabledEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.KeyEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.PointerEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.TouchHitTestingEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.VisibilityChangedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.WindowActivatedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.WindowSizeChangedEventArgs *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum CoreCursorType
    {
        Arrow                  = 0,
        Cross                  = 1,
        Custom                 = 2,
        Hand                   = 3,
        Help                   = 4,
        IBeam                  = 5,
        SizeAll                = 6,
        SizeNortheastSouthwest = 7,
        SizeNorthSouth         = 8,
        SizeNorthwestSoutheast = 9,
        SizeWestEast           = 10,
        UniversalNo            = 11,
        UpArrow                = 12,
        Wait                   = 13,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        Pin                    = 14,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        Person                 = 15
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum CoreDispatcherPriority
    {
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Idle   = -2,
        Low    = -1,
        Normal =  0,
        High   =  1,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum CoreProcessEventsOption
    {
        ProcessOneAndAllPending = 0,
        ProcessOneIfPresent     = 1,
        ProcessUntilQuit        = 2,
        ProcessAllIfPresent     = 3,
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        flags
    ]
    enum CoreVirtualKeyStates
    {
        None   = 0x0,
        Down   = 0x1,
        Locked = 0x2
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0)
    ]
    enum CoreWindowActivationMode
    {
        None                   = 0,
        Deactivated            = 1,
        ActivatedNotForeground = 2,
        ActivatedInForeground  = 3
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum CoreWindowActivationState
    {
        CodeActivated    = 0,
        Deactivated      = 1,
        PointerActivated = 2
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    enum CoreWindowFlowDirection
    {
        LeftToRight = 0,
        RightToLeft = 1
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    struct CorePhysicalKeyStatus
    {
        UINT32 RepeatCount;
        UINT32 ScanCode;
        boolean IsExtendedKey;
        boolean IsMenuKeyDown;
        boolean WasKeyDown;
        boolean IsKeyReleased;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0)
    ]
    struct CoreProximityEvaluation
    {
        INT32 Score;
        Windows.Foundation.Point AdjustedPoint;
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(d1f276c4-98d8-4636-bf49-eb79507548e9)
    ]
    delegate HRESULT DispatchedHandler();

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(a42b0c24-7f21-4abc-99c1-8f01007f0880)
    ]
    delegate HRESULT IdleDispatchedHandler([in] Windows.UI.Core.IdleDispatchedHandlerArgs *e);

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.AutomationProviderRequestedEventArgs),
        uuid(961ff258-21bf-4b42-a298-fa479d4c52e2)
    ]
    interface IAutomationProviderRequestedEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT AutomationProvider([out, retval] IInspectable **value);
        [propput] HRESULT AutomationProvider([in] IInspectable *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.CharacterReceivedEventArgs),
        uuid(c584659f-99b2-4bcc-bd33-04e63f42902e)
    ]
    interface ICharacterReceivedEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT KeyCode([out, retval] UINT32 *value);
        [propget] HRESULT KeyStatus([out, retval] Windows.UI.Core.CorePhysicalKeyStatus *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Core.ClosestInteractiveBoundsRequestedEventArgs),
        uuid(347c11d7-f6f8-40e3-b29f-ae50d3e86486)
    ]
    interface IClosestInteractiveBoundsRequestedEventArgs : IInspectable
    {
        [propget] HRESULT PointerPosition([out, retval] Windows.Foundation.Point *value);
        [propget] HRESULT SearchBounds([out, retval] Windows.Foundation.Rect *value);
        [propget] HRESULT ClosestInteractiveBounds([out, retval] Windows.Foundation.Rect *value);
        [propput] HRESULT ClosestInteractiveBounds([in] Windows.Foundation.Rect value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.CoreCursor),
        uuid(96893acf-111d-442c-8a77-b87992f8e2d6)
    ]
    interface ICoreCursor : IInspectable
    {
        [propget] HRESULT Id([out, retval] UINT32 *value);
        [propget] HRESULT Type([out, retval] Windows.UI.Core.CoreCursorType *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.CoreCursor),
        uuid(f6359621-a79d-4ed3-8c32-a9ef9d6b76a4)
    ]
    interface ICoreCursorFactory : IInspectable
    {
        HRESULT CreateCursor([in] Windows.UI.Core.CoreCursorType type, [in] UINT32 id, [out, retval] Windows.UI.Core.CoreCursor **cursor);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.CoreDispatcher),
        uuid(60db2fa8-b705-4fde-a7d6-ebbb1891d39e)
    ]
    interface ICoreDispatcher : IInspectable
        requires Windows.UI.Core.ICoreAcceleratorKeys
    {
        [propget] HRESULT HasThreadAccess([out, retval] boolean *value);
        HRESULT ProcessEvents([in] Windows.UI.Core.CoreProcessEventsOption options);
        HRESULT RunAsync([in] Windows.UI.Core.CoreDispatcherPriority priority, [in] Windows.UI.Core.DispatchedHandler *callback,
                         [out, retval] Windows.Foundation.IAsyncAction **action);
        HRESULT RunIdleAsync([in] Windows.UI.Core.IdleDispatchedHandler *callback, [out, retval] Windows.Foundation.IAsyncAction **action);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(79b9d5f2-879e-4b89-b798-79e47598030c)
    ]
    interface ICoreWindow : IInspectable
    {
        [propget] HRESULT AutomationHostProvider([out, retval] IInspectable **value);
        [propget] HRESULT Bounds([out, retval] Windows.Foundation.Rect *value);
        [propget] HRESULT CustomProperties([out, retval] Windows.Foundation.Collections.IPropertySet **value);
        [propget] HRESULT Dispatcher([out, retval] Windows.UI.Core.CoreDispatcher **value);
        [propget] HRESULT FlowDirection([out, retval] Windows.UI.Core.CoreWindowFlowDirection *value);
        [propput] HRESULT FlowDirection([in] Windows.UI.Core.CoreWindowFlowDirection value);
        [propget] HRESULT IsInputEnabled([out, retval] boolean *value);
        [propput] HRESULT IsInputEnabled([in] boolean value);
        [propget] HRESULT PointerCursor([out, retval] Windows.UI.Core.CoreCursor **value);
        [propput] HRESULT PointerCursor([in] Windows.UI.Core.CoreCursor *value);
        [propget] HRESULT PointerPosition([out, retval] Windows.Foundation.Point *value);
        [propget] HRESULT Visible([out, retval] boolean *value);
        HRESULT Activate();
        HRESULT Close();
        HRESULT GetAsyncKeyState([in] Windows.System.VirtualKey key, [out, retval] Windows.UI.Core.CoreVirtualKeyStates *state);
        HRESULT GetKeyState([in] Windows.System.VirtualKey key, [out, retval] Windows.UI.Core.CoreVirtualKeyStates *state);
        HRESULT ReleasePointerCapture();
        HRESULT SetPointerCapture();
        [eventadd] HRESULT Activated([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.WindowActivatedEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT Activated([in] EventRegistrationToken cookie);
        [eventadd] HRESULT AutomationProviderRequested([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.AutomationProviderRequestedEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT AutomationProviderRequested([in] EventRegistrationToken cookie);
        [eventadd] HRESULT CharacterReceived([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.CharacterReceivedEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT CharacterReceived([in] EventRegistrationToken cookie);
        [eventadd] HRESULT Closed([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.CoreWindowEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT Closed([in] EventRegistrationToken cookie);
        [eventadd] HRESULT InputEnabled([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.InputEnabledEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT InputEnabled([in] EventRegistrationToken cookie);
        [eventadd] HRESULT KeyDown([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.KeyEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT KeyDown([in] EventRegistrationToken cookie);
        [eventadd] HRESULT KeyUp([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.KeyEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT KeyUp([in] EventRegistrationToken cookie);
        [eventadd] HRESULT PointerCaptureLost([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.PointerEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT PointerCaptureLost([in] EventRegistrationToken cookie);
        [eventadd] HRESULT PointerEntered([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.PointerEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT PointerEntered([in] EventRegistrationToken cookie);
        [eventadd] HRESULT PointerExited([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.PointerEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT PointerExited([in] EventRegistrationToken cookie);
        [eventadd] HRESULT PointerMoved([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.PointerEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT PointerMoved([in] EventRegistrationToken cookie);
        [eventadd] HRESULT PointerPressed([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.PointerEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT PointerPressed([in] EventRegistrationToken cookie);
        [eventadd] HRESULT PointerReleased([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.PointerEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT PointerReleased([in] EventRegistrationToken cookie);
        [eventadd] HRESULT TouchHitTesting([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.TouchHitTestingEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT TouchHitTesting([in] EventRegistrationToken cookie);
        [eventadd] HRESULT PointerWheelChanged([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.PointerEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT PointerWheelChanged([in] EventRegistrationToken cookie);
        [eventadd] HRESULT SizeChanged([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.WindowSizeChangedEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT SizeChanged([in] EventRegistrationToken cookie);
        [eventadd] HRESULT VisibilityChanged([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.VisibilityChangedEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT VisibilityChanged([in] EventRegistrationToken cookie);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.CoreWindow),
        uuid(7c2b1b85-6917-4361-9c02-0d9e3a420b95)
    ]
    interface ICoreWindow2 : IInspectable
    {
        [propput] HRESULT PointerPosition([in] Windows.Foundation.Point value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Core.CoreWindow),
        uuid(32c20dd8-faef-4375-a2ab-32640e4815c7)
    ]
    interface ICoreWindow3 : IInspectable
    {
        [eventadd] HRESULT ClosestInteractiveBoundsRequested([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, Windows.UI.Core.ClosestInteractiveBoundsRequestedEventArgs *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT ClosestInteractiveBoundsRequested([in] EventRegistrationToken cookie);
        HRESULT GetCurrentKeyEventDeviceId([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 4.0),
        exclusiveto(Windows.UI.Core.CoreWindow),
        uuid(35caf0d0-47f0-436c-af97-0dd88f6f5f02)
    ]
    interface ICoreWindow4 : IInspectable
    {
        [eventadd] HRESULT ResizeStarted([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, IInspectable *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT ResizeStarted([in] EventRegistrationToken cookie);
        [eventadd] HRESULT ResizeCompleted([in] Windows.Foundation.TypedEventHandler<Windows.UI.Core.CoreWindow *, IInspectable *> *handler, [out, retval] EventRegistrationToken *cookie);
        [eventremove] HRESULT ResizeCompleted([in] EventRegistrationToken cookie);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        exclusiveto(Windows.UI.Core.CoreWindow),
        uuid(4b4ae1e1-2e6d-4eaa-bda1-1c5cc1bee141)
    ]
    interface ICoreWindow5 : IInspectable
    {
        [propget] HRESULT DispatcherQueue([out, retval] Windows.System.DispatcherQueue **value);
        [propget] HRESULT ActivationMode([out, retval] Windows.UI.Core.CoreWindowActivationMode *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        uuid(272b1ef3-c633-4da5-a26c-c6d0f56b29da)
    ]
    interface ICoreWindowEventArgs : IInspectable
    {
        [propget] HRESULT Handled([out, retval] boolean *value);
        [propput] HRESULT Handled([in] boolean value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.CoreWindow),
        uuid(4d239005-3c2a-41b1-9022-536bb9cf93b1)
    ]
    interface ICoreWindowStatic : IInspectable
    {
        HRESULT GetForCurrentThread([out, retval] Windows.UI.Core.CoreWindow **windows);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.IdleDispatchedHandlerArgs),
        uuid(98bb6a24-dc1c-43cb-b4ed-d1c0eb2391f3)
    ]
    interface IIdleDispatchedHandlerArgs : IInspectable
    {
        [propget] HRESULT IsDispatcherIdle([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.InputEnabledEventArgs),
        uuid(80371d4f-2fd8-4c24-aa86-3163a87b4e5a)
    ]
    interface IInputEnabledEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT InputEnabled([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.KeyEventArgs),
        uuid(5ff5e930-2544-4a17-bd78-1f2fdebb106b)
    ]
    interface IKeyEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT VirtualKey([out, retval] Windows.System.VirtualKey *value);
        [propget] HRESULT KeyStatus([out, retval] Windows.UI.Core.CorePhysicalKeyStatus *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        exclusiveto(Windows.UI.Core.KeyEventArgs),
        uuid(583add98-0790-4571-9b12-645ef9d79e42)
    ]
    interface IKeyEventArgs2 : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT DeviceId([out, retval] HSTRING *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.PointerEventArgs),
        uuid(920d9cb1-a5fc-4a21-8c09-49dfe6ffe25f)
    ]
    interface IPointerEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT CurrentPoint([out, retval] Windows.UI.Input.PointerPoint **value);
        [propget] HRESULT KeyModifiers([out, retval] Windows.System.VirtualKeyModifiers *value);
        HRESULT GetIntermediatePoints([out, retval] Windows.Foundation.Collections.IVector<Windows.UI.Input.PointerPoint *> **value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.TouchHitTestingEventArgs),
        uuid(22f3b823-0b7c-424e-9df7-33d4f962931b)
    ]
    interface ITouchHitTestingEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT ProximityEvaluation([out, retval] Windows.UI.Core.CoreProximityEvaluation *value);
        [propput] HRESULT ProximityEvaluation([in] Windows.UI.Core.CoreProximityEvaluation value);
        [propget] HRESULT Point([out, retval] Windows.Foundation.Point *value);
        [propget] HRESULT BoundingBox([out, retval] Windows.Foundation.Rect *value);
        [default_overload] [overload("EvaluateProximity")] HRESULT EvaluateProximityToRect([in] Windows.Foundation.Rect bounding_box, [out, retval] Windows.UI.Core.CoreProximityEvaluation *evaluation);
        [overload("EvaluateProximity")] HRESULT EvaluateProximityToPolygon([in] UINT32 vertice_size, [in] [size_is(vertice_size)] Windows.Foundation.Point *vertices, [out, retval] Windows.UI.Core.CoreProximityEvaluation *evaluation);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.VisibilityChangedEventArgs),
        uuid(bf9918ea-d801-4564-a495-b1e84f8ad085)
    ]
    interface IVisibilityChangedEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT Visible([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.WindowActivatedEventArgs),
        uuid(179d65e7-4658-4cb6-aa13-41d094ea255e)
    ]
    interface IWindowActivatedEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT WindowActivationState([out, retval] Windows.UI.Core.CoreWindowActivationState *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        exclusiveto(Windows.UI.Core.WindowSizeChangedEventArgs),
        uuid(5a200ec7-0426-47dc-b86c-6f475915e451)
    ]
    interface IWindowSizeChangedEventArgs : IInspectable
        requires Windows.UI.Core.ICoreWindowEventArgs
    {
        [propget] HRESULT Size([out, retval] Windows.Foundation.Size *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass AutomationProviderRequestedEventArgs
    {
        [default] interface Windows.UI.Core.IAutomationProviderRequestedEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass CharacterReceivedEventArgs
    {
        [default] interface Windows.UI.Core.ICharacterReceivedEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 3.0),
        marshaling_behavior(standard)
    ]
    runtimeclass ClosestInteractiveBoundsRequestedEventArgs
    {
        [default] interface Windows.UI.Core.IClosestInteractiveBoundsRequestedEventArgs;
    }

    [
        activatable(Windows.UI.Core.ICoreCursorFactory, Windows.Foundation.UniversalApiContract, 1.0),
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass CoreCursor
    {
        [default] interface Windows.UI.Core.ICoreCursor;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(agile)
    ]
    runtimeclass CoreDispatcher
    {
        [default] interface Windows.UI.Core.ICoreDispatcher;
        interface Windows.UI.Core.ICoreAcceleratorKeys;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.Core.ICoreDispatcherWithTaskPriority;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.Core.ICoreDispatcher2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard),
        static(Windows.UI.Core.ICoreWindowStatic, Windows.Foundation.UniversalApiContract, 1.0)
    ]
    runtimeclass CoreWindow
    {
        [default] interface Windows.UI.Core.ICoreWindow;
        [contract(Windows.Foundation.UniversalApiContract, 1.0)] interface Windows.UI.Core.ICoreWindow2;
        [contract(Windows.Foundation.UniversalApiContract, 2.0)] interface Windows.UI.Core.ICorePointerRedirector;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Core.ICoreWindow3;
        [contract(Windows.Foundation.UniversalApiContract, 4.0)] interface Windows.UI.Core.ICoreWindow4;
        [contract(Windows.Foundation.UniversalApiContract, 5.0)] interface Windows.UI.Core.ICoreWindow5;
        [contract(Windows.Foundation.UniversalApiContract, 8.0)] interface Windows.UI.Core.ICoreWindowWithContext;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass CoreWindowEventArgs
    {
        [default] interface Windows.UI.Core.ICoreWindowEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass IdleDispatchedHandlerArgs
    {
        [default] interface Windows.UI.Core.IIdleDispatchedHandlerArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass InputEnabledEventArgs
    {
        [default] interface Windows.UI.Core.IInputEnabledEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass KeyEventArgs
    {
        [default] interface Windows.UI.Core.IKeyEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
        [contract(Windows.Foundation.UniversalApiContract, 3.0)] interface Windows.UI.Core.IKeyEventArgs2;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass PointerEventArgs
    {
        [default] interface Windows.UI.Core.IPointerEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass TouchHitTestingEventArgs
    {
        [default] interface Windows.UI.Core.ITouchHitTestingEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass VisibilityChangedEventArgs
    {
        [default] interface Windows.UI.Core.IVisibilityChangedEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass WindowActivatedEventArgs
    {
        [default] interface Windows.UI.Core.IWindowActivatedEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 1.0),
        marshaling_behavior(standard)
    ]
    runtimeclass WindowSizeChangedEventArgs
    {
        [default] interface Windows.UI.Core.IWindowSizeChangedEventArgs;
        interface Windows.UI.Core.ICoreWindowEventArgs;
    }
}
