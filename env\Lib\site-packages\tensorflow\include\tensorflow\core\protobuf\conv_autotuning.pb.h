// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/conv_autotuning.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "xla/tsl/protobuf/dnn.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto;
namespace tensorflow {
class ConvolutionProto;
struct ConvolutionProtoDefaultTypeInternal;
extern ConvolutionProtoDefaultTypeInternal _ConvolutionProto_default_instance_;
class MatmulProto;
struct MatmulProtoDefaultTypeInternal;
extern MatmulProtoDefaultTypeInternal _MatmulProto_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ConvolutionProto* Arena::CreateMaybeMessage<::tensorflow::ConvolutionProto>(Arena*);
template<> ::tensorflow::MatmulProto* Arena::CreateMaybeMessage<::tensorflow::MatmulProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class ConvolutionProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ConvolutionProto) */ {
 public:
  inline ConvolutionProto() : ConvolutionProto(nullptr) {}
  ~ConvolutionProto() override;
  explicit PROTOBUF_CONSTEXPR ConvolutionProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConvolutionProto(const ConvolutionProto& from);
  ConvolutionProto(ConvolutionProto&& from) noexcept
    : ConvolutionProto() {
    *this = ::std::move(from);
  }

  inline ConvolutionProto& operator=(const ConvolutionProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConvolutionProto& operator=(ConvolutionProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConvolutionProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConvolutionProto* internal_default_instance() {
    return reinterpret_cast<const ConvolutionProto*>(
               &_ConvolutionProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ConvolutionProto& a, ConvolutionProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ConvolutionProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConvolutionProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConvolutionProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConvolutionProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConvolutionProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ConvolutionProto& from) {
    ConvolutionProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConvolutionProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ConvolutionProto";
  }
  protected:
  explicit ConvolutionProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputFieldNumber = 2,
    kFilterFieldNumber = 3,
    kOutputFieldNumber = 4,
    kConvDescFieldNumber = 5,
    kKindFieldNumber = 1,
    kActivationFieldNumber = 8,
    kConvScaleFieldNumber = 6,
    kSideValueScaleFieldNumber = 7,
    kInputAddressFieldNumber = 9,
    kFilterAddressFieldNumber = 10,
    kOutputAddressFieldNumber = 11,
    kBiasAddressFieldNumber = 12,
    kSideInputAddressFieldNumber = 13,
  };
  // .stream_executor.dnn.TensorDescriptorProto input = 2;
  bool has_input() const;
  private:
  bool _internal_has_input() const;
  public:
  void clear_input();
  const ::stream_executor::dnn::TensorDescriptorProto& input() const;
  PROTOBUF_NODISCARD ::stream_executor::dnn::TensorDescriptorProto* release_input();
  ::stream_executor::dnn::TensorDescriptorProto* mutable_input();
  void set_allocated_input(::stream_executor::dnn::TensorDescriptorProto* input);
  private:
  const ::stream_executor::dnn::TensorDescriptorProto& _internal_input() const;
  ::stream_executor::dnn::TensorDescriptorProto* _internal_mutable_input();
  public:
  void unsafe_arena_set_allocated_input(
      ::stream_executor::dnn::TensorDescriptorProto* input);
  ::stream_executor::dnn::TensorDescriptorProto* unsafe_arena_release_input();

  // .stream_executor.dnn.TensorDescriptorProto filter = 3;
  bool has_filter() const;
  private:
  bool _internal_has_filter() const;
  public:
  void clear_filter();
  const ::stream_executor::dnn::TensorDescriptorProto& filter() const;
  PROTOBUF_NODISCARD ::stream_executor::dnn::TensorDescriptorProto* release_filter();
  ::stream_executor::dnn::TensorDescriptorProto* mutable_filter();
  void set_allocated_filter(::stream_executor::dnn::TensorDescriptorProto* filter);
  private:
  const ::stream_executor::dnn::TensorDescriptorProto& _internal_filter() const;
  ::stream_executor::dnn::TensorDescriptorProto* _internal_mutable_filter();
  public:
  void unsafe_arena_set_allocated_filter(
      ::stream_executor::dnn::TensorDescriptorProto* filter);
  ::stream_executor::dnn::TensorDescriptorProto* unsafe_arena_release_filter();

  // .stream_executor.dnn.TensorDescriptorProto output = 4;
  bool has_output() const;
  private:
  bool _internal_has_output() const;
  public:
  void clear_output();
  const ::stream_executor::dnn::TensorDescriptorProto& output() const;
  PROTOBUF_NODISCARD ::stream_executor::dnn::TensorDescriptorProto* release_output();
  ::stream_executor::dnn::TensorDescriptorProto* mutable_output();
  void set_allocated_output(::stream_executor::dnn::TensorDescriptorProto* output);
  private:
  const ::stream_executor::dnn::TensorDescriptorProto& _internal_output() const;
  ::stream_executor::dnn::TensorDescriptorProto* _internal_mutable_output();
  public:
  void unsafe_arena_set_allocated_output(
      ::stream_executor::dnn::TensorDescriptorProto* output);
  ::stream_executor::dnn::TensorDescriptorProto* unsafe_arena_release_output();

  // .stream_executor.dnn.ConvolutionDescriptorProto conv_desc = 5;
  bool has_conv_desc() const;
  private:
  bool _internal_has_conv_desc() const;
  public:
  void clear_conv_desc();
  const ::stream_executor::dnn::ConvolutionDescriptorProto& conv_desc() const;
  PROTOBUF_NODISCARD ::stream_executor::dnn::ConvolutionDescriptorProto* release_conv_desc();
  ::stream_executor::dnn::ConvolutionDescriptorProto* mutable_conv_desc();
  void set_allocated_conv_desc(::stream_executor::dnn::ConvolutionDescriptorProto* conv_desc);
  private:
  const ::stream_executor::dnn::ConvolutionDescriptorProto& _internal_conv_desc() const;
  ::stream_executor::dnn::ConvolutionDescriptorProto* _internal_mutable_conv_desc();
  public:
  void unsafe_arena_set_allocated_conv_desc(
      ::stream_executor::dnn::ConvolutionDescriptorProto* conv_desc);
  ::stream_executor::dnn::ConvolutionDescriptorProto* unsafe_arena_release_conv_desc();

  // .stream_executor.dnn.ConvolutionKind kind = 1;
  void clear_kind();
  ::stream_executor::dnn::ConvolutionKind kind() const;
  void set_kind(::stream_executor::dnn::ConvolutionKind value);
  private:
  ::stream_executor::dnn::ConvolutionKind _internal_kind() const;
  void _internal_set_kind(::stream_executor::dnn::ConvolutionKind value);
  public:

  // .stream_executor.dnn.ActivationMode activation = 8;
  void clear_activation();
  ::stream_executor::dnn::ActivationMode activation() const;
  void set_activation(::stream_executor::dnn::ActivationMode value);
  private:
  ::stream_executor::dnn::ActivationMode _internal_activation() const;
  void _internal_set_activation(::stream_executor::dnn::ActivationMode value);
  public:

  // double conv_scale = 6;
  void clear_conv_scale();
  double conv_scale() const;
  void set_conv_scale(double value);
  private:
  double _internal_conv_scale() const;
  void _internal_set_conv_scale(double value);
  public:

  // double side_value_scale = 7;
  void clear_side_value_scale();
  double side_value_scale() const;
  void set_side_value_scale(double value);
  private:
  double _internal_side_value_scale() const;
  void _internal_set_side_value_scale(double value);
  public:

  // int64 input_address = 9;
  void clear_input_address();
  int64_t input_address() const;
  void set_input_address(int64_t value);
  private:
  int64_t _internal_input_address() const;
  void _internal_set_input_address(int64_t value);
  public:

  // int64 filter_address = 10;
  void clear_filter_address();
  int64_t filter_address() const;
  void set_filter_address(int64_t value);
  private:
  int64_t _internal_filter_address() const;
  void _internal_set_filter_address(int64_t value);
  public:

  // int64 output_address = 11;
  void clear_output_address();
  int64_t output_address() const;
  void set_output_address(int64_t value);
  private:
  int64_t _internal_output_address() const;
  void _internal_set_output_address(int64_t value);
  public:

  // int64 bias_address = 12;
  void clear_bias_address();
  int64_t bias_address() const;
  void set_bias_address(int64_t value);
  private:
  int64_t _internal_bias_address() const;
  void _internal_set_bias_address(int64_t value);
  public:

  // int64 side_input_address = 13;
  void clear_side_input_address();
  int64_t side_input_address() const;
  void set_side_input_address(int64_t value);
  private:
  int64_t _internal_side_input_address() const;
  void _internal_set_side_input_address(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ConvolutionProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::stream_executor::dnn::TensorDescriptorProto* input_;
    ::stream_executor::dnn::TensorDescriptorProto* filter_;
    ::stream_executor::dnn::TensorDescriptorProto* output_;
    ::stream_executor::dnn::ConvolutionDescriptorProto* conv_desc_;
    int kind_;
    int activation_;
    double conv_scale_;
    double side_value_scale_;
    int64_t input_address_;
    int64_t filter_address_;
    int64_t output_address_;
    int64_t bias_address_;
    int64_t side_input_address_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto;
};
// -------------------------------------------------------------------

class MatmulProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MatmulProto) */ {
 public:
  inline MatmulProto() : MatmulProto(nullptr) {}
  ~MatmulProto() override;
  explicit PROTOBUF_CONSTEXPR MatmulProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MatmulProto(const MatmulProto& from);
  MatmulProto(MatmulProto&& from) noexcept
    : MatmulProto() {
    *this = ::std::move(from);
  }

  inline MatmulProto& operator=(const MatmulProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline MatmulProto& operator=(MatmulProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MatmulProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const MatmulProto* internal_default_instance() {
    return reinterpret_cast<const MatmulProto*>(
               &_MatmulProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MatmulProto& a, MatmulProto& b) {
    a.Swap(&b);
  }
  inline void Swap(MatmulProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MatmulProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MatmulProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MatmulProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MatmulProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MatmulProto& from) {
    MatmulProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MatmulProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MatmulProto";
  }
  protected:
  explicit MatmulProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAbDtypeFieldNumber = 1,
    kCDtypeFieldNumber = 2,
    kMFieldNumber = 5,
    kNFieldNumber = 6,
    kTransAFieldNumber = 3,
    kTransBFieldNumber = 4,
    kActivationFieldNumber = 11,
    kKFieldNumber = 7,
    kLdaFieldNumber = 8,
    kLdbFieldNumber = 9,
    kLdcFieldNumber = 10,
    kAAddressFieldNumber = 12,
    kBAddressFieldNumber = 13,
    kCAddressFieldNumber = 14,
    kBiasAddressFieldNumber = 15,
  };
  // .stream_executor.dnn.DataType ab_dtype = 1;
  void clear_ab_dtype();
  ::stream_executor::dnn::DataType ab_dtype() const;
  void set_ab_dtype(::stream_executor::dnn::DataType value);
  private:
  ::stream_executor::dnn::DataType _internal_ab_dtype() const;
  void _internal_set_ab_dtype(::stream_executor::dnn::DataType value);
  public:

  // .stream_executor.dnn.DataType c_dtype = 2;
  void clear_c_dtype();
  ::stream_executor::dnn::DataType c_dtype() const;
  void set_c_dtype(::stream_executor::dnn::DataType value);
  private:
  ::stream_executor::dnn::DataType _internal_c_dtype() const;
  void _internal_set_c_dtype(::stream_executor::dnn::DataType value);
  public:

  // uint64 m = 5;
  void clear_m();
  uint64_t m() const;
  void set_m(uint64_t value);
  private:
  uint64_t _internal_m() const;
  void _internal_set_m(uint64_t value);
  public:

  // uint64 n = 6;
  void clear_n();
  uint64_t n() const;
  void set_n(uint64_t value);
  private:
  uint64_t _internal_n() const;
  void _internal_set_n(uint64_t value);
  public:

  // bool trans_a = 3;
  void clear_trans_a();
  bool trans_a() const;
  void set_trans_a(bool value);
  private:
  bool _internal_trans_a() const;
  void _internal_set_trans_a(bool value);
  public:

  // bool trans_b = 4;
  void clear_trans_b();
  bool trans_b() const;
  void set_trans_b(bool value);
  private:
  bool _internal_trans_b() const;
  void _internal_set_trans_b(bool value);
  public:

  // .stream_executor.dnn.ActivationMode activation = 11;
  void clear_activation();
  ::stream_executor::dnn::ActivationMode activation() const;
  void set_activation(::stream_executor::dnn::ActivationMode value);
  private:
  ::stream_executor::dnn::ActivationMode _internal_activation() const;
  void _internal_set_activation(::stream_executor::dnn::ActivationMode value);
  public:

  // uint64 k = 7;
  void clear_k();
  uint64_t k() const;
  void set_k(uint64_t value);
  private:
  uint64_t _internal_k() const;
  void _internal_set_k(uint64_t value);
  public:

  // int64 lda = 8;
  void clear_lda();
  int64_t lda() const;
  void set_lda(int64_t value);
  private:
  int64_t _internal_lda() const;
  void _internal_set_lda(int64_t value);
  public:

  // int64 ldb = 9;
  void clear_ldb();
  int64_t ldb() const;
  void set_ldb(int64_t value);
  private:
  int64_t _internal_ldb() const;
  void _internal_set_ldb(int64_t value);
  public:

  // int64 ldc = 10;
  void clear_ldc();
  int64_t ldc() const;
  void set_ldc(int64_t value);
  private:
  int64_t _internal_ldc() const;
  void _internal_set_ldc(int64_t value);
  public:

  // int64 a_address = 12;
  void clear_a_address();
  int64_t a_address() const;
  void set_a_address(int64_t value);
  private:
  int64_t _internal_a_address() const;
  void _internal_set_a_address(int64_t value);
  public:

  // int64 b_address = 13;
  void clear_b_address();
  int64_t b_address() const;
  void set_b_address(int64_t value);
  private:
  int64_t _internal_b_address() const;
  void _internal_set_b_address(int64_t value);
  public:

  // int64 c_address = 14;
  void clear_c_address();
  int64_t c_address() const;
  void set_c_address(int64_t value);
  private:
  int64_t _internal_c_address() const;
  void _internal_set_c_address(int64_t value);
  public:

  // int64 bias_address = 15;
  void clear_bias_address();
  int64_t bias_address() const;
  void set_bias_address(int64_t value);
  private:
  int64_t _internal_bias_address() const;
  void _internal_set_bias_address(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MatmulProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int ab_dtype_;
    int c_dtype_;
    uint64_t m_;
    uint64_t n_;
    bool trans_a_;
    bool trans_b_;
    int activation_;
    uint64_t k_;
    int64_t lda_;
    int64_t ldb_;
    int64_t ldc_;
    int64_t a_address_;
    int64_t b_address_;
    int64_t c_address_;
    int64_t bias_address_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ConvolutionProto

// .stream_executor.dnn.ConvolutionKind kind = 1;
inline void ConvolutionProto::clear_kind() {
  _impl_.kind_ = 0;
}
inline ::stream_executor::dnn::ConvolutionKind ConvolutionProto::_internal_kind() const {
  return static_cast< ::stream_executor::dnn::ConvolutionKind >(_impl_.kind_);
}
inline ::stream_executor::dnn::ConvolutionKind ConvolutionProto::kind() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.kind)
  return _internal_kind();
}
inline void ConvolutionProto::_internal_set_kind(::stream_executor::dnn::ConvolutionKind value) {
  
  _impl_.kind_ = value;
}
inline void ConvolutionProto::set_kind(::stream_executor::dnn::ConvolutionKind value) {
  _internal_set_kind(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.kind)
}

// .stream_executor.dnn.TensorDescriptorProto input = 2;
inline bool ConvolutionProto::_internal_has_input() const {
  return this != internal_default_instance() && _impl_.input_ != nullptr;
}
inline bool ConvolutionProto::has_input() const {
  return _internal_has_input();
}
inline const ::stream_executor::dnn::TensorDescriptorProto& ConvolutionProto::_internal_input() const {
  const ::stream_executor::dnn::TensorDescriptorProto* p = _impl_.input_;
  return p != nullptr ? *p : reinterpret_cast<const ::stream_executor::dnn::TensorDescriptorProto&>(
      ::stream_executor::dnn::_TensorDescriptorProto_default_instance_);
}
inline const ::stream_executor::dnn::TensorDescriptorProto& ConvolutionProto::input() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.input)
  return _internal_input();
}
inline void ConvolutionProto::unsafe_arena_set_allocated_input(
    ::stream_executor::dnn::TensorDescriptorProto* input) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.input_);
  }
  _impl_.input_ = input;
  if (input) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConvolutionProto.input)
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::release_input() {
  
  ::stream_executor::dnn::TensorDescriptorProto* temp = _impl_.input_;
  _impl_.input_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::unsafe_arena_release_input() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvolutionProto.input)
  
  ::stream_executor::dnn::TensorDescriptorProto* temp = _impl_.input_;
  _impl_.input_ = nullptr;
  return temp;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::_internal_mutable_input() {
  
  if (_impl_.input_ == nullptr) {
    auto* p = CreateMaybeMessage<::stream_executor::dnn::TensorDescriptorProto>(GetArenaForAllocation());
    _impl_.input_ = p;
  }
  return _impl_.input_;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::mutable_input() {
  ::stream_executor::dnn::TensorDescriptorProto* _msg = _internal_mutable_input();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvolutionProto.input)
  return _msg;
}
inline void ConvolutionProto::set_allocated_input(::stream_executor::dnn::TensorDescriptorProto* input) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.input_);
  }
  if (input) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(input));
    if (message_arena != submessage_arena) {
      input = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, input, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.input_ = input;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvolutionProto.input)
}

// .stream_executor.dnn.TensorDescriptorProto filter = 3;
inline bool ConvolutionProto::_internal_has_filter() const {
  return this != internal_default_instance() && _impl_.filter_ != nullptr;
}
inline bool ConvolutionProto::has_filter() const {
  return _internal_has_filter();
}
inline const ::stream_executor::dnn::TensorDescriptorProto& ConvolutionProto::_internal_filter() const {
  const ::stream_executor::dnn::TensorDescriptorProto* p = _impl_.filter_;
  return p != nullptr ? *p : reinterpret_cast<const ::stream_executor::dnn::TensorDescriptorProto&>(
      ::stream_executor::dnn::_TensorDescriptorProto_default_instance_);
}
inline const ::stream_executor::dnn::TensorDescriptorProto& ConvolutionProto::filter() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.filter)
  return _internal_filter();
}
inline void ConvolutionProto::unsafe_arena_set_allocated_filter(
    ::stream_executor::dnn::TensorDescriptorProto* filter) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.filter_);
  }
  _impl_.filter_ = filter;
  if (filter) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConvolutionProto.filter)
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::release_filter() {
  
  ::stream_executor::dnn::TensorDescriptorProto* temp = _impl_.filter_;
  _impl_.filter_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::unsafe_arena_release_filter() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvolutionProto.filter)
  
  ::stream_executor::dnn::TensorDescriptorProto* temp = _impl_.filter_;
  _impl_.filter_ = nullptr;
  return temp;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::_internal_mutable_filter() {
  
  if (_impl_.filter_ == nullptr) {
    auto* p = CreateMaybeMessage<::stream_executor::dnn::TensorDescriptorProto>(GetArenaForAllocation());
    _impl_.filter_ = p;
  }
  return _impl_.filter_;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::mutable_filter() {
  ::stream_executor::dnn::TensorDescriptorProto* _msg = _internal_mutable_filter();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvolutionProto.filter)
  return _msg;
}
inline void ConvolutionProto::set_allocated_filter(::stream_executor::dnn::TensorDescriptorProto* filter) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.filter_);
  }
  if (filter) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(filter));
    if (message_arena != submessage_arena) {
      filter = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, filter, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.filter_ = filter;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvolutionProto.filter)
}

// .stream_executor.dnn.TensorDescriptorProto output = 4;
inline bool ConvolutionProto::_internal_has_output() const {
  return this != internal_default_instance() && _impl_.output_ != nullptr;
}
inline bool ConvolutionProto::has_output() const {
  return _internal_has_output();
}
inline const ::stream_executor::dnn::TensorDescriptorProto& ConvolutionProto::_internal_output() const {
  const ::stream_executor::dnn::TensorDescriptorProto* p = _impl_.output_;
  return p != nullptr ? *p : reinterpret_cast<const ::stream_executor::dnn::TensorDescriptorProto&>(
      ::stream_executor::dnn::_TensorDescriptorProto_default_instance_);
}
inline const ::stream_executor::dnn::TensorDescriptorProto& ConvolutionProto::output() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.output)
  return _internal_output();
}
inline void ConvolutionProto::unsafe_arena_set_allocated_output(
    ::stream_executor::dnn::TensorDescriptorProto* output) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.output_);
  }
  _impl_.output_ = output;
  if (output) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConvolutionProto.output)
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::release_output() {
  
  ::stream_executor::dnn::TensorDescriptorProto* temp = _impl_.output_;
  _impl_.output_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::unsafe_arena_release_output() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvolutionProto.output)
  
  ::stream_executor::dnn::TensorDescriptorProto* temp = _impl_.output_;
  _impl_.output_ = nullptr;
  return temp;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::_internal_mutable_output() {
  
  if (_impl_.output_ == nullptr) {
    auto* p = CreateMaybeMessage<::stream_executor::dnn::TensorDescriptorProto>(GetArenaForAllocation());
    _impl_.output_ = p;
  }
  return _impl_.output_;
}
inline ::stream_executor::dnn::TensorDescriptorProto* ConvolutionProto::mutable_output() {
  ::stream_executor::dnn::TensorDescriptorProto* _msg = _internal_mutable_output();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvolutionProto.output)
  return _msg;
}
inline void ConvolutionProto::set_allocated_output(::stream_executor::dnn::TensorDescriptorProto* output) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.output_);
  }
  if (output) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(output));
    if (message_arena != submessage_arena) {
      output = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, output, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.output_ = output;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvolutionProto.output)
}

// .stream_executor.dnn.ConvolutionDescriptorProto conv_desc = 5;
inline bool ConvolutionProto::_internal_has_conv_desc() const {
  return this != internal_default_instance() && _impl_.conv_desc_ != nullptr;
}
inline bool ConvolutionProto::has_conv_desc() const {
  return _internal_has_conv_desc();
}
inline const ::stream_executor::dnn::ConvolutionDescriptorProto& ConvolutionProto::_internal_conv_desc() const {
  const ::stream_executor::dnn::ConvolutionDescriptorProto* p = _impl_.conv_desc_;
  return p != nullptr ? *p : reinterpret_cast<const ::stream_executor::dnn::ConvolutionDescriptorProto&>(
      ::stream_executor::dnn::_ConvolutionDescriptorProto_default_instance_);
}
inline const ::stream_executor::dnn::ConvolutionDescriptorProto& ConvolutionProto::conv_desc() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.conv_desc)
  return _internal_conv_desc();
}
inline void ConvolutionProto::unsafe_arena_set_allocated_conv_desc(
    ::stream_executor::dnn::ConvolutionDescriptorProto* conv_desc) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.conv_desc_);
  }
  _impl_.conv_desc_ = conv_desc;
  if (conv_desc) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ConvolutionProto.conv_desc)
}
inline ::stream_executor::dnn::ConvolutionDescriptorProto* ConvolutionProto::release_conv_desc() {
  
  ::stream_executor::dnn::ConvolutionDescriptorProto* temp = _impl_.conv_desc_;
  _impl_.conv_desc_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::stream_executor::dnn::ConvolutionDescriptorProto* ConvolutionProto::unsafe_arena_release_conv_desc() {
  // @@protoc_insertion_point(field_release:tensorflow.ConvolutionProto.conv_desc)
  
  ::stream_executor::dnn::ConvolutionDescriptorProto* temp = _impl_.conv_desc_;
  _impl_.conv_desc_ = nullptr;
  return temp;
}
inline ::stream_executor::dnn::ConvolutionDescriptorProto* ConvolutionProto::_internal_mutable_conv_desc() {
  
  if (_impl_.conv_desc_ == nullptr) {
    auto* p = CreateMaybeMessage<::stream_executor::dnn::ConvolutionDescriptorProto>(GetArenaForAllocation());
    _impl_.conv_desc_ = p;
  }
  return _impl_.conv_desc_;
}
inline ::stream_executor::dnn::ConvolutionDescriptorProto* ConvolutionProto::mutable_conv_desc() {
  ::stream_executor::dnn::ConvolutionDescriptorProto* _msg = _internal_mutable_conv_desc();
  // @@protoc_insertion_point(field_mutable:tensorflow.ConvolutionProto.conv_desc)
  return _msg;
}
inline void ConvolutionProto::set_allocated_conv_desc(::stream_executor::dnn::ConvolutionDescriptorProto* conv_desc) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.conv_desc_);
  }
  if (conv_desc) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(conv_desc));
    if (message_arena != submessage_arena) {
      conv_desc = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, conv_desc, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.conv_desc_ = conv_desc;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ConvolutionProto.conv_desc)
}

// double conv_scale = 6;
inline void ConvolutionProto::clear_conv_scale() {
  _impl_.conv_scale_ = 0;
}
inline double ConvolutionProto::_internal_conv_scale() const {
  return _impl_.conv_scale_;
}
inline double ConvolutionProto::conv_scale() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.conv_scale)
  return _internal_conv_scale();
}
inline void ConvolutionProto::_internal_set_conv_scale(double value) {
  
  _impl_.conv_scale_ = value;
}
inline void ConvolutionProto::set_conv_scale(double value) {
  _internal_set_conv_scale(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.conv_scale)
}

// double side_value_scale = 7;
inline void ConvolutionProto::clear_side_value_scale() {
  _impl_.side_value_scale_ = 0;
}
inline double ConvolutionProto::_internal_side_value_scale() const {
  return _impl_.side_value_scale_;
}
inline double ConvolutionProto::side_value_scale() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.side_value_scale)
  return _internal_side_value_scale();
}
inline void ConvolutionProto::_internal_set_side_value_scale(double value) {
  
  _impl_.side_value_scale_ = value;
}
inline void ConvolutionProto::set_side_value_scale(double value) {
  _internal_set_side_value_scale(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.side_value_scale)
}

// .stream_executor.dnn.ActivationMode activation = 8;
inline void ConvolutionProto::clear_activation() {
  _impl_.activation_ = 0;
}
inline ::stream_executor::dnn::ActivationMode ConvolutionProto::_internal_activation() const {
  return static_cast< ::stream_executor::dnn::ActivationMode >(_impl_.activation_);
}
inline ::stream_executor::dnn::ActivationMode ConvolutionProto::activation() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.activation)
  return _internal_activation();
}
inline void ConvolutionProto::_internal_set_activation(::stream_executor::dnn::ActivationMode value) {
  
  _impl_.activation_ = value;
}
inline void ConvolutionProto::set_activation(::stream_executor::dnn::ActivationMode value) {
  _internal_set_activation(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.activation)
}

// int64 input_address = 9;
inline void ConvolutionProto::clear_input_address() {
  _impl_.input_address_ = int64_t{0};
}
inline int64_t ConvolutionProto::_internal_input_address() const {
  return _impl_.input_address_;
}
inline int64_t ConvolutionProto::input_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.input_address)
  return _internal_input_address();
}
inline void ConvolutionProto::_internal_set_input_address(int64_t value) {
  
  _impl_.input_address_ = value;
}
inline void ConvolutionProto::set_input_address(int64_t value) {
  _internal_set_input_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.input_address)
}

// int64 filter_address = 10;
inline void ConvolutionProto::clear_filter_address() {
  _impl_.filter_address_ = int64_t{0};
}
inline int64_t ConvolutionProto::_internal_filter_address() const {
  return _impl_.filter_address_;
}
inline int64_t ConvolutionProto::filter_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.filter_address)
  return _internal_filter_address();
}
inline void ConvolutionProto::_internal_set_filter_address(int64_t value) {
  
  _impl_.filter_address_ = value;
}
inline void ConvolutionProto::set_filter_address(int64_t value) {
  _internal_set_filter_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.filter_address)
}

// int64 output_address = 11;
inline void ConvolutionProto::clear_output_address() {
  _impl_.output_address_ = int64_t{0};
}
inline int64_t ConvolutionProto::_internal_output_address() const {
  return _impl_.output_address_;
}
inline int64_t ConvolutionProto::output_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.output_address)
  return _internal_output_address();
}
inline void ConvolutionProto::_internal_set_output_address(int64_t value) {
  
  _impl_.output_address_ = value;
}
inline void ConvolutionProto::set_output_address(int64_t value) {
  _internal_set_output_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.output_address)
}

// int64 bias_address = 12;
inline void ConvolutionProto::clear_bias_address() {
  _impl_.bias_address_ = int64_t{0};
}
inline int64_t ConvolutionProto::_internal_bias_address() const {
  return _impl_.bias_address_;
}
inline int64_t ConvolutionProto::bias_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.bias_address)
  return _internal_bias_address();
}
inline void ConvolutionProto::_internal_set_bias_address(int64_t value) {
  
  _impl_.bias_address_ = value;
}
inline void ConvolutionProto::set_bias_address(int64_t value) {
  _internal_set_bias_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.bias_address)
}

// int64 side_input_address = 13;
inline void ConvolutionProto::clear_side_input_address() {
  _impl_.side_input_address_ = int64_t{0};
}
inline int64_t ConvolutionProto::_internal_side_input_address() const {
  return _impl_.side_input_address_;
}
inline int64_t ConvolutionProto::side_input_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.ConvolutionProto.side_input_address)
  return _internal_side_input_address();
}
inline void ConvolutionProto::_internal_set_side_input_address(int64_t value) {
  
  _impl_.side_input_address_ = value;
}
inline void ConvolutionProto::set_side_input_address(int64_t value) {
  _internal_set_side_input_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.ConvolutionProto.side_input_address)
}

// -------------------------------------------------------------------

// MatmulProto

// .stream_executor.dnn.DataType ab_dtype = 1;
inline void MatmulProto::clear_ab_dtype() {
  _impl_.ab_dtype_ = 0;
}
inline ::stream_executor::dnn::DataType MatmulProto::_internal_ab_dtype() const {
  return static_cast< ::stream_executor::dnn::DataType >(_impl_.ab_dtype_);
}
inline ::stream_executor::dnn::DataType MatmulProto::ab_dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.ab_dtype)
  return _internal_ab_dtype();
}
inline void MatmulProto::_internal_set_ab_dtype(::stream_executor::dnn::DataType value) {
  
  _impl_.ab_dtype_ = value;
}
inline void MatmulProto::set_ab_dtype(::stream_executor::dnn::DataType value) {
  _internal_set_ab_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.ab_dtype)
}

// .stream_executor.dnn.DataType c_dtype = 2;
inline void MatmulProto::clear_c_dtype() {
  _impl_.c_dtype_ = 0;
}
inline ::stream_executor::dnn::DataType MatmulProto::_internal_c_dtype() const {
  return static_cast< ::stream_executor::dnn::DataType >(_impl_.c_dtype_);
}
inline ::stream_executor::dnn::DataType MatmulProto::c_dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.c_dtype)
  return _internal_c_dtype();
}
inline void MatmulProto::_internal_set_c_dtype(::stream_executor::dnn::DataType value) {
  
  _impl_.c_dtype_ = value;
}
inline void MatmulProto::set_c_dtype(::stream_executor::dnn::DataType value) {
  _internal_set_c_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.c_dtype)
}

// bool trans_a = 3;
inline void MatmulProto::clear_trans_a() {
  _impl_.trans_a_ = false;
}
inline bool MatmulProto::_internal_trans_a() const {
  return _impl_.trans_a_;
}
inline bool MatmulProto::trans_a() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.trans_a)
  return _internal_trans_a();
}
inline void MatmulProto::_internal_set_trans_a(bool value) {
  
  _impl_.trans_a_ = value;
}
inline void MatmulProto::set_trans_a(bool value) {
  _internal_set_trans_a(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.trans_a)
}

// bool trans_b = 4;
inline void MatmulProto::clear_trans_b() {
  _impl_.trans_b_ = false;
}
inline bool MatmulProto::_internal_trans_b() const {
  return _impl_.trans_b_;
}
inline bool MatmulProto::trans_b() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.trans_b)
  return _internal_trans_b();
}
inline void MatmulProto::_internal_set_trans_b(bool value) {
  
  _impl_.trans_b_ = value;
}
inline void MatmulProto::set_trans_b(bool value) {
  _internal_set_trans_b(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.trans_b)
}

// uint64 m = 5;
inline void MatmulProto::clear_m() {
  _impl_.m_ = uint64_t{0u};
}
inline uint64_t MatmulProto::_internal_m() const {
  return _impl_.m_;
}
inline uint64_t MatmulProto::m() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.m)
  return _internal_m();
}
inline void MatmulProto::_internal_set_m(uint64_t value) {
  
  _impl_.m_ = value;
}
inline void MatmulProto::set_m(uint64_t value) {
  _internal_set_m(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.m)
}

// uint64 n = 6;
inline void MatmulProto::clear_n() {
  _impl_.n_ = uint64_t{0u};
}
inline uint64_t MatmulProto::_internal_n() const {
  return _impl_.n_;
}
inline uint64_t MatmulProto::n() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.n)
  return _internal_n();
}
inline void MatmulProto::_internal_set_n(uint64_t value) {
  
  _impl_.n_ = value;
}
inline void MatmulProto::set_n(uint64_t value) {
  _internal_set_n(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.n)
}

// uint64 k = 7;
inline void MatmulProto::clear_k() {
  _impl_.k_ = uint64_t{0u};
}
inline uint64_t MatmulProto::_internal_k() const {
  return _impl_.k_;
}
inline uint64_t MatmulProto::k() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.k)
  return _internal_k();
}
inline void MatmulProto::_internal_set_k(uint64_t value) {
  
  _impl_.k_ = value;
}
inline void MatmulProto::set_k(uint64_t value) {
  _internal_set_k(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.k)
}

// int64 lda = 8;
inline void MatmulProto::clear_lda() {
  _impl_.lda_ = int64_t{0};
}
inline int64_t MatmulProto::_internal_lda() const {
  return _impl_.lda_;
}
inline int64_t MatmulProto::lda() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.lda)
  return _internal_lda();
}
inline void MatmulProto::_internal_set_lda(int64_t value) {
  
  _impl_.lda_ = value;
}
inline void MatmulProto::set_lda(int64_t value) {
  _internal_set_lda(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.lda)
}

// int64 ldb = 9;
inline void MatmulProto::clear_ldb() {
  _impl_.ldb_ = int64_t{0};
}
inline int64_t MatmulProto::_internal_ldb() const {
  return _impl_.ldb_;
}
inline int64_t MatmulProto::ldb() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.ldb)
  return _internal_ldb();
}
inline void MatmulProto::_internal_set_ldb(int64_t value) {
  
  _impl_.ldb_ = value;
}
inline void MatmulProto::set_ldb(int64_t value) {
  _internal_set_ldb(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.ldb)
}

// int64 ldc = 10;
inline void MatmulProto::clear_ldc() {
  _impl_.ldc_ = int64_t{0};
}
inline int64_t MatmulProto::_internal_ldc() const {
  return _impl_.ldc_;
}
inline int64_t MatmulProto::ldc() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.ldc)
  return _internal_ldc();
}
inline void MatmulProto::_internal_set_ldc(int64_t value) {
  
  _impl_.ldc_ = value;
}
inline void MatmulProto::set_ldc(int64_t value) {
  _internal_set_ldc(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.ldc)
}

// .stream_executor.dnn.ActivationMode activation = 11;
inline void MatmulProto::clear_activation() {
  _impl_.activation_ = 0;
}
inline ::stream_executor::dnn::ActivationMode MatmulProto::_internal_activation() const {
  return static_cast< ::stream_executor::dnn::ActivationMode >(_impl_.activation_);
}
inline ::stream_executor::dnn::ActivationMode MatmulProto::activation() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.activation)
  return _internal_activation();
}
inline void MatmulProto::_internal_set_activation(::stream_executor::dnn::ActivationMode value) {
  
  _impl_.activation_ = value;
}
inline void MatmulProto::set_activation(::stream_executor::dnn::ActivationMode value) {
  _internal_set_activation(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.activation)
}

// int64 a_address = 12;
inline void MatmulProto::clear_a_address() {
  _impl_.a_address_ = int64_t{0};
}
inline int64_t MatmulProto::_internal_a_address() const {
  return _impl_.a_address_;
}
inline int64_t MatmulProto::a_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.a_address)
  return _internal_a_address();
}
inline void MatmulProto::_internal_set_a_address(int64_t value) {
  
  _impl_.a_address_ = value;
}
inline void MatmulProto::set_a_address(int64_t value) {
  _internal_set_a_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.a_address)
}

// int64 b_address = 13;
inline void MatmulProto::clear_b_address() {
  _impl_.b_address_ = int64_t{0};
}
inline int64_t MatmulProto::_internal_b_address() const {
  return _impl_.b_address_;
}
inline int64_t MatmulProto::b_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.b_address)
  return _internal_b_address();
}
inline void MatmulProto::_internal_set_b_address(int64_t value) {
  
  _impl_.b_address_ = value;
}
inline void MatmulProto::set_b_address(int64_t value) {
  _internal_set_b_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.b_address)
}

// int64 c_address = 14;
inline void MatmulProto::clear_c_address() {
  _impl_.c_address_ = int64_t{0};
}
inline int64_t MatmulProto::_internal_c_address() const {
  return _impl_.c_address_;
}
inline int64_t MatmulProto::c_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.c_address)
  return _internal_c_address();
}
inline void MatmulProto::_internal_set_c_address(int64_t value) {
  
  _impl_.c_address_ = value;
}
inline void MatmulProto::set_c_address(int64_t value) {
  _internal_set_c_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.c_address)
}

// int64 bias_address = 15;
inline void MatmulProto::clear_bias_address() {
  _impl_.bias_address_ = int64_t{0};
}
inline int64_t MatmulProto::_internal_bias_address() const {
  return _impl_.bias_address_;
}
inline int64_t MatmulProto::bias_address() const {
  // @@protoc_insertion_point(field_get:tensorflow.MatmulProto.bias_address)
  return _internal_bias_address();
}
inline void MatmulProto::_internal_set_bias_address(int64_t value) {
  
  _impl_.bias_address_ = value;
}
inline void MatmulProto::set_bias_address(int64_t value) {
  _internal_set_bias_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.MatmulProto.bias_address)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fconv_5fautotuning_2eproto
