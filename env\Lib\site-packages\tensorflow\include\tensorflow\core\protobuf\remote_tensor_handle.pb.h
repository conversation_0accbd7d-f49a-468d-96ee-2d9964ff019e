// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/remote_tensor_handle.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fremote_5ftensor_5fhandle_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fremote_5ftensor_5fhandle_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fremote_5ftensor_5fhandle_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fremote_5ftensor_5fhandle_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fremote_5ftensor_5fhandle_2eproto;
namespace tensorflow {
namespace eager {
class RemoteTensorHandle;
struct RemoteTensorHandleDefaultTypeInternal;
extern RemoteTensorHandleDefaultTypeInternal _RemoteTensorHandle_default_instance_;
class ResourceDtypeAndShape;
struct ResourceDtypeAndShapeDefaultTypeInternal;
extern ResourceDtypeAndShapeDefaultTypeInternal _ResourceDtypeAndShape_default_instance_;
}  // namespace eager
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::eager::RemoteTensorHandle* Arena::CreateMaybeMessage<::tensorflow::eager::RemoteTensorHandle>(Arena*);
template<> ::tensorflow::eager::ResourceDtypeAndShape* Arena::CreateMaybeMessage<::tensorflow::eager::ResourceDtypeAndShape>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace eager {

// ===================================================================

class ResourceDtypeAndShape final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.ResourceDtypeAndShape) */ {
 public:
  inline ResourceDtypeAndShape() : ResourceDtypeAndShape(nullptr) {}
  ~ResourceDtypeAndShape() override;
  explicit PROTOBUF_CONSTEXPR ResourceDtypeAndShape(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResourceDtypeAndShape(const ResourceDtypeAndShape& from);
  ResourceDtypeAndShape(ResourceDtypeAndShape&& from) noexcept
    : ResourceDtypeAndShape() {
    *this = ::std::move(from);
  }

  inline ResourceDtypeAndShape& operator=(const ResourceDtypeAndShape& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResourceDtypeAndShape& operator=(ResourceDtypeAndShape&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResourceDtypeAndShape& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResourceDtypeAndShape* internal_default_instance() {
    return reinterpret_cast<const ResourceDtypeAndShape*>(
               &_ResourceDtypeAndShape_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ResourceDtypeAndShape& a, ResourceDtypeAndShape& b) {
    a.Swap(&b);
  }
  inline void Swap(ResourceDtypeAndShape* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResourceDtypeAndShape* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResourceDtypeAndShape* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResourceDtypeAndShape>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ResourceDtypeAndShape& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ResourceDtypeAndShape& from) {
    ResourceDtypeAndShape::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResourceDtypeAndShape* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.ResourceDtypeAndShape";
  }
  protected:
  explicit ResourceDtypeAndShape(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kDtypeFieldNumber = 1,
  };
  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.ResourceDtypeAndShape)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* shape_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fremote_5ftensor_5fhandle_2eproto;
};
// -------------------------------------------------------------------

class RemoteTensorHandle final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.eager.RemoteTensorHandle) */ {
 public:
  inline RemoteTensorHandle() : RemoteTensorHandle(nullptr) {}
  ~RemoteTensorHandle() override;
  explicit PROTOBUF_CONSTEXPR RemoteTensorHandle(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RemoteTensorHandle(const RemoteTensorHandle& from);
  RemoteTensorHandle(RemoteTensorHandle&& from) noexcept
    : RemoteTensorHandle() {
    *this = ::std::move(from);
  }

  inline RemoteTensorHandle& operator=(const RemoteTensorHandle& from) {
    CopyFrom(from);
    return *this;
  }
  inline RemoteTensorHandle& operator=(RemoteTensorHandle&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RemoteTensorHandle& default_instance() {
    return *internal_default_instance();
  }
  static inline const RemoteTensorHandle* internal_default_instance() {
    return reinterpret_cast<const RemoteTensorHandle*>(
               &_RemoteTensorHandle_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(RemoteTensorHandle& a, RemoteTensorHandle& b) {
    a.Swap(&b);
  }
  inline void Swap(RemoteTensorHandle* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RemoteTensorHandle* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RemoteTensorHandle* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RemoteTensorHandle>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RemoteTensorHandle& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RemoteTensorHandle& from) {
    RemoteTensorHandle::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RemoteTensorHandle* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.eager.RemoteTensorHandle";
  }
  protected:
  explicit RemoteTensorHandle(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResourceDtypesAndShapesFieldNumber = 6,
    kDeviceFieldNumber = 3,
    kOpDeviceFieldNumber = 4,
    kOpIdFieldNumber = 1,
    kOutputNumFieldNumber = 2,
    kDtypeFieldNumber = 5,
  };
  // repeated .tensorflow.eager.ResourceDtypeAndShape resource_dtypes_and_shapes = 6;
  int resource_dtypes_and_shapes_size() const;
  private:
  int _internal_resource_dtypes_and_shapes_size() const;
  public:
  void clear_resource_dtypes_and_shapes();
  ::tensorflow::eager::ResourceDtypeAndShape* mutable_resource_dtypes_and_shapes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::ResourceDtypeAndShape >*
      mutable_resource_dtypes_and_shapes();
  private:
  const ::tensorflow::eager::ResourceDtypeAndShape& _internal_resource_dtypes_and_shapes(int index) const;
  ::tensorflow::eager::ResourceDtypeAndShape* _internal_add_resource_dtypes_and_shapes();
  public:
  const ::tensorflow::eager::ResourceDtypeAndShape& resource_dtypes_and_shapes(int index) const;
  ::tensorflow::eager::ResourceDtypeAndShape* add_resource_dtypes_and_shapes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::ResourceDtypeAndShape >&
      resource_dtypes_and_shapes() const;

  // string device = 3;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // string op_device = 4;
  void clear_op_device();
  const std::string& op_device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op_device();
  PROTOBUF_NODISCARD std::string* release_op_device();
  void set_allocated_op_device(std::string* op_device);
  private:
  const std::string& _internal_op_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op_device(const std::string& value);
  std::string* _internal_mutable_op_device();
  public:

  // int64 op_id = 1;
  void clear_op_id();
  int64_t op_id() const;
  void set_op_id(int64_t value);
  private:
  int64_t _internal_op_id() const;
  void _internal_set_op_id(int64_t value);
  public:

  // int32 output_num = 2;
  void clear_output_num();
  int32_t output_num() const;
  void set_output_num(int32_t value);
  private:
  int32_t _internal_output_num() const;
  void _internal_set_output_num(int32_t value);
  public:

  // .tensorflow.DataType dtype = 5;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.eager.RemoteTensorHandle)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::ResourceDtypeAndShape > resource_dtypes_and_shapes_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_device_;
    int64_t op_id_;
    int32_t output_num_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fremote_5ftensor_5fhandle_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ResourceDtypeAndShape

// .tensorflow.DataType dtype = 1;
inline void ResourceDtypeAndShape::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType ResourceDtypeAndShape::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType ResourceDtypeAndShape::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.ResourceDtypeAndShape.dtype)
  return _internal_dtype();
}
inline void ResourceDtypeAndShape::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void ResourceDtypeAndShape::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.ResourceDtypeAndShape.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool ResourceDtypeAndShape::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool ResourceDtypeAndShape::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& ResourceDtypeAndShape::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& ResourceDtypeAndShape::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.ResourceDtypeAndShape.shape)
  return _internal_shape();
}
inline void ResourceDtypeAndShape::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.eager.ResourceDtypeAndShape.shape)
}
inline ::tensorflow::TensorShapeProto* ResourceDtypeAndShape::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* ResourceDtypeAndShape::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.ResourceDtypeAndShape.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* ResourceDtypeAndShape::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* ResourceDtypeAndShape::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.ResourceDtypeAndShape.shape)
  return _msg;
}
inline void ResourceDtypeAndShape::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.ResourceDtypeAndShape.shape)
}

// -------------------------------------------------------------------

// RemoteTensorHandle

// int64 op_id = 1;
inline void RemoteTensorHandle::clear_op_id() {
  _impl_.op_id_ = int64_t{0};
}
inline int64_t RemoteTensorHandle::_internal_op_id() const {
  return _impl_.op_id_;
}
inline int64_t RemoteTensorHandle::op_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoteTensorHandle.op_id)
  return _internal_op_id();
}
inline void RemoteTensorHandle::_internal_set_op_id(int64_t value) {
  
  _impl_.op_id_ = value;
}
inline void RemoteTensorHandle::set_op_id(int64_t value) {
  _internal_set_op_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.RemoteTensorHandle.op_id)
}

// int32 output_num = 2;
inline void RemoteTensorHandle::clear_output_num() {
  _impl_.output_num_ = 0;
}
inline int32_t RemoteTensorHandle::_internal_output_num() const {
  return _impl_.output_num_;
}
inline int32_t RemoteTensorHandle::output_num() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoteTensorHandle.output_num)
  return _internal_output_num();
}
inline void RemoteTensorHandle::_internal_set_output_num(int32_t value) {
  
  _impl_.output_num_ = value;
}
inline void RemoteTensorHandle::set_output_num(int32_t value) {
  _internal_set_output_num(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.RemoteTensorHandle.output_num)
}

// string device = 3;
inline void RemoteTensorHandle::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& RemoteTensorHandle::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoteTensorHandle.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RemoteTensorHandle::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.eager.RemoteTensorHandle.device)
}
inline std::string* RemoteTensorHandle::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RemoteTensorHandle.device)
  return _s;
}
inline const std::string& RemoteTensorHandle::_internal_device() const {
  return _impl_.device_.Get();
}
inline void RemoteTensorHandle::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* RemoteTensorHandle::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* RemoteTensorHandle::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RemoteTensorHandle.device)
  return _impl_.device_.Release();
}
inline void RemoteTensorHandle::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RemoteTensorHandle.device)
}

// string op_device = 4;
inline void RemoteTensorHandle::clear_op_device() {
  _impl_.op_device_.ClearToEmpty();
}
inline const std::string& RemoteTensorHandle::op_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoteTensorHandle.op_device)
  return _internal_op_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RemoteTensorHandle::set_op_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.eager.RemoteTensorHandle.op_device)
}
inline std::string* RemoteTensorHandle::mutable_op_device() {
  std::string* _s = _internal_mutable_op_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RemoteTensorHandle.op_device)
  return _s;
}
inline const std::string& RemoteTensorHandle::_internal_op_device() const {
  return _impl_.op_device_.Get();
}
inline void RemoteTensorHandle::_internal_set_op_device(const std::string& value) {
  
  _impl_.op_device_.Set(value, GetArenaForAllocation());
}
inline std::string* RemoteTensorHandle::_internal_mutable_op_device() {
  
  return _impl_.op_device_.Mutable(GetArenaForAllocation());
}
inline std::string* RemoteTensorHandle::release_op_device() {
  // @@protoc_insertion_point(field_release:tensorflow.eager.RemoteTensorHandle.op_device)
  return _impl_.op_device_.Release();
}
inline void RemoteTensorHandle::set_allocated_op_device(std::string* op_device) {
  if (op_device != nullptr) {
    
  } else {
    
  }
  _impl_.op_device_.SetAllocated(op_device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_device_.IsDefault()) {
    _impl_.op_device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.eager.RemoteTensorHandle.op_device)
}

// .tensorflow.DataType dtype = 5;
inline void RemoteTensorHandle::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType RemoteTensorHandle::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType RemoteTensorHandle::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoteTensorHandle.dtype)
  return _internal_dtype();
}
inline void RemoteTensorHandle::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void RemoteTensorHandle::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.eager.RemoteTensorHandle.dtype)
}

// repeated .tensorflow.eager.ResourceDtypeAndShape resource_dtypes_and_shapes = 6;
inline int RemoteTensorHandle::_internal_resource_dtypes_and_shapes_size() const {
  return _impl_.resource_dtypes_and_shapes_.size();
}
inline int RemoteTensorHandle::resource_dtypes_and_shapes_size() const {
  return _internal_resource_dtypes_and_shapes_size();
}
inline void RemoteTensorHandle::clear_resource_dtypes_and_shapes() {
  _impl_.resource_dtypes_and_shapes_.Clear();
}
inline ::tensorflow::eager::ResourceDtypeAndShape* RemoteTensorHandle::mutable_resource_dtypes_and_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.eager.RemoteTensorHandle.resource_dtypes_and_shapes)
  return _impl_.resource_dtypes_and_shapes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::ResourceDtypeAndShape >*
RemoteTensorHandle::mutable_resource_dtypes_and_shapes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.eager.RemoteTensorHandle.resource_dtypes_and_shapes)
  return &_impl_.resource_dtypes_and_shapes_;
}
inline const ::tensorflow::eager::ResourceDtypeAndShape& RemoteTensorHandle::_internal_resource_dtypes_and_shapes(int index) const {
  return _impl_.resource_dtypes_and_shapes_.Get(index);
}
inline const ::tensorflow::eager::ResourceDtypeAndShape& RemoteTensorHandle::resource_dtypes_and_shapes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.eager.RemoteTensorHandle.resource_dtypes_and_shapes)
  return _internal_resource_dtypes_and_shapes(index);
}
inline ::tensorflow::eager::ResourceDtypeAndShape* RemoteTensorHandle::_internal_add_resource_dtypes_and_shapes() {
  return _impl_.resource_dtypes_and_shapes_.Add();
}
inline ::tensorflow::eager::ResourceDtypeAndShape* RemoteTensorHandle::add_resource_dtypes_and_shapes() {
  ::tensorflow::eager::ResourceDtypeAndShape* _add = _internal_add_resource_dtypes_and_shapes();
  // @@protoc_insertion_point(field_add:tensorflow.eager.RemoteTensorHandle.resource_dtypes_and_shapes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::eager::ResourceDtypeAndShape >&
RemoteTensorHandle::resource_dtypes_and_shapes() const {
  // @@protoc_insertion_point(field_list:tensorflow.eager.RemoteTensorHandle.resource_dtypes_and_shapes)
  return _impl_.resource_dtypes_and_shapes_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace eager
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fremote_5ftensor_5fhandle_2eproto
