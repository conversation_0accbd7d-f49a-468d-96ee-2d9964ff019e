.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_dn_get_rdn_ava" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_dn_get_rdn_ava \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_dn_get_rdn_ava(gnutls_x509_dn_t " dn ", int " irdn ", int " iava ", gnutls_x509_ava_st * " ava ");"
.SH ARGUMENTS
.IP "gnutls_x509_dn_t dn" 12
a pointer to DN
.IP "int irdn" 12
index of RDN
.IP "int iava" 12
index of AVA.
.IP "gnutls_x509_ava_st * ava" 12
Pointer to structure which will hold output information.
.SH "DESCRIPTION"
Get pointers to data within the DN. The format of the  \fIava\fP structure
is shown below.

struct gnutls_x509_ava_st {
gnutls_datum_t oid;
gnutls_datum_t value;
unsigned long value_tag;
};

The X.509 distinguished name is a sequence of sequences of strings
and this is what the  \fIirdn\fP and  \fIiava\fP indexes model.

Note that  \fIava\fP will contain pointers into the  \fIdn\fP structure which
in turns points to the original certificate. Thus you should not
modify any data or deallocate any of those.

This is a low\-level function that requires the caller to do the
value conversions when necessary (e.g. from UCS\-2).
.SH "RETURNS"
Returns 0 on success, or an error code.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
