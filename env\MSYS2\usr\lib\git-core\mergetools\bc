diff_cmd () {
	"$merge_tool_path" "$LOCAL" "$REMOTE"
}

diff_cmd_help () {
	echo "Use Beyond Compare (requires a graphical session)"
}

merge_cmd () {
	if $base_present
	then
		"$merge_tool_path" "$LOCAL" "$REMOTE" "$BASE" \
			-mergeoutput="$MERGED"
	else
		"$merge_tool_path" "$LOCAL" "$REMOTE" \
			-mergeoutput="$MERGED"
	fi
}

merge_cmd_help () {
	echo "Use Beyond Compare (requires a graphical session)"
}

translate_merge_tool_path() {
	if type bcomp >/dev/null 2>/dev/null
	then
		echo bcomp
	else
		echo bcompare
	fi
}

list_tool_variants () {
	echo bc
	echo bc3
	echo bc4
}
