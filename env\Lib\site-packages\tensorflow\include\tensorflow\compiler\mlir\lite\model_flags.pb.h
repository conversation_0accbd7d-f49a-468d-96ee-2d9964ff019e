// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/compiler/mlir/lite/model_flags.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/compiler/mlir/lite/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto;
namespace tflite {
class ArraysExtraInfo;
struct ArraysExtraInfoDefaultTypeInternal;
extern ArraysExtraInfoDefaultTypeInternal _ArraysExtraInfo_default_instance_;
class ArraysExtraInfo_Entry;
struct ArraysExtraInfo_EntryDefaultTypeInternal;
extern ArraysExtraInfo_EntryDefaultTypeInternal _ArraysExtraInfo_Entry_default_instance_;
class InputArray;
struct InputArrayDefaultTypeInternal;
extern InputArrayDefaultTypeInternal _InputArray_default_instance_;
class InputArrayShape;
struct InputArrayShapeDefaultTypeInternal;
extern InputArrayShapeDefaultTypeInternal _InputArrayShape_default_instance_;
class ModelFlags;
struct ModelFlagsDefaultTypeInternal;
extern ModelFlagsDefaultTypeInternal _ModelFlags_default_instance_;
class ModelFlags_ModelCheck;
struct ModelFlags_ModelCheckDefaultTypeInternal;
extern ModelFlags_ModelCheckDefaultTypeInternal _ModelFlags_ModelCheck_default_instance_;
class RnnState;
struct RnnStateDefaultTypeInternal;
extern RnnStateDefaultTypeInternal _RnnState_default_instance_;
}  // namespace tflite
PROTOBUF_NAMESPACE_OPEN
template<> ::tflite::ArraysExtraInfo* Arena::CreateMaybeMessage<::tflite::ArraysExtraInfo>(Arena*);
template<> ::tflite::ArraysExtraInfo_Entry* Arena::CreateMaybeMessage<::tflite::ArraysExtraInfo_Entry>(Arena*);
template<> ::tflite::InputArray* Arena::CreateMaybeMessage<::tflite::InputArray>(Arena*);
template<> ::tflite::InputArrayShape* Arena::CreateMaybeMessage<::tflite::InputArrayShape>(Arena*);
template<> ::tflite::ModelFlags* Arena::CreateMaybeMessage<::tflite::ModelFlags>(Arena*);
template<> ::tflite::ModelFlags_ModelCheck* Arena::CreateMaybeMessage<::tflite::ModelFlags_ModelCheck>(Arena*);
template<> ::tflite::RnnState* Arena::CreateMaybeMessage<::tflite::RnnState>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tflite {

enum ModelFlags_HloFileType : int {
  ModelFlags_HloFileType_UNKNOWN = 0,
  ModelFlags_HloFileType_HLO_TEXT = 1,
  ModelFlags_HloFileType_HLO_PROTO = 2
};
bool ModelFlags_HloFileType_IsValid(int value);
constexpr ModelFlags_HloFileType ModelFlags_HloFileType_HloFileType_MIN = ModelFlags_HloFileType_UNKNOWN;
constexpr ModelFlags_HloFileType ModelFlags_HloFileType_HloFileType_MAX = ModelFlags_HloFileType_HLO_PROTO;
constexpr int ModelFlags_HloFileType_HloFileType_ARRAYSIZE = ModelFlags_HloFileType_HloFileType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ModelFlags_HloFileType_descriptor();
template<typename T>
inline const std::string& ModelFlags_HloFileType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ModelFlags_HloFileType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ModelFlags_HloFileType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ModelFlags_HloFileType_descriptor(), enum_t_value);
}
inline bool ModelFlags_HloFileType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ModelFlags_HloFileType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ModelFlags_HloFileType>(
    ModelFlags_HloFileType_descriptor(), name, value);
}
// ===================================================================

class InputArrayShape final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tflite.InputArrayShape) */ {
 public:
  inline InputArrayShape() : InputArrayShape(nullptr) {}
  ~InputArrayShape() override;
  explicit PROTOBUF_CONSTEXPR InputArrayShape(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InputArrayShape(const InputArrayShape& from);
  InputArrayShape(InputArrayShape&& from) noexcept
    : InputArrayShape() {
    *this = ::std::move(from);
  }

  inline InputArrayShape& operator=(const InputArrayShape& from) {
    CopyFrom(from);
    return *this;
  }
  inline InputArrayShape& operator=(InputArrayShape&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InputArrayShape& default_instance() {
    return *internal_default_instance();
  }
  static inline const InputArrayShape* internal_default_instance() {
    return reinterpret_cast<const InputArrayShape*>(
               &_InputArrayShape_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(InputArrayShape& a, InputArrayShape& b) {
    a.Swap(&b);
  }
  inline void Swap(InputArrayShape* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InputArrayShape* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InputArrayShape* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InputArrayShape>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const InputArrayShape& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const InputArrayShape& from) {
    InputArrayShape::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InputArrayShape* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tflite.InputArrayShape";
  }
  protected:
  explicit InputArrayShape(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDimsFieldNumber = 2,
    kUnknownRankFieldNumber = 3,
  };
  // repeated int32 dims = 2;
  int dims_size() const;
  private:
  int _internal_dims_size() const;
  public:
  void clear_dims();
  private:
  int32_t _internal_dims(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_dims() const;
  void _internal_add_dims(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_dims();
  public:
  int32_t dims(int index) const;
  void set_dims(int index, int32_t value);
  void add_dims(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      dims() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_dims();

  // optional bool unknown_rank = 3;
  bool has_unknown_rank() const;
  private:
  bool _internal_has_unknown_rank() const;
  public:
  void clear_unknown_rank();
  bool unknown_rank() const;
  void set_unknown_rank(bool value);
  private:
  bool _internal_unknown_rank() const;
  void _internal_set_unknown_rank(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tflite.InputArrayShape)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > dims_;
    bool unknown_rank_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto;
};
// -------------------------------------------------------------------

class InputArray final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tflite.InputArray) */ {
 public:
  inline InputArray() : InputArray(nullptr) {}
  ~InputArray() override;
  explicit PROTOBUF_CONSTEXPR InputArray(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InputArray(const InputArray& from);
  InputArray(InputArray&& from) noexcept
    : InputArray() {
    *this = ::std::move(from);
  }

  inline InputArray& operator=(const InputArray& from) {
    CopyFrom(from);
    return *this;
  }
  inline InputArray& operator=(InputArray&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InputArray& default_instance() {
    return *internal_default_instance();
  }
  static inline const InputArray* internal_default_instance() {
    return reinterpret_cast<const InputArray*>(
               &_InputArray_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(InputArray& a, InputArray& b) {
    a.Swap(&b);
  }
  inline void Swap(InputArray* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InputArray* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InputArray* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InputArray>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const InputArray& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const InputArray& from) {
    InputArray::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InputArray* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tflite.InputArray";
  }
  protected:
  explicit InputArray(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kShapeFieldNumber = 6,
    kMeanValueFieldNumber = 3,
    kDataTypeFieldNumber = 5,
    kStdValueFieldNumber = 4,
  };
  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional .tflite.InputArrayShape shape = 6;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tflite::InputArrayShape& shape() const;
  PROTOBUF_NODISCARD ::tflite::InputArrayShape* release_shape();
  ::tflite::InputArrayShape* mutable_shape();
  void set_allocated_shape(::tflite::InputArrayShape* shape);
  private:
  const ::tflite::InputArrayShape& _internal_shape() const;
  ::tflite::InputArrayShape* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tflite::InputArrayShape* shape);
  ::tflite::InputArrayShape* unsafe_arena_release_shape();

  // optional float mean_value = 3;
  bool has_mean_value() const;
  private:
  bool _internal_has_mean_value() const;
  public:
  void clear_mean_value();
  float mean_value() const;
  void set_mean_value(float value);
  private:
  float _internal_mean_value() const;
  void _internal_set_mean_value(float value);
  public:

  // optional .tflite.IODataType data_type = 5;
  bool has_data_type() const;
  private:
  bool _internal_has_data_type() const;
  public:
  void clear_data_type();
  ::tflite::IODataType data_type() const;
  void set_data_type(::tflite::IODataType value);
  private:
  ::tflite::IODataType _internal_data_type() const;
  void _internal_set_data_type(::tflite::IODataType value);
  public:

  // optional float std_value = 4 [default = 1];
  bool has_std_value() const;
  private:
  bool _internal_has_std_value() const;
  public:
  void clear_std_value();
  float std_value() const;
  void set_std_value(float value);
  private:
  float _internal_std_value() const;
  void _internal_set_std_value(float value);
  public:

  // @@protoc_insertion_point(class_scope:tflite.InputArray)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tflite::InputArrayShape* shape_;
    float mean_value_;
    int data_type_;
    float std_value_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto;
};
// -------------------------------------------------------------------

class RnnState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tflite.RnnState) */ {
 public:
  inline RnnState() : RnnState(nullptr) {}
  ~RnnState() override;
  explicit PROTOBUF_CONSTEXPR RnnState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RnnState(const RnnState& from);
  RnnState(RnnState&& from) noexcept
    : RnnState() {
    *this = ::std::move(from);
  }

  inline RnnState& operator=(const RnnState& from) {
    CopyFrom(from);
    return *this;
  }
  inline RnnState& operator=(RnnState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RnnState& default_instance() {
    return *internal_default_instance();
  }
  static inline const RnnState* internal_default_instance() {
    return reinterpret_cast<const RnnState*>(
               &_RnnState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(RnnState& a, RnnState& b) {
    a.Swap(&b);
  }
  inline void Swap(RnnState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RnnState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RnnState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RnnState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RnnState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RnnState& from) {
    RnnState::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RnnState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tflite.RnnState";
  }
  protected:
  explicit RnnState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStateArrayFieldNumber = 1,
    kBackEdgeSourceArrayFieldNumber = 2,
    kSizeFieldNumber = 3,
    kNumDimsFieldNumber = 4,
    kDiscardableFieldNumber = 5,
  };
  // optional string state_array = 1;
  bool has_state_array() const;
  private:
  bool _internal_has_state_array() const;
  public:
  void clear_state_array();
  const std::string& state_array() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_state_array(ArgT0&& arg0, ArgT... args);
  std::string* mutable_state_array();
  PROTOBUF_NODISCARD std::string* release_state_array();
  void set_allocated_state_array(std::string* state_array);
  private:
  const std::string& _internal_state_array() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_state_array(const std::string& value);
  std::string* _internal_mutable_state_array();
  public:

  // optional string back_edge_source_array = 2;
  bool has_back_edge_source_array() const;
  private:
  bool _internal_has_back_edge_source_array() const;
  public:
  void clear_back_edge_source_array();
  const std::string& back_edge_source_array() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_back_edge_source_array(ArgT0&& arg0, ArgT... args);
  std::string* mutable_back_edge_source_array();
  PROTOBUF_NODISCARD std::string* release_back_edge_source_array();
  void set_allocated_back_edge_source_array(std::string* back_edge_source_array);
  private:
  const std::string& _internal_back_edge_source_array() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_back_edge_source_array(const std::string& value);
  std::string* _internal_mutable_back_edge_source_array();
  public:

  // optional int32 size = 3;
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  int32_t size() const;
  void set_size(int32_t value);
  private:
  int32_t _internal_size() const;
  void _internal_set_size(int32_t value);
  public:

  // optional int32 num_dims = 4;
  bool has_num_dims() const;
  private:
  bool _internal_has_num_dims() const;
  public:
  void clear_num_dims();
  int32_t num_dims() const;
  void set_num_dims(int32_t value);
  private:
  int32_t _internal_num_dims() const;
  void _internal_set_num_dims(int32_t value);
  public:

  // optional bool discardable = 5;
  bool has_discardable() const;
  private:
  bool _internal_has_discardable() const;
  public:
  void clear_discardable();
  bool discardable() const;
  void set_discardable(bool value);
  private:
  bool _internal_discardable() const;
  void _internal_set_discardable(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tflite.RnnState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr state_array_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr back_edge_source_array_;
    int32_t size_;
    int32_t num_dims_;
    bool discardable_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto;
};
// -------------------------------------------------------------------

class ArraysExtraInfo_Entry final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tflite.ArraysExtraInfo.Entry) */ {
 public:
  inline ArraysExtraInfo_Entry() : ArraysExtraInfo_Entry(nullptr) {}
  ~ArraysExtraInfo_Entry() override;
  explicit PROTOBUF_CONSTEXPR ArraysExtraInfo_Entry(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ArraysExtraInfo_Entry(const ArraysExtraInfo_Entry& from);
  ArraysExtraInfo_Entry(ArraysExtraInfo_Entry&& from) noexcept
    : ArraysExtraInfo_Entry() {
    *this = ::std::move(from);
  }

  inline ArraysExtraInfo_Entry& operator=(const ArraysExtraInfo_Entry& from) {
    CopyFrom(from);
    return *this;
  }
  inline ArraysExtraInfo_Entry& operator=(ArraysExtraInfo_Entry&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ArraysExtraInfo_Entry& default_instance() {
    return *internal_default_instance();
  }
  static inline const ArraysExtraInfo_Entry* internal_default_instance() {
    return reinterpret_cast<const ArraysExtraInfo_Entry*>(
               &_ArraysExtraInfo_Entry_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ArraysExtraInfo_Entry& a, ArraysExtraInfo_Entry& b) {
    a.Swap(&b);
  }
  inline void Swap(ArraysExtraInfo_Entry* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ArraysExtraInfo_Entry* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ArraysExtraInfo_Entry* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ArraysExtraInfo_Entry>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ArraysExtraInfo_Entry& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ArraysExtraInfo_Entry& from) {
    ArraysExtraInfo_Entry::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ArraysExtraInfo_Entry* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tflite.ArraysExtraInfo.Entry";
  }
  protected:
  explicit ArraysExtraInfo_Entry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kNameRegexpFieldNumber = 7,
    kShapeFieldNumber = 5,
    kMinFieldNumber = 2,
    kMaxFieldNumber = 3,
    kDataTypeFieldNumber = 4,
    kConstantFloatValueFieldNumber = 6,
  };
  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional string name_regexp = 7;
  bool has_name_regexp() const;
  private:
  bool _internal_has_name_regexp() const;
  public:
  void clear_name_regexp();
  const std::string& name_regexp() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name_regexp(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name_regexp();
  PROTOBUF_NODISCARD std::string* release_name_regexp();
  void set_allocated_name_regexp(std::string* name_regexp);
  private:
  const std::string& _internal_name_regexp() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name_regexp(const std::string& value);
  std::string* _internal_mutable_name_regexp();
  public:

  // optional .tflite.InputArrayShape shape = 5;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tflite::InputArrayShape& shape() const;
  PROTOBUF_NODISCARD ::tflite::InputArrayShape* release_shape();
  ::tflite::InputArrayShape* mutable_shape();
  void set_allocated_shape(::tflite::InputArrayShape* shape);
  private:
  const ::tflite::InputArrayShape& _internal_shape() const;
  ::tflite::InputArrayShape* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tflite::InputArrayShape* shape);
  ::tflite::InputArrayShape* unsafe_arena_release_shape();

  // optional double min = 2;
  bool has_min() const;
  private:
  bool _internal_has_min() const;
  public:
  void clear_min();
  double min() const;
  void set_min(double value);
  private:
  double _internal_min() const;
  void _internal_set_min(double value);
  public:

  // optional double max = 3;
  bool has_max() const;
  private:
  bool _internal_has_max() const;
  public:
  void clear_max();
  double max() const;
  void set_max(double value);
  private:
  double _internal_max() const;
  void _internal_set_max(double value);
  public:

  // optional .tflite.IODataType data_type = 4;
  bool has_data_type() const;
  private:
  bool _internal_has_data_type() const;
  public:
  void clear_data_type();
  ::tflite::IODataType data_type() const;
  void set_data_type(::tflite::IODataType value);
  private:
  ::tflite::IODataType _internal_data_type() const;
  void _internal_set_data_type(::tflite::IODataType value);
  public:

  // optional float constant_float_value = 6;
  bool has_constant_float_value() const;
  private:
  bool _internal_has_constant_float_value() const;
  public:
  void clear_constant_float_value();
  float constant_float_value() const;
  void set_constant_float_value(float value);
  private:
  float _internal_constant_float_value() const;
  void _internal_set_constant_float_value(float value);
  public:

  // @@protoc_insertion_point(class_scope:tflite.ArraysExtraInfo.Entry)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_regexp_;
    ::tflite::InputArrayShape* shape_;
    double min_;
    double max_;
    int data_type_;
    float constant_float_value_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto;
};
// -------------------------------------------------------------------

class ArraysExtraInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tflite.ArraysExtraInfo) */ {
 public:
  inline ArraysExtraInfo() : ArraysExtraInfo(nullptr) {}
  ~ArraysExtraInfo() override;
  explicit PROTOBUF_CONSTEXPR ArraysExtraInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ArraysExtraInfo(const ArraysExtraInfo& from);
  ArraysExtraInfo(ArraysExtraInfo&& from) noexcept
    : ArraysExtraInfo() {
    *this = ::std::move(from);
  }

  inline ArraysExtraInfo& operator=(const ArraysExtraInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline ArraysExtraInfo& operator=(ArraysExtraInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ArraysExtraInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const ArraysExtraInfo* internal_default_instance() {
    return reinterpret_cast<const ArraysExtraInfo*>(
               &_ArraysExtraInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ArraysExtraInfo& a, ArraysExtraInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(ArraysExtraInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ArraysExtraInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ArraysExtraInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ArraysExtraInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ArraysExtraInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ArraysExtraInfo& from) {
    ArraysExtraInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ArraysExtraInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tflite.ArraysExtraInfo";
  }
  protected:
  explicit ArraysExtraInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ArraysExtraInfo_Entry Entry;

  // accessors -------------------------------------------------------

  enum : int {
    kEntriesFieldNumber = 1,
  };
  // repeated .tflite.ArraysExtraInfo.Entry entries = 1;
  int entries_size() const;
  private:
  int _internal_entries_size() const;
  public:
  void clear_entries();
  ::tflite::ArraysExtraInfo_Entry* mutable_entries(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ArraysExtraInfo_Entry >*
      mutable_entries();
  private:
  const ::tflite::ArraysExtraInfo_Entry& _internal_entries(int index) const;
  ::tflite::ArraysExtraInfo_Entry* _internal_add_entries();
  public:
  const ::tflite::ArraysExtraInfo_Entry& entries(int index) const;
  ::tflite::ArraysExtraInfo_Entry* add_entries();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ArraysExtraInfo_Entry >&
      entries() const;

  // @@protoc_insertion_point(class_scope:tflite.ArraysExtraInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ArraysExtraInfo_Entry > entries_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto;
};
// -------------------------------------------------------------------

class ModelFlags_ModelCheck final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tflite.ModelFlags.ModelCheck) */ {
 public:
  inline ModelFlags_ModelCheck() : ModelFlags_ModelCheck(nullptr) {}
  ~ModelFlags_ModelCheck() override;
  explicit PROTOBUF_CONSTEXPR ModelFlags_ModelCheck(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelFlags_ModelCheck(const ModelFlags_ModelCheck& from);
  ModelFlags_ModelCheck(ModelFlags_ModelCheck&& from) noexcept
    : ModelFlags_ModelCheck() {
    *this = ::std::move(from);
  }

  inline ModelFlags_ModelCheck& operator=(const ModelFlags_ModelCheck& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelFlags_ModelCheck& operator=(ModelFlags_ModelCheck&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelFlags_ModelCheck& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelFlags_ModelCheck* internal_default_instance() {
    return reinterpret_cast<const ModelFlags_ModelCheck*>(
               &_ModelFlags_ModelCheck_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ModelFlags_ModelCheck& a, ModelFlags_ModelCheck& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelFlags_ModelCheck* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelFlags_ModelCheck* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelFlags_ModelCheck* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelFlags_ModelCheck>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelFlags_ModelCheck& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ModelFlags_ModelCheck& from) {
    ModelFlags_ModelCheck::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelFlags_ModelCheck* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tflite.ModelFlags.ModelCheck";
  }
  protected:
  explicit ModelFlags_ModelCheck(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCountTypeFieldNumber = 1,
    kCountMinFieldNumber = 2,
    kCountMaxFieldNumber = 3,
  };
  // optional string count_type = 1 [default = "None"];
  bool has_count_type() const;
  private:
  bool _internal_has_count_type() const;
  public:
  void clear_count_type();
  const std::string& count_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_count_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_count_type();
  PROTOBUF_NODISCARD std::string* release_count_type();
  void set_allocated_count_type(std::string* count_type);
  private:
  const std::string& _internal_count_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_count_type(const std::string& value);
  std::string* _internal_mutable_count_type();
  public:

  // optional int32 count_min = 2 [default = -1];
  bool has_count_min() const;
  private:
  bool _internal_has_count_min() const;
  public:
  void clear_count_min();
  int32_t count_min() const;
  void set_count_min(int32_t value);
  private:
  int32_t _internal_count_min() const;
  void _internal_set_count_min(int32_t value);
  public:

  // optional int32 count_max = 3 [default = -1];
  bool has_count_max() const;
  private:
  bool _internal_has_count_max() const;
  public:
  void clear_count_max();
  int32_t count_max() const;
  void set_count_max(int32_t value);
  private:
  int32_t _internal_count_max() const;
  void _internal_set_count_max(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tflite.ModelFlags.ModelCheck)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    static const ::PROTOBUF_NAMESPACE_ID::internal::LazyString _i_give_permission_to_break_this_code_default_count_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr count_type_;
    int32_t count_min_;
    int32_t count_max_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto;
};
// -------------------------------------------------------------------

class ModelFlags final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tflite.ModelFlags) */ {
 public:
  inline ModelFlags() : ModelFlags(nullptr) {}
  ~ModelFlags() override;
  explicit PROTOBUF_CONSTEXPR ModelFlags(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelFlags(const ModelFlags& from);
  ModelFlags(ModelFlags&& from) noexcept
    : ModelFlags() {
    *this = ::std::move(from);
  }

  inline ModelFlags& operator=(const ModelFlags& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelFlags& operator=(ModelFlags&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet& unknown_fields() const {
    return _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance);
  }
  inline ::PROTOBUF_NAMESPACE_ID::UnknownFieldSet* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelFlags& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelFlags* internal_default_instance() {
    return reinterpret_cast<const ModelFlags*>(
               &_ModelFlags_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(ModelFlags& a, ModelFlags& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelFlags* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelFlags* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelFlags* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelFlags>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelFlags& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ModelFlags& from) {
    ModelFlags::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelFlags* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tflite.ModelFlags";
  }
  protected:
  explicit ModelFlags(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ModelFlags_ModelCheck ModelCheck;

  typedef ModelFlags_HloFileType HloFileType;
  static constexpr HloFileType UNKNOWN =
    ModelFlags_HloFileType_UNKNOWN;
  static constexpr HloFileType HLO_TEXT =
    ModelFlags_HloFileType_HLO_TEXT;
  static constexpr HloFileType HLO_PROTO =
    ModelFlags_HloFileType_HLO_PROTO;
  static inline bool HloFileType_IsValid(int value) {
    return ModelFlags_HloFileType_IsValid(value);
  }
  static constexpr HloFileType HloFileType_MIN =
    ModelFlags_HloFileType_HloFileType_MIN;
  static constexpr HloFileType HloFileType_MAX =
    ModelFlags_HloFileType_HloFileType_MAX;
  static constexpr int HloFileType_ARRAYSIZE =
    ModelFlags_HloFileType_HloFileType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  HloFileType_descriptor() {
    return ModelFlags_HloFileType_descriptor();
  }
  template<typename T>
  static inline const std::string& HloFileType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, HloFileType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function HloFileType_Name.");
    return ModelFlags_HloFileType_Name(enum_t_value);
  }
  static inline bool HloFileType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      HloFileType* value) {
    return ModelFlags_HloFileType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kInputArraysFieldNumber = 1,
    kOutputArraysFieldNumber = 2,
    kRnnStatesFieldNumber = 12,
    kModelChecksFieldNumber = 14,
    kSavedModelTagsFieldNumber = 22,
    kSavedModelExportedNamesFieldNumber = 23,
    kControlOutputArraysFieldNumber = 24,
    kSavedModelDirFieldNumber = 20,
    kArraysExtraInfoFieldNumber = 18,
    kVariableBatchFieldNumber = 10,
    kAllowNonexistentArraysFieldNumber = 16,
    kAllowNonasciiArraysFieldNumber = 17,
    kUseHloImportFieldNumber = 25,
    kSavedModelVersionFieldNumber = 21,
    kHloFileTypeFieldNumber = 26,
    kChangeConcatInputRangesFieldNumber = 19,
  };
  // repeated .tflite.InputArray input_arrays = 1;
  int input_arrays_size() const;
  private:
  int _internal_input_arrays_size() const;
  public:
  void clear_input_arrays();
  ::tflite::InputArray* mutable_input_arrays(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::InputArray >*
      mutable_input_arrays();
  private:
  const ::tflite::InputArray& _internal_input_arrays(int index) const;
  ::tflite::InputArray* _internal_add_input_arrays();
  public:
  const ::tflite::InputArray& input_arrays(int index) const;
  ::tflite::InputArray* add_input_arrays();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::InputArray >&
      input_arrays() const;

  // repeated string output_arrays = 2;
  int output_arrays_size() const;
  private:
  int _internal_output_arrays_size() const;
  public:
  void clear_output_arrays();
  const std::string& output_arrays(int index) const;
  std::string* mutable_output_arrays(int index);
  void set_output_arrays(int index, const std::string& value);
  void set_output_arrays(int index, std::string&& value);
  void set_output_arrays(int index, const char* value);
  void set_output_arrays(int index, const char* value, size_t size);
  std::string* add_output_arrays();
  void add_output_arrays(const std::string& value);
  void add_output_arrays(std::string&& value);
  void add_output_arrays(const char* value);
  void add_output_arrays(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& output_arrays() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_output_arrays();
  private:
  const std::string& _internal_output_arrays(int index) const;
  std::string* _internal_add_output_arrays();
  public:

  // repeated .tflite.RnnState rnn_states = 12;
  int rnn_states_size() const;
  private:
  int _internal_rnn_states_size() const;
  public:
  void clear_rnn_states();
  ::tflite::RnnState* mutable_rnn_states(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::RnnState >*
      mutable_rnn_states();
  private:
  const ::tflite::RnnState& _internal_rnn_states(int index) const;
  ::tflite::RnnState* _internal_add_rnn_states();
  public:
  const ::tflite::RnnState& rnn_states(int index) const;
  ::tflite::RnnState* add_rnn_states();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::RnnState >&
      rnn_states() const;

  // repeated .tflite.ModelFlags.ModelCheck model_checks = 14;
  int model_checks_size() const;
  private:
  int _internal_model_checks_size() const;
  public:
  void clear_model_checks();
  ::tflite::ModelFlags_ModelCheck* mutable_model_checks(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ModelFlags_ModelCheck >*
      mutable_model_checks();
  private:
  const ::tflite::ModelFlags_ModelCheck& _internal_model_checks(int index) const;
  ::tflite::ModelFlags_ModelCheck* _internal_add_model_checks();
  public:
  const ::tflite::ModelFlags_ModelCheck& model_checks(int index) const;
  ::tflite::ModelFlags_ModelCheck* add_model_checks();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ModelFlags_ModelCheck >&
      model_checks() const;

  // repeated string saved_model_tags = 22;
  int saved_model_tags_size() const;
  private:
  int _internal_saved_model_tags_size() const;
  public:
  void clear_saved_model_tags();
  const std::string& saved_model_tags(int index) const;
  std::string* mutable_saved_model_tags(int index);
  void set_saved_model_tags(int index, const std::string& value);
  void set_saved_model_tags(int index, std::string&& value);
  void set_saved_model_tags(int index, const char* value);
  void set_saved_model_tags(int index, const char* value, size_t size);
  std::string* add_saved_model_tags();
  void add_saved_model_tags(const std::string& value);
  void add_saved_model_tags(std::string&& value);
  void add_saved_model_tags(const char* value);
  void add_saved_model_tags(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& saved_model_tags() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_saved_model_tags();
  private:
  const std::string& _internal_saved_model_tags(int index) const;
  std::string* _internal_add_saved_model_tags();
  public:

  // repeated string saved_model_exported_names = 23;
  int saved_model_exported_names_size() const;
  private:
  int _internal_saved_model_exported_names_size() const;
  public:
  void clear_saved_model_exported_names();
  const std::string& saved_model_exported_names(int index) const;
  std::string* mutable_saved_model_exported_names(int index);
  void set_saved_model_exported_names(int index, const std::string& value);
  void set_saved_model_exported_names(int index, std::string&& value);
  void set_saved_model_exported_names(int index, const char* value);
  void set_saved_model_exported_names(int index, const char* value, size_t size);
  std::string* add_saved_model_exported_names();
  void add_saved_model_exported_names(const std::string& value);
  void add_saved_model_exported_names(std::string&& value);
  void add_saved_model_exported_names(const char* value);
  void add_saved_model_exported_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& saved_model_exported_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_saved_model_exported_names();
  private:
  const std::string& _internal_saved_model_exported_names(int index) const;
  std::string* _internal_add_saved_model_exported_names();
  public:

  // repeated string control_output_arrays = 24;
  int control_output_arrays_size() const;
  private:
  int _internal_control_output_arrays_size() const;
  public:
  void clear_control_output_arrays();
  const std::string& control_output_arrays(int index) const;
  std::string* mutable_control_output_arrays(int index);
  void set_control_output_arrays(int index, const std::string& value);
  void set_control_output_arrays(int index, std::string&& value);
  void set_control_output_arrays(int index, const char* value);
  void set_control_output_arrays(int index, const char* value, size_t size);
  std::string* add_control_output_arrays();
  void add_control_output_arrays(const std::string& value);
  void add_control_output_arrays(std::string&& value);
  void add_control_output_arrays(const char* value);
  void add_control_output_arrays(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& control_output_arrays() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_control_output_arrays();
  private:
  const std::string& _internal_control_output_arrays(int index) const;
  std::string* _internal_add_control_output_arrays();
  public:

  // optional string saved_model_dir = 20;
  bool has_saved_model_dir() const;
  private:
  bool _internal_has_saved_model_dir() const;
  public:
  void clear_saved_model_dir();
  const std::string& saved_model_dir() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_saved_model_dir(ArgT0&& arg0, ArgT... args);
  std::string* mutable_saved_model_dir();
  PROTOBUF_NODISCARD std::string* release_saved_model_dir();
  void set_allocated_saved_model_dir(std::string* saved_model_dir);
  private:
  const std::string& _internal_saved_model_dir() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_saved_model_dir(const std::string& value);
  std::string* _internal_mutable_saved_model_dir();
  public:

  // optional .tflite.ArraysExtraInfo arrays_extra_info = 18;
  bool has_arrays_extra_info() const;
  private:
  bool _internal_has_arrays_extra_info() const;
  public:
  void clear_arrays_extra_info();
  const ::tflite::ArraysExtraInfo& arrays_extra_info() const;
  PROTOBUF_NODISCARD ::tflite::ArraysExtraInfo* release_arrays_extra_info();
  ::tflite::ArraysExtraInfo* mutable_arrays_extra_info();
  void set_allocated_arrays_extra_info(::tflite::ArraysExtraInfo* arrays_extra_info);
  private:
  const ::tflite::ArraysExtraInfo& _internal_arrays_extra_info() const;
  ::tflite::ArraysExtraInfo* _internal_mutable_arrays_extra_info();
  public:
  void unsafe_arena_set_allocated_arrays_extra_info(
      ::tflite::ArraysExtraInfo* arrays_extra_info);
  ::tflite::ArraysExtraInfo* unsafe_arena_release_arrays_extra_info();

  // optional bool variable_batch = 10;
  bool has_variable_batch() const;
  private:
  bool _internal_has_variable_batch() const;
  public:
  void clear_variable_batch();
  bool variable_batch() const;
  void set_variable_batch(bool value);
  private:
  bool _internal_variable_batch() const;
  void _internal_set_variable_batch(bool value);
  public:

  // optional bool allow_nonexistent_arrays = 16;
  bool has_allow_nonexistent_arrays() const;
  private:
  bool _internal_has_allow_nonexistent_arrays() const;
  public:
  void clear_allow_nonexistent_arrays();
  bool allow_nonexistent_arrays() const;
  void set_allow_nonexistent_arrays(bool value);
  private:
  bool _internal_allow_nonexistent_arrays() const;
  void _internal_set_allow_nonexistent_arrays(bool value);
  public:

  // optional bool allow_nonascii_arrays = 17;
  bool has_allow_nonascii_arrays() const;
  private:
  bool _internal_has_allow_nonascii_arrays() const;
  public:
  void clear_allow_nonascii_arrays();
  bool allow_nonascii_arrays() const;
  void set_allow_nonascii_arrays(bool value);
  private:
  bool _internal_allow_nonascii_arrays() const;
  void _internal_set_allow_nonascii_arrays(bool value);
  public:

  // optional bool use_hlo_import = 25;
  bool has_use_hlo_import() const;
  private:
  bool _internal_has_use_hlo_import() const;
  public:
  void clear_use_hlo_import();
  bool use_hlo_import() const;
  void set_use_hlo_import(bool value);
  private:
  bool _internal_use_hlo_import() const;
  void _internal_set_use_hlo_import(bool value);
  public:

  // optional int32 saved_model_version = 21;
  bool has_saved_model_version() const;
  private:
  bool _internal_has_saved_model_version() const;
  public:
  void clear_saved_model_version();
  int32_t saved_model_version() const;
  void set_saved_model_version(int32_t value);
  private:
  int32_t _internal_saved_model_version() const;
  void _internal_set_saved_model_version(int32_t value);
  public:

  // optional .tflite.ModelFlags.HloFileType hlo_file_type = 26;
  bool has_hlo_file_type() const;
  private:
  bool _internal_has_hlo_file_type() const;
  public:
  void clear_hlo_file_type();
  ::tflite::ModelFlags_HloFileType hlo_file_type() const;
  void set_hlo_file_type(::tflite::ModelFlags_HloFileType value);
  private:
  ::tflite::ModelFlags_HloFileType _internal_hlo_file_type() const;
  void _internal_set_hlo_file_type(::tflite::ModelFlags_HloFileType value);
  public:

  // optional bool change_concat_input_ranges = 19 [default = true];
  bool has_change_concat_input_ranges() const;
  private:
  bool _internal_has_change_concat_input_ranges() const;
  public:
  void clear_change_concat_input_ranges();
  bool change_concat_input_ranges() const;
  void set_change_concat_input_ranges(bool value);
  private:
  bool _internal_change_concat_input_ranges() const;
  void _internal_set_change_concat_input_ranges(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tflite.ModelFlags)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::InputArray > input_arrays_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> output_arrays_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::RnnState > rnn_states_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ModelFlags_ModelCheck > model_checks_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> saved_model_tags_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> saved_model_exported_names_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> control_output_arrays_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr saved_model_dir_;
    ::tflite::ArraysExtraInfo* arrays_extra_info_;
    bool variable_batch_;
    bool allow_nonexistent_arrays_;
    bool allow_nonascii_arrays_;
    bool use_hlo_import_;
    int32_t saved_model_version_;
    int hlo_file_type_;
    bool change_concat_input_ranges_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// InputArrayShape

// repeated int32 dims = 2;
inline int InputArrayShape::_internal_dims_size() const {
  return _impl_.dims_.size();
}
inline int InputArrayShape::dims_size() const {
  return _internal_dims_size();
}
inline void InputArrayShape::clear_dims() {
  _impl_.dims_.Clear();
}
inline int32_t InputArrayShape::_internal_dims(int index) const {
  return _impl_.dims_.Get(index);
}
inline int32_t InputArrayShape::dims(int index) const {
  // @@protoc_insertion_point(field_get:tflite.InputArrayShape.dims)
  return _internal_dims(index);
}
inline void InputArrayShape::set_dims(int index, int32_t value) {
  _impl_.dims_.Set(index, value);
  // @@protoc_insertion_point(field_set:tflite.InputArrayShape.dims)
}
inline void InputArrayShape::_internal_add_dims(int32_t value) {
  _impl_.dims_.Add(value);
}
inline void InputArrayShape::add_dims(int32_t value) {
  _internal_add_dims(value);
  // @@protoc_insertion_point(field_add:tflite.InputArrayShape.dims)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
InputArrayShape::_internal_dims() const {
  return _impl_.dims_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
InputArrayShape::dims() const {
  // @@protoc_insertion_point(field_list:tflite.InputArrayShape.dims)
  return _internal_dims();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
InputArrayShape::_internal_mutable_dims() {
  return &_impl_.dims_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
InputArrayShape::mutable_dims() {
  // @@protoc_insertion_point(field_mutable_list:tflite.InputArrayShape.dims)
  return _internal_mutable_dims();
}

// optional bool unknown_rank = 3;
inline bool InputArrayShape::_internal_has_unknown_rank() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool InputArrayShape::has_unknown_rank() const {
  return _internal_has_unknown_rank();
}
inline void InputArrayShape::clear_unknown_rank() {
  _impl_.unknown_rank_ = false;
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline bool InputArrayShape::_internal_unknown_rank() const {
  return _impl_.unknown_rank_;
}
inline bool InputArrayShape::unknown_rank() const {
  // @@protoc_insertion_point(field_get:tflite.InputArrayShape.unknown_rank)
  return _internal_unknown_rank();
}
inline void InputArrayShape::_internal_set_unknown_rank(bool value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.unknown_rank_ = value;
}
inline void InputArrayShape::set_unknown_rank(bool value) {
  _internal_set_unknown_rank(value);
  // @@protoc_insertion_point(field_set:tflite.InputArrayShape.unknown_rank)
}

// -------------------------------------------------------------------

// InputArray

// optional string name = 1;
inline bool InputArray::_internal_has_name() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool InputArray::has_name() const {
  return _internal_has_name();
}
inline void InputArray::clear_name() {
  _impl_.name_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& InputArray::name() const {
  // @@protoc_insertion_point(field_get:tflite.InputArray.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void InputArray::set_name(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tflite.InputArray.name)
}
inline std::string* InputArray::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tflite.InputArray.name)
  return _s;
}
inline const std::string& InputArray::_internal_name() const {
  return _impl_.name_.Get();
}
inline void InputArray::_internal_set_name(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* InputArray::_internal_mutable_name() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* InputArray::release_name() {
  // @@protoc_insertion_point(field_release:tflite.InputArray.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.name_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void InputArray::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tflite.InputArray.name)
}

// optional .tflite.InputArrayShape shape = 6;
inline bool InputArray::_internal_has_shape() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.shape_ != nullptr);
  return value;
}
inline bool InputArray::has_shape() const {
  return _internal_has_shape();
}
inline void InputArray::clear_shape() {
  if (_impl_.shape_ != nullptr) _impl_.shape_->Clear();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const ::tflite::InputArrayShape& InputArray::_internal_shape() const {
  const ::tflite::InputArrayShape* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tflite::InputArrayShape&>(
      ::tflite::_InputArrayShape_default_instance_);
}
inline const ::tflite::InputArrayShape& InputArray::shape() const {
  // @@protoc_insertion_point(field_get:tflite.InputArray.shape)
  return _internal_shape();
}
inline void InputArray::unsafe_arena_set_allocated_shape(
    ::tflite::InputArrayShape* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tflite.InputArray.shape)
}
inline ::tflite::InputArrayShape* InputArray::release_shape() {
  _impl_._has_bits_[0] &= ~0x00000002u;
  ::tflite::InputArrayShape* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tflite::InputArrayShape* InputArray::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tflite.InputArray.shape)
  _impl_._has_bits_[0] &= ~0x00000002u;
  ::tflite::InputArrayShape* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tflite::InputArrayShape* InputArray::_internal_mutable_shape() {
  _impl_._has_bits_[0] |= 0x00000002u;
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tflite::InputArrayShape>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tflite::InputArrayShape* InputArray::mutable_shape() {
  ::tflite::InputArrayShape* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tflite.InputArray.shape)
  return _msg;
}
inline void InputArray::set_allocated_shape(::tflite::InputArrayShape* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.shape_;
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(shape);
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tflite.InputArray.shape)
}

// optional float mean_value = 3;
inline bool InputArray::_internal_has_mean_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool InputArray::has_mean_value() const {
  return _internal_has_mean_value();
}
inline void InputArray::clear_mean_value() {
  _impl_.mean_value_ = 0;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline float InputArray::_internal_mean_value() const {
  return _impl_.mean_value_;
}
inline float InputArray::mean_value() const {
  // @@protoc_insertion_point(field_get:tflite.InputArray.mean_value)
  return _internal_mean_value();
}
inline void InputArray::_internal_set_mean_value(float value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.mean_value_ = value;
}
inline void InputArray::set_mean_value(float value) {
  _internal_set_mean_value(value);
  // @@protoc_insertion_point(field_set:tflite.InputArray.mean_value)
}

// optional float std_value = 4 [default = 1];
inline bool InputArray::_internal_has_std_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool InputArray::has_std_value() const {
  return _internal_has_std_value();
}
inline void InputArray::clear_std_value() {
  _impl_.std_value_ = 1;
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline float InputArray::_internal_std_value() const {
  return _impl_.std_value_;
}
inline float InputArray::std_value() const {
  // @@protoc_insertion_point(field_get:tflite.InputArray.std_value)
  return _internal_std_value();
}
inline void InputArray::_internal_set_std_value(float value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.std_value_ = value;
}
inline void InputArray::set_std_value(float value) {
  _internal_set_std_value(value);
  // @@protoc_insertion_point(field_set:tflite.InputArray.std_value)
}

// optional .tflite.IODataType data_type = 5;
inline bool InputArray::_internal_has_data_type() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool InputArray::has_data_type() const {
  return _internal_has_data_type();
}
inline void InputArray::clear_data_type() {
  _impl_.data_type_ = 0;
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline ::tflite::IODataType InputArray::_internal_data_type() const {
  return static_cast< ::tflite::IODataType >(_impl_.data_type_);
}
inline ::tflite::IODataType InputArray::data_type() const {
  // @@protoc_insertion_point(field_get:tflite.InputArray.data_type)
  return _internal_data_type();
}
inline void InputArray::_internal_set_data_type(::tflite::IODataType value) {
  assert(::tflite::IODataType_IsValid(value));
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.data_type_ = value;
}
inline void InputArray::set_data_type(::tflite::IODataType value) {
  _internal_set_data_type(value);
  // @@protoc_insertion_point(field_set:tflite.InputArray.data_type)
}

// -------------------------------------------------------------------

// RnnState

// optional string state_array = 1;
inline bool RnnState::_internal_has_state_array() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RnnState::has_state_array() const {
  return _internal_has_state_array();
}
inline void RnnState::clear_state_array() {
  _impl_.state_array_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RnnState::state_array() const {
  // @@protoc_insertion_point(field_get:tflite.RnnState.state_array)
  return _internal_state_array();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RnnState::set_state_array(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.state_array_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tflite.RnnState.state_array)
}
inline std::string* RnnState::mutable_state_array() {
  std::string* _s = _internal_mutable_state_array();
  // @@protoc_insertion_point(field_mutable:tflite.RnnState.state_array)
  return _s;
}
inline const std::string& RnnState::_internal_state_array() const {
  return _impl_.state_array_.Get();
}
inline void RnnState::_internal_set_state_array(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.state_array_.Set(value, GetArenaForAllocation());
}
inline std::string* RnnState::_internal_mutable_state_array() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.state_array_.Mutable(GetArenaForAllocation());
}
inline std::string* RnnState::release_state_array() {
  // @@protoc_insertion_point(field_release:tflite.RnnState.state_array)
  if (!_internal_has_state_array()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.state_array_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.state_array_.IsDefault()) {
    _impl_.state_array_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void RnnState::set_allocated_state_array(std::string* state_array) {
  if (state_array != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.state_array_.SetAllocated(state_array, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.state_array_.IsDefault()) {
    _impl_.state_array_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tflite.RnnState.state_array)
}

// optional string back_edge_source_array = 2;
inline bool RnnState::_internal_has_back_edge_source_array() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RnnState::has_back_edge_source_array() const {
  return _internal_has_back_edge_source_array();
}
inline void RnnState::clear_back_edge_source_array() {
  _impl_.back_edge_source_array_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& RnnState::back_edge_source_array() const {
  // @@protoc_insertion_point(field_get:tflite.RnnState.back_edge_source_array)
  return _internal_back_edge_source_array();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RnnState::set_back_edge_source_array(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.back_edge_source_array_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tflite.RnnState.back_edge_source_array)
}
inline std::string* RnnState::mutable_back_edge_source_array() {
  std::string* _s = _internal_mutable_back_edge_source_array();
  // @@protoc_insertion_point(field_mutable:tflite.RnnState.back_edge_source_array)
  return _s;
}
inline const std::string& RnnState::_internal_back_edge_source_array() const {
  return _impl_.back_edge_source_array_.Get();
}
inline void RnnState::_internal_set_back_edge_source_array(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.back_edge_source_array_.Set(value, GetArenaForAllocation());
}
inline std::string* RnnState::_internal_mutable_back_edge_source_array() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.back_edge_source_array_.Mutable(GetArenaForAllocation());
}
inline std::string* RnnState::release_back_edge_source_array() {
  // @@protoc_insertion_point(field_release:tflite.RnnState.back_edge_source_array)
  if (!_internal_has_back_edge_source_array()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.back_edge_source_array_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.back_edge_source_array_.IsDefault()) {
    _impl_.back_edge_source_array_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void RnnState::set_allocated_back_edge_source_array(std::string* back_edge_source_array) {
  if (back_edge_source_array != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.back_edge_source_array_.SetAllocated(back_edge_source_array, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.back_edge_source_array_.IsDefault()) {
    _impl_.back_edge_source_array_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tflite.RnnState.back_edge_source_array)
}

// optional bool discardable = 5;
inline bool RnnState::_internal_has_discardable() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool RnnState::has_discardable() const {
  return _internal_has_discardable();
}
inline void RnnState::clear_discardable() {
  _impl_.discardable_ = false;
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline bool RnnState::_internal_discardable() const {
  return _impl_.discardable_;
}
inline bool RnnState::discardable() const {
  // @@protoc_insertion_point(field_get:tflite.RnnState.discardable)
  return _internal_discardable();
}
inline void RnnState::_internal_set_discardable(bool value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.discardable_ = value;
}
inline void RnnState::set_discardable(bool value) {
  _internal_set_discardable(value);
  // @@protoc_insertion_point(field_set:tflite.RnnState.discardable)
}

// optional int32 size = 3;
inline bool RnnState::_internal_has_size() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RnnState::has_size() const {
  return _internal_has_size();
}
inline void RnnState::clear_size() {
  _impl_.size_ = 0;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline int32_t RnnState::_internal_size() const {
  return _impl_.size_;
}
inline int32_t RnnState::size() const {
  // @@protoc_insertion_point(field_get:tflite.RnnState.size)
  return _internal_size();
}
inline void RnnState::_internal_set_size(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.size_ = value;
}
inline void RnnState::set_size(int32_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:tflite.RnnState.size)
}

// optional int32 num_dims = 4;
inline bool RnnState::_internal_has_num_dims() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RnnState::has_num_dims() const {
  return _internal_has_num_dims();
}
inline void RnnState::clear_num_dims() {
  _impl_.num_dims_ = 0;
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline int32_t RnnState::_internal_num_dims() const {
  return _impl_.num_dims_;
}
inline int32_t RnnState::num_dims() const {
  // @@protoc_insertion_point(field_get:tflite.RnnState.num_dims)
  return _internal_num_dims();
}
inline void RnnState::_internal_set_num_dims(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.num_dims_ = value;
}
inline void RnnState::set_num_dims(int32_t value) {
  _internal_set_num_dims(value);
  // @@protoc_insertion_point(field_set:tflite.RnnState.num_dims)
}

// -------------------------------------------------------------------

// ArraysExtraInfo_Entry

// optional string name = 1;
inline bool ArraysExtraInfo_Entry::_internal_has_name() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ArraysExtraInfo_Entry::has_name() const {
  return _internal_has_name();
}
inline void ArraysExtraInfo_Entry::clear_name() {
  _impl_.name_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ArraysExtraInfo_Entry::name() const {
  // @@protoc_insertion_point(field_get:tflite.ArraysExtraInfo.Entry.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ArraysExtraInfo_Entry::set_name(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tflite.ArraysExtraInfo.Entry.name)
}
inline std::string* ArraysExtraInfo_Entry::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tflite.ArraysExtraInfo.Entry.name)
  return _s;
}
inline const std::string& ArraysExtraInfo_Entry::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ArraysExtraInfo_Entry::_internal_set_name(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ArraysExtraInfo_Entry::_internal_mutable_name() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ArraysExtraInfo_Entry::release_name() {
  // @@protoc_insertion_point(field_release:tflite.ArraysExtraInfo.Entry.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.name_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ArraysExtraInfo_Entry::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tflite.ArraysExtraInfo.Entry.name)
}

// optional string name_regexp = 7;
inline bool ArraysExtraInfo_Entry::_internal_has_name_regexp() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool ArraysExtraInfo_Entry::has_name_regexp() const {
  return _internal_has_name_regexp();
}
inline void ArraysExtraInfo_Entry::clear_name_regexp() {
  _impl_.name_regexp_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const std::string& ArraysExtraInfo_Entry::name_regexp() const {
  // @@protoc_insertion_point(field_get:tflite.ArraysExtraInfo.Entry.name_regexp)
  return _internal_name_regexp();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ArraysExtraInfo_Entry::set_name_regexp(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000002u;
 _impl_.name_regexp_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tflite.ArraysExtraInfo.Entry.name_regexp)
}
inline std::string* ArraysExtraInfo_Entry::mutable_name_regexp() {
  std::string* _s = _internal_mutable_name_regexp();
  // @@protoc_insertion_point(field_mutable:tflite.ArraysExtraInfo.Entry.name_regexp)
  return _s;
}
inline const std::string& ArraysExtraInfo_Entry::_internal_name_regexp() const {
  return _impl_.name_regexp_.Get();
}
inline void ArraysExtraInfo_Entry::_internal_set_name_regexp(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.name_regexp_.Set(value, GetArenaForAllocation());
}
inline std::string* ArraysExtraInfo_Entry::_internal_mutable_name_regexp() {
  _impl_._has_bits_[0] |= 0x00000002u;
  return _impl_.name_regexp_.Mutable(GetArenaForAllocation());
}
inline std::string* ArraysExtraInfo_Entry::release_name_regexp() {
  // @@protoc_insertion_point(field_release:tflite.ArraysExtraInfo.Entry.name_regexp)
  if (!_internal_has_name_regexp()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000002u;
  auto* p = _impl_.name_regexp_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_regexp_.IsDefault()) {
    _impl_.name_regexp_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ArraysExtraInfo_Entry::set_allocated_name_regexp(std::string* name_regexp) {
  if (name_regexp != nullptr) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.name_regexp_.SetAllocated(name_regexp, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_regexp_.IsDefault()) {
    _impl_.name_regexp_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tflite.ArraysExtraInfo.Entry.name_regexp)
}

// optional double min = 2;
inline bool ArraysExtraInfo_Entry::_internal_has_min() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool ArraysExtraInfo_Entry::has_min() const {
  return _internal_has_min();
}
inline void ArraysExtraInfo_Entry::clear_min() {
  _impl_.min_ = 0;
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline double ArraysExtraInfo_Entry::_internal_min() const {
  return _impl_.min_;
}
inline double ArraysExtraInfo_Entry::min() const {
  // @@protoc_insertion_point(field_get:tflite.ArraysExtraInfo.Entry.min)
  return _internal_min();
}
inline void ArraysExtraInfo_Entry::_internal_set_min(double value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.min_ = value;
}
inline void ArraysExtraInfo_Entry::set_min(double value) {
  _internal_set_min(value);
  // @@protoc_insertion_point(field_set:tflite.ArraysExtraInfo.Entry.min)
}

// optional double max = 3;
inline bool ArraysExtraInfo_Entry::_internal_has_max() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool ArraysExtraInfo_Entry::has_max() const {
  return _internal_has_max();
}
inline void ArraysExtraInfo_Entry::clear_max() {
  _impl_.max_ = 0;
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline double ArraysExtraInfo_Entry::_internal_max() const {
  return _impl_.max_;
}
inline double ArraysExtraInfo_Entry::max() const {
  // @@protoc_insertion_point(field_get:tflite.ArraysExtraInfo.Entry.max)
  return _internal_max();
}
inline void ArraysExtraInfo_Entry::_internal_set_max(double value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.max_ = value;
}
inline void ArraysExtraInfo_Entry::set_max(double value) {
  _internal_set_max(value);
  // @@protoc_insertion_point(field_set:tflite.ArraysExtraInfo.Entry.max)
}

// optional .tflite.IODataType data_type = 4;
inline bool ArraysExtraInfo_Entry::_internal_has_data_type() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool ArraysExtraInfo_Entry::has_data_type() const {
  return _internal_has_data_type();
}
inline void ArraysExtraInfo_Entry::clear_data_type() {
  _impl_.data_type_ = 0;
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline ::tflite::IODataType ArraysExtraInfo_Entry::_internal_data_type() const {
  return static_cast< ::tflite::IODataType >(_impl_.data_type_);
}
inline ::tflite::IODataType ArraysExtraInfo_Entry::data_type() const {
  // @@protoc_insertion_point(field_get:tflite.ArraysExtraInfo.Entry.data_type)
  return _internal_data_type();
}
inline void ArraysExtraInfo_Entry::_internal_set_data_type(::tflite::IODataType value) {
  assert(::tflite::IODataType_IsValid(value));
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.data_type_ = value;
}
inline void ArraysExtraInfo_Entry::set_data_type(::tflite::IODataType value) {
  _internal_set_data_type(value);
  // @@protoc_insertion_point(field_set:tflite.ArraysExtraInfo.Entry.data_type)
}

// optional .tflite.InputArrayShape shape = 5;
inline bool ArraysExtraInfo_Entry::_internal_has_shape() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.shape_ != nullptr);
  return value;
}
inline bool ArraysExtraInfo_Entry::has_shape() const {
  return _internal_has_shape();
}
inline void ArraysExtraInfo_Entry::clear_shape() {
  if (_impl_.shape_ != nullptr) _impl_.shape_->Clear();
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline const ::tflite::InputArrayShape& ArraysExtraInfo_Entry::_internal_shape() const {
  const ::tflite::InputArrayShape* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tflite::InputArrayShape&>(
      ::tflite::_InputArrayShape_default_instance_);
}
inline const ::tflite::InputArrayShape& ArraysExtraInfo_Entry::shape() const {
  // @@protoc_insertion_point(field_get:tflite.ArraysExtraInfo.Entry.shape)
  return _internal_shape();
}
inline void ArraysExtraInfo_Entry::unsafe_arena_set_allocated_shape(
    ::tflite::InputArrayShape* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tflite.ArraysExtraInfo.Entry.shape)
}
inline ::tflite::InputArrayShape* ArraysExtraInfo_Entry::release_shape() {
  _impl_._has_bits_[0] &= ~0x00000004u;
  ::tflite::InputArrayShape* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tflite::InputArrayShape* ArraysExtraInfo_Entry::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tflite.ArraysExtraInfo.Entry.shape)
  _impl_._has_bits_[0] &= ~0x00000004u;
  ::tflite::InputArrayShape* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tflite::InputArrayShape* ArraysExtraInfo_Entry::_internal_mutable_shape() {
  _impl_._has_bits_[0] |= 0x00000004u;
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tflite::InputArrayShape>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tflite::InputArrayShape* ArraysExtraInfo_Entry::mutable_shape() {
  ::tflite::InputArrayShape* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tflite.ArraysExtraInfo.Entry.shape)
  return _msg;
}
inline void ArraysExtraInfo_Entry::set_allocated_shape(::tflite::InputArrayShape* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.shape_;
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(shape);
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000004u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000004u;
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tflite.ArraysExtraInfo.Entry.shape)
}

// optional float constant_float_value = 6;
inline bool ArraysExtraInfo_Entry::_internal_has_constant_float_value() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool ArraysExtraInfo_Entry::has_constant_float_value() const {
  return _internal_has_constant_float_value();
}
inline void ArraysExtraInfo_Entry::clear_constant_float_value() {
  _impl_.constant_float_value_ = 0;
  _impl_._has_bits_[0] &= ~0x00000040u;
}
inline float ArraysExtraInfo_Entry::_internal_constant_float_value() const {
  return _impl_.constant_float_value_;
}
inline float ArraysExtraInfo_Entry::constant_float_value() const {
  // @@protoc_insertion_point(field_get:tflite.ArraysExtraInfo.Entry.constant_float_value)
  return _internal_constant_float_value();
}
inline void ArraysExtraInfo_Entry::_internal_set_constant_float_value(float value) {
  _impl_._has_bits_[0] |= 0x00000040u;
  _impl_.constant_float_value_ = value;
}
inline void ArraysExtraInfo_Entry::set_constant_float_value(float value) {
  _internal_set_constant_float_value(value);
  // @@protoc_insertion_point(field_set:tflite.ArraysExtraInfo.Entry.constant_float_value)
}

// -------------------------------------------------------------------

// ArraysExtraInfo

// repeated .tflite.ArraysExtraInfo.Entry entries = 1;
inline int ArraysExtraInfo::_internal_entries_size() const {
  return _impl_.entries_.size();
}
inline int ArraysExtraInfo::entries_size() const {
  return _internal_entries_size();
}
inline void ArraysExtraInfo::clear_entries() {
  _impl_.entries_.Clear();
}
inline ::tflite::ArraysExtraInfo_Entry* ArraysExtraInfo::mutable_entries(int index) {
  // @@protoc_insertion_point(field_mutable:tflite.ArraysExtraInfo.entries)
  return _impl_.entries_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ArraysExtraInfo_Entry >*
ArraysExtraInfo::mutable_entries() {
  // @@protoc_insertion_point(field_mutable_list:tflite.ArraysExtraInfo.entries)
  return &_impl_.entries_;
}
inline const ::tflite::ArraysExtraInfo_Entry& ArraysExtraInfo::_internal_entries(int index) const {
  return _impl_.entries_.Get(index);
}
inline const ::tflite::ArraysExtraInfo_Entry& ArraysExtraInfo::entries(int index) const {
  // @@protoc_insertion_point(field_get:tflite.ArraysExtraInfo.entries)
  return _internal_entries(index);
}
inline ::tflite::ArraysExtraInfo_Entry* ArraysExtraInfo::_internal_add_entries() {
  return _impl_.entries_.Add();
}
inline ::tflite::ArraysExtraInfo_Entry* ArraysExtraInfo::add_entries() {
  ::tflite::ArraysExtraInfo_Entry* _add = _internal_add_entries();
  // @@protoc_insertion_point(field_add:tflite.ArraysExtraInfo.entries)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ArraysExtraInfo_Entry >&
ArraysExtraInfo::entries() const {
  // @@protoc_insertion_point(field_list:tflite.ArraysExtraInfo.entries)
  return _impl_.entries_;
}

// -------------------------------------------------------------------

// ModelFlags_ModelCheck

// optional string count_type = 1 [default = "None"];
inline bool ModelFlags_ModelCheck::_internal_has_count_type() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ModelFlags_ModelCheck::has_count_type() const {
  return _internal_has_count_type();
}
inline void ModelFlags_ModelCheck::clear_count_type() {
  _impl_.count_type_.ClearToDefault(::tflite::ModelFlags_ModelCheck::Impl_::_i_give_permission_to_break_this_code_default_count_type_, GetArenaForAllocation());
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ModelFlags_ModelCheck::count_type() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.ModelCheck.count_type)
  if (_impl_.count_type_.IsDefault()) return Impl_::_i_give_permission_to_break_this_code_default_count_type_.get();
  return _internal_count_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelFlags_ModelCheck::set_count_type(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.count_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.ModelCheck.count_type)
}
inline std::string* ModelFlags_ModelCheck::mutable_count_type() {
  std::string* _s = _internal_mutable_count_type();
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.ModelCheck.count_type)
  return _s;
}
inline const std::string& ModelFlags_ModelCheck::_internal_count_type() const {
  return _impl_.count_type_.Get();
}
inline void ModelFlags_ModelCheck::_internal_set_count_type(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.count_type_.Set(value, GetArenaForAllocation());
}
inline std::string* ModelFlags_ModelCheck::_internal_mutable_count_type() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.count_type_.Mutable(::tflite::ModelFlags_ModelCheck::Impl_::_i_give_permission_to_break_this_code_default_count_type_, GetArenaForAllocation());
}
inline std::string* ModelFlags_ModelCheck::release_count_type() {
  // @@protoc_insertion_point(field_release:tflite.ModelFlags.ModelCheck.count_type)
  if (!_internal_has_count_type()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.count_type_.Release();
  return p;
}
inline void ModelFlags_ModelCheck::set_allocated_count_type(std::string* count_type) {
  if (count_type != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.count_type_.SetAllocated(count_type, GetArenaForAllocation());
  // @@protoc_insertion_point(field_set_allocated:tflite.ModelFlags.ModelCheck.count_type)
}

// optional int32 count_min = 2 [default = -1];
inline bool ModelFlags_ModelCheck::_internal_has_count_min() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool ModelFlags_ModelCheck::has_count_min() const {
  return _internal_has_count_min();
}
inline void ModelFlags_ModelCheck::clear_count_min() {
  _impl_.count_min_ = -1;
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline int32_t ModelFlags_ModelCheck::_internal_count_min() const {
  return _impl_.count_min_;
}
inline int32_t ModelFlags_ModelCheck::count_min() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.ModelCheck.count_min)
  return _internal_count_min();
}
inline void ModelFlags_ModelCheck::_internal_set_count_min(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000002u;
  _impl_.count_min_ = value;
}
inline void ModelFlags_ModelCheck::set_count_min(int32_t value) {
  _internal_set_count_min(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.ModelCheck.count_min)
}

// optional int32 count_max = 3 [default = -1];
inline bool ModelFlags_ModelCheck::_internal_has_count_max() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool ModelFlags_ModelCheck::has_count_max() const {
  return _internal_has_count_max();
}
inline void ModelFlags_ModelCheck::clear_count_max() {
  _impl_.count_max_ = -1;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline int32_t ModelFlags_ModelCheck::_internal_count_max() const {
  return _impl_.count_max_;
}
inline int32_t ModelFlags_ModelCheck::count_max() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.ModelCheck.count_max)
  return _internal_count_max();
}
inline void ModelFlags_ModelCheck::_internal_set_count_max(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.count_max_ = value;
}
inline void ModelFlags_ModelCheck::set_count_max(int32_t value) {
  _internal_set_count_max(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.ModelCheck.count_max)
}

// -------------------------------------------------------------------

// ModelFlags

// repeated .tflite.InputArray input_arrays = 1;
inline int ModelFlags::_internal_input_arrays_size() const {
  return _impl_.input_arrays_.size();
}
inline int ModelFlags::input_arrays_size() const {
  return _internal_input_arrays_size();
}
inline void ModelFlags::clear_input_arrays() {
  _impl_.input_arrays_.Clear();
}
inline ::tflite::InputArray* ModelFlags::mutable_input_arrays(int index) {
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.input_arrays)
  return _impl_.input_arrays_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::InputArray >*
ModelFlags::mutable_input_arrays() {
  // @@protoc_insertion_point(field_mutable_list:tflite.ModelFlags.input_arrays)
  return &_impl_.input_arrays_;
}
inline const ::tflite::InputArray& ModelFlags::_internal_input_arrays(int index) const {
  return _impl_.input_arrays_.Get(index);
}
inline const ::tflite::InputArray& ModelFlags::input_arrays(int index) const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.input_arrays)
  return _internal_input_arrays(index);
}
inline ::tflite::InputArray* ModelFlags::_internal_add_input_arrays() {
  return _impl_.input_arrays_.Add();
}
inline ::tflite::InputArray* ModelFlags::add_input_arrays() {
  ::tflite::InputArray* _add = _internal_add_input_arrays();
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.input_arrays)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::InputArray >&
ModelFlags::input_arrays() const {
  // @@protoc_insertion_point(field_list:tflite.ModelFlags.input_arrays)
  return _impl_.input_arrays_;
}

// repeated string output_arrays = 2;
inline int ModelFlags::_internal_output_arrays_size() const {
  return _impl_.output_arrays_.size();
}
inline int ModelFlags::output_arrays_size() const {
  return _internal_output_arrays_size();
}
inline void ModelFlags::clear_output_arrays() {
  _impl_.output_arrays_.Clear();
}
inline std::string* ModelFlags::add_output_arrays() {
  std::string* _s = _internal_add_output_arrays();
  // @@protoc_insertion_point(field_add_mutable:tflite.ModelFlags.output_arrays)
  return _s;
}
inline const std::string& ModelFlags::_internal_output_arrays(int index) const {
  return _impl_.output_arrays_.Get(index);
}
inline const std::string& ModelFlags::output_arrays(int index) const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.output_arrays)
  return _internal_output_arrays(index);
}
inline std::string* ModelFlags::mutable_output_arrays(int index) {
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.output_arrays)
  return _impl_.output_arrays_.Mutable(index);
}
inline void ModelFlags::set_output_arrays(int index, const std::string& value) {
  _impl_.output_arrays_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.output_arrays)
}
inline void ModelFlags::set_output_arrays(int index, std::string&& value) {
  _impl_.output_arrays_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.output_arrays)
}
inline void ModelFlags::set_output_arrays(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.output_arrays_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tflite.ModelFlags.output_arrays)
}
inline void ModelFlags::set_output_arrays(int index, const char* value, size_t size) {
  _impl_.output_arrays_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tflite.ModelFlags.output_arrays)
}
inline std::string* ModelFlags::_internal_add_output_arrays() {
  return _impl_.output_arrays_.Add();
}
inline void ModelFlags::add_output_arrays(const std::string& value) {
  _impl_.output_arrays_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.output_arrays)
}
inline void ModelFlags::add_output_arrays(std::string&& value) {
  _impl_.output_arrays_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.output_arrays)
}
inline void ModelFlags::add_output_arrays(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.output_arrays_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tflite.ModelFlags.output_arrays)
}
inline void ModelFlags::add_output_arrays(const char* value, size_t size) {
  _impl_.output_arrays_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tflite.ModelFlags.output_arrays)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelFlags::output_arrays() const {
  // @@protoc_insertion_point(field_list:tflite.ModelFlags.output_arrays)
  return _impl_.output_arrays_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelFlags::mutable_output_arrays() {
  // @@protoc_insertion_point(field_mutable_list:tflite.ModelFlags.output_arrays)
  return &_impl_.output_arrays_;
}

// repeated string control_output_arrays = 24;
inline int ModelFlags::_internal_control_output_arrays_size() const {
  return _impl_.control_output_arrays_.size();
}
inline int ModelFlags::control_output_arrays_size() const {
  return _internal_control_output_arrays_size();
}
inline void ModelFlags::clear_control_output_arrays() {
  _impl_.control_output_arrays_.Clear();
}
inline std::string* ModelFlags::add_control_output_arrays() {
  std::string* _s = _internal_add_control_output_arrays();
  // @@protoc_insertion_point(field_add_mutable:tflite.ModelFlags.control_output_arrays)
  return _s;
}
inline const std::string& ModelFlags::_internal_control_output_arrays(int index) const {
  return _impl_.control_output_arrays_.Get(index);
}
inline const std::string& ModelFlags::control_output_arrays(int index) const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.control_output_arrays)
  return _internal_control_output_arrays(index);
}
inline std::string* ModelFlags::mutable_control_output_arrays(int index) {
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.control_output_arrays)
  return _impl_.control_output_arrays_.Mutable(index);
}
inline void ModelFlags::set_control_output_arrays(int index, const std::string& value) {
  _impl_.control_output_arrays_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.control_output_arrays)
}
inline void ModelFlags::set_control_output_arrays(int index, std::string&& value) {
  _impl_.control_output_arrays_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.control_output_arrays)
}
inline void ModelFlags::set_control_output_arrays(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.control_output_arrays_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tflite.ModelFlags.control_output_arrays)
}
inline void ModelFlags::set_control_output_arrays(int index, const char* value, size_t size) {
  _impl_.control_output_arrays_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tflite.ModelFlags.control_output_arrays)
}
inline std::string* ModelFlags::_internal_add_control_output_arrays() {
  return _impl_.control_output_arrays_.Add();
}
inline void ModelFlags::add_control_output_arrays(const std::string& value) {
  _impl_.control_output_arrays_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.control_output_arrays)
}
inline void ModelFlags::add_control_output_arrays(std::string&& value) {
  _impl_.control_output_arrays_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.control_output_arrays)
}
inline void ModelFlags::add_control_output_arrays(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.control_output_arrays_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tflite.ModelFlags.control_output_arrays)
}
inline void ModelFlags::add_control_output_arrays(const char* value, size_t size) {
  _impl_.control_output_arrays_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tflite.ModelFlags.control_output_arrays)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelFlags::control_output_arrays() const {
  // @@protoc_insertion_point(field_list:tflite.ModelFlags.control_output_arrays)
  return _impl_.control_output_arrays_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelFlags::mutable_control_output_arrays() {
  // @@protoc_insertion_point(field_mutable_list:tflite.ModelFlags.control_output_arrays)
  return &_impl_.control_output_arrays_;
}

// optional bool variable_batch = 10;
inline bool ModelFlags::_internal_has_variable_batch() const {
  bool value = (_impl_._has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool ModelFlags::has_variable_batch() const {
  return _internal_has_variable_batch();
}
inline void ModelFlags::clear_variable_batch() {
  _impl_.variable_batch_ = false;
  _impl_._has_bits_[0] &= ~0x00000004u;
}
inline bool ModelFlags::_internal_variable_batch() const {
  return _impl_.variable_batch_;
}
inline bool ModelFlags::variable_batch() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.variable_batch)
  return _internal_variable_batch();
}
inline void ModelFlags::_internal_set_variable_batch(bool value) {
  _impl_._has_bits_[0] |= 0x00000004u;
  _impl_.variable_batch_ = value;
}
inline void ModelFlags::set_variable_batch(bool value) {
  _internal_set_variable_batch(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.variable_batch)
}

// repeated .tflite.RnnState rnn_states = 12;
inline int ModelFlags::_internal_rnn_states_size() const {
  return _impl_.rnn_states_.size();
}
inline int ModelFlags::rnn_states_size() const {
  return _internal_rnn_states_size();
}
inline void ModelFlags::clear_rnn_states() {
  _impl_.rnn_states_.Clear();
}
inline ::tflite::RnnState* ModelFlags::mutable_rnn_states(int index) {
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.rnn_states)
  return _impl_.rnn_states_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::RnnState >*
ModelFlags::mutable_rnn_states() {
  // @@protoc_insertion_point(field_mutable_list:tflite.ModelFlags.rnn_states)
  return &_impl_.rnn_states_;
}
inline const ::tflite::RnnState& ModelFlags::_internal_rnn_states(int index) const {
  return _impl_.rnn_states_.Get(index);
}
inline const ::tflite::RnnState& ModelFlags::rnn_states(int index) const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.rnn_states)
  return _internal_rnn_states(index);
}
inline ::tflite::RnnState* ModelFlags::_internal_add_rnn_states() {
  return _impl_.rnn_states_.Add();
}
inline ::tflite::RnnState* ModelFlags::add_rnn_states() {
  ::tflite::RnnState* _add = _internal_add_rnn_states();
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.rnn_states)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::RnnState >&
ModelFlags::rnn_states() const {
  // @@protoc_insertion_point(field_list:tflite.ModelFlags.rnn_states)
  return _impl_.rnn_states_;
}

// repeated .tflite.ModelFlags.ModelCheck model_checks = 14;
inline int ModelFlags::_internal_model_checks_size() const {
  return _impl_.model_checks_.size();
}
inline int ModelFlags::model_checks_size() const {
  return _internal_model_checks_size();
}
inline void ModelFlags::clear_model_checks() {
  _impl_.model_checks_.Clear();
}
inline ::tflite::ModelFlags_ModelCheck* ModelFlags::mutable_model_checks(int index) {
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.model_checks)
  return _impl_.model_checks_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ModelFlags_ModelCheck >*
ModelFlags::mutable_model_checks() {
  // @@protoc_insertion_point(field_mutable_list:tflite.ModelFlags.model_checks)
  return &_impl_.model_checks_;
}
inline const ::tflite::ModelFlags_ModelCheck& ModelFlags::_internal_model_checks(int index) const {
  return _impl_.model_checks_.Get(index);
}
inline const ::tflite::ModelFlags_ModelCheck& ModelFlags::model_checks(int index) const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.model_checks)
  return _internal_model_checks(index);
}
inline ::tflite::ModelFlags_ModelCheck* ModelFlags::_internal_add_model_checks() {
  return _impl_.model_checks_.Add();
}
inline ::tflite::ModelFlags_ModelCheck* ModelFlags::add_model_checks() {
  ::tflite::ModelFlags_ModelCheck* _add = _internal_add_model_checks();
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.model_checks)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tflite::ModelFlags_ModelCheck >&
ModelFlags::model_checks() const {
  // @@protoc_insertion_point(field_list:tflite.ModelFlags.model_checks)
  return _impl_.model_checks_;
}

// optional bool allow_nonexistent_arrays = 16;
inline bool ModelFlags::_internal_has_allow_nonexistent_arrays() const {
  bool value = (_impl_._has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool ModelFlags::has_allow_nonexistent_arrays() const {
  return _internal_has_allow_nonexistent_arrays();
}
inline void ModelFlags::clear_allow_nonexistent_arrays() {
  _impl_.allow_nonexistent_arrays_ = false;
  _impl_._has_bits_[0] &= ~0x00000008u;
}
inline bool ModelFlags::_internal_allow_nonexistent_arrays() const {
  return _impl_.allow_nonexistent_arrays_;
}
inline bool ModelFlags::allow_nonexistent_arrays() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.allow_nonexistent_arrays)
  return _internal_allow_nonexistent_arrays();
}
inline void ModelFlags::_internal_set_allow_nonexistent_arrays(bool value) {
  _impl_._has_bits_[0] |= 0x00000008u;
  _impl_.allow_nonexistent_arrays_ = value;
}
inline void ModelFlags::set_allow_nonexistent_arrays(bool value) {
  _internal_set_allow_nonexistent_arrays(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.allow_nonexistent_arrays)
}

// optional bool allow_nonascii_arrays = 17;
inline bool ModelFlags::_internal_has_allow_nonascii_arrays() const {
  bool value = (_impl_._has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool ModelFlags::has_allow_nonascii_arrays() const {
  return _internal_has_allow_nonascii_arrays();
}
inline void ModelFlags::clear_allow_nonascii_arrays() {
  _impl_.allow_nonascii_arrays_ = false;
  _impl_._has_bits_[0] &= ~0x00000010u;
}
inline bool ModelFlags::_internal_allow_nonascii_arrays() const {
  return _impl_.allow_nonascii_arrays_;
}
inline bool ModelFlags::allow_nonascii_arrays() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.allow_nonascii_arrays)
  return _internal_allow_nonascii_arrays();
}
inline void ModelFlags::_internal_set_allow_nonascii_arrays(bool value) {
  _impl_._has_bits_[0] |= 0x00000010u;
  _impl_.allow_nonascii_arrays_ = value;
}
inline void ModelFlags::set_allow_nonascii_arrays(bool value) {
  _internal_set_allow_nonascii_arrays(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.allow_nonascii_arrays)
}

// optional .tflite.ArraysExtraInfo arrays_extra_info = 18;
inline bool ModelFlags::_internal_has_arrays_extra_info() const {
  bool value = (_impl_._has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || _impl_.arrays_extra_info_ != nullptr);
  return value;
}
inline bool ModelFlags::has_arrays_extra_info() const {
  return _internal_has_arrays_extra_info();
}
inline void ModelFlags::clear_arrays_extra_info() {
  if (_impl_.arrays_extra_info_ != nullptr) _impl_.arrays_extra_info_->Clear();
  _impl_._has_bits_[0] &= ~0x00000002u;
}
inline const ::tflite::ArraysExtraInfo& ModelFlags::_internal_arrays_extra_info() const {
  const ::tflite::ArraysExtraInfo* p = _impl_.arrays_extra_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tflite::ArraysExtraInfo&>(
      ::tflite::_ArraysExtraInfo_default_instance_);
}
inline const ::tflite::ArraysExtraInfo& ModelFlags::arrays_extra_info() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.arrays_extra_info)
  return _internal_arrays_extra_info();
}
inline void ModelFlags::unsafe_arena_set_allocated_arrays_extra_info(
    ::tflite::ArraysExtraInfo* arrays_extra_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.arrays_extra_info_);
  }
  _impl_.arrays_extra_info_ = arrays_extra_info;
  if (arrays_extra_info) {
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tflite.ModelFlags.arrays_extra_info)
}
inline ::tflite::ArraysExtraInfo* ModelFlags::release_arrays_extra_info() {
  _impl_._has_bits_[0] &= ~0x00000002u;
  ::tflite::ArraysExtraInfo* temp = _impl_.arrays_extra_info_;
  _impl_.arrays_extra_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tflite::ArraysExtraInfo* ModelFlags::unsafe_arena_release_arrays_extra_info() {
  // @@protoc_insertion_point(field_release:tflite.ModelFlags.arrays_extra_info)
  _impl_._has_bits_[0] &= ~0x00000002u;
  ::tflite::ArraysExtraInfo* temp = _impl_.arrays_extra_info_;
  _impl_.arrays_extra_info_ = nullptr;
  return temp;
}
inline ::tflite::ArraysExtraInfo* ModelFlags::_internal_mutable_arrays_extra_info() {
  _impl_._has_bits_[0] |= 0x00000002u;
  if (_impl_.arrays_extra_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tflite::ArraysExtraInfo>(GetArenaForAllocation());
    _impl_.arrays_extra_info_ = p;
  }
  return _impl_.arrays_extra_info_;
}
inline ::tflite::ArraysExtraInfo* ModelFlags::mutable_arrays_extra_info() {
  ::tflite::ArraysExtraInfo* _msg = _internal_mutable_arrays_extra_info();
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.arrays_extra_info)
  return _msg;
}
inline void ModelFlags::set_allocated_arrays_extra_info(::tflite::ArraysExtraInfo* arrays_extra_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.arrays_extra_info_;
  }
  if (arrays_extra_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(arrays_extra_info);
    if (message_arena != submessage_arena) {
      arrays_extra_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, arrays_extra_info, submessage_arena);
    }
    _impl_._has_bits_[0] |= 0x00000002u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000002u;
  }
  _impl_.arrays_extra_info_ = arrays_extra_info;
  // @@protoc_insertion_point(field_set_allocated:tflite.ModelFlags.arrays_extra_info)
}

// optional bool change_concat_input_ranges = 19 [default = true];
inline bool ModelFlags::_internal_has_change_concat_input_ranges() const {
  bool value = (_impl_._has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool ModelFlags::has_change_concat_input_ranges() const {
  return _internal_has_change_concat_input_ranges();
}
inline void ModelFlags::clear_change_concat_input_ranges() {
  _impl_.change_concat_input_ranges_ = true;
  _impl_._has_bits_[0] &= ~0x00000100u;
}
inline bool ModelFlags::_internal_change_concat_input_ranges() const {
  return _impl_.change_concat_input_ranges_;
}
inline bool ModelFlags::change_concat_input_ranges() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.change_concat_input_ranges)
  return _internal_change_concat_input_ranges();
}
inline void ModelFlags::_internal_set_change_concat_input_ranges(bool value) {
  _impl_._has_bits_[0] |= 0x00000100u;
  _impl_.change_concat_input_ranges_ = value;
}
inline void ModelFlags::set_change_concat_input_ranges(bool value) {
  _internal_set_change_concat_input_ranges(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.change_concat_input_ranges)
}

// optional string saved_model_dir = 20;
inline bool ModelFlags::_internal_has_saved_model_dir() const {
  bool value = (_impl_._has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ModelFlags::has_saved_model_dir() const {
  return _internal_has_saved_model_dir();
}
inline void ModelFlags::clear_saved_model_dir() {
  _impl_.saved_model_dir_.ClearToEmpty();
  _impl_._has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ModelFlags::saved_model_dir() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.saved_model_dir)
  return _internal_saved_model_dir();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelFlags::set_saved_model_dir(ArgT0&& arg0, ArgT... args) {
 _impl_._has_bits_[0] |= 0x00000001u;
 _impl_.saved_model_dir_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.saved_model_dir)
}
inline std::string* ModelFlags::mutable_saved_model_dir() {
  std::string* _s = _internal_mutable_saved_model_dir();
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.saved_model_dir)
  return _s;
}
inline const std::string& ModelFlags::_internal_saved_model_dir() const {
  return _impl_.saved_model_dir_.Get();
}
inline void ModelFlags::_internal_set_saved_model_dir(const std::string& value) {
  _impl_._has_bits_[0] |= 0x00000001u;
  _impl_.saved_model_dir_.Set(value, GetArenaForAllocation());
}
inline std::string* ModelFlags::_internal_mutable_saved_model_dir() {
  _impl_._has_bits_[0] |= 0x00000001u;
  return _impl_.saved_model_dir_.Mutable(GetArenaForAllocation());
}
inline std::string* ModelFlags::release_saved_model_dir() {
  // @@protoc_insertion_point(field_release:tflite.ModelFlags.saved_model_dir)
  if (!_internal_has_saved_model_dir()) {
    return nullptr;
  }
  _impl_._has_bits_[0] &= ~0x00000001u;
  auto* p = _impl_.saved_model_dir_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.saved_model_dir_.IsDefault()) {
    _impl_.saved_model_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ModelFlags::set_allocated_saved_model_dir(std::string* saved_model_dir) {
  if (saved_model_dir != nullptr) {
    _impl_._has_bits_[0] |= 0x00000001u;
  } else {
    _impl_._has_bits_[0] &= ~0x00000001u;
  }
  _impl_.saved_model_dir_.SetAllocated(saved_model_dir, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.saved_model_dir_.IsDefault()) {
    _impl_.saved_model_dir_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tflite.ModelFlags.saved_model_dir)
}

// optional int32 saved_model_version = 21;
inline bool ModelFlags::_internal_has_saved_model_version() const {
  bool value = (_impl_._has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool ModelFlags::has_saved_model_version() const {
  return _internal_has_saved_model_version();
}
inline void ModelFlags::clear_saved_model_version() {
  _impl_.saved_model_version_ = 0;
  _impl_._has_bits_[0] &= ~0x00000040u;
}
inline int32_t ModelFlags::_internal_saved_model_version() const {
  return _impl_.saved_model_version_;
}
inline int32_t ModelFlags::saved_model_version() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.saved_model_version)
  return _internal_saved_model_version();
}
inline void ModelFlags::_internal_set_saved_model_version(int32_t value) {
  _impl_._has_bits_[0] |= 0x00000040u;
  _impl_.saved_model_version_ = value;
}
inline void ModelFlags::set_saved_model_version(int32_t value) {
  _internal_set_saved_model_version(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.saved_model_version)
}

// repeated string saved_model_tags = 22;
inline int ModelFlags::_internal_saved_model_tags_size() const {
  return _impl_.saved_model_tags_.size();
}
inline int ModelFlags::saved_model_tags_size() const {
  return _internal_saved_model_tags_size();
}
inline void ModelFlags::clear_saved_model_tags() {
  _impl_.saved_model_tags_.Clear();
}
inline std::string* ModelFlags::add_saved_model_tags() {
  std::string* _s = _internal_add_saved_model_tags();
  // @@protoc_insertion_point(field_add_mutable:tflite.ModelFlags.saved_model_tags)
  return _s;
}
inline const std::string& ModelFlags::_internal_saved_model_tags(int index) const {
  return _impl_.saved_model_tags_.Get(index);
}
inline const std::string& ModelFlags::saved_model_tags(int index) const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.saved_model_tags)
  return _internal_saved_model_tags(index);
}
inline std::string* ModelFlags::mutable_saved_model_tags(int index) {
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.saved_model_tags)
  return _impl_.saved_model_tags_.Mutable(index);
}
inline void ModelFlags::set_saved_model_tags(int index, const std::string& value) {
  _impl_.saved_model_tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.saved_model_tags)
}
inline void ModelFlags::set_saved_model_tags(int index, std::string&& value) {
  _impl_.saved_model_tags_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.saved_model_tags)
}
inline void ModelFlags::set_saved_model_tags(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.saved_model_tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tflite.ModelFlags.saved_model_tags)
}
inline void ModelFlags::set_saved_model_tags(int index, const char* value, size_t size) {
  _impl_.saved_model_tags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tflite.ModelFlags.saved_model_tags)
}
inline std::string* ModelFlags::_internal_add_saved_model_tags() {
  return _impl_.saved_model_tags_.Add();
}
inline void ModelFlags::add_saved_model_tags(const std::string& value) {
  _impl_.saved_model_tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.saved_model_tags)
}
inline void ModelFlags::add_saved_model_tags(std::string&& value) {
  _impl_.saved_model_tags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.saved_model_tags)
}
inline void ModelFlags::add_saved_model_tags(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.saved_model_tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tflite.ModelFlags.saved_model_tags)
}
inline void ModelFlags::add_saved_model_tags(const char* value, size_t size) {
  _impl_.saved_model_tags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tflite.ModelFlags.saved_model_tags)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelFlags::saved_model_tags() const {
  // @@protoc_insertion_point(field_list:tflite.ModelFlags.saved_model_tags)
  return _impl_.saved_model_tags_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelFlags::mutable_saved_model_tags() {
  // @@protoc_insertion_point(field_mutable_list:tflite.ModelFlags.saved_model_tags)
  return &_impl_.saved_model_tags_;
}

// repeated string saved_model_exported_names = 23;
inline int ModelFlags::_internal_saved_model_exported_names_size() const {
  return _impl_.saved_model_exported_names_.size();
}
inline int ModelFlags::saved_model_exported_names_size() const {
  return _internal_saved_model_exported_names_size();
}
inline void ModelFlags::clear_saved_model_exported_names() {
  _impl_.saved_model_exported_names_.Clear();
}
inline std::string* ModelFlags::add_saved_model_exported_names() {
  std::string* _s = _internal_add_saved_model_exported_names();
  // @@protoc_insertion_point(field_add_mutable:tflite.ModelFlags.saved_model_exported_names)
  return _s;
}
inline const std::string& ModelFlags::_internal_saved_model_exported_names(int index) const {
  return _impl_.saved_model_exported_names_.Get(index);
}
inline const std::string& ModelFlags::saved_model_exported_names(int index) const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.saved_model_exported_names)
  return _internal_saved_model_exported_names(index);
}
inline std::string* ModelFlags::mutable_saved_model_exported_names(int index) {
  // @@protoc_insertion_point(field_mutable:tflite.ModelFlags.saved_model_exported_names)
  return _impl_.saved_model_exported_names_.Mutable(index);
}
inline void ModelFlags::set_saved_model_exported_names(int index, const std::string& value) {
  _impl_.saved_model_exported_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.saved_model_exported_names)
}
inline void ModelFlags::set_saved_model_exported_names(int index, std::string&& value) {
  _impl_.saved_model_exported_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.saved_model_exported_names)
}
inline void ModelFlags::set_saved_model_exported_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.saved_model_exported_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tflite.ModelFlags.saved_model_exported_names)
}
inline void ModelFlags::set_saved_model_exported_names(int index, const char* value, size_t size) {
  _impl_.saved_model_exported_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tflite.ModelFlags.saved_model_exported_names)
}
inline std::string* ModelFlags::_internal_add_saved_model_exported_names() {
  return _impl_.saved_model_exported_names_.Add();
}
inline void ModelFlags::add_saved_model_exported_names(const std::string& value) {
  _impl_.saved_model_exported_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.saved_model_exported_names)
}
inline void ModelFlags::add_saved_model_exported_names(std::string&& value) {
  _impl_.saved_model_exported_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tflite.ModelFlags.saved_model_exported_names)
}
inline void ModelFlags::add_saved_model_exported_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.saved_model_exported_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tflite.ModelFlags.saved_model_exported_names)
}
inline void ModelFlags::add_saved_model_exported_names(const char* value, size_t size) {
  _impl_.saved_model_exported_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tflite.ModelFlags.saved_model_exported_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelFlags::saved_model_exported_names() const {
  // @@protoc_insertion_point(field_list:tflite.ModelFlags.saved_model_exported_names)
  return _impl_.saved_model_exported_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelFlags::mutable_saved_model_exported_names() {
  // @@protoc_insertion_point(field_mutable_list:tflite.ModelFlags.saved_model_exported_names)
  return &_impl_.saved_model_exported_names_;
}

// optional bool use_hlo_import = 25;
inline bool ModelFlags::_internal_has_use_hlo_import() const {
  bool value = (_impl_._has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool ModelFlags::has_use_hlo_import() const {
  return _internal_has_use_hlo_import();
}
inline void ModelFlags::clear_use_hlo_import() {
  _impl_.use_hlo_import_ = false;
  _impl_._has_bits_[0] &= ~0x00000020u;
}
inline bool ModelFlags::_internal_use_hlo_import() const {
  return _impl_.use_hlo_import_;
}
inline bool ModelFlags::use_hlo_import() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.use_hlo_import)
  return _internal_use_hlo_import();
}
inline void ModelFlags::_internal_set_use_hlo_import(bool value) {
  _impl_._has_bits_[0] |= 0x00000020u;
  _impl_.use_hlo_import_ = value;
}
inline void ModelFlags::set_use_hlo_import(bool value) {
  _internal_set_use_hlo_import(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.use_hlo_import)
}

// optional .tflite.ModelFlags.HloFileType hlo_file_type = 26;
inline bool ModelFlags::_internal_has_hlo_file_type() const {
  bool value = (_impl_._has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool ModelFlags::has_hlo_file_type() const {
  return _internal_has_hlo_file_type();
}
inline void ModelFlags::clear_hlo_file_type() {
  _impl_.hlo_file_type_ = 0;
  _impl_._has_bits_[0] &= ~0x00000080u;
}
inline ::tflite::ModelFlags_HloFileType ModelFlags::_internal_hlo_file_type() const {
  return static_cast< ::tflite::ModelFlags_HloFileType >(_impl_.hlo_file_type_);
}
inline ::tflite::ModelFlags_HloFileType ModelFlags::hlo_file_type() const {
  // @@protoc_insertion_point(field_get:tflite.ModelFlags.hlo_file_type)
  return _internal_hlo_file_type();
}
inline void ModelFlags::_internal_set_hlo_file_type(::tflite::ModelFlags_HloFileType value) {
  assert(::tflite::ModelFlags_HloFileType_IsValid(value));
  _impl_._has_bits_[0] |= 0x00000080u;
  _impl_.hlo_file_type_ = value;
}
inline void ModelFlags::set_hlo_file_type(::tflite::ModelFlags_HloFileType value) {
  _internal_set_hlo_file_type(value);
  // @@protoc_insertion_point(field_set:tflite.ModelFlags.hlo_file_type)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tflite

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tflite::ModelFlags_HloFileType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tflite::ModelFlags_HloFileType>() {
  return ::tflite::ModelFlags_HloFileType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcompiler_2fmlir_2flite_2fmodel_5fflags_2eproto
