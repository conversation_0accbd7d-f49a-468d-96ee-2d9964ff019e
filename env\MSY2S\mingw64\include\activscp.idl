cpp_quote("/**")
cpp_quote(" * This file is part of the mingw-w64 runtime package.")
cpp_quote(" * No warranty is given; refer to the file DISCLAIMER within this package.")
cpp_quote(" */")
cpp_quote("")
cpp_quote("#ifndef __ActivScp_h")
cpp_quote("#define __ActivScp_h")
cpp_quote("")
cpp_quote("#include <winapifamily.h>")
cpp_quote("")
cpp_quote("#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)")
cpp_quote("")

#ifndef DO_NO_IMPORTS
import "ocidl.idl";
import "oleidl.idl";
import "oaidl.idl";
#endif

cpp_quote("")
cpp_quote("#ifndef _NO_SCRIPT_GUIDS")
cpp_quote("")
cpp_quote("DEFINE_GUID(CATID_ActiveScript, 0xf0b7a1a1, 0x9847, 0x11cf, 0x8f, 0x20, 0x00, 0x80, 0x5f, 0x2c, 0xd0, 0x64);")
cpp_quote("DEFINE_GUID(CATID_ActiveScriptParse, 0xf0b7a1a2, 0x9847, 0x11cf, 0x8f, 0x20, 0x00, 0x80, 0x5f, 0x2c, 0xd0, 0x64);")
cpp_quote("DEFINE_GUID(CATID_ActiveScriptEncode, 0xf0b7a1a3, 0x9847, 0x11cf, 0x8f, 0x20, 0x00, 0x80, 0x5f, 0x2c, 0xd0, 0x64);")
cpp_quote("DEFINE_GUID(OID_VBSSIP, 0x1629f04e, 0x2799, 0x4db5, 0x8f, 0xe5, 0xac, 0xe1, 0x0f, 0x17, 0xeb, 0xab);")
cpp_quote("DEFINE_GUID(OID_JSSIP,  0x6c9e010, 0x38ce, 0x11d4, 0xa2, 0xa3, 0x00, 0x10, 0x4b, 0xd3, 0x50, 0x90);")
cpp_quote("DEFINE_GUID(OID_WSFSIP, 0x1a610570, 0x38ce, 0x11d4, 0xa2, 0xa3, 0x00, 0x10, 0x4b, 0xd3, 0x50, 0x90);")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#define SCRIPTITEM_ISVISIBLE 0x00000002")
cpp_quote("#define SCRIPTITEM_ISSOURCE 0x00000004")
cpp_quote("#define SCRIPTITEM_GLOBALMEMBERS 0x00000008")
cpp_quote("#define SCRIPTITEM_ISPERSISTENT 0x00000040")
cpp_quote("#define SCRIPTITEM_CODEONLY 0x00000200")
cpp_quote("#define SCRIPTITEM_NOCODE 0x00000400")
cpp_quote("")
cpp_quote("#define SCRIPTITEM_ALL_FLAGS (SCRIPTITEM_ISSOURCE | SCRIPTITEM_ISVISIBLE | SCRIPTITEM_ISPERSISTENT | SCRIPTITEM_GLOBALMEMBERS | SCRIPTITEM_NOCODE | SCRIPTITEM_CODEONLY)")
cpp_quote("")
cpp_quote("#define SCRIPTTYPELIB_ISCONTROL 0x00000010")
cpp_quote("#define SCRIPTTYPELIB_ISPERSISTENT 0x00000040")
cpp_quote("")
cpp_quote("#define SCRIPTTYPELIB_ALL_FLAGS (SCRIPTTYPELIB_ISCONTROL | SCRIPTTYPELIB_ISPERSISTENT)")
cpp_quote("")
cpp_quote("#define SCRIPTTEXT_DELAYEXECUTION 0x00000001")
cpp_quote("#define SCRIPTTEXT_ISVISIBLE 0x00000002")
cpp_quote("#define SCRIPTTEXT_ISEXPRESSION 0x00000020")
cpp_quote("#define SCRIPTTEXT_ISPERSISTENT 0x00000040")
cpp_quote("#define SCRIPTTEXT_HOSTMANAGESSOURCE 0x00000080")
cpp_quote("#define SCRIPTTEXT_ISXDOMAIN 0x00000100")
cpp_quote("")
cpp_quote("#define SCRIPTTEXT_ALL_FLAGS (SCRIPTTEXT_DELAYEXECUTION | SCRIPTTEXT_ISVISIBLE | SCRIPTTEXT_ISEXPRESSION | SCRIPTTEXT_ISPERSISTENT | SCRIPTTEXT_HOSTMANAGESSOURCE | SCRIPTTEXT_ISXDOMAIN)")
cpp_quote("")
cpp_quote("#define SCRIPTPROC_ISEXPRESSION 0x00000020")
cpp_quote("#define SCRIPTPROC_HOSTMANAGESSOURCE 0x00000080")
cpp_quote("#define SCRIPTPROC_IMPLICIT_THIS 0x00000100")
cpp_quote("#define SCRIPTPROC_IMPLICIT_PARENTS 0x00000200")
cpp_quote("#define SCRIPTPROC_ISXDOMAIN 0x00000400")
cpp_quote("")
cpp_quote("#define SCRIPTPROC_ALL_FLAGS (SCRIPTPROC_HOSTMANAGESSOURCE | SCRIPTPROC_ISEXPRESSION | SCRIPTPROC_IMPLICIT_THIS | SCRIPTPROC_IMPLICIT_PARENTS | SCRIPTPROC_ISXDOMAIN)")
cpp_quote("")
cpp_quote("#define SCRIPTINFO_IUNKNOWN 0x00000001")
cpp_quote("#define SCRIPTINFO_ITYPEINFO 0x00000002")
cpp_quote("")
cpp_quote("#define SCRIPTINFO_ALL_FLAGS (SCRIPTINFO_IUNKNOWN | SCRIPTINFO_ITYPEINFO)")
cpp_quote("")
cpp_quote("#define SCRIPTINTERRUPT_DEBUG 0x00000001")
cpp_quote("#define SCRIPTINTERRUPT_RAISEEXCEPTION 0x00000002")
cpp_quote("")
cpp_quote("#define SCRIPTINTERRUPT_ALL_FLAGS (SCRIPTINTERRUPT_DEBUG | SCRIPTINTERRUPT_RAISEEXCEPTION)")
cpp_quote("")
cpp_quote("#define SCRIPTSTAT_STATEMENT_COUNT 0x1")
cpp_quote("#define SCRIPTSTAT_INSTRUCTION_COUNT 0x2")
cpp_quote("#define SCRIPTSTAT_INTSTRUCTION_TIME 0x3")
cpp_quote("#define SCRIPTSTAT_TOTAL_TIME 0x4")
cpp_quote("")
cpp_quote("#define SCRIPT_ENCODE_SECTION 0x1")
cpp_quote("")
cpp_quote("#define SCRIPT_ENCODE_DEFAULT_LANGUAGE 0x1")
cpp_quote("#define SCRIPT_ENCODE_NO_ASP_LANGUAGE 0x2")
cpp_quote("")
cpp_quote("#define SCRIPTPROP_NAME 0x0")
cpp_quote("#define SCRIPTPROP_MAJORVERSION 0x1")
cpp_quote("#define SCRIPTPROP_MINORVERSION 0x2")
cpp_quote("#define SCRIPTPROP_BUILDNUMBER 0x3")
cpp_quote("")
cpp_quote("#define SCRIPTPROP_DELAYEDEVENTSINKING 0x1000")
cpp_quote("#define SCRIPTPROP_CATCHEXCEPTION 0x1001")
cpp_quote("#define SCRIPTPROP_CONVERSIONLCID 0x1002")
cpp_quote("#define SCRIPTPROP_HOSTSTACKREQUIRED 0x1003")
cpp_quote("")
cpp_quote("#define SCRIPTPROP_DEBUGGER 0x1100")
cpp_quote("#define SCRIPTPROP_JITDEBUG 0x1101")
cpp_quote("")
cpp_quote("#define SCRIPTPROP_GCCONTROLSOFTCLOSE 0x2000")
cpp_quote("")
cpp_quote("#define SCRIPTPROP_INTEGERMODE 0x3000")
cpp_quote("#define SCRIPTPROP_STRINGCOMPAREINSTANCE 0x3001")
cpp_quote("")
cpp_quote("#define SCRIPTPROP_INVOKEVERSIONING 0x4000")
cpp_quote("")
cpp_quote("#define SCRIPTPROP_HACK_FIBERSUPPORT 0x70000000")
cpp_quote("#define SCRIPTPROP_HACK_TRIDENTEVENTSINK 0x70000001")
cpp_quote("#define SCRIPTPROP_ABBREVIATE_GLOBALNAME_RESOLUTION 0x70000002")
cpp_quote("#define SCRIPTPROP_HOSTKEEPALIVE 0x70000004")
cpp_quote("")
cpp_quote("#define SCRIPT_E_RECORDED __MSABI_LONG(0x86664004)")
cpp_quote("#define SCRIPT_E_REPORTED __MSABI_LONG(0x80020101)")
cpp_quote("#define SCRIPT_E_PROPAGATE __MSABI_LONG(0x80020102)")
cpp_quote("")
typedef enum tagSCRIPTLANGUAGEVERSION {
  SCRIPTLANGUAGEVERSION_DEFAULT = 0,
  SCRIPTLANGUAGEVERSION_5_7 = 1,
  SCRIPTLANGUAGEVERSION_5_8 = 2,
  SCRIPTLANGUAGEVERSION_MAX = 255
} SCRIPTLANGUAGEVERSION;

cpp_quote("")
typedef enum tagSCRIPTSTATE {
  SCRIPTSTATE_UNINITIALIZED = 0,
  SCRIPTSTATE_INITIALIZED = 5,
  SCRIPTSTATE_STARTED = 1,
  SCRIPTSTATE_CONNECTED = 2,
  SCRIPTSTATE_DISCONNECTED = 3,
  SCRIPTSTATE_CLOSED = 4,
} SCRIPTSTATE;

cpp_quote("")
typedef enum tagSCRIPTTRACEINFO {
  SCRIPTTRACEINFO_SCRIPTSTART = 0,
  SCRIPTTRACEINFO_SCRIPTEND = 1,
  SCRIPTTRACEINFO_COMCALLSTART = 2,
  SCRIPTTRACEINFO_COMCALLEND = 3,
  SCRIPTTRACEINFO_CREATEOBJSTART = 4,
  SCRIPTTRACEINFO_CREATEOBJEND = 5,
  SCRIPTTRACEINFO_GETOBJSTART = 6,
  SCRIPTTRACEINFO_GETOBJEND = 7,
} SCRIPTTRACEINFO;

cpp_quote("")
typedef enum tagSCRIPTTHREADSTATE {
  SCRIPTTHREADSTATE_NOTINSCRIPT = 0,
  SCRIPTTHREADSTATE_RUNNING = 1,
} SCRIPTTHREADSTATE;

cpp_quote("")
typedef enum tagSCRIPTGCTYPE {
  SCRIPTGCTYPE_NORMAL = 0,
  SCRIPTGCTYPE_EXHAUSTIVE = 1,
} SCRIPTGCTYPE;

cpp_quote("")
typedef enum tagSCRIPTUICITEM {
  SCRIPTUICITEM_INPUTBOX = 1,
  SCRIPTUICITEM_MSGBOX = 2,
} SCRIPTUICITEM;

cpp_quote("")
typedef enum tagSCRIPTUICHANDLING {
  SCRIPTUICHANDLING_ALLOW = 0,
  SCRIPTUICHANDLING_NOUIERROR = 1,
  SCRIPTUICHANDLING_NOUIDEFAULT = 2,
} SCRIPTUICHANDLING;

cpp_quote("")
typedef DWORD SCRIPTTHREADID;
cpp_quote("")
cpp_quote("#define SCRIPTTHREADID_CURRENT ((SCRIPTTHREADID)-1)")
cpp_quote("#define SCRIPTTHREADID_BASE    ((SCRIPTTHREADID)-2)")
cpp_quote("#define SCRIPTTHREADID_ALL     ((SCRIPTTHREADID)-3)")
cpp_quote("")

interface IActiveScriptSite;
interface IActiveScriptError;
interface IActiveScriptError64;
interface IActiveScriptSiteWindow;
interface IActiveScriptSiteUIControl;
interface IActiveScriptSiteInterruptPoll;
interface IActiveScript;
interface IActiveScriptParse32;
interface IActiveScriptParse64;
interface IActiveScriptParseProcedureOld32;
interface IActiveScriptParseProcedureOld64;
interface IActiveScriptParseProcedure32;
interface IActiveScriptParseProcedure64;
interface IActiveScriptParseProcedure2_32;
interface IActiveScriptParseProcedure2_64;
interface IActiveScriptEncode;
interface IActiveScriptHostEncode;
interface IBindEventHandler;
interface IActiveScriptStats;
interface IActiveScriptProperty;
interface ITridentEventSink;
interface IActiveScriptGarbageCollector;
interface IActiveScriptSIPInfo;
interface IActiveScriptStringCompare;

cpp_quote("")
[object, uuid (DB01A1E3-A42B-11cf-8f20-00805f2cd064), pointer_default (unique)]
interface IActiveScriptSite : IUnknown {
  HRESULT GetLCID ([out] LCID *plcid);
  HRESULT GetItemInfo ([in] LPCOLESTR pstrName,[in] DWORD dwReturnMask,[out] IUnknown **ppiunkItem,[out] ITypeInfo **ppti);
  HRESULT GetDocVersionString ([out] BSTR *pbstrVersion);
  HRESULT OnScriptTerminate ([in] const VARIANT *pvarResult,[in] const EXCEPINFO *pexcepinfo);
  HRESULT OnStateChange ([in] SCRIPTSTATE ssScriptState);
  HRESULT OnScriptError ([in] IActiveScriptError *pscripterror);
  HRESULT OnEnterScript (void);
  HRESULT OnLeaveScript (void);
}

cpp_quote("")
[object, uuid (EAE1BA61-A4ED-11cf-8f20-00805f2cd064), pointer_default (unique)]
interface IActiveScriptError : IUnknown {
  [local] HRESULT GetExceptionInfo ([out] EXCEPINFO *pexcepinfo);
  [call_as (GetExceptionInfo)] HRESULT RemoteGetExceptionInfo ([out] EXCEPINFO *pexcepinfo);
  HRESULT GetSourcePosition ([out] DWORD *pdwSourceContext,[out] ULONG *pulLineNumber,[out] LONG *plCharacterPosition);
  HRESULT GetSourceLineText ([out] BSTR *pbstrSourceLine);
}

cpp_quote("")
[object, uuid (B21FB2A1-5b8f-4963-8c21-21450f84ed7f), pointer_default (unique)]
interface IActiveScriptError64 : IActiveScriptError {
  HRESULT GetSourcePosition64 ([out] DWORDLONG *pdwSourceContext,[out] ULONG *pulLineNumber,[out] LONG *plCharacterPosition);
}

[object, uuid (D10F6761-83e9-11cf-8f20-00805f2cd064), pointer_default (unique)]
interface IActiveScriptSiteWindow : IUnknown {
  HRESULT GetWindow ([out] HWND *phwnd);
  HRESULT EnableModeless ([in] BOOL fEnable);
}

cpp_quote("")
[object, uuid (AEDAE97E-D7EE-4796-B960-7f092ae844ab), pointer_default (unique)]
interface IActiveScriptSiteUIControl : IUnknown {
  HRESULT GetUIBehavior ([in] SCRIPTUICITEM UicItem,[out] SCRIPTUICHANDLING *pUicHandling);
}

cpp_quote("")
[object, uuid (539698a0-CDCA-11cf-A5EB-00aa0047a063), pointer_default (unique)]
interface IActiveScriptSiteInterruptPoll : IUnknown {
  HRESULT QueryContinue (void);
}

cpp_quote("")
[object, uuid (BB1A2AE1-A4F9-11cf-8f20-00805f2cd064), pointer_default (unique)]
interface IActiveScript : IUnknown {
  HRESULT SetScriptSite ([in] IActiveScriptSite *pass);
  HRESULT GetScriptSite ([in] REFIID riid,[out, iid_is (riid)] void **ppvObject);
  HRESULT SetScriptState ([in] SCRIPTSTATE ss);
  HRESULT GetScriptState ([out] SCRIPTSTATE *pssState);
  HRESULT Close (void);
  HRESULT AddNamedItem ([in] LPCOLESTR pstrName,[in] DWORD dwFlags);
  HRESULT AddTypeLib ([in] REFGUID rguidTypeLib,[in] DWORD dwMajor,[in] DWORD dwMinor,[in] DWORD dwFlags);
  HRESULT GetScriptDispatch ([in] LPCOLESTR pstrItemName,[out] IDispatch **ppdisp);
  HRESULT GetCurrentScriptThreadID ([out] SCRIPTTHREADID *pstidThread);
  HRESULT GetScriptThreadID ([in] DWORD dwWin32ThreadId,[out] SCRIPTTHREADID *pstidThread);
  HRESULT GetScriptThreadState ([in] SCRIPTTHREADID stidThread,[out] SCRIPTTHREADSTATE *pstsState);
  HRESULT InterruptScriptThread ([in] SCRIPTTHREADID stidThread,[in] const EXCEPINFO *pexcepinfo,[in] DWORD dwFlags);
  HRESULT Clone ([out] IActiveScript **ppscript);
}

cpp_quote("")
[object, uuid (BB1A2AE2-A4F9-11cf-8f20-00805f2cd064), pointer_default (unique)]
interface IActiveScriptParse32 : IUnknown {
  HRESULT InitNew (void);
  HRESULT AddScriptlet ([in] LPCOLESTR pstrDefaultName,[in] LPCOLESTR pstrCode,[in] LPCOLESTR pstrItemName,[in] LPCOLESTR pstrSubItemName,[in] LPCOLESTR pstrEventName,[in] LPCOLESTR pstrDelimiter,[in] DWORD dwSourceContextCookie,[in] ULONG ulStartingLineNumber,[in] DWORD dwFlags,[out] BSTR *pbstrName,[out] EXCEPINFO *pexcepinfo);
  HRESULT ParseScriptText ([in] LPCOLESTR pstrCode,[in] LPCOLESTR pstrItemName,[in] IUnknown *punkContext,[in] LPCOLESTR pstrDelimiter,[in] DWORD dwSourceContextCookie,[in] ULONG ulStartingLineNumber,[in] DWORD dwFlags,[out] VARIANT *pvarResult,[out] EXCEPINFO *pexcepinfo);
}

cpp_quote("")
[object, uuid (C7EF7658-E1EE-480e-97ea-D52CB4D76D17), pointer_default (unique)]
interface IActiveScriptParse64 : IUnknown {
  HRESULT InitNew (void);
  HRESULT AddScriptlet ([in] LPCOLESTR pstrDefaultName,[in] LPCOLESTR pstrCode,[in] LPCOLESTR pstrItemName,[in] LPCOLESTR pstrSubItemName,[in] LPCOLESTR pstrEventName,[in] LPCOLESTR pstrDelimiter,[in] DWORDLONG dwSourceContextCookie,[in] ULONG ulStartingLineNumber,[in] DWORD dwFlags,[out] BSTR *pbstrName,[out] EXCEPINFO *pexcepinfo);
  HRESULT ParseScriptText ([in] LPCOLESTR pstrCode,[in] LPCOLESTR pstrItemName,[in] IUnknown *punkContext,[in] LPCOLESTR pstrDelimiter,[in] DWORDLONG dwSourceContextCookie,[in] ULONG ulStartingLineNumber,[in] DWORD dwFlags,[out] VARIANT *pvarResult,[out] EXCEPINFO *pexcepinfo);
}

cpp_quote("")
cpp_quote("#ifdef _WIN64")
cpp_quote("#define IActiveScriptParse IActiveScriptParse64")
cpp_quote("#define IID_IActiveScriptParse IID_IActiveScriptParse64")
cpp_quote("#else")
cpp_quote("#define IActiveScriptParse     IActiveScriptParse32")
cpp_quote("#define IID_IActiveScriptParse IID_IActiveScriptParse32")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("typedef IActiveScriptParse *PIActiveScriptParse;")
cpp_quote("")
[object, uuid (1cff0050-6fdd-11d0-9328-00a0c90dcaa9), pointer_default (unique)]
interface IActiveScriptParseProcedureOld32 : IUnknown {
  HRESULT ParseProcedureText ([in] LPCOLESTR pstrCode,[in] LPCOLESTR pstrFormalParams,[in] LPCOLESTR pstrItemName,[in] IUnknown *punkContext,[in] LPCOLESTR pstrDelimiter,[in] DWORD dwSourceContextCookie,[in] ULONG ulStartingLineNumber,[in] DWORD dwFlags,[out] IDispatch **ppdisp);
}

cpp_quote("")
[object, uuid (21f57128-08c9-4638-BA12-22d15d88dc5c), pointer_default (unique)]
interface IActiveScriptParseProcedureOld64 : IUnknown {
  HRESULT ParseProcedureText ([in] LPCOLESTR pstrCode,[in] LPCOLESTR pstrFormalParams,[in] LPCOLESTR pstrItemName,[in] IUnknown *punkContext,[in] LPCOLESTR pstrDelimiter,[in] DWORDLONG dwSourceContextCookie,[in] ULONG ulStartingLineNumber,[in] DWORD dwFlags,[out] IDispatch **ppdisp);
}

cpp_quote("")
cpp_quote("#ifdef _WIN64")
cpp_quote("#define IActiveScriptParseProcedureOld IActiveScriptParseProcedureOld64")
cpp_quote("#define IID_IActiveScriptParseProcedureOld IID_IActiveScriptParseProcedureOld64")
cpp_quote("#else")
cpp_quote("#define     IActiveScriptParseProcedureOld IActiveScriptParseProcedureOld32")
cpp_quote("#define IID_IActiveScriptParseProcedureOld IID_IActiveScriptParseProcedureOld32")
cpp_quote("#endif")

cpp_quote("")
cpp_quote("typedef IActiveScriptParseProcedureOld *PIActiveScriptParseProcedureOld;")

cpp_quote("")
[object, uuid (AA5B6A80-B834-11d0-932f-00a0c90dcaa9), pointer_default (unique)]
interface IActiveScriptParseProcedure32 : IUnknown {
  HRESULT ParseProcedureText ([in] LPCOLESTR pstrCode,[in] LPCOLESTR pstrFormalParams,[in] LPCOLESTR pstrProcedureName,[in] LPCOLESTR pstrItemName,[in] IUnknown *punkContext,[in] LPCOLESTR pstrDelimiter,[in] DWORD dwSourceContextCookie,[in] ULONG ulStartingLineNumber,[in] DWORD dwFlags,[out] IDispatch **ppdisp);
}
cpp_quote("")
[object, uuid (C64713B6-E029-4cc5-9200-438b72890b6a), pointer_default (unique)]
interface IActiveScriptParseProcedure64 : IUnknown {
  HRESULT ParseProcedureText ([in] LPCOLESTR pstrCode,[in] LPCOLESTR pstrFormalParams,[in] LPCOLESTR pstrProcedureName,[in] LPCOLESTR pstrItemName,[in] IUnknown *punkContext,[in] LPCOLESTR pstrDelimiter,[in] DWORDLONG dwSourceContextCookie,[in] ULONG ulStartingLineNumber,[in] DWORD dwFlags,[out] IDispatch **ppdisp);
}

cpp_quote("")
cpp_quote("#ifdef _WIN64")
cpp_quote("#define IActiveScriptParseProcedure IActiveScriptParseProcedure64")
cpp_quote("#define IID_IActiveScriptParseProcedure IID_IActiveScriptParseProcedure64")
cpp_quote("#else")
cpp_quote("#define IActiveScriptParseProcedure IActiveScriptParseProcedure32")
cpp_quote("#define IID_IActiveScriptParseProcedure IID_IActiveScriptParseProcedure32")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("typedef IActiveScriptParseProcedure *PIActiveScriptParseProcedure;")

cpp_quote("")
[object, uuid (71ee5b20-FB04-11d1-B3A8-00a0c911e8b2), pointer_default (unique)]
interface IActiveScriptParseProcedure2_32 : IActiveScriptParseProcedure32 {
}

cpp_quote("")
[object, uuid (FE7C4271-210c-448d-9f54-76dab7047b28), pointer_default (unique)]
interface IActiveScriptParseProcedure2_64 : IActiveScriptParseProcedure64 {
}

cpp_quote("")
cpp_quote("#ifdef _WIN64")
cpp_quote("#define IActiveScriptParseProcedure2 IActiveScriptParseProcedure2_64")
cpp_quote("#define IID_IActiveScriptParseProcedure2 IID_IActiveScriptParseProcedure2_64")
cpp_quote("#else")
cpp_quote("#define IActiveScriptParseProcedure2 IActiveScriptParseProcedure2_32")
cpp_quote("#define IID_IActiveScriptParseProcedure2 IID_IActiveScriptParseProcedure2_32")
cpp_quote("#endif")
cpp_quote("")
cpp_quote("typedef IActiveScriptParseProcedure2 *PIActiveScriptParseProcedure2;")

cpp_quote("")
[object, uuid (BB1A2AE3-A4F9-11cf-8f20-00805f2cd064), pointer_default (unique)]
interface IActiveScriptEncode : IUnknown {
  HRESULT EncodeSection ([in] LPCOLESTR pchIn,[in] DWORD cchIn,[in, out] LPOLESTR pchOut,[in] DWORD cchOut,[in, out] DWORD *pcchRet);
  HRESULT DecodeScript ([in] LPCOLESTR pchIn,[in] DWORD cchIn,[in, out] LPOLESTR pchOut,[in] DWORD cchOut,[in, out] DWORD *pcchRet);
  HRESULT GetEncodeProgId ([in, out] BSTR *pbstrOut);
}

cpp_quote("")
[object, uuid (BEE9B76E-CFE3-11d1-B747-00c04fc2b085), pointer_default (unique)]
interface IActiveScriptHostEncode : IUnknown {
  HRESULT EncodeScriptHostFile ([in] BSTR bstrInFile,[in, out] BSTR *pbstrOutFile,[in] unsigned long cFlags,[in] BSTR bstrDefaultLang);
}

cpp_quote("")
[object, uuid (63cdbcb0-C1B1-11d0-9336-00a0c90dcaa9), pointer_default (unique)]
interface IBindEventHandler : IUnknown {
  HRESULT BindHandler ([in] LPCOLESTR pstrEvent,[in] IDispatch *pdisp);
}

cpp_quote("")
[object, uuid (B8DA6310-E19B-11d0-933c-00a0c90dcaa9), pointer_default (unique)]
interface IActiveScriptStats : IUnknown {
  HRESULT GetStat ([in] DWORD stid,[out] ULONG *pluHi,[out] ULONG *pluLo);
  HRESULT GetStatEx ([in] REFGUID guid,[out] ULONG *pluHi,[out] ULONG *pluLo);
  HRESULT ResetStats (void);
}

cpp_quote("")
[object, uuid (4954e0d0-FBC7-11d1-8410-006008c3fbfc), pointer_default (unique)]
interface IActiveScriptProperty : IUnknown {
  HRESULT GetProperty ([in] DWORD dwProperty,[in] VARIANT *pvarIndex,[out] VARIANT *pvarValue);
  HRESULT SetProperty ([in] DWORD dwProperty,[in] VARIANT *pvarIndex,[in] VARIANT *pvarValue);
}

cpp_quote("")
[object, uuid (1dc9ca50-06ef-11d2-8415-006008c3fbfc), pointer_default (unique)]
interface ITridentEventSink : IUnknown {
  HRESULT FireEvent ([in] LPCOLESTR pstrEvent,[in] DISPPARAMS *pdp,[out] VARIANT *pvarRes,[out] EXCEPINFO *pei);
}

cpp_quote("")
[object, uuid (6aa2c4a0-2b53-11d4-A2A0-00104bd35090), pointer_default (unique)]
interface IActiveScriptGarbageCollector : IUnknown {
  HRESULT CollectGarbage (SCRIPTGCTYPE scriptgctype);
}

cpp_quote("")
[object, uuid (764651d0-38de-11d4-A2A3-00104bd35090), pointer_default (unique)]
interface IActiveScriptSIPInfo : IUnknown {
  HRESULT GetSIPOID ([out] GUID *poid_sip);
}

cpp_quote("")
[object, uuid (4b7272ae-1955-4bfe-98b0-************), pointer_default (unique)]
interface IActiveScriptSiteTraceInfo : IUnknown {
  HRESULT SendScriptTraceInfo ([in] SCRIPTTRACEINFO stiEventType,[in] GUID guidContextID,[in] DWORD dwScriptContextCookie,[in] LONG lScriptStatementStart,[in] LONG lScriptStatementEnd,[in] DWORD64 dwReserved);
}

cpp_quote("")
[object, uuid (C35456E7-BEBF-4a1b-86a9-24d56be8b369), pointer_default (unique)]
interface IActiveScriptTraceInfo : IUnknown {
  HRESULT StartScriptTracing ([in] IActiveScriptSiteTraceInfo *pSiteTraceInfo,[in] GUID guidContextID);
  HRESULT StopScriptTracing (void);
}

cpp_quote("")
[object, uuid (58562769-ED52-42f7-8403-4963514e1f11), pointer_default (unique)]
interface IActiveScriptStringCompare : IUnknown {
  HRESULT StrComp ([in] BSTR bszStr1,[in] BSTR bszStr2,[out, retval] LONG *iRet);
}
cpp_quote("#endif")
cpp_quote("")
cpp_quote("#endif")
