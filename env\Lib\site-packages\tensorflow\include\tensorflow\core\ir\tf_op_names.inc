#ifdef GET_OP_CATEGORIES
#undef GET_OP_CATEGORIES
bool IsAdd(TFOp op) const;
bool IsAddN(TFOp op) const;
bool IsAll(TFOp op) const;
bool IsAngle(TFOp op) const;
bool IsAny(TFOp op) const;
bool IsAnyDiv(TFOp op) const;
bool IsAnyBatchMatMul(TFOp op) const;
bool IsAnyMatMul(TFOp op) const;
bool IsAnyMax(TFOp op) const;
bool IsAnyMaxPool(TFOp op) const;
bool IsAnyMin(TFOp op) const;
bool IsAnyMul(TFOp op) const;
bool IsAnySparseSegmentReduction(TFOp op) const;
bool IsApproximateEqual(TFOp op) const;
bool IsArg(TFOp op) const;
bool IsArgMax(TFOp op) const;
bool IsArgMin(TFOp op) const;
bool IsAssert(TFOp op) const;
bool IsAssign(TFOp op) const;
bool IsAsString(TFOp op) const;
bool IsAtan2(TFOp op) const;
bool IsAvgPoolGrad(TFOp op) const;
bool IsBetainc(TFOp op) const;
bool IsBiasAdd(TFOp op) const;
bool IsBiasAddV2(TFOp op) const;
bool IsBiasAddGrad(TFOp op) const;
bool IsBitcast(TFOp op) const;
bool IsBroadcastTo(TFOp op) const;
bool IsCast(TFOp op) const;
bool IsCheckNumerics(TFOp op) const;
bool IsCollective(TFOp op) const;
bool IsComplex(TFOp op) const;
bool IsComplexAbs(TFOp op) const;
bool IsConcat(TFOp op) const;
bool IsConcatV2(TFOp op) const;
bool IsConcatOffset(TFOp op) const;
bool IsConj(TFOp op) const;
bool IsConjugateTranspose(TFOp op) const;
bool IsConstant(TFOp op) const;
bool IsControlFlow(TFOp op) const;
bool IsConv2D(TFOp op) const;
bool IsConv2DBackpropFilter(TFOp op) const;
bool IsConv2DBackpropInput(TFOp op) const;
bool IsConv3D(TFOp op) const;
bool IsConv3DBackpropFilterV2(TFOp op) const;
bool IsConv3DBackpropInputV2(TFOp op) const;
bool IsDepthwiseConv2dNative(TFOp op) const;
bool IsDepthwiseConv2dNativeBackpropFilter(TFOp op) const;
bool IsDepthwiseConv2dNativeBackpropInput(TFOp op) const;
bool IsDequeueOp(TFOp op) const;
bool IsDiv(TFOp op) const;
bool IsDivNoNan(TFOp op) const;
bool IsElementWiseMonotonic(TFOp op, bool *is_non_decreasing) const;
bool IsElu(TFOp op) const;
bool IsEluGrad(TFOp op) const;
bool IsQuantizationEmulation(TFOp op) const;
bool IsEnter(TFOp op) const;
bool IsEqual(TFOp op) const;
bool IsExit(TFOp op) const;
bool IsExp(TFOp op) const;
bool IsFakeParam(TFOp op) const;
bool IsFill(TFOp op) const;
bool IsFloorDiv(TFOp op) const;
bool IsFloorMod(TFOp op) const;
bool IsFusedBatchNorm(TFOp op) const;
bool IsFusedBatchNormEx(TFOp op) const;
bool IsFusedBatchNormGrad(TFOp op) const;
bool IsGather(TFOp op) const;
bool IsGreater(TFOp op) const;
bool IsGreaterEqual(TFOp op) const;
bool IsHistogramSummary(TFOp op) const;
bool IsHostConstant(TFOp op) const;
bool IsIdentity(TFOp op) const;
bool IsIdentityN(TFOp op) const;
bool IsIdentityNSingleInput(TFOp op) const;
bool IsIf(TFOp op) const;
bool IsIgamma(TFOp op) const;
bool IsIgammac(TFOp op) const;
bool IsImag(TFOp op) const;
bool IsImmutableConst(TFOp op) const;
bool IsInvGrad(TFOp op) const;
bool IsLeakyRelu(TFOp op) const;
bool IsLeakyReluGrad(TFOp op) const;
bool IsLess(TFOp op) const;
bool IsLessEqual(TFOp op) const;
bool IsLog(TFOp op) const;
bool IsLogicalAnd(TFOp op) const;
bool IsLogicalNot(TFOp op) const;
bool IsLogicalOr(TFOp op) const;
bool IsLoopCond(TFOp op) const;
bool IsMatMul(TFOp op) const;
bool IsMax(TFOp op) const;
bool IsMaxPoolGrad(TFOp op) const;
bool IsMaximum(TFOp op) const;
bool IsMean(TFOp op) const;
bool IsMerge(TFOp op) const;
bool IsMin(TFOp op) const;
bool IsMinimum(TFOp op) const;
bool IsMirrorPad(TFOp op) const;
bool IsMirrorPadGrad(TFOp op) const;
bool IsMod(TFOp op) const;
bool IsMul(TFOp op) const;
bool IsMulNoNan(TFOp op) const;
bool IsNeg(TFOp op) const;
bool IsNextIteration(TFOp op) const;
bool IsNoOp(TFOp op) const;
bool IsNotEqual(TFOp op) const;
bool IsOnesLike(TFOp op) const;
bool IsPack(TFOp op) const;
bool IsPad(TFOp op) const;
bool IsPartitionedCall(TFOp op) const;
bool IsPlaceholder(TFOp op) const;
bool IsPolygamma(TFOp op) const;
bool IsPow(TFOp op) const;
bool IsPrint(TFOp op) const;
bool IsProd(TFOp op) const;
bool IsQuantizedMatMul(TFOp op) const;
bool IsQueue(TFOp op) const;
bool IsRandomShuffle(TFOp op) const;
bool IsRank(TFOp op) const;
bool IsReadVariableOp(TFOp op) const;
bool IsReadVariablesOp(TFOp op) const;
bool IsReal(TFOp op) const;
bool IsRealDiv(TFOp op) const;
bool IsReciprocalGrad(TFOp op) const;
bool IsRecv(TFOp op) const;
bool IsReduction(TFOp op) const;
bool IsRelu(TFOp op) const;
bool IsRelu6(TFOp op) const;
bool IsRelu6Grad(TFOp op) const;
bool IsReluGrad(TFOp op) const;
bool IsReshape(TFOp op) const;
bool IsRestore(TFOp op) const;
bool IsReturn(TFOp op) const;
bool IsRetval(TFOp op) const;
bool IsReverse(TFOp op) const;
bool IsReverseV2(TFOp op) const;
bool IsRsqrt(TFOp op) const;
bool IsRsqrtGrad(TFOp op) const;
bool IsSelect(TFOp op) const;
bool IsSeluGrad(TFOp op) const;
bool IsSend(TFOp op) const;
bool IsShape(TFOp op) const;
bool IsShapeN(TFOp op) const;
bool IsShuffle(TFOp op) const;
bool IsSigmoid(TFOp op) const;
bool IsSigmoidGrad(TFOp op) const;
bool IsSize(TFOp op) const;
bool IsSlice(TFOp op) const;
bool IsSnapshot(TFOp op) const;
bool IsSoftmax(TFOp op) const;
bool IsSoftplus(TFOp op) const;
bool IsSoftplusGrad(TFOp op) const;
bool IsSoftsignGrad(TFOp op) const;
bool IsSplit(TFOp op) const;
bool IsSplitV(TFOp op) const;
bool IsSqrt(TFOp op) const;
bool IsSqrtGrad(TFOp op) const;
bool IsSquare(TFOp op) const;
bool IsSquaredDifference(TFOp op) const;
bool IsSqueeze(TFOp op) const;
bool IsStackCloseOp(TFOp op) const;
bool IsStackOp(TFOp op) const;
bool IsStackPopOp(TFOp op) const;
bool IsStackPushOp(TFOp op) const;
bool IsStatefulPartitionedCall(TFOp op) const;
bool IsStopGradient(TFOp op) const;
bool IsStridedSlice(TFOp op) const;
bool IsStridedSliceGrad(TFOp op) const;
bool IsStringToHashBucketFast(TFOp op) const;
bool IsSub(TFOp op) const;
bool IsSum(TFOp op) const;
bool IsSwitch(TFOp op) const;
bool IsSymbolicGradient(TFOp op) const;
bool IsTanh(TFOp op) const;
bool IsTanhGrad(TFOp op) const;
bool IsTensorArray(TFOp op) const;
bool IsTile(TFOp op) const;
bool IsTranspose(TFOp op) const;
bool IsTruncateDiv(TFOp op) const;
bool IsTruncateMod(TFOp op) const;
bool IsUnique(TFOp op) const;
bool IsUnpack(TFOp op) const;
bool IsVariable(TFOp op) const;
bool IsWhile(TFOp op) const;
bool IsXdivy(TFOp op) const;
bool IsXlaLaunch(TFOp op) const;
bool IsZerosLike(TFOp op) const;
bool IsZeta(TFOp op) const;
bool IsAggregate(TFOp op) const;
bool IsCommutative(TFOp op) const;
#endif  // GET_OP_CATEGORIES

#ifdef GET_OP_NAME_DECLS
#undef GET_OP_NAME_DECLS
StringAttr add_;
StringAttr add_n_;
StringAttr add_v2_;
StringAttr all_;
StringAttr angle_;
StringAttr any_;
StringAttr approximate_equal_;
StringAttr arg_;
StringAttr arg_max_;
StringAttr arg_min_;
StringAttr arg_pool_grad_;
StringAttr assert_;
StringAttr assign_;
StringAttr assign_variable_op_;
StringAttr as_string_;
StringAttr atan2_;
StringAttr auto_reload_variable_;
StringAttr batch_matmul_;
StringAttr batch_matmul_v2_;
StringAttr betainc_;
StringAttr bias_add_;
StringAttr bias_add_grad_;
StringAttr bias_add_v1_;
StringAttr bitcast_;
StringAttr broadcast_to_;
StringAttr cast_;
StringAttr check_numerics_;
StringAttr collective_bcast_recv_;
StringAttr collective_bcast_send_;
StringAttr collective_reduce_;
StringAttr complex_abs_;
StringAttr complex_;
StringAttr concat_;
StringAttr concat_offset_;
StringAttr concat_v2_;
StringAttr conj_;
StringAttr conjugate_transpose_;
StringAttr const_;
StringAttr control_trigger_;
StringAttr conv_2d_back_prop_filter_;
StringAttr conv_2d_back_prop_input_;
StringAttr conv_2d_;
StringAttr conv_3d_back_prop_filter_v2_;
StringAttr conv_3d_back_prop_input_v2_;
StringAttr conv_3d_;
StringAttr depth_wise_conv_2d_native_back_prop_filter_;
StringAttr depth_wise_conv_2d_native_back_prop_input_;
StringAttr depth_wise_conv_2d_native_;
StringAttr device_arg_;
StringAttr device_retval_;
StringAttr div_;
StringAttr div_no_nan_;
StringAttr elu_;
StringAttr elu_grad_;
StringAttr enter_;
StringAttr equal_;
StringAttr exit_;
StringAttr exp_;
StringAttr fake_param_;
StringAttr fake_quant_with_min_max_args_;
StringAttr fake_quant_with_min_max_args_gradient_;
StringAttr fake_quant_with_min_max_vars_;
StringAttr fake_quant_with_min_max_vars_gradient_;
StringAttr fake_quant_with_min_max_vars_per_channel_;
StringAttr fake_quant_with_min_max_vars_per_channel_gradient_;
StringAttr fifo_queue_v2_;
StringAttr fill_;
StringAttr floor_div_;
StringAttr floor_mod_;
StringAttr fractional_max_pool_;
StringAttr fused_batch_norm_ex_;
StringAttr fused_batch_norm_;
StringAttr fused_batch_norm_grad_;
StringAttr fused_batch_norm_grad_v2_;
StringAttr fused_batch_norm_grad_v3_;
StringAttr fused_batch_norm_v2_;
StringAttr fused_batch_norm_v3_;
StringAttr gather_;
StringAttr gather_v2_;
StringAttr greater_equal_;
StringAttr greater_;
StringAttr histogram_summary_;
StringAttr host_const_;
StringAttr host_recv_;
StringAttr host_send_;
StringAttr identity_;
StringAttr identity_n_;
StringAttr if_;
StringAttr igammac_;
StringAttr igamma_;
StringAttr imag_;
StringAttr immutable_const_;
StringAttr inv_grad_;
StringAttr leaky_relu_grad_;
StringAttr leaky_relu_;
StringAttr less_equal_;
StringAttr less_;
StringAttr logical_and_;
StringAttr logical_not_;
StringAttr logical_or_;
StringAttr log_;
StringAttr loop_cond_;
StringAttr matmul_;
StringAttr maximum_;
StringAttr max_;
StringAttr max_pool_3d_;
StringAttr max_pool_grad_;
StringAttr max_pool_;
StringAttr max_pool_v2_;
StringAttr max_pool_with_argmax_;
StringAttr mean_;
StringAttr merge_;
StringAttr minimum_;
StringAttr min_;
StringAttr mirror_pad_grad_;
StringAttr mirror_pad_;
StringAttr mod_;
StringAttr mul_;
StringAttr mul_no_nan_;
StringAttr neg_;
StringAttr next_iteration_;
StringAttr no_op_;
StringAttr not_equal_;
StringAttr ones_like_;
StringAttr pack_;
StringAttr pad_;
StringAttr pad_v2_;
StringAttr padding_fifo_queue_v2_;
StringAttr partitioned_call_;
StringAttr placeholder_;
StringAttr placeholder_v2_;
StringAttr placeholder_with_default_;
StringAttr poly_gamma_;
StringAttr pow_;
StringAttr prevent_gradient_;
StringAttr print_;
StringAttr print_v2_;
StringAttr priority_queue_v2_;
StringAttr prod_;
StringAttr quantize_and_dequantize_;
StringAttr quantize_and_dequantize_v2_;
StringAttr quantize_and_dequantize_v3_;
StringAttr quantize_and_dequantize_v4_;
StringAttr quantize_and_dequantize_v4_grad_;
StringAttr quantized_matmul_;
StringAttr quantized_matmul_v2_;
StringAttr queue_dequeue_many_;
StringAttr queue_dequeue_many_v2_;
StringAttr queue_dequeue_;
StringAttr queue_dequeue_upto_;
StringAttr queue_dequeue_upto_v2_;
StringAttr queue_dequeue_v2_;
StringAttr random_shuffle_;
StringAttr random_shuffle_queue_v2_;
StringAttr rank_;
StringAttr read_variable_op_;
StringAttr read_variables_op_;
StringAttr real_div_;
StringAttr real_;
StringAttr reciprocal_grad_;
StringAttr recv_;
StringAttr ref_enter_;
StringAttr ref_exit_;
StringAttr ref_identity_;
StringAttr ref_merge_;
StringAttr ref_next_iteration_;
StringAttr ref_switch_;
StringAttr relu6_grad_;
StringAttr relu6_;
StringAttr relu_grad_;
StringAttr relu_;
StringAttr reshape_;
StringAttr resource_gather_;
StringAttr restore_;
StringAttr restore_slice_;
StringAttr restore_v2_;
StringAttr return_;
StringAttr retval_;
StringAttr reverse_;
StringAttr reverse_v2_;
StringAttr rsqrt_grad_;
StringAttr rsqrt_;
StringAttr segment_max_;
StringAttr segment_min_;
StringAttr select_;
StringAttr select_v2_;
StringAttr selu_grad_;
StringAttr send_;
StringAttr shape_n_;
StringAttr shape_;
StringAttr shuffle_;
StringAttr sigmoid_grad_;
StringAttr sigmoid_;
StringAttr size_;
StringAttr slice_;
StringAttr snapshot_;
StringAttr softmax_;
StringAttr softplus_;
StringAttr softplus_grad_;
StringAttr softsign_grad_;
StringAttr sparse_matmul_;
StringAttr sparse_segment_mean_;
StringAttr sparse_segment_mean_with_num_segments_;
StringAttr sparse_segment_sqrtn_;
StringAttr sparse_segment_sqrtn_with_num_segments_;
StringAttr sparse_segment_sum_;
StringAttr sparse_segment_sum_with_num_segments_;
StringAttr split_;
StringAttr split_v_;
StringAttr sqrt_grad_;
StringAttr sqrt_;
StringAttr squared_difference_;
StringAttr square_;
StringAttr squeeze_;
StringAttr stack_close_;
StringAttr stack_close_v2_;
StringAttr stack_pop_;
StringAttr stack_pop_v2_;
StringAttr stack_push_;
StringAttr stack_push_v2_;
StringAttr stack_;
StringAttr stack_v2_;
StringAttr stateful_partitioned_call_;
StringAttr stateless_if_;
StringAttr stateless_while_;
StringAttr stop_gradient_;
StringAttr strided_slice_grad_;
StringAttr strided_slice_;
StringAttr string_to_hashbucket_fast_;
StringAttr sub_;
StringAttr sum_;
StringAttr switch_n_;
StringAttr switch_;
StringAttr symbolic_gradient_;
StringAttr tanh_grad_;
StringAttr tanh_;
StringAttr tile_;
StringAttr transpose_;
StringAttr truncate_div_;
StringAttr truncate_mod_;
StringAttr unique_;
StringAttr unique_v2_;
StringAttr unpack_;
StringAttr unsorted_segment_max_;
StringAttr unsorted_segment_min_;
StringAttr var_handle_op_;
StringAttr var_handles_op_;
StringAttr variable_v2_;
StringAttr variable_;
StringAttr while_;
StringAttr xdivy_;
StringAttr xla_merge_;
StringAttr zeros_like_;
StringAttr zeta_;
#endif  // GET_OP_NAME_DECLS

#ifdef GET_OP_NAME_DEFS
#undef GET_OP_NAME_DEFS
add_ = StringAttr::get(getContext(), "tfg.Add");
add_n_ = StringAttr::get(getContext(), "tfg.AddN");
add_v2_ = StringAttr::get(getContext(), "tfg.AddV2");
all_ = StringAttr::get(getContext(), "tfg.All");
angle_ = StringAttr::get(getContext(), "tfg.Angle");
any_ = StringAttr::get(getContext(), "tfg.Any");
approximate_equal_ = StringAttr::get(getContext(), "tfg.ApproximateEqual");
arg_ = StringAttr::get(getContext(), "tfg._Arg");
arg_max_ = StringAttr::get(getContext(), "tfg.ArgMax");
arg_min_ = StringAttr::get(getContext(), "tfg.ArgMin");
arg_pool_grad_ = StringAttr::get(getContext(), "tfg.AvgPoolGrad");
assert_ = StringAttr::get(getContext(), "tfg.Assert");
assign_ = StringAttr::get(getContext(), "tfg.Assign");
assign_variable_op_ = StringAttr::get(getContext(), "tfg.AssignVariableOp");
as_string_ = StringAttr::get(getContext(), "tfg.AsString");
atan2_ = StringAttr::get(getContext(), "tfg.Atan2");
auto_reload_variable_ = StringAttr::get(getContext(), "tfg.AutoReloadVariable");
batch_matmul_ = StringAttr::get(getContext(), "tfg.BatchMatMul");
batch_matmul_v2_ = StringAttr::get(getContext(), "tfg.BatchMatMulV2");
betainc_ = StringAttr::get(getContext(), "tfg.Betainc");
bias_add_ = StringAttr::get(getContext(), "tfg.BiasAdd");
bias_add_grad_ = StringAttr::get(getContext(), "tfg.BiasAddGrad");
bias_add_v1_ = StringAttr::get(getContext(), "tfg.BiasAddV1");
bitcast_ = StringAttr::get(getContext(), "tfg.Bitcast");
broadcast_to_ = StringAttr::get(getContext(), "tfg.BroadcastTo");
cast_ = StringAttr::get(getContext(), "tfg.Cast");
check_numerics_ = StringAttr::get(getContext(), "tfg.CheckNumerics");
collective_bcast_recv_ =
    StringAttr::get(getContext(), "tfg.CollectiveBcastRecv");
collective_bcast_send_ =
    StringAttr::get(getContext(), "tfg.CollectiveBcastSend");
collective_reduce_ = StringAttr::get(getContext(), "tfg.CollectiveReduce");
complex_abs_ = StringAttr::get(getContext(), "tfg.ComplexAbs");
complex_ = StringAttr::get(getContext(), "tfg.Complex");
concat_ = StringAttr::get(getContext(), "tfg.Concat");
concat_offset_ = StringAttr::get(getContext(), "tfg.ConcatOffset");
concat_v2_ = StringAttr::get(getContext(), "tfg.ConcatV2");
conj_ = StringAttr::get(getContext(), "tfg.Conj");
conjugate_transpose_ = StringAttr::get(getContext(), "tfg.ConjugateTranspose");
const_ = StringAttr::get(getContext(), "tfg.Const");
control_trigger_ = StringAttr::get(getContext(), "tfg.ControlTrigger");
conv_2d_back_prop_filter_ =
    StringAttr::get(getContext(), "tfg.Conv2DBackpropFilter");
conv_2d_back_prop_input_ =
    StringAttr::get(getContext(), "tfg.Conv2DBackpropInput");
conv_2d_ = StringAttr::get(getContext(), "tfg.Conv2D");
conv_3d_back_prop_filter_v2_ =
    StringAttr::get(getContext(), "tfg.Conv3DBackpropFilterV2");
conv_3d_back_prop_input_v2_ =
    StringAttr::get(getContext(), "tfg.Conv3DBackpropInputV2");
conv_3d_ = StringAttr::get(getContext(), "tfg.Conv3D");
depth_wise_conv_2d_native_back_prop_filter_ =
    StringAttr::get(getContext(), "tfg.DepthwiseConv2dNativeBackpropFilter");
depth_wise_conv_2d_native_back_prop_input_ =
    StringAttr::get(getContext(), "tfg.DepthwiseConv2dNativeBackpropInput");
depth_wise_conv_2d_native_ =
    StringAttr::get(getContext(), "tfg.DepthwiseConv2dNative");
device_arg_ = StringAttr::get(getContext(), "tfg._DeviceArg");
device_retval_ = StringAttr::get(getContext(), "tfg._DeviceRetval");
div_ = StringAttr::get(getContext(), "tfg.Div");
div_no_nan_ = StringAttr::get(getContext(), "tfg.DivNoNan");
elu_ = StringAttr::get(getContext(), "tfg.Elu");
elu_grad_ = StringAttr::get(getContext(), "tfg.EluGrad");
enter_ = StringAttr::get(getContext(), "tfg.Enter");
equal_ = StringAttr::get(getContext(), "tfg.Equal");
exit_ = StringAttr::get(getContext(), "tfg.Exit");
exp_ = StringAttr::get(getContext(), "tfg.Exp");
fake_param_ = StringAttr::get(getContext(), "tfg.FakeParam");
fake_quant_with_min_max_args_ =
    StringAttr::get(getContext(), "tfg.FakeQuantWithMinMaxArgs");
fake_quant_with_min_max_args_gradient_ =
    StringAttr::get(getContext(), "tfg.FakeQuantWithMinMaxArgsGradient");
fake_quant_with_min_max_vars_ =
    StringAttr::get(getContext(), "tfg.FakeQuantWithMinMaxVars");
fake_quant_with_min_max_vars_gradient_ =
    StringAttr::get(getContext(), "tfg.FakeQuantWithMinMaxVarsGradient");
fake_quant_with_min_max_vars_per_channel_ =
    StringAttr::get(getContext(), "tfg.FakeQuantWithMinMaxPerChannel");
fake_quant_with_min_max_vars_per_channel_gradient_ =
    StringAttr::get(getContext(), "tfg.FakeQuantWithMinMaxPerChannelGradient");
fifo_queue_v2_ = StringAttr::get(getContext(), "tfg.FIFOQueueV2");
fill_ = StringAttr::get(getContext(), "tfg.Fill");
floor_div_ = StringAttr::get(getContext(), "tfg.FloorDiv");
floor_mod_ = StringAttr::get(getContext(), "tfg.FloorMod");
fractional_max_pool_ = StringAttr::get(getContext(), "tfg.FractionalMaxPool");
fused_batch_norm_ex_ = StringAttr::get(getContext(), "tfg._FusedBatchNormEx");
fused_batch_norm_ = StringAttr::get(getContext(), "tfg.FusedBatchNorm");
fused_batch_norm_grad_ =
    StringAttr::get(getContext(), "tfg.FusedBatchNormGrad");
fused_batch_norm_grad_v2_ =
    StringAttr::get(getContext(), "tfg.FusedBatchNormGradV2");
fused_batch_norm_grad_v3_ =
    StringAttr::get(getContext(), "tfg.FusedBatchNormGradV3");
fused_batch_norm_v2_ = StringAttr::get(getContext(), "tfg.FusedBatchNormV2");
fused_batch_norm_v3_ = StringAttr::get(getContext(), "tfg.FusedBatchNormV3");
gather_ = StringAttr::get(getContext(), "tfg.Gather");
gather_v2_ = StringAttr::get(getContext(), "tfg.GatherV2");
greater_equal_ = StringAttr::get(getContext(), "tfg.GreaterEqual");
greater_ = StringAttr::get(getContext(), "tfg.Greater");
histogram_summary_ = StringAttr::get(getContext(), "tfg.HistogramSummary");
host_const_ = StringAttr::get(getContext(), "tfg.HostConst");
host_recv_ = StringAttr::get(getContext(), "tfg._HostRecv");
host_send_ = StringAttr::get(getContext(), "tfg._HostSend");
identity_ = StringAttr::get(getContext(), "tfg.Identity");
identity_n_ = StringAttr::get(getContext(), "tfg.IdentityN");
if_ = StringAttr::get(getContext(), "tfg.If");
igammac_ = StringAttr::get(getContext(), "tfg.Igammac");
igamma_ = StringAttr::get(getContext(), "tfg.Igamma");
imag_ = StringAttr::get(getContext(), "tfg.Imag");
immutable_const_ = StringAttr::get(getContext(), "tfg.ImmutableConst");
inv_grad_ = StringAttr::get(getContext(), "tfg.InvGrad");
leaky_relu_grad_ = StringAttr::get(getContext(), "tfg.LeakyReluGrad");
leaky_relu_ = StringAttr::get(getContext(), "tfg.LeakyRelu");
less_equal_ = StringAttr::get(getContext(), "tfg.LessEqual");
less_ = StringAttr::get(getContext(), "tfg.Less");
logical_and_ = StringAttr::get(getContext(), "tfg.LogicalAnd");
logical_not_ = StringAttr::get(getContext(), "tfg.LogicalNot");
logical_or_ = StringAttr::get(getContext(), "tfg.LogicalOr");
log_ = StringAttr::get(getContext(), "tfg.Log");
loop_cond_ = StringAttr::get(getContext(), "tfg.LoopCond");
matmul_ = StringAttr::get(getContext(), "tfg.MatMul");
maximum_ = StringAttr::get(getContext(), "tfg.Maximum");
max_ = StringAttr::get(getContext(), "tfg.Max");
max_pool_3d_ = StringAttr::get(getContext(), "tfg.MaxPool3D");
max_pool_grad_ = StringAttr::get(getContext(), "tfg.MaxPoolGrad");
max_pool_ = StringAttr::get(getContext(), "tfg.MaxPool");
max_pool_v2_ = StringAttr::get(getContext(), "tfg.MaxPoolV2");
max_pool_with_argmax_ = StringAttr::get(getContext(), "tfg.MaxPoolWithArgmax");
mean_ = StringAttr::get(getContext(), "tfg.Mean");
merge_ = StringAttr::get(getContext(), "tfg.Merge");
minimum_ = StringAttr::get(getContext(), "tfg.Minimum");
min_ = StringAttr::get(getContext(), "tfg.Min");
mirror_pad_grad_ = StringAttr::get(getContext(), "tfg.MirrorPadGrad");
mirror_pad_ = StringAttr::get(getContext(), "tfg.MirrorPad");
mod_ = StringAttr::get(getContext(), "tfg.Mod");
mul_ = StringAttr::get(getContext(), "tfg.Mul");
mul_no_nan_ = StringAttr::get(getContext(), "tfg.MulNoNan");
neg_ = StringAttr::get(getContext(), "tfg.Neg");
next_iteration_ = StringAttr::get(getContext(), "tfg.NextIteration");
no_op_ = StringAttr::get(getContext(), "tfg.NoOp");
not_equal_ = StringAttr::get(getContext(), "tfg.NotEqual");
ones_like_ = StringAttr::get(getContext(), "tfg.OnesLike");
pack_ = StringAttr::get(getContext(), "tfg.Pack");
pad_ = StringAttr::get(getContext(), "tfg.Pad");
pad_v2_ = StringAttr::get(getContext(), "tfg.PadV2");
padding_fifo_queue_v2_ =
    StringAttr::get(getContext(), "tfg.PaddingFIFOQueueV2");
partitioned_call_ = StringAttr::get(getContext(), "tfg.PartitionedCall");
placeholder_ = StringAttr::get(getContext(), "tfg.Placeholder");
placeholder_v2_ = StringAttr::get(getContext(), "tfg.PlaceholderV2");
placeholder_with_default_ =
    StringAttr::get(getContext(), "tfg.PlaceholderWithDefault");
poly_gamma_ = StringAttr::get(getContext(), "tfg.Polygamma");
pow_ = StringAttr::get(getContext(), "tfg.Pow");
prevent_gradient_ = StringAttr::get(getContext(), "tfg.PreventGradient");
print_ = StringAttr::get(getContext(), "tfg.Print");
print_v2_ = StringAttr::get(getContext(), "tfg.PrintV2");
priority_queue_v2_ = StringAttr::get(getContext(), "tfg.PriorityQueueV2");
prod_ = StringAttr::get(getContext(), "tfg.Prod");
quantize_and_dequantize_ =
    StringAttr::get(getContext(), "tfg.QuantizeAndDequantize");
quantize_and_dequantize_v2_ =
    StringAttr::get(getContext(), "tfg.QuantizeAndDequantizeV2");
quantize_and_dequantize_v3_ =
    StringAttr::get(getContext(), "tfg.QuantizeAndDequantizeV3");
quantize_and_dequantize_v4_ =
    StringAttr::get(getContext(), "tfg.QuantizeAndDequantizeV4");
quantize_and_dequantize_v4_grad_ =
    StringAttr::get(getContext(), "tfg.QuantizeAndDequantizeV4Grad");
quantized_matmul_ = StringAttr::get(getContext(), "tfg.QuantizedMatMul");
quantized_matmul_v2_ = StringAttr::get(getContext(), "tfg.QuantizedMatMulV2");
queue_dequeue_many_ = StringAttr::get(getContext(), "tfg.QueueDequeueMany");
queue_dequeue_many_v2_ =
    StringAttr::get(getContext(), "tfg.QueueDequeueManyV2");
queue_dequeue_ = StringAttr::get(getContext(), "tfg.QueueDequeue");
queue_dequeue_upto_ = StringAttr::get(getContext(), "tfg.QueueDequeueUpTo");
queue_dequeue_upto_v2_ =
    StringAttr::get(getContext(), "tfg.QueueDequeueUpToV2");
queue_dequeue_v2_ = StringAttr::get(getContext(), "tfg.QueueDequeueV2");
random_shuffle_ = StringAttr::get(getContext(), "tfg.RandomShuffle");
random_shuffle_queue_v2_ =
    StringAttr::get(getContext(), "tfg.RandomShuffleQueueV2");
rank_ = StringAttr::get(getContext(), "tfg.Rank");
read_variable_op_ = StringAttr::get(getContext(), "tfg.ReadVariableOp");
read_variables_op_ = StringAttr::get(getContext(), "tfg._ReadVariablesOp");
real_div_ = StringAttr::get(getContext(), "tfg.RealDiv");
real_ = StringAttr::get(getContext(), "tfg.Real");
reciprocal_grad_ = StringAttr::get(getContext(), "tfg.ReciprocalGrad");
recv_ = StringAttr::get(getContext(), "tfg._Recv");
ref_enter_ = StringAttr::get(getContext(), "tfg.RefEnter");
ref_exit_ = StringAttr::get(getContext(), "tfg.RefExit");
ref_identity_ = StringAttr::get(getContext(), "tfg.RefIdentity");
ref_merge_ = StringAttr::get(getContext(), "tfg.RefMerge");
ref_next_iteration_ = StringAttr::get(getContext(), "tfg.RefNextIteration");
ref_switch_ = StringAttr::get(getContext(), "tfg.RefSwitch");
relu6_grad_ = StringAttr::get(getContext(), "tfg.Relu6Grad");
relu6_ = StringAttr::get(getContext(), "tfg.Relu6");
relu_grad_ = StringAttr::get(getContext(), "tfg.ReluGrad");
relu_ = StringAttr::get(getContext(), "tfg.Relu");
reshape_ = StringAttr::get(getContext(), "tfg.Reshape");
resource_gather_ = StringAttr::get(getContext(), "tfg.ResourceGather");
restore_ = StringAttr::get(getContext(), "tfg.Restore");
restore_slice_ = StringAttr::get(getContext(), "tfg.RestoreSlice");
restore_v2_ = StringAttr::get(getContext(), "tfg.RestoreV2");
return_ = StringAttr::get(getContext(), "tfg.return");
retval_ = StringAttr::get(getContext(), "tfg._Retval");
reverse_ = StringAttr::get(getContext(), "tfg.Reverse");
reverse_v2_ = StringAttr::get(getContext(), "tfg.ReverseV2");
rsqrt_grad_ = StringAttr::get(getContext(), "tfg.RsqrtGrad");
rsqrt_ = StringAttr::get(getContext(), "tfg.Rsqrt");
segment_max_ = StringAttr::get(getContext(), "tfg.SegmentMax");
segment_min_ = StringAttr::get(getContext(), "tfg.SegmentMin");
select_ = StringAttr::get(getContext(), "tfg.Select");
select_v2_ = StringAttr::get(getContext(), "tfg.SelectV2");
selu_grad_ = StringAttr::get(getContext(), "tfg.SeluGrad");
send_ = StringAttr::get(getContext(), "tfg._Send");
shape_n_ = StringAttr::get(getContext(), "tfg.ShapeN");
shape_ = StringAttr::get(getContext(), "tfg.Shape");
shuffle_ = StringAttr::get(getContext(), "tfg.Shuffle");
sigmoid_grad_ = StringAttr::get(getContext(), "tfg.SigmoidGrad");
sigmoid_ = StringAttr::get(getContext(), "tfg.Sigmoid");
size_ = StringAttr::get(getContext(), "tfg.Size");
slice_ = StringAttr::get(getContext(), "tfg.Slice");
snapshot_ = StringAttr::get(getContext(), "tfg.Snapshot");
softmax_ = StringAttr::get(getContext(), "tfg.Softmax");
softplus_ = StringAttr::get(getContext(), "tfg.Softplus");
softplus_grad_ = StringAttr::get(getContext(), "tfg.SoftplusGrad");
softsign_grad_ = StringAttr::get(getContext(), "tfg.SoftsignGrad");
sparse_matmul_ = StringAttr::get(getContext(), "tfg.SparseMatMul");
sparse_segment_mean_ = StringAttr::get(getContext(), "tfg.SparseSegmentMean");
sparse_segment_mean_with_num_segments_ =
    StringAttr::get(getContext(), "tfg.SparseSegmentMeanWithNumSegments");
sparse_segment_sqrtn_ = StringAttr::get(getContext(), "tfg.SparseSegmentSqrtN");
sparse_segment_sqrtn_with_num_segments_ =
    StringAttr::get(getContext(), "tfg.SparseSegmentSqrtNWithNumSegments");
sparse_segment_sum_ = StringAttr::get(getContext(), "tfg.SparseSegmentSum");
sparse_segment_sum_with_num_segments_ =
    StringAttr::get(getContext(), "tfg.SparseSegmentSumWithNumSegments");
split_ = StringAttr::get(getContext(), "tfg.Split");
split_v_ = StringAttr::get(getContext(), "tfg.SplitV");
sqrt_grad_ = StringAttr::get(getContext(), "tfg.SqrtGrad");
sqrt_ = StringAttr::get(getContext(), "tfg.Sqrt");
squared_difference_ = StringAttr::get(getContext(), "tfg.SquaredDifference");
square_ = StringAttr::get(getContext(), "tfg.Square");
squeeze_ = StringAttr::get(getContext(), "tfg.Squeeze");
stack_close_ = StringAttr::get(getContext(), "tfg.StackClose");
stack_close_v2_ = StringAttr::get(getContext(), "tfg.StackCloseV2");
stack_pop_ = StringAttr::get(getContext(), "tfg.StackPop");
stack_pop_v2_ = StringAttr::get(getContext(), "tfg.StackPopV2");
stack_push_ = StringAttr::get(getContext(), "tfg.StackPush");
stack_push_v2_ = StringAttr::get(getContext(), "tfg.StackPushV2");
stack_ = StringAttr::get(getContext(), "tfg.Stack");
stack_v2_ = StringAttr::get(getContext(), "tfg.StackV2");
stateful_partitioned_call_ =
    StringAttr::get(getContext(), "tfg.StatefulPartitionedCall");
stateless_if_ = StringAttr::get(getContext(), "tfg.StatelessIf");
stateless_while_ = StringAttr::get(getContext(), "tfg.StatelessWhile");
stop_gradient_ = StringAttr::get(getContext(), "tfg.StopGradient");
strided_slice_grad_ = StringAttr::get(getContext(), "tfg.StridedSliceGrad");
strided_slice_ = StringAttr::get(getContext(), "tfg.StridedSlice");
string_to_hashbucket_fast_ =
    StringAttr::get(getContext(), "tfg.StringToHashBucketFast");
sub_ = StringAttr::get(getContext(), "tfg.Sub");
sum_ = StringAttr::get(getContext(), "tfg.Sum");
switch_n_ = StringAttr::get(getContext(), "tfg._SwitchN");
switch_ = StringAttr::get(getContext(), "tfg.Switch");
symbolic_gradient_ = StringAttr::get(getContext(), "tfg.SymbolicGradient");
tanh_grad_ = StringAttr::get(getContext(), "tfg.TanhGrad");
tanh_ = StringAttr::get(getContext(), "tfg.Tanh");
tile_ = StringAttr::get(getContext(), "tfg.Tile");
transpose_ = StringAttr::get(getContext(), "tfg.Transpose");
truncate_div_ = StringAttr::get(getContext(), "tfg.TruncateDiv");
truncate_mod_ = StringAttr::get(getContext(), "tfg.TruncateMod");
unique_ = StringAttr::get(getContext(), "tfg.Unique");
unique_v2_ = StringAttr::get(getContext(), "tfg.UniqueV2");
unpack_ = StringAttr::get(getContext(), "tfg.Unpack");
unsorted_segment_max_ = StringAttr::get(getContext(), "tfg.UnsortedSegmentMax");
unsorted_segment_min_ = StringAttr::get(getContext(), "tfg.UnsortedSegmentMin");
var_handle_op_ = StringAttr::get(getContext(), "tfg.VarHandleOp");
var_handles_op_ = StringAttr::get(getContext(), "tfg._VarHandlesOp");
variable_v2_ = StringAttr::get(getContext(), "tfg.VariableV2");
variable_ = StringAttr::get(getContext(), "tfg.Variable");
while_ = StringAttr::get(getContext(), "tfg.While");
xdivy_ = StringAttr::get(getContext(), "tfg.Xdivy");
xla_merge_ = StringAttr::get(getContext(), "tfg._XlaMerge");
zeros_like_ = StringAttr::get(getContext(), "tfg.ZerosLike");
zeta_ = StringAttr::get(getContext(), "tfg.Zeta");
#endif  // GET_OP_NAME_DEFS