/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCL<PERSON>IMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION (WINAPI_PARTITION_DESKTOP)

#if defined (PNRP_USE_V1_API) && defined (PNRP_USE_V2_API)
#error either define PNRP_USE_V1_API, or PNRP_USE_V2_API
#endif

#if !defined (PNRP_USE_V1_API) && !defined (PNRP_USE_V2_API)
#if _WIN32_WINNT >= 0x0600
#define PNRP_USE_V2_API
#else
#define PNRP_USE_V1_API
#endif
#endif

#define PNRP_MAX_ENDPOINT_ADDRESSES (10)
#define PNRP_MAX_EXTENDED_PAYLOAD_BYTES (0x1000)

#define WSZ_SCOPE_GLOBAL L"GLOBAL"
#define WSZ_SCOPE_SITELOCAL L"SITELOCAL"
#define WSZ_SCOPE_LINKLOCAL L"LINKLOCAL"

typedef enum _PNRP_SCOPE {
  PNRP_SCOPE_ANY = 0,
  PNRP_GLOBAL_SCOPE = 1,
  PNRP_SITE_LOCAL_SCOPE = 2,
  PNRP_LINK_LOCAL_SCOPE = 3
} PNRP_SCOPE,*PPNRP_SCOPE;

typedef enum _PNRP_CLOUD_STATE {
  PNRP_CLOUD_STATE_VIRTUAL = 0,
  PNRP_CLOUD_STATE_SYNCHRONISING = 1,
  PNRP_CLOUD_STATE_ACTIVE = 2,
  PNRP_CLOUD_STATE_DEAD = 3,
  PNRP_CLOUD_STATE_DISABLED = 4,
  PNRP_CLOUD_STATE_NO_NET = 5,
  PNRP_CLOUD_STATE_ALONE = 6
} PNRP_CLOUD_STATE;

typedef enum _PNRP_CLOUD_FLAGS {
  PNRP_CLOUD_NO_FLAGS = 0,
  PNRP_CLOUD_NAME_LOCAL = 1,
  PNRP_CLOUD_RESOLVE_ONLY = 2,
  PNRP_CLOUD_FULL_PARTICIPANT = 4
} PNRP_CLOUD_FLAGS;

typedef enum _PNRP_REGISTERED_ID_STATE {
  PNRP_REGISTERED_ID_STATE_OK = 1,
  PNRP_REGISTERED_ID_STATE_PROBLEM = 2
} PNRP_REGISTERED_ID_STATE;

typedef enum _PNRP_RESOLVE_CRITERIA {
  PNRP_RESOLVE_CRITERIA_DEFAULT = 0,
  PNRP_RESOLVE_CRITERIA_REMOTE_PEER_NAME = 1,
  PNRP_RESOLVE_CRITERIA_NEAREST_REMOTE_PEER_NAME = 2,
  PNRP_RESOLVE_CRITERIA_NON_CURRENT_PROCESS_PEER_NAME = 3,
  PNRP_RESOLVE_CRITERIA_NEAREST_NON_CURRENT_PROCESS_PEER_NAME = 4,
  PNRP_RESOLVE_CRITERIA_ANY_PEER_NAME = 5,
  PNRP_RESOLVE_CRITERIA_NEAREST_PEER_NAME = 6
} PNRP_RESOLVE_CRITERIA;

typedef struct _PNRP_CLOUD_ID {
  INT AddressFamily;
  PNRP_SCOPE Scope;
  ULONG ScopeId;
} PNRP_CLOUD_ID,*PPNRP_CLOUD_ID;

typedef enum _PNRP_EXTENDED_PAYLOAD_TYPE {
  PNRP_EXTENDED_PAYLOAD_TYPE_NONE = 0,
  PNRP_EXTENDED_PAYLOAD_TYPE_BINARY,
  PNRP_EXTENDED_PAYLOAD_TYPE_STRING
} PNRP_EXTENDED_PAYLOAD_TYPE,*PPNRP_EXTENDED_PAYLOAD_TYPE;

#endif
