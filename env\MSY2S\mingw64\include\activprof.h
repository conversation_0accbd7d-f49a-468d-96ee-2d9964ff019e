/*** Autogenerated by WIDL 10.8 from include/activprof.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __activprof_h__
#define __activprof_h__

/* Forward declarations */

#ifndef __IActiveScriptProfilerControl_FWD_DEFINED__
#define __IActiveScriptProfilerControl_FWD_DEFINED__
typedef interface IActiveScriptProfilerControl IActiveScriptProfilerControl;
#ifdef __cplusplus
interface IActiveScriptProfilerControl;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptProfilerControl2_FWD_DEFINED__
#define __IActiveScriptProfilerControl2_FWD_DEFINED__
typedef interface IActiveScriptProfilerControl2 IActiveScriptProfilerControl2;
#ifdef __cplusplus
interface IActiveScriptProfilerControl2;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptProfilerHeapEnum_FWD_DEFINED__
#define __IActiveScriptProfilerHeapEnum_FWD_DEFINED__
typedef interface IActiveScriptProfilerHeapEnum IActiveScriptProfilerHeapEnum;
#ifdef __cplusplus
interface IActiveScriptProfilerHeapEnum;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptProfilerControl3_FWD_DEFINED__
#define __IActiveScriptProfilerControl3_FWD_DEFINED__
typedef interface IActiveScriptProfilerControl3 IActiveScriptProfilerControl3;
#ifdef __cplusplus
interface IActiveScriptProfilerControl3;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptProfilerCallback_FWD_DEFINED__
#define __IActiveScriptProfilerCallback_FWD_DEFINED__
typedef interface IActiveScriptProfilerCallback IActiveScriptProfilerCallback;
#ifdef __cplusplus
interface IActiveScriptProfilerCallback;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptProfilerCallback2_FWD_DEFINED__
#define __IActiveScriptProfilerCallback2_FWD_DEFINED__
typedef interface IActiveScriptProfilerCallback2 IActiveScriptProfilerCallback2;
#ifdef __cplusplus
interface IActiveScriptProfilerCallback2;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptProfilerCallback3_FWD_DEFINED__
#define __IActiveScriptProfilerCallback3_FWD_DEFINED__
typedef interface IActiveScriptProfilerCallback3 IActiveScriptProfilerCallback3;
#ifdef __cplusplus
interface IActiveScriptProfilerCallback3;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

const HRESULT ACTIVPROF_E_PROFILER_PRESENT = MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0200);
const HRESULT ACTIVPROF_E_PROFILER_ABSENT = MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0201);
const HRESULT ACTIVPROF_E_UNABLE_TO_APPLY_ACTION = MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0x0202);
const ULONG PROFILER_HEAP_OBJECT_NAME_ID_UNAVAILABLE=(ULONG)-1;

#ifndef __ActivProf_h
#define __ActivProf_h


#ifndef _NO_SCRIPT_GUIDS
DEFINE_GUID(IID_IActiveScriptProfilerHeapEnum, 0x32e4694e, 0xd37, 0x419b, 0xb9, 0x3d, 0xfa, 0x20, 0xde, 0xd6, 0xe8, 0xea);
DEFINE_GUID(IID_IActiveScriptProfilerControl3, 0xb403015, 0xf381, 0x4023, 0xa5, 0xd0, 0x6f, 0xed, 0x7, 0x6d, 0xe7, 0x16);
#endif

typedef enum __WIDL_activprof_generated_name_0000000C {
    PROFILER_SCRIPT_TYPE_USER = 0,
    PROFILER_SCRIPT_TYPE_DYNAMIC = 1,
    PROFILER_SCRIPT_TYPE_NATIVE = 2,
    PROFILER_SCRIPT_TYPE_DOM = 3
} PROFILER_SCRIPT_TYPE;

typedef enum __WIDL_activprof_generated_name_0000000D {
    PROFILER_EVENT_MASK_TRACE_SCRIPT_FUNCTION_CALL = 0x1,
    PROFILER_EVENT_MASK_TRACE_NATIVE_FUNCTION_CALL = 0x2,
    PROFILER_EVENT_MASK_TRACE_DOM_FUNCTION_CALL = 0x4,
    PROFILER_EVENT_MASK_TRACE_ALL = PROFILER_EVENT_MASK_TRACE_SCRIPT_FUNCTION_CALL | PROFILER_EVENT_MASK_TRACE_NATIVE_FUNCTION_CALL,
    PROFILER_EVENT_MASK_TRACE_ALL_WITH_DOM = PROFILER_EVENT_MASK_TRACE_ALL | PROFILER_EVENT_MASK_TRACE_DOM_FUNCTION_CALL
} PROFILER_EVENT_MASK;

typedef LONG PROFILER_TOKEN;

/*****************************************************************************
 * IActiveScriptProfilerControl interface
 */
#ifndef __IActiveScriptProfilerControl_INTERFACE_DEFINED__
#define __IActiveScriptProfilerControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptProfilerControl, 0x784b5ff0, 0x69b0, 0x47d1, 0xa7,0xdc, 0x25,0x18,0xf4,0x23,0x0e,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("784b5ff0-69b0-47d1-a7dc-2518f4230e90")
IActiveScriptProfilerControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StartProfiling(
        REFCLSID clsidProfilerObject,
        DWORD dwEventMask,
        DWORD dwContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProfilerEventMask(
        DWORD dwEventMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopProfiling(
        HRESULT hrShutdownReason) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptProfilerControl, 0x784b5ff0, 0x69b0, 0x47d1, 0xa7,0xdc, 0x25,0x18,0xf4,0x23,0x0e,0x90)
#endif
#else
typedef struct IActiveScriptProfilerControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptProfilerControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptProfilerControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptProfilerControl *This);

    /*** IActiveScriptProfilerControl methods ***/
    HRESULT (STDMETHODCALLTYPE *StartProfiling)(
        IActiveScriptProfilerControl *This,
        REFCLSID clsidProfilerObject,
        DWORD dwEventMask,
        DWORD dwContext);

    HRESULT (STDMETHODCALLTYPE *SetProfilerEventMask)(
        IActiveScriptProfilerControl *This,
        DWORD dwEventMask);

    HRESULT (STDMETHODCALLTYPE *StopProfiling)(
        IActiveScriptProfilerControl *This,
        HRESULT hrShutdownReason);

    END_INTERFACE
} IActiveScriptProfilerControlVtbl;

interface IActiveScriptProfilerControl {
    CONST_VTBL IActiveScriptProfilerControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptProfilerControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptProfilerControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptProfilerControl_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptProfilerControl methods ***/
#define IActiveScriptProfilerControl_StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext) (This)->lpVtbl->StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext)
#define IActiveScriptProfilerControl_SetProfilerEventMask(This,dwEventMask) (This)->lpVtbl->SetProfilerEventMask(This,dwEventMask)
#define IActiveScriptProfilerControl_StopProfiling(This,hrShutdownReason) (This)->lpVtbl->StopProfiling(This,hrShutdownReason)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptProfilerControl_QueryInterface(IActiveScriptProfilerControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptProfilerControl_AddRef(IActiveScriptProfilerControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptProfilerControl_Release(IActiveScriptProfilerControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptProfilerControl methods ***/
static inline HRESULT IActiveScriptProfilerControl_StartProfiling(IActiveScriptProfilerControl* This,REFCLSID clsidProfilerObject,DWORD dwEventMask,DWORD dwContext) {
    return This->lpVtbl->StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext);
}
static inline HRESULT IActiveScriptProfilerControl_SetProfilerEventMask(IActiveScriptProfilerControl* This,DWORD dwEventMask) {
    return This->lpVtbl->SetProfilerEventMask(This,dwEventMask);
}
static inline HRESULT IActiveScriptProfilerControl_StopProfiling(IActiveScriptProfilerControl* This,HRESULT hrShutdownReason) {
    return This->lpVtbl->StopProfiling(This,hrShutdownReason);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptProfilerControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptProfilerControl2 interface
 */
#ifndef __IActiveScriptProfilerControl2_INTERFACE_DEFINED__
#define __IActiveScriptProfilerControl2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptProfilerControl2, 0x47810165, 0x498f, 0x40be, 0x94,0xf1, 0x65,0x35,0x57,0xe9,0xe7,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("47810165-498f-40be-94f1-653557e9e7da")
IActiveScriptProfilerControl2 : public IActiveScriptProfilerControl
{
    virtual HRESULT STDMETHODCALLTYPE CompleteProfilerStart(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PrepareProfilerStop(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptProfilerControl2, 0x47810165, 0x498f, 0x40be, 0x94,0xf1, 0x65,0x35,0x57,0xe9,0xe7,0xda)
#endif
#else
typedef struct IActiveScriptProfilerControl2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptProfilerControl2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptProfilerControl2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptProfilerControl2 *This);

    /*** IActiveScriptProfilerControl methods ***/
    HRESULT (STDMETHODCALLTYPE *StartProfiling)(
        IActiveScriptProfilerControl2 *This,
        REFCLSID clsidProfilerObject,
        DWORD dwEventMask,
        DWORD dwContext);

    HRESULT (STDMETHODCALLTYPE *SetProfilerEventMask)(
        IActiveScriptProfilerControl2 *This,
        DWORD dwEventMask);

    HRESULT (STDMETHODCALLTYPE *StopProfiling)(
        IActiveScriptProfilerControl2 *This,
        HRESULT hrShutdownReason);

    /*** IActiveScriptProfilerControl2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CompleteProfilerStart)(
        IActiveScriptProfilerControl2 *This);

    HRESULT (STDMETHODCALLTYPE *PrepareProfilerStop)(
        IActiveScriptProfilerControl2 *This);

    END_INTERFACE
} IActiveScriptProfilerControl2Vtbl;

interface IActiveScriptProfilerControl2 {
    CONST_VTBL IActiveScriptProfilerControl2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptProfilerControl2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptProfilerControl2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptProfilerControl2_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptProfilerControl methods ***/
#define IActiveScriptProfilerControl2_StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext) (This)->lpVtbl->StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext)
#define IActiveScriptProfilerControl2_SetProfilerEventMask(This,dwEventMask) (This)->lpVtbl->SetProfilerEventMask(This,dwEventMask)
#define IActiveScriptProfilerControl2_StopProfiling(This,hrShutdownReason) (This)->lpVtbl->StopProfiling(This,hrShutdownReason)
/*** IActiveScriptProfilerControl2 methods ***/
#define IActiveScriptProfilerControl2_CompleteProfilerStart(This) (This)->lpVtbl->CompleteProfilerStart(This)
#define IActiveScriptProfilerControl2_PrepareProfilerStop(This) (This)->lpVtbl->PrepareProfilerStop(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptProfilerControl2_QueryInterface(IActiveScriptProfilerControl2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptProfilerControl2_AddRef(IActiveScriptProfilerControl2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptProfilerControl2_Release(IActiveScriptProfilerControl2* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptProfilerControl methods ***/
static inline HRESULT IActiveScriptProfilerControl2_StartProfiling(IActiveScriptProfilerControl2* This,REFCLSID clsidProfilerObject,DWORD dwEventMask,DWORD dwContext) {
    return This->lpVtbl->StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext);
}
static inline HRESULT IActiveScriptProfilerControl2_SetProfilerEventMask(IActiveScriptProfilerControl2* This,DWORD dwEventMask) {
    return This->lpVtbl->SetProfilerEventMask(This,dwEventMask);
}
static inline HRESULT IActiveScriptProfilerControl2_StopProfiling(IActiveScriptProfilerControl2* This,HRESULT hrShutdownReason) {
    return This->lpVtbl->StopProfiling(This,hrShutdownReason);
}
/*** IActiveScriptProfilerControl2 methods ***/
static inline HRESULT IActiveScriptProfilerControl2_CompleteProfilerStart(IActiveScriptProfilerControl2* This) {
    return This->lpVtbl->CompleteProfilerStart(This);
}
static inline HRESULT IActiveScriptProfilerControl2_PrepareProfilerStop(IActiveScriptProfilerControl2* This) {
    return This->lpVtbl->PrepareProfilerStop(This);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptProfilerControl2_INTERFACE_DEFINED__ */


typedef DWORD_PTR PROFILER_HEAP_OBJECT_ID;
typedef UINT PROFILER_HEAP_OBJECT_NAME_ID;
typedef void *PROFILER_EXTERNAL_OBJECT_ADDRESS;

typedef enum __WIDL_activprof_generated_name_0000000E {
    PROFILER_HEAP_OBJECT_FLAGS_NEW_OBJECT = 0x1,
    PROFILER_HEAP_OBJECT_FLAGS_IS_ROOT = 0x2,
    PROFILER_HEAP_OBJECT_FLAGS_SITE_CLOSED = 0x4,
    PROFILER_HEAP_OBJECT_FLAGS_EXTERNAL = 0x8,
    PROFILER_HEAP_OBJECT_FLAGS_EXTERNAL_UNKNOWN = 0x10,
    PROFILER_HEAP_OBJECT_FLAGS_EXTERNAL_DISPATCH = 0x20,
    PROFILER_HEAP_OBJECT_FLAGS_SIZE_APPROXIMATE = 0x40,
    PROFILER_HEAP_OBJECT_FLAGS_SIZE_UNAVAILABLE = 0x80,
    PROFILER_HEAP_OBJECT_FLAGS_NEW_STATE_UNAVAILABLE = 0x100,
    PROFILER_HEAP_OBJECT_FLAGS_WINRT_INSTANCE = 0x200,
    PROFILER_HEAP_OBJECT_FLAGS_WINRT_RUNTIMECLASS = 0x400,
    PROFILER_HEAP_OBJECT_FLAGS_WINRT_DELEGATE = 0x800,
    PROFILER_HEAP_OBJECT_FLAGS_WINRT_NAMESPACE = 0x1000
} PROFILER_HEAP_OBJECT_FLAGS;

typedef enum __WIDL_activprof_generated_name_0000000F {
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_PROTOTYPE = 0x1,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_FUNCTION_NAME = 0x2,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_SCOPE_LIST = 0x3,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_INTERNAL_PROPERTY = 0x4,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_NAME_PROPERTIES = 0x5,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_INDEX_PROPERTIES = 0x6,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_ELEMENT_ATTRIBUTES_SIZE = 0x7,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_ELEMENT_TEXT_CHILDREN_SIZE = 0x8,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_RELATIONSHIPS = 0x9,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_WINRTEVENTS = 0xa,
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_MAX_VALUE = PROFILER_HEAP_OBJECT_OPTIONAL_INFO_WINRTEVENTS
} PROFILER_HEAP_OBJECT_OPTIONAL_INFO_TYPE;

typedef struct _PROFILER_HEAP_OBJECT_SCOPE_LIST {
    UINT count;
    PROFILER_HEAP_OBJECT_ID scopes[1];
} PROFILER_HEAP_OBJECT_SCOPE_LIST;

typedef enum __WIDL_activprof_generated_name_00000010 {
    PROFILER_PROPERTY_TYPE_NUMBER = 0x1,
    PROFILER_PROPERTY_TYPE_STRING = 0x2,
    PROFILER_PROPERTY_TYPE_HEAP_OBJECT = 0x3,
    PROFILER_PROPERTY_TYPE_EXTERNAL_OBJECT = 0x4,
    PROFILER_PROPERTY_TYPE_BSTR = 0x5
} PROFILER_RELATIONSHIP_INFO;

typedef struct _PROFILER_HEAP_OBJECT_RELATIONSHIP {
    PROFILER_HEAP_OBJECT_NAME_ID relationshipId;
    PROFILER_RELATIONSHIP_INFO relationshipInfo;
    __C89_NAMELESS union {
        double numberValue;
        LPCWSTR stringValue;
        BSTR bstrValue;
        PROFILER_HEAP_OBJECT_ID objectId;
        PROFILER_EXTERNAL_OBJECT_ADDRESS externalObjectAddress;
    } __C89_NAMELESSUNIONNAME;
} PROFILER_HEAP_OBJECT_RELATIONSHIP;

typedef struct _PROFILER_HEAP_OBJECT_RELATIONSHIP_LIST {
    UINT count;
    PROFILER_HEAP_OBJECT_RELATIONSHIP elements[1];
} PROFILER_HEAP_OBJECT_RELATIONSHIP_LIST;

typedef struct _PROFILER_HEAP_OBJECT_OPTIONAL_INFO {
    PROFILER_HEAP_OBJECT_OPTIONAL_INFO_TYPE infoType;
    __C89_NAMELESS union {
        PROFILER_HEAP_OBJECT_ID prototype;
        LPCWSTR functionName;
        UINT elementAttributesSize;
        UINT elementTextChildrenSize;
        PROFILER_HEAP_OBJECT_SCOPE_LIST *scopeList;
        PROFILER_HEAP_OBJECT_RELATIONSHIP *internalProperty;
        PROFILER_HEAP_OBJECT_RELATIONSHIP_LIST *namePropertyList;
        PROFILER_HEAP_OBJECT_RELATIONSHIP_LIST *indexPropertyList;
        PROFILER_HEAP_OBJECT_RELATIONSHIP_LIST *relationshipList;
        PROFILER_HEAP_OBJECT_RELATIONSHIP_LIST *eventList;
    } __C89_NAMELESSUNIONNAME;
} PROFILER_HEAP_OBJECT_OPTIONAL_INFO;

typedef struct _PROFILER_HEAP_OBJECT {
    UINT size;
    __C89_NAMELESS union {
        PROFILER_HEAP_OBJECT_ID objectId;
        PROFILER_EXTERNAL_OBJECT_ADDRESS externalObjectAddress;
    } __C89_NAMELESSUNIONNAME;
    PROFILER_HEAP_OBJECT_NAME_ID typeNameId;
    ULONG flags;
    USHORT unused;
    USHORT optionalInfoCount;
} PROFILER_HEAP_OBJECT;

/*****************************************************************************
 * IActiveScriptProfilerHeapEnum interface
 */
#ifndef __IActiveScriptProfilerHeapEnum_INTERFACE_DEFINED__
#define __IActiveScriptProfilerHeapEnum_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptProfilerHeapEnum, 0x32e4694e, 0x0d37, 0x419b, 0xb9,0x3d, 0xfa,0x20,0xde,0xd6,0xe8,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("32e4694e-0d37-419b-b93d-fa20ded6e8ea")
IActiveScriptProfilerHeapEnum : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        PROFILER_HEAP_OBJECT **heapObjects,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOptionalInfo(
        PROFILER_HEAP_OBJECT *heapObject,
        ULONG celt,
        PROFILER_HEAP_OBJECT_OPTIONAL_INFO *optionalInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeObjectAndOptionalInfo(
        ULONG celt,
        PROFILER_HEAP_OBJECT **heapObjects) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNameIdMap(
        LPCWSTR * pNameList[],
        UINT *pcelt) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptProfilerHeapEnum, 0x32e4694e, 0x0d37, 0x419b, 0xb9,0x3d, 0xfa,0x20,0xde,0xd6,0xe8,0xea)
#endif
#else
typedef struct IActiveScriptProfilerHeapEnumVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptProfilerHeapEnum *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptProfilerHeapEnum *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptProfilerHeapEnum *This);

    /*** IActiveScriptProfilerHeapEnum methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IActiveScriptProfilerHeapEnum *This,
        ULONG celt,
        PROFILER_HEAP_OBJECT **heapObjects,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *GetOptionalInfo)(
        IActiveScriptProfilerHeapEnum *This,
        PROFILER_HEAP_OBJECT *heapObject,
        ULONG celt,
        PROFILER_HEAP_OBJECT_OPTIONAL_INFO *optionalInfo);

    HRESULT (STDMETHODCALLTYPE *FreeObjectAndOptionalInfo)(
        IActiveScriptProfilerHeapEnum *This,
        ULONG celt,
        PROFILER_HEAP_OBJECT **heapObjects);

    HRESULT (STDMETHODCALLTYPE *GetNameIdMap)(
        IActiveScriptProfilerHeapEnum *This,
        LPCWSTR * pNameList[],
        UINT *pcelt);

    END_INTERFACE
} IActiveScriptProfilerHeapEnumVtbl;

interface IActiveScriptProfilerHeapEnum {
    CONST_VTBL IActiveScriptProfilerHeapEnumVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptProfilerHeapEnum_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptProfilerHeapEnum_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptProfilerHeapEnum_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptProfilerHeapEnum methods ***/
#define IActiveScriptProfilerHeapEnum_Next(This,celt,heapObjects,pceltFetched) (This)->lpVtbl->Next(This,celt,heapObjects,pceltFetched)
#define IActiveScriptProfilerHeapEnum_GetOptionalInfo(This,heapObject,celt,optionalInfo) (This)->lpVtbl->GetOptionalInfo(This,heapObject,celt,optionalInfo)
#define IActiveScriptProfilerHeapEnum_FreeObjectAndOptionalInfo(This,celt,heapObjects) (This)->lpVtbl->FreeObjectAndOptionalInfo(This,celt,heapObjects)
#define IActiveScriptProfilerHeapEnum_GetNameIdMap(This,pNameList,pcelt) (This)->lpVtbl->GetNameIdMap(This,pNameList,pcelt)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptProfilerHeapEnum_QueryInterface(IActiveScriptProfilerHeapEnum* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptProfilerHeapEnum_AddRef(IActiveScriptProfilerHeapEnum* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptProfilerHeapEnum_Release(IActiveScriptProfilerHeapEnum* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptProfilerHeapEnum methods ***/
static inline HRESULT IActiveScriptProfilerHeapEnum_Next(IActiveScriptProfilerHeapEnum* This,ULONG celt,PROFILER_HEAP_OBJECT **heapObjects,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,heapObjects,pceltFetched);
}
static inline HRESULT IActiveScriptProfilerHeapEnum_GetOptionalInfo(IActiveScriptProfilerHeapEnum* This,PROFILER_HEAP_OBJECT *heapObject,ULONG celt,PROFILER_HEAP_OBJECT_OPTIONAL_INFO *optionalInfo) {
    return This->lpVtbl->GetOptionalInfo(This,heapObject,celt,optionalInfo);
}
static inline HRESULT IActiveScriptProfilerHeapEnum_FreeObjectAndOptionalInfo(IActiveScriptProfilerHeapEnum* This,ULONG celt,PROFILER_HEAP_OBJECT **heapObjects) {
    return This->lpVtbl->FreeObjectAndOptionalInfo(This,celt,heapObjects);
}
static inline HRESULT IActiveScriptProfilerHeapEnum_GetNameIdMap(IActiveScriptProfilerHeapEnum* This,LPCWSTR * pNameList[],UINT *pcelt) {
    return This->lpVtbl->GetNameIdMap(This,pNameList,pcelt);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptProfilerHeapEnum_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptProfilerControl3 interface
 */
#ifndef __IActiveScriptProfilerControl3_INTERFACE_DEFINED__
#define __IActiveScriptProfilerControl3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptProfilerControl3, 0x0b403015, 0xf381, 0x4023, 0xa5,0xd0, 0x6f,0xed,0x07,0x6d,0xe7,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0b403015-f381-4023-a5d0-6fed076de716")
IActiveScriptProfilerControl3 : public IActiveScriptProfilerControl2
{
    virtual HRESULT STDMETHODCALLTYPE EnumHeap(
        IActiveScriptProfilerHeapEnum **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptProfilerControl3, 0x0b403015, 0xf381, 0x4023, 0xa5,0xd0, 0x6f,0xed,0x07,0x6d,0xe7,0x16)
#endif
#else
typedef struct IActiveScriptProfilerControl3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptProfilerControl3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptProfilerControl3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptProfilerControl3 *This);

    /*** IActiveScriptProfilerControl methods ***/
    HRESULT (STDMETHODCALLTYPE *StartProfiling)(
        IActiveScriptProfilerControl3 *This,
        REFCLSID clsidProfilerObject,
        DWORD dwEventMask,
        DWORD dwContext);

    HRESULT (STDMETHODCALLTYPE *SetProfilerEventMask)(
        IActiveScriptProfilerControl3 *This,
        DWORD dwEventMask);

    HRESULT (STDMETHODCALLTYPE *StopProfiling)(
        IActiveScriptProfilerControl3 *This,
        HRESULT hrShutdownReason);

    /*** IActiveScriptProfilerControl2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CompleteProfilerStart)(
        IActiveScriptProfilerControl3 *This);

    HRESULT (STDMETHODCALLTYPE *PrepareProfilerStop)(
        IActiveScriptProfilerControl3 *This);

    /*** IActiveScriptProfilerControl3 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumHeap)(
        IActiveScriptProfilerControl3 *This,
        IActiveScriptProfilerHeapEnum **ppEnum);

    END_INTERFACE
} IActiveScriptProfilerControl3Vtbl;

interface IActiveScriptProfilerControl3 {
    CONST_VTBL IActiveScriptProfilerControl3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptProfilerControl3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptProfilerControl3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptProfilerControl3_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptProfilerControl methods ***/
#define IActiveScriptProfilerControl3_StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext) (This)->lpVtbl->StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext)
#define IActiveScriptProfilerControl3_SetProfilerEventMask(This,dwEventMask) (This)->lpVtbl->SetProfilerEventMask(This,dwEventMask)
#define IActiveScriptProfilerControl3_StopProfiling(This,hrShutdownReason) (This)->lpVtbl->StopProfiling(This,hrShutdownReason)
/*** IActiveScriptProfilerControl2 methods ***/
#define IActiveScriptProfilerControl3_CompleteProfilerStart(This) (This)->lpVtbl->CompleteProfilerStart(This)
#define IActiveScriptProfilerControl3_PrepareProfilerStop(This) (This)->lpVtbl->PrepareProfilerStop(This)
/*** IActiveScriptProfilerControl3 methods ***/
#define IActiveScriptProfilerControl3_EnumHeap(This,ppEnum) (This)->lpVtbl->EnumHeap(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptProfilerControl3_QueryInterface(IActiveScriptProfilerControl3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptProfilerControl3_AddRef(IActiveScriptProfilerControl3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptProfilerControl3_Release(IActiveScriptProfilerControl3* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptProfilerControl methods ***/
static inline HRESULT IActiveScriptProfilerControl3_StartProfiling(IActiveScriptProfilerControl3* This,REFCLSID clsidProfilerObject,DWORD dwEventMask,DWORD dwContext) {
    return This->lpVtbl->StartProfiling(This,clsidProfilerObject,dwEventMask,dwContext);
}
static inline HRESULT IActiveScriptProfilerControl3_SetProfilerEventMask(IActiveScriptProfilerControl3* This,DWORD dwEventMask) {
    return This->lpVtbl->SetProfilerEventMask(This,dwEventMask);
}
static inline HRESULT IActiveScriptProfilerControl3_StopProfiling(IActiveScriptProfilerControl3* This,HRESULT hrShutdownReason) {
    return This->lpVtbl->StopProfiling(This,hrShutdownReason);
}
/*** IActiveScriptProfilerControl2 methods ***/
static inline HRESULT IActiveScriptProfilerControl3_CompleteProfilerStart(IActiveScriptProfilerControl3* This) {
    return This->lpVtbl->CompleteProfilerStart(This);
}
static inline HRESULT IActiveScriptProfilerControl3_PrepareProfilerStop(IActiveScriptProfilerControl3* This) {
    return This->lpVtbl->PrepareProfilerStop(This);
}
/*** IActiveScriptProfilerControl3 methods ***/
static inline HRESULT IActiveScriptProfilerControl3_EnumHeap(IActiveScriptProfilerControl3* This,IActiveScriptProfilerHeapEnum **ppEnum) {
    return This->lpVtbl->EnumHeap(This,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptProfilerControl3_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptProfilerCallback interface
 */
#ifndef __IActiveScriptProfilerCallback_INTERFACE_DEFINED__
#define __IActiveScriptProfilerCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptProfilerCallback, 0x740eca23, 0x7d9d, 0x42e5, 0xba,0x9d, 0xf8,0xb2,0x4b,0x1c,0x7a,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("740eca23-7d9d-42e5-ba9d-f8b24b1c7a9b")
IActiveScriptProfilerCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        DWORD dwContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        HRESULT hrReason) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScriptCompiled(
        PROFILER_TOKEN scriptId,
        PROFILER_SCRIPT_TYPE type,
        IUnknown *pIDebugDocumentContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE FunctionCompiled(
        PROFILER_TOKEN functionId,
        PROFILER_TOKEN scriptId,
        const WCHAR *pwszFunctionName,
        const WCHAR *pwszFunctionNameHint,
        IUnknown *pIDebugDocumentContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnFunctionEnter(
        PROFILER_TOKEN scriptId,
        PROFILER_TOKEN functionId) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnFunctionExit(
        PROFILER_TOKEN scriptId,
        PROFILER_TOKEN functionId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptProfilerCallback, 0x740eca23, 0x7d9d, 0x42e5, 0xba,0x9d, 0xf8,0xb2,0x4b,0x1c,0x7a,0x9b)
#endif
#else
typedef struct IActiveScriptProfilerCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptProfilerCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptProfilerCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptProfilerCallback *This);

    /*** IActiveScriptProfilerCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IActiveScriptProfilerCallback *This,
        DWORD dwContext);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IActiveScriptProfilerCallback *This,
        HRESULT hrReason);

    HRESULT (STDMETHODCALLTYPE *ScriptCompiled)(
        IActiveScriptProfilerCallback *This,
        PROFILER_TOKEN scriptId,
        PROFILER_SCRIPT_TYPE type,
        IUnknown *pIDebugDocumentContext);

    HRESULT (STDMETHODCALLTYPE *FunctionCompiled)(
        IActiveScriptProfilerCallback *This,
        PROFILER_TOKEN functionId,
        PROFILER_TOKEN scriptId,
        const WCHAR *pwszFunctionName,
        const WCHAR *pwszFunctionNameHint,
        IUnknown *pIDebugDocumentContext);

    HRESULT (STDMETHODCALLTYPE *OnFunctionEnter)(
        IActiveScriptProfilerCallback *This,
        PROFILER_TOKEN scriptId,
        PROFILER_TOKEN functionId);

    HRESULT (STDMETHODCALLTYPE *OnFunctionExit)(
        IActiveScriptProfilerCallback *This,
        PROFILER_TOKEN scriptId,
        PROFILER_TOKEN functionId);

    END_INTERFACE
} IActiveScriptProfilerCallbackVtbl;

interface IActiveScriptProfilerCallback {
    CONST_VTBL IActiveScriptProfilerCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptProfilerCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptProfilerCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptProfilerCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptProfilerCallback methods ***/
#define IActiveScriptProfilerCallback_Initialize(This,dwContext) (This)->lpVtbl->Initialize(This,dwContext)
#define IActiveScriptProfilerCallback_Shutdown(This,hrReason) (This)->lpVtbl->Shutdown(This,hrReason)
#define IActiveScriptProfilerCallback_ScriptCompiled(This,scriptId,type,pIDebugDocumentContext) (This)->lpVtbl->ScriptCompiled(This,scriptId,type,pIDebugDocumentContext)
#define IActiveScriptProfilerCallback_FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext) (This)->lpVtbl->FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext)
#define IActiveScriptProfilerCallback_OnFunctionEnter(This,scriptId,functionId) (This)->lpVtbl->OnFunctionEnter(This,scriptId,functionId)
#define IActiveScriptProfilerCallback_OnFunctionExit(This,scriptId,functionId) (This)->lpVtbl->OnFunctionExit(This,scriptId,functionId)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptProfilerCallback_QueryInterface(IActiveScriptProfilerCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptProfilerCallback_AddRef(IActiveScriptProfilerCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptProfilerCallback_Release(IActiveScriptProfilerCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptProfilerCallback methods ***/
static inline HRESULT IActiveScriptProfilerCallback_Initialize(IActiveScriptProfilerCallback* This,DWORD dwContext) {
    return This->lpVtbl->Initialize(This,dwContext);
}
static inline HRESULT IActiveScriptProfilerCallback_Shutdown(IActiveScriptProfilerCallback* This,HRESULT hrReason) {
    return This->lpVtbl->Shutdown(This,hrReason);
}
static inline HRESULT IActiveScriptProfilerCallback_ScriptCompiled(IActiveScriptProfilerCallback* This,PROFILER_TOKEN scriptId,PROFILER_SCRIPT_TYPE type,IUnknown *pIDebugDocumentContext) {
    return This->lpVtbl->ScriptCompiled(This,scriptId,type,pIDebugDocumentContext);
}
static inline HRESULT IActiveScriptProfilerCallback_FunctionCompiled(IActiveScriptProfilerCallback* This,PROFILER_TOKEN functionId,PROFILER_TOKEN scriptId,const WCHAR *pwszFunctionName,const WCHAR *pwszFunctionNameHint,IUnknown *pIDebugDocumentContext) {
    return This->lpVtbl->FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext);
}
static inline HRESULT IActiveScriptProfilerCallback_OnFunctionEnter(IActiveScriptProfilerCallback* This,PROFILER_TOKEN scriptId,PROFILER_TOKEN functionId) {
    return This->lpVtbl->OnFunctionEnter(This,scriptId,functionId);
}
static inline HRESULT IActiveScriptProfilerCallback_OnFunctionExit(IActiveScriptProfilerCallback* This,PROFILER_TOKEN scriptId,PROFILER_TOKEN functionId) {
    return This->lpVtbl->OnFunctionExit(This,scriptId,functionId);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptProfilerCallback_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptProfilerCallback2 interface
 */
#ifndef __IActiveScriptProfilerCallback2_INTERFACE_DEFINED__
#define __IActiveScriptProfilerCallback2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptProfilerCallback2, 0x31b7f8ad, 0xa637, 0x409c, 0xb2,0x2f, 0x04,0x09,0x95,0xb6,0x10,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("31b7f8ad-a637-409c-b22f-040995b6103d")
IActiveScriptProfilerCallback2 : public IActiveScriptProfilerCallback
{
    virtual HRESULT STDMETHODCALLTYPE OnFunctionEnterByName(
        const WCHAR *pwszFunctionName,
        PROFILER_SCRIPT_TYPE type) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnFunctionExitByName(
        const WCHAR *pwszFunctionName,
        PROFILER_SCRIPT_TYPE type) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptProfilerCallback2, 0x31b7f8ad, 0xa637, 0x409c, 0xb2,0x2f, 0x04,0x09,0x95,0xb6,0x10,0x3d)
#endif
#else
typedef struct IActiveScriptProfilerCallback2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptProfilerCallback2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptProfilerCallback2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptProfilerCallback2 *This);

    /*** IActiveScriptProfilerCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IActiveScriptProfilerCallback2 *This,
        DWORD dwContext);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IActiveScriptProfilerCallback2 *This,
        HRESULT hrReason);

    HRESULT (STDMETHODCALLTYPE *ScriptCompiled)(
        IActiveScriptProfilerCallback2 *This,
        PROFILER_TOKEN scriptId,
        PROFILER_SCRIPT_TYPE type,
        IUnknown *pIDebugDocumentContext);

    HRESULT (STDMETHODCALLTYPE *FunctionCompiled)(
        IActiveScriptProfilerCallback2 *This,
        PROFILER_TOKEN functionId,
        PROFILER_TOKEN scriptId,
        const WCHAR *pwszFunctionName,
        const WCHAR *pwszFunctionNameHint,
        IUnknown *pIDebugDocumentContext);

    HRESULT (STDMETHODCALLTYPE *OnFunctionEnter)(
        IActiveScriptProfilerCallback2 *This,
        PROFILER_TOKEN scriptId,
        PROFILER_TOKEN functionId);

    HRESULT (STDMETHODCALLTYPE *OnFunctionExit)(
        IActiveScriptProfilerCallback2 *This,
        PROFILER_TOKEN scriptId,
        PROFILER_TOKEN functionId);

    /*** IActiveScriptProfilerCallback2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnFunctionEnterByName)(
        IActiveScriptProfilerCallback2 *This,
        const WCHAR *pwszFunctionName,
        PROFILER_SCRIPT_TYPE type);

    HRESULT (STDMETHODCALLTYPE *OnFunctionExitByName)(
        IActiveScriptProfilerCallback2 *This,
        const WCHAR *pwszFunctionName,
        PROFILER_SCRIPT_TYPE type);

    END_INTERFACE
} IActiveScriptProfilerCallback2Vtbl;

interface IActiveScriptProfilerCallback2 {
    CONST_VTBL IActiveScriptProfilerCallback2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptProfilerCallback2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptProfilerCallback2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptProfilerCallback2_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptProfilerCallback methods ***/
#define IActiveScriptProfilerCallback2_Initialize(This,dwContext) (This)->lpVtbl->Initialize(This,dwContext)
#define IActiveScriptProfilerCallback2_Shutdown(This,hrReason) (This)->lpVtbl->Shutdown(This,hrReason)
#define IActiveScriptProfilerCallback2_ScriptCompiled(This,scriptId,type,pIDebugDocumentContext) (This)->lpVtbl->ScriptCompiled(This,scriptId,type,pIDebugDocumentContext)
#define IActiveScriptProfilerCallback2_FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext) (This)->lpVtbl->FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext)
#define IActiveScriptProfilerCallback2_OnFunctionEnter(This,scriptId,functionId) (This)->lpVtbl->OnFunctionEnter(This,scriptId,functionId)
#define IActiveScriptProfilerCallback2_OnFunctionExit(This,scriptId,functionId) (This)->lpVtbl->OnFunctionExit(This,scriptId,functionId)
/*** IActiveScriptProfilerCallback2 methods ***/
#define IActiveScriptProfilerCallback2_OnFunctionEnterByName(This,pwszFunctionName,type) (This)->lpVtbl->OnFunctionEnterByName(This,pwszFunctionName,type)
#define IActiveScriptProfilerCallback2_OnFunctionExitByName(This,pwszFunctionName,type) (This)->lpVtbl->OnFunctionExitByName(This,pwszFunctionName,type)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptProfilerCallback2_QueryInterface(IActiveScriptProfilerCallback2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptProfilerCallback2_AddRef(IActiveScriptProfilerCallback2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptProfilerCallback2_Release(IActiveScriptProfilerCallback2* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptProfilerCallback methods ***/
static inline HRESULT IActiveScriptProfilerCallback2_Initialize(IActiveScriptProfilerCallback2* This,DWORD dwContext) {
    return This->lpVtbl->Initialize(This,dwContext);
}
static inline HRESULT IActiveScriptProfilerCallback2_Shutdown(IActiveScriptProfilerCallback2* This,HRESULT hrReason) {
    return This->lpVtbl->Shutdown(This,hrReason);
}
static inline HRESULT IActiveScriptProfilerCallback2_ScriptCompiled(IActiveScriptProfilerCallback2* This,PROFILER_TOKEN scriptId,PROFILER_SCRIPT_TYPE type,IUnknown *pIDebugDocumentContext) {
    return This->lpVtbl->ScriptCompiled(This,scriptId,type,pIDebugDocumentContext);
}
static inline HRESULT IActiveScriptProfilerCallback2_FunctionCompiled(IActiveScriptProfilerCallback2* This,PROFILER_TOKEN functionId,PROFILER_TOKEN scriptId,const WCHAR *pwszFunctionName,const WCHAR *pwszFunctionNameHint,IUnknown *pIDebugDocumentContext) {
    return This->lpVtbl->FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext);
}
static inline HRESULT IActiveScriptProfilerCallback2_OnFunctionEnter(IActiveScriptProfilerCallback2* This,PROFILER_TOKEN scriptId,PROFILER_TOKEN functionId) {
    return This->lpVtbl->OnFunctionEnter(This,scriptId,functionId);
}
static inline HRESULT IActiveScriptProfilerCallback2_OnFunctionExit(IActiveScriptProfilerCallback2* This,PROFILER_TOKEN scriptId,PROFILER_TOKEN functionId) {
    return This->lpVtbl->OnFunctionExit(This,scriptId,functionId);
}
/*** IActiveScriptProfilerCallback2 methods ***/
static inline HRESULT IActiveScriptProfilerCallback2_OnFunctionEnterByName(IActiveScriptProfilerCallback2* This,const WCHAR *pwszFunctionName,PROFILER_SCRIPT_TYPE type) {
    return This->lpVtbl->OnFunctionEnterByName(This,pwszFunctionName,type);
}
static inline HRESULT IActiveScriptProfilerCallback2_OnFunctionExitByName(IActiveScriptProfilerCallback2* This,const WCHAR *pwszFunctionName,PROFILER_SCRIPT_TYPE type) {
    return This->lpVtbl->OnFunctionExitByName(This,pwszFunctionName,type);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptProfilerCallback2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptProfilerCallback3 interface
 */
#ifndef __IActiveScriptProfilerCallback3_INTERFACE_DEFINED__
#define __IActiveScriptProfilerCallback3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptProfilerCallback3, 0x6ac5ad25, 0x2037, 0x4687, 0x91,0xdf, 0xb5,0x99,0x79,0xd9,0x3d,0x73);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6ac5ad25-2037-4687-91df-b59979d93d73")
IActiveScriptProfilerCallback3 : public IActiveScriptProfilerCallback2
{
    virtual HRESULT STDMETHODCALLTYPE SetWebWorkerId(
        DWORD webWorkerId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptProfilerCallback3, 0x6ac5ad25, 0x2037, 0x4687, 0x91,0xdf, 0xb5,0x99,0x79,0xd9,0x3d,0x73)
#endif
#else
typedef struct IActiveScriptProfilerCallback3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptProfilerCallback3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptProfilerCallback3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptProfilerCallback3 *This);

    /*** IActiveScriptProfilerCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IActiveScriptProfilerCallback3 *This,
        DWORD dwContext);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IActiveScriptProfilerCallback3 *This,
        HRESULT hrReason);

    HRESULT (STDMETHODCALLTYPE *ScriptCompiled)(
        IActiveScriptProfilerCallback3 *This,
        PROFILER_TOKEN scriptId,
        PROFILER_SCRIPT_TYPE type,
        IUnknown *pIDebugDocumentContext);

    HRESULT (STDMETHODCALLTYPE *FunctionCompiled)(
        IActiveScriptProfilerCallback3 *This,
        PROFILER_TOKEN functionId,
        PROFILER_TOKEN scriptId,
        const WCHAR *pwszFunctionName,
        const WCHAR *pwszFunctionNameHint,
        IUnknown *pIDebugDocumentContext);

    HRESULT (STDMETHODCALLTYPE *OnFunctionEnter)(
        IActiveScriptProfilerCallback3 *This,
        PROFILER_TOKEN scriptId,
        PROFILER_TOKEN functionId);

    HRESULT (STDMETHODCALLTYPE *OnFunctionExit)(
        IActiveScriptProfilerCallback3 *This,
        PROFILER_TOKEN scriptId,
        PROFILER_TOKEN functionId);

    /*** IActiveScriptProfilerCallback2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnFunctionEnterByName)(
        IActiveScriptProfilerCallback3 *This,
        const WCHAR *pwszFunctionName,
        PROFILER_SCRIPT_TYPE type);

    HRESULT (STDMETHODCALLTYPE *OnFunctionExitByName)(
        IActiveScriptProfilerCallback3 *This,
        const WCHAR *pwszFunctionName,
        PROFILER_SCRIPT_TYPE type);

    /*** IActiveScriptProfilerCallback3 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetWebWorkerId)(
        IActiveScriptProfilerCallback3 *This,
        DWORD webWorkerId);

    END_INTERFACE
} IActiveScriptProfilerCallback3Vtbl;

interface IActiveScriptProfilerCallback3 {
    CONST_VTBL IActiveScriptProfilerCallback3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptProfilerCallback3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptProfilerCallback3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptProfilerCallback3_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptProfilerCallback methods ***/
#define IActiveScriptProfilerCallback3_Initialize(This,dwContext) (This)->lpVtbl->Initialize(This,dwContext)
#define IActiveScriptProfilerCallback3_Shutdown(This,hrReason) (This)->lpVtbl->Shutdown(This,hrReason)
#define IActiveScriptProfilerCallback3_ScriptCompiled(This,scriptId,type,pIDebugDocumentContext) (This)->lpVtbl->ScriptCompiled(This,scriptId,type,pIDebugDocumentContext)
#define IActiveScriptProfilerCallback3_FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext) (This)->lpVtbl->FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext)
#define IActiveScriptProfilerCallback3_OnFunctionEnter(This,scriptId,functionId) (This)->lpVtbl->OnFunctionEnter(This,scriptId,functionId)
#define IActiveScriptProfilerCallback3_OnFunctionExit(This,scriptId,functionId) (This)->lpVtbl->OnFunctionExit(This,scriptId,functionId)
/*** IActiveScriptProfilerCallback2 methods ***/
#define IActiveScriptProfilerCallback3_OnFunctionEnterByName(This,pwszFunctionName,type) (This)->lpVtbl->OnFunctionEnterByName(This,pwszFunctionName,type)
#define IActiveScriptProfilerCallback3_OnFunctionExitByName(This,pwszFunctionName,type) (This)->lpVtbl->OnFunctionExitByName(This,pwszFunctionName,type)
/*** IActiveScriptProfilerCallback3 methods ***/
#define IActiveScriptProfilerCallback3_SetWebWorkerId(This,webWorkerId) (This)->lpVtbl->SetWebWorkerId(This,webWorkerId)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptProfilerCallback3_QueryInterface(IActiveScriptProfilerCallback3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptProfilerCallback3_AddRef(IActiveScriptProfilerCallback3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptProfilerCallback3_Release(IActiveScriptProfilerCallback3* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptProfilerCallback methods ***/
static inline HRESULT IActiveScriptProfilerCallback3_Initialize(IActiveScriptProfilerCallback3* This,DWORD dwContext) {
    return This->lpVtbl->Initialize(This,dwContext);
}
static inline HRESULT IActiveScriptProfilerCallback3_Shutdown(IActiveScriptProfilerCallback3* This,HRESULT hrReason) {
    return This->lpVtbl->Shutdown(This,hrReason);
}
static inline HRESULT IActiveScriptProfilerCallback3_ScriptCompiled(IActiveScriptProfilerCallback3* This,PROFILER_TOKEN scriptId,PROFILER_SCRIPT_TYPE type,IUnknown *pIDebugDocumentContext) {
    return This->lpVtbl->ScriptCompiled(This,scriptId,type,pIDebugDocumentContext);
}
static inline HRESULT IActiveScriptProfilerCallback3_FunctionCompiled(IActiveScriptProfilerCallback3* This,PROFILER_TOKEN functionId,PROFILER_TOKEN scriptId,const WCHAR *pwszFunctionName,const WCHAR *pwszFunctionNameHint,IUnknown *pIDebugDocumentContext) {
    return This->lpVtbl->FunctionCompiled(This,functionId,scriptId,pwszFunctionName,pwszFunctionNameHint,pIDebugDocumentContext);
}
static inline HRESULT IActiveScriptProfilerCallback3_OnFunctionEnter(IActiveScriptProfilerCallback3* This,PROFILER_TOKEN scriptId,PROFILER_TOKEN functionId) {
    return This->lpVtbl->OnFunctionEnter(This,scriptId,functionId);
}
static inline HRESULT IActiveScriptProfilerCallback3_OnFunctionExit(IActiveScriptProfilerCallback3* This,PROFILER_TOKEN scriptId,PROFILER_TOKEN functionId) {
    return This->lpVtbl->OnFunctionExit(This,scriptId,functionId);
}
/*** IActiveScriptProfilerCallback2 methods ***/
static inline HRESULT IActiveScriptProfilerCallback3_OnFunctionEnterByName(IActiveScriptProfilerCallback3* This,const WCHAR *pwszFunctionName,PROFILER_SCRIPT_TYPE type) {
    return This->lpVtbl->OnFunctionEnterByName(This,pwszFunctionName,type);
}
static inline HRESULT IActiveScriptProfilerCallback3_OnFunctionExitByName(IActiveScriptProfilerCallback3* This,const WCHAR *pwszFunctionName,PROFILER_SCRIPT_TYPE type) {
    return This->lpVtbl->OnFunctionExitByName(This,pwszFunctionName,type);
}
/*** IActiveScriptProfilerCallback3 methods ***/
static inline HRESULT IActiveScriptProfilerCallback3_SetWebWorkerId(IActiveScriptProfilerCallback3* This,DWORD webWorkerId) {
    return This->lpVtbl->SetWebWorkerId(This,webWorkerId);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptProfilerCallback3_INTERFACE_DEFINED__ */

#endif

#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __activprof_h__ */
