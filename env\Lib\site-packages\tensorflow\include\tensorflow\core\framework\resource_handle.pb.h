// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/resource_handle.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto;
namespace tensorflow {
class ResourceHandleProto;
struct ResourceHandleProtoDefaultTypeInternal;
extern ResourceHandleProtoDefaultTypeInternal _ResourceHandleProto_default_instance_;
class ResourceHandleProto_DtypeAndShape;
struct ResourceHandleProto_DtypeAndShapeDefaultTypeInternal;
extern ResourceHandleProto_DtypeAndShapeDefaultTypeInternal _ResourceHandleProto_DtypeAndShape_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ResourceHandleProto* Arena::CreateMaybeMessage<::tensorflow::ResourceHandleProto>(Arena*);
template<> ::tensorflow::ResourceHandleProto_DtypeAndShape* Arena::CreateMaybeMessage<::tensorflow::ResourceHandleProto_DtypeAndShape>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class ResourceHandleProto_DtypeAndShape final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ResourceHandleProto.DtypeAndShape) */ {
 public:
  inline ResourceHandleProto_DtypeAndShape() : ResourceHandleProto_DtypeAndShape(nullptr) {}
  ~ResourceHandleProto_DtypeAndShape() override;
  explicit PROTOBUF_CONSTEXPR ResourceHandleProto_DtypeAndShape(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResourceHandleProto_DtypeAndShape(const ResourceHandleProto_DtypeAndShape& from);
  ResourceHandleProto_DtypeAndShape(ResourceHandleProto_DtypeAndShape&& from) noexcept
    : ResourceHandleProto_DtypeAndShape() {
    *this = ::std::move(from);
  }

  inline ResourceHandleProto_DtypeAndShape& operator=(const ResourceHandleProto_DtypeAndShape& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResourceHandleProto_DtypeAndShape& operator=(ResourceHandleProto_DtypeAndShape&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResourceHandleProto_DtypeAndShape& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResourceHandleProto_DtypeAndShape* internal_default_instance() {
    return reinterpret_cast<const ResourceHandleProto_DtypeAndShape*>(
               &_ResourceHandleProto_DtypeAndShape_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ResourceHandleProto_DtypeAndShape& a, ResourceHandleProto_DtypeAndShape& b) {
    a.Swap(&b);
  }
  inline void Swap(ResourceHandleProto_DtypeAndShape* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResourceHandleProto_DtypeAndShape* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResourceHandleProto_DtypeAndShape* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResourceHandleProto_DtypeAndShape>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ResourceHandleProto_DtypeAndShape& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ResourceHandleProto_DtypeAndShape& from) {
    ResourceHandleProto_DtypeAndShape::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResourceHandleProto_DtypeAndShape* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ResourceHandleProto.DtypeAndShape";
  }
  protected:
  explicit ResourceHandleProto_DtypeAndShape(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kShapeFieldNumber = 2,
    kDtypeFieldNumber = 1,
  };
  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType dtype = 1;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ResourceHandleProto.DtypeAndShape)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* shape_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto;
};
// -------------------------------------------------------------------

class ResourceHandleProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ResourceHandleProto) */ {
 public:
  inline ResourceHandleProto() : ResourceHandleProto(nullptr) {}
  ~ResourceHandleProto() override;
  explicit PROTOBUF_CONSTEXPR ResourceHandleProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ResourceHandleProto(const ResourceHandleProto& from);
  ResourceHandleProto(ResourceHandleProto&& from) noexcept
    : ResourceHandleProto() {
    *this = ::std::move(from);
  }

  inline ResourceHandleProto& operator=(const ResourceHandleProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ResourceHandleProto& operator=(ResourceHandleProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ResourceHandleProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ResourceHandleProto* internal_default_instance() {
    return reinterpret_cast<const ResourceHandleProto*>(
               &_ResourceHandleProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ResourceHandleProto& a, ResourceHandleProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ResourceHandleProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ResourceHandleProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ResourceHandleProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ResourceHandleProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ResourceHandleProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ResourceHandleProto& from) {
    ResourceHandleProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ResourceHandleProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ResourceHandleProto";
  }
  protected:
  explicit ResourceHandleProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ResourceHandleProto_DtypeAndShape DtypeAndShape;

  // accessors -------------------------------------------------------

  enum : int {
    kDtypesAndShapesFieldNumber = 6,
    kDeviceFieldNumber = 1,
    kContainerFieldNumber = 2,
    kNameFieldNumber = 3,
    kMaybeTypeNameFieldNumber = 5,
    kHashCodeFieldNumber = 4,
  };
  // repeated .tensorflow.ResourceHandleProto.DtypeAndShape dtypes_and_shapes = 6;
  int dtypes_and_shapes_size() const;
  private:
  int _internal_dtypes_and_shapes_size() const;
  public:
  void clear_dtypes_and_shapes();
  ::tensorflow::ResourceHandleProto_DtypeAndShape* mutable_dtypes_and_shapes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >*
      mutable_dtypes_and_shapes();
  private:
  const ::tensorflow::ResourceHandleProto_DtypeAndShape& _internal_dtypes_and_shapes(int index) const;
  ::tensorflow::ResourceHandleProto_DtypeAndShape* _internal_add_dtypes_and_shapes();
  public:
  const ::tensorflow::ResourceHandleProto_DtypeAndShape& dtypes_and_shapes(int index) const;
  ::tensorflow::ResourceHandleProto_DtypeAndShape* add_dtypes_and_shapes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >&
      dtypes_and_shapes() const;

  // string device = 1;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // string container = 2;
  void clear_container();
  const std::string& container() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_container(ArgT0&& arg0, ArgT... args);
  std::string* mutable_container();
  PROTOBUF_NODISCARD std::string* release_container();
  void set_allocated_container(std::string* container);
  private:
  const std::string& _internal_container() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_container(const std::string& value);
  std::string* _internal_mutable_container();
  public:

  // string name = 3;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string maybe_type_name = 5;
  void clear_maybe_type_name();
  const std::string& maybe_type_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_maybe_type_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_maybe_type_name();
  PROTOBUF_NODISCARD std::string* release_maybe_type_name();
  void set_allocated_maybe_type_name(std::string* maybe_type_name);
  private:
  const std::string& _internal_maybe_type_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_maybe_type_name(const std::string& value);
  std::string* _internal_mutable_maybe_type_name();
  public:

  // uint64 hash_code = 4;
  void clear_hash_code();
  uint64_t hash_code() const;
  void set_hash_code(uint64_t value);
  private:
  uint64_t _internal_hash_code() const;
  void _internal_set_hash_code(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ResourceHandleProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape > dtypes_and_shapes_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr container_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr maybe_type_name_;
    uint64_t hash_code_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ResourceHandleProto_DtypeAndShape

// .tensorflow.DataType dtype = 1;
inline void ResourceHandleProto_DtypeAndShape::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType ResourceHandleProto_DtypeAndShape::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType ResourceHandleProto_DtypeAndShape::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.ResourceHandleProto.DtypeAndShape.dtype)
  return _internal_dtype();
}
inline void ResourceHandleProto_DtypeAndShape::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void ResourceHandleProto_DtypeAndShape::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.ResourceHandleProto.DtypeAndShape.dtype)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool ResourceHandleProto_DtypeAndShape::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool ResourceHandleProto_DtypeAndShape::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& ResourceHandleProto_DtypeAndShape::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& ResourceHandleProto_DtypeAndShape::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.ResourceHandleProto.DtypeAndShape.shape)
  return _internal_shape();
}
inline void ResourceHandleProto_DtypeAndShape::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.ResourceHandleProto.DtypeAndShape.shape)
}
inline ::tensorflow::TensorShapeProto* ResourceHandleProto_DtypeAndShape::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* ResourceHandleProto_DtypeAndShape::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.ResourceHandleProto.DtypeAndShape.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* ResourceHandleProto_DtypeAndShape::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* ResourceHandleProto_DtypeAndShape::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.ResourceHandleProto.DtypeAndShape.shape)
  return _msg;
}
inline void ResourceHandleProto_DtypeAndShape::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ResourceHandleProto.DtypeAndShape.shape)
}

// -------------------------------------------------------------------

// ResourceHandleProto

// string device = 1;
inline void ResourceHandleProto::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& ResourceHandleProto::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.ResourceHandleProto.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ResourceHandleProto::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ResourceHandleProto.device)
}
inline std::string* ResourceHandleProto::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.ResourceHandleProto.device)
  return _s;
}
inline const std::string& ResourceHandleProto::_internal_device() const {
  return _impl_.device_.Get();
}
inline void ResourceHandleProto::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* ResourceHandleProto::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* ResourceHandleProto::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.ResourceHandleProto.device)
  return _impl_.device_.Release();
}
inline void ResourceHandleProto::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ResourceHandleProto.device)
}

// string container = 2;
inline void ResourceHandleProto::clear_container() {
  _impl_.container_.ClearToEmpty();
}
inline const std::string& ResourceHandleProto::container() const {
  // @@protoc_insertion_point(field_get:tensorflow.ResourceHandleProto.container)
  return _internal_container();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ResourceHandleProto::set_container(ArgT0&& arg0, ArgT... args) {
 
 _impl_.container_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ResourceHandleProto.container)
}
inline std::string* ResourceHandleProto::mutable_container() {
  std::string* _s = _internal_mutable_container();
  // @@protoc_insertion_point(field_mutable:tensorflow.ResourceHandleProto.container)
  return _s;
}
inline const std::string& ResourceHandleProto::_internal_container() const {
  return _impl_.container_.Get();
}
inline void ResourceHandleProto::_internal_set_container(const std::string& value) {
  
  _impl_.container_.Set(value, GetArenaForAllocation());
}
inline std::string* ResourceHandleProto::_internal_mutable_container() {
  
  return _impl_.container_.Mutable(GetArenaForAllocation());
}
inline std::string* ResourceHandleProto::release_container() {
  // @@protoc_insertion_point(field_release:tensorflow.ResourceHandleProto.container)
  return _impl_.container_.Release();
}
inline void ResourceHandleProto::set_allocated_container(std::string* container) {
  if (container != nullptr) {
    
  } else {
    
  }
  _impl_.container_.SetAllocated(container, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.container_.IsDefault()) {
    _impl_.container_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ResourceHandleProto.container)
}

// string name = 3;
inline void ResourceHandleProto::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& ResourceHandleProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ResourceHandleProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ResourceHandleProto::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ResourceHandleProto.name)
}
inline std::string* ResourceHandleProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ResourceHandleProto.name)
  return _s;
}
inline const std::string& ResourceHandleProto::_internal_name() const {
  return _impl_.name_.Get();
}
inline void ResourceHandleProto::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* ResourceHandleProto::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* ResourceHandleProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ResourceHandleProto.name)
  return _impl_.name_.Release();
}
inline void ResourceHandleProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ResourceHandleProto.name)
}

// uint64 hash_code = 4;
inline void ResourceHandleProto::clear_hash_code() {
  _impl_.hash_code_ = uint64_t{0u};
}
inline uint64_t ResourceHandleProto::_internal_hash_code() const {
  return _impl_.hash_code_;
}
inline uint64_t ResourceHandleProto::hash_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.ResourceHandleProto.hash_code)
  return _internal_hash_code();
}
inline void ResourceHandleProto::_internal_set_hash_code(uint64_t value) {
  
  _impl_.hash_code_ = value;
}
inline void ResourceHandleProto::set_hash_code(uint64_t value) {
  _internal_set_hash_code(value);
  // @@protoc_insertion_point(field_set:tensorflow.ResourceHandleProto.hash_code)
}

// string maybe_type_name = 5;
inline void ResourceHandleProto::clear_maybe_type_name() {
  _impl_.maybe_type_name_.ClearToEmpty();
}
inline const std::string& ResourceHandleProto::maybe_type_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.ResourceHandleProto.maybe_type_name)
  return _internal_maybe_type_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ResourceHandleProto::set_maybe_type_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.maybe_type_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ResourceHandleProto.maybe_type_name)
}
inline std::string* ResourceHandleProto::mutable_maybe_type_name() {
  std::string* _s = _internal_mutable_maybe_type_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.ResourceHandleProto.maybe_type_name)
  return _s;
}
inline const std::string& ResourceHandleProto::_internal_maybe_type_name() const {
  return _impl_.maybe_type_name_.Get();
}
inline void ResourceHandleProto::_internal_set_maybe_type_name(const std::string& value) {
  
  _impl_.maybe_type_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ResourceHandleProto::_internal_mutable_maybe_type_name() {
  
  return _impl_.maybe_type_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ResourceHandleProto::release_maybe_type_name() {
  // @@protoc_insertion_point(field_release:tensorflow.ResourceHandleProto.maybe_type_name)
  return _impl_.maybe_type_name_.Release();
}
inline void ResourceHandleProto::set_allocated_maybe_type_name(std::string* maybe_type_name) {
  if (maybe_type_name != nullptr) {
    
  } else {
    
  }
  _impl_.maybe_type_name_.SetAllocated(maybe_type_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.maybe_type_name_.IsDefault()) {
    _impl_.maybe_type_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ResourceHandleProto.maybe_type_name)
}

// repeated .tensorflow.ResourceHandleProto.DtypeAndShape dtypes_and_shapes = 6;
inline int ResourceHandleProto::_internal_dtypes_and_shapes_size() const {
  return _impl_.dtypes_and_shapes_.size();
}
inline int ResourceHandleProto::dtypes_and_shapes_size() const {
  return _internal_dtypes_and_shapes_size();
}
inline void ResourceHandleProto::clear_dtypes_and_shapes() {
  _impl_.dtypes_and_shapes_.Clear();
}
inline ::tensorflow::ResourceHandleProto_DtypeAndShape* ResourceHandleProto::mutable_dtypes_and_shapes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ResourceHandleProto.dtypes_and_shapes)
  return _impl_.dtypes_and_shapes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >*
ResourceHandleProto::mutable_dtypes_and_shapes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ResourceHandleProto.dtypes_and_shapes)
  return &_impl_.dtypes_and_shapes_;
}
inline const ::tensorflow::ResourceHandleProto_DtypeAndShape& ResourceHandleProto::_internal_dtypes_and_shapes(int index) const {
  return _impl_.dtypes_and_shapes_.Get(index);
}
inline const ::tensorflow::ResourceHandleProto_DtypeAndShape& ResourceHandleProto::dtypes_and_shapes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ResourceHandleProto.dtypes_and_shapes)
  return _internal_dtypes_and_shapes(index);
}
inline ::tensorflow::ResourceHandleProto_DtypeAndShape* ResourceHandleProto::_internal_add_dtypes_and_shapes() {
  return _impl_.dtypes_and_shapes_.Add();
}
inline ::tensorflow::ResourceHandleProto_DtypeAndShape* ResourceHandleProto::add_dtypes_and_shapes() {
  ::tensorflow::ResourceHandleProto_DtypeAndShape* _add = _internal_add_dtypes_and_shapes();
  // @@protoc_insertion_point(field_add:tensorflow.ResourceHandleProto.dtypes_and_shapes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::ResourceHandleProto_DtypeAndShape >&
ResourceHandleProto::dtypes_and_shapes() const {
  // @@protoc_insertion_point(field_list:tensorflow.ResourceHandleProto.dtypes_and_shapes)
  return _impl_.dtypes_and_shapes_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fresource_5fhandle_2eproto
