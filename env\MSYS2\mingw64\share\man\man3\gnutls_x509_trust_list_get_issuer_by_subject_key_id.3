.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_trust_list_get_issuer_by_subject_key_id" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_trust_list_get_issuer_by_subject_key_id \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_trust_list_get_issuer_by_subject_key_id(gnutls_x509_trust_list_t " list ", const gnutls_datum_t * " dn ", const gnutls_datum_t * " spki ", gnutls_x509_crt_t * " issuer ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "gnutls_x509_trust_list_t list" 12
The list
.IP "const gnutls_datum_t * dn" 12
is the issuer's DN (may be \fBNULL\fP)
.IP "const gnutls_datum_t * spki" 12
is the subject key ID
.IP "gnutls_x509_crt_t * issuer" 12
Will hold the issuer if any. Should be deallocated after use.
.IP "unsigned int flags" 12
Use zero
.SH "DESCRIPTION"
This function will find the issuer with the given name and subject key ID, and
return a copy of the issuer, which must be freed using \fBgnutls_x509_crt_deinit()\fP.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
3.4.2
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
