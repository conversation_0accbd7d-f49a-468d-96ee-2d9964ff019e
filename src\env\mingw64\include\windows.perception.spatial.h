/*** Autogenerated by WIDL 10.8 from include/windows.perception.spatial.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_perception_spatial_h__
#define __windows_perception_spatial_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume ABI::Windows::Perception::Spatial::ISpatialBoundingVolume
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                interface ISpatialBoundingVolume;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics ABI::Windows::Perception::Spatial::ISpatialBoundingVolumeStatics
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                interface ISpatialBoundingVolumeStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                interface ISpatialCoordinateSystem;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingVolume_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingVolume_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                class SpatialBoundingVolume;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingVolume __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingVolume;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingVolume_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CSpatialCoordinateSystem_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CSpatialCoordinateSystem_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                class SpatialCoordinateSystem;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialCoordinateSystem __x_ABI_CWindows_CPerception_CSpatial_CSpatialCoordinateSystem;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CPerception_CSpatial_CSpatialCoordinateSystem_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_FWD_DEFINED__
#define ____FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_FWD_DEFINED__
#define ____FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_SpatialPerceptionAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1_SpatialPerceptionAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_SpatialPerceptionAccessStatus __FIAsyncOperation_1_SpatialPerceptionAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_SpatialBoundingBox_FWD_DEFINED__
#define ____FIReference_1_SpatialBoundingBox_FWD_DEFINED__
typedef interface __FIReference_1_SpatialBoundingBox __FIReference_1_SpatialBoundingBox;
#ifdef __cplusplus
#define __FIReference_1_SpatialBoundingBox ABI::Windows::Foundation::IReference<ABI::Windows::Perception::Spatial::SpatialBoundingBox >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_SpatialBoundingFrustum_FWD_DEFINED__
#define ____FIReference_1_SpatialBoundingFrustum_FWD_DEFINED__
typedef interface __FIReference_1_SpatialBoundingFrustum __FIReference_1_SpatialBoundingFrustum;
#ifdef __cplusplus
#define __FIReference_1_SpatialBoundingFrustum ABI::Windows::Foundation::IReference<ABI::Windows::Perception::Spatial::SpatialBoundingFrustum >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_SpatialBoundingOrientedBox_FWD_DEFINED__
#define ____FIReference_1_SpatialBoundingOrientedBox_FWD_DEFINED__
typedef interface __FIReference_1_SpatialBoundingOrientedBox __FIReference_1_SpatialBoundingOrientedBox;
#ifdef __cplusplus
#define __FIReference_1_SpatialBoundingOrientedBox ABI::Windows::Foundation::IReference<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.foundation.numerics.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CPerception_CSpatial_CSpatialPerceptionAccessStatus __x_ABI_CWindows_CPerception_CSpatial_CSpatialPerceptionAccessStatus;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingBox __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingBox;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                typedef struct SpatialBoundingBox SpatialBoundingBox;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingFrustum __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingFrustum;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                typedef struct SpatialBoundingFrustum SpatialBoundingFrustum;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingOrientedBox __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingOrientedBox;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                typedef struct SpatialBoundingOrientedBox SpatialBoundingOrientedBox;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingSphere __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingSphere;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                typedef struct SpatialBoundingSphere SpatialBoundingSphere;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume ABI::Windows::Perception::Spatial::ISpatialBoundingVolume
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                interface ISpatialBoundingVolume;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics ABI::Windows::Perception::Spatial::ISpatialBoundingVolumeStatics
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                interface ISpatialBoundingVolumeStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_FWD_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem;
#ifdef __cplusplus
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                interface ISpatialCoordinateSystem;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_FWD_DEFINED__
#define ____FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_FWD_DEFINED__
#define ____FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_SpatialPerceptionAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1_SpatialPerceptionAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_SpatialPerceptionAccessStatus __FIAsyncOperation_1_SpatialPerceptionAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_SpatialBoundingBox_FWD_DEFINED__
#define ____FIReference_1_SpatialBoundingBox_FWD_DEFINED__
typedef interface __FIReference_1_SpatialBoundingBox __FIReference_1_SpatialBoundingBox;
#ifdef __cplusplus
#define __FIReference_1_SpatialBoundingBox ABI::Windows::Foundation::IReference<ABI::Windows::Perception::Spatial::SpatialBoundingBox >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_SpatialBoundingFrustum_FWD_DEFINED__
#define ____FIReference_1_SpatialBoundingFrustum_FWD_DEFINED__
typedef interface __FIReference_1_SpatialBoundingFrustum __FIReference_1_SpatialBoundingFrustum;
#ifdef __cplusplus
#define __FIReference_1_SpatialBoundingFrustum ABI::Windows::Foundation::IReference<ABI::Windows::Perception::Spatial::SpatialBoundingFrustum >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_SpatialBoundingOrientedBox_FWD_DEFINED__
#define ____FIReference_1_SpatialBoundingOrientedBox_FWD_DEFINED__
typedef interface __FIReference_1_SpatialBoundingOrientedBox __FIReference_1_SpatialBoundingOrientedBox;
#ifdef __cplusplus
#define __FIReference_1_SpatialBoundingOrientedBox ABI::Windows::Foundation::IReference<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                enum SpatialPerceptionAccessStatus {
                    SpatialPerceptionAccessStatus_Unspecified = 0,
                    SpatialPerceptionAccessStatus_Allowed = 1,
                    SpatialPerceptionAccessStatus_DeniedByUser = 2,
                    SpatialPerceptionAccessStatus_DeniedBySystem = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CPerception_CSpatial_CSpatialPerceptionAccessStatus {
    SpatialPerceptionAccessStatus_Unspecified = 0,
    SpatialPerceptionAccessStatus_Allowed = 1,
    SpatialPerceptionAccessStatus_DeniedByUser = 2,
    SpatialPerceptionAccessStatus_DeniedBySystem = 3
};
#ifdef WIDL_using_Windows_Perception_Spatial
#define SpatialPerceptionAccessStatus __x_ABI_CWindows_CPerception_CSpatial_CSpatialPerceptionAccessStatus
#endif /* WIDL_using_Windows_Perception_Spatial */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                struct SpatialBoundingBox {
                    ABI::Windows::Foundation::Numerics::Vector3 Center;
                    ABI::Windows::Foundation::Numerics::Vector3 Extents;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingBox {
    __x_ABI_CWindows_CFoundation_CNumerics_CVector3 Center;
    __x_ABI_CWindows_CFoundation_CNumerics_CVector3 Extents;
};
#ifdef WIDL_using_Windows_Perception_Spatial
#define SpatialBoundingBox __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingBox
#endif /* WIDL_using_Windows_Perception_Spatial */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                struct SpatialBoundingFrustum {
                    ABI::Windows::Foundation::Numerics::Plane Near;
                    ABI::Windows::Foundation::Numerics::Plane Far;
                    ABI::Windows::Foundation::Numerics::Plane Right;
                    ABI::Windows::Foundation::Numerics::Plane Left;
                    ABI::Windows::Foundation::Numerics::Plane Top;
                    ABI::Windows::Foundation::Numerics::Plane Bottom;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingFrustum {
    __x_ABI_CWindows_CFoundation_CNumerics_CPlane Near;
    __x_ABI_CWindows_CFoundation_CNumerics_CPlane Far;
    __x_ABI_CWindows_CFoundation_CNumerics_CPlane Right;
    __x_ABI_CWindows_CFoundation_CNumerics_CPlane Left;
    __x_ABI_CWindows_CFoundation_CNumerics_CPlane Top;
    __x_ABI_CWindows_CFoundation_CNumerics_CPlane Bottom;
};
#ifdef WIDL_using_Windows_Perception_Spatial
#define SpatialBoundingFrustum __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingFrustum
#endif /* WIDL_using_Windows_Perception_Spatial */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                struct SpatialBoundingOrientedBox {
                    ABI::Windows::Foundation::Numerics::Vector3 Center;
                    ABI::Windows::Foundation::Numerics::Vector3 Extents;
                    ABI::Windows::Foundation::Numerics::Quaternion Orientation;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingOrientedBox {
    __x_ABI_CWindows_CFoundation_CNumerics_CVector3 Center;
    __x_ABI_CWindows_CFoundation_CNumerics_CVector3 Extents;
    __x_ABI_CWindows_CFoundation_CNumerics_CQuaternion Orientation;
};
#ifdef WIDL_using_Windows_Perception_Spatial
#define SpatialBoundingOrientedBox __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingOrientedBox
#endif /* WIDL_using_Windows_Perception_Spatial */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                struct SpatialBoundingSphere {
                    ABI::Windows::Foundation::Numerics::Vector3 Center;
                    FLOAT Radius;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingSphere {
    __x_ABI_CWindows_CFoundation_CNumerics_CVector3 Center;
    FLOAT Radius;
};
#ifdef WIDL_using_Windows_Perception_Spatial
#define SpatialBoundingSphere __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingSphere
#endif /* WIDL_using_Windows_Perception_Spatial */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
/*****************************************************************************
 * ISpatialBoundingVolume interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume, 0xfb2065da, 0x68c3, 0x33df, 0xb7,0xaf, 0x4c,0x78,0x72,0x07,0x99,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                MIDL_INTERFACE("fb2065da-68c3-33df-b7af-4c787207999c")
                ISpatialBoundingVolume : public IInspectable
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume, 0xfb2065da, 0x68c3, 0x33df, 0xb7,0xaf, 0x4c,0x78,0x72,0x07,0x99,0x9c)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_Release(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial
#define IID_ISpatialBoundingVolume IID___x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume
#define ISpatialBoundingVolumeVtbl __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeVtbl
#define ISpatialBoundingVolume __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume
#define ISpatialBoundingVolume_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_QueryInterface
#define ISpatialBoundingVolume_AddRef __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_AddRef
#define ISpatialBoundingVolume_Release __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_Release
#define ISpatialBoundingVolume_GetIids __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetIids
#define ISpatialBoundingVolume_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetRuntimeClassName
#define ISpatialBoundingVolume_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_GetTrustLevel
#endif /* WIDL_using_Windows_Perception_Spatial */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialBoundingVolumeStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics, 0x05889117, 0xb3e1, 0x36d8, 0xb0,0x17, 0x56,0x61,0x81,0xa5,0xb1,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                MIDL_INTERFACE("05889117-b3e1-36d8-b017-566181a5b196")
                ISpatialBoundingVolumeStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE FromBox(
                        ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem *system,
                        ABI::Windows::Perception::Spatial::SpatialBoundingBox box,
                        ABI::Windows::Perception::Spatial::ISpatialBoundingVolume **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromOrientedBox(
                        ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem *system,
                        ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox box,
                        ABI::Windows::Perception::Spatial::ISpatialBoundingVolume **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromSphere(
                        ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem *system,
                        ABI::Windows::Perception::Spatial::SpatialBoundingSphere sphere,
                        ABI::Windows::Perception::Spatial::ISpatialBoundingVolume **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromFrustum(
                        ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem *system,
                        ABI::Windows::Perception::Spatial::SpatialBoundingFrustum frustum,
                        ABI::Windows::Perception::Spatial::ISpatialBoundingVolume **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics, 0x05889117, 0xb3e1, 0x36d8, 0xb0,0x17, 0x56,0x61,0x81,0xa5,0xb1,0x96)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This,
        TrustLevel *trustLevel);

    /*** ISpatialBoundingVolumeStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *FromBox)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,
        __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingBox box,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value);

    HRESULT (STDMETHODCALLTYPE *FromOrientedBox)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,
        __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingOrientedBox box,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value);

    HRESULT (STDMETHODCALLTYPE *FromSphere)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,
        __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingSphere sphere,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value);

    HRESULT (STDMETHODCALLTYPE *FromFrustum)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,
        __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingFrustum frustum,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStaticsVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialBoundingVolumeStatics methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromBox(This,system,box,value) (This)->lpVtbl->FromBox(This,system,box,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromOrientedBox(This,system,box,value) (This)->lpVtbl->FromOrientedBox(This,system,box,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromSphere(This,system,sphere,value) (This)->lpVtbl->FromSphere(This,system,sphere,value)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromFrustum(This,system,frustum,value) (This)->lpVtbl->FromFrustum(This,system,frustum,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_Release(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialBoundingVolumeStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromBox(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,__x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingBox box,__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value) {
    return This->lpVtbl->FromBox(This,system,box,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromOrientedBox(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,__x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingOrientedBox box,__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value) {
    return This->lpVtbl->FromOrientedBox(This,system,box,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromSphere(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,__x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingSphere sphere,__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value) {
    return This->lpVtbl->FromSphere(This,system,sphere,value);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromFrustum(__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *system,__x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingFrustum frustum,__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value) {
    return This->lpVtbl->FromFrustum(This,system,frustum,value);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial
#define IID_ISpatialBoundingVolumeStatics IID___x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics
#define ISpatialBoundingVolumeStaticsVtbl __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStaticsVtbl
#define ISpatialBoundingVolumeStatics __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics
#define ISpatialBoundingVolumeStatics_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_QueryInterface
#define ISpatialBoundingVolumeStatics_AddRef __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_AddRef
#define ISpatialBoundingVolumeStatics_Release __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_Release
#define ISpatialBoundingVolumeStatics_GetIids __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetIids
#define ISpatialBoundingVolumeStatics_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetRuntimeClassName
#define ISpatialBoundingVolumeStatics_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_GetTrustLevel
#define ISpatialBoundingVolumeStatics_FromBox __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromBox
#define ISpatialBoundingVolumeStatics_FromOrientedBox __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromOrientedBox
#define ISpatialBoundingVolumeStatics_FromSphere __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromSphere
#define ISpatialBoundingVolumeStatics_FromFrustum __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_FromFrustum
#endif /* WIDL_using_Windows_Perception_Spatial */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolumeStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * ISpatialCoordinateSystem interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem, 0x69ebca4b, 0x60a3, 0x3586, 0xa6,0x53, 0x59,0xa7,0xbd,0x67,0x6d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Perception {
            namespace Spatial {
                MIDL_INTERFACE("69ebca4b-60a3-3586-a653-59a7bd676d07")
                ISpatialCoordinateSystem : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE TryGetTransformTo(
                        ABI::Windows::Perception::Spatial::ISpatialCoordinateSystem *target,
                        ABI::Windows::Foundation::IReference<ABI::Windows::Foundation::Numerics::Matrix4x4 > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem, 0x69ebca4b, 0x60a3, 0x3586, 0xa6,0x53, 0x59,0xa7,0xbd,0x67,0x6d,0x07)
#endif
#else
typedef struct __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *This,
        TrustLevel *trustLevel);

    /*** ISpatialCoordinateSystem methods ***/
    HRESULT (STDMETHODCALLTYPE *TryGetTransformTo)(
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *target,
        __FIReference_1_Matrix4x4 **value);

    END_INTERFACE
} __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystemVtbl;

interface __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem {
    CONST_VTBL __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpatialCoordinateSystem methods ***/
#define __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_TryGetTransformTo(This,target,value) (This)->lpVtbl->TryGetTransformTo(This,target,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_QueryInterface(__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_AddRef(__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_Release(__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetIids(__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetRuntimeClassName(__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetTrustLevel(__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpatialCoordinateSystem methods ***/
static inline HRESULT __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_TryGetTransformTo(__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem *target,__FIReference_1_Matrix4x4 **value) {
    return This->lpVtbl->TryGetTransformTo(This,target,value);
}
#endif
#ifdef WIDL_using_Windows_Perception_Spatial
#define IID_ISpatialCoordinateSystem IID___x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem
#define ISpatialCoordinateSystemVtbl __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystemVtbl
#define ISpatialCoordinateSystem __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem
#define ISpatialCoordinateSystem_QueryInterface __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_QueryInterface
#define ISpatialCoordinateSystem_AddRef __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_AddRef
#define ISpatialCoordinateSystem_Release __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_Release
#define ISpatialCoordinateSystem_GetIids __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetIids
#define ISpatialCoordinateSystem_GetRuntimeClassName __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetRuntimeClassName
#define ISpatialCoordinateSystem_GetTrustLevel __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_GetTrustLevel
#define ISpatialCoordinateSystem_TryGetTransformTo __x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_TryGetTransformTo
#endif /* WIDL_using_Windows_Perception_Spatial */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CPerception_CSpatial_CISpatialCoordinateSystem_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Perception.Spatial.SpatialBoundingVolume
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Perception_Spatial_SpatialBoundingVolume_DEFINED
#define RUNTIMECLASS_Windows_Perception_Spatial_SpatialBoundingVolume_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Perception_Spatial_SpatialBoundingVolume[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','p','a','t','i','a','l','B','o','u','n','d','i','n','g','V','o','l','u','m','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_SpatialBoundingVolume[] = L"Windows.Perception.Spatial.SpatialBoundingVolume";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_SpatialBoundingVolume[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','p','a','t','i','a','l','B','o','u','n','d','i','n','g','V','o','l','u','m','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Perception_Spatial_SpatialBoundingVolume_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*
 * Class Windows.Perception.Spatial.SpatialCoordinateSystem
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef RUNTIMECLASS_Windows_Perception_Spatial_SpatialCoordinateSystem_DEFINED
#define RUNTIMECLASS_Windows_Perception_Spatial_SpatialCoordinateSystem_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Perception_Spatial_SpatialCoordinateSystem[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','p','a','t','i','a','l','C','o','o','r','d','i','n','a','t','e','S','y','s','t','e','m',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_SpatialCoordinateSystem[] = L"Windows.Perception.Spatial.SpatialCoordinateSystem";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Perception_Spatial_SpatialCoordinateSystem[] = {'W','i','n','d','o','w','s','.','P','e','r','c','e','p','t','i','o','n','.','S','p','a','t','i','a','l','.','S','p','a','t','i','a','l','C','o','o','r','d','i','n','a','t','e','S','y','s','t','e','m',0};
#endif
#endif /* RUNTIMECLASS_Windows_Perception_Spatial_SpatialCoordinateSystem_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > interface
 */
#ifndef ____FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume, 0x89e8f1ee, 0x3a2a, 0x5b69, 0xa7,0x86, 0xcd,0xdc,0xf7,0x45,0x6a,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("89e8f1ee-3a2a-5b69-a786-cddcf7456a3a")
                IIterable<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Perception::Spatial::SpatialBoundingVolume*, ABI::Windows::Perception::Spatial::ISpatialBoundingVolume* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume, 0x89e8f1ee, 0x3a2a, 0x5b69, 0xa7,0x86, 0xcd,0xdc,0xf7,0x45,0x6a,0x3a)
#endif
#else
typedef struct __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolumeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume **value);

    END_INTERFACE
} __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolumeVtbl;

interface __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume {
    CONST_VTBL __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolumeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > methods ***/
#define __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_QueryInterface(__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_AddRef(__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_Release(__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetIids(__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetRuntimeClassName(__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetTrustLevel(__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_First(__FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_SpatialBoundingVolume IID___FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume
#define IIterable_SpatialBoundingVolumeVtbl __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolumeVtbl
#define IIterable_SpatialBoundingVolume __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume
#define IIterable_SpatialBoundingVolume_QueryInterface __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_QueryInterface
#define IIterable_SpatialBoundingVolume_AddRef __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_AddRef
#define IIterable_SpatialBoundingVolume_Release __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_Release
#define IIterable_SpatialBoundingVolume_GetIids __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetIids
#define IIterable_SpatialBoundingVolume_GetRuntimeClassName __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetRuntimeClassName
#define IIterable_SpatialBoundingVolume_GetTrustLevel __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetTrustLevel
#define IIterable_SpatialBoundingVolume_First __FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > interface
 */
#ifndef ____FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume, 0xeb8385c5, 0x0775, 0x5415, 0x8f,0x76, 0x32,0x7e,0x6e,0x38,0x8a,0xc5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("eb8385c5-0775-5415-8f76-327e6e388ac5")
                IIterator<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Perception::Spatial::SpatialBoundingVolume*, ABI::Windows::Perception::Spatial::ISpatialBoundingVolume* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume, 0xeb8385c5, 0x0775, 0x5415, 0x8f,0x76, 0x32,0x7e,0x6e,0x38,0x8a,0xc5)
#endif
#else
typedef struct __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolumeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume *This,
        UINT32 items_size,
        __x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolumeVtbl;

interface __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume {
    CONST_VTBL __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolumeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > methods ***/
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_QueryInterface(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_AddRef(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_Release(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetIids(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetRuntimeClassName(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetTrustLevel(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Perception::Spatial::SpatialBoundingVolume* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_get_Current(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_get_HasCurrent(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_MoveNext(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetMany(__FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume* This,UINT32 items_size,__x_ABI_CWindows_CPerception_CSpatial_CISpatialBoundingVolume **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_SpatialBoundingVolume IID___FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume
#define IIterator_SpatialBoundingVolumeVtbl __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolumeVtbl
#define IIterator_SpatialBoundingVolume __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume
#define IIterator_SpatialBoundingVolume_QueryInterface __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_QueryInterface
#define IIterator_SpatialBoundingVolume_AddRef __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_AddRef
#define IIterator_SpatialBoundingVolume_Release __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_Release
#define IIterator_SpatialBoundingVolume_GetIids __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetIids
#define IIterator_SpatialBoundingVolume_GetRuntimeClassName __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetRuntimeClassName
#define IIterator_SpatialBoundingVolume_GetTrustLevel __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetTrustLevel
#define IIterator_SpatialBoundingVolume_get_Current __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_get_Current
#define IIterator_SpatialBoundingVolume_get_HasCurrent __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_get_HasCurrent
#define IIterator_SpatialBoundingVolume_MoveNext __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_MoveNext
#define IIterator_SpatialBoundingVolume_GetMany __FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CPerception__CSpatial__CSpatialBoundingVolume_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus, 0x6ced54c8, 0x7689, 0x525a, 0x80,0xe1, 0x95,0x6a,0x9d,0x85,0xcd,0x83);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("6ced54c8-7689-525a-80e1-956a9d85cd83")
            IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus, 0x6ced54c8, 0x7689, 0x525a, 0x80,0xe1, 0x95,0x6a,0x9d,0x85,0xcd,0x83)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus *This,
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatusVtbl;

interface __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > methods ***/
#define __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_QueryInterface(__FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_AddRef(__FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_Release(__FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_Invoke(__FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus* This,__FIAsyncOperation_1_SpatialPerceptionAccessStatus *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_SpatialPerceptionAccessStatus IID___FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus
#define IAsyncOperationCompletedHandler_SpatialPerceptionAccessStatusVtbl __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatusVtbl
#define IAsyncOperationCompletedHandler_SpatialPerceptionAccessStatus __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus
#define IAsyncOperationCompletedHandler_SpatialPerceptionAccessStatus_QueryInterface __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_QueryInterface
#define IAsyncOperationCompletedHandler_SpatialPerceptionAccessStatus_AddRef __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_AddRef
#define IAsyncOperationCompletedHandler_SpatialPerceptionAccessStatus_Release __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_Release
#define IAsyncOperationCompletedHandler_SpatialPerceptionAccessStatus_Invoke __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > interface
 */
#ifndef ____FIAsyncOperation_1_SpatialPerceptionAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_SpatialPerceptionAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_SpatialPerceptionAccessStatus, 0xb425d126, 0x1069, 0x563f, 0xa8,0x63, 0x44,0xa3,0x0a,0x8f,0x07,0x1d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b425d126-1069-563f-a863-44a30a8f071d")
            IAsyncOperation<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > : IAsyncOperation_impl<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_SpatialPerceptionAccessStatus, 0xb425d126, 0x1069, 0x563f, 0xa8,0x63, 0x44,0xa3,0x0a,0x8f,0x07,0x1d)
#endif
#else
typedef struct __FIAsyncOperation_1_SpatialPerceptionAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_SpatialPerceptionAccessStatus *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSpatialPerceptionAccessStatus *results);

    END_INTERFACE
} __FIAsyncOperation_1_SpatialPerceptionAccessStatusVtbl;

interface __FIAsyncOperation_1_SpatialPerceptionAccessStatus {
    CONST_VTBL __FIAsyncOperation_1_SpatialPerceptionAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > methods ***/
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_SpatialPerceptionAccessStatus_QueryInterface(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_SpatialPerceptionAccessStatus_AddRef(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_SpatialPerceptionAccessStatus_Release(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetIids(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetRuntimeClassName(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetTrustLevel(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Perception::Spatial::SpatialPerceptionAccessStatus > methods ***/
static inline HRESULT __FIAsyncOperation_1_SpatialPerceptionAccessStatus_put_Completed(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This,__FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_SpatialPerceptionAccessStatus_get_Completed(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This,__FIAsyncOperationCompletedHandler_1_SpatialPerceptionAccessStatus **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetResults(__FIAsyncOperation_1_SpatialPerceptionAccessStatus* This,__x_ABI_CWindows_CPerception_CSpatial_CSpatialPerceptionAccessStatus *results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_SpatialPerceptionAccessStatus IID___FIAsyncOperation_1_SpatialPerceptionAccessStatus
#define IAsyncOperation_SpatialPerceptionAccessStatusVtbl __FIAsyncOperation_1_SpatialPerceptionAccessStatusVtbl
#define IAsyncOperation_SpatialPerceptionAccessStatus __FIAsyncOperation_1_SpatialPerceptionAccessStatus
#define IAsyncOperation_SpatialPerceptionAccessStatus_QueryInterface __FIAsyncOperation_1_SpatialPerceptionAccessStatus_QueryInterface
#define IAsyncOperation_SpatialPerceptionAccessStatus_AddRef __FIAsyncOperation_1_SpatialPerceptionAccessStatus_AddRef
#define IAsyncOperation_SpatialPerceptionAccessStatus_Release __FIAsyncOperation_1_SpatialPerceptionAccessStatus_Release
#define IAsyncOperation_SpatialPerceptionAccessStatus_GetIids __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetIids
#define IAsyncOperation_SpatialPerceptionAccessStatus_GetRuntimeClassName __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetRuntimeClassName
#define IAsyncOperation_SpatialPerceptionAccessStatus_GetTrustLevel __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetTrustLevel
#define IAsyncOperation_SpatialPerceptionAccessStatus_put_Completed __FIAsyncOperation_1_SpatialPerceptionAccessStatus_put_Completed
#define IAsyncOperation_SpatialPerceptionAccessStatus_get_Completed __FIAsyncOperation_1_SpatialPerceptionAccessStatus_get_Completed
#define IAsyncOperation_SpatialPerceptionAccessStatus_GetResults __FIAsyncOperation_1_SpatialPerceptionAccessStatus_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_SpatialPerceptionAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IReference<ABI::Windows::Perception::Spatial::SpatialBoundingBox > interface
 */
#ifndef ____FIReference_1_SpatialBoundingBox_INTERFACE_DEFINED__
#define ____FIReference_1_SpatialBoundingBox_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIReference_1_SpatialBoundingBox, 0xab3274d9, 0x9b82, 0x5396, 0xbb,0x00, 0xd7,0x0c,0x53,0x97,0x96,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("ab3274d9-9b82-5396-bb00-d70c539796b3")
            IReference<ABI::Windows::Perception::Spatial::SpatialBoundingBox > : IReference_impl<ABI::Windows::Perception::Spatial::SpatialBoundingBox >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIReference_1_SpatialBoundingBox, 0xab3274d9, 0x9b82, 0x5396, 0xbb,0x00, 0xd7,0x0c,0x53,0x97,0x96,0xb3)
#endif
#else
typedef struct __FIReference_1_SpatialBoundingBoxVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIReference_1_SpatialBoundingBox *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIReference_1_SpatialBoundingBox *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIReference_1_SpatialBoundingBox *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIReference_1_SpatialBoundingBox *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIReference_1_SpatialBoundingBox *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIReference_1_SpatialBoundingBox *This,
        TrustLevel *trustLevel);

    /*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingBox > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIReference_1_SpatialBoundingBox *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingBox *value);

    END_INTERFACE
} __FIReference_1_SpatialBoundingBoxVtbl;

interface __FIReference_1_SpatialBoundingBox {
    CONST_VTBL __FIReference_1_SpatialBoundingBoxVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIReference_1_SpatialBoundingBox_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIReference_1_SpatialBoundingBox_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIReference_1_SpatialBoundingBox_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIReference_1_SpatialBoundingBox_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIReference_1_SpatialBoundingBox_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIReference_1_SpatialBoundingBox_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingBox > methods ***/
#define __FIReference_1_SpatialBoundingBox_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingBox_QueryInterface(__FIReference_1_SpatialBoundingBox* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIReference_1_SpatialBoundingBox_AddRef(__FIReference_1_SpatialBoundingBox* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIReference_1_SpatialBoundingBox_Release(__FIReference_1_SpatialBoundingBox* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingBox_GetIids(__FIReference_1_SpatialBoundingBox* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIReference_1_SpatialBoundingBox_GetRuntimeClassName(__FIReference_1_SpatialBoundingBox* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIReference_1_SpatialBoundingBox_GetTrustLevel(__FIReference_1_SpatialBoundingBox* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingBox > methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingBox_get_Value(__FIReference_1_SpatialBoundingBox* This,__x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingBox *value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IReference_SpatialBoundingBox IID___FIReference_1_SpatialBoundingBox
#define IReference_SpatialBoundingBoxVtbl __FIReference_1_SpatialBoundingBoxVtbl
#define IReference_SpatialBoundingBox __FIReference_1_SpatialBoundingBox
#define IReference_SpatialBoundingBox_QueryInterface __FIReference_1_SpatialBoundingBox_QueryInterface
#define IReference_SpatialBoundingBox_AddRef __FIReference_1_SpatialBoundingBox_AddRef
#define IReference_SpatialBoundingBox_Release __FIReference_1_SpatialBoundingBox_Release
#define IReference_SpatialBoundingBox_GetIids __FIReference_1_SpatialBoundingBox_GetIids
#define IReference_SpatialBoundingBox_GetRuntimeClassName __FIReference_1_SpatialBoundingBox_GetRuntimeClassName
#define IReference_SpatialBoundingBox_GetTrustLevel __FIReference_1_SpatialBoundingBox_GetTrustLevel
#define IReference_SpatialBoundingBox_get_Value __FIReference_1_SpatialBoundingBox_get_Value
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIReference_1_SpatialBoundingBox_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IReference<ABI::Windows::Perception::Spatial::SpatialBoundingFrustum > interface
 */
#ifndef ____FIReference_1_SpatialBoundingFrustum_INTERFACE_DEFINED__
#define ____FIReference_1_SpatialBoundingFrustum_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIReference_1_SpatialBoundingFrustum, 0xf434face, 0x0c36, 0x5749, 0xa8,0xa0, 0x0b,0xb6,0xce,0x78,0xa6,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("f434face-0c36-5749-a8a0-0bb6ce78a614")
            IReference<ABI::Windows::Perception::Spatial::SpatialBoundingFrustum > : IReference_impl<ABI::Windows::Perception::Spatial::SpatialBoundingFrustum >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIReference_1_SpatialBoundingFrustum, 0xf434face, 0x0c36, 0x5749, 0xa8,0xa0, 0x0b,0xb6,0xce,0x78,0xa6,0x14)
#endif
#else
typedef struct __FIReference_1_SpatialBoundingFrustumVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIReference_1_SpatialBoundingFrustum *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIReference_1_SpatialBoundingFrustum *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIReference_1_SpatialBoundingFrustum *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIReference_1_SpatialBoundingFrustum *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIReference_1_SpatialBoundingFrustum *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIReference_1_SpatialBoundingFrustum *This,
        TrustLevel *trustLevel);

    /*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingFrustum > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIReference_1_SpatialBoundingFrustum *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingFrustum *value);

    END_INTERFACE
} __FIReference_1_SpatialBoundingFrustumVtbl;

interface __FIReference_1_SpatialBoundingFrustum {
    CONST_VTBL __FIReference_1_SpatialBoundingFrustumVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIReference_1_SpatialBoundingFrustum_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIReference_1_SpatialBoundingFrustum_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIReference_1_SpatialBoundingFrustum_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIReference_1_SpatialBoundingFrustum_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIReference_1_SpatialBoundingFrustum_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIReference_1_SpatialBoundingFrustum_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingFrustum > methods ***/
#define __FIReference_1_SpatialBoundingFrustum_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingFrustum_QueryInterface(__FIReference_1_SpatialBoundingFrustum* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIReference_1_SpatialBoundingFrustum_AddRef(__FIReference_1_SpatialBoundingFrustum* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIReference_1_SpatialBoundingFrustum_Release(__FIReference_1_SpatialBoundingFrustum* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingFrustum_GetIids(__FIReference_1_SpatialBoundingFrustum* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIReference_1_SpatialBoundingFrustum_GetRuntimeClassName(__FIReference_1_SpatialBoundingFrustum* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIReference_1_SpatialBoundingFrustum_GetTrustLevel(__FIReference_1_SpatialBoundingFrustum* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingFrustum > methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingFrustum_get_Value(__FIReference_1_SpatialBoundingFrustum* This,__x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingFrustum *value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IReference_SpatialBoundingFrustum IID___FIReference_1_SpatialBoundingFrustum
#define IReference_SpatialBoundingFrustumVtbl __FIReference_1_SpatialBoundingFrustumVtbl
#define IReference_SpatialBoundingFrustum __FIReference_1_SpatialBoundingFrustum
#define IReference_SpatialBoundingFrustum_QueryInterface __FIReference_1_SpatialBoundingFrustum_QueryInterface
#define IReference_SpatialBoundingFrustum_AddRef __FIReference_1_SpatialBoundingFrustum_AddRef
#define IReference_SpatialBoundingFrustum_Release __FIReference_1_SpatialBoundingFrustum_Release
#define IReference_SpatialBoundingFrustum_GetIids __FIReference_1_SpatialBoundingFrustum_GetIids
#define IReference_SpatialBoundingFrustum_GetRuntimeClassName __FIReference_1_SpatialBoundingFrustum_GetRuntimeClassName
#define IReference_SpatialBoundingFrustum_GetTrustLevel __FIReference_1_SpatialBoundingFrustum_GetTrustLevel
#define IReference_SpatialBoundingFrustum_get_Value __FIReference_1_SpatialBoundingFrustum_get_Value
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIReference_1_SpatialBoundingFrustum_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IReference<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox > interface
 */
#ifndef ____FIReference_1_SpatialBoundingOrientedBox_INTERFACE_DEFINED__
#define ____FIReference_1_SpatialBoundingOrientedBox_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIReference_1_SpatialBoundingOrientedBox, 0x09f88309, 0x9f81, 0x5207, 0xbd,0xb2, 0xab,0xef,0x92,0x6d,0xb1,0x8f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("09f88309-9f81-5207-bdb2-abef926db18f")
            IReference<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox > : IReference_impl<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIReference_1_SpatialBoundingOrientedBox, 0x09f88309, 0x9f81, 0x5207, 0xbd,0xb2, 0xab,0xef,0x92,0x6d,0xb1,0x8f)
#endif
#else
typedef struct __FIReference_1_SpatialBoundingOrientedBoxVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIReference_1_SpatialBoundingOrientedBox *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIReference_1_SpatialBoundingOrientedBox *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIReference_1_SpatialBoundingOrientedBox *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIReference_1_SpatialBoundingOrientedBox *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIReference_1_SpatialBoundingOrientedBox *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIReference_1_SpatialBoundingOrientedBox *This,
        TrustLevel *trustLevel);

    /*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIReference_1_SpatialBoundingOrientedBox *This,
        __x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingOrientedBox *value);

    END_INTERFACE
} __FIReference_1_SpatialBoundingOrientedBoxVtbl;

interface __FIReference_1_SpatialBoundingOrientedBox {
    CONST_VTBL __FIReference_1_SpatialBoundingOrientedBoxVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIReference_1_SpatialBoundingOrientedBox_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIReference_1_SpatialBoundingOrientedBox_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIReference_1_SpatialBoundingOrientedBox_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIReference_1_SpatialBoundingOrientedBox_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIReference_1_SpatialBoundingOrientedBox_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIReference_1_SpatialBoundingOrientedBox_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox > methods ***/
#define __FIReference_1_SpatialBoundingOrientedBox_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingOrientedBox_QueryInterface(__FIReference_1_SpatialBoundingOrientedBox* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIReference_1_SpatialBoundingOrientedBox_AddRef(__FIReference_1_SpatialBoundingOrientedBox* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIReference_1_SpatialBoundingOrientedBox_Release(__FIReference_1_SpatialBoundingOrientedBox* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingOrientedBox_GetIids(__FIReference_1_SpatialBoundingOrientedBox* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIReference_1_SpatialBoundingOrientedBox_GetRuntimeClassName(__FIReference_1_SpatialBoundingOrientedBox* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIReference_1_SpatialBoundingOrientedBox_GetTrustLevel(__FIReference_1_SpatialBoundingOrientedBox* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IReference<ABI::Windows::Perception::Spatial::SpatialBoundingOrientedBox > methods ***/
static inline HRESULT __FIReference_1_SpatialBoundingOrientedBox_get_Value(__FIReference_1_SpatialBoundingOrientedBox* This,__x_ABI_CWindows_CPerception_CSpatial_CSpatialBoundingOrientedBox *value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IReference_SpatialBoundingOrientedBox IID___FIReference_1_SpatialBoundingOrientedBox
#define IReference_SpatialBoundingOrientedBoxVtbl __FIReference_1_SpatialBoundingOrientedBoxVtbl
#define IReference_SpatialBoundingOrientedBox __FIReference_1_SpatialBoundingOrientedBox
#define IReference_SpatialBoundingOrientedBox_QueryInterface __FIReference_1_SpatialBoundingOrientedBox_QueryInterface
#define IReference_SpatialBoundingOrientedBox_AddRef __FIReference_1_SpatialBoundingOrientedBox_AddRef
#define IReference_SpatialBoundingOrientedBox_Release __FIReference_1_SpatialBoundingOrientedBox_Release
#define IReference_SpatialBoundingOrientedBox_GetIids __FIReference_1_SpatialBoundingOrientedBox_GetIids
#define IReference_SpatialBoundingOrientedBox_GetRuntimeClassName __FIReference_1_SpatialBoundingOrientedBox_GetRuntimeClassName
#define IReference_SpatialBoundingOrientedBox_GetTrustLevel __FIReference_1_SpatialBoundingOrientedBox_GetTrustLevel
#define IReference_SpatialBoundingOrientedBox_get_Value __FIReference_1_SpatialBoundingOrientedBox_get_Value
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIReference_1_SpatialBoundingOrientedBox_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_perception_spatial_h__ */
