/*** Autogenerated by WIDL 10.8 from include/windows.applicationmodel.core.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_applicationmodel_core_h__
#define __windows_applicationmodel_core_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry ABI::Windows::ApplicationModel::Core::IAppListEntry
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IAppListEntry;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication ABI::Windows::ApplicationModel::Core::ICoreApplication
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplication;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 ABI::Windows::ApplicationModel::Core::ICoreApplication2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplication2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 ABI::Windows::ApplicationModel::Core::ICoreApplication3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplication3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit ABI::Windows::ApplicationModel::Core::ICoreApplicationExit
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationExit;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError ABI::Windows::ApplicationModel::Core::ICoreApplicationUnhandledError
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationUnhandledError;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount ABI::Windows::ApplicationModel::Core::ICoreApplicationUseCount
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationUseCount;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView ABI::Windows::ApplicationModel::Core::ICoreApplicationView
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 ABI::Windows::ApplicationModel::Core::ICoreApplicationView2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 ABI::Windows::ApplicationModel::Core::ICoreApplicationView3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 ABI::Windows::ApplicationModel::Core::ICoreApplicationView5
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView5;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 ABI::Windows::ApplicationModel::Core::ICoreApplicationView6
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView6;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar ABI::Windows::ApplicationModel::Core::ICoreApplicationViewTitleBar
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationViewTitleBar;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication ABI::Windows::ApplicationModel::Core::ICoreImmersiveApplication
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreImmersiveApplication;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 ABI::Windows::ApplicationModel::Core::ICoreImmersiveApplication2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreImmersiveApplication2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 ABI::Windows::ApplicationModel::Core::ICoreImmersiveApplication3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreImmersiveApplication3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView ABI::Windows::ApplicationModel::Core::IFrameworkView
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IFrameworkView;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource ABI::Windows::ApplicationModel::Core::IFrameworkViewSource
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IFrameworkViewSource;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs ABI::Windows::ApplicationModel::Core::IHostedViewClosingEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IHostedViewClosingEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError ABI::Windows::ApplicationModel::Core::IUnhandledError
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IUnhandledError;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs ABI::Windows::ApplicationModel::Core::IUnhandledErrorDetectedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IUnhandledErrorDetectedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CAppListEntry_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CAppListEntry_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                class AppListEntry;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CAppListEntry __x_ABI_CWindows_CApplicationModel_CCore_CAppListEntry;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CCore_CAppListEntry_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplication_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplication_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                class CoreApplication;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CCoreApplication __x_ABI_CWindows_CApplicationModel_CCore_CCoreApplication;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplication_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationView_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationView_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                class CoreApplicationView;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationView __x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationView;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationView_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationViewTitleBar_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationViewTitleBar_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                class CoreApplicationViewTitleBar;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationViewTitleBar __x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationViewTitleBar;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CCore_CCoreApplicationViewTitleBar_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CHostedViewClosingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CHostedViewClosingEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                class HostedViewClosingEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CHostedViewClosingEventArgs __x_ABI_CWindows_CApplicationModel_CCore_CHostedViewClosingEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CCore_CHostedViewClosingEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CUnhandledError_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CUnhandledError_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                class UnhandledError;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CUnhandledError __x_ABI_CWindows_CApplicationModel_CCore_CUnhandledError;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CCore_CUnhandledError_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CUnhandledErrorDetectedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CUnhandledErrorDetectedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                class UnhandledErrorDetectedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CUnhandledErrorDetectedEventArgs __x_ABI_CWindows_CApplicationModel_CCore_CUnhandledErrorDetectedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CApplicationModel_CCore_CUnhandledErrorDetectedEventArgs_FWD_DEFINED__ */

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::CoreApplicationView* >
#endif /* __cplusplus */
#endif

#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_FWD_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_FWD_DEFINED__
typedef interface __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs;
#ifdef __cplusplus
#define __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::Core::UnhandledErrorDetectedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_AppRestartFailureReason_FWD_DEFINED__
#define ____FIAsyncOperation_1_AppRestartFailureReason_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_AppRestartFailureReason __FIAsyncOperation_1_AppRestartFailureReason;
#ifdef __cplusplus
#define __FIAsyncOperation_1_AppRestartFailureReason ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Core::HostedViewClosingEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*,IInspectable* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <windows.foundation.h>
#include <windows.storage.h>
#include <windows.system.h>
#include <windows.applicationmodel.h>
#include <windows.applicationmodel.activation.h>
#include <windows.ui.core.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Activation {
                interface IActivatedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CFoundation_CIGetActivationFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CFoundation_CIGetActivationFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CFoundation_CIGetActivationFactory __x_ABI_CWindows_CFoundation_CIGetActivationFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CFoundation_CIGetActivationFactory ABI::Windows::Foundation::IGetActivationFactory
namespace ABI {
    namespace Windows {
        namespace Foundation {
            interface IGetActivationFactory;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CApplicationModel_CCore_CAppRestartFailureReason __x_ABI_CWindows_CApplicationModel_CCore_CAppRestartFailureReason;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry ABI::Windows::ApplicationModel::Core::IAppListEntry
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IAppListEntry;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication ABI::Windows::ApplicationModel::Core::ICoreApplication
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplication;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 ABI::Windows::ApplicationModel::Core::ICoreApplication2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplication2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 ABI::Windows::ApplicationModel::Core::ICoreApplication3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplication3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit ABI::Windows::ApplicationModel::Core::ICoreApplicationExit
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationExit;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError ABI::Windows::ApplicationModel::Core::ICoreApplicationUnhandledError
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationUnhandledError;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount ABI::Windows::ApplicationModel::Core::ICoreApplicationUseCount
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationUseCount;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView ABI::Windows::ApplicationModel::Core::ICoreApplicationView
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 ABI::Windows::ApplicationModel::Core::ICoreApplicationView2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 ABI::Windows::ApplicationModel::Core::ICoreApplicationView3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 ABI::Windows::ApplicationModel::Core::ICoreApplicationView5
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView5;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 ABI::Windows::ApplicationModel::Core::ICoreApplicationView6
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationView6;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar ABI::Windows::ApplicationModel::Core::ICoreApplicationViewTitleBar
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreApplicationViewTitleBar;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication ABI::Windows::ApplicationModel::Core::ICoreImmersiveApplication
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreImmersiveApplication;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 ABI::Windows::ApplicationModel::Core::ICoreImmersiveApplication2
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreImmersiveApplication2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 ABI::Windows::ApplicationModel::Core::ICoreImmersiveApplication3
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface ICoreImmersiveApplication3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView ABI::Windows::ApplicationModel::Core::IFrameworkView
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IFrameworkView;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource ABI::Windows::ApplicationModel::Core::IFrameworkViewSource
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IFrameworkViewSource;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs ABI::Windows::ApplicationModel::Core::IHostedViewClosingEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IHostedViewClosingEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError ABI::Windows::ApplicationModel::Core::IUnhandledError
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IUnhandledError;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs ABI::Windows::ApplicationModel::Core::IUnhandledErrorDetectedEventArgs
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                interface IUnhandledErrorDetectedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::CoreApplicationView* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_AppRestartFailureReason_FWD_DEFINED__
#define ____FIAsyncOperation_1_AppRestartFailureReason_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_AppRestartFailureReason __FIAsyncOperation_1_AppRestartFailureReason;
#ifdef __cplusplus
#define __FIAsyncOperation_1_AppRestartFailureReason ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                enum AppRestartFailureReason {
                    AppRestartFailureReason_RestartPending = 0,
                    AppRestartFailureReason_NotInForeground = 1,
                    AppRestartFailureReason_InvalidUser = 2,
                    AppRestartFailureReason_Other = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CApplicationModel_CCore_CAppRestartFailureReason {
    AppRestartFailureReason_RestartPending = 0,
    AppRestartFailureReason_NotInForeground = 1,
    AppRestartFailureReason_InvalidUser = 2,
    AppRestartFailureReason_Other = 3
};
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define AppRestartFailureReason __x_ABI_CWindows_CApplicationModel_CCore_CAppRestartFailureReason
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
/*****************************************************************************
 * IAppListEntry interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry, 0xef00f07f, 0x2108, 0x490a, 0x87,0x7a, 0x8a,0x9f,0x17,0xc2,0x5f,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("ef00f07f-2108-490a-877a-8a9f17c25fad")
                IAppListEntry : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DisplayInfo(
                        ABI::Windows::ApplicationModel::IAppDisplayInfo **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE LaunchAsync(
                        ABI::Windows::Foundation::IAsyncOperation<boolean > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry, 0xef00f07f, 0x2108, 0x490a, 0x87,0x7a, 0x8a,0x9f,0x17,0xc2,0x5f,0xad)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *This,
        TrustLevel *trustLevel);

    /*** IAppListEntry methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DisplayInfo)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *This,
        __x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo **value);

    HRESULT (STDMETHODCALLTYPE *LaunchAsync)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry *This,
        __FIAsyncOperation_1_boolean **operation);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntryVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAppListEntry methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_get_DisplayInfo(This,value) (This)->lpVtbl->get_DisplayInfo(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_LaunchAsync(This,operation) (This)->lpVtbl->LaunchAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_Release(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAppListEntry methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_get_DisplayInfo(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry* This,__x_ABI_CWindows_CApplicationModel_CIAppDisplayInfo **value) {
    return This->lpVtbl->get_DisplayInfo(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_LaunchAsync(__x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry* This,__FIAsyncOperation_1_boolean **operation) {
    return This->lpVtbl->LaunchAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_IAppListEntry IID___x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry
#define IAppListEntryVtbl __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntryVtbl
#define IAppListEntry __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry
#define IAppListEntry_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_QueryInterface
#define IAppListEntry_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_AddRef
#define IAppListEntry_Release __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_Release
#define IAppListEntry_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetIids
#define IAppListEntry_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetRuntimeClassName
#define IAppListEntry_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_GetTrustLevel
#define IAppListEntry_get_DisplayInfo __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_get_DisplayInfo
#define IAppListEntry_LaunchAsync __x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_LaunchAsync
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CIAppListEntry_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreApplication interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication, 0x0aacf7a4, 0x5e1d, 0x49df, 0x80,0x34, 0xfb,0x6a,0x68,0xbc,0x5e,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("0aacf7a4-5e1d-49df-8034-fb6a68bc5ed1")
                ICoreApplication : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Suspending(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Suspending(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Resuming(
                        ABI::Windows::Foundation::IEventHandler<IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Resuming(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Properties(
                        ABI::Windows::Foundation::Collections::IPropertySet **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentView(
                        ABI::Windows::ApplicationModel::Core::ICoreApplicationView **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Run(
                        ABI::Windows::ApplicationModel::Core::IFrameworkViewSource *view_source) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RunWithActivationFactories(
                        ABI::Windows::Foundation::IGetActivationFactory *factory) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication, 0x0aacf7a4, 0x5e1d, 0x49df, 0x80,0x34, 0xfb,0x6a,0x68,0xbc,0x5e,0xd1)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        TrustLevel *trustLevel);

    /*** ICoreApplication methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *add_Suspending)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Suspending)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_Resuming)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        __FIEventHandler_1_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Resuming)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        __x_ABI_CWindows_CFoundation_CCollections_CIPropertySet **value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentView)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **value);

    HRESULT (STDMETHODCALLTYPE *Run)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *view_source);

    HRESULT (STDMETHODCALLTYPE *RunWithActivationFactories)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication *This,
        __x_ABI_CWindows_CFoundation_CIGetActivationFactory *factory);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplication methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_add_Suspending(This,handler,token) (This)->lpVtbl->add_Suspending(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_remove_Suspending(This,token) (This)->lpVtbl->remove_Suspending(This,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_add_Resuming(This,handler,token) (This)->lpVtbl->add_Resuming(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_remove_Resuming(This,token) (This)->lpVtbl->remove_Resuming(This,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_get_Properties(This,value) (This)->lpVtbl->get_Properties(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetCurrentView(This,value) (This)->lpVtbl->GetCurrentView(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_Run(This,view_source) (This)->lpVtbl->Run(This,view_source)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_RunWithActivationFactories(This,factory) (This)->lpVtbl->RunWithActivationFactories(This,factory)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplication methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_get_Id(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_add_Suspending(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Suspending(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_remove_Suspending(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Suspending(This,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_add_Resuming(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,__FIEventHandler_1_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Resuming(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_remove_Resuming(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Resuming(This,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_get_Properties(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,__x_ABI_CWindows_CFoundation_CCollections_CIPropertySet **value) {
    return This->lpVtbl->get_Properties(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetCurrentView(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **value) {
    return This->lpVtbl->GetCurrentView(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_Run(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *view_source) {
    return This->lpVtbl->Run(This,view_source);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_RunWithActivationFactories(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication* This,__x_ABI_CWindows_CFoundation_CIGetActivationFactory *factory) {
    return This->lpVtbl->RunWithActivationFactories(This,factory);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplication IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication
#define ICoreApplicationVtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationVtbl
#define ICoreApplication __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication
#define ICoreApplication_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_QueryInterface
#define ICoreApplication_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_AddRef
#define ICoreApplication_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_Release
#define ICoreApplication_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetIids
#define ICoreApplication_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetRuntimeClassName
#define ICoreApplication_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetTrustLevel
#define ICoreApplication_get_Id __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_get_Id
#define ICoreApplication_add_Suspending __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_add_Suspending
#define ICoreApplication_remove_Suspending __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_remove_Suspending
#define ICoreApplication_add_Resuming __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_add_Resuming
#define ICoreApplication_remove_Resuming __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_remove_Resuming
#define ICoreApplication_get_Properties __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_get_Properties
#define ICoreApplication_GetCurrentView __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_GetCurrentView
#define ICoreApplication_Run __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_Run
#define ICoreApplication_RunWithActivationFactories __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_RunWithActivationFactories
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreApplication2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2, 0x998681fb, 0x1ab6, 0x4b7f, 0xbe,0x4a, 0x9a,0x06,0x45,0x22,0x4c,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("998681fb-1ab6-4b7f-be4a-9a0645224c04")
                ICoreApplication2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_BackgroundActivated(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_BackgroundActivated(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_LeavingBackground(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_LeavingBackground(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_EnteredBackground(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_EnteredBackground(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE EnablePrelaunch(
                        boolean value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2, 0x998681fb, 0x1ab6, 0x4b7f, 0xbe,0x4a, 0x9a,0x06,0x45,0x22,0x4c,0x04)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        TrustLevel *trustLevel);

    /*** ICoreApplication2 methods ***/
    HRESULT (STDMETHODCALLTYPE *add_BackgroundActivated)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_BackgroundActivated)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_LeavingBackground)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_LeavingBackground)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_EnteredBackground)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_EnteredBackground)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *EnablePrelaunch)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplication2 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_BackgroundActivated(This,handler,token) (This)->lpVtbl->add_BackgroundActivated(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_BackgroundActivated(This,token) (This)->lpVtbl->remove_BackgroundActivated(This,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_LeavingBackground(This,handler,token) (This)->lpVtbl->add_LeavingBackground(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_LeavingBackground(This,token) (This)->lpVtbl->remove_LeavingBackground(This,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_EnteredBackground(This,handler,token) (This)->lpVtbl->add_EnteredBackground(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_EnteredBackground(This,token) (This)->lpVtbl->remove_EnteredBackground(This,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_EnablePrelaunch(This,value) (This)->lpVtbl->EnablePrelaunch(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplication2 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_BackgroundActivated(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_BackgroundActivated(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_BackgroundActivated(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_BackgroundActivated(This,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_LeavingBackground(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_LeavingBackground(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_LeavingBackground(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_LeavingBackground(This,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_EnteredBackground(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_EnteredBackground(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_EnteredBackground(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_EnteredBackground(This,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_EnablePrelaunch(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2* This,boolean value) {
    return This->lpVtbl->EnablePrelaunch(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplication2 IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2
#define ICoreApplication2Vtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2Vtbl
#define ICoreApplication2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2
#define ICoreApplication2_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_QueryInterface
#define ICoreApplication2_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_AddRef
#define ICoreApplication2_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_Release
#define ICoreApplication2_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetIids
#define ICoreApplication2_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetRuntimeClassName
#define ICoreApplication2_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_GetTrustLevel
#define ICoreApplication2_add_BackgroundActivated __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_BackgroundActivated
#define ICoreApplication2_remove_BackgroundActivated __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_BackgroundActivated
#define ICoreApplication2_add_LeavingBackground __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_LeavingBackground
#define ICoreApplication2_remove_LeavingBackground __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_LeavingBackground
#define ICoreApplication2_add_EnteredBackground __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_add_EnteredBackground
#define ICoreApplication2_remove_EnteredBackground __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_remove_EnteredBackground
#define ICoreApplication2_EnablePrelaunch __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_EnablePrelaunch
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * ICoreApplication3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3, 0xfeec0d39, 0x598b, 0x4507, 0x8a,0x67, 0x77,0x26,0x32,0x58,0x0a,0x57);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("feec0d39-598b-4507-8a67-772632580a57")
                ICoreApplication3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE RequestRestartAsync(
                        HSTRING launch_arguments,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RequestRestartForUserAsync(
                        ABI::Windows::System::IUser *user,
                        HSTRING launch_arguments,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3, 0xfeec0d39, 0x598b, 0x4507, 0x8a,0x67, 0x77,0x26,0x32,0x58,0x0a,0x57)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 *This,
        TrustLevel *trustLevel);

    /*** ICoreApplication3 methods ***/
    HRESULT (STDMETHODCALLTYPE *RequestRestartAsync)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 *This,
        HSTRING launch_arguments,
        __FIAsyncOperation_1_AppRestartFailureReason **operation);

    HRESULT (STDMETHODCALLTYPE *RequestRestartForUserAsync)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        HSTRING launch_arguments,
        __FIAsyncOperation_1_AppRestartFailureReason **operation);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplication3 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_RequestRestartAsync(This,launch_arguments,operation) (This)->lpVtbl->RequestRestartAsync(This,launch_arguments,operation)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_RequestRestartForUserAsync(This,user,launch_arguments,operation) (This)->lpVtbl->RequestRestartForUserAsync(This,user,launch_arguments,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplication3 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_RequestRestartAsync(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3* This,HSTRING launch_arguments,__FIAsyncOperation_1_AppRestartFailureReason **operation) {
    return This->lpVtbl->RequestRestartAsync(This,launch_arguments,operation);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_RequestRestartForUserAsync(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3* This,__x_ABI_CWindows_CSystem_CIUser *user,HSTRING launch_arguments,__FIAsyncOperation_1_AppRestartFailureReason **operation) {
    return This->lpVtbl->RequestRestartForUserAsync(This,user,launch_arguments,operation);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplication3 IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3
#define ICoreApplication3Vtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3Vtbl
#define ICoreApplication3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3
#define ICoreApplication3_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_QueryInterface
#define ICoreApplication3_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_AddRef
#define ICoreApplication3_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_Release
#define ICoreApplication3_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetIids
#define ICoreApplication3_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetRuntimeClassName
#define ICoreApplication3_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_GetTrustLevel
#define ICoreApplication3_RequestRestartAsync __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_RequestRestartAsync
#define ICoreApplication3_RequestRestartForUserAsync __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_RequestRestartForUserAsync
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplication3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ICoreApplicationExit interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit, 0xcf86461d, 0x261e, 0x4b72, 0x9a,0xcd, 0x44,0xed,0x2a,0xce,0x6a,0x29);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("cf86461d-261e-4b72-9acd-44ed2ace6a29")
                ICoreApplicationExit : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Exit(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Exiting(
                        ABI::Windows::Foundation::IEventHandler<IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Exiting(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit, 0xcf86461d, 0x261e, 0x4b72, 0x9a,0xcd, 0x44,0xed,0x2a,0xce,0x6a,0x29)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExitVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationExit methods ***/
    HRESULT (STDMETHODCALLTYPE *Exit)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This);

    HRESULT (STDMETHODCALLTYPE *add_Exiting)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This,
        __FIEventHandler_1_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Exiting)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExitVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExitVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationExit methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_Exit(This) (This)->lpVtbl->Exit(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_add_Exiting(This,handler,token) (This)->lpVtbl->add_Exiting(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_remove_Exiting(This,token) (This)->lpVtbl->remove_Exiting(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationExit methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_Exit(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This) {
    return This->lpVtbl->Exit(This);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_add_Exiting(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This,__FIEventHandler_1_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Exiting(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_remove_Exiting(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Exiting(This,token);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationExit IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit
#define ICoreApplicationExitVtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExitVtbl
#define ICoreApplicationExit __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit
#define ICoreApplicationExit_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_QueryInterface
#define ICoreApplicationExit_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_AddRef
#define ICoreApplicationExit_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_Release
#define ICoreApplicationExit_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetIids
#define ICoreApplicationExit_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetRuntimeClassName
#define ICoreApplicationExit_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_GetTrustLevel
#define ICoreApplicationExit_Exit __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_Exit
#define ICoreApplicationExit_add_Exiting __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_add_Exiting
#define ICoreApplicationExit_remove_Exiting __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_remove_Exiting
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationExit_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreApplicationUnhandledError interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError, 0xf0e24ab0, 0xdd09, 0x42e1, 0xb0,0xbc, 0xe0,0xe1,0x31,0xf7,0x8d,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("f0e24ab0-dd09-42e1-b0bc-e0e131f78d7e")
                ICoreApplicationUnhandledError : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_UnhandledErrorDetected(
                        ABI::Windows::Foundation::IEventHandler<ABI::Windows::ApplicationModel::Core::UnhandledErrorDetectedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_UnhandledErrorDetected(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError, 0xf0e24ab0, 0xdd09, 0x42e1, 0xb0,0xbc, 0xe0,0xe1,0x31,0xf7,0x8d,0x7e)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationUnhandledError methods ***/
    HRESULT (STDMETHODCALLTYPE *add_UnhandledErrorDetected)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError *This,
        __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_UnhandledErrorDetected)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledErrorVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationUnhandledError methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_add_UnhandledErrorDetected(This,handler,token) (This)->lpVtbl->add_UnhandledErrorDetected(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_remove_UnhandledErrorDetected(This,token) (This)->lpVtbl->remove_UnhandledErrorDetected(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationUnhandledError methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_add_UnhandledErrorDetected(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError* This,__FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_UnhandledErrorDetected(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_remove_UnhandledErrorDetected(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_UnhandledErrorDetected(This,token);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationUnhandledError IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError
#define ICoreApplicationUnhandledErrorVtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledErrorVtbl
#define ICoreApplicationUnhandledError __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError
#define ICoreApplicationUnhandledError_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_QueryInterface
#define ICoreApplicationUnhandledError_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_AddRef
#define ICoreApplicationUnhandledError_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_Release
#define ICoreApplicationUnhandledError_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetIids
#define ICoreApplicationUnhandledError_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetRuntimeClassName
#define ICoreApplicationUnhandledError_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_GetTrustLevel
#define ICoreApplicationUnhandledError_add_UnhandledErrorDetected __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_add_UnhandledErrorDetected
#define ICoreApplicationUnhandledError_remove_UnhandledErrorDetected __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_remove_UnhandledErrorDetected
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUnhandledError_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreApplicationUseCount interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount, 0x518dc408, 0xc077, 0x475b, 0x80,0x9e, 0x0b,0xc0,0xc5,0x7e,0x4b,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("518dc408-c077-475b-809e-0bc0c57e4b74")
                ICoreApplicationUseCount : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE IncrementApplicationUseCount(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE DecrementApplicationUseCount(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount, 0x518dc408, 0xc077, 0x475b, 0x80,0x9e, 0x0b,0xc0,0xc5,0x7e,0x4b,0x74)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCountVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationUseCount methods ***/
    HRESULT (STDMETHODCALLTYPE *IncrementApplicationUseCount)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount *This);

    HRESULT (STDMETHODCALLTYPE *DecrementApplicationUseCount)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount *This);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCountVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCountVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationUseCount methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_IncrementApplicationUseCount(This) (This)->lpVtbl->IncrementApplicationUseCount(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_DecrementApplicationUseCount(This) (This)->lpVtbl->DecrementApplicationUseCount(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationUseCount methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_IncrementApplicationUseCount(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount* This) {
    return This->lpVtbl->IncrementApplicationUseCount(This);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_DecrementApplicationUseCount(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount* This) {
    return This->lpVtbl->DecrementApplicationUseCount(This);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationUseCount IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount
#define ICoreApplicationUseCountVtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCountVtbl
#define ICoreApplicationUseCount __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount
#define ICoreApplicationUseCount_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_QueryInterface
#define ICoreApplicationUseCount_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_AddRef
#define ICoreApplicationUseCount_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_Release
#define ICoreApplicationUseCount_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetIids
#define ICoreApplicationUseCount_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetRuntimeClassName
#define ICoreApplicationUseCount_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_GetTrustLevel
#define ICoreApplicationUseCount_IncrementApplicationUseCount __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_IncrementApplicationUseCount
#define ICoreApplicationUseCount_DecrementApplicationUseCount __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_DecrementApplicationUseCount
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationUseCount_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreApplicationView interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView, 0x638bb2db, 0x451d, 0x4661, 0xb0,0x99, 0x41,0x4f,0x34,0xff,0xb9,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("638bb2db-451d-4661-b099-414f34ffb9f1")
                ICoreApplicationView : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_CoreWindow(
                        ABI::Windows::UI::Core::ICoreWindow **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Activated(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Activated(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsMain(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsHosted(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView, 0x638bb2db, 0x451d, 0x4661, 0xb0,0x99, 0x41,0x4f,0x34,0xff,0xb9,0xf1)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationView methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CoreWindow)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow **value);

    HRESULT (STDMETHODCALLTYPE *add_Activated)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Activated)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_IsMain)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsHosted)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationView methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_CoreWindow(This,value) (This)->lpVtbl->get_CoreWindow(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_add_Activated(This,handler,token) (This)->lpVtbl->add_Activated(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_remove_Activated(This,token) (This)->lpVtbl->remove_Activated(This,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_IsMain(This,value) (This)->lpVtbl->get_IsMain(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_IsHosted(This,value) (This)->lpVtbl->get_IsHosted(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationView methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_CoreWindow(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow **value) {
    return This->lpVtbl->get_CoreWindow(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_add_Activated(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Activated(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_remove_Activated(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Activated(This,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_IsMain(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,boolean *value) {
    return This->lpVtbl->get_IsMain(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_IsHosted(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView* This,boolean *value) {
    return This->lpVtbl->get_IsHosted(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationView IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView
#define ICoreApplicationViewVtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewVtbl
#define ICoreApplicationView __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView
#define ICoreApplicationView_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_QueryInterface
#define ICoreApplicationView_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_AddRef
#define ICoreApplicationView_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_Release
#define ICoreApplicationView_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetIids
#define ICoreApplicationView_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetRuntimeClassName
#define ICoreApplicationView_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_GetTrustLevel
#define ICoreApplicationView_get_CoreWindow __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_CoreWindow
#define ICoreApplicationView_add_Activated __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_add_Activated
#define ICoreApplicationView_remove_Activated __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_remove_Activated
#define ICoreApplicationView_get_IsMain __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_IsMain
#define ICoreApplicationView_get_IsHosted __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_get_IsHosted
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreApplicationView2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2, 0x68eb7adf, 0x917f, 0x48eb, 0x9a,0xeb, 0x7d,0xe5,0x3e,0x08,0x6a,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("68eb7adf-917f-48eb-9aeb-7de53e086ab1")
                ICoreApplicationView2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Dispatcher(
                        ABI::Windows::UI::Core::ICoreDispatcher **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2, 0x68eb7adf, 0x917f, 0x48eb, 0x9a,0xeb, 0x7d,0xe5,0x3e,0x08,0x6a,0xb1)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationView2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Dispatcher)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 *This,
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationView2 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_get_Dispatcher(This,value) (This)->lpVtbl->get_Dispatcher(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationView2 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_get_Dispatcher(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2* This,__x_ABI_CWindows_CUI_CCore_CICoreDispatcher **value) {
    return This->lpVtbl->get_Dispatcher(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationView2 IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2
#define ICoreApplicationView2Vtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2Vtbl
#define ICoreApplicationView2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2
#define ICoreApplicationView2_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_QueryInterface
#define ICoreApplicationView2_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_AddRef
#define ICoreApplicationView2_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_Release
#define ICoreApplicationView2_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetIids
#define ICoreApplicationView2_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetRuntimeClassName
#define ICoreApplicationView2_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_GetTrustLevel
#define ICoreApplicationView2_get_Dispatcher __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_get_Dispatcher
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreApplicationView3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3, 0x07ebe1b3, 0xa4cf, 0x4550, 0xab,0x70, 0xb0,0x7e,0x85,0x33,0x0b,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("07ebe1b3-a4cf-4550-ab70-b07e85330bc8")
                ICoreApplicationView3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IsComponent(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_TitleBar(
                        ABI::Windows::ApplicationModel::Core::ICoreApplicationViewTitleBar **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_HostedViewClosing(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Core::HostedViewClosingEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_HostedViewClosing(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3, 0x07ebe1b3, 0xa4cf, 0x4550, 0xab,0x70, 0xb0,0x7e,0x85,0x33,0x0b,0xc8)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationView3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsComponent)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_TitleBar)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar **value);

    HRESULT (STDMETHODCALLTYPE *add_HostedViewClosing)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This,
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_HostedViewClosing)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationView3 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_get_IsComponent(This,value) (This)->lpVtbl->get_IsComponent(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_get_TitleBar(This,value) (This)->lpVtbl->get_TitleBar(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_add_HostedViewClosing(This,handler,token) (This)->lpVtbl->add_HostedViewClosing(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_remove_HostedViewClosing(This,token) (This)->lpVtbl->remove_HostedViewClosing(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationView3 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_get_IsComponent(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This,boolean *value) {
    return This->lpVtbl->get_IsComponent(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_get_TitleBar(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar **value) {
    return This->lpVtbl->get_TitleBar(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_add_HostedViewClosing(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This,__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_HostedViewClosing(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_remove_HostedViewClosing(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_HostedViewClosing(This,token);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationView3 IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3
#define ICoreApplicationView3Vtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3Vtbl
#define ICoreApplicationView3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3
#define ICoreApplicationView3_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_QueryInterface
#define ICoreApplicationView3_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_AddRef
#define ICoreApplicationView3_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_Release
#define ICoreApplicationView3_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetIids
#define ICoreApplicationView3_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetRuntimeClassName
#define ICoreApplicationView3_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_GetTrustLevel
#define ICoreApplicationView3_get_IsComponent __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_get_IsComponent
#define ICoreApplicationView3_get_TitleBar __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_get_TitleBar
#define ICoreApplicationView3_add_HostedViewClosing __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_add_HostedViewClosing
#define ICoreApplicationView3_remove_HostedViewClosing __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_remove_HostedViewClosing
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreApplicationView5 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5, 0x2bc095a8, 0x8ef0, 0x446d, 0x9e,0x60, 0x3a,0x3e,0x04,0x28,0xc6,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("2bc095a8-8ef0-446d-9e60-3a3e0428c671")
                ICoreApplicationView5 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Properties(
                        ABI::Windows::Foundation::Collections::IPropertySet **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5, 0x2bc095a8, 0x8ef0, 0x446d, 0x9e,0x60, 0x3a,0x3e,0x04,0x28,0xc6,0x71)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationView5 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 *This,
        __x_ABI_CWindows_CFoundation_CCollections_CIPropertySet **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationView5 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_get_Properties(This,value) (This)->lpVtbl->get_Properties(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationView5 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_get_Properties(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5* This,__x_ABI_CWindows_CFoundation_CCollections_CIPropertySet **value) {
    return This->lpVtbl->get_Properties(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationView5 IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5
#define ICoreApplicationView5Vtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5Vtbl
#define ICoreApplicationView5 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5
#define ICoreApplicationView5_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_QueryInterface
#define ICoreApplicationView5_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_AddRef
#define ICoreApplicationView5_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_Release
#define ICoreApplicationView5_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetIids
#define ICoreApplicationView5_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetRuntimeClassName
#define ICoreApplicationView5_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_GetTrustLevel
#define ICoreApplicationView5_get_Properties __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_get_Properties
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView5_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * ICoreApplicationView6 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6, 0xc119d49a, 0x0679, 0x49ba, 0x80,0x3f, 0xb7,0x9c,0x5c,0xf3,0x4c,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("c119d49a-0679-49ba-803f-b79c5cf34cca")
                ICoreApplicationView6 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DispatcherQueue(
                        ABI::Windows::System::IDispatcherQueue **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6, 0xc119d49a, 0x0679, 0x49ba, 0x80,0x3f, 0xb7,0x9c,0x5c,0xf3,0x4c,0xca)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationView6 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DispatcherQueue)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueue **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationView6 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_get_DispatcherQueue(This,value) (This)->lpVtbl->get_DispatcherQueue(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationView6 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_get_DispatcherQueue(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6* This,__x_ABI_CWindows_CSystem_CIDispatcherQueue **value) {
    return This->lpVtbl->get_DispatcherQueue(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationView6 IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6
#define ICoreApplicationView6Vtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6Vtbl
#define ICoreApplicationView6 __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6
#define ICoreApplicationView6_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_QueryInterface
#define ICoreApplicationView6_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_AddRef
#define ICoreApplicationView6_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_Release
#define ICoreApplicationView6_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetIids
#define ICoreApplicationView6_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetRuntimeClassName
#define ICoreApplicationView6_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_GetTrustLevel
#define ICoreApplicationView6_get_DispatcherQueue __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_get_DispatcherQueue
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView6_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ICoreApplicationViewTitleBar interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar, 0x006d35e3, 0xe1f1, 0x431b, 0x95,0x08, 0x29,0xb9,0x69,0x26,0xac,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("006d35e3-e1f1-431b-9508-29b96926ac53")
                ICoreApplicationViewTitleBar : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE put_ExtendViewIntoTitleBar(
                        boolean value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ExtendViewIntoTitleBar(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SystemOverlayLeftInset(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SystemOverlayRightInset(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Height(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_LayoutMetricsChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_LayoutMetricsChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsVisible(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_IsVisibleChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_IsVisibleChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar, 0x006d35e3, 0xe1f1, 0x431b, 0x95,0x08, 0x29,0xb9,0x69,0x26,0xac,0x53)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBarVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        TrustLevel *trustLevel);

    /*** ICoreApplicationViewTitleBar methods ***/
    HRESULT (STDMETHODCALLTYPE *put_ExtendViewIntoTitleBar)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_ExtendViewIntoTitleBar)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_SystemOverlayLeftInset)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *get_SystemOverlayRightInset)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *add_LayoutMetricsChanged)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_LayoutMetricsChanged)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_IsVisible)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *add_IsVisibleChanged)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_IsVisibleChanged)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBarVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBarVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreApplicationViewTitleBar methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_put_ExtendViewIntoTitleBar(This,value) (This)->lpVtbl->put_ExtendViewIntoTitleBar(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_ExtendViewIntoTitleBar(This,value) (This)->lpVtbl->get_ExtendViewIntoTitleBar(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_SystemOverlayLeftInset(This,value) (This)->lpVtbl->get_SystemOverlayLeftInset(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_SystemOverlayRightInset(This,value) (This)->lpVtbl->get_SystemOverlayRightInset(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_Height(This,value) (This)->lpVtbl->get_Height(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_add_LayoutMetricsChanged(This,handler,token) (This)->lpVtbl->add_LayoutMetricsChanged(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_remove_LayoutMetricsChanged(This,token) (This)->lpVtbl->remove_LayoutMetricsChanged(This,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_IsVisible(This,value) (This)->lpVtbl->get_IsVisible(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_add_IsVisibleChanged(This,handler,token) (This)->lpVtbl->add_IsVisibleChanged(This,handler,token)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_remove_IsVisibleChanged(This,token) (This)->lpVtbl->remove_IsVisibleChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreApplicationViewTitleBar methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_put_ExtendViewIntoTitleBar(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,boolean value) {
    return This->lpVtbl->put_ExtendViewIntoTitleBar(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_ExtendViewIntoTitleBar(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,boolean *value) {
    return This->lpVtbl->get_ExtendViewIntoTitleBar(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_SystemOverlayLeftInset(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,DOUBLE *value) {
    return This->lpVtbl->get_SystemOverlayLeftInset(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_SystemOverlayRightInset(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,DOUBLE *value) {
    return This->lpVtbl->get_SystemOverlayRightInset(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_Height(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,DOUBLE *value) {
    return This->lpVtbl->get_Height(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_add_LayoutMetricsChanged(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_LayoutMetricsChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_remove_LayoutMetricsChanged(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_LayoutMetricsChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_IsVisible(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,boolean *value) {
    return This->lpVtbl->get_IsVisible(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_add_IsVisibleChanged(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_IsVisibleChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_remove_IsVisibleChanged(__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_IsVisibleChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreApplicationViewTitleBar IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar
#define ICoreApplicationViewTitleBarVtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBarVtbl
#define ICoreApplicationViewTitleBar __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar
#define ICoreApplicationViewTitleBar_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_QueryInterface
#define ICoreApplicationViewTitleBar_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_AddRef
#define ICoreApplicationViewTitleBar_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_Release
#define ICoreApplicationViewTitleBar_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetIids
#define ICoreApplicationViewTitleBar_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetRuntimeClassName
#define ICoreApplicationViewTitleBar_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_GetTrustLevel
#define ICoreApplicationViewTitleBar_put_ExtendViewIntoTitleBar __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_put_ExtendViewIntoTitleBar
#define ICoreApplicationViewTitleBar_get_ExtendViewIntoTitleBar __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_ExtendViewIntoTitleBar
#define ICoreApplicationViewTitleBar_get_SystemOverlayLeftInset __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_SystemOverlayLeftInset
#define ICoreApplicationViewTitleBar_get_SystemOverlayRightInset __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_SystemOverlayRightInset
#define ICoreApplicationViewTitleBar_get_Height __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_Height
#define ICoreApplicationViewTitleBar_add_LayoutMetricsChanged __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_add_LayoutMetricsChanged
#define ICoreApplicationViewTitleBar_remove_LayoutMetricsChanged __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_remove_LayoutMetricsChanged
#define ICoreApplicationViewTitleBar_get_IsVisible __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_get_IsVisible
#define ICoreApplicationViewTitleBar_add_IsVisibleChanged __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_add_IsVisibleChanged
#define ICoreApplicationViewTitleBar_remove_IsVisibleChanged __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_remove_IsVisibleChanged
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreImmersiveApplication interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication, 0x1ada0e3e, 0xe4a2, 0x4123, 0xb4,0x51, 0xdc,0x96,0xbf,0x80,0x04,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("1ada0e3e-e4a2-4123-b451-dc96bf800419")
                ICoreImmersiveApplication : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Views(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::ApplicationModel::Core::CoreApplicationView* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateNewView(
                        HSTRING runtime_type,
                        HSTRING entry_point,
                        ABI::Windows::ApplicationModel::Core::ICoreApplicationView **view) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MainView(
                        ABI::Windows::ApplicationModel::Core::ICoreApplicationView **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication, 0x1ada0e3e, 0xe4a2, 0x4123, 0xb4,0x51, 0xdc,0x96,0xbf,0x80,0x04,0x19)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplicationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This,
        TrustLevel *trustLevel);

    /*** ICoreImmersiveApplication methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Views)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This,
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView **value);

    HRESULT (STDMETHODCALLTYPE *CreateNewView)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This,
        HSTRING runtime_type,
        HSTRING entry_point,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **view);

    HRESULT (STDMETHODCALLTYPE *get_MainView)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplicationVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplicationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreImmersiveApplication methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_get_Views(This,value) (This)->lpVtbl->get_Views(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_CreateNewView(This,runtime_type,entry_point,view) (This)->lpVtbl->CreateNewView(This,runtime_type,entry_point,view)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_get_MainView(This,value) (This)->lpVtbl->get_MainView(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreImmersiveApplication methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_get_Views(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This,__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView **value) {
    return This->lpVtbl->get_Views(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_CreateNewView(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This,HSTRING runtime_type,HSTRING entry_point,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **view) {
    return This->lpVtbl->CreateNewView(This,runtime_type,entry_point,view);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_get_MainView(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **value) {
    return This->lpVtbl->get_MainView(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreImmersiveApplication IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication
#define ICoreImmersiveApplicationVtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplicationVtbl
#define ICoreImmersiveApplication __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication
#define ICoreImmersiveApplication_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_QueryInterface
#define ICoreImmersiveApplication_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_AddRef
#define ICoreImmersiveApplication_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_Release
#define ICoreImmersiveApplication_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetIids
#define ICoreImmersiveApplication_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetRuntimeClassName
#define ICoreImmersiveApplication_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_GetTrustLevel
#define ICoreImmersiveApplication_get_Views __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_get_Views
#define ICoreImmersiveApplication_CreateNewView __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_CreateNewView
#define ICoreImmersiveApplication_get_MainView __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_get_MainView
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreImmersiveApplication2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2, 0x828e1e36, 0xe9e3, 0x4cfc, 0x9b,0x66, 0x48,0xb7,0x8e,0xa9,0xbb,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("828e1e36-e9e3-4cfc-9b66-48b78ea9bb2c")
                ICoreImmersiveApplication2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateNewViewFromMainView(
                        ABI::Windows::ApplicationModel::Core::ICoreApplicationView **view) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2, 0x828e1e36, 0xe9e3, 0x4cfc, 0x9b,0x66, 0x48,0xb7,0x8e,0xa9,0xbb,0x2c)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 *This,
        TrustLevel *trustLevel);

    /*** ICoreImmersiveApplication2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateNewViewFromMainView)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **view);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreImmersiveApplication2 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_CreateNewViewFromMainView(This,view) (This)->lpVtbl->CreateNewViewFromMainView(This,view)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreImmersiveApplication2 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_CreateNewViewFromMainView(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **view) {
    return This->lpVtbl->CreateNewViewFromMainView(This,view);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreImmersiveApplication2 IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2
#define ICoreImmersiveApplication2Vtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2Vtbl
#define ICoreImmersiveApplication2 __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2
#define ICoreImmersiveApplication2_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_QueryInterface
#define ICoreImmersiveApplication2_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_AddRef
#define ICoreImmersiveApplication2_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_Release
#define ICoreImmersiveApplication2_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetIids
#define ICoreImmersiveApplication2_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetRuntimeClassName
#define ICoreImmersiveApplication2_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_GetTrustLevel
#define ICoreImmersiveApplication2_CreateNewViewFromMainView __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_CreateNewViewFromMainView
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreImmersiveApplication3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3, 0x34a05b2f, 0xee0d, 0x41e5, 0x83,0x14, 0xcf,0x10,0xc9,0x1b,0xf0,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("34a05b2f-ee0d-41e5-8314-cf10c91bf0af")
                ICoreImmersiveApplication3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateNewViewWithViewSource(
                        ABI::Windows::ApplicationModel::Core::IFrameworkViewSource *view_source,
                        ABI::Windows::ApplicationModel::Core::ICoreApplicationView **view) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3, 0x34a05b2f, 0xee0d, 0x41e5, 0x83,0x14, 0xcf,0x10,0xc9,0x1b,0xf0,0xaf)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 *This,
        TrustLevel *trustLevel);

    /*** ICoreImmersiveApplication3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateNewViewWithViewSource)(
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *view_source,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **view);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3Vtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3 {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreImmersiveApplication3 methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_CreateNewViewWithViewSource(This,view_source,view) (This)->lpVtbl->CreateNewViewWithViewSource(This,view_source,view)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_Release(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreImmersiveApplication3 methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_CreateNewViewWithViewSource(__x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3* This,__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *view_source,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **view) {
    return This->lpVtbl->CreateNewViewWithViewSource(This,view_source,view);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_ICoreImmersiveApplication3 IID___x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3
#define ICoreImmersiveApplication3Vtbl __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3Vtbl
#define ICoreImmersiveApplication3 __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3
#define ICoreImmersiveApplication3_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_QueryInterface
#define ICoreImmersiveApplication3_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_AddRef
#define ICoreImmersiveApplication3_Release __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_Release
#define ICoreImmersiveApplication3_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetIids
#define ICoreImmersiveApplication3_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetRuntimeClassName
#define ICoreImmersiveApplication3_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_GetTrustLevel
#define ICoreImmersiveApplication3_CreateNewViewWithViewSource __x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_CreateNewViewWithViewSource
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CICoreImmersiveApplication3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IFrameworkView interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView, 0xfaab5cd0, 0x8924, 0x45ac, 0xad,0x0f, 0xa0,0x8f,0xae,0x5d,0x03,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("faab5cd0-8924-45ac-ad0f-a08fae5d0324")
                IFrameworkView : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Initialize(
                        ABI::Windows::ApplicationModel::Core::ICoreApplicationView *application_view) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SetWindow(
                        ABI::Windows::UI::Core::ICoreWindow *window) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Load(
                        HSTRING entry_point) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Run(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Uninitialize(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView, 0xfaab5cd0, 0x8924, 0x45ac, 0xad,0x0f, 0xa0,0x8f,0xae,0x5d,0x03,0x24)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This,
        TrustLevel *trustLevel);

    /*** IFrameworkView methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *application_view);

    HRESULT (STDMETHODCALLTYPE *SetWindow)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *window);

    HRESULT (STDMETHODCALLTYPE *Load)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This,
        HSTRING entry_point);

    HRESULT (STDMETHODCALLTYPE *Run)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This);

    HRESULT (STDMETHODCALLTYPE *Uninitialize)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView *This);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IFrameworkView methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Initialize(This,application_view) (This)->lpVtbl->Initialize(This,application_view)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_SetWindow(This,window) (This)->lpVtbl->SetWindow(This,window)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Load(This,entry_point) (This)->lpVtbl->Load(This,entry_point)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Run(This) (This)->lpVtbl->Run(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Uninitialize(This) (This)->lpVtbl->Uninitialize(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Release(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IFrameworkView methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Initialize(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *application_view) {
    return This->lpVtbl->Initialize(This,application_view);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_SetWindow(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *window) {
    return This->lpVtbl->SetWindow(This,window);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Load(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This,HSTRING entry_point) {
    return This->lpVtbl->Load(This,entry_point);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Run(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This) {
    return This->lpVtbl->Run(This);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Uninitialize(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView* This) {
    return This->lpVtbl->Uninitialize(This);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_IFrameworkView IID___x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView
#define IFrameworkViewVtbl __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewVtbl
#define IFrameworkView __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView
#define IFrameworkView_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_QueryInterface
#define IFrameworkView_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_AddRef
#define IFrameworkView_Release __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Release
#define IFrameworkView_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetIids
#define IFrameworkView_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetRuntimeClassName
#define IFrameworkView_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_GetTrustLevel
#define IFrameworkView_Initialize __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Initialize
#define IFrameworkView_SetWindow __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_SetWindow
#define IFrameworkView_Load __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Load
#define IFrameworkView_Run __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Run
#define IFrameworkView_Uninitialize __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_Uninitialize
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IFrameworkViewSource interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource, 0xcd770614, 0x65c4, 0x426c, 0x94,0x94, 0x34,0xfc,0x43,0x55,0x48,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("cd770614-65c4-426c-9494-34fc43554862")
                IFrameworkViewSource : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateView(
                        ABI::Windows::ApplicationModel::Core::IFrameworkView **view) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource, 0xcd770614, 0x65c4, 0x426c, 0x94,0x94, 0x34,0xfc,0x43,0x55,0x48,0x62)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *This,
        TrustLevel *trustLevel);

    /*** IFrameworkViewSource methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateView)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView **view);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSourceVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IFrameworkViewSource methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_CreateView(This,view) (This)->lpVtbl->CreateView(This,view)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_Release(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IFrameworkViewSource methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_CreateView(__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource* This,__x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkView **view) {
    return This->lpVtbl->CreateView(This,view);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_IFrameworkViewSource IID___x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource
#define IFrameworkViewSourceVtbl __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSourceVtbl
#define IFrameworkViewSource __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource
#define IFrameworkViewSource_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_QueryInterface
#define IFrameworkViewSource_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_AddRef
#define IFrameworkViewSource_Release __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_Release
#define IFrameworkViewSource_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetIids
#define IFrameworkViewSource_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetRuntimeClassName
#define IFrameworkViewSource_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_GetTrustLevel
#define IFrameworkViewSource_CreateView __x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_CreateView
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CIFrameworkViewSource_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IHostedViewClosingEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs, 0xd238943c, 0xb24e, 0x4790, 0xac,0xb5, 0x3e,0x42,0x43,0xc4,0xff,0x87);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("d238943c-b24e-4790-acb5-3e4243c4ff87")
                IHostedViewClosingEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetDeferral(
                        ABI::Windows::Foundation::IDeferral **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs, 0xd238943c, 0xb24e, 0x4790, 0xac,0xb5, 0x3e,0x42,0x43,0xc4,0xff,0x87)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *This,
        TrustLevel *trustLevel);

    /*** IHostedViewClosingEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeferral)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *This,
        __x_ABI_CWindows_CFoundation_CIDeferral **result);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IHostedViewClosingEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetDeferral(This,result) (This)->lpVtbl->GetDeferral(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IHostedViewClosingEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetDeferral(__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs* This,__x_ABI_CWindows_CFoundation_CIDeferral **result) {
    return This->lpVtbl->GetDeferral(This,result);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_IHostedViewClosingEventArgs IID___x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs
#define IHostedViewClosingEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgsVtbl
#define IHostedViewClosingEventArgs __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs
#define IHostedViewClosingEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_QueryInterface
#define IHostedViewClosingEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_AddRef
#define IHostedViewClosingEventArgs_Release __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_Release
#define IHostedViewClosingEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetIids
#define IHostedViewClosingEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetRuntimeClassName
#define IHostedViewClosingEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetTrustLevel
#define IHostedViewClosingEventArgs_GetDeferral __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_GetDeferral
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUnhandledError interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError, 0x9459b726, 0x53b5, 0x4686, 0x9e,0xaf, 0xfa,0x81,0x62,0xdc,0x39,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("9459b726-53b5-4686-9eaf-fa8162dc3980")
                IUnhandledError : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Handled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Propagate(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError, 0x9459b726, 0x53b5, 0x4686, 0x9e,0xaf, 0xfa,0x81,0x62,0xdc,0x39,0x80)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError *This,
        TrustLevel *trustLevel);

    /*** IUnhandledError methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Handled)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *Propagate)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError *This);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUnhandledError methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_get_Handled(This,value) (This)->lpVtbl->get_Handled(This,value)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_Propagate(This) (This)->lpVtbl->Propagate(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_Release(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUnhandledError methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_get_Handled(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError* This,boolean *value) {
    return This->lpVtbl->get_Handled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_Propagate(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError* This) {
    return This->lpVtbl->Propagate(This);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_IUnhandledError IID___x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError
#define IUnhandledErrorVtbl __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorVtbl
#define IUnhandledError __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError
#define IUnhandledError_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_QueryInterface
#define IUnhandledError_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_AddRef
#define IUnhandledError_Release __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_Release
#define IUnhandledError_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetIids
#define IUnhandledError_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetRuntimeClassName
#define IUnhandledError_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_GetTrustLevel
#define IUnhandledError_get_Handled __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_get_Handled
#define IUnhandledError_Propagate __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_Propagate
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUnhandledErrorDetectedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs, 0x679ab78b, 0xb336, 0x4822, 0xac,0x40, 0x0d,0x75,0x0f,0x0b,0x7a,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace ApplicationModel {
            namespace Core {
                MIDL_INTERFACE("679ab78b-b336-4822-ac40-0d750f0b7a2b")
                IUnhandledErrorDetectedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_UnhandledError(
                        ABI::Windows::ApplicationModel::Core::IUnhandledError **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs, 0x679ab78b, 0xb336, 0x4822, 0xac,0x40, 0x0d,0x75,0x0f,0x0b,0x7a,0x2b)
#endif
#else
typedef struct __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IUnhandledErrorDetectedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UnhandledError)(
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError **value);

    END_INTERFACE
} __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgsVtbl;

interface __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUnhandledErrorDetectedEventArgs methods ***/
#define __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_get_UnhandledError(This,value) (This)->lpVtbl->get_UnhandledError(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_QueryInterface(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_AddRef(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_Release(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetIids(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetTrustLevel(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUnhandledErrorDetectedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_get_UnhandledError(__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledError **value) {
    return This->lpVtbl->get_UnhandledError(This,value);
}
#endif
#ifdef WIDL_using_Windows_ApplicationModel_Core
#define IID_IUnhandledErrorDetectedEventArgs IID___x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs
#define IUnhandledErrorDetectedEventArgsVtbl __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgsVtbl
#define IUnhandledErrorDetectedEventArgs __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs
#define IUnhandledErrorDetectedEventArgs_QueryInterface __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_QueryInterface
#define IUnhandledErrorDetectedEventArgs_AddRef __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_AddRef
#define IUnhandledErrorDetectedEventArgs_Release __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_Release
#define IUnhandledErrorDetectedEventArgs_GetIids __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetIids
#define IUnhandledErrorDetectedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetRuntimeClassName
#define IUnhandledErrorDetectedEventArgs_GetTrustLevel __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_GetTrustLevel
#define IUnhandledErrorDetectedEventArgs_get_UnhandledError __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_get_UnhandledError
#endif /* WIDL_using_Windows_ApplicationModel_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Core.AppListEntry
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Core_AppListEntry_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Core_AppListEntry_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Core_AppListEntry[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','A','p','p','L','i','s','t','E','n','t','r','y',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_AppListEntry[] = L"Windows.ApplicationModel.Core.AppListEntry";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_AppListEntry[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','A','p','p','L','i','s','t','E','n','t','r','y',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Core_AppListEntry_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Core.CoreApplication
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplication_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplication_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplication[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','C','o','r','e','A','p','p','l','i','c','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplication[] = L"Windows.ApplicationModel.Core.CoreApplication";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplication[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','C','o','r','e','A','p','p','l','i','c','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplication_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Core.CoreApplicationView
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplicationView_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplicationView_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplicationView[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','C','o','r','e','A','p','p','l','i','c','a','t','i','o','n','V','i','e','w',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplicationView[] = L"Windows.ApplicationModel.Core.CoreApplicationView";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplicationView[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','C','o','r','e','A','p','p','l','i','c','a','t','i','o','n','V','i','e','w',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplicationView_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Core.CoreApplicationViewTitleBar
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplicationViewTitleBar_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplicationViewTitleBar_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplicationViewTitleBar[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','C','o','r','e','A','p','p','l','i','c','a','t','i','o','n','V','i','e','w','T','i','t','l','e','B','a','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplicationViewTitleBar[] = L"Windows.ApplicationModel.Core.CoreApplicationViewTitleBar";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_CoreApplicationViewTitleBar[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','C','o','r','e','A','p','p','l','i','c','a','t','i','o','n','V','i','e','w','T','i','t','l','e','B','a','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Core_CoreApplicationViewTitleBar_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Core.HostedViewClosingEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Core_HostedViewClosingEventArgs_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Core_HostedViewClosingEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Core_HostedViewClosingEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','H','o','s','t','e','d','V','i','e','w','C','l','o','s','i','n','g','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_HostedViewClosingEventArgs[] = L"Windows.ApplicationModel.Core.HostedViewClosingEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_HostedViewClosingEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','H','o','s','t','e','d','V','i','e','w','C','l','o','s','i','n','g','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Core_HostedViewClosingEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Core.UnhandledError
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Core_UnhandledError_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Core_UnhandledError_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Core_UnhandledError[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','U','n','h','a','n','d','l','e','d','E','r','r','o','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_UnhandledError[] = L"Windows.ApplicationModel.Core.UnhandledError";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_UnhandledError[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','U','n','h','a','n','d','l','e','d','E','r','r','o','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Core_UnhandledError_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.ApplicationModel.Core.UnhandledErrorDetectedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_ApplicationModel_Core_UnhandledErrorDetectedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_ApplicationModel_Core_UnhandledErrorDetectedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_ApplicationModel_Core_UnhandledErrorDetectedEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','U','n','h','a','n','d','l','e','d','E','r','r','o','r','D','e','t','e','c','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_UnhandledErrorDetectedEventArgs[] = L"Windows.ApplicationModel.Core.UnhandledErrorDetectedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_ApplicationModel_Core_UnhandledErrorDetectedEventArgs[] = {'W','i','n','d','o','w','s','.','A','p','p','l','i','c','a','t','i','o','n','M','o','d','e','l','.','C','o','r','e','.','U','n','h','a','n','d','l','e','d','E','r','r','o','r','D','e','t','e','c','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_ApplicationModel_Core_UnhandledErrorDetectedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0x49a07732, 0xe7b8, 0x5c5b, 0x9d,0xe7, 0x22,0xe3,0x3c,0xb9,0x70,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("49a07732-e7b8-5c5b-9de7-22e33cb97004")
            IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs*, ABI::Windows::ApplicationModel::Activation::IBackgroundActivatedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs, 0x49a07732, 0xe7b8, 0x5c5b, 0x9d,0xe7, 0x22,0xe3,0x3c,0xb9,0x70,0x04)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::Activation::BackgroundActivatedEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CActivation_CIBackgroundActivatedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_BackgroundActivatedEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define IEventHandler_BackgroundActivatedEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgsVtbl
#define IEventHandler_BackgroundActivatedEventArgs __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs
#define IEventHandler_BackgroundActivatedEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_QueryInterface
#define IEventHandler_BackgroundActivatedEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_AddRef
#define IEventHandler_BackgroundActivatedEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Release
#define IEventHandler_BackgroundActivatedEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CActivation__CBackgroundActivatedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs, 0xcf193a96, 0xeb13, 0x5e3b, 0x8b,0xdf, 0x87,0xb6,0xef,0xae,0x83,0x39);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("cf193a96-eb13-5e3b-8bdf-87b6efae8339")
            ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Core::CoreApplicationView*, ABI::Windows::ApplicationModel::Core::ICoreApplicationView* >, ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs, 0xcf193a96, 0xeb13, 0x5e3b, 0x8b,0xdf, 0x87,0xb6,0xef,0xae,0x83,0x39)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *sender,
        __x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Release(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Activation::IActivatedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *sender,__x_ABI_CWindows_CApplicationModel_CActivation_CIActivatedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreApplicationView_IActivatedEventArgs IID___FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgsVtbl __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgsVtbl
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_QueryInterface
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs_AddRef __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_AddRef
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs_Release __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Release
#define ITypedEventHandler_CoreApplicationView_IActivatedEventArgs_Invoke __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CActivation__CIActivatedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs, 0xe0739c32, 0xfc14, 0x5361, 0xa8,0xb3, 0x08,0x09,0x69,0x9f,0xbc,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("e0739c32-fc14-5361-a8b3-0809699fbcbd")
            IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs*, ABI::Windows::ApplicationModel::IEnteredBackgroundEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs, 0xe0739c32, 0xfc14, 0x5361, 0xa8,0xb3, 0x08,0x09,0x69,0x9f,0xbc,0xbd)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::EnteredBackgroundEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CIEnteredBackgroundEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_EnteredBackgroundEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs
#define IEventHandler_EnteredBackgroundEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgsVtbl
#define IEventHandler_EnteredBackgroundEventArgs __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs
#define IEventHandler_EnteredBackgroundEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_QueryInterface
#define IEventHandler_EnteredBackgroundEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_AddRef
#define IEventHandler_EnteredBackgroundEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Release
#define IEventHandler_EnteredBackgroundEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CEnteredBackgroundEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs, 0x9b6171c2, 0xabb2, 0x5194, 0xaf,0xc0, 0xce,0xf1,0x67,0xc4,0x24,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("9b6171c2-abb2-5194-afc0-cef167c424eb")
            IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs*, ABI::Windows::ApplicationModel::ILeavingBackgroundEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs, 0x9b6171c2, 0xabb2, 0x5194, 0xaf,0xc0, 0xce,0xf1,0x67,0xc4,0x24,0xeb)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::LeavingBackgroundEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CILeavingBackgroundEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_LeavingBackgroundEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs
#define IEventHandler_LeavingBackgroundEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgsVtbl
#define IEventHandler_LeavingBackgroundEventArgs __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs
#define IEventHandler_LeavingBackgroundEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_QueryInterface
#define IEventHandler_LeavingBackgroundEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_AddRef
#define IEventHandler_LeavingBackgroundEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Release
#define IEventHandler_LeavingBackgroundEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CLeavingBackgroundEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs, 0x338579bf, 0x1a35, 0x5cc4, 0xa6,0x22, 0xa6,0xf3,0x84,0xfd,0x89,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("338579bf-1a35-5cc4-a622-a6f384fd892c")
            IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::SuspendingEventArgs*, ABI::Windows::ApplicationModel::ISuspendingEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs, 0x338579bf, 0x1a35, 0x5cc4, 0xa6,0x22, 0xa6,0xf3,0x84,0xfd,0x89,0x2c)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::SuspendingEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CISuspendingEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_SuspendingEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs
#define IEventHandler_SuspendingEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgsVtbl
#define IEventHandler_SuspendingEventArgs __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs
#define IEventHandler_SuspendingEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_QueryInterface
#define IEventHandler_SuspendingEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_AddRef
#define IEventHandler_SuspendingEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Release
#define IEventHandler_SuspendingEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CSuspendingEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_AppRestartFailureReason, 0xdcec478a, 0x9f27, 0x5c5d, 0xaf,0xdb, 0xc9,0x1a,0xee,0x4f,0x1f,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("dcec478a-9f27-5c5d-afdb-c91aee4f1f02")
            IAsyncOperationCompletedHandler<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > : IAsyncOperationCompletedHandler_impl<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_AppRestartFailureReason, 0xdcec478a, 0x9f27, 0x5c5d, 0xaf,0xdb, 0xc9,0x1a,0xee,0x4f,0x1f,0x02)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_AppRestartFailureReasonVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason *This,
        __FIAsyncOperation_1_AppRestartFailureReason *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_AppRestartFailureReasonVtbl;

interface __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_AppRestartFailureReasonVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > methods ***/
#define __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_QueryInterface(__FIAsyncOperationCompletedHandler_1_AppRestartFailureReason* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_AddRef(__FIAsyncOperationCompletedHandler_1_AppRestartFailureReason* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_Release(__FIAsyncOperationCompletedHandler_1_AppRestartFailureReason* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_Invoke(__FIAsyncOperationCompletedHandler_1_AppRestartFailureReason* This,__FIAsyncOperation_1_AppRestartFailureReason *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_AppRestartFailureReason IID___FIAsyncOperationCompletedHandler_1_AppRestartFailureReason
#define IAsyncOperationCompletedHandler_AppRestartFailureReasonVtbl __FIAsyncOperationCompletedHandler_1_AppRestartFailureReasonVtbl
#define IAsyncOperationCompletedHandler_AppRestartFailureReason __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason
#define IAsyncOperationCompletedHandler_AppRestartFailureReason_QueryInterface __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_QueryInterface
#define IAsyncOperationCompletedHandler_AppRestartFailureReason_AddRef __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_AddRef
#define IAsyncOperationCompletedHandler_AppRestartFailureReason_Release __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_Release
#define IAsyncOperationCompletedHandler_AppRestartFailureReason_Invoke __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_AppRestartFailureReason_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::ApplicationModel::Core::CoreApplicationView* > interface
 */
#ifndef ____FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView, 0xde9e16c4, 0x1b7c, 0x5126, 0xb1,0xd8, 0x7c,0xd0,0x4f,0x13,0xbd,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("de9e16c4-1b7c-5126-b1d8-7cd04f13bd08")
                IVectorView<ABI::Windows::ApplicationModel::Core::CoreApplicationView* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Core::CoreApplicationView*, ABI::Windows::ApplicationModel::Core::ICoreApplicationView* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView, 0xde9e16c4, 0x1b7c, 0x5126, 0xb1,0xd8, 0x7c,0xd0,0x4f,0x13,0xbd,0x08)
#endif
#else
typedef struct __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::ApplicationModel::Core::CoreApplicationView* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This,
        UINT32 index,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationViewVtbl;

interface __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView {
    CONST_VTBL __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::ApplicationModel::Core::CoreApplicationView* > methods ***/
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_QueryInterface(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_AddRef(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_Release(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetIids(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetRuntimeClassName(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetTrustLevel(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::ApplicationModel::Core::CoreApplicationView* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetAt(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This,UINT32 index,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_get_Size(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_IndexOf(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetMany(__FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_CoreApplicationView IID___FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView
#define IVectorView_CoreApplicationViewVtbl __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationViewVtbl
#define IVectorView_CoreApplicationView __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView
#define IVectorView_CoreApplicationView_QueryInterface __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_QueryInterface
#define IVectorView_CoreApplicationView_AddRef __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_AddRef
#define IVectorView_CoreApplicationView_Release __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_Release
#define IVectorView_CoreApplicationView_GetIids __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetIids
#define IVectorView_CoreApplicationView_GetRuntimeClassName __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetRuntimeClassName
#define IVectorView_CoreApplicationView_GetTrustLevel __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetTrustLevel
#define IVectorView_CoreApplicationView_GetAt __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetAt
#define IVectorView_CoreApplicationView_get_Size __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_get_Size
#define IVectorView_CoreApplicationView_IndexOf __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_IndexOf
#define IVectorView_CoreApplicationView_GetMany __FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CApplicationModel__CCore__CCoreApplicationView_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventHandler<ABI::Windows::ApplicationModel::Core::UnhandledErrorDetectedEventArgs* > interface
 */
#ifndef ____FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_INTERFACE_DEFINED__
#define ____FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs, 0xf68bc421, 0x6b54, 0x559b, 0x9c,0xdd, 0x48,0x9a,0xad,0x0b,0xd4,0x1d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("f68bc421-6b54-559b-9cdd-489aad0bd41d")
            IEventHandler<ABI::Windows::ApplicationModel::Core::UnhandledErrorDetectedEventArgs* > : IEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Core::UnhandledErrorDetectedEventArgs*, ABI::Windows::ApplicationModel::Core::IUnhandledErrorDetectedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs, 0xf68bc421, 0x6b54, 0x559b, 0x9c,0xdd, 0x48,0x9a,0xad,0x0b,0xd4,0x1d)
#endif
#else
typedef struct __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs *This);

    /*** IEventHandler<ABI::Windows::ApplicationModel::Core::UnhandledErrorDetectedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs *This,
        IInspectable *sender,
        __x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *args);

    END_INTERFACE
} __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgsVtbl;

interface __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs {
    CONST_VTBL __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IEventHandler<ABI::Windows::ApplicationModel::Core::UnhandledErrorDetectedEventArgs* > methods ***/
#define __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_QueryInterface(__FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_AddRef(__FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_Release(__FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEventHandler<ABI::Windows::ApplicationModel::Core::UnhandledErrorDetectedEventArgs* > methods ***/
static inline HRESULT __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_Invoke(__FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs* This,IInspectable *sender,__x_ABI_CWindows_CApplicationModel_CCore_CIUnhandledErrorDetectedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IEventHandler_UnhandledErrorDetectedEventArgs IID___FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs
#define IEventHandler_UnhandledErrorDetectedEventArgsVtbl __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgsVtbl
#define IEventHandler_UnhandledErrorDetectedEventArgs __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs
#define IEventHandler_UnhandledErrorDetectedEventArgs_QueryInterface __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_QueryInterface
#define IEventHandler_UnhandledErrorDetectedEventArgs_AddRef __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_AddRef
#define IEventHandler_UnhandledErrorDetectedEventArgs_Release __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_Release
#define IEventHandler_UnhandledErrorDetectedEventArgs_Invoke __FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIEventHandler_1_Windows__CApplicationModel__CCore__CUnhandledErrorDetectedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > interface
 */
#ifndef ____FIAsyncOperation_1_AppRestartFailureReason_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_AppRestartFailureReason_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_AppRestartFailureReason, 0x0938905d, 0x54c0, 0x572f, 0x84,0x51, 0x4b,0xfd,0x2b,0x52,0xed,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("0938905d-54c0-572f-8451-4bfd2b52edda")
            IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > : IAsyncOperation_impl<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_AppRestartFailureReason, 0x0938905d, 0x54c0, 0x572f, 0x84,0x51, 0x4b,0xfd,0x2b,0x52,0xed,0xda)
#endif
#else
typedef struct __FIAsyncOperation_1_AppRestartFailureReasonVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_AppRestartFailureReason *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_AppRestartFailureReason *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_AppRestartFailureReason *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_AppRestartFailureReason *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_AppRestartFailureReason *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_AppRestartFailureReason *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_AppRestartFailureReason *This,
        __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_AppRestartFailureReason *This,
        __FIAsyncOperationCompletedHandler_1_AppRestartFailureReason **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_AppRestartFailureReason *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CAppRestartFailureReason *results);

    END_INTERFACE
} __FIAsyncOperation_1_AppRestartFailureReasonVtbl;

interface __FIAsyncOperation_1_AppRestartFailureReason {
    CONST_VTBL __FIAsyncOperation_1_AppRestartFailureReasonVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_AppRestartFailureReason_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_AppRestartFailureReason_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_AppRestartFailureReason_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_AppRestartFailureReason_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_AppRestartFailureReason_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_AppRestartFailureReason_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > methods ***/
#define __FIAsyncOperation_1_AppRestartFailureReason_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_AppRestartFailureReason_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_AppRestartFailureReason_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_AppRestartFailureReason_QueryInterface(__FIAsyncOperation_1_AppRestartFailureReason* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_AppRestartFailureReason_AddRef(__FIAsyncOperation_1_AppRestartFailureReason* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_AppRestartFailureReason_Release(__FIAsyncOperation_1_AppRestartFailureReason* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_AppRestartFailureReason_GetIids(__FIAsyncOperation_1_AppRestartFailureReason* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_AppRestartFailureReason_GetRuntimeClassName(__FIAsyncOperation_1_AppRestartFailureReason* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_AppRestartFailureReason_GetTrustLevel(__FIAsyncOperation_1_AppRestartFailureReason* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::ApplicationModel::Core::AppRestartFailureReason > methods ***/
static inline HRESULT __FIAsyncOperation_1_AppRestartFailureReason_put_Completed(__FIAsyncOperation_1_AppRestartFailureReason* This,__FIAsyncOperationCompletedHandler_1_AppRestartFailureReason *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_AppRestartFailureReason_get_Completed(__FIAsyncOperation_1_AppRestartFailureReason* This,__FIAsyncOperationCompletedHandler_1_AppRestartFailureReason **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_AppRestartFailureReason_GetResults(__FIAsyncOperation_1_AppRestartFailureReason* This,__x_ABI_CWindows_CApplicationModel_CCore_CAppRestartFailureReason *results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_AppRestartFailureReason IID___FIAsyncOperation_1_AppRestartFailureReason
#define IAsyncOperation_AppRestartFailureReasonVtbl __FIAsyncOperation_1_AppRestartFailureReasonVtbl
#define IAsyncOperation_AppRestartFailureReason __FIAsyncOperation_1_AppRestartFailureReason
#define IAsyncOperation_AppRestartFailureReason_QueryInterface __FIAsyncOperation_1_AppRestartFailureReason_QueryInterface
#define IAsyncOperation_AppRestartFailureReason_AddRef __FIAsyncOperation_1_AppRestartFailureReason_AddRef
#define IAsyncOperation_AppRestartFailureReason_Release __FIAsyncOperation_1_AppRestartFailureReason_Release
#define IAsyncOperation_AppRestartFailureReason_GetIids __FIAsyncOperation_1_AppRestartFailureReason_GetIids
#define IAsyncOperation_AppRestartFailureReason_GetRuntimeClassName __FIAsyncOperation_1_AppRestartFailureReason_GetRuntimeClassName
#define IAsyncOperation_AppRestartFailureReason_GetTrustLevel __FIAsyncOperation_1_AppRestartFailureReason_GetTrustLevel
#define IAsyncOperation_AppRestartFailureReason_put_Completed __FIAsyncOperation_1_AppRestartFailureReason_put_Completed
#define IAsyncOperation_AppRestartFailureReason_get_Completed __FIAsyncOperation_1_AppRestartFailureReason_get_Completed
#define IAsyncOperation_AppRestartFailureReason_GetResults __FIAsyncOperation_1_AppRestartFailureReason_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_AppRestartFailureReason_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Core::HostedViewClosingEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs, 0xd9a3f433, 0x9bcc, 0x54d6, 0xb3,0xcf, 0x7b,0x01,0xf0,0x26,0xd4,0xcd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d9a3f433-9bcc-54d6-b3cf-7b01f026d4cd")
            ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Core::HostedViewClosingEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Core::CoreApplicationView*, ABI::Windows::ApplicationModel::Core::ICoreApplicationView* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Core::HostedViewClosingEventArgs*, ABI::Windows::ApplicationModel::Core::IHostedViewClosingEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs, 0xd9a3f433, 0x9bcc, 0x54d6, 0xb3,0xcf, 0x7b,0x01,0xf0,0x26,0xd4,0xcd)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Core::HostedViewClosingEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *sender,
        __x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Core::HostedViewClosingEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_AddRef(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_Release(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationView*,ABI::Windows::ApplicationModel::Core::HostedViewClosingEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_Invoke(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationView *sender,__x_ABI_CWindows_CApplicationModel_CCore_CIHostedViewClosingEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreApplicationView_HostedViewClosingEventArgs IID___FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs
#define ITypedEventHandler_CoreApplicationView_HostedViewClosingEventArgsVtbl __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgsVtbl
#define ITypedEventHandler_CoreApplicationView_HostedViewClosingEventArgs __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs
#define ITypedEventHandler_CoreApplicationView_HostedViewClosingEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_QueryInterface
#define ITypedEventHandler_CoreApplicationView_HostedViewClosingEventArgs_AddRef __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_AddRef
#define ITypedEventHandler_CoreApplicationView_HostedViewClosingEventArgs_Release __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_Release
#define ITypedEventHandler_CoreApplicationView_HostedViewClosingEventArgs_Invoke __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationView_Windows__CApplicationModel__CCore__CHostedViewClosingEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable, 0x28342e21, 0xdad3, 0x5e32, 0xba,0xe1, 0xaf,0xe7,0xb2,0x6c,0x66,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("28342e21-dad3-5e32-bae1-afe7b26c66fb")
            ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*, ABI::Windows::ApplicationModel::Core::ICoreApplicationViewTitleBar* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable, 0x28342e21, 0xdad3, 0x5e32, 0xba,0xe1, 0xaf,0xe7,0xb2,0x6c,0x66,0xfb)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable *This,
        __x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_Release(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::ApplicationModel::Core::CoreApplicationViewTitleBar*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable* This,__x_ABI_CWindows_CApplicationModel_CCore_CICoreApplicationViewTitleBar *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreApplicationViewTitleBar_IInspectable IID___FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable
#define ITypedEventHandler_CoreApplicationViewTitleBar_IInspectableVtbl __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectableVtbl
#define ITypedEventHandler_CoreApplicationViewTitleBar_IInspectable __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable
#define ITypedEventHandler_CoreApplicationViewTitleBar_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_QueryInterface
#define ITypedEventHandler_CoreApplicationViewTitleBar_IInspectable_AddRef __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_AddRef
#define ITypedEventHandler_CoreApplicationViewTitleBar_IInspectable_Release __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_Release
#define ITypedEventHandler_CoreApplicationViewTitleBar_IInspectable_Invoke __FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CApplicationModel__CCore__CCoreApplicationViewTitleBar_IInspectable_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_applicationmodel_core_h__ */
