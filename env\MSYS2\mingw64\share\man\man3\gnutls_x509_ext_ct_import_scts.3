.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_ext_ct_import_scts" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_ext_ct_import_scts \- API function
.SH SYNOPSIS
.B #include <gnutls/x509-ext.h>
.sp
.BI "int gnutls_x509_ext_ct_import_scts(const gnutls_datum_t * " ext ", gnutls_x509_ct_scts_t " scts ", unsigned int " flags ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * ext" 12
a DER\-encoded extension
.IP "gnutls_x509_ct_scts_t scts" 12
The SCT list
.IP "unsigned int flags" 12
should be zero
.SH "DESCRIPTION"
This function will read a SignedCertificateTimestampList structure
from the DER data of the X.509 Certificate Transparency SCT extension
(OID *******.4.1.11129.2.4.2).

The list of SCTs (Signed Certificate Timestamps) is placed on  \fIscts\fP ,
which must be previously initialized with \fBgnutls_x509_ext_ct_scts_init()\fP.
.SH "RETURNS"
\fBGNUTLS_E_SUCCESS\fP (0) on success or a negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
