.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_crl_set_number" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_crl_set_number \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_crl_set_number(gnutls_x509_crl_t " crl ", const void * " nr ", size_t " nr_size ");"
.SH ARGUMENTS
.IP "gnutls_x509_crl_t crl" 12
a CRL of type \fBgnutls_x509_crl_t\fP
.IP "const void * nr" 12
The CRL number
.IP "size_t nr_size" 12
Holds the size of the nr field.
.SH "DESCRIPTION"
This function will set the CRL's number extension. This
is to be used as a unique and monotonic number assigned to
the CRL by the authority.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, otherwise a
negative error value.
.SH "SINCE"
2.8.0
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
