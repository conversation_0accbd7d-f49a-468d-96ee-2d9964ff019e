// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/framework/kernel_def.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/attr_value.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto;
namespace tensorflow {
class KernelDef;
struct KernelDefDefaultTypeInternal;
extern KernelDefDefaultTypeInternal _KernelDef_default_instance_;
class KernelDef_AttrConstraint;
struct KernelDef_AttrConstraintDefaultTypeInternal;
extern KernelDef_AttrConstraintDefaultTypeInternal _KernelDef_AttrConstraint_default_instance_;
class KernelList;
struct KernelListDefaultTypeInternal;
extern KernelListDefaultTypeInternal _KernelList_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::KernelDef* Arena::CreateMaybeMessage<::tensorflow::KernelDef>(Arena*);
template<> ::tensorflow::KernelDef_AttrConstraint* Arena::CreateMaybeMessage<::tensorflow::KernelDef_AttrConstraint>(Arena*);
template<> ::tensorflow::KernelList* Arena::CreateMaybeMessage<::tensorflow::KernelList>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class KernelDef_AttrConstraint final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.KernelDef.AttrConstraint) */ {
 public:
  inline KernelDef_AttrConstraint() : KernelDef_AttrConstraint(nullptr) {}
  ~KernelDef_AttrConstraint() override;
  explicit PROTOBUF_CONSTEXPR KernelDef_AttrConstraint(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  KernelDef_AttrConstraint(const KernelDef_AttrConstraint& from);
  KernelDef_AttrConstraint(KernelDef_AttrConstraint&& from) noexcept
    : KernelDef_AttrConstraint() {
    *this = ::std::move(from);
  }

  inline KernelDef_AttrConstraint& operator=(const KernelDef_AttrConstraint& from) {
    CopyFrom(from);
    return *this;
  }
  inline KernelDef_AttrConstraint& operator=(KernelDef_AttrConstraint&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KernelDef_AttrConstraint& default_instance() {
    return *internal_default_instance();
  }
  static inline const KernelDef_AttrConstraint* internal_default_instance() {
    return reinterpret_cast<const KernelDef_AttrConstraint*>(
               &_KernelDef_AttrConstraint_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(KernelDef_AttrConstraint& a, KernelDef_AttrConstraint& b) {
    a.Swap(&b);
  }
  inline void Swap(KernelDef_AttrConstraint* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KernelDef_AttrConstraint* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KernelDef_AttrConstraint* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KernelDef_AttrConstraint>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const KernelDef_AttrConstraint& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const KernelDef_AttrConstraint& from) {
    KernelDef_AttrConstraint::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KernelDef_AttrConstraint* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.KernelDef.AttrConstraint";
  }
  protected:
  explicit KernelDef_AttrConstraint(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kAllowedValuesFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.AttrValue allowed_values = 2;
  bool has_allowed_values() const;
  private:
  bool _internal_has_allowed_values() const;
  public:
  void clear_allowed_values();
  const ::tensorflow::AttrValue& allowed_values() const;
  PROTOBUF_NODISCARD ::tensorflow::AttrValue* release_allowed_values();
  ::tensorflow::AttrValue* mutable_allowed_values();
  void set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values);
  private:
  const ::tensorflow::AttrValue& _internal_allowed_values() const;
  ::tensorflow::AttrValue* _internal_mutable_allowed_values();
  public:
  void unsafe_arena_set_allocated_allowed_values(
      ::tensorflow::AttrValue* allowed_values);
  ::tensorflow::AttrValue* unsafe_arena_release_allowed_values();

  // @@protoc_insertion_point(class_scope:tensorflow.KernelDef.AttrConstraint)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::AttrValue* allowed_values_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto;
};
// -------------------------------------------------------------------

class KernelDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.KernelDef) */ {
 public:
  inline KernelDef() : KernelDef(nullptr) {}
  ~KernelDef() override;
  explicit PROTOBUF_CONSTEXPR KernelDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  KernelDef(const KernelDef& from);
  KernelDef(KernelDef&& from) noexcept
    : KernelDef() {
    *this = ::std::move(from);
  }

  inline KernelDef& operator=(const KernelDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline KernelDef& operator=(KernelDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KernelDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const KernelDef* internal_default_instance() {
    return reinterpret_cast<const KernelDef*>(
               &_KernelDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(KernelDef& a, KernelDef& b) {
    a.Swap(&b);
  }
  inline void Swap(KernelDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KernelDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KernelDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KernelDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const KernelDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const KernelDef& from) {
    KernelDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KernelDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.KernelDef";
  }
  protected:
  explicit KernelDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef KernelDef_AttrConstraint AttrConstraint;

  // accessors -------------------------------------------------------

  enum : int {
    kConstraintFieldNumber = 3,
    kHostMemoryArgFieldNumber = 4,
    kOpFieldNumber = 1,
    kDeviceTypeFieldNumber = 2,
    kLabelFieldNumber = 5,
    kPriorityFieldNumber = 6,
  };
  // repeated .tensorflow.KernelDef.AttrConstraint constraint = 3;
  int constraint_size() const;
  private:
  int _internal_constraint_size() const;
  public:
  void clear_constraint();
  ::tensorflow::KernelDef_AttrConstraint* mutable_constraint(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint >*
      mutable_constraint();
  private:
  const ::tensorflow::KernelDef_AttrConstraint& _internal_constraint(int index) const;
  ::tensorflow::KernelDef_AttrConstraint* _internal_add_constraint();
  public:
  const ::tensorflow::KernelDef_AttrConstraint& constraint(int index) const;
  ::tensorflow::KernelDef_AttrConstraint* add_constraint();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint >&
      constraint() const;

  // repeated string host_memory_arg = 4;
  int host_memory_arg_size() const;
  private:
  int _internal_host_memory_arg_size() const;
  public:
  void clear_host_memory_arg();
  const std::string& host_memory_arg(int index) const;
  std::string* mutable_host_memory_arg(int index);
  void set_host_memory_arg(int index, const std::string& value);
  void set_host_memory_arg(int index, std::string&& value);
  void set_host_memory_arg(int index, const char* value);
  void set_host_memory_arg(int index, const char* value, size_t size);
  std::string* add_host_memory_arg();
  void add_host_memory_arg(const std::string& value);
  void add_host_memory_arg(std::string&& value);
  void add_host_memory_arg(const char* value);
  void add_host_memory_arg(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& host_memory_arg() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_host_memory_arg();
  private:
  const std::string& _internal_host_memory_arg(int index) const;
  std::string* _internal_add_host_memory_arg();
  public:

  // string op = 1;
  void clear_op();
  const std::string& op() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op();
  PROTOBUF_NODISCARD std::string* release_op();
  void set_allocated_op(std::string* op);
  private:
  const std::string& _internal_op() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op(const std::string& value);
  std::string* _internal_mutable_op();
  public:

  // string device_type = 2;
  void clear_device_type();
  const std::string& device_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_type();
  PROTOBUF_NODISCARD std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  private:
  const std::string& _internal_device_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_type(const std::string& value);
  std::string* _internal_mutable_device_type();
  public:

  // string label = 5;
  void clear_label();
  const std::string& label() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_label(ArgT0&& arg0, ArgT... args);
  std::string* mutable_label();
  PROTOBUF_NODISCARD std::string* release_label();
  void set_allocated_label(std::string* label);
  private:
  const std::string& _internal_label() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_label(const std::string& value);
  std::string* _internal_mutable_label();
  public:

  // int32 priority = 6;
  void clear_priority();
  int32_t priority() const;
  void set_priority(int32_t value);
  private:
  int32_t _internal_priority() const;
  void _internal_set_priority(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.KernelDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint > constraint_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> host_memory_arg_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr label_;
    int32_t priority_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto;
};
// -------------------------------------------------------------------

class KernelList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.KernelList) */ {
 public:
  inline KernelList() : KernelList(nullptr) {}
  ~KernelList() override;
  explicit PROTOBUF_CONSTEXPR KernelList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  KernelList(const KernelList& from);
  KernelList(KernelList&& from) noexcept
    : KernelList() {
    *this = ::std::move(from);
  }

  inline KernelList& operator=(const KernelList& from) {
    CopyFrom(from);
    return *this;
  }
  inline KernelList& operator=(KernelList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const KernelList& default_instance() {
    return *internal_default_instance();
  }
  static inline const KernelList* internal_default_instance() {
    return reinterpret_cast<const KernelList*>(
               &_KernelList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(KernelList& a, KernelList& b) {
    a.Swap(&b);
  }
  inline void Swap(KernelList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(KernelList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  KernelList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<KernelList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const KernelList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const KernelList& from) {
    KernelList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(KernelList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.KernelList";
  }
  protected:
  explicit KernelList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKernelFieldNumber = 1,
  };
  // repeated .tensorflow.KernelDef kernel = 1;
  int kernel_size() const;
  private:
  int _internal_kernel_size() const;
  public:
  void clear_kernel();
  ::tensorflow::KernelDef* mutable_kernel(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef >*
      mutable_kernel();
  private:
  const ::tensorflow::KernelDef& _internal_kernel(int index) const;
  ::tensorflow::KernelDef* _internal_add_kernel();
  public:
  const ::tensorflow::KernelDef& kernel(int index) const;
  ::tensorflow::KernelDef* add_kernel();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef >&
      kernel() const;

  // @@protoc_insertion_point(class_scope:tensorflow.KernelList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef > kernel_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// KernelDef_AttrConstraint

// string name = 1;
inline void KernelDef_AttrConstraint::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& KernelDef_AttrConstraint::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.AttrConstraint.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void KernelDef_AttrConstraint::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.AttrConstraint.name)
}
inline std::string* KernelDef_AttrConstraint::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.AttrConstraint.name)
  return _s;
}
inline const std::string& KernelDef_AttrConstraint::_internal_name() const {
  return _impl_.name_.Get();
}
inline void KernelDef_AttrConstraint::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* KernelDef_AttrConstraint::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* KernelDef_AttrConstraint::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.AttrConstraint.name)
  return _impl_.name_.Release();
}
inline void KernelDef_AttrConstraint::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.AttrConstraint.name)
}

// .tensorflow.AttrValue allowed_values = 2;
inline bool KernelDef_AttrConstraint::_internal_has_allowed_values() const {
  return this != internal_default_instance() && _impl_.allowed_values_ != nullptr;
}
inline bool KernelDef_AttrConstraint::has_allowed_values() const {
  return _internal_has_allowed_values();
}
inline const ::tensorflow::AttrValue& KernelDef_AttrConstraint::_internal_allowed_values() const {
  const ::tensorflow::AttrValue* p = _impl_.allowed_values_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::AttrValue&>(
      ::tensorflow::_AttrValue_default_instance_);
}
inline const ::tensorflow::AttrValue& KernelDef_AttrConstraint::allowed_values() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.AttrConstraint.allowed_values)
  return _internal_allowed_values();
}
inline void KernelDef_AttrConstraint::unsafe_arena_set_allocated_allowed_values(
    ::tensorflow::AttrValue* allowed_values) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.allowed_values_);
  }
  _impl_.allowed_values_ = allowed_values;
  if (allowed_values) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.KernelDef.AttrConstraint.allowed_values)
}
inline ::tensorflow::AttrValue* KernelDef_AttrConstraint::release_allowed_values() {
  
  ::tensorflow::AttrValue* temp = _impl_.allowed_values_;
  _impl_.allowed_values_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::AttrValue* KernelDef_AttrConstraint::unsafe_arena_release_allowed_values() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.AttrConstraint.allowed_values)
  
  ::tensorflow::AttrValue* temp = _impl_.allowed_values_;
  _impl_.allowed_values_ = nullptr;
  return temp;
}
inline ::tensorflow::AttrValue* KernelDef_AttrConstraint::_internal_mutable_allowed_values() {
  
  if (_impl_.allowed_values_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::AttrValue>(GetArenaForAllocation());
    _impl_.allowed_values_ = p;
  }
  return _impl_.allowed_values_;
}
inline ::tensorflow::AttrValue* KernelDef_AttrConstraint::mutable_allowed_values() {
  ::tensorflow::AttrValue* _msg = _internal_mutable_allowed_values();
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.AttrConstraint.allowed_values)
  return _msg;
}
inline void KernelDef_AttrConstraint::set_allocated_allowed_values(::tensorflow::AttrValue* allowed_values) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.allowed_values_);
  }
  if (allowed_values) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(allowed_values));
    if (message_arena != submessage_arena) {
      allowed_values = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, allowed_values, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.allowed_values_ = allowed_values;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.AttrConstraint.allowed_values)
}

// -------------------------------------------------------------------

// KernelDef

// string op = 1;
inline void KernelDef::clear_op() {
  _impl_.op_.ClearToEmpty();
}
inline const std::string& KernelDef::op() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.op)
  return _internal_op();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void KernelDef::set_op(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.op)
}
inline std::string* KernelDef::mutable_op() {
  std::string* _s = _internal_mutable_op();
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.op)
  return _s;
}
inline const std::string& KernelDef::_internal_op() const {
  return _impl_.op_.Get();
}
inline void KernelDef::_internal_set_op(const std::string& value) {
  
  _impl_.op_.Set(value, GetArenaForAllocation());
}
inline std::string* KernelDef::_internal_mutable_op() {
  
  return _impl_.op_.Mutable(GetArenaForAllocation());
}
inline std::string* KernelDef::release_op() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.op)
  return _impl_.op_.Release();
}
inline void KernelDef::set_allocated_op(std::string* op) {
  if (op != nullptr) {
    
  } else {
    
  }
  _impl_.op_.SetAllocated(op, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_.IsDefault()) {
    _impl_.op_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.op)
}

// string device_type = 2;
inline void KernelDef::clear_device_type() {
  _impl_.device_type_.ClearToEmpty();
}
inline const std::string& KernelDef::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.device_type)
  return _internal_device_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void KernelDef::set_device_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.device_type)
}
inline std::string* KernelDef::mutable_device_type() {
  std::string* _s = _internal_mutable_device_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.device_type)
  return _s;
}
inline const std::string& KernelDef::_internal_device_type() const {
  return _impl_.device_type_.Get();
}
inline void KernelDef::_internal_set_device_type(const std::string& value) {
  
  _impl_.device_type_.Set(value, GetArenaForAllocation());
}
inline std::string* KernelDef::_internal_mutable_device_type() {
  
  return _impl_.device_type_.Mutable(GetArenaForAllocation());
}
inline std::string* KernelDef::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.device_type)
  return _impl_.device_type_.Release();
}
inline void KernelDef::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  _impl_.device_type_.SetAllocated(device_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_type_.IsDefault()) {
    _impl_.device_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.device_type)
}

// repeated .tensorflow.KernelDef.AttrConstraint constraint = 3;
inline int KernelDef::_internal_constraint_size() const {
  return _impl_.constraint_.size();
}
inline int KernelDef::constraint_size() const {
  return _internal_constraint_size();
}
inline void KernelDef::clear_constraint() {
  _impl_.constraint_.Clear();
}
inline ::tensorflow::KernelDef_AttrConstraint* KernelDef::mutable_constraint(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.constraint)
  return _impl_.constraint_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint >*
KernelDef::mutable_constraint() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.KernelDef.constraint)
  return &_impl_.constraint_;
}
inline const ::tensorflow::KernelDef_AttrConstraint& KernelDef::_internal_constraint(int index) const {
  return _impl_.constraint_.Get(index);
}
inline const ::tensorflow::KernelDef_AttrConstraint& KernelDef::constraint(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.constraint)
  return _internal_constraint(index);
}
inline ::tensorflow::KernelDef_AttrConstraint* KernelDef::_internal_add_constraint() {
  return _impl_.constraint_.Add();
}
inline ::tensorflow::KernelDef_AttrConstraint* KernelDef::add_constraint() {
  ::tensorflow::KernelDef_AttrConstraint* _add = _internal_add_constraint();
  // @@protoc_insertion_point(field_add:tensorflow.KernelDef.constraint)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef_AttrConstraint >&
KernelDef::constraint() const {
  // @@protoc_insertion_point(field_list:tensorflow.KernelDef.constraint)
  return _impl_.constraint_;
}

// repeated string host_memory_arg = 4;
inline int KernelDef::_internal_host_memory_arg_size() const {
  return _impl_.host_memory_arg_.size();
}
inline int KernelDef::host_memory_arg_size() const {
  return _internal_host_memory_arg_size();
}
inline void KernelDef::clear_host_memory_arg() {
  _impl_.host_memory_arg_.Clear();
}
inline std::string* KernelDef::add_host_memory_arg() {
  std::string* _s = _internal_add_host_memory_arg();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.KernelDef.host_memory_arg)
  return _s;
}
inline const std::string& KernelDef::_internal_host_memory_arg(int index) const {
  return _impl_.host_memory_arg_.Get(index);
}
inline const std::string& KernelDef::host_memory_arg(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.host_memory_arg)
  return _internal_host_memory_arg(index);
}
inline std::string* KernelDef::mutable_host_memory_arg(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.host_memory_arg)
  return _impl_.host_memory_arg_.Mutable(index);
}
inline void KernelDef::set_host_memory_arg(int index, const std::string& value) {
  _impl_.host_memory_arg_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::set_host_memory_arg(int index, std::string&& value) {
  _impl_.host_memory_arg_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::set_host_memory_arg(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.host_memory_arg_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::set_host_memory_arg(int index, const char* value, size_t size) {
  _impl_.host_memory_arg_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.KernelDef.host_memory_arg)
}
inline std::string* KernelDef::_internal_add_host_memory_arg() {
  return _impl_.host_memory_arg_.Add();
}
inline void KernelDef::add_host_memory_arg(const std::string& value) {
  _impl_.host_memory_arg_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::add_host_memory_arg(std::string&& value) {
  _impl_.host_memory_arg_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::add_host_memory_arg(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.host_memory_arg_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.KernelDef.host_memory_arg)
}
inline void KernelDef::add_host_memory_arg(const char* value, size_t size) {
  _impl_.host_memory_arg_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.KernelDef.host_memory_arg)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
KernelDef::host_memory_arg() const {
  // @@protoc_insertion_point(field_list:tensorflow.KernelDef.host_memory_arg)
  return _impl_.host_memory_arg_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
KernelDef::mutable_host_memory_arg() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.KernelDef.host_memory_arg)
  return &_impl_.host_memory_arg_;
}

// string label = 5;
inline void KernelDef::clear_label() {
  _impl_.label_.ClearToEmpty();
}
inline const std::string& KernelDef::label() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.label)
  return _internal_label();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void KernelDef::set_label(ArgT0&& arg0, ArgT... args) {
 
 _impl_.label_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.label)
}
inline std::string* KernelDef::mutable_label() {
  std::string* _s = _internal_mutable_label();
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelDef.label)
  return _s;
}
inline const std::string& KernelDef::_internal_label() const {
  return _impl_.label_.Get();
}
inline void KernelDef::_internal_set_label(const std::string& value) {
  
  _impl_.label_.Set(value, GetArenaForAllocation());
}
inline std::string* KernelDef::_internal_mutable_label() {
  
  return _impl_.label_.Mutable(GetArenaForAllocation());
}
inline std::string* KernelDef::release_label() {
  // @@protoc_insertion_point(field_release:tensorflow.KernelDef.label)
  return _impl_.label_.Release();
}
inline void KernelDef::set_allocated_label(std::string* label) {
  if (label != nullptr) {
    
  } else {
    
  }
  _impl_.label_.SetAllocated(label, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.label_.IsDefault()) {
    _impl_.label_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.KernelDef.label)
}

// int32 priority = 6;
inline void KernelDef::clear_priority() {
  _impl_.priority_ = 0;
}
inline int32_t KernelDef::_internal_priority() const {
  return _impl_.priority_;
}
inline int32_t KernelDef::priority() const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelDef.priority)
  return _internal_priority();
}
inline void KernelDef::_internal_set_priority(int32_t value) {
  
  _impl_.priority_ = value;
}
inline void KernelDef::set_priority(int32_t value) {
  _internal_set_priority(value);
  // @@protoc_insertion_point(field_set:tensorflow.KernelDef.priority)
}

// -------------------------------------------------------------------

// KernelList

// repeated .tensorflow.KernelDef kernel = 1;
inline int KernelList::_internal_kernel_size() const {
  return _impl_.kernel_.size();
}
inline int KernelList::kernel_size() const {
  return _internal_kernel_size();
}
inline void KernelList::clear_kernel() {
  _impl_.kernel_.Clear();
}
inline ::tensorflow::KernelDef* KernelList::mutable_kernel(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.KernelList.kernel)
  return _impl_.kernel_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef >*
KernelList::mutable_kernel() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.KernelList.kernel)
  return &_impl_.kernel_;
}
inline const ::tensorflow::KernelDef& KernelList::_internal_kernel(int index) const {
  return _impl_.kernel_.Get(index);
}
inline const ::tensorflow::KernelDef& KernelList::kernel(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.KernelList.kernel)
  return _internal_kernel(index);
}
inline ::tensorflow::KernelDef* KernelList::_internal_add_kernel() {
  return _impl_.kernel_.Add();
}
inline ::tensorflow::KernelDef* KernelList::add_kernel() {
  ::tensorflow::KernelDef* _add = _internal_add_kernel();
  // @@protoc_insertion_point(field_add:tensorflow.KernelList.kernel)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::KernelDef >&
KernelList::kernel() const {
  // @@protoc_insertion_point(field_list:tensorflow.KernelList.kernel)
  return _impl_.kernel_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fframework_2fkernel_5fdef_2eproto
