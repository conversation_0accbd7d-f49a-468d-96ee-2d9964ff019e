/*
 * Copyright 2017 <PERSON>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

import "mfobjects.idl";

cpp_quote("#define MFT_OUTPUT_BOUND_LOWER_UNBOUNDED MINLONGLONG")
cpp_quote("#define MFT_OUTPUT_BOUND_UPPER_UNBOUNDED MAXLONGLONG")

typedef [v1_enum] enum _MFT_MESSAGE_TYPE
{
    MFT_MESSAGE_COMMAND_FLUSH                   = 0x00000000,
    MFT_MESSAGE_COMMAND_DRAIN                   = 0x00000001,
    MFT_MESSAGE_SET_D3D_MANAGER                 = 0x00000002,
    MFT_MESSAGE_DROP_SAMPLES                    = 0x00000003,
    MFT_MESSAGE_COMMAND_TICK                    = 0x00000004,
    MFT_MESSAGE_NOTIFY_BEGIN_STREAMING          = 0x10000000,
    MFT_MESSAGE_NOTIFY_END_STREAMING            = 0x10000001,
    MFT_MESSAGE_NOTIFY_END_OF_STREAM            = 0x10000002,
    MFT_MESSAGE_NOTIFY_START_OF_STREAM          = 0x10000003,
    MFT_MESSAGE_NOTIFY_RELEASE_RESOURCES        = 0x10000004,
    MFT_MESSAGE_NOTIFY_REACQUIRE_RESOURCES      = 0x10000005,
    MFT_MESSAGE_NOTIFY_EVENT                    = 0x10000006,
    MFT_MESSAGE_COMMAND_SET_OUTPUT_STREAM_STATE = 0x10000007,
    MFT_MESSAGE_COMMAND_FLUSH_OUTPUT_STREAM     = 0x10000008,
    MFT_MESSAGE_COMMAND_MARKER                  = 0x20000000
} MFT_MESSAGE_TYPE;

enum _MFT_SET_TYPE_FLAGS
{
    MFT_SET_TYPE_TEST_ONLY = 0x00000001,
};

enum _MFT_INPUT_STREAM_INFO_FLAGS
{
    MFT_INPUT_STREAM_WHOLE_SAMPLES            = 0x00000001,
    MFT_INPUT_STREAM_SINGLE_SAMPLE_PER_BUFFER = 0x00000002,
    MFT_INPUT_STREAM_FIXED_SAMPLE_SIZE        = 0x00000004,
    MFT_INPUT_STREAM_HOLDS_BUFFERS            = 0x00000008,
    MFT_INPUT_STREAM_DOES_NOT_ADDREF          = 0x00000100,
    MFT_INPUT_STREAM_REMOVABLE                = 0x00000200,
    MFT_INPUT_STREAM_OPTIONAL                 = 0x00000400,
    MFT_INPUT_STREAM_PROCESSES_IN_PLACE       = 0x00000800
};

enum _MFT_INPUT_STATUS_FLAGS
{
    MFT_INPUT_STATUS_ACCEPT_DATA = 0x00000001,
};

enum _MFT_INPUT_DATA_BUFFER_FLAGS
{
    MFT_INPUT_DATA_BUFFER_PLACEHOLDER = 0xffffffff,
};

enum _MFT_OUTPUT_STREAM_INFO_FLAGS
{
    MFT_OUTPUT_STREAM_WHOLE_SAMPLES            = 0x00000001,
    MFT_OUTPUT_STREAM_SINGLE_SAMPLE_PER_BUFFER = 0x00000002,
    MFT_OUTPUT_STREAM_FIXED_SAMPLE_SIZE        = 0x00000004,
    MFT_OUTPUT_STREAM_DISCARDABLE              = 0x00000008,
    MFT_OUTPUT_STREAM_OPTIONAL                 = 0x00000010,
    MFT_OUTPUT_STREAM_PROVIDES_SAMPLES         = 0x00000100,
    MFT_OUTPUT_STREAM_CAN_PROVIDE_SAMPLES      = 0x00000200,
    MFT_OUTPUT_STREAM_LAZY_READ                = 0x00000400,
    MFT_OUTPUT_STREAM_REMOVABLE                = 0x00000800
};

enum _MFT_OUTPUT_DATA_BUFFER_FLAGS
{
    MFT_OUTPUT_DATA_BUFFER_INCOMPLETE    = 0x01000000,
    MFT_OUTPUT_DATA_BUFFER_FORMAT_CHANGE = 0x00000100,
    MFT_OUTPUT_DATA_BUFFER_STREAM_END    = 0x00000200,
    MFT_OUTPUT_DATA_BUFFER_NO_SAMPLE     = 0x00000300
};

enum _MFT_OUTPUT_STATUS_FLAGS
{
    MFT_OUTPUT_STATUS_SAMPLE_READY = 0x00000001
};

enum _MFT_PROCESS_OUTPUT_FLAGS
{
    MFT_PROCESS_OUTPUT_DISCARD_WHEN_NO_BUFFER = 0x00000001,
    MFT_PROCESS_OUTPUT_REGENERATE_LAST_OUTPUT = 0x00000002
};

enum _MFT_PROCESS_OUTPUT_STATUS
{
    MFT_PROCESS_OUTPUT_STATUS_NEW_STREAMS = 0x00000100
};

enum _MFT_DRAIN_TYPE
{
    MFT_DRAIN_PRODUCE_TAILS = 0x00000000,
    MFT_DRAIN_NO_TAILS      = 0x00000001
};

typedef enum _MF3DVideoOutputType
{
    MF3DVideoOutputType_BaseView,
    MF3DVideoOutputType_Stereo
} MF3DVideoOutputType;

typedef struct _MFT_INPUT_STREAM_INFO
{
    LONGLONG hnsMaxLatency;
    DWORD    dwFlags;
    DWORD    cbSize;
    DWORD    cbMaxLookahead;
    DWORD    cbAlignment;
} MFT_INPUT_STREAM_INFO;

typedef struct _MFT_OUTPUT_STREAM_INFO
{
    DWORD dwFlags;
    DWORD cbSize;
    DWORD cbAlignment;
} MFT_OUTPUT_STREAM_INFO;

typedef struct _MFT_OUTPUT_DATA_BUFFER
{
    DWORD         dwStreamID;
    IMFSample     *pSample;
    DWORD         dwStatus;
    IMFCollection *pEvents;
} MFT_OUTPUT_DATA_BUFFER, *PMFT_OUTPUT_DATA_BUFFER;

[
    object,
    uuid(bf94c121-5b05-4e6f-8000-ba598961414d)
]
interface IMFTransform : IUnknown
{
    HRESULT GetStreamLimits([out] DWORD *input_minimum, [out] DWORD *input_maximum, [out] DWORD *output_minimum,
                            [out] DWORD *output_maximum);

    HRESULT GetStreamCount([out] DWORD *inputs,  [out] DWORD *outputs);

    HRESULT GetStreamIDs([in] DWORD input_size, [out,size_is(input_size)] DWORD *inputs,
                         [in] DWORD output_size, [out,size_is(output_size)] DWORD *outputs);

    HRESULT GetInputStreamInfo([in] DWORD id, [out] MFT_INPUT_STREAM_INFO *info);

    HRESULT GetOutputStreamInfo([in] DWORD id, [out] MFT_OUTPUT_STREAM_INFO *info);

    HRESULT GetAttributes([out] IMFAttributes **attributes);

    HRESULT GetInputStreamAttributes([in] DWORD id, [out] IMFAttributes **attributes);

    HRESULT GetOutputStreamAttributes([in] DWORD id, [out] IMFAttributes **attributes);

    HRESULT DeleteInputStream([in] DWORD id);

    HRESULT AddInputStreams([in] DWORD streams, [in] DWORD *ids);

    HRESULT GetInputAvailableType([in] DWORD id, [in] DWORD index, [out] IMFMediaType **type);

    HRESULT GetOutputAvailableType([in] DWORD id, [in] DWORD index, [out] IMFMediaType **type);

    HRESULT SetInputType(DWORD id, [in] IMFMediaType *type, [in] DWORD flags);

    HRESULT SetOutputType(DWORD id, [in] IMFMediaType *type, [in] DWORD flags);

    HRESULT GetInputCurrentType([in] DWORD id, [out] IMFMediaType **type);

    HRESULT GetOutputCurrentType([in] DWORD id, [out] IMFMediaType **type);

    HRESULT GetInputStatus([in] DWORD id, [out] DWORD *flags);

    HRESULT GetOutputStatus([out] DWORD *flags);

    HRESULT SetOutputBounds([in] LONGLONG lower, [in] LONGLONG upper);

    HRESULT ProcessEvent([in] DWORD id, [in] IMFMediaEvent *event);

    HRESULT ProcessMessage([in] MFT_MESSAGE_TYPE message, [in] ULONG_PTR param);

    [local] HRESULT ProcessInput([in] DWORD id, [in] IMFSample *sample, [in] DWORD flags);

    [local] HRESULT ProcessOutput([in] DWORD flags, [in] DWORD count, [in,out,size_is(count)] MFT_OUTPUT_DATA_BUFFER *samples,
                                  [out] DWORD *status);
}

cpp_quote("HRESULT WINAPI MFCreateTransformActivate(IMFActivate **activate);")

cpp_quote("EXTERN_GUID(MF_SA_BUFFERS_PER_SAMPLE,              0x873c5171, 0x1e3d, 0x4e25, 0x98, 0x8d, 0xb4, 0x33, 0xce, 0x04, 0x19, 0x83);")
cpp_quote("EXTERN_GUID(MF_SA_D3D_AWARE,                       0xeaa35c29, 0x775e, 0x488e, 0x9b, 0x61, 0xb3, 0x28, 0x3e, 0x49, 0x58, 0x3b);")
cpp_quote("EXTERN_GUID(MF_SA_D3D11_ALLOW_DYNAMIC_YUV_TEXTURE, 0xce06d49f, 0x0613, 0x4b9d, 0x86, 0xa6, 0xd8, 0xc4, 0xf9, 0xc1, 0x00, 0x75);")
cpp_quote("EXTERN_GUID(MF_SA_D3D11_AWARE,                0x206b4fc8, 0xfcf9, 0x4c51, 0xaf, 0xe3, 0x97, 0x64, 0x36, 0x9e, 0x33, 0xa0);")
cpp_quote("EXTERN_GUID(MF_SA_D3D11_BINDFLAGS,            0xeacf97ad, 0x065c, 0x4408, 0xbe, 0xe3, 0xfd, 0xcb, 0xfd, 0x12, 0x8b, 0xe2);")
cpp_quote("EXTERN_GUID(MF_SA_D3D11_HW_PROTECTED,         0x3a8ba9d9, 0x92ca, 0x4307, 0xa3, 0x91, 0x69, 0x99, 0xdb, 0xf3, 0xb6, 0xce);")
cpp_quote("EXTERN_GUID(MF_SA_D3D11_SHARED,               0x7b8f32c3, 0x6d96, 0x4b89, 0x92, 0x03, 0xdd, 0x38, 0xb6, 0x14, 0x14, 0xf3);")
cpp_quote("EXTERN_GUID(MF_SA_D3D11_SHARED_WITHOUT_MUTEX, 0x39dbd44d, 0x2e44, 0x4931, 0xa4, 0xc8, 0x35, 0x2d, 0x3d, 0xc4, 0x21, 0x15);")
cpp_quote("EXTERN_GUID(MF_SA_D3D11_USAGE,                0xe85fe442, 0x2ca3, 0x486e, 0xa9, 0xc7, 0x10, 0x9d, 0xda, 0x60, 0x98, 0x80);")
cpp_quote("EXTERN_GUID(MF_SA_REQUIRED_SAMPLE_COUNT,     0x18802c61, 0x324b, 0x4952, 0xab, 0xd0, 0x17, 0x6f, 0xf5, 0xc6, 0x96, 0xff);")
cpp_quote("EXTERN_GUID(MF_SA_REQUIRED_SAMPLE_COUNT_PROGRESSIVE, 0xb172d58e, 0xfa77, 0x4e48, 0x8d, 0x2a, 0x1d, 0xf2, 0xd8, 0x50, 0xea, 0xc2);")
cpp_quote("EXTERN_GUID(MF_SA_MINIMUM_OUTPUT_SAMPLE_COUNT, 0x851745d5, 0xc3d6, 0x476d, 0x95, 0x27, 0x49, 0x8e, 0xf2, 0xd1, 0x0d, 0x18);")
cpp_quote("EXTERN_GUID(MF_SA_MINIMUM_OUTPUT_SAMPLE_COUNT_PROGRESSIVE, 0x0f5523a5, 0x1cb2, 0x47c5, 0xa5, 0x50, 0x2e, 0xeb, 0x84, 0xb4, 0xd1, 0x4a);")
cpp_quote("EXTERN_GUID(MF_TRANSFORM_ASYNC,              0xf81a699a, 0x649a, 0x497d, 0x8c, 0x73, 0x29, 0xf8, 0xfe, 0xd6, 0xad, 0x7a);")
cpp_quote("EXTERN_GUID(MF_TRANSFORM_ASYNC_UNLOCK,       0xe5666d6b, 0x3422, 0x4eb6, 0xa4, 0x21, 0xda, 0x7d, 0xb1, 0xf8, 0xe2, 0x07);")
cpp_quote("EXTERN_GUID(MF_TRANSFORM_CATEGORY_Attribute, 0xceabba49, 0x506d, 0x4757, 0xa6, 0xff, 0x66, 0xc1, 0x84, 0x98, 0x7e, 0x4e);")
cpp_quote("EXTERN_GUID(MF_TRANSFORM_FLAGS_Attribute,    0x9359bb7e, 0x6275, 0x46c4, 0xa0, 0x25, 0x1c, 0x01, 0xe4, 0x5f, 0x1a, 0x86);")
cpp_quote("EXTERN_GUID(MFT_CODEC_MERIT_Attribute,       0x88a7cb15, 0x7b07, 0x4a34, 0x91, 0x28, 0xe6, 0x4c, 0x67, 0x03, 0xc4, 0xd3);")
cpp_quote("EXTERN_GUID(MFT_CONNECTED_STREAM_ATTRIBUTE,  0x71eeb820, 0xa59f, 0x4de2, 0xbc, 0xec, 0x38, 0xdb, 0x1d, 0xd6, 0x11, 0xa4);")
cpp_quote("EXTERN_GUID(MFT_CONNECTED_TO_HW_STREAM,      0x34e6e728, 0x06d6, 0x4491, 0xa5, 0x53, 0x47, 0x95, 0x65, 0x0d, 0xb9, 0x12);")
cpp_quote("EXTERN_GUID(MFT_DECODER_EXPOSE_OUTPUT_TYPES_IN_NATIVE_ORDER, 0xef80833f, 0xf8fa, 0x44d9, 0x80, 0xd8, 0x41, 0xed, 0x62, 0x32, 0x67, 0x0c);")
cpp_quote("EXTERN_GUID(MFT_DECODER_FINAL_VIDEO_RESOLUTION_HINT, 0xdc2f8496, 0x15c4, 0x407a, 0xb6, 0xf0, 0x1b, 0x66, 0xab, 0x5f, 0xbf, 0x53);")
cpp_quote("EXTERN_GUID(MFT_ENCODER_SUPPORTS_CONFIG_EVENT,       0x86a355ae, 0x3a77, 0x4ec4, 0x9f, 0x31, 0x01, 0x14, 0x9a, 0x4e, 0x92, 0xde);")
cpp_quote("EXTERN_GUID(MFT_ENUM_HARDWARE_URL_Attribute,         0x2fb866ac, 0xb078, 0x4942, 0xab, 0x6c, 0x00, 0x3d, 0x05, 0xcd, 0xa6, 0x74);")
cpp_quote("EXTERN_GUID(MFT_ENUM_HARDWARE_VENDOR_ID_Attribute,   0x3aecb0cc, 0x035b, 0x4bcc, 0x81, 0x85, 0x2b, 0x8d, 0x55, 0x1e, 0xf3, 0xaf);")
cpp_quote("EXTERN_GUID(MFT_ENUM_TRANSCODE_ONLY_ATTRIBUTE,       0x111ea8cd, 0xb62a, 0x4bdb, 0x89, 0xf6, 0x67, 0xff, 0xcd, 0xc2, 0x45, 0x8b);")
cpp_quote("EXTERN_GUID(MFT_FIELDOFUSE_UNLOCK_Attribute,         0x8ec2e9fd, 0x9148, 0x410d, 0x83, 0x1e, 0x70, 0x24, 0x39, 0x46, 0x1a, 0x8e);")
cpp_quote("EXTERN_GUID(MFT_FRIENDLY_NAME_Attribute,             0x314ffbae, 0x5b41, 0x4c95, 0x9c, 0x19, 0x4e, 0x7d, 0x58, 0x6f, 0xac, 0xe3);")
cpp_quote("EXTERN_GUID(MFT_INPUT_TYPES_Attributes,              0x4276c9b1, 0x759d, 0x4bf3, 0x9c, 0xd0, 0x0d, 0x72, 0x3d, 0x13, 0x8f, 0x96);")
cpp_quote("EXTERN_GUID(MFT_OUTPUT_TYPES_Attributes,             0x8eae8cf3, 0xa44f, 0x4306, 0xba, 0x5c, 0xbf, 0x5d, 0xda, 0x24, 0x28, 0x18);")
cpp_quote("EXTERN_GUID(MFT_PREFERRED_ENCODER_PROFILE,           0x53004909, 0x1ef5, 0x46d7, 0xa1, 0x8e, 0x5a, 0x75, 0xf8, 0xb5, 0x90, 0x5f);")
cpp_quote("EXTERN_GUID(MFT_PREFERRED_OUTPUTTYPE_Attribute,      0x7e700499, 0x396a, 0x49ee, 0xb1, 0xb4, 0xf6, 0x28, 0x02, 0x1e, 0x8c, 0x9d);")
cpp_quote("EXTERN_GUID(MFT_PROCESS_LOCAL_Attribute,             0x543186e4, 0x4649, 0x4e65, 0xb5, 0x88, 0x4a, 0xa3, 0x52, 0xaf, 0xf3, 0x79);")
cpp_quote("EXTERN_GUID(MFT_REMUX_MARK_I_PICTURE_AS_CLEAN_POINT, 0x364e8f85, 0x3f2e, 0x436c, 0xb2, 0xa2, 0x44, 0x40, 0xa0, 0x12, 0xa9, 0xe8);")
cpp_quote("EXTERN_GUID(MFT_SUPPORT_3DVIDEO,                     0x093f81b1, 0x4f2e, 0x4631, 0x81, 0x68, 0x79, 0x34, 0x03, 0x2a, 0x01, 0xd3);")
cpp_quote("EXTERN_GUID(MFT_TRANSFORM_CLSID_Attribute,           0x6821c42b, 0x65a4, 0x4e82, 0x99, 0xbc, 0x9a, 0x88, 0x20, 0x5e, 0xcd, 0x0c);")

cpp_quote("DEFINE_PROPERTYKEY(MFPKEY_CLSID,                     0xc57a84c0, 0x1a80, 0x40a3, 0x97, 0xb5, 0x92, 0x72, 0xa4, 0x03, 0xc8, 0xae, 1);")
cpp_quote("DEFINE_PROPERTYKEY(MFPKEY_CATEGORY,                  0xc57a84c0, 0x1a80, 0x40a3, 0x97, 0xb5, 0x92, 0x72, 0xa4, 0x03, 0xc8, 0xae, 2);")
cpp_quote("DEFINE_PROPERTYKEY(MFPKEY_EXATTRIBUTE_SUPPORTED,     0x456fe843, 0x3c87, 0x40c0, 0x94, 0x9d, 0x14, 0x09, 0xc9, 0x7d, 0xab, 0x2c, 1);")
cpp_quote("DEFINE_PROPERTYKEY(MFPKEY_MULTICHANNEL_CHANNEL_MASK, 0x58bdaf8c, 0x3224, 0x4692, 0x86, 0xd0, 0x44, 0xd6, 0x5c, 0x5b, 0xf8, 0x2b, 1);")
