.\" DO NOT MODIFY THIS FILE!  It was generated by gdoc.
.TH "gnutls_x509_rdn_get_by_oid" 3 "3.8.9" "gnutls" "gnutls"
.SH NAME
gnutls_x509_rdn_get_by_oid \- API function
.SH SYNOPSIS
.B #include <gnutls/x509.h>
.sp
.BI "int gnutls_x509_rdn_get_by_oid(const gnutls_datum_t * " idn ", const char * " oid ", unsigned " indx ", unsigned int " raw_flag ", void * " buf ", size_t * " buf_size ");"
.SH ARGUMENTS
.IP "const gnutls_datum_t * idn" 12
should contain a DER encoded RDN sequence
.IP "const char * oid" 12
an Object Identifier
.IP "unsigned indx" 12
In case multiple same OIDs exist in the RDN indicates which
to send. Use 0 for the first one.
.IP "unsigned int raw_flag" 12
If non\-zero then the raw DER data are returned.
.IP "void * buf" 12
a pointer to a structure to hold the peer's name
.IP "size_t * buf_size" 12
holds the size of  \fIbuf\fP 
.SH "DESCRIPTION"
This function will return the name of the given Object identifier,
of the RDN sequence.  The name will be encoded using the rules
from RFC4514.
.SH "RETURNS"
On success, \fBGNUTLS_E_SUCCESS\fP (0) is returned, or
\fBGNUTLS_E_SHORT_MEMORY_BUFFER\fP is returned and * \fIbuf_size\fP is
updated if the provided buffer is not long enough, otherwise a
negative error value.
.SH "REPORTING BUGS"
Report bugs to <<EMAIL>>.
.br
Home page: https://www.gnutls.org

.SH COPYRIGHT
Copyright \(co 2001-2023 Free Software Foundation, Inc., and others.
.br
Copying and distribution of this file, with or without modification,
are permitted in any medium without royalty provided the copyright
notice and this notice are preserved.
.SH "SEE ALSO"
The full documentation for
.B gnutls
is maintained as a Texinfo manual.
If the /usr/share/doc/gnutls/
directory does not contain the HTML form visit
.B
.IP https://www.gnutls.org/manual/
.PP
