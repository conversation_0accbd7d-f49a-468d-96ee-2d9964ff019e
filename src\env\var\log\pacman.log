[2025-06-22T10:11:18+0000] [PACMAN] Running 'pacman -Syu --root /d/a/msys2-installer/msys2-installer/_build/newmsys/msys64'
[2025-06-22T10:11:18+0000] [PACMAN] synchronizing package lists
[2025-06-22T10:11:21+0000] [PACMAN] starting full system upgrade
[2025-06-22T10:11:21+0000] [PACMAN] Running 'pacman -S --noconfirm --root /d/a/msys2-installer/msys2-installer/_build/newmsys/msys64 filesystem msys2-runtime'
[2025-06-22T10:11:24+0000] [ALPM] transaction started
[2025-06-22T10:11:24+0000] [ALPM] installed filesystem (2025.05.08-2)
[2025-06-22T10:11:24+0000] [ALPM] installed msys2-runtime (3.6.3-3)
[2025-06-22T10:11:24+0000] [ALPM] transaction completed
[2025-06-22T10:11:25+0000] [PACMAN] Running 'pacman -S --noconfirm --root /d/a/msys2-installer/msys2-installer/_build/newmsys/msys64 base'
[2025-06-22T10:11:49+0000] [ALPM] transaction started
[2025-06-22T10:11:49+0000] [ALPM] installed bash (5.2.037-2)
[2025-06-22T10:11:51+0000] [ALPM] installed bash-completion (2.16.0-1)
[2025-06-22T10:11:51+0000] [ALPM] installed gcc-libs (13.4.0-2)
[2025-06-22T10:11:51+0000] [ALPM] installed libbz2 (1.0.8-4)
[2025-06-22T10:11:51+0000] [ALPM] installed libintl (0.22.5-1)
[2025-06-22T10:11:51+0000] [ALPM] installed libiconv (1.18-1)
[2025-06-22T10:11:51+0000] [ALPM] installed libexpat (2.7.1-1)
[2025-06-22T10:11:51+0000] [ALPM] installed liblzma (5.8.1-1)
[2025-06-22T10:11:51+0000] [ALPM] installed liblz4 (1.10.0-1)
[2025-06-22T10:11:51+0000] [ALPM] installed libopenssl (3.5.0-1)
[2025-06-22T10:11:51+0000] [ALPM] installed libzstd (1.5.7-1)
[2025-06-22T10:11:51+0000] [ALPM] installed zlib (1.3.1-1)
[2025-06-22T10:11:51+0000] [ALPM] installed bsdtar (3.8.1-1)
[2025-06-22T10:11:51+0000] [ALPM] installed bzip2 (1.0.8-4)
[2025-06-22T10:11:51+0000] [ALPM] installed gmp (6.3.0-1)
[2025-06-22T10:11:52+0000] [ALPM] installed coreutils (8.32-5)
[2025-06-22T10:11:52+0000] [ALPM] installed openssl (3.5.0-1)
[2025-06-22T10:11:52+0000] [ALPM] installed findutils (4.10.0-2)
[2025-06-22T10:11:52+0000] [ALPM] installed sed (4.9-1)
[2025-06-22T10:11:52+0000] [ALPM] installed libffi (3.5.0-1)
[2025-06-22T10:12:01+0000] [ALPM] installed ncurses (6.5.20240831-2)
[2025-06-22T10:12:01+0000] [ALPM] installed libpcre2_8 (10.45-1)
[2025-06-22T10:12:01+0000] [ALPM] installed less (679-1)
[2025-06-22T10:12:01+0000] [ALPM] installed gzip (1.14-1)
[2025-06-22T10:12:01+0000] [ALPM] installed libxcrypt (4.4.38-1)
[2025-06-22T10:12:01+0000] [ALPM] installed info (7.2-1)
[2025-06-22T10:12:01+0000] [ALPM] installed libtasn1 (4.20.0-1)
[2025-06-22T10:12:01+0000] [ALPM] installed libp11-kit (0.25.5-2)
[2025-06-22T10:12:01+0000] [ALPM] installed p11-kit (0.25.5-2)
[2025-06-22T10:12:01+0000] [ALPM] installed ca-certificates (20241223-1)
[2025-06-22T10:12:02+0000] [ALPM] installed brotli (1.1.0-2)
[2025-06-22T10:12:02+0000] [ALPM] installed libdb (6.2.32-5)
[2025-06-22T10:12:02+0000] [ALPM] installed libedit (20240808_3.1-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libsqlite (3.50.1-1)
[2025-06-22T10:12:02+0000] [ALPM] installed heimdal-libs (7.8.0-5)
[2025-06-22T10:12:02+0000] [ALPM] installed libunistring (1.3-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libidn2 (2.3.8-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libnghttp2 (1.66.0-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libpsl (0.21.5-2)
[2025-06-22T10:12:02+0000] [ALPM] installed libssh2 (1.11.1-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libcurl (8.14.1-1)
[2025-06-22T10:12:03+0000] [ALPM] installed curl (8.14.1-1)
[2025-06-22T10:12:03+0000] [ALPM] installed libpcre (8.45-5)
[2025-06-22T10:12:03+0000] [ALPM] installed grep (1~3.0-7)
[2025-06-22T10:12:03+0000] [ALPM] installed dash (0.5.12-1)
[2025-06-22T10:12:03+0000] [ALPM] installed file (5.46-2)
[2025-06-22T10:12:03+0000] [ALPM] installed mpfr (4.2.2-1)
[2025-06-22T10:12:03+0000] [ALPM] installed libreadline (8.2.013-1)
[2025-06-22T10:12:03+0000] [ALPM] installed gawk (5.3.2-1)
[2025-06-22T10:12:03+0000] [ALPM] installed libargp (20241207-1)
[2025-06-22T10:12:03+0000] [ALPM] installed getent (2.18.90-5)
[2025-06-22T10:12:03+0000] [ALPM] installed inetutils (2.6-1)
[2025-06-22T10:12:04+0000] [ALPM] installed mintty (1~3.7.8-1)
[2025-06-22T10:12:04+0000] [ALPM] installed msys2-keyring (1~20250619-1)
[2025-06-22T10:12:04+0000] [ALPM] installed msys2-launcher (1.5-3)
[2025-06-22T10:12:04+0000] [ALPM] installed nano (8.5-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libgettextpo (0.22.5-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libasprintf (0.22.5-1)
[2025-06-22T10:12:04+0000] [ALPM] installed gettext (0.22.5-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libgpg-error (1.55-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libassuan (3.0.2-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libgcrypt (1.11.1-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libhogweed (3.10.1-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libnettle (3.10.1-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libgnutls (3.8.9-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libksba (1.6.7-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libnpth (1.8-1)
[2025-06-22T10:12:04+0000] [ALPM] installed nettle (3.10.1-1)
[2025-06-22T10:12:04+0000] [ALPM] installed pinentry (1.3.1-2)
[2025-06-22T10:12:04+0000] [ALPM] installed gnupg (2.4.8-1)
[2025-06-22T10:12:04+0000] [ALPM] installed pacman-mirrors (20250607-1)
[2025-06-22T10:12:04+0000] [ALPM] installed which (2.23-4)
[2025-06-22T10:12:05+0000] [ALPM] installed xz (5.8.1-1)
[2025-06-22T10:12:05+0000] [ALPM] installed zstd (1.5.7-1)
[2025-06-22T10:12:06+0000] [ALPM] installed pacman (6.1.0-16)
[2025-06-22T10:12:06+0000] [ALPM] installed db (6.2.32-5)
[2025-06-22T10:12:06+0000] [ALPM] installed libgdbm (1.25-1)
[2025-06-22T10:12:06+0000] [ALPM] installed gdbm (1.25-1)
[2025-06-22T10:12:08+0000] [ALPM] installed perl (5.38.4-2)
[2025-06-22T10:12:09+0000] [ALPM] installed pacman-contrib (1.10.6-1)
[2025-06-22T10:12:09+0000] [ALPM] installed rebase (4.5.0-5)
[2025-06-22T10:12:09+0000] [ALPM] installed tar (1.35-2)
[2025-06-22T10:12:09+0000] [ALPM] installed time (1.9-3)
[2025-06-22T10:12:12+0000] [ALPM] installed tzcode (2025b-1)
[2025-06-22T10:12:12+0000] [ALPM] installed libutil-linux (2.40.2-2)
[2025-06-22T10:12:12+0000] [ALPM] installed util-linux (2.40.2-2)
[2025-06-22T10:12:12+0000] [ALPM] installed wget (1.25.0-1)
[2025-06-22T10:12:12+0000] [ALPM] installed base (2022.06-1)
[2025-06-22T10:12:12+0000] [ALPM] transaction completed
[2025-06-22T10:12:12+0000] [ALPM] running 'texinfo-install.hook'...
