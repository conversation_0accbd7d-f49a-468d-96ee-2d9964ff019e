// This file is part of Eigen, a lightweight C++ template library
// for linear algebra.
//
//
// This Source Code Form is subject to the terms of the Mozilla
// Public License v. 2.0. If a copy of the MPL was not distributed
// with this file, You can obtain one at http://mozilla.org/MPL/2.0/.

#ifndef EIGEN_ARPACKSUPPORT_MODULE_H
#define EIGEN_ARPACKSUPPORT_MODULE_H

#include "../../Eigen/Core"

/** \defgroup ArpackSupport_Module Arpack support module
 *
 * This module provides a wrapper to <PERSON><PERSON><PERSON>, a library for sparse eigenvalue decomposition.
 *
 * \code
 * #include <Eigen/ArpackSupport>
 * \endcode
 */

#include "../../Eigen/SparseCholesky"

#include "../../Eigen/src/Core/util/DisableStupidWarnings.h"

// IWYU pragma: begin_exports
#include "src/Eigenvalues/ArpackSelfAdjointEigenSolver.h"
// IWYU pragma: end_exports

#include "../../Eigen/src/Core/util/ReenableStupidWarnings.h"

#endif  // EIGEN_ARPACKSUPPORT_MODULE_H
