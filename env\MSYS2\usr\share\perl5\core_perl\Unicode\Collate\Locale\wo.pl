+{
   locale_version => 1.31,
# eng doesn't require tailoring
   entry => <<'ENTRY', # for DUCET v13.0.0
00E0      ; [.1FA3.0020.0002] # LATIN SMALL LETTER A WITH GRAVE
0061 0300 ; [.1FA3.0020.0002] # LATIN SMALL LETTER A WITH GRAVE
0061 0340 ; [.1FA3.0020.0002] # LATIN SMALL LETTER A WITH GRAVE
00C0      ; [.1FA3.0020.0008] # LATIN CAPITAL LETTER A WITH GRAVE
0041 0300 ; [.1FA3.0020.0008] # LATIN CAPITAL LETTER A WITH GRAVE
0041 0340 ; [.1FA3.0020.0008] # LATIN CAPITAL LETTER A WITH GRAVE
00E9      ; [.2008.0020.0002] # LATIN SMALL LETTER E WITH ACUTE
0065 0301 ; [.2008.0020.0002] # LATIN SMALL LETTER E WITH ACUTE
0065 0341 ; [.2008.0020.0002] # LATIN SMALL LETTER E WITH ACUTE
00C9      ; [.2008.0020.0008] # LATIN CAPITAL LETTER E WITH ACUTE
0045 0301 ; [.2008.0020.0008] # LATIN CAPITAL LETTER E WITH ACUTE
0045 0341 ; [.2008.0020.0008] # LATIN CAPITAL LETTER E WITH ACUTE
00EB      ; [.2009.0020.0002] # LATIN SMALL LETTER E WITH DIAERESIS
0065 0308 ; [.2009.0020.0002] # LATIN SMALL LETTER E WITH DIAERESIS
00CB      ; [.2009.0020.0008] # LATIN CAPITAL LETTER E WITH DIAERESIS
0045 0308 ; [.2009.0020.0008] # LATIN CAPITAL LETTER E WITH DIAERESIS
00F1      ; [.2119.0020.0002] # LATIN SMALL LETTER N WITH TILDE
006E 0303 ; [.2119.0020.0002] # LATIN SMALL LETTER N WITH TILDE
00D1      ; [.2119.0020.0008] # LATIN CAPITAL LETTER N WITH TILDE
004E 0303 ; [.2119.0020.0008] # LATIN CAPITAL LETTER N WITH TILDE
00F3      ; [.213D.0020.0002] # LATIN SMALL LETTER O WITH ACUTE
006F 0301 ; [.213D.0020.0002] # LATIN SMALL LETTER O WITH ACUTE
006F 0341 ; [.213D.0020.0002] # LATIN SMALL LETTER O WITH ACUTE
00D3      ; [.213D.0020.0008] # LATIN CAPITAL LETTER O WITH ACUTE
004F 0301 ; [.213D.0020.0008] # LATIN CAPITAL LETTER O WITH ACUTE
004F 0341 ; [.213D.0020.0008] # LATIN CAPITAL LETTER O WITH ACUTE
ENTRY
};
